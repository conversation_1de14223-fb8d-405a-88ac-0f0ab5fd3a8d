#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#define HWND void*
#endif

#if (CPCAPI2_BRAND_CONFERENCE_CONNECTOR_MODULE == 1)

#include "cpcapi2_test_fixture.h"
#include "test_call_events.h"
#include "test_events.h"
#include "test_account_events.h"
#include "test_framework/xmpp_test_helper.h"
#include "test_framework/screenshare_utils.h"

#include <webrtc/modules/desktop_capture/desktop_capturer.h>
#include <webrtc/base/stream.h>
#include <webrtc/video_engine/vie_encoder.h>

#include <confconnector/ConferenceConnector.h>
#include <confconnector/ConferenceConnectorHandler.h>
#include <confconnector/ConferenceConnectorInternal.h>
#include <confconnector/ConferenceConnectorTypes.h>
#include <orchestration_server/OrchestrationServer.h>
#include <cloudserviceconfig/CloudServiceConfig.h>
#include <confbridge/ConferenceBridgeJsonApi.h>
#include <confbridge/ConferenceBridgeManager.h>
#include <auth_server/AuthServerInternal.h>

#include "../../impl/media/VideoInterface.h"
#include "../../impl/util/CurlPPHelper.h"
#include "../../impl/util/CurlPPProgress.h"
#include "../../impl/util/CurlPPSSL.h"
#include "../../impl/auth_server/AuthServerJwtUtils.h"
#include "../../impl/auth_server/AuthServerDbAccess.h"
#include "../../impl/jsonapi/JsonApiServerInterface.h"

#include <boost/algorithm/string.hpp>

using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::ConferenceConnector;
using namespace CPCAPI2::CloudServiceConfig;
using namespace CPCAPI2::OrchestrationServer;
using namespace CPCAPI2::JsonApi;
using namespace CPCAPI2::SipConversation;
using namespace CPCAPI2::SipAccount;
using namespace webrtc;

class ConferenceConnectorTests : public CpcapiAutoTest
{
public:
   ConferenceConnectorTests() {}
   virtual ~ConferenceConnectorTests() {}
};

void getCloudServerUrls(std::string& authUrl, std::string& orchUrl, std::string& agentUrl, int authPort=18084, int jsonApiHttpPort=18082, int jsonApiWsPort=9003)
{
   std::stringstream authBuf;
   authBuf << "https://127.0.0.1:" << authPort;
   authUrl = authBuf.str().c_str();

   std::stringstream orchBuf;
   orchBuf << "https://127.0.0.1:" << jsonApiHttpPort << "/jsonApi";
   // orchBuf << "http://inproc.local:" << XMPP_ORCH_SERVER_HTTP_PORT << "/jsonApi";
   orchUrl = orchBuf.str().c_str();

   std::stringstream agentBuf;
   agentBuf << "wss://127.0.0.1:" << jsonApiWsPort;
   agentUrl = agentBuf.str().c_str();
}

void setupAuthServer(TestAccount& max)
{
   CPCAPI2::AuthServer::DbAccess authDb;
   authDb.initialize("authserver.db");
   authDb.flushUsers();
   authDb.addUser("user1", "1234");
   authDb.addUser("user2", "1234");
   authDb.addUser("server", "server");

   CPCAPI2::AuthServer::AuthServerConfig authServerConfig;
   authServerConfig.certificateFilePath = TestEnvironmentConfig::testResourcePath() + "p256-private-key-unit-tests.p8";
   authServerConfig.httpsCertificateFilePath = TestEnvironmentConfig::testResourcePath() + "cert.pem";
   authServerConfig.httpsPrivateKeyFilePath = TestEnvironmentConfig::testResourcePath() + "privkey.pem";
   authServerConfig.httpsDiffieHellmanParamsFilePath = TestEnvironmentConfig::testResourcePath() + "dh2048.pem";
   authServerConfig.numThreads = 4;
   authServerConfig.port = 18084;
   max.authServer->start(authServerConfig);
}

class RegisterConferenceResultHandlerFuncObj : public CPCAPI2::ConferenceBridge::RegisterConferenceResultHandler
{
public:
   RegisterConferenceResultHandlerFuncObj(const std::function<void(CPCAPI2::ConferenceBridge::ConferenceRegistrarHandle, const CPCAPI2::ConferenceBridge::RegisterConferenceResult&)>& func) : mFunc(func) {}
   virtual ~RegisterConferenceResultHandlerFuncObj() {}

   virtual int onRegisterConferenceComplete(CPCAPI2::ConferenceBridge::ConferenceRegistrarHandle registrar, const CPCAPI2::ConferenceBridge::RegisterConferenceResult& args) override {
      mFunc(registrar, args);
      delete this;
      return kSuccess;
   }
   virtual bool synchronous() const override {
      return true;
   }

private:
   std::function<void(CPCAPI2::ConferenceBridge::ConferenceRegistrarHandle, const CPCAPI2::ConferenceBridge::RegisterConferenceResult&)> mFunc;
};

void setupConfBridgeServer(TestAccount& maia, int jsonApiHttpPort=18082, int jsonApiWsPort=9003, int videoStreamingPort=9005, int raftPort=18082, int raftNodeId=1)
{
   cpc::string certificateFilePath = TestEnvironmentConfig::testResourcePath() + "p256-public-key-unit-tests.spki"; // pub key used to validate auth tokens
   JsonApi::JsonApiServerConfig jsonApiServCfg(jsonApiWsPort, jsonApiHttpPort, certificateFilePath);
   jsonApiServCfg.wssCertificateFilePath = TestEnvironmentConfig::testResourcePath() + "cert.pem";
   jsonApiServCfg.wssPrivateKeyFilePath = TestEnvironmentConfig::testResourcePath() + "privkey.pem";
   jsonApiServCfg.wssDiffieHellmanParamsFilePath = TestEnvironmentConfig::testResourcePath() + "dh2048.pem";
   jsonApiServCfg.httpsCertificateFilePath = TestEnvironmentConfig::testResourcePath() + "cert.pem";
   jsonApiServCfg.httpsPrivateKeyFilePath = TestEnvironmentConfig::testResourcePath() + "privkey.pem";
   jsonApiServCfg.httpsDiffieHellmanParamsFilePath = TestEnvironmentConfig::testResourcePath() + "dh2048.pem";

   cpc::string conferenceBridgeServiceId = "confbridge";

   if (jsonApiServCfg.certificateFilePath.size() == 0)
      jsonApiServCfg.certificateFilePath = TestEnvironmentConfig::testResourcePath() + "p256-public-key-unit-tests.spki"; // pub key used to validate auth tokens
   maia.jsonApiServer->start(jsonApiServCfg);
   CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(maia.phone)->setJsonApiServer(maia.jsonApiServer);
   CPCAPI2::ConferenceBridge::ConferenceBridgeJsonApi::getInterface(maia.phone);

   CPCAPI2::VideoStreaming::VideoStreamingManager* videoStreamingMgr = CPCAPI2::VideoStreaming::VideoStreamingManager::getInterface(maia.phone);
   if (videoStreamingMgr != NULL)
   {
      videoStreamingMgr->setVideoStreamingServer(CPCAPI2::VideoStreaming::VideoStreamingManager::getInterface(maia.phone));
      CPCAPI2::VideoStreaming::VideoStreamingServerConfig vsConfig;
      vsConfig.listenPort = videoStreamingPort;
      vsConfig.wssCertificateFilePath = TestEnvironmentConfig::testResourcePath() + "cert.pem";
      vsConfig.wssPrivateKeyFilePath = TestEnvironmentConfig::testResourcePath() + "privkey.pem";
      vsConfig.wssDiffieHellmanParamsFilePath = TestEnvironmentConfig::testResourcePath() + "dh2048.pem";
      videoStreamingMgr->startVideoStreamingServer(vsConfig);
   }

   CPCAPI2::ConferenceBridge::ConferenceRegistrar* confRegistrar = CPCAPI2::ConferenceBridge::ConferenceRegistrar::getInterface(maia.phone);
   if (confRegistrar != NULL)
   {
      CPCAPI2::ConferenceBridge::ConferenceRegistrarConfig confRegistrarConfig;
      confRegistrarConfig.conferenceRegistrarServiceIp = cpc::string("127.0.0.1:") + std::to_string(jsonApiHttpPort).c_str();
      confRegistrarConfig.conferenceRegistrarServicePort = raftPort;
      confRegistrarConfig.joinClusterUrl = cpc::string("https://127.0.0.1:") + std::to_string(raftPort).c_str() + cpc::string("/statusApi/joinCluster");
      confRegistrarConfig.nodeId = raftNodeId;
      confRegistrarConfig.wsUrlBase = cpc::string("wss://127.0.0.1:") + std::to_string(jsonApiWsPort).c_str();
      confRegistrarConfig.jsonApiHostname = "cpclientapi.softphone.com";
      confRegistrarConfig.authServiceUrl = "https://127.0.0.1:18084/login_v2";
      confRegistrarConfig.authServiceApiKey = "-----BEGIN PUBLIC KEY-----\n"
         "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiKzxgDrW3u9+XzGN4r/GI5o+BMtt\n"
         "y+63OT4lwEgFlkpedq5FY3eCzI4lB1WXDVip0OpCM1zYoCh6mqi7KaS1Vg==\n"
         "-----END PUBLIC KEY-----";
      confRegistrarConfig.urlMapFilename = maia.config.name + cpc::string("_urlmap_autotests.db");

      //confRegistrarConfig.httpUrlBase = cpc::string("https://127.0.0.1:") + cpc::string(std::to_string(jsonApiHttpPort).c_str()) + cpc::string("/screenshare");
      confRegistrar->start(confRegistrarConfig);
      {
         CPCAPI2::ConferenceBridge::ConferenceRegistrarHandle h;
         CPCAPI2::ConferenceBridge::ConferenceRegistrarStartupResult args;
         ASSERT_TRUE(cpcExpectEvent(maia.conferenceRegistrarEvents, "ConferenceRegistrarHandler::onStartupComplete", 15000, CPCAPI2::test::AlwaysTruePred(), h, args));
      }
   }

   CPCAPI2::OrchestrationServer::OrchestrationServer* agentOrchServer = CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(maia.phone);
   CPCAPI2::OrchestrationServer::OrchestrationServerConfig serverConfig;
   serverConfig.redisIp = "mock";
   serverConfig.redisPort = 6379;
   agentOrchServer->start(serverConfig);

   CPCAPI2::Media::MediaStackSettings mediaSettings;
   mediaSettings.audioLayer = CPCAPI2::Media::AudioLayers_File;
   mediaSettings.audioOutputDisabled = true;
   mediaSettings.numAudioEncoderThreads = 4;
   CPCAPI2::Media::MediaManager* media = CPCAPI2::Media::MediaManager::getInterface(maia.phone);
   ASSERT_TRUE(media != NULL);
   CPCAPI2::Media::AudioExt* audioExt = CPCAPI2::Media::AudioExt::getInterface(media);
   ASSERT_TRUE(audioExt != NULL);
   audioExt->setAudioDeviceFile(TestEnvironmentConfig::testResourcePath() + "silence16.pcm", "");
   media->initializeMediaStack(mediaSettings);

   CPCAPI2::Media::Audio* audioIf = CPCAPI2::Media::Audio::getInterface(media);
   audioIf->setEchoCancellationMode(CPCAPI2::Media::AudioDeviceRole_Headset, CPCAPI2::Media::EchoCancellationMode_None);
   audioIf->setNoiseSuppressionMode(CPCAPI2::Media::AudioDeviceRole_Headset, CPCAPI2::Media::NoiseSuppressionMode_None);
   audioExt->setMicAGCEnabled(false);
   //audioIf->queryCodecList();
   //audioIf->setMicMute(true);
   //audioIf->setSpeakerMute(true);

   //Video::getInterface(mMedia)->setHandler(this);
   CPCAPI2::Media::Video::getInterface(media)->setCaptureDevice(CPCAPI2::Media::kCustomVideoSourceDeviceId);
   CPCAPI2::Media::Video::getInterface(media)->startCapture();
   //Video::getInterface(mMedia)->setVideoMixMode(CPCAPI2::Media::VideoMixMode_MCU);
   //Video::getInterface(mMedia)->queryCodecList();


   CloudServiceConfigManager* cloudConfigMgr = CPCAPI2::CloudServiceConfig::CloudServiceConfigManager::getInterface(maia.phone);
   test::EventHandler cloudConfigEvents(conferenceBridgeServiceId, dynamic_cast<CPCAPI2::AutoTestProcessor*>(cloudConfigMgr));

   std::string authUrl("");
   std::string orchUrl("");
   std::string agentUrl("");
   getCloudServerUrls(authUrl, orchUrl, agentUrl, 18084, jsonApiHttpPort, jsonApiWsPort);
   orchUrl = "http://inproc.local";

   ConferenceBridge::ConferenceBridgeConfig bridgeSettings;
   bridgeSettings.httpJoinUrlBase = cpc::string("https://127.0.0.1:") + std::to_string(jsonApiHttpPort).c_str();
   bridgeSettings.wsUrlBase = cpc::string("wss://127.0.0.1:") + std::to_string(jsonApiWsPort).c_str();
   bridgeSettings.serverUid = "BC";
   //bridgeSettings.natTraversalServerInfo.natTraversalServerHostname = "cpsipv6.counterpath.net";
   //bridgeSettings.natTraversalServerInfo.natTraversalServerPort = 3478;
   //bridgeSettings.natTraversalServerInfo.natTraversalServerType = ConferenceBridge::ConferenceNatTraversalServerInfo::NatTraversalServerType_StunOnly;
   //bridgeSettings.natTraversalServerInfo.natTraversalServerType2 = ConferenceBridge::ConferenceNatTraversalServerInfo::NatTraversalServerType_StunAndTurn;
   //bridgeSettings.natTraversalServerInfo.natTraversalServerUsername = "cpcapi2";
   //bridgeSettings.natTraversalServerInfo.natTraversalServerPassword = "echo$8";
   //bridgeSettings.natTraversalServerInfo.serverPublicIpAddress = "**************";

   resip::Data user1enc = CPCAPI2::JsonApi::JsonApiServerInterface::doEncrypt("user1");
   bridgeSettings.userContext = user1enc.c_str();

   CPCAPI2::ConferenceBridge::ConferenceBridgeManager::getInterface(maia.phone)->start(bridgeSettings);

   ServerInfo xmppAgentServerInfo;
   xmppAgentServerInfo.region = "NA";
   xmppAgentServerInfo.uri = agentUrl.c_str();
   xmppAgentServerInfo.services.push_back(conferenceBridgeServiceId);
   ServiceConfigSettings serviceConfigSettings;
   serviceConfigSettings.authServerUrl = std::string(authUrl + "/login_v1").c_str();
   serviceConfigSettings.orchestrationServerUrl = orchUrl.c_str();
   serviceConfigSettings.username = "server";
   serviceConfigSettings.password = "server";

   cloudConfigMgr->setServerInfo(serviceConfigSettings, xmppAgentServerInfo);
   {
      CloudServiceConfigHandle h;
      SetServerInfoResult args;
      ASSERT_TRUE(cpcExpectEvent((&cloudConfigEvents), "CloudServiceConfigHandler::onSetServerInfoSuccess", 5000, CPCAPI2::test::AlwaysTruePred(), h, args));
   }
}

// where is screen sharing supported...
#if defined(_WIN32) || (defined( __APPLE__ ) && TARGET_OS_IPHONE == 0) || (defined(__linux__) && !defined(ANDROID))

// used in ScreenShareImpl.cpp
extern void (*gCustomCapturerFactory)(std::unique_ptr<DesktopCapturer>& outCapturer);


class LogChecker
{
public:
   LogChecker() : mFoundWebRtcLog(false)
   {
   }
   
   void loggingListener(const char *message, const char* subsystem, CPCAPI2::LogLevel level)
   {
      if (boost::trim_left_copy(std::string(subsystem)) == "MEDIA_STACK")
      {
         // [2021-10-20 10:00:16.014](Info)(MediaStackLog.cpp:77) 0 t95491             (10: 0:16: 14 |    5)                               95491; (vie_channel.cc:2076): OnInitializeDecoder 127 H264
         if (std::string(message).find("OnInitializeDecoder") != std::string::npos)
         {
            mFoundWebRtcLog = true;
         }
      }
   }
   
   std::atomic_bool mFoundWebRtcLog;
};


static void doFakeScreenShare(int _captureWidth, int _captureHeight, int frameWidth, int frameHeight)
{
#if (defined(__linux__) && !defined(ANDROID))
   // lowered from 3000 to 2000 to mitigate more flaky tests
   if (_captureWidth >= 2000 || _captureHeight > 2000)
   {
      // SCORE-1312
      GTEST_SKIP() << "Skipping test to avoid hitting SCORE-1312";
   }
#endif

   static int captureWidth = 0, captureHeight = 0;
   captureWidth = _captureWidth;
   captureHeight = _captureHeight;
//safeCout("Desiring capture at " << captureWidth << "x" << captureHeight);

   gCustomCapturerFactory = [](std::unique_ptr<DesktopCapturer>& outCapturer)
   {
//safeCout("Capturing at " << captureWidth << "x" << captureHeight);
      outCapturer = std::unique_ptr<CPCAPI2::test::FakeScreenCapturer>(new CPCAPI2::test::FakeScreenCapturer(captureWidth, captureHeight));
   };
   
   LoginResultEvent loginResult; loginResult.success = true;
   cpc::vector<cpc::string> permissions; permissions.push_back("*");

   // Max is the auth server
   TestAccount max("max", Account_Init);
   setupAuthServer(max);

   // Maia is the orchestration and confbridge server
   TestAccount maia("maia", Account_Init);
   setupConfBridgeServer(maia);

   // Alice is a client SDK (screenshare presenter)
   TestAccount alice("alice", Account_Init);

   // Bob is a client SDK (screenshare participant)
   TestAccount bob("bob", Account_Init);

   LogChecker bobMediaStackLogChecker;

   std::function<void(const char *message, const char* subsystem, CPCAPI2::LogLevel level)> f = std::bind(&LogChecker::loggingListener,
                                                                                                      &bobMediaStackLogChecker, std::placeholders::_1,
                                                                                                      std::placeholders::_2, std::placeholders::_3);

   AutoTestsLogger::ScopedMessageListenerFunction smlf(f);


   // enable H.264
   alice.video->queryCodecList();
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);
   bob.video->queryCodecList();
   bob.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);

   alice.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   bob.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   alice.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   bob.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   alice.initiateVideo(false, true);
   bob.initiateVideo(false, true);

   std::atomic_bool testDone(false);
   auto mediaEvent = std::async(std::launch::async, [&]() {
      while (!testDone) {
         // conf connector fires method calls it expects to be processed on app thread
         alice.mediaEvents->processNonUnitTestEvents(0);
         bob.mediaEvents->processNonUnitTestEvents(0);
         std::this_thread::sleep_for(std::chrono::milliseconds(20));
      }
   });

   ConferenceConnectorHandle aliceConfConn = alice.createConferenceConnector();
   ConferenceConnectorSettings aliceCloudSettings;
   aliceCloudSettings.authServerUrl = "https://127.0.0.1:18084";
   aliceCloudSettings.orchestrationServerUrl = "https://127.0.0.1:18082/jsonApi";
   aliceCloudSettings.regionCode = "NA";
   aliceCloudSettings.username = "user1";
   aliceCloudSettings.password = "1234";
   aliceCloudSettings.ignoreCertVerification = true;
   aliceCloudSettings.authServerApiKey = "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiKzxgDrW3u9+XzGN4r/GI5o+BMtty+63OT4lwEgFlkpedq5FY3eCzI4lB1WXDVip0OpCM1zYoCh6mqi7KaS1Vg==";
   alice.conferenceConnector->setConnectionSettings(aliceConfConn, aliceCloudSettings);
   alice.conferenceConnector->connectToConferenceService(aliceConfConn);

   std::promise<std::string> joinUrlPr;
   std::future<std::string> joinUrlFu = joinUrlPr.get_future();
   auto maiaEvent = std::async(std::launch::async, [&]() {
      JsonApiUserHandle jsonApiUser = 0;
      NewLoginEvent args;

      // Maia has to process the login attempt (associate the context with an SDK instance)
      ASSERT_TRUE(cpcExpectEvent(maia.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 15000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args));
      ASSERT_NE(jsonApiUser, 0);
      maia.jsonApiServer->setJsonApiUserContext(jsonApiUser, maia.phone, permissions);
      maia.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);

      // Maia has to process the login attempt (associate the context with an SDK instance)
      ASSERT_TRUE(cpcExpectEvent(maia.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 15000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args));
      ASSERT_NE(jsonApiUser, 0);
      maia.jsonApiServer->setJsonApiUserContext(jsonApiUser, maia.phone, permissions);
      maia.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);

   });

   auto aliceEvent = std::async(std::launch::async, [&]() {
      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
      }

      alice.conferenceConnector->queryConferenceList(aliceConfConn);

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 0);
      }

      CloudConferenceSettings confSettings;
      confSettings.conferenceDescription = "test";
      confSettings.conferenceType = CloudConferenceType_Screenshare;
      confSettings.conferenceId = "screenshare";
      alice.conferenceConnector->createNewConference(aliceConfConn, confSettings);
      CloudConferenceHandle aliceScreenShare = 0;

      {
         ConferenceConnectorHandle conn;
         ConferenceCreatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceCreated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_NE(args.conference, 0);
         ASSERT_NE(args.conference, -1);
         aliceScreenShare = args.conference;
      }

      alice.conferenceConnector->queryConferenceList(aliceConfConn);

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 1);
         ASSERT_FALSE(args.conferenceList[0].joinUrl.empty());
         joinUrlPr.set_value(args.conferenceList[0].joinUrl.c_str());
         ASSERT_GT(args.conferenceList[0].conference, 0); // handle used to create a new session
      }

      //std::this_thread::sleep_for(std::chrono::milliseconds(2000));

      CloudConferenceSessionHandle aliceSession = alice.conferenceConnector->createConferenceSession(aliceScreenShare);

      CloudConferenceSessionSettings aliceSessionSettings;
      aliceSessionSettings.role = CloudConferenceRole_Host;
      alice.conferenceConnector->setSessionSettings(aliceSession, aliceSessionSettings);

      CloudConferenceSessionMediaSettings aliceSessionMedia;
      aliceSessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
      aliceSessionMedia.videoDirection = ConferenceConnector::MediaDirection_SendRecv;
      aliceSessionMedia.remoteVideoRenderSurface = alice.videoHelper->getHwndIncomingWindow();
      aliceSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
      alice.conferenceConnector->setSessionMediaSettings(aliceSession, aliceSessionMedia);

      alice.conferenceConnector->startSession(aliceSession);

      try
      {
         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connecting);
         }

         {
            ConferenceConnectorHandle conn;
            ConferenceSessionMediaStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionMediaStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.mediaStatus, CPCAPI2::ConferenceConnector::SessionMediaStatus_OfferSent);
         }

         {
            ConferenceConnectorHandle conn;
            ConferenceSessionMediaStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionMediaStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.mediaStatus, CPCAPI2::ConferenceConnector::SessionMediaStatus_AnswerReceived);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(20000));

         safeCout("ALICE ENDS SESSION");
         alice.conferenceConnector->endSession(aliceSession);

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
         }

         {
            ConferenceConnectorHandle conn;
            ConferenceEndedEvent args;
            bool gotOnConferenceEnded = cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceEnded", 30000, CPCAPI2::test::AlwaysTruePred(), conn, args);
            //if (!gotOnConferenceEnded)
            //{
            //   int* a = 0;
            //   *a = NULL;
            //}
            ASSERT_TRUE(gotOnConferenceEnded);
            ASSERT_EQ(args.conference, aliceScreenShare);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(5000));
         
         // if this fails it means we didn't see an expected log line from WebRTC.
         // this check is here to make sure WebRTC logging is showing up properly (OBELISK-6046)
         ASSERT_TRUE(bobMediaStackLogChecker.mFoundWebRtcLog);

         alice.conferenceConnector->destroyConferenceConnector(aliceConfConn);



      }
      catch (...)
      {
      }

   });

   std::shared_ptr<std::promise<int> > bobPr(new std::promise<int>());
   std::future<int> bobFu = bobPr->get_future();
   auto bobEvent = std::async(std::launch::async, [&, bobPr]() {
      ASSERT_EQ(joinUrlFu.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
      std::string joinUrlFromAlice = joinUrlFu.get();

      ConferenceConnectorHandle bobConfConn = bob.createConferenceConnector();
      ConferenceConnectorSettings bobCloudSettings;
      bobCloudSettings.authServerUrl = "https://127.0.0.1:18084";
      bobCloudSettings.orchestrationServerUrl = ""; // not needed, we use the joinUrl instead
      bobCloudSettings.joinUrl = joinUrlFromAlice.c_str();
      bobCloudSettings.regionCode = "NA";
      bobCloudSettings.username = "user2";
      bobCloudSettings.password = "1234";
      bobCloudSettings.ignoreCertVerification = true;
      bobCloudSettings.authServerApiKey = "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiKzxgDrW3u9+XzGN4r/GI5o+BMtty+63OT4lwEgFlkpedq5FY3eCzI4lB1WXDVip0OpCM1zYoCh6mqi7KaS1Vg==";

      bob.conferenceConnector->setConnectionSettings(bobConfConn, bobCloudSettings);
      bob.conferenceConnector->connectToConferenceService(bobConfConn);

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
      }

      bob.conferenceConnector->queryConferenceList(bobConfConn);
      CloudConferenceHandle bobScreenShare;

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 1);
         bobScreenShare = args.conferenceList[0].conference;
      }

      //std::this_thread::sleep_for(std::chrono::milliseconds(2000));

      CloudConferenceSessionHandle bobSession = bob.conferenceConnector->createConferenceSession(bobScreenShare);

      CloudConferenceSessionSettings bobSessionSettings;
      bobSessionSettings.role = CloudConferenceRole_Participant;
      bob.conferenceConnector->setSessionSettings(bobSession, bobSessionSettings);

      CloudConferenceSessionMediaSettings bobSessionMedia;
      bobSessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
      bobSessionMedia.videoDirection = ConferenceConnector::MediaDirection_RecvOnly;
      bobSessionMedia.remoteVideoRenderSurface = bob.videoHelper->getHwndIncomingWindow();
      bobSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
      bob.conferenceConnector->setSessionMediaSettings(bobSession, bobSessionMedia);

      bob.conferenceConnector->startSession(bobSession);

      try
      {
         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connecting);
         }

         {
            ConferenceConnectorHandle conn;
            ConferenceSessionMediaStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionMediaStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.mediaStatus, CPCAPI2::ConferenceConnector::SessionMediaStatus_OfferSent);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
            EXPECT_EQ(args.screenshare.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_RecvOnly);
            EXPECT_EQ(args.audio.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
            EXPECT_EQ(args.video.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
            //EXPECT_NE(args.screenshare.mediaStreamId, -1);
            //bob.video->setIncomingVideoRenderTarget(args.screenshare.mediaStreamId, hwndBobRemote);
         }

//         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         bobSessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
         bobSessionMedia.videoDirection = ConferenceConnector::MediaDirection_RecvOnly;
         bobSessionMedia.remoteVideoRenderSurface = bob.videoHelper->getHwndIncomingWindow();
         bobSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
         bob.conferenceConnector->setSessionMediaSettings(bobSession, bobSessionMedia);
         bob.conferenceConnector->updateSessionMedia(bobSession);

         {
            ConferenceConnectorHandle conn;
            ConferenceSessionMediaStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionMediaStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.mediaStatus, CPCAPI2::ConferenceConnector::SessionMediaStatus_AnswerReceived);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
            EXPECT_EQ(args.screenshare.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_RecvOnly);
            EXPECT_EQ(args.audio.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
            EXPECT_EQ(args.video.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
            //bob.video->setIncomingVideoRenderTarget(args.screenshare.mediaStreamId, hwndBobRemote);
         }

         // need to wait long enough that some frames come in and are created (takes a while for large frames)
         std::this_thread::sleep_for(std::chrono::milliseconds(15000));

         ConferenceConnectorInternal* confConnInt = dynamic_cast<ConferenceConnectorInternal*>(bob.conferenceConnector);

         confConnInt->queryMediaStatistics(bobConfConn);

         {
            ConferenceConnectorHandle conn;
            ConferenceConnectorMediaStatisticsEvent args;
            ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceConnectorMediaStatistics", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(args.mediaStreamStats[0].videoReceiveFrameWidth, frameWidth);
            ASSERT_EQ(args.mediaStreamStats[0].videoReceiveFrameHeight, frameHeight);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceParticipantListUpdatedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceParticipantListUpdated", 180000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            //EXPECT_EQ(args.participantList.size(), 0);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 700000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(CPCAPI2::ConferenceConnector::SessionStatus_NotConnected, args.sessionStatus);
         }

         {
            ConferenceConnectorHandle conn;
            ConferenceEndedEvent args;
            ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceEnded", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(args.conference, bobScreenShare);
         }

         /*
         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceParticipantListUpdatedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceParticipantListUpdated", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            //EXPECT_EQ(args.participantList.size(), 0);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 120000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         bob.conferenceConnector->endSession(bobSession);
         */
      }
      catch (...)
      {
      }
      bob.conferenceConnector->destroyConferenceConnector(bobConfConn);

      //{
      //   // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
      //   ConferenceConnectorHandle conn;
      //   ConferenceSessionStatusChangedEvent args;
      //   ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
      //   ASSERT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
      //}
      bobPr->set_value(0);
   });

   aliceEvent.wait_for(std::chrono::milliseconds(45000));
   aliceEvent.get();

   bobEvent.wait_for(std::chrono::milliseconds(45000));
   bobEvent.get();
   //bobFu.get();

   maiaEvent.wait_for(std::chrono::milliseconds(45000));
   maiaEvent.get();

   //waitFor2(aliceEvent, maiaEvent);

   testDone = true;

   CPCAPI2::ConferenceBridge::ConferenceBridgeManager::getInterface(maia.phone)->shutdown();
   CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(maia.phone)->flushAll();
   CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(maia.phone)->shutdown();
   maia.jsonApiServer->shutdown();

   max.authServer->shutdown();

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
}

#else

static void doFakeScreenShare(int _captureWidth, int _captureHeight, int frameWidth, int frameHeight)
{
   safeCout("No screen share support on this platform, skipping test!");
}

#endif


TEST_F(ConferenceConnectorTests, ConnectSuccess_NPE)
{
	LoginResultEvent loginResult; loginResult.success = true;
	cpc::vector<cpc::string> permissions; permissions.push_back("*");

	// Max is the auth server
	TestAccount max("max", Account_Init);
	setupAuthServer(max);

	// Maia is the orchestration and confbridge server
	TestAccount maia("maia", Account_Init);
	setupConfBridgeServer(maia);

	// Alice is a client SDK (screenshare presenter)
	TestAccount alice("alice", Account_Init);

	// Bob is a client SDK (screenshare participant)
	TestAccount bob("bob", Account_Init);

	// enable H.264
	alice.video->queryCodecList();
	alice.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
	alice.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
	alice.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);
	bob.video->queryCodecList();
	bob.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
	bob.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
	bob.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);

	alice.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
	bob.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

	alice.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
	bob.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

	alice.initiateVideo(false, true);
    bob.initiateVideo(false, true);

	std::atomic_bool testDone(false);
	auto mediaEvent = std::async(std::launch::async, [&]() {
		while (!testDone) {
		    // conf connector fires method calls it expects to be processed on app thread
			alice.mediaEvents->processNonUnitTestEvents(0);
			bob.mediaEvents->processNonUnitTestEvents(0);
			std::this_thread::sleep_for(std::chrono::milliseconds(20));
		}
	});

	ConferenceConnectorHandle aliceConfConn = alice.createConferenceConnector();
	ConferenceConnectorSettings aliceCloudSettings;
	aliceCloudSettings.authServerUrl = "https://127.0.0.1:18084";
	aliceCloudSettings.orchestrationServerUrl = "https://127.0.0.1:18082/jsonApi";
	aliceCloudSettings.regionCode = "NA";
	aliceCloudSettings.username = "user1";
	aliceCloudSettings.password = "1234";
	aliceCloudSettings.ignoreCertVerification = true;
   aliceCloudSettings.authServerApiKey = "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiKzxgDrW3u9+XzGN4r/GI5o+BMtty+63OT4lwEgFlkpedq5FY3eCzI4lB1WXDVip0OpCM1zYoCh6mqi7KaS1Vg==";
	alice.conferenceConnector->setConnectionSettings(aliceConfConn, aliceCloudSettings);
	alice.conferenceConnector->connectToConferenceService(aliceConfConn);

	std::promise<std::string> joinUrlPr;
	std::future<std::string> joinUrlFu = joinUrlPr.get_future();
	auto maiaEvent = std::async(std::launch::async, [&]() {
		JsonApiUserHandle jsonApiUser = 0;
		NewLoginEvent args;

		// Maia has to process the login attempt (associate the context with an SDK instance)
		ASSERT_TRUE(cpcExpectEvent(maia.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 15000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args));
		ASSERT_NE(jsonApiUser, 0);
		maia.jsonApiServer->setJsonApiUserContext(jsonApiUser, maia.phone, permissions);
		maia.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);

		// Maia has to process the login attempt (associate the context with an SDK instance)
		ASSERT_TRUE(cpcExpectEvent(maia.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 15000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args));
		ASSERT_NE(jsonApiUser, 0);
		maia.jsonApiServer->setJsonApiUserContext(jsonApiUser, maia.phone, permissions);
		maia.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);

	});

	auto aliceEvent = std::async(std::launch::async, [&]() {
		{
			ConferenceConnectorHandle conn;
			ServiceConnectionStatusEvent args;
			ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
			ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
		}

		{
			ConferenceConnectorHandle conn;
			ServiceConnectionStatusEvent args;
			ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
			ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
		}

		{
			ConferenceConnectorHandle conn;
			ServiceConnectionStatusEvent args;
			ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
			ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
		}

		alice.conferenceConnector->queryConferenceList(aliceConfConn);

		{
			ConferenceConnectorHandle conn;
			ConferenceListUpdatedEvent args;
			ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
			ASSERT_EQ(args.conferenceList.size(), 0);
		}

		CloudConferenceSettings confSettings;
      confSettings.conferenceDescription = "test";
      confSettings.conferenceType = CloudConferenceType_Screenshare;
      confSettings.conferenceId = "screenshare";
      alice.conferenceConnector->createNewConference(aliceConfConn, confSettings);
      CloudConferenceHandle aliceScreenShare = 0;

      {
         ConferenceConnectorHandle conn;
         ConferenceCreatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceCreated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_NE(args.conference, 0);
         ASSERT_NE(args.conference, -1);
         aliceScreenShare = args.conference;
      }

		alice.conferenceConnector->queryConferenceList(aliceConfConn);

		{
			ConferenceConnectorHandle conn;
			ConferenceListUpdatedEvent args;
			ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
			ASSERT_EQ(args.conferenceList.size(), 1);
			ASSERT_FALSE(args.conferenceList[0].joinUrl.empty());
			joinUrlPr.set_value(args.conferenceList[0].joinUrl.c_str());
			ASSERT_GT(args.conferenceList[0].conference, 0); // handle used to create a new session
		}

      //std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      
      bool skipFindDisplay = false;

      if (TestEnvironmentConfig::dockerContainerized())
      {
         skipFindDisplay = true;
      }

      if (!skipFindDisplay)
      {
         CPCAPI2::Media::VideoExt* videoExt = CPCAPI2::Media::VideoExt::getInterface(alice.media);
         videoExt->queryScreenshareDeviceList(reinterpret_cast<Media::ScreenshareDeviceListHandler*>(0xDEADBEEF), true, false);
         CPCAPI2::Media::ScreenshareDeviceListEvent evt;
         int handle = 0;
         ASSERT_TRUE(alice.mediaEvents->expectEvent("ScreenshareDeviceListHandler::onScreenshareDeviceList", 30000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
         
         bool foundDisplay = false;
         for (cpc::vector<Media::ScreenshareDeviceInfo>::const_iterator it = evt.devices.begin(); it != evt.devices.end(); ++it)
         {
            if (!it->isWindow)
            {
               foundDisplay = true;
               videoExt->setScreenshareCaptureDevice(it->deviceId);
               break;
            }
         }
         ASSERT_TRUE(foundDisplay);
      }

		CloudConferenceSessionHandle aliceSession = alice.conferenceConnector->createConferenceSession(aliceScreenShare);

		CloudConferenceSessionSettings aliceSessionSettings;
		aliceSessionSettings.role = CloudConferenceRole_Host;
		alice.conferenceConnector->setSessionSettings(aliceSession, aliceSessionSettings);

		CloudConferenceSessionMediaSettings aliceSessionMedia;
		aliceSessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
		aliceSessionMedia.videoDirection = ConferenceConnector::MediaDirection_SendRecv;
      aliceSessionMedia.remoteVideoRenderSurface = alice.videoHelper->getHwndIncomingWindow();
      aliceSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
		alice.conferenceConnector->setSessionMediaSettings(aliceSession, aliceSessionMedia);

		alice.conferenceConnector->startSession(aliceSession);

		try
		{
			{
				// virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
				ConferenceConnectorHandle conn;
				ConferenceSessionStatusChangedEvent args;
				EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
				EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connecting);
			}

         {
            ConferenceConnectorHandle conn;
            ConferenceSessionMediaStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionMediaStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.mediaStatus, CPCAPI2::ConferenceConnector::SessionMediaStatus_OfferSent);
         }

         {
            ConferenceConnectorHandle conn;
            ConferenceSessionMediaStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionMediaStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.mediaStatus, CPCAPI2::ConferenceConnector::SessionMediaStatus_AnswerReceived);
         }

			{
				// virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
				ConferenceConnectorHandle conn;
				ConferenceSessionStatusChangedEvent args;
				EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
				EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
			}

			std::this_thread::sleep_for(std::chrono::milliseconds(20000));

			safeCout("ALICE ENDS SESSION");
			alice.conferenceConnector->endSession(aliceSession);

			{
				// virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
				ConferenceConnectorHandle conn;
				ConferenceSessionStatusChangedEvent args;
				EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
				EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
			}

			{
				ConferenceConnectorHandle conn;
				ConferenceEndedEvent args;
            bool gotOnConferenceEnded = cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceEnded", 30000, CPCAPI2::test::AlwaysTruePred(), conn, args);
            //if (!gotOnConferenceEnded)
            //{
            //   int* a = 0;
            //   *a = NULL;
            //}
            ASSERT_TRUE(gotOnConferenceEnded);
				ASSERT_EQ(args.conference, aliceScreenShare);
			}

			std::this_thread::sleep_for(std::chrono::milliseconds(5000));

			alice.conferenceConnector->destroyConferenceConnector(aliceConfConn);



		}
		catch (...)
		{
		}

	});

	std::shared_ptr<std::promise<int> > bobPr(new std::promise<int>());
	std::future<int> bobFu = bobPr->get_future();
	auto bobEvent = std::async(std::launch::async, [&, bobPr]() {
		ASSERT_EQ(joinUrlFu.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
		std::string joinUrlFromAlice = joinUrlFu.get();

		ConferenceConnectorHandle bobConfConn = bob.createConferenceConnector();
		ConferenceConnectorSettings bobCloudSettings;
		bobCloudSettings.authServerUrl = "https://127.0.0.1:18084";
		bobCloudSettings.orchestrationServerUrl = ""; // not needed, we use the joinUrl instead
		bobCloudSettings.joinUrl = joinUrlFromAlice.c_str();
		bobCloudSettings.regionCode = "NA";
		bobCloudSettings.username = "user2";
		bobCloudSettings.password = "1234";
		bobCloudSettings.ignoreCertVerification = true;
      bobCloudSettings.authServerApiKey = "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiKzxgDrW3u9+XzGN4r/GI5o+BMtty+63OT4lwEgFlkpedq5FY3eCzI4lB1WXDVip0OpCM1zYoCh6mqi7KaS1Vg==";

		bob.conferenceConnector->setConnectionSettings(bobConfConn, bobCloudSettings);
		bob.conferenceConnector->connectToConferenceService(bobConfConn);

		{
			ConferenceConnectorHandle conn;
			ServiceConnectionStatusEvent args;
			ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
			ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
		}

		{
			ConferenceConnectorHandle conn;
			ServiceConnectionStatusEvent args;
			ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
			ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
		}

		{
			ConferenceConnectorHandle conn;
			ServiceConnectionStatusEvent args;
			ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
			ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
		}

		bob.conferenceConnector->queryConferenceList(bobConfConn);
		CloudConferenceHandle bobScreenShare;

		{
			ConferenceConnectorHandle conn;
			ConferenceListUpdatedEvent args;
			ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
			ASSERT_EQ(args.conferenceList.size(), 1);
			bobScreenShare = args.conferenceList[0].conference;
		}

      //std::this_thread::sleep_for(std::chrono::milliseconds(2000));

		CloudConferenceSessionHandle bobSession = bob.conferenceConnector->createConferenceSession(bobScreenShare);

		CloudConferenceSessionSettings bobSessionSettings;
		bobSessionSettings.role = CloudConferenceRole_Participant;
		bob.conferenceConnector->setSessionSettings(bobSession, bobSessionSettings);

		CloudConferenceSessionMediaSettings bobSessionMedia;
		bobSessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
		bobSessionMedia.videoDirection = ConferenceConnector::MediaDirection_RecvOnly;
        bobSessionMedia.remoteVideoRenderSurface = bob.videoHelper->getHwndIncomingWindow();
        bobSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
		bob.conferenceConnector->setSessionMediaSettings(bobSession, bobSessionMedia);

		bob.conferenceConnector->startSession(bobSession);

		try
		{
			{
				// virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
				ConferenceConnectorHandle conn;
				ConferenceSessionStatusChangedEvent args;
				EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
				EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connecting);
			}

         {
            ConferenceConnectorHandle conn;
            ConferenceSessionMediaStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionMediaStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.mediaStatus, CPCAPI2::ConferenceConnector::SessionMediaStatus_OfferSent);
         }
         
			{
				// virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
				ConferenceConnectorHandle conn;
				ConferenceSessionStatusChangedEvent args;
				EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
				EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
				EXPECT_EQ(args.screenshare.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_RecvOnly);
				EXPECT_EQ(args.audio.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
				EXPECT_EQ(args.video.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
				//EXPECT_NE(args.screenshare.mediaStreamId, -1);
				//bob.video->setIncomingVideoRenderTarget(args.screenshare.mediaStreamId, hwndBobRemote);
			}
			std::this_thread::sleep_for(std::chrono::milliseconds(5000));

			bobSessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
			bobSessionMedia.videoDirection = ConferenceConnector::MediaDirection_RecvOnly;
         bobSessionMedia.remoteVideoRenderSurface = bob.videoHelper->getHwndIncomingWindow();
         bobSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
			bob.conferenceConnector->setSessionMediaSettings(bobSession, bobSessionMedia);
			bob.conferenceConnector->updateSessionMedia(bobSession);

         {
            ConferenceConnectorHandle conn;
            ConferenceSessionMediaStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionMediaStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.mediaStatus, CPCAPI2::ConferenceConnector::SessionMediaStatus_AnswerReceived);
         }

			{
				// virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
				ConferenceConnectorHandle conn;
				ConferenceSessionStatusChangedEvent args;
				EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
				EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
				EXPECT_EQ(args.screenshare.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_RecvOnly);
				EXPECT_EQ(args.audio.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
				EXPECT_EQ(args.video.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
				//bob.video->setIncomingVideoRenderTarget(args.screenshare.mediaStreamId, hwndBobRemote);
			}

			//std::this_thread::sleep_for(std::chrono::milliseconds(5000));

			{
				// virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
				ConferenceConnectorHandle conn;
				ConferenceParticipantListUpdatedEvent args;
				EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceParticipantListUpdated", 180000, CPCAPI2::test::AlwaysTruePred(), conn, args));
				//EXPECT_EQ(args.participantList.size(), 0);
			}

			{
				// virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
				ConferenceConnectorHandle conn;
				ConferenceSessionStatusChangedEvent args;
				EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 700000, CPCAPI2::test::AlwaysTruePred(), conn, args));
				EXPECT_EQ(CPCAPI2::ConferenceConnector::SessionStatus_NotConnected, args.sessionStatus);
			}

			{
				ConferenceConnectorHandle conn;
				ConferenceEndedEvent args;
				ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceEnded", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
				ASSERT_EQ(args.conference, bobScreenShare);
			}

			/*
			{
			   // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
			   ConferenceConnectorHandle conn;
			   ConferenceParticipantListUpdatedEvent args;
			   EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceParticipantListUpdated", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
			   //EXPECT_EQ(args.participantList.size(), 0);
			}

			{
			   // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
			   ConferenceConnectorHandle conn;
			   ConferenceSessionStatusChangedEvent args;
			   EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 120000, CPCAPI2::test::AlwaysTruePred(), conn, args));
			   EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
			}

			std::this_thread::sleep_for(std::chrono::milliseconds(5000));

			bob.conferenceConnector->endSession(bobSession);
			*/
		}
		catch (...)
		{
		}
		bob.conferenceConnector->destroyConferenceConnector(bobConfConn);

		//{
		//   // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
		//   ConferenceConnectorHandle conn;
		//   ConferenceSessionStatusChangedEvent args;
		//   ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
		//   ASSERT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
		//}
		bobPr->set_value(0);
	});

	aliceEvent.wait_for(std::chrono::milliseconds(45000));
	aliceEvent.get();

	bobEvent.wait_for(std::chrono::milliseconds(45000));
	bobEvent.get();
	//bobFu.get();

	maiaEvent.wait_for(std::chrono::milliseconds(45000));
	maiaEvent.get();

	//waitFor2(aliceEvent, maiaEvent);

	testDone = true;

	CPCAPI2::ConferenceBridge::ConferenceBridgeManager::getInterface(maia.phone)->shutdown();
	CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(maia.phone)->flushAll();
	CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(maia.phone)->shutdown();
	maia.jsonApiServer->shutdown();

	max.authServer->shutdown();

	std::this_thread::sleep_for(std::chrono::milliseconds(1000));
}

TEST_F(ConferenceConnectorTests, ConnectSuccessWithAuthenticationRetry)
{
   LoginResultEvent loginResult; loginResult.success = true;
   cpc::vector<cpc::string> permissions; permissions.push_back("*");

   // Auth server
   TestAccount auth("auth", Account_Init);
   setupAuthServer(auth);

   // Orch server and confbridge
   TestAccount orch("orch", Account_Init);
   setupConfBridgeServer(orch);

   CPCAPI2::AuthServer::AuthServerInternal* authInternal = dynamic_cast<CPCAPI2::AuthServer::AuthServerInternal*>(auth.authServer);

   // Alice is a client SDK
   // 1) Dropping auth login requests but a valid auth server, results in "curlpp exception: Empty reply from server", and attempts to retry authentication
   TestAccount alice("alice", Account_Init);

   ConferenceConnectorHandle aliceConfConn = alice.createConferenceConnector();
   ConferenceConnectorSettings aliceCloudSettings;
   aliceCloudSettings.authServerUrl = "https://127.0.0.1:18084";
   aliceCloudSettings.orchestrationServerUrl = "https://127.0.0.1:18082/jsonApi";
   aliceCloudSettings.regionCode = "NA";
   aliceCloudSettings.username = "alice";
   aliceCloudSettings.password = "1234";
   aliceCloudSettings.ignoreCertVerification = true;
   aliceCloudSettings.authServerApiKey = "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiKzxgDrW3u9+XzGN4r/GI5o+BMtty+63OT4lwEgFlkpedq5FY3eCzI4lB1WXDVip0OpCM1zYoCh6mqi7KaS1Vg==";
   aliceCloudSettings.authenticationTimeoutSeconds = 10;
   alice.conferenceConnector->setConnectionSettings(aliceConfConn, aliceCloudSettings);
   alice.conferenceConnector->connectToConferenceService(aliceConfConn);

   authInternal->dropIncomingLoginRequests(true);

   auto authEvent = std::async(std::launch::async, [&]()
   {
      JsonApiUserHandle jsonApiUser = 0;
      NewLoginEvent args;

      // Server should not receive any new login events
      ASSERT_FALSE(cpcExpectEvent(orch.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 5000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args));
   });

   waitForMs(authEvent, std::chrono::milliseconds(10000));

   auto authEvent2 = std::async(std::launch::async, [&]()
   {
      JsonApiUserHandle jsonApiUser = 0;
      NewLoginEvent args;

      // Server should not receive any new login events even with auth retry as the login requests are being dropped
      ASSERT_FALSE(cpcExpectEvent(orch.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 15000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args));
   });

   auto aliceEvent = std::async(std::launch::async, [&]()
   {
      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 20000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_ConnFailure, args.connectionStatus);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      alice.conferenceConnector->destroyConferenceConnector(aliceConfConn);
   });

   waitFor2Ms(aliceEvent, authEvent2, std::chrono::milliseconds(30000));

   // Restart auth server login handling
   authInternal->dropIncomingLoginRequests(false);

   // Bob is a client SDK
   // 2) Sending auth login requests to an invalid auth server, results in "curlpp exception: Failed to connect to 127.0.0.1 port 19084", and attempts to retry authentication
   TestAccount bob("bob", Account_Init);

   ConferenceConnectorHandle bobConfConn = bob.createConferenceConnector();
   ConferenceConnectorSettings bobCloudSettings;
   bobCloudSettings.authServerUrl = "https://127.0.0.1:19084"; // Invalid auth server address
   bobCloudSettings.orchestrationServerUrl = "https://127.0.0.1:18082/jsonApi";
   bobCloudSettings.regionCode = "NA";
   bobCloudSettings.username = "bob";
   bobCloudSettings.password = "1234";
   bobCloudSettings.ignoreCertVerification = true;
   bobCloudSettings.authServerApiKey = "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiKzxgDrW3u9+XzGN4r/GI5o+BMtty+63OT4lwEgFlkpedq5FY3eCzI4lB1WXDVip0OpCM1zYoCh6mqi7KaS1Vg==";
   bobCloudSettings.authenticationTimeoutSeconds = 10;
   bob.conferenceConnector->setConnectionSettings(bobConfConn, bobCloudSettings);
   bob.conferenceConnector->connectToConferenceService(bobConfConn);

   auto authEvent3 = std::async(std::launch::async, [&]()
   {
      JsonApiUserHandle jsonApiUser = 0;
      NewLoginEvent args;

      // Server should not receive any login events due to the invalid address
      ASSERT_FALSE(cpcExpectEvent(orch.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 5000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args));
   });

   waitForMs(authEvent3, std::chrono::milliseconds(30000));

   auto authEvent4 = std::async(std::launch::async, [&]()
   {
      JsonApiUserHandle jsonApiUser = 0;
      NewLoginEvent args;

      // Server should not receive any login events due to the invalid address
      ASSERT_FALSE(cpcExpectEvent(orch.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 15000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args));
   });

   auto bobEvent = std::async(std::launch::async, [&]()
   {
      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 20000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_ConnFailure, args.connectionStatus);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      bob.conferenceConnector->destroyConferenceConnector(bobConfConn);
   });

   waitFor2Ms(bobEvent, authEvent4, std::chrono::milliseconds(30000));

   // Max is a client SDK
   // 3) Dropping auth login requests in auth server for initial attempt, but accepting in the retry attemp, 1st attempt for auth-login fails but 2nd is successful
   TestAccount max("max", Account_Init);

   ConferenceConnectorHandle maxConfConn = max.createConferenceConnector();
   ConferenceConnectorSettings maxCloudSettings;
   maxCloudSettings.authServerUrl = "https://127.0.0.1:18084";
   maxCloudSettings.orchestrationServerUrl = "https://127.0.0.1:18082/jsonApi";
   maxCloudSettings.regionCode = "NA";
   maxCloudSettings.username = "max";
   maxCloudSettings.password = "1234";
   maxCloudSettings.ignoreCertVerification = true;
   maxCloudSettings.authServerApiKey = "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiKzxgDrW3u9+XzGN4r/GI5o+BMtty+63OT4lwEgFlkpedq5FY3eCzI4lB1WXDVip0OpCM1zYoCh6mqi7KaS1Vg==";
   maxCloudSettings.authenticationTimeoutSeconds = 10;
   max.conferenceConnector->setConnectionSettings(maxConfConn, maxCloudSettings);
   max.conferenceConnector->connectToConferenceService(maxConfConn);

   authInternal->dropIncomingLoginRequests(true);

   auto authEvent5 = std::async(std::launch::async, [&]()
   {
      JsonApiUserHandle jsonApiUser = 0;
      NewLoginEvent args;

      // Server should not receive any new login events
      ASSERT_FALSE(cpcExpectEvent(orch.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 5000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args));
   });

   waitForMs(authEvent5, std::chrono::milliseconds(30000));

   authInternal->dropIncomingLoginRequests(false);
   auto authEvent6 = std::async(std::launch::async, [&]()
   {
      JsonApiUserHandle jsonApiUser = 0;
      NewLoginEvent args;

      // Server receives the login attempt due to the auth retry and sends the response
      ASSERT_TRUE(cpcExpectEvent(orch.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 15000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args));
      ASSERT_NE(jsonApiUser, 0);
      orch.jsonApiServer->setJsonApiUserContext(jsonApiUser, orch.phone, permissions);
      orch.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);
   });

   auto maxEvent = std::async(std::launch::async, [&]()
   {
      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(max.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 20000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
      }
 
      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(max.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 20000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(max.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 20000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      max.conferenceConnector->destroyConferenceConnector(maxConfConn);
   });

   waitFor2Ms(maxEvent, authEvent6, std::chrono::milliseconds(30000));

   CPCAPI2::ConferenceBridge::ConferenceBridgeManager::getInterface(orch.phone)->shutdown();
   CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(orch.phone)->flushAll();
   CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(orch.phone)->shutdown();
   orch.jsonApiServer->shutdown();

   auth.authServer->shutdown();

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
}

TEST_F(ConferenceConnectorTests, PersistentConferenceHostStopStart_NPE)
{
   LoginResultEvent loginResult; loginResult.success = true;
   cpc::vector<cpc::string> permissions; permissions.push_back("*");

   // Max is the auth server
   TestAccount max("max", Account_Init);
   setupAuthServer(max);

   // Maia is the orchestration and confbridge server
   TestAccount maia("maia", Account_Init);
   setupConfBridgeServer(maia);
   CPCAPI2::ConferenceBridge::ConferenceRegistrar* confRegistrar = CPCAPI2::ConferenceBridge::ConferenceRegistrar::getInterface(maia.phone);
   resip::Data user1enc = CPCAPI2::JsonApi::JsonApiServerInterface::doEncrypt("user1");

   // Alice is a client SDK (screenshare presenter)
   TestAccount alice("alice", Account_Init);

   // Bob is a client SDK (screenshare participant)
   TestAccount bob("bob", Account_Init);

   // enable H.264
   alice.video->queryCodecList();
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);
   bob.video->queryCodecList();
   bob.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);

   alice.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   bob.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   alice.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   bob.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   alice.initiateVideo(false, true);
   bob.initiateVideo(false, true);

   std::atomic_bool testDone(false);
   auto mediaEvent = std::async(std::launch::async, [&]() {
      while (!testDone) {
         // conf connector fires method calls it expects to be processed on app thread
         alice.mediaEvents->processNonUnitTestEvents(0);
         bob.mediaEvents->processNonUnitTestEvents(0);
         std::this_thread::sleep_for(std::chrono::milliseconds(20));
      }
   });

   ConferenceConnectorHandle aliceConfConn = alice.createConferenceConnector();
   ConferenceConnectorSettings aliceCloudSettings;
   aliceCloudSettings.authServerUrl = "https://127.0.0.1:18084";
   aliceCloudSettings.orchestrationServerUrl = "https://127.0.0.1:18082/jsonApi";
   aliceCloudSettings.regionCode = "NA";
   aliceCloudSettings.username = "user1";
   aliceCloudSettings.password = "1234";
   aliceCloudSettings.ignoreCertVerification = true;
   aliceCloudSettings.authServerApiKey = "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiKzxgDrW3u9+XzGN4r/GI5o+BMtty+63OT4lwEgFlkpedq5FY3eCzI4lB1WXDVip0OpCM1zYoCh6mqi7KaS1Vg==";
   alice.conferenceConnector->setConnectionSettings(aliceConfConn, aliceCloudSettings);
   alice.conferenceConnector->connectToConferenceService(aliceConfConn);

   std::promise<std::string> joinUrlPr;
   std::future<std::string> joinUrlFu = joinUrlPr.get_future();
   std::promise<bool> aliceDone;
   std::future<bool> aliceDoneFu = aliceDone.get_future();
   auto maiaEvent = std::async(std::launch::async, [&]() {
      JsonApiUserHandle jsonApiUser = 0;
      NewLoginEvent args;

      // Maia has to process the login attempt (associate the context with an SDK instance)
      ASSERT_TRUE(cpcExpectEvent(maia.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 15000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args));
      ASSERT_NE(jsonApiUser, 0);
      maia.jsonApiServer->setJsonApiUserContext(jsonApiUser, maia.phone, permissions);
      maia.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);

      // Maia has to process the login attempt (associate the context with an SDK instance)
      ASSERT_TRUE(cpcExpectEvent(maia.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 15000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args));
      ASSERT_NE(jsonApiUser, 0);
      maia.jsonApiServer->setJsonApiUserContext(jsonApiUser, maia.phone, permissions);
      maia.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);

   });

   auto aliceEvent = std::async(std::launch::async, [&]() {
      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
      }

      alice.conferenceConnector->queryConferenceList(aliceConfConn);

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 0);
      }

      CloudConferenceSettings confSettings;
      confSettings.conferenceDescription = "test";
      confSettings.conferenceType = CloudConferenceType_Screenshare;
      confSettings.conferenceId = "screenshare";
      confSettings.persistent = true;
      alice.conferenceConnector->createNewConference(aliceConfConn, confSettings);
      CloudConferenceHandle aliceScreenShare = 0;

      {
         ConferenceConnectorHandle conn;
         ConferenceCreatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceCreated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_NE(args.conference, 0);
         ASSERT_NE(args.conference, -1);
         aliceScreenShare = args.conference;
      }

      alice.conferenceConnector->queryConferenceList(aliceConfConn);

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 1);
         ASSERT_FALSE(args.conferenceList[0].joinUrl.empty());
         joinUrlPr.set_value(args.conferenceList[0].joinUrl.c_str());
         ASSERT_GT(args.conferenceList[0].conference, 0); // handle used to create a new session
      }

      //std::this_thread::sleep_for(std::chrono::milliseconds(2000));

      CloudConferenceSessionHandle aliceSession = alice.conferenceConnector->createConferenceSession(aliceScreenShare);

      CloudConferenceSessionSettings aliceSessionSettings;
      aliceSessionSettings.role = CloudConferenceRole_Host;
      alice.conferenceConnector->setSessionSettings(aliceSession, aliceSessionSettings);

      CloudConferenceSessionMediaSettings aliceSessionMedia;
      aliceSessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
      aliceSessionMedia.videoDirection = ConferenceConnector::MediaDirection_SendRecv;
      aliceSessionMedia.remoteVideoRenderSurface = alice.videoHelper->getHwndIncomingWindow();
      aliceSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
      alice.conferenceConnector->setSessionMediaSettings(aliceSession, aliceSessionMedia);

      alice.conferenceConnector->startSession(aliceSession);

      try
      {
         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connecting);
         }

         {
            ConferenceConnectorHandle conn;
            ConferenceSessionMediaStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionMediaStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.mediaStatus, CPCAPI2::ConferenceConnector::SessionMediaStatus_OfferSent);
         }

         {
            ConferenceConnectorHandle conn;
            ConferenceSessionMediaStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionMediaStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.mediaStatus, CPCAPI2::ConferenceConnector::SessionMediaStatus_AnswerReceived);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(10000));

         safeCout("ALICE ENDS SESSION (1)");
         alice.conferenceConnector->endSession(aliceSession);

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
         }


         CloudConferenceSessionHandle aliceSession2 = alice.conferenceConnector->createConferenceSession(aliceScreenShare);

         alice.conferenceConnector->setSessionSettings(aliceSession2, aliceSessionSettings);

         aliceSessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
         aliceSessionMedia.videoDirection = ConferenceConnector::MediaDirection_SendRecv;
         aliceSessionMedia.remoteVideoRenderSurface = alice.videoHelper->getHwndIncomingWindow();
         aliceSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
         alice.conferenceConnector->setSessionMediaSettings(aliceSession2, aliceSessionMedia);

         alice.conferenceConnector->startSession(aliceSession2);

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connecting);
         }

         {
            ConferenceConnectorHandle conn;
            ConferenceSessionMediaStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionMediaStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.mediaStatus, CPCAPI2::ConferenceConnector::SessionMediaStatus_OfferSent);
         }

         {
            ConferenceConnectorHandle conn;
            ConferenceSessionMediaStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionMediaStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.mediaStatus, CPCAPI2::ConferenceConnector::SessionMediaStatus_AnswerReceived);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(10000));

         safeCout("ALICE ENDS SESSION (2)");
         alice.conferenceConnector->endSession(aliceSession2);

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
         }

         aliceDone.set_value(true);
         //{
         //   ConferenceConnectorHandle conn;
         //   ConferenceEndedEvent args;
         //   bool gotOnConferenceEnded = cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceEnded", 30000, CPCAPI2::test::AlwaysTruePred(), conn, args);
         //   //if (!gotOnConferenceEnded)
         //   //{
         //   //   int* a = 0;
         //   //   *a = NULL;
         //   //}
         //   ASSERT_TRUE(gotOnConferenceEnded);
         //   ASSERT_EQ(args.conference, aliceScreenShare);
         //}

         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         alice.conferenceConnector->destroyConferenceConnector(aliceConfConn);



      }
      catch (...)
      {
      }

   });

   std::shared_ptr<std::promise<int> > bobPr(new std::promise<int>());
   std::future<int> bobFu = bobPr->get_future();
   auto bobEvent = std::async(std::launch::async, [&, bobPr]() {
      ASSERT_EQ(joinUrlFu.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
      std::string joinUrlFromAlice = joinUrlFu.get();

      ConferenceConnectorHandle bobConfConn = bob.createConferenceConnector();
      ConferenceConnectorSettings bobCloudSettings;
      bobCloudSettings.authServerUrl = "https://127.0.0.1:18084";
      bobCloudSettings.orchestrationServerUrl = ""; // not needed, we use the joinUrl instead
      bobCloudSettings.joinUrl = joinUrlFromAlice.c_str();
      bobCloudSettings.regionCode = "NA";
      bobCloudSettings.username = "user2";
      bobCloudSettings.password = "1234";
      bobCloudSettings.ignoreCertVerification = true;
      bobCloudSettings.authServerApiKey = "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiKzxgDrW3u9+XzGN4r/GI5o+BMtty+63OT4lwEgFlkpedq5FY3eCzI4lB1WXDVip0OpCM1zYoCh6mqi7KaS1Vg==";

      bob.conferenceConnector->setConnectionSettings(bobConfConn, bobCloudSettings);
      bob.conferenceConnector->connectToConferenceService(bobConfConn);

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
      }

      bob.conferenceConnector->queryConferenceList(bobConfConn);
      CloudConferenceHandle bobScreenShare;

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 1);
         bobScreenShare = args.conferenceList[0].conference;
      }

      //std::this_thread::sleep_for(std::chrono::milliseconds(2000));

      CloudConferenceSessionHandle bobSession = bob.conferenceConnector->createConferenceSession(bobScreenShare);

      CloudConferenceSessionSettings bobSessionSettings;
      bobSessionSettings.role = CloudConferenceRole_Participant;
      bob.conferenceConnector->setSessionSettings(bobSession, bobSessionSettings);

      CloudConferenceSessionMediaSettings bobSessionMedia;
      bobSessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
      bobSessionMedia.videoDirection = ConferenceConnector::MediaDirection_RecvOnly;
      bobSessionMedia.remoteVideoRenderSurface = bob.videoHelper->getHwndIncomingWindow();
      bobSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
      bob.conferenceConnector->setSessionMediaSettings(bobSession, bobSessionMedia);

      bob.conferenceConnector->startSession(bobSession);

      try
      {
         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connecting);
         }

         {
            ConferenceConnectorHandle conn;
            ConferenceSessionMediaStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionMediaStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.mediaStatus, CPCAPI2::ConferenceConnector::SessionMediaStatus_OfferSent);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
            EXPECT_EQ(args.screenshare.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_RecvOnly);
            EXPECT_EQ(args.audio.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
            EXPECT_EQ(args.video.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
            //EXPECT_NE(args.screenshare.mediaStreamId, -1);
            //bob.video->setIncomingVideoRenderTarget(args.screenshare.mediaStreamId, hwndBobRemote);
         }
         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         bobSessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
         bobSessionMedia.videoDirection = ConferenceConnector::MediaDirection_RecvOnly;
         bobSessionMedia.remoteVideoRenderSurface = bob.videoHelper->getHwndIncomingWindow();
         bobSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
         bob.conferenceConnector->setSessionMediaSettings(bobSession, bobSessionMedia);
         bob.conferenceConnector->updateSessionMedia(bobSession);

         {
            ConferenceConnectorHandle conn;
            ConferenceSessionMediaStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionMediaStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.mediaStatus, CPCAPI2::ConferenceConnector::SessionMediaStatus_AnswerReceived);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
            EXPECT_EQ(args.screenshare.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_RecvOnly);
            EXPECT_EQ(args.audio.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
            EXPECT_EQ(args.video.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
            //bob.video->setIncomingVideoRenderTarget(args.screenshare.mediaStreamId, hwndBobRemote);
         }

         //std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceParticipantListUpdatedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceParticipantListUpdated", 180000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            //EXPECT_EQ(args.participantList.size(), 2);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceParticipantListUpdatedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceParticipantListUpdated", 180000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            //EXPECT_EQ(args.participantList.size(), 1);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceParticipantListUpdatedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceParticipantListUpdated", 180000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            //EXPECT_EQ(args.participantList.size(), 2);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceParticipantListUpdatedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceParticipantListUpdated", 180000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            //EXPECT_EQ(args.participantList.size(), 1);
         }

         ASSERT_EQ(aliceDoneFu.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
         aliceDoneFu.get();

         //{
         //   // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
         //   ConferenceConnectorHandle conn;
         //   ConferenceSessionStatusChangedEvent args;
         //   EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 700000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         //   EXPECT_EQ(CPCAPI2::ConferenceConnector::SessionStatus_NotConnected, args.sessionStatus);
         //}

         //{
         //   ConferenceConnectorHandle conn;
         //   ConferenceEndedEvent args;
         //   ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceEnded", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         //   ASSERT_EQ(args.conference, bobScreenShare);
         //}

         /*
         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceParticipantListUpdatedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceParticipantListUpdated", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            //EXPECT_EQ(args.participantList.size(), 0);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 120000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         bob.conferenceConnector->endSession(bobSession);
         */
      }
      catch (...)
      {
      }
      bob.conferenceConnector->destroyConferenceConnector(bobConfConn);

      //{
      //   // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
      //   ConferenceConnectorHandle conn;
      //   ConferenceSessionStatusChangedEvent args;
      //   ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
      //   ASSERT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
      //}
      bobPr->set_value(0);
   });

   aliceEvent.wait_for(std::chrono::milliseconds(45000));
   aliceEvent.get();

   bobEvent.wait_for(std::chrono::milliseconds(45000));
   bobEvent.get();
   //bobFu.get();

   maiaEvent.wait_for(std::chrono::milliseconds(45000));
   maiaEvent.get();

   //waitFor2(aliceEvent, maiaEvent);

   testDone = true;

   CPCAPI2::ConferenceBridge::ConferenceBridgeManager::getInterface(maia.phone)->shutdown();
   CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(maia.phone)->flushAll();
   CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(maia.phone)->shutdown();
   maia.jsonApiServer->shutdown();

   max.authServer->shutdown();

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
}

class MyListNodesHandler : public CPCAPI2::ConferenceBridge::ListNodesHandler
{
public:
   MyListNodesHandler() {}
   virtual ~MyListNodesHandler() {}

   virtual int onListNodesComplete(CPCAPI2::ConferenceBridge::ConferenceRegistrarHandle registrar, const CPCAPI2::ConferenceBridge::ListNodesResult& args)
   {
      safeCout("nodes list for " << registrar);
      for (const cpc::string& n : args.nodes)
      {
         safeCout("node: " << n);
      }
      return 0;
   }
};
TEST_F(ConferenceConnectorTests, ConnectSuccessMultipleServers_OtherServer_NPE)
{
#if (CPCAPI2_BRAND_CONFERENCE_BRIDGE_REGISTRAR_MODULE == 1)
   LoginResultEvent loginResult; loginResult.success = true;
   cpc::vector<cpc::string> permissions; permissions.push_back("*");

   // Max is the auth server
   TestAccount max("max", Account_Init);
   setupAuthServer(max);

   // Maia is the orchestration and confbridge server
   TestAccount maia("maia", Account_Init);
   setupConfBridgeServer(maia);
   CPCAPI2::ConferenceBridge::ConferenceRegistrar* confRegistrar = CPCAPI2::ConferenceBridge::ConferenceRegistrar::getInterface(maia.phone);
   resip::Data user1enc = CPCAPI2::JsonApi::JsonApiServerInterface::doEncrypt("user1");

   TestAccount adam("adam", Account_Init);
   setupConfBridgeServer(adam, 18089, 9004, 9006, 18082, 2);

   std::this_thread::sleep_for(std::chrono::seconds(5));
   MyListNodesHandler listNodesHandler;
   maia.conferenceRegistrar->listNodes(&listNodesHandler);
   adam.conferenceRegistrar->listNodes(&listNodesHandler);

   // Alice is a client SDK (screenshare presenter) connected to Maia
   TestAccount alice("alice", Account_Init);

   // Bob is a client SDK (screenshare participant) connected to Adam
   TestAccount bob("bob", Account_Init);

   // enable H.264
   alice.video->queryCodecList();
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);
   bob.video->queryCodecList();
   bob.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);

   alice.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   bob.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   alice.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   bob.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

#if _WIN32
   HWND hwndAliceRemote = NULL;
   HWND hwndBobRemote = NULL;
   HWND hwndBobRemoteLarge = NULL;

   if (TestEnvironmentConfig::drawLocalVideo())
   {
      ASSERT_EQ(0, ViECreateWindow(hwndAliceRemote, 0, 0, 352, 288, "Alice (incoming)"));
      alice.video->setIncomingVideoRenderTarget(hwndAliceRemote, Media::VideoSurfaceType_WindowsHWND);

      ASSERT_EQ(0, ViECreateWindow(hwndBobRemote, 356, 0, 352, 288, "Bob (incoming)"));
      bob.video->setIncomingVideoRenderTarget(hwndBobRemote, Media::VideoSurfaceType_WindowsHWND);

      ASSERT_EQ(0, ViECreateWindow(hwndBobRemoteLarge, 0, 292, 704, 576, "Bob (incoming large)"));
   }
#endif

   std::atomic_bool testDone(false);
   auto mediaEvent = std::async(std::launch::async, [&]() {
      while (!testDone) {
         // conf connector fires method calls it expects to be processed on app thread
         alice.mediaEvents->processNonUnitTestEvents(0);
         bob.mediaEvents->processNonUnitTestEvents(0);
         std::this_thread::sleep_for(std::chrono::milliseconds(20));
      }
   });

   ConferenceConnectorHandle aliceConfConn = alice.createConferenceConnector();
   ConferenceConnectorSettings aliceCloudSettings;
   aliceCloudSettings.authServerUrl = "https://127.0.0.1:18084";
   aliceCloudSettings.orchestrationServerUrl = "https://127.0.0.1:18082/jsonApi";
   aliceCloudSettings.regionCode = "NA";
   aliceCloudSettings.username = "user1";
   aliceCloudSettings.password = "1234";
   aliceCloudSettings.authServerApiKey = "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiKzxgDrW3u9+XzGN4r/GI5o+BMtty+63OT4lwEgFlkpedq5FY3eCzI4lB1WXDVip0OpCM1zYoCh6mqi7KaS1Vg==";
   aliceCloudSettings.ignoreCertVerification = true;
   alice.conferenceConnector->setConnectionSettings(aliceConfConn, aliceCloudSettings);
   alice.conferenceConnector->connectToConferenceService(aliceConfConn);

   std::promise<std::string> joinUrlPr;
   std::future<std::string> joinUrlFu = joinUrlPr.get_future();
   auto maiaEvent = std::async(std::launch::async, [&]() {
      JsonApiUserHandle jsonApiUser = 0;
      NewLoginEvent args;

      // Maia has to process the login attempt (associate the context with an SDK instance)
      ASSERT_TRUE(cpcExpectEvent(maia.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 15000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args));
      ASSERT_NE(jsonApiUser, 0);
      maia.jsonApiServer->setJsonApiUserContext(jsonApiUser, maia.phone, permissions);
      maia.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);

      // Maia has to process the login attempt (associate the context with an SDK instance)
      ASSERT_TRUE(cpcExpectEvent(maia.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 15000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args));
      ASSERT_NE(jsonApiUser, 0);
      maia.jsonApiServer->setJsonApiUserContext(jsonApiUser, maia.phone, permissions);
      maia.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);

   });

   auto aliceEvent = std::async(std::launch::async, [&]() {
      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
      }

      alice.conferenceConnector->queryConferenceList(aliceConfConn);

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 0);
      }

      CloudConferenceSettings confSettings;
      confSettings.conferenceDescription = "test";
      confSettings.conferenceType = CloudConferenceType_Screenshare;
      confSettings.conferenceId = "screenshare";
      alice.conferenceConnector->createNewConference(aliceConfConn, confSettings);
      CloudConferenceHandle aliceScreenShare = 0;

      {
         ConferenceConnectorHandle conn;
         ConferenceCreatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceCreated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_NE(args.conference, 0);
         ASSERT_NE(args.conference, -1);
         aliceScreenShare = args.conference;
      }

      alice.conferenceConnector->queryConferenceList(aliceConfConn);

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 1);
         ASSERT_FALSE(args.conferenceList[0].joinUrl.empty());
         joinUrlPr.set_value(args.conferenceList[0].joinUrl.c_str());
         ASSERT_GT(args.conferenceList[0].conference, 0); // handle used to create a new session
      }

      CloudConferenceSessionHandle aliceSession = alice.conferenceConnector->createConferenceSession(aliceScreenShare);

      CloudConferenceSessionSettings aliceSessionSettings;
      aliceSessionSettings.role = CloudConferenceRole_Host;
      alice.conferenceConnector->setSessionSettings(aliceSession, aliceSessionSettings);

      CloudConferenceSessionMediaSettings aliceSessionMedia;
      aliceSessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
      aliceSessionMedia.videoDirection = ConferenceConnector::MediaDirection_SendRecv;
#if _WIN32
      aliceSessionMedia.remoteVideoRenderSurface = hwndAliceRemote;
      aliceSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
#endif
      alice.conferenceConnector->setSessionMediaSettings(aliceSession, aliceSessionMedia);

      alice.conferenceConnector->startSession(aliceSession);

      try
      {
         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connecting);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(20000));

         safeCout("ALICE ENDS SESSION");
         alice.conferenceConnector->endSession(aliceSession);

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
         }

         {
            ConferenceConnectorHandle conn;
            ConferenceEndedEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceEnded", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(args.conference, aliceScreenShare);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         alice.conferenceConnector->destroyConferenceConnector(aliceConfConn);



      }
      catch (...)
      {
      }

   });

   std::shared_ptr<std::promise<int> > bobPr(new std::promise<int>());
   std::future<int> bobFu = bobPr->get_future();
   auto bobEvent = std::async(std::launch::async, [&, bobPr]() {
      ASSERT_EQ(joinUrlFu.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
      std::string joinUrlFromAlice = joinUrlFu.get();

      // muck with the joinUrl to simulate a load balancer connecting us to Adam instead of Maia
      joinUrlFromAlice.at(22) = '9';

      ConferenceConnectorHandle bobConfConn = bob.createConferenceConnector();
      ConferenceConnectorSettings bobCloudSettings;
      bobCloudSettings.authServerUrl = "https://127.0.0.1:18084";
      bobCloudSettings.orchestrationServerUrl = ""; // not needed, we use the joinUrl instead
      bobCloudSettings.joinUrl = joinUrlFromAlice.c_str();
      bobCloudSettings.regionCode = "NA";
      bobCloudSettings.username = "user2";
      bobCloudSettings.password = "1234";
      bobCloudSettings.authServerApiKey = "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiKzxgDrW3u9+XzGN4r/GI5o+BMtty+63OT4lwEgFlkpedq5FY3eCzI4lB1WXDVip0OpCM1zYoCh6mqi7KaS1Vg==";
      bobCloudSettings.ignoreCertVerification = true;
      bob.conferenceConnector->setConnectionSettings(bobConfConn, bobCloudSettings);
      bob.conferenceConnector->connectToConferenceService(bobConfConn);

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
      }

      bob.conferenceConnector->queryConferenceList(bobConfConn);
      CloudConferenceHandle bobScreenShare;

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 1);
         bobScreenShare = args.conferenceList[0].conference;
      }

      CloudConferenceSessionHandle bobSession = bob.conferenceConnector->createConferenceSession(bobScreenShare);

      CloudConferenceSessionSettings bobSessionSettings;
      bobSessionSettings.role = CloudConferenceRole_Participant;
      bob.conferenceConnector->setSessionSettings(bobSession, bobSessionSettings);

      CloudConferenceSessionMediaSettings bobSessionMedia;
      bobSessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
      bobSessionMedia.videoDirection = ConferenceConnector::MediaDirection_RecvOnly;
#if _WIN32
      bobSessionMedia.remoteVideoRenderSurface = hwndBobRemote;
      bobSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
#endif
      bob.conferenceConnector->setSessionMediaSettings(bobSession, bobSessionMedia);

      bob.conferenceConnector->startSession(bobSession);

      try
      {
         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connecting);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
            EXPECT_EQ(args.screenshare.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_RecvOnly);
            EXPECT_EQ(args.audio.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
            EXPECT_EQ(args.video.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
            //EXPECT_NE(args.screenshare.mediaStreamId, -1);
            //bob.video->setIncomingVideoRenderTarget(args.screenshare.mediaStreamId, hwndBobRemote);
         }
         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         bobSessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
         bobSessionMedia.videoDirection = ConferenceConnector::MediaDirection_RecvOnly;
#if _WIN32
         bobSessionMedia.remoteVideoRenderSurface = hwndBobRemoteLarge;
         bobSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
#endif
         bob.conferenceConnector->setSessionMediaSettings(bobSession, bobSessionMedia);
         bob.conferenceConnector->updateSessionMedia(bobSession);

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
            EXPECT_EQ(args.screenshare.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_RecvOnly);
            EXPECT_EQ(args.audio.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
            EXPECT_EQ(args.video.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
            //bob.video->setIncomingVideoRenderTarget(args.screenshare.mediaStreamId, hwndBobRemote);
         }

         //std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceParticipantListUpdatedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceParticipantListUpdated", 180000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            //EXPECT_EQ(args.participantList.size(), 0);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 700000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(CPCAPI2::ConferenceConnector::SessionStatus_NotConnected, args.sessionStatus);
         }

         {
            ConferenceConnectorHandle conn;
            ConferenceEndedEvent args;
            ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceEnded", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(args.conference, bobScreenShare);
         }

         /*
         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceParticipantListUpdatedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceParticipantListUpdated", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            //EXPECT_EQ(args.participantList.size(), 0);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 120000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         bob.conferenceConnector->endSession(bobSession);
         */
      }
      catch (...)
      {
      }
      bob.conferenceConnector->destroyConferenceConnector(bobConfConn);

      //{
      //   // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
      //   ConferenceConnectorHandle conn;
      //   ConferenceSessionStatusChangedEvent args;
      //   ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
      //   ASSERT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
      //}
      bobPr->set_value(0);
   });

   aliceEvent.wait_for(std::chrono::milliseconds(45000));
   aliceEvent.get();

   bobEvent.wait_for(std::chrono::milliseconds(45000));
   bobEvent.get();
   //bobFu.get();

   maiaEvent.wait_for(std::chrono::milliseconds(45000));
   maiaEvent.get();

   //waitFor2(aliceEvent, maiaEvent);

   testDone = true;

#if _WIN32
   DestroyWindow(hwndAliceRemote);
   DestroyWindow(hwndBobRemote);
   DestroyWindow(hwndBobRemoteLarge);
#endif

   CPCAPI2::ConferenceBridge::ConferenceBridgeManager::getInterface(maia.phone)->shutdown();
   CPCAPI2::ConferenceBridge::ConferenceBridgeManager::getInterface(adam.phone)->shutdown();

   CPCAPI2::ConferenceBridge::ConferenceRegistrar::getInterface(maia.phone)->shutdown();
   CPCAPI2::ConferenceBridge::ConferenceRegistrar::getInterface(adam.phone)->shutdown();

   CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(maia.phone)->flushAll();
   CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(maia.phone)->shutdown();
   maia.conferenceRegistrar->shutdown();
   maia.jsonApiServer->shutdown();

   CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(adam.phone)->flushAll();
   CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(adam.phone)->shutdown();
   adam.conferenceRegistrar->shutdown();
   adam.jsonApiServer->shutdown();

   max.authServer->shutdown();

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
#endif // CPCAPI2_BRAND_CONFERENCE_BRIDGE_REGISTRAR_MODULE
}

TEST_F(ConferenceConnectorTests, ConnectSuccessMultipleServers_OtherServerStartedLater_NPE)
{
#if (CPCAPI2_BRAND_CONFERENCE_BRIDGE_REGISTRAR_MODULE == 1)
   LoginResultEvent loginResult; loginResult.success = true;
   cpc::vector<cpc::string> permissions; permissions.push_back("*");
   const int maiaNodeId = 1 + (std::abs(resip::Random::getCryptoRandom()) % 0x7ffe);
   const int adamNodeId = 1 + (std::abs(resip::Random::getCryptoRandom()) % 0x7ffe);

   // Max is the auth server
   TestAccount max("max", Account_Init);
   setupAuthServer(max);

   // Maia is the orchestration and confbridge server
   TestAccount maia("maia", Account_Init);
   setupConfBridgeServer(maia, 18082, 9003, 9005, 18082, maiaNodeId);
   CPCAPI2::ConferenceBridge::ConferenceRegistrar* confRegistrar = CPCAPI2::ConferenceBridge::ConferenceRegistrar::getInterface(maia.phone);
   resip::Data user1enc = CPCAPI2::JsonApi::JsonApiServerInterface::doEncrypt("user1");

   TestAccount adam("adam", Account_Init);
   setupConfBridgeServer(adam, 18089, 9004, 9006, 18082, adamNodeId /*2*/);

   // Alice is a client SDK (screenshare presenter) connected to Maia
   TestAccount alice("alice", Account_Init);

   // Bob is a client SDK (screenshare participant) connected to Adam
   TestAccount bob("bob", Account_Init);

   // enable H.264
   alice.video->queryCodecList();
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);
   bob.video->queryCodecList();
   bob.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);

   alice.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   bob.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   alice.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   bob.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

#if _WIN32
   HWND hwndAliceRemote = NULL;
   HWND hwndBobRemote = NULL;
   HWND hwndBobRemoteLarge = NULL;

   if (TestEnvironmentConfig::drawLocalVideo())
   {
      ASSERT_EQ(0, ViECreateWindow(hwndAliceRemote, 0, 0, 352, 288, "Alice (incoming)"));
      alice.video->setIncomingVideoRenderTarget(hwndAliceRemote, Media::VideoSurfaceType_WindowsHWND);

      ASSERT_EQ(0, ViECreateWindow(hwndBobRemote, 356, 0, 352, 288, "Bob (incoming)"));
      bob.video->setIncomingVideoRenderTarget(hwndBobRemote, Media::VideoSurfaceType_WindowsHWND);

      ASSERT_EQ(0, ViECreateWindow(hwndBobRemoteLarge, 0, 292, 704, 576, "Bob (incoming large)"));
   }
#endif

   std::atomic_bool testDone(false);
   auto mediaEvent = std::async(std::launch::async, [&]() {
      while (!testDone) {
         // conf connector fires method calls it expects to be processed on app thread
         alice.mediaEvents->processNonUnitTestEvents(0);
         bob.mediaEvents->processNonUnitTestEvents(0);
         std::this_thread::sleep_for(std::chrono::milliseconds(20));
      }
   });

   ConferenceConnectorHandle aliceConfConn = alice.createConferenceConnector();
   ConferenceConnectorSettings aliceCloudSettings;
   aliceCloudSettings.authServerUrl = "https://127.0.0.1:18084";
   aliceCloudSettings.orchestrationServerUrl = "https://127.0.0.1:18082/jsonApi";
   aliceCloudSettings.regionCode = "NA";
   aliceCloudSettings.username = "user1";
   aliceCloudSettings.password = "1234";
   aliceCloudSettings.ignoreCertVerification = true;
   aliceCloudSettings.authServerApiKey = "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiKzxgDrW3u9+XzGN4r/GI5o+BMtty+63OT4lwEgFlkpedq5FY3eCzI4lB1WXDVip0OpCM1zYoCh6mqi7KaS1Vg==";
   alice.conferenceConnector->setConnectionSettings(aliceConfConn, aliceCloudSettings);
   alice.conferenceConnector->connectToConferenceService(aliceConfConn);

   std::promise<std::string> joinUrlPr;
   std::future<std::string> joinUrlFu = joinUrlPr.get_future();
   auto maiaEvent = std::async(std::launch::async, [&]() {
      JsonApiUserHandle jsonApiUser = 0;
      NewLoginEvent args;

      // Maia has to process the login attempt (associate the context with an SDK instance)
      ASSERT_TRUE(cpcExpectEvent(maia.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 15000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args));
      ASSERT_NE(jsonApiUser, 0);
      maia.jsonApiServer->setJsonApiUserContext(jsonApiUser, maia.phone, permissions);
      maia.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);

      // Maia has to process the login attempt (associate the context with an SDK instance)
      ASSERT_TRUE(cpcExpectEvent(maia.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 15000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args));
      ASSERT_NE(jsonApiUser, 0);
      maia.jsonApiServer->setJsonApiUserContext(jsonApiUser, maia.phone, permissions);
      maia.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);

   });

   auto aliceEvent = std::async(std::launch::async, [&]() {
      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
      }

      alice.conferenceConnector->queryConferenceList(aliceConfConn);

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 0);
      }

      CloudConferenceSettings confSettings;
      confSettings.conferenceDescription = "test";
      confSettings.conferenceType = CloudConferenceType_Screenshare;
      confSettings.conferenceId = "screenshare";
      alice.conferenceConnector->createNewConference(aliceConfConn, confSettings);
      CloudConferenceHandle aliceScreenShare = 0;

      {
         ConferenceConnectorHandle conn;
         ConferenceCreatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceCreated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_NE(args.conference, 0);
         ASSERT_NE(args.conference, -1);
         aliceScreenShare = args.conference;
      }

      std::this_thread::sleep_for(std::chrono::seconds(4));

      alice.conferenceConnector->queryConferenceList(aliceConfConn);

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 1);
         ASSERT_FALSE(args.conferenceList[0].joinUrl.empty());
         joinUrlPr.set_value(args.conferenceList[0].joinUrl.c_str());
         ASSERT_GT(args.conferenceList[0].conference, 0); // handle used to create a new session
      }

      CloudConferenceSessionHandle aliceSession = alice.conferenceConnector->createConferenceSession(aliceScreenShare);

      CloudConferenceSessionSettings aliceSessionSettings;
      aliceSessionSettings.role = CloudConferenceRole_Host;
      alice.conferenceConnector->setSessionSettings(aliceSession, aliceSessionSettings);

      CloudConferenceSessionMediaSettings aliceSessionMedia;
      aliceSessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
      aliceSessionMedia.videoDirection = ConferenceConnector::MediaDirection_SendRecv;
#if _WIN32
      aliceSessionMedia.remoteVideoRenderSurface = hwndAliceRemote;
      aliceSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
#endif
      alice.conferenceConnector->setSessionMediaSettings(aliceSession, aliceSessionMedia);

      alice.conferenceConnector->startSession(aliceSession);

      try
      {
         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connecting);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(20000));

         safeCout("ALICE ENDS SESSION");
         alice.conferenceConnector->endSession(aliceSession);

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
         }

         {
            ConferenceConnectorHandle conn;
            ConferenceEndedEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceEnded", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(args.conference, aliceScreenShare);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         alice.conferenceConnector->destroyConferenceConnector(aliceConfConn);



      }
      catch (...)
      {
      }

   });

   std::shared_ptr<std::promise<int> > bobPr(new std::promise<int>());
   std::future<int> bobFu = bobPr->get_future();
   auto bobEvent = std::async(std::launch::async, [&, bobPr]() {
      ASSERT_EQ(joinUrlFu.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
      std::string joinUrlFromAlice = joinUrlFu.get();

      // muck with the joinUrl to simulate a load balancer connecting us to Adam instead of Maia
      joinUrlFromAlice.at(22) = '9';

      ConferenceConnectorHandle bobConfConn = bob.createConferenceConnector();
      ConferenceConnectorSettings bobCloudSettings;
      bobCloudSettings.authServerUrl = "https://127.0.0.1:18084";
      bobCloudSettings.orchestrationServerUrl = ""; // not needed, we use the joinUrl instead
      bobCloudSettings.joinUrl = joinUrlFromAlice.c_str();
      bobCloudSettings.regionCode = "NA";
      bobCloudSettings.username = "user2";
      bobCloudSettings.password = "1234";
      bobCloudSettings.authServerApiKey = "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiKzxgDrW3u9+XzGN4r/GI5o+BMtty+63OT4lwEgFlkpedq5FY3eCzI4lB1WXDVip0OpCM1zYoCh6mqi7KaS1Vg==";
      bobCloudSettings.ignoreCertVerification = true;
      bob.conferenceConnector->setConnectionSettings(bobConfConn, bobCloudSettings);
      bob.conferenceConnector->connectToConferenceService(bobConfConn);

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
      }

      bob.conferenceConnector->queryConferenceList(bobConfConn);
      CloudConferenceHandle bobScreenShare;

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 1);
         bobScreenShare = args.conferenceList[0].conference;
      }

      CloudConferenceSessionHandle bobSession = bob.conferenceConnector->createConferenceSession(bobScreenShare);

      CloudConferenceSessionSettings bobSessionSettings;
      bobSessionSettings.role = CloudConferenceRole_Participant;
      bob.conferenceConnector->setSessionSettings(bobSession, bobSessionSettings);

      CloudConferenceSessionMediaSettings bobSessionMedia;
      bobSessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
      bobSessionMedia.videoDirection = ConferenceConnector::MediaDirection_RecvOnly;
#if _WIN32
      bobSessionMedia.remoteVideoRenderSurface = hwndBobRemote;
      bobSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
#endif
      bob.conferenceConnector->setSessionMediaSettings(bobSession, bobSessionMedia);

      bob.conferenceConnector->startSession(bobSession);

      try
      {
         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connecting);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
            EXPECT_EQ(args.screenshare.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_RecvOnly);
            EXPECT_EQ(args.audio.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
            EXPECT_EQ(args.video.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
            //EXPECT_NE(args.screenshare.mediaStreamId, -1);
            //bob.video->setIncomingVideoRenderTarget(args.screenshare.mediaStreamId, hwndBobRemote);
         }
         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         bobSessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
         bobSessionMedia.videoDirection = ConferenceConnector::MediaDirection_RecvOnly;
#if _WIN32
         bobSessionMedia.remoteVideoRenderSurface = hwndBobRemoteLarge;
         bobSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
#endif
         bob.conferenceConnector->setSessionMediaSettings(bobSession, bobSessionMedia);
         bob.conferenceConnector->updateSessionMedia(bobSession);

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
            EXPECT_EQ(args.screenshare.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_RecvOnly);
            EXPECT_EQ(args.audio.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
            EXPECT_EQ(args.video.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
            //bob.video->setIncomingVideoRenderTarget(args.screenshare.mediaStreamId, hwndBobRemote);
         }

         //std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceParticipantListUpdatedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceParticipantListUpdated", 180000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            //EXPECT_EQ(args.participantList.size(), 0);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 700000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(CPCAPI2::ConferenceConnector::SessionStatus_NotConnected, args.sessionStatus);
         }

         {
            ConferenceConnectorHandle conn;
            ConferenceEndedEvent args;
            ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceEnded", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(args.conference, bobScreenShare);
         }

         /*
         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceParticipantListUpdatedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceParticipantListUpdated", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            //EXPECT_EQ(args.participantList.size(), 0);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 120000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         bob.conferenceConnector->endSession(bobSession);
         */
      }
      catch (...)
      {
      }
      bob.conferenceConnector->destroyConferenceConnector(bobConfConn);

      //{
      //   // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
      //   ConferenceConnectorHandle conn;
      //   ConferenceSessionStatusChangedEvent args;
      //   ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
      //   ASSERT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
      //}
      bobPr->set_value(0);
   });

   aliceEvent.wait_for(std::chrono::milliseconds(45000));
   aliceEvent.get();

   bobEvent.wait_for(std::chrono::milliseconds(45000));
   bobEvent.get();
   //bobFu.get();

   maiaEvent.wait_for(std::chrono::milliseconds(45000));
   maiaEvent.get();

   //waitFor2(aliceEvent, maiaEvent);

   testDone = true;

#if _WIN32
   DestroyWindow(hwndAliceRemote);
   DestroyWindow(hwndBobRemote);
   DestroyWindow(hwndBobRemoteLarge);
#endif

   CPCAPI2::ConferenceBridge::ConferenceBridgeManager::getInterface(maia.phone)->shutdown();
   CPCAPI2::ConferenceBridge::ConferenceBridgeManager::getInterface(adam.phone)->shutdown();

   CPCAPI2::ConferenceBridge::ConferenceRegistrar::getInterface(maia.phone)->shutdown();
   CPCAPI2::ConferenceBridge::ConferenceRegistrar::getInterface(adam.phone)->shutdown();

   CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(maia.phone)->flushAll();
   CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(maia.phone)->shutdown();
   maia.conferenceRegistrar->shutdown();
   maia.jsonApiServer->shutdown();

   CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(adam.phone)->flushAll();
   CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(adam.phone)->shutdown();
   adam.conferenceRegistrar->shutdown();
   adam.jsonApiServer->shutdown();

   max.authServer->shutdown();

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
#endif // CPCAPI2_BRAND_CONFERENCE_BRIDGE_REGISTRAR_MODULE
}

TEST_F(ConferenceConnectorTests, CreateMultipleConferencesWithSameId)
{
   LoginResultEvent loginResult; loginResult.success = true;
   cpc::vector<cpc::string> permissions; permissions.push_back("*");

   // Max is the auth server
   TestAccount max("max", Account_Init);
   setupAuthServer(max);

   // Maia is the orchestration and confbridge server
   TestAccount maia("maia", Account_Init);
   setupConfBridgeServer(maia);
   CPCAPI2::ConferenceBridge::ConferenceRegistrar* confRegistrar = CPCAPI2::ConferenceBridge::ConferenceRegistrar::getInterface(maia.phone);

   // Alice is a client SDK (screenshare presenter)
   TestAccount alice("alice", Account_Init);

   // Bob is a client SDK (screenshare participant)
   TestAccount bob("bob", Account_Init);

   // enable H.264
   alice.video->queryCodecList();
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);
   bob.video->queryCodecList();
   bob.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);

   alice.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   bob.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   alice.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   bob.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

#if _WIN32
   HWND hwndAliceRemote = NULL;
   HWND hwndBobRemote = NULL;
   HWND hwndBobRemoteLarge = NULL;

   ASSERT_EQ(0, ViECreateWindow(hwndAliceRemote, 0, 0, 352, 288, "Alice (incoming)"));
   alice.video->setIncomingVideoRenderTarget(hwndAliceRemote, Media::VideoSurfaceType_WindowsHWND);

   ASSERT_EQ(0, ViECreateWindow(hwndBobRemote, 356, 0, 352, 288, "Bob (incoming)"));
   bob.video->setIncomingVideoRenderTarget(hwndBobRemote, Media::VideoSurfaceType_WindowsHWND);

   ASSERT_EQ(0, ViECreateWindow(hwndBobRemoteLarge, 0, 292, 704, 576, "Bob (incoming large)"));
#endif

   std::atomic_bool testDone(false);
   auto mediaEvent = std::async(std::launch::async, [&]() {
      while (!testDone) {
         // conf connector fires method calls it expects to be processed on app thread
         alice.mediaEvents->processNonUnitTestEvents(0);
         bob.mediaEvents->processNonUnitTestEvents(0);
         std::this_thread::sleep_for(std::chrono::milliseconds(20));
      }
   });

   ConferenceConnectorHandle aliceConfConn = alice.createConferenceConnector();
   ConferenceConnectorSettings aliceCloudSettings;
   aliceCloudSettings.authServerUrl = "https://127.0.0.1:18084";
   //aliceCloudSettings.orchestrationServerUrl = "https://127.0.0.1:18082/jsonApi";
   aliceCloudSettings.joinUrl = "https://127.0.0.1:18082/screenshare/testMultipleSameId";
   aliceCloudSettings.regionCode = "NA";
   aliceCloudSettings.username = "user1";
   aliceCloudSettings.password = "1234";
   aliceCloudSettings.ignoreCertVerification = true;
   aliceCloudSettings.authServerApiKey = "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiKzxgDrW3u9+XzGN4r/GI5o+BMtty+63OT4lwEgFlkpedq5FY3eCzI4lB1WXDVip0OpCM1zYoCh6mqi7KaS1Vg==";
   alice.conferenceConnector->setConnectionSettings(aliceConfConn, aliceCloudSettings);
   alice.conferenceConnector->connectToConferenceService(aliceConfConn);

   CPCAPI2::ConferenceBridge::ConferenceBridgeManager* maiaConfBridgeMgr = CPCAPI2::ConferenceBridge::ConferenceBridgeManager::getInterface(maia.phone);

   auto maiaEvent = std::async(std::launch::async, [&]() {
      JsonApiUserHandle jsonApiUser = 0;
      NewLoginEvent args;

      while (!testDone)
      {
         maiaConfBridgeMgr->process(kBlockingModeNonBlocking);

         // Maia has to process the login attempt (associate the context with an SDK instance)
         if (cpcWaitForEvent(maia.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 1000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args))
         {
            ASSERT_NE(jsonApiUser, 0);
            maia.jsonApiServer->setJsonApiUserContext(jsonApiUser, maia.phone, permissions);
            maia.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);
         }
      }
   });

   std::mutex waitToCreateMtx;
   std::condition_variable waitToCreateConference;

   auto aliceEvent = std::async(std::launch::async, [&]() {
      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
      }

      alice.conferenceConnector->queryConferenceList(aliceConfConn);

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
      }

      CloudConferenceSettings confSettings;
      confSettings.conferenceDescription = "test";
      confSettings.conferenceType = CloudConferenceType_Screenshare;
      confSettings.conferenceId = "screenshare";
      {
         std::unique_lock<std::mutex> lck(waitToCreateMtx);
         safeCout("ALICE WAITS");
         waitToCreateConference.wait(lck);
      }
      alice.conferenceConnector->createNewConference(aliceConfConn, confSettings);
      safeCout("ALICE CREATES");
      std::this_thread::yield();

      {
         ConferenceConnectorHandle conn;
         ConferenceCreatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceCreated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_NE(args.conference, 0);
         ASSERT_NE(args.conference, -1);
      }

      alice.conferenceConnector->queryConferenceList(aliceConfConn);

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 1);
         ASSERT_FALSE(args.conferenceList[0].joinUrl.empty());
         ASSERT_GT(args.conferenceList[0].conference, 0); // handle used to create a new session
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      alice.conferenceConnector->destroyConferenceConnector(aliceConfConn);


   });

   auto bobEvent = std::async(std::launch::async, [&]() {
      ConferenceConnectorHandle bobConfConn = bob.createConferenceConnector();
      ConferenceConnectorSettings bobCloudSettings;
      bobCloudSettings.authServerUrl = "https://127.0.0.1:18084";
      //bobCloudSettings.orchestrationServerUrl = "https://127.0.0.1:18082/jsonApi";
      bobCloudSettings.joinUrl = "https://127.0.0.1:18082/screenshare/testMultipleSameId";
      bobCloudSettings.regionCode = "NA";
      bobCloudSettings.username = "user2";
      bobCloudSettings.password = "1234";
      bobCloudSettings.authServerApiKey = "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiKzxgDrW3u9+XzGN4r/GI5o+BMtty+63OT4lwEgFlkpedq5FY3eCzI4lB1WXDVip0OpCM1zYoCh6mqi7KaS1Vg==";
      bobCloudSettings.ignoreCertVerification = true;
      bob.conferenceConnector->setConnectionSettings(bobConfConn, bobCloudSettings);
      bob.conferenceConnector->connectToConferenceService(bobConfConn);

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
      }

      bob.conferenceConnector->queryConferenceList(bobConfConn);

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
      }

      CloudConferenceSettings confSettings;
      confSettings.conferenceDescription = "test";
      confSettings.conferenceType = CloudConferenceType_Screenshare;
      confSettings.conferenceId = "screenshare";
      {
         std::unique_lock<std::mutex> lck(waitToCreateMtx);
         safeCout("BOB WAITS");
         waitToCreateConference.wait(lck);
      }
      bob.conferenceConnector->createNewConference(bobConfConn, confSettings);
      safeCout("BOB CREATES");
      std::this_thread::yield();

      {
         ConferenceConnectorHandle conn;
         ConferenceCreatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceCreated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_NE(args.conference, 0);
         ASSERT_NE(args.conference, -1);
      }

      bob.conferenceConnector->queryConferenceList(bobConfConn);

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 1);
         ASSERT_FALSE(args.conferenceList[0].joinUrl.empty());
         ASSERT_GT(args.conferenceList[0].conference, 0); // handle used to create a new session
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      bob.conferenceConnector->destroyConferenceConnector(bobConfConn);

      //{
      //   // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
      //   ConferenceConnectorHandle conn;
      //   ConferenceSessionStatusChangedEvent args;
      //   ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
      //   ASSERT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
      //}
   });

   std::this_thread::sleep_for(std::chrono::milliseconds(15000));
   {
      std::unique_lock<std::mutex> lck(waitToCreateMtx);
      waitToCreateConference.notify_all();
   }

   aliceEvent.wait_for(std::chrono::milliseconds(45000));
   aliceEvent.get();

   bobEvent.wait_for(std::chrono::milliseconds(45000));
   bobEvent.get();
   //bobFu.get();

   testDone = true;

   maiaEvent.wait_for(std::chrono::milliseconds(45000));
   maiaEvent.get();

   //waitFor2(aliceEvent, maiaEvent);

#if _WIN32
   DestroyWindow(hwndAliceRemote);
   DestroyWindow(hwndBobRemote);
   DestroyWindow(hwndBobRemoteLarge);
#endif

   CPCAPI2::ConferenceBridge::ConferenceBridgeManager::getInterface(maia.phone)->shutdown();
   CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(maia.phone)->flushAll();
   CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(maia.phone)->shutdown();
   maia.jsonApiServer->shutdown();

   max.authServer->shutdown();

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
}

TEST_F(ConferenceConnectorTests, HostRepeatPauseStartScreenshare)
{
	LoginResultEvent loginResult; loginResult.success = true;
	cpc::vector<cpc::string> permissions; permissions.push_back("*");

	// Max is the auth server
	TestAccount max("max", Account_Init);
	setupAuthServer(max);

	// Maia is the orchestration and confbridge server
	TestAccount maia("maia", Account_Init);
	setupConfBridgeServer(maia);
   CPCAPI2::ConferenceBridge::ConferenceRegistrar* confRegistrar = CPCAPI2::ConferenceBridge::ConferenceRegistrar::getInterface(maia.phone);
   resip::Data user1enc = CPCAPI2::JsonApi::JsonApiServerInterface::doEncrypt("user1");

	// Alice is a client SDK (screenshare presenter)
	TestAccount alice("alice", Account_Init);

	// enable H.264
	alice.video->queryCodecList();
	alice.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
	alice.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
	alice.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);

	alice.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

	alice.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

	std::atomic_bool testDone(false);
	auto mediaEvent = std::async(std::launch::async, [&]() {
		while (!testDone) {
		    // conf connector fires method calls it expects to be processed on app thread
			alice.mediaEvents->processNonUnitTestEvents(0);
			std::this_thread::sleep_for(std::chrono::milliseconds(20));
		}
	});

	ConferenceConnectorHandle aliceConfConn = alice.createConferenceConnector();
	ConferenceConnectorSettings aliceCloudSettings;
	aliceCloudSettings.authServerUrl = "https://127.0.0.1:18084";
	aliceCloudSettings.orchestrationServerUrl = "https://127.0.0.1:18082/jsonApi";
	aliceCloudSettings.regionCode = "NA";
	aliceCloudSettings.username = "user1";
	aliceCloudSettings.password = "1234";
   aliceCloudSettings.authServerApiKey = "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiKzxgDrW3u9+XzGN4r/GI5o+BMtty+63OT4lwEgFlkpedq5FY3eCzI4lB1WXDVip0OpCM1zYoCh6mqi7KaS1Vg==";
	aliceCloudSettings.ignoreCertVerification = true;
	alice.conferenceConnector->setConnectionSettings(aliceConfConn, aliceCloudSettings);
	alice.conferenceConnector->connectToConferenceService(aliceConfConn);

	std::promise<std::string> joinUrlPr;
	std::future<std::string> joinUrlFu = joinUrlPr.get_future();
	auto maiaEvent = std::async(std::launch::async, [&]() {
		JsonApiUserHandle jsonApiUser = 0;
		NewLoginEvent args;

		// Maia has to process the login attempt (associate the context with an SDK instance)
		ASSERT_TRUE(cpcExpectEvent(maia.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 15000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args));
		ASSERT_NE(jsonApiUser, 0);
		maia.jsonApiServer->setJsonApiUserContext(jsonApiUser, maia.phone, permissions);
		maia.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);

	});

	auto aliceEvent = std::async(std::launch::async, [&]() {
		{
			ConferenceConnectorHandle conn;
			ServiceConnectionStatusEvent args;
			ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
			ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
		}

		{
			ConferenceConnectorHandle conn;
			ServiceConnectionStatusEvent args;
			ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
			ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
		}

		{
			ConferenceConnectorHandle conn;
			ServiceConnectionStatusEvent args;
			ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
			ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
		}

		alice.conferenceConnector->queryConferenceList(aliceConfConn);

		{
			ConferenceConnectorHandle conn;
			ConferenceListUpdatedEvent args;
			ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
			ASSERT_EQ(args.conferenceList.size(), 0);
		}


         CloudConferenceSettings confSettings;
         confSettings.conferenceDescription = "test";
         confSettings.conferenceType = CloudConferenceType_Screenshare;
         confSettings.conferenceId = "screenshare";
         alice.conferenceConnector->createNewConference(aliceConfConn, confSettings);
         CloudConferenceHandle aliceScreenShare = 0;

         {
            ConferenceConnectorHandle conn;
            ConferenceCreatedEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceCreated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_NE(args.conference, 0);
            ASSERT_NE(args.conference, -1);
            aliceScreenShare = args.conference;
         }

         alice.conferenceConnector->queryConferenceList(aliceConfConn);

         {
            ConferenceConnectorHandle conn;
            ConferenceListUpdatedEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         }


         CloudConferenceSessionHandle aliceSession = alice.conferenceConnector->createConferenceSession(aliceScreenShare);

         CloudConferenceSessionSettings aliceSessionSettings;
         aliceSessionSettings.role = CloudConferenceRole_Host;
         alice.conferenceConnector->setSessionSettings(aliceSession, aliceSessionSettings);

         CloudConferenceSessionMediaSettings aliceSessionMedia;
         aliceSessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
         aliceSessionMedia.videoDirection = ConferenceConnector::MediaDirection_SendRecv;
         alice.conferenceConnector->setSessionMediaSettings(aliceSession, aliceSessionMedia);

         alice.conferenceConnector->startSession(aliceSession);
         std::this_thread::sleep_for(std::chrono::seconds(5));

         // should crash sometime in this loop for OBELISK-5504
         for (int i = 0; i < 10; ++i)
         {
            aliceSessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
            aliceSessionMedia.videoDirection = ConferenceConnector::MediaDirection_RecvOnly;
            alice.conferenceConnector->setSessionMediaSettings(aliceSession, aliceSessionMedia);
            alice.conferenceConnector->updateSessionMedia(aliceSession);
          
            aliceSessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
            aliceSessionMedia.videoDirection = ConferenceConnector::MediaDirection_SendRecv;
            alice.conferenceConnector->setSessionMediaSettings(aliceSession, aliceSessionMedia);
            alice.conferenceConnector->updateSessionMedia(aliceSession);
            
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
         }

         alice.conferenceConnector->endSession(aliceSession);
     

	});

	aliceEvent.wait_for(std::chrono::milliseconds(45000));
	aliceEvent.get();

	maiaEvent.wait_for(std::chrono::milliseconds(45000));
	maiaEvent.get();

	testDone = true;

	CPCAPI2::ConferenceBridge::ConferenceBridgeManager::getInterface(maia.phone)->shutdown();
	CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(maia.phone)->flushAll();
	CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(maia.phone)->shutdown();
	maia.jsonApiServer->shutdown();

	max.authServer->shutdown();

	std::this_thread::sleep_for(std::chrono::milliseconds(1000));
}

TEST_F(ConferenceConnectorTests, DISABLED_LongLivedConferenceSoftDisconnect) // only working on Windows
{
   LoginResultEvent loginResult; loginResult.success = true;
   cpc::vector<cpc::string> permissions; permissions.push_back("*");

   // Max is the auth server
   TestAccount max("max", Account_Init);
   setupAuthServer(max);

   // Maia is the orchestration and confbridge server
   TestAccount maia("maia", Account_Init);
   setupConfBridgeServer(maia);

   // Alice is a client SDK (screenshare presenter)
   TestAccount alice("alice", Account_Init);

   // Bob is a client SDK (screenshare participant)
   TestAccount bob("bob", Account_Init);

   // enable H.264
   alice.video->queryCodecList();
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);
   
   bob.video->queryCodecList();
   bob.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);

   alice.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   bob.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   alice.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   bob.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

#if _WIN32
   HWND hwndAliceRemote = NULL;
   HWND hwndBobRemote = NULL;
   HWND hwndBobRemoteLarge = NULL;

   ASSERT_EQ(0, ViECreateWindow(hwndAliceRemote, 0, 0, 352, 288, "Alice (incoming)"));
   alice.video->setIncomingVideoRenderTarget(hwndAliceRemote, Media::VideoSurfaceType_WindowsHWND);

   ASSERT_EQ(0, ViECreateWindow(hwndBobRemote, 356, 0, 352, 288, "Bob (incoming)"));
   bob.video->setIncomingVideoRenderTarget(hwndBobRemote, Media::VideoSurfaceType_WindowsHWND);

   ASSERT_EQ(0, ViECreateWindow(hwndBobRemoteLarge, 0, 292, 704, 576, "Bob (incoming large)"));
#endif

   std::atomic_bool testDone(false);
   auto mediaEvent = std::async(std::launch::async, [&]() {
      while (!testDone) {
         // conf connector fires method calls it expects to be processed on app thread
         alice.mediaEvents->processNonUnitTestEvents(0);
         bob.mediaEvents->processNonUnitTestEvents(0);
         std::this_thread::sleep_for(std::chrono::milliseconds(20));
      }
   });

   ConferenceConnectorHandle aliceConfConn = alice.createConferenceConnector();
   ConferenceConnectorSettings aliceCloudSettings;
   aliceCloudSettings.authServerUrl = "https://127.0.0.1:18084";
   aliceCloudSettings.orchestrationServerUrl = "https://127.0.0.1:18082/jsonApi";
   aliceCloudSettings.regionCode = "NA";
   aliceCloudSettings.username = "user1";
   aliceCloudSettings.password = "1234";
   aliceCloudSettings.authServerApiKey = "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiKzxgDrW3u9+XzGN4r/GI5o+BMtty+63OT4lwEgFlkpedq5FY3eCzI4lB1WXDVip0OpCM1zYoCh6mqi7KaS1Vg==";
   aliceCloudSettings.ignoreCertVerification = true;
   alice.conferenceConnector->setConnectionSettings(aliceConfConn, aliceCloudSettings);
   alice.conferenceConnector->connectToConferenceService(aliceConfConn);

   CloudConferenceSessionSettings aliceSessionSettings;
   aliceSessionSettings.role = CloudConferenceRole_Host;

   CloudConferenceSessionMediaSettings aliceSessionMedia;
   aliceSessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
   aliceSessionMedia.videoDirection = ConferenceConnector::MediaDirection_SendRecv;
#if _WIN32
   aliceSessionMedia.remoteVideoRenderSurface = hwndAliceRemote;
   aliceSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
#endif

   CloudConferenceSettings confSettings;
   confSettings.conferenceDescription = "test";
   confSettings.conferenceType = CloudConferenceType_Screenshare;
   confSettings.conferenceId = "screenshare";
   confSettings.persistent = true;

   std::promise<std::string> joinUrlPr;
   std::future<std::string> joinUrlFu = joinUrlPr.get_future();
   auto maiaEvent = std::async(std::launch::async, [&]() {
      JsonApiUserHandle jsonApiUser = 0;
      NewLoginEvent args;

      // Maia has to process the login attempt (associate the context with an SDK instance)
      ASSERT_TRUE(cpcExpectEvent(maia.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 15000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args));
      ASSERT_NE(jsonApiUser, 0);
      maia.jsonApiServer->setJsonApiUserContext(jsonApiUser, maia.phone, permissions);
      maia.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);

      // Maia has to process the login attempt (associate the context with an SDK instance)
      ASSERT_TRUE(cpcExpectEvent(maia.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 15000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args));
      ASSERT_NE(jsonApiUser, 0);
      maia.jsonApiServer->setJsonApiUserContext(jsonApiUser, maia.phone, permissions);
      maia.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);
      
   });

   auto aliceEvent = std::async(std::launch::async, [&]() {

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
      }

      alice.conferenceConnector->queryConferenceList(aliceConfConn);

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 0);
      }

      alice.conferenceConnector->createNewConference(aliceConfConn, confSettings);
      CloudConferenceHandle aliceScreenShare = 0;

      {
         ConferenceConnectorHandle conn;
         ConferenceCreatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceCreated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_NE(args.conference, 0);
         ASSERT_NE(args.conference, -1);
         aliceScreenShare = args.conference;
      }

      alice.conferenceConnector->queryConferenceList(aliceConfConn);

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 1);
         ASSERT_FALSE(args.conferenceList[0].joinUrl.empty());
         joinUrlPr.set_value(args.conferenceList[0].joinUrl.c_str());
         ASSERT_GT(args.conferenceList[0].conference, 0); // handle used to create a new session
      }

      CloudConferenceSessionHandle aliceSession = alice.conferenceConnector->createConferenceSession(aliceScreenShare);

      alice.conferenceConnector->setSessionSettings(aliceSession, aliceSessionSettings);

      alice.conferenceConnector->setSessionMediaSettings(aliceSession, aliceSessionMedia);

      alice.conferenceConnector->startSession(aliceSession);

      try
      {
         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connecting);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(10000));

         alice.conferenceConnector->endSession(aliceSession);

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         alice.conferenceConnector->queryConferenceList(aliceConfConn);

         CloudConferenceHandle aliceScreenShare = 0;
         {
            ConferenceConnectorHandle conn;
            ConferenceListUpdatedEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(args.conferenceList.size(), 1);
            aliceScreenShare = args.conferenceList.begin()->conference;
         }

         aliceSession = alice.conferenceConnector->createConferenceSession(aliceScreenShare);
         alice.conferenceConnector->setSessionSettings(aliceSession, aliceSessionSettings);
         alice.conferenceConnector->setSessionMediaSettings(aliceSession, aliceSessionMedia);
         alice.conferenceConnector->startSession(aliceSession);

      
         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connecting);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(10000));

         alice.conferenceConnector->endSession(aliceSession);

         
         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         alice.conferenceConnector->destroyConferenceConnector(aliceConfConn);

      }
      catch (...)
      {
      }

   });

   std::shared_ptr<std::promise<int> > bobPr(new std::promise<int>());
   std::future<int> bobFu = bobPr->get_future();
   auto bobEvent = std::async(std::launch::async, [&, bobPr]() {
      ASSERT_EQ(joinUrlFu.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
      std::string joinUrlFromAlice = joinUrlFu.get();

      ConferenceConnectorHandle bobConfConn = bob.createConferenceConnector();
      ConferenceConnectorSettings bobCloudSettings;
      bobCloudSettings.authServerUrl = "https://127.0.0.1:18084";
      bobCloudSettings.orchestrationServerUrl = ""; // not needed, we use the joinUrl instead
      bobCloudSettings.joinUrl = joinUrlFromAlice.c_str();
      bobCloudSettings.regionCode = "NA";
      bobCloudSettings.username = "user2";
      bobCloudSettings.password = "1234";
      bobCloudSettings.authServerApiKey = "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiKzxgDrW3u9+XzGN4r/GI5o+BMtty+63OT4lwEgFlkpedq5FY3eCzI4lB1WXDVip0OpCM1zYoCh6mqi7KaS1Vg==";
      bobCloudSettings.ignoreCertVerification = true;
      bob.conferenceConnector->setConnectionSettings(bobConfConn, bobCloudSettings);
      bob.conferenceConnector->connectToConferenceService(bobConfConn);

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
      }

      bob.conferenceConnector->queryConferenceList(bobConfConn);
      CloudConferenceHandle bobScreenShare;

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 1);
         bobScreenShare = args.conferenceList[0].conference;
      }

      CloudConferenceSessionHandle bobSession = bob.conferenceConnector->createConferenceSession(bobScreenShare);

      CloudConferenceSessionSettings bobSessionSettings;
      bobSessionSettings.role = CloudConferenceRole_Participant;
      bob.conferenceConnector->setSessionSettings(bobSession, bobSessionSettings);

      CloudConferenceSessionMediaSettings bobSessionMedia;
      bobSessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
      bobSessionMedia.videoDirection = ConferenceConnector::MediaDirection_RecvOnly;
#if _WIN32
      bobSessionMedia.remoteVideoRenderSurface = hwndBobRemote;
      bobSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
#endif
      bob.conferenceConnector->setSessionMediaSettings(bobSession, bobSessionMedia);

      bob.conferenceConnector->startSession(bobSession);

      try
      {
         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connecting);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
            EXPECT_EQ(args.screenshare.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_RecvOnly);
            EXPECT_EQ(args.audio.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
            EXPECT_EQ(args.video.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
            //EXPECT_NE(args.screenshare.mediaStreamId, -1);
            //bob.video->setIncomingVideoRenderTarget(args.screenshare.mediaStreamId, hwndBobRemote);
         }
         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         bobSessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
         bobSessionMedia.videoDirection = ConferenceConnector::MediaDirection_RecvOnly;
#if _WIN32
         bobSessionMedia.remoteVideoRenderSurface = hwndBobRemoteLarge;
         bobSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
#endif
         bob.conferenceConnector->setSessionMediaSettings(bobSession, bobSessionMedia);
         bob.conferenceConnector->updateSessionMedia(bobSession);

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
            EXPECT_EQ(args.screenshare.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_RecvOnly);
            EXPECT_EQ(args.audio.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
            EXPECT_EQ(args.video.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
            //bob.video->setIncomingVideoRenderTarget(args.screenshare.mediaStreamId, hwndBobRemote);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(45000));

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceParticipantListUpdatedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceParticipantListUpdated", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            //EXPECT_EQ(args.participantList.size(), 0);
         }
      }
      catch (...)
      {
      }

      bob.conferenceConnector->destroyConferenceConnector(bobConfConn);

      //{
      //   // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
      //   ConferenceConnectorHandle conn;
      //   ConferenceSessionStatusChangedEvent args;
      //   ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
      //   ASSERT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
      //}
      bobPr->set_value(0);
   });

   std::this_thread::sleep_for(std::chrono::milliseconds(20000));

   aliceEvent.wait_for(std::chrono::milliseconds(60000));
   aliceEvent.get();

   bobEvent.wait_for(std::chrono::milliseconds(30000));
   bobEvent.get();
   //bobFu.get();

   maiaEvent.wait_for(std::chrono::milliseconds(45000));
   maiaEvent.get();

   //waitFor2(aliceEvent, maiaEvent);

   testDone = true;

#if _WIN32
   DestroyWindow(hwndAliceRemote);
   DestroyWindow(hwndBobRemote);
   DestroyWindow(hwndBobRemoteLarge);
#endif

   CPCAPI2::ConferenceBridge::ConferenceBridgeManager::getInterface(maia.phone)->shutdown();
   CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(maia.phone)->flushAll();
   CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(maia.phone)->shutdown();
   maia.jsonApiServer->shutdown();

   max.authServer->shutdown();

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
}

TEST_F(ConferenceConnectorTests, LongLivedConferenceHardDisconnect)
{
   LoginResultEvent loginResult; loginResult.success = true;
   cpc::vector<cpc::string> permissions; permissions.push_back("*");

   // Max is the auth server
   TestAccount max("max", Account_Init);
   setupAuthServer(max);

   // Maia is the orchestration and confbridge server
   TestAccount maia("maia", Account_Init);
   setupConfBridgeServer(maia);

   // Alice is a client SDK (screenshare presenter)
   TestAccount alice1("alice", Account_Init);
   TestAccount alice2("alice", Account_Init);

   // Bob is a client SDK (screenshare participant)
   TestAccount bob("bob", Account_Init);

   // enable H.264
   alice1.video->queryCodecList();
   alice1.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   alice1.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   alice1.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);
   
   alice2.video->queryCodecList();
   alice2.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   alice2.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   alice2.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);
   
   bob.video->queryCodecList();
   bob.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);

   alice1.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   alice2.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   bob.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   alice1.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   alice2.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   bob.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

#if _WIN32
   HWND hwndAlice1Remote = NULL;
   HWND hwndAlice2Remote = NULL;
   HWND hwndBobRemote = NULL;
   HWND hwndBobRemoteLarge = NULL;

   if (TestEnvironmentConfig::drawLocalVideo())
   {
      ASSERT_EQ(0, ViECreateWindow(hwndAlice1Remote, 0, 0, 352, 288, "Alice1 (incoming)"));
      ASSERT_EQ(0, ViECreateWindow(hwndAlice2Remote, 0, 292, 352, 288, "Alice2 (incoming)"));
      alice1.video->setIncomingVideoRenderTarget(hwndAlice1Remote, Media::VideoSurfaceType_WindowsHWND);
      alice2.video->setIncomingVideoRenderTarget(hwndAlice2Remote, Media::VideoSurfaceType_WindowsHWND);

      ASSERT_EQ(0, ViECreateWindow(hwndBobRemote, 356, 0, 352, 288, "Bob (incoming)"));
      bob.video->setIncomingVideoRenderTarget(hwndBobRemote, Media::VideoSurfaceType_WindowsHWND);

      ASSERT_EQ(0, ViECreateWindow(hwndBobRemoteLarge, 356, 292, 704, 576, "Bob (incoming large)"));
   }
#endif

   std::atomic_bool alice1Done(false);
   std::atomic_bool testDone(false);
   auto mediaEvent = std::async(std::launch::async, [&]() {
      while (!testDone) {
         // conf connector fires method calls it expects to be processed on app thread
         alice1.mediaEvents->processNonUnitTestEvents(0);
         alice2.mediaEvents->processNonUnitTestEvents(0);
         bob.mediaEvents->processNonUnitTestEvents(0);
         std::this_thread::sleep_for(std::chrono::milliseconds(20));
      }
safeCout("XXX2 EXIT MEDIA LOOP");
   });

   ConferenceConnectorSettings aliceCloudSettings;
   aliceCloudSettings.authServerUrl = "https://127.0.0.1:18084";
   aliceCloudSettings.orchestrationServerUrl = "https://127.0.0.1:18082/jsonApi";
   aliceCloudSettings.regionCode = "NA";
   aliceCloudSettings.username = "user1";
   aliceCloudSettings.password = "1234";
   aliceCloudSettings.authServerApiKey = "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiKzxgDrW3u9+XzGN4r/GI5o+BMtty+63OT4lwEgFlkpedq5FY3eCzI4lB1WXDVip0OpCM1zYoCh6mqi7KaS1Vg==";
   aliceCloudSettings.ignoreCertVerification = true;

   ConferenceConnectorHandle aliceConfConn = alice1.createConferenceConnector();
   alice1.conferenceConnector->setConnectionSettings(aliceConfConn, aliceCloudSettings);
   alice1.conferenceConnector->connectToConferenceService(aliceConfConn);

   CloudConferenceSessionSettings aliceSessionSettings;
   aliceSessionSettings.role = CloudConferenceRole_Host;

   CloudConferenceSessionMediaSettings alice1SessionMedia;
   alice1SessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
   alice1SessionMedia.videoDirection = ConferenceConnector::MediaDirection_SendRecv;
#if _WIN32
   alice1SessionMedia.remoteVideoRenderSurface = hwndAlice1Remote;
   alice1SessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
#endif

   CloudConferenceSessionMediaSettings alice2SessionMedia;
   alice2SessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
   alice2SessionMedia.videoDirection = ConferenceConnector::MediaDirection_SendRecv;
#if _WIN32
   alice2SessionMedia.remoteVideoRenderSurface = hwndAlice2Remote;
   alice2SessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
#endif

   CloudConferenceSettings confSettings;
   confSettings.conferenceDescription = "test";
   confSettings.conferenceType = CloudConferenceType_Screenshare;
   confSettings.conferenceId = "screenshare";
   confSettings.persistent = true;
   
   CloudConferenceSessionHandle alice2Session;
   
   std::promise<std::string> joinUrlPr;
   std::future<std::string> joinUrlFu = joinUrlPr.get_future();
   auto maiaEvent = std::async(std::launch::async, [&]() {
      JsonApiUserHandle jsonApiUser = 0;
      NewLoginEvent args;

      // Maia has to process the login attempt (associate the context with an SDK instance)
      ASSERT_TRUE(cpcExpectEvent(maia.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 15000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args));
      ASSERT_NE(jsonApiUser, 0);
      maia.jsonApiServer->setJsonApiUserContext(jsonApiUser, maia.phone, permissions);
      maia.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);

      // Maia has to process the login attempt (associate the context with an SDK instance)
      ASSERT_TRUE(cpcExpectEvent(maia.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 15000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args));
      ASSERT_NE(jsonApiUser, 0);
      maia.jsonApiServer->setJsonApiUserContext(jsonApiUser, maia.phone, permissions);
      maia.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);

      // there's a much bigger delay here as this is Alice's second attempt after a delay
      
      // Maia has to process the login attempt (associate the context with an SDK instance)
      ASSERT_TRUE(cpcExpectEvent(maia.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 45000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args));
      ASSERT_NE(jsonApiUser, 0);
      maia.jsonApiServer->setJsonApiUserContext(jsonApiUser, maia.phone, permissions);
      maia.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);

   });

   auto alice1Event = std::async(std::launch::async, [&]() {

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice1.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice1.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice1.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
      }

      alice1.conferenceConnector->queryConferenceList(aliceConfConn);

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice1.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 0);
      }

      alice1.conferenceConnector->createNewConference(aliceConfConn, confSettings);
      CloudConferenceHandle aliceScreenShare = 0;

      {
         ConferenceConnectorHandle conn;
         ConferenceCreatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice1.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceCreated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_NE(args.conference, 0);
         ASSERT_NE(args.conference, -1);
         aliceScreenShare = args.conference;
      }

      alice1.conferenceConnector->queryConferenceList(aliceConfConn);

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice1.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 1);
         ASSERT_FALSE(args.conferenceList[0].joinUrl.empty());
         joinUrlPr.set_value(args.conferenceList[0].joinUrl.c_str());
         ASSERT_GT(args.conferenceList[0].conference, 0); // handle used to create a new session
      }

      CloudConferenceSessionHandle aliceSession = alice1.conferenceConnector->createConferenceSession(aliceScreenShare);

      alice1.conferenceConnector->setSessionSettings(aliceSession, aliceSessionSettings);

      alice1.conferenceConnector->setSessionMediaSettings(aliceSession, alice1SessionMedia);

      alice1.conferenceConnector->startSession(aliceSession);

      try
      {
         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice1.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connecting);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice1.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(10000));

         alice1.conferenceConnector->endSession(aliceSession);

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice1.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         alice1.conferenceConnector->disconnectFromConferenceService(aliceConfConn);

         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice1.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Disconnecting, args.connectionStatus);
         }

         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice1.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Disconnected, args.connectionStatus);
         }
 

         alice1.conferenceConnector->destroyConferenceConnector(aliceConfConn);


         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         
         aliceConfConn = alice2.createConferenceConnector();
         alice2.conferenceConnector->setConnectionSettings(aliceConfConn, aliceCloudSettings);
         alice2.conferenceConnector->connectToConferenceService(aliceConfConn);
         
         std::this_thread::sleep_for(std::chrono::milliseconds(3000));

         alice1Done = true;

      }
      catch (...)
      {
      }

   });

   auto alice2Event = std::async(std::launch::async, [&]() {
      
      while (!alice1Done)
      {
         std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      }
      
      
      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice2.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice2.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice2.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
      }

      alice2.conferenceConnector->queryConferenceList(aliceConfConn);

      CloudConferenceHandle aliceScreenShare = 0;
      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice2.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 1);
         ASSERT_FALSE(args.conferenceList[0].joinUrl.empty());
         ASSERT_GT(args.conferenceList[0].conference, 0); // handle used to create a new session
         aliceScreenShare = args.conferenceList[0].conference;
      }

      CloudConferenceSessionHandle aliceSession = alice2.conferenceConnector->createConferenceSession(aliceScreenShare);

      alice2.conferenceConnector->setSessionSettings(aliceSession, aliceSessionSettings);

      alice2.conferenceConnector->setSessionMediaSettings(aliceSession, alice2SessionMedia);

      alice2.conferenceConnector->startSession(aliceSession);

      try
      {
         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice2.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connecting);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice2.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(20000));

         alice2.conferenceConnector->endSession(aliceSession);

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice2.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         alice2.conferenceConnector->disconnectFromConferenceService(aliceConfConn);

         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice2.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Disconnecting, args.connectionStatus);
         }

        {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice2.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Disconnected, args.connectionStatus);
         }
 
// DRL I think we should have this to clean up the long lived conference but it isn't on the API?
//      alice2.conferenceConnector->destroyConference(aliceConfConn);

      alice2.conferenceConnector->destroyConferenceConnector(aliceConfConn);

      }
      catch (...)
      {
      }

   });

   std::shared_ptr<std::promise<int> > bobPr(new std::promise<int>());
   std::future<int> bobFu = bobPr->get_future();
   auto bobEvent = std::async(std::launch::async, [&, bobPr]() {
      ASSERT_EQ(joinUrlFu.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
      std::string joinUrlFromAlice = joinUrlFu.get();

      ConferenceConnectorHandle bobConfConn = bob.createConferenceConnector();
      ConferenceConnectorSettings bobCloudSettings;
      bobCloudSettings.authServerUrl = "https://127.0.0.1:18084";
      bobCloudSettings.orchestrationServerUrl = ""; // not needed, we use the joinUrl instead
      bobCloudSettings.joinUrl = joinUrlFromAlice.c_str();
      bobCloudSettings.regionCode = "NA";
      bobCloudSettings.username = "user2";
      bobCloudSettings.password = "1234";
      bobCloudSettings.authServerApiKey = "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiKzxgDrW3u9+XzGN4r/GI5o+BMtty+63OT4lwEgFlkpedq5FY3eCzI4lB1WXDVip0OpCM1zYoCh6mqi7KaS1Vg==";
      bobCloudSettings.ignoreCertVerification = true;
      bob.conferenceConnector->setConnectionSettings(bobConfConn, bobCloudSettings);
      bob.conferenceConnector->connectToConferenceService(bobConfConn);

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
      }

      bob.conferenceConnector->queryConferenceList(bobConfConn);
      CloudConferenceHandle bobScreenShare;

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 1);
         bobScreenShare = args.conferenceList[0].conference;
      }

      CloudConferenceSessionHandle bobSession = bob.conferenceConnector->createConferenceSession(bobScreenShare);

      CloudConferenceSessionSettings bobSessionSettings;
      bobSessionSettings.role = CloudConferenceRole_Participant;
      bob.conferenceConnector->setSessionSettings(bobSession, bobSessionSettings);

      CloudConferenceSessionMediaSettings bobSessionMedia;
      bobSessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
      bobSessionMedia.videoDirection = ConferenceConnector::MediaDirection_RecvOnly;
#if _WIN32
      bobSessionMedia.remoteVideoRenderSurface = hwndBobRemote;
      bobSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
#endif
      bob.conferenceConnector->setSessionMediaSettings(bobSession, bobSessionMedia);

      bob.conferenceConnector->startSession(bobSession);

      try
      {
         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connecting);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
            EXPECT_EQ(args.screenshare.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_RecvOnly);
            EXPECT_EQ(args.audio.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
            EXPECT_EQ(args.video.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
            //EXPECT_NE(args.screenshare.mediaStreamId, -1);
            //bob.video->setIncomingVideoRenderTarget(args.screenshare.mediaStreamId, hwndBobRemote);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         bobSessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
         bobSessionMedia.videoDirection = ConferenceConnector::MediaDirection_RecvOnly;
#if _WIN32
         bobSessionMedia.remoteVideoRenderSurface = hwndBobRemoteLarge;
         bobSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
#endif
         bob.conferenceConnector->setSessionMediaSettings(bobSession, bobSessionMedia);
         bob.conferenceConnector->updateSessionMedia(bobSession);

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
            EXPECT_EQ(args.screenshare.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_RecvOnly);
            EXPECT_EQ(args.audio.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
            EXPECT_EQ(args.video.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
            //bob.video->setIncomingVideoRenderTarget(args.screenshare.mediaStreamId, hwndBobRemote);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceParticipantListUpdatedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceParticipantListUpdated", 20000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.participantList.size(), 2);
         }
         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceParticipantListUpdatedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceParticipantListUpdated", 20000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.participantList.size(), 2);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceParticipantListUpdatedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceParticipantListUpdated", 20000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.participantList.size(), 1);
         }
         
         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceParticipantListUpdatedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceParticipantListUpdated", 20000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.participantList.size(), 2);
         }
         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceParticipantListUpdatedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceParticipantListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.participantList.size(), 2);
         }


         //std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceParticipantListUpdatedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceParticipantListUpdated", 45000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.participantList.size(), 1);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         bob.conferenceConnector->endSession(bobSession);
      }
      catch (...)
      {
      }
      
      bob.conferenceConnector->destroyConferenceConnector(bobConfConn);

      //{
      //   // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
      //   ConferenceConnectorHandle conn;
      //   ConferenceSessionStatusChangedEvent args;
      //   ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
      //   ASSERT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
      //}
      bobPr->set_value(0);
   });

   alice1Event.wait_for(std::chrono::milliseconds(60000));
   alice1Event.get();

   alice2Event.wait_for(std::chrono::milliseconds(60000));
   alice2Event.get();

   bobEvent.wait_for(std::chrono::milliseconds(30000));
   bobEvent.get();
   //bobFu.get();

   maiaEvent.wait_for(std::chrono::milliseconds(45000));
   maiaEvent.get();

   //waitFor2(aliceEvent, maiaEvent);

   testDone = true;

#if _WIN32
   DestroyWindow(hwndAlice1Remote);
   DestroyWindow(hwndAlice2Remote);
   DestroyWindow(hwndBobRemote);
   DestroyWindow(hwndBobRemoteLarge);
#endif

   CPCAPI2::ConferenceBridge::ConferenceBridgeManager::getInterface(maia.phone)->shutdown();
   CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(maia.phone)->flushAll();
   CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(maia.phone)->shutdown();
   maia.jsonApiServer->shutdown();

   max.authServer->shutdown();

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
}

class VideoStreamCallbackCounter
{
public:
   VideoStreamCallbackCounter() : mCnt(0) {}
   ~VideoStreamCallbackCounter() {}

   void incr()
   {
      mCnt++;
   }

   int val()
   {
      return mCnt;
   }

private:
   std::atomic_int mCnt;
};

#if _WIN32
TEST_F(ConferenceConnectorTests, WebStreamingTest)
{
   LoginResultEvent loginResult; loginResult.success = true;
   cpc::vector<cpc::string> permissions; permissions.push_back("*");

   // Max is the auth server
   TestAccount max("max", Account_Init);
   setupAuthServer(max);

   // Maia is the orchestration and confbridge server
   TestAccount maia("maia", Account_Init);
   setupConfBridgeServer(maia);

   // Alice is a client SDK (screenshare presenter)
   TestAccount alice("alice", Account_Init);

   // Bob is a client SDK (screenshare participant)
   TestAccount bob("bob", Account_Init);

   // enable H.264
   alice.video->queryCodecList();
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);

   bob.video->queryCodecList();
   bob.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);

   alice.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   bob.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   alice.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   bob.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   HWND hwndAliceRemote = NULL;
   HWND hwndBobRemote = NULL;
   HWND hwndBobRemoteLarge = NULL;

   if (TestEnvironmentConfig::drawLocalVideo())
   {
      ASSERT_EQ(0, ViECreateWindow(hwndAliceRemote, 0, 0, 352, 288, "Alice (incoming)"));
      alice.video->setIncomingVideoRenderTarget(hwndAliceRemote, Media::VideoSurfaceType_WindowsHWND);

      ASSERT_EQ(0, ViECreateWindow(hwndBobRemote, 356, 0, 352, 288, "Bob (incoming)"));
      bob.video->setIncomingVideoRenderTarget(hwndBobRemote, Media::VideoSurfaceType_WindowsHWND);

      ASSERT_EQ(0, ViECreateWindow(hwndBobRemoteLarge, 0, 292, 704, 576, "Bob (incoming large)"));
   }

   CPCAPI2::VideoStreaming::VideoStreamingManager* bobVSM = CPCAPI2::VideoStreaming::VideoStreamingManager::getInterface(bob.phone);
   VideoStreaming::VideoStreamingServerConfig streamingServerConfig;
   streamingServerConfig.serverType = VideoStreaming::ServerType_CallbackFunction;
   bobVSM->startVideoStreamingServer(streamingServerConfig);

   std::atomic_bool testDone(false);
   auto mediaEvent = std::async(std::launch::async, [&]() {
      while (!testDone) {
         // conf connector fires method calls it expects to be processed on app thread
         alice.mediaEvents->processNonUnitTestEvents(0);
         bob.mediaEvents->processNonUnitTestEvents(0);
         std::this_thread::sleep_for(std::chrono::milliseconds(20));
      }
   });

   ConferenceConnectorHandle aliceConfConn = alice.createConferenceConnector();
   ConferenceConnectorSettings aliceCloudSettings;
   aliceCloudSettings.authServerUrl = "https://127.0.0.1:18084";
   aliceCloudSettings.orchestrationServerUrl = "https://127.0.0.1:18082/jsonApi";
   aliceCloudSettings.regionCode = "NA";
   aliceCloudSettings.username = "user1";
   aliceCloudSettings.password = "1234";
   aliceCloudSettings.ignoreCertVerification = true;
   aliceCloudSettings.authServerApiKey = "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiKzxgDrW3u9+XzGN4r/GI5o+BMtty+63OT4lwEgFlkpedq5FY3eCzI4lB1WXDVip0OpCM1zYoCh6mqi7KaS1Vg==";
   alice.conferenceConnector->setConnectionSettings(aliceConfConn, aliceCloudSettings);
   alice.conferenceConnector->connectToConferenceService(aliceConfConn);

   std::promise<std::string> joinUrlPr;
   std::future<std::string> joinUrlFu = joinUrlPr.get_future();
   auto maiaEvent = std::async(std::launch::async, [&]() {
      JsonApiUserHandle jsonApiUser = 0;
      NewLoginEvent args;

      // Maia has to process the login attempt (associate the context with an SDK instance)
      ASSERT_TRUE(cpcExpectEvent(maia.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 15000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args));
      ASSERT_NE(jsonApiUser, 0);
      maia.jsonApiServer->setJsonApiUserContext(jsonApiUser, maia.phone, permissions);
      maia.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);

      // Maia has to process the login attempt (associate the context with an SDK instance)
      ASSERT_TRUE(cpcExpectEvent(maia.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 15000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args));
      ASSERT_NE(jsonApiUser, 0);
      maia.jsonApiServer->setJsonApiUserContext(jsonApiUser, maia.phone, permissions);
      maia.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);

   });

   auto aliceEvent = std::async(std::launch::async, [&]() {
      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
      }

      alice.conferenceConnector->queryConferenceList(aliceConfConn);

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 0);
      }

      CloudConferenceSettings confSettings;
      confSettings.conferenceDescription = "test";
      confSettings.conferenceType = CloudConferenceType_Screenshare;
      confSettings.conferenceId = "screenshare";
      alice.conferenceConnector->createNewConference(aliceConfConn, confSettings);
      CloudConferenceHandle aliceScreenShare = 0;

      {
         ConferenceConnectorHandle conn;
         ConferenceCreatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceCreated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_NE(args.conference, 0);
         ASSERT_NE(args.conference, -1);
         aliceScreenShare = args.conference;
      }

      alice.conferenceConnector->queryConferenceList(aliceConfConn);

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 1);
         ASSERT_FALSE(args.conferenceList[0].joinUrl.empty());
         joinUrlPr.set_value(args.conferenceList[0].joinUrl.c_str());
         ASSERT_GT(args.conferenceList[0].conference, 0); // handle used to create a new session
      }

      CloudConferenceSessionHandle aliceSession = alice.conferenceConnector->createConferenceSession(aliceScreenShare);

      CloudConferenceSessionSettings aliceSessionSettings;
      aliceSessionSettings.role = CloudConferenceRole_Host;
      alice.conferenceConnector->setSessionSettings(aliceSession, aliceSessionSettings);

      CloudConferenceSessionMediaSettings aliceSessionMedia;
      aliceSessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
      aliceSessionMedia.videoDirection = ConferenceConnector::MediaDirection_SendRecv;
      aliceSessionMedia.remoteVideoRenderSurface = hwndAliceRemote;
      aliceSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
      alice.conferenceConnector->setSessionMediaSettings(aliceSession, aliceSessionMedia);

      alice.conferenceConnector->startSession(aliceSession);

      try
      {
         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connecting);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(20000));

         safeCout("ALICE ENDS SESSION");
         alice.conferenceConnector->endSession(aliceSession);

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
         }

         {
            ConferenceConnectorHandle conn;
            ConferenceEndedEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceEnded", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(args.conference, aliceScreenShare);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         alice.conferenceConnector->destroyConferenceConnector(aliceConfConn);

      }
      catch (...)
      {
      }

   });

   std::shared_ptr<std::promise<int> > bobPr(new std::promise<int>());
   std::future<int> bobFu = bobPr->get_future();
   std::unique_ptr<VideoStreamCallbackCounter> cbc(new VideoStreamCallbackCounter());

   auto bobEvent = std::async(std::launch::async, [&, bobPr]() {
      ASSERT_EQ(joinUrlFu.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
      std::string joinUrlFromAlice = joinUrlFu.get();

      ConferenceConnectorHandle bobConfConn = bob.createConferenceConnector();
      ConferenceConnectorSettings bobCloudSettings;
      bobCloudSettings.authServerUrl = "https://127.0.0.1:18084";
      bobCloudSettings.orchestrationServerUrl = ""; // not needed, we use the joinUrl instead
      bobCloudSettings.joinUrl = joinUrlFromAlice.c_str();
      bobCloudSettings.regionCode = "NA";
      bobCloudSettings.username = "user2";
      bobCloudSettings.password = "1234";
      bobCloudSettings.authServerApiKey = "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiKzxgDrW3u9+XzGN4r/GI5o+BMtty+63OT4lwEgFlkpedq5FY3eCzI4lB1WXDVip0OpCM1zYoCh6mqi7KaS1Vg==";
      bobCloudSettings.ignoreCertVerification = true;
      bob.conferenceConnector->setConnectionSettings(bobConfConn, bobCloudSettings);
      bob.conferenceConnector->connectToConferenceService(bobConfConn);

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
      }

      bob.conferenceConnector->queryConferenceList(bobConfConn);
      CloudConferenceHandle bobScreenShare;

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 1);
         bobScreenShare = args.conferenceList[0].conference;
      }

      CloudConferenceSessionHandle bobSession = bob.conferenceConnector->createConferenceSession(bobScreenShare);

      CloudConferenceSessionSettings bobSessionSettings;
      bobSessionSettings.role = CloudConferenceRole_Participant;
      bob.conferenceConnector->setSessionSettings(bobSession, bobSessionSettings);

      CloudConferenceSessionMediaSettings bobSessionMedia;
      bobSessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
      bobSessionMedia.videoDirection = ConferenceConnector::MediaDirection_RecvOnly;
      bobSessionMedia.remoteVideoRenderSurface = hwndBobRemote;
      bobSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
      bob.conferenceConnector->setSessionMediaSettings(bobSession, bobSessionMedia);

      bob.conferenceConnector->startSession(bobSession);

      try
      {
         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connecting);
         }

         CPCAPI2::VideoStreaming::VideoStreamHandle bobVidStrmHdl = 0;
         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
            EXPECT_EQ(args.screenshare.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_RecvOnly);
            EXPECT_EQ(args.audio.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
            EXPECT_EQ(args.video.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
            bobVidStrmHdl = (CPCAPI2::VideoStreaming::VideoStreamHandle)args.screenshare.mediaStreamId;
            bobVSM->addVideoStreamCallback(bobVidStrmHdl, cbc.get(), [](void* cbState, unsigned int videoStreamHandle, int width, int height, const unsigned char* ybuffer, unsigned int ystride, const unsigned char* ubuffer, unsigned int ustride, const unsigned char* vbuffer, unsigned int vstride) {
               VideoStreamCallbackCounter* cbcIn = (VideoStreamCallbackCounter*)cbState;
               cbcIn->incr();
            });

            //EXPECT_NE(args.screenshare.mediaStreamId, -1);
            //bob.video->setIncomingVideoRenderTarget(args.screenshare.mediaStreamId, hwndBobRemote);
         }
         std::this_thread::sleep_for(std::chrono::milliseconds(5000));
         EXPECT_NE(cbc->val(), 0);
         bobVSM->removeVideoStreamCallback(bobVidStrmHdl);


         std::this_thread::sleep_for(std::chrono::milliseconds(1000));

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceParticipantListUpdatedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceParticipantListUpdated", 180000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            //EXPECT_EQ(args.participantList.size(), 0);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 700000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(CPCAPI2::ConferenceConnector::SessionStatus_NotConnected, args.sessionStatus);
         }

         {
            ConferenceConnectorHandle conn;
            ConferenceEndedEvent args;
            ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceEnded", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(args.conference, bobScreenShare);
         }

         /*
         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceParticipantListUpdatedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceParticipantListUpdated", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            //EXPECT_EQ(args.participantList.size(), 0);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 120000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         bob.conferenceConnector->endSession(bobSession);
         */
      }
      catch (...)
      {
      }
      bob.conferenceConnector->destroyConferenceConnector(bobConfConn);

      //{
      //   // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
      //   ConferenceConnectorHandle conn;
      //   ConferenceSessionStatusChangedEvent args;
      //   ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
      //   ASSERT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
      //}
      bobPr->set_value(0);
   });

   aliceEvent.wait_for(std::chrono::milliseconds(45000));
   aliceEvent.get();

   bobEvent.wait_for(std::chrono::milliseconds(45000));
   bobEvent.get();
   //bobFu.get();

   maiaEvent.wait_for(std::chrono::milliseconds(45000));
   maiaEvent.get();

   //waitFor2(aliceEvent, maiaEvent);

   testDone = true;

   DestroyWindow(hwndAliceRemote);
   DestroyWindow(hwndBobRemote);
   //DestroyWindow(hwndBobRemoteLarge);

   CPCAPI2::ConferenceBridge::ConferenceBridgeManager::getInterface(maia.phone)->shutdown();
   CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(maia.phone)->flushAll();
   CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(maia.phone)->shutdown();
   maia.jsonApiServer->shutdown();

   max.authServer->shutdown();

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
}
#endif // WIN32

TEST_F(ConferenceConnectorTests, AVConference_NPE)
{
   LoginResultEvent loginResult; loginResult.success = true;
   cpc::vector<cpc::string> permissions; permissions.push_back("*");

   // Max is the auth server
   TestAccount max("max", Account_Init);
   setupAuthServer(max);

   // Maia is the orchestration and confbridge server
   TestAccount maia("maia", Account_Init);
   setupConfBridgeServer(maia);

   // Alice is a client SDK (screenshare presenter)
   TestAccount alice("alice", Account_Init);

   // Bob is a client SDK (screenshare participant)
   TestAccount bob("bob", Account_Init);

   // enable H.264
   alice.video->queryCodecList();
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);
   
   bob.video->queryCodecList();
   bob.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);

   alice.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   bob.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   alice.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   bob.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   HWND hwndAliceRemote = NULL;
   HWND hwndBobRemote = NULL;
   HWND hwndBobRemoteLarge = NULL;
#if _WIN32
   ASSERT_EQ(0, ViECreateWindow(hwndAliceRemote, 0, 0, 352, 288, "Alice (incoming)"));
   //alice.video->setIncomingVideoRenderTarget(hwndAliceRemote);
   alice.video->startCapture();

   ASSERT_EQ(0, ViECreateWindow(hwndBobRemote, 356, 0, 352, 288, "Bob (incoming)"));
   //bob.video->setIncomingVideoRenderTarget(hwndBobRemote);
   bob.video->startCapture();

   ASSERT_EQ(0, ViECreateWindow(hwndBobRemoteLarge, 0, 292, 704, 576, "Bob (incoming large)"));

#else // _WIN32
   // CPCAPI2::Media::VideoExt::getInterface(alice.media)->startScreenshare();
#endif

   std::atomic_bool testDone(false);
   auto mediaEvent = std::async(std::launch::async, [&]() {
      while (!testDone) {
         // conf connector fires method calls it expects to be processed on app thread
         alice.mediaEvents->processNonUnitTestEvents(0);
         bob.mediaEvents->processNonUnitTestEvents(0);
         std::this_thread::sleep_for(std::chrono::milliseconds(20));
      }
   });

   ConferenceConnectorHandle aliceConfConn = alice.createConferenceConnector();
   ConferenceConnectorSettings aliceCloudSettings;
   aliceCloudSettings.authServerUrl = "https://127.0.0.1:18084";
   aliceCloudSettings.orchestrationServerUrl = "https://127.0.0.1:18082/jsonApi";
   aliceCloudSettings.regionCode = "NA";
   aliceCloudSettings.username = "user1";
   aliceCloudSettings.password = "1234";
   aliceCloudSettings.ignoreCertVerification = true;
   aliceCloudSettings.authServerApiKey = "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiKzxgDrW3u9+XzGN4r/GI5o+BMtty+63OT4lwEgFlkpedq5FY3eCzI4lB1WXDVip0OpCM1zYoCh6mqi7KaS1Vg==";
   alice.conferenceConnector->setConnectionSettings(aliceConfConn, aliceCloudSettings);
   alice.conferenceConnector->connectToConferenceService(aliceConfConn);

   std::promise<std::string> joinUrlPr;
   std::future<std::string> joinUrlFu = joinUrlPr.get_future();
   auto maiaEvent = std::async(std::launch::async, [&]() {
      JsonApiUserHandle jsonApiUser = 0;
      NewLoginEvent args;

      // Maia has to process the login attempt (associate the context with an SDK instance)
      ASSERT_TRUE(cpcExpectEvent(maia.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 15000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args));
      ASSERT_NE(jsonApiUser, 0);
      maia.jsonApiServer->setJsonApiUserContext(jsonApiUser, maia.phone, permissions);
      maia.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);

      // Maia has to process the login attempt (associate the context with an SDK instance)
      ASSERT_TRUE(cpcExpectEvent(maia.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 15000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args));
      ASSERT_NE(jsonApiUser, 0);
      maia.jsonApiServer->setJsonApiUserContext(jsonApiUser, maia.phone, permissions);
      maia.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);

   });

   auto aliceEvent = std::async(std::launch::async, [&]() {
      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
      }

      alice.conferenceConnector->queryConferenceList(aliceConfConn);

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 0);
      }

      CloudConferenceSettings confSettings;
      confSettings.conferenceDescription = "mcu test";
      confSettings.conferenceType = CloudConferenceType_AudioVideo_MCU;
      confSettings.conferenceId = "mcu";
      alice.conferenceConnector->createNewConference(aliceConfConn, confSettings);
      CloudConferenceHandle aliceScreenShare = 0;

      {
         ConferenceConnectorHandle conn;
         ConferenceCreatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceCreated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_NE(args.conference, 0);
         ASSERT_NE(args.conference, -1);
         aliceScreenShare = args.conference;
      }

      alice.conferenceConnector->queryConferenceList(aliceConfConn);

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 1);
         ASSERT_FALSE(args.conferenceList[0].joinUrl.empty());
         joinUrlPr.set_value(args.conferenceList[0].joinUrl.c_str());
         ASSERT_GT(args.conferenceList[0].conference, 0); // handle used to create a new session
      }

      CloudConferenceSessionHandle aliceSession = alice.conferenceConnector->createConferenceSession(aliceScreenShare);

      CloudConferenceSessionSettings aliceSessionSettings;
      aliceSessionSettings.role = CloudConferenceRole_Host;
      alice.conferenceConnector->setSessionSettings(aliceSession, aliceSessionSettings);

      CloudConferenceSessionMediaSettings aliceSessionMedia;
      aliceSessionMedia.audioDirection = ConferenceConnector::MediaDirection_SendRecv;
      aliceSessionMedia.videoDirection = ConferenceConnector::MediaDirection_SendRecv;
      aliceSessionMedia.remoteVideoRenderSurface = hwndAliceRemote;
      aliceSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
      alice.conferenceConnector->setSessionMediaSettings(aliceSession, aliceSessionMedia);

      alice.conferenceConnector->startSession(aliceSession);

      try
      {
         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connecting);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(20000));

         safeCout("ALICE ENDS SESSION");
         alice.conferenceConnector->endSession(aliceSession);

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
         }
         //{
         //   // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
         //   ConferenceConnectorHandle conn;
         //   ConferenceSessionStatusChangedEvent args;
         //   EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         //   EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
         //}

         {
            ConferenceConnectorHandle conn;
            ConferenceEndedEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceEnded", 10000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(args.conference, aliceScreenShare);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         alice.conferenceConnector->destroyConferenceConnector(aliceConfConn);



      }
      catch (...)
      {
      }

   });

   std::shared_ptr<std::promise<int> > bobPr(new std::promise<int>());
   std::future<int> bobFu = bobPr->get_future();
   auto bobEvent = std::async(std::launch::async, [&, bobPr]() {
      ASSERT_EQ(joinUrlFu.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
      std::string joinUrlFromAlice = joinUrlFu.get();

      ConferenceConnectorHandle bobConfConn = bob.createConferenceConnector();
      ConferenceConnectorSettings bobCloudSettings;
      bobCloudSettings.authServerUrl = "https://127.0.0.1:18084";
      bobCloudSettings.orchestrationServerUrl = ""; // not needed, we use the joinUrl instead
      bobCloudSettings.joinUrl = joinUrlFromAlice.c_str();
      bobCloudSettings.regionCode = "NA";
      bobCloudSettings.username = "user2";
      bobCloudSettings.password = "1234";
      bobCloudSettings.authServerApiKey = "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiKzxgDrW3u9+XzGN4r/GI5o+BMtty+63OT4lwEgFlkpedq5FY3eCzI4lB1WXDVip0OpCM1zYoCh6mqi7KaS1Vg==";
      bobCloudSettings.ignoreCertVerification = true;
      bob.conferenceConnector->setConnectionSettings(bobConfConn, bobCloudSettings);
      bob.conferenceConnector->connectToConferenceService(bobConfConn);

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
      }

      bob.conferenceConnector->queryConferenceList(bobConfConn);
      CloudConferenceHandle bobScreenShare;

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 1);
         bobScreenShare = args.conferenceList[0].conference;
      }

      CloudConferenceSessionHandle bobSession = bob.conferenceConnector->createConferenceSession(bobScreenShare);

      CloudConferenceSessionSettings bobSessionSettings;
      bobSessionSettings.role = CloudConferenceRole_Participant;
      bob.conferenceConnector->setSessionSettings(bobSession, bobSessionSettings);

      CloudConferenceSessionMediaSettings bobSessionMedia;
      bobSessionMedia.audioDirection = ConferenceConnector::MediaDirection_SendRecv;
      bobSessionMedia.videoDirection = ConferenceConnector::MediaDirection_SendRecv;
      bobSessionMedia.remoteVideoRenderSurface = hwndBobRemote;
      bobSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
      bob.conferenceConnector->setSessionMediaSettings(bobSession, bobSessionMedia);

      bob.conferenceConnector->startSession(bobSession);

      try
      {
         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connecting);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
            EXPECT_EQ(args.screenshare.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
            EXPECT_EQ(args.audio.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_SendRecv);
            EXPECT_EQ(args.video.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_SendRecv);
            //EXPECT_NE(args.screenshare.mediaStreamId, -1);
            //bob.video->setIncomingVideoRenderTarget(args.screenshare.mediaStreamId, hwndBobRemote);
         }
         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         bobSessionMedia.audioDirection = ConferenceConnector::MediaDirection_SendRecv;
         bobSessionMedia.videoDirection = ConferenceConnector::MediaDirection_SendRecv;
         bobSessionMedia.remoteVideoRenderSurface = hwndBobRemoteLarge;
         bobSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
         bob.conferenceConnector->setSessionMediaSettings(bobSession, bobSessionMedia);
         bob.conferenceConnector->updateSessionMedia(bobSession);

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
            EXPECT_EQ(args.screenshare.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
            EXPECT_EQ(args.audio.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_SendRecv);
            EXPECT_EQ(args.video.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_SendRecv);
            //bob.video->setIncomingVideoRenderTarget(args.screenshare.mediaStreamId, hwndBobRemote);
         }

         //std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceParticipantListUpdatedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceParticipantListUpdated", 180000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            //EXPECT_EQ(args.participantList.size(), 0);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 700000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(CPCAPI2::ConferenceConnector::SessionStatus_NotConnected, args.sessionStatus);
         }

         {
            ConferenceConnectorHandle conn;
            ConferenceEndedEvent args;
            ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceEnded", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(args.conference, bobScreenShare);
         }

         /*
         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceParticipantListUpdatedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceParticipantListUpdated", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            //EXPECT_EQ(args.participantList.size(), 0);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 120000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         bob.conferenceConnector->endSession(bobSession);
         */
      }
      catch (...)
      {
      }
      //{
      //   // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
      //   ConferenceConnectorHandle conn;
      //   ConferenceSessionStatusChangedEvent args;
      //   ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
      //   ASSERT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
      //}
      bobPr->set_value(0);
   });

   aliceEvent.wait_for(std::chrono::milliseconds(45000));
   aliceEvent.get();

   bobEvent.wait_for(std::chrono::milliseconds(45000));
   bobEvent.get();
   //bobFu.get();

   maiaEvent.wait_for(std::chrono::milliseconds(45000));
   maiaEvent.get();
   //waitFor2(aliceEvent, maiaEvent);

   testDone = true;

#if _WIN32
   DestroyWindow(hwndAliceRemote);
   DestroyWindow(hwndBobRemote);
   DestroyWindow(hwndBobRemoteLarge);
#endif // _WIN32

   CPCAPI2::ConferenceBridge::ConferenceBridgeManager::getInterface(maia.phone)->shutdown();
   CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(maia.phone)->flushAll();
   CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(maia.phone)->shutdown();
   maia.jsonApiServer->shutdown();

   max.authServer->shutdown();

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
}


TEST_F(ConferenceConnectorTests, ConnectTimeout)
{
   // Max is the auth server
   TestAccount max("max", Account_Init);
   setupAuthServer(max);

   // Bob is a client SDK (screenshare participant)
   TestAccount bob("bob", Account_Init);
   std::shared_ptr<std::promise<int> > bobPr(new std::promise<int>());
   std::future<int> bobFu = bobPr->get_future();
   auto bobEvent = std::async(std::launch::async, [&, bobPr]() {

      ConferenceConnectorHandle bobConfConn = bob.createConferenceConnector();
      ConferenceConnectorSettings bobCloudSettings;
      bobCloudSettings.authServerUrl = "https://127.0.0.1:18084";
      bobCloudSettings.orchestrationServerUrl = ""; // not needed, we use the joinUrl instead
      bobCloudSettings.joinUrl = "http://example.com:81"; // service that is going to timeout
      bobCloudSettings.regionCode = "NA";
      bobCloudSettings.username = "user2";
      bobCloudSettings.password = "1234";
      bobCloudSettings.authServerApiKey = "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiKzxgDrW3u9+XzGN4r/GI5o+BMtty+63OT4lwEgFlkpedq5FY3eCzI4lB1WXDVip0OpCM1zYoCh6mqi7KaS1Vg==";
      bobCloudSettings.ignoreCertVerification = true;
      bob.conferenceConnector->setConnectionSettings(bobConfConn, bobCloudSettings);
      bob.conferenceConnector->connectToConferenceService(bobConfConn);
      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 60000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Timeout, args.connectionStatus);
      }
   });

   bobEvent.wait_for(std::chrono::milliseconds(45000));
   bobEvent.get();
   
   max.authServer->shutdown();

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
}

#if _WIN32
TEST_F(ConferenceConnectorTests, DISABLED_DoublePresenter)
{
   LoginResultEvent loginResult; loginResult.success = true;
   cpc::vector<cpc::string> permissions; permissions.push_back("*");

   // Max is the auth server
   TestAccount max("max", Account_Init);
   setupAuthServer(max);

   // Maia is the orchestration and confbridge server
   TestAccount maia("maia", Account_Init);
   setupConfBridgeServer(maia);

   // Alice is a client SDK (screenshare presenter)
   TestAccount alice("alice", Account_Init);
   TestAccount aliceTwo("aliceTwo", Account_Init);

   // Bob is a client SDK (screenshare participant)
   TestAccount bob("bob", Account_Init);

   // enable H.264
   alice.video->queryCodecList();
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);
   
   aliceTwo.video->queryCodecList();
   aliceTwo.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   aliceTwo.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   aliceTwo.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);

   bob.video->queryCodecList();
   bob.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);

   alice.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   aliceTwo.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   bob.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   alice.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   aliceTwo.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   bob.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   HWND hwndAliceRemote = NULL;
   HWND hwndBobRemote = NULL;
   HWND hwndBobRemoteLarge = NULL;

   if (TestEnvironmentConfig::drawLocalVideo())
   {
      ASSERT_EQ(0, ViECreateWindow(hwndAliceRemote, 0, 0, 352, 288, "Alice (incoming)"));
      alice.video->setIncomingVideoRenderTarget(hwndAliceRemote);

      ASSERT_EQ(0, ViECreateWindow(hwndBobRemote, 356, 0, 352, 288, "Bob (incoming)"));
      bob.video->setIncomingVideoRenderTarget(hwndBobRemote);

      ASSERT_EQ(0, ViECreateWindow(hwndBobRemoteLarge, 0, 292, 704, 576, "Bob (incoming large)"));
   }

   std::atomic_bool testDone(false);
   auto mediaEvent = std::async(std::launch::async, [&]() {
      while (!testDone) {
         // conf connector fires method calls it expects to be processed on app thread
         alice.mediaEvents->processNonUnitTestEvents(0);
         aliceTwo.mediaEvents->processNonUnitTestEvents(0);
         bob.mediaEvents->processNonUnitTestEvents(0);
         std::this_thread::sleep_for(std::chrono::milliseconds(20));
      }
   });

   ConferenceConnectorHandle aliceConfConn = alice.createConferenceConnector();
   ConferenceConnectorSettings aliceCloudSettings;
   aliceCloudSettings.authServerUrl = "https://127.0.0.1:18084";
   aliceCloudSettings.orchestrationServerUrl = "https://127.0.0.1:18082/jsonApi";
   aliceCloudSettings.regionCode = "NA";
   aliceCloudSettings.username = "user1";
   aliceCloudSettings.password = "1234";
   aliceCloudSettings.ignoreCertVerification = true;
   aliceCloudSettings.authServerApiKey = "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiKzxgDrW3u9+XzGN4r/GI5o+BMtty+63OT4lwEgFlkpedq5FY3eCzI4lB1WXDVip0OpCM1zYoCh6mqi7KaS1Vg==";
   alice.conferenceConnector->setConnectionSettings(aliceConfConn, aliceCloudSettings);
   alice.conferenceConnector->connectToConferenceService(aliceConfConn);

   std::promise<std::string> joinUrlPr;
   std::future<std::string> joinUrlFu = joinUrlPr.get_future();
   auto maiaEvent = std::async(std::launch::async, [&]() {
      JsonApiUserHandle jsonApiUser = 0;
      NewLoginEvent args;

      // Maia has to process the login attempt (associate the context with an SDK instance)
      ASSERT_TRUE(cpcExpectEvent(maia.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 15000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args));
      ASSERT_NE(jsonApiUser, 0);
      maia.jsonApiServer->setJsonApiUserContext(jsonApiUser, maia.phone, permissions);
      maia.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);

      // Maia has to process the login attempt (associate the context with an SDK instance)
      ASSERT_TRUE(cpcExpectEvent(maia.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 15000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args));
      ASSERT_NE(jsonApiUser, 0);
      maia.jsonApiServer->setJsonApiUserContext(jsonApiUser, maia.phone, permissions);
      maia.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);

   });

   auto aliceEvent = std::async(std::launch::async, [&]() {
      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
      }

      alice.conferenceConnector->queryConferenceList(aliceConfConn);

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 0);
      }

      CloudConferenceSettings confSettings;
      confSettings.conferenceDescription = "test";
      confSettings.conferenceType = CloudConferenceType_Screenshare;
      confSettings.conferenceId = "screenshare";
      alice.conferenceConnector->createNewConference(aliceConfConn, confSettings);
      CloudConferenceHandle aliceScreenShare = 0;

      {
         ConferenceConnectorHandle conn;
         ConferenceCreatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceCreated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_NE(args.conference, 0);
         ASSERT_NE(args.conference, -1);
         aliceScreenShare = args.conference;
      }

      alice.conferenceConnector->queryConferenceList(aliceConfConn);

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 1);
         ASSERT_FALSE(args.conferenceList[0].joinUrl.empty());
         joinUrlPr.set_value(args.conferenceList[0].joinUrl.c_str());
         ASSERT_GT(args.conferenceList[0].conference, 0); // handle used to create a new session
      }

      CloudConferenceSessionHandle aliceSession = alice.conferenceConnector->createConferenceSession(aliceScreenShare);

      CloudConferenceSessionSettings aliceSessionSettings;
      aliceSessionSettings.role = CloudConferenceRole_Host;
      alice.conferenceConnector->setSessionSettings(aliceSession, aliceSessionSettings);

      CloudConferenceSessionMediaSettings aliceSessionMedia;
      aliceSessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
      aliceSessionMedia.videoDirection = ConferenceConnector::MediaDirection_SendRecv;
      aliceSessionMedia.remoteVideoRenderSurface = hwndAliceRemote;
      aliceSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
      alice.conferenceConnector->setSessionMediaSettings(aliceSession, aliceSessionMedia);

      alice.conferenceConnector->startSession(aliceSession);

      try
      {
         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connecting);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(40000));

         {
            ConferenceConnectorHandle conn;
            ConferenceEndedEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceEnded", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(args.conference, aliceScreenShare);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         alice.conferenceConnector->destroyConferenceConnector(aliceConfConn);



      }
      catch (...)
      {
      }

   });

   // BEGIN ALICETWO
   ConferenceConnectorHandle aliceTwoConfConn = aliceTwo.createConferenceConnector();
   //ConferenceConnectorSettings aliceCloudSettings;
   aliceCloudSettings.authServerUrl = "https://127.0.0.1:18084";
   aliceCloudSettings.orchestrationServerUrl = "https://127.0.0.1:18082/jsonApi";
   aliceCloudSettings.regionCode = "NA";
   aliceCloudSettings.username = "user1";
   aliceCloudSettings.password = "1234";
   aliceCloudSettings.ignoreCertVerification = true;
   aliceCloudSettings.authServerApiKey = "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiKzxgDrW3u9+XzGN4r/GI5o+BMtty+63OT4lwEgFlkpedq5FY3eCzI4lB1WXDVip0OpCM1zYoCh6mqi7KaS1Vg==";
   aliceTwo.conferenceConnector->setConnectionSettings(aliceTwoConfConn, aliceCloudSettings);
   aliceTwo.conferenceConnector->connectToConferenceService(aliceTwoConfConn);

   auto maiaEvent2 = std::async(std::launch::async, [&]() {
      JsonApiUserHandle jsonApiUser = 0;
      NewLoginEvent args;

      // Maia has to process the login attempt (associate the context with an SDK instance)
      ASSERT_TRUE(cpcExpectEvent(maia.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 15000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args));
      ASSERT_NE(jsonApiUser, 0);
      maia.jsonApiServer->setJsonApiUserContext(jsonApiUser, maia.phone, permissions);
      maia.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);

   });

   auto aliceTwoEvent = std::async(std::launch::async, [&]() {
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(aliceTwo.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(aliceTwo.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(aliceTwo.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
      }

      aliceTwo.conferenceConnector->queryConferenceList(aliceTwoConfConn);

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(aliceTwo.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 1);
      }

      CloudConferenceSettings confSettingsTwo;
      confSettingsTwo.conferenceDescription = "test";
      confSettingsTwo.conferenceType = CloudConferenceType_Screenshare;
      confSettingsTwo.conferenceId = "screenshare";
      aliceTwo.conferenceConnector->createNewConference(aliceTwoConfConn, confSettingsTwo);
      CloudConferenceHandle aliceTwoScreenShare = 0;


      //{
      //   ConferenceConnectorHandle conn;
      //   ConferenceEndedEvent args;
      //   ASSERT_TRUE(cpcExpectEvent(aliceTwo.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceEnded", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
      //   //ASSERT_EQ(args.conference, aliceScreenShare);
      //}

      {
         ConferenceConnectorHandle conn;
         ConferenceCreatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(aliceTwo.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceCreated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_NE(args.conference, 0);
         ASSERT_NE(args.conference, -1);
         aliceTwoScreenShare = args.conference;
      }

      aliceTwo.conferenceConnector->queryConferenceList(aliceTwoConfConn);

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(aliceTwo.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 1);
         ASSERT_FALSE(args.conferenceList[0].joinUrl.empty());
         //joinUrlPr.set_value(args.conferenceList[0].joinUrl.c_str());
         ASSERT_GT(args.conferenceList[0].conference, 0); // handle used to create a new session
      }

      CloudConferenceSessionHandle aliceTwoSession = aliceTwo.conferenceConnector->createConferenceSession(aliceTwoScreenShare);

      CloudConferenceSessionSettings aliceTwoSessionSettings;
      aliceTwoSessionSettings.role = CloudConferenceRole_Host;
      aliceTwo.conferenceConnector->setSessionSettings(aliceTwoSession, aliceTwoSessionSettings);

      CloudConferenceSessionMediaSettings aliceTwoSessionMedia;
      aliceTwoSessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
      aliceTwoSessionMedia.videoDirection = ConferenceConnector::MediaDirection_SendRecv;
      //aliceTwoSessionMedia.remoteVideoRenderSurface = hwndAliceRemote;
      aliceTwo.conferenceConnector->setSessionMediaSettings(aliceTwoSession, aliceTwoSessionMedia);

      aliceTwo.conferenceConnector->startSession(aliceTwoSession);

      try
      {
         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(aliceTwo.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connecting);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(aliceTwo.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(20000));

         safeCout("ALICE ENDS SESSION");
         aliceTwo.conferenceConnector->endSession(aliceTwoSession);

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(aliceTwo.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
         }

         {
            ConferenceConnectorHandle conn;
            ConferenceEndedEvent args;
            ASSERT_TRUE(cpcExpectEvent(aliceTwo.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceEnded", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(args.conference, aliceTwoScreenShare);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         aliceTwo.conferenceConnector->destroyConferenceConnector(aliceTwoConfConn);



      }
      catch (...)
      {
      }

   });

   // END ALICETWO

   std::shared_ptr<std::promise<int> > bobPr(new std::promise<int>());
   std::future<int> bobFu = bobPr->get_future();
   auto bobEvent = std::async(std::launch::async, [&, bobPr]() {
      ASSERT_EQ(joinUrlFu.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
      std::string joinUrlFromAlice = joinUrlFu.get();

      ConferenceConnectorHandle bobConfConn = bob.createConferenceConnector();
      ConferenceConnectorSettings bobCloudSettings;
      bobCloudSettings.authServerUrl = "https://127.0.0.1:18084";
      bobCloudSettings.orchestrationServerUrl = ""; // not needed, we use the joinUrl instead
      bobCloudSettings.joinUrl = joinUrlFromAlice.c_str();
      bobCloudSettings.regionCode = "NA";
      bobCloudSettings.username = "user2";
      bobCloudSettings.password = "1234";
      bobCloudSettings.authServerApiKey = "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiKzxgDrW3u9+XzGN4r/GI5o+BMtty+63OT4lwEgFlkpedq5FY3eCzI4lB1WXDVip0OpCM1zYoCh6mqi7KaS1Vg==";
      bobCloudSettings.ignoreCertVerification = true;
      bob.conferenceConnector->setConnectionSettings(bobConfConn, bobCloudSettings);
      bob.conferenceConnector->connectToConferenceService(bobConfConn);

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
      }

      bob.conferenceConnector->queryConferenceList(bobConfConn);
      CloudConferenceHandle bobScreenShare;

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         //ASSERT_EQ(args.conferenceList.size(), 2);
         bobScreenShare = args.conferenceList[0].conference;
      }

      CloudConferenceSessionHandle bobSession = bob.conferenceConnector->createConferenceSession(bobScreenShare);

      CloudConferenceSessionSettings bobSessionSettings;
      bobSessionSettings.role = CloudConferenceRole_Participant;
      bob.conferenceConnector->setSessionSettings(bobSession, bobSessionSettings);

      CloudConferenceSessionMediaSettings bobSessionMedia;
      bobSessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
      bobSessionMedia.videoDirection = ConferenceConnector::MediaDirection_RecvOnly;
      bobSessionMedia.remoteVideoRenderSurface = hwndBobRemote;
      bobSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
      bob.conferenceConnector->setSessionMediaSettings(bobSession, bobSessionMedia);

      bob.conferenceConnector->startSession(bobSession);

      try
      {
         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connecting);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
            EXPECT_EQ(args.screenshare.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_RecvOnly);
            EXPECT_EQ(args.audio.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
            EXPECT_EQ(args.video.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
            //EXPECT_NE(args.screenshare.mediaStreamId, -1);
            //bob.video->setIncomingVideoRenderTarget(args.screenshare.mediaStreamId, hwndBobRemote);
         }

         {
            ConferenceConnectorHandle conn;
            ConferenceEndedEvent args;
            ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceEnded", 35000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(args.conference, bobScreenShare);
         }
      }
      catch (...)
      {
      }
      bobPr->set_value(0);
   });

   aliceEvent.wait_for(std::chrono::milliseconds(45000));
   aliceEvent.get();

   bobEvent.wait_for(std::chrono::milliseconds(45000));
   bobEvent.get();
   //bobFu.get();

   maiaEvent.wait_for(std::chrono::milliseconds(45000));
   maiaEvent.get();

   //maiaEvent2.wait_for(std::chrono::milliseconds(45000));
   //maiaEvent2.get();
   //waitFor2(aliceEvent, maiaEvent);

   testDone = true;

   DestroyWindow(hwndAliceRemote);
   DestroyWindow(hwndBobRemote);
   DestroyWindow(hwndBobRemoteLarge);

   CPCAPI2::ConferenceBridge::ConferenceBridgeManager::getInterface(maia.phone)->shutdown();
   CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(maia.phone)->flushAll();
   CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(maia.phone)->shutdown();
   maia.jsonApiServer->shutdown();

   max.authServer->shutdown();

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
}
#endif

class TestScreenshareDeviceListHandler : public CPCAPI2::Media::ScreenshareDeviceListHandler
{
public:
   TestScreenshareDeviceListHandler() {}
   virtual ~TestScreenshareDeviceListHandler() {}

   virtual void onScreenshareDeviceList(const CPCAPI2::Media::ScreenshareDeviceListEvent& evt) override
   {
      resip::Lock lck(mDevicesMtx);
      mDevices = evt.devices;
      mDevicesCond.signal();
   }

   const cpc::vector<CPCAPI2::Media::ScreenshareDeviceInfo>& devices() {
      mDevicesMtx.lock();
      mDevicesCond.wait(mDevicesMtx);
      mDevicesMtx.unlock();
      return mDevices;
   }

private:
   cpc::vector<CPCAPI2::Media::ScreenshareDeviceInfo> mDevices;
   resip::Mutex mDevicesMtx;
   resip::Condition mDevicesCond;
};

TEST_F(ConferenceConnectorTests, DISABLED_PresenterOnlySimple)
{
   const cpc::string SCREENSHARE_SERVER = "screenshare1.bria-x.net";
   //const cpc::string SCREENSHARE_SERVER = "***********:18090";

   LoginResultEvent loginResult; loginResult.success = true;
   cpc::vector<cpc::string> permissions; permissions.push_back("*");

   // Alice is a client SDK (screenshare presenter)
   TestAccount alice("alice");

   // enable H.264
   
   alice.video->queryCodecList();
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);

   alice.video->setIncomingVideoRenderTarget(NULL);

   alice.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   alice.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   HWND hwndAliceRemoteVid = NULL;
   HWND hwndAliceRemote = NULL;
#if _WIN32
   ASSERT_EQ(0, ViECreateWindow(hwndAliceRemoteVid, 712, 0, 352, 288, "Alice (incoming video)"));
   alice.video->startCapture();
   alice.video->setIncomingVideoRenderTarget(hwndAliceRemoteVid);

   ASSERT_EQ(0, ViECreateWindow(hwndAliceRemote, 0, 0, 352, 288, "Alice (incoming)"));

#else // _WIN32
   // CPCAPI2::Media::VideoExt::getInterface(alice.media)->startScreenshare();
#endif

   TestScreenshareDeviceListHandler tsdlh;
   CPCAPI2::Media::VideoExt::getInterface(alice.media)->queryScreenshareDeviceList(&tsdlh, true, false);

   //std::this_thread::sleep_for(std::chrono::seconds(2));

   ConferenceConnectorHandle aliceConfConn = alice.createConferenceConnector();
   ConferenceConnectorSettings aliceCloudSettings;
   aliceCloudSettings.authServerUrl = "https://cloudsdk4.bria-x.net:18082";
   aliceCloudSettings.authServerApiKey = "-----BEGIN PUBLIC KEY-----\n"
      "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEZzshRNe4gMUuqNBwrE6leU/N5vW8\n"
      "+HW/pZw5XfsxvzEtUlGwp+A9sNedJNUtjYD/2gFFY/nFuM4Gmnu6zeteTQ==\n"
      "-----END PUBLIC KEY-----";
   aliceCloudSettings.orchestrationServerUrl = "http://" + SCREENSHARE_SERVER + "/jsonApi";
   aliceCloudSettings.regionCode = "LOCAL";
   {
      resip::Data usernameStr;
      {
         resip::DataStream ds(usernameStr);
         ds << "alice-unittests-" << resip::Random::getCryptoRandomBase64(2);
      }
      aliceCloudSettings.username = "<EMAIL>"; // usernameStr.c_str();
   }
   aliceCloudSettings.password = "1234";
   alice.conferenceConnector->setConnectionSettings(aliceConfConn, aliceCloudSettings);
   alice.conferenceConnector->connectToConferenceService(aliceConfConn);

   std::promise<std::string> joinUrlPr;
   std::future<std::string> joinUrlFu = joinUrlPr.get_future();

   auto aliceEvent = std::async(std::launch::async, [&]() {
      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 30000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
      }

      alice.conferenceConnector->queryConferenceList(aliceConfConn);

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 0);
      }

      for (int itc = 0; itc < 1; itc++)
      {
         CloudConferenceSettings confSettings;
         confSettings.conferenceDescription = "test";
         confSettings.conferenceType = CloudConferenceType_Screenshare;
         confSettings.conferenceId = "screenshare";
         alice.conferenceConnector->createNewConference(aliceConfConn, confSettings);
         CloudConferenceHandle aliceScreenShare = 0;

         {
            ConferenceConnectorHandle conn;
            ConferenceCreatedEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceCreated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_GT(args.conference, 0);
            aliceScreenShare = args.conference;
         }

         alice.conferenceConnector->queryConferenceList(aliceConfConn);

         {
            ConferenceConnectorHandle conn;
            ConferenceListUpdatedEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(args.conferenceList.size(), 1);
            ASSERT_FALSE(args.conferenceList[0].joinUrl.empty());
            if (itc == 0)
            {
               joinUrlPr.set_value(args.conferenceList[0].joinUrl.c_str());
            }
            ASSERT_GT(args.conferenceList[0].conference, 0); // handle used to create a new session
         }

         CloudConferenceSessionHandle aliceSession = alice.conferenceConnector->createConferenceSession(aliceScreenShare);

         CloudConferenceSessionSettings aliceSessionSettings;
         aliceSessionSettings.role = CloudConferenceRole_Host;
         alice.conferenceConnector->setSessionSettings(aliceSession, aliceSessionSettings);

         CloudConferenceSessionMediaSettings aliceSessionMedia;
         aliceSessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
         aliceSessionMedia.videoDirection = ConferenceConnector::MediaDirection_SendRecv;
         aliceSessionMedia.remoteVideoRenderSurface = hwndAliceRemote;
         aliceSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
         alice.conferenceConnector->setSessionMediaSettings(aliceSession, aliceSessionMedia);

         alice.conferenceConnector->startSession(aliceSession);

         try
         {
            {
               // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
               ConferenceConnectorHandle conn;
               ConferenceSessionStatusChangedEvent args;
               EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
               EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connecting);
            }

            {
               // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
               ConferenceConnectorHandle conn;
               ConferenceSessionStatusChangedEvent args;
               EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
               EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
            }

            std::this_thread::sleep_for(std::chrono::milliseconds(5000));
            //safeCout("ALICE HANGS UP");
            //alice.conversation->end(aliceCall);
            //alice.video->setIncomingVideoRenderTarget(NULL);
            //bob.video->setIncomingVideoRenderTarget(NULL);

            std::this_thread::sleep_for(std::chrono::milliseconds(23000));

            //alice.conversation->setMediaEnabled(aliceCall, SipConversation::MediaType_Video, true);
            //alice.conversation->sendMediaChangeRequest(aliceCall);
            //safeCout("ALICE setVideoMute");
            //alice.video->setVideoMute(true);

            std::this_thread::sleep_for(std::chrono::milliseconds(64000));

            safeCout("ALICE ENDS SESSION");
            alice.conferenceConnector->endSession(aliceSession);

            {
               // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
               ConferenceConnectorHandle conn;
               ConferenceSessionStatusChangedEvent args;
               EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
               EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
            }

            {
               ConferenceConnectorHandle conn;
               ConferenceCreatedEvent args;
               ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceEnded", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
               ASSERT_EQ(args.conference, aliceScreenShare);
            }

            std::this_thread::sleep_for(std::chrono::milliseconds(5000));

            //alice.conferenceConnector->destroyConferenceConnector(aliceConfConn);

            //alice.conferenceConnector->queryConferenceList(aliceConfConn);

            //{
            //   ConferenceConnectorHandle conn;
            //   ConferenceListUpdatedEvent args;
            //   ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            //   ASSERT_EQ(args.conferenceList.size(), 0);
            //}

         }
         catch (...)
         {
         }
      }
   });

   aliceEvent.wait_for(std::chrono::milliseconds(180000));
   aliceEvent.get();

   //waitFor2(aliceEvent, maiaEvent);

   alice.video->stopCapture();

#if _WIN32
   DestroyWindow(hwndAliceRemoteVid);
   DestroyWindow(hwndAliceRemote);
#endif // _WIN32

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
}

TEST_F(ConferenceConnectorTests, ConnectSuccess_ScreenShare_1024x4096)
{
   // a 4K tall window
   doFakeScreenShare(
      1024,
      4096,
      768,     // adjusted from 1024 to maintain aspect ratio
      3072     // reduced from 4096 to meet max limit
   );
}

TEST_F(ConferenceConnectorTests, ConnectSuccess_ScreenShare_1024x8192)
{
   // an 8K tall window
   doFakeScreenShare(
      1024,
      8192,
      384,     // adjusted from 1024 to maintain aspect ratio
      3072     // reduced from 8192 to meet max limit
   );
}

TEST_F(ConferenceConnectorTests, ConnectSuccess_ScreenShare_2047x1152)
{
   // an almost normal size, but the width is not even (a requirement for webrtc) nor 16 bit aligned (an OpenH264 requirement)
   doFakeScreenShare(
      2047,
      1152,
      2048,    // increased from 2047 to satisfy 16 bit alignment
      1152
   );
}


TEST_F(ConferenceConnectorTests, ConnectSuccess_ScreenShare_2049x1152)
{
   // an almost normal size, but the width is not even (a requirement for webrtc) nor 16 bit aligned (an OpenH264 requirement)
   doFakeScreenShare(
      2049,
      1152,
      2048,    // reduced from 2049 to satisfy 16 bit alignment
      1152
   );
}


TEST_F(ConferenceConnectorTests, ConnectSuccess_ScreenShare_2048x1151)
{
   // an almost normal size, but the height is not even (a requirement for webrtc) nor 16 bit aligned (an OpenH264 requirement)
   doFakeScreenShare(
      2048,
      1151,
      2048,    // not adjusted (should it be to maintain aspect ratio?)
      1152     // increased from 1151 to satisfy 16 bit alignment
   );
}


TEST_F(ConferenceConnectorTests, ConnectSuccess_ScreenShare_2048x1153)
{
   // an almost normal size, but the height is not even (a requirement for webrtc) nor 16 bit aligned (an OpenH264 requirement)
   doFakeScreenShare(
      2048,
      1153,
      2048,    // not adjusted (should it be to maintain aspect ratio?)
      1152     // reduced from 1153 to satisfy 16 bit alignment
   );
}


TEST_F(ConferenceConnectorTests, ConnectSuccess_ScreenShare_3071x3071)
{
   // let's try a square that is not even width (a webRTC requirement) nor 16 bit aligned (an OpenH264 requirement)
   doFakeScreenShare(
      3071,
      3071,
      3072,    // rounded up from 3071 to satisfy 16 bit alignment
      3072     // adjusted to maintain aspect ratio
   );
}


TEST_F(ConferenceConnectorTests, ConnectSuccess_ScreenShare_3072x3072)
{
   // let's try a square that shouldn't require any scaling
   doFakeScreenShare(
      3072,
      3072,
      3072,
      3072
   );
}


TEST_F(ConferenceConnectorTests, ConnectSuccess_ScreenShare_4096x4096)
{
   // let's try a 4K square that is too tall for webrtc
   doFakeScreenShare(
      4096,
      4096,
      3072,    // adjusted from 4096 to maintain aspect ratio
      3072     // reduced from 4096 to meet max limit
   );
}


TEST_F(ConferenceConnectorTests, ConnectSuccess_ScreenShare_8192x8192)
{
   // let's try an 8K square that is too tall for webrtc
   doFakeScreenShare(
      8192,
      8192,
      3072,    // adjusted from 8192 to maintain aspect ratio
      3072     // reduced from 8192 to meet max limit
   );
}


TEST_F(ConferenceConnectorTests, ConnectSuccess_ScreenShare_8192x4608)
{
   // let's try normal 8K and make sure it gets scaled down for webrtc
   doFakeScreenShare(
      8192,
      4608,
      4096,    // adjusted from 8192 to meet max limit
      2304     // adjusted from 4608 to maintain aspect ratio
   );
}


TEST_F(ConferenceConnectorTests, ConnectSuccess_ScreenShare_4096x3072)
{
   // let's try dimensions that fit the WebRTC maximums, but exceed the resulting
   // size limitation of the Wels SVC Encoder
   doFakeScreenShare(
      4096,
      3072,
      3536,    // adjusted from 4096 to meet max limit and satisfy 16 bit alignment
      2656     // adjusted from 3072 to maintain aspect ratio and satisfy 16 bit alignment
   );
}


TEST_F(ConferenceConnectorTests, ConnectSuccess_ScreenShare_1024x768)
{
   // let's try dimensions that shouldn't require any changes
   doFakeScreenShare(
      1024,
      768,
      1024,
      768
   );
}

TEST_F(ConferenceConnectorTests, ConnectSuccess_ScreenShare_3172x2962)
{
   // CSS-99
   doFakeScreenShare(
      3172,
      2962,
      3168,    // adjusted from 3172 to satisfy 16 bit alignment
      2960     // adjusted from 2962 to maintain aspect ratio
   );
}

TEST_F(ConferenceConnectorTests, ConnectSuccess_ScreenShare_3172x2969)
{
   // CSS-99
   doFakeScreenShare(
      3172,
      2969,
      3168,    // adjusted from 3172 to satisfy 16 bit alignment
      2976     // adjusted from 2969 to maintain aspect ratio
   );
}

#ifdef __APPLE__ // only tested on mac so far
class DummyWindow
{
public:
   DummyWindow(const std::string& localCaption)
   {
      int xPosition = 0;
      int yPosition = 0;
      EXPECT_EQ(0, ViECreateWindow(hwndLocalWindow, xPosition, yPosition, 352, 288, localCaption.c_str(), nsLocalWindow));
   }
   
   ~DummyWindow()
   {
      ViEDestroyWindow(nsLocalWindow, hwndLocalWindow);
   }
private:
   CpTestWindowHandle hwndLocalWindow;
   CpTestWindowHandle nsLocalWindow;


};

TEST_F(ConferenceConnectorTests, ShareWindow)
{
	LoginResultEvent loginResult; loginResult.success = true;
	cpc::vector<cpc::string> permissions; permissions.push_back("*");

	// Max is the auth server
	TestAccount max("max", Account_Init);
	setupAuthServer(max);

	// Maia is the orchestration and confbridge server
	TestAccount maia("maia", Account_Init);
	setupConfBridgeServer(maia);

	// Alice is a client SDK (screenshare presenter)
	TestAccount alice("alice", Account_Init);

	// Bob is a client SDK (screenshare participant)
	TestAccount bob("bob", Account_Init);

	// enable H.264
	alice.video->queryCodecList();
	alice.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
	alice.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
	alice.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);
	bob.video->queryCodecList();
	bob.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
	bob.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
	bob.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);

	alice.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
	bob.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

	alice.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
	bob.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

	alice.initiateVideo(false, true);
    bob.initiateVideo(false, true);

    const int kTestFrameRate = 10;
   
	std::atomic_bool testDone(false);
	auto mediaEvent = std::async(std::launch::async, [&]() {
		while (!testDone) {
		    // conf connector fires method calls it expects to be processed on app thread
			alice.mediaEvents->processNonUnitTestEvents(0);
			bob.mediaEvents->processNonUnitTestEvents(0);
			std::this_thread::sleep_for(std::chrono::milliseconds(20));
		}
	});

	ConferenceConnectorHandle aliceConfConn = alice.createConferenceConnector();
	ConferenceConnectorSettings aliceCloudSettings;
	aliceCloudSettings.authServerUrl = "https://127.0.0.1:18084";
	aliceCloudSettings.orchestrationServerUrl = "https://127.0.0.1:18082/jsonApi";
	aliceCloudSettings.regionCode = "NA";
	aliceCloudSettings.username = "user1";
	aliceCloudSettings.password = "1234";
	aliceCloudSettings.ignoreCertVerification = true;
   aliceCloudSettings.authServerApiKey = "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiKzxgDrW3u9+XzGN4r/GI5o+BMtty+63OT4lwEgFlkpedq5FY3eCzI4lB1WXDVip0OpCM1zYoCh6mqi7KaS1Vg==";
	alice.conferenceConnector->setConnectionSettings(aliceConfConn, aliceCloudSettings);
	alice.conferenceConnector->connectToConferenceService(aliceConfConn);

	std::promise<std::string> joinUrlPr;
	std::future<std::string> joinUrlFu = joinUrlPr.get_future();
	auto maiaEvent = std::async(std::launch::async, [&]() {
		JsonApiUserHandle jsonApiUser = 0;
		NewLoginEvent args;

		// Maia has to process the login attempt (associate the context with an SDK instance)
		ASSERT_TRUE(cpcExpectEvent(maia.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 15000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args));
		ASSERT_NE(jsonApiUser, 0);
		maia.jsonApiServer->setJsonApiUserContext(jsonApiUser, maia.phone, permissions);
		maia.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);

		// Maia has to process the login attempt (associate the context with an SDK instance)
		ASSERT_TRUE(cpcExpectEvent(maia.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 15000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args));
		ASSERT_NE(jsonApiUser, 0);
		maia.jsonApiServer->setJsonApiUserContext(jsonApiUser, maia.phone, permissions);
		maia.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);

	});

	auto aliceEvent = std::async(std::launch::async, [&]() {
		{
			ConferenceConnectorHandle conn;
			ServiceConnectionStatusEvent args;
			ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
			ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
		}

		{
			ConferenceConnectorHandle conn;
			ServiceConnectionStatusEvent args;
			ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
			ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
		}

		{
			ConferenceConnectorHandle conn;
			ServiceConnectionStatusEvent args;
			ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
			ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
		}

		alice.conferenceConnector->queryConferenceList(aliceConfConn);

		{
			ConferenceConnectorHandle conn;
			ConferenceListUpdatedEvent args;
			ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
			ASSERT_EQ(args.conferenceList.size(), 0);
		}

		CloudConferenceSettings confSettings;
      confSettings.conferenceDescription = "test";
      confSettings.conferenceType = CloudConferenceType_Screenshare;
      confSettings.conferenceId = "screenshare";
      alice.conferenceConnector->createNewConference(aliceConfConn, confSettings);
      CloudConferenceHandle aliceScreenShare = 0;

      {
         ConferenceConnectorHandle conn;
         ConferenceCreatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceCreated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_NE(args.conference, 0);
         ASSERT_NE(args.conference, -1);
         aliceScreenShare = args.conference;
      }

		alice.conferenceConnector->queryConferenceList(aliceConfConn);

		{
			ConferenceConnectorHandle conn;
			ConferenceListUpdatedEvent args;
			ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
			ASSERT_EQ(args.conferenceList.size(), 1);
			ASSERT_FALSE(args.conferenceList[0].joinUrl.empty());
			joinUrlPr.set_value(args.conferenceList[0].joinUrl.c_str());
			ASSERT_GT(args.conferenceList[0].conference, 0); // handle used to create a new session
		}

      
      const std::string dummyCaptureWindowTitle = "cpcapi2DummyCaptureWindow";
      DummyWindow dummyWindowToCapture(dummyCaptureWindowTitle);
      std::this_thread::sleep_for(std::chrono::seconds(2));
      
      // crashes seen on linux
#ifndef __linux__
      CPCAPI2::Media::VideoExt* videoExt = CPCAPI2::Media::VideoExt::getInterface(alice.media);
      videoExt->queryScreenshareDeviceList(reinterpret_cast<Media::ScreenshareDeviceListHandler*>(0xDEADBEEF), false, true);
      CPCAPI2::Media::ScreenshareDeviceListEvent evt;
      int handle = 0;
      ASSERT_TRUE(alice.mediaEvents->expectEvent("ScreenshareDeviceListHandler::onScreenshareDeviceList", 30000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
      
      bool foundWindow = false;
      for (cpc::vector<Media::ScreenshareDeviceInfo>::const_iterator it = evt.devices.begin(); it != evt.devices.end(); ++it)
      {
         cpc::string d = it->deviceDescription;
         if (d.c_str() == dummyCaptureWindowTitle)
         {
            foundWindow = true;
            videoExt->setScreenshareCaptureDevice(it->deviceId);
            break;
         }
      }
      ASSERT_TRUE(foundWindow);
#endif // #ifndef __linux__

		CloudConferenceSessionHandle aliceSession = alice.conferenceConnector->createConferenceSession(aliceScreenShare);

		CloudConferenceSessionSettings aliceSessionSettings;
		aliceSessionSettings.role = CloudConferenceRole_Host;
		alice.conferenceConnector->setSessionSettings(aliceSession, aliceSessionSettings);

		CloudConferenceSessionMediaSettings aliceSessionMedia;
		aliceSessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
		aliceSessionMedia.videoDirection = ConferenceConnector::MediaDirection_SendRecv;
      aliceSessionMedia.remoteVideoRenderSurface = alice.videoHelper->getHwndIncomingWindow();
      aliceSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
      aliceSessionMedia.screenCaptureMaxFrameRate = kTestFrameRate;
		alice.conferenceConnector->setSessionMediaSettings(aliceSession, aliceSessionMedia);

		alice.conferenceConnector->startSession(aliceSession);

		try
		{
			{
				// virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
				ConferenceConnectorHandle conn;
				ConferenceSessionStatusChangedEvent args;
				EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
				EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connecting);
			}

         {
            ConferenceConnectorHandle conn;
            ConferenceSessionMediaStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionMediaStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.mediaStatus, CPCAPI2::ConferenceConnector::SessionMediaStatus_OfferSent);
         }

         {
            ConferenceConnectorHandle conn;
            ConferenceSessionMediaStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionMediaStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.mediaStatus, CPCAPI2::ConferenceConnector::SessionMediaStatus_AnswerReceived);
         }

			{
				// virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
				ConferenceConnectorHandle conn;
				ConferenceSessionStatusChangedEvent args;
				EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
				EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
			}

			std::this_thread::sleep_for(std::chrono::milliseconds(20000));

			safeCout("ALICE ENDS SESSION");
			alice.conferenceConnector->endSession(aliceSession);

			{
				// virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
				ConferenceConnectorHandle conn;
				ConferenceSessionStatusChangedEvent args;
				EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
				EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
			}

			{
				ConferenceConnectorHandle conn;
				ConferenceEndedEvent args;
            bool gotOnConferenceEnded = cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceEnded", 30000, CPCAPI2::test::AlwaysTruePred(), conn, args);
            //if (!gotOnConferenceEnded)
            //{
            //   int* a = 0;
            //   *a = NULL;
            //}
            ASSERT_TRUE(gotOnConferenceEnded);
				ASSERT_EQ(args.conference, aliceScreenShare);
			}

			std::this_thread::sleep_for(std::chrono::milliseconds(5000));

			alice.conferenceConnector->destroyConferenceConnector(aliceConfConn);



		}
		catch (...)
		{
		}

	});

	std::shared_ptr<std::promise<int> > bobPr(new std::promise<int>());
	std::future<int> bobFu = bobPr->get_future();
	auto bobEvent = std::async(std::launch::async, [&, bobPr]() {
		ASSERT_EQ(joinUrlFu.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
		std::string joinUrlFromAlice = joinUrlFu.get();

		ConferenceConnectorHandle bobConfConn = bob.createConferenceConnector();
		ConferenceConnectorSettings bobCloudSettings;
		bobCloudSettings.authServerUrl = "https://127.0.0.1:18084";
		bobCloudSettings.orchestrationServerUrl = ""; // not needed, we use the joinUrl instead
		bobCloudSettings.joinUrl = joinUrlFromAlice.c_str();
		bobCloudSettings.regionCode = "NA";
		bobCloudSettings.username = "user2";
		bobCloudSettings.password = "1234";
		bobCloudSettings.ignoreCertVerification = true;
      bobCloudSettings.authServerApiKey = "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiKzxgDrW3u9+XzGN4r/GI5o+BMtty+63OT4lwEgFlkpedq5FY3eCzI4lB1WXDVip0OpCM1zYoCh6mqi7KaS1Vg==";

		bob.conferenceConnector->setConnectionSettings(bobConfConn, bobCloudSettings);
		bob.conferenceConnector->connectToConferenceService(bobConfConn);

		{
			ConferenceConnectorHandle conn;
			ServiceConnectionStatusEvent args;
			ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
			ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
		}

		{
			ConferenceConnectorHandle conn;
			ServiceConnectionStatusEvent args;
			ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
			ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
		}

		{
			ConferenceConnectorHandle conn;
			ServiceConnectionStatusEvent args;
			ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
			ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
		}

		bob.conferenceConnector->queryConferenceList(bobConfConn);
		CloudConferenceHandle bobScreenShare;

		{
			ConferenceConnectorHandle conn;
			ConferenceListUpdatedEvent args;
			ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
			ASSERT_EQ(args.conferenceList.size(), 1);
			bobScreenShare = args.conferenceList[0].conference;
		}

      //std::this_thread::sleep_for(std::chrono::milliseconds(2000));

		CloudConferenceSessionHandle bobSession = bob.conferenceConnector->createConferenceSession(bobScreenShare);

		CloudConferenceSessionSettings bobSessionSettings;
		bobSessionSettings.role = CloudConferenceRole_Participant;
		bob.conferenceConnector->setSessionSettings(bobSession, bobSessionSettings);

		CloudConferenceSessionMediaSettings bobSessionMedia;
		bobSessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
		bobSessionMedia.videoDirection = ConferenceConnector::MediaDirection_RecvOnly;
        bobSessionMedia.remoteVideoRenderSurface = bob.videoHelper->getHwndIncomingWindow();
        bobSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
		bob.conferenceConnector->setSessionMediaSettings(bobSession, bobSessionMedia);

		bob.conferenceConnector->startSession(bobSession);

		try
		{
			{
				// virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
				ConferenceConnectorHandle conn;
				ConferenceSessionStatusChangedEvent args;
				EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
				EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connecting);
			}

         {
            ConferenceConnectorHandle conn;
            ConferenceSessionMediaStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionMediaStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.mediaStatus, CPCAPI2::ConferenceConnector::SessionMediaStatus_OfferSent);
         }
         
			{
				// virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
				ConferenceConnectorHandle conn;
				ConferenceSessionStatusChangedEvent args;
				EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
				EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
				EXPECT_EQ(args.screenshare.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_RecvOnly);
				EXPECT_EQ(args.audio.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
				EXPECT_EQ(args.video.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
				//EXPECT_NE(args.screenshare.mediaStreamId, -1);
				//bob.video->setIncomingVideoRenderTarget(args.screenshare.mediaStreamId, hwndBobRemote);
			}
			std::this_thread::sleep_for(std::chrono::milliseconds(5000));

			bobSessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
			bobSessionMedia.videoDirection = ConferenceConnector::MediaDirection_RecvOnly;
         bobSessionMedia.remoteVideoRenderSurface = bob.videoHelper->getHwndIncomingWindow();
         bobSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
			bob.conferenceConnector->setSessionMediaSettings(bobSession, bobSessionMedia);
			bob.conferenceConnector->updateSessionMedia(bobSession);

         {
            ConferenceConnectorHandle conn;
            ConferenceSessionMediaStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionMediaStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.mediaStatus, CPCAPI2::ConferenceConnector::SessionMediaStatus_AnswerReceived);
         }

			{
				// virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
				ConferenceConnectorHandle conn;
				ConferenceSessionStatusChangedEvent args;
				EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
				EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
				EXPECT_EQ(args.screenshare.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_RecvOnly);
				EXPECT_EQ(args.audio.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
				EXPECT_EQ(args.video.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
				//bob.video->setIncomingVideoRenderTarget(args.screenshare.mediaStreamId, hwndBobRemote);
			}

			std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         ConferenceConnectorInternal* confConnInt = dynamic_cast<ConferenceConnectorInternal*>(bob.conferenceConnector);
         confConnInt->queryMediaStatistics(bobSession);

         {
            ConferenceConnectorHandle conn;
            ConferenceConnectorMediaStatisticsEvent args;
            ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceConnectorMediaStatistics", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_NEAR(args.mediaStreamStats[0].videoReceiveFps, kTestFrameRate, 4);
         }
         
			{
				// virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
				ConferenceConnectorHandle conn;
				ConferenceParticipantListUpdatedEvent args;
				EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceParticipantListUpdated", 180000, CPCAPI2::test::AlwaysTruePred(), conn, args));
				//EXPECT_EQ(args.participantList.size(), 0);
			}

			{
				// virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
				ConferenceConnectorHandle conn;
				ConferenceSessionStatusChangedEvent args;
				EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 700000, CPCAPI2::test::AlwaysTruePred(), conn, args));
				EXPECT_EQ(CPCAPI2::ConferenceConnector::SessionStatus_NotConnected, args.sessionStatus);
			}

			{
				ConferenceConnectorHandle conn;
				ConferenceEndedEvent args;
				ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceEnded", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
				ASSERT_EQ(args.conference, bobScreenShare);
			}

		}
		catch (...)
		{
		}
		bob.conferenceConnector->destroyConferenceConnector(bobConfConn);

		bobPr->set_value(0);
	});

	aliceEvent.wait_for(std::chrono::milliseconds(45000));
	aliceEvent.get();

	bobEvent.wait_for(std::chrono::milliseconds(45000));
	bobEvent.get();
	//bobFu.get();

	maiaEvent.wait_for(std::chrono::milliseconds(45000));
	maiaEvent.get();

	//waitFor2(aliceEvent, maiaEvent);

	testDone = true;

	CPCAPI2::ConferenceBridge::ConferenceBridgeManager::getInterface(maia.phone)->shutdown();
	CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(maia.phone)->flushAll();
	CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(maia.phone)->shutdown();
	maia.jsonApiServer->shutdown();

	max.authServer->shutdown();

	std::this_thread::sleep_for(std::chrono::milliseconds(1000));
}
#endif // #ifdef __APPLE__

#endif
