#pragma once
#ifndef X11_HELPERS_H
#define X11_HELPERS_H

// Avoid include <X11/Xlib.h>.
struct _XDisplay;
typedef unsigned long VideoWindow;

namespace CPCAPI2
{
namespace test
{
struct WindowAndDisplayHolder
{
   void* window;
   void* display;
};

class TestX11App
{
public:
   TestX11App();
   virtual ~TestX11App();

   static WindowAndDisplayHolder createWindow(int xpos, int ypos, unsigned int width,
                                              unsigned int height, const char* title);
   static void destroyWindow(void* display, void* window);
};
}
}
#endif //X11_HELPERS_H
