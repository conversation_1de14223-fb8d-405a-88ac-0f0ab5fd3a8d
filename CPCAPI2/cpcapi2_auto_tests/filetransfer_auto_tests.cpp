
#if 0

#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#if (CPCAPI2_BRAND_SIP_FILE_TRANSFER_MODULE == 1)

#include "cpcapi2_test_fixture.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>

#include "test_framework/cpcapi2_test_framework.h"
#include "test_framework/bare_bones_sip_endpoint.h"

#include <thread>
#include <future>

using namespace CPCAPI2;
using namespace CPCAPI2::SipFileTransfer;
using namespace CPCAPI2::test;

namespace {

class FileTransferTest : public CpcapiAutoTest
{
public:
   FileTransferTest() {}
   virtual ~FileTransferTest() {}
};

TEST_F(FileTransferTest, BasicFileTransfer) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
   SipFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
   SipFileTransferItemHandle aliceTransferItem = alice.fileTransferManager->createFileTransferItem( alice.handle );

   // Setup the file transfer items
   SipFileTransferItems items;
   SipFileTransferItemDetail itemDetail;
   itemDetail.handle        = aliceTransferItem;
   itemDetail.localfileName = "send.png";
   itemDetail.localfilePath = ".";
   items.push_back( itemDetail );

   alice.fileTransferManager->configureFileTransferItems( aliceTransfer, items );
   alice.fileTransferManager->addParticipant( aliceTransfer, bob.config.uri().c_str() );
   alice.fileTransferManager->start( aliceTransfer );

   // Overview of Bob's thread:
   //  - 
   auto bobEvents = std::async(std::launch::async, [&] () {
      SipFileTransferHandle bobTransfer = 0;
      {
         SipFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( bob.fileTransferEvents, "SipFileTransferHandler::onNewFileTransfer",
            5000, AlwaysTruePred(), h, evt ));
         bobTransfer = h;
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
      }

      // Bob provisionally accepts the file transfer (sends a 180 to Alice)
      bob.fileTransferManager->provisionalAccept(bobTransfer);

      SipFileTransferItems items;
      {
         SipFileTransferHandle h;
         FileTransferConfiguredEvent evt;
         ASSERT_TRUE(cpcExpectEvent( bob.fileTransferEvents, "SipFileTransferHandler::onFileTransferConfigured",
            5000, AlwaysTruePred(), h, evt ));
         bobTransfer = h;
         items = evt.fileItems;
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         ASSERT_EQ(evt.fileItems.size(), 1);
      }

      // Bob then proceeds to ask the user what should be the filename and location
      // and whether or not they want to accept etc. This is then "configured".
      items[ 0 ].acceptedState = ftitem_accepted;
      items[ 0 ].localfileName = "recv.png";
      bob.fileTransferManager->configureFileTransferItems( bobTransfer, items );

      // The file transfer is then accepted
      bob.fileTransferManager->accept( bobTransfer );

      {
         // Watch the progress
         FileTransferItemProgressEvent evt;
         evt.percent = 0;
         evt.fileTransferItem = 0;

         while( evt.percent < 100 )
         {
            ASSERT_TRUE(cpcExpectEvent( bob.fileTransferEvents, "SipFileTransferHandler::onFileTransferItemProgress",
               5000, AlwaysTruePred(), bobTransfer, evt));
         }
      }
      
      {
         // Wait for the transfer to finish (it will be disconnected from Alice's side)
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcExpectEvent( bob.fileTransferEvents, "SipFileTransferHandler::onFileTransferItemEnded",
         5000, AlwaysTruePred(), bobTransfer, ftie ));

         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( bob.fileTransferEvents, "SipFileTransferHandler::onFileTransferEnded",
         5000, AlwaysTruePred(), bobTransfer, fte ));
      };
   });

   // Overview of Alice's thread:
   //  - 
   auto aliceEvents = std::async(std::launch::async, [&] () {

      {
         SipFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( alice.fileTransferEvents, "SipFileTransferHandler::onNewFileTransfer",
            5000, AlwaysTruePred(), h, evt ));
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
      }

      SipFileTransferItems items;
      {
         SipFileTransferHandle h;
         FileTransferConfiguredEvent evt;
         ASSERT_TRUE(cpcExpectEvent( alice.fileTransferEvents, "SipFileTransferHandler::onFileTransferConfigured",
            5000, AlwaysTruePred(), h, evt ));
         items = evt.fileItems;
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
         ASSERT_EQ(evt.fileItems.size(), 1);
      }

      ASSERT_EQ( items[ 0 ].localfileName, "send.png" );

      {
         // Watch the progress
         FileTransferItemProgressEvent evt;
         evt.fileTransferItem = aliceTransferItem;
         evt.percent = 0;

         while( evt.percent < 100 )
         {
            ASSERT_TRUE(cpcExpectEvent( alice.fileTransferEvents, "SipFileTransferHandler::onFileTransferItemProgress",
               5000, AlwaysTruePred(), aliceTransfer, evt));
         }
      }

      {
         // Wait for the transfer of the item to finish
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcExpectEvent( alice.fileTransferEvents, "SipFileTransferHandler::onFileTransferItemEnded",
         5000, AlwaysTruePred(), aliceTransfer, ftie ));

         // end the file transfer session
         alice.fileTransferManager->end( aliceTransfer );

         // Wait for the transfer to finish
         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( alice.fileTransferEvents, "SipFileTransferHandler::onFileTransferEnded",
         5000, AlwaysTruePred(), aliceTransfer, fte ));
      };
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(10000));
}

// Test works against local SIP servers but will have problems running against NewPace etc
// servers due to timing and/or message delivery order differences.
TEST_F(FileTransferTest, SendTwoFilesOneINVITE) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
   SipFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
   SipFileTransferItemHandle aliceTransferItem = alice.fileTransferManager->createFileTransferItem( alice.handle );
   SipFileTransferItemHandle aliceTransferItem2 = alice.fileTransferManager->createFileTransferItem( alice.handle );

   // Setup the file transfer items
   SipFileTransferItems items;
   SipFileTransferItemDetail itemDetail, itemDetail2;
   itemDetail.handle         = aliceTransferItem;
   itemDetail.localfileName  = "send.png";
   itemDetail.localfilePath  = ".";
   itemDetail2.handle        = aliceTransferItem2;
   itemDetail2.localfileName = "send2.png";
   itemDetail2.localfilePath = ".";
   items.push_back( itemDetail );
   items.push_back( itemDetail2 );

   alice.fileTransferManager->configureFileTransferItems( aliceTransfer, items );
   alice.fileTransferManager->addParticipant( aliceTransfer, bob.config.uri().c_str() );
   alice.fileTransferManager->start( aliceTransfer );

   // Overview of Bob's thread:
   //  - 
   auto bobEvents = std::async(std::launch::async, [&] () {
      SipFileTransferHandle bobTransfer = 0;
      {
         SipFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( bob.fileTransferEvents, "SipFileTransferHandler::onNewFileTransfer",
            5000, AlwaysTruePred(), h, evt ));
         bobTransfer = h;
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
      }

      // Bob provisionally accepts the file transfer (sends a 180 to Alice)
      bob.fileTransferManager->provisionalAccept(bobTransfer);

      SipFileTransferItems items;
      {
         SipFileTransferHandle h;
         FileTransferConfiguredEvent evt;
         ASSERT_TRUE(cpcExpectEvent( bob.fileTransferEvents, "SipFileTransferHandler::onFileTransferConfigured",
            5000, AlwaysTruePred(), h, evt ));
         bobTransfer = h;
         items = evt.fileItems;
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         ASSERT_EQ(evt.fileItems.size(), 2);
      }

      // Bob then proceeds to ask the user what should be the filename and location
      // and whether or not they want to accept etc. This is then "configured".
      items[ 0 ].acceptedState = ftitem_accepted;
      items[ 0 ].localfileName = "recv.png";
      items[ 1 ].acceptedState = ftitem_accepted;
      items[ 1 ].localfileName = "recv2.png";
      bob.fileTransferManager->configureFileTransferItems( bobTransfer, items );

      // The file transfer is then accepted
      bob.fileTransferManager->accept( bobTransfer );

      {
         // Wait for the transfer to be finished (from Alice's side)
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcWaitForEvent( bob.fileTransferEvents, "SipFileTransferHandler::onFileTransferItemEnded",
            600000, AlwaysTruePred(), bobTransfer, ftie ));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

         // Should be two of these events (one per file)
         ASSERT_TRUE(cpcWaitForEvent( bob.fileTransferEvents, "SipFileTransferHandler::onFileTransferItemEnded",
            600000, AlwaysTruePred(), bobTransfer, ftie ));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( bob.fileTransferEvents, "SipFileTransferHandler::onFileTransferEnded",
            5000, AlwaysTruePred(), bobTransfer, fte ));
      };
   });

   // Overview of Alice's thread:
   //  - 
   auto aliceEvents = std::async(std::launch::async, [&] () {

      {
         SipFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( alice.fileTransferEvents, "SipFileTransferHandler::onNewFileTransfer",
            5000, AlwaysTruePred(), h, evt ));
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
      }

      SipFileTransferItems items;
      {
         SipFileTransferHandle h;
         FileTransferConfiguredEvent evt;
         ASSERT_TRUE(cpcExpectEvent( alice.fileTransferEvents, "SipFileTransferHandler::onFileTransferConfigured",
            5000, AlwaysTruePred(), h, evt ));
         items = evt.fileItems;
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
         ASSERT_EQ(evt.fileItems.size(), 2);
      }

      ASSERT_EQ( items[ 0 ].localfileName, "send.png" );
      ASSERT_EQ( items[ 1 ].localfileName, "send2.png" );

      {
         // Wait for the transfer of the items to finish (two of them)
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcWaitForEvent( alice.fileTransferEvents, "SipFileTransferHandler::onFileTransferItemEnded",
            600000, AlwaysTruePred(), aliceTransfer, ftie ));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

         ASSERT_TRUE(cpcWaitForEvent( alice.fileTransferEvents, "SipFileTransferHandler::onFileTransferItemEnded",
            600000, AlwaysTruePred(), aliceTransfer, ftie ));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

         // end the file transfer session
         alice.fileTransferManager->end( aliceTransfer );

         // Wait for the transfer to finish
         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( alice.fileTransferEvents, "SipFileTransferHandler::onFileTransferEnded",
         5000, AlwaysTruePred(), aliceTransfer, fte ));
      };
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(10000));
}

// Test works against local SIP servers but will have problems running against NewPace etc
// servers due to timing and/or message delivery order differences.
TEST_F(FileTransferTest, DISABLED_SendTwoFilesTwoINVITEs) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
   SipFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
   SipFileTransferHandle aliceTransfer2 = alice.fileTransferManager->createFileTransfer(alice.handle);
   SipFileTransferItemHandle aliceTransferItem = alice.fileTransferManager->createFileTransferItem( alice.handle );
   SipFileTransferItemHandle aliceTransferItem2 = alice.fileTransferManager->createFileTransferItem( alice.handle );

   // Setup the file transfer items
   SipFileTransferItems items;
   SipFileTransferItemDetail itemDetail;
   itemDetail.handle         = aliceTransferItem;
   itemDetail.localfileName  = "send.png";
   itemDetail.localfilePath  = ".";
   items.push_back( itemDetail );
   alice.fileTransferManager->configureFileTransferItems( aliceTransfer, items );
   alice.fileTransferManager->addParticipant( aliceTransfer, bob.config.uri().c_str() );

   items.clear();
   itemDetail.handle         = aliceTransferItem2;
   itemDetail.localfileName  = "send2.png";
   itemDetail.localfilePath  = ".";
   items.push_back( itemDetail );
   alice.fileTransferManager->configureFileTransferItems( aliceTransfer2, items );
   alice.fileTransferManager->addParticipant( aliceTransfer2, bob.config.uri().c_str() );

   // Make 2 calls
   alice.fileTransferManager->start( aliceTransfer );
   std::this_thread::sleep_for(std::chrono::milliseconds(500)); // to help the timing
   alice.fileTransferManager->start( aliceTransfer2 );

   // Overview of Bob's thread:
   //  - 
   auto bobEvents = std::async(std::launch::async, [&] () {
      SipFileTransferHandle bobTransfer = 0;
      {
         SipFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( bob.fileTransferEvents, "SipFileTransferHandler::onNewFileTransfer",
            5000, AlwaysTruePred(), h, evt ));
         bobTransfer = h;
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
      }

      // Bob provisionally accepts the file transfer (sends a 180 to Alice)
      bob.fileTransferManager->provisionalAccept(bobTransfer);

      SipFileTransferItems items;
      {
         SipFileTransferHandle h;
         FileTransferConfiguredEvent evt;
         ASSERT_TRUE(cpcExpectEvent( bob.fileTransferEvents, "SipFileTransferHandler::onFileTransferConfigured",
            5000, AlwaysTruePred(), h, evt ));
         items = evt.fileItems;
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         ASSERT_EQ(evt.fileItems.size(), 1);
      }

      SipFileTransferHandle bobTransfer2 = 0;
      {
         SipFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( bob.fileTransferEvents, "SipFileTransferHandler::onNewFileTransfer",
            5000, AlwaysTruePred(), h, evt ));
         bobTransfer2 = h;
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
      }

      // Bob provisionally accepts the file transfer (sends a 180 to Alice)
      bob.fileTransferManager->provisionalAccept(bobTransfer2);

      SipFileTransferItems items2;
      {
         SipFileTransferHandle h;
         FileTransferConfiguredEvent evt;
         ASSERT_TRUE(cpcExpectEvent( bob.fileTransferEvents, "SipFileTransferHandler::onFileTransferConfigured",
            5000, AlwaysTruePred(), h, evt ));
         items2 = evt.fileItems;
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         ASSERT_EQ(evt.fileItems.size(), 1);
      }

      // Bob then proceeds to ask the user what should be the filename and location
      // and whether or not they want to accept etc. This is then "configured".
      items[ 0 ].acceptedState = ftitem_accepted;
      items[ 0 ].localfileName = "recv.png";
      items2[ 0 ].acceptedState = ftitem_accepted;
      items2[ 0 ].localfileName = "recv2.png";

      bob.fileTransferManager->configureFileTransferItems( bobTransfer, items );
      std::this_thread::sleep_for(std::chrono::milliseconds(500)); // to help the timing
      bob.fileTransferManager->configureFileTransferItems( bobTransfer2, items2 );

      // The file transfer is then accepted
      bob.fileTransferManager->accept( bobTransfer );
      std::this_thread::sleep_for(std::chrono::milliseconds(500)); // to help the timing
      bob.fileTransferManager->accept( bobTransfer2 );

      {
         // Wait for the transfer to be finished (from Alice's side)
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcWaitForEvent( bob.fileTransferEvents, "SipFileTransferHandler::onFileTransferItemEnded",
            600000, AlwaysTruePred(), bobTransfer, ftie ));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( bob.fileTransferEvents, "SipFileTransferHandler::onFileTransferEnded",
            10000, AlwaysTruePred(), bobTransfer, fte ));

         // Should be two of these events (one per file)
         ASSERT_TRUE(cpcWaitForEvent( bob.fileTransferEvents, "SipFileTransferHandler::onFileTransferItemEnded",
            600000, AlwaysTruePred(), bobTransfer2, ftie ));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

         ASSERT_TRUE(cpcExpectEvent( bob.fileTransferEvents, "SipFileTransferHandler::onFileTransferEnded",
            10000, AlwaysTruePred(), bobTransfer2, fte ));
      };
   });

   // Overview of Alice's thread:
   //  - 
   auto aliceEvents = std::async(std::launch::async, [&] () {

      {
         SipFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( alice.fileTransferEvents, "SipFileTransferHandler::onNewFileTransfer",
            5000, AlwaysTruePred(), h, evt ));
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
      }

      {
         SipFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( alice.fileTransferEvents, "SipFileTransferHandler::onNewFileTransfer",
            5000, AlwaysTruePred(), h, evt ));
         ASSERT_EQ(h, aliceTransfer2);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
      }

      SipFileTransferItems items;
      {
         SipFileTransferHandle h;
         FileTransferConfiguredEvent evt;
         ASSERT_TRUE(cpcExpectEvent( alice.fileTransferEvents, "SipFileTransferHandler::onFileTransferConfigured",
            5000, AlwaysTruePred(), h, evt ));
         items = evt.fileItems;
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
         ASSERT_EQ(evt.fileItems.size(), 1);
      }

      SipFileTransferItems items2;
      {
         SipFileTransferHandle h;
         FileTransferConfiguredEvent evt;
         ASSERT_TRUE(cpcExpectEvent( alice.fileTransferEvents, "SipFileTransferHandler::onFileTransferConfigured",
            5000, AlwaysTruePred(), h, evt ));
         items2 = evt.fileItems;
         ASSERT_EQ(h, aliceTransfer2);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
         ASSERT_EQ(evt.fileItems.size(), 1);
      }

      ASSERT_EQ( items[ 0 ].localfileName, "send.png" );
      ASSERT_EQ( items2[ 0 ].localfileName, "send2.png" );

      {
         // Wait for the transfer of the items to finish (two of them)
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcWaitForEvent( alice.fileTransferEvents, "SipFileTransferHandler::onFileTransferItemEnded",
            600000, AlwaysTruePred(), aliceTransfer, ftie ));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

         // end the file transfer session
         alice.fileTransferManager->end( aliceTransfer );

         // Wait for the transfer to finish
         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( alice.fileTransferEvents, "SipFileTransferHandler::onFileTransferEnded",
         10000, AlwaysTruePred(), aliceTransfer, fte ));

         ASSERT_TRUE(cpcWaitForEvent( alice.fileTransferEvents, "SipFileTransferHandler::onFileTransferItemEnded",
            600000, AlwaysTruePred(), aliceTransfer2, ftie ));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

         // end the file transfer session
         alice.fileTransferManager->end( aliceTransfer2 );

         ASSERT_TRUE(cpcExpectEvent( alice.fileTransferEvents, "SipFileTransferHandler::onFileTransferEnded",
         10000, AlwaysTruePred(), aliceTransfer2, fte ));
      };
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(100000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(100000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(10000));
}

// test case for performing paralell file transfers at the same time
TEST_F(FileTransferTest, MultiFileTransfer) {
   TestAccount alice("alice");
   TestAccount bob("bob");
   TestAccount alice2("alice2");
   TestAccount bob2("bob2");

   // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
   SipFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
   SipFileTransferHandle alice2Transfer = alice2.fileTransferManager->createFileTransfer(alice2.handle);
   SipFileTransferItemHandle aliceTransferItem = alice.fileTransferManager->createFileTransferItem( alice.handle );
   SipFileTransferItemHandle alice2TransferItem = alice2.fileTransferManager->createFileTransferItem( alice2.handle );

   // configure the items
   SipFileTransferItems items;
   SipFileTransferItemDetail itemDetail;
   itemDetail.handle        = aliceTransferItem;
   itemDetail.localfileName = "send.png";
   itemDetail.localfilePath = ".";
   items.push_back( itemDetail );
   alice.fileTransferManager->configureFileTransferItems( aliceTransfer, items );

   items.clear();
   itemDetail.handle        = alice2TransferItem;
   itemDetail.localfileName = "send2.png";
   itemDetail.localfilePath = ".";
   items.push_back( itemDetail );
   alice2.fileTransferManager->configureFileTransferItems( alice2Transfer, items );

   // start the file transfers
   alice.fileTransferManager->addParticipant( aliceTransfer, bob.config.uri().c_str() );
   alice2.fileTransferManager->addParticipant( alice2Transfer, bob2.config.uri().c_str() );
   alice.fileTransferManager->start( aliceTransfer );
   alice2.fileTransferManager->start( alice2Transfer );

   // Overview of Bob's thread:
   //  - 
   auto bobEvents = std::async(std::launch::async, [&] () {
      SipFileTransferHandle bobTransfer = 0;
      {
         SipFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( bob.fileTransferEvents, "SipFileTransferHandler::onNewFileTransfer",
            5000, AlwaysTruePred(), h, evt ));
         bobTransfer = h;
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
      }

      // Bob provisionally accepts the file transfer (sends a 180 to Alice)
      bob.fileTransferManager->provisionalAccept(bobTransfer);

      SipFileTransferItems items;
      {
         SipFileTransferHandle h;
         FileTransferConfiguredEvent evt;
         ASSERT_TRUE(cpcExpectEvent( bob.fileTransferEvents, "SipFileTransferHandler::onFileTransferConfigured",
            5000, AlwaysTruePred(), h, evt ));
         items = evt.fileItems;
         ASSERT_EQ(bobTransfer, h);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         ASSERT_EQ(items.size(), 1);
      }

      // Bob reconfigures the filename and accepts
      items[ 0 ].acceptedState = ftitem_accepted;
      items[ 0 ].localfileName = "recv.png";
      bob.fileTransferManager->configureFileTransferItems( bobTransfer, items );
      bob.fileTransferManager->accept( bobTransfer );

      {
         // Watch the progress
         FileTransferItemProgressEvent evt;
         evt.percent = 0;
         evt.fileTransferItem = 0;

         while( evt.percent < 100 )
         {
            ASSERT_TRUE(cpcExpectEvent( bob.fileTransferEvents, "SipFileTransferHandler::onFileTransferItemProgress",
               5000, AlwaysTruePred(), bobTransfer, evt));
         }
      }
      
      {
         // Wait for the transfer to finish (it will be disconnected from Alice's side)
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcExpectEvent( bob.fileTransferEvents, "SipFileTransferHandler::onFileTransferItemEnded",
         5000, AlwaysTruePred(), bobTransfer, ftie ));

         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( bob.fileTransferEvents, "SipFileTransferHandler::onFileTransferEnded",
         5000, AlwaysTruePred(), bobTransfer, fte ));
      };
   });

   // Overview of Bob2's thread:
   //  - 
   auto bob2Events = std::async(std::launch::async, [&] () {
      SipFileTransferHandle bob2Transfer = 0;
      {
         SipFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( bob2.fileTransferEvents, "SipFileTransferHandler::onNewFileTransfer",
            5000, AlwaysTruePred(), h, evt ));
         bob2Transfer = h;
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice2.config.uri());
      }

      // Bob provisionally accepts the file transfer (sends a 180 to Alice)
      bob2.fileTransferManager->provisionalAccept(bob2Transfer);

      SipFileTransferItems items;
      {
         SipFileTransferHandle h;
         FileTransferConfiguredEvent evt;
         ASSERT_TRUE(cpcExpectEvent( bob2.fileTransferEvents, "SipFileTransferHandler::onFileTransferConfigured",
            5000, AlwaysTruePred(), h, evt ));
         items = evt.fileItems;
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
      }

      // Bob reconfigures the filename and accepts
      items[ 0 ].acceptedState = ftitem_accepted;
      items[ 0 ].localfileName = "recv2.png";
      bob2.fileTransferManager->configureFileTransferItems( bob2Transfer, items );
      bob2.fileTransferManager->accept( bob2Transfer );

      {
         // Watch the progress
         FileTransferItemProgressEvent evt;
         evt.percent = 0;
         evt.fileTransferItem = 0;

         while( evt.percent < 100 )
         {
            ASSERT_TRUE(cpcExpectEvent( bob2.fileTransferEvents, "SipFileTransferHandler::onFileTransferItemProgress",
               5000, AlwaysTruePred(), bob2Transfer, evt));
         }
      }
      
      {
         // Wait for the transfer to finish (it will be disconnected from Alice's side)
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcExpectEvent( bob2.fileTransferEvents, "SipFileTransferHandler::onFileTransferItemEnded",
         5000, AlwaysTruePred(), bob2Transfer, ftie ));

         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( bob2.fileTransferEvents, "SipFileTransferHandler::onFileTransferEnded",
         5000, AlwaysTruePred(), bob2Transfer, fte ));
      };
   });

   // Overview of Alice's thread:
   //  - 
   auto aliceEvents = std::async(std::launch::async, [&] () {

      {
         SipFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( alice.fileTransferEvents, "SipFileTransferHandler::onNewFileTransfer",
            5000, AlwaysTruePred(), h, evt ));
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
      }

      {
         SipFileTransferHandle h;
         FileTransferConfiguredEvent evt;
         ASSERT_TRUE(cpcExpectEvent( alice.fileTransferEvents, "SipFileTransferHandler::onFileTransferConfigured",
            5000, AlwaysTruePred(), h, evt ));
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
         ASSERT_EQ(evt.fileItems.size(), 1);
      }

      {
         // Watch the progress
         FileTransferItemProgressEvent evt;
         evt.fileTransferItem = aliceTransferItem;
         evt.percent = 0;

         while( evt.percent < 100 )
         {
            ASSERT_TRUE(cpcExpectEvent( alice.fileTransferEvents, "SipFileTransferHandler::onFileTransferItemProgress",
               5000, AlwaysTruePred(), aliceTransfer, evt));
         }
      }

      {
         // Wait for the transfer to finish
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcExpectEvent( alice.fileTransferEvents, "SipFileTransferHandler::onFileTransferItemEnded",
         5000, AlwaysTruePred(), aliceTransfer, ftie ));

         // end the file transfer session
         alice.fileTransferManager->end( aliceTransfer );

         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( alice.fileTransferEvents, "SipFileTransferHandler::onFileTransferEnded",
         5000, AlwaysTruePred(), aliceTransfer, fte ));
      };
   });

   // Overview of Alice2's thread:
   //  - 
   auto alice2Events = std::async(std::launch::async, [&] () {

      {
         SipFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( alice2.fileTransferEvents, "SipFileTransferHandler::onNewFileTransfer",
            5000, AlwaysTruePred(), h, evt ));
         ASSERT_EQ(h, alice2Transfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
      }

      {
         SipFileTransferHandle h;
         FileTransferConfiguredEvent evt;
         ASSERT_TRUE(cpcExpectEvent( alice2.fileTransferEvents, "SipFileTransferHandler::onFileTransferConfigured",
            5000, AlwaysTruePred(), h, evt ));
         ASSERT_EQ(h, alice2Transfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
         ASSERT_EQ(evt.fileItems.size(), 1);
      }

      {
         // Watch the progress
         FileTransferItemProgressEvent evt;
         evt.fileTransferItem = alice2TransferItem;
         evt.percent = 0;

         while( evt.percent < 100 )
         {
            ASSERT_TRUE(cpcExpectEvent( alice2.fileTransferEvents, "SipFileTransferHandler::onFileTransferItemProgress",
               5000, AlwaysTruePred(), alice2Transfer, evt));
         }
      }

      {
         // Wait for the transfer to finish
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcExpectEvent( alice2.fileTransferEvents, "SipFileTransferHandler::onFileTransferItemEnded",
         5000, AlwaysTruePred(), alice2Transfer, ftie ));

         // end the file transfer session
         alice2.fileTransferManager->end( alice2Transfer );

         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( alice2.fileTransferEvents, "SipFileTransferHandler::onFileTransferEnded",
         5000, AlwaysTruePred(), alice2Transfer, fte ));
      };
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(bob2Events.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bob2Events.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
   ASSERT_EQ(alice2Events.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(alice2Events.get());

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(10000));
}

// test case for multiple sequential file transfers
TEST_F(FileTransferTest, MultiSequentialFileTransfer) {
   SipFileTransferHandle aliceTransfer;
   SipFileTransferItemHandle aliceTransferItem;
   TestAccount alice("alice");
   TestAccount bob("bob");

   // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
   aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
   aliceTransferItem = alice.fileTransferManager->createFileTransferItem( alice.handle );

   // configure the items
   SipFileTransferItems items;
   SipFileTransferItemDetail itemDetail;
   itemDetail.handle        = aliceTransferItem;
   itemDetail.localfileName = "send.png";
   itemDetail.localfilePath = ".";
   items.push_back( itemDetail );

   alice.fileTransferManager->configureFileTransferItems( aliceTransfer, items );
   alice.fileTransferManager->addParticipant( aliceTransfer, bob.config.uri().c_str() );
   alice.fileTransferManager->start( aliceTransfer );

   // Overview of Bob's thread:
   //  - 
   auto bobEvents = std::async(std::launch::async, [&] () {
      SipFileTransferHandle bobTransfer = 0;
      {
         SipFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( bob.fileTransferEvents, "SipFileTransferHandler::onNewFileTransfer",
            5000, AlwaysTruePred(), h, evt ));
         bobTransfer = h;
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
      }

      // Bob provisionally accepts the file transfer (sends a 180 to Alice)
      bob.fileTransferManager->provisionalAccept(bobTransfer);

      SipFileTransferItems items;
      {
         SipFileTransferHandle h;
         FileTransferConfiguredEvent evt;
         ASSERT_TRUE(cpcExpectEvent( bob.fileTransferEvents, "SipFileTransferHandler::onFileTransferConfigured",
            5000, AlwaysTruePred(), h, evt ));
         items = evt.fileItems;
         ASSERT_EQ(bobTransfer, h);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         ASSERT_EQ(evt.fileItems.size(), 1);
      }

      // Bob then proceeds to ask the user what should be the filename and location
      // and whether or not they want to accept etc. This is then "configured".
      items[ 0 ].acceptedState = ftitem_accepted;
      items[ 0 ].localfileName = "recv.png";
      bob.fileTransferManager->configureFileTransferItems( bobTransfer, items );

      // The file transfer is then accepted
      bob.fileTransferManager->accept( bobTransfer );

      {
         // Watch the progress
         FileTransferItemProgressEvent evt;
         evt.percent = 0;
         evt.fileTransferItem = 0;

         while( evt.percent < 100 )
         {
            ASSERT_TRUE(cpcExpectEvent( bob.fileTransferEvents, "SipFileTransferHandler::onFileTransferItemProgress",
               5000, AlwaysTruePred(), bobTransfer, evt));
         }
      }
      
      {
         // Wait for the transfer to finish (it will be disconnected from Alice's side)
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcExpectEvent( bob.fileTransferEvents, "SipFileTransferHandler::onFileTransferItemEnded",
         5000, AlwaysTruePred(), bobTransfer, ftie ));

         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( bob.fileTransferEvents, "SipFileTransferHandler::onFileTransferEnded",
         5000, AlwaysTruePred(), bobTransfer, fte ));
      };
   });

   // Overview of Alice's thread:
   //  - 
   auto aliceEvents = std::async(std::launch::async, [&] () {

      {
         SipFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( alice.fileTransferEvents, "SipFileTransferHandler::onNewFileTransfer",
            5000, AlwaysTruePred(), h, evt ));
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
      }

      SipFileTransferItems items;
      {
         SipFileTransferHandle h;
         FileTransferConfiguredEvent evt;
         ASSERT_TRUE(cpcExpectEvent( alice.fileTransferEvents, "SipFileTransferHandler::onFileTransferConfigured",
            5000, AlwaysTruePred(), h, evt ));
         items = evt.fileItems;
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
         ASSERT_EQ(evt.fileItems.size(), 1);
      }

      ASSERT_EQ( items[ 0 ].localfileName, "send.png" );

      {
         // Watch the progress
         FileTransferItemProgressEvent evt;
         evt.fileTransferItem = aliceTransferItem;
         evt.percent = 0;

         while( evt.percent < 100 )
         {
            ASSERT_TRUE(cpcExpectEvent( alice.fileTransferEvents, "SipFileTransferHandler::onFileTransferItemProgress",
               5000, AlwaysTruePred(), aliceTransfer, evt));
         }
      }

      {
         // Wait for the transfer to finish
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcExpectEvent( alice.fileTransferEvents, "SipFileTransferHandler::onFileTransferItemEnded",
         5000, AlwaysTruePred(), aliceTransfer, ftie ));

         // end the file transfer session
         alice.fileTransferManager->end( aliceTransfer );

         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( alice.fileTransferEvents, "SipFileTransferHandler::onFileTransferEnded",
         5000, AlwaysTruePred(), aliceTransfer, fte ));
      };
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   // NOW: do it all over again using the same alice and bob, and a different file.

   // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
   aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
   aliceTransferItem = alice.fileTransferManager->createFileTransferItem( alice.handle );

   items.clear();
   itemDetail.handle = aliceTransferItem;
   itemDetail.localfileName = "send2.png";
   itemDetail.localfilePath = ".";
   items.push_back( itemDetail );

   alice.fileTransferManager->configureFileTransferItems( aliceTransfer, items );
   alice.fileTransferManager->addParticipant( aliceTransfer, bob.config.uri().c_str() );
   alice.fileTransferManager->start( aliceTransfer );

   // Overview of Bob's thread:
   //  - 
   bobEvents = std::async(std::launch::async, [&] () {
      SipFileTransferHandle bobTransfer = 0;

      {
         SipFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( bob.fileTransferEvents, "SipFileTransferHandler::onNewFileTransfer",
            5000, AlwaysTruePred(), h, evt ));
         bobTransfer = h;
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
      }

      // Bob provisionally accepts the file transfer (sends a 180 to Alice)
      bob.fileTransferManager->provisionalAccept(bobTransfer);

      SipFileTransferItems items;
      {
         SipFileTransferHandle h;
         FileTransferConfiguredEvent evt;
         ASSERT_TRUE(cpcExpectEvent( bob.fileTransferEvents, "SipFileTransferHandler::onFileTransferConfigured",
            5000, AlwaysTruePred(), h, evt ));
         items = evt.fileItems;
         ASSERT_EQ(bobTransfer, h);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         ASSERT_EQ(evt.fileItems.size(), 1);
      }

      // Bob then proceeds to ask the user what should be the filename and location
      // and whether or not they want to accept etc. This is then "configured".
      items[ 0 ].acceptedState = ftitem_accepted;
      items[ 0 ].localfileName = "recv2.png";
      bob.fileTransferManager->configureFileTransferItems( bobTransfer, items );

      // The file transfer is then accepted
      bob.fileTransferManager->accept( bobTransfer );

      {
         // Watch the progress
         FileTransferItemProgressEvent evt;
         evt.percent = 0;
         evt.fileTransferItem = 0;

         while( evt.percent < 100 )
         {
            ASSERT_TRUE(cpcExpectEvent( bob.fileTransferEvents, "SipFileTransferHandler::onFileTransferItemProgress",
               5000, AlwaysTruePred(), bobTransfer, evt));
         }
      }
      
      {
         // Wait for the transfer to finish (it will be disconnected from Alice's side)
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcExpectEvent( bob.fileTransferEvents, "SipFileTransferHandler::onFileTransferItemEnded",
         5000, AlwaysTruePred(), bobTransfer, ftie ));

         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( bob.fileTransferEvents, "SipFileTransferHandler::onFileTransferEnded",
         5000, AlwaysTruePred(), bobTransfer, fte ));
      };
   });

   // Overview of Alice's thread:
   //  - 
   aliceEvents = std::async(std::launch::async, [&] () {

      {
         SipFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( alice.fileTransferEvents, "SipFileTransferHandler::onNewFileTransfer",
            5000, AlwaysTruePred(), h, evt ));
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
      }

      SipFileTransferItems items;
      {
         SipFileTransferHandle h;
         FileTransferConfiguredEvent evt;
         ASSERT_TRUE(cpcExpectEvent( alice.fileTransferEvents, "SipFileTransferHandler::onFileTransferConfigured",
            5000, AlwaysTruePred(), h, evt ));
         items = evt.fileItems;
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
         ASSERT_EQ(evt.fileItems.size(), 1);
      }

      ASSERT_EQ( items[ 0 ].localfileName, "send2.png" );

      {
         // Watch the progress
         FileTransferItemProgressEvent evt;
         evt.fileTransferItem = aliceTransferItem;
         evt.percent = 0;

         while( evt.percent < 100 )
         {
            ASSERT_TRUE(cpcExpectEvent( alice.fileTransferEvents, "SipFileTransferHandler::onFileTransferItemProgress",
               5000, AlwaysTruePred(), aliceTransfer, evt));
         }
      }

      {
         // Wait for the transfer to finish
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcExpectEvent( alice.fileTransferEvents, "SipFileTransferHandler::onFileTransferItemEnded",
         5000, AlwaysTruePred(), aliceTransfer, ftie ));

         // end the file transfer session
         alice.fileTransferManager->end( aliceTransfer );

         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( alice.fileTransferEvents, "SipFileTransferHandler::onFileTransferEnded",
         5000, AlwaysTruePred(), aliceTransfer, fte ));
      };
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(600000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(600000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());


   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(10000));
}

TEST_F(FileTransferTest, CancelFileTransfer) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
   SipFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
   SipFileTransferItemHandle aliceTransferItem = alice.fileTransferManager->createFileTransferItem( alice.handle );

   SipFileTransferItems items;
   SipFileTransferItemDetail detail;
   detail.handle        = aliceTransferItem;
   detail.localfileName = "send.png";
   detail.localfilePath = ".";
   items.push_back( detail );

   alice.fileTransferManager->configureFileTransferItems( aliceTransfer, items );
   alice.fileTransferManager->addParticipant( aliceTransfer, bob.config.uri().c_str() );
   alice.fileTransferManager->start( aliceTransfer );

   // Overview of Bob's thread:
   //  - 
   auto bobEvents = std::async(std::launch::async, [&] () {
      SipFileTransferHandle bobTransfer = 0;
      {
         SipFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( bob.fileTransferEvents, "SipFileTransferHandler::onNewFileTransfer",
            5000, AlwaysTruePred(), h, evt ));
         bobTransfer = h;
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
      }

      // Bob provisionally accepts the file transfer (sends a 180 to Alice)
      bob.fileTransferManager->provisionalAccept(bobTransfer);

      SipFileTransferItems items;
      {
         SipFileTransferHandle h;
         FileTransferConfiguredEvent evt;
         ASSERT_TRUE(cpcExpectEvent( bob.fileTransferEvents, "SipFileTransferHandler::onFileTransferConfigured",
            5000, AlwaysTruePred(), h, evt ));
         items = evt.fileItems;
         ASSERT_EQ(bobTransfer, h);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         ASSERT_EQ(evt.fileItems.size(), 1);
      }

      // Bob configures the items and accepts
      items[ 0 ].acceptedState = ftitem_accepted;
      items[ 0 ].localfileName = "recv.png";
      bob.fileTransferManager->configureFileTransferItems( bobTransfer, items );
      bob.fileTransferManager->accept( bobTransfer );

      {
         // Wait for the transfer to be canceled (from Alice's side)
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcWaitForEvent( bob.fileTransferEvents, "SipFileTransferHandler::onFileTransferItemEnded",
         10000, AlwaysTruePred(), bobTransfer, ftie ));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Interrupted);

         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( bob.fileTransferEvents, "SipFileTransferHandler::onFileTransferEnded",
         5000, AlwaysTruePred(), bobTransfer, fte ));
      };
   });

   // Overview of Alice's thread:
   //  - 
   auto aliceEvents = std::async(std::launch::async, [&] () {

      {
         SipFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( alice.fileTransferEvents, "SipFileTransferHandler::onNewFileTransfer",
            5000, AlwaysTruePred(), h, evt ));
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
      }

      {
         // Cancel the file transfer
         alice.fileTransferManager->cancelItem( aliceTransfer, aliceTransferItem );

         // Wait for the transfer of the item to finish
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcWaitForEvent( alice.fileTransferEvents, "SipFileTransferHandler::onFileTransferItemEnded",
         10000, AlwaysTruePred(), aliceTransfer, ftie ));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Interrupted);

         // end the file transfer session
         alice.fileTransferManager->end( aliceTransfer );

         // Wait for the transfer to finish
         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( alice.fileTransferEvents, "SipFileTransferHandler::onFileTransferEnded",
         5000, AlwaysTruePred(), aliceTransfer, fte ));
      };
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(10000));
}

TEST_F(FileTransferTest, RejectFileTransfer) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
   SipFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
   SipFileTransferItemHandle aliceTransferItem = alice.fileTransferManager->createFileTransferItem( alice.handle );
   
   // configure the items
   SipFileTransferItems items;
   SipFileTransferItemDetail itemDetail;
   itemDetail.handle        = aliceTransferItem;
   itemDetail.localfileName = "send.png";
   itemDetail.localfilePath = ".";
   items.push_back( itemDetail );
   alice.fileTransferManager->configureFileTransferItems( aliceTransfer, items );

   alice.fileTransferManager->addParticipant( aliceTransfer, bob.config.uri().c_str() );
   alice.fileTransferManager->start( aliceTransfer );

   // Overview of Bob's thread:
   //  - 
   auto bobEvents = std::async(std::launch::async, [&] () {
      SipFileTransferHandle bobTransfer = 0;
      {
         SipFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( bob.fileTransferEvents, "SipFileTransferHandler::onNewFileTransfer",
            5000, AlwaysTruePred(), h, evt ));
         bobTransfer = h;
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
      }

      // Bob provisionally accepts the file transfer (sends a 180 to Alice)
      bob.fileTransferManager->provisionalAccept(bobTransfer);

      SipFileTransferItems items;
      {
         SipFileTransferHandle h;
         FileTransferConfiguredEvent evt;
         ASSERT_TRUE(cpcExpectEvent( bob.fileTransferEvents, "SipFileTransferHandler::onFileTransferConfigured",
            5000, AlwaysTruePred(), h, evt ));
         ASSERT_EQ(bobTransfer, h);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         ASSERT_EQ(evt.fileItems.size(), 1);
      }

      // Instead of configuring the items and accepting, bob just rejects.
      bob.fileTransferManager->reject( bobTransfer, 404 );

      {
         // Wait for the item to be cancelled
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcExpectEvent( bob.fileTransferEvents, "SipFileTransferHandler::onFileTransferItemEnded",
         5000, AlwaysTruePred(), bobTransfer, ftie ));
         ASSERT_EQ( ftie.endReason, FileTransferItemEndReason_Interrupted );
      };

      {
         // Wait for the transfer to be finished
         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( bob.fileTransferEvents, "SipFileTransferHandler::onFileTransferEnded",
         5000, AlwaysTruePred(), bobTransfer, fte ));
      };
   });

   // Overview of Alice's thread:
   //  - 
   auto aliceEvents = std::async(std::launch::async, [&] () {

      {
         SipFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( alice.fileTransferEvents, "SipFileTransferHandler::onNewFileTransfer",
            5000, AlwaysTruePred(), h, evt ));
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
      }

      {
         // Wait for the item to be cancelled
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcExpectEvent( alice.fileTransferEvents, "SipFileTransferHandler::onFileTransferItemEnded",
         5000, AlwaysTruePred(), aliceTransfer, ftie ));
         ASSERT_EQ( ftie.endReason, FileTransferItemEndReason_Interrupted );
      };

      {
         // Wait for the transfer to finish (bob rejects)
         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( alice.fileTransferEvents, "SipFileTransferHandler::onFileTransferEnded",
         5000, AlwaysTruePred(), aliceTransfer, fte ));
      };
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(10000));
}

TEST_F(FileTransferTest, FileTransferNoFiles) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
   SipFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
   //SipFileTransferItemHandle aliceTransferItem = alice.fileTransferManager->createFileTransferItem( alice.handle, aliceTransfer, ".", "send.png" );
   alice.fileTransferManager->addParticipant( aliceTransfer, bob.config.uri().c_str() );
   alice.fileTransferManager->start( aliceTransfer );

   // Overview of Alice's thread:
   //  - 
   auto aliceEvents = std::async(std::launch::async, [&] () {

      {
         SipFileTransferHandle h;
         SipFileTransfer::ErrorEvent evt;
         ASSERT_TRUE(cpcExpectEvent( alice.fileTransferEvents, "SipFileTransferHandler::onError",
            5000, AlwaysTruePred(), h, evt ));
         ASSERT_EQ(h, aliceTransfer);
		 ASSERT_EQ(evt.errorText, "Cannot start file transfer. No files have been added");
      }
     });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(10000));
}
   
TEST_F(FileTransferTest, FileTransferRemoveHandler) {
   Phone* phone = Phone::create();
   phone->initialize(LicenseInfo(), (PhoneHandler*)NULL);

   CPCAPI2::SipAccount::SipAccountManager* acct = CPCAPI2::SipAccount::SipAccountManager::getInterface(phone);
   
   TestAccountConfig config("alice");
   CPCAPI2::SipAccount::SipAccountHandle accountHandle = acct->create(config.settings);
   
   class MySipFileTransferHandler : public SipFileTransferHandler
   {
   public:
      MySipFileTransferHandler() : receivedEvent(false) {}
      
      bool receivedEvent;
      
      int onNewFileTransfer(const SipFileTransferHandle& fileTransfer, const NewFileTransferEvent& event ) { return 0; }
      int onFileTransferConfigured(const SipFileTransferHandle& fileTransfer, const FileTransferConfiguredEvent& event ) { return 0; }
      int onFileTransferEnded(const SipFileTransferHandle& fileTransfer, const FileTransferEndedEvent& event ) { return 0; }
      int onFileTransferItemProgress(const SipFileTransferHandle& fileTransfer, const FileTransferItemProgressEvent& event ) { return 0; }
      int onFileTransferItemEnded(const SipFileTransferHandle& fileTransfer, const FileTransferItemEndedEvent& event ) { return 0; }
      int onError(const SipFileTransferHandle& fileTransfer, const CPCAPI2::SipFileTransfer::ErrorEvent& event ) { receivedEvent = true; return 0; }
   };
   
   
   MySipFileTransferHandler* handler = new MySipFileTransferHandler();
   SipFileTransferManager* fileTransferMgr = SipFileTransferManager::getInterface(phone);
   fileTransferMgr->setHandler(accountHandle, handler);
   acct->enable(accountHandle);
   
   SipFileTransferHandle fileTransferHandle = fileTransferMgr->createFileTransfer(accountHandle);
   ASSERT_EQ(kSuccess, fileTransferMgr->start(fileTransferHandle));
   
   auto start = std::chrono::high_resolution_clock::now();
   
   std::atomic_bool threadStopFlag(false);
   auto transferEvent = std::async(std::launch::async, [&] ()
   {
      while (handler->receivedEvent == false)
      {
         phone->process(CPCAPI2::Phone::kBlockingModeNonBlocking);

         if (threadStopFlag) return;

         std::this_thread::sleep_for(std::chrono::milliseconds(100));
      }
   });
   
   flaggableWaitFor(transferEvent, threadStopFlag);
   auto end = std::chrono::high_resolution_clock::now();
   
   fileTransferMgr->setHandler(accountHandle, NULL);
   delete handler;
   
   fileTransferHandle = fileTransferMgr->createFileTransfer(accountHandle);
   ASSERT_EQ(kSuccess, fileTransferMgr->start(fileTransferHandle));

   // wait about as long as it took before
   std::this_thread::sleep_for((end-start) * 2);
   
   phone->process(CPCAPI2::Phone::kBlockingModeNonBlocking);
   
   Phone::release(phone);
}
 
   
}

#endif // CPCAPI2_SIP_FILE_TRANSFER_MODULE

#endif // 0
