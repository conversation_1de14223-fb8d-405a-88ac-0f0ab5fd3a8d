#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#include "cpcapi2_test_fixture.h"

using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::SipEvent;
using namespace CPCAPI2::SipAccount;

namespace {

class EventSubscriptionTests : public CpcapiAutoTest
{
public:
   EventSubscriptionTests() {}
   virtual ~EventSubscriptionTests() {}
};

TEST_F(EventSubscriptionTests, BasicSubscribe) {
	TestAccount alice("alice");
   TestAccount bob("bob");

	// make an outgoing subscription from <PERSON> to <PERSON> using the demo.xten.com server
	SipEventSubscriptionHandle aliceSubs = alice.subs->createSubscription(alice.handle);
   SipEventSubscriptionSettings subsSettings;
   subsSettings.eventPackage = "cpcapi2test";
   subsSettings.expiresSeconds = 3600;
   subsSettings.supportedMimeTypes.push_back(CPCAPI2::MimeType("application", "cpcapi2test"));
   alice.subs->applySubscriptionSettings(aliceSubs, subsSettings);
	alice.subs->addParticipant(aliceSubs, bob.config.uri());
	alice.subs->start(aliceSubs);

	// Overview of Bob's thread:
	//  - 
	auto bobEvents = std::async(std::launch::async, [&] () {
		SipEventSubscriptionHandle bobSubs = 0;
      {
         SipEventSubscriptionHandle h;
		   NewSubscriptionEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(bob.subsEvents, 
			   "SipEventSubscriptionHandler::onNewSubscription", 
			   5000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, bob.handle);
         bobSubs = h;
			ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
			ASSERT_EQ(evt.remoteAddress, alice.config.uri());
			ASSERT_EQ(evt.remoteDisplayName, alice.config.name);
         ASSERT_EQ(evt.supportedMimeTypes.size(), 1);
      }

		// Bob accepts the subscription (200 OK) -- this should trigger state transitions for both Bob and Alice
      SipEventState evt1234;
      evt1234.contentUTF8 = "1,2,3,4\r\n";
      evt1234.contentLength = 9;
      evt1234.eventPackage = "cpcapi2test";
      evt1234.expiresTimeMs = 3598;
      evt1234.mimeType = "application";
      evt1234.mimeSubType = "cpcapi2test";
	  ASSERT_EQ(bob.subs->provisionalAccept(bobSubs, evt1234), kSuccess);
      {
			SipEventSubscriptionHandle h;
			SubscriptionStateChangedEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(bob.subsEvents, 
			   "SipEventSubscriptionHandler::onSubscriptionStateChanged",
			   15000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Pending);
      }
		ASSERT_EQ(bob.subs->accept(bobSubs, evt1234), kSuccess);
      {
			SipEventSubscriptionHandle h;
			SubscriptionStateChangedEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(bob.subsEvents, 
			   "SipEventSubscriptionHandler::onSubscriptionStateChanged",
			   15000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }


		std::this_thread::sleep_for(std::chrono::milliseconds(500));

      SipEventState evt5678;
      evt5678.contentUTF8 = "5,6,7,8\r\n";
      evt5678.contentLength = 9;
      evt5678.eventPackage = "cpcapi2test";
      evt5678.expiresTimeMs = 3598;
      evt5678.mimeType = "application";
      evt5678.mimeSubType = "cpcapi2test";
      ASSERT_EQ(bob.subs->notify(bobSubs, evt5678), kSuccess);

		std::this_thread::sleep_for(std::chrono::milliseconds(500));

		ASSERT_EQ(bob.subs->end(bobSubs), kSuccess);
      {
			SipEventSubscriptionHandle h;
			SubscriptionEndedEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(bob.subsEvents, 
			   "SipEventSubscriptionHandler::onSubscriptionEnded",
			   5000,
            AlwaysTruePred(), h, evt));
			ASSERT_EQ(bobSubs, h);
			ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }
	});

	// Overview of Alice's thread:
	//  - 
	auto aliceEvents = std::async(std::launch::async, [&] () {
      {
			SipEventSubscriptionHandle h;
			NewSubscriptionEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.subsEvents, 
			   "SipEventSubscriptionHandler::onNewSubscription", 
			   15000,
			   AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, alice.handle);
			ASSERT_EQ(h, aliceSubs);
			ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Outgoing);
			ASSERT_EQ(evt.remoteAddress, bob.config.uri());
			ASSERT_EQ(evt.remoteDisplayName, "");
      }

	  {
			SipEventSubscriptionHandle h;
			SubscriptionStateChangedEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.subsEvents, 
			   "SipEventSubscriptionHandler::onSubscriptionStateChanged",
			   5000,
            AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
			ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Pending);
      }

      {
			SipEventSubscriptionHandle h;
			SubscriptionStateChangedEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.subsEvents, 
			   "SipEventSubscriptionHandler::onSubscriptionStateChanged",
			   5000,
            AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
			ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      {
			SipEventSubscriptionHandle h;
			IncomingEventStateEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.subsEvents, 
			   "SipEventSubscriptionHandler::onIncomingEventState",
			   5000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
         ASSERT_EQ(evt.eventState.contentLength, 9);
         //std::cout << "content is " << evt.eventState.content << std::endl;
         ASSERT_EQ(strcmp(evt.eventState.contentUTF8.c_str(), "1,2,3,4\r\n"), 0);
         ASSERT_EQ(evt.eventState.eventPackage, "cpcapi2test");
         ASSERT_NEAR(evt.eventState.expiresTimeMs, 3598, 10);
         ASSERT_EQ(evt.eventState.mimeType, "application");
         ASSERT_EQ(evt.eventState.mimeSubType, "cpcapi2test");
      }

      {
			SipEventSubscriptionHandle h;
			IncomingEventStateEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.subsEvents, 
			   "SipEventSubscriptionHandler::onIncomingEventState",
			   5000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
         ASSERT_EQ(evt.eventState.contentLength, 9);
         //std::cout << "content is " << evt.eventState.content << std::endl;
         ASSERT_EQ(strcmp(evt.eventState.contentUTF8.c_str(), "5,6,7,8\r\n"), 0);
         ASSERT_EQ(evt.eventState.eventPackage, "cpcapi2test");
         ASSERT_NEAR(evt.eventState.expiresTimeMs, 3598, 10);
         ASSERT_EQ(evt.eventState.mimeType, "application");
         ASSERT_EQ(evt.eventState.mimeSubType, "cpcapi2test");
      }

      {
			SipEventSubscriptionHandle h;
			SubscriptionEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.subsEvents, 
			   "SipEventSubscriptionHandler::onSubscriptionEnded",
			   15000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
			ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }

	});

	// std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
	ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
	ASSERT_NO_THROW(bobEvents.get());
	ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
	ASSERT_NO_THROW(aliceEvents.get());

	// not needed, but handy sometimes when debugging ...
	//std::this_thread::sleep_for(std::chrono::milliseconds(10000));
}

TEST_F(EventSubscriptionTests, BasicSubscribeDoubleStart) {
	TestAccount alice("alice");
   TestAccount bob("bob");

	// make an outgoing subscription from Alice to Bob using the demo.xten.com server
	SipEventSubscriptionHandle aliceSubs = alice.subs->createSubscription(alice.handle);
   SipEventSubscriptionSettings subsSettings;
   subsSettings.eventPackage = "cpcapi2test";
   subsSettings.expiresSeconds = 3600;
   subsSettings.supportedMimeTypes.push_back(CPCAPI2::MimeType("application", "cpcapi2test"));
   alice.subs->applySubscriptionSettings(aliceSubs, subsSettings);
	alice.subs->addParticipant(aliceSubs, bob.config.uri());
   
	alice.subs->start(aliceSubs);
   alice.subs->start(aliceSubs); // seen in OBELISK-2969; double start caused crash

	// Overview of Bob's thread:
	//  - 
	auto bobEvents = std::async(std::launch::async, [&] () {
		SipEventSubscriptionHandle bobSubs = 0;
      {
         SipEventSubscriptionHandle h;
		   NewSubscriptionEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(bob.subsEvents, 
			   "SipEventSubscriptionHandler::onNewSubscription", 
			   5000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, bob.handle);
         bobSubs = h;
			ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
			ASSERT_EQ(evt.remoteAddress, alice.config.uri());
			ASSERT_EQ(evt.remoteDisplayName, alice.config.name);
         ASSERT_EQ(evt.supportedMimeTypes.size(), 1);
      }

		// Bob accepts the subscription (200 OK) -- this should trigger state transitions for both Bob and Alice
      SipEventState evt1234;
      evt1234.contentUTF8 = "1,2,3,4\r\n";
      evt1234.contentLength = 9;
      evt1234.eventPackage = "cpcapi2test";
      evt1234.expiresTimeMs = 3598;
      evt1234.mimeType = "application";
      evt1234.mimeSubType = "cpcapi2test";
	  ASSERT_EQ(bob.subs->provisionalAccept(bobSubs, evt1234), kSuccess);
      {
			SipEventSubscriptionHandle h;
			SubscriptionStateChangedEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(bob.subsEvents, 
			   "SipEventSubscriptionHandler::onSubscriptionStateChanged",
			   15000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Pending);
      }
		ASSERT_EQ(bob.subs->accept(bobSubs, evt1234), kSuccess);
      {
			SipEventSubscriptionHandle h;
			SubscriptionStateChangedEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(bob.subsEvents, 
			   "SipEventSubscriptionHandler::onSubscriptionStateChanged",
			   15000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }


		std::this_thread::sleep_for(std::chrono::milliseconds(500));

      SipEventState evt5678;
      evt5678.contentUTF8 = "5,6,7,8\r\n";
      evt5678.contentLength = 9;
      evt5678.eventPackage = "cpcapi2test";
      evt5678.expiresTimeMs = 3598;
      evt5678.mimeType = "application";
      evt5678.mimeSubType = "cpcapi2test";
      ASSERT_EQ(bob.subs->notify(bobSubs, evt5678), kSuccess);

		std::this_thread::sleep_for(std::chrono::milliseconds(500));

		ASSERT_EQ(bob.subs->end(bobSubs), kSuccess);
      {
			SipEventSubscriptionHandle h;
			SubscriptionEndedEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(bob.subsEvents, 
			   "SipEventSubscriptionHandler::onSubscriptionEnded",
			   5000,
            AlwaysTruePred(), h, evt));
			ASSERT_EQ(bobSubs, h);
			ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }
	});

	// Overview of Alice's thread:
	//  - 
	auto aliceEvents = std::async(std::launch::async, [&] () {
      {
			SipEventSubscriptionHandle h;
			NewSubscriptionEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.subsEvents, 
			   "SipEventSubscriptionHandler::onNewSubscription", 
			   15000,
			   AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, alice.handle);
			ASSERT_EQ(h, aliceSubs);
			ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Outgoing);
			ASSERT_EQ(evt.remoteAddress, bob.config.uri());
			ASSERT_EQ(evt.remoteDisplayName, "");
      }

	  {
			SipEventSubscriptionHandle h;
			SubscriptionStateChangedEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.subsEvents, 
			   "SipEventSubscriptionHandler::onSubscriptionStateChanged",
			   5000,
            AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
			ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Pending);
      }

      {
			SipEventSubscriptionHandle h;
			SubscriptionStateChangedEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.subsEvents, 
			   "SipEventSubscriptionHandler::onSubscriptionStateChanged",
			   5000,
            AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
			ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      {
			SipEventSubscriptionHandle h;
			IncomingEventStateEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.subsEvents, 
			   "SipEventSubscriptionHandler::onIncomingEventState",
			   5000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
         ASSERT_EQ(evt.eventState.contentLength, 9);
         //std::cout << "content is " << evt.eventState.content << std::endl;
         ASSERT_EQ(strcmp(evt.eventState.contentUTF8.c_str(), "1,2,3,4\r\n"), 0);
         ASSERT_EQ(evt.eventState.eventPackage, "cpcapi2test");
         ASSERT_NEAR(evt.eventState.expiresTimeMs, 3598, 10);
         ASSERT_EQ(evt.eventState.mimeType, "application");
         ASSERT_EQ(evt.eventState.mimeSubType, "cpcapi2test");
      }

      {
			SipEventSubscriptionHandle h;
			IncomingEventStateEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.subsEvents, 
			   "SipEventSubscriptionHandler::onIncomingEventState",
			   5000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
         ASSERT_EQ(evt.eventState.contentLength, 9);
         //std::cout << "content is " << evt.eventState.content << std::endl;
         ASSERT_EQ(strcmp(evt.eventState.contentUTF8.c_str(), "5,6,7,8\r\n"), 0);
         ASSERT_EQ(evt.eventState.eventPackage, "cpcapi2test");
         ASSERT_NEAR(evt.eventState.expiresTimeMs, 3598, 10);
         ASSERT_EQ(evt.eventState.mimeType, "application");
         ASSERT_EQ(evt.eventState.mimeSubType, "cpcapi2test");
      }

      {
			SipEventSubscriptionHandle h;
			SubscriptionEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.subsEvents, 
			   "SipEventSubscriptionHandler::onSubscriptionEnded",
			   15000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
			ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }

	});

	// std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
	ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
	ASSERT_NO_THROW(bobEvents.get());
	ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
	ASSERT_NO_THROW(aliceEvents.get());

	// not needed, but handy sometimes when debugging ...
	//std::this_thread::sleep_for(std::chrono::milliseconds(10000));
}

TEST_F(EventSubscriptionTests, ClientSubscribeExpires) {
	TestAccount alice("alice");
   TestAccount bob("bob");

#define shortExpires 10

	// make an outgoing subscription from Alice to Bob using the demo.xten.com server
	SipEventSubscriptionHandle aliceSubs = alice.subs->createSubscription(alice.handle);
   SipEventSubscriptionSettings subsSettings;
   subsSettings.eventPackage = "cpcapi2test";
   subsSettings.expiresSeconds = shortExpires;
   subsSettings.supportedMimeTypes.push_back(CPCAPI2::MimeType("application", "cpcapi2test"));
   alice.subs->applySubscriptionSettings(aliceSubs, subsSettings);
	alice.subs->addParticipant(aliceSubs, bob.config.uri());
	alice.subs->start(aliceSubs);

	// Overview of Bob's thread:
	//  - 
	auto bobEvents = std::async(std::launch::async, [&] () {
		SipEventSubscriptionHandle bobSubs = 0;
      {
			SipEventSubscriptionHandle h;
			NewSubscriptionEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(bob.subsEvents, 
			   "SipEventSubscriptionHandler::onNewSubscription", 
			   5000,
			   AlwaysTruePred(), h, evt));
			bobSubs = h;
			ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
			ASSERT_EQ(evt.remoteAddress, alice.config.uri());
			ASSERT_EQ(evt.remoteDisplayName, alice.config.name);
      }


		// Bob accepts the subscription (200 OK) -- this should trigger state transitions for both Bob and Alice
      SipEventState evt1234;
      evt1234.contentUTF8 = "1,2,3,4\r\n";
      evt1234.contentLength = 9;
      evt1234.eventPackage = "cpcapi2test";
      evt1234.expiresTimeMs = 3598;
      evt1234.mimeType = "application";
      evt1234.mimeSubType = "cpcapi2test";
		ASSERT_EQ(bob.subs->accept(bobSubs, evt1234), kSuccess);

      {
			SipEventSubscriptionHandle h;
			SubscriptionStateChangedEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(bob.subsEvents, 
			   "SipEventSubscriptionHandler::onSubscriptionStateChanged",
			   15000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

		std::this_thread::sleep_for(std::chrono::milliseconds(15000));

      SipEventState evt5678;
      evt5678.contentUTF8 = "5,6,7,8\r\n";
      evt5678.contentLength = 9;
      evt5678.eventPackage = "cpcapi2test";
      evt5678.expiresTimeMs = 3598;
      evt5678.mimeType = "application";
      evt5678.mimeSubType = "cpcapi2test";
      ASSERT_EQ(bob.subs->notify(bobSubs, evt5678), kSuccess);

		std::this_thread::sleep_for(std::chrono::milliseconds(500));

		ASSERT_EQ(bob.subs->end(bobSubs), kSuccess);

      {
			SipEventSubscriptionHandle h;
			SubscriptionEndedEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(bob.subsEvents, 
			   "SipEventSubscriptionHandler::onSubscriptionEnded",
			   5000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(bobSubs, h);
			ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }
	});

	// Overview of Alice's thread:
	//  - 
	auto aliceEvents = std::async(std::launch::async, [&] () {
      {
			SipEventSubscriptionHandle h;
			NewSubscriptionEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.subsEvents, 
			   "SipEventSubscriptionHandler::onNewSubscription", 
			   15000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(h, aliceSubs);
			ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Outgoing);
			ASSERT_EQ(evt.remoteAddress, bob.config.uri());
			ASSERT_EQ(evt.remoteDisplayName, "");
      }

      {
			SipEventSubscriptionHandle h;
			SubscriptionStateChangedEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.subsEvents, 
			   "SipEventSubscriptionHandler::onSubscriptionStateChanged",
			   5000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
			ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      {
			SipEventSubscriptionHandle h;
			IncomingEventStateEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.subsEvents, 
			   "SipEventSubscriptionHandler::onIncomingEventState",
			   20000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
         ASSERT_EQ(evt.eventState.contentLength, 9);
         //std::cout << "content is " << evt.eventState.content << std::endl;
         ASSERT_EQ(strcmp(evt.eventState.contentUTF8.c_str(), "1,2,3,4\r\n"), 0);
         ASSERT_EQ(evt.eventState.eventPackage, "cpcapi2test");
         ASSERT_LE(evt.eventState.expiresTimeMs, shortExpires);
         ASSERT_EQ(evt.eventState.mimeType, "application");
         ASSERT_EQ(evt.eventState.mimeSubType, "cpcapi2test");
      }

      {
			SipEventSubscriptionHandle h;
			IncomingEventStateEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.subsEvents, 
			   "SipEventSubscriptionHandler::onIncomingEventState",
			   35000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
         ASSERT_EQ(evt.eventState.contentLength, 9);
         //std::cout << "content is " << evt.eventState.content << std::endl;
         ASSERT_EQ(strcmp(evt.eventState.contentUTF8.c_str(), "5,6,7,8\r\n"), 0);
         ASSERT_EQ(evt.eventState.eventPackage, "cpcapi2test");
         ASSERT_LE(evt.eventState.expiresTimeMs, shortExpires);
         ASSERT_EQ(evt.eventState.mimeType, "application");
         ASSERT_EQ(evt.eventState.mimeSubType, "cpcapi2test");
      }
      {
			SipEventSubscriptionHandle h;
			SubscriptionEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.subsEvents, 
			   "SipEventSubscriptionHandler::onSubscriptionEnded",
			   15000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
			ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }
	});

	// std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
	ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(35000)), std::future_status::ready);
	ASSERT_NO_THROW(bobEvents.get());
	ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(35000)), std::future_status::ready);
	ASSERT_NO_THROW(aliceEvents.get());

	// not needed, but handy sometimes when debugging ...
	//std::this_thread::sleep_for(std::chrono::milliseconds(10000));
}

TEST_F(EventSubscriptionTests, BasicSubscribeRefresh) {
	TestAccount alice("alice");
   TestAccount bob("bob");

	// make an outgoing subscription from Alice to Bob using the demo.xten.com server
	SipEventSubscriptionHandle aliceSubs = alice.subs->createSubscription(alice.handle);
   SipEventSubscriptionSettings subsSettings;
   subsSettings.eventPackage = "cpcapi2test";
   subsSettings.expiresSeconds = 3600;
   subsSettings.supportedMimeTypes.push_back(CPCAPI2::MimeType("application", "cpcapi2test"));
   alice.subs->applySubscriptionSettings(aliceSubs, subsSettings);
	alice.subs->addParticipant(aliceSubs, bob.config.uri());
   
	alice.subs->start(aliceSubs);

	// Overview of Bob's thread:
	//  - 
	auto bobEvents = std::async(std::launch::async, [&] () {
		SipEventSubscriptionHandle bobSubs = 0;
      {
         SipEventSubscriptionHandle h;
		   NewSubscriptionEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(bob.subsEvents,
			   "SipEventSubscriptionHandler::onNewSubscription", 
			   5000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, bob.handle);
         bobSubs = h;
			ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
			ASSERT_EQ(evt.remoteAddress, alice.config.uri());
			ASSERT_EQ(evt.remoteDisplayName, alice.config.name);
         ASSERT_EQ(evt.supportedMimeTypes.size(), 1);
      }

		// Bob accepts the subscription (200 OK) -- this should trigger state transitions for both Bob and Alice
      SipEventState evt1234;
      evt1234.contentUTF8 = "1,2,3,4\r\n";
      evt1234.contentLength = 9;
      evt1234.eventPackage = "cpcapi2test";
      evt1234.expiresTimeMs = 3598;
      evt1234.mimeType = "application";
      evt1234.mimeSubType = "cpcapi2test";
	  ASSERT_EQ(bob.subs->provisionalAccept(bobSubs, evt1234), kSuccess);
      {
			SipEventSubscriptionHandle h;
			SubscriptionStateChangedEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(bob.subsEvents,
			   "SipEventSubscriptionHandler::onSubscriptionStateChanged",
			   15000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Pending);
      }
		ASSERT_EQ(bob.subs->accept(bobSubs, evt1234), kSuccess);
      {
			SipEventSubscriptionHandle h;
			SubscriptionStateChangedEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(bob.subsEvents,
			   "SipEventSubscriptionHandler::onSubscriptionStateChanged",
			   15000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      // expecting a SUBSCRIBE refresh to come in around this time.
      // right now, there exists no public CPCAPI2 event for verifying this
		std::this_thread::sleep_for(std::chrono::milliseconds(2000));

      SipEventState evt5678;
      evt5678.contentUTF8 = "5,6,7,8\r\n";
      evt5678.contentLength = 9;
      evt5678.eventPackage = "cpcapi2test";
      evt5678.expiresTimeMs = 3598;
      evt5678.mimeType = "application";
      evt5678.mimeSubType = "cpcapi2test";
      ASSERT_EQ(bob.subs->notify(bobSubs, evt5678), kSuccess);


		ASSERT_EQ(bob.subs->end(bobSubs), kSuccess);
      {
			SipEventSubscriptionHandle h;
			SubscriptionEndedEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(bob.subsEvents,
			   "SipEventSubscriptionHandler::onSubscriptionEnded",
			   5000,
            AlwaysTruePred(), h, evt));
			ASSERT_EQ(bobSubs, h);
			ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }
	});

	// Overview of Alice's thread:
	//  - 
	auto aliceEvents = std::async(std::launch::async, [&] () {
      {
			SipEventSubscriptionHandle h;
			NewSubscriptionEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.subsEvents,
			   "SipEventSubscriptionHandler::onNewSubscription", 
			   15000,
			   AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, alice.handle);
			ASSERT_EQ(h, aliceSubs);
			ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Outgoing);
			ASSERT_EQ(evt.remoteAddress, bob.config.uri());
			ASSERT_EQ(evt.remoteDisplayName, "");
      }

	  {
			SipEventSubscriptionHandle h;
			SubscriptionStateChangedEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.subsEvents,
			   "SipEventSubscriptionHandler::onSubscriptionStateChanged",
			   5000,
            AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
			ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Pending);
      }

      {
			SipEventSubscriptionHandle h;
			SubscriptionStateChangedEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.subsEvents,
			   "SipEventSubscriptionHandler::onSubscriptionStateChanged",
			   5000,
            AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
			ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      {
			SipEventSubscriptionHandle h;
			IncomingEventStateEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.subsEvents,
			   "SipEventSubscriptionHandler::onIncomingEventState",
			   5000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
         ASSERT_EQ(evt.eventState.contentLength, 9);
         //std::cout << "content is " << evt.eventState.content << std::endl;
         ASSERT_EQ(strcmp(evt.eventState.contentUTF8.c_str(), "1,2,3,4\r\n"), 0);
         ASSERT_EQ(evt.eventState.eventPackage, "cpcapi2test");
         ASSERT_NEAR(evt.eventState.expiresTimeMs, 3598, 10);
         ASSERT_EQ(evt.eventState.mimeType, "application");
         ASSERT_EQ(evt.eventState.mimeSubType, "cpcapi2test");
      }
      
      alice.subs->refresh(aliceSubs);

      {
			SipEventSubscriptionHandle h;
			IncomingEventStateEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.subsEvents,
			   "SipEventSubscriptionHandler::onIncomingEventState",
			   5000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
         ASSERT_EQ(evt.eventState.contentLength, 9);
         //std::cout << "content is " << evt.eventState.content << std::endl;
         ASSERT_EQ(strcmp(evt.eventState.contentUTF8.c_str(), "5,6,7,8\r\n"), 0);
         ASSERT_EQ(evt.eventState.eventPackage, "cpcapi2test");
         ASSERT_NEAR(evt.eventState.expiresTimeMs, 3598, 10);
         ASSERT_EQ(evt.eventState.mimeType, "application");
         ASSERT_EQ(evt.eventState.mimeSubType, "cpcapi2test");
      }

      {
			SipEventSubscriptionHandle h;
			SubscriptionEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.subsEvents,
			   "SipEventSubscriptionHandler::onSubscriptionEnded",
			   15000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
			ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }

	});

	// std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
	ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
	ASSERT_NO_THROW(bobEvents.get());
	ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
	ASSERT_NO_THROW(aliceEvents.get());

	// not needed, but handy sometimes when debugging ...
	//std::this_thread::sleep_for(std::chrono::milliseconds(10000));
}

// First subscription accepted then terminated with reason=deactivated 
// which triggers a new automatic subscription which is then accepted then terminated normally
TEST_F(EventSubscriptionTests, NotifySubscriptionStateTerminatedWithDeactivatedReason) {
	TestAccount alice("alice");
   TestAccount bob("bob");

	// make an outgoing subscription from Alice to Bob using the demo.xten.com server
	SipEventSubscriptionHandle aliceSubs = alice.subs->createSubscription(alice.handle);
   SipEventSubscriptionSettings subsSettings;
   subsSettings.eventPackage = "cpcapi2test";
   subsSettings.expiresSeconds = 3600;
   subsSettings.supportedMimeTypes.push_back(CPCAPI2::MimeType("application", "cpcapi2test"));
   alice.subs->applySubscriptionSettings(aliceSubs, subsSettings);
	alice.subs->addParticipant(aliceSubs, bob.config.uri());
	alice.subs->start(aliceSubs);

	// Overview of Bob's thread:
	//  - 
	auto bobEvents = std::async(std::launch::async, [&] () {
		SipEventSubscriptionHandle bobSubs = 0;
      {
         SipEventSubscriptionHandle h;
		   NewSubscriptionEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(bob.subsEvents,
			   "SipEventSubscriptionHandler::onNewSubscription", 
			   5000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, bob.handle);
         bobSubs = h;
			ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
			ASSERT_EQ(evt.remoteAddress, alice.config.uri());
			ASSERT_EQ(evt.remoteDisplayName, alice.config.name);
         ASSERT_EQ(evt.supportedMimeTypes.size(), 1);
      }

		// Bob accepts the subscription (200 OK) -- this should trigger state transitions for both Bob and Alice
      SipEventState evt1234;
      evt1234.contentUTF8 = "1,2,3,4\r\n";
      evt1234.contentLength = 9;
      evt1234.eventPackage = "cpcapi2test";
      evt1234.expiresTimeMs = 3598;
      evt1234.mimeType = "application";
      evt1234.mimeSubType = "cpcapi2test";
		ASSERT_EQ(bob.subs->accept(bobSubs, evt1234), kSuccess);
      {
			SipEventSubscriptionHandle h;
			SubscriptionStateChangedEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(bob.subsEvents,
			   "SipEventSubscriptionHandler::onSubscriptionStateChanged",
			   15000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }


		std::this_thread::sleep_for(std::chrono::milliseconds(500));

      SipEventState evt5678;
      evt5678.contentUTF8 = "5,6,7,8\r\n";
      evt5678.contentLength = 9;
      evt5678.eventPackage = "cpcapi2test";
      evt5678.expiresTimeMs = 3598;
      evt5678.mimeType = "application";
      evt5678.mimeSubType = "cpcapi2test";
      ASSERT_EQ(bob.subs->notify(bobSubs, evt5678), kSuccess);

		std::this_thread::sleep_for(std::chrono::milliseconds(500));

      ASSERT_EQ(bob.subs->end(bobSubs, SipSubscriptionTerminateReason_Deactivate), kSuccess);
      {
			SipEventSubscriptionHandle h;
			SubscriptionEndedEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(bob.subsEvents,
			   "SipEventSubscriptionHandler::onSubscriptionEnded",
			   5000,
            AlwaysTruePred(), h, evt));
			ASSERT_EQ(bobSubs, h);
			ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }

      // Expect a new subscription
      {
         SipEventSubscriptionHandle h;
		   NewSubscriptionEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(bob.subsEvents,
			   "SipEventSubscriptionHandler::onNewSubscription", 
			   5000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, bob.handle);
         bobSubs = h;
			ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
			ASSERT_EQ(evt.remoteAddress, alice.config.uri());
			ASSERT_EQ(evt.remoteDisplayName, alice.config.name);
         ASSERT_EQ(evt.supportedMimeTypes.size(), 1);
      }

      // Accept the subscription
		ASSERT_EQ(bob.subs->accept(bobSubs, evt1234), kSuccess);
      {
			SipEventSubscriptionHandle h;
			SubscriptionStateChangedEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(bob.subsEvents,
			   "SipEventSubscriptionHandler::onSubscriptionStateChanged",
			   15000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      // End the subscription
		ASSERT_EQ(bob.subs->end(bobSubs), kSuccess);
      {
			SipEventSubscriptionHandle h;
			SubscriptionEndedEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(bob.subsEvents,
			   "SipEventSubscriptionHandler::onSubscriptionEnded",
			   5000,
            AlwaysTruePred(), h, evt));
			ASSERT_EQ(bobSubs, h);
			ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }
	});

	// Overview of Alice's thread:
	//  - 
	auto aliceEvents = std::async(std::launch::async, [&] () {
      {
			SipEventSubscriptionHandle h;
			NewSubscriptionEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.subsEvents,
			   "SipEventSubscriptionHandler::onNewSubscription", 
			   15000,
			   AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, alice.handle);
			ASSERT_EQ(h, aliceSubs);
			ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Outgoing);
			ASSERT_EQ(evt.remoteAddress, bob.config.uri());
			ASSERT_EQ(evt.remoteDisplayName, "");
      }

      {
			SipEventSubscriptionHandle h;
			SubscriptionStateChangedEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.subsEvents,
			   "SipEventSubscriptionHandler::onSubscriptionStateChanged",
			   5000,
            AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
			ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      {
			SipEventSubscriptionHandle h;
			IncomingEventStateEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.subsEvents,
			   "SipEventSubscriptionHandler::onIncomingEventState",
			   5000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
         ASSERT_EQ(evt.eventState.contentLength, 9);
         //std::cout << "content is " << evt.eventState.content << std::endl;
         ASSERT_EQ(strcmp(evt.eventState.contentUTF8.c_str(), "1,2,3,4\r\n"), 0);
         ASSERT_EQ(evt.eventState.eventPackage, "cpcapi2test");
         ASSERT_NEAR(evt.eventState.expiresTimeMs, 3598, 10);
         ASSERT_EQ(evt.eventState.mimeType, "application");
         ASSERT_EQ(evt.eventState.mimeSubType, "cpcapi2test");
      }

      {
			SipEventSubscriptionHandle h;
			IncomingEventStateEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.subsEvents,
			   "SipEventSubscriptionHandler::onIncomingEventState",
			   5000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
         ASSERT_EQ(evt.eventState.contentLength, 9);
         //std::cout << "content is " << evt.eventState.content << std::endl;
         ASSERT_EQ(strcmp(evt.eventState.contentUTF8.c_str(), "5,6,7,8\r\n"), 0);
         ASSERT_EQ(evt.eventState.eventPackage, "cpcapi2test");
         ASSERT_NEAR(evt.eventState.expiresTimeMs, 3598, 10);
         ASSERT_EQ(evt.eventState.mimeType, "application");
         ASSERT_EQ(evt.eventState.mimeSubType, "cpcapi2test");
      }

      {
			SipEventSubscriptionHandle h;
			SubscriptionEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.subsEvents,
			   "SipEventSubscriptionHandler::onSubscriptionEnded",
			   15000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
			ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
         ASSERT_EQ(evt.reason, "deactivated");
      }

      {
			SipEventSubscriptionHandle h;
			NewSubscriptionEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.subsEvents,
			   "SipEventSubscriptionHandler::onNewSubscription", 
			   15000,
			   AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, alice.handle);
			ASSERT_EQ(h, aliceSubs);
			ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Outgoing);
			ASSERT_EQ(evt.remoteAddress, bob.config.uri());
			ASSERT_EQ(evt.remoteDisplayName, "");
      }

      {
			SipEventSubscriptionHandle h;
			SubscriptionStateChangedEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.subsEvents,
			   "SipEventSubscriptionHandler::onSubscriptionStateChanged",
			   5000,
            AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
			ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      {
			SipEventSubscriptionHandle h;
			IncomingEventStateEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.subsEvents,
			   "SipEventSubscriptionHandler::onIncomingEventState",
			   5000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
         ASSERT_EQ(evt.eventState.contentLength, 9);
         //std::cout << "content is " << evt.eventState.content << std::endl;
         ASSERT_EQ(strcmp(evt.eventState.contentUTF8.c_str(), "1,2,3,4\r\n"), 0);
         ASSERT_EQ(evt.eventState.eventPackage, "cpcapi2test");
         ASSERT_NEAR(evt.eventState.expiresTimeMs, 3598, 10);
         ASSERT_EQ(evt.eventState.mimeType, "application");
         ASSERT_EQ(evt.eventState.mimeSubType, "cpcapi2test");
      }

      {
			SipEventSubscriptionHandle h;
			SubscriptionEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.subsEvents,
			   "SipEventSubscriptionHandler::onSubscriptionEnded",
			   15000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
			ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
         ASSERT_EQ(evt.reason, "noresource");
      }
	});

	// std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
	ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
	ASSERT_NO_THROW(bobEvents.get());
	ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
	ASSERT_NO_THROW(aliceEvents.get());

	// not needed, but handy sometimes when debugging ...
	//std::this_thread::sleep_for(std::chrono::milliseconds(10000));
}

// First subscription accepted then terminated with reason=probation and retry-later of 1 second
// which triggers a new automatic subscription LATER which is then accepted then terminated normally
// Related to issue SCORE-1213.
TEST_F(EventSubscriptionTests, DISABLED_NotifySubscriptionStateTerminatedWithProbationReason) {
	TestAccount alice("alice");
   TestAccount bob("bob");

	// make an outgoing subscription from Alice to Bob using the demo.xten.com server
	SipEventSubscriptionHandle aliceSubs = alice.subs->createSubscription(alice.handle);
   SipEventSubscriptionSettings subsSettings;
   subsSettings.eventPackage = "cpcapi2test";
   subsSettings.expiresSeconds = 3600;
   subsSettings.supportedMimeTypes.push_back(CPCAPI2::MimeType("application", "cpcapi2test"));
   alice.subs->applySubscriptionSettings(aliceSubs, subsSettings);
	alice.subs->addParticipant(aliceSubs, bob.config.uri());
	alice.subs->start(aliceSubs);

	// Overview of Bob's thread:
	//  - 
	auto bobEvents = std::async(std::launch::async, [&] () {
		SipEventSubscriptionHandle bobSubs = 0;
      {
         SipEventSubscriptionHandle h;
		   NewSubscriptionEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(bob.subsEvents,
			   "SipEventSubscriptionHandler::onNewSubscription", 
			   5000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, bob.handle);
         bobSubs = h;
			ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
			ASSERT_EQ(evt.remoteAddress, alice.config.uri());
			ASSERT_EQ(evt.remoteDisplayName, alice.config.name);
         ASSERT_EQ(evt.supportedMimeTypes.size(), 1);
      }

		// Bob accepts the subscription (200 OK) -- this should trigger state transitions for both Bob and Alice
      SipEventState evt1234;
      evt1234.contentUTF8 = "1,2,3,4\r\n";
      evt1234.contentLength = 9;
      evt1234.eventPackage = "cpcapi2test";
      evt1234.expiresTimeMs = 3598;
      evt1234.mimeType = "application";
      evt1234.mimeSubType = "cpcapi2test";
		ASSERT_EQ(bob.subs->accept(bobSubs, evt1234), kSuccess);
      {
			SipEventSubscriptionHandle h;
			SubscriptionStateChangedEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(bob.subsEvents,
			   "SipEventSubscriptionHandler::onSubscriptionStateChanged",
			   15000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }


		std::this_thread::sleep_for(std::chrono::milliseconds(500));

      SipEventState evt5678;
      evt5678.contentUTF8 = "5,6,7,8\r\n";
      evt5678.contentLength = 9;
      evt5678.eventPackage = "cpcapi2test";
      evt5678.expiresTimeMs = 3598;
      evt5678.mimeType = "application";
      evt5678.mimeSubType = "cpcapi2test";
      ASSERT_EQ(bob.subs->notify(bobSubs, evt5678), kSuccess);

		std::this_thread::sleep_for(std::chrono::milliseconds(500));

      ASSERT_EQ(bob.subs->end(bobSubs, SipSubscriptionTerminateReason_Probation, 1), kSuccess);
      {
			SipEventSubscriptionHandle h;
			SubscriptionEndedEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(bob.subsEvents,
			   "SipEventSubscriptionHandler::onSubscriptionEnded",
			   5000,
            AlwaysTruePred(), h, evt));
			ASSERT_EQ(bobSubs, h);
			ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }

      // Expect a new subscription
      {
         SipEventSubscriptionHandle h;
		   NewSubscriptionEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(bob.subsEvents,
			   "SipEventSubscriptionHandler::onNewSubscription", 
			   5000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, bob.handle);
         bobSubs = h;
			ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
			ASSERT_EQ(evt.remoteAddress, alice.config.uri());
			ASSERT_EQ(evt.remoteDisplayName, alice.config.name);
         ASSERT_EQ(evt.supportedMimeTypes.size(), 1);
      }

      // Accept the subscription
		ASSERT_EQ(bob.subs->accept(bobSubs, evt1234), kSuccess);
      {
			SipEventSubscriptionHandle h;
			SubscriptionStateChangedEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(bob.subsEvents,
			   "SipEventSubscriptionHandler::onSubscriptionStateChanged",
			   15000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      // End the subscription
		ASSERT_EQ(bob.subs->end(bobSubs), kSuccess);
      {
			SipEventSubscriptionHandle h;
			SubscriptionEndedEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(bob.subsEvents,
			   "SipEventSubscriptionHandler::onSubscriptionEnded",
			   5000,
            AlwaysTruePred(), h, evt));
			ASSERT_EQ(bobSubs, h);
			ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }
	});

	// Overview of Alice's thread:
	//  - 
	auto aliceEvents = std::async(std::launch::async, [&] () {
      {
			SipEventSubscriptionHandle h;
			NewSubscriptionEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.subsEvents,
			   "SipEventSubscriptionHandler::onNewSubscription", 
			   15000,
			   AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, alice.handle);
			ASSERT_EQ(h, aliceSubs);
			ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Outgoing);
			ASSERT_EQ(evt.remoteAddress, bob.config.uri());
			ASSERT_EQ(evt.remoteDisplayName, "");
      }

      {
			SipEventSubscriptionHandle h;
			SubscriptionStateChangedEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.subsEvents,
			   "SipEventSubscriptionHandler::onSubscriptionStateChanged",
			   5000,
            AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
			ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      {
			SipEventSubscriptionHandle h;
			IncomingEventStateEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.subsEvents,
			   "SipEventSubscriptionHandler::onIncomingEventState",
			   5000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
         ASSERT_EQ(evt.eventState.contentLength, 9);
         //std::cout << "content is " << evt.eventState.content << std::endl;
         ASSERT_EQ(strcmp(evt.eventState.contentUTF8.c_str(), "1,2,3,4\r\n"), 0);
         ASSERT_EQ(evt.eventState.eventPackage, "cpcapi2test");
         ASSERT_NEAR(evt.eventState.expiresTimeMs, 3598, 10);
         ASSERT_EQ(evt.eventState.mimeType, "application");
         ASSERT_EQ(evt.eventState.mimeSubType, "cpcapi2test");
      }

      {
			SipEventSubscriptionHandle h;
			IncomingEventStateEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.subsEvents,
			   "SipEventSubscriptionHandler::onIncomingEventState",
			   5000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
         ASSERT_EQ(evt.eventState.contentLength, 9);
         //std::cout << "content is " << evt.eventState.content << std::endl;
         ASSERT_EQ(strcmp(evt.eventState.contentUTF8.c_str(), "5,6,7,8\r\n"), 0);
         ASSERT_EQ(evt.eventState.eventPackage, "cpcapi2test");
         ASSERT_NEAR(evt.eventState.expiresTimeMs, 3598, 10);
         ASSERT_EQ(evt.eventState.mimeType, "application");
         ASSERT_EQ(evt.eventState.mimeSubType, "cpcapi2test");
      }

      {
			SipEventSubscriptionHandle h;
			SubscriptionEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.subsEvents,
			   "SipEventSubscriptionHandler::onSubscriptionEnded",
			   15000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
			ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
         ASSERT_EQ(evt.reason, "probation");
      }

      {
			SipEventSubscriptionHandle h;
			NewSubscriptionEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.subsEvents,
			   "SipEventSubscriptionHandler::onNewSubscription", 
			   15000,
			   AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, alice.handle);
			ASSERT_EQ(h, aliceSubs);
			ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Outgoing);
			ASSERT_EQ(evt.remoteAddress, bob.config.uri());
			ASSERT_EQ(evt.remoteDisplayName, "");
      }

      {
			SipEventSubscriptionHandle h;
			SubscriptionStateChangedEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.subsEvents,
			   "SipEventSubscriptionHandler::onSubscriptionStateChanged",
			   5000,
            AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
			ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      {
			SipEventSubscriptionHandle h;
			IncomingEventStateEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.subsEvents,
			   "SipEventSubscriptionHandler::onIncomingEventState",
			   5000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
         ASSERT_EQ(evt.eventState.contentLength, 9);
         //std::cout << "content is " << evt.eventState.content << std::endl;
         ASSERT_EQ(strcmp(evt.eventState.contentUTF8.c_str(), "1,2,3,4\r\n"), 0);
         ASSERT_EQ(evt.eventState.eventPackage, "cpcapi2test");
         ASSERT_NEAR(evt.eventState.expiresTimeMs, 3598, 10);
         ASSERT_EQ(evt.eventState.mimeType, "application");
         ASSERT_EQ(evt.eventState.mimeSubType, "cpcapi2test");
      }

      {
			SipEventSubscriptionHandle h;
			SubscriptionEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.subsEvents,
			   "SipEventSubscriptionHandler::onSubscriptionEnded",
			   15000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
			ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
         ASSERT_EQ(evt.reason, "noresource");
      }
	});

	// std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
	ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(25000)), std::future_status::ready);
	ASSERT_NO_THROW(bobEvents.get());
	ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(25000)), std::future_status::ready);
	ASSERT_NO_THROW(aliceEvents.get());

	// not needed, but handy sometimes when debugging ...
	//std::this_thread::sleep_for(std::chrono::milliseconds(10000));
}

}
