#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#include "cpcapi2_test_fixture.h"
#include "test_call_events.h"
#include "test_events.h"

using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::SipConversation;
using namespace CPCAPI2::SipAccount;

class ForkingCallTests : public CpcapiAutoTest
{
public:
   ForkingCallTests() {}
   virtual ~ForkingCallTests() {}
};

TEST_F(ForkingCallTests, BasicCallForkingCallerEnds) {
   TestAccount alice("alice");
   TestAccount bob("bob");
   TestAccount max("max", Account_NoInit);
   max.config.settings.username = bob.config.settings.username;
   max.enable();

   for (int i=0; i<10; i++)
   {

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      //assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      {
         SipConversationHandle h;
	      NewConversationEvent evt;
	      ASSERT_TRUE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onNewConversation",
		      15000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
         ASSERT_EQ(aliceCall, h);
	      ASSERT_EQ(ConversationType_Outgoing, evt.conversationType);
	      ASSERT_EQ(bob.config.uri(), evt.remoteAddress);
      }

      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      assertSuccess(alice.conversation->end(aliceCall));

      {
         SipConversationHandle h;
         ConversationEndedEvent evt;
	      ASSERT_TRUE(alice.conversationEvents->expectEvent("SipConversationHandler::onConversationEnded",
		      15000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(aliceCall, h);
	      ASSERT_EQ(ConversationEndReason_UserTerminatedLocally, evt.endReason);
      }
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());

      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);

      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   // Max's thread (is Bob Two):
   auto maxEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle maxCall;
      assertNewConversationIncoming(max, &maxCall, alice.config.uri());
      assertSuccess(max.conversation->sendRingingResponse(maxCall));
      assertConversationStateChanged(max, maxCall, ConversationState_LocalRinging);
      assertConversationEnded(max, maxCall, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor3(aliceEvents, bobEvents, maxEvents);

   } // for loop
}

#if !(defined(__linux__) && !defined(ANDROID))
TEST_F(ForkingCallTests, AcallsBredirects_to_forkedC) {
   TestAccount alice("alice");
   TestAccount maia("maia");
   TestAccount bob("bob");
   TestAccount max("max", Account_NoInit);
   max.config.settings.username = bob.config.settings.username;
   max.enable();

   //for (int i=0; i<10; i++)
   {

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, maia.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      //assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      {
         SipConversationHandle h;
	      NewConversationEvent evt;
	      ASSERT_TRUE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onNewConversation",
		      15000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
         ASSERT_EQ(aliceCall, h);
	      ASSERT_EQ(ConversationType_Outgoing, evt.conversationType);
	      ASSERT_EQ(maia.config.uri(), evt.remoteAddress);
      }

      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertRedirectRequest(alice, aliceCall, [&](const RedirectRequestEvent& evt){
         ASSERT_EQ(bob.config.uri(), evt.targetAddress);
      });
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      // check if hold works
      alice.conversation->hold(aliceCall);
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_TRUE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendOnly, evt.localMediaInfo[0].mediaDirection);
      });
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      assertSuccess(alice.conversation->unhold(aliceCall));
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });

      {
         SipConversationHandle h;
         ConversationEndedEvent evt;
	      ASSERT_TRUE(alice.conversationEvents->expectEvent("SipConversationHandler::onConversationEnded",
		      15000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(aliceCall, h);
	      ASSERT_EQ(ConversationEndReason_UserTerminatedRemotely, evt.endReason);
      }
   });

   auto maiaEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle maiaCall;
      assertNewConversationIncoming(maia, &maiaCall, alice.config.uri());

      assertSuccess(maia.conversation->sendRingingResponse(maiaCall));
      assertConversationStateChanged(maia, maiaCall, ConversationState_LocalRinging);

      assertSuccess(maia.conversation->redirect(maiaCall, max.config.uri(), ""));
      assertConversationEnded(maia, maiaCall, ConversationEndReason_Redirected);
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());

      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);

      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendOnly);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_FALSE(evt.localHold);
         ASSERT_TRUE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_ReceiveOnly, evt.localMediaInfo[0].mediaDirection);
      });
      assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
      std::this_thread::sleep_for(std::chrono::milliseconds(1000)); // give max time to ring before answering
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });

      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });

   // Max's thread (is Bob Two):
   auto maxEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle maxCall;
      assertNewConversationIncoming(max, &maxCall, alice.config.uri());
      assertSuccess(max.conversation->sendRingingResponse(maxCall));
      assertConversationStateChanged(max, maxCall, ConversationState_LocalRinging);
      assertConversationEnded(max, maxCall, ConversationEndReason_UserTerminatedRemotely);
   });

   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
   ASSERT_EQ(maiaEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(maiaEvents.get());
   ASSERT_EQ(maxEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(maxEvents.get());
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());

   } // for loop
}
#endif

TEST_F(ForkingCallTests, BasicCallForkingLocalHangup) {
   TestAccount alice("alice");
   TestAccount bob("bob");
   TestAccount max("max", Account_NoInit);
   max.config.settings.username = bob.config.settings.username;
   max.enable();

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      //assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      {
         SipConversationHandle h;
	      NewConversationEvent evt;
	      ASSERT_TRUE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onNewConversation",
		      15000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
         ASSERT_EQ(aliceCall, h);
	      ASSERT_EQ(ConversationType_Outgoing, evt.conversationType);
	      ASSERT_EQ(bob.config.uri(), evt.remoteAddress);
      }

      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      alice.conversation->end(aliceCall);

      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());

      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);

      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   // Max's thread (is Bob Two):
   auto maxEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle maxCall;
      assertNewConversationIncoming(max, &maxCall, alice.config.uri());
      assertSuccess(max.conversation->sendRingingResponse(maxCall));
      assertConversationStateChanged(max, maxCall, ConversationState_LocalRinging);
      assertConversationEnded(max, maxCall, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor3(aliceEvents, bobEvents, maxEvents);
}


TEST_F(ForkingCallTests, BasicCallForkingOneLegRejects) {
   TestAccount alice("alice");
   TestAccount bob("bob");
   TestAccount max("max", Account_NoInit);
   max.config.settings.username = bob.config.settings.username;
   max.enable();

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      //assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      {
         SipConversationHandle h;
	      NewConversationEvent evt;
	      ASSERT_TRUE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onNewConversation",
		      15000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
         ASSERT_EQ(aliceCall, h);
	      ASSERT_EQ(ConversationType_Outgoing, evt.conversationType);
	      ASSERT_EQ(bob.config.uri(), evt.remoteAddress);
      }

      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());

      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });

   // Max's thread (is Bob Two):
   auto maxEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle maxCall;
      assertNewConversationIncoming(max, &maxCall, alice.config.uri());
      assertSuccess(max.conversation->reject(maxCall, 486));
      assertConversationEnded(max, maxCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor3(aliceEvents, bobEvents, maxEvents);
}


TEST_F(ForkingCallTests, DISABLED_BasicCallForkingCancelToSelf) {

   TestAccount alice("alice");
   TestAccount bob("bob", Account_NoInit);
   bob.config.settings.username = alice.config.settings.username;
   bob.enable();

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {

      {
         SipConversationHandle h;
	      NewConversationEvent evt;
	      ASSERT_TRUE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onNewConversation",
		      15000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
         ASSERT_EQ(aliceCall, h);
	      ASSERT_EQ(ConversationType_Outgoing, evt.conversationType);
	      ASSERT_EQ(alice.config.uri(), evt.remoteAddress);
      }

      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onConversationStateChanged",
	         15000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
         ASSERT_EQ(ConversationState_RemoteRinging, evt.conversationState);
      }
      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onConversationStateChanged",
	         15000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
         ASSERT_EQ(ConversationState_RemoteRinging, evt.conversationState);
      }
      
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   // max = alice (receiving self call)
   auto maxEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle aliceCall2;
      {
         NewConversationEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent("SipConversationHandler::onNewConversation",
            30000, HandleNotEqualsPred<SipConversationHandle>(aliceCall), aliceCall2, evt));
         ASSERT_EQ(alice.config.uri(), evt.remoteAddress);
      }
      assertSuccess(alice.conversation->sendRingingResponse(aliceCall2));
      assertConversationStateChanged(alice, aliceCall2, ConversationState_LocalRinging);
      assertConversationEnded(alice, aliceCall2, ConversationEndReason_UserTerminatedRemotely);
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });


   waitFor3(aliceEvents, bobEvents, maxEvents);
}


TEST_F(ForkingCallTests, BasicCallForkingCancel) {
   TestAccount alice("alice");
   TestAccount bob("bob");
   TestAccount max("max", Account_NoInit);
   max.config.settings.username = bob.config.settings.username;
   max.enable();

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      //assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      {
         SipConversationHandle h;
	      NewConversationEvent evt;
	      ASSERT_TRUE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onNewConversation",
		      15000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
         ASSERT_EQ(aliceCall, h);
	      ASSERT_EQ(ConversationType_Outgoing, evt.conversationType);
	      ASSERT_EQ(bob.config.uri(), evt.remoteAddress);
      }

      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });
   
   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());

      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   // Max's thread (is Bob Two):
   auto maxEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle maxCall;
      assertNewConversationIncoming(max, &maxCall, alice.config.uri());
      assertSuccess(max.conversation->sendRingingResponse(maxCall));
      assertConversationStateChanged(max, maxCall, ConversationState_LocalRinging);
      assertConversationEnded(max, maxCall, ConversationEndReason_UserTerminatedRemotely);
   });
   
   waitFor3(aliceEvents, bobEvents, maxEvents);
}

// disabled on linux; test relies on timing of max being able to redirect in time,
// so max's conversation end reason is redirected. If max can't redirect quickly enough,
// bob will answer and max's end reason will be ended remotely.
#if !(defined(__linux__) && !defined(ANDROID))
TEST_F(ForkingCallTests, ForkedCallRedirectAnswer)
{
   TestAccount alice("alice");
   TestAccount bob("bob");
   //redirect target
   TestAccount ron("ron");
   TestAccount max("max", Account_NoInit);
   max.config.settings.username = bob.config.settings.username;
   max.enable();

   {
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->addParticipant(aliceCall, bob.config.uri());
      alice.conversation->start(aliceCall);

      auto aliceEvents = std::async(std::launch::async, [&]() {
         {
            SipConversationHandle h;
            NewConversationEvent evt;
            ASSERT_TRUE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onNewConversation",
               15000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
            ASSERT_EQ(aliceCall, h);
            ASSERT_EQ(ConversationType_Outgoing, evt.conversationType);
            ASSERT_EQ(bob.config.uri(), evt.remoteAddress);
         }

         //assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
         //assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);

         // There must not be a RedirectRequestEvent after 200
         {
            SipConversationHandle h;
            RedirectRequestEvent evt;
            ASSERT_FALSE(alice.conversationEvents->waitForEvent(0, "SipConversationHandler::onIncomingRedirectRequest",
               15000, AlwaysTruePred(), h, evt));
         }
   });

      auto bobEvents = std::async(std::launch::async, [&]() {
         SipConversationHandle bobCall;
         assertNewConversationIncoming(bob, &bobCall, alice.config.uri());

         assertSuccess(bob.conversation->sendRingingResponse(bobCall));
         assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);

         assertSuccess(bob.conversation->accept(bobCall));
         assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
         assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

         std::this_thread::sleep_for(std::chrono::milliseconds(10000));
         assertSuccess(bob.conversation->end(bobCall));
         assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      });

      // Max's thread (is Bob Two):
      auto maxEvents = std::async(std::launch::async, [&]() {
         SipConversationHandle maxCall;
         assertNewConversationIncoming(max, &maxCall, alice.config.uri());
         assertSuccess(max.conversation->sendRingingResponse(maxCall));
         assertConversationStateChanged(max, maxCall, ConversationState_LocalRinging);

         assertSuccess(max.conversation->redirect(maxCall, ron.config.uri(), ""));
         assertConversationEnded(max, maxCall, ConversationEndReason_Redirected);
      });

      // Ron gets nothing, bob answered
      auto ronEvents = std::async(std::launch::async, [&]() {
         {
            SipConversationHandle h;
            NewConversationEvent evt;
            ASSERT_FALSE(ron.conversationEvents->waitForEvent(0, "SipConversationHandler::onNewConversation",
               15000, AlwaysTruePred(), h, evt));
         }
      });

      waitFor4(aliceEvents, bobEvents, maxEvents, ronEvents);
   }
}
#endif
