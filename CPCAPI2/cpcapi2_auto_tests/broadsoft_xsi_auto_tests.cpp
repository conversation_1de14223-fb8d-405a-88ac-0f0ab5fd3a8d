#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#include "cpcapi2_test_fixture.h"
#include "test_events.h"
#include "broadsoftxsi/BroadsoftXsiInterface.h"

#include <vector>

using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::BroadsoftXsi;

namespace {

class BroadsoftXsiModuleTest : public CpcapiAutoTest
{
public:
   BroadsoftXsiModuleTest() {}
   virtual ~BroadsoftXsiModuleTest() {}
};

class HandlerIMade : public BroadsoftXsiHandler
{
   virtual int onQueryCallLogs(const BroadsoftXsiHandle& c, const QueryCallLogsSuccessEvent& e)
   {
      for (auto it = e.callLogs.begin(); it != e.callLogs.end(); ++it)
      {
         int i = 0;
      }
      return kSuccess;
   }

   virtual int onQueryServices(const BroadsoftXsiHandle& c, const QueryServicesSuccessEvent&) { return kSuccess; }
   virtual int onQueryServicesFailure(const BroadsoftXsiHandle& c, const BroadsoftXsiFailureEvent& e) { return kSuccess; }
   virtual int onQueryCallLogsFailure(const BroadsoftXsiHandle& c, const BroadsoftXsiFailureEvent& e) { return kSuccess; }
   virtual int onDeleteCallLogsSuccess(const BroadsoftXsiHandle& c, const DeleteCallLogsSuccessEvent& e) { return kSuccess; }
   virtual int onDeleteCallLogsFailure(const BroadsoftXsiHandle& c, const BroadsoftXsiFailureEvent& e) { return kSuccess; }
   virtual int onDeleteCallLogSuccess(const BroadsoftXsiHandle& c, const DeleteCallLogSuccessEvent& e) { return kSuccess; }
   virtual int onDeleteCallLogFailure(const BroadsoftXsiHandle& c, const BroadsoftXsiFailureEvent& e) { return kSuccess; }
   virtual int onQueryEnterpriseDirectory(const BroadsoftXsiHandle& c, const QueryEnterpriseDirectorySuccessEvent& e) { return kSuccess; }
   virtual int onQueryEnterpriseDirectoryFailure(const BroadsoftXsiHandle& c, const BroadsoftXsiFailureEvent& e) { return kSuccess; }
   virtual int onQueryBroadworksAnywhereSettings(const BroadsoftXsiHandle& c, const QueryBroadworksAnywhereSettingsSuccessEvent& e) { return kSuccess; }
   virtual int onQueryCallForwardAlwaysSettings(const BroadsoftXsiHandle& c, const QueryCallForwardAlwaysSettingsSuccessEvent& e) { return kSuccess; }
   virtual int onQueryCallForwardBusySettings(const BroadsoftXsiHandle& c, const QueryCallForwardBusySettingsSuccessEvent& e) { return kSuccess; }
   virtual int onQueryCallForwardNoAnswerSettings(const BroadsoftXsiHandle& c, const QueryCallForwardNoAnswerSettingsSuccessEvent& e) { return kSuccess; }
   virtual int onQueryDoNotDisturbSettings(const BroadsoftXsiHandle& c, const QueryDoNotDisturbSettingsSuccessEvent& e) { return kSuccess; }
   virtual int onQueryRemoteOfficeSettings(const BroadsoftXsiHandle& c, const QueryRemoteOfficeSettingsSuccessEvent& e) { return kSuccess; }
   virtual int onQuerySimultaneousRingSettings(const BroadsoftXsiHandle& c, const QuerySimultaneousRingSettingsSuccessEvent& e) { return kSuccess; }
   virtual int onQueryServiceSettingsFailure(const BroadsoftXsiHandle& c, const BroadsoftXsiFailureEvent& e) { return kSuccess; }
   virtual int onSetServiceSettings(const BroadsoftXsiHandle& c, const SetServiceSettingsSuccessEvent& e) { return kSuccess; }
   virtual int onSetServiceSettingsFailure(const BroadsoftXsiHandle& c, const BroadsoftXsiFailureEvent& e) { return kSuccess; }
   virtual int onCreateCallSuccess(const BroadsoftXsiHandle& c, const CreateCallSuccessEvent& e) { return kSuccess; }
   virtual int onCreateCallFailure(const BroadsoftXsiHandle& c, const BroadsoftXsiFailureEvent& e) { return kSuccess; }
   virtual int onError(const BroadsoftXsiHandle& c, const ErrorEvent& e) { return kSuccess; }
};


TEST_F(BroadsoftXsiModuleTest, QueryCallLogs) {
   Phone* phone = Phone::create();
   phone->initialize(LicenseInfo(), (PhoneHandler*)NULL);
   phone->setLoggingEnabled("booyah", true);
   HandlerIMade a;
   auto bXsi = BroadsoftXsiInterface::getInterface(phone);
   auto h = bXsi->create();
   bXsi->setHandler(h, &a);
   LocalSettings settings;
   settings.userName = "<EMAIL>";
   settings.password = "counterpath";
   settings.domain = "xsp1.iop1.broadworks.net";
   settings.useHttps = true;
   //settings.logXMLPayload = true;
   settings.logParsedResponse = true;
   bXsi->configureSettings(h, settings);
   bXsi->applySettings(h);

   //bXsi->queryCallLogs(h);
   //std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   //bXsi->deleteCallLogs(h);
   //std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   //bXsi->deleteCallLog(h, CallLogType_Missed, "44370627:0");
   //std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   //DirectorySearchParameters searchParams;
   //searchParams.results = 10;
   //searchParams.sortColumn = SortColumn_FirstName;
   //bXsi->queryEnterpriseDirectory(h, searchParams);
   //std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   //{BroadworksAnywhere settings;
   //settings.alertAllLocationsForClickToDialCalls = true;
   //settings.alertAllLocationsForGroupPagingCalls = false;
   //bXsi->setBroadworksAnywhereSettings(h, settings); }
   //bXsi->queryServiceSettings(h, ServiceType_BroadworksAnywhere);

   //{CallForwardAlways settings;
   //settings.enabled = false;
   //settings.phoneNumber = "8196";
   //settings.ringSplash = true;
   //bXsi->setCallForwardAlwaysSettings(h, settings); }
   //bXsi->queryServiceSettings(h, ServiceType_CallForwardAlways);

   //{CallForwardBusy settings;
   //settings.enabled = false;
   //settings.phoneNumber = "8195";
   //bXsi->setCallForwardBusySettings(h, settings); }
   //bXsi->queryServiceSettings(h, ServiceType_CallForwardBusy);

   //{CallForwardNoAnswer settings;
   //settings.enabled = true;
   //settings.phoneNumber = "8196";
   //settings.numberOfRings = 6;
   //bXsi->setCallForwardNoAnswerSettings(h, settings); }
   //bXsi->queryServiceSettings(h, ServiceType_CallForwardNoAnswer);

   //{DoNotDisturb settings;
   //settings.enabled = false;
   //settings.ringSplash = false;
   //bXsi->setDoNotDisturbSettings(h, settings); }
   //bXsi->queryServiceSettings(h, ServiceType_DoNotDisturb);

   //{RemoteOffice settings;
   //settings.enabled = false;
   //settings.phoneNumber = "8196";
   //bXsi->setRemoteOfficeSettings(h, settings); }
   //bXsi->queryServiceSettings(h, ServiceType_RemoteOffice);

   //{SimultaneousRing settings;
   //settings.enabled = false;
   //settings.dontRingWhenOnCall = true;
   //bXsi->setSimultaneousRingSettings(h, settings); }
   //bXsi->queryServiceSettings(h, ServiceType_SimultaneousRing);
   //std::this_thread::sleep_for(std::chrono::milliseconds(8000));

   bXsi->destroy(h);
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   Phone::release(phone);
}

}  // namespace

