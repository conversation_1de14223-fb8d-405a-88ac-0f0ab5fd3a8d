#include "cpcapi2_test_fixture.h"
#include "test_account_events.h"
#include "test_events.h"
#include "test_call_events.h"
#include "sipua_alianza_api_test_fixture.h"
#include "sipua_alianza_api_test_helper.h"
#include "sipua_alianza_api_test_events.h"

#include "alianza_api/interface/public/alianza_api_handler.h"
#include "alianza_api/interface/public/alianza_api_types.h"
#include "alianza_api/impl/alianza_api_manager_interface.h"

#include <sstream>
#include <memory>

#include "util/CurlPPHelper.h"
#include "util/CurlPPProgress.h"
#include "util/CurlPPSSL.h"
#include "util/CurlURI.h"

// rapidjson
#include <writer.h>
#include <stringbuffer.h>
#include <document.h>


using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::SipAccount;
using namespace std::chrono;
using namespace curlpp::options;

#define GTEST_SKIP_REPRO() if (TestEnvironmentConfig::testEnvironmentId() == "repro") { GTEST_SKIP(); }

namespace
{
   
   class ApiManagerHolder
   {
   public:
      ApiManagerHolder(const std::shared_ptr<CPCAPI2::test::AlianzaApiManager>& apiMgr, PhoneInterface* phone, CPCAPI2::test::EventHandler* apiEvents) :
         mApiManager(apiMgr), mPhone(phone), mApiEvents(apiEvents)
      {
      }

      std::shared_ptr<CPCAPI2::test::AlianzaApiManager> apiMgr() const { return mApiManager; } 

      ~ApiManagerHolder()
      {
         mApiManager->shutdown();
         delete mApiEvents;
         Phone::release(mPhone);
      }

   private:

      std::shared_ptr<CPCAPI2::test::AlianzaApiManager> mApiManager;
      PhoneInterface* mPhone;
      CPCAPI2::test::EventHandler* mApiEvents;
   };

   class SipuaAlianzaApiModuleTest : public CpcapiAutoTest
   {
   public:
      SipuaAlianzaApiModuleTest() {}
      virtual ~SipuaAlianzaApiModuleTest() {}

      void SetUp() override
      {
         if (TestEnvironmentConfig::cpeTestLevel() > TestEnvironmentConfig::CpeTestLevel::Exhaustive)
         {
            GTEST_SKIP();
         }
      }

      void addNumberInPartition(const AlianzaAccountConfig& alianzaConfig, AlianzaSessionInfo& alianzaSession, std::shared_ptr<CPCAPI2::test::AlianzaApiManager> apiMgr, test::EventHandler* apiEvents);
      void addAccountInPartition(const AlianzaAccountConfig& alianzaConfig, AlianzaSessionInfo& alianzaSession, std::shared_ptr<CPCAPI2::test::AlianzaApiManager> apiMgr, test::EventHandler* apiEvents);
      void setAccountGroupName(const AlianzaAccountConfig& alianzaConfig, AlianzaSessionInfo& alianzaSession, std::shared_ptr<CPCAPI2::test::AlianzaApiManager> apiMgr, test::EventHandler* apiEvents);
      void addUserInAccount(const AlianzaAccountConfig& alianzaConfig, AlianzaSessionInfo& alianzaSession, std::shared_ptr<AlianzaSipUaInfo> ua, std::shared_ptr<CPCAPI2::test::AlianzaApiManager> apiMgr, test::EventHandler* apiEvents);
      void authenticateUser(const AlianzaAccountConfig& alianzaConfig, AlianzaSessionInfo& alianzaSession, std::shared_ptr<AlianzaSipUaInfo> ua, std::shared_ptr<CPCAPI2::test::AlianzaApiManager> apiMgr, test::EventHandler* apiEvents);
      void queryClientConfiguration(const AlianzaAccountConfig& alianzaConfig, AlianzaSessionInfo& alianzaSession, std::shared_ptr<AlianzaSipUaInfo> ua, std::shared_ptr<CPCAPI2::test::AlianzaApiManager> apiMgr, test::EventHandler* apiEvents);
      void addNumberInAccount(const AlianzaAccountConfig& alianzaConfig, AlianzaSessionInfo& alianzaSession, std::shared_ptr<CPCAPI2::test::AlianzaApiManager> apiMgr, test::EventHandler* apiEvents);
      void updateNumberInAccount(const AlianzaAccountConfig& alianzaConfig, AlianzaSessionInfo& alianzaSession, std::shared_ptr<CPCAPI2::test::AlianzaApiManager> apiMgr, test::EventHandler* apiEvents);
      void updateNumberInUser(const AlianzaAccountConfig& alianzaConfig, AlianzaSessionInfo& alianzaSession, std::shared_ptr<CPCAPI2::test::AlianzaApiManager> apiMgr, test::EventHandler* apiEvents);
      void deleteNumberInPartition(const AlianzaAccountConfig& alianzaConfig, AlianzaSessionInfo& alianzaSession, std::shared_ptr<CPCAPI2::test::AlianzaApiManager> apiMgr, test::EventHandler* apiEvents);
      void deleteNumberInAccount(const AlianzaAccountConfig& alianzaConfig, AlianzaSessionInfo& alianzaSession, std::shared_ptr<CPCAPI2::test::AlianzaApiManager> apiMgr, test::EventHandler* apiEvents);
      void deleteAccountInPartition(const AlianzaAccountConfig& alianzaConfig, AlianzaSessionInfo& alianzaSession, std::shared_ptr<CPCAPI2::test::AlianzaApiManager> apiMgr, test::EventHandler* apiEvents);
      void checkNumberInAccount(const AlianzaAccountConfig& alianzaConfig, AlianzaSessionInfo& alianzaSession, std::shared_ptr<CPCAPI2::test::AlianzaApiManager> apiMgr, test::EventHandler* apiEvents);

      ApiManagerHolder createApiManager(const AlianzaAccountConfig& alianzaConfig, CPCAPI2::test::EventHandler*& apiEvents);
   };

   ApiManagerHolder SipuaAlianzaApiModuleTest::createApiManager(const AlianzaAccountConfig& alianzaConfig, CPCAPI2::test::EventHandler*& apiEvents)
   {
      PhoneInterface* phone = static_cast<PhoneInterface*>(Phone::create());
      dynamic_cast<PhoneInternal*>(phone)->initialize(LicenseInfo(), (PhoneHandler*)NULL);
      static_cast<PhoneInternal*>(phone)->setPhoneName("AlianzaApi");
      phone->setLoggingEnabled(&AutoTestsLogger::instance(), true);

      std::shared_ptr<CPCAPI2::test::AlianzaApiManager> apiMgr = std::make_shared<CPCAPI2::test::AlianzaApiManagerInterface>(phone);
      apiEvents = new CPCAPI2::test::EventHandler("AlianzaApi", dynamic_cast<CPCAPI2::AutoTestProcessor*>(apiMgr.get()));
      apiMgr->setHandler((CPCAPI2::test::AlianzaApiHandler*)0xDEADBEEF);
      apiMgr->start(alianzaConfig.api);

      return ApiManagerHolder(apiMgr, phone, apiEvents);
   }

   void SipuaAlianzaApiModuleTest::addNumberInPartition(const AlianzaAccountConfig& alianzaConfig, AlianzaSessionInfo& alianzaSession, std::shared_ptr<AlianzaApiManager> apiMgr, test::EventHandler* apiEvents)
   {
      std::shared_ptr<AlianzaSipUaInfo> ua = alianzaSession.getUa();
      bool success = false;
      int retries = 3;
      while ((success == false) && (retries > 0))
      {
         std::string messageBody("");
         std::string url("");
         AlianzaApiTestHelper::createMessageToAddNumberInPartition(alianzaConfig, alianzaSession, url, messageBody);

         AlianzaApiHttpResponseEvent evt;
         AlianzaApiHandle handle = 0;
         apiMgr->sendCreateMessage(url.c_str(), messageBody.c_str());
         cpcExpectEvent(apiEvents, "AlianzaApiHandler::onAlianzaApiHttpResponse", 10000, CPCAPI2::test::AlwaysTruePred(), handle, evt);

         if (evt.rc == 201)
         {
            retries = 0;
            std::shared_ptr<rapidjson::Document> jsonResponse(new rapidjson::Document);
            jsonResponse->Parse<0>(evt.response.c_str());
            if (!jsonResponse->HasParseError())
            {
               if (jsonResponse->HasMember("id"))
               {
                  const rapidjson::Value& moduleIdVal = (*jsonResponse)["id"];
                  if (moduleIdVal.IsString())
                  {
                     success = true;
                     ua->phoneId = (*jsonResponse)["id"].GetString();
                  }
               }
            }
         }
         else if ((evt.rc == 400) && (AlianzaApiTestHelper::isErrorPhoneNumberAlreadyExists(evt.response) || 
                                      AlianzaApiTestHelper::isErrorTNDisconnectEventInProgress(evt.response)) ||
                                      AlianzaApiTestHelper::isErrorPortRequiredTelephoneNumberNotInInventory(evt.response))
         {
            ua->initIdentity(alianzaConfig);
            retries--;
         }
         else
         {
            safeCout("SipuaAlianzaApiModuleTest::addNumberInPartition(): error in http response: status: " << evt.rc << " for http partition number create request, retries pending: " << retries);
            retries = 0;
         }
      }
      ASSERT_TRUE(success) << " aborting http partition number create request after multiple retries";

      // ((rc == 400) && (reasonMatch(response.str().c_str(), "PhoneNumberAlreadyExists"))
      // ((rc == 400) && (reasonMatch(response.str().c_str(), "TNDisconnectEventInProgress"))
   }

   void SipuaAlianzaApiModuleTest::deleteNumberInPartition(const AlianzaAccountConfig& alianzaConfig, AlianzaSessionInfo& alianzaSession, std::shared_ptr<AlianzaApiManager> apiMgr, test::EventHandler* apiEvents)
   {
      std::string messageBody("");
      std::string url("");
      std::shared_ptr<AlianzaSipUaInfo> ua = alianzaSession.getUa();
      AlianzaApiTestHelper::createMessageToDeleteNumberInPartition(alianzaConfig, alianzaSession, *ua, url, messageBody);

      apiMgr->sendDestroyMessage(url.c_str(), messageBody.c_str());
      assertNumberDeletedInPartition(apiEvents, ua->phoneId);
   }

   void SipuaAlianzaApiModuleTest::deleteNumberInAccount(const AlianzaAccountConfig& alianzaConfig, AlianzaSessionInfo& alianzaSession, std::shared_ptr<AlianzaApiManager> apiMgr, test::EventHandler* apiEvents)
   {
      std::string messageBody("");
      std::string url("");
      std::shared_ptr<AlianzaSipUaInfo> ua = alianzaSession.getUa();
      AlianzaApiTestHelper::createMessageToDeleteNumberInAccount(alianzaConfig, alianzaSession, *ua, url, messageBody);

      apiMgr->sendDestroyMessage(url.c_str(), messageBody.c_str());
      assertNumberDeletedInAccount(apiEvents, ua->phoneId);
   }

   void SipuaAlianzaApiModuleTest::addAccountInPartition(const AlianzaAccountConfig& alianzaConfig, AlianzaSessionInfo& alianzaSession, std::shared_ptr<AlianzaApiManager> apiMgr, test::EventHandler* apiEvents)
   {
      std::string messageBody("");
      std::string url("");
      AlianzaApiTestHelper::createMessageToAddAccountInPartition(alianzaConfig, alianzaSession, url, messageBody);

      apiMgr->sendCreateMessage(url.c_str(), messageBody.c_str());
      assertAccountCreatedInPartition(apiEvents, alianzaSession.accountId);
   }

   void SipuaAlianzaApiModuleTest::deleteAccountInPartition(const AlianzaAccountConfig& alianzaConfig, AlianzaSessionInfo& alianzaSession, std::shared_ptr<AlianzaApiManager> apiMgr, test::EventHandler* apiEvents)
   {
      std::string messageBody("");
      std::string url("");
      AlianzaApiTestHelper::createMessageToDeleteAccountInPartition(alianzaConfig, alianzaSession, url, messageBody);

      apiMgr->sendDestroyMessage(url.c_str(), messageBody.c_str());
      assertAccountDeletedInPartition(apiEvents, alianzaSession.accountId);
   }
   
   void SipuaAlianzaApiModuleTest::setAccountGroupName(const AlianzaAccountConfig& alianzaConfig, AlianzaSessionInfo& alianzaSession, std::shared_ptr<CPCAPI2::test::AlianzaApiManager> apiMgr, test::EventHandler* apiEvents)
   {
      std::string messageBody("");
      std::string url("");
      AlianzaApiTestHelper::createMessageToUpdateGroupNameInAccount(alianzaConfig, alianzaSession, url, messageBody);

      apiMgr->sendUpdateMessage(url.c_str(), messageBody.c_str());
      assertGroupNameUpdatedInAccount(apiEvents);
   }

   void SipuaAlianzaApiModuleTest::addUserInAccount(const AlianzaAccountConfig& alianzaConfig, AlianzaSessionInfo& alianzaSession, std::shared_ptr<AlianzaSipUaInfo> ua, std::shared_ptr<AlianzaApiManager> apiMgr, test::EventHandler* apiEvents)
   {
      bool success = false;
      int retries = 3;
      while ((success == false) && (retries > 0))
      {
         std::string messageBody("");
         std::string url("");

         AlianzaApiTestHelper::createMessageToAddUserInAccount(alianzaConfig, alianzaSession, *ua, url, messageBody);

         AlianzaApiHttpResponseEvent evt;
         AlianzaApiHandle handle = 0;
         apiMgr->sendCreateMessage(url.c_str(), messageBody.c_str());
         cpcExpectEvent(apiEvents, "AlianzaApiHandler::onAlianzaApiHttpResponse", 10000, CPCAPI2::test::AlwaysTruePred(), handle, evt);

         if (evt.rc == 201)
         {
            retries = 0;
            success = AlianzaApiTestHelper::extractDataFromAddUserInAccountResponse(evt.response, ua->userId, ua->voicemailId, ua->extension, alianzaSession.plan);
         }
         else if ((evt.rc == 400) && AlianzaApiTestHelper::isErrorDuplicateUsername(evt.response))
         {
            ua->initIdentity(alianzaConfig);
            retries--;
         }
         else
         {
            safeCout("SipuaAlianzaApiModuleTest::addUserInAccount(): error in http response: status: " << evt.rc << " for http user create request, retries pending: " << retries);
            retries = 0;
         }
      }
      ASSERT_TRUE(success) << " aborting http user create request after multiple retries";
   }

   void SipuaAlianzaApiModuleTest::authenticateUser(const AlianzaAccountConfig& alianzaConfig, AlianzaSessionInfo& alianzaSession, std::shared_ptr<AlianzaSipUaInfo> ua, std::shared_ptr<CPCAPI2::test::AlianzaApiManager> apiMgr, test::EventHandler* apiEvents)
   {
      std::string messageBody("");
      std::string url("");
      AlianzaApiTestHelper::createMessageForAuthorization(ua->username, ua->password, alianzaConfig, alianzaSession, url, messageBody);

      apiMgr->sendCreateMessage(url.c_str(), messageBody.c_str());
      assertAuthorizationSuccessWithToken(apiEvents, ua->userAuthToken);
      //safeCout("userAuthToken:" << ua->userAuthToken);
   }

   void SipuaAlianzaApiModuleTest::queryClientConfiguration(const AlianzaAccountConfig& alianzaConfig, AlianzaSessionInfo& alianzaSession, std::shared_ptr<AlianzaSipUaInfo> ua, std::shared_ptr<CPCAPI2::test::AlianzaApiManager> apiMgr, test::EventHandler* apiEvents)
   {
      std::string messageBody("");
      std::string url("");
      AlianzaApiTestHelper::createMessageToFetchConfigurationInAccount(alianzaConfig, alianzaSession, *ua, url, messageBody);

      //safeCout("userAuthToken:" << ua->userAuthToken);
      apiMgr->sendUpdateMessage(url.c_str(), messageBody.c_str(), ua->userAuthToken);
      assertConfigurationFetchedInAccount(apiEvents);
   }

   void SipuaAlianzaApiModuleTest::addNumberInAccount(const AlianzaAccountConfig& alianzaConfig, AlianzaSessionInfo& alianzaSession, std::shared_ptr<AlianzaApiManager> apiMgr, test::EventHandler* apiEvents)
   {
      std::string messageBody("");
      std::string url("");
      std::shared_ptr<AlianzaSipUaInfo> ua = alianzaSession.getUa();
      AlianzaApiTestHelper::createMessageToAddNumberInAccount(alianzaConfig, alianzaSession, *ua, url, messageBody);

      apiMgr->sendCreateMessage(url.c_str(), messageBody.c_str());
      assertNumberCreatedInAccount(apiEvents, ua->phoneId);
   }

   void SipuaAlianzaApiModuleTest::updateNumberInAccount(const AlianzaAccountConfig& alianzaConfig, AlianzaSessionInfo& alianzaSession, std::shared_ptr<AlianzaApiManager> apiMgr, test::EventHandler* apiEvents)
   {
      std::string messageBody("");
      std::string url("");
      std::shared_ptr<AlianzaSipUaInfo> ua = alianzaSession.getUa();
      AlianzaApiTestHelper::createMessageToUpdateNumberInAccount(alianzaConfig, alianzaSession, *ua, url, messageBody);

      apiMgr->sendUpdateMessage(url.c_str(), messageBody.c_str());
      assertNumberUpdatedInAccount(apiEvents, ua->phoneId);
   }

   void SipuaAlianzaApiModuleTest::updateNumberInUser(const AlianzaAccountConfig& alianzaConfig, AlianzaSessionInfo& alianzaSession, std::shared_ptr<AlianzaApiManager> apiMgr, test::EventHandler* apiEvents)
   {
      std::string messageBody("");
      std::string url("");
      std::shared_ptr<AlianzaSipUaInfo> ua = alianzaSession.getUa();
      AlianzaApiTestHelper::createMessageToUpdateUserInAccount(alianzaConfig, alianzaSession, *ua, url, messageBody);

      apiMgr->sendUpdateMessage(url.c_str(), messageBody.c_str());
      assertUserUpdatedInAccount(apiEvents, ua->phoneId);
   }

   void SipuaAlianzaApiModuleTest::checkNumberInAccount(const AlianzaAccountConfig& alianzaConfig, AlianzaSessionInfo& alianzaSession, std::shared_ptr<CPCAPI2::test::AlianzaApiManager> apiMgr, test::EventHandler* apiEvents)
   {
      std::string messageBody("");
      std::string url("");
      std::shared_ptr<AlianzaSipUaInfo> ua = alianzaSession.getUa();
      AlianzaApiTestHelper::createMessageToGetNumberInAccount(alianzaConfig, alianzaSession, *ua, url, messageBody);
      std::string status;

      for (int i = 0; i < 5; ++i)
      {
         apiEvents->clearCommands();
         apiMgr->sendQueryMessage(url.c_str(), messageBody.c_str());
         assertNumberInAccountStatusRetrieved(apiEvents, status);
         if (status == "ACTIVE")
         {
            break;
         }
         std::this_thread::sleep_for(std::chrono::seconds(3));
      }
      ASSERT_TRUE(status == "ACTIVE") << "Number in account status is " << status;
   }

   TEST_F(SipuaAlianzaApiModuleTest, Authorize)
   {
      GTEST_SKIP_REPRO();
      AlianzaAccountConfig alianzaConfig;
      ASSERT_TRUE(alianzaConfig.importConfig());

      AlianzaSessionInfo alianzaSession("alice");
      alianzaSession.init(alianzaConfig);
      CPCAPI2::test::EventHandler* apiEvents = NULL;
      ApiManagerHolder apiMgrHolder = createApiManager(alianzaConfig, apiEvents); 
      ASSERT_TRUE(apiMgrHolder.apiMgr() != NULL);

      apiMgrHolder.apiMgr()->authorize();
      assertAuthorizationSuccess(apiEvents);
   }

   TEST_F(SipuaAlianzaApiModuleTest, CreateAndDestroyPartitionNumber)
   {
      GTEST_SKIP_REPRO();
      AlianzaAccountConfig alianzaConfig;
      ASSERT_TRUE(alianzaConfig.importConfig());

      AlianzaSessionInfo alianzaSession("alice");
      alianzaSession.init(alianzaConfig);
      CPCAPI2::test::EventHandler* apiEvents = NULL;
      ApiManagerHolder apiMgrHolder = createApiManager(alianzaConfig, apiEvents); 
      ASSERT_TRUE(apiMgrHolder.apiMgr() != NULL);

      apiMgrHolder.apiMgr()->authorize();
      assertAuthorizationSuccess(apiEvents);

      /*
      {
         std::string messageBody("");
         std::string authToken("");
         std::string url("");
         AlianzaApiTestHelper::createMessageForAuthorization(mConfig, url, messageBody);

         apiMgrHolder.apiMgr()->sendMessage(url.c_str(), messageBody.c_str());
         assertAuthorizationSuccessWithToken(apiEvents, authToken);
      }
      */

      std::shared_ptr<AlianzaSipUaInfo> ua = alianzaSession.getUa();

      ASSERT_NO_FATAL_FAILURE(addNumberInPartition(alianzaConfig, alianzaSession, apiMgrHolder.apiMgr(), apiEvents));

      {
         std::string messageBody("");
         std::string url("");
         AlianzaApiTestHelper::createMessageToDeleteNumberInPartition(alianzaConfig, alianzaSession, *ua, url, messageBody);

         apiMgrHolder.apiMgr()->sendDestroyMessage(url.c_str(), messageBody.c_str());
         assertNumberDeletedInPartition(apiEvents, ua->phoneId);
      }
   }

   TEST_F(SipuaAlianzaApiModuleTest, QueryPartitionNumber)
   {
      GTEST_SKIP_REPRO();
      AlianzaAccountConfig alianzaConfig;
      ASSERT_TRUE(alianzaConfig.importConfig());

      AlianzaSessionInfo alianzaSession("alice");
      alianzaSession.init(alianzaConfig);
      CPCAPI2::test::EventHandler* apiEvents = NULL;
      ApiManagerHolder apiMgrHolder = createApiManager(alianzaConfig, apiEvents); 
      ASSERT_TRUE(apiMgrHolder.apiMgr() != NULL);

      apiMgrHolder.apiMgr()->authorize();
      assertAuthorizationSuccess(apiEvents);

      std::shared_ptr<AlianzaSipUaInfo> ua = alianzaSession.getUa();

      {
         std::string messageBody("");
         std::string url("");
         AlianzaApiTestHelper::createMessageToGetNumberInPartition(alianzaConfig, alianzaSession, url, messageBody);

         apiMgrHolder.apiMgr()->sendQueryMessage(url.c_str(), messageBody.c_str());
         assertNumberDoesNotExistInPartition(apiEvents, ua->phoneNumber);
      }

      ASSERT_NO_FATAL_FAILURE(addNumberInPartition(alianzaConfig, alianzaSession, apiMgrHolder.apiMgr(), apiEvents));

      {
         std::string messageBody("");
         std::string url("");
         AlianzaApiTestHelper::createMessageToGetNumberInPartition(alianzaConfig, alianzaSession, url, messageBody);

         apiMgrHolder.apiMgr()->sendQueryMessage(url.c_str(), messageBody.c_str());
         assertNumberRetrievedFromPartition(apiEvents, ua->phoneNumber);
      }

      {
         std::string messageBody("");
         std::string url("");
         AlianzaApiTestHelper::createMessageToDeleteNumberInPartition(alianzaConfig, alianzaSession, *ua, url, messageBody);

         apiMgrHolder.apiMgr()->sendDestroyMessage(url.c_str(), messageBody.c_str());
         assertNumberDeletedInPartition(apiEvents, ua->phoneId);
      }

      {
         std::string messageBody("");
         std::string url("");
         AlianzaApiTestHelper::createMessageToGetNumberInPartition(alianzaConfig, alianzaSession, url, messageBody);

         apiMgrHolder.apiMgr()->sendQueryMessage(url.c_str(), messageBody.c_str());
         assertNumberDoesNotExistInPartition(apiEvents, ua->phoneNumber);
      }
   }

   TEST_F(SipuaAlianzaApiModuleTest, CreateAndDestroyAccount)
   {
      GTEST_SKIP_REPRO();
      AlianzaAccountConfig alianzaConfig;
      ASSERT_TRUE(alianzaConfig.importConfig());

      AlianzaSessionInfo alianzaSession("alice");
      alianzaSession.init(alianzaConfig);
      CPCAPI2::test::EventHandler* apiEvents = NULL;
      ApiManagerHolder apiMgrHolder = createApiManager(alianzaConfig, apiEvents); 
      ASSERT_TRUE(apiMgrHolder.apiMgr() != NULL);

      apiMgrHolder.apiMgr()->authorize();
      assertAuthorizationSuccess(apiEvents);

      std::shared_ptr<AlianzaSipUaInfo> ua = alianzaSession.getUa();

      ASSERT_NO_FATAL_FAILURE(addNumberInPartition(alianzaConfig, alianzaSession, apiMgrHolder.apiMgr(), apiEvents));

      {
         std::string messageBody("");
         std::string url("");
         AlianzaApiTestHelper::createMessageToAddAccountInPartition(alianzaConfig, alianzaSession, url, messageBody);

         apiMgrHolder.apiMgr()->sendCreateMessage(url.c_str(), messageBody.c_str());
         assertAccountCreatedInPartition(apiEvents, alianzaSession.accountId);
      }

      ASSERT_NO_FATAL_FAILURE(setAccountGroupName(alianzaConfig, alianzaSession, apiMgrHolder.apiMgr(), apiEvents));
      ASSERT_NO_FATAL_FAILURE(addUserInAccount(alianzaConfig, alianzaSession, ua, apiMgrHolder.apiMgr(), apiEvents));
      ASSERT_NO_FATAL_FAILURE(authenticateUser(alianzaConfig, alianzaSession, ua, apiMgrHolder.apiMgr(), apiEvents));
      ASSERT_NO_FATAL_FAILURE(queryClientConfiguration(alianzaConfig, alianzaSession, ua, apiMgrHolder.apiMgr(), apiEvents));

      std::this_thread::sleep_for(std::chrono::milliseconds(3000));

      {
         std::string messageBody("");
         std::string url("");
         AlianzaApiTestHelper::createMessageToDeleteAccountInPartition(alianzaConfig, alianzaSession, url, messageBody);

         apiMgrHolder.apiMgr()->sendDestroyMessage(url.c_str(), messageBody.c_str());
         assertAccountDeletedInPartition(apiEvents, alianzaSession.accountId);
      }

      {
         std::string messageBody("");
         std::string url("");
         AlianzaApiTestHelper::createMessageToDeleteNumberInPartition(alianzaConfig, alianzaSession, *ua, url, messageBody);

         apiMgrHolder.apiMgr()->sendDestroyMessage(url.c_str(), messageBody.c_str());
         assertNumberDeletedInPartition(apiEvents, ua->phoneId);
      }
   }

   // This test case was added for SCORE-1305
   TEST_F(SipuaAlianzaApiModuleTest, CreateAndDestroyAccountWithConfigServiceApi)
   {
      GTEST_SKIP_REPRO();
      AlianzaAccountConfig alianzaConfig;
      ASSERT_TRUE(alianzaConfig.importConfig());

      AlianzaSessionInfo alianzaSession("alice");
      alianzaSession.init(alianzaConfig);
      CPCAPI2::test::EventHandler* apiEvents = NULL;
      ApiManagerHolder apiMgrHolder = createApiManager(alianzaConfig, apiEvents); 
      ASSERT_TRUE(apiMgrHolder.apiMgr() != NULL);

      apiMgrHolder.apiMgr()->authorize();
      assertAuthorizationSuccess(apiEvents);

      std::shared_ptr<AlianzaSipUaInfo> ua = alianzaSession.getUa();

      ASSERT_NO_FATAL_FAILURE(addNumberInPartition(alianzaConfig, alianzaSession, apiMgrHolder.apiMgr(), apiEvents));

      {
         std::string messageBody("");
         std::string url("");
         AlianzaApiTestHelper::createMessageToAddAccountInPartition(alianzaConfig, alianzaSession, url, messageBody);

         apiMgrHolder.apiMgr()->sendCreateMessage(url.c_str(), messageBody.c_str());
         assertAccountCreatedInPartition(apiEvents, alianzaSession.accountId);
      }

      {
         std::string messageBody("");
         std::string url("");
         AlianzaApiTestHelper::createMessageToUpdateGroupNameInAccount(alianzaConfig, alianzaSession, url, messageBody);

         apiMgrHolder.apiMgr()->sendUpdateMessage(url.c_str(), messageBody.c_str());
         assertGroupNameUpdatedInAccount(apiEvents);
      }

      ASSERT_NO_FATAL_FAILURE(addUserInAccount(alianzaConfig, alianzaSession, ua, apiMgrHolder.apiMgr(), apiEvents));

      std::string userAuthToken;
      {
         std::string messageBody("");
         std::string url("");
         AlianzaApiTestHelper::createMessageForAuthorization(ua->username, ua->password, alianzaConfig, alianzaSession, url, messageBody);

         apiMgrHolder.apiMgr()->sendCreateMessage(url.c_str(), messageBody.c_str());
         assertAuthorizationSuccessWithToken(apiEvents, userAuthToken);
      }

      {
         std::string messageBody("");
         std::string url("");
         AlianzaApiTestHelper::createMessageToFetchConfigurationInAccount(alianzaConfig, alianzaSession, *ua, url, messageBody);

         apiMgrHolder.apiMgr()->sendUpdateMessage(url.c_str(), messageBody.c_str(), userAuthToken);
         assertConfigurationFetchedInAccount(apiEvents);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(3000));

      {
         std::string messageBody("");
         std::string url("");
         AlianzaApiTestHelper::createMessageToDeleteAccountInPartition(alianzaConfig, alianzaSession, url, messageBody);

         apiMgrHolder.apiMgr()->sendDestroyMessage(url.c_str(), messageBody.c_str());
         assertAccountDeletedInPartition(apiEvents, alianzaSession.accountId);
      }

      {
         std::string messageBody("");
         std::string url("");
         AlianzaApiTestHelper::createMessageToDeleteNumberInPartition(alianzaConfig, alianzaSession, *ua, url, messageBody);

         apiMgrHolder.apiMgr()->sendDestroyMessage(url.c_str(), messageBody.c_str());
         assertNumberDeletedInPartition(apiEvents, ua->phoneId);
      }
   }

   TEST_F(SipuaAlianzaApiModuleTest, CreateAndDestroyAccountNumber)
   {
      GTEST_SKIP_REPRO();
      AlianzaAccountConfig alianzaConfig;
      ASSERT_TRUE(alianzaConfig.importConfig());

      AlianzaSessionInfo alianzaSession("alice");
      alianzaSession.init(alianzaConfig);
      CPCAPI2::test::EventHandler* apiEvents = NULL;
      ApiManagerHolder apiMgrHolder = createApiManager(alianzaConfig, apiEvents); 
      ASSERT_TRUE(apiMgrHolder.apiMgr() != NULL);

      apiMgrHolder.apiMgr()->authorize();
      assertAuthorizationSuccess(apiEvents);

      std::shared_ptr<AlianzaSipUaInfo> ua = alianzaSession.getUa();

      ASSERT_NO_FATAL_FAILURE(addNumberInPartition(alianzaConfig, alianzaSession, apiMgrHolder.apiMgr(), apiEvents));

      {
         std::string messageBody("");
         std::string url("");
         AlianzaApiTestHelper::createMessageToAddAccountInPartition(alianzaConfig, alianzaSession, url, messageBody);

         apiMgrHolder.apiMgr()->sendCreateMessage(url.c_str(), messageBody.c_str());
         assertAccountCreatedInPartition(apiEvents, alianzaSession.accountId);
      }

      ASSERT_NO_FATAL_FAILURE(setAccountGroupName(alianzaConfig, alianzaSession, apiMgrHolder.apiMgr(), apiEvents));
      ASSERT_NO_FATAL_FAILURE(addUserInAccount(alianzaConfig, alianzaSession, ua, apiMgrHolder.apiMgr(), apiEvents));
      ASSERT_NO_FATAL_FAILURE(authenticateUser(alianzaConfig, alianzaSession, ua, apiMgrHolder.apiMgr(), apiEvents));
      ASSERT_NO_FATAL_FAILURE(queryClientConfiguration(alianzaConfig, alianzaSession, ua, apiMgrHolder.apiMgr(), apiEvents));

      {
         std::string messageBody("");
         std::string url("");
         AlianzaApiTestHelper::createMessageToAddNumberInAccount(alianzaConfig, alianzaSession, *ua, url, messageBody);

         apiMgrHolder.apiMgr()->sendCreateMessage(url.c_str(), messageBody.c_str());
         assertNumberCreatedInAccount(apiEvents, ua->phoneId);
      }

      ASSERT_NO_FATAL_FAILURE(checkNumberInAccount(alianzaConfig, alianzaSession, apiMgrHolder.apiMgr(), apiEvents));

      {
         std::string messageBody("");
         std::string url("");
         AlianzaApiTestHelper::createMessageToUpdateNumberInAccount(alianzaConfig, alianzaSession, *ua, url, messageBody);

         apiMgrHolder.apiMgr()->sendUpdateMessage(url.c_str(), messageBody.c_str());
         assertNumberUpdatedInAccount(apiEvents, ua->phoneId);
      }

      {
         std::string messageBody("");
         std::string url("");
         AlianzaApiTestHelper::createMessageToUpdateUserInAccount(alianzaConfig, alianzaSession, *ua, url, messageBody);

         apiMgrHolder.apiMgr()->sendUpdateMessage(url.c_str(), messageBody.c_str());
         assertUserUpdatedInAccount(apiEvents, ua->phoneId);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(3000));

      {
         std::string messageBody("");
         std::string url("");
         AlianzaApiTestHelper::createMessageToDeleteNumberInAccount(alianzaConfig, alianzaSession, *ua, url, messageBody);

         apiMgrHolder.apiMgr()->sendDestroyMessage(url.c_str(), messageBody.c_str());
         assertNumberDeletedInAccount(apiEvents, ua->phoneId);
      }

      {
         std::string messageBody("");
         std::string url("");
         AlianzaApiTestHelper::createMessageToDeleteAccountInPartition(alianzaConfig, alianzaSession, url, messageBody);

         apiMgrHolder.apiMgr()->sendDestroyMessage(url.c_str(), messageBody.c_str());
         assertAccountDeletedInPartition(apiEvents, alianzaSession.accountId);
      }
   }

   TEST_F(SipuaAlianzaApiModuleTest, CreateAndDestroyAccountWithExtension)
   {
      GTEST_SKIP_REPRO();
      AlianzaAccountConfig alianzaConfig;
      ASSERT_TRUE(alianzaConfig.importConfig());

      AlianzaSessionInfo alianzaSession("alice");
      alianzaSession.init(alianzaConfig);
      CPCAPI2::test::EventHandler* apiEvents = NULL;
      ApiManagerHolder apiMgrHolder = createApiManager(alianzaConfig, apiEvents);
      ASSERT_TRUE(apiMgrHolder.apiMgr() != NULL);

      apiMgrHolder.apiMgr()->authorize();
      assertAuthorizationSuccess(apiEvents);

      {
         std::string messageBody("");
         std::string url("");
         AlianzaApiTestHelper::createMessageToAddAccountInPartition(alianzaConfig, alianzaSession, url, messageBody);

         apiMgrHolder.apiMgr()->sendCreateMessage(url.c_str(), messageBody.c_str());
         assertAccountCreatedInPartition(apiEvents, alianzaSession.accountId);
      }

      ASSERT_NO_FATAL_FAILURE(setAccountGroupName(alianzaConfig, alianzaSession, apiMgrHolder.apiMgr(), apiEvents));

      std::shared_ptr<AlianzaSipUaInfo> ua = alianzaSession.getUa();
 
      ASSERT_NO_FATAL_FAILURE(addUserInAccount(alianzaConfig, alianzaSession, ua, apiMgrHolder.apiMgr(), apiEvents));
      ASSERT_NO_FATAL_FAILURE(authenticateUser(alianzaConfig, alianzaSession, ua, apiMgrHolder.apiMgr(), apiEvents));
      ASSERT_NO_FATAL_FAILURE(queryClientConfiguration(alianzaConfig, alianzaSession, ua, apiMgrHolder.apiMgr(), apiEvents));


      // Add an additional UA
      std::shared_ptr<AlianzaSipUaInfo> ua2 = alianzaSession.addUa(alianzaConfig);

      ASSERT_NO_FATAL_FAILURE(addUserInAccount(alianzaConfig, alianzaSession, ua2, apiMgrHolder.apiMgr(), apiEvents));
      ASSERT_NO_FATAL_FAILURE(authenticateUser(alianzaConfig, alianzaSession, ua, apiMgrHolder.apiMgr(), apiEvents));
      ASSERT_NO_FATAL_FAILURE(queryClientConfiguration(alianzaConfig, alianzaSession, ua, apiMgrHolder.apiMgr(), apiEvents));


      // Adding this delay as it take some time before the number addition traverses accross
      // the different micro-services, and before the caller-id update can occur. Attempting
      // an update without the delay, results in a 400 response (InvalidStatus).
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      {
         std::string messageBody("");
         std::string url("");
         AlianzaApiTestHelper::createMessageToDeleteAccountInPartition(alianzaConfig, alianzaSession, url, messageBody);

         apiMgrHolder.apiMgr()->sendDestroyMessage(url.c_str(), messageBody.c_str());
         assertAccountDeletedInPartition(apiEvents, alianzaSession.accountId);
      }
   }

   TEST_F(SipuaAlianzaApiModuleTest, VerifyQueries)
   {
      GTEST_SKIP_REPRO();
      AlianzaAccountConfig alianzaConfig;
      ASSERT_TRUE(alianzaConfig.importConfig());

      AlianzaSessionInfo alianzaSession("alice");
      alianzaSession.init(alianzaConfig);
      CPCAPI2::test::EventHandler* apiEvents = NULL;
      ApiManagerHolder apiMgrHolder = createApiManager(alianzaConfig, apiEvents); 
      ASSERT_TRUE(apiMgrHolder.apiMgr() != NULL);

      apiMgrHolder.apiMgr()->authorize();
      assertAuthorizationSuccess(apiEvents);

/*
      {
         std::string messageBody("");
         std::string url("");
         AlianzaApiTestHelper::createMessageToGetAccountsInPartition(mConfig, mSipUa, url, messageBody);

         apiMgrHolder.apiMgr()->sendQueryMessage(url.c_str(), messageBody.c_str());
         assertAccountsRetrievedFromPartition(apiEvents);
      }
*/

      ASSERT_NO_FATAL_FAILURE(addNumberInPartition(alianzaConfig, alianzaSession, apiMgrHolder.apiMgr(), apiEvents));
      ASSERT_NO_FATAL_FAILURE(addAccountInPartition(alianzaConfig, alianzaSession, apiMgrHolder.apiMgr(), apiEvents));
      ASSERT_NO_FATAL_FAILURE(setAccountGroupName(alianzaConfig, alianzaSession, apiMgrHolder.apiMgr(), apiEvents));

      std::shared_ptr<AlianzaSipUaInfo> ua = alianzaSession.getUa();
      ASSERT_NO_FATAL_FAILURE(addUserInAccount(alianzaConfig, alianzaSession, ua, apiMgrHolder.apiMgr(), apiEvents));
      ASSERT_NO_FATAL_FAILURE(authenticateUser(alianzaConfig, alianzaSession, ua, apiMgrHolder.apiMgr(), apiEvents));
      ASSERT_NO_FATAL_FAILURE(queryClientConfiguration(alianzaConfig, alianzaSession, ua, apiMgrHolder.apiMgr(), apiEvents));
      ASSERT_NO_FATAL_FAILURE(addNumberInAccount(alianzaConfig, alianzaSession, apiMgrHolder.apiMgr(), apiEvents));
      ASSERT_NO_FATAL_FAILURE(checkNumberInAccount(alianzaConfig, alianzaSession, apiMgrHolder.apiMgr(), apiEvents));
      ASSERT_NO_FATAL_FAILURE(updateNumberInAccount(alianzaConfig, alianzaSession, apiMgrHolder.apiMgr(), apiEvents));
      ASSERT_NO_FATAL_FAILURE(updateNumberInUser(alianzaConfig, alianzaSession, apiMgrHolder.apiMgr(), apiEvents));
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));

      {
         std::string messageBody("");
         std::string url("");
         AlianzaApiTestHelper::createMessageToGetAccount(alianzaConfig, alianzaSession, url, messageBody);

         apiMgrHolder.apiMgr()->sendQueryMessage(url.c_str(), messageBody.c_str());
         assertAccountRetrievedFromPartition(apiEvents);
      }

      {
         std::string messageBody("");
         std::string url("");
         AlianzaApiTestHelper::createMessageToGetUserInAccount(alianzaConfig, alianzaSession, *ua, url, messageBody);

         apiMgrHolder.apiMgr()->sendQueryMessage(url.c_str(), messageBody.c_str());
         assertUserRetrievedFromAccount(apiEvents);
      }


      {
         std::string messageBody("");
         std::string url("");
         AlianzaApiTestHelper::createMessageToGetNumberInAccount(alianzaConfig, alianzaSession, *ua, url, messageBody);

         apiMgrHolder.apiMgr()->sendQueryMessage(url.c_str(), messageBody.c_str());
         assertNumberRetrievedFromAccount(apiEvents, ua->phoneId);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      ASSERT_NO_FATAL_FAILURE(deleteNumberInAccount(alianzaConfig, alianzaSession, apiMgrHolder.apiMgr(), apiEvents));
      ASSERT_NO_FATAL_FAILURE(deleteAccountInPartition(alianzaConfig, alianzaSession, apiMgrHolder.apiMgr(), apiEvents));
   }
}

