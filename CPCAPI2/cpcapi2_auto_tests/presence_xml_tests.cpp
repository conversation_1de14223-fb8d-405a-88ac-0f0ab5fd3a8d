

#if 0

#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#include <gtest/gtest.h>
#include <cpcapi2.h>

#include "../../impl/presence/SipPresenceModelOps.h"
#include "../../impl/presence/SipPresenceXmlEncoder.h"

#include "cpcapi2utils.h"

#include <map>

using namespace CPCAPI2;
using namespace CPCAPI2::SipPresence;
using namespace std;
using ::testing::Test;
using ::testing::Types;

namespace {

/*
 * These presence tests check the XML <-> C++ struct mappings
 * Each type has expected mappings between xml and objects which are tested in both directions
 * - xmlToObj parses the known XML string and compares it to the expected object
 * - objToXml encodes the known object and compares it to the expected XML
 *
 * GTest 'typed tests' are used to remove duplicate code and require only the generateTuples method
 * to be implemented for each type being tested. See: https://code.google.com/p/googletest/wiki/AdvancedGuide#Typed_Tests
 */

template<typename T>
struct XmlTuple
{
   XmlTuple(const string& xml, const T& obj): xml(xml),obj(obj){}
   const string xml;
   const T obj;
};

template<typename T>
class XmlTuples
{
public:
   void add(const char* xml, const T& obj)
   {
      m_tuples.push_back(XmlTuple<T>(string(xml), obj));
   }
   const vector<XmlTuple<T>>& tuples() const
   {
      return m_tuples;
   }
private:
   vector<XmlTuple<T>> m_tuples;
};

template<typename T> void generateTuples(XmlTuples<T>& tests);

template<typename T>
class PresenceXmlTest : public Test
{
public:
   PresenceXmlTest()
   {
      generateTuples<T>(m_tests);
   }
	virtual ~PresenceXmlTest() {}
   XmlTuples<T> m_tests;
};

typedef Types < Note, 
					 Activity, 
					 Activities, 
					 Mood, 
					 Moods, 
					 AudioIsType, 
					 VideoIsType, 
					 TextIsType, 
					 PlaceIs, 
					 PlaceType, 
					 PrivacyType, 
					 Privacy, 
					 RelationshipValue, 
					 Relationship, 
					 ServiceClassType, 
					 ServiceClass, 
					 SphereType, 
					 Sphere, 
					 StatusIcon, 
					 TimeOffset, 
					 UserInput, 
					 Device, 
					 Person, 
					 Contact, 
					 Status, 
					 Tuple, 
					 Presence> Implementations;

TYPED_TEST_CASE(PresenceXmlTest , Implementations );

TYPED_TEST(PresenceXmlTest, TestObjToXml)
{
   std::ostringstream out;
   XmlEncoder encoder(out);
   for(size_t i=0; i<this->m_tests.tuples().size(); i++)
   {
      out.clear();
      out.str(string());
      const XmlTuple<TypeParam>& test = this->m_tests.tuples()[i];
      encoder.encode(test.obj);
      std::string result = (out.str());
      ASSERT_EQ(test.xml, result);
   }
}

TYPED_TEST(PresenceXmlTest, TestXmlToObj)
{
   XmlParser parser;
   for(size_t i=0; i<this->m_tests.tuples().size(); i++)
   {
      const XmlTuple<TypeParam>& test = this->m_tests.tuples()[i];
      TypeParam obj;
      XmlRoot xmlRoot(test.xml);
      if(!xmlRoot.root())
         FAIL(); // XML document invalid
      if(!parser.parse(xmlRoot.root(), obj))
         FAIL(); // Parse failed (returned false)
      if(!(obj == test.obj))
         FAIL(); // xmlToObj objects differ
   }
}

cpc::string toStr(const char* str)
{
   return str ? cpc::string(str) : cpc::string();
}

Optional<cpc::string> newOptionalString(const char* str)
{
   Optional<cpc::string> result;
   result.present = !!str;
   result.value = toStr(str);
   return result;
}

FromUntil newFromUntil(const char* from, const char* until)
{
   FromUntil result;
   result.from = newOptionalString(from);
   result.until = newOptionalString(until);
   return result;
}

Note newNote(const char* text, const char* lang){
   Note note;
   note.text = toStr(text);
   note.lang = newOptionalString(lang);
   return note;
}
template<>
void generateTuples<Note>(XmlTuples<Note>& tests)
{
   tests.add("<note xml:lang=\"en\">abc</note>", newNote("abc", "en"));
   tests.add("<note>abc</note>", newNote("abc", NULL));
   tests.add("<note></note>", newNote("", NULL));
   tests.add("<note>&lt;escape message&gt;</note>", newNote("<escape message>", NULL));
   tests.add("<note>escape &quot;test&apos;d &lt;&gt; test&amp; end</note>", newNote("escape \"test'd <> test& end", NULL));   
}

Activity newActivity(ActivityType type){
   Activity activity;
   activity.activity = type;
   return activity;
}
template<>
void generateTuples<Activity>(XmlTuples<Activity>& tests)
{
   tests.add("<rpid:appointment/>", newActivity(ActivityType_Appointment));
   tests.add("<rpid:away/>", newActivity(ActivityType_Away));
   tests.add("<rpid:breakfast/>", newActivity(ActivityType_Breakfast));
   tests.add("<rpid:busy/>", newActivity(ActivityType_Busy));
   tests.add("<rpid:dinner/>", newActivity(ActivityType_Dinner));
   tests.add("<rpid:holiday/>", newActivity(ActivityType_Holiday));
   tests.add("<rpid:in-transit/>", newActivity(ActivityType_InTransit));
   tests.add("<rpid:looking-for-work/>", newActivity(ActivityType_LookingForWork));
   tests.add("<rpid:meal/>", newActivity(ActivityType_Meal));
   tests.add("<rpid:on-the-phone/>", newActivity(ActivityType_OnThePhone));
   tests.add("<rpid:performance/>", newActivity(ActivityType_Performance));
   tests.add("<rpid:permanent-absence/>", newActivity(ActivityType_PermanentAbsence));
   tests.add("<rpid:playing/>", newActivity(ActivityType_Playing));
   tests.add("<rpid:presentation/>", newActivity(ActivityType_Presentation));
   tests.add("<rpid:shopping/>", newActivity(ActivityType_Shopping));
   tests.add("<rpid:sleeping/>", newActivity(ActivityType_Sleeping));
   tests.add("<rpid:spectator/>", newActivity(ActivityType_Spectator));
   tests.add("<rpid:steering/>", newActivity(ActivityType_Steering));
   tests.add("<rpid:travel/>", newActivity(ActivityType_Travel));
   tests.add("<rpid:tv/>", newActivity(ActivityType_Tv));
   tests.add("<rpid:vacation/>", newActivity(ActivityType_Vacation));
   tests.add("<rpid:working/>", newActivity(ActivityType_Working));
   tests.add("<rpid:worship/>", newActivity(ActivityType_Worship));
   tests.add("<rpid:unknown/>", newActivity(ActivityType_Unknown));
   Activity other = newActivity(ActivityType_Other);
   other.otherValue = newNote("abc","en");
   tests.add("<rpid:other xml:lang=\"en\">abc</rpid:other>", other);
}

template<>
void generateTuples<Activities>(XmlTuples<Activities>& tests)
{
   Activities obj;
   obj.fromUntil = newFromUntil("2005-05-30T12:00:00+05:00", "2005-05-30T17:00:00+05:00");
   obj.notes.push_back(newNote("Far away", NULL));
   obj.activities.push_back(newActivity(ActivityType_Away));
   const char* xml = "<rpid:activities from=\"2005-05-30T12:00:00+05:00\" until=\"2005-05-30T17:00:00+05:00\">\n\
<note>Far away</note>\n\
<rpid:away/>\n\
</rpid:activities>";
   tests.add(xml, obj);
}

Mood newMood(MoodValue type){
   Mood mood;
   mood.mood = type;
   return mood;
}
template<>
void generateTuples<Mood>(XmlTuples<Mood>& tests)
{
   tests.add("<rpid:afraid/>", newMood(MoodValue_Afraid));
   tests.add("<rpid:amazed/>", newMood(MoodValue_Amazed));
   tests.add("<rpid:angry/>", newMood(MoodValue_Angry));
   tests.add("<rpid:annoyed/>", newMood(MoodValue_Annoyed));
   tests.add("<rpid:anxious/>", newMood(MoodValue_Anxious));
   tests.add("<rpid:ashamed/>", newMood(MoodValue_Ashamed));
   tests.add("<rpid:bored/>", newMood(MoodValue_Bored));
   tests.add("<rpid:brave/>", newMood(MoodValue_Brave));
   tests.add("<rpid:calm/>", newMood(MoodValue_Calm));
   tests.add("<rpid:cold/>", newMood(MoodValue_Cold));
   tests.add("<rpid:confused/>", newMood(MoodValue_Confused));
   tests.add("<rpid:contented/>", newMood(MoodValue_Contented));
   tests.add("<rpid:cranky/>", newMood(MoodValue_Cranky));
   tests.add("<rpid:curious/>", newMood(MoodValue_Curious));
   tests.add("<rpid:depressed/>", newMood(MoodValue_Depressed));
   tests.add("<rpid:disappointed/>", newMood(MoodValue_Disappointed));
   tests.add("<rpid:disgusted/>", newMood(MoodValue_Disgusted));
   tests.add("<rpid:distracted/>", newMood(MoodValue_Distracted));
   tests.add("<rpid:embarrassed/>", newMood(MoodValue_Embarrassed));
   tests.add("<rpid:excited/>", newMood(MoodValue_Excited));
   tests.add("<rpid:flirtatious/>", newMood(MoodValue_Flirtatious));
   tests.add("<rpid:frustrated/>", newMood(MoodValue_Frustrated));
   tests.add("<rpid:grumpy/>", newMood(MoodValue_Grumpy));
   tests.add("<rpid:guilty/>", newMood(MoodValue_Guilty));
   tests.add("<rpid:happy/>", newMood(MoodValue_Happy));
   tests.add("<rpid:hot/>", newMood(MoodValue_Hot));
   tests.add("<rpid:humbled/>", newMood(MoodValue_Humbled));
   tests.add("<rpid:humiliated/>", newMood(MoodValue_Humiliated));
   tests.add("<rpid:hungry/>", newMood(MoodValue_Hungry));
   tests.add("<rpid:hurt/>", newMood(MoodValue_Hurt));
   tests.add("<rpid:impressed/>", newMood(MoodValue_Impressed));
   tests.add("<rpid:in_awe/>", newMood(MoodValue_InAwe));
   tests.add("<rpid:in_love/>", newMood(MoodValue_InLove));
   tests.add("<rpid:indignant/>", newMood(MoodValue_Indignant));
   tests.add("<rpid:interested/>", newMood(MoodValue_Interested));
   tests.add("<rpid:invincible/>", newMood(MoodValue_Invincible));
   tests.add("<rpid:jealous/>", newMood(MoodValue_Jealous));
   tests.add("<rpid:lonely/>", newMood(MoodValue_Lonely));
   tests.add("<rpid:mean/>", newMood(MoodValue_Mean));
   tests.add("<rpid:moody/>", newMood(MoodValue_Moody));
   tests.add("<rpid:nervous/>", newMood(MoodValue_Nervous));
   tests.add("<rpid:neutral/>", newMood(MoodValue_Neutral));
   tests.add("<rpid:offended/>", newMood(MoodValue_Offended));
   tests.add("<rpid:playful/>", newMood(MoodValue_Playful));
   tests.add("<rpid:proud/>", newMood(MoodValue_Proud));
   tests.add("<rpid:relieved/>", newMood(MoodValue_Relieved));
   tests.add("<rpid:remorseful/>", newMood(MoodValue_Remorseful));
   tests.add("<rpid:restless/>", newMood(MoodValue_Restless));
   tests.add("<rpid:sad/>", newMood(MoodValue_Sad));
   tests.add("<rpid:sarcastic/>", newMood(MoodValue_Sarcastic));
   tests.add("<rpid:serious/>", newMood(MoodValue_Serious));
   tests.add("<rpid:shocked/>", newMood(MoodValue_Shocked));
   tests.add("<rpid:shy/>", newMood(MoodValue_Shy));
   tests.add("<rpid:sick/>", newMood(MoodValue_Sick));
   tests.add("<rpid:sleepy/>", newMood(MoodValue_Sleepy));
   tests.add("<rpid:stressed/>", newMood(MoodValue_Stressed));
   tests.add("<rpid:surprised/>", newMood(MoodValue_Surprised));
   tests.add("<rpid:thirsty/>", newMood(MoodValue_Thirsty));
   tests.add("<rpid:worried/>", newMood(MoodValue_Worried));
   tests.add("<rpid:unknown/>", newMood(MoodValue_Unknown));
   Mood other = newMood(MoodValue_Other);
   other.otherValue = newNote("abc","en");
   tests.add("<rpid:other xml:lang=\"en\">abc</rpid:other>", other);
}

template<>
void generateTuples<Moods>(XmlTuples<Moods>& tests)
{
   Moods obj;
   obj.notes.push_back(newNote("I'm ready for the bar BOF!", NULL));
   obj.moods.push_back(newMood(MoodValue_Sleepy));
   obj.moods.push_back(newMood(MoodValue_Thirsty));
   const char* xml = "<rpid:mood>\n\
<rpid:note>I&apos;m ready for the bar BOF!</rpid:note>\n\
<rpid:sleepy/>\n\
<rpid:thirsty/>\n\
</rpid:mood>";
   tests.add(xml, obj);
}

template<>
void generateTuples<AudioIsType>(XmlTuples<AudioIsType>& tests)
{
   tests.add("<rpid:audio>\n<rpid:noisy/>\n</rpid:audio>", AudioIsType_Noisy);
   tests.add("<rpid:audio>\n<rpid:ok/>\n</rpid:audio>", AudioIsType_Ok);
   tests.add("<rpid:audio>\n<rpid:quiet/>\n</rpid:audio>", AudioIsType_Quiet);
   tests.add("<rpid:audio>\n<rpid:unknown/>\n</rpid:audio>", AudioIsType_Unknown);
}

template<>
void generateTuples<VideoIsType>(XmlTuples<VideoIsType>& tests)
{
   tests.add("<rpid:video>\n<rpid:toobright/>\n</rpid:video>", VideoIsType_TooBright);
   tests.add("<rpid:video>\n<rpid:ok/>\n</rpid:video>", VideoIsType_Ok);
   tests.add("<rpid:video>\n<rpid:dark/>\n</rpid:video>", VideoIsType_Dark);
   tests.add("<rpid:video>\n<rpid:unknown/>\n</rpid:video>", VideoIsType_Unknown);
}

template<>
void generateTuples<TextIsType>(XmlTuples<TextIsType>& tests)
{
   tests.add("<rpid:text>\n<rpid:uncomfortable/>\n</rpid:text>", TextIsType_Uncomfortable);
   tests.add("<rpid:text>\n<rpid:inappropriate/>\n</rpid:text>", TextIsType_Inappropriate);
   tests.add("<rpid:text>\n<rpid:ok/>\n</rpid:text>", TextIsType_Ok);
   tests.add("<rpid:text>\n<rpid:unknown/>\n</rpid:text>", TextIsType_Unknown);
}

template<>
void generateTuples<PlaceIs>(XmlTuples<PlaceIs>& tests)
{
   PlaceIs obj;
   obj.audio.present = true;
   obj.audio.value = AudioIsType_Noisy;
   const char* xml = "<rpid:place-is>\n\
<rpid:audio>\n<rpid:noisy/>\n</rpid:audio>\n\
</rpid:place-is>";
   tests.add(xml, obj);
}

template<>
void generateTuples<PlaceType>(XmlTuples<PlaceType>& tests)
{
   PlaceType obj;
   obj.placeType.otherValue = newNote("abc", NULL);
   const char* xml = "<rpid:place-type>\n\
<rpid:other>abc</rpid:other>\n\
</rpid:place-type>";
   tests.add(xml, obj);
}

template<>
void generateTuples<PrivacyType>(XmlTuples<PrivacyType>& tests)
{
   tests.add("<rpid:audio/>", PrivacyType_Audio);
   tests.add("<rpid:text/>", PrivacyType_Text);
   tests.add("<rpid:video/>", PrivacyType_Video);
   tests.add("<rpid:unknown/>", PrivacyType_Unknown);
}

template<>
void generateTuples<Privacy>(XmlTuples<Privacy>& tests)
{
   Privacy obj;
   obj.id = newOptionalString("1");
   obj.privacy.push_back(PrivacyType_Text);
   obj.privacy.push_back(PrivacyType_Audio);
   obj.notes.push_back(newNote("yes","en"));
   obj.notes.push_back(newNote("oui","fr"));
   const char* xml = "<rpid:privacy id=\"1\">\n\
<rpid:note xml:lang=\"en\">yes</rpid:note>\n\
<rpid:note xml:lang=\"fr\">oui</rpid:note>\n\
<rpid:text/>\n\
<rpid:audio/>\n\
</rpid:privacy>";
   tests.add(xml, obj);
}

RelationshipValue newRelationship(RelationshipType type)
{
   RelationshipValue value;
   value.relationship = type;
   return value;
}
template<>
void generateTuples<RelationshipValue>(XmlTuples<RelationshipValue>& tests)
{
   tests.add("<rpid:assistant/>", newRelationship(RelationshipType_Assistant));
   tests.add("<rpid:associate/>", newRelationship(RelationshipType_Associate));
   tests.add("<rpid:family/>", newRelationship(RelationshipType_Family));
   tests.add("<rpid:friend/>", newRelationship(RelationshipType_Friend));
   tests.add("<rpid:self/>", newRelationship(RelationshipType_Self));
   tests.add("<rpid:supervisor/>", newRelationship(RelationshipType_Supervisor));
   tests.add("<rpid:unknown/>", newRelationship(RelationshipType_Unknown));
   RelationshipValue other = newRelationship(RelationshipType_Other);
   other.otherValue = newNote("abc","en");
   tests.add("<rpid:other xml:lang=\"en\">abc</rpid:other>", other);
}

template<>
void generateTuples<Relationship>(XmlTuples<Relationship>& tests)
{
   Relationship obj;
   obj.relationship = newRelationship(RelationshipType_Self);
   obj.notes.push_back(newNote("abc","en"));
   const char* xml = "<rpid:relationship>\n\
<note xml:lang=\"en\">abc</note>\n\
<rpid:self/>\n\
</rpid:relationship>";
   tests.add(xml, obj);
}

template<>
void generateTuples<ServiceClassType>(XmlTuples<ServiceClassType>& tests)
{
   tests.add("<rpid:courier/>", ServiceClassType_Courier);
   tests.add("<rpid:electronic/>", ServiceClassType_Electronic);
   tests.add("<rpid:freight/>", ServiceClassType_Freight);
   tests.add("<rpid:in-person/>", ServiceClassType_InPerson);
   tests.add("<rpid:postal/>", ServiceClassType_Postal);
   tests.add("<rpid:unknown/>", ServiceClassType_Unknown);
}

template<>
void generateTuples<ServiceClass>(XmlTuples<ServiceClass>& tests)
{
   ServiceClass obj;
   obj.serviceClass = ServiceClassType_Electronic;
   obj.notes.push_back(newNote("abc","en"));
   const char* xml = "<rpid:service-class>\n\
<note xml:lang=\"en\">abc</note>\n\
<rpid:electronic/>\n\
</rpid:service-class>";
   tests.add(xml, obj);
}

template<>
void generateTuples<SphereType>(XmlTuples<SphereType>& tests)
{
   tests.add("<rpid:home/>", SphereType_Home);
   tests.add("<rpid:work/>", SphereType_Work);
   tests.add("<rpid:unknown/>", SphereType_Unknown);
}

template<>
void generateTuples<Sphere>(XmlTuples<Sphere>& tests)
{
   Sphere obj;
   obj.id = newOptionalString("x");
   obj.sphere.present = true;
   obj.sphere.value = SphereType_Work;
   const char* xml = "<rpid:sphere id=\"x\">\n\
<rpid:work/>\n\
</rpid:sphere>";
   tests.add(xml, obj);
}

template<>
void generateTuples<StatusIcon>(XmlTuples<StatusIcon>& tests)
{
   StatusIcon obj;
   obj.uri = toStr("http://example.com/mail.png");
   const char* xml = "<rpid:status-icon>http://example.com/mail.png</rpid:status-icon>";
   tests.add(xml, obj);
}

template<>
void generateTuples<TimeOffset>(XmlTuples<TimeOffset>& tests)
{
   TimeOffset obj;
   obj.id = newOptionalString("1");
   obj.description = newOptionalString("test offset");
   obj.offset = -240;
   const char* xml = "<rpid:time-offset id=\"1\" description=\"test offset\">-240</rpid:time-offset>";
   tests.add(xml, obj);
}

UserInput newUserInput(const char* id, ActiveIdle activeIdle, int idleThreshold, const char* lastInput)
{
   UserInput obj;
   obj.activeIdle = activeIdle;
   obj.idleThreshold.present = idleThreshold >= 0;
   obj.idleThreshold.value = idleThreshold;
   obj.lastInput = newOptionalString(lastInput);
   obj.id = newOptionalString(id);
   return obj;
} 

template<>
void generateTuples<UserInput>(XmlTuples<UserInput>& tests)
{
   tests.add("<rpid:user-input idle-threshold=\"600\" last-input=\"2004-10-21T13:20:00-05:00\">idle</rpid:user-input>", 
         newUserInput(NULL, ActiveIdle_Idle, 600, "2004-10-21T13:20:00-05:00"));
   tests.add("<rpid:user-input id=\"xyz\">active</rpid:user-input>", 
         newUserInput("xyz", ActiveIdle_Active, -1, NULL));
}

template<>
void generateTuples<Device>(XmlTuples<Device>& tests)
{
   Device obj;
   obj.id = toStr("pc147");
   obj.deviceID = toStr("urn:device:0003ba4811e3");
   obj.userInput.present = true;
   obj.userInput.value = newUserInput(NULL, ActiveIdle_Active, -1, NULL);
   obj.timestamp = newOptionalString("2004-10-21T13:20:00-05:00");
   obj.notes.push_back(newNote("PC", NULL));
   const char* xml = "<dm:device id=\"pc147\">\n\
<note>PC</note>\n\
<rpid:user-input>active</rpid:user-input>\n\
<dm:deviceID>urn:device:0003ba4811e3</dm:deviceID>\n\
<dm:timestamp>2004-10-21T13:20:00-05:00</dm:timestamp>\n\
</dm:device>";
   tests.add(xml, obj);
}

template<>
void generateTuples<Person>(XmlTuples<Person>& tests)
{
   Person obj;
   obj.id = toStr("p1");
   obj.activities.present = true;
   obj.activities.value.fromUntil = newFromUntil("2005-05-30T12:00:00+05:00", "2005-05-30T17:00:00+05:00");
   obj.activities.value.notes.push_back(newNote("Far away", NULL));
   obj.activities.value.activities.push_back(newActivity(ActivityType_Away));
   obj.classEnt = newOptionalString("calendar");
   obj.mood.present = true;
   obj.mood.value.moods.push_back(newMood(MoodValue_Angry));
   Mood otherMood = newMood(MoodValue_Other);
   otherMood.otherValue = newNote("brooding", NULL);
   obj.mood.value.moods.push_back(otherMood);
   obj.placeIs.present = true;
   obj.placeIs.value.audio.present = true;
   obj.placeIs.value.audio.value = AudioIsType_Noisy;
   obj.placeType.present = true;
   obj.placeType.value.placeType.otherValue = newNote("residence", NULL);
   obj.privacy.present = true;
   obj.privacy.value.privacy.push_back(PrivacyType_Unknown);
   obj.sphere.present = true;
   obj.sphere.value.sphere.present = true;
   obj.sphere.value.sphere.value = SphereType_Work;
   obj.statusIcon.present = true;
   obj.statusIcon.value.uri = toStr("http://example.com/play.gif");
   obj.timeOffset.present = true;
   obj.timeOffset.value.offset = -240;
   obj.notes.push_back(newNote("Scoring 120", NULL));
   obj.timestamp = newOptionalString("2005-05-30T16:09:44+05:00");
   const char* xml = "<dm:person id=\"p1\">\n\
<dm:note>Scoring 120</dm:note>\n\
<rpid:activities from=\"2005-05-30T12:00:00+05:00\" until=\"2005-05-30T17:00:00+05:00\">\n\
<note>Far away</note>\n\
<rpid:away/>\n\
</rpid:activities>\n\
<rpid:class>calendar</rpid:class>\n\
<rpid:mood>\n\
<rpid:angry/>\n\
<rpid:other>brooding</rpid:other>\n\
</rpid:mood>\n\
<rpid:place-is>\n\
<rpid:audio>\n<rpid:noisy/>\n</rpid:audio>\n\
</rpid:place-is>\n\
<rpid:place-type>\n\
<rpid:other>residence</rpid:other>\n\
</rpid:place-type>\n\
<rpid:privacy>\n<rpid:unknown/>\n</rpid:privacy>\n\
<rpid:sphere>\n<rpid:work/>\n</rpid:sphere>\n\
<rpid:status-icon>http://example.com/play.gif</rpid:status-icon>\n\
<rpid:time-offset>-240</rpid:time-offset>\n\
<dm:timestamp>2005-05-30T16:09:44+05:00</dm:timestamp>\n\
</dm:person>";
   tests.add(xml, obj);
}

template<>
void generateTuples<Contact>(XmlTuples<Contact>& tests)
{
   Contact obj;
   obj.contact = toStr("im:<EMAIL>");
   obj.priority.present = true;
   obj.priority.value = 0.8f;
   const char* xml = "<contact priority=\"0.8\">im:<EMAIL></contact>";
   tests.add(xml, obj);
}

template<>
void generateTuples<Status>(XmlTuples<Status>& tests)
{
   Status obj;
   tests.add("<status>\n</status>", obj);
   obj.basic.present = true;
   obj.basic.value = BasicStatusType_Open;
   tests.add("<status>\n<basic>open</basic>\n</status>", obj);
   obj.basic.value = BasicStatusType_Closed;
   tests.add("<status>\n<basic>closed</basic>\n</status>", obj);
}

template<>
void generateTuples<Tuple>(XmlTuples<Tuple>& tests)
{
   Tuple obj;
   obj.id = toStr("bs35r9");
   obj.status.basic.present = true;
   obj.status.basic.value = BasicStatusType_Open;
   obj.deviceID = newOptionalString("urn:device:0003ba4811e3");
   obj.relationship.present = true;
   obj.relationship.value.relationship.relationship = RelationshipType_Self;
   obj.serviceClass.present = true;
   obj.serviceClass.value.serviceClass = ServiceClassType_Electronic;
   obj.contact.present = true;
   obj.contact.value.priority.present = true;
   obj.contact.value.priority.value = 0.8f;
   obj.contact.value.contact = toStr("im:<EMAIL>");
   obj.notes.push_back(newNote("Don't Disturb Please!","en"));
   obj.notes.push_back(newNote("Ne derangez pas, s'il vous plait","fr"));
   obj.timestamp = newOptionalString("2005-10-27T16:49:29Z");
   const char* xml = "<tuple id=\"bs35r9\">\n\
<note xml:lang=\"en\">Don&apos;t Disturb Please!</note>\n\
<note xml:lang=\"fr\">Ne derangez pas, s&apos;il vous plait</note>\n\
<status>\n\
<basic>open</basic>\n\
</status>\n\
<dm:deviceID>urn:device:0003ba4811e3</dm:deviceID>\n\
<rpid:relationship>\n<rpid:self/>\n</rpid:relationship>\n\
<rpid:service-class>\n<rpid:electronic/>\n</rpid:service-class>\n\
<contact priority=\"0.8\">im:<EMAIL></contact>\n\
<dm:timestamp>2005-10-27T16:49:29Z</dm:timestamp>\n\
</tuple>";
   tests.add(xml, obj);
}


template<>
void generateTuples<Presence>(XmlTuples<Presence>& tests)
{
   {
      Presence obj;
      obj.entity = toStr("pres:<EMAIL>");
      obj.notes.push_back(newNote("I'll be in Tokyo next week", NULL));
      const char* xml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<presence xmlns=\"urn:ietf:params:xml:ns:pidf\"\n\
xmlns:dm=\"urn:ietf:params:xml:ns:pidf:data-model\"\n\
xmlns:rpid=\"urn:ietf:params:xml:ns:pidf:rpid\"\n\
 entity=\"pres:<EMAIL>\">\n\
<note>I&apos;ll be in Tokyo next week</note>\n\
</presence>";
      tests.add(xml, obj);
   }

   { // example from: http://kamailio.org/docs/modules/3.0.x/modules_k/pua_mi.html
      Presence obj;
      obj.entity = toStr("<EMAIL>");
      obj.notes.push_back(newNote("CPU:16 MEM:476", NULL));
      Tuple tuple;
      tuple.id = toStr("0x81475a0");
      tuple.status.basic.present = true;
      tuple.status.basic.value = BasicStatusType_Open;
      obj.tuples.push_back(tuple);
      Person person;
      person.id = toStr("pdd748945");
      person.activities.present = true;
      person.activities.value.activities.push_back(newActivity(ActivityType_Away));
      obj.persons.push_back(person);
      // note, the actual example includes a string 'away' next to <away/> the reason is unknown
      const char* xml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<presence xmlns=\"urn:ietf:params:xml:ns:pidf\"\n\
xmlns:dm=\"urn:ietf:params:xml:ns:pidf:data-model\"\n\
xmlns:rpid=\"urn:ietf:params:xml:ns:pidf:rpid\"\n\
 entity=\"<EMAIL>\">\n\
<note>CPU:16 MEM:476</note>\n\
<tuple id=\"0x81475a0\">\n\
<status>\n\
<basic>open</basic>\n\
</status>\n\
</tuple>\n\
<dm:person id=\"pdd748945\">\n\
<rpid:activities>\n\
<rpid:away/>\n\
</rpid:activities>\n\
</dm:person>\n\
</presence>";
      tests.add(xml, obj);
   }
}

// XmlConversionTests /////////////////////////////////////////////////////////////////////
class XmlConversionTests : public CpcapiAutoTest
{
public:
   XmlConversionTests() {}
   virtual ~XmlConversionTests() {}
};

TEST_F(XmlConversionTests, DISABLED_ToXml)
{
   {
      Presence obj;
      obj.entity = toStr("pres:<EMAIL>");
      obj.notes.push_back(newNote("I'll be in Tokyo next week", NULL));
      const char* xml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<presence xmlns=\"urn:ietf:params:xml:ns:pidf\"\n\
                 xmlns:dm=\"urn:ietf:params:xml:ns:pidf:data-model\"\n\
                 xmlns:rpid=\"urn:ietf:params:xml:ns:pidf:rpid\"\n\
 entity=\"pres:<EMAIL>\">\n\
<note>I'll be in Tokyo next week</note>\n\
</presence>";   

      std::ostringstream out;
      XmlEncoder encoder(out);

      out.clear();
      out.str(string());
      encoder.encode(obj);
      std::string result = (out.str());
      ASSERT_EQ(xml, result);

   }

   

   { // example from: http://kamailio.org/docs/modules/3.0.x/modules_k/pua_mi.html
      Presence obj;
      obj.entity = toStr("<EMAIL>");
      obj.notes.push_back(newNote("CPU:16 MEM:476", NULL));
      Tuple tuple;
      tuple.id = toStr("0x81475a0");
      tuple.status.basic.present = true;
      tuple.status.basic.value = BasicStatusType_Open;
      obj.tuples.push_back(tuple);
      Person person;
      person.id = toStr("pdd748945");
      person.activities.present = true;
      person.activities.value.activities.push_back(newActivity(ActivityType_Away));
      obj.persons.push_back(person);
      // note, the actual example includes a string 'away' next to <away/> the reason is unknown
      const char* xml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<presence xmlns=\"urn:ietf:params:xml:ns:pidf\"\n\
                 xmlns:dm=\"urn:ietf:params:xml:ns:pidf:data-model\"\n\
                 xmlns:rpid=\"urn:ietf:params:xml:ns:pidf:rpid\"\n\
 entity=\"<EMAIL>\">\n\
<note>CPU:16 MEM:476</note>\n\
<tuple id=\"0x81475a0\">\n\
<status>\n\
<basic>open</basic>\n\
</status>\n\
</tuple>\n\
<dm:person id=\"pdd748945\">\n\
<rpid:activities>\n\
<rpid:away/>\n\
</rpid:activities>\n\
</dm:person>\n\
</presence>";

      std::ostringstream out;
      XmlEncoder encoder(out);

      out.clear();
      out.str(string());
      encoder.encode(obj);
      std::string result = (out.str());
      ASSERT_EQ(xml, result);
   }
}

TEST_F(XmlConversionTests, DISABLED_ToXmlSpecialChar)
{
      {
      Presence obj;
      Presence parsedObj;
      obj.entity = toStr("pres:<EMAIL>");
      Note note = newNote("I'll be in <Tokyo> next week", NULL);
      obj.notes.push_back(note);
      const char* xml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<presence xmlns=\"urn:ietf:params:xml:ns:pidf\"\n\
                 xmlns:dm=\"urn:ietf:params:xml:ns:pidf:data-model\"\n\
                 xmlns:rpid=\"urn:ietf:params:xml:ns:pidf:rpid\"\n\
 entity=\"pres:<EMAIL>\">\n\
<note>I&apos;ll be in &lt;Tokyo&gt; next week</note>\n\
</presence>";   

      XmlParser parser;

      XmlRoot xmlRoot(xml);
      if(!xmlRoot.root())
         FAIL(); // XML document invalid
      if(!parser.parse(xmlRoot.root(), parsedObj))
         FAIL(); // Parse failed (returned false)
      if(!(obj == parsedObj))
         FAIL(); // xmlToObj objects differ

      std::ostringstream out;
      XmlEncoder encoder(out);

      out.clear();
      out.str(string());
      encoder.encode(parsedObj);
      ASSERT_EQ(note.text, parsedObj.notes[0].text);
      std::string result = (out.str());
      ASSERT_EQ(xml, result);

   }

   

   { // example from: http://kamailio.org/docs/modules/3.0.x/modules_k/pua_mi.html
      Presence obj;
      Presence parsedObj;
      obj.entity = toStr("<EMAIL>");
      obj.notes.push_back(newNote("CPU:16 <Tokyo> MEM:476", NULL));
      Tuple tuple;
      tuple.id = toStr("0x81<Tokyo>475a0");
      tuple.status.basic.present = true;
      tuple.status.basic.value = BasicStatusType_Open;
      obj.tuples.push_back(tuple);
      Person person;
      person.id = toStr("pdd7<Tokyo>48945");
      person.activities.present = true;
      person.activities.value.activities.push_back(newActivity(ActivityType_Away));
      obj.persons.push_back(person);
      // note, the actual example includes a string 'away' next to <away/> the reason is unknown
      const char* xml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<presence xmlns=\"urn:ietf:params:xml:ns:pidf\"\n\
                 xmlns:dm=\"urn:ietf:params:xml:ns:pidf:data-model\"\n\
                 xmlns:rpid=\"urn:ietf:params:xml:ns:pidf:rpid\"\n\
 entity=\"<EMAIL>\">\n\
<note>CPU:16 &lt;Tokyo&gt; MEM:476</note>\n\
<tuple id=\"0x81&lt;Tokyo&gt;475a0\">\n\
<status>\n\
<basic>open</basic>\n\
</status>\n\
</tuple>\n\
<dm:person id=\"pdd7&lt;Tokyo&gt;48945\">\n\
<rpid:activities>\n\
<rpid:away/>\n\
</rpid:activities>\n\
</dm:person>\n\
</presence>";

      XmlParser parser;

      XmlRoot xmlRoot(xml);
      if(!xmlRoot.root())
         FAIL(); // XML document invalid
      if(!parser.parse(xmlRoot.root(), parsedObj))
         FAIL(); // Parse failed (returned false)
      if(!(obj == parsedObj))
         FAIL(); // xmlToObj objects differ

      std::ostringstream out;
      XmlEncoder encoder(out);

      out.clear();
      out.str(string());
      encoder.encode(parsedObj);
      std::string result = (out.str());
      ASSERT_EQ(xml, result);
   }
}

TEST_F(XmlConversionTests, DISABLED_ToXmlDoNotDisturb)
{
   { 
      Presence obj;
      Presence parsedObj;
      obj.entity = toStr("<EMAIL>");
      obj.notes.push_back(newNote("CPU:16 MEM:476", NULL));
      Tuple tuple;
      tuple.id = toStr("0x81475a0");
      tuple.status.basic.present = true;
      tuple.status.basic.value = BasicStatusType_Open;
      obj.tuples.push_back(tuple);
      Person person;
      person.id = toStr("pdd748945");
      person.activities.present = true;
      person.activities.value.activities.push_back(newActivity(ActivityType_Busy));
      Activity otherActivity = newActivity(ActivityType_Other);
      otherActivity.otherValue.text = "DND";
      person.activities.value.activities.push_back(otherActivity);
      obj.persons.push_back(person);
      // note, the actual example includes a string 'away' next to <away/> the reason is unknown
      const char* xml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<presence xmlns=\"urn:ietf:params:xml:ns:pidf\"\n\
                 xmlns:dm=\"urn:ietf:params:xml:ns:pidf:data-model\"\n\
                 xmlns:rpid=\"urn:ietf:params:xml:ns:pidf:rpid\"\n\
 entity=\"<EMAIL>\">\n\
<note>CPU:16 MEM:476</note>\n\
<tuple id=\"0x81475a0\">\n\
<status>\n\
<basic>open</basic>\n\
</status>\n\
</tuple>\n\
<dm:person id=\"pdd748945\">\n\
<rpid:activities>\n\
<rpid:busy/>\n\
<rpid:other>DND</rpid:other>\n\
</rpid:activities>\n\
</dm:person>\n\
</presence>";

      XmlParser parser;

      XmlRoot xmlRoot(xml);
      if(!xmlRoot.root())
         FAIL(); // XML document invalid
      if(!parser.parse(xmlRoot.root(), parsedObj))
         FAIL(); // Parse failed (returned false)
      if(!(obj == parsedObj))
         FAIL(); // xmlToObj objects differ

      std::ostringstream out;
      XmlEncoder encoder(out);

      out.clear();
      out.str(string());
      encoder.encode(obj);
      std::string result = (out.str());
      ASSERT_EQ(xml, result);
   }
}


TEST_F(XmlConversionTests, FromXmlToObj)
{
   XmlParser parser;
   Presence obj;
   Presence testObj;
   Tuple tuple;
   Person person;
   Activity activity;
   Note personNote;

   // Fill in presence object
   tuple.id = "t6a5ed77e";
   tuple.status.basic = BasicStatus::BasicStatusType_Open;
   activity.activity = ActivityType::ActivityType_Holiday;
   personNote.text = "On Holiday";
   person.id = "p06360c4a";
   person.activities.present = true;
   person.activities.value.activities.push_back(activity);
   person.notes.push_back(personNote);
   obj.entity = toStr("sip:1001@teo");
   obj.tuples.push_back(tuple);
   obj.persons.push_back(person);

   std::string xmlToParse = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<presence xmlns=\"urn:ietf:params:xml:ns:pidf\"\n\
                 xmlns:dm=\"urn:ietf:params:xml:ns:pidf:data-model\"\n\
                 xmlns:rpid=\"urn:ietf:params:xml:ns:pidf:rpid\"\n\
entity = 'sip:1001@teo'>\n\
<tuple id = 't6a5ed77e'>\n\
<status>\n\
<basic>open</basic>\n\
</status>\n\
</tuple>\n\
<dm:person id = 'p06360c4a'>\n\
<rpid:activities>\n\
<rpid:holiday/>\n\
</rpid:activities>\n\
<dm:note>On Holiday</dm:note>\n\
</dm:person>\n\
</presence>";
   
   XmlRoot xmlRoot(xmlToParse);
   if (!xmlRoot.root())
      FAIL(); // XML document invalid
   if (!parser.parse(xmlRoot.root(), testObj))
      FAIL(); // Parse failed (returned false)
   if (!(obj == testObj))
      FAIL(); // xmlToObj objects differ
}

TEST_F(XmlConversionTests, FromXmlToObjMissingActivity)
{
   XmlParser parser;
   Presence obj;
   Presence testObj;
   Tuple tuple;
   Person person;
   //Activity activity;
   Note personNote;

   // Fill in presence object
   tuple.id = "t6a5ed77e";
   tuple.status.basic = BasicStatus::BasicStatusType_Open;
   //activity.activity = ActivityType::ActivityType_Holiday;
   personNote.text = "On Holiday";
   person.id = "p06360c4a";
   //person.activities.present = true;
   //person.activities.value.activities.push_back(activity);
   person.notes.push_back(personNote);
   obj.entity = toStr("sip:1001@teo");
   obj.tuples.push_back(tuple);
   obj.persons.push_back(person);

   std::string xmlToParse = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<presence xmlns=\"urn:ietf:params:xml:ns:pidf\"\n\
xmlns:dm=\"urn:ietf:params:xml:ns:pidf:data-model\"\n\
xmlns:rpid=\"urn:ietf:params:xml:ns:pidf:rpid\"\n\
entity = 'sip:1001@teo'>\n\
<tuple id = 't6a5ed77e'>\n\
<status>\n\
<basic>open</basic>\n\
</status>\n\
</tuple>\n\
<dm:person id = 'p06360c4a'>\n\
<rpid:activities>\n\
<rpid:missing_activity/>\n\
</rpid:activities>\n\
<dm:note>On Holiday</dm:note>\n\
</dm:person>\n\
</presence>";

   XmlRoot xmlRoot(xmlToParse);
   if (!xmlRoot.root())
      FAIL(); // XML document invalid
   if (!parser.parse(xmlRoot.root(), testObj))
      FAIL(); // Parse failed (returned false)
   if (!(obj == testObj))
      FAIL(); // xmlToObj objects differ
}

TEST_F(XmlConversionTests, FromXmlToObjCorruptPerson)
{
   XmlParser parser;
   Presence obj;
   Presence testObj;
   Tuple tuple;
   Person person;
   Activity activity;
   Note presenceNote;
   Contact contact;

   // Fill in presence object
   tuple.id = "101";
   tuple.status.basic = BasicStatus::BasicStatusType_Closed;
   contact.contact = "sip:<EMAIL>";
   contact.priority = 1;
   tuple.contact = contact;
   activity.activity = ActivityType::ActivityType_Away;
   person.activities.present = true;
   person.activities.value.activities.push_back(activity);
   obj.entity = toStr("sip:<EMAIL>");
   obj.tuples.push_back(tuple);
   obj.persons.push_back(person);
   presenceNote.text = "Unavailable";
   obj.notes.push_back(presenceNote);

   std::string xmlToParse = "<?xml version=\"1.0\" encoding=\"iso-8859-1\"?><presence xmlns=\"urn:ietf:params:xml:ns:pidf\"\n\
xmlns:pp=\"urn:ietf:params:xml:ns:pidf:person\"\n\
xmlns:es=\"urn:ietf:params:xml:ns:pidf:rpid:status:rpid-status\"\n\
xmlns:ep=\"urn:ietf:params:xml:ns:pidf:rpid:rpid-person\"\n\
entity='sip:<EMAIL>'>\n\
<pp:person>\n\
<status>\n\
<ep:activities>\n\
<ep:away/>\n\
</ep:activities>\n\
</status>\n\
</pp:person>\n\
<note>Unavailable</note>\n\
<tuple id='101'>\n\
<contact priority='1'>sip:<EMAIL></contact>\n\
<status>\n\
<basic>closed</basic>\n\
</status>\n\
</tuple>\n\
</presence>";

   XmlRoot xmlRoot(xmlToParse);
   if (!xmlRoot.root())
      FAIL(); // XML document invalid
   if (!parser.parse(xmlRoot.root(), testObj))
      FAIL(); // Parse failed (returned false)
   if (!(obj == testObj))
      FAIL(); // xmlToObj objects differ
}
///////////////////////////////////////////////////////////////////////////////////////////

}

#endif // 0
