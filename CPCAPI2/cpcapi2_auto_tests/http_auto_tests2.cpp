#include "cpcapi2_test_fixture.h"
#include "test_account_events.h"
#include "test_events.h"
#include "test_call_events.h"

#include "test_framework/http_test_framework.h"

#include <sstream>

#include "../../impl/util/CurlPPHelper.h"
#include "../../impl/util/CurlPPProgress.h"
#include "../../impl/util/CurlPPSSL.h"

using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace std::chrono;

// CPCAPI2 (as of this commit) uses a 10 second hardcoded HTTP timeout.
// this may or may not change in the future; the main purpose of this
// definition is to make it clear where we are currently making this
// assumption
static const int CPCAPI2_HARD_CODED_HTTP_TIMEOUT_SEC = 10;

namespace
{

   // uses SimpleWebServer from https://github.com/eidheim/Simple-Web-Server

   class HttpModuleTest2 : public CpcapiAutoTest
   {
   public:
      HttpModuleTest2() : mL<PERSON>enPort(9090), mServer() {
         mServer.config.port = mListenPort;
      }
      virtual ~HttpModuleTest2() {}
      
      cpc::string serverBaseUrl() const;
      
      const int mListenPort;
      SimpleWeb::Server<SimpleWeb::HTTP> mServer;
   };
   
   cpc::string HttpModuleTest2::serverBaseUrl() const
   {
      std::ostringstream ss;
      ss << "http://127.0.0.1:" << mListenPort;
      //ss << "https://www.google.com";
      
      return ss.str().c_str();
   }
   
   std::string resourcePathRegex(const std::string& resourcePath)
   {
      std::stringstream ss;
      ss << resourcePath << "$";
      return ss.str();
   }

   TEST_F(HttpModuleTest2, BasicPostRequest)
   {
      const std::string resourcePath("/test");
   
      // listens for requests at /test
      mServer.resource[resourcePathRegex(resourcePath)]["POST"]=[](std::shared_ptr<HttpServer::Response> response,
                                             std::shared_ptr<HttpServer::Request> request)
      {
         // get body contents, and echo it back in the response
         
         auto content=request->content.string();
         *response << "HTTP/1.1 200 OK\r\nContent-Length: " << content.length() << "\r\n\r\n" << content;
      };
    
      thread server_thread([this]()
      {
         mServer.start();
      });
    
      // the SimpleWeb example code demonstrates waiting for 1 second to let the HTTP server start.
      // unsure if there we could instead rely on an event..
      this_thread::sleep_for(seconds(1));
    
      try
      {
         CurlPPHelper helper;
         curlpp::Easy request;
         std::string messageBody = "test123"; // memory needs to hang around until after perform

         std::stringstream url;
         url << serverBaseUrl() << resourcePath;

         helper.setDefaultOptions( request, url.str(), "POST", messageBody.size() );
         request.setOpt( new curlpp::options::PostFields( messageBody ));

         CurlPPSSL cssl( SslCipherOptions(), CurlPPSSL::E_CERT_WHATEVER_ERROR );
         request.setOpt( new curlpp::options::SslCtxFunction( cssl ));

         std::stringstream responseBody;
         request.setOpt( new curlpp::options::WriteStream( &responseBody ));
         request.perform();

         ASSERT_EQ( curlpp::infos::ResponseCode::get( request ), 200 );
         ASSERT_EQ( messageBody, responseBody.str() );      
      }
      catch( curlpp::RuntimeError& e )
      {
         std::cerr << "Runtime Error: " << e.what();
      }

      mServer.stop();
      server_thread.join();
    
      return;
   }
   
   TEST_F(HttpModuleTest2, RepeatGetRequest)
   {
      const std::string resourcePath("/repeatget");
   
      const std::string kTestContent = "jasdlfasd;jf;asdlkj";
   
      mServer.resource[resourcePathRegex(resourcePath)]["GET"]=[kTestContent](std::shared_ptr<HttpServer::Response> response,
                                             std::shared_ptr<HttpServer::Request> request)
      {
         // get body contents, and echo it back in the response
         
         *response << "HTTP/1.1 200 OK\r\nContent-Length: " << kTestContent.length() << "\r\n\r\n" << kTestContent;
      };
    
      thread server_thread([this]()
      {
         mServer.start();
      });
    
      // the SimpleWeb example code demonstrates waiting for 1 second to let the HTTP server start.
      // unsure if there we could instead rely on an event..
      this_thread::sleep_for(seconds(1));
    
    
      for (int i = 0; i < 50; ++i)
      {
         CurlPPHelper helper;
         curlpp::Easy request;

         std::stringstream url;
         url << serverBaseUrl() << resourcePath;

         helper.setDefaultOptions( request, url.str() );

         std::stringstream responseBody;
         request.setOpt( new curlpp::options::WriteStream( &responseBody ));
         request.perform();
         
         ASSERT_EQ( curlpp::infos::ResponseCode::get( request ), 200 );
         ASSERT_EQ( kTestContent, responseBody.str() );
      }

      mServer.stop();
      server_thread.join();
    
      return;
   }
   
   TEST_F(HttpModuleTest2, PostLargeBody)
   {
      const int bodySizeBytes = 1000 * 50;
      char* body = new char[bodySizeBytes];
      
      int locs[32];
      const int locsLen = sizeof(locs) / sizeof(locs[0]);
      // write values at particular points to compare later
      for (int i = 0; i < locsLen; ++i)
      {
         int writeLoc = (bodySizeBytes / locsLen) * i;
         locs[i] = writeLoc;
         body[writeLoc] = i;
      }
      
      const std::string resourcePath("/postfile");

      mServer.resource[resourcePathRegex(resourcePath)]["POST"]=[&body, &locs, locsLen](std::shared_ptr<HttpServer::Response> response,
                                                             std::shared_ptr<HttpServer::Request> request)
      {
         // get body contents, and compare it to what we submitted
         
         auto content=request->content.string();
         
         bool matches = true;
         for (int i = 0; i < locsLen; ++i)
         {
            if (locs[i] > content.length())
            {
               matches = false;
            }
         
            if (body[locs[i]] != content[locs[i]])
            {
               matches = false;
               break;
            }
         }
         
         if (matches)
         {
            *response << "HTTP/1.1 200 OK\r\nContent-Length: 0";
         }
         else
         {
            *response << "HTTP/1.1 400 Upload mismatch\r\nContent-Length: 0";
         }
      };
    
      thread server_thread([this]()
      {
         mServer.start();
      });
    
      // the SimpleWeb example code demonstrates waiting for 1 second to let the HTTP server start.
      // unsure if there we could instead rely on an event..
      this_thread::sleep_for(seconds(1));
    
      CurlPPHelper helper;
      curlpp::Easy request;
      
      std::stringstream url;
      url << serverBaseUrl() << resourcePath;

      helper.setDefaultOptions( request, url.str(), "POST", bodySizeBytes );

      // body contains some embedded nulls. make sure to copy them
      std::string bodyText;
      bodyText.assign( body, bodySizeBytes );
      request.setOpt( new curlpp::options::PostFields( bodyText ));

      std::stringstream responseBody;
      request.setOpt( new curlpp::options::WriteStream( &responseBody ));
      request.perform();

      ASSERT_EQ( curlpp::infos::ResponseCode::get( request ), 200 );
      
      delete [] body;
      
      mServer.stop();
      server_thread.join();
    
      return;
   }
   
   std::time_t getTime()
   {
      time_point<system_clock> startTime = system_clock::now();
      std::time_t ttp = system_clock::to_time_t(startTime);
      return ttp;
   }
   
   // Make a POST request which receives no response at all for 10 seconds
   TEST_F(HttpModuleTest2, PostNoResponseTimeout)
   {
      const int bodySizeBytes = 1000 * 50;
      char* body = new char[bodySizeBytes];
      bool requestSeen = false;

      const std::string resourcePath("/postnoresponsetimeout");
      
      mServer.resource[resourcePathRegex(resourcePath)]["POST"]=[&requestSeen](std::shared_ptr<HttpServer::Response> response,
                                                                          std::shared_ptr<HttpServer::Request> request)
      {
         requestSeen = true;
      
         // don't respond for 15 seconds
         std::this_thread::sleep_for(seconds(CPCAPI2_HARD_CODED_HTTP_TIMEOUT_SEC));

         *response << "HTTP/1.1 200 OK\r\nContent-Length: 0";
      };
    
      thread server_thread([this]()
      {
         mServer.start();
      });
    
      // the SimpleWeb example code demonstrates waiting for 1 second to let the HTTP server start.
      // unsure if there we could instead rely on an event..
      this_thread::sleep_for(seconds(1));
   
      // Curlpp operations should be performed in a try .. catch block normally
      std::time_t startTime = getTime();
      try
      {
         CurlPPHelper helper;
         curlpp::Easy request;

         std::stringstream url;
         url << serverBaseUrl() << resourcePath;

         helper.setDefaultOptions( request, url.str(), "POST", bodySizeBytes );
         helper.setTimeoutOption( request, CPCAPI2_HARD_CODED_HTTP_TIMEOUT_SEC - 1 );

         std::string bodyText;
         bodyText.assign( body, bodySizeBytes ); // body may contain some embedded nulls
         request.setOpt( new curlpp::options::PostFields( bodyText ));

         // send the request
         std::stringstream responseBody;
         request.setOpt( new curlpp::options::WriteStream( &responseBody ));
         request.perform();
      }
      catch( curlpp::RuntimeError& e )
      {
         // should have timed out within CPCAPI2_HARD_CODED_HTTP_TIMEOUT_SEC seconds
         std::time_t diff = getTime() - startTime;
         ASSERT_TRUE( diff < CPCAPI2_HARD_CODED_HTTP_TIMEOUT_SEC + 4 );
         ASSERT_TRUE( requestSeen );
      }
      
      delete [] body;
      
      mServer.stop();
      server_thread.join();
    
      return;
   }
   
   TEST_F(HttpModuleTest2, PostNoResponseAbort)
   {
      const int bodySizeBytes = 1000 * 50;
      char* body = new char[bodySizeBytes];
      bool requestSeen = false;

      const std::string resourcePath("/postnoresponseabort");
      
      mServer.resource[resourcePathRegex(resourcePath)]["POST"]=[&requestSeen](std::shared_ptr<HttpServer::Response> response,
                                                                          std::shared_ptr<HttpServer::Request> request)
      {
         requestSeen = true;
      
         // don't respond for 15 seconds
         std::this_thread::sleep_for(seconds(15));
         
         *response << "HTTP/1.1 200 OK\r\nContent-Length: 0";
      };
    
      thread server_thread([this]()
      {
         mServer.start();
      });
    
      // the SimpleWeb example code demonstrates waiting for 1 second to let the HTTP server start.
      // unsure if there we could instead rely on an event..
      this_thread::sleep_for(seconds(1));
   
      CurlPPProgress progress; // external for multi-thread access

      auto httpRequestEvent = std::async(std::launch::async, [&]()
      {
         CurlPPHelper helper;
         curlpp::Easy request;
      
         std::stringstream url;
         url << serverBaseUrl() << resourcePath;
      
         std::time_t deltaTime;
         std::time_t startTime;
         try
         {
            helper.setDefaultOptions( request, url.str(), "POST", bodySizeBytes );

            std::string bodyText;
            bodyText.assign( body, bodySizeBytes ); // body may contain some embedded nulls
            request.setOpt( new curlpp::options::PostFields( bodyText ));

            // Prepare the ability to abort the request
            request.setOpt( new curlpp::options::ProgressFunction( progress ));

            // send the request
            std::stringstream responseBody;
            request.setOpt( new curlpp::options::WriteStream( &responseBody ));

            startTime = getTime();
            request.perform();
         }
         catch( curlpp::RuntimeError& e )
         {
            // This happens when the perform is aborted
            std::cerr << e.what() << std::endl;
         }
         deltaTime = getTime() - startTime;
      });
      
      std::this_thread::sleep_for(seconds(3));
      
      // make sure the HTTP request is still active
      ASSERT_NE(httpRequestEvent.wait_for(seconds(0)), std::future_status::ready);

      progress.abort();
      
      // give the SDK 3 seconds to abort the HTTP request and return
      ASSERT_EQ(httpRequestEvent.wait_for(seconds(3)), std::future_status::ready);
      
      delete [] body;
      
      mServer.stop();
      server_thread.join();
      return;
   }
   
   TEST_F(HttpModuleTest2, BasicGetLargeBody)
   {
      // 50 MB
      const int hostedBodySizeBytes = 1000 * 1000 * 50;
      std::unique_ptr<char> hostedBody(new char[hostedBodySizeBytes]);

      int locs[32];
      const int locsLen = sizeof(locs) / sizeof(locs[0]);
      // write values at particular points to compare later
      for (int i = 0; i < locsLen; ++i)
      {
         int writeLoc = (hostedBodySizeBytes / locsLen) * i;
         locs[i] = writeLoc;
         hostedBody.get()[writeLoc] = i;
      }
      
      // use streams so that we avoid making copies of the data where possible on the server side
      HttpTestFramework::FakeFileStream fakeFileBuf(hostedBody.get(), hostedBodySizeBytes);

      const std::string resourcePath("/basicgetlargebody");
      mServer.resource[resourcePathRegex(resourcePath)]["GET"]=[this, &hostedBodySizeBytes,
                                                        &hostedBody, &locs,
                                                        &locsLen, &fakeFileBuf](std::shared_ptr<HttpServer::Response> response,
                                                                                std::shared_ptr<HttpServer::Request> request)
      {
         std::shared_ptr<std::istream> ifs = std::make_shared<std::istream>(&fakeFileBuf);

         *response << "HTTP/1.1 200 OK\r\n" << "Content-Length: " << hostedBodySizeBytes << "\r\n\r\n";
         HttpTestFramework::FileSender::resourceSendStream(mServer, response, ifs, hostedBodySizeBytes);
      };

      thread server_thread([this]()
      {
         mServer.start();
      });
    
      // the SimpleWeb example code demonstrates waiting for 1 second to let the HTTP server start.
      // unsure if there we could instead rely on an event..
      this_thread::sleep_for(seconds(1));
    
      try
      {
         CurlPPHelper helper;
         curlpp::Easy request;

         std::stringstream url;
         url << serverBaseUrl() << resourcePath;
         helper.setDefaultOptions( request, url.str() );

         std::stringstream responseStream;
         request.setOpt( new curlpp::options::WriteStream( &responseStream ));
      
         std::time_t startTime = getTime();
         request.perform();
         std::time_t endTime = getTime();
      
         std::cout << "Took " << endTime - startTime << " seconds to download file in BasicGetLargeBody test" << std::endl;
      
         std::string responseBody( responseStream.str() );
         ASSERT_EQ(responseBody.size(), hostedBodySizeBytes);
      
         for (int i = 0; i < locsLen; ++i)
         {
            ASSERT_FALSE(locs[i] > responseBody.size());
            ASSERT_EQ(hostedBody.get()[locs[i]], responseBody[locs[i]]);
         }
      }
      catch( curlpp::RuntimeError& e )
      {
         std::cout << "Error while performing GET: " << e.what() << std::endl;
         ASSERT_TRUE( 0 && "runtime error" );
      }

      mServer.stop();
      server_thread.join();
    
      return;
   }

   TEST_F(HttpModuleTest2, BasicGetLargeBodySlowDownload)
   {
      // 50 MB
      const int hostedBodySizeBytes = 1000 * 1000 * 50;
      std::unique_ptr<char> hostedBody(new char[hostedBodySizeBytes]);

      int locs[32];
      const int locsLen = sizeof(locs) / sizeof(locs[0]);
      // write values at particular points to compare later
      for (int i = 0; i < locsLen; ++i)
      {
         int writeLoc = (hostedBodySizeBytes / locsLen) * i;
         locs[i] = writeLoc;
         hostedBody.get()[writeLoc] = i;
      }
      
      // use streams so that we avoid making copies of the data where possible on the server side
      HttpTestFramework::FakeFileStream fakeFileBuf(hostedBody.get(), hostedBodySizeBytes);

      const std::string resourcePath("/basicgetlargebodyslowdownload");
      mServer.resource[resourcePathRegex(resourcePath)]["GET"]=[this, &hostedBodySizeBytes,
                                                        &hostedBody, &locs,
                                                        &locsLen, &fakeFileBuf](std::shared_ptr<HttpServer::Response> response,
                                                                                std::shared_ptr<HttpServer::Request> request)
      {
         std::shared_ptr<std::istream> ifs = std::make_shared<std::istream>(&fakeFileBuf);

         *response << "HTTP/1.1 200 OK\r\n" << "Content-Length: " << hostedBodySizeBytes << "\r\n\r\n";

         // ask the sender to take at least 15 seconds to send the file; however, this should  NOT
         // triger our HTTP timeout, as the server is still sending data, albeit slowly
         HttpTestFramework::FileSender::resourceSendStream(mServer, response, ifs, hostedBodySizeBytes,
                                                           seconds(CPCAPI2_HARD_CODED_HTTP_TIMEOUT_SEC)
                                                           + seconds(5));
      };

      thread server_thread([this]()
      {
         mServer.start();
      });
    
      // the SimpleWeb example code demonstrates waiting for 1 second to let the HTTP server start.
      // unsure if there we could instead rely on an event..
      this_thread::sleep_for(seconds(1));
    
      try
      {
         CurlPPHelper helper;
         curlpp::Easy request;

         std::stringstream url;
         url << serverBaseUrl() << resourcePath;
      
         helper.setDefaultOptions( request, url.str() );

         std::stringstream responseStream;
         request.setOpt( new curlpp::options::WriteStream( &responseStream ));

         std::time_t startTime = getTime();
         request.perform();
         std::time_t endTime = getTime();
      
         std::cout << "Took " << endTime - startTime << " seconds to download file in BasicGetLargeBody test" << std::endl;
      
         std::string responseBody( responseStream.str() );
         ASSERT_EQ( responseBody.size(), hostedBodySizeBytes);
      
         for (int i = 0; i < locsLen; ++i)
         {
            ASSERT_FALSE(locs[i] > responseBody.size());
            ASSERT_EQ(hostedBody.get()[locs[i]], responseBody[locs[i]]);
         }
      }
      catch( curlpp::RuntimeError& e )
      {
         std::cout << "Error while performing GET: " << e.what() << std::endl;
         ASSERT_TRUE( 0 && "runtime error" );
      }

      mServer.stop();
      server_thread.join();
    
      return;
   }
   
   TEST_F(HttpModuleTest2, BasicGetMidDownloadTimeout)
   {
      HttpTestFramework::FileSender::CvWrapper cv;
      cv.cv = std::make_shared<std::condition_variable>();
      cv.mx = std::make_shared<std::mutex>();
   
      system_clock::time_point hangPoint;
      const std::string resourcePath("/BasicGetMidDownloadTimeout");
      mServer.resource[resourcePathRegex(resourcePath)]["GET"]=[this, &cv, &hangPoint]
                                                               (std::shared_ptr<HttpServer::Response> response,
                                                                std::shared_ptr<HttpServer::Request> request)
      {
         *response << "HTTP/1.1 200 OK\r\n" << "Content-Length: 50000000\r\n\r\n";

         // ask the sender to send stream data for the download, but hang doing so part way
         
         hangPoint = system_clock::now() + seconds(15);
         HttpTestFramework::FileSender::resourceSendStreamHault(mServer, response, hangPoint, cv);
      };

      thread server_thread([this]()
      {
         mServer.start();
      });
    
      // the SimpleWeb example code demonstrates waiting for 1 second to let the HTTP server start.
      // unsure if there we could instead rely on an event..
      this_thread::sleep_for(seconds(1));
    
      time_point<system_clock> startTime;
      time_point<system_clock> endTime;
      try
      {
         CurlPPHelper helper;
         CurlPPProgress progress( CPCAPI2_HARD_CODED_HTTP_TIMEOUT_SEC ); // dynamic timeout specified this way
         curlpp::Easy request;
      
         std::stringstream url;
         url << serverBaseUrl() << resourcePath;
      
         helper.setDefaultOptions( request, url.str() );

         std::stringstream responseStream;
         request.setOpt( new curlpp::options::WriteStream( &responseStream ));
      
         // Prepare the ability to abort the request and set the timer
         request.setOpt( new curlpp::options::ProgressFunction( progress ));

         startTime = system_clock::now();
         request.perform();
      }
      catch( curlpp::RuntimeError& /*e*/ )
      {
         // curlpp throws RuntimeError on timeout.
         std::cout << "timeout received" << std::endl;
      }

      endTime = system_clock::now();
      cv.cv->notify_all();
      
      // should have hung sometime after the request started
      ASSERT_GT(hangPoint, startTime);
      
      // should have hung sometime before the request stopped
      ASSERT_GT(endTime, hangPoint);
      
      // request should have timed out sometime around CPCAPI2_HARD_CODED_HTTP_TIMEOUT_SEC seconds after the hang
      ASSERT_GT(endTime, hangPoint + seconds(CPCAPI2_HARD_CODED_HTTP_TIMEOUT_SEC - 5));
      ASSERT_LT(endTime, hangPoint + seconds(CPCAPI2_HARD_CODED_HTTP_TIMEOUT_SEC + 5));

      mServer.stop();
      server_thread.join();
    
      return;
   }

   TEST_F(HttpModuleTest2, BasicGetMidDownloadAbort)
   {
      HttpTestFramework::FileSender::CvWrapper cv;
      cv.cv = std::make_shared<std::condition_variable>();
      cv.mx = std::make_shared<std::mutex>();
      
      // 50 MB
      const int hostedBodySizeBytes = 1000 * 1000 * 50;
      std::unique_ptr<char> hostedBody(new char[hostedBodySizeBytes]);
      
      HttpTestFramework::FakeFileStream fakeFileBuf(hostedBody.get(), hostedBodySizeBytes);
   
      const std::string resourcePath("/BasicGetMidDownloadAbort");
      mServer.resource[resourcePathRegex(resourcePath)]["GET"]=[this, &hostedBodySizeBytes,
                                                        &hostedBody,
                                                        &fakeFileBuf](std::shared_ptr<HttpServer::Response> response,
                                                                      std::shared_ptr<HttpServer::Request> request)
      {
         std::shared_ptr<std::istream> ifs = std::make_shared<std::istream>(&fakeFileBuf);

         *response << "HTTP/1.1 200 OK\r\n" << "Content-Length: " << hostedBodySizeBytes << "\r\n\r\n";

         // ask the sender to take at least 10 seconds to deliver the file
         HttpTestFramework::FileSender::resourceSendStream(mServer, response, ifs, hostedBodySizeBytes,
                                                           seconds(10));
      };

      thread server_thread([this]()
      {
         mServer.start();
      });
    
      // the SimpleWeb example code demonstrates waiting for 1 second to let the HTTP server start.
      // unsure if there we could instead rely on an event..
      this_thread::sleep_for(seconds(1));
    
      CurlPPHelper helper;
      CurlPPProgress progress;
      curlpp::Easy request;
      
      std::stringstream url;
      url << serverBaseUrl() << resourcePath;
      
      helper.setDefaultOptions( request, url.str() );

      std::stringstream responseStream;
      request.setOpt( new curlpp::options::WriteStream( &responseStream ));
      
      // Prepare the ability to abort the request and set the timer
      request.setOpt( new curlpp::options::ProgressFunction( progress ));

      auto httpRequestEvent = std::async(std::launch::async, [&]()
      {
         try
         {
            request.perform();
         }
         catch( curlpp::RuntimeError& e )
         {
            // happens on abort.
         }
      });
      
      std::this_thread::sleep_for(seconds(3));
      
      // make sure the HTTP request is still active
      ASSERT_NE(httpRequestEvent.wait_for(seconds(0)), std::future_status::ready);

      progress.abort();
      
      // give the SDK 3 seconds to abort the HTTP request and return
      ASSERT_EQ(httpRequestEvent.wait_for(seconds(3)), std::future_status::ready);

      mServer.stop();
      server_thread.join();
    
      return;
   }

#ifdef _WIN32
   // Attempt to download a very large file to memory, and check that it fails gracefully.
   // can't yet run this on macOS, because it always succeeds (the OS seems to not let the process exhaust free heap space)
   TEST_F(HttpModuleTest2, MassiveDownloadFailGracefully)
   {
      HttpTestFramework::FileSender::CvWrapper cv;
      cv.cv = std::make_shared<std::condition_variable>();
      cv.mx = std::make_shared<std::mutex>();
      
      // 5000 mB
      const unsigned long hostedBodySizeBytes = 1000l * 1000l * 5000l;
      
      const std::string resourcePath("/MassiveDownloadFailGracefully");
      mServer.resource[resourcePathRegex(resourcePath)]["GET"]=[this, &hostedBodySizeBytes](std::shared_ptr<HttpServer::Response> response,
                                                                                            std::shared_ptr<HttpServer::Request> request)
      {
         *response << "HTTP/1.1 200 OK\r\n" << "Content-Length: " << hostedBodySizeBytes << "\r\n\r\n";

         // place no limit on how fast the download comes in
         HttpTestFramework::FileSender::resourceSendStreamBogusBytes(mServer, response, hostedBodySizeBytes);
      };

      thread server_thread([this]()
      {
         mServer.start();
      });
    
      // the SimpleWeb example code demonstrates waiting for 1 second to let the HTTP server start.
      // unsure if there we could instead rely on an event..
      this_thread::sleep_for(seconds(1));
    
      try
      {
         CurlPPHelper helper;
         CurlPPProgress progress( 60 * 10 );
         curlpp::Easy request;
      
         std::stringstream url;
         url << serverBaseUrl() << resourcePath;
      
         helper.setDefaultOptions( request, url.str() );

         std::stringstream responseStream;
         request.setOpt( new curlpp::options::WriteStream( &responseStream ));

         request.setOpt( new curlpp::options::ProgressFunction( progress ));

         request.perform();
      }
      catch( curlpp::RuntimeError& e )
      {
         std::cout << "Error while performing HTTP GET: " << e.what() << std::endl;
      }
      
      // expect that somewhere along the way, internally the SDK would fail to allocate a large enough
      // contiguous memory region. ensure we don't crash but instead fail the request, and indicate as such
      //ASSERT_EQ(responseResult.errorCode, -1);
      //ASSERT_EQ(responseResult.status, HTTPClient::EHTTPUnknownError);

      mServer.stop();
      server_thread.join();
    
      return;
   }
#endif

   TEST_F(HttpModuleTest2, FileSizeLimitTest)
   {
      HttpTestFramework::FileSender::CvWrapper cv;
      cv.cv = std::make_shared<std::condition_variable>();
      cv.mx = std::make_shared<std::mutex>();

      // 20 mB
      const unsigned long hostedBodySizeBytes = 1000l * 1000l * 20l;

      const std::string resourcePath("/FileSizeLimitTest");
      mServer.resource[resourcePathRegex(resourcePath)]["GET"] = [this, &hostedBodySizeBytes](std::shared_ptr<HttpServer::Response> response,
         std::shared_ptr<HttpServer::Request> request)
      {
         *response << "HTTP/1.1 200 OK\r\n" << "Content-Length: " << hostedBodySizeBytes << "\r\n\r\n";

         // place no limit on how fast the download comes in
         HttpTestFramework::FileSender::resourceSendStreamBogusBytes(mServer, response, hostedBodySizeBytes);
      };

      thread server_thread([this]()
      {
         mServer.start();
      });

      // the SimpleWeb example code demonstrates waiting for 1 second to let the HTTP server start.
      // unsure if there we could instead rely on an event..
      this_thread::sleep_for(seconds(1));

      CurlPPHelper helper;
      std::stringstream url;
      url << serverBaseUrl() << resourcePath;

      // first, try a request with no limit and ensure it succeeds
      try
      {
         curlpp::Easy request;

         helper.setDefaultOptions( request, url.str() );
         helper.setTimeoutOption( request, 60 * 60 * 1 ); // 1 hour

         std::stringstream responseStream;
         request.setOpt( new curlpp::options::WriteStream( &responseStream ));

         request.perform();
      }
      catch( curlpp::RuntimeError& e )
      {
         std::cout << "Error while performing HTTP GET: " << e.what() << std::endl;
         ASSERT_TRUE( 0 && "Error while performing HTTP GET" );
      }

      // next, try the same request with a size limit, and ensure it fails
      try
      {
         curlpp::Easy request;

         helper.setDefaultOptions( request, url.str() );
         helper.setTimeoutOption( request, 60 * 60 * 1 ); // 1 hour
         request.setOpt( new curlpp::options::MaxFileSize( 5000 ));

         std::stringstream responseStream;
         request.setOpt( new curlpp::options::WriteStream( &responseStream ));
         request.perform(); // should throw exception
         ASSERT_TRUE( 0 && "No Error while performing HTTP GET" );
      }
      catch( curlpp::RuntimeError& e )
      {
         std::cout << "Error while performing HTTP GET: " << e.what() << std::endl;
      }

      mServer.stop();
      server_thread.join();

      return;
   }
}
