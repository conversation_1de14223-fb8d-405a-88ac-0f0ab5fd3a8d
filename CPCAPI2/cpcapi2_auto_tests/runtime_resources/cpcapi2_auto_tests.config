pushprovider_apns_p8file = APNsAuthKey_462WVPCXB7.p8
pushprovider_apns_authkeyid = 462WVPCXB7
pushprovider_apns_teamid = UQC9N9AMZM
apn_server_url = https://api.development.push.apple.com:443/3/device/
fcm_server_url = https://fcm.googleapis.com/fcm/send
pushprovider_fcm_keyid = AAAAl52NiXc:APA91bFmg4ymYoK7oA49AgpT5EOKJkM15RcE2LQ8ik5V1qgbM0FihBwHFciNZpo2eHXkUvXDMP-IrI3l_xHo7KXUd-T2G-ip2jzyfj6-ryIHLV-wgNEBD4l6xppCVdj2puj1xGe8CqvyiqQ-Jw2sWvixXV-_wI2PSA
authprovider_stretto_group = cloudSdkAuth
authprovider_stretto_baseurl = https://ccsdev.mobilevoiplive.com/login
cloud_server_username = server
cloud_server_password = 6f8adc5c68f6e9de2c658d9cdffae8eb9debb02251f3c6c2d0b02bb6a80222e5
auth_server_url = http://cloudsdk4.bria-x.net:18082/login_v1
orch_server_url = http://inproc.local:18080/jsonApi
jsonapi_server_region = LOCAL
jsonapi_server_ws_url = ws://127.0.0.1:9003
jsonapi_websocket_port = 9003
jsonapi_http_port = 18080
# leave blank to use HTTP; otherwise specify a cert for HTTPS
jsonapi_http_cert_file_path =
# leave blank to use HTTP; otherwise specify a private key for HTTPS
jsonapi_http_priv_key_file_path = 
certificate_file_path = p256-public-key.spki
pushdb_redis_ip = pushdbsqlite
pushdb_redis_port = 9
orchdb_redis_ip = mock
orchdb_redis_port = 6379
