# defines per device max CPU usage values for this test

# SDK mac jenkins II
# w/ http://tbswitcher.rugarciap.com/ (Turbo Boost Switcher) installed + turbo boost disabled
max_average_cpu_usage_for_device_11985117-a114-5bb0-86cb-c527402a6a1a = 132

# YVR-JenSL-SDK1
max_average_cpu_usage_for_device_ed6334e3-4d15-5485-ae86-33809bf7f356 = 117
max_average_cpu_usage_for_device_ed6334e3-4d15-5485-ae86-33809bf7f356_x64 = 142

# YVR-JENSL-SDK2 (M1 based Mac Mini - Jenkins slave)
max_average_cpu_usage_for_device_8123db26-d602-5583-9462-c86bf4f599f1 = 148

# BEG-JenSL-SDK1
max_average_cpu_usage_for_device_08bdc2b7-c529-541a-81b4-3623d1616146 = 300
max_average_cpu_usage_for_device_08bdc2b7-c529-541a-81b4-3623d1616146_x64 = 300