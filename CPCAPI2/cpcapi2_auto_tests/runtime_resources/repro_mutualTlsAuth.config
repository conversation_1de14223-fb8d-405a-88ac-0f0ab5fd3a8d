CertificatePath = ./runtime_resources/SelfSignedCerts/server
CADirectory = ./runtime_resources/SelfSignedCerts/root
HttpAdminUserFile = ./runtime_resources/users.txt
CertificatePath_Android = /data/user/0/com.counterpath.sdkdemo.advancedaudiocall/files/runtime_resources/SelfSignedCerts/server/
CADirectory_Android = /data/user/0/com.counterpath.sdkdemo.advancedaudiocall/files/runtime_resources/SelfSignedCerts/root/
DatabasePath_Android = /data/user/0/com.counterpath.sdkdemo.advancedaudiocall/files/runtime_resources
HttpAdminUserFile_Android = /data/user/0/com.counterpath.sdkdemo.advancedaudiocall/files/runtime_resources/users.txt
Domains = 127.0.0.1,autotest.cpcapi2
IPAddress = 0.0.0.0
UDPPort = 0
TCPPort = 0
TLSPort = 6061
DTLSPort = 0
TLSDomainName = autotest.cpcapi2
TLSClientVerification = Mandatory
TLSUseEmailAsSIP = true
Transport1Interface = 0.0.0.0:6061
Transport1Type = TLS
Transport1TlsDomain = autotest.cpcapi2
Transport1TlsClientVerification = Mandatory
Transport1RecordRouteUri = sip:autotest.cpcapi2;transport=TLS
RecordRouteUri = sip:autotest.cpcapi2;transport=tls
ForceRecordRouting = true
DisableOutbound = false
OutboundVersion = 5626
EnableFlowTokens = false
ClientNatDetectionMode = DISABLED
FlowTimer = 0
EnableCertificateAuthenticator = True
DisableAuth = true
HttpAdminRealm = repro
