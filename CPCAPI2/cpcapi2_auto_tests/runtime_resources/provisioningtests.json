{"sipAccount": [{"sipAccountSettings": {"username": "AutoTestUser", "domain": "autotest.net", "password": "AutoTestPwd", "displayName": "AutoTest", "auth_username": "AutoTestAuth", "auth_realm": "realm.autotest.net", "useRegistrar": false, "outboundProxy": "proxy.autotest.net", "alwaysRouteViaOutboundProxy": true, "registrationIntervalSeconds": 3601, "minimumRegistrationIntervalSeconds": 3599, "maximumRegistrationIntervalSeconds": 3602, "useRport": false, "sipTransportType": 5, "excludeEncryptedTransports": true, "userAgent": "AutoTest UA", "udpKeepAliveTime": 31, "tcpKeepAliveTime": 121, "useOutbound": true, "useGruu": true, "otherNonEscapedCharsInUri": "$%^", "nameServers": ["***************", "192.168.300.300"], "additionalNameServers": ["***************", "***************"], "sessionTimerMode": 2, "sessionTimeSeconds": 601, "stunServerSource": 2, "stunServer": "stun.autotest.net", "ignoreCertVerification": true, "additionalCertPeerNames": ["AutoTestAdditionalCertPeerName01", "AutoTestAdditionalCertPeerName02"], "acceptedCertPublicKeys": ["AutoTestAcceptedCertPublicKey01", "AutoTestAcceptedCertPublicKey02"], "requiredCertPublicKeys": ["AutoTestRequiredCertPublicKey03", "AutoTestRequiredCertPublicKey04"], "sipQosSettings": 41, "useImsAuthHeader": true, "minSipPort": 1, "maxSipPort": 10000, "useMethodParamInReferTo": true, "useInstanceId": true, "ipVersion": 1, "sslVersion": 1000, "reRegisterOnResponseTypes": [{"method": "AUTOTEST", "responseCode": 505}, {"method": "JUSTTEST", "responseCode": 506}], "enableRegeventDeregistration": true, "XCAPRoot": "/XCAP/autotest", "tunnelConfig": {"useTunnel": true, "tunnelType": 0, "server": "tunnel.autotest.net", "transportType": 2, "mediaTransportType": 4, "redundancyFactor": 1, "doLoadBalancing": true, "ignoreCertVerification": true, "disableNagleAlgorithm": true}, "capabilities": [{"name": "TEST1", "value": "AUTO"}, {"name": "TEST2", "value": "AUTO"}], "additionalFromParameters": [{"name": "ADITIONALPARAMETER", "value": "FROM"}, {"name": "FROMPARAMETER", "value": "ADDITIONAL"}], "sourceAddress": "***************", "preferPAssertedIdentity": true, "autoRetryOnTransportDisconnect": true, "keepAliveMode": 1, "useRinstance": false, "enableNat64Support": false, "userCertificatePEM": "-----<PERSON><PERSON><PERSON> CERTIFICATE-----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\n2qNlWjnQaqeDWk/giu+6PMrZuoAePB0bSYXOs2JD\n-----END CERTIFICATE-----", "userPrivateKeyPEM": "-----B<PERSON>IN RSA PRIVATE KEY-----\nMIIJJwIBAAKCAgEApnzqDFfBMOp2C5NvH50QS7WJ/woUsKcutIURPORQoK9LKa7N\nr4BHGUWJEWBjgQwFV5RX2JuSj7z719W/PaTFLQ70lJM+4YB+1Ka+SEVqsETUvpHj\nP0FLFVJIoykOBfjNnBaMIVfD7+wrae0SCfI6imtKU3bC0c2bx1lXDj+qKwRowLu2\nupD3JU3R9Eg9bAFG4IqwtUpDFOzo04Fw894g3C9XWPUvLdhbf9pp8bZLsRudMqLF\ni1PCIYJF9z39D/OO08MEviVzHGtoeqzBoHA8pZYQn4IP08uTGk1y3E2/zt9971KR\n-----END RSA PRIVATE KEY-----"}}], "conversation": [{"conversationSettings": {"sessionName": "foo", "natTraversalMode": 2, "natTraversalServerSource": 1, "natTraversalServer": "Bar", "holdMode": 2, "prackMode": 3, "minRtpPort": 100, "maxRtpPort": 10000, "minRtpPortAudio": 200, "maxRtpPortAudio": 20000, "minRtpPortVideo": 30, "maxRtpPortVideo": 300, "turnUsername": "pete", "turnPassword": "007", "includePPreferredIdentity": true, "includePAssertedIdentity": true, "includeAttribsForStaticPLs": true}}], "customSettings": {"appSettingA": true, "appSettingB": 56, "appSettingZ": "foobar"}, "xmppAccount": [{"xmppAccountSettings": {"username": "test_alice", "domain": "autotest.net", "password": "123", "port": 0, "priority": 0, "softwareName": "CPCAPI2-based Client", "softwareVersion": "1.0", "nameServers": ["0.0.0.0"], "identityType": "phone", "connectTimeOut": 10, "keepAliveTime": 30, "usePingKeepAlive": false, "enableLocalSocks5Proxy": true, "enableRemoteStreamHostDiscovery": true, "sslVersion": 3, "ignoreCertVerification": false, "logXmppStanzas": true, "ipVersion": 0, "enableStreamManagement": true, "enableStreamResumption": false, "streamManagementSequence": 0, "publishInitialPresenceAsAvailable": true, "fallbackOnResourceConflict": false, "enableCompression": true, "enableXmppPresence": true, "enableXmppStanza": true, "additionalCertPeerNames": ["AutoTestAdditionalCertPeerName01", "AutoTestAdditionalCertPeerName02"], "acceptedCertPublicKeys": ["AutoTestAcceptedCertPublicKey01", "AutoTestAcceptedCertPublicKey02"]}}]}