i: {"apiVersion":1006,"cmd":"LOGIN","requestID":1,"group":"imap.mobilevoiplive.com","username":"<EMAIL>","password":"vccs0007","displayName":"<PERSON><PERSON> the Bear"}
o: {"apiVersion":1006,"requestID":1,"errorCode":"OK","cmd":"STATUS_RESPONSE","command":"LOGIN"}
i: {"cmd":"LIST_CONFERENCES","requestID":2,"includeSubscriptions":true}
o: {"requestID":2,"cmd":"LIST_CONFERENCES_RESPONSE","conferences":[{"nomoderatorOverrun":0,"supportVideo":true,"participantPin":"","lobbySipAddress":"+<EMAIL>","created":*************,"description":"The description of vccs0007's conference","title":"Conference of vccs0007","bridgeNumber":"1173","supportAudio":true,"socketModerator":true,"moderatorPin":"10707","sipAddress":"+<EMAIL>","recordNames":false,"supportMessaging":true,"supportScreensharing":true,"id":8,"moderatorSipAddress":"+<EMAIL>"}]}
i: {"cmd":"GET_XMPP_CONNECTION","requestID":3,"conferenceID":8,"needGuestAccount":false}
o: {"xmppUsername":"<EMAIL>","conferenceID":8,"displayName":"Smokey the Bear","requestID":3,"cmd":"GET_XMPP_CONNECTION_RESPONSE","xmppWebsocketURL":"wss://***********:5291/","xmppDomain":"imap.mobilevoiplive.com","xmppChatRoomJid":"<EMAIL>"}
