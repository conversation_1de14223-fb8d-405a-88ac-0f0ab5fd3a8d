i: {"apiVersion":1006,"cmd":"LOGIN","requestID":1,"group":"imap.mobilevoiplive.com","username":"<EMAIL>","password":"vccs0007","displayName":"<PERSON><PERSON> the Bear"}
o: {"apiVersion":1006,"requestID":1,"errorCode":"OK","cmd":"STATUS_RESPONSE","command":"LOGIN"}
i: {"cmd":"LIST_CONFERENCES","requestID":2,"includeSubscriptions":true}
o: {"requestID":2,"cmd":"LIST_CONFERENCES_RESPONSE","conferences":[{"supportVideo":true,"participantPin":"0707","lobbySipAddress":"sip:+<EMAIL>","description":"The description of vccs0007's conference","title":"Conference of vccs0007","supportAudio":true,"hosted":true,"socketModerator":true,"moderatorPin":"10707","sipAddress":"+<EMAIL>","supportMessaging":true,"id":8,"participants":[{"capabilities":15,"isModerator":true,"participantNumber":0,"displayName":"+12380201151","callStatus":"IN_CONFERENCE","sipAddress":"sip:+<EMAIL>","startTime":1453492361938,"isMuted":false}],"nomoderatorOverrun":0,"videoActive":false,"created":1452714906250,"muteLocked":false,"active":true,"started":1453492361946,"hostInConference":true,"bridgeNumber":"1172","presenterNumber":-1,"recordNames":false,"supportScreensharing":true,"participantLocked":false,"moderatorSipAddress":"+<EMAIL>","updated":1453492361946,"allowSelfId":false}]}
i: {"apiVersion":1006,"cmd":"SUBSCRIBE","requestID":3,"capabilities":15,"participantType":"BRIA_DESKTOP","group":"imap.mobilevoiplive.com","bridge":"1172","pin":"10707","displayName":"Smokey the Bear"}
o: {"apiVersion":1006,"conferenceID":8,"requestID":3,"errorCode":"OK","cmd":"SUBSCRIBE_RESPONSE"}
o: {"conferenceDetail":{"supportVideo":true,"participantPin":"0707","lobbySipAddress":"sip:+<EMAIL>","description":"The description of vccs0007's conference","title":"Conference of vccs0007","supportAudio":true,"hosted":true,"socketModerator":true,"moderatorPin":"10707","sipAddress":"+<EMAIL>","supportMessaging":true,"id":8,"participants":[{"capabilities":15,"isModerator":true,"participantNumber":0,"displayName":"+12380201151","callStatus":"IN_CONFERENCE","sipAddress":"sip:+<EMAIL>","startTime":1453492361938,"isMuted":false}],"nomoderatorOverrun":0,"videoActive":false,"created":1452714906250,"muteLocked":false,"active":true,"started":1453492361946,"hostInConference":true,"bridgeNumber":"1172","presenterNumber":-1,"recordNames":false,"supportScreensharing":true,"participantLocked":false,"moderatorSipAddress":"+<EMAIL>","updated":1453492361946,"allowSelfId":false},"cmd":"CONFERENCE_UPDATED"}
i: {"cmd":"MUTE_PARTICIPANT","requestID":4,"conferenceID":8,"participantNumber":0}
o: {"conferenceID":8,"participantStatus":{"capabilities":15,"isModerator":true,"participantNumber":0,"displayName":"+12380201151","callStatus":"IN_CONFERENCE","sipAddress":"sip:+<EMAIL>","startTime":1453492361938,"isMuted":true},"cmd":"PARTICIPANT_UPDATED"}
o: {"apiVersion":1006,"conferenceID":8,"participantNumber":0,"requestID":4,"errorCode":"OK","cmd":"STATUS_RESPONSE","command":"MUTE_PARTICIPANT"}
i: {"cmd":"UNMUTE_PARTICIPANT","requestID":5,"conferenceID":8,"participantNumber":0}
o: {"conferenceID":8,"participantStatus":{"capabilities":15,"isModerator":true,"participantNumber":0,"displayName":"+12380201151","callStatus":"IN_CONFERENCE","sipAddress":"sip:+<EMAIL>","startTime":1453492361938,"isMuted":false},"cmd":"PARTICIPANT_UPDATED"}
o: {"apiVersion":1006,"conferenceID":8,"participantNumber":0,"requestID":5,"errorCode":"OK","cmd":"STATUS_RESPONSE","command":"UNMUTE_PARTICIPANT"}
i: {"cmd":"PARTICIPANT_IS_RECORDING","requestID":6,"conferenceID":8,"participantNumber":0}
o: {"conferenceID":8,"participantStatus":{"capabilities":15,"isModerator":true,"participantNumber":0,"displayName":"+12380201151","callStatus":"IN_CONFERENCE","sipAddress":"sip:+<EMAIL>","startTime":1453492361938,"isMuted":true,"isRecording":true},"cmd":"PARTICIPANT_UPDATED"}
o: {"apiVersion":1006,"conferenceID":8,"participantNumber":0,"requestID":6,"errorCode":"OK","cmd":"STATUS_RESPONSE","command":"PARTICIPANT_IS_RECORDING"}
i: {"cmd":"PARTICIPANT_NOT_RECORDING","requestID":7,"conferenceID":8,"participantNumber":0}
o: {"conferenceID":8,"participantStatus":{"capabilities":15,"isModerator":true,"participantNumber":0,"displayName":"+12380201151","callStatus":"IN_CONFERENCE","sipAddress":"sip:+<EMAIL>","startTime":1453492361938,"isMuted":false,"isRecording":false},"cmd":"PARTICIPANT_UPDATED"}
o: {"apiVersion":1006,"conferenceID":8,"participantNumber":0,"requestID":7,"errorCode":"OK","cmd":"STATUS_RESPONSE","command":"PARTICIPANT_NOT_RECORDING"}
i: {"cmd":"KICK_PARTICIPANT","requestID":8,"conferenceID":8,"participantNumber":0}
o: {"conferenceID":8,"participantStatus":{"capabilities":15,"isModerator":true,"participantNumber":0,"displayName":"+12380201151","callStatus":"KICKED","sipAddress":"sip:+<EMAIL>","startTime":1453492361938,"isMuted":false},"cmd":"PARTICIPANT_UPDATED","isSelf":true}
o: {"conferenceDetail":{"supportVideo":true,"participantPin":"0707","lobbySipAddress":"sip:+<EMAIL>","description":"The description of vccs0007's conference","title":"Conference of vccs0007","supportAudio":true,"hosted":true,"socketModerator":true,"moderatorPin":"10707","sipAddress":"+<EMAIL>","supportMessaging":true,"id":8,"participants":[{"capabilities":15,"isModerator":true,"participantNumber":0,"displayName":"+12380201151","callStatus":"KICKED","sipAddress":"sip:+<EMAIL>","startTime":1453492361938,"isMuted":false}],"nomoderatorOverrun":0,"videoActive":false,"created":1452714906250,"muteLocked":false,"active":false,"started":1453492361946,"hostInConference":true,"bridgeNumber":"1172","presenterNumber":-1,"recordNames":false,"supportScreensharing":true,"participantLocked":false,"moderatorSipAddress":"+<EMAIL>","updated":1453492433379,"allowSelfId":false},"cmd":"CONFERENCE_UPDATED"}
o: {"apiVersion":1006,"conferenceID":8,"participantNumber":0,"requestID":8,"errorCode":"OK","cmd":"STATUS_RESPONSE","command":"KICK_PARTICIPANT"}
i: {"cmd":"UNSUBSCRIBE","requestID":9,"group":"imap.mobilevoiplive.com","bridge":"1172"}
o: {"apiVersion":1006,"requestID":9,"errorCode":"OK","cmd":"STATUS_RESPONSE","command":"UNSUBSCRIBE"}
