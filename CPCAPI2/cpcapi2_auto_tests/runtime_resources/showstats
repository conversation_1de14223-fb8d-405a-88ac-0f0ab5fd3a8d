#!/bin/bash

if [[ $1 = "-h" ]]; then
   echo "showstats <number of file-uploads to monitor>"
   exit 1
fi

re='^[0-9]+$'
if ! [[ $1 =~ $re ]] ; then
   echo "error: <invalid argument>"
   echo ""
   echo "showstats <number of file-uploads to monitor>"
   exit 1
fi

# lines to monitor in tail
let "lines = $1 + 2"
clear; rm /opt/xmppfileupload/stats.out; touch /opt/xmppfileupload/stats.out;
watch -t -n 1 tail -n$lines /opt/xmppfileupload/stats.out
