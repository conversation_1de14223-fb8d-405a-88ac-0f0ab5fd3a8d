# Collab test accounts config file, for test collab_tests_tool.cpp
accountSettingsFile = vccsAccountSettings.json

# Collab link of the account in vccsAccountsSettings.json
collabLink = https://collab.cloudprovisioning.com:443/join/MWTFOMFEGL/counterpath.com

# Call duration in millisecond
callDurationMilliSec = 50000

# Relative path of the wav file
wavFilePath = file:ctclip3TestClip-16k.wav

# XMPP Proxy
xmppProxy = imp.softphone.com

# Skip all XMPP tests? (joining chat room, etc)
skipXmppTests = false

# Skip participant call status checks? Should skip if using tool for load testing
skipParticipantCallStatusCheck = false

# Waiting time for the conference_updated message after call ends
waitTimeForConferenceUpdateMilliSec = 20000
