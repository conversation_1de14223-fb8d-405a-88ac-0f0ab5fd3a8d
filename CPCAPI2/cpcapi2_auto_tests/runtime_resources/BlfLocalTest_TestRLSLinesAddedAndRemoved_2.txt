--UniqueBroadWorksBoundary
Content-Type:application/rlmi+xml
Content-Length:%content_length%
Content-ID:<MXnkrI@broadworks>

<?xml version="1.0" encoding="UTF-8"?>
<list xmlns="urn:ietf:params:xml:ns:rlmi" uri="sip:+<EMAIL>" version="0" fullState="true">
  <resource uri="sip:<EMAIL>">
    <name>Two Alianza</name>
    <instance id="LykKMW6tLg" state="terminated" reason="noresource" cid="ijWMSo@broadworks"/>
  </resource>
  <resource uri="sip:<EMAIL>">
    <name>Three Alianza</name>
    <instance id="Oo0RPZ84wu" state="active" cid="LCsRBM@broadworks"/>
  </resource>
</list>

--UniqueBroadWorksBoundary
Content-Type:application/dialog-info+xml
Content-Length:%content_length%
Content-ID:<LCsRBM@broadworks>

<?xml version="1.0" encoding="UTF-8"?>
<dialog-info xmlns="urn:ietf:params:xml:ns:dialog-info" version="0" state="full" entity="sip:<EMAIL>">
  <dialog id="NmhmUWxO" direction="recipient">
    <state>confirmed</state>
    <local>
      <identity display="Three Alianza">sip:<EMAIL></identity>
      <identity display="Three Alianza">tel:+41993570203;ext=203</identity>
    </local>
  </dialog>
</dialog-info>

--UniqueBroadWorksBoundary--
