<?xml version="1.0"?>
<wap-provisioningdoc version="1.1">
    <characteristic />
    <characteristic type="VERS">
        <parm name="version" value="1" />
        <parm name="validity" value="1728000" />
    </characteristic>
    <characteristic type="TOKEN">
        <parm name="token" value="X" />
    </characteristic>
    <characteristic type="MSG">
        <parm name="title" value="Example" />
        <parm name="message" value="Hello world" />
        <parm name="Accept_btn" value="X" />
        <parm name="Reject_btn" value="X" />
    </characteristic>
    <characteristic type="APPLICATION">
        <parm name="AppID" value="ap2001" />
        <parm name="Name" value="IMS Settings" />
        <parm name="AppRef" value="IMS-Settings" />
        <characteristic type="ConRefs">
            <parm name="ConRef" value="X" />
        </characteristic>
        <parm name="PDP_ContextOperPref" value="X" />
        <parm name="Timer_T1" value="X" />
        <parm name="Timer_T2" value="X" />
        <parm name="Timer_T4" value="X" />
        <parm name="Private_User_Identity" value="X" />
        <characteristic type="Public_User_Identity_List">
            <parm name="Public_User_Identity" value="X" />
        </characteristic>
        <parm name="Home_network_domain_name" value="X" />
        <characteristic type="Ext">
            <parm name="NatUrlFmt" value="1" />
            <parm name="IntUrlFmt" value="1" />
            <parm name="Q-Value" value="1.0" />
            <characteristic type="SecondaryDevicePar">
                <parm name="VoiceCall" value="0" />
                <parm name="Chat" value="0" />
                <parm name="SendSms" value="0" />
                <parm name="SendMms" value="0" />
                <parm name="FileTranfer" value="0" />
                <parm name="VideoShare" value="0" />
                <parm name="ImageShare" value="0" />
                <parm name="VideoCall" value="0" />
                <parm name="GeoLocPush" value="0" />
            </characteristic>
            <parm name="MaxSizeImageShare" value="0" />
            <parm name="MaxTimeVideoShare" value="0" />
            <characteristic type="Ext" />
        </characteristic>
        <characteristic type="ICSI_List">
            <parm name="ICSI" value="0" />
            <parm name="ICSI_Resource_Allocation_Mode" value="X" />
        </characteristic>
        <characteristic type="LBO_P-CSCF_Address">
            <parm name="Address" value="X" />
            <parm name="AddressType" value="X" />
        </characteristic>
        <parm name="Voice_Domain_Preference_E_UTRAN" value="X" />
        <parm name="SMS_Over_IP_Networks_Indication" value="X" />
        <parm name="Keep_Alive_Enabled" value="X" />
        <parm name="Voice_Domain_Preference_UTRAN" value="X" />
        <parm name="Mobility_Management_IMS_Voice_Termination" value="X" />
        <parm name="RegRetryBaseTime" value="X" />
        <parm name="RegRetryMaxTime" value="X" />
        <characteristic type="PhoneContext_List">
            <parm name="PhoneContext" value="X" />
            <parm name="Public_User_Identity" value="X" />
        </characteristic>
        <characteristic type="APPAUTH">
            <parm name="AuthType" value="X" />
            <parm name="Realm" value="X" />
            <parm name="UserName" value="X" />
            <parm name="UserPwd" value="X" />
        </characteristic>
    </characteristic>
    <characteristic type="APPLICATION">
        <parm name="AppID" value="ap2002" />
        <parm name="Name" value="RCS settings" />
        <parm name="AppRef" value="RCSe-Settings" />
        <characteristic type="IMS">
            <parm name="To-AppRef" value="IMS-Settings" />
        </characteristic>
        <characteristic type="SERVICES">
            <parm name="presencePrfl" value="X" />
            <parm name="ChatAuth" value="X" />
            <parm name="GroupChatAuth" value="X" />
            <parm name="ftAuth" value="X" />
            <parm name="standaloneMsgAuth" value="X" />
            <parm name="geolocPullAuth" value="X" />
            <parm name="geolocPushAuth" value="X" />
            <parm name="vsAuth" value="X" />
            <parm name="isAuth" value="X" />
            <parm name="rcsIPVoiceCallAuth" value="X" />
            <parm name="rcsIPVideoCallAuth" value="X" />
            <characteristic type="Ext" />
        </characteristic>
        <characteristic type="PRESENCE">
            <parm name="AvailabilityAuth" value="X" />
            <characteristic type="FAVLINK">
                <parm name="AutMa" value="X" />
                <characteristic type="LINKS">
                    <parm name=" OpFavUrl1" value="X" />
                    <parm name=" OpFavUrl2" value="X" />
                    <parm name=" OpFavUrl3" value="X" />
                </characteristic>
                <parm name="LabelMaxLength" value="X" />
            </characteristic>
            <parm name="IconMaxSize" value="X" />
            <parm name="NoteMaxSize" value="X" />
            <characteristic type="VIPCONTACTS">
                <parm name="NonVipPollPeriodSetting" value="X" />
                <parm name="NonVipMaxPollPerPeriod" value="X" />
            </characteristic>
            <parm name="PublishTimer" value="X" />
            <parm name="NickNameLength" value="X" />
            <characteristic type="Location">
                <parm name="TextMaxLength" value="X" />
                <parm name="LocInfoMaxValidTime" value="X" />
            </characteristic>
            <characteristic type="Ext" />
            <parm name="client-obj-datalimit" value="X" />
            <parm name="content-serveruri" value="X" />
            <parm name="source-throttlepublish" value="X" />
            <parm name="max-number-ofsubscriptions-inpresence-list" value="X" />
            <parm name="service-uritemplate" value="X" />
            <parm name="RLS-URI" value="X" />
        </characteristic>
        <characteristic type="XDMS">
            <parm name="RevokeTimer" value="X" />
            <parm name="enablePNBManagement" value="X" />
            <parm name="enableXDMSubscribe" value="X" />
            <characteristic type="Ext" />
            <parm name="XCAPRootURI" value="X" />
            <parm name="XCAPAuthenticationUserName" value="X" />
            <parm name="XCAPAuthenticationSecret" value="X" />
            <parm name="XCAPAuthenticationType" value="X" />
        </characteristic>
        <characteristic type="SUPL">
            <parm name="TextMaxLength" value="X" />
            <parm name="LocInfoMaxValidTime" value="X" />
            <parm name="geolocPullOpen" value="X" />
            <parm name="geolocPullApiGwAddress" value="X" />
            <parm name="geolocPullBlockTimer" value="X" />
            <characteristic type="Ext" />
            <parm name="Addr" value="X" />
            <parm name="AddrType" value="X" />
        </characteristic>
        <characteristic type="IM">
            <parm name="imMsgTech" value="X" />
            <parm name="imCapAlwaysON" value="X" />
            <parm name="GroupChatFullStandFwd" value="X " />
            <parm name="GroupChatOnlyFStandFwd" value="X" />
            <parm name="imWarnSF" value="X" />
            <parm name="SmsFallBackAuth" value="X" />
            <parm name="imCapNonRCS" value="X" />
            <parm name="imWarnIW" value="X" />
            <parm name="AutAccept" value="X" />
            <parm name="imSessionStart" value="X" />
            <parm name="AutAcceptGroupChat" value="X" />
            <parm name="firstMessageInvite" value="X" />
            <parm name="TimerIdle" value="X" />
            <parm name="MaxConcurrentSession" value="X" />
            <parm name="multiMediaChat" value="X" />
            <parm name="MaxSize1to1" value="X" />
            <parm name="MaxSize1toM" value="X" />
            <parm name="ftWarnSize" value="X" />
            <parm name="MaxSizeFileTr" value="X" />
            <parm name="ftThumb" value="X" />
            <parm name="ftStAndFwEnabled" value="X" />
            <parm name="ftCapAlwaysON" value="X" />
            <parm name="ftAutAccept" value="X" />
            <parm name="ftHTTPCSURI" value="X" />
            <parm name="ftHTTPCSUser" value="X" />
            <parm name="ftHTTPCSPwd" value="X" />
            <parm name="ftDefaultMech" value="X" />
            <characteristic type="Ext" />
            <parm name="pres-srv-cap" value="X" />
            <parm name="deferred-msg-func-uri" value="X" />
            <parm name="max_adhoc_group_size" value="X" />
            <parm name="conf-fcty-uri" value="X" />
            <parm name="exploder-uri" value="X" />
        </characteristic>
        <characteristic type="CPM">
            <characteristic type="StandaloneMsg">
                <parm name="MaxSizeStandalone" value="X" />
            </characteristic>
            <characteristic type="MessageStore">
                <parm name="Url" value="X" />
                <parm name="AuthProt" value="X" />
                <parm name="UserName" value="X" />
                <parm name="UserPwd" value="X" />
            </characteristic>
            <characteristic type="Ext" />
        </characteristic>
        <characteristic type="CAPDISCOVERY">
            <parm name="pollingPeriod" value="X" />
            <parm name="pollingRate" value="X" />
            <parm name="pollingRatePeriod" value="X" />
            <parm name="capInfoExpiry" value="X" />
            <parm name="defaultDisc" value="X" />
            <parm name="capDiscCommonStack" value="X" />
            <characteristic type="Ext" />
        </characteristic>
        <characteristic type="APN">
            <parm name="rcseOnlyAPN" value="X" />
            <parm name="enableRcseSwitch" value="X" />
            <parm name="alwaysUseIMSAPN" value="X" />
            <characteristic type="EXT" />
        </characteristic>
        <characteristic type="OTHER">
            <parm name="endUserConfReqId" value="X" />
            <parm name="allowVSSave" value="X" />
            <characteristic type=" transportProto">
                <parm name="psSignalling" value="X" />
                <parm name="psMedia" value="X" />
                <parm name="psRTMedia" value="X" />
                <parm name="wifiSignalling" value="X" />
                <parm name="wifiMedia" value="X" />
                <parm name="wifiRTMedia" value="X" />
            </characteristic>
            <parm name="uuid_Value" value="X" />
            <parm name="IPCallBreakOut" value="X" />
            <parm name="IPCallBreakOutCS" value="X" />
            <parm name="rcsIPVideoCallUpgradeFromCS" value="X" />
            <parm name="rcsIPVideoCallUpgradeOnCapError" value="X" />
            <parm name="rcsIPVideoCallUpgradeAttemptEarly" value="X" />
            <characteristic type="Ext" />
        </characteristic>
        <characteristic type="SERVICEPROVIDEREXT" />
    </characteristic>
</wap-provisioningdoc>