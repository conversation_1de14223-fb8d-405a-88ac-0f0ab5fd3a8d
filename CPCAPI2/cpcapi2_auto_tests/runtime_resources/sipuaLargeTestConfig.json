{"sipuaLargeTestConfig": [{"environmentId": "d2", "platformType": "CPE1", "environmentType": "DEV", "partitionId": "B9JNiBLFQ9GuXvHbqc65wQ", "carrierId": "tFR4mbmvSGqAZWulL-FoKg", "sipGatewayProxy": "sip-gateway.dev.useast2.dev.qa.alz.ninja", "numberLeaserUrl": "http://number-leaser.useast2.dev.qa.alz.ninja", "inboundRatePlanProductId": "QH53qMI0QJOzudGZZVrm7A", "callIngPlanProductId": "L-zIriSmTkWk_3_vg-9Qmg", "accountRoutePlanId": "dfa74671-b20c-40c0-8f39-143bb559d3ef", "sipDomain": "alz000.sip.dev.us.cymbus.net", "sipOutboundProxy": "alz000.sip.dev.us.cymbus.net", "regionId": "uswest2", "sipTransportType": "UDP", "publicApiUrl": "https://api.d2.alianza.com/v2/", "extensionLength": 4}, {"environmentId": "q2", "platformType": "CPE1", "environmentType": "QA", "partitionId": "lcl1_pn_RpSK8T5chhOYnQ", "carrierId": "HPjb4XhLQSiRelNzLUFFbw", "sipGatewayProxy": "sip-gateway.qa.useast2.dev.qa.alz.ninja", "numberLeaserUrl": "http://number-leaser.useast2.dev.qa.alz.ninja", "inboundRatePlanProductId": "OrHkOsnZS_SXbf9e_bZAiA", "callIngPlanProductId": "yfKgUkadRhy8A0WkeFKb_w", "accountRoutePlanId": "8291daf3-a810-4d7a-a1af-171e8540edf6", "sipDomain": "alz000.sip.qa.us.cymbus.net", "sipOutboundProxy": "alz000.sip.qa.us.cymbus.net", "regionId": "uswest2", "sipTransportType": "UDP", "publicApiUrl": "https://api.q2.alianza.com/v2/", "extensionLength": 4}, {"environmentId": "b2", "platformType": "CPE1", "environmentType": "BETA", "partitionId": "lTLlVV7mQX2D21hhFTMMBQ", "carrierId": "3bwjH65oRFKM268Kru4riw", "sipGatewayProxy": "sip-gateway.beta.useast2.dev.qa.alz.ninja", "numberLeaserUrl": "http://number-leaser.useast2.dev.qa.alz.ninja", "inboundRatePlanProductId": "0WhcPMCXTIKEzUr03X_tlw", "callIngPlanProductId": "0klqIbXeRcOmwpCOoj1CGA", "accountRoutePlanId": "8a8f3565-b8f1-495f-a7ed-b9ab324603d7", "sipDomain": "alz000.sip.beta.us.cymbus.net", "sipOutboundProxy": "alz000.sip.beta.us.cymbus.net", "regionId": "uswest2", "sipTransportType": "UDP", "publicApiUrl": "https://api.b2.alianza.com/v2/", "extensionLength": 4}, {"environmentId": "p2", "platformType": "CPE1", "environmentType": "PROD", "partitionId": "uiAgqM4QTReonzQNLxy5Og", "carrierId": "D4BcqfLlT0qWbkGZc2i9KA", "sipGatewayProxy": "sip-gateway.prod.useast2.dev.qa.alz.ninja", "numberLeaserUrl": "http://number-leaser.useast2.dev.qa.alz.ninja", "inboundRatePlanProductId": "X96jZXiCTxibzBW3bz28TA", "callIngPlanProductId": "zH5xGSyyQt-R3rVCwanrHA", "accountRoutePlanId": "04e20157-15b3-4457-a81a-ec626968421e", "sipDomain": "alz000.sip.us.cymbus.net", "sipOutboundProxy": "alz000.sip.us.cymbus.net", "regionId": "useast2", "sipTransportType": "UDP", "publicApiUrl": "https://api.alianza.com/v2/", "extensionLength": 4}, {"environmentId": "dev", "platformType": "CPE2", "environmentType": "DEV", "partitionId": "B9JNiBLFQ9GuXvHbqc65wQ", "carrierId": "tFR4mbmvSGqAZWulL-FoKg", "sipGatewayProxy": "sip-gateway.dev.useast2.dev.qa.alz.ninja", "numberLeaserUrl": "http://number-leaser.useast2.dev.qa.alz.ninja", "inboundRatePlanProductId": "QH53qMI0QJOzudGZZVrm7A", "callIngPlanProductId": "L-zIriSmTkWk_3_vg-9Qmg", "accountRoutePlanId": "dfa74671-b20c-40c0-8f39-143bb559d3ef", "sipDomain": "alz000.sip.dev.us.cymbus.net", "sipOutboundProxy": "alz000.sip.dev.us.cymbus.net", "regionId": "uswest2", "sipTransportType": "UDP", "publicApiUrl": "https://api.d2.alianza.com/v2/", "extensionLength": 4}, {"environmentId": "qa", "platformType": "CPE2", "environmentType": "QA", "partitionId": "lcl1_pn_RpSK8T5chhOYnQ", "carrierId": "HPjb4XhLQSiRelNzLUFFbw", "sipGatewayProxy": "sip-gateway.qa.useast2.dev.qa.alz.ninja", "numberLeaserUrl": "http://number-leaser.useast2.dev.qa.alz.ninja", "inboundRatePlanProductId": "OrHkOsnZS_SXbf9e_bZAiA", "callIngPlanProductId": "yfKgUkadRhy8A0WkeFKb_w", "accountRoutePlanId": "8291daf3-a810-4d7a-a1af-171e8540edf6", "sipDomain": "alz000.sip.qa.us.cymbus.net", "sipOutboundProxy": "alz000.sip.qa.us.cymbus.net", "regionId": "uswest2", "sipTransportType": "UDP", "publicApiUrl": "https://api.q2.alianza.com/v2/", "extensionLength": 4}, {"environmentId": "beta", "platformType": "CPE2", "environmentType": "BETA", "partitionId": "lTLlVV7mQX2D21hhFTMMBQ", "carrierId": "3bwjH65oRFKM268Kru4riw", "sipGatewayProxy": "sip-gateway.beta.useast2.dev.qa.alz.ninja", "numberLeaserUrl": "http://number-leaser.useast2.dev.qa.alz.ninja", "inboundRatePlanProductId": "0WhcPMCXTIKEzUr03X_tlw", "callIngPlanProductId": "0klqIbXeRcOmwpCOoj1CGA", "accountRoutePlanId": "8a8f3565-b8f1-495f-a7ed-b9ab324603d7", "sipDomain": "alz000.sip.beta.us.cymbus.net", "sipOutboundProxy": "alz000.sip.beta.us.cymbus.net", "regionId": "uswest2", "sipTransportType": "UDP", "publicApiUrl": "https://api.b2.alianza.com/v2/", "extensionLength": 4}, {"environmentId": "prod", "platformType": "CPE2", "environmentType": "PROD", "partitionId": "uiAgqM4QTReonzQNLxy5Og", "carrierId": "D4BcqfLlT0qWbkGZc2i9KA", "sipGatewayProxy": "sip-gateway.prod.useast2.dev.qa.alz.ninja", "numberLeaserUrl": "http://number-leaser.useast2.dev.qa.alz.ninja", "inboundRatePlanProductId": "X96jZXiCTxibzBW3bz28TA", "callIngPlanProductId": "zH5xGSyyQt-R3rVCwanrHA", "accountRoutePlanId": "04e20157-15b3-4457-a81a-ec626968421e", "sipDomain": "alz000.sip.us.cymbus.net", "sipOutboundProxy": "alz000.sip.us.cymbus.net", "regionId": "useast2", "sipTransportType": "UDP", "publicApiUrl": "https://api.alianza.com/v2/", "extensionLength": 4}, {"environmentId": "repro", "platformType": "REPRO", "environmentType": "DEV", "partitionId": "", "carrierId": "", "sipGatewayProxy": "", "numberLeaserUrl": "", "inboundRatePlanProductId": "", "callIngPlanProductId": "", "accountRoutePlanId": "", "sipDomain": "cp.local", "sipOutboundProxy": "127.0.0.1:6060", "regionId": "", "sipTransportType": "UDP", "publicApiUrl": "", "extensionLength": 4}]}