# Collab test accounts config file, for test collab_tests_tool.cpp
accountSettingsFile = vccsAccountSettingsNetChange.json

# Collab link of the account in vccsAccountSettingsNetChange.json
collabLink = https://imap.mobilevoiplive.com:8990/join/VLTKEYAAAA/imap.mobilevoiplive.com

# Call duration in millisecond
callDurationMilliSec = 600000

# Relative path of the wav file
wavFilePath = file:_ctclip3TestClip-16k.wav

# XMPP Proxy
xmppProxy = imp.softphone.com

# Skip all XMPP tests? (joining chat room, etc)
skipXmppTests = true

# Skip participant call status checks? Should skip if using tool for load testing
skipParticipantCallStatusCheck = true

# Waiting time for the conference_updated message after call ends
waitTimeForConferenceUpdateMilliSec = 20000
