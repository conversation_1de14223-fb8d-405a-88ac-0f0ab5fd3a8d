<?xml version="1.0" encoding="UTF-8" ?>
<scenario name="Basic UAS scenario">


  <send retrans="500">
  <![CDATA[
  INVITE sip:jziph@[remote_ip] SIP/2.0
  Via: SIP/2.0/[transport] [local_ip]:[local_port]
  From: sipp <sip:sipp@[local_ip]:[local_port]>;tag=[call_number]
  To: tel:[service]
  Call-ID: [call_id]
  Cseq: 1 INVITE
  Contact: sip:sipp@[local_ip]:[local_port]
  Content-Type: application/sdp
  Content-Length: [len]
  Max-Forwards: 100

  v=0
  o=user1 53655765 2353687637 IN IP[local_ip_type] [local_ip]
  s=-
  c=IN IP[media_ip_type] *********
  t=0 0
  m=audio 6000 RTP/AVP 120
  a=rtpmap:120 opus/48000/2
  a=fmtp:120 useinbandfec=1; usetx=1; maxaveragebitrate=64000; minptime=10
  m=video 6010 RTP/AVP 117
  a=rtpmap:117 H264/90000
  a=fmtp:117 profile-level-id=42801f; max-br=2000
  a=rtcp-fb:117 nack pli
  a=rtcp-fb:117 nack
  a=sendrecv
]]>
 </send>

  <recv response="100" optional="true"/>

  <recv response="180" optional="true"/>

  <recv response="183" optional="true"/>

  <recv response="200" rrs="true" />

 <send>
  <![CDATA[
  ACK sip:[service]@[remote_ip]:[remote_port] SIP/2.0
  Via: SIP/2.0/[transport] [local_ip]:[local_port]
  From: sipp <sip:sipp@[local_ip]:[local_port]>;tag=[call_number]
  To: <tel:[service]>[peer_tag_param]
  Call-ID: [call_id]
  Cseq: 1 ACK
  Contact: sip:sipp@[local_ip]:[local_port]
  Content-Length: 0
  Max-Forwards: 100
  ]]>
 </send>

 <recv request="BYE">
 </recv>

 <send>
  <![CDATA[
  SIP/2.0 200 OK
  [last_Via:]
  [last_From:]
  [last_To:]
  [last_Call-ID:]
  [last_CSeq:]
  Contact: <sip:[local_ip]:[local_port];transport=[transport]>
  Content-Length: 0
  ]]>
 </send>
 	
</scenario>
