<?xml version="1.0" encoding="UTF-8" ?>
<scenario name="Basic UAS scenario">
 <recv request="SUBSCRIBE">
  
  <action>
   <!-- since we need to send a request to the remote part -->
   <!-- we need to extract the Contact and the From header content -->
   <!-- <ereg regexp=".*" search_in="hdr" header="From" assign_to="remote_from"/> -->
   <!-- assign the content of the Contaact SIP URI to the remote_contact var -->
   <!-- first var of assign_to contains the whole match -->
   <ereg regexp="sip:(.*)>.*" search_in="hdr" header="Contact" assign_to="trash"/>
   <ereg regexp="sip:.*;tag=(.*)" search_in="hdr" header="From:" check_it="true" assign_to="1,SdkTag" />
  </action>
 </recv>
 <!-- since <PERSON><PERSON><PERSON> complains about not used variable reference the trach var -->
 
 <Reference variables="trash"/>
 <Reference variables="1"/>

 <send>
  <![CDATA[
  SIP/2.0 200 OK
  [last_Via:]
  [last_From:]
  [last_To:];tag=[pid]SIPpTag01
  [last_Call-ID:]
  [last_CSeq:]
  Contact: <sip:[local_ip]:[local_port];transport=[transport]>
  Content-Length: 0
  ]]>
 </send>

 <send>
  <![CDATA[  
    NOTIFY sip:6417@*************:6771;rinstance=133dd15ec46a5717 SIP/2.0
    Record-Route: <sip:**************:5060;r2=on;lr=on;ftag=92d93447-26dc-47f6-be90-0ff5f4cddba3;uuid=c75e8532-7357-439c-bcaf-95ceec5a322d>
    Record-Route: <sip:************:5080;r2=on;lr=on;ftag=92d93447-26dc-47f6-be90-0ff5f4cddba3;uuid=c75e8532-7357-439c-bcaf-95ceec5a322d>
    [last_Via:]
    Via: SIP/2.0/UDP **************:5060;branch=z9hG4bK590e.5316988ceb43de3472c87cf53219e45b.0
    Via: SIP/2.0/UDP ************:5060;received=************;rport=5060;branch=z9hG4bKPj7d46cd23-5576-4cdb-af8a-680cdcb27444
    From: sip:[service]@[local_ip]:[local_port];tag=[pid]SIPpTag01
    To: sip:sdk@[remote_ip]:[remote_port];tag=[$SdkTag]
    [last_Call-ID:]
    CSeq: 10 NOTIFY
    Contact: <sip:[local_ip]:[local_port];transport=[transport]>
    Event: presence
    Subscription-State: active;expires=3415
    Allow-Events: message-summary, presence, dialog, refer
    Max-Forwards: 69
    User-Agent: OKG PBX
    Content-Type: application/pidf+xml
    Content-Length: [len]

    <?xml version="1.0" encoding="UTF-8"?>
    <presence entity="sip:6417@************:5060" xmlns="urn:ietf:params:xml:ns:pidf" xmlns:dm="urn:ietf:params:xml:ns:pidf:data-model" xmlns:rpid="urn:ietf:params:xml:ns:pidf:rpid">
     <note>On hold</note>
     <tuple id="6417">
      <status>
       <basic>open</basic>
      </status>
      <contact priority="1">sip:<EMAIL></contact>
     </tuple>
     <dm:person>
      <rpid:activities>
       <rpid:on-the-phone />
      </rpid:activities>
     </dm:person>
    </presence>
  ]]>
 </send>
 
 <recv response="200" rrs="true" />
 	
</scenario>
