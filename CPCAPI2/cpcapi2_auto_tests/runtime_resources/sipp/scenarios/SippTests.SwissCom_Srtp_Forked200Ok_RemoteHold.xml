<?xml version="1.0" encoding="UTF-8" ?>
<scenario name="Basic UAS scenario">
 <recv request="INVITE">
  
  <action>
   <!-- since we need to send a request to the remote part -->
   <!-- we need to extract the Contact and the From header content -->
   <!-- <ereg regexp=".*" search_in="hdr" header="From" assign_to="remote_from"/> -->
   <!-- assign the content of the Contaact SIP URI to the remote_contact var -->
   <!-- first var of assign_to contains the whole match -->
   <ereg regexp="sip:(.*)>.*" search_in="hdr" header="Contact" assign_to="trash"/>
   <ereg regexp=".*" search_in="hdr" header="From" assign_to="remote_from"/>
  </action>
 </recv>
 <!-- since <PERSON><PERSON><PERSON> complains about not used variable reference the trach var -->
 
 <Reference variables="trash"/>

 <send retrans="500">
   <![CDATA[
   SIP/2.0 180 Ringing
   [last_Via:]
   [last_From:]
   [last_To:];tag=[pid]SIPpTag01[call_number]xyz
   [last_Call-ID:]
   [last_CSeq:]
   Contact: <sip:[local_ip]:[local_port];transport=[transport]>
   Content-Length: 0
   ]]>
 </send>

 <pause milliseconds="5000"/>

 <!-- OBELISK-6349: Important part to the test; 200 OK has different tag for To header, triggering branching handling in the SDK -->
 <send retrans="500">
   <![CDATA[
   SIP/2.0 200 OK
   [last_Via:]
   [last_From:]
   [last_To:];tag=[pid]SIPpTag01[call_number]
   [last_Call-ID:]
   [last_CSeq:]
   Contact: <sip:[local_ip]:[local_port];transport=[transport]>
   Content-Type: application/sdp
   Content-Length: [len]

   v=0
   o=user1 53655765 2353687637 IN IP[local_ip_type] [local_ip]
   s=-
   c=IN IP[media_ip_type] [media_ip]
   t=0 0
   m=audio [media_port] RTP/SAVP 0 101
   a=crypto:3 AES_CM_128_HMAC_SHA1_80 inline:XU5JZBWufcpZYCQVUA0pYnmzXssZN+qmnsDdN/va
   ]]>
 </send>

 <recv request="ACK" crlf="true">
 </recv>

 <!-- place call on hold -->
 <send retrans="500">
   <![CDATA[
   INVITE sip:foo@[remote_ip] SIP/2.0
   Via: SIP/2.0/[transport] [local_ip]:[local_port];branch=[branch]
   From: <sip:sipp@[local_ip]:[local_port]>;tag=[pid]SIPpTag01[call_number]
   To[$remote_from]
   [last_Call-ID:]
   CSeq: 10 INVITE
   Contact: <sip:[local_ip]:[local_port];transport=[transport]>
   Content-Type: application/sdp
   Content-Length: [len]

   v=0
   o=user1 53655765 2353687638 IN IP[local_ip_type] [local_ip]
   s=-
   c=IN IP[media_ip_type] [media_ip]
   t=0 0
   m=audio [media_port] RTP/SAVP 0 101
   a=crypto:3 AES_CM_128_HMAC_SHA1_80 inline:XU5JZBWufcpZYCQVUA0pYnmzXssZN+qmnsDdN/va
   a=sendonly
   ]]>
 </send>

 <recv response="100" optional="true"/>

 <recv response="200" rtd="true">
 </recv>

 <send>
   <![CDATA[
   ACK sip:[service]@[remote_ip]:[remote_port] SIP/2.0
   Via: SIP/2.0/[transport] [local_ip]:[local_port];branch=[branch]
   From: <sip:sipp@[local_ip]:[local_port]>;tag=[pid]SIPpTag01[call_number]
   To[$remote_from]
   Call-ID: [call_id]
   CSeq: 10 ACK
   Contact: sip:sipp@[local_ip]:[local_port]
   Max-Forwards: 70
   Content-Length: 0
   ]]>
 </send>

 <pause milliseconds="3000"/>


 <!-- take call off hold -->
 <send retrans="500">
   <![CDATA[
   INVITE sip:foo@[remote_ip] SIP/2.0
   Via: SIP/2.0/[transport] [local_ip]:[local_port];branch=[branch]
   From: <sip:sipp@[local_ip]:[local_port]>;tag=[pid]SIPpTag01[call_number]
   To[$remote_from]
   [last_Call-ID:]
   CSeq: 11 INVITE
   Contact: <sip:[local_ip]:[local_port];transport=[transport]>
   Content-Type: application/sdp
   Content-Length: [len]

   v=0
   o=user1 53655765 2353687639 IN IP[local_ip_type] [local_ip]
   s=-
   c=IN IP[media_ip_type] [media_ip]
   t=0 0
   m=audio [media_port] RTP/SAVP 0 101
   a=crypto:3 AES_CM_128_HMAC_SHA1_80 inline:XU5JZBWufcpZYCQVUA0pYnmzXssZN+qmnsDdN/va
   ]]>
 </send>

 <recv response="100" optional="true"/>

 <recv response="200" rtd="true">
 </recv>

 <send>
   <![CDATA[
   ACK sip:[service]@[remote_ip]:[remote_port] SIP/2.0
   Via: SIP/2.0/[transport] [local_ip]:[local_port];branch=[branch]
   From: <sip:sipp@[local_ip]:[local_port]>;tag=[pid]SIPpTag01[call_number]
   To[$remote_from]
   Call-ID: [call_id]
   CSeq: 11 ACK
   Contact: sip:sipp@[local_ip]:[local_port]
   Max-Forwards: 70
   Content-Length: 0
   ]]>
 </send>


 <pause milliseconds="1000"/>

 <!-- no SDP change but From header has changed -->
 <send retrans="500">
   <![CDATA[
   INVITE sip:foo@[remote_ip] SIP/2.0
   Via: SIP/2.0/[transport] [local_ip]:[local_port];branch=[branch]
   From: "Display Name Now Present" <sip:sipp@[local_ip]:[local_port]>;tag=[pid]SIPpTag01[call_number]
   To[$remote_from]
   [last_Call-ID:]
   CSeq: 12 INVITE
   Contact: <sip:[local_ip]:[local_port];transport=[transport]>
   Content-Type: application/sdp
   Content-Length: [len]

   v=0
   o=user1 53655765 2353687639 IN IP[local_ip_type] [local_ip]
   s=-
   c=IN IP[media_ip_type] [media_ip]
   t=0 0
   m=audio [media_port] RTP/SAVP 0 101
   a=crypto:3 AES_CM_128_HMAC_SHA1_80 inline:XU5JZBWufcpZYCQVUA0pYnmzXssZN+qmnsDdN/va
   ]]>
 </send>

 <recv response="100" optional="true"/>

 <recv response="200" rtd="true">
 </recv>

 <send>
   <![CDATA[
   ACK sip:[service]@[remote_ip]:[remote_port] SIP/2.0
   Via: SIP/2.0/[transport] [local_ip]:[local_port];branch=[branch]
   From: <sip:sipp@[local_ip]:[local_port]>;tag=[pid]SIPpTag01[call_number]
   To[$remote_from]
   Call-ID: [call_id]
   CSeq: 12 ACK
   Contact: sip:sipp@[local_ip]:[local_port]
   Max-Forwards: 70
   Content-Length: 0
   ]]>
 </send>

 
 <recv request="BYE">
 </recv>

 <send>
   <![CDATA[
   SIP/2.0 200 OK
   [last_Via:]
   [last_From:]
   [last_To:]
   [last_Call-ID:]
   [last_CSeq:]
   Contact: <sip:[local_ip]:[local_port];transport=[transport]>
   Content-Length: 0
   ]]>
 </send>

</scenario>
