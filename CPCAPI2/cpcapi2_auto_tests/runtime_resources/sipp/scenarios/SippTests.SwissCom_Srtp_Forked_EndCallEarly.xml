<?xml version="1.0" encoding="UTF-8" ?>
<scenario name="Basic UAS scenario">
 <recv request="INVITE">
  
  <action>
   <!-- since we need to send a request to the remote part -->
   <!-- we need to extract the Contact and the From header content -->
   <!-- <ereg regexp=".*" search_in="hdr" header="From" assign_to="remote_from"/> -->
   <!-- assign the content of the Contaact SIP URI to the remote_contact var -->
   <!-- first var of assign_to contains the whole match -->
   <ereg regexp="sip:(.*)>.*" search_in="hdr" header="Contact" assign_to="trash"/>
  </action>
 </recv>
 <!-- since <PERSON><PERSON><PERSON> complains about not used variable reference the trach var -->
 
 <Reference variables="trash"/>

 <send retrans="500">
  <![CDATA[
  SIP/2.0 180 Ringing
  [last_Via:]
  [last_From:]
  [last_To:];tag=[pid]SIPpTag01[call_number]xyz
  [last_Call-ID:]
  [last_CSeq:]
  Contact: <sip:[local_ip]:[local_port];transport=[transport]>
  Content-Length: 0
  ]]>
 </send>

 <pause milliseconds="10"/> 

 <!-- 180 forking (different To tag) seen on Swisscom network.
      If the app ends the SDK in between these, a crash was hit --> 
 <send retrans="500">
  <![CDATA[
  SIP/2.0 180 Ringing
  [last_Via:]
  [last_From:]
  [last_To:];tag=[pid]SIPpTag01[call_number]abc
  [last_Call-ID:]
  [last_CSeq:]
  Contact: <sip:[local_ip]:[local_port];transport=[transport]>
  Content-Length: 0
  ]]>
 </send>

 <recv request="CANCEL" lost="100"/>


 <pause milliseconds="5000000"/>

 <send retrans="500">
  <![CDATA[
  SIP/2.0 200 OK
  [last_Via:]
  [last_From:]
  [last_To:];tag=[pid]SIPpTag01[call_number]
  [last_Call-ID:]
  [last_CSeq:]
  Contact: <sip:[local_ip]:[local_port];transport=[transport]>
  Content-Type: application/sdp
  Content-Length: [len]
  
  v=0
  o=user1 53655765 2353687637 IN IP[local_ip_type] [local_ip]
  s=-
  c=IN IP[media_ip_type] [media_ip]
  t=0 0
  m=audio [media_port] RTP/SAVP 0 101
  a=crypto:3 AES_CM_128_HMAC_SHA1_80 inline:XU5JZBWufcpZYCQVUA0pYnmzXssZN+qmnsDdN/va
  ]]>
 </send>

 <recv request="ACK" crlf="true">
 </recv>
 
 <recv request="BYE">
 </recv>

 <send>
  <![CDATA[
  SIP/2.0 200 OK
  [last_Via:]
  [last_From:]
  [last_To:]
  [last_Call-ID:]
  [last_CSeq:]
  Contact: <sip:[local_ip]:[local_port];transport=[transport]>
  Content-Length: 0
  ]]>
 </send>
 	
</scenario>
