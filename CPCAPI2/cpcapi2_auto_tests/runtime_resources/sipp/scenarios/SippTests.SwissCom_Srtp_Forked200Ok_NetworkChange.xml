<?xml version="1.0" encoding="UTF-8" ?>
<scenario name="Basic UAS scenario">
 <recv request="INVITE">
  
  <action>
   <!-- since we need to send a request to the remote part -->
   <!-- we need to extract the Contact and the From header content -->
   <!-- <ereg regexp=".*" search_in="hdr" header="From" assign_to="remote_from"/> -->
   <!-- assign the content of the Contact SIP URI to the remote_contact var -->
   <!-- first var of assign_to contains the whole match -->
   <ereg regexp="sip:(.*)>.*" search_in="hdr" header="Contact" assign_to="trash"/>
  </action>
 </recv>
 <!-- since S<PERSON><PERSON> complains about not used variable reference the trach var -->
 
 <Reference variables="trash"/>

 <send>
  <![CDATA[
  SIP/2.0 180 Ringing
  [last_Via:]
  [last_From:]
  To: "John Kimble"<sip:sipp@127.0.0.1:50010>;tag=[pid]SIPpTag01[call_number]xyz
  [last_Call-ID:]
  [last_CSeq:]
  Contact: <sip:[local_ip]:[local_port];transport=[transport]>
  Content-Length: 0
  ]]>
 </send>

 <pause milliseconds="5000"/>

 <!-- Important part of the test; 200 OK has different tag for To header, triggering branching handling in the SDK -->
 <send retrans="500">
  <![CDATA[
  SIP/2.0 200 OK
  [last_Via:]
  [last_From:]
  To: "John Kimble"<sip:sipp@127.0.0.1:50010>;tag=[pid]SIPpTag01[call_number]
  [last_Call-ID:]
  [last_CSeq:]
  Contact: <sip:[local_ip]:[local_port];transport=[transport]>
  Content-Type: application/sdp
  Content-Length: [len]
  
  v=0
  o=user1 53655765 2353687637 IN IP[local_ip_type] [local_ip]
  s=-
  c=IN IP[media_ip_type] [media_ip]
  t=0 0
  m=audio [media_port] RTP/SAVP 0 101
  ]]>
 </send>

 <recv request="ACK" crlf="true">
 </recv>

<pause milliseconds="20000"/>
 	
</scenario>
