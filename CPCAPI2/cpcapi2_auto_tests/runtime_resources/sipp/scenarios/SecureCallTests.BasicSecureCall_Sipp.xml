<?xml version="1.0" encoding="UTF-8" ?>
<scenario name="Basic UAS scenario">
 <recv request="INVITE">
  
  <action>
   <!-- since we need to send a request to the remote part -->
   <!-- we need to extract the Contact and the From header content -->
   <!-- <ereg regexp=".*" search_in="hdr" header="From" assign_to="remote_from"/> -->
   <!-- assign the content of the Contaact SIP URI to the remote_contact var -->
   <!-- first var of assign_to contains the whole match -->
   <ereg regexp="sip:(.*)>.*" search_in="hdr" header="Contact" assign_to="trash"/>
  </action>
 </recv>
 <!-- since <PERSON><PERSON><PERSON> complains about not used variable reference the trach var -->
 
 <Reference variables="trash"/>

 <send retrans="500">
  <![CDATA[
  SIP/2.0 200 OK
  [last_Via:]
  [last_From:]
  [last_To:];tag=[pid]SIPpTag01[call_number]
  [last_Call-ID:]
  [last_CSeq:]
  Contact: <sip:[local_ip]:[local_port];transport=[transport]>
  Content-Type: application/sdp
  Content-Length: [len]
  
  v=0
  o=user1 53655765 2353687637 IN IP[local_ip_type] [local_ip]
  s=-
  c=IN IP[media_ip_type] [media_ip]
  t=0 0
  m=audio [media_port] RTP/SAVP 0 101
  a=crypto:1 AES_256_CM_HMAC_SHA1_80 inline:D8j1U5jUkv/12h6jCc9eIM4e4jx8noeSyp9C/jnqshCuECQ4Xs/Ed/KLxhLsyg==
  ]]>
 </send>

 <recv request="ACK" crlf="true">
 </recv>

  <nop>
    <action>
      <exec play_pcap_audio="SecureCallTests.BasicSecureCall_Sipp.pcapng"/>
    </action>
  </nop>
 
 <recv request="BYE">
 </recv>

 <send>
  <![CDATA[
  SIP/2.0 200 OK
  [last_Via:]
  [last_From:]
  [last_To:]
  [last_Call-ID:]
  [last_CSeq:]
  Contact: <sip:[local_ip]:[local_port];transport=[transport]>
  Content-Length: 0
  ]]>
 </send>
 	
</scenario>
