CertificatePath = ./runtime_resources/SelfSignedCerts/server
CADirectory = ./runtime_resources/SelfSignedCerts/root
CertificatePath_Android = /data/user/0/com.counterpath.sdkdemo.advancedaudiocall/files/runtime_resources/SelfSignedCerts/server/
CADirectory_Android = /data/user/0/com.counterpath.sdkdemo.advancedaudiocall/files/runtime_resources/SelfSignedCerts/root/
DatabasePath_Android = /data/user/0/com.counterpath.sdkdemo.advancedaudiocall/files/runtime_resources
HttpAdminUserFile = ./runtime_resources/users.txt
HttpAdminUserFile_Android = /data/user/0/com.counterpath.sdkdemo.advancedaudiocall/files/runtime_resources/users.txt
DisableAuth = true
Domains = cp.local,127.0.0.1,autotest.cpcapi2
LogFileMaxBytes = 5242880
LogFilename = repro.log
LogLevel = NONE
ForceRecordRouting = true
Transport1Interface = 127.0.0.1:6061
Transport1RecordRouteUri = auto
Transport1TlsClientVerification = None
Transport1TlsDomain = autotest.cpcapi2
Transport1Type = TLS
Transport2Interface = 127.0.0.1:6060
Transport2RecordRouteUri = auto
Transport2Type = UDP
Transport3Interface = 127.0.0.1:6060
Transport3RecordRouteUri = auto
Transport3Type = TCP
Transport4Interface = ::1:6060
Transport4RecordRouteUri = auto
Transport4Type = UDP
Transport5Interface = ::1:6060
Transport5RecordRouteUri = auto
Transport5Type = TCP
Transport6Interface = 127.0.0.1:7061
Transport6RecordRouteUri = auto
Transport6TlsClientVerification = None
Transport6TlsDomain = autotest.cpcapi2
Transport6Type = WSS
Transport7Interface = 127.0.0.1:5071
Transport7RecordRouteUri = <sip:127.0.0.1:5071;transport=tls>
Transport7TlsClientVerification = None
Transport7TlsDomain = autotest.cpcapi2
Transport7Type = TLS
WSPort = 2114
WSSPort = 2115
SSLType = Highest
