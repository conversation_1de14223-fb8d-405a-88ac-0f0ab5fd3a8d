# defines per device max Energy usage values for this test

# <PERSON>'s iMac
max_average_energy_usage_for_device_3ff78095-e697-5568-817d-ffec14160a4c = 0

# WinBrix-QA2 (<PERSON> CI)
max_average_energy_usage_for_device_f2dc60b4-4d4e-5060-8317-be8820650198 = 0

# SDK mac jenkins
max_average_energy_usage_for_device_b9def77b-3518-5969-a536-2217ac8e2db8 = 100

# SDK mac jenkins II
# w/ http://tbswitcher.rugarciap.com/ (Turbo Boost Switcher) installed + turbo boost disabled
max_average_energy_usage_for_device_11985117-a114-5bb0-86cb-c527402a6a1a = 27

# YVR-JenSL-SDK1
max_average_energy_usage_for_device_ed6334e3-4d15-5485-ae86-33809bf7f356 = 0

# YVR-JENSL-SDK2 (M1 based Mac Mini - Jenkins slave)
max_average_energy_usage_for_device_8123db26-d602-5583-9462-c86bf4f599f1 = 36

# <PERSON> Linux Docker
max_average_energy_usage_for_device_01b307ac-ba4f-54f5-9aaf-c33bb06bbbf6 = 0

# Vineet's Mac Mini 2018 - range seen during testing 49-60
max_average_energy_usage_for_device_aecd2be4-7b6c-507e-a785-59d3cd60a1f9 = 80

# Vineet's Macbook (M1 Pro) - range seen during testing 94-110
max_average_energy_usage_for_device_45305b3d-9963-5ac9-96cb-8bf03e18f2b3 = 120
