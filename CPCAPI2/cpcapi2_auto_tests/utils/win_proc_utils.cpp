#include "win_proc_utils.h"

#ifdef WIN32

#define NTDDI_VERSION 0x06000000 // Visa and above
#include "Powersetting.h"

WinPerf::WinPerf()
{
   mSavedPowerProfile = NULL;
}

WinPerf::~WinPerf()
{
   if (mSavedPowerProfile != NULL)
   {
      restoreSavedPowerProfile();
   }
   LocalFree(mSavedPowerProfile);
}

bool WinPerf::setHighPerfPowerProfile()
{
   return PowerSetActiveScheme(0, &GUID_MIN_POWER_SAVINGS) == ERROR_SUCCESS;
}

bool WinPerf::saveCurrentPowerProfile()
{
   return PowerGetActiveScheme(0, &mSavedPowerProfile) == ERROR_SUCCESS;
}

bool WinPerf::restoreSavedPowerProfile()
{
   return PowerSetActiveScheme(0, mSavedPowerProfile) == ERROR_SUCCESS;
}

#endif // WIN32
