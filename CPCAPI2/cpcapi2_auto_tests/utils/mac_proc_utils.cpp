#include "mac_proc_utils.h"
#include "cpcapi2_test_framework.h"

#include <mach/task.h>
#include <mach/mach.h>
#include <mach/mach_traps.h>
#include <mach/mach_vm.h>
#include <sys/sysctl.h>
#include <unistd.h>

#include <numeric>
#include <future>
#include <sstream>
#include <fstream>
#include <filesystem>
#include <assert.h>


#define TIME_VALUE_TO_TIMEVAL(a, r) do {  \
  (r)->tv_sec = (a)->seconds;             \
  (r)->tv_usec = (a)->microseconds;       \
} while (0)

static const int kMicroSecondsPerSecond = 1000 * 1000;

int64_t TimeValToMicroseconds(const struct timeval& tv)
{
   return tv.tv_sec * kMicroSecondsPerSecond + tv.tv_usec;
}

bool GetTaskInfo(mach_port_t task, task_basic_info_64* task_info_data)
{
   pid_t pid = getpid();
   if (task == MACH_PORT_NULL)
   {
      safeCout("GetTaskInfo(): invalid task for pid: " << pid);
      return false;
   }

   mach_msg_type_number_t count = TASK_BASIC_INFO_64_COUNT;
   kern_return_t kr = task_info(task,
                                TASK_BASIC_INFO_64,
                                reinterpret_cast<task_info_t>(task_info_data),
                                &count);
   // Most likely cause for failure: |task| is a zombie.
   if (kr != KERN_SUCCESS)
   {
      safeCout("GetTaskInfo(): failure getting task-info for pid: " << pid << " with rc: " << kr);
      return false;
   }
   return true;
}

bool GetCPUType(cpu_type_t* cpu_type)
{
   size_t len = sizeof(*cpu_type);
   int result = sysctlbyname("sysctl.proc_cputype",
                             cpu_type,
                             &len,
                             NULL,
                             0);
   if (result != 0)
   {
      safeCout("GetCPUType(): failure getting cpu-type for pid: " << getpid() << " with rc: " << result);
      return false;
   }
   return true;
}

bool GetTimeDelta(int64_t& curProc_last_system_time, std::chrono::system_clock::time_point& curProc_last_cpu_time, int64_t& system_time_delta, int64_t& time_delta)
{
   pid_t pid = getpid();
   mach_port_t task;
   kern_return_t kern_return = task_for_pid(mach_task_self(), pid, &task);
   if (kern_return != KERN_SUCCESS )
   {
   }

   if (task == MACH_PORT_NULL)
   {
      safeCout("GetTimeDelta(): failure getting task for pid: " << pid << " with rc: " << kern_return);
      return false;
   }

   // Libtop explicitly loops over the threads (libtop_pinfo_update_cpu_usage()
   // in libtop.c), but this is more concise and gives the same results:
   task_thread_times_info thread_info_data;
   mach_msg_type_number_t thread_info_count = TASK_THREAD_TIMES_INFO_COUNT;
   kern_return_t kr = task_info(task,
                                TASK_THREAD_TIMES_INFO,
                                reinterpret_cast<task_info_t>(&thread_info_data),
                                &thread_info_count);
   if (kr != KERN_SUCCESS)
   {
      // Most likely cause: |task| is a zombie.
      safeCout("GetTimeDelta(): failure getting task-info for pid: " << pid << " with rc: " << kr);
      return false;
   }
   task_basic_info_64 task_info_data;
   if (!GetTaskInfo(task, &task_info_data))
   {
      safeCout("GetTimeDelta(): failure getting task-info for pid: " << pid);
      return false;
   }

   /* Set total_time. */
   // thread info contains live time...
   struct timeval user_timeval, system_timeval, task_timeval;
   TIME_VALUE_TO_TIMEVAL(&thread_info_data.user_time, &user_timeval);
   TIME_VALUE_TO_TIMEVAL(&thread_info_data.system_time, &system_timeval);
   timeradd(&user_timeval, &system_timeval, &task_timeval);
   // ... task info contains terminated time.
   TIME_VALUE_TO_TIMEVAL(&task_info_data.user_time, &user_timeval);
   TIME_VALUE_TO_TIMEVAL(&task_info_data.system_time, &system_timeval);
   timeradd(&user_timeval, &task_timeval, &task_timeval);
   timeradd(&system_timeval, &task_timeval, &task_timeval);

   std::chrono::system_clock::time_point time = std::chrono::system_clock::now();

   int64_t task_time = TimeValToMicroseconds(task_timeval);
   if (curProc_last_system_time == 0)
   {
      // First call, just set the last values.
      curProc_last_cpu_time = time;
      curProc_last_system_time = task_time;
      return true;
   }

   system_time_delta = task_time - curProc_last_system_time;
   time_delta = std::chrono::duration_cast<std::chrono::microseconds>(time - curProc_last_cpu_time).count();

   assert(0U != time_delta);
   if (time_delta == 0)
   {
      safeCout("GetTimeDelta(): invalid value for time_delta: " << time_delta << " for pid: " << pid);
      return false;
   }
   curProc_last_cpu_time = time;
   curProc_last_system_time = task_time;
   return true;
}

bool GetPowerInfo(task_power_info_v2* task_power_data)
{
   pid_t pid = getpid();
   mach_port_t task;
   kern_return_t kern_return = task_for_pid(mach_task_self(), pid, &task);
   if (kern_return != KERN_SUCCESS)
   {
   }

   if (task == MACH_PORT_NULL)
   {
      safeCout("GetPowerInfo(): failure getting task for pid: " << pid << " with rc: " << kern_return);
      return false;
   }

   mach_msg_type_number_t count = TASK_POWER_INFO_V2_COUNT;
   kern_return_t kr = task_info(task,
                                TASK_POWER_INFO_V2,
                                reinterpret_cast<task_info_t>(task_power_data),
                                &count);
   // Most likely cause for failure: |task| is a zombie.
   if (kr != KERN_SUCCESS)
   {
      safeCout("GetPowerInfo(): failure getting task-info for pid: " << pid << " with rc: " << kr);
      return false;
   }

   return true;
}

MacCpuUsageMonitoring::MacCpuUsageMonitoring() :
curProc_last_system_time_(0),
allProc_previousTotalTicks_(0),
allProc_previousIdleTicks_(0)
{
}

// reworked and borrowed from https://chromium.googlesource.com/chromium/src/+/master/base/process/process_metrics_mac.cc

double MacCpuUsageMonitoring::getCurrentProcessCPUUsage()
{
   pid_t pid = getpid();
   int64_t system_time_delta = 0;
   int64_t time_delta = 0;

   if (!GetTimeDelta(curProc_last_system_time_, curProc_last_cpu_time_, system_time_delta, time_delta))
   {
      safeCout("MacEnergyUsageMonitoring::getCurrentProcessCPUUsage(): error getting time-delta for pid: " << pid);
      return -1;
   }

   if (time_delta <= 0)
   {
      safeCout("MacEnergyUsageMonitoring::getCurrentProcessCPUUsage(): invalid time-delta value: " << time_delta << " for pid: " << pid);
      return -1;
   }

   return static_cast<double>(system_time_delta * 100.0) / time_delta;
}

double MacCpuUsageMonitoring::getAllProcessesCPUUsage()
{
   double allProcCpuUsage = -1;

   host_cpu_load_info_data_t cpuinfo;
   mach_msg_type_number_t count = HOST_CPU_LOAD_INFO_COUNT;
   if (host_statistics(mach_host_self(), HOST_CPU_LOAD_INFO, (host_info_t)&cpuinfo, &count) == KERN_SUCCESS)
   {
      double totalTicks = 0;
      for(int i=0; i<CPU_STATE_MAX; i++)
      {
         totalTicks += cpuinfo.cpu_ticks[i];
      }

      double totalTicksSinceLastTime = totalTicks - allProc_previousTotalTicks_;
      double idleTicksSinceLastTime  = cpuinfo.cpu_ticks[CPU_STATE_IDLE] - allProc_previousIdleTicks_;
      double ret = 1.0f-((totalTicksSinceLastTime > 0) ? ((double)idleTicksSinceLastTime)/totalTicksSinceLastTime : 0);
      allProc_previousTotalTicks_ = totalTicks;
      allProc_previousIdleTicks_  = cpuinfo.cpu_ticks[CPU_STATE_IDLE];
      allProcCpuUsage = ret*100;
   }

   return allProcCpuUsage;
}

MacEnergyUsageMonitoring::MacEnergyUsageMonitoring() :
_started(false),
_parsed(false),
_filename(""),
_scriptname(""),
_lastSystemTime(0),
_lastIdleSystemTime(0),
_lastPackageIdleSystemTime(0),
_lastAbsoluteIdleWakeups(0),
_lastAbsolutePackageIdleWakeups(0)
{
}

MacEnergyUsageMonitoring::~MacEnergyUsageMonitoring()
{
   reset();
}

uint64_t MacEnergyUsageMonitoring::getCurrentProcessUserEnergyUsage()
{
   task_power_info_v2 task_power_data;
   if (!GetPowerInfo(&task_power_data))
   {
      safeCout("MacEnergyUsageMonitoring::getCurrentProcessUserEnergyUsage(): error getting power-info for pid: " << getpid());
      return 0;
   }

   uint64_t userCpuPower = task_power_data.cpu_energy.total_user;
   return userCpuPower;
}

uint64_t MacEnergyUsageMonitoring::getCurrentProcessSystemEnergyUsage()
{
   task_power_info_v2 task_power_data;
   if (!GetPowerInfo(&task_power_data))
   {
      safeCout("MacEnergyUsageMonitoring::getCurrentProcessSystemEnergyUsage(): error getting power-info for pid: " << getpid());
      return 0;
   }

   uint64_t systemCpuPower = task_power_data.cpu_energy.total_system;
   return systemCpuPower;
}

uint64_t MacEnergyUsageMonitoring::getCurrentProcessGpuEnergyUsage()
{
   task_power_info_v2 task_power_data;
   if (!GetPowerInfo(&task_power_data))
   {
      safeCout("MacEnergyUsageMonitoring::getCurrentProcessGpuEnergyUsage(): error getting power-info for pid: " << getpid());
      return 0;
   }

   uint64_t gpuPower = task_power_data.gpu_energy.task_gpu_utilisation;
   return gpuPower;
}

uint64_t MacEnergyUsageMonitoring::getCurrentProcessTaskEnergyUsage()
{
   task_power_info_v2 task_power_data;
   if (!GetPowerInfo(&task_power_data))
   {
      safeCout("MacEnergyUsageMonitoring::getCurrentProcessTaskEnergyUsage(): error getting power-info for pid: " << getpid());
      return 0;
   }

   uint64_t taskEnergy = 0;
#if defined(__arm__) || defined(__arm64__)
   taskEnergy = task_power_data.task_energy;
#endif /* defined(__arm__) || defined(__arm64__) */

   return taskEnergy;
}

int MacEnergyUsageMonitoring::getPackageIdleWakeupsPerSecond()
{
   task_power_info_v2 task_power_data;
   if (!GetPowerInfo(&task_power_data))
   {
      safeCout("MacEnergyUsageMonitoring::getPackageIdleWakeupsPerSecond(): error getting power-info for pid: " << getpid());
      return -1;
   }

   return calculatePackageIdleWakeupsPerSecond(task_power_data.cpu_energy.task_platform_idle_wakeups);
}

int MacEnergyUsageMonitoring::getIdleWakeupsPerSecond()
{
   task_power_info_v2 task_power_data;
   if (!GetPowerInfo(&task_power_data))
   {
      safeCout("MacEnergyUsageMonitoring::getIdleWakeupsPerSecond(): error getting power-info for pid: " << getpid());
      return -1;
   }

   return calculateIdleWakeupsPerSecond(task_power_data.cpu_energy.task_interrupt_wakeups);
}

void MacEnergyUsageMonitoring::reset()
{
   if (_started)
   {
      stop();
   }

   EnergyData filler;
   _energyData = filler;
   _parsed = false;
   _started = false;

   if (!_filename.empty())
   {
      std::remove(_filename.c_str());
      _filename.erase();
   }

   if (!_scriptname.empty())
   {
      std::remove(_scriptname.c_str());
      _scriptname.erase();
   }
}

void MacEnergyUsageMonitoring::start(uint32_t maxRunDurationSecs)
{
   if (_started)
   {
      safeCout("MacEnergyUsageMonitoring::start(): monitoring already in progress");
      return;
   }

   reset();

   _started = true;
   _energyData.startTimeMsecs = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
   pid_t pid = getpid();
   std::stringstream fileName;
   fileName << "top_" << pid << "_" << _energyData.startTimeMsecs;
   _filename = fileName.str() + ".out";
   std::stringstream topCmd;
   topCmd << "top -pid " << pid << " -stats pid,cpu,idlew,power,command -c a -s 1 -l " << maxRunDurationSecs << " | grep --line-buffered " << pid << " | grep --line-buffered -i cpcapi > " << _filename;
   std::string topCmdStr(topCmd.str().c_str());
   // safeCout("MacEnergyUsageMonitoring::start(): top command: " << topCmdStr.c_str());

   _scriptname = fileName.str() + ".sh";
   std::fstream fs;
   fs.open(_scriptname.c_str(), std::fstream::out);
   if (fs.is_open())
   {
      fs << "#!/bin/bash" << std::endl;
      fs << topCmdStr << std::endl;
   }
   fs.close();
   std::filesystem::permissions(_scriptname.c_str(), std::filesystem::perms::all);
   std::stringstream scriptCmd;
   scriptCmd << "./" << _scriptname << " &";
   std::string scriptCmdStr = scriptCmd.str();
   std::system(scriptCmdStr.c_str());
}

void MacEnergyUsageMonitoring::stop()
{
   if (!_started || _filename.empty() || _scriptname.empty())
   {
      safeCout("MacEnergyUsageMonitoring::stop(): exiting stop request: started: " << _started << " filename-initialized: " << !_filename.empty() << " scriptname-initialized: " << !_scriptname.empty());
      return;
   }

   _started = false;
   pid_t pid = getpid();
   std::stringstream fileName;
   fileName << "pid_" << _filename;
   std::string filename = fileName.str();
   std::stringstream psCmd;
   psCmd << "ps -e -o pid,ppid,command | grep --line-buffered " << _scriptname << " > " << filename;
   std::string psCmdStr(psCmd.str().c_str());
   // safeCout("MacEnergyUsageMonitoring::stop(): ps command to query monitoring pid: " << psCmdStr.c_str());
   std::system(psCmdStr.c_str());

   pid_t toppid = 0;
   pid_t ppid = 0;
   std::string cmd("");
   std::string param("");
   std::fstream fs;
   bool found = false;
   fs.open(filename.c_str(), std::fstream::in);
   if (fs.is_open())
   {
      std::string buff("");
      while (std::getline(fs, buff) && !found)
      {
         std::stringstream sbuff;
         sbuff << buff;
         sbuff >> toppid >> ppid >> cmd >> param;
         if ((cmd.compare("/bin/bash") == 0) && (param.compare("./" + _scriptname) == 0))
         {
            found = true;
            break;
         }
      }
   }
   fs.close();
   int rc = std::remove(filename.c_str());
   if (rc != 0)
   {
      safeCout("MacEnergyUsageMonitoring::stop(): error deleting file: " << filename.c_str() << " rc: " << rc);
   }

   if (!found || (toppid <= 0))
   {
      safeCout("MacEnergyUsageMonitoring::stop(): exiting as top pid could not be extracted: found: " << found << " toppid: " << toppid << " ppid: " << ppid);
      return;
   }

   std::stringstream killCmd;
   killCmd << "pkill -P " << toppid;
   std::system(killCmd.str().c_str());

   _energyData.stopTimeMsecs = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
}

void MacEnergyUsageMonitoring::getTaskEnergyFromTopInfo(EnergyData& energyData)
{
   if (_started || _filename.empty() || _scriptname.empty())
   {
      safeCout("MacEnergyUsageMonitoring::getTaskEnergyFromTopInfo(): exiting request: top-completed: " << !_started << " filename-initialized: " << !_filename.empty() << " scriptname-initialized: " << !_scriptname.empty());
      return;
   }

   if (_parsed)
   {
      energyData = _energyData;
      return;
   }

   pid_t pid = getpid();

   std::fstream fs;
   fs.open(_filename.c_str(), std::fstream::in);
   if (fs.is_open())
   {
      std::string buff("");
      std::string lastLine("");
      std::string previousLine("");
      while (std::getline(fs, buff))
      {
         previousLine = lastLine;
         lastLine = buff;
      }

      // Let's verify if all is good, in case top was still updating file when process is killed, will need to ignore the last-line
      std::vector<std::string> tokens;
      std::size_t pos = 0;
      std::size_t pos2 = 0;
      bool validated(false);

      while (true)
      {
         pos2 = lastLine.find_first_of("0123456789", pos);
         if (pos2 == std::string::npos) break;
         pos = lastLine.find_first_of(" ", pos2);
         if (pos == std::string::npos) break;
         tokens.push_back(lastLine.substr(pos2, pos - pos2));
         if (tokens.size() == 4)
         {
            validated = true;
            break;
         }
      }

      std::stringstream ss;
      if (validated)
      {
         ss << lastLine;
      }
      else
      {
         ss << previousLine;
      }
      std::string command("");
      ss >> pid >> energyData.cpuUsage >> energyData.idleWakeups >> energyData.energyUsage >> command;
      energyData.startTimeMsecs = _energyData.startTimeMsecs;
      energyData.stopTimeMsecs = _energyData.stopTimeMsecs;
      _energyData = energyData;
      _parsed = true;
   }
   fs.close();

   if (!_parsed)
   {
      safeCout("MacEnergyUsageMonitoring::getTaskEnergyFromTopInfo(): error parsing file: " << _filename.c_str());
   }

   int rc = std::remove(_filename.c_str());
   if (rc != 0)
   {
      safeCout("MacEnergyUsageMonitoring::getTaskEnergyFromTopInfo(): error deleting file: " << _filename.c_str() << " rc: " << rc);
   }
   _filename.erase();
   rc = std::remove(_scriptname.c_str());
   if (rc != 0)
   {
      safeCout("MacEnergyUsageMonitoring::getTaskEnergyFromTopInfo(): error deleting file: " << _scriptname.c_str() << " rc: " << rc);
   }
   _scriptname.erase();
}

void MacEnergyUsageMonitoring::getTaskEnergyFromTopInfoBlocking(int sampleIntervalSecs, int sampleCount, EnergyData& energyData)
{
   if (_started || !_filename.empty() || !_scriptname.empty())
   {
      safeCout("MacEnergyUsageMonitoring::getTaskEnergyFromTopInfoBlocking(): exiting request as async top monitoring request in progress: started: " << _started << " filename-initialized: " << !_filename.empty() << " scriptname-initialized: " << !_scriptname.empty());
      return;
   }

   pid_t pid = getpid();
   energyData.startTimeMsecs = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
   std::stringstream fileName;
   fileName << "top_" << pid << "_" << energyData.startTimeMsecs << ".out";
   std::string filename = fileName.str();
   std::stringstream topCmd;
   topCmd << "top -pid " << pid << " -stats pid,cpu,idlew,power,command -c a -s " << sampleIntervalSecs << " -l " << sampleCount << " | grep --line-buffered " << pid << " | grep --line-buffered -i cpcapi > " << filename;
   // safeCout("MacEnergyUsageMonitoring::getTaskEnergyFromTopInfoBlocking(): top command: " << topCmd.str().c_str());
   std::system(topCmd.str().c_str());
   energyData.stopTimeMsecs = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count();

   std::string spid("");
   std::fstream fs;
   fs.open(fileName.str().c_str(), std::fstream::in);
   if (fs.is_open())
   {
      std::string buff("");
      std::string lastLine("");
      while (std::getline(fs, buff))
      {
         lastLine = buff;
      }

      std::stringstream ss;
      ss << lastLine;
      std::string command("");
      ss >> spid >> energyData.cpuUsage >> energyData.idleWakeups >> energyData.energyUsage >> command;
      // safeCout("MacEnergyUsageMonitoring::getTaskEnergyFromTopInfoBlocking(): energy-data: pid: " << spid << " cpu-usage: " << energyData.cpuUsage << " idle-wakeups: " << energyData.idleWakeups << " energy-usage: " << energyData.energyUsage);
   }
   fs.close();
   int rc = std::remove(filename.c_str());
   if (rc != 0)
   {
      safeCout("MacEnergyUsageMonitoring::getTaskEnergyFromTopInfoBlocking(): error deleting file: " << filename.c_str() << " rc: " << rc);
   }
}

int MacEnergyUsageMonitoring::calculateEventsPerSecond(int64_t eventCount, int64_t& lastEventCount, int64_t& lastSystemTime, std::chrono::system_clock::time_point& lastCalculated)
{
   int64_t system_time_delta = 0;
   int64_t time_delta = 0;
   pid_t pid = getpid();

   if (!GetTimeDelta(lastSystemTime, lastCalculated, system_time_delta, time_delta)) // curProc_last_system_time_, curProc_last_cpu_time_, system_time_delta, time_delta))
   {
      safeCout("MacEnergyUsageMonitoring::calculateEventsPerSecond(): error getting time-delta for pid: " << pid);
      return -1;
   }

   if (time_delta <= 0)
   {
      safeCout("MacEnergyUsageMonitoring::calculateEventsPerSecond(): invalid time-delta value: " << time_delta << " for pid: " << pid);
      return -1;
   }

   int events_per_second = 0;
   if (lastEventCount != 0)
   {
      const int64_t events_delta = eventCount - lastEventCount;
      int64_t time_delta_secs = time_delta / 1000000; // From micro to secs
      if (time_delta_secs > 0)
      {
         events_per_second = (events_delta / time_delta_secs);
      }
   }
   // last_calculated = curProc_last_cpu_time_;
   lastEventCount = eventCount;
   return events_per_second;
}

int MacEnergyUsageMonitoring::calculateIdleWakeupsPerSecond(int64_t absoluteIdleWakeups)
{
   return calculateEventsPerSecond(absoluteIdleWakeups, _lastAbsoluteIdleWakeups, _lastIdleSystemTime, _lastIdleWakeupTime);
}

int MacEnergyUsageMonitoring::calculatePackageIdleWakeupsPerSecond(int64_t absolutePackageIdleWakeups)
{
   return calculateEventsPerSecond(absolutePackageIdleWakeups, _lastAbsolutePackageIdleWakeups, _lastPackageIdleSystemTime, _lastPackageIdleWakeupTime);
}

/*
// TODO: dependancy on libpmenergy.dylib
double MacEnergyUsageMonitoring::getEnergyImpactInternal(mach_port_t task, uint64_t mach_time)
{
   OpaquePMTaskEnergyData energy_info{};
   if (pm_sample_task(task, &energy_info, mach_time, kPMSampleFlags) != 0)
     return 0.0;
   return pm_energy_impact(&energy_info);
}

int MacEnergyUsageMonitoring::getEnergyImpact()
{
   uint64_t now = mach_absolute_time();
   if (last_energy_impact_ == 0)
   {
     last_energy_impact_ = getEnergyImpactInternal(TaskForPid(process_), now);
     last_energy_impact_time_ = now;
     return 0;
   }
   double total_energy_impact = getEnergyImpactInternal(TaskForPid(process_), now);
   uint64_t delta = now - last_energy_impact_time_;
   if (delta == 0)
     return 0;
   // Scale by 100 since the histogram is integral.
   double seconds_since_last_measurement = base::TimeTicks::FromMachAbsoluteTime(delta).since_origin().InSecondsF();
   int energy_impact = 100 * (total_energy_impact - last_energy_impact_) / seconds_since_last_measurement;
   last_energy_impact_ = total_energy_impact;
   last_energy_impact_time_ = now;
   return energy_impact;
}
*/

std::string MacSystemInfo::cpuBrandString()
{
   char buffer[1024];
   memset(buffer, 0, sizeof(buffer));
   size_t size = sizeof(buffer);
   if (sysctlbyname("machdep.cpu.brand_string", &buffer, &size, NULL, 0) < 0)
   {
       perror("sysctl");
   }
   return buffer;
}
