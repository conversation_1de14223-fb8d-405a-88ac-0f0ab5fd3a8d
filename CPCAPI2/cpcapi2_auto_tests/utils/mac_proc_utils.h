#pragma once

#include <sys/types.h>
#include <chrono>
#include <string>

// Note: getCurrentProcessCPUUsage() is designed to be called repeatedly at regular intervals.
// Create a new instance of MacCpuUsageMonitoring each time you'd like a reading from scratch to avoid
// taking into consideration CPU usage from a previous call
class MacCpuUsageMonitoring
{
public:
   MacCpuUsageMonitoring();
   double getCurrentProcessCPUUsage();
   double getAllProcessesCPUUsage();

private:
   int64_t curProc_last_system_time_;
   std::chrono::system_clock::time_point curProc_last_cpu_time_;

   double allProc_previousTotalTicks_ = 0;
   double allProc_previousIdleTicks_ = 0;
};

class MacEnergyUsageMonitoring
{
public:
   MacEnergyUsageMonitoring();
   virtual~ MacEnergyUsageMonitoring();

   struct EnergyData
   {
      double cpuUsage;
      double energyUsage;
      std::string idleWakeups;
      uint64_t startTimeMsecs;
      uint64_t stopTimeMsecs;
      EnergyData() : cpuUsage(0), energyUsage(0), idleWakeups(""), startTimeMsecs(0), stopTimeMsecs(0) {}
   };

   /**
    * @maxRunDurationSecs Specify the max run duration, at which point the monitoring will stop even if the stop function has not been called.
   */
   void start(uint32_t maxRunDurationSecs = 600);
   void stop();
   void reset();

   /**
    * Extracting task energy data requires that top had been initiated and terminated, so that the
    * cumulative data can be extracted, i.e. call start and stop before calling this function.
    * Will not populate the data if top is still running.
    *
    * Will delete the top output files onces the data has been parsed.
   */
   void getTaskEnergyFromTopInfo(EnergyData& energyData);
   void getTaskEnergyFromTopInfoBlocking(int sampleIntervalSecs, int sampleCount, EnergyData& energyData);

   // Following functions to extract the raw task power attributes
   uint64_t getCurrentProcessUserEnergyUsage();
   uint64_t getCurrentProcessSystemEnergyUsage();
   uint64_t getCurrentProcessGpuEnergyUsage();
   uint64_t getCurrentProcessTaskEnergyUsage();
   int getPackageIdleWakeupsPerSecond();
   int getIdleWakeupsPerSecond();

private:

   int calculateEventsPerSecond(int64_t eventCount, int64_t& lastEventCount, int64_t& lastSystemTime, std::chrono::system_clock::time_point& lastCalculated);
   int calculateIdleWakeupsPerSecond(int64_t absoluteIdleWakeups);
   int calculatePackageIdleWakeupsPerSecond(int64_t absolutePackageIdleWakeups);

   // Following variables are for the async top energy monitoring
   bool _started;
   bool _parsed;
   std::string _filename;
   std::string _scriptname;
   EnergyData _energyData;

   // Following variables are for the raw task power attributes
   int64_t _lastSystemTime;
   int64_t _lastIdleSystemTime;
   int64_t _lastPackageIdleSystemTime;

   std::chrono::system_clock::time_point _lastCpuTime;
   std::chrono::system_clock::time_point _lastIdleWakeupTime;
   std::chrono::system_clock::time_point _lastPackageIdleWakeupTime;

   int64_t _lastAbsoluteIdleWakeups;
   int64_t _lastAbsolutePackageIdleWakeups;
};

class MacSystemInfo
{
public:
   static std::string cpuBrandString();

};
