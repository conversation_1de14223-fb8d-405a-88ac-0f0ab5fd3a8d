#if _WIN32
#include "stdafx.h"
#else
#endif

#include "script_runner.h"
#include <cpcapi2utils.h>

#include <thread>
#include <future>
#include <fstream>
#include <stdio.h>
#include <stdlib.h>



using namespace std;
using namespace CPCAPI2;

script_runner::script_runner()
{
}


script_runner::~script_runner()
{
}

string ExePath() {
   
   string filePath;
   //Add ifdef win32 in each method
#ifdef _WIN32
   char buffer[MAX_PATH];
   GetModuleFileName(NULL, buffer, MAX_PATH);
   string::size_type pos = string(buffer).rfind("cpcapi2_auto_tests.exe");
   filePath = string(buffer).substr(0, pos);
   return filePath;
#else
   // Add default code here
#endif
   return filePath;
}

string getUnboundFolderPath()
{
   string currentDirectory = ExePath();
   string maradnsFolderPath = currentDirectory + "unbound\\win";
   return maradnsFolderPath;
}

string getMaradnsFolderPath()
{
   string currentDirectory = ExePath();
   string maradnsFolderPath = currentDirectory + "maradns-win";
   return maradnsFolderPath;
}


string getEjabberdScriptPath()
{
   string currentDirectory = ExePath();
   string ejabberdFolderPath = currentDirectory + "ejabberd\\bin";
   string ejabberdFilePath = currentDirectory + "ejabberd\\bin\\serverManager.cmd";
   return ejabberdFilePath;
}

void script_runner::runPrerequisiteScripts()
{
   string ejabberdScript = getEjabberdScriptPath();
   
#ifdef _WIN32
   m_hjob = CreateJobObject(nullptr, nullptr);

   JOBOBJECT_EXTENDED_LIMIT_INFORMATION info = {};
   info.BasicLimitInformation.LimitFlags = JOB_OBJECT_LIMIT_KILL_ON_JOB_CLOSE;
   SetInformationJobObject(m_hjob, JobObjectExtendedLimitInformation,
      &info, sizeof(info));

   LPSTR preRequisiteEjabberdScript = const_cast<char *>(ejabberdScript.c_str());
   
   //STARTUPINFOA siEjabberd;
   //PROCESS_INFORMATION piEjabberd;
   //ZeroMemory(&siEjabberd, sizeof(siEjabberd));
   //siEjabberd.cb = sizeof(siEjabberd);
   //ZeroMemory(&piEjabberd, sizeof(piEjabberd));
   //
   //// Start ejabberd in the child process
   //if (!CreateProcessA(NULL,     // No module name (use command line)
   //                    preRequisiteEjabberdScript, // Command line
   //                    NULL,           // Process handle not inheritable
   //                    NULL,           // Thread handle not inheritable
   //                    FALSE,          // Set handle inheritance to FALSE
   //                    0,              // No creation flags
   //                    NULL,           // Use parent's environment block
   //                    NULL,           // Use parent's starting directory
   //                    &siEjabberd,            // Pointer to STARTUPINFO structure
   //                    &piEjabberd)           // Pointer to PROCESS_INFORMATION structure
   //    )
   //{
   //   std::cout << "CreateProcess failed (" << GetLastError() << ")." << std::endl;
   //}
   
   //STARTUPINFOA siMaradns;
   //PROCESS_INFORMATION piMaradns;
   //ZeroMemory(&siMaradns, sizeof(siMaradns));
   //siMaradns.cb = sizeof(siMaradns);
   //ZeroMemory(&piMaradns, sizeof(piMaradns));

   // Start maradns in child process
   //_chdir(getMaradnsFolderPath().c_str());
   //if (!CreateProcessA(NULL,     // No module name (use command line)
   //                    "cmd.exe /C run_maradns.bat", // Command line
   //                    NULL,           // Process handle not inheritable
   //                    NULL,           // Thread handle not inheritable
   //                    FALSE,          // Set handle inheritance to FALSE
   //                    0,              // No creation flags
   //                    NULL,           // Use parent's environment block
   //                    NULL,           // Use parent's starting directory 
   //                    &siMaradns,            // Pointer to STARTUPINFO structure
   //                    &piMaradns)           // Pointer to PROCESS_INFORMATION structure
   //    )
   //{
   //   std::cout << "CreateProcess failed (" << GetLastError() << ")." << std::endl;
   //}

   STARTUPINFOA siUnbound;
   PROCESS_INFORMATION piUnbound;
   ZeroMemory(&siUnbound, sizeof(siUnbound));
   siUnbound.cb = sizeof(siUnbound);
   ZeroMemory(&piUnbound, sizeof(piUnbound));

   char workingDir[MAX_PATH];
   GetCurrentDirectoryA(MAX_PATH, workingDir);
   workingDir[MAX_PATH-1] = 0;

   // Start unbound in child process
   _chdir(getUnboundFolderPath().c_str());
   if (!CreateProcessA(NULL,     // No module name (use command line)
      "cmd.exe /C run_unbound.bat", // Command line
      NULL,           // Process handle not inheritable
      NULL,           // Thread handle not inheritable
      FALSE,          // Set handle inheritance to FALSE
      0,              // No creation flags
      NULL,           // Use parent's environment block
      NULL,           // Use parent's starting directory 
      &siUnbound,            // Pointer to STARTUPINFO structure
      &piUnbound)           // Pointer to PROCESS_INFORMATION structure
      )
   {
      std::cout << "CreateProcess failed (" << GetLastError() << ")." << std::endl;
   }
   else
   {
      if (!AssignProcessToJobObject(m_hjob, piUnbound.hProcess))
         std::cout << "AssignProcessToJobObject failed (" << GetLastError() << ")." << std::endl;
      CloseHandle(piUnbound.hProcess);
      CloseHandle(piUnbound.hThread);
   }
   
   // restore working dir
   _chdir(workingDir);
#else
   //Default behaviour for run prequisite scripts
#endif
   
   
   
   
}

void script_runner::stopPrerequisiteScripts()
{
#ifdef _WIN32
   CloseHandle(m_hjob);
#else
   // Add default code here
#endif
}
