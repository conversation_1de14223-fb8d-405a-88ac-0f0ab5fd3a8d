#ifdef ANDROID
#include <android/log.h>
#include <stdio.h>
#include <unistd.h>
#include <pthread.h>
#include "stdoutToLogcatRedirector.h"

using namespace CPCAPI2;

int StdoutToLogcatRedirector::fd[2];
const char* StdoutToLogcatRedirector::LOGOUTPUT = "CPCAPI2_AUTOTESTS";

StdoutToLogcatRedirector::StdoutToLogcatRedirector()
{
    runLoggingThread();
}

StdoutToLogcatRedirector::~StdoutToLogcatRedirector()
{
}

void* StdoutToLogcatRedirector::loggingFunc(void*)
{
    ssize_t readSize;
    char buf[1024];

    while ((readSize = read(fd[0], buf, sizeof buf-1)) > 0)
    {
        if (buf[readSize-1] == '\n')
        {
            readSize--;
        }
        buf[readSize] = 0;
        __android_log_write(ANDROID_LOG_DEBUG, LOGOUTPUT, buf);
    }
    
    return NULL;
}

void StdoutToLogcatRedirector::runLoggingThread()
{
    setvbuf(stdout, 0, _IOLBF, 0);
    setvbuf(stderr, 0, _IONBF, 0);

    pipe(fd);
    dup2(fd[1], 1);
    dup2(fd[1], 2);

    if (pthread_create(&loggingThread, 0, &loggingFunc, 0) == -1)
    {
        __android_log_print(ANDROID_LOG_ERROR, LOGOUTPUT, "========>>> The pthread_create() for logging failed! <<<========");
    }
    if ((errNum = pthread_detach(loggingThread)) != 0)
    {
        __android_log_print(ANDROID_LOG_ERROR, LOGOUTPUT, "========>>> The pthread_detach() for logging failed with error code %d <<<========", errNum);
    }
}

#endif
