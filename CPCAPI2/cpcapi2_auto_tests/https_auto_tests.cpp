#include "cpcapi2_test_fixture.h"
#include "test_account_events.h"
#include "test_events.h"
#include "test_call_events.h"


#include <sstream>

#include "../../impl/util/CurlPPHelper.h"
#include "../../impl/util/CurlPPProgress.h"
#include "../../impl/util/CurlPPSSL.h"
#include "../../impl/util/HttpClient.h"

using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace std::chrono;

#define RESIPROCATE_SUBSYSTEM resip::Subsystem::NONE

namespace
{


   class HttpsTests : public CpcapiAutoTest
   {
   public:
      HttpsTests()
      {
      }

      void SetUp() override
      {
      }

      virtual ~HttpsTests() {}
   };

void CurlPPGetExpectError(std::string url, int curlError, SslCipherOptions tlsSettings)
{
   CurlPPHelper helper;
   curlpp::Easy request;
   CurlPPSSL cssl(tlsSettings, 0);

   request.setOpt(new curlpp::options::SslCtxFunction(cssl));
   helper.setDefaultOptions(request, url, "GET", 0);

   try
   {
      request.perform();
      FAIL() << "curlpp should have thrown an exception";
   }
   catch (curlpp::LibcurlRuntimeError ex)
   {
      EXPECT_EQ(curlError, ex.whatCode());
   }
   catch (...)
   {
      FAIL() << "Unexpected exception";
   }
}

void CurlPPGetExpectError(std::string url, int curlError)
{
	CurlPPGetExpectError(url, curlError, SslCipherOptions());
}

void CurlPPGetExpectSuccess(std::string url, SslCipherOptions tlsSettings, bool ignoreCertErrors = false)
{
   CurlPPHelper helper;
   curlpp::Easy request;

   int acceptableFailures = 0;
   if (ignoreCertErrors)
   {
      acceptableFailures = CurlPPSSL::E_CERT_WHATEVER_ERROR;
   }

   CurlPPSSL cssl(tlsSettings, acceptableFailures);


   request.setOpt(new curlpp::options::SslCtxFunction(cssl));
   helper.setDefaultOptions(request, url, "GET", 0);

   // this doesn't seem to work?
   //if (tlsCertMismatchAllowed)
   //{
   //  helper.setCertMismatchOptions(request, tlsCertMismatchAllowed);
   //}

   try
   {
      request.perform();
   }
   catch (curlpp::LibcurlRuntimeError ex)
   {
      FAIL() << "curlpp exception. Error code: " << ex.whatCode();
   }
   catch (...)
   {
      FAIL() << "Unexpected exception";
   }
}

void CurlPPGetExpectSuccess(std::string url)
{
	CurlPPGetExpectSuccess(url, SslCipherOptions());
}

HTTPClient::ResponseResult HttpClientGet(std::string url, SslCipherOptions tlsSettings, bool ignoreCertErrors = false)
{
   HTTPClient httpClient;
   HTTPClient::RequestConfig requestConfig;
   HTTPClient::ResponseResult responseResult;

   requestConfig.verb = HTTPClient::EHTTPVerbGET;

   requestConfig.tlsVersion = tlsSettings.getTLSVersion(SslCipherUsageHttp);
   requestConfig.cipherSuite = tlsSettings.getCiphers(SslCipherUsageHttp);
   requestConfig.ignoreCertErrors = ignoreCertErrors;

   httpClient.HTTPSendMessage(url.c_str(), requestConfig, responseResult);

   return responseResult;
}

HTTPClient::ResponseResult HttpClientGet(std::string url)
{
	return HttpClientGet(url, SslCipherOptions());
}

TEST_F(HttpsTests, Expired)
{
   std::string url = "https://expired.badssl.com";
   int curlError = CURLE_SSL_CACERT;

   CurlPPGetExpectError(url, curlError);

   HTTPClient::ResponseResult responseResult = HttpClientGet(url);
   ASSERT_EQ(responseResult.curlErrorCode, curlError);
}

TEST_F(HttpsTests, WrongHost)
{
   std::string url = "https://wrong.host.badssl.com";
   int curlError = CURLE_PEER_FAILED_VERIFICATION;

   CurlPPGetExpectError(url, curlError);

   HTTPClient::ResponseResult responseResult = HttpClientGet(url);
   ASSERT_EQ(responseResult.curlErrorCode, curlError);
}

TEST_F(HttpsTests, SelfSigned)
{
   std::string url = "https://self-signed.badssl.com";
   int curlError = CURLE_SSL_CACERT;

   CurlPPGetExpectError(url, curlError);

   HTTPClient::ResponseResult responseResult = HttpClientGet(url);
   ASSERT_EQ(responseResult.curlErrorCode, curlError);
}


#ifndef ANDROID // OBELISK-5683
TEST_F(HttpsTests, SelfSigned_IgnoreCertVerify)
{
   std::string url = "https://self-signed.badssl.com/";
   
   bool ignoreCertErrors = true;
   
   CurlPPGetExpectSuccess(url, SslCipherOptions(), ignoreCertErrors);

   HTTPClient::ResponseResult responseResult = HttpClientGet(url, SslCipherOptions(), ignoreCertErrors);
   ASSERT_EQ(responseResult.status, 200);
}
#endif // #ifndef ANDROID

TEST_F(HttpsTests, UntrustedRoot)
{
   std::string url = "https://untrusted-root.badssl.com";
   int curlError = CURLE_SSL_CACERT;

   CurlPPGetExpectError(url, curlError);

   HTTPClient::ResponseResult responseResult = HttpClientGet(url);
   ASSERT_EQ(responseResult.curlErrorCode, curlError);
}

TEST_F(HttpsTests, DISABLED_Revoked)
{
   std::string url = "https://revoked.badssl.com";
   int curlError = CURLE_SSL_CACERT;

   CurlPPGetExpectError(url, curlError);

   HTTPClient::ResponseResult responseResult = HttpClientGet(url);
   ASSERT_EQ(responseResult.curlErrorCode, curlError);
}

// Cert Expired
TEST_F(HttpsTests, DISABLED_NoCommonName)
{
   std::string url = "https://no-common-name.badssl.com";

   CurlPPGetExpectSuccess(url);

   HTTPClient::ResponseResult responseResult = HttpClientGet(url);
   ASSERT_EQ(responseResult.status, 200);
}

TEST_F(HttpsTests, DISABLED_NoSubject)
{
   std::string url = "https://no-subject.badssl.com";

   CurlPPGetExpectSuccess(url);

   HTTPClient::ResponseResult responseResult = HttpClientGet(url);
   ASSERT_EQ(responseResult.status, 200);
}

// Incomplete certificate is not a direct threat. It is colored gray by badssl.com
// CUrrently fails though it would be acceptable if it passed
TEST_F(HttpsTests, DISABLED_IncompleteChain)
{
   std::string url = "https://incomplete-chain.badssl.com";

   CurlPPGetExpectSuccess(url);

   HTTPClient::ResponseResult responseResult = HttpClientGet(url);
   ASSERT_EQ(responseResult.status, 200);
}

TEST_F(HttpsTests, DISABLED_Sha1Intermediate)
{
   std::string url = "https://sha1-intermediate.badssl.com";
   int curlError = CURLE_SSL_CACERT;

   CurlPPGetExpectError(url, curlError);

   HTTPClient::ResponseResult responseResult = HttpClientGet(url);
   ASSERT_EQ(responseResult.curlErrorCode, curlError);
}

TEST_F(HttpsTests, Sha256)
{
   std::string url = "https://sha256.badssl.com";

   CurlPPGetExpectSuccess(url);

   HTTPClient::ResponseResult responseResult = HttpClientGet(url);
   ASSERT_EQ(responseResult.status, 200);
}

// disabled until cert is renewed
TEST_F(HttpsTests, DISABLED_Sha384)
{
   std::string url = "https://sha384.badssl.com";

   CurlPPGetExpectSuccess(url);

   HTTPClient::ResponseResult responseResult = HttpClientGet(url);
   ASSERT_EQ(responseResult.status, 200);
}

// disabled until cert is renewed
TEST_F(HttpsTests, DISABLED_Sha512)
{
   std::string url = "https://sha512.badssl.com";

   CurlPPGetExpectSuccess(url);

   HTTPClient::ResponseResult responseResult = HttpClientGet(url);
   ASSERT_EQ(responseResult.status, 200);
}

TEST_F(HttpsTests, Ecc256)
{
#if _WIN32
   if (TestEnvironmentConfig::dockerContainerized())
   {
      GTEST_SKIP() << "Skipped due to test failure on windows docker runs -- curlpp exception code 60; todo: investigate";
   }
#endif

   std::string url = "https://ecc256.badssl.com";

   CurlPPGetExpectSuccess(url);

   HTTPClient::ResponseResult responseResult = HttpClientGet(url);
   ASSERT_EQ(responseResult.status, 200);
}

TEST_F(HttpsTests, Ecc384)
{
#if _WIN32
   if (TestEnvironmentConfig::dockerContainerized())
   {
      GTEST_SKIP() << "Skipped due to test failure on windows docker runs -- curlpp exception code 60; todo: investigate";
   }
#endif

   std::string url = "https://ecc384.badssl.com";

   CurlPPGetExpectSuccess(url);

   HTTPClient::ResponseResult responseResult = HttpClientGet(url);
   ASSERT_EQ(responseResult.status, 200);
}

TEST_F(HttpsTests, Rsa2048)
{
   std::string url = "https://rsa2048.badssl.com";

   CurlPPGetExpectSuccess(url);

   HTTPClient::ResponseResult responseResult = HttpClientGet(url);
   ASSERT_EQ(responseResult.status, 200);
}

TEST_F(HttpsTests, DISABLED_Rsa4096)
{
   std::string url = "https://rsa4096.badssl.com";

   CurlPPGetExpectSuccess(url);

   HTTPClient::ResponseResult responseResult = HttpClientGet(url);
   ASSERT_EQ(responseResult.status, 200);
}

TEST_F(HttpsTests, DISABLED_Rsa8192)
{
   std::string url = "https://rsa8192.badssl.com";

   CurlPPGetExpectSuccess(url);

   HTTPClient::ResponseResult responseResult = HttpClientGet(url);
   ASSERT_EQ(responseResult.status, 200);
}

TEST_F(HttpsTests, DISABLED_ExtendedValidation) // disabled as per SCORE-1712
{
#if _WIN32
   if (TestEnvironmentConfig::dockerContainerized()) GTEST_SKIP(); // UNIT_TEST_WIN_DOCKER_SKIP
#endif

   std::string url = "https://extended-validation.badssl.com";

   CurlPPGetExpectSuccess(url);

   HTTPClient::ResponseResult responseResult = HttpClientGet(url);
   ASSERT_EQ(responseResult.status, 200);
}

TEST_F(HttpsTests, CipherCBC_Succeed)
{
   std::string url = "https://cbc.badssl.com";

   SslCipherOptions tlsSettings = SslCipherOptions();
   tlsSettings.setCiphers(SslCipherUsageHttp, CipherSuiteBroadCompatibility);

   CurlPPGetExpectSuccess(url, tlsSettings);

   HTTPClient::ResponseResult responseResult = HttpClientGet(url, tlsSettings);
   ASSERT_EQ(responseResult.status, 200);
}

TEST_F(HttpsTests, CipherCBC_Fail)
{
   std::string url = "https://cbc.badssl.com";
   int curlError = CURLE_SSL_CONNECT_ERROR;

   SslCipherOptions tlsSettings = SslCipherOptions();
   tlsSettings.setCiphers(SslCipherUsageHttp, CipherSuiteAdvanced);

   CurlPPGetExpectError(url, curlError, tlsSettings);

   HTTPClient::ResponseResult responseResult = HttpClientGet(url, tlsSettings);
   ASSERT_EQ(responseResult.curlErrorCode, curlError);
}

TEST_F(HttpsTests, CipherRC4)
{
   std::string url = "https://rc4.badssl.com";
   int curlError = CURLE_SSL_CONNECT_ERROR;

   CurlPPGetExpectError(url, curlError);

   HTTPClient::ResponseResult responseResult = HttpClientGet(url);
   ASSERT_EQ(responseResult.curlErrorCode, curlError);
}

TEST_F(HttpsTests, CipherRC4MD5)
{
   std::string url = "https://rc4-md5.badssl.com";
   int curlError = CURLE_SSL_CONNECT_ERROR;

   CurlPPGetExpectError(url, curlError);

   HTTPClient::ResponseResult responseResult = HttpClientGet(url);
   ASSERT_EQ(responseResult.curlErrorCode, curlError);
}

TEST_F(HttpsTests, Cipher3DES)
{
   std::string url = "https://3des.badssl.com";
   int curlError = CURLE_SSL_CONNECT_ERROR;

   CurlPPGetExpectError(url, curlError);

   HTTPClient::ResponseResult responseResult = HttpClientGet(url);
   ASSERT_EQ(responseResult.curlErrorCode, curlError);
}

TEST_F(HttpsTests, CipherNull)
{
   std::string url = "https://null.badssl.com";
   int curlError = CURLE_SSL_CONNECT_ERROR;

   CurlPPGetExpectError(url, curlError);

   HTTPClient::ResponseResult responseResult = HttpClientGet(url);
   ASSERT_EQ(responseResult.curlErrorCode, curlError);
}

TEST_F(HttpsTests, CipherDH480)
{
   std::string url = "https://dh480.badssl.com";
   int curlError = CURLE_SSL_CONNECT_ERROR;

   CurlPPGetExpectError(url, curlError);

   HTTPClient::ResponseResult responseResult = HttpClientGet(url);
   ASSERT_EQ(responseResult.curlErrorCode, curlError);
}

TEST_F(HttpsTests, CipherDH512)
{
   std::string url = "https://dh512.badssl.com";
   int curlError = CURLE_SSL_CONNECT_ERROR;

   CurlPPGetExpectError(url, curlError);

   HTTPClient::ResponseResult responseResult = HttpClientGet(url);
   ASSERT_EQ(responseResult.curlErrorCode, curlError);
}

TEST_F(HttpsTests, CipherDH1024)
{
   std::string url = "https://dh1024.badssl.com";

   CurlPPGetExpectSuccess(url);

   HTTPClient::ResponseResult responseResult = HttpClientGet(url);
   ASSERT_EQ(responseResult.status, 200);
}

TEST_F(HttpsTests, CipherDH2048)
{
   std::string url = "https://dh2048.badssl.com";

   CurlPPGetExpectSuccess(url);

   HTTPClient::ResponseResult responseResult = HttpClientGet(url);
   ASSERT_EQ(responseResult.status, 200);
}

TEST_F(HttpsTests, DISABLED_CipherDHSmallSubGroup)
{
   std::string url = "https://dh-small-subgroup.badssl.com";
   int curlError = CURLE_SSL_CONNECT_ERROR;

   CurlPPGetExpectError(url, curlError);

   HTTPClient::ResponseResult responseResult = HttpClientGet(url);
   ASSERT_EQ(responseResult.curlErrorCode, curlError);
}

TEST_F(HttpsTests, DISABLED_CipherDHComposite)
{
   std::string url = "https://dh-composite.badssl.com/";
   int curlError = CURLE_SSL_CONNECT_ERROR;

   CurlPPGetExpectError(url, curlError);

   HTTPClient::ResponseResult responseResult = HttpClientGet(url);
   ASSERT_EQ(responseResult.curlErrorCode, curlError);
}


TEST_F(HttpsTests, DISABLED_CipherStaticRSA)
{
   std::string url = "https://static-rsa.badssl.com/";

   CurlPPGetExpectSuccess(url);

   HTTPClient::ResponseResult responseResult = HttpClientGet(url);
   ASSERT_EQ(responseResult.status, 200);
}

TEST_F(HttpsTests, DISABLED_ClientCert)
{
   std::string url = "https://client.badssl.com";

   SslCipherOptions tlsSettings = SslCipherOptions();

   CurlPPHelper helper;
   curlpp::Easy request;

   CurlPPSSL cssl(tlsSettings, 0, "", false, (TestEnvironmentConfig::testResourcePath() + "badssl.com-client.pem"));
   request.setOpt(new curlpp::options::SslCtxFunction(cssl));

   helper.setDefaultOptions(request, url, "GET", 0);

   try
   {
	   request.perform();
   }
   catch (curlpp::LibcurlRuntimeError ex)
   {
	   FAIL() << "curlpp certificate exception. Error code: " << ex.whatCode();
   }
   catch (...)
   {
	   FAIL() << "Unexpected exception";
   }


   HTTPClient httpClient;
   HTTPClient::RequestConfig requestConfig;
   HTTPClient::ResponseResult responseResult;

   requestConfig.verb = HTTPClient::EHTTPVerbGET;

   requestConfig.clientCertificate = TestEnvironmentConfig::testResourcePath() + "badssl.com-client.pem";
   requestConfig.tlsVersion = tlsSettings.getTLSVersion(SslCipherUsageHttp);
   requestConfig.cipherSuite = tlsSettings.getCiphers(SslCipherUsageHttp);

   httpClient.HTTPSendMessage(url.c_str(), requestConfig, responseResult);
   ASSERT_EQ(responseResult.status, 200);
}

TEST_F(HttpsTests, DISABLED_ClientCertMissing)
{
   std::string url = "https://client-cert-missing.badssl.com/";
   int curlError = CURLE_SSL_CONNECT_ERROR;

   CurlPPGetExpectError(url, curlError);

   HTTPClient::ResponseResult responseResult = HttpClientGet(url);
   ASSERT_EQ(responseResult.curlErrorCode, curlError);
}

TEST_F(HttpsTests, TLS_1_0)
{
   std::string url = "https://tls-v1-0.badssl.com:1010/";
   int curlError = CURLE_SSL_CONNECT_ERROR;

   SslCipherOptions tlsSettings = SslCipherOptions();

   /* This is the same code as in the other TLS tests, but it doesn't fail.
      I suspect that the TLS 1.0 server is misconfigured.
   tlsSettings.setTLSVersion(SslCipherUsageHttp, TLSVersion::TLS_NONE);
   tlsSettings.setCiphers(SslCipherUsageHttp, CPCAPI2::CipherSuiteLegacy);
   CurlPPHelper helper;
   curlpp::Easy request;
   CurlPPSSL cssl(tlsSettings, 0);

   request.setOpt(new curlpp::options::SslCtxFunction(cssl));
   helper.setDefaultOptions(request, url, "GET", 0);

   try
   {
	   request.perform();
	   FAIL() << "curlpp should have thrown an exception";
   }
   catch (...) { */ /* curlpp should throw an exception */ /*}
   ASSERT_EQ(HttpClientGet(url, tlsSettings).curlErrorCode, curlError);*/

   tlsSettings.setTLSVersion(SslCipherUsageHttp, TLSVersion::TLS_V1_0);
   tlsSettings.setCiphers(SslCipherUsageHttp, CPCAPI2::CipherSuiteLegacy);
   CurlPPGetExpectSuccess(url, tlsSettings);
   ASSERT_EQ(HttpClientGet(url, tlsSettings).status, 200);

   tlsSettings.setTLSVersion(SslCipherUsageHttp, TLSVersion::TLS_V1_1);
   CurlPPGetExpectError(url, curlError, tlsSettings);
   ASSERT_EQ(HttpClientGet(url, tlsSettings).curlErrorCode, curlError);

   tlsSettings.setTLSVersion(SslCipherUsageHttp, TLSVersion::TLS_V1_2);
   CurlPPGetExpectError(url, curlError, tlsSettings);
   ASSERT_EQ(HttpClientGet(url, tlsSettings).curlErrorCode, curlError);

   tlsSettings.setTLSVersion(SslCipherUsageHttp, TLSVersion::TLS_HIGHEST);
   CurlPPGetExpectSuccess(url, tlsSettings);
   ASSERT_EQ(HttpClientGet(url, tlsSettings).status, 200);

   tlsSettings.setTLSVersion(SslCipherUsageHttp, TLSVersion::TLS_NON_DEPRECATED);
   CurlPPGetExpectError(url, curlError, tlsSettings);
   ASSERT_EQ(HttpClientGet(url, tlsSettings).curlErrorCode, curlError);
}

TEST_F(HttpsTests, TLS_1_1)
{
   std::string url = "https://tls-v1-1.badssl.com:1011/";
   int curlError = CURLE_SSL_CONNECT_ERROR;

   SslCipherOptions tlsSettings = SslCipherOptions();

   tlsSettings.setTLSVersion(SslCipherUsageHttp, TLSVersion::TLS_NONE);
   tlsSettings.setCiphers(SslCipherUsageHttp, CPCAPI2::CipherSuiteLegacy);
   CurlPPHelper helper;
   curlpp::Easy request;
   CurlPPSSL cssl(tlsSettings, 0);

   request.setOpt(new curlpp::options::SslCtxFunction(cssl));
   helper.setDefaultOptions(request, url, "GET", 0);

   try
   {
	   request.perform();
	   FAIL() << "curlpp should have thrown an exception";
   }
   catch (...) { /* curlpp should throw an exception */ }
   ASSERT_EQ(HttpClientGet(url, tlsSettings).curlErrorCode, curlError);

   tlsSettings.setTLSVersion(SslCipherUsageHttp, TLSVersion::TLS_V1_0);
   CurlPPGetExpectError(url, curlError, tlsSettings);
   ASSERT_EQ(HttpClientGet(url, tlsSettings).curlErrorCode, curlError);

   tlsSettings.setTLSVersion(SslCipherUsageHttp, TLSVersion::TLS_V1_1);
   CurlPPGetExpectSuccess(url, tlsSettings);
   ASSERT_EQ(HttpClientGet(url, tlsSettings).status, 200);

   tlsSettings.setTLSVersion(SslCipherUsageHttp, TLSVersion::TLS_V1_2);
   CurlPPGetExpectError(url, curlError, tlsSettings);
   ASSERT_EQ(HttpClientGet(url, tlsSettings).curlErrorCode, curlError);

   tlsSettings.setTLSVersion(SslCipherUsageHttp, TLSVersion::TLS_HIGHEST);
   CurlPPGetExpectSuccess(url, tlsSettings);
   ASSERT_EQ(HttpClientGet(url, tlsSettings).status, 200);

   tlsSettings.setTLSVersion(SslCipherUsageHttp, TLSVersion::TLS_NON_DEPRECATED);
   CurlPPGetExpectError(url, curlError, tlsSettings);
   ASSERT_EQ(HttpClientGet(url, tlsSettings).curlErrorCode, curlError);
}

TEST_F(HttpsTests, TLS_1_2)
{
   std::string url = "https://tls-v1-2.badssl.com:1012/";
   int curlError = CURLE_SSL_CONNECT_ERROR;

   SslCipherOptions tlsSettings = SslCipherOptions();

   tlsSettings.setTLSVersion(SslCipherUsageHttp, TLSVersion::TLS_NONE);
   CurlPPHelper helper;
   curlpp::Easy request;
   CurlPPSSL cssl(tlsSettings, 0);

   request.setOpt(new curlpp::options::SslCtxFunction(cssl));
   helper.setDefaultOptions(request, url, "GET", 0);

   try
   {
	   request.perform();
	   FAIL() << "curlpp should have thrown an exception";
   }
   catch (...) { /* curlpp should throw an exception */ }
   ASSERT_EQ(HttpClientGet(url, tlsSettings).curlErrorCode, curlError);

   tlsSettings.setTLSVersion(SslCipherUsageHttp, TLSVersion::TLS_V1_0);
   CurlPPGetExpectError(url, curlError, tlsSettings);
   ASSERT_EQ(HttpClientGet(url, tlsSettings).curlErrorCode, curlError);

   tlsSettings.setTLSVersion(SslCipherUsageHttp, TLSVersion::TLS_V1_1);
   CurlPPGetExpectError(url, curlError, tlsSettings);
   ASSERT_EQ(HttpClientGet(url, tlsSettings).curlErrorCode, curlError);

   tlsSettings.setTLSVersion(SslCipherUsageHttp, TLSVersion::TLS_V1_2);
   CurlPPGetExpectSuccess(url, tlsSettings);
   ASSERT_EQ(HttpClientGet(url, tlsSettings).status, 200);

   tlsSettings.setTLSVersion(SslCipherUsageHttp, TLSVersion::TLS_HIGHEST);
   CurlPPGetExpectSuccess(url, tlsSettings);
   ASSERT_EQ(HttpClientGet(url, tlsSettings).status, 200);

   tlsSettings.setTLSVersion(SslCipherUsageHttp, TLSVersion::TLS_NON_DEPRECATED);
   CurlPPGetExpectSuccess(url, tlsSettings);
   ASSERT_EQ(HttpClientGet(url, tlsSettings).status, 200);
}
}
