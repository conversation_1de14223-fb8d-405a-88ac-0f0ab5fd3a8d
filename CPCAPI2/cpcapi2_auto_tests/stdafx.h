// stdafx.h : include file for standard system include files,
// or project specific include files that are used frequently, but
// are changed infrequently
//

#pragma once

#include "targetver.h"

#include <stdio.h>
#ifndef __linux__
#include <tchar.h>
#endif


// TODO: reference additional headers your program requires here
#include <string>
#include <set>
#include <thread>
#include <functional>
#include <future>

#include <gtest/gtest.h>

#include <cpcapi2.h>
#include <brand_branded.h>
