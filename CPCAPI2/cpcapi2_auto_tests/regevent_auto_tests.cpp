#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#if (CPCAPI2_BRAND_SIP_REG_EVENT_MODULE == 1)

#include "cpcapi2_test_fixture.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>

#include "test_framework/cpcapi2_test_framework.h"
#include "test_framework/bare_bones_sip_endpoint.h"
#include "test_account_events.h"

using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::SipEvent;
using namespace CPCAPI2::SipRegEvent;

namespace {

class RogersTestAccount : public TestAccount
{
public:
   RogersTestAccount(const std::string& name, bool autoRegEvent = false) : TestAccount(name, Account_NoInit)
   {
      if (name == "alice")
      {
         // one to one auto intercom test account
         config.settings.displayName = "NETWORK TRANSFO RMATION 1933-000";
         config.settings.username = "+***********";
         config.settings.auth_username = "+<EMAIL>";
         config.settings.password = "0c0e67e1c0de7da73f14e84f52e016a4";
      } 
      else
      {
         assert(false);
      }

      config.settings.domain = "onenumber.rogers.com";
      config.settings.outboundProxy = "bcvan.access.ims.rogers.com";
      config.settings.registrationIntervalSeconds = 60;
      config.settings.enableRegeventDeregistration = autoRegEvent;

      init();

      if (!autoRegEvent)
      {
         reg = SipRegEventManager::getInterface(phone);
         reg->setHandler(handle, (SipRegEventHandler*) 0xDEADBEEF);
         regEvents = networkChangeEvents = new test::EventHandler(name.c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(reg));
      }

      enable();
   }

   SipRegEventManager* reg;
   CPCAPI2::test::EventHandler* regEvents;

};

class RegEventTest : public CpcapiAutoTest
{
public:
   RegEventTest() {}
   virtual ~RegEventTest() {}
};

TEST_F(RegEventTest, ManualRegeventHandling) {

   RogersTestAccount alice("alice");
   SipRegEventSubscriptionHandle handle = alice.reg->createSubscription(alice.handle);
   SipRegEventSubscriptionSettings regSubSettings;
   alice.reg->applySubscriptionSettings(handle, regSubSettings);
   alice.reg->start(handle);

   {
      // Wait for a new subscription
      SipRegEventSubscriptionHandle h;
      NewRegEventSubscriptionEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.regEvents, "SipRegEventHandler::onNewSubscription",
         5000,
         AlwaysTruePred(), h, evt));
      ASSERT_EQ(handle, h);
      ASSERT_EQ(alice.handle, evt.account);
   }

   {
      // Wait for the subscription state to transition to Active
      SipRegEventSubscriptionHandle h;
      RegEventSubscriptionStateChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.regEvents,
         "SipRegEventHandler::onSubscriptionStateChanged",
         5000,
         AlwaysTruePred(), h, evt));
      ASSERT_EQ(handle, h);
      ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
   }

   {
      // Wait for a NOTIFY
      SipRegEventSubscriptionHandle h;
      RegEventUpdatedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.regEvents,
         "SipRegEventHandler::onRegStateUpdated",
         5000,
         AlwaysTruePred(), h, evt));
      ASSERT_EQ(handle, h);
      cpc::vector<ChangeItem> regChanges = evt.registrationEventInfo.changes;
      ASSERT_EQ(2, regChanges.size());
      ASSERT_EQ(RegEvent_Created, regChanges[0].registrationEvent);
   }

   // register second account with same URI to trigger reg event
   RogersTestAccount alice2("alice");

   {
      // Wait for another NOTIFY to indicate de-registration of first account
      SipRegEventSubscriptionHandle h;
      RegEventUpdatedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.regEvents,
         "SipRegEventHandler::onRegStateUpdated",
         60000,
         AlwaysTruePred(), h, evt));
      ASSERT_EQ(handle, h);
      cpc::vector<ChangeItem> regChanges = evt.registrationEventInfo.changes;
      ASSERT_EQ(4, regChanges.size());
   }

   // Terminate the subscription
   ASSERT_EQ(alice.reg->end(handle), kSuccess);

   {
      // Wait for the subscription state to transition to Terminated
      SipRegEventSubscriptionHandle h;
      RegEventSubscriptionEndedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.regEvents,
         "SipRegEventHandler::onSubscriptionEnded",
         10000,
         AlwaysTruePred(), h, evt));
      ASSERT_EQ(handle, h);
      ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
   }      
}

TEST_F(RegEventTest, AutoRegeventHandling) {
   RogersTestAccount alice("alice", true);

   // register second account with same URI to trigger reg event
   RogersTestAccount alice2("alice");

   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
      // Expect a unregistered event
      ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged",
         20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Unregistered, evt.accountStatus);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_NetworkDeregistered, evt.reason);
   }
}
}

#endif //CPCAPI2_BRAND_SIP_REG_EVENT_MODULE
