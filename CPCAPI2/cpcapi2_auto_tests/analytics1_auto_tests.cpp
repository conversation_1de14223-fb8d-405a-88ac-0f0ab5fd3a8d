#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#if (CPCAPI2_BRAND_ANALYTICS_MODULE == 1)

#include "cpcapi2_test_fixture.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>

#include "test_framework/cpcapi2_test_framework.h"
#include "test_framework/bare_bones_sip_endpoint.h"
#include "test_framework/xmpp_test_helper.h"
#include "test_framework/http_test_framework.h"
#include "test_framework/ptt_test_helper.h"
#include "test_call_events.h"
#include "test_account_events.h"
#include "impl/ptt/PushToTalkHandlerInternal.h"
#include "impl/ptt/PushToTalkManagerInternal.h"
#include "impl/ptt/PushToTalkSyncHandler.h"
#include "impl/ptt/PushToTalkManagerImpl.h"


#include <cpcstl/string.h>
#include <string>
#include <sstream>
#include <thread>
#include <future>
#include <memory>

#include <libxml/xpath.h>
#include <libxml/xpathInternals.h>
//#include "../CPCAPI2/impl/util/LibxmlSharedUsage.h"
#include "../../impl/util/LibxmlSharedUsage.h"
#include "analytics1/AnalyticsManagerInt.h"
#include "phone/PhoneInternal.h"

using namespace CPCAPI2;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::Analytics;
using namespace CPCAPI2::test;
using namespace CPCAPI2::XmppAccount;
using namespace CPCAPI2::SipConversation;
using namespace CPCAPI2::PushToTalk;

namespace {

   class Analytics1ModuleTest : public CpcapiAutoTest
   {
   public:
      Analytics1ModuleTest() {}
      virtual ~Analytics1ModuleTest() {}
      AnalyticsHandle openModuleWithEmptyServerUrl(AnalyticsManager* mgr);
      _xmlNode* getXmlNode(std::string doc, std::string path);
      std::string getVqmReportValue(std::string doc, std::string path);
      bool getXmlNodeExists(std::string doc, std::string path);

      void configureSipAndPttAccounts(TestAccount& alice, TestAccount& bob, PushToTalkServiceHandle& alicePttService, PushToTalkServiceHandle& bobPttService, AnalyticsHandle& ahAlice, AnalyticsHandle& ahBob, PttIdentity& aliceSipIdentity);
      void makeSipCall(TestAccount& alice, TestAccount& bob);
      void makePttCall(TestAccount& alice, TestAccount& bob, const PushToTalkServiceHandle& alicePttService, const PushToTalkServiceHandle& bobPttService, PttIdentity& aliceSipIdentity);
      void verifySipAndPttCallReport(TestAccount& alice, TestAccount& bob, AnalyticsHandle& ahAlice, AnalyticsHandle& ahBob);
      void verifyPttReport(TestAccount& account, PushToTalkServiceSettings& settings, int callCount, std::vector<bool>& incomingCalls, std::vector<std::string>& channels, std::vector<int>& responsesReceived);

      const int MaxAccounts = 8;
      const int HadFailedRegistrations = -1; // used in failedRegistrations below
      struct Account
      {
         std::string name;
         std::shared_ptr<TestAccountBase> acct;
         std::string id;   // the ID from the acct above so we can check it after the acct has been freed
         bool enabled;
         int failedRegistrations;
         int failedCalls;

         Account(const std::string& _name) : name(_name), enabled(false), failedRegistrations(0), failedCalls(0)
         {            
         }

         void SetAccount(std::shared_ptr<TestAccountBase> _acct) 
         {
            acct = _acct;
            if (std::dynamic_pointer_cast<TestAccount>(_acct)) 
            {
               id = "SIP.";
               std::ostringstream strs;
               strs << std::static_pointer_cast<TestAccount>(_acct)->handle;
               id += strs.str();
            }
            else
            {
               id = "XMPP.";
               std::ostringstream strs;
               strs << std::static_pointer_cast<XmppTestAccount>(_acct)->handle;
               id += strs.str();
            }
         }
      };
      void SendAndCheckReportAccounts(AnalyticsManager *analyticsManager, AnalyticsHandle ah, const std::vector<Analytics1ModuleTest::Account>& accounts, const SipAccountSettings& settings);
      void CheckReportAccounts(cpc::string content, const std::vector<Analytics1ModuleTest::Account>& accounts, const SipAccountSettings& settings);
      void CheckReportAccount(cpc::string content, const Analytics1ModuleTest::Account& account, const SipAccountSettings& settings);
   };

   //this test is to make sure we get correct response from ccsDev or ccs3 servers.
   //also can be modified later to add in more data to see how server responds
   TEST_F(Analytics1ModuleTest, LoginTestStrettoResponse)
   {
       // This test account creates the phone, the analytics module, calls open() 
       //on module with default values, and creates/init/enable the Sip account
       TestAccount alice("alice");

       //get the analytics module created by testAccount class
       AnalyticsManager *analyticsManager = alice.analyticsManager;
       AnalyticsHandle ah = openModuleWithEmptyServerUrl(analyticsManager);

       static_cast<AnalyticsManagerInt*>(analyticsManager)->instantMessageInfoFired(alice.handle, true, true);
       static_cast<AnalyticsManagerInt*>(analyticsManager)->instantMessageInfoFired(alice.handle, false, true);

       // Set some stats
       PresenceStats ps;
       ps.numContacts = 10;
       ps.numContactsWithPresence = 6;
       analyticsManager->setPresenceStats(ah, ps);

       ProvisioningStats provst;
       provst.failedProvisionAttempts = 1;
       provst.successfulProvisionAttempts = 1;
       analyticsManager->setProvisioningStats(ah, provst);

       StabilityStats ss;
       ss.numCrashes = 6000; // probably this number required before I call this feature stable :)
       analyticsManager->setStabilityStats(ah, ss);

       std::this_thread::sleep_for(std::chrono::milliseconds(3000));

       analyticsManager->sendReport(ah);

       // Wait for response from report
       {
       auto sendReponse = std::async(std::launch::async, [&]() {
           AnalyticsHandle h;
           OnReportResponseEvent evt;
           ASSERT_TRUE(cpcWaitForEvent(
               alice.analyticsEvents,
               "AnalyticsHandler::onReportResponse",
               15000,
               AlwaysTruePred(),
               h, evt));

               ASSERT_EQ(evt.responseCode, 200);
           });
       waitFor(sendReponse);
       }

       //close the module
       analyticsManager->close(ah);
       std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   }

   // currently using local web server to just test the XML doc output
   TEST_F(Analytics1ModuleTest, LoginTestServerResponse )
   {
      // This test account creates the phone, the analytics module, calls open() 
      //on module with default values, and creates/init/enable the Sip account
      TestAccount alice("alice");

      //get phone from the TestAccount Class
      Phone* phone = alice.phone;

      //get the analytics module created by testAccount class
      AnalyticsManager *analyticsManager = alice.analyticsManager;
      AnalyticsHandle ah = openModuleWithEmptyServerUrl(analyticsManager);
      std::this_thread::sleep_for(std::chrono::milliseconds(3000));

      analyticsManager->sendReport(ah);

      // Wait for response from report
      {
         auto sendReponse = std::async(std::launch::async, [&] () {
            AnalyticsHandle h;
            OnReportResponseEvent evt;
            ASSERT_TRUE( cpcWaitForEvent(
            alice.analyticsEvents,
               "AnalyticsHandler::onReportResponse",
               15000,
               AlwaysTruePred( ),
               h, evt ) );

         ASSERT_EQ(evt.responseCode, 200);
         });
         waitFor(sendReponse);
      }

      //close the module
      analyticsManager->close(ah);
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   }

   TEST_F(Analytics1ModuleTest, LoginTestServerResponse_NoClose )
   {
      // This test account creates the phone, the analytics module, calls open()
      //on module with default values, and creates/init/enable the Sip account
      TestAccount alice("alice");

      //get phone from the TestAccount Class
      Phone* phone = alice.phone;

      //get the analytics module created by testAccount class
      AnalyticsManager *analyticsManager = alice.analyticsManager;
      AnalyticsHandle ah = openModuleWithEmptyServerUrl(analyticsManager);
      std::this_thread::sleep_for(std::chrono::milliseconds(3000));

      analyticsManager->sendReport(ah);

      // Wait for response from report
      {
         auto sendReponse = std::async(std::launch::async, [&] () {
            AnalyticsHandle h;
            OnReportResponseEvent evt;
            ASSERT_TRUE( cpcWaitForEvent(
            alice.analyticsEvents,
               "AnalyticsHandler::onReportResponse",
               15000,
               AlwaysTruePred( ),
               h, evt ) );

         ASSERT_EQ(evt.responseCode, 200);
         });
         waitFor(sendReponse);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   }

   TEST_F(Analytics1ModuleTest, RemoveAccountHandler)
   {
      Phone* phone = Phone::create();
      phone->initialize(LicenseInfo(), (PhoneHandler*)NULL);
      AnalyticsManager* analyticsManager = AnalyticsManager::getInterface(phone);

      class TestHandler : public AnalyticsHandler {
      public:
         TestHandler(): receivedCallback(false) {}
         ~TestHandler()
         {
         }

         virtual int onConnectionFailed( const AnalyticsHandle& serverHandle, const OnConnectionFailedEvent& evt )
         {
            return kSuccess;
         }

         virtual int onReportResponse( const AnalyticsHandle& serverHandle, const OnReportResponseEvent& evt )
         {
            receivedCallback = true;
            return kSuccess;
         }

         bool receivedCallback;
      };

      std::unique_ptr<TestHandler> aliceHandler(new TestHandler());
      AnalyticsHandle ah = openModuleWithEmptyServerUrl(analyticsManager);

      analyticsManager->setHandler(aliceHandler.get());
      analyticsManager->sendReport(ah);

      auto start = std::chrono::high_resolution_clock::now();
      std::atomic_bool threadStopFlag(false);
      auto accountEvent = std::async(std::launch::async, [&] ()
      {
         while (aliceHandler->receivedCallback == false)
         {
            analyticsManager->process(AnalyticsManager::kBlockingModeNonBlocking);

            if (threadStopFlag) return;

            std::this_thread::sleep_for(std::chrono::milliseconds(100));
         }
      });

      flaggableWaitFor(accountEvent, threadStopFlag);
      auto end = std::chrono::high_resolution_clock::now();

      analyticsManager->sendReport(ah);
      analyticsManager->setHandler(NULL);
      aliceHandler.reset();

      // wait about as long as it took before
      std::this_thread::sleep_for((end-start) * 2);

      analyticsManager->process(SipAccountManager::kBlockingModeNonBlocking);

      Phone::release(phone);
   }

   TEST_F(Analytics1ModuleTest, LoginTestConnectionFailed) {

       // This test account creates the phone, the analytics module, calls open() 
       //on module with default values, and creates/init/enable the Sip account
       TestAccount alice("alice");

       //get phone from the TestAccount Class
       Phone* phone = alice.phone;
       SipAccountManager* acct = SipAccountManager::getInterface(phone);

       //get the analytics module created by testAccount class
       AnalyticsManager *analyticsManager = alice.analyticsManager;

        AnalyticsSettings serverSettings;
        serverSettings.serverURL = "http://0.0.0.0";

        serverSettings.strettoUserName = "<EMAIL>";
        serverSettings.httpUserName = "vqmUser";
        serverSettings.httpPassword = "lockTheTaskbar";

        GeneralStats generalStats;

        //set the basic stats to the default handler which is always (1)
        //the third paramater is optional and is only used by the test cases due
        //to issue with timing of creating module & account getting configured.
        //In real scenario, the app layer will never pass in the third parameter.
        AnalyticsHandle ah = analyticsManager->open(serverSettings, generalStats, 1);
        std::this_thread::sleep_for(std::chrono::milliseconds(3000));

       analyticsManager->sendReport(ah);

       // Wait for response from server connection object
       {
       auto connectionReponse = std::async(std::launch::async, [&]() {
           AnalyticsHandle h;
           OnConnectionFailedEvent evt;
           ASSERT_TRUE(cpcWaitForEvent(
           alice.analyticsEvents,
           "AnalyticsHandler::onConnectionFailed",
           25000,
           AlwaysTruePred(),
           h, evt));

           ASSERT_NE(evt.errNo, 0);
        });
       waitFor(connectionReponse);
       }

       //close the module
       analyticsManager->close(ah);
       std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   }

   TEST_F(Analytics1ModuleTest, LoginTestQueryEncoding) {

       // This test account creates the phone, the analytics module, calls open() 
       //on module with default values, and creates/init/enable the Sip account
       TestAccount alice("alice");

       //get phone from the TestAccount Class
       Phone* phone = alice.phone;
       SipAccountManager* acct = SipAccountManager::getInterface(phone);

       static bool receivedRequest;
       receivedRequest = false;
       SimpleWeb::Server<SimpleWeb::HTTP> server;
       server.config.address = "127.0.0.1";
       server.config.port = 5060;
       server.default_resource["POST"] = [](std::shared_ptr<HttpServer::Response> response,
          std::shared_ptr<HttpServer::Request> request)
       {
          receivedRequest = true;
          ASSERT_TRUE(request->query_string.compare("user=d.bomb+is+nice%2bcool!%40counterpath.com&device=") == 0);
          auto content = request->content.string();
          *response << "HTTP/1.1 200 OK\r\nContent-Length: " << content.length() << "\r\n\r\n" << content;
       };

       thread server_thread([&]()
       {
          server.start();
       });

       // the SimpleWeb example code demonstrates waiting for 1 second to let the HTTP server start.
       // unsure if there we could instead rely on an event..
       std::this_thread::sleep_for(std::chrono::milliseconds(1000));

       //get the analytics module created by testAccount class
       AnalyticsManager *analyticsManager = alice.analyticsManager;

        AnalyticsSettings serverSettings;
        serverSettings.serverURL = "http://127.0.0.1:5060";

        // test encoding and NOT encoding of certain characters such as "+", "@" and space
        serverSettings.strettoUserName = "d.bomb is nice+cool!@counterpath.com";
        serverSettings.httpUserName = "vqmUser";
        serverSettings.httpPassword = "lockTheTaskbar";

        GeneralStats generalStats;

        //set the basic stats to the default handler which is always (1)
        //the third paramater is optional and is only used by the test cases due
        //to issue with timing of creating module & account getting configured.
        //In real scenario, the app layer will never pass in the third parameter.
        AnalyticsHandle ah = analyticsManager->open(serverSettings, generalStats, 1);
        std::this_thread::sleep_for(std::chrono::milliseconds(3000));

       analyticsManager->sendReport(ah);

       // Wait for response from server connection object
       {
       auto sendReponse = std::async(std::launch::async, [&]() {
          AnalyticsHandle h;
          OnReportResponseEvent evt;
          ASSERT_TRUE(cpcWaitForEvent(
             alice.analyticsEvents,
             "AnalyticsHandler::onReportResponse",
             15000,
             AlwaysTruePred(),
             h, evt));

          ASSERT_EQ(evt.responseCode, 200);
       });
       waitFor(sendReponse);
       }

       ASSERT_TRUE(receivedRequest);

       server.stop();
       server_thread.join();

       //close the module
       analyticsManager->close(ah);
       std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   }

   std::string resourcePathRegex(const std::string& resourcePath)
   {
      std::stringstream ss;
      ss << resourcePath << "$";
      return ss.str();
   }

   TEST_F(Analytics1ModuleTest, BasicHttpVerify) {

      // This test account creates the phone, the analytics module, calls open()
      //on module with default values, and creates/init/enable the Sip account
      TestAccount alice("alice");

      //get phone from the TestAccount Class
      Phone* phone = alice.phone;
      SipAccountManager* acct = SipAccountManager::getInterface(phone);
      (void)acct;

      //get the analytics module created by testAccount class
      AnalyticsManager *analyticsManager = alice.analyticsManager;

      AnalyticsSettings serverSettings;
      serverSettings.serverURL = "http://127.0.0.1:50060/uem";

      serverSettings.strettoUserName = "<EMAIL>";
      serverSettings.httpUserName = "vqmUser";
      serverSettings.httpPassword = "lockTheTaskbar";

      GeneralStats generalStats;

      //set the basic stats to the default handler which is always (1)
      //the third paramater is optional and is only used by the test cases due
      //to issue with timing of creating module & account getting configured.
      //In real scenario, the app layer will never pass in the third parameter.
      AnalyticsHandle ah = analyticsManager->open(serverSettings, generalStats, 1);
      std::this_thread::sleep_for(std::chrono::milliseconds(3000));


      const std::string resourcePath("/uem");
      SimpleWeb::Server<SimpleWeb::HTTP> httpServer;
      httpServer.config.port = 50060;

      // listens for requests at /test
      httpServer.resource[resourcePathRegex(resourcePath)]["POST"]=[](std::shared_ptr<HttpServer::Response> response,
                                             std::shared_ptr<HttpServer::Request> request)
      {
         // get body contents, and echo it back in the response

         auto content=request->content.string();
         *response << "HTTP/1.1 200 OK\r\nContent-Length: " << content.length() << "\r\n\r\n" << content;
      };

      thread server_thread([&]()
      {
         httpServer.start();
      });

      // the SimpleWeb example code demonstrates waiting for 1 second to let the HTTP server start.
      // unsure if there we could instead rely on an event..
      std::this_thread::sleep_for(std::chrono::seconds(1));

      analyticsManager->sendReport(ah);

      // Wait for response from server connection object
      {
         auto connectionReponse = std::async(std::launch::async, [&]() {
            AnalyticsHandle h;
            OnReportCreatedSuccessEvent evt;
            ASSERT_TRUE(cpcWaitForEvent(
                alice.analyticsEvents,
                "AnalyticsHandlerInt::onReportCreatedSuccess",
                15000,
                AlwaysTruePred(),
                h, evt));
         });
         waitFor(connectionReponse);
      }

      httpServer.stop();
      server_thread.join();

      //close the module
      analyticsManager->close(ah);
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   }


   TEST_F(Analytics1ModuleTest, SipAccountTest) {

       // This test account creates the phone, the analytics module, calls open() 
       //on module with default values, and creates/init/enable the Sip account
       TestAccount alice("alice");

       //get phone from the TestAccount Class
       Phone* phone = alice.phone;
       SipAccountManager* acct = SipAccountManager::getInterface(phone);
       const SipAccountSettings& settings = alice.config.settings;

       //get the analytics module created by testAccount class
       AnalyticsManager *analyticsManager = alice.analyticsManager;
       //set the basic stats to the default handler which is always (1)
       //the third paramater is optional and is only used by the test cases due
       //to issue with timing of creating module & account getting configured.
       //In real scenario, the app layer will never pass in the third parameter.
       AnalyticsHandle ah = openModuleWithEmptyServerUrl(analyticsManager);
       std::this_thread::sleep_for(std::chrono::milliseconds(3000));

       analyticsManager->sendReport(ah);

       // Wait for response from report
       {
           auto sendReponse = std::async(std::launch::async, [&]() {
               AnalyticsHandle h;
               OnReportCreatedSuccessEvent evt;
               ASSERT_TRUE(cpcWaitForEvent(
                   alice.analyticsEvents,
                   "AnalyticsHandlerInt::onReportCreatedSuccess",
                   15000,
                   AlwaysTruePred(),
                   h, evt));

               //make sure document is not empty
               ASSERT_NE(evt.content, cpc::string());
               //make sure settings_data tag exists
               bool exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data[1]");
               ASSERT_TRUE(exists);
               //make sure account_list tag exists
               exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list[1]");
               ASSERT_TRUE(exists);
               //make sure at least 1 account tag exists
               exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list/account[1]");
               ASSERT_TRUE(exists);

               _xmlNode* node = getXmlNode(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list/account[1]");
               //make sure id = SIP*
               ASSERT_EQ(strncmp((const char *)node->parent->properties->children->content, "SIP", strlen("SIP")), 0);
               //make sure relevant data tags exist and their content is not empty
               EXPECT_STREQ((const char *)node->properties->children->content, "domain"); //tag name
               EXPECT_STREQ((const char *)node->properties->next->children->content, settings.domain); //tag value
               node = node->next; // get next data tag
               EXPECT_STREQ((const char *)node->properties->children->content, "username"); //tag name
               //ASSERT_EQ(strncmp((const char *)node->properties->next->children->content, "alice", strlen("alice")), 0);
               EXPECT_STREQ((const char *)node->properties->next->children->content, settings.username); //tag value
               node = node->next; // get next data tag
               // Only include the outbound proxy if it's enabled.
               if (!settings.outboundProxy.empty())
               {
                   EXPECT_STREQ((const char *)node->properties->children->content, "outboundProxy"); //tag name
                   EXPECT_STREQ((const char *)node->properties->next->children->content, settings.outboundProxy); //tag value
                   node = node->next; // get next data tag
               }

               EXPECT_STREQ((const char *)node->properties->children->content, "protocol"); //tag name
               EXPECT_STREQ((const char *)node->properties->next->children->content, "SIP"); //tag value
               node = node->next; // get next data tag
               EXPECT_STREQ((const char *)node->properties->children->content, "enabled"); //tag name
               EXPECT_STREQ((const char *)node->properties->next->children->content, "true"); //tag value
               node = node->next; // get next data tag
               EXPECT_STREQ((const char *)node->properties->children->content, "sipSimpleSupported"); //tag name
               EXPECT_STREQ((const char *)node->properties->next->children->content, "true"); //tag value
               node = node->next; // get next data tag
               EXPECT_STREQ((const char *)node->properties->children->content, "signalingTransport"); //tag name
               EXPECT_STRNE((const char *)node->properties->next->children->content, ""); //tag value

           });
           waitFor(sendReponse);
       }

       //close the module
       analyticsManager->close(ah);
       std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   }

   TEST_F(Analytics1ModuleTest, SipAccountFailureTest) {

       // This test account creates the phone, the analytics module, calls open() 
       //on module with default values, and creates/init/enable the Sip account
       TestAccount alice("alice", Account_NoInit, false);
       alice.config.settings.outboundProxy = "";
       alice.config.settings.domain = "demo.xten.con"; // Intentional typo
       alice.init();

       // Perform registration
       ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);

       CPCAPI2::SipAccount::SipAccountHandle h;
       CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

       // Expect a registering event
       ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged",
           20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
       ASSERT_EQ(alice.handle, h);
       ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);
       ASSERT_EQ(0, evt.signalingStatusCode);

       // Expect a 503 registration failure
       ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged",
           40000, CPCAPI2::test::AlwaysTruePred(), h, evt));
       ASSERT_EQ(alice.handle, h);
       ASSERT_EQ(503, evt.signalingStatusCode);
       ASSERT_EQ(evt.reason, CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Dns_Lookup);
       ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);

       //get phone from the TestAccount Class
       Phone* phone = alice.phone;
       SipAccountManager* acct = SipAccountManager::getInterface(phone);
       const SipAccountSettings& settings = alice.config.settings;

       std::string transport = "";
       if (settings.sipTransportType == SipAccountTransport_Auto) transport = "Auto";
       if (settings.sipTransportType == SipAccountTransport_UDP) transport = "UDP";
       if (settings.sipTransportType == SipAccountTransport_TCP) transport = "TCP";
       if (settings.sipTransportType == SipAccountTransport_TLS) transport = "TLS";

       //get the analytics module created by testAccount class
       AnalyticsManager *analyticsManager = alice.analyticsManager;
       //set the basic stats to the default handler which is always (1)
       //the third paramater is optional and is only used by the test cases due
       //to issue with timing of creating module & account getting configured.
       //In real scenario, the app layer will never pass in the third parameter.
       AnalyticsHandle ah = openModuleWithEmptyServerUrl(analyticsManager);
       std::this_thread::sleep_for(std::chrono::milliseconds(3000));

       analyticsManager->sendReport(ah);

       // Wait for response from report
       {
           auto sendReponse = std::async(std::launch::async, [&]() {
               AnalyticsHandle h;
               OnReportCreatedSuccessEvent evt;
               ASSERT_TRUE(cpcWaitForEvent(
                   alice.analyticsEvents,
                   "AnalyticsHandlerInt::onReportCreatedSuccess",
                   15000,
                   AlwaysTruePred(),
                   h, evt));

               //make sure document is not empty
               ASSERT_NE(evt.content, cpc::string());
               //make sure settings_data tag exists
               bool exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data[1]");
               ASSERT_TRUE(exists);
               //make sure account_list tag exists
               exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data/account_list[1]");
               ASSERT_TRUE(exists);
               //make sure at least 1 account tag exists
               exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data/account_list/account[1]");
               ASSERT_TRUE(exists);

               _xmlNode* node = getXmlNode(evt.content.c_str(), "/cpc_usage_report/activity_data/account_list/account[1]");
               //make sure id = SIP*
               ASSERT_EQ(strncmp((const char *)node->parent->properties->children->content, "SIP", strlen("SIP")), 0);
               //make sure relevant data tags exist and their content is not empty
               EXPECT_STREQ((const char *)node->properties->children->content, "failedRegistrations"); //tag name
               EXPECT_STREQ((const char *)node->properties->next->children->content, "1"); //tag value

           });
           waitFor(sendReponse);
       }

       //close the module
       analyticsManager->close(ah);
       std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   }

   void Analytics1ModuleTest::SendAndCheckReportAccounts(AnalyticsManager *analyticsManager, AnalyticsHandle ah, const std::vector<Analytics1ModuleTest::Account>& accounts, const SipAccountSettings& settings)
   {
      analyticsManager->sendReport(ah);

      // Wait for response from report
      auto sendReponse = std::async(std::launch::async, [&]() {
         AnalyticsHandle h;
         OnReportCreatedSuccessEvent evt;
         ASSERT_TRUE(cpcWaitForEvent(
               std::static_pointer_cast<TestAccount>(accounts[0].acct)->analyticsEvents,
               "AnalyticsHandlerInt::onReportCreatedSuccess",
               600000,
               AlwaysTruePred(),
               h, evt));

         CheckReportAccounts(evt.content, accounts, settings);
      });
      waitForMs(sendReponse, std::chrono::milliseconds(600000));
   }

   void Analytics1ModuleTest::CheckReportAccounts(cpc::string content, const std::vector<Analytics1ModuleTest::Account>& accounts, const SipAccountSettings& settings)
   {
      const char *strContent = content.c_str();
      std::cout << std::endl << "Checking report:" << std::endl << strContent << std::endl << std::endl;

      //make sure document is not empty
      ASSERT_NE(content, cpc::string());

      int reportAccountIndex = 1;     // 1-based index
      for (const Analytics1ModuleTest::Account& account: accounts)
      {
         if (account.acct  || account.failedCalls || account.failedRegistrations)
            reportAccountIndex++;     // this account has NOT been removed, or is required due to stats

         CheckReportAccount(content, account, settings);
      }

      // make sure there are no more accounts
      std::string temp = "/cpc_usage_report/settings_data/account_list/account[";
      temp += std::to_string(reportAccountIndex);
      temp += "]";
      _xmlNode* node = getXmlNode(content.c_str(), temp);
      ASSERT_EQ(node, (_xmlNode*)NULL);
   }

   void Analytics1ModuleTest::CheckReportAccount(cpc::string content, const Analytics1ModuleTest::Account& account, const SipAccountSettings& settings)
   {
      bool found = false;
      for (int reportAccountIndex = 1; reportAccountIndex <= MaxAccounts; reportAccountIndex++)
      {
         std::string temp = "/cpc_usage_report/settings_data/account_list/account[";
         temp += std::to_string(reportAccountIndex);
         temp += "]";
         _xmlNode* node = getXmlNode(content.c_str(), temp);
         if (node == NULL)
            continue;      // no more accounts in the report or the node is an empty tag

         // first item is the ID
         const char* tempID = (const char *)node->parent->properties->children->content;
         if (std::string((const char *)node->parent->properties->children->content) != account.id)
            continue;   // not a matching account

         ASSERT_FALSE(found);   // make sure it's not in the list more than once
         found = true;

         if (account.name.find("Xmpp") != std::string::npos)
         {
            ASSERT_EQ(std::string((const char *)node->parent->properties->children->content).compare(0, strlen("XMPP"), "XMPP"), 0);
            EXPECT_STREQ((const char *)node->properties->children->content, "domain"); //tag name
//            EXPECT_STREQ((const char *)node->properties->next->children->content, settings.domain); //tag value, XMPP test framework assigns username and domain
            node = node->next; // get next data tag
            EXPECT_STREQ((const char *)node->properties->children->content, "username"); //tag name
//            EXPECT_STREQ((const char *)node->properties->next->children->content, settings.username); //tag value, XMPP test framework assigns username and domain
            node = node->next; // get next data tag
            EXPECT_STREQ((const char *)node->properties->children->content, "protocol"); //tag name
            EXPECT_STREQ((const char *)node->properties->next->children->content, "XMPP"); //tag value
            node = node->next; // get next data tag
            EXPECT_STREQ((const char *)node->properties->children->content, "enabled"); //tag name
const char* temp = (const char *)node->properties->next->children->content;
            if (account.enabled)
               EXPECT_STREQ((const char *)node->properties->next->children->content, "true"); //tag value
            else
               EXPECT_STREQ((const char *)node->properties->next->children->content, "false"); //tag value
            node = node->next; // get next data tag
            EXPECT_STREQ((const char *)node->properties->children->content, "sipSimpleSupported"); //tag name
            EXPECT_STREQ((const char *)node->properties->next->children->content, "false"); //tag value
            node = node->next; // get next data tag
            EXPECT_STREQ((const char *)node->properties->children->content, "signalingTransport"); //tag name
            EXPECT_STREQ((const char *)node->properties->next->children->content, "TCP"); //tag value
         }
         else
         {
            //make sure id = SIP*
            ASSERT_EQ(std::string((const char *)node->parent->properties->children->content).compare(0, strlen("SIP"), "SIP"), 0);
            //make sure relevant data tags exist and their content is not empty
            EXPECT_STREQ((const char *)node->properties->children->content, "domain"); //tag name
            if (account.failedRegistrations == 0) // our test uses a bad domain for unregistered account
               EXPECT_STREQ((const char *)node->properties->next->children->content, settings.domain); //tag value
            node = node->next; // get next data tag
            EXPECT_STREQ((const char *)node->properties->children->content, "username"); //tag name
            // for some reason the name provided in the report has a suffix starting with an underscore like "Sip0_1217c659ec80" so we match on the prefix
            ASSERT_EQ(strncmp((const char *)node->properties->next->children->content, account.name.c_str(), account.name.length()), 0);
            node = node->next; // get next data tag
            // Only include the outbound proxy if it's enabled.
            if (!settings.outboundProxy.empty() && account.failedRegistrations == 0) // our test uses no proxy for unregistered account
            {
                  EXPECT_STREQ((const char *)node->properties->children->content, "outboundProxy"); //tag name
                  EXPECT_STREQ((const char *)node->properties->next->children->content, settings.outboundProxy); //tag value
                  node = node->next; // get next data tag
            }
            EXPECT_STREQ((const char *)node->properties->children->content, "protocol"); //tag name
            EXPECT_STREQ((const char *)node->properties->next->children->content, "SIP"); //tag value
            node = node->next; // get next data tag
            EXPECT_STREQ((const char *)node->properties->children->content, "enabled"); //tag name
            if (account.enabled)
               EXPECT_STREQ((const char *)node->properties->next->children->content, "true"); //tag value
            else
               EXPECT_STREQ((const char *)node->properties->next->children->content, "false"); //tag value
            node = node->next; // get next data tag
            EXPECT_STREQ((const char *)node->properties->children->content, "sipSimpleSupported"); //tag name
            EXPECT_STREQ((const char *)node->properties->next->children->content, "true"); //tag value
            node = node->next; // get next data tag
            EXPECT_STREQ((const char *)node->properties->children->content, "signalingTransport"); //tag name
            EXPECT_STREQ((const char *)node->properties->next->children->content, "UDP"); //tag value
         }
      }

      // make sure a deleted account is no longer found in the report unless it has stats
      ASSERT_TRUE(found == (account.acct || account.failedCalls || account.failedRegistrations));

      found = false;
      for (int reportAccountIndex = 1; reportAccountIndex <= MaxAccounts; reportAccountIndex++)
      {
         std::string temp = "/cpc_usage_report/activity_data/account_list/account[";
         temp += std::to_string(reportAccountIndex);
         temp += "]";
         _xmlNode* node = getXmlNode(content.c_str(), temp);
         if (node == NULL)
            continue;      // no more accounts in the report or the node is an empty tag

         // first item is the ID
         if (std::string((const char *)node->parent->properties->children->content) != account.id)
            continue;   // not a matching account

         ASSERT_FALSE(found);   // make sure it's not in the list more than once
         found = true;

         EXPECT_STREQ((const char *)node->properties->children->content, "failedRegistrations"); //tag name
         if (account.failedRegistrations == 0)
            EXPECT_STREQ((const char *)node->properties->next->children->content, "0");
         else
            EXPECT_STRNE((const char *)node->properties->next->children->content, "0");
         node = node->next; // get next data tag
      }

      ASSERT_TRUE(found || account.failedRegistrations == 0 || account.failedRegistrations == HadFailedRegistrations);

      found = false;
      for (int reportAccountIndex = 1; reportAccountIndex <= MaxAccounts; reportAccountIndex++)
      {
         std::string temp = "/cpc_usage_report/activity_data/call_list/call[";
         temp += std::to_string(reportAccountIndex);
         temp += "]";
         _xmlNode* node = getXmlNode(content.c_str(), temp);
         if (node == NULL)
            continue;      // no more accounts in the report or the node is an empty tag

         // first item is the ID
         if (std::string((const char *)node->parent->properties->children->content) != account.id)
            continue;   // not a matching account

         ASSERT_FALSE(found);   // make sure it's not in the list more than once
         found = true;

         node = node->next; // callStart, get next data tag
         node = node->next; // callDuration, get next data tag
         node = node->next; // incoming, get next data tag

         ASSERT_EQ(account.failedCalls, 1);
         EXPECT_STREQ((const char *)node->properties->children->content, "callSuccessful"); //tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "false");
         node = node->next; // get next data tag
      }

      ASSERT_TRUE(found || account.failedCalls == 0);
   }

   TEST_F(Analytics1ModuleTest, MultipleAccounts_NoRegister) {
      std::vector<Analytics1ModuleTest::Account> accounts = 
      {
         // these will be used to place a call
         Account("Sip0"),      // first account must be SIP!
         Account("Sip1"),      // this one will not register
         Account("Xmpp2"),
      };
      assert(accounts.size() <= MaxAccounts);

      // The first test account creates the phone, the analytics module, and the other
      // accounts re-use them so we can have all the accounts appear in the same report
      for (Analytics1ModuleTest::Account& account: accounts)
      {
         Phone* phone = account.name == accounts.begin()->name 
            ? NULL 
            : std::static_pointer_cast<TestAccount>(accounts.begin()->acct)->phone;
         if (account.name.find("Xmpp") != std::string::npos)
         {
            auto acct = std::make_shared<XmppTestAccount>(account.name, Account_Init, "", phone);
            acct->enable(true, NULL, false);  // skip checking for state changes since test framework doesn't support this with multiple accounts
            account.SetAccount(acct);
         }
         else
         {
            std::shared_ptr<TestAccount> acct;
            if (account.name == "Sip1")
            {
               // start off one account as not registering
               acct = std::make_shared<TestAccount>(account.name, Account_NoInit, true, phone);
               acct->config.settings.outboundProxy = "";
               acct->config.settings.domain = "demo.xten.con"; // Intentional typo
               acct->init();
               account.failedRegistrations = 1;
            }
            else
            {
               acct = std::make_shared<TestAccount>(account.name, Account_Init, true, phone);
            }
            acct->enable(false);              // skip checking for state changes since test framework doesn't support this with multiple accounts
            account.SetAccount(acct);
         }

         account.enabled = true;
      }

      //get phone from the first TestAccount Class
      Phone* phone = std::static_pointer_cast<TestAccount>(accounts[0].acct)->phone;
      SipAccountManager* acct = SipAccountManager::getInterface(phone);
      const SipAccountSettings& settings = std::static_pointer_cast<TestAccount>(accounts[0].acct)->config.settings;

      //get the analytics module created by testAccount class
      AnalyticsManager *analyticsManager = std::static_pointer_cast<TestAccount>(accounts[0].acct)->analyticsManager;
      //set the basic stats to the default handler which is always (1)
      //the third paramater is optional and is only used by the test cases due
      //to issue with timing of creating module & account getting configured.
      //In real scenario, the app layer will never pass in the third parameter.
      AnalyticsHandle ah = openModuleWithEmptyServerUrl(analyticsManager);

      std::this_thread::sleep_for(std::chrono::milliseconds(10000));   // wait for account registration failure

      SendAndCheckReportAccounts(analyticsManager, ah, accounts, settings);

      // check the report again to make sure stats are cleared out after sending
      accounts[1].failedRegistrations = HadFailedRegistrations;

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));   // wait for accounts to change

      SendAndCheckReportAccounts(analyticsManager, ah, accounts, settings);

      //close the module
      analyticsManager->close(ah);

      for (std::vector<Analytics1ModuleTest::Account>::reverse_iterator it = accounts.rbegin(); it != accounts.rend(); it++)
      {
         if (it->acct)
         {
            // without these we'll get test failure because the next event is not as expected
            if (it->name.find("Xmpp") != std::string::npos)
               std::static_pointer_cast<XmppTestAccount>(it->acct)->destroy(false);
            else
               std::static_pointer_cast<TestAccount>(it->acct)->disable(true, false);
            it->acct.reset();
         }
      }
   }

   TEST_F(Analytics1ModuleTest, MultipleAccounts_DisabledAccounts) {
      std::vector<Analytics1ModuleTest::Account> accounts = 
      {
         // these will be used to place a call
         Account("Sip0"),      // first account must be SIP!
         // these two will be disabled at some point
         Account("Xmpp1"),
         Account("Sip2"),
         //
         Account("Sip3"),
      };
      assert(accounts.size() <= MaxAccounts);

      // The first test account creates the phone, the analytics module, and the other
      // accounts re-use them so we can have all the accounts appear in the same report
      for (Analytics1ModuleTest::Account& account: accounts)
      {
         Phone* phone = account.name == accounts.begin()->name 
            ? NULL 
            : std::static_pointer_cast<TestAccount>(accounts.begin()->acct)->phone;
         if (account.name.find("Xmpp") != std::string::npos)
         {
            auto acct = std::make_shared<XmppTestAccount>(account.name, Account_Init, "", phone);
            acct->enable(true, NULL, false);  // skip checking for state changes since test framework doesn't support this with multiple accounts
            account.SetAccount(acct);
         }
         else
         {
            auto acct = std::make_shared<TestAccount>(account.name, Account_Init, true, phone);
            acct->enable(false);              // skip checking for state changes since test framework doesn't support this with multiple accounts
            account.SetAccount(acct);
         }

         account.enabled = true;
      }

      //get phone from the first TestAccount Class
      Phone* phone = std::static_pointer_cast<TestAccount>(accounts[0].acct)->phone;
      SipAccountManager* acct = SipAccountManager::getInterface(phone);
      const SipAccountSettings& settings = std::static_pointer_cast<TestAccount>(accounts[0].acct)->config.settings;

      //get the analytics module created by testAccount class
      AnalyticsManager *analyticsManager = std::static_pointer_cast<TestAccount>(accounts[0].acct)->analyticsManager;
      //set the basic stats to the default handler which is always (1)
      //the third paramater is optional and is only used by the test cases due
      //to issue with timing of creating module & account getting configured.
      //In real scenario, the app layer will never pass in the third parameter.
      AnalyticsHandle ah = openModuleWithEmptyServerUrl(analyticsManager);

      std::this_thread::sleep_for(std::chrono::milliseconds(10000));   // wait for accounts to register

      // disable one SIP and one XMPP account and make sure the report reflects the change
      {
         ASSERT_TRUE(accounts[1].name.find("Xmpp") != std::string::npos);
         std::static_pointer_cast<XmppTestAccount>(accounts[1].acct)->disable(false);  // skip checking for state changes since test framework doesn't support this with multiple accounts
         accounts[1].enabled = false;
      }
      {
         ASSERT_TRUE(accounts[2].name.find("Sip") != std::string::npos);
         std::static_pointer_cast<TestAccount>(accounts[2].acct)->disable(false, false);  // skip checking for state changes since test framework doesn't support this with multiple accounts
         accounts[2].enabled = false;
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(10000));   // wait for accounts to deregister

      SendAndCheckReportAccounts(analyticsManager, ah, accounts, settings);

      //close the module
      analyticsManager->close(ah);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      for (std::vector<Analytics1ModuleTest::Account>::reverse_iterator it = accounts.rbegin(); it != accounts.rend(); it++)
      {
         if (it->acct)
         {
            // without these we'll get test failure because the next event is not as expected
            if (it->name.find("Xmpp") != std::string::npos)
               std::static_pointer_cast<XmppTestAccount>(it->acct)->destroy(false);
            else
               std::static_pointer_cast<TestAccount>(it->acct)->disable(true, false);
            it->acct.reset();
         }
      }
   }

   TEST_F(Analytics1ModuleTest, MultipleAccounts_RemovedAccounts) {
      std::vector<Analytics1ModuleTest::Account> accounts = 
      {
         // these will be used to place a call
         Account("Sip0"),      // first account must be SIP!
         // these two will be removed
         Account("Xmpp4"),
         Account("Sip5"),
         //
         Account("Sip7"),
      };
      assert(accounts.size() <= MaxAccounts);

      // The first test account creates the phone, the analytics module, and the other
      // accounts re-use them so we can have all the accounts appear in the same report
      for (Analytics1ModuleTest::Account& account: accounts)
      {
         Phone* phone = account.name == accounts.begin()->name 
            ? NULL 
            : std::static_pointer_cast<TestAccount>(accounts.begin()->acct)->phone;
         if (account.name.find("Xmpp") != std::string::npos)
         {
            auto acct = std::make_shared<XmppTestAccount>(account.name, Account_Init, "", phone);
            acct->enable(true, NULL, false);  // skip checking for state changes since test framework doesn't support this with multiple accounts
            account.SetAccount(acct);
         }
         else
         {
            auto acct = std::make_shared<TestAccount>(account.name, Account_Init, true, phone);
            acct->enable(false);              // skip checking for state changes since test framework doesn't support this with multiple accounts
            account.SetAccount(acct);
         }

         account.enabled = true;
      }

      //get phone from the first TestAccount Class
      Phone* phone = std::static_pointer_cast<TestAccount>(accounts[0].acct)->phone;
      SipAccountManager* acct = SipAccountManager::getInterface(phone);
      const SipAccountSettings& settings = std::static_pointer_cast<TestAccount>(accounts[0].acct)->config.settings;

      //get the analytics module created by testAccount class
      AnalyticsManager *analyticsManager = std::static_pointer_cast<TestAccount>(accounts[0].acct)->analyticsManager;
      //set the basic stats to the default handler which is always (1)
      //the third paramater is optional and is only used by the test cases due
      //to issue with timing of creating module & account getting configured.
      //In real scenario, the app layer will never pass in the third parameter.
      AnalyticsHandle ah = openModuleWithEmptyServerUrl(analyticsManager);

      std::this_thread::sleep_for(std::chrono::milliseconds(10000));   // wait for accounts to register

      // remove one SIP and one XMPP account and make sure the report reflects the change
      {
         ASSERT_TRUE(accounts[1].name.find("Xmpp") != std::string::npos);
         std::static_pointer_cast<XmppTestAccount>(accounts[1].acct)->destroy(false); // without this we'll get test failure because the next event is not as expected
         accounts[1].acct.reset();
         accounts[1].enabled = false;
      }
      {
         ASSERT_TRUE(accounts[2].name.find("Sip") != std::string::npos);
         std::static_pointer_cast<TestAccount>(accounts[2].acct)->disable(true, false);     // skip checking for state changes since test framework doesn't support this with multiple accounts
         std::static_pointer_cast<TestAccount>(accounts[2].acct)->shutdown();               // without this we'll get test failure because the next event is not as expected
         accounts[2].acct.reset();
         accounts[2].enabled = false;
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(10000));   // wait for accounts to change
      
      SendAndCheckReportAccounts(analyticsManager, ah, accounts, settings);

      //close the module
      analyticsManager->close(ah);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      for (std::vector<Analytics1ModuleTest::Account>::reverse_iterator it = accounts.rbegin(); it != accounts.rend(); it++)
      {
         if (it->acct)
         {
            // without these we'll get test failure because the next event is not as expected
            if (it->name.find("Xmpp") != std::string::npos)
               std::static_pointer_cast<XmppTestAccount>(it->acct)->destroy(false);
            else
               std::static_pointer_cast<TestAccount>(it->acct)->disable(true, false);
            it->acct.reset();
         }
      }
   }

   TEST_F(Analytics1ModuleTest, MultipleAccounts_FailedCalls) {
      std::vector<Analytics1ModuleTest::Account> accounts = 
      {
         // these will be used to place a call
         Account("Sip0"),      // first account must be SIP!
         Account("Sip1"),
      };
      assert(accounts.size() <= MaxAccounts);

      // The first test account creates the phone, the analytics module, and the other
      // accounts re-use them so we can have all the accounts appear in the same report
      for (Analytics1ModuleTest::Account& account: accounts)
      {
         Phone* phone = account.name == accounts.begin()->name 
            ? NULL 
            : std::static_pointer_cast<TestAccount>(accounts.begin()->acct)->phone;
         assert(account.name.find("Sip") != std::string::npos);
         auto acct = std::make_shared<TestAccount>(account.name, Account_Init, true, phone);
         acct->enable(false);              // skip checking for state changes since test framework doesn't support this with multiple accounts
         account.SetAccount(acct);

         account.enabled = true;
      }

      //get phone from the first TestAccount Class
      Phone* phone = std::static_pointer_cast<TestAccount>(accounts[0].acct)->phone;
      SipAccountManager* acct = SipAccountManager::getInterface(phone);
      const SipAccountSettings& settings = std::static_pointer_cast<TestAccount>(accounts[0].acct)->config.settings;

      //get the analytics module created by testAccount class
      AnalyticsManager *analyticsManager = std::static_pointer_cast<TestAccount>(accounts[0].acct)->analyticsManager;
      //set the basic stats to the default handler which is always (1)
      //the third paramater is optional and is only used by the test cases due
      //to issue with timing of creating module & account getting configured.
      //In real scenario, the app layer will never pass in the third parameter.
      AnalyticsHandle ah = openModuleWithEmptyServerUrl(analyticsManager);

      std::this_thread::sleep_for(std::chrono::milliseconds(10000));   // wait for accounts to register

      SipConversationHandle call0 = std::static_pointer_cast<TestAccount>(accounts[0].acct)->conversation->createConversation(std::static_pointer_cast<TestAccount>(accounts[0].acct)->handle);
      std::static_pointer_cast<TestAccount>(accounts[0].acct)->conversation->addParticipant(call0, std::static_pointer_cast<TestAccount>(accounts[1].acct)->config.uri());
      std::static_pointer_cast<TestAccount>(accounts[0].acct)->conversation->start(call0);

      std::this_thread::sleep_for(std::chrono::milliseconds(2000));   // wait for call to initiate

      std::static_pointer_cast<TestAccount>(accounts[0].acct)->conversation->end(call0);
      accounts[0].failedCalls = 1;
      accounts[1].failedCalls = 1;  // seems like an incoming call that isn't answered still ends up in the stats

      std::this_thread::sleep_for(std::chrono::milliseconds(2000));   // wait for call to end

      SendAndCheckReportAccounts(analyticsManager, ah, accounts, settings);

      std::this_thread::sleep_for(std::chrono::milliseconds(2000));   // wait for things to settle

      // check the report again to make sure stats are cleared out after sending
      accounts[0].failedCalls = 0;
      accounts[1].failedCalls = 0;
      SendAndCheckReportAccounts(analyticsManager, ah, accounts, settings);

      //close the module
      analyticsManager->close(ah);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      for (std::vector<Analytics1ModuleTest::Account>::reverse_iterator it = accounts.rbegin(); it != accounts.rend(); it++)
      {
         if (it->acct)
         {
            // without these we'll get test failure because the next event is not as expected
            std::static_pointer_cast<TestAccount>(it->acct)->disable(true, false);
            it->acct.reset();
         }
      }
   }

   TEST_F(Analytics1ModuleTest, MultipleAccounts_FailedCallsWithRemovedAccount) {
      std::vector<Analytics1ModuleTest::Account> accounts = 
      {
         // these will be used to place a call
         Account("Sip0"),      // first account must be SIP!
         Account("Sip1"),
      };
      assert(accounts.size() <= MaxAccounts);

      // The first test account creates the phone, the analytics module, and the other
      // accounts re-use them so we can have all the accounts appear in the same report
      for (Analytics1ModuleTest::Account& account: accounts)
      {
         Phone* phone = account.name == accounts.begin()->name 
            ? NULL 
            : std::static_pointer_cast<TestAccount>(accounts.begin()->acct)->phone;
         assert(account.name.find("Sip") != std::string::npos);
         auto acct = std::make_shared<TestAccount>(account.name, Account_Init, true, phone);
         acct->enable(false);              // skip checking for state changes since test framework doesn't support this with multiple accounts
         account.SetAccount(acct);

         account.enabled = true;
      }

      //get phone from the first TestAccount Class
      Phone* phone = std::static_pointer_cast<TestAccount>(accounts[0].acct)->phone;
      SipAccountManager* acct = SipAccountManager::getInterface(phone);
      const SipAccountSettings& settings = std::static_pointer_cast<TestAccount>(accounts[0].acct)->config.settings;

      //get the analytics module created by testAccount class
      AnalyticsManager *analyticsManager = std::static_pointer_cast<TestAccount>(accounts[0].acct)->analyticsManager;
      //set the basic stats to the default handler which is always (1)
      //the third paramater is optional and is only used by the test cases due
      //to issue with timing of creating module & account getting configured.
      //In real scenario, the app layer will never pass in the third parameter.
      AnalyticsHandle ah = openModuleWithEmptyServerUrl(analyticsManager);

      std::this_thread::sleep_for(std::chrono::milliseconds(10000));   // wait for accounts to register

      SipConversationHandle call0 = std::static_pointer_cast<TestAccount>(accounts[0].acct)->conversation->createConversation(std::static_pointer_cast<TestAccount>(accounts[0].acct)->handle);
      std::static_pointer_cast<TestAccount>(accounts[0].acct)->conversation->addParticipant(call0, std::static_pointer_cast<TestAccount>(accounts[1].acct)->config.uri());
      std::static_pointer_cast<TestAccount>(accounts[0].acct)->conversation->start(call0);

      std::this_thread::sleep_for(std::chrono::milliseconds(2000));   // wait for call to initiate

      std::static_pointer_cast<TestAccount>(accounts[0].acct)->conversation->end(call0);
      accounts[0].failedCalls = 1;
      accounts[1].failedCalls = 1;  // seems like an incoming call that isn't answered still ends up in the stats

      std::this_thread::sleep_for(std::chrono::milliseconds(2000));   // wait for call to end

      // remove one SIP account and make sure the report still contains it (needed for callstats)
      {
         ASSERT_TRUE(accounts[1].name.find("Sip") != std::string::npos);
         std::static_pointer_cast<TestAccount>(accounts[1].acct)->disable(false, false);  // skip checking for state changes since test framework doesn't support this with multiple accounts
         accounts[1].enabled = false;
         std::static_pointer_cast<TestAccount>(accounts[1].acct)->shutdown();             // without this we'll get test failure because the next event is not as expected
         accounts[1].acct.reset();
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));   // wait for accounts to change
      
      // check that the removed account is included with the stats
      SendAndCheckReportAccounts(analyticsManager, ah, accounts, settings);

      std::this_thread::sleep_for(std::chrono::milliseconds(2000));   // wait for things to settle

      // check the report again to make sure stats are cleared out after sending, and that the removed account is NOT included
      accounts[0].failedCalls = 0;
      accounts[1].failedCalls = 0;
      SendAndCheckReportAccounts(analyticsManager, ah, accounts, settings);

      //close the module
      analyticsManager->close(ah);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      for (std::vector<Analytics1ModuleTest::Account>::reverse_iterator it = accounts.rbegin(); it != accounts.rend(); it++)
      {
         if (it->acct)
         {
            // without these we'll get test failure because the next event is not as expected
            std::static_pointer_cast<TestAccount>(it->acct)->disable(true, false);
            it->acct.reset();
         }
      }
   }

   TEST_F(Analytics1ModuleTest, MultipleAccounts_RemoveAccountThenAddBack) {
      std::vector<Analytics1ModuleTest::Account> accounts = 
      {
         // these will be used to place a call
         Account("Sip0"),      // first account must be SIP!
         Account("Sip1"),
         Account("Xmpp2"),
      };
      assert(accounts.size() <= MaxAccounts);

      // The first test account creates the phone, the analytics module, and the other
      // accounts re-use them so we can have all the accounts appear in the same report
      for (Analytics1ModuleTest::Account& account: accounts)
      {
         Phone* phone = account.name == accounts.begin()->name 
            ? NULL 
            : std::static_pointer_cast<TestAccount>(accounts.begin()->acct)->phone;
         if (account.name.find("Xmpp") != std::string::npos)
         {
            auto acct = std::make_shared<XmppTestAccount>(account.name, Account_Init, "", phone);
            acct->enable(true, NULL, false);  // skip checking for state changes since test framework doesn't support this with multiple accounts
            account.SetAccount(acct);
         }
         else
         {
            auto acct = std::make_shared<TestAccount>(account.name, Account_Init, true, phone);
            acct->enable(false);              // skip checking for state changes since test framework doesn't support this with multiple accounts
            account.SetAccount(acct);
         }

         account.enabled = true;
      }

      //get phone from the first TestAccount Class
      Phone* phone = std::static_pointer_cast<TestAccount>(accounts[0].acct)->phone;
      SipAccountManager* acct = SipAccountManager::getInterface(phone);
      const SipAccountSettings& settings = std::static_pointer_cast<TestAccount>(accounts[0].acct)->config.settings;

      //get the analytics module created by testAccount class
      AnalyticsManager *analyticsManager = std::static_pointer_cast<TestAccount>(accounts[0].acct)->analyticsManager;
      //set the basic stats to the default handler which is always (1)
      //the third paramater is optional and is only used by the test cases due
      //to issue with timing of creating module & account getting configured.
      //In real scenario, the app layer will never pass in the third parameter.
      AnalyticsHandle ah = openModuleWithEmptyServerUrl(analyticsManager);

      std::this_thread::sleep_for(std::chrono::milliseconds(10000));   // wait for accounts to register

      SendAndCheckReportAccounts(analyticsManager, ah, accounts, settings);

      // remove one SIP and one XMPP account and make sure the report reflects the change
      {
         ASSERT_TRUE(accounts[1].name.find("Sip") != std::string::npos);
         std::static_pointer_cast<TestAccount>(accounts[1].acct)->disable(false, false);  // skip checking for state changes since test framework doesn't support this with multiple accounts
         accounts[1].enabled = false;
         std::static_pointer_cast<TestAccount>(accounts[1].acct)->shutdown();             // without this we'll get test failure because the next event is not as expected
         accounts[1].acct.reset();
      }
      {
         ASSERT_TRUE(accounts[2].name.find("Xmpp") != std::string::npos);
         accounts[2].enabled = false;
         std::static_pointer_cast<XmppTestAccount>(accounts[2].acct)->destroy(false); // without this we'll get test failure because the next event is not as expected
         accounts[2].acct.reset();
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));   // wait for accounts to change
      
      SendAndCheckReportAccounts(analyticsManager, ah, accounts, settings);

      // add back the accounts
      accounts[1].SetAccount(std::make_shared<TestAccount>(accounts[1].name, Account_Init, true, phone));
      accounts[2].SetAccount(std::make_shared<XmppTestAccount>(accounts[2].name, Account_Init, "", phone));

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));   // wait for accounts to change
      
      SendAndCheckReportAccounts(analyticsManager, ah, accounts, settings);

      // enable the accounts
      {
         std::static_pointer_cast<TestAccount>(accounts[1].acct)->enable(false);  // skip checking for state changes since test framework doesn't support this with multiple accounts
         accounts[1].enabled = true;
      }
      {
         std::static_pointer_cast<XmppTestAccount>(accounts[2].acct)->enable(true, NULL, false);  // skip checking for state changes since test framework doesn't support this with multiple accounts
         accounts[2].enabled = true;
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));   // wait for accounts to change
      
      SendAndCheckReportAccounts(analyticsManager, ah, accounts, settings);

      //close the module
      analyticsManager->close(ah);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      for (std::vector<Analytics1ModuleTest::Account>::reverse_iterator it = accounts.rbegin(); it != accounts.rend(); it++)
      {
         if (it->acct)
         {
            // without these we'll get test failure because the next event is not as expected
            if (it->name.find("Xmpp") != std::string::npos)
               std::static_pointer_cast<XmppTestAccount>(it->acct)->destroy(false);
            else
               std::static_pointer_cast<TestAccount>(it->acct)->disable(true, false);
            it->acct.reset();
         }
      }
   }

TEST_F(Analytics1ModuleTest, PttServiceSuccess)
{
   TestAccount alice("alice", Account_Init);

   const int unicastPort = 40001;

   PttIdentity aliceSipIdentity(PushToTalk::PttIdentityType_SIP, "<EMAIL>", "Alice");
   PttIdentity aliceXmppIdentity(PushToTalk::PttIdentityType_XMPP, "<EMAIL>", "Alice");
   cpc::vector<PttIdentity> aliceIdentities;
   aliceIdentities.push_back(aliceSipIdentity);
   aliceIdentities.push_back(aliceXmppIdentity);
   std::string aliceDomain("counterpath.com");
   PushToTalkServiceSettings aliceServiceSettings;
   aliceServiceSettings.localIdentities = aliceIdentities;
   aliceServiceSettings.unicastPort = unicastPort;
   aliceServiceSettings.unicastBindAddress = "127.0.0.1";
   aliceServiceSettings.unicastIpRanges = PttTestHelper::getUnicastIpRange(2);
   aliceServiceSettings.mediaInactivityIntervalSeconds = 5;
   aliceServiceSettings.subscribedChannels.push_back("channel01");
   aliceServiceSettings.subscribedChannels.push_back("channel02");
   aliceServiceSettings.subscribedChannels.push_back("channel03");
   PushToTalkSettingsInternal aliceInternalSettings;
   aliceInternalSettings.outgoingSessionExpiryMsecs = 5000;

   PushToTalkManager* alicePttManager = PushToTalkManager::getInterface(alice.phone);
   PushToTalkServiceHandle alicePttService = alicePttManager->createPttService();
   alicePttManager->setHandler(alicePttService, (PushToTalkHandler*)0xDEADBEEF);
   alicePttManager->configureService(alicePttService, aliceServiceSettings);
   PushToTalkManagerInternal* alicePttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(alicePttManager);

   // Get the analytics module created by testAccount class
   AnalyticsManager* analyticsManager = alice.analyticsManager;

   // Set the basic stats to the default handler which is always (1),
   // the third paramater is optional and is only used by the test cases due
   // to issue with timing of creating module & account getting configured.
   // In real scenario, the app layer will never pass in the third parameter.
   AnalyticsHandle ah = openModuleWithEmptyServerUrl(analyticsManager);
   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   alicePttManagerInternal->setPttInternalSettings(alicePttService, aliceInternalSettings);

   alicePttManager->startService(alicePttService);

   auto aliceEvents = std::async(std::launch::async, [&]()
   {
      assertPttServiceConfigured(alice, alicePttService);
      assertPttServiceStartupCompleteLan(alice, alicePttService);
   });

   waitFor(aliceEvents);

   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   analyticsManager->sendReport(ah);

   // Wait for response from report
   {
      auto sendReponse = std::async(std::launch::async, [&]()
      {
         AnalyticsHandle h;
         OnReportCreatedSuccessEvent evt;
         ASSERT_TRUE(cpcWaitForEvent(alice.analyticsEvents, "AnalyticsHandlerInt::onReportCreatedSuccess", 15000, AlwaysTruePred(), h, evt));
         safeCout("Analytics1ModuleTest::PttServiceTest(): XML: " << evt.content.c_str());

         // Make sure document is not empty
         ASSERT_NE(evt.content, cpc::string());
         // Make sure settings_data tag exists
         bool exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data[1]");
         ASSERT_TRUE(exists);
         // Make sure account_list tag exists
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list[1]");
         ASSERT_TRUE(exists);
         // Make sure at least 2 account tags exists
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list/account[1]");
         ASSERT_TRUE(exists);
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list/account[2]");
         ASSERT_TRUE(exists);

         _xmlNode* node = getXmlNode(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list/account[1]");   // PTT is first account SIP is second
         // Make sure id = SIP*
         ASSERT_EQ(strncmp((const char *)node->parent->properties->children->content, "PTT", strlen("PTT")), 0);
         // Make sure relevant data tags exist and their content is not empty
         EXPECT_STREQ((const char *)node->properties->children->content, "domain"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, aliceDomain.c_str()); // tag value

         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "username"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, aliceServiceSettings.getPrimaryLocalIdentity().c_str()); // tag value

         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "protocol"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "PTT"); // tag value

         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "enabled"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "true"); // tag value

         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "signalingTransport"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
      });
      waitFor(sendReponse);
   }

   alicePttManager->shutdownService(alicePttService);
   assertPttServiceShutdownComplete(alice, alicePttService);

   // Close the module
   analyticsManager->close(ah);
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
}

TEST_F(Analytics1ModuleTest, PttServiceFailure)
{
   TestAccount alice("alice", Account_Init);

   const int unicastPort = 40001;

   PttIdentity aliceIdentity(PushToTalk::PttIdentityType_SIP, "<EMAIL>", "Alice");
   std::string aliceDomain("counterpath.com");
   PushToTalkServiceSettings aliceServiceSettings(aliceIdentity, unicastPort);
   aliceServiceSettings.unicastBindAddress = "*********"; // Use invalid ip address to trigger service failure
   aliceServiceSettings.unicastIpRanges = PttTestHelper::getUnicastIpRange(2);
   aliceServiceSettings.mediaInactivityIntervalSeconds = 5;
   PushToTalkSettingsInternal aliceInternalSettings;
   aliceInternalSettings.outgoingSessionExpiryMsecs = 5000;

   PushToTalkManager* alicePttManager = PushToTalkManager::getInterface(alice.phone);
   PushToTalkServiceHandle alicePttService = alicePttManager->createPttService();
   alicePttManager->setHandler(alicePttService, (PushToTalkHandler*)0xDEADBEEF);
   alicePttManager->configureService(alicePttService, aliceServiceSettings);
   PushToTalkManagerInternal* alicePttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(alicePttManager);

   // Get the analytics module created by testAccount class
   AnalyticsManager* analyticsManager = alice.analyticsManager;

   // Set the basic stats to the default handler which is always (1),
   // the third paramater is optional and is only used by the test cases due
   // to issue with timing of creating module & account getting configured.
   // In real scenario, the app layer will never pass in the third parameter.
   AnalyticsHandle ah = openModuleWithEmptyServerUrl(analyticsManager);
   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   alicePttManagerInternal->setPttInternalSettings(alicePttService, aliceInternalSettings);

   alicePttManager->startService(alicePttService);

   auto aliceEvents = std::async(std::launch::async, [&]()
   {
      assertPttServiceConfigured(alice, alicePttService);

      CPCAPI2::PushToTalk::PushToTalkServiceHandle h;
      CPCAPI2::PushToTalk::PttServiceStatusChangedEvent evt;
      ASSERT_TRUE(alice.pttEvents->expectEvent(__LINE__, "PushToTalkHandler::onPttServiceStatusChanged", 30000, HandleEqualsPred<CPCAPI2::PushToTalk::PushToTalkServiceHandle>(alicePttService), h, evt));
      ASSERT_EQ(CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Connecting, evt.status);
      ASSERT_TRUE(alice.pttEvents->expectEvent(__LINE__, "PushToTalkHandler::onPttServiceStatusChanged", 30000, HandleEqualsPred<CPCAPI2::PushToTalk::PushToTalkServiceHandle>(alicePttService), h, evt));
      ASSERT_EQ(CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Disconnected, evt.status);
      ASSERT_EQ(PttServiceStatusChangedEvent::Reason_ConnectionFailure, evt.reason);
   });

   waitFor(aliceEvents);

   std::this_thread::sleep_for(std::chrono::milliseconds(3000));
   analyticsManager->sendReport(ah);

   // Wait for response from report
   {
      auto sendReponse = std::async(std::launch::async, [&]()
      {
         AnalyticsHandle h;
         OnReportCreatedSuccessEvent evt;
         ASSERT_TRUE(cpcWaitForEvent(alice.analyticsEvents, "AnalyticsHandlerInt::onReportCreatedSuccess", 15000, AlwaysTruePred(), h, evt));
         safeCout("Analytics1ModuleTest::PttServiceTest(): XML: " << evt.content.c_str());

         // Make sure document is not empty
         ASSERT_NE(evt.content, cpc::string());
         // Make sure settings_data tag exists
         bool exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data[1]");
         ASSERT_TRUE(exists);
         // Make sure account_list tag exists
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list[1]");
         ASSERT_TRUE(exists);
         // Make sure at least 2 account tags exists
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list/account[1]");
         ASSERT_TRUE(exists);
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list/account[2]");
         ASSERT_TRUE(exists);

         _xmlNode* node = getXmlNode(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list/account[1]");   // PTT is first account SIP is second
         // Make sure id = SIP*
         ASSERT_EQ(strncmp((const char *)node->parent->properties->children->content, "PTT", strlen("PTT")), 0);
         // Make sure relevant data tags exist and their content is not empty
         EXPECT_STREQ((const char *)node->properties->children->content, "domain"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, aliceDomain.c_str()); // tag value

         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "username"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, aliceServiceSettings.getPrimaryLocalIdentity().c_str()); // tag value

         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "protocol"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "PTT"); // tag value

         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "enabled"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "false"); // tag value

         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "signalingTransport"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
      });
      waitFor(sendReponse);
   }

   alicePttManager->shutdownService(alicePttService);
   assertPttServiceShutdownComplete(alice, alicePttService);

   // Close the module
   analyticsManager->close(ah);
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
}

TEST_F(Analytics1ModuleTest, PttLanCallSuccess)
{
   TestAccount alice("alice", Account_Init);
   TestAccount bob("bob", Account_Init);

   const int unicastPort = 40001;

   PttIdentity aliceSipIdentity(PushToTalk::PttIdentityType_SIP, "<EMAIL>", "Alice");
   PttIdentity aliceXmppIdentity(PushToTalk::PttIdentityType_XMPP, "<EMAIL>", "Alice");
   cpc::vector<PttIdentity> aliceIdentities;
   aliceIdentities.push_back(aliceSipIdentity);
   aliceIdentities.push_back(aliceXmppIdentity);
   std::string aliceDomain("counterpath.com");
   PushToTalkServiceSettings aliceServiceSettings;
   aliceServiceSettings.localIdentities = aliceIdentities;
   aliceServiceSettings.unicastPort = unicastPort;
   aliceServiceSettings.unicastBindAddress = "127.0.0.1";
   aliceServiceSettings.unicastIpRanges = PttTestHelper::getUnicastIpRange(2);
   aliceServiceSettings.mediaInactivityIntervalSeconds = 5;
   aliceServiceSettings.subscribedChannels.push_back("channel01");
   aliceServiceSettings.subscribedChannels.push_back("channel02");
   aliceServiceSettings.subscribedChannels.push_back("channel03");
   PushToTalkSettingsInternal aliceInternalSettings;
   aliceInternalSettings.outgoingSessionExpiryMsecs = 5000;

   PttIdentity bobSipIdentity(PushToTalk::PttIdentityType_SIP, "<EMAIL>", "Bob");
   PttIdentity bobXmppIdentity(PushToTalk::PttIdentityType_XMPP, "<EMAIL>", "Bob");
   cpc::vector<PttIdentity> bobIdentities;
   bobIdentities.push_back(bobSipIdentity);
   bobIdentities.push_back(bobXmppIdentity);
   std::string bobDomain("counterpath.com");
   PushToTalkServiceSettings bobServiceSettings;
   bobServiceSettings.localIdentities = bobIdentities;
   bobServiceSettings.unicastPort = unicastPort;
   bobServiceSettings.unicastBindAddress = "*********";
   bobServiceSettings.unicastIpRanges = PttTestHelper::getUnicastIpRange(2);
   bobServiceSettings.mediaInactivityIntervalSeconds = 5;
   bobServiceSettings.subscribedChannels.push_back("channel01");
   bobServiceSettings.subscribedChannels.push_back("channel02");
   bobServiceSettings.subscribedChannels.push_back("channel03");
   bobServiceSettings.subscribedChannels.push_back("Sales");
   bobServiceSettings.subscribedChannels.push_back("Support");
   bobServiceSettings.subscribedChannels.push_back("Electronic");
   bobServiceSettings.subscribedChannels.push_back("Automotive");
   bobServiceSettings.subscribedChannels.push_back("Office");
   PushToTalkSettingsInternal bobInternalSettings;
   bobInternalSettings.outgoingSessionExpiryMsecs = 5000;

   PushToTalkManager* alicePttManager = PushToTalkManager::getInterface(alice.phone);
   PushToTalkServiceHandle alicePttService = alicePttManager->createPttService();
   alicePttManager->setHandler(alicePttService, (PushToTalkHandler*)0xDEADBEEF);
   alicePttManager->configureService(alicePttService, aliceServiceSettings);
   PushToTalkManagerInternal* alicePttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(alicePttManager);

   PushToTalkManager* bobPttManager = PushToTalkManager::getInterface(bob.phone);
   PushToTalkServiceHandle bobPttService = bobPttManager->createPttService();
   bobPttManager->setHandler(bobPttService, (PushToTalkHandler*)0xDEADBEEF);
   bobPttManager->configureService(bobPttService, bobServiceSettings);
   PushToTalkManagerInternal* bobPttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(bobPttManager);

   // Get the analytics module created by testAccount class
   AnalyticsManager* aliceAnalyticsManager = alice.analyticsManager;
   AnalyticsManager* bobAnalyticsManager = bob.analyticsManager;

   // Set the basic stats to the default handler which is always (1),
   // the third paramater is optional and is only used by the test cases due
   // to issue with timing of creating module & account getting configured.
   // In real scenario, the app layer will never pass in the third parameter.
   AnalyticsHandle ahAlice = openModuleWithEmptyServerUrl(aliceAnalyticsManager);
   AnalyticsHandle ahBob = openModuleWithEmptyServerUrl(bobAnalyticsManager);
   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   alicePttManagerInternal->setPttInternalSettings(alicePttService, aliceInternalSettings);
   bobPttManagerInternal->setPttInternalSettings(bobPttService, bobInternalSettings);

   alicePttManager->startService(alicePttService);
   bobPttManager->startService(bobPttService);

   auto aliceEvents = std::async(std::launch::async, [&]()
   {
      assertPttServiceConfigured(alice, alicePttService);
      assertPttServiceStartupCompleteLan(alice, alicePttService);
   });

   auto bobEvents = std::async(std::launch::async, [&]()
   {
      assertPttServiceConfigured(bob, bobPttService);
      assertPttServiceStartupCompleteLan(bob, bobPttService);
   });

   waitFor2(aliceEvents, bobEvents);

   {
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));

      int count = 10;

      for (int index = 0; index < count; ++index)
      {
         auto aliceEvents2 = std::async(std::launch::async, [&]()
         {
            PushToTalkSessionHandle alicePtt = alicePttManager->createPttSession(alicePttService);
            if (index % 2 == 0)
            {
               alicePttManager->setChannel(alicePtt, "Support");
            }
            else
            {
               alicePttManager->addRecipient(alicePtt, bobSipIdentity);
            }
            alicePttManager->start(alicePtt);
            assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Initiated);
            assertPttClientOfferEvent(alice, alicePtt);
            assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Active);

            // Initiate talk spurt
            alicePttManager->startTalkSpurt(alicePtt);
            assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Talking);
            std::this_thread::sleep_for(std::chrono::milliseconds(4000));
            alicePttManager->endTalkSpurt(alicePtt);
            assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Active);

            alicePttManager->end(alicePtt);
            assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Ending);
            assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Idle);
         });

         auto bobEvents2 = std::async(std::launch::async, [&]()
         {
            PushToTalkSessionHandle bobPtt = 0;
            assertPttIncomingCallEx2(bob, bobPtt, aliceSipIdentity.userName, aliceSipIdentity.displayName, bobPttService);
            bobPttManager->accept(bobPtt);
            assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Initiated);
            assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Active);
            assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Talking);
            assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Ending);
            assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Idle);
         });

         waitFor2(aliceEvents2, bobEvents2);
         std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      }
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   aliceAnalyticsManager->sendReport(ahAlice);

   // Wait for response from report
   {
      auto sendReponse = std::async(std::launch::async, [&]()
      {
         AnalyticsHandle h;
         OnReportCreatedSuccessEvent evt;
         ASSERT_TRUE(cpcWaitForEvent(alice.analyticsEvents, "AnalyticsHandlerInt::onReportCreatedSuccess", 15000, AlwaysTruePred(), h, evt));
         safeCout("Analytics1ModuleTest::PttServiceTest(): XML: " << evt.content.c_str());

         // Make sure document is not empty
         ASSERT_NE(evt.content, cpc::string());
         // Make sure settings_data tag exists
         bool exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data[1]");
         ASSERT_TRUE(exists);
         // Make sure account_list tag exists
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list[1]");
         ASSERT_TRUE(exists);
         // Make sure at least 2 account tags exists
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list/account[1]");
         ASSERT_TRUE(exists);
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list/account[2]");
         ASSERT_TRUE(exists);

         _xmlNode* node = getXmlNode(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list/account[1]");   // PTT is first account SIP is second
         // Make sure id = SIP*
         ASSERT_EQ(strncmp((const char *)node->parent->properties->children->content, "PTT", strlen("PTT")), 0);
         // Make sure relevant data tags exist and their content is not empty
         EXPECT_STREQ((const char *)node->properties->children->content, "domain"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, aliceDomain.c_str()); // tag value

         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "username"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, aliceServiceSettings.getPrimaryLocalIdentity().c_str()); // tag value

         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "protocol"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "PTT"); // tag value

         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "enabled"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "true"); // tag value

         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "signalingTransport"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
      });
      waitFor(sendReponse);
   }

   bobAnalyticsManager->sendReport(ahBob);

   // Wait for response from report
   {
      auto sendReponse = std::async(std::launch::async, [&]()
      {
         AnalyticsHandle h;
         OnReportCreatedSuccessEvent evt;
         ASSERT_TRUE(cpcWaitForEvent(bob.analyticsEvents, "AnalyticsHandlerInt::onReportCreatedSuccess", 15000, AlwaysTruePred(), h, evt));
         safeCout("Analytics1ModuleTest::PttServiceTest(): XML: " << evt.content.c_str());

         // Make sure document is not empty
         ASSERT_NE(evt.content, cpc::string());
         // Make sure settings_data tag exists
         bool exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data[1]");
         ASSERT_TRUE(exists);
         // Make sure account_list tag exists
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list[1]");
         ASSERT_TRUE(exists);
         // Make sure at least 2 account tags exists
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list/account[1]");
         ASSERT_TRUE(exists);
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list/account[2]");
         ASSERT_TRUE(exists);

         _xmlNode* node = getXmlNode(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list/account[1]");   // PTT is first account SIP is second
         // Make sure id = SIP*
         ASSERT_EQ(strncmp((const char *)node->parent->properties->children->content, "PTT", strlen("PTT")), 0);
         // Make sure relevant data tags exist and their content is not empty
         EXPECT_STREQ((const char *)node->properties->children->content, "domain"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, bobDomain.c_str()); // tag value

         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "username"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, bobServiceSettings.getPrimaryLocalIdentity().c_str()); // tag value

         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "protocol"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "PTT"); // tag value

         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "enabled"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "true"); // tag value

         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "signalingTransport"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
      });
      waitFor(sendReponse);
   }

   alicePttManager->shutdownService(alicePttService);
   assertPttServiceShutdownComplete(alice, alicePttService);
   bobPttManager->shutdownService(bobPttService);
   assertPttServiceShutdownComplete(bob, bobPttService);

   // Close the module
   aliceAnalyticsManager->close(ahAlice);
   bobAnalyticsManager->close(ahBob);
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
}

void Analytics1ModuleTest::verifyPttReport(TestAccount& account, PushToTalkServiceSettings& settings, int callCount, std::vector<bool>& incomingCalls, std::vector<std::string>& channels, std::vector<int>& responsesReceived)
{
   std::string domain("counterpath.com");
   AnalyticsHandle h;
   OnReportCreatedSuccessEvent evt;
   ASSERT_TRUE(cpcWaitForEvent(account.analyticsEvents, "AnalyticsHandlerInt::onReportCreatedSuccess", 15000, AlwaysTruePred(), h, evt)) << " failure for " << settings.getPrimaryLocalIdentity() << " account";
   safeCout("Analytics1ModuleTest::PttServiceTest(): XML: " << evt.content.c_str());

   // Make sure document is not empty
   ASSERT_NE(evt.content, cpc::string()) << " failure for " << settings.getPrimaryLocalIdentity() << " account";
   // Make sure settings_data tag exists
   bool exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data[1]");
   ASSERT_TRUE(exists) << " failure for " << settings.getPrimaryLocalIdentity() << " account";
   // Make sure account_list tag exists
   exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list[1]");
   ASSERT_TRUE(exists) << " failure for " << settings.getPrimaryLocalIdentity() << " account";
   // Make sure at least 2 account tags exists
   exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list/account[1]");
   ASSERT_TRUE(exists) << " failure for " << settings.getPrimaryLocalIdentity() << " account";
   exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list/account[2]");
   ASSERT_TRUE(exists) << " failure for " << settings.getPrimaryLocalIdentity() << " account";

   _xmlNode* node = getXmlNode(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list/account[1]");   // PTT is first account SIP is second
   ASSERT_EQ(strncmp((const char *)node->parent->properties->children->content, "PTT", strlen("PTT")), 0) << " failure for " << settings.getPrimaryLocalIdentity() << " account";

   // Make sure relevant data tags exist and their content is not empty
   EXPECT_STREQ((const char *)node->properties->children->content, "domain") << " channel validation failure for " << settings.getPrimaryLocalIdentity() << " account"; // tag name
   EXPECT_STREQ((const char *)node->properties->next->children->content, domain.c_str()) << " failure for " << settings.getPrimaryLocalIdentity() << " account"; // tag value

   node = node->next; // get next data tag
   EXPECT_STREQ((const char *)node->properties->children->content, "username") << " failure for " << settings.getPrimaryLocalIdentity() << " account"; // tag name
   EXPECT_STREQ((const char *)node->properties->next->children->content, settings.getPrimaryLocalIdentity().c_str()) << " failure for " << settings.getPrimaryLocalIdentity() << " account"; // tag value

   node = node->next; // get next data tag
   EXPECT_STREQ((const char *)node->properties->children->content, "protocol") << " failure for " << settings.getPrimaryLocalIdentity() << " account"; // tag name
   EXPECT_STREQ((const char *)node->properties->next->children->content, "PTT") << " failure for " << settings.getPrimaryLocalIdentity() << " account"; // tag value

   node = node->next; // get next data tag
   EXPECT_STREQ((const char *)node->properties->children->content, "enabled") << " failure for " << settings.getPrimaryLocalIdentity() << " account"; // tag name
   EXPECT_STREQ((const char *)node->properties->next->children->content, "true") << " failure for " << settings.getPrimaryLocalIdentity() << " account"; // tag value

   node = node->next; // get next data tag
   EXPECT_STREQ((const char *)node->properties->children->content, "signalingTransport") << " failure for " << settings.getPrimaryLocalIdentity() << " account"; // tag name
   EXPECT_STRNE((const char *)node->properties->next->children->content, "") << " failure for " << settings.getPrimaryLocalIdentity() << " account"; // tag value

   node = node->next; // get next data tag
   EXPECT_STREQ((const char *)node->properties->children->content, "serviceType") << " failure for " << settings.getPrimaryLocalIdentity() << " account"; // tag name
   EXPECT_STREQ((const char *)node->properties->next->children->content, "WAN") << " failure for " << settings.getPrimaryLocalIdentity() << " account"; // tag value

   exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data[1]");
   ASSERT_TRUE(exists) << " failure for " << settings.getPrimaryLocalIdentity() << " account";

   ASSERT_EQ(callCount, incomingCalls.size()) << " failure for " << settings.getPrimaryLocalIdentity() << " account";
   ASSERT_EQ(callCount, channels.size()) << " failure for " << settings.getPrimaryLocalIdentity() << " account";
   ASSERT_EQ(callCount, responsesReceived.size()) << " failure for " << settings.getPrimaryLocalIdentity() << " account";
   for (int index = 0; index < callCount; ++index)
   {
      std::stringstream ss1;
      ss1 << "/cpc_usage_report/activity_data/call_list[" << 1 << "]";
      exists = getXmlNodeExists(evt.content.c_str(), ss1.str());
      ASSERT_TRUE(exists) << " call-list node validation failure for " << settings.getPrimaryLocalIdentity() << " on channel: " << channels[index] << " call: " << (index + 1) << " query: " << ss1.str();;

      std::stringstream ss2;
      ss2 << "/cpc_usage_report/activity_data/call_list/call[" << (index + 1) << "]";
      exists = getXmlNodeExists(evt.content.c_str(), ss2.str());
      ASSERT_TRUE(exists) << " call node validation failure for " << settings.getPrimaryLocalIdentity() << " on channel: " << channels[index] << " call: " << (index + 1) << " query: " << ss2.str();

      node = getXmlNode(evt.content.c_str(), ss2.str());
      ASSERT_EQ(strncmp((const char *)node->parent->properties->children->content, "PTT", strlen("PTT")), 0) << " call-node access validation failure for " << settings.getPrimaryLocalIdentity() << " on channel: " << channels[index] << " call: " << (index + 1) << " query: " << ss2.str();

      // Make sure relevant data tags exist and their content is not empty
      EXPECT_STREQ((const char *)node->properties->children->content, "callStart") << " call-start tag validation failure for " << settings.getPrimaryLocalIdentity() << " on channel: " << channels[index] << " call: " << (index + 1); // tag name
      EXPECT_STRNE((const char *)node->properties->next->children->content, "") << " call-start validation failure for " << settings.getPrimaryLocalIdentity() << " on channel: " << channels[index] << " call: " << (index + 1); // tag value

      node = node->next; // get next data tag
      EXPECT_STREQ((const char *)node->properties->children->content, "callDuration") << " call-duration tag validation failure for " << settings.getPrimaryLocalIdentity() << " on channel: " << channels[index] << " call: " << (index + 1); // tag name
      EXPECT_STRNE((const char *)node->properties->next->children->content, "") << " call-duration validation failure for " << settings.getPrimaryLocalIdentity() << " on channel: " << channels[index] << " call: " << (index + 1); // tag value

      std::stringstream ss3;
      ss3 << (incomingCalls[index] ? "true" : "false");
      node = node->next; // get next data tag
      EXPECT_STREQ((const char *)node->properties->children->content, "incoming") << " incoming tag validation failure for " << settings.getPrimaryLocalIdentity() << " on channel: " << channels[index] << " call: " << (index + 1) << " query: " << ss3.str(); // tag name
      EXPECT_STREQ((const char *)node->properties->next->children->content, ss3.str().c_str()) << " incoming validation failure for " << settings.getPrimaryLocalIdentity() << " on channel: " << channels[index] << " call: " << (index + 1) << " query: " << ss3.str(); // tag value

      node = node->next; // get next data tag
      EXPECT_STREQ((const char *)node->properties->children->content, "callSuccessful") << " call-successful tag validation failure for " << settings.getPrimaryLocalIdentity() << " on channel: " << channels[index] << " call: " << (index + 1); // tag name
      EXPECT_STREQ((const char *)node->properties->next->children->content, "true") << " call-successful validation failure for " << settings.getPrimaryLocalIdentity() << " on channel: " << channels[index] << " call: " << (index + 1); // tag value

      node = node->next; // get next data tag
      EXPECT_STREQ((const char *)node->properties->children->content, "audioInCodec") << " audio-in-codec tag validation failure for " << settings.getPrimaryLocalIdentity() << " on channel: " << channels[index] << " call: " << (index + 1); // tag name
      EXPECT_STRNE((const char *)node->properties->next->children->content, "") << " audio-in-codec validation failure for " << settings.getPrimaryLocalIdentity() << " on channel: " << channels[index] << " call: " << (index + 1); // tag value

      node = node->next; // get next data tag
      EXPECT_STREQ((const char *)node->properties->children->content, "audioOutCodec") << " audio-out-codec tag validation failure for " << settings.getPrimaryLocalIdentity() << " on channel: " << channels[index] << " call: " << (index + 1); // tag name
      EXPECT_STRNE((const char *)node->properties->next->children->content, "") << " audio-out-codec validation failure for " << settings.getPrimaryLocalIdentity() << " on channel: " << channels[index] << " call: " << (index + 1); // tag value

      node = node->next; // get next data tag
      EXPECT_STREQ((const char *)node->properties->children->content, "mediaEncryption") << " media-encryption tag validation failure for " << settings.getPrimaryLocalIdentity() << " on channel: " << channels[index] << " call: " << (index + 1); // tag name
      EXPECT_STREQ((const char *)node->properties->next->children->content, "SRTP") << " media-encryption validation failure for " << settings.getPrimaryLocalIdentity() << " on channel: " << channels[index] << " call: " << (index + 1); // tag value

#if !(defined(__linux__) && !defined(ANDROID))
      if (!TestEnvironmentConfig::dockerContainerized()) // UNIT_TEST_WIN_DOCKER_SKIP this is another scenario where we won't have a "usbDevice" entry
      {
         node = node->next; // get next data tag
         if (strcmp((const char*)node->properties->children->content, "usbDevice") == 0)
         {
            EXPECT_STREQ((const char *)node->properties->children->content, "usbDevice") << " usb-device tag validation failure for " << settings.getPrimaryLocalIdentity() << " on channel: " << channels[index] << " call: " << (index + 1); // tag name
            EXPECT_STRNE((const char *)node->properties->next->children->content, "") << " usb-device validation failure for " << settings.getPrimaryLocalIdentity() << " on channel: " << channels[index] << " call: " << (index + 1); // tag value
         }
      }
#endif

      std::stringstream ss4;
      ss4 << channels[index];
      node = node->next; // get next data tag
      EXPECT_STREQ((const char *)node->properties->children->content, "channel") << " channel tag failure for " << settings.getPrimaryLocalIdentity() << " on channel: " << channels[index] << " call: " << (index + 1) << " query: " << ss4.str(); // tag name
      EXPECT_STREQ((const char *)node->properties->next->children->content, ss4.str().c_str()) << " channel validation failure for " << settings.getPrimaryLocalIdentity() << " on channel: " << channels[index] << " call: " << (index + 1) << " query: " << ss4.str(); // tag value

      node = node->next; // get next data tag
      EXPECT_STREQ((const char *)node->properties->children->content, "serviceType") << " service-type tag validation failure for " << settings.getPrimaryLocalIdentity() << " on channel: " << channels[index] << " call: " << (index + 1); // tag name
      EXPECT_STREQ((const char *)node->properties->next->children->content, "WAN") << " service-type validation failure for " << settings.getPrimaryLocalIdentity() << " on channel: " << channels[index] << " call: " << (index + 1); // tag value

      node = node->next; // get next data tag
      EXPECT_STREQ((const char *)node->properties->children->content, "remoteIdentity") << " remote-identity tag validation failure for " << settings.getPrimaryLocalIdentity() << " on channel: " << channels[index] << " call: " << (index + 1); // tag name
      EXPECT_STRNE((const char *)node->properties->next->children->content, "") << " remote-identity validation failure for " << settings.getPrimaryLocalIdentity() << " on channel: " << channels[index] << " call: " << (index + 1); // tag value

      std::stringstream ss5;
      ss5 << responsesReceived[index];
      node = node->next; // get next data tag
      EXPECT_STREQ((const char *)node->properties->children->content, "responsesReceived") << " response-received tag validation failure for " << settings.getPrimaryLocalIdentity() << " on channel: " << channels[index] << " call: " << (index + 1) << " query: " << ss5.str(); // tag name
      EXPECT_STREQ((const char *)node->properties->next->children->content, ss5.str().c_str()) << " response-received validation failure for " << settings.getPrimaryLocalIdentity() << " on channel: " << channels[index] << " call: " << (index + 1) << " query: " << ss5.str(); // tag value

      node = node->next; // get next data tag
      EXPECT_STREQ((const char *)node->properties->children->content, "oneWayAudio") << " one-way-audio tag validation failure for " << settings.getPrimaryLocalIdentity() << " on channel: " << channels[index] << " call: " << (index + 1); // tag name
      EXPECT_STREQ((const char *)node->properties->next->children->content, "true") << " one-way-audio validation failure for " << settings.getPrimaryLocalIdentity() << " on channel: " << channels[index] << " call: " << (index + 1); // tag value

      node = node->next; // get next data tag
      EXPECT_STREQ((const char *)node->properties->children->content, "dataNetworkType") << " network-type tag validation failure for " << settings.getPrimaryLocalIdentity() << " on channel: " << channels[index] << " call: " << (index + 1); // tag name
      EXPECT_STREQ((const char *)node->properties->next->children->content, "WIFI") << " network-type validation failure for " << settings.getPrimaryLocalIdentity() << " on channel: " << channels[index] << " call: " << (index + 1); // tag value
   }
}

#if !defined(ANDROID)
TEST_F(Analytics1ModuleTest, PttWanCallSuccess)
{
   const int unicastPort = 40001;
   std::string domain("counterpath.com");
   CPCAPI2::JsonApi::LoginResultEvent loginResult; loginResult.success = true;
   cpc::vector<cpc::string> permissions; permissions.push_back("*");

   std::atomic_bool loginComplete(false);
   std::atomic_bool rootConfDetailsHandlerActive(true);
   std::shared_ptr<TestAccount> authServer = NULL;
   std::shared_ptr<TestAccount> confServer = NULL;
   CPCAPI2::ConferenceBridge::ConferenceDetailsResult rootConfDetails;
   resip::Condition rootDetailsReady; resip::Mutex mutex;

   auto loginEvent = std::async(std::launch::async, [&]()
   {
      PttTestHelper::runWanServer(authServer, confServer, rootConfDetails, loginComplete, rootConfDetailsHandlerActive, rootDetailsReady);
   });

   rootDetailsReady.wait(mutex);
   mutex.unlock();
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   PttIdentity aliceIdentity(PushToTalk::PttIdentityType_XMPP, "<EMAIL>", "Alice");
   PushToTalkServiceSettings aliceServiceSettings(aliceIdentity, unicastPort);
   aliceServiceSettings.serviceType = PushToTalk::PttServiceType_WAN;
   aliceServiceSettings.unicastBindAddress = "127.0.0.1";
   aliceServiceSettings.authServiceAddress = "https://127.0.0.1:18084";
   aliceServiceSettings.confServiceAddress = rootConfDetails.conferenceJoinUrl; // "https://127.0.0.1:18082/jsonApi";
   aliceServiceSettings.username = "<EMAIL>";
   aliceServiceSettings.password = "1234";
   aliceServiceSettings.authServiceApiKey = "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiKzxgDrW3u9+XzGN4r/GI5o+BMtty+63OT4lwEgFlkpedq5FY3eCzI4lB1WXDVip0OpCM1zYoCh6mqi7KaS1Vg==";
   aliceServiceSettings.ignoreCertVerification = true;

   PttIdentity bobIdentity(PushToTalk::PttIdentityType_XMPP, "<EMAIL>", "Bob");
   PushToTalkServiceSettings bobServiceSettings(bobIdentity, unicastPort);
   bobServiceSettings.serviceType = PushToTalk::PttServiceType_WAN;
   bobServiceSettings.unicastBindAddress = "*********";
   bobServiceSettings.authServiceAddress = "https://127.0.0.1:18084";
   bobServiceSettings.confServiceAddress = rootConfDetails.conferenceJoinUrl; // "https://127.0.0.1:18082/jsonApi";
   bobServiceSettings.username = "<EMAIL>";
   bobServiceSettings.password = "1234";
   bobServiceSettings.authServiceApiKey = "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiKzxgDrW3u9+XzGN4r/GI5o+BMtty+63OT4lwEgFlkpedq5FY3eCzI4lB1WXDVip0OpCM1zYoCh6mqi7KaS1Vg==";
   bobServiceSettings.ignoreCertVerification = true;

   PttIdentity maxIdentity(PushToTalk::PttIdentityType_XMPP, "<EMAIL>", "Max");
   PushToTalkServiceSettings maxServiceSettings(maxIdentity, unicastPort);
   maxServiceSettings.serviceType = PushToTalk::PttServiceType_WAN;
   maxServiceSettings.unicastBindAddress = "*********";
   maxServiceSettings.authServiceAddress = "https://127.0.0.1:18084";
   maxServiceSettings.confServiceAddress = rootConfDetails.conferenceJoinUrl; // "https://127.0.0.1:18082/jsonApi";
   maxServiceSettings.username = "<EMAIL>";
   maxServiceSettings.password = "1234";
   maxServiceSettings.authServiceApiKey = "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiKzxgDrW3u9+XzGN4r/GI5o+BMtty+63OT4lwEgFlkpedq5FY3eCzI4lB1WXDVip0OpCM1zYoCh6mqi7KaS1Vg==";
   maxServiceSettings.ignoreCertVerification = true;

   PttIdentity tasiaIdentity(PushToTalk::PttIdentityType_XMPP, "<EMAIL>", "Tasia");
   PushToTalkServiceSettings tasiaServiceSettings(tasiaIdentity, unicastPort);
   tasiaServiceSettings.serviceType = PushToTalk::PttServiceType_WAN;
   tasiaServiceSettings.unicastBindAddress = "*********";
   tasiaServiceSettings.authServiceAddress = "https://127.0.0.1:18084";
   tasiaServiceSettings.confServiceAddress = rootConfDetails.conferenceJoinUrl; // "https://127.0.0.1:18082/jsonApi";
   tasiaServiceSettings.username = "<EMAIL>";
   tasiaServiceSettings.password = "1234";
   tasiaServiceSettings.authServiceApiKey = "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiKzxgDrW3u9+XzGN4r/GI5o+BMtty+63OT4lwEgFlkpedq5FY3eCzI4lB1WXDVip0OpCM1zYoCh6mqi7KaS1Vg==";
   tasiaServiceSettings.ignoreCertVerification = true;

   PushToTalkSettingsInternal aliceInternalSettings;
   aliceInternalSettings.outgoingConnectedMsecs = 5000;
   aliceInternalSettings.outgoingConnectionErrorMsecs = 10000;
   aliceInternalSettings.outgoingSessionExpiryMsecs = 5000;
   PushToTalkSettingsInternal bobInternalSettings;
   bobInternalSettings.outgoingSessionExpiryMsecs = 5000;
   PushToTalkSettingsInternal maxInternalSettings;
   maxInternalSettings.outgoingSessionExpiryMsecs = 5000;
   PushToTalkSettingsInternal tasiaInternalSettings;
   tasiaInternalSettings.outgoingSessionExpiryMsecs = 5000;

   // Alice is the PTT sender/initiator
   TestAccount alice("alice", Account_Init);

   PushToTalkManager* alicePttManager = PushToTalkManager::getInterface(alice.phone);
   PushToTalkServiceHandle alicePttService = alicePttManager->createPttService();
   alicePttManager->setHandler(alicePttService, (PushToTalkHandlerInternal*)0xDEADBEEF);
   alicePttManager->configureService(alicePttService, aliceServiceSettings);
   PushToTalkManagerInternal* alicePttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(alicePttManager);

   // Bob is a PTT recipient
   TestAccount bob("bob", Account_Init);
   PushToTalkManager* bobPttManager = PushToTalkManager::getInterface(bob.phone);
   PushToTalkServiceHandle bobPttService = bobPttManager->createPttService();
   bobPttManager->setHandler(bobPttService, (PushToTalkHandlerInternal*)0xDEADBEEF);
   bobPttManager->configureService(bobPttService, bobServiceSettings);
   PushToTalkManagerInternal* bobPttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(bobPttManager);

   // Max is a PTT recipient
   TestAccount max("max", Account_Init);
   PushToTalkManager* maxPttManager = PushToTalkManager::getInterface(max.phone);
   PushToTalkServiceHandle maxPttService = maxPttManager->createPttService();
   maxPttManager->setHandler(maxPttService, (PushToTalkHandlerInternal*)0xDEADBEEF);
   maxPttManager->configureService(maxPttService, maxServiceSettings);
   PushToTalkManagerInternal* maxPttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(maxPttManager);

   // Tasia is a PTT recipient
   TestAccount tasia("tasia", Account_Init);
   PushToTalkManager* tasiaPttManager = PushToTalkManager::getInterface(tasia.phone);
   PushToTalkServiceHandle tasiaPttService = tasiaPttManager->createPttService();
   tasiaPttManager->setHandler(tasiaPttService, (PushToTalkHandlerInternal*)0xDEADBEEF);
   tasiaPttManager->configureService(tasiaPttService, tasiaServiceSettings);
   PushToTalkManagerInternal* tasiaPttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(tasiaPttManager);

   alicePttManagerInternal->setPttInternalSettings(alicePttService, aliceInternalSettings);
   bobPttManagerInternal->setPttInternalSettings(bobPttService, bobInternalSettings);
   maxPttManagerInternal->setPttInternalSettings(maxPttService, maxInternalSettings);
   tasiaPttManagerInternal->setPttInternalSettings(tasiaPttService, tasiaInternalSettings);

   cpc::vector<cpc::string> aliceChannelSubscriptions;
   aliceChannelSubscriptions.push_back("channel01");
   aliceChannelSubscriptions.push_back("channel02");
   aliceChannelSubscriptions.push_back("channel03");

   cpc::vector<cpc::string> bobChannelSubscriptions;
   bobChannelSubscriptions.push_back("channel01");
   bobChannelSubscriptions.push_back("channel02");
   bobChannelSubscriptions.push_back("channel03");

   cpc::vector<cpc::string> maxChannelSubscriptions;
   maxChannelSubscriptions.push_back("channel01");
   maxChannelSubscriptions.push_back("channel02");

   cpc::vector<cpc::string> tasiaChannelSubscriptions;
   tasiaChannelSubscriptions.push_back("channel01");

   alicePttManager->setChannelSubscriptions(alicePttService, aliceChannelSubscriptions);
   bobPttManager->setChannelSubscriptions(bobPttService, bobChannelSubscriptions);
   maxPttManager->setChannelSubscriptions(maxPttService, maxChannelSubscriptions);
   tasiaPttManager->setChannelSubscriptions(tasiaPttService, tasiaChannelSubscriptions);

   // Get the analytics module created by testAccount class
   AnalyticsManager* aliceAnalyticsManager = alice.analyticsManager;
   AnalyticsManager* bobAnalyticsManager = bob.analyticsManager;
   AnalyticsManager* maxAnalyticsManager = max.analyticsManager;
   AnalyticsManager* tasiaAnalyticsManager = tasia.analyticsManager;

   // Set the basic stats to the default handler which is always (1),
   // the third paramater is optional and is only used by the test cases due
   // to issue with timing of creating module & account getting configured.
   // In real scenario, the app layer will never pass in the third parameter.
   AnalyticsHandle ahAlice = openModuleWithEmptyServerUrl(aliceAnalyticsManager);
   AnalyticsHandle ahBob = openModuleWithEmptyServerUrl(bobAnalyticsManager);
   AnalyticsHandle ahMax = openModuleWithEmptyServerUrl(maxAnalyticsManager);
   AnalyticsHandle ahTasia = openModuleWithEmptyServerUrl(tasiaAnalyticsManager);
   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   alicePttManager->startService(alicePttService);
   bobPttManager->startService(bobPttService);
   maxPttManager->startService(maxPttService);
   tasiaPttManager->startService(tasiaPttService);

   auto aliceStartEvents = std::async(std::launch::async, [&]()
   {
      assertPttServiceStartupCompleteWan(alice, alicePttService);
   });

   auto bobStartEvents = std::async(std::launch::async, [&]()
   {
      assertPttServiceStartupCompleteWan(bob, bobPttService);
   });

   auto maxStartEvents = std::async(std::launch::async, [&]()
   {
      assertPttServiceStartupCompleteWan(max, maxPttService);
   });

   auto tasiaStartEvents = std::async(std::launch::async, [&]()
   {
      assertPttServiceStartupCompleteWan(tasia, tasiaPttService);
   });

   waitFor4(aliceStartEvents, bobStartEvents, maxStartEvents, tasiaStartEvents);

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   auto aliceEvents = std::async(std::launch::async, [&]()
   {
      PushToTalkSessionHandle alicePtt = alicePttManager->createPttSession(alicePttService);
      alicePttManager->setChannel(alicePtt, "channel01");
      alicePttManager->start(alicePtt);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Initiated);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Active);
      alicePttManager->startTalkSpurt(alicePtt);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Talking);
      std::this_thread::sleep_for(std::chrono::milliseconds(3000));
      alicePttManager->endTalkSpurt(alicePtt);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Active);
      alicePttManager->end(alicePtt);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Ending);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Idle);

      std::this_thread::sleep_for(std::chrono::milliseconds(2000));

      PushToTalkSessionHandle alicePtt2 = alicePttManager->createPttSession(alicePttService);
      alicePttManager->setChannel(alicePtt2, "channel02");
      alicePttManager->start(alicePtt2);
      assertPttSessionStateChanged(alice, alicePtt2, PttSessionState_Initiated);
      assertPttSessionStateChanged(alice, alicePtt2, PttSessionState_Active);
      alicePttManager->startTalkSpurt(alicePtt2);
      assertPttSessionStateChanged(alice, alicePtt2, PttSessionState_Talking);
      std::this_thread::sleep_for(std::chrono::milliseconds(3000));
      alicePttManager->endTalkSpurt(alicePtt2);
      assertPttSessionStateChanged(alice, alicePtt2, PttSessionState_Active);
      alicePttManager->end(alicePtt2);
      assertPttSessionStateChanged(alice, alicePtt2, PttSessionState_Ending);
      assertPttSessionStateChanged(alice, alicePtt2, PttSessionState_Idle);

      std::this_thread::sleep_for(std::chrono::milliseconds(2000));

      PushToTalkSessionHandle alicePtt3 = alicePttManager->createPttSession(alicePttService);
      alicePttManager->setChannel(alicePtt3, "channel03");
      alicePttManager->start(alicePtt3);
      assertPttSessionStateChanged(alice, alicePtt3, PttSessionState_Initiated);
      assertPttSessionStateChanged(alice, alicePtt3, PttSessionState_Active);
      alicePttManager->startTalkSpurt(alicePtt3);
      assertPttSessionStateChanged(alice, alicePtt3, PttSessionState_Talking);
      std::this_thread::sleep_for(std::chrono::milliseconds(3000));
      alicePttManager->endTalkSpurt(alicePtt3);
      assertPttSessionStateChanged(alice, alicePtt3, PttSessionState_Active);
      alicePttManager->end(alicePtt3);
      assertPttSessionStateChanged(alice, alicePtt3, PttSessionState_Ending);
      assertPttSessionStateChanged(alice, alicePtt3, PttSessionState_Idle);
   });

   auto bobEvents = std::async(std::launch::async, [&]()
   {
      PushToTalkSessionHandle bobPtt = 0;
      assertPttIncomingCallEx(bob, bobPtt, aliceServiceSettings.localIdentities[0].userName, aliceServiceSettings.localIdentities[0].displayName);
      bobPttManager->accept(bobPtt);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Initiated);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Active);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Talking);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Ending);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Idle);

      PushToTalkSessionHandle bobPtt2 = 0;
      assertPttIncomingCallEx(bob, bobPtt2, aliceServiceSettings.localIdentities[0].userName, aliceServiceSettings.localIdentities[0].displayName);
      bobPttManager->accept(bobPtt2);
      assertPttSessionStateChanged(bob, bobPtt2, PttSessionState_Initiated);
      assertPttSessionStateChanged(bob, bobPtt2, PttSessionState_Active);
      assertPttSessionStateChanged(bob, bobPtt2, PttSessionState_Talking);
      assertPttSessionStateChanged(bob, bobPtt2, PttSessionState_Ending);
      assertPttSessionStateChanged(bob, bobPtt2, PttSessionState_Idle);

      PushToTalkSessionHandle bobPtt3 = 0;
      assertPttIncomingCallEx(bob, bobPtt3, aliceServiceSettings.localIdentities[0].userName, aliceServiceSettings.localIdentities[0].displayName);
      bobPttManager->accept(bobPtt3);
      assertPttSessionStateChanged(bob, bobPtt3, PttSessionState_Initiated);
      assertPttSessionStateChanged(bob, bobPtt3, PttSessionState_Active);
      assertPttSessionStateChanged(bob, bobPtt3, PttSessionState_Talking);
      assertPttSessionStateChanged(bob, bobPtt3, PttSessionState_Ending);
      assertPttSessionStateChanged(bob, bobPtt3, PttSessionState_Idle);
   });

   auto maxEvents = std::async(std::launch::async, [&]()
   {
      PushToTalkSessionHandle maxPtt = 0;
      CPCAPI2::PushToTalk::PttIncomingCallEvent evt;

      bool waitfor(true);
      while (waitfor)
      {
         ASSERT_TRUE(max.pttEvents->expectEvent(__LINE__, "PushToTalkHandler::onPttIncomingCall", 30000, AlwaysTruePred(), maxPtt, evt));
         if ((evt.channelId == "channel01") || (evt.channelId == "channel02"))
         {
            maxPttManager->accept(maxPtt);
            assertPttSessionStateChanged(max, maxPtt, PttSessionState_Initiated);
            assertPttSessionStateChanged(max, maxPtt, PttSessionState_Active);
            assertPttSessionStateChanged(max, maxPtt, PttSessionState_Talking);
            assertPttSessionStateChanged(max, maxPtt, PttSessionState_Ending);
            assertPttSessionStateChanged(max, maxPtt, PttSessionState_Idle);
            if (evt.channelId == "channel02")
            {
               waitfor = false;
            }
         }
      }
   });

   auto tasiaEvents = std::async(std::launch::async, [&]()
   {
      PushToTalkSessionHandle tasiaPtt = 0;
      CPCAPI2::PushToTalk::PttIncomingCallEvent evt;

      bool waitfor(true);
      while (waitfor)
      {
         ASSERT_TRUE(tasia.pttEvents->expectEvent(__LINE__, "PushToTalkHandler::onPttIncomingCall", 30000, AlwaysTruePred(), tasiaPtt, evt));
         if (evt.channelId == "channel01")
         {
            tasiaPttManager->accept(tasiaPtt);
            assertPttSessionStateChanged(tasia, tasiaPtt, PttSessionState_Initiated);
            assertPttSessionStateChanged(tasia, tasiaPtt, PttSessionState_Active);
            assertPttSessionStateChanged(tasia, tasiaPtt, PttSessionState_Talking);
            assertPttSessionStateChanged(tasia, tasiaPtt, PttSessionState_Ending);
            assertPttSessionStateChanged(tasia, tasiaPtt, PttSessionState_Idle);
            waitfor = false;
         }
      }
   });

   waitFor4(aliceEvents, bobEvents, maxEvents, tasiaEvents);

   aliceAnalyticsManager->sendReport(ahAlice);

   // Wait for response from report
   {
      auto sendReponse = std::async(std::launch::async, [&]()
      {
         int callCount = 3;
         std::vector<bool> incomingCalls{false, false, false};
         std::vector<std::string> channels{"channel01", "channel02", "channel03"};
         std::vector<int> responsesReceived{3, 2, 1};
         verifyPttReport(alice, aliceServiceSettings, callCount, incomingCalls, channels, responsesReceived);
      });
      waitFor(sendReponse);
   }

   bobAnalyticsManager->sendReport(ahBob);

   // Wait for response from report
   {
      auto sendReponse = std::async(std::launch::async, [&]()
      {
         int callCount = 3;
         std::vector<bool> incomingCalls{true, true, true};
         std::vector<std::string> channels{"channel01", "channel02", "channel03"};
         std::vector<int> responsesReceived{0, 0, 0};
         verifyPttReport(bob, bobServiceSettings, callCount, incomingCalls, channels, responsesReceived);
      });
      waitFor(sendReponse);
   }

   maxAnalyticsManager->sendReport(ahMax);

   // Wait for response from report
   {
      auto sendReponse = std::async(std::launch::async, [&]()
      {
         int callCount = 2;
         std::vector<bool> incomingCalls{true, true};
         std::vector<std::string> channels{"channel01", "channel02"};
         std::vector<int> responsesReceived{0, 0};
         verifyPttReport(max, maxServiceSettings, callCount, incomingCalls, channels, responsesReceived);
      });
      waitFor(sendReponse);
   }

   tasiaAnalyticsManager->sendReport(ahTasia);

   // Wait for response from report
   {
      auto sendReponse = std::async(std::launch::async, [&]()
      {
         int callCount = 1;
         std::vector<bool> incomingCalls{true};
         std::vector<std::string> channels{"channel01"};
         std::vector<int> responsesReceived{0};
         verifyPttReport(tasia, tasiaServiceSettings, callCount, incomingCalls, channels, responsesReceived);
      });
      waitFor(sendReponse);
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   alicePttManager->shutdownService(alicePttService);
   bobPttManager->shutdownService(bobPttService);
   maxPttManager->shutdownService(maxPttService);
   tasiaPttManager->shutdownService(tasiaPttService);
   assertPttServiceShutdownComplete(alice, alicePttService);
   assertPttServiceShutdownComplete(bob, bobPttService);
   assertPttServiceShutdownComplete(max, maxPttService);
   assertPttServiceShutdownComplete(tasia, tasiaPttService);

   loginComplete = true;
   waitForMs(loginEvent, std::chrono::milliseconds(120000));

   CPCAPI2::ConferenceBridge::ConferenceBridgeManager::getInterface(confServer->phone)->shutdown();
   CPCAPI2::ConferenceBridge::ConferenceRegistrar::getInterface(confServer->phone)->shutdown();
   CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(confServer->phone)->flushAll();
   CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(confServer->phone)->shutdown();
   CPCAPI2::VideoStreaming::VideoStreamingManager::getInterface(confServer->phone)->stopVideoStreamingServer();
   confServer->conferenceRegistrar->shutdown();
   confServer->jsonApiServer->shutdown();
   authServer->authServer->shutdown();

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   // Close the analytics module
   aliceAnalyticsManager->close(ahAlice);
   bobAnalyticsManager->close(ahBob);
   maxAnalyticsManager->close(ahMax);
   tasiaAnalyticsManager->close(ahTasia);
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
}
#endif // #if !defined(ANDROID)

TEST_F(Analytics1ModuleTest, PttCallFailureConnectionTimeout)
{
   TestAccount alice("alice", Account_Init);
   TestAccount bob("bob", Account_Init);

   const int unicastPort = 40001;

   PttIdentity aliceSipIdentity(PushToTalk::PttIdentityType_SIP, "<EMAIL>", "Alice");
   std::string aliceDomain("counterpath.com");
   PushToTalkServiceSettings aliceServiceSettings(aliceSipIdentity, unicastPort);
   aliceServiceSettings.unicastBindAddress = "127.0.0.1";
   aliceServiceSettings.unicastIpRanges = PttTestHelper::getUnicastIpRange(2);
   aliceServiceSettings.subscribedChannels.push_back("channel01");
   PushToTalkSettingsInternal aliceInternalSettings;
   aliceInternalSettings.outgoingSessionExpiryMsecs = 5000;

   PttIdentity bobSipIdentity(PushToTalk::PttIdentityType_SIP, "<EMAIL>", "Bob");
   std::string bobDomain("counterpath.com");
   PushToTalkServiceSettings bobServiceSettings(bobSipIdentity, unicastPort);
   bobServiceSettings.unicastBindAddress = "*********";
   bobServiceSettings.unicastIpRanges = PttTestHelper::getUnicastIpRange(2);
   bobServiceSettings.subscribedChannels.push_back("channel01");
   PushToTalkSettingsInternal bobInternalSettings;
   bobInternalSettings.outgoingSessionExpiryMsecs = 5000;

   PushToTalkManager* alicePttManager = PushToTalkManager::getInterface(alice.phone);
   PushToTalkServiceHandle alicePttService = alicePttManager->createPttService();
   alicePttManager->setHandler(alicePttService, (PushToTalkHandler*)0xDEADBEEF);
   alicePttManager->configureService(alicePttService, aliceServiceSettings);
   PushToTalkManagerInternal* alicePttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(alicePttManager);

   PushToTalkManager* bobPttManager = PushToTalkManager::getInterface(bob.phone);
   PushToTalkServiceHandle bobPttService = bobPttManager->createPttService();
   bobPttManager->setHandler(bobPttService, (PushToTalkHandler*)0xDEADBEEF);
   bobPttManager->configureService(bobPttService, bobServiceSettings);
   PushToTalkManagerInternal* bobPttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(bobPttManager);

   // Get the analytics module created by testAccount class
   AnalyticsManager* aliceAnalyticsManager = alice.analyticsManager;
   AnalyticsManager* bobAnalyticsManager = bob.analyticsManager;

   // Set the basic stats to the default handler which is always (1),
   // the third paramater is optional and is only used by the test cases due
   // to issue with timing of creating module & account getting configured.
   // In real scenario, the app layer will never pass in the third parameter.
   AnalyticsHandle ahAlice = openModuleWithEmptyServerUrl(aliceAnalyticsManager);
   AnalyticsHandle ahBob = openModuleWithEmptyServerUrl(bobAnalyticsManager);
   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   alicePttManagerInternal->setPttInternalSettings(alicePttService, aliceInternalSettings);
   bobPttManagerInternal->setPttInternalSettings(bobPttService, bobInternalSettings);

   alicePttManager->startService(alicePttService);
   bobPttManager->startService(bobPttService);

   auto aliceEvents = std::async(std::launch::async, [&]()
   {
      assertPttServiceConfigured(alice, alicePttService);
      assertPttServiceStartupCompleteLan(alice, alicePttService);
   });

   auto bobEvents = std::async(std::launch::async, [&]()
   {
      assertPttServiceConfigured(bob, bobPttService);
      assertPttServiceStartupCompleteLan(bob, bobPttService);
   });

   waitFor2(aliceEvents, bobEvents);

   {
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));

      {
         auto aliceEvents2 = std::async(std::launch::async, [&]()
         {
            PushToTalkSessionHandle alicePtt = alicePttManager->createPttSession(alicePttService);
            alicePttManager->setChannel(alicePtt, "channel01");
            alicePttManager->start(alicePtt);
            assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Initiated);
            // As no connection responses are received, ptt session will timeout
            assertPttSessionError(alice, alicePtt, PttSessionError_ConnectionTimeout);
            assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Ending);
            assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Idle);
         });

         waitFor(aliceEvents2);
      }
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   aliceAnalyticsManager->sendReport(ahAlice);

   // Wait for response from report
   {
      auto sendReponse = std::async(std::launch::async, [&]()
      {
         AnalyticsHandle h;
         OnReportCreatedSuccessEvent evt;
         ASSERT_TRUE(cpcWaitForEvent(alice.analyticsEvents, "AnalyticsHandlerInt::onReportCreatedSuccess", 15000, AlwaysTruePred(), h, evt));
         safeCout("Analytics1ModuleTest::PttServiceTest(): XML: " << evt.content.c_str());

         // Make sure document is not empty
         ASSERT_NE(evt.content, cpc::string());
         // Make sure settings_data tag exists
         bool exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data[1]");
         ASSERT_TRUE(exists);
         // Make sure account_list tag exists
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list[1]");
         ASSERT_TRUE(exists);
         // Make sure at least 2 account tags exists
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list/account[1]");
         ASSERT_TRUE(exists);
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list/account[2]");
         ASSERT_TRUE(exists);
         // Make sure activity_data tag exists
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data[1]");
         ASSERT_TRUE(exists);
         // Make sure call_list tag exists
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data/call_list[1]");
         ASSERT_TRUE(exists);
         // Make sure atleast 1 call exists
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data/call_list/call[1]");
         ASSERT_TRUE(exists);

         _xmlNode* node = getXmlNode(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list/account[1]");   // PTT is first account SIP is second
         // Make sure it's a PTT account
         ASSERT_EQ(strncmp((const char *)node->parent->properties->children->content, "PTT", strlen("PTT")), 0);
         // Make sure relevant data tags exist and their content is not empty
         EXPECT_STREQ((const char *)node->properties->children->content, "domain"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, aliceDomain.c_str()); // tag value

         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "username"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, aliceServiceSettings.getPrimaryLocalIdentity().c_str()); // tag value

         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "protocol"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "PTT"); // tag value

         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "enabled"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "true"); // tag value

         node = getXmlNode(evt.content.c_str(), "/cpc_usage_report/activity_data/call_list/call[1]");
         // Make sure it's a PTT call
         ASSERT_EQ(strncmp((const char *)node->parent->properties->children->content, "PTT", strlen("PTT")), 0);

         // make sure relevant data tags exist and their content is not empty
         EXPECT_STREQ((const char *)node->properties->children->content, "callStart"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "callDuration"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "incoming"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "false"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "callSuccessful"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "false"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "audioInCodec"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "audioOutCodec"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "mediaEncryption"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "None"); // tag value
         node = node->next; // get next data tag
         if (strcmp((const char*)node->properties->children->content, "usbDevice") == 0)
         {
            EXPECT_STREQ((const char *)node->properties->children->content, "usbDevice"); // tag name
            EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
            node = node->next; // get next data tag
         }
         EXPECT_STREQ((const char *)node->properties->children->content, "channel"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "channel01"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "serviceType"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "LAN"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "remoteIdentity"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "channel01"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "failureReason"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "connection timeout"); // tag value
      });
      waitFor(sendReponse);
   }

   alicePttManager->shutdownService(alicePttService);
   assertPttServiceShutdownComplete(alice, alicePttService);
   bobPttManager->shutdownService(bobPttService);
   assertPttServiceShutdownComplete(bob, bobPttService);

   // Close the module
   aliceAnalyticsManager->close(ahAlice);
   bobAnalyticsManager->close(ahBob);
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
}

TEST_F(Analytics1ModuleTest, PttCallFailureCallOverride)
{
   TestAccount alice("alice", Account_Init);
   TestAccount bob("bob", Account_Init);
   TestAccount max("max", Account_Init);

   const int unicastPort = 40001;

   PttIdentity aliceSipIdentity(PushToTalk::PttIdentityType_SIP, "<EMAIL>", "Alice");
   PttIdentity aliceXmppIdentity(PushToTalk::PttIdentityType_XMPP, "<EMAIL>", "Alice");
   cpc::vector<PttIdentity> aliceIdentities;
   aliceIdentities.push_back(aliceSipIdentity);
   aliceIdentities.push_back(aliceXmppIdentity);
   std::string aliceDomain("counterpath.com");
   PushToTalkServiceSettings aliceServiceSettings;
   aliceServiceSettings.localIdentities = aliceIdentities;
   aliceServiceSettings.unicastPort = unicastPort;
   aliceServiceSettings.unicastBindAddress = "127.0.0.1";
   aliceServiceSettings.unicastIpRanges = PttTestHelper::getUnicastIpRange(3);
   // aliceServiceSettings.mediaInactivityIntervalSeconds = 5;
   aliceServiceSettings.subscribedChannels.push_back("channel01");
   PushToTalkSettingsInternal aliceInternalSettings;
   aliceInternalSettings.outgoingSessionExpiryMsecs = 5000;
   aliceInternalSettings.unicastInitRetryIntervalMsecs = 20000;
   // aliceInternalSettings.mediaInactivityMonitorEnabled = false;

   PttIdentity bobSipIdentity(PushToTalk::PttIdentityType_SIP, "<EMAIL>", "Bob");
   PttIdentity bobXmppIdentity(PushToTalk::PttIdentityType_XMPP, "<EMAIL>", "Bob");
   cpc::vector<PttIdentity> bobIdentities;
   bobIdentities.push_back(bobSipIdentity);
   bobIdentities.push_back(bobXmppIdentity);
   std::string bobDomain("counterpath.com");
   PushToTalkServiceSettings bobServiceSettings;
   bobServiceSettings.localIdentities = bobIdentities;
   bobServiceSettings.unicastPort = unicastPort;
   bobServiceSettings.unicastBindAddress = "*********";
   bobServiceSettings.unicastIpRanges = PttTestHelper::getUnicastIpRange(3);
   // bobServiceSettings.mediaInactivityIntervalSeconds = 5;
   bobServiceSettings.subscribedChannels.push_back("channel01");
   PushToTalkSettingsInternal bobInternalSettings;
   bobInternalSettings.outgoingSessionExpiryMsecs = 5000;
   bobInternalSettings.unicastInitRetryIntervalMsecs = 20000;
   bobInternalSettings.channelOverrideDurationMs = 10000;
   // bobInternalSettings.mediaInactivityMonitorEnabled = false;

   PttIdentity maxSipIdentity(PushToTalk::PttIdentityType_SIP, "<EMAIL>", "Max");
   PttIdentity maxXmppIdentity(PushToTalk::PttIdentityType_XMPP, "<EMAIL>", "Max");
   cpc::vector<PttIdentity> maxIdentities;
   maxIdentities.push_back(maxSipIdentity);
   maxIdentities.push_back(maxXmppIdentity);
   std::string maxDomain("counterpath.com");
   PushToTalkServiceSettings maxServiceSettings;
   maxServiceSettings.localIdentities = maxIdentities;
   maxServiceSettings.unicastPort = unicastPort;
   maxServiceSettings.unicastBindAddress = "*********";
   maxServiceSettings.unicastIpRanges = PttTestHelper::getUnicastIpRange(3);
   // maxServiceSettings.mediaInactivityIntervalSeconds = 5;
   maxServiceSettings.subscribedChannels.push_back("channel01");
   PushToTalkSettingsInternal maxInternalSettings;
   maxInternalSettings.outgoingSessionExpiryMsecs = 5000;
   // maxInternalSettings.mediaInactivityMonitorEnabled = false;
   maxInternalSettings.incomingSetupMsecs = 500;

   PushToTalkManager* alicePttManager = PushToTalkManager::getInterface(alice.phone);
   PushToTalkServiceHandle alicePttService = alicePttManager->createPttService();
   alicePttManager->setHandler(alicePttService, (PushToTalkHandler*)0xDEADBEEF);
   alicePttManager->configureService(alicePttService, aliceServiceSettings);
   PushToTalkManagerInternal* alicePttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(alicePttManager);

   PushToTalkManager* bobPttManager = PushToTalkManager::getInterface(bob.phone);
   PushToTalkServiceHandle bobPttService = bobPttManager->createPttService();
   bobPttManager->setHandler(bobPttService, (PushToTalkHandler*)0xDEADBEEF);
   bobPttManager->configureService(bobPttService, bobServiceSettings);
   PushToTalkManagerInternal* bobPttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(bobPttManager);

   PushToTalkManager* maxPttManager = PushToTalkManager::getInterface(max.phone);
   PushToTalkServiceHandle maxPttService = maxPttManager->createPttService();
   maxPttManager->setHandler(maxPttService, (PushToTalkHandlerInternal*)0xDEADBEEF);
   maxPttManager->configureService(maxPttService, maxServiceSettings);
   PushToTalkManagerInternal* maxPttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(maxPttManager);

   // Get the analytics module created by testAccount class
   AnalyticsManager* aliceAnalyticsManager = alice.analyticsManager;
   AnalyticsManager* bobAnalyticsManager = bob.analyticsManager;
   AnalyticsManager* maxAnalyticsManager = max.analyticsManager;

   // Set the basic stats to the default handler which is always (1),
   // the third paramater is optional and is only used by the test cases due
   // to issue with timing of creating module & account getting configured.
   // In real scenario, the app layer will never pass in the third parameter.
   AnalyticsHandle ahAlice = openModuleWithEmptyServerUrl(aliceAnalyticsManager);
   AnalyticsHandle ahBob = openModuleWithEmptyServerUrl(bobAnalyticsManager);
   AnalyticsHandle ahMax = openModuleWithEmptyServerUrl(maxAnalyticsManager);
   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   alicePttManagerInternal->setPttInternalSettings(alicePttService, aliceInternalSettings);
   bobPttManagerInternal->setPttInternalSettings(bobPttService, bobInternalSettings);
   maxPttManagerInternal->setPttInternalSettings(maxPttService, maxInternalSettings);

   alicePttManager->startService(alicePttService);
   bobPttManager->startService(bobPttService);
   maxPttManager->startService(maxPttService);

   auto aliceEvents = std::async(std::launch::async, [&]()
   {
      assertPttServiceConfigured(alice, alicePttService);
      assertPttServiceStartupCompleteLan(alice, alicePttService);
   });

   auto bobEvents = std::async(std::launch::async, [&]()
   {
      assertPttServiceConfigured(bob, bobPttService);
      assertPttServiceStartupCompleteLan(bob, bobPttService);
   });

   auto maxEvents = std::async(std::launch::async, [&]()
   {
      assertPttServiceConfigured(max, maxPttService);
      assertPttServiceStartupCompleteLan(max, maxPttService);
   });

   waitFor3(aliceEvents, bobEvents, maxEvents);

   {
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));

      {
         PushToTalkSessionHandle alicePtt = 0;
         PushToTalkSessionHandle bobPtt = 0;
         PushToTalkSessionHandle maxPtt = 0;

         auto aliceEvents2 = std::async(std::launch::async, [&] ()
         {
            alicePtt = alicePttManager->createPttSession(alicePttService);
            alicePttManager->setChannel(alicePtt, "channel01");
            alicePttManager->start(alicePtt);
            assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Initiated);
            assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Active);

            // Initiate talk spurt
            alicePttManager->startTalkSpurt(alicePtt);
            assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Talking);
         });

         auto bobEvents2 = std::async(std::launch::async, [&] ()
         {
            assertPttIncomingCallEx(bob, bobPtt, aliceServiceSettings.localIdentities[0].userName, aliceServiceSettings.localIdentities[0].displayName);
            bobPttManager->accept(bobPtt);
            assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Initiated);
            assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Active);
            assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Talking);
         });

         waitFor2(aliceEvents2, bobEvents2);

         auto aliceEvents3 = std::async(std::launch::async, [&] ()
         {
            assertPttSessionError(alice, alicePtt, PttSessionError_CallOverride);
            assertPttReceiverDisconnected(alice, alicePtt, "channel01", PttSessionState_Talking, 0, 1);
            alicePttManager->end(alicePtt);
            assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Ending);
            assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Idle);
         });

         auto bobEvents3 = std::async(std::launch::async, [&] ()
         {
            assertPttSessionError(bob, bobPtt, PttSessionError_ChannelOverride);

            PushToTalkSessionHandle bobPtt2 = 0;
            assertPttIncomingCallEx(bob, bobPtt2, maxServiceSettings.localIdentities[0].userName, maxServiceSettings.localIdentities[0].displayName);
            bobPttManager->accept(bobPtt2);
            assertPttSessionStateChanged(bob, bobPtt2, PttSessionState_Initiated);
            assertPttSessionStateChanged(bob, bobPtt2, PttSessionState_Active);
            assertPttSessionStateChanged(bob, bobPtt2, PttSessionState_Talking);
            assertPttSessionStateChanged(bob, bobPtt2, PttSessionState_Ending);
            assertPttSessionStateChanged(bob, bobPtt2, PttSessionState_Idle);
         });

         auto maxEvents3 = std::async(std::launch::async, [&] ()
         {
            maxPtt = maxPttManager->createPttSession(maxPttService);
            maxPttManager->setChannel(maxPtt, "channel01");
            maxPttManager->start(maxPtt);

            assertPttSessionStateChanged(max, maxPtt, PttSessionState_Initiated);
            assertPttSessionStateChanged(max, maxPtt, PttSessionState_Active);

            // Initiate talk spurt
            maxPttManager->startTalkSpurt(maxPtt);
            assertPttSessionStateChanged(max, maxPtt, PttSessionState_Talking);
            std::this_thread::sleep_for(std::chrono::milliseconds(3000));
            maxPttManager->endTalkSpurt(maxPtt);
            assertPttSessionStateChanged(max, maxPtt, PttSessionState_Active);
            maxPttManager->end(maxPtt);
            assertPttSessionStateChanged(max, maxPtt, PttSessionState_Ending);
            assertPttSessionStateChanged(max, maxPtt, PttSessionState_Idle);
         });

         waitFor3(aliceEvents3, bobEvents3, maxEvents3);
         std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      }
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   aliceAnalyticsManager->sendReport(ahAlice);

   // Wait for response from report
   {
      auto sendReponse = std::async(std::launch::async, [&]()
      {
         AnalyticsHandle h;
         OnReportCreatedSuccessEvent evt;
         ASSERT_TRUE(cpcWaitForEvent(alice.analyticsEvents, "AnalyticsHandlerInt::onReportCreatedSuccess", 15000, AlwaysTruePred(), h, evt));
         safeCout("Analytics1ModuleTest::PttServiceTest(): XML: " << evt.content.c_str());

         // Make sure document is not empty
         ASSERT_NE(evt.content, cpc::string());
         // Make sure settings_data tag exists
         bool exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data[1]");
         ASSERT_TRUE(exists);
         // Make sure account_list tag exists
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list[1]");
         ASSERT_TRUE(exists);
         // Make sure at least 2 account tags exists
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list/account[1]");
         ASSERT_TRUE(exists);
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list/account[2]");
         ASSERT_TRUE(exists);
         // Make sure activity_data tag exists
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data[1]");
         ASSERT_TRUE(exists);
         // Make sure call_list tag exists
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data/call_list[1]");
         ASSERT_TRUE(exists);
         // Make sure a call tag exists
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data/call_list/call[1]");
         ASSERT_TRUE(exists);

         _xmlNode* node = getXmlNode(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list/account[1]");   // PTT is first account SIP is second
         // Make sure it's a PTT account
         ASSERT_EQ(strncmp((const char *)node->parent->properties->children->content, "PTT", strlen("PTT")), 0);
         // Make sure relevant data tags exist and their content is not empty
         EXPECT_STREQ((const char *)node->properties->children->content, "domain"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, aliceDomain.c_str()); // tag value

         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "username"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, aliceServiceSettings.getPrimaryLocalIdentity().c_str()); // tag value

         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "protocol"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "PTT"); // tag value

         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "enabled"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "true"); // tag value

         node = getXmlNode(evt.content.c_str(), "/cpc_usage_report/activity_data/call_list/call[1]");
         // Make sure it's a PTT call
         ASSERT_EQ(strncmp((const char *)node->parent->properties->children->content, "PTT", strlen("PTT")), 0);

         // make sure relevant data tags exist and their content is not empty
         EXPECT_STREQ((const char *)node->properties->children->content, "callStart"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "callDuration"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "incoming"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "false"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "callSuccessful"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "false"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "audioInCodec"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "audioOutCodec"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "mediaEncryption"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "None"); // tag value
         node = node->next; // get next data tag
         if (strcmp((const char*)node->properties->children->content, "usbDevice") == 0)
         {
            EXPECT_STREQ((const char *)node->properties->children->content, "usbDevice"); // tag name
            EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
            node = node->next; // get next data tag
         }
         EXPECT_STREQ((const char *)node->properties->children->content, "channel"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "channel01"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "serviceType"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "LAN"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "remoteIdentity"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "channel01"); // tag value
      });
      waitFor(sendReponse);
   }

   bobAnalyticsManager->sendReport(ahBob);

   // Wait for response from report
   {
      auto sendReponse = std::async(std::launch::async, [&]()
      {
         AnalyticsHandle h;
         OnReportCreatedSuccessEvent evt;
         ASSERT_TRUE(cpcWaitForEvent(bob.analyticsEvents, "AnalyticsHandlerInt::onReportCreatedSuccess", 15000, AlwaysTruePred(), h, evt));
         safeCout("Analytics1ModuleTest::PttServiceTest(): XML: " << evt.content.c_str());

         // Make sure document is not empty
         ASSERT_NE(evt.content, cpc::string());
         // Make sure settings_data tag exists
         bool exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data[1]");
         ASSERT_TRUE(exists);
         // Make sure account_list tag exists
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list[1]");
         ASSERT_TRUE(exists);
         // Make sure at least 2 account tags exists
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list/account[1]");
         ASSERT_TRUE(exists);
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list/account[2]");
         ASSERT_TRUE(exists);
         // Make sure activity_data tag exists
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data[1]");
         ASSERT_TRUE(exists);
         // Make sure call_list tag exists
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data/call_list[1]");
         ASSERT_TRUE(exists);
         // Make sure 2 call tags exist
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data/call_list/call[1]");
         ASSERT_TRUE(exists);
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data/call_list/call[2]");
         ASSERT_TRUE(exists);

         _xmlNode* node = getXmlNode(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list/account[1]");   // PTT is first account SIP is second
         // Make sure it's a PTT account
         ASSERT_EQ(strncmp((const char *)node->parent->properties->children->content, "PTT", strlen("PTT")), 0);
         // Make sure relevant data tags exist and their content is not empty
         EXPECT_STREQ((const char *)node->properties->children->content, "domain"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, bobDomain.c_str()); // tag value

         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "username"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, bobServiceSettings.getPrimaryLocalIdentity().c_str()); // tag value

         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "protocol"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "PTT"); // tag value

         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "enabled"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "true"); // tag value

         node = getXmlNode(evt.content.c_str(), "/cpc_usage_report/activity_data/call_list/call[1]");
         // Make sure it's a PTT call
         ASSERT_EQ(strncmp((const char *)node->parent->properties->children->content, "PTT", strlen("PTT")), 0);

         // make sure relevant data tags exist and their content is not empty
         EXPECT_STREQ((const char *)node->properties->children->content, "callStart"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "callDuration"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "incoming"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "true"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "callSuccessful"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "false"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "audioInCodec"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "audioOutCodec"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "mediaEncryption"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "None"); // tag value
         node = node->next; // get next data tag
         if (strcmp((const char*)node->properties->children->content, "usbDevice") == 0)
         {
            EXPECT_STREQ((const char *)node->properties->children->content, "usbDevice"); // tag name
            EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
            node = node->next; // get next data tag
         }
         EXPECT_STREQ((const char *)node->properties->children->content, "channel"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "channel01"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "serviceType"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "LAN"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "remoteIdentity"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "sip:<EMAIL>"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "failureReason"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "channel override"); // tag value

         node = getXmlNode(evt.content.c_str(), "/cpc_usage_report/activity_data/call_list/call[2]");
         // Make sure it's a PTT call
         ASSERT_EQ(strncmp((const char *)node->parent->properties->children->content, "PTT", strlen("PTT")), 0);

         // make sure relevant data tags exist and their content is not empty
         EXPECT_STREQ((const char *)node->properties->children->content, "callStart"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "callDuration"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "incoming"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "true"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "callSuccessful"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "true"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "audioInCodec"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "audioOutCodec"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "mediaEncryption"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "None"); // tag value
         node = node->next; // get next data tag
         if (strcmp((const char*)node->properties->children->content, "usbDevice") == 0)
         {
            EXPECT_STREQ((const char *)node->properties->children->content, "usbDevice"); // tag name
            EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
            node = node->next; // get next data tag
         }
         EXPECT_STREQ((const char *)node->properties->children->content, "channel"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "channel01"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "serviceType"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "LAN"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "remoteIdentity"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "sip:<EMAIL>"); // tag value
      });
      waitFor(sendReponse);
   }

/*
   maxAnalyticsManager->sendReport(ahMax);

   // Wait for response from report
   {
      auto sendReponse = std::async(std::launch::async, [&]()
      {
         AnalyticsHandle h;
         OnReportCreatedSuccessEvent evt;
         ASSERT_TRUE(cpcWaitForEvent(max.analyticsEvents, "AnalyticsHandlerInt::onReportCreatedSuccess", 15000, AlwaysTruePred(), h, evt));
         safeCout("Analytics1ModuleTest::PttServiceTest(): XML: " << evt.content.c_str());

         // Make sure document is not empty
         ASSERT_NE(evt.content, cpc::string());
         // Make sure settings_data tag exists
         bool exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data[1]");
         ASSERT_TRUE(exists);
         // Make sure account_list tag exists
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list[1]");
         ASSERT_TRUE(exists);
         // Make sure at least 2 account tags exists
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list/account[1]");
         ASSERT_TRUE(exists);
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list/account[2]");
         ASSERT_TRUE(exists);
         // Make sure activity_data tag exists
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data[1]");
         ASSERT_TRUE(exists);
         // Make sure call_list tag exists
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data/call_list[1]");
         ASSERT_TRUE(exists);
         // Make sure 2 call tags exist
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data/call_list/call[1]");
         ASSERT_TRUE(exists);
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data/call_list/call[2]");
         ASSERT_TRUE(exists);

         _xmlNode* node = getXmlNode(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list/account[2]");
         // Make sure it's a PTT account
         ASSERT_EQ(strncmp((const char *)node->parent->properties->children->content, "PTT", strlen("PTT")), 0);
         // Make sure relevant data tags exist and their content is not empty
         EXPECT_STREQ((const char *)node->properties->children->content, "domain"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, maxDomain.c_str()); // tag value

         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "username"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, maxServiceSettings.getPrimaryLocalIdentity().c_str()); // tag value

         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "protocol"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "PTT"); // tag value

         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "enabled"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "true"); // tag value

         node = getXmlNode(evt.content.c_str(), "/cpc_usage_report/activity_data/call_list/call[1]");
         // Make sure it's a PTT call
         ASSERT_EQ(strncmp((const char *)node->parent->properties->children->content, "PTT", strlen("PTT")), 0);

         // make sure relevant data tags exist and their content is not empty
         EXPECT_STREQ((const char *)node->properties->children->content, "callStart"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "callDuration"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "incoming"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "true"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "callSuccessful"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "false"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "audioInCodec"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "audioOutCodec"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "mediaEncryption"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "None"); // tag value
#if !(defined(__linux__) && !defined(ANDROID))
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "usbDevice"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
#endif
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "channel"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "channel01"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "serviceType"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "LAN"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "remoteIdentity"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "sip:<EMAIL>"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "failureReason"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "setup timeout"); // tag value

         node = getXmlNode(evt.content.c_str(), "/cpc_usage_report/activity_data/call_list/call[2]");
         // Make sure it's a PTT call
         ASSERT_EQ(strncmp((const char *)node->parent->properties->children->content, "PTT", strlen("PTT")), 0);

         // make sure relevant data tags exist and their content is not empty
         EXPECT_STREQ((const char *)node->properties->children->content, "callStart"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "callDuration"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "incoming"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "false"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "callSuccessful"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "true"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "audioInCodec"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "audioOutCodec"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "mediaEncryption"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "None"); // tag value
#if !(defined(__linux__) && !defined(ANDROID))
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "usbDevice"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
#endif
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "channel"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "channel01"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "serviceType"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "LAN"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "remoteIdentity"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "channel01"); // tag value
      });
      waitFor(sendReponse);
   }
*/

   alicePttManager->shutdownService(alicePttService);
   assertPttServiceShutdownComplete(alice, alicePttService);
   bobPttManager->shutdownService(bobPttService);
   assertPttServiceShutdownComplete(bob, bobPttService);
   maxPttManager->shutdownService(maxPttService);
   assertPttServiceShutdownComplete(max, maxPttService);

   // Close the module
   aliceAnalyticsManager->close(ahAlice);
   bobAnalyticsManager->close(ahBob);
   maxAnalyticsManager->close(ahMax);
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
}

TEST_F(Analytics1ModuleTest, PttCallSuccessMultipleRecipients)
{
   TestAccount alice("alice", Account_Init);
   TestAccount bob("bob", Account_Init);
   TestAccount max("max", Account_Init);

   const int unicastPort = 40001;

   PttIdentity aliceSipIdentity(PushToTalk::PttIdentityType_SIP, "<EMAIL>", "Alice");
   std::string aliceDomain("counterpath.com");
   PushToTalkServiceSettings aliceServiceSettings(aliceSipIdentity, unicastPort);
   aliceServiceSettings.unicastPort = unicastPort;
   aliceServiceSettings.unicastBindAddress = "127.0.0.1";
   aliceServiceSettings.unicastIpRanges = PttTestHelper::getUnicastIpRange(3);
   aliceServiceSettings.subscribedChannels.push_back("channel01");
   PushToTalkSettingsInternal aliceInternalSettings;
   aliceInternalSettings.outgoingSessionExpiryMsecs = 5000;
   aliceInternalSettings.unicastInitRetryIntervalMsecs = 20000;

   PttIdentity bobSipIdentity(PushToTalk::PttIdentityType_SIP, "<EMAIL>", "Bob");
   std::string bobDomain("counterpath.com");
   PushToTalkServiceSettings bobServiceSettings(bobSipIdentity, unicastPort);
   bobServiceSettings.unicastPort = unicastPort;
   bobServiceSettings.unicastBindAddress = "*********";
   bobServiceSettings.unicastIpRanges = PttTestHelper::getUnicastIpRange(3);
   bobServiceSettings.subscribedChannels.push_back("channel01");
   PushToTalkSettingsInternal bobInternalSettings;
   bobInternalSettings.outgoingSessionExpiryMsecs = 5000;
   bobInternalSettings.unicastInitRetryIntervalMsecs = 20000;
   bobInternalSettings.channelOverrideDurationMs = 10000;

   PttIdentity maxSipIdentity(PushToTalk::PttIdentityType_SIP, "<EMAIL>", "Max");
   std::string maxDomain("counterpath.com");
   PushToTalkServiceSettings maxServiceSettings(maxSipIdentity, unicastPort);
   maxServiceSettings.unicastPort = unicastPort;
   maxServiceSettings.unicastBindAddress = "*********";
   maxServiceSettings.unicastIpRanges = PttTestHelper::getUnicastIpRange(3);
   maxServiceSettings.subscribedChannels.push_back("channel01");
   PushToTalkSettingsInternal maxInternalSettings;
   maxInternalSettings.outgoingSessionExpiryMsecs = 5000;
   maxInternalSettings.incomingSetupMsecs = 500;

   PushToTalkManager* alicePttManager = PushToTalkManager::getInterface(alice.phone);
   PushToTalkServiceHandle alicePttService = alicePttManager->createPttService();
   alicePttManager->setHandler(alicePttService, (PushToTalkHandler*)0xDEADBEEF);
   alicePttManager->configureService(alicePttService, aliceServiceSettings);
   PushToTalkManagerInternal* alicePttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(alicePttManager);

   PushToTalkManager* bobPttManager = PushToTalkManager::getInterface(bob.phone);
   PushToTalkServiceHandle bobPttService = bobPttManager->createPttService();
   bobPttManager->setHandler(bobPttService, (PushToTalkHandler*)0xDEADBEEF);
   bobPttManager->configureService(bobPttService, bobServiceSettings);
   PushToTalkManagerInternal* bobPttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(bobPttManager);

   PushToTalkManager* maxPttManager = PushToTalkManager::getInterface(max.phone);
   PushToTalkServiceHandle maxPttService = maxPttManager->createPttService();
   maxPttManager->setHandler(maxPttService, (PushToTalkHandlerInternal*)0xDEADBEEF);
   maxPttManager->configureService(maxPttService, maxServiceSettings);
   PushToTalkManagerInternal* maxPttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(maxPttManager);

   // Get the analytics module created by testAccount class
   AnalyticsManager* aliceAnalyticsManager = alice.analyticsManager;
   AnalyticsManager* bobAnalyticsManager = bob.analyticsManager;
   AnalyticsManager* maxAnalyticsManager = max.analyticsManager;

   // Set the basic stats to the default handler which is always (1),
   // the third paramater is optional and is only used by the test cases due
   // to issue with timing of creating module & account getting configured.
   // In real scenario, the app layer will never pass in the third parameter.
   AnalyticsHandle ahAlice = openModuleWithEmptyServerUrl(aliceAnalyticsManager);
   AnalyticsHandle ahBob = openModuleWithEmptyServerUrl(bobAnalyticsManager);
   AnalyticsHandle ahMax = openModuleWithEmptyServerUrl(maxAnalyticsManager);
   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   alicePttManagerInternal->setPttInternalSettings(alicePttService, aliceInternalSettings);
   bobPttManagerInternal->setPttInternalSettings(bobPttService, bobInternalSettings);
   maxPttManagerInternal->setPttInternalSettings(maxPttService, maxInternalSettings);

   alicePttManager->startService(alicePttService);
   bobPttManager->startService(bobPttService);
   maxPttManager->startService(maxPttService);

   auto aliceEvents = std::async(std::launch::async, [&]()
   {
      assertPttServiceConfigured(alice, alicePttService);
      assertPttServiceStartupCompleteLan(alice, alicePttService);
   });

   auto bobEvents = std::async(std::launch::async, [&]()
   {
      assertPttServiceConfigured(bob, bobPttService);
      assertPttServiceStartupCompleteLan(bob, bobPttService);
   });

   auto maxEvents = std::async(std::launch::async, [&]()
   {
      assertPttServiceConfigured(max, maxPttService);
      assertPttServiceStartupCompleteLan(max, maxPttService);
   });

   waitFor3(aliceEvents, bobEvents, maxEvents);

   {
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));

      {
         PushToTalkSessionHandle alicePtt = 0;
         PushToTalkSessionHandle bobPtt = 0;
         PushToTalkSessionHandle maxPtt = 0;

         auto aliceEvents2 = std::async(std::launch::async, [&] ()
         {
            alicePtt = alicePttManager->createPttSession(alicePttService);
            alicePttManager->setChannel(alicePtt, "channel01");
            alicePttManager->start(alicePtt);
            assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Initiated);
            assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Active);

            // Initiate talk spurt
            alicePttManager->startTalkSpurt(alicePtt);
            assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Talking);
            std::this_thread::sleep_for(std::chrono::milliseconds(2000));
            alicePttManager->endTalkSpurt(alicePtt);
            assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Active);
            alicePttManager->end(alicePtt);
            assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Ending);
            assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Idle);
         });

         auto bobEvents2 = std::async(std::launch::async, [&] ()
         {
            assertPttIncomingCallEx(bob, bobPtt, aliceServiceSettings.localIdentities[0].userName, aliceServiceSettings.localIdentities[0].displayName);
            bobPttManager->accept(bobPtt);
            assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Initiated);
            assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Active);
            assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Talking);
            assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Ending);
            assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Idle);
         });

         auto maxEvents2 = std::async(std::launch::async, [&] ()
         {
            assertPttIncomingCallEx(max, maxPtt, aliceServiceSettings.localIdentities[0].userName, aliceServiceSettings.localIdentities[0].displayName);
            maxPttManager->accept(maxPtt);
            assertPttSessionStateChanged(max, maxPtt, PttSessionState_Initiated);
            assertPttSessionStateChanged(max, maxPtt, PttSessionState_Active);
            assertPttSessionStateChanged(max, maxPtt, PttSessionState_Talking);
            assertPttSessionStateChanged(max, maxPtt, PttSessionState_Ending);
            assertPttSessionStateChanged(max, maxPtt, PttSessionState_Idle);
         });

         waitFor3(aliceEvents2, bobEvents2, maxEvents2);
         std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      }
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   aliceAnalyticsManager->sendReport(ahAlice);

   // Wait for response from report
   {
      auto sendReponse = std::async(std::launch::async, [&]()
      {
         AnalyticsHandle h;
         OnReportCreatedSuccessEvent evt;
         ASSERT_TRUE(cpcWaitForEvent(alice.analyticsEvents, "AnalyticsHandlerInt::onReportCreatedSuccess", 15000, AlwaysTruePred(), h, evt));
         safeCout("Analytics1ModuleTest::PttServiceTest(): XML: " << evt.content.c_str());

         // Make sure document is not empty
         ASSERT_NE(evt.content, cpc::string());
         // Make sure settings_data tag exists
         bool exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data[1]");
         ASSERT_TRUE(exists);
         // Make sure account_list tag exists
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list[1]");
         ASSERT_TRUE(exists);
         // Make sure at least 2 account tags exists
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list/account[1]");
         ASSERT_TRUE(exists);
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list/account[2]");
         ASSERT_TRUE(exists);
         // Make sure activity_data tag exists
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data[1]");
         ASSERT_TRUE(exists);
         // Make sure call_list tag exists
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data/call_list[1]");
         ASSERT_TRUE(exists);
         // Make sure a call tag exists
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data/call_list/call[1]");
         ASSERT_TRUE(exists);

         _xmlNode* node = getXmlNode(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list/account[1]");   // PTT is first account SIP is second
         // Make sure it's a PTT account
         ASSERT_EQ(strncmp((const char *)node->parent->properties->children->content, "PTT", strlen("PTT")), 0);
         // Make sure relevant data tags exist and their content is not empty
         EXPECT_STREQ((const char *)node->properties->children->content, "domain"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, aliceDomain.c_str()); // tag value

         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "username"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, aliceServiceSettings.getPrimaryLocalIdentity().c_str()); // tag value

         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "protocol"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "PTT"); // tag value

         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "enabled"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "true"); // tag value

         node = getXmlNode(evt.content.c_str(), "/cpc_usage_report/activity_data/call_list/call[1]");
         // Make sure it's a PTT call
         ASSERT_EQ(strncmp((const char *)node->parent->properties->children->content, "PTT", strlen("PTT")), 0);

         // make sure relevant data tags exist and their content is not empty
         EXPECT_STREQ((const char *)node->properties->children->content, "callStart"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "callDuration"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "incoming"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "false"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "callSuccessful"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "true"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "audioInCodec"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "audioOutCodec"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "mediaEncryption"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "None"); // tag value
         node = node->next; // get next data tag
         if (strcmp((const char*)node->properties->children->content, "usbDevice") == 0)
         {
            EXPECT_STREQ((const char *)node->properties->children->content, "usbDevice"); // tag name
            EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
            node = node->next; // get next data tag
         }
         EXPECT_STREQ((const char *)node->properties->children->content, "channel"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "channel01"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "serviceType"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "LAN"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "remoteIdentity"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "channel01"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "responsesReceived"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "2"); // tag value
      });
      waitFor(sendReponse);
   }

   bobAnalyticsManager->sendReport(ahBob);

   // Wait for response from report
   {
      auto sendReponse = std::async(std::launch::async, [&]()
      {
         AnalyticsHandle h;
         OnReportCreatedSuccessEvent evt;
         ASSERT_TRUE(cpcWaitForEvent(bob.analyticsEvents, "AnalyticsHandlerInt::onReportCreatedSuccess", 15000, AlwaysTruePred(), h, evt));
         safeCout("Analytics1ModuleTest::PttServiceTest(): XML: " << evt.content.c_str());

         // Make sure document is not empty
         ASSERT_NE(evt.content, cpc::string());
         // Make sure settings_data tag exists
         bool exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data[1]");
         ASSERT_TRUE(exists);
         // Make sure account_list tag exists
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list[1]");
         ASSERT_TRUE(exists);
         // Make sure at least 2 account tags exists
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list/account[1]");
         ASSERT_TRUE(exists);
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list/account[2]");
         ASSERT_TRUE(exists);
         // Make sure activity_data tag exists
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data[1]");
         ASSERT_TRUE(exists);
         // Make sure call_list tag exists
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data/call_list[1]");
         ASSERT_TRUE(exists);
         // Make sure a call tag exists
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data/call_list/call[1]");
         ASSERT_TRUE(exists);

         _xmlNode* node = getXmlNode(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list/account[1]");   // PTT is first account SIP is second
         // Make sure it's a PTT account
         ASSERT_EQ(strncmp((const char *)node->parent->properties->children->content, "PTT", strlen("PTT")), 0);
         // Make sure relevant data tags exist and their content is not empty
         EXPECT_STREQ((const char *)node->properties->children->content, "domain"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, bobDomain.c_str()); // tag value

         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "username"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, bobServiceSettings.getPrimaryLocalIdentity().c_str()); // tag value

         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "protocol"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "PTT"); // tag value

         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "enabled"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "true"); // tag value

         node = getXmlNode(evt.content.c_str(), "/cpc_usage_report/activity_data/call_list/call[1]");
         // Make sure it's a PTT call
         ASSERT_EQ(strncmp((const char *)node->parent->properties->children->content, "PTT", strlen("PTT")), 0);

         // make sure relevant data tags exist and their content is not empty
         EXPECT_STREQ((const char *)node->properties->children->content, "callStart"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "callDuration"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "incoming"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "true"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "callSuccessful"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "true"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "audioInCodec"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "audioOutCodec"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "mediaEncryption"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "None"); // tag value
         node = node->next; // get next data tag
         if (strcmp((const char*)node->properties->children->content, "usbDevice") == 0)
         {
            EXPECT_STREQ((const char *)node->properties->children->content, "usbDevice"); // tag name
            EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
            node = node->next; // get next data tag
         }
         EXPECT_STREQ((const char *)node->properties->children->content, "channel"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "channel01"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "serviceType"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "LAN"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "remoteIdentity"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "sip:<EMAIL>"); // tag value
      });
      waitFor(sendReponse);
   }

   maxAnalyticsManager->sendReport(ahMax);

   // Wait for response from report
   {
      auto sendReponse = std::async(std::launch::async, [&]()
      {
         AnalyticsHandle h;
         OnReportCreatedSuccessEvent evt;
         ASSERT_TRUE(cpcWaitForEvent(max.analyticsEvents, "AnalyticsHandlerInt::onReportCreatedSuccess", 15000, AlwaysTruePred(), h, evt));
         safeCout("Analytics1ModuleTest::PttServiceTest(): XML: " << evt.content.c_str());

         // Make sure document is not empty
         ASSERT_NE(evt.content, cpc::string());
         // Make sure settings_data tag exists
         bool exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data[1]");
         ASSERT_TRUE(exists);
         // Make sure account_list tag exists
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list[1]");
         ASSERT_TRUE(exists);
         // Make sure at least 2 account tags exists
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list/account[1]");
         ASSERT_TRUE(exists);
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list/account[2]");
         ASSERT_TRUE(exists);
         // Make sure activity_data tag exists
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data[1]");
         ASSERT_TRUE(exists);
         // Make sure call_list tag exists
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data/call_list[1]");
         ASSERT_TRUE(exists);
         // Make sure a call tag exists
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data/call_list/call[1]");
         ASSERT_TRUE(exists);

         _xmlNode* node = getXmlNode(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list/account[1]");   // PTT is first account SIP is second
         // Make sure it's a PTT account
         ASSERT_EQ(strncmp((const char *)node->parent->properties->children->content, "PTT", strlen("PTT")), 0);
         // Make sure relevant data tags exist and their content is not empty
         EXPECT_STREQ((const char *)node->properties->children->content, "domain"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, maxDomain.c_str()); // tag value

         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "username"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, maxServiceSettings.getPrimaryLocalIdentity().c_str()); // tag value

         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "protocol"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "PTT"); // tag value

         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "enabled"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "true"); // tag value

         node = getXmlNode(evt.content.c_str(), "/cpc_usage_report/activity_data/call_list/call[1]");
         // Make sure it's a PTT call
         ASSERT_EQ(strncmp((const char *)node->parent->properties->children->content, "PTT", strlen("PTT")), 0);

         // make sure relevant data tags exist and their content is not empty
         EXPECT_STREQ((const char *)node->properties->children->content, "callStart"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "callDuration"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "incoming"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "true"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "callSuccessful"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "true"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "audioInCodec"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "audioOutCodec"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "mediaEncryption"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "None"); // tag value
         node = node->next; // get next data tag
         if (strcmp((const char*)node->properties->children->content, "usbDevice") == 0)
         {
            EXPECT_STREQ((const char *)node->properties->children->content, "usbDevice"); // tag name
            EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
            node = node->next; // get next data tag
         }
         EXPECT_STREQ((const char *)node->properties->children->content, "channel"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "channel01"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "serviceType"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "LAN"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "remoteIdentity"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "sip:<EMAIL>"); // tag value
      });
      waitFor(sendReponse);
   }

   alicePttManager->shutdownService(alicePttService);
   assertPttServiceShutdownComplete(alice, alicePttService);
   bobPttManager->shutdownService(bobPttService);
   assertPttServiceShutdownComplete(bob, bobPttService);
   maxPttManager->shutdownService(maxPttService);
   assertPttServiceShutdownComplete(max, maxPttService);

   // Close the module
   aliceAnalyticsManager->close(ahAlice);
   bobAnalyticsManager->close(ahBob);
   maxAnalyticsManager->close(ahMax);
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
}

#define GARBAGE_NAME "SomeGarbageItem"

void Analytics1ModuleTest::makeSipCall(TestAccount& alice, TestAccount& bob)
{
   // Alice calls Bob then Bob hangs up
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceConversationEvents = std::async(std::launch::async, [&]()
   {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);

      SipConversationHandle h;
      ConversationStateChangedEvent evt;
      ASSERT_TRUE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onConversationStateChanged", 15000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt)) << " missed conversation changed event";
      ASSERT_EQ(ConversationState_Connected, evt.conversationState);

      std::this_thread::sleep_for(std::chrono::milliseconds(2000));

      assertConversationEnded_time(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely, 20000);
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
   });

   auto bobConversationEvents = std::async(std::launch::async, [&]()
   {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      bob.conversation->sendRingingResponse(bobCall);
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      bob.conversation->accept(bobCall);
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      bob.conversation->end(bobCall);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
   });

   waitFor2(aliceConversationEvents, bobConversationEvents);
}

void Analytics1ModuleTest::makePttCall(TestAccount& alice, TestAccount& bob, const PushToTalkServiceHandle& alicePttService, const PushToTalkServiceHandle& bobPttService, PttIdentity& aliceSipIdentity)
{
   PushToTalkManager* alicePttManager = PushToTalkManager::getInterface(alice.phone);
   PushToTalkManager* bobPttManager = PushToTalkManager::getInterface(bob.phone);

   {
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      auto aliceEvents2 = std::async(std::launch::async, [&]()
      {
         PushToTalkSessionHandle alicePtt = alicePttManager->createPttSession(alicePttService);
         alicePttManager->setChannel(alicePtt, "channel01");
         alicePttManager->start(alicePtt);
         assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Initiated);
         assertPttClientOfferEvent(alice, alicePtt);
         assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Active);

         // Initiate talk spurt
         alicePttManager->startTalkSpurt(alicePtt);
         assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Talking);
         std::this_thread::sleep_for(std::chrono::milliseconds(4000));
         alicePttManager->endTalkSpurt(alicePtt);
         assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Active);

         alicePttManager->end(alicePtt);
         assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Ending);
         assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Idle);
      });

      auto bobEvents2 = std::async(std::launch::async, [&]()
      {
         PushToTalkSessionHandle bobPtt = 0;
         PushToTalkServiceHandle bobService = bobPttService;
         assertPttIncomingCallEx2(bob, bobPtt, aliceSipIdentity.userName, aliceSipIdentity.displayName, bobService);
         bobPttManager->accept(bobPtt);
         assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Initiated);
         assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Active);
         assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Talking);
         assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Ending);
         assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Idle);
      });

      waitFor2(aliceEvents2, bobEvents2);
   }
}

void Analytics1ModuleTest::verifySipAndPttCallReport(TestAccount& alice, TestAccount& bob, AnalyticsHandle& ahAlice, AnalyticsHandle& ahBob)
{
   alice.analyticsManager->sendReport(ahAlice);

   // Wait for response from report
   {
      auto sendReponse = std::async(std::launch::async, [&]()
      {
         AnalyticsHandle h;
         OnReportCreatedSuccessEvent evt;
         ASSERT_TRUE(cpcWaitForEvent(alice.analyticsEvents, "AnalyticsHandlerInt::onReportCreatedSuccess", 15000, AlwaysTruePred(), h, evt));

         safeCout("Send UEM document: " << evt.content);

         // Make sure document is not empty
         ASSERT_NE(evt.content, cpc::string());
         // Make sure the document does not contain non-recognized values
         size_t npos = cpc::string::npos; // Linux compiler didn't like having this below
         ASSERT_EQ(evt.content.find(GARBAGE_NAME), npos);

         xmlNode* node = getXmlNode(evt.content.c_str(), "/cpc_usage_report/settings_data");
         EXPECT_STREQ((const char *)node->properties->children->content, "directoryConfiguration"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "true"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "historyDefaultAccount"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "false"); // tag value
         node = node->next; // get next data tag

         node = getXmlNode(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list/account[2]"); // PTT is first account, SIP is second
         // Make sure id = SIP*
         ASSERT_EQ(strncmp((const char *)node->parent->properties->children->content, "SIP", strlen("SIP")), 0);
         // Make sure relevant data tags exist and their content is not empty
         EXPECT_STREQ((const char *)node->properties->children->content, "domain"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, alice.config.settings.domain); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "username"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, alice.config.settings.username); // tag value
         node = node->next; // get next data tag
         // Only include the outbound proxy if it's enabled.
         if (!alice.config.settings.outboundProxy.empty())
         {
            EXPECT_STREQ((const char *)node->properties->children->content, "outboundProxy"); // tag name
            EXPECT_STREQ((const char *)node->properties->next->children->content, alice.config.settings.outboundProxy); // tag value
            node = node->next; // get next data tag
         }

         EXPECT_STREQ((const char *)node->properties->children->content, "protocol"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "SIP"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "enabled"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "true"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "sipRefreshWifiInterval"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "33"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "sipSimpleSupported"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "true"); // tag value

         bool exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data[1]");
         ASSERT_TRUE(exists);
         // Make sure presence tag exists
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data/call_list[1]");
         ASSERT_TRUE(exists);
         // Make sure provisioning tag exists
         exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data/call_list/call[1]");
         ASSERT_TRUE(exists);
         node = getXmlNode(evt.content.c_str(), "/cpc_usage_report/activity_data/call_list/call[1]");
         // Make sure id = SIP*
         ASSERT_EQ(strncmp((const char *)node->parent->properties->children->content, "SIP", strlen("SIP")), 0);

         // Make sure relevant data tags exist and their content is not empty
         EXPECT_STREQ((const char *)node->properties->children->content, "callStart"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "callDuration"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "incoming"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "false"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "callSuccessful"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "true"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "numDigitsDialed"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "callTransfer"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "false"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "audioInCodec"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "audioOutCodec"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "recordedCall"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
         node = node->next; // get next data tag
         if (strcmp((const char*)node->properties->children->content, "usbDevice") == 0)
         {
            EXPECT_STREQ((const char *)node->properties->children->content, "usbDevice"); // tag name
            EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
            node = node->next; // get next data tag
         }
         EXPECT_STREQ((const char *)node->properties->children->content, "oneWayAudio"); // tag name
         // Swapping the mock network above causes one way audio to get flagged
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "poorNetworkQualityIndicated"); // tag name
         EXPECT_STRNE((const char *)node->properties->next->children->content, ""); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "dataNetworkType"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "WIFI"); // tag value

         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "callHoldUsed"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "false"); // tag value
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "networkIpChange"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, "false"); // tag value

         cpc::string uri = "sip:" + alice.config.settings.username + "@" + alice.config.settings.domain;
         node = node->next; // get next data tag
         EXPECT_STREQ((const char *)node->properties->children->content, "callOrigin"); // tag name
         EXPECT_STREQ((const char *)node->properties->next->children->content, uri); // tag value
         node = node->next; // get next data tag
      });
      waitFor(sendReponse);
   }
}

void Analytics1ModuleTest::configureSipAndPttAccounts(TestAccount& alice, TestAccount& bob, PushToTalkServiceHandle& alicePttService, PushToTalkServiceHandle& bobPttService, AnalyticsHandle& ahAlice, AnalyticsHandle& ahBob, PttIdentity& aliceSipIdentity)
{
   const int unicastPort = 40001;

   SipAccountSettings settingsWIFI = alice.config.settings;
   settingsWIFI.registrationIntervalSeconds = 33;

   ASSERT_EQ(alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings), kSuccess);
   ASSERT_EQ(alice.account->configureTransportAccountSettings(alice.handle, settingsWIFI, TransportWiFi), kSuccess);
   ASSERT_EQ(alice.account->applySettings(alice.handle), kSuccess);

   // Set up the PTT services
   cpc::vector<PttIdentity> aliceIdentities;
   aliceIdentities.push_back(aliceSipIdentity);
   std::string aliceDomain("counterpath.com");
   PushToTalkServiceSettings aliceServiceSettings;
   aliceServiceSettings.localIdentities = aliceIdentities;
   aliceServiceSettings.unicastPort = unicastPort;
   aliceServiceSettings.unicastBindAddress = "127.0.0.1";
   aliceServiceSettings.unicastIpRanges = PttTestHelper::getUnicastIpRange(2);
   aliceServiceSettings.mediaInactivityIntervalSeconds = 5;
   aliceServiceSettings.subscribedChannels.push_back("channel01");
   PushToTalkSettingsInternal aliceInternalSettings;
   aliceInternalSettings.outgoingSessionExpiryMsecs = 5000;

   PttIdentity bobSipIdentity(PushToTalk::PttIdentityType_SIP, "<EMAIL>", "Bob");
   cpc::vector<PttIdentity> bobIdentities;
   bobIdentities.push_back(bobSipIdentity);
   std::string bobDomain("counterpath.com");
   PushToTalkServiceSettings bobServiceSettings;
   bobServiceSettings.localIdentities = bobIdentities;
   bobServiceSettings.unicastPort = unicastPort;
   bobServiceSettings.unicastBindAddress = "*********";
   bobServiceSettings.unicastIpRanges = PttTestHelper::getUnicastIpRange(2);
   bobServiceSettings.mediaInactivityIntervalSeconds = 5;
   bobServiceSettings.subscribedChannels.push_back("channel01");
   PushToTalkSettingsInternal bobInternalSettings;
   bobInternalSettings.outgoingSessionExpiryMsecs = 5000;

   PushToTalkManager* alicePttManager = PushToTalkManager::getInterface(alice.phone);
   alicePttService = alicePttManager->createPttService();
   alicePttManager->setHandler(alicePttService, (PushToTalkHandler*)0xDEADBEEF);
   alicePttManager->configureService(alicePttService, aliceServiceSettings);
   PushToTalkManagerInternal* alicePttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(alicePttManager);

   PushToTalkManager* bobPttManager = PushToTalkManager::getInterface(bob.phone);
   bobPttService = bobPttManager->createPttService();
   bobPttManager->setHandler(bobPttService, (PushToTalkHandler*)0xDEADBEEF);
   bobPttManager->configureService(bobPttService, bobServiceSettings);
   PushToTalkManagerInternal* bobPttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(bobPttManager);

   alicePttManagerInternal->setPttInternalSettings(alicePttService, aliceInternalSettings);
   bobPttManagerInternal->setPttInternalSettings(bobPttService, bobInternalSettings);

   // Avoid enabling account earlier, so it doesn't briefly disable then enable when we apply updated account settings
   alice.enable();

   alicePttManager->startService(alicePttService);
   bobPttManager->startService(bobPttService);

   auto aliceEvents = std::async(std::launch::async, [&]()
   {
      assertPttServiceConfigured(alice, alicePttService);
      assertPttServiceStartupCompleteLan(alice, alicePttService);
   });

   auto bobEvents = std::async(std::launch::async, [&]()
   {
      assertPttServiceConfigured(bob, bobPttService);
      assertPttServiceStartupCompleteLan(bob, bobPttService);
   });

   waitFor2(aliceEvents, bobEvents);

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   // Get the analytics module created by testAccount class
   AnalyticsManager* aliceAnalyticsManager = alice.analyticsManager;
   AnalyticsManager* bobAnalyticsManager = bob.analyticsManager;

   // Set the basic stats to the default handler which is always (1),
   // the third paramater is optional and is only used by the test cases due
   // to issue with timing of creating module & account getting configured.
   // In real scenario, the app layer will never pass in the third parameter.
   ahAlice = openModuleWithEmptyServerUrl(aliceAnalyticsManager);
   ahBob = openModuleWithEmptyServerUrl(bobAnalyticsManager);

   SettingsStats settingsStats;
   settingsStats.insert(cpc::pair<cpc::string, cpc::string>("directoryConfiguration", "true"));
   settingsStats.insert(cpc::pair<cpc::string, cpc::string>("historyDefaultAccount", "false"));
   settingsStats.insert(cpc::pair<cpc::string, cpc::string>(GARBAGE_NAME, "whatever"));
   alice.analyticsManager->setSettingsStats(ahAlice, settingsStats);
}

TEST_F(Analytics1ModuleTest, PttAndSipCallTest)
{
   // This test account creates the phone, the analytics module, calls open()
   // on module with default values, and creates/init/enable the Sip account
   TestAccount alice("alice", Account_Init);
   TestAccount bob("bob");

   PushToTalkManager* alicePttManager = PushToTalkManager::getInterface(alice.phone);
   PttIdentity aliceSipIdentity(PushToTalk::PttIdentityType_SIP, "<EMAIL>", "Alice");
   PushToTalkServiceHandle alicePttService = 0;
   PushToTalkManager* bobPttManager = PushToTalkManager::getInterface(bob.phone);
   PushToTalkServiceHandle bobPttService = 0;
   AnalyticsHandle ahAlice = 0;
   AnalyticsHandle ahBob = 0;

   configureSipAndPttAccounts(alice, bob, alicePttService, bobPttService, ahAlice, ahBob, aliceSipIdentity);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   makeSipCall(alice, bob);
   makePttCall(alice, bob, alicePttService, bobPttService, aliceSipIdentity);
   verifySipAndPttCallReport(alice, bob, ahAlice, ahBob);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   alicePttManager->shutdownService(alicePttService);
   assertPttServiceShutdownComplete(alice, alicePttService);
   bobPttManager->shutdownService(bobPttService);
   assertPttServiceShutdownComplete(bob, bobPttService);

   // Close the module
   AnalyticsManager* aliceAnalyticsManager = alice.analyticsManager;
   AnalyticsManager* bobAnalyticsManager = bob.analyticsManager;
   aliceAnalyticsManager->close(ahAlice);
   bobAnalyticsManager->close(ahBob);
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
}

#if (CPCAPI2_BRAND_XMPP_ACCOUNT_MODULE == 1)
   TEST_F(Analytics1ModuleTest, XmppAccountTest) {

       // This test account creates the phone, the analytics module, calls open() 
       //on module with default values, and creates/init/enable the Sip account
       TestAccount alice2("alice2");

       //get phone from the TestAccount Class
       Phone* phone = alice2.phone;
       SipAccountManager* acct = SipAccountManager::getInterface(phone);
       XmppAccountManager* xmppAcct = XmppAccountManager::getInterface(phone);

       //init test xmpp account
       XmppTestAccount aliceXmpp("alice", Account_Init, "", phone);
       XmppTestAccountConfig& xmppConfig = aliceXmpp.config;
       aliceXmpp.enable(true, NULL, false);  // skip checking for state changes since test framework doesn't support this with multiple accounts

       //get the analytics module created by testAccount class
       AnalyticsManager *analyticsManager = alice2.analyticsManager;

       //set the basic stats to the default handler which is always (1)
       //the third paramater is optional and is only used by the test cases due
       //to issue with timing of creating module & account getting configured.
       //In real scenario, the app layer will never pass in the third parameter.
       AnalyticsHandle ah = openModuleWithEmptyServerUrl(analyticsManager);
       std::this_thread::sleep_for(std::chrono::milliseconds(3000));

       analyticsManager->sendReport(ah);

       //Wait for response from report
       {
           auto sendReponse = std::async(std::launch::async, [&]() {
               AnalyticsHandle h;
               OnReportCreatedSuccessEvent evt;
               ASSERT_TRUE(cpcWaitForEvent(
                   alice2.analyticsEvents,
                   "AnalyticsHandlerInt::onReportCreatedSuccess",
                   15000,
                   AlwaysTruePred(),
                   h, evt));

               //make sure document is not empty
               ASSERT_NE(evt.content, cpc::string());
               //make sure settings_data tag exists
               bool exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data[1]");
               ASSERT_TRUE(exists);
               //make sure account_list tag exists
               exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list[1]");
               ASSERT_TRUE(exists);
               //make sure a second account tag exists
               exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list/account[2]");
               ASSERT_TRUE(exists);

               _xmlNode* node = getXmlNode(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list/account[2]");
               //make sure id = XMPP*
               ASSERT_EQ(strncmp((const char *)node->parent->properties->children->content, "XMPP", strlen("XMPP")), 0);
               //make sure relevant data tags exist and their content is not empty
               EXPECT_STREQ((const char *)node->properties->children->content, "domain"); //tag name
               EXPECT_STREQ((const char *)node->properties->next->children->content, xmppConfig.settings.domain); //tag value
               node = node->next; // get next data tag
               EXPECT_STREQ((const char *)node->properties->children->content, "username"); //tag name
               EXPECT_STREQ((const char *)node->properties->next->children->content, xmppConfig.settings.username); //tag value
               node = node->next; // get next data tag
               EXPECT_STREQ((const char *)node->properties->children->content, "protocol"); //tag name
               EXPECT_STREQ((const char *)node->properties->next->children->content, "XMPP"); //tag value
               node = node->next; // get next data tag
               EXPECT_STREQ((const char *)node->properties->children->content, "enabled"); //tag name
               EXPECT_STREQ((const char *)node->properties->next->children->content, "true"); //tag value
               node = node->next; // get next data tag
               EXPECT_STREQ((const char *)node->properties->children->content, "sipSimpleSupported"); //tag name
               EXPECT_STREQ((const char *)node->properties->next->children->content, "false"); //tag value
               node = node->next; // get next data tag
               EXPECT_STREQ((const char *)node->properties->children->content, "signalingTransport"); //tag name
               EXPECT_STRNE((const char *)node->properties->next->children->content, ""); //tag value
           });
           waitFor(sendReponse);
       }

       //close the module
       analyticsManager->close(ah);
       std::this_thread::sleep_for(std::chrono::milliseconds(1000));

       aliceXmpp.destroy(false); // without this we'll get test failure because the next event is not as expected
   }
#endif // #if (CPCAPI2_BRAND_XMPP_ACCOUNT_MODULE == 1)

#if (CPCAPI2_BRAND_XMPP_ACCOUNT_MODULE == 1)
   TEST_F(Analytics1ModuleTest, XmppAccountFailureTest) {

       // This test account creates the phone, the analytics module, calls open() 
       //on module with default values, and creates/init/enable the Sip account
       TestAccount alice2("alice2");

       //get phone from the TestAccount Class
       Phone* phone = alice2.phone;
       SipAccountManager* acct = SipAccountManager::getInterface(phone);
       XmppAccountManager* xmppAcct = XmppAccountManager::getInterface(phone);

       //init test xmpp account
       XmppTestAccount aliceXmpp("alice", Account_Init, "", phone);
       aliceXmpp.enable(true, NULL, false);  // skip checking for state changes since test framework doesn't support this with multiple accounts

       //get the analytics module created by testAccount class
       AnalyticsManager *analyticsManager = alice2.analyticsManager;

       //set the basic stats to the default handler which is always (1)
       //the third paramater is optional and is only used by the test cases due
       //to issue with timing of creating module & account getting configured.
       //In real scenario, the app layer will never pass in the third parameter.
       AnalyticsHandle ah = openModuleWithEmptyServerUrl(analyticsManager);
       std::this_thread::sleep_for(std::chrono::milliseconds(3000));

       XmppAccountStatusChangedEvent e;
       e.accountStatus = XmppAccountStatusChangedEvent::Status_Disconnected;
       e.errorCode = Error::Error_AuthenticationFailed;
       static_cast<AnalyticsManagerInt*>(analyticsManager)->xmppAccountStatusChangeFired(aliceXmpp.handle, e);
       static_cast<AnalyticsManagerInt*>(analyticsManager)->xmppAccountStatusChangeFired(aliceXmpp.handle, e);
       static_cast<AnalyticsManagerInt*>(analyticsManager)->sendReport(ah);

       //Wait for response from report
       {
           auto sendReponse = std::async(std::launch::async, [&]() {
               AnalyticsHandle h;
               OnReportCreatedSuccessEvent evt;
               ASSERT_TRUE(cpcWaitForEvent(
                   alice2.analyticsEvents,
                   "AnalyticsHandlerInt::onReportCreatedSuccess",
                   15000,
                   AlwaysTruePred(),
                   h, evt));

               //make sure document is not empty
               ASSERT_NE(evt.content, cpc::string());
               //make sure settings_data tag exists
               bool exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data[1]");
               ASSERT_TRUE(exists);
               //make sure account_list tag exists
               exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data/account_list[1]");
               ASSERT_TRUE(exists);
               //make sure a second account tag exists
               exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data/account_list/account[2]");
               ASSERT_TRUE(exists);

               _xmlNode* node = getXmlNode(evt.content.c_str(), "/cpc_usage_report/activity_data/account_list/account[2]");
               //make sure id = XMPP*
               ASSERT_EQ(strncmp((const char *)node->parent->properties->children->content, "XMPP", strlen("XMPP")), 0);
               //make sure relevant data tags exist and their content is not empty
               EXPECT_STREQ((const char *)node->properties->children->content, "failedRegistrations"); //tag name
               EXPECT_STREQ((const char *)node->properties->next->children->content, "2"); //tag value
           });
           waitFor(sendReponse);
       }

       //close the module
       analyticsManager->close(ah);
       std::this_thread::sleep_for(std::chrono::milliseconds(1000));

       aliceXmpp.destroy(false); // without this we'll get test failure because the next event is not as expected
   }
#endif // #if (CPCAPI2_BRAND_XMPP_ACCOUNT_MODULE == 1)

#if (CPCAPI2_BRAND_XMPP_ACCOUNT_MODULE == 1)
   TEST_F(Analytics1ModuleTest, InstantMessageTest) {

       // This test account creates the phone, the analytics module, calls open() 
       //on module with default values, and creates/init/enable the Sip account
       TestAccount alice("alice");

        //get phone from the TestAccount Class
        Phone* phone = alice.phone;
        SipAccountManager* acct = SipAccountManager::getInterface(phone);
        XmppAccountManager* xmppAcct = XmppAccountManager::getInterface(phone);

        //init test xmpp account
        XmppTestAccount aliceXmpp("alice");
        XmppTestAccountConfig& xmppConfig = aliceXmpp.config;
        XmppAccountHandle xmppHandle = xmppAcct->create(xmppConfig.settings);
        xmppAcct->applySettings(xmppHandle);
        // Make sure some events happen
        xmppAcct->process(XmppAccountManager::kBlockingModeNonBlocking);

       //get the analytics module created by testAccount class
       AnalyticsManager *analyticsManager = alice.analyticsManager;
       AnalyticsHandle ah = openModuleWithEmptyServerUrl(analyticsManager);

       CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent arg;
       arg.accountStatus = CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status_Connected;
       static_cast<AnalyticsManagerInt*>(analyticsManager)->xmppAccountStatusChangeFired(xmppHandle, arg);
       std::this_thread::sleep_for(std::chrono::milliseconds(3000));

       //make some calls to instantMessageInfoFired
       // this is a bit of a hack just to test that the correct values were added to the registry & final XML doc
       // We cant simulate a real IM scenario because of the current implementation of TestAccount class
       // It creates its own instance of Phone object which the Analytics Interface is connected to.
       // But when the SIP/XMPP IM interface trys to retrieve the Analytics interface to use, it gets a new copy based on a new phone object
       // This scenario would not happen in real case, because there will always be only one instance of Phone, which implies only one instance of Analytics Interface 
       static_cast<AnalyticsManagerInt*>(analyticsManager)->instantMessageInfoFired(alice.handle, true, true);
       static_cast<AnalyticsManagerInt*>(analyticsManager)->instantMessageInfoFired(xmppHandle, true, false);
       static_cast<AnalyticsManagerInt*>(analyticsManager)->instantMessageInfoFired(alice.handle, false, true);
       static_cast<AnalyticsManagerInt*>(analyticsManager)->instantMessageInfoFired(xmppHandle, false, false);

       analyticsManager->sendReport(ah);

       //Wait for response from report
       {
           auto sendReponse = std::async(std::launch::async, [&]() {
               AnalyticsHandle h;
               OnReportCreatedSuccessEvent evt;
               ASSERT_TRUE(cpcWaitForEvent(
                   alice.analyticsEvents,
                   "AnalyticsHandlerInt::onReportCreatedSuccess",
                   30000,
                   AlwaysTruePred(),
                   h, evt));

               //make sure document is not empty
               ASSERT_NE(evt.content, cpc::string());
               //make sure settings_data tag exists
               bool exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data[1]");
               ASSERT_TRUE(exists);
               //make sure account_list tag exists
               exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data/account_list[1]");
               ASSERT_TRUE(exists);
               //make sure a first account tag exists
               exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data/account_list/account[1]");
               ASSERT_TRUE(exists);

               _xmlNode* node = getXmlNode(evt.content.c_str(), "/cpc_usage_report/activity_data/account_list/account[1]");
               //make sure id = SIP*
               ASSERT_EQ(strncmp((const char *)node->parent->properties->children->content, "SIP", strlen("SIP")), 0);
               //make sure relevant data tags exist and their content is not empty
               EXPECT_STREQ((const char *)node->properties->children->content, "outgoingIms"); //tag name
               EXPECT_STREQ((const char *)node->properties->next->children->content, "1"); //tag value
               node = node->next;
               //make sure relevant data tags exist and their content is not empty
               EXPECT_STREQ((const char *)node->properties->children->content, "incomingIms"); //tag name
               EXPECT_STREQ((const char *)node->properties->next->children->content, "1"); //tag value

               //make sure a second account tag exists
               exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data/account_list/account[2]");
               ASSERT_TRUE(exists);

               node = getXmlNode(evt.content.c_str(), "/cpc_usage_report/activity_data/account_list/account[2]");
               //make sure id = XMPP*
               ASSERT_EQ(strncmp((const char *)node->parent->properties->children->content, "XMPP", strlen("XMPP")), 0);
               //make sure relevant data tags exist and their content is not empty
               EXPECT_STREQ((const char *)node->properties->children->content, "outgoingIms"); //tag name
               EXPECT_STREQ((const char *)node->properties->next->children->content, "1"); //tag value
               node = node->next;
               //make sure relevant data tags exist and their content is not empty
               EXPECT_STREQ((const char *)node->properties->children->content, "incomingIms"); //tag name
               EXPECT_STREQ((const char *)node->properties->next->children->content, "1"); //tag value
           });
           waitFor(sendReponse);
       }

       

       //close the module
       analyticsManager->close(ah);
       std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   }
#endif // #if (CPCAPI2_BRAND_XMPP_ACCOUNT_MODULE == 1)

   TEST_F(Analytics1ModuleTest, ActivityDataTest) {

       // This test account creates the phone, the analytics module, calls open() 
       //on module with default values, and creates/init/enable the Sip account
       TestAccount alice("alice");

       //get phone from the TestAccount Class
       Phone* phone = alice.phone;
       SipAccountManager* acct = SipAccountManager::getInterface(phone);

       //get the analytics module created by testAccount class
       AnalyticsManager *analyticsManager = alice.analyticsManager;
       AnalyticsHandle ah = openModuleWithEmptyServerUrl(analyticsManager);
       std::this_thread::sleep_for(std::chrono::milliseconds(3000));

       // Set some stats
       PresenceStats ps;
       ps.numContacts = 10;
       ps.numContactsWithPresence = 6;
       analyticsManager->setPresenceStats(ah, ps);

       ProvisioningStats provst;
       provst.failedProvisionAttempts = 1;
       provst.successfulProvisionAttempts = 1;
       analyticsManager->setProvisioningStats(ah, provst);

       StabilityStats ss;
       ss.numCrashes = 6000; // probably this number required before I call this feature stable :)
       analyticsManager->setStabilityStats(ah, ss);
       analyticsManager->sendReport(ah);

     //Wait for response from report
     {
        auto sendReponse = std::async(std::launch::async, [&]() {
            AnalyticsHandle h;
            OnReportCreatedSuccessEvent evt;
            ASSERT_TRUE(cpcWaitForEvent(
            alice.analyticsEvents,
            "AnalyticsHandlerInt::onReportCreatedSuccess",
            15000,
            AlwaysTruePred(),
            h, evt));

            //make sure document is not empty
            ASSERT_NE(evt.content, cpc::string());
            //make sure activity_data tag exists
            bool exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data[1]");
            ASSERT_TRUE(exists);
            //make sure presence tag exists
            exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data/presence[1]");
            ASSERT_TRUE(exists);
            //make sure provisioning tag exists
            exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data/provisioning[1]");
            ASSERT_TRUE(exists);
            //make sure stability tag exists
            exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data/stability[1]");
            ASSERT_TRUE(exists);

            _xmlNode* node = getXmlNode(evt.content.c_str(), "/cpc_usage_report/activity_data/presence[1]");
            //make sure relevant data tags exist and their content is not empty
            EXPECT_STREQ((const char *)node->properties->children->content, "numContacts"); //tag name
            EXPECT_STREQ((const char *)node->properties->next->children->content, "10"); //tag value
            node = node->next; // get next data tag
            EXPECT_STREQ((const char *)node->properties->children->content, "numContactsWithPresence"); //tag name
            EXPECT_STREQ((const char *)node->properties->next->children->content, "6"); //tag value
            
            node = getXmlNode(evt.content.c_str(), "/cpc_usage_report/activity_data/provisioning[1]"); // get next tag
            EXPECT_STREQ((const char *)node->properties->children->content, "successfulProvisionAttempts"); //tag name
            EXPECT_STREQ((const char *)node->properties->next->children->content, "1"); //tag value
            node = node->next; // get next data tag
            EXPECT_STREQ((const char *)node->properties->children->content, "failedProvisionAttempts"); //tag name
            EXPECT_STREQ((const char *)node->properties->next->children->content, "1"); //tag value
            
            node = getXmlNode(evt.content.c_str(), "/cpc_usage_report/activity_data/stability[1]"); // get next tag
            EXPECT_STREQ((const char *)node->properties->children->content, "numCrashes"); //tag name
            EXPECT_STREQ((const char *)node->properties->next->children->content, "6000"); //tag value
        });
        waitFor(sendReponse);
    }

       //close the module
       analyticsManager->close(ah);
       std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   }

    TEST_F(Analytics1ModuleTest, GeneralDataTest) {

        // This test account creates the phone, the analytics module, calls open() 
        //on module with default values, and creates/init/enable the Sip account
        TestAccount alice("alice");

        //get phone from the TestAccount Class
        Phone* phone = alice.phone;
        SipAccountManager* acct = SipAccountManager::getInterface(phone);

        //get the analytics module created by testAccount class
        AnalyticsManager *analyticsManager = alice.analyticsManager;
        AnalyticsHandle ah = openModuleWithEmptyServerUrl(analyticsManager);
        std::this_thread::sleep_for(std::chrono::milliseconds(3000));

        analyticsManager->sendReport(ah);

        //Wait for response from report
        {
            auto sendReponse = std::async(std::launch::async, [&]() {
                AnalyticsHandle h;
                OnReportCreatedSuccessEvent evt;
                ASSERT_TRUE(cpcWaitForEvent(
                alice.analyticsEvents,
                "AnalyticsHandlerInt::onReportCreatedSuccess",
                15000,
                AlwaysTruePred(),
                h, evt));

                //make sure document is not empty
                ASSERT_NE(evt.content, cpc::string());
                //make sure settings_data tag exists
                bool exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/general[1]");
                ASSERT_TRUE(exists);

                _xmlNode* node = getXmlNode(evt.content.c_str(), "/cpc_usage_report/general[1]");
                //make sure relevant data tags exist and their content is not empty
                EXPECT_STREQ((const char *)node->properties->children->content, "deviceUUID"); //tag name
                EXPECT_STREQ((const char *)node->properties->next->children->content, "518E27C9-A499-4A73-87EF-1595682F7772"); //tag value
                node = node->next; // get next data tag
                EXPECT_STREQ((const char *)node->properties->children->content, "clientVersion"); //tag name
                EXPECT_STREQ((const char *)node->properties->next->children->content, "4.4"); //tag value
                node = node->next; // get next data tag
                EXPECT_STREQ((const char *)node->properties->children->content, "installationDate"); //tag name
                EXPECT_STREQ((const char *)node->properties->next->children->content, "**********"); //tag value
                node = node->next; // get next data tag
                EXPECT_STREQ((const char *)node->properties->children->content, "osType"); //tag name
                EXPECT_STREQ((const char *)node->properties->next->children->content, "Win32"); //tag value
                node = node->next; // get next data tag
                EXPECT_STREQ((const char *)node->properties->children->content, "osVersion"); //tag name
                EXPECT_STREQ((const char *)node->properties->next->children->content, "7"); //tag value
                node = node->next; // get next data tag
                EXPECT_STREQ((const char *)node->properties->children->content, "hardwareModel"); //tag name
                EXPECT_STREQ((const char *)node->properties->next->children->content, "Clone"); //tag value
                node = node->next; // get next data tag
                EXPECT_STREQ((const char *)node->properties->children->content, "clientPublicIpAddress"); //tag name
                EXPECT_STREQ((const char *)node->properties->next->children->content, "*************"); //tag value
                node = node->next; // get next data tag
                EXPECT_STREQ((const char *)node->properties->children->content, "clientLaunchTime"); //tag name
                EXPECT_STREQ((const char *)node->properties->next->children->content, "**********"); //tag value
                node = node->next; // get next data tag
                EXPECT_STREQ((const char *)node->properties->children->content, "xmlTemplateVersion"); //tag name
                EXPECT_STREQ((const char *)node->properties->next->children->content, "1.8"); //tag value
                node = node->next; // get next data tag
                EXPECT_STREQ((const char *)node->properties->children->content, "language"); //tag name
                EXPECT_STREQ((const char *)node->properties->next->children->content, "en.CA"); //tag value
                node = node->next; // get next data tag
                EXPECT_STREQ((const char *)node->properties->children->content, "timezone"); //tag name
                EXPECT_STREQ((const char *)node->properties->next->children->content, "UTC-5:00"); //tag value
                node = node->next; // get next data tag
                EXPECT_STREQ((const char *)node->properties->children->content, "serialNumber"); //tag name
                EXPECT_STREQ((const char *)node->properties->next->children->content, "AABBCCDDEEFF"); //tag value
            });
            waitFor(sendReponse);
        }

        //close the module
        analyticsManager->close(ah);
        std::this_thread::sleep_for(std::chrono::milliseconds(1000));
    }

   TEST_F(Analytics1ModuleTest, AudioCallTest)
   {
       // This test account creates the phone, the analytics module, calls open() 
       //on module with default values, and creates/init/enable the Sip account
       TestAccount alice("alice", Account_Init);

       SipAccountSettings settingsWIFI = alice.config.settings;
       settingsWIFI.registrationIntervalSeconds = 33;
       SipAccountSettings settingsWWAN = alice.config.settings;
       settingsWWAN.registrationIntervalSeconds = 44;

       ASSERT_EQ(alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings), kSuccess);
       ASSERT_EQ(alice.account->configureTransportAccountSettings(alice.handle, settingsWIFI, TransportWiFi), kSuccess);
       ASSERT_EQ(alice.account->configureTransportAccountSettings(alice.handle, settingsWWAN, TransportWWAN), kSuccess);
       ASSERT_EQ(alice.account->applySettings(alice.handle), kSuccess);
       // avoid enabling account earlier, so it doesn't briefly disable then enable when we apply updated account settings
       alice.enable();


       TestAccount bob("bob");

       //get phone from the TestAccount Class
       Phone* phone = alice.phone;
       //get the analytics module created by testAccount class
       AnalyticsHandle aliceHandle = openModuleWithEmptyServerUrl(alice.analyticsManager);
       AnalyticsHandle bobHandle = openModuleWithEmptyServerUrl(bob.analyticsManager);

       SettingsStats settingsStats;
       settingsStats.insert(cpc::pair<cpc::string, cpc::string>("directoryConfiguration", "true"));
       settingsStats.insert(cpc::pair<cpc::string, cpc::string>("historyDefaultAccount", "false"));
#define GARBAGE_NAME "SomeGarbageItem"
       settingsStats.insert(cpc::pair<cpc::string, cpc::string>(GARBAGE_NAME, "whatever"));
       alice.analyticsManager->setSettingsStats(aliceHandle, settingsStats);

       std::this_thread::sleep_for(std::chrono::milliseconds(5000));


       // Alice calls Bob then Bob hangs up
       SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
       alice.conversation->addParticipant(aliceCall, bob.config.uri());
       alice.conversation->start(aliceCall);

       // > 1 network changes are not reliable in this test; we don't wait long enough
       // between network changes, so sometimes we attempt to trigger e.g. 2 network changes
       // but our network changes manager only detects 1
       const int networkChangeCount = 1;
       int n = resip::Random::getCryptoRandom();
       NetworkTransport randNetTrans = n % 2 == 0 ? TransportWiFi : TransportWWAN;

       auto aliceConversationEvents = std::async(std::launch::async, [&]() {
           // Wait for the expected state changes and for the call to end
           assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
           assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
           assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);

            SipConversationHandle h;
            ConversationStateChangedEvent evt;
            ASSERT_TRUE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onConversationStateChanged",
               15000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt)) << "missed conversation changed event";
            ASSERT_EQ(ConversationState_Connected, evt.conversationState);

           std::this_thread::sleep_for(std::chrono::milliseconds(2000));

           assertSuccess(alice.conversation->hold(aliceCall));
           assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt) {
              ASSERT_TRUE(evt.localHold);
              ASSERT_FALSE(evt.remoteHold);
              ASSERT_EQ(MediaDirection_SendOnly, evt.localMediaInfo[0].mediaDirection);
           });
           std::this_thread::sleep_for(std::chrono::milliseconds(3000));
           assertSuccess(alice.conversation->unhold(aliceCall));
           assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt) {
              ASSERT_FALSE(evt.localHold);
              ASSERT_FALSE(evt.remoteHold);
              ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
           });


           const char* mockIfaces[] = { "**************", "**************" };

           for (int i = 0; i < networkChangeCount; i++)
           {
              alice.network->setNetworkTransport(i % 2 == 0 ? NetworkTransport::TransportWWAN : NetworkTransport::TransportWiFi);
              const char* ifaceToSet = mockIfaces[i];
              std::set<resip::Data> ifaces;
              ifaces.insert(ifaceToSet); // NOTE: this has no bearing on the c= line in the SDP offers
              alice.network->setMockInterfaces(ifaces);
              alice.network->setNetworkTransport(randNetTrans);

              // consume these events so call to disable(..) later gets account status change to unregistering/unregistered
              assertAccountRefreshing(alice);
              assertAccountRegistered(alice);

              std::this_thread::sleep_for(std::chrono::milliseconds(1000));
           }

           assertConversationEnded_time(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely, 20000);
          // expect registration refresh after network change during a call
          assertAccountRefreshing(alice);
          assertAccountRegistered(alice);

           std::this_thread::sleep_for(std::chrono::milliseconds(10000));
       });

       auto bobConversationEvents = std::async(std::launch::async, [&]() {
           // Wait for the expected state changes and for the call to end
           SipConversationHandle bobCall;
           assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
           bob.conversation->sendRingingResponse(bobCall);
           assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
           bob.conversation->accept(bobCall);
           assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
           assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

           // alice places call on hold
           assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendOnly);
           assertSuccess(bob.conversation->accept(bobCall));

           // alice unholds the call
           assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
           assertSuccess(bob.conversation->accept(bobCall));

           for (int i = 0; i < networkChangeCount; ++i)
           {
              // handle alice's network change re-INVITEs
              assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
              assertSuccess(bob.conversation->accept(bobCall));
           }

           std::this_thread::sleep_for(std::chrono::milliseconds(5000));
           bob.conversation->end(bobCall);
           assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
           std::this_thread::sleep_for(std::chrono::milliseconds(10000));
       });

       waitFor2(aliceConversationEvents, bobConversationEvents);

       /*
      std::this_thread::sleep_for(std::chrono::milliseconds(3000));
      // Alice calls Bob then Bob hangs up
      aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->addParticipant(aliceCall, bob.config.uri());
      alice.conversation->start(aliceCall);
      auto aliceConversationEvents2 = std::async(std::launch::async, [&]() {
          // Wait for the expected state changes and for the call to end
          assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
          assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
          assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);

           SipConversationHandle h;
           ConversationStateChangedEvent evt;
           ASSERT_TRUE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onConversationStateChanged",
              15000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt)) << "missed conversation changed event";
           ASSERT_EQ(ConversationState_Connected, evt.conversationState);

          std::this_thread::sleep_for(std::chrono::milliseconds(2000));

          assertConversationEnded_time(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely, 20000);
          std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      });

      auto bobConversationEvents2 = std::async(std::launch::async, [&]() {
          // Wait for the expected state changes and for the call to end
          SipConversationHandle bobCall;
          assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
          bob.conversation->sendRingingResponse(bobCall);
          assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
          bob.conversation->accept(bobCall);
          assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
          assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

          std::this_thread::sleep_for(std::chrono::milliseconds(5000));
          bob.conversation->end(bobCall);
          assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
          std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      });

      waitFor2(aliceConversationEvents2, bobConversationEvents2);
      */

       alice.analyticsManager->sendReport(aliceHandle);

       // Wait for response from report
       {
           auto sendReponse = std::async(std::launch::async, [&]() {
               AnalyticsHandle h;
               OnReportCreatedSuccessEvent evt;
               ASSERT_TRUE(cpcWaitForEvent(
               alice.analyticsEvents,
               "AnalyticsHandlerInt::onReportCreatedSuccess",
               15000,
               AlwaysTruePred(),
               h, evt));

               safeCout("Send UEM document: " << evt.content);

               //make sure document is not empty
               ASSERT_NE(evt.content, cpc::string());
               // make sure the document does not contain non-recognized values
               size_t npos = cpc::string::npos; // Linux compiler didn't like having this below
               ASSERT_EQ(evt.content.find(GARBAGE_NAME), npos);

               xmlNode* node = getXmlNode(evt.content.c_str(), "/cpc_usage_report/settings_data");
               EXPECT_STREQ((const char *)node->properties->children->content, "directoryConfiguration"); //tag name
               EXPECT_STREQ((const char *)node->properties->next->children->content, "true"); //tag value
               node = node->next; // get next data tag
               EXPECT_STREQ((const char *)node->properties->children->content, "historyDefaultAccount"); //tag name
               EXPECT_STREQ((const char *)node->properties->next->children->content, "false"); //tag value
               node = node->next; // get next data tag

               node = getXmlNode(evt.content.c_str(), "/cpc_usage_report/settings_data/account_list/account[1]");
               //make sure id = SIP*
               ASSERT_EQ(strncmp((const char *)node->parent->properties->children->content, "SIP", strlen("SIP")), 0);
               //make sure relevant data tags exist and their content is not empty
               EXPECT_STREQ((const char *)node->properties->children->content, "domain"); //tag name
               EXPECT_STREQ((const char *)node->properties->next->children->content, alice.config.settings.domain); //tag value
               node = node->next; // get next data tag
               EXPECT_STREQ((const char *)node->properties->children->content, "username"); //tag name
               //ASSERT_EQ(strncmp((const char *)node->properties->next->children->content, "alice", strlen("alice")), 0);
               EXPECT_STREQ((const char *)node->properties->next->children->content, alice.config.settings.username); //tag value
               node = node->next; // get next data tag
               // Only include the outbound proxy if it's enabled.
               if (!alice.config.settings.outboundProxy.empty())
               {
                  EXPECT_STREQ((const char *)node->properties->children->content, "outboundProxy"); //tag name
                  EXPECT_STREQ((const char *)node->properties->next->children->content, alice.config.settings.outboundProxy); //tag value
                  node = node->next; // get next data tag
               }

               EXPECT_STREQ((const char *)node->properties->children->content, "protocol"); //tag name
               EXPECT_STREQ((const char *)node->properties->next->children->content, "SIP"); //tag value
               node = node->next; // get next data tag
               EXPECT_STREQ((const char *)node->properties->children->content, "enabled"); //tag name
               EXPECT_STREQ((const char *)node->properties->next->children->content, "true"); //tag value
               node = node->next; // get next data tag
               EXPECT_STREQ((const char *)node->properties->children->content, "sipRefreshCellInterval"); //tag name
               EXPECT_STREQ((const char *)node->properties->next->children->content, "44"); //tag value
               node = node->next; // get next data tag
               EXPECT_STREQ((const char *)node->properties->children->content, "sipRefreshWifiInterval"); //tag name
               EXPECT_STREQ((const char *)node->properties->next->children->content, "33"); //tag value
               node = node->next; // get next data tag
               EXPECT_STREQ((const char *)node->properties->children->content, "sipSimpleSupported"); //tag name
               EXPECT_STREQ((const char *)node->properties->next->children->content, "true"); //tag value


               bool exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data[1]");
               ASSERT_TRUE(exists);
               //make sure presence tag exists
               exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data/call_list[1]");
               ASSERT_TRUE(exists);
               //make sure provisioning tag exists
               exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data/call_list/call[1]");
               ASSERT_TRUE(exists);
               node = getXmlNode(evt.content.c_str(), "/cpc_usage_report/activity_data/call_list/call[1]");
               //make sure id = SIP*
               ASSERT_EQ(strncmp((const char *)node->parent->properties->children->content, "SIP", strlen("SIP")), 0);

               //make sure relevant data tags exist and their content is not empty
               EXPECT_STREQ((const char *)node->properties->children->content, "callStart"); //tag name
               EXPECT_STRNE((const char *)node->properties->next->children->content, ""); //tag value
               node = node->next; // get next data tag
               EXPECT_STREQ((const char *)node->properties->children->content, "callDuration"); //tag name
               EXPECT_STRNE((const char *)node->properties->next->children->content, ""); //tag value
               node = node->next; // get next data tag
               EXPECT_STREQ((const char *)node->properties->children->content, "incoming"); //tag name
               EXPECT_STREQ((const char *)node->properties->next->children->content, "false"); //tag value
               node = node->next; // get next data tag
               EXPECT_STREQ((const char *)node->properties->children->content, "callSuccessful"); //tag name
               EXPECT_STREQ((const char *)node->properties->next->children->content, "true"); //tag value
               node = node->next; // get next data tag
               EXPECT_STREQ((const char *)node->properties->children->content, "numDigitsDialed"); //tag name
               EXPECT_STRNE((const char *)node->properties->next->children->content, ""); //tag value
               node = node->next; // get next data tag
               EXPECT_STREQ((const char *)node->properties->children->content, "callTransfer"); //tag name
               EXPECT_STREQ((const char *)node->properties->next->children->content, "false"); //tag value
               node = node->next; // get next data tag
               EXPECT_STREQ((const char *)node->properties->children->content, "audioInCodec"); //tag name
               EXPECT_STRNE((const char *)node->properties->next->children->content, ""); //tag value
               node = node->next; // get next data tag
               EXPECT_STREQ((const char *)node->properties->children->content, "audioOutCodec"); //tag name
               EXPECT_STRNE((const char *)node->properties->next->children->content, ""); //tag value
               node = node->next; // get next data tag
               EXPECT_STREQ((const char *)node->properties->children->content, "recordedCall"); //tag name
               EXPECT_STRNE((const char *)node->properties->next->children->content, ""); //tag value
               node = node->next; // get next data tag
               if (strcmp((const char*)node->properties->children->content, "usbDevice") == 0)
               {
                  EXPECT_STREQ((const char *)node->properties->children->content, "usbDevice"); //tag name
                  EXPECT_STRNE((const char *)node->properties->next->children->content, ""); //tag value
                  node = node->next; // get next data tag
               }
               EXPECT_STREQ((const char *)node->properties->children->content, "oneWayAudio"); //tag name
               // swapping the mock network above causes one way audio to get flagged
               //EXPECT_STREQ((const char *)node->properties->next->children->content, "true"); //tag value
               node = node->next; // get next data tag
               EXPECT_STREQ((const char *)node->properties->children->content, "poorNetworkQualityIndicated"); //tag name
               EXPECT_STRNE((const char *)node->properties->next->children->content, ""); //tag value
               node = node->next; // get next data tag
               EXPECT_STREQ((const char *)node->properties->children->content, "dataNetworkType"); //tag name

               if (randNetTrans == TransportWiFi)
               {
                  EXPECT_STREQ((const char *)node->properties->next->children->content, "WIFI"); //tag value
               }
               else
               {
                  EXPECT_STREQ((const char *)node->properties->next->children->content, "4G"); //tag value
               }

               node = node->next; // get next data tag
               EXPECT_STREQ((const char *)node->properties->children->content, "callHoldUsed"); //tag name
               EXPECT_STREQ((const char *)node->properties->next->children->content, "true"); //tag value
               node = node->next; // get next data tag
               EXPECT_STREQ((const char *)node->properties->children->content, "networkIpChange"); //tag name
               EXPECT_STREQ((const char *)node->properties->next->children->content, "true"); //tag value

               cpc::string uri = "sip:" + alice.config.settings.username + "@" + alice.config.settings.domain;
               node = node->next; // get next data tag
               EXPECT_STREQ((const char *)node->properties->children->content, "callOrigin"); //tag name
               EXPECT_STREQ((const char *)node->properties->next->children->content, uri); //tag value
               node = node->next; // get next data tag

               #if (CPCAPI2_BRAND_VQMONEP_MOBILE==1)
                     //need to fix, soemtimes onConvEnded is called before VQM report is generated...seems like vqm module bug
                    //std::string vqmReport = getVqmReportValue(evt.content, "/cpc_usage_report/activity_data/call_list/call[1]/vqm_report[1]"); // get next tag
                   // EXPECT_STRNE(vqmReport.c_str(), ""); //tag name
               #endif

           });
           waitFor(sendReponse);
       }

       //close the module
       alice.analyticsManager->close(aliceHandle);
       bob.analyticsManager->close(bobHandle);
   }

   TEST_F(Analytics1ModuleTest, AudioCallRejectedTest) {

       // This test account creates the phone, the analytics module, calls open() 
       //on module with default values, and creates/init/enable the Sip account
       TestAccount alice("alice");
       TestAccount bob("bob");

       //get phone from the TestAccount Class
       Phone* phone = alice.phone;
       SipAccountManager* acct = SipAccountManager::getInterface(phone);

       //get the analytics module created by testAccount class
       AnalyticsHandle aliceHandle = openModuleWithEmptyServerUrl(alice.analyticsManager);
       AnalyticsHandle bobHandle = openModuleWithEmptyServerUrl(bob.analyticsManager);

       std::this_thread::sleep_for(std::chrono::milliseconds(5000));


       // Alice calls Bob then Bob hangs up
       SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
       alice.conversation->addParticipant(aliceCall, bob.config.uri());
       alice.conversation->start(aliceCall);

       auto aliceConversationEvents = std::async(std::launch::async, [&]() {
           // Wait for the expected state changes and for the call to end
           assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
           assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
           assertConversationEnded(alice, aliceCall, ConversationEndReason_ServerRejected);
           std::this_thread::sleep_for(std::chrono::milliseconds(10000));
       });

       auto bobConversationEvents = std::async(std::launch::async, [&]() {
           // Wait for the expected state changes and for the call to end
           SipConversationHandle bobCall;
           assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
           bob.conversation->sendRingingResponse(bobCall);
           assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
           bob.conversation->reject(bobCall);
           assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
           std::this_thread::sleep_for(std::chrono::milliseconds(10000));
       });

       waitFor2(aliceConversationEvents, bobConversationEvents);

       alice.analyticsManager->sendReport(aliceHandle);

       // Wait for response from report
       {
           auto sendReponse = std::async(std::launch::async, [&]() {
               AnalyticsHandle h;
               OnReportCreatedSuccessEvent evt;
               ASSERT_TRUE(cpcWaitForEvent(
                   alice.analyticsEvents,
                   "AnalyticsHandlerInt::onReportCreatedSuccess",
                   15000,
                   AlwaysTruePred(),
                   h, evt));

               //make sure document is not empty
               ASSERT_NE(evt.content, cpc::string());
               //make sure activity_data tag exists
               bool exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data[1]");
               ASSERT_TRUE(exists);
               //make sure presence tag exists
               exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data/call_list[1]");
               ASSERT_TRUE(exists);
               //make sure provisioning tag exists
               exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data/call_list/call[1]");
               ASSERT_TRUE(exists);
               xmlNode* node = getXmlNode(evt.content.c_str(), "/cpc_usage_report/activity_data/call_list/call[1]");
               //make sure id = SIP*
               ASSERT_EQ(strncmp((const char *)node->parent->properties->children->content, "SIP", strlen("SIP")), 0);

               //make sure relevant data tags exist and their content is not empty
               EXPECT_STREQ((const char *)node->properties->children->content, "callStart"); //tag name
               EXPECT_STRNE((const char *)node->properties->next->children->content, ""); //tag value
               node = node->next; // get next data tag
               EXPECT_STREQ((const char *)node->properties->children->content, "callDuration"); //tag name
               EXPECT_STREQ((const char *)node->properties->next->children->content, "0"); //tag value
               node = node->next; // get next data tag
               EXPECT_STREQ((const char *)node->properties->children->content, "incoming"); //tag name
               EXPECT_STREQ((const char *)node->properties->next->children->content, "false"); //tag value
               node = node->next; // get next data tag
               EXPECT_STREQ((const char *)node->properties->children->content, "callSuccessful"); //tag name
               EXPECT_STREQ((const char *)node->properties->next->children->content, "false"); //tag value
               node = node->next; // get next data tag
               EXPECT_STREQ((const char *)node->properties->children->content, "failedDialedCallReason"); //tag name
               EXPECT_STREQ((const char *)node->properties->next->children->content, "486"); //tag value
               node = node->next; // get next data tag
               EXPECT_STREQ((const char *)node->properties->children->content, "numDigitsDialed"); //tag name
               EXPECT_STRNE((const char *)node->properties->next->children->content, ""); //tag value
               node = node->next; // get next data tag
               EXPECT_STREQ((const char *)node->properties->children->content, "callTransfer"); //tag name
               EXPECT_STREQ((const char *)node->properties->next->children->content, "false"); //tag value

#if (CPCAPI2_BRAND_VQMONEP_MOBILE==1)
               //need to fix, soemtimes onConvEnded is called before VQM report is generated...seems like vqm module bug
               //std::string vqmReport = getVqmReportValue(evt.content, "/cpc_usage_report/activity_data/call_list/call[1]/vqm_report[1]"); // get next tag
               // EXPECT_STRNE(vqmReport.c_str(), ""); //tag name
#endif

           });
           waitFor(sendReponse);
       }

       //close the module
       alice.analyticsManager->close(aliceHandle);
       bob.analyticsManager->close(bobHandle);
   }

   // no camera in device list on our linux test runs
#if !(defined( __linux__ ) && !defined( ANDROID )) 
   TEST_F(Analytics1ModuleTest, VideoCallTest) {
#if _WIN32
   if (TestEnvironmentConfig::dockerContainerized()) GTEST_SKIP(); // UNIT_TEST_WIN_DOCKER_SKIP
#endif

   // This test account creates the phone, the analytics module, calls open()
   //on module with default values, and creates/init/enable the Sip account
   TestAccount alice("alice");
   TestAccount bob("bob");

   //get phone from the TestAccount Class
   Phone* phone = alice.phone;
   SipAccountManager* acct = SipAccountManager::getInterface(phone);

   //get the analytics module created by testAccount class
   AnalyticsHandle aliceHandle = openModuleWithEmptyServerUrl(alice.analyticsManager);
   AnalyticsHandle bobHandle = openModuleWithEmptyServerUrl(bob.analyticsManager);

   //set video device to the first device in list, just to trigger setCaptureDevice in VideoImpl, this will propulate the report properly
   //with video device name. In a real scenario, the App layer will query the devices and set the default capture device before starting a call.
   //but for the sake of these unit tests we need to do it manually
   
   CPCAPI2::Media::VideoDeviceListUpdatedEvent evt;
   int handle = 0;
   ASSERT_EQ(alice.audio->queryDeviceList(), kSuccess);
   ASSERT_TRUE(alice.mediaEvents->expectEvent("VideoHandler::onVideoDeviceListUpdated", 30000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
   ASSERT_NE(evt.deviceInfo.size(), 0);
    
   alice.video->setCaptureDevice(evt.deviceInfo[0].id);

       

       std::this_thread::sleep_for(std::chrono::milliseconds(5000));

       SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
       alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, true);
       alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
       alice.conversation->addParticipant(aliceCall, bob.config.uri());
       alice.conversation->start(aliceCall);

       auto aliceEvents = std::async(std::launch::async, [&]() {
           // let the cameras get going
           std::this_thread::sleep_for(std::chrono::milliseconds(2000));
           assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
           assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
           assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
           std::this_thread::sleep_for(std::chrono::milliseconds(10000));
       });

       auto bobEvents = std::async(std::launch::async, [&]() {
           // let the cameras get going
           std::this_thread::sleep_for(std::chrono::milliseconds(2000));
           SipConversationHandle bobCall;
           assertNewConversationIncoming_ex(bob, &bobCall, alice.config.uri(), [](const NewConversationEvent& evt){
               ASSERT_EQ(2, evt.remoteMediaInfo.size());
           });

           bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
           // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
           assertSuccess(bob.conversation->accept(bobCall));
           assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

           std::this_thread::sleep_for(std::chrono::milliseconds(10000));
           assertSuccess(bob.conversation->end(bobCall));
           assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
           std::this_thread::sleep_for(std::chrono::milliseconds(10000));
       });

       waitFor2(aliceEvents, bobEvents);

       std::this_thread::sleep_for(std::chrono::milliseconds(5000));

       alice.analyticsManager->sendReport(aliceHandle);


           // Wait for response from report
           {
               auto sendReponse = std::async(std::launch::async, [&]() {
                   AnalyticsHandle h;
                   OnReportCreatedSuccessEvent evt;
                   ASSERT_TRUE(cpcWaitForEvent(
                       alice.analyticsEvents,
                       "AnalyticsHandlerInt::onReportCreatedSuccess",
                       15000,
                       AlwaysTruePred(),
                       h, evt));

                   //make sure document is not empty
                   ASSERT_NE(evt.content, cpc::string());
                   //make sure activity_data tag exists
                   bool exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data[1]");
                   ASSERT_TRUE(exists);
                   //make sure presence tag exists
                   exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data/call_list[1]");
                   ASSERT_TRUE(exists);
                   //make sure provisioning tag exists
                   exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data/call_list/call[1]");
                   ASSERT_TRUE(exists);
                   xmlNode* node = getXmlNode(evt.content.c_str(), "/cpc_usage_report/activity_data/call_list/call[1]");
                   //make sure id = SIP*
                   ASSERT_EQ(strncmp((const char *)node->parent->properties->children->content, "SIP", strlen("SIP")), 0);

                   node = getXmlNode(evt.content.c_str(), "/cpc_usage_report/activity_data/call_list/call[1]/videoOut[1]");
                   //make sure relevant data tags exist and their content is not empty
                   EXPECT_STREQ((const char *)node->properties->children->content, "device"); //tag name
                   EXPECT_STRNE((const char *)node->properties->next->children->content, ""); //tag value
                   node = node->next; // get next data tag
                   EXPECT_STREQ((const char *)node->properties->children->content, "codec"); //tag name
                   EXPECT_STRNE((const char *)node->properties->next->children->content, ""); //tag value
                   node = node->next; // get next data tag
                   EXPECT_STREQ((const char *)node->properties->children->content, "width"); //tag name
                   EXPECT_STRNE((const char *)node->properties->next->children->content, ""); //tag value
                   node = node->next; // get next data tag
                   EXPECT_STREQ((const char *)node->properties->children->content, "height"); //tag name
                   EXPECT_STRNE((const char *)node->properties->next->children->content, ""); //tag value

                   node = getXmlNode(evt.content.c_str(), "/cpc_usage_report/activity_data/call_list/call[1]/videoIn[1]");
                   EXPECT_STREQ((const char *)node->properties->children->content, "codec"); //tag name
                   EXPECT_STRNE((const char *)node->properties->next->children->content, ""); //tag value
                   node = node->next; // get next data tag
                   EXPECT_STREQ((const char *)node->properties->children->content, "width"); //tag name
                   EXPECT_STRNE((const char *)node->properties->next->children->content, ""); //tag value
                   node = node->next; // get next data tag
                   EXPECT_STREQ((const char *)node->properties->children->content, "height"); //tag name
                   EXPECT_STRNE((const char *)node->properties->next->children->content, ""); //tag value
               });
               waitFor(sendReponse);
           }

       //close the module
       alice.analyticsManager->close(aliceHandle);
       bob.analyticsManager->close(bobHandle);
   }
#endif // !defined( __linux__ ) && !defined( ANDROID )

   TEST_F(Analytics1ModuleTest, LoginTestNoServerResponse)
   {
      TestAccount alice("alice");

      AnalyticsManager *analyticsManager = alice.analyticsManager;


      AnalyticsSettings serverSettings;
      serverSettings.serverURL = "https://*********:5098"; // address that is routable but we hope to receive no response on

      serverSettings.strettoUserName = "<EMAIL>";
      serverSettings.httpUserName = "vqmUser";
      serverSettings.httpPassword = "lockTheTaskbar";

      GeneralStats generalStats;
      generalStats.deviceUUID = "518E27C9-A499-4A73-87EF-1595682F7772";
      generalStats.clientVersion = "4.4";
      generalStats.installationDate = **********;
      generalStats.osType = "Win32";
      generalStats.osVersion = "7";
      generalStats.hardwareModel = "Clone";
      generalStats.publicIPAddress = "*************";
      generalStats.launchTime = "**********";
      generalStats.language = "en.CA";
      generalStats.timezone = "UTC-5:00"; // the server is VERY PARTICULAR about this format.
      generalStats.serialNumber = "AABBCCDDEEFF";

      AnalyticsHandle ah = analyticsManager->open(serverSettings, generalStats, 1);

      PresenceStats ps;
      ps.numContacts = 10;
      ps.numContactsWithPresence = 6;
      analyticsManager->setPresenceStats(ah, ps);

      ProvisioningStats provst;
      provst.failedProvisionAttempts = 1;
      provst.successfulProvisionAttempts = 1;
      analyticsManager->setProvisioningStats(ah, provst);

      StabilityStats ss;
      ss.numCrashes = 6000;
      analyticsManager->setStabilityStats(ah, ss);

      analyticsManager->sendReport(ah);

      auto sendResponse = std::async(std::launch::async, [&]() {

      AnalyticsHandle h;
      OnConnectionFailedEvent evt;
      ASSERT_TRUE(cpcWaitForEvent(
         alice.analyticsEvents,
         "AnalyticsHandler::onConnectionFailed",
         13000, // timeout once cURL starts querying is 10 seconds; give a few more seconds to let the query get going
         AlwaysTruePred(),
         h, evt));
      });

      waitFor(sendResponse);


      analyticsManager->close(ah);
   }
   
   struct PhoneShutdownCvInfo
   {
      std::unique_ptr<std::condition_variable> cv;
      std::unique_ptr<std::mutex> mutex;
   };

   static void phoneShutdown(void* context)
   {
      // use a condition variable to signal to the test case that the phone has shutdown
      
      PhoneShutdownCvInfo* info = reinterpret_cast<PhoneShutdownCvInfo*>(context);
      std::unique_lock<std::mutex> lock(*(info->mutex.get()));
      info->cv->notify_all();
   }
   
   TEST_F(Analytics1ModuleTest, LoginTestNoServerResponseSdkShutdown)
   {
      PhoneShutdownCvInfo* shutdownTracking(new PhoneShutdownCvInfo());
      shutdownTracking->cv.reset(new std::condition_variable());
      shutdownTracking->mutex.reset(new std::mutex());
      TestAccount alice("alice");
      
      {
         AnalyticsManager *analyticsManager = alice.analyticsManager;
         AnalyticsSettings serverSettings;
         serverSettings.serverURL = "https://*********:5098"; // address that is routable but we hope to receive no response on
         GeneralStats generalStats;
         AnalyticsHandle ah = analyticsManager->open(serverSettings, generalStats, 1);

         analyticsManager->sendReport(ah);

         analyticsManager->close(ah);
         
         alice.shutdown();
      }
      
      {
         std::unique_lock<std::mutex> lock(*(shutdownTracking->mutex));
         // if the analytics manager is working correctly, it will abort the active HTTP request when the SDK shuts down.
         // as such, we test that the SDK does not get stuck during shutdown, assuming that if the SDK takes any longer,
         // the problem is the HTTP request not being aborted
         auto ret = shutdownTracking->cv->wait_until(lock, std::chrono::system_clock::now() + std::chrono::seconds(10));
         
         // Check the shutdown status from TestAccount
         if (alice.didShutdownTimeout())
         {
            // let shutdownTracking leak
            ASSERT_NE(std::cv_status::timeout, ret);
            return;
         }
      }
      
      delete shutdownTracking;
   }

   // no camera in device list on our linux test runs
#if !(defined( __linux__ ) && !defined( ANDROID ))
   TEST_F(Analytics1ModuleTest, VideoCallConferenceTest) {
#if _WIN32
      if (TestEnvironmentConfig::dockerContainerized()) GTEST_SKIP(); // UNIT_TEST_WIN_DOCKER_SKIP
#endif

       // This test account creates the phone, the analytics module, calls open() 
       //on module with default values, and creates/init/enable the Sip account
       TestAccount alice("alice");
       TestAccount bob("bob");
       TestAccount max("max");

       //get phone from the TestAccount Class
       Phone* phone = alice.phone;
       SipAccountManager* acct = SipAccountManager::getInterface(phone);

       //get the analytics module created by testAccount class
       AnalyticsHandle aliceHandle = openModuleWithEmptyServerUrl(alice.analyticsManager);
       AnalyticsHandle bobHandle = openModuleWithEmptyServerUrl(bob.analyticsManager);
       AnalyticsHandle maxHandle = openModuleWithEmptyServerUrl(max.analyticsManager);

       //set video device to the first device in list, just to trigger setCaptureDevice in VideoImpl, this will propulate the report properly
       //with video device name. In a real scenario, the App layer will query the devices and set the default capture device before starting a call.
       //but for the sake of these unit tests we need to do it manually
      CPCAPI2::Media::VideoDeviceListUpdatedEvent evt;
      int handle = 0;
      ASSERT_EQ(alice.audio->queryDeviceList(), kSuccess);
      ASSERT_TRUE(alice.mediaEvents->expectEvent("VideoHandler::onVideoDeviceListUpdated", 30000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
      ASSERT_NE(evt.deviceInfo.size(), 0);
      max.video->setCaptureDevice(evt.deviceInfo[0].id);

       std::this_thread::sleep_for(std::chrono::milliseconds(5000));

       SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
       alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, true);
       alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
       alice.conversation->addParticipant(aliceCall, bob.config.uri());
       alice.conversation->start(aliceCall);

       auto aliceEvents = std::async(std::launch::async, [&]() {
           assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
           assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
           assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
           
           assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendOnly);
           assertSuccess(alice.conversation->accept(aliceCall));
           assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendReceive);
           assertSuccess(alice.conversation->accept(aliceCall));

           std::this_thread::sleep_for(std::chrono::milliseconds(4000));
           assertSuccess(alice.conversation->end(aliceCall));
           assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
           std::this_thread::sleep_for(std::chrono::milliseconds(10000));
       });

       auto bobEvents = std::async(std::launch::async, [&]() {
           SipConversationHandle bobCall;
           assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
           // Bob sends 180 ringing -- this should trigger state transitions for both Bob and Alice
           assertSuccess(bob.conversation->sendRingingResponse(bobCall));
           assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
           bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
           // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
           assertSuccess(bob.conversation->accept(bobCall));
           assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

           std::this_thread::sleep_for(std::chrono::milliseconds(1000));
           assertSuccess(bob.conversation->hold(bobCall));

           // make an outgoing (audio only) call from Bob to Max using the demo.xten.com server
           SipConversationHandle bobCallToMax = bob.conversation->createConversation(bob.handle);
           bob.conversation->setMediaEnabled(bobCallToMax, MediaType_Audio, true);
           bob.conversation->setMediaEnabled(bobCallToMax, MediaType_Video, true);
           bob.conversation->addParticipant(bobCallToMax, max.config.uri());
           bob.conversation->start(bobCallToMax);

           // BOB <=> MAX
           assertNewConversationOutgoing(bob, bobCallToMax, max.config.uri());
           assertConversationStateChanged(bob, bobCallToMax, ConversationState_RemoteRinging);
           assertConversationStateChanged(bob, bobCallToMax, ConversationState_Connected);

           // make this a conference by un-holding all calls
           assertSuccess(bob.conversation->unhold(bobCall));

           std::this_thread::sleep_for(std::chrono::milliseconds(4000));
           assertSuccess(bob.conversation->end(bobCallToMax));
           assertConversationEnded(bob, bobCallToMax, ConversationEndReason_UserTerminatedLocally);

           // BOB <=> ALICE
           assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
           std::this_thread::sleep_for(std::chrono::milliseconds(10000));
       });

       // Overview of Max's thread:
       auto maxEvents = std::async(std::launch::async, [&]() {
           SipConversationHandle maxCallFromBob;
           assertNewConversationIncoming(max, &maxCallFromBob, bob.config.uri());
           // Bob sends 180 ringing -- this should trigger state transitions for both Bob and Max
           assertSuccess(max.conversation->sendRingingResponse(maxCallFromBob));
           assertConversationStateChanged(max, maxCallFromBob, ConversationState_LocalRinging);
           max.conversation->setMediaEnabled(maxCallFromBob, MediaType_Video, true);
           // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
           assertSuccess(max.conversation->accept(maxCallFromBob));
           assertConversationStateChanged(max, maxCallFromBob, ConversationState_Connected);
           assertConversationEnded(max, maxCallFromBob, ConversationEndReason_UserTerminatedRemotely);
           std::this_thread::sleep_for(std::chrono::milliseconds(10000));
       });

       waitFor3(aliceEvents, bobEvents, maxEvents);

       std::this_thread::sleep_for(std::chrono::milliseconds(10000));

       bob.analyticsManager->sendReport(bobHandle);


       // Wait for response from report
       {
           auto sendReponse = std::async(std::launch::async, [&]() {
               AnalyticsHandle h;
               OnReportCreatedSuccessEvent evt;
               ASSERT_TRUE(cpcWaitForEvent(
                   bob.analyticsEvents,
                   "AnalyticsHandlerInt::onReportCreatedSuccess",
                   25000,
                   AlwaysTruePred(),
                   h, evt));

               //make sure document is not empty
               ASSERT_NE(evt.content, cpc::string());
               //make sure activity_data tag exists
               bool exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data[1]");
               ASSERT_TRUE(exists);
               //make sure presence tag exists
               exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data/call_list[1]");
               ASSERT_TRUE(exists);
               //make sure provisioning tag exists
               exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/activity_data/call_list/call[1]");
               ASSERT_TRUE(exists);
               xmlNode* node = getXmlNode(evt.content.c_str(), "/cpc_usage_report/activity_data/call_list/call[1]");
               //make sure id = SIP*
               ASSERT_EQ(strncmp((const char *)node->parent->properties->children->content, "SIP", strlen("SIP")), 0);

               //make sure relevant data tags exist and their content is not empty
               EXPECT_STREQ((const char *)node->properties->children->content, "callStart"); //tag name
               EXPECT_STRNE((const char *)node->properties->next->children->content, ""); //tag value
               node = node->next; // get next data tag
               EXPECT_STREQ((const char *)node->properties->children->content, "callDuration"); //tag name
               EXPECT_STRNE((const char *)node->properties->next->children->content, ""); //tag value
               node = node->next; // get next data tag
               EXPECT_STREQ((const char *)node->properties->children->content, "incoming"); //tag name
               EXPECT_STREQ((const char *)node->properties->next->children->content, "false"); //tag value
               node = node->next; // get next data tag
               EXPECT_STREQ((const char *)node->properties->children->content, "callSuccessful"); //tag name
               EXPECT_STREQ((const char *)node->properties->next->children->content, "true"); //tag value
               node = node->next; // get next data tag
               EXPECT_STREQ((const char *)node->properties->children->content, "numDigitsDialed"); //tag name
               EXPECT_STRNE((const char *)node->properties->next->children->content, ""); //tag value
               node = node->next; // get next data tag
               EXPECT_STREQ((const char *)node->properties->children->content, "callTransfer"); //tag name
               EXPECT_STREQ((const char *)node->properties->next->children->content, "false"); //tag value
               node = node->next; // get next data tag
               EXPECT_STREQ((const char *)node->properties->children->content, "localConference"); //tag name
               EXPECT_STREQ((const char *)node->properties->next->children->content, "true"); //tag value
               node = node->next; // get next data tag
               EXPECT_STREQ((const char *)node->properties->children->content, "maxConferenceParticipants"); //tag name
               EXPECT_STREQ((const char *)node->properties->next->children->content, "3"); //tag value
               node = node->next; // get next data tag
               EXPECT_STREQ((const char *)node->properties->children->content, "videoConference"); //tag name
               EXPECT_STREQ((const char *)node->properties->next->children->content, "true"); //tag value
           });
           waitFor(sendReponse);
       }

       //close the module
       alice.analyticsManager->close(aliceHandle);
       bob.analyticsManager->close(bobHandle);
       max.analyticsManager->close(maxHandle);
   }
#endif // !defined( __linux__ ) && !defined( ANDROID )

   AnalyticsHandle Analytics1ModuleTest::openModuleWithEmptyServerUrl(AnalyticsManager* mgr)
   {
      // if CPCAPI2_AUTO_TEST is enabled, and an empty serverURL is specified, the analytics module will
      // not actually post to a server. this is useful since we generally do not want the unit tests to rely on
      // an external server

      AnalyticsSettings serverSettings;
      serverSettings.serverURL = ""; //"https://ccsdev.mobilevoiplive.com:18082/uxmetrics/submit/sdkdemo";
      serverSettings.strettoUserName = "t2@sdkdemo";
      serverSettings.httpUserName = "sdkuemtestun";
      serverSettings.httpPassword = "sdkuemtestpw";

      // "http://************:8081/uxmetrics/submit/imap.mobilevoiplive.com?user=ptt0030%40imap.mobilevoiplive.com&device=ptt0030%40imap.mobilevoiplive.com";
      // serverSettings.serverURL = "http://************:8081/uxmetrics/submit/imap.mobilevoiplive.com";
      /*
      serverSettings.serverURL = "https://imap.mobilevoiplive.com:8082/uxmetrics/submit/imap.mobilevoiplive.com";
      serverSettings.strettoUserName = "<EMAIL>";
      serverSettings.httpUserName = "somethingSomethingUEM";
      serverSettings.httpPassword = "kihjmalseropasdflk";
      */

      GeneralStats generalStats;
      generalStats.deviceUUID = "518E27C9-A499-4A73-87EF-1595682F7772";
      generalStats.clientVersion = "4.4";
      generalStats.installationDate = **********;
      generalStats.osType = "Win32";
      generalStats.osVersion = "7";
      generalStats.hardwareModel = "Clone";
      generalStats.publicIPAddress = "*************";
      generalStats.launchTime = "**********";
      generalStats.language = "en.CA";
      generalStats.timezone = "UTC-5:00"; // the server is VERY PARTICULAR about this format.
      generalStats.serialNumber = "AABBCCDDEEFF";

      //set the basic stats to the default handler which is always (1)
      //the third paramater is optional and is only used by the test cases due
      //to issue with timing of creating module & account getting configured.
      //In real scenario, the app layer will never pass in the third parameter.
      return mgr->open(serverSettings, generalStats, 1);
   }

   //grabs the value of a specific tag inside the PATH parameter passed in. If the tag
   //does not exist then you get empty string back
   std::string Analytics1ModuleTest::getVqmReportValue(std::string doc, std::string path) {

       const xmlChar *plainTextPath = (const xmlChar*) path.c_str();
       std::string stdPlain = "";

       // Use libxml + XPath to parse the document (hopefully the shortest way)
       LibxmlSharedUsage::addRef();

       xmlDocPtr xmlDoc = xmlParseMemory(doc.c_str(), doc.size());
       xmlXPathContextPtr xpathCtx = NULL;
       xmlXPathObjectPtr xpathObjPlain = NULL;
       xmlChar *dataName = NULL;
       xmlChar *dataValue = NULL;

       // Create xpath evaluation context
       xpathCtx = xmlXPathNewContext(xmlDoc);
       if (xpathCtx == NULL)
           goto done;

       // Evaluate xpath expression to get plaintext
       xpathObjPlain = xmlXPathEvalExpression(plainTextPath, xpathCtx);
       if (xpathObjPlain == NULL || xmlXPathNodeSetIsEmpty(xpathObjPlain->nodesetval))
           goto done;

       // get text value of a specific tag PATH
       if (xpathObjPlain->nodesetval->nodeTab[0]->children != NULL && xpathObjPlain->nodesetval->nodeTab[0]->children->children != NULL && xpathObjPlain->nodesetval->nodeTab[0]->children->children->content != NULL) {
           dataValue = xpathObjPlain->nodesetval->nodeTab[0]->children->children->content;

           stdPlain = (const char *)dataValue; // copy the plaintext
           xmlFree(dataValue);
       }

   done:
       if (xpathObjPlain != NULL)
       {
           xmlXPathFreeObject(xpathObjPlain);
           xpathObjPlain = NULL;
       }
       if (xpathCtx != NULL)
       {
           xmlXPathFreeContext(xpathCtx);
           xpathCtx = NULL;
       }
       if (xmlDoc != NULL)
       {
           xmlFreeDoc(xmlDoc);
           xmlDoc = NULL;
       }

       LibxmlSharedUsage::release();
       return stdPlain;
   }

   // _xmlNode returned is the pointer to the first DATA tag inside of the PATH provided.
   // from that pointer we can interate to each of the DATA tags inside PATH provided.
   // if the tag does not exist we get a NULL;
   _xmlNode* Analytics1ModuleTest::getXmlNode(std::string doc, std::string path) {
       const xmlChar *plainTextPath = (const xmlChar*) path.c_str();
       std::string stdPlain;

       // Use libxml + XPath to parse the document (hopefully the shortest way)
       LibxmlSharedUsage::addRef();

       xmlDocPtr xmlDoc = xmlParseMemory(doc.c_str(), doc.size());
       xmlXPathContextPtr xpathCtx = NULL;
       xmlXPathObjectPtr xpathObjPlain = NULL;
       xmlChar *data = NULL;

       // Create xpath evaluation context
       xpathCtx = xmlXPathNewContext(xmlDoc);

       // Evaluate xpath expression to get plaintext
       xpathObjPlain = xmlXPathEvalExpression(plainTextPath, xpathCtx);
       if (xpathObjPlain == NULL || xmlXPathNodeSetIsEmpty(xpathObjPlain->nodesetval)) {
           return NULL;
       }

       LibxmlSharedUsage::release();

       return xpathObjPlain->nodesetval->nodeTab[0]->children;
   }

   // _xmlNode returned is the pointer to the first DATA tag inside of the PATH provided.
   // from that pointer we can interate to each of the DATA tags inside PATH provided.
   // if the tag does not exist we get a NULL;
   bool Analytics1ModuleTest::getXmlNodeExists(std::string doc, std::string path) {
       const xmlChar *plainTextPath = (const xmlChar*)path.c_str();
       std::string stdPlain;

       // Use libxml + XPath to parse the document (hopefully the shortest way)
       LibxmlSharedUsage::addRef();

       xmlDocPtr xmlDoc = xmlParseMemory(doc.c_str(), doc.size());
       xmlXPathContextPtr xpathCtx = NULL;
       xmlXPathObjectPtr xpathObjPlain = NULL;
       xmlChar *data = NULL;
       bool exists = false;

       // Create xpath evaluation context
       xpathCtx = xmlXPathNewContext(xmlDoc);

       // Evaluate xpath expression to get plaintext
       xpathObjPlain = xmlXPathEvalExpression(plainTextPath, xpathCtx);
       if (xpathObjPlain != NULL && !xmlXPathNodeSetIsEmpty(xpathObjPlain->nodesetval)) {
           exists = true;
       }

       LibxmlSharedUsage::release();
       return exists;
   }
}  // namespace

#endif // (CPCAPI2_BRAND_ANALYTICS_MODULE == 1)
