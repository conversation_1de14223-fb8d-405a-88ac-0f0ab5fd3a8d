#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#if (CPCAPI2_BRAND_SIP_SHARED_CALL_APPEARANCE_MODULE == 1)

#include "cpcapi2_test_fixture.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>

#include "test_framework/cpcapi2_test_framework.h"
#include "test_framework/bare_bones_sip_endpoint.h"
#include "test_account_events.h"
#include "test_call_events.h"
#include "impl/dialogevent/DialogInfoDocumentHelper.h"
#include "impl/dialogevent/SipDialogEventSubscriptionManagerInterface.h"

#include <thread>
#include <future>

using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::SipEvent;
using namespace CPCAPI2::SipConversation;
using namespace CPCAPI2::SipDialogEvent;
using namespace CPCAPI2::SipSharedCallAppearance;

namespace {

const cpc::string SCAP_PUID = "sip:+<EMAIL>";

class SharedCallAppearanceTestAccount : public TestAccount
{
public:
   SharedCallAppearanceTestAccount(const std::string& name, bool disableOnDestruct = true) : TestAccount(name, Account_NoInit, disableOnDestruct)
   {
      if (name == "alice")
      {
         config.settings.displayName = "**********";
         config.settings.username = "+1**********";
         config.settings.auth_username = "<EMAIL>";
         config.settings.domain = "csa1.luqdlab.com";
         config.settings.outboundProxy = "**********";
         config.settings.password = "newsys";
         config.settings.registrationIntervalSeconds = 3600;
      }
      else if (name == "bob")
      {
         config.settings.displayName = "**********";
         config.settings.username = "+1**********";
         config.settings.auth_username = "<EMAIL>";
         config.settings.domain = "csa1.luqdlab.com";
         config.settings.outboundProxy = "**********";
         config.settings.password = "newsys";
         config.settings.registrationIntervalSeconds = 3600;
      }
      else
      {
         assert(false);
      }

      init();

      scaManager = SipSharedCallAppearanceManager::getInterface(phone);
      scaManager->setHandler(handle, (SipSharedCallAppearanceHandler*) 0xDEADBEEF);
      scaEvents = new test::EventHandler(name.c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(scaManager));
      scaStateManager = SipSharedCallAppearanceStateManager::getInterface(scaManager);

      enable();
   }

   SipSharedCallAppearanceManager* scaManager;
   SipSharedCallAppearanceManager* scaEvents;
   SipSharedCallAppearanceStateManager* scaStateManager;
};

class StandardLineTestAccount : public TestAccount
{
public:
   StandardLineTestAccount(const std::string& name) : TestAccount(name, Account_NoInit)
   {
      if (name == "charlie")
      {
         config.settings.displayName = "**********";
         config.settings.username = "+1**********";
         config.settings.auth_username = "<EMAIL>";
         config.settings.domain = "csa1.luqdlab.com";
         config.settings.outboundProxy = "**********";
         config.settings.password = "newsys";
         config.settings.registrationIntervalSeconds = 3600;
      }
      else if (name == "dave")
      {
         config.settings.displayName = "**********";
         config.settings.username = "+***********";
         config.settings.auth_username = "<EMAIL>";
         config.settings.domain = "csa1.luqdlab.com";
         config.settings.outboundProxy = "**********";
         config.settings.password = "newsys";
         config.settings.registrationIntervalSeconds = 3600;
      }
      else
      {
         assert(false);
      }

      init();
      enable();
   }
};

#define assertPhoneError(account, errorText) \
   SharedCallAppearanceTest::expectPhoneError(__LINE__, account, errorText) 
#define assertSCAError(account, scaSet, errorText) \
   SharedCallAppearanceTest::expectSCAError(__LINE__, account, scaSet, errorText)
#define assertIncomingTransferRequest(account, conversation, targetConversation) \
   SharedCallAppearanceTest::expectIncomingTransferRequest(__LINE__, account, conversation, targetConversation)
#define assertSCANewSubscription(account, scaSet, sca) \
   SharedCallAppearanceTest::expectSCANewSubscription(__LINE__, account, scaSet, sca)
#define assertSCAState(account, scaSet, sca, subscriptionStarted, subscriptionState) \
   SharedCallAppearanceTest::expectSCAState(__LINE__, account, scaSet, sca, subscriptionStarted, subscriptionState)
#define assertSCACallHeldState(account, scaSet, sca, appearance, held) \
   expectSCACallHeldState(account, scaSet, sca, appearance, held);
#define assertSCACallExclusiveState(account, scaSet, sca, appearance, exclusive) \
   expectSCACallExclusiveState(account, scaSet, sca, appearance, exclusive);
#define assertSCACallScapHeldState(account, scaSet, sca, appearance, scapHeld) \
   expectSCACallScapHeldState(account, scaSet, sca, appearance, scapHeld);
#define assertSCASubscriptionStateChanged(account, scaSet, sca, subscriptionState) \
   SharedCallAppearanceTest::expectSCASubscriptionStateChanged(__LINE__, account, scaSet, sca, subscriptionState)
#define assertSCAStateChanged(account, scaSet, sca) \
   SharedCallAppearanceTest::expectSCAStateChanged(__LINE__, account, scaSet, sca, DialogDirection_NotSpecified, DialogState_NotSpecified, "", "", 0)
#define assertSCAStateChanged_ex(account, scaSet, sca, direction, dialogState, localAddress, remoteAddress, appearance) \
   SharedCallAppearanceTest::expectSCAStateChanged(__LINE__, account, scaSet, sca, direction, dialogState, localAddress, remoteAddress, appearance)
#define waitForSCASubscriptionEnded(account, scaSet, sca) \
   SharedCallAppearanceTest::waitForSCASubscriptionEnded_(__LINE__, account, scaSet, sca)
#define assertSCAMakeExclusiveSuccess(account, scaSet, sca) \
   SharedCallAppearanceTest::expectSCAMakeExclusiveSuccess(__LINE__, account, scaSet, sca)
#define waitForConversationStateChanged(account, conversation, conversationState) \
   SharedCallAppearanceTest::waitForConversationStateChanged_(__LINE__, account, conversation, conversationState)
#define waitForConversationEnded(account, conversation, endReason) \
   SharedCallAppearanceTest::waitForConversationEnded_(__LINE__, account, conversation, endReason)

class SharedCallAppearanceTest : public CpcapiAutoTest
{
public:
   SharedCallAppearanceTest() {}
   virtual ~SharedCallAppearanceTest() {}

   void startSubscription(SharedCallAppearanceTestAccount& account, SipSharedCallAppearanceSetHandle& scaSet, SipSharedCallAppearanceHandle sca, SipSharedCallAppearanceSetSettings scaSetSettings = SipSharedCallAppearanceSetSettings(), SipSharedCallAppearanceSettings scaSettings = SipSharedCallAppearanceSettings());
   void terminateSubscription(SharedCallAppearanceTestAccount& account, SipSharedCallAppearanceSetHandle scaSet, SipSharedCallAppearanceHandle sca);
   void outboundCallOnSCA(SharedCallAppearanceTestAccount& fromAccount, StandardLineTestAccount& toAccount, const cpc::string& helpdeskSCA);
   void inboundCallOnSCA(SharedCallAppearanceTestAccount& bob, StandardLineTestAccount& charlie, const cpc::string& helpdeskSCA, bool makeExclusive);

   static void expectPhoneError(int line, TestAccount& account, const cpc::string& errorText);
   static void expectSCAError(int line, SharedCallAppearanceTestAccount& account, SipSharedCallAppearanceSetHandle scaSet, const cpc::string& errorText);
   static void expectSCANewSubscription(int line, SharedCallAppearanceTestAccount& account, SipSharedCallAppearanceSetHandle scaSet, SipSharedCallAppearanceHandle sca);
   static void expectSCAState(int line, SharedCallAppearanceTestAccount& account, SipSharedCallAppearanceSetHandle scaSet, SipSharedCallAppearanceHandle sca, bool subscriptionStarted, SipSubscriptionState subscriptionState);
   static void expectSCACallHeldState(SharedCallAppearanceTestAccount& account, SipSharedCallAppearanceSetHandle scaSet, SipSharedCallAppearanceHandle sca, int appearance, bool held);
   static void expectSCACallScapHeldState(SharedCallAppearanceTestAccount& account, SipSharedCallAppearanceSetHandle scaSet, SipSharedCallAppearanceHandle sca, int appearance, bool scapHeld);
   static void expectSCACallExclusiveState(SharedCallAppearanceTestAccount& account, SipSharedCallAppearanceSetHandle scaSet, SipSharedCallAppearanceHandle sca, int appearance, bool exclusive);
   static void expectSCASubscriptionStateChanged(int line, SharedCallAppearanceTestAccount& account, SipSharedCallAppearanceSetHandle scaSet, SipSharedCallAppearanceHandle sca, SipSubscriptionState subscriptionState);
   static void expectSCAStateChanged(int line, SharedCallAppearanceTestAccount& account, SipSharedCallAppearanceSetHandle scaSet, SipSharedCallAppearanceHandle sca, DialogDirection direction, DialogState dialogState, const cpc::string& localAddress, const cpc::string& remoteAddress, int appearance);
   static void waitForSCASubscriptionEnded_(int line, SharedCallAppearanceTestAccount& account, SipSharedCallAppearanceSetHandle scaSet, SipSharedCallAppearanceHandle sca);
   static void expectIncomingTransferRequest(int line, TestAccount& account, SipConversationHandle conversation, SipConversationHandle& targetConversation);
   static void expectSCAMakeExclusiveSuccess(int line, SharedCallAppearanceTestAccount& account, SipSharedCallAppearanceSetHandle scaSet, SipSharedCallAppearanceHandle sca);
   static void waitForConversationStateChanged_(int line, TestAccount& account, SipConversationHandle conversation, ConversationState conversationState);
   static void waitForConversationEnded_(int line, TestAccount& account, SipConversationHandle conversation, ConversationEndReason endReason);
};

// Ensure proper parsing of <appearance> and <exclusive> XML elements
TEST_F(SharedCallAppearanceTest, AppearanceAndExclusiveElements)
{
   cpc::string xml;

   DialogInfoDocument dialogInfoDoc;

   xml += "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
   xml += "<dialog-info xmlns=\"urn:ietf:params:xml:ns:dialog-info\" xmlns:sa=\"urn:ietf:params:xml:ns:sa-dialog-info\" version=\"1\" state=\"full\" entity=\"sip:+<EMAIL>\">\n";
   xml += "  <dialog id=\"1\" call-id=\"<EMAIL>\" local-tag=\"5444a41f-54491044da19623-Sit-lucentNGFS-023554\" remote-tag=\"5444a41f-54491044c7f4d0b-Soo-lucentNGFS-023553\" direction=\"recipient\">\n";
   xml += "    <sa:exclusive>true</sa:exclusive>\n";
   xml += "    <sa:appearance>1</sa:appearance>\n";
   xml += "    <state>trying</state>\n";
   xml += "    <local>\n";
   xml += "      <identity>sip:+<EMAIL></identity>\n";
   xml += "    </local>\n";
   xml += "    <remote>\n";
   xml += "     <identity display-name=\"**********\">sip:+<EMAIL></identity>\n";
   xml += "    </remote>\n";
   xml += "  </dialog>\n";
   xml += "</dialog-info>\n";

   ASSERT_TRUE(DialogInfoDocumentHelper::fromXml(xml, dialogInfoDoc, true));
   ASSERT_EQ(dialogInfoDoc.version, 1);
   ASSERT_EQ(dialogInfoDoc.state, DialogInfoDocumentState_Full);
   ASSERT_EQ(dialogInfoDoc.entity, "sip:+<EMAIL>");
   ASSERT_EQ(dialogInfoDoc.dialogs.size(), 1);
   ASSERT_EQ(dialogInfoDoc.dialogs[0].id, "1");
   ASSERT_EQ(dialogInfoDoc.dialogs[0].dialogId.callId, "<EMAIL>");
   ASSERT_EQ(dialogInfoDoc.dialogs[0].dialogId.localTag, "5444a41f-54491044da19623-Sit-lucentNGFS-023554");
   ASSERT_EQ(dialogInfoDoc.dialogs[0].dialogId.remoteTag, "5444a41f-54491044c7f4d0b-Soo-lucentNGFS-023553");
   ASSERT_EQ(dialogInfoDoc.dialogs[0].direction, DialogDirection_Recipient);
   ASSERT_EQ(dialogInfoDoc.dialogs[0].appearance, 1);
   ASSERT_EQ(dialogInfoDoc.dialogs[0].exclusive, true);
   ASSERT_EQ(dialogInfoDoc.dialogs[0].stateInfo.state, DialogState_Trying);
   ASSERT_EQ(dialogInfoDoc.dialogs[0].localParticipant.identity.address, "sip:+<EMAIL>");
   ASSERT_EQ(dialogInfoDoc.dialogs[0].remoteParticipant.identity.address, "sip:+<EMAIL>");
   ASSERT_EQ(dialogInfoDoc.dialogs[0].remoteParticipant.identity.displayName, "**********");
}

// Invalid account handle passed in
TEST_F(SharedCallAppearanceTest, SubscribeInvalidAccountFailure)
{
   SharedCallAppearanceTestAccount alice("alice"); // SCAP Integrated Client

   // Create a new SCA set using an invalid account
   SipSharedCallAppearanceSetSettings settings;
   SipSharedCallAppearanceSetHandle scaSet = alice.scaManager->createSharedCallAppearanceSet(123, settings);

   // Expect an error 
   assertPhoneError(alice, "Creating SCA set with invalid account handle: 123, SipSharedCallAppearanceSetHandle invalid: " + cpc::to_string(scaSet));
}

// Account passed in disabled
TEST_F(SharedCallAppearanceTest, SubscribeAccountDisabledFailure)
{
   SharedCallAppearanceTestAccount alice("alice", false); // SCAP Integrated Client

   // Disable the account
   alice.account->disable(alice.handle);
   assertAccountDeregistering(alice);
   assertAccountDeregistered(alice);

   // Create a new SCA set with a disabled account
   SipSharedCallAppearanceSetSettings settings;
   SipSharedCallAppearanceSetHandle scaSet = alice.scaManager->createSharedCallAppearanceSet(alice.handle, settings);

   // Expect an error
   assertPhoneError(alice, "Creating SCA set before account enabled: " + cpc::to_string(alice.handle) + ", SipSharedCallAppearanceSetHandle invalid: " + cpc::to_string(scaSet));
}

// ALU17 - No SCA to subscribe to
TEST_F(SharedCallAppearanceTest, SubscribeNoSharedCallAppearanceFailure) 
{
   SharedCallAppearanceTestAccount alice("alice"); // SCAP Integrated Client

   // Start a subscription without any SCA
   SipSharedCallAppearanceSetSettings settings;
   SipDialogEventSubscriptionHandle scaSet = alice.scaManager->createSharedCallAppearanceSet(alice.handle, settings);
   alice.scaManager->start(scaSet);

   // Expect an error
   assertSCAError(alice, scaSet, "Cannot start subscriptions. No SCA have been added");
}

// ALU17 - Subscribe to too many SCAs
TEST_F(SharedCallAppearanceTest, SubscribeTooManySharedCallAppearanceFailure) 
{
   SharedCallAppearanceTestAccount alice("alice"); // SCAP Integrated Client

   // Start a subscription without any SCA
   SipSharedCallAppearanceSetSettings settings;
   SipDialogEventSubscriptionHandle scaSet = alice.scaManager->createSharedCallAppearanceSet(alice.handle, settings);
   SipSharedCallAppearanceSettings scaSettings;
   for (int i = 0; i < 22; i++)
   {
      alice.scaManager->addSharedCallAppearance(scaSet, "sca" + cpc::to_string(i) + "@domain", scaSettings);
   }
   alice.scaManager->start(scaSet);

   // Expect an error
   assertSCAError(alice, scaSet, "Cannot start subscriptions. Too many SCAs have been added");
}

// ALU17 - Invalid URI passed as a SCA
TEST_F(SharedCallAppearanceTest, InvalidSharedCallAppearanceAddressFailure)
{
   SharedCallAppearanceTestAccount alice("alice"); // SCAP Integrated Client

   // Alice subscribes to an invalid SCA
   SipSharedCallAppearanceSetSettings settings;
   settings.expires = 3600;
   SipSharedCallAppearanceHandle sca = "sip:alice@invalid"; // Invalid URI
   SipSharedCallAppearanceSetHandle scaSet = alice.scaManager->createSharedCallAppearanceSet(alice.handle, settings);
   ASSERT_TRUE(scaSet > 0);
   SipSharedCallAppearanceSettings scaSettings;
   assertSuccess(alice.scaManager->addSharedCallAppearance(scaSet, sca, scaSettings));
   
   // Start the subscription
   assertSuccess(alice.scaManager->start(scaSet));

   // Wait for the subscription state to transition to Terminated
   waitForSCASubscriptionEnded(alice, scaSet, sca);
}

// ALU17 - Subscribe to one SCA which accepts the incoming subscription
TEST_F(SharedCallAppearanceTest, SubscribeSharedCallAppearanceAccepted)
{
   SharedCallAppearanceTestAccount alice("alice"); // SCAP Integrated client

   SipSharedCallAppearanceHandle helpdeskSCA = alice.config.uri();

   // Alice subscribes to the SCA
   SipSharedCallAppearanceSetHandle scaSet;
   startSubscription(alice, scaSet, helpdeskSCA);

   // Terminate subscription
   terminateSubscription(alice, scaSet, helpdeskSCA);
}

// ALU17 - Subscribe to one SCA which accepts the incoming subscription
TEST_F(SharedCallAppearanceTest, SubscribeSharedCallAppearanceAcceptedWithCall)
{
   SharedCallAppearanceTestAccount alice("alice"); // SCAP Integrated Client - Helpdesk
   SharedCallAppearanceTestAccount bob("bob");     // SCAP External Client - Helpdesk
   StandardLineTestAccount charlie("charlie");     // Standard SIP line 

   SipSharedCallAppearanceHandle helpdeskSCA = alice.config.uri();

   // Alice and Bob subscribe to SCA
   SipSharedCallAppearanceSetHandle aliceSCASet;
   startSubscription(alice, aliceSCASet, helpdeskSCA);
   SipSharedCallAppearanceSetHandle bobSCASet;
   startSubscription(bob, bobSCASet, helpdeskSCA);

   // Charlie calls Helpdesk SCA
   SipConversationHandle charlieCall = charlie.conversation->createConversation(charlie.handle);
   charlie.conversation->addParticipant(charlieCall, helpdeskSCA);
   charlie.conversation->start(charlieCall);
   assertNewConversationOutgoing(charlie, charlieCall, helpdeskSCA);
   assertSCAStateChanged_ex(alice, aliceSCASet, helpdeskSCA, DialogDirection_Recipient, DialogState_Trying, helpdeskSCA, charlie.config.uri(), 1);
   assertSCAStateChanged_ex(bob, bobSCASet, helpdeskSCA, DialogDirection_Recipient, DialogState_Trying, helpdeskSCA, charlie.config.uri(), 1);

   // Alice's and Bob's phones ring
   SipConversationHandle aliceCall;
   assertNewConversationIncoming(alice, &aliceCall, charlie.config.uri());
   SipConversationHandle bobCall;
   assertNewConversationIncoming(bob, &bobCall, charlie.config.uri());
   bob.conversation->sendRingingResponse(bobCall);
   assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
   assertConversationStateChanged(charlie, charlieCall, ConversationState_RemoteRinging);
   assertSCAStateChanged_ex(bob, bobSCASet, helpdeskSCA, DialogDirection_Recipient, DialogState_Early, helpdeskSCA, charlie.config.uri(), 1);
   assertSCAStateChanged_ex(alice, aliceSCASet, helpdeskSCA, DialogDirection_Recipient, DialogState_Early, helpdeskSCA, charlie.config.uri(), 1);

   // Bob picks up the call
   bob.conversation->accept(bobCall);
   waitForConversationStateChanged(bob, bobCall, ConversationState_Connected);
   assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
   bob.conversation->accept(bobCall);
   assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
   waitForConversationStateChanged(charlie, charlieCall, ConversationState_Connected);

   // Alice's phone stops ringing
   assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);

   // Wait for the notifications (Confirmed state)
   assertSCAStateChanged_ex(bob, bobSCASet, helpdeskSCA, DialogDirection_Recipient, DialogState_Confirmed, helpdeskSCA, charlie.config.uri(), 1);
   assertSCAStateChanged_ex(alice, aliceSCASet, helpdeskSCA, DialogDirection_Recipient, DialogState_Confirmed, helpdeskSCA, charlie.config.uri(), 1);

   // ===> Bob is talking to Charlie
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   // Bob hangs up the call
   bob.conversation->end(bobCall);
   assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedRemotely);   
   assertSCAStateChanged_ex(bob, bobSCASet, helpdeskSCA, DialogDirection_Recipient, DialogState_Terminated, helpdeskSCA, charlie.config.uri(), 1);
   assertSCAStateChanged_ex(alice, aliceSCASet, helpdeskSCA, DialogDirection_Recipient, DialogState_Terminated, helpdeskSCA, charlie.config.uri(), 1);

   // Alice and Bob terminate subscription to SCA 
   terminateSubscription(alice, aliceSCASet, helpdeskSCA);
   terminateSubscription(bob, bobSCASet, helpdeskSCA);
}

// ALU19 - Answer an inbound call on a SCA
TEST_F(SharedCallAppearanceTest, InboundCallOnSCA)
{
   SharedCallAppearanceTestAccount alice("alice"); // SCAP Integrated Client - Helpdesk
   StandardLineTestAccount charlie("charlie");     // Standard SIP line 

   SipSharedCallAppearanceHandle helpdeskSCA = alice.config.uri();

   inboundCallOnSCA(alice, charlie, helpdeskSCA, false);
}

TEST_F(SharedCallAppearanceTest, InboundCallOnSCA2)
{
   SharedCallAppearanceTestAccount bob("bob"); // SCAP External Client - Helpdesk
   StandardLineTestAccount charlie("charlie"); // Standard SIP line 

   SipSharedCallAppearanceHandle helpdeskSCA = SCAP_PUID;

   inboundCallOnSCA(bob, charlie, helpdeskSCA, true);
}

void SharedCallAppearanceTest::inboundCallOnSCA(SharedCallAppearanceTestAccount& bob, StandardLineTestAccount& charlie, const cpc::string& helpdeskSCA, bool makeExclusive)
{
   // Bob subscribes to SCA
   SipSharedCallAppearanceSetHandle bobSCASet;
   startSubscription(bob, bobSCASet, helpdeskSCA);

   // Charlie calls Helpdesk SCA
   SipConversationHandle charlieCall = charlie.conversation->createConversation(charlie.handle);
   charlie.conversation->addParticipant(charlieCall, helpdeskSCA);
   charlie.conversation->start(charlieCall);
   assertNewConversationOutgoing(charlie, charlieCall, helpdeskSCA);

   // Bob's phone ringing
   assertSCAStateChanged_ex(bob, bobSCASet, helpdeskSCA, DialogDirection_Recipient, DialogState_Trying, helpdeskSCA, charlie.config.uri(), 1);
   SipConversationHandle bobCall;
   assertNewConversationIncoming_ex(bob, &bobCall, charlie.config.uri(), [&](const NewConversationEvent& evt) {
      // Extract the appearance number from the incoming INVITE. Note that the appearance number can also be obtained from SipSharedCallApperanceStateChangedEvent
      int appearance = bob.scaManager->getAppearanceForScapCall(evt.alertInfoHeader);
      ASSERT_EQ(1, appearance);
   });
   bob.conversation->sendRingingResponse(bobCall);
   assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
   assertConversationStateChanged(charlie, charlieCall, ConversationState_RemoteRinging);
   assertSCAStateChanged_ex(bob, bobSCASet, helpdeskSCA, DialogDirection_Recipient, DialogState_Early, helpdeskSCA, charlie.config.uri(), 1);

   // Bob picks up the call
   bob.conversation->accept(bobCall);
   waitForConversationStateChanged(bob, bobCall, ConversationState_Connected);
   assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
   bob.conversation->accept(bobCall);
   assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
   waitForConversationStateChanged(charlie, charlieCall, ConversationState_Connected);
   assertSCAStateChanged_ex(bob, bobSCASet, helpdeskSCA, DialogDirection_Recipient, DialogState_Confirmed, helpdeskSCA, charlie.config.uri(), 1);

   // ===> Bob is talking to Charlie
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   // Make sure the call is no longer exclusive once it's established
   assertSCACallExclusiveState(bob, bobSCASet, helpdeskSCA, 1, false);

   if (makeExclusive)
   {
      // Mark call as exclusive
      assertSuccess(bob.scaManager->makeExclusive(bobSCASet, helpdeskSCA, 1)); // Appearance number can be retrieved from notification

      // Make sure the operation was successful and that the call is now exclusive
      assertSCAStateChanged_ex(bob, bobSCASet, helpdeskSCA, DialogDirection_Recipient, DialogState_Confirmed, helpdeskSCA, charlie.config.uri(), 1);
      assertSCACallExclusiveState(bob, bobSCASet, helpdeskSCA, 1, true);
      assertSCAMakeExclusiveSuccess(bob, bobSCASet, helpdeskSCA);
   }

   // Bob hangs up the call
   bob.conversation->end(bobCall);
   assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedRemotely);   
   assertSCAStateChanged_ex(bob, bobSCASet, helpdeskSCA, DialogDirection_Recipient, DialogState_Terminated, helpdeskSCA, charlie.config.uri(), 1);

   // Terminate the subscription with SCA
   terminateSubscription(bob, bobSCASet, helpdeskSCA);
}

// ALU18 - Automatically selecting a SCA for an outbound call (SCAP Integrated Client)
TEST_F(SharedCallAppearanceTest, OutboundCallOnSCA)
{
   SharedCallAppearanceTestAccount alice("alice"); // SCAP Integrated Client - Helpdesk
   StandardLineTestAccount charlie("charlie");     // Standard SIP line 

   SipSharedCallAppearanceHandle helpdeskSCA = alice.config.uri();

   outboundCallOnSCA(alice, charlie, helpdeskSCA);
}

// ALU18 - Automatically selecting a SCA for an outbound call (SCAP External Client)
TEST_F(SharedCallAppearanceTest, OutboundCallOnSCA2)
{
   SharedCallAppearanceTestAccount bob("bob"); // SCAP External Client - Helpdesk
   StandardLineTestAccount charlie("charlie"); // Standard SIP line 

   SipSharedCallAppearanceHandle helpdeskSCA = SCAP_PUID;

   outboundCallOnSCA(bob, charlie, helpdeskSCA);
}

void SharedCallAppearanceTest::outboundCallOnSCA(SharedCallAppearanceTestAccount& alice, StandardLineTestAccount& charlie, const cpc::string& helpdeskSCA)
{
   // Alice subscribes to SCA
   SipSharedCallAppearanceSetHandle aliceSCASet;
   startSubscription(alice, aliceSCASet, helpdeskSCA);

   // Alice calls Charlie using HelpDesk SCA
   SipConversationHandle aliceCall = alice.scaManager->makeCall(aliceSCASet, helpdeskSCA, charlie.config.uri());
   ASSERT_TRUE(aliceCall > 0);
   assertNewConversationOutgoing(alice, aliceCall, charlie.config.uri());
   assertSCAStateChanged_ex(alice, aliceSCASet, helpdeskSCA, DialogDirection_Initiator, DialogState_Trying, helpdeskSCA, charlie.config.uri(), 1); // This notification contains the appearance

   // Charlie's phone is ringing
   SipConversationHandle charlieCall;
   assertNewConversationIncoming(charlie, &charlieCall, helpdeskSCA);
   charlie.conversation->sendRingingResponse(charlieCall);
   assertConversationStateChanged(charlie, charlieCall, ConversationState_LocalRinging);
   assertSCAStateChanged_ex(alice, aliceSCASet, helpdeskSCA, DialogDirection_Initiator, DialogState_Early, helpdeskSCA, charlie.config.uri(), 1);
   assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
   assertConversationStateChanged_ex(alice, aliceCall, [&](const ConversationStateChangedEvent& evt) {
      ASSERT_EQ(ConversationState_RemoteRinging, evt.conversationState);
      // Extract the appearance number from the ringing response.
      // Note that the appearance number can also be obtained from SipSharedCallAppearanceStateChangedEvent
      int appearance = alice.scaManager->getAppearanceForScapCall(evt.alertInfoHeader);
      ASSERT_EQ(1, appearance);
   });

   // Charlie picks up the call
   charlie.conversation->accept(charlieCall);
   waitForConversationStateChanged(charlie, charlieCall, ConversationState_Connected);
   waitForConversationStateChanged(alice, aliceCall, ConversationState_Connected);

   // ===> Alice is talking to Charlie
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   // Alice hangs up the call
   assertSuccess(alice.conversation->end(aliceCall));
   assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedRemotely);
   assertSCAStateChanged_ex(alice, aliceSCASet, helpdeskSCA, DialogDirection_Initiator, DialogState_Terminated, helpdeskSCA, charlie.config.uri(), 1);

   // Terminate the subscription with SCA
   terminateSubscription(alice, aliceSCASet, helpdeskSCA);
}

// ALU18 - Automatically selecting a SCA for an outbound conference call
TEST_F(SharedCallAppearanceTest, OutboundConferenceCallOnSCA)
{
   SharedCallAppearanceTestAccount alice("alice"); // SCAP Integrated Client - Helpdesk
   StandardLineTestAccount charlie("charlie");     // Standard SIP line 

   SipSharedCallAppearanceHandle helpdeskSCA = alice.config.uri();

   // Alice subscribes to SCA
   SipSharedCallAppearanceSetHandle aliceSCASet;
   SipSharedCallAppearanceSetSettings scaSetSettings;
   scaSetSettings.conferenceFactoryUri = "sip:<EMAIL>";
   startSubscription(alice, aliceSCASet, helpdeskSCA, scaSetSettings);

   // Alice calls Charlie using HelpDesk SCA
   SipConversationHandle aliceCall = alice.scaManager->makeCall(aliceSCASet, helpdeskSCA, charlie.config.uri());
   ASSERT_TRUE(aliceCall > 0);
   assertNewConversationOutgoing(alice, aliceCall, charlie.config.uri());
   assertSCAStateChanged_ex(alice, aliceSCASet, helpdeskSCA, DialogDirection_Initiator, DialogState_Trying, helpdeskSCA, charlie.config.uri(), 1); // This notification contains the appearance

   // Charlie's phone is ringing
   SipConversationHandle charlieCall;
   assertNewConversationIncoming(charlie, &charlieCall, alice.config.uri());
   charlie.conversation->sendRingingResponse(charlieCall);
   assertConversationStateChanged(charlie, charlieCall, ConversationState_LocalRinging);
   assertSCAStateChanged_ex(alice, aliceSCASet, helpdeskSCA, DialogDirection_Initiator, DialogState_Early, helpdeskSCA, charlie.config.uri(), 1);
   assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);

   // Charlie picks up the call
   charlie.conversation->accept(charlieCall);
   waitForConversationStateChanged(charlie, charlieCall, ConversationState_Connected);
   waitForConversationStateChanged(alice, aliceCall, ConversationState_Connected);

   // ===> Alice is talking to Charlie
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   // Alice makes a conference call and includes Charlie
   cpc::vector<SipConversationHandle> otherConversations;
   otherConversations.push_back(aliceCall);
   SipConversationHandle aliceConfCall = alice.scaManager->makeConferenceCall(aliceSCASet, helpdeskSCA, otherConversations);
   ASSERT_TRUE(aliceConfCall > 0);

   // Wait for the call to conference the conference server to be setup
   assertNewConversationOutgoing(alice, aliceConfCall, scaSetSettings.conferenceFactoryUri);
   assertConversationMediaChanged(alice, aliceConfCall, MediaDirection_SendReceive);
   assertConversationStateChanged(alice, aliceConfCall, ConversationState_Connected);

   // Charlie's call (RTP) gets redirected to the conference server
   assertConversationMediaChangeRequest(charlie, charlieCall, MediaDirection_SendReceive);
   charlie.conversation->accept(charlieCall);
   assertConversationMediaChanged_ex(charlie, charlieCall, [](const ConversationMediaChangedEvent& evt){
      ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      ASSERT_EQ(MediaDirection_Inactive, evt.localMediaInfo[1].mediaDirection);
      });

   // Wait for Alice's original call to be terminated
   assertTransferProgress(alice, aliceCall, TransferProgressEventType_Trying);
   assertTransferProgress(alice, aliceCall, TransferProgressEventType_Connected);
   assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);

   // ===> Alice and Charlie are in the conference
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   // Alice hangs up the call with the conference. Charlie's call gets terminated
   assertSuccess(alice.conversation->end(aliceConfCall));
   assertConversationEnded(alice, aliceConfCall, ConversationEndReason_UserTerminatedLocally);
   assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedRemotely);
   assertSCAStateChanged(alice, aliceSCASet, helpdeskSCA);

   // Terminate the subscription with SCA
   terminateSubscription(alice, aliceSCASet, helpdeskSCA);
}

// ALU19 - Two simultaneous calls on the same SCA, two different appearances
TEST_F(SharedCallAppearanceTest, TwoOutboundCallsOnSCA)
{
   SharedCallAppearanceTestAccount alice("alice"); // SCAP Integrated Client - Helpdesk
   SharedCallAppearanceTestAccount bob("bob");     // SCAP External Client - Helpdesk
   StandardLineTestAccount charlie("charlie");     // Standard SIP line 
   StandardLineTestAccount dave("dave");           // Standard SIP line 

   SipSharedCallAppearanceHandle helpdeskSCA = alice.config.uri();

   // Alice subscribes to SCA
   SipSharedCallAppearanceSetHandle aliceSCASet;
   startSubscription(alice, aliceSCASet, helpdeskSCA);

   // Alice calls Charlie using HelpDesk SCA
   SipConversationHandle aliceCall = alice.scaManager->makeCall(aliceSCASet, helpdeskSCA, charlie.config.uri());
   ASSERT_TRUE(aliceCall > 0);
   assertNewConversationOutgoing(alice, aliceCall, charlie.config.uri());
   assertSCAStateChanged_ex(alice, aliceSCASet, helpdeskSCA, DialogDirection_Initiator, DialogState_Trying, helpdeskSCA, charlie.config.uri(), 1); // This notification contains the appearance

   // Charlie's phone is ringing
   SipConversationHandle charlieCall;
   assertNewConversationIncoming(charlie, &charlieCall, helpdeskSCA);
   charlie.conversation->sendRingingResponse(charlieCall);
   assertConversationStateChanged(charlie, charlieCall, ConversationState_LocalRinging);
   assertSCAStateChanged_ex(alice, aliceSCASet, helpdeskSCA, DialogDirection_Initiator, DialogState_Early, helpdeskSCA, charlie.config.uri(), 1);
   assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);

   // Charlie picks up the call
   charlie.conversation->accept(charlieCall);
   waitForConversationStateChanged(charlie, charlieCall, ConversationState_Connected);
   waitForConversationStateChanged(alice, aliceCall, ConversationState_Connected);

   // ===> Alice is talking to Charlie
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   // Bob subscribes to SCA
   SipSharedCallAppearanceSetHandle bobSCASet;
   startSubscription(bob, bobSCASet, helpdeskSCA);

   // Bob calls Dave using HelpDesk SCA
   SipConversationHandle bobCall = bob.scaManager->makeCall(bobSCASet, helpdeskSCA, dave.config.uri());
   ASSERT_TRUE(bobCall > 0);
   assertNewConversationOutgoing(bob, bobCall, dave.config.uri());
   assertSCAStateChanged_ex(bob, bobSCASet, helpdeskSCA, DialogDirection_Initiator, DialogState_Trying, helpdeskSCA, dave.config.uri(), 2); // This notification contains the appearance
   assertSCAStateChanged_ex(alice, aliceSCASet, helpdeskSCA, DialogDirection_Initiator, DialogState_Trying, helpdeskSCA, dave.config.uri(), 2);

   // Dave's phone is ringing
   SipConversationHandle daveCall;
   assertNewConversationIncoming(dave, &daveCall, helpdeskSCA);
   dave.conversation->sendRingingResponse(daveCall);
   assertConversationStateChanged(dave, daveCall, ConversationState_LocalRinging);
   assertSCAStateChanged_ex(bob, bobSCASet, helpdeskSCA, DialogDirection_Initiator, DialogState_Early, helpdeskSCA, dave.config.uri(), 2);
   assertSCAStateChanged_ex(alice, aliceSCASet, helpdeskSCA, DialogDirection_Initiator, DialogState_Early, helpdeskSCA, dave.config.uri(), 2);
   assertConversationStateChanged(bob, bobCall, ConversationState_RemoteRinging);

   // Dave picks up the call
   dave.conversation->accept(daveCall);
   waitForConversationStateChanged(dave, daveCall, ConversationState_Connected);
   waitForConversationStateChanged(bob, bobCall, ConversationState_Connected);
   assertSCAStateChanged_ex(alice, aliceSCASet, helpdeskSCA, DialogDirection_Initiator, DialogState_Confirmed, helpdeskSCA, dave.config.uri(), 2);

   // ===> Bob is talking to Dave
   // ===> Alice is talking to Charlie
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   // Alice hangs up the call
   assertSuccess(alice.conversation->end(aliceCall));
   assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedRemotely);

   // ===> Bob is talking to Dave
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   // Bob hangs up the call
   assertSuccess(bob.conversation->end(bobCall));
   waitForConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   waitForConversationEnded(dave, daveCall, ConversationEndReason_UserTerminatedRemotely);

   // Terminate the subscription with SCA
   terminateSubscription(alice, aliceSCASet, helpdeskSCA);
   terminateSubscription(bob, bobSCASet, helpdeskSCA);
}

// ALU20 - Local hold/unhold with SCA
TEST_F(SharedCallAppearanceTest, LocalHoldUnholdOnSCA)
{
   SharedCallAppearanceTestAccount alice("alice"); // SCAP Integrated Client - Helpdesk
   StandardLineTestAccount charlie("charlie");     // Standard SIP line 

   SipSharedCallAppearanceHandle helpdeskSCA = alice.config.uri();

   // Alice subscribes to SCA
   SipSharedCallAppearanceSetHandle aliceSCASet;
   startSubscription(alice, aliceSCASet, helpdeskSCA);

   // Alice calls Charlie using HelpDesk SCA
   SipConversationHandle aliceCall = alice.scaManager->makeCall(aliceSCASet, helpdeskSCA, charlie.config.uri());
   ASSERT_TRUE(aliceCall > 0);
   assertNewConversationOutgoing(alice, aliceCall, charlie.config.uri());
   assertSCAStateChanged_ex(alice, aliceSCASet, helpdeskSCA, DialogDirection_Initiator, DialogState_Trying, helpdeskSCA, charlie.config.uri(), 1); // This notification contains the appearance

   // Charlie's phone is ringing
   SipConversationHandle charlieCall;
   assertNewConversationIncoming(charlie, &charlieCall, alice.config.uri());
   charlie.conversation->sendRingingResponse(charlieCall);
   assertConversationStateChanged(charlie, charlieCall, ConversationState_LocalRinging);
   assertSCAStateChanged_ex(alice, aliceSCASet, helpdeskSCA, DialogDirection_Initiator, DialogState_Early, helpdeskSCA, charlie.config.uri(), 1);
   assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);

   // Charlie picks up the call
   charlie.conversation->accept(charlieCall);
   waitForConversationStateChanged(charlie, charlieCall, ConversationState_Connected);
   waitForConversationStateChanged(alice, aliceCall, ConversationState_Connected);
   
   // ===> Alice is talking to Charlie
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   // Alice puts call on hold
   assertSuccess(alice.conversation->hold(aliceCall));
   assertConversationMediaChangeRequest(charlie, charlieCall, MediaDirection_SendOnly);
   assertSuccess(charlie.conversation->accept(charlieCall));
   assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendOnly);
   assertConversationMediaChanged(charlie, charlieCall, MediaDirection_ReceiveOnly);
   assertSCAStateChanged_ex(alice, aliceSCASet, helpdeskSCA, DialogDirection_Initiator, DialogState_Confirmed, helpdeskSCA, charlie.config.uri(), 1);

   // Make sure the call is in held state
   assertSCACallHeldState(alice, aliceSCASet, helpdeskSCA, 1, true);

   // Call is held
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));      

   // Alice puts call off hold
   assertSuccess(alice.conversation->unhold(aliceCall));
   assertConversationMediaChangeRequest(charlie, charlieCall, MediaDirection_SendReceive);
   assertSuccess(charlie.conversation->accept(charlieCall));
   assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
   assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);
   assertSCAStateChanged_ex(alice, aliceSCASet, helpdeskSCA, DialogDirection_Initiator, DialogState_Confirmed, helpdeskSCA, charlie.config.uri(), 1);

   // Make sure the call is no longer in held state
   assertSCACallHeldState(alice, aliceSCASet, helpdeskSCA, 1, false);

   // Call is no longer held
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));      

   // Alice hangs up the call
   assertSuccess(alice.conversation->end(aliceCall));
   assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedRemotely);
   assertSCAStateChanged_ex(alice, aliceSCASet, helpdeskSCA, DialogDirection_Initiator, DialogState_Terminated, helpdeskSCA, charlie.config.uri(), 1);

   // Terminate the subscription with SCA
   terminateSubscription(alice, aliceSCASet, helpdeskSCA);
}

// ALU21 - SCAP hold with SCA
// ALU22 - SCAP unhold
TEST_F(SharedCallAppearanceTest, SCAPHoldUnholdOnSCA)
{
   SharedCallAppearanceTestAccount alice("alice"); // SCAP Integrated Client - Helpdesk
   StandardLineTestAccount charlie("charlie");     // Standard SIP line 

   SipSharedCallAppearanceHandle helpdeskSCA = alice.config.uri();

   // Alice subscribes to SCA
   SipSharedCallAppearanceSetHandle aliceSCASet;
   SipSharedCallAppearanceSetSettings scaSetSettings;
   scaSetSettings.scapHoldUri = "sip:<EMAIL>";
   SipSharedCallAppearanceSettings scaSettings;
   scaSettings.allowScapHoldJoin = true;
   startSubscription(alice, aliceSCASet, helpdeskSCA, scaSetSettings, scaSettings);

   // Alice calls Charlie using HelpDesk SCA
   SipConversationHandle aliceCall = alice.scaManager->makeCall(aliceSCASet, helpdeskSCA, charlie.config.uri());
   ASSERT_TRUE(aliceCall > 0);
   assertNewConversationOutgoing(alice, aliceCall, charlie.config.uri());
   assertSCAStateChanged_ex(alice, aliceSCASet, helpdeskSCA, DialogDirection_Initiator, DialogState_Trying, helpdeskSCA, charlie.config.uri(), 1); // This notification contains the appearance

   // Charlie's phone is ringing
   SipConversationHandle charlieCall;
   assertNewConversationIncoming(charlie, &charlieCall, alice.config.uri());
   charlie.conversation->sendRingingResponse(charlieCall);
   assertConversationStateChanged(charlie, charlieCall, ConversationState_LocalRinging);
   assertSCAStateChanged_ex(alice, aliceSCASet, helpdeskSCA, DialogDirection_Initiator, DialogState_Early, helpdeskSCA, charlie.config.uri(), 1);
   assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);

   // Charlie picks up the call
   charlie.conversation->accept(charlieCall);
   waitForConversationStateChanged(charlie, charlieCall, ConversationState_Connected);
   waitForConversationStateChanged(alice, aliceCall, ConversationState_Connected);

   // ===> Alice is talking to Charlie
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   // Alice puts call on SCAP hold. Alice's call is terminated
   assertSuccess(alice.scaManager->scapHold(aliceSCASet, helpdeskSCA, aliceCall));
   assertSCAStateChanged_ex(alice, aliceSCASet, helpdeskSCA, DialogDirection_Initiator, DialogState_Confirmed, helpdeskSCA, charlie.config.uri(), 1);
   assertTransferProgress(alice, aliceCall, TransferProgressEventType_Connected);
   assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);

   // Charlie's call (i.e. RTP) is redirected to SCAP-Hold server
   assertConversationMediaChangeRequest(charlie, charlieCall, MediaDirection_SendOnly);
   assertSuccess(charlie.conversation->accept(charlieCall));
   assertConversationMediaChanged(charlie, charlieCall, MediaDirection_ReceiveOnly);

   // Make sure the call is in SCAP held state
   assertSCACallScapHeldState(alice, aliceSCASet, helpdeskSCA, 1, true);

   // Charlie's call is SCAP held
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   // Now Alice picks up the SCAP held call
   SipConversationHandle aliceNewCall = alice.scaManager->scapUnhold(aliceSCASet, helpdeskSCA, 1);  // Appearance number can be retrieved from notification or state
   ASSERT_TRUE(aliceNewCall > 0);
   assertNewConversationOutgoing(alice, aliceNewCall, helpdeskSCA);

   // Charlie's call (i.e. RTP) is redirected to Alice
   assertConversationMediaChangeRequest(charlie, charlieCall, MediaDirection_SendReceive);
   assertSuccess(charlie.conversation->accept(charlieCall));
   assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);
   waitForConversationStateChanged(alice, aliceNewCall, ConversationState_Connected);

   // ===> Alice is talking to Charlie again
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   // Alice hangs up the call
   assertSuccess(alice.conversation->end(aliceNewCall));
   assertConversationEnded(alice, aliceNewCall, ConversationEndReason_UserTerminatedLocally);
   assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedRemotely);
   assertSCAStateChanged_ex(alice, aliceSCASet, helpdeskSCA, DialogDirection_Initiator, DialogState_Terminated, helpdeskSCA, charlie.config.uri(), 1);

   // Terminate the subscription with SCA
   terminateSubscription(alice, aliceSCASet, helpdeskSCA);
}

// ALU23 - SCAP bridge-in
// ALU26 - Join a call that is in the "confirmed" state at the monitored endpoint
TEST_F(SharedCallAppearanceTest, SCAPBridgeInOnSCA)
{
   SharedCallAppearanceTestAccount alice("alice"); // SCAP Integrated Client - Helpdesk
   SharedCallAppearanceTestAccount bob("bob");     // SCAP External Client - Helpdesk
   StandardLineTestAccount charlie("charlie");     // Standard SIP line 

   SipSharedCallAppearanceHandle helpdeskSCA = alice.config.uri();

   // Alice and Bob subscribe to SCA
   SipSharedCallAppearanceSetHandle aliceSCASet;
   startSubscription(alice, aliceSCASet, helpdeskSCA);
   SipSharedCallAppearanceSetHandle bobSCASet;
   startSubscription(bob, bobSCASet, helpdeskSCA);

   // Charlie calls Helpdesk SCA
   SipConversationHandle charlieCall = charlie.conversation->createConversation(charlie.handle);
   charlie.conversation->addParticipant(charlieCall, helpdeskSCA);
   charlie.conversation->start(charlieCall);
   assertNewConversationOutgoing(charlie, charlieCall, helpdeskSCA);
   assertSCAStateChanged_ex(alice, aliceSCASet, helpdeskSCA, DialogDirection_Recipient, DialogState_Trying, helpdeskSCA, charlie.config.uri(), 1);
   assertSCAStateChanged_ex(bob, bobSCASet, helpdeskSCA, DialogDirection_Recipient, DialogState_Trying, helpdeskSCA, charlie.config.uri(), 1);

   // Alice's and Bob's phones ring
   SipConversationHandle aliceCall;
   assertNewConversationIncoming(alice, &aliceCall, charlie.config.uri());
   SipConversationHandle bobCall;
   assertNewConversationIncoming(bob, &bobCall, charlie.config.uri());
   bob.conversation->sendRingingResponse(bobCall);
   assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
   assertConversationStateChanged(charlie, charlieCall, ConversationState_RemoteRinging);
   assertSCAStateChanged_ex(bob, bobSCASet, helpdeskSCA, DialogDirection_Recipient, DialogState_Early, helpdeskSCA, charlie.config.uri(), 1);
   assertSCAStateChanged_ex(alice, aliceSCASet, helpdeskSCA, DialogDirection_Recipient, DialogState_Early, helpdeskSCA, charlie.config.uri(), 1);

   // Bob picks up the call
   bob.conversation->accept(bobCall);
   waitForConversationStateChanged(bob, bobCall, ConversationState_Connected);
   assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
   bob.conversation->accept(bobCall);
   assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
   waitForConversationStateChanged(charlie, charlieCall, ConversationState_Connected);

   // Alice's phone stops ringing
   assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);

   // Wait for the notifications (Confirmed state). This also ensures that the call will no longer be exclusive when comes the time for Alice to join it
   assertSCAStateChanged_ex(bob, bobSCASet, helpdeskSCA, DialogDirection_Recipient, DialogState_Confirmed, helpdeskSCA, charlie.config.uri(), 1);
   assertSCAStateChanged_ex(alice, aliceSCASet, helpdeskSCA, DialogDirection_Recipient, DialogState_Confirmed, helpdeskSCA, charlie.config.uri(), 1);

   // ===> Bob is talking to Charlie
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   // Alice wants to join/bridge the call between Bob and Charlie
   SipConversationHandle aliceNewCall = alice.scaManager->scapBridgeIn(aliceSCASet, helpdeskSCA, 1); // Appearance number can be retrieved from notification
   ASSERT_TRUE(aliceNewCall > 0);

   // Wait for Bob to join the conference
   assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
   bob.conversation->accept(bobCall);
   assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
      ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      ASSERT_EQ(MediaDirection_Inactive, evt.localMediaInfo[1].mediaDirection);
      });

   // Wait for Charlie to join the conference
   assertConversationMediaChangeRequest(charlie, charlieCall, MediaDirection_SendReceive);
   charlie.conversation->accept(charlieCall);
   assertConversationMediaChanged_ex(charlie, charlieCall, [](const ConversationMediaChangedEvent& evt){
      ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      ASSERT_EQ(MediaDirection_Inactive, evt.localMediaInfo[1].mediaDirection);
      });

   // Wait for Alice to join the conference
   assertNewConversationOutgoing(alice, aliceNewCall, helpdeskSCA);
   assertConversationMediaChanged(alice, aliceNewCall, MediaDirection_SendReceive);
   assertConversationStateChanged(alice, aliceNewCall, ConversationState_Connected);
   assertSCAStateChanged_ex(bob, bobSCASet, helpdeskSCA, DialogDirection_Recipient, DialogState_Confirmed, helpdeskSCA, charlie.config.uri(), 1);
   assertSCAStateChanged_ex(alice, aliceSCASet, helpdeskSCA, DialogDirection_Recipient, DialogState_Confirmed, helpdeskSCA, charlie.config.uri(), 1);

   // ===> Alice, Bob and Charlie are now talking (in a conference)
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   // Charlie hangs up. The conference is terminated between Alice, Bob and Charlie
   charlie.conversation->end(charlieCall);
   assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedLocally);
   assertSCAStateChanged_ex(bob, bobSCASet, helpdeskSCA, DialogDirection_Recipient, DialogState_Terminated, helpdeskSCA, charlie.config.uri(), 1);
   assertSCAStateChanged_ex(alice, aliceSCASet, helpdeskSCA, DialogDirection_Recipient, DialogState_Terminated, helpdeskSCA, charlie.config.uri(), 1);
   assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   assertConversationEnded(alice, aliceNewCall, ConversationEndReason_UserTerminatedRemotely);

   // Terminate the subscription with SCA
   terminateSubscription(alice, aliceSCASet, helpdeskSCA);
}

// ALU24 - Exclusive use of a SCA
TEST_F(SharedCallAppearanceTest, ExclusiveUseOfSCA)
{
   SharedCallAppearanceTestAccount bob("bob"); // SCAP Integrated Client - Helpdesk
   StandardLineTestAccount charlie("charlie");     // Standard SIP line 

   SipSharedCallAppearanceHandle helpdeskSCA = SCAP_PUID;

   // Bob subscribes to SCA
   SipSharedCallAppearanceSetHandle bobSCASet;
   startSubscription(bob, bobSCASet, helpdeskSCA);

   // Bob calls Charlie using HelpDesk SCA
   SipConversationHandle bobCall = bob.scaManager->makeCall(bobSCASet, helpdeskSCA, charlie.config.uri());
   ASSERT_TRUE(bobCall > 0);
   assertNewConversationOutgoing(bob, bobCall, charlie.config.uri());
   assertSCAStateChanged_ex(bob, bobSCASet, helpdeskSCA, DialogDirection_Initiator, DialogState_Trying, helpdeskSCA, charlie.config.uri(), 1); // This notification contains the appearance

   // Charlie's phone is ringing
   SipConversationHandle charlieCall;
   assertNewConversationIncoming(charlie, &charlieCall, helpdeskSCA);
   charlie.conversation->sendRingingResponse(charlieCall);
   assertConversationStateChanged(charlie, charlieCall, ConversationState_LocalRinging);
   assertSCAStateChanged_ex(bob, bobSCASet, helpdeskSCA, DialogDirection_Initiator, DialogState_Early, helpdeskSCA, charlie.config.uri(), 1);
   assertConversationStateChanged(bob, bobCall, ConversationState_RemoteRinging);

   // Charlie picks up the call
   charlie.conversation->accept(charlieCall);
   waitForConversationStateChanged(charlie, charlieCall, ConversationState_Connected);
   waitForConversationStateChanged(bob, bobCall, ConversationState_Connected);

   // ===> Alice is talking to Charlie
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   // Make sure the call is no longer exclusive once it's established
   assertSCACallExclusiveState(bob, bobSCASet, helpdeskSCA, 1, false);

   // Mark call as exclusive
   assertSuccess(bob.scaManager->makeExclusive(bobSCASet, helpdeskSCA, 1)); // Appearance number can be retrieved from notification

   // Make sure the operation was successful and that the call is now exclusive
   assertSCAStateChanged_ex(bob, bobSCASet, helpdeskSCA, DialogDirection_Initiator, DialogState_Confirmed, helpdeskSCA, charlie.config.uri(), 1);
   assertSCACallExclusiveState(bob, bobSCASet, helpdeskSCA, 1, true);
   assertSCAMakeExclusiveSuccess(bob, bobSCASet, helpdeskSCA);

   // Alice hangs up the call
   assertSuccess(bob.conversation->end(bobCall));
   assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedRemotely);
   assertSCAStateChanged_ex(bob, bobSCASet, helpdeskSCA, DialogDirection_Initiator, DialogState_Terminated, helpdeskSCA, charlie.config.uri(), 1);

   // Terminate the subscription with SCA
   terminateSubscription(bob, bobSCASet, helpdeskSCA);
}

void SharedCallAppearanceTest::expectPhoneError(int line, TestAccount& account, const cpc::string& errorText)
{
   PhoneErrorEvent evt;
   cpc::string module;
   ASSERT_TRUE(account.events->expectEvent(line,
      "PhoneHandler::onError",
      15000, StrEqualsPred("SipAccountInterface"), module, evt));
   ASSERT_EQ(errorText, evt.errorText);
}

void SharedCallAppearanceTest::expectSCAError(int line, SharedCallAppearanceTestAccount& account, SipSharedCallAppearanceSetHandle scaSet, const cpc::string& errorText)
{
   SipSharedCallAppearanceSetHandle h;
   SipSharedCallAppearance::ErrorEvent evt;
   ASSERT_TRUE(account.scaEvents->expectEvent(line,
      "SipSharedCallAppearanceHandler::onError", 
      5000, AlwaysTruePred(), h, evt));
   ASSERT_EQ(scaSet, h);
	ASSERT_EQ(errorText, evt.errorText);
}

void SharedCallAppearanceTest::expectSCANewSubscription(int line, SharedCallAppearanceTestAccount& account, SipSharedCallAppearanceSetHandle scaSet, SipSharedCallAppearanceHandle sca)
{
	SipSharedCallAppearanceSetHandle h;
	SharedCallAppearanceNewSubscriptionEvent evt;
   ASSERT_TRUE(account.scaEvents->expectEvent(line,
		"SipSharedCallAppearanceHandler::onSharedCallAppearanceNewSubscription",
		15000,
		AlwaysTruePred(), h, evt));
   ASSERT_EQ(scaSet, h);
   ASSERT_EQ(account.handle, evt.account);
	ASSERT_EQ(sca, evt.sca);
}

void SharedCallAppearanceTest::expectSCAState(int line, SharedCallAppearanceTestAccount& account, SipSharedCallAppearanceSetHandle scaSet, SipSharedCallAppearanceHandle sca, bool subscriptionStarted, SipSubscriptionState subscriptionState)
{
   // Validate the state of the SCA
   SipSharedCallAppearanceState scaState;
   ASSERT_EQ(account.scaStateManager->getState(scaSet, sca, scaState), kSuccess);
   ASSERT_EQ(sca, scaState.sca);
   ASSERT_EQ(subscriptionStarted, scaState.subscriptionStarted);
   ASSERT_EQ(subscriptionState, scaState.subscriptionState);
}

void SharedCallAppearanceTest::expectSCACallHeldState(SharedCallAppearanceTestAccount& account, SipSharedCallAppearanceSetHandle scaSet, SipSharedCallAppearanceHandle sca, int appearance, bool held)
{
   SipSharedCallAppearanceCallInfo callInfo;
   assertSuccess(account.scaStateManager->getCall(scaSet, sca, appearance, callInfo));
   ASSERT_EQ(callInfo.dialog.appearance, appearance);
   ASSERT_EQ(callInfo.isHeld, held);
}

void SharedCallAppearanceTest::expectSCACallScapHeldState(SharedCallAppearanceTestAccount& account, SipSharedCallAppearanceSetHandle scaSet, SipSharedCallAppearanceHandle sca, int appearance, bool scapHeld)
{
   SipSharedCallAppearanceCallInfo callInfo;
   assertSuccess(account.scaStateManager->getCall(scaSet, sca, appearance, callInfo));
   ASSERT_EQ(callInfo.dialog.appearance, appearance);
   ASSERT_EQ(callInfo.isScapHeld, scapHeld);
}

void SharedCallAppearanceTest::expectSCACallExclusiveState(SharedCallAppearanceTestAccount& account, SipSharedCallAppearanceSetHandle scaSet, SipSharedCallAppearanceHandle sca, int appearance, bool exclusive)
{
   SipSharedCallAppearanceCallInfo callInfo;
   assertSuccess(account.scaStateManager->getCall(scaSet, sca, appearance, callInfo));
   ASSERT_EQ(callInfo.dialog.appearance, appearance);
   ASSERT_EQ(callInfo.dialog.exclusive, exclusive);
}

void SharedCallAppearanceTest::expectSCASubscriptionStateChanged(int line, SharedCallAppearanceTestAccount& account, SipSharedCallAppearanceSetHandle scaSet, SipSharedCallAppearanceHandle sca, SipSubscriptionState subscriptionState)
{
	SipSharedCallAppearanceSetHandle h;
   SharedCallAppearanceSubscriptionStateChangedEvent evt;
   ASSERT_TRUE(account.scaEvents->expectEvent(line,
		"SipSharedCallAppearanceHandler::onSharedCallAppearanceSubscriptionStateChanged",
		15000,
		AlwaysTruePred(), h, evt));
   ASSERT_EQ(scaSet, h);
   ASSERT_EQ(account.handle, evt.account);
	ASSERT_EQ(sca, evt.sca);
   ASSERT_EQ(subscriptionState, evt.subscriptionState);
}

void SharedCallAppearanceTest::expectSCAStateChanged(int line, SharedCallAppearanceTestAccount& account, SipSharedCallAppearanceSetHandle scaSet, SipSharedCallAppearanceHandle sca, DialogDirection direction, DialogState dialogState, const cpc::string& localAddress, const cpc::string& remoteAddress, int appearance)
{
   SipSharedCallAppearanceSetHandle h;
   SharedCallAppearanceStateChangedEvent evt;
   ASSERT_TRUE(account.scaEvents->expectEvent(line,
      "SipSharedCallAppearanceHandler::onSharedCallAppearanceStateChanged",
		5000, AlwaysTruePred(), h, evt));
   ASSERT_EQ(scaSet, h);
   ASSERT_EQ(sca, evt.sca);
 
   if (direction != DialogDirection_NotSpecified)
   {
      DialogInfo dialog;
      for (cpc::vector<SipSharedCallAppearanceCallInfo>::const_iterator it = evt.calls.begin(); it != evt.calls.end(); it++)
      {
         if (it->dialog.appearance == appearance)
         {
            dialog = it->dialog;
            break;
         }
      }
      ASSERT_EQ(appearance, dialog.appearance);
      ASSERT_EQ(direction, dialog.direction);
      ASSERT_EQ(dialogState, dialog.stateInfo.state);
      ASSERT_EQ(0, dialog.stateInfo.code);
      if (dialogState == DialogState_Trying)
      {
         ASSERT_FALSE(dialog.dialogId.callId.empty());
         ASSERT_FALSE(dialog.dialogId.localTag.empty());
      }
      else if (dialogState == DialogState_Early || dialogState == DialogState_Confirmed || dialogState == DialogState_Terminated)
      {
         ASSERT_FALSE(dialog.dialogId.callId.empty());
         ASSERT_FALSE(dialog.dialogId.localTag.empty());
         ASSERT_FALSE(dialog.dialogId.remoteTag.empty());
      }
      ASSERT_EQ(localAddress, dialog.localParticipant.identity.address);
      if (!cpc::string(remoteAddress).empty())
      {
         ASSERT_EQ(remoteAddress, dialog.remoteParticipant.identity.address);
      }
   }
}

void SharedCallAppearanceTest::waitForSCASubscriptionEnded_(int line, SharedCallAppearanceTestAccount& account, SipSharedCallAppearanceSetHandle scaSet, SipSharedCallAppearanceHandle sca)
{
	SipSharedCallAppearanceSetHandle h;
	SharedCallAppearanceSubscriptionEndedEvent evt;
   ASSERT_TRUE(account.scaEvents->waitForEvent(line,
		"SipSharedCallAppearanceHandler::onSharedCallAppearanceSubscriptionEnded",
		15000,
		AlwaysTruePred(), h, evt));
   ASSERT_EQ(scaSet, h);
	ASSERT_EQ(SipSubscriptionEndReason_ServerEnded, evt.endReason);
}

void SharedCallAppearanceTest::expectIncomingTransferRequest(int line, TestAccount& account, SipConversationHandle conversation, SipConversationHandle& targetConversation)
{
	SipConversationHandle h;
	TransferRequestEvent evt;
   ASSERT_TRUE(account.conversationEvents->expectEvent(line,
	   "SipConversationHandler::onIncomingTransferRequest",
	   15000,
		HandleEqualsPred<SipConversationHandle>(conversation), h, evt));
   targetConversation = evt.transferTargetConversation;
}

void SharedCallAppearanceTest::waitForConversationStateChanged_(int line, TestAccount& account, SipConversationHandle conversation, ConversationState conversationState)
{
   while(true)
   {
      SipConversationHandle h;
      ConversationStateChangedEvent evt;
      ASSERT_TRUE(account.conversationEvents->waitForEvent(line,
            "SipConversationHandler::onConversationStateChanged",
            5000, HandleEqualsPred<SipConversationHandle>(conversation), h, evt));
      ASSERT_EQ(h, conversation);
      if (evt.conversationState == conversationState)
      {
         break;
      }
   }
}

void SharedCallAppearanceTest::waitForConversationEnded_(int line, TestAccount& account, SipConversationHandle conversation, ConversationEndReason endReason)
{
   SipConversationHandle h;
   ConversationEndedEvent evt;
   ASSERT_TRUE(account.conversationEvents->waitForEvent(line,
         "SipConversationHandler::onConversationEnded",
         5000, HandleEqualsPred<SipConversationHandle>(conversation), h, evt));
   ASSERT_EQ(h, conversation);
   ASSERT_EQ(endReason, evt.endReason);
}

void SharedCallAppearanceTest::startSubscription(SharedCallAppearanceTestAccount& account, SipSharedCallAppearanceSetHandle& scaSet, SipSharedCallAppearanceHandle sca, SipSharedCallAppearanceSetSettings scaSetSettings, SipSharedCallAppearanceSettings scaSettings)
{
   // Alice subscribes to the SCA
   scaSet = account.scaManager->createSharedCallAppearanceSet(account.handle, scaSetSettings);
   ASSERT_TRUE(scaSet > 0);
   assertSuccess(account.scaManager->addSharedCallAppearance(scaSet, sca, scaSettings));

   // Start the subscription
   assertSuccess(account.scaManager->start(scaSet));

   // Wait for the new subscription notification
   assertSCANewSubscription(account, scaSet, sca);

   // Validate the state of the SCA
   assertSCAState(account, scaSet, sca, true, SipSubscriptionState_Pending);

   // Wait for the subscription state change notification
   assertSCASubscriptionStateChanged(account, scaSet, sca, SipSubscriptionState_Active);

   // Validate the state of the SCA
   assertSCAState(account, scaSet, sca, true, SipSubscriptionState_Active);

   // Wait for the initial SCA state change notification
   assertSCAStateChanged(account, scaSet, sca);

   // Validate the state of the SCA
   assertSCAState(account, scaSet, sca, true, SipSubscriptionState_Active);
}

void SharedCallAppearanceTest::terminateSubscription(SharedCallAppearanceTestAccount& account, SipSharedCallAppearanceSetHandle scaSet, SipSharedCallAppearanceHandle sca)
{
   // Terminate subscription
   assertSuccess(account.scaManager->end(scaSet));

   // Wait for the subscription termination notification
   waitForSCASubscriptionEnded(account, scaSet, sca);
}

void SharedCallAppearanceTest::expectSCAMakeExclusiveSuccess(int line, SharedCallAppearanceTestAccount& account, SipSharedCallAppearanceSetHandle scaSet, SipSharedCallAppearanceHandle sca)
{
   SipSharedCallAppearanceSetHandle h;
   SharedCallAppearanceMakeExclusiveSuccessEvent evt;
   ASSERT_TRUE(account.scaEvents->expectEvent(__LINE__,
      "SipSharedCallAppearanceHandler::onSharedCallAppearanceMakeExclusiveSuccess", 
		15000,
		HandleEqualsPred<SipSharedCallAppearanceSetHandle>(scaSet), h, evt));
   ASSERT_EQ(scaSet, h);
   ASSERT_EQ(sca, evt.sca);
}

}

#endif // CPCAPI2_BRAND_SIP_SHARED_CALL_APPEARANCE_MODULE
