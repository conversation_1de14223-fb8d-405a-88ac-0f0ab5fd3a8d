#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#include "cpcapi2_test_fixture.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>

#include "test_framework/cpcapi2_test_framework.h"
#include "test_framework/bare_bones_sip_endpoint.h"
#include "impl/cpm/CpimMessage.h"
#include "impl/cpm/CpmHelper.h"

#include <thread>
#include <future>

using namespace CPCAPI2;
using namespace CPCAPI2::CPM;
using namespace CPCAPI2::test;

namespace {

class CPMTest : public CpcapiAutoTest
{
public:
   CPMTest() {}
   virtual ~CPMTest() {}
};

TEST_F(CPMTest, CpimMessageParseAndGenerate)
{
   cpc::string cpimContent;
   cpimContent.append("From: MR SANDERS <im:<EMAIL>>\r\n");
   cpimContent.append("To: Depressed Donkey <im:<EMAIL>>\r\n");
   cpimContent.append("DateTime: 2000-12-13T13:40:00-08:00\r\n");
   cpimContent.append("Subject: the weather will be fine today\r\n");
   cpimContent.append("\r\n");
   cpimContent.append("Content-Type: text/plain\r\n");
   cpimContent.append("\r\n");
   cpimContent.append("This is my encapsulated text message content");
   resip::Data cpimContentBytes = cpimContent.c_str();

   // Parse the content and build the CPIM message
   CpimMessage cpimMessage = CpimMessage::parse(cpimContentBytes);

   // Verify that the message was parsed properly
   ASSERT_EQ(cpimMessage.getHeaders().size(), 4);
   ASSERT_EQ(cpimMessage.getHeader("From").getValue(), "MR SANDERS <im:<EMAIL>>");
   ASSERT_EQ(cpimMessage.getHeader("Subject").getValue(), "the weather will be fine today");
   ASSERT_EQ(cpimMessage.getContentHeaders().size(), 1);
   ASSERT_EQ(cpimMessage.getContentHeader("Content-Type").getValue(), "text/plain");
   ASSERT_EQ(cpimMessage.getContent(), resip::Data("This is my encapsulated text message content"));

   // Generate the content of the CPIM message, should be the same as the original
   resip::Data genCpimContentBytes = cpimMessage.toBytes();
   cpc::string genCpimContent = genCpimContentBytes.c_str();
   ASSERT_EQ(genCpimContent, cpimContent);
}

TEST_F(CPMTest, CpimMessageDateHeaderTest)
{
   long origTimezone = getTimezone();
   
   cpc::string message;
   cpc::string fromUrlStr;
   cpc::string toUrlStr;
   tm datetime;
   cpc::string datetimeString;
   resip::Mime contentType;
   resip::Data messageContent;
   cpc::vector<DispositionNotificationType> dispositionNotifications;

   {
      cpc::string cpimContent;
      cpimContent.append("DateTime: 1999-12-31T13:40:00-08:00\r\n"); // Remote: PST
      cpimContent.append("\r\n");
      cpimContent.append("Content-Type: text/plain\r\n");
      cpimContent.append("\r\n");
      cpimContent.append("This is my encapsulated text message content");
      resip::Data cpimContentBytes = cpimContent.c_str();
      CpimMessage cpimMessage = CpimMessage::parse(cpimContentBytes);

	  setTimezone(18000); // Local: EST (GMT - 5)
      CpmHelper::extractCpimMessage(cpimMessage, message, fromUrlStr, toUrlStr, datetimeString, datetime, contentType, messageContent, dispositionNotifications);
      ASSERT_EQ(datetime.tm_year, 99);
      ASSERT_EQ(datetime.tm_mon, 11);
      ASSERT_EQ(datetime.tm_mday, 31);
      ASSERT_EQ(datetime.tm_hour, 16); // 3 hour difference
      ASSERT_EQ(datetime.tm_min, 40);
      ASSERT_EQ(datetime.tm_sec, 00);

	  setTimezone(0); // Local: GMT
      CpmHelper::extractCpimMessage(cpimMessage, message, fromUrlStr, toUrlStr, datetimeString, datetime, contentType, messageContent, dispositionNotifications);
      ASSERT_EQ(datetime.tm_year, 99);
      ASSERT_EQ(datetime.tm_mon, 11);
      ASSERT_EQ(datetime.tm_mday, 31);
      ASSERT_EQ(datetime.tm_hour, 21); // 8 hour difference
      ASSERT_EQ(datetime.tm_min, 40);
      ASSERT_EQ(datetime.tm_sec, 00);

	  setTimezone(-18000); // Local: GMT + 5
      CpmHelper::extractCpimMessage(cpimMessage, message, fromUrlStr, toUrlStr, datetimeString, datetime, contentType, messageContent, dispositionNotifications);
      ASSERT_EQ(datetime.tm_year, 100);
      ASSERT_EQ(datetime.tm_mon, 0);
      ASSERT_EQ(datetime.tm_mday, 1);
      ASSERT_EQ(datetime.tm_hour, 2); // 13 hour difference
      ASSERT_EQ(datetime.tm_min, 40);
      ASSERT_EQ(datetime.tm_sec, 00);
   }

   {
      cpc::string cpimContent;
      cpimContent.append("DateTime: 2000-01-01T01:40:00Z\r\n"); // Remote: GMT
      cpimContent.append("\r\n");
      cpimContent.append("Content-Type: text/plain\r\n");
      cpimContent.append("\r\n");
      cpimContent.append("This is my encapsulated text message content");
      resip::Data cpimContentBytes = cpimContent.c_str();
      CpimMessage cpimMessage = CpimMessage::parse(cpimContentBytes);

	  setTimezone(18000); // Local: EST (GMT - 5)
      CpmHelper::extractCpimMessage(cpimMessage, message, fromUrlStr, toUrlStr, datetimeString, datetime, contentType, messageContent, dispositionNotifications);
      ASSERT_EQ(datetime.tm_year, 99);
      ASSERT_EQ(datetime.tm_mon, 11);
      ASSERT_EQ(datetime.tm_mday, 31);
      ASSERT_TRUE(datetime.tm_hour == 20 || datetime.tm_hour == 21); // 5 hour difference
      ASSERT_EQ(datetime.tm_min, 40);
      ASSERT_EQ(datetime.tm_sec, 00);

	  setTimezone(0); // Local: GMT
      CpmHelper::extractCpimMessage(cpimMessage, message, fromUrlStr, toUrlStr, datetimeString, datetime, contentType, messageContent, dispositionNotifications);
      ASSERT_EQ(datetime.tm_year, 100);
      ASSERT_EQ(datetime.tm_mon, 0);
      ASSERT_EQ(datetime.tm_mday, 1);
      ASSERT_EQ(datetime.tm_hour, 1); // 0 hour difference
      ASSERT_EQ(datetime.tm_min, 40);
      ASSERT_EQ(datetime.tm_sec, 00);

	  setTimezone(-18000); // Local: GMT + 5
      CpmHelper::extractCpimMessage(cpimMessage, message, fromUrlStr, toUrlStr, datetimeString, datetime, contentType, messageContent, dispositionNotifications);
      ASSERT_EQ(datetime.tm_year, 100);
      ASSERT_EQ(datetime.tm_mon, 0);
      ASSERT_EQ(datetime.tm_mday, 1);
      ASSERT_EQ(datetime.tm_min, 40);
      ASSERT_EQ(datetime.tm_sec, 00);
   }

   {
      cpc::string cpimContent;
      cpimContent.append("DateTime: 2000-12-13T13:40:00+07:30\r\n"); // Remote: GMT + 7.5
      cpimContent.append("\r\n");
      cpimContent.append("Content-Type: text/plain\r\n");
      cpimContent.append("\r\n");
      cpimContent.append("This is my encapsulated text message content");
      resip::Data cpimContentBytes = cpimContent.c_str();
      CpimMessage cpimMessage = CpimMessage::parse(cpimContentBytes);

	  setTimezone(18000); // Local: EST (GMT - 5)
      CpmHelper::extractCpimMessage(cpimMessage, message, fromUrlStr, toUrlStr, datetimeString, datetime, contentType, messageContent, dispositionNotifications);
      ASSERT_EQ(datetime.tm_year, 100);
      ASSERT_EQ(datetime.tm_mon, 11);
      ASSERT_EQ(datetime.tm_mday, 13);
      ASSERT_EQ(datetime.tm_hour, 1); // 12.5 hour difference
      ASSERT_EQ(datetime.tm_min, 10);
      ASSERT_EQ(datetime.tm_sec, 00);

	  setTimezone(0); // Local: GMT
      CpmHelper::extractCpimMessage(cpimMessage, message, fromUrlStr, toUrlStr, datetimeString, datetime, contentType, messageContent, dispositionNotifications);
      ASSERT_EQ(datetime.tm_year, 100);
      ASSERT_EQ(datetime.tm_mon, 11);
      ASSERT_EQ(datetime.tm_mday, 13);
      ASSERT_EQ(datetime.tm_hour, 6); // 7.5 hour difference
      ASSERT_EQ(datetime.tm_min, 10);
      ASSERT_EQ(datetime.tm_sec, 00);

	  setTimezone(-18000); // Local: GMT + 5
      CpmHelper::extractCpimMessage(cpimMessage, message, fromUrlStr, toUrlStr, datetimeString, datetime, contentType, messageContent, dispositionNotifications);
      ASSERT_EQ(datetime.tm_year, 100);
      ASSERT_EQ(datetime.tm_mon, 11);
      ASSERT_EQ(datetime.tm_mday, 13);
      ASSERT_EQ(datetime.tm_hour, 11); // 2.5 hour difference
      ASSERT_EQ(datetime.tm_min, 10);
      ASSERT_EQ(datetime.tm_sec, 00);
   }

   setTimezone(origTimezone);
}

}
