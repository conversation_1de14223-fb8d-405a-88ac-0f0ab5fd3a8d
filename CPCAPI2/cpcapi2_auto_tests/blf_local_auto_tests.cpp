#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#if (CPCAPI2_BRAND_SIP_DIALOG_EVENT_MODULE == 1)

#include "cpcapi2_test_fixture.h"
#include "test_account_events.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>

#include "impl/dialogevent/DialogInfoDocumentHelper.h"
#include "impl/dialogevent/SipDialogEventSubscriptionManagerInterface.h"
#include "test_framework/cpcapi2_test_framework.h"
#include "test_framework/test_runtime_environment.h"

#include "repro/Proxy.hxx"
#include "resip/stack/Helper.hxx"
#include <thread>
#include <regex>

#include "util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::EXTERNAL

using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::SipDialogEvent;
using namespace CPCAPI2::SipEvent;
using namespace CPCAPI2::SipConversation;
using namespace CPCAPI2::SipBusyLampField;

namespace {
class BlfServerTestAccount : public TestAccount
{
public:
   BlfServerTestAccount(const std::string& name, TestAccountInitMode initMode = Account_Enable) : 
      dialogEventSubscriptionManager(NULL),
      TestAccount(name, Account_NoInit)
   {
      // if the base class called init() above it would not have had access to our derived version
      if (initMode != Account_NoInit)
         init();
      if (initMode == Account_Enable)
         enable();
   }

   virtual void init(CPCAPI2::PhoneHandler* phoneHandler = NULL, CPCAPI2::NetworkTransport transport = CPCAPI2::TransportWiFi) OVERRIDE
   {
      // initialize the base class first
      TestAccount::init(phoneHandler, transport);

      if (dialogEventSubscriptionManager == NULL)
      {
         dialogEventSubscriptionManager = SipDialogEventSubscriptionManager::getInterface(phone);
         dialogEventSubscriptionEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(dialogEventSubscriptionManager));
         dialogEventSubscriptionManager->setHandler(handle, (SipDialogEventSubscriptionHandler*)0xDEADBEEF);
      }
   }

   ~BlfServerTestAccount()
   {
      dialogEventSubscriptionManager->setHandler(handle, NULL);

      dialogEventSubscriptionEvents->shutdown();
      delete dialogEventSubscriptionEvents;
      dialogEventSubscriptionEvents = NULL;
   }

   SipDialogEventSubscriptionManager* dialogEventSubscriptionManager;
   CPCAPI2::test::EventHandler* dialogEventSubscriptionEvents;
};

class BlfAttendantTestAccount : public TestAccount
{
public:
   BlfAttendantTestAccount(const std::string& name, TestAccountInitMode initMode = Account_Enable, bool disableOnDestruct = true) : 
      busyLampFieldManager(NULL),
      TestAccount(name, Account_NoInit, disableOnDestruct)
   {
      // if the base class called init() above it would not have had access to our derived version
      if (initMode != Account_NoInit)
         init();
      if (initMode == Account_Enable)
         enable();
   }

   virtual void init(CPCAPI2::PhoneHandler* phoneHandler = NULL, CPCAPI2::NetworkTransport transport = CPCAPI2::TransportWiFi) OVERRIDE
   {
      // initialize the base class first
      TestAccount::init(phoneHandler, transport);

      if (busyLampFieldManager == NULL)
      {
         busyLampFieldManager = SipBusyLampFieldManager::getInterface(phone);
         busyLampFieldEvents = new test::EventHandler(config.name.c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(busyLampFieldManager));
         busyLampFieldManager->setHandler(handle, (SipBusyLampFieldHandler*)0xDEADBEEF);

         busyLampFieldStateManager = SipBusyLampFieldStateManager::getInterface(busyLampFieldManager);
      }
   }

   ~BlfAttendantTestAccount()
   {
      busyLampFieldManager->setHandler(handle, NULL);

      busyLampFieldEvents->shutdown();
      delete busyLampFieldEvents;
      busyLampFieldEvents = NULL;
   }

   SipBusyLampFieldStateManager* busyLampFieldStateManager;
   SipBusyLampFieldManager* busyLampFieldManager;
   CPCAPI2::test::EventHandler* busyLampFieldEvents;
};

class BlfLocalTest : public CpcapiAutoTest
{
public:
   const cpc::string LineAddress1 = "sip:<EMAIL>";
   const cpc::string LineAddress2 = "sip:<EMAIL>";
   const cpc::string LineAddress3 = "sip:<EMAIL>";
   const cpc::string LineAddress4 = "sip:<EMAIL>";
   const cpc::string LineTelephone1 = "tel:+***********;ext=201";
   const cpc::string LineTelephone2 = "tel:+***********;ext=202";
   const cpc::string LineTelephone3 = "tel:+***********;ext=203";
   const cpc::string LineTelephone4 = "tel:+***********;ext=204";
   const cpc::string LineExt1 = "201";
   const cpc::string LineExt2 = "202";
   const cpc::string LineExt3 = "203";
   const cpc::string LineExt4 = "204";
   const cpc::string LineName1 = "One Alianza";
   const cpc::string LineName2 = "Two Alianza";
   const cpc::string LineName2Changed = "Two Alianza Changed";
   const cpc::string LineName3 = "Three Alianza";
   const cpc::string LineName4 = "Four Alianza";

   BlfLocalTest() {}
   virtual ~BlfLocalTest() {}

   static void assertPhoneError(int line, TestAccount& account, const cpc::string& errorText);
   static void assertBLFError(int line, BlfAttendantTestAccount& account, SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const cpc::string& errorText);
   static void assertBLFRemoteLineNewSubscription(int line, BlfAttendantTestAccount& account, SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const SipBusyLampFieldRemoteLineHandle& remoteLine, const cpc::string& remoteLineName);
   static void assertBLFRemoteLineState(int line, BlfAttendantTestAccount& account, SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const SipBusyLampFieldRemoteLineHandle& remoteLine, bool subscriptionStarted, SipSubscriptionState subscriptionState, DialogState dialogState = DialogState_NotSpecified);
   static void assertBLFRemoteLineStateChanged(int line, BlfAttendantTestAccount& account, SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const SipBusyLampFieldRemoteLineHandle& remoteLine, DialogDirection direction, DialogState dialogState, const cpc::string& localSipAddress, const cpc::string& localTelAddress, const cpc::string& localTelExt, const cpc::string& remoteAddress, const cpc::string& remoteName, cpc::string& dialogId);
   static void assertBLFRemoteLineSubscriptionStateChanged(int line, BlfAttendantTestAccount& account, SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const SipBusyLampFieldRemoteLineHandle& remoteLine, SipSubscriptionState subscriptionState);
   static void assertBLFRemoteLineSubscriptionEnded(int line, BlfAttendantTestAccount& account, SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const SipBusyLampFieldRemoteLineHandle& remoteLine);
   static void assertBLFRemoteLineEmptySubscriptions(int line, BlfAttendantTestAccount& account, SipBusyLampFieldRemoteLineSetHandle remoteLineSet);
   static void waitForConversationStateChanged_(int line, TestAccount& account, SipConversationHandle conversation, ConversationState conversationState);
};

// assumes content is "\r\n" line delimited
cpc::string ApplyReplacementsToMime(const cpc::string& content, const std::map<cpc::string, cpc::string>& replacements)
{
   std::string input = content.c_str();

   // replace the placeholders
   for (auto const& x : replacements)
   {
      input = std::regex_replace(input, std::regex(std::string("%") + std::string(x.first.c_str()) + std::string("%")), std::string(x.second.c_str()));
   }

   // update the content-length for each MIME part
   const std::string boundary("\r\n\r\n--UniqueBroadWorksBoundary");
   const std::string dataDelimiter("\r\n\r\n");
   const std::string contentLengthPlaceholder("%content_length%");   // we use % just because it's not a regex special character
   size_t pos = 0;
   size_t lastPos = 0;
   std::string result;
   while ((pos = input.find(boundary, lastPos+1)) != std::string::npos) 
   {
      std::string part = input.substr(lastPos, pos - lastPos);

      // find the start of the data of this part
      size_t dataStart = part.find(dataDelimiter);
      assert(dataStart != std::string::npos);
      dataStart += dataDelimiter.length();

      // set the length of this part in the Content-Length header
      size_t len = part.length() - dataStart;
      part = std::regex_replace(part, std::regex(contentLengthPlaceholder), std::to_string(len));

      result += part;
      lastPos = pos;
   }

   // end the MIME with the last piece which should be "--\r\n" and there could be additional garbage to be ignored
   std::string part = input.substr(lastPos, input.length() - lastPos);
   result += part;

   return result.c_str();
}

cpc::string ReadMimeFile(const cpc::string& path, const std::map<cpc::string, cpc::string>& replacements = std::map<cpc::string, cpc::string>())
{
   std::ifstream in((TestEnvironmentConfig::testResourcePath() + path).c_str());
   assert(in.is_open());
   std::ostringstream iss;
   iss << in.rdbuf() << std::flush;

   // We need \r\n termination for the tests to succeed so we do a conversion. I think it would be good to remove this 
   // limitation as I believe both line terminations are valid for MIME and XML.
   std::string temp = std::regex_replace(iss.str(), std::regex("\r\n"), "\n");   // first turn everything into \n style
   temp = std::regex_replace(temp, std::regex("\n"), "\r\n");                    // now make it all \r\n
   return ApplyReplacementsToMime(temp.c_str(), replacements);
}

SipEventState EventStateFromFile(cpc::string uri, const cpc::string& path, const std::map<cpc::string, cpc::string>& replacements = std::map<cpc::string, cpc::string>())
{
   cpc::string body = ReadMimeFile(path, replacements);

   DialogInfoDocument dialogInfoDoc = DialogInfoDocumentHelper::createEmptyDocument(0, uri);
   SipEventState eventState;
   eventState.eventPackage = DialogInfoDocumentHelper::EVENT_PACKAGE_NAME;
   eventState.expiresTimeMs = 3600;
   eventState.mimeType = "multipart";
   eventState.mimeSubType = "related;type=\"application/rlmi+xml\";boundary=UniqueBroadWorksBoundary";
   eventState.contentLength = body.size();
   eventState.contentUTF8 = body;

   return eventState;
}

repro::ReproRunner* runRepro(cpc::string config)
{
   repro::ReproRunner* res = new repro::ReproRunner();
   const char* const reproArgs[] = { "" };
   resip::Data configFile = (TestEnvironmentConfig::testResourcePath() + config).c_str();
   res->run(1, reproArgs, configFile);
   return res;
}

void configureAccountForNetworkSwitching(TestAccount& account)
{
   cpc::string name = account.config.settings.username.c_str();
   cpc::string baseName = name.substr(0, name.find("_"));
   account.config.settings.username = baseName + "123";
   account.config.settings.password = baseName + "123";
   account.config.settings.domain = "cp.local";
   account.config.settings.useOptionsPing = false;
   account.config.settings.optionsPingInterval = 5000;
   account.config.settings.autoRetryOnTransportDisconnect = true;
   account.config.settings.useRport = false;
   account.config.settings.useOutbound = false;
   account.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   account.config.settings.registrationIntervalSeconds = 30;
   account.config.settings.minimumRegistrationIntervalSeconds = 5;
   account.config.settings.maximumRegistrationIntervalSeconds = 5;
   account.config.settings.enableDNSResetOnRegistrationRefresh = true;
   account.config.settings.enableAuthResetUponDNSReset = true;
   account.config.settings.outboundProxy = "pritcpv4sectcpv4.local"; // primary is TCP Port 7080 and secondary is TCP Port 7090
   account.config.settings.sipTransportType = SipAccountTransportType::SipAccountTransport_TCP;
   account.config.settings.ipVersion = IpVersion::IpVersion_V4;
}

TEST_F(BlfLocalTest, TestRLSSubscribe)
{
   BlfAttendantTestAccount alice("alice");
   BlfServerTestAccount bob("bob");

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipDialogEventSubscriptionHandle bobSubs = 0;
      {
         // Wait for a new subscription
         SipDialogEventSubscriptionHandle h;
         NewDialogEventSubscriptionEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onNewSubscription",
            5000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, bob.handle);
         bobSubs = h;
         ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
      }

      // Accept the subscription
      ASSERT_EQ(bob.dialogEventSubscriptionManager->accept(bobSubs), kSuccess);

      {
         // Wait for the subscription state to transition to Active
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionStateChanged",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      {
         SipEventState eventState = EventStateFromFile(bob.config.uri(), "BlfLocalTest_TestRLSSubscribe.txt");
         ASSERT_EQ(bob.subs->notify(bobSubs, eventState), kSuccess);
      }

      {
         // Wait for the subscription state to transition to Terminated
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionEnded",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }
   });

   auto aliceEvents = std::async(std::launch::async, [&] () {
      cpc::string dialogId;
      SipBusyLampFieldRemoteLineHandle aliceRemoteLine = bob.config.uri();

      // Attendant subscribes to the remote line
      SipBusyLampFieldRemoteLineSetSettings settings;
      settings.expires = 3600;
      settings.resourceListAddress = bob.config.uri();
      SipBusyLampFieldRemoteLineSetHandle remoteLineSet = alice.busyLampFieldManager->createBusyLampFieldRemoteLineSet(alice.handle, settings);
      ASSERT_TRUE(remoteLineSet > 0);

      // Start the subscription
      assertSuccess(alice.busyLampFieldManager->start(remoteLineSet));

      // ==== Line 1 ====
      // Wait for the new subscription notification
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress1, LineName1);
      // Validate the state of the remote line
      assertBLFRemoteLineState(__LINE__, alice, remoteLineSet, LineAddress1, true, SipSubscriptionState_Pending);
      // Wait for the subscription state change notification
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress1, SipSubscriptionState_Active);
      // Wait for remote line state change notification
      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress1, DialogDirection_NotSpecified, DialogState_Terminated, LineAddress1, LineTelephone1, LineExt1, "", "", dialogId);

      // ==== Line 2 ====
      // Wait for the new subscription notification
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress2, LineName2);
      // Validate the state of the remote line
      assertBLFRemoteLineState(__LINE__, alice, remoteLineSet, LineAddress2, true, SipSubscriptionState_Pending);
      // Wait for the subscription state change notification
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, SipSubscriptionState_Active);
      // Wait for remote line state change notification
      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, DialogDirection_Initiator, DialogState_Proceeding, LineAddress2, LineTelephone2, LineExt2, "", "", dialogId);

      // ==== Line 3 ====
      // Wait for the new subscription notification
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress3, LineName3);
      // Validate the state of the remote line
      assertBLFRemoteLineState(__LINE__, alice, remoteLineSet, LineAddress3, true, SipSubscriptionState_Pending);
      // Wait for the subscription state change notification
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, SipSubscriptionState_Active);
      // Wait for remote line state change notification
      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, DialogDirection_Recipient, DialogState_Confirmed, LineAddress3, LineTelephone3, LineExt3, "", "", dialogId);

      // Terminate the subscription
      assertSuccess(alice.busyLampFieldManager->end(remoteLineSet));

      // Wait for the subscription termination notifications
      assertBLFRemoteLineSubscriptionEnded(__LINE__, alice, remoteLineSet, LineAddress1);
      assertBLFRemoteLineSubscriptionEnded(__LINE__, alice, remoteLineSet, LineAddress2);
      assertBLFRemoteLineSubscriptionEnded(__LINE__, alice, remoteLineSet, LineAddress3);

      // Validate the state of the remote lines
      assertBLFRemoteLineState(__LINE__, alice, remoteLineSet, LineAddress1, false, SipSubscriptionState_Terminated);
      assertBLFRemoteLineState(__LINE__, alice, remoteLineSet, LineAddress2, false, SipSubscriptionState_Terminated);
      assertBLFRemoteLineState(__LINE__, alice, remoteLineSet, LineAddress3, false, SipSubscriptionState_Terminated);
   });

   waitFor2(aliceEvents, bobEvents);
}

TEST_F(BlfLocalTest, TestRLSSubscribeWithNameChange)
{
   BlfAttendantTestAccount alice("alice");
   BlfServerTestAccount bob("bob");

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipDialogEventSubscriptionHandle bobSubs = 0;
      {
         // Wait for a new subscription
         SipDialogEventSubscriptionHandle h;
         NewDialogEventSubscriptionEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onNewSubscription",
            5000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, bob.handle);
         bobSubs = h;
         ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
      }

      // Accept the subscription
      ASSERT_EQ(bob.dialogEventSubscriptionManager->accept(bobSubs), kSuccess);

      {
         // Wait for the subscription state to transition to Active
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionStateChanged",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      {
         SipEventState eventState = EventStateFromFile(bob.config.uri(), "BlfLocalTest_TestRLSSubscribe.txt");
         ASSERT_EQ(bob.subs->notify(bobSubs, eventState), kSuccess);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      {
         SipEventState eventState = EventStateFromFile(bob.config.uri(), "BlfLocalTest_TestRLSSubscribeWithChange.txt");
         ASSERT_EQ(bob.subs->notify(bobSubs, eventState), kSuccess);
      }

      {
         // Wait for the subscription state to transition to Terminated
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionEnded",
            15000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }
   });

   auto aliceEvents = std::async(std::launch::async, [&] () {
      cpc::string dialogId;
      SipBusyLampFieldRemoteLineHandle aliceRemoteLine = bob.config.uri();

      // Attendant subscribes to the remote line
      SipBusyLampFieldRemoteLineSetSettings settings;
      settings.expires = 3600;
      settings.resourceListAddress = bob.config.uri();
      SipBusyLampFieldRemoteLineSetHandle remoteLineSet = alice.busyLampFieldManager->createBusyLampFieldRemoteLineSet(alice.handle, settings);
      ASSERT_TRUE(remoteLineSet > 0);

      // Start the subscription
      assertSuccess(alice.busyLampFieldManager->start(remoteLineSet));

      // ==== Line 1 ====
      // Wait for the new subscription notification
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress1, LineName1);
      // Validate the state of the remote line
      assertBLFRemoteLineState(__LINE__, alice, remoteLineSet, LineAddress1, true, SipSubscriptionState_Pending);
      // Wait for the subscription state change notification
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress1, SipSubscriptionState_Active);
      // Wait for remote line state change notification
      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress1, DialogDirection_NotSpecified, DialogState_Terminated, LineAddress1, LineTelephone1, LineExt1, "", "", dialogId);

      // ==== Line 2 ====
      // Wait for the new subscription notification
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress2, LineName2);
      // Validate the state of the remote line
      assertBLFRemoteLineState(__LINE__, alice, remoteLineSet, LineAddress2, true, SipSubscriptionState_Pending);
      // Wait for the subscription state change notification
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, SipSubscriptionState_Active);
      // Wait for remote line state change notification
      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, DialogDirection_Initiator, DialogState_Proceeding, LineAddress2, LineTelephone2, LineExt2, "", "", dialogId);

      // ==== Line 3 ====
      // Wait for the new subscription notification
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress3, LineName3);
      // Validate the state of the remote line
      assertBLFRemoteLineState(__LINE__, alice, remoteLineSet, LineAddress3, true, SipSubscriptionState_Pending);
      // Wait for the subscription state change notification
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, SipSubscriptionState_Active);
      // Wait for remote line state change notification
      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, DialogDirection_Recipient, DialogState_Confirmed, LineAddress3, LineTelephone3, LineExt3, "", "", dialogId);

      // at this point the name of line 2 changes so we'll get updated events for all the lines (should this be fixed?)

      // ==== Line 1 ====
      // Wait for the subscription state change notification
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress1, SipSubscriptionState_Active);
      // Wait for remote line state change notification
      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress1, DialogDirection_NotSpecified, DialogState_Terminated, LineAddress1, LineTelephone1, LineExt1, "", "", dialogId);

      // ==== Line 2 changed ====
      // Wait for the subscription ended/restarted notification
      assertBLFRemoteLineSubscriptionEnded(__LINE__, alice, remoteLineSet, LineAddress2);
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress2, LineName2Changed);
      // Wait for the subscription state change notification
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, SipSubscriptionState_Active);
      // Wait for remote line state change notification
      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, DialogDirection_Initiator, DialogState_Proceeding, LineAddress2, LineTelephone2, LineExt2, "", "", dialogId);

      // ==== Line 3 ====
      // Wait for the subscription state change notification
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, SipSubscriptionState_Active);
      // Wait for remote line state change notification
      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, DialogDirection_Recipient, DialogState_Confirmed, LineAddress3, LineTelephone3, LineExt3, "", "", dialogId);

      // Terminate the subscription
      assertSuccess(alice.busyLampFieldManager->end(remoteLineSet));

      // Wait for the subscription termination notifications
      assertBLFRemoteLineSubscriptionEnded(__LINE__, alice, remoteLineSet, LineAddress1);
      assertBLFRemoteLineSubscriptionEnded(__LINE__, alice, remoteLineSet, LineAddress2);
      assertBLFRemoteLineSubscriptionEnded(__LINE__, alice, remoteLineSet, LineAddress3);

      // Validate the state of the remote lines
      assertBLFRemoteLineState(__LINE__, alice, remoteLineSet, LineAddress1, false, SipSubscriptionState_Terminated);
      assertBLFRemoteLineState(__LINE__, alice, remoteLineSet, LineAddress2, false, SipSubscriptionState_Terminated);
      assertBLFRemoteLineState(__LINE__, alice, remoteLineSet, LineAddress3, false, SipSubscriptionState_Terminated);
   });

   waitFor2(aliceEvents, bobEvents);
}

TEST_F(BlfLocalTest, TestRLSOutgoingCall)
{
   BlfAttendantTestAccount alice("alice");
   BlfServerTestAccount bob("bob");

   const cpc::string RemoteAddress = "sip:+<EMAIL>;user=phone";
   const cpc::string RemoteName = "Randy Smith";

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipDialogEventSubscriptionHandle bobSubs = 0;
      {
         // Wait for a new subscription
         SipDialogEventSubscriptionHandle h;
         NewDialogEventSubscriptionEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onNewSubscription",
            5000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, bob.handle);
         bobSubs = h;
         ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
      }

      // Accept the subscription
      ASSERT_EQ(bob.dialogEventSubscriptionManager->accept(bobSubs), kSuccess);

      {
         // Wait for the subscription state to transition to Active
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionStateChanged",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      {
         SipEventState eventState = EventStateFromFile(bob.config.uri(), "BlfLocalTest_TestRLSSubscribe.txt");
         ASSERT_EQ(bob.subs->notify(bobSubs, eventState), kSuccess);
      }

      {
         std::map<cpc::string, cpc::string> replacements = 
         {
            {"line_uri", LineAddress1},
            {"line_name", LineName1}, 
            {"line_number", LineTelephone1}, 
            {"remote_uri", RemoteAddress}, 
            {"remote_name", RemoteName}, 
            {"call_direction", "direction=\"initiator\""}, 
            {"call_state", "proceeding"}, 
         };
         SipEventState eventState = EventStateFromFile(bob.config.uri(), "BlfLocalTest_TestRLSCallState.txt", replacements);
         ASSERT_EQ(bob.subs->notify(bobSubs, eventState), kSuccess);
      }

      {
         std::map<cpc::string, cpc::string> replacements = 
         {
            {"line_uri", LineAddress1},
            {"line_name", LineName1}, 
            {"line_number", LineTelephone1}, 
            {"remote_uri", RemoteAddress}, 
            {"remote_name", RemoteName}, 
            {"call_direction", "direction=\"initiator\""}, 
            {"call_state", "confirmed"}, 
         };
         SipEventState eventState = EventStateFromFile(bob.config.uri(), "BlfLocalTest_TestRLSCallState.txt", replacements);
         ASSERT_EQ(bob.subs->notify(bobSubs, eventState), kSuccess);
      }

      {
         std::map<cpc::string, cpc::string> replacements = 
         {
            {"line_uri", LineAddress1},
            {"line_name", LineName1}, 
            {"line_number", LineTelephone1}, 
            {"remote_uri", RemoteAddress}, 
            {"remote_name", RemoteName}, 
            {"call_direction", ""}, 
            {"call_state", "terminated"}, 
         };
         SipEventState eventState = EventStateFromFile(bob.config.uri(), "BlfLocalTest_TestRLSCallState.txt", replacements);
         ASSERT_EQ(bob.subs->notify(bobSubs, eventState), kSuccess);
      }

      {
         // Wait for the subscription state to transition to Terminated
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionEnded",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }
   });

   auto aliceEvents = std::async(std::launch::async, [&] () {
      cpc::string dialogId;
      SipBusyLampFieldRemoteLineHandle aliceRemoteLine = bob.config.uri();

      // Attendant subscribes to the remote line
      SipBusyLampFieldRemoteLineSetSettings settings;
      settings.expires = 3600;
      settings.resourceListAddress = bob.config.uri();
      SipBusyLampFieldRemoteLineSetHandle remoteLineSet = alice.busyLampFieldManager->createBusyLampFieldRemoteLineSet(alice.handle, settings);
      ASSERT_TRUE(remoteLineSet > 0);

      // Start the subscription
      assertSuccess(alice.busyLampFieldManager->start(remoteLineSet));

      // ==== Line 1 ====
      // Wait for the new subscription notification
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress1, LineName1);
      // Validate the state of the remote line
      assertBLFRemoteLineState(__LINE__, alice, remoteLineSet, LineAddress1, true, SipSubscriptionState_Pending);
      // Wait for the subscription state change notification
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress1, SipSubscriptionState_Active);
      // Wait for remote line state change notification
      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress1, DialogDirection_NotSpecified, DialogState_Terminated, LineAddress1, LineTelephone1, LineExt1, "", "", dialogId);

      // ==== Line 2 ====
      // Wait for the new subscription notification
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress2, LineName2);
      // Validate the state of the remote line
      assertBLFRemoteLineState(__LINE__, alice, remoteLineSet, LineAddress2, true, SipSubscriptionState_Pending);
      // Wait for the subscription state change notification
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, SipSubscriptionState_Active);
      // Wait for remote line state change notification
      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, DialogDirection_Initiator, DialogState_Proceeding, LineAddress2, LineTelephone2, LineExt2, "", "", dialogId);

      // ==== Line 3 ====
      // Wait for the new subscription notification
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress3, LineName3);
      // Validate the state of the remote line
      assertBLFRemoteLineState(__LINE__, alice, remoteLineSet, LineAddress3, true, SipSubscriptionState_Pending);
      // Wait for the subscription state change notification
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, SipSubscriptionState_Active);
      // Wait for remote line state change notification
      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, DialogDirection_Recipient, DialogState_Confirmed, LineAddress3, LineTelephone3, LineExt3, "", "", dialogId);

      // ==== Outgoing Call ====
      // Wait for remote line state change notifications for the outgoing call
      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress1, DialogDirection_Initiator, DialogState_Proceeding, LineAddress1, LineTelephone1, LineExt1, RemoteAddress, RemoteName, dialogId);
      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress1, DialogDirection_Initiator, DialogState_Confirmed, LineAddress1, LineTelephone1, LineExt1, RemoteAddress, RemoteName, dialogId);
      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress1, DialogDirection_NotSpecified, DialogState_Terminated, LineAddress1, LineTelephone1, LineExt1, "", "", dialogId);

      // Terminate the subscription
      assertSuccess(alice.busyLampFieldManager->end(remoteLineSet));

      // Wait for the subscription termination notifications
      assertBLFRemoteLineSubscriptionEnded(__LINE__, alice, remoteLineSet, LineAddress1);
      assertBLFRemoteLineSubscriptionEnded(__LINE__, alice, remoteLineSet, LineAddress2);
      assertBLFRemoteLineSubscriptionEnded(__LINE__, alice, remoteLineSet, LineAddress3);

      // Validate the state of the remote lines
      assertBLFRemoteLineState(__LINE__, alice, remoteLineSet, LineAddress1, false, SipSubscriptionState_Terminated);
      assertBLFRemoteLineState(__LINE__, alice, remoteLineSet, LineAddress2, false, SipSubscriptionState_Terminated);
      assertBLFRemoteLineState(__LINE__, alice, remoteLineSet, LineAddress3, false, SipSubscriptionState_Terminated);
   });

   waitFor2(aliceEvents, bobEvents);
}

// In this case we are terminating a subscription with a reason of "deactivated" and 
// expecting the client to resubscribe immediately.
TEST_F(BlfLocalTest, TestRLSTerminationAndImmediateResubscribe)
{
   BlfAttendantTestAccount alice("alice");
   BlfServerTestAccount bob("bob");

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipDialogEventSubscriptionHandle bobSubs = 0;
      {
         // Wait for a new subscription
         SipDialogEventSubscriptionHandle h;
         NewDialogEventSubscriptionEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onNewSubscription",
            5000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, bob.handle);
         bobSubs = h;
         ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
      }

      // Accept the subscription
      ASSERT_EQ(bob.dialogEventSubscriptionManager->accept(bobSubs), kSuccess);

      {
         // Wait for the subscription state to transition to Active
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionStateChanged",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      {
         SipEventState eventState = EventStateFromFile(bob.config.uri(), "BlfLocalTest_TestRLSSubscribe.txt");
         ASSERT_EQ(bob.subs->notify(bobSubs, eventState), kSuccess);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      ASSERT_EQ(bob.subs->end(bobSubs, SipSubscriptionTerminateReason_Deactivate), kSuccess);

      {
         // Wait for the subscription state to transition to Terminated
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionEnded",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }

      {
         // NOTE the short timeout here as we don't want any delay due to the lacking retry-after
         // Wait for a new subscription
         SipDialogEventSubscriptionHandle h;
         NewDialogEventSubscriptionEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onNewSubscription",
            2000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, bob.handle);
         bobSubs = h;
         ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
      }

      // Accept the subscription
      ASSERT_EQ(bob.dialogEventSubscriptionManager->accept(bobSubs), kSuccess);

      {
         // Wait for the subscription state to transition to Active
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionStateChanged",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      {
         SipEventState eventState = EventStateFromFile(bob.config.uri(), "BlfLocalTest_TestRLSSubscribe.txt");
         ASSERT_EQ(bob.subs->notify(bobSubs, eventState), kSuccess);
      }

      {
         // Wait for the subscription state to transition to Terminated
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionEnded",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }
   });

   auto aliceEvents = std::async(std::launch::async, [&] () {
      cpc::string dialogId;
      SipBusyLampFieldRemoteLineHandle aliceRemoteLine = bob.config.uri();

      // Attendant subscribes to the remote line
      SipBusyLampFieldRemoteLineSetSettings settings;
      settings.expires = 3600;
      settings.resourceListAddress = bob.config.uri();
      SipBusyLampFieldRemoteLineSetHandle remoteLineSet = alice.busyLampFieldManager->createBusyLampFieldRemoteLineSet(alice.handle, settings);
      ASSERT_TRUE(remoteLineSet > 0);

      // Start the subscription
      assertSuccess(alice.busyLampFieldManager->start(remoteLineSet));

      // ==== Line 1 ====
      // Wait for the new subscription notification
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress1, LineName1);
      // Validate the state of the remote line
      assertBLFRemoteLineState(__LINE__, alice, remoteLineSet, LineAddress1, true, SipSubscriptionState_Pending);
      // Wait for the subscription state change notification
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress1, SipSubscriptionState_Active);
      // Wait for remote line state change notification
      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress1, DialogDirection_NotSpecified, DialogState_Terminated, LineAddress1, LineTelephone1, LineExt1, "", "", dialogId);

      // ==== Line 2 ====
      // Wait for the new subscription notification
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress2, LineName2);
      // Validate the state of the remote line
      assertBLFRemoteLineState(__LINE__, alice, remoteLineSet, LineAddress2, true, SipSubscriptionState_Pending);
      // Wait for the subscription state change notification
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, SipSubscriptionState_Active);
      // Wait for remote line state change notification
      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, DialogDirection_Initiator, DialogState_Proceeding, LineAddress2, LineTelephone2, LineExt2, "", "", dialogId);

      // ==== Line 3 ====
      // Wait for the new subscription notification
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress3, LineName3);
      // Validate the state of the remote line
      assertBLFRemoteLineState(__LINE__, alice, remoteLineSet, LineAddress3, true, SipSubscriptionState_Pending);
      // Wait for the subscription state change notification
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, SipSubscriptionState_Active);
      // Wait for remote line state change notification
      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, DialogDirection_Recipient, DialogState_Confirmed, LineAddress3, LineTelephone3, LineExt3, "", "", dialogId);

      // BLF server should terminate the subscription here

      // Wait for the subscription "waiting" notifications
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress1, SipSubscriptionState_WaitingToSubscribe);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, SipSubscriptionState_WaitingToSubscribe);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, SipSubscriptionState_WaitingToSubscribe);

      // Wait for the subscription state change notification
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress1, SipSubscriptionState_Active);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, SipSubscriptionState_Active);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, SipSubscriptionState_Active);


      // Terminate the subscription
      assertSuccess(alice.busyLampFieldManager->end(remoteLineSet));

      // Wait for the subscription termination notifications
      assertBLFRemoteLineSubscriptionEnded(__LINE__, alice, remoteLineSet, LineAddress1);
      assertBLFRemoteLineSubscriptionEnded(__LINE__, alice, remoteLineSet, LineAddress2);
      assertBLFRemoteLineSubscriptionEnded(__LINE__, alice, remoteLineSet, LineAddress3);
   });

   waitFor2(aliceEvents, bobEvents);
}

// SCORE-1207 In this case we are terminating a subscription with a reason of "deactivated" and 
// expecting the client to resubscribe immediately.
TEST_F(BlfLocalTest, TestRLSTerminationAsDeactivate)
{
   BlfAttendantTestAccount alice("alice");
   BlfServerTestAccount bob("bob");

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipDialogEventSubscriptionHandle bobSubs = 0;
      {
         // Wait for a new subscription
         SipDialogEventSubscriptionHandle h;
         NewDialogEventSubscriptionEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onNewSubscription",
            5000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, bob.handle);
         bobSubs = h;
         ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
      }

      // Accept the subscription
      ASSERT_EQ(bob.dialogEventSubscriptionManager->accept(bobSubs), kSuccess);

      {
         // Wait for the subscription state to transition to Active
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionStateChanged",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      {
         SipEventState eventState = EventStateFromFile(bob.config.uri(), "BlfLocalTest_TestRLSSubscribe.txt");
         ASSERT_EQ(bob.subs->notify(bobSubs, eventState), kSuccess);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      ASSERT_EQ(bob.subs->end(bobSubs, SipSubscriptionTerminateReason_Deactivate), kSuccess);

      {
         // Wait for the subscription state to transition to Terminated
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionEnded",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }

      {
         // NOTE the short timeout here as we don't want any delay due to expected immediate retry
         // Wait for a new subscription
         SipDialogEventSubscriptionHandle h;
         NewDialogEventSubscriptionEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onNewSubscription",
            2000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, bob.handle);
         bobSubs = h;
         ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
      }

      // Accept the subscription
      ASSERT_EQ(bob.dialogEventSubscriptionManager->accept(bobSubs), kSuccess);

      {
         // Wait for the subscription state to transition to Active
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionStateChanged",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      {
         SipEventState eventState = EventStateFromFile(bob.config.uri(), "BlfLocalTest_TestRLSSubscribe.txt");
         ASSERT_EQ(bob.subs->notify(bobSubs, eventState), kSuccess);
      }

      {
         // Wait for the subscription state to transition to Terminated
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionEnded",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }
   });

   auto aliceEvents = std::async(std::launch::async, [&] () {
      cpc::string dialogId;
      SipBusyLampFieldRemoteLineHandle aliceRemoteLine = bob.config.uri();

      // Attendant subscribes to the remote line
      SipBusyLampFieldRemoteLineSetSettings settings;
      settings.expires = 3600;
      settings.resourceListAddress = bob.config.uri();
      SipBusyLampFieldRemoteLineSetHandle remoteLineSet = alice.busyLampFieldManager->createBusyLampFieldRemoteLineSet(alice.handle, settings);
      ASSERT_TRUE(remoteLineSet > 0);

      // Start the subscription
      assertSuccess(alice.busyLampFieldManager->start(remoteLineSet));

      // Wait for the new subscription notifications
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress1, LineName1);
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress2, LineName2);
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress3, LineName3);

      // Wait for the subscription state change notification
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress1, SipSubscriptionState_Active);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, SipSubscriptionState_Active);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, SipSubscriptionState_Active);

      // BLF server should terminate the subscription here

      // Wait for the subscription "waiting" notifications
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress1, SipSubscriptionState_WaitingToSubscribe);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, SipSubscriptionState_WaitingToSubscribe);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, SipSubscriptionState_WaitingToSubscribe);

      // Wait for the subscription state change notification
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress1, SipSubscriptionState_Active);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, SipSubscriptionState_Active);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, SipSubscriptionState_Active);

      // Terminate the subscription
      assertSuccess(alice.busyLampFieldManager->end(remoteLineSet));

      // Wait for the subscription termination notifications
      assertBLFRemoteLineSubscriptionEnded(__LINE__, alice, remoteLineSet, LineAddress1);
      assertBLFRemoteLineSubscriptionEnded(__LINE__, alice, remoteLineSet, LineAddress2);
      assertBLFRemoteLineSubscriptionEnded(__LINE__, alice, remoteLineSet, LineAddress3);
   });

   waitFor2(aliceEvents, bobEvents);
}

// SCORE-1207 In this case we are terminating a subscription with a reason of "timeout" and 
// expecting the client to resubscribe immediately.
TEST_F(BlfLocalTest, TestRLSTerminationAsTimeout)
{
   BlfAttendantTestAccount alice("alice");
   BlfServerTestAccount bob("bob");

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipDialogEventSubscriptionHandle bobSubs = 0;
      {
         // Wait for a new subscription
         SipDialogEventSubscriptionHandle h;
         NewDialogEventSubscriptionEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onNewSubscription",
            5000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, bob.handle);
         bobSubs = h;
         ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
      }

      // Accept the subscription
      ASSERT_EQ(bob.dialogEventSubscriptionManager->accept(bobSubs), kSuccess);

      {
         // Wait for the subscription state to transition to Active
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionStateChanged",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      {
         SipEventState eventState = EventStateFromFile(bob.config.uri(), "BlfLocalTest_TestRLSSubscribe.txt");
         ASSERT_EQ(bob.subs->notify(bobSubs, eventState), kSuccess);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      ASSERT_EQ(bob.subs->end(bobSubs, SipSubscriptionTerminateReason_Timeout), kSuccess);

      {
         // Wait for the subscription state to transition to Terminated
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionEnded",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }

      {
         // NOTE the short timeout here as we don't want any delay due to expected immediate retry
         // Wait for a new subscription
         SipDialogEventSubscriptionHandle h;
         NewDialogEventSubscriptionEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onNewSubscription",
            2000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, bob.handle);
         bobSubs = h;
         ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
      }

      // Accept the subscription
      ASSERT_EQ(bob.dialogEventSubscriptionManager->accept(bobSubs), kSuccess);

      {
         // Wait for the subscription state to transition to Active
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionStateChanged",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      // DRL NOTE: If I remove this line the LinuxSdkUnitTest-trunk-phabricator job will fail
      // because the NOTIFY sent out has the termination in it. This doesn't happen on my local machine.
      // We should investigate this further.
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      {
         SipEventState eventState = EventStateFromFile(bob.config.uri(), "BlfLocalTest_TestRLSSubscribe.txt");
         ASSERT_EQ(bob.subs->notify(bobSubs, eventState), kSuccess);
      }

      {
         // Wait for the subscription state to transition to Terminated
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionEnded",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }
   });

   auto aliceEvents = std::async(std::launch::async, [&] () {
      cpc::string dialogId;
      SipBusyLampFieldRemoteLineHandle aliceRemoteLine = bob.config.uri();

      // Attendant subscribes to the remote line
      SipBusyLampFieldRemoteLineSetSettings settings;
      settings.expires = 3600;
      settings.resourceListAddress = bob.config.uri();
      SipBusyLampFieldRemoteLineSetHandle remoteLineSet = alice.busyLampFieldManager->createBusyLampFieldRemoteLineSet(alice.handle, settings);
      ASSERT_TRUE(remoteLineSet > 0);

      // Start the subscription
      assertSuccess(alice.busyLampFieldManager->start(remoteLineSet));

      // Wait for the new subscription notifications
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress1, LineName1);
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress2, LineName2);
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress3, LineName3);

      // Wait for the subscription state change notification
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress1, SipSubscriptionState_Active);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, SipSubscriptionState_Active);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, SipSubscriptionState_Active);

      // BLF server should terminate the subscription here

      // Wait for the subscription "waiting" notifications
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress1, SipSubscriptionState_WaitingToSubscribe);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, SipSubscriptionState_WaitingToSubscribe);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, SipSubscriptionState_WaitingToSubscribe);

      // Wait for the subscription state change notification
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress1, SipSubscriptionState_Active);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, SipSubscriptionState_Active);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, SipSubscriptionState_Active);

      // Terminate the subscription
      assertSuccess(alice.busyLampFieldManager->end(remoteLineSet));

      // Wait for the subscription termination notifications
      assertBLFRemoteLineSubscriptionEnded(__LINE__, alice, remoteLineSet, LineAddress1);
      assertBLFRemoteLineSubscriptionEnded(__LINE__, alice, remoteLineSet, LineAddress2);
      assertBLFRemoteLineSubscriptionEnded(__LINE__, alice, remoteLineSet, LineAddress3);
   });

   waitFor2(aliceEvents, bobEvents);
}

// SCORE-1343 In this case we are terminating a subscription with no reason
// expecting the client to resubscribe within 5 seconds
TEST_F(BlfLocalTest, TestRLSTermination)
{
   BlfAttendantTestAccount alice("alice");
   BlfServerTestAccount bob("bob");

   const int sdkBlfSubTerminatedNoReasonRetryDelayMsec = 6000;

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipDialogEventSubscriptionHandle bobSubs = 0;
      {
         // Wait for a new subscription
         SipDialogEventSubscriptionHandle h;
         NewDialogEventSubscriptionEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onNewSubscription",
            5000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, bob.handle);
         bobSubs = h;
         ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
      }

      // Accept the subscription
      ASSERT_EQ(bob.dialogEventSubscriptionManager->accept(bobSubs), kSuccess);

      {
         // Wait for the subscription state to transition to Active
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionStateChanged",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      {
         SipEventState eventState = EventStateFromFile(bob.config.uri(), "BlfLocalTest_TestRLSSubscribe.txt");
         ASSERT_EQ(bob.subs->notify(bobSubs, eventState), kSuccess);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      ASSERT_EQ(bob.subs->end(bobSubs, SipSubscriptionTerminateReason_NoReason), kSuccess);

      {
         // Wait for the subscription state to transition to Terminated
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionEnded",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }


      {
         SipDialogEventSubscriptionHandle h;
         NewDialogEventSubscriptionEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onNewSubscription",
            sdkBlfSubTerminatedNoReasonRetryDelayMsec, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, bob.handle);
         bobSubs = h;
         ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
      }

      // Accept the subscription
      ASSERT_EQ(bob.dialogEventSubscriptionManager->accept(bobSubs), kSuccess);

      {
         // Wait for the subscription state to transition to Active
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionStateChanged",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      {
         SipEventState eventState = EventStateFromFile(bob.config.uri(), "BlfLocalTest_TestRLSReSubscribe.txt");
         ASSERT_EQ(bob.subs->notify(bobSubs, eventState), kSuccess);
      }

      {
         // Wait for the subscription state to transition to Terminated
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionEnded",
            10000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }
   });

   auto aliceEvents = std::async(std::launch::async, [&] () {
      cpc::string dialogId;
      SipBusyLampFieldRemoteLineHandle aliceRemoteLine = bob.config.uri();

      // Attendant subscribes to the remote line
      SipBusyLampFieldRemoteLineSetSettings settings;
      settings.expires = 3600;
      settings.resourceListAddress = bob.config.uri();
      SipBusyLampFieldRemoteLineSetHandle remoteLineSet = alice.busyLampFieldManager->createBusyLampFieldRemoteLineSet(alice.handle, settings);
      ASSERT_TRUE(remoteLineSet > 0);

      // Start the subscription
      assertSuccess(alice.busyLampFieldManager->start(remoteLineSet));

      // Wait for the new subscription notifications
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress1, LineName1);
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress2, LineName2);
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress3, LineName3);

      // Wait for the subscription state change notification
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress1, SipSubscriptionState_Active);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, SipSubscriptionState_Active);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, SipSubscriptionState_Active);

      // BLF server should terminate the subscription here

      // Wait for the subscription "waiting" notifications
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress1, SipSubscriptionState_WaitingToSubscribe);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, SipSubscriptionState_WaitingToSubscribe);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, SipSubscriptionState_WaitingToSubscribe);

      auto start = std::chrono::system_clock::now();

      // expect a new subscription for line 4
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress4, LineName4);

      // but expect no other new subscriptions
      {
         SipBusyLampFieldRemoteLineSetHandle h;
         RemoteLineNewSubscriptionEvent evt;
         ASSERT_FALSE(alice.busyLampFieldEvents->expectEvent(__LINE__,
            "SipBusyLampFieldHandler::onRemoteLineNewSubscription",
            5000,
            AlwaysTruePred(), h, evt));
      }

      // Wait for the subscription state change notification; the order of lines is important and should match that of the NOTIFY
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress4, SipSubscriptionState_Active);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, SipSubscriptionState_Active);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, SipSubscriptionState_Active);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress1, SipSubscriptionState_Active);

      auto end = std::chrono::system_clock::now();

      std::chrono::duration<double> diffSec = end - start;
      ASSERT_LE(diffSec.count(), sdkBlfSubTerminatedNoReasonRetryDelayMsec * 1000);

      // Terminate the subscription
      assertSuccess(alice.busyLampFieldManager->end(remoteLineSet));

      // Wait for the subscription termination notifications
      assertBLFRemoteLineSubscriptionEnded(__LINE__, alice, remoteLineSet, LineAddress1);
      assertBLFRemoteLineSubscriptionEnded(__LINE__, alice, remoteLineSet, LineAddress2);
      assertBLFRemoteLineSubscriptionEnded(__LINE__, alice, remoteLineSet, LineAddress3);
   });

   waitFor2(aliceEvents, bobEvents);
}

// NOTE: this is a 5 minute test (hence disabled)!
// In this case we are terminating a subscription with a reason of "probation" and 
// expecting the client to resubscribe in about 5 minutes.
TEST_F(BlfLocalTest, DISABLED_TestRLSTerminationWithNoRetryHeader)
{
   BlfAttendantTestAccount alice("alice");
   BlfServerTestAccount bob("bob");

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipDialogEventSubscriptionHandle bobSubs = 0;
      {
         // Wait for a new subscription
         SipDialogEventSubscriptionHandle h;
         NewDialogEventSubscriptionEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onNewSubscription",
            5000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, bob.handle);
         bobSubs = h;
         ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
      }

      // Accept the subscription
      ASSERT_EQ(bob.dialogEventSubscriptionManager->accept(bobSubs), kSuccess);

      {
         // Wait for the subscription state to transition to Active
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionStateChanged",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      {
         SipEventState eventState = EventStateFromFile(bob.config.uri(), "BlfLocalTest_TestRLSSubscribe.txt");
         ASSERT_EQ(bob.subs->notify(bobSubs, eventState), kSuccess);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      // NOTE we are NOT providing a retry-after
      ASSERT_EQ(bob.subs->end(bobSubs, SipSubscriptionTerminateReason_Probation), kSuccess);

      {
         // Wait for the subscription state to transition to Terminated
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionEnded",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }

      {
         // the SDK will use a delay of 300 seconds with a random give-or-take of up to 2.5 seconds for the retry
         // Wait for NO new subscription for 295 seconds
         SipDialogEventSubscriptionHandle h;
         NewDialogEventSubscriptionEvent evt;
         ASSERT_FALSE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onNewSubscription",
            295000, AlwaysTruePred(), h, evt));
      }

      {
         // Wait for a new subscription for up to 10 seconds
         SipDialogEventSubscriptionHandle h;
         NewDialogEventSubscriptionEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onNewSubscription",
            10000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, bob.handle);
         bobSubs = h;
         ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
      }

      // Accept the subscription
      ASSERT_EQ(bob.dialogEventSubscriptionManager->accept(bobSubs), kSuccess);

      {
         // Wait for the subscription state to transition to Active
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionStateChanged",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      {
         SipEventState eventState = EventStateFromFile(bob.config.uri(), "BlfLocalTest_TestRLSSubscribe.txt");
         ASSERT_EQ(bob.subs->notify(bobSubs, eventState), kSuccess);
      }

      {
         // Wait for the subscription state to transition to Terminated
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionEnded",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }
   });

   auto aliceEvents = std::async(std::launch::async, [&] () {
      cpc::string dialogId;
      SipBusyLampFieldRemoteLineHandle aliceRemoteLine = bob.config.uri();

      // Attendant subscribes to the remote line
      SipBusyLampFieldRemoteLineSetSettings settings;
      settings.expires = 3600;
      settings.resourceListAddress = bob.config.uri();
      SipBusyLampFieldRemoteLineSetHandle remoteLineSet = alice.busyLampFieldManager->createBusyLampFieldRemoteLineSet(alice.handle, settings);
      ASSERT_TRUE(remoteLineSet > 0);

      // Start the subscription
      assertSuccess(alice.busyLampFieldManager->start(remoteLineSet));

      // Wait for the new subscription notifications
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress1, LineName1);
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress2, LineName2);
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress3, LineName3);

      // Wait for the subscription "active" notifications
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress1, SipSubscriptionState_Active);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, SipSubscriptionState_Active);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, SipSubscriptionState_Active);

      // BLF server should terminate the subscription here

      // Wait for the subscription "waiting" notifications
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress1, SipSubscriptionState_WaitingToSubscribe);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, SipSubscriptionState_WaitingToSubscribe);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, SipSubscriptionState_WaitingToSubscribe);

      // retry-after is around 300 seconds
      std::this_thread::sleep_for(std::chrono::seconds(300));

      // Wait for the subscription "active" notifications
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress1, SipSubscriptionState_Active);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, SipSubscriptionState_Active);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, SipSubscriptionState_Active);

      // Terminate the subscription
      assertSuccess(alice.busyLampFieldManager->end(remoteLineSet));

      // Wait for the subscription termination notifications
      assertBLFRemoteLineSubscriptionEnded(__LINE__, alice, remoteLineSet, LineAddress1);
      assertBLFRemoteLineSubscriptionEnded(__LINE__, alice, remoteLineSet, LineAddress2);
      assertBLFRemoteLineSubscriptionEnded(__LINE__, alice, remoteLineSet, LineAddress3);
   });

   waitFor2Ms(aliceEvents, bobEvents, std::chrono::seconds(350));
}

// In this case we are terminating a subscription with a reason of "probation" with 
// a 10 second retry-after and expecting the client to resubscribe then.
TEST_F(BlfLocalTest, TestRLSTerminationProbationWithRetryHeader)
{
   BlfAttendantTestAccount alice("alice");
   BlfServerTestAccount bob("bob");

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipDialogEventSubscriptionHandle bobSubs = 0;
      {
         // Wait for a new subscription
         SipDialogEventSubscriptionHandle h;
         NewDialogEventSubscriptionEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onNewSubscription",
            5000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, bob.handle);
         bobSubs = h;
         ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
      }

      // Accept the subscription
      ASSERT_EQ(bob.dialogEventSubscriptionManager->accept(bobSubs), kSuccess);

      {
         // Wait for the subscription state to transition to Active
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionStateChanged",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      {
         SipEventState eventState = EventStateFromFile(bob.config.uri(), "BlfLocalTest_TestRLSSubscribe.txt");
         ASSERT_EQ(bob.subs->notify(bobSubs, eventState), kSuccess);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      // NOTE the 10 second value for retry-after
      ASSERT_EQ(bob.subs->end(bobSubs, SipSubscriptionTerminateReason_Probation, 10), kSuccess);

      {
         // Wait for the subscription state to transition to Terminated
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionEnded",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }

      {
         // Wait for NO new subscription for 8 seconds
         SipDialogEventSubscriptionHandle h;
         NewDialogEventSubscriptionEvent evt;
         ASSERT_FALSE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onNewSubscription",
            8000, AlwaysTruePred(), h, evt));
      }

      {
         // Wait for a new subscription
         SipDialogEventSubscriptionHandle h;
         NewDialogEventSubscriptionEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onNewSubscription",
            5000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, bob.handle);
         bobSubs = h;
         ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
      }

      // Accept the subscription
      ASSERT_EQ(bob.dialogEventSubscriptionManager->accept(bobSubs), kSuccess);

      {
         // Wait for the subscription state to transition to Active
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionStateChanged",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      {
         SipEventState eventState = EventStateFromFile(bob.config.uri(), "BlfLocalTest_TestRLSSubscribe.txt");
         ASSERT_EQ(bob.subs->notify(bobSubs, eventState), kSuccess);
      }

      {
         // Wait for the subscription state to transition to Terminated
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionEnded",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }
   });

   auto aliceEvents = std::async(std::launch::async, [&] () {
      cpc::string dialogId;
      SipBusyLampFieldRemoteLineHandle aliceRemoteLine = bob.config.uri();

      // Attendant subscribes to the remote line
      SipBusyLampFieldRemoteLineSetSettings settings;
      settings.expires = 3600;
      settings.resourceListAddress = bob.config.uri();
      SipBusyLampFieldRemoteLineSetHandle remoteLineSet = alice.busyLampFieldManager->createBusyLampFieldRemoteLineSet(alice.handle, settings);
      ASSERT_TRUE(remoteLineSet > 0);

      // Start the subscription
      assertSuccess(alice.busyLampFieldManager->start(remoteLineSet));

      // Wait for the new subscription notifications
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress1, LineName1);
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress2, LineName2);
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress3, LineName3);

      // Wait for the subscription "active" notifications
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress1, SipSubscriptionState_Active);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, SipSubscriptionState_Active);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, SipSubscriptionState_Active);

      // BLF server should terminate the subscription here

      // Wait for the subscription "waiting" notifications
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress1, SipSubscriptionState_WaitingToSubscribe);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, SipSubscriptionState_WaitingToSubscribe);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, SipSubscriptionState_WaitingToSubscribe);

      // retry-after is 10 seconds
      {
         // Wait for NO new subscription for 8 seconds
         SipBusyLampFieldRemoteLineSetHandle h;
         RemoteLineNewSubscriptionEvent evt;
         ASSERT_FALSE(cpcExpectEvent(alice.busyLampFieldEvents, "SipBusyLampFieldHandler::onRemoteLineNewSubscription",
            8000, AlwaysTruePred(), h, evt));
      }

      // Wait for the subscription "active" notifications
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress1, SipSubscriptionState_Active);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, SipSubscriptionState_Active);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, SipSubscriptionState_Active);

      // Terminate the subscription
      assertSuccess(alice.busyLampFieldManager->end(remoteLineSet));

      // Wait for the subscription termination notifications
      assertBLFRemoteLineSubscriptionEnded(__LINE__, alice, remoteLineSet, LineAddress1);
      assertBLFRemoteLineSubscriptionEnded(__LINE__, alice, remoteLineSet, LineAddress2);
      assertBLFRemoteLineSubscriptionEnded(__LINE__, alice, remoteLineSet, LineAddress3);
   });

   waitFor2(aliceEvents, bobEvents);
}

// In this case we are terminating a subscription with a reason of "noresource" with 
// a 3 second retry-after and expecting the client NOT to resubscribe.
TEST_F(BlfLocalTest, TestRLSTerminationNoResourceWithRetryHeader)
{
   BlfAttendantTestAccount alice("alice");
   BlfServerTestAccount bob("bob");

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipDialogEventSubscriptionHandle bobSubs = 0;
      {
         // Wait for a new subscription
         SipDialogEventSubscriptionHandle h;
         NewDialogEventSubscriptionEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onNewSubscription",
            5000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, bob.handle);
         bobSubs = h;
         ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
      }

      // Accept the subscription
      ASSERT_EQ(bob.dialogEventSubscriptionManager->accept(bobSubs), kSuccess);

      {
         // Wait for the subscription state to transition to Active
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionStateChanged",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      {
         SipEventState eventState = EventStateFromFile(bob.config.uri(), "BlfLocalTest_TestRLSSubscribe.txt");
         ASSERT_EQ(bob.subs->notify(bobSubs, eventState), kSuccess);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      // NOTE the 3 second value for retry-after, but with "noresource" the client should NOT resubscribe
      ASSERT_EQ(bob.subs->end(bobSubs, SipSubscriptionTerminateReason_NoResource, 3), kSuccess);

      {
         // Wait for the subscription state to transition to Terminated
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionEnded",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }

      {
         // Wait for NO new subscription for 8 seconds to be sure
         SipDialogEventSubscriptionHandle h;
         NewDialogEventSubscriptionEvent evt;
         ASSERT_FALSE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onNewSubscription",
            8000, AlwaysTruePred(), h, evt));
      }
   });

   auto aliceEvents = std::async(std::launch::async, [&] () {
      cpc::string dialogId;
      SipBusyLampFieldRemoteLineHandle aliceRemoteLine = bob.config.uri();

      // Attendant subscribes to the remote line
      SipBusyLampFieldRemoteLineSetSettings settings;
      settings.expires = 3600;
      settings.resourceListAddress = bob.config.uri();
      SipBusyLampFieldRemoteLineSetHandle remoteLineSet = alice.busyLampFieldManager->createBusyLampFieldRemoteLineSet(alice.handle, settings);
      ASSERT_TRUE(remoteLineSet > 0);

      // Start the subscription
      assertSuccess(alice.busyLampFieldManager->start(remoteLineSet));

      // Wait for the new subscription notifications
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress1, LineName1);
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress2, LineName2);
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress3, LineName3);

      // Wait for the subscription "active" notifications
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress1, SipSubscriptionState_Active);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, SipSubscriptionState_Active);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, SipSubscriptionState_Active);

      // BLF server should terminate the subscription here

      // Wait for the subscription termination notifications
      assertBLFRemoteLineSubscriptionEnded(__LINE__, alice, remoteLineSet, LineAddress1);
      assertBLFRemoteLineSubscriptionEnded(__LINE__, alice, remoteLineSet, LineAddress2);
      assertBLFRemoteLineSubscriptionEnded(__LINE__, alice, remoteLineSet, LineAddress3);

      // retry-after is 3 seconds
      {
         // Wait for NO new subscription for 8 seconds to be sure
         SipBusyLampFieldRemoteLineSetHandle h;
         RemoteLineNewSubscriptionEvent evt;
         ASSERT_FALSE(cpcExpectEvent(alice.busyLampFieldEvents, "SipBusyLampFieldHandler::onRemoteLineNewSubscription",
            8000, AlwaysTruePred(), h, evt));
      }
   });

   waitFor2(aliceEvents, bobEvents);
}

// In this case we are having the BLF server send a sequence of lines added and removed and 
// expecting the client to received matching updates.
TEST_F(BlfLocalTest, TestRLSLinesAddedAndRemoved)
{
   BlfAttendantTestAccount alice("alice");
   BlfServerTestAccount bob("bob");

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipDialogEventSubscriptionHandle bobSubs = 0;
      {
         // Wait for a new subscription
         SipDialogEventSubscriptionHandle h;
         NewDialogEventSubscriptionEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onNewSubscription",
            5000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, bob.handle);
         bobSubs = h;
         ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
      }

      // Accept the subscription
      ASSERT_EQ(bob.dialogEventSubscriptionManager->accept(bobSubs), kSuccess);

      {
         // Wait for the subscription state to transition to Active
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionStateChanged",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      {
         SipEventState eventState = EventStateFromFile(bob.config.uri(), "BlfLocalTest_TestRLSLinesAddedAndRemoved_1.txt");
         ASSERT_EQ(bob.subs->notify(bobSubs, eventState), kSuccess);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(3000));

      {
         SipEventState eventState = EventStateFromFile(bob.config.uri(), "BlfLocalTest_TestRLSLinesAddedAndRemoved_2.txt");
         ASSERT_EQ(bob.subs->notify(bobSubs, eventState), kSuccess);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(3000));

      {
         SipEventState eventState = EventStateFromFile(bob.config.uri(), "BlfLocalTest_TestRLSLinesAddedAndRemoved_3.txt");
         ASSERT_EQ(bob.subs->notify(bobSubs, eventState), kSuccess);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(3000));

      {
         SipEventState eventState = EventStateFromFile(bob.config.uri(), "BlfLocalTest_TestRLSLinesAddedAndRemoved_4.txt");
         ASSERT_EQ(bob.subs->notify(bobSubs, eventState), kSuccess);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(3000));

      ASSERT_EQ(bob.subs->end(bobSubs, SipSubscriptionTerminateReason_NoResource), kSuccess);

      {
         // Wait for the subscription state to transition to Terminated
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionEnded",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }
   });

   auto aliceEvents = std::async(std::launch::async, [&] () {
      cpc::string dialogId;
      SipBusyLampFieldRemoteLineHandle aliceRemoteLine = bob.config.uri();

      // Attendant subscribes to the remote line
      SipBusyLampFieldRemoteLineSetSettings settings;
      settings.expires = 3600;
      settings.resourceListAddress = bob.config.uri();
      SipBusyLampFieldRemoteLineSetHandle remoteLineSet = alice.busyLampFieldManager->createBusyLampFieldRemoteLineSet(alice.handle, settings);
      ASSERT_TRUE(remoteLineSet > 0);

      // Start the subscription
      assertSuccess(alice.busyLampFieldManager->start(remoteLineSet));


      // The first set should include lines 2 and 3

      // ==== Line 2 ====
      // Wait for the new subscription notification
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress2, LineName2);
      // Validate the state of the remote line
      assertBLFRemoteLineState(__LINE__, alice, remoteLineSet, LineAddress2, true, SipSubscriptionState_Pending);
      // Wait for the subscription state change notification
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, SipSubscriptionState_Active);
      // Wait for remote line state change notification
      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, DialogDirection_Initiator, DialogState_Proceeding, LineAddress2, LineTelephone2, LineExt2, "", "", dialogId);

      // ==== Line 3 ====
      // Wait for the new subscription notification
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress3, LineName3);
      // Validate the state of the remote line
      assertBLFRemoteLineState(__LINE__, alice, remoteLineSet, LineAddress3, true, SipSubscriptionState_Pending);
      // Wait for the subscription state change notification
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, SipSubscriptionState_Active);
      // Wait for remote line state change notification
      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, DialogDirection_Recipient, DialogState_Confirmed, LineAddress3, LineTelephone3, LineExt3, "", "", dialogId);


      // The next update we get should remove line 2

      // the line hasn't changed but we get events regardless?
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, SipSubscriptionState_Active);

      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, DialogDirection_NotSpecified, DialogState_Terminated, LineAddress2, LineTelephone2, LineExt2, "", "", dialogId);
      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, DialogDirection_Recipient, DialogState_Confirmed, LineAddress3, LineTelephone3, LineExt3, "", "", dialogId);


      // The next update we get should add line 1 and restore line 2

      // ==== Line 1 ====
      // Wait for the new subscription notification
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress1, LineName1);
      // Validate the state of the remote line
      assertBLFRemoteLineState(__LINE__, alice, remoteLineSet, LineAddress1, true, SipSubscriptionState_Pending);
      // Wait for the subscription state change notification
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress1, SipSubscriptionState_Active);
      // Wait for remote line state change notification
      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress1, DialogDirection_NotSpecified, DialogState_Terminated, LineAddress1, LineTelephone1, LineExt1, "", "", dialogId);

      // ==== Line 2 ====
// DRL FIXIT! I expect the app will want these events (SCORE-1229)...
//      // Wait for the new subscription notification
//      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress2, LineName2);
//      // Validate the state of the remote line
//      assertBLFRemoteLineState(__LINE__, alice, remoteLineSet, LineAddress2, true, SipSubscriptionState_Pending);
      // Wait for the subscription state change notification
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, SipSubscriptionState_Active);
      // Wait for remote line state change notification
      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, DialogDirection_Initiator, DialogState_Proceeding, LineAddress2, LineTelephone2, LineExt2, "", "", dialogId);

      // ==== Line 3 ====
      // the line state hasn't changed but we get an event regardless?
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, SipSubscriptionState_Active);
      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, DialogDirection_Recipient, DialogState_Confirmed, LineAddress3, LineTelephone3, LineExt3, "", "", dialogId);
      
      // The next update we get should remove all three lines

      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress1, DialogDirection_NotSpecified, DialogState_Terminated, LineAddress1, LineTelephone1, LineExt1, "", "", dialogId);
      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, DialogDirection_NotSpecified, DialogState_Terminated, LineAddress2, LineTelephone2, LineExt2, "", "", dialogId);
      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, DialogDirection_NotSpecified, DialogState_Terminated, LineAddress3, LineTelephone3, LineExt3, "", "", dialogId);

      assertBLFRemoteLineEmptySubscriptions(__LINE__, alice, remoteLineSet);

      // BLF server should terminate the subscription here

      // Wait for the subscription termination notifications
      assertBLFRemoteLineSubscriptionEnded(__LINE__, alice, remoteLineSet, LineAddress1);
      assertBLFRemoteLineSubscriptionEnded(__LINE__, alice, remoteLineSet, LineAddress2);
      assertBLFRemoteLineSubscriptionEnded(__LINE__, alice, remoteLineSet, LineAddress3);
   });

   waitFor2(aliceEvents, bobEvents);
}

// In this case we are having the BLF server send a sequence of lines added and removed with 
// an initial disconnect and reconnect by the client, and expecting the client to receive
// matching updates.
TEST_F(BlfLocalTest, TestRLSLinesAddedAndRemovedWithDisconnect)
{
   const int sdkBlfSubTerminatedNoReasonRetryDelayMsec = 6000;

   BlfAttendantTestAccount alice("alice");
   BlfServerTestAccount bob("bob");

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipDialogEventSubscriptionHandle bobSubs = 0;
      {
         // Wait for a new subscription
         SipDialogEventSubscriptionHandle h;
         NewDialogEventSubscriptionEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onNewSubscription",
            5000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, bob.handle);
         bobSubs = h;
         ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
      }

      // Accept the subscription
      ASSERT_EQ(bob.dialogEventSubscriptionManager->accept(bobSubs), kSuccess);

      {
         // Wait for the subscription state to transition to Active
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionStateChanged",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      {
         SipEventState eventState = EventStateFromFile(bob.config.uri(), "BlfLocalTest_TestRLSLinesAddedAndRemoved_1.txt");
         ASSERT_EQ(bob.subs->notify(bobSubs, eventState), kSuccess);
      }

      // client disconnects and reconnects

      {
         // Wait for the subscription state to transition to Terminated
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionEnded",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }

      {
         // Wait for a new subscription
         SipDialogEventSubscriptionHandle h;
         NewDialogEventSubscriptionEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onNewSubscription",
            5000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, bob.handle);
         bobSubs = h;
         ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
      }

      // Accept the subscription
      ASSERT_EQ(bob.dialogEventSubscriptionManager->accept(bobSubs), kSuccess);

      {
         // Wait for the subscription state to transition to Active
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionStateChanged",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      {
         SipEventState eventState = EventStateFromFile(bob.config.uri(), "BlfLocalTest_TestRLSLinesAddedAndRemoved_1.txt");
         ASSERT_EQ(bob.subs->notify(bobSubs, eventState), kSuccess);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(3000));

      {
         SipEventState eventState = EventStateFromFile(bob.config.uri(), "BlfLocalTest_TestRLSLinesAddedAndRemoved_2.txt");
         ASSERT_EQ(bob.subs->notify(bobSubs, eventState), kSuccess);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(3000));

      ASSERT_EQ(bob.subs->end(bobSubs, SipSubscriptionTerminateReason_NoReason), kSuccess);

      {
         // Wait for the subscription state to transition to Terminated
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionEnded",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }


      {
         SipDialogEventSubscriptionHandle h;
         NewDialogEventSubscriptionEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onNewSubscription",
            sdkBlfSubTerminatedNoReasonRetryDelayMsec, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, bob.handle);
         bobSubs = h;
         ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
      }

      // Accept the subscription
      ASSERT_EQ(bob.dialogEventSubscriptionManager->accept(bobSubs), kSuccess);

      {
         // Wait for the subscription state to transition to Active
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionStateChanged",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      {
         SipEventState eventState = EventStateFromFile(bob.config.uri(), "BlfLocalTest_TestRLSLinesAddedAndRemoved_3.txt");
         ASSERT_EQ(bob.subs->notify(bobSubs, eventState), kSuccess);
      }


      {
         // Wait for the subscription state to transition to Terminated
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionEnded",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }
   });

   auto aliceEvents = std::async(std::launch::async, [&] () {
      cpc::string dialogId;
      SipBusyLampFieldRemoteLineHandle aliceRemoteLine = bob.config.uri();

      // Attendant subscribes to the remote line
      SipBusyLampFieldRemoteLineSetSettings settings;
      settings.expires = 3600;
      settings.resourceListAddress = bob.config.uri();
      SipBusyLampFieldRemoteLineSetHandle remoteLineSet = alice.busyLampFieldManager->createBusyLampFieldRemoteLineSet(alice.handle, settings);
      ASSERT_TRUE(remoteLineSet > 0);

      // Start the subscription
      assertSuccess(alice.busyLampFieldManager->start(remoteLineSet));


      // The first set should include lines 2 and 3

      // ==== Line 2 ====
      // Wait for the new subscription notification
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress2, LineName2);
      // Validate the state of the remote line
      assertBLFRemoteLineState(__LINE__, alice, remoteLineSet, LineAddress2, true, SipSubscriptionState_Pending);
      // Wait for the subscription state change notification
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, SipSubscriptionState_Active);
      // Wait for remote line state change notification
      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, DialogDirection_Initiator, DialogState_Proceeding, LineAddress2, LineTelephone2, LineExt2, "", "", dialogId);

      // ==== Line 3 ====
      // Wait for the new subscription notification
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress3, LineName3);
      // Validate the state of the remote line
      assertBLFRemoteLineState(__LINE__, alice, remoteLineSet, LineAddress3, true, SipSubscriptionState_Pending);
      // Wait for the subscription state change notification
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, SipSubscriptionState_Active);
      // Wait for remote line state change notification
      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, DialogDirection_Recipient, DialogState_Confirmed, LineAddress3, LineTelephone3, LineExt3, "", "", dialogId);

      // Re-start the subscription
      assertSuccess(alice.busyLampFieldManager->end(remoteLineSet));

      // Wait for the subscription termination notifications
      assertBLFRemoteLineSubscriptionEnded(__LINE__, alice, remoteLineSet, LineAddress2);
      assertBLFRemoteLineSubscriptionEnded(__LINE__, alice, remoteLineSet, LineAddress3);

      assertSuccess(alice.busyLampFieldManager->start(remoteLineSet));

      // Reconnect should just repeat what happened above

      // Wait for the new subscription notification
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress2, "");  // NO NAME?
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress3, "");  // NO NAME??

      // ==== Line 2 ====
      // Validate the state of the remote line
      assertBLFRemoteLineState(__LINE__, alice, remoteLineSet, LineAddress2, true, SipSubscriptionState_Pending);
      // Wait for the subscription state change notification
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, SipSubscriptionState_Active);
      // Wait for remote line state change notification
      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, DialogDirection_Initiator, DialogState_Proceeding, LineAddress2, LineTelephone2, LineExt2, "", "", dialogId);

      // ==== Line 3 ====
      // Validate the state of the remote line
      assertBLFRemoteLineState(__LINE__, alice, remoteLineSet, LineAddress3, true, SipSubscriptionState_Pending);
      // Wait for the subscription state change notification
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, SipSubscriptionState_Active);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, SipSubscriptionState_Active);
      // Wait for remote line state change notification
      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, DialogDirection_Recipient, DialogState_Confirmed, LineAddress3, LineTelephone3, LineExt3, "", "", dialogId);

      // The next update we get should add line 1 and restore line 2 ?????

      // Wait for the subscription termination notifications

//      assertBLFRemoteLineSubscriptionEnded(__LINE__, alice, remoteLineSet, LineAddress2);
      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, DialogDirection_NotSpecified, DialogState_Terminated, LineAddress2, LineTelephone2, LineExt2, "", "", dialogId);

      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, SipSubscriptionState_WaitingToSubscribe);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, SipSubscriptionState_WaitingToSubscribe);

      // Wait for the subscription state change notification
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress1, SipSubscriptionState_Active);
      
      // BLF server should terminate the subscription here

      assertSuccess(alice.busyLampFieldManager->end(remoteLineSet));
   });

   waitFor2(aliceEvents, bobEvents);
}

// In this case we are having the BLF server send a sequence of lines added and removed and 
// expecting the client to receive matching updates.
TEST_F(BlfLocalTest, TestRLSLinesDisconnectForEveryChange)
{
   const int sdkBlfSubTerminatedNoReasonRetryDelayMsec = 6000;

   BlfAttendantTestAccount alice("alice");
   BlfServerTestAccount bob("bob");

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipDialogEventSubscriptionHandle bobSubs = 0;
      {
         // Wait for a new subscription
         SipDialogEventSubscriptionHandle h;
         NewDialogEventSubscriptionEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onNewSubscription",
            5000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, bob.handle);
         bobSubs = h;
         ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
      }

      // Accept the subscription
      ASSERT_EQ(bob.dialogEventSubscriptionManager->accept(bobSubs), kSuccess);

      {
         // Wait for the subscription state to transition to Active
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionStateChanged",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      {
         SipEventState eventState = EventStateFromFile(bob.config.uri(), "BlfLocalTest_TestRLSLinesAddedAndRemoved_1.txt");
         ASSERT_EQ(bob.subs->notify(bobSubs, eventState), kSuccess);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(3000));

      ASSERT_EQ(bob.subs->end(bobSubs, SipSubscriptionTerminateReason_NoReason), kSuccess);

      {
         // Wait for the subscription state to transition to Terminated
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionEnded",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }


      {
         SipDialogEventSubscriptionHandle h;
         NewDialogEventSubscriptionEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onNewSubscription",
            sdkBlfSubTerminatedNoReasonRetryDelayMsec, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, bob.handle);
         bobSubs = h;
         ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
      }

      // Accept the subscription
      ASSERT_EQ(bob.dialogEventSubscriptionManager->accept(bobSubs), kSuccess);

      {
         // Wait for the subscription state to transition to Active
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionStateChanged",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      {
         SipEventState eventState = EventStateFromFile(bob.config.uri(), "BlfLocalTest_TestRLSLinesAddedAndRemoved_2.txt");
         ASSERT_EQ(bob.subs->notify(bobSubs, eventState), kSuccess);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(3000));

      ASSERT_EQ(bob.subs->end(bobSubs, SipSubscriptionTerminateReason_NoReason), kSuccess);

      {
         // Wait for the subscription state to transition to Terminated
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionEnded",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }


      {
         SipDialogEventSubscriptionHandle h;
         NewDialogEventSubscriptionEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onNewSubscription",
            sdkBlfSubTerminatedNoReasonRetryDelayMsec, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, bob.handle);
         bobSubs = h;
         ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
      }

      // Accept the subscription
      ASSERT_EQ(bob.dialogEventSubscriptionManager->accept(bobSubs), kSuccess);

      {
         // Wait for the subscription state to transition to Active
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionStateChanged",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      {
         SipEventState eventState = EventStateFromFile(bob.config.uri(), "BlfLocalTest_TestRLSLinesAddedAndRemoved_3.txt");
         ASSERT_EQ(bob.subs->notify(bobSubs, eventState), kSuccess);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(3000));

      ASSERT_EQ(bob.subs->end(bobSubs, SipSubscriptionTerminateReason_NoResource), kSuccess);

      {
         // Wait for the subscription state to transition to Terminated
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionEnded",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }
   });

   auto aliceEvents = std::async(std::launch::async, [&] () {
      cpc::string dialogId;
      SipBusyLampFieldRemoteLineHandle aliceRemoteLine = bob.config.uri();

      // Attendant subscribes to the remote line
      SipBusyLampFieldRemoteLineSetSettings settings;
      settings.expires = 3600;
      settings.resourceListAddress = bob.config.uri();
      SipBusyLampFieldRemoteLineSetHandle remoteLineSet = alice.busyLampFieldManager->createBusyLampFieldRemoteLineSet(alice.handle, settings);
      ASSERT_TRUE(remoteLineSet > 0);

      // Start the subscription
      assertSuccess(alice.busyLampFieldManager->start(remoteLineSet));


      // The first set should include lines 2 and 3

      // ==== Line 2 ====
      // Wait for the new subscription notification
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress2, LineName2);
      // Validate the state of the remote line
      assertBLFRemoteLineState(__LINE__, alice, remoteLineSet, LineAddress2, true, SipSubscriptionState_Pending);
      // Wait for the subscription state change notification
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, SipSubscriptionState_Active);
      // Wait for remote line state change notification
      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, DialogDirection_Initiator, DialogState_Proceeding, LineAddress2, LineTelephone2, LineExt2, "", "", dialogId);

      // ==== Line 3 ====
      // Wait for the new subscription notification
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress3, LineName3);
      // Validate the state of the remote line
      assertBLFRemoteLineState(__LINE__, alice, remoteLineSet, LineAddress3, true, SipSubscriptionState_Pending);
      // Wait for the subscription state change notification
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, SipSubscriptionState_Active);
      // Wait for remote line state change notification
      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, DialogDirection_Recipient, DialogState_Confirmed, LineAddress3, LineTelephone3, LineExt3, "", "", dialogId);

      // now server disconnects and we wait for the resubscribe to take place

      // The next update we get should remove line 2
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, SipSubscriptionState_WaitingToSubscribe);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, SipSubscriptionState_WaitingToSubscribe);

      // ==== Line 2 ====
      assertBLFRemoteLineSubscriptionEnded(__LINE__, alice, remoteLineSet, LineAddress2);
      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, DialogDirection_NotSpecified, DialogState_Terminated, LineAddress2, LineTelephone2, LineExt2, "", "", dialogId);

//      // ==== Line 3 ====
//      // the line hasn't changed but we get events regardless?
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, SipSubscriptionState_Active);
//      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, DialogDirection_Recipient, DialogState_Confirmed, LineAddress3, LineTelephone3, LineExt3, "", "", dialogId);


      // The next update we get should add line 1 and restore line 2
//      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, SipSubscriptionState_WaitingToSubscribe);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, SipSubscriptionState_WaitingToSubscribe);

      // ==== Line 1 ====
      // Wait for the new subscription notification
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress1, LineName1);
      // Validate the state of the remote line
      assertBLFRemoteLineState(__LINE__, alice, remoteLineSet, LineAddress1, true, SipSubscriptionState_Pending);
      // Wait for the subscription state change notification
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress1, SipSubscriptionState_Active);
      // Wait for remote line state change notification
//      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress1, DialogDirection_NotSpecified, DialogState_Terminated, LineAddress1, LineTelephone1, LineExt1, "", "", dialogId);

      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, DialogDirection_Recipient, DialogState_Confirmed, LineAddress3, LineTelephone3, LineExt3, "", "", dialogId);
      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress1, DialogDirection_NotSpecified, DialogState_Terminated, LineAddress1, LineTelephone1, LineExt1, "", "", dialogId);

      // ==== Line 2 ====
// DRL FIXIT! I expect the app will want these events (SCORE-1229)...
//      // Wait for the new subscription notification
//      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress2, LineName2);
//      // Validate the state of the remote line
//      assertBLFRemoteLineState(__LINE__, alice, remoteLineSet, LineAddress2, true, SipSubscriptionState_Pending);
      // Wait for the subscription state change notification
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, SipSubscriptionState_Active);
      // Wait for remote line state change notification
      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, DialogDirection_Initiator, DialogState_Proceeding, LineAddress2, LineTelephone2, LineExt2, "", "", dialogId);

      // ==== Line 3 ====
      // the line state hasn't changed but we get an event regardless?
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, SipSubscriptionState_Active);
      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, DialogDirection_Recipient, DialogState_Confirmed, LineAddress3, LineTelephone3, LineExt3, "", "", dialogId);
      
      // BLF server should terminate the subscription here

      // Wait for the subscription termination notifications
      assertBLFRemoteLineSubscriptionEnded(__LINE__, alice, remoteLineSet, LineAddress1);
      assertBLFRemoteLineSubscriptionEnded(__LINE__, alice, remoteLineSet, LineAddress2);
      assertBLFRemoteLineSubscriptionEnded(__LINE__, alice, remoteLineSet, LineAddress3);
   });

   waitFor2(aliceEvents, bobEvents);
}

// SCORE-1228 Test DNS failover handling for BLF to ensure the BLF subscriptions and states are restored.
// This test case starts with only a single SIP server up (the secondary), then brings a second SIP server up (the primary) 
// and because we've set enableDNSResetOnRegistrationRefresh=true the SDK switches over SIP registration to the primary SIP server.
// This test is currently disabled because there are asserts going off in this scenario.
TEST_F(BlfLocalTest, DISABLED_DnsSwitch)
{
   const int NetworkChangeDelayMs = 6000; // it looks like we need some time for things to settle after the DNS change

   ASSERT_TRUE(TestRuntimeEnvironment::instance()->checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   repro::ReproRunner* repro_7090 = runRepro("repro_auth2.config"); // Port 7090
   ASSERT_TRUE(repro_7090->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   BlfAttendantTestAccount alice("alice", Account_NoInit);
   BlfServerTestAccount bob("bob", Account_NoInit);

   configureAccountForNetworkSwitching(alice);
   configureAccountForNetworkSwitching(bob);

   alice.init();
   alice.account->setTFTimerValueMs(10000);

   bob.init();
   bob.account->setTFTimerValueMs(10000);

   ASSERT_TRUE(repro_7080->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7090->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   ASSERT_TRUE(repro_7080->getProxy()->getUserStore().addUser(bob.config.settings.username.c_str(),
      bob.config.settings.domain.c_str(), "cp.local",
      bob.config.settings.password.c_str(),
      true, "Bob", "<EMAIL>"));
   ASSERT_TRUE(repro_7090->getProxy()->getUserStore().addUser(bob.config.settings.username.c_str(),
      bob.config.settings.domain.c_str(), "cp.local",
      bob.config.settings.password.c_str(),
      true, "Bob", "<EMAIL>"));
   
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   delete repro_7080;

   alice.account->enable(alice.handle);
   bob.account->enable(bob.handle);
      
   {
      assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_TCP);
      assertOnClientAuthUntilEx(alice, 7090, 200, false);

      assertAccountStatusUptilRegisteredEx2(bob, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_TCP);
      assertOnClientAuthUntilEx(bob, 7090, 200, false);
   }

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipDialogEventSubscriptionHandle bobSubs = 0;
      {
         // Wait for a new subscription
         SipDialogEventSubscriptionHandle h;
         NewDialogEventSubscriptionEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onNewSubscription",
            5000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, bob.handle);
         bobSubs = h;
         ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
      }

      // Accept the subscription
      ASSERT_EQ(bob.dialogEventSubscriptionManager->accept(bobSubs), kSuccess);

      {
         // Wait for the subscription state to transition to Active
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionStateChanged",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      {
         SipEventState eventState = EventStateFromFile(bob.config.uri(), "BlfLocalTest_TestRLSSubscribe.txt");
         ASSERT_EQ(bob.subs->notify(bobSubs, eventState), kSuccess);
      }

      // Alice will force the switchover here
      std::this_thread::sleep_for(std::chrono::milliseconds(NetworkChangeDelayMs));

      assertOnDnsResetEx(bob, 7080, IpVersion_V4, SipAccountTransport_TCP);
      assertAccountRefreshingEx(bob);
      resip::Helper::setNonceHelper(NULL);

      // NOTE: There is no subscription ended event here since alice does not send an un-subscribe due to the network change.

      {
         // Wait for a new subscription
         SipDialogEventSubscriptionHandle h;
         NewDialogEventSubscriptionEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onNewSubscription",
            10000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, bob.handle);
         bobSubs = h;
         ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
      }

      // Accept the subscription
      ASSERT_EQ(bob.dialogEventSubscriptionManager->accept(bobSubs), kSuccess);

      {
         // Wait for the subscription state to transition to Active
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionStateChanged",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      {
         SipEventState eventState = EventStateFromFile(bob.config.uri(), "BlfLocalTest_TestRLSSubscribe.txt");
         ASSERT_EQ(bob.subs->notify(bobSubs, eventState), kSuccess);
      }

      {
         // Wait for the subscription state to transition to Terminated
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionEnded",
            25000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }
   });

   auto aliceEvents = std::async(std::launch::async, [&] () {
      cpc::string dialogId;
      SipBusyLampFieldRemoteLineHandle aliceRemoteLine = bob.config.uri();

      // Attendant subscribes to the remote line
      SipBusyLampFieldRemoteLineSetSettings settings;
      settings.expires = 3600;
      settings.resourceListAddress = bob.config.uri();
      SipBusyLampFieldRemoteLineSetHandle remoteLineSet = alice.busyLampFieldManager->createBusyLampFieldRemoteLineSet(alice.handle, settings);
      ASSERT_TRUE(remoteLineSet > 0);

      // Start the subscription
      assertSuccess(alice.busyLampFieldManager->start(remoteLineSet));

      // Wait for the new subscription notifications
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress1, LineName1);
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress2, LineName2);
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress3, LineName3);

      // Wait for the subscription state change notification
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress1, SipSubscriptionState_Active);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, SipSubscriptionState_Active);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, SipSubscriptionState_Active);

      // reset the subscription here
      repro_7080 = runRepro("repro_auth1.config"); // Port 7080
      ASSERT_TRUE(repro_7080->getProxy() != NULL);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      assertOnDnsResetEx(alice, 7080, IpVersion_V4, SipAccountTransport_TCP);
      assertAccountRefreshingEx(alice);
      resip::Helper::setNonceHelper(NULL);
      
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_TCP);
      assertOnClientAuthUntilEx(alice, 7080, 200, false);

      std::this_thread::sleep_for(std::chrono::milliseconds(NetworkChangeDelayMs));

      // Wait for the new subscription notifications, and we don't get the line names here
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress1, "");
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress2, "");
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress3, "");

      // Wait for the subscription state change notification
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress1, SipSubscriptionState_Active);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, SipSubscriptionState_Active);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, SipSubscriptionState_Active);

      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress1, DialogDirection_NotSpecified, DialogState_Terminated, LineAddress1, LineTelephone1, LineExt1, "", "", dialogId);
      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, DialogDirection_NotSpecified, DialogState_Terminated, LineAddress2, LineTelephone2, LineExt2, "", "", dialogId);
      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, DialogDirection_NotSpecified, DialogState_Terminated, LineAddress3, LineTelephone3, LineExt3, "", "", dialogId);

      // Terminate the subscription
      assertSuccess(alice.busyLampFieldManager->end(remoteLineSet));

      // Wait for the subscription termination notifications
      assertBLFRemoteLineSubscriptionEnded(__LINE__, alice, remoteLineSet, LineAddress1);
      assertBLFRemoteLineSubscriptionEnded(__LINE__, alice, remoteLineSet, LineAddress2);
      assertBLFRemoteLineSubscriptionEnded(__LINE__, alice, remoteLineSet, LineAddress3);
   });

   waitFor2(aliceEvents, bobEvents);
}

// SCORE-1306 Test network change handling for BLF to ensure the BLF subscriptions and states are restored.
// This test case starts with the WIFI and then switches to WWAN, forcing the network change handling to kick in.
TEST_F(BlfLocalTest, NetworkChange)
{
   BlfAttendantTestAccount alice("alice");
   BlfServerTestAccount bob("bob");

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipDialogEventSubscriptionHandle bobSubs = 0;
      {
         // Wait for a new subscription
         SipDialogEventSubscriptionHandle h;
         NewDialogEventSubscriptionEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onNewSubscription",
            5000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, bob.handle);
         bobSubs = h;
         ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
      }

      // Accept the subscription
      ASSERT_EQ(bob.dialogEventSubscriptionManager->accept(bobSubs), kSuccess);

      {
         // Wait for the subscription state to transition to Active
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionStateChanged",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      {
         SipEventState eventState = EventStateFromFile(bob.config.uri(), "BlfLocalTest_TestRLSSubscribe.txt");
         ASSERT_EQ(bob.subs->notify(bobSubs, eventState), kSuccess);
      }

      // Alice will experience a network change at this point

      // NOTE: There is no subscription ended event here since alice does not send an un-subscribe due to the network change.

      {
         // Wait for a new subscription
         SipDialogEventSubscriptionHandle h;
         NewDialogEventSubscriptionEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onNewSubscription",
            10000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, bob.handle);
         bobSubs = h;
         ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
      }

      // Accept the subscription
      ASSERT_EQ(bob.dialogEventSubscriptionManager->accept(bobSubs), kSuccess);

      {
         // Wait for the subscription state to transition to Active
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionStateChanged",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      {
         SipEventState eventState = EventStateFromFile(bob.config.uri(), "BlfLocalTest_TestRLSSubscribe.txt");
         ASSERT_EQ(bob.subs->notify(bobSubs, eventState), kSuccess);
      }

      {
         // Wait for the subscription state to transition to Terminated
         SipDialogEventSubscriptionHandle h;
         DialogEventSubscriptionEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionEnded",
            25000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }
   });

   auto aliceEvents = std::async(std::launch::async, [&] () {
      cpc::string dialogId;
      SipBusyLampFieldRemoteLineHandle aliceRemoteLine = bob.config.uri();

      // Attendant subscribes to the remote line
      SipBusyLampFieldRemoteLineSetSettings settings;
      settings.expires = 3600;
      settings.resourceListAddress = bob.config.uri();
      SipBusyLampFieldRemoteLineSetHandle remoteLineSet = alice.busyLampFieldManager->createBusyLampFieldRemoteLineSet(alice.handle, settings);
      ASSERT_TRUE(remoteLineSet > 0);

      // Start the subscription
      assertSuccess(alice.busyLampFieldManager->start(remoteLineSet));

      // Wait for the new subscription notifications
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress1, LineName1);
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress2, LineName2);
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress3, LineName3);

      // Wait for the subscription state change notification
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress1, SipSubscriptionState_Active);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, SipSubscriptionState_Active);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, SipSubscriptionState_Active);

      // Affect the network change for Alice
      NetworkTransport transport = TransportWWAN;
      alice.network->setNetworkTransport(transport);

      NetworkChangeEvent evt;
      NetworkChangeManager::NetworkChangeManagerHandle handle;
      ASSERT_TRUE(cpcExpectEvent(alice.networkChangeEvents, "NetworkChangeHandler::onNetworkChangeEx", 30000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
      ASSERT_EQ(evt.networkTransport, transport);

      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);

      // Wait for the new subscription notifications, and we don't get the line names here
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress1, "");
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress2, "");
      assertBLFRemoteLineNewSubscription(__LINE__, alice, remoteLineSet, LineAddress3, "");

      // Wait for the subscription state change notification
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress1, SipSubscriptionState_Active);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, SipSubscriptionState_Active);
      assertBLFRemoteLineSubscriptionStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, SipSubscriptionState_Active);

      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress1, DialogDirection_NotSpecified, DialogState_Terminated, LineAddress1, LineTelephone1, LineExt1, "", "", dialogId);
      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress2, DialogDirection_NotSpecified, DialogState_Terminated, LineAddress2, LineTelephone2, LineExt2, "", "", dialogId);
      assertBLFRemoteLineStateChanged(__LINE__, alice, remoteLineSet, LineAddress3, DialogDirection_NotSpecified, DialogState_Terminated, LineAddress3, LineTelephone3, LineExt3, "", "", dialogId);

      // Terminate the subscription
      assertSuccess(alice.busyLampFieldManager->end(remoteLineSet));

      // Wait for the subscription termination notifications
      assertBLFRemoteLineSubscriptionEnded(__LINE__, alice, remoteLineSet, LineAddress1);
      assertBLFRemoteLineSubscriptionEnded(__LINE__, alice, remoteLineSet, LineAddress2);
      assertBLFRemoteLineSubscriptionEnded(__LINE__, alice, remoteLineSet, LineAddress3);
   });

   waitFor2(aliceEvents, bobEvents);
}

void BlfLocalTest::assertPhoneError(int line, TestAccount& account, const cpc::string& errorText)
{
   PhoneErrorEvent evt;
   cpc::string module;
   ASSERT_TRUE(account.phoneEvents->expectEvent(line,
      "PhoneHandler::onError",
      15000, StrEqualsPred("SipAccountInterface"), module, evt));
   ASSERT_EQ(errorText, evt.errorText);
}

void BlfLocalTest::assertBLFError(int line, BlfAttendantTestAccount& account, SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const cpc::string& errorText)
{
   SipBusyLampFieldRemoteLineSetHandle h;
   SipBusyLampField::ErrorEvent evt;
   ASSERT_TRUE(account.busyLampFieldEvents->expectEvent(line,
      "SipBusyLampFieldHandler::onError",
      5000, AlwaysTruePred(), h, evt));
   ASSERT_EQ(remoteLineSet, h);
   ASSERT_EQ(errorText, evt.errorText);
}

void BlfLocalTest::assertBLFRemoteLineNewSubscription(int line, BlfAttendantTestAccount& account, SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const SipBusyLampFieldRemoteLineHandle& remoteLine, const cpc::string& remoteLineName)
{
   SipBusyLampFieldRemoteLineSetHandle h;
   RemoteLineNewSubscriptionEvent evt;
   ASSERT_TRUE(account.busyLampFieldEvents->expectEvent(line,
      "SipBusyLampFieldHandler::onRemoteLineNewSubscription",
      5000,
      AlwaysTruePred(), h, evt));
   ASSERT_EQ(remoteLineSet, h);
   ASSERT_EQ(remoteLine, evt.remoteLine);
   if (!cpc::string(remoteLineName).empty())
   {
      ASSERT_EQ(remoteLineName, evt.remoteLineName);
   }
}

void BlfLocalTest::assertBLFRemoteLineState(int line, BlfAttendantTestAccount& account, SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const SipBusyLampFieldRemoteLineHandle& remoteLine, bool subscriptionStarted, SipSubscriptionState subscriptionState, DialogState dialogState)
{
   SipBusyLampFieldRemoteLineState remoteLineState;
   assertSuccess(account.busyLampFieldStateManager->getState(remoteLineSet, remoteLine, remoteLineState));
   ASSERT_EQ(remoteLine, remoteLineState.remoteLine);
   ASSERT_EQ(subscriptionStarted, remoteLineState.subscriptionStarted);
   ASSERT_EQ(subscriptionState, remoteLineState.subscriptionState);
   if (dialogState != DialogState_NotSpecified)
   {
      ASSERT_EQ(remoteLineState.calls.size(), 1);
      ASSERT_EQ(dialogState, remoteLineState.calls[0].dialog.stateInfo.state);
   }
}

void BlfLocalTest::assertBLFRemoteLineStateChanged(int line, BlfAttendantTestAccount& account, SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const SipBusyLampFieldRemoteLineHandle& remoteLine, DialogDirection direction, DialogState dialogState, const cpc::string& localSipAddress, const cpc::string& localTelAddress, const cpc::string& localTelExt, const cpc::string& remoteAddress, const cpc::string& remoteName, cpc::string& dialogId)
{
   SipBusyLampFieldRemoteLineSetHandle h;
   RemoteLineStateChangedEvent evt;
   ASSERT_TRUE(account.busyLampFieldEvents->expectEvent(line,
      "SipBusyLampFieldHandler::onRemoteLineStateChanged",
      5000, AlwaysTruePred(), h, evt));
   ASSERT_EQ(remoteLineSet, h);
   ASSERT_EQ(remoteLine, evt.remoteLine);
 
   if (direction != DialogDirection_NotSpecified)
   {
      ASSERT_EQ(evt.calls.size(), 1);
      SipDialogEvent::DialogInfo dialog = evt.calls[0].dialog;
      dialogId = dialog.id;
      ASSERT_EQ(direction, dialog.direction);
      ASSERT_EQ(dialogState, dialog.stateInfo.state);
      ASSERT_EQ(0, dialog.stateInfo.code);
      ASSERT_EQ(localSipAddress, dialog.localParticipant.identities[0].address);
      ASSERT_EQ(localTelAddress, dialog.localParticipant.identities[1].address);
      ASSERT_EQ("ext", dialog.localParticipant.identities[1].uriParams[0].name);
      ASSERT_EQ(localTelExt, dialog.localParticipant.identities[1].uriParams[0].value);
      if (!cpc::string(remoteAddress).empty())
      {
         ASSERT_EQ(remoteAddress, dialog.remoteParticipant.identities[0].address);
      }
      if (!cpc::string(remoteName).empty())
      {
         ASSERT_EQ(remoteName, dialog.remoteParticipant.identities[0].displayName);
      }
   }
}

void BlfLocalTest::assertBLFRemoteLineSubscriptionStateChanged(int line, BlfAttendantTestAccount& account, SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const SipBusyLampFieldRemoteLineHandle& remoteLine, SipSubscriptionState subscriptionState)
{
   SipBusyLampFieldRemoteLineSetHandle h;
   RemoteLineSubscriptionStateChangedEvent evt;
   ASSERT_TRUE(account.busyLampFieldEvents->expectEvent(line,
      "SipBusyLampFieldHandler::onRemoteLineSubscriptionStateChanged",
      15000,
      AlwaysTruePred(), h, evt));
   ASSERT_EQ(remoteLineSet, h);
   ASSERT_EQ(remoteLine, evt.remoteLine);
   ASSERT_EQ(subscriptionState, evt.subscriptionState);
}

void BlfLocalTest::assertBLFRemoteLineSubscriptionEnded(int line, BlfAttendantTestAccount& account, SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const SipBusyLampFieldRemoteLineHandle& remoteLine)
{
   SipBusyLampFieldRemoteLineSetHandle h;
   RemoteLineSubscriptionEndedEvent evt;
   ASSERT_TRUE(account.busyLampFieldEvents->expectEvent(line,
      "SipBusyLampFieldHandler::onRemoteLineSubscriptionEnded",
      15000,
      AlwaysTruePred(), h, evt));
   ASSERT_EQ(remoteLineSet, h);
   ASSERT_EQ(remoteLine, evt.remoteLine);
   if (evt.endReason != SipSubscriptionEndReason_Unknown)  // DRL NOTE: In the RLS mode onDialogResourceListUpdated() case we're not parsing this.
      ASSERT_EQ(SipSubscriptionEndReason_ServerEnded, evt.endReason);
}

void BlfLocalTest::assertBLFRemoteLineEmptySubscriptions(int line, BlfAttendantTestAccount& account, SipBusyLampFieldRemoteLineSetHandle remoteLineSet)
{
   SipBusyLampFieldRemoteLineSetHandle h;
   RemoteLineEmptySubscriptionsEvent evt;
   ASSERT_TRUE(account.busyLampFieldEvents->expectEvent(line,
      "SipBusyLampFieldHandler::onRemoteLineEmptySubscriptions",
      15000,
      AlwaysTruePred(), h, evt));
}

void BlfLocalTest::waitForConversationStateChanged_(int line, TestAccount& account, SipConversationHandle conversation, ConversationState conversationState)
{
   while(true)
   {
      SipConversationHandle h;
      ConversationStateChangedEvent evt;
      ASSERT_TRUE(account.conversationEvents->waitForEvent(line,
            "SipConversationHandler::onConversationStateChanged",
            5000, HandleEqualsPred<SipConversationHandle>(conversation), h, evt));
      ASSERT_EQ(h, conversation);
      if (evt.conversationState == conversationState)
      {
         break;
      }
   }
}

}

#endif // CPCAPI2_BRAND_SIP_DIALOG_EVENT_MODULE
