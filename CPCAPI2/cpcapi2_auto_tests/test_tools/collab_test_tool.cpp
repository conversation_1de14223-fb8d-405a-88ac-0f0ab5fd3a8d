#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#if (CPCAPI2_BRAND_VCCS_MODULE == 1)

#include "../cpcapi2_test_fixture.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>

#include "../test_framework/bare_bones_sip_endpoint.h"
#include "../test_framework/cpcapi2_test_framework.h"
#include "../vccs_test_harness/VccsTestHarness.h"
#include "test_account_events.h"
#include "test_call_events.h"
#include "../test_framework/xmpp_test_helper.h"
#include <vccs/VccsAccountManagerInterface.h>
#include "../test_framework/xmpp_test_helper.h"
#include "impl/call/SipConversationManagerInternal.h"
#include "../experimental/audio_ext/AudioExt.h"
#include <cpcstl/string.h>
#include <string>
#include <thread>
#include <future>
#include <memory>
#include <boost/algorithm/string.hpp>


using namespace CPCAPI2;
using namespace CPCAPI2::VCCS;
using namespace CPCAPI2::test;

class CollabTestTool : public CpcapiAutoTest
{
public:
   CollabTestTool() {}
   virtual ~CollabTestTool() {}
};
class MyConfigParse : public resip::ConfigParse
{
private:
   void parseCommandLine(int argc, char** argv, int skipCount = 0) {}
   void printHelpText(int argc, char **argv) {}
};

struct CollabTestToolConfig
{
   cpc::vector<Account::VccsAccountSettings> vccsSettings;
   cpc::string vccsURL;
   int callDurationMilliSec;
   cpc::string wavFile;
   cpc::string xmppProxy;
   int waitTimeForConferenceUpdateMilliSec;
   bool skipXmppTests;
   SipConversation::MediaDirection videoMediaDirection;
   bool skipParticipantCallStatusCheck;
   cpc::string appId;
   
   CollabTestToolConfig()
   {
      cpc::string vccsURL = "";
      int callDurationMilliSec = 10000;
      cpc::vector<Account::VccsAccountSettings> vccsAccountSettings;
      cpc::string wavFile;
      cpc::string xmppProxy;
      bool skipXmppTests = false;
      int waitTimeForConferenceUpdateMilliSec = 20000;
      videoMediaDirection = SipConversation::MediaDirection_SendReceive;
      bool skipParticipantCallStatusCheck = false;
   }
};

#define STATUS_LOG_FMT(x) "[[OK[" << x << "]]]"
#define FAIL_LOG_FMT(x) "[[NOK[" << x << "]]]"

void parseConfigfile(TestAccount& alice, CollabTestToolConfig& outConfig);
void setVccsAccount(TestAccount& alice, const Account::VccsAccountSettings& vccsAccountSettings);
void subscribeConference(TestAccount& alice, CollabTestToolConfig& config,
                         Conference::ConferenceDetails& outDetails,
                         Conference::VccsConferenceHandle& outConference,
                         cpc::vector<CPCAPI2::SipAccount::SipAccountSettings>& outAccounts,
                         cpc::vector<CPCAPI2::SipConversationSettings>& outConvs);
void setupSipAccount(TestAccount& alice,
                     const CPCAPI2::SipAccount::SipAccountSettings& accountSetting);
void collabCall(TestAccount& alice, cpc::string confUri, Conference::VccsConferenceHandle hConference,
                int callDurationMilliSec, CPCAPI2::SipConversation::SipConversationHandle& aliceCall, const CollabTestToolConfig& config);
void collabCallEnd(TestAccount& alice, Conference::VccsConferenceHandle hConference,
                   const CPCAPI2::SipConversation::SipConversationHandle& aliceCall, const CollabTestToolConfig& config, const Conference::ConferenceDetails& confDetails);
void setupXmppAccount(XmppTestAccount& test, const TestAccount& alice, const cpc::string& xmppProxy, Conference::VccsConferenceHandle hConference, cpc::string& outChatRoomJid);
void joinChatRoom(XmppTestAccount& test, cpc::string chatRoomJid,
                  XmppMultiUserChat::XmppMultiUserChatHandle& h);
void testAudioVideoCollabCall(TestAccount& alice, Conference::VccsConferenceHandle hConference, const CPCAPI2::SipAccount::SipAccountSettings& sipAccountSettings,
                              const CPCAPI2::SipConversationSettings& sipConvSettings, const Conference::ConferenceDetails& details, const CollabTestToolConfig& config);
void testGroupChat(TestAccount& alice, XmppTestAccount& test, const cpc::string& xmppProxy, Conference::VccsConferenceHandle hConference);

void updateGenericSetting(const std::string& settingCommandLineName, cpc::string& outSettingValue, const std::vector<std::string>& commandLineArgs)
{
   for (std::vector<std::string>::const_iterator it = commandLineArgs.begin(); it != commandLineArgs.end(); ++it)
   {
      const std::string cmd = *it;
      
      std::vector<std::string> split;
      boost::split(split, cmd, boost::is_any_of("="));
      
      if (split.size() == 2)
      {
         if (split[0] == settingCommandLineName)
         {
            outSettingValue = split[1].c_str();
         }
      }
   }
}

bool hasUsageFlag(const std::vector<std::string>& commandLineArgs)
{
   for (std::vector<std::string>::const_iterator it = commandLineArgs.begin(); it != commandLineArgs.end(); ++it)
   {
      if (*it == "--usage")
      {
         return true;
      }
   }
   return false;
}

void printUsage()
{
   safeCout("Usage:" << std::endl);
   safeCout("collab_test_tool [--%idx%] [--videoMediaDirection]");
   safeCout(std::endl);
   safeCout("--%idx%   Set value of %idx% macro; %idx% can be used in settings file to be replaced at runtime");
   safeCout("--videoMediaDirection  Set direction for video. Value can be one of: None, Inactive, SendOnly, ReceiveOnly, SendRecieve")
   safeCout(std::endl);
}

// --%idx%=whatever
bool getIdxMacroVal(const std::vector<std::string>& commandLineArgs, std::string& outIdxMacroVal)
{
   bool found = false;
   for (std::vector<std::string>::const_iterator it = commandLineArgs.begin(); it != commandLineArgs.end(); ++it)
   {
      const std::string cmd = *it;
      
      std::vector<std::string> split;
      boost::split(split, cmd, boost::is_any_of("="));
      
      if (split.size() == 2)
      {
         if (split[0] == "--%idx%")
         {
            found = true;
            outIdxMacroVal = split[1].c_str();
            break;
         }
      }
   }
   
   return found;
}

void cpcStringReplaceAll(cpc::string& source, const std::string& searchString, const std::string& replaceString)
{
   std::string sourceCopy = source.c_str();
   boost::replace_all(sourceCopy, searchString, replaceString);
   source = sourceCopy.c_str();
}

void idxMacroReplace(cpc::vector<Account::VccsAccountSettings>& replaceTarget, const std::string& idxMacroVal)
{
   for (cpc::vector<Account::VccsAccountSettings>::iterator it = replaceTarget.begin(); it != replaceTarget.end(); ++it)
   {
      cpcStringReplaceAll(it->displayName, "%idx%", idxMacroVal);
      cpcStringReplaceAll(it->group, "%idx%", idxMacroVal);
      cpcStringReplaceAll(it->password, "%idx%", idxMacroVal);
      cpcStringReplaceAll(it->displayName, "%idx%", idxMacroVal);
      cpcStringReplaceAll(it->password, "%idx%", idxMacroVal);
      cpcStringReplaceAll(it->xmppUserName, "%idx%", idxMacroVal);
   }
}

void idxMacroReplace(std::string& replaceTarget, const std::string& idxMacroVal)
{
   boost::replace_all(replaceTarget, "%idx%", idxMacroVal);
}

void idxMacroReplace(resip::Data& replaceTarget, const std::string& idxMacroVal)
{
   replaceTarget.replace("%idx%", idxMacroVal.c_str());
}


TEST_F(CollabTestTool, testCollabCallAndGroupChat) {

   if (hasUsageFlag(TestEnvironmentConfig::commandLineArgs()))
   {
      printUsage();
      return;
   }

   TestAccount alice("alice", Account_Init);
   
   CollabTestToolConfig collabTestToolConfig;
   ASSERT_NO_FATAL_FAILURE(parseConfigfile(alice, collabTestToolConfig));
   // override settings from config file with command line params
   
   ASSERT_NE(collabTestToolConfig.vccsSettings.size(), 0) << FAIL_LOG_FMT("returned vccs account settings is empty");
   ASSERT_NO_FATAL_FAILURE(setVccsAccount(alice, collabTestToolConfig.vccsSettings[0]));
   
   Conference::ConferenceDetails details;
   Conference::VccsConferenceHandle hConference( -1 );

   cpc::vector<CPCAPI2::SipAccount::SipAccountSettings> accounts;
   cpc::vector<CPCAPI2::SipConversationSettings> convs;
      
   ASSERT_NO_FATAL_FAILURE(subscribeConference(alice, collabTestToolConfig, details, hConference, accounts, convs));
   if (!collabTestToolConfig.skipXmppTests)
   {
      XmppTestAccount xmppTestAccount("Test XMPP account", Account_NoInit, "testXmppUser" /* username should be overridden later */);
      ASSERT_NO_FATAL_FAILURE(testGroupChat(alice, xmppTestAccount, collabTestToolConfig.xmppProxy, hConference));
   }
   ASSERT_NO_FATAL_FAILURE(testAudioVideoCollabCall(alice, hConference, accounts[0], convs[0], details, collabTestToolConfig));
}

void testAudioVideoCollabCall(TestAccount& alice, Conference::VccsConferenceHandle hConference, const CPCAPI2::SipAccount::SipAccountSettings& sipAccountSettings,
                              const CPCAPI2::SipConversationSettings& sipConvSettings, const Conference::ConferenceDetails& details, const CollabTestToolConfig& config)
{
   
   ASSERT_NO_FATAL_FAILURE(setupSipAccount(alice, sipAccountSettings));
   ASSERT_EQ(kSuccess, alice.conversation->setDefaultSettings(alice.handle, sipConvSettings));
   
   Media::AudioExt* audioExt = Media::AudioExt::getInterface(alice.media);
   audioExt->setAudioDeviceFile("silence16.pcm", "");
   
   alice.audio->setEchoCancellationMode(CPCAPI2::Media::AudioDeviceRole_None, CPCAPI2::Media::EchoCancellationMode_None);

   Media::MediaStackSettings mediaStackSettings;
   mediaStackSettings.audioLayer = Media::AudioLayers_File;
   alice.media->updateMediaSettings(mediaStackSettings);
  
   cpc::string confUri("sip:");
   confUri.append(details.lobbySipAddress);
   safeCout("conference uri is: " << confUri);
   CPCAPI2::SipConversation::SipConversationHandle aliceCall;
   ASSERT_NO_FATAL_FAILURE(collabCall(alice, confUri, hConference, config.callDurationMilliSec, aliceCall, config));
   ASSERT_NO_FATAL_FAILURE(collabCallEnd(alice, hConference, aliceCall, config, details));
}

void testGroupChat(TestAccount& alice, XmppTestAccount& test, const cpc::string& xmppProxy, Conference::VccsConferenceHandle hConference)
{
   cpc::string chatRoomJid;
   ASSERT_NO_FATAL_FAILURE(setupXmppAccount(test, alice, xmppProxy, hConference, chatRoomJid));
   safeCout(STATUS_LOG_FMT("xmpp account registered successfully " << chatRoomJid));
   XmppMultiUserChat::XmppMultiUserChatHandle h;
   ASSERT_NO_FATAL_FAILURE(joinChatRoom(test, chatRoomJid, h));
   int ret = test.mucManager->sendMessage(h, "collab test : this message is sent from the host itself", "");
   safeCout("sending messages to collab group chat" << ret);

   {
      XmppMultiUserChat::XmppMultiUserChatHandle handle;
      XmppMultiUserChat::MultiUserChatNewMessageEvent event;
      
      ASSERT_TRUE(cpcExpectEvent(test.events, "XmppMultiUserChatHandler::onMultiUserChatNewMessage", 5000, CPCAPI2::test::AlwaysTruePred(), handle, event));
      ASSERT_EQ(h, handle);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_EQ(event.nickname, test.config.nick);
      ASSERT_EQ(event.plain, "collab test : this message is sent from the host itself");
   }
}

void parseConfigfile(TestAccount& alice, CollabTestToolConfig& outConfig)
{
   MyConfigParse configParse;
   std::string configFileName = "cpcapi2_collab_test_tool.config";
   ASSERT_TRUE(TestEnvironmentConfig::getResourceFile(configFileName, configFileName));
   resip::Data configFile = configFileName.c_str();
   configParse.parseConfig(0, NULL, configFile.c_str());
   
   std::string idxMacroVal;
   bool doIdxMacroReplace = getIdxMacroVal(TestEnvironmentConfig::commandLineArgs(), idxMacroVal);
   
   resip::Data accountSettingsFile;
   resip::Data collabLink;
   resip::Data wavFilePath;
   resip::Data xmppProxy;
   if (configParse.getConfigValue("accountSettingsFile", accountSettingsFile))
   {
      if (doIdxMacroReplace)
      {
         idxMacroReplace(accountSettingsFile, idxMacroVal);
      }
   
      std::string settingsFileName = accountSettingsFile.c_str();
      ASSERT_TRUE(TestEnvironmentConfig::getResourceFile(settingsFileName, settingsFileName));
      resip::Data settingsFile = settingsFileName.c_str();
      std::ifstream in(settingsFile.c_str());
      assert(in.is_open());
      
      std::ostringstream iss;
      iss << in.rdbuf() << std::flush;
      
      cpc::string accountDoc = iss.str().c_str();
      if (doIdxMacroReplace)
      {
         cpcStringReplaceAll(accountDoc, "%idx%", idxMacroVal);
      }
      
      ASSERT_EQ(kSuccess, alice.vccsAccountManager->decodeProvisioningResponse(accountDoc, outConfig.vccsSettings)) << FAIL_LOG_FMT("account provisioning file format is invalid");
   }
   if (configParse.getConfigValue("collabLink", collabLink))
   {
      if (doIdxMacroReplace)
      {
         idxMacroReplace(collabLink, idxMacroVal);
      }
   
      outConfig.vccsURL = collabLink.c_str();
   }

   configParse.getConfigValue("callDurationMilliSec", outConfig.callDurationMilliSec);

   if (configParse.getConfigValue("wavFilePath", wavFilePath))
   {
      outConfig.wavFile = wavFilePath.c_str();
      
      if (doIdxMacroReplace)
      {
         idxMacroReplace(wavFilePath, idxMacroVal);
      }
   }
   
   if (configParse.getConfigValue("xmppProxy", xmppProxy))
   {
      cpc::string proxy = xmppProxy.c_str();
      if (doIdxMacroReplace)
      {
         idxMacroReplace(xmppProxy, idxMacroVal);
      }
      if (proxy.size() != 0)
      {
         outConfig.xmppProxy = proxy;
         safeCout(STATUS_LOG_FMT(" using xmpp proxy: " << outConfig.xmppProxy ));
      }
      else
         safeCout("No xmpp proxy found in the config file, will be using the proxy value return from server");
   }
   else
      safeCout("No xmpp proxy option found in the config file, will be using the proxy value return from server");
   
   configParse.getConfigValue("waitTimeForConferenceUpdateMilliSec", outConfig.waitTimeForConferenceUpdateMilliSec);

   outConfig.skipXmppTests = false;
   if (configParse.getConfigValue("skipXmppTests", outConfig.skipXmppTests))
   {
      if (outConfig.skipXmppTests)
      {
         safeCout("skipXmppTests specified and true; skipping XMPP tests!");
      }
   }
   
   outConfig.videoMediaDirection = SipConversation::MediaDirection_SendReceive;
   resip::Data videoMediaDirectionData;
   configParse.getConfigValue("videoMediaDirection", videoMediaDirectionData);
   cpc::string videoMediaDirectionStr = videoMediaDirectionData.c_str();
   // allow override via command line
   updateGenericSetting("--videoMediaDirection", videoMediaDirectionStr, TestEnvironmentConfig::commandLineArgs());
   if (videoMediaDirectionStr == "None")
   {
      outConfig.videoMediaDirection = SipConversation::MediaDirection_None;
   }
   else if (videoMediaDirectionStr == "Inactive")
   {
      outConfig.videoMediaDirection = SipConversation::MediaDirection_Inactive;
   }
   else if (videoMediaDirectionStr == "SendOnly")
   {
      outConfig.videoMediaDirection = SipConversation::MediaDirection_SendOnly;
   }
   else if (videoMediaDirectionStr == "ReceiveOnly")
   {
      outConfig.videoMediaDirection = SipConversation::MediaDirection_ReceiveOnly;
   }
   else if (videoMediaDirectionStr == "SendReceive")
   {
      outConfig.videoMediaDirection = SipConversation::MediaDirection_SendReceive;
   }
   
   resip::Data appId;
   configParse.getConfigValue("appId", appId);
   if (doIdxMacroReplace)
   {
      idxMacroReplace(appId, idxMacroVal);
   }
   outConfig.appId = appId.c_str();

   bool skipPartCheck;
   configParse.getConfigValue("skipParticipantCallStatusCheck", skipPartCheck);
   outConfig.skipParticipantCallStatusCheck = skipPartCheck;

   safeCout(STATUS_LOG_FMT("Done parsing config files"));
}

void setVccsAccount(TestAccount& alice, const Account::VccsAccountSettings& vccsAccountsettings)
{
   int result = alice.vccsAccountManager->configureDefaultAccountSettings( alice.vccsAccountHandle, vccsAccountsettings);
   ASSERT_EQ(kSuccess, result) << FAIL_LOG_FMT("Config account settigns failed");
   alice.vccsAccountManager->applySettings( alice.vccsAccountHandle ); // prob. not needed


   result = alice.vccsAccountManager->enable( alice.vccsAccountHandle );
   ASSERT_EQ(kSuccess, result) << FAIL_LOG_FMT("Failed to enable account");

   // Wait for the account to reach the registered state
   {
      auto aliceRegistered = std::async(std::launch::async, [&] () {
         Account::VccsAccountStateChangedEvent evt;
         Account::VccsAccountHandle h;
         ASSERT_TRUE( cpcWaitForEvent(
                                      alice.vccsEvents,
                                      "VccsAccountHandler::onAccountStateChanged",
                                      15000,
                                      AlwaysTruePred( ),
                                      h, evt ) );
         ASSERT_TRUE( evt.oldState == Account::VccsAccountState_Unregistered ) << FAIL_LOG_FMT("account state is wrong, expect: Unregistered");
         ASSERT_TRUE( evt.newState == Account::VccsAccountState_Registering ) << FAIL_LOG_FMT("account state failed to change from Unregistered to Registering");
         safeCout(STATUS_LOG_FMT("Account is registering"));
         
         ASSERT_TRUE( cpcWaitForEvent(
                                      alice.vccsEvents,
                                      "VccsAccountHandler::onAccountStateChanged",
                                      15000,
                                      AlwaysTruePred( ),
                                      h, evt ) );
         ASSERT_TRUE( evt.oldState == Account::VccsAccountState_Registering )<< FAIL_LOG_FMT("account state is wrong, expect: Registering");
         ASSERT_TRUE( evt.newState == Account::VccsAccountState_Registered ) << FAIL_LOG_FMT("account state failed to change from Registering to Registered");
      });
      waitFor( aliceRegistered );
   }
   
   if (::testing::Test::HasFatalFailure())
   {
      FAIL() << FAIL_LOG_FMT("Account registration failed.");
   }
   safeCout(STATUS_LOG_FMT("VCCS account seems registered okay"));
}

void subscribeConference(TestAccount& alice, CollabTestToolConfig& config,
                         Conference::ConferenceDetails& outDetails,
                         Conference::VccsConferenceHandle& outConference,
                         cpc::vector<CPCAPI2::SipAccount::SipAccountSettings>& outAccounts,
                         cpc::vector<CPCAPI2::SipConversationSettings>& outConvs)
{
   int result;
   
   cpc::string outWebSocketURL;
   cpc::string outServerName;
   int outPortNumber;
   cpc::string outGroupName;
   cpc::string outSubscriptionCode;
   result = alice.vccsAccountManager->crackVCCSURL(config.vccsURL, false, outWebSocketURL, outServerName, outPortNumber, outGroupName, outSubscriptionCode);
   ASSERT_TRUE(result) << FAIL_LOG_FMT("crack VCCS URL failure");
   //ASSERT_EQ(outGroupName, "sdktesttool.com");
   //ASSERT_EQ(outSubscriptionCode, "IZDXMUPMBT");
   //ASSERT_EQ(outPortNumber, 443);
   
   
   // Subscribe to the conference from which we want events
   VCCS::Conference::SubscriptionInfo info;
   info.conferenceCode = outSubscriptionCode;
   info.applicationID = config.appId;
   result = alice.vccsConferenceManager->subscribe( alice.vccsAccountHandle, info);
   ASSERT_EQ(kSuccess, result) << FAIL_LOG_FMT("Failed to subscribe to the conference");
   
   auto aliceSubscribe = std::async(std::launch::async, [&] () {
      Conference::SubscribeEvent evt;
      Account::VccsAccountHandle h;
      ASSERT_TRUE( cpcWaitForEvent(
                                   alice.vccsEvents,
                                   "VccsConferenceHandler::onSubscribe",
                                   15000,
                                   AlwaysTruePred( ),
                                   h, evt ) ) << FAIL_LOG_FMT("failed to subscribe to the conference ");
      
      ASSERT_TRUE(evt.hConference > 0);
      outConference = evt.hConference;
      //ASSERT_EQ(evt.directSIPAddress, "<EMAIL>") << FAIL_LOG_FMT("Direct sip address is wrong");
      ASSERT_EQ(alice.account->decodeProvisioningResponse(evt.directSIPProvisioning, outAccounts), kSuccess) << FAIL_LOG_FMT("sip account provisioning file format is invalid");
      ASSERT_NE(outAccounts.size(), 0) << FAIL_LOG_FMT("after subscribing to the conference, returned sip account settings is empty");
      ASSERT_EQ(alice.conversation->decodeProvisioningResponse(evt.directSIPProvisioning, outConvs), kSuccess) << FAIL_LOG_FMT("conversation provisioning file format is invalid");
      ASSERT_NE(outConvs.size(), 0) << FAIL_LOG_FMT("after subscribing to the conference, returned conversation settings is empty");
   });
   waitFor( aliceSubscribe );
   if (::testing::Test::HasFatalFailure())
   {
      FAIL() << FAIL_LOG_FMT("failed to subscribe to the conference ");
   }
   
   auto aliceConferenceUpdate = std::async(std::launch::async, [&] () {
      Conference::ConferenceListUpdatedEvent evt;
      Account::VccsAccountHandle h;
      ASSERT_TRUE( cpcWaitForEvent(
                                   alice.vccsEvents,
                                   "VccsConferenceHandler::onConferenceListUpdated",
                                   10000,
                                   AlwaysTruePred( ),
                                   h, evt ) ) << FAIL_LOG_FMT("missed Conference_Updated after subscription ");
      ASSERT_TRUE( evt.changes[0].conference.bridgeNumber.size() > 0 ) << FAIL_LOG_FMT("Bridge number is wrong");
      ASSERT_GE(evt.changes[0].conference.lobbySipAddress.size(), 0) << FAIL_LOG_FMT("SIP address to dial into the bridge is empty or not present (lobbySipAddress)");
      ASSERT_TRUE( evt.changes[0].conference.id != -1 );
      ASSERT_TRUE(evt.changes.size() != 0) << FAIL_LOG_FMT("no conference info");
      outDetails = evt.changes[0].conference;
   });
   waitFor(aliceConferenceUpdate);
   if (::testing::Test::HasFatalFailure())
   {
      FAIL() << FAIL_LOG_FMT("failure in ConferenceUpdate after subscribing to the conference");
   }
   
   auto aliceParticipantupdate = std::async(std::launch::async, [&] () {
      Conference::ParticipantListUpdatedEvent evt;
      Conference::VccsConferenceHandle h;
      ASSERT_TRUE( cpcWaitForEvent(
                                   alice.vccsEvents,
                                   "VccsConferenceHandler::onParticipantListUpdated",
                                   15000,
                                   AlwaysTruePred( ),
                                   h, evt ) ) << FAIL_LOG_FMT("missed Participant_Updated after subscription OR Participant_Updated was sent before conference_Updated and was ignored, the order should be conference_Updated then Participant_Updated");
      //   ASSERT_TRUE(evt.changes[0].participant.isModerator);
      ASSERT_TRUE(evt.changes.size() != 0) << FAIL_LOG_FMT("no participant info");
      
      if (!config.skipParticipantCallStatusCheck)
      {
         ASSERT_EQ(evt.changes[0].participant.callStatus, 4) << FAIL_LOG_FMT("participant callStatus should be NONE");
      }
   });
   waitFor(aliceParticipantupdate);
   if (::testing::Test::HasFatalFailure())
   {
      FAIL() << FAIL_LOG_FMT("failure in participantUpated after subscribing to the conference");
   }
   safeCout(STATUS_LOG_FMT("subscribed to the conference"));
}

void setupSipAccount(TestAccount& alice, const CPCAPI2::SipAccount::SipAccountSettings& accountSettings)
{
   alice.config.settings = accountSettings;
   
   alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings);
   alice.account->applySettings(alice.handle);
   alice.enable(false);
   ASSERT_NO_FATAL_FAILURE(assertAccountRegisteringEx(alice)) << FAIL_LOG_FMT("sip account account registration failure");
   ASSERT_NO_FATAL_FAILURE(assertAccountRegisteredEx(alice)) << FAIL_LOG_FMT("sip account registration failure");
   safeCout(STATUS_LOG_FMT("sip account seems registered okay"));
}

void collabCall(TestAccount& alice, cpc::string confUri, Conference::VccsConferenceHandle hConference,
                int callDurationMilliSec, CPCAPI2::SipConversation::SipConversationHandle& aliceCall, const CollabTestToolConfig& config)
{
   aliceCall = alice.conversation->createConversation(alice.handle);
   CPCAPI2::SipConversation::MediaInfo aliceVideoMedia;
   aliceVideoMedia.mediaDirection = config.videoMediaDirection;
   aliceVideoMedia.mediaType = CPCAPI2::SipConversation::MediaType_Video;
   aliceVideoMedia.mediaEncryptionOptions.mediaEncryptionMode = CPCAPI2::SipConversation::MediaEncryptionMode_SRTP_SDES_Encrypted;
   aliceVideoMedia.mediaEncryptionOptions.secureMediaRequired = true;
   
   CPCAPI2::SipConversation::MediaInfo aliceAudioMedia;
   aliceAudioMedia.mediaDirection = CPCAPI2::SipConversation::MediaDirection_SendReceive;
   aliceAudioMedia.mediaType = CPCAPI2::SipConversation::MediaType_Audio;
   aliceAudioMedia.mediaEncryptionOptions.mediaEncryptionMode = CPCAPI2::SipConversation::MediaEncryptionMode_SRTP_SDES_Encrypted;
   aliceAudioMedia.mediaEncryptionOptions.secureMediaRequired = true;
   
   alice.video->setCaptureDevice(CPCAPI2::Media::kCustomVideoSourceDeviceId);
   int ret = alice.video->startCapture();
   ASSERT_EQ(ret, kSuccess) << FAIL_LOG_FMT("failed to start capture with custom device");
   
   ret = alice.conversation->addParticipant(aliceCall, confUri);
   ASSERT_EQ(kSuccess, ret) << FAIL_LOG_FMT("Failed to add participant");
   safeCout(STATUS_LOG_FMT("call in bridge"));

   ret = alice.conversation->configureMedia(aliceCall, aliceAudioMedia);
   ret = alice.conversation->configureMedia(aliceCall, aliceVideoMedia);
   ASSERT_EQ(ret, kSuccess) << "confgiure alice media failed";
   
   ret = alice.conversation->start(aliceCall);
   ASSERT_EQ(kSuccess, ret) << FAIL_LOG_FMT("Failed to start a conversation");
   safeCout(STATUS_LOG_FMT("making an audio/video call..."));

   ASSERT_NO_FATAL_FAILURE(assertConversationStateChanged(alice, aliceCall, CPCAPI2::SipConversation::ConversationState_Connected)) << FAIL_LOG_FMT("conversation not connected");
   safeCout(STATUS_LOG_FMT("conversation connected..."));
   ASSERT_EQ(kSuccess, alice.conversation->playSound(aliceCall, config.wavFile, true)) << FAIL_LOG_FMT("play sound failed");
   safeCout(STATUS_LOG_FMT("playing audio....."));
   std::this_thread::sleep_for(std::chrono::milliseconds(callDurationMilliSec));
   
   // check there is participant in the conference, and particpant status is in_conference
   auto aliceParticipantStatus = std::async(std::launch::async, [&] () {
      Conference::ParticipantListUpdatedEvent evt;
      Conference::VccsConferenceHandle h;
      ASSERT_TRUE( cpcWaitForEvent(
                                   alice.vccsEvents,
                                   "VccsConferenceHandler::onParticipantListUpdated",
                                   15000,
                                   AlwaysTruePred( ),
                                   h, evt ) ) << FAIL_LOG_FMT("missed Participant_Updated after call in the bridge");
      ASSERT_TRUE(evt.changes.size() != 0) << FAIL_LOG_FMT("no participant info");
      
      if (!config.skipParticipantCallStatusCheck)
      {
         ASSERT_EQ(evt.changes[0].participant.callStatus, 0) << FAIL_LOG_FMT("participant callStatus should be IN_CONFERENCE");
      }
      safeCout(STATUS_LOG_FMT("participant " << evt.changes[0].participant.sipUsername << " call status is IN_CONFERENCE"));
   });
   waitFor(aliceParticipantStatus);
   if (::testing::Test::HasFatalFailure())
   {
      FAIL() << FAIL_LOG_FMT("failed to join the conference");
   }
   
   CPCAPI2::SipConversation::SipConversationHandle h;
   CPCAPI2::SipConversation::ConversationStatisticsUpdatedEvent evt;
   CPCAPI2::SipConversation::SipConversationManagerInternal* convInternal = dynamic_cast<CPCAPI2::SipConversation::SipConversationManagerInternal*>(alice.conversation);
   
   convInternal->refreshConversationStatistics(aliceCall, true, true, true, true);
   
   ASSERT_TRUE(alice.conversationEvents->expectEvent("SipConversationHandler::onConversationStatisticsUpdated", 15000, HandleEqualsPred<CPCAPI2::SipConversation::SipConversationHandle>(aliceCall), h, evt)) << "missed updated conversation statistics event";
   {
      ASSERT_EQ(1, evt.conversationStatistics.audioChannels.size()) << FAIL_LOG_FMT("Audio channel must be at least 1");
      ASSERT_NE(evt.conversationStatistics.audioChannels[0].streamDataCounters.packetsReceived, 0) << FAIL_LOG_FMT("Audio - Packets received is 0");
      ASSERT_NE(evt.conversationStatistics.audioChannels[0].streamDataCounters.packetsSent, 0) << FAIL_LOG_FMT("Audio - Packets sent is 0");
      ASSERT_EQ(1, evt.conversationStatistics.videoChannels.size()) << FAIL_LOG_FMT("video channel must be at least 1");
      ASSERT_NE(evt.conversationStatistics.videoChannels[0].streamDataCounters.packetsReceived, 0) << FAIL_LOG_FMT("Video - Packets received is 0");
      ASSERT_NE(evt.conversationStatistics.videoChannels[0].streamDataCounters.packetsSent, 0) << FAIL_LOG_FMT("Video - Packets sent is 0");
   }
   
}

void collabCallEnd(TestAccount& alice, Conference::VccsConferenceHandle hConference,
                   const CPCAPI2::SipConversation::SipConversationHandle& aliceCall,
                   const CollabTestToolConfig& config,
                   const Conference::ConferenceDetails& confDetails)
{
   safeCout(STATUS_LOG_FMT("going to end the call-------------"));
   int result = alice.conversation->end(aliceCall);
   ASSERT_EQ(kSuccess, result) << FAIL_LOG_FMT("Failed to end the conversation");
   ASSERT_NO_FATAL_FAILURE(assertConversationEnded(alice, aliceCall, CPCAPI2::SipConversation::ConversationEndReason_UserTerminatedLocally)) << FAIL_LOG_FMT("first account outgoing call end signal failure");
   safeCout(STATUS_LOG_FMT("conversation  ends"));
   
   if (!config.skipParticipantCallStatusCheck)
   {
      Conference::ConferenceListUpdatedEvent evt;
      auto aliceConferenceUpdate = std::async(std::launch::async, [&] () {
         
         Account::VccsAccountHandle h;
         ASSERT_TRUE( cpcWaitForEvent(
                                      alice.vccsEvents,
                                      "VccsConferenceHandler::onConferenceListUpdated",
                                      config.waitTimeForConferenceUpdateMilliSec,
                                      AlwaysTruePred( ),
                                      h, evt ) ) << FAIL_LOG_FMT("missed Conference_Updated after collab call ended ");
         
         ASSERT_TRUE(evt.changes.size() != 0) << FAIL_LOG_FMT("no conference info");
         ASSERT_FALSE(evt.changes[0].conference.isConferenceLive) << FAIL_LOG_FMT("conference is still active");
      });
      waitFor(aliceConferenceUpdate);
      if (::testing::Test::HasFatalFailure())
      {
         FAIL() << FAIL_LOG_FMT("failed to end the conference");
      }
   }
   alice.vccsConferenceManager->unsubscribe( alice.vccsAccountHandle, confDetails.bridgeNumber );
   auto aliceUnsubscribe = std::async(std::launch::async, [&] () {
      Conference::UnsubscribeEvent evt;
      Account::VccsAccountHandle h;
      ASSERT_TRUE( cpcWaitForEvent(
                                   alice.vccsEvents,
                                   "VccsConferenceHandler::onUnsubscribe",
                                   15000,
                                   AlwaysTruePred( ),
                                   h, evt ) );
   });
   waitFor( aliceUnsubscribe );
   if (::testing::Test::HasFatalFailure())
   {
      FAIL() << FAIL_LOG_FMT("failed to unsubscribe the conference");
   }
   safeCout(STATUS_LOG_FMT("unsubscribed to the conference"));
   alice.vccsConferenceManager->queryConferenceDetails(alice.vccsAccountHandle, hConference);
   safeCout(STATUS_LOG_FMT("conference ended"));
}

void setupXmppAccount(XmppTestAccount& test, const TestAccount& alice, const cpc::string& xmppProxy, Conference::VccsConferenceHandle hConference, cpc::string& outChatRoomJid)
{
   // get chatRoomJid
   Conference::XMPPAccountInfoEvent evt;
   alice.vccsConferenceManager->getXMPPAccountInfo( alice.vccsAccountHandle, hConference, false );
   {
      Conference::VccsConferenceHandle h;
      auto aliceAccountInfo = std::async(std::launch::async, [&] () {
      ASSERT_TRUE( cpcWaitForEvent(
                                   alice.vccsEvents,
                                   "VccsConferenceHandler::onXMPPAccountInfo",
                                   15000,
                                   AlwaysTruePred( ),
                                   h, evt ) );
      });
      waitFor( aliceAccountInfo );
   }
   if (::testing::Test::HasFatalFailure())
   {
      FAIL() << FAIL_LOG_FMT("failure when try to get xmpp account info from server");
   }
   safeCout("xmpp info username: " << evt.username);
   safeCout("xmpp info passwd: " << evt.password);
   safeCout("xmpp info domain: " << evt.domain);
   safeCout("xmpp info room: " << evt.chatRoomJid);
   int pos = evt.username.find("@");
   // Check that we got something back.
   ASSERT_TRUE( evt.domain.size() > 0 );
   ASSERT_TRUE( evt.chatRoomJid.size() > 0 );
   ASSERT_TRUE( evt.username.size() > 0 );
   ASSERT_TRUE( evt.password.size() > 0 ); // optional
   ASSERT_TRUE( evt.port >= 0 );
   outChatRoomJid = evt.chatRoomJid;
   test.config.settings.username = evt.username.substr(0, pos);
   test.config.settings.password = evt.password;
   test.config.settings.domain = evt.domain;
   test.config.nick = evt.username.substr(0, pos);
   if (xmppProxy.size() != 0)
      test.config.settings.proxy = xmppProxy;
   else
      test.config.settings.proxy = evt.proxy;
   test.init();
   safeCout("xmpp info proxy: " << test.config.settings.proxy);
   ASSERT_NO_FATAL_FAILURE(test.enable()) << FAIL_LOG_FMT("Failure in Xmpp account registeration");

}

void joinChatRoom(XmppTestAccount& test, cpc::string chatRoomJid,
                  XmppMultiUserChat::XmppMultiUserChatHandle& h)
{
   h = test.mucManager->create(test.handle, chatRoomJid);
   XmppMultiUserChat::RoomConfig rc;
   rc.createIfNotExisting = false;
   rc.isInstant = true;
   ASSERT_EQ(kSuccess, test.mucManager->join(h, rc, test.config.nick, "", "seconds:0")) << FAIL_LOG_FMT("Failure when join chat room " << chatRoomJid);
   {
      XmppMultiUserChat::XmppMultiUserChatHandle h1;
      XmppMultiUserChat::MultiUserChatReadyEvent evt1;
      ASSERT_TRUE(cpcExpectEvent(test.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 10000, CPCAPI2::test::AlwaysTruePred(), h1, evt1));
      ASSERT_EQ(h, h1);
      ASSERT_TRUE(!evt1.room.empty()) << FAIL_LOG_FMT("No chatRoom exists");
   }
   
}
#endif
