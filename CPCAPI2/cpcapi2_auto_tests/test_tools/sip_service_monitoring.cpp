#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#include "cpcapi2_test_fixture.h"
#include "test_call_events.h"
#include "test_events.h"
#include "test_account_events.h"
#include <fstream>
#include <filesystem>
#include "rutil/ConfigParse.hxx"
#include "json/JsonHelper.h"
#include "test_framework/visqol_runner.h"



using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::SipConversation;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::Media;

class SipServiceMonitoring : public CpcapiAutoTest
{
public:
   SipServiceMonitoring() {}
   virtual ~SipServiceMonitoring() {}
   
   void applyAudioCodecSettings(TestAccount& testAccount, const cpc::string& codecSettingJson);
   void visqolAnalysis(const std::string& originalWav, const std::string& testWav, double& mos, double threshold_mos);
};

class MyConfigParse : public resip::ConfigParse
{
private:
   void parseCommandLine(int argc, char** argv, int skipCount = 0) {}
   void printHelpText(int argc, char **argv) {}
};

std::atomic<bool> failed(false);

#define STATUS_LOG_FMT(x) "[[OK[" << x << "]]]"
#define FAIL_LOG_FMT(x) "[[NOK[" << x << "]]]"

TEST_F(SipServiceMonitoring, test) {

   // To run this test locally, build the autotests via VSCode as usual and pick the "macOS/Linux: SIP service monitoring tool" VSCode 
   // launch configuration.

#if defined(CPCAPI2_OVERRIDE_TEST_LIST_BUILD)
   // run the test without any check. This flow should be hit when the binary is being built for production purposes / delivery to
   // the operations team (i.e. no other test cases should be included in the binary).
#else
   // likely a local developer build; ensure CPCAPI2_ENABLE_TEST_TOOLS is set (which conveys intention to run this test, since
   // this test requires a special working directory to be set).
   const char* env = getenv("CPCAPI2_ENABLE_TEST_TOOLS");
   if (!env || strlen(env) == 0 || env[0] == '0')
   {
      GTEST_SKIP() << "Skipping test; binary not built with OVERRIDE_TEST_FILE_BUILD_LIST and environment variable CPCAPI2_ENABLE_TEST_TOOLS not set";
   }
#endif

   TestAccount alice("alice", Account_Init);
   TestAccount bob("bob", Account_Init);


   // Disabled setEchoCancellationMode and setNoiseSuppressionMode to cut 
   // additional audio processing which can slightly change the audio as it passes through 
   // the call. Mic is muted to avoid real background noise from being picked
   // up by microphone in computer, which would introduce unpredictable discrepancies
   // that would negatively impact visqol score.
   alice.audio->setEchoCancellationMode(AudioDeviceRole_Headset, EchoCancellationMode_None);
   alice.audio->setNoiseSuppressionMode(AudioDeviceRole_Headset, NoiseSuppressionMode_None);
   bob.audio->setEchoCancellationMode(AudioDeviceRole_Headset, EchoCancellationMode_None);
   bob.audio->setNoiseSuppressionMode(AudioDeviceRole_Headset, NoiseSuppressionMode_None);
   alice.audio->setMicMute(true);
   bob.audio->setMicMute(true);

   MyConfigParse configParse;
   configParse.parseConfig(0, NULL, "cpcapi2_sip_account.config");
   resip::Data accountSettingsFile;
   resip::Data convSettingsFile;
   resip::Data wavFilePath;
   cpc::string wavFile = "";
   resip::Data targetDialUri;
   resip::Data visqolThresholdMosConfig;
   std::string visqolThresholdMosString;
   double visqolThresholdMos;
   bool visqolEnabled = false;
   int callDurationMilliSec = 10000;
   int postRegistrationPauseMillisec = 0;
   bool srtpEnabled = false;
   if (configParse.getConfigValue("accountSettingsFile", accountSettingsFile))
   {
      std::ifstream in(accountSettingsFile.c_str());
      assert(in.is_open());

      std::ostringstream iss;
      iss << in.rdbuf() << std::flush;

      cpc::string accountDoc = iss.str().c_str();
      cpc::vector<SipAccountSettings> accounts;
      int result = alice.account->decodeProvisioningResponse(accountDoc, accounts);
      ASSERT_EQ(kSuccess, result) << FAIL_LOG_FMT("account provisioning file format is invalid");

      alice.config.settings = accounts[0];
      alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings);
      alice.account->applySettings(alice.handle);
      bob.config.settings = accounts[1];
      bob.account->configureDefaultAccountSettings(bob.handle, bob.config.settings);
      bob.account->applySettings(alice.handle);

      alice.enable(false);
      ASSERT_NO_FATAL_FAILURE(assertAccountRegisteringEx(alice)) << FAIL_LOG_FMT("first account account registration failure");
      ASSERT_NO_FATAL_FAILURE(assertAccountRegisteredEx(alice)) << FAIL_LOG_FMT("first account registration failure");
      safeCout(STATUS_LOG_FMT("first account seems registered okay"));

      bob.enable(false);
      ASSERT_NO_FATAL_FAILURE(assertAccountRegisteringEx(bob)) << FAIL_LOG_FMT("second account account registration failure");
      ASSERT_NO_FATAL_FAILURE(assertAccountRegisteredEx(bob)) << FAIL_LOG_FMT("second account registration failure");
      safeCout(STATUS_LOG_FMT("second account seems registered okay"));
   }

   MediaStackSettings mediaStackSettings;
   mediaStackSettings.audioLayer = AudioLayers_Dummy;
   alice.media->updateMediaSettings(mediaStackSettings);
   bob.media->updateMediaSettings(mediaStackSettings);

   if (configParse.getConfigValue("convSettingsFile", convSettingsFile))
   {
      std::ifstream in(convSettingsFile.c_str());
      assert(in.is_open());

      std::ostringstream iss;
      iss << in.rdbuf() << std::flush;

      cpc::string conversationDoc = iss.str().c_str();
      cpc::vector<SipConversationSettings> convs;
      int result = alice.conversation->decodeProvisioningResponse(conversationDoc, convs);
      ASSERT_EQ(kSuccess, result) << "conversation provisioning file format is invalid";
      alice.conversation->setDefaultSettings(alice.handle, convs[0]);
      bob.conversation->setDefaultSettings(bob.handle, convs[1]);
      
      applyAudioCodecSettings(alice, conversationDoc.c_str());
      applyAudioCodecSettings(bob, conversationDoc.c_str());
   }

   if (configParse.getConfigValue("wavFilePath", wavFilePath))
      wavFile = wavFilePath.c_str();

   configParse.getConfigValue("callDurationMilliSec", callDurationMilliSec);
   configParse.getConfigValue("srtpEnabled", srtpEnabled);
   configParse.getConfigValue("postRegistrationPauseMillisec", postRegistrationPauseMillisec);
   configParse.getConfigValue("visqolEnabled", visqolEnabled);

   #if !((defined( __APPLE__) && defined(__aarch64__)) || (defined(__linux__) && !defined(ANDROID)))
   visqolEnabled = false;
   safeCout("ViSQOL is not supported on this platform, tool operation will proceed without ViSQOL quality analysis");
   #endif

   #if (defined (__linux__) && !defined(ANDROID))
   visqolEnabled = false;
   safeCout("Visqol check disabled until https://alianza.atlassian.net/browse/SCORE-1319 can be resolved")
   #endif

   // In case of visqol testing, we extract the threshold mos from the config, use a different wav file to be played by alice, 
   // and adjust the call duration accordingly
   if (visqolEnabled)
   {
      configParse.getConfigValue("visqolThresholdMos", visqolThresholdMosConfig);
      visqolThresholdMos = std::stod(visqolThresholdMosConfig.c_str());
      ASSERT_LE(visqolThresholdMos, 4.2) << FAIL_LOG_FMT("visqolThresholdMos " + std::to_string(visqolThresholdMos) + " too high");
      ASSERT_GE(visqolThresholdMos, 3.4) << FAIL_LOG_FMT("visqolThresholdMos " + std::to_string(visqolThresholdMos) + " too low");
      safeCout(STATUS_LOG_FMT("visqolThresholdMos " + std::to_string(visqolThresholdMos) + " ok"));
   }

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   
   if (!configParse.getConfigValue("targetDialUri", targetDialUri))
   {
      targetDialUri = bob.config.uri();
   }
   alice.conversation->addParticipant(aliceCall, targetDialUri.c_str());

   MediaInfo aliceMedia;
   MediaInfo bobMedia;

   if (srtpEnabled)
   {
      aliceMedia.mediaDirection = MediaDirection_SendReceive;
      aliceMedia.mediaType = MediaType_Audio;
      aliceMedia.mediaEncryptionOptions.mediaEncryptionMode = MediaEncryptionMode_SRTP_SDES_Encrypted;
      aliceMedia.mediaEncryptionOptions.secureMediaRequired = true;
      aliceMedia.mediaEncryptionOptions.mediaCryptoSuites.clear(); // Ensure no other cryptos are included
      aliceMedia.mediaEncryptionOptions.mediaCryptoSuites.push_back(MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80);
      int ret = alice.conversation->configureMedia(aliceCall, aliceMedia);
      ASSERT_EQ(ret, kSuccess) << "confgiure alice media failed"; 
      bobMedia.mediaDirection = MediaDirection_SendReceive;
      bobMedia.mediaType = MediaType_Audio;
      bobMedia.mediaEncryptionOptions.mediaEncryptionMode = MediaEncryptionMode_SRTP_SDES_Encrypted;
      bobMedia.mediaEncryptionOptions.secureMediaRequired = true;
      bobMedia.mediaEncryptionOptions.mediaCryptoSuites.clear(); // Ensure no other cryptos are included
      bobMedia.mediaEncryptionOptions.mediaCryptoSuites.push_back(MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80);
   }
   
   safeCout(STATUS_LOG_FMT("about to send INVITE in " << postRegistrationPauseMillisec << " ms to " << targetDialUri));
   std::this_thread::sleep_for(std::chrono::milliseconds(postRegistrationPauseMillisec));

   alice.conversation->start(aliceCall);

   auto aliceConversationEvents = std::async(std::launch::async, [&]() {
      if (!failed) 
      {
         if (!srtpEnabled)
            ASSERT_NO_FATAL_FAILURE(assertNewConversationOutgoing(alice, aliceCall, targetDialUri.c_str())) << FAIL_LOG_FMT("first account outgoing call signal failure");
         else
            ASSERT_NO_FATAL_FAILURE(assertNewConversationOutgoingAudio_crypto(alice, aliceCall, targetDialUri.c_str(), aliceMedia, [](const NewConversationEvent& evt) {})) << FAIL_LOG_FMT("first account outgoing call signal failure");
      }
      safeCout(STATUS_LOG_FMT("first account sent outgoing INVITE"));
      
      if (!srtpEnabled)
         ASSERT_NO_FATAL_FAILURE(assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive)) << FAIL_LOG_FMT("first account outgoing call signal failure");
      else
         ASSERT_NO_FATAL_FAILURE(assertConversationMediaChangedAudio_crypto(alice, aliceCall, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, aliceMedia, bobMedia, [](const ConversationMediaChangedEvent& evt) {})) << FAIL_LOG_FMT("first account outgoing call signal failure");
   
      bool connected = false;
      while (!connected)
      {
         assertConversationStateChanged_ex(alice, aliceCall, [&](const ConversationStateChangedEvent& evt)
         {
            if (evt.conversationState == ConversationState_Connected)
            {
               safeCout(STATUS_LOG_FMT("first account detected establish call"));
               connected = true;
            }
            else if (!(evt.conversationState == ConversationState_Early ||
               evt.conversationState == ConversationState_RemoteRinging))
            {
               FAIL() << FAIL_LOG_FMT("Conversation state went to unexpected " << evt.conversationState << " state");
            }
         });

         if (::testing::Test::HasFailure())
         {
            return;
         }
      }

      ASSERT_EQ(kSuccess, alice.conversation->playSound(aliceCall, wavFile, true)) << FAIL_LOG_FMT("play sound failed");
      std::this_thread::sleep_for(std::chrono::milliseconds(callDurationMilliSec));
      ASSERT_NO_FATAL_FAILURE(assertAudioFlowing(alice, aliceCall)) << FAIL_LOG_FMT("first account detected no audio flowing");
      safeCout(STATUS_LOG_FMT("first account detected audio flowing"));
      ASSERT_EQ(alice.conversation->end(aliceCall), kSuccess) << FAIL_LOG_FMT("end conversation failed");
      
      ASSERT_NO_FATAL_FAILURE(assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally)) << FAIL_LOG_FMT("first account outgoing call end signal failure");
      safeCout(STATUS_LOG_FMT("first account detected call ended"));
      ASSERT_NO_FATAL_FAILURE(assertCallHadAudio(alice, aliceCall)) << FAIL_LOG_FMT("first account had no audio at call end");
      
   });
   std::stringstream recordFileName;
   auto bobConversationEvents = std::async(std::launch::async, [&]() {
      SipConversationHandle bobCall;
      
      if (!srtpEnabled)
         ASSERT_NO_FATAL_FAILURE(TestCallEvents::expectNewConversationIncoming(__LINE__, bob, &bobCall, alice.config.uri(), nullptr, false)) << FAIL_LOG_FMT("second account incoming call signal failure");
      else
         ASSERT_NO_FATAL_FAILURE(TestCallEvents::expectNewConversationIncomingAudio_crypto(__LINE__, bob, &bobCall, alice.config.uri(), aliceMedia, [](const NewConversationEvent& evt){}, false)) << FAIL_LOG_FMT("second account incoming call signal failure");
      
      ASSERT_EQ(bob.conversation->sendRingingResponse(bobCall), kSuccess) << FAIL_LOG_FMT("Sending ringing response failed");
      ASSERT_NO_FATAL_FAILURE(assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging)) << FAIL_LOG_FMT("second account incoming call ringing signal failure");
      if (srtpEnabled)
      {
         int ret = bob.conversation->configureMedia(bobCall, bobMedia);
         ASSERT_EQ(kSuccess, ret) << FAIL_LOG_FMT("bob configure media failed");
         bob.conversation->setMediaEnabled(bobCall, MediaType_Audio, true);
      }
      
      safeCout(STATUS_LOG_FMT("second account call ringing locally"));
      ASSERT_EQ(bob.conversation->accept(bobCall), kSuccess) << FAIL_LOG_FMT("accept call failed");
      
      safeCout("second account accepting call");
      if (!srtpEnabled)
         ASSERT_NO_FATAL_FAILURE(assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive)) << FAIL_LOG_FMT("second account call accept signal failure");
      else
         ASSERT_NO_FATAL_FAILURE(assertConversationMediaChangedAudio_crypto(bob, bobCall, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, bobMedia, aliceMedia, [](const ConversationMediaChangedEvent& evt) {})) << FAIL_LOG_FMT("second account call accept signal failure");
   
      ASSERT_NO_FATAL_FAILURE(assertConversationStateChanged(bob, bobCall, ConversationState_Connected)) << FAIL_LOG_FMT("second account call accept signal failure");
      
      CPCAPI2::Recording::RecorderHandle recorderHandle;
      if (visqolEnabled) {
         // Logic for recording audio from alice for visqol quality analysis
         recordFileName << "SipVisqolAnalysis.wav";
         if(!std::filesystem::remove(recordFileName.str())) {
            safeCout("Error deleting " + recordFileName.str());
         }
         recorderHandle = bob.recording->audioRecorderCreate(recordFileName.str().c_str());
         assertSuccess(bob.recording->recorderAddConversation(recorderHandle, bobCall));
         assertSuccess(bob.recording->recorderStart(recorderHandle));
      }
         
      safeCout(STATUS_LOG_FMT("second account sees established call"));
      std::this_thread::sleep_for(std::chrono::milliseconds(callDurationMilliSec));

      if (visqolEnabled) {
         assertSuccess(bob.recording->recorderDestroy(recorderHandle));
      }
      
      ASSERT_NO_FATAL_FAILURE(assertAudioFlowing(bob, bobCall)) << FAIL_LOG_FMT("second account detected no audio flowing");
      safeCout(STATUS_LOG_FMT("second account detected audio flowing"));
      
      ASSERT_NO_FATAL_FAILURE(assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely)) << FAIL_LOG_FMT("second account incoming call end signal failure");
      
      safeCout(STATUS_LOG_FMT("second account saw incoming BYE; call ended"));
      ASSERT_NO_FATAL_FAILURE(assertCallHadAudio(bob, bobCall)) << FAIL_LOG_FMT("second account had no audio at call end");
   });
   waitFor2(aliceConversationEvents, bobConversationEvents);

   // ViSQOL analysis step
   if (visqolEnabled) {
      double mos = 1.0;
      visqolAnalysis(wavFile.c_str(), recordFileName.str().c_str(), mos, visqolThresholdMos);
   }
}

void SipServiceMonitoring::applyAudioCodecSettings(TestAccount& testAccount, const cpc::string& codecSettingsJson)
{
   rapidjson::Document provisionedJSON;
   provisionedJSON.Parse<0>(codecSettingsJson.c_str());

   if (provisionedJSON.HasParseError())
   {
      FAIL() << FAIL_LOG_FMT("Invalid provisioning format for codecs");
   }

   if (!provisionedJSON.HasMember("customSettings"))
   {
      safeCout("No customSetting section present; skipping codec configuration");
      return;
   }
   
   const rapidjson::Value& customSettings = provisionedJSON["customSettings"];
   if (!customSettings.HasMember("audioCodecs"))
   {
      safeCout("No customSetting section present; skipping codec configuration");
      return;
   }

   const rapidjson::Value& audioCodecs = customSettings["audioCodecs"];
   if (!audioCodecs.IsArray())
   {
      safeCout("No customSetting section present; skipping codec configuration");
      return;
   }
   
   testAccount.disableAllAudioCodecs();

   for (rapidjson::Value::ConstValueIterator codecItr = audioCodecs.Begin(); codecItr != audioCodecs.End(); ++codecItr)
   {
      ASSERT_TRUE(codecItr->IsObject()) << FAIL_LOG_FMT("audioCodecs array element is not an object");
      
      rapidjson::Value::ConstMemberIterator itr = codecItr->FindMember("name");
      ASSERT_NE(itr, codecItr->MemberEnd()) << FAIL_LOG_FMT("audioCodeces array element does not contain name");
      const rapidjson::Value& codecNameValue = itr->value;
      ASSERT_TRUE(codecNameValue.IsString()) << FAIL_LOG_FMT("codec name is not a string");
      cpc::string codecName = codecNameValue.GetString();
      
      itr = codecItr->FindMember("enabled");
      ASSERT_NE(itr, codecItr->MemberEnd()) << FAIL_LOG_FMT("audioCodeces array element does not contain enabled");
      const rapidjson::Value& codecEnabledValue = itr->value;
      ASSERT_TRUE(codecEnabledValue.IsBool()) << FAIL_LOG_FMT("codec enabled is not a bool");
      bool codecEnabled = codecEnabledValue.GetBool();
      
      if (codecEnabled)
      {
         testAccount.enableCodec(codecName.c_str());
      }
   }
}

#ifdef _WIN32
static int setenv(const char *name, const char *value, int overwrite)
{
   int errcode = 0;
   if(!overwrite) {
      size_t envsize = 0;
      errcode = getenv_s(&envsize, NULL, 0, name);
      if(errcode || envsize) return errcode;
   }
   return _putenv_s(name, value);
}
#endif

void SipServiceMonitoring::visqolAnalysis(const std::string& originalWav, const std::string& testWav, double& mos, double threshold_mos){
   std::string visqolWavFile = "";
   // visqol_runner doesn't need the "file:" prefix specified for the wav file path in the config file, and hence that is being erased in the following lines of code
   const std::string filePrefix("file:");
   auto filePrefixPos = originalWav.find(filePrefix);
   if (filePrefixPos == 0)
   {
      visqolWavFile = originalWav.substr(filePrefix.length());
   }
   
   // .jza. this line breaks the windows build; is it necessary?
   setenv("CPCAPI2_RESOURCE_PATH", std::filesystem::current_path().string().c_str(), true);
   std::filesystem::path visqolTestWav = std::filesystem::current_path() / testWav;
   std::filesystem::path visqolReferenceWavFile = std::filesystem::current_path() / visqolWavFile;
   
   VisqolRunner::visqol_exec(visqolReferenceWavFile.string(), visqolTestWav.string(), mos);
   safeCout("MOS-LQO: " << mos);
   ASSERT_GE(mos, threshold_mos) << FAIL_LOG_FMT("MOS value " + std::to_string(mos) + " below minimum threshold " + std::to_string(threshold_mos));
   safeCout(STATUS_LOG_FMT("MOS value " + std::to_string(mos) + " meets minimum threshold " + std::to_string(threshold_mos)));
}

