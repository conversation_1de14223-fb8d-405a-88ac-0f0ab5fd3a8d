#!/bin/bash

set +e

testToolName="sip_test_tool"

cd /opt/cpcapi2_test_tool/test_resources
chmod u+x ../$testToolName

# enable core dumps
ulimit -c unlimited
mkdir -p /opt/cpcapi2_test_tool/tmp
# !warning! this modifies the *host's* core pattern!
sudo sh -c 'echo "/opt/cpcapi2_test_tool/tmp/core.dmp" > /proc/sys/kernel/core_pattern'
sudo sh -c 'echo "0" > /proc/sys/kernel/core_uses_pid'
# needed because we are calling setcap on cpcapi2_auto_tests below
sudo sh -c 'echo 1 > /proc/sys/fs/suid_dumpable'
alias gdbbt="gdb -q -n -ex bt -batch"
rm -rf /opt/cpcapi2_test_tool/dumps/*

# run the tool
echo "About to start ../$testToolName"
../$testToolName
ret=$?
me=`basename "$0"`
trap 'echo TEST FAILURE -- return code $? on $me line $LINENO; exit 1;' ERR

if [[ $ret -gt 1 ]]; then #crash
        dumpName=core.dmp
 
        if test -f "/opt/cpcapi2_test_tool/tmp/$dumpName"; then
		while [[ $(fuser -f /opt/cpcapi2_test_tool/tmp/$dumpName) ]]
                do
                        echo "[ Entrypoint ] Waiting for dump to finish writing.."
			sleep 1
                done
        fileName=$testToolName     
		mv /opt/cpcapi2_test_tool/tmp/$dumpName /opt/cpcapi2_test_tool/dumps/$fileName.dmp
		gdb -ex "thread apply all bt" -batch -n /opt/cpcapi2_test_tool/$testToolName /opt/cpcapi2_test_tool/dumps/$fileName.dmp > /opt/cpcapi2_test_tool/dumps/$fileName.dmp.txt
        echo "Wrote to /opt/cpcapi2_test_tool/dumps/$fileName.dmp.txt"
        else
		    echo "[ Entrypoint ] $testToolName exited with code $ret but no core dump available"
        fi
fi

exit $ret 
