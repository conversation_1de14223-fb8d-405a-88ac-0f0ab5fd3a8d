# this script uses docker to test the sip_test_tool binary.
# sip_test_tool should be already built and in $(pwd)/../../../../build
# before invoking this script.

mkdir dumps || true

docker build -t cpcapi2_sip_service_test_tool_test -f Dockerfile.sip-service-test.centos .
docker run --rm --mount type=bind,source="$(pwd)",target=/opt/cpcapi2_test_tool/test_resources \
                --mount type=bind,source="$(pwd)/../../../Linux/build",target=/opt/cpcapi2_test_tool \
                --mount type=bind,source="$(pwd)/dumps",target=/opt/cpcapi2_test_tool/dumps \
                --privileged cpcapi2_sip_service_test_tool_test 
