# see https://docs.counterpath.com/guides/sdk/sdk1130/SDK/provResponse/strettoProvResponseINTRO.htm for schema of account, conversation JSON
# sip account config file
accountSettingsFile = account.json
# sip conversation config file
convSettingsFile = conversation.json
# relative path of the wav file
wavFilePath = file:referenceClip-wideband.wav
# duration of the call
callDurationMilliSec = 10000
# Use SRTP media encryption
srtpEnabled = true
# optional delay after both accounts successfully register before sending the INVITE
postRegistrationPauseMillisec = 1000
# optionally specify a target URI to dial, instead of dialing the second account's
# username@domain
targetDialUri = sip:<EMAIL>
# enable or disable visqol check. Enabled = true, Disabled = false. Disabled by default.
visqolEnabled = true
# minimum mos value threshold for visqol quality analysis to pass. For wideband codecs,
# use a value of around 4.0. For narrowband codecs (e.g. G711) use a value of around 3.4
visqolThresholdMos = 4.0