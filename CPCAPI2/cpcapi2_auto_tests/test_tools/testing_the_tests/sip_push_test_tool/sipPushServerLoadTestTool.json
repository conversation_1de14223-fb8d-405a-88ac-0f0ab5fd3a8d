{"logToConsole": true, "eventTimeoutInMilliseconds": 20000, "httpServerPort": 8989, "pushServerSSLVersion": "auto", "pushServerUrl": "https://push-g2-uw2.softphone.com/push", "pushServerCredential": "SkBYfTgqypCMn1KuW7m51LSBVz2xlNod:5f7d47c728804dd193004ae9dde4b4e8", "applicationId": "com.dominique.app.test", "localSipAccount": {"sipAccountSettings": {"username": "***********", "password": "87RU3mygolnP", "authUser": "***********", "domain": "bstnma-ncg32.mobilevoiplive.com", "outboundProxy": "", "userAgent": "SipSDKPushSample", "useRport": false, "useOutbound": false, "userCertificatePEM": "-----<PERSON><PERSON><PERSON> CERTIFICATE-----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\n2qNlWjnQaqeDWk/giu+6PMrZuoAePB0bSYXOs2JD\n-----END CERTIFICATE-----", "userPrivateKeyPEM": "-----B<PERSON><PERSON> RSA PRIVATE KEY-----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\nJyupKfz138DlDzC1GWr0wgkQN/xzT0gOvDu2d3B6hbIzgbyzN/bQIHcXGw==\n-----END RSA PRIVATE KEY-----"}}, "remoteSipUser": "", "callerUriCheck": "", "requests": [{"accountRequest": {"method": "updateAccount", "accountUUID": "48F80FEA-60EB-42FE-8EC3-7D5D1107B8E3", "username": "***********", "password": "WaMc7wXYVeGf", "auth_username": "***********", "domain": "bstnma-ncg32.mobilevoiplive.com", "outboundProxy": "", "applicationId": "com.dominique.app.test", "userAgent": "SipSDKPushSample", "mtlsClientCertificatePEM": "", "mtlsClientPrivateKeyPEM": "", "publicPushNatEmulation": false, "publicPushRegistrationMode": "SingleDeviceTakeover", "publicPushInsertRInstance": false, "publicPushDisableHashToken": false, "publicPushAutoSend180": false, "publicPushDisableOverrideDomain": true, "publicPushRegistrationRefresh": 3600, "reportSubscriptionStatus": true, "reportServiceError": true}}, {"accountRequest": {"method": "deleteAccount", "accountUUID": "48F80FEA-60EB-42FE-8EC3-7D5D1107B8E3", "reason": "normalClearing"}}, {"publicPushRequest": {"method": "subscribe", "accountUUID": "48F80FEA-60EB-42FE-8EC3-7D5D1107B8E3", "applicationId": "com.dominique.app.test", "ignoreTlsCertVerify": true, "pushType": "test", "sandbox": true, "sipInstance": "<urn:uuid:********-D67F-4DD0-A58D-E3DC5CCBC4BC>", "sipTransport": "UDP", "token": "d8013c3a86d6e2bc82331e91b99950dc9040b4d23a2893cdc7d159b97374cccb", "apnsToken": "4d9d0ece73a2736841ad8a72b2859bffe608802e1fab2d7ba1788f0fd0b7e910"}}, {"publicPushRequest": {"method": "unsubscribe", "accountUUID": "********-D67F-4DD0-A58D-E3CD5CCBC4BC"}}]}