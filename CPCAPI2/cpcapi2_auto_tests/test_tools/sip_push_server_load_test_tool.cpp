/*
 *  sip_push_server_load_test_tool.cpp
 *
 *  Created by <PERSON> on April 08, 2021.
 *  Copyright 2021 CounterPath Corporation. All rights reserved.
*/

// compile problems currently exist with rapidjson usage
#ifndef _WIN32

#if _WIN32
#include "stdafx.h"
#endif

#include <regex>

#define BOOST_UUID_RANDOM_PROVIDER_FORCE_POSIX

#include <boost/uuid/uuid.hpp>
#include <boost/uuid/uuid_generators.hpp>
#include <boost/uuid/uuid_io.hpp>
#include <boost/lexical_cast.hpp>

#include <server_http.hpp>

#ifndef WIN32
#include <sys/resource.h>
#endif

#include <rutil/Mutex.hxx>
#include <rutil/Lock.hxx>
#include <rutil/Condition.hxx>

#include "phone/PhoneInternal.h"
#include "json/JsonHelper.h"
#include "brand_branded.h"
#include "../cpcapi2_test_fixture.h"
#include "test_account_events.h"
#include "test_call_events.h"
#include "impl/util/CurlPPHelper.h"
#include "impl/util/CurlPPProgress.h"
#include "impl/util/CurlPPSSL.h"
#include "impl/util/DeviceInfo.h"
#include "../test_framework/cpcapi2_test_framework.h"

typedef SimpleWeb::Server<SimpleWeb::HTTP> HttpServer;

using namespace CPCAPI2;
using namespace CPCAPI2::test;

static std::recursive_mutex _safeCoutMutex;
static bool _safeCoutToggle = true;
#undef safeCout
#define safeCout(e) { using namespace CPCAPI2::test; using namespace CPCAPI2; std::lock_guard<std::recursive_mutex> lock(_safeCoutMutex); if (_safeCoutToggle) { std::cout.flush(); std::cerr.flush(); std::cout << e << std::endl; std::cout.flush(); } }

#define STATUS_LOG_FMT(x) "[[OK[" << x << "]]]"
#define FAIL_LOG_FMT(x) "[[NOK[" << x << "]]]"

#ifdef _WIN32
extern int setenv(const char* name, const char* value, int overwrite);
#endif

template<typename T>
inline static std::string stringify(const T& o)
{
   rapidjson::StringBuffer sb;
   rapidjson::Writer<rapidjson::StringBuffer> writer(sb);
   o.Accept(writer);
   return sb.GetString();
}

// need to make sure 'str' is in the form of "<key>" : "<value>" and the quotes present properly
inline static bool replaceJsonStringValue(std::string& str, const std::string& key, const std::string& value)
{
   auto result = std::regex_replace(str, std::regex("(\"" + key + "\")[^\"]*\"[^\"]*\"(.*)"), "$1 : \"" + value + "\"$2");
   if (result == str) return false;

   str = result;
   return true;
}

static std::map<std::string, std::pair<int, std::string> > sslVersionStringMap = {
   { "auto",      { CURL_SSLVERSION_DEFAULT,       "The default version range (mostly TLS v1.0 or above)" } },
   { "tlsv1.0+",  { CURL_SSLVERSION_TLSv1_0,       "TLS v1.0 or above" } },
   { "tlsv1.1+",  { CURL_SSLVERSION_TLSv1_1,       "TLS v1.1 or above" } },
   { "tlsv1.2+",  { CURL_SSLVERSION_TLSv1_2,       "TLS v1.2 or above" } },
   { "tlsv1.3+",  { CURL_SSLVERSION_TLSv1_3,       "TLS v1.3 or above" } },
   { "max",       { CURL_SSLVERSION_MAX_DEFAULT,   "The default max version (mostly TLS v1.2 or TLS v1.3)" } },
   { "tlsv1.0-",  { CURL_SSLVERSION_MAX_TLSv1_0,   "TLS v1.0 or below" } },
   { "tlsv1.1-",  { CURL_SSLVERSION_MAX_TLSv1_1,   "TLS v1.1 or below" } },
   { "tlsv1.2-",  { CURL_SSLVERSION_MAX_TLSv1_2,   "TLS v1.2 or below" } },
   { "tlsv1.3-",  { CURL_SSLVERSION_MAX_TLSv1_3,   "TLS v1.3 or below" } }
};

static unsigned int eventTimeout = 10000;

class SipPushServerLoadTestTool : public CpcapiAutoTest
{
public:
   SipPushServerLoadTestTool() : pushAccount(NULL), normalAccount(NULL), httpServerThread(NULL) {}
   virtual ~SipPushServerLoadTestTool() {}

   virtual void SetUp() OVERRIDE;
   virtual void TearDown() OVERRIDE;

protected:
   void CleanUp();

   SimpleWeb::Server<SimpleWeb::HTTP> httpServer;
   std::thread* httpServerThread;
   CurlPPHelper helper;
   curlpp::Easy request;
   std::stringstream response;

   TestAccount* pushAccount;
   TestAccount* normalAccount;
   resip::Mutex pushAccountMutex;
   resip::Condition pushAccountCond;

   unsigned short httpServerPort = 8989;
   std::string localSipAccount;    // optional from JSON, Bob's SIP account configuration 
   std::string remoteSipUser;      // optional from JSON, URI to reach Alice
   std::string callerUriCheck;     // optional from JSON, URI Alice should see from Bob
   std::string pushServerUrl;
   std::string pushServerCredential;
   int pushServerSSLVersion = sslVersionStringMap["auto"].first;
   std::string tunnelToken;
   std::map<std::string, std::string> requests;
};

static void _expectAccountRegistering(int line, TestAccount& account)
{
   SipAccount::SipAccountHandle h;
   SipAccount::SipAccountStatusChangedEvent evt;
   ASSERT_TRUE(account.accountEvents->expectEvent(line, "SipAccountHandler::onAccountStatusChanged", eventTimeout, HandleEqualsPred<SipAccount::SipAccountHandle>(account.handle), h, evt)) << FAIL_LOG_FMT("missing account status changed event");
   if (evt.accountStatus == SipAccount::SipAccountStatusChangedEvent::Status_Failure)
   {
      safeCout(FAIL_LOG_FMT("Registration failed with code ") << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
   }
   ASSERT_EQ(SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus) << FAIL_LOG_FMT("account is not registering");
}

#define expectAccountRegistering(...) _expectAccountRegistering(__LINE__, __VA_ARGS__)

static void _expectAccountRegistered(int line, TestAccount& account)
{
   SipAccount::SipAccountHandle h;
   SipAccount::SipAccountStatusChangedEvent evt;
   ASSERT_TRUE(account.accountEvents->expectEvent(line, "SipAccountHandler::onAccountStatusChanged", eventTimeout, HandleEqualsPred<SipAccount::SipAccountHandle>(account.handle), h, evt)) << FAIL_LOG_FMT("missing account status changed event");
   if (evt.accountStatus == SipAccount::SipAccountStatusChangedEvent::Status_Failure)
   {
      safeCout(FAIL_LOG_FMT("Registration failed with code ") << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
   }
   ASSERT_EQ(SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus) << FAIL_LOG_FMT("account is not registered");
}

#define expectAccountRegistered(...) _expectAccountRegistered(__LINE__, __VA_ARGS__)

static cpc::string convertion(SipConversation::ConversationState state) {
   cpc::string stateDescription;
   switch (state) {
   case(SipConversation::ConversationState_None): stateDescription = "ConversationState_None";
      break;
   case(SipConversation::ConversationState_LocalOriginated): stateDescription = "ConversationState_LocalOriginated";
      break;
   case(SipConversation::ConversationState_RemoteOriginated): stateDescription = "ConversationState_RemoteOriginated";
      break;
   case(SipConversation::ConversationState_RemoteRinging): stateDescription = "ConversationState_RemoteRinging";
      break;
   case(SipConversation::ConversationState_LocalRinging): stateDescription = "ConversationState_LocalRinging";
      break;
   case(SipConversation::ConversationState_Connected): stateDescription = "ConversationState_Connected";
      break;
   case(SipConversation::ConversationState_Early): stateDescription = "ConversationState_Early";
      break;
   case(SipConversation::ConversationState_Ended): stateDescription = "ConversationState_Ended";
      break;
   default: stateDescription = "invalid state";
   }
   return stateDescription;
}

static void _expectNewConversationOutgoing(int line, TestAccount& account, SipConversation::SipConversationHandle handle, const cpc::string& toUri)
{
   SipConversation::SipConversationHandle h;
   SipConversation::NewConversationEvent evt;
   ASSERT_TRUE(account.conversationEvents->expectEvent(line, "SipConversationHandler::onNewConversation", eventTimeout, HandleEqualsPred<SipConversation::SipConversationHandle>(handle), h, evt)) << FAIL_LOG_FMT("missed outgoing call event");
   ASSERT_EQ(SipConversation::ConversationType_Outgoing, evt.conversationType) << FAIL_LOG_FMT("Conversation type does not match with outgoing");
   ASSERT_EQ(toUri, evt.remoteAddress) << FAIL_LOG_FMT("Remote address does not match");
}

#define expectNewConversationOutgoing(...) _expectNewConversationOutgoing(__LINE__, __VA_ARGS__)

static void _expectConversationStateChanged(int line, TestAccount& account, SipConversation::SipConversationHandle handle, SipConversation::ConversationState state)
{
   SipConversation::SipConversationHandle h;
   SipConversation::ConversationStateChangedEvent evt;
   ASSERT_TRUE(account.conversationEvents->expectEvent(line, "SipConversationHandler::onConversationStateChanged", eventTimeout, HandleEqualsPred<SipConversation::SipConversationHandle>(handle), h, evt)) << FAIL_LOG_FMT("missed conversation changed event");
   ASSERT_EQ(state, evt.conversationState) << FAIL_LOG_FMT("Conversation state does not match, current state is " << convertion(evt.conversationState).c_str() << ". expecting " << convertion(state).c_str());
}

#define expectConversationStateChanged(...) _expectConversationStateChanged(__LINE__, __VA_ARGS__)

static void _expectConversationMediaChanged(int line, TestAccount& account, SipConversation::SipConversationHandle handle, SipConversation::MediaDirection direction)
{
   SipConversation::SipConversationHandle h;
   SipConversation::ConversationMediaChangedEvent evt;
   ASSERT_TRUE(account.conversationEvents->expectEvent(line, "SipConversationHandler::onConversationMediaChanged", eventTimeout, HandleEqualsPred<SipConversation::SipConversationHandle>(handle), h, evt)) << FAIL_LOG_FMT("missed conversation state changed event");
   ASSERT_LE((size_t)1, evt.localMediaInfo.size()) << FAIL_LOG_FMT("local media info size cannot be empty");
   for (cpc::vector<SipConversation::MediaInfo>::const_iterator it = evt.localMediaInfo.begin(); it != evt.localMediaInfo.end(); it++)
   {
      ASSERT_EQ(direction, it->mediaDirection) << FAIL_LOG_FMT("Media Direction is wrong");
   }
}

#define expectConversationMediaChanged(...) _expectConversationMediaChanged(__LINE__, __VA_ARGS__)

static void _expectNewConversationIncoming(int line, TestAccount& account, SipConversation::SipConversationHandle* handle, const cpc::string& fromUri, bool checkFromUri)
{
   SipConversation::NewConversationEvent evt;
   ASSERT_TRUE(account.conversationEvents->expectEvent(line, "SipConversationHandler::onNewConversation", eventTimeout, AlwaysTruePred(), *handle, evt)) << FAIL_LOG_FMT("missed inbound call event");
   ASSERT_EQ(SipConversation::ConversationType_Incoming, evt.conversationType) << FAIL_LOG_FMT("wrong conversation type, expecting conversation type Incoming");
   if (checkFromUri)
      ASSERT_EQ(resip::Uri(fromUri.c_str()).getAorNoPort(), resip::Uri(evt.remoteAddress.c_str()).getAorNoPort()) << FAIL_LOG_FMT("remote address is incorrect");

   ASSERT_FALSE(evt.isCodecsMismatched) << FAIL_LOG_FMT("Codec Mismatched");
}

#define expectNewConversationIncoming(...) _expectNewConversationIncoming(__LINE__, __VA_ARGS__)

static void _expectConversationEnded(int line, TestAccount& account, SipConversation::SipConversationHandle handle, SipConversation::ConversationEndReason endReason)
{
   SipConversation::SipConversationHandle h;
   SipConversation::ConversationEndedEvent evt;
   ASSERT_TRUE(account.conversationEvents->expectEvent(line, "SipConversationHandler::onConversationEnded", eventTimeout, HandleEqualsPred<SipConversation::SipConversationHandle>(handle), h, evt)) << FAIL_LOG_FMT("missed conversation ended event");
   ASSERT_EQ(endReason, evt.endReason) << FAIL_LOG_FMT("Coversation ended with incorrect reason");
}

#define expectConversationEnded(...) _expectConversationEnded(__LINE__, __VA_ARGS__)

std::string resourcePathRegex(const std::string& resourcePath)
{
   std::stringstream ss;
   ss << resourcePath << "$";
   return ss.str();
}

extern std::pair<SipConversation::SipConversationHandle, SipConversation::SipConversationHandle> doBasicCall(TestAccount& alice, TestAccount& bob, const resip::Data& assertAliceCodec, const resip::Data& assertBobCodec);

void SipPushServerLoadTestTool::CleanUp()
{
   // these two commands clean up the subscription that may have been 
   // left by a previous failed unit test

   const auto& unsubscribe = requests["unsubscribe"];

   helper.setDefaultOptions(request, pushServerUrl, "POST", unsubscribe.size());
   request.setOpt(new curlpp::options::PostFields(unsubscribe));

   std::list<std::string> header;
   header.push_back("Content-Type: application/json");

   request.setOpt(new curlpp::options::HttpHeader(header));
   request.setOpt(new curlpp::options::UserPwd(pushServerCredential));
   request.setOpt(new curlpp::options::SslVersion(pushServerSSLVersion));
   request.setOpt(new curlpp::options::Verbose(true));

   CurlPPSSL cssl(SslCipherOptions(), CurlPPSSL::E_CERT_WHATEVER_ERROR);
   request.setOpt(new curlpp::options::SslCtxFunction(cssl));

   request.perform();

   const auto& deleteAccount = requests["deleteAccount"];

   helper.setDefaultOptions(request, pushServerUrl, "POST", deleteAccount.size());
   request.setOpt(new curlpp::options::PostFields(deleteAccount));

   request.perform();
}

void SipPushServerLoadTestTool::SetUp()
{
#if defined(CPCAPI2_OVERRIDE_TEST_LIST_BUILD)
   // run the test without any check. This flow should be hit when the binary is being built for production purposes / delivery to
   // the operations team (i.e. no other test cases should be included in the binary).
#else
   // likely a local developer build; ensure CPCAPI2_ENABLE_TEST_TOOLS is set (which conveys intention to run this test, since
   // this test requires a special working directory to be set).
   const char* env = getenv("CPCAPI2_ENABLE_TEST_TOOLS");
   if (!env || strlen(env) == 0 || env[0] == '0')
   {
      GTEST_SKIP() << "Skipping test; binary not built with OVERRIDE_TEST_FILE_BUILD_LIST and environment variable CPCAPI2_ENABLE_TEST_TOOLS not set";
   }
#endif

   safeCout(STATUS_LOG_FMT("setup"));

   setenv("CPCAPI2_SAVE_LOGGING_TO_FILE", "1", 1);

   // load configurations
   std::string configFileName = "sipPushServerLoadTestTool.json";
   ASSERT_TRUE(TestEnvironmentConfig::getResourceFile(configFileName, configFileName));
   resip::Data configFile = configFileName.c_str();

   std::ifstream ifs(configFile.c_str());
   ASSERT_TRUE(ifs.is_open()) << FAIL_LOG_FMT("failed when opening " << configFileName.c_str() << " file");

   std::ostringstream oss;
   oss << ifs.rdbuf() << std::flush;

   rapidjson::Document json;

   json.Parse<0>(oss.str().c_str());
   ASSERT_FALSE(json.HasParseError()) << FAIL_LOG_FMT("parse error occured in " << configFileName.c_str() << ":" << json.GetParseError());

   if (!json.HasMember("logToConsole") || !json["logToConsole"].GetBool())
   {
      setenv("CPCAPI2_LOG_TO_CONSOLE", "0", 1);
   }

   if (json.HasMember("eventTimeoutInMilliseconds"))
   {
      eventTimeout = json["eventTimeoutInMilliseconds"].GetUint();
   }

   if (json.HasMember("httpServerPort"))
   {
      httpServerPort = json["httpServerPort"].GetUint();
   }

   if (json.HasMember("pushServerUrl"))
   {
      pushServerUrl = json["pushServerUrl"].GetString();
   }

   if (json.HasMember("pushServerCredential"))
   {
      pushServerCredential = json["pushServerCredential"].GetString();
   }

   if (json.HasMember("pushServerSSLVersion"))
   {
      std::stringstream ss;
      for (auto& i : sslVersionStringMap) ss << std::setfill(' ') << std::left << std::setw(15) << i.first << i.second.second << '\n';

      std::string pushServerSSLVersionStr = json["pushServerSSLVersion"].GetString();
      auto it = sslVersionStringMap.find(pushServerSSLVersionStr);
      if (it == sslVersionStringMap.end())
      {
         safeCout("please refer to the following possible values for \"pushServerSSLVersion\":\n" << ss.str());
         ASSERT_TRUE(it != sslVersionStringMap.end()) << FAIL_LOG_FMT("invalid ssl version string : " << pushServerSSLVersionStr);
      }

      pushServerSSLVersion = it->second.first;
   }

   std::string applicationId;

   if (json.HasMember("applicationId"))
   {
      applicationId = json["applicationId"].GetString();
   }

   if (json.HasMember("localSipAccount"))
   {
      localSipAccount = stringify(json["localSipAccount"]);
   }
   if (json.HasMember("remoteSipUser"))
   {
      remoteSipUser = json["remoteSipUser"].GetString();
   }
   if (json.HasMember("callerUriCheck"))
   {
      callerUriCheck = json["callerUriCheck"].GetString();
   }

   pushAccount = new TestAccount("alice", Account_NoInit, true, NULL, NULL, false, false);
   TestAccount& alice = *pushAccount;

   normalAccount = new TestAccount("bob", Account_NoInit, true, NULL, NULL, false, false);
   TestAccount& bob = *normalAccount;

   if (requests.empty() && json.HasMember("requests"))
   {
      const auto& requests_json = json["requests"];
      ASSERT_TRUE(requests_json.IsArray()) << FAIL_LOG_FMT("node requests is not an array");

      const auto uuid = boost::uuids::random_generator()();

      for (auto it = requests_json.Begin(), end = requests_json.End(); it != end; ++it)
      {
         ASSERT_TRUE(it->IsObject()) << FAIL_LOG_FMT("item of requests is not an object");
         ASSERT_TRUE(it->GetObject().MemberCount() == 1) << FAIL_LOG_FMT("item of requests is invalid");

         const auto& request_json = it->GetObject().MemberBegin()->value;
         ASSERT_TRUE(request_json.IsObject()) << FAIL_LOG_FMT("request is not an object");
         ASSERT_TRUE(request_json.HasMember("method")) << FAIL_LOG_FMT("item of requests has no method field");
         ASSERT_TRUE(request_json["method"].IsString()) << FAIL_LOG_FMT("method field is not a string");

         std::string method = request_json["method"].GetString();

         std::string request_str = stringify(*it);
         ASSERT_TRUE(replaceJsonStringValue(request_str, "accountUUID", boost::lexical_cast<std::string>(uuid)));
         if (!applicationId.empty()) replaceJsonStringValue(request_str, "applicationId", applicationId);

         ASSERT_TRUE(requests.insert(std::make_pair(method, request_str)).second) << FAIL_LOG_FMT("duplicate request method: " << method);

         if (method == "subscribe")
         {
            ASSERT_TRUE(request_json.HasMember("token")) << FAIL_LOG_FMT("subscribe request has no token field");
            ASSERT_TRUE(request_json["token"].IsString()) << FAIL_LOG_FMT("token field is not a string");
            tunnelToken = request_json["token"].GetString();
            ASSERT_TRUE(request_json.HasMember("sipInstance")) << FAIL_LOG_FMT("subscribe request has no sipInstance field");
            ASSERT_TRUE(request_json["sipInstance"].IsString()) << FAIL_LOG_FMT("sipInstance field is not a string");
         }
         else if (method == "updateAccount")
         {
            ASSERT_TRUE(request_json.HasMember("domain")) << FAIL_LOG_FMT("updateAccount request has no domain field");
            ASSERT_TRUE(request_json["domain"].IsString()) << FAIL_LOG_FMT("domain field is not a string");
            ASSERT_TRUE(request_json.HasMember("username")) << FAIL_LOG_FMT("updateAccount request has no username field");
            ASSERT_TRUE(request_json["username"].IsString()) << FAIL_LOG_FMT("username field is not a string");

            // we copy the destination SIP account information from the JSON file to populate the test account here

            ASSERT_TRUE(request_json.HasMember("username"));
            std::string username = request_json["username"].GetString();

            ASSERT_TRUE(request_json.HasMember("password"));
            std::string password = request_json["password"].GetString();

            ASSERT_TRUE(request_json.HasMember("domain"));
            std::string domain = request_json["domain"].GetString();

            std::string authUser;
            if (request_json.HasMember("auth_username"))
               authUser = request_json["auth_username"].GetString();

            std::string outboundProxy;
            if (request_json.HasMember("outboundProxy"))
               outboundProxy = request_json["outboundProxy"].GetString();

            std::string userAgent;
            if (request_json.HasMember("userAgent"))
               userAgent = request_json["userAgent"].GetString();

            std::string mtlsClientCertificatePEM;
            if (request_json.HasMember("mtlsClientCertificatePEM"))
               mtlsClientCertificatePEM = request_json["mtlsClientCertificatePEM"].GetString();

            std::string mtlsClientPrivateKeyPEM;
            if (request_json.HasMember("mtlsClientPrivateKeyPEM"))
               mtlsClientPrivateKeyPEM = request_json["mtlsClientPrivateKeyPEM"].GetString();

            // account settings
            alice.config.settings.username = username.c_str();
            alice.config.settings.password = password.c_str();
            alice.config.settings.auth_username = authUser.c_str();
            alice.config.settings.domain = domain.c_str();
            alice.config.settings.outboundProxy = outboundProxy.c_str();
            alice.config.settings.userAgent = userAgent.c_str();
            alice.config.settings.userCertificatePEM = mtlsClientCertificatePEM.c_str();
            alice.config.settings.userPrivateKeyPEM = mtlsClientPrivateKeyPEM.c_str();
         }
      }
   }

   // we clean up the server state as much as we can in case a previous test did not complete
   CleanUp();

#ifndef __APPLE__ // seems to be a Mac bug in file_audio_device.cc
   bob.config.useFileAudioDevice = true;
#endif
   bob.init();

   if (!localSipAccount.empty())
   {
      cpc::vector<SipAccount::SipAccountSettings> settings;
      int result = bob.account->decodeProvisioningResponse(cpc::string("{\"sipAccount\": [") + localSipAccount.c_str() + "]}", settings);
      ASSERT_EQ(kSuccess, result);
      if (!settings.empty())
      {
         bob.config.settings = settings[0];  // used for comparing the Uri in the test
         ASSERT_EQ(bob.account->configureDefaultAccountSettings(bob.handle, settings[0]), kSuccess);
         ASSERT_EQ(bob.account->applySettings(bob.handle), kSuccess);
      }
   }

   bob.enable(true);

   httpServer.config.port = httpServerPort;

   httpServer.resource[resourcePathRegex("/")]["POST"] = [&](std::shared_ptr<HttpServer::Response> response, std::shared_ptr<HttpServer::Request> request) {
      // get body contents, and echo it back in the response

      auto content = request->content.string();
      *response << "HTTP/1.1 200 OK\r\nContent-Length: " << content.length() << "\r\n\r\n" << content;

      rapidjson::Document json;

      json.Parse<0>(content.c_str());
      ASSERT_FALSE(json.HasParseError()) << FAIL_LOG_FMT("parse error occured in HTTP notification:" << json.GetParseError());

      ASSERT_TRUE(json.IsObject());
      ASSERT_TRUE(json.HasMember("cpc"));

      const auto& cpc_json = json["cpc"];
      ASSERT_TRUE(cpc_json.IsObject());

      ASSERT_TRUE(cpc_json.HasMember("tunnelURL"));
      std::string tunnelURL = cpc_json["tunnelURL"].GetString();

      ASSERT_TRUE(cpc_json.HasMember("id"));
      std::string tunnelId = cpc_json["id"].GetString();

      // constant settings
#ifndef __APPLE__ // seems to be a Mac bug in file_audio_device.cc
      alice.config.useFileAudioDevice = true;
#endif
      alice.config.settings.useRegistrar = false;
      alice.config.settings.ignoreCertVerification = true;
      alice.config.settings.sipTransportType = SipAccount::SipAccountTransport_UDP;

      // tunnelling settings
      alice.config.settings.tunnelConfig.useTunnel = true;
      alice.config.settings.tunnelConfig.tunnelType = SipAccount::TunnelType_StrettoTunnel;
      alice.config.settings.tunnelConfig.strettoTunnelURL = tunnelURL.c_str();
      alice.config.settings.tunnelConfig.strettoTunnelSessionID = tunnelId.c_str();
      alice.config.settings.tunnelConfig.strettoTunnelToken = tunnelToken.c_str();

      resip::Lock lock(pushAccountMutex);
      pushAccountCond.signal();

      safeCout(STATUS_LOG_FMT("incoming push notification strettoTunnelURL=" << tunnelURL << " strettoTunnelSessionID=" << tunnelId << " tunnelToken=" << tunnelToken));
   };

   httpServerThread = new std::thread([&]() {
      httpServer.start();
   });

   safeCout(STATUS_LOG_FMT("setup finished"));
}

void SipPushServerLoadTestTool::TearDown()
{
   safeCout("shutdown");

   try
   {
      std::stringstream response;

      const auto& unsubscribe = requests["unsubscribe"];

      helper.setDefaultOptions(request, pushServerUrl, "POST", unsubscribe.size());
      request.setOpt(new curlpp::options::PostFields(unsubscribe));

      request.perform();
      ASSERT_EQ(curlpp::infos::ResponseCode::get(request), 200);

      const auto& deleteAccount = requests["deleteAccount"];

      helper.setDefaultOptions(request, pushServerUrl, "POST", deleteAccount.size());
      request.setOpt(new curlpp::options::PostFields(deleteAccount));

      request.perform();
      ASSERT_EQ(curlpp::infos::ResponseCode::get(request), 200);
   }
   catch (curlpp::RuntimeError& e)
   {
      safeCout("cURL runtime error: " << e.what());
   }

   httpServer.stop();

   if (httpServerThread != NULL) httpServerThread->join();
   delete httpServerThread;
   httpServerThread = NULL;

   delete normalAccount;
   normalAccount = NULL;

   if (pushAccount != NULL) pushAccount->disable(true, false);
   delete pushAccount;
   pushAccount = NULL;

   safeCout("shutdown finished");
}

TEST_F(SipPushServerLoadTestTool, test)
{

   // To run this test locally, build the autotests via VSCode as usual and pick the "macOS/Linux: SIP push server monitoring tool" VSCode 
   // launch configuration -- such that CPCAPI2_ENABLE_TEST_TOOLS env variable is set

   TestAccount& alice = *pushAccount;
   TestAccount& bob = *normalAccount;

   try
   {
      auto updateAccount = requests["updateAccount"];

      helper.setDefaultOptions(request, pushServerUrl, "POST", updateAccount.size());
      request.setOpt(new curlpp::options::PostFields(updateAccount));

      std::list<std::string> header;
      header.push_back("Content-Type: application/json");

      request.setOpt(new curlpp::options::HttpHeader(header));
      request.setOpt(new curlpp::options::UserPwd(pushServerCredential));
      request.setOpt(new curlpp::options::SslVersion(pushServerSSLVersion));
      request.setOpt(new curlpp::options::Verbose(true));

      CurlPPSSL cssl(SslCipherOptions(), CurlPPSSL::E_CERT_WHATEVER_ERROR);
      request.setOpt(new curlpp::options::SslCtxFunction(cssl));

      request.setOpt(new curlpp::options::WriteStream(&response));
      request.perform();
      ASSERT_EQ(curlpp::infos::ResponseCode::get(request), 200);

      auto subscribe = requests["subscribe"];
      cpc::string instanceId;
      assertSuccess(DeviceInfo::getInstanceId(instanceId)) << FAIL_LOG_FMT("unable to retrieve device ID for push account");
      ASSERT_TRUE(replaceJsonStringValue(subscribe, "sipInstance", ("<urn:uuid:" + instanceId + ">").c_str()));

      helper.setDefaultOptions(request, pushServerUrl, "POST", subscribe.size());
      request.setOpt(new curlpp::options::PostFields(subscribe));

      request.perform();
      ASSERT_EQ(curlpp::infos::ResponseCode::get(request), 200);

      // apparently if we send an invite too soon after subscribing it could be missed by the Alianza backend
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));

      SipConversation::SipConversationHandle bobCall;

      {
         resip::Lock lock(pushAccountMutex);

         bobCall = bob.conversation->createConversation(bob.handle);

         bob.conversation->addParticipant(bobCall,
            remoteSipUser.empty() ? alice.config.uri() : cpc::string(("sip:" + remoteSipUser).c_str()));
         bob.conversation->start(bobCall);

         ASSERT_TRUE(pushAccountCond.wait(pushAccountMutex, eventTimeout)) << FAIL_LOG_FMT("push account didn't receive push notification from push server");
      }

      auto aliceEvent = std::async(std::launch::async, [&]() {
         alice.enable(false);

         ASSERT_NO_FATAL_FAILURE(expectAccountRegistering(alice));
         ASSERT_NO_FATAL_FAILURE(expectAccountRegistered(alice));

         SipConversation::SipConversationHandle aliceCall;
         // the callerUriCheck below is to handle the case where the domain is different (alianza.sip.us.cymbus.net => sip.cymbus.net)
         ASSERT_NO_FATAL_FAILURE(expectNewConversationIncoming(alice, &aliceCall, 
            callerUriCheck.empty() ? bob.config.uri() : cpc::string(("sip:" + callerUriCheck).c_str()), true));

         ASSERT_NO_FATAL_FAILURE(assertSuccess(alice.conversation->sendRingingResponse(aliceCall)));
         ASSERT_NO_FATAL_FAILURE(expectConversationStateChanged(alice, aliceCall, SipConversation::ConversationState_LocalRinging));

         ASSERT_NO_FATAL_FAILURE(assertSuccess(alice.conversation->accept(aliceCall)));
         ASSERT_NO_FATAL_FAILURE(expectConversationMediaChanged(alice, aliceCall, SipConversation::MediaDirection_SendReceive));
         ASSERT_NO_FATAL_FAILURE(expectConversationStateChanged(alice, aliceCall, SipConversation::ConversationState_Connected));
         SipConversation::SipConversationState aliceConvState;
         alice.conversationState->getState(aliceCall, aliceConvState);
         ASSERT_NE(aliceConvState.localMediaInfo.size(), 0);
         //ASSERT_TRUE(resip::isEqualNoCase(resip::Data(aliceConvState.localMediaInfo[0].audioCodec.plname), assertaliceCodec));

         std::this_thread::sleep_for(std::chrono::milliseconds(7000));  // Bob takes 8 seconds, so we wait a few here

         ASSERT_NO_FATAL_FAILURE(expectConversationEnded(alice, aliceCall, SipConversation::ConversationEndReason_UserTerminatedRemotely));
      });

      auto bobEvent = std::async(std::launch::async, [&]() {
         // the remoteSipUser below is because sometimes we'll be calling using an extension
         ASSERT_NO_FATAL_FAILURE(expectNewConversationOutgoing(bob, bobCall, 
            remoteSipUser.empty() ? alice.config.uri() : cpc::string(("sip:" + remoteSipUser).c_str())));

         ASSERT_NO_FATAL_FAILURE(expectConversationStateChanged(bob, bobCall, SipConversation::ConversationState_RemoteRinging));
         ASSERT_NO_FATAL_FAILURE(expectConversationMediaChanged(bob, bobCall, SipConversation::MediaDirection_SendReceive));

         bool connected = false;
         while (!connected)
         {
            assertConversationStateChanged_ex(bob, bobCall, [&](const SipConversation::ConversationStateChangedEvent& evt)
            {
               if (evt.conversationState == SipConversation::ConversationState_Connected)
               {
                  safeCout(STATUS_LOG_FMT("first account detected establish call"));
                  connected = true;
               }
               else if (!(evt.conversationState == SipConversation::ConversationState_Early ||
                  evt.conversationState == SipConversation::ConversationState_RemoteRinging))
               {
                  FAIL() << FAIL_LOG_FMT("Conversation state went to unexpected " << evt.conversationState << " state");
               }
            });

            if (::testing::Test::HasFailure())
            {
               return;
            }
         }

         SipConversation::SipConversationState bobConvState;
         bob.conversationState->getState(bobCall, bobConvState);
         ASSERT_NE(bobConvState.localMediaInfo.size(), 0);

         std::this_thread::sleep_for(std::chrono::milliseconds(4000));
         bob.conversation->startDtmfTone(bobCall, 3, true);
         std::this_thread::sleep_for(std::chrono::milliseconds(4000));

         ASSERT_NO_FATAL_FAILURE(assertSuccess(bob.conversation->end(bobCall)));
         ASSERT_NO_FATAL_FAILURE(expectConversationEnded(bob, bobCall, SipConversation::ConversationEndReason_UserTerminatedLocally));
      });

      waitFor2(aliceEvent, bobEvent);
      ASSERT_FALSE(HasFatalFailure());
   }
   catch (curlpp::RuntimeError& e)
   {
      FAIL() << FAIL_LOG_FMT("cURL runtime error: " << e.what());
   }

   safeCout(STATUS_LOG_FMT("sip_push_server_load_test_tool completed successfully"));
}

#endif // #ifndef _WIN32