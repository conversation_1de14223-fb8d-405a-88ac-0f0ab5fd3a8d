#if _WIN32
#include "stdafx.h"
#endif

#include <cstdlib>
#include <atomic>
#include <rutil/Socket.hxx>
#include "brand_branded.h"

#include "../test_framework/xmpp_test_helper.h"

#include "xmpp/XmppAccountInterface.h"

using namespace CPCAPI2;
using namespace CPCAPI2::XmppAccount;

class XmppTestTool : public CpcapiAutoTest
{
public:
   XmppTestTool() {}
   virtual ~XmppTestTool() {}
};

#define STATUS_LOG_FMT(x) "[[OK[" << x << "]]]"
#define FAIL_LOG_FMT(x) "[[NOK[" << x << "]]]"

void setupXmppAccounts(XmppTestAccount& alice, XmppTestAccount& bob);
void RosterTest(XmppTestAccount& alice, XmppTestAccount& bob, int& bobIndexInAliceRoster, int& aliceIndexInBobRoster);
void PresenceTest(XmppTestAccount& alice, XmppTestAccount& bob, const int& bobIndexInAliceRoster, const int& aliceIndexInBobRoster);
void VcardTest(XmppTestAccount& alice, XmppTestAccount& bob);
void ImTest(XmppTestAccount& alice, XmppTestAccount& bob);
void MUCTest(XmppTestAccount& alice, XmppTestAccount& bob);


TEST_F(XmppTestTool, test)
{
   putenv("CPCAPI2_AUTO_CREATE_XMPP_USERS=0");
   XmppTestAccount alice("alice", Account_Init);
   XmppTestAccount bob("bob", Account_Init);
   setupXmppAccounts(alice, bob);
   safeCout(STATUS_LOG_FMT("xmpp accoutns registered successfully"));
   assertXmppVCardFetched(alice, [&](const CPCAPI2::XmppVCard::VCardFetchedEvent& evt){});
   assertXmppVCardFetched(bob, [&](const CPCAPI2::XmppVCard::VCardFetchedEvent& evt){});
   int bobIndexInAliceRoster = 0, aliceIndexInBobRoster = 0;
   ASSERT_NO_FATAL_FAILURE(RosterTest(alice, bob, bobIndexInAliceRoster, aliceIndexInBobRoster));
   ASSERT_NO_FATAL_FAILURE(PresenceTest(alice, bob, bobIndexInAliceRoster, aliceIndexInBobRoster));
   ASSERT_NO_FATAL_FAILURE(VcardTest(alice, bob));
   ASSERT_NO_FATAL_FAILURE(ImTest(alice, bob));
   ASSERT_NO_FATAL_FAILURE(MUCTest(alice, bob));
}

void setupXmppAccounts(XmppTestAccount& alice, XmppTestAccount& bob)
{
   std::string configFileName = "xmppTestToolAccounts_2.json";
   ASSERT_TRUE(TestEnvironmentConfig::getResourceFile(configFileName, configFileName));
   resip::Data configFile = configFileName.c_str();

   std::ifstream in(configFile.c_str());
   ASSERT_TRUE(in.is_open()) << FAIL_LOG_FMT("failed when opening " << configFileName.c_str() << " file");
   
   std::ostringstream iss;
   iss << in.rdbuf() << std::flush;
   
   cpc::string doc = iss.str().c_str();
   cpc::vector<XmppAccountSettings> accounts;
   
   ASSERT_EQ(kSuccess, alice.account->decodeProvisioningResponse(doc, accounts)) << FAIL_LOG_FMT("failed when getting accounts settings from the json file");
   ASSERT_EQ(accounts.size(), 2) << FAIL_LOG_FMT("the tool should have two xmpp accounts settings");
   
   alice.config.settings = accounts[0];
   alice.account->configureDefaultAccountSettings(alice.handle, accounts[0]);
   alice.account->applySettings(alice.handle);
   bob.config.settings = accounts[1];
   bob.account->configureDefaultAccountSettings(bob.handle, accounts[1]);
   bob.account->applySettings(bob.handle);
   
   ASSERT_NO_FATAL_FAILURE(alice.enable()) << FAIL_LOG_FMT("Failure in first Xmpp account registration");
   
   ASSERT_NO_FATAL_FAILURE(bob.enable()) << FAIL_LOG_FMT("Failure in second Xmpp account registration");
}

void RosterTest(XmppTestAccount& alice, XmppTestAccount& bob, int& bobIndexInAliceRoster, int& aliceIndexInBobRoster)
{
   assertXmppRosterUpdate(alice, [](const CPCAPI2::XmppRoster::XmppRosterUpdateEvent& evt)
   {
      ASSERT_TRUE(evt.fullUpdate);
      ASSERT_GT(evt.added.size(), 0);
   });
   assertXmppRosterUpdate(bob, [](const CPCAPI2::XmppRoster::XmppRosterUpdateEvent& evt)
   {
      ASSERT_TRUE(evt.fullUpdate);
      ASSERT_GT(evt.added.size(), 0);
   });
   CPCAPI2::XmppRoster::XmppRosterManager* aliceRoster = alice.roster;
   cpc::vector<XmppRoster::RosterItem> aliceItems;
   ASSERT_EQ(kSuccess, alice.roster->getRosterState(alice.rosterHandle, aliceItems)) << FAIL_LOG_FMT("failed to get alice's roster");
   ASSERT_GT(aliceItems.size(), 0) << FAIL_LOG_FMT("alice's roster is empty");
   bool foundAccount = false;
   for (int i = 0; i < aliceItems.size(); i++)
   {
      if (aliceItems[i].address == bob.config.bare())
      {
         ASSERT_EQ(aliceItems[i].address, bob.config.bare());
         ASSERT_EQ(aliceItems[i].subscription, XmppRoster::SubscriptionState_InOut) << FAIL_LOG_FMT("these two account are not subscribed to each other");
         bobIndexInAliceRoster = i;
         foundAccount = true;
         safeCout(STATUS_LOG_FMT("found " << aliceItems[i].displayName << "(bob) in "<< alice.config.settings.username << "'s(alice) roster"));
      }
      else
      {
         safeCout(i << " found " << aliceItems[i].displayName << " in alice's roster");
      }
   }
   if(!foundAccount)
   {
      FAIL() << FAIL_LOG_FMT("Could not find " << bob.config.settings.username << " in "<< alice.config.settings.username << "'s roster");
   }
   
   foundAccount = false;
   cpc::vector<XmppRoster::RosterItem> bobItems;
   ASSERT_EQ(kSuccess, bob.roster->getRosterState(bob.rosterHandle, bobItems)) << FAIL_LOG_FMT("failed to get bob's roster");
   ASSERT_GT(bobItems.size(), 0) << FAIL_LOG_FMT("bob's roster is empty");
   for (int i = 0; i < bobItems.size(); i++)
   {
      if (bobItems[i].address == alice.config.bare())
      {
         ASSERT_EQ(bobItems[i].address, alice.config.bare());
         ASSERT_EQ(bobItems[i].subscription, XmppRoster::SubscriptionState_InOut) << FAIL_LOG_FMT("these two account are not subscribed to each other");
         aliceIndexInBobRoster = i;
         foundAccount = true;
         safeCout(STATUS_LOG_FMT(i << " found " << bobItems[i].displayName << "(alice) in "<< bob.config.settings.username << "'s(bob) roster"));
      }
      else
      {
         safeCout(i << " found " << bobItems[i].displayName << " in bob's roster");
      }
   }
   if(!foundAccount)
   {
      FAIL() << FAIL_LOG_FMT("Could not find " << alice.config.settings.username << " in "<< bob.config.settings.username << "'s roster");
   }
}

void PresenceTest(XmppTestAccount& alice, XmppTestAccount& bob, const int& bobIndexInAliceRoster, const int& aliceIndexInBobRoster)
{
   //bob change presence status
   ASSERT_EQ(kSuccess, bob.account->publishPresence(bob.handle, XmppRoster::PresenceType_Away, "Away")) << FAIL_LOG_FMT("bob failed to change its presence status");
   safeCout(STATUS_LOG_FMT("bob presence state changed from Available to Away"));
   std::this_thread::sleep_for(std::chrono::seconds(1));
   cpc::vector<XmppRoster::RosterItem> aliceItems;
   ASSERT_EQ(kSuccess, alice.roster->getRosterState(alice.rosterHandle, aliceItems)) << FAIL_LOG_FMT("failed to get alice's roster");
   {
      ASSERT_GE(aliceItems.size(), bobIndexInAliceRoster) << FAIL_LOG_FMT("alice's roster items has changed");
      ASSERT_EQ(aliceItems[bobIndexInAliceRoster].address, bob.config.bare()) << FAIL_LOG_FMT("check presence on the wrong account");
      ASSERT_GT(aliceItems[bobIndexInAliceRoster].resources.size(), 0);
      safeCout("resources size: " << aliceItems[bobIndexInAliceRoster].resources.size());
      //safeCout("0:   " << aliceItems[bobIndexInAliceRoster].resources[0].presenceType << "  1:   " << aliceItems[bobIndexInAliceRoster].resources[1].presenceType);
      ASSERT_EQ(aliceItems[bobIndexInAliceRoster].resources.back().presenceType, XmppRoster::PresenceType_Away) << FAIL_LOG_FMT("bob's presence status is wrong");
   }
   safeCout(STATUS_LOG_FMT("alice verified bob's presence changed to Away"));
   
   ASSERT_EQ(kSuccess, alice.account->publishPresence(alice.handle, XmppRoster::PresenceType_DND, "Do Not Disturb")) << FAIL_LOG_FMT("alice failed to change its presence status");
   safeCout(STATUS_LOG_FMT("alice presence state changed from Available to Do Not Disturb"));
   std::this_thread::sleep_for(std::chrono::seconds(1));
   cpc::vector<XmppRoster::RosterItem> bobItems;
   ASSERT_EQ(kSuccess, bob.roster->getRosterState(bob.rosterHandle, bobItems)) << FAIL_LOG_FMT("failed to get bob's roster");
   {
      ASSERT_GE(bobItems.size(), aliceIndexInBobRoster) << FAIL_LOG_FMT("bob's roster items has changed");
      ASSERT_EQ(bobItems[aliceIndexInBobRoster].address, alice.config.bare()) << FAIL_LOG_FMT("check presence on the wrong account");
      ASSERT_GT(bobItems[aliceIndexInBobRoster].resources.size(), 0);
      ASSERT_EQ(bobItems[aliceIndexInBobRoster].resources.back().presenceType, XmppRoster::PresenceType_DND) << FAIL_LOG_FMT("alice's presence status is wrong");
   }
   safeCout(STATUS_LOG_FMT("bob verified alice's presence changed to Do Not Disturb"));
   
   ASSERT_EQ(kSuccess, bob.account->publishPresence(bob.handle, XmppRoster::PresenceType_DND, "Do Not Disturb"))  << FAIL_LOG_FMT("bob failed to change its presence status");
   safeCout(STATUS_LOG_FMT("bob presence state changed from Away to Do Not Disturb"));
   std::this_thread::sleep_for(std::chrono::seconds(1));
   ASSERT_EQ(kSuccess, alice.roster->getRosterState(alice.rosterHandle, aliceItems)) << FAIL_LOG_FMT("failed to get alice's roster");
   {
      ASSERT_GE(aliceItems.size(), bobIndexInAliceRoster) << FAIL_LOG_FMT("alice's roster items has changed");
      ASSERT_EQ(aliceItems[bobIndexInAliceRoster].address, bob.config.bare()) << FAIL_LOG_FMT("check presence on the wrong account");
      ASSERT_GT(aliceItems[bobIndexInAliceRoster].resources.size(), 0);
      ASSERT_EQ(aliceItems[bobIndexInAliceRoster].resources.back().presenceType, XmppRoster::PresenceType_DND) << FAIL_LOG_FMT("bob's presence status is wrong");
   }
   safeCout(STATUS_LOG_FMT("alice verified bob's presence changed to Do Not Disturb"));
   
   ASSERT_EQ(kSuccess, alice.account->publishPresence(alice.handle, XmppRoster::PresenceType_Away, "AWAY"))  << FAIL_LOG_FMT("alice failed to change its presence status");
   safeCout(STATUS_LOG_FMT("alice presence state changed from Do Not Disturb to Away"));
   std::this_thread::sleep_for(std::chrono::seconds(1));
   ASSERT_EQ(kSuccess, bob.roster->getRosterState(bob.rosterHandle, bobItems)) << FAIL_LOG_FMT("failed to get bob's roster");
   {
      ASSERT_GE(bobItems.size(), aliceIndexInBobRoster) << FAIL_LOG_FMT("bob's roster items has changed");
      ASSERT_EQ(bobItems[aliceIndexInBobRoster].address, alice.config.bare()) << FAIL_LOG_FMT("check presence on the wrong account");
      ASSERT_GT(bobItems[aliceIndexInBobRoster].resources.size(), 0);
      ASSERT_EQ(bobItems[aliceIndexInBobRoster].resources.back().presenceType, XmppRoster::PresenceType_Away) << FAIL_LOG_FMT("alice's presence status is wrong");
   }
   safeCout(STATUS_LOG_FMT("bob verified alice's presence changed to Away"));
}

void VcardTest(XmppTestAccount& alice, XmppTestAccount& bob)
{
   ASSERT_EQ(kSuccess, alice.vcardManager->fetchVCard(alice.vcardHandle, bob.config.bare()));
   assertXmppVCardFetched(alice, [&](const CPCAPI2::XmppVCard::VCardFetchedEvent& evt)
   {
      ASSERT_EQ(alice.handle, evt.account);
      ASSERT_EQ(alice.vcardHandle, evt.handle);
      ASSERT_EQ(bob.config.bare(), evt.jid) << FAIL_LOG_FMT("alice fetched vcard from wrong user");
      safeCout(STATUS_LOG_FMT("alice fetched bob's VCard"));
      ASSERT_GT(evt.detail.emailList.size(), 0) << FAIL_LOG_FMT("bob's vcard email list is empty");
      ASSERT_GT(evt.detail.emailList[0].userid.size(), 0) << FAIL_LOG_FMT("bob's vcard email value is empty");
      ASSERT_GT(evt.detail.telephoneList.size(), 0) << FAIL_LOG_FMT("bob's vcard telephone list is empty");
      ASSERT_GT(evt.detail.telephoneList[0].number.size(), 0) << FAIL_LOG_FMT("bob's vcard tel number value is empty");
      ASSERT_GT(evt.detail.cpcollab.size(), 0) << FAIL_LOG_FMT("bob's vcard conference value is empty");
   });
   safeCout(STATUS_LOG_FMT("alice verified bob's VCard values"));
   ASSERT_EQ(kSuccess, bob.vcardManager->fetchVCard(bob.vcardHandle, alice.config.bare()));
   assertXmppVCardFetched(bob, [&](const CPCAPI2::XmppVCard::VCardFetchedEvent& evt)
   {
      ASSERT_EQ(bob.handle, evt.account);
      ASSERT_EQ(bob.vcardHandle, evt.handle);
      ASSERT_EQ(alice.config.bare(), evt.jid) << FAIL_LOG_FMT("bob fetched vcard from wrong user");
      safeCout(STATUS_LOG_FMT("bob fetched alice's VCard"));
      ASSERT_GT(evt.detail.emailList.size(), 0) << FAIL_LOG_FMT("alice's vcard email list is empty");
      ASSERT_GT(evt.detail.emailList[0].userid.size(), 0) << FAIL_LOG_FMT("alice's vcard email value is empty");
      ASSERT_GT(evt.detail.telephoneList.size(), 0) << FAIL_LOG_FMT("alice's vcard telephone list is empty");
      ASSERT_GT(evt.detail.telephoneList[0].number.size(), 0) << FAIL_LOG_FMT("alice's vcard tel number value is empty");
      ASSERT_GT(evt.detail.cpcollab.size(), 0) << FAIL_LOG_FMT("alice's vcard conference value is empty");
   });
   safeCout(STATUS_LOG_FMT("bob verified alice's VCard values"));
}


void ImTest(XmppTestAccount& alice, XmppTestAccount& bob)
{
   //IM tests
   cpc::string aliceMessage = "a message from xmpp test tool - from ";
   aliceMessage.append(alice.config.settings.username);
   aliceMessage.append(" to ");
   aliceMessage.append(bob.config.settings.username);
   aliceMessage.append(resip::Random::getCryptoRandomHex(5).c_str()); //add some random hexs to distinguish the message content from last test run
   
   cpc::string bobMessage = "a message from xmpp test tool - from ";
   bobMessage.append(bob.config.settings.username);
   bobMessage.append(" to ");
   bobMessage.append(alice.config.settings.username);
   bobMessage.append(resip::Random::getCryptoRandomHex(5).c_str());
   
   bool messageFromLastTestRun = true;
   
   
   CPCAPI2::XmppChat::XmppChatHandle alice_h;
   CPCAPI2::XmppChat::NewChatEvent alice_evt;
   bool bobToAliceMsgDelayed = cpcExpectEvent(alice.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), alice_h, alice_evt);

   CPCAPI2::XmppChat::XmppChatHandle bob_h;
   CPCAPI2::XmppChat::NewChatEvent bob_evt;
   bool aliceToBobMsgDelayed = cpcExpectEvent(bob.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), bob_h, bob_evt);

   CPCAPI2::XmppChat::XmppChatHandle endChat_h;
   CPCAPI2::XmppChat::ChatEndedEvent endChat_evt;
   if(bobToAliceMsgDelayed && aliceToBobMsgDelayed) //both message a->b and message b->a got sent again
   {
      alice.chat->end(alice_h);
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onChatEnded", 15000, CPCAPI2::test::AlwaysTruePred(), endChat_h, endChat_evt)) << FAIL_LOG_FMT("alice - failed to end the chat");
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onChatEnded", 15000, CPCAPI2::test::AlwaysTruePred(), endChat_h, endChat_evt)) << FAIL_LOG_FMT("failed to end the chat");
   }
   else if(bobToAliceMsgDelayed) //message b -> a delayed
   {
      alice.chat->end(alice_h);
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), alice_h, alice_evt));
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onChatEnded", 15000, CPCAPI2::test::AlwaysTruePred(), endChat_h, endChat_evt)) << FAIL_LOG_FMT("alice - failed to end the chat from previous test run");
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onChatEnded", 15000, CPCAPI2::test::AlwaysTruePred(), endChat_h, endChat_evt)) << FAIL_LOG_FMT("failed to end the chat");
   }
   else if(aliceToBobMsgDelayed) //message a -> b delayed
   {
      bob.chat->end(bob_h); //bob received delayed message sent by alice from last test run??
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), alice_h, alice_evt));
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onChatEnded", 15000, CPCAPI2::test::AlwaysTruePred(), endChat_h, endChat_evt)) << FAIL_LOG_FMT("bob - failed to end the chat from previous test run");
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onChatEnded", 15000, CPCAPI2::test::AlwaysTruePred(), endChat_h, endChat_evt)) << FAIL_LOG_FMT("alice - failed to end the chat from previous test run");
   }
   //end all chats from last test run
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   
   //alice start chat
   CPCAPI2::XmppChat::XmppChatHandle aliceChat = alice.chat->createChat(alice.handle);
//   safeCout("alice chat handle" <<  aliceChat);
   ASSERT_EQ(kSuccess, alice.chat->addParticipant(aliceChat, bob.config.bare())) << FAIL_LOG_FMT("alice failed to add participant to a chat");
   ASSERT_EQ(kSuccess, alice.chat->start(aliceChat)) << FAIL_LOG_FMT("alice failed to start to a chat");
   {
      CPCAPI2::XmppChat::XmppChatHandle h;
      CPCAPI2::XmppChat::NewChatEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.account, alice.handle);
      ASSERT_EQ(evt.chatType, CPCAPI2::XmppChat::ChatType_Outgoing) << FAIL_LOG_FMT("alice's chat type is wrong");
      ASSERT_EQ(aliceChat, h) << FAIL_LOG_FMT("chat handler mismatched");
   }

   //alice send message to bob
   CPCAPI2::XmppChat::XmppChatMessageHandle cm1 = alice.chat->sendMessage(aliceChat, aliceMessage);
   {
      safeCout(STATUS_LOG_FMT("alice is sending out a message to bob"));
      CPCAPI2::XmppChat::XmppChatHandle h;
      CPCAPI2::XmppChat::SendMessageSuccessEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onSendMessageSuccess", 10000, CPCAPI2::test::AlwaysTruePred(), h, evt)) << FAIL_LOG_FMT("alice failed to send the message");
      ASSERT_EQ(evt.message, cm1) << FAIL_LOG_FMT("chat message handler mismatched");
      safeCout("alice chat handler values onSendMessageSuccess:  " << aliceChat << "     h: " <<  h << "message handle : " << cm1);
   }
   
   //bob on newChat
   CPCAPI2::XmppChat::XmppChatHandle bobChat = 0;
   {
      CPCAPI2::XmppChat::XmppChatHandle h;
      CPCAPI2::XmppChat::NewChatEvent evt;
      
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_NE(h, 0);
//      safeCout("bob received chat handler values:  " << "     h: " <<  h);
      ASSERT_EQ(evt.chatType, CPCAPI2::XmppChat::ChatType_Incoming);
      bobChat = h;
   }
   
   //bob received message
   {
      CPCAPI2::XmppChat::XmppChatHandle h;
      CPCAPI2::XmppChat::NewMessageEvent evt;
      while(messageFromLastTestRun)
      {
         ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewMessage", 10000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         safeCout("bob received message isDelayedDelivery:   " << evt.isDelayedDelivery << " handler : " << h << "  message content:  " << evt.messageContent);
         if (!evt.isDelayedDelivery)
         {
            messageFromLastTestRun = false;
            ASSERT_EQ(evt.from, alice.config.bare());
            ASSERT_EQ(evt.messageContent, aliceMessage) << FAIL_LOG_FMT("message received by bob does not match with the content sent from alice");
            ASSERT_EQ(h, bobChat);
            ASSERT_TRUE(evt.message != 0);
//            safeCout("bob received messageHandler:   " << evt.message);
            CPCAPI2::XmppChat::XmppChatMessageHandle bobRecvdCm1 = evt.message;
            bob.chat->notifyMessageDelivered(bobChat, bobRecvdCm1, CPCAPI2::XmppChat::MessageDeliveryStatus_Delivered);
         }
      }
   }
   safeCout(STATUS_LOG_FMT("bob received the message sent from alice"));
   
   //alice Wait for the message delivery notification (from Bob)
   while (true)
   {
      CPCAPI2::XmppChat::XmppChatHandle h;
      CPCAPI2::XmppChat::MessageDeliveredEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onMessageDelivered", 10000, CPCAPI2::test::AlwaysTruePred(), h, evt)) << FAIL_LOG_FMT("alice did not received the message delivered confirmation from bob");
      if (evt.message == cm1)
      {
         safeCout("alice received onMessageDelivered handler : " << h);
         break;
      }
   }
   
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   ASSERT_EQ(kSuccess, bob.chat->setIsComposingMessage(bobChat)) << FAIL_LOG_FMT("bob - failed to send isComposing");
   
   {
      CPCAPI2::XmppChat::XmppChatHandle h;
      CPCAPI2::XmppChat::IsComposingMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onIsComposingMessage", 10000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.state, CPCAPI2::XmppChat::IsComposingMessageState_Active);
//      safeCout("alice chat handler values onIsComposingMessage:  " << "     h: " <<  h);
   }

   CPCAPI2::XmppChat::XmppChatMessageHandle bobCm1 = bob.chat->sendMessage(bobChat, bobMessage);
   
   {
      safeCout(STATUS_LOG_FMT("bob is sending out a message to alice"));
      CPCAPI2::XmppChat::XmppChatHandle h;
      CPCAPI2::XmppChat::SendMessageSuccessEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onSendMessageSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt)) << FAIL_LOG_FMT("bob failed to send the message");
      ASSERT_NE(evt.message, 0);
      ASSERT_EQ(evt.message, bobCm1);
      ASSERT_EQ(h, bobChat);
   }
   
   messageFromLastTestRun = true;
   {
      CPCAPI2::XmppChat::XmppChatHandle h;
      CPCAPI2::XmppChat::NewMessageEvent evt;
      while(messageFromLastTestRun)
      {
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         safeCout("alice received message isDelayedDelivery:   " << evt.isDelayedDelivery << " handler : " << h << "  message content:  " << evt.messageContent);
         if (!evt.isDelayedDelivery)
         {
            messageFromLastTestRun = false;
            ASSERT_EQ(evt.from, bob.config.bare());
            ASSERT_EQ(evt.messageContent, bobMessage) << FAIL_LOG_FMT("message received by alice does not match with the content sent from bob");
            CPCAPI2::XmppChat::XmppChatMessageHandle aliceRecvdCm1 = evt.message;
            alice.chat->notifyMessageDelivered(aliceChat, aliceRecvdCm1, CPCAPI2::XmppChat::MessageDeliveryStatus_Delivered);
         }
      }
   }
   safeCout(STATUS_LOG_FMT("alice received the message sent from bob"));
   
   while (true)
   {
      // Wait for the message delivery notification (from alice)
      CPCAPI2::XmppChat::XmppChatHandle h;
      CPCAPI2::XmppChat::MessageDeliveredEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onMessageDelivered", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt)) << FAIL_LOG_FMT("bob did not received the message delivered confirmation from alice");
      if (evt.message == bobCm1)
      {
         safeCout("bob received onMessageDelivered handler : " << h);
         break;
      }
   }
   
   ASSERT_EQ(kSuccess, alice.chat->validateChatHandle(alice.handle, aliceChat)) <<  FAIL_LOG_FMT("alice - failed to validate chat handler ");
   {
      CPCAPI2::XmppChat::XmppChatHandle h;
      CPCAPI2::XmppChat::ValidateChatHandleEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onValidateChatHandleResult", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_TRUE(evt.chatHandleValid) <<  FAIL_LOG_FMT("alice - invalid chat handler ");
   }
   
   
   ASSERT_EQ(kSuccess, alice.chat->end(aliceChat)) << FAIL_LOG_FMT("alice - failed to end the chat");
   
   {
      CPCAPI2::XmppChat::XmppChatHandle h;
      CPCAPI2::XmppChat::ChatEndedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onChatEnded", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt)) << FAIL_LOG_FMT("alice - failed to end the chat");
      ASSERT_EQ(evt.endReason, CPCAPI2::XmppChat::ChatEndReason_UserTerminatedLocally);
      ASSERT_EQ(h, aliceChat);
//      safeCout("alice chat handler values onChatEnded:  " << "     h: " <<  h);
   }
   
   {
      CPCAPI2::XmppChat::XmppChatHandle h;
      CPCAPI2::XmppChat::ChatEndedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onChatEnded", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt)) << FAIL_LOG_FMT("failed to end the chat");
      ASSERT_EQ(evt.endReason, CPCAPI2::XmppChat::ChatEndReason_UserTerminatedRemotely);
      ASSERT_EQ(h, bobChat);
//      safeCout("bob received onChatEnded handler : " << h);
   }
   
   ASSERT_EQ(kSuccess, alice.chat->validateChatHandle(alice.handle, aliceChat)) <<  FAIL_LOG_FMT("alice - failed to validate chat handler ");
   {
      CPCAPI2::XmppChat::XmppChatHandle h;
      CPCAPI2::XmppChat::ValidateChatHandleEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onValidateChatHandleResult", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_FALSE(evt.chatHandleValid) <<  FAIL_LOG_FMT("alice - chat handler is still valid after chat ended ");
   }
}


void MUCTest(XmppTestAccount& alice, XmppTestAccount& bob)
{
   {
      XmppAccountHandle h;
      CPCAPI2::XmppMultiUserChat::ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt)) << FAIL_LOG_FMT("alice - failed to enable MUC service");
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt)) << FAIL_LOG_FMT("bob - failed to enable MUC service");
   }

   cpc::string aliceMessage = "a message from xmpp test tool - from ";
   aliceMessage.append(alice.config.settings.username);
   aliceMessage.append(" to ");
   aliceMessage.append(bob.config.settings.username);
   aliceMessage.append(resip::Random::getCryptoRandomHex(5).c_str()); //add some random hexs to distinguish the message content from last test run

   cpc::string bobMessage = "a message from xmpp test tool - from ";
   bobMessage.append(bob.config.settings.username);
   bobMessage.append(" to ");
   bobMessage.append(alice.config.settings.username);
   bobMessage.append(resip::Random::getCryptoRandomHex(5).c_str());

   cpc::string room = resip::Random::getCryptoRandomHex(16).c_str();
   CPCAPI2::XmppMultiUserChat::XmppMultiUserChatHandle alice_h = alice.mucManager->create(alice.handle, room);

   {
      CPCAPI2::XmppMultiUserChat::XmppMultiUserChatHandle h;
      CPCAPI2::XmppMultiUserChat::RoomConfig rc;
      rc.createIfNotExisting = true;
      rc.isInstant = true;
      alice.mucManager->join(alice_h, rc, alice.config.nick, "", "message:0");
      CPCAPI2::XmppMultiUserChat::MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 50000, CPCAPI2::test::AlwaysTruePred(), h, evt)) << FAIL_LOG_FMT("alice - failed to create room " << room);
   }

   {
      CPCAPI2::XmppMultiUserChat::ParticipantSelfUpdatedEvent evt;
      CPCAPI2::XmppMultiUserChat::XmppMultiUserChatHandle h;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantSelfUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.state.role, CPCAPI2::XmppMultiUserChat::RoleModerator);
   }

   alice.mucManager->invite(alice_h, bob.config.bare(), "invite");

   CPCAPI2::XmppMultiUserChat::XmppMultiUserChatHandle bobChat;

   while (true)
   {
      CPCAPI2::XmppMultiUserChat::MultiUserChatInvitationReceivedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatInvitationReceived", 5000, CPCAPI2::test::AlwaysTruePred(), bobChat, evt)) << FAIL_LOG_FMT("bob - failed to receive invitation from alice to join room " << room);

      if (!evt.isDelayedDelivery)
      {
         ASSERT_EQ(evt.room, room) << FAIL_LOG_FMT("bob - failed to receive invitation from alice to join room " << room);
         break;
      }
   }

   bob.mucManager->accept(bobChat, bob.config.nick, "message:0");

   {
      CPCAPI2::XmppMultiUserChat::XmppMultiUserChatHandle h;
      CPCAPI2::XmppMultiUserChat::ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt)) << FAIL_LOG_FMT("alice failed to invite bob to join room " << room);
   }

   {
      CPCAPI2::XmppMultiUserChat::XmppMultiUserChatHandle h;
      CPCAPI2::XmppMultiUserChat::ParticipantSelfUpdatedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onParticipantSelfUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.state.role, CPCAPI2::XmppMultiUserChat::RoleParticipant);
   }

   {
      CPCAPI2::XmppMultiUserChat::XmppMultiUserChatHandle h;
      CPCAPI2::XmppMultiUserChat::ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt)) << FAIL_LOG_FMT("bob failed to join room " << room);
   }

   alice.mucManager->sendMessage(alice_h, aliceMessage, "");

   {
      CPCAPI2::XmppMultiUserChat::XmppMultiUserChatHandle h;
      CPCAPI2::XmppMultiUserChat::MultiUserChatNewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatNewMessage", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt)) << FAIL_LOG_FMT("alice failed to receive own message");
      ASSERT_EQ(evt.plain, aliceMessage) << FAIL_LOG_FMT("alice received a mismatching own message");
   }

   {
      CPCAPI2::XmppMultiUserChat::XmppMultiUserChatHandle h;
      CPCAPI2::XmppMultiUserChat::MultiUserChatNewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatNewMessage", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt)) << FAIL_LOG_FMT("bob failed to receive message from alice");
      ASSERT_EQ(evt.plain, aliceMessage) << FAIL_LOG_FMT("bob received a mismatching message from alice");
   }

   bob.mucManager->sendMessage(bobChat, bobMessage, "");

   {
      CPCAPI2::XmppMultiUserChat::XmppMultiUserChatHandle h;
      CPCAPI2::XmppMultiUserChat::MultiUserChatNewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatNewMessage", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt)) << FAIL_LOG_FMT("alice failed to receive message from bob");
      ASSERT_EQ(evt.plain, bobMessage) << FAIL_LOG_FMT("alice received a mismatching message from bob");
   }

   {
      CPCAPI2::XmppMultiUserChat::XmppMultiUserChatHandle h;
      CPCAPI2::XmppMultiUserChat::MultiUserChatNewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatNewMessage", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt)) << FAIL_LOG_FMT("bob failed to receive own message");
      ASSERT_EQ(evt.plain, bobMessage) << FAIL_LOG_FMT("bob received a mismatching own message");
   }

   alice.mucManager->destroyRoom(alice_h);

   {
      CPCAPI2::XmppMultiUserChat::XmppMultiUserChatHandle h;
      CPCAPI2::XmppMultiUserChat::ParticipantSelfUpdatedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantSelfUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_TRUE(evt.state.isRoomDestroyed) << FAIL_LOG_FMT("alice cannot leave destroyed room " << room);
   }

   {
      CPCAPI2::XmppMultiUserChat::XmppMultiUserChatHandle h;
      CPCAPI2::XmppMultiUserChat::ParticipantSelfUpdatedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onParticipantSelfUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_TRUE(evt.state.isRoomDestroyed) << FAIL_LOG_FMT("bob cannot leave destroyed room " << room);
   }
}
