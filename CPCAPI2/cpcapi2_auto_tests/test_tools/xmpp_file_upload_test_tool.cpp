#if _WIN32
#include "stdafx.h"
#endif

#include <cstdlib>
#include <functional>
#include <regex>
#include <atomic>
#include <iomanip>
#include <rutil/Socket.hxx>
#include "brand_branded.h"
#include "json/JsonHelper.h"

#include "../test_framework/xmpp_test_helper.h"

#include "xmpp/XmppAccountInterface.h"
#include "xmpp/XmppAccountInternal.h"

#include <boost/asio.hpp>
#include <boost/lexical_cast.hpp>

#ifndef WIN32
#include <sys/resource.h>
#endif

#include "curlpp/Easy.hpp"
#include "curlpp/Options.hpp"
#include "util/CurlPPProgress.h"
#include "util/CurlPPHelper.h"
#include "util/CurlPPSSL.h"
#include "util/FileUtils.h"


using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::Licensing;
using namespace CPCAPI2::XmppAccount;
using namespace CPCAPI2::XmppFileTransfer;

struct XmppTestFileInfo
{
   std::string filename;
   int filesize;
   XmppTestFileInfo() : filename(""), filesize(0) {}
   XmppTestFileInfo(std::string filename_, int filesize_) : filename(filename_), filesize(filesize_) {}
};

// forward declarations
class XmppFileUploadStats;
struct XmppAccountSettingsEx;
class XmppFileUploadTestTool;

static std::recursive_mutex gSafeCoutMutex;
static bool gSafeCoutToggle = true;
static XmppFileUploadTestTool* gTool = NULL;
std::map<std::string, XmppTestAccount*> gXmppAccounts;
std::map<std::string, std::map<int, XmppTestFileInfo>> gRemoteFiles;
std::map<std::string, std::string> gAccountServices;
std::map<XmppAccountHandle, std::string> gXmppAccountHandles;


#undef safeCout
#define safeCout(e) { using namespace CPCAPI2::test; using namespace CPCAPI2; std::lock_guard<std::recursive_mutex> lock(gSafeCoutMutex); if (gSafeCoutToggle) { std::cout.flush(); std::cerr.flush(); std::cout << e << std::endl; std::cout.flush(); } }


class XmppFileUploadStats : public CPCAPI2::XmppAccount::XmppAccountHandler
{

public:

   XmppFileUploadStats() : mProcess(false)
   {
   }

   virtual~ XmppFileUploadStats()
   {
      stop();
   }

   struct UploadStats
   {
      struct Transfer
      {
         XmppFileTransferHandle transfer;
         XmppFileTransferItemHandle item;
         short percentage; // [ 0% ... 100% ]

         Transfer() : transfer(0), item(0), percentage(0) {};
         Transfer(XmppFileTransferHandle transfer_, XmppFileTransferItemHandle item_, short percentage_) : transfer(transfer_), item(item_), percentage(percentage_) {};
      };

      enum Status
      {
         Status_Waiting,
         Status_Progress,
         Status_Success,
         Status_Failure,
         Status_Unknown
      };

      std::string account;
      CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status accountStatus;
      std::string filename;
      long filesize;
      Status uploadStatus;
      Status downloadStatus;
      std::string starttime;
      std::string endtime;
      std::vector<Transfer> transfers;
      UploadStats() : account(""), accountStatus(XmppAccountStatusChangedEvent::Status_Disconnected), filename(""), filesize(0), uploadStatus(Status_Unknown), downloadStatus(Status_Unknown) {};
      UploadStats(const std::string& account_, const std::string& filename_, long filesize_, Status status_ = Status_Unknown) : account(account_), accountStatus(XmppAccountStatusChangedEvent::Status_Disconnected), filename(filename_), filesize(filesize_), uploadStatus(status_), downloadStatus(Status_Unknown) {};
   };

   // XmppAccountHandler
   virtual int onAccountStatusChanged(XmppAccountHandle account, const XmppAccountStatusChangedEvent& args);
   virtual int onError(XmppAccountHandle account, const CPCAPI2::XmppAccount::ErrorEvent& args) { return kSuccess; }
   virtual int onEntityTime(XmppAccountHandle account, const CPCAPI2::XmppAccount::EntityTimeEvent& args) { return kSuccess; }
   virtual int onEntityFeature(XmppAccountHandle account, const CPCAPI2::XmppAccount::EntityFeatureEvent& args) { return kSuccess; }
   virtual int onPrivateStorageData(XmppAccountHandle account, const CPCAPI2::XmppAccount::PrivateStorageDataEvent& args) { return kSuccess; }

   virtual void start();
   virtual void stop();
   void initialize(std::vector<XmppAccountSettingsEx>& accounts);
   void render();
   void updateFileProgress(const std::string& account, int filenumber, XmppFileTransferHandle transfer, FileTransferItemProgressEvent evt);
   void updateFileProgress(const std::string& account, int filenumber, UploadStats::Status status, long filesize);
   void updateDownloadStatus(const std::string& account, int filenumber, UploadStats::Status status);
   static std::string getStatus(UploadStats::Status status, bool download = false);
   static std::string getAccountStatus(CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status status);
   void setTableHeader(std::ostringstream& os);
   void setTableValues(std::ostringstream& os, UploadStats& stats);

   std::map<std::string, std::map<int, UploadStats>> mUploadStats; // mapping of xmpp account username and upload-stats for the files to be transferred
   std::atomic<bool> mProcess;
   std::unique_ptr<std::thread> mThread;
   std::map<XmppAccountHandle, XmppAccountStatusChangedEvent::Status> mStatus; // mapping of xmpp account handle and current account status
};


class XmppFileUploadTestTool : public CpcapiAutoTest
{

public:

   XmppFileUploadTestTool() :
      mMaximumSimultaneousFileUploads(100),
      mMaximumSimultaneousAccounts(100),
      mMaximumFileUploadThreads(100),
      mEnableConsole(false),
      mEventTimeout(10000),
      mUploadProgressInterval(2000),
      mActiveAccounts(0),
      mAccountActivationInterval(0),
      mFileTransmissionInterval(0),
      mFileTransmissionInitiationDelayInMilliseconds(0),
      mFdLimit(10000),
      mFileToUpload("")
   {
      mStats.reset(new XmppFileUploadStats());
      mUploadProgressTimer.reset(new boost::asio::deadline_timer(mIos));
      mSignals.reset(new boost::asio::signal_set(mIos, SIGINT));
      gTool = this;
   }
   virtual ~XmppFileUploadTestTool()
   {
   }

   void setupXmppAccounts();
   static void signalHandler(const boost::system::error_code& error, int signal_number);
   static const std::string generateRandomString(unsigned int size = 1024);

   void uploadProgressTimerHandler(const boost::system::error_code& error);
   void shutdownFileUploadTestTool();
   void updateFileProgress(const std::string& account, int filenumber, XmppFileTransferHandle transfer, FileTransferItemProgressEvent evt);
   void updateFileProgress(const std::string& account, int filenumber, XmppFileUploadStats::UploadStats::Status status, long filesize = 0);
   void updateDownloadStatus(const std::string& account, int filenumber, XmppFileUploadStats::UploadStats::Status status);

   void enableXmppAccounts(std::vector<XmppAccountSettingsEx>& accountSettings);
   void initiateTransfer(std::vector<XmppAccountSettingsEx>& accountSettings);
   void initiateTransferConcurrently(std::vector<XmppAccountSettingsEx>& accountSettings);

   std::vector<XmppAccountSettingsEx> mAccountSettings;
   std::unique_ptr<XmppFileUploadStats> mStats;

   boost::asio::io_service mIos;
   std::unique_ptr<boost::asio::deadline_timer> mUploadProgressTimer;
   std::unique_ptr<boost::asio::signal_set> mSignals;

   uint32_t mMaximumSimultaneousFileUploads;
   uint32_t mMaximumSimultaneousAccounts;
   uint32_t mMaximumFileUploadThreads;
   bool mEnableConsole;
   unsigned int mEventTimeout;
   unsigned int mUploadProgressInterval;
   unsigned int mActiveAccounts;
   unsigned int mAccountActivationInterval;
   unsigned int mFileTransmissionInterval;
   unsigned int mFileTransmissionInitiationDelayInMilliseconds;
   unsigned int mFdLimit;
   std::string mFileToUpload;

};

#define STATUS_LOG_FMT(x) "[[OK[" << x << "]]]"
#define FAIL_LOG_FMT(x) "[[NOK[" << x << "]]]"

struct XmppAccountSettingsEx : XmppAccountSettings
{
   XmppAccountSettingsEx(const XmppAccountSettings& settings, bool disableInitialSelfPresenceCheck, std::string fileToUpload_, unsigned int fileUploadCount_ = 1) :
      XmppAccountSettings(settings),
      disableInitialSelfPresenceCheck(disableInitialSelfPresenceCheck),
      fileToUpload(fileToUpload_),
      fileUploadCount(fileUploadCount_)
   {
   }

   bool disableInitialSelfPresenceCheck;
   std::string fileToUpload;
   unsigned int fileUploadCount;
};

void XmppFileUploadStats::initialize(std::vector<XmppAccountSettingsEx>& accounts)
{
   for (std::vector<XmppAccountSettingsEx>::iterator i = accounts.begin(); i != accounts.end(); ++i)
   {
      XmppAccountSettingsEx& account = (*i);
      // std::string filename("");
      // ASSERT_TRUE(TestEnvironmentConfig::getResourceFile(account.fileToUpload.c_str(), filename)) << FAIL_LOG_FMT("Failed to retrieve the file-upload configuration file: " << filename);
      // long filesize = CPCAPI2::FileUtils::GetFileSize(filename.c_str());
      // std::stringstream ss;
      // ss << filesize;
      // UploadStats stats(account.username.c_str(), account.fileToUpload.c_str(), ss.str(), UploadStats::Status_Unknown);
      UploadStats stats(account.username.c_str(), account.fileToUpload.c_str(), 0, UploadStats::Status_Unknown);
      std::map<int, UploadStats> accountFileStats;
      for (int index = 1; index <= account.fileUploadCount; ++index)
      {
         accountFileStats[index] = stats;
      }
      mUploadStats[account.username.c_str()] = accountFileStats;
   }
   start();
}

void XmppFileUploadStats::start()
{
   if (mProcess)
   {
      return;
   }
   mProcess = true;
   mThread.reset(new std::thread([&]()
   {
      while (mProcess)
      {
         render();
         std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      }
   }));
}

void XmppFileUploadStats::stop()
{
   if (mProcess)
   {
      mProcess = false;
      if (mThread)
      {
         mThread->join();
      }
      mThread.reset();
   }
}

int XmppFileUploadStats::onAccountStatusChanged(XmppAccountHandle account, const XmppAccountStatusChangedEvent& args)
{
   std::map<XmppAccountHandle, std::string>::iterator i = gXmppAccountHandles.find(account);
   if (i == gXmppAccountHandles.end())
   {
      safeCout("XmppFileUploadStats::onAccountStatusChanged(): no account found for account-handle: " << account);
      return kSuccess;;
   }
   for (std::map<std::string, std::map<int, UploadStats>>::iterator j = mUploadStats.begin(); j != mUploadStats.end(); j++)
   {
      if (j->first.compare(i->second) == 0)
      {
         for (std::map<int, UploadStats>::iterator k = (j->second).begin(); k != (j->second).end(); k++)
         {
            k->second.accountStatus = args.accountStatus;
         }
      }
   }
   mStatus[account] = args.accountStatus;
   return kSuccess;
}

void XmppFileUploadStats::render()
{
   std::ostringstream os;
   setTableHeader(os);
   for (std::map<std::string, std::map<int, UploadStats>>::iterator i = mUploadStats.begin(); i != mUploadStats.end(); i++)
   {
      for (std::map<int, UploadStats>::iterator j = (i->second).begin(); j != (i->second).end(); j++)
      {
         setTableValues(os, j->second);
      }
   }
   std::ofstream outfile;
   outfile.open("stats.out", std::ios::app);
   outfile << os.str();
   outfile.close();
}

void updateTime(std::string& entry)
{
   auto t = std::chrono::system_clock::to_time_t(std::chrono::system_clock::now());
   auto tt = *localtime(&t);
   char now [80];
   strftime(now, sizeof(now), " %T", &tt);
   entry = now;
}

void XmppFileUploadStats::updateFileProgress(const std::string& account, int filenumber, XmppFileTransferHandle transfer, FileTransferItemProgressEvent evt)
{
   std::map<std::string, std::map<int, UploadStats>>::iterator i = mUploadStats.find(account);
   if (i == mUploadStats.end())
   {
      safeCout("XmppFileUploadStats::updateFileProgress(): no stats monitored for account: " << account << " filenumber: " << filenumber);
      return;
   }

   std::map<int, UploadStats>::iterator j = (i->second).find(filenumber);
   if (j == (i->second).end())
   {
      safeCout("XmppFileUploadStats::updateFileProgress(): no stats monitored for filenumber: " << filenumber << " in account: " << account);
      return;
   }

   UploadStats& stats = j->second;
   if ((stats.uploadStatus == UploadStats::Status_Progress) && (stats.transfers.size() == 0))
   {
      if (stats.starttime.empty())
      {
         updateTime(stats.starttime);
      }
   }
   // if ((stats.transfers[stats.transfers.size() - 1].percentage == 100) && (stats.status == UploadStats::Status_Success))
   if ((stats.uploadStatus == UploadStats::Status_Success) || (stats.uploadStatus == UploadStats::Status_Failure) ||
      ((stats.transfers.size() > 0) && (stats.transfers[stats.transfers.size() - 1].percentage == 100) && (stats.uploadStatus == UploadStats::Status_Progress)))
   {
      if (stats.endtime.empty())
      {
         updateTime(stats.endtime);
      }
   }
   if ((stats.transfers.size() > 0) && (stats.transfers[stats.transfers.size() - 1].percentage == 100) && (stats.uploadStatus == UploadStats::Status_Progress))
   {
      // TODO: sometimes file-ended events were missed
      stats.uploadStatus = UploadStats::Status_Success;
   }

   bool found(false);
   for (std::vector<UploadStats::Transfer>::iterator k = stats.transfers.begin(); k != stats.transfers.end(); ++k)
   {
      if (((*k).transfer == transfer) && ((*k).item == evt.fileTransferItem))
      {
         found = true;
         (*k).percentage = evt.percent;
      }
   }
   if (!found)
   {
      UploadStats::Transfer transferStats(transfer, evt.fileTransferItem, evt.percent);
      stats.transfers.push_back(transferStats);
   }
}

void XmppFileUploadStats::updateFileProgress(const std::string& account, int filenumber, XmppFileUploadStats::UploadStats::Status status, long filesize)
{
   std::map<std::string, std::map<int, UploadStats>>::iterator i = mUploadStats.find(account);
   if (i == mUploadStats.end())
   {
      safeCout("XmppFileUploadStats::updateFileProgress(): no stats monitored for account: " << account << " filenumber: " << filenumber);
      return;
   }

   std::map<int, UploadStats>::iterator j = (i->second).find(filenumber);
   if (j == (i->second).end())
   {
      safeCout("XmppFileUploadStats::updateFileProgress(): no stats monitored for filenumber: " << filenumber << " in account: " << account);
      return;
   }

   UploadStats& stats = j->second;
   stats.uploadStatus = status;
   stats.filesize = filesize; // ((stats.filesize == 0) ? filesize : stats.filesize);
   if ((status != UploadStats::Status_Waiting) && (status != UploadStats::Status_Unknown))
   {
      if (stats.starttime.empty())
      {
         updateTime(stats.starttime);
      }
   }

   if ((status == UploadStats::Status_Success) || (status == UploadStats::Status_Failure))
   {
      if (stats.endtime.empty())
      {
         updateTime(stats.endtime);
      }
   }
}

void XmppFileUploadStats::updateDownloadStatus(const std::string& account, int filenumber, XmppFileUploadStats::UploadStats::Status status)
{
   std::map<std::string, std::map<int, UploadStats>>::iterator i = mUploadStats.find(account);
   if (i == mUploadStats.end())
   {
      safeCout("XmppFileUploadStats::updateDownloadStatus(): no stats monitored for account: " << account);
      return;
   }

   std::map<int, UploadStats>::iterator j = (i->second).find(filenumber);
   if (j == (i->second).end())
   {
      safeCout("XmppFileUploadStats::updateDownloadStatus(): no stats monitored for filenumber: " << filenumber << " in account: " << account);
      return;
   }

   UploadStats& stats = j->second;
   stats.downloadStatus = status;
}


std::string XmppFileUploadStats::getStatus(UploadStats::Status status, bool download)
{
   std::string ss("");
   switch (status)
   {
      case UploadStats::Status_Waiting: ss = "Waiting"; break;
      case UploadStats::Status_Progress: (download ? ss = "Downloading" : ss = "Uploading"); break;
      case UploadStats::Status_Success: ss = "Success"; break;
      case UploadStats::Status_Failure: ss = "Failure"; break;
      default: ss = "-"; break;
   }
   return ss;
}

std::string XmppFileUploadStats::getAccountStatus(CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status status)
{
   std::string ss("");
   switch (status)
   {
      case CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status_Connected: ss = "Connected"; break;
      case CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status_Failure: ss = "Failure"; break;
      case CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status_Disconnected: ss = "Disconnected"; break;
      case CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status_Connecting: ss = "Connecting"; break;
      case CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status_Disconnecting: ss = "Disconnecting"; break;
      case CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status_Destroyed: ss = "Destroyed"; break;
      case CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status_Resuming: ss = "Resuming"; break;
      case CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status_Resumed: ss = "Resumed"; break;
      default: ss = "-"; break;
   }
   return ss;
}

/** Convert double to string with specified number of places after the decimal and left padding. */
std::string prd(const int x, const int width, bool right = true)
{
   std::stringstream ss;
   ss << std::fixed << (right ? std::right : std::left);
   ss.fill(' ');               // fill space around displayed #
   ss.width(width);            // set  width around displayed #
   ss << x;
   return ss.str();
}

std::string prd(const std::string x, const int width, bool right = true)
{
   std::stringstream ss;
   ss << std::fixed << (right ? std::right : std::left);
   ss.fill(' ');               // fill space around displayed #
   ss.width(width);            // set  width around displayed #
   ss << x;
   return ss.str();
}

std::string prd(const double x, const int decDigits, const int width, bool right = true)
{
   std::stringstream ss;
   ss << std::fixed << (right ? std::right : std::left);
   ss.fill(' ');               // fill space around displayed #
   ss.width(width);            // set  width around displayed #
   ss.precision(decDigits);    // set # places after decimal
   ss << x;
   return ss.str();
}

/** Center-aligns string within a field of width w. Pads with blank spaces to enforce alignment. */
std::string center(const std::string s, const int w)
{
   std::stringstream ss, spaces;
   int padding = w - s.size();                 // count excess room to pad
   for (int i = 0; i < (padding / 2); ++i)
      spaces << " ";
   ss << spaces.str() << s << spaces.str();    // format with padding
   if (padding > 0 && (padding % 2 != 0))      // if odd #, add 1 space
      ss << " ";
   return ss.str();
}

void XmppFileUploadStats::setTableHeader(std::ostringstream& os)
{
   os << center("Account", 20)            << " | "
      << center("Status", 12)             << " | "
      << center("File", 12)               << " | "
      << center("Size", 12)               << " | "
      << center("Upload", 12)             << " | "
      << center("Start-Time", 12)         << " | "
      << center("End-Time", 12)           << " | "
      << center("Percent", 12)            << " | "
      << center("Download", 12)           << " | " << "\n";
   os << std::string(20*1 + 12*8 + 1*9 + 17, '-') << "\n";
}

void XmppFileUploadStats::setTableValues(std::ostringstream& os, UploadStats& stats)
{
   if ((stats.uploadStatus == UploadStats::Status_Unknown) || (stats.transfers.size() == 0))
   {
      os << prd(stats.account, 20, false)                          << " | "
         << prd(getAccountStatus(stats.accountStatus), 12)         << " | "
         << prd(stats.filename, 12)                                << " | "
         << prd("-", 12)                                           << " | "
         << prd("-", 12)                                           << " | "
         << prd("-", 12)                                           << " | "
         << prd("-", 12)                                           << " | "
         << prd("-", 12)                                           << " | "
         << prd("-", 12)                                           << " | " << "\n";
   }
   else
   {
      short percentage = stats.transfers[stats.transfers.size() - 1].percentage;
      os << prd(stats.account, 20, false)                          << " | "
         << prd(getAccountStatus(stats.accountStatus), 12)         << " | "
         << prd(stats.filename, 12)                                << " | "
         << prd(stats.filesize, 12)                                << " | "
         << prd(getStatus(stats.uploadStatus), 12)                 << " | "
         << prd(stats.starttime, 12)                               << " | "
         << prd(stats.endtime, 12)                                 << " | "
         << prd(percentage, 12)                                    << " | "
         << prd(getStatus(stats.downloadStatus, true), 12)         << " | " << "\n";
   }
}

/*
{
 "logToConsole": true,
 "console": true,
 "fileToUpload": "send.png",
 "eventTimeoutInMilliseconds": 60000,
 "statsIntervalInMilliseconds": 3000,
 "accountActivationIntervalInMilliseconds": 100,
 "userGroups": [
   "flw_loadtest"
 ],
 "fdLimit": 100,

 "flw_loadtest": {
   "activeAccounts": 1,
   "fileToUpload": "send1.png",
   "xmppAccountSettings": {
     "domain": "imap.mobilevoiplive.com",
     "password": "PknhrkuzNKVe5Z3es3UNNLG4NRXRXgoO",
     "port": 5222,
     "ignoreCertVerification": true
   }
 },

 "accounts": [
   {
     "xmppAccountSettings": {
       "username": "flw_loadtest100",
       "domain": "imap.mobilevoiplive.com",
       "password": "PknhrkuzNKVe5Z3es3UNNLG4NRXRXgoO",
       "port": 5222,
       "ignoreCertVerification": true
     }
   }
 ]
}
*/

void XmppFileUploadTestTool::updateFileProgress(const std::string& account, int filenumber, XmppFileTransferHandle transfer, FileTransferItemProgressEvent evt)
{
   if (mStats)
   {
      mStats->updateFileProgress(account, filenumber, transfer, evt);
   }
}

void XmppFileUploadTestTool::updateFileProgress(const std::string& account, int filenumber, XmppFileUploadStats::UploadStats::Status status, long filesize)
{
   if (mStats)
   {
      mStats->updateFileProgress(account, filenumber, status, filesize);
   }
}

void XmppFileUploadTestTool::updateDownloadStatus(const std::string& account, int filenumber, XmppFileUploadStats::UploadStats::Status status)
{
   if (mStats)
   {
      mStats->updateDownloadStatus(account, filenumber, status);
   }
}

void XmppFileUploadTestTool::setupXmppAccounts()
{
   safeCout(STATUS_LOG_FMT("setup"));

   std::string configFileName = "xmppFileUploadTestTool.json";
   ASSERT_TRUE(TestEnvironmentConfig::getResourceFile(configFileName, configFileName)) << FAIL_LOG_FMT("failed to retrieve the file-upload configuration file: " << configFileName);
   resip::Data configFile = configFileName.c_str();

   // load configurations
   std::ifstream ifs(configFile.c_str());
   ASSERT_TRUE(ifs.is_open()) << FAIL_LOG_FMT("failed to open the file-upload configuration file: " << configFileName.c_str());

   std::ostringstream oss;
   oss << ifs.rdbuf() << std::flush;

   rapidjson::Document json;

   json.Parse<0>(oss.str().c_str());
   ASSERT_FALSE(json.HasParseError()) << FAIL_LOG_FMT("Parse error occured in " << configFileName.c_str() << ":" << json.GetParseError());

   if (json.HasMember("logToConsole"))
   {
      ASSERT_TRUE(json["logToConsole"].IsBool()) <<  FAIL_LOG_FMT("node: logToConsole is not a bool");
      bool enabled = json["logToConsole"].GetBool();
      (enabled ? putenv("CPCAPI2_LOG_TO_CONSOLE=1") : putenv("CPCAPI2_LOG_TO_CONSOLE=0"));
   }

   if (json.HasMember("console"))
   {
      ASSERT_TRUE(json["console"].IsBool()) <<  FAIL_LOG_FMT("node: console is not a bool");
      mEnableConsole = json["console"].GetBool();
   }

   ASSERT_TRUE(json.HasMember("fileToUpload")) << FAIL_LOG_FMT("missing node: fileToUpload");
   ASSERT_TRUE(json["fileToUpload"].IsString()) <<  FAIL_LOG_FMT("node: fileToUpload is not a string");
   mFileToUpload = json["fileToUpload"].GetString();
   ASSERT_TRUE(!mFileToUpload.empty()) << FAIL_LOG_FMT("fileToUpload must not be empty");

   if (json.HasMember("eventTimeoutInMilliseconds"))
   {
      ASSERT_TRUE(json["eventTimeoutInMilliseconds"].IsUint()) <<  FAIL_LOG_FMT("node: eventTimeoutInMilliseconds is not a uint");
      mEventTimeout = json["eventTimeoutInMilliseconds"].GetUint();
   }

   if (json.HasMember("uploadProgressIntervalInMilliseconds"))
   {
      ASSERT_TRUE(json["uploadProgressIntervalInMilliseconds"].IsUint()) <<  FAIL_LOG_FMT("node: uploadProgressIntervalInMilliseconds is not a uint");
      mUploadProgressInterval = json["uploadProgressIntervalInMilliseconds"].GetUint();
   }

   if (json.HasMember("accountActivationIntervalInMilliseconds"))
   {
      ASSERT_TRUE(json["accountActivationIntervalInMilliseconds"].IsUint()) <<  FAIL_LOG_FMT("node: accountActivationIntervalInMilliseconds is not a uint");
      mAccountActivationInterval = json["accountActivationIntervalInMilliseconds"].GetUint();
   }

   if (json.HasMember("fileTransmissionIntervalInMilliseconds"))
   {
      ASSERT_TRUE(json["fileTransmissionIntervalInMilliseconds"].IsUint()) <<  FAIL_LOG_FMT("node: fileTransmissionIntervalInMilliseconds is not a uint");
      mFileTransmissionInterval = json["fileTransmissionIntervalInMilliseconds"].GetUint();
   }

   if (json.HasMember("fileTransmissionInitiationDelayInMilliseconds"))
   {
      ASSERT_TRUE(json["fileTransmissionInitiationDelayInMilliseconds"].IsUint()) <<  FAIL_LOG_FMT("node: fileTransmissionInitiationDelayInMilliseconds is not a uint");
      mFileTransmissionInitiationDelayInMilliseconds = json["fileTransmissionInitiationDelayInMilliseconds"].GetUint();
   }

   if (json.HasMember("maximumSimultaneousFileUploads"))
   {
      ASSERT_TRUE(json["maximumSimultaneousFileUploads"].IsUint()) <<  FAIL_LOG_FMT("node: maximumSimultaneousFileUploads is not a uint");
      mMaximumSimultaneousFileUploads = json["maximumSimultaneousFileUploads"].GetUint();
   }

   if (json.HasMember("maximumSimultaneousAccounts"))
   {
      ASSERT_TRUE(json["maximumSimultaneousAccounts"].IsUint()) <<  FAIL_LOG_FMT("node: maximumSimultaneousAccounts is not a uint");
      mMaximumSimultaneousAccounts = json["maximumSimultaneousAccounts"].GetUint();
   }

   if (json.HasMember("maximumFileUploadThreads"))
   {
      ASSERT_TRUE(json["maximumFileUploadThreads"].IsUint()) <<  FAIL_LOG_FMT("node: maximumFileUploadThreads is not a uint");
      mMaximumFileUploadThreads = json["maximumFileUploadThreads"].GetUint();
   }

   if (json.HasMember("fdLimit"))
   {
#ifdef WIN32
      safeCout("fdLimit is not supported under Win32");
#else
      mFdLimit = json["fdLimit"].GetUint();
#endif
   }

#ifdef WIN32
   safeCout("max number of accounts cannot exceed 120 under Win32");
#else
   rlimit dataLimit;
   ASSERT_EQ(getrlimit(RLIMIT_DATA, &dataLimit), 0) << FAIL_LOG_FMT("error getting system data limit");
   rlimit sizeLimit;
   ASSERT_EQ(getrlimit(RLIMIT_FSIZE, &sizeLimit), 0) << FAIL_LOG_FMT("error getting system file size limit");
   rlimit stackLimit;
   ASSERT_EQ(getrlimit(RLIMIT_STACK, &stackLimit), 0) << FAIL_LOG_FMT("error getting system stack limit");
   rlimit fdLimit;
   ASSERT_EQ(getrlimit(RLIMIT_NOFILE, &fdLimit), 0) << FAIL_LOG_FMT("error getting system fd limit");

   safeCout("XmppFileUploadTestTool::setupXmppAccounts(): ***** system limits: data: " << dataLimit.rlim_cur << " data max: " << dataLimit.rlim_max << " file-size: " << sizeLimit.rlim_cur << " file-size max: " << sizeLimit.rlim_max << " stack: " << stackLimit.rlim_cur << " stack max: " << stackLimit.rlim_max << " fd: " << fdLimit.rlim_cur << " fd max: " << fdLimit.rlim_max << " configured fd limit: " << mFdLimit << " FD_SETSIZE: " << FD_SETSIZE << " thread-limit: " << std::thread::hardware_concurrency());
   ASSERT_TRUE(mFdLimit <= fdLimit.rlim_max) << FAIL_LOG_FMT("configured fd limit: " << mFdLimit << " higher than system fd limit: " << fdLimit.rlim_max);
   fdLimit.rlim_cur = fdLimit.rlim_max = mFdLimit;
   ASSERT_EQ(setrlimit(RLIMIT_NOFILE, &fdLimit), 0) << FAIL_LOG_FMT("error setting system fd limit to: " << mFdLimit << " system error: " << strerror(resip::getErrno()));
#endif

   // ASSERT_TRUE(json.HasMember("userGroups")) << FAIL_LOG_FMT("missing node: userGroup");
   // const auto& userGroups_json = json["userGroups"];
   // ASSERT_TRUE(userGroups_json.IsArray()) << FAIL_LOG_FMT("node userGroups is not an array");
   // ASSERT_FALSE(userGroups_json.Empty()) << FAIL_LOG_FMT("userGroups must not be empty");

   if (json.HasMember("userGroups"))
   {
      const auto& userGroups_json = json["userGroups"];
      ASSERT_TRUE(userGroups_json.IsArray()) << FAIL_LOG_FMT("node userGroups is not an array");
      if (!userGroups_json.Empty())
      {
         for (auto i = userGroups_json.Begin(); i != userGroups_json.End(); ++i)
         {
            ASSERT_TRUE(i->IsString()) << FAIL_LOG_FMT("user-group array member is not a string");
            ASSERT_TRUE(json.HasMember(i->GetString())) << FAIL_LOG_FMT("missing user-group node: " << i->GetString());
            const auto& userGroupObject_json = json[i->GetString()];
            ASSERT_TRUE(userGroupObject_json.IsObject()) << FAIL_LOG_FMT("user-group node: " << i->GetString() << " is not an object");

            std::string fileToUpload(mFileToUpload);
            if (userGroupObject_json.HasMember("fileToUpload"))
            {
               ASSERT_TRUE(userGroupObject_json["fileToUpload"].IsString()) << FAIL_LOG_FMT("user-group node: " << i->GetString() << " member node: fileToUpload is not a string");
               fileToUpload = userGroupObject_json["fileToUpload"].GetString();
            }

            unsigned int fileUploadCount = 1;
            if (userGroupObject_json.HasMember("fileUploadCount"))
            {
               ASSERT_TRUE(userGroupObject_json["fileUploadCount"].IsUint()) << FAIL_LOG_FMT("user-group node: " << i->GetString() << " member node: fileUploadCount is not an uint");
               fileUploadCount = userGroupObject_json["fileUploadCount"].GetUint();
               ASSERT_TRUE(fileUploadCount >= 1) << FAIL_LOG_FMT("user-group node: " << i->GetString() << " member node: fileUploadCount value is not within accepted range");
            }

            ASSERT_TRUE(userGroupObject_json.HasMember("activeAccounts")) << FAIL_LOG_FMT("user-group node: " << i->GetString() << " missing member node: activeAccounts");
            ASSERT_TRUE(userGroupObject_json["activeAccounts"].IsUint()) << FAIL_LOG_FMT("user-group node: " << i->GetString() << " member node: activeAccounts is not an uint");
            int activeAccounts = userGroupObject_json["activeAccounts"].GetUint();

            ASSERT_TRUE(userGroupObject_json.HasMember("xmppAccountSettings")) << FAIL_LOG_FMT("user-group node: " << i->GetString() << " missing xmppAccountSettings node");
            const auto& settings_json = userGroupObject_json["xmppAccountSettings"];
            ASSERT_TRUE(settings_json.IsObject()) << FAIL_LOG_FMT("user-group node: " << i->GetString() << " xmppAccountSettings node not an object");

            bool disableInitialSelfPresenceCheck = true;
            if (settings_json.HasMember("disableInitialSelfPresenceCheck"))
            {
               ASSERT_TRUE(settings_json["disableInitialSelfPresenceCheck"].IsBool()) << FAIL_LOG_FMT("user-group node: " << i->GetString() << " xmppAccountSettings node: disableInitialSelfPresenceCheck is not a bool");
               disableInitialSelfPresenceCheck = settings_json["disableInitialSelfPresenceCheck"].GetBool();
            }

            XmppAccountSettings settings;
            JsonDeserialize(userGroupObject_json, "xmppAccountSettings", settings);
            for (int j = 1; j <= activeAccounts; ++j)
            {
               std::ostringstream ss;
               ss << i->GetString() << j;
               settings.username = ss.str().c_str();

               mAccountSettings.push_back(XmppAccountSettingsEx(settings, disableInitialSelfPresenceCheck, fileToUpload, fileUploadCount));
            }
         }
      }
   }

   if (json.HasMember("accounts"))
   {
      const auto& accounts_json = json["accounts"];
      ASSERT_TRUE(accounts_json.IsArray()) << FAIL_LOG_FMT("member node: accounts is not an array");
      for (auto i = accounts_json.Begin(); i != accounts_json.End(); ++i)
      {
         ASSERT_TRUE(i->HasMember("xmppAccountSettings")) << FAIL_LOG_FMT("missing xmppAccountSettings node");
         const auto& settings_json = (*i)["xmppAccountSettings"];
         ASSERT_TRUE(settings_json.IsObject()) << FAIL_LOG_FMT("xmppAccountSettings node not an object");

         bool disableInitialSelfPresenceCheck = true;
         if (settings_json.HasMember("disableInitialSelfPresenceCheck"))
         {
            ASSERT_TRUE(settings_json["disableInitialSelfPresenceCheck"].IsBool()) << FAIL_LOG_FMT("xmppAccountSettings node: disableInitialSelfPresenceCheck is not a bool");
            disableInitialSelfPresenceCheck = settings_json["disableInitialSelfPresenceCheck"].GetBool();
         }

         XmppAccountSettings settings;
         JsonDeserialize(*i, "xmppAccountSettings", settings);

         mAccountSettings.push_back(XmppAccountSettingsEx(settings, disableInitialSelfPresenceCheck, mFileToUpload));
      }
   }

   ASSERT_TRUE(mAccountSettings.size() > 0) << FAIL_LOG_FMT("no accounts configured");

   ifs.close();
   safeCout(STATUS_LOG_FMT("setupXmppAccounts finished"));

   mStats->initialize(mAccountSettings);
}

void XmppFileUploadTestTool::enableXmppAccounts(std::vector<XmppAccountSettingsEx>& accountSettings)
{
   for (auto settings : accountSettings)
   {
      std::string accountUsername = settings.username.c_str();
      XmppTestAccount*& userAccount = gXmppAccounts[accountUsername];
      std::string& accountService = gAccountServices[accountUsername];

      safeCout("XmppFileUploadTestTool::enableXmppAccounts(): ********** starting account creation for: " << settings.username.c_str());

      {
         userAccount = new XmppTestAccount(settings.username.c_str(), Account_Init);
         gXmppAccountHandles[userAccount->handle] = settings.username.c_str();
         userAccount->config.settings = settings;
         userAccount->account->configureDefaultAccountSettings(userAccount->handle, userAccount->config.settings);
         userAccount->account->applySettings(userAccount->handle);
         userAccount->setAccountStatusHandler(mStats.get()); // TODO: handle unique pointer

         ASSERT_NO_FATAL_FAILURE(userAccount->enable()) << FAIL_LOG_FMT("failure in Xmpp account registration");

         {
            XmppAccount::XmppAccountHandle h;
            ServiceAvailabilityEvent evt;
            ASSERT_TRUE(cpcExpectEvent(userAccount->events, "XmppFileTransferHandler::onServiceAvailability", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt)) << FAIL_LOG_FMT("failure in enabling xmpp account, service-availability status not available");
            ASSERT_EQ(h, userAccount->handle) << FAIL_LOG_FMT("service-availability: mismatch in xmpp account handle received: " << h << " expecting: " << userAccount->handle);
            ASSERT_TRUE(evt.XEP0363_available) << FAIL_LOG_FMT("service-availability: XEP0363 service not available");
            ASSERT_TRUE(!evt.XEP0363_service.empty()) << FAIL_LOG_FMT("service-availability: XEP0363 service name is empty");
            accountService = evt.XEP0363_service;
         }

         safeCout("XmppFileUploadTestTool::enableXmppAccounts(): ********** account: " << settings.username.c_str() << " enabled");
      }

      if (mAccountActivationInterval > 0)
      {
         std::this_thread::sleep_for(std::chrono::milliseconds(mAccountActivationInterval));
      }
   }
}

void XmppFileUploadTestTool::initiateTransfer(std::vector<XmppAccountSettingsEx>& accountSettings)
{
   std::atomic<unsigned int> uploadsInProgress(0);
   unsigned int maximumSimultaneousFileUploads = mMaximumSimultaneousFileUploads;

   std::vector<std::thread*> accountThreads;
   for (auto settings : accountSettings)
   {
      std::string accountUsername = settings.username.c_str();
      XmppTestAccount*& userAccount = gXmppAccounts[accountUsername];
      // std::vector<std::string>& accountRemoteFile = gRemoteFiles[accountUsername];
      // std::vector<int>& accountRemoteFileSize = gRemoteFileSizes[accountUsername];
      std::map<int, XmppTestFileInfo>& accountFiles = gRemoteFiles[accountUsername];
      std::string& accountService = gAccountServices[accountUsername];

      // accountThreads.push_back(new std::thread([settings, &accountRemoteFile, &accountRemoteFileSize, &userAccount, &accountService, &maximumSimultaneousFileUploads, &uploadsInProgress]()
      accountThreads.push_back(new std::thread([settings, &accountFiles, &userAccount, &accountService, &maximumSimultaneousFileUploads, &uploadsInProgress]()
      {
         bool tryAgain(true);
         while (tryAgain)
         {
            if (uploadsInProgress < maximumSimultaneousFileUploads)
            {
               uploadsInProgress++;

               for (int fileNumber = 1; fileNumber <= settings.fileUploadCount; ++fileNumber)
               {
                  int attemptMax = 2;
                  bool retry = true;
                  for (int attempt = 1; retry && (attempt <= attemptMax); ++attempt)
                  {
                     safeCout("XmppFileUploadTestTool::initiateTransfer(): ********** starting file transfer for: " << settings.username.c_str() << " file-number: " << fileNumber << " attempt: " << attempt);
                     // Make an outgoing file transfer from Alice to the server
                     XmppFileTransferHandle userTransfer = userAccount->fileTransferManager->createFileTransfer(userAccount->handle);
                     XmppFileTransferItemHandle accountTransferItem1 = userAccount->fileTransferManager->createFileTransferItem(userAccount->handle);

                     // Setup the file transfer items
                     XmppFileTransferItems items;
                     XmppFileTransferItemDetail itemDetail1;
                     XmppFileTransferItemDetail itemDetail2;
                     itemDetail1.handle = accountTransferItem1;
                     itemDetail1.localfileName = settings.fileToUpload.c_str();
                     itemDetail1.localfilePath = TestEnvironmentConfig::testResourcePath();
                     items.push_back(itemDetail1);

                     userAccount->fileTransferManager->configureFileTransferItems(userTransfer, items);
                     userAccount->fileTransferManager->addParticipant(userTransfer, accountService.c_str());
                     userAccount->fileTransferManager->start(userTransfer);

                     {
                        XmppFileTransferItems items;
                        {
                           XmppFileTransferHandle h;
                           NewFileTransferEvent evt;
                           ASSERT_TRUE(cpcExpectEvent(userAccount->events, "XmppFileTransferHandler::onNewFileTransfer", 120000, AlwaysTruePred(), h, evt)) << FAIL_LOG_FMT("error initiating file-transfer, transfer start confirmation not received");
                           items = evt.fileItems;
                           ASSERT_EQ(h, userTransfer) << FAIL_LOG_FMT("mismatch in file-transfer handle received: " << h << " expecting: " << userTransfer);
                           ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing) << FAIL_LOG_FMT("mismatch in file-transfer type, expecting an outgoing transfer");
                           ASSERT_EQ(evt.fileItems.size(), 1) << FAIL_LOG_FMT("mismatch in file-transfer file-item list size: " << evt.fileItems.size() << " expecting: " << 1);
                           ASSERT_EQ(items[0].localfileName, settings.fileToUpload.c_str()) << FAIL_LOG_FMT("mismatch in file-transfer file-name: " << items[0].localfileName << " expecting: " << settings.fileToUpload.c_str());
                           accountFiles[fileNumber].filesize = items[0].fileSizeBytes;
                        }

                        // Watch the progress
                        FileTransferItemProgressEvent progressEvt;
                        progressEvt.fileTransferItem = accountTransferItem1;
                        progressEvt.percent = 0;

                        {
                           while (progressEvt.percent < 100)
                           {
                              // ASSERT_TRUE(cpcExpectEvent(userAccount->events, "XmppFileTransferHandler::onFileTransferItemProgress", 60000, AlwaysTruePred(), userTransfer, evt)) << FAIL_LOG_FMT("did not receive file-transfer item progress update for transfer: " << userTransfer << " transfer-item: " << accountTransferItem1);
                              if (cpcExpectEvent(userAccount->events, "XmppFileTransferHandler::onFileTransferItemProgress", 120000, AlwaysTruePred(), userTransfer, progressEvt))
                              {
                              }
                              else
                              {
                                 safeCout("XmppFileUploadTestTool::initiateTransfer(): ********** file transfer error for: " << settings.username.c_str() << " file-number: " << fileNumber << " attempt: " << attempt);
                                 ASSERT_TRUE(false) << FAIL_LOG_FMT("file-transfer error for: " << settings.username.c_str() << " file-number: " << fileNumber << " attempt: " << attempt << " transfer: " << userTransfer << " item: " << accountTransferItem1);
                                 break;
                              }
                           }

                           if (progressEvt.percent == 100)
                           {
                              retry = false;
                           }
                           else
                           {
                              userAccount->fileTransferManager->end(userTransfer);
                           }
                        }

                        {
                           // Wait for the transfer of the item to finish
                           FileTransferItemEndedEvent ftie;
                           ASSERT_TRUE(cpcExpectEvent(userAccount->events, "XmppFileTransferHandler::onFileTransferItemEnded", 120000, AlwaysTruePred(), userTransfer, ftie)) << FAIL_LOG_FMT("XmppFileUploadTestTool::initiateTransfer(): item file-transfer did not end properly for tranfser: " << userTransfer << " item: " << accountTransferItem1);
                           ASSERT_EQ(ftie.streamTypeAttempted, FileTransferStreamType_XEP0363) << FAIL_LOG_FMT("mismatch in file-transfer item stream-type attempted type: " << ftie.streamTypeAttempted << " expected: " << FileTransferStreamType_XEP0363);
                           if (progressEvt.percent == 100)
                           {
                              ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete) << FAIL_LOG_FMT("file-transfer did not get completed for " << settings.username.c_str() << " file-number: " << fileNumber << " attempt: " << attempt);
                           }
                           else
                           {
                              // ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_LocalCancel) << " file-transfer failed for " << settings.username.c_str() << " attempt: " << attempt << " due to: " << ftie.endReason;
                              safeCout("XmppFileUploadTestTool::initiateTransfer(): ********** file-transfer failed for " << settings.username.c_str() << " file-number: " << fileNumber << " attempt: " << attempt << " with reason-code: " << ftie.endReason);
                              ASSERT_TRUE(false) << FAIL_LOG_FMT("file-transfer failed for " << settings.username.c_str() << " file-number: " << fileNumber << " attempt: " << attempt << " with reason-code: " << ftie.endReason);
                           }
                           accountFiles[fileNumber].filename = ftie.remoteFileURI.c_str();

                           // Wait for the transfer to finish
                           FileTransferEndedEvent fte;
                           ASSERT_TRUE(cpcExpectEvent(userAccount->events, "XmppFileTransferHandler::onFileTransferEnded", 120000, AlwaysTruePred(), userTransfer, fte)) << FAIL_LOG_FMT("file-transfer did not end properly for " << settings.username.c_str() << " file-number: " << fileNumber << " attempt: " << attempt);
                        }
                     }
                  }
               }

               uploadsInProgress--;
               tryAgain = false;
            }
            else
            {
               safeCout("XmppFileUploadTestTool::initiateTransfer(): ********** waiting on file transfer for: " << settings.username.c_str() << " as have reached maximum uploads: " << maximumSimultaneousFileUploads);
               std::this_thread::sleep_for(std::chrono::milliseconds(500));
            }
         }
      }));
   }

   for (std::thread* t : accountThreads)
   {
      t->join();
      delete t;
   }
   accountThreads.clear();
}

template<typename TFn, typename TEvt>
void fireFileTransferEvent(const char* funcName, TFn func, EventHandler* handler, XmppFileTransferHandle handle, const TEvt& evt)
{
   // resip::ReadCallbackBase* rcb = makeFpCommandNew(funcName, func, reinterpret_cast<XmppFileTransferHandler*>(0xDEADBEEF), handle, evt);
   resip::ReadCallbackBase* rcb = makeFpCommandNew(funcName, func, reinterpret_cast<XmppFileTransferHandler*>(handler), handle, evt);
   AutoTestReadCallback* fpCmd = dynamic_cast<AutoTestReadCallback*>(rcb);
   if (fpCmd != NULL)
   {
      handler->addEvent(fpCmd);
      return;
   }
   if (rcb != NULL)
   {
      fpCmd = new AutoTestReadCallback(rcb, "", std::make_tuple(0,0));
      handler->addEvent(fpCmd);
      return;
   }
}

void XmppFileUploadTestTool::initiateTransferConcurrently(std::vector<XmppAccountSettingsEx>& accountSettings)
{
   std::atomic<unsigned int> uploadsInProgress(0);
   unsigned int maximumSimultaneousFileUploads = mMaximumSimultaneousFileUploads;

   std::vector<std::thread*> accountThreads;
   for (auto settings : accountSettings)
   {
      std::string accountUsername = settings.username.c_str();
      XmppTestAccount*& userAccount = gXmppAccounts[accountUsername];
      std::map<int, XmppTestFileInfo>& accountFiles = gRemoteFiles[accountUsername];
      std::string& accountService = gAccountServices[accountUsername];

      accountThreads.push_back(new std::thread([settings, &accountFiles, &userAccount, &accountService, &maximumSimultaneousFileUploads, &uploadsInProgress]()
      {
         std::atomic<bool> keepOnLooping(true);
         auto masterEvents = std::async(std::launch::async, [&keepOnLooping, &userAccount] ()
         {
            std::string transferEvtName = "XmppFileTransferHandler::onNewFileTransfer";
            std::string progressEvtName = "XmppFileTransferHandler::onFileTransferItemProgress";
            std::string itemEndedEvtName = "XmppFileTransferHandler::onFileTransferItemEnded";
            std::string transferEndedEvtName = "XmppFileTransferHandler::onFileTransferEnded";

            while (keepOnLooping)
            {
               XmppFileTransferHandle h = 0;
               if (userAccount->events->doesEventExist(transferEvtName))
               {
                  NewFileTransferEvent evt;
                  cpcExpectEvent(userAccount->events, transferEvtName, 500, AlwaysTruePred(), h, evt);
                  EventHandler* handler = userAccount->getXmppFileTransferEventHandler(h);
                  if (handler)
                  {
                     fireFileTransferEvent(cpcFunc(XmppFileTransferHandler::onNewFileTransfer), handler, h, evt);
                  }
               }
               else if (userAccount->events->doesEventExist(progressEvtName))
               {
                  FileTransferItemProgressEvent evt;
                  cpcExpectEvent(userAccount->events, progressEvtName, 60000, AlwaysTruePred(), h, evt);
                  EventHandler* handler = userAccount->getXmppFileTransferEventHandler(h);
                  if (handler)
                  {
                     fireFileTransferEvent(cpcFunc(XmppFileTransferHandler::onFileTransferItemProgress), handler, h, evt);
                  }
               }
               else if (userAccount->events->doesEventExist(itemEndedEvtName))
               {
                  FileTransferItemEndedEvent evt;
                  cpcExpectEvent(userAccount->events, itemEndedEvtName, 60000, AlwaysTruePred(), h, evt);
                  EventHandler* handler = userAccount->getXmppFileTransferEventHandler(h);
                  if (handler)
                  {
                     fireFileTransferEvent(cpcFunc(XmppFileTransferHandler::onFileTransferItemEnded), handler, h, evt);
                  }
               }
               else if (userAccount->events->doesEventExist(transferEndedEvtName))
               {
                  FileTransferEndedEvent evt;
                  cpcExpectEvent(userAccount->events, transferEndedEvtName, 60000, AlwaysTruePred(), h, evt);
                  EventHandler* handler = userAccount->getXmppFileTransferEventHandler(h);
                  if (handler)
                  {
                     fireFileTransferEvent(cpcFunc(XmppFileTransferHandler::onFileTransferEnded), handler, h, evt);
                  }
               }
               else
               {
                  std::this_thread::sleep_for(std::chrono::milliseconds(500));
               }
            }
         });

         // async file transfers at the same time
         std::vector<std::future<void> > futures;

         for (int fileNumber = 1; fileNumber <= settings.fileUploadCount; ++fileNumber)
         {
            futures.emplace_back(std::async(std::launch::async, [settings, fileNumber, &userAccount, &uploadsInProgress, &maximumSimultaneousFileUploads, &accountService, &accountFiles]()
            {
               bool retryForUploadsLimit(true);
               while (retryForUploadsLimit)
               {
                  if (uploadsInProgress < maximumSimultaneousFileUploads)
                  {
                     uploadsInProgress++;
                     int attemptMax = 2;
                     bool retryTransfer = true;

                     std::set<XmppFileTransferHandle> transfers;
                     std::set<XmppFileTransferItemHandle> transferItems;
                     std::map<XmppFileTransferItemHandle, XmppFileTransferItems> transferItemDetails;

                     for (int attempt = 1; retryTransfer && (attempt <= attemptMax); ++attempt)
                     {
                        // Make an outgoing file transfer from Alice to the server
                        XmppFileTransferHandle userTransfer = userAccount->fileTransferManager->createFileTransfer(userAccount->handle);
                        XmppFileTransferItemHandle accountTransferItem1 = userAccount->fileTransferManager->createFileTransferItem(userAccount->handle);
                        safeCout("XmppFileUploadTestTool::initiateTransferConcurrently(): ********** starting file transfer for: " << settings.username.c_str() << " file-number: " << fileNumber << " attempt: " << attempt << " transfer-handle: " << userTransfer);

                        EventHandler* transferHandler = userAccount->addXmppFileTransferEventHandler(userTransfer);
                        ASSERT_NE(nullptr, transferHandler) << FAIL_LOG_FMT("could not retrieve file-transfer event handler for: " << settings.username.c_str() << " file-number: " << fileNumber << " attempt: " << attempt << " transfer-handle: " << userTransfer);

                        // Setup the file transfer items
                        XmppFileTransferItems items;
                        XmppFileTransferItemDetail itemDetail1;
                        XmppFileTransferItemDetail itemDetail2;
                        itemDetail1.handle = accountTransferItem1;
                        itemDetail1.localfileName = settings.fileToUpload.c_str();
                        itemDetail1.localfilePath = TestEnvironmentConfig::testResourcePath();
                        items.push_back(itemDetail1);

                        transfers.insert(userTransfer);
                        transferItems.insert(accountTransferItem1);
                        transferItemDetails[accountTransferItem1] = items;

                        userAccount->fileTransferManager->configureFileTransferItems(userTransfer, items);
                        userAccount->fileTransferManager->addParticipant(userTransfer, accountService.c_str());
                        userAccount->fileTransferManager->start(userTransfer);

                        {
                           XmppFileTransferHandle h;
                           NewFileTransferEvent evt;
                           ASSERT_TRUE(cpcExpectEvent(transferHandler, "XmppFileTransferHandler::onNewFileTransfer", 120000, AlwaysTruePred(), h, evt)) << FAIL_LOG_FMT("error initiating file-transfer, transfer start confirmation not received");
                           items = evt.fileItems;
                           ASSERT_TRUE(transfers.find(h) != transfers.end()) << FAIL_LOG_FMT("mismatch in file-transfer handle received: " << h << " expecting: " << userTransfer);
                           ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing) << FAIL_LOG_FMT("mismatch in file-transfer type, expecting an outgoing transfer");
                           ASSERT_EQ(evt.fileItems.size(), 1) << FAIL_LOG_FMT("mismatch in file-transfer file-item list size: " << evt.fileItems.size() << " expecting: " << 1);
                           ASSERT_EQ(items[0].localfileName, settings.fileToUpload.c_str()) << FAIL_LOG_FMT("mismatch in file-transfer file-name: " << items[0].localfileName << " expecting: " << settings.fileToUpload.c_str());
                           accountFiles[fileNumber].filesize = items[0].fileSizeBytes;
                        }

                        gTool->updateFileProgress(settings.username.c_str(), fileNumber, XmppFileUploadStats::UploadStats::Status_Progress, items[0].fileSizeBytes);

                        // Watch the progress
                        FileTransferItemProgressEvent progressEvt;
                        progressEvt.fileTransferItem = accountTransferItem1;
                        progressEvt.percent = 0;

                        while (progressEvt.percent < 100)
                        {
                           // ASSERT_TRUE(cpcExpectEvent(userAccount->events, "XmppFileTransferHandler::onFileTransferItemProgress", 60000, AlwaysTruePred(), userTransfer, evt)) << FAIL_LOG_FMT("did not receive file-transfer item progress update for transfer: " << userTransfer << " transfer-item: " << accountTransferItem1);
                           if (cpcExpectEvent(transferHandler, "XmppFileTransferHandler::onFileTransferItemProgress", 120000, AlwaysTruePred(), userTransfer, progressEvt))
                           {
                              gTool->updateFileProgress(settings.username.c_str(), fileNumber, userTransfer, progressEvt);
                           }
                           else
                           {
                              safeCout("XmppFileUploadTestTool::initiateTransferConcurrently(): ********** file transfer error for: " << settings.username.c_str() << " file-number: " << fileNumber << " attempt: " << attempt);
                              ASSERT_TRUE(false) << FAIL_LOG_FMT("file-transfer error for: " << settings.username.c_str() << " file-number: " << fileNumber << " attempt: " << attempt << " transfer: " << userTransfer << " item: " << accountTransferItem1);
                              break;
                           }
                        }

                        if (progressEvt.percent == 100)
                        {
                           retryTransfer = false;
                        }
                        else
                        {
                           userAccount->fileTransferManager->end(userTransfer);
                        }

                        // Wait for the transfer of the item to finish
                        FileTransferItemEndedEvent ftie;
                        ASSERT_TRUE(cpcExpectEvent(transferHandler, "XmppFileTransferHandler::onFileTransferItemEnded", 120000, AlwaysTruePred(), userTransfer, ftie)) << FAIL_LOG_FMT("item file-transfer did not end properly for tranfser: " << userTransfer << " item: " << accountTransferItem1);
                        ASSERT_EQ(ftie.streamTypeAttempted, FileTransferStreamType_XEP0363) << FAIL_LOG_FMT("mismatch in file-transfer item stream-type attempted type: " << ftie.streamTypeAttempted << " expected: " << FileTransferStreamType_XEP0363);

                        accountFiles[fileNumber].filename = ftie.remoteFileURI.c_str();
                        if (progressEvt.percent == 100)
                        {
                           ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete) << FAIL_LOG_FMT("file-transfer did not get completed for " << settings.username.c_str() << " file-number: " << fileNumber << " attempt: " << attempt);
                           gTool->updateFileProgress(settings.username.c_str(), fileNumber, XmppFileUploadStats::UploadStats::Status_Success, items[0].fileSizeBytes);
                        }
                        else
                        {
                           // ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_LocalCancel) << " file-transfer failed for " << settings.username.c_str() << " attempt: " << attempt << " due to: " << ftie.endReason;
                           safeCout("XmppFileUploadTestTool::initiateTransferConcurrently(): ********** file-transfer failed for " << settings.username.c_str() << " file-number: " << fileNumber << " attempt: " << attempt << " with reason-code: " << ftie.endReason);
                           ASSERT_TRUE(false) << FAIL_LOG_FMT("file-transfer failed for " << settings.username.c_str() << " file-number: " << fileNumber << " attempt: " << attempt << " with reason-code: " << ftie.endReason);
                           if (attempt == attemptMax)
                           {
                              gTool->updateFileProgress(settings.username.c_str(), fileNumber, XmppFileUploadStats::UploadStats::Status_Failure, items[0].fileSizeBytes);
                           }
                        }

                        // Wait for the transfer to finish
                        FileTransferEndedEvent fte;
                        ASSERT_TRUE(cpcExpectEvent(transferHandler, "XmppFileTransferHandler::onFileTransferEnded", 120000, AlwaysTruePred(), userTransfer, fte)) << FAIL_LOG_FMT("file-transfer did not end properly for " << settings.username.c_str() << " file-number: " << fileNumber << " attempt: " << attempt);
                     }

                     uploadsInProgress--;
                     retryForUploadsLimit = false;
                  }
                  else
                  {
                     safeCout("XmppFileUploadTestTool::initiateTransferConcurrently(): ********** waiting on file transfer for: " << settings.username.c_str() << " as have reached maximum uploads: " << maximumSimultaneousFileUploads);
                     std::this_thread::sleep_for(std::chrono::milliseconds(500));
                     gTool->updateFileProgress(settings.username.c_str(), fileNumber, XmppFileUploadStats::UploadStats::Status_Waiting);
                  }
               }
            }));

            if (gTool->mFileTransmissionInterval > 0)
            {
               std::this_thread::sleep_for(std::chrono::milliseconds(gTool->mFileTransmissionInterval));
            }
         }

         for (auto& future : futures) future.wait();
         keepOnLooping = false;
      }));

      if (mFileTransmissionInterval > 0)
      {
         std::this_thread::sleep_for(std::chrono::milliseconds(mFileTransmissionInterval));
      }
   }

   for (std::thread* t : accountThreads)
   {
      t->join();
      delete t;
   }
   accountThreads.clear();
}

TEST_F(XmppFileUploadTestTool, test)
{
   std::ofstream outfile;
   outfile.open("stats.out");
   outfile << "";
   outfile.close();
   setupXmppAccounts();

   safeCout("XmppFileUploadTestTool::test(): ********** completed setup with total accounts: " << mAccountSettings.size() << " maximumSimultaneousFileUploads: " << mMaximumSimultaneousFileUploads << " maximumSimultaneousAccounts: " << mMaximumSimultaneousAccounts << " maximumFileUploadThreads: " << mMaximumFileUploadThreads);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   for (auto settings : mAccountSettings)
   {
      gXmppAccounts[settings.username.c_str()] = NULL;
      gRemoteFiles[settings.username.c_str()] = std::map<int, XmppTestFileInfo>();
      gAccountServices[settings.username.c_str()] = "";

      for (int fileNumber = 1; fileNumber <= settings.fileUploadCount; ++fileNumber)
      {
         gRemoteFiles[settings.username.c_str()][fileNumber] = XmppTestFileInfo();
      }
   }

   std::vector<XmppAccountSettingsEx> batchAccountSettings;
   int accountIndex = 0;
   for (auto settings : mAccountSettings)
   {
      batchAccountSettings.push_back(settings);
      accountIndex++;
      if ((batchAccountSettings.size() == mMaximumSimultaneousAccounts)
         || (accountIndex == mAccountSettings.size()))
      {
         std::stringstream ss1;
         for (auto set1 : batchAccountSettings)
         {
            ss1 << set1.username.c_str() << " ";
         }
         safeCout("XmppFileUploadTestTool::test(): ********** starting enabling for account batch with count: " << batchAccountSettings.size() << " for: " << ss1.str().c_str());
         enableXmppAccounts(batchAccountSettings);

         if ((mFileTransmissionInitiationDelayInMilliseconds > 0) && (accountIndex == 1))
         {
            std::this_thread::sleep_for(std::chrono::milliseconds(mFileTransmissionInitiationDelayInMilliseconds));
         }

         std::vector<XmppAccountSettingsEx> batchThreadSettings;
         int threadIndex = 0;
         for (auto threadSettings : batchAccountSettings)
         {
            batchThreadSettings.push_back(threadSettings);
            threadIndex++;
            if ((batchThreadSettings.size() == mMaximumFileUploadThreads)
               || (threadIndex == batchAccountSettings.size()))
            {
               std::stringstream ss2;
               for (auto set2 : batchThreadSettings)
               {
                  ss2 << set2.username.c_str() << " ";
               }
               safeCout("XmppFileUploadTestTool::test(): ********** starting transfer for thread batch with count: " << batchThreadSettings.size() << " for: " << ss2.str().c_str());
               initiateTransferConcurrently(batchThreadSettings);

               batchThreadSettings.clear();
            }
         }

         for (auto batchSettings : batchAccountSettings)
         {
            XmppTestAccount* account = gXmppAccounts[batchSettings.username.c_str()];
            account->disable();
            std::this_thread::sleep_for(std::chrono::milliseconds(1000));
            delete account;
         }
         batchAccountSettings.clear();
      }
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   int successfulDownloads = 0;
   int totalDownloads = 0;
   for (auto settings : mAccountSettings)
   {
      totalDownloads += gRemoteFiles[settings.username.c_str()].size();
      int totalBatchSize = gRemoteFiles[settings.username.c_str()].size();
      for (int fileNumber = 1; fileNumber <= gRemoteFiles[settings.username.c_str()].size(); ++fileNumber)
      {
         std::string remoteFile = gRemoteFiles[settings.username.c_str()][fileNumber].filename;
         int remoteFileSize = gRemoteFiles[settings.username.c_str()][fileNumber].filesize;
         safeCout("XmppFileUploadTestTool::test(): ********** account: " << settings.username.c_str() << " file-number: " << fileNumber << " of " << totalBatchSize << " downloading file: " << remoteFile);
         CurlPPHelper helper;
         curlpp::Easy request;

         std::stringstream url;
         bool ignoreCertErrors = true;
         int acceptableFailures = 0;
         if (ignoreCertErrors)
         {
            acceptableFailures = CurlPPSSL::E_CERT_WHATEVER_ERROR;
         }

         updateDownloadStatus(settings.username.c_str(), fileNumber, XmppFileUploadStats::UploadStats::Status_Progress);

         int curlError = CURLE_SSL_CONNECT_ERROR;

         SslCipherOptions tlsSettings = SslCipherOptions();
         tlsSettings.setTLSVersion(SslCipherUsageHttp, TLSVersion::TLS_V1_2);
         tlsSettings.setCiphers(SslCipherUsageHttp, CPCAPI2::CipherSuiteLegacy);

         CurlPPSSL cssl(tlsSettings, acceptableFailures);
         request.setOpt(new curlpp::options::SslCtxFunction(cssl));
         helper.setDefaultOptions(request, remoteFile);

         std::stringstream responseBody;
         request.setOpt(new curlpp::options::WriteStream(&responseBody));

         try
         {
            if (remoteFile.size() <= 0)
            {
               safeCout("XmppFileUploadTestTool::test(): ********** remote filename not populated for " << settings.username.c_str() << " file-number: " << fileNumber << " of " << totalBatchSize);
               // ASSERT_TRUE(false) << FAIL_LOG_FMT("remote filename not populated for " << settings.username.c_str() << " file-number: " << fileNumber << " of " << totalBatchSize);
               continue;
            }
            request.perform();
            long responseCode = curlpp::infos::ResponseCode::get(request);
            if (responseCode != 200)
            {
               updateDownloadStatus(settings.username.c_str(), fileNumber, XmppFileUploadStats::UploadStats::Status_Failure);
               safeCout("XmppFileUploadTestTool::test(): ********** file-transfer error: curlpp response-code: " << responseCode << " for " << settings.username.c_str() << " file-number: " << fileNumber << " of " << totalBatchSize);
               continue;
            }
            // ASSERT_EQ(responseCode, 200) << FAIL_LOG_FMT("file-transfer error: curlpp response-code: " << responseCode);
            safeCout("XmppFileUploadTestTool::test(): downloaded file: " << remoteFile << " file-number: " << fileNumber << " of " << totalBatchSize << " with size: " << responseBody.str().size());
            if (remoteFileSize != responseBody.str().size())
            {
               updateDownloadStatus(settings.username.c_str(), fileNumber, XmppFileUploadStats::UploadStats::Status_Failure);
               safeCout("XmppFileUploadTestTool::test(): ********** file-tranfer error: mismatch in file-size: " << responseBody.str().size() << " expected: " << remoteFileSize << " for " << settings.username.c_str() << " file-number: " << fileNumber << " of " << totalBatchSize);
               continue;
            }
            // ASSERT_EQ(remoteFileSize, responseBody.str().size()) << FAIL_LOG_FMT("file-tranfer error: mismatch in file-size: " << responseBody.str().size() << " expected: " << remoteFileSize);
            updateDownloadStatus(settings.username.c_str(), fileNumber, XmppFileUploadStats::UploadStats::Status_Success);
            successfulDownloads++;
         }
         catch (curlpp::LibcurlRuntimeError ex)
         {
            updateDownloadStatus(settings.username.c_str(), fileNumber, XmppFileUploadStats::UploadStats::Status_Failure);
            safeCout("XmppFileUploadTestTool::test(): ********** file-transfer error: curlpp exception. Error code: " << ex.whatCode() << " for " << settings.username.c_str() << " file-number: " << fileNumber << " of " << totalBatchSize);
            continue;
            // ASSERT_TRUE(false) << FAIL_LOG_FMT("XmppFileUploadTestTool::test(): curlpp exception. Error code: " << ex.whatCode() << " for " << settings.username.c_str() << " file-number: " << fileNumber << " of " << totalBatchSize);
         }
         catch (...)
         {
            updateDownloadStatus(settings.username.c_str(), fileNumber, XmppFileUploadStats::UploadStats::Status_Failure);
            safeCout("XmppFileUploadTestTool::test(): ********** file-transfer error: unexpected exception for " << settings.username.c_str() << " file-number: " << fileNumber << " of " << totalBatchSize);
            continue;
            // ASSERT_TRUE(false) << FAIL_LOG_FMT("XmppFileUploadTestTool::test(): unexpected exception for "  << settings.username.c_str() << " file-number: " << fileNumber << " of " << totalBatchSize);
         }
      }
   }

   ASSERT_TRUE(successfulDownloads == totalDownloads) << FAIL_LOG_FMT("mismatch in total downloads: " << successfulDownloads << " expected: " << totalDownloads);
   std::this_thread::sleep_for(std::chrono::milliseconds(2000)); // Let the final stats get updated so they can be rendered
   mStats->stop();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   safeCout(STATUS_LOG_FMT("XmppFileUploadTestTool::test(): ********** successfully downloaded " << successfulDownloads << " of " << totalDownloads << " files using " << mAccountSettings.size() << " accounts"));

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
}

void XmppFileUploadTestTool::uploadProgressTimerHandler(const boost::system::error_code& error)
{
   if (error == boost::asio::error::operation_aborted) return;

   mUploadProgressTimer->expires_from_now(boost::posix_time::milliseconds(mUploadProgressInterval));
   mUploadProgressTimer->async_wait(std::bind(&XmppFileUploadTestTool::uploadProgressTimerHandler, this, std::placeholders::_1));
}
