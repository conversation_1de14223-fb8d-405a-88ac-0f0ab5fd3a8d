/*
 *  xmpp_server_load_test_tool.cpp
 *
 *  Created by <PERSON> on Feb 25, 2020.
 *  Copyright 2020 CounterPath Corporation. All rights reserved.
*/

#if _WIN32
#include "stdafx.h"
#endif

#include <cstdlib>
#include <functional>
#include <regex>
#include <boost/asio.hpp>
#include <boost/lexical_cast.hpp>

#ifndef WIN32
#include <sys/resource.h>
#endif

#include "json/JsonHelper.h"
#include "brand_branded.h"

#include "../test_framework/xmpp_test_helper.h"

#include "xmpp/XmppAccountInternal.h"

using namespace CPCAPI2;
using namespace CPCAPI2::XmppAccount;

static std::recursive_mutex _safeCoutMutex;
static bool _safeCoutToggle = true;
#undef safeCout
#define safeCout(e) { using namespace CPCAPI2::test; using namespace CPCAPI2; std::lock_guard<std::recursive_mutex> lock(_safeCoutMutex); if (_safeCoutToggle) { std::cout.flush(); std::cerr.flush(); std::cout << e << std::endl; std::cout.flush(); } }

#define STATUS_LOG_FMT(x) "[[OK[" << x << "]]]"
#define FAIL_LOG_FMT(x) "[[NOK[" << x << "]]]"

// forward declarations
struct XmppServerLoadTestAccount;
struct XmppServerLoadTestAccountManager;
static XmppRoster::XmppCannedPresence getCannedPresence(const XmppRoster::ResourceItem& resourceItem);
static XmppServerLoadTestAccount* getRandomAccount();
static const std::string generateRandomString(unsigned int size = 1024);

template<typename T, typename... Args>
inline static std::unique_ptr<T> _make_unique(Args&&... args)
{
   return std::unique_ptr<T>(new T(std::forward<Args>(args)...));
}

struct Stats
{
   Stats()
   {
      fullReset();
   }

   void reset()
   {
      memset(this + offsetof(Stats, accountConnected), 0, offsetof(Stats, presenceAttempt) - offsetof(Stats, accountConnected));
   }

   void fullReset()
   {
      memset(this + offsetof(Stats, accountConnected), 0, offsetof(Stats, remotes) - offsetof(Stats, accountConnected));
   }

   size_t accountConnected;
   size_t accountFailure;
   size_t accountDisconnected;
   size_t accountConnecting;
   size_t accountDisconnecting;
   size_t accountDestroyed;

   // calculate request / response delay
   size_t eventCount;
   long long eventDelay;

   size_t processCount;
   long long processTime;

   // non-transient stats
   size_t presenceAttempt;
   size_t presenceSuccess;
   size_t presenceFailure;
   size_t presenceNeglect;
   size_t chatAttempt;
   size_t chatSuccess;
   size_t chatFailure;
   size_t chatNeglect;
   size_t mucAttempt;
   size_t mucSuccess;
   size_t mucFailure;
   size_t mucNeglect;
   size_t eventSuccess;
   size_t eventTimeout;
   size_t eventFailure;

   std::map<std::string, unsigned int> remotes;
};

struct XmppAccountSettingsEx : XmppAccountSettings
{
   XmppAccountSettingsEx(const XmppAccountSettings& settings, bool disableInitialSelfPresenceCheck)
      : XmppAccountSettings(settings)
      , disableInitialSelfPresenceCheck(disableInitialSelfPresenceCheck)
   {
   }

   bool disableInitialSelfPresenceCheck;
};

// global shared variables
static boost::asio::io_service ios;
static boost::asio::deadline_timer presenceTimer(ios);
static boost::asio::deadline_timer statsTimer(ios);
static boost::asio::deadline_timer accountActivationTimer(ios);
static boost::asio::signal_set signals(ios, SIGINT);

static bool enableConsole = false;
static unsigned int presenceTestInterval = 1000;
static unsigned int chatTestInterval = 1000;
static unsigned int mucTestInterval = 1000;
static unsigned int eventTimeout = 10000;
static unsigned int statsInterval = 3000;
static unsigned int activeAccounts = 0;
static unsigned int accountActivationInterval = 0;
static unsigned int fdLimit = 10000;

static std::vector<XmppServerLoadTestAccountManager*> managers;
static std::vector<XmppServerLoadTestAccount*> accounts;
static std::vector<XmppAccountSettingsEx> accountSettings;
static std::vector<XmppRoster::XmppCannedStatus> presenceSet;
static Stats stats;

static const std::map<std::string, int> predefinedGroups = {
   { "group1", 0 },
   { "group1.1", 20 },
   { "group1.1.1", 40 },
   { "group1.1.2", 60 },
   { "group1.1.3", 80 },
   { "group1.1.4", 100 },
   { "group1.2", 120 },
   { "group1.2.1", 140 },
   { "group1.2.2", 160 },
   { "group1.2.3", 180 },
   { "group1.2.4", 200 },
   { "group1.3", 220 },
   { "group1.3.1", 240 },
   { "group1.3.2", 260 },
   { "group1.3.3", 280 },
   { "group1.3.4", 300 },
   { "group2", 0 },
   { "group2.1", 320 },
   { "group2.1.1", 340 },
   { "group2.1.2", 360 },
   { "group2.1.3", 380 },
   { "group2.1.4", 400 },
   { "group2.2", 420 },
   { "group2.2.1", 440 },
   { "group2.2.2", 460 },
   { "group2.2.3", 480 },
   { "group2.2.4", 500 },
   { "group2.3", 520 },
   { "group2.3.1", 540 },
   { "group2.3.2", 560 },
   { "group2.3.3", 580 },
   { "group2.3.4", 600 },
};

class XmppServerLoadTestTool : public CpcapiAutoTest
{
public:
   XmppServerLoadTestTool() {}
   virtual ~XmppServerLoadTestTool() {}
};

struct XmppServerLoadTestAccountManager
{
   XmppServerLoadTestAccountManager()
      : running(true)
   {
      phone = PhoneInternal::create(managers.size() % std::thread::hardware_concurrency());
      phone->initialize(LicenseInfo(), (PhoneHandler*)NULL);
      phone->setLoggingEnabled(&AutoTestsLogger::instance(), true);

      account = XmppAccountManager::getInterface(phone);
      roster = XmppRoster::XmppRosterManager::getInterface(phone);
      chat = XmppChat::XmppChatManager::getInterface(phone);
      muc = XmppMultiUserChat::XmppMultiUserChatManager::getInterface(phone);

      // require initialized 'account' to run
      thread = new std::thread(&XmppServerLoadTestAccountManager::run, this);
   }

   ~XmppServerLoadTestAccountManager()
   {
      running = false;
      thread->join();
      delete thread;

      Phone::release(phone);
   }

   void run();

   Phone* phone;
   XmppAccountManager* account;
   XmppRoster::XmppRosterManager* roster;
   XmppChat::XmppChatManager* chat;
   XmppMultiUserChat::XmppMultiUserChatManager* muc;

   std::vector<XmppServerLoadTestAccount*> accounts;

   std::atomic<bool> running;
   std::thread* thread;
};

struct XmppServerLoadTestAccount
{
   XmppServerLoadTestAccount(XmppServerLoadTestAccountManager& _manager, const XmppAccountSettingsEx& _settings)
      : manager(_manager)
      , settings(_settings)
      , disableInitialSelfPresenceCheck(_settings.disableInitialSelfPresenceCheck)
      , status(XmppAccountStatusChangedEvent::Status_Disconnected)
      , presenceTestNext(std::chrono::steady_clock::now() + std::chrono::seconds(std::rand() % (presenceTestInterval / 1000))) // randomize tesst among accounts
      , chatTestNext(std::chrono::steady_clock::now() + std::chrono::seconds(std::rand() % (chatTestInterval / 1000))) // randomize tests among accounts
      , mucTestNext(std::chrono::steady_clock::now() + std::chrono::seconds(std::rand() % (mucTestInterval / 1000))) // randomize tests among accounts
      , inactive(false)
      , mucServiceAvailable(false)
   {
      manager.accounts.push_back(this);

      std::ostringstream oss;
      oss << settings.username << '_' << settings.domain << '_' << std::rand();

      settings.resource = oss.str().c_str();

      handle = manager.account->create(settings);
      manager.account->setHandler(handle, reinterpret_cast<XmppAccountHandler*>(0xDEADBEEF));

      manager.roster->setHandler(handle, reinterpret_cast<XmppRoster::XmppRosterHandler*>(0xDEADBEEF));
      rosterHandle = manager.roster->createRoster(handle);

      manager.chat->setHandler(handle, reinterpret_cast<XmppChat::XmppChatHandler*>(0xDEADBEEF));

      manager.muc->setHandler(handle, reinterpret_cast<XmppMultiUserChat::XmppMultiUserChatHandler*>(0xDEADBEEF));

      manager.account->enable(handle);

      staticEvent<XmppAccountHandle, XmppAccountStatusChangedEvent>("XmppAccountHandler::onAccountStatusChanged", [this](XmppAccountHandle _handle, const XmppAccountStatusChangedEvent& evt) {
         if (_handle != handle) return EventHandler::NotHandled;

         // clean up all pending events
         timedEventHandlers.clear();

         status = evt.accountStatus;

         switch (status)
         {
         case XmppAccountStatusChangedEvent::Status_Connected:
            safeCout("account=" << settings.resource << " connected to " << evt.remote);

            remote = evt.remote;
            ++stats.remotes[remote];

            // resumption may not have this event, just wait for its expiration then start presence test
            expectEvent<XmppRoster::XmppRosterHandle, XmppRoster::XmppRosterUpdateEvent>("XmppRosterHandler::onRosterUpdate", eventTimeout, [this](XmppRoster::XmppRosterHandle handle, const XmppRoster::XmppRosterUpdateEvent& evt) {
               if (handle != rosterHandle) return EventHandler::NotHandled;
               if (!evt.fullUpdate) return EventHandler::NotHandled;

               if (evt.added.size() < accounts.size() - 1) safeCout(FAIL_LOG_FMT("account=" << settings.resource << " roster isn't populated fully"));

               if (evt.added.empty())
               {
                  safeCout(FAIL_LOG_FMT("account=" << settings.resource << " roster shouldn't be empty"));
               }
               else if (evt.added[std::rand() % evt.added.size()].item.subscription != XmppRoster::SubscriptionState_InOut)
               {
                  safeCout(FAIL_LOG_FMT("account=" << settings.resource << " roster item should be subscribed BOTH"));
               }

               if (!disableInitialSelfPresenceCheck)
               {
                  // check initial presence
                  expectEvent<XmppRoster::XmppRosterHandle, XmppRoster::XmppRosterPresenceEvent>("XmppRosterHandler::onSelfPresence", eventTimeout, [this](XmppRoster::XmppRosterHandle handle, const XmppRoster::XmppRosterPresenceEvent& evt) {
                     if (handle != rosterHandle) return EventHandler::NotHandled;
                     assert(evt.rosterItem.address == settings.username + "@" + settings.domain);

                     // self presence from other login
                     if (evt.resource != settings.resource) return EventHandler::NotHandled;

                     // multiple presences of the same account
                     for (auto& item : evt.rosterItem.resources)
                     {
                        if (item.resource != settings.resource) continue;

                        return getCannedPresence(item).status == XmppRoster::XmppCannedStatus_Available ? EventHandler::Handled : EventHandler::Failure;
                     }

                     return EventHandler::Failure;
                  }, true);
               }

               return EventHandler::Handled;
            });

            break;

         case XmppAccountStatusChangedEvent::Status_Destroyed:
         case XmppAccountStatusChangedEvent::Status_Failure:
         case XmppAccountStatusChangedEvent::Status_Disconnected:
            switch (status) // awkward nesting but for output only
            {
            case XmppAccountStatusChangedEvent::Status_Destroyed:
               safeCout("account=" << settings.resource << " destroyed");
               break;

            case XmppAccountStatusChangedEvent::Status_Failure:
               safeCout("account=" << settings.resource << " failure");
               break;

            case XmppAccountStatusChangedEvent::Status_Disconnected:
               safeCout("account=" << settings.resource << " disconnected");
               break;

            default:
               assert(false);
            }

            // disconnect without connected
            if (remote.empty()) break;

            assert(stats.remotes[remote] > 0);
            --stats.remotes[remote];

            if (stats.remotes[remote] == 0) stats.remotes.erase(remote);

            remote.clear();
            break;

         case XmppAccountStatusChangedEvent::Status_Connecting:
            safeCout("account=" << settings.resource << " connecting");

            expectEvent<XmppAccountHandle, XmppAccountStatusChangedEvent>("XmppAccountHandler::onAccountStatusChanged", eventTimeout, [this](XmppAccountHandle _handle, const XmppAccountStatusChangedEvent& evt) {
               if (_handle != handle) return EventHandler::NotHandled;
               return evt.accountStatus == XmppAccountStatusChangedEvent::Status_Connected ? EventHandler::Handled : EventHandler::Failure;
            });

            break;

         case XmppAccountStatusChangedEvent::Status_Disconnecting:
            safeCout("account=" << settings.resource << " disconnecting");
            break;

         case XmppAccountStatusChangedEvent::Status_Resuming:
            safeCout("account=" << settings.resource << " resuming");

            expectEvent<XmppAccountHandle, XmppAccountStatusChangedEvent>("XmppAccountHandler::onAccountStatusChanged", eventTimeout, [this](XmppAccountHandle _handle, const XmppAccountStatusChangedEvent& evt) {
               if (_handle != handle) return EventHandler::NotHandled;
               return evt.accountStatus == XmppAccountStatusChangedEvent::Status_Resumed ? EventHandler::Handled : EventHandler::Failure;
            });

            assert(!remote.empty());
            assert(stats.remotes[remote] > 0);
            --stats.remotes[remote];

            if (stats.remotes[remote] == 0) stats.remotes.erase(remote);

            remote.clear();

            break;

         case XmppAccountStatusChangedEvent::Status_Resumed:
            safeCout("account=" << settings.resource << " resumed");

            remote = evt.remote;
            ++stats.remotes[remote];

            break;

         default:
            assert(0);
         }

         return EventHandler::Handled;
      });

      staticEvent<XmppAccountHandle, XmppMultiUserChat::ServiceAvailabilityEvent>("XmppMultiUserChatHandler::onServiceAvailability", [this](XmppAccountHandle _handle, const XmppMultiUserChat::ServiceAvailabilityEvent& evt) {
         if (_handle != handle) return EventHandler::NotHandled;
         mucServiceAvailable = evt.available;
         return EventHandler::Handled;
      });

      staticEvent<XmppAccountHandle, XmppAccount::ErrorEvent>("XmppAccountHandler::onError", [this](XmppAccountHandle _handle, const XmppAccount::ErrorEvent& evt) {
         if (_handle != handle) return EventHandler::NotHandled;
         safeCout("account=" << settings.resource << " error=" << evt.errorText);
         return EventHandler::Handled;
      });

      expectEvent<XmppAccountHandle, XmppAccountStatusChangedEvent>("XmppAccountHandler::onAccountStatusChanged", eventTimeout, [this](XmppAccountHandle _handle, const XmppAccountStatusChangedEvent& evt) {
         if (_handle != handle) return EventHandler::NotHandled;
         return evt.accountStatus == XmppAccountStatusChangedEvent::Status_Connecting ? EventHandler::Handled : EventHandler::Failure;
      });
   }

   ~XmppServerLoadTestAccount()
   {
      manager.account->disable(handle);
      manager.account->destroy(handle);
      //expectDestroyed

      XmppAccountStatusChangedEvent evt;
      evt.accountStatus = XmppAccountStatusChangedEvent::Status_Destroyed;
      process(makeFpCommandNew("XmppAccountHandler::onAccountStatusChanged", &XmppAccountHandler::onAccountStatusChanged, reinterpret_cast<XmppAccountHandler*>(0xDEADBEEF), handle, evt));

      manager.accounts.erase(std::remove(manager.accounts.begin(), manager.accounts.end(), this));
   }

   void presenceTest()
   {
      auto now = std::chrono::steady_clock::now();

      if (presenceTestNext > now) return;

      if (presenceSet.empty())
      {
         safeCout(FAIL_LOG_FMT("presence set is empty, validate presenceWhiteList and presenceBlackList"));
         return;
      }

      ++stats.presenceAttempt;

      presenceTestNext = now + std::chrono::milliseconds(presenceTestInterval);

      // if there is a pending timed event, don't queue another one to limit the size of timedEventHandlers
      if ((status != XmppAccountStatusChangedEvent::Status_Connected && status != XmppAccountStatusChangedEvent::Status_Resumed) || !timedEventHandlers.empty())
      {
         ++stats.presenceNeglect;
         return;
      }

      XmppRoster::XmppCannedStatus cannedStatus = presenceSet[std::rand() % presenceSet.size()];

      // too many outputs
      safeCout("PresenceTest account=" << settings.resource << " presence=" << cannedStatus);
      manager.account->publishCannedPresence(handle, cannedStatus);

      // no self presence if inactive
      if (inactive)
      {
         ++stats.presenceNeglect;
         return;
      }

      expectEvent<XmppRoster::XmppRosterHandle, XmppRoster::XmppRosterPresenceEvent>("XmppRosterHandler::onSelfPresence", eventTimeout, [=](XmppRoster::XmppRosterHandle handle, const XmppRoster::XmppRosterPresenceEvent& evt) {
         if (handle != rosterHandle) return EventHandler::NotHandled;
         assert(evt.rosterItem.address == settings.username + "@" + settings.domain);

         // self presence from other login
         if (evt.resource != settings.resource) return EventHandler::NotHandled;

         // multiple presences of the same account
         for (auto& item : evt.rosterItem.resources)
         {
            if (item.resource != settings.resource) continue;

            if (getCannedPresence(item).status != cannedStatus)
            {
               safeCout(FAIL_LOG_FMT("account=" << settings.resource << " actual presence=" << getCannedPresence(item).status << " doesn't match published presence=" << cannedStatus));
               return EventHandler::NotHandled; // a bit hack here, return NotHandled instead of Failure to allow the presence handler to expire or match dislocated event
            }

            // too many outputs
            safeCout(STATUS_LOG_FMT("account=" << settings.resource << " presence matches " << getCannedPresence(item).status));
            ++stats.eventCount;
            stats.eventDelay += std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::steady_clock::now() - now).count();

            return EventHandler::Handled;
         }

         // some presence doesn't have resource at all, check against offline presence directly
         // this won't be calculated in stats
         return evt.compositeCannedPresence.status == XmppRoster::XmppCannedStatus_Offline ? EventHandler::Handled : EventHandler::Failure;
      }, false, [](const EventHandler::Result& result) {
         switch (result)
         {
         case EventHandler::NotHandled:
            ++stats.presenceNeglect;
            break;

         case EventHandler::Handled:
            ++stats.presenceSuccess;
            break;

         case EventHandler::Failure:
            ++stats.presenceFailure;
            break;

         case EventHandler::Expired:
            ++stats.presenceFailure;
            break;

         default:
            assert(false);
         }
      });

      // special treatment for Invisible as two presences will be published for this status
      if (cannedStatus == XmppRoster::XmppCannedStatus_Invisible)
      {
         expectEvent<XmppRoster::XmppRosterHandle, XmppRoster::XmppRosterPresenceEvent>("XmppRosterHandler::onSelfPresence", eventTimeout, [=](XmppRoster::XmppRosterHandle handle, const XmppRoster::XmppRosterPresenceEvent& evt) {
            if (handle != rosterHandle) return EventHandler::NotHandled;
            assert(evt.rosterItem.address == settings.username + "@" + settings.domain);

            // self presence from other login
            if (evt.resource != settings.resource) return EventHandler::NotHandled;

            // multiple presences of the same account
            for (auto& item : evt.rosterItem.resources)
            {
               if (item.resource != settings.resource) continue;

               // do not count the secondary presence in stats

               if (getCannedPresence(item).status != XmppRoster::XmppCannedStatus_Available)
               {
                  safeCout(FAIL_LOG_FMT("account=" << settings.resource << " doesn't match expected invisibility's 2nd presence update"));
                  return EventHandler::Failure;
               }

               stats.eventDelay += std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::steady_clock::now() - now).count(); // this delay is inaccurate because 'now' is not re-evaulated

               return EventHandler::Handled;
            }

            return EventHandler::Failure;
         }, true);
      }
   }

#if 0 // bliu: disable as there is no good way to deallocate a chat
   void chatTest()
   {
      auto now = std::chrono::steady_clock::now();

      if (chatTestExpiry > now) return;

      chatTestExpiry = now + std::chrono::milliseconds(chatTestInterval);

      // if there is a pending timed event, don't queue another one to limit the size of timedEventHandlers
      if ((status != XmppAccountStatusChangedEvent::Status_Connected && status != XmppAccountStatusChangedEvent::Status_Resumed) || !timedEventHandlers.empty())
      {
         ++stats.chatNeglect;
         return;
      }

      std::string message = generateRandomString();

      // too many outputs
      safeCout("ChatTest account=" << settings.resource << " message=" << message);
      auto chatHandle = manager.chat->createChat(handle);

      auto target = getRandomAccount();
      if (target == NULL) return;

      manager.chat->addParticipant(chatHandle, target->settings.username + "@" + target->settings.domain);

      auto messageHandle = manager.chat->sendMessage(chatHandle, message.c_str());

      expectEvent<XmppChat::XmppChatHandle, XmppChat::SendMessageSuccessEvent>("XmppChatHandler::onSendMessageSuccess", eventTimeout, [=](XmppChat::XmppChatHandle handle, const XmppChat::SendMessageSuccessEvent& evt) {
         if (handle != chatHandle) return EventHandler::NotHandled;
         if (evt.message != messageHandle) return EventHandler::Failure;

         return EventHandler::Handled;
      });

      target->expectEvent<XmppChat::XmppChatHandle, XmppChat::NewMessageEvent>("XmppChatHandler::onNewMessage", eventTimeout, [=](XmppChat::XmppChatHandle handle, const XmppChat::NewMessageEvent& evt) {
         if (handle != target->handle) return EventHandler::NotHandled;

         return EventHandler::Handled;
      });
   }
#endif

   void mucTest()
   {
      auto now = std::chrono::steady_clock::now();

      if (mucTestNext > now) return;

      ++stats.mucAttempt;

      mucTestNext = now + std::chrono::milliseconds(mucTestInterval);

      // if there is a pending timed event, don't queue another one to limit the size of timedEventHandlers
      if ((status != XmppAccountStatusChangedEvent::Status_Connected && status != XmppAccountStatusChangedEvent::Status_Resumed) || !mucServiceAvailable || !timedEventHandlers.empty())
      {
         ++stats.mucNeglect;
         return;
      }

      std::string message = generateRandomString();

      auto mucHandle = manager.muc->create(handle, cpc::string("test"));
      XmppMultiUserChat::RoomConfig config;
      config.createIfNotExisting = true;
      config.isInstant = true;
      manager.muc->join(mucHandle, config, settings.username);

      // bliu: no way to check mucFailure
      expectEvent<XmppMultiUserChat::XmppMultiUserChatHandle, XmppMultiUserChat::MultiUserChatReadyEvent>("XmppMultiUserChatHandler::onMultiUserChatReady", 300000, [=](XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::MultiUserChatReadyEvent& evt) {
         if (handle != mucHandle) return EventHandler::NotHandled;

         safeCout("MUCTest account=" << settings.resource << " joins room " << evt.roomjid);

         return EventHandler::Handled;
      }, false, [=](const EventHandler::Result& result) {
         switch (result)
         {
         case EventHandler::NotHandled:
            ++stats.mucNeglect;
            break;

         case EventHandler::Handled:
            ++stats.mucSuccess;
            break;

         case EventHandler::Failure:
            ++stats.mucFailure;
            break;

         case EventHandler::Expired:
            ++stats.mucFailure;
            break;

         default:
            assert(false);
         }

         manager.muc->leave(mucHandle);

#if 0 // cannot add more event in destructor otherwise timedEventHandlers.clear() may crash
         // TODO add adjacent event to onComplete
         expectEvent<XmppMultiUserChat::XmppMultiUserChatHandle, XmppMultiUserChat::LocalUserLeftEvent>("XmppMultiUserChatHandler::onLocalUserLeft", eventTimeout, [=](XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::LocalUserLeftEvent& evt) {
            if (handle != mucHandle) return EventHandler::NotHandled;

            safeCout("MUCTest account=" << settings.resource << " leaves room");
            return EventHandler::Handled;
         });
#endif

      });
   }

   struct EventHandler
   {
      enum Result { Handled, NotHandled, Failure, Expired };

      EventHandler(const std::string& eventName, int timeoutMS = -1, bool adjacent = false, std::function<void(const Result&)> onComplete = NULL)
         : eventName(eventName)
         , timeoutMS(timeoutMS)
         , adjacent(adjacent)
         , result(NotHandled)
         , onComplete(onComplete)
      {
         if (timeoutMS != -1) expiry = std::chrono::steady_clock::now() + std::chrono::milliseconds(timeoutMS);
      }

      virtual ~EventHandler()
      {
         if (onComplete != NULL) onComplete(result);
      }

      virtual Result handle(AutoTestReadCallback* cmd) = 0;

      const std::string eventName;
      int timeoutMS;
      std::chrono::steady_clock::time_point expiry; // may be modified if adjacent
      const bool adjacent; // the event continues from the previous one. if the previous fails, this one won't be handled
      Result result;
      std::function<void(const Result&)> onComplete;
   };

   template<typename Arg0, typename Arg1>
   struct TimedEventHandler : EventHandler
   {
      TimedEventHandler(const std::string& eventName, unsigned int timeoutMS, typename std::function<Result(Arg0, Arg1)> handler, bool adjacent, std::function<void(const Result&)> onComplete)
         : EventHandler(eventName, timeoutMS, adjacent, onComplete)
         , handler(handler)
      {
      }

      virtual Result handle(AutoTestReadCallback* cmd)
      {
         assert(cmd->eventName() == eventName);
         assert(expiry > std::chrono::steady_clock::now());
         result = handler(cmd->arg0<Arg0, Arg1>(), cmd->arg1<Arg0, Arg1>());
         return result;
      }

      typename std::function<Result(Arg0, Arg1)> handler;
   };

   template<typename Arg0, typename Arg1>
   struct StaticEventHandler : EventHandler
   {
      StaticEventHandler(const std::string& eventName, typename std::function<Result(Arg0, Arg1)> handler)
         : EventHandler(eventName)
         , handler(handler)
      {
      }

      virtual Result handle(AutoTestReadCallback* cmd)
      {
         assert(cmd->eventName() == eventName);
         assert(timeoutMS == -1);
         result = handler(cmd->arg0<Arg0, Arg1>(), cmd->arg1<Arg0, Arg1>());
         return result;
      }

      typename std::function<Result(Arg0, Arg1)> handler;
   };

   template<typename Arg0, typename Arg1>
   void expectEvent(const std::string& eventName, unsigned int timeoutMS, typename std::function<EventHandler::Result(Arg0, Arg1)> handler, bool adjacent = false, std::function<void(const EventHandler::Result&)> onComplete = NULL)
   {
      timedEventHandlers.push_back(_make_unique<TimedEventHandler<Arg0, Arg1> >(eventName, timeoutMS, handler, adjacent, onComplete));
   }

   template<typename Arg0, typename Arg1>
   void staticEvent(const std::string& eventName, typename std::function<EventHandler::Result(Arg0, Arg1)> handler)
   {
      assert(staticEventHandlers.find(eventName) == staticEventHandlers.end());
      staticEventHandlers[eventName] = _make_unique<StaticEventHandler<Arg0, Arg1> >(eventName, handler);
   }

   void process(AutoTestReadCallback* cmd)
   {
      // assume all timed events are in chrono order
      while (!timedEventHandlers.empty())
      {
         auto& handler = timedEventHandlers.front();

         // consume all expired events
         if (handler->expiry <= std::chrono::steady_clock::now())
         {
            safeCout(FAIL_LOG_FMT("account=" << settings.resource << " expired handler=" << handler->eventName));

            handler->result = EventHandler::Expired;
            timedEventHandlers.pop_front();
            ++stats.eventTimeout;

            // consume all adjacent events
            while (!timedEventHandlers.empty())
            {
               auto& handler = timedEventHandlers.front();
               if (!handler->adjacent) break;

               safeCout(FAIL_LOG_FMT("account=" << settings.resource << " expired adjacent handler=" << handler->eventName));

               handler->result = EventHandler::Expired;
               timedEventHandlers.pop_front();
               ++stats.eventTimeout;
            }

            continue;
         }

         // make sure there is a valid incoming event to be handled, check this after consuming all expired events
         if (cmd != NULL && cmd->eventName() == handler->eventName)
         {
            EventHandler::Result result = handler->handle(cmd);
            if (result == EventHandler::NotHandled) break;

            // if the current event handler fails, clean up all adjacent events
            if (result == EventHandler::Failure)
            {
               safeCout(FAIL_LOG_FMT("account=" << settings.resource << " failed handler=" << handler->eventName));

               timedEventHandlers.pop_front();
               ++stats.eventFailure;

               while (!timedEventHandlers.empty())
               {
                  auto& handler = timedEventHandlers.front();
                  if (!handler->adjacent) break;

                  safeCout(FAIL_LOG_FMT("account=" << settings.resource << " failed adjacent handler=" << handler->eventName));

                  handler->result = EventHandler::Failure;
                  timedEventHandlers.pop_front();
                  ++stats.eventFailure;
               }
            }
            // adjust the adjacent event's expiry
            else
            {
               timedEventHandlers.pop_front();
               ++stats.eventSuccess;

               if (!timedEventHandlers.empty())
               {
                  auto& handler = timedEventHandlers.front();
                  if (handler->adjacent) handler->expiry = std::chrono::steady_clock::now() + std::chrono::milliseconds(handler->timeoutMS);
               }
            }
         }

         break;
      }

      // static handler should be processed after expected handler because some static handler may add new expected handler
      // newly created expected handler would be processed right after which causes failure
      auto it = staticEventHandlers.find(cmd != NULL ? cmd->eventName() : "");
      if (it != staticEventHandlers.end()) it->second->handle(cmd);
   }

   XmppServerLoadTestAccountManager& manager;

   XmppAccountHandle handle;
   XmppRoster::XmppRosterHandle rosterHandle;

   XmppAccountSettings settings;
   bool disableInitialSelfPresenceCheck;
   XmppAccountStatusChangedEvent::Status status;

   std::deque<std::unique_ptr<EventHandler> > timedEventHandlers;
   std::map<std::string, std::unique_ptr<EventHandler> > staticEventHandlers;

   std::string remote;

   std::chrono::steady_clock::time_point presenceTestNext;
   std::chrono::steady_clock::time_point chatTestNext;
   std::chrono::steady_clock::time_point mucTestNext;

   bool inactive;
   bool mucServiceAvailable;
};

void XmppServerLoadTestAccountManager::run()
{
   while (running)
   {
      auto cmd = dynamic_cast<AutoTestProcessor*>(account)->process_test(100);

#if 0
      // filter out unnecessary events to save resource
      if (cmd != NULL)
      {
         if (cmd->eventName() != "XmppAccountHandler::onAccountStatusChanged"
            && cmd->eventName() != "XmppRosterHandler::onRosterUpdate"
            && cmd->eventName() != "XmppRosterHandler::onSelfPresence")
         {
            delete cmd;
            continue;
         }
      }
#endif

      // need NULL cmd to trigger expiry handling
      ios.post([cmd, this]() {
         auto now = std::chrono::steady_clock::now();

         for (auto account : accounts)
         {
            account->process(cmd);
            account->presenceTest();
            //account->chatTest();
            account->mucTest();
         }

         delete cmd;

         ++stats.processCount;
         stats.processTime += std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::steady_clock::now() - now).count();
      });
   }
}

static void setup()
{
   safeCout(STATUS_LOG_FMT("setup"));

   std::string configFileName = "xmppServerLoadTestTool.json";
   ASSERT_TRUE(TestEnvironmentConfig::getResourceFile(configFileName, configFileName));
   resip::Data configFile = configFileName.c_str();

   // load configurations
   std::ifstream ifs(configFile.c_str());
   ASSERT_TRUE(ifs.is_open()) << FAIL_LOG_FMT("failed when opening " << configFileName.c_str() << " file");

   std::ostringstream oss;
   oss << ifs.rdbuf() << std::flush;

   rapidjson::Document json;

   json.Parse<0>(oss.str().c_str());
   ASSERT_FALSE(json.HasParseError()) << FAIL_LOG_FMT("parse error occured in " << configFileName.c_str() << ":" << json.GetParseError());

   if (!json.HasMember("logToConsole") || !json["logToConsole"].GetBool())
   {
      putenv("CPCAPI2_LOG_TO_CONSOLE=0");
   }

   if (json.HasMember("console"))
   {
      enableConsole = json["console"].GetBool();
   }

   if (json.HasMember("presenceTestIntervalInMilliseconds"))
   {
      presenceTestInterval = json["presenceTestIntervalInMilliseconds"].GetUint();
      ASSERT_FALSE(presenceTestInterval < 1000) << FAIL_LOG_FMT("presenceTestIntervalInMilliseconds should be no less than 1000");
   }

   if (json.HasMember("chatTestIntervalInMilliseconds"))
   {
      chatTestInterval = json["chatTestIntervalInMilliseconds"].GetUint();
      ASSERT_FALSE(chatTestInterval < 1000) << FAIL_LOG_FMT("chatTestIntervalInMilliseconds should be no less than 1000");
   }

   if (json.HasMember("mucTestIntervalInMilliseconds"))
   {
      mucTestInterval = json["mucTestIntervalInMilliseconds"].GetUint();
      ASSERT_FALSE(mucTestInterval < 1000) << FAIL_LOG_FMT("mucTestIntervalInMilliseconds should be no less than 1000");
   }

   if (json.HasMember("eventTimeoutInMilliseconds"))
   {
      eventTimeout = json["eventTimeoutInMilliseconds"].GetUint();
   }

   if (json.HasMember("statsIntervalInMilliseconds"))
   {
      statsInterval = json["statsIntervalInMilliseconds"].GetUint();
   }

   if (json.HasMember("accountActivationIntervalInMilliseconds"))
   {
      accountActivationInterval = json["accountActivationIntervalInMilliseconds"].GetUint();
   }

   if (json.HasMember("fdLimit"))
   {

#ifdef WIN32
      safeCout("fdLimit is not supported under Win32");
#else
      fdLimit = json["fdLimit"].GetUint();
#endif

   }

#ifdef WIN32
   safeCout("max number of accounts cannot exceed 120 under Win32");
#else
   rlimit limit;
   limit.rlim_cur = limit.rlim_max = fdLimit;
   ASSERT_TRUE(setrlimit(RLIMIT_NOFILE, &limit) == 0);
#endif

   if (json.HasMember("presenceWhiteList"))
   {
      const auto& list_json = json["presenceWhiteList"];
      ASSERT_TRUE(list_json.IsArray()) << FAIL_LOG_FMT("node presenceWhiteList is not an array");

      for (auto it = list_json.Begin(), end = list_json.End(); it != end; ++it)
      {
         ASSERT_TRUE(it->IsUint()) << FAIL_LOG_FMT("item of presenceWhiteList is not an unsigned int");
         auto presence = it->GetUint();
         ASSERT_TRUE(presence < XmppRoster::XmppCannedStatus_Invalid) << FAIL_LOG_FMT("item of presenceWhiteList should be between 0 and " << XmppRoster::XmppCannedStatus_Invalid);
         presenceSet.push_back(static_cast<XmppRoster::XmppCannedStatus>(presence));
      }
   }

   if (presenceSet.empty())
   {
      for (int i = 0; i < XmppRoster::XmppCannedStatus_Invalid; ++i) presenceSet.push_back(static_cast<XmppRoster::XmppCannedStatus>(i));
   }

   std::vector<XmppRoster::XmppCannedStatus> presenceBlackList;

   if (json.HasMember("presenceBlackList"))
   {
      const auto& list_json = json["presenceBlackList"];
      ASSERT_TRUE(list_json.IsArray()) << FAIL_LOG_FMT("node presenceBlackList is not an array");

      for (auto it = list_json.Begin(), end = list_json.End(); it != end; ++it)
      {
         ASSERT_TRUE(it->IsUint()) << FAIL_LOG_FMT("item of presenceBlackList is not an unsigned int");
         auto presence = it->GetUint();
         ASSERT_TRUE(presence < XmppRoster::XmppCannedStatus_Invalid) << FAIL_LOG_FMT("item of presenceBlackList should be between 0 and " << XmppRoster::XmppCannedStatus_Invalid);
         presenceBlackList.push_back(static_cast<XmppRoster::XmppCannedStatus>(presence));
      }
   }
   else
   {
      presenceBlackList.push_back(XmppRoster::XmppCannedStatus_Invisible);
      presenceBlackList.push_back(XmppRoster::XmppCannedStatus_Offline);
      presenceBlackList.push_back(XmppRoster::XmppCannedStatus_Other);
   }

   for (auto presence : presenceBlackList)
   {
      presenceSet.erase(std::remove(presenceSet.begin(), presenceSet.end(), static_cast<XmppRoster::XmppCannedStatus>(presence)));
   }

   // load user group
   ASSERT_TRUE(json.HasMember("userGroup")) << FAIL_LOG_FMT("missing node: userGroup");

   std::string group = json["userGroup"].GetString();
   ASSERT_TRUE(!group.empty()) << FAIL_LOG_FMT("userGroup must not be empty");

   ASSERT_TRUE(json.HasMember(group.c_str())) << FAIL_LOG_FMT("missing node: " << group);

   const auto& group_json = json[group.c_str()];
   ASSERT_TRUE(group_json.IsObject()) << FAIL_LOG_FMT("node " << group << " is not an object");

   ASSERT_TRUE(group_json.HasMember("accounts")) << FAIL_LOG_FMT("missing node: accounts");

   const auto& accounts_json = group_json["accounts"];
   ASSERT_TRUE(accounts_json.IsArray()) << FAIL_LOG_FMT("node accounts is not an array");

   // load user accounts
   for (auto it = accounts_json.Begin(), end = accounts_json.End(); it != end; ++it)
   {
      ASSERT_TRUE(it->HasMember("xmppAccountSettings")) << FAIL_LOG_FMT("missing xmppAccountSettings node");

      const auto& settings_json = (*it)["xmppAccountSettings"];
      ASSERT_TRUE(settings_json.IsObject()) << FAIL_LOG_FMT("xmppAccountSettings node not an object");

      bool disableInitialSelfPresenceCheck = false;
      if (settings_json.HasMember("disableInitialSelfPresenceCheck"))
      {
         disableInitialSelfPresenceCheck = settings_json["disableInitialSelfPresenceCheck"].GetBool();
      }

      XmppAccountSettings settings;
      JsonDeserialize(*it, "xmppAccountSettings", settings);

      if (group_json.HasMember("isPredefined") && group_json["isPredefined"].GetBool())
      {
         auto it = predefinedGroups.find(settings.domain.c_str());
         ASSERT_TRUE(it != predefinedGroups.end()) << FAIL_LOG_FMT("invalid predefined group: " << settings.domain);

         for (int i = 0; i < it->second; ++i)
         {
            std::ostringstream ss;
            ss << "user" << std::internal << std::setfill('0') << std::setw(4) << i;
            settings.username = ss.str().c_str();
            ss << '@' << settings.domain << ":1234";
            settings.password = ss.str().c_str();

            accountSettings.push_back(XmppAccountSettingsEx(settings, disableInitialSelfPresenceCheck));
         }
      }
      else
      {
         accountSettings.push_back(XmppAccountSettingsEx(settings, disableInitialSelfPresenceCheck));
      }
   }

   if (group_json.HasMember("activeAccounts"))
   {
      activeAccounts = std::min<unsigned int>(group_json["activeAccounts"].GetUint(), accountSettings.size());
   }

   // create account managers
   for (int i = 0; i < std::thread::hardware_concurrency(); ++i) managers.push_back(new XmppServerLoadTestAccountManager());

   safeCout(STATUS_LOG_FMT("setup finished"));
}

static void shutdown()
{
   safeCout(STATUS_LOG_FMT("shutdown"));

   for (auto account : accounts) delete account;

   // async release multiple phones at the same time (encountered some deadlock previously)
   std::vector<std::future<void> > futures;
   for (auto manager : managers) futures.emplace_back(std::async(std::launch::async, [=]() { delete manager; }));

   for (auto& future : futures) future.wait();
}

// mimiced from fromResourceItem() in XmppCannedPresence.cpp
static XmppRoster::XmppCannedPresence getCannedPresence(const XmppRoster::ResourceItem& resourceItem)
{
   XmppRoster::XmppCannedPresence presence;
   switch (resourceItem.presenceType)
   {
   case XmppRoster::PresenceType_Available:
      presence.status = XmppRoster::XmppCannedStatus_Available;
      break;
   case XmppRoster::PresenceType_Chat:
      presence.status = XmppRoster::XmppCannedStatus_Chat;
      break;
   case XmppRoster::PresenceType_Away:
      presence.status = XmppRoster::XmppCannedStatus_Away;
      break;
   case XmppRoster::PresenceType_DND:
      presence.status = XmppRoster::XmppCannedStatus_DoNotDisturb;
      break;
   case XmppRoster::PresenceType_XA:
      presence.status = XmppRoster::XmppCannedStatus_Away;
      break;
   case XmppRoster::PresenceType_Unavailable:
      presence.status = XmppRoster::XmppCannedStatus_Offline;
      break;
   default:
      presence.status = XmppRoster::XmppCannedStatus_Invalid;
      break;
   }

   if (resourceItem.userActivityGeneralType == XmppRoster::ActivityInactive && resourceItem.userActivitySpecificType == XmppRoster::ActivityHiding)
      presence.status = XmppRoster::XmppCannedStatus_AppearAway;
   else if (resourceItem.userActivityGeneralType == XmppRoster::ActivityWorking && resourceItem.userActivitySpecificType == XmppRoster::ActivityOther)
      presence.status = XmppRoster::XmppCannedStatus_Busy;
   else if (resourceItem.userActivitySpecificType == XmppRoster::ActivityHavingLunch)
      presence.status = XmppRoster::XmppCannedStatus_HavingLunch;
   else if (resourceItem.userActivitySpecificType == XmppRoster::ActivityOnVacation)
      presence.status = XmppRoster::XmppCannedStatus_OnVacation;
   else if (resourceItem.userActivitySpecificType == XmppRoster::ActivityScheduledHoliday)
      presence.status = XmppRoster::XmppCannedStatus_ScheduledHoliday;
   else if (resourceItem.userActivityGeneralType == XmppRoster::ActivityInactive && resourceItem.userActivitySpecificType == XmppRoster::ActivityOther)
      presence.status = XmppRoster::XmppCannedStatus_InactiveOther;
   else if (resourceItem.userActivitySpecificType == XmppRoster::ActivityOnThePhone)
      presence.status = XmppRoster::XmppCannedStatus_OnThePhone;
   else if (resourceItem.userActivityGeneralType == XmppRoster::ActivityUndefinedGeneralType && resourceItem.userActivitySpecificType == XmppRoster::ActivityOther)
      presence.status = XmppRoster::XmppCannedStatus_NotAvailableForCalls;
   else if (resourceItem.presenceType == XmppRoster::PresenceType_XA && resourceItem.userActivityGeneralType == XmppRoster::ActivityWorking && resourceItem.userActivitySpecificType == XmppRoster::ActivityHiding)
      presence.status = XmppRoster::XmppCannedStatus_AppearOffline;

   return presence;
}

static XmppServerLoadTestAccount* getRandomAccount()
{
   if (accounts.empty()) return NULL;

   return accounts[std::rand() % accounts.size()];
}

static const std::string generateRandomString(unsigned int size)
{
   if (size == 0) return "";

   std::string message(std::rand() % size, 0);
   std::generate_n(message.begin(), message.size(), []() {
      static const char charSet[] = {
         "**********"
         "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
         "abcdefghijklmnopqrstuvwxyz"
         " `-=[]\\;',./~!@#$%^&*()_+{}|:\"<>?"
      };

      return charSet[std::rand() % sizeof(charSet) - 1];
   });

   return message;
}

template<typename T>
struct TableFormat
{
   TableFormat(const std::string& field, const T& v, int fieldWidth, int valueWidth)
      : field(field)
      , v(v)
      , fieldWidth(fieldWidth)
      , valueWidth(valueWidth)
   {
   }

   const std::string field;
   const T v;
   const int fieldWidth;
   const int valueWidth;
};

template<typename T>
static TableFormat<T> makeTableFormat(const std::string& field, const T& v, int fieldWidth = 20, int valueWidth = 10)
{
   return TableFormat<T>(field, v, fieldWidth, valueWidth);
}

template<typename T>
std::ostream& operator <<(std::ostream& os, const TableFormat<T>& tf)
{
   std::ostringstream oss;
   oss << std::setfill('.') << std::left << std::setw(tf.fieldWidth) << tf.field << std::setw(tf.valueWidth) << std::right << tf.v;
   return os << oss.str();
}

template<int width = 10>
std::ostream& slot(std::ostream& os)
{
   return os << std::setfill(' ') << std::right << std::setw(width) << ' ';
}

static void statsTimerHandler(const boost::system::error_code& error)
{
   if (error == boost::asio::error::operation_aborted) return;

   for (auto account : accounts)
   {
      switch (account->status)
      {
      case XmppAccountStatusChangedEvent::Status_Connected:
      case XmppAccountStatusChangedEvent::Status_Resumed:
         ++stats.accountConnected;
         break;

      case XmppAccountStatusChangedEvent::Status_Failure:
         ++stats.accountFailure;
         break;
      case XmppAccountStatusChangedEvent::Status_Disconnected:
         ++stats.accountDisconnected;
         break;

      case XmppAccountStatusChangedEvent::Status_Connecting:
      case XmppAccountStatusChangedEvent::Status_Resuming:
         ++stats.accountConnecting;
         break;

      case XmppAccountStatusChangedEvent::Status_Disconnecting:
         ++stats.accountDisconnecting;
         break;

      case XmppAccountStatusChangedEvent::Status_Destroyed:
         ++stats.accountDestroyed;
         break;

      default:
         assert(0);
      }
   }

   auto t = std::chrono::system_clock::to_time_t(std::chrono::system_clock::now());
   auto tt = *localtime(&t);
   char now [80];
   strftime(now, sizeof(now), " %F %T", &tt);

   std::ostringstream oss;
   oss << "[Users On Server]";
   for (auto item : stats.remotes)
   {
      oss << '\n' << makeTableFormat(item.first, item.second);
   }

   safeCout("\n\n\n" << std::setfill('=') << std::right << std::setw(70) << now
      << "\n"
      << "\n" << "[Accounts]"
      << "\n" << makeTableFormat("Active / Total", accounts.size()) << " / " << accountSettings.size()
      << "\n" << makeTableFormat("Connected", stats.accountConnected)
      << slot << makeTableFormat("Disconnected", stats.accountDisconnected)
      << "\n" << makeTableFormat("Connecting", stats.accountConnecting)
      << slot << makeTableFormat("Disconnecting", stats.accountDisconnecting)
      << "\n" << makeTableFormat("Failure", stats.accountFailure)
      << "\n"
      << "\n" << "[Presence]"
      << "\n" << makeTableFormat("Attempt", stats.presenceAttempt)
      << slot << makeTableFormat("Success", stats.presenceSuccess)
      << "\n" << makeTableFormat("Failure", stats.presenceFailure)
      << slot << makeTableFormat("Neglect", stats.presenceNeglect)
      << "\n"
      << "\n" << "[MUC]"
      << "\n" << makeTableFormat("Attempt", stats.mucAttempt)
      << slot << makeTableFormat("Success", stats.mucSuccess)
      << "\n" << makeTableFormat("Failure", stats.mucFailure)
      << slot << makeTableFormat("Neglect", stats.mucNeglect)
      << "\n"
      //<< "\n" << makeTableFormat("Process Count", stats.processCount)
      //<< "\n" << makeTableFormat("Process Time", stats.processTime) << "ms"
      //<< "\n"
      << "\n" << "[Event]"
      << "\n" << makeTableFormat<long long>("Average Delay", stats.eventCount == 0 ? -1 : stats.eventDelay / stats.eventCount) << "ms"
      << slot<8> << makeTableFormat("Success", stats.eventSuccess)
      << "\n" << makeTableFormat("Failure", stats.eventFailure)
      << slot << makeTableFormat("Timeout", stats.eventTimeout)
      << "\n"
      << "\n" << oss.str()
      << "\n" << std::setfill('-') << std::left << std::setw(70)
      << "\n"
   );

   stats.reset();

   statsTimer.expires_from_now(boost::posix_time::milliseconds(statsInterval));
   statsTimer.async_wait(std::bind(statsTimerHandler, std::placeholders::_1));
}

static void accountActivationTimerHandler(const boost::system::error_code& error)
{
   if (error == boost::asio::error::operation_aborted) return;

   if (accounts.size() >= activeAccounts) return;

   const auto& settings = accountSettings[accounts.size()];
   accounts.push_back(new XmppServerLoadTestAccount(*managers[accounts.size() % managers.size()], settings));

   accountActivationTimer.expires_from_now(boost::posix_time::milliseconds(accountActivationInterval));
   accountActivationTimer.async_wait(std::bind(accountActivationTimerHandler, std::placeholders::_1));
}

static void signalHandler(const boost::system::error_code& error, int signal_number)
{
   assert(signal_number == SIGINT);

   if (!enableConsole)
   {
      ios.stop();
      return;
   }

   struct CommandInfo
   {
      std::string parameter;
      std::string description;
      std::function<bool(const std::vector<std::string>&)> handler;
      bool hidden;
   };

   static std::map<std::string, CommandInfo> commandInfos;

   static auto addCommand = [&](const std::string& command, const std::string& parameter, const std::string& description, std::function<bool(const std::vector<std::string>&)> handler, bool hidden = false)
   {
      assert(commandInfos.find(command) == commandInfos.end());

      commandInfos.insert(std::make_pair(command, CommandInfo{ parameter, description, handler, hidden }));
   };

   if (commandInfos.empty())
   {
      addCommand("h|help", "h [command] - <> for mandatory parameters, [] for optional parameters", "Show detailed information of a command", [](const std::vector<std::string>& args) {
         if(args.size() == 1)
         {
            std::cout << "All available commands:" << std::endl;
            for (auto info : commandInfos) if (!info.second.hidden) std::cout << "  " << makeTableFormat(info.first, info.second.description) << std::endl;

            return true;
         }
         else if (args.size() == 2)
         {
            for (auto item : commandInfos)
            {
               std::regex re(item.first);

               if (std::regex_match(args[1], re))
               {
                  std::cout << "Description: " << item.second.description << std::endl;
                  std::cout << "Usage: " << item.first << " " << item.second.parameter << std::endl;

                  return true;
               }
            }

            return false;
         }

         return false;
      });

      addCommand("q|quit", "", "Quit the program", NULL);

      addCommand("c|continue", "", "Continue after exiting the console", NULL);

      addCommand("s|stats", "", "Show current stats", [](const std::vector<std::string>& args) {
         std::lock_guard<std::recursive_mutex> lock(_safeCoutMutex);
         _safeCoutToggle = true;
         statsTimerHandler(boost::asio::error::timed_out);
         _safeCoutToggle = false;

         return true;
      });

      addCommand("pi", "<interval in milliseconds>", "Set presence test interval", [](const std::vector<std::string>& args) {
         if (args.size() != 2) return false;

         try
         {
            auto value1 = boost::lexical_cast<unsigned int>(args[1]);

            ios.post([value1]() {
               presenceTestInterval = std::max<unsigned int>(value1, 1000);;

               for (auto account : accounts)
               {
                  account->presenceTestNext = std::chrono::steady_clock::now() + std::chrono::seconds(std::rand() % (presenceTestInterval / 1000));
               }
            });

            return true;
         }
         catch (const boost::bad_lexical_cast& e)
         {
            return false;
         }
      });

      addCommand("ci", "<interval in milliseconds>", "Set chat test interval", [](const std::vector<std::string>& args) {
         if (args.size() != 2) return false;

         try
         {
            auto value1 = boost::lexical_cast<unsigned int>(args[1]);

            ios.post([value1]() {
               chatTestInterval = std::max<unsigned int>(value1, 1000);

               for (auto account : accounts)
               {
                  account->chatTestNext = std::chrono::steady_clock::now() + std::chrono::seconds(std::rand() % (chatTestInterval / 1000));
               }
            });

            return true;
         }
         catch (const boost::bad_lexical_cast& e)
         {
            return false;
         }
      }, true);

      addCommand("mi", "<interval in milliseconds>", "Set muc test interval", [](const std::vector<std::string>& args) {
         if (args.size() != 2) return false;

         try
         {
            auto value1 = boost::lexical_cast<unsigned int>(args[1]);

            ios.post([value1]() {
               mucTestInterval = std::max<unsigned int>(value1, 1000);

               for (auto account : accounts)
               {
                  account->mucTestNext = std::chrono::steady_clock::now() + std::chrono::seconds(std::rand() % (mucTestInterval / 1000));
               }
            });

            return true;
         }
         catch (const boost::bad_lexical_cast& e)
         {
            return false;
         }
      }, true);

      addCommand("ai", "<interval in milliseconds>", "Set account activation interval", [](const std::vector<std::string>& args) {
         if (args.size() != 2) return false;

         try
         {
            auto value1 = boost::lexical_cast<unsigned int>(args[1]);

            ios.post([value1]() {
               accountActivationInterval = value1;

               accountActivationTimerHandler(boost::asio::error::timed_out);
            });

            return true;
         }
         catch (const boost::bad_lexical_cast& e)
         {
            return false;
         }
      });

      addCommand("i|inactive", "<account|*> <0|1> - 0 for active and 1 for inactive", "Set account active / inactive mode", [](const std::vector<std::string>& args) {
         if (args.size() != 3) return false;

         try
         {
            auto value1 = args[1];
            auto value2 = boost::lexical_cast<unsigned int>(args[2]);
            if (value2 != 0 && value2 != 1) return false;

            ios.post([value1, value2]() {
               for (auto account : accounts)
               {
                  if (value1 == "*" || value1 == account->settings.username.c_str())
                  {
                     account->inactive = value2 == 1;
                     account->manager.account->setInactive(account->handle, value2);
                  }
               }
            });

            return true;
         }
         catch (const boost::bad_lexical_cast& e)
         {
            return false;
         }
      }, true);

      addCommand("a|account", "<count>", "Set the total number of active accounts", [](const std::vector<std::string>& args) {
         if (args.size() != 2) return false;

         try
         {
            auto value1 = boost::lexical_cast<unsigned int>(args[1]);

            ios.post([value1]() {
               if (value1 < activeAccounts)
               {
                  activeAccounts = value1;

                  while (accounts.size() > activeAccounts)
                  {
                     delete accounts.back();
                     accounts.pop_back();
                  }
               }
               else if (value1 > activeAccounts)
               {
                  activeAccounts = std::min<unsigned int>(value1, accountSettings.size());

                  accountActivationTimerHandler(boost::asio::error::timed_out);
               }
            });

            return true;
         }
         catch (const boost::bad_lexical_cast& e)
         {
            return false;
         }
      });

      addCommand("n|network", "<account|*> [0|1] - * for all accounts, 0 to eanble network, 1 to disable network, blank to simulate network loss", "Simulate network loss for accounts", [](const std::vector<std::string>& args) {
         if (args.size() == 2)
         {
            auto value1 = args[1];

            ios.post([value1]() {
               for (auto account : accounts)
               {
                  if (value1 == "*" || value1 == account->settings.username.c_str())
                  {
                     XmppAccount::XmppAccountManagerInternal::getInternalInterface(account->manager.phone)->simulateNetworkLoss(account->handle);
                  }
               }
            });

            return true;
         }
         else if (args.size() == 3)
         {
            try
            {
               auto value1 = args[1];
               auto value2 = boost::lexical_cast<bool>(args[2]);

               ios.post([value1, value2]() {
                  for (auto account : accounts)
                  {
                     if (value1 == "*" || value1 == account->settings.username.c_str())
                     {
                        XmppAccount::XmppAccountManagerInternal::getInternalInterface(account->manager.phone)->simulateNetworkRestriction(account->handle, value2);
                     }
                  }
               });

               return true;
            }
            catch (const boost::bad_lexical_cast& e)
            {
               return false;
            }
         }

         return false;
      });

      addCommand("p|presence", "<account> <presence>", "Publish a presence for an account however this won't be counted in stats", [](const std::vector<std::string>& args) {
         if (args.size() != 3) return false;

         try
         {
            auto value1 = args[1];
            auto value2 = static_cast<XmppRoster::XmppCannedStatus>(boost::lexical_cast<unsigned int>(args[2]));

            if (std::find(presenceSet.begin(), presenceSet.end(), value2) == presenceSet.end())
            {
               std::cout << "Presence not in presence set:";
               for (auto presence : presenceSet) std::cout << " " << presence;
               std::cout << std::endl;
               return true;
            }

            ios.post([value1, value2]() {
               for (auto account : accounts)
               {
                  if (account->settings.username == value1.c_str())
                  {
                     account->manager.account->publishCannedPresence(account->handle, value2);
                     break;
                  }
               }
            });

            return true;
         }
         catch (const boost::bad_lexical_cast& e)
         {
            return false;
         }
      }, true);

      addCommand("muc", "<account>", "Run a MUC test for an account", [](const std::vector<std::string>& args) {
         if (args.size() != 2) return false;

         try
         {
            auto value1 = args[1];

            ios.post([value1]() {
               for (auto account : accounts)
               {
                  if (account->settings.username == value1.c_str())
                  {
                     account->mucTest();
                     break;
                  }
               }
            });

            return true;
         }
         catch (const boost::bad_lexical_cast& e)
         {
            return false;
         }
      }, true);

      addCommand("lp", "", "List the presence mapping", [](const std::vector<std::string>& args) {
         if (args.size() != 1) return false;

         static const std::map<XmppRoster::XmppCannedStatus, std::string> presenceStringMap = {
            { XmppRoster::XmppCannedStatus_Available,             "Available" },
            { XmppRoster::XmppCannedStatus_Chat,                  "Chat" },
            { XmppRoster::XmppCannedStatus_Away,                  "Away" },
            { XmppRoster::XmppCannedStatus_AppearAway,            "AppearAway" },
            { XmppRoster::XmppCannedStatus_Busy,                  "Busy" },
            { XmppRoster::XmppCannedStatus_HavingLunch,           "HavingLunch" },
            { XmppRoster::XmppCannedStatus_OnVacation,            "OnVacation" },
            { XmppRoster::XmppCannedStatus_ScheduledHoliday,      "ScheduledHoliday" },
            { XmppRoster::XmppCannedStatus_InactiveOther,         "InactiveOther" },
            { XmppRoster::XmppCannedStatus_OnThePhone,            "OnThePhone" },
            { XmppRoster::XmppCannedStatus_DoNotDisturb,          "DoNotDisturb" },
            { XmppRoster::XmppCannedStatus_NotAvailableForCalls,  "NotAvailableForCalls" },
            { XmppRoster::XmppCannedStatus_AppearOffline,         "AppearOffline" },
            { XmppRoster::XmppCannedStatus_Invisible,             "Invisible" },
            { XmppRoster::XmppCannedStatus_Offline,               "Offline" },
            { XmppRoster::XmppCannedStatus_Other,                 "Other" },
            { XmppRoster::XmppCannedStatus_Invalid,               "Invalid" }
         };

         for (auto presence : presenceSet)
         {
            auto it = presenceStringMap.find(presence);
            assert(it != presenceStringMap.end());
            std::cout << "\n  " << makeTableFormat(it->second, presence);
         }

         std::cout << std::endl;

         return true;
      });

      addCommand("lg", "", "List the group mapping", [](const std::vector<std::string>& args) {
         if (args.size() != 1) return false;

         for (auto item : predefinedGroups)
         {
            std::cout << "\n  " << makeTableFormat(item.first, item.second);
         }

         std::cout << std::endl;

         return true;
      });

      addCommand("z|zero", "", "Zero out all stats and reset all events", [](const std::vector<std::string>& args) {
         if (args.size() != 1) return false;

         ios.post([]() {
            stats.fullReset();

            for (auto account : accounts) account->timedEventHandlers.clear();
         });

         return true;
      });
   }

   signals.async_wait(signalHandler);

   static std::thread* console = NULL;

   if (console != NULL) return;

   std::lock_guard<std::recursive_mutex> lock(_safeCoutMutex);
   _safeCoutToggle = false;

   statsTimer.cancel();

   console = new std::thread([]() {
      static auto tokenize = [](const std::string& s)
      {
         std::istringstream iss(s);

         std::vector<std::string> tokens;
         std::string token;
         while (iss >> token) tokens.push_back(token);

         return tokens;
      };

#if 0 // consume previous cin buffer
      std::cin.ignore(std::numeric_limits<std::streamsize>::max());
      std::cin.clear();
#endif

      std::cout << std::endl << "Type \"help <command>\" (case sensitive) for more information" << std::endl;
      std::cout << "Console> " << std::flush;

      // add "Console> " in front of any user input command
      for (std::string cmd;; std::cout << "Console> " << std::flush)
      {
         std::getline(std::cin, cmd);

         // user may type Ctrl+C or Ctrl+D to make cin invalid
         if (std::cin.fail() || std::cin.eof())
         {
            std::cin.clear();
            std::cout << std::endl;
            continue;
         }

         const auto args = tokenize(cmd);

         if (args.empty()) continue;

         if (args.size() == 1 && (args[0] == "c" || args[0] == "continue"))
         {
            std::cout << std::endl;
            break;
         }

         if (args.size() == 1 && (args[0] == "q" || args[0] == "quit"))
         {
            std::cout << std::endl;
            ios.stop();
            break;
         }

         bool handled = false;

         for (auto item : commandInfos)
         {
            std::regex re(item.first);

            if (std::regex_match(args[0], re))
            {
               if (!item.second.handler(args)) std::cout << "Invalid parameter, usage: " << item.first << " " << item.second.parameter << std::endl;
               handled = true;
               break;
            }
         }

         if (!handled) std::cout << "Unrecognized command: \"" << cmd << '\"' << std::endl;
      }

      std::lock_guard<std::recursive_mutex> lock(_safeCoutMutex);
      _safeCoutToggle = true;

      ios.post([]() {
         console->join();
         delete console;
         console = NULL;

         statsTimerHandler(boost::asio::error::timed_out);
      });
   });
}

TEST_F(XmppServerLoadTestTool, test)
{
   // capture Ctrl+C
   signals.async_wait(signalHandler);

   setup();

   statsTimerHandler(boost::asio::error::timed_out);
   accountActivationTimerHandler(boost::asio::error::timed_out);

   ios.run();

   shutdown();
}
