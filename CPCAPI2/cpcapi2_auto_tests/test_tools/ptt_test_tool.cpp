#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#if (CPCAPI2_BRAND_PTT_MODULE == 1)

#include "../cpcapi2_test_fixture.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>

#include "../test_framework/cpcapi2_test_framework.h"
#include "ptt/PushToTalkManager.h"
#include "ptt/PushToTalkManagerInternal.h"

#include <future>

using namespace CPCAPI2;
using namespace CPCAPI2::PushToTalk;
using namespace CPCAPI2::test;

class PttTestTool : public CpcapiAutoTest
{
public:
   PttTestTool() {}
   virtual ~PttTestTool() {}
};

#define STATUS_LOG_FMT(x) "[[OK[" << x << "]]]"
#define FAIL_LOG_FMT(x) "[[NOK[" << x << "]]]"

void _expectPttServiceStartupCompleteWan(int line, TestAccount& account, PushToTalkServiceHandle handle)
{
   PushToTalkServiceHandle h;
   PttServiceStatusChangedEvent evt;
   ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandler::onPttServiceStatusChanged", 30000, HandleEqualsPred<PushToTalkServiceHandle>(handle), h, evt)) << FAIL_LOG_FMT("expectPttServiceStartupCompleteWan failure, service: " << handle);
   ASSERT_EQ(PttServiceStatusChangedEvent::Status_Connecting, evt.status) << FAIL_LOG_FMT("expectPttServiceStartupCompleteWan failure, service: " << handle);
   ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandler::onPttServiceStatusChanged", 30000, HandleEqualsPred<PushToTalkServiceHandle>(handle), h, evt)) << FAIL_LOG_FMT("expectPttServiceStartupCompleteWan failure, service: " << handle);
   ASSERT_EQ(PttServiceStatusChangedEvent::Status_Authenticating, evt.status) << FAIL_LOG_FMT("expectPttServiceStartupCompleteWan failure, service: " << handle);
   ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandler::onPttServiceStatusChanged", 30000, HandleEqualsPred<PushToTalkServiceHandle>(handle), h, evt)) << FAIL_LOG_FMT("expectPttServiceStartupCompleteWan failure, service: " << handle);
   ASSERT_EQ(PttServiceStatusChangedEvent::Status_Connected, evt.status) << FAIL_LOG_FMT("expectPttServiceStartupCompleteWan failure, service: " << handle);
   ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandler::onPttServiceStatusChanged", 30000, HandleEqualsPred<PushToTalkServiceHandle>(handle), h, evt)) << FAIL_LOG_FMT("expectPttServiceStartupCompleteWan failure, service: " << handle);
   ASSERT_EQ(PttServiceStatusChangedEvent::Status_Ready, evt.status) << FAIL_LOG_FMT("expectPttServiceStartupCompleteWan failure, service: " << handle);
}

#define expectPttServiceStartupCompleteWan(...) _expectPttServiceStartupCompleteWan(__LINE__, __VA_ARGS__)

void _expectPttServiceShutdownComplete(int line, TestAccount& account, PushToTalkServiceHandle handle)
{
   PushToTalkServiceHandle h;
   PttServiceStatusChangedEvent evt;
   ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandler::onPttServiceStatusChanged", 40000, HandleEqualsPred<PushToTalkServiceHandle>(handle), h, evt)) << FAIL_LOG_FMT("expectPttServiceShutdownComplete failure, service: " << handle);
   ASSERT_EQ(PttServiceStatusChangedEvent::Status_Disconnecting, evt.status) << FAIL_LOG_FMT("expectPttServiceShutdownComplete failure, service: " << handle);
   ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandler::onPttServiceStatusChanged", 40000, HandleEqualsPred<PushToTalkServiceHandle>(handle), h, evt)) << FAIL_LOG_FMT("expectPttServiceShutdownComplete failure, service: " << handle);
   ASSERT_EQ(PttServiceStatusChangedEvent::Status_Disconnected, evt.status) << FAIL_LOG_FMT("expectPttServiceShutdownComplete failure, service: " << handle);
   ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandler::onPttServiceStatusChanged", 40000, HandleEqualsPred<PushToTalkServiceHandle>(handle), h, evt)) << FAIL_LOG_FMT("expectPttServiceShutdownComplete failure, service: " << handle);
   ASSERT_EQ(PttServiceStatusChangedEvent::Status_Disabled, evt.status) << FAIL_LOG_FMT("expectPttServiceShutdownComplete failure, service: " << handle);
}

#define expectPttServiceShutdownComplete(...) _expectPttServiceShutdownComplete(__LINE__, __VA_ARGS__)

void _expectPttIncomingCallEx(int line, TestAccount& account, PushToTalkSessionHandle& handle, const cpc::string& userName, const cpc::string& displayName)
{
   PushToTalkSessionHandle h;
   PttIncomingCallEvent evt;
   ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandler::onPttIncomingCall", 30000, AlwaysTruePred(), h, evt)) << FAIL_LOG_FMT("expectPttIncomingCallEx failure");
   handle = h;
   ASSERT_EQ(userName, evt.callerIdentity.userName) << FAIL_LOG_FMT("expectPttIncomingCallEx failure, session: " << handle);
   ASSERT_EQ(displayName, evt.callerIdentity.displayName) << FAIL_LOG_FMT("expectPttIncomingCallEx failure, session: " << handle);
}

#define expectPttIncomingCallEx(...) _expectPttIncomingCallEx(__LINE__, __VA_ARGS__)

void _expectPttSessionStateChanged(int line, TestAccount& account, PushToTalkSessionHandle handle, PttSessionStateType state)
{
   PushToTalkSessionHandle h;
   PttSessionStateChangedEvent evt;
   ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandler::onPttSessionStateChanged", 30000, HandleEqualsPred<PushToTalkSessionHandle>(handle), h, evt)) << FAIL_LOG_FMT("expectPttSessionStateChanged failure, session: " << handle);
   ASSERT_EQ(state, evt.currentState) << FAIL_LOG_FMT("expectPttSessionStateChanged failure, session: " << handle);
}

#define expectPttSessionStateChanged(...) _expectPttSessionStateChanged(__LINE__, __VA_ARGS__)

void _expectPttParticipantListUpdate(int line, TestAccount& account, PushToTalkSessionHandle& handle, int listCount)
{
   PushToTalkSessionHandle h;
   PttParticipantListUpdateEvent evt;
   ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandlerInternal::onPttParticipantListUpdate", 30000, HandleEqualsPred<PushToTalkSessionHandle>(handle), h, evt)) << FAIL_LOG_FMT("expectPttParticipantListUpdate failure, session: " << handle);
   ASSERT_EQ(listCount, evt.participants.size()) << FAIL_LOG_FMT("expectPttParticipantListUpdate failure, session: " << handle);
}

#define expectPttParticipantListUpdate(...) _expectPttParticipantListUpdate(__LINE__, __VA_ARGS__)

void _expectPttMediaStatistics(int line, TestAccount& account, PushToTalkSessionHandle handle)
{
   PushToTalkSessionHandle h;
   PttMediaStatisticsEvent evt;
   ASSERT_TRUE(account.pttEvents->expectEvent("PushToTalkHandlerInternal::onPttMediaStatistics", 30000, HandleEqualsPred<PushToTalkServiceHandle>(handle), h, evt)) << FAIL_LOG_FMT("expectPttMediaStatistics failure, session: " << handle);
   ASSERT_EQ(evt.mediaStreamStats.size(), 1) << FAIL_LOG_FMT("expectPttMediaStatistics failure, session: " << handle);
   ASSERT_GT(evt.mediaStreamStats[0].rtpPacketCount, 0) << FAIL_LOG_FMT("expectPttMediaStatistics failure, session: " << handle);
}

#define expectPttMediaStatistics(...) _expectPttMediaStatistics(__LINE__, __VA_ARGS__)

static const cpc::string testChannel = "channel01";

struct PttTestAccount : TestAccount
{
   PushToTalkManager* pttManager;
   PushToTalkServiceHandle pttService;
   PushToTalkSessionHandle pttSession;
   PushToTalkServiceSettings pttSettings;
   PushToTalkSettingsInternal pttSettingsInternal;

   PttTestAccount(const std::string& name, const PushToTalkServiceSettings& settings, const PushToTalkSettingsInternal& settingsInternal) :
      TestAccount(name, Account_NoInit),
      pttSettings(settings),
      pttSettingsInternal(settingsInternal),
      pttSession(0)
   {
      config.useFileAudioDevice = true;
      init();

      pttManager = PushToTalkManager::getInterface(phone);

      pttService = pttManager->createPttService();
      pttManager->setHandler(pttService, (PushToTalkHandlerInternal*)0xDEADBEEF);
      pttManager->configureService(pttService, const_cast<PushToTalkServiceSettings&>(pttSettings));

      PushToTalkManagerInternal* pttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(pttManager);
      pttManagerInternal->setPttInternalSettings(pttService, pttSettingsInternal);
      safeCout(STATUS_LOG_FMT("ptt_test_tool using session-expiry: " << pttSettingsInternal.outgoingSessionExpiryMsecs << " spurt-expiry: " << pttSettingsInternal.outgoingSessionWaitForTalkSpurtMsecs << " msecs"));

      cpc::vector<cpc::string> channels;
      channels.push_back(testChannel);
      pttManager->setChannelSubscriptions(pttService, channels);

      pttManager->startService(pttService);
      expectPttServiceStartupCompleteWan(*this, pttService);
   }

   virtual ~PttTestAccount()
   {
      pttManager->shutdownService(pttService);
      expectPttServiceShutdownComplete(*this, pttService);
   }
};

class MyMediaStatusHandler : public PushToTalkHandlerInternal
{
public:
   MyMediaStatusHandler(std::promise<bool>& offerSentPromise) : mOfferSent(false), mOfferSentPromise(offerSentPromise) {}
   virtual~ MyMediaStatusHandler() {}

   virtual int onPttConferenceMediaStatusChangedEvent(PushToTalkSessionHandle session, const PttConferenceMediaStatusChangedEvent& args)
   {
      if (args.mediaStatus == PttConferenceMediaStatusChangedEvent::MediaStatus_OfferSent)
      {
         mOfferSentPromise.set_value(true);
         mOfferSent = true;
      }
      return kSuccess;
   }
   std::atomic_bool mOfferSent;
   std::promise<bool>& mOfferSentPromise;
};

TEST_F(PttTestTool, test)
{
   std::vector<PttTestAccount*> accounts;
   cpc::vector<PushToTalkServiceSettings> settings;
   cpc::vector<PushToTalkSettingsInternal> settingsInternal;

   std::string configFileName = "pttTestToolAccounts.json";
   ASSERT_TRUE(TestEnvironmentConfig::getResourceFile(configFileName, configFileName));
   resip::Data configFile = configFileName.c_str();

   std::ifstream in(configFile.c_str());
   ASSERT_TRUE(in.is_open()) << FAIL_LOG_FMT("failed when opening " << configFile.c_str());

   std::ostringstream iss;
   iss << in.rdbuf() << std::flush;

   cpc::string doc = iss.str().c_str();

   ASSERT_EQ(kSuccess, PushToTalkManager::decodeProvisioningResponse(doc, settings)) << FAIL_LOG_FMT("failed when getting accounts settings from the json file");
   ASSERT_TRUE(!settings.empty()) << FAIL_LOG_FMT("the tool should have some account settings");
   ASSERT_EQ(kSuccess, PushToTalkManagerInternal::decodeProvisioningResponseInternal(doc, settingsInternal)) << FAIL_LOG_FMT("failed when getting accounts settings internal from the json file");
   ASSERT_EQ(settings.size(), settingsInternal.size());

   for (int i = 0; i < settings.size(); i++)
   {
      auto account = new PttTestAccount(settings[i].username.c_str(), settings[i], settingsInternal[i]);
      if (::testing::Test::HasFatalFailure()) FAIL() << FAIL_LOG_FMT("failed when starting account");

      accounts.push_back(account);
   }

   safeCout(STATUS_LOG_FMT("total " << accounts.size() << " ptt accounts registered successfully"));

   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   std::vector<std::future<void>> futures;

   for (auto _account : accounts)
   {
      bool isSender = futures.empty();

      futures.emplace_back(std::async([isSender, _account, &accounts]()
      {
         PushToTalkServiceHandle service = _account->pttService;
         PushToTalkSessionHandle session = 0;
         std::promise<bool> offerSentPromise;
         std::future<bool> offerSentFuture = offerSentPromise.get_future();
         MyMediaStatusHandler mediaStatusHandler(offerSentPromise);
         PushToTalkManagerInternal* pttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(_account->pttManager);

         if (isSender)
         {
            pttManagerInternal->addPttObserver(&mediaStatusHandler);

            session = _account->pttManager->createPttSession(service);
            _account->pttSession = session;
            _account->pttManager->setChannel(session, testChannel);
            _account->pttManager->start(session);
         }
         else
         {
            ASSERT_NO_FATAL_FAILURE(expectPttIncomingCallEx(*_account, _account->pttSession, accounts[0]->pttSettings.localIdentities[0].userName, accounts[0]->pttSettings.localIdentities[0].displayName));
            session = _account->pttSession;
            _account->pttManager->accept(session);
         }

         ASSERT_NO_FATAL_FAILURE(expectPttSessionStateChanged(*_account, session, PttSessionState_Initiated));
         ASSERT_NO_FATAL_FAILURE(expectPttSessionStateChanged(*_account, session, PttSessionState_Active));

         for (int c = 0; c < 1; ++c)
         {
            if (isSender)
            {
               // not trivial to check participant list at non-sender
               for (size_t i = 0; i < accounts.size(); ++i) ASSERT_NO_FATAL_FAILURE(expectPttParticipantListUpdate(*_account, session, i + 1));

               // Ensure that the ice-handshake has completed before triggering the talk-spurt. Issue only exists when there are
               // binding failures when testing the candidate list.
               ASSERT_EQ(offerSentFuture.wait_for(std::chrono::milliseconds(20000)), std::future_status::ready) << FAIL_LOG_FMT("ptt_test_tool timeout waiting for ptt-sender to send an offer to the ptt server");
               ASSERT_TRUE(offerSentFuture.get()) << FAIL_LOG_FMT("ptt_test_tool ptt-sender did not send an offer to the ptt server");

               _account->pttManager->startTalkSpurt(session);
            }

            ASSERT_NO_FATAL_FAILURE(expectPttSessionStateChanged(*_account, session, PttSessionState_Talking));

            int mediaQueryDelay = 3;
#if (defined(__linux__) && !defined(ANDROID)) || defined(__APPLE__)
            const char* sMediaQueryDelay = getenv("CPCAPI2_PTT_MEDIA_QUERY_DELAY_SECS");
            if (sMediaQueryDelay)
            {
               std::stringstream ss;
               ss << sMediaQueryDelay;
               ss >> mediaQueryDelay;
            }
#endif
            safeCout(STATUS_LOG_FMT("ptt_test_tool using media-query delay of " << mediaQueryDelay << " seconds"));
            std::this_thread::sleep_for(std::chrono::seconds(mediaQueryDelay)); // required for queryMediaStatistics()
            dynamic_cast<PushToTalkManagerInternal*>(_account->pttManager)->queryMediaStatistics(session);
            ASSERT_NO_FATAL_FAILURE(expectPttMediaStatistics(*_account, session));

            if (isSender)
            {
               std::this_thread::sleep_for(std::chrono::seconds(5));
               _account->pttManager->endTalkSpurt(session);
               ASSERT_NO_FATAL_FAILURE(expectPttSessionStateChanged(*_account, session, PttSessionState_Active));
            }
         }

         if (isSender)
         {
            _account->pttManager->end(session);
            pttManagerInternal->removePttObserver(&mediaStatusHandler);
         }

         ASSERT_NO_FATAL_FAILURE(expectPttSessionStateChanged(*_account, session, PttSessionState_Ending));
         ASSERT_NO_FATAL_FAILURE(expectPttSessionStateChanged(*_account, session, PttSessionState_Idle));

         delete _account;
         if (::testing::Test::HasFatalFailure()) FAIL() << FAIL_LOG_FMT("failed when stopping account with service: " << service << " and session: " << session);
      }));
   }

   for (auto& _future : futures)
   {
      ASSERT_TRUE(_future.wait_for(std::chrono::seconds(60)) == std::future_status::ready) << FAIL_LOG_FMT("ptt_test_tool didn't complete in time");
   }

   safeCout(STATUS_LOG_FMT("ptt_test_tool completed successfully"));
}

#endif
