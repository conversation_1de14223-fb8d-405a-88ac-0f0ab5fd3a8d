#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#define HWND void*
#endif

#if (CPCAPI2_BRAND_CONFERENCE_CONNECTOR_MODULE == 1)

#include "cpcapi2_test_fixture.h"
#include "test_call_events.h"
#include "test_events.h"
#include "test_account_events.h"
#include "test_framework/xmpp_test_helper.h"
#include "test_framework/screenshare_utils.h"

#include <confconnector/ConferenceConnector.h>
#include <confconnector/ConferenceConnectorHandler.h>
#include <confconnector/ConferenceConnectorInternal.h>
#include <confconnector/ConferenceConnectorTypes.h>
#include <orchestration_server/OrchestrationServer.h>
#include <cloudserviceconfig/CloudServiceConfig.h>
#include <confbridge/ConferenceBridgeJsonApi.h>
#include <confbridge/ConferenceBridgeManager.h>
#include <webrtc/modules/desktop_capture/desktop_capturer.h>

#include "../../impl/util/CurlPPHelper.h"
#include "../../impl/util/CurlPPProgress.h"
#include "../../impl/util/CurlPPSSL.h"
#include "../../impl/auth_server/AuthServerJwtUtils.h"
#include "../../impl/auth_server/AuthServerDbAccess.h"
#include "../../impl/jsonapi/JsonApiServerInterface.h"

using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::ConferenceConnector;
using namespace CPCAPI2::CloudServiceConfig;
using namespace CPCAPI2::OrchestrationServer;
using namespace CPCAPI2::JsonApi;
using namespace CPCAPI2::SipConversation;
using namespace CPCAPI2::SipAccount;

class ScreenshareLiveServerTests : public CpcapiAutoTest
{
public:
   ScreenshareLiveServerTests() {}
   virtual ~ScreenshareLiveServerTests() {}
};

class ConferenceConnectorLiveServerParmTests : public CpcapiAutoTestWithParam<std::pair<std::string,std::string> >
{
public:
   ConferenceConnectorLiveServerParmTests() {}
   virtual ~ConferenceConnectorLiveServerParmTests() {}
};

// where is screen sharing supported...
#if defined(_WIN32) || (defined( __APPLE__ ) && TARGET_OS_IPHONE == 0) || (defined(__linux__) && !defined(ANDROID))

// used in ScreenShareImpl.cpp
extern void (*gCustomCapturerFactory)(std::unique_ptr<webrtc::DesktopCapturer>& outCapturer);

TEST_F(ScreenshareLiveServerTests, ScreenShare_externalServer)
{
   cpc::string SCREENSHARE_SERVER;
   cpc::string SCREENSHARE_FRAMESIZE = "1024x768";

   if (const char* str = std::getenv("SCREENSHARE_SERVER"))
   {
      SCREENSHARE_SERVER = str;
      safeCout("Using environment SCREENSHARE_SERVER: " << SCREENSHARE_SERVER);
   }
   else
   {
      GTEST_SKIP() << "Skipping test since SCREENSHARE_SERVER env variable not defined. \
                       Define this env variable to the address of a screen share server to run this test.";
   }

   if (const char* str = std::getenv("SCREENSHARE_FRAMESIZE"))
   {
      SCREENSHARE_FRAMESIZE = str;
      safeCout("Using environment SCREENSHARE_FRAMESIZE: " << SCREENSHARE_FRAMESIZE);
   }
   else
      safeCout("Using hard-coded SCREENSHARE_FRAMESIZE: " << SCREENSHARE_FRAMESIZE);
   
   static int captureWidth = 1024;
   static int captureHeight = 768;
   size_t i = SCREENSHARE_FRAMESIZE.find("x");
   if (i == std::string::npos)
   {
      safeCout("Unrecognized SCREENSHARE_FRAMESIZE: " << SCREENSHARE_FRAMESIZE);
      ASSERT_TRUE(false);
   }
   else
   {
      captureWidth = std::atoi(SCREENSHARE_FRAMESIZE.substr(0, i));
      captureHeight = std::atoi(SCREENSHARE_FRAMESIZE.substr(i+1));
   }

   gCustomCapturerFactory = [](std::unique_ptr<webrtc::DesktopCapturer>& outCapturer)
   {
safeCout("Capturing at " << captureWidth << "x" << captureHeight);
      outCapturer = std::unique_ptr<CPCAPI2::test::FakeScreenCapturer>(new CPCAPI2::test::FakeScreenCapturer(captureWidth, captureHeight));
   };
   
   LoginResultEvent loginResult; loginResult.success = true;
   cpc::vector<cpc::string> permissions; permissions.push_back("*");

   // Alice is a client SDK (host)
   TestAccount alice("alice", Account_Init);

   // Bob is a client SDK (participant)
   TestAccount bob("bob", Account_Init);

   // enable H.264
   alice.video->queryCodecList();
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);
   bob.video->queryCodecList();
   bob.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);

   alice.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   bob.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   alice.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   bob.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   HWND hwndAliceRemote = NULL;
   HWND hwndBobRemote = NULL;
   HWND hwndBobRemoteLarge = NULL;
#if _WIN32
   ASSERT_EQ(0, ViECreateWindow(hwndAliceRemote, 0, 0, 352, 288, "Alice (incoming)"));
   //alice.video->setIncomingVideoRenderTarget(hwndAliceRemote);
   alice.video->startCapture();

   ASSERT_EQ(0, ViECreateWindow(hwndBobRemote, 356, 0, 352, 288, "Bob (incoming)"));
   //bob.video->setIncomingVideoRenderTarget(hwndBobRemote);
   bob.video->startCapture();

   ASSERT_EQ(0, ViECreateWindow(hwndBobRemoteLarge, 0, 292, 704, 576, "Bob (incoming large)"));

#else // _WIN32
   // CPCAPI2::Media::VideoExt::getInterface(alice.media)->startScreenshare();
#endif

   ConferenceConnectorHandle aliceConfConn = alice.createConferenceConnector();
   ConferenceConnectorSettings aliceCloudSettings;
   aliceCloudSettings.authServerUrl = "https://cloudsdk5.bria-x.net:18082";
   aliceCloudSettings.authServerApiKey = "-----BEGIN PUBLIC KEY-----\n"
      "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEZzshRNe4gMUuqNBwrE6leU/N5vW8\n"
      "+HW/pZw5XfsxvzEtUlGwp+A9sNedJNUtjYD/2gFFY/nFuM4Gmnu6zeteTQ==\n"
      "-----END PUBLIC KEY-----";
   aliceCloudSettings.orchestrationServerUrl = "https://" + SCREENSHARE_SERVER + "/jsonApi";
   aliceCloudSettings.regionCode = "LOCAL";
   aliceCloudSettings.username = "user1";
   aliceCloudSettings.password = "1234";
   aliceCloudSettings.ignoreCertVerification = true;
   alice.conferenceConnector->setConnectionSettings(aliceConfConn, aliceCloudSettings);
   alice.conferenceConnector->connectToConferenceService(aliceConfConn);

   std::promise<std::string> joinUrlPr;
   std::future<std::string> joinUrlFu = joinUrlPr.get_future();
   auto aliceEvent = std::async(std::launch::async, [&]() {
      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
      }

      alice.conferenceConnector->queryConferenceList(aliceConfConn);

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 0);
      }

      CloudConferenceSettings confSettings;
      confSettings.conferenceDescription = "mcu test";
      confSettings.conferenceType = CloudConferenceType_Screenshare;
//      confSettings.conferenceType = CloudConferenceType_AudioVideo_MCU;
      confSettings.conferenceId = "mcu";
      alice.conferenceConnector->createNewConference(aliceConfConn, confSettings);
      CloudConferenceHandle aliceScreenShare = 0;

      {
         ConferenceConnectorHandle conn;
         ConferenceCreatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceCreated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_NE(args.conference, 0);
         ASSERT_NE(args.conference, -1);
         aliceScreenShare = args.conference;
      }

      alice.conferenceConnector->queryConferenceList(aliceConfConn);

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 1);
         ASSERT_FALSE(args.conferenceList[0].joinUrl.empty());
         joinUrlPr.set_value(args.conferenceList[0].joinUrl.c_str());
         ASSERT_GT(args.conferenceList[0].conference, 0); // handle used to create a new session
      }

      CloudConferenceSessionHandle aliceSession = alice.conferenceConnector->createConferenceSession(aliceScreenShare);

      CloudConferenceSessionSettings aliceSessionSettings;
      aliceSessionSettings.role = CloudConferenceRole_Host;
      alice.conferenceConnector->setSessionSettings(aliceSession, aliceSessionSettings);

      CloudConferenceSessionMediaSettings aliceSessionMedia;
      aliceSessionMedia.audioDirection = ConferenceConnector::MediaDirection_SendRecv;
      aliceSessionMedia.videoDirection = ConferenceConnector::MediaDirection_SendRecv;
      aliceSessionMedia.remoteVideoRenderSurface = hwndAliceRemote;
      aliceSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
      alice.conferenceConnector->setSessionMediaSettings(aliceSession, aliceSessionMedia);

      alice.conferenceConnector->startSession(aliceSession);

      try
      {
         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connecting);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(30000));

         safeCout("ALICE ENDS SESSION");
         alice.conferenceConnector->endSession(aliceSession);

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
         }
         //{
         //   // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
         //   ConferenceConnectorHandle conn;
         //   ConferenceSessionStatusChangedEvent args;
         //   EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         //   EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
         //}

         {
            ConferenceConnectorHandle conn;
            ConferenceEndedEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceEnded", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(args.conference, aliceScreenShare);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         alice.conferenceConnector->destroyConferenceConnector(aliceConfConn);



      }
      catch (...)
      {
      }

   });

   std::shared_ptr<std::promise<int> > bobPr(new std::promise<int>());
   std::future<int> bobFu = bobPr->get_future();
   auto bobEvent = std::async(std::launch::async, [&, bobPr]() {
      ASSERT_EQ(joinUrlFu.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
      std::string joinUrlFromAlice = joinUrlFu.get();

      ConferenceConnectorHandle bobConfConn = bob.createConferenceConnector();
      ConferenceConnectorSettings bobCloudSettings;
      bobCloudSettings.authServerUrl = "https://cloudsdk5.bria-x.net:18082";
      bobCloudSettings.orchestrationServerUrl = ""; // not needed, we use the joinUrl instead
      bobCloudSettings.joinUrl = joinUrlFromAlice.c_str();
      bobCloudSettings.regionCode = "LOCAL";
      bobCloudSettings.username = "user2";
      bobCloudSettings.password = "1234";
      bobCloudSettings.authServerApiKey = "-----BEGIN PUBLIC KEY-----\n"
         "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEZzshRNe4gMUuqNBwrE6leU/N5vW8\n"
         "+HW/pZw5XfsxvzEtUlGwp+A9sNedJNUtjYD/2gFFY/nFuM4Gmnu6zeteTQ==\n"
         "-----END PUBLIC KEY-----";
      bobCloudSettings.ignoreCertVerification = true;
      bob.conferenceConnector->setConnectionSettings(bobConfConn, bobCloudSettings);
      bob.conferenceConnector->connectToConferenceService(bobConfConn);

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
      }

      bob.conferenceConnector->queryConferenceList(bobConfConn);
      CloudConferenceHandle bobScreenShare;

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 1);
         bobScreenShare = args.conferenceList[0].conference;
      }

      CloudConferenceSessionHandle bobSession = bob.conferenceConnector->createConferenceSession(bobScreenShare);

      CloudConferenceSessionSettings bobSessionSettings;
      bobSessionSettings.role = CloudConferenceRole_Participant;
      bob.conferenceConnector->setSessionSettings(bobSession, bobSessionSettings);

      CloudConferenceSessionMediaSettings bobSessionMedia;
      bobSessionMedia.audioDirection = ConferenceConnector::MediaDirection_SendRecv;
      bobSessionMedia.videoDirection = ConferenceConnector::MediaDirection_SendRecv;
      bobSessionMedia.remoteVideoRenderSurface = hwndBobRemote;
      bobSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
      bob.conferenceConnector->setSessionMediaSettings(bobSession, bobSessionMedia);

      bob.conferenceConnector->startSession(bobSession);

      try
      {
         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connecting);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
            EXPECT_EQ(args.screenshare.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_RecvOnly);
            EXPECT_EQ(args.audio.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_SendRecv);
            EXPECT_EQ(args.video.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
            //EXPECT_NE(args.screenshare.mediaStreamId, -1);
            //bob.video->setIncomingVideoRenderTarget(args.screenshare.mediaStreamId, hwndBobRemote);
         }
         
         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         bobSessionMedia.audioDirection = ConferenceConnector::MediaDirection_SendRecv;
         bobSessionMedia.videoDirection = ConferenceConnector::MediaDirection_SendRecv;
         bobSessionMedia.remoteVideoRenderSurface = hwndBobRemoteLarge;
         bobSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
         bob.conferenceConnector->setSessionMediaSettings(bobSession, bobSessionMedia);
         bob.conferenceConnector->updateSessionMedia(bobSession);

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
            EXPECT_EQ(args.screenshare.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_RecvOnly);
            EXPECT_EQ(args.audio.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_SendRecv);
            EXPECT_EQ(args.video.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
            //bob.video->setIncomingVideoRenderTarget(args.screenshare.mediaStreamId, hwndBobRemote);
         }

         // need to wait long enough that some frames come in
         std::this_thread::sleep_for(std::chrono::milliseconds(15000));

         ConferenceConnectorInternal* confConnInt = dynamic_cast<ConferenceConnectorInternal*>(bob.conferenceConnector);

         confConnInt->queryMediaStatistics(bobConfConn);

         {
            ConferenceConnectorHandle conn;
            ConferenceConnectorMediaStatisticsEvent args;
            ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceConnectorMediaStatistics", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(args.mediaStreamStats[0].videoReceiveFrameWidth, captureWidth);
            ASSERT_EQ(args.mediaStreamStats[0].videoReceiveFrameHeight, captureHeight);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceParticipantListUpdatedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceParticipantListUpdated", 180000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            //EXPECT_EQ(args.participantList.size(), 0);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 700000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(CPCAPI2::ConferenceConnector::SessionStatus_NotConnected, args.sessionStatus);
         }

         {
            ConferenceConnectorHandle conn;
            ConferenceEndedEvent args;
            ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceEnded", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(args.conference, bobScreenShare);
         }

         /*
         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceParticipantListUpdatedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceParticipantListUpdated", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            //EXPECT_EQ(args.participantList.size(), 0);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 120000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         bob.conferenceConnector->endSession(bobSession);
         */
      }
      catch (...)
      {
      }
      //{
      //   // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
      //   ConferenceConnectorHandle conn;
      //   ConferenceSessionStatusChangedEvent args;
      //   ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
      //   ASSERT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
      //}
      bobPr->set_value(0);
   });

   aliceEvent.wait_for(std::chrono::milliseconds(45000));
   aliceEvent.get();

   bobEvent.wait_for(std::chrono::milliseconds(45000));
   bobEvent.get();
   //bobFu.get();

#if _WIN32
   DestroyWindow(hwndAliceRemote);
   DestroyWindow(hwndBobRemote);
   DestroyWindow(hwndBobRemoteLarge);
#endif // _WIN32

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
}
#endif

TEST_F(ScreenshareLiveServerTests, DISABLED_AVConference_externalServer)
{
   const cpc::string SCREENSHARE_SERVER = "cloudsdk5.bria-x.net";

   LoginResultEvent loginResult; loginResult.success = true;
   cpc::vector<cpc::string> permissions; permissions.push_back("*");

   // Alice is a client SDK (host)
   TestAccount alice("alice", Account_Init);

   // Bob is a client SDK (participant)
   TestAccount bob("bob", Account_Init);

   // enable H.264
   alice.video->queryCodecList();
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);
   bob.video->queryCodecList();
   bob.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);

   alice.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   bob.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   alice.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   bob.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   HWND hwndAliceRemote = NULL;
   HWND hwndBobRemote = NULL;
   HWND hwndBobRemoteLarge = NULL;
#if _WIN32
   ASSERT_EQ(0, ViECreateWindow(hwndAliceRemote, 0, 0, 352, 288, "Alice (incoming)"));
   //alice.video->setIncomingVideoRenderTarget(hwndAliceRemote);
   alice.video->startCapture();

   ASSERT_EQ(0, ViECreateWindow(hwndBobRemote, 356, 0, 352, 288, "Bob (incoming)"));
   //bob.video->setIncomingVideoRenderTarget(hwndBobRemote);
   bob.video->startCapture();

   ASSERT_EQ(0, ViECreateWindow(hwndBobRemoteLarge, 0, 292, 704, 576, "Bob (incoming large)"));

#else // _WIN32
   // CPCAPI2::Media::VideoExt::getInterface(alice.media)->startScreenshare();
#endif

   ConferenceConnectorHandle aliceConfConn = alice.conferenceConnector->createConferenceConnector();
   ConferenceConnectorSettings aliceCloudSettings;
   aliceCloudSettings.authServerUrl = "https://cloudsdk5.bria-x.net:18082";
   aliceCloudSettings.authServerApiKey = "-----BEGIN PUBLIC KEY-----\n"
      "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEZzshRNe4gMUuqNBwrE6leU/N5vW8\n"
      "+HW/pZw5XfsxvzEtUlGwp+A9sNedJNUtjYD/2gFFY/nFuM4Gmnu6zeteTQ==\n"
      "-----END PUBLIC KEY-----";
   aliceCloudSettings.orchestrationServerUrl = "https://" + SCREENSHARE_SERVER + "/jsonApi";
   aliceCloudSettings.regionCode = "LOCAL";
   aliceCloudSettings.username = "user1";
   aliceCloudSettings.password = "1234";
   aliceCloudSettings.ignoreCertVerification = true;
   alice.conferenceConnector->setConnectionSettings(aliceConfConn, aliceCloudSettings);
   alice.conferenceConnector->connectToConferenceService(aliceConfConn);

   std::promise<std::string> joinUrlPr;
   std::future<std::string> joinUrlFu = joinUrlPr.get_future();
   auto aliceEvent = std::async(std::launch::async, [&]() {
      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
      }

      alice.conferenceConnector->queryConferenceList(aliceConfConn);

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 0);
      }

      CloudConferenceSettings confSettings;
      confSettings.conferenceDescription = "mcu test";
      confSettings.conferenceType = CloudConferenceType_AudioVideo_MCU;
      confSettings.conferenceId = "mcu";
      alice.conferenceConnector->createNewConference(aliceConfConn, confSettings);
      CloudConferenceHandle aliceScreenShare = 0;

      {
         ConferenceConnectorHandle conn;
         ConferenceCreatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceCreated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_NE(args.conference, 0);
         ASSERT_NE(args.conference, -1);
         aliceScreenShare = args.conference;
      }

      alice.conferenceConnector->queryConferenceList(aliceConfConn);

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 1);
         ASSERT_FALSE(args.conferenceList[0].joinUrl.empty());
         joinUrlPr.set_value(args.conferenceList[0].joinUrl.c_str());
         ASSERT_GT(args.conferenceList[0].conference, 0); // handle used to create a new session
      }

      CloudConferenceSessionHandle aliceSession = alice.conferenceConnector->createConferenceSession(aliceScreenShare);

      CloudConferenceSessionSettings aliceSessionSettings;
      aliceSessionSettings.role = CloudConferenceRole_Host;
      alice.conferenceConnector->setSessionSettings(aliceSession, aliceSessionSettings);

      CloudConferenceSessionMediaSettings aliceSessionMedia;
      aliceSessionMedia.audioDirection = ConferenceConnector::MediaDirection_SendRecv;
      aliceSessionMedia.videoDirection = ConferenceConnector::MediaDirection_SendRecv;
      aliceSessionMedia.remoteVideoRenderSurface = hwndAliceRemote;
      aliceSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
      alice.conferenceConnector->setSessionMediaSettings(aliceSession, aliceSessionMedia);

      alice.conferenceConnector->startSession(aliceSession);

      try
      {
         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connecting);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(20000));

         safeCout("ALICE ENDS SESSION");
         alice.conferenceConnector->endSession(aliceSession);

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
         }
         //{
         //   // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
         //   ConferenceConnectorHandle conn;
         //   ConferenceSessionStatusChangedEvent args;
         //   EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         //   EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
         //}

         {
            ConferenceConnectorHandle conn;
            ConferenceEndedEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceEnded", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(args.conference, aliceScreenShare);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         alice.conferenceConnector->destroyConferenceConnector(aliceConfConn);



      }
      catch (...)
      {
      }

   });

   std::shared_ptr<std::promise<int> > bobPr(new std::promise<int>());
   std::future<int> bobFu = bobPr->get_future();
   auto bobEvent = std::async(std::launch::async, [&, bobPr]() {
      ASSERT_EQ(joinUrlFu.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
      std::string joinUrlFromAlice = joinUrlFu.get();

      ConferenceConnectorHandle bobConfConn = bob.conferenceConnector->createConferenceConnector();
      ConferenceConnectorSettings bobCloudSettings;
      bobCloudSettings.authServerUrl = "https://cloudsdk5.bria-x.net:18082";
      bobCloudSettings.orchestrationServerUrl = ""; // not needed, we use the joinUrl instead
      bobCloudSettings.joinUrl = joinUrlFromAlice.c_str();
      bobCloudSettings.regionCode = "LOCAL";
      bobCloudSettings.username = "user2";
      bobCloudSettings.password = "1234";
      bobCloudSettings.authServerApiKey = "-----BEGIN PUBLIC KEY-----\n"
         "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEZzshRNe4gMUuqNBwrE6leU/N5vW8\n"
         "+HW/pZw5XfsxvzEtUlGwp+A9sNedJNUtjYD/2gFFY/nFuM4Gmnu6zeteTQ==\n"
         "-----END PUBLIC KEY-----";
      bobCloudSettings.ignoreCertVerification = true;
      bob.conferenceConnector->setConnectionSettings(bobConfConn, bobCloudSettings);
      bob.conferenceConnector->connectToConferenceService(bobConfConn);

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
      }

      bob.conferenceConnector->queryConferenceList(bobConfConn);
      CloudConferenceHandle bobScreenShare;

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 1);
         bobScreenShare = args.conferenceList[0].conference;
      }

      CloudConferenceSessionHandle bobSession = bob.conferenceConnector->createConferenceSession(bobScreenShare);

      CloudConferenceSessionSettings bobSessionSettings;
      bobSessionSettings.role = CloudConferenceRole_Participant;
      bob.conferenceConnector->setSessionSettings(bobSession, bobSessionSettings);

      CloudConferenceSessionMediaSettings bobSessionMedia;
      bobSessionMedia.audioDirection = ConferenceConnector::MediaDirection_SendRecv;
      bobSessionMedia.videoDirection = ConferenceConnector::MediaDirection_SendRecv;
      bobSessionMedia.remoteVideoRenderSurface = hwndBobRemote;
      bobSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
      bob.conferenceConnector->setSessionMediaSettings(bobSession, bobSessionMedia);

      bob.conferenceConnector->startSession(bobSession);

      try
      {
         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connecting);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
            EXPECT_EQ(args.screenshare.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
            EXPECT_EQ(args.audio.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_SendRecv);
            EXPECT_EQ(args.video.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_SendRecv);
            //EXPECT_NE(args.screenshare.mediaStreamId, -1);
            //bob.video->setIncomingVideoRenderTarget(args.screenshare.mediaStreamId, hwndBobRemote);
         }
         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         bobSessionMedia.audioDirection = ConferenceConnector::MediaDirection_SendRecv;
         bobSessionMedia.videoDirection = ConferenceConnector::MediaDirection_SendRecv;
         bobSessionMedia.remoteVideoRenderSurface = hwndBobRemoteLarge;
         bobSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
         bob.conferenceConnector->setSessionMediaSettings(bobSession, bobSessionMedia);
         bob.conferenceConnector->updateSessionMedia(bobSession);

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
            EXPECT_EQ(args.screenshare.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
            EXPECT_EQ(args.audio.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_SendRecv);
            EXPECT_EQ(args.video.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_SendRecv);
            //bob.video->setIncomingVideoRenderTarget(args.screenshare.mediaStreamId, hwndBobRemote);
         }

         //std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceParticipantListUpdatedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceParticipantListUpdated", 180000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            //EXPECT_EQ(args.participantList.size(), 0);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 700000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(CPCAPI2::ConferenceConnector::SessionStatus_NotConnected, args.sessionStatus);
         }

         {
            ConferenceConnectorHandle conn;
            ConferenceEndedEvent args;
            ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceEnded", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(args.conference, bobScreenShare);
         }

         /*
         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceParticipantListUpdatedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceParticipantListUpdated", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            //EXPECT_EQ(args.participantList.size(), 0);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 120000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         bob.conferenceConnector->endSession(bobSession);
         */
      }
      catch (...)
      {
      }
      //{
      //   // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
      //   ConferenceConnectorHandle conn;
      //   ConferenceSessionStatusChangedEvent args;
      //   ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
      //   ASSERT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
      //}
      bobPr->set_value(0);
   });

   aliceEvent.wait_for(std::chrono::milliseconds(45000));
   aliceEvent.get();

   bobEvent.wait_for(std::chrono::milliseconds(45000));
   bobEvent.get();
   //bobFu.get();

#if _WIN32
   DestroyWindow(hwndAliceRemote);
   DestroyWindow(hwndBobRemote);
   DestroyWindow(hwndBobRemoteLarge);
#endif // _WIN32

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
}


/*
INSTANTIATE_TEST_SUITE_P(ConnectSuccessLiveServers, ConferenceConnectorLiveServerParmTests,
   ::testing::Values(std::make_pair<std::string,std::string>("ss1-na-northeast1a.softphone.com", "alice-s1b"),
      std::make_pair<std::string, std::string>("ss2-na-uscentra1a.softphone.com", "alice-s2b"),
      std::make_pair<std::string, std::string>("ss3-eu-west2c.softphone.com", "alice-s3b"),
      std::make_pair<std::string, std::string>("ss4-eu-west2c.softphone.com", "alice-s4b"),
      std::make_pair<std::string, std::string>("ss1-na-northeast1a.softphone.com", "alice-s2b"),
      std::make_pair<std::string, std::string>("ss2-na-uscentra1a.softphone.com", "alice-s1b"),
      std::make_pair<std::string, std::string>("ss3-eu-west2c.softphone.com", "alice-s1b"),
      std::make_pair<std::string, std::string>("ss4-eu-west2c.softphone.com", "alice-s2b"))
);
INSTANTIATE_TEST_SUITE_P(ConnectSuccessLiveServers, ConferenceConnectorLiveServerParmTests,
   ::testing::Values(
      std::make_pair<std::string, std::string>("ss3-eu-west2c.softphone.com", "alice-s3a"))
);
INSTANTIATE_TEST_SUITE_P(ConnectSuccessLiveServers, ConferenceConnectorLiveServerParmTests,
   ::testing::Values(std::make_pair<std::string, std::string>("ss1-staging.softphone.com", "alice-sta1"),
      std::make_pair<std::string, std::string>("ss2-staging.softphone.com", "alice-sta2"),
      std::make_pair<std::string, std::string>("ss1-staging.softphone.com", "alice-sta2"),
      std::make_pair<std::string, std::string>("ss2-staging.softphone.com", "alice-sta1"))
);
*/

// suppress test failure when no INSTANTIATE_TEST_SUITE_P present
GTEST_ALLOW_UNINSTANTIATED_PARAMETERIZED_TEST(ConferenceConnectorLiveServerParmTests);

TEST_P(ConferenceConnectorLiveServerParmTests, ParameterizedConnectSuccessLiveServers)
{
   resip::Data alices3 = CPCAPI2::JsonApi::JsonApiServerInterface::doEncrypt("<EMAIL>");
   auto params = GetParam();
   //const cpc::string SCREENSHARE_SERVER = "cloudsdk-dev.bria-x.net";
   //const cpc::string SCREENSHARE_SERVER = "cloudsdk5.bria-x.net";
   //const cpc::string SCREENSHARE_SERVER = "ss1-na-northeast1a.softphone.com";
   //const cpc::string SCREENSHARE_SERVER = "screenshare.cymbus.com";
   //const cpc::string SCREENSHARE_SERVER = "ss2-na-uscentra1a.softphone.com"; 
   //const cpc::string SCREENSHARE_SERVER = "ss3-eu-west2c.softphone.com";
   const cpc::string SCREENSHARE_SERVER = params.first.c_str();
   //const cpc::string SCREENSHARE_SERVER = "cpclientapi.softphone.com:18090";
   //const cpc::string SCREENSHARE_SERVER = "ss-staging.softphone.com";
   //const cpc::string SCREENSHARE_SERVER = "ptt-east-1.softphone.com";
   //const cpc::string SCREENSHARE_SERVER = "***********:18090";

   LoginResultEvent loginResult; loginResult.success = true;
   cpc::vector<cpc::string> permissions; permissions.push_back("*");

   // Alice is a client SDK (screenshare presenter)
   TestAccount alice("alice");

   // Bob is a client SDK (screenshare participant)
   TestAccount bob("bob");

   // enable H.264
   alice.video->queryCodecList();
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);
   
   bob.video->queryCodecList();
   bob.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);

   alice.video->setIncomingVideoRenderTarget(NULL);
   bob.video->setIncomingVideoRenderTarget(NULL);

   alice.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   bob.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   alice.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   bob.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   HWND hwndAliceRemoteVid = NULL;
   HWND hwndBobRemoteVid = NULL;
   HWND hwndAliceRemote = NULL;
   HWND hwndBobRemote = NULL;
   HWND hwndBobRemoteLarge = NULL;
#if _WIN32
   ASSERT_EQ(0, ViECreateWindow(hwndAliceRemoteVid, 712, 0, 352, 288, "Alice (incoming video)"));
   //alice.video->startCapture();
   alice.video->setIncomingVideoRenderTarget(hwndAliceRemoteVid);

   ASSERT_EQ(0, ViECreateWindow(hwndBobRemoteVid, 1068, 0, 352, 288, "Bob (incoming video)"));
   //bob.video->startCapture();
   bob.video->setIncomingVideoRenderTarget(hwndBobRemoteVid);

   ASSERT_EQ(0, ViECreateWindow(hwndAliceRemote, 0, 0, 352, 288, "Alice (incoming)"));

   ASSERT_EQ(0, ViECreateWindow(hwndBobRemote, 356, 0, 352, 288, "Bob (incoming)"));

   ASSERT_EQ(0, ViECreateWindow(hwndBobRemoteLarge, 0, 292, 1056, 864, "Bob (incoming large)"));

#else // _WIN32
   // CPCAPI2::Media::VideoExt::getInterface(alice.media)->startScreenshare();
#endif

   //TestScreenshareDeviceListHandler tsdlh;
   //CPCAPI2::Media::VideoExt::getInterface(alice.media)->queryScreenshareDeviceList(&tsdlh, true, false);

   std::atomic_bool testDone(false);
   auto mediaEvent = std::async(std::launch::async, [&]() {
      while (!testDone) {
         // conf connector fires method calls it expects to be processed on app thread
         alice.mediaEvents->processNonUnitTestEvents(0);
         bob.mediaEvents->processNonUnitTestEvents(0);
         std::this_thread::sleep_for(std::chrono::milliseconds(20));
      }
   });

   //CPCAPI2::Media::VideoExt::getInterface(alice.media)->setScreenshareCaptureDevice(tsdlh.devices().begin()->deviceId);

   //std::this_thread::sleep_for(std::chrono::seconds(2));

   ConferenceConnectorHandle aliceConfConn = alice.createConferenceConnector();
   ConferenceConnectorSettings aliceCloudSettings;
   aliceCloudSettings.authServerUrl =  "https://cloudsdk5.bria-x.net:18082";// "https://cpclientapi.softphone.com:18082";
   aliceCloudSettings.authServerApiKey = "-----BEGIN PUBLIC KEY-----\n"
      "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEZzshRNe4gMUuqNBwrE6leU/N5vW8\n"
      "+HW/pZw5XfsxvzEtUlGwp+A9sNedJNUtjYD/2gFFY/nFuM4Gmnu6zeteTQ==\n"
      "-----END PUBLIC KEY-----";
   aliceCloudSettings.orchestrationServerUrl = "https://" + SCREENSHARE_SERVER + "/jsonApi";
   //aliceCloudSettings.joinUrl = "https://" + SCREENSHARE_SERVER + "/screenshare/testBSTA-ABCE" + cpc::string(resip::Random::getCryptoRandomBase64(2).c_str());
   aliceCloudSettings.regionCode = "LOCAL";

   resip::Data usernameStr;
   {
      {
         resip::DataStream ds(usernameStr);
         ds << params.second; // "alice-unittests-s4"; // << resip::Random::getCryptoRandomBase64(2);
         ds << "@example.com";
      }
      aliceCloudSettings.username = usernameStr.c_str();
   }
   aliceCloudSettings.password = "1234";
   //aliceCloudSettings.context = "SdkTeamMeeting";
   alice.conferenceConnector->setConnectionSettings(aliceConfConn, aliceCloudSettings);
   alice.conferenceConnector->connectToConferenceService(aliceConfConn);

   std::promise<std::string> joinUrlPr;
   std::future<std::string> joinUrlFu = joinUrlPr.get_future();

   auto aliceEvent = std::async(std::launch::async, [&]() {
      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 30000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
      }

      alice.conferenceConnector->queryConferenceList(aliceConfConn);

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         //ASSERT_EQ(args.conferenceList.size(), 0);
         for (auto conf : args.conferenceList)
         {
            alice.conferenceConnector->destroyConference(conf.conference);

            {
               ConferenceConnectorHandle conn;
               ConferenceEndedEvent args;
               ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceEnded", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            }
         }
      }

      alice.conferenceConnector->queryConferenceList(aliceConfConn);

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 0);
      }

      //{
      //   ConferenceConnectorHandle conn;
      //   ConferenceListUpdatedEvent args;
      //   ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
      //   ASSERT_EQ(args.conferenceList.size(), 0);
      //}

      for (int itc = 0; itc < 1; itc++)
      {
         CloudConferenceSettings confSettings;
         confSettings.conferenceDescription = aliceCloudSettings.username;
         confSettings.conferenceType = CloudConferenceType_Screenshare;
         //confSettings.conferenceId = "screenshare";
         confSettings.persistent = true;
         alice.conferenceConnector->createNewConference(aliceConfConn, confSettings);
         CloudConferenceHandle aliceScreenShare = 0;

         {
            ConferenceConnectorHandle conn;
            ConferenceCreatedEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceCreated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_GT(args.conference, 0);
            aliceScreenShare = args.conference;
         }

         safeCout("conference created with handle " << aliceScreenShare);

         alice.conferenceConnector->queryConferenceList(aliceConfConn);

         cpc::string persistentUrl;
         {
            ConferenceConnectorHandle conn;
            ConferenceListUpdatedEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_GT(args.conferenceList.size(), 0);
            //ASSERT_FALSE(args.conferenceList[0].joinUrl.empty());
            CloudConferenceInfo cci;
            for (auto conf : args.conferenceList)
            {
               if (conf.conference == aliceScreenShare)
               {
                  cci = conf;
                  break;
               }
            }
            if (itc == 0)
            {
               persistentUrl = cci.joinUrl;
            }
            ASSERT_GT(cci.conference, 0); // handle used to create a new session
         }

         alice.conferenceConnector->disconnectFromConferenceService(aliceConfConn);
         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 30000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Disconnecting, args.connectionStatus);
         }
         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 30000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Disconnected, args.connectionStatus);
         }



         aliceConfConn = alice.createConferenceConnector();
         aliceCloudSettings.joinUrl = persistentUrl;
         aliceCloudSettings.orchestrationServerUrl = ""; // "https://" + SCREENSHARE_SERVER + "/jsonApi";
         alice.conferenceConnector->setConnectionSettings(aliceConfConn, aliceCloudSettings);
         alice.conferenceConnector->connectToConferenceService(aliceConfConn);
         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 30000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
         }

         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
         }

         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
         }


         alice.conferenceConnector->queryConferenceList(aliceConfConn);

         {
            ConferenceConnectorHandle conn;
            ConferenceListUpdatedEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            bool found = false;
            for (auto conf : args.conferenceList)
            {
               if (conf.conference == aliceScreenShare)
               {
                  found = true;
                  //alice.conferenceConnector->destroyConference(conf.conference);
               }
            }
            ASSERT_TRUE(found);
            //ASSERT_EQ(args.conferenceList.size(), 1);
            //ASSERT_FALSE(args.conferenceList[0].joinUrl.empty());
            //ASSERT_GT(args.conferenceList[0].conference, 0); // handle used to create a new session
            //auto theConf = args.conferenceList[0].conference;
            //alice.conferenceConnector->destroyConference(theConf);
         }

         alice.conferenceConnector->disconnectFromConferenceService(aliceConfConn);
         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 30000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Disconnecting, args.connectionStatus);
         }
         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 30000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Disconnected, args.connectionStatus);
         }


         aliceConfConn = alice.createConferenceConnector();
         aliceCloudSettings.joinUrl = "";
         aliceCloudSettings.orchestrationServerUrl = "https://" + SCREENSHARE_SERVER + "/jsonApi";
         alice.conferenceConnector->setConnectionSettings(aliceConfConn, aliceCloudSettings);
         alice.conferenceConnector->connectToConferenceService(aliceConfConn);
         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 30000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
         }

         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
         }

         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
         }


         CloudConferenceSettings confSettingsTwo;
         confSettingsTwo.conferenceDescription = usernameStr.c_str();
         confSettingsTwo.conferenceType = CloudConferenceType_Screenshare;
         //confSettingsTwo.conferenceId = "screenshareTwo";
         alice.conferenceConnector->createNewConference(aliceConfConn, confSettingsTwo);

         {
            ConferenceConnectorHandle conn;
            ConferenceCreatedEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceCreated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_GT(args.conference, 0);
            aliceScreenShare = args.conference;
         }


         alice.conferenceConnector->queryConferenceList(aliceConfConn);

         {
            ConferenceConnectorHandle conn;
            ConferenceListUpdatedEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            //ASSERT_EQ(args.conferenceList.size(), 2);
            auto itmyconf = args.conferenceList.begin();
            for (; itmyconf != args.conferenceList.end(); ++itmyconf)
            {
               if (itmyconf->conference == aliceScreenShare)
                  break;
            }
            ASSERT_FALSE(itmyconf == args.conferenceList.end());
            ASSERT_FALSE(itmyconf->joinUrl.empty());
            if (itc == 0)
            {
               joinUrlPr.set_value(itmyconf->joinUrl.c_str());
            }
            //ASSERT_GT(args.conferenceList[0].conference, 0); // handle used to create a new session
         }

         CloudConferenceSessionHandle aliceSession = alice.conferenceConnector->createConferenceSession(aliceScreenShare);

         CloudConferenceSessionSettings aliceSessionSettings;
         aliceSessionSettings.role = CloudConferenceRole_Host;
         alice.conferenceConnector->setSessionSettings(aliceSession, aliceSessionSettings);

         CloudConferenceSessionMediaSettings aliceSessionMedia;
         aliceSessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
         aliceSessionMedia.videoDirection = ConferenceConnector::MediaDirection_SendRecv;
         aliceSessionMedia.remoteVideoRenderSurface = hwndAliceRemote;
         aliceSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
         alice.conferenceConnector->setSessionMediaSettings(aliceSession, aliceSessionMedia);

         alice.conferenceConnector->startSession(aliceSession);

         try
         {
            {
               // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
               ConferenceConnectorHandle conn;
               ConferenceSessionStatusChangedEvent args;
               EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
               EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connecting);
            }

            {
               // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
               ConferenceConnectorHandle conn;
               ConferenceSessionStatusChangedEvent args;
               EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
               EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
            }

            std::this_thread::sleep_for(std::chrono::milliseconds(5000));
            //safeCout("ALICE HANGS UP");
            //alice.conversation->end(aliceCall);
            //alice.video->setIncomingVideoRenderTarget(NULL);
            //bob.video->setIncomingVideoRenderTarget(NULL);

            std::this_thread::sleep_for(std::chrono::milliseconds(45000));

            //alice.conversation->setMediaEnabled(aliceCall, SipConversation::MediaType_Video, true);
            //alice.conversation->sendMediaChangeRequest(aliceCall);
            //safeCout("ALICE setVideoMute");
            //alice.video->setVideoMute(true);

            //std::this_thread::sleep_for(std::chrono::milliseconds(64000));

            safeCout("ALICE ENDS SESSION");
            alice.conferenceConnector->endSession(aliceSession);

            {
               // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
               ConferenceConnectorHandle conn;
               ConferenceSessionStatusChangedEvent args;
               EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
               EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
            }

            {
               ConferenceConnectorHandle conn;
               ConferenceEndedEvent args;
               ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceEnded", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
               ASSERT_EQ(args.conference, aliceScreenShare);
            }

            std::this_thread::sleep_for(std::chrono::milliseconds(5000));

            alice.conferenceConnector->destroyConferenceConnector(aliceConfConn);

            //alice.conferenceConnector->queryConferenceList(aliceConfConn);

            //{
            //   ConferenceConnectorHandle conn;
            //   ConferenceListUpdatedEvent args;
            //   ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            //   ASSERT_EQ(args.conferenceList.size(), 0);
            //}

         }
         catch (...)
         {
         }
      }
   });

   std::shared_ptr<std::promise<int> > bobPr(new std::promise<int>());
   std::future<int> bobFu = bobPr->get_future();
   auto bobEvent = std::async(std::launch::async, [&, bobPr]() {
      ASSERT_EQ(joinUrlFu.wait_for(std::chrono::milliseconds(25000)), std::future_status::ready);
      std::string joinUrlFromAlice = joinUrlFu.get();

      std::this_thread::sleep_for(std::chrono::seconds(2));

      ConferenceConnectorHandle bobConfConn = bob.createConferenceConnector();
      ConferenceConnectorSettings bobCloudSettings;
      bobCloudSettings.authServerUrl = "https://cloudsdk5.bria-x.net:18082";
      bobCloudSettings.authServerApiKey = "-----BEGIN PUBLIC KEY-----\n"
         "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEZzshRNe4gMUuqNBwrE6leU/N5vW8\n"
         "+HW/pZw5XfsxvzEtUlGwp+A9sNedJNUtjYD/2gFFY/nFuM4Gmnu6zeteTQ==\n"
         "-----END PUBLIC KEY-----";
      bobCloudSettings.orchestrationServerUrl = ""; // not needed, we use the joinUrl instead
      bobCloudSettings.joinUrl = joinUrlFromAlice.c_str();
      safeCout("BOB is joining URL: " + joinUrlFromAlice);
      bobCloudSettings.regionCode = "LOCAL";
      {
         resip::Data usernameStr;
         {
            resip::DataStream ds(usernameStr);
            ds << "bob-unittests-" << resip::Random::getCryptoRandomBase64(2);
         }
         bobCloudSettings.username = usernameStr.c_str();
      }
      bobCloudSettings.password = "1234";
      bob.conferenceConnector->setConnectionSettings(bobConfConn, bobCloudSettings);
      bob.conferenceConnector->connectToConferenceService(bobConfConn);

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
      }

      bob.conferenceConnector->queryConferenceList(bobConfConn);
      CloudConferenceHandle bobScreenShare;

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 1);
         bobScreenShare = args.conferenceList[0].conference;
      }

      CloudConferenceSessionHandle bobSession = bob.conferenceConnector->createConferenceSession(bobScreenShare);

      CloudConferenceSessionSettings bobSessionSettings;
      bobSessionSettings.role = CloudConferenceRole_Participant;
      bob.conferenceConnector->setSessionSettings(bobSession, bobSessionSettings);

      CloudConferenceSessionMediaSettings bobSessionMedia;
      bobSessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
      bobSessionMedia.videoDirection = ConferenceConnector::MediaDirection_RecvOnly;
      bobSessionMedia.remoteVideoRenderSurface = hwndBobRemoteLarge;
      bobSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
      bob.conferenceConnector->setSessionMediaSettings(bobSession, bobSessionMedia);

      bob.conferenceConnector->startSession(bobSession);

      try
      {
         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connecting);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
            //EXPECT_EQ(args.screenshare.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_RecvOnly);
            //EXPECT_EQ(args.audio.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
            //EXPECT_EQ(args.video.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
            //bob.video->setIncomingVideoRenderTarget(args.screenshare.mediaStreamId, hwndBobRemote);
         }
         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         //bobSessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
         //bobSessionMedia.videoDirection = ConferenceConnector::MediaDirection_RecvOnly;
         //bobSessionMedia.remoteVideoRenderSurface = hwndBobRemote;
         //bobSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
         //bob.conferenceConnector->setSessionMediaSettings(bobSession, bobSessionMedia);
         //bob.conferenceConnector->updateSessionMedia(bobSession);

         //{
         //   // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
         //   ConferenceConnectorHandle conn;
         //   ConferenceSessionStatusChangedEvent args;
         //   EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         //   EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
         //   EXPECT_EQ(args.screenshare.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_RecvOnly);
         //   EXPECT_EQ(args.audio.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
         //   EXPECT_EQ(args.video.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
         //   //bob.video->setIncomingVideoRenderTarget(args.screenshare.mediaStreamId, hwndBobRemote);
         //}


         std::this_thread::sleep_for(std::chrono::milliseconds(15000));

         //safeCout("BOB HANGS UP");
         //bob.conversation->end(bobCall);
         //alice.video->setIncomingVideoRenderTarget(NULL);
         //bob.video->setIncomingVideoRenderTarget(NULL);

         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceParticipantListUpdatedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceParticipantListUpdated", 180000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            //EXPECT_EQ(args.participantList.size(), 0);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 700000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(CPCAPI2::ConferenceConnector::SessionStatus_NotConnected, args.sessionStatus);
         }

         {
            ConferenceConnectorHandle conn;
            ConferenceEndedEvent args;
            ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceEnded", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(args.conference, bobScreenShare);
         }

         safeCout("BOB HANGS UP");
         alice.video->setIncomingVideoRenderTarget(NULL);
         bob.video->setIncomingVideoRenderTarget(NULL);


         //std::this_thread::sleep_for(std::chrono::milliseconds(10000));
         safeCout("BOB ENDS SESSION");
         bob.conferenceConnector->endSession(bobSession);
         bob.conferenceConnector->destroyConferenceConnector(bobConfConn);

      }
      catch (...)
      {
      }
      //{
      //   // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
      //   ConferenceConnectorHandle conn;
      //   ConferenceSessionStatusChangedEvent args;
      //   ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
      //   ASSERT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
      //}
      bobPr->set_value(0);
   });

   aliceEvent.wait_for(std::chrono::milliseconds(180000));
   aliceEvent.get();

   bobEvent.wait_for(std::chrono::milliseconds(45000));
   bobEvent.get();
   //bobFu.get();

   //waitFor2(aliceEvent, maiaEvent);

   alice.video->stopCapture();
   bob.video->stopCapture();

   testDone = true;

#if _WIN32
   DestroyWindow(hwndAliceRemoteVid);
   DestroyWindow(hwndBobRemoteVid);
   DestroyWindow(hwndAliceRemote);
   DestroyWindow(hwndBobRemote);
   DestroyWindow(hwndBobRemoteLarge);
#endif // _WIN32

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
}

TEST_F(ScreenshareLiveServerTests, DISABLED_ManyParticipantsScalabilityTest)
{
   resip::Data alices3 = CPCAPI2::JsonApi::JsonApiServerInterface::doEncrypt("<EMAIL>");
   //const cpc::string SCREENSHARE_SERVER = "cloudsdk-dev.bria-x.net";
   //const cpc::string SCREENSHARE_SERVER = "cloudsdk5.bria-x.net";
   //const cpc::string SCREENSHARE_SERVER = "ss1-na-northeast1a.softphone.com";
   //const cpc::string SCREENSHARE_SERVER = "screenshare.cymbus.com";
   //const cpc::string SCREENSHARE_SERVER = "ss2-na-uscentra1a.softphone.com"; 
   //const cpc::string SCREENSHARE_SERVER = "ss3-eu-west2c.softphone.com";
   const cpc::string SCREENSHARE_SERVER = "cpclientapi.softphone.com:18090";
   //const cpc::string SCREENSHARE_SERVER = "ss-staging.softphone.com";
   //const cpc::string SCREENSHARE_SERVER = "ptt-east-1.softphone.com";
   //const cpc::string SCREENSHARE_SERVER = "***********:18090";

   LoginResultEvent loginResult; loginResult.success = true;
   cpc::vector<cpc::string> permissions; permissions.push_back("*");

   // Alice is a client SDK (screenshare presenter)
   TestAccount alice("alice");

   // enable H.264
   alice.video->queryCodecList();
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);

   alice.video->setIncomingVideoRenderTarget(NULL);

   alice.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   alice.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   HWND hwndAliceRemoteVid = NULL;
   HWND hwndAliceRemote = NULL;
#if _WIN32
   ASSERT_EQ(0, ViECreateWindow(hwndAliceRemoteVid, 712, 0, 352, 288, "Alice (incoming video)"));
   //alice.video->startCapture();
   alice.video->setIncomingVideoRenderTarget(hwndAliceRemoteVid);

   ASSERT_EQ(0, ViECreateWindow(hwndAliceRemote, 0, 0, 352, 288, "Alice (incoming)"));

#else // _WIN32
   // CPCAPI2::Media::VideoExt::getInterface(alice.media)->startScreenshare();
#endif

   //TestScreenshareDeviceListHandler tsdlh;
   //CPCAPI2::Media::VideoExt::getInterface(alice.media)->queryScreenshareDeviceList(&tsdlh, true, false);

   std::atomic_bool testDone(false);
   auto mediaEvent = std::async(std::launch::async, [&]() {
      while (!testDone) {
         // conf connector fires method calls it expects to be processed on app thread
         alice.mediaEvents->processNonUnitTestEvents(0);
         std::this_thread::sleep_for(std::chrono::milliseconds(20));
      }
   });

   //CPCAPI2::Media::VideoExt::getInterface(alice.media)->setScreenshareCaptureDevice(tsdlh.devices().begin()->deviceId);

   //std::this_thread::sleep_for(std::chrono::seconds(2));

   ConferenceConnectorHandle aliceConfConn = alice.conferenceConnector->createConferenceConnector();
   ConferenceConnectorSettings aliceCloudSettings;
   aliceCloudSettings.authServerUrl = "https://cloudsdk5.bria-x.net:18082";// "https://cpclientapi.softphone.com:18082";
   aliceCloudSettings.authServerApiKey = "-----BEGIN PUBLIC KEY-----\n"
      "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEZzshRNe4gMUuqNBwrE6leU/N5vW8\n"
      "+HW/pZw5XfsxvzEtUlGwp+A9sNedJNUtjYD/2gFFY/nFuM4Gmnu6zeteTQ==\n"
      "-----END PUBLIC KEY-----";
   aliceCloudSettings.orchestrationServerUrl = "https://" + SCREENSHARE_SERVER + "/jsonApi";
   //aliceCloudSettings.joinUrl = "https://" + SCREENSHARE_SERVER + "/screenshare/testBSTA-ABCE" + cpc::string(resip::Random::getCryptoRandomBase64(2).c_str());
   aliceCloudSettings.regionCode = "LOCAL";

   resip::Data usernameStr;
   {
      {
         resip::DataStream ds(usernameStr);
         ds << "alicemanyparticipants" << resip::Random::getCryptoRandomBase64(2);
         ds << "@example.com";
      }
      aliceCloudSettings.username = usernameStr.c_str();
   }
   aliceCloudSettings.password = "1234";
   //aliceCloudSettings.context = "SdkTeamMeeting";
   alice.conferenceConnector->setConnectionSettings(aliceConfConn, aliceCloudSettings);
   alice.conferenceConnector->connectToConferenceService(aliceConfConn);

   std::promise<std::string> joinUrlPr;
   std::future<std::string> joinUrlFu = joinUrlPr.get_future();

   auto aliceEvent = std::async(std::launch::async, [&]() {
      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 30000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
      }

      alice.conferenceConnector->queryConferenceList(aliceConfConn);

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         //ASSERT_EQ(args.conferenceList.size(), 0);
         for (auto conf : args.conferenceList)
         {
            alice.conferenceConnector->destroyConference(conf.conference);

            {
               ConferenceConnectorHandle conn;
               ConferenceEndedEvent args;
               ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceEnded", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            }
         }
      }

      alice.conferenceConnector->queryConferenceList(aliceConfConn);

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 0);
      }

      //{
      //   ConferenceConnectorHandle conn;
      //   ConferenceListUpdatedEvent args;
      //   ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
      //   ASSERT_EQ(args.conferenceList.size(), 0);
      //}

      for (int itc = 0; itc < 1; itc++)
      {
         CloudConferenceSettings confSettings;
         confSettings.conferenceDescription = aliceCloudSettings.username;
         confSettings.conferenceType = CloudConferenceType_Screenshare;
         //confSettings.conferenceId = "screenshare";
         confSettings.persistent = true;
         alice.conferenceConnector->createNewConference(aliceConfConn, confSettings);
         CloudConferenceHandle aliceScreenShare = 0;

         {
            ConferenceConnectorHandle conn;
            ConferenceCreatedEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceCreated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_GT(args.conference, 0);
            aliceScreenShare = args.conference;
         }

         safeCout("conference created with handle " << aliceScreenShare);

         alice.conferenceConnector->queryConferenceList(aliceConfConn);

         cpc::string persistentUrl;
         {
            ConferenceConnectorHandle conn;
            ConferenceListUpdatedEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_GT(args.conferenceList.size(), 0);
            //ASSERT_FALSE(args.conferenceList[0].joinUrl.empty());
            CloudConferenceInfo cci;
            for (auto conf : args.conferenceList)
            {
               if (conf.conference == aliceScreenShare)
               {
                  cci = conf;
                  break;
               }
            }
            if (itc == 0)
            {
               persistentUrl = cci.joinUrl;
            }
            ASSERT_GT(cci.conference, 0); // handle used to create a new session
         }

         alice.conferenceConnector->disconnectFromConferenceService(aliceConfConn);
         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 30000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Disconnecting, args.connectionStatus);
         }
         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 30000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Disconnected, args.connectionStatus);
         }



         aliceConfConn = alice.conferenceConnector->createConferenceConnector();
         aliceCloudSettings.joinUrl = persistentUrl;
         aliceCloudSettings.orchestrationServerUrl = ""; // "https://" + SCREENSHARE_SERVER + "/jsonApi";
         alice.conferenceConnector->setConnectionSettings(aliceConfConn, aliceCloudSettings);
         alice.conferenceConnector->connectToConferenceService(aliceConfConn);
         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 30000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
         }

         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
         }

         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
         }


         alice.conferenceConnector->queryConferenceList(aliceConfConn);

         {
            ConferenceConnectorHandle conn;
            ConferenceListUpdatedEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            bool found = false;
            for (auto conf : args.conferenceList)
            {
               if (conf.conference == aliceScreenShare)
               {
                  found = true;
                  //alice.conferenceConnector->destroyConference(conf.conference);
               }
            }
            ASSERT_TRUE(found);
            //ASSERT_EQ(args.conferenceList.size(), 1);
            //ASSERT_FALSE(args.conferenceList[0].joinUrl.empty());
            //ASSERT_GT(args.conferenceList[0].conference, 0); // handle used to create a new session
            //auto theConf = args.conferenceList[0].conference;
            //alice.conferenceConnector->destroyConference(theConf);
         }

         alice.conferenceConnector->disconnectFromConferenceService(aliceConfConn);
         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 30000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Disconnecting, args.connectionStatus);
         }
         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 30000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Disconnected, args.connectionStatus);
         }


         aliceConfConn = alice.conferenceConnector->createConferenceConnector();
         aliceCloudSettings.joinUrl = "";
         aliceCloudSettings.orchestrationServerUrl = "https://" + SCREENSHARE_SERVER + "/jsonApi";
         alice.conferenceConnector->setConnectionSettings(aliceConfConn, aliceCloudSettings);
         alice.conferenceConnector->connectToConferenceService(aliceConfConn);
         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 30000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
         }

         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
         }

         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
         }


         CloudConferenceSettings confSettingsTwo;
         confSettingsTwo.conferenceDescription = usernameStr.c_str();
         confSettingsTwo.conferenceType = CloudConferenceType_Screenshare;
         //confSettingsTwo.conferenceId = "screenshareTwo";
         alice.conferenceConnector->createNewConference(aliceConfConn, confSettingsTwo);

         {
            ConferenceConnectorHandle conn;
            ConferenceCreatedEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceCreated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_GT(args.conference, 0);
            aliceScreenShare = args.conference;
         }


         alice.conferenceConnector->queryConferenceList(aliceConfConn);

         {
            ConferenceConnectorHandle conn;
            ConferenceListUpdatedEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            //ASSERT_EQ(args.conferenceList.size(), 2);
            auto itmyconf = args.conferenceList.begin();
            for (; itmyconf != args.conferenceList.end(); ++itmyconf)
            {
               if (itmyconf->conference == aliceScreenShare)
                  break;
            }
            ASSERT_FALSE(itmyconf == args.conferenceList.end());
            ASSERT_FALSE(itmyconf->joinUrl.empty());
            if (itc == 0)
            {
               joinUrlPr.set_value(itmyconf->joinUrl.c_str());
            }
            //ASSERT_GT(args.conferenceList[0].conference, 0); // handle used to create a new session
         }

         CloudConferenceSessionHandle aliceSession = alice.conferenceConnector->createConferenceSession(aliceScreenShare);

         CloudConferenceSessionSettings aliceSessionSettings;
         aliceSessionSettings.role = CloudConferenceRole_Host;
         alice.conferenceConnector->setSessionSettings(aliceSession, aliceSessionSettings);

         CloudConferenceSessionMediaSettings aliceSessionMedia;
         aliceSessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
         aliceSessionMedia.videoDirection = ConferenceConnector::MediaDirection_SendRecv;
         aliceSessionMedia.remoteVideoRenderSurface = hwndAliceRemote;
         aliceSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
         alice.conferenceConnector->setSessionMediaSettings(aliceSession, aliceSessionMedia);

         alice.conferenceConnector->startSession(aliceSession);

         try
         {
            {
               // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
               ConferenceConnectorHandle conn;
               ConferenceSessionStatusChangedEvent args;
               EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
               EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connecting);
            }

            {
               // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
               ConferenceConnectorHandle conn;
               ConferenceSessionStatusChangedEvent args;
               EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
               EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
            }

            std::this_thread::sleep_for(std::chrono::milliseconds(5000));
            //safeCout("ALICE HANGS UP");
            //alice.conversation->end(aliceCall);
            //alice.video->setIncomingVideoRenderTarget(NULL);
            //bob.video->setIncomingVideoRenderTarget(NULL);

            std::this_thread::sleep_for(std::chrono::milliseconds(240000));

            //alice.conversation->setMediaEnabled(aliceCall, SipConversation::MediaType_Video, true);
            //alice.conversation->sendMediaChangeRequest(aliceCall);
            //safeCout("ALICE setVideoMute");
            //alice.video->setVideoMute(true);

            //std::this_thread::sleep_for(std::chrono::milliseconds(64000));

            safeCout("ALICE ENDS SESSION");
            alice.conferenceConnector->endSession(aliceSession);

            {
               // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
               ConferenceConnectorHandle conn;
               ConferenceSessionStatusChangedEvent args;
               EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
               EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
            }

            {
               ConferenceConnectorHandle conn;
               ConferenceEndedEvent args;
               ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceEnded", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
               ASSERT_EQ(args.conference, aliceScreenShare);
            }

            std::this_thread::sleep_for(std::chrono::milliseconds(5000));

            alice.conferenceConnector->destroyConferenceConnector(aliceConfConn);

            //alice.conferenceConnector->queryConferenceList(aliceConfConn);

            //{
            //   ConferenceConnectorHandle conn;
            //   ConferenceListUpdatedEvent args;
            //   ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            //   ASSERT_EQ(args.conferenceList.size(), 0);
            //}

         }
         catch (...)
         {
         }
      }
   });

   std::shared_ptr<std::promise<int> > bobPr(new std::promise<int>());
   std::future<int> bobFu = bobPr->get_future();
   ASSERT_EQ(joinUrlFu.wait_for(std::chrono::milliseconds(25000)), std::future_status::ready);
   std::string joinUrlFromAlice = joinUrlFu.get();


   std::vector<std::future<void> > bobEvents;
   std::vector<TestAccount*> testAccounts;

   CPCAPI2::Media::MediaTransportsReactorFactory* reactorFac = CPCAPI2::Media::MediaTransportsReactorFactory::create();
   reactorFac->initialize();

   CPCAPI2::Phone* masterPhone = CPCAPI2::PhoneInternal::create(0);
   masterPhone->initialize(LicenseInfo(), (CPCAPI2::PhoneHandler*)NULL, false);
   resip::MultiReactor* externalLogger = &dynamic_cast<PhoneInterface*>(masterPhone)->getSdkLoggerThread();
   CPCAPI2::Media::MediaManager* masterMediaManager = dynamic_cast<CPCAPI2::Media::MediaManager*>(CPCAPI2::Media::MediaManagerInternal::getInterface(masterPhone, reactorFac));
   masterMediaManager->initializeMediaStack();

   for (int i = 0; i < 20; i++) {
      bobEvents.emplace_back(std::async(std::launch::async, [&, i]() {
         std::this_thread::sleep_for(std::chrono::seconds(i));
         // Bob is a client SDK (screenshare participant)
         CPCAPI2::Phone* testPhone = CPCAPI2::PhoneInterface::create(dynamic_cast<CPCAPI2::PhoneInternal*>(masterPhone), externalLogger);
         LicenseInfo licenseInfo;
         licenseInfo.licenseKey = "";
         licenseInfo.licenseDocumentLocation = "";
         licenseInfo.licenseAor = "";
         testPhone->initialize(licenseInfo, (CPCAPI2::PhoneHandler*)NULL, false);
         testPhone->setLogLevel(CPCAPI2::LogLevel_None);
         dynamic_cast<PhoneInterface*>(testPhone)->registerInterface("MediaManagerInterface", dynamic_cast<PhoneModule*>(masterMediaManager));
         TestAccount* bob = new TestAccount("bob", Account_Init, true, testPhone, reactorFac);
         testAccounts.push_back(bob);

         // enable H.264

         bob->video->queryCodecList();
         bob->video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
         bob->video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
         bob->video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);

         bob->video->setIncomingVideoRenderTarget(NULL);

         bob->video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

         bob->video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

         HWND hwndBobRemoteVid = NULL;
         HWND hwndBobRemoteVid2 = NULL;
         HWND hwndBobRemote = NULL;
         HWND hwndBobRemoteLarge = NULL;

         bob->video->setIncomingVideoRenderTarget(hwndBobRemoteVid2);

         ConferenceConnectorHandle bobConfConn = bob->conferenceConnector->createConferenceConnector();
         ConferenceConnectorSettings bobCloudSettings;
         bobCloudSettings.authServerUrl = "https://cloudsdk5.bria-x.net:18082";
         bobCloudSettings.authServerApiKey = "-----BEGIN PUBLIC KEY-----\n"
            "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEZzshRNe4gMUuqNBwrE6leU/N5vW8\n"
            "+HW/pZw5XfsxvzEtUlGwp+A9sNedJNUtjYD/2gFFY/nFuM4Gmnu6zeteTQ==\n"
            "-----END PUBLIC KEY-----";
         bobCloudSettings.orchestrationServerUrl = ""; // not needed, we use the joinUrl instead
         bobCloudSettings.joinUrl = joinUrlFromAlice.c_str();
         safeCout("BOB is joining URL: " + joinUrlFromAlice);
         bobCloudSettings.regionCode = "LOCAL";
         {
            resip::Data usernameStr;
            {
               resip::DataStream ds(usernameStr);
               ds << "bob-unittests-" << resip::Random::getCryptoRandomBase64(2);
            }
            bobCloudSettings.username = usernameStr.c_str();
         }
         bobCloudSettings.password = "1234";
         bob->conferenceConnector->setConnectionSettings(bobConfConn, bobCloudSettings);
         bob->conferenceConnector->connectToConferenceService(bobConfConn);

         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(bob->conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
         }

         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(bob->conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
         }

         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(bob->conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
         }

         bob->conferenceConnector->queryConferenceList(bobConfConn);
         CloudConferenceHandle bobScreenShare;

         {
            ConferenceConnectorHandle conn;
            ConferenceListUpdatedEvent args;
            ASSERT_TRUE(cpcExpectEvent(bob->conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(args.conferenceList.size(), 1);
            bobScreenShare = args.conferenceList[0].conference;
         }

         CloudConferenceSessionHandle bobSession = bob->conferenceConnector->createConferenceSession(bobScreenShare);

         CloudConferenceSessionSettings bobSessionSettings;
         bobSessionSettings.role = CloudConferenceRole_Participant;
         bob->conferenceConnector->setSessionSettings(bobSession, bobSessionSettings);

         CloudConferenceSessionMediaSettings bobSessionMedia;
         bobSessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
         bobSessionMedia.videoDirection = ConferenceConnector::MediaDirection_RecvOnly;
         bobSessionMedia.remoteVideoRenderSurface = hwndBobRemoteLarge;
         bobSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
         bob->conferenceConnector->setSessionMediaSettings(bobSession, bobSessionMedia);

         bob->conferenceConnector->startSession(bobSession);

         try
         {
            {
               // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
               ConferenceConnectorHandle conn;
               ConferenceSessionStatusChangedEvent args;
               EXPECT_TRUE(cpcExpectEvent(bob->conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
               EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connecting);
            }

            {
               // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
               ConferenceConnectorHandle conn;
               ConferenceSessionStatusChangedEvent args;
               EXPECT_TRUE(cpcExpectEvent(bob->conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
               EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
               //EXPECT_EQ(args.screenshare.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_RecvOnly);
               //EXPECT_EQ(args.audio.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
               //EXPECT_EQ(args.video.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
               //bob->video->setIncomingVideoRenderTarget(args.screenshare.mediaStreamId, hwndBobRemote);
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(5000));

            //bobSessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
            //bobSessionMedia.videoDirection = ConferenceConnector::MediaDirection_RecvOnly;
            //bobSessionMedia.remoteVideoRenderSurface = hwndBobRemote;
            //bobSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
            //bob->conferenceConnector->setSessionMediaSettings(bobSession, bobSessionMedia);
            //bob->conferenceConnector->updateSessionMedia(bobSession);

            //{
            //   // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            //   ConferenceConnectorHandle conn;
            //   ConferenceSessionStatusChangedEvent args;
            //   EXPECT_TRUE(cpcExpectEvent(bob->conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            //   EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
            //   EXPECT_EQ(args.screenshare.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_RecvOnly);
            //   EXPECT_EQ(args.audio.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
            //   EXPECT_EQ(args.video.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
            //   //bob->video->setIncomingVideoRenderTarget(args.screenshare.mediaStreamId, hwndBobRemote);
            //}


            std::this_thread::sleep_for(std::chrono::milliseconds(15000));

            //safeCout("BOB HANGS UP");
            //bob->conversation->end(bobCall);
            //alice.video->setIncomingVideoRenderTarget(NULL);
            //bob->video->setIncomingVideoRenderTarget(NULL);

            std::this_thread::sleep_for(std::chrono::milliseconds(5000));

            {
               // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
               ConferenceConnectorHandle conn;
               ConferenceParticipantListUpdatedEvent args;
               EXPECT_TRUE(cpcExpectEvent(bob->conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceParticipantListUpdated", 180000, CPCAPI2::test::AlwaysTruePred(), conn, args));
               //EXPECT_EQ(args.participantList.size(), 0);
            }

            {
               // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
               ConferenceConnectorHandle conn;
               ConferenceSessionStatusChangedEvent args;
               EXPECT_TRUE(cpcExpectEvent(bob->conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 700000, CPCAPI2::test::AlwaysTruePred(), conn, args));
               EXPECT_EQ(CPCAPI2::ConferenceConnector::SessionStatus_NotConnected, args.sessionStatus);
            }

            {
               ConferenceConnectorHandle conn;
               ConferenceEndedEvent args;
               ASSERT_TRUE(cpcExpectEvent(bob->conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceEnded", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
               ASSERT_EQ(args.conference, bobScreenShare);
            }

            safeCout("BOB HANGS UP");
            alice.video->setIncomingVideoRenderTarget(NULL);
            bob->video->setIncomingVideoRenderTarget(NULL);


            //std::this_thread::sleep_for(std::chrono::milliseconds(10000));
            safeCout("BOB ENDS SESSION");
            bob->conferenceConnector->endSession(bobSession);
            bob->conferenceConnector->destroyConferenceConnector(bobConfConn);

         }
         catch (...)
         {
         }
      }));
   }

   aliceEvent.wait_for(std::chrono::milliseconds(180000));
   aliceEvent.get();

   for (auto& bobEvent : bobEvents) {
      bobEvent.wait_for(std::chrono::milliseconds(35000));
      bobEvent.get();
   }

   //waitFor2(aliceEvent, maiaEvent);

   alice.video->stopCapture();

   testDone = true;

#if _WIN32
   DestroyWindow(hwndAliceRemoteVid);
   DestroyWindow(hwndAliceRemote);
#endif // _WIN32

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
}

TEST_F(ScreenshareLiveServerTests, DISABLED_ConnectSuccess_local_load_test)
{
   //const cpc::string SCREENSHARE_SERVER = "cloudsdk-dev.bria-x.net";
   //const cpc::string SCREENSHARE_SERVER = "cloudsdk5.bria-x.net";
   //const cpc::string SCREENSHARE_SERVER = "ss1-na-northeast1a.softphone.com";
   //const cpc::string SCREENSHARE_SERVER = "screenshare.cymbus.com";
   //const cpc::string SCREENSHARE_SERVER = "ss2-na-uscentra1a.softphone.com"; 
   //const cpc::string SCREENSHARE_SERVER = "ss3-eu-west2c.softphone.com";
   //const cpc::string SCREENSHARE_SERVER = "ss4-eu-west2c.softphone.com";
   const cpc::string SCREENSHARE_SERVER = "cpclientapi.softphone.com:18090";
   //const cpc::string SCREENSHARE_SERVER = "ss-staging.softphone.com";
   //const cpc::string SCREENSHARE_SERVER = "ptt-east-1.softphone.com";
   //const cpc::string SCREENSHARE_SERVER = "***********:18090";

   LoginResultEvent loginResult; loginResult.success = true;
   cpc::vector<cpc::string> permissions; permissions.push_back("*");

   // Alice is a client SDK (screenshare presenter)
   TestAccount alice("alice");

   // enable H.264
   alice.video->queryCodecList();
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);

   alice.video->setIncomingVideoRenderTarget(NULL);

   alice.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   alice.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   HWND hwndAliceRemoteVid = NULL;
   HWND hwndBobRemoteVid = NULL;
   HWND hwndAliceRemote = NULL;
   HWND hwndBobRemote = NULL;
   HWND hwndBobRemoteLarge = NULL;

   //TestScreenshareDeviceListHandler tsdlh;
   //CPCAPI2::Media::VideoExt::getInterface(alice.media)->queryScreenshareDeviceList(&tsdlh, true, false);

   std::atomic_bool testDone(false);
   auto mediaEvent = std::async(std::launch::async, [&]() {
      while (!testDone) {
         // conf connector fires method calls it expects to be processed on app thread
         alice.mediaEvents->processNonUnitTestEvents(0);
         std::this_thread::sleep_for(std::chrono::milliseconds(20));
      }
   });

   //CPCAPI2::Media::VideoExt::getInterface(alice.media)->setScreenshareCaptureDevice(tsdlh.devices().begin()->deviceId);

   //std::this_thread::sleep_for(std::chrono::seconds(2));
   for (int testCount = 0; testCount < 10; testCount++)
   {
      std::this_thread::sleep_for(std::chrono::seconds(1));

      ConferenceConnectorHandle aliceConfConn = alice.conferenceConnector->createConferenceConnector();
      ConferenceConnectorSettings aliceCloudSettings;
      aliceCloudSettings.authServerUrl = "https://cloudsdk5.bria-x.net:18082";// "https://cpclientapi.softphone.com:18082";
      aliceCloudSettings.authServerApiKey = "-----BEGIN PUBLIC KEY-----\n"
         "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEZzshRNe4gMUuqNBwrE6leU/N5vW8\n"
         "+HW/pZw5XfsxvzEtUlGwp+A9sNedJNUtjYD/2gFFY/nFuM4Gmnu6zeteTQ==\n"
         "-----END PUBLIC KEY-----";
      aliceCloudSettings.orchestrationServerUrl = "https://" + SCREENSHARE_SERVER + "/jsonApi";
      //aliceCloudSettings.joinUrl = "https://" + SCREENSHARE_SERVER + "/screenshare/testBSTA-ABCE" + cpc::string(resip::Random::getCryptoRandomBase64(2).c_str());
      aliceCloudSettings.regionCode = "LOCAL";

      resip::Data usernameStr;
      {
         {
            resip::DataStream ds(usernameStr);
            ds << "alice-unittests-" << resip::Random::getCryptoRandomBase64(6);
            ds << "@example.com";
         }
         aliceCloudSettings.username = usernameStr.c_str();
      }
      aliceCloudSettings.password = "1234";
      //aliceCloudSettings.context = "SdkTeamMeeting";
      alice.conferenceConnector->setConnectionSettings(aliceConfConn, aliceCloudSettings);
      alice.conferenceConnector->connectToConferenceService(aliceConfConn);

      std::promise<std::string> joinUrlPr;
      std::future<std::string> joinUrlFu = joinUrlPr.get_future();

      std::async(std::launch::async, [&]() {
         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 30000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
         }

         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
         }

         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
         }


         std::this_thread::sleep_for(std::chrono::milliseconds(500));

      });
   }


   //aliceEvent.wait_for(std::chrono::milliseconds(180000));
   //aliceEvent.get();


   //waitFor2(aliceEvent, maiaEvent);

   alice.video->stopCapture();

   testDone = true;

#if _WIN32
   DestroyWindow(hwndAliceRemoteVid);
   DestroyWindow(hwndAliceRemote);
#endif // _WIN32

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
}


TEST_F(ScreenshareLiveServerTests, DISABLED_ConnectSuccess_local_load_test2)
{
   //const cpc::string SCREENSHARE_SERVER = "cloudsdk-dev.bria-x.net";
   //const cpc::string SCREENSHARE_SERVER = "cloudsdk5.bria-x.net";
   //const cpc::string SCREENSHARE_SERVER = "ss1-na-northeast1a.softphone.com";
   //const cpc::string SCREENSHARE_SERVER = "screenshare.cymbus.com";
   //const cpc::string SCREENSHARE_SERVER = "ss2-na-uscentra1a.softphone.com"; 
   //const cpc::string SCREENSHARE_SERVER = "ss3-eu-west2c.softphone.com";
   //const cpc::string SCREENSHARE_SERVER = "ss4-eu-west2c.softphone.com";
   const cpc::string SCREENSHARE_SERVER = "cpclientapi.softphone.com:18090";
   //const cpc::string SCREENSHARE_SERVER = "ss-staging.softphone.com";
   //const cpc::string SCREENSHARE_SERVER = "ptt-east-1.softphone.com";
   //const cpc::string SCREENSHARE_SERVER = "***********:18090";

   LoginResultEvent loginResult; loginResult.success = true;
   cpc::vector<cpc::string> permissions; permissions.push_back("*");

   // Alice is a client SDK (screenshare presenter)
   TestAccount alice("alice");

   // enable H.264
   alice.video->queryCodecList();
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);

   alice.video->setIncomingVideoRenderTarget(NULL);

   alice.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   alice.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   HWND hwndAliceRemoteVid = NULL;
   HWND hwndBobRemoteVid = NULL;
   HWND hwndAliceRemote = NULL;
   HWND hwndBobRemote = NULL;
   HWND hwndBobRemoteLarge = NULL;
#if _WIN32
   ASSERT_EQ(0, ViECreateWindow(hwndAliceRemoteVid, 712, 0, 352, 288, "Alice (incoming video)"));
   //alice.video->startCapture();
   alice.video->setIncomingVideoRenderTarget(hwndAliceRemoteVid);


   ASSERT_EQ(0, ViECreateWindow(hwndAliceRemote, 0, 0, 352, 288, "Alice (incoming)"));

#else // _WIN32
   // CPCAPI2::Media::VideoExt::getInterface(alice.media)->startScreenshare();
#endif

   //TestScreenshareDeviceListHandler tsdlh;
   //CPCAPI2::Media::VideoExt::getInterface(alice.media)->queryScreenshareDeviceList(&tsdlh, true, false);

   std::atomic_bool testDone(false);
   auto mediaEvent = std::async(std::launch::async, [&]() {
      while (!testDone) {
         // conf connector fires method calls it expects to be processed on app thread
         alice.mediaEvents->processNonUnitTestEvents(0);
         std::this_thread::sleep_for(std::chrono::milliseconds(20));
      }
   });

   //CPCAPI2::Media::VideoExt::getInterface(alice.media)->setScreenshareCaptureDevice(tsdlh.devices().begin()->deviceId);

   //std::this_thread::sleep_for(std::chrono::seconds(2));

   ConferenceConnectorHandle aliceConfConn = alice.conferenceConnector->createConferenceConnector();
   ConferenceConnectorSettings aliceCloudSettings;
   aliceCloudSettings.authServerUrl = "https://cloudsdk5.bria-x.net:18082";// "https://cpclientapi.softphone.com:18082";
   aliceCloudSettings.authServerApiKey = "-----BEGIN PUBLIC KEY-----\n"
      "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEZzshRNe4gMUuqNBwrE6leU/N5vW8\n"
      "+HW/pZw5XfsxvzEtUlGwp+A9sNedJNUtjYD/2gFFY/nFuM4Gmnu6zeteTQ==\n"
      "-----END PUBLIC KEY-----";
   aliceCloudSettings.orchestrationServerUrl = "https://" + SCREENSHARE_SERVER + "/jsonApi";
   //aliceCloudSettings.joinUrl = "https://" + SCREENSHARE_SERVER + "/screenshare/testBSTA-ABCE" + cpc::string(resip::Random::getCryptoRandomBase64(2).c_str());
   aliceCloudSettings.regionCode = "LOCAL";

   resip::Data usernameStr;
   {
      {
         resip::DataStream ds(usernameStr);
         ds << "alice-unittests-" << resip::Random::getCryptoRandomBase64(6);
         ds << "@example.com";
      }
      aliceCloudSettings.username = usernameStr.c_str();
   }
   aliceCloudSettings.password = "1234";
   //aliceCloudSettings.context = "SdkTeamMeeting";
   alice.conferenceConnector->setConnectionSettings(aliceConfConn, aliceCloudSettings);
   alice.conferenceConnector->connectToConferenceService(aliceConfConn);

   std::promise<std::string> joinUrlPr;
   std::future<std::string> joinUrlFu = joinUrlPr.get_future();

   auto aliceEvent = std::async(std::launch::async, [&]() {
      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 30000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
      }

      alice.conferenceConnector->queryConferenceList(aliceConfConn);

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         //ASSERT_EQ(args.conferenceList.size(), 0);
         for (auto conf : args.conferenceList)
         {
            alice.conferenceConnector->destroyConference(conf.conference);

            {
               ConferenceConnectorHandle conn;
               ConferenceEndedEvent args;
               ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceEnded", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            }
         }
      }

      alice.conferenceConnector->queryConferenceList(aliceConfConn);

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 0);
      }

      //{
      //   ConferenceConnectorHandle conn;
      //   ConferenceListUpdatedEvent args;
      //   ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
      //   ASSERT_EQ(args.conferenceList.size(), 0);
      //}

      for (int itc = 0; itc < 1; itc++)
      {
         CloudConferenceSettings confSettings;
         confSettings.conferenceDescription = aliceCloudSettings.username;
         confSettings.conferenceType = CloudConferenceType_Screenshare;
         //confSettings.conferenceId = "screenshare";
         confSettings.persistent = true;
         alice.conferenceConnector->createNewConference(aliceConfConn, confSettings);
         CloudConferenceHandle aliceScreenShare = 0;

         {
            ConferenceConnectorHandle conn;
            ConferenceCreatedEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceCreated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_GT(args.conference, 0);
            aliceScreenShare = args.conference;
         }

         safeCout("conference created with handle " << aliceScreenShare);

         alice.conferenceConnector->queryConferenceList(aliceConfConn);

         cpc::string persistentUrl;
         {
            ConferenceConnectorHandle conn;
            ConferenceListUpdatedEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_GT(args.conferenceList.size(), 0);
            //ASSERT_FALSE(args.conferenceList[0].joinUrl.empty());
            CloudConferenceInfo cci;
            for (auto conf : args.conferenceList)
            {
               if (conf.conference == aliceScreenShare)
               {
                  cci = conf;
                  break;
               }
            }
            if (itc == 0)
            {
               persistentUrl = cci.joinUrl;
            }
            ASSERT_GT(cci.conference, 0); // handle used to create a new session
         }

         alice.conferenceConnector->disconnectFromConferenceService(aliceConfConn);
         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 30000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Disconnecting, args.connectionStatus);
         }
         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 30000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Disconnected, args.connectionStatus);
         }



         aliceConfConn = alice.conferenceConnector->createConferenceConnector();
         aliceCloudSettings.joinUrl = persistentUrl;
         aliceCloudSettings.orchestrationServerUrl = ""; // "https://" + SCREENSHARE_SERVER + "/jsonApi";
         alice.conferenceConnector->setConnectionSettings(aliceConfConn, aliceCloudSettings);
         alice.conferenceConnector->connectToConferenceService(aliceConfConn);
         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 30000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
         }

         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
         }

         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
         }


         alice.conferenceConnector->queryConferenceList(aliceConfConn);

         {
            ConferenceConnectorHandle conn;
            ConferenceListUpdatedEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            bool found = false;
            for (auto conf : args.conferenceList)
            {
               if (conf.conference == aliceScreenShare)
               {
                  found = true;
                  //alice.conferenceConnector->destroyConference(conf.conference);
               }
            }
            ASSERT_TRUE(found);
            //ASSERT_EQ(args.conferenceList.size(), 1);
            //ASSERT_FALSE(args.conferenceList[0].joinUrl.empty());
            //ASSERT_GT(args.conferenceList[0].conference, 0); // handle used to create a new session
            //auto theConf = args.conferenceList[0].conference;
            //alice.conferenceConnector->destroyConference(theConf);
         }

         alice.conferenceConnector->disconnectFromConferenceService(aliceConfConn);
         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 30000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Disconnecting, args.connectionStatus);
         }
         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 30000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Disconnected, args.connectionStatus);
         }


         aliceConfConn = alice.conferenceConnector->createConferenceConnector();
         aliceCloudSettings.joinUrl = "";
         aliceCloudSettings.orchestrationServerUrl = "https://" + SCREENSHARE_SERVER + "/jsonApi";
         alice.conferenceConnector->setConnectionSettings(aliceConfConn, aliceCloudSettings);
         alice.conferenceConnector->connectToConferenceService(aliceConfConn);
         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 30000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
         }

         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
         }

         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
         }


         CloudConferenceSettings confSettingsTwo;
         confSettingsTwo.conferenceDescription = usernameStr.c_str();
         confSettingsTwo.conferenceType = CloudConferenceType_Screenshare;
         //confSettingsTwo.conferenceId = "screenshareTwo";
         alice.conferenceConnector->createNewConference(aliceConfConn, confSettingsTwo);

         {
            ConferenceConnectorHandle conn;
            ConferenceCreatedEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceCreated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_GT(args.conference, 0);
            aliceScreenShare = args.conference;
         }


         alice.conferenceConnector->queryConferenceList(aliceConfConn);

         {
            ConferenceConnectorHandle conn;
            ConferenceListUpdatedEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            //ASSERT_EQ(args.conferenceList.size(), 2);
            auto itmyconf = args.conferenceList.begin();
            for (; itmyconf != args.conferenceList.end(); ++itmyconf)
            {
               if (itmyconf->conference == aliceScreenShare)
                  break;
            }
            ASSERT_FALSE(itmyconf == args.conferenceList.end());
            ASSERT_FALSE(itmyconf->joinUrl.empty());
            if (itc == 0)
            {
               joinUrlPr.set_value(itmyconf->joinUrl.c_str());
            }
            //ASSERT_GT(args.conferenceList[0].conference, 0); // handle used to create a new session
         }



            std::this_thread::sleep_for(std::chrono::milliseconds(5000));

            alice.conferenceConnector->destroyConferenceConnector(aliceConfConn);

      }
   });



   aliceEvent.wait_for(std::chrono::milliseconds(180000));
   aliceEvent.get();


   //waitFor2(aliceEvent, maiaEvent);

   alice.video->stopCapture();

   testDone = true;

#if _WIN32
   DestroyWindow(hwndAliceRemoteVid);
   DestroyWindow(hwndAliceRemote);
#endif // _WIN32

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
}

TEST_F(ScreenshareLiveServerTests, DISABLED_ScalabilityTest)
{
   const cpc::string SCREENSHARE_SERVER = "cloudsdk4.bria-x.net"; 
   //const cpc::string SCREENSHARE_SERVER = "**************:18090";
   std::vector<std::future<void> > bobEvents;
   std::vector<TestAccount*> testAccounts;

   CPCAPI2::Media::MediaTransportsReactorFactory* reactorFac = CPCAPI2::Media::MediaTransportsReactorFactory::create();
   reactorFac->initialize();

   CPCAPI2::Phone* masterPhone = CPCAPI2::PhoneInternal::create(0);
   masterPhone->initialize(LicenseInfo(), (CPCAPI2::PhoneHandler*)NULL, false);
   resip::MultiReactor* externalLogger = &dynamic_cast<PhoneInterface*>(masterPhone)->getSdkLoggerThread();
   CPCAPI2::Media::MediaManager* masterMediaManager = dynamic_cast<CPCAPI2::Media::MediaManager*>(CPCAPI2::Media::MediaManagerInternal::getInterface(masterPhone, reactorFac));
   masterMediaManager->initializeMediaStack();

   for (int i = 0; i < 25; i++) {
      bobEvents.emplace_back( std::async(std::launch::async, [&, i]() {
         std::this_thread::sleep_for(std::chrono::seconds(i));
         // Bob is a client SDK (screenshare participant)
         CPCAPI2::Phone* testPhone = CPCAPI2::PhoneInterface::create(dynamic_cast<CPCAPI2::PhoneInternal*>(masterPhone), externalLogger);
         LicenseInfo licenseInfo;
         licenseInfo.licenseKey = "";
         licenseInfo.licenseDocumentLocation = "";
         licenseInfo.licenseAor = "";
         testPhone->initialize(licenseInfo, (CPCAPI2::PhoneHandler*)NULL, false);
         testPhone->setLogLevel(CPCAPI2::LogLevel_None);
         dynamic_cast<PhoneInterface*>(testPhone)->registerInterface("MediaManagerInterface", dynamic_cast<PhoneModule*>(masterMediaManager));
         TestAccount* bob = new TestAccount("bob", Account_Init, true, testPhone, reactorFac);
         testAccounts.push_back(bob);

         // enable H.264
         
         bob->video->queryCodecList();
         bob->video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
         bob->video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
         bob->video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);

         bob->video->setIncomingVideoRenderTarget(NULL);

         bob->video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

         bob->video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

         HWND hwndBobRemoteVid = NULL;
         HWND hwndBobRemoteVid2 = NULL;
         HWND hwndBobRemote = NULL;
         HWND hwndBobRemoteLarge = NULL;

         bob->video->setIncomingVideoRenderTarget(hwndBobRemoteVid2);
         //ASSERT_EQ(joinUrlFu.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
         std::string joinUrlFromAlice = "https://cloudsdk4.bria-x.net/screenshare/D0/counterpath-dev"; //joinUrlFu.get();
         //std::string joinUrlFromAlice = "http://**************:18090/confbridge/<EMAIL>/";

         ConferenceConnectorHandle bobConfConn = bob->conferenceConnector->createConferenceConnector();
         ConferenceConnectorSettings bobCloudSettings;
         bobCloudSettings.authServerUrl = "https://auth.softphone.com";
         bobCloudSettings.authServerApiKey = "-----BEGIN PUBLIC KEY-----\n"
            "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEZzshRNe4gMUuqNBwrE6leU/N5vW8\n"
            "+HW/pZw5XfsxvzEtUlGwp+A9sNedJNUtjYD/2gFFY/nFuM4Gmnu6zeteTQ==\n"
            "-----END PUBLIC KEY-----";
         bobCloudSettings.orchestrationServerUrl = ""; // not needed, we use the joinUrl instead
         bobCloudSettings.joinUrl = joinUrlFromAlice.c_str();
         safeCout("BOB is joining URL: " + joinUrlFromAlice);
         bobCloudSettings.regionCode = "LOCAL";
         {
            resip::Data usernameStr;
            {
               resip::DataStream ds(usernameStr);
               ds << "bob-unittests-" << resip::Random::getCryptoRandomBase64(2);
            }
            bobCloudSettings.username = usernameStr.c_str();
         }
         bobCloudSettings.password = "1234";
         bob->conferenceConnector->setConnectionSettings(bobConfConn, bobCloudSettings);
         bob->conferenceConnector->connectToConferenceService(bobConfConn);

         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(bob->conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
         }

         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(bob->conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
         }

         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(bob->conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
         }

         bob->conferenceConnector->queryConferenceList(bobConfConn);
         CloudConferenceHandle bobScreenShare;

         {
            ConferenceConnectorHandle conn;
            ConferenceListUpdatedEvent args;
            ASSERT_TRUE(cpcExpectEvent(bob->conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(args.conferenceList.size(), 1);
            bobScreenShare = args.conferenceList[0].conference;
         }

         CloudConferenceSessionHandle bobSession = bob->conferenceConnector->createConferenceSession(bobScreenShare);

         CloudConferenceSessionSettings bobSessionSettings;
         bobSessionSettings.role = CloudConferenceRole_Participant;
         bob->conferenceConnector->setSessionSettings(bobSession, bobSessionSettings);

         CloudConferenceSessionMediaSettings bobSessionMedia;
         bobSessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
         bobSessionMedia.videoDirection = ConferenceConnector::MediaDirection_RecvOnly;
         bobSessionMedia.remoteVideoRenderSurface = hwndBobRemote;
         bobSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
         bob->conferenceConnector->setSessionMediaSettings(bobSession, bobSessionMedia);

         bob->conferenceConnector->startSession(bobSession);

         try
         {
            {
               // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
               ConferenceConnectorHandle conn;
               ConferenceSessionStatusChangedEvent args;
               EXPECT_TRUE(cpcExpectEvent(bob->conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
               EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connecting);
            }

            {
               // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
               ConferenceConnectorHandle conn;
               ConferenceSessionStatusChangedEvent args;
               EXPECT_TRUE(cpcExpectEvent(bob->conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
               EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
               EXPECT_EQ(args.screenshare.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_RecvOnly);
               EXPECT_EQ(args.audio.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
               EXPECT_EQ(args.video.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
               EXPECT_NE(args.screenshare.mediaStreamId, -1);
               //bob->video->setIncomingVideoRenderTarget(args.screenshare.mediaStreamId, hwndBobRemote);
            }
            //std::this_thread::sleep_for(std::chrono::milliseconds(5000));
            //std::this_thread::sleep_for(std::chrono::milliseconds(5000));
            //std::this_thread::sleep_for(std::chrono::milliseconds(5000));

            {
               // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
               ConferenceConnectorHandle conn;
               ConferenceParticipantListUpdatedEvent args;
               EXPECT_TRUE(cpcExpectEvent(bob->conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceParticipantListUpdated", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            }

            //std::this_thread::sleep_for(std::chrono::seconds(180));
            //bob->conferenceConnector->endSession(bobSession);

            //{
            //   // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            //   ConferenceConnectorHandle conn;
            //   ConferenceSessionStatusChangedEvent args;
            //   EXPECT_TRUE(cpcExpectEvent(bob->conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 180000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            //   EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
            //}

            //safeCout("BOB HANGS UP");
            //bob->video->setIncomingVideoRenderTarget(NULL);
         }
         catch (...)
         {
         }
      }));
   }

   std::this_thread::sleep_for(std::chrono::seconds(240));

   for (auto& bobEvent : bobEvents) {
      bobEvent.wait_for(std::chrono::milliseconds(45000));
      bobEvent.get();
   }

   for (TestAccount* testAcct : testAccounts) {
      delete testAcct;
   }

   reactorFac->shutdown();

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
}

TEST_F(ScreenshareLiveServerTests, DISABLED_AudioConferenceScalabilityTest)
{
   const cpc::string SCREENSHARE_SERVER = "cloudsdk4.bria-x.net";
   //const cpc::string SCREENSHARE_SERVER = "**************:18090";
   std::vector<std::future<void> > bobEvents;
   std::vector<TestAccount*> testAccounts;

   CPCAPI2::Media::MediaTransportsReactorFactory* reactorFac = CPCAPI2::Media::MediaTransportsReactorFactory::create();
   reactorFac->initialize();

   CPCAPI2::Phone* masterPhone = CPCAPI2::PhoneInternal::create(0);
   masterPhone->initialize(LicenseInfo(), (CPCAPI2::PhoneHandler*)NULL, false);
   resip::MultiReactor* externalLogger = &dynamic_cast<PhoneInterface*>(masterPhone)->getSdkLoggerThread();
   CPCAPI2::Media::MediaManager* masterMediaManager = dynamic_cast<CPCAPI2::Media::MediaManager*>(CPCAPI2::Media::MediaManagerInternal::getInterface(masterPhone, reactorFac));
   CPCAPI2::Media::MediaStackSettings mediaStackSettings;
   mediaStackSettings.audioLayer = CPCAPI2::Media::AudioLayers_Dummy;
   masterMediaManager->initializeMediaStack(mediaStackSettings);

   for (int i = 0; i < 50; i++) {
      bobEvents.emplace_back(std::async(std::launch::async, [&, i]() {
         std::this_thread::sleep_for(std::chrono::seconds(i));
         // Bob is a client SDK (screenshare participant)
         CPCAPI2::Phone* testPhone = CPCAPI2::PhoneInterface::create(dynamic_cast<CPCAPI2::PhoneInternal*>(masterPhone), externalLogger);
         LicenseInfo licenseInfo;
         licenseInfo.licenseKey = "";
         licenseInfo.licenseDocumentLocation = "";
         licenseInfo.licenseAor = "";
         testPhone->initialize(licenseInfo, (CPCAPI2::PhoneHandler*)NULL, false);
         testPhone->setLogLevel(CPCAPI2::LogLevel_None);
         dynamic_cast<PhoneInterface*>(testPhone)->registerInterface("MediaManagerInterface", dynamic_cast<PhoneModule*>(masterMediaManager));
         TestAccount* bob = new TestAccount("bob", Account_Init, true, testPhone, reactorFac);
         testAccounts.push_back(bob);

         //ASSERT_EQ(joinUrlFu.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
         std::string joinUrlFromAlice = "https://cloudsdk4.bria-x.net/screenshare/D0/counterpath-dev"; //joinUrlFu.get();
         //std::string joinUrlFromAlice = "http://**************:18090/confbridge/<EMAIL>/";

         ConferenceConnectorHandle bobConfConn = bob->conferenceConnector->createConferenceConnector();
         ConferenceConnectorSettings bobCloudSettings;
         bobCloudSettings.authServerUrl = "https://auth.softphone.com";
         bobCloudSettings.authServerApiKey = "-----BEGIN PUBLIC KEY-----\n"
            "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEZzshRNe4gMUuqNBwrE6leU/N5vW8\n"
            "+HW/pZw5XfsxvzEtUlGwp+A9sNedJNUtjYD/2gFFY/nFuM4Gmnu6zeteTQ==\n"
            "-----END PUBLIC KEY-----";
         bobCloudSettings.orchestrationServerUrl = ""; // not needed, we use the joinUrl instead
         bobCloudSettings.joinUrl = joinUrlFromAlice.c_str();
         safeCout("BOB is joining URL: " + joinUrlFromAlice);
         bobCloudSettings.regionCode = "LOCAL";
         {
            resip::Data usernameStr;
            {
               resip::DataStream ds(usernameStr);
               ds << "bob-unittests-" << resip::Random::getCryptoRandomBase64(2);
            }
            bobCloudSettings.username = usernameStr.c_str();
         }
         bobCloudSettings.password = "1234";
         bob->conferenceConnector->setConnectionSettings(bobConfConn, bobCloudSettings);
         bob->conferenceConnector->connectToConferenceService(bobConfConn);

         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(bob->conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
         }

         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(bob->conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
         }

         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(bob->conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
         }

         bob->conferenceConnector->queryConferenceList(bobConfConn);
         CloudConferenceHandle bobScreenShare = (CloudConferenceHandle)(-1);

         {
            ConferenceConnectorHandle conn;
            ConferenceListUpdatedEvent args;
            ASSERT_TRUE(cpcExpectEvent(bob->conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            for (const CloudConferenceInfo& cci : args.conferenceList)
            {
               if (cci.displayName == cpc::string("Electronic"))
               {
                  bobScreenShare = cci.conference;
                  break;
               }
            }
            ASSERT_NE(bobScreenShare, (CloudConferenceHandle)(-1));
         }

         bool canJoin = false;

         while (!canJoin)
         {
            ConferenceConnectorHandle conn;
            ConferenceParticipantListUpdatedEvent args;
            if (cpcWaitForEvent(bob->conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceParticipantListUpdated", 1000, CPCAPI2::test::AlwaysTruePred(), conn, args))
            {
               if (args.conference == bobScreenShare)
               {
                  safeCout("onConferenceParticipantListUpdated for our conference (wait to join)");
                  if (args.participantList.size() >= 1)
                  {
                     bool someoneHasTheFloor = false;
                     for (const CloudConferenceParticipantInfo& ccpi : args.participantList)
                     {
                        if (ccpi.hasFloor)
                        {
                           someoneHasTheFloor = true;
                        }
                     }
                     if (someoneHasTheFloor)
                     {
                        canJoin = true;
                     }
                  }
               }
            }
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(i * 20));

         CloudConferenceSessionHandle bobSession = bob->conferenceConnector->createConferenceSession(bobScreenShare);

         CloudConferenceSessionSettings bobSessionSettings;
         bobSessionSettings.role = CloudConferenceRole_Participant;
         bobSessionSettings.address = bobCloudSettings.username;
         bobSessionSettings.displayName = bobCloudSettings.username;
         bob->conferenceConnector->setSessionSettings(bobSession, bobSessionSettings);

         CloudConferenceSessionMediaSettings bobSessionMedia;
         bobSessionMedia.audioDirection = ConferenceConnector::MediaDirection_RecvOnly;
         bobSessionMedia.videoDirection = ConferenceConnector::MediaDirection_None;
         bob->conferenceConnector->setSessionMediaSettings(bobSession, bobSessionMedia);

         bob->conferenceConnector->startSession(bobSession);

         try
         {
            {
               // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
               ConferenceConnectorHandle conn;
               ConferenceSessionStatusChangedEvent args;
               EXPECT_TRUE(cpcExpectEvent(bob->conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
               EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connecting);
            }

            {
               // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
               ConferenceConnectorHandle conn;
               ConferenceSessionStatusChangedEvent args;
               EXPECT_TRUE(cpcExpectEvent(bob->conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
               EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
               EXPECT_EQ(args.audio.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_RecvOnly);
               EXPECT_EQ(args.screenshare.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
               EXPECT_EQ(args.video.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
               //bob->video->setIncomingVideoRenderTarget(args.screenshare.mediaStreamId, hwndBobRemote);
            }

            bool canLeave = false;
            while (!canLeave)
            {
               ConferenceConnectorHandle conn;
               ConferenceParticipantListUpdatedEvent args;
               if (cpcWaitForEvent(bob->conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceParticipantListUpdated", 1000, CPCAPI2::test::AlwaysTruePred(), conn, args))
               {
                  if (args.conference == bobScreenShare)
                  {
                     safeCout("onConferenceParticipantListUpdated for our conference (wait to leave)");
                     bool someoneHasTheFloor = false;
                     for (const CloudConferenceParticipantInfo& ccpi : args.participantList)
                     {
                        if (ccpi.hasFloor)
                        {
                           someoneHasTheFloor = true;
                        }
                     }
                     if (!someoneHasTheFloor)
                     {
                        canLeave = true;
                     }
                  }
               }
            }

            bob->conferenceConnector->endSession(bobSession);

            //std::this_thread::sleep_for(std::chrono::milliseconds(5000));
            //std::this_thread::sleep_for(std::chrono::milliseconds(5000));
            //std::this_thread::sleep_for(std::chrono::milliseconds(5000));

            //{
            //   // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            //   ConferenceConnectorHandle conn;
            //   ConferenceParticipantListUpdatedEvent args;
            //   EXPECT_TRUE(cpcExpectEvent(bob->conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceParticipantListUpdated", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            //}

            //std::this_thread::sleep_for(std::chrono::seconds(180));
            //bob->conferenceConnector->endSession(bobSession);

            //{
            //   // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            //   ConferenceConnectorHandle conn;
            //   ConferenceSessionStatusChangedEvent args;
            //   EXPECT_TRUE(cpcExpectEvent(bob->conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 180000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            //   EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
            //}

            //safeCout("BOB HANGS UP");
            //bob->video->setIncomingVideoRenderTarget(NULL);
         }
         catch (...)
         {
         }
      }));
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   for (auto& bobEvent : bobEvents) {
      bobEvent.wait_for(std::chrono::milliseconds(35000));
      bobEvent.get();
   }

   for (TestAccount* testAcct : testAccounts) {
      delete testAcct;
   }

   reactorFac->shutdown();

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
}

TEST_F(ScreenshareLiveServerTests, DISABLED_AudioConferenceScalabilityTest_cpclientapi)
{
   const cpc::string SCREENSHARE_SERVER = "cpclientapi.softphone.com";
   //const cpc::string SCREENSHARE_SERVER = "**************:18090";
   std::vector<std::future<void> > bobEvents;
   std::vector<TestAccount*> testAccounts;
   // Alice is a client SDK (screenshare presenter)
   TestAccount alice("alice", Account_Init);

   CPCAPI2::Media::MediaTransportsReactorFactory* reactorFac = CPCAPI2::Media::MediaTransportsReactorFactory::create();
   reactorFac->initialize();

   CPCAPI2::Phone* masterPhone = CPCAPI2::PhoneInternal::create(0);
   masterPhone->initialize(LicenseInfo(), (CPCAPI2::PhoneHandler*)NULL, false);
   resip::MultiReactor* externalLogger = &dynamic_cast<PhoneInterface*>(masterPhone)->getSdkLoggerThread();
   CPCAPI2::Media::MediaManager* masterMediaManager = dynamic_cast<CPCAPI2::Media::MediaManager*>(CPCAPI2::Media::MediaManagerInternal::getInterface(masterPhone, reactorFac));
   CPCAPI2::Media::MediaStackSettings mediaStackSettings;
   mediaStackSettings.audioLayer = CPCAPI2::Media::AudioLayers_Dummy;
   masterMediaManager->initializeMediaStack(mediaStackSettings);

   for (int i = 0; i < 5; i++) {
      bobEvents.emplace_back(std::async(std::launch::async, [&, i]() {
         std::this_thread::sleep_for(std::chrono::seconds(i));
         // Bob is a client SDK (screenshare participant)
         CPCAPI2::Phone* testPhone = CPCAPI2::PhoneInterface::create(dynamic_cast<CPCAPI2::PhoneInternal*>(masterPhone), externalLogger);
         LicenseInfo licenseInfo;
         licenseInfo.licenseKey = "";
         licenseInfo.licenseDocumentLocation = "";
         licenseInfo.licenseAor = "";
         testPhone->initialize(licenseInfo, (CPCAPI2::PhoneHandler*)NULL, false);
         testPhone->setLogLevel(CPCAPI2::LogLevel_None);
         dynamic_cast<PhoneInterface*>(testPhone)->registerInterface("MediaManagerInterface", dynamic_cast<PhoneModule*>(masterMediaManager));
         TestAccount* bob = new TestAccount("bob", Account_Init, true, testPhone, reactorFac);
         testAccounts.push_back(bob);

         //ASSERT_EQ(joinUrlFu.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
         std::string joinUrlFromAlice = "https://cpclientapi.softphone.com/screenshare/AA/jgeras-test1"; //joinUrlFu.get();
         //std::string joinUrlFromAlice = "http://**************:18090/confbridge/<EMAIL>/";

         ConferenceConnectorHandle bobConfConn = bob->conferenceConnector->createConferenceConnector();
         ConferenceConnectorSettings bobCloudSettings;
         bobCloudSettings.authServerUrl = "https://auth.softphone.com";
         bobCloudSettings.authServerApiKey = "-----BEGIN PUBLIC KEY-----\n"
            "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEZzshRNe4gMUuqNBwrE6leU/N5vW8\n"
            "+HW/pZw5XfsxvzEtUlGwp+A9sNedJNUtjYD/2gFFY/nFuM4Gmnu6zeteTQ==\n"
            "-----END PUBLIC KEY-----";
         bobCloudSettings.orchestrationServerUrl = ""; // not needed, we use the joinUrl instead
         bobCloudSettings.joinUrl = joinUrlFromAlice.c_str();
         safeCout("BOB is joining URL: " + joinUrlFromAlice);
         bobCloudSettings.regionCode = "LOCAL";
         {
            resip::Data usernameStr;
            {
               resip::DataStream ds(usernameStr);
               ds << "bob-unittests-" << resip::Random::getCryptoRandomBase64(2);
            }
            bobCloudSettings.username = usernameStr.c_str();
         }
         bobCloudSettings.password = "1234";
         bob->conferenceConnector->setConnectionSettings(bobConfConn, bobCloudSettings);
         bob->conferenceConnector->connectToConferenceService(bobConfConn);

         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(bob->conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
         }

         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(bob->conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
         }

         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(bob->conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 10000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
         }

         bob->conferenceConnector->queryConferenceList(bobConfConn);
         CloudConferenceHandle bobScreenShare = (CloudConferenceHandle)(-1);

         {
            ConferenceConnectorHandle conn;
            ConferenceListUpdatedEvent args;
            ASSERT_TRUE(cpcExpectEvent(bob->conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            for (const CloudConferenceInfo& cci : args.conferenceList)
            {
               if (cci.displayName == cpc::string("root"))
               {
                  bobScreenShare = cci.conference;
                  break;
               }
            }
            ASSERT_NE(bobScreenShare, (CloudConferenceHandle)(-1));
         }

         bool canJoin = false;

         while (!canJoin)
         {
            ConferenceConnectorHandle conn;
            ConferenceParticipantListUpdatedEvent args;
            if (cpcWaitForEvent(bob->conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceParticipantListUpdated", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args))
            {
               if (args.conference == bobScreenShare)
               {
                  safeCout("onConferenceParticipantListUpdated for our conference (wait to join)");
                  if (args.participantList.size() >= 1)
                  {
                     bool someoneHasTheFloor = false;
                     for (const CloudConferenceParticipantInfo& ccpi : args.participantList)
                     {
                        if (ccpi.hasFloor)
                        {
                           someoneHasTheFloor = true;
                        }
                     }
                     if (someoneHasTheFloor)
                     {
                        canJoin = true;
                     }
                  }
               }
            }
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(i * 20));

         CloudConferenceSessionHandle bobSession = bob->conferenceConnector->createConferenceSession(bobScreenShare);

         CloudConferenceSessionSettings bobSessionSettings;
         bobSessionSettings.role = CloudConferenceRole_Participant;
         bobSessionSettings.address = bobCloudSettings.username;
         bobSessionSettings.displayName = bobCloudSettings.username;
         bob->conferenceConnector->setSessionSettings(bobSession, bobSessionSettings);

         CloudConferenceSessionMediaSettings bobSessionMedia;
         bobSessionMedia.audioDirection = ConferenceConnector::MediaDirection_RecvOnly;
         bobSessionMedia.videoDirection = ConferenceConnector::MediaDirection_None;
         bob->conferenceConnector->setSessionMediaSettings(bobSession, bobSessionMedia);

         bob->conferenceConnector->startSession(bobSession);

         try
         {
            {
               // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
               ConferenceConnectorHandle conn;
               ConferenceSessionStatusChangedEvent args;
               EXPECT_TRUE(cpcExpectEvent(bob->conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
               EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connecting);
            }

            {
               // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
               ConferenceConnectorHandle conn;
               ConferenceSessionStatusChangedEvent args;
               EXPECT_TRUE(cpcExpectEvent(bob->conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
               EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
               EXPECT_EQ(args.audio.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_RecvOnly);
               EXPECT_EQ(args.screenshare.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
               EXPECT_EQ(args.video.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
               //bob->video->setIncomingVideoRenderTarget(args.screenshare.mediaStreamId, hwndBobRemote);
            }

            bool canLeave = false;
            while (!canLeave)
            {
               ConferenceConnectorHandle conn;
               ConferenceParticipantListUpdatedEvent args;
               if (cpcWaitForEvent(bob->conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceParticipantListUpdated", 1000, CPCAPI2::test::AlwaysTruePred(), conn, args))
               {
                  if (args.conference == bobScreenShare)
                  {
                     safeCout("onConferenceParticipantListUpdated for our conference (wait to leave)");
                     bool someoneHasTheFloor = false;
                     for (const CloudConferenceParticipantInfo& ccpi : args.participantList)
                     {
                        if (ccpi.hasFloor)
                        {
                           someoneHasTheFloor = true;
                        }
                     }
                     if (!someoneHasTheFloor)
                     {
                        canLeave = true;
                     }
                  }
               }
            }

            bob->conferenceConnector->endSession(bobSession);

            //std::this_thread::sleep_for(std::chrono::milliseconds(5000));
            //std::this_thread::sleep_for(std::chrono::milliseconds(5000));
            //std::this_thread::sleep_for(std::chrono::milliseconds(5000));

            //{
            //   // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            //   ConferenceConnectorHandle conn;
            //   ConferenceParticipantListUpdatedEvent args;
            //   EXPECT_TRUE(cpcExpectEvent(bob->conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceParticipantListUpdated", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            //}

            //std::this_thread::sleep_for(std::chrono::seconds(180));
            //bob->conferenceConnector->endSession(bobSession);

            //{
            //   // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            //   ConferenceConnectorHandle conn;
            //   ConferenceSessionStatusChangedEvent args;
            //   EXPECT_TRUE(cpcExpectEvent(bob->conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 180000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            //   EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
            //}

            //safeCout("BOB HANGS UP");
            //bob->video->setIncomingVideoRenderTarget(NULL);
         }
         catch (...)
         {
         }
      }));
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   std::atomic_bool testDone(false);
   auto mediaEvent = std::async(std::launch::async, [&]() {
      while (!testDone) {
         // conf connector fires method calls it expects to be processed on app thread
         alice.mediaEvents->processNonUnitTestEvents(0);
         std::this_thread::sleep_for(std::chrono::milliseconds(20));
      }
   });

   ConferenceConnectorHandle aliceConfConn = alice.conferenceConnector->createConferenceConnector();
   ConferenceConnectorSettings aliceCloudSettings;
   aliceCloudSettings.authServerUrl = "https://auth.softphone.com";
   aliceCloudSettings.orchestrationServerUrl = "https://cpclientapi.softphone.com/jsonApi";
   aliceCloudSettings.regionCode = "LOCAL";
   aliceCloudSettings.username = "jgeras-test1";
   aliceCloudSettings.password = "1234";
   aliceCloudSettings.authServerApiKey = "-----BEGIN PUBLIC KEY-----\n"
      "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEZzshRNe4gMUuqNBwrE6leU/N5vW8\n"
      "+HW/pZw5XfsxvzEtUlGwp+A9sNedJNUtjYD/2gFFY/nFuM4Gmnu6zeteTQ==\n"
      "-----END PUBLIC KEY-----";
   aliceCloudSettings.ignoreCertVerification = true;
   alice.conferenceConnector->setConnectionSettings(aliceConfConn, aliceCloudSettings);
   alice.conferenceConnector->connectToConferenceService(aliceConfConn);

   auto aliceEvent = std::async(std::launch::async, [&]() {
      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
      }

      //alice.conferenceConnector->queryConferenceList(aliceConfConn);

      //{
      //   ConferenceConnectorHandle conn;
      //   ConferenceListUpdatedEvent args;
      //   ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
      //   ASSERT_EQ(args.conferenceList.size(), 1);
      //}

      //CloudConferenceSettings confSettings;
      //confSettings.conferenceDescription = "root";
      //confSettings.conferenceType = CloudConferenceType_AudioVideo_MCU;
      //confSettings.conferenceId = "root";
      //alice.conferenceConnector->createNewConference(aliceConfConn, confSettings);
      CloudConferenceHandle aliceScreenShare = 0;

      //{
      //   ConferenceConnectorHandle conn;
      //   ConferenceCreatedEvent args;
      //   ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceCreated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
      //   ASSERT_NE(args.conference, 0);
      //   ASSERT_NE(args.conference, -1);
      //   aliceScreenShare = args.conference;
      //}

      alice.conferenceConnector->queryConferenceList(aliceConfConn);

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 1);
         ASSERT_FALSE(args.conferenceList[0].joinUrl.empty());
         //ASSERT_GT(args.conferenceList[0].conference, 0); // handle used to create a new session
         aliceScreenShare = args.conferenceList[0].conference;
      }

      std::this_thread::sleep_for(std::chrono::seconds(15));

      CloudConferenceSessionHandle aliceSession = alice.conferenceConnector->createConferenceSession(aliceScreenShare);

      CloudConferenceSessionSettings aliceSessionSettings;
      aliceSessionSettings.role = CloudConferenceRole_Participant;
      aliceSessionSettings.addToFloor = true;
      aliceSessionSettings.address = "alice@invalid";
      aliceSessionSettings.displayName = "alice in wonderland";
      alice.conferenceConnector->setSessionSettings(aliceSession, aliceSessionSettings);

      CloudConferenceSessionMediaSettings aliceSessionMedia;
      aliceSessionMedia.audioDirection = ConferenceConnector::MediaDirection_SendRecv;
      aliceSessionMedia.videoDirection = ConferenceConnector::MediaDirection_None;
      alice.conferenceConnector->setSessionMediaSettings(aliceSession, aliceSessionMedia);

      alice.conferenceConnector->startSession(aliceSession);

      try
      {
         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connecting);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(10000));

         safeCout("ALICE ENDS SESSION");
         alice.conferenceConnector->endSession(aliceSession);

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
         }
         //{
         //   // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
         //   ConferenceConnectorHandle conn;
         //   ConferenceSessionStatusChangedEvent args;
         //   EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         //   EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
         //}

         {
            ConferenceConnectorHandle conn;
            ConferenceEndedEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceEnded", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(args.conference, aliceScreenShare);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         alice.conferenceConnector->destroyConferenceConnector(aliceConfConn);



      }
      catch (...)
      {
      }

   });


   for (auto& bobEvent : bobEvents) {
      bobEvent.wait_for(std::chrono::milliseconds(35000));
      bobEvent.get();
   }

   aliceEvent.wait_for(std::chrono::milliseconds(60000));
   aliceEvent.get();

   for (TestAccount* testAcct : testAccounts) {
      delete testAcct;
   }

   reactorFac->shutdown();

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
}

TEST_F(ScreenshareLiveServerTests, DISABLED_ChangePresenter_screenshare1)
{
   //const cpc::string SCREENSHARE_SERVER = "screenshare1.bria-x.net";
   const cpc::string SCREENSHARE_SERVER = "localhost:18090";

   LoginResultEvent loginResult; loginResult.success = true;
   cpc::vector<cpc::string> permissions; permissions.push_back("*");

   // Alice is a client SDK (screenshare presenter)
   TestAccount alice("alice");

   // Bob is a client SDK (screenshare participant)
   TestAccount bob("bob");

   // enable H.264
   
   alice.video->queryCodecList();
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);
   
   bob.video->queryCodecList();
   bob.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);

   alice.video->setIncomingVideoRenderTarget(NULL);
   bob.video->setIncomingVideoRenderTarget(NULL);

   alice.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   bob.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   alice.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   bob.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   HWND hwndAliceRemoteVid = NULL;
   HWND hwndBobRemoteVid = NULL;
   HWND hwndAliceRemoteVid2 = NULL;
   HWND hwndBobRemoteVid2 = NULL;
   HWND hwndAliceRemote = NULL;
   HWND hwndBobRemote = NULL;
   HWND hwndBobRemoteLarge = NULL;
#if _WIN32
   ASSERT_EQ(0, ViECreateWindow(hwndAliceRemoteVid, 712, 0, 352, 288, "Alice (incoming video)"));
   alice.video->startCapture();
   alice.video->setIncomingVideoRenderTarget(hwndAliceRemoteVid);

   ASSERT_EQ(0, ViECreateWindow(hwndBobRemoteVid, 1068, 0, 352, 288, "Bob (incoming video)"));
   bob.video->startCapture();
   bob.video->setIncomingVideoRenderTarget(hwndBobRemoteVid);

   ASSERT_EQ(0, ViECreateWindow(hwndAliceRemoteVid2, 1424, 0, 352, 288, "Alice (incoming video 2)"));

   ASSERT_EQ(0, ViECreateWindow(hwndBobRemoteVid2, 1780, 0, 352, 288, "Bob (incoming video 2)"));

   ASSERT_EQ(0, ViECreateWindow(hwndAliceRemote, 0, 0, 352, 288, "Alice (incoming)"));

   ASSERT_EQ(0, ViECreateWindow(hwndBobRemote, 356, 0, 352, 288, "Bob (incoming)"));

   ASSERT_EQ(0, ViECreateWindow(hwndBobRemoteLarge, 0, 292, 704, 576, "Bob (incoming large)"));

#else // _WIN32
   // CPCAPI2::Media::VideoExt::getInterface(alice.media)->startScreenshare();
#endif

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->setMediaEnabled(aliceCall, SipConversation::MediaType_Video, true);
   alice.conversation->start(aliceCall);

   SipConversationHandle bobCall = 0;
   {
      SipConversationHandle h;
      NewConversationEvent args;
      cpcExpectEvent(bob.conversationEvents, "SipConversationHandler::onNewConversation", 15000, CPCAPI2::test::AlwaysTruePred(), h, args);
      bobCall = h;
   }

   // Bob sends 180 ringing -- this should trigger state transitions for both Bob and Alice
   ASSERT_EQ(bob.conversation->sendRingingResponse(bobCall), kSuccess);

   {
      SipConversationHandle h;
      ConversationStateChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
         "SipConversationHandler::onConversationStateChanged",
         5000,
         HandleEqualsPred<SipConversationHandle>(bobCall),
         h, evt));
      ASSERT_EQ(evt.conversationState, ConversationState_LocalRinging);
   }

   bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);

   // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
   ASSERT_EQ(bob.conversation->accept(bobCall), kSuccess);

   {
      SipConversationHandle h;
      ConversationMediaChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
         "SipConversationHandler::onConversationMediaChanged",
         15000,
         HandleEqualsPred<SipConversationHandle>(bobCall),
         h, evt));
      ASSERT_EQ(evt.localMediaInfo.size(), 2);
      ASSERT_EQ(evt.localMediaInfo[0].mediaType, MediaType_Audio);
      ASSERT_EQ(evt.localMediaInfo[0].mediaDirection, MediaDirection_SendReceive);
   }

   {
      SipConversationHandle h;
      ConversationStateChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
         "SipConversationHandler::onConversationStateChanged",
         5000,
         HandleEqualsPred<SipConversationHandle>(bobCall),
         h, evt));
      ASSERT_EQ(evt.conversationState, ConversationState_Connected);
   }

   std::this_thread::sleep_for(std::chrono::seconds(2));
   alice.video->setIncomingVideoRenderTarget(hwndAliceRemoteVid2);
   bob.video->setIncomingVideoRenderTarget(hwndBobRemoteVid2);

   //std::this_thread::sleep_for(std::chrono::seconds(2));

   ConferenceConnectorHandle aliceConfConn = alice.conferenceConnector->createConferenceConnector();
   ConferenceConnectorSettings aliceCloudSettings;
   aliceCloudSettings.authServerUrl = "https://cloudsdk3.bria-x.net:18082";
   aliceCloudSettings.authServerApiKey = "-----BEGIN PUBLIC KEY-----\n"
      "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEZzshRNe4gMUuqNBwrE6leU/N5vW8\n"
      "+HW/pZw5XfsxvzEtUlGwp+A9sNedJNUtjYD/2gFFY/nFuM4Gmnu6zeteTQ==\n"
      "-----END PUBLIC KEY-----";
   aliceCloudSettings.orchestrationServerUrl = "http://" + SCREENSHARE_SERVER + "/jsonApi";
   aliceCloudSettings.regionCode = "LOCAL";
   {
      resip::Data usernameStr;
      {
         resip::DataStream ds(usernameStr);
         ds << "alice-unittests-" << resip::Random::getCryptoRandomBase64(2);
      }
      aliceCloudSettings.username = usernameStr.c_str();
   }
   aliceCloudSettings.password = "1234";
   alice.conferenceConnector->setConnectionSettings(aliceConfConn, aliceCloudSettings);
   alice.conferenceConnector->connectToConferenceService(aliceConfConn);

   std::promise<std::string> joinUrlPr;
   std::future<std::string> joinUrlFu = joinUrlPr.get_future();

   auto aliceEvent = std::async(std::launch::async, [&]() {
      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 30000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
      }

      alice.conferenceConnector->queryConferenceList(aliceConfConn);

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 0);
      }

      CloudConferenceSettings confSettings;
      confSettings.conferenceDescription = "test";
      confSettings.conferenceType = CloudConferenceType_Screenshare;
      confSettings.conferenceId = "screenshare";
      alice.conferenceConnector->createNewConference(aliceConfConn, confSettings);
      CloudConferenceHandle aliceScreenShare = 0;

      {
         ConferenceConnectorHandle conn;
         ConferenceCreatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceCreated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_GT(args.conference, 0);
         aliceScreenShare = args.conference;
      }

      alice.conferenceConnector->queryConferenceList(aliceConfConn);

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 1);
         ASSERT_FALSE(args.conferenceList[0].joinUrl.empty());
         joinUrlPr.set_value(args.conferenceList[0].joinUrl.c_str());
         ASSERT_GT(args.conferenceList[0].conference, 0); // handle used to create a new session
      }

      CloudConferenceSessionHandle aliceSession = alice.conferenceConnector->createConferenceSession(aliceScreenShare);

      CloudConferenceSessionSettings aliceSessionSettings;
      aliceSessionSettings.role = CloudConferenceRole_Host;
      alice.conferenceConnector->setSessionSettings(aliceSession, aliceSessionSettings);

      CloudConferenceSessionMediaSettings aliceSessionMedia;
      aliceSessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
      aliceSessionMedia.videoDirection = ConferenceConnector::MediaDirection_SendRecv;
      aliceSessionMedia.remoteVideoRenderSurface = hwndAliceRemote;
      aliceSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
      alice.conferenceConnector->setSessionMediaSettings(aliceSession, aliceSessionMedia);

      alice.conferenceConnector->startSession(aliceSession);

      try
      {
         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connecting);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(45000));

         safeCout("ALICE PASSES THE BATON");
         alice.conferenceConnector->endSession(aliceSession);

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
         }

         CloudConferenceHandle bobConf;
         {
            ConferenceConnectorHandle conn;
            ConferenceListUpdatedEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 60000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(args.conferenceList.size(), 1);
            bobConf = args.conferenceList[0].conference;
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(3000));


         CloudConferenceSessionHandle alicePartSession = alice.conferenceConnector->createConferenceSession(bobConf);

         CloudConferenceSessionSettings alicePartSessionSettings;
         alicePartSessionSettings.role = CloudConferenceRole_Participant;
         alice.conferenceConnector->setSessionSettings(alicePartSession, alicePartSessionSettings);

         CloudConferenceSessionMediaSettings alicePartSessionMedia;
         alicePartSessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
         alicePartSessionMedia.videoDirection = ConferenceConnector::MediaDirection_RecvOnly;
         alicePartSessionMedia.remoteVideoRenderSurface = hwndAliceRemote;
         alicePartSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
         alice.conferenceConnector->setSessionMediaSettings(alicePartSession, alicePartSessionMedia);

         alice.conferenceConnector->startSession(alicePartSession);

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connecting);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(30000));
         alice.conferenceConnector->endSession(alicePartSession);

      }
      catch (...)
      {
      }

   });

   std::shared_ptr<std::promise<int> > bobPr(new std::promise<int>());
   std::future<int> bobFu = bobPr->get_future();
   auto bobEvent = std::async(std::launch::async, [&, bobPr]() {
      ASSERT_EQ(joinUrlFu.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
      std::string joinUrlFromAlice = joinUrlFu.get();

      ConferenceConnectorHandle bobConfConn = bob.conferenceConnector->createConferenceConnector();
      ConferenceConnectorSettings bobCloudSettings;
      bobCloudSettings.authServerUrl = "https://cloudsdk3.bria-x.net:18082";
      bobCloudSettings.authServerApiKey = "-----BEGIN PUBLIC KEY-----\n"
         "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEZzshRNe4gMUuqNBwrE6leU/N5vW8\n"
         "+HW/pZw5XfsxvzEtUlGwp+A9sNedJNUtjYD/2gFFY/nFuM4Gmnu6zeteTQ==\n"
         "-----END PUBLIC KEY-----";
      bobCloudSettings.orchestrationServerUrl = ""; // not needed, we use the joinUrl instead
      bobCloudSettings.joinUrl = joinUrlFromAlice.c_str();
      safeCout("BOB is joining URL: " + joinUrlFromAlice);
      bobCloudSettings.regionCode = "LOCAL";
      {
         resip::Data usernameStr;
         {
            resip::DataStream ds(usernameStr);
            ds << "bob-unittests-" << resip::Random::getCryptoRandomBase64(2);
         }
         bobCloudSettings.username = usernameStr.c_str();
      }
      bobCloudSettings.password = "1234";
      bob.conferenceConnector->setConnectionSettings(bobConfConn, bobCloudSettings);
      bob.conferenceConnector->connectToConferenceService(bobConfConn);

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
      }

      bob.conferenceConnector->queryConferenceList(bobConfConn);
      CloudConferenceHandle bobScreenShare;

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 1);
         bobScreenShare = args.conferenceList[0].conference;
      }

      CloudConferenceSessionHandle bobSession = bob.conferenceConnector->createConferenceSession(bobScreenShare);

      CloudConferenceSessionSettings bobSessionSettings;
      bobSessionSettings.role = CloudConferenceRole_Participant;
      bob.conferenceConnector->setSessionSettings(bobSession, bobSessionSettings);

      CloudConferenceSessionMediaSettings bobSessionMedia;
      bobSessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
      bobSessionMedia.videoDirection = ConferenceConnector::MediaDirection_RecvOnly;
      bobSessionMedia.remoteVideoRenderSurface = hwndBobRemote;
      bobSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
      bob.conferenceConnector->setSessionMediaSettings(bobSession, bobSessionMedia);

      bob.conferenceConnector->startSession(bobSession);

      try
      {
         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connecting);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
            EXPECT_EQ(args.screenshare.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_RecvOnly);
            EXPECT_EQ(args.audio.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
            EXPECT_EQ(args.video.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
            EXPECT_NE(args.screenshare.mediaStreamId, -1);
            //bob.video->setIncomingVideoRenderTarget(args.screenshare.mediaStreamId, hwndBobRemote);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 180000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
         }
         safeCout("BOB GETS THE BATON");

         CloudConferenceSettings bobConfSettings;
         bobConfSettings.conferenceDescription = "bob test";
         bobConfSettings.conferenceType = CloudConferenceType_Screenshare;
         bobConfSettings.conferenceId = "bob_screenshare";
         bob.conferenceConnector->createNewConference(bobConfConn, bobConfSettings);
         CloudConferenceHandle bobPresenterScreenShare = 0;

         {
            ConferenceConnectorHandle conn;
            ConferenceCreatedEvent args;
            ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceCreated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_GT(args.conference, 0);
            bobPresenterScreenShare = args.conference;
         }

         bob.conferenceConnector->queryConferenceList(bobConfConn);

         {
            ConferenceConnectorHandle conn;
            ConferenceListUpdatedEvent args;
            ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(args.conferenceList.size(), 1);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         CloudConferenceSessionHandle bobHostSession = bob.conferenceConnector->createConferenceSession(bobPresenterScreenShare);

         CloudConferenceSessionSettings bobHostSessionSettings;
         bobHostSessionSettings.role = CloudConferenceRole_Host;
         bob.conferenceConnector->setSessionSettings(bobHostSession, bobHostSessionSettings);

         CloudConferenceSessionMediaSettings bobHostSessionMedia;
         bobHostSessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
         bobHostSessionMedia.videoDirection = ConferenceConnector::MediaDirection_SendRecv;
         bobHostSessionMedia.remoteVideoRenderSurface = hwndBobRemote;
         bobHostSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
         bob.conferenceConnector->setSessionMediaSettings(bobHostSession, bobHostSessionMedia);

         bob.conferenceConnector->startSession(bobHostSession);

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connecting);
         }

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
            EXPECT_EQ(args.screenshare.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_SendRecv);
            EXPECT_EQ(args.audio.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
            EXPECT_EQ(args.video.mediaDirection, CPCAPI2::ConferenceConnector::MediaDirection_None);
            EXPECT_NE(args.screenshare.mediaStreamId, -1);
         }

         //safeCout("BOB HANGS UP");
         //bob.conversation->end(bobCall);
         //alice.video->setIncomingVideoRenderTarget(NULL);
         //bob.video->setIncomingVideoRenderTarget(NULL);

         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceParticipantListUpdatedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceParticipantListUpdated", 180000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            //EXPECT_EQ(args.participantList.size(), 0);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(30000));

         bob.conferenceConnector->endSession(bobHostSession);
         {
            // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 180000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
         }

         safeCout("BOB HANGS UP");
         bob.conversation->end(bobCall);
         alice.video->setIncomingVideoRenderTarget(NULL);
         bob.video->setIncomingVideoRenderTarget(NULL);


         //std::this_thread::sleep_for(std::chrono::milliseconds(10000));
         //safeCout("BOB ENDS SESSION");
         //bob.conferenceConnector->endSession(bobSession);
      }
      catch (...)
      {
      }
      //{
      //   // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
      //   ConferenceConnectorHandle conn;
      //   ConferenceSessionStatusChangedEvent args;
      //   ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
      //   ASSERT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
      //}
      bobPr->set_value(0);
   });

   aliceEvent.wait_for(std::chrono::milliseconds(45000));
   aliceEvent.get();

   bobEvent.wait_for(std::chrono::milliseconds(45000));
   bobEvent.get();
   //bobFu.get();

   //waitFor2(aliceEvent, maiaEvent);

   alice.video->stopCapture();
   bob.video->stopCapture();

#if _WIN32
   DestroyWindow(hwndAliceRemoteVid);
   DestroyWindow(hwndBobRemoteVid);
   DestroyWindow(hwndAliceRemote);
   DestroyWindow(hwndBobRemote);
   DestroyWindow(hwndBobRemoteLarge);
#endif // _WIN32

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
}

TEST_F(ScreenshareLiveServerTests, DISABLED_CreatePersistentConference_externalServer)
{
   //const cpc::string SCREENSHARE_SERVER = "cloudsdk-dev.bria-x.net";
   //const cpc::string SCREENSHARE_SERVER = "cloudsdk5.bria-x.net";
   //const cpc::string SCREENSHARE_SERVER = "ss1-na-northeast1a.softphone.com";
   //const cpc::string SCREENSHARE_SERVER = "ss2-na-uscentra1a.softphone.com"; 
   //const cpc::string SCREENSHARE_SERVER = "ss3-eu-west2c.softphone.com";
   const cpc::string SCREENSHARE_SERVER = "cpclientapi.softphone.com:18090";
   //const cpc::string SCREENSHARE_SERVER = "ss-staging.softphone.com";
   //const cpc::string SCREENSHARE_SERVER = "ptt-east-1.softphone.com";
   //const cpc::string SCREENSHARE_SERVER = "***********:18090";

   LoginResultEvent loginResult; loginResult.success = true;
   cpc::vector<cpc::string> permissions; permissions.push_back("*");

   // Alice is a client SDK (screenshare presenter)
   TestAccount alice("alice");

   // Bob is a client SDK (screenshare participant)
   TestAccount bob("bob");

   // enable H.264
   alice.video->queryCodecList();
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);

   bob.video->queryCodecList();
   bob.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);

   alice.video->setIncomingVideoRenderTarget(NULL);
   bob.video->setIncomingVideoRenderTarget(NULL);

   alice.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   bob.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   alice.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   bob.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   HWND hwndAliceRemoteVid = NULL;
   HWND hwndBobRemoteVid = NULL;
   HWND hwndAliceRemote = NULL;
   HWND hwndBobRemote = NULL;
   HWND hwndBobRemoteLarge = NULL;
#if _WIN32
   ASSERT_EQ(0, ViECreateWindow(hwndAliceRemoteVid, 712, 0, 352, 288, "Alice (incoming video)"));
   //alice.video->startCapture();
   alice.video->setIncomingVideoRenderTarget(hwndAliceRemoteVid);

   ASSERT_EQ(0, ViECreateWindow(hwndBobRemoteVid, 1068, 0, 352, 288, "Bob (incoming video)"));
   //bob.video->startCapture();
   bob.video->setIncomingVideoRenderTarget(hwndBobRemoteVid);

   ASSERT_EQ(0, ViECreateWindow(hwndAliceRemote, 0, 0, 352, 288, "Alice (incoming)"));

   ASSERT_EQ(0, ViECreateWindow(hwndBobRemote, 356, 0, 352, 288, "Bob (incoming)"));

   ASSERT_EQ(0, ViECreateWindow(hwndBobRemoteLarge, 0, 292, 1056, 864, "Bob (incoming large)"));

#else // _WIN32
   // CPCAPI2::Media::VideoExt::getInterface(alice.media)->startScreenshare();
#endif

   //TestScreenshareDeviceListHandler tsdlh;
   //CPCAPI2::Media::VideoExt::getInterface(alice.media)->queryScreenshareDeviceList(&tsdlh, true, false);

   std::atomic_bool testDone(false);
   auto mediaEvent = std::async(std::launch::async, [&]() {
      while (!testDone) {
         // conf connector fires method calls it expects to be processed on app thread
         alice.mediaEvents->processNonUnitTestEvents(0);
         bob.mediaEvents->processNonUnitTestEvents(0);
         std::this_thread::sleep_for(std::chrono::milliseconds(20));
      }
   });

   //CPCAPI2::Media::VideoExt::getInterface(alice.media)->setScreenshareCaptureDevice(tsdlh.devices().begin()->deviceId);

   //std::this_thread::sleep_for(std::chrono::seconds(2));

   ConferenceConnectorHandle aliceConfConn = alice.conferenceConnector->createConferenceConnector();
   ConferenceConnectorSettings aliceCloudSettings;
   aliceCloudSettings.authServerUrl = "https://ss.softphone.com";// "https://cloudsdk5.bria-x.net:18082";// "https://cpclientapi.softphone.com:18082";
   aliceCloudSettings.authServerApiKey = "-----BEGIN PUBLIC KEY-----\n"
      "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEZzshRNe4gMUuqNBwrE6leU/N5vW8\n"
      "+HW/pZw5XfsxvzEtUlGwp+A9sNedJNUtjYD/2gFFY/nFuM4Gmnu6zeteTQ==\n"
      "-----END PUBLIC KEY-----";
   aliceCloudSettings.orchestrationServerUrl = "https://" + SCREENSHARE_SERVER + "/jsonApi";
   //aliceCloudSettings.joinUrl = "https://" + SCREENSHARE_SERVER + "/screenshare/testBSTA-ABCE" + cpc::string(resip::Random::getCryptoRandomBase64(2).c_str());
   //aliceCloudSettings.joinUrl = "https://ss-staging.softphone.com/screenshare/pWgHJsycgGc7eqFFc8UZCjtNmR/conf/EGHL";
   aliceCloudSettings.regionCode = "LOCAL";

   resip::Data usernameStr;
   {
      {
         resip::DataStream ds(usernameStr);
         ds << "alice-unittests-" << resip::Random::getCryptoRandomBase64(2);
         ds << "@example.com";
      }
      aliceCloudSettings.username = usernameStr.c_str();
   }
   aliceCloudSettings.password = "1234";
   //aliceCloudSettings.cont ext = "SdkTeamMeeting";
   alice.conferenceConnector->setConnectionSettings(aliceConfConn, aliceCloudSettings);
   alice.conferenceConnector->connectToConferenceService(aliceConfConn);

   std::promise<std::string> joinUrlPr;
   std::future<std::string> joinUrlFu = joinUrlPr.get_future();

   auto aliceEvent = std::async(std::launch::async, [&]() {
      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 30000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
      }

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
      }

      alice.conferenceConnector->queryConferenceList(aliceConfConn);

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 0);
      }

      {
         CloudConferenceSettings confSettings;
         confSettings.conferenceDescription = aliceCloudSettings.username;
         confSettings.conferenceType = CloudConferenceType_Screenshare;
         //confSettings.conferenceId = "screenshare";
         confSettings.persistent = true;
         alice.conferenceConnector->createNewConference(aliceConfConn, confSettings);
         CloudConferenceHandle aliceScreenShare = 0;

         {
            ConferenceConnectorHandle conn;
            ConferenceCreatedEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceCreated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_GT(args.conference, 0);
            aliceScreenShare = args.conference;
         }

         safeCout("conference created with handle " << aliceScreenShare);

         alice.conferenceConnector->queryConferenceList(aliceConfConn);

         cpc::string persistentUrl;
         {
            ConferenceConnectorHandle conn;
            ConferenceListUpdatedEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_GT(args.conferenceList.size(), 0);
            ASSERT_FALSE(args.conferenceList[0].joinUrl.empty());
            CloudConferenceInfo cci;
            for (auto conf : args.conferenceList)
            {
               if (conf.conference == aliceScreenShare)
               {
                  cci = conf;
                  break;
               }
            }
            persistentUrl = cci.joinUrl;
            ASSERT_GT(cci.conference, 0); // handle used to create a new session
         }
         alice.conferenceConnector->destroyConference(aliceScreenShare);

         alice.conferenceConnector->disconnectFromConferenceService(aliceConfConn);
         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 30000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Disconnecting, args.connectionStatus);
         }
         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 30000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Disconnected, args.connectionStatus);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(1000));
         aliceConfConn = alice.conferenceConnector->createConferenceConnector();
         alice.conferenceConnector->setConnectionSettings(aliceConfConn, aliceCloudSettings);

         alice.conferenceConnector->connectToConferenceService(aliceConfConn);

         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 30000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
         }

         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
         }

         {
            ConferenceConnectorHandle conn;
            ServiceConnectionStatusEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
         }

         alice.conferenceConnector->queryConferenceList(aliceConfConn);

         {
            ConferenceConnectorHandle conn;
            ConferenceListUpdatedEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(args.conferenceList.size(), 0);
         }

         {
            CloudConferenceSettings confSettings;
            confSettings.conferenceDescription = aliceCloudSettings.username;
            confSettings.conferenceType = CloudConferenceType_Screenshare;
            confSettings.conferenceId = persistentUrl;
            confSettings.persistent = true;
            alice.conferenceConnector->createNewConference(aliceConfConn, confSettings);
            CloudConferenceHandle aliceScreenShare = 0;

            {
               ConferenceConnectorHandle conn;
               ConferenceCreatedEvent args;
               ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceCreated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
               ASSERT_GT(args.conference, 0);
               aliceScreenShare = args.conference;
            }

            safeCout("conference created with handle " << aliceScreenShare);

            alice.conferenceConnector->queryConferenceList(aliceConfConn);

            {
               ConferenceConnectorHandle conn;
               ConferenceListUpdatedEvent args;
               ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
               ASSERT_GT(args.conferenceList.size(), 0);
               ASSERT_FALSE(args.conferenceList[0].joinUrl.empty());
               CloudConferenceInfo cci;
               for (auto conf : args.conferenceList)
               {
                  if (conf.conference == aliceScreenShare)
                  {
                     cci = conf;
                     ASSERT_EQ(persistentUrl, cci.joinUrl);
                     break;
                  }
               }
               ASSERT_GT(cci.conference, 0); // handle used to create a new session
            }

            alice.conferenceConnector->disconnectFromConferenceService(aliceConfConn);
            {
               ConferenceConnectorHandle conn;
               ServiceConnectionStatusEvent args;
               ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 30000, CPCAPI2::test::AlwaysTruePred(), conn, args));
               ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Disconnecting, args.connectionStatus);
            }
            {
               ConferenceConnectorHandle conn;
               ServiceConnectionStatusEvent args;
               ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 30000, CPCAPI2::test::AlwaysTruePred(), conn, args));
               ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Disconnected, args.connectionStatus);
            }
         }
      }
   });

   std::shared_ptr<std::promise<int> > bobPr(new std::promise<int>());
   std::future<int> bobFu = bobPr->get_future();
   auto bobEvent = std::async(std::launch::async, [&, bobPr]() {

   });

   aliceEvent.wait_for(std::chrono::milliseconds(180000));
   aliceEvent.get();

   bobEvent.wait_for(std::chrono::milliseconds(45000));
   bobEvent.get();
   //bobFu.get();

   //waitFor2(aliceEvent, maiaEvent);

   alice.video->stopCapture();
   bob.video->stopCapture();

   testDone = true;

#if _WIN32
   DestroyWindow(hwndAliceRemoteVid);
   DestroyWindow(hwndBobRemoteVid);
   DestroyWindow(hwndAliceRemote);
   DestroyWindow(hwndBobRemote);
   DestroyWindow(hwndBobRemoteLarge);
#endif // _WIN32

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
}
#endif

// bliu: better run with --gtest_repeat=N since some expectation would fail and the test shouldn't continue
TEST_F(ScreenshareLiveServerTests, DISABLED_LoadTest)
{
#define USE_SS2 1
#if USE_SS2
   const cpc::string SCREENSHARE_SERVER = "ss2-staging.softphone.com";
#else
   const cpc::string SCREENSHARE_SERVER = "127.0.0.1";
#endif
   const int numTestEndpoints = 4;
   const size_t MaxIterations = 100;

   size_t currentIteration = 1;

   HWND hwndAliceRemote = 0;
   ASSERT_EQ(0, ViECreateWindow(hwndAliceRemote, 0, 0, 352, 288, "Alice (incoming)"));

   TestAccount alice("alice");

   // enable H.264
   alice.video->queryCodecList();
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);
   alice.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   std::vector<std::unique_ptr<TestAccount>> accounts;
   std::mutex accountsMutex;

   std::vector<std::future<void>> accountsReady;

   for (int i = 0; i < numTestEndpoints; i++) accountsReady.emplace_back(std::async(std::launch::async, [&, i](){
      auto bob = std::make_unique<TestAccount>("bob" + std::to_string(i));
      std::lock_guard<std::mutex> lock (accountsMutex);
      accounts.emplace_back(std::move(bob));
   }));

   for (auto& f : accountsReady) f.wait();

   std::atomic_bool testDone = false;
   auto mediaEvent = std::async(std::launch::async, [&]() {
      while (!testDone) {
         // conf connector fires method calls it expects to be processed on app thread
         alice.mediaEvents->processNonUnitTestEvents(0);

         for (auto& a : accounts) a->mediaEvents->processNonUnitTestEvents(0);

         std::this_thread::sleep_for(std::chrono::milliseconds(20));
      }
   });

   auto progressEvent = std::async(std::launch::async, [&]() {
      while (!testDone) {
         safeCout("Current iteration=" << currentIteration);
         std::this_thread::sleep_for(std::chrono::seconds(1));
      }
   });

   std::shared_ptr<void> testDoneGuard {
      NULL,
      [&](void*){
         testDone = true;
         mediaEvent.wait();
         progressEvent.wait();
      }
   };

   for (; currentIteration <= MaxIterations; ++ currentIteration)
   {
      std::this_thread::sleep_for(std::chrono::milliseconds(10)); // to avoid reactor queue build up and crash due to deleted modules

#if 1 // connector
      ConferenceConnectorHandle aliceConfConn = alice.createConferenceConnector();
      ConferenceConnectorSettings aliceCloudSettings;

#if USE_SS2
      aliceCloudSettings.authServerUrl =  "https://cloudsdk5.bria-x.net:18082";// "https://cpclientapi.softphone.com:18082";
      aliceCloudSettings.authServerApiKey =
         "-----BEGIN PUBLIC KEY-----\n"
         "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEZzshRNe4gMUuqNBwrE6leU/N5vW8\n"
         "+HW/pZw5XfsxvzEtUlGwp+A9sNedJNUtjYD/2gFFY/nFuM4Gmnu6zeteTQ==\n"
         "-----END PUBLIC KEY-----";
      aliceCloudSettings.orchestrationServerUrl = "https://" + SCREENSHARE_SERVER + "/jsonApi";
      //aliceCloudSettings.joinUrl = "";
      aliceCloudSettings.regionCode = "LOCAL";
      aliceCloudSettings.username = "<EMAIL>";
      aliceCloudSettings.password = "user24";
#else
      aliceCloudSettings.authServerUrl = "https://127.0.0.1:18084";
      aliceCloudSettings.orchestrationServerUrl = "https://127.0.0.1:18082/jsonApi";
      aliceCloudSettings.regionCode = "LOCAL";
      aliceCloudSettings.username = "user1";
      aliceCloudSettings.password = "1234";
      aliceCloudSettings.ignoreCertVerification = true;
      aliceCloudSettings.authServerApiKey = "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiKzxgDrW3u9+XzGN4r/GI5o+BMtty+63OT4lwEgFlkpedq5FY3eCzI4lB1WXDVip0OpCM1zYoCh6mqi7KaS1Vg==";
#endif
      alice.conferenceConnector->setConnectionSettings(aliceConfConn, aliceCloudSettings);
      alice.conferenceConnector->connectToConferenceService(aliceConfConn);

      {
         ConferenceConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 30000, test::HandleEqualsPred(aliceConfConn), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);

         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 10000, test::HandleEqualsPred(aliceConfConn), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);

         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 10000, test::HandleEqualsPred(aliceConfConn), conn, args));
         ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
      }
#endif

#if 1 // screen sharing
      CloudConferenceSettings confSettings;
      confSettings.conferenceDescription = "LoadTest";
      confSettings.conferenceType = CloudConferenceType_Screenshare;
      alice.conferenceConnector->createNewConference(aliceConfConn, confSettings);

      CloudConferenceHandle aliceScreenShare;

      {
         ConferenceConnectorHandle conn;
         ConferenceCreatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceCreated", 10000, test::HandleEqualsPred(aliceConfConn), conn, args));
         ASSERT_GT(args.conference, 0);
         aliceScreenShare = args.conference;
      }

      CloudConferenceSessionHandle aliceSession = alice.conferenceConnector->createConferenceSession(aliceScreenShare);

      CloudConferenceSessionSettings aliceSessionSettings;
      aliceSessionSettings.role = CloudConferenceRole_Host;
      alice.conferenceConnector->setSessionSettings(aliceSession, aliceSessionSettings);

      CloudConferenceSessionMediaSettings aliceSessionMedia;
      aliceSessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
      aliceSessionMedia.videoDirection = ConferenceConnector::MediaDirection_SendRecv;
      aliceSessionMedia.remoteVideoRenderSurface = hwndAliceRemote;
      aliceSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
      alice.conferenceConnector->setSessionMediaSettings(aliceSession, aliceSessionMedia);

      alice.conferenceConnector->startSession(aliceSession);

      {
         ConferenceConnectorHandle conn;
         ConferenceSessionStatusChangedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 10000, test::HandleEqualsPred(aliceConfConn), conn, args));
         ASSERT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connecting);

         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, test::HandleEqualsPred(aliceConfConn), conn, args));
         ASSERT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
      }

      alice.conferenceConnector->queryConferenceList(aliceConfConn);

      cpc::string joinUrl;
      cpc::string displayName;

      {
         ConferenceConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 10000, test::HandleEqualsPred(aliceConfConn), conn, args));
         ASSERT_TRUE(!args.conferenceList.empty());

         for (auto c : args.conferenceList)
         {
            if (c.conference != aliceScreenShare) continue;
            joinUrl = c.joinUrl;
            displayName = c.displayName;
            safeCout("ALICE STARTED SESSION joinUrl=" << joinUrl);
            break;
         }
      }

      while (true)
      {
         alice.conferenceConnector->queryParticipantList(aliceScreenShare);

         {
            ConferenceConnectorHandle conn;
            ConferenceParticipantListUpdatedEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceParticipantListUpdated", 30000, test::HandleEqualsPred(aliceConfConn), conn, args));
            if (args.conference == aliceScreenShare && !args.participantList.empty()) break;
         }

         std::this_thread::sleep_for(std::chrono::seconds(1));
      }

      safeCout("ALICE onConferenceParticipantListUpdated");

#if 1 // viewers
      std::vector<std::future<void>> waitEvents;

      safeCout("Starting connection phase with " << numTestEndpoints << " endpoint(s)");

      for (int i = 0; i < numTestEndpoints; i++) waitEvents.emplace_back(std::async(std::launch::async, [&accounts, i, joinUrl, displayName, aliceScreenShare]()
      {
         auto& bob = *accounts[i];
         ConferenceConnectorHandle connector = bob.createConferenceConnector();
         ConferenceConnectorSettings connectorSettings;

#if USE_SS2
         connectorSettings.authServerUrl = "https://auth.softphone.com";
         connectorSettings.authServerApiKey =
            "-----BEGIN PUBLIC KEY-----\n"
            "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEZzshRNe4gMUuqNBwrE6leU/N5vW8\n"
            "+HW/pZw5XfsxvzEtUlGwp+A9sNedJNUtjYD/2gFFY/nFuM4Gmnu6zeteTQ==\n"
            "-----END PUBLIC KEY-----";
         connectorSettings.orchestrationServerUrl = ""; // not needed, we use the joinUrl instead
         connectorSettings.joinUrl = joinUrl;
         connectorSettings.regionCode = "LOCAL";
         connectorSettings.username = ("load-test-user-" + resip::Random::getCryptoRandomBase64(2)).c_str();
         connectorSettings.password = "1234";
#else
         connectorSettings.authServerUrl = "https://127.0.0.1:18084";
         connectorSettings.orchestrationServerUrl = "https://127.0.0.1:18082/jsonApi";
         connectorSettings.regionCode = "LOCAL";
         connectorSettings.username = "user1";
         connectorSettings.password = "1234";
         connectorSettings.ignoreCertVerification = true;
         connectorSettings.authServerApiKey = "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiKzxgDrW3u9+XzGN4r/GI5o+BMtty+63OT4lwEgFlkpedq5FY3eCzI4lB1WXDVip0OpCM1zYoCh6mqi7KaS1Vg==";
#endif
         bob.conferenceConnector->setConnectionSettings(connector, connectorSettings);

         bob.conferenceConnector->connectToConferenceService(connector);
         safeCout("Viewer " << i << " connecting to " << joinUrl.c_str());

         {
            ConferenceConnectorHandle h;
            ServiceConnectionStatusEvent evt;

            ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, test::HandleEqualsPred(connector), h, evt));
            ASSERT_EQ(evt.connectionStatus, ConferenceConnector::ServiceConnectionStatus_Connecting);

            ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, test::HandleEqualsPred(connector), h, evt));
            ASSERT_EQ(evt.connectionStatus, ConferenceConnector::ServiceConnectionStatus_Authenticating);

            ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, test::HandleEqualsPred(connector), h, evt));
            ASSERT_EQ(evt.connectionStatus, ConferenceConnector::ServiceConnectionStatus_Connected);
         }

         safeCout("Viewer " << i << " connected to " << joinUrl.c_str());

         bob.conferenceConnector->queryConferenceList(connector);

         {
            ConferenceConnectorHandle h;
            ConferenceListUpdatedEvent evt;
            CloudConferenceHandle conference;
            ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 15000, test::HandleEqualsPred(connector), h, evt));
            ASSERT_TRUE(!evt.conferenceList.empty());
            ASSERT_TRUE(std::find_if(evt.conferenceList.begin(), evt.conferenceList.end(), [&](const CloudConferenceInfo& info) {
               bool result = displayName == info.displayName;
               if (result) conference = info.conference;
               return result;
            }) != evt.conferenceList.end());
            ASSERT_EQ(conference, aliceScreenShare);
         }

         CloudConferenceSessionHandle viewer = bob.conferenceConnector->createConferenceSession(aliceScreenShare);

         CloudConferenceSessionSettings viewerSettings;
         viewerSettings.role = CloudConferenceRole_Participant;
         viewerSettings.address = connectorSettings.username;
         viewerSettings.displayName = connectorSettings.username;
         bob.conferenceConnector->setSessionSettings(viewer, viewerSettings);

         CloudConferenceSessionMediaSettings viewerMedia;
         viewerMedia.audioDirection = ConferenceConnector::MediaDirection_None;
         viewerMedia.videoDirection = ConferenceConnector::MediaDirection_RecvOnly;
         bob.conferenceConnector->setSessionMediaSettings(viewer, viewerMedia);

         bob.conferenceConnector->startSession(viewer);

         safeCout("Viewer " << i << " started session");

         {
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;

            ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, test::HandleEqualsPred(connector), conn, args));
            ASSERT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connecting);

            ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, test::HandleEqualsPred(connector), conn, args));
            ASSERT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connected);
         }

         safeCout("Viewer " << i << " connected to session");

         // allow the session to last for 5 seconds
         std::this_thread::sleep_for(std::chrono::seconds(5));

         bob.conferenceConnector->endSession(viewer);

         {
            ConferenceConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, test::HandleEqualsPred(connector), conn, args));
            ASSERT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
            ASSERT_EQ(args.session, viewer);
         }

         bob.conferenceConnector->disconnectFromConferenceService(connector);

         {
            ConferenceConnectorHandle h;
            ServiceConnectionStatusEvent evt;

            ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, test::HandleEqualsPred(connector), h, evt));
            ASSERT_EQ(evt.connectionStatus, ConferenceConnector::ServiceConnectionStatus_Disconnecting);

            ASSERT_TRUE(cpcExpectEvent(bob.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, test::HandleEqualsPred(connector), h, evt));
            ASSERT_EQ(evt.connectionStatus, ConferenceConnector::ServiceConnectionStatus_Disconnected);
         }

         bob.conferenceConnector->destroyConferenceConnector(connector);

         safeCout("Viewer " << i << " ended session");
      })); // for all viewers

      waitEvents.emplace_back(std::async(std::launch::async, [](){ std::this_thread::sleep_for(std::chrono::seconds(5)); }));

      for (auto& w : waitEvents) w.wait();
#endif // viewers

      safeCout("ALICE ENDED SESSION");
      alice.conferenceConnector->endSession(aliceSession);

      {
         ConferenceConnectorHandle conn;
         ConferenceSessionStatusChangedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000, test::HandleEqualsPred(aliceConfConn), conn, args));
         ASSERT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
      }

      alice.conferenceConnector->destroyConference(aliceScreenShare);

      {
         ConferenceConnectorHandle conn;
         ConferenceEndedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onConferenceEnded", 20000, test::HandleEqualsPred(aliceConfConn), conn, args));
         ASSERT_EQ(args.conference, aliceScreenShare);
      }
#endif // screen sharing

      alice.conferenceConnector->disconnectFromConferenceService(aliceConfConn);

      {
         ConferenceConnectorHandle h;
         ServiceConnectionStatusEvent evt;

         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, test::HandleEqualsPred(aliceConfConn), h, evt));
         ASSERT_EQ(evt.connectionStatus, ConferenceConnector::ServiceConnectionStatus_Disconnecting);

         ASSERT_TRUE(cpcExpectEvent(alice.conferenceConnectorEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, test::HandleEqualsPred(aliceConfConn), h, evt));
         ASSERT_EQ(evt.connectionStatus, ConferenceConnector::ServiceConnectionStatus_Disconnected);
      }

      alice.conferenceConnector->destroyConferenceConnector(aliceConfConn);

      // bliu: this is necessary although there is no corresponding startCapture()
      alice.video->stopCapture();

      if (alice.conferenceConnectorEvents != NULL) alice.conferenceConnectorEvents->clearCommands();
      for (auto& a : accounts) if (a->conferenceConnectorEvents != NULL) a->conferenceConnectorEvents->clearCommands();
   }

   DestroyWindow(hwndAliceRemote);
}
