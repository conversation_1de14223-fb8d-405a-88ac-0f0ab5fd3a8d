#if 0

#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#include "cpcapi2_test_fixture.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>

#include "test_framework/cpcapi2_test_framework.h"
#include "test_framework/bare_bones_sip_endpoint.h"
#include "test_account_events.h"
#include "test_call_events.h"

using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::SipConversation;

namespace {

class IntercomTestAccount : public TestAccount
{
public:
   IntercomTestAccount(const std::string& name) : TestAccount(name, Account_NoInit)
   {
      if (name == "alice")
      {
         // one to one auto intercom test account
         config.settings.displayName = "**********";
         config.settings.username = "+1**********";
         config.settings.auth_username = "<EMAIL>";
         
      }
      else if (name == "bob")
      {
         // one to one intercom account, whisper enabled
         config.settings.displayName = "**********";
         config.settings.username = "+1**********";
         config.settings.auth_username = "<EMAIL>";
      }
      else if (name == "charlie")
      {
         // basic account
         config.settings.displayName = "**********";
         config.settings.username = "+1**********";
         config.settings.auth_username = "<EMAIL>";
      }
      else if (name == "dave")
      {
         // one to many intercom account
         config.settings.displayName = "**********";
         config.settings.username = "+1**********";
         config.settings.auth_username = "<EMAIL>";
         mIntercomGroupId = "40";
      }
      else if (name == "max")
      {
         // one to many intercom account
         config.settings.displayName = "**********";
         config.settings.username = "+***********";
         config.settings.auth_username = "<EMAIL>";
         mIntercomGroupId = "40";
      }
      else
      {
         assert(false);
      }
      config.settings.domain = "csa1.luqdlab.com";
      config.settings.outboundProxy = "10.30.0.25";
      config.settings.password = "newsys";
      config.settings.registrationIntervalSeconds = 3600;

      init();
      enable();
   }
   
   cpc::string getIntercomGroupId() const { return mIntercomGroupId; }

private:
   cpc::string mIntercomGroupId;
};

class IntercomCallTests : public CpcapiAutoTest
{
public:
   IntercomCallTests() {}
   virtual ~IntercomCallTests() {}

   static const cpc::string intercomServiceCode;
   static const cpc::string intercomServiceCodeGroup;
};

// standard one-to-one intercom service code
const cpc::string IntercomCallTests::intercomServiceCode = "*1234";
const cpc::string IntercomCallTests::intercomServiceCodeGroup = "*1235";

TEST_F(IntercomCallTests, OneToOneIntercomIdle) {
   IntercomTestAccount alice("alice");
   IntercomTestAccount bob("bob");

   cpc::string intercomFeatureUri = "sip:" + IntercomCallTests::intercomServiceCode + "@" + alice.config.settings.domain;

   // alice creates 1-1 intercom with bob
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, intercomFeatureUri);
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(alice, aliceCall, intercomFeatureUri);
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      // alice talks to bob
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall;
      // validate auto answer flag for incoming intercom
      assertNewConversationIncoming_ex(bob, &bobCall, alice.config.uri(), [](const NewConversationEvent& evt){
         ASSERT_EQ(true, evt.autoAnswer);
      });
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor2(aliceEvents, bobEvents);
}

TEST_F(IntercomCallTests, OneToOneIntercomBargeIn) {
   IntercomTestAccount alice("alice");
   IntercomTestAccount bob("bob");
   IntercomTestAccount charlie("charlie");

   cpc::string intercomFeatureUri = "sip:" + IntercomCallTests::intercomServiceCode + "@" + alice.config.settings.domain;

   auto aliceEvents = std::async(std::launch::async, [&] () {
      // wait for bob and charlie to establish call
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      // alice sends intercom request to bob
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->addParticipant(aliceCall, intercomFeatureUri);
      alice.conversation->start(aliceCall);
      assertNewConversationOutgoing(alice, aliceCall, intercomFeatureUri);
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      // alice talks to Bob
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      // alice ends intercom call
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   // bob creates a basic call with charlie
   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall, charlie.config.uri());
   bob.conversation->start(bobCall);

   auto bobEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(bob, bobCall, charlie.config.uri());
      assertConversationStateChanged(bob, bobCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      // bob is getting invited into a conference for intercom
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);

      SipConversationHandle aliceIntercomCall;
      // validate auto answer flag for incoming intercom
      assertNewConversationIncoming_ex(bob, &aliceIntercomCall, alice.config.uri(), [](const NewConversationEvent& evt){
         ASSERT_EQ(true, evt.autoAnswer);
      });
      assertSuccess(bob.conversation->sendRingingResponse(aliceIntercomCall));
      assertConversationStateChanged(bob, aliceIntercomCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(aliceIntercomCall));
      assertConversationMediaChanged(bob, aliceIntercomCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, aliceIntercomCall, ConversationState_Connected);

      // Alice ends the intercom call
      assertConversationEnded(bob, aliceIntercomCall, ConversationEndReason_UserTerminatedRemotely);

      // B continues talking to C
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto charlieEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle charlieCall;
      assertNewConversationIncoming(charlie, &charlieCall, bob.config.uri());
      assertSuccess(charlie.conversation->sendRingingResponse(charlieCall));
      assertConversationStateChanged(charlie, charlieCall, ConversationState_LocalRinging);
      assertSuccess(charlie.conversation->accept(charlieCall));
      assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_Connected);

      // Bob receives intercom call from Alice, Charlie is added to conference
      assertConversationMediaChangeRequest(charlie, charlieCall, MediaDirection_SendReceive); 
      assertSuccess(charlie.conversation->accept(charlieCall));
      assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);

      // Bob ends the call
      assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor3(aliceEvents, bobEvents, charlieEvents);
}

TEST_F(IntercomCallTests, OneToManyIntercom) {

   IntercomTestAccount charlie("charlie");
   IntercomTestAccount dave("dave");
   IntercomTestAccount max("max");

   cpc::string intercomFeatureUri = "sip:" + IntercomCallTests::intercomServiceCodeGroup + dave.getIntercomGroupId() + "@" + dave.config.settings.domain;

   auto maxEvents = std::async(std::launch::async, [&] () {
      // wait for dave and charlie to establish call
      std::this_thread::sleep_for(std::chrono::milliseconds(7000));
      // max sends intercom request to dave
      SipConversationHandle maxCall = max.conversation->createConversation(max.handle);
      max.conversation->addParticipant(maxCall, intercomFeatureUri);
      max.conversation->start(maxCall);
      assertNewConversationOutgoing(max, maxCall, intercomFeatureUri);
      assertConversationMediaChanged(max, maxCall, MediaDirection_SendReceive);
      assertConversationStateChanged(max, maxCall, ConversationState_Connected);
      // max talks to dave
      std::this_thread::sleep_for(std::chrono::milliseconds(7000));
      // max ends intercom call
      assertSuccess(max.conversation->end(maxCall));
      assertConversationEnded(max, maxCall, ConversationEndReason_UserTerminatedLocally);
   });


   auto daveEvents = std::async(std::launch::async, [&] () {
      // dave creates a basic call with charlie
      SipConversationHandle daveCall = dave.conversation->createConversation(dave.handle);
      dave.conversation->addParticipant(daveCall, charlie.config.uri());
      dave.conversation->start(daveCall);

      assertNewConversationOutgoing(dave, daveCall, charlie.config.uri());
      assertConversationStateChanged(dave, daveCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(dave, daveCall, MediaDirection_SendReceive);
      assertConversationStateChanged(dave, daveCall, ConversationState_Connected);

      SipConversationHandle maxIntercomCall;
      assertNewConversationIncoming(dave, &maxIntercomCall, max.config.uri());
      // validate auto answer flag for incoming intercom
      /*assertNewConversationIncoming_ex(dave, &maxIntercomCall, max.config.uri(), [](const NewConversationEvent& evt){
         ASSERT_EQ(true, evt.autoAnswer);
      });*/
      assertSuccess(dave.conversation->sendRingingResponse(maxIntercomCall));
      assertConversationStateChanged(dave, maxIntercomCall, ConversationState_LocalRinging);
      
      // dave puts call with charlie on hold
      dave.conversation->hold(daveCall);
      assertConversationMediaChanged_ex(dave, daveCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_TRUE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendOnly, evt.localMediaInfo[0].mediaDirection);
      });

      // accept the intercom
      assertSuccess(dave.conversation->accept(maxIntercomCall));
      assertConversationMediaChanged(dave, maxIntercomCall, MediaDirection_ReceiveOnly);
      assertConversationStateChanged(dave, maxIntercomCall, ConversationState_Connected);

      assertConversationMediaChangeRequest(dave, maxIntercomCall, MediaDirection_SendReceive); 
      assertSuccess(dave.conversation->accept(maxIntercomCall));
      assertConversationMediaChanged(dave, maxIntercomCall, MediaDirection_SendReceive);

      // server adds inactive video to SDP, for some reason
      assertConversationMediaChangeRequest(dave, maxIntercomCall, MediaDirection_SendReceive); 
      assertSuccess(dave.conversation->accept(maxIntercomCall));
      assertConversationMediaChanged(dave, maxIntercomCall, MediaDirection_SendReceive);

      // max ends the intercom call
      assertConversationEnded(dave, maxIntercomCall, ConversationEndReason_UserTerminatedRemotely);

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      // Dave resumes previous call with Charlie
      dave.conversation->unhold(daveCall);
      assertConversationMediaChanged_ex(dave, daveCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      assertSuccess(dave.conversation->end(daveCall));
      assertConversationEnded(dave, daveCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto charlieEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle charlieCall;
      assertNewConversationIncoming(charlie, &charlieCall, dave.config.uri());
      assertSuccess(charlie.conversation->sendRingingResponse(charlieCall));
      assertConversationStateChanged(charlie, charlieCall, ConversationState_LocalRinging);
      assertSuccess(charlie.conversation->accept(charlieCall));
      assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_Connected);

      // Receiving hold request
      assertConversationMediaChangeRequest(charlie, charlieCall, MediaDirection_SendOnly); 
      assertSuccess(charlie.conversation->accept(charlieCall));
      assertConversationMediaChanged(charlie, charlieCall, MediaDirection_ReceiveOnly);

      // Call with Dave resumed
      assertConversationMediaChangeRequest(charlie, charlieCall, MediaDirection_SendReceive); 
      assertSuccess(charlie.conversation->accept(charlieCall));
      assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);

      // Dave ends the call
      assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor3(daveEvents, maxEvents, charlieEvents);
}

}

#endif // 0
