
#Specifies that the latest microsoft/iis image will be used as the base image

#for jenkins (remote), use servercore image, for local machines use windows
#FROM mcr.microsoft.com/windows/servercore:ltsc2019
FROM mcr.microsoft.com/windows:20H2

#visual C++ redist (2015) - remove later if unnecessary
ADD https://download.microsoft.com/download/6/A/A/6AA4EDFF-645B-48C5-81CC-ED5963AEAD48/vc_redist.x86.exe /vc_redist.x86.exe
# to install python
ADD https://aka.ms/nugetclidl /nuget.exe

COPY Dockerfile.windows-run.bat .

RUN C:\vc_redist.x86.exe /quiet /install
RUN C:\nuget.exe install python -ExcludeVersion -Version 3.9.7
 
ENTRYPOINT ["Dockerfile.windows-run.bat"]

CMD ["--gtest_list_tests"]