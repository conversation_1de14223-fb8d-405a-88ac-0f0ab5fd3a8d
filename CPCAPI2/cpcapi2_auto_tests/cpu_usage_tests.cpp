

// CPU usage tests on linux docker runs are not reliable;
// e.g. much higher CPU usage seen if these tests are run
// at the very start of the test run
#if !(defined( __linux__ ) && !defined( ANDROID ))

#if _WIN32
#include "stdafx.h"
#include "TCHAR.h"
#include "pdh.h"
#else
#include "brand_branded.h"
#endif

#ifdef __APPLE__
#include <mach/mach_init.h>
#include <mach/mach_error.h>
#include <mach/mach_host.h>
#include <mach/vm_map.h>
#include "utils/mac_proc_utils.h"
#endif

#ifdef _WIN32
#include "utils/win_proc_utils.h"
#endif

#ifdef __linux__
#include <sys/times.h>
#endif

#include "rutil/ConfigParse.hxx"

#include "cpcapi2_test_fixture.h"
#include "test_call_events.h"
#include "test_events.h"

#include <iostream>
#include <vector>
#include <map>
#include <numeric>
#include <iomanip>
#include <filesystem>

using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::SipConversation;
using namespace CPCAPI2::SipAccount;

class MyConfigParse : public resip::ConfigParse
{
private:
   void parseCommandLine(int argc, char** argv, int skipCount = 0) {}
   void printHelpText(int argc, char **argv) {}
};

class CPUUsageTests : public CpcapiAutoTest
{
public:
   CPUUsageTests() {}
   virtual ~CPUUsageTests() {}

   void currentTestPerDeviceMaxCpuUsage(const cpc::string& deviceIdentifier, int& averageCpuMax);
   void logCurrentTestAverageCpuUsage(const cpc::string& deviceIdentifier, double averageCpu, const cpc::string& testName);
   void audioCallUsage(std::string codec, std::string statsCodec);
   void videoCallUsage(std::string codec, std::string statsCodec);

#ifdef __APPLE__
   void currentTestPerDeviceMaxEnergyUsage(const cpc::string& deviceIdentifier, int& averageEnergyMax);
   void logCurrentTestAverageEnergyUsage(const cpc::string& deviceIdentifier, double averageEnergy, double averageCpu, const cpc::string& testName);
   void audioCallEnergyUsage(std::string codec, std::string statsCodec);
   void videoCallEnergyUsage(std::string codec, std::string statsCodec);
#endif
};

/*
class CPUUsageTestsStatsManager
{
public:
   static std::shared_ptr<CPUUsageTestsStatsManager> instance();
   virtual~ CPUUsageTestsStatsManager();
private:
   CPUUsageTestsStatsManager();
   static std::weak_ptr<CPUUsageTestsStatsManager> _instance;
};

std::weak_ptr<CPUUsageTestsStatsManager> CPUUsageTestsStatsManager::_instance;

std::shared_ptr<CPUUsageTestsStatsManager> CPUUsageTestsStatsManager::instance()
{
   std::shared_ptr<CPUUsageTestsStatsManager> instance = _instance.lock();

   if (!instance)
   {
      instance.reset(new CPUUsageTestsStatsManager());
      _instance = instance;
   }
   return instance;
}
*/

class CPUUsageTestsStatsManager
{
public:
   static CPUUsageTestsStatsManager* instance();
   virtual~ CPUUsageTestsStatsManager();
   std::map<std::string, std::vector<double>> cpuStats;
   std::map<std::string, std::vector<double>> energyStats;
private:
   CPUUsageTestsStatsManager();
   static CPUUsageTestsStatsManager* _instance;
   static std::mutex _mutex;
};

CPUUsageTestsStatsManager* CPUUsageTestsStatsManager::_instance = NULL;
std::mutex CPUUsageTestsStatsManager::_mutex;

CPUUsageTestsStatsManager* CPUUsageTestsStatsManager::instance()
{
   if (!_instance)
   {
      std::lock_guard<std::mutex> lock(_mutex);
      if (!_instance)
      {
         _instance = new CPUUsageTestsStatsManager();
      }
   }
   return _instance;
}

CPUUsageTestsStatsManager::CPUUsageTestsStatsManager()
{
}

CPUUsageTestsStatsManager::~CPUUsageTestsStatsManager()
{
}

void CPUUsageTests::currentTestPerDeviceMaxCpuUsage(const cpc::string& deviceIdentifier, int& averageCpuMax)
{
   cpc::string identifer = deviceIdentifier;
#if defined(_WIN32) && defined(_M_X64)
   identifer += "_x64";
#endif

   MyConfigParse fileConfig;
   std::stringstream testConfigName;
   testConfigName << TestEnvironmentConfig::testResourcePath().c_str()
   << ::testing::UnitTest::GetInstance()->current_test_case()->name() << "."
   << ::testing::UnitTest::GetInstance()->current_test_info()->name() << ".config";
   fileConfig.parseConfig(0, NULL, resip::Data(testConfigName.str().c_str()));

   std::stringstream configString;
   ASSERT_FALSE(identifer.empty());
   configString << "max_average_cpu_usage_for_device_" << identifer.c_str();
   ASSERT_TRUE(fileConfig.getConfigValue(configString.str().c_str(), averageCpuMax)) << "Couldn't find a max CPU usage value specified in "
   << testConfigName.str() << " for device id: " << identifer.c_str()
   << ". You likely need to manually add an entry to this file."
   << " Average CPU use for this test was " << averageCpuMax;
}

void CPUUsageTests::logCurrentTestAverageCpuUsage(const cpc::string& deviceIdentifier, double averageCpu, const cpc::string& testName)
{
   cpc::string identifer = deviceIdentifier;
#if defined(_WIN32) && defined(_M_X64)
   identifer += "_x64";
#endif

   std::stringstream resultsFilename;

   #ifdef _WIN32
   const std::string delim = "\\";
   #else
   const std::string delim = "/";
   #endif

   resultsFilename << "logs" << delim << ::testing::UnitTest::GetInstance()->current_test_case()->name() << "."
   << ::testing::UnitTest::GetInstance()->current_test_info()->name() << "_results.csv";
   std::ofstream resultsFile;
   resultsFile.open(resultsFilename.str(), std::ios::trunc);
   if (resultsFile.is_open())
   {
      const std::string comma(",");
      resultsFile << testName << comma << "deviceIdentifier" << std::endl;
      resultsFile << averageCpu << comma << identifer << std::endl;
   }

   resultsFile.close();
}

struct CpuUsageInfo
{
   double currentProcCpuUsage;
   // double allProcCpuUsage; // out of number of logical cores * 100
};

struct UsageConfiguration
{
#ifdef WIN32
   PDH_HQUERY cpuQuery;
   PDH_HCOUNTER processTotal;
   //PDH_HCOUNTER cpuTotal;
#elif __APPLE__
   MacCpuUsageMonitoring macCpuUsageMonitoring;
   MacEnergyUsageMonitoring macEnergyUsageMonitoring;
#elif __linux__
   clock_t lastTime;
   clock_t lastProcessUserTime;
   clock_t lastProcessSystemTime;
#endif
};

void setupUsageCheck(UsageConfiguration& config)
{
#ifdef WIN32
   //Initialization of CPU measurment variables
   PdhOpenQuery(NULL, NULL, &config.cpuQuery);
   PdhAddCounter(config.cpuQuery, "\\Process(cpcapi2_auto_tests)\\% Processor Time", NULL, &config.processTotal);
   //PdhAddCounter(config.cpuQuery, "\\Processor(_Total)\\% Processor Time", NULL, &config.cpuTotal);
#elif defined(__linux__)
   tms t;
   config.lastTime = times(&t);
   config.lastProcessUserTime = t.tms_utime;
   config.lastProcessSystemTime = t.tms_stime;
#endif
}

void cleanupUsageCheck(UsageConfiguration& config)
{
#ifdef WIN32
   PdhRemoveCounter(config.processTotal);
   //PdhRemoveCounter(config.cpuTotal);
   PdhCloseQuery(config.cpuQuery);
#endif
}

CpuUsageInfo getCurrentCpuUsage(UsageConfiguration& config)
{
   CpuUsageInfo info;
#ifdef WIN32
   PDH_FMT_COUNTERVALUE processVal;
   //PDH_FMT_COUNTERVALUE cpuVal;

   PdhCollectQueryData(config.cpuQuery);
   PdhGetFormattedCounterValue(config.processTotal, PDH_FMT_DOUBLE | PDH_FMT_NOCAP100, NULL, &processVal);
   //PdhGetFormattedCounterValue(config.cpuTotal, PDH_FMT_DOUBLE, NULL, &cpuVal);
   info.currentProcCpuUsage = processVal.doubleValue;
   //info.allProcCpuUsage = cpuVal.doubleValue;
#elif __APPLE__
   info.currentProcCpuUsage = config.macCpuUsageMonitoring.getCurrentProcessCPUUsage();
   //info.allProcCpuUsage = config.macCpuUsageMonitoring.getAllProcessesCPUUsage();
#elif defined(__linux)
   tms t;
   clock_t now = times(&t);

   info.currentProcCpuUsage = (((t.tms_utime + config.lastProcessUserTime) + (t.tms_stime - config.lastProcessSystemTime)) * 100) / (double)(now - config.lastTime);

   config.lastTime = now;
   config.lastProcessUserTime = t.tms_utime;
   config.lastProcessSystemTime = t.tms_stime;
#endif
   return info;
}

void runOriginalRepro()
{
   const char* const reproArgs[] = { "" };
   resip::Data configFile = (TestEnvironmentConfig::testResourcePath() + "repro.config").c_str();
   ReproHolder::instance()->run(1, reproArgs, configFile);
}

void CPUUsageTests::audioCallUsage(std::string codec, std::string statsCodec)
{
#if _WIN32
   if (TestEnvironmentConfig::dockerContainerized()) GTEST_SKIP(); // UNIT_TEST_WIN_DOCKER_SKIP
#endif

   const unsigned int duration = 30000;

#ifdef _WIN32
   WinPerf winPerf;

   if (TestEnvironmentConfig::modifyPowerSchemeAsNeeded())
   {
      ASSERT_TRUE(winPerf.saveCurrentPowerProfile());
      ASSERT_TRUE(winPerf.setHighPerfPowerProfile());
   }
#endif

   ReproHolder::destroyInstance();

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.useRegistrar = false;
   alice.config.settings.domain = "bogus.invalid";
   alice.config.settings.outboundProxy = "";
   alice.config.settings.sourceAddress = "127.0.0.1";
   alice.init();
   alice.enable();

   TestAccount bob("bob", Account_NoInit);

   resip::Uri bobTarget;
   bobTarget.user() = bob.config.settings.username;
   bobTarget.host() = "127.0.0.1";
   bobTarget.port() = 5055;

   bob.config.settings.useRegistrar = false;
   bob.config.settings.minSipPort = bobTarget.port();
   bob.config.settings.maxSipPort = bobTarget.port();
   bob.config.settings.sourceAddress = "127.0.0.1";
   bob.init();
   bob.enable();

   alice.enableOnlyThisCodec(codec.c_str());
   bob.enableOnlyThisCodec(codec.c_str());

   // Conversation creation
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bobTarget.getAOR(true).c_str());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]()
   {
      SipConversationHandle h;
      NewConversationEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
         "SipConversationHandler::onNewConversation",
         5000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(evt.account, alice.handle);

      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      SipConversationState aliceConvState;
      alice.conversationState->getState(aliceCall, aliceConvState);
      ASSERT_NE(aliceConvState.localMediaInfo.size(), 0);
      ASSERT_TRUE(resip::isEqualNoCase(resip::Data(aliceConvState.localMediaInfo[0].audioCodec.plname), statsCodec.c_str()));

      std::this_thread::sleep_for(std::chrono::milliseconds(duration));
      assertAudioFlowing(alice, aliceCall);
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
      assertCallHadAudio(alice, aliceCall);
   });

   auto bobEvents = std::async(std::launch::async, [&]()
   {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      SipConversationState bobConvState;
      bob.conversationState->getState(bobCall, bobConvState);
      ASSERT_NE(bobConvState.localMediaInfo.size(), 0);
      ASSERT_TRUE(resip::isEqualNoCase(resip::Data(bobConvState.localMediaInfo[0].audioCodec.plname), statsCodec.c_str()));

      UsageConfiguration queryConfig;
      setupUsageCheck(queryConfig);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertAudioFlowing(bob, bobCall);

      getCurrentCpuUsage(queryConfig);
      std::this_thread::sleep_for(std::chrono::milliseconds(duration));

      CpuUsageInfo currentValue = getCurrentCpuUsage(queryConfig);

      {
         SipConversationHandle h;
         ConversationEndedEvent evt;
         ASSERT_TRUE(bob.conversationEvents->expectEvent("SipConversationHandler::onConversationEnded",
            50000, HandleEqualsPred<SipConversationHandle>(bobCall), h, evt));
         ASSERT_EQ(ConversationEndReason_UserTerminatedRemotely, evt.endReason);
      }
      assertCallHadAudio(bob, bobCall);

      cleanupUsageCheck(queryConfig);

      safeCout("CPU usage by CPCAPI2 process during " << codec.c_str() << " audio call: " << currentValue.currentProcCpuUsage);

      ASSERT_NO_FATAL_FAILURE(logCurrentTestAverageCpuUsage(alice.phone->getInstanceId(), currentValue.currentProcCpuUsage,
         ::testing::UnitTest::GetInstance()->current_test_info()->name()));

      int averageCpuMax;
      ASSERT_NO_FATAL_FAILURE(currentTestPerDeviceMaxCpuUsage(alice.phone->getInstanceId(), averageCpuMax));
      ASSERT_LT(currentValue.currentProcCpuUsage, averageCpuMax) << " cpu-usage: " << currentValue.currentProcCpuUsage << " max cpu-usage: " << averageCpuMax;
   });

   std::this_thread::sleep_for(std::chrono::milliseconds(duration));
   waitFor2(aliceEvents, bobEvents);

   runOriginalRepro();
}

TEST_F(CPUUsageTests, AudioCallCpuUsageOpus)
{
   audioCallUsage("OPUS", "opus");
}

#if CPCAPI2_BRAND_CODEC_G729
TEST_F(CPUUsageTests, AudioCallCpuUsageG729)
{
   audioCallUsage("G.729", "G729");
}
#endif // #if CPCAPI2_BRAND_CODEC_G729

TEST_F(CPUUsageTests, AudioCallCpuUsageG722)
{
   audioCallUsage("G.722", "G722");
}

TEST_F(CPUUsageTests, AudioCallCpuUsageG711)
{
   audioCallUsage("G711 uLaw", "PCMU");
}

void CPUUsageTests::videoCallUsage(std::string codec, std::string statsCodec)
{
#if _WIN32
   if (TestEnvironmentConfig::dockerContainerized()) GTEST_SKIP(); // UNIT_TEST_WIN_DOCKER_SKIP
#endif

   const unsigned int duration = 30000;

#ifdef _WIN32
   WinPerf winPerf;

   if (TestEnvironmentConfig::modifyPowerSchemeAsNeeded())
   {
      ASSERT_TRUE(winPerf.saveCurrentPowerProfile());
      ASSERT_TRUE(winPerf.setHighPerfPowerProfile());
   }
#endif

   ReproHolder::destroyInstance();

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.useRegistrar = false;
   alice.config.settings.domain = "bogus.invalid";
   alice.config.settings.outboundProxy = "";
   alice.config.settings.sourceAddress = "127.0.0.1";
   alice.init();
   alice.enable();

   TestAccount bob("bob", Account_NoInit);

   resip::Uri bobTarget;
   bobTarget.user() = bob.config.settings.username;
   bobTarget.host() = "127.0.0.1";
   bobTarget.port() = 5055;

   bob.config.settings.useRegistrar = false;
   bob.config.settings.minSipPort = bobTarget.port();
   bob.config.settings.maxSipPort = bobTarget.port();
   bob.config.settings.sourceAddress = "127.0.0.1";
   bob.init();
   bob.enable();

   CPCAPI2::Media::MediaStackSettings mediaStackSettings;
   mediaStackSettings.audioLayer = CPCAPI2::Media::AudioLayers_Dummy;
   alice.media->updateMediaSettings(mediaStackSettings);
   bob.media->updateMediaSettings(mediaStackSettings);

   alice.enableOnlyThisVideoCodec(codec.c_str());
   bob.enableOnlyThisVideoCodec(codec.c_str());

   alice.enableOnlyThisCodec("OPUS");
   bob.enableOnlyThisCodec("OPUS");

   alice.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_HD_1920x1080p);
   bob.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_HD_1920x1080p);

   alice.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_HD_1920x1080p);
   bob.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_HD_1920x1080p);

#ifndef __linux__
   CpTestWindowHandle hwndAliceCapture = NULL;
   CpTestWindowHandle nswindowAliceCaptureWindow = NULL;
   ASSERT_EQ(0, ViECreateWindow(hwndAliceCapture, 0, 0, 352, 288, "Alice (capture)", nswindowAliceCaptureWindow));
   alice.video->startCapture();
   if (TestEnvironmentConfig::drawLocalVideo())
   {
      alice.video->setLocalVideoRenderTarget(hwndAliceCapture);
   }

   CpTestWindowHandle hwndAliceRemote = NULL;
   CpTestWindowHandle nswindowAliceRemoteWindow = NULL;
   ASSERT_EQ(0, ViECreateWindow(hwndAliceRemote, 356, 0, 352, 288, "Alice (incoming)", nswindowAliceRemoteWindow));
   if (TestEnvironmentConfig::drawLocalVideo())
   {
      alice.video->setIncomingVideoRenderTarget(hwndAliceRemote);
   }

   CpTestWindowHandle hwndBobCapture = NULL;
   CpTestWindowHandle nswindowBobCaptureWindow = NULL;
   ASSERT_EQ(0, ViECreateWindow(hwndBobCapture, 0, 292, 352, 288, "Bob (capture)", nswindowBobCaptureWindow));
   bob.video->startCapture();
   if (TestEnvironmentConfig::drawLocalVideo())
   {
      bob.video->setLocalVideoRenderTarget(hwndBobCapture);
   }

   CpTestWindowHandle hwndBobRemote = NULL;
   CpTestWindowHandle nswindowBobRemoteWindow = NULL;
   ASSERT_EQ(0, ViECreateWindow(hwndBobRemote, 356, 292, 352, 288, "Bob (incoming)", nswindowBobRemoteWindow));
   if (TestEnvironmentConfig::drawLocalVideo())
   {
      bob.video->setIncomingVideoRenderTarget(hwndBobRemote);
   }
#endif

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, true);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
   alice.conversation->addParticipant(aliceCall, bobTarget.getAOR(true).c_str());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]()
   {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));

      SipConversationHandle h;
      NewConversationEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
         "SipConversationHandler::onNewConversation",
         5000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(evt.account, alice.handle);

      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt)
      {
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      SipConversationState aliceConvState;
      alice.conversationState->getState(aliceCall, aliceConvState);
      ASSERT_EQ(aliceConvState.localMediaInfo.size(), 2);
      ASSERT_TRUE(resip::isEqualNoCase(resip::Data(aliceConvState.localMediaInfo[0].audioCodec.plname), "opus"));
      ASSERT_TRUE(resip::isEqualNoCase(resip::Data(aliceConvState.localMediaInfo[1].videoCodec.plName), statsCodec.c_str()));

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      UsageConfiguration queryConfig;
      setupUsageCheck(queryConfig);
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      getCurrentCpuUsage(queryConfig);
      std::this_thread::sleep_for(std::chrono::milliseconds(duration));

      CpuUsageInfo currentValue = getCurrentCpuUsage(queryConfig);

      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertCallHadMedia(alice, aliceCall, true, true);

      cleanupUsageCheck(queryConfig);

      safeCout("Average CPU usage by CPCAPI2 process during video call: " << currentValue.currentProcCpuUsage);

      ASSERT_NO_FATAL_FAILURE(logCurrentTestAverageCpuUsage(alice.phone->getInstanceId(), currentValue.currentProcCpuUsage,
      ::testing::UnitTest::GetInstance()->current_test_info()->name()));

      int averageCpuMax;
      ASSERT_NO_FATAL_FAILURE(currentTestPerDeviceMaxCpuUsage(alice.phone->getInstanceId(), averageCpuMax));
      ASSERT_LT(currentValue.currentProcCpuUsage, averageCpuMax) << " cpu-usage: " << currentValue.currentProcCpuUsage << " max cpu-usage: " << averageCpuMax;
   });

   auto bobEvents = std::async(std::launch::async, [&]()
   {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      SipConversationHandle bobCall;
      assertNewConversationIncoming_ex(bob, &bobCall, alice.config.uri(), [](const NewConversationEvent& evt){
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
      });

      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt)
      {
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

     SipConversationState bobConvState;
     bob.conversationState->getState(bobCall, bobConvState);
     ASSERT_EQ(bobConvState.localMediaInfo.size(), 2);
     ASSERT_TRUE(resip::isEqualNoCase(resip::Data(bobConvState.localMediaInfo[0].audioCodec.plname), "opus"));
     ASSERT_TRUE(resip::isEqualNoCase(resip::Data(bobConvState.localMediaInfo[1].videoCodec.plName), statsCodec.c_str()));

      std::this_thread::sleep_for(std::chrono::milliseconds(duration + 7000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      assertCallHadMedia(bob, bobCall, true, true);
   });

   std::this_thread::sleep_for(std::chrono::milliseconds(duration));
   waitFor2(aliceEvents, bobEvents);

#ifndef __linux__
   alice.video->stopCapture();
   alice.video->setLocalVideoRenderTarget(NULL);
   ViEDestroyWindow(nswindowAliceCaptureWindow, hwndAliceCapture);
   ViEDestroyWindow(nswindowAliceRemoteWindow, hwndAliceRemote);
   bob.video->stopCapture();
   bob.video->setLocalVideoRenderTarget(NULL);
   ViEDestroyWindow(nswindowBobCaptureWindow, hwndBobCapture);
   ViEDestroyWindow(nswindowBobRemoteWindow, hwndBobRemote);
#endif
}

// Requires ManyCam; if you don't have it, define CPCAPI2_AUTO_TESTS_NO_VIDEO_STATS
// to get a clean run.
TEST_F(CPUUsageTests, VideoCallCpuUsageH264)
{
   SCOPED_TRACE(__FUNCTION__);
   videoCallUsage("H.264", "H264");
}

TEST_F(CPUUsageTests, VideoCallCpuUsageH264_NoVideoRender)
{
   SCOPED_TRACE(__FUNCTION__);
   const bool wasDrawingLocalVideo = TestEnvironmentConfig::drawLocalVideo();
   TestEnvironmentConfig::setDrawLocalVideo(false);
   videoCallUsage("H.264", "H264");
   TestEnvironmentConfig::setDrawLocalVideo(wasDrawingLocalVideo);
}

TEST_F(CPUUsageTests, VideoCallCpuUsageVP8)
{
   SCOPED_TRACE(__FUNCTION__);
   videoCallUsage("VP8", "VP8");
}

TEST_F(CPUUsageTests, VideoCallCpuUsageVP8_NoVideoRender)
{
   SCOPED_TRACE(__FUNCTION__);
   const bool wasDrawingLocalVideo = TestEnvironmentConfig::drawLocalVideo();
   TestEnvironmentConfig::setDrawLocalVideo(false);
   videoCallUsage("VP8", "VP8");
   TestEnvironmentConfig::setDrawLocalVideo(wasDrawingLocalVideo);
}

#ifdef __APPLE__

void CPUUsageTests::currentTestPerDeviceMaxEnergyUsage(const cpc::string& deviceIdentifier, int& averageEnergyMax)
{
   cpc::string identifer = deviceIdentifier;
#if defined(_WIN32) && defined(_M_X64)
   identifer += "_x64";
#endif

   MyConfigParse fileConfig;
   std::stringstream testConfigName;
   testConfigName << TestEnvironmentConfig::testResourcePath().c_str()
      << ::testing::UnitTest::GetInstance()->current_test_case()->name() << "."
      << ::testing::UnitTest::GetInstance()->current_test_info()->name() << ".config";
   fileConfig.parseConfig(0, NULL, resip::Data(testConfigName.str().c_str()));

   std::stringstream configString;
   ASSERT_FALSE(identifer.empty());
   configString << "max_average_energy_usage_for_device_" << identifer.c_str();
   ASSERT_TRUE(fileConfig.getConfigValue(configString.str().c_str(), averageEnergyMax)) << "Couldn't find a max Energy usage value specified in "
      << testConfigName.str() << " for device id: " << identifer.c_str()
      << ". You likely need to manually add an entry to this file."
      << " Average Energy use for this test was " << averageEnergyMax;
}

void CPUUsageTests::logCurrentTestAverageEnergyUsage(const cpc::string& deviceIdentifier, double averageEnergy, double averageCpu, const cpc::string& testName)
{
   CPUUsageTestsStatsManager* mgr = CPUUsageTestsStatsManager::instance();
   cpc::string identifier = deviceIdentifier;
#if defined(_WIN32) && defined(_M_X64)
   identifier += "_x64";
#endif

   std::stringstream resultsFilename;

   #ifdef _WIN32
   const std::string delim = "\\";
   #else
   const std::string delim = "/";
   #endif

   const std::string comma(",");
   double cumulativeEnergy = 0.0;
   double cumulativeCpu = 0.0;
   std::string testname = ::testing::UnitTest::GetInstance()->current_test_info()->name();
   resultsFilename << "logs" << delim << ::testing::UnitTest::GetInstance()->current_test_case()->name() << "." << testname << "_results.csv";
   std::filesystem::create_directories("logs");
   std::ofstream resultsFile;
   int repeat = ::testing::GTEST_FLAG(repeat);
   if (repeat > 1)
   {
      if (mgr->energyStats.find(testname) == mgr->energyStats.end())
      {
         std::vector<double> energyStats;
         energyStats.push_back(averageEnergy);
         std::vector<double> cpuStats;
         cpuStats.push_back(averageCpu);
         mgr->energyStats[testname] = energyStats;
         mgr->cpuStats[testname] = cpuStats;
         resultsFile.open(resultsFilename.str().c_str(), std::ios::trunc);
         resultsFile << "deviceIdentifier" << comma << "energy" << comma << "averageEnergy" << comma << "cpu" << comma << "averageCpu" << std::endl;
      }
      else
      {
         mgr->energyStats[testname].push_back(averageEnergy);
         mgr->cpuStats[testname].push_back(averageCpu);
         resultsFile.open(resultsFilename.str(), std::ios::app);
      }
      cumulativeEnergy = std::accumulate(mgr->energyStats[testname].begin(), mgr->energyStats[testname].end(), 0.0) / mgr->energyStats[testname].size();
      cumulativeCpu = std::accumulate(mgr->cpuStats[testname].begin(), mgr->cpuStats[testname].end(), 0.0) / mgr->cpuStats[testname].size();
   }
   else
   {
      cumulativeEnergy = averageEnergy;
      cumulativeCpu = averageCpu;
      resultsFile.open(resultsFilename.str().c_str(), std::ios::trunc);
      resultsFile << "deviceIdentifier" << comma << "energy" << comma << "averageEnergy" << comma << "cpu" << comma << "averageCpu" << std::endl;
   }

   if (resultsFile.is_open())
   {
      resultsFile << identifier << comma << std::setprecision(2) << std::fixed << averageEnergy << comma << cumulativeEnergy << comma << averageCpu << comma << cumulativeCpu << std::endl;
   }
   else
   {
      safeCout("CPUUsageTests::logCurrentTestAverageEnergyUsage(): error opening csv file: " << resultsFilename.str().c_str());
   }

   resultsFile.close();
}

void CPUUsageTests::audioCallEnergyUsage(std::string codec, std::string statsCodec)
{
#if _WIN32
   if (TestEnvironmentConfig::dockerContainerized()) GTEST_SKIP(); // UNIT_TEST_WIN_DOCKER_SKIP
#endif

   const unsigned int duration = 30000;

#ifdef _WIN32
   WinPerf winPerf;

   if (TestEnvironmentConfig::modifyPowerSchemeAsNeeded())
   {
      ASSERT_TRUE(winPerf.saveCurrentPowerProfile());
      ASSERT_TRUE(winPerf.setHighPerfPowerProfile());
   }
#endif

   ReproHolder::destroyInstance();

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.useRegistrar = false;
   alice.config.settings.domain = "bogus.invalid";
   alice.config.settings.outboundProxy = "";
   alice.config.settings.sourceAddress = "127.0.0.1";
   alice.init();
   alice.enable();

   TestAccount bob("bob", Account_NoInit);

   resip::Uri bobTarget;
   bobTarget.user() = bob.config.settings.username;
   bobTarget.host() = "127.0.0.1";
   bobTarget.port() = 5055;

   bob.config.settings.useRegistrar = false;
   bob.config.settings.minSipPort = bobTarget.port();
   bob.config.settings.maxSipPort = bobTarget.port();
   bob.config.settings.sourceAddress = "127.0.0.1";
   bob.init();
   bob.enable();

   alice.enableOnlyThisCodec(codec.c_str());
   bob.enableOnlyThisCodec(codec.c_str());

   // Conversation creation
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bobTarget.getAOR(true).c_str());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]()
   {
      SipConversationHandle h;
      NewConversationEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
         "SipConversationHandler::onNewConversation",
         5000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(evt.account, alice.handle);

      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      SipConversationState aliceConvState;
      alice.conversationState->getState(aliceCall, aliceConvState);
      ASSERT_NE(aliceConvState.localMediaInfo.size(), 0);
      ASSERT_TRUE(resip::isEqualNoCase(resip::Data(aliceConvState.localMediaInfo[0].audioCodec.plname), statsCodec.c_str()));

      std::this_thread::sleep_for(std::chrono::milliseconds(duration));
      assertAudioFlowing(alice, aliceCall);
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
      assertCallHadAudio(alice, aliceCall);
   });

   auto bobEvents = std::async(std::launch::async, [&]()
   {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      SipConversationState bobConvState;
      bob.conversationState->getState(bobCall, bobConvState);
      ASSERT_NE(bobConvState.localMediaInfo.size(), 0);
      ASSERT_TRUE(resip::isEqualNoCase(resip::Data(bobConvState.localMediaInfo[0].audioCodec.plname), statsCodec.c_str()));

      UsageConfiguration queryConfig;
      setupUsageCheck(queryConfig);
      MacEnergyUsageMonitoring::EnergyData energyData;
      queryConfig.macEnergyUsageMonitoring.start();

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertAudioFlowing(bob, bobCall);

      std::this_thread::sleep_for(std::chrono::milliseconds(duration));

      {
         SipConversationHandle h;
         ConversationEndedEvent evt;
         ASSERT_TRUE(bob.conversationEvents->expectEvent("SipConversationHandler::onConversationEnded",
            50000, HandleEqualsPred<SipConversationHandle>(bobCall), h, evt));
         ASSERT_EQ(ConversationEndReason_UserTerminatedRemotely, evt.endReason);
      }
      assertCallHadAudio(bob, bobCall);

      cleanupUsageCheck(queryConfig);
      queryConfig.macEnergyUsageMonitoring.stop();
      queryConfig.macEnergyUsageMonitoring.getTaskEnergyFromTopInfo(energyData);
      safeCout("CPUUsageTests::audioCallEnergyUsage(): energy-usage: " << energyData.energyUsage << " cpu-usage: " << energyData.cpuUsage << " monitoring-duration: " << (energyData.stopTimeMsecs - energyData.startTimeMsecs) << " msecs");

      int averageEnergyMax = 0;
      ASSERT_NO_FATAL_FAILURE(logCurrentTestAverageEnergyUsage(alice.phone->getInstanceId(), energyData.energyUsage, energyData.cpuUsage,
         ::testing::UnitTest::GetInstance()->current_test_info()->name()));
      ASSERT_NO_FATAL_FAILURE(currentTestPerDeviceMaxEnergyUsage(alice.phone->getInstanceId(), averageEnergyMax));
      ASSERT_LT(energyData.energyUsage, averageEnergyMax) << " energy-usage: " << energyData.energyUsage << " cpu-usage: " << energyData.cpuUsage;

   });

   std::this_thread::sleep_for(std::chrono::milliseconds(duration));
   waitFor2(aliceEvents, bobEvents);

   runOriginalRepro();
}

void CPUUsageTests::videoCallEnergyUsage(std::string codec, std::string statsCodec)
{
#if _WIN32
   if (TestEnvironmentConfig::dockerContainerized()) GTEST_SKIP(); // UNIT_TEST_WIN_DOCKER_SKIP
#endif

   const unsigned int duration = 30000;

#ifdef _WIN32
   WinPerf winPerf;

   if (TestEnvironmentConfig::modifyPowerSchemeAsNeeded())
   {
      ASSERT_TRUE(winPerf.saveCurrentPowerProfile());
      ASSERT_TRUE(winPerf.setHighPerfPowerProfile());
   }
#endif

   ReproHolder::destroyInstance();

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.useRegistrar = false;
   alice.config.settings.domain = "bogus.invalid";
   alice.config.settings.outboundProxy = "";
   alice.config.settings.sourceAddress = "127.0.0.1";
   alice.init();
   alice.enable();

   TestAccount bob("bob", Account_NoInit);

   resip::Uri bobTarget;
   bobTarget.user() = bob.config.settings.username;
   bobTarget.host() = "127.0.0.1";
   bobTarget.port() = 5055;

   bob.config.settings.useRegistrar = false;
   bob.config.settings.minSipPort = bobTarget.port();
   bob.config.settings.maxSipPort = bobTarget.port();
   bob.config.settings.sourceAddress = "127.0.0.1";
   bob.init();
   bob.enable();

   CPCAPI2::Media::MediaStackSettings mediaStackSettings;
   mediaStackSettings.audioLayer = CPCAPI2::Media::AudioLayers_Dummy;
   alice.media->updateMediaSettings(mediaStackSettings);
   bob.media->updateMediaSettings(mediaStackSettings);

   alice.enableOnlyThisVideoCodec(codec.c_str());
   bob.enableOnlyThisVideoCodec(codec.c_str());

   alice.enableOnlyThisCodec("OPUS");
   bob.enableOnlyThisCodec("OPUS");

   alice.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_HD_1920x1080p);
   bob.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_HD_1920x1080p);

   alice.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_HD_1920x1080p);
   bob.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_HD_1920x1080p);

#ifndef __linux__
   CpTestWindowHandle hwndAliceCapture = NULL;
   CpTestWindowHandle nswindowAliceCaptureWindow = NULL;
   ASSERT_EQ(0, ViECreateWindow(hwndAliceCapture, 0, 0, 352, 288, "Alice (capture)", nswindowAliceCaptureWindow));
   alice.video->startCapture();
   if (TestEnvironmentConfig::drawLocalVideo())
   {
      alice.video->setLocalVideoRenderTarget(hwndAliceCapture);
   }

   CpTestWindowHandle hwndAliceRemote = NULL;
   CpTestWindowHandle nswindowAliceRemoteWindow = NULL;
   ASSERT_EQ(0, ViECreateWindow(hwndAliceRemote, 356, 0, 352, 288, "Alice (incoming)", nswindowAliceRemoteWindow));
   if (TestEnvironmentConfig::drawLocalVideo())
   {
      alice.video->setIncomingVideoRenderTarget(hwndAliceRemote);
   }

   CpTestWindowHandle hwndBobCapture = NULL;
   CpTestWindowHandle nswindowBobCaptureWindow = NULL;
   ASSERT_EQ(0, ViECreateWindow(hwndBobCapture, 0, 292, 352, 288, "Bob (capture)", nswindowBobCaptureWindow));
   bob.video->startCapture();
   if (TestEnvironmentConfig::drawLocalVideo())
   {
      bob.video->setLocalVideoRenderTarget(hwndBobCapture);
   }

   CpTestWindowHandle hwndBobRemote = NULL;
   CpTestWindowHandle nswindowBobRemoteWindow = NULL;
   ASSERT_EQ(0, ViECreateWindow(hwndBobRemote, 356, 292, 352, 288, "Bob (incoming)", nswindowBobRemoteWindow));
   if (TestEnvironmentConfig::drawLocalVideo())
   {
      bob.video->setIncomingVideoRenderTarget(hwndBobRemote);
   }
#endif

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, true);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
   alice.conversation->addParticipant(aliceCall, bobTarget.getAOR(true).c_str());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]() {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));

      SipConversationHandle h;
      NewConversationEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
         "SipConversationHandler::onNewConversation",
         5000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(evt.account, alice.handle);

      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      SipConversationState aliceConvState;
      alice.conversationState->getState(aliceCall, aliceConvState);
      ASSERT_EQ(aliceConvState.localMediaInfo.size(), 2);
      ASSERT_TRUE(resip::isEqualNoCase(resip::Data(aliceConvState.localMediaInfo[0].audioCodec.plname), "opus"));
      ASSERT_TRUE(resip::isEqualNoCase(resip::Data(aliceConvState.localMediaInfo[1].videoCodec.plName), statsCodec.c_str()));

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      UsageConfiguration queryConfig;
      setupUsageCheck(queryConfig);
      MacEnergyUsageMonitoring::EnergyData energyData;
      queryConfig.macEnergyUsageMonitoring.start();
      std::this_thread::sleep_for(std::chrono::milliseconds(duration));

      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertCallHadMedia(alice, aliceCall, true, true);

      cleanupUsageCheck(queryConfig);
      queryConfig.macEnergyUsageMonitoring.stop();
      queryConfig.macEnergyUsageMonitoring.getTaskEnergyFromTopInfo(energyData);
      safeCout("CPUUsageTests::videoCallEnergyUsage(): energy-usage: " << energyData.energyUsage << " cpu-usage: " << energyData.cpuUsage << " monitoring-duration: " << (energyData.stopTimeMsecs - energyData.startTimeMsecs) << " msecs");

      int averageEnergyMax = 0;
      ASSERT_NO_FATAL_FAILURE(logCurrentTestAverageEnergyUsage(alice.phone->getInstanceId(), energyData.energyUsage, energyData.cpuUsage,
         ::testing::UnitTest::GetInstance()->current_test_info()->name()));
      ASSERT_NO_FATAL_FAILURE(currentTestPerDeviceMaxEnergyUsage(alice.phone->getInstanceId(), averageEnergyMax));
      ASSERT_LT(energyData.energyUsage, averageEnergyMax) << " energy-usage: " << energyData.energyUsage << " cpu-usage: " << energyData.cpuUsage;
   });

   auto bobEvents = std::async(std::launch::async, [&]()
   {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      SipConversationHandle bobCall;
      assertNewConversationIncoming_ex(bob, &bobCall, alice.config.uri(), [](const NewConversationEvent& evt){
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
      });

      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt)
      {
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      SipConversationState bobConvState;
      bob.conversationState->getState(bobCall, bobConvState);
      ASSERT_EQ(bobConvState.localMediaInfo.size(), 2);
      ASSERT_TRUE(resip::isEqualNoCase(resip::Data(bobConvState.localMediaInfo[0].audioCodec.plname), "opus"));
      ASSERT_TRUE(resip::isEqualNoCase(resip::Data(bobConvState.localMediaInfo[1].videoCodec.plName), statsCodec.c_str()));

      std::this_thread::sleep_for(std::chrono::milliseconds(duration + 7000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      assertCallHadMedia(bob, bobCall, true, true);
   });

   std::this_thread::sleep_for(std::chrono::milliseconds(duration));
   waitFor2Ms(aliceEvents, bobEvents, std::chrono::milliseconds(300000));

#ifndef __linux__
   alice.video->stopCapture();
   alice.video->setLocalVideoRenderTarget(NULL);
   ViEDestroyWindow(nswindowAliceCaptureWindow, hwndAliceCapture);
   ViEDestroyWindow(nswindowAliceRemoteWindow, hwndAliceRemote);
   bob.video->stopCapture();
   bob.video->setLocalVideoRenderTarget(NULL);
   ViEDestroyWindow(nswindowBobCaptureWindow, hwndBobCapture);
   ViEDestroyWindow(nswindowBobRemoteWindow, hwndBobRemote);
#endif
}

TEST_F(CPUUsageTests, AudioCallEnergyUsageOpus)
{
   SCOPED_TRACE(__FUNCTION__);
   audioCallEnergyUsage("OPUS", "opus");
}

#if CPCAPI2_BRAND_CODEC_G729
TEST_F(CPUUsageTests, AudioCallEnergyUsageG729)
{
   SCOPED_TRACE(__FUNCTION__);
   audioCallEnergyUsage("G.729", "G729");
}
#endif // #if CPCAPI2_BRAND_CODEC_G729

TEST_F(CPUUsageTests, AudioCallEnergyUsageG722)
{
   SCOPED_TRACE(__FUNCTION__);
   audioCallEnergyUsage("G.722", "G722");
}

TEST_F(CPUUsageTests, AudioCallEnergyUsageG711)
{
   SCOPED_TRACE(__FUNCTION__);
   audioCallEnergyUsage("G711 uLaw", "PCMU");
}

// Requires ManyCam; if you don't have it, define CPCAPI2_AUTO_TESTS_NO_VIDEO_STATS
// to get a clean run.
TEST_F(CPUUsageTests, VideoCallEnergyUsageH264)
{
   SCOPED_TRACE(__FUNCTION__);
   videoCallEnergyUsage("H.264", "H264");
}

TEST_F(CPUUsageTests, VideoCallEnergyUsageH264_NoVideoRender)
{
   SCOPED_TRACE(__FUNCTION__);
   const bool wasDrawingLocalVideo = TestEnvironmentConfig::drawLocalVideo();
   TestEnvironmentConfig::setDrawLocalVideo(false);
   videoCallEnergyUsage("H.264", "H264");
   TestEnvironmentConfig::setDrawLocalVideo(wasDrawingLocalVideo);
}

TEST_F(CPUUsageTests, VideoCallEnergyUsageVP8)
{
   SCOPED_TRACE(__FUNCTION__);
   videoCallEnergyUsage("VP8", "VP8");
}

TEST_F(CPUUsageTests, VideoCallEnergyUsageVP8_NoVideoRender)
{
   SCOPED_TRACE(__FUNCTION__);
   const bool wasDrawingLocalVideo = TestEnvironmentConfig::drawLocalVideo();
   TestEnvironmentConfig::setDrawLocalVideo(false);
   videoCallEnergyUsage("VP8", "VP8");
   TestEnvironmentConfig::setDrawLocalVideo(wasDrawingLocalVideo);
}

#endif // __APPLE__

#endif // #if !(defined( __linux__ ) && !defined( ANDROID ))
