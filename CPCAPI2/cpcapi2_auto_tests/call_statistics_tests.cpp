#if _WIN32
#include "stdafx.h"
#include "TCHAR.h"
#include "pdh.h"
#else
#include "brand_branded.h"
#endif

#ifdef __APPLE__
#include <mach/mach_init.h>
#include <mach/mach_error.h>
#include <mach/mach_host.h>
#include <mach/vm_map.h>
#include "utils/mac_proc_utils.h"
#endif

#ifdef _WIN32
#include "utils/win_proc_utils.h"
#endif


#include "rutil/ConfigParse.hxx"
#include "json/JsonSerialize.h"

#include "cpcapi2_test_fixture.h"
#include "test_call_events.h"
#include "test_events.h"

using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::SipConversation;
using namespace CPCAPI2::SipAccount;

class CallStatisticsTests : public CpcapiAutoTest
{
public:
   CallStatisticsTests() {}
   virtual ~CallStatisticsTests() {}
};

void stringifyStats(CPCAPI2::SipConversation::AudioStatistics& audioStats)
{
   safeCout("===== LOCAL STATS =====");
   safeCout("cumulativeLost:  " << audioStats.streamStatistics.cumulativeLost);
   safeCout("extendedMax:     " << audioStats.streamStatistics.extendedMax);
   safeCout("fractionLost:    " << audioStats.streamStatistics.fractionLost);
   safeCout("jitterSamples:   " << audioStats.streamStatistics.jitterSamples);
   safeCout("rttMs:           " << audioStats.streamStatistics.rttMs);
   safeCout("averageJitterMs: " << audioStats.averageJitterMs);
   safeCout("MOSCQ:           " << audioStats.XRvoipMetrics.MOSCQ);
   safeCout("MOSLQ:           " << audioStats.XRvoipMetrics.MOSLQ);
}

void stringifyRemoteStats(CPCAPI2::SipConversation::RemoteAudioStatistics& audioStats)
{
   safeCout("===== REMOTE STATS =====");
   safeCout("cumulativeLost:          " << audioStats.streamStatistics.cumulativeLost);
   safeCout("extendedMax:             " << audioStats.streamStatistics.extendedMax);
   safeCout("fractionLost:            " << audioStats.streamStatistics.fractionLost);
   safeCout("jitterSamples:           " << audioStats.streamStatistics.jitterSamples);
   safeCout("rttMs:                   " << audioStats.streamStatistics.rttMs);
   safeCout("MOSCQ:                   " << audioStats.XRvoipMetrics.MOSCQ);
   safeCout("MOSLQ:                   " << audioStats.XRvoipMetrics.MOSLQ);
   safeCout("lastRtcpReceived         " << audioStats.lastRtcpReceived);
   safeCout("lastSenderReportReceived " << audioStats.lastSenderReportReceived)
   safeCout("lastRtcpXrReceived       " << audioStats.lastRtcpXrReceived);
}

// Requires ManyCam; if you don't have it, define CPCAPI2_AUTO_TESTS_NO_VIDEO_STATS
// to get a clean run.
TEST_F(CallStatisticsTests, AudioVideoCallStatistics) {

   TestAccount alice("alice");
   TestAccount bob("bob");

   // enable VP8
   alice.video->queryCodecList();
   alice.video->setCodecEnabled(*********, true);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("H264")), false);
   bob.video->queryCodecList();
   bob.video->setCodecEnabled(*********, true);

   alice.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_Low);
   alice.video->setPreferredResolution(*********, CPCAPI2::Media::VideoCaptureResolution_Low);
   
   bob.video->setLocalVideoPreviewResolution(TestEnvironmentConfig::defaultVideoRes());
   bob.video->setPreferredResolution(*********, TestEnvironmentConfig::defaultVideoRes());

   alice.initiateVideo();
   bob.initiateVideo();

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, true);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertSuccess(alice.conversation->refreshConversationStatistics(aliceCall, true, true, true));
      {
         SipConversationHandle h;
         ConversationStatisticsUpdatedEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent("SipConversationHandler::onConversationStatisticsUpdated",
            15000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
         ASSERT_EQ(1, evt.conversationStatistics.audioChannels.size());
         ASSERT_NE(evt.conversationStatistics.audioChannels[0].streamDataCounters.packetsReceived, 0);
         ASSERT_NE(evt.conversationStatistics.audioChannels[0].streamDataCounters.packetsSent, 0);
         ASSERT_EQ(1, evt.conversationStatistics.videoChannels.size());
         ASSERT_EQ("OPUS", evt.conversationStatistics.audioChannels[0].encoder.displayName);
         ASSERT_EQ("OPUS", evt.conversationStatistics.audioChannels[0].decoder.displayName);
#ifndef CPCAPI2_AUTO_TESTS_NO_VIDEO_STATS
         ASSERT_NE(evt.conversationStatistics.videoChannels[0].streamDataCounters.packetsReceived, 0);
         ASSERT_NE(evt.conversationStatistics.videoChannels[0].streamDataCounters.packetsSent, 0);
         ASSERT_EQ("VP8", evt.conversationStatistics.videoChannels[0].encoder.displayName);
         ASSERT_EQ("VP8", evt.conversationStatistics.videoChannels[0].decoder.displayName);
#endif
         stringifyStats(evt.conversationStatistics.audioChannels[0]);
         stringifyRemoteStats(evt.conversationStatistics.remoteAudioChannels[0]);
      }
      {
         SipConversationHandle h;
         ConversationEndedEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent("SipConversationHandler::onConversationEnded",
            40000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
         ASSERT_EQ(ConversationEndReason_UserTerminatedRemotely, evt.endReason);
      }

      SipConversationState currState;
      ASSERT_EQ(alice.conversationState->getState(aliceCall, currState), kSuccess);
      ASSERT_EQ(1, currState.statistics.audioChannels.size());
      ASSERT_NE(currState.statistics.audioChannels[0].streamDataCounters.packetsReceived, 0);
      ASSERT_NE(currState.statistics.audioChannels[0].streamDataCounters.packetsSent, 0);
      ASSERT_EQ(1, currState.statistics.videoChannels.size());
#ifndef CPCAPI2_AUTO_TESTS_NO_VIDEO_STATS
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsReceived, 0);
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsSent, 0);
#endif

   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());

      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      unsigned int audioPacRecv = 0;
      unsigned int audioPacSent = 0;
      unsigned int videoPacRecv = 0;
      unsigned int videoPacSent = 0;

      SipConversationHandle h;
      ConversationStatisticsUpdatedEvent evt;
      do
      {
         std::this_thread::sleep_for(std::chrono::milliseconds(5000));
         assertSuccess(bob.conversation->refreshConversationStatistics(bobCall, true, true, true));
         {
            ASSERT_TRUE(bob.conversationEvents->expectEvent("SipConversationHandler::onConversationStatisticsUpdated",
               15000, HandleEqualsPred<SipConversationHandle>(bobCall), h, evt));
            ASSERT_EQ(1, evt.conversationStatistics.audioChannels.size());
            ASSERT_NE(evt.conversationStatistics.audioChannels[0].streamDataCounters.packetsReceived, 0);
            audioPacRecv = evt.conversationStatistics.audioChannels[0].streamDataCounters.packetsReceived;
            ASSERT_NE(evt.conversationStatistics.audioChannels[0].streamDataCounters.packetsSent, 0);
            audioPacSent = evt.conversationStatistics.audioChannels[0].streamDataCounters.packetsSent;
            ASSERT_EQ(1, evt.conversationStatistics.videoChannels.size());
            //ASSERT_NE(evt.conversationStatistics.remoteAudioChannels[0].lastRtcpReceived, 0);
            //std::cout << "time started: " << evt.conversationStatistics.audioChannels[0].callStartTimeNTP << std::endl;
            //std::cout << "local: " << evt.conversationStatistics.audioChannels[0].endpoint.ipAddress << ", remote: " << evt.conversationStatistics.remoteAudioChannels[0].endpoint.ipAddress << std::endl;
   #ifndef CPCAPI2_AUTO_TESTS_NO_VIDEO_STATS
            //ASSERT_NE(evt.conversationStatistics.remoteVideoChannels[0].lastRtcpReceived, 0);
            ASSERT_NE(evt.conversationStatistics.videoChannels[0].streamDataCounters.packetsReceived, 0);
            videoPacRecv = evt.conversationStatistics.videoChannels[0].streamDataCounters.packetsReceived;
            ASSERT_NE(evt.conversationStatistics.videoChannels[0].streamDataCounters.packetsSent, 0);
            videoPacSent = evt.conversationStatistics.videoChannels[0].streamDataCounters.packetsSent;
   #endif
            stringifyStats(evt.conversationStatistics.audioChannels[0]);
            stringifyRemoteStats(evt.conversationStatistics.remoteAudioChannels[0]);
         }
      } while(( evt.conversationStatistics.remoteAudioChannels[0].lastRtcpReceived == 0 )
#ifndef CPCAPI2_AUTO_TESTS_NO_VIDEO_STATS
         || ( evt.conversationStatistics.remoteVideoChannels[0].lastRtcpReceived == 0 )
#endif
         );

      std::this_thread::sleep_for(std::chrono::milliseconds(25000));

      assertSuccess(bob.conversation->refreshConversationStatistics(bobCall, true, true, true));
      {
         ASSERT_TRUE(bob.conversationEvents->expectEvent("SipConversationHandler::onConversationStatisticsUpdated",
            15000, HandleEqualsPred<SipConversationHandle>(bobCall), h, evt));
         ASSERT_EQ(1, evt.conversationStatistics.audioChannels.size());
         ASSERT_TRUE(evt.conversationStatistics.audioChannels[0].streamDataCounters.packetsReceived > audioPacRecv);
         audioPacRecv = evt.conversationStatistics.audioChannels[0].streamDataCounters.packetsReceived;
         ASSERT_TRUE(evt.conversationStatistics.audioChannels[0].streamDataCounters.packetsSent > audioPacSent);
         audioPacSent = evt.conversationStatistics.audioChannels[0].streamDataCounters.packetsSent;
         ASSERT_EQ(1, evt.conversationStatistics.videoChannels.size());
         //std::cout << "time started: " << evt.conversationStatistics.audioChannels[0].callStartTimeNTP << std::endl;
#ifndef CPCAPI2_AUTO_TESTS_NO_VIDEO_STATS
         ASSERT_TRUE(evt.conversationStatistics.videoChannels[0].streamDataCounters.packetsReceived > videoPacRecv);
         videoPacRecv = evt.conversationStatistics.videoChannels[0].streamDataCounters.packetsReceived;
         ASSERT_TRUE(evt.conversationStatistics.videoChannels[0].streamDataCounters.packetsSent > videoPacSent);
         videoPacSent = evt.conversationStatistics.videoChannels[0].streamDataCounters.packetsSent;
#endif
         stringifyStats(evt.conversationStatistics.audioChannels[0]);
         stringifyRemoteStats(evt.conversationStatistics.remoteAudioChannels[0]);
      }
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      assertSuccess(bob.conversation->refreshConversationStatistics(bobCall, true, true, true));
      {
         ASSERT_TRUE(bob.conversationEvents->expectEvent("SipConversationHandler::onConversationStatisticsUpdated",
            15000, HandleEqualsPred<SipConversationHandle>(bobCall), h, evt));
         ASSERT_EQ(1, evt.conversationStatistics.audioChannels.size());
         ASSERT_TRUE(evt.conversationStatistics.audioChannels[0].streamDataCounters.packetsReceived > audioPacRecv);
         audioPacRecv = evt.conversationStatistics.audioChannels[0].streamDataCounters.packetsReceived;
         ASSERT_TRUE(evt.conversationStatistics.audioChannels[0].streamDataCounters.packetsSent > audioPacSent);
         audioPacSent = evt.conversationStatistics.audioChannels[0].streamDataCounters.packetsSent;
         ASSERT_EQ(1, evt.conversationStatistics.videoChannels.size());
         //std::cout << "time started: " << evt.conversationStatistics.audioChannels[0].callStartTimeNTP << std::endl;
#ifndef CPCAPI2_AUTO_TESTS_NO_VIDEO_STATS
         ASSERT_TRUE(evt.conversationStatistics.videoChannels[0].streamDataCounters.packetsReceived > videoPacRecv);
         videoPacRecv = evt.conversationStatistics.videoChannels[0].streamDataCounters.packetsReceived;
         ASSERT_TRUE(evt.conversationStatistics.videoChannels[0].streamDataCounters.packetsSent > videoPacSent);
         videoPacSent = evt.conversationStatistics.videoChannels[0].streamDataCounters.packetsSent;
#endif
         stringifyStats(evt.conversationStatistics.audioChannels[0]);
         stringifyRemoteStats(evt.conversationStatistics.remoteAudioChannels[0]);
      }

      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor2(aliceEvents, bobEvents);
}

TEST_F(CallStatisticsTests, AudioVideoCallStatistics_ReInvite) {

   TestAccount alice("alice");
   TestAccount bob("bob");

   alice.enableOnlyThisVideoCodec("H.264");
   bob.enableOnlyThisVideoCodec("H.264");

   alice.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_Low);
   alice.video->setPreferredResolution(*********, CPCAPI2::Media::VideoCaptureResolution_Low);
   
   bob.video->setLocalVideoPreviewResolution(TestEnvironmentConfig::defaultVideoRes());
   bob.video->setPreferredResolution(*********, TestEnvironmentConfig::defaultVideoRes());

   alice.initiateVideo();
   bob.initiateVideo();

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, true);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      // for bob's re-INVITE
      SipConversationHandle h;
      ConversationMediaChangeRequestEvent evt;
      ASSERT_TRUE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onConversationMediaChangeRequest",
      45000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
      assertSuccess(alice.conversation->accept(aliceCall));

      {
         SipConversationHandle h;
         ConversationEndedEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent("SipConversationHandler::onConversationEnded",
            40000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
         ASSERT_EQ(ConversationEndReason_UserTerminatedRemotely, evt.endReason);
      }

   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());

      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      unsigned int audioPacRecv = 0;
      unsigned int audioPacSent = 0;
      unsigned int videoPacRecv = 0;
      unsigned int videoPacSent = 0;

      SipConversationHandle h;
      ConversationStatisticsUpdatedEvent evt;

      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertSuccess(bob.conversation->refreshConversationStatistics(bobCall, true, true, true));
      {
         ASSERT_TRUE(bob.conversationEvents->expectEvent("SipConversationHandler::onConversationStatisticsUpdated",
            15000, HandleEqualsPred<SipConversationHandle>(bobCall), h, evt));
         ASSERT_EQ(1, evt.conversationStatistics.audioChannels.size());
         ASSERT_NE(evt.conversationStatistics.audioChannels[0].streamDataCounters.packetsReceived, 0);
         audioPacRecv = evt.conversationStatistics.audioChannels[0].streamDataCounters.packetsReceived;
         ASSERT_NE(evt.conversationStatistics.audioChannels[0].streamDataCounters.packetsSent, 0);
         audioPacSent = evt.conversationStatistics.audioChannels[0].streamDataCounters.packetsSent;
         ASSERT_EQ(1, evt.conversationStatistics.videoChannels.size());
#ifndef CPCAPI2_AUTO_TESTS_NO_VIDEO_STATS
         ASSERT_NE(evt.conversationStatistics.videoChannels[0].streamDataCounters.packetsReceived, 0);
         videoPacRecv = evt.conversationStatistics.videoChannels[0].streamDataCounters.packetsReceived;
         ASSERT_NE(evt.conversationStatistics.videoChannels[0].streamDataCounters.packetsSent, 0);
         videoPacSent = evt.conversationStatistics.videoChannels[0].streamDataCounters.packetsSent;
#endif
      }
      
      bob.conversation->sendMediaChangeRequest(bobCall);
      
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      
      assertSuccess(bob.conversation->refreshConversationStatistics(bobCall, true, true, true));
      {
         ASSERT_TRUE(bob.conversationEvents->expectEvent("SipConversationHandler::onConversationStatisticsUpdated",
            15000, HandleEqualsPred<SipConversationHandle>(bobCall), h, evt));
         
         ASSERT_GT(evt.conversationStatistics.videoChannels[0].streamDataCounters.packetsReceived, videoPacRecv);
      }

      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor2(aliceEvents, bobEvents);
}

TEST_F(CallStatisticsTests, AudioVideoCallStatistics_SessionTimers) {

   TestAccount alice("alice", Account_NoInit);
   TestAccount bob("bob", Account_NoInit);
   
   alice.config.settings.sessionTimerMode = SipAccountSessionTimerMode_Always;
   alice.config.settings.sessionTimeSeconds = 90;
   bob.config.settings.sessionTimerMode = SipAccountSessionTimerMode_Always;
   bob.config.settings.sessionTimeSeconds = 90;
   
   alice.init();
   bob.init();
   
   alice.enable();
   bob.enable();

   alice.enableOnlyThisVideoCodec("H.264");
   bob.enableOnlyThisVideoCodec("H.264");

   alice.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_Low);
   alice.video->setPreferredResolution(*********, CPCAPI2::Media::VideoCaptureResolution_Low);
   
   bob.video->setLocalVideoPreviewResolution(TestEnvironmentConfig::defaultVideoRes());
   bob.video->setPreferredResolution(*********, TestEnvironmentConfig::defaultVideoRes());

   alice.initiateVideo();
   bob.initiateVideo();

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, true);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      unsigned int audioPacRecv = 0;
      unsigned int audioPacSent = 0;
      unsigned int videoPacRecv = 0;
      unsigned int videoPacSent = 0;

      std::this_thread::sleep_for(std::chrono::milliseconds(15000));
      assertSuccess(alice.conversation->refreshConversationStatistics(aliceCall, true, true, true));
      {
         SipConversationHandle h;
         ConversationStatisticsUpdatedEvent evt;

         ASSERT_TRUE(alice.conversationEvents->expectEvent("SipConversationHandler::onConversationStatisticsUpdated",
            15000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
         ASSERT_EQ(1, evt.conversationStatistics.audioChannels.size());
         ASSERT_NE(evt.conversationStatistics.audioChannels[0].streamDataCounters.packetsReceived, 0);
         audioPacRecv = evt.conversationStatistics.audioChannels[0].streamDataCounters.packetsReceived;
         ASSERT_NE(evt.conversationStatistics.audioChannels[0].streamDataCounters.packetsSent, 0);
         audioPacSent = evt.conversationStatistics.audioChannels[0].streamDataCounters.packetsSent;
         ASSERT_EQ(1, evt.conversationStatistics.videoChannels.size());
#ifndef CPCAPI2_AUTO_TESTS_NO_VIDEO_STATS
         ASSERT_NE(evt.conversationStatistics.videoChannels[0].streamDataCounters.packetsReceived, 0);
         videoPacRecv = evt.conversationStatistics.videoChannels[0].streamDataCounters.packetsReceived;
         ASSERT_NE(evt.conversationStatistics.videoChannels[0].streamDataCounters.packetsSent, 0);
         videoPacSent = evt.conversationStatistics.videoChannels[0].streamDataCounters.packetsSent;
#endif
      }

      // for bob's session timer re-INVITE
      SipConversationHandle h;
      ConversationMediaChangeRequestEvent evt;
      ASSERT_TRUE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onConversationMediaChangeRequest",
      45000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
      assertSuccess(alice.conversation->accept(aliceCall));

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      
      assertSuccess(alice.conversation->refreshConversationStatistics(aliceCall, true, true, true));
      {
         SipConversationHandle h;
         ConversationStatisticsUpdatedEvent evt;

         ASSERT_TRUE(alice.conversationEvents->expectEvent("SipConversationHandler::onConversationStatisticsUpdated",
            15000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
         
         ASSERT_GT(evt.conversationStatistics.videoChannels[0].streamDataCounters.packetsReceived, videoPacRecv);
      }


      {
         SipConversationHandle h;
         ConversationEndedEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent("SipConversationHandler::onConversationEnded",
            180000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
         ASSERT_EQ(ConversationEndReason_UserTerminatedRemotely, evt.endReason);
      }

   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());

      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      unsigned int audioPacRecv = 0;
      unsigned int audioPacSent = 0;
      unsigned int videoPacRecv = 0;
      unsigned int videoPacSent = 0;

      std::this_thread::sleep_for(std::chrono::milliseconds(15000));
      assertSuccess(bob.conversation->refreshConversationStatistics(bobCall, true, true, true));
      {
         SipConversationHandle h;
         ConversationStatisticsUpdatedEvent evt;

         ASSERT_TRUE(bob.conversationEvents->expectEvent("SipConversationHandler::onConversationStatisticsUpdated",
            15000, HandleEqualsPred<SipConversationHandle>(bobCall), h, evt));
         ASSERT_EQ(1, evt.conversationStatistics.audioChannels.size());
         ASSERT_NE(evt.conversationStatistics.audioChannels[0].streamDataCounters.packetsReceived, 0);
         audioPacRecv = evt.conversationStatistics.audioChannels[0].streamDataCounters.packetsReceived;
         ASSERT_NE(evt.conversationStatistics.audioChannels[0].streamDataCounters.packetsSent, 0);
         audioPacSent = evt.conversationStatistics.audioChannels[0].streamDataCounters.packetsSent;
         ASSERT_EQ(1, evt.conversationStatistics.videoChannels.size());
#ifndef CPCAPI2_AUTO_TESTS_NO_VIDEO_STATS
         ASSERT_NE(evt.conversationStatistics.videoChannels[0].streamDataCounters.packetsReceived, 0);
         videoPacRecv = evt.conversationStatistics.videoChannels[0].streamDataCounters.packetsReceived;
         ASSERT_NE(evt.conversationStatistics.videoChannels[0].streamDataCounters.packetsSent, 0);
         videoPacSent = evt.conversationStatistics.videoChannels[0].streamDataCounters.packetsSent;
#endif
      }
      
      {
      // session timer re-INVITE
         SipConversationHandle h;
         ConversationMediaChangedEvent evt;
         ASSERT_TRUE(bob.conversationEvents->expectEvent("SipConversationHandler::onConversationMediaChanged",
            120000, HandleEqualsPred<SipConversationHandle>(bobCall), h, evt));
      }
      
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      
      assertSuccess(bob.conversation->refreshConversationStatistics(bobCall, true, true, true));
      {
         SipConversationHandle h;
         ConversationStatisticsUpdatedEvent evt;

         ASSERT_TRUE(bob.conversationEvents->expectEvent("SipConversationHandler::onConversationStatisticsUpdated",
            15000, HandleEqualsPred<SipConversationHandle>(bobCall), h, evt));
         
         ASSERT_GT(evt.conversationStatistics.videoChannels[0].streamDataCounters.packetsReceived, videoPacRecv);
      }

      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor2(aliceEvents, bobEvents);
}

// OBELISK-6144
TEST_F(CallStatisticsTests, DISABLED_AudioVideoCallStatistics_NetworkChange) {

   TestAccount alice("alice", Account_NoInit);
   TestAccount bob("bob", Account_NoInit);
   
   alice.init();
   bob.init();
   
   alice.enable();
   bob.enable();

   alice.enableOnlyThisVideoCodec("H.264");
   bob.enableOnlyThisVideoCodec("H.264");

   alice.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_Low);
   alice.video->setPreferredResolution(*********, CPCAPI2::Media::VideoCaptureResolution_Low);
   
   bob.video->setLocalVideoPreviewResolution(TestEnvironmentConfig::defaultVideoRes());
   bob.video->setPreferredResolution(*********, TestEnvironmentConfig::defaultVideoRes());

   alice.initiateVideo();
   bob.initiateVideo();

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, true);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      unsigned int audioPacRecv = 0;
      unsigned int audioPacSent = 0;
      unsigned int videoPacRecv = 0;
      unsigned int videoPacSent = 0;

      std::this_thread::sleep_for(std::chrono::milliseconds(15000));
      assertSuccess(alice.conversation->refreshConversationStatistics(aliceCall, true, true, true));
      {
         SipConversationHandle h;
         ConversationStatisticsUpdatedEvent evt;

         ASSERT_TRUE(alice.conversationEvents->expectEvent("SipConversationHandler::onConversationStatisticsUpdated",
            15000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
         ASSERT_EQ(1, evt.conversationStatistics.audioChannels.size());
         ASSERT_NE(evt.conversationStatistics.audioChannels[0].streamDataCounters.packetsReceived, 0);
         audioPacRecv = evt.conversationStatistics.audioChannels[0].streamDataCounters.packetsReceived;
         ASSERT_NE(evt.conversationStatistics.audioChannels[0].streamDataCounters.packetsSent, 0);
         audioPacSent = evt.conversationStatistics.audioChannels[0].streamDataCounters.packetsSent;
         ASSERT_EQ(1, evt.conversationStatistics.videoChannels.size());
#ifndef CPCAPI2_AUTO_TESTS_NO_VIDEO_STATS
         ASSERT_NE(evt.conversationStatistics.videoChannels[0].streamDataCounters.packetsReceived, 0);
         videoPacRecv = evt.conversationStatistics.videoChannels[0].streamDataCounters.packetsReceived;
         ASSERT_NE(evt.conversationStatistics.videoChannels[0].streamDataCounters.packetsSent, 0);
         videoPacSent = evt.conversationStatistics.videoChannels[0].streamDataCounters.packetsSent;
#endif
      }

      // for bob's session network change re-INVITE
      SipConversationHandle h;
      ConversationMediaChangeRequestEvent evt;
      ASSERT_TRUE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onConversationMediaChangeRequest",
      45000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
      assertSuccess(alice.conversation->accept(aliceCall));

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      
      assertSuccess(alice.conversation->refreshConversationStatistics(aliceCall, true, true, true));
      {
         SipConversationHandle h;
         ConversationStatisticsUpdatedEvent evt;
      
         ASSERT_TRUE(alice.conversationEvents->expectEvent("SipConversationHandler::onConversationStatisticsUpdated",
            15000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
         
         ASSERT_GT(evt.conversationStatistics.videoChannels[0].streamDataCounters.packetsReceived, videoPacRecv);
      }


      {
         SipConversationHandle h;
         ConversationEndedEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent("SipConversationHandler::onConversationEnded",
            180000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
         ASSERT_EQ(ConversationEndReason_UserTerminatedRemotely, evt.endReason);
      }

   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());

      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      unsigned int audioPacRecv = 0;
      unsigned int audioPacSent = 0;
      unsigned int videoPacRecv = 0;
      unsigned int videoPacSent = 0;

      std::this_thread::sleep_for(std::chrono::milliseconds(15000));
    
      bob.network->setNetworkTransport(TransportWWAN);
      std::set<resip::Data> ifaces;
      ifaces.insert("**************");
      bob.network->setMockInterfaces(ifaces);
      
      {
         // network change re-INVITE
         SipConversationHandle h;
         ConversationMediaChangedEvent evt;
         ASSERT_TRUE(bob.conversationEvents->expectEvent("SipConversationHandler::onConversationMediaChanged",
            120000, HandleEqualsPred<SipConversationHandle>(bobCall), h, evt));
      }
      
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor2(aliceEvents, bobEvents);
}


void doCallForAudioWidebandThenNarrowbandCalls(TestAccount& alice, TestAccount& bob)
{
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   const int callLengthBeforeStatsCheckMs = 15000;
   const int callLengthAfterStatsCheckms = 3000;

   auto aliceEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(callLengthBeforeStatsCheckMs));
      assertSuccess(alice.conversation->refreshConversationStatistics(aliceCall, true, true, true));
      {
         SipConversationHandle h;
         ConversationStatisticsUpdatedEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent("SipConversationHandler::onConversationStatisticsUpdated",
            15000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
         ASSERT_EQ(1, evt.conversationStatistics.audioChannels.size());
         
         ASSERT_EQ(1, evt.conversationStatistics.audioChannels.size());
         AudioStatistics aliceStats = *(evt.conversationStatistics.audioChannels.begin());
         ASSERT_GE(aliceStats.XRvoipMetrics.MOSCQ, 40);
         ASSERT_GE(aliceStats.XRvoipMetrics.MOSLQ, 40);
      }
      {
         std::this_thread::sleep_for(std::chrono::milliseconds(callLengthAfterStatsCheckms));
      
         SipConversationHandle h;
         ConversationEndedEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent("SipConversationHandler::onConversationEnded",
            40000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
         ASSERT_EQ(ConversationEndReason_UserTerminatedRemotely, evt.endReason);
      }

   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());

      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(callLengthBeforeStatsCheckMs));

      assertSuccess(bob.conversation->refreshConversationStatistics(bobCall, true, true, true));
      {
         SipConversationHandle h;
         ConversationStatisticsUpdatedEvent evt;
         ASSERT_TRUE(bob.conversationEvents->expectEvent("SipConversationHandler::onConversationStatisticsUpdated",
            15000, HandleEqualsPred<SipConversationHandle>(bobCall), h, evt));
         ASSERT_EQ(1, evt.conversationStatistics.audioChannels.size());
         AudioStatistics bobStats = *(evt.conversationStatistics.audioChannels.begin());
         ASSERT_GE(bobStats.XRvoipMetrics.MOSCQ, 40);
         ASSERT_GE(bobStats.XRvoipMetrics.MOSLQ, 40);
      }
      
      std::this_thread::sleep_for(std::chrono::milliseconds(callLengthAfterStatsCheckms));

      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor2(aliceEvents, bobEvents);
}

// for demonstrating / verifying OBELISK-4393 --
// OPUS call then G711 call resulted in G711 call having MOS of 2.4
// even though audio quality was good.
TEST_F(CallStatisticsTests, AudioWidebandThenNarrowbandCalls)
{
   TestAccount alice("alice");
   TestAccount bob("bob");

   alice.enableOnlyThisCodec("OPUS");
   doCallForAudioWidebandThenNarrowbandCalls(alice, bob);

   alice.enableOnlyThisCodec("G711 uLaw");
   doCallForAudioWidebandThenNarrowbandCalls(alice, bob);
}

TEST_F(CallStatisticsTests, AudioCallRTCPChecks)
{
   // Calculate referent CPU usage value. Percentage of CPU used before call is placed.
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   TestAccount alice("alice");
   TestAccount bob("bob");

   alice.enableOnlyThisCodec("OPUS");
   bob.enableOnlyThisCodec("OPUS");

   // Conversation creation
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]() {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertAudioFlowing(alice, aliceCall);
      std::this_thread::sleep_for(std::chrono::milliseconds(45000));
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
      assertCallHadAudio(alice, aliceCall);
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(3000));
      assertAudioFlowing(bob, bobCall);

      uint64_t rtcpTimestamp = 0;
      for (unsigned int i = 0; i < 3; i++)
      {
         std::this_thread::sleep_for(std::chrono::milliseconds(10000));
         assertSuccess(bob.conversation->refreshConversationStatistics(bobCall, true, true, true));
         {
            SipConversationHandle h;
            ConversationStatisticsUpdatedEvent evt;
            ASSERT_TRUE(bob.conversationEvents->waitForEvent(__LINE__, "SipConversationHandler::onConversationStatisticsUpdated",
               15000, HandleEqualsPred<SipConversationHandle>(bobCall), h, evt));

            ASSERT_GT( evt.conversationStatistics.remoteAudioChannels[ 0 ].lastRtcpReceived, rtcpTimestamp );
            rtcpTimestamp = evt.conversationStatistics.remoteAudioChannels[ 0 ].lastRtcpReceived;
         }
      }

      {
         SipConversationHandle h;
         ConversationEndedEvent evt;
         ASSERT_TRUE(bob.conversationEvents->expectEvent("SipConversationHandler::onConversationEnded",
            50000, HandleEqualsPred<SipConversationHandle>(bobCall), h, evt));
         ASSERT_EQ(ConversationEndReason_UserTerminatedRemotely, evt.endReason);
      }
      assertCallHadAudio(bob, bobCall);

   });

   waitFor2(aliceEvents, bobEvents);
}


TEST_F(CallStatisticsTests, SerializeStatistics)
{
   TestAccount alice("alice");
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);

   AudioStatistics as, emptyAs;
   RemoteAudioStatistics ras, emptyRas;
   VideoStatistics vs, emptyVs;
   RemoteVideoStatistics rvs, emptyRvs;
   ConversationStatistics convStats;

   memset(&emptyAs, 0, sizeof(emptyAs));
   memset(&emptyRas, 0, sizeof(emptyRas));
   memset(&emptyVs, 0, sizeof(emptyVs));
   memset(&emptyRvs, 0, sizeof(emptyRvs));

   as.encoder.channels = 1;
   as.encoder.pacsize = 2;
   as.encoder.plfreq = 3;
   strcpy(as.encoder.plname, "plname1");
   as.encoder.pltype = 4;
   as.encoder.rate = 5;
   as.encoder.priority = 0;
   as.decoder.channels = 6;
   as.decoder.pacsize = 7;
   as.decoder.plfreq = 8;
   strcpy(as.decoder.plname, "plname2");
   as.decoder.pltype = 9;
   as.decoder.rate = 10;
   as.decoder.priority = 0;
   vs.encoder.hadwareAccelerated = true;
   vs.encoder.priority = 0;
   vs.encoder.height = 11;
   vs.encoder.maxBitrate = 12;
   vs.encoder.maxFramerate = 13;
   vs.encoder.minBitrate = 14;
   strcpy(vs.encoder.plName, "plname3");
   vs.encoder.plType = 16;
   vs.encoder.startBitrate = 17;
   vs.encoder.width = 18;
   vs.decoder.hadwareAccelerated = true;
   vs.decoder.priority = 0;
   vs.decoder.height = 18;
   vs.decoder.maxBitrate = 19;
   vs.decoder.maxFramerate = 20;
   vs.decoder.minBitrate = 21;
   strcpy(vs.decoder.plName, "plname4");
   vs.decoder.plType = 22;
   vs.decoder.startBitrate = 23;
   vs.decoder.width = 24;
   as.streamStatistics.cumulativeLost = 25;
   as.streamStatistics.extendedMax = 26;
   as.streamStatistics.fractionLost = 27;
   as.streamStatistics.jitterSamples = 28;
   as.streamStatistics.rttMs = 29;
   ras.streamStatistics.cumulativeLost = 30;
   ras.streamStatistics.extendedMax = 31;
   ras.streamStatistics.fractionLost = 32;
   ras.streamStatistics.jitterSamples = 33;
   ras.streamStatistics.rttMs = 34;
   vs.streamStatistics.cumulativeLost = 35;
   vs.streamStatistics.extendedMax = 36;
   vs.streamStatistics.fractionLost = 37;
   vs.streamStatistics.jitterSamples = 38;
   vs.streamStatistics.rttMs = 39;
   rvs.streamStatistics.cumulativeLost = 40;
   rvs.streamStatistics.extendedMax = 41;
   rvs.streamStatistics.fractionLost = 42;
   rvs.streamStatistics.jitterSamples = 43;
   rvs.streamStatistics.rttMs = 44;
   as.streamDataCounters.bytesReceived = 45;
   as.streamDataCounters.bytesSent = 46;
   as.streamDataCounters.packetsReceived = 47;
   as.streamDataCounters.packetsSent = 48;
   vs.streamDataCounters.bytesReceived = 45;
   vs.streamDataCounters.bytesSent = 46;
   vs.streamDataCounters.packetsReceived = 47;
   vs.streamDataCounters.packetsSent = 48;
   as.XRvoipMetrics.burstDensity = 49;
   as.XRvoipMetrics.burstDuration = 50;
   as.XRvoipMetrics.discardRate = 51;
   as.XRvoipMetrics.endSystemDelay = 52;
   as.XRvoipMetrics.extRfactor = 53;
   as.XRvoipMetrics.gapDensity = 54;
   as.XRvoipMetrics.gapDuration = 55;
   as.XRvoipMetrics.Gmin = 56;
   as.XRvoipMetrics.JBabsMax = 57;
   as.XRvoipMetrics.JBmax = 101;
   as.XRvoipMetrics.JBnominal = 102;
   as.XRvoipMetrics.lossRate = 103;
   as.XRvoipMetrics.MOSCQ = 104;
   as.XRvoipMetrics.MOSLQ = 105;
   as.XRvoipMetrics.noiseLevel = 107;
   as.XRvoipMetrics.RERL = 108;
   as.XRvoipMetrics.Rfactor = 109;
   as.XRvoipMetrics.roundTripDelay = 110;
   as.XRvoipMetrics.RXconfig = 111;
   as.XRvoipMetrics.signalLevel = 112;
   ras.XRvoipMetrics.burstDensity = 58;
   ras.XRvoipMetrics.burstDuration = 59;
   ras.XRvoipMetrics.discardRate = 60;
   ras.XRvoipMetrics.endSystemDelay = 61;
   ras.XRvoipMetrics.extRfactor = 62;
   ras.XRvoipMetrics.gapDensity = 63;
   ras.XRvoipMetrics.gapDuration = 64;
   ras.XRvoipMetrics.Gmin = 65;
   ras.XRvoipMetrics.JBabsMax = 66;
   ras.XRvoipMetrics.JBmax = 113;
   ras.XRvoipMetrics.JBnominal = 114;
   ras.XRvoipMetrics.lossRate = 115;
   ras.XRvoipMetrics.MOSCQ = 116;
   ras.XRvoipMetrics.MOSLQ = 117;
   ras.XRvoipMetrics.noiseLevel = 118;
   ras.XRvoipMetrics.RERL = 119;
   ras.XRvoipMetrics.Rfactor = 120;
   ras.XRvoipMetrics.roundTripDelay = 121;
   ras.XRvoipMetrics.RXconfig = 122;
   ras.XRvoipMetrics.signalLevel = 123;
   as.XRstatisticsSummary.begin_seq = 67;
   as.XRstatisticsSummary.dev_jitter = 68;
   as.XRstatisticsSummary.dev_ttl_or_hl = 69;
   as.XRstatisticsSummary.dup_packets = 70;
   as.XRstatisticsSummary.end_seq = 71;
   as.XRstatisticsSummary.lost_packets = 72;
   as.XRstatisticsSummary.max_jitter = 73;
   as.XRstatisticsSummary.max_ttl_or_hl = 74;
   as.XRstatisticsSummary.mean_jitter = 75;
   as.XRstatisticsSummary.mean_ttl_or_hl = 124;
   as.XRstatisticsSummary.min_jitter = 125;
   as.XRstatisticsSummary.min_ttl_or_hl = 126;
   ras.XRstatisticsSummary.begin_seq = 76;
   ras.XRstatisticsSummary.dev_jitter = 77;
   ras.XRstatisticsSummary.dev_ttl_or_hl = 78;
   ras.XRstatisticsSummary.dup_packets = 79;
   ras.XRstatisticsSummary.end_seq = 80;
   ras.XRstatisticsSummary.lost_packets = 81;
   ras.XRstatisticsSummary.max_jitter = 82;
   ras.XRstatisticsSummary.max_ttl_or_hl = 83;
   ras.XRstatisticsSummary.mean_jitter = 84;
   ras.XRstatisticsSummary.mean_ttl_or_hl = 127;
   ras.XRstatisticsSummary.min_jitter = 128;
   ras.XRstatisticsSummary.min_ttl_or_hl = 129;
   as.endpoint.ipAddress = "*******";
   as.endpoint.port = 85;
   ras.endpoint.ipAddress = "*******";
   ras.endpoint.port = 86;
   vs.endpoint.ipAddress = "**********";
   vs.endpoint.port = 87;
   rvs.endpoint.ipAddress = "***********";
   rvs.endpoint.port = 88;
   as.averageJitterMs = 89;
   as.callStartTimeNTP = 90;
   as.discardedPackets = 91;
   as.maxJitterMs = 92;
   ras.sender_SSRC = 93;
   ras.source_SSRC = 94;
   vs.currentTargetBitrate = 95;
   vs.discardedPackets = 96;
   vs.fecBitrateSent = 97;
   vs.nackBitrateSent = 98;
   vs.totalBitrateSent = 99;
   vs.videoBitrateSent = 100;
   ras.lastRtcpReceived = 130;
   ras.lastRtcpXrReceived = 132;
   ras.lastSenderReportReceived = 133;
   rvs.lastRtcpReceived = 131;
   rvs.lastSenderReportReceived = 134;
   convStats.audioChannels.push_back(as);
   convStats.audioChannels.push_back(emptyAs);
   convStats.videoChannels.push_back(vs);
   convStats.videoChannels.push_back(emptyVs);
   convStats.remoteAudioChannels.push_back(ras);
   convStats.remoteAudioChannels.push_back(emptyRas);
   convStats.remoteVideoChannels.push_back(rvs);
   convStats.remoteVideoChannels.push_back(emptyRvs);
   convStats.callQuality = ConversationCallQuality_Fair;

   rapidjson::StringBuffer convStatsStringBuffer;
   rapidjson::Writer<rapidjson::StringBuffer> writer(convStatsStringBuffer);
   CPCAPI2::Json::Serialize(writer, convStats);

   const char* actual_string = convStatsStringBuffer.GetString();
   const char* expected_string = "{\"audioChannels\":[{\"encoder\":{\"pltype\":4,\"plname\":\"plname1\",\"plfreq\":3,\"pacsize\":2,\"channels\":1,\"rate\":5,\"priority\":0,\"displayName\":\"\"},\"decoder\":{\"pltype\":9,\"plname\":\"plname2\",\"plfreq\":8,\"pacsize\":7,\"channels\":6,\"rate\":10,\"priority\":0,\"displayName\":\"\"},\"streamStatistics\":{\"fractionLost\":27,\"cumulativeLost\":25,\"extendedMax\":26,\"jitterSamples\":28,\"rttMs\":29},\"streamDataCounters\":{\"bytesSent\":46,\"packetsSent\":48,\"bytesReceived\":45,\"packetsReceived\":47},\"maxJitterMs\":92,\"averageJitterMs\":89,\"discardedPackets\":91,\"XRvoipMetrics\":{\"lossRate\":103,\"discardRate\":51,\"burstDensity\":49,\"gapDensity\":54,\"burstDuration\":50,\"gapDuration\":55,\"roundTripDelay\":110,\"endSystemDelay\":52,\"signalLevel\":112,\"noiseLevel\":107,\"RERL\":108,\"Gmin\":56,\"Rfactor\":109,\"extRfactor\":53,\"MOSLQ\":105,\"MOSCQ\":104,\"RXconfig\":111,\"JBnominal\":102,\"JBmax\":101,\"JBabsMax\":57},\"XRstatisticsSummary\":{\"begin_seq\":67,\"end_seq\":71,\"lost_packets\":72,\"dup_packets\":70,\"min_jitter\":125,\"max_jitter\":73,\"mean_jitter\":75,\"dev_jitter\":68,\"min_ttl_or_hl\":126,\"max_ttl_or_hl\":74,\"mean_ttl_or_hl\":124,\"dev_ttl_or_hl\":69},\"intervalCallQualityReport\":\"\",\"callStartTimeNTP\":90,\"endpoint\":{\"ipAddress\":\"*******\",\"port\":85}},{\"encoder\":{\"pltype\":0,\"plname\":\"\",\"plfreq\":0,\"pacsize\":0,\"channels\":0,\"rate\":0,\"priority\":0,\"displayName\":\"\"},\"decoder\":{\"pltype\":0,\"plname\":\"\",\"plfreq\":0,\"pacsize\":0,\"channels\":0,\"rate\":0,\"priority\":0,\"displayName\":\"\"},\"streamStatistics\":{\"fractionLost\":0,\"cumulativeLost\":0,\"extendedMax\":0,\"jitterSamples\":0,\"rttMs\":0},\"streamDataCounters\":{\"bytesSent\":0,\"packetsSent\":0,\"bytesReceived\":0,\"packetsReceived\":0},\"maxJitterMs\":0,\"averageJitterMs\":0,\"discardedPackets\":0,\"XRvoipMetrics\":{\"lossRate\":0,\"discardRate\":0,\"burstDensity\":0,\"gapDensity\":0,\"burstDuration\":0,\"gapDuration\":0,\"roundTripDelay\":0,\"endSystemDelay\":0,\"signalLevel\":0,\"noiseLevel\":0,\"RERL\":0,\"Gmin\":0,\"Rfactor\":0,\"extRfactor\":0,\"MOSLQ\":0,\"MOSCQ\":0,\"RXconfig\":0,\"JBnominal\":0,\"JBmax\":0,\"JBabsMax\":0},\"XRstatisticsSummary\":{\"begin_seq\":0,\"end_seq\":0,\"lost_packets\":0,\"dup_packets\":0,\"min_jitter\":0,\"max_jitter\":0,\"mean_jitter\":0,\"dev_jitter\":0,\"min_ttl_or_hl\":0,\"max_ttl_or_hl\":0,\"mean_ttl_or_hl\":0,\"dev_ttl_or_hl\":0},\"intervalCallQualityReport\":\"\",\"callStartTimeNTP\":0,\"endpoint\":{\"ipAddress\":\"\",\"port\":0}}],\"remoteAudioChannels\":[{\"sender_SSRC\":93,\"source_SSRC\":94,\"streamStatistics\":{\"fractionLost\":32,\"cumulativeLost\":30,\"extendedMax\":31,\"jitterSamples\":33,\"rttMs\":34},\"XRvoipMetrics\":{\"lossRate\":115,\"discardRate\":60,\"burstDensity\":58,\"gapDensity\":63,\"burstDuration\":59,\"gapDuration\":64,\"roundTripDelay\":121,\"endSystemDelay\":61,\"signalLevel\":123,\"noiseLevel\":118,\"RERL\":119,\"Gmin\":65,\"Rfactor\":120,\"extRfactor\":62,\"MOSLQ\":117,\"MOSCQ\":116,\"RXconfig\":122,\"JBnominal\":114,\"JBmax\":113,\"JBabsMax\":66},\"XRstatisticsSummary\":{\"begin_seq\":76,\"end_seq\":80,\"lost_packets\":81,\"dup_packets\":79,\"min_jitter\":128,\"max_jitter\":82,\"mean_jitter\":84,\"dev_jitter\":77,\"min_ttl_or_hl\":129,\"max_ttl_or_hl\":83,\"mean_ttl_or_hl\":127,\"dev_ttl_or_hl\":78},\"endpoint\":{\"ipAddress\":\"*******\",\"port\":86},\"lastRtcpReceived\":130,\"lastRtcpXrReceived\":132,\"lastSenderReportReceived\":133},{\"sender_SSRC\":0,\"source_SSRC\":0,\"streamStatistics\":{\"fractionLost\":0,\"cumulativeLost\":0,\"extendedMax\":0,\"jitterSamples\":0,\"rttMs\":0},\"XRvoipMetrics\":{\"lossRate\":0,\"discardRate\":0,\"burstDensity\":0,\"gapDensity\":0,\"burstDuration\":0,\"gapDuration\":0,\"roundTripDelay\":0,\"endSystemDelay\":0,\"signalLevel\":0,\"noiseLevel\":0,\"RERL\":0,\"Gmin\":0,\"Rfactor\":0,\"extRfactor\":0,\"MOSLQ\":0,\"MOSCQ\":0,\"RXconfig\":0,\"JBnominal\":0,\"JBmax\":0,\"JBabsMax\":0},\"XRstatisticsSummary\":{\"begin_seq\":0,\"end_seq\":0,\"lost_packets\":0,\"dup_packets\":0,\"min_jitter\":0,\"max_jitter\":0,\"mean_jitter\":0,\"dev_jitter\":0,\"min_ttl_or_hl\":0,\"max_ttl_or_hl\":0,\"mean_ttl_or_hl\":0,\"dev_ttl_or_hl\":0},\"endpoint\":{\"ipAddress\":\"\",\"port\":0},\"lastRtcpReceived\":0,\"lastRtcpXrReceived\":0,\"lastSenderReportReceived\":0}],\"videoChannels\":[{\"encoder\":{\"plName\":\"plname3\",\"plType\":16,\"width\":18,\"height\":11,\"startBitrate\":17,\"maxBitrate\":12,\"minBitrate\":14,\"maxFramerate\":13,\"hadwareAccelerated\":true,\"priority\":0,\"displayName\":\"\"},\"decoder\":{\"plName\":\"plname4\",\"plType\":22,\"width\":24,\"height\":18,\"startBitrate\":23,\"maxBitrate\":19,\"minBitrate\":21,\"maxFramerate\":20,\"hadwareAccelerated\":true,\"priority\":0,\"displayName\":\"\"},\"streamStatistics\":{\"fractionLost\":37,\"cumulativeLost\":35,\"extendedMax\":36,\"jitterSamples\":38,\"rttMs\":39},\"streamDataCounters\":{\"bytesSent\":46,\"packetsSent\":48,\"bytesReceived\":45,\"packetsReceived\":47},\"totalBitrateSent\":99,\"videoBitrateSent\":100,\"fecBitrateSent\":97,\"nackBitrateSent\":98,\"discardedPackets\":96,\"currentTargetBitrate\":95,\"endpoint\":{\"ipAddress\":\"**********\",\"port\":87}},{\"encoder\":{\"plName\":\"\",\"plType\":0,\"width\":0,\"height\":0,\"startBitrate\":0,\"maxBitrate\":0,\"minBitrate\":0,\"maxFramerate\":0,\"hadwareAccelerated\":false,\"priority\":0,\"displayName\":\"\"},\"decoder\":{\"plName\":\"\",\"plType\":0,\"width\":0,\"height\":0,\"startBitrate\":0,\"maxBitrate\":0,\"minBitrate\":0,\"maxFramerate\":0,\"hadwareAccelerated\":false,\"priority\":0,\"displayName\":\"\"},\"streamStatistics\":{\"fractionLost\":0,\"cumulativeLost\":0,\"extendedMax\":0,\"jitterSamples\":0,\"rttMs\":0},\"streamDataCounters\":{\"bytesSent\":0,\"packetsSent\":0,\"bytesReceived\":0,\"packetsReceived\":0},\"totalBitrateSent\":0,\"videoBitrateSent\":0,\"fecBitrateSent\":0,\"nackBitrateSent\":0,\"discardedPackets\":0,\"currentTargetBitrate\":0,\"endpoint\":{\"ipAddress\":\"\",\"port\":0}}],\"remoteVideoChannels\":[{\"streamStatistics\":{\"fractionLost\":42,\"cumulativeLost\":40,\"extendedMax\":41,\"jitterSamples\":43,\"rttMs\":44},\"endpoint\":{\"ipAddress\":\"***********\",\"port\":88},\"lastRtcpReceived\":131,\"lastSenderReportReceived\":134},{\"streamStatistics\":{\"fractionLost\":0,\"cumulativeLost\":0,\"extendedMax\":0,\"jitterSamples\":0,\"rttMs\":0},\"endpoint\":{\"ipAddress\":\"\",\"port\":0},\"lastRtcpReceived\":0,\"lastSenderReportReceived\":0}],\"callQuality\":2,\"networkMos\":-1}";
   ASSERT_STREQ(expected_string, actual_string);

   JitterBufferStatistics jitBufStats;
   AudioJitterBufferStatistics ajbs, emptyAjbs;
   VideoJitterBufferStatistics vjbs, emptyVjbs;

   memset(&emptyAjbs, 0, sizeof(emptyAjbs));
   memset(&emptyVjbs, 0, sizeof(emptyVjbs));

   ajbs.addedSamples = 200;
   ajbs.clockDriftPPM = 201;
   ajbs.currentAccelerateRate = 202;
   ajbs.currentBufferSizeMs = 203;
   ajbs.currentDiscardRate = 204;
   ajbs.currentEffectivePacketLossRate = 205;
   ajbs.currentSynthesizedAudioInsertRate = 206;
   ajbs.currentSynthesizedAudioPreemptiveInsertRate = 207;
   ajbs.jitterBurstsFound = true;
   ajbs.maxWaitingTimeMs = 209;
   ajbs.meanWaitingTimeMs = 210;
   ajbs.medianWaitingTimeMs = 211;
   ajbs.minWaitingTimeMs = 212;
   ajbs.preferredBufferSizeMs = 213;
   vjbs.currentBufferSizeMs = 214;
   vjbs.currentDiscardRate = 215;
   vjbs.numDecodedDeltaFrames = 216;
   vjbs.numDecodedKeyFrames = 217;

   jitBufStats.audioChannels.push_back(ajbs);
   jitBufStats.audioChannels.push_back(emptyAjbs);
   jitBufStats.videoChannels.push_back(vjbs);
   jitBufStats.videoChannels.push_back(emptyVjbs);

   rapidjson::StringBuffer jitBufStatsStringBuffer;
   rapidjson::Writer<rapidjson::StringBuffer> writer2(jitBufStatsStringBuffer);
   CPCAPI2::Json::Serialize(writer2, jitBufStats);


   actual_string = jitBufStatsStringBuffer.GetString();
   expected_string = "{\"audioChannels\":[{\"currentBufferSizeMs\":203,\"preferredBufferSizeMs\":213,\"jitterBurstsFound\":true,\"currentEffectiv\
ePacketLossRate\":205,\"currentDiscardRate\":204,\"currentSynthesizedAudioInsertRate\":206,\"currentSynthesizedAudioPreempti\
veInsertRate\":207,\"currentAccelerateRate\":202,\"clockDriftPPM\":201,\"meanWaitingTimeMs\":210,\"medianWaitingTimeMs\":211\
,\"minWaitingTimeMs\":212,\"maxWaitingTimeMs\":209,\"addedSamples\":200},{\"currentBufferSizeMs\":0,\"preferredBufferSizeMs\\
":0,\"jitterBurstsFound\":false,\"currentEffectivePacketLossRate\":0,\"currentDiscardRate\":0,\"currentSynthesizedAudioInser\
tRate\":0,\"currentSynthesizedAudioPreemptiveInsertRate\":0,\"currentAccelerateRate\":0,\"clockDriftPPM\":0,\"meanWaitingTim\
eMs\":0,\"medianWaitingTimeMs\":0,\"minWaitingTimeMs\":0,\"maxWaitingTimeMs\":0,\"addedSamples\":0}],\"videoChannels\":[{\"n\
umDecodedKeyFrames\":217,\"numDecodedDeltaFrames\":216,\"currentBufferSizeMs\":214,\"currentDiscardRate\":215},{\"numDecoded\
KeyFrames\":0,\"numDecodedDeltaFrames\":0,\"currentBufferSizeMs\":0,\"currentDiscardRate\":0}]}";
   ASSERT_STREQ(expected_string, actual_string);
}
