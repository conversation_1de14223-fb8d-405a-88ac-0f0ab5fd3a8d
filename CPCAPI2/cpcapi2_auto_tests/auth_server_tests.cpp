#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#if (CPCAPI2_BRAND_AUTH_SERVER_MODULE == 1)

#include "cpcapi2_test_fixture.h"
#include "test_call_events.h"
#include "test_events.h"
#include "test_account_events.h"

#include "../../impl/util/CurlPPHelper.h"
#include "../../impl/util/CurlPPProgress.h"
#include "../../impl/util/CurlPPSSL.h"
#include "../../impl/auth_server/AuthServerJwtUtils.h"
#include "../../impl/auth_server/AuthServerDbAccess.h"

#include <auth_server/AuthServer.h>

// rapidjson
#include <writer.h>
#include <stringbuffer.h>
#include <document.h>

using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::AuthServer;

class AuthServerTests : public CpcapiAutoTest
{
public:
   AuthServerTests() {}
   virtual ~AuthServerTests() {}

   static CPCAPI2::AuthServer::AuthServerConfig setUpAuthServerConfig()
   {
      CPCAPI2::AuthServer::AuthServerConfig authServerConfig;
      authServerConfig.certificateFilePath = TestEnvironmentConfig::testResourcePath() + "p256-private-key-unit-tests.p8";
      authServerConfig.httpsCertificateFilePath = TestEnvironmentConfig::testResourcePath() + "cert.pem";
      authServerConfig.httpsPrivateKeyFilePath = TestEnvironmentConfig::testResourcePath() + "privkey.pem";
      authServerConfig.httpsDiffieHellmanParamsFilePath = TestEnvironmentConfig::testResourcePath() + "dh2048.pem";
      authServerConfig.numThreads = 4;
      authServerConfig.port = 18080;
      return authServerConfig;
   }

   static void setUpRequest(curlpp::Easy &request, std::stringstream &responseBody, const std::stringstream &url, const std::string &messageBody)
   {
      CurlPPHelper helper;
      helper.setDefaultOptions(request, url.str(), "POST", messageBody.size());
      request.setOpt(new curlpp::options::PostFields(std::string(messageBody.c_str(), messageBody.size())));

      CurlPPSSL cssl(SslCipherOptions(), 0, TestEnvironmentConfig::testResourcePath());
      request.setOpt(new curlpp::options::SslCtxFunction(cssl));

      request.setOpt(new curlpp::options::WriteStream(&responseBody));
   }

   static void gracefulShutdown(TestAccount *testAccount)
   {
      testAccount->authServer->shutdown();

      {
         CPCAPI2::AuthServer::DbAccess db;
         db.initialize("authserver.db");
         ASSERT_EQ(db.flushUsers(), 0);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   }

   static std::string setUpMessageBodyStr(const std::string& username, const std::string& password, std::string pubkey = "") {
      std::stringstream result;
      result << "{ \"username\": \"" << username << "\", \"password\": \"" << password; // memory needs to hang around until after perform
      
      if (!pubkey.empty()) {
         result << "\",  \"pubKey\": \"" << pubkey;
      }
      result << "\" }";

      result.flush();
      return result.str();
   }

private:
};

TEST_F(AuthServerTests, AuthSuccess)
{
   {
      CPCAPI2::AuthServer::DbAccess db;
      db.initialize("authserver.db");
      ASSERT_EQ(db.addUser("jgeras5", "5e884898da28047151d0e56f8dc6292773603d0d6aabbdd62a11ef721d1542d8"), 0);
   }

   // Max is the remote SDK
   TestAccount max("max", Account_Init);
   auto authServerConfig = setUpAuthServerConfig();
   max.authServer->start(authServerConfig);

   AuthServerStartedEvent evt;
   int dummyHandle = 0;
   max.authServerEvents->expectEvent(__LINE__, "AuthServerHandler::onAuthServerStarted",
                                     __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), dummyHandle, evt);

   try
   {
      curlpp::Easy request;
      auto messageBody = AuthServerTests::setUpMessageBodyStr("jgeras5", "5e884898da28047151d0e56f8dc6292773603d0d6aabbdd62a11ef721d1542d8"); // memory needs to hang around until after perform
      std::stringstream url;
      url << "https://127.0.0.1:18080/login_v1";

      std::stringstream responseBody;
      setUpRequest(request, responseBody, url, messageBody);

      EXPECT_NO_THROW(request.perform());

      ASSERT_EQ(curlpp::infos::ResponseCode::get(request), 200);
      // ASSERT_EQ(messageBody, responseBody.str());

      std::shared_ptr<rapidjson::Document> jsonRequest(new rapidjson::Document);
      jsonRequest->Parse<0>(responseBody.str().c_str());
      ASSERT_FALSE(jsonRequest->HasParseError());
      ASSERT_TRUE(jsonRequest->HasMember("token"));
      const rapidjson::Value &tokenVal = (*jsonRequest)["token"];
      ASSERT_TRUE(tokenVal.IsString());
      ASSERT_TRUE(tokenVal.GetStringLength() > 0);

      resip::Data tokenData(tokenVal.GetString(), tokenVal.GetStringLength());
      bool isValid = false;
      std::map<resip::Data, resip::Data> pubClaims;
      ASSERT_EQ(CPCAPI2::AuthServer::JwtUtils::VerifyJWT((TestEnvironmentConfig::testResourcePath() + "p256-public-key-unit-tests.spki").c_str(), tokenData, isValid, &pubClaims), 0);
      ASSERT_TRUE(isValid);
      ASSERT_NE(pubClaims.size(), 0);
   }
   catch (curlpp::RuntimeError &e)
   {
      std::cerr << "Runtime Error: " << e.what();
   }

   gracefulShutdown(&max);
}

TEST_F(AuthServerTests, AddUserSuccess)
{
   // Max is the remote SDK
   TestAccount max("max", Account_Init);
   auto authServerConfig = setUpAuthServerConfig();
   max.authServer->start(authServerConfig);

   AuthServerStartedEvent evt;
   int dummyHandle = 0;
   max.authServerEvents->expectEvent(__LINE__, "AuthServerHandler::onAuthServerStarted",
                                     __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), dummyHandle, evt);

   try
   {
      curlpp::Easy request;
      resip::Data randomUsername = resip::Random::getCryptoRandomBase64(8);
      auto messageBodyStr = setUpMessageBodyStr(randomUsername.c_str(), "5e884898da28047151d0e56f8dc6292773603d0d6aabbdd62a11ef721d1542d8"); // memory needs to hang around until after perform
      resip::Data messageBody;
      ASSERT_EQ(CPCAPI2::AuthServer::JwtUtils::Encrypt((TestEnvironmentConfig::testResourcePath() + "p256-public-key-unit-tests.spki").c_str(), resip::Data(messageBodyStr), messageBody, true), 0);

      std::stringstream url;
      url << "https://127.0.0.1:18080/addUser";

      std::stringstream responseBody;
      setUpRequest(request, responseBody, url, std::string(messageBody.c_str(), messageBody.size()));
      request.perform();

      ASSERT_EQ(curlpp::infos::ResponseCode::get(request), 201);
      // ASSERT_EQ(messageBody, responseBody.str());
   }
   catch (curlpp::RuntimeError &e)
   {
      std::cerr << "Runtime Error: " << e.what();
   }

   gracefulShutdown(&max);
}

TEST_F(AuthServerTests, AddUserSuccessWithPubKey)
{
   // Max is the remote SDK
   TestAccount max("max", Account_Init);
   auto authServerConfig = setUpAuthServerConfig();
   max.authServer->start(authServerConfig);

   AuthServerStartedEvent evt;
   int dummyHandle = 0;
   max.authServerEvents->expectEvent(__LINE__, "AuthServerHandler::onAuthServerStarted",
                                     __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), dummyHandle, evt);

   try
   {
      curlpp::Easy request;
      resip::Data randomUsername = resip::Random::getCryptoRandomBase64(8);
      auto messageBodyStr = setUpMessageBodyStr(randomUsername.c_str(), "5e884898da28047151d0e56f8dc6292773603d0d6aabbdd62a11ef721d1542d8", "-----BEGIN PUBLIC KEY-----\\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiKzxgDrW3u9+XzGN4r/GI5o+BMtt\\ny+63OT4lwEgFlkpedq5FY3eCzI4lB1WXDVip0OpCM1zYoCh6mqi7KaS1Vg==\\n-----END PUBLIC KEY-----\\n");
      resip::Data messageBody = messageBodyStr.c_str();

      std::stringstream url;
      url << "https://127.0.0.1:18080/addUser";

      std::stringstream responseBody;
      setUpRequest(request, responseBody, url, std::string(messageBody.c_str(), messageBody.size()));
      request.perform();

      ASSERT_EQ(curlpp::infos::ResponseCode::get(request), 201);
      // ASSERT_EQ(messageBody, responseBody.str());
   }
   catch (curlpp::RuntimeError &e)
   {
      std::cerr << "Runtime Error: " << e.what();
   }

   gracefulShutdown(&max);
}

TEST_F(AuthServerTests, AddUserFailBadPubKey)
{
   // Max is the remote SDK
   TestAccount max("max", Account_Init);
   auto authServerConfig = setUpAuthServerConfig();
   max.authServer->start(authServerConfig);

   AuthServerStartedEvent evt;
   int dummyHandle = 0;
   max.authServerEvents->expectEvent(__LINE__, "AuthServerHandler::onAuthServerStarted",
                                     __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), dummyHandle, evt);

   try
   {
      curlpp::Easy request;
      resip::Data randomUsername = resip::Random::getCryptoRandomBase64(8);
      auto messageBodyStr = setUpMessageBodyStr(randomUsername.c_str(), "5e884898da28047151d0e56f8dc6292773603d0d6aabbdd62a11ef721d1542d8", "-----BEGIN PUBLIC KEY-----\\nMXkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiKzxgDrW3u9+XzGN4r/GI5o+BMtt\\ny+63OT4lwEgFlkpedq5FY3eCzI4lB1WXDVip0OpCM1zYoCh6mqi7KaS1Vg==\\n-----END PUBLIC KEY-----\\n");
      resip::Data messageBody = messageBodyStr.c_str();

      std::stringstream url;
      url << "https://127.0.0.1:18080/addUser";

      std::stringstream responseBody;
      setUpRequest(request, responseBody, url, std::string(messageBody.c_str(), messageBody.size()));
      request.perform();

      ASSERT_EQ(400, curlpp::infos::ResponseCode::get(request));
      // ASSERT_EQ(messageBody, responseBody.str());
   }
   catch (curlpp::RuntimeError &e)
   {
      std::cerr << "Runtime Error: " << e.what();
   }

   gracefulShutdown(&max);
}

TEST_F(AuthServerTests, AddUserFailUnencryptedNoPubKey)
{
   // Max is the remote SDK
   TestAccount max("max", Account_Init);
   auto authServerConfig = setUpAuthServerConfig();
   max.authServer->start(authServerConfig);

   AuthServerStartedEvent evt;
   int dummyHandle = 0;
   max.authServerEvents->expectEvent(__LINE__, "AuthServerHandler::onAuthServerStarted",
                                     __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), dummyHandle, evt);

   try
   {
      CurlPPHelper helper;
      curlpp::Easy request;
      resip::Data randomUsername = resip::Random::getCryptoRandomBase64(8);
      auto messageBodyStr = setUpMessageBodyStr(randomUsername.c_str(), "5e884898da28047151d0e56f8dc6292773603d0d6aabbdd62a11ef721d1542d8"); // memory needs to hang around until after perform
      resip::Data messageBody = messageBodyStr.c_str();

      std::stringstream url;
      url << "https://127.0.0.1:18080/addUser";

      std::stringstream responseBody;
      setUpRequest(request, responseBody, url, std::string(messageBody.c_str(), messageBody.size()));
      request.perform();

      ASSERT_EQ(400, curlpp::infos::ResponseCode::get(request));
      // ASSERT_EQ(messageBody, responseBody.str());
   }
   catch (curlpp::RuntimeError &e)
   {
      std::cerr << "Runtime Error: " << e.what();
   }

   gracefulShutdown(&max);
}

TEST_F(AuthServerTests, AddUserPasswordChanged)
{

   // User was added with old password previously
   {
      CPCAPI2::AuthServer::DbAccess db;
      db.initialize("authserver.db");
      ASSERT_EQ(db.addUser("test", "1234"), 0);
   }

   // Max is the remote SDK
   TestAccount max("max", Account_Init);
   auto authServerConfig = setUpAuthServerConfig();
   max.authServer->start(authServerConfig);

   AuthServerStartedEvent evt;
   int dummyHandle = 0;
   max.authServerEvents->expectEvent(__LINE__, "AuthServerHandler::onAuthServerStarted",
                                     __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), dummyHandle, evt);

   try
   {
      curlpp::Easy request;
      auto messageBodyStr = setUpMessageBodyStr("test", "5678"); // memory needs to hang around until after perform
      resip::Data messageBody;
      ASSERT_EQ(CPCAPI2::AuthServer::JwtUtils::Encrypt((TestEnvironmentConfig::testResourcePath() + "p256-public-key-unit-tests.spki").c_str(), resip::Data(messageBodyStr), messageBody, true), 0);

      std::stringstream url;
      url << "https://127.0.0.1:18080/addUser";

      std::stringstream responseBody;
      setUpRequest(request, responseBody, url, std::string(messageBody.c_str(), messageBody.size()));
      request.perform();

      ASSERT_EQ(curlpp::infos::ResponseCode::get(request), 201);
      // ASSERT_EQ(messageBody, responseBody.str());
   }
   catch (curlpp::RuntimeError &e)
   {
      std::cerr << "Runtime Error: " << e.what();
   }

   // Check that login with new password succeeds
   try
   {
      curlpp::Easy request;
      auto messageBody = setUpMessageBodyStr("test", "5678"); // memory needs to hang around until after perform
      std::stringstream url;
      url << "https://127.0.0.1:18080/login_v1";

      std::stringstream responseBody;
      setUpRequest(request, responseBody, url, messageBody);
      EXPECT_NO_THROW(request.perform());

      ASSERT_EQ(curlpp::infos::ResponseCode::get(request), 200);
      // ASSERT_EQ(messageBody, responseBody.str());

      std::shared_ptr<rapidjson::Document> jsonRequest(new rapidjson::Document);
      jsonRequest->Parse<0>(responseBody.str().c_str());
      ASSERT_FALSE(jsonRequest->HasParseError());
      ASSERT_TRUE(jsonRequest->HasMember("token"));
      const rapidjson::Value &tokenVal = (*jsonRequest)["token"];
      ASSERT_TRUE(tokenVal.IsString());
      ASSERT_TRUE(tokenVal.GetStringLength() > 0);

      resip::Data tokenData(tokenVal.GetString(), tokenVal.GetStringLength());
      bool isValid = false;
      std::map<resip::Data, resip::Data> pubClaims;
      ASSERT_EQ(CPCAPI2::AuthServer::JwtUtils::VerifyJWT((TestEnvironmentConfig::testResourcePath() + "p256-public-key-unit-tests.spki").c_str(), tokenData, isValid, &pubClaims), 0);
      ASSERT_TRUE(isValid);
      ASSERT_NE(pubClaims.size(), 0);
   }
   catch (curlpp::RuntimeError &e)
   {
      std::cerr << "Runtime Error: " << e.what();
   }

   gracefulShutdown(&max);
}

std::mutex globalAuthV2Mutex;
void doAuthSuccessV2Client()
{
   try
   {
      // CurlPPHelper helper;
      curlpp::Easy request;
      auto messageBodyStr = AuthServerTests::setUpMessageBodyStr("jgeras5", "5e884898da28047151d0e56f8dc6292773603d0d6aabbdd62a11ef721d1542d8"); // memory needs to hang around until after perform
      resip::Data messageBody;
      ASSERT_EQ(CPCAPI2::AuthServer::JwtUtils::Encrypt((TestEnvironmentConfig::testResourcePath() + "p256-public-key-unit-tests.spki").c_str(), resip::Data(messageBodyStr), messageBody, true), 0);

      std::stringstream url;
      url << "https://127.0.0.1:18080/login_v2";

      std::stringstream responseBody;
      AuthServerTests::setUpRequest(request, responseBody, url, std::string(messageBody.c_str(), messageBody.size()));

      {
         std::lock_guard guard(globalAuthV2Mutex);
         EXPECT_NO_THROW(request.perform());
      }
      

      ASSERT_EQ(curlpp::infos::ResponseCode::get(request), 200);
      // ASSERT_EQ(messageBody, responseBody.str());

      std::shared_ptr<rapidjson::Document> jsonRequest(new rapidjson::Document);
      jsonRequest->Parse<0>(responseBody.str().c_str());
      ASSERT_FALSE(jsonRequest->HasParseError());
      ASSERT_TRUE(jsonRequest->HasMember("token"));
      const rapidjson::Value &tokenVal = (*jsonRequest)["token"];
      ASSERT_TRUE(tokenVal.IsString());
      ASSERT_TRUE(tokenVal.GetStringLength() > 0);

      resip::Data tokenData(tokenVal.GetString(), tokenVal.GetStringLength());
      bool isValid = false;
      std::map<resip::Data, resip::Data> pubClaims;
      ASSERT_EQ(CPCAPI2::AuthServer::JwtUtils::VerifyJWT((TestEnvironmentConfig::testResourcePath() + "p256-public-key-unit-tests.spki").c_str(), tokenData, isValid, &pubClaims), 0);
      ASSERT_TRUE(isValid);
      ASSERT_NE(pubClaims.size(), 0);
   }
   catch (curlpp::RuntimeError &e)
   {
      std::cerr << "Runtime Error: " << e.what();
   }
}

TEST_F(AuthServerTests, AuthSuccessV2)
{
   // Max is the remote SDK
   TestAccount max("max", Account_Init);
   auto authServerConfig = setUpAuthServerConfig();
   max.authServer->start(authServerConfig);

   AuthServerStartedEvent evt;
   int dummyHandle = 0;
   max.authServerEvents->expectEvent(__LINE__, "AuthServerHandler::onAuthServerStarted",
                                     __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), dummyHandle, evt);

   doAuthSuccessV2Client();

   gracefulShutdown(&max);
}

TEST_F(AuthServerTests, MultithreadAuthSuccessV2)
{
   // Max is the remote SDK
   TestAccount max("max", Account_Init);
   auto authServerConfig = setUpAuthServerConfig();
   max.authServer->start(authServerConfig);

   AuthServerStartedEvent evt;
   int dummyHandle = 0;
   max.authServerEvents->expectEvent(__LINE__, "AuthServerHandler::onAuthServerStarted",
                                     __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), dummyHandle, evt);

   const int numThreads = 10;
   std::vector<std::thread *> threads;
   for (int i = 0; i < numThreads; i++)
   {
      threads.push_back(new std::thread([&]()
                                        { doAuthSuccessV2Client(); }));
   }

   for (std::thread *t : threads)
   {
      t->join();
   }
   gracefulShutdown(&max);
}

struct EncryptDecryptTestParam
{
   std::string plaintext;
   bool useBase64;
};

class AuthServerEncryptDecryptTests : public CpcapiAutoTestWithParam<EncryptDecryptTestParam>
{
};

EncryptDecryptTestParam kEncryptDecryptTestParams[] = {{std::string("123456"), false},
                                                       {std::string("123456"), true},
                                                       {std::string("@#$!@$%#fasdfjas;da"), false},
                                                       {std::string("@#$!@$%#fasdfjas;da"), true},
                                                       {std::string("n9m6hAo8pfzEH8VExgeNRqzQNj76eOtaldQn10AIHU6IWUkWD7EPM4uXvPECvZceGAfoY17XhBNla32G8B71xPq2xlW0oyv42pcw6L736J34y2QCU8nXhWIPSJZAPXFY"), true},
                                                       {std::string("n9m6hAo8pfzEH8VExgeNRqzQNj76eOtaldQn10AIHU6IWUkWD7EPM4uXvPECvZceGAfoY17XhBNla32G8B71xPq2xlW0oyv42pcw6L736J34y2QCU8nXhWIPSJZAPXFY"), true}};

INSTANTIATE_TEST_SUITE_P(AuthServerVariousEncryptDecrypt, AuthServerEncryptDecryptTests,
                         ::testing::ValuesIn(kEncryptDecryptTestParams));

TEST_P(AuthServerEncryptDecryptTests, ParamaterizedEncryptDecrypt)
{

   const EncryptDecryptTestParam testParam = GetParam();

   resip::Data encryptedData;
   CPCAPI2::AuthServer::JwtUtils::Encrypt((TestEnvironmentConfig::testResourcePath() + "p256-public-key-unit-tests.spki").c_str(), testParam.plaintext.c_str(), encryptedData, testParam.useBase64);

   resip::Data outData;
   CPCAPI2::AuthServer::JwtUtils::Decrypt((TestEnvironmentConfig::testResourcePath() + "p256-private-key-unit-tests.p8").c_str(), encryptedData, outData);

   ASSERT_EQ(testParam.plaintext.c_str(), outData);
}

#endif
