#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#include "cpcapi2_test_fixture.h"
#include "test_call_events.h"
#include "test_events.h"

using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::SipConversation;
using namespace CPCAPI2::SipAccount;

class CallSanityTests : public CpcapiAutoTest
{
public:
   CallSanityTests() {}
   virtual ~CallSanityTests() {}
};

TEST_F(CallSanityTests, MissingSetConversationHandler) {
   TestAccount alice("alice", Account_NoInit);
   alice.config.registerHandlers = false;
   alice.init();
   alice.account->setHandler(alice.handle, (SipAccountHandler*)0xDEADBEEF); 
   alice.enable();

   // now set the handler for conversation TOO LATE (after account is enabled)
   alice.conversation->setHandler(alice.handle, (SipConversationHandler*)0xDEADBEEF);
   assertPhoneError_ex(alice, "SipAccountInterface", [](const PhoneErrorEvent& evt){
      // error should contain information about the failed method
      ASSERT_TRUE(evt.errorText.find("setHandler") >= 0);
   });
}

TEST_F(CallSanityTests, RemoveConversationHandler) {
   
   Phone* phone = Phone::create();
   phone->initialize(LicenseInfo(), (PhoneHandler*)NULL);
   
   CPCAPI2::Media::MediaManager::getInterface(phone)->initializeMediaStack();
   
   SipAccountManager* acct = SipAccountManager::getInterface(phone);
   
   TestAccountConfig config("alice");
   SipAccountHandle accountHandle = acct->create(config.settings);
   
   class MySipConversationHandler : public SipConversationHandler
   {
   public:
      MySipConversationHandler() : receivedEvent(false) {}
      
      bool receivedEvent;
      
      int onNewConversation(SipConversationHandle conversation, const NewConversationEvent& args) { receivedEvent = true; return 0; }
      int onConversationEnded(SipConversationHandle conversation, const ConversationEndedEvent& args) { return 0; }
      int onIncomingTransferRequest(SipConversationHandle conversation, const TransferRequestEvent& args) { return 0; }
      int onIncomingRedirectRequest(SipConversationHandle conversation, const RedirectRequestEvent& args) { return 0; }
      int onIncomingTargetChangeRequest(SipConversationHandle conversation, const TargetChangeRequestEvent& args) { return 0; }
      int onIncomingHangupRequest(SipConversationHandle conversation, const HangupRequestEvent& args) { return 0; }
      int onIncomingBroadsoftTalkRequest(SipConversationHandle conversation, const BroadsoftTalkEvent& args) { return 0; }
      int onIncomingBroadsoftHoldRequest(SipConversationHandle conversation, const BroadsoftHoldEvent& args) { return 0; }
      int onTransferProgress(SipConversationHandle conversation, const TransferProgressEvent& args) { return 0; }
      int onConversationStateChangeRequest(SipConversationHandle conversation, const ConversationStateChangeRequestEvent& args) { return 0; }
      int onConversationStateChanged(SipConversationHandle conversation, const ConversationStateChangedEvent& args) { return 0; }
      int onConversationMediaChangeRequest(SipConversationHandle conversation, const ConversationMediaChangeRequestEvent& args) { return 0; }
      int onConversationMediaChanged(SipConversationHandle conversation, const ConversationMediaChangedEvent& args) { return 0; }
      int onConversationStatisticsUpdated(SipConversationHandle conversation, const ConversationStatisticsUpdatedEvent& args) { return 0; }
      int onAudioDeviceLevelChange(SipConversationHandle conversation, const ConversationAudioDeviceLevelChangeEvent& args) { return 0; }
      int onError(SipConversationHandle conversation, const SipConversation::ErrorEvent& args) { return 0; }
   };
   
   MySipConversationHandler* accountHandler = new MySipConversationHandler();
   
   SipConversationManager* convMgr = SipConversationManager::getInterface(phone);
   convMgr->setHandler(accountHandle, accountHandler);
   acct->enable(accountHandle);
   
   
   SipConversationHandle conversation = convMgr->createConversation(accountHandle);
   convMgr->addParticipant(conversation, "sip:bogus28342398");
   convMgr->start(conversation);
   convMgr->end(conversation);
   
   auto start = std::chrono::high_resolution_clock::now();
   
   std::atomic_bool threadStopFlag(false);
   auto callEvent = std::async(std::launch::async, [&] ()
   {
      while (accountHandler->receivedEvent == false)
      {
         phone->process(SipAccountManager::kBlockingModeNonBlocking);

         if (threadStopFlag) return;

         std::this_thread::sleep_for(std::chrono::milliseconds(100));
      }
   });
   
   flaggableWaitFor(callEvent, threadStopFlag);
   auto end = std::chrono::high_resolution_clock::now();
   
   conversation = convMgr->createConversation(accountHandle);
   convMgr->addParticipant(conversation, "sip:bogus28342398");
   convMgr->start(conversation);
   convMgr->end(conversation);
   
   convMgr->setHandler(accountHandle, NULL);
   delete accountHandler;
   
   acct->disable(accountHandle);
   
   std::chrono::milliseconds w = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
   w = (2 * w) + std::chrono::milliseconds(1000);
   
   // wait about as long as it took before
   std::this_thread::sleep_for(w);
   
   phone->process(1000);
   
   Phone::release(phone);
}

// using a lambda instead causes compiler internal failures with VS2017
static void PhoneCbHook(void* context)
{
   Phone* p = reinterpret_cast<Phone*>(context);
   p->process(Phone::kBlockingModeNonBlocking);
}

TEST_F(CallSanityTests, CallbackHook) {
   
   Phone* phone = Phone::create();
   LicenseInfo li;
   phone->initialize(li, (PhoneHandler*)NULL, SslCipherOptions());

   static_cast<PhoneInternal*>(phone)->setCallbackHook(PhoneCbHook, phone);
   
   
   CPCAPI2::Media::MediaManager::getInterface(phone)->initializeMediaStack();
   
   SipAccountManager* acct = SipAccountManager::getInterface(phone);
   
   TestAccountConfig config("alice");
   SipAccountHandle accountHandle = acct->create(config.settings);
   
   class MySipConversationHandler : public SipConversationHandler
   {
   public:
      MySipConversationHandler() : receivedEvent(false) {}
      
      std::atomic_bool receivedEvent;
      
      int onNewConversation(SipConversationHandle conversation, const NewConversationEvent& args) { receivedEvent = true; return 0; }
      int onConversationEnded(SipConversationHandle conversation, const ConversationEndedEvent& args) { return 0; }
      int onIncomingTransferRequest(SipConversationHandle conversation, const TransferRequestEvent& args) { return 0; }
      int onIncomingRedirectRequest(SipConversationHandle conversation, const RedirectRequestEvent& args) { return 0; }
      int onIncomingTargetChangeRequest(SipConversationHandle conversation, const TargetChangeRequestEvent& args) { return 0; }
      int onIncomingHangupRequest(SipConversationHandle conversation, const HangupRequestEvent& args) { return 0; }
      int onIncomingBroadsoftTalkRequest(SipConversationHandle conversation, const BroadsoftTalkEvent& args) { return 0; }
      int onIncomingBroadsoftHoldRequest(SipConversationHandle conversation, const BroadsoftHoldEvent& args) { return 0; }
      int onTransferProgress(SipConversationHandle conversation, const TransferProgressEvent& args) { return 0; }
      int onConversationStateChangeRequest(SipConversationHandle conversation, const ConversationStateChangeRequestEvent& args) { return 0; }
      int onConversationStateChanged(SipConversationHandle conversation, const ConversationStateChangedEvent& args) { return 0; }
      int onConversationMediaChangeRequest(SipConversationHandle conversation, const ConversationMediaChangeRequestEvent& args) { return 0; }
      int onConversationMediaChanged(SipConversationHandle conversation, const ConversationMediaChangedEvent& args) { return 0; }
      int onConversationStatisticsUpdated(SipConversationHandle conversation, const ConversationStatisticsUpdatedEvent& args) { return 0; }
      int onAudioDeviceLevelChange(SipConversationHandle conversation, const ConversationAudioDeviceLevelChangeEvent& args) { return 0; }
      int onError(SipConversationHandle conversation, const SipConversation::ErrorEvent& args) { return 0; }
   };
   
   std::unique_ptr<MySipConversationHandler> accountHandler = std::make_unique<MySipConversationHandler>();
   
   SipConversationManager* convMgr = SipConversationManager::getInterface(phone);
   convMgr->setHandler(accountHandle, accountHandler.get());
   acct->enable(accountHandle);
   
   
   SipConversationHandle conversation = convMgr->createConversation(accountHandle);
   convMgr->addParticipant(conversation, "sip:bogus28342398");
   convMgr->start(conversation);
   convMgr->end(conversation);

   for (int i = 0; i < 10; ++i)
   {
      if (accountHandler->receivedEvent)
      {
         break;
      }
      else
      {
         std::this_thread::sleep_for(std::chrono::seconds(1));
      }
   }
   
   ASSERT_TRUE(accountHandler->receivedEvent);
   
   Phone::release(phone);
}

TEST_F(CallSanityTests, DisabledAccountCreateConversation) {
   TestAccount alice("alice", Account_Init);
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   assertPhoneError(alice, "SipAccountInterface");
}

TEST_F(CallSanityTests, StartWithNoParticipants) {
   TestAccount alice("alice");
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
	alice.conversation->start(aliceCall);
   assertConversationError(alice, aliceCall);
}

TEST_F(CallSanityTests, SdkShutdownDuringCall) {
   const int call_duration_ms = 10000;

   TestAccount* alicePtr = new TestAccount("alice");
   TestAccount& alice = *alicePtr;
   TestAccount* bobPtr = new TestAccount("bob");
   TestAccount& bob = *bobPtr;

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   // spawn a thread for Bob via std::async; both Bob and Alice will have events firing
   // at about the same time, and the ordering of those events is only interesting on a
   // per-person basis -- so Bob gets his own thread, and Alice gets her own thread

   // Overview of Bob's thread:
   //  - wait for onNewConversation (triggered when Bob gets the incoming INVITE)
   //  - send 180 ringing
   //  - wait for onConversationStateChanged (LocalRinging)
   //  - answer the call (200 OK)
   //  - wait for onConversationMediaChanged (Audio -> SendReceive)
   //  - wait for onConversationStateChanged (Connected)
   //  - end the call (BYE)
   //  - wait for onConversationEnded
   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall = 0;
      {
         SipConversationHandle h;
         NewConversationEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
            "SipConversationHandler::onNewConversation",
            5000,
            AlwaysTruePred(),
            h, evt));
         bobCall = h;
         ASSERT_EQ(evt.account, bob.handle);
         ASSERT_EQ(evt.conversationState, ConversationState_RemoteOriginated);
         ASSERT_EQ(evt.conversationType, ConversationType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
         ASSERT_EQ(evt.remoteDisplayName, alice.config.name);
         ASSERT_EQ(evt.remoteMediaInfo.size(), 1);
      }

      // Bob sends 180 ringing -- this should trigger state transitions for both Bob and Alice
      ASSERT_EQ(bob.conversation->sendRingingResponse(bobCall), kSuccess);

      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
            "SipConversationHandler::onConversationStateChanged",
            5000,
            HandleEqualsPred<SipConversationHandle>(bobCall),
            h, evt));
         ASSERT_EQ(evt.conversationState, ConversationState_LocalRinging);
      }

      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      ASSERT_EQ(bob.conversation->accept(bobCall), kSuccess);

      {
         SipConversationHandle h;
         ConversationMediaChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
            "SipConversationHandler::onConversationMediaChanged",
            15000,
            HandleEqualsPred<SipConversationHandle>(bobCall),
            h, evt));
         ASSERT_EQ(evt.localMediaInfo.size(),1);
         ASSERT_EQ(evt.localMediaInfo[0].mediaType, MediaType_Audio);
         ASSERT_EQ(evt.localMediaInfo[0].mediaDirection, MediaDirection_SendReceive);
         std::string strLocalPlname(evt.localMediaInfo[0].audioCodec.plname);
         //ASSERT_EQ(strLocalPlname, "opus");
         std::string strRemotePlname(evt.remoteMediaInfo[0].audioCodec.plname);
         //ASSERT_EQ(strRemotePlname, "opus");
      }

      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
            "SipConversationHandler::onConversationStateChanged",
            5000,
            HandleEqualsPred<SipConversationHandle>(bobCall),
            h, evt));
         ASSERT_EQ(evt.conversationState, ConversationState_Connected);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(call_duration_ms));

      delete bobPtr;

      //ASSERT_EQ(bob.conversation->end(bobCall), kSuccess);

      //{
      //   SipConversationHandle h;
      //   ConversationEndedEvent evt;
      //   ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
      //      "SipConversationHandler::onConversationEnded",
      //      5000,
      //      HandleEqualsPred<SipConversationHandle>(bobCall),
      //      h, evt));
      //   ASSERT_EQ(evt.endReason, ConversationEndReason_UserTerminatedLocally);
      //}
   });

   // Overview of Alice's thread:
   //  - wait for onNewConversation (triggered as soon as Alice sends the INVITE)
   //  - wait for onConversationStateChanged (RemoteRinging) (triggered when Alice receives the 180 from Bob)
   //  - wait for onConversationMediaChanged (Audio -> SendReceive) (triggered when Alice receives the 200 OK from Bob)
   //  - wait for onConversationStateChanged (Connected) (also triggered when Alice receives the 200 OK from Bob)
   //  - wait for onConversationEnded (triggered when Alice receives the BYE from Bob)
   auto aliceEvents = std::async(std::launch::async, [&] () {
      SipConversationState prevState;

      {
         SipConversationHandle h;
         NewConversationEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
            "SipConversationHandler::onNewConversation",
            15000,
            HandleEqualsPred<SipConversationHandle>(aliceCall),
            h, evt));
         ASSERT_EQ(evt.account, alice.handle);
         ASSERT_EQ(evt.conversationType, ConversationType_Outgoing);
         ASSERT_EQ(evt.remoteAddress, bob.config.uri());
         ASSERT_EQ(evt.remoteDisplayName, "");
         ASSERT_EQ(evt.localMediaInfo.size(), 1);
      }

      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
            "SipConversationHandler::onConversationStateChanged",
            5000,
            HandleEqualsPred<SipConversationHandle>(aliceCall),
            h, evt));
         ASSERT_EQ(evt.conversationState, ConversationState_RemoteRinging);
      }

      {
         SipConversationHandle h;
         ConversationMediaChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
            "SipConversationHandler::onConversationMediaChanged",
            5000,
            HandleEqualsPred<SipConversationHandle>(aliceCall),
            h, evt));
         ASSERT_EQ(evt.localMediaInfo.size(),1);
         ASSERT_EQ(evt.localMediaInfo[0].mediaType, MediaType_Audio);
         ASSERT_EQ(evt.localMediaInfo[0].mediaDirection, MediaDirection_SendReceive);
      }

      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
            "SipConversationHandler::onConversationStateChanged",
            5000,
            HandleEqualsPred<SipConversationHandle>(aliceCall),
            h, evt));
         ASSERT_EQ(evt.conversationState, ConversationState_Connected);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(call_duration_ms));

      delete alicePtr;


      //{
      //   SipConversationHandle h;
      //   ConversationEndedEvent evt;
      //   ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
      //      "SipConversationHandler::onConversationEnded",
      //      call_duration_ms + 1000,
      //      HandleEqualsPred<SipConversationHandle>(aliceCall),
      //      h, evt));
      //   ASSERT_EQ(evt.endReason, ConversationEndReason_UserTerminatedRemotely);
      //}
   });

   waitFor2(bobEvents, aliceEvents);

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

}

TEST_F(CallSanityTests, DISABLED_SdkShutdownDuringVideoCall) {
   const int call_duration_ms = 10000;

   TestAccount* alicePtr = new TestAccount("alice");
   TestAccount& alice = *alicePtr;
   TestAccount* bobPtr = new TestAccount("bob");
   TestAccount& bob = *bobPtr;

   // disable H.264
   alice.video->queryCodecList();
   alice.video->setCodecEnabled(cpc::hash(cpc::string("H264")), false);

   bob.video->queryCodecList();
   bob.video->setCodecEnabled(cpc::hash(cpc::string("H264")), false);

#if _WIN32
   HWND hwndAliceCapture = NULL;
   ASSERT_EQ(0, ViECreateWindow(hwndAliceCapture, 0, 0, 352, 288, "Alice (capture)"));
   alice.video->startCapture();
   alice.video->setLocalVideoRenderTarget(hwndAliceCapture);

   HWND hwndAliceRemote = NULL;
   ASSERT_EQ(0, ViECreateWindow(hwndAliceRemote, 356, 0, 352, 288, "Alice (incoming)"));
   alice.video->setIncomingVideoRenderTarget(hwndAliceRemote);

   HWND hwndBobCapture = NULL;
   ASSERT_EQ(0, ViECreateWindow(hwndBobCapture, 0, 292, 352, 288, "Bob (capture)"));
   bob.video->startCapture();
   bob.video->setLocalVideoRenderTarget(hwndBobCapture);

   HWND hwndBobRemote = NULL;
   ASSERT_EQ(0, ViECreateWindow(hwndBobRemote, 356, 292, 352, 288, "Bob (incoming)"));
   bob.video->setIncomingVideoRenderTarget(hwndBobRemote);
#endif // _WIN32
   
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, true);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   // spawn a thread for Bob via std::async; both Bob and Alice will have events firing
   // at about the same time, and the ordering of those events is only interesting on a
   // per-person basis -- so Bob gets his own thread, and Alice gets her own thread

   // Overview of Bob's thread:
   //  - wait for onNewConversation (triggered when Bob gets the incoming INVITE)
   //  - send 180 ringing
   //  - wait for onConversationStateChanged (LocalRinging)
   //  - answer the call (200 OK)
   //  - wait for onConversationMediaChanged (Audio -> SendReceive)
   //  - wait for onConversationStateChanged (Connected)
   //  - end the call (BYE)
   //  - wait for onConversationEnded
   auto bobEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));

      SipConversationHandle bobCall = 0;
      {
         SipConversationHandle h;
         NewConversationEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
            "SipConversationHandler::onNewConversation",
            5000,
            AlwaysTruePred(),
            h, evt));
         bobCall = h;
         ASSERT_EQ(evt.account, bob.handle);
         ASSERT_EQ(evt.conversationState, ConversationState_RemoteOriginated);
         ASSERT_EQ(evt.conversationType, ConversationType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
         ASSERT_EQ(evt.remoteDisplayName, alice.config.name);
         ASSERT_EQ(evt.remoteMediaInfo.size(), 2);
      }

      // Bob sends 180 ringing -- this should trigger state transitions for both Bob and Alice
      ASSERT_EQ(bob.conversation->sendRingingResponse(bobCall), kSuccess);

      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
            "SipConversationHandler::onConversationStateChanged",
            5000,
            HandleEqualsPred<SipConversationHandle>(bobCall),
            h, evt));
         ASSERT_EQ(evt.conversationState, ConversationState_LocalRinging);
      }

      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);

      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      ASSERT_EQ(bob.conversation->accept(bobCall), kSuccess);

      {
         SipConversationHandle h;
         ConversationMediaChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
            "SipConversationHandler::onConversationMediaChanged",
            15000,
            HandleEqualsPred<SipConversationHandle>(bobCall),
            h, evt));
         ASSERT_EQ(evt.localMediaInfo.size(),2);
         ASSERT_EQ(evt.localMediaInfo[0].mediaType, MediaType_Audio);
         ASSERT_EQ(evt.localMediaInfo[0].mediaDirection, MediaDirection_SendReceive);
         std::string strLocalPlname(evt.localMediaInfo[0].audioCodec.plname);
         //ASSERT_EQ(strLocalPlname, "opus");
         std::string strRemotePlname(evt.remoteMediaInfo[0].audioCodec.plname);
         //ASSERT_EQ(strRemotePlname, "opus");
      }

      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
            "SipConversationHandler::onConversationStateChanged",
            5000,
            HandleEqualsPred<SipConversationHandle>(bobCall),
            h, evt));
         ASSERT_EQ(evt.conversationState, ConversationState_Connected);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(call_duration_ms));
      bob.shutdown();
      //bob.events->shutdown();
      //CPCAPI2::Phone::release(bob.phone);
      TestAccount* bobCopyPtr = bobPtr;
      bobPtr = NULL;
      //delete bobCopyPtr; // intentional leak

      //ASSERT_EQ(bob.conversation->end(bobCall), kSuccess);

      //{
      //   SipConversationHandle h;
      //   ConversationEndedEvent evt;
      //   ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
      //      "SipConversationHandler::onConversationEnded",
      //      5000,
      //      HandleEqualsPred<SipConversationHandle>(bobCall),
      //      h, evt));
      //   ASSERT_EQ(evt.endReason, ConversationEndReason_UserTerminatedLocally);
      //}
   });

   // Overview of Alice's thread:
   //  - wait for onNewConversation (triggered as soon as Alice sends the INVITE)
   //  - wait for onConversationStateChanged (RemoteRinging) (triggered when Alice receives the 180 from Bob)
   //  - wait for onConversationMediaChanged (Audio -> SendReceive) (triggered when Alice receives the 200 OK from Bob)
   //  - wait for onConversationStateChanged (Connected) (also triggered when Alice receives the 200 OK from Bob)
   //  - wait for onConversationEnded (triggered when Alice receives the BYE from Bob)
   auto aliceEvents = std::async(std::launch::async, [&] () {
      
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      
      SipConversationState prevState;

      {
         SipConversationHandle h;
         NewConversationEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
            "SipConversationHandler::onNewConversation",
            15000,
            HandleEqualsPred<SipConversationHandle>(aliceCall),
            h, evt));
         ASSERT_EQ(evt.account, alice.handle);
         ASSERT_EQ(evt.conversationType, ConversationType_Outgoing);
         ASSERT_EQ(evt.remoteAddress, bob.config.uri());
         ASSERT_EQ(evt.remoteDisplayName, "");
         ASSERT_EQ(evt.localMediaInfo.size(), 2);
      }

      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
            "SipConversationHandler::onConversationStateChanged",
            5000,
            HandleEqualsPred<SipConversationHandle>(aliceCall),
            h, evt));
         ASSERT_EQ(evt.conversationState, ConversationState_RemoteRinging);
      }

      {
         SipConversationHandle h;
         ConversationMediaChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
            "SipConversationHandler::onConversationMediaChanged",
            5000,
            HandleEqualsPred<SipConversationHandle>(aliceCall),
            h, evt));
         ASSERT_EQ(evt.localMediaInfo.size(),2);
         ASSERT_EQ(evt.localMediaInfo[0].mediaType, MediaType_Audio);
         ASSERT_EQ(evt.localMediaInfo[0].mediaDirection, MediaDirection_SendReceive);
      }

      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
            "SipConversationHandler::onConversationStateChanged",
            5000,
            HandleEqualsPred<SipConversationHandle>(aliceCall),
            h, evt));
         ASSERT_EQ(evt.conversationState, ConversationState_Connected);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(call_duration_ms));

      alice.shutdown();
      //alice.events->shutdown();
      //CPCAPI2::Phone::release(alice.phone);
      TestAccount* aliceCopyPtr = alicePtr;
      alicePtr = NULL;
      //delete aliceCopyPtr; // intentional leak


      //{
      //   SipConversationHandle h;
      //   ConversationEndedEvent evt;
      //   ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
      //      "SipConversationHandler::onConversationEnded",
      //      call_duration_ms + 1000,
      //      HandleEqualsPred<SipConversationHandle>(aliceCall),
      //      h, evt));
      //   ASSERT_EQ(evt.endReason, ConversationEndReason_UserTerminatedRemotely);
      //}
   });

   waitFor2(bobEvents, aliceEvents);

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

#if _WIN32
   DestroyWindow(hwndAliceCapture);
   DestroyWindow(hwndAliceRemote);
   DestroyWindow(hwndBobCapture);
   DestroyWindow(hwndBobRemote);
#endif // _WIN32
}

TEST_F(CallSanityTests, SdkShutdownDuringCallCleanUpDialogs) {
   const int call_duration_ms = 10000;

   TestAccount alice("alice");
   TestAccount bob("bob", Account_Init);

   bob.enable();

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   // spawn a thread for Bob via std::async; both Bob and Alice will have events firing
   // at about the same time, and the ordering of those events is only interesting on a
   // per-person basis -- so Bob gets his own thread, and Alice gets her own thread

   // Overview of Bob's thread:
   //  - wait for onNewConversation (triggered when Bob gets the incoming INVITE)
   //  - send 180 ringing
   //  - wait for onConversationStateChanged (LocalRinging)
   //  - answer the call (200 OK)
   //  - wait for onConversationMediaChanged (Audio -> SendReceive)
   //  - wait for onConversationStateChanged (Connected)
   //  - end the call (BYE)
   //  - wait for onConversationEnded
   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall = 0;
      {
         SipConversationHandle h;
         NewConversationEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
            "SipConversationHandler::onNewConversation",
            5000,
            AlwaysTruePred(),
            h, evt));
         bobCall = h;
         ASSERT_EQ(evt.account, bob.handle);
         ASSERT_EQ(evt.conversationState, ConversationState_RemoteOriginated);
         ASSERT_EQ(evt.conversationType, ConversationType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
         ASSERT_EQ(evt.remoteDisplayName, alice.config.name);
         ASSERT_EQ(evt.remoteMediaInfo.size(), 1);
      }

      // Bob sends 180 ringing -- this should trigger state transitions for both Bob and Alice
      ASSERT_EQ(bob.conversation->sendRingingResponse(bobCall), kSuccess);

      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
            "SipConversationHandler::onConversationStateChanged",
            5000,
            HandleEqualsPred<SipConversationHandle>(bobCall),
            h, evt));
         ASSERT_EQ(evt.conversationState, ConversationState_LocalRinging);
      }

      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      ASSERT_EQ(bob.conversation->accept(bobCall), kSuccess);

      {
         SipConversationHandle h;
         ConversationMediaChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
            "SipConversationHandler::onConversationMediaChanged",
            15000,
            HandleEqualsPred<SipConversationHandle>(bobCall),
            h, evt));
         ASSERT_EQ(evt.localMediaInfo.size(),1);
         ASSERT_EQ(evt.localMediaInfo[0].mediaType, MediaType_Audio);
         ASSERT_EQ(evt.localMediaInfo[0].mediaDirection, MediaDirection_SendReceive);
         std::string strLocalPlname(evt.localMediaInfo[0].audioCodec.plname);
         //ASSERT_EQ(strLocalPlname, "opus");
         std::string strRemotePlname(evt.remoteMediaInfo[0].audioCodec.plname);
         //ASSERT_EQ(strRemotePlname, "opus");
      }

      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
            "SipConversationHandler::onConversationStateChanged",
            5000,
            HandleEqualsPred<SipConversationHandle>(bobCall),
            h, evt));
         ASSERT_EQ(evt.conversationState, ConversationState_Connected);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(call_duration_ms));

      bob.disable(true);

      //ASSERT_EQ(bob.conversation->end(bobCall), kSuccess);

      //{
      //   SipConversationHandle h;
      //   ConversationEndedEvent evt;
      //   ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
      //      "SipConversationHandler::onConversationEnded",
      //      5000,
      //      HandleEqualsPred<SipConversationHandle>(bobCall),
      //      h, evt));
      //   ASSERT_EQ(evt.endReason, ConversationEndReason_UserTerminatedLocally);
      //}
   });

   // Overview of Alice's thread:
   //  - wait for onNewConversation (triggered as soon as Alice sends the INVITE)
   //  - wait for onConversationStateChanged (RemoteRinging) (triggered when Alice receives the 180 from Bob)
   //  - wait for onConversationMediaChanged (Audio -> SendReceive) (triggered when Alice receives the 200 OK from Bob)
   //  - wait for onConversationStateChanged (Connected) (also triggered when Alice receives the 200 OK from Bob)
   //  - wait for onConversationEnded (triggered when Alice receives the BYE from Bob)
   auto aliceEvents = std::async(std::launch::async, [&] () {
      SipConversationState prevState;

      {
         SipConversationHandle h;
         NewConversationEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
            "SipConversationHandler::onNewConversation",
            15000,
            HandleEqualsPred<SipConversationHandle>(aliceCall),
            h, evt));
         ASSERT_EQ(evt.account, alice.handle);
         ASSERT_EQ(evt.conversationType, ConversationType_Outgoing);
         ASSERT_EQ(evt.remoteAddress, bob.config.uri());
         ASSERT_EQ(evt.remoteDisplayName, "");
         ASSERT_EQ(evt.localMediaInfo.size(), 1);
      }

      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
            "SipConversationHandler::onConversationStateChanged",
            5000,
            HandleEqualsPred<SipConversationHandle>(aliceCall),
            h, evt));
         ASSERT_EQ(evt.conversationState, ConversationState_RemoteRinging);
      }

      {
         SipConversationHandle h;
         ConversationMediaChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
            "SipConversationHandler::onConversationMediaChanged",
            5000,
            HandleEqualsPred<SipConversationHandle>(aliceCall),
            h, evt));
         ASSERT_EQ(evt.localMediaInfo.size(),1);
         ASSERT_EQ(evt.localMediaInfo[0].mediaType, MediaType_Audio);
         ASSERT_EQ(evt.localMediaInfo[0].mediaDirection, MediaDirection_SendReceive);
      }

      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
            "SipConversationHandler::onConversationStateChanged",
            5000,
            HandleEqualsPred<SipConversationHandle>(aliceCall),
            h, evt));
         ASSERT_EQ(evt.conversationState, ConversationState_Connected);
      }

      {
         SipConversationHandle h;
         ConversationEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
            "SipConversationHandler::onConversationEnded",
            call_duration_ms + 5000,
            HandleEqualsPred<SipConversationHandle>(aliceCall),
            h, evt));
         ASSERT_EQ(evt.endReason, ConversationEndReason_UserTerminatedRemotely);
      }
   });

   waitFor2(bobEvents, aliceEvents);

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

}

TEST_F(CallSanityTests, SdkShutdownDuringCallNoDisable) {
   const int call_duration_ms = 10000;

   TestAccount* alicePtr = new TestAccount("alice", Account_Enable, false /*disableOnDestruct*/);
   TestAccount& alice = *alicePtr;
   TestAccount* bobPtr = new TestAccount("bob", Account_Enable, false /*disableOnDestruct*/);
   TestAccount& bob = *bobPtr;

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   // spawn a thread for Bob via std::async; both Bob and Alice will have events firing
   // at about the same time, and the ordering of those events is only interesting on a
   // per-person basis -- so Bob gets his own thread, and Alice gets her own thread

   // Overview of Bob's thread:
   //  - wait for onNewConversation (triggered when Bob gets the incoming INVITE)
   //  - send 180 ringing
   //  - wait for onConversationStateChanged (LocalRinging)
   //  - answer the call (200 OK)
   //  - wait for onConversationMediaChanged (Audio -> SendReceive)
   //  - wait for onConversationStateChanged (Connected)
   //  - end the call (BYE)
   //  - wait for onConversationEnded
   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall = 0;
      {
         SipConversationHandle h;
         NewConversationEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
            "SipConversationHandler::onNewConversation",
            5000,
            AlwaysTruePred(),
            h, evt));
         bobCall = h;
         ASSERT_EQ(evt.account, bob.handle);
         ASSERT_EQ(evt.conversationState, ConversationState_RemoteOriginated);
         ASSERT_EQ(evt.conversationType, ConversationType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
         ASSERT_EQ(evt.remoteDisplayName, alice.config.name);
         ASSERT_EQ(evt.remoteMediaInfo.size(), 1);
      }

      // Bob sends 180 ringing -- this should trigger state transitions for both Bob and Alice
      ASSERT_EQ(bob.conversation->sendRingingResponse(bobCall), kSuccess);

      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
            "SipConversationHandler::onConversationStateChanged",
            5000,
            HandleEqualsPred<SipConversationHandle>(bobCall),
            h, evt));
         ASSERT_EQ(evt.conversationState, ConversationState_LocalRinging);
      }

      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      ASSERT_EQ(bob.conversation->accept(bobCall), kSuccess);

      {
         SipConversationHandle h;
         ConversationMediaChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
            "SipConversationHandler::onConversationMediaChanged",
            15000,
            HandleEqualsPred<SipConversationHandle>(bobCall),
            h, evt));
         ASSERT_EQ(evt.localMediaInfo.size(),1);
         ASSERT_EQ(evt.localMediaInfo[0].mediaType, MediaType_Audio);
         ASSERT_EQ(evt.localMediaInfo[0].mediaDirection, MediaDirection_SendReceive);
         std::string strLocalPlname(evt.localMediaInfo[0].audioCodec.plname);
         //ASSERT_EQ(strLocalPlname, "opus");
         std::string strRemotePlname(evt.remoteMediaInfo[0].audioCodec.plname);
         //ASSERT_EQ(strRemotePlname, "opus");
      }

      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
            "SipConversationHandler::onConversationStateChanged",
            5000,
            HandleEqualsPred<SipConversationHandle>(bobCall),
            h, evt));
         ASSERT_EQ(evt.conversationState, ConversationState_Connected);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(call_duration_ms));

      delete bobPtr;

      //ASSERT_EQ(bob.conversation->end(bobCall), kSuccess);

      //{
      //   SipConversationHandle h;
      //   ConversationEndedEvent evt;
      //   ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
      //      "SipConversationHandler::onConversationEnded",
      //      5000,
      //      HandleEqualsPred<SipConversationHandle>(bobCall),
      //      h, evt));
      //   ASSERT_EQ(evt.endReason, ConversationEndReason_UserTerminatedLocally);
      //}
   });

   // Overview of Alice's thread:
   //  - wait for onNewConversation (triggered as soon as Alice sends the INVITE)
   //  - wait for onConversationStateChanged (RemoteRinging) (triggered when Alice receives the 180 from Bob)
   //  - wait for onConversationMediaChanged (Audio -> SendReceive) (triggered when Alice receives the 200 OK from Bob)
   //  - wait for onConversationStateChanged (Connected) (also triggered when Alice receives the 200 OK from Bob)
   //  - wait for onConversationEnded (triggered when Alice receives the BYE from Bob)
   auto aliceEvents = std::async(std::launch::async, [&] () {
      SipConversationState prevState;

      {
         SipConversationHandle h;
         NewConversationEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
            "SipConversationHandler::onNewConversation",
            15000,
            HandleEqualsPred<SipConversationHandle>(aliceCall),
            h, evt));
         ASSERT_EQ(evt.account, alice.handle);
         ASSERT_EQ(evt.conversationType, ConversationType_Outgoing);
         ASSERT_EQ(evt.remoteAddress, bob.config.uri());
         ASSERT_EQ(evt.remoteDisplayName, "");
         ASSERT_EQ(evt.localMediaInfo.size(), 1);
      }

      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
            "SipConversationHandler::onConversationStateChanged",
            5000,
            HandleEqualsPred<SipConversationHandle>(aliceCall),
            h, evt));
         ASSERT_EQ(evt.conversationState, ConversationState_RemoteRinging);
      }

      {
         SipConversationHandle h;
         ConversationMediaChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
            "SipConversationHandler::onConversationMediaChanged",
            5000,
            HandleEqualsPred<SipConversationHandle>(aliceCall),
            h, evt));
         ASSERT_EQ(evt.localMediaInfo.size(),1);
         ASSERT_EQ(evt.localMediaInfo[0].mediaType, MediaType_Audio);
         ASSERT_EQ(evt.localMediaInfo[0].mediaDirection, MediaDirection_SendReceive);
      }

      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
            "SipConversationHandler::onConversationStateChanged",
            5000,
            HandleEqualsPred<SipConversationHandle>(aliceCall),
            h, evt));
         ASSERT_EQ(evt.conversationState, ConversationState_Connected);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(call_duration_ms));

      delete alicePtr;


      //{
      //   SipConversationHandle h;
      //   ConversationEndedEvent evt;
      //   ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
      //      "SipConversationHandler::onConversationEnded",
      //      call_duration_ms + 1000,
      //      HandleEqualsPred<SipConversationHandle>(aliceCall),
      //      h, evt));
      //   ASSERT_EQ(evt.endReason, ConversationEndReason_UserTerminatedRemotely);
      //}
   });

   waitFor2(bobEvents, aliceEvents);

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

}

TEST_F(CallSanityTests, SdkShutdownDuringRingingCallNoDisable) {
   const int call_duration_ms = 10000;

   TestAccount* alicePtr = new TestAccount("alice", Account_Enable, false /*disableOnDestruct*/);
   TestAccount& alice = *alicePtr;
   TestAccount* bobPtr = new TestAccount("bob", Account_Enable, false /*disableOnDestruct*/);
   TestAccount& bob = *bobPtr;

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   // spawn a thread for Bob via std::async; both Bob and Alice will have events firing
   // at about the same time, and the ordering of those events is only interesting on a
   // per-person basis -- so Bob gets his own thread, and Alice gets her own thread

   // Overview of Bob's thread:
   //  - wait for onNewConversation (triggered when Bob gets the incoming INVITE)
   //  - send 180 ringing
   //  - wait for onConversationStateChanged (LocalRinging)
   //  - answer the call (200 OK)
   //  - wait for onConversationMediaChanged (Audio -> SendReceive)
   //  - wait for onConversationStateChanged (Connected)
   //  - end the call (BYE)
   //  - wait for onConversationEnded
   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall = 0;
      {
         SipConversationHandle h;
         NewConversationEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
            "SipConversationHandler::onNewConversation",
            5000,
            AlwaysTruePred(),
            h, evt));
         bobCall = h;
         ASSERT_EQ(evt.account, bob.handle);
         ASSERT_EQ(evt.conversationState, ConversationState_RemoteOriginated);
         ASSERT_EQ(evt.conversationType, ConversationType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
         ASSERT_EQ(evt.remoteDisplayName, alice.config.name);
         ASSERT_EQ(evt.remoteMediaInfo.size(), 1);
      }

      // Bob sends 180 ringing -- this should trigger state transitions for both Bob and Alice
      ASSERT_EQ(bob.conversation->sendRingingResponse(bobCall), kSuccess);

      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
            "SipConversationHandler::onConversationStateChanged",
            5000,
            HandleEqualsPred<SipConversationHandle>(bobCall),
            h, evt));
         ASSERT_EQ(evt.conversationState, ConversationState_LocalRinging);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(call_duration_ms));

      delete bobPtr;

   });

   // Overview of Alice's thread:
   //  - wait for onNewConversation (triggered as soon as Alice sends the INVITE)
   //  - wait for onConversationStateChanged (RemoteRinging) (triggered when Alice receives the 180 from Bob)
   //  - wait for onConversationMediaChanged (Audio -> SendReceive) (triggered when Alice receives the 200 OK from Bob)
   //  - wait for onConversationStateChanged (Connected) (also triggered when Alice receives the 200 OK from Bob)
   //  - wait for onConversationEnded (triggered when Alice receives the BYE from Bob)
   auto aliceEvents = std::async(std::launch::async, [&] () {
      SipConversationState prevState;

      {
         SipConversationHandle h;
         NewConversationEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
            "SipConversationHandler::onNewConversation",
            15000,
            HandleEqualsPred<SipConversationHandle>(aliceCall),
            h, evt));
         ASSERT_EQ(evt.account, alice.handle);
         ASSERT_EQ(evt.conversationType, ConversationType_Outgoing);
         ASSERT_EQ(evt.remoteAddress, bob.config.uri());
         ASSERT_EQ(evt.remoteDisplayName, "");
         ASSERT_EQ(evt.localMediaInfo.size(), 1);
      }

      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
            "SipConversationHandler::onConversationStateChanged",
            5000,
            HandleEqualsPred<SipConversationHandle>(aliceCall),
            h, evt));
         ASSERT_EQ(evt.conversationState, ConversationState_RemoteRinging);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(call_duration_ms));

      delete alicePtr;

   });

   waitFor2(bobEvents, aliceEvents);

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

}

TEST_F(CallSanityTests, SdkShutdownAfterFailedCallWithAudioMonitoring)
{
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_5060 = new repro::ReproRunner();
   const char* const reproArgs[] = {""};
   resip::Data configFile = (TestEnvironmentConfig::testResourcePath() + "repro_5060.config").c_str();
   repro_5060->run(1, reproArgs, configFile);
   ASSERT_TRUE(repro_5060->getProxy() != NULL);

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.outboundProxy = "127.0.0.1:5060";
   alice.enable();

   // enable TURN so call cannot be established
   SipConversationSettings settings;
   settings.natTraversalMode = NatTraversalMode_TURN;
   settings.natTraversalServer = "stun.counterpath.com";
   alice.conversation->setDefaultSettings(alice.handle, settings);

   // make an outgoing call to any URI
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   cpc::string remoteUri = "sip:someuriinvalid@" + alice.config.settings.domain;
   alice.conversation->addParticipant(aliceCall, remoteUri);
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]() {

      assertNewConversationOutgoing(alice, aliceCall, remoteUri);
   
      alice.conversation->startMonitoringAudioDeviceLevels(aliceCall);

      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
      
      alice.disable();
   });

   waitFor(aliceEvents);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   delete repro_5060;
   const char* const defaultReproArgs[] = {""};
   resip::Data defaultConfigFile = (TestEnvironmentConfig::testResourcePath() + "repro.config").c_str();
   ReproHolder::instance()->run(1, defaultReproArgs, defaultConfigFile);
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
}
