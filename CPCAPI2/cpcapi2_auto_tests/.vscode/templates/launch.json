// VSCode will read launch.json from cpcapi2_auto_tests/.vscode/launch.json
// This file is not in source control so that developers can freely customize it. If it does not exist during CMake configure (e.g. when opening 
// our workspace in VSCode), it will be copied from our versioned template at cpcapi2_auto_tests/.vscode/templates/launch.json
{
    "configurations": [
        {
            // This config should be only used for running the autotests on macOS or Linux
            "name": "macOS example: AccountModuleTest.AccountEnableDisable",
            "args": ["--gtest_filter=NetworkChangeTest.NetworkChangeHandoverUsingStarcode_PreferPAssertedIdentity", "--gtest_break_on_failure"],
            "type": "lldb",
            "request": "launch",
            "program": "${command:cmake.launchTargetPath}",
            "cwd": "${workspaceFolder}",
            // Only certain tests require enabling this environment variable; those tests will fail if this env variable is not set.
            // Before enabling this environment variable ensure you have followed the instructions printed when the test fails.
            "env": { "CPCAPI2_MAC_LOOPBACKALIASES_SET": "0" }
        },
        {
            // This config should be only used for running the autotests on Windows
            "name": "Windows example: FailbackConnectionProblem",
            "args": ["--gtest_filter=DnsFailoverTest.FailbackConnectionProblem"],
            "environment": [{ "name": "CPCAPI2_NO_PAUSE", "value": "1" }],
            "type": "cppvsdbg",
            "request": "launch",
            "program": "${command:cmake.launchTargetPath}",
            "cwd": "${workspaceFolder}",
            "stopAtEntry": false,
            "externalConsole": true
        },
        {
            // This config should be only used on macOS for attaching to a non-autotest app using our SDK; e.g. sample app, Cymbus/Bria
            "name": "macOS example: Attach to AdvancedAudioCall",
            "type": "lldb",
            "request": "attach",
            "program": "/Users/<USER>/Library/Developer/Xcode/DerivedData/SDKallprojects-bnjrrzzzoujccghihnvazzyzhdqy/Build/Products/Debug/AdvancedAudioCall.app/Contents/MacOS/AdvancedAudioCall",
            "initCommands": [
                "target create /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKallprojects-bnjrrzzzoujccghihnvazzyzhdqy/Build/Products/Debug/AdvancedAudioCall.app/Contents/MacOS/AdvancedAudioCall", 
                "add-dsym /Users/<USER>/Documents/CPCAPI2.branch/core/samples/libs/CPCAPI2.framework/Versions/A/CPCAPI2.dSYM"
            ]
        },
        {
            // This config should only be used for running the standalone SIP service monitoring test tool
            // (see https://alianza.atlassian.net/wiki/spaces/DEV/pages/2573893659/Standalone+Service+Monitoring+Test+Tools)
            "name": "macOS/Linux Example: SIP service monitoring tool",
            // this filter is only required when building the autotests via VSCode to run the tool
            "args": ["--gtest_filter=SipServiceMonitoring.test"],
            // this environment variable is only required when building the autotests via VSCode to run the tool
            "env": { "CPCAPI2_ENABLE_TEST_TOOLS": "1" },
            "type": "lldb",
            "request": "launch",
            "program": "${command:cmake.launchTargetPath}",
            "cwd": "${workspaceFolder}/test_tools/testing_the_tests/sip_service_test_tool",
        },
        {
            // This config should only be used for running the standalone SIP push test tool
            // (see https://alianza.atlassian.net/wiki/spaces/DEV/pages/2573893659/Standalone+Service+Monitoring+Test+Tools)
            "name": "macOS/Linux Example: SIP push test tool",
            // this filter is only required when building the autotests via VSCode to run the tool
            "args": ["--gtest_filter=SipPushServerLoadTestTool.test"],
            // this environment variable is only required when building the autotests via VSCode to run the tool
            "env": { "CPCAPI2_ENABLE_TEST_TOOLS": "1" },
            "type": "lldb",
            "request": "launch",
            "program": "${command:cmake.launchTargetPath}",
            "cwd": "${workspaceFolder}/test_tools/testing_the_tests/sip_push_test_tool",
        },
    ]
}
