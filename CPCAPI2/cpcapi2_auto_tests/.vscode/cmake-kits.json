[{"name": "osx-clang-x86_64", "toolchainFile": "../../projects/cmake/toolchains/osx-clang-x86_64.cmake"}, {"name": "osx-clang-armv8", "toolchainFile": "../../projects/cmake/toolchains/osx-clang-armv8.cmake"}, {"name": "ios-clang-x86_64-simulator", "toolchainFile": "../../projects/cmake/toolchains/ios-clang-x86_64-simulator.cmake"}, {"name": "ios-clang-armv8", "toolchainFile": "../../projects/cmake/toolchains/ios-clang-armv8.cmake"}, {"name": "windows-msvc-x86", "toolchainFile": "../../projects/cmake/toolchains/windows-msvc-x86.cmake"}, {"name": "windows-msvc-x86_64", "toolchainFile": "../../projects/cmake/toolchains/windows-msvc-x86_64.cmake"}, {"name": "linux-clang-x86_64", "toolchainFile": "../../projects/cmake/toolchains/linux-clang-x86_64.cmake"}, {"name": "linux-clang-armv8", "toolchainFile": "../../projects/cmake/toolchains/linux-clang-armv8.cmake"}]