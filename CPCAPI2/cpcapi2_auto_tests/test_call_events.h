#pragma once
#ifndef TEST_CALL_EVENTS_H
#define TEST_CALL_EVENTS_H

#include "brand_branded.h"
#include <cpcapi2.h>

#include "cpcapi2_test_fixture.h"

#define EVENT_VALIDATOR(TYPE) std::function<void(const TYPE& evt)>

class TestCallEvents
{
public:

   static void expectNewConversationIncoming(int line, TestAccount& account, 
      CPCAPI2::SipConversation::SipConversationHandle* handle,
      const cpc::string& fromUri,
      EVENT_VALIDATOR(CPCAPI2::SipConversation::NewConversationEvent) validator,
      bool checkFromUri = true, int timeMs = 30000);
   static void expectNewConversationIncoming_answermode(int line, TestAccount& account,
      CPCAPI2::SipConversation::SipConversationHandle* handle,
      const cpc::string& fromUri,
      CPCAPI2::SipConversation::AnswerModeSettings& answerMode,
      EVENT_VALIDATOR(CPCAPI2::SipConversation::NewConversationEvent) validator,
      bool checkFromUri=true);
   static void expectNewConversationIncoming_crypto(
      int line,
      TestAccount& account,
      CPCAPI2::SipConversation::SipConversationHandle* handle,
      const cpc::string& fromUri,
      const CPCAPI2::SipConversation::MediaInfo& audio,
      const CPCAPI2::SipConversation::MediaInfo& video,
      EVENT_VALIDATOR(CPCAPI2::SipConversation::NewConversationEvent) validator);
   static void expectNewConversationIncomingAudio_crypto(
      int line,
      TestAccount& account,
      CPCAPI2::SipConversation::SipConversationHandle* handle,
      const cpc::string& fromUri,
      const CPCAPI2::SipConversation::MediaInfo& audio,
      EVENT_VALIDATOR(CPCAPI2::SipConversation::NewConversationEvent) validator,
      bool checkFromUri=true);
   #define assertNewConversationIncoming(account, handle, fromUri) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectNewConversationIncoming(__LINE__, account, handle, fromUri, nullptr, true); \
   }
   #define assertNewConversationIncoming_time(account, handle, fromUri, timeMs) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectNewConversationIncoming(__LINE__, account, handle, fromUri, nullptr, true, timeMs); \
   }
   #define assertNewConversationIncoming_ex(account, handle, fromUri, validator) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectNewConversationIncoming(__LINE__, account, handle, fromUri, validator); \
   }
   #define assertNewConversationIncoming_answermode(account, handle, fromUri, answerMode, validator) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectNewConversationIncoming_answermode(__LINE__, account, handle, fromUri, answerMode, validator); \
   }
   #define assertNewConversationIncoming_crypto(account, handle, fromUri, audio, video, validator) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectNewConversationIncoming_crypto(__LINE__, account, handle, fromUri, audio, video, validator); \
   }
   #define assertNewConversationIncomingAudio_crypto(account, handle, fromUri, audio, validator) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectNewConversationIncomingAudio_crypto(__LINE__, account, handle, fromUri, audio, validator, true); \
   }

   static void expectNewConversationIncomingTransfer(int line, TestAccount& account, 
         CPCAPI2::SipConversation::SipConversationHandle* handle,
         const cpc::string& fromUri,
         EVENT_VALIDATOR(CPCAPI2::SipConversation::NewConversationEvent) validator);
   #define assertNewConversationIncomingTransfer(account, handle, fromUri) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectNewConversationIncomingTransfer(__LINE__, account, handle, fromUri, nullptr); \
   }
   #define assertNewConversationIncomingTransfer_ex(account, handle, fromUri, validator) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectNewConversationIncomingTransfer(__LINE__, account, handle, fromUri, validator); \
   }

   static void expectNewConversationOutgoing(int line, TestAccount& account, 
      CPCAPI2::SipConversation::SipConversationHandle handle,
      const cpc::string& toUri,
      EVENT_VALIDATOR(CPCAPI2::SipConversation::NewConversationEvent) validator);
   static void expectNewConversationOutgoing_answermode(int line, TestAccount& account,
      CPCAPI2::SipConversation::SipConversationHandle handle,
      const cpc::string& toUri,
      CPCAPI2::SipConversation::AnswerModeSettings& answerMode,
      EVENT_VALIDATOR(CPCAPI2::SipConversation::NewConversationEvent) validator);
   static void expectNewConversationOutgoing_time(int line, TestAccount& account,
      CPCAPI2::SipConversation::SipConversationHandle handle,
      const cpc::string& toUri,
      EVENT_VALIDATOR(CPCAPI2::SipConversation::NewConversationEvent) validator,
      unsigned int timeMS);
   static void expectNewConversationOutgoing_crypto(
      int line,
      TestAccount& account,
      CPCAPI2::SipConversation::SipConversationHandle handle,
      const cpc::string& toUri,
      const CPCAPI2::SipConversation::MediaInfo& audio,
      const CPCAPI2::SipConversation::MediaInfo& video,
      EVENT_VALIDATOR(CPCAPI2::SipConversation::NewConversationEvent) validator);
   static void expectNewConversationOutgoingAudio_crypto(
      int line,
      TestAccount& account,
      CPCAPI2::SipConversation::SipConversationHandle handle,
      const cpc::string& toUri,
      const CPCAPI2::SipConversation::MediaInfo& audio,
      EVENT_VALIDATOR(CPCAPI2::SipConversation::NewConversationEvent) validator);
   #define assertNewConversationOutgoing(account, handle, toUri) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectNewConversationOutgoing(__LINE__, account, handle, toUri, nullptr); \
   }
   #define assertNewConversationOutgoing_ex(account, handle, toUri, validator) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectNewConversationOutgoing(__LINE__, account, handle, toUri, validator); \
   }
   #define assertNewConversationOutgoing_answermode(account, handle, toUri, answerMode, validator) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectNewConversationOutgoing_answermode(__LINE__, account, handle, toUri, answerMode, validator); \
   }
   #define assertNewConversationOutgoing_time(account, handle, toUri, time) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectNewConversationOutgoing_time(__LINE__, account, handle, toUri, nullptr, time); \
   }
   #define assertNewConversationOutgoing_crypto(account, handle, toUri, audio, video, validator) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectNewConversationOutgoing_crypto(__LINE__, account, handle, toUri, audio, video, validator); \
   }
   #define assertNewConversationOutgoingAudio_crypto(account, handle, toUri, audio, validator) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectNewConversationOutgoingAudio_crypto(__LINE__, account, handle, toUri, audio, validator); \
   }
   
   static void expectConversationMediaChanged(int line, TestAccount& account, 
         CPCAPI2::SipConversation::SipConversationHandle handle,
         CPCAPI2::SipConversation::MediaDirection direction);
   static void expectConversationMediaChanged_time(int line, TestAccount& account, 
         CPCAPI2::SipConversation::SipConversationHandle handle,
         CPCAPI2::SipConversation::MediaDirection direction,
         unsigned int timeMS);
   static void expectConversationMediaChanged_ex(int line, TestAccount& account, 
         CPCAPI2::SipConversation::SipConversationHandle handle,
         EVENT_VALIDATOR(CPCAPI2::SipConversation::ConversationMediaChangedEvent) validator);
   static void expectConversationMediaChanged_ex_time(int line, TestAccount& account,
         CPCAPI2::SipConversation::SipConversationHandle handle,
         EVENT_VALIDATOR(CPCAPI2::SipConversation::ConversationMediaChangedEvent) validator,
         unsigned int timeMS);
   static void expectConversationMediaChanged_crypto(
      int line,
      TestAccount& account,
      CPCAPI2::SipConversation::SipConversationHandle handle,
      CPCAPI2::SipConversation::MediaCryptoSuite audioCrypto,
      CPCAPI2::SipConversation::MediaCryptoSuite videoCrypto,
      const CPCAPI2::SipConversation::MediaInfo& localAudio,
      const CPCAPI2::SipConversation::MediaInfo& localVideo,
      const CPCAPI2::SipConversation::MediaInfo& remoteAudio,
      const CPCAPI2::SipConversation::MediaInfo& remoteVideo,
      EVENT_VALIDATOR(CPCAPI2::SipConversation::ConversationMediaChangedEvent) validator);
   static void expectConversationMediaChangedAudio_crypto(
      int line,
      TestAccount& account,
      CPCAPI2::SipConversation::SipConversationHandle handle,
      CPCAPI2::SipConversation::MediaCryptoSuite audioCrypto,
      const CPCAPI2::SipConversation::MediaInfo& localAudio,
      const CPCAPI2::SipConversation::MediaInfo& remoteAudio,
      EVENT_VALIDATOR(CPCAPI2::SipConversation::ConversationMediaChangedEvent) validator);
   static void compareMediaInfoInConversationMediaChanged_crypto(
      int mediaIndex,
      CPCAPI2::SipConversation::MediaCryptoSuite crypto,
      const CPCAPI2::SipConversation::MediaInfo& localMedia,
      const CPCAPI2::SipConversation::MediaInfo& remoteMedia,
      CPCAPI2::SipConversation::ConversationMediaChangedEvent& evt);
   #define assertConversationMediaChanged(account, handle, direction) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectConversationMediaChanged(__LINE__, account, handle, direction); \
   }
   #define assertConversationMediaChanged_time(account, handle, direction, timeMs) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectConversationMediaChanged_time(__LINE__, account, handle, direction, timeMs); \
   }
   #define assertConversationMediaChanged_ex(account, handle, validator) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectConversationMediaChanged_ex(__LINE__, account, handle, validator); \
   }
   #define assertConversationMediaChanged_ex_time(account, handle, validator, time) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectConversationMediaChanged_ex_time(__LINE__, account, handle, validator, time); \
   }
   #define assertConversationMediaChanged_crypto(account, handle, audioCrypto, videoCrypto, localAudio, localVideo, remoteAudio, remoteVideo, validator); \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectConversationMediaChanged_crypto(__LINE__, account, handle, audioCrypto, videoCrypto, localAudio, localVideo, remoteAudio, remoteVideo, validator); \
   }
   #define assertConversationMediaChangedAudio_crypto(account, handle, audioCrypto, localAudio, remoteAudio, validator); \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectConversationMediaChangedAudio_crypto(__LINE__, account, handle, audioCrypto, localAudio, remoteAudio, validator); \
   }

   static void expectConversationMediaChangeRequest(int line, TestAccount& account, 
      CPCAPI2::SipConversation::SipConversationHandle handle,
      CPCAPI2::SipConversation::MediaDirection direction,
      int timeoutMs = 15000);
   static void expectConversationMediaChangeRequest_ex(int line, TestAccount& account, 
      CPCAPI2::SipConversation::SipConversationHandle handle,
      EVENT_VALIDATOR(CPCAPI2::SipConversation::ConversationMediaChangeRequestEvent) validator);
   static void expectConversationMediaChangeRequestAudio_crypto(
      int line,
      TestAccount& account,
      CPCAPI2::SipConversation::SipConversationHandle handle,
      const CPCAPI2::SipConversation::MediaInfo& remoteAudio,
      EVENT_VALIDATOR(CPCAPI2::SipConversation::ConversationMediaChangeRequestEvent) validator);
   static void expectConversationMediaChangeRequest_crypto(
      int line,
      TestAccount& account,
      CPCAPI2::SipConversation::SipConversationHandle handle,
      const CPCAPI2::SipConversation::MediaInfo& remoteAudio,
      const CPCAPI2::SipConversation::MediaInfo& remoteVideo,
      EVENT_VALIDATOR(CPCAPI2::SipConversation::ConversationMediaChangeRequestEvent) validator);
   static void compareMediaInfoInConversationMediaChangeRequest_crypto(
      int mediaIndex,
      const CPCAPI2::SipConversation::MediaInfo& remoteMedia,
      CPCAPI2::SipConversation::ConversationMediaChangeRequestEvent& evt);
   #define assertConversationMediaChangeRequest(account, handle, direction) \
   { \
      SCOPED_TRACE(account.config.name); \
      TestCallEvents::expectConversationMediaChangeRequest(__LINE__, account, handle, direction); \
   }
   #define assertConversationMediaChangeRequest_time(account, handle, direction, timeMs) \
   { \
      SCOPED_TRACE(account.config.name); \
      TestCallEvents::expectConversationMediaChangeRequest(__LINE__, account, handle, direction, timeMs); \
   }
   #define assertConversationMediaChangeRequest_ex(account, handle, validator) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectConversationMediaChangeRequest_ex(__LINE__, account, handle, validator); \
   }
   #define assertConversationMediaChangeRequestAudio_crypto(account, handle, remoteAudio, validator) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectConversationMediaChangeRequestAudio_crypto(__LINE__, account, handle, remoteAudio, validator); \
   }
   #define assertConversationMediaChangeRequest_crypto(account, handle, remoteAudio, remoteVideo, validator) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectConversationMediaChangeRequest_crypto(__LINE__, account, handle, remoteAudio, remoteVideo, validator); \
   }

   static void expectConversationStateChanged(int line, TestAccount& account, 
         CPCAPI2::SipConversation::SipConversationHandle handle,
         CPCAPI2::SipConversation::ConversationState state);
   static void expectConversationStateChanged_time(int line, TestAccount& account,
         CPCAPI2::SipConversation::SipConversationHandle handle,
         CPCAPI2::SipConversation::ConversationState state,
         unsigned int timeMS);
   static void expectConversationStateChanged_ex(int line, TestAccount& account,
         CPCAPI2::SipConversation::SipConversationHandle handle,
         EVENT_VALIDATOR(CPCAPI2::SipConversation::ConversationStateChangedEvent) validator);
   static void expectConversationStateChanged_answermode(int line, TestAccount& account,
         CPCAPI2::SipConversation::SipConversationHandle handle,
         CPCAPI2::SipConversation::ConversationState state,
         CPCAPI2::SipConversation::AnswerModeSettings& answerMode,
         EVENT_VALIDATOR(CPCAPI2::SipConversation::ConversationStateChangedEvent) validator);
   #define assertConversationStateChanged(account, handle, state) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectConversationStateChanged(__LINE__, account, handle, state); \
   }
   #define assertConversationStateChanged_ex(account, handle, validator) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectConversationStateChanged_ex(__LINE__, account, handle, validator); \
   }
   #define assertConversationStateChanged_time(account, handle, state, time) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectConversationStateChanged_time(__LINE__, account, handle, state, time); \
   }
   #define assertConversationStateChanged_answermode(account, handle, state, answerMode, validator) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectConversationStateChanged_answermode(__LINE__, account, handle, state, answerMode, validator); \
   }

   static void expectConversationStateChangedUptilConnected(int line, TestAccount& account,
         CPCAPI2::SipConversation::SipConversationHandle handle,
         EVENT_VALIDATOR(CPCAPI2::SipConversation::ConversationStateChangedEvent) validator);
   #define assertConversationStateChangedUptilConnected(account, handle) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectConversationStateChangedUptilConnected(__LINE__, account, handle, nullptr); \
   }
   #define assertConversationStateChangedUptilConnected_ex(account, handle, validator) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectConversationStateChangedUptilConnected(__LINE__, account, handle, validator); \
   }

   static void expectConversationEnded(int line, TestAccount& account, 
         CPCAPI2::SipConversation::SipConversationHandle handle, 
         CPCAPI2::SipConversation::ConversationEndReason endReason,
         EVENT_VALIDATOR(CPCAPI2::SipConversation::ConversationEndedEvent) validator);
   static void expectConversationEnded_time(int line, TestAccount& account,
         CPCAPI2::SipConversation::SipConversationHandle handle,
         CPCAPI2::SipConversation::ConversationEndReason endReason,
         EVENT_VALIDATOR(CPCAPI2::SipConversation::ConversationEndedEvent) validator,
         unsigned int timeMS);
   #define assertConversationEnded(account, handle, endReason) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectConversationEnded(__LINE__, account, handle, endReason, nullptr); \
   }
   #define assertConversationEnded_ex(account, handle, endReason, validator) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectConversationEnded(__LINE__, account, handle, endReason, validator); \
   }
   #define assertConversationEnded_time(account, handle, endReason, timeMs) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectConversationEnded_time(__LINE__, account, handle, endReason, nullptr, timeMs); \
   }
   #define assertConversationEnded_time_ex(account, handle, endReason, timeMs, validator) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectConversationEnded_time(__LINE__, account, handle, endReason, validator, timeMs); \
   }

   static void expectConversationError(int line, TestAccount& account, 
         CPCAPI2::SipConversation::SipConversationHandle handle);
   #define assertConversationError(account, handle) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectConversationError(__LINE__, account, handle); \
   }

   static void expectConversationStaticsticsUpdated(int line, TestAccount& account, 
         CPCAPI2::SipConversation::SipConversationHandle handle,
         EVENT_VALIDATOR(CPCAPI2::SipConversation::ConversationStatisticsUpdatedEvent) validator);
   #define assertConversationStaticsticsUpdated(account, handle, validator) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectConversationStaticsticsUpdated(__LINE__, account, handle, validator); \
   }

   static void expectTransferProgress(int line, TestAccount& account, 
         CPCAPI2::SipConversation::SipConversationHandle handle,
         CPCAPI2::SipConversation::TransferProgressEventType type);
   #define assertTransferProgress(account, handle, type) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectTransferProgress(__LINE__, account, handle, type); \
   }

   static void expectTransferRequest(int line, TestAccount& account, 
         CPCAPI2::SipConversation::SipConversationHandle handle,
         CPCAPI2::SipConversation::SipConversationHandle* transferTargetConversation,
         EVENT_VALIDATOR(CPCAPI2::SipConversation::TransferRequestEvent) validator);
   #define assertTransferRequest_ex(account, handle, transferTargetConversation, validator) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectTransferRequest(__LINE__, account, handle, transferTargetConversation, validator); \
   }

   static void expectTransferTryingRingingConnected(int line, TestAccount& account, CPCAPI2::SipConversation::SipConversationHandle callHandle);
   #define assertTransferTryingRingingConnected(account, callHandle) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectTransferTryingRingingConnected(__LINE__, account, callHandle); \
   }

   static void expectRedirectRequest(int line, TestAccount& account, 
         CPCAPI2::SipConversation::SipConversationHandle handle,
         EVENT_VALIDATOR(CPCAPI2::SipConversation::RedirectRequestEvent) validator);
   #define assertRedirectRequest(account, handle, validator) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectRedirectRequest(__LINE__, account, handle, validator); \
   }

   // note: this will only assert that media was flowing at some point previously during the current phone call.
   // what it does not help with is asserting that media is flowing at the time of this call to expectMediaFlowing
   static void expectMediaFlowing(int line, TestAccount& account, CPCAPI2::SipConversation::SipConversationHandle handle, bool audio, bool video, EVENT_VALIDATOR(CPCAPI2::SipConversation::ConversationStatisticsUpdatedEvent) validator);
   #define assertAudioFlowing(account, handle) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectMediaFlowing(__LINE__, account, handle, true, false, NULL); \
   }
   #define assertMediaFlowing(account, handle, audio, video) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectMediaFlowing(__LINE__, account, handle, audio, video, NULL); \
   }
   #define assertMediaFlowingEx(account, handle, audio, video, validator) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectMediaFlowingEx(__LINE__, account, handle, audio, video, validator); \
   }

   static void expectMediaDisabled(int line, TestAccount& account, CPCAPI2::SipConversation::SipConversationHandle handle, bool audio, bool video, EVENT_VALIDATOR(CPCAPI2::SipConversation::ConversationStatisticsUpdatedEvent) validator);
   #define assertMediaDisabled(account, handle, audio, video) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectMediaFlowing(__LINE__, account, handle, audio, video, NULL); \
   }
   #define assertMediaDisabledEx(account, handle, audio, video, validator) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectMediaFlowing(__LINE__, account, handle, audio, video, validator); \
   }

   static void expectCallHadMedia(int line, TestAccount& account, CPCAPI2::SipConversation::SipConversationHandle handle, bool audio=true, bool video=false);
   #define assertCallHadAudio(account, handle) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectCallHadMedia(__LINE__, account, handle, true, false); \
   }
   #define assertCallHadMedia(account, handle, audio, video) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectCallHadMedia(__LINE__, account, handle, audio, video); \
   }

   static void expectCallHadIncomingMedia(int line, TestAccount& account, CPCAPI2::SipConversation::SipConversationHandle handle, bool audio = true, bool video = false);
   #define assertCallHadIncomingMedia(account, handle, audio, video) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectCallHadIncomingMedia(__LINE__, account, handle, audio, video); \
   }

   static void expectCallHadOutgoingMedia(int line, TestAccount& account, CPCAPI2::SipConversation::SipConversationHandle handle, bool audio = true, bool video = false);
   #define assertCallHadOutgoingMedia(account, handle, audio, video) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectCallHadOutgoingMedia(__LINE__, account, handle, audio, video); \
   }

   static void expectRTCP(int line, TestAccount& account, CPCAPI2::SipConversation::SipConversationHandle handle, bool audio = true, bool video = false);
   #define assertRTCP(account, handle, audio, video) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestCallEvents::expectRTCP(__LINE__, account, handle, audio, video); \
   }

   static cpc::string convertion(CPCAPI2::SipConversation::ConversationState state);
};

#endif //TEST_CALL_EVENTS_H
