#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#include "cpcapi2_test_fixture.h"
#include "test_framework/cpcapi2_test_framework.h"
#include "test_framework/xmpp_test_helper.h"

#include <sys/types.h>
#include <sys/stat.h>
#ifndef WIN32
#include <unistd.h>
#endif

#ifdef WIN32
#define stat _stat
#endif

#include <stdio.h>  /* defines FILENAME_MAX */
#ifdef WIN32
#include <direct.h>
#define GetCurrentDir _getcwd
#else
#include <unistd.h>
#define GetCurrentDir getcwd
#endif
#include <stdlib.h>     /* srand, rand */
#include <time.h>       /* time */

using namespace CPCAPI2;
using namespace CPCAPI2::Pcap;
using namespace CPCAPI2::test;

class TestPhoneHandler : public CPCAPI2::PhoneHandler
{
public:
   TestPhoneHandler() {}
   virtual ~TestPhoneHandler() {}

   virtual int onError(const cpc::string& sourceModule, const PhoneErrorEvent& args)
   {
      return CPCAPI2::kSuccess;
   }

   virtual int onLicensingError(const LicensingErrorEvent& args)
   {
      return CPCAPI2::kSuccess;
   }

   virtual int onLicensingSuccess()
   {
      return CPCAPI2::kSuccess;
   }
};

class TestPcapHandler : public CPCAPI2::Pcap::PcapHandler
{
public:
   TestPcapHandler() : pcapStarted(false), pcapCompleted(false), pcapFailure(false), totalBytesCaptured(0) {}
   virtual ~TestPcapHandler() {}

   virtual int onQueryInterfacesSuccess(const QueryInterfacesSuccessEvent& evt)
   {
      interfaces = evt.interfaces;
      return CPCAPI2::kSuccess;
   }

   virtual int onCaptureStarted(const CaptureStartedEvent& evt)
   {
      pcapStarted = true;
      captureInterface = evt.pcapInterface;
      return CPCAPI2::kSuccess;
   }

   virtual int onCaptureProgress(unsigned long totalBytes)
   {
      totalBytesCaptured += totalBytes;
      return CPCAPI2::kSuccess;
   }

   virtual int onCaptureCompleted(const CaptureCompletedEvent& evt)
   {
      pcapCompleted = true;
      return CPCAPI2::kSuccess;
   }

   virtual int onError(const ErrorEvent& evt)
   {
      pcapFailure = true;
      return CPCAPI2::kSuccess;
   }

   bool pcapStarted;
   bool pcapCompleted;
   bool pcapFailure;
   unsigned long totalBytesCaptured;
   PcapInterface captureInterface;
   cpc::vector<PcapInterface> interfaces;
};

class PcapTests : public CpcapiAutoTest
{
public:
   PcapTests() {}
   virtual ~PcapTests() {}
};

TEST_F(PcapTests, PcapStartStop) {
#if (CPCAPI2_BRAND_PCAP_MODULE == 1)
   char cCurrentPath[FILENAME_MAX];
   if (GetCurrentDir(cCurrentPath, sizeof(cCurrentPath)))
   {
      cCurrentPath[sizeof(cCurrentPath) - 1] = '\0';
      cpc::string filePath = cCurrentPath;
      filePath.append("/capture.pcap");

      std::unique_ptr<TestPcapHandler> testPcapHandler(new TestPcapHandler());
      std::unique_ptr<TestPhoneHandler> testPhoneHandler(new TestPhoneHandler());
      CPCAPI2::Phone* phone = Phone::create();
      phone->setLoggingEnabled(&AutoTestsLogger::instance(), true);
      LicenseInfo licenseInfo;
      licenseInfo.licenseKey = "";
      licenseInfo.licenseDocumentLocation = "";
      phone->initialize(licenseInfo, testPhoneHandler.get());
      PcapManager* pcapManager = PcapManager::getInterface(phone);
      if (pcapManager)
      {
         pcapManager->setHandler(testPcapHandler.get());

         // Start capturing.
         ASSERT_EQ(pcapManager->start(filePath), kSuccess);
         ASSERT_EQ(pcapManager->process(CPCAPI2::Pcap::PcapManager::kBlockingModeInfinite), kSuccess);
         ASSERT_EQ(testPcapHandler->pcapStarted, true);
         ASSERT_EQ(testPcapHandler->pcapCompleted, false);
         ASSERT_EQ(testPcapHandler->pcapFailure, false);

         // generate at least some traffic on the LAN to be captured.
         // we don't use a SIP account as that would be over loopback
         XmppTestAccount alice("alice");

         // Capture trafic for 5 seconds.
         std::this_thread::sleep_for(std::chrono::milliseconds(5000));
         ASSERT_EQ(pcapManager->process(CPCAPI2::Pcap::PcapManager::kBlockingModeInfinite), kSuccess);

         // Stop capturing.
         ASSERT_EQ(pcapManager->stop(), kSuccess);
         std::this_thread::sleep_for(std::chrono::milliseconds(5000));
         ASSERT_EQ(pcapManager->process(CPCAPI2::Pcap::PcapManager::kBlockingModeInfinite), kSuccess);
         ASSERT_EQ(testPcapHandler->pcapStarted, true);
         ASSERT_EQ(testPcapHandler->pcapCompleted, true);
         ASSERT_EQ(testPcapHandler->pcapFailure, false);
         ASSERT_GT(testPcapHandler->totalBytesCaptured, 0);

         // Check capture.pcap modification time.
         struct stat result;
         ASSERT_EQ(stat(filePath.c_str(), &result), 0);
         auto mod_time = result.st_mtime;
         auto currentTime = std::time(0);   // get time now
         ASSERT_GT(5000, currentTime - mod_time);
      }
      
      Phone::release(phone);
   }

#endif
}

TEST_F(PcapTests, QueryInterfaces) {
#if (CPCAPI2_BRAND_PCAP_MODULE == 1)
   std::unique_ptr<TestPcapHandler> testPcapHandler(new TestPcapHandler());
   std::unique_ptr<TestPhoneHandler> testPhoneHandler(new TestPhoneHandler());
   CPCAPI2::Phone* phone = Phone::create();
   phone->setLoggingEnabled(&AutoTestsLogger::instance(), true);
   LicenseInfo licenseInfo;
   licenseInfo.licenseKey = "";
   licenseInfo.licenseDocumentLocation = "";
   phone->initialize(licenseInfo, testPhoneHandler.get());
   PcapManager* pcapManager = PcapManager::getInterface(phone);

   if (pcapManager)
   {
      pcapManager->setHandler(testPcapHandler.get());

      // Query interfaces.
      ASSERT_EQ(pcapManager->queryInterfaces(), kSuccess);
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      ASSERT_EQ(pcapManager->process(CPCAPI2::Pcap::PcapManager::kBlockingModeNonBlocking), kSuccess);
      ASSERT_EQ(testPcapHandler->pcapStarted, false);
      ASSERT_EQ(testPcapHandler->pcapCompleted, false);
      ASSERT_EQ(testPcapHandler->pcapFailure, false);
      ASSERT_GT(testPcapHandler->interfaces.size(), 0);
   }
   
   Phone::release(phone);

#endif
}

// disabled -- can capture on adapters like thunderbolt
TEST_F(PcapTests, DISABLED_CaptureRandomInterface) {
#if (CPCAPI2_BRAND_PCAP_MODULE == 1)
   char cCurrentPath[FILENAME_MAX];
   if (GetCurrentDir(cCurrentPath, sizeof(cCurrentPath)))
   {
      cCurrentPath[sizeof(cCurrentPath) - 1] = '\0';
      cpc::string filePath = cCurrentPath;
      filePath.append("/capture.pcap");
      std::unique_ptr<TestPcapHandler> testPcapHandler(new TestPcapHandler());
      std::unique_ptr<TestPhoneHandler> testPhoneHandler(new TestPhoneHandler());
      CPCAPI2::Phone* phone = Phone::create();
      phone->setLoggingEnabled(&AutoTestsLogger::instance(), true);
      LicenseInfo licenseInfo;
      licenseInfo.licenseKey = "";
      licenseInfo.licenseDocumentLocation = "";
      phone->initialize(licenseInfo, testPhoneHandler.get());
      PcapManager* pcapManager = PcapManager::getInterface(phone);
      if (pcapManager)
      {
         pcapManager->setHandler(testPcapHandler.get());

         // Query interfaces.
         ASSERT_EQ(pcapManager->queryInterfaces(), kSuccess);
         std::this_thread::sleep_for(std::chrono::milliseconds(1000));
         ASSERT_EQ(pcapManager->process(CPCAPI2::Pcap::PcapManager::kBlockingModeNonBlocking), kSuccess);
         ASSERT_EQ(testPcapHandler->pcapStarted, false);
         ASSERT_EQ(testPcapHandler->pcapCompleted, false);
         ASSERT_EQ(testPcapHandler->pcapFailure, false);
         ASSERT_GT(testPcapHandler->interfaces.size(), 0);

         // initialize random seed.
         srand(time(NULL));

         // generate secret number between 0 and testPcapHandler->interfaces.size().
         int i = rand() % testPcapHandler->interfaces.size();
         pcapManager->setCaptureInterface(testPcapHandler->interfaces[i].name);

         // Start capturing.
         ASSERT_EQ(pcapManager->start(filePath), kSuccess);
         ASSERT_EQ(pcapManager->process(CPCAPI2::Pcap::PcapManager::kBlockingModeInfinite), kSuccess);
         ASSERT_EQ(testPcapHandler->pcapStarted, true);
         ASSERT_EQ(testPcapHandler->pcapCompleted, false);
         ASSERT_EQ(testPcapHandler->pcapFailure, false);
         ASSERT_EQ(testPcapHandler->captureInterface.name, testPcapHandler->interfaces[i].name);

         // Sleep for 1 second.
         std::this_thread::sleep_for(std::chrono::milliseconds(1000));

         // Stop capturing.
         ASSERT_EQ(pcapManager->stop(), kSuccess);
         ASSERT_EQ(pcapManager->process(CPCAPI2::Pcap::PcapManager::kBlockingModeInfinite), kSuccess);
         ASSERT_EQ(testPcapHandler->pcapStarted, true);
         ASSERT_EQ(testPcapHandler->pcapCompleted, true);
         ASSERT_EQ(testPcapHandler->pcapFailure, false);

         // Check capture.pcap modification time.
         struct stat result;
         ASSERT_EQ(stat(filePath.c_str(), &result), 0);
         auto mod_time = result.st_mtime;
         auto currentTime = std::time(0);   // get time now
         ASSERT_GT(5000, currentTime - mod_time);
      }
      
      Phone::release(phone);
   }
#endif
}
