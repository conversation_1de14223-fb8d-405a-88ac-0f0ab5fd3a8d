#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#if 0 // tests disabled -- rely on external server that no longer exists
#if (CPCAPI2_BRAND_RESOURCE_LIST_MODULE == 1)

#include "cpcapi2_test_fixture.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>
#include <libxml/parser.h>
#include "xcap/XcapSubscriptionHandler.h"
#include "../../../../CPCAPI2/impl/xcap/XcapInternalInterface.h"
#include "../../../../CPCAPI2/impl/xcap/XcapResourceListManagerInterface.h"

#include "test_framework/cpcapi2_test_framework.h"
#include "test_framework/bare_bones_sip_endpoint.h"

#include <thread>
#include <future>
#include <cstdio>

using namespace CPCAPI2;
using namespace CPCAPI2::XCAP;
using namespace CPCAPI2::SipEvent;
using namespace CPCAPI2::test;

namespace 
{

class ResourceListTest : public CpcapiAutoTest
{
public:
   ResourceListTest() {}
   virtual ~ResourceListTest() {}
};

TEST_F(ResourceListTest, XcapRlsAddGetRemoveList) 
{
   TestAccount alice("alice");
   XcapResourceListHandle resourceListHandle = 0;
   ResourceList rlsList;
   ResourceListItem item;
   ResourceListItem rlsListChild;

   // Add resource list section
   rlsList.name = "employee-list";
   rlsList.list.displayName = "New employee list";
   rlsList.list.id = "employee-list";
   rlsList.list.itemType = ItemType::List;
   
   item.displayName = "Test item: entry";
   item.id = "test_item_entry";
   item.itemType = ItemType::Entry;
   rlsList.list.list.push_back(item);

   item.displayName = "Test item: entry reference";
   item.id = "resource-lists/users/sip:openxcap@*************/index/~~/resource-lists/list%5b@name=%22employee-list%22%5d/entry%5b@uri=%22test_item_entry%22%5d";
   item.itemType = ItemType::Entry_Ref;
   rlsList.list.list.push_back(item);

   item.displayName = "Test item: external";
   item.id = "http://xcap.example.org/resource-lists/users/sip:<EMAIL>/index/~~/resource-lists/list%5b@name=%22mkting%22%5d";
   item.itemType = ItemType::External;
   rlsList.list.list.push_back(item);
   
   item.displayName = "Test item: list";
   item.id = "test_item_list";
   item.itemType = ItemType::List;
   rlsListChild.displayName = "Test item: list member 1";
   rlsListChild.id = "test_item_list_member_1";
   rlsListChild.itemType = ItemType::Entry;
   item.list.push_back(rlsListChild);
   rlsListChild.displayName = "Test item: list member 2";
   rlsListChild.id = "test_item_list_member_2";
   rlsListChild.itemType = ItemType::Entry;
   item.list.push_back(rlsListChild);
   rlsList.list.list.push_back(item);

   resourceListHandle = alice.xcapResourceListManager->addResourceList(alice.handle, rlsList, "index");
   ASSERT_EQ(resourceListHandle, 1);

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   //check if it exists
   rlsList.name = "";
   rlsList.list.displayName = "";
   rlsList.list.fields.clear();
   rlsList.list.id = "";
   rlsList.list.itemType = ItemType::Entry;
   rlsList.list.list.clear();
   int res = alice.xcapResourceListManager->getResourceList(resourceListHandle, rlsList);
   ASSERT_EQ(res, kSuccess);
   ASSERT_EQ(rlsList.name, "employee-list");

   // Remove resource list section
   res = alice.xcapResourceListManager->removeResourceList(resourceListHandle);
   ASSERT_EQ(res, kSuccess);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   //check if it exists
   rlsList.name = "";
   rlsList.list.displayName = "";
   rlsList.list.fields.clear();
   rlsList.list.id = "";
   rlsList.list.itemType = ItemType::Entry;
   rlsList.list.list.clear();
   res = alice.xcapResourceListManager->getResourceList(resourceListHandle, rlsList);
   ASSERT_EQ(res, kError);

}

TEST_F(ResourceListTest, XcapRlsUpdateList)
{
   TestAccount alice("alice");
   XcapResourceListHandle resourceListHandle = 0;
   ResourceList list;
   XcapResourceListUpdateRequestHandle updateRequestHandle;
   ResourceListItem item1;
   ResourceListItem item2;

   item1.displayName = "test_item_1";
   item1.id = "test_item_1";
   item1.itemType = ItemType::Entry;

   list.name = "good-friends";
   list.list.itemType = ItemType::List;
   list.list.displayName = "Good friends";
   list.list.id = "good-friends";
   list.list.list.push_back(item1);
   resourceListHandle = alice.xcapResourceListManager->addResourceList(alice.handle, list, "index");

   // add
   updateRequestHandle = alice.xcapResourceListManager->createUpdateRequest(resourceListHandle);
   item2.displayName = "test_item_2";
   item2.id = "test_item_2";
   item2.itemType = ItemType::Entry;
   alice.xcapResourceListManager->addItem(updateRequestHandle, item2);
   alice.xcapResourceListManager->start(updateRequestHandle);
   {
      XcapResourceListUpdateEvent evt;
      ASSERT_TRUE(alice.xcapResourceListEvents->expectEvent("XcapResourceListHandler::onResourceListUpdate",
         20000, AlwaysTruePred(), resourceListHandle, evt));
      ASSERT_EQ(evt.account, alice.handle);
      ASSERT_EQ(evt.resourceList, resourceListHandle);
      ASSERT_EQ(evt.changes[0].changeType, ChangeType::ChangeType_Add);
      ASSERT_EQ(evt.changes[0].changedItem.displayName, "test_item_2");
      ASSERT_EQ(evt.changes[0].changedItem.id, "test_item_2");
      ASSERT_EQ(evt.changes[0].changedItem.itemType, ItemType::Entry);
   }

   // update
   updateRequestHandle = alice.xcapResourceListManager->createUpdateRequest(resourceListHandle);
   item1.displayName = "Test item 1 si changed!";
   item1.id = "test_item_1";
   item1.itemType = ItemType::Entry;
   alice.xcapResourceListManager->updateItem(updateRequestHandle, item1);
   alice.xcapResourceListManager->start(updateRequestHandle);
   {
      XcapResourceListUpdateEvent evt;
      ASSERT_TRUE(alice.xcapResourceListEvents->expectEvent("XcapResourceListHandler::onResourceListUpdate",
         20000, AlwaysTruePred(), resourceListHandle, evt));
      ASSERT_EQ(evt.account, alice.handle);
      ASSERT_EQ(evt.resourceList, resourceListHandle);
      ASSERT_EQ(evt.changes[0].changeType, ChangeType::ChangeType_Update);
      ASSERT_EQ(evt.changes[0].changedItem.displayName, "Test item 1 si changed!");
      ASSERT_EQ(evt.changes[0].changedItem.id, "test_item_1");
      ASSERT_EQ(evt.changes[0].changedItem.itemType, ItemType::Entry);
   }

   // remove
   updateRequestHandle = alice.xcapResourceListManager->createUpdateRequest(resourceListHandle);
   alice.xcapResourceListManager->removeItem(updateRequestHandle, item2);
   alice.xcapResourceListManager->start(updateRequestHandle);
   {
      XcapResourceListUpdateEvent evt;
      ASSERT_TRUE(alice.xcapResourceListEvents->expectEvent("XcapResourceListHandler::onResourceListUpdate",
         20000, AlwaysTruePred(), resourceListHandle, evt));
      ASSERT_EQ(evt.account, alice.handle);
      ASSERT_EQ(evt.resourceList, resourceListHandle);
      ASSERT_EQ(evt.changes[0].changeType, ChangeType::ChangeType_Remove);
      ASSERT_EQ(evt.changes[0].changedItem.displayName, "test_item_2");
      ASSERT_EQ(evt.changes[0].changedItem.id, "test_item_2");
      ASSERT_EQ(evt.changes[0].changedItem.itemType, ItemType::Entry);
   }
}

TEST_F(ResourceListTest, XcapRlsQueryList)
{
   TestAccount alice("alice");
   XcapResourceListHandle resourceListHandle = 0;
   ResourceList list;
   ResourceListItem item1;

   item1.displayName = "test_item_1";
   item1.id = "test_item_1";
   item1.itemType = ItemType::Entry;

   list.name = "good-friends";
   list.list.itemType = ItemType::List;
   list.list.displayName = "Good friends";
   list.list.id = "good-friends";
   list.list.list.push_back(item1);
   resourceListHandle = alice.xcapResourceListManager->addResourceList(alice.handle, list, "index");

   //std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   alice.xcapResourceListManager->queryResourceList(alice.handle, resourceListHandle);

   {
      XcapResourceListUpdateEvent evt;
      ASSERT_TRUE(alice.xcapResourceListEvents->expectEvent("XcapResourceListHandler::onResourceListUpdate",
         20000, AlwaysTruePred(), resourceListHandle, evt));
      ASSERT_EQ(evt.account, alice.handle);
      ASSERT_EQ(evt.resourceList, resourceListHandle);
      ASSERT_EQ(evt.changes[0].changeType, ChangeType::ChangeType_Query);
      ASSERT_EQ(evt.changes[0].changedItem.displayName, "Good friends");
      ASSERT_EQ(evt.changes[0].changedItem.id, "good-friends");
      ASSERT_EQ(evt.changes[0].changedItem.itemType, ItemType::List);
   }
}

}

#endif //CPCAPI2_BRAND_RESOURCE_LIST_MODULE
#endif // tests disabled -- rely on external server that no longer exists