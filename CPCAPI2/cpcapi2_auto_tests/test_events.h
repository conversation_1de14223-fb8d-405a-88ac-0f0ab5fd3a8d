#pragma once
#ifndef TEST_EVENTS_H
#define TEST_EVENTS_H

#include "cpcapi2_test_fixture.h"

class TestEvents
{
public:

   static void expectPhoneError(int line, TestAccount& account, const cpc::string& module,
      void(*validator)(const CPCAPI2::PhoneErrorEvent& evt));
   static void expectPhoneErrorEx(int line, CPCAPI2::test::EventHandler* events, TestAccount& account, const cpc::string& module,
      void(*validator)(const CPCAPI2::PhoneErrorEvent& evt));
   #define assertPhoneError(account, module) \
         TestEvents::expectPhoneError(__LINE__, account, module, NULL)
   #define assertPhoneError_ex(account, module, validator) \
         TestEvents::expectPhoneError(__LINE__, account, module, validator)
   #define assertPhoneErrorEx(events, account, module) \
         TestEvents::expectPhoneErrorEx(__LINE__, events, account, module, NULL)
   #define assertPhoneErrorEx2(events, account, module, validator) \
         TestEvents::expectPhoneErrorEx(__LINE__, events, account, module, validator)

};

#endif //TEST_EVENTS_H
