#if _WIN32
#include "stdafx.h"
#endif

#ifdef _WIN32

#include "cpcapi2_test_fixture.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>
#include "../impl/util/RegUtils.h"
#include <string>

#include "test_framework/cpcapi2_test_framework.h"

using namespace CPCAPI2;
using namespace CPCAPI2::test;

namespace {
   class RegUtilsAutoTests : public CpcapiAutoTest
   {
   public:
      RegUtilsAutoTests() {}
      ~RegUtilsAutoTests() {}
   };

   TEST_F(RegUtilsAutoTests, RegUtilsBasicTests)
   {
      char basicSubKeyPath[] = "CounterPath\\PierreTest";
      char basicStringValueName[] = "test";
      char basicStringValue[] = "test";
      char basicDwValueName[] = "dwtest";
      unsigned long basicDwValue = 2;
      unsigned long basicDwOutValue = 0;

      // Test write
      ASSERT_TRUE(RegUtils::CreateKey(HKEY_CURRENT_USER, basicSubKeyPath));
      ASSERT_TRUE(RegUtils::SetStringValue(HKEY_CURRENT_USER, basicSubKeyPath, basicStringValueName, basicStringValue));
      ASSERT_TRUE(RegUtils::SetDWORDValue(HKEY_CURRENT_USER, basicSubKeyPath, basicDwValueName, basicDwValue));

      std::string basicStringOutValue;
      ASSERT_TRUE(RegUtils::QueryStringValue(HKEY_CURRENT_USER, basicSubKeyPath, basicStringValueName, basicStringOutValue));
      ASSERT_TRUE(basicStringOutValue == basicStringValue);

      basicDwOutValue = RegUtils::QueryDWORDValue(HKEY_CURRENT_USER, basicSubKeyPath, basicDwValueName);
      ASSERT_EQ(basicDwOutValue, basicDwValue);

      ASSERT_TRUE(RegUtils::DeleteValue(HKEY_CURRENT_USER, basicSubKeyPath, basicStringValueName));
      ASSERT_TRUE(RegUtils::DeleteValue(HKEY_CURRENT_USER, basicSubKeyPath, basicDwValueName));
   }
}

#endif
