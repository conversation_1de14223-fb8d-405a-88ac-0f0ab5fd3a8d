#include "cpcapi2_test_fixture.h"
#include "test_account_events.h"
#include "test_events.h"
#include "test_call_events.h"

#include "../../impl/watchdog/WatchdogManagerInt.h"

using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::Watchdog;
using namespace CPCAPI2::SipConversation;

namespace
{

   class WatchdogTest : public CpcapiAutoTest
   {
   public:
      WatchdogTest() {}
      virtual ~WatchdogTest() {}
      void sleepFn();
      void noOpFn();
   };
   
   class MyWatchdogTestHandler : public WatchdogHandler
   {
   public:
      MyWatchdogTestHandler() : receivedEvent(false) {}
   
      int threadAlert()
      {
         receivedEvent = true;
         return kSuccess;
      }

      bool receivedEvent;
   };
   
   void WatchdogTest::sleepFn()
   {
      std::this_thread::sleep_for(std::chrono::seconds(6));
   }
   
   void WatchdogTest::noOpFn()
   {
   }


   TEST_F(WatchdogTest, TestAlert)
   {
      MyWatchdogTestHandler* handler = new MyWatchdogTestHandler();
      {
         TestAccount alice("alice", Account_Init);
         WatchdogManagerInt* wi = static_cast<WatchdogManagerInt*>(WatchdogManager::getInterface(alice.phone));
         
#if 0
         WatchdogSettings settings;
         settings.dumpFolder = "~/Desktop";
         wi->configureSettings(settings);
#endif

         // have seen loading from OS certificate store trigger the watchdog, so let's
         // enable the account first, then enable the watchdog after
         alice.enable();
         
         wi->setHandler(handler);
         wi->startThreadWatchdog();
         
         ASSERT_FALSE(handler->receivedEvent);
         
         PhoneInterface* pi = static_cast<PhoneInterface*>(alice.phone);
         pi->getSdkModuleThread().post(resip::resip_bind(&WatchdogTest::sleepFn, this));
         
         std::atomic_bool threadStopFlag(false);
         auto alertEvent = std::async(std::launch::async, [&] ()
         {
            while (handler->receivedEvent == false)
            {
               if (threadStopFlag) return;

               std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }
         });
         flaggableWaitFor(alertEvent, threadStopFlag);
      }
      
      // no support for removing handler from WatchdogManagerInt at this point, so remove it late
      delete handler;
   }
   
   TEST_F(WatchdogTest, HangStarcode)
   {
      TestAccount alice("alice");
      
      MyWatchdogTestHandler* handler = new MyWatchdogTestHandler();
      {
         WatchdogManagerInt* wi = static_cast<WatchdogManagerInt*>(WatchdogManager::getInterface(alice.phone));
         wi->setHandler(handler);
         
         {
            SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
            alice.conversation->addParticipant(aliceCall, "sip:***watchdogcpcapi2");
            alice.conversation->start(aliceCall);
         }
         
         {
            SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
            alice.conversation->addParticipant(aliceCall, "sip:***hangcpcapi2");
            alice.conversation->start(aliceCall);
         }
            
         std::atomic_bool threadStopFlag(false);
         auto alertEvent = std::async(std::launch::async, [&] ()
         {
            while (handler->receivedEvent == false)
            {
               if (threadStopFlag) return;

               std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }
         });
         flaggableWaitFor(alertEvent, threadStopFlag);
      }
   }
   

   TEST_F(WatchdogTest, TestMultipleAlerts)
   {
      MyWatchdogTestHandler* handler = new MyWatchdogTestHandler();
      {
         TestAccount alice("alice", Account_Init);
         WatchdogManagerInt* wi = static_cast<WatchdogManagerInt*>(WatchdogManager::getInterface(alice.phone));
         
         // have seen loading from OS certificate store trigger the watchdog, so let's
         // enable the account first, then enable the watchdog after
         alice.enable();
         
         wi->setHandler(handler);
         wi->startThreadWatchdog();
         
         ASSERT_FALSE(handler->receivedEvent);
         
         PhoneInterface* pi = static_cast<PhoneInterface*>(alice.phone);
         pi->getSdkModuleThread().post(resip::resip_bind(&WatchdogTest::sleepFn, this));
         
         std::atomic_bool threadStopFlag(false);
         auto alertEvent = std::async(std::launch::async, [&] ()
         {
            while (handler->receivedEvent == false)
            {
               if (threadStopFlag) return;

               std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }
         });
         flaggableWaitFor(alertEvent, threadStopFlag);
         

         handler->receivedEvent = false;
         std::this_thread::sleep_for(std::chrono::milliseconds(11000));

         // go for a second round; the watchdog will back off for 10 seconds before doing any further probes
         pi->getSdkModuleThread().post(resip::resip_bind(&WatchdogTest::sleepFn, this));
         alertEvent = std::async(std::launch::async, [&] ()
         {
            while (handler->receivedEvent == false)
            {
               if (threadStopFlag) return;

               std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }
         });
         flaggableWaitFor(alertEvent, threadStopFlag);
         
         
      }
      
      // no support for removing handler from WatchdogManagerInt at this point, so remove it late
      delete handler;
   }
   
   TEST_F(WatchdogTest, TestNoAlert)
   {
      MyWatchdogTestHandler* handler = new MyWatchdogTestHandler();
      {
         TestAccount alice("alice", Account_Init);
         
         // have seen loading from OS certificate store trigger the watchdog, so let's
         // enable the account first, then enable the watchdog after
         alice.enable();
         
         WatchdogManagerInt* wi = static_cast<WatchdogManagerInt*>(WatchdogManager::getInterface(alice.phone));
         wi->setHandler(handler);
         wi->startThreadWatchdog();
         
         ASSERT_FALSE(handler->receivedEvent);
         
         PhoneInterface* pi = static_cast<PhoneInterface*>(alice.phone);
         pi->getSdkModuleThread().post(resip::resip_bind(&WatchdogTest::noOpFn, this));
         
         std::this_thread::sleep_for(std::chrono::milliseconds(5000));
         ASSERT_FALSE(handler->receivedEvent);
      }
   
      // no support for removing handler from WatchdogManagerInt at this point, so remove it late
      delete handler;
   }
   
   TEST_F(WatchdogTest, BasicCallNoAlert) {
      TestAccount alice("alice");
      TestAccount bob("bob");
      
      MyWatchdogTestHandler* handler = new MyWatchdogTestHandler();
      {
         WatchdogManagerInt* wi = static_cast<WatchdogManagerInt*>(WatchdogManager::getInterface(alice.phone));
         wi->setHandler(handler);
         wi->startThreadWatchdog();

         // make an outgoing (audio only) call from Alice to Bob
         SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
         alice.conversation->addParticipant(aliceCall, bob.config.uri());
         alice.conversation->start(aliceCall);

         auto aliceEvents = std::async(std::launch::async, [&] () {
            assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
            assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
            assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
            assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
            std::this_thread::sleep_for(std::chrono::milliseconds(5000));
            assertAudioFlowing(alice, aliceCall);
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
            assertSuccess(alice.conversation->end(aliceCall));
            assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
            assertCallHadAudio(alice, aliceCall);
         });

         auto bobEvents = std::async(std::launch::async, [&] () {
            SipConversationHandle bobCall;
            assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
            assertSuccess(bob.conversation->sendRingingResponse(bobCall));
            assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
            assertSuccess(bob.conversation->accept(bobCall));
            assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
            assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
            std::this_thread::sleep_for(std::chrono::milliseconds(1000));
            assertAudioFlowing(bob, bobCall);

            {
               SipConversationHandle h;
               ConversationEndedEvent evt;
               ASSERT_TRUE(bob.conversationEvents->expectEvent("SipConversationHandler::onConversationEnded",
                  35000, HandleEqualsPred<SipConversationHandle>(bobCall), h, evt));
               ASSERT_EQ(ConversationEndReason_UserTerminatedRemotely, evt.endReason);
            }
            assertCallHadAudio(bob, bobCall);
         });

         waitFor2(aliceEvents, bobEvents);
         
         ASSERT_FALSE(handler->receivedEvent);
      }
      delete handler;
   }
}
