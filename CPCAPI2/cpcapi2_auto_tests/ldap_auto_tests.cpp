#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#include "cpcapi2_test_fixture.h"
#include "test_account_events.h"
#include "test_events.h"

#include "interface/experimental/ldap/LdapManager.h"

using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::OpenLdap;

#define CHANGE_STATE_WAIT 500

class TestPhoneHandler : public CPCAPI2::PhoneHandler
{
public:
   TestPhoneHandler() {}
   virtual ~TestPhoneHandler() {}

   virtual int onError(const cpc::string& sourceModule, const PhoneErrorEvent& args)
   {
      return CPCAPI2::kSuccess;
   }

   virtual int onLicensingError(const LicensingErrorEvent& args)
   {
      return CPCAPI2::kSuccess;
   }

   virtual int onLicensingSuccess()
   {
      return CPCAPI2::kSuccess;
   }
};

class TestLdapHandler : public CPCAPI2::OpenLdap::<PERSON>da<PERSON>Handler
{
public:
   TestLdapHandler() : searchCompleted(false)
   {
      onStateChangedEvent.state = LdapState::LdapDisconnected;
   }

   virtual ~TestLdapHandler() {}

   virtual int onSearchCompleted(const LdapClientHandle handle, const LdapDataEvent& args)
   {
      entries = args.entries;
      searchCompleted = true;
      return CPCAPI2::kSuccess;
   }

   virtual int onStateChanged(const LdapClientHandle handle, const OnStateChangedEvent& args)
   {
      onStateChangedEvent.state = args.state;
      return CPCAPI2::kSuccess;
   }

   virtual int onError(const LdapClientHandle handle, const ErrorEvent& args)
   {
      errorEvent = args;
      return CPCAPI2::kSuccess;
   }

   bool searchCompleted;
   cpc::vector<LdapDataEntry> entries;
   OnStateChangedEvent onStateChangedEvent;
   ErrorEvent errorEvent;
};

   // TODO: convert event handling to auto test style (process_test / EventHandler)
namespace {
   class LdapModuleTest : public CpcapiAutoTest
   {
   public:
      // Add server ip and test account info.
      // For mServerUrl on Android use something like: ldap://***********
      // Make sure that you can reach server from test device.
      LdapModuleTest() :
#ifdef WIN32
         mServerUrl("***********"),
#else
         mServerUrl("ldaps://***********"),
		 //mServerUrl("ldap://***********"),
#endif
//		 mTestUsername("cp\\djevtic"),
         mTestUsername("CN = ldap ldap, OU = Service Accounts, OU = Vancouver, OU = Canada, OU = Corp.CounterPath, DC = cp, DC = local"),
         mTestPassword("x10ldap") {}
      virtual ~LdapModuleTest() {}
      const cpc::string mServerUrl;
      const cpc::string mTestUsername;
      const cpc::string mTestPassword;
   };

   TEST_F(LdapModuleTest, DISABLED_LdapConnectionError) {
#if (CPCAPI2_BRAND_OPEN_LDAP_MODULE == 1)
      // Set server IP and user credentials.
      ASSERT_FALSE(mServerUrl.empty());
      ASSERT_FALSE(mTestUsername.empty());
      ASSERT_FALSE(mTestPassword.empty());
      std::unique_ptr<TestPhoneHandler> testPhoneHandler(new TestPhoneHandler());
      std::unique_ptr<TestLdapHandler> testLdapHandler(new TestLdapHandler());
      CPCAPI2::Phone* phone = Phone::create();
      LicenseInfo licenseInfo;
      licenseInfo.licenseKey = "";
      licenseInfo.licenseDocumentLocation = "";
      phone->initialize(licenseInfo, testPhoneHandler.get());
      LdapManager* ldapManager = LdapManager::getInterface(phone);
      if (ldapManager)
      {
         LdapClientHandle ldapClientHandle = ldapManager->createClient();
         ASSERT_GT(ldapClientHandle, 0);
         ASSERT_EQ(ldapManager->setHandler(ldapClientHandle, testLdapHandler.get()), kSuccess);
         LdapClientSettings ldapClientSettings;
         ldapClientSettings.serverUrl = mServerUrl;
         ldapClientSettings.encryption = LdapEncryption_Ldaps;
         ldapClientSettings.cert_strategy = LdapCertStrategy_Hard;

         ASSERT_EQ(ldapManager->applySettings(ldapClientHandle, ldapClientSettings), kSuccess);

         // Connect to ldap server.
         ASSERT_EQ(ldapManager->connect(ldapClientHandle), kSuccess);
         do {
             std::this_thread::sleep_for(std::chrono::milliseconds(CHANGE_STATE_WAIT));
             ldapManager->process(CPCAPI2::OpenLdap::LdapManager::kBlockingModeNonBlocking);
         } while (testLdapHandler->onStateChangedEvent.state == LdapState::LdapConnecting);
         ASSERT_EQ(testLdapHandler->onStateChangedEvent.state, LdapState::LdapDisconnected);
         ASSERT_EQ(testLdapHandler->errorEvent.errorType, LdapConnectionError);
#ifdef WIN32
         ASSERT_EQ(strcmp("Parameter Error", testLdapHandler->errorEvent.errorText.c_str()), 0);
#else
         ASSERT_EQ(strcmp("Authentication method not supported", testLdapHandler->errorEvent.errorText.c_str()), 0);
#endif
      }
#endif // CPCAPI2_BRAND_OPEN_LDAP_MODULE
   }

   TEST_F(LdapModuleTest, LdapServerUnreachable) {
#if (CPCAPI2_BRAND_OPEN_LDAP_MODULE == 1)
      // Set server IP and user credentials.
      ASSERT_FALSE(mServerUrl.empty());
      ASSERT_FALSE(mTestUsername.empty());
      ASSERT_FALSE(mTestPassword.empty());
      std::unique_ptr<TestPhoneHandler> testPhoneHandler(new TestPhoneHandler());
      std::unique_ptr<TestLdapHandler> testLdapHandler(new TestLdapHandler());
      
      TestAccount alice("alice", Account_Init);

      LdapManager* ldapManager = LdapManager::getInterface(alice.phone);
      if (ldapManager)
      {
         LdapClientHandle ldapClientHandle = ldapManager->createClient();
         ASSERT_GT(ldapClientHandle, 0);
         ASSERT_EQ(ldapManager->setHandler(ldapClientHandle, testLdapHandler.get()), kSuccess);
         LdapClientSettings ldapClientSettings;
#ifdef WIN32
         ldapClientSettings.serverUrl = "*********"; // non-routable
#else
         ldapClientSettings.serverUrl = "ldaps://*********"; // non-routable
#endif
         ldapClientSettings.username = "error";
         ldapClientSettings.password = "hiddenpassword";
         ldapClientSettings.encryption = LdapEncryption_Ldaps;
         ldapClientSettings.cert_strategy = LdapCertStrategy_Hard;
         ldapClientSettings.connection_timeout = 5;

         ASSERT_EQ(ldapManager->applySettings(ldapClientHandle, ldapClientSettings), kSuccess);

         // Connect to ldap server.
         ASSERT_EQ(ldapManager->connect(ldapClientHandle), kSuccess);
         do {
             std::this_thread::sleep_for(std::chrono::milliseconds(CHANGE_STATE_WAIT));
             ldapManager->process(CPCAPI2::OpenLdap::LdapManager::kBlockingModeNonBlocking);
         } while (testLdapHandler->onStateChangedEvent.state == LdapState::LdapConnecting);
         ASSERT_EQ(testLdapHandler->onStateChangedEvent.state, LdapState::LdapDisconnected);
         ASSERT_EQ(testLdapHandler->errorEvent.errorType, LdapConnectionError);
#ifdef WIN32
         ASSERT_EQ(strcmp("Server Down", testLdapHandler->errorEvent.errorText.c_str()), 0);
#else
         ASSERT_EQ(strcmp("Can't contact LDAP server", testLdapHandler->errorEvent.errorText.c_str()), 0);
#endif
      }
#endif // CPCAPI2_BRAND_OPEN_LDAP_MODULE
   }

   // Be careful with this test. You can lock your account.
#if 0
   TEST_F(LdapModuleTest, DISABLED_LdapInvalidCredentials) {
#if (CPCAPI2_BRAND_OPEN_LDAP_MODULE == 1)
      // Set server IP and user credentials.
      ASSERT_FALSE(mServerUrl.empty());
      ASSERT_FALSE(mTestUsername.empty());
      ASSERT_FALSE(mTestPassword.empty());
      std::unique_ptr<TestPhoneHandler> testPhoneHandler(new TestPhoneHandler());
      std::unique_ptr<TestLdapHandler> testLdapHandler(new TestLdapHandler());
      CPCAPI2::Phone* phone = Phone::create();
      LicenseInfo licenseInfo;
      licenseInfo.licenseKey = "";
      licenseInfo.licenseDocumentLocation = "";
      phone->initialize(licenseInfo, testPhoneHandler.get());
      LdapManager* ldapManager = LdapManager::getInterface(phone);
      if (ldapManager)
      {
         LdapClientHandle ldapClientHandle = ldapManager->createClient();
         ASSERT_GT(ldapClientHandle, 0);
         ASSERT_EQ(ldapManager->setHandler(ldapClientHandle, testLdapHandler.get()), kSuccess);
         LdapClientSettings ldapClientSettings;
         ldapClientSettings.username = mTestUsername;
         ldapClientSettings.password = "wrongPassword";
         ldapClientSettings.serverUrl = mServerUrl;
         ldapClientSettings.encryption = LdapEncryption_Ldaps;
         ldapClientSettings.cert_strategy = LdapCertStrategy_Hard;

         ASSERT_EQ(ldapManager->applySettings(ldapClientHandle, ldapClientSettings), kSuccess);

         // Connect to ldap server.
         ASSERT_EQ(ldapManager->connect(ldapClientHandle), kSuccess);
         ASSERT_EQ(ldapManager->connect(ldapClientHandle), kSuccess);
         do {
             std::this_thread::sleep_for(std::chrono::milliseconds(CHANGE_STATE_WAIT));
             ldapManager->process(CPCAPI2::OpenLdap::LdapManager::kBlockingModeNonBlocking);
         } while (testLdapHandler->onStateChangedEvent.state == LdapState::LdapConnecting);
         ASSERT_EQ(testLdapHandler->onStateChangedEvent.state, LdapState::LdapDisconnected);
         ASSERT_EQ(testLdapHandler->errorEvent.errorType, LdapConnectionError);
         ASSERT_EQ(testLdapHandler->errorEvent.errorText, "Invalid Credentials");
      }
#endif // CPCAPI2_BRAND_OPEN_LDAP_MODULE
   }
#endif

   TEST_F(LdapModuleTest, DISABLED_LdapConnectDisconnectSuccess) {
#if (CPCAPI2_BRAND_OPEN_LDAP_MODULE == 1)
      // Set server IP and user credentials.
      ASSERT_FALSE(mServerUrl.empty());
      ASSERT_FALSE(mTestUsername.empty());
      ASSERT_FALSE(mTestPassword.empty());
      std::unique_ptr<TestPhoneHandler> testPhoneHandler(new TestPhoneHandler());
      std::unique_ptr<TestLdapHandler> testLdapHandler(new TestLdapHandler());
      CPCAPI2::Phone* phone = Phone::create();
      LicenseInfo licenseInfo;
      licenseInfo.licenseKey = "";
      licenseInfo.licenseDocumentLocation = "";
      phone->initialize(licenseInfo, testPhoneHandler.get());
      LdapManager* ldapManager = LdapManager::getInterface(phone);
      if (ldapManager)
      {
         LdapClientHandle ldapClientHandle = ldapManager->createClient();
         ASSERT_GT(ldapClientHandle, 0);
         ASSERT_EQ(ldapManager->setHandler(ldapClientHandle, testLdapHandler.get()), kSuccess);
         LdapClientSettings ldapClientSettings;
         ldapClientSettings.username = mTestUsername;
         ldapClientSettings.password = mTestPassword;
         ldapClientSettings.serverUrl = mServerUrl;
         ldapClientSettings.encryption = LdapEncryption_Ldaps;
         ldapClientSettings.cert_strategy = LdapCertStrategy_Hard;

         ASSERT_EQ(ldapManager->applySettings(ldapClientHandle, ldapClientSettings), kSuccess);

         // Connect to ldap server.
         ASSERT_EQ(ldapManager->connect(ldapClientHandle), kSuccess);
         do {
             std::this_thread::sleep_for(std::chrono::milliseconds(CHANGE_STATE_WAIT));
             ldapManager->process(CPCAPI2::OpenLdap::LdapManager::kBlockingModeNonBlocking);
         } while (testLdapHandler->onStateChangedEvent.state == LdapState::LdapConnecting);

         ASSERT_EQ(testLdapHandler->onStateChangedEvent.state, LdapState::LdapConnected);

         // Disconnect from ldap server.
         ASSERT_EQ(ldapManager->disconnect(ldapClientHandle), kSuccess);
         do {
             std::this_thread::sleep_for(std::chrono::milliseconds(CHANGE_STATE_WAIT));
             ldapManager->process(CPCAPI2::OpenLdap::LdapManager::kBlockingModeNonBlocking);
         } while (testLdapHandler->onStateChangedEvent.state == LdapState::LdapConnected);
         ASSERT_EQ(testLdapHandler->onStateChangedEvent.state, LdapState::LdapDisconnected);
      }
#endif // CPCAPI2_BRAND_OPEN_LDAP_MODULE
   }

   TEST_F(LdapModuleTest, DISABLED_LdapConnectDisconnectSuccess10Times) {
#if (CPCAPI2_BRAND_OPEN_LDAP_MODULE == 1)
      // Set server IP and user credentials.
      ASSERT_FALSE(mServerUrl.empty());
      ASSERT_FALSE(mTestUsername.empty());
      ASSERT_FALSE(mTestPassword.empty());
      std::unique_ptr<TestPhoneHandler> testPhoneHandler(new TestPhoneHandler());
      std::unique_ptr<TestLdapHandler> testLdapHandler(new TestLdapHandler());
      CPCAPI2::Phone* phone = Phone::create();
      LicenseInfo licenseInfo;
      licenseInfo.licenseKey = "";
      licenseInfo.licenseDocumentLocation = "";
      phone->initialize(licenseInfo, testPhoneHandler.get());
      LdapManager* ldapManager = LdapManager::getInterface(phone);
      if (ldapManager)
      {
         LdapClientHandle ldapClientHandle = ldapManager->createClient();
         ASSERT_GT(ldapClientHandle, 0);
         ASSERT_EQ(ldapManager->setHandler(ldapClientHandle, testLdapHandler.get()), kSuccess);
         LdapClientSettings ldapClientSettings;
         ldapClientSettings.username = mTestUsername;
         ldapClientSettings.password = mTestPassword;
         ldapClientSettings.serverUrl = mServerUrl;
         ldapClientSettings.encryption = LdapEncryption_Ldaps;
         ldapClientSettings.cert_strategy = LdapCertStrategy_Hard;

         ASSERT_EQ(ldapManager->applySettings(ldapClientHandle, ldapClientSettings), kSuccess);

         for ( int i = 0; i < 10; i++ ) {
             // Connect to ldap server.
             ASSERT_EQ(ldapManager->connect(ldapClientHandle), kSuccess);
             do {
                 std::this_thread::sleep_for(std::chrono::milliseconds(CHANGE_STATE_WAIT));
                 ldapManager->process(CPCAPI2::OpenLdap::LdapManager::kBlockingModeNonBlocking);
             } while (testLdapHandler->onStateChangedEvent.state == LdapState::LdapConnecting);

             ASSERT_EQ(testLdapHandler->onStateChangedEvent.state, LdapState::LdapConnected);

             // Disconnect from ldap server.
             ASSERT_EQ(ldapManager->disconnect(ldapClientHandle), kSuccess);
             do {
                 std::this_thread::sleep_for(std::chrono::milliseconds(CHANGE_STATE_WAIT));
                 ldapManager->process(CPCAPI2::OpenLdap::LdapManager::kBlockingModeNonBlocking);
             } while (testLdapHandler->onStateChangedEvent.state == LdapState::LdapConnected);
             ASSERT_EQ(testLdapHandler->onStateChangedEvent.state, LdapState::LdapDisconnected);
         }
      }
#endif // CPCAPI2_BRAND_OPEN_LDAP_MODULE
   }

   TEST_F(LdapModuleTest, DISABLED_LdapSearch) {
#if (CPCAPI2_BRAND_OPEN_LDAP_MODULE == 1)
      std::unique_ptr<TestPhoneHandler> testPhoneHandler(new TestPhoneHandler());
      std::unique_ptr<TestLdapHandler> testLdapHandler(new TestLdapHandler());
      CPCAPI2::Phone* phone = Phone::create();
      LicenseInfo licenseInfo;
      licenseInfo.licenseKey = "";
      licenseInfo.licenseDocumentLocation = "";
      phone->initialize(licenseInfo, testPhoneHandler.get());
      LdapManager* ldapManager = LdapManager::getInterface(phone);
      if (ldapManager)
      {
         LdapClientHandle ldapClientHandle = ldapManager->createClient();
         ASSERT_GT(ldapClientHandle, 0);
         ASSERT_EQ(ldapManager->setHandler(ldapClientHandle, testLdapHandler.get()), kSuccess);
         LdapClientSettings ldapClientSettings;
         ldapClientSettings.username = mTestUsername;
         ldapClientSettings.password = mTestPassword;
         ldapClientSettings.serverUrl = mServerUrl;
         ldapClientSettings.encryption = LdapEncryption_Ldaps;
         ldapClientSettings.cert_strategy = LdapCertStrategy_Hard;

         ASSERT_EQ(ldapManager->applySettings(ldapClientHandle, ldapClientSettings), kSuccess);

         LdapDataMap dataMap;
         dataMap.displayName = "cn";
         dataMap.firstName = "firstName";
         dataMap.lastName = "sn";
         dataMap.softphone = "internalnumber";
         dataMap.jobTitle = "job";
         dataMap.department = "service";
         dataMap.city = "city";
         dataMap.workPhone = "telephonenumber";
         dataMap.homePhone = "homephone";
         dataMap.mobilePhone = "mobile";
         dataMap.email = "mail";
         dataMap.jabber = "jabber";

         ASSERT_EQ(ldapManager->setDataMap(ldapClientHandle, dataMap), kSuccess);
         // Connect to ldap server.
         ASSERT_EQ(ldapManager->connect(ldapClientHandle), kSuccess);
         do {
            std::this_thread::sleep_for(std::chrono::milliseconds(CHANGE_STATE_WAIT));
            ldapManager->process(CPCAPI2::OpenLdap::LdapManager::kBlockingModeNonBlocking);
         } while (testLdapHandler->onStateChangedEvent.state == LdapState::LdapConnecting);

         ASSERT_EQ(testLdapHandler->onStateChangedEvent.state, LdapState::LdapConnected);

         ASSERT_EQ(ldapManager->search(ldapClientHandle, "(cn=*Android*)", "DC=cp,DC=local",
                                       LdapSearchScope_SubTree, 60, 0, true), kSuccess);
         do {
            std::this_thread::sleep_for(std::chrono::milliseconds(CHANGE_STATE_WAIT));
            ldapManager->process(CPCAPI2::OpenLdap::LdapManager::kBlockingModeNonBlocking);
         } while (!testLdapHandler->searchCompleted);

         ASSERT_NE(testLdapHandler->errorEvent.errorType, LdapSearchError);
         ASSERT_FALSE(testLdapHandler->entries.empty());

         // Disconnect from ldap server.
         ASSERT_EQ(ldapManager->disconnect(ldapClientHandle), kSuccess);
         do {
            std::this_thread::sleep_for(std::chrono::milliseconds(CHANGE_STATE_WAIT));
            ldapManager->process(CPCAPI2::OpenLdap::LdapManager::kBlockingModeNonBlocking);
         } while (testLdapHandler->onStateChangedEvent.state == LdapState::LdapConnected);
         ASSERT_EQ(testLdapHandler->onStateChangedEvent.state, LdapState::LdapDisconnected);
      }
#endif // CPCAPI2_BRAND_OPEN_LDAP_MODULE
   }

   TEST_F(LdapModuleTest, DISABLED_LdapSearchSDK) {
#if (CPCAPI2_BRAND_OPEN_LDAP_MODULE == 1)
      std::unique_ptr<TestPhoneHandler> testPhoneHandler(new TestPhoneHandler());
      std::unique_ptr<TestLdapHandler> testLdapHandler(new TestLdapHandler());
      CPCAPI2::Phone* phone = Phone::create();
      LicenseInfo licenseInfo;
      licenseInfo.licenseKey = "";
      licenseInfo.licenseDocumentLocation = "";
      phone->initialize(licenseInfo, testPhoneHandler.get());
      LdapManager* ldapManager = LdapManager::getInterface(phone);
      if (ldapManager)
      {
         LdapClientHandle ldapClientHandle = ldapManager->createClient();
         ASSERT_GT(ldapClientHandle, 0);
         ASSERT_EQ(ldapManager->setHandler(ldapClientHandle, testLdapHandler.get()), kSuccess);
         LdapClientSettings ldapClientSettings;
         ldapClientSettings.username = mTestUsername;
         ldapClientSettings.password = mTestPassword;
         ldapClientSettings.serverUrl = mServerUrl;
         ldapClientSettings.encryption = LdapEncryption_Ldaps;
         ldapClientSettings.cert_strategy = LdapCertStrategy_Hard;

         ASSERT_EQ(ldapManager->applySettings(ldapClientHandle, ldapClientSettings), kSuccess);

         LdapDataMap dataMap;
         dataMap.displayName = "cn";
         dataMap.firstName = "firstName";
         dataMap.lastName = "sn";
         dataMap.softphone = "internalnumber";
         dataMap.jobTitle = "job";
         dataMap.department = "service";
         dataMap.city = "city";
         dataMap.workPhone = "telephonenumber";
         dataMap.homePhone = "homephone";
         dataMap.mobilePhone = "mobile";
         dataMap.email = "mail";
         dataMap.jabber = "jabber";

         ASSERT_EQ(ldapManager->setDataMap(ldapClientHandle, dataMap), kSuccess);
         // Connect to ldap server.
         ASSERT_EQ(ldapManager->connect(ldapClientHandle), kSuccess);
         do {
            std::this_thread::sleep_for(std::chrono::milliseconds(CHANGE_STATE_WAIT));
            ldapManager->process(CPCAPI2::OpenLdap::LdapManager::kBlockingModeNonBlocking);
         } while (testLdapHandler->onStateChangedEvent.state == LdapState::LdapConnecting);
         ASSERT_EQ(testLdapHandler->onStateChangedEvent.state, LdapState::LdapConnected);

         cpc::string searchPattern = "(&(memberOf=CN=CounterPath Users,OU=Groups,OU=Corp.CounterPath,DC=cp,DC=local)(|(cn=sdk*)(givenName=sdk*)(sn=sdk*)(ipPhone=sdk*)(title=sdk*)(department=sdk*)(l=sdk*)(workNumber=sdk*)(telephoneNumber=sdk*)(homePhone=sdk*)(mobile=sdk*)(mail=sdk*)(sAMAccountName=sdk*)))";
         cpc::string rootDn = "DC=cp,DC=local";
         ASSERT_EQ(ldapManager->search(ldapClientHandle, searchPattern, rootDn,
                                       LdapSearchScope_SubTree, 60, 0, true), kSuccess);
         do {
            std::this_thread::sleep_for(std::chrono::milliseconds(CHANGE_STATE_WAIT));
            ldapManager->process(CPCAPI2::OpenLdap::LdapManager::kBlockingModeNonBlocking);
         } while (!testLdapHandler->searchCompleted);
         ASSERT_NE(testLdapHandler->errorEvent.errorType, LdapSearchError);
         ASSERT_FALSE(testLdapHandler->entries.empty());

         // Disconnect from ldap server.
         ASSERT_EQ(ldapManager->disconnect(ldapClientHandle), kSuccess);
         do {
            std::this_thread::sleep_for(std::chrono::milliseconds(CHANGE_STATE_WAIT));
            ldapManager->process(CPCAPI2::OpenLdap::LdapManager::kBlockingModeNonBlocking);
         } while (testLdapHandler->onStateChangedEvent.state == LdapState::LdapConnected);
         ASSERT_EQ(testLdapHandler->onStateChangedEvent.state, LdapState::LdapDisconnected);
      }
#endif // CPCAPI2_BRAND_OPEN_LDAP_MODULE
   }
}
