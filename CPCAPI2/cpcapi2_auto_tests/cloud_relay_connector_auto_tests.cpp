#if _WIN32
#include "stdafx.h"
#include <Windows.h>
#else
#include "brand_branded.h"
#define HWND void*
#endif

#if (CPCAPI2_BRAND_CLOUD_RELAY_CONNECTOR_MODULE == 1)

#include "cpcapi2_test_fixture.h"
#include "test_call_events.h"
#include "test_events.h"
#include "test_account_events.h"
#include "test_framework/xmpp_test_helper.h"

#include <cloudrelayconnector/CloudRelayConnector.h>
#include <cloudrelayconnector/CloudRelayConnectorHandler.h>
#include <cloudrelayconnector/CloudRelayConnectorTypes.h>
#include <orchestration_server/OrchestrationServer.h>
#include <cloudserviceconfig/CloudServiceConfig.h>
#include <cloudrelay/CloudRelayJsonApi.h>
#include <cloudrelay/CloudRelayManager.h>

#include "../../impl/util/CurlPPHelper.h"
#include "../../impl/util/CurlPPProgress.h"
#include "../../impl/util/CurlPPSSL.h"
#include "../../impl/auth_server/AuthServerJwtUtils.h"
#include "../../impl/auth_server/AuthServerDbAccess.h"

#include <boost/asio.hpp>

using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::CloudRelayConnector;
using namespace CPCAPI2::CloudServiceConfig;
using namespace CPCAPI2::OrchestrationServer;
using namespace CPCAPI2::JsonApi;
using namespace CPCAPI2::SipConversation;
using namespace CPCAPI2::SipAccount;

class CloudRelayConnectorTests : public CpcapiAutoTest
{
public:
   CloudRelayConnectorTests() {}
   virtual ~CloudRelayConnectorTests() {}
};

void getCloudRelayServerUrls(std::string& authUrl, std::string& orchUrl, std::string& agentUrl)
{
   std::stringstream authBuf;
   authBuf << "https://127.0.0.1:" << 18084;
   authUrl = authBuf.str().c_str();

   std::stringstream orchBuf;
   orchBuf << "https://127.0.0.1:" << 18082 << "/jsonApi";
   // orchBuf << "http://inproc.local:" << XMPP_ORCH_SERVER_HTTP_PORT << "/jsonApi";
   orchUrl = orchBuf.str().c_str();

   std::stringstream agentBuf;
   agentBuf << "wss://127.0.0.1:" << 9003;
   agentUrl = agentBuf.str().c_str();
}

void CloudRelay_setupAuthServer(TestAccount& max)
{
   CPCAPI2::AuthServer::DbAccess authDb;
   authDb.initialize("authserver.db");
   authDb.flushUsers();
   authDb.addUser("user1", "1234");
   authDb.addUser("user2", "1234");
   authDb.addUser("server", "server");

   CPCAPI2::AuthServer::AuthServerConfig authServerConfig;
   authServerConfig.certificateFilePath = TestEnvironmentConfig::testResourcePath() + "p256-private-key-unit-tests.p8";
   authServerConfig.httpsCertificateFilePath = TestEnvironmentConfig::testResourcePath() + "cert.pem";
   authServerConfig.httpsPrivateKeyFilePath = TestEnvironmentConfig::testResourcePath() + "privkey.pem";
   authServerConfig.httpsDiffieHellmanParamsFilePath = TestEnvironmentConfig::testResourcePath() + "dh2048.pem";
   authServerConfig.numThreads = 4;
   authServerConfig.port = 18084;
   max.authServer->start(authServerConfig);
}

void CloudRelay_setupConfBridgeServer(TestAccount& maia)
{
   cpc::string certificateFilePath = TestEnvironmentConfig::testResourcePath() + "p256-public-key-unit-tests.spki"; // pub key used to validate auth tokens
   JsonApi::JsonApiServerConfig jsonApiServCfg(9003, 18082, certificateFilePath);
   jsonApiServCfg.wssCertificateFilePath = TestEnvironmentConfig::testResourcePath() + "cert.pem";
   jsonApiServCfg.wssPrivateKeyFilePath = TestEnvironmentConfig::testResourcePath() + "privkey.pem";
   jsonApiServCfg.wssDiffieHellmanParamsFilePath = TestEnvironmentConfig::testResourcePath() + "dh2048.pem";
   jsonApiServCfg.httpsCertificateFilePath = TestEnvironmentConfig::testResourcePath() + "cert.pem";
   jsonApiServCfg.httpsPrivateKeyFilePath = TestEnvironmentConfig::testResourcePath() + "privkey.pem";
   jsonApiServCfg.httpsDiffieHellmanParamsFilePath = TestEnvironmentConfig::testResourcePath() + "dh2048.pem";

   cpc::string conferenceBridgeServiceId = "cloudrelay";

   if (jsonApiServCfg.certificateFilePath.size() == 0)
      jsonApiServCfg.certificateFilePath = TestEnvironmentConfig::testResourcePath() + "p256-public-key-unit-tests.spki"; // pub key used to validate auth tokens
   maia.jsonApiServer->start(jsonApiServCfg);
   CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(maia.phone)->setJsonApiServer(maia.jsonApiServer);
   CPCAPI2::CloudRelay::CloudRelayJsonApi::getInterface(maia.phone);

   CPCAPI2::OrchestrationServer::OrchestrationServer* agentOrchServer = CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(maia.phone);
   CPCAPI2::OrchestrationServer::OrchestrationServerConfig serverConfig;
   serverConfig.redisIp = "mock";
   serverConfig.redisPort = 6379;
   agentOrchServer->start(serverConfig);

   CPCAPI2::Media::MediaStackSettings mediaSettings;
   mediaSettings.audioLayer = CPCAPI2::Media::AudioLayers_File;
   mediaSettings.audioOutputDisabled = true;
   mediaSettings.numAudioEncoderThreads = 4;
   CPCAPI2::Media::MediaManager* media = CPCAPI2::Media::MediaManager::getInterface(maia.phone);
   ASSERT_TRUE(media != NULL);
   CPCAPI2::Media::AudioExt* audioExt = CPCAPI2::Media::AudioExt::getInterface(media);
   ASSERT_TRUE(audioExt != NULL);
   audioExt->setAudioDeviceFile(TestEnvironmentConfig::testResourcePath() + "silence16.pcm", "");
   media->initializeMediaStack(mediaSettings);

   CloudServiceConfigManager* cloudConfigMgr = CPCAPI2::CloudServiceConfig::CloudServiceConfigManager::getInterface(maia.phone);
   test::EventHandler cloudConfigEvents(conferenceBridgeServiceId, dynamic_cast<CPCAPI2::AutoTestProcessor*>(cloudConfigMgr));

   std::string authUrl("");
   std::string orchUrl("");
   std::string agentUrl("");
   getCloudRelayServerUrls(authUrl, orchUrl, agentUrl);
   orchUrl = "http://inproc.local";

   ConferenceBridge::ConferenceBridgeConfig bridgeSettings;
   bridgeSettings.httpJoinUrlBase = "https://127.0.0.1:18082";
   bridgeSettings.wsUrlBase = "https://127.0.0.1:9003";
   bridgeSettings.userContext = maia.config.name;
   bridgeSettings.serverUid = "BC";
   bridgeSettings.mediaEncryptionMode = CPCAPI2::ConferenceBridge::ConferenceMediaEncryptionMode_SRTP_DTLS;
   CPCAPI2::ConferenceBridge::ConferenceBridgeManager::getInterface(maia.phone)->start(bridgeSettings);

   ServerInfo xmppAgentServerInfo;
   xmppAgentServerInfo.region = "NA";
   xmppAgentServerInfo.uri = agentUrl.c_str();
   xmppAgentServerInfo.services.push_back(conferenceBridgeServiceId);
   ServiceConfigSettings serviceConfigSettings;
   serviceConfigSettings.authServerUrl = std::string(authUrl + "/login_v1").c_str();
   serviceConfigSettings.orchestrationServerUrl = orchUrl.c_str();
   serviceConfigSettings.username = "server";
   serviceConfigSettings.password = "server";

   cloudConfigMgr->setServerInfo(serviceConfigSettings, xmppAgentServerInfo);
   {
      CloudServiceConfigHandle h;
      SetServerInfoResult args;
      ASSERT_TRUE(cpcExpectEvent((&cloudConfigEvents), "CloudServiceConfigHandler::onSetServerInfoSuccess", 5000, CPCAPI2::test::AlwaysTruePred(), h, args));
   }
}


TEST_F(CloudRelayConnectorTests, RelayTest)
{
   LoginResultEvent loginResult; loginResult.success = true;
   cpc::vector<cpc::string> permissions; permissions.push_back("*");

   // Max is the auth server
   TestAccount max("max", Account_Init);
   CloudRelay_setupAuthServer(max);

   // Maia is the orchestration and confbridge server
   TestAccount maia("maia", Account_Init);
   CloudRelay_setupConfBridgeServer(maia);

   // Alice is a client SDK (screenshare presenter)
   TestAccount alice("alice", Account_Init);

   // Bob is a client SDK (screenshare participant)
   TestAccount bob("bob", Account_Init);

   std::atomic_bool testDone(false);
   auto mediaEvent = std::async(std::launch::async, [&]() {
      while (!testDone) {
         alice.media->process(CPCAPI2::kBlockingModeNonBlocking);
         bob.media->process(CPCAPI2::kBlockingModeNonBlocking);
         std::this_thread::sleep_for(std::chrono::milliseconds(20));
      }
   });

   CloudRelayConnectorHandle aliceConfConn = alice.cloudRelayConnector->createCloudRelayConnector();
   CloudRelayConnectorSettings aliceCloudSettings;
   aliceCloudSettings.authServerUrl = "https://127.0.0.1:18084";
   aliceCloudSettings.orchestrationServerUrl = "https://127.0.0.1:18082/jsonApi";
   aliceCloudSettings.regionCode = "NA";
   aliceCloudSettings.username = "user1";
   aliceCloudSettings.password = "1234";
   aliceCloudSettings.ignoreCertVerification = true;
   aliceCloudSettings.authServerApiKey = "-----BEGIN PUBLIC KEY-----\n"
      "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEZzshRNe4gMUuqNBwrE6leU/N5vW8\n"
      "+HW/pZw5XfsxvzEtUlGwp+A9sNedJNUtjYD/2gFFY/nFuM4Gmnu6zeteTQ==\n"
      "-----END PUBLIC KEY-----";

   alice.cloudRelayConnector->setConnectionSettings(aliceConfConn, aliceCloudSettings);
   alice.cloudRelayConnector->connectToCloudRelay(aliceConfConn);

   auto maiaEvent = std::async(std::launch::async, [&]() {
      JsonApiUserHandle jsonApiUser = 0;
      NewLoginEvent args;

      // Maia has to process the login attempt (associate the context with an SDK instance)
      ASSERT_TRUE(cpcExpectEvent(maia.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 15000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args));
      ASSERT_NE(jsonApiUser, 0);
      maia.jsonApiServer->setJsonApiUserContext(jsonApiUser, maia.phone, permissions);
      maia.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);

      // Maia has to process the login attempt (associate the context with an SDK instance)
      ASSERT_TRUE(cpcExpectEvent(maia.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 15000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args));
      ASSERT_NE(jsonApiUser, 0);
      maia.jsonApiServer->setJsonApiUserContext(jsonApiUser, maia.phone, permissions);
      maia.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);

      // Maia has to process the login attempt (associate the context with an SDK instance)
      ASSERT_TRUE(cpcExpectEvent(maia.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 15000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args));
      ASSERT_NE(jsonApiUser, 0);
      maia.jsonApiServer->setJsonApiUserContext(jsonApiUser, maia.phone, permissions);
      maia.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);

      // Maia has to process the login attempt (associate the context with an SDK instance)
      ASSERT_TRUE(cpcExpectEvent(maia.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 15000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args));
      ASSERT_NE(jsonApiUser, 0);
      maia.jsonApiServer->setJsonApiUserContext(jsonApiUser, maia.phone, permissions);
      maia.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);



   });

   resip::Condition condBobConnected;
   resip::Mutex mtxBobConnected;

   auto aliceEvent = std::async(std::launch::async, [&]() {
      mtxBobConnected.lock();

      {
         CloudRelayConnectorHandle conn;
         CPCAPI2::CloudRelayConnector::ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.cloudRelayConnectorEvents, "CloudRelayConnectorHandler::onServiceConnectionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(CPCAPI2::CloudRelayConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
      }

      {
         CloudRelayConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.cloudRelayConnectorEvents, "CloudRelayConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(CPCAPI2::CloudRelayConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
      }

      {
         CloudRelayConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.cloudRelayConnectorEvents, "CloudRelayConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(CPCAPI2::CloudRelayConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
      }

      condBobConnected.wait(mtxBobConnected);
      alice.cloudRelayConnector->broadcast(aliceConfConn, "test123");

      {
         CloudRelayConnectorHandle conn;
         CPCAPI2::CloudRelayConnector::BroadcastEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.cloudRelayConnectorEvents, "CloudRelayConnectorHandler::onBroadcast", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.msg, cpc::string("test123"));
      }

      {
         CloudRelayConnectorHandle conn;
         CPCAPI2::CloudRelayConnector::MessageEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.cloudRelayConnectorEvents, "CloudRelayConnectorHandler::onMessage", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.msg, cpc::string("test456"));
      }

      alice.cloudRelayConnector->destroyCloudRelayConnector(aliceConfConn);

   });

   auto bobEvent = std::async(std::launch::async, [&]() {

      CloudRelayConnectorHandle bobConfConn = bob.cloudRelayConnector->createCloudRelayConnector();
      CloudRelayConnectorSettings bobCloudSettings;
      bobCloudSettings.authServerUrl = "https://127.0.0.1:18084";
      bobCloudSettings.orchestrationServerUrl = "https://127.0.0.1:18082/jsonApi";
      bobCloudSettings.regionCode = "NA";
      bobCloudSettings.username = "user2";
      bobCloudSettings.password = "1234";
      bobCloudSettings.authServerApiKey = "-----BEGIN PUBLIC KEY-----\n"
         "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEZzshRNe4gMUuqNBwrE6leU/N5vW8\n"
         "+HW/pZw5XfsxvzEtUlGwp+A9sNedJNUtjYD/2gFFY/nFuM4Gmnu6zeteTQ==\n"
         "-----END PUBLIC KEY-----";

      bobCloudSettings.ignoreCertVerification = true;
      bob.cloudRelayConnector->setConnectionSettings(bobConfConn, bobCloudSettings);
      bob.cloudRelayConnector->connectToCloudRelay(bobConfConn);

      {
         CloudRelayConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.cloudRelayConnectorEvents, "CloudRelayConnectorHandler::onServiceConnectionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(CPCAPI2::CloudRelayConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
      }

      {
         CloudRelayConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.cloudRelayConnectorEvents, "CloudRelayConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(CPCAPI2::CloudRelayConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
      }

      {
         CloudRelayConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.cloudRelayConnectorEvents, "CloudRelayConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(CPCAPI2::CloudRelayConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
      }

      mtxBobConnected.lock();
      condBobConnected.signal();
      mtxBobConnected.unlock();

      {
         CloudRelayConnectorHandle conn;
         CPCAPI2::CloudRelayConnector::BroadcastEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.cloudRelayConnectorEvents, "CloudRelayConnectorHandler::onBroadcast", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.msg, cpc::string("test123"));

         bob.cloudRelayConnector->sendTo(bobConfConn, args.senderId, "test456");
      }

   });

   aliceEvent.wait_for(std::chrono::milliseconds(45000));
   aliceEvent.get();

   bobEvent.wait_for(std::chrono::milliseconds(45000));
   bobEvent.get();
   //bobFu.get();

   maiaEvent.wait_for(std::chrono::milliseconds(45000));
   maiaEvent.get();
   //waitFor2(aliceEvent, maiaEvent);

   testDone = true;

   CPCAPI2::ConferenceBridge::ConferenceBridgeManager::getInterface(maia.phone)->shutdown();
   CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(maia.phone)->flushAll();
   CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(maia.phone)->shutdown();
   maia.jsonApiServer->shutdown();

   max.authServer->shutdown();

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
}

#if 0
TEST_F(CloudRelayConnectorTests, AVConference_cloud)
{
   //const cpc::string SCREENSHARE_SERVER = "ss.softphone.com";
   const cpc::string SCREENSHARE_SERVER = "cloudsdk4.bria-x.net";
   //const cpc::string SCREENSHARE_SERVER = "screenshare1.bria-x.net"; 
   //const cpc::string SCREENSHARE_SERVER = "cpclientapi.softphone.com:18090";
   //const cpc::string SCREENSHARE_SERVER = "***********:18090";

   LoginResultEvent loginResult; loginResult.success = true;
   cpc::vector<cpc::string> permissions; permissions.push_back("*");

   // Alice is a client SDK (screenshare presenter)
   TestAccount alice("alice");

   // Bob is a client SDK (screenshare participant)
   TestAccount bob("bob");

   // enable H.264
   TestVideoHandler videoHandler;
   alice.video->setHandler(&videoHandler);
   alice.video->queryCodecList();
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);
   bob.video->setHandler(&videoHandler);
   bob.video->queryCodecList();
   bob.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);

   alice.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   bob.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   alice.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   bob.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   HWND hwndAliceRemote = NULL;
   HWND hwndBobRemote = NULL;
   HWND hwndBobRemoteLarge = NULL;

   std::atomic_bool testDone(false);
   auto mediaEvent = std::async(std::launch::async, [&]() {
   #if _WIN32
      ASSERT_EQ(0, ViECreateWindow(hwndAliceRemote, 0, 0, 352, 288, "Alice (incoming)"));
      //alice.video->setIncomingVideoRenderTarget(hwndAliceRemote);
      alice.video->startCapture();

      ASSERT_EQ(0, ViECreateWindow(hwndBobRemote, 356, 0, 352, 288, "Bob (incoming)"));
      //bob.video->setIncomingVideoRenderTarget(hwndBobRemote);
      bob.video->startCapture();

      ASSERT_EQ(0, ViECreateWindow(hwndBobRemoteLarge, 0, 292, 704, 576, "Bob (incoming large)"));

   #else // _WIN32
      CPCAPI2::Media::VideoExt::getInterface(alice.media)->startScreenshare(NULL);
   #endif
      while (!testDone) {
         alice.media->process(CPCAPI2::kBlockingModeNonBlocking);
         bob.media->process(CPCAPI2::kBlockingModeNonBlocking);

         MSG msg;
         if (PeekMessage(&msg, NULL, 0, 0, PM_REMOVE))
         {
            TranslateMessage(&msg);
            DispatchMessage(&msg);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(20));
      }

#if _WIN32
      DestroyWindow(hwndAliceRemote);
      DestroyWindow(hwndBobRemote);
      DestroyWindow(hwndBobRemoteLarge);
#endif // _WIN32

   });

   CloudRelayConnectorHandle aliceConfConn = alice.cloudRelayConnector->createCloudRelayConnector();
   CloudRelayConnectorSettings aliceCloudSettings;
   aliceCloudSettings.authServerUrl = "https://auth.softphone.com";
   aliceCloudSettings.authServerApiKey = "-----BEGIN PUBLIC KEY-----\n"
      "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEZzshRNe4gMUuqNBwrE6leU/N5vW8\n"
      "+HW/pZw5XfsxvzEtUlGwp+A9sNedJNUtjYD/2gFFY/nFuM4Gmnu6zeteTQ==\n"
      "-----END PUBLIC KEY-----";
   aliceCloudSettings.orchestrationServerUrl = "https://" + SCREENSHARE_SERVER + "/jsonApi";
   aliceCloudSettings.regionCode = "LOCAL";
   {
      resip::Data usernameStr;
      {
         resip::DataStream ds(usernameStr);
         ds << "alice-unittests-" << resip::Random::getCryptoRandomBase64(2);
      }
      aliceCloudSettings.username = "<EMAIL>"; // usernameStr.c_str();
   }
   aliceCloudSettings.password = "1234";
   alice.cloudRelayConnector->setConnectionSettings(aliceConfConn, aliceCloudSettings);
   alice.cloudRelayConnector->connectToCloudRelay(aliceConfConn);

   std::promise<std::string> joinUrlPr;
   std::future<std::string> joinUrlFu = joinUrlPr.get_future();

   auto aliceEvent = std::async(std::launch::async, [&]() {
      {
         CloudRelayConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.cloudRelayConnectorEvents, "CloudRelayConnectorHandler::onServiceConnectionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(CloudRelayConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
      }

      {
         CloudRelayConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.cloudRelayConnectorEvents, "CloudRelayConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(CloudRelayConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
      }

      {
         CloudRelayConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.cloudRelayConnectorEvents, "CloudRelayConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(CloudRelayConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
      }

      alice.cloudRelayConnector->queryConferenceList(aliceConfConn);

      {
         CloudRelayConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.cloudRelayConnectorEvents, "CloudRelayConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 0);
      }

      CloudConferenceSettings confSettings;
      confSettings.conferenceDescription = "mcu test";
      confSettings.conferenceType = CloudConferenceType_AudioVideo_MCU;
      confSettings.conferenceId = "mcu";
      alice.cloudRelayConnector->createNewConference(aliceConfConn, confSettings);
      CloudConferenceHandle aliceScreenShare = 0;

      {
         CloudRelayConnectorHandle conn;
         ConferenceCreatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.cloudRelayConnectorEvents, "CloudRelayConnectorHandler::onConferenceCreated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_NE(args.conference, 0);
         ASSERT_NE(args.conference, -1);
         aliceScreenShare = args.conference;
      }

      alice.cloudRelayConnector->queryConferenceList(aliceConfConn);

      {
         CloudRelayConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.cloudRelayConnectorEvents, "CloudRelayConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 1);
         ASSERT_FALSE(args.conferenceList[0].joinUrl.empty());
         joinUrlPr.set_value(args.conferenceList[0].joinUrl.c_str());
         ASSERT_GT(args.conferenceList[0].conference, 0); // handle used to create a new session
      }

      CloudConferenceSessionHandle aliceSession = alice.cloudRelayConnector->createConferenceSession(aliceScreenShare);

      CloudConferenceSessionSettings aliceSessionSettings;
      aliceSessionSettings.role = CloudConferenceRole_Host;
      alice.cloudRelayConnector->setSessionSettings(aliceSession, aliceSessionSettings);

      CloudConferenceSessionMediaSettings aliceSessionMedia;
      aliceSessionMedia.audioDirection = CloudRelayConnector::MediaDirection_SendRecv;
      aliceSessionMedia.videoDirection = CloudRelayConnector::MediaDirection_SendRecv;
      aliceSessionMedia.remoteVideoRenderSurface = hwndAliceRemote;
      aliceSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
      alice.cloudRelayConnector->setSessionMediaSettings(aliceSession, aliceSessionMedia);

      alice.cloudRelayConnector->startSession(aliceSession);

      try
      {
         {
            // virtual int onConferenceSessionStatusChanged(CloudRelayConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            CloudRelayConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.cloudRelayConnectorEvents, "CloudRelayConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::CloudRelayConnector::SessionStatus_Connecting);
         }

         {
            // virtual int onConferenceSessionStatusChanged(CloudRelayConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            CloudRelayConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.cloudRelayConnectorEvents, "CloudRelayConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::CloudRelayConnector::SessionStatus_Connected);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(20000));

         safeCout("ALICE ENDS SESSION");
         alice.cloudRelayConnector->endSession(aliceSession);

         {
            // virtual int onConferenceSessionStatusChanged(CloudRelayConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            CloudRelayConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.cloudRelayConnectorEvents, "CloudRelayConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::CloudRelayConnector::SessionStatus_NotConnected);
         }
         {
            // virtual int onConferenceSessionStatusChanged(CloudRelayConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            CloudRelayConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(alice.cloudRelayConnectorEvents, "CloudRelayConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::CloudRelayConnector::SessionStatus_NotConnected);
         }

         {
            CloudRelayConnectorHandle conn;
            ConferenceEndedEvent args;
            ASSERT_TRUE(cpcExpectEvent(alice.cloudRelayConnectorEvents, "CloudRelayConnectorHandler::onConferenceEnded", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(args.conference, aliceScreenShare);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         alice.cloudRelayConnector->destroyCloudRelayConnector(aliceConfConn);



      }
      catch (...)
      {
      }

   });

   std::shared_ptr<std::promise<int> > bobPr(new std::promise<int>());
   std::future<int> bobFu = bobPr->get_future();
   auto bobEvent = std::async(std::launch::async, [&, bobPr]() {
      ASSERT_EQ(joinUrlFu.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
      std::string joinUrlFromAlice = joinUrlFu.get();

      CloudRelayConnectorHandle bobConfConn = bob.cloudRelayConnector->createCloudRelayConnector();
      CloudRelayConnectorSettings bobCloudSettings;
      bobCloudSettings.authServerUrl = "https://auth.softphone.com";
      bobCloudSettings.authServerApiKey = "-----BEGIN PUBLIC KEY-----\n"
         "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEZzshRNe4gMUuqNBwrE6leU/N5vW8\n"
         "+HW/pZw5XfsxvzEtUlGwp+A9sNedJNUtjYD/2gFFY/nFuM4Gmnu6zeteTQ==\n"
         "-----END PUBLIC KEY-----";
      bobCloudSettings.orchestrationServerUrl = ""; // not needed, we use the joinUrl instead
      bobCloudSettings.joinUrl = joinUrlFromAlice.c_str();
      safeCout("BOB is joining URL: " + joinUrlFromAlice);
      bobCloudSettings.regionCode = "LOCAL";
      {
         resip::Data usernameStr;
         {
            resip::DataStream ds(usernameStr);
            ds << "bob-unittests-" << resip::Random::getCryptoRandomBase64(2);
         }
         bobCloudSettings.username = usernameStr.c_str();
      }
      bobCloudSettings.password = "1234";

      bob.cloudRelayConnector->setConnectionSettings(bobConfConn, bobCloudSettings);
      bob.cloudRelayConnector->connectToCloudRelay(bobConfConn);

      {
         CloudRelayConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.cloudRelayConnectorEvents, "CloudRelayConnectorHandler::onServiceConnectionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(CloudRelayConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
      }

      {
         CloudRelayConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.cloudRelayConnectorEvents, "CloudRelayConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(CloudRelayConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
      }

      {
         CloudRelayConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.cloudRelayConnectorEvents, "CloudRelayConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(CloudRelayConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
      }

      bob.cloudRelayConnector->queryConferenceList(bobConfConn);
      CloudConferenceHandle bobScreenShare;

      {
         CloudRelayConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.cloudRelayConnectorEvents, "CloudRelayConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 1);
         bobScreenShare = args.conferenceList[0].conference;
      }

      CloudConferenceSessionHandle bobSession = bob.cloudRelayConnector->createConferenceSession(bobScreenShare);

      CloudConferenceSessionSettings bobSessionSettings;
      bobSessionSettings.role = CloudConferenceRole_Participant;
      bob.cloudRelayConnector->setSessionSettings(bobSession, bobSessionSettings);

      CloudConferenceSessionMediaSettings bobSessionMedia;
      bobSessionMedia.audioDirection = CloudRelayConnector::MediaDirection_SendRecv;
      bobSessionMedia.videoDirection = CloudRelayConnector::MediaDirection_SendRecv;
      bobSessionMedia.remoteVideoRenderSurface = hwndBobRemote;
      bobSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
      bob.cloudRelayConnector->setSessionMediaSettings(bobSession, bobSessionMedia);

      bob.cloudRelayConnector->startSession(bobSession);

      try
      {
         {
            // virtual int onConferenceSessionStatusChanged(CloudRelayConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            CloudRelayConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.cloudRelayConnectorEvents, "CloudRelayConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::CloudRelayConnector::SessionStatus_Connecting);
         }

         {
            // virtual int onConferenceSessionStatusChanged(CloudRelayConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            CloudRelayConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.cloudRelayConnectorEvents, "CloudRelayConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::CloudRelayConnector::SessionStatus_Connected);
            EXPECT_EQ(args.screenshare.mediaDirection, CPCAPI2::CloudRelayConnector::MediaDirection_None);
            EXPECT_EQ(args.audio.mediaDirection, CPCAPI2::CloudRelayConnector::MediaDirection_SendRecv);
            EXPECT_EQ(args.video.mediaDirection, CPCAPI2::CloudRelayConnector::MediaDirection_SendRecv);
            //EXPECT_NE(args.screenshare.mediaStreamId, -1);
            //bob.video->setIncomingVideoRenderTarget(args.screenshare.mediaStreamId, hwndBobRemote);
         }
         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         bobSessionMedia.audioDirection = CloudRelayConnector::MediaDirection_SendRecv;
         bobSessionMedia.videoDirection = CloudRelayConnector::MediaDirection_SendRecv;
         bobSessionMedia.remoteVideoRenderSurface = hwndBobRemoteLarge;
         bobSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
         bob.cloudRelayConnector->setSessionMediaSettings(bobSession, bobSessionMedia);
         bob.cloudRelayConnector->updateSessionMedia(bobSession);

         {
            // virtual int onConferenceSessionStatusChanged(CloudRelayConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            CloudRelayConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.cloudRelayConnectorEvents, "CloudRelayConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::CloudRelayConnector::SessionStatus_Connected);
            EXPECT_EQ(args.screenshare.mediaDirection, CPCAPI2::CloudRelayConnector::MediaDirection_None);
            EXPECT_EQ(args.audio.mediaDirection, CPCAPI2::CloudRelayConnector::MediaDirection_SendRecv);
            EXPECT_EQ(args.video.mediaDirection, CPCAPI2::CloudRelayConnector::MediaDirection_SendRecv);
            //bob.video->setIncomingVideoRenderTarget(args.screenshare.mediaStreamId, hwndBobRemote);
         }

         //std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         {
            // virtual int onConferenceSessionStatusChanged(CloudRelayConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            CloudRelayConnectorHandle conn;
            ConferenceParticipantListUpdatedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.cloudRelayConnectorEvents, "CloudRelayConnectorHandler::onConferenceParticipantListUpdated", 180000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            //EXPECT_EQ(args.participantList.size(), 0);
         }

         {
            // virtual int onConferenceSessionStatusChanged(CloudRelayConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            CloudRelayConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.cloudRelayConnectorEvents, "CloudRelayConnectorHandler::onConferenceSessionStatusChanged", 700000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(CPCAPI2::CloudRelayConnector::SessionStatus_NotConnected, args.sessionStatus);
         }

         {
            CloudRelayConnectorHandle conn;
            ConferenceEndedEvent args;
            ASSERT_TRUE(cpcExpectEvent(bob.cloudRelayConnectorEvents, "CloudRelayConnectorHandler::onConferenceEnded", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(args.conference, bobScreenShare);
         }

         /*
         {
            // virtual int onConferenceSessionStatusChanged(CloudRelayConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            CloudRelayConnectorHandle conn;
            ConferenceParticipantListUpdatedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.cloudRelayConnectorEvents, "CloudRelayConnectorHandler::onConferenceParticipantListUpdated", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            //EXPECT_EQ(args.participantList.size(), 0);
         }

         {
            // virtual int onConferenceSessionStatusChanged(CloudRelayConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            CloudRelayConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.cloudRelayConnectorEvents, "CloudRelayConnectorHandler::onConferenceSessionStatusChanged", 120000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::CloudRelayConnector::SessionStatus_NotConnected);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         bob.cloudRelayConnector->endSession(bobSession);
         */
      }
      catch (...)
      {
      }
      //{
      //   // virtual int onConferenceSessionStatusChanged(CloudRelayConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
      //   CloudRelayConnectorHandle conn;
      //   ConferenceSessionStatusChangedEvent args;
      //   ASSERT_TRUE(cpcExpectEvent(bob.cloudRelayConnectorEvents, "CloudRelayConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
      //   ASSERT_EQ(args.sessionStatus, CPCAPI2::CloudRelayConnector::SessionStatus_NotConnected);
      //}
      bobPr->set_value(0);
   });

   aliceEvent.wait_for(std::chrono::milliseconds(45000));
   aliceEvent.get();

   bobEvent.wait_for(std::chrono::milliseconds(45000));
   bobEvent.get();
   //bobFu.get();

   //waitFor2(aliceEvent, maiaEvent);

   testDone = true;


   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
}

TEST_F(CloudRelayConnectorTests, AVConference_cloud_participant_only)
{
   //const cpc::string SCREENSHARE_SERVER = "ss.softphone.com";
   const cpc::string SCREENSHARE_SERVER = "cloudsdk4.bria-x.net";
   //const cpc::string SCREENSHARE_SERVER = "screenshare1.bria-x.net"; 
   //const cpc::string SCREENSHARE_SERVER = "cpclientapi.softphone.com:18090";
   //const cpc::string SCREENSHARE_SERVER = "***********:18090";

   LoginResultEvent loginResult; loginResult.success = true;
   cpc::vector<cpc::string> permissions; permissions.push_back("*");

   // Bob is a client SDK (screenshare participant)
   TestAccount bob("bob");

   // enable H.264
   bob.enableOnlyThisVideoCodec("H.264");

   bob.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   bob.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   HWND hwndBobRemoteLarge = NULL;
#if _WIN32

   ASSERT_EQ(0, ViECreateWindow(hwndBobRemoteLarge, 0, 292, 704, 576, "Bob (incoming large)"));
   //bob.video->setIncomingVideoRenderTarget(hwndBobRemote);
   bob.video->startCapture();


#else // _WIN32
   CPCAPI2::Media::VideoExt::getInterface(alice.media)->startScreenshare(NULL);
#endif

   std::atomic_bool testDone(false);
   auto mediaEvent = std::async(std::launch::async, [&]() {
      while (!testDone) {
         bob.media->process(CPCAPI2::kBlockingModeNonBlocking);
         std::this_thread::sleep_for(std::chrono::milliseconds(20));
      }
   });


   auto bobEvent = std::async(std::launch::async, [&]() {
      
      std::string joinUrlFromAlice = "https://cloudsdk4.bria-x.net/screenshare/D0/DZIZLWJY";

      CloudRelayConnectorHandle bobConfConn = bob.cloudRelayConnector->createCloudRelayConnector();
      CloudRelayConnectorSettings bobCloudSettings;
      bobCloudSettings.authServerUrl = "https://auth.softphone.com";
      bobCloudSettings.authServerApiKey = "-----BEGIN PUBLIC KEY-----\n"
         "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEZzshRNe4gMUuqNBwrE6leU/N5vW8\n"
         "+HW/pZw5XfsxvzEtUlGwp+A9sNedJNUtjYD/2gFFY/nFuM4Gmnu6zeteTQ==\n"
         "-----END PUBLIC KEY-----";
      bobCloudSettings.orchestrationServerUrl = ""; // not needed, we use the joinUrl instead
      bobCloudSettings.joinUrl = joinUrlFromAlice.c_str();
      safeCout("BOB is joining URL: " + joinUrlFromAlice);
      bobCloudSettings.regionCode = "LOCAL";
      {
         resip::Data usernameStr;
         {
            resip::DataStream ds(usernameStr);
            ds << "bob-unittests-" << resip::Random::getCryptoRandomBase64(2);
         }
         bobCloudSettings.username = usernameStr.c_str();
      }
      bobCloudSettings.password = "1234";

      bob.cloudRelayConnector->setConnectionSettings(bobConfConn, bobCloudSettings);
      bob.cloudRelayConnector->connectToCloudRelay(bobConfConn);

      {
         CloudRelayConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.cloudRelayConnectorEvents, "CloudRelayConnectorHandler::onServiceConnectionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(CloudRelayConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
      }

      {
         CloudRelayConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.cloudRelayConnectorEvents, "CloudRelayConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(CloudRelayConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
      }

      {
         CloudRelayConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.cloudRelayConnectorEvents, "CloudRelayConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(CloudRelayConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
      }

      bob.cloudRelayConnector->queryConferenceList(bobConfConn);
      CloudConferenceHandle bobScreenShare;

      {
         CloudRelayConnectorHandle conn;
         ConferenceListUpdatedEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.cloudRelayConnectorEvents, "CloudRelayConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.conferenceList.size(), 1);
         bobScreenShare = args.conferenceList[0].conference;
      }

      CloudConferenceSessionHandle bobSession = bob.cloudRelayConnector->createConferenceSession(bobScreenShare);

      CloudConferenceSessionSettings bobSessionSettings;
      bobSessionSettings.role = CloudConferenceRole_Participant;
      bob.cloudRelayConnector->setSessionSettings(bobSession, bobSessionSettings);

      CloudConferenceSessionMediaSettings bobSessionMedia;
      bobSessionMedia.audioDirection = CloudRelayConnector::MediaDirection_SendRecv;
      bobSessionMedia.videoDirection = CloudRelayConnector::MediaDirection_SendRecv;
      bobSessionMedia.remoteVideoRenderSurface = hwndBobRemoteLarge;
      bobSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
      bob.cloudRelayConnector->setSessionMediaSettings(bobSession, bobSessionMedia);

      bob.cloudRelayConnector->startSession(bobSession);

      try
      {
         {
            // virtual int onConferenceSessionStatusChanged(CloudRelayConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            CloudRelayConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.cloudRelayConnectorEvents, "CloudRelayConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::CloudRelayConnector::SessionStatus_Connecting);
         }

         {
            // virtual int onConferenceSessionStatusChanged(CloudRelayConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            CloudRelayConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.cloudRelayConnectorEvents, "CloudRelayConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::CloudRelayConnector::SessionStatus_Connected);
            EXPECT_EQ(args.screenshare.mediaDirection, CPCAPI2::CloudRelayConnector::MediaDirection_None);
            EXPECT_EQ(args.audio.mediaDirection, CPCAPI2::CloudRelayConnector::MediaDirection_SendRecv);
            EXPECT_EQ(args.video.mediaDirection, CPCAPI2::CloudRelayConnector::MediaDirection_SendRecv);
            //EXPECT_NE(args.screenshare.mediaStreamId, -1);
            //bob.video->setIncomingVideoRenderTarget(args.screenshare.mediaStreamId, hwndBobRemote);
         }
         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         //bobSessionMedia.audioDirection = CloudRelayConnector::MediaDirection_SendRecv;
         //bobSessionMedia.videoDirection = CloudRelayConnector::MediaDirection_SendRecv;
         //bobSessionMedia.remoteVideoRenderSurface = hwndBobRemoteLarge;
         //bobSessionMedia.remoteVideoRenderSurfaceType = Media::VideoSurfaceType_WindowsHWND;
         //bob.cloudRelayConnector->setSessionMediaSettings(bobSession, bobSessionMedia);
         //bob.cloudRelayConnector->updateSessionMedia(bobSession);

         //{
         //   // virtual int onConferenceSessionStatusChanged(CloudRelayConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
         //   CloudRelayConnectorHandle conn;
         //   ConferenceSessionStatusChangedEvent args;
         //   EXPECT_TRUE(cpcExpectEvent(bob.cloudRelayConnectorEvents, "CloudRelayConnectorHandler::onConferenceSessionStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         //   EXPECT_EQ(args.sessionStatus, CPCAPI2::CloudRelayConnector::SessionStatus_Connected);
         //   EXPECT_EQ(args.screenshare.mediaDirection, CPCAPI2::CloudRelayConnector::MediaDirection_None);
         //   EXPECT_EQ(args.audio.mediaDirection, CPCAPI2::CloudRelayConnector::MediaDirection_SendRecv);
         //   EXPECT_EQ(args.video.mediaDirection, CPCAPI2::CloudRelayConnector::MediaDirection_SendRecv);
         //   //bob.video->setIncomingVideoRenderTarget(args.screenshare.mediaStreamId, hwndBobRemote);
         //}

         //std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         {
            // virtual int onConferenceSessionStatusChanged(CloudRelayConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            CloudRelayConnectorHandle conn;
            ConferenceParticipantListUpdatedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.cloudRelayConnectorEvents, "CloudRelayConnectorHandler::onConferenceParticipantListUpdated", 180000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            //EXPECT_EQ(args.participantList.size(), 0);
         }

         {
            // virtual int onConferenceSessionStatusChanged(CloudRelayConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            CloudRelayConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.cloudRelayConnectorEvents, "CloudRelayConnectorHandler::onConferenceSessionStatusChanged", 700000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(CPCAPI2::CloudRelayConnector::SessionStatus_NotConnected, args.sessionStatus);
         }

         {
            CloudRelayConnectorHandle conn;
            ConferenceEndedEvent args;
            ASSERT_TRUE(cpcExpectEvent(bob.cloudRelayConnectorEvents, "CloudRelayConnectorHandler::onConferenceEnded", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            ASSERT_EQ(args.conference, bobScreenShare);
         }

         /*
         {
            // virtual int onConferenceSessionStatusChanged(CloudRelayConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            CloudRelayConnectorHandle conn;
            ConferenceParticipantListUpdatedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.cloudRelayConnectorEvents, "CloudRelayConnectorHandler::onConferenceParticipantListUpdated", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            //EXPECT_EQ(args.participantList.size(), 0);
         }

         {
            // virtual int onConferenceSessionStatusChanged(CloudRelayConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
            CloudRelayConnectorHandle conn;
            ConferenceSessionStatusChangedEvent args;
            EXPECT_TRUE(cpcExpectEvent(bob.cloudRelayConnectorEvents, "CloudRelayConnectorHandler::onConferenceSessionStatusChanged", 120000, CPCAPI2::test::AlwaysTruePred(), conn, args));
            EXPECT_EQ(args.sessionStatus, CPCAPI2::CloudRelayConnector::SessionStatus_NotConnected);
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         bob.cloudRelayConnector->endSession(bobSession);
         */
      }
      catch (...)
      {
      }
      //{
      //   // virtual int onConferenceSessionStatusChanged(CloudRelayConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
      //   CloudRelayConnectorHandle conn;
      //   ConferenceSessionStatusChangedEvent args;
      //   ASSERT_TRUE(cpcExpectEvent(bob.cloudRelayConnectorEvents, "CloudRelayConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
      //   ASSERT_EQ(args.sessionStatus, CPCAPI2::CloudRelayConnector::SessionStatus_NotConnected);
      //}
   });

   bobEvent.wait_for(std::chrono::milliseconds(45000));
   bobEvent.get();
   //bobFu.get();

   //waitFor2(aliceEvent, maiaEvent);

   testDone = true;

#if _WIN32
   //DestroyWindow(hwndBobRemote);
   DestroyWindow(hwndBobRemoteLarge);
#endif // _WIN32

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
}





#endif // 0



#endif
