#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#if (CPCAPI2_BRAND_REMOTE_SYNC_MODULE == 1)

#include "cpcapi2_test_fixture.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>
#include "util/FileDescriptorMonitor.h"

#include "test_framework/cpcapi2_test_framework.h"
#include "test_framework/bare_bones_sip_endpoint.h"
#include "vccs_test_harness/VccsTestHarness.h"
#include "test_framework/xmpp_test_helper.h"

#include "remotesync/RemoteSyncManagerInternal.h"
#include "remotesync/RemoteSyncInternalHandlerTypes.h"

#include <cpcstl/string.h>
#include <string>
#include <sstream>
#include <thread>
#include <future>
#include <memory>

using namespace CPCAPI2;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::RemoteSync;
using namespace CPCAPI2::test;

#ifdef _WIN32
#include <Windows.h>
#else
#include <sys/time.h>
#endif

typedef std::shared_ptr< RemoteSyncItem > RemoteSyncItemPtr;

/**
 * Return the number of milliseconds since the UNIX epoch (Jan 1st, 1970).
 * NB: Win32 epoch is not the same.
 */
int64_t millisSinceEpoch()
{
   int64_t result( 0 );
#ifdef _WIN32
   FILETIME ft;
   GetSystemTimeAsFileTime( &ft );

   LARGE_INTEGER date, adjust;
   date.HighPart = ft.dwHighDateTime;
   date.LowPart  = ft.dwLowDateTime;

	// 100-nanoseconds = milliseconds * 10000
	adjust.QuadPart = ************** * 10000;

	// removes the diff between 1970 and 1601
	date.QuadPart -= adjust.QuadPart;

   // result in millis, not nano-intervals
   result = date.QuadPart / 10000;
#else
   struct timeval now;
   gettimeofday(&now, NULL);
   result = (((int64_t)now.tv_sec) * 1000L) + (((int64_t)now.tv_usec) / 1000L);
   //safeCout("millisSinceEpoch: result=" << result);
#endif
   return result;
}

namespace
{

class RemoteSyncModuleTest : public CpcapiAutoTest
{
public:
   RemoteSyncModuleTest() {}
   virtual ~RemoteSyncModuleTest() {}

   static void expectSyncConnected(int line, CPCAPI2::test::EventHandler* remoteSyncEvents, CPCAPI2::RemoteSync::SessionHandle handle)
   {
      auto connectEvt = std::async(std::launch::async, [&] ()
      {
         SessionHandle h;
         OnConnectionStateEvent evt;
         ASSERT_TRUE(cpcWaitForEvent(remoteSyncEvents, "RemoteSyncHandler::onConnectionState", 15000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.previousState, ConnectionState_Disconnected);
         ASSERT_EQ(evt.currentState, ConnectionState_Connecting);

         ASSERT_TRUE(cpcWaitForEvent(remoteSyncEvents, "RemoteSyncHandler::onConnectionState", 15000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.previousState, ConnectionState_Connecting);
         ASSERT_EQ(evt.currentState, ConnectionState_Connected);
      });
      waitFor(connectEvt)
   }

   static void expectSyncDisconnected(int line, CPCAPI2::test::EventHandler* remoteSyncEvents, CPCAPI2::RemoteSync::SessionHandle handle)
   {
      auto disconnectEvt = std::async(std::launch::async, [&] ()
      {
         SessionHandle h;
         OnConnectionStateEvent evt;
         ASSERT_TRUE(cpcWaitForEvent(remoteSyncEvents, "RemoteSyncHandler::onConnectionState", 15000, HandleEqualsPred<SessionHandle>(handle), h, evt));
         ASSERT_EQ(evt.previousState, ConnectionState_Connected);
         ASSERT_EQ(evt.currentState, ConnectionState_Disconnected);
      });
      waitFor(disconnectEvt)
   }

   static void expectFetchRangeCreated(int line, CPCAPI2::test::EventHandler* remoteSyncEvents, CPCAPI2::RemoteSync::SessionHandle handle, CPCAPI2::RemoteSync::RequestHandle requestHandle)
   {
      auto fetchRangeCreatedTimeEvent = std::async(std::launch::async, [&]()
      {
         SessionHandle h;
         FetchRangeCompleteEvent evt;
         ASSERT_TRUE(remoteSyncEvents->expectEvent("RemoteSyncHandler::onFetchRangeComplete", 15000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.requestID, requestHandle);

         // Update the item in our map with the new server ID
         for (size_t i = 0; i < evt.items.size(); ++i)
         {
            RemoteSyncItem& pItem(evt.items[i]);
            std::cout << "Item " << i << " content is: " << pItem.content << std::endl;
         }
      });
      waitFor(fetchRangeCreatedTimeEvent);
   }

   static void expectFetchRangeComplete(int line, CPCAPI2::test::EventHandler* remoteSyncEvents, CPCAPI2::RemoteSync::SessionHandle handle, CPCAPI2::RemoteSync::RequestHandle requestHandle)
   {
      auto fetchRangeEventRevision = std::async(std::launch::async, [&]()
      {
         SessionHandle h;
         FetchRangeCompleteEvent evt;
         ASSERT_TRUE(remoteSyncEvents->expectEvent("RemoteSyncHandler::onFetchRangeComplete", 15000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.requestID, requestHandle);

         // Update the item in our map with the new server ID
         for (size_t i = 0; i < evt.items.size(); ++i)
         {
            RemoteSyncItem& pItem(evt.items[i]);
            std::cout << "Item " << i << " content is: " << pItem.content << std::endl;
         }
      });
      waitFor(fetchRangeEventRevision);
   }

   static void expectFetchRangeCompleteEx(int line, CPCAPI2::test::EventHandler* remoteSyncEvents, CPCAPI2::RemoteSync::SessionHandle handle, CPCAPI2::RemoteSync::RequestHandle requestHandle,
      std::string& remoteName, std::string& associatedUri, std::string& associatedNumber, int callDuration, std::string& devicePlatform)
   {
      auto fetchRangeCallHistoryEvent = std::async(std::launch::async, [&]()
      {
         SessionHandle h;
         FetchRangeCompleteEvent evt;
         ASSERT_TRUE(remoteSyncEvents->expectEvent("RemoteSyncHandler::onFetchRangeComplete", 15000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.requestID, requestHandle);

         for (size_t i = 0; i < evt.items.size(); ++i)
         {
            RemoteSyncItem& pItem(evt.items[i]);
            std::cout << "Item [" << i << "] callhistory data is: " << get_debug_string(pItem.callHistory) << std::endl;
            RemoteSyncCallHistory::CallHistoryType chType = RemoteSyncCallHistory::getCallHistoryType(pItem.state);
            std::cout << "call history type: " << chType << std::endl;

            if (i == 0)
            {
               ASSERT_TRUE(RemoteSyncCallHistory::CallHistoryTypeMissed == chType);
               ASSERT_TRUE(remoteName == pItem.callHistory.remoteName.c_str());
               ASSERT_TRUE(associatedUri == pItem.callHistory.associatedUri.c_str());
               ASSERT_TRUE(associatedNumber == pItem.callHistory.associatedNumber.c_str());
               ASSERT_TRUE(callDuration == pItem.callHistory.callDuration);
               ASSERT_TRUE(devicePlatform == pItem.callHistory.primaryDevicePlatform.c_str());
            }
         }
      });
      waitFor(fetchRangeCallHistoryEvent);
   }

   static void expectFetchConversationsComplete(int line, CPCAPI2::test::EventHandler* remoteSyncEvents, CPCAPI2::RemoteSync::SessionHandle handle, CPCAPI2::RemoteSync::RequestHandle requestHandle)
   {
      auto fetchConversationsEvent = std::async( std::launch::async, [&] ()
      {
         SessionHandle h;
         FetchConversationsCompleteEvent evt;
         ASSERT_TRUE(remoteSyncEvents->expectEvent("RemoteSyncHandler::onFetchConversationsComplete", 15000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.requestID, requestHandle);

         // Update the item in our map with the new server ID
         for (size_t i = 0; i < evt.items.size(); ++i)
         {
            // Print some of the information (more is available)
            RemoteSyncConversationThreadItem& pItem(evt.items[i]);
            std::cout << "hasLatestChatInfo: "     << pItem.hasLatestChatInfo     << std::endl;
            std::cout << "hasLatestMessage: "      << pItem.hasLatestMessage      << std::endl;
            std::cout << "latestMessage content: " << pItem.latestMessage.content << std::endl;
            std::cout << "totalMessages: "         << pItem.totalMessages         << std::endl;
            std::cout << "unreadMessages: "        << pItem.unreadMessages        << std::endl;
         }
      });
      waitFor(fetchConversationsEvent);
   }

   static void expectNotificationUpdate(int line, CPCAPI2::test::EventHandler* remoteSyncEvents, CPCAPI2::RemoteSync::SessionHandle handle, int itemCount)
   {
      auto notifyEvent = std::async(std::launch::async, [&] ()
      {
         SessionHandle h;
         NotificationUpdateEvent evt;
         ASSERT_TRUE(cpcWaitForEvent(remoteSyncEvents, "RemoteSyncHandler::onNotificationUpdate", 15000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.items.size(), itemCount);
      });
      waitFor(notifyEvent);
   }

   static void expectNotificationUpdateEx(int line, CPCAPI2::test::EventHandler* remoteSyncEvents, CPCAPI2::RemoteSync::SessionHandle handle, const CPCAPI2::RemoteSync::RemoteSyncItem& syncItem)
   {
      auto notifyEvt = std::async(std::launch::async, [&]()
      {
         SessionHandle h;
         NotificationUpdateEvent evt;
         RemoteSyncItem itemCh;
         for (size_t i = 0; i < 3; ++i)
         {
            ASSERT_TRUE(cpcWaitForEvent(remoteSyncEvents, "RemoteSyncHandler::onNotificationUpdate", 15000, AlwaysTruePred(), h, evt));
            ASSERT_EQ(evt.items.size(), 1);
            itemCh = evt.items[0];
            if (itemCh.itemType == RemoteSyncItem::callhistory)
               break;
         }

         ASSERT_EQ(itemCh.callHistory.associatedNumber, syncItem.callHistory.associatedNumber);
         ASSERT_EQ(itemCh.callHistory.associatedUri, syncItem.callHistory.associatedUri);
         ASSERT_EQ(itemCh.callHistory.callDuration, syncItem.callHistory.callDuration);
         ASSERT_EQ(itemCh.callHistory.remoteName, syncItem.callHistory.remoteName);
         ASSERT_EQ(itemCh.callHistory.primaryDeviceHash, syncItem.callHistory.primaryDeviceHash);
         ASSERT_EQ(itemCh.callHistory.primaryDeviceName, syncItem.callHistory.primaryDeviceName);
         ASSERT_EQ(itemCh.callHistory.primaryDevicePlatform, syncItem.callHistory.primaryDevicePlatform);
      });
      waitFor(notifyEvt);
   }

   static void expectMessageReactions(int line, CPCAPI2::test::EventHandler* remoteSyncEvents, CPCAPI2::RemoteSync::SessionHandle handle, cpc::vector<cpc::string> reactions, long long revision=-1)
   {
      auto notifyEvent = std::async(std::launch::async, [&] ()
      {
         std::string reactionsCsv;
         for (cpc::vector<cpc::string>::const_iterator i = reactions.begin(); i != reactions.end(); ++i)
         {
            if (!reactionsCsv.empty() && !i->empty())
               reactionsCsv += ",";
            reactionsCsv += (*i);
         }

         SessionHandle h;
         MessageReactionsEvent evt;
         ASSERT_TRUE(cpcWaitForEvent(remoteSyncEvents, "RemoteSyncHandler::onMessageReactions", 15000, AlwaysTruePred(), h, evt));
         ASSERT_STREQ(reactionsCsv.c_str(), evt.value.c_str());
         if (revision != -1)
            ASSERT_EQ(revision, evt.rev);
      });
      waitFor(notifyEvent);
   }

   static void expectFetchMessagesReactionsComplete(int line, CPCAPI2::test::EventHandler* remoteSyncEvents, CPCAPI2::RemoteSync::SessionHandle handle, cpc::vector<cpc::string> reactions)
   {
      auto notifyEvent = std::async(std::launch::async, [&] ()
      {
         SessionHandle h;
         FetchMessagesReactionsCompleteEvent evt;
         ASSERT_TRUE(cpcWaitForEvent(remoteSyncEvents, "RemoteSyncHandler::onFetchMessagesReactionsComplete", 15000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(reactions.size(), evt.reactions.size());

         cpc::vector<cpc::string>::const_iterator i1 = reactions.begin();
         cpc::vector<RemoteSyncReaction>::const_iterator i2 = evt.reactions.begin();
         for (; i1 != reactions.end(); ++i1, ++i2)
         {
            ASSERT_STREQ(*i1, i2->value);
         }
      });
      waitFor(notifyEvent);
   }

   static void expectMessageCount(int line, CPCAPI2::test::EventHandler* remoteSyncEvents, CPCAPI2::RemoteSync::SessionHandle handle, CPCAPI2::RemoteSync::RequestHandle requestHandle)
   {
      auto getMessageCountEvent = std::async(std::launch::async, [&] ()
      {
         SessionHandle h;
         MessageCountEvent evt;
         ASSERT_TRUE(remoteSyncEvents->expectEvent("RemoteSyncHandler::onMessageCount", 15000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.requestID, requestHandle);

         // Update the item in our map with the new server ID
         std::cout << "Total messages: " <<  evt.total << std::endl;
         std::cout << "Unread messages: " << evt.unread << std::endl;
         std::cout << "Total conversations: " << evt.totalConversations << std::endl;
         std::cout << "Unread conversations: " << evt.unreadConversations << std::endl;
      });
      waitFor(getMessageCountEvent);
   }

   static void expectSyncItemComplete(int line, CPCAPI2::test::EventHandler* remoteSyncEvents, CPCAPI2::RemoteSync::SessionHandle handle, CPCAPI2::RemoteSync::RequestHandle requestHandle, const CPCAPI2::RemoteSync::RemoteSyncItem& syncItem)
   {
      auto syncEvent = std::async(std::launch::async, [&]()
      {
         SessionHandle h;
         SyncItemsCompleteEvent evt;
         ASSERT_TRUE(cpcWaitForEvent(remoteSyncEvents, "RemoteSyncHandler::onSyncItemsComplete", 15000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.requestID, requestHandle);

         for (size_t i = 0; i < evt.items.size(); ++i)
         {
            if (i == 0)
            {
               RemoteSyncItemUpdate rsiu(evt.items[i]);
               ASSERT_EQ(syncItem.itemRead, rsiu.itemRead);
               ASSERT_EQ(syncItem.itemDeleted, rsiu.itemDeleted);
               ASSERT_EQ(syncItem.itemUserDeleted, rsiu.itemUserDeleted);
               ASSERT_EQ(syncItem.state, rsiu.itemState);
               ASSERT_EQ(syncItem.clientTimestamp, rsiu.clientCreatedTime);
               ASSERT_EQ(RemoteSyncCallHistory::getCallHistoryType(rsiu.itemState), RemoteSyncCallHistory::CallHistoryTypeCompletedElsewhere);
               ASSERT_TRUE(RemoteSyncCallHistory::hasCallFeatureUtilization(RemoteSyncCallHistory::CallFeatureUtilizationVideo, rsiu.itemState));
               ASSERT_FALSE(RemoteSyncCallHistory::hasCallFeatureUtilization(RemoteSyncCallHistory::CallFeatureUtilizationAudioRecording, rsiu.itemState));
               ASSERT_FALSE(RemoteSyncCallHistory::hasCallFeatureUtilization(RemoteSyncCallHistory::CallFeatureUtilizationLocalMixer, rsiu.itemState));
               ASSERT_FALSE(RemoteSyncCallHistory::hasCallFeatureUtilization(RemoteSyncCallHistory::CallFeatureUtilizationScreenshareHost, rsiu.itemState));
               ASSERT_FALSE(RemoteSyncCallHistory::hasCallFeatureUtilization(RemoteSyncCallHistory::CallFeatureUtilizationScreenshareViewer, rsiu.itemState));
            }
         }
      });
      waitFor(syncEvent);
   }

   static void expectSyncItemCompleteEx(int line, CPCAPI2::test::EventHandler* remoteSyncEvents, CPCAPI2::RemoteSync::SessionHandle handle, CPCAPI2::RemoteSync::RequestHandle requestHandle, std::map<cpc::string, RemoteSyncItemPtr>& clientMap)
   {
      auto syncEvent = std::async(std::launch::async, [&] ()
      {
         SessionHandle h;
         SyncItemsCompleteEvent evt;
         ASSERT_TRUE(cpcWaitForEvent(remoteSyncEvents, "RemoteSyncHandler::onSyncItemsComplete", 15000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.requestID, requestHandle);

         // Update the item in our map with the new server ID
         for (size_t i = 0; i < evt.items.size(); ++i)
         {
            RemoteSyncItemUpdate rsiu(evt.items[i]);
            RemoteSyncItemPtr pItem(clientMap[rsiu.clientID]);
            if (pItem.get() == NULL)
               continue;

            // Update the item with whatever info came from the server
            pItem->serverID        = rsiu.serverID;
            pItem->itemRead        = rsiu.itemRead;
            pItem->itemDeleted     = rsiu.itemDeleted;
            pItem->itemEdited      = rsiu.itemEdited;
            pItem->itemUserDeleted = rsiu.itemUserDeleted;
            pItem->state           = rsiu.itemState;
            pItem->clientTimestamp = rsiu.clientCreatedTime;
         }
      });
      waitFor(syncEvent);
   }

   static void expectUpdateItemComplete(int line, CPCAPI2::test::EventHandler* remoteSyncEvents, CPCAPI2::RemoteSync::SessionHandle handle, CPCAPI2::RemoteSync::RequestHandle requestHandle)
   {
      auto updateEvent = std::async(std::launch::async, [&] ()
      {
         SessionHandle h;
         UpdateItemCompleteEvent evt;
         ASSERT_TRUE(remoteSyncEvents->expectEvent("RemoteSyncHandler::onUpdateItemComplete", 15000, AlwaysTruePred(), h, evt));
         ASSERT_TRUE(evt.requestID == requestHandle);
      });
      waitFor(updateEvent);
   }

   static void expectUpdateItemsComplete(int line, CPCAPI2::test::EventHandler* remoteSyncEvents, CPCAPI2::RemoteSync::SessionHandle handle, CPCAPI2::RemoteSync::RequestHandle requestHandle)
   {
      auto updateComplete = std::async(std::launch::async, [&]()
      {
         SessionHandle h;
         UpdateItemsCompleteEvent evt;
         ASSERT_TRUE(cpcWaitForEvent(remoteSyncEvents, "RemoteSyncHandler::onUpdateItemsComplete", 15000, AlwaysTruePred(), h, evt));
         ASSERT_TRUE(evt.requestID == requestHandle);
      });
      waitFor(updateComplete);
   }

   static void expectItemsUpdated(int line, CPCAPI2::test::EventHandler* remoteSyncEvents, const ItemsUpdatedEvent& updatedEvent)
   {
      auto itemsUpdated = std::async(std::launch::async, [&]()
      {
         SessionHandle h;
         ItemsUpdatedEvent evt;
         ASSERT_TRUE(cpcWaitForEvent(remoteSyncEvents, "RemoteSyncHandler::onItemsUpdated", 15000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.isRead, updatedEvent.isRead);
         ASSERT_EQ(evt.isDeleted, updatedEvent.isDeleted);
         ASSERT_EQ(evt.isEdited, updatedEvent.isEdited);
         ASSERT_EQ(evt.accounts.size(), updatedEvent.accounts.size());
         ASSERT_EQ(evt.itemTypes.size(), updatedEvent.itemTypes.size());
         ASSERT_EQ(evt.conversationIDs.size(), updatedEvent.conversationIDs.size());
         ASSERT_EQ(evt.serverIDs.size(), updatedEvent.serverIDs.size());

         for (int i = 0; i < evt.accounts.size(); ++i)
         {
            ASSERT_EQ(evt.accounts[i], updatedEvent.accounts[i]) << " account at index " << i << " do not match";
         }
         for (int i = 0; i < evt.itemTypes.size(); ++i)
         {
            ASSERT_EQ(evt.itemTypes[i], updatedEvent.itemTypes[i]) << " itemTypes at index " << i << " do not match";
         }
         for (int i = 0; i < evt.conversationIDs.size(); ++i)
         {
            ASSERT_EQ(evt.conversationIDs[i], updatedEvent.conversationIDs[i]) << " conversationIDs at index " << i << " do not match";
         }
         for (int i = 0; i < evt.serverIDs.size(); ++i)
         {
            ASSERT_EQ(evt.serverIDs[i], updatedEvent.serverIDs[i]) << " serverIDs at index " << i << " do not match";
         }
      });
      waitFor(itemsUpdated);
   }
};

#define assertSyncConnected(remoteSyncEvents, handle) \
   RemoteSyncModuleTest::expectSyncConnected(__LINE__, remoteSyncEvents, handle)

#define assertSyncDisconnected(remoteSyncEvents, handle) \
   RemoteSyncModuleTest::expectSyncDisconnected(__LINE__, remoteSyncEvents, handle)

#define assertFetchRangeCreated(remoteSyncEvents, handle, requestHandle) \
   RemoteSyncModuleTest::expectFetchRangeCreated(__LINE__, remoteSyncEvents, handle, requestHandle)

#define assertFetchRangeComplete(remoteSyncEvents, handle, requestHandle) \
   RemoteSyncModuleTest::expectFetchRangeComplete(__LINE__, remoteSyncEvents, handle, requestHandle)

#define assertFetchRangeCompleteEx(remoteSyncEvents, handle, requestHandle, remoteName, associatedUri, associatedNumber, callDuration, devicePlatform) \
   RemoteSyncModuleTest::expectFetchRangeCompleteEx(__LINE__, remoteSyncEvents, handle, requestHandle, remoteName, associatedUri, associatedNumber, callDuration, devicePlatform)

#define assertFetchConversationsComplete(remoteSyncEvents, handle, requestHandle) \
   RemoteSyncModuleTest::expectFetchConversationsComplete(__LINE__, remoteSyncEvents, handle, requestHandle)

#define assertNotificationUpdate(remoteSyncEvents, handle, itemCount) \
   RemoteSyncModuleTest::expectNotificationUpdate(__LINE__, remoteSyncEvents, handle, itemCount)

#define assertNotificationUpdateEx(remoteSyncEvents, handle, syncItem) \
   RemoteSyncModuleTest::expectNotificationUpdateEx(__LINE__, remoteSyncEvents, handle, syncItem)

#define assertMessageReactions(remoteSyncEvents, handle, reactions, revision) \
   RemoteSyncModuleTest::expectMessageReactions(__LINE__, remoteSyncEvents, handle, reactions, revision)

#define assertFetchMessagesReactionsComplete(remoteSyncEvents, handle, reactions) \
   RemoteSyncModuleTest::expectFetchMessagesReactionsComplete(__LINE__, remoteSyncEvents, handle, reactions)

#define assertMessageCount(remoteSyncEvents, handle, requestHandle) \
   RemoteSyncModuleTest::expectMessageCount(__LINE__, remoteSyncEvents, handle, requestHandle)

#define assertSyncItemComplete(remoteSyncEvents, handle, requestHandle, syncItem) \
   RemoteSyncModuleTest::expectSyncItemComplete(__LINE__, remoteSyncEvents, handle, requestHandle, syncItem)

#define assertSyncItemCompleteEx(account, handle, requestHandle, clientMap) \
   RemoteSyncModuleTest::expectSyncItemCompleteEx(__LINE__, account, handle, requestHandle, clientMap)

#define assertUpdateItemComplete(remoteSyncEvents, handle, requestHandle) \
   RemoteSyncModuleTest::expectUpdateItemComplete(__LINE__, remoteSyncEvents, handle, requestHandle)

#define assertUpdateItemsComplete(remoteSyncEvents, handle, requestHandle) \
   RemoteSyncModuleTest::expectUpdateItemsComplete(__LINE__, remoteSyncEvents, handle, requestHandle)

#define assertItemsUpdated(remoteSyncEvents, updatedEvt) \
   RemoteSyncModuleTest::expectItemsUpdated(__LINE__, remoteSyncEvents, updatedEvt)

// disabled until we have a solid RemoteSync server test account to run against
TEST_F(RemoteSyncModuleTest, RemoteSync)
{
   RemoteSyncManagerInternal::resetRequestHandleCount(); // kluge for test predictability

   // NB: "alice" and "bob" are actually the same user (since syncing
   // really happens between different registrations of the same user)
   TestAccount alice("alice", Account_Init);
   TestAccount bob("bob", Account_Init);

   // Map keyed by client ID (string), value RemoteSyncItem
   std::map<cpc::string, RemoteSyncItemPtr> clientMap;

   // ^^^ NOTE: RemoteSyncItem cannot be in both maps at the same time. It is
   // only in the first map until such time as a server ID is assigned to it.
   // Internal IP: const cpc::string host( "***********" );
   const cpc::string host("127.0.0.1");
   const cpc::string group("group1.1");
   const cpc::string password("user0001@group1.1:1234");
   const cpc::string user1("user0001@group1.1");
   const cpc::string user2("user0002@group1.1");

   std::stringstream wsUrlAlice;
   wsUrlAlice << "ws://" << host << ":" << std::to_string(8989).c_str() << "/sync/sock/" << group << "/" << user1;
   std::stringstream wsUrlBob;
   wsUrlBob << "ws://" << host << ":" << std::to_string(8990).c_str() << "/sync/sock/" << group << "/" << user1; // Change the port for bob (due to limitations on the test harness)

   RemoteSyncSettings settings;
   settings.wsSettings.certMode = WebSocket::CertVerificationMode_None;
   settings.password = password;
   settings.accounts.push_back(user1);
   settings.wsSettings.webSocketURL = wsUrlAlice.str().c_str();

   // Create both sessions
   alice.remoteSync->configureSettings(alice.remoteSyncSession, settings);

   settings.wsSettings.webSocketURL = wsUrlBob.str().c_str();

   bob.remoteSync->configureSettings(bob.remoteSyncSession, settings);

   // Start the test harness.
   VccsTestHarness aliceHarness(TestEnvironmentConfig::testResourcePath() + "vccs_test_harness_scenarios/RemoteSyncModuleTest_RemoteSync_alice.dat");
   VccsTestHarness bobHarness(TestEnvironmentConfig::testResourcePath() + "vccs_test_harness_scenarios/RemoteSyncModuleTest_RemoteSync_bob.dat", 8990);
   aliceHarness.start();
   bobHarness.start();

   // Connect alice
   alice.remoteSync->connect(alice.remoteSyncSession);
   assertSyncConnected(alice.remoteSyncEvents, alice.remoteSyncSession);

   // Connect bob
   bob.remoteSync->connect(bob.remoteSyncSession);
   assertSyncConnected(bob.remoteSyncEvents, bob.remoteSyncSession);

   // Alice sends an item to be synchronized
   RemoteSyncItemPtr pItem(new RemoteSyncItem(0, "1234", user1,
      RemoteSyncItem::Source::original, user1, user2, "text/plain", "This is a test", "conversation1", "uid-1", 
      "original1", "stable1", true, false, false, false, false, "",
      RemoteSyncItem::DeliveryStatusUnknown, 1234567890)); // hard-coded creation time for benefit of predictability for test harness

   RequestHandle aliceRequest = alice.remoteSync->syncItem(alice.remoteSyncSession, *pItem);
   clientMap[pItem->clientID] = pItem;

   // Alice waits for the sync event to know that the request has finished
   assertSyncItemCompleteEx(alice.remoteSyncEvents, alice.remoteSyncSession, aliceRequest, clientMap);

   // Mark all of our items as being read.
   for (auto iter = clientMap.begin(); iter != clientMap.end(); ++iter)
   {
      RemoteSyncItemPtr pItem(iter->second);
      if (pItem.get() == NULL)
         continue;

      aliceRequest = alice.remoteSync->updateItem(alice.remoteSyncSession, pItem->serverID, "0", 
         "original1", "stable1", true, false, false, false, pItem->state, 0, 2345678901, **********);

      // Wait for the event for each item
      assertUpdateItemComplete(alice.remoteSyncEvents, alice.remoteSyncSession, aliceRequest);
   }

   // Now try to fetch a range of items.
   cpc::vector<RemoteSyncItem::ItemType> itemTypes;
   itemTypes.push_back(RemoteSyncItem::im);
   aliceRequest = alice.remoteSync->fetchRangeCreatedTime(alice.remoteSyncSession, 0, 0, itemTypes, "", "", false, 10, 0, false);

   // Wait for the fetch range event to come back
   assertFetchRangeCreated(alice.remoteSyncEvents, alice.remoteSyncSession, aliceRequest);

   // Now try to fetch a range of items by revision
   itemTypes.clear();
   itemTypes.push_back(RemoteSyncItem::im);
   aliceRequest = alice.remoteSync->fetchRangeRevision(alice.remoteSyncSession, 0, 0, itemTypes, "", "", false, 10, 0, false);

   // Wait for the fetch range event to come back
   assertFetchRangeComplete(alice.remoteSyncEvents, alice.remoteSyncSession, aliceRequest);

   aliceRequest = alice.remoteSync->fetchConversations(alice.remoteSyncSession, 0, 1234567890, 25, 0);
   assertFetchConversationsComplete(alice.remoteSyncEvents, alice.remoteSyncSession, aliceRequest);

   // After all of this, Bob should have an event for On Notify Update
   assertNotificationUpdate(bob.remoteSyncEvents, bob.remoteSyncSession, 1);

   // Get the message count(s)
   cpc::vector<RemoteSyncItem::ItemType> types;
   types.push_back(RemoteSyncItem::im);
   aliceRequest = alice.remoteSync->getMessageCount(alice.remoteSyncSession, user1, types);
   assertMessageCount(alice.remoteSyncEvents, alice.remoteSyncSession, aliceRequest);

   // Now try to fetch call history.
   cpc::vector<RemoteSyncItem::ItemType> chItemType;
   chItemType.push_back(RemoteSyncItem::callhistory);
   aliceRequest = alice.remoteSync->fetchRangeCreatedTime(alice.remoteSyncSession, 0, 0, chItemType, "", "", false, 10, 0, false);

   // Wait for the fetch range event to come back
   std::string remoteName("Mr. Auto Test");
   std::string associatedUri("<EMAIL>");
   std::string associatedNumber("5551234");
   int callDuration(75);
   std::string devicePlatform("iPhone");
   assertFetchRangeCompleteEx(alice.remoteSyncEvents, alice.remoteSyncSession, aliceRequest, remoteName, associatedUri, associatedNumber, callDuration, devicePlatform);

   // check callhistory with sync
   // Alice sends an item to be synchronized
   std::string remoteName2("Mr. Test Auto");
   std::string associatedUri2("<EMAIL>");
   std::string associatedNumber2("5552468");
   int callDuration2(120);
   std::string deviceHash2("qwertyuiop");
   std::string devicePlatform2("iPhone");
   std::string deviceName2("Mr. Test's phone");
   RemoteSyncItemPtr pItemCh(new RemoteSyncItem(0, user1,
      RemoteSyncItem::original, RemoteSyncItem::callhistory, user1, user2, "0", "uid-101", 
      "original1", "stable1", false, false, false, false, cpc::vector<RemoteSyncReaction>(),
      (RemoteSyncCallHistory::ITEM_STATE_CALLHISTORY_TYPE_COMPLETED_ELSEWHERE | RemoteSyncCallHistory::ITEM_STATE_MASK_FEATURE_UTILIZATION_VIDEO),
      1234567890, // hard-coded creation time for benefit of predictability for test harness
      remoteName2.c_str(), associatedUri2.c_str(), associatedNumber2.c_str(), callDuration2, deviceHash2.c_str(), devicePlatform2.c_str(), deviceName2.c_str()));

   aliceRequest = alice.remoteSync->syncItem(alice.remoteSyncSession, *pItemCh);
   assertSyncItemComplete(alice.remoteSyncEvents, alice.remoteSyncSession, aliceRequest, *pItemCh);

   // Check for call history info in NotificationUpdateEvent
   RemoteSyncItem syncItem(*pItemCh);
   syncItem.callHistory.callDuration = 90;
   assertNotificationUpdateEx(bob.remoteSyncEvents, bob.remoteSyncSession, syncItem);

   // alice does updateItems:
   ItemsUpdatedEvent updatedEvt;
   cpc::vector<cpc::string> accounts;
   updatedEvt.accounts.push_back("user0001@group1.1");
   updatedEvt.accounts.push_back("user0002@group2.1");
   cpc::vector<RemoteSyncItem::ItemType> uitsItemTypes;
   updatedEvt.itemTypes.push_back(RemoteSyncItem::im);
   updatedEvt.itemTypes.push_back(RemoteSyncItem::callhistory);
   updatedEvt.conversationIDs.push_back("conversation1");
   updatedEvt.conversationIDs.push_back("conversation101");
   updatedEvt.serverIDs.push_back(33063);
   updatedEvt.serverIDs.push_back(33066);
   updatedEvt.isRead = true;
   updatedEvt.isDeleted = false;
   updatedEvt.isEdited = true;

   aliceRequest = alice.remoteSync->updateItems(alice.remoteSyncSession, updatedEvt.accounts, updatedEvt.itemTypes, updatedEvt.conversationIDs, updatedEvt.serverIDs, updatedEvt.isRead, updatedEvt.isDeleted, updatedEvt.isEdited, **********);
   assertUpdateItemsComplete(alice.remoteSyncEvents, alice.remoteSyncSession, aliceRequest);

   // Check ItemsUpdatedEvent for bob
   assertItemsUpdated(bob.remoteSyncEvents, updatedEvt);

   // Log out alice and bob
   alice.remoteSync->disconnect(alice.remoteSyncSession);
   assertSyncDisconnected(alice.remoteSyncEvents, alice.remoteSyncSession);

   bob.remoteSync->disconnect(bob.remoteSyncSession);
   assertSyncDisconnected(bob.remoteSyncEvents, bob.remoteSyncSession);
}

TEST_F(RemoteSyncModuleTest, RemoteSyncWithReadReceipt)
{
   RemoteSyncManagerInternal::resetRequestHandleCount(); // kluge for test predictability

   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   // Map keyed by client ID (string), value RemoteSyncItem
   std::map<cpc::string, RemoteSyncItemPtr> clientMap;

   const cpc::string host("127.0.0.1");
   const cpc::string group("group1.1");
   const cpc::string password1("user0001@group1.1:1234");
   const cpc::string password2("user0002@group1.1:1234");
   const cpc::string user1("user0001@group1.1");
   const cpc::string user2("user0002@group1.1");

   std::stringstream wsUrlAlice;
   wsUrlAlice << "ws://" << host << ":" << std::to_string(8989).c_str() << "/sync/sock/" << group << "/" << user1;
   std::stringstream wsUrlBob;
   wsUrlBob << "ws://" << host << ":" << std::to_string(8990).c_str() << "/sync/sock/" << group << "/" << user2; // Change the port for bob (due to limitations on the test harness)

   RemoteSyncSettings aliceSettings;
   aliceSettings.wsSettings.certMode = WebSocket::CertVerificationMode_None;
   aliceSettings.password = password1;
   aliceSettings.accounts.push_back(user1);
   aliceSettings.wsSettings.webSocketURL = wsUrlAlice.str().c_str();

   // Create both sessions
   alice.remoteSync->configureSettings(alice.remoteSyncSession, aliceSettings);

   RemoteSyncSettings bobSettings;
   bobSettings.wsSettings.certMode = WebSocket::CertVerificationMode_None;
   bobSettings.password = password2;
   bobSettings.accounts.push_back(user2);
   bobSettings.wsSettings.webSocketURL = wsUrlBob.str().c_str();

   bob.remoteSync->configureSettings(bob.remoteSyncSession, bobSettings);

   // Start the test harness.
   VccsTestHarness aliceHarness(TestEnvironmentConfig::testResourcePath() + "vccs_test_harness_scenarios/RemoteSyncModuleTest_RemoteSyncWithReadReceipt_alice.dat");
   VccsTestHarness bobHarness(TestEnvironmentConfig::testResourcePath() + "vccs_test_harness_scenarios/RemoteSyncModuleTest_RemoteSyncWithReadReceipt_bob.dat", 8990);
   aliceHarness.start();
   bobHarness.start();

   // Connect alice
   alice.remoteSync->connect(alice.remoteSyncSession);
   assertSyncConnected(alice.remoteSyncEvents, alice.remoteSyncSession);

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   // Connect bob
   bob.remoteSync->connect(bob.remoteSyncSession);
   assertSyncConnected(bob.remoteSyncEvents, bob.remoteSyncSession);

   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   // Alice sends a message to Bob
   CPCAPI2::XmppChat::XmppChatHandle aliceChat = alice.chat->createChat(alice.handle);
   alice.chat->addParticipant(aliceChat, bob.config.bare());
   alice.chat->start(aliceChat);

   {
      CPCAPI2::XmppChat::XmppChatHandle h;
      CPCAPI2::XmppChat::NewChatEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.chatType, CPCAPI2::XmppChat::ChatType_Outgoing);
   }

   CPCAPI2::XmppChat::XmppChatMessageHandle cm1 = alice.chat->sendMessage(aliceChat, "test123");
   cpc::string messageId;
   cpc::string threadId;
   {
      RemoteSyncItemPtr pItem(new RemoteSyncItem(0, "cid-1", user1,
         RemoteSyncItem::Source::original, user1, user2, "text/plain", "message-1", "tid-1", "uid-1", 
         "original1", "stable1", true, false, false, false, false, cpc::vector<RemoteSyncReaction>(),
         RemoteSyncItem::DeliveryStatusUnknown, 1234567890)); // hard-coded creation time for benefit of predictability for test harness

      RequestHandle aliceRequest = alice.remoteSync->syncItem(alice.remoteSyncSession, *pItem);
      clientMap[pItem->clientID] = pItem;
      assertSyncItemCompleteEx(alice.remoteSyncEvents, alice.remoteSyncSession, aliceRequest, clientMap);
   }

   {
      CPCAPI2::XmppChat::XmppChatHandle h;
      CPCAPI2::XmppChat::SendMessageSuccessEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onSendMessageSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.message, cm1);
      messageId = evt.messageId;
      threadId = evt.threadId;

      // Alice sends the item to be synchronized
      RemoteSyncItemPtr pItem(new RemoteSyncItem(0, "cid-1", user1,
         RemoteSyncItem::Source::original, user1, user2, "text/plain", "message-1", "tid-1", "uid-1", 
         "original1", "stable1", true, false, false, false, false, cpc::vector<RemoteSyncReaction>(),
         RemoteSyncItem::DeliveryStatusQueued, 1234567890)); // hard-coded creation time for benefit of predictability for test harness

      RequestHandle aliceRequest = alice.remoteSync->syncItem(alice.remoteSyncSession, *pItem);
      clientMap[pItem->clientID] = pItem;
      assertSyncItemCompleteEx(alice.remoteSyncEvents, alice.remoteSyncSession, aliceRequest, clientMap);
   }

   cpc::string remoteSyncToID = alice.chat->getRemoteSyncToID(aliceChat, cm1);
   ASSERT_GT(remoteSyncToID.size(), 2);

   alice.chat->validateChatHandle(alice.handle, aliceChat);
   {
      CPCAPI2::XmppChat::XmppChatHandle h;
      CPCAPI2::XmppChat::ValidateChatHandleEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onValidateChatHandleResult", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_TRUE(evt.chatHandleValid);
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   CPCAPI2::XmppChat::XmppChatHandle bobChat = 0;

   {
      CPCAPI2::XmppChat::XmppChatHandle h;
      CPCAPI2::XmppChat::NewChatEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewChat", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_NE(h, 0);
      ASSERT_EQ(evt.chatType, CPCAPI2::XmppChat::ChatType_Incoming);
      bobChat = h;
   }

   {
      CPCAPI2::XmppChat::XmppChatHandle h;
      CPCAPI2::XmppChat::NewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.from, alice.config.bare());
      ASSERT_EQ(evt.messageContent, "test123");
      ASSERT_TRUE(evt.message != 0);
      ASSERT_EQ(evt.messageId, messageId);
      ASSERT_EQ(evt.threadId, threadId);

      {
         RemoteSyncItemPtr pItem(new RemoteSyncItem(0, "cid-2", user2,
            RemoteSyncItem::Source::original, user1, user2, "text/plain", "message-1", "tid-1", "uid-1", 
            "original1", "stable1", false, false, false, false, false, cpc::vector<RemoteSyncReaction>(),
            RemoteSyncItem::DeliveryStatusReceived, 1234567890)); // hard-coded creation time for benefit of predictability for test harness

         RequestHandle bobRequest = bob.remoteSync->syncItem(bob.remoteSyncSession, *pItem);
         clientMap[pItem->clientID] = pItem;
         assertSyncItemCompleteEx(bob.remoteSyncEvents, bob.remoteSyncSession, bobRequest, clientMap);
      }
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   {
      // Wait for the message delivery notification (from Bob)
      CPCAPI2::XmppChat::XmppChatHandle h;
      CPCAPI2::XmppChat::MessageDeliveredEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onMessageDelivered", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.messageId, messageId);
      ASSERT_EQ(evt.threadId, threadId);

      {
         RemoteSyncItemPtr pItem(new RemoteSyncItem(0, "cid-1", user1,
            RemoteSyncItem::Source::original, user1, user2, "text/plain", "message-1", "tid-1", "uid-1", 
            "original1", "stable1", true, false, false, false, false, cpc::vector<RemoteSyncReaction>(),
            RemoteSyncItem::DeliveryStatusDelivered, 1234567890, 2345678901)); // hard-coded creation time for benefit of predictability for test harness

         RequestHandle aliceRequest = alice.remoteSync->syncItem(alice.remoteSyncSession, *pItem);
         clientMap[pItem->clientID] = pItem;
         assertSyncItemCompleteEx(alice.remoteSyncEvents, alice.remoteSyncSession, aliceRequest, clientMap);
      }
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   {
      RemoteSyncItemPtr pItem(new RemoteSyncItem(0, "cid-2", user2,
         RemoteSyncItem::Source::original, user1, user2, "text/plain", "message-1", "tid-1", "uid-1", 
         "original1", "stable1", false, true, false, false, false, cpc::vector<RemoteSyncReaction>(),
         RemoteSyncItem::DeliveryStatusRead, 1234567890)); // hard-coded creation time for benefit of predictability for test harness

      RequestHandle bobRequest = bob.remoteSync->syncItem(bob.remoteSyncSession, *pItem);
      clientMap[pItem->clientID] = pItem;
      assertSyncItemCompleteEx(bob.remoteSyncEvents, bob.remoteSyncSession, bobRequest, clientMap);
      // Bob reads the message, send notification
      bob.chat->notifyMessageRead(bobChat, threadId, messageId);
   }

   {
      // Wait for the message read receipt (from Bob)
      CPCAPI2::XmppChat::XmppChatHandle h;
      CPCAPI2::XmppChat::MessageReadEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onMessageRead", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.messageId, messageId);
      ASSERT_EQ(evt.threadId, threadId);

      {
         RemoteSyncItemPtr pItem(new RemoteSyncItem(0, "cid-1", user1,
            RemoteSyncItem::Source::original, user1, user2, "text/plain", "message-1", "tid-1", "uid-1", 
            "original1", "stable1", true, true, false, true, false, cpc::vector<RemoteSyncReaction>(),
            RemoteSyncItem::DeliveryStatusRead, 1234567890, 2345678901, **********)); // hard-coded creation time for benefit of predictability for test harness

         RequestHandle aliceRequest = alice.remoteSync->syncItem(alice.remoteSyncSession, *pItem);
         clientMap[pItem->clientID] = pItem;
         assertSyncItemCompleteEx(alice.remoteSyncEvents, alice.remoteSyncSession, aliceRequest, clientMap);
      }
   }

   // Log out alice and bob
   alice.remoteSync->disconnect(alice.remoteSyncSession);
   assertSyncDisconnected(alice.remoteSyncEvents, alice.remoteSyncSession);

   bob.remoteSync->disconnect(bob.remoteSyncSession);
   assertSyncDisconnected(bob.remoteSyncEvents, bob.remoteSyncSession);
}

TEST_F(RemoteSyncModuleTest, RemoteSyncWithReactions)
{
   RemoteSyncManagerInternal::resetRequestHandleCount(); // kluge for test predictability

   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   // Map keyed by client ID (string), value RemoteSyncItem
   std::map<cpc::string, RemoteSyncItemPtr> clientMap;

   const cpc::string host("127.0.0.1");
   const cpc::string group("group1.1");
   const cpc::string password1("user0001@group1.1:1234");
   const cpc::string password2("user0002@group1.1:1234");
   const cpc::string user1("user0001@group1.1");
   const cpc::string user2("user0002@group1.1");

   std::stringstream wsUrlAlice;
   wsUrlAlice << "ws://" << host << ":" << std::to_string(8989).c_str() << "/sync/sock/" << group << "/" << user1;
   std::stringstream wsUrlBob;
   wsUrlBob << "ws://" << host << ":" << std::to_string(8990).c_str() << "/sync/sock/" << group << "/" << user2; // Change the port for bob (due to limitations on the test harness)

   RemoteSyncSettings aliceSettings;
   aliceSettings.wsSettings.certMode = WebSocket::CertVerificationMode_None;
   aliceSettings.password = password1;
   aliceSettings.accounts.push_back(user1);
   aliceSettings.wsSettings.webSocketURL = wsUrlAlice.str().c_str();

   // Create both sessions
   alice.remoteSync->configureSettings(alice.remoteSyncSession, aliceSettings);

   RemoteSyncSettings bobSettings;
   bobSettings.wsSettings.certMode = WebSocket::CertVerificationMode_None;
   bobSettings.password = password2;
   bobSettings.accounts.push_back(user2);
   bobSettings.wsSettings.webSocketURL = wsUrlBob.str().c_str();

   bob.remoteSync->configureSettings(bob.remoteSyncSession, bobSettings);

   // Start the test harness.
   VccsTestHarness aliceHarness(TestEnvironmentConfig::testResourcePath() + "vccs_test_harness_scenarios/RemoteSyncModuleTest_RemoteSyncWithReactions_alice.dat");
   VccsTestHarness bobHarness(TestEnvironmentConfig::testResourcePath() + "vccs_test_harness_scenarios/RemoteSyncModuleTest_RemoteSyncWithReactions_bob.dat", 8990);
   aliceHarness.start();
   bobHarness.start();

   // Connect alice
   alice.remoteSync->connect(alice.remoteSyncSession);
   assertSyncConnected(alice.remoteSyncEvents, alice.remoteSyncSession);

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   // Connect bob
   bob.remoteSync->connect(bob.remoteSyncSession);
   assertSyncConnected(bob.remoteSyncEvents, bob.remoteSyncSession);

   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   // Alice sends a message to Bob
   CPCAPI2::XmppChat::XmppChatHandle aliceChat = alice.chat->createChat(alice.handle);
   alice.chat->addParticipant(aliceChat, bob.config.bare());
   alice.chat->start(aliceChat);

   {
      CPCAPI2::XmppChat::XmppChatHandle h;
      CPCAPI2::XmppChat::NewChatEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.chatType, CPCAPI2::XmppChat::ChatType_Outgoing);
   }

   CPCAPI2::XmppChat::XmppChatMessageHandle cm1 = alice.chat->sendMessage(aliceChat, "test123");
   cpc::string messageId;
   cpc::string threadId;
   {
      RemoteSyncItemPtr pItem(new RemoteSyncItem(0, "cid-1", user1,
         RemoteSyncItem::Source::original, user1, user2, "text/plain", "message-1", "tid-1", "uid-1", 
         "original1", "stable1", true, false, false, false, false, cpc::vector<RemoteSyncReaction>(),
         RemoteSyncItem::DeliveryStatusUnknown, 1234567890)); // hard-coded creation time for benefit of predictability for test harness

      RequestHandle aliceRequest = alice.remoteSync->syncItem(alice.remoteSyncSession, *pItem);
      clientMap[pItem->clientID] = pItem;
      assertSyncItemCompleteEx(alice.remoteSyncEvents, alice.remoteSyncSession, aliceRequest, clientMap);
   }

   {
      CPCAPI2::XmppChat::XmppChatHandle h;
      CPCAPI2::XmppChat::SendMessageSuccessEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onSendMessageSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.message, cm1);
      messageId = evt.messageId;
      threadId = evt.threadId;

      // Alice sends the item to be synchronized
      RemoteSyncItemPtr pItem(new RemoteSyncItem(0, "cid-1", user1,
         RemoteSyncItem::Source::original, user1, user2, "text/plain", "message-1", "tid-1", "uid-1", 
         "original1", "stable1", true, false, false, false, false, cpc::vector<RemoteSyncReaction>(),
         RemoteSyncItem::DeliveryStatusQueued, 1234567890)); // hard-coded creation time for benefit of predictability for test harness

      RequestHandle aliceRequest = alice.remoteSync->syncItem(alice.remoteSyncSession, *pItem);
      clientMap[pItem->clientID] = pItem;
      assertSyncItemCompleteEx(alice.remoteSyncEvents, alice.remoteSyncSession, aliceRequest, clientMap);
   }

   cpc::string remoteSyncToID = alice.chat->getRemoteSyncToID(aliceChat, cm1);
   ASSERT_GT(remoteSyncToID.size(), 2);

   alice.chat->validateChatHandle(alice.handle, aliceChat);
   {
      CPCAPI2::XmppChat::XmppChatHandle h;
      CPCAPI2::XmppChat::ValidateChatHandleEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onValidateChatHandleResult", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_TRUE(evt.chatHandleValid);
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   CPCAPI2::XmppChat::XmppChatHandle bobChat = 0;

   {
      CPCAPI2::XmppChat::XmppChatHandle h;
      CPCAPI2::XmppChat::NewChatEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewChat", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_NE(h, 0);
      ASSERT_EQ(evt.chatType, CPCAPI2::XmppChat::ChatType_Incoming);
      bobChat = h;
   }

   {
      CPCAPI2::XmppChat::XmppChatHandle h;
      CPCAPI2::XmppChat::NewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.from, alice.config.bare());
      ASSERT_EQ(evt.messageContent, "test123");
      ASSERT_TRUE(evt.message != 0);
      ASSERT_EQ(evt.messageId, messageId);
      ASSERT_EQ(evt.threadId, threadId);

      {
         RemoteSyncItemPtr pItem(new RemoteSyncItem(0, "cid-2", user2,
            RemoteSyncItem::Source::original, user1, user2, "text/plain", "message-1", "tid-1", "uid-1", 
            "original1", "stable1", false, false, false, false, false, cpc::vector<RemoteSyncReaction>(),
            RemoteSyncItem::DeliveryStatusReceived, 1234567890)); // hard-coded creation time for benefit of predictability for test harness

         RequestHandle bobRequest = bob.remoteSync->syncItem(bob.remoteSyncSession, *pItem);
         clientMap[pItem->clientID] = pItem;
         assertSyncItemCompleteEx(bob.remoteSyncEvents, bob.remoteSyncSession, bobRequest, clientMap);
      }
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   {
      // Wait for the message delivery notification (from Bob)
      CPCAPI2::XmppChat::XmppChatHandle h;
      CPCAPI2::XmppChat::MessageDeliveredEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onMessageDelivered", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.messageId, messageId);
      ASSERT_EQ(evt.threadId, threadId);

      {
         RemoteSyncItemPtr pItem(new RemoteSyncItem(0, "cid-1", user1,
            RemoteSyncItem::Source::original, user1, user2, "text/plain", "message-1", "tid-1", "uid-1", 
            "original1", "stable1", true, false, false, false, false, cpc::vector<RemoteSyncReaction>(),
            RemoteSyncItem::DeliveryStatusDelivered, 1234567890, 2345678901)); // hard-coded creation time for benefit of predictability for test harness

         RequestHandle aliceRequest = alice.remoteSync->syncItem(alice.remoteSyncSession, *pItem);
         clientMap[pItem->clientID] = pItem;
         assertSyncItemCompleteEx(alice.remoteSyncEvents, alice.remoteSyncSession, aliceRequest, clientMap);
      }
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   {
      RemoteSyncItemPtr pItem(new RemoteSyncItem(0, "cid-2", user2,
         RemoteSyncItem::Source::original, user1, user2, "text/plain", "message-1", "tid-1", "uid-1", 
         "original1", "stable1", false, true, false, false, false, cpc::vector<RemoteSyncReaction>(),
         RemoteSyncItem::DeliveryStatusRead, 1234567890)); // hard-coded creation time for benefit of predictability for test harness

      RequestHandle bobRequest = bob.remoteSync->syncItem(bob.remoteSyncSession, *pItem);
      clientMap[pItem->clientID] = pItem;
      assertSyncItemCompleteEx(bob.remoteSyncEvents, bob.remoteSyncSession, bobRequest, clientMap);
      // Bob reads the message, sends reaction
      cpc::vector<cpc::string> reactions;
      reactions.push_back("💘");
      reactions.push_back("💜");
      bob.chat->sendReaction(bobChat, messageId, reactions);
   }

   {
      // Wait for the message reactions (from Bob)
      CPCAPI2::XmppChat::XmppChatHandle h;
      CPCAPI2::XmppChat::NewReactionEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewReaction", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.reactionTarget, messageId);
      ASSERT_EQ(evt.threadId, threadId);
      ASSERT_EQ(evt.reactions.size(), 2);

      // let's test the Stretto sync for reactions here
      assertMessageReactions(bob.remoteSyncEvents, bob.remoteSyncSession, evt.reactions, 1481640938758);

      {
         cpc::vector<RemoteSyncReaction> reactions;
         reactions.push_back(RemoteSyncReaction(999999, 90 , "addr" , 88 , "💘"));
         reactions.push_back(RemoteSyncReaction(999999, 90 , "addr" , 88 , "💜"));

         RemoteSyncItemPtr pItem(new RemoteSyncItem(0, "cid-1", user1,
            RemoteSyncItem::Source::original, user1, user2, "text/plain", "message-1", "tid-1", "uid-1", 
            "original1", "stable1", true, true, false, true, false, reactions,
            RemoteSyncItem::DeliveryStatusRead, 1234567890, 2345678901, **********)); // hard-coded creation time for benefit of predictability for test harness

         RequestHandle aliceRequest = alice.remoteSync->syncItem(alice.remoteSyncSession, *pItem);
         clientMap[pItem->clientID] = pItem;
         assertSyncItemCompleteEx(alice.remoteSyncEvents, alice.remoteSyncSession, aliceRequest, clientMap);
      }

      {
         // the address below has to match what's in the .dat file
         RemoteSyncReaction reaction(99775511, 1481640938759, "c4d75a4d52ac818d54a286acdf5dff13", 8888, "🤠 CODE");
         bob.remoteSync->sendReaction(bob.remoteSyncSession, reaction);

         // let's test the Stretto sync for incoming reactions from the .dat file
         cpc::vector<cpc::string> reactions;
         reactions.push_back("🤠 DAT");
         assertMessageReactions(bob.remoteSyncEvents, bob.remoteSyncSession, reactions, 1481640938760);
      }

      {
         bob.remoteSync->fetchMessagesReactions(bob.remoteSyncSession, true, 0, 10);

         // let's test the Stretto sync for incoming reactions from the .dat file
         cpc::vector<cpc::string> reactions;
         reactions.push_back("🤠 DAT");
         assertFetchMessagesReactionsComplete(bob.remoteSyncEvents, bob.remoteSyncSession, reactions);
      }

   }

   // Log out alice and bob
   alice.remoteSync->disconnect(alice.remoteSyncSession);
   assertSyncDisconnected(alice.remoteSyncEvents, alice.remoteSyncSession);

   bob.remoteSync->disconnect(bob.remoteSyncSession);
   assertSyncDisconnected(bob.remoteSyncEvents, bob.remoteSyncSession);
}

TEST_F(RemoteSyncModuleTest, RemoteSyncWithReadReceiptReceiverOffline)
{
   RemoteSyncManagerInternal::resetRequestHandleCount(); // kluge for test predictability

   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob", Account_Init);

   // Map keyed by client ID (string), value RemoteSyncItem
   std::map<cpc::string, RemoteSyncItemPtr> clientMap;

   const cpc::string host("127.0.0.1");
   const cpc::string group("group1.1");
   const cpc::string password1("user0001@group1.1:1234");
   const cpc::string password2("user0002@group1.1:1234");
   const cpc::string user1("user0001@group1.1");
   const cpc::string user2("user0002@group1.1");

   std::stringstream wsUrlAlice;
   wsUrlAlice << "ws://" << host << ":" << std::to_string(8989).c_str() << "/sync/sock/" << group << "/" << user1;
   std::stringstream wsUrlBob;
   wsUrlBob << "ws://" << host << ":" << std::to_string(8990).c_str() << "/sync/sock/" << group << "/" << user2; // Change the port for bob (due to limitations on the test harness)

   RemoteSyncSettings aliceSettings;
   aliceSettings.wsSettings.certMode = WebSocket::CertVerificationMode_None;
   aliceSettings.password = password1;
   aliceSettings.accounts.push_back(user1);
   aliceSettings.wsSettings.webSocketURL = wsUrlAlice.str().c_str();
   alice.remoteSync->configureSettings(alice.remoteSyncSession, aliceSettings);

   // Start the test harness.
   VccsTestHarness aliceHarness(TestEnvironmentConfig::testResourcePath() + "vccs_test_harness_scenarios/RemoteSyncModuleTest_RemoteSyncWithReadReceiptReceiverOffline_alice.dat");
   VccsTestHarness bobHarness(TestEnvironmentConfig::testResourcePath() + "vccs_test_harness_scenarios/RemoteSyncModuleTest_RemoteSyncWithReadReceiptReceiverOffline_bob.dat", 8990);
   aliceHarness.start();
   bobHarness.start();

   // Connect alice
   alice.remoteSync->connect(alice.remoteSyncSession);
   assertSyncConnected(alice.remoteSyncEvents, alice.remoteSyncSession);

   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   // Alice sends a message to Bob
   CPCAPI2::XmppChat::XmppChatHandle aliceChat = alice.chat->createChat(alice.handle);
   alice.chat->addParticipant(aliceChat, bob.config.bare());
   alice.chat->start(aliceChat);

   {
      CPCAPI2::XmppChat::XmppChatHandle h;
      CPCAPI2::XmppChat::NewChatEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.chatType, CPCAPI2::XmppChat::ChatType_Outgoing);
   }

   CPCAPI2::XmppChat::XmppChatMessageHandle cm1 = alice.chat->sendMessage(aliceChat, "test123");
   cpc::string messageId;
   cpc::string threadId;
   {
      RemoteSyncItemPtr pItem(new RemoteSyncItem(0, "cid-1", user1,
         RemoteSyncItem::Source::original, user1, user2, "text/plain", "message-1", "tid-1", "uid-1", 
         "original1", "stable1", true, false, false, false, false, "",
         RemoteSyncItem::DeliveryStatusUnknown, 1234567890)); // hard-coded creation time for benefit of predictability for test harness

      RequestHandle aliceRequest = alice.remoteSync->syncItem(alice.remoteSyncSession, *pItem);
      clientMap[pItem->clientID] = pItem;
      assertSyncItemCompleteEx(alice.remoteSyncEvents, alice.remoteSyncSession, aliceRequest, clientMap);
   }

   {
      CPCAPI2::XmppChat::XmppChatHandle h;
      CPCAPI2::XmppChat::SendMessageSuccessEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onSendMessageSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.message, cm1);
      messageId = evt.messageId;
      threadId = evt.threadId;

      // Alice sends the item to be synchronized
      RemoteSyncItemPtr pItem(new RemoteSyncItem(0, "cid-1", user1,
         RemoteSyncItem::Source::original, user1, user2, "text/plain", "message-1", "tid-1", "uid-1", 
         "original1", "stable1", true, false, false, false, false, "",
         RemoteSyncItem::DeliveryStatusQueued, 1234567890)); // hard-coded creation time for benefit of predictability for test harness

      RequestHandle aliceRequest = alice.remoteSync->syncItem(alice.remoteSyncSession, *pItem);
      clientMap[pItem->clientID] = pItem;
      assertSyncItemCompleteEx(alice.remoteSyncEvents, alice.remoteSyncSession, aliceRequest, clientMap);
   }

   cpc::string remoteSyncToID = alice.chat->getRemoteSyncToID(aliceChat, cm1);
   ASSERT_GT(remoteSyncToID.size(), 2);

   alice.disable();

   alice.chat->validateChatHandle(alice.handle, aliceChat);
   {
      CPCAPI2::XmppChat::XmppChatHandle h;
      CPCAPI2::XmppChat::ValidateChatHandleEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onValidateChatHandleResult", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_FALSE(evt.chatHandleValid);
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   RemoteSyncSettings bobSettings;
   bobSettings.wsSettings.certMode = WebSocket::CertVerificationMode_None;
   bobSettings.password = password2;
   bobSettings.accounts.push_back(user2);
   bobSettings.wsSettings.webSocketURL = wsUrlBob.str().c_str();
   bob.remoteSync->configureSettings(bob.remoteSyncSession, bobSettings);

   // Connect bob
   bob.remoteSync->connect(bob.remoteSyncSession);
   assertSyncConnected(bob.remoteSyncEvents, bob.remoteSyncSession);

   bob.enable();

   CPCAPI2::XmppChat::XmppChatHandle bobChat = 0;

   {
      CPCAPI2::XmppChat::XmppChatHandle h;
      CPCAPI2::XmppChat::NewChatEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewChat", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_NE(h, 0);
      ASSERT_EQ(evt.chatType, CPCAPI2::XmppChat::ChatType_Incoming);
      bobChat = h;
   }

   {
      CPCAPI2::XmppChat::XmppChatHandle h;
      CPCAPI2::XmppChat::NewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.from, alice.config.bare());
      ASSERT_EQ(evt.messageContent, "test123");
      ASSERT_TRUE(evt.message != 0);
      ASSERT_EQ(evt.messageId, messageId);
      ASSERT_EQ(evt.threadId, threadId);

      {
         RemoteSyncItemPtr pItem(new RemoteSyncItem(0, "cid-2", user2,
            RemoteSyncItem::Source::original, user1, user2, "text/plain", "message-1", "tid-1", "uid-1", 
            "original1", "stable1", false, false, false, false, false, "",
            RemoteSyncItem::DeliveryStatusReceived, 1234567890)); // hard-coded creation time for benefit of predictability for test harness

         RequestHandle bobRequest = bob.remoteSync->syncItem(bob.remoteSyncSession, *pItem);
         clientMap[pItem->clientID] = pItem;
         assertSyncItemCompleteEx(bob.remoteSyncEvents, bob.remoteSyncSession, bobRequest, clientMap);
      }
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   {
      RemoteSyncItemPtr pItem(new RemoteSyncItem(0, "cid-2", user2,
         RemoteSyncItem::Source::original, user1, user2, "text/plain", "message-1", "tid-1", "uid-1", 
         "original1", "stable1", false, true, false, false, false, "",
         RemoteSyncItem::DeliveryStatusRead, 1234567890)); // hard-coded creation time for benefit of predictability for test harness

      RequestHandle bobRequest = bob.remoteSync->syncItem(bob.remoteSyncSession, *pItem);
      clientMap[pItem->clientID] = pItem;
      assertSyncItemCompleteEx(bob.remoteSyncEvents, bob.remoteSyncSession, bobRequest, clientMap);
      // Bob reads the message, send notification
      bob.chat->notifyMessageRead(bobChat, threadId, messageId);
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   alice.enable();

   {
      // Wait for the message delivery notification (from Bob)
      CPCAPI2::XmppChat::XmppChatHandle h;
      CPCAPI2::XmppChat::MessageDeliveredEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onMessageDelivered", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.messageId, messageId);
      ASSERT_EQ(evt.threadId, threadId);

      {
         RemoteSyncItemPtr pItem(new RemoteSyncItem(0, "cid-1", user1,
            RemoteSyncItem::Source::original, user1, user2, "text/plain", "message-1", "tid-1", "uid-1", 
            "original1", "stable1", true, false, false, false, false, "",
            RemoteSyncItem::DeliveryStatusDelivered, 1234567890, 2345678901)); // hard-coded creation time for benefit of predictability for test harness

         RequestHandle aliceRequest = alice.remoteSync->syncItem(alice.remoteSyncSession, *pItem);
         clientMap[pItem->clientID] = pItem;
         assertSyncItemCompleteEx(alice.remoteSyncEvents, alice.remoteSyncSession, aliceRequest, clientMap);
      }
   }

   {
      // Wait for the message read receipt (from Bob)
      CPCAPI2::XmppChat::XmppChatHandle h;
      CPCAPI2::XmppChat::MessageReadEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onMessageRead", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.messageId, messageId);
      ASSERT_EQ(evt.threadId, threadId);

      {
         RemoteSyncItemPtr pItem(new RemoteSyncItem(0, "cid-1", user1,
            RemoteSyncItem::Source::original, user1, user2, "text/plain", "message-1", "tid-1", "uid-1", 
            "original1", "stable1", true, true, false, false, false, "",
            RemoteSyncItem::DeliveryStatusRead, 1234567890, 2345678901, **********)); // hard-coded creation time for benefit of predictability for test harness

         RequestHandle aliceRequest = alice.remoteSync->syncItem(alice.remoteSyncSession, *pItem);
         clientMap[pItem->clientID] = pItem;
         assertSyncItemCompleteEx(alice.remoteSyncEvents, alice.remoteSyncSession, aliceRequest, clientMap);
      }
   }

   // Log out alice and bob
   alice.remoteSync->disconnect(alice.remoteSyncSession);
   assertSyncDisconnected(alice.remoteSyncEvents, alice.remoteSyncSession);

   bob.remoteSync->disconnect(bob.remoteSyncSession);
   assertSyncDisconnected(bob.remoteSyncEvents, bob.remoteSyncSession);
}

TEST_F(RemoteSyncModuleTest, AutoPing)
{
   RemoteSyncManagerInternal::resetRequestHandleCount(); // kluge for test predictability

   TestAccount alice( "alice", Account_Init );

   // Map keyed by client ID (string), value RemoteSyncItem
   std::map< cpc::string, RemoteSyncItemPtr > clientMap;

   // ^^^ NOTE: RemoteSyncItem cannot be in both maps at the same time. It is
   // only in the first map until such time as a server ID is assigned to it.

   // Internal IP: const cpc::string host( "***********" );
   const cpc::string host( "127.0.0.1" );
   const int         port( 8989 );
   const cpc::string group( "group1.1" );
   const cpc::string password( "user0001@group1.1:1234" );
   const cpc::string user1( "user0001@group1.1" );
   const cpc::string user2( "user0002@group1.1" );

   cpc::string wsURL;
   wsURL = "ws://";
   wsURL += host;
   wsURL += ":";
   wsURL += std::to_string( port ).c_str( );
   wsURL += "/sync/sock/";
   wsURL += group;
   wsURL += "/";
   wsURL += user1;

   RemoteSyncSettings aliceSettings;

   aliceSettings.password = password;
   aliceSettings.wsSettings.webSocketURL = wsURL;
   aliceSettings.wsSettings.pingIntervalSeconds = 1;
   aliceSettings.accounts.push_back( user1 );
   alice.remoteSync->configureSettings( alice.remoteSyncSession, aliceSettings );

   // Start the test harness.
   VccsTestHarness aliceHarness( TestEnvironmentConfig::testResourcePath() + "vccs_test_harness_scenarios/RemoteSyncModuleTest_AutoPing_alice.dat" );
   aliceHarness.start();

   // connect alice
   RequestHandle aliceRequest = alice.remoteSync->connect( alice.remoteSyncSession );
   {
      auto aliceConnect = std::async(std::launch::async, [&] () {
         SessionHandle h;
         OnConnectionStateEvent evt;
         ASSERT_TRUE( cpcWaitForEvent(
            alice.remoteSyncEvents,
            "RemoteSyncHandler::onConnectionState",
            15000,
            AlwaysTruePred( ),
            h, evt ) );
         ASSERT_TRUE( evt.previousState == ConnectionState_Disconnected );
         ASSERT_TRUE( evt.currentState  == ConnectionState_Connecting );

         ASSERT_TRUE( cpcWaitForEvent(
            alice.remoteSyncEvents,
            "RemoteSyncHandler::onConnectionState",
            15000,
            AlwaysTruePred( ),
            h, evt ) );
         ASSERT_TRUE( evt.previousState == ConnectionState_Connecting );
         ASSERT_TRUE( evt.currentState  == ConnectionState_Connected );
      });
      waitFor( aliceConnect );
   }

   // Wait for 10 timestamp delta events (obtained from ping responses)
   for( int i = 0 ; i < 10 ; ++i )
   {
      auto alicePing = std::async( std::launch::async, [ &]( ) {
         SessionHandle h;
         OnTimestampDeltaEvent evt;
         ASSERT_TRUE( cpcWaitForEvent(
            alice.remoteSyncEvents,
            "RemoteSyncHandler::onTimestampDelta",
            15000,
            AlwaysTruePred( ),
            h, evt ) );

         std::cout << "Alice delta time is: " << evt.timestampDelta << std::endl;
      } );
      waitFor( alicePing );
   }

   // Log out alice
   alice.remoteSync->disconnect( alice.remoteSyncSession );
   auto aliceLogoutEvent = std::async( std::launch::async, [&] () {
      SessionHandle h;
      OnConnectionStateEvent evt;
      ASSERT_TRUE( cpcWaitForEvent(
         alice.remoteSyncEvents,
         "RemoteSyncHandler::onConnectionState",
         15000,
         AlwaysTruePred( ),
         h, evt ) );
      ASSERT_TRUE( evt.previousState == ConnectionState_Connected );
      ASSERT_TRUE( evt.currentState  == ConnectionState_Disconnected );
   });
   waitFor( aliceLogoutEvent );
}

TEST_F(RemoteSyncModuleTest, AutoPing_Destroy)
{
   for (int i = 0; i < 2; ++i)
   {
      std::stringstream ss;
      ss << "AutoPing_Destroy iteration" << i;
      SCOPED_TRACE(ss.str().c_str());

      RemoteSyncManagerInternal::resetRequestHandleCount(); // kluge for test predictability

      TestAccount alice( "alice", Account_Init );

      // Map keyed by client ID (string), value RemoteSyncItem
      std::map< cpc::string, RemoteSyncItemPtr > clientMap;

      // ^^^ NOTE: RemoteSyncItem cannot be in both maps at the same time. It is
      // only in the first map until such time as a server ID is assigned to it.

      // Internal IP: const cpc::string host( "***********" );
      const cpc::string host( "127.0.0.1" );
      const int         port( 8989 );
      const cpc::string group( "group1.1" );
      const cpc::string password( "user0001@group1.1:1234" );
      const cpc::string user1( "user0001@group1.1" );
      const cpc::string user2( "user0002@group1.1" );

      cpc::string wsURL;
      wsURL = "ws://";
      wsURL += host;
      wsURL += ":";
      wsURL += std::to_string( port ).c_str( );
      wsURL += "/sync/sock/";
      wsURL += group;
      wsURL += "/";
      wsURL += user1;

      RemoteSyncSettings aliceSettings;

      aliceSettings.password = password;
      aliceSettings.wsSettings.webSocketURL = wsURL;
      aliceSettings.wsSettings.pingIntervalSeconds = 1;
      aliceSettings.accounts.push_back( user1 );
      alice.remoteSync->configureSettings( alice.remoteSyncSession, aliceSettings );

      // Start the test harness.
      VccsTestHarness aliceHarness( TestEnvironmentConfig::testResourcePath() + "vccs_test_harness_scenarios/RemoteSyncModuleTest_AutoPing_destroy_alice.dat" );
      aliceHarness.start();

      // connect alice
      RequestHandle aliceRequest = alice.remoteSync->connect( alice.remoteSyncSession );
      {
         auto aliceConnect = std::async(std::launch::async, [&] () {
            SessionHandle h;
            OnConnectionStateEvent evt;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.remoteSyncEvents,
               "RemoteSyncHandler::onConnectionState",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
            ASSERT_TRUE( evt.previousState == ConnectionState_Disconnected );
            ASSERT_TRUE( evt.currentState  == ConnectionState_Connecting );

            ASSERT_TRUE( cpcWaitForEvent(
               alice.remoteSyncEvents,
               "RemoteSyncHandler::onConnectionState",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
            ASSERT_TRUE( evt.previousState == ConnectionState_Connecting );
            ASSERT_TRUE( evt.currentState  == ConnectionState_Connected );
         });
         waitFor( aliceConnect );
      }

      // Wait for 3 timestamp delta events (obtained from ping responses)
      for( int i = 0 ; i < 3 ; ++i )
      {
         auto alicePing = std::async( std::launch::async, [ &]( ) {
            SessionHandle h;
            OnTimestampDeltaEvent evt;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.remoteSyncEvents,
               "RemoteSyncHandler::onTimestampDelta",
               15000,
               AlwaysTruePred( ),
               h, evt ) );

            std::cout << "Alice delta time is: " << evt.timestampDelta << std::endl;
         } );
         waitFor( alicePing );
      }

      if (i == 0) // try to test different code flows; simulate apps that don't call disconnect
      {
         alice.remoteSync->disconnect( alice.remoteSyncSession );
      }
      alice.remoteSync->destroy( alice.remoteSyncSession );

      auto aliceLogoutEvent = std::async( std::launch::async, [&] () {
         SessionHandle h;
         OnConnectionStateEvent evt;
         ASSERT_TRUE( cpcWaitForEvent(
            alice.remoteSyncEvents,
            "RemoteSyncHandler::onConnectionState",
            15000,
            AlwaysTruePred( ),
            h, evt ) );
         ASSERT_TRUE( evt.previousState == ConnectionState_Connected );
         ASSERT_TRUE( evt.currentState  == ConnectionState_Disconnected );
      });
      waitFor( aliceLogoutEvent );
   }
}

TEST_F(RemoteSyncModuleTest, RetryConnect)
{
   RemoteSyncManagerInternal::resetRequestHandleCount(); // kluge for test predictability

   TestAccount alice( "alice", Account_Init );

   // Map keyed by client ID (string), value RemoteSyncItem
   std::map< cpc::string, RemoteSyncItemPtr > clientMap;

   // ^^^ NOTE: RemoteSyncItem cannot be in both maps at the same time. It is
   // only in the first map until such time as a server ID is assigned to it.

   // Internal IP: const cpc::string host( "***********" );
   const cpc::string host( "127.0.0.1" );
   const int         port( 9995 ); // wrong port to prevent connection from succeeding
   const cpc::string group( "group1.1" );
   const cpc::string password( "user0001@group1.1:1234" );
   const cpc::string user1( "user0001@group1.1" );

   cpc::string wsURL;
   wsURL = "wss://";
   wsURL += host;
   wsURL += ":";
   wsURL += std::to_string( port ).c_str( );
   wsURL += "/sync/sock/";
   wsURL += group;
   wsURL += "/";
   wsURL += user1;

   RemoteSyncSettings aliceSettings;

   aliceSettings.password = password;
   aliceSettings.wsSettings.webSocketURL = wsURL;
   aliceSettings.wsSettings.pingIntervalSeconds = 1;
   aliceSettings.wsSettings.initialRetryIntervalSeconds = 5;
   aliceSettings.wsSettings.maxRetryIntervalSeconds = 30;

   // Configure sessions
   alice.remoteSync->configureSettings( alice.remoteSyncSession, aliceSettings );

   // Wait for alice to connect
   alice.remoteSync->connect( alice.remoteSyncSession );
   {
      auto aliceConnect = std::async(std::launch::async, [&] () {
         SessionHandle h;
         OnConnectionStateEvent evt;
         ASSERT_TRUE( cpcWaitForEvent(
            alice.remoteSyncEvents,
            "RemoteSyncHandler::onConnectionState",
            15000,
            AlwaysTruePred( ),
            h, evt ) );
         ASSERT_EQ( evt.previousState, ConnectionState_Disconnected );
         ASSERT_EQ( evt.currentState, ConnectionState_Connecting );
      });
      waitFor( aliceConnect );
   }

   // Warning: how long to fail may be OS dependent
   {
      auto aliceConnect = std::async(std::launch::async, [&] () {
         SessionHandle h;
         OnConnectionStateEvent evt;
         ASSERT_TRUE( cpcWaitForEvent(
            alice.remoteSyncEvents,
            "RemoteSyncHandler::onConnectionState",
            30000,
            AlwaysTruePred( ),
            h, evt ) );
         ASSERT_EQ( evt.previousState, ConnectionState_Connecting );
         ASSERT_EQ( evt.currentState, ConnectionState_Failed );
      });
      waitFor( aliceConnect );
   }

   // should retry within initialRetryIntervalSeconds = 5 seconds
   {
      auto aliceConnect = std::async(std::launch::async, [&] () {
         SessionHandle h;
         OnConnectionStateEvent evt;
         ASSERT_TRUE( cpcWaitForEvent(
            alice.remoteSyncEvents,
            "RemoteSyncHandler::onConnectionState",
            10000,
            AlwaysTruePred( ),
            h, evt ) );
         ASSERT_EQ( evt.previousState, ConnectionState_Failed );
         ASSERT_EQ( evt.currentState, ConnectionState_Connecting );
      });
      waitFor( aliceConnect );
   }

   // Log out alice
   alice.remoteSync->disconnect( alice.remoteSyncSession );

   // don't try to check event state here. Race condition exists
   // between transition to failure (due to websocket state machine auto
   // reconnecting and failing again, and our disconnect call above)
}

TEST_F(RemoteSyncModuleTest, NetworkChange)
{
   RemoteSyncManagerInternal::resetRequestHandleCount(); // kluge for test predictability


   TestAccount alice("alice", Account_Init);

   std::set<resip::Data> ifaces;

   // Map keyed by client ID (string), value RemoteSyncItem
   std::map< cpc::string, RemoteSyncItemPtr > clientMap;

   // ^^^ NOTE: RemoteSyncItem cannot be in both maps at the same time. It is
   // only in the first map until such time as a server ID is assigned to it.
   // Internal IP: const cpc::string host( "***********" );
   const cpc::string host("127.0.0.1");
   const int         port(8989);
   const cpc::string group("group1.1");
   const cpc::string password("user0001@group1.1:1234");
   const cpc::string user1("user0001@group1.1");
   const cpc::string user2("user0002@group1.1");

   cpc::string wsURL;
   wsURL = "ws://";
   wsURL += host;
   wsURL += ":";
   wsURL += std::to_string(port).c_str();
   wsURL += "/sync/sock/";
   wsURL += group;
   wsURL += "/";
   wsURL += user1;

   RemoteSyncSettings settings;
   settings.wsSettings.certMode = WebSocket::CertVerificationMode_None;
   settings.wsSettings.webSocketURL = wsURL;
   settings.password = password;
   settings.accounts.push_back(user1);

   // Create both sessions
   alice.remoteSync->configureSettings(alice.remoteSyncSession, settings);

   // Change the port for bob (due to limitations on the test harness)
   wsURL = "ws://";
   wsURL += host;
   wsURL += ":";
   wsURL += std::to_string(8990).c_str();
   wsURL += "/sync/sock/";
   wsURL += group;
   wsURL += "/";
   wsURL += user1;
   settings.wsSettings.webSocketURL = wsURL;


   // Start the test harness.
   VccsTestHarness aliceHarness(TestEnvironmentConfig::testResourcePath() + "vccs_test_harness_scenarios/RemoteSyncModuleTest_NetworkChange.dat");
   aliceHarness.start();

   // Connect alice
   alice.remoteSync->connect(alice.remoteSyncSession);
   {
      auto aliceConnect = std::async(std::launch::async, [&]() {
         SessionHandle h;
         OnConnectionStateEvent evt;
         ASSERT_TRUE(cpcWaitForEvent(
            alice.remoteSyncEvents,
            "RemoteSyncHandler::onConnectionState",
            15000,
            AlwaysTruePred(),
            h, evt));
         ASSERT_TRUE(evt.previousState == ConnectionState_Disconnected);
         ASSERT_TRUE(evt.currentState == ConnectionState_Connecting);

         ASSERT_TRUE(cpcWaitForEvent(
            alice.remoteSyncEvents,
            "RemoteSyncHandler::onConnectionState",
            15000,
            AlwaysTruePred(),
            h, evt));
         ASSERT_TRUE(evt.previousState == ConnectionState_Connecting);
         ASSERT_TRUE(evt.currentState == ConnectionState_Connected);
      });
      waitFor(aliceConnect);
   }

   // simulate a network change
   ifaces.clear();
   ifaces.insert("222.222.22.101");
   alice.network->setMockInterfaces(ifaces);
   alice.network->setNetworkTransport(NetworkTransport::TransportWiFi);

   {
      auto aliceConnect = std::async(std::launch::async, [&]() {
         SessionHandle h;
         OnConnectionStateEvent evt;
         ASSERT_TRUE(cpcWaitForEvent(
            alice.remoteSyncEvents,
            "RemoteSyncHandler::onConnectionState",
            15000,
            AlwaysTruePred(),
            h, evt));
         ASSERT_EQ(evt.previousState, ConnectionState_Connected);
         ASSERT_EQ(evt.currentState, ConnectionState_Failed);

         ASSERT_TRUE(cpcWaitForEvent(
            alice.remoteSyncEvents,
            "RemoteSyncHandler::onConnectionState",
            15000,
            AlwaysTruePred(),
            h, evt));
         ASSERT_EQ(evt.previousState, ConnectionState_Failed);
         ASSERT_EQ(evt.currentState, ConnectionState_Connecting);

         ASSERT_TRUE(cpcWaitForEvent(
            alice.remoteSyncEvents,
            "RemoteSyncHandler::onConnectionState",
            15000,
            AlwaysTruePred(),
            h, evt));
         ASSERT_EQ(evt.previousState, ConnectionState_Connecting);
         ASSERT_EQ(evt.currentState, ConnectionState_Connected);
      });
      waitFor(aliceConnect);
   }
}

TEST_F(RemoteSyncModuleTest, DISABLED_LockedOut)
{
   TestAccount alice( "alice", Account_Init );

   // Map keyed by client ID (string), value RemoteSyncItem
   std::map< cpc::string, RemoteSyncItemPtr > clientMap;

   // ^^^ NOTE: RemoteSyncItem cannot be in both maps at the same time. It is
   // only in the first map until such time as a server ID is assigned to it.

   const cpc::string host( "imap.mobilevoiplive.com" );
   const int         port( 8889 );
   const cpc::string group( "unittests.com" );
   const cpc::string password( "123456" );
   const cpc::string user1( "<EMAIL>" );

   cpc::string wsURL;
   wsURL = "wss://";
   wsURL += host;
   wsURL += ":";
   wsURL += std::to_string( port ).c_str( );
   wsURL += "/sync/sock/";
   wsURL += group;
   wsURL += "/";
   wsURL += user1;

   RemoteSyncSettings settings;
   settings.wsSettings.certMode = WebSocket::CertVerificationMode_None;
   settings.wsSettings.webSocketURL = wsURL;
   settings.password = password;
   settings.accounts.push_back( user1 );

   // Create sessions
   SessionHandle aliceSession = alice.remoteSync->configureSettings( alice.remoteSyncSession, settings );

   // Initiate connection for alice
   alice.remoteSync->connect( alice.remoteSyncSession );

   // Wait for alice to connect
   auto aliceConnect = std::async(std::launch::async, [&] () {
      SessionHandle h;
      OnConnectionStateEvent evt;
      ASSERT_TRUE( cpcWaitForEvent(
         alice.remoteSyncEvents,
         "RemoteSyncHandler::onConnectionState",
         15000,
         AlwaysTruePred( ),
         h, evt ) );
      ASSERT_TRUE( evt.previousState == ConnectionState_Disconnected );
      ASSERT_TRUE( evt.currentState  == ConnectionState_Connecting );

      ASSERT_TRUE( cpcWaitForEvent(
         alice.remoteSyncEvents,
         "RemoteSyncHandler::onConnectionState",
         15000,
         AlwaysTruePred( ),
         h, evt ) );
      ASSERT_TRUE( evt.previousState == ConnectionState_Connecting );
      ASSERT_TRUE( evt.currentState  == ConnectionState_Failed );
   });
   waitFor( aliceConnect );

   // Stop the reconnections
   alice.remoteSync->disconnect( alice.remoteSyncSession );

   // Wait for a transition to disconnected (it could be in a couple
   // of different states at this point)
   auto aliceDisconnect = std::async( std::launch::async, [ & ]() {
      for( int i = 0 ; i < 5 ; ++i )
      {
         SessionHandle h;
         OnConnectionStateEvent evt;
         ASSERT_TRUE( cpcWaitForEvent(
            alice.remoteSyncEvents,
            "RemoteSyncHandler::onConnectionState",
            15000,
            AlwaysTruePred(),
            h, evt ) );

         if( evt.currentState == ConnectionState_Disconnected )
            break;
      }
   });
   waitFor( aliceDisconnect );
}

TEST_F(RemoteSyncModuleTest, GetRemoteSyncAccountID)
{
   XmppTestAccount alice("alice");
   cpc::string accountId = alice.account->getRemoteSyncAccountID(alice.handle);
   ASSERT_GT(accountId.size(), 0);

   CPCAPI2::XmppAccount::XmppAccountHandle verifyHandle = alice.account->getAccountHandleFromRemoteSyncID(accountId);
   ASSERT_EQ(alice.handle, verifyHandle);
}

class MyRemoteSyncHandler : public CPCAPI2::RemoteSync::RemoteSyncHandler
{

public:
   MyRemoteSyncHandler(bool expectsEventOnCtorThread) :
      mReceivedEvent(false),
      mExpectEventsOnCtorThtread(expectsEventOnCtorThread)
   {
      mCtorThreadId = std::this_thread::get_id();
   }

   bool receivedEvent() const { return mReceivedEvent; }

protected:
   std::atomic<bool> mReceivedEvent;
   bool mExpectEventsOnCtorThtread;
   std::thread::id mCtorThreadId;

   int onSetAccounts(const SessionHandle& sessionHandle, const SetAccountsEvent& evt)
   {
      return kSuccess;
   }

   int onNotificationUpdate(const SessionHandle& sessionHandle, const NotificationUpdateEvent& evt)
   {
      return kSuccess;
   }

   int onMessageReactions(const SessionHandle& sessionHandle, const MessageReactionsEvent& evt)
   {
      return kSuccess;
   }

   int onFetchMessagesReactionsComplete(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::FetchMessagesReactionsCompleteEvent& evt)
   {
      return kSuccess;
   }

   int onSyncItemsComplete(const SessionHandle& sessionHandle, const SyncItemsCompleteEvent& evt)
   {
      return kSuccess;
   }

   int onUpdateItemComplete(const SessionHandle& sessionHandle, const UpdateItemCompleteEvent& evt)
   {
      return kSuccess;
   }

   int onFetchRangeComplete(const SessionHandle& sessionHandle, const FetchRangeCompleteEvent& evt)
   {
      return kSuccess;
   }

   int onFetchConversationsComplete(const SessionHandle& sessionHandle, const FetchConversationsCompleteEvent& evt)
   {
      return kSuccess;
   }

   int onConversationUpdated(const SessionHandle& sessionHandle, const ConversationUpdatedEvent& evt)
   {
      return kSuccess;
   }

   int onMessageCount(const SessionHandle& sessionHandle, const MessageCountEvent& evt)
   {
      return kSuccess;
   }

   int onError(const SessionHandle& sessionHandle, const OnErrorEvent& evt)
   {
      return kSuccess;
   }

   int onConnectionState(const SessionHandle& sessionHandle, const OnConnectionStateEvent& evt)
   {
      mReceivedEvent = true;

      if (mExpectEventsOnCtorThtread)
      {
         EXPECT_EQ(mCtorThreadId, std::this_thread::get_id());
      }
      else
      {
         EXPECT_NE(mCtorThreadId, std::this_thread::get_id());
      }

      return kSuccess;
   }

   virtual int onTimestampDelta(const SessionHandle& sessionHandle, const OnTimestampDeltaEvent& evt)
   {
      return kSuccess;
   }

   int onUpdateItemsComplete(const SessionHandle& sessionHandle, const UpdateItemsCompleteEvent& evt)
   {
      return kSuccess;
   }

   int onItemsUpdated(const SessionHandle& sessionHandle, const ItemsUpdatedEvent& evt)
   {
      return kSuccess;
   }
};

class MyAsyncRemoteSyncHandler : public MyRemoteSyncHandler,
                                 public CPCAPI2::RemoteSync::RemoteSyncAsyncHandler
{

public:
   MyAsyncRemoteSyncHandler() : MyRemoteSyncHandler(false) // onEvent should be fired from remote sync asio thread
   {
   }

private:
   void onEvent(resip::ReadCallbackBase* rcb)
   {
      mReceivedEvent = true;

      if (mExpectEventsOnCtorThtread)
      {
         EXPECT_EQ(mCtorThreadId, std::this_thread::get_id());
      }
      else
      {
         EXPECT_NE(mCtorThreadId, std::this_thread::get_id());
      }
   }

   int onConnectionState(const SessionHandle& sessionHandle, const OnConnectionStateEvent& evt)
   {
      EXPECT_TRUE(false); // should not hit
      return kSuccess;
   }
};

class MySyncRemoteSyncHandler : public MyRemoteSyncHandler,
                                public CPCAPI2::RemoteSync::RemoteSyncSyncHandler
{
public:
   MySyncRemoteSyncHandler() : MyRemoteSyncHandler(false) // event should occur on remote sync asio thread
   {
   }
};


TEST_F(RemoteSyncModuleTest, SdkSynchronousObserverNoAppHandler)
{
   CPCAPI2::Phone* phone = CPCAPI2::Phone::create();
   phone->initialize(LicenseInfo(), (PhoneHandler*)NULL);

   CPCAPI2::RemoteSync::RemoteSyncManager* rcm = rcm->getInterface(phone);
   CPCAPI2::RemoteSync::SessionHandle h = rcm->create();

   MySyncRemoteSyncHandler observer;

   static_cast<RemoteSyncManagerInternal*>(rcm)->addEventObserver(&observer);

   CPCAPI2::RemoteSync::RemoteSyncSettings s;
   s.wsSettings.webSocketURL = "ws://192.0.2.0"; // non-routable
   rcm->configureSettings(h, s);

   rcm->connect(h);
   rcm->disconnect(h);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   ASSERT_TRUE(observer.receivedEvent());

   static_cast<RemoteSyncManagerInternal*>(rcm)->removeEventObserver(&observer);
   rcm->setHandler(h, NULL);
}

TEST_F(RemoteSyncModuleTest, SdkAsynchronousObserverNoAppHandler)
{
   CPCAPI2::Phone* phone = CPCAPI2::Phone::create();
   phone->initialize(LicenseInfo(), (PhoneHandler*)NULL);

   CPCAPI2::RemoteSync::RemoteSyncManager* rcm = rcm->getInterface(phone);
   CPCAPI2::RemoteSync::SessionHandle h = rcm->create();

   MyAsyncRemoteSyncHandler observer;
   static_cast<RemoteSyncManagerInternal*>(rcm)->addEventObserver(&observer);

   CPCAPI2::RemoteSync::RemoteSyncSettings s;
   s.wsSettings.webSocketURL = "ws://192.0.2.0"; // non-routable
   rcm->configureSettings(h, s);

   rcm->connect(h);
   rcm->disconnect(h);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   ASSERT_TRUE(observer.receivedEvent());

   static_cast<RemoteSyncManagerInternal*>(rcm)->removeEventObserver(&observer);
   rcm->setHandler(h, NULL);
}

TEST_F(RemoteSyncModuleTest, SdkAsynchronousObserverBeforeCreateNoAppHandler)
{
   CPCAPI2::Phone* phone = CPCAPI2::Phone::create();
   phone->initialize(LicenseInfo(), (PhoneHandler*)NULL);

   CPCAPI2::RemoteSync::RemoteSyncManager* rcm = rcm->getInterface(phone);

   MyAsyncRemoteSyncHandler observer;
   static_cast<RemoteSyncManagerInternal*>(rcm)->addEventObserver(&observer);

   CPCAPI2::RemoteSync::SessionHandle h = rcm->create();

   CPCAPI2::RemoteSync::RemoteSyncSettings s;
   s.wsSettings.webSocketURL = "ws://192.0.2.0"; // non-routable
   rcm->configureSettings(h, s);

   rcm->connect(h);
   rcm->disconnect(h);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   ASSERT_TRUE(observer.receivedEvent());

   static_cast<RemoteSyncManagerInternal*>(rcm)->removeEventObserver(&observer);
   rcm->setHandler(h, NULL);
}

TEST_F(RemoteSyncModuleTest, SdkAsynchronousObserverWithAppHandler)
{
   CPCAPI2::Phone* phone = CPCAPI2::Phone::create();
   phone->initialize(LicenseInfo(), (PhoneHandler*)NULL);

   CPCAPI2::RemoteSync::RemoteSyncManager* rcm = rcm->getInterface(phone);
   CPCAPI2::RemoteSync::SessionHandle h = rcm->create();

   MyRemoteSyncHandler appHandler(true);
   rcm->setHandler(h, &appHandler);

   MyAsyncRemoteSyncHandler observer;
   static_cast<RemoteSyncManagerInternal*>(rcm)->addEventObserver(&observer);

   CPCAPI2::RemoteSync::RemoteSyncSettings s;
   s.wsSettings.webSocketURL = "ws://192.0.2.0"; // non-routable
   rcm->configureSettings(h, s);

   rcm->connect(h);
   rcm->disconnect(h);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   rcm->process(1000);

   ASSERT_TRUE(observer.receivedEvent());
   ASSERT_TRUE(appHandler.receivedEvent());

   static_cast<RemoteSyncManagerInternal*>(rcm)->removeEventObserver(&observer);
   rcm->setHandler(h, NULL);
}

TEST_F(RemoteSyncModuleTest, SdkAsynchronousObserverWithAsynchronousAppHandler)
{
   CPCAPI2::Phone* phone = CPCAPI2::Phone::create();
   phone->initialize(LicenseInfo(), (PhoneHandler*)NULL);

   CPCAPI2::RemoteSync::RemoteSyncManager* rcm = rcm->getInterface(phone);
   CPCAPI2::RemoteSync::SessionHandle h = rcm->create();

   MyAsyncRemoteSyncHandler appHandler;
   rcm->setHandler(h, &appHandler);

   MyAsyncRemoteSyncHandler observer;
   static_cast<RemoteSyncManagerInternal*>(rcm)->addEventObserver(&observer);

   CPCAPI2::RemoteSync::RemoteSyncSettings s;
   s.wsSettings.webSocketURL = "ws://192.0.2.0"; // non-routable
   rcm->configureSettings(h, s);

   rcm->connect(h);
   rcm->disconnect(h);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   rcm->process(1000);

   ASSERT_TRUE(observer.receivedEvent());
   ASSERT_TRUE(appHandler.receivedEvent());

   rcm->setHandler(h, NULL);
}

TEST_F(RemoteSyncModuleTest, SdkAsynchronousObserverWithAsynchronousAppHandler_Destroy)
{
   CPCAPI2::Phone* phone = CPCAPI2::Phone::create();
   phone->initialize(LicenseInfo(), (PhoneHandler*)NULL);

   // attempt connection, immediately destroy
   {
      CPCAPI2::RemoteSync::RemoteSyncManager* rcm = rcm->getInterface(phone);
      CPCAPI2::RemoteSync::SessionHandle h = rcm->create();

      MyAsyncRemoteSyncHandler appHandler;
      rcm->setHandler(h, &appHandler);

      MyAsyncRemoteSyncHandler observer;
      static_cast<RemoteSyncManagerInternal*>(rcm)->addEventObserver(&observer);

      CPCAPI2::RemoteSync::RemoteSyncSettings s;
      s.wsSettings.webSocketURL = "ws://192.0.2.0"; // non-routable
      rcm->configureSettings(h, s);

      rcm->connect(h);
      static_cast<RemoteSyncManagerInternal*>(rcm)->removeEventObserver(&observer);
      rcm->setHandler(h, NULL);
      //rcm->disconnect(h);
      rcm->destroy(h);

      std::this_thread::sleep_for(std::chrono::milliseconds(2000));

      rcm->process(1000);
   }

   // attempt connection, immediately disconnect, destroy
   {
      CPCAPI2::RemoteSync::RemoteSyncManager* rcm = rcm->getInterface(phone);
      CPCAPI2::RemoteSync::SessionHandle h = rcm->create();

      MyAsyncRemoteSyncHandler appHandler;
      rcm->setHandler(h, &appHandler);

      MyAsyncRemoteSyncHandler observer;
      static_cast<RemoteSyncManagerInternal*>(rcm)->addEventObserver(&observer);

      CPCAPI2::RemoteSync::RemoteSyncSettings s;
      s.wsSettings.webSocketURL = "ws://192.0.2.0"; // non-routable
      rcm->configureSettings(h, s);

      rcm->connect(h);
      static_cast<RemoteSyncManagerInternal*>(rcm)->removeEventObserver(&observer);
      rcm->setHandler(h, NULL);
      rcm->disconnect(h);
      rcm->destroy(h);

      std::this_thread::sleep_for(std::chrono::milliseconds(2000));

      rcm->process(1000);
   }

   // attempt connection, wait, disconnect, destroy
   {
      CPCAPI2::RemoteSync::RemoteSyncManager* rcm = rcm->getInterface(phone);
      CPCAPI2::RemoteSync::SessionHandle h = rcm->create();

      MyAsyncRemoteSyncHandler appHandler;
      rcm->setHandler(h, &appHandler);

      MyAsyncRemoteSyncHandler observer;
      static_cast<RemoteSyncManagerInternal*>(rcm)->addEventObserver(&observer);

      CPCAPI2::RemoteSync::RemoteSyncSettings s;
      s.wsSettings.webSocketURL = "ws://192.0.2.0"; // non-routable
      rcm->configureSettings(h, s);

      rcm->connect(h);

      std::this_thread::sleep_for(std::chrono::milliseconds(6000));

      static_cast<RemoteSyncManagerInternal*>(rcm)->removeEventObserver(&observer);
      rcm->setHandler(h, NULL);
      rcm->disconnect(h);
      rcm->destroy(h);

      std::this_thread::sleep_for(std::chrono::milliseconds(2000));

      rcm->process(1000);
   }

   // attempt connection, wait, destroy
   {
      CPCAPI2::RemoteSync::RemoteSyncManager* rcm = rcm->getInterface(phone);
      CPCAPI2::RemoteSync::SessionHandle h = rcm->create();

      MyAsyncRemoteSyncHandler appHandler;
      rcm->setHandler(h, &appHandler);

      MyAsyncRemoteSyncHandler observer;
      static_cast<RemoteSyncManagerInternal*>(rcm)->addEventObserver(&observer);

      CPCAPI2::RemoteSync::RemoteSyncSettings s;
      s.wsSettings.webSocketURL = "ws://192.0.2.0"; // non-routable
      rcm->configureSettings(h, s);

      rcm->connect(h);

      std::this_thread::sleep_for(std::chrono::milliseconds(6000));

      // as opposed to above, don't disconnect
      //rcm->disconnect(h);
      static_cast<RemoteSyncManagerInternal*>(rcm)->removeEventObserver(&observer);
      rcm->setHandler(h, NULL);
      rcm->destroy(h);

      std::this_thread::sleep_for(std::chrono::milliseconds(2000));

      rcm->process(1000);
   }
}

TEST_F(RemoteSyncModuleTest, SdkAsynchronousObserverWithSynchronousAppHandler)
{
   CPCAPI2::Phone* phone = CPCAPI2::Phone::create();
   phone->initialize(LicenseInfo(), (PhoneHandler*)NULL);

   CPCAPI2::RemoteSync::RemoteSyncManager* rcm = rcm->getInterface(phone);
   CPCAPI2::RemoteSync::SessionHandle h = rcm->create();

   MySyncRemoteSyncHandler appHandler;
   rcm->setHandler(h, &appHandler);

   MyAsyncRemoteSyncHandler observer;
   static_cast<RemoteSyncManagerInternal*>(rcm)->addEventObserver(&observer);

   CPCAPI2::RemoteSync::RemoteSyncSettings s;
   s.wsSettings.webSocketURL = "ws://192.0.2.0"; // non-routable
   rcm->configureSettings(h, s);

   rcm->connect(h);
   rcm->disconnect(h);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   rcm->process(1000);

   ASSERT_TRUE(observer.receivedEvent());
   ASSERT_TRUE(appHandler.receivedEvent());

   static_cast<RemoteSyncManagerInternal*>(rcm)->removeEventObserver(&observer);
   rcm->setHandler(h, NULL);
}

TEST_F(RemoteSyncModuleTest, SyncItemsBeforeConfigureSettings)
{
   CPCAPI2::Phone* phone = CPCAPI2::Phone::create();
   phone->initialize(LicenseInfo(), (PhoneHandler*)NULL);

   CPCAPI2::RemoteSync::RemoteSyncManager* rcm = rcm->getInterface(phone);
   CPCAPI2::RemoteSync::SessionHandle h = rcm->create();

   MySyncRemoteSyncHandler appHandler;
   rcm->setHandler(h, &appHandler);

   RemoteSyncItem syncItem;
   rcm->syncItem(h, syncItem);

   CPCAPI2::RemoteSync::RemoteSyncSettings s;
   s.wsSettings.webSocketURL = "ws://192.0.2.0"; // non-routable
   rcm->configureSettings(h, s);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   rcm->setHandler(h, NULL);
}

// OBELISK-5947
TEST_F(RemoteSyncModuleTest, DISABLED_RepeatSyncCreate_CheckFdCount)
{
   TestAccount alice( "alice", Account_Init );

   // Map keyed by client ID (string), value RemoteSyncItem
   std::map< cpc::string, RemoteSyncItemPtr > clientMap;

   // ^^^ NOTE: RemoteSyncItem cannot be in both maps at the same time. It is
   // only in the first map until such time as a server ID is assigned to it.

   const cpc::string host( TestEnvironmentConfig::unreachableV4Ip() );
   const int         port( 9995 );
   const cpc::string group( "group1.1" );
   const cpc::string password( "user0001@group1.1:1234" );
   const cpc::string user1( "user0001@group1.1" );

   cpc::string wsURL;
   wsURL = "wss://";
   wsURL += host;
   wsURL += ":";
   wsURL += std::to_string( port ).c_str( );
   wsURL += "/sync/sock/";
   wsURL += group;
   wsURL += "/";
   wsURL += user1;

   RemoteSyncSettings aliceSettings;

   aliceSettings.password = password;
   aliceSettings.wsSettings.webSocketURL = wsURL;

   alice.remoteSync->configureSettings( alice.remoteSyncSession, aliceSettings );
   alice.remoteSync->connect(alice.remoteSyncSession);
   std::this_thread::sleep_for(std::chrono::seconds(2));
   alice.remoteSync->disconnect(alice.remoteSyncSession);
   std::this_thread::sleep_for(std::chrono::seconds(2));
   
   size_t openFdCount = Utils::FileDescriptorMonitor::getSnapshot().size();
   
   for (int i = 0; i < 10; ++i)
   {
      RemoteSync::SessionHandle s = alice.remoteSync->create();
      alice.remoteSync->configureSettings(s, aliceSettings);
      alice.remoteSync->connect(s);
      std::this_thread::sleep_for(std::chrono::seconds(10));
      alice.remoteSync->disconnect(s);
      alice.remoteSync->destroy(s);
      std::this_thread::sleep_for(std::chrono::seconds(2));
      
      size_t newFdCount = Utils::FileDescriptorMonitor::getSnapshot().size();
      size_t delta = newFdCount - openFdCount;
      openFdCount = newFdCount;

      ASSERT_EQ(delta, 0); // currently failing
   }
}

}  // namespace

#endif // (CPCAPI2_BRAND_REMOTE_SYNC_MODULE == 1)
