#include "test_events.h"

using namespace CPCAPI2;

template<typename T>
struct TruePred
{
   bool operator()(T arg)
   {
      return true;
   }
};

void TestEvents::expectPhoneError(int line, TestAccount& account, const cpc::string& module,
   void(*validator)(const PhoneErrorEvent& evt))
{
   PhoneErrorEvent evt;
   cpc::string m;
   ASSERT_TRUE(account.phoneEvents->expectEvent(line, "PhoneHandler::onError", 15000, TruePred<cpc::string>(), m, evt));
   ASSERT_EQ(module, m);
   if (NULL != validator)
   {
      validator(evt);
   }
}

void TestEvents::expectPhoneErrorEx(int line, CPCAPI2::test::EventHandler* events, TestAccount& account, const cpc::string& module,
   void(*validator)(const CPCAPI2::PhoneErrorEvent& evt))
{
   PhoneErrorEvent evt;
   cpc::string m;
   ASSERT_TRUE(events->expectEvent(line, "PhoneHandler::onError", 15000, TruePred<cpc::string>(), m, evt));
   ASSERT_EQ(module, m);
   if (NULL != validator)
   {
      validator(evt);
   }
}
