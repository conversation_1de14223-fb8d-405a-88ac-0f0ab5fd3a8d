#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#if (CPCAPI2_BRAND_MESSAGESTORE_MODULE == 1)

#include "cpcapi2_test_fixture.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>
#include <rutil/Logger.hxx>

#include <boost/regex.hpp>

#include "test_framework/xmpp_test_helper.h"
#include "test_framework/cpcapi2_test_framework.h"
#include "test_framework/bare_bones_sip_endpoint.h"

#include "../CPCAPI2/impl/messagestore/MessageStoreManagerInterface.h"

#include <stdlib.h>
#include <cpcstl/string.h>
#include <string>
#include <thread>
#include <future>
#include <memory>
#include <iostream>
#include <filesystem>

using namespace CPCAPI2;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::MessageStore;
using namespace CPCAPI2::test;

using namespace CPCAPI2::XmppAccount;
using namespace CPCAPI2::XmppMultiUserChat;

#define RESIPROCATE_SUBSYSTEM resip::Subsystem::NONE

#ifdef _WIN32
#include <Windows.h>
#else
#include <sys/time.h>
#endif

static const char* s_sillyMessages[] = {
   "We're no strangers to love",
   "You know the rules and so do I",
   "A full commitment's what I'm thinking of",
   "You wouldn't get this from any other guy",
   "I just wanna tell you how I'm feeling",
   "Gotta make you understand",
   "Never gonna give you up",
   "Never gonna let you down",
   "Never gonna run around and desert you",
   "Never gonna make you cry",
   "Never gonna say goodbye",
   "Never gonna tell a lie and hurt you",
   "We've known each other for so long",
   "Your heart's been aching but",
   "You're too shy to say it",
   "Inside we both know what's been going on",
   "We know the game and we're gonna play it",
   "And if you ask me how I'm feeling",
   "Don't tell me you're too blind to see",
   "Never gonna give you up",
   "Never gonna let you down",
   "Never gonna run around and desert you",
   "Never gonna make you cry",
   "Never gonna say goodbye",
   "Never gonna tell a lie and hurt you",
   "Never gonna give you up",
   "Never gonna let you down",
   "Never gonna run around and desert you",
   "Never gonna make you cry",
   "Never gonna say goodbye",
   "Never gonna tell a lie and hurt you",
   "(Ooh, give you up)",
   "(Ooh, give you up)",
   "(Ooh)",
   "Never gonna give, never gonna give",
   "(Give you up)",
   "(Ooh)",
   "Never gonna give, never gonna give",
   "(Give you up)",
   "We've know each other for so long",
   "Your heart's been aching but",
   "You're too shy to say it",
   "Inside we both know what's been going on",
   "We know the game and we're gonna play it",
   "I just wanna tell you how I'm feeling",
   "Gotta make you understand",
   "Never gonna give you up",
   "Never gonna let you down",
   "Never gonna run around and desert you",
   "Never gonna make you cry",
   "Never gonna say goodbye",
   "Never gonna tell a lie and hurt you",
   "Never gonna give you up",
   "Never gonna let you down",
   "Never gonna run around and desert you",
   "Never gonna make you cry",
   "Never gonna say goodbye",
   "Never gonna tell a lie and hurt you",
   "Never gonna give you up",
   "Never gonna let you down",
   "Never gonna run around and desert you",
   "Never gonna make you cry",
   "Never gonna say goodbye",
   "Never gonna tell a lie and hurt you",
   NULL
};

static const char* s_UserIDs[] = {
   "<EMAIL>",
   "<EMAIL>",
   "<EMAIL>",
   "<EMAIL>",
   "<EMAIL>",
   "<EMAIL>",
   "<EMAIL>",
   "<EMAIL>",
   NULL
};

#ifdef _WIN32
// stolen from https://stackoverflow.com/questions/17258029/c-setenv-undefined-identifier-in-visual-studio#23616164
static int setenv(const char *name, const char *value, int overwrite)
{
    int errcode = 0;
    if(!overwrite) {
        size_t envsize = 0;
        errcode = getenv_s(&envsize, NULL, 0, name);
        if(errcode || envsize) return errcode;
    }
    return _putenv_s(name, value);
}
#endif

// should really be moved somewhere generically into Utils package.
static int64_t millisSinceEpoch()
{
   int64_t result(0);
#ifdef _WIN32
   FILETIME ft;
   GetSystemTimeAsFileTime(&ft);

   LARGE_INTEGER date, adjust;
   date.HighPart = ft.dwHighDateTime;
   date.LowPart = ft.dwLowDateTime;

   // 100-nanoseconds = milliseconds * 10000
   adjust.QuadPart = 11644473600000 * 10000;

   // removes the diff between 1970 and 1601
   date.QuadPart -= adjust.QuadPart;

   // result in millis, not nano-intervals
   result = date.QuadPart / 10000;
#else
   struct timeval now;
   gettimeofday(&now, NULL);
   result = (((int64_t)now.tv_sec) * 1000L) + (((int64_t)now.tv_usec) / 1000L);
#endif
   return result;
}

#ifdef __APPLE__
#include <dirent.h>
#include <cstdio>

static void cleanupDir( void )
{
   DIR* d = opendir(".");
   if (d == NULL)
      return;

   struct dirent* de;
   std::string fileExtension = ".CAC";
   while ((de = readdir(d)) != NULL)
   {
      std::cout << de->d_name << std::endl;
      std::string fileName = de->d_name;
      if (fileName.length() > 4 && 0 == fileName.compare(fileName.length() - 4, 4, fileExtension))
         remove(fileName.c_str());
   }
   closedir(d);
}
#else
#include <filesystem>

static void cleanupDir( void )
{
   // Delete any old files lying around from previous (possibly aborted or crashed?)
   // runs of the message store tests
   std::filesystem::directory_iterator iter( std::filesystem::current_path() );
   std::filesystem::directory_iterator end_iter; // default ctor is past the end
   for( ; iter != end_iter ; ++iter )
   {
      if( !std::filesystem::is_regular_file( iter->status() ))
         continue;

      std::string extension( iter->path().extension().string() );
      if( extension != ".CAC" )
         continue;

      fs:remove( iter->path() );
   }
}
#endif

namespace {

   class MessageStoreModuleTest : public CpcapiAutoTest
   {
   public:
      MessageStoreModuleTest() {}
      virtual ~MessageStoreModuleTest() {}
   };

   TEST_F(MessageStoreModuleTest, FullQuery) {
#if _WIN32
      if (TestEnvironmentConfig::dockerContainerized()) GTEST_SKIP(); // UNIT_TEST_WIN_DOCKER_SKIP
#endif

      XmppTestAccount alice("alice", Account_Init);

      // Has to match the settings in cpcapi2_test_fixture
      const cpc::string roomjid( "<EMAIL>" );

      const uint64_t startTimeMillis = millisSinceEpoch();
      const int queryBlockSize( 50 );

      // Cast the message store interface to a known class for special
      // hidden access to testing functions
      MessageStore::MessageStoreManagerInterface *pInterface = dynamic_cast< MessageStore::MessageStoreManagerInterface* >( alice.messageStoreManager );
      if( pInterface != NULL )
      {
         // first, signal that a room was created. Just make a bogus room handle for it
         CPCAPI2::XmppAccount::XmppAccountHandle hBogusAccount = 0x1337BEEF;
         XmppMultiUserChat::XmppMultiUserChatHandle hBogusChat = 0x8BADF00D;

         XmppMultiUserChat::ServiceAvailabilityEvent sae;
         sae.available = true;
         sae.service = roomjid; // could be different
         pInterface->fireOnServiceAvailability( hBogusAccount, sae );

         XmppMultiUserChat::NewRoomEvent nre;
         nre.hAccount = hBogusAccount;
         nre.roomjid  = roomjid;
         pInterface->fireOnNewRoomHandle( hBogusChat, nre );

         XmppMultiUserChat::MultiUserChatRoomStateChangedEvent mucrsce;
         mucrsce.state.maxHistoryFetch = 50;
         pInterface->fireOnMultiUserChatRoomStateChanged( hBogusChat, mucrsce );

         XmppMultiUserChat::MultiUserChatNewMessageEvent mucnme;
         for( int i = 0, j = 0, k = 0 ; i < queryBlockSize ; ++i )
         {
            mucnme.isDelayedDelivery = true;
            if( s_sillyMessages[ j ] == NULL )
               j = 0;

            if( s_UserIDs[ k ] == NULL )
               k = 0;

            mucnme.plain = s_sillyMessages[ j++ ];
            mucnme.jid = s_UserIDs[ k++ ];
            mucnme.nickname = "Rick";

            uint64_t stampMillis = millisSinceEpoch();
            mucnme.timestamp = stampMillis / 1000; // convert to seconds
            mucnme.millisecond = stampMillis % 1000;

            std::string uniqueid( std::to_string( i ) + "." + std::to_string( j ) + "." + std::to_string( k ));
            mucnme.messageId = uniqueid.c_str();
            pInterface->fireOnMultiUserChatNewMessage( hBogusChat, mucnme );
            std::this_thread::sleep_for( std::chrono::milliseconds( 5 ));
         }
      }

      // Wait for the handle that matches our roomID.
      MessageStoreHandle hMessageStore;
      {
         KreateEvent evt;
         while( evt.roomID != roomjid )
         {
            auto aliceCreate = std::async(std::launch::async, [&] () {
               unsigned int placeHolder;
               ASSERT_TRUE( cpcWaitForEvent(
                  alice.messageStoreEvents,
                  "MessageStoreHandler::onCreate",
                  15000,
                  AlwaysTruePred( ),
                  placeHolder, evt ) );

               hMessageStore = evt.hMessageStore;
            });
            waitFor( aliceCreate );
         }
      }

      // Now that the data has been filled, try to retrieve ALL the history
      alice.messageStoreManager->queryCachedHistory( hMessageStore, 0 );

      // Wait for the data to be returned
      {
         auto aliceQuery = std::async(std::launch::async, [&] () {
            HistoryEvent evt;
            unsigned int placeHolder;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.messageStoreEvents,
               "MessageStoreHandler::onQueryHistory",
               15000,
               AlwaysTruePred( ),
               placeHolder, evt ) );

            std::cout << "Alice history size is: " << evt.history.size() << std::endl;
            ASSERT_TRUE( evt.hMessageStore == hMessageStore );
            ASSERT_TRUE( evt.history.size() == queryBlockSize  );
         });
         waitFor( aliceQuery );
      }

      // Destroy the cache
      alice.messageStoreManager->destroy( hMessageStore );
      {
         auto aliceDestroy = std::async(std::launch::async, [&] () {
            DestroyEvent evt;
            unsigned int placeHolder;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.messageStoreEvents,
               "MessageStoreHandler::onDestroy",
               15000,
               AlwaysTruePred( ),
               placeHolder, evt ) );

            ASSERT_TRUE( evt.hMessageStore == hMessageStore );
            ASSERT_TRUE( evt.result.result == kSuccess );
         });
         waitFor( aliceDestroy );
      }
   }
   
   // OBELISK-5963
   TEST_F(MessageStoreModuleTest, DISABLED_DoubleInsert) {

      XmppTestAccount alice("alice", Account_Init);

      // Has to match the settings in cpcapi2_test_fixture
      const cpc::string roomjid( "<EMAIL>" );

      const uint64_t startTimeMillis = millisSinceEpoch();
      const int queryBlockSize( 50 );

      // Cast the message store interface to a known class for special
      // hidden access to testing functions
      MessageStore::MessageStoreManagerInterface *pInterface = dynamic_cast< MessageStore::MessageStoreManagerInterface* >( alice.messageStoreManager );
      if( pInterface != NULL )
      {
         // first, signal that a room was created. Just make a bogus room handle for it
         CPCAPI2::XmppAccount::XmppAccountHandle hBogusAccount = 0x1337BEEF;
         XmppMultiUserChat::XmppMultiUserChatHandle hBogusChat = 0x8BADF00D;

         XmppMultiUserChat::ServiceAvailabilityEvent sae;
         sae.available = true;
         sae.service = roomjid; // could be different
         pInterface->fireOnServiceAvailability( hBogusAccount, sae );

         XmppMultiUserChat::NewRoomEvent nre;
         nre.hAccount = hBogusAccount;
         nre.roomjid  = roomjid;
         pInterface->fireOnNewRoomHandle( hBogusChat, nre );

         XmppMultiUserChat::MultiUserChatRoomStateChangedEvent mucrsce;
         mucrsce.state.maxHistoryFetch = 50;
         pInterface->fireOnMultiUserChatRoomStateChanged( hBogusChat, mucrsce );

         XmppMultiUserChat::MultiUserChatNewMessageEvent mucnme;
         for( int i = 0, j = 0, k = 0 ; i < queryBlockSize ; ++i )
         {
            mucnme.isDelayedDelivery = true;
            if( s_sillyMessages[ j ] == NULL )
               j = 0;

            if( s_UserIDs[ k ] == NULL )
               k = 0;

            mucnme.plain = s_sillyMessages[ j++ ];
            mucnme.jid = s_UserIDs[ k++ ];
            mucnme.nickname = "Rick";

            uint64_t stampMillis = millisSinceEpoch();
            mucnme.timestamp = stampMillis / 1000; // convert to seconds
            mucnme.millisecond = stampMillis % 1000;

            std::string uniqueid( std::to_string( i ) + "." + std::to_string( j ) + "." + std::to_string( k ));
            mucnme.messageId = uniqueid.c_str();
            pInterface->fireOnMultiUserChatNewMessage( hBogusChat, mucnme );
            
            // OBELISK-5963: intentionally double insert; should not cause unhandled exceptions
            pInterface->fireOnMultiUserChatNewMessage( hBogusChat, mucnme );
            std::this_thread::sleep_for( std::chrono::milliseconds( 5 ));
         }
      }

      // Wait for the handle that matches our roomID.
      MessageStoreHandle hMessageStore;
      {
         KreateEvent evt;
         while( evt.roomID != roomjid )
         {
            auto aliceCreate = std::async(std::launch::async, [&] () {
               unsigned int placeHolder;
               ASSERT_TRUE( cpcWaitForEvent(
                  alice.messageStoreEvents,
                  "MessageStoreHandler::onCreate",
                  15000,
                  AlwaysTruePred( ),
                  placeHolder, evt ) );

               hMessageStore = evt.hMessageStore;
            });
            waitFor( aliceCreate );
         }
      }

      // Now that the data has been filled, try to retrieve ALL the history
      alice.messageStoreManager->queryCachedHistory( hMessageStore, 0 );

      // Wait for the data to be returned
      {
         auto aliceQuery = std::async(std::launch::async, [&] () {
            HistoryEvent evt;
            unsigned int placeHolder;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.messageStoreEvents,
               "MessageStoreHandler::onQueryHistory",
               15000,
               AlwaysTruePred( ),
               placeHolder, evt ) );

            std::cout << "Alice history size is: " << evt.history.size() << std::endl;
            ASSERT_TRUE( evt.hMessageStore == hMessageStore );
            ASSERT_TRUE( evt.history.size() == queryBlockSize  );
         });
         waitFor( aliceQuery );
      }

      // Destroy the cache
      alice.messageStoreManager->destroy( hMessageStore );
      {
         auto aliceDestroy = std::async(std::launch::async, [&] () {
            DestroyEvent evt;
            unsigned int placeHolder;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.messageStoreEvents,
               "MessageStoreHandler::onDestroy",
               15000,
               AlwaysTruePred( ),
               placeHolder, evt ) );

            ASSERT_TRUE( evt.hMessageStore == hMessageStore );
            ASSERT_TRUE( evt.result.result == kSuccess );
         });
         waitFor( aliceDestroy );
      }
   }

   TEST_F(MessageStoreModuleTest, MassMessageStore) {
#if _WIN32
      if (TestEnvironmentConfig::dockerContainerized()) GTEST_SKIP(); // UNIT_TEST_WIN_DOCKER_SKIP
#endif

      cleanupDir();

      XmppTestAccount alice("alice", Account_Init);


      const uint64_t startTimeMillis = millisSinceEpoch();
      const int blockSize(600);


      auto start = std::chrono::system_clock::now();

      // Cast the message store interface to a known class for special
      // hidden access to testing functions
      MessageStore::MessageStoreManagerInterface* pInterface = dynamic_cast<MessageStore::MessageStoreManagerInterface*>(alice.messageStoreManager);
      if (pInterface != NULL)
      {
         // first, signal that a room was created. Just make a bogus room handle for it
         CPCAPI2::XmppAccount::XmppAccountHandle hBogusAccount = 0x1337BEEF;

         XmppMultiUserChat::ServiceAvailabilityEvent sae;
         sae.available = true;
         sae.service = "conference.localhost.localdomain"; // could be different
         pInterface->fireOnServiceAvailability(hBogusAccount, sae);


         const int kRoomCount = 33;
         XmppMultiUserChat::XmppMultiUserChatHandle hBogusChats[kRoomCount];
         for (int i = 0; i < kRoomCount; ++i)
         {
            hBogusChats[i] = 0x8BADF00D + i;

            XmppMultiUserChat::NewRoomEvent nre;
            nre.hAccount = hBogusAccount;

            // Has to match the settings in cpcapi2_test_fixture
            std::stringstream roomjid;
            roomjid << "localroom" << i << "@localhost.localdomain";

            nre.roomjid = roomjid.str().c_str();
            pInterface->fireOnNewRoomHandle(hBogusChats[i], nre);

            XmppMultiUserChat::MultiUserChatRoomStateChangedEvent mucrsce;
            mucrsce.state.maxHistoryFetch = 50;
            pInterface->fireOnMultiUserChatRoomStateChanged(hBogusChats[i], mucrsce);
         }

         XmppMultiUserChat::MultiUserChatNewMessageEvent mucnme;
         for (int i = 0, j = 0, k = 0; i < blockSize; ++i)
         {
            mucnme.isDelayedDelivery = true;
            if (s_sillyMessages[j] == NULL)
               j = 0;

            if (s_UserIDs[k] == NULL)
               k = 0;

            mucnme.plain = s_sillyMessages[j++];
            mucnme.jid = s_UserIDs[k++];

            std::stringstream uniqueId;
            uniqueId << i << "." << j << "." << k;

            mucnme.messageId = uniqueId.str().c_str();

            for (int roomIndex = 0; roomIndex < kRoomCount; ++roomIndex)
            {
               uint64_t stampMillis = millisSinceEpoch();
               mucnme.timestamp = stampMillis / 1000; // convert to seconds
               mucnme.millisecond = stampMillis % 1000;
               pInterface->fireOnMultiUserChatNewMessage(hBogusChats[roomIndex], mucnme);
            }
         }
      }

      class TestReactorExecuteHelper
      {
      public:
         TestReactorExecuteHelper() : mTestFunctionFinished(false) {}

         std::atomic<bool> mTestFunctionFinished;

         static void staticTestFunction(void* context)
         {
            if (TestReactorExecuteHelper * instance = static_cast<TestReactorExecuteHelper*>(context))
            {
               instance->testFunction();
            }
         }

         void testFunction()
         {
            mTestFunctionFinished = true;
         }
      };

      TestReactorExecuteHelper* reh = new TestReactorExecuteHelper();

      static_cast<PhoneInternal*>(alice.phone)->blockUntilRanOnSdkModuleThread(TestReactorExecuteHelper::staticTestFunction, reinterpret_cast<void*>(reh));

      auto end = std::chrono::system_clock::now();

      std::chrono::duration<double> diff = end - start;

      ASSERT_EQ(true, reh->mTestFunctionFinished);
      InfoLog(<< "main SDK thread took " << diff.count() << " seconds to run our method");

#if defined(__linux__) && !defined(ANDROID)
      const int timeLimitSec = 20;
#else
      const int timeLimitSec = 10;
#endif
      ASSERT_LE(diff.count(), timeLimitSec);

      delete reh;

   }

   TEST_F(MessageStoreModuleTest, EvilTimestamps) {
#if _WIN32
      if (TestEnvironmentConfig::dockerContainerized()) GTEST_SKIP(); // UNIT_TEST_WIN_DOCKER_SKIP
#endif

      XmppTestAccount alice("alice", Account_Init);

      // Has to match the settings in cpcapi2_test_fixture
      const cpc::string roomjid( "<EMAIL>" );

      const uint64_t startTimeMillis = millisSinceEpoch();
      const int queryBlockSize( 64 );

      // Cast the message store interface to a known class for special
      // hidden access to testing functions
      MessageStore::MessageStoreManagerInterface *pInterface = dynamic_cast< MessageStore::MessageStoreManagerInterface* >( alice.messageStoreManager );
      if( pInterface != NULL )
      {
         // first, signal that a room was created. Just make a bogus room handle for it
         CPCAPI2::XmppAccount::XmppAccountHandle hBogusAccount = 0x1337BEEF;
         XmppMultiUserChat::XmppMultiUserChatHandle hBogusChat = 0x8BADF00D;

         XmppMultiUserChat::ServiceAvailabilityEvent sae;
         sae.available = true;
         sae.service = roomjid; // could be different
         pInterface->fireOnServiceAvailability( hBogusAccount, sae );

         XmppMultiUserChat::NewRoomEvent nre;
         nre.hAccount = hBogusAccount;
         nre.roomjid  = roomjid;
         pInterface->fireOnNewRoomHandle( hBogusChat, nre );

         XmppMultiUserChat::MultiUserChatRoomStateChangedEvent mucrsce;
         mucrsce.state.maxHistoryFetch = 64;
         pInterface->fireOnMultiUserChatRoomStateChanged( hBogusChat, mucrsce );

         XmppMultiUserChat::MultiUserChatNewMessageEvent mucnme;
         for( int i = 0, j = 0, k = 0 ; i < queryBlockSize ; ++i )
         {
            mucnme.isDelayedDelivery = true;
            if( s_sillyMessages[ j ] == NULL )
               j = 0;

            if( s_UserIDs[ k ] == NULL )
               k = 0;

            mucnme.plain = s_sillyMessages[ j++ ];
            mucnme.jid = s_UserIDs[ k++ ];
            mucnme.nickname = "Rick";

            // Just set all the timestamps the same :)
            mucnme.timestamp = 123456;
            mucnme.millisecond = 0;

            std::string uniqueid( std::to_string( i ) + "." + std::to_string( j ) + "." + std::to_string( k ));
            mucnme.messageId = uniqueid.c_str();
            pInterface->fireOnMultiUserChatNewMessage( hBogusChat, mucnme );
            std::this_thread::sleep_for( std::chrono::milliseconds( 5 ));
         }
      }

      // Wait for the handle that matches our roomID.
      MessageStoreHandle hMessageStore;
      {
         KreateEvent evt;
         while( evt.roomID != roomjid )
         {
            auto aliceCreate = std::async(std::launch::async, [&] () {
               unsigned int placeHolder;
               ASSERT_TRUE( cpcWaitForEvent(
                  alice.messageStoreEvents,
                  "MessageStoreHandler::onCreate",
                  15000,
                  AlwaysTruePred( ),
                  placeHolder, evt ) );

               hMessageStore = evt.hMessageStore;
            });
            waitFor( aliceCreate );
         }
      }

      // Now that the data has been filled, try to retrieve ALL the history
      alice.messageStoreManager->queryCachedHistory( hMessageStore, 0 );

      // Wait for the data to be returned
      {
         auto aliceQuery = std::async(std::launch::async, [&] () {
            HistoryEvent evt;
            unsigned int placeHolder;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.messageStoreEvents,
               "MessageStoreHandler::onQueryHistory",
               15000,
               AlwaysTruePred( ),
               placeHolder, evt ) );

            std::cout << "Alice history size is: " << evt.history.size() << std::endl;
            ASSERT_TRUE( evt.hMessageStore == hMessageStore );
            ASSERT_TRUE( evt.history.size() == queryBlockSize  );

            cpc::vector< MessageStore::MessageInfo >::const_iterator iter = evt.history.begin();
            while( iter != evt.history.end() )
            {
               std::cout << iter->plainTextContent << std::endl;
               ++iter;
            }
         });
         waitFor( aliceQuery );
      }

      // Try to retrieve the last bit of history
      alice.messageStoreManager->queryCachedHistoryBackward( hMessageStore, millisSinceEpoch(), 6 );

      // Wait for the data to be returned
      {
         auto aliceQuery = std::async(std::launch::async, [&] () {
            HistoryEvent evt;
            unsigned int placeHolder;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.messageStoreEvents,
               "MessageStoreHandler::onQueryHistory",
               15000,
               AlwaysTruePred( ),
               placeHolder, evt ) );

            std::cout << "Alice backward history size is: " << evt.history.size() << std::endl;
            ASSERT_TRUE( evt.hMessageStore == hMessageStore );
            ASSERT_TRUE( evt.history.size() == 6 );

            cpc::vector< MessageStore::MessageInfo >::const_iterator iter = evt.history.begin();
            while( iter != evt.history.end() )
            {
               std::cout << iter->plainTextContent << std::endl;
               ++iter;
            }
         });
         waitFor( aliceQuery );
      }

      // Destroy the cache
      alice.messageStoreManager->destroy( hMessageStore );
      {
         auto aliceDestroy = std::async(std::launch::async, [&] () {
            DestroyEvent evt;
            unsigned int placeHolder;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.messageStoreEvents,
               "MessageStoreHandler::onDestroy",
               15000,
               AlwaysTruePred( ),
               placeHolder, evt ) );

            ASSERT_TRUE( evt.hMessageStore == hMessageStore );
            ASSERT_TRUE( evt.result.result == kSuccess );
         });
         waitFor( aliceDestroy );
      }
   }

   TEST_F(MessageStoreModuleTest, MessageCorrection) {
#if _WIN32
      if (TestEnvironmentConfig::dockerContainerized()) GTEST_SKIP(); // UNIT_TEST_WIN_DOCKER_SKIP
#endif

      XmppTestAccount alice("alice", Account_Init);

      // Has to match the settings in cpcapi2_test_fixture
      const cpc::string roomjid( "<EMAIL>" );

      const uint64_t startTimeMillis = millisSinceEpoch();
      const int queryBlockSize( 64 );
      const XmppMultiUserChat::XmppMultiUserChatHandle hBogusChat = 0x8BADF00D;

      // Cast the message store interface to a known class for special
      // hidden access to testing functions
      MessageStore::MessageStoreManagerInterface *pInterface = dynamic_cast< MessageStore::MessageStoreManagerInterface* >( alice.messageStoreManager );
      if( pInterface != NULL )
      {
         // first, signal that a room was created. Just make a bogus room handle for it
         CPCAPI2::XmppAccount::XmppAccountHandle hBogusAccount = 0x1337BEEF;

         XmppMultiUserChat::ServiceAvailabilityEvent sae;
         sae.available = true;
         sae.service = roomjid; // could be different
         pInterface->fireOnServiceAvailability( hBogusAccount, sae );

         XmppMultiUserChat::NewRoomEvent nre;
         nre.hAccount = hBogusAccount;
         nre.roomjid  = roomjid;
         pInterface->fireOnNewRoomHandle( hBogusChat, nre );

         XmppMultiUserChat::MultiUserChatRoomStateChangedEvent mucrsce;
         mucrsce.state.maxHistoryFetch = 64;
         pInterface->fireOnMultiUserChatRoomStateChanged( hBogusChat, mucrsce );

         XmppMultiUserChat::MultiUserChatNewMessageEvent mucnme;
         for( int i = 0, j = 0, k = 0 ; i < queryBlockSize ; ++i )
         {
            mucnme.isDelayedDelivery = true;
            if( s_sillyMessages[ j ] == NULL )
               j = 0;

            if( s_UserIDs[ k ] == NULL )
               k = 0;

            mucnme.plain = s_sillyMessages[ j++ ];
            mucnme.jid = s_UserIDs[ k++ ];
            mucnme.nickname = "Rick";

            // Just set all the timestamps the same :)
            mucnme.timestamp = 123456;
            mucnme.millisecond = 0;

            std::string uniqueid( std::to_string( i ) + "." + std::to_string( j ) + "." + std::to_string( k ));
            mucnme.messageId = uniqueid.c_str();
            pInterface->fireOnMultiUserChatNewMessage( hBogusChat, mucnme );
            std::this_thread::sleep_for( std::chrono::milliseconds( 5 ));
         }
      }

      // Wait for the handle that matches our roomID.
      MessageStoreHandle hMessageStore;
      {
         KreateEvent evt;
         while( evt.roomID != roomjid )
         {
            auto aliceCreate = std::async(std::launch::async, [&] () {
               unsigned int placeHolder;
               ASSERT_TRUE( cpcWaitForEvent(
                  alice.messageStoreEvents,
                  "MessageStoreHandler::onCreate",
                  15000,
                  AlwaysTruePred( ),
                  placeHolder, evt ) );

               hMessageStore = evt.hMessageStore;
            });
            waitFor( aliceCreate );
         }
      }

      // Now that the data has been filled, try to retrieve ALL the history
      alice.messageStoreManager->queryCachedHistory( hMessageStore, 0 );

      XmppMultiUserChat::MultiUserChatNewMessageEvent mucnmeUpdated;

      // Wait for the data to be returned
      {
         auto aliceQuery = std::async(std::launch::async, [&] () {
            HistoryEvent evt;
            unsigned int placeHolder;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.messageStoreEvents,
               "MessageStoreHandler::onQueryHistory",
               15000,
               AlwaysTruePred( ),
               placeHolder, evt ) );

            std::cout << "Alice history1 size is: " << evt.history.size() << std::endl;
            ASSERT_TRUE( evt.hMessageStore == hMessageStore );
            ASSERT_TRUE( evt.history.size() == queryBlockSize );

            cpc::vector< MessageStore::MessageInfo >::const_iterator iter = evt.history.begin();
            int i = 0;
            while( iter != evt.history.end() )
            {
               std::cout << iter->plainTextContent << std::endl;

               ASSERT_FALSE( iter->isEdited );
               ASSERT_FALSE( iter->isDeleted );
               ASSERT_FALSE( iter->isDelivered );
               
               if (i == 0) // test one change only
               {
                  mucnmeUpdated.isDelayedDelivery = true;
                  mucnmeUpdated.messageId = iter->uniqueID + "-r";
                  mucnmeUpdated.plain = iter->plainTextContent;
                  mucnmeUpdated.jid = iter->sender.uniqueID.c_str();
                  mucnmeUpdated.replaces = iter->uniqueID;

                  // set the timestamp later so it appears as the last history entry below
                  mucnmeUpdated.timestamp = 123499;
                  mucnmeUpdated.millisecond = 0;

                  pInterface->fireOnMultiUserChatNewMessage( hBogusChat, mucnmeUpdated );
                  std::this_thread::sleep_for( std::chrono::milliseconds( 5 ));
               }

               ++iter;
               i++;
            }
         });
         waitFor( aliceQuery );
      }

      // Now that the data has been filled, try to retrieve ALL the history (again)
      alice.messageStoreManager->queryCachedHistory( hMessageStore, 0 );

      // Wait for the data to be returned
      {
         auto aliceQuery = std::async(std::launch::async, [&] () {
            HistoryEvent evt;
            unsigned int placeHolder;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.messageStoreEvents,
               "MessageStoreHandler::onQueryHistory",
               15000,
               AlwaysTruePred( ),
               placeHolder, evt ) );

            std::cout << "Alice history2 size is: " << evt.history.size() << std::endl;
            ASSERT_TRUE( evt.hMessageStore == hMessageStore );
            ASSERT_TRUE( evt.history.size() == queryBlockSize );

            cpc::vector< MessageStore::MessageInfo >::const_iterator iter = evt.history.begin();
            int i = 0;
            while( iter != evt.history.end() )
            {
               std::cout << iter->plainTextContent << std::endl;

               if (i == queryBlockSize-1) // check the last history entry for our one change
               {
                  ASSERT_TRUE( mucnmeUpdated.replaces == iter->uniqueID);
                  ASSERT_TRUE( mucnmeUpdated.plain == iter->plainTextContent);
                  ASSERT_TRUE( mucnmeUpdated.jid == iter->sender.uniqueID.c_str());
                  ASSERT_TRUE( mucnmeUpdated.timestamp == 123499 );
                  ASSERT_TRUE( mucnmeUpdated.millisecond == 0 );

                  ASSERT_TRUE( iter->isEdited );
               }
               else
                  ASSERT_FALSE( iter->isEdited );
               ASSERT_FALSE( iter->isDeleted );
               ASSERT_FALSE( iter->isDelivered );

               ++iter;
               i++;
            }
         });
         waitFor( aliceQuery );
      }

      // Destroy the cache
      alice.messageStoreManager->destroy( hMessageStore );
      {
         auto aliceDestroy = std::async(std::launch::async, [&] () {
            DestroyEvent evt;
            unsigned int placeHolder;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.messageStoreEvents,
               "MessageStoreHandler::onDestroy",
               15000,
               AlwaysTruePred( ),
               placeHolder, evt ) );

            ASSERT_TRUE( evt.hMessageStore == hMessageStore );
            ASSERT_TRUE( evt.result.result == kSuccess );
         });
         waitFor( aliceDestroy );
      }
   }

   TEST_F(MessageStoreModuleTest, MessageRetraction) {
#if _WIN32
      if (TestEnvironmentConfig::dockerContainerized()) GTEST_SKIP(); // UNIT_TEST_WIN_DOCKER_SKIP
#endif

      XmppTestAccount alice("alice", Account_Init);

      // Has to match the settings in cpcapi2_test_fixture
      const cpc::string roomjid( "<EMAIL>" );

      const uint64_t startTimeMillis = millisSinceEpoch();
      const int queryBlockSize( 64 );
      const XmppMultiUserChat::XmppMultiUserChatHandle hBogusChat = 0x8BADF00D;

      // Cast the message store interface to a known class for special
      // hidden access to testing functions
      MessageStore::MessageStoreManagerInterface *pInterface = dynamic_cast< MessageStore::MessageStoreManagerInterface* >( alice.messageStoreManager );
      if( pInterface != NULL )
      {
         // first, signal that a room was created. Just make a bogus room handle for it
         CPCAPI2::XmppAccount::XmppAccountHandle hBogusAccount = 0x1337BEEF;

         XmppMultiUserChat::ServiceAvailabilityEvent sae;
         sae.available = true;
         sae.service = roomjid; // could be different
         pInterface->fireOnServiceAvailability( hBogusAccount, sae );

         XmppMultiUserChat::NewRoomEvent nre;
         nre.hAccount = hBogusAccount;
         nre.roomjid  = roomjid;
         pInterface->fireOnNewRoomHandle( hBogusChat, nre );

         XmppMultiUserChat::MultiUserChatRoomStateChangedEvent mucrsce;
         mucrsce.state.maxHistoryFetch = 64;
         pInterface->fireOnMultiUserChatRoomStateChanged( hBogusChat, mucrsce );

         XmppMultiUserChat::MultiUserChatNewMessageEvent mucnme;
         for( int i = 0, j = 0, k = 0 ; i < queryBlockSize ; ++i )
         {
            mucnme.isDelayedDelivery = true;
            if( s_sillyMessages[ j ] == NULL )
               j = 0;

            if( s_UserIDs[ k ] == NULL )
               k = 0;

            mucnme.plain = s_sillyMessages[ j++ ];
            mucnme.jid = s_UserIDs[ k++ ];
            mucnme.nickname = "Rick";

            // Just set all the timestamps the same :)
            mucnme.timestamp = 123456;
            mucnme.millisecond = 123;

            std::string uniqueid( std::to_string( i ) + "." + std::to_string( j ) + "." + std::to_string( k ));
            mucnme.messageId = uniqueid.c_str();
            pInterface->fireOnMultiUserChatNewMessage( hBogusChat, mucnme );
            std::this_thread::sleep_for( std::chrono::milliseconds( 5 ));
         }
      }

      // Wait for the handle that matches our roomID.
      MessageStoreHandle hMessageStore;
      {
         KreateEvent evt;
         while( evt.roomID != roomjid )
         {
            auto aliceCreate = std::async(std::launch::async, [&] () {
               unsigned int placeHolder;
               ASSERT_TRUE( cpcWaitForEvent(
                  alice.messageStoreEvents,
                  "MessageStoreHandler::onCreate",
                  15000,
                  AlwaysTruePred( ),
                  placeHolder, evt ) );

               hMessageStore = evt.hMessageStore;
            });
            waitFor( aliceCreate );
         }
      }

      // Now that the data has been filled, try to retrieve ALL the history
      alice.messageStoreManager->queryCachedHistory( hMessageStore, 0 );

      XmppMultiUserChat::MultiUserChatNewMessageRetractionEvent mucnmeRetracted;

      // Wait for the data to be returned
      {
         auto aliceQuery = std::async(std::launch::async, [&] () {
            HistoryEvent evt;
            unsigned int placeHolder;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.messageStoreEvents,
               "MessageStoreHandler::onQueryHistory",
               15000,
               AlwaysTruePred( ),
               placeHolder, evt ) );

            std::cout << "Alice history1 size is: " << evt.history.size() << std::endl;
            ASSERT_TRUE( evt.hMessageStore == hMessageStore );
            ASSERT_TRUE( evt.history.size() == queryBlockSize );

            cpc::vector< MessageStore::MessageInfo >::const_iterator iter = evt.history.begin();
            int i = 0;
            while( iter != evt.history.end() )
            {
               std::cout << iter->plainTextContent << std::endl;

               ASSERT_FALSE( iter->isEdited );
               ASSERT_FALSE( iter->isDeleted );
               ASSERT_FALSE( iter->isDelivered );
               ASSERT_FALSE( iter->isRead );
               
               if (i == 0) // test one change only
               {
                  mucnmeRetracted.isDelayedDelivery = true;
                  mucnmeRetracted.messageId = iter->uniqueID;
                  mucnmeRetracted.jid = iter->sender.uniqueID.c_str();

                  // Just set all the timestamps the same (but later than the original, just for fun) :)
                  mucnmeRetracted.timestamp = 125666;
                  mucnmeRetracted.millisecond = 678;

                  pInterface->fireOnMultiUserChatNewMessageRetraction( hBogusChat, mucnmeRetracted );
                  std::this_thread::sleep_for( std::chrono::milliseconds( 5 ));
               }

               ++iter;
               i++;
            }
         });
         waitFor( aliceQuery );
      }

      // Now that the data has been filled, try to retrieve ALL the history (again)
      alice.messageStoreManager->queryCachedHistory( hMessageStore, 0 );

      // Wait for the data to be returned
      {
         auto aliceQuery = std::async(std::launch::async, [&] () {
            HistoryEvent evt;
            unsigned int placeHolder;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.messageStoreEvents,
               "MessageStoreHandler::onQueryHistory",
               15000,
               AlwaysTruePred( ),
               placeHolder, evt ) );

            std::cout << "Alice history2 size is: " << evt.history.size() << std::endl;
            ASSERT_TRUE( evt.hMessageStore == hMessageStore );
            ASSERT_TRUE( evt.history.size() == queryBlockSize );

            cpc::vector< MessageStore::MessageInfo >::const_iterator iter = evt.history.begin();
            int i = 0;
            bool found = false;
            while( iter != evt.history.end() )
            {
               std::cout << iter->plainTextContent << std::endl;

               if (mucnmeRetracted.messageId == iter->uniqueID)   // check for our one change
               {
                  ASSERT_TRUE( mucnmeRetracted.jid == iter->sender.uniqueID.c_str());
                  ASSERT_TRUE( mucnmeRetracted.timestamp * 1000 + mucnmeRetracted.millisecond == iter->timestampMillis );

                  ASSERT_TRUE( iter->isDeleted );
               
                  found = true;
               }
               else
               {
                  ASSERT_FALSE( iter->isDeleted );
               }
               ASSERT_FALSE( iter->isEdited );
               ASSERT_FALSE( iter->isDelivered );
               ASSERT_FALSE( iter->isRead );

               ++iter;
               i++;
            }
            ASSERT_TRUE( found );
         });
         waitFor( aliceQuery );
      }

      // Destroy the cache
      alice.messageStoreManager->destroy( hMessageStore );
      {
         auto aliceDestroy = std::async(std::launch::async, [&] () {
            DestroyEvent evt;
            unsigned int placeHolder;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.messageStoreEvents,
               "MessageStoreHandler::onDestroy",
               15000,
               AlwaysTruePred( ),
               placeHolder, evt ) );

            ASSERT_TRUE( evt.hMessageStore == hMessageStore );
            ASSERT_TRUE( evt.result.result == kSuccess );
         });
         waitFor( aliceDestroy );
      }
   }

   TEST_F(MessageStoreModuleTest, MessageDelivered) {
#if _WIN32
      if (TestEnvironmentConfig::dockerContainerized()) GTEST_SKIP(); // UNIT_TEST_WIN_DOCKER_SKIP
#endif

      XmppTestAccount alice("alice", Account_Init);

      // Has to match the settings in cpcapi2_test_fixture
      const cpc::string roomjid( "<EMAIL>" );

      const uint64_t startTimeMillis = millisSinceEpoch();
      const int queryBlockSize( 64 );
      const XmppMultiUserChat::XmppMultiUserChatHandle hBogusChat = 0x8BADF00D;
      XmppMultiUserChat::MultiUserChatNewMessageEvent mucnme;

      // Cast the message store interface to a known class for special
      // hidden access to testing functions
      MessageStore::MessageStoreManagerInterface *pInterface = dynamic_cast< MessageStore::MessageStoreManagerInterface* >( alice.messageStoreManager );
      if( pInterface != NULL )
      {
         // first, signal that a room was created. Just make a bogus room handle for it
         CPCAPI2::XmppAccount::XmppAccountHandle hBogusAccount = 0x1337BEEF;

         XmppMultiUserChat::ServiceAvailabilityEvent sae;
         sae.available = true;
         sae.service = roomjid; // could be different
         pInterface->fireOnServiceAvailability( hBogusAccount, sae );

         XmppMultiUserChat::NewRoomEvent nre;
         nre.hAccount = hBogusAccount;
         nre.roomjid  = roomjid;
         pInterface->fireOnNewRoomHandle( hBogusChat, nre );

         XmppMultiUserChat::MultiUserChatRoomStateChangedEvent mucrsce;
         mucrsce.state.maxHistoryFetch = 64;
         pInterface->fireOnMultiUserChatRoomStateChanged( hBogusChat, mucrsce );

         for( int i = 0, j = 0, k = 0 ; i < queryBlockSize ; ++i )
         {
            mucnme.isDelayedDelivery = true;
            if( s_sillyMessages[ j ] == NULL )
               j = 0;

            if( s_UserIDs[ k ] == NULL )
               k = 0;

            mucnme.plain = s_sillyMessages[ j++ ];
            mucnme.jid = s_UserIDs[ k++ ];
            mucnme.nickname = "Rick";

            // Just set all the timestamps the same :)
            mucnme.timestamp = 123456;
            mucnme.millisecond = 123;

            std::string uniqueid( std::to_string( i ) + "." + std::to_string( j ) + "." + std::to_string( k ));
            mucnme.messageId = uniqueid.c_str();
            pInterface->fireOnMultiUserChatNewMessage( hBogusChat, mucnme );
            std::this_thread::sleep_for( std::chrono::milliseconds( 5 ));
         }
      }

      // Wait for the handle that matches our roomID.
      MessageStoreHandle hMessageStore;
      {
         KreateEvent evt;
         while( evt.roomID != roomjid )
         {
            auto aliceCreate = std::async(std::launch::async, [&] () {
               unsigned int placeHolder;
               ASSERT_TRUE( cpcWaitForEvent(
                  alice.messageStoreEvents,
                  "MessageStoreHandler::onCreate",
                  15000,
                  AlwaysTruePred( ),
                  placeHolder, evt ) );

               hMessageStore = evt.hMessageStore;
            });
            waitFor( aliceCreate );
         }
      }

      // Now that the data has been filled, try to retrieve ALL the history
      alice.messageStoreManager->queryCachedHistory( hMessageStore, 0 );

      XmppMultiUserChat::MessageDeliveredEvent deliveredEvt;

      // Wait for the data to be returned
      {
         auto aliceQuery = std::async(std::launch::async, [&] () {
            HistoryEvent evt;
            unsigned int placeHolder;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.messageStoreEvents,
               "MessageStoreHandler::onQueryHistory",
               15000,
               AlwaysTruePred( ),
               placeHolder, evt ) );

            std::cout << "Alice history1 size is: " << evt.history.size() << std::endl;
            ASSERT_TRUE( evt.hMessageStore == hMessageStore );
            ASSERT_TRUE( evt.history.size() == queryBlockSize );

            cpc::vector< MessageStore::MessageInfo >::const_iterator iter = evt.history.begin();
            int i = 0;
            while( iter != evt.history.end() )
            {
               std::cout << iter->plainTextContent << std::endl;

               ASSERT_FALSE( iter->isEdited );
               ASSERT_FALSE( iter->isDeleted );
               ASSERT_FALSE( iter->isDelivered );
               ASSERT_FALSE( iter->isRead );
               
               if (i == 0) // test one change only
               {
                  deliveredEvt.isDelayedDelivery = true;
                  deliveredEvt.messageId = iter->uniqueID;
                  deliveredEvt.from = iter->sender.uniqueID.c_str();

                  // Just set all the timestamps the same (but later than the original, just for fun) :)
                  deliveredEvt.timestamp = 125666;
                  deliveredEvt.millisecond = 678;

                  pInterface->fireOnMessageDelivered( hBogusChat, deliveredEvt );
                  std::this_thread::sleep_for( std::chrono::milliseconds( 5 ));
               }

               ++iter;
               i++;
            }
         });
         waitFor( aliceQuery );
      }

      // Now that the data has been filled, try to retrieve ALL the history (again)
      alice.messageStoreManager->queryCachedHistory( hMessageStore, 0 );

      // Wait for the data to be returned
      {
         auto aliceQuery = std::async(std::launch::async, [&] () {
            HistoryEvent evt;
            unsigned int placeHolder;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.messageStoreEvents,
               "MessageStoreHandler::onQueryHistory",
               15000,
               AlwaysTruePred( ),
               placeHolder, evt ) );

            std::cout << "Alice history2 size is: " << evt.history.size() << std::endl;
            ASSERT_TRUE( evt.hMessageStore == hMessageStore );
            ASSERT_TRUE( evt.history.size() == queryBlockSize );

            cpc::vector< MessageStore::MessageInfo >::const_iterator iter = evt.history.begin();
            int i = 0;
            bool found = false;
            while( iter != evt.history.end() )
            {
               std::cout << iter->plainTextContent << std::endl;

               if (deliveredEvt.messageId == iter->uniqueID)   // check for our one change
               {
                  ASSERT_TRUE( deliveredEvt.from == iter->sender.uniqueID.c_str());
                  // NOTE: The time of the original message should NOT be changed as a result of the "delivered" change!
                  ASSERT_TRUE( mucnme.timestamp * 1000 + mucnme.millisecond == iter->timestampMillis );

                  ASSERT_TRUE( iter->isDelivered );
               
                  found = true;
               }
               else
               {
                  ASSERT_FALSE( iter->isDelivered );
               }
               ASSERT_FALSE( iter->isEdited );
               ASSERT_FALSE( iter->isDeleted );
               ASSERT_FALSE( iter->isRead );

               ++iter;
               i++;
            }
            ASSERT_TRUE( found );
         });
         waitFor( aliceQuery );
      }

      // Destroy the cache
      alice.messageStoreManager->destroy( hMessageStore );
      {
         auto aliceDestroy = std::async(std::launch::async, [&] () {
            DestroyEvent evt;
            unsigned int placeHolder;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.messageStoreEvents,
               "MessageStoreHandler::onDestroy",
               15000,
               AlwaysTruePred( ),
               placeHolder, evt ) );

            ASSERT_TRUE( evt.hMessageStore == hMessageStore );
            ASSERT_TRUE( evt.result.result == kSuccess );
         });
         waitFor( aliceDestroy );
      }
   }

   TEST_F(MessageStoreModuleTest, MessageRead) {
#if _WIN32
      if (TestEnvironmentConfig::dockerContainerized()) GTEST_SKIP(); // UNIT_TEST_WIN_DOCKER_SKIP
#endif

      XmppTestAccount alice("alice", Account_Init);

      // Has to match the settings in cpcapi2_test_fixture
      const cpc::string roomjid( "<EMAIL>" );

      const uint64_t startTimeMillis = millisSinceEpoch();
      const int queryBlockSize( 64 );
      const XmppMultiUserChat::XmppMultiUserChatHandle hBogusChat = 0x8BADF00D;
      XmppMultiUserChat::MultiUserChatNewMessageEvent mucnme;

      // Cast the message store interface to a known class for special
      // hidden access to testing functions
      MessageStore::MessageStoreManagerInterface *pInterface = dynamic_cast< MessageStore::MessageStoreManagerInterface* >( alice.messageStoreManager );
      if( pInterface != NULL )
      {
         // first, signal that a room was created. Just make a bogus room handle for it
         CPCAPI2::XmppAccount::XmppAccountHandle hBogusAccount = 0x1337BEEF;

         XmppMultiUserChat::ServiceAvailabilityEvent sae;
         sae.available = true;
         sae.service = roomjid; // could be different
         pInterface->fireOnServiceAvailability( hBogusAccount, sae );

         XmppMultiUserChat::NewRoomEvent nre;
         nre.hAccount = hBogusAccount;
         nre.roomjid  = roomjid;
         pInterface->fireOnNewRoomHandle( hBogusChat, nre );

         XmppMultiUserChat::MultiUserChatRoomStateChangedEvent mucrsce;
         mucrsce.state.maxHistoryFetch = 64;
         pInterface->fireOnMultiUserChatRoomStateChanged( hBogusChat, mucrsce );

         for( int i = 0, j = 0, k = 0 ; i < queryBlockSize ; ++i )
         {
            mucnme.isDelayedDelivery = true;
            if( s_sillyMessages[ j ] == NULL )
               j = 0;

            if( s_UserIDs[ k ] == NULL )
               k = 0;

            mucnme.plain = s_sillyMessages[ j++ ];
            mucnme.jid = s_UserIDs[ k++ ];
            mucnme.nickname = "Rick";

            // Just set all the timestamps the same :)
            mucnme.timestamp = 123456;
            mucnme.millisecond = 123;

            std::string uniqueid( std::to_string( i ) + "." + std::to_string( j ) + "." + std::to_string( k ));
            mucnme.messageId = uniqueid.c_str();
            pInterface->fireOnMultiUserChatNewMessage( hBogusChat, mucnme );
            std::this_thread::sleep_for( std::chrono::milliseconds( 5 ));
         }
      }

      // Wait for the handle that matches our roomID.
      MessageStoreHandle hMessageStore;
      {
         KreateEvent evt;
         while( evt.roomID != roomjid )
         {
            auto aliceCreate = std::async(std::launch::async, [&] () {
               unsigned int placeHolder;
               ASSERT_TRUE( cpcWaitForEvent(
                  alice.messageStoreEvents,
                  "MessageStoreHandler::onCreate",
                  15000,
                  AlwaysTruePred( ),
                  placeHolder, evt ) );

               hMessageStore = evt.hMessageStore;
            });
            waitFor( aliceCreate );
         }
      }

      // Now that the data has been filled, try to retrieve ALL the history
      alice.messageStoreManager->queryCachedHistory( hMessageStore, 0 );

      XmppMultiUserChat::MessageReadEvent readEvt;

      // Wait for the data to be returned
      {
         auto aliceQuery = std::async(std::launch::async, [&] () {
            HistoryEvent evt;
            unsigned int placeHolder;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.messageStoreEvents,
               "MessageStoreHandler::onQueryHistory",
               15000,
               AlwaysTruePred( ),
               placeHolder, evt ) );

            std::cout << "Alice history1 size is: " << evt.history.size() << std::endl;
            ASSERT_TRUE( evt.hMessageStore == hMessageStore );
            ASSERT_TRUE( evt.history.size() == queryBlockSize );

            cpc::vector< MessageStore::MessageInfo >::const_iterator iter = evt.history.begin();
            int i = 0;
            while( iter != evt.history.end() )
            {
               std::cout << iter->plainTextContent << std::endl;

               ASSERT_FALSE( iter->isEdited );
               ASSERT_FALSE( iter->isDeleted );
               ASSERT_FALSE( iter->isDelivered );
               ASSERT_FALSE( iter->isRead );
               
               if (i == 0) // test one change only
               {
                  readEvt.isDelayedDelivery = true;
                  readEvt.messageId = iter->uniqueID;
                  readEvt.from = iter->sender.uniqueID.c_str();

                  // Just set all the timestamps the same (but later than the original, just for fun) :)
                  readEvt.timestamp = 125666;
                  readEvt.millisecond = 678;

                  pInterface->fireOnMessageRead( hBogusChat, readEvt );
                  std::this_thread::sleep_for( std::chrono::milliseconds( 5 ));
               }

               ++iter;
               i++;
            }
         });
         waitFor( aliceQuery );
      }

      // Now that the data has been filled, try to retrieve ALL the history (again)
      alice.messageStoreManager->queryCachedHistory( hMessageStore, 0 );

      // Wait for the data to be returned
      {
         auto aliceQuery = std::async(std::launch::async, [&] () {
            HistoryEvent evt;
            unsigned int placeHolder;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.messageStoreEvents,
               "MessageStoreHandler::onQueryHistory",
               15000,
               AlwaysTruePred( ),
               placeHolder, evt ) );

            std::cout << "Alice history2 size is: " << evt.history.size() << std::endl;
            ASSERT_TRUE( evt.hMessageStore == hMessageStore );
            ASSERT_TRUE( evt.history.size() == queryBlockSize );

            cpc::vector< MessageStore::MessageInfo >::const_iterator iter = evt.history.begin();
            int i = 0;
            bool found = false;
            while( iter != evt.history.end() )
            {
               std::cout << iter->plainTextContent << std::endl;

               if (readEvt.messageId == iter->uniqueID)   // check for our one change
               {
                  ASSERT_TRUE( readEvt.from == iter->sender.uniqueID.c_str());
                  // NOTE: The time of the original message should NOT be changed as a result of the "read" change!
                  ASSERT_TRUE( mucnme.timestamp * 1000 + mucnme.millisecond == iter->timestampMillis );

                  ASSERT_FALSE( iter->isEdited );
                  ASSERT_FALSE( iter->isDeleted );
                  ASSERT_FALSE( iter->isDelivered );
                  ASSERT_TRUE( iter->isRead );
               
                  found = true;
               }
               else
               {
                  ASSERT_FALSE( iter->isEdited );
                  ASSERT_FALSE( iter->isDeleted );
                  ASSERT_FALSE( iter->isDelivered );
                  ASSERT_FALSE( iter->isRead );
               }

               ++iter;
               i++;
            }
            ASSERT_TRUE( found );
         });
         waitFor( aliceQuery );
      }

      // Destroy the cache
      alice.messageStoreManager->destroy( hMessageStore );
      {
         auto aliceDestroy = std::async(std::launch::async, [&] () {
            DestroyEvent evt;
            unsigned int placeHolder;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.messageStoreEvents,
               "MessageStoreHandler::onDestroy",
               15000,
               AlwaysTruePred( ),
               placeHolder, evt ) );

            ASSERT_TRUE( evt.hMessageStore == hMessageStore );
            ASSERT_TRUE( evt.result.result == kSuccess );
         });
         waitFor( aliceDestroy );
      }
   }

   TEST_F(MessageStoreModuleTest, MessageReactions) {
#if _WIN32
      if (TestEnvironmentConfig::dockerContainerized()) GTEST_SKIP(); // UNIT_TEST_WIN_DOCKER_SKIP
#endif

      XmppTestAccount alice("alice", Account_Init);

      // Has to match the settings in cpcapi2_test_fixture
      const cpc::string roomjid( "<EMAIL>" );

      const uint64_t startTimeMillis = millisSinceEpoch();
      const int queryBlockSize( 64 );
      const XmppMultiUserChat::XmppMultiUserChatHandle hBogusChat = 0x8BADF00D;
      XmppMultiUserChat::MultiUserChatNewMessageEvent mucnme;

      // Cast the message store interface to a known class for special
      // hidden access to testing functions
      MessageStore::MessageStoreManagerInterface *pInterface = dynamic_cast< MessageStore::MessageStoreManagerInterface* >( alice.messageStoreManager );
      if( pInterface != NULL )
      {
         // first, signal that a room was created. Just make a bogus room handle for it
         CPCAPI2::XmppAccount::XmppAccountHandle hBogusAccount = 0x1337BEEF;

         XmppMultiUserChat::ServiceAvailabilityEvent sae;
         sae.available = true;
         sae.service = roomjid; // could be different
         pInterface->fireOnServiceAvailability( hBogusAccount, sae );

         XmppMultiUserChat::NewRoomEvent nre;
         nre.hAccount = hBogusAccount;
         nre.roomjid  = roomjid;
         pInterface->fireOnNewRoomHandle( hBogusChat, nre );

         XmppMultiUserChat::MultiUserChatRoomStateChangedEvent mucrsce;
         mucrsce.state.maxHistoryFetch = 64;
         pInterface->fireOnMultiUserChatRoomStateChanged( hBogusChat, mucrsce );

         for( int i = 0, j = 0, k = 0 ; i < queryBlockSize ; ++i )
         {
            mucnme.isDelayedDelivery = true;
            if( s_sillyMessages[ j ] == NULL )
               j = 0;

            if( s_UserIDs[ k ] == NULL )
               k = 0;

            mucnme.plain = s_sillyMessages[ j++ ];
            mucnme.jid = s_UserIDs[ k++ ];
            mucnme.nickname = "Rick";

            // Just set all the timestamps the same :)
            mucnme.timestamp = 123456;
            mucnme.millisecond = 123;

            std::string uniqueid( std::to_string( i ) + "." + std::to_string( j ) + "." + std::to_string( k ));
            mucnme.messageId = uniqueid.c_str();
            pInterface->fireOnMultiUserChatNewMessage( hBogusChat, mucnme );
            std::this_thread::sleep_for( std::chrono::milliseconds( 5 ));
         }
      }

      // Wait for the handle that matches our roomID.
      MessageStoreHandle hMessageStore;
      {
         KreateEvent evt;
         while( evt.roomID != roomjid )
         {
            auto aliceCreate = std::async(std::launch::async, [&] () {
               unsigned int placeHolder;
               ASSERT_TRUE( cpcWaitForEvent(
                  alice.messageStoreEvents,
                  "MessageStoreHandler::onCreate",
                  15000,
                  AlwaysTruePred( ),
                  placeHolder, evt ) );

               hMessageStore = evt.hMessageStore;
            });
            waitFor( aliceCreate );
         }
      }

      // Now that the data has been filled, try to get the last message info
      alice.messageStoreManager->queryLastMessageInfo( hMessageStore );

      // Wait for the data to be returned
      {
         auto aliceQuery = std::async(std::launch::async, [&] () {
            LastMessageInfoEvent evt;
            unsigned int placeHolder;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.messageStoreEvents,
               "MessageStoreHandler::onQueryLastMessageInfo",
               15000,
               AlwaysTruePred( ),
               placeHolder, evt ) );

            ASSERT_TRUE( evt.hMessageStore == hMessageStore );
            ASSERT_EQ( evt.result.result, kSuccess );
            ASSERT_FALSE( evt.lastInfo.isEdited );
            ASSERT_FALSE( evt.lastInfo.isDeleted );
            ASSERT_FALSE( evt.lastInfo.isDelivered );
            ASSERT_FALSE( evt.lastInfo.isRead );
            ASSERT_EQ( evt.lastInfo.reactions.size(), 0 );
            ASSERT_TRUE( mucnme.timestamp * 1000 + mucnme.millisecond == evt.lastInfo.timestampMillis );
         });
         waitFor( aliceQuery );
      }

      // Now that the data has been filled, try to retrieve ALL the history
      alice.messageStoreManager->queryCachedHistory( hMessageStore, 0 );

      XmppMultiUserChat::MultiUserChatNewReactionEvent reactionEvt;

      // Wait for the data to be returned
      {
         auto aliceQuery = std::async(std::launch::async, [&] () {
            HistoryEvent evt;
            unsigned int placeHolder;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.messageStoreEvents,
               "MessageStoreHandler::onQueryHistory",
               15000,
               AlwaysTruePred( ),
               placeHolder, evt ) );

            std::cout << "Alice history1 size is: " << evt.history.size() << std::endl;
            ASSERT_TRUE( evt.hMessageStore == hMessageStore );
            ASSERT_TRUE( evt.history.size() == queryBlockSize );

            cpc::vector< MessageStore::MessageInfo >::const_iterator iter = evt.history.begin();
            int i = 0;
            while( iter != evt.history.end() )
            {
               std::cout << iter->plainTextContent << std::endl;

               ASSERT_FALSE( iter->isEdited );
               ASSERT_FALSE( iter->isDeleted );
               ASSERT_FALSE( iter->isDelivered );
               ASSERT_FALSE( iter->isRead );
               
               if (i == 0) // test one change only
               {
                  reactionEvt.isDelayedDelivery = true;
                  reactionEvt.messageId = iter->uniqueID;
                  reactionEvt.jid = iter->sender.uniqueID.c_str();
                  reactionEvt.reactions.push_back("💘");
                  reactionEvt.reactions.push_back("💜");

                  // Just set all the timestamps the same (but later than the original, just for fun) :)
                  reactionEvt.timestamp = 125666;
                  reactionEvt.millisecond = 678;

                  pInterface->fireOnMultiUserChatNewReaction( hBogusChat, reactionEvt );
                  std::this_thread::sleep_for( std::chrono::milliseconds( 5 ));
               }

               ++iter;
               i++;
            }
         });
         waitFor( aliceQuery );
      }

      // Now that the data has been filled, try to retrieve ALL the history (again)
      alice.messageStoreManager->queryCachedHistory( hMessageStore, 0 );

      // Wait for the data to be returned
      {
         auto aliceQuery = std::async(std::launch::async, [&] () {
            HistoryEvent evt;
            unsigned int placeHolder;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.messageStoreEvents,
               "MessageStoreHandler::onQueryHistory",
               15000,
               AlwaysTruePred( ),
               placeHolder, evt ) );

            std::cout << "Alice history2 size is: " << evt.history.size() << std::endl;
            ASSERT_TRUE( evt.hMessageStore == hMessageStore );
            ASSERT_TRUE( evt.history.size() == queryBlockSize );

            cpc::vector< MessageStore::MessageInfo >::const_iterator iter = evt.history.begin();
            int i = 0;
            bool found = false;
            while( iter != evt.history.end() )
            {
               std::cout << iter->plainTextContent << std::endl;

               if (reactionEvt.messageId == iter->uniqueID)   // check for our one change
               {
                  ASSERT_TRUE( reactionEvt.jid == iter->sender.uniqueID.c_str());
                  // NOTE: The time of the original message should NOT be changed as a result of the "reactions" change!
                  ASSERT_TRUE( mucnme.timestamp * 1000 + mucnme.millisecond == iter->timestampMillis );

                  ASSERT_TRUE( iter->reactions.size() == 2);
                  cpc::vector< cpc::string >::const_iterator it = iter->reactions.begin();
                  while( it != iter->reactions.end() )
                  {
                     if (it == iter->reactions.begin())
                        ASSERT_TRUE( *it == "💘" );
                     else
                        ASSERT_TRUE( *it == "💜" );
                     ++it;
                  }

               
                  found = true;
               }
               ASSERT_FALSE( iter->isEdited );  // changing reactions shouldn't mark the message as edited
               ASSERT_FALSE( iter->isDeleted );
               ASSERT_FALSE( iter->isDelivered );
               ASSERT_FALSE( iter->isRead );

               ++iter;
               i++;
            }
            ASSERT_TRUE( found );
         });
         waitFor( aliceQuery );
      }

      // Destroy the cache
      alice.messageStoreManager->destroy( hMessageStore );
      {
         auto aliceDestroy = std::async(std::launch::async, [&] () {
            DestroyEvent evt;
            unsigned int placeHolder;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.messageStoreEvents,
               "MessageStoreHandler::onDestroy",
               15000,
               AlwaysTruePred( ),
               placeHolder, evt ) );

            ASSERT_TRUE( evt.hMessageStore == hMessageStore );
            ASSERT_TRUE( evt.result.result == kSuccess );
         });
         waitFor( aliceDestroy );
      }
   }

   TEST_F(MessageStoreModuleTest, NoChanges) {
#if _WIN32
      if (TestEnvironmentConfig::dockerContainerized()) GTEST_SKIP(); // UNIT_TEST_WIN_DOCKER_SKIP
#endif

      XmppTestAccount alice("alice", Account_Init);

      // Has to match the settings in cpcapi2_test_fixture
      const cpc::string roomjid( "<EMAIL>" );

      const uint64_t startTimeMillis = millisSinceEpoch();
      const int queryBlockSize( 64 );
      const XmppMultiUserChat::XmppMultiUserChatHandle hBogusChat = 0x8BADF00D;
      XmppMultiUserChat::MultiUserChatNewMessageEvent mucnme;

      // Cast the message store interface to a known class for special
      // hidden access to testing functions
      MessageStore::MessageStoreManagerInterface *pInterface = dynamic_cast< MessageStore::MessageStoreManagerInterface* >( alice.messageStoreManager );
      if( pInterface != NULL )
      {
         // first, signal that a room was created. Just make a bogus room handle for it
         CPCAPI2::XmppAccount::XmppAccountHandle hBogusAccount = 0x1337BEEF;

         XmppMultiUserChat::ServiceAvailabilityEvent sae;
         sae.available = true;
         sae.service = roomjid; // could be different
         pInterface->fireOnServiceAvailability( hBogusAccount, sae );

         XmppMultiUserChat::NewRoomEvent nre;
         nre.hAccount = hBogusAccount;
         nre.roomjid  = roomjid;
         pInterface->fireOnNewRoomHandle( hBogusChat, nre );

         XmppMultiUserChat::MultiUserChatRoomStateChangedEvent mucrsce;
         mucrsce.state.maxHistoryFetch = 64;
         pInterface->fireOnMultiUserChatRoomStateChanged( hBogusChat, mucrsce );

         for( int i = 0, j = 0, k = 0 ; i < queryBlockSize ; ++i )
         {
            mucnme.isDelayedDelivery = true;
            if( s_sillyMessages[ j ] == NULL )
               j = 0;

            if( s_UserIDs[ k ] == NULL )
               k = 0;

            mucnme.plain = s_sillyMessages[ j++ ];
            mucnme.jid = s_UserIDs[ k++ ];
            mucnme.nickname = "Rick";

            // Just set all the timestamps the same :)
            mucnme.timestamp = 123456;
            mucnme.millisecond = 123;

            std::string uniqueid( std::to_string( i ) + "." + std::to_string( j ) + "." + std::to_string( k ));
            mucnme.messageId = uniqueid.c_str();
            pInterface->fireOnMultiUserChatNewMessage( hBogusChat, mucnme );
            std::this_thread::sleep_for( std::chrono::milliseconds( 5 ));
         }
      }

      // Wait for the handle that matches our roomID.
      MessageStoreHandle hMessageStore;
      {
         KreateEvent evt;
         while( evt.roomID != roomjid )
         {
            auto aliceCreate = std::async(std::launch::async, [&] () {
               unsigned int placeHolder;
               ASSERT_TRUE( cpcWaitForEvent(
                  alice.messageStoreEvents,
                  "MessageStoreHandler::onCreate",
                  15000,
                  AlwaysTruePred( ),
                  placeHolder, evt ) );

               hMessageStore = evt.hMessageStore;
            });
            waitFor( aliceCreate );
         }
      }

      // Now that the data has been filled, try to retrieve ALL the history
      alice.messageStoreManager->queryCachedHistory( hMessageStore, 0 );

      // Wait for the data to be returned
      {
         auto aliceQuery = std::async(std::launch::async, [&] () {
            HistoryEvent evt;
            unsigned int placeHolder;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.messageStoreEvents,
               "MessageStoreHandler::onQueryHistory",
               15000,
               AlwaysTruePred( ),
               placeHolder, evt ) );

            std::cout << "Alice history1 size is: " << evt.history.size() << std::endl;
            ASSERT_TRUE( evt.hMessageStore == hMessageStore );
            ASSERT_TRUE( evt.history.size() == queryBlockSize );

            cpc::vector< MessageStore::MessageInfo >::const_iterator iter = evt.history.begin();
            int i = 0;
            while( iter != evt.history.end() )
            {
               std::cout << iter->plainTextContent << std::endl;

               if (!iter->roomID.empty()) // DRL FIXIT? Why are we getting empty room ID?
                  ASSERT_STREQ( roomjid, iter->roomID );
               ASSERT_FALSE( iter->isEdited );
               ASSERT_FALSE( iter->isDeleted );
               ASSERT_FALSE( iter->isDelivered );
               ASSERT_FALSE( iter->isRead );
               
               if (i == 0) // test one change only
               {
// re-use the original setting
//                  mucnme.isDelayedDelivery = true;
                  mucnme.plain = iter->plainTextContent;
                  mucnme.jid = iter->recipient.uniqueID;
// re-use the original setting
//                  mucnme.nickname = "Rick";
// re-use the original timestamp
//                  mucnme.timestamp = 123456;
//                  mucnme.millisecond = 123;
                  mucnme.messageId = iter->uniqueID;

                  pInterface->fireOnMultiUserChatNewMessage( hBogusChat, mucnme );
                  std::this_thread::sleep_for( std::chrono::milliseconds( 5 ));
               }

               ++iter;
               i++;
            }
         });
         waitFor( aliceQuery );
      }

      // Now that the data has been filled, try to retrieve ALL the history (again)
      alice.messageStoreManager->queryCachedHistory( hMessageStore, 0 );

      // Wait for the data to be returned
      {
         auto aliceQuery = std::async(std::launch::async, [&] () {
            HistoryEvent evt;
            unsigned int placeHolder;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.messageStoreEvents,
               "MessageStoreHandler::onQueryHistory",
               15000,
               AlwaysTruePred( ),
               placeHolder, evt ) );

            std::cout << "Alice history2 size is: " << evt.history.size() << std::endl;
            ASSERT_TRUE( evt.hMessageStore == hMessageStore );
            ASSERT_TRUE( evt.history.size() == queryBlockSize );

            cpc::vector< MessageStore::MessageInfo >::const_iterator iter = evt.history.begin();
            int i = 0;
            bool found = false;
            while( iter != evt.history.end() )
            {
               std::cout << iter->plainTextContent << std::endl;

               if (mucnme.messageId == iter->uniqueID)   // check for our one change
               {
                  // NOTE: The time of the original message should NOT be changed as a result of a NOOP update!
                  ASSERT_TRUE( mucnme.timestamp * 1000 + mucnme.millisecond == iter->timestampMillis );
                  // NOTE: The isEdited state should not change for a NOOP update!
                  ASSERT_FALSE( iter->isEdited );
                  found = true;
               }
               else
                  ASSERT_FALSE( iter->isEdited );
               ASSERT_FALSE( iter->isDeleted );
               ASSERT_FALSE( iter->isDelivered );
               ASSERT_FALSE( iter->isRead );

               ++iter;
               i++;
            }
            ASSERT_TRUE( found );
         });
         waitFor( aliceQuery );
      }

      // Destroy the cache
      alice.messageStoreManager->destroy( hMessageStore );
      {
         auto aliceDestroy = std::async(std::launch::async, [&] () {
            DestroyEvent evt;
            unsigned int placeHolder;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.messageStoreEvents,
               "MessageStoreHandler::onDestroy",
               15000,
               AlwaysTruePred( ),
               placeHolder, evt ) );

            ASSERT_TRUE( evt.hMessageStore == hMessageStore );
            ASSERT_TRUE( evt.result.result == kSuccess );
         });
         waitFor( aliceDestroy );
      }
   }

   TEST_F(MessageStoreModuleTest, QueryByID) {
#if _WIN32
      if (TestEnvironmentConfig::dockerContainerized()) GTEST_SKIP(); // UNIT_TEST_WIN_DOCKER_SKIP
#endif

      XmppTestAccount alice("alice", Account_Init);

      // Has to match the settings in cpcapi2_test_fixture
      const cpc::string roomjid( "<EMAIL>" );

      const uint64_t startTimeMillis = millisSinceEpoch();
      const int queryBlockSize( 50 );

      // Cast the message store interface to a known class for special
      // hidden access to testing functions
      MessageStore::MessageStoreManagerInterface *pInterface = dynamic_cast< MessageStore::MessageStoreManagerInterface* >( alice.messageStoreManager );
      if( pInterface != NULL )
      {
         // first, signal that a room was created. Just make a bogus room handle for it
         CPCAPI2::XmppAccount::XmppAccountHandle hBogusAccount = 0x1337BEEF;
         XmppMultiUserChat::XmppMultiUserChatHandle hBogusChat = 0x8BADF00D;

         XmppMultiUserChat::ServiceAvailabilityEvent sae;
         sae.available = true;
         sae.service = roomjid; // could be different
         pInterface->fireOnServiceAvailability( hBogusAccount, sae );

         XmppMultiUserChat::NewRoomEvent nre;
         nre.hAccount = hBogusAccount;
         nre.roomjid  = roomjid;
         pInterface->fireOnNewRoomHandle( hBogusChat, nre );

         XmppMultiUserChat::MultiUserChatRoomStateChangedEvent mucrsce;
         mucrsce.state.maxHistoryFetch = 50;
         pInterface->fireOnMultiUserChatRoomStateChanged( hBogusChat, mucrsce );

         XmppMultiUserChat::MultiUserChatNewMessageEvent mucnme;
         for( int i = 0, j = 0, k = 0 ; i < queryBlockSize ; ++i )
         {
            mucnme.isDelayedDelivery = true;
            if( s_sillyMessages[ j ] == NULL )
               j = 0;

            if( s_UserIDs[ k ] == NULL )
               k = 0;

            std::string uniqueid( std::to_string( i ) + "." + std::to_string( j ) + "." + std::to_string( k ));
            mucnme.messageId = uniqueid.c_str();
            mucnme.plain = s_sillyMessages[ j++ ];
            mucnme.jid = s_UserIDs[ k++ ];
            mucnme.nickname = "Rick";

            uint64_t stampMillis = millisSinceEpoch();
            mucnme.timestamp = stampMillis / 1000; // convert to seconds
            mucnme.millisecond = stampMillis % 1000;

            pInterface->fireOnMultiUserChatNewMessage( hBogusChat, mucnme );
            std::this_thread::sleep_for( std::chrono::milliseconds( 5 ));
         }
      }

      // Wait for the handle that matches our roomID.
      MessageStoreHandle hMessageStore;
      {
         KreateEvent evt;
         while( evt.roomID != roomjid )
         {
            auto aliceCreate = std::async(std::launch::async, [&] () {
               unsigned int placeHolder;
               ASSERT_TRUE( cpcWaitForEvent(
                  alice.messageStoreEvents,
                  "MessageStoreHandler::onCreate",
                  15000,
                  AlwaysTruePred( ),
                  placeHolder, evt ) );

               hMessageStore = evt.hMessageStore;
            });
            waitFor( aliceCreate );
         }
      }

      // Now that the data has been filled, try to retrieve ALL the history
      alice.messageStoreManager->queryMessageInfo( hMessageStore, "0.0.0" );

      // Wait for the data to be returned
      {
         auto aliceQuery = std::async(std::launch::async, [&] () {
            MessageInfoEvent evt;
            unsigned int placeHolder;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.messageStoreEvents,
               "MessageStoreHandler::onQueryMessageInfo",
               15000,
               AlwaysTruePred( ),
               placeHolder, evt ) );

            std::cout << "Alice message is: " << evt.info.plainTextContent << std::endl;
            ASSERT_TRUE( evt.hMessageStore == hMessageStore );
         });
         waitFor( aliceQuery );
      }

      // Destroy the cache
      alice.messageStoreManager->destroy( hMessageStore );
      {
         auto aliceDestroy = std::async(std::launch::async, [&] () {
            DestroyEvent evt;
            unsigned int placeHolder;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.messageStoreEvents,
               "MessageStoreHandler::onDestroy",
               15000,
               AlwaysTruePred( ),
               placeHolder, evt ) );

            ASSERT_TRUE( evt.hMessageStore == hMessageStore );
            ASSERT_TRUE( evt.result.result == kSuccess );
         });
         waitFor( aliceDestroy );
      }
   }

   TEST_F(MessageStoreModuleTest, Adhoc) {
#if _WIN32
      if (TestEnvironmentConfig::dockerContainerized()) GTEST_SKIP(); // UNIT_TEST_WIN_DOCKER_SKIP
#endif

      cleanupDir(); // This test is sensitive to old files lying around

      XmppTestAccount alice("alice", Account_Init);

      // Has to match the settings in cpcapi2_test_fixture
      const cpc::string roomjid( "<EMAIL>" );

      const uint64_t startTimeMillis = millisSinceEpoch();
      const int queryBlockSize( 50 );

      // Cast the message store interface to a known class for special
      // hidden access to testing functions
      MessageStore::MessageStoreManagerInterface *pInterface = dynamic_cast< MessageStore::MessageStoreManagerInterface* >( alice.messageStoreManager );
      if( pInterface != NULL )
      {
         // first, signal that a room was created. Just make a bogus room handle for it
         CPCAPI2::XmppAccount::XmppAccountHandle hBogusAccount = 0x1337BEEF;
         XmppMultiUserChat::XmppMultiUserChatHandle hBogusChat = 0x8BADF00D;

         MessageStore::Settings msSettings;
         msSettings.ignoreNonPersistentMUC = true;
         pInterface->applySettings( msSettings );

         XmppMultiUserChat::ServiceAvailabilityEvent sae;
         sae.available = true;
         sae.service = roomjid; // could be different
         pInterface->fireOnServiceAvailability( hBogusAccount, sae );

         XmppMultiUserChat::NewRoomEvent nre;
         nre.hAccount = hBogusAccount;
         nre.roomjid  = roomjid;
         pInterface->fireOnNewRoomHandle( hBogusChat, nre );

         XmppMultiUserChat::MultiUserChatRoomStateChangedEvent mucrsce;
         mucrsce.state.maxHistoryFetch = 50;
         mucrsce.state.isPersistent = true;
         pInterface->fireOnMultiUserChatRoomStateChanged( hBogusChat, mucrsce );

         XmppMultiUserChat::MultiUserChatNewMessageEvent mucnme;
         for( int i = 0, j = 0, k = 0 ; i < queryBlockSize ; ++i )
         {
            mucnme.isDelayedDelivery = true;
            if( s_sillyMessages[ j ] == NULL )
               j = 0;

            if( s_UserIDs[ k ] == NULL )
               k = 0;

            mucnme.plain = s_sillyMessages[ j++ ];
            mucnme.jid = s_UserIDs[ k++ ];
            mucnme.nickname = "Rick";

            uint64_t stampMillis = millisSinceEpoch();
            mucnme.timestamp = stampMillis / 1000; // convert to seconds
            mucnme.millisecond = stampMillis % 1000;

            std::string uniqueid( std::to_string( i ) + "." + std::to_string( j ) + "." + std::to_string( k ));
            mucnme.messageId = uniqueid.c_str();
            pInterface->fireOnMultiUserChatNewMessage( hBogusChat, mucnme );
            std::this_thread::sleep_for( std::chrono::milliseconds( 5 ));
         }
      }

      // Wait for the handle that matches our roomID. But, we shouldn't get it.
      MessageStoreHandle hMessageStore;
      {
         KreateEvent evt;
         auto aliceCreate = std::async(std::launch::async, [&] () {
            unsigned int placeHolder;
            ASSERT_FALSE( cpcWaitForEvent(
               alice.messageStoreEvents,
               "MessageStoreHandler::onCreate",
               3000,
               AlwaysTruePred( ),
               placeHolder, evt ) );

            hMessageStore = evt.hMessageStore;
         });
      }
   }

   TEST_F(MessageStoreModuleTest, PartialQuery) {
#if _WIN32
      if (TestEnvironmentConfig::dockerContainerized()) GTEST_SKIP(); // UNIT_TEST_WIN_DOCKER_SKIP
#endif

      XmppTestAccount alice("alice", Account_Init);

      // Has to match the settings in cpcapi2_test_fixture
      const cpc::string roomjid( "<EMAIL>" );

      const uint64_t startTimeMillis = millisSinceEpoch();
      const int blockSize( 50 );

      // Cast the message store interface to a known class for special
      // hidden access to testing functions
      MessageStore::MessageStoreManagerInterface *pInterface = dynamic_cast< MessageStore::MessageStoreManagerInterface* >( alice.messageStoreManager );
      if( pInterface != NULL )
      {
         // first, signal that a room was created. Just make a bogus room handle for it
         CPCAPI2::XmppAccount::XmppAccountHandle hBogusAccount = 0x1337BEEF;
         XmppMultiUserChat::XmppMultiUserChatHandle hBogusChat = 0x8BADF00D;

         XmppMultiUserChat::ServiceAvailabilityEvent sae;
         sae.available = true;
         sae.service = roomjid; // could be different
         pInterface->fireOnServiceAvailability( hBogusAccount, sae );

         XmppMultiUserChat::NewRoomEvent nre;
         nre.hAccount = hBogusAccount;
         nre.roomjid  = roomjid;
         pInterface->fireOnNewRoomHandle( hBogusChat, nre );

         XmppMultiUserChat::MultiUserChatRoomStateChangedEvent mucrsce;
         mucrsce.state.maxHistoryFetch = 50;
         pInterface->fireOnMultiUserChatRoomStateChanged( hBogusChat, mucrsce );

         XmppMultiUserChat::MultiUserChatNewMessageEvent mucnme;
         for( int i = 0, j = 0, k = 0 ; i < blockSize ; ++i )
         {
            mucnme.isDelayedDelivery = true;
            if( s_sillyMessages[ j ] == NULL )
               j = 0;

            if( s_UserIDs[ k ] == NULL )
               k = 0;

            mucnme.plain = s_sillyMessages[ j++ ];
            mucnme.jid = s_UserIDs[ k++ ];

            uint64_t stampMillis = millisSinceEpoch();
            mucnme.timestamp = stampMillis / 1000; // convert to seconds
            mucnme.millisecond = stampMillis % 1000;

            std::string uniqueid( std::to_string( i ) + "." + std::to_string( j ) + "." + std::to_string( k ));
            mucnme.messageId = uniqueid.c_str();
            pInterface->fireOnMultiUserChatNewMessage( hBogusChat, mucnme );
            std::this_thread::sleep_for( std::chrono::milliseconds( 5 ));
         }
      }

      // Wait for the handle that matches our roomID.
      MessageStoreHandle hMessageStore;
      {
         KreateEvent evt;
         while( evt.roomID != roomjid )
         {
            auto aliceCreate = std::async(std::launch::async, [&] () {
               unsigned int placeHolder;
               ASSERT_TRUE( cpcWaitForEvent(
                  alice.messageStoreEvents,
                  "MessageStoreHandler::onCreate",
                  15000,
                  AlwaysTruePred( ),
                  placeHolder, evt ) );

               hMessageStore = evt.hMessageStore;
            });
            waitFor( aliceCreate );
         }
      }

      // Now that the data has been filled, try to retrieve the history.
      // This is done by cutting half the time from the original start time
      // until now.
      uint64_t nowMillis( millisSinceEpoch() );
      uint64_t cutoffMillis( startTimeMillis + (( nowMillis - startTimeMillis ) >> 1 ));
      alice.messageStoreManager->queryCachedHistory( hMessageStore, cutoffMillis );

      // Wait for the data to be returned
      {
         auto aliceQuery = std::async(std::launch::async, [&] () {
            HistoryEvent evt;
            unsigned int placeHolder;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.messageStoreEvents,
               "MessageStoreHandler::onQueryHistory",
               15000,
               AlwaysTruePred( ),
               placeHolder, evt ) );

            ASSERT_TRUE( evt.hMessageStore == hMessageStore );
            std::cout << "Alice history size is: " << evt.history.size() << std::endl;
         });
         waitFor( aliceQuery );
      }

      // Destroy the cache
      alice.messageStoreManager->destroy( hMessageStore );
      {
         auto aliceDestroy = std::async(std::launch::async, [&] () {
            DestroyEvent evt;
            unsigned int placeHolder;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.messageStoreEvents,
               "MessageStoreHandler::onDestroy",
               15000,
               AlwaysTruePred( ),
               placeHolder, evt ) );

            ASSERT_TRUE( evt.hMessageStore == hMessageStore );
            ASSERT_TRUE( evt.result.result == kSuccess );
         });
         waitFor( aliceDestroy );
      }
   }



   TEST_F(MessageStoreModuleTest, GapDetect) {
#if _WIN32
      if (TestEnvironmentConfig::dockerContainerized()) GTEST_SKIP(); // UNIT_TEST_WIN_DOCKER_SKIP
#endif

      XmppTestAccount alice("alice", Account_Init);

      // Has to match the settings in cpcapi2_test_fixture
      const cpc::string roomjid( "<EMAIL>" );

      const uint64_t startTimeMillis = millisSinceEpoch();
      uint64_t startAfterGapMillis;
      const int blockSize( 50 );

      // Cast the message store interface to a known class for special
      // hidden access to testing functions
      MessageStore::MessageStoreManagerInterface *pInterface = dynamic_cast< MessageStore::MessageStoreManagerInterface* >( alice.messageStoreManager );
      if( pInterface != NULL )
      {
         // first, signal that a room was created. Just make a bogus room handle for it
         CPCAPI2::XmppAccount::XmppAccountHandle hBogusAccount = 0x1337BEEF;
         XmppMultiUserChat::XmppMultiUserChatHandle hBogusChat = 0x8BADF00D;

         XmppMultiUserChat::ServiceAvailabilityEvent sae;
         sae.available = true;
         sae.service = roomjid; // could be different
         pInterface->fireOnServiceAvailability( hBogusAccount, sae );

         XmppMultiUserChat::NewRoomEvent nre;
         nre.hAccount = hBogusAccount;
         nre.roomjid  = roomjid;
         pInterface->fireOnNewRoomHandle( hBogusChat, nre );

         XmppMultiUserChat::MultiUserChatRoomStateChangedEvent mucrsce;
         mucrsce.state.maxHistoryFetch = 50;
         pInterface->fireOnMultiUserChatRoomStateChanged( hBogusChat, mucrsce );

         // Fill the room with blockSize + blocksize/2 history (after a gap)
         XmppMultiUserChat::MultiUserChatNewMessageEvent mucnme;
         for( int i = 0, j = 0, k = 0 ; i < ( blockSize >> 1 ) ; ++i )
         {
            mucnme.isDelayedDelivery = true;
            if( s_sillyMessages[ j ] == NULL )
               j = 0;

            if( s_UserIDs[ k ] == NULL )
               k = 0;

            mucnme.plain = s_sillyMessages[ j++ ];
            mucnme.jid = s_UserIDs[ k++ ];

            uint64_t stampMillis = millisSinceEpoch();
            mucnme.timestamp = stampMillis / 1000; // convert to seconds
            mucnme.millisecond = stampMillis % 1000;

            std::string uniqueid( "1." + std::to_string( i ) + "." + std::to_string( j ) + "." + std::to_string( k ));
            mucnme.messageId = uniqueid.c_str();
            pInterface->fireOnMultiUserChatNewMessage( hBogusChat, mucnme );
            std::this_thread::sleep_for( std::chrono::milliseconds( 5 ));
         }

         // "the gap"
         std::this_thread::sleep_for( std::chrono::milliseconds( 100 ));
         startAfterGapMillis = millisSinceEpoch();

         // "the rest"
         for( int i = 0, j = 0, k = 0 ; i < blockSize ; ++i )
         {
            mucnme.isDelayedDelivery = true;
            if( s_sillyMessages[ j ] == NULL )
               j = 0;

            if( s_UserIDs[ k ] == NULL )
               k = 0;

            mucnme.plain = s_sillyMessages[ j++ ];
            mucnme.jid = s_UserIDs[ k++ ];

            uint64_t stampMillis = millisSinceEpoch();
            mucnme.timestamp = stampMillis / 1000; // convert to seconds
            mucnme.millisecond = stampMillis % 1000;

            std::string uniqueid( "2." + std::to_string( i ) + "." + std::to_string( j ) + "." + std::to_string( k ));
            mucnme.messageId = uniqueid.c_str();
            pInterface->fireOnMultiUserChatNewMessage( hBogusChat, mucnme );
            std::this_thread::sleep_for( std::chrono::milliseconds( 5 ));
         }
      }

      // Wait for the handle that matches our roomID.
      MessageStoreHandle hMessageStore;
      {
         KreateEvent evt;
         while( evt.roomID != roomjid )
         {
            auto aliceCreate = std::async(std::launch::async, [&] () {
               unsigned int placeHolder;
               ASSERT_TRUE( cpcWaitForEvent(
                  alice.messageStoreEvents,
                  "MessageStoreHandler::onCreate",
                  15000,
                  AlwaysTruePred( ),
                  placeHolder, evt ) );

               hMessageStore = evt.hMessageStore;
            });
            waitFor( aliceCreate );
         }
      }

      // Now that the data has been filled, try to retrieve the history.
      // query after the 'gap' point.
      alice.messageStoreManager->queryCachedHistory( hMessageStore, startAfterGapMillis );

      // Wait for the data to be returned
      {
         auto aliceQuery = std::async(std::launch::async, [&] () {
            HistoryEvent evt;
            unsigned int placeHolder;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.messageStoreEvents,
               "MessageStoreHandler::onQueryHistory",
               15000,
               AlwaysTruePred( ),
               placeHolder, evt ) );

            ASSERT_TRUE( evt.hMessageStore == hMessageStore );
            std::cout << "Alice history size is: " << evt.history.size() << std::endl;
         });
         waitFor( aliceQuery );
      }

      // Destroy the cache
      alice.messageStoreManager->destroy( hMessageStore );
      {
         auto aliceDestroy = std::async(std::launch::async, [&] () {
            DestroyEvent evt;
            unsigned int placeHolder;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.messageStoreEvents,
               "MessageStoreHandler::onDestroy",
               15000,
               AlwaysTruePred( ),
               placeHolder, evt ) );

            ASSERT_TRUE( evt.hMessageStore == hMessageStore );
            ASSERT_TRUE( evt.result.result == kSuccess );
         });
         waitFor( aliceDestroy );
      }
   }

   TEST_F(MessageStoreModuleTest, DISABLED_RealXMPPServer) {

      const cpc::string roomName = "small";
      const cpc::string roomDomain = "imap.mobilevoiplive.com";

      // Has to match the settings in cpcapi2_test_fixture
      cpc::string roomjid;

      XmppTestAccount alice( "alice", Account_NoInit );
      alice.config.settings.domain = roomDomain;
      alice.config.settings.port = 5224;
      alice.config.settings.username = "vccs1001";
      alice.config.settings.password = "BD7RgrtSKaYr";
      alice.config.settings.proxy = "***********";
      alice.init();
      alice.enable();

      MessageStoreHandle hMessageStore;

      {
         XmppAccountHandle h;
         ServiceAvailabilityEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(h, alice.handle);
         ASSERT_TRUE(evt.available);
         ASSERT_TRUE(!evt.service.empty());
      }

      alice.mucManager->getRoomList(alice.handle);

      {
         XmppAccountHandle h;
         RoomListRetrievedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onRoomListRetrieved", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(h, alice.handle);

         for (cpc::vector<RoomListItem>::const_iterator it = evt.rooms.begin(); it != evt.rooms.end(); ++it)
         {
            if (it->name == roomName)
            {
               roomjid = it->jid;
               XmppMultiUserChatHandle h = alice.mucManager->create( alice.handle, it->jid );
               {
                  alice.mucManager->getRoomInfo(h);

                  MultiUserChatRoomStateChangedEvent roomStateChangedEvt;
                  ASSERT_TRUE(cpcWaitForEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatRoomStateChanged", 5000, CPCAPI2::test::AlwaysTruePred(), h, roomStateChangedEvt));

                  roomStateChangedEvt = roomStateChangedEvt;
               }

               RoomConfig rc;
               rc.createIfNotExisting = false;
               rc.isInstant = false;

               // join the room to initiate a query of the history.
               // (events will continue to come in slowly)
               alice.mucManager->join( h, rc, roomName, alice.config.settings.password, "since:0");
               {
                  XmppMultiUserChatHandle h;
                  MultiUserChatReadyEvent evt;
                  ASSERT_TRUE(cpcWaitForEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
                  //ASSERT_EQ(aliceChat, h);
                  ASSERT_TRUE(!evt.roomjid.empty());
                  ASSERT_TRUE(!evt.isNewRoom);

                  std::this_thread::sleep_for( std::chrono::seconds( 10 ));
                  //ASSERT_TRUE(cpcWaitForEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatSubjectChanged", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
               }

               break;
            }
         }
      }

      // Wait for the handle that matches our roomID.
      {
         KreateEvent evt;
         while( evt.roomID != roomjid )
         {
            auto aliceCreate = std::async(std::launch::async, [&] () {
               unsigned int placeHolder;
               ASSERT_TRUE( cpcWaitForEvent(
                  alice.messageStoreEvents,
                  "MessageStoreHandler::onCreate",
                  15000,
                  AlwaysTruePred( ),
                  placeHolder, evt ) );

               hMessageStore = evt.hMessageStore;
            });
            waitFor( aliceCreate );
         }
      }

      // Wait for the history loading to finish
      {
         HistoryLoadedEvent evt;
         auto aliceHistoryLoad = std::async(std::launch::async, [&] () {
            unsigned int placeHolder;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.messageStoreEvents,
               "MessageStoreHandler::onHistoryLoaded",
               15000,
               AlwaysTruePred( ),
               placeHolder, evt ) );

            hMessageStore = evt.hMessageStore;
         });
         waitFor( aliceHistoryLoad );
      }

      // try to retrieve the history.
      alice.messageStoreManager->queryCachedHistory( hMessageStore, 0 );

      // Wait for the data to be returned
      {
         auto aliceQuery = std::async(std::launch::async, [&] () {
            HistoryEvent evt;
            MessageStoreHandle h;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.messageStoreEvents,
               "MessageStoreHandler::onQueryHistory",
               15000,
               AlwaysTruePred( ),
               h, evt ) );

            ASSERT_TRUE( evt.hMessageStore == hMessageStore );
            ASSERT_TRUE( evt.history.size() != 0 );
            std::cout << "Alice history size is: " << evt.history.size() << std::endl;
         });
         waitFor( aliceQuery );
      }

      // Destroy the cache
      alice.messageStoreManager->destroy( hMessageStore );
      {
         auto aliceDestroy = std::async(std::launch::async, [&] () {
            DestroyEvent evt;
            MessageStoreHandle h;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.messageStoreEvents,
               "MessageStoreHandler::onDestroy",
               15000,
               AlwaysTruePred( ),
               h, evt ) );

            ASSERT_TRUE( evt.hMessageStore == hMessageStore );
            ASSERT_TRUE( evt.result.result == kSuccess );
         });
         waitFor( aliceDestroy );
      }
   }
}  // namespace

#endif // (CPCAPI2_BRAND_REMOTE_SYNC_MODULE == 1)
