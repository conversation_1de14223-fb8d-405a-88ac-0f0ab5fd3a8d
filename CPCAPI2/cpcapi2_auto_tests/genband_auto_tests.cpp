#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#ifdef CPCAPI2_GENBAND_MODULE

#include "cpcapi2_test_fixture.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>

#include "test_framework/cpcapi2_test_framework.h"
#include "test_framework/bare_bones_sip_endpoint.h"

#include <thread>
#include <future>

using namespace CPCAPI2;
using namespace CPCAPI2::Genband;
using namespace CPCAPI2::test;

namespace {

class GenbandModuleTest : public CpcapiAutoTest
{
public:
   GenbandModuleTest() {}
   virtual ~GenbandModuleTest() {}
};

// 
TEST_F(GenbandModuleTest, GenbandAPITest) {
	TestAccount cpath1001("cpath1001");

	GenbandHeader head;
	head.UserAgent = "Unspecified";
	head.Authorization = "Basic";
	head.Username = "<EMAIL>";
	head.Domain = "cpath.developer.genband.com";
	head.Password = "abcd1234";
	GenbandBody body;	
	body.Url = "https://ottproxy02.genband.com:8444/rest/version/1/user/<EMAIL>/callcontrol";
	//body.Body = "{\"callControlRequest\":{\"type\":\"callStart\",\"from\":\"sip:<EMAIL>\",\"to\":\"sip:<EMAIL>\",\"sdp\":\"v=0\r\no=cpath1001 ********* 0 IN IP4 ************\r\ns=sip call\r\nc=IN IP4 ************\r\nt=0 0\r\nm=audio 17254 RTP/AVP 0 8 18 101\r\na=ptime:20\r\na=fmtp:18 annexb=no\r\na=rtpmap:101 telephone-event/8000\r\na=fmtp:101 0-15\r\n\"}}";
	body.Body = "{\"callControlRequest\":{\"type\":callStart,\"from\":<EMAIL>,\"to\":<EMAIL>,\"sdp\":v=0\r\no=cpath1001 ********** 0 IN IP4 ************\r\ns=sip call\r\nc=IN IP4 ************\r\nt=0 0\r\nm=audio 17254 RTP/AVP 0 8 18 101\r\na=ptime:20\r\na=fmtp:18 annexb=no\r\na=rtpmap:101 telephone-event/8000\r\na=fmtp:101 0-15\r\n}}";

	GenbandResult result = cpath1001.genbandManager->makeCall(head, body);

	ASSERT_EQ(result.ResultCode, (long)200);
	//ASSERT_EQ(1, 1);
}

// 
TEST_F(GenbandModuleTest, GenbandAPITestAnswer) {
	TestAccount cpath1001("cpath1001");

	GenbandHeader head;
	head.UserAgent = "Unspecified";
	head.Authorization = "Basic";
	head.Username = "cpath1001";
	head.Domain = "cpath.developer.genband.com";
	head.Password = "abcd1234";
	GenbandBody body;	
	body.Url = "https://ottproxy02.genband.com:8444/rest/version/1/user/<EMAIL>/callcontrol";
	body.Body = "{\"callControlRequest\":{\"type\":\"callStart\",\"from\":\"sip:<EMAIL>\",\"to\":\"sip:<EMAIL>\",\"sdp\":\"v=0\r\no=cpath1001 ********** 0 IN IP4 ************\r\ns=sip call\r\ne=<EMAIL>\r\nc=IN IP4 *************\r\nt=0 0\r\nm=audio 17254 RTP/AVP 0 8 18 101\r\na=ptime:20\r\na=fmtp:18 annexb=no\r\na=rtpmap:101 telephone-event/8000\r\na=fmtp:101 0-15\r\n\"}}";

	GenbandResult result = cpath1001.genbandManager->answerCall(head, body);

	ASSERT_EQ(result.ResultCode, (long)200);
	//ASSERT_EQ(1, 1);
}

} // namespace
#endif