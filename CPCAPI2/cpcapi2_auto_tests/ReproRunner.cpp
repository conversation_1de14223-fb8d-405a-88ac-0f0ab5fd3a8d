#include "ReproRunner.h"

#include "cpcapi2_test_fixture.h"

void CPCAPI2::ReproRunner::restart()
{
   mCipherList = resip::BaseSecurity::DefaultCipherSuite;
   this->restart((TestEnvironmentConfig::testResourcePath() + "repro.config").c_str());
}

void CPCAPI2::ReproRunner::restart(resip::Data configFile)
{
   mCipherList = resip::BaseSecurity::DefaultCipherSuite;
   mDHParamsFile = (TestEnvironmentConfig::testResourcePath() + "dh2048.pem");
   repro::ReproRunner::restart(configFile);
}

void CPCAPI2::ReproRunner::restart(resip::Data configFile, resip::BaseSecurity::CipherList cipherList)
{
   mCipherList = cipherList;
   mDHParamsFile = (TestEnvironmentConfig::testResourcePath() + "dh2048.pem");
   repro::ReproRunner::restart(configFile);
}
