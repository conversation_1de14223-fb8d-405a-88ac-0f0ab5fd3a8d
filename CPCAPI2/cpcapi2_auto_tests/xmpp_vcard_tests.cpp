#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#include "test_framework/xmpp_test_helper.h"
#include "xmpp/XmppAccountInterface.h"
#include "xmpp/XmppAccountJsonApi.h"
#include "xmpp/XmppVCardJsonApi.h"
#include "../../impl/auth_server/AuthServerJwtUtils.h"

#include <fstream>
#include <sstream>
#include <iostream>
#include <vector>

#include <sha.h>

#ifndef ANDROID
//define USE_CPCAPI2_JSON_TESTS 1
#endif

#if (CPCAPI2_BRAND_XMPP_VCARD_MODULE == 1)

#if defined(__GNUC__) || defined(__clang__)
#pragma GCC diagnostic ignored "-Wdeprecated-declarations"
#endif

using namespace CPCAPI2;
using namespace CPCAPI2::XmppAccount;
using namespace CPCAPI2::XmppVCard;
using namespace CPCAPI2::XmppRoster;
using namespace CPCAPI2::Json<PERSON><PERSON>;
using namespace CPCAPI2::CloudConnector;

namespace
{

class XmppVCardModuleTest : public CpcapiAutoTest
{

public:

   XmppVCardModuleTest() {}
   virtual ~XmppVCardModuleTest() {}
   static void SetUpTestCase() {}
   static void TearDownTestCase() {}
   virtual void TearDown() {}

   virtual void SetUp()
   {
      if (!TestEnvironmentConfig::autoCreateXmppUsers())
      {
         auto initialization = [&](XmppTestAccount& account)
         {
            CPCAPI2::XmppVCard::XmppVCardDetail vcard;
            resetVCard(account, vcard);
            
            assertXmppRosterUpdate(account, [&](const XmppRosterUpdateEvent& evt)
            {
               ASSERT_TRUE(evt.fullUpdate);
                                      
               safeCout("XmppVCardModuleTest::Setup(): account name: " << account.config.name << " account jid: " << account.config.bare() << " added: " << evt.added.size() << " removed: " << evt.removed.size() << " updated: " << evt.updated.size());
                                      
               for (const auto& item : evt.added)
               {
                  account.roster->removeRosterItem(account.rosterHandle, item.item.address);
               }
               });
            
            while (true)
            {
               XmppRosterHandle h;
               XmppRosterSubscriptionRequestEvent evt;
               if (!account.events->expectEvent(__LINE__, "XmppRosterHandler::onSubscriptionRequest", 1000, CPCAPI2::test::AlwaysTruePred(), h, evt)) break;
               
               account.roster->rejectSubscriptionRequest(account.rosterHandle, evt.address);
            };
            
            auto resetEvent = std::async(std::launch::async, [&]()
            {
               account.vcardManager->storeVCard(account.vcardHandle, vcard);
               assertXmppVCardOperationResult(account, [&](const VCardOperationResultEvent& evt)
               {
                  ASSERT_EQ(account.handle, evt.account);
                  ASSERT_EQ(account.vcardHandle, evt.handle);
                  ASSERT_EQ(0, evt.result);
                  ASSERT_EQ(1, evt.type);
                  });
               });
            
            waitFor(resetEvent);
         };
         
         
         // clear out rosters for test accounts
         const char* xmppUsersString = getenv("CPCAPI2_XMPP_USERS");
         if (xmppUsersString == NULL)
         {
            xmppUsersString = DefaultXmppUserList;
         }
         
         static std::vector<XmppTestUserInfo> xmppUsers;
         xmppUsers = split<XmppTestUserInfo>(xmppUsersString, ',');
         
         for (std::vector<XmppTestUserInfo>::iterator i = xmppUsers.begin(); i != xmppUsers.end(); i++)
         {
            XmppTestAccount account((*i).username, Account_NoInit, (*i).username);
            account.config.settings.enableXmppPresence = false; // Block presence triggered auto-fetched vcard events
            account.enable();
            
            auto accountEvent = std::async(std::launch::async, [&]()
            {
               initialization(account);
            });
            
            waitFor(accountEvent);
            
            std::this_thread::sleep_for(std::chrono::milliseconds(1000));
         }
      }
   }
   static void resetVCard(XmppTestAccount& account, CPCAPI2::XmppVCard::XmppVCardDetail& vcard);
   static void setVCard(XmppTestAccount& account, CPCAPI2::XmppVCard::XmppVCardDetail& vcard);
   static void expectXmppVCardFetchMatch(int line, XmppTestAccount& account, cpc::string& jid, CPCAPI2::XmppVCard::XmppVCardDetail& vcard, std::function<void(CPCAPI2::XmppVCard::VCardFetchedEvent& evt)> validator);
   static void assertVCardAttributes(int line, XmppTestAccount& account, cpc::string& jid, CPCAPI2::XmppVCard::XmppVCardDetail& vcard, const CPCAPI2::XmppVCard::VCardFetchedEvent& evt);

};

#define assertXmppVCardFetchMatch(account, jid, vcard, validator) XmppVCardModuleTest::expectXmppVCardFetchMatch(__LINE__, account, jid, vcard, validator)
   
static void generateJwt(const resip::Data& p8file, const resip::Data& userIdentity, resip::Data& jwt)
{
   std::map<resip::Data, resip::Data> pubClaims;
   pubClaims["cp_user"] = userIdentity;
   CPCAPI2::AuthServer::JwtUtils::GenerateJWT(p8file, "CPCAPI2::AuthServer", pubClaims, 86400, jwt);
}
 
void XmppVCardModuleTest::resetVCard(XmppTestAccount& account, CPCAPI2::XmppVCard::XmppVCardDetail& vcard)
{
   vcard.emailList.clear();
   vcard.telephoneList.clear();
   vcard.addressList.clear();
   vcard.labelList.clear();
   vcard.name.family = "";
   vcard.name.given = "";
   vcard.name.middle = "";
   vcard.name.prefix = "";
   vcard.name.suffix = "";
   vcard.geo.latitude = "";
   vcard.geo.longitude = "";
   vcard.organization.name = "";
   vcard.organization.units.clear();
   vcard.photo.type = "";
   vcard.photo.hash = "";
   vcard.photo.extval = "";
   vcard.photo.binval.clear();
   vcard.logo.type = "";
   vcard.logo.hash = "";
   vcard.logo.extval = "";
   vcard.logo.binval.clear();
   vcard.classification = XmppVCardDetail::ClassNone;
   vcard.formattedname = "";
   vcard.nickname = "";
   vcard.url = "";
   vcard.birthday = "";
   vcard.jid = account.config.bare();
   vcard.title = "";
   vcard.role = "";
   vcard.note = "";
   vcard.desc = account.config.bare();
   vcard.mailer = "";
   vcard.timezone = "";
   vcard.product = "";
   vcard.revision = "";
   vcard.sortstring = "";
   vcard.phonetic = "";
   vcard.cpcollab = "";
   vcard.cpsoftphone = "";
   vcard.cpsoftphone_pref = false;
   vcard.uid = "";
}
   
void XmppVCardModuleTest::setVCard(XmppTestAccount& account, CPCAPI2::XmppVCard::XmppVCardDetail& vcard)
{
   XmppVCardDetail::Email email;
   email.userid = account.config.name + "_userid";
   email.home = true;
   email.work = false;
   email.internet = false;
   email.pref = false;
   email.x400 = false;
   vcard.emailList.clear();
   vcard.emailList.push_back(email);
   XmppVCardDetail::Telephone telephone;
   telephone.number = "number";
   telephone.home = true;
   telephone.work = false;
   telephone.voice = false;
   telephone.fax = false;
   telephone.pager = false;
   telephone.msg = false;
   telephone.cell = false;
   telephone.video = false;
   telephone.bbs = false;
   telephone.modem = false;
   telephone.isdn = false;
   telephone.pcs = false;
   telephone.pref = false;
   vcard.telephoneList.clear();
   vcard.telephoneList.push_back(telephone);
   XmppVCardDetail::Address address;
   address.pobox = "pobox";
   address.extadd = "extadd";
   address.street = "street";
   address.locality = "locality";
   address.region = "region";
   address.pcode = "pcode";
   address.ctry = "ctry";
   address.home = true;
   address.work = false;
   address.postal = false;
   address.parcel = false;
   address.pref = false;
   address.dom = true;
   address.intl = false;
   vcard.addressList.clear();
   vcard.addressList.push_back(address);
   XmppVCardDetail::Label label;
   label.lines.push_back("lines");
   label.home = true;
   label.work = false;
   label.postal = false;
   label.parcel = false;
   label.pref = false;
   label.dom = true;
   label.intl = false;
   vcard.labelList.clear();
   vcard.labelList.push_back(label);
   vcard.name.family = "family";
   vcard.name.given = "given";
   vcard.name.middle = "middle";
   vcard.name.prefix = "prefix";
   vcard.name.suffix = "suffix";
   vcard.geo.latitude = "latitude";
   vcard.geo.longitude = "longitude";
   vcard.organization.name = "organization";
   vcard.organization.units.clear();
   vcard.organization.units.push_back("units");
   
   vcard.photo.type = "image/png"; // only valid if binval exists
   vcard.photo.extval = ""; // only valid if no binval exists
   std::ifstream ifs((TestEnvironmentConfig::testResourcePath() + "vcard_photo.png").c_str(), std::ios_base::binary);
   ASSERT_TRUE(ifs.is_open());
   std::stringstream buff;
   buff << ifs.rdbuf();
   ifs.close();
   std::string strbuff = buff.str();
   vcard.photo.binval.assign(strbuff.data(), strbuff.data() + strbuff.size());
   gloox::SHA sha;
   sha.feed(strbuff);
   vcard.photo.hash = sha.hex().c_str();

   vcard.logo.type = "image/png"; // only valid if binval exists
   vcard.logo.extval = ""; // only valid if no binval exists
   std::ifstream ifs2((TestEnvironmentConfig::testResourcePath() + "vcard_logo.png").c_str(), std::ios_base::binary);
   ASSERT_TRUE(ifs2.is_open());
   std::stringstream buff2;
   buff2 << ifs2.rdbuf();
   ifs2.close();
   std::string strbuff2 = buff2.str();
   vcard.logo.binval.assign(strbuff2.data(), strbuff2.data() + strbuff2.size());
   gloox::SHA sha2;
   sha2.feed(strbuff2);
   vcard.logo.hash = sha2.hex().c_str();
   
   vcard.classification = XmppVCardDetail::ClassConfidential;
   vcard.formattedname = "formattedname";
   vcard.nickname = "nickname";
   vcard.url = "url";
   vcard.birthday = "birthday";
   vcard.jid = account.config.bare();
   vcard.title = "title";
   vcard.role = "role";
   vcard.note = "note";
   vcard.desc = account.config.bare();;
   vcard.mailer = "mailer";
   vcard.timezone = "timezone";
   // vcard.product = ""; // info is populated internally, e.g. gloox1.0.9
   vcard.revision = "revision";
   vcard.sortstring = "sortstring";
   vcard.phonetic = "phonetic";
   vcard.cpcollab = "cpcollab";
   vcard.cpsoftphone = "cpsoftphone";
   vcard.cpsoftphone_pref = true;
   vcard.uid = "uid";
}

void XmppVCardModuleTest::expectXmppVCardFetchMatch(int line, XmppTestAccount& account, cpc::string& jid, CPCAPI2::XmppVCard::XmppVCardDetail& vcard, std::function<void(CPCAPI2::XmppVCard::VCardFetchedEvent& evt)> validator)
{
   XmppVCardHandle h;
   VCardFetchedEvent evt;
   ASSERT_TRUE(account.events->expectEvent(line, "XmppVCardHandler::onVCardFetched", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   
   assertVCardAttributes(line, account, jid, vcard, evt);
   
   if (validator)
   {
      validator(evt);
   }
}
   
void XmppVCardModuleTest::assertVCardAttributes(int line, XmppTestAccount& account, cpc::string& jid, CPCAPI2::XmppVCard::XmppVCardDetail& vcard, const CPCAPI2::XmppVCard::VCardFetchedEvent& evt)
{
   safeCout("XmppVCardModuleTest::expectXmppVCardFetchMatch(): line: " << line << " account name: " << account.config.name << " account jid: " << account.config.bare() << " query jid: " << jid << " original vcard: " << std::endl << CPCAPI2::XmppVCard::get_debug_string(vcard) << std::endl);
   safeCout("XmppVCardModuleTest::expectXmppVCardFetchMatch(): line: " << line << " account name: " << account.config.name << " account jid: " << account.config.bare() << " query jid: " << jid << " event vcard: " << std::endl << CPCAPI2::XmppVCard::get_debug_string(evt.detail) << std::endl);
   
   ASSERT_EQ(account.handle, evt.account);
   ASSERT_EQ(account.vcardHandle, evt.handle);
   ASSERT_EQ(jid, evt.jid);
   ASSERT_EQ(vcard.emailList.size(), evt.detail.emailList.size());
   if (evt.detail.emailList.size() > 0)
   {
      ASSERT_EQ(vcard.emailList[0].userid, evt.detail.emailList[0].userid);
      ASSERT_EQ(vcard.emailList[0].home, evt.detail.emailList[0].home);
      ASSERT_EQ(vcard.emailList[0].work, evt.detail.emailList[0].work);
      ASSERT_EQ(vcard.emailList[0].internet, evt.detail.emailList[0].internet);
      ASSERT_EQ(vcard.emailList[0].pref, evt.detail.emailList[0].pref);
      ASSERT_EQ(vcard.emailList[0].x400, evt.detail.emailList[0].x400);
   }
   
   ASSERT_EQ(vcard.telephoneList.size(), evt.detail.telephoneList.size());
   if (evt.detail.telephoneList.size() > 0)
   {
      ASSERT_EQ(vcard.telephoneList[0].number, evt.detail.telephoneList[0].number);
      ASSERT_EQ(vcard.telephoneList[0].home, evt.detail.telephoneList[0].home);
      ASSERT_EQ(vcard.telephoneList[0].work, evt.detail.telephoneList[0].work);
      ASSERT_EQ(vcard.telephoneList[0].voice, evt.detail.telephoneList[0].voice);
      ASSERT_EQ(vcard.telephoneList[0].fax, evt.detail.telephoneList[0].fax);
      ASSERT_EQ(vcard.telephoneList[0].pager, evt.detail.telephoneList[0].pager);
      ASSERT_EQ(vcard.telephoneList[0].msg, evt.detail.telephoneList[0].msg);
      ASSERT_EQ(vcard.telephoneList[0].cell, evt.detail.telephoneList[0].cell);
      ASSERT_EQ(vcard.telephoneList[0].video, evt.detail.telephoneList[0].video);
      ASSERT_EQ(vcard.telephoneList[0].bbs, evt.detail.telephoneList[0].bbs);
      ASSERT_EQ(vcard.telephoneList[0].modem, evt.detail.telephoneList[0].modem);
      ASSERT_EQ(vcard.telephoneList[0].isdn, evt.detail.telephoneList[0].isdn);
      ASSERT_EQ(vcard.telephoneList[0].pcs, evt.detail.telephoneList[0].pcs);
      ASSERT_EQ(vcard.telephoneList[0].pref, evt.detail.telephoneList[0].pref);
   }

   ASSERT_EQ(vcard.addressList.size(), evt.detail.addressList.size());
   if (evt.detail.addressList.size() > 0)
   {
      ASSERT_EQ(vcard.addressList[0].pobox, evt.detail.addressList[0].pobox);
      ASSERT_EQ(vcard.addressList[0].extadd, evt.detail.addressList[0].extadd);
      ASSERT_EQ(vcard.addressList[0].street, evt.detail.addressList[0].street);
      ASSERT_EQ(vcard.addressList[0].locality, evt.detail.addressList[0].locality);
      ASSERT_EQ(vcard.addressList[0].region, evt.detail.addressList[0].region);
      ASSERT_EQ(vcard.addressList[0].pcode, evt.detail.addressList[0].pcode);
      ASSERT_EQ(vcard.addressList[0].ctry, evt.detail.addressList[0].ctry);
      ASSERT_EQ(vcard.addressList[0].home, evt.detail.addressList[0].home);
      ASSERT_EQ(vcard.addressList[0].work, evt.detail.addressList[0].work);
      ASSERT_EQ(vcard.addressList[0].postal, evt.detail.addressList[0].postal);
      ASSERT_EQ(vcard.addressList[0].parcel, evt.detail.addressList[0].parcel);
      ASSERT_EQ(vcard.addressList[0].pref, evt.detail.addressList[0].pref);
      ASSERT_EQ(vcard.addressList[0].dom, evt.detail.addressList[0].dom);
      ASSERT_EQ(vcard.addressList[0].intl, evt.detail.addressList[0].intl);
   }

   ASSERT_EQ(vcard.labelList.size(), evt.detail.labelList.size());
   if (evt.detail.labelList.size() > 0)
   {
      ASSERT_EQ(vcard.labelList[0].lines.size(), evt.detail.labelList[0].lines.size());
      if (evt.detail.labelList[0].lines.size() > 0)
      {
         ASSERT_EQ(vcard.labelList[0].lines[0], evt.detail.labelList[0].lines[0]);
         ASSERT_EQ(vcard.labelList[0].home, evt.detail.labelList[0].home);
         ASSERT_EQ(vcard.labelList[0].work, evt.detail.labelList[0].work);
         ASSERT_EQ(vcard.labelList[0].postal, evt.detail.labelList[0].postal);
         ASSERT_EQ(vcard.labelList[0].parcel, evt.detail.labelList[0].parcel);
         ASSERT_EQ(vcard.labelList[0].pref, evt.detail.labelList[0].pref);
         ASSERT_EQ(vcard.labelList[0].dom, evt.detail.labelList[0].dom);
         ASSERT_EQ(vcard.labelList[0].intl, evt.detail.labelList[0].intl);
      }
   }

   ASSERT_EQ(vcard.name.family, evt.detail.name.family);
   ASSERT_EQ(vcard.name.given, evt.detail.name.given);
   ASSERT_EQ(vcard.name.middle, evt.detail.name.middle);
   ASSERT_EQ(vcard.name.prefix, evt.detail.name.prefix);
   ASSERT_EQ(vcard.name.suffix, evt.detail.name.suffix);
   ASSERT_EQ(vcard.geo.latitude, evt.detail.geo.latitude);
   ASSERT_EQ(vcard.geo.longitude, evt.detail.geo.longitude);
   ASSERT_EQ(vcard.organization.name, evt.detail.organization.name);
   ASSERT_EQ(vcard.organization.units.size(), evt.detail.organization.units.size());
   if (evt.detail.organization.units.size() > 0)
   {
      ASSERT_EQ(vcard.organization.units[0], evt.detail.organization.units[0]);
   }
   
   if (!vcard.photo.type.empty() && (vcard.photo.binval.size() > 0))
   {
      ASSERT_EQ(vcard.photo.type, evt.detail.photo.type);
      ASSERT_EQ(vcard.photo.binval.size(), evt.detail.photo.binval.size());
      ASSERT_EQ(vcard.photo.hash, evt.detail.photo.hash);
   }
   else
   {
      ASSERT_EQ(vcard.photo.extval, evt.detail.photo.extval);
   }
   
   if (!vcard.logo.type.empty() && (vcard.logo.binval.size() > 0))
   {
      ASSERT_EQ(vcard.logo.type, evt.detail.logo.type);
      ASSERT_EQ(vcard.logo.binval.size(), evt.detail.logo.binval.size());
      ASSERT_EQ(vcard.logo.hash, evt.detail.logo.hash);
   }
   else
   {
      ASSERT_EQ(vcard.logo.extval, evt.detail.logo.extval);
   }

   ASSERT_EQ(vcard.classification, evt.detail.classification);
   ASSERT_EQ(vcard.formattedname, evt.detail.formattedname);
   ASSERT_EQ(vcard.nickname, evt.detail.nickname);
   ASSERT_EQ(vcard.url, evt.detail.url);
   ASSERT_EQ(vcard.birthday, evt.detail.birthday);
   ASSERT_EQ(vcard.jid, evt.detail.jid);
   ASSERT_EQ(vcard.title, evt.detail.title);
   ASSERT_EQ(vcard.role, evt.detail.role);
   ASSERT_EQ(vcard.note, evt.detail.note);
   ASSERT_EQ(vcard.desc, evt.detail.desc);
   ASSERT_EQ(vcard.mailer, evt.detail.mailer);
   ASSERT_EQ(vcard.timezone, evt.detail.timezone);
   // ASSERT_EQ(vcard.product, evt.detail.product); // cannot assert, as info is populated internally, e.g. gloox1.0.9
   ASSERT_EQ(vcard.revision, evt.detail.revision);
   ASSERT_EQ(vcard.sortstring, evt.detail.sortstring);
   ASSERT_EQ(vcard.phonetic, evt.detail.phonetic);
   ASSERT_EQ(vcard.cpcollab, evt.detail.cpcollab);
   ASSERT_EQ(vcard.cpsoftphone, evt.detail.cpsoftphone);
   ASSERT_EQ(vcard.cpsoftphone_pref, evt.detail.cpsoftphone_pref);
   ASSERT_EQ(vcard.uid, evt.detail.uid);
}

TEST_F(XmppVCardModuleTest, XmppBasicVCardFetch)
{
   XmppTestAccount alice("alice", Account_NoInit);
   alice.config.settings.enableXmppPresence = false; // Block presence triggered auto-fetched vcard events
   alice.enable();

   assertXmppVCardFetched(alice, [&](const VCardFetchedEvent& evt)
   {
      ASSERT_EQ(alice.handle, evt.account);
      ASSERT_EQ(alice.vcardHandle, evt.handle);
      ASSERT_EQ(alice.config.bare(), evt.jid);
   });

   XmppTestAccount bob("bob", Account_NoInit);

   alice.vcardManager->fetchVCard(alice.vcardHandle, bob.config.bare());
   assertXmppVCardFetched(alice, [&](const VCardFetchedEvent& evt)
   {
      ASSERT_EQ(alice.handle, evt.account);
      ASSERT_EQ(alice.vcardHandle, evt.handle);
      ASSERT_EQ(bob.config.bare(), evt.jid);
   });
   
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
}

#if USE_CPCAPI2_JSON_TESTS
TEST_F(XmppVCardModuleTest, XmppBasicVCardFetch_JSON)
{
   XmppTestCloudAccount alice("alice");
   alice.config.settings.enableXmppPresence = false; // Block presence triggered auto-fetched vcard events

   XmppVCardDetail aliceFetched;
   cpc::string aliceJid = alice.config.bare();
   auto aliceInitFetchEvent = std::async(std::launch::async, [&]()
   {
      alice.enable();
                                            
      assertXmppVCardFetched(alice, [&](const VCardFetchedEvent& evt)
      {
         ASSERT_EQ(alice.handle, evt.account);
         ASSERT_EQ(alice.vcardHandle, evt.handle);
         ASSERT_EQ(aliceJid, evt.jid);
         aliceFetched = evt.detail;
                                                                      
         safeCout("XmppVCardModuleTest::XmppVCardStore_JSON(): account name: " << alice.config.name << " account jid: " << alice.config.bare() << " original vcard: " << std::endl << CPCAPI2::XmppVCard::get_debug_string(aliceFetched) << std::endl);
      });
   });
   
   waitFor(aliceInitFetchEvent);
   
   auto aliceFetchEvent = std::async(std::launch::async, [&]()
   {
      alice.vcardJson->fetchVCard(alice.vcardHandle, aliceJid);
      assertXmppVCardFetchMatch(alice, aliceJid, aliceFetched, [&](const VCardFetchedEvent& evt)
      {
         ASSERT_EQ(alice.handle, evt.account);
         ASSERT_EQ(alice.vcardHandle, evt.handle);
         ASSERT_EQ(aliceJid, evt.jid);
      });
   });
   
   waitFor(aliceFetchEvent);
   
   std::this_thread::sleep_for(std::chrono::seconds(3));

   alice.disable();
   
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
}
#endif // USE_CPCAPI2_JSON_TESTS
   
TEST_F(XmppVCardModuleTest, XmppVCardStoreAndFetch)
{
   XmppTestAccount alice("alice", Account_NoInit);
   alice.config.settings.enableXmppPresence = false; // Block presence triggered auto-fetched vcard events
   alice.enable();

   assertXmppVCardFetched(alice, [&](const VCardFetchedEvent& evt)
   {
      ASSERT_EQ(alice.handle, evt.account);
      ASSERT_EQ(alice.vcardHandle, evt.handle);
      ASSERT_EQ(alice.config.bare(), evt.jid);
   });

   XmppTestAccount bob("bob", Account_NoInit);
   bob.config.settings.enableXmppPresence = false; // Block presence triggered auto-fetched vcard events
   bob.enable();

   XmppVCardDetail original;

   bob.vcardManager->fetchVCard(bob.vcardHandle, bob.config.bare());
   assertXmppVCardFetched(bob, [&](const VCardFetchedEvent& evt)
   {
      ASSERT_EQ(bob.handle, evt.account);
      ASSERT_EQ(bob.vcardHandle, evt.handle);
      ASSERT_EQ(bob.config.bare(), evt.jid);
      original = evt.detail;
   });

   const cpc::string testCpsoftphone("sip:<EMAIL>");
   bool testCpsoftphone_pref = true;

   { // modify bob's vcard
      XmppVCardDetail vcard = original;
      vcard.desc = bob.config.bare();
      std::ifstream ifs ((TestEnvironmentConfig::testResourcePath() + "vcard_photo.png").c_str(), std::ios_base::binary);
      ASSERT_TRUE(ifs.is_open());
      ifs.seekg( 0, std::ios::end);
      std::streamoff size = ifs.tellg();
      ifs.seekg( 0, std::ios::beg);
      char* buffer = new char [size];
      ifs.read(buffer, size);
      vcard.photo.type = "image/png";
      vcard.photo.binval.assign(buffer, buffer + size);
      vcard.cpcollab = "cpcollab";
      vcard.cpsoftphone = testCpsoftphone;
      vcard.cpsoftphone_pref = testCpsoftphone_pref;
      bob.vcardManager->storeVCard(bob.vcardHandle, vcard);
      assertXmppVCardOperationResult(bob, [&](const VCardOperationResultEvent& evt)
      {
         ASSERT_EQ(bob.handle, evt.account);
         ASSERT_EQ(bob.vcardHandle, evt.handle);
         ASSERT_EQ("", evt.jid); // empty "from"??
         ASSERT_EQ(evt.result, 0);
      });
      delete[] buffer;
   }

   alice.vcardManager->fetchVCard(alice.vcardHandle, bob.config.bare());
   assertXmppVCardFetched(alice, [&](const VCardFetchedEvent& evt)
   {
      ASSERT_EQ(alice.handle, evt.account);
      ASSERT_EQ(alice.vcardHandle, evt.handle);
      ASSERT_EQ(bob.config.bare(), evt.jid);
      ASSERT_EQ(bob.config.bare(), evt.detail.desc);
      std::ofstream ofs ("recv.png", std::ios_base::binary | std::ios_base::trunc);
      ofs.write(evt.detail.photo.binval.data(), evt.detail.photo.binval.size());
      ASSERT_EQ(evt.detail.cpcollab, "cpcollab");
      ASSERT_EQ(evt.detail.cpsoftphone, testCpsoftphone);
      ASSERT_EQ(evt.detail.cpsoftphone_pref, testCpsoftphone_pref);
   });

   bob.vcardManager->storeVCard(bob.vcardHandle, original);
   assertXmppVCardOperationResult(bob, [&](const VCardOperationResultEvent& evt)
   {
      ASSERT_EQ(bob.handle, evt.account);
      ASSERT_EQ(bob.vcardHandle, evt.handle);
      ASSERT_EQ("", evt.jid); // empty "from"??
      ASSERT_EQ(evt.result, 0);
   });
   
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   bob.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
}

#if USE_CPCAPI2_JSON_TESTS
TEST_F(XmppVCardModuleTest, XmppVCardStoreAndFetch_JSON)
{
   XmppTestAccount bob("bob", Account_NoInit); // Bob is local SDK client
   XmppTestCloudAccount alice("alice"); // Alice is the cloud SDK client
   
   bob.config.settings.enableXmppPresence = false; // Block presence triggered auto-fetched vcard events
   alice.config.settings.enableXmppPresence = false; // Block presence triggered auto-fetched vcard events

   std::this_thread::sleep_for(std::chrono::seconds(5));

   XmppVCardDetail bobFetched;
   cpc::string bobJid = bob.config.bare();
   auto bobInitFetchEvent = std::async(std::launch::async, [&]() noexcept
   {
      bob.enable();
      assertXmppVCardFetched(bob, [&](const VCardFetchedEvent& evt)
      {
         ASSERT_EQ(bob.handle, evt.account);
         ASSERT_EQ(bob.vcardHandle, evt.handle);
         ASSERT_EQ(bobJid, evt.jid);
         bobFetched = evt.detail;
         
         safeCout("XmppVCardModuleTest::XmppVCardStoreAndFetch_JSON(): account name: " << bob.config.name << " account jid: " << bob.config.bare() << " original vcard: " << std::endl << CPCAPI2::XmppVCard::get_debug_string(bobFetched) << std::endl);
      });
   });
   
   waitFor(bobInitFetchEvent);

   std::this_thread::sleep_for(std::chrono::seconds(5));

   XmppVCardDetail aliceFetched;
   cpc::string aliceJid = alice.config.bare();
   auto aliceInitFetchEvent = std::async(std::launch::async, [&]() noexcept
   {
      alice.enable();
      assertXmppVCardFetched(alice, [&](const VCardFetchedEvent& evt)
      {
         ASSERT_EQ(alice.handle, evt.account);
         ASSERT_EQ(alice.vcardHandle, evt.handle);
         ASSERT_EQ(aliceJid, evt.jid);
         aliceFetched = evt.detail;
                                                                      
         safeCout("XmppVCardModuleTest::XmppVCardStoreAndFetch_JSON(): account name: " << alice.config.name << " account jid: " << alice.config.bare() << " original vcard: " << std::endl << CPCAPI2::XmppVCard::get_debug_string(aliceFetched) << std::endl);
      });
   });
   
   waitFor(aliceInitFetchEvent);
   
   std::this_thread::sleep_for(std::chrono::seconds(5));

   XmppVCardDetail bobStored;
   setVCard(bob, bobStored);
   
   auto bobStoredEvent = std::async(std::launch::async, [&]() noexcept
   {
      bob.vcardManager->storeVCard(bob.vcardHandle, bobStored);
      assertXmppVCardOperationResult(bob, [&](const VCardOperationResultEvent& evt)
      {
         ASSERT_EQ(bob.handle, evt.account);
         ASSERT_EQ(bob.vcardHandle, evt.handle);
         ASSERT_EQ(evt.result, 0);
         ASSERT_EQ(1, evt.type);
      });
                                         
      bob.vcardManager->fetchVCard(bob.vcardHandle, bobJid);
      assertXmppVCardFetchMatch(bob, bobJid, bobStored, [&](const VCardFetchedEvent& evt)
      {
         ASSERT_EQ(bob.handle, evt.account);
         ASSERT_EQ(bob.vcardHandle, evt.handle);
         ASSERT_EQ(bobJid, evt.jid);
         ASSERT_EQ(bobJid, evt.detail.jid);
         bobFetched = evt.detail;
      });
   });
   
   waitFor(bobStoredEvent);
   
   std::this_thread::sleep_for(std::chrono::seconds(5));
   
   XmppVCardDetail aliceStored;
   setVCard(alice, aliceStored);
   
   auto aliceStoredEvent = std::async(std::launch::async, [&]() noexcept
   {
      alice.vcardJson->storeVCard(alice.vcardHandle, aliceStored);
      assertXmppVCardOperationResult(alice, [&](const VCardOperationResultEvent& evt)
      {
         ASSERT_EQ(alice.handle, evt.account);
         ASSERT_EQ(alice.vcardHandle, evt.handle);
         ASSERT_EQ(evt.result, 0);
         ASSERT_EQ(1, evt.type);
      });
      
      alice.vcardJson->fetchVCard(alice.vcardHandle, aliceJid);
      assertXmppVCardFetchMatch(alice, aliceJid, aliceStored, [&](const VCardFetchedEvent& evt)
      {
         ASSERT_EQ(alice.handle, evt.account);
         ASSERT_EQ(alice.vcardHandle, evt.handle);
         ASSERT_EQ(aliceJid, evt.jid);
         ASSERT_EQ(aliceJid, evt.detail.jid);
         aliceFetched = evt.detail;
      });
   });
   
   waitFor(aliceStoredEvent);
   
   std::this_thread::sleep_for(std::chrono::seconds(5));

   auto bobFetchAlternateEvent = std::async(std::launch::async, [&]() noexcept
   {
      bob.vcardManager->fetchVCard(bob.vcardHandle, aliceJid);      
      assertXmppVCardFetchMatch(bob, aliceJid, aliceStored, [&](const VCardFetchedEvent& evt)
      {
         ASSERT_EQ(bob.handle, evt.account);
         ASSERT_EQ(bob.vcardHandle, evt.handle);
         ASSERT_EQ(aliceJid, evt.jid);
         ASSERT_EQ(aliceJid, evt.detail.jid);
      });
   });
   
   waitFor(bobFetchAlternateEvent);
   
   std::this_thread::sleep_for(std::chrono::seconds(5));

   auto aliceFetchAlternateEvent = std::async(std::launch::async, [&]() noexcept
   {
      alice.vcardJson->fetchVCard(alice.vcardHandle, bobJid);
      assertXmppVCardFetchMatch(alice, bobJid, bobStored, [&](const VCardFetchedEvent& evt)
      {
         ASSERT_EQ(alice.handle, evt.account);
         ASSERT_EQ(alice.vcardHandle, evt.handle);
         ASSERT_EQ(bobJid, evt.jid);
         ASSERT_EQ(bobJid, evt.detail.jid);
         
         // std::ofstream ofs ("output.png", std::ios_base::binary | std::ios_base::trunc);
         // ofs.write(evt.detail.photo.binval.data(), evt.detail.photo.binval.size());
      });
   });
   
   waitFor(aliceFetchAlternateEvent);
   
   if (!TestEnvironmentConfig::autoCreateXmppUsers())
   {
      std::this_thread::sleep_for(std::chrono::seconds(5));

      resetVCard(bob, bobStored);

      auto bobResetEvent = std::async(std::launch::async, [&]() noexcept
      {
         bob.vcardManager->storeVCard(bob.vcardHandle, bobStored);
         assertXmppVCardOperationResult(bob, [&](const VCardOperationResultEvent& evt)
         {
            ASSERT_EQ(bob.handle, evt.account);
            ASSERT_EQ(bob.vcardHandle, evt.handle);
            ASSERT_EQ(0, evt.result);
            ASSERT_EQ(1, evt.type);
         });
      });
      
      waitFor(bobResetEvent);
      
      std::this_thread::sleep_for(std::chrono::seconds(5));
      
      resetVCard(alice, aliceStored);
      
      auto aliceResetEvent = std::async(std::launch::async, [&]() noexcept
      {
         alice.vcardJson->storeVCard(alice.vcardHandle, aliceStored);
         assertXmppVCardOperationResult(alice, [&](const VCardOperationResultEvent& evt)
         {
            ASSERT_EQ(alice.handle, evt.account);
            ASSERT_EQ(alice.vcardHandle, evt.handle);
            ASSERT_EQ(evt.result, 0);
            ASSERT_EQ(1, evt.type);
         });
      });
      
      waitFor(aliceResetEvent);
      
      std::this_thread::sleep_for(std::chrono::seconds(3));
      
      alice.disable();
      
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   }
}

TEST_F(XmppVCardModuleTest, XmppVCardStore_JSON)
{
   XmppTestCloudAccount alice("alice");
   alice.config.settings.enableXmppPresence = false; // Block presence triggered auto-fetched vcard events

   XmppVCardDetail aliceFetched;
   cpc::string aliceJid = alice.config.bare();
   auto aliceInitFetchEvent = std::async(std::launch::async, [&]()
   {
      alice.enable();
                                            
      assertXmppVCardFetched(alice, [&](const VCardFetchedEvent& evt)
      {
         ASSERT_EQ(alice.handle, evt.account);
         ASSERT_EQ(alice.vcardHandle, evt.handle);
         ASSERT_EQ(aliceJid, evt.jid);
         aliceFetched = evt.detail;
                                                                      
         safeCout("XmppVCardModuleTest::XmppVCardStore_JSON(): account name: " << alice.config.name << " account jid: " << alice.config.bare() << " original vcard: " << std::endl << CPCAPI2::XmppVCard::get_debug_string(aliceFetched) << std::endl);
      });
   });
   
   waitFor(aliceInitFetchEvent);
   std::this_thread::sleep_for(std::chrono::seconds(5));
   
   XmppVCardDetail aliceStored;
   setVCard(alice, aliceStored);
   
   auto aliceStoredEvent = std::async(std::launch::async, [&]()
   {
      alice.vcardJson->storeVCard(alice.vcardHandle, aliceStored);
      assertXmppVCardOperationResult(alice, [&](const VCardOperationResultEvent& evt)
      {
         ASSERT_EQ(alice.handle, evt.account);
         ASSERT_EQ(alice.vcardHandle, evt.handle);
         ASSERT_EQ(evt.result, 0);
         ASSERT_EQ(1, evt.type);
      });
      
      alice.vcardJson->fetchVCard(alice.vcardHandle, aliceJid);
      assertXmppVCardFetchMatch(alice, aliceJid, aliceStored, [&](const VCardFetchedEvent& evt)
      {
         ASSERT_EQ(alice.handle, evt.account);
         ASSERT_EQ(alice.vcardHandle, evt.handle);
         ASSERT_EQ(aliceJid, evt.jid);
         ASSERT_EQ(aliceJid, evt.detail.jid);
      });
   });
   
   waitFor(aliceStoredEvent);
   
   if (!TestEnvironmentConfig::autoCreateXmppUsers())
   {
      std::this_thread::sleep_for(std::chrono::seconds(5));

      resetVCard(alice, aliceStored);
   
      auto aliceResetEvent = std::async(std::launch::async, [&]()
      {
         alice.vcardJson->storeVCard(alice.vcardHandle, aliceStored);
         assertXmppVCardOperationResult(alice, [&](const VCardOperationResultEvent& evt)
         {
            ASSERT_EQ(alice.handle, evt.account);
            ASSERT_EQ(alice.vcardHandle, evt.handle);
            ASSERT_EQ(evt.result, 0);
            ASSERT_EQ(1, evt.type);
         });
      });
   
      waitFor(aliceResetEvent);
   
      std::this_thread::sleep_for(std::chrono::seconds(5));
   
      alice.disable();
   
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   }
}
   
TEST_F(XmppVCardModuleTest, XmppVCardStoreExtval_JSON)
{
   XmppTestCloudAccount alice("alice");
   alice.config.settings.enableXmppPresence = false; // Block presence triggered auto-fetched vcard events

   XmppVCardDetail aliceFetched;
   cpc::string aliceJid = alice.config.bare();
   auto aliceInitFetchEvent = std::async(std::launch::async, [&]()
   {
      alice.enable();
                                            
      assertXmppVCardFetched(alice, [&](const VCardFetchedEvent& evt)
      {
         ASSERT_EQ(alice.handle, evt.account);
         ASSERT_EQ(alice.vcardHandle, evt.handle);
         ASSERT_EQ(aliceJid, evt.jid);
         aliceFetched = evt.detail;
                                                                      
         safeCout("XmppVCardModuleTest::XmppVCardStore_JSON(): account name: " << alice.config.name << " account jid: " << alice.config.bare() << " original vcard: " << std::endl << CPCAPI2::XmppVCard::get_debug_string(aliceFetched) << std::endl);
      });
   });
   
   waitFor(aliceInitFetchEvent);
   
   XmppVCardDetail aliceStored;
   setVCard(alice, aliceStored);
   aliceStored.photo.binval.clear();
   aliceStored.photo.type = "";
   aliceStored.photo.hash = "";
   aliceStored.photo.extval = "vcard_photo.png";
   aliceStored.logo.binval.clear();
   aliceStored.logo.type = "";
   aliceStored.logo.hash = "";
   aliceStored.logo.extval = "vcard_logo.png";

   auto aliceStoredEvent = std::async(std::launch::async, [&]()
   {
      alice.vcardJson->storeVCard(alice.vcardHandle, aliceStored);
      assertXmppVCardOperationResult(alice, [&](const VCardOperationResultEvent& evt)
      {
         ASSERT_EQ(alice.handle, evt.account);
         ASSERT_EQ(alice.vcardHandle, evt.handle);
         ASSERT_EQ(evt.result, 0);
         ASSERT_EQ(1, evt.type);
      });
      
      alice.vcardJson->fetchVCard(alice.vcardHandle, aliceJid);
      assertXmppVCardFetchMatch(alice, aliceJid, aliceStored, [&](const VCardFetchedEvent& evt)
      {
         ASSERT_EQ(alice.handle, evt.account);
         ASSERT_EQ(alice.vcardHandle, evt.handle);
         ASSERT_EQ(aliceJid, evt.jid);
         ASSERT_EQ(aliceJid, evt.detail.jid);
      });
   });
   
   waitFor(aliceStoredEvent);
   
   if (!TestEnvironmentConfig::autoCreateXmppUsers())
   {
      std::this_thread::sleep_for(std::chrono::seconds(5));

      resetVCard(alice, aliceStored);
      
      auto aliceResetEvent = std::async(std::launch::async, [&]()
      {
         alice.vcardJson->storeVCard(alice.vcardHandle, aliceStored);
         assertXmppVCardOperationResult(alice, [&](const VCardOperationResultEvent& evt)
         {
            ASSERT_EQ(alice.handle, evt.account);
            ASSERT_EQ(alice.vcardHandle, evt.handle);
            ASSERT_EQ(evt.result, 0);
            ASSERT_EQ(1, evt.type);
         });
      });
      
      waitFor(aliceResetEvent);
      
      std::this_thread::sleep_for(std::chrono::seconds(3));
      
      alice.disable();
      
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   }
}
#endif // USE_CPCAPI2_JSON_TESTS
 
TEST_F(XmppVCardModuleTest, DISABLED_VCardUpdate)
{
   XmppTestAccount alice("alice", Account_NoInit);
   
   auto aliceFetchEvent = std::async(std::launch::async, [&]()
   {
      alice.enable(false);
      assertXmppRosterUpdate(alice, [&](const XmppRosterUpdateEvent& evt)
      {
         safeCout("XmppVCardModuleTest::VCardUpdate(): account name: " << alice.config.name << " account jid: " << alice.config.bare() << " roster update received, with added item count: " << evt.added.size());
         cpc::vector<XmppRosterUpdateEvent::ChangeItemAdd>::const_iterator it = evt.added.begin();
         for (;it!=evt.added.end(); ++it)
         {
            alice.roster->removeRosterItem(alice.rosterHandle, it->item.address);
         }
      });

      CPCAPI2::XmppRoster::PresenceType presenceType = XmppRoster::PresenceType_Available;
      assertXmppSelfPresence(alice, [&](const XmppRosterPresenceEvent& evt)
      {
         safeCout("XmppVCardModuleTest::VCardUpdate(): account name: " << alice.config.name << " received a self presence for: " << evt.rosterItem.address << " resource list: " << evt.rosterItem.resources.size());
         ASSERT_EQ(evt.rosterItem.address, alice.config.bare());
         ASSERT_EQ(evt.rosterItem.resources.size(), 1);
         presenceType = evt.rosterItem.resources[0].presenceType;
      });
      
      assertXmppVCardFetched(alice, [&](const VCardFetchedEvent& evt)
      {
         safeCout("XmppVCardModuleTest::VCardUpdate(): account name: " << alice.config.name << " account jid: " << alice.config.bare() << " init vcard fetched: " << std::endl << CPCAPI2::XmppVCard::get_debug_string(evt.detail) << std::endl);
         ASSERT_EQ(alice.handle, evt.account);
         ASSERT_EQ(alice.vcardHandle, evt.handle);
         ASSERT_EQ(alice.config.bare(), evt.jid);
      });
      
      if (presenceType != XmppRoster::PresenceType_Available)
      {
         assertXmppVCardFetched(alice, [&](const VCardFetchedEvent& evt)
         {
            safeCout("XmppVCardModuleTest::VCardUpdate(): account name: " << alice.config.name << " account jid: " << alice.config.bare() << " presence vcard fetched: " << std::endl << CPCAPI2::XmppVCard::get_debug_string(evt.detail) << std::endl);
            ASSERT_EQ(alice.handle, evt.account);
            ASSERT_EQ(alice.vcardHandle, evt.handle);
            ASSERT_EQ(alice.config.bare(), evt.jid);
         });
      }
   });
   
   waitFor(aliceFetchEvent);

   XmppTestAccount bob("bob", Account_NoInit);
   
   auto bobFetchEvent = std::async(std::launch::async, [&]()
   {
      bob.enable(false);
      assertXmppRosterUpdate(bob, [&](const XmppRosterUpdateEvent& evt)
      {
         safeCout("XmppVCardModuleTest::VCardUpdate(): account name: " << bob.config.name << " account jid: " << bob.config.bare() << " roster update received, with added item count: " << evt.added.size());
         cpc::vector<XmppRosterUpdateEvent::ChangeItemAdd>::const_iterator it = evt.added.begin();
         for (;it!=evt.added.end(); ++it)
         {
            bob.roster->removeRosterItem(bob.rosterHandle, it->item.address);
         }
      });
      
      CPCAPI2::XmppRoster::PresenceType presenceType = XmppRoster::PresenceType_Available;
      assertXmppSelfPresence(bob, [&](const XmppRosterPresenceEvent& evt)
      {
         safeCout("XmppVCardModuleTest::VCardUpdate(): account name: " << bob.config.name << " received a self presence for: " << evt.rosterItem.address << " resource list: " << evt.rosterItem.resources.size());
         ASSERT_EQ(evt.rosterItem.address, bob.config.bare());
         ASSERT_EQ(evt.rosterItem.resources.size(), 1);
         presenceType = evt.rosterItem.resources[0].presenceType;
      });
      
      safeCout("XmppVCardModuleTest::VCardUpdate(): account name: " << bob.config.name << " account jid: " << bob.config.bare() << " waiting for init vcard fetched event");
      assertXmppVCardFetched(bob, [&](const VCardFetchedEvent& evt)
      {
         safeCout("XmppVCardModuleTest::VCardUpdate(): account name: " << bob.config.name << " account jid: " << bob.config.bare() << " init vcard fetched: " << std::endl << CPCAPI2::XmppVCard::get_debug_string(evt.detail) << std::endl);
         ASSERT_EQ(bob.handle, evt.account);
         ASSERT_EQ(bob.vcardHandle, evt.handle);
         ASSERT_EQ(bob.config.bare(), evt.jid);
      });
      
      if (presenceType != XmppRoster::PresenceType_Available)
      {
         assertXmppVCardFetched(bob, [&](const VCardFetchedEvent& evt)
         {
            safeCout("XmppVCardModuleTest::VCardUpdate(): account name: " << bob.config.name << " account jid: " << bob.config.bare() << " presence vcard fetched: " << std::endl << CPCAPI2::XmppVCard::get_debug_string(evt.detail) << std::endl);
            ASSERT_EQ(bob.handle, evt.account);
            ASSERT_EQ(bob.vcardHandle, evt.handle);
            ASSERT_EQ(bob.config.bare(), evt.jid);
         });
      }
   });

   waitFor(bobFetchEvent);
   
   alice.roster->subscribePresence(alice.rosterHandle, bob.config.bare(), "bob");
   assertXmppSubscriptionRequest(bob, [&](const XmppRosterSubscriptionRequestEvent& evt)
   {
      ASSERT_EQ(evt.address, alice.config.bare());
      bob.roster->acceptSubscriptionRequest(bob.rosterHandle, evt.address);
   });

   CPCAPI2::XmppRoster::PresenceType presenceType = XmppRoster::PresenceType_Available;
   assertXmppRosterPresence(alice, [&](const XmppRosterPresenceEvent& evt)
   {
      safeCout("XmppVCardModuleTest::VCardUpdate(): account name: " << alice.config.name << " received a first roster presence for: " << evt.rosterItem.address << " resource list: " << evt.rosterItem.resources.size());
      ASSERT_EQ(evt.rosterItem.address, bob.config.bare());
      ASSERT_EQ(evt.rosterItem.subscription, XmppRoster::SubscriptionState_Out);
      ASSERT_EQ(evt.rosterItem.resources.size(), 1);
      presenceType = evt.rosterItem.resources[0].presenceType;
   });
   
   // Sometimes an away presence may be received subsequently followed by the available presence
   if (presenceType != XmppRoster::PresenceType_Available)
   {
      assertXmppRosterPresence(alice, [&](const XmppRosterPresenceEvent& evt)
      {
         safeCout("XmppVCardModuleTest::VCardUpdate(): account name: " << alice.config.name << " received a second roster presence for: " << evt.rosterItem.address << " resource list: " << evt.rosterItem.resources.size());
         // << " " << ((evt.rosterItem.resources.size() >= 1) ? (int)evt.rosterItem.resources[0].presenceType : "null") << " " << ((evt.rosterItem.resources.size() >= 2) ? (int)evt.rosterItem.resources[1].presenceType : "null"));
         ASSERT_EQ(evt.rosterItem.address, bob.config.bare());
         ASSERT_EQ(evt.rosterItem.subscription, XmppRoster::SubscriptionState_Out);
         ASSERT_EQ(evt.rosterItem.resources.size(), 2);
         safeCout("XmppVCardModuleTest::VCardUpdate(): resource list: " << evt.rosterItem.resources.size() << " item 1: " << evt.rosterItem.resources[0].presenceType << " item 2: " << evt.rosterItem.resources[1].presenceType);
         ASSERT_EQ(evt.rosterItem.resources[0].presenceType, XmppRoster::PresenceType_Available);
      });
   }

   XmppVCardDetail original;

   bob.vcardManager->fetchVCard(bob.vcardHandle, bob.config.bare());
   assertXmppVCardFetched(bob, [&](const VCardFetchedEvent& evt)
   {
      ASSERT_EQ(bob.handle, evt.account);
      ASSERT_EQ(bob.vcardHandle, evt.handle);
      ASSERT_EQ(bob.config.bare(), evt.jid);
      original = evt.detail;
   });

   { // modify bob's vcard
      XmppVCardDetail vcard = original;
      vcard.desc = bob.config.bare();
      std::ifstream ifs ((TestEnvironmentConfig::testResourcePath() + "vcard_photo.png").c_str(), std::ios_base::binary);
      ASSERT_TRUE(ifs.is_open());
      ifs.seekg( 0, std::ios::end);
      std::streamoff size = ifs.tellg();
      ifs.seekg( 0, std::ios::beg);
      char* buffer = new char [size];
      ifs.read(buffer, size);
      vcard.photo.type = "image/png";
      vcard.photo.binval.assign(buffer, buffer + size);
      bob.vcardManager->storeVCard(bob.vcardHandle, vcard);
      
      assertXmppVCardOperationResult(bob, [&](const VCardOperationResultEvent& evt)
      {
         ASSERT_EQ(bob.handle, evt.account);
         ASSERT_EQ(bob.vcardHandle, evt.handle);
         ASSERT_EQ(evt.result, 0);
      });
      delete[] buffer;
   }

   bob.account->publishPresence(bob.handle, PresenceType_Available);

   assertXmppVCardFetched(alice, [&](const VCardFetchedEvent& evt)
   {
      ASSERT_EQ(alice.handle, evt.account);
      ASSERT_EQ(alice.vcardHandle, evt.handle);
      ASSERT_EQ(bob.config.bare(), evt.jid);
   });

   bob.vcardManager->storeVCard(bob.vcardHandle, original);
   assertXmppVCardOperationResult(bob, [&](const VCardOperationResultEvent& evt)
   {
      ASSERT_EQ(bob.handle, evt.account);
      ASSERT_EQ(bob.vcardHandle, evt.handle);
      ASSERT_EQ(evt.result, 0);
   });
   
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   bob.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
}

TEST_F(XmppVCardModuleTest, DISABLED_FetchEmptyVCard)
{
   XmppTestAccount alice("alice", Account_NoInit);
   alice.config.settings.enableXmppPresence = false; // Block presence triggered auto-fetched vcard events
   alice.enable();

   assertXmppVCardFetched(alice, [&](const VCardFetchedEvent& evt)
   {
      ASSERT_EQ(alice.handle, evt.account);
      ASSERT_EQ(alice.vcardHandle, evt.handle);
      ASSERT_EQ(alice.config.bare(), evt.jid);
   });

   XmppTestAccount bob("bob", Account_NoInit);
   XmppTestAccount carol("carol", Account_NoInit);

   alice.vcardManager->fetchVCard(alice.vcardHandle, carol.config.bare());
   assertXmppVCardFetched(alice, [&](const VCardFetchedEvent& evt)
   {
      ASSERT_EQ(alice.handle, evt.account);
      ASSERT_EQ(alice.vcardHandle, evt.handle);
      ASSERT_EQ(carol.config.bare(), evt.jid);
   });
   
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
}

TEST_F(XmppVCardModuleTest, DISABLED_FetchInvalidJid)
{
   XmppTestAccount alice("alice", Account_NoInit);
   alice.config.settings.enableXmppPresence = false; // Block presence triggered auto-fetched vcard events
   alice.enable();

   assertXmppVCardFetched(alice, [&](const VCardFetchedEvent& evt)
   {
      ASSERT_EQ(alice.handle, evt.account);
      ASSERT_EQ(alice.vcardHandle, evt.handle);
      ASSERT_EQ(alice.config.bare(), evt.jid);
   });

   cpc::string invalidJid = "invalid jid@" + alice.config.settings.domain;

   alice.vcardManager->fetchVCard(alice.vcardHandle, invalidJid);
   assertXmppVCardOperationResult(alice, [&](const VCardOperationResultEvent& evt)
   {
      ASSERT_EQ(alice.handle, evt.account);
      ASSERT_EQ(alice.vcardHandle, evt.handle);
      ASSERT_EQ(invalidJid, evt.jid); // empty "from"??
      ASSERT_NE(evt.result, 0);
      ASSERT_EQ(0, evt.type);
   });
   
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
}

TEST_F(XmppVCardModuleTest, DISABLED_Phonetic)
{
   XmppTestAccount alice("alice", Account_NoInit);
   alice.config.settings.enableXmppPresence = false; // Block presence triggered auto-fetched vcard events
   alice.enable();

   XmppVCardDetail vcard;

   assertXmppVCardFetched(alice, [&](const VCardFetchedEvent& evt)
   {
      ASSERT_EQ(alice.handle, evt.account);
      ASSERT_EQ(alice.vcardHandle, evt.handle);
      ASSERT_EQ(alice.config.bare(), evt.jid);
      vcard = evt.detail;
   });

   vcard.phonetic = "phonetic";

   alice.vcardManager->storeVCard(alice.vcardHandle, vcard);
   assertXmppVCardOperationResult(alice, [&](const VCardOperationResultEvent& evt)
   {
      ASSERT_EQ(alice.handle, evt.account);
      ASSERT_EQ(alice.vcardHandle, evt.handle);
      ASSERT_EQ("", evt.jid); // empty "from"??
      ASSERT_EQ(evt.result, 0);
   });

   alice.vcardManager->fetchVCard(alice.vcardHandle, alice.config.bare());
   assertXmppVCardFetched(alice, [&](const VCardFetchedEvent& evt)
   {
      ASSERT_EQ(alice.handle, evt.account);
      ASSERT_EQ(alice.vcardHandle, evt.handle);
      ASSERT_EQ(alice.config.bare(), evt.jid);
   });

   std::cin.get();
   
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
}

} // namespace

#endif
