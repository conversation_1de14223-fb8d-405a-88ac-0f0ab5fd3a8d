#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#include "cpcapi2_test_fixture.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>

#include <cpcstl/string.h>
#include <string>
#include <sstream>
#include <thread>
#include <future>
#include <memory>

#include "analytics1/RegistryUtils.h"
#include <utils/msrp_tree.h>


using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::Analytics;

namespace {

   class RegistryUtilsModuleTest : public CpcapiAutoTest
   {
   public:
      RegistryUtilsModuleTest() {}
      virtual ~RegistryUtilsModuleTest() {}
   };

   TEST_F(RegistryUtilsModuleTest, RegistryUtilsList_RemoveOne)
   {
      msrp_tree *registry = msrp_tree_create();
      std::string activityRoot;
      std::string itemPath;
      std::string attrValue;

      // create a list containing one item

      ASSERT_FALSE(RegistryUtils::listExists(registry, "test_path", "list1", activityRoot));
      ASSERT_TRUE(RegistryUtils::createList(registry, "test_path", "list1", activityRoot));
      ASSERT_EQ(activityRoot, "test_path/list1_list");
      ASSERT_TRUE(RegistryUtils::listExists(registry, "test_path", "list1", activityRoot));
      ASSERT_EQ(RegistryUtils::getListSize(registry, "test_path/list1_list"), 0);

      ASSERT_TRUE(RegistryUtils::addListItem(registry, "test_path/list1_list", itemPath));
      ASSERT_EQ(itemPath, "test_path/list1_list/0/list1");
      ASSERT_TRUE(RegistryUtils::setAttribute(registry, itemPath.c_str(), "ID", "1.1"));

      ASSERT_EQ(RegistryUtils::getListSize(registry, "test_path/list1_list"), 1);

      ASSERT_TRUE(RegistryUtils::getListItem(registry, "test_path/list1_list", 0, itemPath));
      ASSERT_EQ(itemPath, "test_path/list1_list/0/list1");
      ASSERT_TRUE(RegistryUtils::getAttribute(registry, itemPath.c_str(), "ID", attrValue));
      ASSERT_EQ(attrValue, "1.1");

      // remove the only item

      ASSERT_TRUE(RegistryUtils::erase(registry, "test_path/list1_list/0"));  // NOTE: we must use the path only up to the index when erasing
      ASSERT_EQ(RegistryUtils::getListSize(registry, "test_path/list1_list"), 0);

      msrp_tree_destroy(registry);
   }

   TEST_F(RegistryUtilsModuleTest, RegistryUtilsList_RemoveAndAdd)
   {
      msrp_tree *registry = msrp_tree_create();
      std::string activityRoot;
      std::string itemPath;
      std::string attrValue;

      // create a list containing two items

      ASSERT_FALSE(RegistryUtils::listExists(registry, "test_path", "list1", activityRoot));
      ASSERT_TRUE(RegistryUtils::createList(registry, "test_path", "list1", activityRoot));
      ASSERT_EQ(activityRoot, "test_path/list1_list");
      ASSERT_TRUE(RegistryUtils::listExists(registry, "test_path", "list1", activityRoot));
      ASSERT_EQ(RegistryUtils::getListSize(registry, "test_path/list1_list"), 0);

      ASSERT_TRUE(RegistryUtils::addListItem(registry, "test_path/list1_list", itemPath));
      ASSERT_EQ(itemPath, "test_path/list1_list/0/list1");
      ASSERT_TRUE(RegistryUtils::setAttribute(registry, itemPath.c_str(), "ID", "1.1"));

      ASSERT_TRUE(RegistryUtils::addListItem(registry, "test_path/list1_list", itemPath));
      ASSERT_EQ(itemPath, "test_path/list1_list/1/list1");
      ASSERT_TRUE(RegistryUtils::setAttribute(registry, itemPath.c_str(), "ID", "1.2"));

      ASSERT_EQ(RegistryUtils::getListSize(registry, "test_path/list1_list"), 2);

      ASSERT_TRUE(RegistryUtils::getListItem(registry, "test_path/list1_list", 0, itemPath));
      ASSERT_EQ(itemPath, "test_path/list1_list/0/list1");
      ASSERT_TRUE(RegistryUtils::getAttribute(registry, itemPath.c_str(), "ID", attrValue));
      ASSERT_EQ(attrValue, "1.1");

      ASSERT_TRUE(RegistryUtils::getListItem(registry, "test_path/list1_list", 1, itemPath));
      ASSERT_EQ(itemPath, "test_path/list1_list/1/list1");
      ASSERT_TRUE(RegistryUtils::getAttribute(registry, itemPath.c_str(), "ID", attrValue));
      ASSERT_EQ(attrValue, "1.2");

      // remove the first item in a list of size 2 which will force the renaming of the second items path

      ASSERT_TRUE(RegistryUtils::erase(registry, "test_path/list1_list/0"));  // NOTE: we must use the path only up to the index when erasing
      ASSERT_EQ(RegistryUtils::getListSize(registry, "test_path/list1_list"), 1);

      // make sure we can access the second item using the new path

      ASSERT_TRUE(RegistryUtils::getListItem(registry, "test_path/list1_list", 0, itemPath));
      ASSERT_EQ(itemPath, "test_path/list1_list/0/list1");
      ASSERT_TRUE(RegistryUtils::getAttribute(registry, itemPath.c_str(), "ID", attrValue));
      ASSERT_EQ(attrValue, "1.2");

      // add a new item which should take over the old index of the original second item

      ASSERT_TRUE(RegistryUtils::addListItem(registry, "test_path/list1_list", itemPath));
      ASSERT_EQ(itemPath, "test_path/list1_list/1/list1");
      ASSERT_TRUE(RegistryUtils::setAttribute(registry, itemPath.c_str(), "ID", "1.3"));

      ASSERT_EQ(RegistryUtils::getListSize(registry, "test_path/list1_list"), 2);

      // check that we can access both items as expected

      ASSERT_TRUE(RegistryUtils::getListItem(registry, "test_path/list1_list", 0, itemPath));
      ASSERT_EQ(itemPath, "test_path/list1_list/0/list1");
      ASSERT_TRUE(RegistryUtils::getAttribute(registry, itemPath.c_str(), "ID", attrValue));
      ASSERT_EQ(attrValue, "1.2");

      ASSERT_TRUE(RegistryUtils::getListItem(registry, "test_path/list1_list", 1, itemPath));
      ASSERT_EQ(itemPath, "test_path/list1_list/1/list1");
      ASSERT_TRUE(RegistryUtils::getAttribute(registry, itemPath.c_str(), "ID", attrValue));
      ASSERT_EQ(attrValue, "1.3");

      msrp_tree_destroy(registry);
   }

   TEST_F(RegistryUtilsModuleTest, RegistryUtilsList_RemoveFromFront)
   {
      msrp_tree *registry = msrp_tree_create();
      std::string activityRoot;
      std::string itemPath;
      std::string attrValue;

      // create a list containing two items

      ASSERT_FALSE(RegistryUtils::listExists(registry, "test_path", "list1", activityRoot));
      ASSERT_TRUE(RegistryUtils::createList(registry, "test_path", "list1", activityRoot));
      ASSERT_EQ(activityRoot, "test_path/list1_list");
      ASSERT_TRUE(RegistryUtils::listExists(registry, "test_path", "list1", activityRoot));
      ASSERT_EQ(RegistryUtils::getListSize(registry, "test_path/list1_list"), 0);

      ASSERT_TRUE(RegistryUtils::addListItem(registry, "test_path/list1_list", itemPath));
      ASSERT_EQ(itemPath, "test_path/list1_list/0/list1");
      ASSERT_TRUE(RegistryUtils::setAttribute(registry, itemPath.c_str(), "ID", "1.1"));

      ASSERT_TRUE(RegistryUtils::addListItem(registry, "test_path/list1_list", itemPath));
      ASSERT_EQ(itemPath, "test_path/list1_list/1/list1");
      ASSERT_TRUE(RegistryUtils::setAttribute(registry, itemPath.c_str(), "ID", "1.2"));

      ASSERT_EQ(RegistryUtils::getListSize(registry, "test_path/list1_list"), 2);

      // make sure we can access the two items as expected

      ASSERT_TRUE(RegistryUtils::getListItem(registry, "test_path/list1_list", 0, itemPath));
      ASSERT_EQ(itemPath, "test_path/list1_list/0/list1");
      ASSERT_TRUE(RegistryUtils::getAttribute(registry, itemPath.c_str(), "ID", attrValue));
      ASSERT_EQ(attrValue, "1.1");

      ASSERT_TRUE(RegistryUtils::getListItem(registry, "test_path/list1_list", 1, itemPath));
      ASSERT_EQ(itemPath, "test_path/list1_list/1/list1");
      ASSERT_TRUE(RegistryUtils::getAttribute(registry, itemPath.c_str(), "ID", attrValue));
      ASSERT_EQ(attrValue, "1.2");

      // remove the items front to back, checking that the first item changes as expected

      ASSERT_TRUE(RegistryUtils::getListItem(registry, "test_path/list1_list", 0, itemPath));
      ASSERT_EQ(itemPath, "test_path/list1_list/0/list1");
      ASSERT_TRUE(RegistryUtils::getAttribute(registry, itemPath.c_str(), "ID", attrValue));
      ASSERT_EQ(attrValue, "1.1");

      ASSERT_TRUE(RegistryUtils::erase(registry, "test_path/list1_list/0"));  // NOTE: we must use the path only up to the index when erasing
      ASSERT_EQ(RegistryUtils::getListSize(registry, "test_path/list1_list"), 1);

      ASSERT_TRUE(RegistryUtils::getListItem(registry, "test_path/list1_list", 0, itemPath));
      ASSERT_EQ(itemPath, "test_path/list1_list/0/list1");
      ASSERT_TRUE(RegistryUtils::getAttribute(registry, itemPath.c_str(), "ID", attrValue));
      ASSERT_EQ(attrValue, "1.2");

      ASSERT_TRUE(RegistryUtils::erase(registry, "test_path/list1_list/0"));  // NOTE: we must use the path only up to the index when erasing
      ASSERT_EQ(RegistryUtils::getListSize(registry, "test_path/list1_list"), 0);

      msrp_tree_destroy(registry);
   }

   TEST_F(RegistryUtilsModuleTest, RegistryUtilsList_RemoveFromEnd)
   {
      msrp_tree *registry = msrp_tree_create();
      std::string activityRoot;
      std::string itemPath;
      std::string attrValue;

      // create a list containing two items

      ASSERT_FALSE(RegistryUtils::listExists(registry, "test_path", "list1", activityRoot));
      ASSERT_TRUE(RegistryUtils::createList(registry, "test_path", "list1", activityRoot));
      ASSERT_EQ(activityRoot, "test_path/list1_list");
      ASSERT_TRUE(RegistryUtils::listExists(registry, "test_path", "list1", activityRoot));
      ASSERT_EQ(RegistryUtils::getListSize(registry, "test_path/list1_list"), 0);

      ASSERT_TRUE(RegistryUtils::addListItem(registry, "test_path/list1_list", itemPath));
      ASSERT_EQ(itemPath, "test_path/list1_list/0/list1");
      ASSERT_TRUE(RegistryUtils::setAttribute(registry, itemPath.c_str(), "ID", "1.1"));

      ASSERT_TRUE(RegistryUtils::addListItem(registry, "test_path/list1_list", itemPath));
      ASSERT_EQ(itemPath, "test_path/list1_list/1/list1");
      ASSERT_TRUE(RegistryUtils::setAttribute(registry, itemPath.c_str(), "ID", "1.2"));

      ASSERT_EQ(RegistryUtils::getListSize(registry, "test_path/list1_list"), 2);

      // make sure we can access the two items as expected

      ASSERT_TRUE(RegistryUtils::getListItem(registry, "test_path/list1_list", 0, itemPath));
      ASSERT_EQ(itemPath, "test_path/list1_list/0/list1");
      ASSERT_TRUE(RegistryUtils::getAttribute(registry, itemPath.c_str(), "ID", attrValue));
      ASSERT_EQ(attrValue, "1.1");

      ASSERT_TRUE(RegistryUtils::getListItem(registry, "test_path/list1_list", 1, itemPath));
      ASSERT_EQ(itemPath, "test_path/list1_list/1/list1");
      ASSERT_TRUE(RegistryUtils::getAttribute(registry, itemPath.c_str(), "ID", attrValue));
      ASSERT_EQ(attrValue, "1.2");

      // remove the items back to front, checking that the first item does not change

      ASSERT_TRUE(RegistryUtils::getListItem(registry, "test_path/list1_list", 0, itemPath));
      ASSERT_EQ(itemPath, "test_path/list1_list/0/list1");
      ASSERT_TRUE(RegistryUtils::getAttribute(registry, itemPath.c_str(), "ID", attrValue));
      ASSERT_EQ(attrValue, "1.1");

      ASSERT_TRUE(RegistryUtils::erase(registry, "test_path/list1_list/1"));  // NOTE: we must use the path only up to the index when erasing
      ASSERT_EQ(RegistryUtils::getListSize(registry, "test_path/list1_list"), 1);

      ASSERT_TRUE(RegistryUtils::getListItem(registry, "test_path/list1_list", 0, itemPath));
      ASSERT_EQ(itemPath, "test_path/list1_list/0/list1");
      ASSERT_TRUE(RegistryUtils::getAttribute(registry, itemPath.c_str(), "ID", attrValue));
      ASSERT_EQ(attrValue, "1.1");

      ASSERT_TRUE(RegistryUtils::erase(registry, "test_path/list1_list/0"));  // NOTE: we must use the path only up to the index when erasing
      ASSERT_EQ(RegistryUtils::getListSize(registry, "test_path/list1_list"), 0);

      msrp_tree_destroy(registry);
   }

}  // namespace
