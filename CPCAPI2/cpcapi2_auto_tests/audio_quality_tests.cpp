#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#include "cpcapi2_test_fixture.h"
#include "test_call_events.h"
#include "test_events.h"
#include "test_account_events.h"
#include <fstream>
#include <boost/algorithm/string.hpp>
#include "speech_quality_test.h"

using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::SipConversation;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::Media;
using namespace std;

// likely need to go with virtual audio devices to get higher.
static const float kMoslqoSucceedMin = 4.2;

// you can disable this to skip testing of MOS
#ifdef _WIN32
#define HAVE_AUDIO_QUALIY_HARDWARE_SETUP 1
#endif

static std::string currentTestOutputWavFilename(const std::string& currentTestNameOrig)
{
   std::string currentTestName = currentTestNameOrig;

   std::stringstream ss;
   // handle slash characters from gtest parameterized test names
   boost::replace_all(currentTestName, "/", "-");
   ss << "AudioQualityTests." << currentTestName << ".wav";
   return ss.str();
}

static std::string currentTestSummaryFilename(const std::string& currentTestNameOrig)
{
   std::string currentTestName = currentTestNameOrig;

   std::stringstream ss;
   // handle slash characters from gtest parameterized test names
   boost::replace_all(currentTestName, "/", "-");
   ss << "AudioQualityTests." << currentTestName << "_toolSummary.txt";
   return ss.str();
}


class AudioQualityTests : public CpcapiAutoTest
{
public:
   AudioQualityTests() {}
   virtual ~AudioQualityTests() {}
   void SetUp() override
   {
      if (!TestEnvironmentConfig::includeAudioQualityTests())
      {
         GTEST_SKIP() << "Skipping audio quality tests; includeAudioQualityTests() returned false";
      }
   }
};

#ifdef HAVE_AUDIO_QUALIY_HARDWARE_SETUP
TEST_F(AudioQualityTests, MuteTest) {
   TestAccount alice("alice");
   TestAccount bob("bob");
   CPCAPI2::Media::AudioDeviceListUpdatedEvent evt;
   int handle = 0;
   ASSERT_EQ(alice.audio->queryDeviceList(), kSuccess);
   ASSERT_TRUE(alice.mediaEvents->expectEvent("AudioHandler::onAudioDeviceListUpdated", 30000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
   ASSERT_NE(evt.deviceInfo.size(), 0);
   int numRenderDevs = evt.deviceInfo.size();
   AudioDeviceInfo defaultDeviceInfo;

   for (int i = 0; i<numRenderDevs; i++)
   {
      defaultDeviceInfo = evt.deviceInfo[i];
      if (evt.deviceInfo[i].deviceType != CPCAPI2::Media::MediaDeviceType_Render)
      {
         continue;
      }

      if (defaultDeviceInfo.defaultSystemDevice)
      {
         break;
      }
   }

   alice.audio->setRenderDevice(defaultDeviceInfo.id, AudioDeviceRole_Headset);
   bob.audio->setCaptureDevice(defaultDeviceInfo.id, AudioDeviceRole_Headset);
   alice.enableOnlyThisCodec("OPUS");

   alice.audio->setSpeakerVolume(100);
   alice.audio->setEchoCancellationMode(AudioDeviceRole_Headset, EchoCancellationMode_None);
   alice.audio->setNoiseSuppressionMode(AudioDeviceRole_Headset, NoiseSuppressionMode_None);
   bob.audio->setEchoCancellationMode(AudioDeviceRole_Headset, EchoCancellationMode_None);
   bob.audio->setNoiseSuppressionMode(AudioDeviceRole_Headset, NoiseSuppressionMode_None);

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);
   bob.audio->setMicMute(true);

   alice.audio->setMicMute(true);

   SpeechQualityTestConfig speechQualityTestConfig;
   speechQualityTestConfig.outputSummaryFilename = currentTestSummaryFilename(test_info_->name());
   speechQualityTestConfig.outputWavFilename = currentTestOutputWavFilename(test_info_->name());
   SpeechQualityTestRunner speechQualityTestRunner;
   speechQualityTestRunner.configure(speechQualityTestConfig);


   auto aliceEvents = std::async(std::launch::async, [&]() {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      SpeechQualityTestResult tr = speechQualityTestRunner.runTest();
      ASSERT_EQ(tr.exception, false);

      // for whatever reason, the current tool doesn't give 0 scores for a near silent test file..
      ASSERT_LE(tr.moslqo, 1.8);
      ASSERT_LE(tr.pesqmos, 1.8);

      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      SipConversationHandle bobCall;

      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      // wait an additional 30 seconds, since the pesq tool takes much longer on empty WAV files
      std::this_thread::sleep_for(std::chrono::milliseconds((speechQualityTestConfig.referenceWavLengthSeconds + 30) * 1000));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor2(aliceEvents, bobEvents);
}
#endif // HAVE_AUDIO_QUALIY_HARDWARE_SETUP

#ifdef HAVE_AUDIO_QUALIY_HARDWARE_SETUP
bool OpusTest_BadLogParseFunction(const char *message, CPCAPI2::LogLevel)
{
   bool result = false;
   if (NULL != strstr(message, "Core Audio is only supported on Vista")) result = true;
   else if (NULL != strstr(message, "Core Audio is not supported")) result = true;
   else if (NULL != strstr(message, "will revert to the Wave API")) result = true;
   return result;
}

TEST_F(AudioQualityTests, OpusTest) {
   AutoTestsLogger::ScopedSetBadLogMessageCheckFunction blm(&OpusTest_BadLogParseFunction);

   TestAccount alice("alice");
   TestAccount bob("bob");
   CPCAPI2::Media::AudioDeviceListUpdatedEvent evt;
   int handle = 0;
   ASSERT_EQ(alice.audio->queryDeviceList(), kSuccess);
   ASSERT_TRUE(alice.mediaEvents->expectEvent("AudioHandler::onAudioDeviceListUpdated", 30000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
   ASSERT_NE(evt.deviceInfo.size(), 0);
   int numRenderDevs = evt.deviceInfo.size();
   AudioDeviceInfo defaultDeviceInfo;

   for (int i = 0; i<numRenderDevs; i++)
   {
      defaultDeviceInfo = evt.deviceInfo[i];
      if (evt.deviceInfo[i].deviceType != CPCAPI2::Media::MediaDeviceType_Render)
      {
         continue;
      }

      if (defaultDeviceInfo.defaultSystemDevice)
      {
         break;
      }
   }

   alice.audio->setRenderDevice(defaultDeviceInfo.id, AudioDeviceRole_Headset);
   bob.audio->setCaptureDevice(defaultDeviceInfo.id, AudioDeviceRole_Headset);
   alice.enableOnlyThisCodec("OPUS");

   alice.audio->setSpeakerVolume(100);
   alice.audio->setEchoCancellationMode(AudioDeviceRole_Headset, EchoCancellationMode_None);
   alice.audio->setNoiseSuppressionMode(AudioDeviceRole_Headset, NoiseSuppressionMode_None);
   bob.audio->setEchoCancellationMode(AudioDeviceRole_Headset, EchoCancellationMode_None);
   bob.audio->setNoiseSuppressionMode(AudioDeviceRole_Headset, NoiseSuppressionMode_None);

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);
   bob.audio->setMicMute(true);

   SpeechQualityTestConfig speechQualityTestConfig;
   speechQualityTestConfig.outputSummaryFilename = currentTestSummaryFilename(test_info_->name());
   speechQualityTestConfig.outputWavFilename = currentTestOutputWavFilename(test_info_->name());
   SpeechQualityTestRunner speechQualityTestRunner;
   speechQualityTestRunner.configure(speechQualityTestConfig);


   auto aliceEvents = std::async(std::launch::async, [&]() {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      // Start recording with audio playback/record utility when connected
      //Start audio playback/record utility

      SpeechQualityTestResult tr = speechQualityTestRunner.runTest();
      ASSERT_EQ(tr.exception, false);
      ASSERT_GE(tr.moslqo, kMoslqoSucceedMin);

      //std::this_thread::sleep_for(std::chrono::milliseconds(55000));
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      SipConversationHandle bobCall;

      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds((speechQualityTestConfig.referenceWavLengthSeconds) * 1000));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor2(aliceEvents, bobEvents);
}
#endif // HAVE_AUDIO_QUALIY_HARDWARE_SETUP

#ifdef HAVE_AUDIO_QUALIY_HARDWARE_SETUP
TEST_F(AudioQualityTests, OpusDtxOffTest) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   OpusConfig opusConfig;
   opusConfig.enableDtx = false;
   alice.audio->setCodecConfig(opusConfig);
   bob.audio->setCodecConfig(opusConfig);

   CPCAPI2::Media::AudioDeviceListUpdatedEvent evt;
   int handle = 0;
   ASSERT_EQ(alice.audio->queryDeviceList(), kSuccess);
   ASSERT_TRUE(alice.mediaEvents->expectEvent("AudioHandler::onAudioDeviceListUpdated", 30000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
   ASSERT_NE(evt.deviceInfo.size(), 0);
   int numRenderDevs = evt.deviceInfo.size();
   AudioDeviceInfo defaultDeviceInfo;

   for (int i = 0; i < numRenderDevs; i++)
   {
      defaultDeviceInfo = evt.deviceInfo[i];
      if (evt.deviceInfo[i].deviceType != CPCAPI2::Media::MediaDeviceType_Render)
      {
         continue;
      }

      if (defaultDeviceInfo.defaultSystemDevice)
      {
         break;
      }
   }

   alice.audio->setRenderDevice(defaultDeviceInfo.id, AudioDeviceRole_Headset);
   bob.audio->setCaptureDevice(defaultDeviceInfo.id, AudioDeviceRole_Headset);
   alice.enableOnlyThisCodec("OPUS");

   alice.audio->setSpeakerVolume(100);
   alice.audio->setEchoCancellationMode(AudioDeviceRole_Headset, EchoCancellationMode_None);
   alice.audio->setNoiseSuppressionMode(AudioDeviceRole_Headset, NoiseSuppressionMode_None);
   bob.audio->setEchoCancellationMode(AudioDeviceRole_Headset, EchoCancellationMode_None);
   bob.audio->setNoiseSuppressionMode(AudioDeviceRole_Headset, NoiseSuppressionMode_None);

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);
   bob.audio->setMicMute(true);

   SpeechQualityTestConfig speechQualityTestConfig;
   speechQualityTestConfig.outputSummaryFilename = currentTestSummaryFilename(test_info_->name());
   speechQualityTestConfig.outputWavFilename = currentTestOutputWavFilename(test_info_->name());
   SpeechQualityTestRunner speechQualityTestRunner;
   speechQualityTestRunner.configure(speechQualityTestConfig);


   auto aliceEvents = std::async(std::launch::async, [&]() {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      // Start recording with audio playback/record utility when connected
      //Start audio playback/record utility

      SpeechQualityTestResult tr = speechQualityTestRunner.runTest();
      ASSERT_EQ(tr.exception, false);
      ASSERT_GE(tr.moslqo, kMoslqoSucceedMin);

      //std::this_thread::sleep_for(std::chrono::milliseconds(55000));
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      SipConversationHandle bobCall;

      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds((speechQualityTestConfig.referenceWavLengthSeconds) * 1000));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor2(aliceEvents, bobEvents);
}
#endif // HAVE_AUDIO_QUALIY_HARDWARE_SETUP

#ifdef HAVE_AUDIO_QUALIY_HARDWARE_SETUP
TEST_F(AudioQualityTests, G729Test_WithAnnexB) {
   TestAccount alice("alice");
   TestAccount bob("bob");
   CPCAPI2::Media::AudioDeviceListUpdatedEvent evt;
   int handle = 0;
   ASSERT_EQ(alice.audio->queryDeviceList(), kSuccess);
   ASSERT_TRUE(alice.mediaEvents->expectEvent("AudioHandler::onAudioDeviceListUpdated", 30000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
   ASSERT_NE(evt.deviceInfo.size(), 0);
   int numRenderDevs = evt.deviceInfo.size();
   AudioDeviceInfo defaultDeviceInfo;

   for (int i = 0; i<numRenderDevs; i++)
   {
      defaultDeviceInfo = evt.deviceInfo[i];
      if (evt.deviceInfo[i].deviceType != CPCAPI2::Media::MediaDeviceType_Render)
      {
         continue;
      }

      if (defaultDeviceInfo.defaultSystemDevice)
      {
         break;
      }
   }

   alice.audio->setRenderDevice(defaultDeviceInfo.id, AudioDeviceRole_Headset);
   bob.audio->setCaptureDevice(defaultDeviceInfo.id, AudioDeviceRole_Headset);
   alice.enableOnlyThisCodec("G.729");

   CPCAPI2::Media::G729Config g729config;   
   g729config.useAnnexB = true;
   alice.audio->setCodecConfig(g729config);
   bob.audio->setCodecConfig(g729config);

   alice.audio->setSpeakerVolume(100);
   alice.audio->setEchoCancellationMode(AudioDeviceRole_Headset, EchoCancellationMode_None);
   alice.audio->setNoiseSuppressionMode(AudioDeviceRole_Headset, NoiseSuppressionMode_None);
   bob.audio->setEchoCancellationMode(AudioDeviceRole_Headset, EchoCancellationMode_None);
   bob.audio->setNoiseSuppressionMode(AudioDeviceRole_Headset, NoiseSuppressionMode_None);

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);
   bob.audio->setMicMute(true);

   SpeechQualityTestConfig speechQualityTestConfig;
   speechQualityTestConfig.outputSummaryFilename = currentTestSummaryFilename(test_info_->name());
   speechQualityTestConfig.outputWavFilename = currentTestOutputWavFilename(test_info_->name());
   SpeechQualityTestRunner speechQualityTestRunner;
   speechQualityTestRunner.configure(speechQualityTestConfig);


   auto aliceEvents = std::async(std::launch::async, [&]() {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      // Start recording with audio playback/record utility when connected
      //Start audio playback/record utility

      SpeechQualityTestResult tr = speechQualityTestRunner.runTest();
      ASSERT_EQ(tr.exception, false);
      ASSERT_GE(tr.moslqo, 3.1);

      //std::this_thread::sleep_for(std::chrono::milliseconds(55000));
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      SipConversationHandle bobCall;

      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds((speechQualityTestConfig.referenceWavLengthSeconds) * 1000));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor2(aliceEvents, bobEvents);
}
#endif // HAVE_AUDIO_QUALIY_HARDWARE_SETUP

#ifdef HAVE_AUDIO_QUALIY_HARDWARE_SETUP
TEST_F(AudioQualityTests, G729Test_NoAnnexB) {
   TestAccount alice("alice");
   TestAccount bob("bob");
   CPCAPI2::Media::AudioDeviceListUpdatedEvent evt;
   int handle = 0;
   ASSERT_EQ(alice.audio->queryDeviceList(), kSuccess);
   ASSERT_TRUE(alice.mediaEvents->expectEvent("AudioHandler::onAudioDeviceListUpdated", 30000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
   ASSERT_NE(evt.deviceInfo.size(), 0);
   int numRenderDevs = evt.deviceInfo.size();
   AudioDeviceInfo defaultDeviceInfo;

   for (int i = 0; i<numRenderDevs; i++)
   {
      defaultDeviceInfo = evt.deviceInfo[i];
      if (evt.deviceInfo[i].deviceType != CPCAPI2::Media::MediaDeviceType_Render)
      {
         continue;
      }

      if (defaultDeviceInfo.defaultSystemDevice)
      {
         break;
      }
   }

   alice.audio->setRenderDevice(defaultDeviceInfo.id, AudioDeviceRole_Headset);
   bob.audio->setCaptureDevice(defaultDeviceInfo.id, AudioDeviceRole_Headset);
   alice.enableOnlyThisCodec("G.729");

   CPCAPI2::Media::G729Config g729config;   
   g729config.useAnnexB = false;
   alice.audio->setCodecConfig(g729config);
   bob.audio->setCodecConfig(g729config);

   alice.audio->setSpeakerVolume(100);
   alice.audio->setEchoCancellationMode(AudioDeviceRole_Headset, EchoCancellationMode_None);
   alice.audio->setNoiseSuppressionMode(AudioDeviceRole_Headset, NoiseSuppressionMode_None);
   bob.audio->setEchoCancellationMode(AudioDeviceRole_Headset, EchoCancellationMode_None);
   bob.audio->setNoiseSuppressionMode(AudioDeviceRole_Headset, NoiseSuppressionMode_None);

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);
   bob.audio->setMicMute(true);

   SpeechQualityTestConfig speechQualityTestConfig;
   speechQualityTestConfig.outputSummaryFilename = currentTestSummaryFilename(test_info_->name());
   speechQualityTestConfig.outputWavFilename = currentTestOutputWavFilename(test_info_->name());
   SpeechQualityTestRunner speechQualityTestRunner;
   speechQualityTestRunner.configure(speechQualityTestConfig);


   auto aliceEvents = std::async(std::launch::async, [&]() {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      // Start recording with audio playback/record utility when connected
      //Start audio playback/record utility

      SpeechQualityTestResult tr = speechQualityTestRunner.runTest();
      ASSERT_EQ(tr.exception, false);
      ASSERT_GE(tr.moslqo, 3.1);

      //std::this_thread::sleep_for(std::chrono::milliseconds(55000));
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      SipConversationHandle bobCall;

      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds((speechQualityTestConfig.referenceWavLengthSeconds) * 1000));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor2(aliceEvents, bobEvents);
}
#endif // HAVE_AUDIO_QUALIY_HARDWARE_SETUP


TEST_F(AudioQualityTests, DISABLED_FileOpusTest) {
   TestAccount alice("alice");
   TestAccount bob("bob");
   
   CPCAPI2::Media::AudioDeviceListUpdatedEvent evt;
   int handle = 0;
   ASSERT_EQ(alice.audio->queryDeviceList(), kSuccess);
   ASSERT_TRUE(alice.mediaEvents->expectEvent("AudioHandler::onAudioDeviceListUpdated", 30000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
   ASSERT_NE(evt.deviceInfo.size(), 0);

   alice.enableOnlyThisCodec("OPUS");

   MediaStackSettings mediaStackSettings;
   mediaStackSettings.audioLayer = AudioLayers_Dummy;
   alice.media->updateMediaSettings(mediaStackSettings);
   bob.media->updateMediaSettings(mediaStackSettings);

   bob.audio->setSpeakerVolume(100);
   alice.audio->setEchoCancellationMode(AudioDeviceRole_Headset, EchoCancellationMode_None);
   alice.audio->setNoiseSuppressionMode(AudioDeviceRole_Headset, NoiseSuppressionMode_None);
   bob.audio->setEchoCancellationMode(AudioDeviceRole_Headset, EchoCancellationMode_None);
   bob.audio->setNoiseSuppressionMode(AudioDeviceRole_Headset, NoiseSuppressionMode_None);

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   MediaInfo aliceMedia;
   aliceMedia.mediaDirection = MediaDirection_SendOnly;
   alice.conversation->configureMedia(aliceCall, aliceMedia);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]() {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendOnly);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

#ifdef __linux__
      system("sudo tc qdisc replace dev lo root netem loss 20%");
      // sudo tc qdisc change dev lo root tbf rate 40kbit latency 5ms burst 200kbit
#endif

      alice.conversation->playSound(aliceCall, "file:/src/CounterPath/CPCAPI2/core/cpcapi2_auto_tests/PESQ/sox/ctclip3TestClip-16k.wav", true);

      std::this_thread::sleep_for(std::chrono::milliseconds(17 * 1000 * 5));
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);

#ifdef __linux__
      system("sudo tc qdisc del dev lo root");
#endif
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      SipConversationHandle bobCall;

      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      MediaInfo bobMedia;
      bobMedia.mediaDirection = MediaDirection_ReceiveOnly;
      bob.conversation->configureMedia(bobCall, bobMedia);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_ReceiveOnly);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      bob.audio->setMicMute(true);

      std::stringstream recordFileName;
      recordFileName << "AudioQualityTests.FileOpusTest-" << resip::Random::getCryptoRandomHex(6).c_str() << ".wav";
      CPCAPI2::Recording::RecorderHandle recorderHandle = bob.recording->audioRecorderCreate(recordFileName.str().c_str());
      assertSuccess(bob.recording->recorderAddConversation(recorderHandle, bobCall));
      assertSuccess(bob.recording->recorderStart(recorderHandle));

      std::this_thread::sleep_for(std::chrono::milliseconds(17 * 1000 * 5));

      assertSuccess(bob.recording->recorderDestroy(recorderHandle));

      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor2(aliceEvents, bobEvents);
}


class SrtpAudioQualityTests : public CpcapiAutoTestWithParam<CPCAPI2::SipConversation::MediaEncryptionOptions>
{
public:
   SrtpAudioQualityTests() {}
   virtual ~SrtpAudioQualityTests() {}
   void SetUp() override
   {
      if (!TestEnvironmentConfig::includeAudioQualityTests())
      {
         GTEST_SKIP() << "Skipping SRTP audio quality tests; includeAudioQualityTests() returned false";
      }
   }
};



MediaEncryptionOptions getMediaEncryptionOptions(MediaEncryptionMode encryptionMode, MediaCryptoSuite cryptoSuite)
{
   MediaEncryptionOptions encryptionOptions;
   encryptionOptions.mediaEncryptionMode = encryptionMode;
   encryptionOptions.mediaCryptoSuites.clear();
   encryptionOptions.mediaCryptoSuites.push_back(cryptoSuite);
   encryptionOptions.secureMediaRequired = true;

   return encryptionOptions;
}


INSTANTIATE_TEST_SUITE_P(AllSupportedCryptoSuites, SrtpAudioQualityTests,
   ::testing::Values( getMediaEncryptionOptions(MediaEncryptionMode_SRTP_SDES_Encrypted, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32),
                      getMediaEncryptionOptions(MediaEncryptionMode_SRTP_SDES_Encrypted, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80),
                      getMediaEncryptionOptions(MediaEncryptionMode_SRTP_SDES_Encrypted, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32),
                      getMediaEncryptionOptions(MediaEncryptionMode_SRTP_SDES_Encrypted, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80),
                      getMediaEncryptionOptions(MediaEncryptionMode_SRTP_SDES_Encrypted, MediaCryptoSuite_AES_CM_192_HMAC_SHA1_32),
                      getMediaEncryptionOptions(MediaEncryptionMode_SRTP_SDES_Encrypted, MediaCryptoSuite_AES_CM_192_HMAC_SHA1_80),
                      getMediaEncryptionOptions(MediaEncryptionMode_SRTP_SDES_Encrypted, MediaCryptoSuite_AEAD_AES_128_GCM),
                      getMediaEncryptionOptions(MediaEncryptionMode_SRTP_SDES_Encrypted, MediaCryptoSuite_AEAD_AES_256_GCM)
                      /*
                      getMediaEncryptionOptions(MediaEncryptionMode_SRTP_DTLS_Encrypted, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32),
                      getMediaEncryptionOptions(MediaEncryptionMode_SRTP_DTLS_Encrypted, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80),
                      getMediaEncryptionOptions(MediaEncryptionMode_SRTP_DTLS_Encrypted, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32),
                      getMediaEncryptionOptions(MediaEncryptionMode_SRTP_DTLS_Encrypted, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80)
                      getMediaEncryptionOptions(MediaEncryptionMode_SRTP_DTLS_Encrypted, MediaCryptoSuite_AES_CM_192_HMAC_SHA1_32),
                      getMediaEncryptionOptions(MediaEncryptionMode_SRTP_DTLS_Encrypted, MediaCryptoSuite_AES_CM_192_HMAC_SHA1_80)
                      getMediaEncryptionOptions(MediaEncryptionMode_SRTP_DTLS_Encrypted, MediaCryptoSuite_AEAD_AES_128_GCM),
                      getMediaEncryptionOptions(MediaEncryptionMode_SRTP_DTLS_Encrypted, MediaCryptoSuite_AEAD_AES_256_GCM)
                      */
                     ));


TEST_P(SrtpAudioQualityTests, ParameterizedSrtpCallTest) {

   const MediaEncryptionOptions kMediaEncryptionOptions = GetParam();

   TestAccount alice("alice");
   TestAccount bob("bob");
   CPCAPI2::Media::AudioDeviceListUpdatedEvent evt;
   int handle = 0;
   ASSERT_EQ(alice.audio->queryDeviceList(), kSuccess);
   ASSERT_TRUE(alice.mediaEvents->expectEvent("AudioHandler::onAudioDeviceListUpdated", 30000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
   ASSERT_NE(evt.deviceInfo.size(), 0);
   size_t numRenderDevs = evt.deviceInfo.size();
   AudioDeviceInfo defaultDeviceInfo;

   for (int i = 0; i<numRenderDevs; i++)
   {
      defaultDeviceInfo = evt.deviceInfo[i];
      if (evt.deviceInfo[i].deviceType != CPCAPI2::Media::MediaDeviceType_Render)
      {
         continue;
      }

      if (defaultDeviceInfo.defaultSystemDevice)
      {
         break;
      }
   }

   alice.audio->setRenderDevice(defaultDeviceInfo.id, AudioDeviceRole_Headset);
   bob.audio->setCaptureDevice(defaultDeviceInfo.id, AudioDeviceRole_Headset);
   alice.enableOnlyThisCodec("OPUS");

   alice.audio->setSpeakerVolume(100);
   alice.audio->setEchoCancellationMode(AudioDeviceRole_Headset, EchoCancellationMode_None);
   alice.audio->setNoiseSuppressionMode(AudioDeviceRole_Headset, NoiseSuppressionMode_None);
   bob.audio->setEchoCancellationMode(AudioDeviceRole_Headset, EchoCancellationMode_None);
   bob.audio->setNoiseSuppressionMode(AudioDeviceRole_Headset, NoiseSuppressionMode_None);

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   MediaInfo aliceMedia;
   aliceMedia.mediaDirection = MediaDirection_SendReceive;
   aliceMedia.mediaType = MediaType_Audio;
   aliceMedia.mediaEncryptionOptions = kMediaEncryptionOptions;
   alice.conversation->configureMedia(aliceCall, aliceMedia);
   alice.conversation->start(aliceCall);
   bob.audio->setMicMute(true);

#ifdef HAVE_AUDIO_QUALIY_HARDWARE_SETUP
   SpeechQualityTestConfig speechQualityTestConfig;
   speechQualityTestConfig.outputSummaryFilename = currentTestSummaryFilename(::testing::UnitTest::GetInstance()->current_test_info()->name());
   speechQualityTestConfig.outputWavFilename = currentTestOutputWavFilename(::testing::UnitTest::GetInstance()->current_test_info()->name());
   SpeechQualityTestRunner speechQualityTestRunner;
   speechQualityTestRunner.configure(speechQualityTestConfig);
#endif // HAVE_AUDIO_QUALIY_HARDWARE_SETUP

   auto aliceEvents = std::async(std::launch::async, [&]() {
      assertNewConversationOutgoing_ex(alice, aliceCall, bob.config.uri(), [kMediaEncryptionOptions](const NewConversationEvent& evt)
      {
         ASSERT_EQ(1, evt.localMediaInfo.size());
         ASSERT_EQ(kMediaEncryptionOptions.mediaEncryptionMode, evt.localMediaInfo[0].mediaEncryptionOptions.mediaEncryptionMode);
         ASSERT_TRUE(evt.localMediaInfo[0].mediaEncryptionOptions.secureMediaRequired);
         ASSERT_EQ(1, evt.localMediaInfo[0].mediaEncryptionOptions.mediaCryptoSuites.size());
         ASSERT_EQ(*kMediaEncryptionOptions.mediaCryptoSuites.begin(), *evt.localMediaInfo[0].mediaEncryptionOptions.mediaCryptoSuites.begin());
         ASSERT_EQ(MediaCryptoSuite_None, evt.localMediaInfo[0].mediaCrypto);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);

      {
         SipConversationHandle h;
         ConversationMediaChangedEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent("SipConversationHandler::onConversationMediaChanged",
            15000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
         ASSERT_EQ((size_t)1, evt.localMediaInfo.size());
         for (cpc::vector<MediaInfo>::const_iterator it = evt.localMediaInfo.begin(); it != evt.localMediaInfo.end(); it++)
         {
            ASSERT_EQ(MediaDirection_SendReceive, it->mediaDirection);
            ASSERT_EQ(kMediaEncryptionOptions.mediaEncryptionMode, it->mediaEncryptionOptions.mediaEncryptionMode);
            ASSERT_TRUE(it->mediaEncryptionOptions.secureMediaRequired);
            ASSERT_EQ(1, it->mediaEncryptionOptions.mediaCryptoSuites.size());
            ASSERT_EQ(*kMediaEncryptionOptions.mediaCryptoSuites.begin(), *it->mediaEncryptionOptions.mediaCryptoSuites.begin());
            ASSERT_EQ(*kMediaEncryptionOptions.mediaCryptoSuites.begin(), it->mediaCrypto);
         }
      }

      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      // Start recording with audio playback/record utility when connected
      //Start audio playback/record utility

#ifdef HAVE_AUDIO_QUALIY_HARDWARE_SETUP
      SpeechQualityTestResult tr = speechQualityTestRunner.runTest();
      ASSERT_EQ(tr.exception, false);
      ASSERT_GE(tr.moslqo, kMoslqoSucceedMin);
#endif

      //std::this_thread::sleep_for(std::chrono::milliseconds(55000));
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      SipConversationHandle bobCall;

      assertNewConversationIncoming_ex(bob, &bobCall, alice.config.uri(), [kMediaEncryptionOptions](const NewConversationEvent& evt)
      {
         // note: for DTLS, mediaEncryptionOptions doesn't appear to be set propertly for the incoming call.
         // RemoteParticipant::onOffer(..) seems to only set secureMediaRequired if SDES is used, and similarly for requestedCryptoSuites

         ASSERT_EQ(1, evt.remoteMediaInfo.size());
         ASSERT_EQ(kMediaEncryptionOptions.mediaEncryptionMode, evt.remoteMediaInfo[0].mediaEncryptionOptions.mediaEncryptionMode);
         ASSERT_TRUE(evt.remoteMediaInfo[0].mediaEncryptionOptions.secureMediaRequired);
         ASSERT_EQ(1, evt.remoteMediaInfo[0].mediaEncryptionOptions.mediaCryptoSuites.size());
         ASSERT_EQ(*kMediaEncryptionOptions.mediaCryptoSuites.begin(), *evt.remoteMediaInfo[0].mediaEncryptionOptions.mediaCryptoSuites.begin());
         ASSERT_EQ(MediaCryptoSuite_None, evt.remoteMediaInfo[0].mediaCrypto);
      });

      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);

      MediaInfo bobMedia;
      bobMedia.mediaDirection = MediaDirection_SendReceive;
      bobMedia.mediaType = MediaType_Audio;
      bobMedia.mediaEncryptionOptions = kMediaEncryptionOptions;
      bob.conversation->configureMedia(bobCall, bobMedia);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Audio, true);

      assertSuccess(bob.conversation->accept(bobCall));

      {
         SipConversationHandle h;
         ConversationMediaChangedEvent evt;
         ASSERT_TRUE(bob.conversationEvents->expectEvent("SipConversationHandler::onConversationMediaChanged",
            15000, HandleEqualsPred<SipConversationHandle>(bobCall), h, evt));
         ASSERT_LE((size_t)1, evt.localMediaInfo.size());
         for (cpc::vector<MediaInfo>::const_iterator it = evt.localMediaInfo.begin(); it != evt.localMediaInfo.end(); it++)
         {
            ASSERT_EQ(MediaDirection_SendReceive, it->mediaDirection);
            ASSERT_EQ(kMediaEncryptionOptions.mediaEncryptionMode, it->mediaEncryptionOptions.mediaEncryptionMode);
            ASSERT_TRUE(it->mediaEncryptionOptions.secureMediaRequired);
            ASSERT_EQ(1, it->mediaEncryptionOptions.mediaCryptoSuites.size());
            ASSERT_EQ(*kMediaEncryptionOptions.mediaCryptoSuites.begin(), *it->mediaEncryptionOptions.mediaCryptoSuites.begin());
            ASSERT_EQ(*kMediaEncryptionOptions.mediaCryptoSuites.begin(), it->mediaCrypto);
         }
      }

      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

#if HAVE_AUDIO_QUALIY_HARDWARE_SETUP
      std::this_thread::sleep_for(std::chrono::milliseconds((speechQualityTestConfig.referenceWavLengthSeconds) * 1000));
#endif // HAVE_AUDIO_QUALIY_HARDWARE_SETUP
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor2(aliceEvents, bobEvents);
}

#ifdef HAVE_AUDIO_QUALIY_HARDWARE_SETUP
TEST_F(AudioQualityTests, GsmTest) {
   TestAccount alice("alice");
   TestAccount bob("bob");
   CPCAPI2::Media::AudioDeviceListUpdatedEvent evt;
   int handle = 0;
   ASSERT_EQ(alice.audio->queryDeviceList(), kSuccess);
   ASSERT_TRUE(alice.mediaEvents->expectEvent("AudioHandler::onAudioDeviceListUpdated", 30000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
   ASSERT_NE(evt.deviceInfo.size(), 0);
   int numRenderDevs = evt.deviceInfo.size();
   AudioDeviceInfo defaultDeviceInfo;

   for (int i = 0; i < numRenderDevs; i++)
   {
      defaultDeviceInfo = evt.deviceInfo[i];
      if (evt.deviceInfo[i].deviceType != CPCAPI2::Media::MediaDeviceType_Render)
      {
         continue;
      }

      if (defaultDeviceInfo.defaultSystemDevice)
      {
         break;
      }
   }

   alice.audio->setRenderDevice(defaultDeviceInfo.id, AudioDeviceRole_Headset);
   bob.audio->setCaptureDevice(defaultDeviceInfo.id, AudioDeviceRole_Headset);
   alice.enableOnlyThisCodec("GSM");

   alice.audio->setSpeakerVolume(100);
   alice.audio->setEchoCancellationMode(AudioDeviceRole_Headset, EchoCancellationMode_None);
   alice.audio->setNoiseSuppressionMode(AudioDeviceRole_Headset, NoiseSuppressionMode_None);
   bob.audio->setEchoCancellationMode(AudioDeviceRole_Headset, EchoCancellationMode_None);
   bob.audio->setNoiseSuppressionMode(AudioDeviceRole_Headset, NoiseSuppressionMode_None);

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);
   bob.audio->setMicMute(true);

   SpeechQualityTestConfig speechQualityTestConfig;
   speechQualityTestConfig.outputSummaryFilename = currentTestSummaryFilename(test_info_->name());
   speechQualityTestConfig.outputWavFilename = currentTestOutputWavFilename(test_info_->name());
   SpeechQualityTestRunner speechQualityTestRunner;
   speechQualityTestRunner.configure(speechQualityTestConfig);


   auto aliceEvents = std::async(std::launch::async, [&]() {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      // Start recording with audio playback/record utility when connected
      //Start audio playback/record utility

      SpeechQualityTestResult tr = speechQualityTestRunner.runTest();
      ASSERT_EQ(tr.exception, false);
      ASSERT_GE(tr.moslqo, 3.2);

      //std::this_thread::sleep_for(std::chrono::milliseconds(55000));
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      SipConversationHandle bobCall;

      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds((speechQualityTestConfig.referenceWavLengthSeconds) * 1000));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor2(aliceEvents, bobEvents);
}
#endif // HAVE_AUDIO_QUALIY_HARDWARE_SETUP
