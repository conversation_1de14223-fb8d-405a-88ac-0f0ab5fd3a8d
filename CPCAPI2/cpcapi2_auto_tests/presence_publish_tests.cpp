#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#include "cpcapi2_test_fixture.h"

using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::SipEvent;
using namespace CPCAPI2::SipPresence;
using namespace CPCAPI2::SipAccount;

class PresencePublishTests : public CpcapiAutoTest
{
public:
   PresencePublishTests() {}
   virtual ~PresencePublishTests() {}
};

TEST_F(PresencePublishTests, DISABLED_BasicPublish) {

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.username = "***********";
   alice.config.settings.password = "7034";
   alice.config.settings.domain = "*************";
   alice.config.settings.outboundProxy = alice.config.settings.domain;
   alice.config.settings.sipTransportType = SipAccountTransport_TCP; 
   alice.enable();

   TestAccount bob("bob", Account_NoInit);
   bob.config.settings.username = "***********";
   bob.config.settings.password = "5252";
   bob.config.settings.domain = "*************";
   bob.config.settings.outboundProxy = bob.config.settings.domain;
   bob.config.settings.sipTransportType = SipAccountTransport_TCP; 
   bob.enable();

   // get through a full publish for bob before alice subscribes to ensure presence status is in a known state
   SipEventPublicationHandle bobPub = bob.presence->createPublication(bob.handle, SipPresencePublicationSettings());
   bob.presence->publish(bobPub, CannedStatus_Available);
   SipEventSubscriptionHandle h;
   {
      PresencePublicationSuccessEvent evt;
      ASSERT_TRUE(bob.presenceEvents->expectEvent("SipPresencePublicationHandler::onPublicationSuccess",
         15000, HandleEqualsPred<SipEventPublicationHandle>(bobPub), h, evt));
   }

   auto bobEvents = std::async(std::launch::async, [&] () {
      std::this_thread::sleep_for(std::chrono::seconds(5));
      bob.presence->publish(bobPub, CannedStatus_Busy);
      {
         PresencePublicationSuccessEvent evt;
         ASSERT_TRUE(bob.presenceEvents->expectEvent("SipPresencePublicationHandler::onPublicationSuccess",
            15000, HandleEqualsPred<SipEventPublicationHandle>(bobPub), h, evt));
      }
   });

   auto aliceEvents = std::async(std::launch::async, [&] () {

      SipEventPublicationHandle aliceSubs = alice.subs->createSubscription(alice.handle);
      {
         SipPresenceSubscriptionSettings subsSettings;
         alice.presence->applySubscriptionSettings(aliceSubs, subsSettings);
         alice.presence->addParticipant(aliceSubs, bob.config.uri());
         alice.presence->start(aliceSubs);
      }

      SipEventSubscriptionHandle h;
      {
         NewPresenceSubscriptionEvent evt;
         ASSERT_TRUE(alice.presenceEvents->expectEvent("SipPresenceSubscriptionHandler::onNewSubscription",
            15000, HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs), h, evt));
         ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Outgoing);
         ASSERT_EQ(evt.remoteAddress, bob.config.uri());
         ASSERT_EQ(evt.remoteDisplayName, "");
      }

      {
         PresenceSubscriptionStateChangedEvent evt;
         ASSERT_TRUE(alice.presenceEvents->expectEvent(
            "SipPresenceSubscriptionHandler::onSubscriptionStateChanged",
            15000, HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs), h, evt));
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      {
         IncomingPresenceStatusEvent evt;
         ASSERT_TRUE(alice.presenceEvents->expectEvent(
            "SipPresenceSubscriptionHandler::onIncomingPresenceStatus",
            15000, HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs), h, evt));
         ASSERT_EQ(CannedStatus_Available, evt.status);
      }

      {
         IncomingPresenceStatusEvent evt;
         ASSERT_TRUE(alice.presenceEvents->expectEvent(
            "SipPresenceSubscriptionHandler::onIncomingPresenceStatus",
            15000, HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs), h, evt));
         ASSERT_EQ(CannedStatus_Busy, evt.status);
      }

      alice.presence->end(aliceSubs);

      {
         PresenceSubscriptionEndedEvent evt;
         ASSERT_TRUE(alice.presenceEvents->expectEvent(
            "SipPresenceSubscriptionHandler::onSubscriptionEnded",
            15000, HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs), h, evt));
         ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }
   });

   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
}

TEST_F(PresencePublishTests, DISABLED_BasicPublishEnded) {

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.username = "***********";
   alice.config.settings.password = "7034";
   alice.config.settings.domain = "*************";
   alice.config.settings.outboundProxy = alice.config.settings.domain;
   alice.config.settings.sipTransportType = SipAccountTransport_TCP; 
   alice.enable();

   TestAccount bob("bob", Account_NoInit);
   bob.config.settings.username = "***********";
   bob.config.settings.password = "5252";
   bob.config.settings.domain = "*************";
   bob.config.settings.outboundProxy = bob.config.settings.domain;
   bob.config.settings.sipTransportType = SipAccountTransport_TCP; 
   bob.enable();

   // get through a full publish for bob before alice subscribes to ensure presence status is in a known state
   SipEventPublicationHandle bobPub = bob.presence->createPublication(bob.handle, SipPresencePublicationSettings());
   bob.presence->publish(bobPub, CannedStatus_Available);
   SipEventSubscriptionHandle h;
   {
      PresencePublicationSuccessEvent evt;
      ASSERT_TRUE(bob.presenceEvents->expectEvent("SipPresencePublicationHandler::onPublicationSuccess",
         15000, HandleEqualsPred<SipEventPublicationHandle>(bobPub), h, evt));
   }

   auto bobEvents = std::async(std::launch::async, [&] () {
      std::this_thread::sleep_for(std::chrono::seconds(5));
      bob.presence->publish(bobPub, CannedStatus_Busy);
      {
         PresencePublicationSuccessEvent evt;
         ASSERT_TRUE(bob.presenceEvents->expectEvent("SipPresencePublicationHandler::onPublicationSuccess",
            15000, HandleEqualsPred<SipEventPublicationHandle>(bobPub), h, evt));
      }

      std::this_thread::sleep_for(std::chrono::seconds(5));
      bob.presence->endPublish(bobPub);
      {
         PresencePublicationRemoveEvent evt;
         ASSERT_TRUE(bob.presenceEvents->expectEvent("SipPresencePublicationHandler::onPublicationRemove",
            15000, HandleEqualsPred<SipEventPublicationHandle>(bobPub), h, evt));
      }

   });

   auto aliceEvents = std::async(std::launch::async, [&] () {

      SipEventPublicationHandle aliceSubs = alice.subs->createSubscription(alice.handle);
      {
         SipPresenceSubscriptionSettings subsSettings;
         alice.presence->applySubscriptionSettings(aliceSubs, subsSettings);
         alice.presence->addParticipant(aliceSubs, bob.config.uri());
         alice.presence->start(aliceSubs);
      }

      SipEventSubscriptionHandle h;
      {
         NewPresenceSubscriptionEvent evt;
         ASSERT_TRUE(alice.presenceEvents->expectEvent("SipPresenceSubscriptionHandler::onNewSubscription",
            15000, HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs), h, evt));
         ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Outgoing);
         ASSERT_EQ(evt.remoteAddress, bob.config.uri());
         ASSERT_EQ(evt.remoteDisplayName, "");
      }

      {
         PresenceSubscriptionStateChangedEvent evt;
         ASSERT_TRUE(alice.presenceEvents->expectEvent(
            "SipPresenceSubscriptionHandler::onSubscriptionStateChanged",
            15000, HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs), h, evt));
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      {
         IncomingPresenceStatusEvent evt;
         ASSERT_TRUE(alice.presenceEvents->expectEvent(
            "SipPresenceSubscriptionHandler::onIncomingPresenceStatus",
            15000, HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs), h, evt));
         ASSERT_EQ(CannedStatus_Available, evt.status);
      }

      {
         IncomingPresenceStatusEvent evt;
         ASSERT_TRUE(alice.presenceEvents->expectEvent(
            "SipPresenceSubscriptionHandler::onIncomingPresenceStatus",
            15000, HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs), h, evt));
         ASSERT_EQ(CannedStatus_Busy, evt.status);
      }

      alice.presence->end(aliceSubs);

      {
         PresenceSubscriptionEndedEvent evt;
         ASSERT_TRUE(alice.presenceEvents->expectEvent(
            "SipPresenceSubscriptionHandler::onSubscriptionEnded",
            15000, HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs), h, evt));
         ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }
   });

   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
}

TEST_F(PresencePublishTests, DISABLED_MultiplePublish) {

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.username = "***********";
   alice.config.settings.password = "zExnBFXx";
   alice.config.settings.domain = "*************";
   alice.config.settings.outboundProxy = alice.config.settings.domain;
   alice.config.settings.sipTransportType = SipAccountTransport_TCP; 
   alice.enable();

   SipEventPublicationHandle alicePub = alice.presence->createPublication(alice.handle, SipPresencePublicationSettings());
   alice.presence->publish(alicePub, CannedStatus_Available);
   alice.presence->publish(alicePub, CannedStatus_Busy);

   {
      SipEventSubscriptionHandle h;
      PresencePublicationSuccessEvent evt;
      ASSERT_TRUE(alice.presenceEvents->expectEvent("SipPresencePublicationHandler::onPublicationSuccess",
         15000, HandleEqualsPred<SipEventPublicationHandle>(alicePub), h, evt));

      ASSERT_TRUE(alice.presenceEvents->expectEvent("SipPresencePublicationHandler::onPublicationSuccess",
         15000, HandleEqualsPred<SipEventPublicationHandle>(alicePub), h, evt));
   }
}

TEST_F(PresencePublishTests, PresenceRemoveHandler) {
   Phone* phone = Phone::create();
   phone->initialize(LicenseInfo(), (PhoneHandler*)NULL);

   CPCAPI2::SipAccount::SipAccountManager* acct = CPCAPI2::SipAccount::SipAccountManager::getInterface(phone);
   
   TestAccountConfig config("alice");
   TestAccount bob("bob", Account_Enable, false);
   CPCAPI2::SipAccount::SipAccountHandle accountHandle = acct->create(config.settings);
   
   class MySipPresencePublicationHandler : public SipPresencePublicationHandler
   {
   public:
      MySipPresencePublicationHandler() : receivedEvent(false) {}
      bool receivedEvent;
      
      virtual int onPublicationSuccess(CPCAPI2::SipEvent::SipEventPublicationHandle publication, const PresencePublicationSuccessEvent& args) { return kSuccess; }
      virtual int onPublicationFailure(CPCAPI2::SipEvent::SipEventPublicationHandle publication, const PresencePublicationFailureEvent& args) { receivedEvent = true; return kSuccess; }
      virtual int onPublicationRemove(CPCAPI2::SipEvent::SipEventPublicationHandle publication, const PresencePublicationRemoveEvent & args) { return kSuccess; }
      virtual int onError(CPCAPI2::SipEvent::SipEventPublicationHandle publication, const PresencePublicationErrorEvent& args) { return kSuccess; }
   };
   
   
   std::chrono::high_resolution_clock::time_point start, end;
   
   SipPresenceManager* presenceManager = SipPresenceManager::getInterface(phone);
   std::unique_ptr<MySipPresencePublicationHandler> handler(new MySipPresencePublicationHandler());
   
   presenceManager->setPublicationHandler(accountHandle, handler.get());
   acct->enable(accountHandle);
   
   SipPresencePublicationSettings pubSettings;
   SipEventPublicationHandle pubHandle = presenceManager->createPublication(accountHandle, pubSettings);

   ASSERT_EQ(kSuccess, presenceManager->publish(pubHandle, SipPresence::Presence()));
   
   start = std::chrono::high_resolution_clock::now();

   std::atomic_bool threadStopFlag(false);
   auto presenceEvent = std::async(std::launch::async, [&] ()
   {
     while (handler->receivedEvent == false)
     {
        phone->process(CPCAPI2::Phone::kBlockingModeNonBlocking);

        if (threadStopFlag) return;

        std::this_thread::sleep_for(std::chrono::milliseconds(100));
     }
   });
   
   flaggableWaitFor(presenceEvent, threadStopFlag);
   
   end = std::chrono::high_resolution_clock::now();

   
   pubHandle = presenceManager->createPublication(accountHandle, pubSettings);
   ASSERT_EQ(kSuccess, presenceManager->publish(pubHandle, SipPresence::Presence()));
   presenceManager->setPublicationHandler(accountHandle, NULL);
   handler.reset();
   
   // wait about as long as it took before
   std::this_thread::sleep_for((end-start) * 2);
   phone->process(CPCAPI2::Phone::kBlockingModeNonBlocking);

   Phone::release(phone);
}
