#pragma once

#ifndef SIPUA_ALIANZA_API_TEST_HELPER_H
#define SIPUA_ALIANZA_API_TEST_HELPER_H

#include "brand_branded.h"
#include <cpcapi2.h>
#include "cpcapi2_test_fixture.h"
#include "sipua_alianza_api_test_fixture.h"
#include "alianza_api/interface/public/alianza_api_handler.h"
#include "alianza_api/interface/public/alianza_api_types.h"
#include "alianza_api/interface/public/alianza_api_manager.h"

class AlianzaApiTestHelper
{
public:

   AlianzaApiTestHelper();
   virtual~ AlianzaApiTestHelper();

   static void createMessageForAuthorization(const TestAccount& account, std::string& url, std::string& messageBody);
   static void createMessageToAddNumberInPartition(const TestAccount& account, std::string& url, std::string& messageBody);
   static void createMessageToAddAccountInPartition(const TestAccount& account, std::string& url, std::string& messageBody);
   static void createMessageToAddUserInAccount(const TestAccount& account, std::string& url, std::string& messageBody);
   static void createMessageToUpdateGroupNameInAccount(const TestAccount& account, std::string& url, std::string& messageBody);
   static void createMessageToFetchConfigurationInAccount(const TestAccount& account, std::string& url, std::string& messageBody);
   static void createMessageToAddNumberInAccount(const TestAccount& account, std::string& url, std::string& messageBody);
   static void createMessageToUpdateNumberInAccount(const TestAccount& account, std::string& url, std::string& messageBody);
   static void createMessageToUpdateUserInAccount(const TestAccount& account, std::string& url, std::string& messageBody);
   static void createMessageToDeleteNumberInPartition(const TestAccount& account, std::string& url, std::string& messageBody);
   static void createMessageToDeleteAccountInPartition(const TestAccount& account, std::string& url, std::string& messageBody);
   static void createMessageToDeleteNumberInAccount(const TestAccount& account, std::string& url, std::string& messageBody);
   static void createMessageToGetNumberInPartition(const TestAccount& account, std::string& url, std::string& messageBody);
   static void createMessageToGetAccountsInPartition(const TestAccount& account, std::string& url, std::string& messageBody);
   static void createMessageToGetAccount(const TestAccount& account, std::string& url, std::string& messageBody);
   static void createMessageToGetUserInAccount(const TestAccount& account, std::string& url, std::string& messageBody);
   static void createMessageToGetNumberInAccount(const TestAccount& account, std::string& url, std::string& messageBody);

   static void createMessageForAuthorization(const std::string& username, const std::string& password, const AlianzaAccountConfig& config, const AlianzaSessionInfo& info, std::string& url, std::string& messageBody);
   static void createMessageForAuthorization(const AlianzaAccountConfig& config, const AlianzaSessionInfo& info, std::string& url, std::string& messageBody);
   static void createMessageToAddNumberInPartition(const AlianzaAccountConfig& config, const AlianzaSessionInfo& info, std::string& url, std::string& messageBody);
   static void createMessageToAddAccountInPartition(const AlianzaAccountConfig& config, const AlianzaSessionInfo& info, std::string& url, std::string& messageBody);
   static void createMessageToAddUserInAccount(const AlianzaAccountConfig& config, const AlianzaSessionInfo& info, const AlianzaSipUaInfo& ua, std::string& url, std::string& messageBody);
   static void createMessageToUpdateGroupNameInAccount(const AlianzaAccountConfig& config, const AlianzaSessionInfo& info, std::string& url, std::string& messageBody);
   static void createMessageToFetchConfigurationInAccount(const AlianzaAccountConfig& config, const AlianzaSessionInfo& info, const AlianzaSipUaInfo& ua, std::string& url, std::string& messageBody);
   static void createMessageToAddNumberInAccount(const AlianzaAccountConfig& config, const AlianzaSessionInfo& info, const AlianzaSipUaInfo& ua, std::string& url, std::string& messageBody);
   static void createMessageToUpdateNumberInAccount(const AlianzaAccountConfig& config, const AlianzaSessionInfo& info, const AlianzaSipUaInfo& ua, std::string& url, std::string& messageBody);
   static void createMessageToUpdateUserInAccount(const AlianzaAccountConfig& config, const AlianzaSessionInfo& info, const AlianzaSipUaInfo& ua, std::string& url, std::string& messageBody);
   static void createMessageToDeleteNumberInPartition(const AlianzaAccountConfig& config, const AlianzaSessionInfo& info, const AlianzaSipUaInfo& ua, std::string& url, std::string& messageBody);
   static void createMessageToGetNumberInPartition(const AlianzaAccountConfig& config, const AlianzaSessionInfo& info, std::string& url, std::string& messageBody);
   static void createMessageToDeleteAccountInPartition(const AlianzaAccountConfig& config, const AlianzaSessionInfo& info, std::string& url, std::string& messageBody);
   static void createMessageToDeleteNumberInAccount(const AlianzaAccountConfig& config, const AlianzaSessionInfo& info, const AlianzaSipUaInfo& ua, std::string& url, std::string& messageBody);
   static void createMessageToGetAccountsInPartition(const AlianzaAccountConfig& config, const AlianzaSessionInfo& info, std::string& url, std::string& messageBody);
   static void createMessageToGetAccount(const AlianzaAccountConfig& config, const AlianzaSessionInfo& info, std::string& url, std::string& messageBody);
   static void createMessageToGetUserInAccount(const AlianzaAccountConfig& config, const AlianzaSessionInfo& info, const AlianzaSipUaInfo& ua, std::string& url, std::string& messageBody);
   static void createMessageToGetNumberInAccount(const AlianzaAccountConfig& config, const AlianzaSessionInfo& info, const AlianzaSipUaInfo& ua, std::string& url, std::string& messageBody);
   static void createMessageToGetClientRegistrationStatus(const AlianzaAccountConfig& config, const AlianzaSessionInfo& info, const AlianzaSipUaInfo& ua, std::string& url, std::string& messageBody);

   static bool extractStatusMessagesInResponse(const std::string& response, int& status, std::vector<std::string>& messages);
   static bool isError(const std::string& response, const int statusCode, const std::string& message);
   static bool isErrorPhoneNumberAlreadyExists(const std::string& response);
   static bool isErrorDuplicateAccountNumber(const std::string& response);
   static bool isErrorDuplicateUsername(const std::string& response);
   static bool isErrorSipUsernameInUse(const std::string& response);
   static bool isErrorEmailAddressInUse(const std::string& response);
   static bool isErrorTNDisconnectEventInProgress(const std::string& response);
   static bool isErrorInvalidStatus(const std::string& response);
   static bool isErrorTelephoneNumbersExist(const std::string& response);
   static bool extractDataTypeInResponse(const std::string& response, std::string& errorType);
   static bool isErrorServiceActivationEventInProgress(const std::string& response);
   static bool isErrorTelephoneNumberEntityNotFound(const std::string& response);
   static bool isErrorPortRequiredTelephoneNumberNotInInventory(const std::string& response);

   static bool extractPhoneIdFromCreateNumberInPartitionResponse(const std::string& response, std::string& phoneId);
   static bool extractAccountIdFromCreateAccountInPartitionResponse(const std::string& response, std::string& accountId);
   static bool extractDataFromAddUserInAccountResponse(const std::string& response, std::string& userId, std::string& voicemailId, std::string& extension, AlianzaCallingPlanInfo& callingPlan);
   static bool extractUserAuthTokenFromUserAuthResponse(const std::string& response, std::string& authToken);
   static bool extractClientConfigFromQueryClientConfigResponse(const std::string& response, AlianzaSipUaInfo& uaInfo);
   static bool extractServiceStatusForNumberInAccountResponse(const std::string& response, std::string& serviceStatus);

   static void createErrorResponse(const int status, const std::string& reason, std::string& response);
   static void createErrorResponseWithDataObject(const int status, const std::string& reason, const std::string& type, const std::string& id, std::string& response);
   static void createErrorResponseWithDataList(const int status, const std::string& reason, const std::string& data, std::string& response);
   static void createResponseWithId(const std::string& id, std::string& response);
   static void createResponseForAddUserRequest(const std::string& userId, const std::string& voicemailId, const std::string& planId, const std::string& extension, std::string& response);
   static void createResponseForUserAuthRequest(const std::string& authToken, std::string& response);
   static void createResponseForQueryUserConfigRequest(const std::string& sipUser, const std::string& sipPassword, const std::string& sipDomain, std::string& response);
   static void createResponseForCheckNumberInAccountRequest(const std::string& status, std::string& response);

private:

};

#endif // SIPUA_ALIANZA_API_TEST_HELPER_H

