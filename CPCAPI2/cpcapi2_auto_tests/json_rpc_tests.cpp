#if _WIN32
#include "stdafx.h"
#endif
#include "brand_branded.h"

#if (CPCAPI2_BRAND_JSON_RPC_SERVER_MODULE == 1)

#include "cpcapi2_test_fixture.h"
#include "test_events.h"

#include "interface/experimental/jsonrpc/JsonRpcServer.h"
#include "nlohmann/json.hpp"

#include <memory>

#include "gen/Phone/datatypes/PhoneInitialize.h"
#include "gen/Phone/datatypes/PhoneSetLoggingEnabled.h"
#include "gen/Phone/datatypes/PhoneSetLogLevel.h"
#include "gen/Phone/datatypes/PhoneGetInstanceId.h"

#include "gen/SipAccount/datatypes/SipAccountCreate.h"
#include "gen/SipConversation/datatypes/SipConversationCreateConversation.h"

#include "gen/SipConversation/datatypes/SipConversationConfigureMedia.h"
#include "gen/SipConversation/datatypes/SipConversationSetMediaEnabled.h"
#include "gen/SipConversation/datatypes/SipConversationSetAnonymousMode.h"
#include "gen/SipConversation/datatypes/SipConversationHold.h"
#include "gen/SipConversation/datatypes/SipConversationUnhold.h"
#include "gen/SipConversation/datatypes/SipConversationSendMediaChangeRequest.h"
#include "gen/SipConversation/datatypes/SipConversationRedirect.h"
#include "gen/SipConversation/datatypes/SipConversationSendRingingResponse.h"
#include "gen/SipConversation/datatypes/SipConversationReject.h"
#include "gen/SipConversation/datatypes/SipConversationAccept.h"
#include "gen/SipConversation/datatypes/SipConversationAcceptIncomingTransferRequest.h"
#include "gen/SipConversation/datatypes/SipConversationRejectIncomingTransferRequest.h"
#include "gen/SipConversation/datatypes/SipConversationTransfer.h"
#include "gen/SipConversation/datatypes/SipConversationSetDtmfMode.h"
#include "gen/SipConversation/datatypes/SipConversationStartDtmfTone.h"
#include "gen/SipConversation/datatypes/SipConversationStopDtmfTone.h"
#include "gen/SipConversation/datatypes/SipConversationPlaySound.h"
#include "gen/SipConversation/datatypes/SipConversationStopPlaySound.h"
#include "gen/SipConversation/datatypes/SipConversationGetCallCount.h"
#include "gen/SipConversation/datatypes/SipConversationSetCallKitMode.h"
#include "gen/SipConversation/datatypes/SipConversationSetTelecomFrameworkMode.h"
#include "gen/SipConversation/datatypes/MediaType.h"

// Enums
#include "gen/SipConversation/datatypes/NatTraversalMode.h"
#include "gen/SipConversation/datatypes/NatTraversalServerSourceType.h"
#include "gen/SipConversation/datatypes/NatTraversalServerType.h"
#include "gen/SipConversation/datatypes/HoldMode.h"
#include "gen/SipConversation/datatypes/PrackMode.h"
#include "gen/SipConversation/datatypes/AnswerMode.h"
#include "gen/SipConversation/datatypes/ConversationEndReason.h"
#include "gen/SipConversation/datatypes/ConversationType.h"
#include "gen/SipAccount/datatypes/IpVersion.h"
#include "gen/SipAccount/datatypes/StunServerSourceType.h"
#include "gen/SipAccount/datatypes/TunnelType.h"
#include "gen/SipAccount/datatypes/TunnelTransportType.h"
#include "gen/SipAccount/datatypes/TunnelMediaTransportType.h"

// Structs
#include "gen/SipConversation/datatypes/SipConversationSettings.h"
#include "gen/SipAccount/datatypes/SipAccountSettings.h"
#include "gen/SipAccount/datatypes/TunnelConfig.h"

// (void) used to suppress "expression result unused"
#define CHECK_GEN_JSONRPC_ENUM_VALUE(_module, _enum_name, _enum_item) (void) ::jsonrpc::CPCAPI2::_module::_enum_name::_enum_item;
#define CHECK_GEN_JSONRPC_SIPCONVERSATION_ENUM_VALUE(_enum_name, _enum_item) CHECK_GEN_JSONRPC_ENUM_VALUE(SipConversation, _enum_name, _enum_item)
#define CHECK_GEN_JSONRPC_SIPACCOUNT_ENUM_VALUE(_enum_name, _enum_item) CHECK_GEN_JSONRPC_ENUM_VALUE(SipAccount, _enum_name, _enum_item)

#define CHECK_FIELD_TYPE(_object, _field_name, _type) {               \
                        { \
                           const std::optional<_type> tmp = _object._field_name; \
                        } \
                   } 


using namespace CPCAPI2;
using namespace CPCAPI2::test;

class JsonRpcTests : public CpcapiAutoTest
{
public:
   JsonRpcTests() {}
   virtual ~JsonRpcTests() {}
};

template <typename T>
nlohmann::json send_and_parse_reponse(T& request, const std::string& method, CPCAPI2::JsonRpc::JsonRpcServer* server)
{
   std::string json = "{\"jsonrpc\": \"2.0\", \"method\": \"" + method + "\", \"params\": " + request.marshal() + ", \"id\": 4}";  
   auto response = server->processIncoming(json);
   return nlohmann::json::parse(response);
}

template <typename T>
bool send_and_check_error(T& request, const std::string& method, CPCAPI2::JsonRpc::JsonRpcServer* server)
{
   nlohmann::json j = send_and_parse_reponse(request, method, server);
   return j.contains("error");
}

int64_t processAndGetResultField(CPCAPI2::JsonRpc::JsonRpcServer* server, const std::string& request) {
   auto response = server->processIncoming(request);
   nlohmann::json j = nlohmann::json::parse(response);
   return j.at("result");
}

int64_t createPhone(CPCAPI2::JsonRpc::JsonRpcServer* server)
{
   std::string json = "{\"jsonrpc\": \"2.0\", \"method\": \"Phone::getVersion\", \"id\": 0}";
   return processAndGetResultField(server, "{\"jsonrpc\": \"2.0\", \"method\": \"Phone::create\", \"id\": 1}");
}

int64_t createAccount(CPCAPI2::JsonRpc::JsonRpcServer* server, int64_t phoneHandle)
{
   jsonrpc::CPCAPI2::SipAccount::SipAccountCreate createAccount;
   createAccount.phoneHandle = phoneHandle;
   std::string json = "{\"jsonrpc\": \"2.0\", \"method\": \"SipAccount::create\", \"params\": " + createAccount.marshal() + ", \"id\": 2}";
   return processAndGetResultField(server,json);
}

int64_t createConversation(CPCAPI2::JsonRpc::JsonRpcServer* server, int64_t phoneHandle, int64_t accountHandle)
{
   jsonrpc::CPCAPI2::SipConversation::SipConversationCreateConversation createConversation;
   createConversation.phoneHandle = phoneHandle;
   createConversation.accountHandle = accountHandle;
   
   std::string json = "{\"jsonrpc\": \"2.0\", \"method\": \"SipConversation::createConversation\", \"params\": " + createConversation.marshal() + ", \"id\": 3}";
   return processAndGetResultField(server,json);
}

TEST_F(JsonRpcTests, SipConversation_EnumConvertions)
{  
   {
      CHECK_GEN_JSONRPC_SIPCONVERSATION_ENUM_VALUE(NatTraversalMode, None);
      CHECK_GEN_JSONRPC_SIPCONVERSATION_ENUM_VALUE(NatTraversalMode, Auto);
      CHECK_GEN_JSONRPC_SIPCONVERSATION_ENUM_VALUE(NatTraversalMode, Stun);
      CHECK_GEN_JSONRPC_SIPCONVERSATION_ENUM_VALUE(NatTraversalMode, Turn);
      CHECK_GEN_JSONRPC_SIPCONVERSATION_ENUM_VALUE(NatTraversalMode, Ice);
   }
   {
      CHECK_GEN_JSONRPC_SIPCONVERSATION_ENUM_VALUE(NatTraversalServerSourceType, None);
      CHECK_GEN_JSONRPC_SIPCONVERSATION_ENUM_VALUE(NatTraversalServerSourceType, Srv);
      CHECK_GEN_JSONRPC_SIPCONVERSATION_ENUM_VALUE(NatTraversalServerSourceType, Custom);
   }
   {
      CHECK_GEN_JSONRPC_SIPCONVERSATION_ENUM_VALUE(NatTraversalServerType, StunAndTurn);
      CHECK_GEN_JSONRPC_SIPCONVERSATION_ENUM_VALUE(NatTraversalServerType, StunOnly);
      CHECK_GEN_JSONRPC_SIPCONVERSATION_ENUM_VALUE(NatTraversalServerType, TurnOnly);
   }
   {
      CHECK_GEN_JSONRPC_SIPCONVERSATION_ENUM_VALUE(MediaType, Video);
      CHECK_GEN_JSONRPC_SIPCONVERSATION_ENUM_VALUE(MediaType, Audio);
   }
   {
      CHECK_GEN_JSONRPC_SIPCONVERSATION_ENUM_VALUE(HoldMode, Rfc3264);
      CHECK_GEN_JSONRPC_SIPCONVERSATION_ENUM_VALUE(HoldMode, Rfc3264);
   }
   {
      CHECK_GEN_JSONRPC_SIPCONVERSATION_ENUM_VALUE(PrackMode, Disabled);
      CHECK_GEN_JSONRPC_SIPCONVERSATION_ENUM_VALUE(PrackMode, Supported);
      CHECK_GEN_JSONRPC_SIPCONVERSATION_ENUM_VALUE(PrackMode, Required);
      CHECK_GEN_JSONRPC_SIPCONVERSATION_ENUM_VALUE(PrackMode, SupportUasAndUac);
   }

   {
      CHECK_GEN_JSONRPC_SIPCONVERSATION_ENUM_VALUE(AnswerMode, Disabled);
      CHECK_GEN_JSONRPC_SIPCONVERSATION_ENUM_VALUE(AnswerMode, Manual);
      CHECK_GEN_JSONRPC_SIPCONVERSATION_ENUM_VALUE(AnswerMode, Auto);
   }
   {
      CHECK_GEN_JSONRPC_SIPCONVERSATION_ENUM_VALUE(ConversationEndReason, Unknown);
      CHECK_GEN_JSONRPC_SIPCONVERSATION_ENUM_VALUE(ConversationEndReason, UserTerminatedLocally);
      CHECK_GEN_JSONRPC_SIPCONVERSATION_ENUM_VALUE(ConversationEndReason, UserTerminatedRemotely);
      CHECK_GEN_JSONRPC_SIPCONVERSATION_ENUM_VALUE(ConversationEndReason, ServerError);
      CHECK_GEN_JSONRPC_SIPCONVERSATION_ENUM_VALUE(ConversationEndReason, ServerRejected);
      CHECK_GEN_JSONRPC_SIPCONVERSATION_ENUM_VALUE(ConversationEndReason, Redirected);
      CHECK_GEN_JSONRPC_SIPCONVERSATION_ENUM_VALUE(ConversationEndReason, CallAnsweredElsewhere);
   }
   {
      CHECK_GEN_JSONRPC_SIPCONVERSATION_ENUM_VALUE(ConversationType, Incoming);
      CHECK_GEN_JSONRPC_SIPCONVERSATION_ENUM_VALUE(ConversationType, Outgoing);
      CHECK_GEN_JSONRPC_SIPCONVERSATION_ENUM_VALUE(ConversationType, IncomingJoinRequest);
      CHECK_GEN_JSONRPC_SIPCONVERSATION_ENUM_VALUE(ConversationType, IncomingTransferRequest);
      CHECK_GEN_JSONRPC_SIPCONVERSATION_ENUM_VALUE(ConversationType, OutgoingNetworkChangeHandover);
   }
   {
      ::jsonrpc::CPCAPI2::SipConversation::SipConversationSettings item;
      (void) item.sessionName;
      (void) item.natTraversalMode;
      (void) item.natTraversalServerSource;
      (void) item.natTraversalServer;
      (void) item.natTraversalServerType;
      (void) item.holdMode;
      (void) item.prackMode;
      (void) item.answerMode;
      (void) item.networkChangeHandoverMode;
      (void) item.networkChangeHandoverStarcode;
      (void) item.minRtpPort;
      (void) item.maxRtpPort;
      (void) item.minRtpPortAudio;
      (void) item.maxRtpPortAudio;
      (void) item.minRtpPortVideo;
      (void) item.maxRtpPortVideo;
      (void) item.turnUsername;
      (void) item.turnPassword;
      (void) item.includePPreferredIdentity;
      (void) item.includePAssertedIdentity;
      (void) item.includeAttribsForStaticPLs;
      (void) item.adornTransferMessages;
   }
   {
      ::jsonrpc::CPCAPI2::SipConversation::AnswerModeSettings item;
      (void) item.mode;
      (void) item.privileged;
      (void) item.required;
      (void) item.challenge;
      (void) item.allowManual;
      (void) item.allowAuto;
      (void) item.allowPrivileged;
   }
}

TEST_F(JsonRpcTests, SipAccount_FieldCheck)
{  

   {
      CHECK_GEN_JSONRPC_SIPACCOUNT_ENUM_VALUE(IpVersion, V4);
      CHECK_GEN_JSONRPC_SIPACCOUNT_ENUM_VALUE(IpVersion, V6);
      CHECK_GEN_JSONRPC_SIPACCOUNT_ENUM_VALUE(IpVersion, Auto);
      CHECK_GEN_JSONRPC_SIPACCOUNT_ENUM_VALUE(IpVersion, AutoPreferV6);
   }
   {
      CHECK_GEN_JSONRPC_SIPACCOUNT_ENUM_VALUE(StunServerSourceType, None);
      CHECK_GEN_JSONRPC_SIPACCOUNT_ENUM_VALUE(StunServerSourceType, Srv);
      CHECK_GEN_JSONRPC_SIPACCOUNT_ENUM_VALUE(StunServerSourceType, Custom);
   }   
   {
      CHECK_GEN_JSONRPC_SIPACCOUNT_ENUM_VALUE(KeepAliveMode, Default);
      CHECK_GEN_JSONRPC_SIPACCOUNT_ENUM_VALUE(KeepAliveMode, NoKeepAlives);
      CHECK_GEN_JSONRPC_SIPACCOUNT_ENUM_VALUE(KeepAliveMode, Crlfcrlf);
   }   
   {
      CHECK_GEN_JSONRPC_SIPACCOUNT_ENUM_VALUE(SslVersion, TlsDefault);
      CHECK_GEN_JSONRPC_SIPACCOUNT_ENUM_VALUE(SslVersion, SslNone);
      CHECK_GEN_JSONRPC_SIPACCOUNT_ENUM_VALUE(SslVersion, SslV2);
      CHECK_GEN_JSONRPC_SIPACCOUNT_ENUM_VALUE(SslVersion, SslV3);
      CHECK_GEN_JSONRPC_SIPACCOUNT_ENUM_VALUE(SslVersion, TlsV1_0);
      CHECK_GEN_JSONRPC_SIPACCOUNT_ENUM_VALUE(SslVersion, TlsV1_1);
      CHECK_GEN_JSONRPC_SIPACCOUNT_ENUM_VALUE(SslVersion, TlsV1_2);
      CHECK_GEN_JSONRPC_SIPACCOUNT_ENUM_VALUE(SslVersion, TlsV1_3);
      CHECK_GEN_JSONRPC_SIPACCOUNT_ENUM_VALUE(SslVersion, SslHighest);
      CHECK_GEN_JSONRPC_SIPACCOUNT_ENUM_VALUE(SslVersion, TlsNonDeprecated);
   }   
   {
      ::jsonrpc::CPCAPI2::SipAccount::TunnelConfig item;
      CHECK_FIELD_TYPE(item, useTunnel, bool);
      CHECK_FIELD_TYPE(item, tunnelType, jsonrpc::CPCAPI2::SipAccount::TunnelType);
      CHECK_FIELD_TYPE(item, server, cpc::string);
      CHECK_FIELD_TYPE(item, transportType, jsonrpc::CPCAPI2::SipAccount::TunnelTransportType);
      CHECK_FIELD_TYPE(item, mediaTransportType, jsonrpc::CPCAPI2::SipAccount::TunnelMediaTransportType);
      CHECK_FIELD_TYPE(item, redundancyFactor, int);
      CHECK_FIELD_TYPE(item, doLoadBalancing, bool);
      CHECK_FIELD_TYPE(item, ignoreCertVerification, bool);
      CHECK_FIELD_TYPE(item, disableNagleAlgorithm, bool);
      CHECK_FIELD_TYPE(item, strettoTunnelUrl, cpc::string);
      CHECK_FIELD_TYPE(item, strettoTunnelToken, cpc::string);
      CHECK_FIELD_TYPE(item, strettoTunnelSessionId, cpc::string);
      CHECK_FIELD_TYPE(item, strettoTunnelTestConnection, bool);
      CHECK_FIELD_TYPE(item, logStrettoTunnelTransportTraces, bool);
      CHECK_FIELD_TYPE(item, strettoTunnelSkipHandshake, bool);
   }
   {
      ::jsonrpc::CPCAPI2::SipAccount::SipAccountSettings item;
      CHECK_FIELD_TYPE(item, mUsername, cpc::string);
      CHECK_FIELD_TYPE(item, mDomain, cpc::string);
      CHECK_FIELD_TYPE(item, mPassword, cpc::string);
      CHECK_FIELD_TYPE(item, mDisplayName, cpc::string);
      CHECK_FIELD_TYPE(item, mAuthUsername, cpc::string);
      CHECK_FIELD_TYPE(item, mAuthRealm, cpc::string);
      CHECK_FIELD_TYPE(item, mUseRegistrar, bool);
      CHECK_FIELD_TYPE(item, mOutboundProxy, cpc::string);
      CHECK_FIELD_TYPE(item, mAlwaysRouteViaOutboundProxy, bool);
      CHECK_FIELD_TYPE(item, mRegistrationIntervalSeconds, int);
      CHECK_FIELD_TYPE(item, mMinimumRegistrationIntervalSeconds, int);
      CHECK_FIELD_TYPE(item, mMaximumRegistrationIntervalSeconds, int);
      CHECK_FIELD_TYPE(item, mUseRport, bool);
      CHECK_FIELD_TYPE(item, mSipTransportType, jsonrpc::CPCAPI2::SipAccount::SipAccountTransportType);
      CHECK_FIELD_TYPE(item, mExcludeEncryptedTransports, bool);
      CHECK_FIELD_TYPE(item, mUserAgent, cpc::string);
      CHECK_FIELD_TYPE(item, mUdpKeepAliveTime, int);
      CHECK_FIELD_TYPE(item, mTcpKeepAliveTime, int);
      CHECK_FIELD_TYPE(item, mUseOutbound, bool);
      CHECK_FIELD_TYPE(item, mUseGruu, bool);
      CHECK_FIELD_TYPE(item, mOtherNonEscapedCharsInUri, cpc::string);
      CHECK_FIELD_TYPE(item, mNameServers, cpc::vector<cpc::string>);
      CHECK_FIELD_TYPE(item, mAdditionalNameServers, cpc::vector<cpc::string>);
      CHECK_FIELD_TYPE(item, mSessionTimerMode, jsonrpc::CPCAPI2::SipAccount::SipAccountSessionTimerMode);
      CHECK_FIELD_TYPE(item, mSessionTimeSeconds, int);
      CHECK_FIELD_TYPE(item, mStunServerSource, jsonrpc::CPCAPI2::SipAccount::StunServerSourceType);
      CHECK_FIELD_TYPE(item, mStunServer, cpc::string);
      CHECK_FIELD_TYPE(item, mIgnoreCertVerification, bool);
      CHECK_FIELD_TYPE(item, mAdditionalCertPeerNames, cpc::vector<cpc::string>);
      CHECK_FIELD_TYPE(item, mAcceptedCertPublicKeys, cpc::vector<cpc::string>);
      CHECK_FIELD_TYPE(item, mRequiredCertPublicKeys, cpc::vector<cpc::string>);
      CHECK_FIELD_TYPE(item, mSipQosSettings, int);
      CHECK_FIELD_TYPE(item, mUseImsAuthHeader, bool);
      CHECK_FIELD_TYPE(item, mMinSipPort, int);
      CHECK_FIELD_TYPE(item, mMaxSipPort, int);
      CHECK_FIELD_TYPE(item, mDefaultSipPort, int);
      CHECK_FIELD_TYPE(item, mDefaultSipsPort, int);
      CHECK_FIELD_TYPE(item, mUseMethodParamInReferTo, bool);
      CHECK_FIELD_TYPE(item, mUseInstanceId, bool);
      CHECK_FIELD_TYPE(item, mAnswerModeSupported, bool);
      CHECK_FIELD_TYPE(item, mIpVersion, jsonrpc::CPCAPI2::SipAccount::IpVersion);
      CHECK_FIELD_TYPE(item, mSslVersion, jsonrpc::CPCAPI2::SipAccount::SslVersion);
      CHECK_FIELD_TYPE(item, mCipherSuite, cpc::string);
      CHECK_FIELD_TYPE(item, mEnableLegacyServerConnect, bool);
      CHECK_FIELD_TYPE(item, mReRegisterOnResponseTypes, cpc::vector<jsonrpc::CPCAPI2::SipAccount::SipResponseType>);
      CHECK_FIELD_TYPE(item, mEnableRegeventDeregistration, bool);
      CHECK_FIELD_TYPE(item, mEnableDnsResetOnRegistrationRefresh, bool);
      CHECK_FIELD_TYPE(item, mEnableAuthResetUponDnsReset, bool);
      CHECK_FIELD_TYPE(item, mXcapRoot, cpc::string);
      CHECK_FIELD_TYPE(item, mTunnelConfig, jsonrpc::CPCAPI2::SipAccount::TunnelConfig);
      CHECK_FIELD_TYPE(item, mCapabilities, cpc::vector<jsonrpc::CPCAPI2::SipAccount::SipParameterType>);
      CHECK_FIELD_TYPE(item, mAdditionalFromParameters, cpc::vector<jsonrpc::CPCAPI2::SipAccount::SipParameterType>);
      CHECK_FIELD_TYPE(item, mSourceAddress, cpc::string);
      CHECK_FIELD_TYPE(item, mPreferPAssertedIdentity, bool);
      CHECK_FIELD_TYPE(item, mAutoRetryOnTransportDisconnect, bool);
      CHECK_FIELD_TYPE(item, mKeepAliveMode, jsonrpc::CPCAPI2::SipAccount::KeepAliveMode);
      CHECK_FIELD_TYPE(item, mUseRinstance, bool);
      CHECK_FIELD_TYPE(item, mEnableNat64Support, bool);
      CHECK_FIELD_TYPE(item, mUsePrivacyHeaderOnlyForAnonymous, bool);
      CHECK_FIELD_TYPE(item, mTransportHoldover, jsonrpc::CPCAPI2::SipAccount::TransportHoldover);
      CHECK_FIELD_TYPE(item, mUseOptionsPing, bool);
      CHECK_FIELD_TYPE(item, mOptionsPingInterval, int);
      CHECK_FIELD_TYPE(item, mUserCertificatePem, cpc::string);
      CHECK_FIELD_TYPE(item, mUserPrivateKeyPem, cpc::string);
      CHECK_FIELD_TYPE(item, mForceListenSocket, bool);
      CHECK_FIELD_TYPE(item, mLocalGroup, cpc::string);
      CHECK_FIELD_TYPE(item, mOverrideMsecsTimerF, int);
   }
}

TEST_F(JsonRpcTests, SipConversation_InvokeMethods)
{
   CPCAPI2::JsonRpc::JsonRpcServer* server = CPCAPI2::JsonRpc::JsonRpcServer::create();

   int64_t phoneHandle = createPhone(server);

   int64_t accountHandle = createAccount(server, phoneHandle);

   int64_t conversationHandle = createConversation(server, phoneHandle, accountHandle);

   {
      jsonrpc::CPCAPI2::SipConversation::SipConversationConfigureMedia item;
      item.phoneHandle = phoneHandle;
      item.conversationHandle = conversationHandle;
      ASSERT_FALSE(send_and_check_error(item, "SipConversation::configureMedia", server));
   }

   {
      jsonrpc::CPCAPI2::SipConversation::SipConversationSetMediaEnabled item;
      item.phoneHandle = phoneHandle;
      item.conversationHandle = conversationHandle;
      item.mediaType = jsonrpc::CPCAPI2::SipConversation::MediaType::Audio;
      ASSERT_FALSE(send_and_check_error(item, "SipConversation::setMediaEnabled", server));
   }

   {
      jsonrpc::CPCAPI2::SipConversation::SipConversationSetAnonymousMode item;
      item.phoneHandle = phoneHandle;
      item.conversationHandle = conversationHandle;
      ASSERT_FALSE(send_and_check_error(item, "SipConversation::setAnonymousMode", server));
   }

   {
      jsonrpc::CPCAPI2::SipConversation::SipConversationHold item;
      item.phoneHandle = phoneHandle;
      item.conversationHandle = conversationHandle;
      ASSERT_FALSE(send_and_check_error(item, "SipConversation::hold", server));
   }

   {
      jsonrpc::CPCAPI2::SipConversation::SipConversationUnhold item;
      item.phoneHandle = phoneHandle;
      item.conversationHandle = conversationHandle;
      ASSERT_FALSE(send_and_check_error(item, "SipConversation::unhold", server));
   }

   {
      jsonrpc::CPCAPI2::SipConversation::SipConversationSendMediaChangeRequest item;
      item.phoneHandle = phoneHandle;
      item.conversationHandle = conversationHandle;
      ASSERT_FALSE(send_and_check_error(item, "SipConversation::sendMediaChangeRequest", server));
   }

   {
      jsonrpc::CPCAPI2::SipConversation::SipConversationRedirect item;
      item.phoneHandle = phoneHandle;
      item.conversationHandle = conversationHandle;
      ASSERT_FALSE(send_and_check_error(item, "SipConversation::redirect", server));
   }

   {
      jsonrpc::CPCAPI2::SipConversation::SipConversationSendRingingResponse item;
      item.phoneHandle = phoneHandle;
      item.conversationHandle = conversationHandle;
      ASSERT_FALSE(send_and_check_error(item, "SipConversation::sendRingingResponse", server));
   }

   {
      jsonrpc::CPCAPI2::SipConversation::SipConversationReject item;
      item.phoneHandle = phoneHandle;
      item.conversationHandle = conversationHandle;
      ASSERT_FALSE(send_and_check_error(item, "SipConversation::reject", server));
   }

   {
      jsonrpc::CPCAPI2::SipConversation::SipConversationAccept item;
      item.phoneHandle = phoneHandle;
      item.conversationHandle = conversationHandle;
      ASSERT_FALSE(send_and_check_error(item, "SipConversation::accept", server));
   }

   {
      jsonrpc::CPCAPI2::SipConversation::SipConversationAcceptIncomingTransferRequest item;
      item.phoneHandle = phoneHandle;
      item.conversationHandle = conversationHandle;
      ASSERT_FALSE(send_and_check_error(item, "SipConversation::acceptIncomingTransferRequest", server));
   }

   {
      jsonrpc::CPCAPI2::SipConversation::SipConversationRejectIncomingTransferRequest item;
      item.phoneHandle = phoneHandle;
      item.conversationHandle = conversationHandle;
      ASSERT_FALSE(send_and_check_error(item, "SipConversation::rejectIncomingTransferRequest", server));
   }

   {
      jsonrpc::CPCAPI2::SipConversation::SipConversationTransfer item;
      item.phoneHandle = phoneHandle;
      item.transferTargetConversation = conversationHandle;
      item.transfereeConversation = conversationHandle;
      ASSERT_FALSE(send_and_check_error(item, "SipConversation::transfer", server));
   }

   {
      jsonrpc::CPCAPI2::SipConversation::SipConversationSetDtmfMode item;
      item.phoneHandle = phoneHandle;
      ASSERT_FALSE(send_and_check_error(item, "SipConversation::setDtmfMode", server));
   }

   {
      jsonrpc::CPCAPI2::SipConversation::SipConversationStartDtmfTone item;
      item.phoneHandle = phoneHandle;
      item.conversationHandle = conversationHandle;
      ASSERT_FALSE(send_and_check_error(item, "SipConversation::startDtmfTone", server));
   }

   {
      jsonrpc::CPCAPI2::SipConversation::SipConversationStopDtmfTone item;
      item.phoneHandle = phoneHandle;
      ASSERT_FALSE(send_and_check_error(item, "SipConversation::stopDtmfTone", server));
   }

   {
      jsonrpc::CPCAPI2::SipConversation::SipConversationPlaySound item;
      item.phoneHandle = phoneHandle;
      item.conversationHandle = conversationHandle;
      ASSERT_FALSE(send_and_check_error(item, "SipConversation::playSound", server));
   }

   {
      jsonrpc::CPCAPI2::SipConversation::SipConversationStopPlaySound item;
      item.phoneHandle = phoneHandle;
      item.conversationHandle = conversationHandle;
      ASSERT_FALSE(send_and_check_error(item, "SipConversation::stopPlaySound", server));
   }

   {
      jsonrpc::CPCAPI2::SipConversation::SipConversationGetCallCount item;
      item.phoneHandle = phoneHandle;
      ASSERT_FALSE(send_and_check_error(item, "SipConversation::getCallCount", server));
   }
   {
      jsonrpc::CPCAPI2::SipConversation::SipConversationGetCallCount item;
      item.phoneHandle = phoneHandle;
      auto resp = send_and_parse_reponse(item, "SipConversation::getCallCount", server);
      // Check that something returns
      ASSERT_FALSE(resp.at("result").is_null());
   }

   {
      jsonrpc::CPCAPI2::SipConversation::SipConversationSetCallKitMode item;
      item.phoneHandle = phoneHandle;
      item.conversationHandle = conversationHandle;
      ASSERT_FALSE(send_and_check_error(item, "SipConversation::setCallKitMode", server));
   }

   {
      jsonrpc::CPCAPI2::SipConversation::SipConversationSetTelecomFrameworkMode item;
      item.phoneHandle = phoneHandle;
      item.conversationHandle = conversationHandle;
      ASSERT_FALSE(send_and_check_error(item, "SipConversation::setTelecomFrameworkMode", server));
      // Check error with bad method name
      ASSERT_TRUE(send_and_check_error(item, "SipConversation::badMethod", server));
   }
}

TEST_F(JsonRpcTests, Phone_GetVersion)
{
   CPCAPI2::JsonRpc::JsonRpcServer* server = CPCAPI2::JsonRpc::JsonRpcServer::create();

   std::string json = "{\"jsonrpc\": \"2.0\", \"method\": \"Phone::getVersion\", \"id\": 0}";
   std::string response = server->processIncoming(json);

   json = "{\"jsonrpc\": \"2.0\", \"method\": \"Phone::create\", \"id\": 1}";
   response = server->processIncoming(json);

   nlohmann::json j = nlohmann::json::parse(response);
   int64_t h = j.at("result");

   jsonrpc::CPCAPI2::Phone::PhoneInitialize phoneInitialize;
   phoneInitialize.handle = h;
   phoneInitialize.useNetworkChangeManager = true;
   json = "{\"jsonrpc\": \"2.0\", \"method\": \"Phone::initialize\", \"params\": " + phoneInitialize.marshal() + ", \"id\": 2}";
   response = server->processIncoming(json);

   jsonrpc::CPCAPI2::Phone::PhoneSetLoggingEnabled phoneSetLoggingEnabled;
   phoneSetLoggingEnabled.handle = h;
   phoneSetLoggingEnabled.enabled = true;
   json = "{\"jsonrpc\": \"2.0\", \"method\": \"Phone::setLoggingEnabled\", \"params\": " + phoneSetLoggingEnabled.marshal() + ", \"id\": 3}";
   response = server->processIncoming(json);

   jsonrpc::CPCAPI2::Phone::PhoneSetLogLevel phoneSetLogLevel;
   phoneSetLogLevel.handle = h;
   phoneSetLogLevel.level = jsonrpc::CPCAPI2::Phone::LogLevel::Debug;
   json = "{\"jsonrpc\": \"2.0\", \"method\": \"Phone::setLogLevel\", \"params\": " + phoneSetLogLevel.marshal() + ", \"id\": 4}";
   response = server->processIncoming(json);

   jsonrpc::CPCAPI2::Phone::PhoneGetInstanceId phoneGetInstanceId;
   phoneGetInstanceId.handle = h;
   json = "{\"jsonrpc\": \"2.0\", \"method\": \"Phone::getInstanceId\", \"params\": " + phoneGetInstanceId.marshal() + ", \"id\": 5}";
   response = server->processIncoming(json);
   
   j = nlohmann::json::parse(response);
   std::string instanceId = j.at("result");
   std::cout << "Instance ID: " << instanceId << std::endl;
}

#endif
