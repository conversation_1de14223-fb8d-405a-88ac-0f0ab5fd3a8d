#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#include "cpcapi2_test_fixture.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>

#include "test_framework/cpcapi2_test_framework.h"
#include "test_framework/bare_bones_sip_endpoint.h"
#include "impl/iscomposing/IsComposingDocument.h"

#include <thread>
#include <future>

using namespace CPCAPI2;
using namespace CPCAPI2::IsComposing;
using namespace CPCAPI2::test;

namespace {

class IsComposingTest : public CpcapiAutoTest
{
public:
   IsComposingTest() {}
   virtual ~IsComposingTest() {}
};

TEST_F(IsComposingTest, IsComposingDocumentParse)
{
   long origTimezone = getTimezone();

   setTimezone(18000); // Local: EST (GMT - 5)

   // Create the isComposing XML document
   cpc::string isComposingXml;
   isComposingXml.append("<?xml version='1.0' encoding='UTF-8'?>\n");
   isComposingXml.append("<isComposing xmlns='urn:ietf:params:xml:ns:im-iscomposing'\n");
   isComposingXml.append("xmlns:xsi='http://www.w3.org/2001/XMLSchema-instance'>\n");
   isComposingXml.append("  <state>active</state>\n");
   isComposingXml.append("  <lastactive>2014-03-24T20:26:08Z</lastactive>\n");
   isComposingXml.append("  <contenttype>text/html</contenttype>\n");
   isComposingXml.append("  <refresh>60</refresh>\n");
   isComposingXml.append("</isComposing>\n");

   // Parse the XML
   IsComposingDocument* isComposingDocument = IsComposingDocument::parse(isComposingXml);

   // Validate the elements retrieved from the XML
   ASSERT_TRUE(isComposingDocument->getLastActive().tm_hour == 15 || isComposingDocument->getLastActive().tm_hour == 16);
   ASSERT_EQ(isComposingDocument->getLastActive().tm_min, 26);
   ASSERT_EQ(isComposingDocument->getLastActive().tm_sec, 8);
   ASSERT_EQ(isComposingDocument->getLastActive().tm_mday, 24);
   ASSERT_EQ(isComposingDocument->getLastActive().tm_mon, 2);
   ASSERT_EQ(isComposingDocument->getLastActive().tm_year, 114);
   ASSERT_EQ(isComposingDocument->getContentType(), resip::Mime("text", "html"));
   ASSERT_EQ(isComposingDocument->getRefresh(), 60);

   setTimezone(origTimezone);
}

TEST_F(IsComposingTest, IsComposingDocumentParseWithNoOptionalElements)
{
   // Create the isComposing XML document
   cpc::string isComposingXml;
   isComposingXml.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n");
   isComposingXml.append("<isComposing xmlns=\"urn:ietf:params:xml:ns:im-iscomposing\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:schemaLocation=\"urn:ietf:params:xml:ns:im-composing iscomposing.xsd\">\n");
   isComposingXml.append("   <state>active</state>\n");
   isComposingXml.append("</isComposing>\n");

   // Parse the XML
   IsComposingDocument* isComposingDocument = IsComposingDocument::parse(isComposingXml);

   // Validate the elements retrieved from the XML
   ASSERT_EQ(isComposingDocument->getLastActive().tm_hour, 0);
   ASSERT_EQ(isComposingDocument->getLastActive().tm_min, 0);
   ASSERT_EQ(isComposingDocument->getLastActive().tm_sec, 0);
   ASSERT_EQ(isComposingDocument->getLastActive().tm_mday, 0);
   ASSERT_EQ(isComposingDocument->getLastActive().tm_mon, 0);
   ASSERT_EQ(isComposingDocument->getLastActive().tm_year, 0);
   ASSERT_EQ(isComposingDocument->getContentType(), resip::Mime());
   ASSERT_EQ(isComposingDocument->getRefresh(), 0);
}

}
