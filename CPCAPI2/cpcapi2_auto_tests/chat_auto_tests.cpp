
#if 0

#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#if (CPCAPI2_BRAND_SIP_CHAT_MODULE == 1)

#include "cpcapi2_test_fixture.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>

#include "test_framework/cpcapi2_test_framework.h"
#include "test_framework/bare_bones_sip_endpoint.h"

#include <thread>
#include <future>

using namespace CPCAPI2;
using namespace CPCAPI2::SipChat;
using namespace CPCAPI2::test;

namespace {

class ChatTest : public CpcapiAutoTest
{
public:
   ChatTest() {}
   virtual ~ChatTest() {}

   void BasicChatWithDeliveryNotification(MessageDeliveryStatus deliveryStatus, DispositionNotificationType dispositionNotificationType);
   void BasicChatWithDisplayNotification(MessageDisplayStatus displayStatus);

   static const bool checkTo;
   static const bool checkDateTime;
};

const bool ChatTest::checkTo = true; // Set to false for NewPace
const bool ChatTest::checkDateTime = true; // Set to false for NewPace

TEST_F(ChatTest, ChatCreationFailureInvalidAccount) 
{
   TestAccount alice("alice");

   // Create a chat session with the wrong account handle
   SipChatHandle aliceChat = alice.chatManager->createChat(123);

   // Expect an error
   PhoneErrorEvent evt;
   cpc::string module;
   ASSERT_TRUE(alice.events->expectEvent("PhoneHandler::onError", 15000, StrEqualsPred("SipAccountInterface"), module, evt));
   ASSERT_EQ(evt.errorText, "Creating chat session with invalid account handle: 123");
}

TEST_F(ChatTest, ChatCreationFailureAccountDisabled)
{
   TestAccount alice("alice");
   alice.account->disable(alice.handle);

   // Create a chat session using a disabled account
   SipChatHandle aliceChat = alice.chatManager->createChat(alice.handle);

   // Expect an error
   PhoneErrorEvent evt;
   cpc::string module;
   ASSERT_TRUE(alice.events->expectEvent("PhoneHandler::onError", 15000, StrEqualsPred("SipAccountInterface"), module, evt));
   ASSERT_EQ(evt.errorText, "Creating chat session before account enabled: 512");
}

TEST_F(ChatTest, ChatCreationFailureNoParticipant) 
{
   TestAccount alice("alice");

   // Start chat session without any participant
   SipChatHandle aliceChat = alice.chatManager->createChat(alice.handle);
   alice.chatManager->start(aliceChat);

   {
      SipChatHandle h;
      SipChat::ErrorEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onError",
         5000, AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, aliceChat);
		ASSERT_EQ(evt.errorText, "Cannot start chat session. No participants have been added");
   }

   alice.chatManager->end(aliceChat); // Must be called for proper cleanup

   {
      // Wait for the end of chat session notification (from Bob)
      ChatEndedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onChatEnded",
      5000, AlwaysTruePred(), aliceChat, evt));
      ASSERT_EQ(evt.chat, aliceChat);
      ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedLocally);
   }
}

TEST_F(ChatTest, ChatCreationFailureInvalidParticipantAddress)
{
   TestAccount alice("alice");

   // Start chat session with an invalid participant address
   SipChatHandle aliceChat = alice.chatManager->createChat(alice.handle);
   alice.chatManager->addParticipant(aliceChat, "b");

   {
      SipChatHandle h;
      SipChat::ErrorEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onError",
         5000, AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, aliceChat);
		ASSERT_EQ(evt.errorText, "Failed to parse participant URI 'b'");
   }

   alice.chatManager->end(aliceChat); // Must be called for proper cleanup

   {
      // Wait for the end of chat session notification (from Bob)
      ChatEndedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onChatEnded",
      5000, AlwaysTruePred(), aliceChat, evt));
      ASSERT_EQ(evt.chat, aliceChat);
      ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedLocally);
   }
}

TEST_F(ChatTest, ChatCreationFailureParticipantSameAsCreator)
{
   TestAccount alice("alice");

   // Start chat session with itself
   SipChatHandle aliceChat = alice.chatManager->createChat(alice.handle);
   alice.chatManager->addParticipant(aliceChat, alice.config.uri().c_str());
   alice.chatManager->start(aliceChat);

   {
      SipChatHandle h;
      SipChat::ErrorEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onError",
         5000, AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, aliceChat);
		ASSERT_EQ(evt.errorText, "Cannot start chat session. Creator and participant are the same");
   }

   alice.chatManager->end(aliceChat); // Must be called for proper cleanup

   {
      // Wait for the end of chat session notification (from Bob)
      ChatEndedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onChatEnded",
      5000, AlwaysTruePred(), aliceChat, evt));
      ASSERT_EQ(evt.chat, aliceChat);
      ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedLocally);
   }
}

TEST_F(ChatTest, ChatRejected) 
{
   TestAccount alice("alice");
   TestAccount bob("bob");

   // Start chat session with bob
   SipChatHandle aliceChat = alice.chatManager->createChat(alice.handle);
   alice.chatManager->addParticipant(aliceChat, bob.config.uri().c_str());
   alice.chatManager->start(aliceChat);

   // Bob's thread
   auto bobEvents = std::async(std::launch::async, [&] () {
      SipChatHandle bobChat = 0;
      {
         // Wait for the new chat notification (from Alice)
         SipChatHandle h;
         NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onNewChat",
            5000, AlwaysTruePred(), h, evt));
         bobChat = h;
         ASSERT_EQ(evt.chatType, ChatType_Incoming);
      }

      // Reject the chat session (from Alice)
      bob.chatManager->reject(bobChat, 486);

      {
         // Wait for the end of chat session notification (from Bob)
         ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onChatEnded",
         5000, AlwaysTruePred(), bobChat, evt));
         ASSERT_EQ(evt.chat, bobChat);
         ASSERT_EQ(evt.endReason, ChatEndReason_Rejected);
      }
   });

   // Alice's thread
   auto aliceEvents = std::async(std::launch::async, [&] () {
      {
         // Wait for the end of chat session notification (from Bob)
         ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onChatEnded",
         5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.chat, aliceChat);
         ASSERT_EQ(evt.endReason, ChatEndReason_Rejected);
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
}

TEST_F(ChatTest, ChatSendMessageFailureNoText) 
{
   TestAccount alice("alice");
   TestAccount bob("bob");

   // Start chat session with bob
   SipChatHandle aliceChat = alice.chatManager->createChat(alice.handle);
   alice.chatManager->addParticipant(aliceChat, bob.config.uri().c_str());
   alice.chatManager->start(aliceChat);

   SipChatMessageHandle newMessageHandle;

   // Bob's thread
   auto bobEvents = std::async(std::launch::async, [&] () {
      SipChatHandle bobChat = 0;
      {
         // Wait for the new chat notification (from Alice)
         SipChatHandle h;
         NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onNewChat",
            5000, AlwaysTruePred(), h, evt));
         bobChat = h;
         ASSERT_EQ(evt.chatType, ChatType_Incoming);
      }

      // Accept the chat session (from Alice)
      bob.chatManager->accept(bobChat);

      {
         // Wait for the end chat notification (from Alice)
         ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onChatEnded",
            5000, AlwaysTruePred(), bobChat, evt));
         ASSERT_EQ(evt.chat, bobChat);
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedRemotely);
      }
   });

   // Alice's thread
   auto aliceEvents = std::async(std::launch::async, [&] () {
      {
         // Wait for the new chat notification (from Alice)
         NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onNewChat",
            5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.chatType, ChatType_Outgoing);
      }

      newMessageHandle = alice.chatManager->sendMessage(aliceChat, ""); // Sending an empty message - not allowed

      {
         // Wait for the new message notification (from Alice)
         SipChat::SendMessageFailureEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onSendMessageFailure",
            5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.message, newMessageHandle);
      }

      // End the chat session
      alice.chatManager->end(aliceChat);

      {
         // Wait for the end of chat session notification (from Alice)
         ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onChatEnded",
         5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.chat, aliceChat);
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedLocally);
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
}

TEST_F(ChatTest, ChatAccepted) 
{
   TestAccount alice("alice");
   TestAccount bob("bob");

   // Start chat session with bob
   SipChatHandle aliceChat = alice.chatManager->createChat(alice.handle);
   alice.chatManager->addParticipant(aliceChat, bob.config.uri().c_str());
   alice.chatManager->start(aliceChat);

   // Bob's thread
   auto bobEvents = std::async(std::launch::async, [&] () {
      SipChatHandle bobChat = 0;
      {
         // Wait for the new chat notification (from Alice)
         SipChatHandle h;
         NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onNewChat",
            5000, AlwaysTruePred(), h, evt));
         bobChat = h;
         ASSERT_EQ(evt.chatType, ChatType_Incoming);
      }

      // Accept the chat session (from Alice)
      bob.chatManager->accept(bobChat);

      {
         // Wait for the end chat notification (from Alice)
         ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onChatEnded",
            5000, AlwaysTruePred(), bobChat, evt));
         ASSERT_EQ(evt.chat, bobChat);
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedRemotely);
      }
   });

   // Alice's thread
   auto aliceEvents = std::async(std::launch::async, [&] () {
      {
         // Wait for the new chat notification (from Alice)
         NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onNewChat",
            5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.chatType, ChatType_Outgoing);
      }

      // End the chat session
      alice.chatManager->end(aliceChat);

      {
         // Wait for the end of chat session notification (from Alice)
         ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onChatEnded",
         5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.chat, aliceChat);
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedLocally);
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
}

TEST_F(ChatTest, BasicChat)
{
   TestAccount alice("alice");
   TestAccount bob("bob");

   // Set the date/time of the message to be sent
   struct tm messageDateTime;
   messageDateTime.tm_year = 70;
   messageDateTime.tm_mon = 0;
   messageDateTime.tm_mday = 1;
   messageDateTime.tm_hour = 0;
   messageDateTime.tm_min = 20;
   messageDateTime.tm_sec = 34;

   // Start chat session with bob
   SipChatHandle aliceChat = alice.chatManager->createChat(alice.handle);
   alice.chatManager->addParticipant(aliceChat, bob.config.uri().c_str());
   alice.chatManager->start(aliceChat);

   SipChatMessageHandle newMessageHandle;

   // Bob's thread
   auto bobEvents = std::async(std::launch::async, [&] () {
      SipChatHandle bobChat = 0;
      {
         // Wait for the new chat notification (from Alice)
         SipChatHandle h;
         NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onNewChat",
            5000, AlwaysTruePred(), h, evt));
         bobChat = h;
         ASSERT_EQ(evt.chatType, ChatType_Incoming);
      }

      // Accept the chat session (from Alice)
      bob.chatManager->accept(bobChat);

      {
         // Wait for the new message notification (from Alice)
         SipChatHandle h;
         SipChat::NewMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onNewMessage",
            5000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobChat, h);
         ASSERT_EQ(evt.message, newMessageHandle);
         ASSERT_EQ(evt.from, alice.config.uri().c_str());
         if (checkTo)
         {
            ASSERT_EQ(evt.to, bob.config.uri().c_str());
         }
         if (checkDateTime)
         {
            ASSERT_EQ(evt.datetime.tm_year, messageDateTime.tm_year);
            ASSERT_EQ(evt.datetime.tm_mon, messageDateTime.tm_mon);
            ASSERT_EQ(evt.datetime.tm_mday, messageDateTime.tm_mday);
            ASSERT_EQ(evt.datetime.tm_hour, messageDateTime.tm_hour);
            ASSERT_EQ(evt.datetime.tm_min, messageDateTime.tm_min);
            ASSERT_EQ(evt.datetime.tm_sec, messageDateTime.tm_sec);
         }
         ASSERT_EQ(evt.mimeType, CPM::MimeType_TextPlain);
         ASSERT_EQ(evt.messageContent, "Hi Bob");
      }

      // End the chat session
      bob.chatManager->end(bobChat);

      {
         // Wait for the end chat notification (from Bob)
         ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onChatEnded",
            5000, AlwaysTruePred(), bobChat, evt));
         ASSERT_EQ(evt.chat, bobChat);
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedLocally);
      }
   });

   // Alice's thread
   auto aliceEvents = std::async(std::launch::async, [&] () {
      {
         // Wait for the new chat notification (from Alice)
         NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onNewChat",
            5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.chatType, ChatType_Outgoing);
      }

      newMessageHandle = alice.chatManager->sendMessage(aliceChat, "Hi Bob", CPM::MimeType_TextPlain, &messageDateTime);

      {
         // Wait for the message sent notification (from Alice)
         SipChat::SendMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onSendMessageSuccess",
            5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.message, newMessageHandle);
      }

      {
         // Wait for the end of chat session notification (from Bob)
         ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onChatEnded",
         5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.chat, aliceChat);
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedRemotely);
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
}

TEST_F(ChatTest, BasicChatWithBinaryContent)
{
   TestAccount alice("alice");
   TestAccount bob("bob");

   char messageContent[] = {1, 2, 3, 0, 4, 127, 22, 90, 0, 0};

   // Start chat session with bob
   SipChatHandle aliceChat = alice.chatManager->createChat(alice.handle);
   alice.chatManager->addParticipant(aliceChat, bob.config.uri().c_str());
   alice.chatManager->start(aliceChat);

   SipChatMessageHandle newMessageHandle;

   // Bob's thread
   auto bobEvents = std::async(std::launch::async, [&] () {
      SipChatHandle bobChat = 0;
      {
         // Wait for the new chat notification (from Alice)
         SipChatHandle h;
         NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onNewChat",
            5000, AlwaysTruePred(), h, evt));
         bobChat = h;
         ASSERT_EQ(evt.chatType, ChatType_Incoming);
      }

      // Accept the chat session (from Alice)
      bob.chatManager->accept(bobChat);

      {
         // Wait for the new message notification (from Alice)
         SipChatHandle h;
         SipChat::NewMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onNewMessage",
            5000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobChat, h);
         ASSERT_EQ(evt.message, newMessageHandle);
         ASSERT_EQ(evt.from, alice.config.uri().c_str());
         if (checkTo)
         {
            ASSERT_EQ(evt.to, bob.config.uri().c_str());
         }
         ASSERT_EQ(evt.mimeType, CPM::MimeType_ImageJpeg);
         ASSERT_EQ(evt.messageContent.size(), 10);
         ASSERT_EQ(evt.messageContent.c_str()[0], messageContent[0]);
         ASSERT_EQ(evt.messageContent.c_str()[1], messageContent[1]);
         ASSERT_EQ(evt.messageContent.c_str()[2], messageContent[2]);
         ASSERT_EQ(evt.messageContent.c_str()[3], messageContent[3]);
         ASSERT_EQ(evt.messageContent.c_str()[4], messageContent[4]);
         ASSERT_EQ(evt.messageContent.c_str()[5], messageContent[5]);
         ASSERT_EQ(evt.messageContent.c_str()[6], messageContent[6]);
         ASSERT_EQ(evt.messageContent.c_str()[7], messageContent[7]);
         ASSERT_EQ(evt.messageContent.c_str()[8], messageContent[8]);
         ASSERT_EQ(evt.messageContent.c_str()[9], messageContent[9]);
      }

      // End the chat session
      bob.chatManager->end(bobChat);

      {
         // Wait for the end chat notification (from Bob)
         ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onChatEnded",
            5000, AlwaysTruePred(), bobChat, evt));
         ASSERT_EQ(evt.chat, bobChat);
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedLocally);
      }
   });

   // Alice's thread
   auto aliceEvents = std::async(std::launch::async, [&] () {
      {
         // Wait for the new chat notification (from Alice)
         NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onNewChat",
            5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.chatType, ChatType_Outgoing);
      }

      newMessageHandle = alice.chatManager->sendMessage(aliceChat, cpc::string(messageContent, 10), CPM::MimeType_ImageJpeg);

      {
         // Wait for the message sent notification (from Alice)
         SipChat::SendMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onSendMessageSuccess",
            5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.message, newMessageHandle);
      }

      {
         // Wait for the end of chat session notification (from Bob)
         ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onChatEnded",
         5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.chat, aliceChat);
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedRemotely);
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
}

TEST_F(ChatTest, TwoWayChat) 
{
   TestAccount alice("alice");
   TestAccount bob("bob");

   // Start chat session with bob
   SipChatHandle aliceChat = alice.chatManager->createChat(alice.handle);
   alice.chatManager->addParticipant(aliceChat, bob.config.uri().c_str());
   alice.chatManager->start(aliceChat);

   SipChatMessageHandle aliceNewMessageHandle;
   SipChatMessageHandle bobNewMessageHandle;

   // Bob's thread
   auto bobEvents = std::async(std::launch::async, [&] () {
      SipChatHandle bobChat = 0;
      {
         // Wait for the new chat notification (from Alice)
         SipChatHandle h;
         NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onNewChat",
            5000, AlwaysTruePred(), h, evt));
         bobChat = h;
         ASSERT_EQ(evt.chatType, ChatType_Incoming);
      }

      // Accept the chat session (from Alice)
      bob.chatManager->accept(bobChat);

      {
         // Wait for the new message notification (from Alice)
         SipChatHandle h;
         SipChat::NewMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onNewMessage",
            5000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobChat, h);
         ASSERT_EQ(evt.message, aliceNewMessageHandle);
         ASSERT_EQ(evt.messageContent, "Hi Bob");
      }

      // Send reply to Alice
      bobNewMessageHandle = bob.chatManager->sendMessage(bobChat, "Hello Alice");

      {
        // Wait for the message sent notification (from Bob)
         SipChat::SendMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onSendMessageSuccess",
            5000, AlwaysTruePred(), bobChat, evt));
         ASSERT_EQ(evt.message, bobNewMessageHandle);
      }

      {
         // Wait for the end chat notification (from Alice)
         ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onChatEnded",
            5000, AlwaysTruePred(), bobChat, evt));
         ASSERT_EQ(evt.chat, bobChat);
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedRemotely);
      }
   });

   // Alice's thread
   auto aliceEvents = std::async(std::launch::async, [&] () {
      {
         // Wait for the new chat notification (from Alice)
         NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onNewChat",
            5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.chatType, ChatType_Outgoing);
      }

      aliceNewMessageHandle = alice.chatManager->sendMessage(aliceChat, "Hi Bob");

      {
         // Wait for the message sent notification (from Alice)
         SipChat::SendMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onSendMessageSuccess",
            5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.message, aliceNewMessageHandle);
      }

      {
         // Wait for the new message notification (from Bob)
         SipChat::NewMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onNewMessage",
            5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.message, bobNewMessageHandle);
         ASSERT_EQ(evt.messageContent, "Hello Alice");
      }

      // End the chat session
      alice.chatManager->end(aliceChat);

      {
         // Wait for the end of chat session notification (from Alice)
         ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onChatEnded",
         5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.chat, aliceChat);
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedLocally);
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
}

TEST_F(ChatTest, BasicChatWithDeliveryNotificationFailed) 
{
   BasicChatWithDeliveryNotification(MessageDeliveryStatus_Failed, DispositionNotificationType_NegativeDelivery);
}

TEST_F(ChatTest, BasicChatWithDeliveryNotificationDelivered) 
{
   BasicChatWithDeliveryNotification(MessageDeliveryStatus_Delivered, DispositionNotificationType_PositiveDelivery);
}

void ChatTest::BasicChatWithDeliveryNotification(MessageDeliveryStatus deliveryStatus, DispositionNotificationType dispositionNotificationType) 
{
   TestAccount alice("alice");
   TestAccount bob("bob");

   long origTimezone = getTimezone();

   // Set the date/time of the message to be sent
   struct tm messageDateTime;
   messageDateTime.tm_year = 70;
   messageDateTime.tm_mon = 0;
   messageDateTime.tm_mday = 1;
   messageDateTime.tm_hour = 0;
   messageDateTime.tm_min = 20;
   messageDateTime.tm_sec = 34;
   setTimezone(0); // Local: GMT

   // Start chat session with bob
   SipChatHandle aliceChat = alice.chatManager->createChat(alice.handle);
   alice.chatManager->addParticipant(aliceChat, bob.config.uri().c_str());
   alice.chatManager->start(aliceChat);

   SipChatMessageHandle deliveryNotificationHandle;
   SipChatMessageHandle newMessageHandle;

   // Bob's thread
   auto bobEvents = std::async(std::launch::async, [&] () {
      SipChatHandle bobChat = 0;
      {
         // Wait for the new chat notification (from Alice)
         SipChatHandle h;
         NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onNewChat",
            5000, AlwaysTruePred(), h, evt));
         bobChat = h;
         ASSERT_EQ(evt.chatType, ChatType_Incoming);
      }

      // Accept the chat session (from Alice)
      bob.chatManager->accept(bobChat);

      {
         // Wait for the new message notification (from Alice)
         SipChatHandle h;
         SipChat::NewMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onNewMessage",
            5000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobChat, h);
         ASSERT_EQ(evt.message, newMessageHandle);
         ASSERT_EQ(evt.from, alice.config.uri().c_str());
         if (checkTo)
         {
            ASSERT_EQ(evt.to, bob.config.uri().c_str());
         }
         ASSERT_EQ(evt.mimeType, CPM::MimeType_TextPlain);
         ASSERT_EQ(evt.messageContent, "Hi Bob");
         if (checkDateTime)
         {
            ASSERT_EQ(evt.datetime.tm_year, messageDateTime.tm_year);
            ASSERT_EQ(evt.datetime.tm_mon, messageDateTime.tm_mon);
            ASSERT_EQ(evt.datetime.tm_mday, messageDateTime.tm_mday);
            ASSERT_EQ(evt.datetime.tm_hour, messageDateTime.tm_hour);
            ASSERT_EQ(evt.datetime.tm_min, messageDateTime.tm_min);
            ASSERT_EQ(evt.datetime.tm_sec, messageDateTime.tm_sec);
         }

         // Make sure we get a request for the message delivery disposition notification
         ASSERT_TRUE(std::find(evt.dispositionNotifications.begin(), evt.dispositionNotifications.end(), dispositionNotificationType) != evt.dispositionNotifications.end());
      }
      
      // Send the message delivered notification
      deliveryNotificationHandle = bob.chatManager->notifyMessageDelivered(bobChat, newMessageHandle, deliveryStatus);

      {
         // Wait for the notify message delivered notification (from Bob)
         SipChat::NotifyMessageDeliveredSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onNotifyMessageDeliveredSuccess",
            5000, AlwaysTruePred(), bobChat, evt));
         ASSERT_EQ(evt.notification, deliveryNotificationHandle);
         ASSERT_EQ(evt.message, newMessageHandle);
      }

      {
         // Wait for the end chat notification (from Alice)
         ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onChatEnded",
            5000, AlwaysTruePred(), bobChat, evt));
         ASSERT_EQ(evt.chat, bobChat);
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedRemotely);
      }
   });

   // Alice's thread
   auto aliceEvents = std::async(std::launch::async, [&] () {
      {
         // Wait for the new chat notification (from Alice)
         NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onNewChat",
            5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.chatType, ChatType_Outgoing);
      }
      
      // Send a message. Set the requested disposition type
      cpc::vector<DispositionNotificationType> dispositionNotifications;
      dispositionNotifications.push_back(dispositionNotificationType);
      newMessageHandle = alice.chatManager->sendMessage(aliceChat, "Hi Bob", CPM::MimeType_TextPlain, &messageDateTime, dispositionNotifications);

      {
         // Wait for the send message notification (from Alice)
         SipChat::SendMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onSendMessageSuccess",
            5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.message, newMessageHandle);
      }

      {
         // Wait for the message delivery notification (from Bob)
         SipChat::MessageDeliveredEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onMessageDelivered",
         5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.notification, deliveryNotificationHandle);
         ASSERT_EQ(evt.message, newMessageHandle);
         ASSERT_EQ(evt.messageDeliveryStatus, deliveryStatus);
         if (checkDateTime)
         {
            ASSERT_EQ(evt.datetime.tm_year, messageDateTime.tm_year);
            ASSERT_EQ(evt.datetime.tm_mon, messageDateTime.tm_mon);
            ASSERT_EQ(evt.datetime.tm_mday, messageDateTime.tm_mday);
            ASSERT_EQ(evt.datetime.tm_hour, messageDateTime.tm_hour);
            ASSERT_EQ(evt.datetime.tm_min, messageDateTime.tm_min);
            ASSERT_EQ(evt.datetime.tm_sec, messageDateTime.tm_sec);
         }
      }

      // End the chat session
      alice.chatManager->end(aliceChat);

      {
         // Wait for the end of chat session notification (from Alice)
         ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onChatEnded",
         5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.chat, aliceChat);
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedLocally);
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   setTimezone(origTimezone);
}

TEST_F(ChatTest, BasicChatWithDisplayNotificationError)
{
   BasicChatWithDisplayNotification(MessageDisplayStatus_Error);
}

TEST_F(ChatTest, BasicChatWithDisplayNotificationDisplayed)
{
   BasicChatWithDisplayNotification(MessageDisplayStatus_Displayed);
}

void ChatTest::BasicChatWithDisplayNotification(MessageDisplayStatus displayStatus)
{
   TestAccount alice("alice");
   TestAccount bob("bob");

   // Start chat session with bob
   SipChatHandle aliceChat = alice.chatManager->createChat(alice.handle);
   alice.chatManager->addParticipant(aliceChat, bob.config.uri().c_str());
   alice.chatManager->start(aliceChat);

   SipChatMessageHandle displayNotificationHandle;
   SipChatMessageHandle newMessageHandle;

   // Bob's thread
   auto bobEvents = std::async(std::launch::async, [&] () {
      SipChatHandle bobChat = 0;
      {
         // Wait for the new chat notification (from Alice)
         SipChatHandle h;
         NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onNewChat",
            5000, AlwaysTruePred(), h, evt));
         bobChat = h;
         ASSERT_EQ(evt.chatType, ChatType_Incoming);
      }

      // Accept the chat session (from Alice)
      bob.chatManager->accept(bobChat);

      {
         // Wait for the new message notification (from Alice)
         SipChatHandle h;
         SipChat::NewMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onNewMessage",
            5000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobChat, h);
         ASSERT_EQ(evt.message, newMessageHandle);
         ASSERT_EQ(evt.messageContent, "Hi Bob");

         // Make sure we get a request for the message delivery disposition notification
         ASSERT_TRUE(std::find(evt.dispositionNotifications.begin(), evt.dispositionNotifications.end(), DispositionNotificationType_Display) != evt.dispositionNotifications.end());
      }

      // Send the message displayed notification
      displayNotificationHandle = bob.chatManager->notifyMessageDisplayed(bobChat, newMessageHandle, displayStatus);

      {
         // Wait for the notify message displayed notification (from Bob)
         SipChat::NotifyMessageDisplayedSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onNotifyMessageDisplayedSuccess",
            5000, AlwaysTruePred(), bobChat, evt));
         ASSERT_EQ(evt.notification, displayNotificationHandle);
         ASSERT_EQ(evt.message, newMessageHandle);
      }

      {
         // Wait for the end chat notification (from Alice)
         ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onChatEnded",
            5000, AlwaysTruePred(), bobChat, evt));
         ASSERT_EQ(evt.chat, bobChat);
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedRemotely);
      }
   });

   // Alice's thread
   auto aliceEvents = std::async(std::launch::async, [&] () {
      {
         // Wait for the new chat notification (from Alice)
         NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onNewChat",
            5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.chatType, ChatType_Outgoing);
      }

      // Send a message. Set the requested disposition type
      cpc::vector<DispositionNotificationType> dispositionNotifications;
      dispositionNotifications.push_back(DispositionNotificationType_Display);
      newMessageHandle = alice.chatManager->sendMessage(aliceChat, "Hi Bob", CPM::MimeType_TextPlain, 0, dispositionNotifications);

      {
         // Wait for the send message notification (from Alice)
         SipChat::SendMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onSendMessageSuccess",
            5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.message, newMessageHandle);
      }

      {
         // Wait for the message display notification (from Bob)
         SipChat::MessageDisplayedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onMessageDisplayed",
         5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.notification, displayNotificationHandle);
         ASSERT_EQ(evt.message, newMessageHandle);
         ASSERT_EQ(evt.messageDisplayStatus, displayStatus);
      }

      // End the chat session
      alice.chatManager->end(aliceChat);

      {
         // Wait for the end of chat session notification (from Alice)
         ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onChatEnded",
         5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.chat, aliceChat);
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedLocally);
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
}

TEST_F(ChatTest, BasicChatWithDeliveryAndDisplayNotification) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   // Start chat session with bob
   SipChatHandle aliceChat = alice.chatManager->createChat(alice.handle);
   alice.chatManager->addParticipant(aliceChat, bob.config.uri().c_str());
   alice.chatManager->start(aliceChat);

   SipChatMessageHandle deliveryNotificationHandle;
   SipChatMessageHandle displayNotificationHandle;
   SipChatMessageHandle newMessageHandle;

   // Bob's thread
   auto bobEvents = std::async(std::launch::async, [&] () {
      SipChatHandle bobChat = 0;
      {
         // Wait for the new chat notification (from Alice)
         SipChatHandle h;
         NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onNewChat",
            5000, AlwaysTruePred(), h, evt));
         bobChat = h;
         ASSERT_EQ(evt.chatType, ChatType_Incoming);
      }

      // Accept the chat session (from Alice)
      bob.chatManager->accept(bobChat);

      {
         // Wait for the new message notification (from Alice)
         SipChatHandle h;
         SipChat::NewMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onNewMessage",
            5000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobChat, h);
         ASSERT_EQ(evt.message, newMessageHandle);
         ASSERT_EQ(evt.messageContent, "Hi Bob");

         // Make sure we get a request for the message delivery and display disposition notification
         ASSERT_TRUE(std::find(evt.dispositionNotifications.begin(), evt.dispositionNotifications.end(), DispositionNotificationType_PositiveDelivery) != evt.dispositionNotifications.end());
         ASSERT_TRUE(std::find(evt.dispositionNotifications.begin(), evt.dispositionNotifications.end(), DispositionNotificationType_Display) != evt.dispositionNotifications.end());
      }

      // Send the message delivered notification
      deliveryNotificationHandle = bob.chatManager->notifyMessageDelivered(bobChat, newMessageHandle, MessageDeliveryStatus_Delivered);

      {
         // Wait for the notify message delivered notification (from Bob)
         SipChat::NotifyMessageDeliveredSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onNotifyMessageDeliveredSuccess",
            5000, AlwaysTruePred(), bobChat, evt));
         ASSERT_EQ(evt.notification, deliveryNotificationHandle);
         ASSERT_EQ(evt.message, newMessageHandle);
      }

      // Send the message displayed notification
      displayNotificationHandle = bob.chatManager->notifyMessageDisplayed(bobChat, newMessageHandle, MessageDisplayStatus_Displayed);

      {
         // Wait for the notify message displayed notification (from Bob)
         SipChat::NotifyMessageDisplayedSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onNotifyMessageDisplayedSuccess",
            5000, AlwaysTruePred(), bobChat, evt));
         ASSERT_EQ(evt.notification, displayNotificationHandle);
         ASSERT_EQ(evt.message, newMessageHandle);
      }

      {
         // Wait for the end chat notification (from Alice)
         ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onChatEnded",
            5000, AlwaysTruePred(), bobChat, evt));
         ASSERT_EQ(evt.chat, bobChat);
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedRemotely);
      }
   });

   // Alice's thread
   auto aliceEvents = std::async(std::launch::async, [&] () {
      {
         // Wait for the new chat notification (from Alice)
         NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onNewChat",
            5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.chatType, ChatType_Outgoing);
      }

      // Send a message. Set the requested disposition type
      cpc::vector<DispositionNotificationType> dispositionNotifications;
      dispositionNotifications.push_back(DispositionNotificationType_PositiveDelivery);
      dispositionNotifications.push_back(DispositionNotificationType_Display);
      newMessageHandle = alice.chatManager->sendMessage(aliceChat, "Hi Bob", CPM::MimeType_TextPlain, 0, dispositionNotifications);

      {
         // Wait for the send message notification (from Alice)
         SipChat::SendMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onSendMessageSuccess",
            5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.message, newMessageHandle);
      }

      {
         // Wait for the message delivered notification (from Bob)
         SipChat::MessageDeliveredEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onMessageDelivered",
         5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.notification, deliveryNotificationHandle);
         ASSERT_EQ(evt.message, newMessageHandle);
         ASSERT_EQ(evt.messageDeliveryStatus, MessageDeliveryStatus_Delivered);
      }

      {
         // Wait for the message display notification (from Bob)
         SipChat::MessageDisplayedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onMessageDisplayed",
         5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.notification, displayNotificationHandle);
         ASSERT_EQ(evt.message, newMessageHandle);
         ASSERT_EQ(evt.messageDisplayStatus, MessageDisplayStatus_Displayed);
      }

      // End the chat session
      alice.chatManager->end(aliceChat);

      {
         // Wait for the end of chat session notification (from Alice)
         ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onChatEnded",
         5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.chat, aliceChat);
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedLocally);
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
}

TEST_F(ChatTest, BasicChatWithIsComposingNotification) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   // Start chat session with bob
   SipChatHandle aliceChat = alice.chatManager->createChat(alice.handle);
   alice.chatManager->addParticipant(aliceChat, bob.config.uri().c_str());
   alice.chatManager->start(aliceChat);

   SipChatMessageHandle newMessageHandle;

   // Bob's thread
   auto bobEvents = std::async(std::launch::async, [&] () {
      SipChatHandle bobChat = 0;
      {
         // Wait for the new chat notification (from Alice)
         SipChatHandle h;
         NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onNewChat",
            5000, AlwaysTruePred(), h, evt));
         bobChat = h;
         ASSERT_EQ(evt.chatType, ChatType_Incoming);
      }

      // Accept the chat session (from Alice)
      bob.chatManager->accept(bobChat);
      
      {
         // Wait for the isComposing notification [Active] (from Alice)
         IsComposingMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onIsComposingMessage",
         5000, AlwaysTruePred(), bobChat, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Active);
         ASSERT_EQ(evt.mimeType, CPM::MimeType_TextPlain);
      }
      
      {
         // Wait for the isComposing notification [Idle] (from Alice)
         IsComposingMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onIsComposingMessage",
         5000, AlwaysTruePred(), bobChat, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Idle);
         ASSERT_EQ(evt.mimeType, CPM::MimeType_TextPlain);
      }

      {
         // Wait for the new message notification (from Alice)
         SipChatHandle h;
         SipChat::NewMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onNewMessage",
            5000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobChat, h);
         ASSERT_EQ(evt.message, newMessageHandle);
         ASSERT_EQ(evt.messageContent, "Hi Bob");
      }

      // End the chat session
      bob.chatManager->end(bobChat);

      {
         // Wait for the end chat notification (from Bob)
         ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onChatEnded",
            5000, AlwaysTruePred(), bobChat, evt));
         ASSERT_EQ(evt.chat, bobChat);
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedLocally);
      }
   });

   // Alice's thread
   auto aliceEvents = std::async(std::launch::async, [&] () {
      {
         // Wait for the new chat notification (from Alice)
         NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onNewChat",
            5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.chatType, ChatType_Outgoing);
      }

      alice.chatManager->setIsComposingMessage(aliceChat);

      {
         // Wait for the response (from Alice)
         SetIsComposingMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onSetIsComposingMessageSuccess",
            5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Active);
      }

      newMessageHandle = alice.chatManager->sendMessage(aliceChat, "Hi Bob");

      {
         // Wait for the send message notification (from Alice)
         SipChat::SendMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onSendMessageSuccess",
            5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.message, newMessageHandle);
      }

      {
         // Wait for the end of chat session notification (from Bob)
         ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onChatEnded",
         5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.chat, aliceChat);
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedRemotely);
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
}

TEST_F(ChatTest, IsComposingNotificationActiveToIdle) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   // Start chat session with bob
   SipChatHandle aliceChat = alice.chatManager->createChat(alice.handle);
   alice.chatManager->addParticipant(aliceChat, bob.config.uri().c_str());
   alice.chatManager->start(aliceChat);

   // Bob's thread
   auto bobEvents = std::async(std::launch::async, [&] () {
      SipChatHandle bobChat = 0;
      {
         // Wait for the new chat notification (from Alice)
         SipChatHandle h;
         NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onNewChat",
            5000, AlwaysTruePred(), h, evt));
         bobChat = h;
         ASSERT_EQ(evt.chatType, ChatType_Incoming);
      }

      // Accept the chat session (from Alice)
      bob.chatManager->accept(bobChat);
      
      {
         // Wait for the isComposing notification [Active] (from Alice)
         IsComposingMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onIsComposingMessage",
         5000, AlwaysTruePred(), bobChat, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Active);
         ASSERT_EQ(evt.mimeType, CPM::MimeType_TextPlain);
      }

      {
         // Wait for the isComposing notification [Idle] (from Alice)
         IsComposingMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onIsComposingMessage",
         10000, AlwaysTruePred(), bobChat, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Idle);
         ASSERT_EQ(evt.mimeType, CPM::MimeType_TextPlain);
      }

      // End the chat session
      bob.chatManager->end(bobChat);

      {
         // Wait for the end chat notification (from Bob)
         ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onChatEnded",
            5000, AlwaysTruePred(), bobChat, evt));
         ASSERT_EQ(evt.chat, bobChat);
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedLocally);
      }
   });

   // Alice's thread
   auto aliceEvents = std::async(std::launch::async, [&] () {
      {
         // Wait for the new chat notification (from Alice)
         NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onNewChat",
            5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.chatType, ChatType_Outgoing);
      }

      alice.chatManager->setIsComposingMessage(aliceChat, CPM::MimeType_TextPlain, 0, 10, 5);

      {
         // Wait for the send success notification [Active] (from Alice)
         SetIsComposingMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onSetIsComposingMessageSuccess",
            10000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Active);
      }

      // State will fall back to Idle after 5 seconds

      {
         // Wait for the send success notification [Idle]  (from Alice)
         SetIsComposingMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onSetIsComposingMessageSuccess",
            10000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Idle);
      }

      {
         // Wait for the end of chat session notification (from Bob)
         ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onChatEnded",
         5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.chat, aliceChat);
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedRemotely);
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
}

TEST_F(ChatTest, DISABLED_IsComposingNotificationComposerRefreshTimerTest) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   // Start chat session with bob
   SipChatHandle aliceChat = alice.chatManager->createChat(alice.handle);
   alice.chatManager->addParticipant(aliceChat, bob.config.uri().c_str());
   alice.chatManager->start(aliceChat);

   // Bob's thread
   auto bobEvents = std::async(std::launch::async, [&] () {
      SipChatHandle bobChat = 0;
      {
         // Wait for the new chat notification (from Alice)
         SipChatHandle h;
         NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onNewChat",
            5000, AlwaysTruePred(), h, evt));
         bobChat = h;
         ASSERT_EQ(evt.chatType, ChatType_Incoming);
      }

      // Accept the chat session (from Alice)
      bob.chatManager->accept(bobChat);
      
      {
         // Wait for the isComposing notification [Active] (from Alice)
         IsComposingMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onIsComposingMessage",
         5000, AlwaysTruePred(), bobChat, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Active);
         ASSERT_EQ(evt.mimeType, CPM::MimeType_TextPlain);
      }

      {
         // Wait for the isComposing notification [Idle] (from Alice)
         IsComposingMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onIsComposingMessage",
         10000, AlwaysTruePred(), bobChat, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Idle);
         ASSERT_EQ(evt.mimeType, CPM::MimeType_TextPlain);
      }

      // End the chat session
      bob.chatManager->end(bobChat);

      {
         // Wait for the end chat notification (from Bob)
         ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onChatEnded",
            5000, AlwaysTruePred(), bobChat, evt));
         ASSERT_EQ(evt.chat, bobChat);
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedLocally);
      }
   });

   // Alice's thread
   auto aliceEvents = std::async(std::launch::async, [&] () {
      {
         // Wait for the new chat notification (from Alice)
         NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onNewChat",
            5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.chatType, ChatType_Outgoing);
      }

      // Generate the sending of an Active state notification every 2 seconds
      alice.chatManager->setIsComposingMessage(aliceChat, CPM::MimeType_TextPlain, 0, 2, 7);

      {
         // Wait for the send success notification [Active] - right away (from Alice)
         SetIsComposingMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onSetIsComposingMessageSuccess",
            5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Active);
      }

      {
         // Wait for the send success notification [Active] - 2nd sec (from Alice)
         SetIsComposingMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onSetIsComposingMessageSuccess",
            5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Active);
      }

      {
         // Wait for the send success notification [Active] - 4th sec (from Alice)
         SetIsComposingMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onSetIsComposingMessageSuccess",
            5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Active);
      }

      {
         // Wait for the send success notification [Active] - 6th sec (from Alice)
         SetIsComposingMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onSetIsComposingMessageSuccess",
            5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Active);
      }

      {
         // Wait for the send success notification [Idle] - 7th sec (from Alice)
         SetIsComposingMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onSetIsComposingMessageSuccess",
            5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Idle);
      }

      {
         // Wait for the end of chat session notification (from Bob)
         ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onChatEnded",
         5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.chat, aliceChat);
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedRemotely);
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
}

TEST_F(ChatTest, DISABLED_IsComposingNotificationUserTyping) {
   long origTimezone = getTimezone();

   TestAccount alice("alice");
   TestAccount bob("bob");

   // Start chat session with bob
   SipChatHandle aliceChat = alice.chatManager->createChat(alice.handle);
   alice.chatManager->addParticipant(aliceChat, bob.config.uri().c_str());
   alice.chatManager->start(aliceChat);

   // Bob's thread
   auto bobEvents = std::async(std::launch::async, [&] () {
      SipChatHandle bobChat = 0;
      {
         // Wait for the new chat notification (from Alice)
         SipChatHandle h;
         NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onNewChat",
            10000, AlwaysTruePred(), h, evt));
         bobChat = h;
         ASSERT_EQ(evt.chatType, ChatType_Incoming);
      }

      // Accept the chat session (from Alice)
      bob.chatManager->accept(bobChat);
      
      // User is typing for a while, wait for the Active state notif then back to Idle

      {
         // Wait for the isComposing notification [Active] (from Alice)
         IsComposingMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onIsComposingMessage",
         10000, AlwaysTruePred(), bobChat, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Active);
         ASSERT_EQ(evt.mimeType, CPM::MimeType_TextPlain);
         ASSERT_EQ(evt.lastActive.tm_year, 70);
         ASSERT_EQ(evt.lastActive.tm_mon, 0);
         ASSERT_EQ(evt.lastActive.tm_mday, 1);
         ASSERT_EQ(evt.lastActive.tm_hour, 0);
         ASSERT_EQ(evt.lastActive.tm_min, 20);
         ASSERT_EQ(evt.lastActive.tm_sec, 34);
      }

      {
         // Wait for the isComposing notification [Idle] (from Alice)
         IsComposingMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onIsComposingMessage",
         10000, AlwaysTruePred(), bobChat, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Idle);
         ASSERT_EQ(evt.mimeType, CPM::MimeType_TextPlain);
         ASSERT_EQ(evt.lastActive.tm_year, 70);
         ASSERT_EQ(evt.lastActive.tm_mon, 0);
         ASSERT_EQ(evt.lastActive.tm_mday, 1);
         ASSERT_EQ(evt.lastActive.tm_hour, 0);
         ASSERT_EQ(evt.lastActive.tm_min, 20);
         ASSERT_EQ(evt.lastActive.tm_sec, 38);
      }

      // User is typing again for a short period, wait for the Active state notif then back to Idle

      {
         // Wait for the isComposing notification [Active] (from Alice)
         IsComposingMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onIsComposingMessage",
         10000, AlwaysTruePred(), bobChat, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Active);
         ASSERT_EQ(evt.mimeType, CPM::MimeType_TextPlain);
         ASSERT_EQ(evt.lastActive.tm_year, 70);
         ASSERT_EQ(evt.lastActive.tm_mon, 0);
         ASSERT_EQ(evt.lastActive.tm_mday, 1);
         ASSERT_EQ(evt.lastActive.tm_hour, 0);
         ASSERT_EQ(evt.lastActive.tm_min, 20);
         ASSERT_EQ(evt.lastActive.tm_sec, 43);
      }

      {
         // Wait for the isComposing notification [Idle] (from Alice)
         IsComposingMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onIsComposingMessage",
         10000, AlwaysTruePred(), bobChat, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Idle);
         ASSERT_EQ(evt.mimeType, CPM::MimeType_TextPlain);
         ASSERT_EQ(evt.lastActive.tm_year, 70);
         ASSERT_EQ(evt.lastActive.tm_mon, 0);
         ASSERT_EQ(evt.lastActive.tm_mday, 1);
         ASSERT_EQ(evt.lastActive.tm_hour, 0);
         ASSERT_EQ(evt.lastActive.tm_min, 20);
         ASSERT_EQ(evt.lastActive.tm_sec, 43);
      }

      // End the chat session
      bob.chatManager->end(bobChat);

      {
         // Wait for the end chat notification (from Bob)
         ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onChatEnded",
            10000, AlwaysTruePred(), bobChat, evt));
         ASSERT_EQ(evt.chat, bobChat);
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedLocally);
      }
   });

   // Set the date/time used in the IsComposingNotifications
   struct tm isComposingDateTime;
   isComposingDateTime.tm_year = 70;
   isComposingDateTime.tm_mon = 0;
   isComposingDateTime.tm_mday = 1;
   isComposingDateTime.tm_hour = 0;
   isComposingDateTime.tm_min = 20;
   isComposingDateTime.tm_sec = 34;
   setTimezone(0); // Local: GMT

   // Alice's thread
   auto aliceEvents = std::async(std::launch::async, [&] () {
      {
         // Wait for the new chat notification (from Alice)
         NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onNewChat",
            10000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.chatType, ChatType_Outgoing);
      }

      // User is typing
      std::cout << "User is typing" << std::endl;
      alice.chatManager->setIsComposingMessage(aliceChat, CPM::MimeType_TextPlain, &isComposingDateTime, 90, 5);

      {
         // Wait for the send success notification [Active]  (from Alice)
         SetIsComposingMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onSetIsComposingMessageSuccess",
            10000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Active);
      }

      // Wait 1 second
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      isComposingDateTime.tm_sec += 1;

      // User is typing
      std::cout << "User is typing" << std::endl;
      alice.chatManager->setIsComposingMessage(aliceChat, CPM::MimeType_TextPlain, &isComposingDateTime, 90, 5);

      // Wait 1 second
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      isComposingDateTime.tm_sec += 1;

      // User is typing
      std::cout << "User is typing" << std::endl;
      alice.chatManager->setIsComposingMessage(aliceChat, CPM::MimeType_TextPlain, &isComposingDateTime, 90, 5);

      // Wait 1 second
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      isComposingDateTime.tm_sec += 1;

      // User is typing
      std::cout << "User is typing" << std::endl;
      alice.chatManager->setIsComposingMessage(aliceChat, CPM::MimeType_TextPlain, &isComposingDateTime, 90, 5);

      // Wait 1 second
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      isComposingDateTime.tm_sec += 1;

      // User is typing
      std::cout << "User is typing" << std::endl;
      alice.chatManager->setIsComposingMessage(aliceChat, CPM::MimeType_TextPlain, &isComposingDateTime, 90, 5);

      // User has stopped typing
      std::cout << "User stopped typing" << std::endl;

      {
         // Wait for the send success notification [Idle] (from Alice)
         SetIsComposingMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onSetIsComposingMessageSuccess",
            10000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Idle);
      }

      // User started typing again, 5 seconds later
      isComposingDateTime.tm_sec += 5;
      std::cout << "User is typing again" << std::endl;
      alice.chatManager->setIsComposingMessage(aliceChat, CPM::MimeType_TextPlain, &isComposingDateTime, 90, 5);

      {
         // Wait for the send success notification [Active]  (from Alice)
         SetIsComposingMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onSetIsComposingMessageSuccess",
            10000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Active);
      }

      // User has stopped typing
      std::cout << "User stopped typing" << std::endl;

      {
         // Wait for the send success notification [Idle] (from Alice)
         SetIsComposingMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onSetIsComposingMessageSuccess",
            10000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Idle);
      }

      {
         // Wait for the end of chat session notification (from Bob)
         ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onChatEnded",
         10000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.chat, aliceChat);
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedRemotely);
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   setTimezone(origTimezone);
}

TEST_F(ChatTest, TwoWayChat2) 
{
   TestAccount alice("alice");
   TestAccount bob("bob");

   // Start chat session with bob
   SipChatHandle aliceChat = alice.chatManager->createChat(alice.handle);
   alice.chatManager->addParticipant(aliceChat, bob.config.uri().c_str());
   alice.chatManager->start(aliceChat);

   SipChatMessageHandle aliceNewMessageHandle1;
   SipChatMessageHandle aliceNewMessageHandle2;
   SipChatMessageHandle bobNewMessageHandle1;

   SipChatMessageHandle aliceDeliveryNotificationHandle1;
   SipChatMessageHandle aliceDisplayNotificationHandle1;
   SipChatMessageHandle bobDeliveryNotificationHandle1;
   SipChatMessageHandle bobDisplayNotificationHandle1;
   SipChatMessageHandle bobDeliveryNotificationHandle2;
   SipChatMessageHandle bobDisplayNotificationHandle2;

   // Bob's thread
   auto bobEvents = std::async(std::launch::async, [&] () {
      SipChatHandle bobChat = 0;
      {
         // Wait for the new chat notification (from Alice)
         SipChatHandle h;
         NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onNewChat",
            5000, AlwaysTruePred(), h, evt));
         bobChat = h;
         ASSERT_EQ(evt.chatType, ChatType_Incoming);
      }

      // Accept the chat session (from Alice)
      bob.chatManager->accept(bobChat);

      {
         // Wait for the new message notification (from Alice)
         SipChatHandle h;
         SipChat::NewMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onNewMessage",
            5000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobChat, h);
         ASSERT_EQ(evt.message, aliceNewMessageHandle1);
         ASSERT_EQ(evt.messageContent, "Chuck");
      }

      bobDeliveryNotificationHandle1 = bob.chatManager->notifyMessageDelivered(bobChat, aliceNewMessageHandle1, MessageDeliveryStatus_Delivered);

      {
         // Wait for the notify message delivered notification (from Bob)
         SipChat::NotifyMessageDeliveredSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onNotifyMessageDeliveredSuccess",
            5000, AlwaysTruePred(), bobChat, evt));
         ASSERT_EQ(evt.notification, bobDeliveryNotificationHandle1);
         ASSERT_EQ(evt.message, aliceNewMessageHandle1);
      }

      bobDisplayNotificationHandle1 = bob.chatManager->notifyMessageDisplayed(bobChat, aliceNewMessageHandle1, MessageDisplayStatus_Displayed);

      {
         // Wait for the notify message delivered notification (from Bob)
         SipChat::NotifyMessageDisplayedSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onNotifyMessageDisplayedSuccess",
            5000, AlwaysTruePred(), bobChat, evt));
         ASSERT_EQ(evt.notification, bobDisplayNotificationHandle1);
         ASSERT_EQ(evt.message, aliceNewMessageHandle1);
      }

      {
         // Wait for the isComposing notification [Active] (from Alice)
         IsComposingMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onIsComposingMessage",
         5000, AlwaysTruePred(), bobChat, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Active);
         ASSERT_EQ(evt.mimeType, CPM::MimeType_TextPlain);
      }

      {
         // Wait for the isComposing notification [Idle] (from Alice)
         IsComposingMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onIsComposingMessage",
         5000, AlwaysTruePred(), bobChat, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Idle);
         ASSERT_EQ(evt.mimeType, CPM::MimeType_TextPlain);
      }

      {
         // Wait for the new message notification (from Alice)
         SipChatHandle h;
         SipChat::NewMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onNewMessage",
            5000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobChat, h);
         ASSERT_EQ(evt.message, aliceNewMessageHandle2);
         ASSERT_EQ(evt.messageContent, "vvjjv");
      }

      bobDeliveryNotificationHandle2 = bob.chatManager->notifyMessageDelivered(bobChat, aliceNewMessageHandle2, MessageDeliveryStatus_Delivered);

      {
         // Wait for the notify message delivered notification (from Bob)
         SipChat::NotifyMessageDeliveredSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onNotifyMessageDeliveredSuccess",
            5000, AlwaysTruePred(), bobChat, evt));
         ASSERT_EQ(evt.notification, bobDeliveryNotificationHandle2);
         ASSERT_EQ(evt.message, aliceNewMessageHandle2);
      }

      bobDisplayNotificationHandle2 = bob.chatManager->notifyMessageDisplayed(bobChat, aliceNewMessageHandle2, MessageDisplayStatus_Displayed);

      {
         // Wait for the notify message delivered notification (from Bob)
         SipChat::NotifyMessageDisplayedSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onNotifyMessageDisplayedSuccess",
            5000, AlwaysTruePred(), bobChat, evt));
         ASSERT_EQ(evt.notification, bobDisplayNotificationHandle2);
         ASSERT_EQ(evt.message, aliceNewMessageHandle2);
      }

      bob.chatManager->setIsComposingMessage(bobChat);

      {
         // Wait for the response (from Bob)
         SetIsComposingMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onSetIsComposingMessageSuccess",
            5000, AlwaysTruePred(), bobChat, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Active);
      }

      {
         // Send a message. Set the requested disposition type
         cpc::vector<DispositionNotificationType> dispositionNotifications;
         dispositionNotifications.push_back(DispositionNotificationType_PositiveDelivery);
         dispositionNotifications.push_back(DispositionNotificationType_Display);
         bobNewMessageHandle1 = bob.chatManager->sendMessage(bobChat, "Hhffgg", CPM::MimeType_TextPlain, 0, dispositionNotifications);
      }

      {
         // Wait for the message sent notification (from Bob)
         SipChat::SendMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onSendMessageSuccess",
            5000, AlwaysTruePred(), bobChat, evt));
         ASSERT_EQ(evt.message, bobNewMessageHandle1);
      }

      {
         // Wait for the message delivery notification (from Alice)
         SipChat::MessageDeliveredEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onMessageDelivered",
         5000, AlwaysTruePred(), bobChat, evt));
         ASSERT_EQ(evt.notification, aliceDeliveryNotificationHandle1);
         ASSERT_EQ(evt.message, bobNewMessageHandle1);
         ASSERT_EQ(evt.messageDeliveryStatus, MessageDeliveryStatus_Delivered);
      }

      {
         // Wait for the message displayed notification (from Alice)
         SipChat::MessageDisplayedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onMessageDisplayed",
         5000, AlwaysTruePred(), bobChat, evt));
         ASSERT_EQ(evt.notification, aliceDisplayNotificationHandle1);
         ASSERT_EQ(evt.message, bobNewMessageHandle1);
         ASSERT_EQ(evt.messageDisplayStatus, MessageDisplayStatus_Displayed);
      }

      {
         // Wait for the end chat notification (from Alice)
         ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.chatEvents, "SipChatHandler::onChatEnded",
            5000, AlwaysTruePred(), bobChat, evt));
         ASSERT_EQ(evt.chat, bobChat);
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedRemotely);
      }
   });

   // Alice's thread
   auto aliceEvents = std::async(std::launch::async, [&] () {
      {
         // Wait for the new chat notification (from Alice)
         NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onNewChat",
            5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.chatType, ChatType_Outgoing);
      }

      {
         // Send a message. Set the requested disposition type
         cpc::vector<DispositionNotificationType> dispositionNotifications;
         dispositionNotifications.push_back(DispositionNotificationType_PositiveDelivery);
         dispositionNotifications.push_back(DispositionNotificationType_Display);
         aliceNewMessageHandle1 = alice.chatManager->sendMessage(aliceChat, "Chuck", CPM::MimeType_TextPlain, 0, dispositionNotifications);
      }

      {
         // Wait for the message sent notification (from Alice)
         SipChat::SendMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onSendMessageSuccess",
            5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.message, aliceNewMessageHandle1);
      }

      {
         // Wait for the message delivery notification (from Bob)
         SipChat::MessageDeliveredEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onMessageDelivered",
         5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.notification, bobDeliveryNotificationHandle1);
         ASSERT_EQ(evt.message, aliceNewMessageHandle1);
         ASSERT_EQ(evt.messageDeliveryStatus, MessageDeliveryStatus_Delivered);
      }

      {
         // Wait for the message displayed notification (from Bob)
         SipChat::MessageDisplayedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onMessageDisplayed",
         5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.notification, bobDisplayNotificationHandle1);
         ASSERT_EQ(evt.message, aliceNewMessageHandle1);
         ASSERT_EQ(evt.messageDisplayStatus, MessageDisplayStatus_Displayed);
      }

      alice.chatManager->setIsComposingMessage(aliceChat);

      {
         // Wait for the response (from Alice)
         SetIsComposingMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onSetIsComposingMessageSuccess",
            5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Active);
      }

      {
         // Send a message. Set the requested disposition type
         cpc::vector<DispositionNotificationType> dispositionNotifications;
         dispositionNotifications.push_back(DispositionNotificationType_PositiveDelivery);
         dispositionNotifications.push_back(DispositionNotificationType_Display);
         aliceNewMessageHandle2 = alice.chatManager->sendMessage(aliceChat, "vvjjv", CPM::MimeType_TextPlain, 0, dispositionNotifications);
      }

      {
         // Wait for the message sent notification (from Alice)
         SipChat::SendMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onSendMessageSuccess",
            5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.message, aliceNewMessageHandle2);
      }

      {
         // Wait for the message delivery notification (from Bob)
         SipChat::MessageDeliveredEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onMessageDelivered",
         5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.notification, bobDeliveryNotificationHandle2);
         ASSERT_EQ(evt.message, aliceNewMessageHandle2);
         ASSERT_EQ(evt.messageDeliveryStatus, MessageDeliveryStatus_Delivered);
      }

      {
         // Wait for the message displayed notification (from Bob)
         SipChat::MessageDisplayedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onMessageDisplayed",
         5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.notification, bobDisplayNotificationHandle2);
         ASSERT_EQ(evt.message, aliceNewMessageHandle2);
         ASSERT_EQ(evt.messageDisplayStatus, MessageDisplayStatus_Displayed);
      }

      {
         // Wait for the isComposing notification [Active] (from Bob)
         IsComposingMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onIsComposingMessage",
         5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Active);
         ASSERT_EQ(evt.mimeType, CPM::MimeType_TextPlain);
      }

      {
         // Wait for the isComposing notification [Idle] (from Bob)
         IsComposingMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onIsComposingMessage",
         5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Idle);
         ASSERT_EQ(evt.mimeType, CPM::MimeType_TextPlain);
      }

      {
         // Wait for the new message notification (from Bob)
         SipChatHandle h;
         SipChat::NewMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onNewMessage",
            5000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(aliceChat, h);
         ASSERT_EQ(evt.message, bobNewMessageHandle1);
         ASSERT_EQ(evt.messageContent, "Hhffgg");
      }

      aliceDeliveryNotificationHandle1 = alice.chatManager->notifyMessageDelivered(aliceChat, bobNewMessageHandle1, MessageDeliveryStatus_Delivered);

      {
         // Wait for the notify message delivered notification (from Alice)
         SipChat::NotifyMessageDeliveredSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onNotifyMessageDeliveredSuccess",
            5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.notification, aliceDeliveryNotificationHandle1);
         ASSERT_EQ(evt.message, bobNewMessageHandle1);
      }

      aliceDisplayNotificationHandle1 = alice.chatManager->notifyMessageDisplayed(aliceChat, bobNewMessageHandle1, MessageDisplayStatus_Displayed);

      {
         // Wait for the notify message displayed notification (from Alice)
         SipChat::NotifyMessageDisplayedSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onNotifyMessageDisplayedSuccess",
            5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.notification, aliceDisplayNotificationHandle1);
         ASSERT_EQ(evt.message, bobNewMessageHandle1);
      }

      // End the chat session
      alice.chatManager->end(aliceChat);

      {
         // Wait for the end of chat session notification (from Alice)
         ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "SipChatHandler::onChatEnded",
         5000, AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.chat, aliceChat);
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedLocally);
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
}

}

#endif // CPCAPI2_SIP_CHAT_MODULE

#endif // 0
