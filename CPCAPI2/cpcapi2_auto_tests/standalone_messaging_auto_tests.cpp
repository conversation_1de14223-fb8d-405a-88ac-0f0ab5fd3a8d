#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#if (CPCAPI2_BRAND_SIP_STANDALONE_MESSAGING_MODULE == 1)

#include "cpcapi2_test_fixture.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>

#include "test_framework/cpcapi2_test_framework.h"
#include "test_framework/bare_bones_sip_endpoint.h"

#include <thread>
#include <future>

using namespace CPCAPI2;
using namespace CPCAPI2::SipStandaloneMessaging;
using namespace CPCAPI2::test;

namespace {

class StandaloneMessagingTest : public CpcapiAutoTest
{
public:
   StandaloneMessagingTest() {}
   virtual ~StandaloneMessagingTest() {}

   static const bool checkTo;
   static const bool checkDateTime;
   static const bool forceLargeMode;
   static const int binaryContentLength;
};

const bool StandaloneMessagingTest::checkTo = true; // Set to false for NewPace, Genband does not support Standalone Messaging
const bool StandaloneMessagingTest::checkDateTime = true; // Set to false for NewPace,  Genband does not support Standalone Messaging
const bool StandaloneMessagingTest::forceLargeMode = false; // Set to true for NewPace,  Genband does not support Standalone Messaging
const int StandaloneMessagingTest::binaryContentLength = 1500; // Set this attribute to 712 for NewPace,  Genband does not support Standalone Messaging

TEST_F(StandaloneMessagingTest, SendMessageFailureInvalidAccount) 
{
   TestAccount alice("alice");

   // Send a message with the wrong account handle
   SipStandaloneMessageHandle newMessageHandle = alice.standaloneMessagingManager->sendMessage(123, "b", "Hi Bob");

   // Expect an error
   PhoneErrorEvent evt;
   cpc::string module;
   ASSERT_TRUE(alice.events->expectEvent("PhoneHandler::onError", 15000, HandleEqualsPred<cpc::string>("SipAccountInterface"), module, evt));
   ASSERT_EQ(evt.errorText, "Invalid account handle specified: 123");
}

TEST_F(StandaloneMessagingTest, SendMessageFailureAccountDisabled)
{
   TestAccount alice("alice");
   alice.account->disable(alice.handle);

   // Send a message using a disabled account
   SipStandaloneMessageHandle newMessageHandle = alice.standaloneMessagingManager->sendMessage(alice.handle, "b", "Hi Bob");

   // Expect an error
   PhoneErrorEvent evt;
   cpc::string module;
   ASSERT_TRUE(alice.events->expectEvent("PhoneHandler::onError", 15000, HandleEqualsPred<cpc::string>("SipAccountInterface"), module, evt));
   ASSERT_EQ(evt.errorText, "Account not enabled: 512");
}

TEST_F(StandaloneMessagingTest, SendMessageFailureInvalidTargetAddress)
{
   TestAccount alice("alice");

   // Send a message to an invalid SIP address
   SipStandaloneMessageHandle newMessageHandle = alice.standaloneMessagingManager->sendMessage(alice.handle, "b", "Hi Bob");

   {
      SipStandaloneMessaging::ErrorEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.standaloneMessagingEvents, "SipStandaloneMessagingHandler::onError",
         5000, AlwaysTruePred(), alice.handle, evt));
		ASSERT_EQ(evt.errorText, "Failed to parse target URI 'b'");
   }
}

TEST_F(StandaloneMessagingTest, SendMessageFailureTargetSameAsOriginator)
{
   TestAccount alice("alice");

   // Send a message to herself
   SipStandaloneMessageHandle newMessageHandle = alice.standaloneMessagingManager->sendMessage(alice.handle, alice.config.uri().c_str(), "Hi Bob");

   {
      SipAccount::SipAccountHandle h;
      SipStandaloneMessaging::ErrorEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.standaloneMessagingEvents, "SipStandaloneMessagingHandler::onError",
         5000, AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
		ASSERT_EQ(evt.errorText, "Cannot send message. Originator and sender are the same");
   }
}

TEST_F(StandaloneMessagingTest, SendMessageFailureNoText) 
{
   TestAccount alice("alice");
   TestAccount bob("bob");

   SipStandaloneMessageHandle newMessageHandle = alice.standaloneMessagingManager->sendMessage(alice.handle, bob.config.uri().c_str(), ""); // Sending an empty message - not allowed

   {
      // Wait for the new message notification (from Alice)
      SipStandaloneMessaging::SendMessageFailureEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.standaloneMessagingEvents, "SipStandaloneMessagingHandler::onSendMessageFailure",
         5000, AlwaysTruePred(), alice.handle, evt));
      ASSERT_EQ(evt.message, newMessageHandle);
   }
}

TEST_F(StandaloneMessagingTest, SendPagerModeStandaloneMessage)
{
   TestAccount alice("alice");
   TestAccount bob("bob");

   // Set the date/time of the message to be sent
   struct tm messageDateTime;
   messageDateTime.tm_year = 70;
   messageDateTime.tm_mon = 0;
   messageDateTime.tm_mday = 1;
   messageDateTime.tm_hour = 0;
   messageDateTime.tm_min = 20;
   messageDateTime.tm_sec = 34;

   // Send a message
   SipStandaloneMessageHandle newMessageHandle;
   newMessageHandle = alice.standaloneMessagingManager->sendMessage(alice.handle, bob.config.uri().c_str(), "Hi Bob", MimeType_TextPlain, &messageDateTime);

   // Bob's thread
   auto bobEvents = std::async(std::launch::async, [&] () {
      {
         // Wait for the new message notification (from Alice)
         SipStandaloneMessaging::NewMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.standaloneMessagingEvents, "SipStandaloneMessagingHandler::onNewMessage",
            5000, AlwaysTruePred(), bob.handle, evt));
         ASSERT_EQ(evt.message, newMessageHandle);
         ASSERT_EQ(evt.from, alice.config.uri().c_str());
         ASSERT_EQ(evt.to, bob.config.uri().c_str());
         if (checkDateTime)
         {
            ASSERT_EQ(evt.datetime.tm_year, messageDateTime.tm_year);
            ASSERT_EQ(evt.datetime.tm_mon, messageDateTime.tm_mon);
            ASSERT_EQ(evt.datetime.tm_mday, messageDateTime.tm_mday);
            ASSERT_EQ(evt.datetime.tm_hour, messageDateTime.tm_hour);
            ASSERT_EQ(evt.datetime.tm_min, messageDateTime.tm_min);
            ASSERT_EQ(evt.datetime.tm_sec, messageDateTime.tm_sec);
         }
         ASSERT_EQ(evt.mimeType, MimeType_TextPlain);
         ASSERT_EQ(evt.messageContent, "Hi Bob");
      }
   });

   // Alice's thread
   auto aliceEvents = std::async(std::launch::async, [&] () {
      {
         // Wait for the message sent notification (from Alice)
         SipStandaloneMessaging::SendMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.standaloneMessagingEvents, "SipStandaloneMessagingHandler::onSendMessageSuccess",
            5000, AlwaysTruePred(), alice.handle, evt));
         ASSERT_EQ(evt.message, newMessageHandle);
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
}

TEST_F(StandaloneMessagingTest, SendPagerModeStandaloneMessageWithDeliveryNotification)
{
   TestAccount alice("alice");
   TestAccount bob("bob");

   long origTimezone = timezone;

   // Set the date/time of the message to be sent
   struct tm messageDateTime;
   messageDateTime.tm_year = 70;
   messageDateTime.tm_mon = 0;
   messageDateTime.tm_mday = 1;
   messageDateTime.tm_hour = 0;
   messageDateTime.tm_min = 20;
   messageDateTime.tm_sec = 34;
   timezone = 0; // Local: GMT

   SipStandaloneMessageHandle deliveryNotificationHandle;

   // Send a message. Set the requested disposition type
   SipStandaloneMessageHandle newMessageHandle;
   cpc::vector<DispositionNotificationType> dispositionNotifications;
   dispositionNotifications.push_back(DispositionNotificationType_PositiveDelivery);
   newMessageHandle = alice.standaloneMessagingManager->sendMessage(alice.handle, bob.config.uri().c_str(), "Hi Bob", MimeType_TextPlain, &messageDateTime, dispositionNotifications);

   // Bob's thread
   auto bobEvents = std::async(std::launch::async, [&] () {
      // Wait for the new message notification (from Alice)
      SipStandaloneMessaging::NewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.standaloneMessagingEvents, "SipStandaloneMessagingHandler::onNewMessage",
         5000, AlwaysTruePred(), bob.handle, evt));
      ASSERT_EQ(evt.message, newMessageHandle);
      ASSERT_EQ(evt.from, alice.config.uri().c_str());
      ASSERT_EQ(evt.to, bob.config.uri().c_str());
      ASSERT_EQ(evt.mimeType, MimeType_TextPlain);
      ASSERT_EQ(evt.messageContent, "Hi Bob");
      if (checkDateTime)
      {
         ASSERT_EQ(evt.datetime.tm_year, messageDateTime.tm_year);
         ASSERT_EQ(evt.datetime.tm_mon, messageDateTime.tm_mon);
         ASSERT_EQ(evt.datetime.tm_mday, messageDateTime.tm_mday);
         ASSERT_EQ(evt.datetime.tm_hour, messageDateTime.tm_hour);
         ASSERT_EQ(evt.datetime.tm_min, messageDateTime.tm_min);
         ASSERT_EQ(evt.datetime.tm_sec, messageDateTime.tm_sec);
      }

      // Make sure we get a request for the message delivery disposition notification
      ASSERT_TRUE(std::find(evt.dispositionNotifications.begin(), evt.dispositionNotifications.end(), DispositionNotificationType_PositiveDelivery) != evt.dispositionNotifications.end());

      // Send the message delivered notification
      deliveryNotificationHandle = bob.standaloneMessagingManager->notifyMessageDelivered(bob.handle, newMessageHandle, MessageDeliveryStatus_Delivered, evt.from, evt.datetimeString);

      {
         // Wait for the notify message delivered notification (from Bob)
         SipStandaloneMessaging::NotifyMessageDeliveredSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.standaloneMessagingEvents, "SipStandaloneMessagingHandler::onNotifyMessageDeliveredSuccess",
            5000, AlwaysTruePred(), bob.handle, evt));
         ASSERT_EQ(evt.notification, deliveryNotificationHandle);
         ASSERT_EQ(evt.message, newMessageHandle);
      }
   });

   // Alice's thread
   auto aliceEvents = std::async(std::launch::async, [&] () {
      {
         // Wait for the send message notification (from Alice)
         SipStandaloneMessaging::SendMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.standaloneMessagingEvents, "SipStandaloneMessagingHandler::onSendMessageSuccess",
            5000, AlwaysTruePred(), alice.handle, evt));
         ASSERT_EQ(evt.message, newMessageHandle);
      }

      {
         // Wait for the message delivery notification (from Bob)
         SipStandaloneMessaging::MessageDeliveredEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.standaloneMessagingEvents, "SipStandaloneMessagingHandler::onMessageDelivered",
         5000, AlwaysTruePred(), alice.handle, evt));
         ASSERT_EQ(evt.notification, deliveryNotificationHandle);
         ASSERT_EQ(evt.message, newMessageHandle);
         ASSERT_EQ(evt.messageDeliveryStatus, MessageDeliveryStatus_Delivered);
         if (checkDateTime)
         {
            ASSERT_EQ(evt.datetime.tm_year, messageDateTime.tm_year);
            ASSERT_EQ(evt.datetime.tm_mon, messageDateTime.tm_mon);
            ASSERT_EQ(evt.datetime.tm_mday, messageDateTime.tm_mday);
            ASSERT_EQ(evt.datetime.tm_hour, messageDateTime.tm_hour);
            ASSERT_EQ(evt.datetime.tm_min, messageDateTime.tm_min);
            ASSERT_EQ(evt.datetime.tm_sec, messageDateTime.tm_sec);
         }
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   timezone = origTimezone;
}

TEST_F(StandaloneMessagingTest, SendReceivePagerModeStandaloneMessageWithDeliveryNotificationFailure)
{
   TestAccount alice("alice");
   TestAccount bob("bob");

   SipStandaloneMessageHandle deliveryNotificationHandle;

   // Send a message. Set the requested disposition type
   cpc::vector<DispositionNotificationType> dispositionNotifications;
   dispositionNotifications.push_back(DispositionNotificationType_PositiveDelivery);
   SipStandaloneMessageHandle newMessageHandle = alice.standaloneMessagingManager->sendMessage(alice.handle, bob.config.uri().c_str(), "Hi Bob", MimeType_TextPlain, 0, dispositionNotifications);

   // Bob's thread
   auto bobEvents = std::async(std::launch::async, [&] () {
      // Wait for the new message notification (from Alice)
      SipStandaloneMessaging::NewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.standaloneMessagingEvents, "SipStandaloneMessagingHandler::onNewMessage",
         5000, AlwaysTruePred(), bob.handle, evt));
      ASSERT_EQ(evt.message, newMessageHandle);
      ASSERT_EQ(evt.messageContent, "Hi Bob");

      // Make sure we get a request for the message delivery disposition notification
      ASSERT_TRUE(std::find(evt.dispositionNotifications.begin(), evt.dispositionNotifications.end(), DispositionNotificationType_PositiveDelivery) != evt.dispositionNotifications.end());

      // Send the message delivered notification
      cpc::string wrongBobAddress = "sip:wrong_bob@" + bob.config.settings.domain;
      deliveryNotificationHandle = bob.standaloneMessagingManager->notifyMessageDelivered(bob.handle, newMessageHandle, MessageDeliveryStatus_Delivered, wrongBobAddress.c_str(), evt.datetimeString);

      {
         // Wait for the notify message delivery notification (from Bob)
         SipStandaloneMessaging::NotifyMessageDeliveredFailureEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.standaloneMessagingEvents, "SipStandaloneMessagingHandler::onNotifyMessageDeliveredFailure",
            5000, AlwaysTruePred(), bob.handle, evt));
         ASSERT_EQ(evt.notification, deliveryNotificationHandle);
         ASSERT_EQ(evt.message, newMessageHandle);
      }
   });

   // Alice's thread
   auto aliceEvents = std::async(std::launch::async, [&] () {
      {
         // Wait for the send message notification (from Alice)
         SipStandaloneMessaging::SendMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.standaloneMessagingEvents, "SipStandaloneMessagingHandler::onSendMessageSuccess",
            5000, AlwaysTruePred(), alice.handle, evt));
         ASSERT_EQ(evt.message, newMessageHandle);
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
}

TEST_F(StandaloneMessagingTest, SendReceivePagerModeStandaloneMessageWithDisplayNotification)
{
   TestAccount alice("alice");
   TestAccount bob("bob");

   SipStandaloneMessageHandle displayNotificationHandle;

   // Send a message. Set the requested disposition type
   cpc::vector<DispositionNotificationType> dispositionNotifications;
   dispositionNotifications.push_back(DispositionNotificationType_Display);
   SipStandaloneMessageHandle newMessageHandle = alice.standaloneMessagingManager->sendMessage(alice.handle, bob.config.uri().c_str(), "Hi Bob", MimeType_TextPlain, 0, dispositionNotifications);

   // Bob's thread
   auto bobEvents = std::async(std::launch::async, [&] () {
      // Wait for the new message notification (from Alice)
      SipStandaloneMessaging::NewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.standaloneMessagingEvents, "SipStandaloneMessagingHandler::onNewMessage",
         5000, AlwaysTruePred(), bob.handle, evt));
      ASSERT_EQ(evt.message, newMessageHandle);
      ASSERT_EQ(evt.messageContent, "Hi Bob");

      // Make sure we get a request for the message display disposition notification
      ASSERT_TRUE(std::find(evt.dispositionNotifications.begin(), evt.dispositionNotifications.end(), DispositionNotificationType_Display) != evt.dispositionNotifications.end());

      // Send the message displayed notification
      displayNotificationHandle = bob.standaloneMessagingManager->notifyMessageDisplayed(bob.handle, newMessageHandle, MessageDisplayStatus_Displayed, evt.from, evt.datetimeString);

      {
         // Wait for the notify message displayed notification (from Bob)
         SipStandaloneMessaging::NotifyMessageDisplayedSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.standaloneMessagingEvents, "SipStandaloneMessagingHandler::onNotifyMessageDisplayedSuccess",
            5000, AlwaysTruePred(), bob.handle, evt));
         ASSERT_EQ(evt.notification, displayNotificationHandle);
         ASSERT_EQ(evt.message, newMessageHandle);
      }
   });

   // Alice's thread
   auto aliceEvents = std::async(std::launch::async, [&] () {
      {
         // Wait for the send message notification (from Alice)
         SipStandaloneMessaging::SendMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.standaloneMessagingEvents, "SipStandaloneMessagingHandler::onSendMessageSuccess",
            5000, AlwaysTruePred(), alice.handle, evt));
         ASSERT_EQ(evt.message, newMessageHandle);
      }

      {
         // Wait for the message display notification (from Bob)
         SipStandaloneMessaging::MessageDisplayedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.standaloneMessagingEvents, "SipStandaloneMessagingHandler::onMessageDisplayed",
         5000, AlwaysTruePred(), alice.handle, evt));
         ASSERT_EQ(evt.notification, displayNotificationHandle);
         ASSERT_EQ(evt.message, newMessageHandle);
         ASSERT_EQ(evt.messageDisplayStatus, MessageDisplayStatus_Displayed);
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
}

TEST_F(StandaloneMessagingTest, SendReceivePagerModeStandaloneMessageWithDisplayNotificationFailure)
{
   TestAccount alice("alice");
   TestAccount bob("bob");

   SipStandaloneMessageHandle displayNotificationHandle;

   // Send a message. Set the requested disposition type
   cpc::vector<DispositionNotificationType> dispositionNotifications;
   dispositionNotifications.push_back(DispositionNotificationType_Display);
   SipStandaloneMessageHandle newMessageHandle = alice.standaloneMessagingManager->sendMessage(alice.handle, bob.config.uri().c_str(), "Hi Bob", MimeType_TextPlain, 0, dispositionNotifications);

   // Bob's thread
   auto bobEvents = std::async(std::launch::async, [&] () {
      // Wait for the new message notification (from Alice)
      SipStandaloneMessaging::NewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.standaloneMessagingEvents, "SipStandaloneMessagingHandler::onNewMessage",
         5000, AlwaysTruePred(), bob.handle, evt));
      ASSERT_EQ(evt.message, newMessageHandle);
      ASSERT_EQ(evt.messageContent, "Hi Bob");

      // Make sure we get a request for the message display disposition notification
      ASSERT_TRUE(std::find(evt.dispositionNotifications.begin(), evt.dispositionNotifications.end(), DispositionNotificationType_Display) != evt.dispositionNotifications.end());

      // Send the message displayed notification
      cpc::string wrongBobAddress = "sip:wrong_bob@" + bob.config.settings.domain;
      displayNotificationHandle = bob.standaloneMessagingManager->notifyMessageDisplayed(bob.handle, newMessageHandle, MessageDisplayStatus_Displayed, wrongBobAddress.c_str(), evt.datetimeString);

      {
         // Wait for the notify message displayed notification (from Bob)
         SipStandaloneMessaging::NotifyMessageDisplayedFailureEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.standaloneMessagingEvents, "SipStandaloneMessagingHandler::onNotifyMessageDisplayedFailure",
            5000, AlwaysTruePred(), bob.handle, evt));
         ASSERT_EQ(evt.notification, displayNotificationHandle);
         ASSERT_EQ(evt.message, newMessageHandle);
      }
   });

   // Alice's thread
   auto aliceEvents = std::async(std::launch::async, [&] () {
      {
         // Wait for the send message notification (from Alice)
         SipStandaloneMessaging::SendMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.standaloneMessagingEvents, "SipStandaloneMessagingHandler::onSendMessageSuccess",
            5000, AlwaysTruePred(), alice.handle, evt));
         ASSERT_EQ(evt.message, newMessageHandle);
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
}

TEST_F(StandaloneMessagingTest, SendReceivePagerModeStandaloneMessageWithDeliveryAndDisplayNotification)
{
   TestAccount alice("alice");
   TestAccount bob("bob");

   SipStandaloneMessageHandle deliveryNotificationHandle;
   SipStandaloneMessageHandle displayNotificationHandle;

   // Send a message. Set the requested disposition type
   cpc::vector<DispositionNotificationType> dispositionNotifications;
   dispositionNotifications.push_back(DispositionNotificationType_PositiveDelivery);
   dispositionNotifications.push_back(DispositionNotificationType_Display);
   SipStandaloneMessageHandle newMessageHandle = alice.standaloneMessagingManager->sendMessage(alice.handle, bob.config.uri().c_str(), "Hi Bob", MimeType_ImageJpeg, 0, dispositionNotifications);

   // Bob's thread
   auto bobEvents = std::async(std::launch::async, [&] () {
      // Wait for the new message notification (from Alice)
      SipStandaloneMessaging::NewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.standaloneMessagingEvents, "SipStandaloneMessagingHandler::onNewMessage",
         5000, AlwaysTruePred(), bob.handle, evt));
      ASSERT_EQ(evt.message, newMessageHandle);
      ASSERT_EQ(evt.messageContent, "Hi Bob");

      // Make sure we get a request for the message display disposition notification
      ASSERT_TRUE(std::find(evt.dispositionNotifications.begin(), evt.dispositionNotifications.end(), DispositionNotificationType_PositiveDelivery) != evt.dispositionNotifications.end());
      ASSERT_TRUE(std::find(evt.dispositionNotifications.begin(), evt.dispositionNotifications.end(), DispositionNotificationType_Display) != evt.dispositionNotifications.end());

      // Send the message delivered notification
      deliveryNotificationHandle = bob.standaloneMessagingManager->notifyMessageDelivered(bob.handle, newMessageHandle, MessageDeliveryStatus_Delivered, evt.from, evt.datetimeString);

      {
         // Wait for the notify message displayed notification (from Bob)
         SipStandaloneMessaging::NotifyMessageDeliveredSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.standaloneMessagingEvents, "SipStandaloneMessagingHandler::onNotifyMessageDeliveredSuccess",
            5000, AlwaysTruePred(), bob.handle, evt));
         ASSERT_EQ(evt.notification, deliveryNotificationHandle);
         ASSERT_EQ(evt.message, newMessageHandle);
      }

      // Send the message displayed notification
      displayNotificationHandle = bob.standaloneMessagingManager->notifyMessageDisplayed(bob.handle, newMessageHandle, MessageDisplayStatus_Displayed, evt.from, evt.datetimeString);

      {
         // Wait for the notify message displayed notification (from Bob)
         SipStandaloneMessaging::NotifyMessageDisplayedSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.standaloneMessagingEvents, "SipStandaloneMessagingHandler::onNotifyMessageDisplayedSuccess",
            5000, AlwaysTruePred(), bob.handle, evt));
         ASSERT_EQ(evt.notification, displayNotificationHandle);
         ASSERT_EQ(evt.message, newMessageHandle);
      }
   });

   // Alice's thread
   auto aliceEvents = std::async(std::launch::async, [&] () {
      {
         // Wait for the send message notification (from Alice)
         SipStandaloneMessaging::SendMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.standaloneMessagingEvents, "SipStandaloneMessagingHandler::onSendMessageSuccess",
            5000, AlwaysTruePred(), alice.handle, evt));
         ASSERT_EQ(evt.message, newMessageHandle);
      }

      {
         // Wait for the message delivery notification (from Bob)
         SipStandaloneMessaging::MessageDeliveredEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.standaloneMessagingEvents, "SipStandaloneMessagingHandler::onMessageDelivered",
         5000, AlwaysTruePred(), alice.handle, evt));
         ASSERT_EQ(evt.notification, deliveryNotificationHandle);
         ASSERT_EQ(evt.message, newMessageHandle);
         ASSERT_EQ(evt.messageDeliveryStatus, MessageDeliveryStatus_Delivered);
      }

      {
         // Wait for the message display notification (from Bob)
         SipStandaloneMessaging::MessageDisplayedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.standaloneMessagingEvents, "SipStandaloneMessagingHandler::onMessageDisplayed",
         5000, AlwaysTruePred(), alice.handle, evt));
         ASSERT_EQ(evt.notification, displayNotificationHandle);
         ASSERT_EQ(evt.message, newMessageHandle);
         ASSERT_EQ(evt.messageDisplayStatus, MessageDisplayStatus_Displayed);
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
}

TEST_F(StandaloneMessagingTest, SendLargeModeStandaloneMessage)
{
   TestAccount alice("alice");
   TestAccount bob("bob");

   // Prepare the content of the message to send
   char messageContent[binaryContentLength];
   for (int i = 0; i < binaryContentLength; i++)
   {
      messageContent[i] = i % 256;
   }

   // Send a message
   SipStandaloneMessageHandle newMessageHandle;
   newMessageHandle = alice.standaloneMessagingManager->sendMessage(alice.handle, bob.config.uri().c_str(), cpc::string(messageContent, binaryContentLength), MimeType_ImageJpeg, 0, cpc::vector<DispositionNotificationType>(), forceLargeMode);

   // Bob's thread
   auto bobEvents = std::async(std::launch::async, [&] () {
      {
         // Wait for the new message notification (from Alice)
         SipStandaloneMessaging::NewMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.standaloneMessagingEvents, "SipStandaloneMessagingHandler::onNewMessage",
            5000, AlwaysTruePred(), bob.handle, evt));
         ASSERT_EQ(evt.message, newMessageHandle);
         ASSERT_EQ(evt.from, alice.config.uri().c_str());
         if (checkTo)
         {
            ASSERT_EQ(evt.to, bob.config.uri().c_str());
         }
         ASSERT_EQ(evt.mimeType, MimeType_ImageJpeg);
         ASSERT_EQ(evt.messageContent.size(), binaryContentLength);
         for (int i = 0; i < binaryContentLength; i++)
         {
            ASSERT_EQ(evt.messageContent.c_str()[i], messageContent[i]);
         }
      }
   });

   // Alice's thread
   auto aliceEvents = std::async(std::launch::async, [&] () {
      {
         // Wait for the message sent notification (from Alice)
         SipStandaloneMessaging::SendMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.standaloneMessagingEvents, "SipStandaloneMessagingHandler::onSendMessageSuccess",
            5000, AlwaysTruePred(), alice.handle, evt));
         ASSERT_EQ(evt.message, newMessageHandle);
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
}

TEST_F(StandaloneMessagingTest, SendReceiveLargeModeStandaloneMessageWithDeliveryNotification)
{
   TestAccount alice("alice");
   TestAccount bob("bob");

   // Prepare the content of the message to send
   char messageContent[binaryContentLength];
   for (int i = 0; i < binaryContentLength; i++)
   {
      messageContent[i] = i % 256;
   }

   SipStandaloneMessageHandle deliveryNotificationHandle;

   // Send a message. Set the requested disposition type
   cpc::vector<DispositionNotificationType> dispositionNotifications;
   dispositionNotifications.push_back(DispositionNotificationType_PositiveDelivery);
   SipStandaloneMessageHandle newMessageHandle = alice.standaloneMessagingManager->sendMessage(alice.handle, bob.config.uri().c_str(), cpc::string(messageContent, binaryContentLength), MimeType_ImageJpeg, 0, dispositionNotifications, forceLargeMode);

   // Bob's thread
   auto bobEvents = std::async(std::launch::async, [&] () {
      // Wait for the new message notification (from Alice)
      SipStandaloneMessaging::NewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.standaloneMessagingEvents, "SipStandaloneMessagingHandler::onNewMessage",
         5000, AlwaysTruePred(), bob.handle, evt));
      ASSERT_EQ(evt.message, newMessageHandle);
      ASSERT_EQ(evt.messageContent.size(), binaryContentLength);
      for (int i = 0; i < binaryContentLength; i++)
      {
         ASSERT_EQ(evt.messageContent.c_str()[i], messageContent[i]);
      }

      // Make sure we get a request for the message delivery disposition notification
      ASSERT_TRUE(std::find(evt.dispositionNotifications.begin(), evt.dispositionNotifications.end(), DispositionNotificationType_PositiveDelivery) != evt.dispositionNotifications.end());

      // Send the message delivered notification
      deliveryNotificationHandle = bob.standaloneMessagingManager->notifyMessageDelivered(bob.handle, newMessageHandle, MessageDeliveryStatus_Delivered, evt.from, evt.datetimeString);

      {
         // Wait for the notify message delivered notification (from Bob)
         SipStandaloneMessaging::NotifyMessageDeliveredSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.standaloneMessagingEvents, "SipStandaloneMessagingHandler::onNotifyMessageDeliveredSuccess",
            5000, AlwaysTruePred(), bob.handle, evt));
         ASSERT_EQ(evt.notification, deliveryNotificationHandle);
         ASSERT_EQ(evt.message, newMessageHandle);
      }
   });

   // Alice's thread
   auto aliceEvents = std::async(std::launch::async, [&] () {
      {
         // Wait for the send message notification (from Alice)
         SipStandaloneMessaging::SendMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.standaloneMessagingEvents, "SipStandaloneMessagingHandler::onSendMessageSuccess",
            5000, AlwaysTruePred(), alice.handle, evt));
         ASSERT_EQ(evt.message, newMessageHandle);
      }

      {
         // Wait for the message delivered notification (from Bob)
         SipStandaloneMessaging::MessageDeliveredEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.standaloneMessagingEvents, "SipStandaloneMessagingHandler::onMessageDelivered",
         5000, AlwaysTruePred(), alice.handle, evt));
         ASSERT_EQ(evt.notification, deliveryNotificationHandle);
         ASSERT_EQ(evt.message, newMessageHandle);
         ASSERT_EQ(evt.messageDeliveryStatus, MessageDeliveryStatus_Delivered);
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
}

TEST_F(StandaloneMessagingTest, SendReceiveLargeModeStandaloneMessageWithDisplayNotification)
{
   TestAccount alice("alice");
   TestAccount bob("bob");

   // Prepare the content of the message to send
   char messageContent[binaryContentLength];
   for (int i = 0; i < binaryContentLength; i++)
   {
      messageContent[i] = i % 256;
   }

   SipStandaloneMessageHandle displayNotificationHandle;

   // Send a message. Set the requested disposition type
   cpc::vector<DispositionNotificationType> dispositionNotifications;
   dispositionNotifications.push_back(DispositionNotificationType_Display);
   SipStandaloneMessageHandle newMessageHandle = alice.standaloneMessagingManager->sendMessage(alice.handle, bob.config.uri().c_str(), cpc::string(messageContent, binaryContentLength), MimeType_ImageJpeg, 0, dispositionNotifications, forceLargeMode);

   // Bob's thread
   auto bobEvents = std::async(std::launch::async, [&] () {
      // Wait for the new message notification (from Alice)
      SipStandaloneMessaging::NewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.standaloneMessagingEvents, "SipStandaloneMessagingHandler::onNewMessage",
         5000, AlwaysTruePred(), bob.handle, evt));
      ASSERT_EQ(evt.message, newMessageHandle);
      ASSERT_EQ(evt.messageContent.size(), binaryContentLength);
      for (int i = 0; i < binaryContentLength; i++)
      {
         ASSERT_EQ(evt.messageContent.c_str()[i], messageContent[i]);
      }

      // Make sure we get a request for the message display disposition notification
      ASSERT_TRUE(std::find(evt.dispositionNotifications.begin(), evt.dispositionNotifications.end(), DispositionNotificationType_Display) != evt.dispositionNotifications.end());

      // Send the message displayed notification
      displayNotificationHandle = bob.standaloneMessagingManager->notifyMessageDisplayed(bob.handle, newMessageHandle, MessageDisplayStatus_Displayed, evt.from, evt.datetimeString);

      {
         // Wait for the notify message displayed notification (from Bob)
         SipStandaloneMessaging::NotifyMessageDisplayedSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.standaloneMessagingEvents, "SipStandaloneMessagingHandler::onNotifyMessageDisplayedSuccess",
            5000, AlwaysTruePred(), bob.handle, evt));
         ASSERT_EQ(evt.notification, displayNotificationHandle);
         ASSERT_EQ(evt.message, newMessageHandle);
      }
   });

   // Alice's thread
   auto aliceEvents = std::async(std::launch::async, [&] () {
      {
         // Wait for the send message notification (from Alice)
         SipStandaloneMessaging::SendMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.standaloneMessagingEvents, "SipStandaloneMessagingHandler::onSendMessageSuccess",
            5000, AlwaysTruePred(), alice.handle, evt));
         ASSERT_EQ(evt.message, newMessageHandle);
      }

      {
         // Wait for the message display notification (from Bob)
         SipStandaloneMessaging::MessageDisplayedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.standaloneMessagingEvents, "SipStandaloneMessagingHandler::onMessageDisplayed",
         5000, AlwaysTruePred(), alice.handle, evt));
         ASSERT_EQ(evt.notification, displayNotificationHandle);
         ASSERT_EQ(evt.message, newMessageHandle);
         ASSERT_EQ(evt.messageDisplayStatus, MessageDisplayStatus_Displayed);
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
}

TEST_F(StandaloneMessagingTest, SendReceiveLargeModeStandaloneMessageWithDeliveryAndDisplayNotification)
{
   TestAccount alice("alice");
   TestAccount bob("bob");

   // Prepare the content of the message to send
   char messageContent[binaryContentLength];
   for (int i = 0; i < binaryContentLength; i++)
   {
      messageContent[i] = i % 256;
   }

   SipStandaloneMessageHandle deliveryNotificationHandle;
   SipStandaloneMessageHandle displayNotificationHandle;

   // Send a message. Set the requested disposition type
   cpc::vector<DispositionNotificationType> dispositionNotifications;
   dispositionNotifications.push_back(DispositionNotificationType_PositiveDelivery);
   dispositionNotifications.push_back(DispositionNotificationType_Display);
   SipStandaloneMessageHandle newMessageHandle = alice.standaloneMessagingManager->sendMessage(alice.handle, bob.config.uri().c_str(), cpc::string(messageContent, binaryContentLength), MimeType_ImageJpeg, 0, dispositionNotifications, forceLargeMode);

   // Bob's thread
   auto bobEvents = std::async(std::launch::async, [&] () {
      // Wait for the new message notification (from Alice)
      SipStandaloneMessaging::NewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.standaloneMessagingEvents, "SipStandaloneMessagingHandler::onNewMessage",
         5000, AlwaysTruePred(), bob.handle, evt));
      ASSERT_EQ(evt.message, newMessageHandle);
      ASSERT_EQ(evt.messageContent.size(), binaryContentLength);
      for (int i = 0; i < binaryContentLength; i++)
      {
         ASSERT_EQ(evt.messageContent.c_str()[i], messageContent[i]);
      }

      // Make sure we get a request for the message delivery and display disposition notification
      ASSERT_TRUE(std::find(evt.dispositionNotifications.begin(), evt.dispositionNotifications.end(), DispositionNotificationType_PositiveDelivery) != evt.dispositionNotifications.end());
      ASSERT_TRUE(std::find(evt.dispositionNotifications.begin(), evt.dispositionNotifications.end(), DispositionNotificationType_Display) != evt.dispositionNotifications.end());

      // Send the message delivered notification
      deliveryNotificationHandle = bob.standaloneMessagingManager->notifyMessageDelivered(bob.handle, newMessageHandle, MessageDeliveryStatus_Delivered, evt.from, evt.datetimeString);

      {
         // Wait for the notify message displayed notification (from Bob)
         SipStandaloneMessaging::NotifyMessageDeliveredSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.standaloneMessagingEvents, "SipStandaloneMessagingHandler::onNotifyMessageDeliveredSuccess",
            5000, AlwaysTruePred(), bob.handle, evt));
         ASSERT_EQ(evt.notification, deliveryNotificationHandle);
         ASSERT_EQ(evt.message, newMessageHandle);
      }

      // Send the message displayed notification
      displayNotificationHandle = bob.standaloneMessagingManager->notifyMessageDisplayed(bob.handle, newMessageHandle, MessageDisplayStatus_Displayed, evt.from, evt.datetimeString);

      {
         // Wait for the notify message displayed notification (from Bob)
         SipStandaloneMessaging::NotifyMessageDisplayedSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.standaloneMessagingEvents, "SipStandaloneMessagingHandler::onNotifyMessageDisplayedSuccess",
            5000, AlwaysTruePred(), bob.handle, evt));
         ASSERT_EQ(evt.notification, displayNotificationHandle);
         ASSERT_EQ(evt.message, newMessageHandle);
      }
   });

   // Alice's thread
   auto aliceEvents = std::async(std::launch::async, [&] () {
      {
         // Wait for the send message notification (from Alice)
         SipStandaloneMessaging::SendMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.standaloneMessagingEvents, "SipStandaloneMessagingHandler::onSendMessageSuccess",
            5000, AlwaysTruePred(), alice.handle, evt));
         ASSERT_EQ(evt.message, newMessageHandle);
      }

      {
         // Wait for the message delivery notification (from Bob)
         SipStandaloneMessaging::MessageDeliveredEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.standaloneMessagingEvents, "SipStandaloneMessagingHandler::onMessageDelivered",
         5000, AlwaysTruePred(), alice.handle, evt));
         ASSERT_EQ(evt.notification, deliveryNotificationHandle);
         ASSERT_EQ(evt.message, newMessageHandle);
         ASSERT_EQ(evt.messageDeliveryStatus, MessageDeliveryStatus_Delivered);
      }

      {
         // Wait for the message display notification (from Bob)
         SipStandaloneMessaging::MessageDisplayedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.standaloneMessagingEvents, "SipStandaloneMessagingHandler::onMessageDisplayed",
         5000, AlwaysTruePred(), alice.handle, evt));
         ASSERT_EQ(evt.notification, displayNotificationHandle);
         ASSERT_EQ(evt.message, newMessageHandle);
         ASSERT_EQ(evt.messageDisplayStatus, MessageDisplayStatus_Displayed);
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
}

}

#endif // CPCAPI2_SIP_STANDALONE_MESSAGING_MODULE
