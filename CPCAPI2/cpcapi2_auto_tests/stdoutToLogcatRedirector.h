#ifndef STDOUTTOLOGCATREDIRECTOR_H
#define STDOUTTOLOGCATREDIRECTOR_H

#include "stdoutRedirector.h"

namespace CPCAPI2
{
    class StdoutToLogcatRedirector : public StdoutRedirector
    {
        public:
            StdoutToLogcatRedirector();
            virtual ~StdoutToLogcatRedirector();
            void runLoggingThread();
            static void* loggingFunc(void*);

        private:
            static int fd[2];
            static const char* LOGOUTPUT;
            int errNum;
            pthread_t loggingThread;
    };
}

#endif
