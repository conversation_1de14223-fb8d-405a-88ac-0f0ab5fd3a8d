#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#if (CPCAPI2_BRAND_ORCHESTRATION_SERVER_MODULE == 1)

#include "cpcapi2_test_fixture.h"
#include "test_call_events.h"
#include "test_events.h"
#include "test_account_events.h"

#include "../../impl/util/CurlPPHelper.h"
#include "../../impl/util/CurlPPProgress.h"
#include "../../impl/util/CurlPPSSL.h"
#include "../../impl/auth_server/AuthServerJwtUtils.h"
#include "../../impl/auth_server/AuthServerDbAccess.h"

#include <orchestration_server/OrchestrationServer.h>

// rapidjson
#include <writer.h>
#include <stringbuffer.h>
#include <document.h>

using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::OrchestrationServer;
using namespace CPCAPI2::JsonApi;

class OrchestrationServerTests : public CpcapiAutoTest
{
public:
   OrchestrationServerTests() {}
   virtual ~OrchestrationServerTests() {}
};

TEST_F(OrchestrationServerTests, DISABLED_RequestServicesFirstTime) {
   {
      CPCAPI2::AuthServer::DbAccess authDb;
      ASSERT_EQ(authDb.initialize("authserver.db"), 0);
      authDb.flushUsers();
      authDb.addUser("user1", "1234");
      authDb.addUser("server", "server");
   }

   // Max is the remote SDK
   TestAccount max("max", Account_Init);
   JsonApi::JsonApiServerConfig jsonApiServCfg;
   jsonApiServCfg.websocketPort = 9003;
   jsonApiServCfg.httpPort = 18080;
   jsonApiServCfg.certificateFilePath = TestEnvironmentConfig::testResourcePath() + "p256-public-key-unit-tests.spki"; // pub key used to validate auth tokens
   max.jsonApiServer->start(jsonApiServCfg);
   CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(max.phone);

   CPCAPI2::AuthServer::AuthServerConfig authServerConfig;
   authServerConfig.certificateFilePath = TestEnvironmentConfig::testResourcePath() + "p256-private-key-unit-tests.p8";
   authServerConfig.numThreads = 4;
   authServerConfig.port = 18081;
   max.authServer->start(authServerConfig);

   CPCAPI2::OrchestrationServer::OrchestrationServerConfig serverConfig;
   serverConfig.redisIp = "mock"; // "**************";
   serverConfig.redisPort = 6379;
   max.orchestrationServer->start(serverConfig);

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   auto maxInit = std::async(std::launch::async, [&]() {
      ServerInfo xmppAgentServerInfo;
      xmppAgentServerInfo.region = "NA";
      xmppAgentServerInfo.uri = "ws://127.0.0.1:9003";
      xmppAgentServerInfo.services.push_back("xmppagent");
      max.orchestrationServer->setServerInfo(xmppAgentServerInfo);
   });
   maxInit.wait();

   try
   {
      /* first hit the auth server for a token */
      CurlPPHelper helper;
      curlpp::Easy request;
      std::string messageBody = "{ \"username\": \"user1\", \"password\": \"1234\", \"device_uuid\": \"12345abcde\" }"; // memory needs to hang around until after perform

      std::stringstream url;
      url << "http://127.0.0.1:18081/login_v1";

      helper.setDefaultOptions(request, url.str(), "POST", messageBody.size());
      request.setOpt(new curlpp::options::PostFields(messageBody));

      CurlPPSSL cssl(SslCipherOptions(), CurlPPSSL::E_CERT_WHATEVER_ERROR);
      request.setOpt(new curlpp::options::SslCtxFunction(cssl));

      std::stringstream responseBody;
      request.setOpt(new curlpp::options::WriteStream(&responseBody));
      request.perform();

      ASSERT_EQ(curlpp::infos::ResponseCode::get(request), 200);
      //ASSERT_EQ(messageBody, responseBody.str());

      std::shared_ptr<rapidjson::Document> jsonRequest(new rapidjson::Document);
      jsonRequest->Parse<0>(responseBody.str().c_str());
      ASSERT_FALSE(jsonRequest->HasParseError());
      ASSERT_TRUE(jsonRequest->HasMember("token"));
      const rapidjson::Value& tokenVal = (*jsonRequest)["token"];
      ASSERT_TRUE(tokenVal.IsString());
      ASSERT_TRUE(tokenVal.GetStringLength() > 0);

      resip::Data tokenData(tokenVal.GetString(), tokenVal.GetStringLength());

      auto aliceEvent = std::async(std::launch::async, [&]() {

         try
         {
            /* now hit orchestration server */
            curlpp::Easy orchRequest;
            std::string orchMessageBody = "{"
               "\"moduleId\":\"OrchestrationServer\","
               "\"functionObject\" : {"
               "\"functionName\":\"requestService\","
               "   \"serviceRequests\" : [{"
               "   \"resource\":\"jgeras\","
               "      \"service\" : \"xmppagent\","
               "      \"region\" : \"NA\""
               "}]"
               "}"
               "}";

            std::stringstream jsonApiUrl;
            jsonApiUrl << "http://127.0.0.1:18080/jsonApi";

            helper.setDefaultOptions(orchRequest, jsonApiUrl.str(), "POST", orchMessageBody.size());
            orchRequest.setOpt(new curlpp::options::PostFields(orchMessageBody));

            std::list<std::string> header;
            header.push_back(std::string("Authorization: bearer ") + tokenData.c_str());
            orchRequest.setOpt(new curlpp::options::HttpHeader(header));

            orchRequest.setOpt(new curlpp::options::SslCtxFunction(cssl));

            std::stringstream orchResponseBody;
            orchRequest.setOpt(new curlpp::options::WriteStream(&orchResponseBody));
            orchRequest.perform();

            ASSERT_EQ(curlpp::infos::ResponseCode::get(orchRequest), 200);
         }
         catch (curlpp::RuntimeError& e)
         {
            std::cerr << "Runtime Error: " << e.what();
         }
      });

      auto maxEvent = std::async(std::launch::async, [&]() {
         JsonApiUserHandle jsonApiUser;
         NewLoginEvent args;

         // Max has to process the login attempt (associate the context with an SDK instance)
         cpcExpectEvent(max.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 5000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args);
         cpc::vector<cpc::string> permissions; permissions.push_back("*");
         max.jsonApiServer->setJsonApiUserContext(jsonApiUser, max.phone, permissions);
         LoginResultEvent loginResult;
         loginResult.success = true;
         max.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);
      });

      waitFor2(aliceEvent, maxEvent);


      //ASSERT_EQ(messageBody, responseBody.str());
      
      //std::shared_ptr<rapidjson::Document> jsonRequest(new rapidjson::Document);
      //jsonRequest->Parse<0>(responseBody.str().c_str());
      //ASSERT_FALSE(jsonRequest->HasParseError());
      //ASSERT_TRUE(jsonRequest->HasMember("token"));
      //const rapidjson::Value& tokenVal = (*jsonRequest)["token"];
      //ASSERT_TRUE(tokenVal.IsString());
      //ASSERT_TRUE(tokenVal.GetStringLength() > 0);

   }
   catch (curlpp::RuntimeError& e)
   {
      std::cerr << "Runtime Error: " << e.what();
   }

   max.orchestrationServer->flushAll();

   max.orchestrationServer->shutdown();
   max.authServer->shutdown();
   max.jsonApiServer->shutdown();

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

}

TEST_F(OrchestrationServerTests, DISABLED_SetServerInfo) {
   cpc::string xmppAgentServiceId = CPCAPI2::XmppAgent::XmppAgentManager::getServiceId();
   cpc::string pushNotificationClientServiceId = CPCAPI2::PushEndpoint::PushNotificationEndpointManager::getServiceId();

   {
      CPCAPI2::AuthServer::DbAccess authDb;
      ASSERT_EQ(authDb.initialize("authserver.db"), 0);
      authDb.flushUsers();
      authDb.addUser("user1", "1234");
      authDb.addUser("server", "server");
   }

   // Max is the remote SDK
   TestAccount max("max", Account_Init);
   JsonApi::JsonApiServerConfig jsonApiServCfg;
   jsonApiServCfg.websocketPort = 9003;
   jsonApiServCfg.httpPort = 18080;
   jsonApiServCfg.certificateFilePath = TestEnvironmentConfig::testResourcePath() + "p256-public-key-unit-tests.spki"; // pub key used to validate auth tokens
   max.jsonApiServer->start(jsonApiServCfg);
   CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(max.phone);

   CPCAPI2::AuthServer::AuthServerConfig authServerConfig;
   authServerConfig.certificateFilePath = TestEnvironmentConfig::testResourcePath() + "p256-private-key-unit-tests.p8";
   authServerConfig.numThreads = 4;
   authServerConfig.port = 18081;
   max.authServer->start(authServerConfig);

   CPCAPI2::OrchestrationServer::OrchestrationServerConfig serverConfig;
   serverConfig.redisIp = "mock"; // "**************";
   serverConfig.redisPort = 6379;
   max.orchestrationServer->start(serverConfig);

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   try
   {
      auto maxEvent = std::async(std::launch::async, [&]() {
         JsonApiUserHandle jsonApiUser;
         NewLoginEvent args;

         // Max has to process the login attempt (associate the context with an SDK instance)
         cpcExpectEvent(max.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 5000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args);
         cpc::vector<cpc::string> permissions; permissions.push_back("*");
         max.jsonApiServer->setJsonApiUserContext(jsonApiUser, max.phone, permissions);
         LoginResultEvent loginResult;
         loginResult.success = true;
         max.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);
      });

      /* first hit the auth server for a token */
      CurlPPHelper helper;
      curlpp::Easy request;
      std::string messageBody = "{ \"username\": \"server\", \"password\": \"server\", \"device_uuid\": \"12345abcde\" }"; // memory needs to hang around until after perform

      std::stringstream url;
      url << "http://127.0.0.1:18081/login_v1";

      helper.setDefaultOptions(request, url.str(), "POST", messageBody.size());
      request.setOpt(new curlpp::options::PostFields(messageBody));

      CurlPPSSL cssl(SslCipherOptions(), CurlPPSSL::E_CERT_WHATEVER_ERROR);
      request.setOpt(new curlpp::options::SslCtxFunction(cssl));

      std::stringstream responseBody;
      request.setOpt(new curlpp::options::WriteStream(&responseBody));
      request.perform();

      ASSERT_EQ(curlpp::infos::ResponseCode::get(request), 200);
      //ASSERT_EQ(messageBody, responseBody.str());

      std::shared_ptr<rapidjson::Document> jsonRequest(new rapidjson::Document);
      jsonRequest->Parse<0>(responseBody.str().c_str());
      ASSERT_FALSE(jsonRequest->HasParseError());
      ASSERT_TRUE(jsonRequest->HasMember("token"));
      const rapidjson::Value& tokenVal = (*jsonRequest)["token"];
      ASSERT_TRUE(tokenVal.IsString());
      ASSERT_TRUE(tokenVal.GetStringLength() > 0);

      resip::Data tokenData(tokenVal.GetString(), tokenVal.GetStringLength());

      auto aliceEvent = std::async(std::launch::async, [&]() {

         try
         {
            /* now hit orchestration server */
            curlpp::Easy orchRequest;
            std::stringstream  orchMessageBody;
            orchMessageBody << "{"
               << "\"moduleId\":\"OrchestrationServer\","
               << "\"functionObject\" : {"
               << "\"functionName\":\"setServerInfo\","
               << "   \"serverInfo\" : {"
               << "      \"region\" : \"NA\","
               << "      \"uri\" : \"ws://127.0.0.1:9003\","
               << "      \"services\" : [\"" << xmppAgentServiceId << "\", \"" << pushNotificationClientServiceId << "\"]"
            << "}}}";

            std::stringstream jsonApiUrl;
            jsonApiUrl << "http://127.0.0.1:18080/jsonApi";

            helper.setDefaultOptions(orchRequest, jsonApiUrl.str(), "POST", orchMessageBody.str().size());
            orchRequest.setOpt(new curlpp::options::PostFields(orchMessageBody.str()));

            std::list<std::string> header;
            header.push_back(std::string("Authorization: bearer ") + tokenData.c_str());
            orchRequest.setOpt(new curlpp::options::HttpHeader(header));

            orchRequest.setOpt(new curlpp::options::SslCtxFunction(cssl));

            std::stringstream orchResponseBody;
            orchRequest.setOpt(new curlpp::options::WriteStream(&orchResponseBody));
            orchRequest.perform();

            ASSERT_EQ(curlpp::infos::ResponseCode::get(orchRequest), 200);
         }
         catch (curlpp::RuntimeError& e)
         {
            std::cerr << "Runtime Error: " << e.what();
         }
      });

      waitFor2(aliceEvent, maxEvent);

   }
   catch (curlpp::RuntimeError& e)
   {
      std::cerr << "Runtime Error: " << e.what();
   }

   max.orchestrationServer->flushAll();

   max.orchestrationServer->shutdown();
   max.authServer->shutdown();
   max.jsonApiServer->shutdown();

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

}


#endif
