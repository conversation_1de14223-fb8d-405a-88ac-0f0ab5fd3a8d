#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#include "cpcapi2_test_fixture.h"
#include "test_framework/cpcapi2_test_framework.h"
#include "test_call_events.h"
#include "test_events.h"
#include "test_account_events.h"

#include "impl/call/MosEstimator.h"



using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::SipConversation;


class MosAutoTests : public CpcapiAutoTest
{
public:
   MosAutoTests() {}
   virtual ~MosAutoTests() {}

   unsigned short percent2Fraction(float percent) const;
   unsigned short doCalculateNetworkMos(float localPercentLost, unsigned int totalReceivedPackets,
                                        int64_t rttMs, unsigned int averageJitterMs, unsigned int numIntervalsWithoutUpdate);
};

unsigned short MosAutoTests::doCalculateNetworkMos(float localPercentLost, unsigned int totalReceivedPackets,
   int64_t rttMs, unsigned int averageJitterMs, unsigned int numIntervalsWithoutUpdate)
{
   unsigned short mos = MosEstimator::calculateNetworkMos(localPercentLost / 100.0 * 255.0, totalReceivedPackets, rttMs, 
                                                          averageJitterMs, numIntervalsWithoutUpdate);

   safeCout(">>>>>>>>>>>>>>> doCalculateNetworkMos -- localPercentLost: " << localPercentLost << ", totalReceivedPackets: "
      << totalReceivedPackets << ", averageJitterMs: " << averageJitterMs << ", numIntervalsWithoutUpdate: "
      << numIntervalsWithoutUpdate << " network MOS estimate: " << mos);

   return mos;
}

unsigned short MosAutoTests::percent2Fraction(float percent) const
{
   return percent / 100.0 * 255.0;
}

TEST_F(MosAutoTests, test) 
{
   doCalculateNetworkMos(0, 1000, 5, 5, 0);
   doCalculateNetworkMos(1, 1000, 5, 5, 0);
   doCalculateNetworkMos(2, 1000, 5, 5, 0);
   doCalculateNetworkMos(4, 1000, 5, 5, 0);
   doCalculateNetworkMos(6, 1000, 5, 5, 0);
   doCalculateNetworkMos(10, 1000, 5, 5, 0);
}
