#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#include "tscf_test_fixture.h"
#include "test_account_events.h"
#include <utils/msrp_strcasestr.h>
#include "impl/phone/PhoneInterface.h"

using namespace CPCAPI2;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::SipConversation;
using namespace CPCAPI2::SipPresence;
using namespace CPCAPI2::SipEvent;
#if (CPCAPI2_BRAND_SIP_FILE_TRANSFER_MODULE == 1)
using namespace CPCAPI2::SipFileTransfer;
#endif
using namespace CPCAPI2::SipInstantMessage;
using namespace CPCAPI2::SipMessageWaitingIndication;
#if (CPCAPI2_BRAND_SIP_CHAT_MODULE == 1)
using namespace CPCAPI2::SipChat;
#endif
#if (CPCAPI2_BRAND_SIP_STANDALONE_MESSAGING_MODULE == 1)
using namespace CPCAPI2::SipStandaloneMessaging;
#endif
using namespace CPCAPI2::RcsCapabilityDiscovery;
using namespace CPCAPI2::RcsProvision;
using namespace CPCAPI2::Media;
using namespace CPCAPI2::PeerConnection;
#if (CPCAPI2_BRAND_WEB_CALL_MODULE == 1)
using namespace CPCAPI2::WebCall;
#endif
#ifdef CPCAPI2_GENBAND_MODULE
using namespace CPCAPI2::Genband;
#endif
#if (CPCAPI2_BRAND_GENBAND_SOPI_MODULE == 1)
using namespace CPCAPI2::GenbandSopi;
#endif
#if (CPCAPI2_BRAND_XCAP_MODULE == 1)
using namespace CPCAPI2::XCAP;
#endif
#if (CPCAPI2_BRAND_WATCHER_INFO_MODULE == 1)
using namespace CPCAPI2::WatcherInfo;
#endif
#if (CPCAPI2_BRAND_CONFERENCE_MODULE == 1)
using namespace CPCAPI2::SipConference;
#endif
#if (CPCAPI2_BRAND_REMOTE_SYNC_MODULE == 1)
using namespace CPCAPI2::RemoteSync;
#endif

using namespace CPCAPI2::test;

using namespace ::testing;

TscfAccountConfig::TscfAccountConfig(const std::string& n, const std::string& p)
   : TestAccountConfig(n)
{
   name = (n.c_str());
   password = (p.c_str());
   settings.username = name;
   settings.password = password;
   settings.domain = "************";
   settings.outboundProxy = "";
   settings.tunnelConfig.useTunnel = true;
   settings.tunnelConfig.tunnelType = TunnelType_TSCF;
   settings.tunnelConfig.server = "**************";
   settings.tunnelConfig.transportType = TunnelTransport_UDP;
}

TscfAccountConfig 
TscfAccountConfig::makeSecureConfig(const std::string& name, const std::string& password)
{
   TscfAccountConfig config(name, password);
   config.settings.domain = "autotest.cpcapi2";
   config.settings.outboundProxy = "";
   config.settings.nameServers.push_back(defaultDnsServer());
   config.settings.sipTransportType = SipAccountTransport_TLS; 
   config.settings.ignoreCertVerification = true;
   return config;
}

TscfAccount::TscfAccount(const std::string& name, const std::string& password, TestAccountInitMode initMode, bool disableOnDestruct, CPCAPI2::Phone* p)
   : TestAccount(name, Account_NoInit, disableOnDestruct, p)
{
   config = TscfAccountConfig(name, password);
   switch(initMode)
   {
      case Account_Enable:
         enable();
         break;
      case Account_Init:
         init();
         break;
      case Account_NoInit:
         break;
   }
}

void TscfAccount::enable(bool assertRegistrationState)
{
   init();
   ASSERT_EQ(account->enable(handle), kSuccess);
   if (assertRegistrationState)
   {
      // SipAccountImpl currently fires Status_Registering TWICE for TSCF accounts (r125313).
      // Not ideal, but for now, swallow the extra registering event
      assertAccountRegisteringEx(*this);
      assertAccountRegisteringEx(*this);
      assertAccountRegisteredEx(*this);
   }
   enabled = true;
}
