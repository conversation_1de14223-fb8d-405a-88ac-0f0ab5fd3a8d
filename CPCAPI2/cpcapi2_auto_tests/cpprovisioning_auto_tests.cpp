#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#if (CPCAPI2_BRAND_STRETTO_PROVISIONING_MODULE == 1)

#include "cpcapi2_test_fixture.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>
#include <strettoprovisioning/StrettoProvisioningInternalTypes.h>

#include "test_framework/cpcapi2_test_framework.h"
#include "test_framework/http_test_framework.h"

#include <cpcstl/string.h>
#include <string>
#include <thread>
#include <future>
#include <memory>

using namespace CPCAPI2;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::test;
using namespace CPCAPI2::StrettoProvisioning;

#ifdef _WIN32
#include <Windows.h>
#else
#include <sys/time.h>
#endif

namespace {

   class CPProvisioningModuleTest : public CpcapiAutoTest
   {
   public:
      CPProvisioningModuleTest() : mHttpServerListenPort(9090) {
         mHttpServer.config.port = mHttpServerListenPort;
      }
      virtual ~CPProvisioningModuleTest() {}
      
      const int mHttpServerListenPort;
      SimpleWeb::Server<SimpleWeb::HTTP> mHttpServer;
   };

   std::string resourcePathRegex(const std::string& resourcePath)
   {
      std::stringstream ss;
      ss << resourcePath << ".*";
      return ss.str();
   }

   TEST_F(CPProvisioningModuleTest, Provisioning) {

      const std::string resourcePath("/login");
   
      // listens for requests at /test
      mHttpServer.resource[resourcePathRegex(resourcePath)]["GET"]=[](std::shared_ptr<HttpServer::Response> response,
                                             std::shared_ptr<HttpServer::Request> request)
      {
         std::ifstream in((TestEnvironmentConfig::testResourcePath() + "provisioningtests2.json").c_str());
         assert(in.is_open());

         std::ostringstream iss;
         iss << in.rdbuf() << std::flush;

         cpc::string doc = iss.str().c_str();
         
         *response << "HTTP/1.1 200 OK\r\nContent-Length: " << doc.size() << "\r\n\r\n" << doc;
      };
    
      thread server_thread([this]()
      {
         mHttpServer.start();
      });
      
      std::this_thread::sleep_for(std::chrono::seconds(3));
      

      TestAccount alice("alice", Account_Init);

      // Set the settings
      StrettoProvisioningInternalSettings settings;
      settings.provisioningUrl             = "http://127.0.0.1:9090/login";
      settings.authInfo.spid               = "group1.1";
      settings.authInfo.userName           = "user0000@group1.1";
      settings.authInfo.password           = "user0000@group1.1:1234";
      settings.buildInfo.versionString     = "3.1.4.15926";
      settings.logJSONPayload              = true;                      // not used yet

      alice.provisioningManager->configureSettings( alice.provisioningHandle, settings );
      alice.provisioningManager->applySettings( alice.provisioningHandle ); // prob. not needed

      // Fetch the config
      alice.provisioningManager->request( alice.provisioningHandle );

      auto aliceProvisioned = std::async(std::launch::async, [&] () {
         StrettoProvisioningEvent evt;
         StrettoProvisioningHandle h;
         ASSERT_TRUE( cpcWaitForEvent(
            alice.provisioningEvents,
            "StrettoProvisioningHandler::onProvisioningSuccess",
            15000,
            AlwaysTruePred( ),
            h, evt ) );
         ASSERT_TRUE( evt.document.size() > 0 );
         
         cpc::vector<SipAccountSettings> settings;
         ASSERT_EQ(kSuccess, alice.account->decodeProvisioningResponse(evt.document, settings));
         ASSERT_EQ(1, settings.size());
         
         alice.account->configureDefaultAccountSettings(alice.handle, settings[0]);
         alice.account->applySettings(alice.handle);
         
         alice.enable();
         
      });
      waitFor( aliceProvisioned );
      
      mHttpServer.stop();
      server_thread.join();
   }

}  // namespace

#endif // (CPCAPI2_BRAND_REMOTE_SYNC_MODULE == 1)

