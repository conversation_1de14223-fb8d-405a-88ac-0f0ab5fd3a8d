#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#include "test_framework/xmpp_test_helper.h"
#include "xmpp/XmppAccountJsonApi.h"
#include "xmpp/XmppRosterJsonApi.h"

#include "../../impl/auth_server/AuthServerJwtUtils.h"
#include "../../impl/xmpp/CpcXepUserActivity.h"

#ifndef ANDROID
//define USE_CPCAPI2_JSON_TESTS 1
#endif

#if (CPCAPI2_BRAND_XMPP_ROSTER_MODULE == 1)

#if defined(__GNUC__) || defined(__clang__)
#pragma GCC diagnostic ignored "-Wdeprecated-declarations"
#endif

using namespace CPCAPI2;
using namespace CPCAPI2::XmppAccount;
using namespace CPCAPI2::XmppRoster;
using namespace CPCAPI2::<PERSON>son<PERSON><PERSON>;

namespace {

class XmppRosterModuleTest : public CpcapiAutoTest
{
public:
   XmppRosterModuleTest() {}
   virtual ~XmppRosterModuleTest() {}
   static void SetUpTestCase() {}
   static void TearDownTestCase() {}
   virtual void TearDown() {}

   virtual void SetUp()
   {
      if (!TestEnvironmentConfig::autoCreateXmppUsers())
      {
         auto initialization = [&](XmppTestAccount& account)
         {
            assertXmppRosterUpdate(account, [&](const XmppRosterUpdateEvent& evt)
            {
               ASSERT_TRUE(evt.fullUpdate);

               for (const auto& item : evt.added)
               {
                  account.roster->removeRosterItem(account.rosterHandle, item.item.address);
               }
            });

            while (true)
            {
               XmppRosterHandle h;
               XmppRosterSubscriptionRequestEvent evt;
               if (!account.events->expectEvent(__LINE__, "XmppRosterHandler::onSubscriptionRequest", 1000, CPCAPI2::test::AlwaysTruePred(), h, evt)) break;
               
               account.roster->rejectSubscriptionRequest(account.rosterHandle, evt.address);
            };
            safeCout("initialization exiting: user: " << account.config.settings.username);
         };

         // clear out rosters for test accounts
         const char* xmppUsersString = getenv("CPCAPI2_XMPP_USERS");
         if (xmppUsersString == NULL)
         {
            xmppUsersString = DefaultXmppUserList;
         }
         
         static std::vector<XmppTestUserInfo> xmppUsers;
         xmppUsers = split<XmppTestUserInfo>(xmppUsersString, ',');
         
         for (std::vector<XmppTestUserInfo>::iterator i = xmppUsers.begin(); i != xmppUsers.end(); i++)
         {
            std::this_thread::sleep_for(std::chrono::milliseconds(1000));
            
            safeCout("initialization start: user=" << (*i).username);
            XmppTestAccount user((*i).username, Account_Enable, (*i).username);
            auto userEvent = std::async(std::launch::async, [&]()
            {
               initialization(user);
            });

            waitFor(userEvent);
            safeCout("initialization end: user=" << (*i).username);
            std::this_thread::sleep_for(std::chrono::milliseconds(1000));
         }
      }
   }
};

TEST_F(XmppRosterModuleTest, XmppBasicRoster)
{
   XmppTestAccount alice("alice");

   assertXmppRosterUpdate(alice, [](const XmppRosterUpdateEvent& evt)
   {
      ASSERT_TRUE(evt.fullUpdate);
      ASSERT_EQ(evt.added.size(), 0);
      ASSERT_EQ(evt.updated.size(), 0);
      ASSERT_EQ(evt.removed.size(), 0);
   });
   
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
}

#if USE_CPCAPI2_JSON_TESTS
TEST_F(XmppRosterModuleTest, XmppBasicRoster_JSON)
{
   XmppTestCloudAccount alice("alice");
   alice.enable();

   assertXmppRosterUpdate(alice, [](const XmppRosterUpdateEvent& evt)
   {
      ASSERT_TRUE(evt.fullUpdate);
      ASSERT_EQ(evt.added.size(), 0);
      ASSERT_EQ(evt.updated.size(), 0);
      ASSERT_EQ(evt.removed.size(), 0);
   });
   
   std::this_thread::sleep_for(std::chrono::seconds(2));
   
   alice.disable();
   
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
}
#endif

void xmppRosterAdd(XmppTestAccount& alice, XmppTestAccount& bob, bool isCloud)
{
   CPCAPI2::XmppRoster::XmppRosterManager* roster = alice.roster;
   if (isCloud)
      roster = alice.rosterJson;
   
   assertXmppRosterUpdate(alice, [](const XmppRosterUpdateEvent& evt)
   {
      ASSERT_TRUE(evt.fullUpdate);
      ASSERT_EQ(evt.added.size(), 0);
      ASSERT_EQ(evt.updated.size(), 0);
      ASSERT_EQ(evt.removed.size(), 0);
   });

   roster->addRosterItem(alice.rosterHandle, bob.config.bare(), "bob");

   // bliu: this is added to address the lack of roster push on Openfire 4+
   roster->subscribePresence(alice.rosterHandle, bob.config.bare(), "bob");

   assertXmppRosterUpdate(alice, [](const XmppRosterUpdateEvent& evt)
   {
      ASSERT_FALSE(evt.fullUpdate);
      ASSERT_EQ(evt.added.size(), 1);
      ASSERT_EQ(evt.updated.size(), 0);
      ASSERT_EQ(evt.removed.size(), 0);
   });

   { // bliu: this is added to address the extra onRosterUpdate caused by subscribePresence() on older Openfire
      XmppRosterHandle h;
      XmppRosterUpdateEvent evt;
      if (alice.events->expectEvent(__LINE__, "XmppRosterHandler::onRosterUpdate", 3000, CPCAPI2::test::AlwaysTruePred(), h, evt))
      {
         ASSERT_FALSE(evt.fullUpdate);
         ASSERT_EQ(evt.added.size(), 0);
         ASSERT_EQ(evt.updated.size(), 1);
         ASSERT_EQ(evt.removed.size(), 0);
      }
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.enable();
   
   assertXmppRosterUpdate(alice, [](const XmppRosterUpdateEvent& evt)
   {
      ASSERT_TRUE(evt.fullUpdate);
      ASSERT_EQ(evt.added.size(), 1);
      ASSERT_EQ(evt.updated.size(), 0);
      ASSERT_EQ(evt.removed.size(), 0);
   });

   roster->removeRosterItem(alice.rosterHandle, bob.config.bare());
   assertXmppRosterUpdate(alice, [](const XmppRosterUpdateEvent& evt)
   {
      ASSERT_FALSE(evt.fullUpdate);
      ASSERT_EQ(evt.added.size(), 0);
      ASSERT_EQ(evt.updated.size(), 0);
      ASSERT_EQ(evt.removed.size(), 1);
   });
}

TEST_F(XmppRosterModuleTest, XmppRosterAdd)
{
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob", Account_NoInit);
   xmppRosterAdd(alice, bob, false);
   
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
}
   
#if USE_CPCAPI2_JSON_TESTS
TEST_F(XmppRosterModuleTest, XmppRosterAdd_JSON)
{
   XmppTestCloudAccount alice("alice");
   XmppTestAccount bob("bob", Account_NoInit);
   alice.enable();
      
   xmppRosterAdd(alice, bob, true);
      
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
}
#endif // USE_CPCAPI2_JSON_TESTS
  
void xmppRosterAddAccept(XmppTestAccount& alice, XmppTestAccount& bob, bool aliceIsCloud, bool bobIsCloud)
{
   CPCAPI2::XmppRoster::XmppRosterManager* aliceRoster = alice.roster;
   CPCAPI2::XmppRoster::XmppRosterManager* bobRoster = bob.roster;
   if (aliceIsCloud)
   {
      aliceRoster = alice.rosterJson;
   }
   if (bobIsCloud)
   {
      bobRoster = bob.rosterJson;
   }

   auto aliceRosterEvent = std::async(std::launch::async, [&] ()
   {
      assertXmppRosterUpdate(alice, [](const XmppRosterUpdateEvent& evt)
      {
         ASSERT_TRUE(evt.fullUpdate);
         ASSERT_EQ(evt.added.size(), 0);
         ASSERT_EQ(evt.updated.size(), 0);
         ASSERT_EQ(evt.removed.size(), 0);
      });
   });

   auto bobRosterEvent = std::async(std::launch::async, [&] ()
   {
      assertXmppRosterUpdate(bob, [](const XmppRosterUpdateEvent& evt)
      {
         ASSERT_TRUE(evt.fullUpdate);
         ASSERT_EQ(evt.added.size(), 0);
         ASSERT_EQ(evt.updated.size(), 0);
         ASSERT_EQ(evt.removed.size(), 0);
      });
   });

   waitFor2(aliceRosterEvent, bobRosterEvent);

   auto aliceAddEvent = std::async(std::launch::async, [&] ()
   {
      aliceRoster->addRosterItem(alice.rosterHandle, bob.config.bare(), "bob");
      assertXmppRosterUpdate(alice, [](const XmppRosterUpdateEvent& evt)
      {
         ASSERT_FALSE(evt.fullUpdate);
         ASSERT_EQ(evt.added.size(), 1);
         ASSERT_EQ(evt.updated.size(), 0);
         ASSERT_EQ(evt.removed.size(), 0);
      });

      aliceRoster->subscribePresence(alice.rosterHandle, bob.config.bare(), "bob");
      assertXmppRosterUpdate(alice, [](const XmppRosterUpdateEvent& evt)
      {
         ASSERT_FALSE(evt.fullUpdate);
         ASSERT_EQ(evt.added.size(), 0);
         ASSERT_EQ(evt.updated.size(), 1);
         ASSERT_EQ(evt.removed.size(), 0);
         ASSERT_EQ(evt.updated[0].item.subscription, XmppRoster::SubscriptionState_None_OutPending);
         ASSERT_TRUE(evt.updated[0].item.resources.empty());
      });
      assertXmppRosterUpdate(alice, [](const XmppRosterUpdateEvent& evt)
      {
         ASSERT_FALSE(evt.fullUpdate);
         ASSERT_EQ(evt.added.size(), 0);
         ASSERT_EQ(evt.updated.size(), 1);
         ASSERT_EQ(evt.removed.size(), 0);
         ASSERT_EQ(evt.updated[0].item.subscription, XmppRoster::SubscriptionState_Out);
         ASSERT_TRUE(evt.updated[0].item.resources.empty());
      });
      assertXmppRosterPresence(alice, [&](const XmppRosterPresenceEvent& evt)
      {
         ASSERT_EQ(evt.rosterItem.address, bob.config.bare());
         ASSERT_EQ(evt.rosterItem.subscription, XmppRoster::SubscriptionState_Out);
         ASSERT_EQ(evt.rosterItem.resources.size(), 1);
         ASSERT_EQ(evt.rosterItem.resources[0].presenceType, XmppRoster::PresenceType_Available);
      });
   });

   auto bobAcceptEvent = std::async(std::launch::async, [&] ()
   {
      assertXmppSubscriptionRequest(bob, [&](const XmppRosterSubscriptionRequestEvent& evt)
      {
         ASSERT_EQ(evt.address, alice.config.bare());
      });
      bobRoster->acceptSubscriptionRequest(bob.rosterHandle, alice.config.bare());
      assertXmppRosterUpdate(bob, [](const XmppRosterUpdateEvent& evt)
      {
         ASSERT_FALSE(evt.fullUpdate);
         ASSERT_EQ(evt.added.size(), 1);
         ASSERT_EQ(evt.updated.size(), 0);
         ASSERT_EQ(evt.removed.size(), 0);
         ASSERT_EQ(evt.added[0].item.subscription, XmppRoster::SubscriptionState_In);
      });
   });

   waitFor2(aliceAddEvent, bobAcceptEvent);
}

TEST_F(XmppRosterModuleTest, DISABLED_XmppRosterAddAccept)
{
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");
   xmppRosterAddAccept(alice, bob, false, false);
   
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   bob.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
}

#if USE_CPCAPI2_JSON_TESTS
TEST_F(XmppRosterModuleTest, XmppRosterAddAccept_JSON)
{
   XmppTestAccount alice("alice");
   XmppTestCloudAccount bob("bob");
   bob.enable();
      
   xmppRosterAddAccept(alice, bob, false, true);
      
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   bob.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
}
#endif // USE_CPCAPI2_JSON_TESTS
 
void xmppRosterAddReject(XmppTestAccount& alice, XmppTestAccount& bob, bool aliceIsCloud, bool bobIsCloud)
{
   CPCAPI2::XmppRoster::XmppRosterManager* aliceRoster = alice.roster;
   CPCAPI2::XmppRoster::XmppRosterManager* bobRoster = bob.roster;
   if (aliceIsCloud)
   {
      aliceRoster = alice.rosterJson;
   }
   if (bobIsCloud)
   {
      bobRoster = bob.rosterJson;
   }

   auto aliceRosterEvent = std::async(std::launch::async, [&] ()
   {
      assertXmppRosterUpdate(alice, [](const XmppRosterUpdateEvent& evt)
      {
         ASSERT_TRUE(evt.fullUpdate);
         ASSERT_EQ(evt.added.size(), 0);
         ASSERT_EQ(evt.updated.size(), 0);
         ASSERT_EQ(evt.removed.size(), 0);
      });
   });

   auto bobRosterEvent = std::async(std::launch::async, [&] ()
   {
      assertXmppRosterUpdate(bob, [](const XmppRosterUpdateEvent& evt)
      {
         ASSERT_TRUE(evt.fullUpdate);
         ASSERT_EQ(evt.added.size(), 0);
         ASSERT_EQ(evt.updated.size(), 0);
         ASSERT_EQ(evt.removed.size(), 0);
      });
   });

   waitFor2(aliceRosterEvent, bobRosterEvent);

   auto aliceAddEvent = std::async(std::launch::async, [&] ()
   {
      aliceRoster->addRosterItem(alice.rosterHandle, bob.config.bare(), "bob");
      assertXmppRosterUpdate(alice, [](const XmppRosterUpdateEvent& evt){
         ASSERT_FALSE(evt.fullUpdate);
         ASSERT_EQ(evt.added.size(), 1);
         ASSERT_EQ(evt.updated.size(), 0);
         ASSERT_EQ(evt.removed.size(), 0);
      });

      aliceRoster->subscribePresence(alice.rosterHandle, bob.config.bare(), "bob");
      assertXmppRosterUpdate(alice, [](const XmppRosterUpdateEvent& evt)
      {
         ASSERT_FALSE(evt.fullUpdate);
         ASSERT_EQ(evt.added.size(), 0);
         ASSERT_EQ(evt.updated.size(), 1);
         ASSERT_EQ(evt.removed.size(), 0);
         ASSERT_EQ(evt.updated[0].item.subscription, XmppRoster::SubscriptionState_None_OutPending);
      });
      assertXmppRosterUpdate(alice, [](const XmppRosterUpdateEvent& evt)
      {
         ASSERT_FALSE(evt.fullUpdate);
         ASSERT_EQ(evt.added.size(), 0);
         ASSERT_EQ(evt.updated.size(), 1);
         ASSERT_EQ(evt.removed.size(), 0);
         // bliu: Openfire 4+ doesn't respond with updated roster after subscription rejection hence less-and-equal check is used instead (this issue can be reproduced with Pidgin as well)
         ASSERT_LE(evt.updated[0].item.subscription, XmppRoster::SubscriptionState_None_OutPending);
      });
      assertXmppRosterPresence(alice, [&](const XmppRosterPresenceEvent& evt)
      {
         ASSERT_EQ(evt.rosterItem.address, bob.config.bare());
         // bliu: Openfire 4+ doesn't respond with updated roster after subscription rejection hence less-and-equal check is used instead (this issue can be reproduced with Pidgin as well)
         ASSERT_LE(evt.rosterItem.subscription, XmppRoster::SubscriptionState_None_OutPending);
      });
   });

   auto bobRejectEvent = std::async(std::launch::async, [&] ()
   {
      assertXmppSubscriptionRequest(bob, [&](const XmppRosterSubscriptionRequestEvent& evt)
      {
         ASSERT_EQ(evt.address, alice.config.bare());
      });
      bobRoster->rejectSubscriptionRequest(bob.rosterHandle, alice.config.bare());
   });

   waitFor2(aliceAddEvent, bobRejectEvent);
}

TEST_F(XmppRosterModuleTest, XmppRosterAddReject)
{
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");
   xmppRosterAddReject(alice, bob, false, false);
   
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   bob.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
}
 
#if USE_CPCAPI2_JSON_TESTS
TEST_F(XmppRosterModuleTest, XmppRosterAddReject_JSON)
{
   XmppTestAccount alice("alice");
   XmppTestCloudAccount bob("bob");
   bob.enable();
      
   xmppRosterAddReject(alice, bob, false, true);
      
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   bob.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
}
#endif // USE_CPCAPI2_JSON_TESTS

void xmppRosterDoNotDisturb(XmppTestAccount& alice, XmppTestAccount& bob, bool aliceIsCloud, bool bobIsCloud)
{
   CPCAPI2::XmppRoster::XmppRosterManager* aliceRoster = alice.roster;
   CPCAPI2::XmppRoster::XmppRosterManager* bobRoster = bob.roster;
   CPCAPI2::XmppAccount::XmppAccountManager* bobAccount = bob.account;
   if (aliceIsCloud)
   {
      aliceRoster = alice.rosterJson;
   }
   if (bobIsCloud)
   {
      bobRoster = bob.rosterJson;
      bobAccount = bob.accountJson;
   }

   auto aliceRosterEvent = std::async(std::launch::async, [&] ()
   {
      assertXmppRosterUpdate(alice, [](const XmppRosterUpdateEvent& evt)
      {
         ASSERT_TRUE(evt.fullUpdate);
         ASSERT_EQ(evt.added.size(), 0);
         ASSERT_EQ(evt.updated.size(), 0);
         ASSERT_EQ(evt.removed.size(), 0);
      });
   });

   auto bobRosterEvent = std::async(std::launch::async, [&] ()
   {
      assertXmppRosterUpdate(bob, [](const XmppRosterUpdateEvent& evt)
      {
         ASSERT_TRUE(evt.fullUpdate);
         ASSERT_EQ(evt.added.size(), 0);
         ASSERT_EQ(evt.updated.size(), 0);
         ASSERT_EQ(evt.removed.size(), 0);
      });
   });

   waitFor2(aliceRosterEvent, bobRosterEvent);

   auto aliceAddEvent = std::async(std::launch::async, [&] ()
   {
      aliceRoster->addRosterItem(alice.rosterHandle, bob.config.bare(), "bob");
      assertXmppRosterUpdate(alice, [&](const XmppRosterUpdateEvent& evt)
      {
         ASSERT_FALSE(evt.fullUpdate);
         ASSERT_EQ(evt.added.size(), 1);
         ASSERT_EQ(evt.updated.size(), 0);
         ASSERT_EQ(evt.removed.size(), 0);
      });

      aliceRoster->subscribePresence(alice.rosterHandle, bob.config.bare(), "bob");
      assertXmppRosterPresence(alice, [&](const XmppRosterPresenceEvent& evt)
      {
         ASSERT_EQ(evt.rosterItem.address, bob.config.bare());
         ASSERT_EQ(evt.rosterItem.subscription, XmppRoster::SubscriptionState_Out);
         ASSERT_EQ(evt.rosterItem.resources.size(), 1);
         ASSERT_EQ(evt.rosterItem.resources[0].presenceType, XmppRoster::PresenceType_Available);
      });
   });

   auto bobAcceptEvent = std::async(std::launch::async, [&] ()
   {
      assertXmppSubscriptionRequest(bob, [&](const XmppRosterSubscriptionRequestEvent& evt)
      {
         ASSERT_EQ(evt.address, alice.config.bare());
      });
      bobRoster->acceptSubscriptionRequest(bob.rosterHandle, alice.config.bare());
   });

   waitFor2(aliceAddEvent, bobAcceptEvent);

   bobAccount->publishPresence(bob.handle, XmppRoster::PresenceType_DND, "In a meeting");
   assertXmppRosterPresence(alice, [&](const XmppRosterPresenceEvent& evt)
   {
      ASSERT_EQ(evt.rosterItem.address, bob.config.bare());
      ASSERT_EQ(evt.rosterItem.subscription, XmppRoster::SubscriptionState_Out);
      ASSERT_EQ(evt.rosterItem.resources.size(), 1);
      ASSERT_EQ(evt.rosterItem.resources[0].presenceType, XmppRoster::PresenceType_DND);
      ASSERT_EQ(evt.rosterItem.resources[0].presenceStatusText, "In a meeting");
   });
}

TEST_F(XmppRosterModuleTest, DISABLED_XmppRosterDoNotDisturb)
{
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");
   xmppRosterDoNotDisturb(alice, bob, false, false);
}

TEST_F(XmppRosterModuleTest, DISABLED_XmppRosterDoNotDisturb_JSON)
{
   XmppTestAccount alice("alice");
   XmppTestCloudAccount bob("bob");
   bob.enable();
      
   xmppRosterDoNotDisturb(alice, bob, false, true);
      
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   bob.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
}
   
TEST_F(XmppRosterModuleTest, DISABLED_UserActivity)
{
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   auto aliceRosterEvent = std::async(std::launch::async, [&] ()
   {
      assertXmppRosterUpdate(alice, [](const XmppRosterUpdateEvent& evt)
      {
         ASSERT_TRUE(evt.fullUpdate);
         ASSERT_EQ(evt.added.size(), 0);
         ASSERT_EQ(evt.updated.size(), 0);
         ASSERT_EQ(evt.removed.size(), 0);
      });
   });

   auto bobRosterEvent = std::async(std::launch::async, [&] ()
   {
      assertXmppRosterUpdate(bob, [](const XmppRosterUpdateEvent& evt)
      {
         ASSERT_TRUE(evt.fullUpdate);
         ASSERT_EQ(evt.added.size(), 0);
         ASSERT_EQ(evt.updated.size(), 0);
         ASSERT_EQ(evt.removed.size(), 0);
      });
   });

   waitFor2(aliceRosterEvent, bobRosterEvent);

   auto aliceAddEvent = std::async(std::launch::async, [&] ()
   {
      alice.roster->addRosterItem(alice.rosterHandle, bob.config.bare(), "bob");
      assertXmppRosterUpdate(alice, [&](const XmppRosterUpdateEvent& evt)
      {
         ASSERT_FALSE(evt.fullUpdate);
         ASSERT_EQ(evt.added.size(), 1);
         ASSERT_EQ(evt.updated.size(), 0);
         ASSERT_EQ(evt.removed.size(), 0);
      });

      alice.roster->subscribePresence(alice.rosterHandle, bob.config.bare(), "bob");
      assertXmppRosterPresence(alice, [&](const XmppRosterPresenceEvent& evt)
      {
         ASSERT_EQ(evt.rosterItem.address, bob.config.bare());
         ASSERT_EQ(evt.rosterItem.subscription, XmppRoster::SubscriptionState_Out);
         ASSERT_EQ(evt.rosterItem.resources.size(), 1);
         ASSERT_EQ(evt.rosterItem.resources[0].presenceType, XmppRoster::PresenceType_Available);
      });
   });

   auto bobAcceptEvent = std::async(std::launch::async, [&] ()
   {
      assertXmppSubscriptionRequest(bob, [&](const XmppRosterSubscriptionRequestEvent& evt)
      {
         ASSERT_EQ(evt.address, alice.config.bare());
      });
      bob.roster->acceptSubscriptionRequest(bob.rosterHandle, alice.config.bare());
   });

   waitFor2(aliceAddEvent, bobAcceptEvent);

   bob.account->publishPresence(bob.handle, XmppRoster::PresenceType_DND, "In a meeting", XmppRoster::ActivityTalking, XmppRoster::ActivityOnThePhone, "activity");
   assertXmppRosterPresence(alice, [&](const XmppRosterPresenceEvent& evt)
   {
      ASSERT_EQ(evt.rosterItem.address, bob.config.bare());
      ASSERT_EQ(evt.rosterItem.subscription, XmppRoster::SubscriptionState_Out);
      ASSERT_EQ(evt.rosterItem.resources.size(), 1);
      ASSERT_EQ(evt.rosterItem.resources[0].presenceType, XmppRoster::PresenceType_DND);
      ASSERT_EQ(evt.rosterItem.resources[0].presenceStatusText, "In a meeting");
      ASSERT_EQ(evt.rosterItem.resources[0].userActivityGeneralType, XmppRoster::ActivityTalking);
      ASSERT_EQ(evt.rosterItem.resources[0].userActivitySpecificType, XmppRoster::ActivityOnThePhone);
      ASSERT_EQ(evt.rosterItem.resources[0].userActivityText, "activity");
   });
}

TEST_F(XmppRosterModuleTest, DISABLED_MultipleUserActivities)
{
   safeCout("Starting MultipleUserActivities test");
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");
   XmppTestAccount bob1(bob, "bob1");

   auto aliceRosterEvent = std::async(std::launch::async, [&] ()
   {
      assertXmppRosterUpdate(alice, [](const XmppRosterUpdateEvent& evt)
      {
         ASSERT_TRUE(evt.fullUpdate);
         ASSERT_EQ(evt.added.size(), 0);
         ASSERT_EQ(evt.updated.size(), 0);
         ASSERT_EQ(evt.removed.size(), 0);
      });
   });

   auto bobRosterEvent = std::async(std::launch::async, [&] ()
   {
      assertXmppRosterUpdate(bob, [](const XmppRosterUpdateEvent& evt)
      {
         ASSERT_TRUE(evt.fullUpdate);
         ASSERT_EQ(evt.added.size(), 0);
         ASSERT_EQ(evt.updated.size(), 0);
         ASSERT_EQ(evt.removed.size(), 0);
      });
   });

   auto bob1RosterEvent = std::async(std::launch::async, [&] ()
   {
      assertXmppRosterUpdate(bob1, [](const XmppRosterUpdateEvent& evt)
      {
         ASSERT_TRUE(evt.fullUpdate);
         ASSERT_EQ(evt.added.size(), 0);
         ASSERT_EQ(evt.updated.size(), 0);
         ASSERT_EQ(evt.removed.size(), 0);
      });
   });

   waitFor3(aliceRosterEvent, bobRosterEvent, bob1RosterEvent);

   auto aliceAddEvent = std::async(std::launch::async, [&] () {
      alice.roster->addRosterItem(alice.rosterHandle, bob.config.bare(), "bob");
      assertXmppRosterUpdate(alice, [&](const XmppRosterUpdateEvent& evt){
         ASSERT_FALSE(evt.fullUpdate);
         ASSERT_EQ(evt.added.size(), 1);
         ASSERT_EQ(evt.updated.size(), 0);
         ASSERT_EQ(evt.removed.size(), 0);
      });

      alice.roster->subscribePresence(alice.rosterHandle, bob.config.bare(), "bob");
      assertXmppRosterPresence(alice, [&](const XmppRosterPresenceEvent& evt)
      {
         ASSERT_EQ(evt.rosterItem.address, bob.config.bare());
         ASSERT_EQ(evt.rosterItem.subscription, XmppRoster::SubscriptionState_Out);
      });
      assertXmppRosterPresence(alice, [&](const XmppRosterPresenceEvent& evt)
      {
         ASSERT_EQ(evt.rosterItem.address, bob.config.bare());
         ASSERT_EQ(evt.rosterItem.resources.size(), 2);
      });
   });

   auto bobAcceptEvent = std::async(std::launch::async, [&] ()
   {
      assertXmppSubscriptionRequest(bob, [&](const XmppRosterSubscriptionRequestEvent& evt)
      {
         ASSERT_EQ(evt.address, alice.config.bare());
      });
      bob.roster->acceptSubscriptionRequest(bob.rosterHandle, alice.config.bare());
      assertXmppRosterUpdate(bob, [&](const XmppRosterUpdateEvent& evt)
      {
         ASSERT_FALSE(evt.fullUpdate);
      });
   });

   auto bob1ConsumeSubscribeEvent = std::async(std::launch::async, [&] ()
   {
      assertXmppSubscriptionRequest(bob1, [&](const XmppRosterSubscriptionRequestEvent& evt)
      {
         ASSERT_EQ(evt.address, alice.config.bare());
      });
      assertXmppRosterUpdate(bob1, [&](const XmppRosterUpdateEvent& evt)
      {
         ASSERT_FALSE(evt.fullUpdate);
      });
   });

   waitFor3(aliceAddEvent, bobAcceptEvent, bob1ConsumeSubscribeEvent);

   static const char *bob1PresenceNote = "In a Meeting Now";
   static const char *bob1ActivityNote = "Custom Activity";
   auto aliceWaitEvent = std::async(std::launch::async, [&] ()
   {
      // DCM: instead of checking for each and every update, just wait/check until we get both
      int awayIndex;
      int onThePhoneIndex;

      do {
         awayIndex=-1;
         onThePhoneIndex=-1;
         
         assertXmppRosterPresence(alice, [&](const XmppRosterPresenceEvent& evt)
         {
            ASSERT_EQ(evt.rosterItem.address, bob.config.bare());

            if (evt.rosterItem.resources.size() == 2)
            {
               if (evt.rosterItem.resources[0].presenceType == XmppRoster::PresenceType_Away)
                  awayIndex = 0;
               else if (evt.rosterItem.resources[0].presenceType == XmppRoster::PresenceType_DND)
                  onThePhoneIndex = 0;
         
               if (evt.rosterItem.resources[1].presenceType == XmppRoster::PresenceType_Away)
                  awayIndex = 1;
               else if (evt.rosterItem.resources[1].presenceType == XmppRoster::PresenceType_DND)
                  onThePhoneIndex = 1;
            }

            if (awayIndex >= 0 && onThePhoneIndex >= 0)
            {
               ASSERT_EQ(evt.rosterItem.resources[awayIndex].presenceType, XmppRoster::PresenceType_Away);
               ASSERT_EQ(evt.rosterItem.resources[awayIndex].resource, bob.config.settings.resource);
               
               ASSERT_EQ(evt.rosterItem.resources[onThePhoneIndex].presenceType, XmppRoster::PresenceType_DND);
               ASSERT_EQ(evt.rosterItem.resources[onThePhoneIndex].presenceStatusText, bob1PresenceNote);
               ASSERT_EQ(evt.rosterItem.resources[onThePhoneIndex].userActivityGeneralType, XmppRoster::ActivityTalking);
               ASSERT_EQ(evt.rosterItem.resources[onThePhoneIndex].userActivitySpecificType, XmppRoster::ActivityOnThePhone);
               ASSERT_EQ(evt.rosterItem.resources[onThePhoneIndex].userActivityText, bob1ActivityNote);
               ASSERT_EQ(evt.rosterItem.resources[onThePhoneIndex].resource, bob1.config.settings.resource);
            }
            else
            {
               safeCout("MultipleUserActivities: waiting for bob and bob1's presence updates");
            }
         });

      } while (awayIndex==-1 || onThePhoneIndex==-1);

      ASSERT_TRUE(awayIndex >= 0);
      ASSERT_TRUE(onThePhoneIndex >= 0);
   });

   bob.account->publishPresence(bob.handle, XmppRoster::PresenceType_Away);
   bob1.account->publishPresence(bob1.handle, XmppRoster::PresenceType_DND, bob1PresenceNote, XmppRoster::ActivityTalking, XmppRoster::ActivityOnThePhone, bob1ActivityNote);

   waitFor(aliceWaitEvent);
}

#if USE_CPCAPI2_JSON_TESTS
static void generateJwt(const resip::Data& p8file, const resip::Data& userIdentity, resip::Data& jwt)
{
   std::map<resip::Data, resip::Data> pubClaims;
   pubClaims["cp_user"] = userIdentity;
   CPCAPI2::AuthServer::JwtUtils::GenerateJWT(p8file, "CPCAPI2::AuthServer", pubClaims, 86400, jwt);
}

TEST_F(XmppRosterModuleTest, MultipleUserActivities_JSON)
{
   safeCout("Starting MultipleUserActivities test");

   XmppTestCloudAccount alice("alice");
   alice.enable();
   
   XmppTestAccount bob("bob");
   XmppTestAccount bob1(bob, "bob1");
   

   auto aliceRosterEvent = std::async(std::launch::async, [&]()
   {
      assertXmppRosterUpdate(alice, [](const XmppRosterUpdateEvent& evt)
      {
         ASSERT_TRUE(evt.fullUpdate);
         ASSERT_EQ(evt.added.size(), 0);
         ASSERT_EQ(evt.updated.size(), 0);
         ASSERT_EQ(evt.removed.size(), 0);
      });
   });

   auto bobRosterEvent = std::async(std::launch::async, [&]()
   {
      assertXmppRosterUpdate(bob, [](const XmppRosterUpdateEvent& evt)
      {
         ASSERT_TRUE(evt.fullUpdate);
         ASSERT_EQ(evt.added.size(), 0);
         ASSERT_EQ(evt.updated.size(), 0);
         ASSERT_EQ(evt.removed.size(), 0);
      });
   });

   auto bob1RosterEvent = std::async(std::launch::async, [&]()
   {
      assertXmppRosterUpdate(bob1, [](const XmppRosterUpdateEvent& evt)
      {
         ASSERT_TRUE(evt.fullUpdate);
         ASSERT_EQ(evt.added.size(), 0);
         ASSERT_EQ(evt.updated.size(), 0);
         ASSERT_EQ(evt.removed.size(), 0);
      });
   });

   waitFor3(aliceRosterEvent, bobRosterEvent, bob1RosterEvent);

   auto aliceAddEvent = std::async(std::launch::async, [&]()
   {
      alice.rosterJson->addRosterItem(alice.rosterHandle, bob.config.bare(), "bob");
      assertXmppRosterUpdate(alice, [&](const XmppRosterUpdateEvent& evt)
      {
         ASSERT_FALSE(evt.fullUpdate);
         ASSERT_EQ(evt.added.size(), 1);
         ASSERT_EQ(evt.updated.size(), 0);
         ASSERT_EQ(evt.removed.size(), 0);
      });

      alice.rosterJson->subscribePresence(alice.rosterHandle, bob.config.bare(), "bob");
      assertXmppRosterPresence(alice, [&](const XmppRosterPresenceEvent& evt)
      {
         ASSERT_EQ(evt.rosterItem.address, bob.config.bare());
         ASSERT_EQ(evt.rosterItem.subscription, XmppRoster::SubscriptionState_Out);
      });
      assertXmppRosterPresence(alice, [&](const XmppRosterPresenceEvent& evt)
      {
         ASSERT_EQ(evt.rosterItem.address, bob.config.bare());
         ASSERT_EQ(evt.rosterItem.resources.size(), 2);
      });
   });

   auto bobAcceptEvent = std::async(std::launch::async, [&]()
   {
      assertXmppSubscriptionRequest(bob, [&](const XmppRosterSubscriptionRequestEvent& evt)
      {
         ASSERT_EQ(evt.address, alice.config.bare());
      });
      bob.roster->acceptSubscriptionRequest(bob.rosterHandle, alice.config.bare());
      assertXmppRosterUpdate(bob, [&](const XmppRosterUpdateEvent& evt)
      {
         ASSERT_FALSE(evt.fullUpdate);
      });
   });

   auto bob1ConsumeSubscribeEvent = std::async(std::launch::async, [&]()
   {
      assertXmppSubscriptionRequest(bob1, [&](const XmppRosterSubscriptionRequestEvent& evt)
      {
         ASSERT_EQ(evt.address, alice.config.bare());
      });
      assertXmppRosterUpdate(bob1, [&](const XmppRosterUpdateEvent& evt)
      {
         ASSERT_FALSE(evt.fullUpdate);
      });
   });

   waitFor3(aliceAddEvent, bobAcceptEvent, bob1ConsumeSubscribeEvent);

   static const char *bob1PresenceNote = "In a Meeting Now";
   static const char *bob1ActivityNote = "Custom Activity";
   auto aliceWaitEvent = std::async(std::launch::async, [&]()
   {
      // DCM: instead of checking for each and every update, just wait/check until we get both
      int awayIndex;
      int onThePhoneIndex;

      do {
         awayIndex = -1;
         onThePhoneIndex = -1;

         assertXmppRosterPresence(alice, [&](const XmppRosterPresenceEvent& evt)
         {
            ASSERT_EQ(evt.rosterItem.address, bob.config.bare());

            if (evt.rosterItem.resources.size() == 2)
            {
               if (evt.rosterItem.resources[0].presenceType == XmppRoster::PresenceType_Away)
                  awayIndex = 0;
               else if (evt.rosterItem.resources[0].presenceType == XmppRoster::PresenceType_DND)
                  onThePhoneIndex = 0;

               if (evt.rosterItem.resources[1].presenceType == XmppRoster::PresenceType_Away)
                  awayIndex = 1;
               else if (evt.rosterItem.resources[1].presenceType == XmppRoster::PresenceType_DND)
                  onThePhoneIndex = 1;
            }

            if (awayIndex >= 0 && onThePhoneIndex >= 0)
            {
               ASSERT_EQ(evt.rosterItem.resources[awayIndex].presenceType, XmppRoster::PresenceType_Away);
               ASSERT_EQ(evt.rosterItem.resources[awayIndex].resource, bob.config.settings.resource);

               ASSERT_EQ(evt.rosterItem.resources[onThePhoneIndex].presenceType, XmppRoster::PresenceType_DND);
               ASSERT_EQ(evt.rosterItem.resources[onThePhoneIndex].presenceStatusText, bob1PresenceNote);
               ASSERT_EQ(evt.rosterItem.resources[onThePhoneIndex].userActivityGeneralType, XmppRoster::ActivityTalking);
               ASSERT_EQ(evt.rosterItem.resources[onThePhoneIndex].userActivitySpecificType, XmppRoster::ActivityOnThePhone);
               ASSERT_EQ(evt.rosterItem.resources[onThePhoneIndex].userActivityText, bob1ActivityNote);
               ASSERT_EQ(evt.rosterItem.resources[onThePhoneIndex].resource, bob1.config.settings.resource);
            }
            else
            {
               safeCout("MultipleUserActivities: waiting for bob and bob1's presence updates");
            }
         });

      } while (awayIndex == -1 || onThePhoneIndex == -1);

      ASSERT_TRUE(awayIndex >= 0);
      ASSERT_TRUE(onThePhoneIndex >= 0);
   });

   bob.account->publishPresence(bob.handle, XmppRoster::PresenceType_Away);
   bob1.account->publishPresence(bob1.handle, XmppRoster::PresenceType_DND, bob1PresenceNote, XmppRoster::ActivityTalking, XmppRoster::ActivityOnThePhone, bob1ActivityNote);

   waitFor(aliceWaitEvent);

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   bob.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   bob1.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
}

#endif // USE_CPCAPI2_JSON_TESTS

TEST_F(XmppRosterModuleTest, DISABLED_XmppRosterMultipleLogins) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");
   XmppTestAccount bob1(bob, "bob1");

   auto aliceRosterEvent = std::async(std::launch::async, [&] () {
      assertXmppRosterUpdate(alice, [](const XmppRosterUpdateEvent& evt){
         ASSERT_TRUE(evt.fullUpdate);
         ASSERT_EQ(evt.added.size(), 0);
         ASSERT_EQ(evt.updated.size(), 0);
         ASSERT_EQ(evt.removed.size(), 0);
      });
   });

   auto bobRosterEvent = std::async(std::launch::async, [&] () {
      assertXmppRosterUpdate(bob, [](const XmppRosterUpdateEvent& evt){
         ASSERT_TRUE(evt.fullUpdate);
         ASSERT_EQ(evt.added.size(), 0);
         ASSERT_EQ(evt.updated.size(), 0);
         ASSERT_EQ(evt.removed.size(), 0);
      });
   });

   auto bob1RosterEvent = std::async(std::launch::async, [&] () {
      assertXmppRosterUpdate(bob1, [](const XmppRosterUpdateEvent& evt){
         ASSERT_TRUE(evt.fullUpdate);
         ASSERT_EQ(evt.added.size(), 0);
         ASSERT_EQ(evt.updated.size(), 0);
         ASSERT_EQ(evt.removed.size(), 0);
      });
   });

   waitFor3(aliceRosterEvent, bobRosterEvent, bob1RosterEvent);

   auto aliceAddEvent = std::async(std::launch::async, [&] () {
      alice.roster->addRosterItem(alice.rosterHandle, bob.config.bare(), bob.config.name);
      assertXmppRosterUpdate(alice, [](const XmppRosterUpdateEvent& evt){
         ASSERT_FALSE(evt.fullUpdate);
         ASSERT_EQ(evt.added.size(), 1);
         ASSERT_EQ(evt.updated.size(), 0);
         ASSERT_EQ(evt.removed.size(), 0);
      });

      alice.roster->subscribePresence(alice.rosterHandle, bob.config.bare(), bob.config.name);
      assertXmppRosterPresence(alice, [&](const XmppRosterPresenceEvent& evt){
         ASSERT_EQ(evt.rosterItem.address, bob.config.bare());
         ASSERT_NE(evt.rosterItem.subscription, XmppRoster::SubscriptionState_Out);
      });
   });

   auto bobAcceptEvent = std::async(std::launch::async, [&] () {
      std::this_thread::sleep_for(std::chrono::seconds(5)); // wait until both bob clients receive subscription request
      assertXmppSubscriptionRequest(bob, [&](const XmppRosterSubscriptionRequestEvent& evt){
         ASSERT_EQ(evt.address, alice.config.bare());
      });
      bob.roster->acceptSubscriptionRequest(bob.rosterHandle, alice.config.bare());
      assertXmppRosterUpdate(bob, [](const XmppRosterUpdateEvent& evt){
         ASSERT_FALSE(evt.fullUpdate);
         ASSERT_EQ(evt.added.size(), 1);
         ASSERT_EQ(evt.updated.size(), 0);
         ASSERT_EQ(evt.removed.size(), 0);
      });
   });

   auto bob1AcceptEvent = std::async(std::launch::async, [&] () {
      std::this_thread::sleep_for(std::chrono::seconds(5)); // wait until both bob clients receive subscription request
      assertXmppSubscriptionRequest(bob1, [&](const XmppRosterSubscriptionRequestEvent& evt){
         ASSERT_EQ(evt.address, alice.config.bare());
      });
      bob1.roster->acceptSubscriptionRequest(bob1.rosterHandle, alice.config.bare());
      assertXmppRosterUpdate(bob1, [](const XmppRosterUpdateEvent& evt){
         ASSERT_FALSE(evt.fullUpdate);
         ASSERT_EQ(evt.added.size(), 1);
         ASSERT_EQ(evt.updated.size(), 0);
         ASSERT_EQ(evt.removed.size(), 0);
      });
   });

   waitFor3(aliceAddEvent, bobAcceptEvent, bob1AcceptEvent);

   assertXmppRosterPresence(alice, [&](const XmppRosterPresenceEvent& evt){
      ASSERT_EQ(evt.rosterItem.address, bob.config.bare());
      //ASSERT_EQ(evt.rosterItem.subscription, XmppRoster::SubscriptionState_Out);
      ASSERT_EQ(evt.rosterItem.resources.size(), 2);
      ASSERT_EQ(evt.rosterItem.resources[0].presenceType, XmppRoster::PresenceType_Available);
   });
}

TEST_F(XmppRosterModuleTest, XmppRosterGetStatusPeerNotEnabled) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob", Account_NoInit);

   cpc::vector<XmppRoster::RosterItem> items;

   alice.roster->addRosterItem(alice.rosterHandle, bob.config.bare(), "bob");

   std::this_thread::sleep_for(std::chrono::seconds(2));

   alice.roster->getRosterState(alice.rosterHandle, items);
   ASSERT_EQ(items.size(), 1);
   ASSERT_EQ(items[0].address, bob.config.bare());
   ASSERT_EQ(items[0].displayName, "bob");
   ASSERT_EQ(items[0].groups.size(), 0);
   ASSERT_EQ(items[0].resources.empty(), true);
   ASSERT_EQ(items[0].subscription, XmppRoster::SubscriptionState_None);

   alice.roster->subscribePresence(alice.rosterHandle, bob.config.bare(), "bob");

   std::this_thread::sleep_for(std::chrono::seconds(2));

   alice.roster->getRosterState(alice.rosterHandle, items);
   ASSERT_EQ(items.size(), 1);
   ASSERT_EQ(items[0].address, bob.config.bare());
   ASSERT_EQ(items[0].displayName, "bob");
   ASSERT_EQ(items[0].groups.size(), 0);
   ASSERT_EQ(items[0].resources.empty(), true);
   ASSERT_EQ(items[0].subscription, XmppRoster::SubscriptionState_None_OutPending);
}

TEST_F(XmppRosterModuleTest, DISABLED_XmppRosterGetStatus) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   assertXmppRosterUpdate(alice, [](const XmppRosterUpdateEvent& evt) {
      ASSERT_TRUE(evt.fullUpdate);
      ASSERT_EQ(evt.added.size(), 0);
      ASSERT_EQ(evt.updated.size(), 0);
      ASSERT_EQ(evt.removed.size(), 0);
   });

   assertXmppRosterUpdate(bob, [](const XmppRosterUpdateEvent& evt) {
      ASSERT_TRUE(evt.fullUpdate);
      ASSERT_EQ(evt.added.size(), 0);
      ASSERT_EQ(evt.updated.size(), 0);
      ASSERT_EQ(evt.removed.size(), 0);
   });

   cpc::vector<XmppRoster::RosterItem> items;

   alice.roster->addRosterItem(alice.rosterHandle, bob.config.bare(), "bob");
   assertXmppRosterUpdate(alice, [&](const XmppRosterUpdateEvent& evt) {
      ASSERT_EQ(evt.added.size(), 1);
      ASSERT_EQ(evt.added[0].item.address, bob.config.bare());
   });

   alice.roster->getRosterState(alice.rosterHandle, items);
   ASSERT_EQ(items.size(), 1);
   ASSERT_EQ(items[0].address, bob.config.bare());
   ASSERT_EQ(items[0].displayName, "bob");
   ASSERT_EQ(items[0].groups.size(), 0);
   ASSERT_EQ(items[0].resources.empty(), true);
   ASSERT_EQ(items[0].subscription, XmppRoster::SubscriptionState_None);

   alice.roster->subscribePresence(alice.rosterHandle, bob.config.bare(), "bob");

   // subcribePresence will cause bob to be deleted from the roster??
   //alice.roster->getRosterState(alice.rosterHandle, items);
   //ASSERT_EQ(items.size(), 1);
   //ASSERT_EQ(items[0].address, bob.config.bare());
   //ASSERT_EQ(items[0].displayName, "bob");
   //ASSERT_EQ(items[0].groups.size(), 0);
   //ASSERT_EQ(items[0].resources.empty(), true);
   //ASSERT_EQ(items[0].subscription, XmppRoster::SubscriptionState_None_OutPending);

   assertXmppSubscriptionRequest(bob, [&](const XmppRosterSubscriptionRequestEvent& evt){
      ASSERT_EQ(evt.address, alice.config.bare());
      ASSERT_EQ(evt.msg, "");
      bob.roster->acceptSubscriptionRequest(bob.rosterHandle, alice.config.bare());
   });

   std::this_thread::sleep_for(std::chrono::seconds(2));

   alice.roster->getRosterState(alice.rosterHandle, items);
   ASSERT_EQ(items.size(), 1);
   ASSERT_EQ(items[0].address, bob.config.bare());
   ASSERT_EQ(items[0].displayName, "bob");
   ASSERT_EQ(items[0].groups.size(), 0);
   ASSERT_EQ(items[0].resources.empty(), false);
   ASSERT_EQ(items[0].resources[0].presenceType, XmppRoster::PresenceType_Available);
   ASSERT_EQ(items[0].resources[0].presenceStatusText, "");
   ASSERT_EQ(items[0].resources[0].priority, 0);
   ASSERT_EQ(items[0].resources[0].resource.empty(), false);
   ASSERT_EQ(items[0].subscription, XmppRoster::SubscriptionState_Out);

   bob.account->publishPresence(bob.handle, XmppRoster::PresenceType_Away, "away");

   std::this_thread::sleep_for(std::chrono::seconds(2));

   alice.roster->getRosterState(alice.rosterHandle, items);
   ASSERT_EQ(items.size(), 1);
   ASSERT_EQ(items[0].address, bob.config.bare());
   ASSERT_EQ(items[0].displayName, "bob");
   ASSERT_EQ(items[0].groups.size(), 0);
   ASSERT_EQ(items[0].resources.empty(), false);
   ASSERT_EQ(items[0].resources[0].presenceType, XmppRoster::PresenceType_Away);
   ASSERT_EQ(items[0].resources[0].presenceStatusText, "away");
   ASSERT_EQ(items[0].resources[0].priority, 0);
   ASSERT_EQ(items[0].resources[0].resource.empty(), false);
   ASSERT_EQ(items[0].subscription, XmppRoster::SubscriptionState_Out);
}

TEST_F(XmppRosterModuleTest, DISABLED_ManualAddAccept) { // disabled, require another client to be subscribed
   XmppTestAccount alice("alice");
   assertXmppRosterUpdate(alice, [](const XmppRosterUpdateEvent& evt){
      ASSERT_TRUE(evt.fullUpdate);
      ASSERT_EQ(evt.added.size(), 0);
      ASSERT_EQ(evt.updated.size(), 0);
      ASSERT_EQ(evt.removed.size(), 0);
   });

   XmppTestAccount bob("bob", Account_NoInit);

   alice.roster->addRosterItem(alice.rosterHandle, bob.config.bare(), "bob");
   assertXmppRosterUpdate(alice, [](const XmppRosterUpdateEvent& evt){
      ASSERT_FALSE(evt.fullUpdate);
      ASSERT_EQ(evt.added.size(), 1);
      ASSERT_EQ(evt.updated.size(), 0);
      ASSERT_EQ(evt.removed.size(), 0);
      ASSERT_EQ(evt.added[0].item.subscription, XmppRoster::SubscriptionState_None);
      ASSERT_TRUE(evt.added[0].item.resources.empty());
   });

   alice.roster->subscribePresence(alice.rosterHandle, bob.config.bare(), "bob");
   assertXmppRosterUpdate(alice, [](const XmppRosterUpdateEvent& evt){
      ASSERT_FALSE(evt.fullUpdate);
      ASSERT_EQ(evt.added.size(), 0);
      ASSERT_EQ(evt.updated.size(), 1);
      ASSERT_EQ(evt.removed.size(), 0);
      ASSERT_EQ(evt.updated[0].item.subscription, XmppRoster::SubscriptionState_None_OutPending);
      ASSERT_TRUE(evt.updated[0].item.resources.empty());
   });
   assertXmppRosterUpdate(alice, [](const XmppRosterUpdateEvent& evt){
      ASSERT_FALSE(evt.fullUpdate);
      ASSERT_EQ(evt.added.size(), 0);
      ASSERT_EQ(evt.updated.size(), 1);
      ASSERT_EQ(evt.removed.size(), 0);
      ASSERT_EQ(evt.updated[0].item.subscription, XmppRoster::SubscriptionState_Out);
      ASSERT_TRUE(evt.updated[0].item.resources.empty());
   });
   assertXmppRosterUpdate(alice, [](const XmppRosterUpdateEvent& evt){ // bliu: this identical event shouldn't be here
      ASSERT_FALSE(evt.fullUpdate);
      ASSERT_EQ(evt.added.size(), 0);
      ASSERT_EQ(evt.updated.size(), 1);
      ASSERT_EQ(evt.removed.size(), 0);
      ASSERT_EQ(evt.updated[0].item.subscription, XmppRoster::SubscriptionState_Out);
      ASSERT_TRUE(evt.updated[0].item.resources.empty());
   });
   assertXmppRosterPresence(alice, [&](const XmppRosterPresenceEvent& evt){
      ASSERT_EQ(evt.rosterItem.address, bob.config.bare());
      ASSERT_EQ(evt.rosterItem.subscription, XmppRoster::SubscriptionState_Out);
      ASSERT_EQ(evt.rosterItem.resources.size(), 1);
      ASSERT_EQ(evt.rosterItem.resources[0].presenceType, XmppRoster::PresenceType_Available);
   });
}

TEST_F(XmppRosterModuleTest, BlockPresence) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   auto aliceRosterEvent = std::async(std::launch::async, [&]() {
      assertXmppRosterUpdate(alice, [](const XmppRosterUpdateEvent& evt) {
         ASSERT_TRUE(evt.fullUpdate);
         ASSERT_EQ(evt.added.size(), 0);
         ASSERT_EQ(evt.updated.size(), 0);
         ASSERT_EQ(evt.removed.size(), 0);
      });
   });

   auto bobRosterEvent = std::async(std::launch::async, [&]() {
      assertXmppRosterUpdate(bob, [](const XmppRosterUpdateEvent& evt) {
         ASSERT_TRUE(evt.fullUpdate);
         ASSERT_EQ(evt.added.size(), 0);
         ASSERT_EQ(evt.updated.size(), 0);
         ASSERT_EQ(evt.removed.size(), 0);
      });
   });

   waitFor2(aliceRosterEvent, bobRosterEvent);

   auto aliceAddEvent = std::async(std::launch::async, [&]() {
      alice.roster->addRosterItem(alice.rosterHandle, bob.config.bare(), "bob");
      assertXmppRosterUpdate(alice, [&](const XmppRosterUpdateEvent& evt) {
         ASSERT_FALSE(evt.fullUpdate);
         ASSERT_EQ(evt.added.size(), 1);
         ASSERT_EQ(evt.updated.size(), 0);
         ASSERT_EQ(evt.removed.size(), 0);
      });

      alice.roster->subscribePresence(alice.rosterHandle, bob.config.bare(), "bob");
      assertXmppRosterPresence(alice, [&](const XmppRosterPresenceEvent& evt) {
         ASSERT_EQ(evt.rosterItem.address, bob.config.bare());
         ASSERT_EQ(evt.rosterItem.subscription, XmppRoster::SubscriptionState_Out);
         ASSERT_EQ(evt.rosterItem.resources.size(), 1);
         ASSERT_EQ(evt.rosterItem.resources[0].presenceType, XmppRoster::PresenceType_Available);
      });
   });

   auto bobAcceptEvent = std::async(std::launch::async, [&]() {
      assertXmppSubscriptionRequest(bob, [&](const XmppRosterSubscriptionRequestEvent& evt) {
         ASSERT_EQ(evt.address, alice.config.bare());
      });
      bob.roster->acceptSubscriptionRequest(bob.rosterHandle, alice.config.bare());
   });

   waitFor2(aliceAddEvent, bobAcceptEvent);

   bob.account->publishPresence(bob.handle, XmppRoster::PresenceType_Available, "Available");
   assertXmppRosterPresence(alice, [&](const XmppRosterPresenceEvent& evt) {
      ASSERT_EQ(evt.rosterItem.address, bob.config.bare());
      ASSERT_EQ(evt.rosterItem.subscription, XmppRoster::SubscriptionState_Out);
      ASSERT_EQ(evt.rosterItem.resources.size(), 1);
      ASSERT_EQ(evt.rosterItem.resources[0].presenceType, XmppRoster::PresenceType_Available);
      ASSERT_EQ(evt.rosterItem.resources[0].presenceStatusText, "Available");
   });

   alice.account->blockIncomingPresence(alice.handle, true);

   std::this_thread::sleep_for(std::chrono::seconds(2));

   bob.account->publishPresence(bob.handle, XmppRoster::PresenceType_Available, "Blocked");

   std::this_thread::sleep_for(std::chrono::seconds(2));

   alice.account->blockIncomingPresence(alice.handle, false);

   std::this_thread::sleep_for(std::chrono::seconds(2));

   bob.account->publishPresence(bob.handle, XmppRoster::PresenceType_Available, "Unblocked");
   assertXmppRosterPresence(alice, [&](const XmppRosterPresenceEvent& evt) {
      ASSERT_EQ(evt.rosterItem.address, bob.config.bare());
      ASSERT_EQ(evt.rosterItem.subscription, XmppRoster::SubscriptionState_Out);
      ASSERT_EQ(evt.rosterItem.resources.size(), 1);
      ASSERT_EQ(evt.rosterItem.resources[0].presenceType, XmppRoster::PresenceType_Available);
      ASSERT_EQ(evt.rosterItem.resources[0].presenceStatusText, "Unblocked");
   });
}

TEST_F(XmppRosterModuleTest, CpcXepUserActivity) {

   CpcXepUserActivity* x = new CpcXepUserActivity();
   x->setGeneralType(CpcXepUserActivity::ActivityDoingChores);
   x->setSpecificType(CpcXepUserActivity::ActivityBrushingTeeth);
   gloox::Tag* t = x->tag();
   delete t;
   delete x;

}

}  // namespace

#endif
