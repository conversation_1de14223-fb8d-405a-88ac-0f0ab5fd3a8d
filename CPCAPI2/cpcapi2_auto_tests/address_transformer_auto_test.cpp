#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#include "cpcapi2_test_fixture.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>

#include "test_framework/cpcapi2_test_framework.h"
#include "test_framework/bare_bones_sip_endpoint.h"

#include <thread>
#include <future>
#include "../../impl/phone/AddressTransformers.h"

using namespace CPCAPI2;

namespace {

class AddressTransformerTest : public CpcapiAutoTest
{
public:
   AddressTransformerTest() {}
   virtual ~AddressTransformerTest() {}
};

TEST_F(AddressTransformerTest, DefaultAddressTransformer) {
   DefaultAddressTransformer defaultTransformer;
   
   AddressTransformationContext context;
   context.registrationDomain = "demo.xten.com";
   cpc::string targetAddress = "";
   int result = 0;

   cpc::string sip1 = "alice";
   context.addressUsageType = AddressUsageType::AddressUsageType_SipPresence;
   result = defaultTransformer.applyTransformation(sip1, context, targetAddress);
   ASSERT_TRUE(result == kSuccess);
   ASSERT_TRUE(targetAddress == "sip:<EMAIL>");

   cpc::string sip2 = "sip:alice";   
   context.addressUsageType = AddressUsageType::AddressUsageType_SipFileTransfer;
   result = defaultTransformer.applyTransformation(sip2, context, targetAddress);
   ASSERT_TRUE(result == kSuccess);
   ASSERT_TRUE(targetAddress == "sip:<EMAIL>");

   cpc::string sip3 = "alice@*********";  
   context.addressUsageType = AddressUsageType::AddressUsageType_SipChat;
   result = defaultTransformer.applyTransformation(sip3, context, targetAddress);
   ASSERT_TRUE(result == kSuccess);
   ASSERT_TRUE(targetAddress == "sip:alice@*********");

   cpc::string sip4 = "1234";   
   context.addressUsageType = AddressUsageType::AddressUsageType_SipConversation;
   result = defaultTransformer.applyTransformation(sip4, context, targetAddress);
   ASSERT_TRUE(result == kSuccess);
   ASSERT_TRUE(targetAddress == "sip:<EMAIL>");

   cpc::string sip5 = "sip:1234";   
   context.addressUsageType = AddressUsageType::AddressUsageType_SipConversation;
   result = defaultTransformer.applyTransformation(sip5, context, targetAddress);
   ASSERT_TRUE(result == kSuccess);
   ASSERT_TRUE(targetAddress == "sip:<EMAIL>");

   cpc::string sip6 = "sip:1234@*********";  
   context.addressUsageType = AddressUsageType::AddressUsageType_SipConversation;
   result = defaultTransformer.applyTransformation(sip6, context, targetAddress);
   ASSERT_TRUE(result == kSuccess);
   ASSERT_TRUE(targetAddress == "sip:1234@*********");

   cpc::string sip7 = "1234@*********:5060";  
   context.addressUsageType = AddressUsageType::AddressUsageType_SipConversation;
   result = defaultTransformer.applyTransformation(sip7, context, targetAddress);
   ASSERT_TRUE(result == kSuccess);
   ASSERT_TRUE(targetAddress == "sip:1234@*********");

   cpc::string tel1 = "tel:+12503826161";
   context.addressUsageType = AddressUsageType::AddressUsageType_SipConversation;
   result = defaultTransformer.applyTransformation(tel1, context, targetAddress);
   ASSERT_TRUE(result == kSuccess);
   ASSERT_TRUE(targetAddress == "sip:+<EMAIL>;user=phone");
}

}