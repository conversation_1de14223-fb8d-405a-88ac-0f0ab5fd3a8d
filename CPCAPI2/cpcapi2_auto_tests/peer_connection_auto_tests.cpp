#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#if (CPCAPI2_BRAND_PEER_CONNECTION_MODULE == 1)

#include "cpcapi2_test_fixture.h"

#include <gtest/gtest.h>
#include <cpcapi2.h>

#include "test_framework/cpcapi2_test_framework.h"
#include "test_framework/bare_bones_sip_endpoint.h"

#include <thread>
#include <future>

using namespace CPCAPI2;
using namespace CPCAPI2::Media;
using namespace CPCAPI2::PeerConnection;
using namespace CPCAPI2::test;

namespace {

class PeerConnectionTest : public CpcapiAutoTest
{
public:
   PeerConnectionTest() {}
   virtual ~PeerConnectionTest() {}
};

TEST_F(PeerConnectionTest, BasicAudioCall) {

   TestAccount alice("alice");
   TestAccount bob("bob");

   PeerConnectionSettings aliceSettings;
   aliceSettings.certAor = alice.config.settings.username + "@" + alice.config.settings.domain;
   aliceSettings.natTraversalMode = PeerConnectionSettings::NatTraversalMode_None;
   //aliceSettings.natTraversalServerHostname = "stun.counterpath.com";
   aliceSettings.natTraversalServerPort = 3478;
   aliceSettings.sessionName = "peerconnectiontest";
   PeerConnectionSettings bobSettings;
   bobSettings = aliceSettings;
   bobSettings.certAor = bob.config.settings.username + "@" + bob.config.settings.domain;

   PeerConnectionHandle alicePeerConn = alice.peerConnection->createPeerConnection();
   alice.peerConnection->setHandler(alicePeerConn, (PeerConnectionHandler*)0xDEADBEEF);
   alice.peerConnection->setDefaultSettings(alicePeerConn, aliceSettings);
   CPCAPI2::PeerConnection::MediaInfo mediaInfo;
   mediaInfo.mediaType = PeerConnection::MediaType_Audio;
   mediaInfo.mediaDirection = PeerConnection::MediaDirection_SendRecv;
   mediaInfo.mediaEncryptionOptions.mediaEncryptionMode = PeerConnection::MediaEncryptionMode_SRTP_DTLS_Encrypted;
   mediaInfo.mediaEncryptionOptions.secureMediaRequired = true;
   alice.peerConnection->configureMedia(alicePeerConn, alice.peerConnection->createMediaStream(), mediaInfo);
   ASSERT_EQ(alice.peerConnection->createOffer(alicePeerConn), kSuccess);

   CPCAPI2::PeerConnection::SessionDescription aliceSdp;
   {
      PeerConnectionHandle h;
      CreateOfferResult evt;
      ASSERT_TRUE(cpcExpectEvent(alice.peerConnEvents,
         "PeerConnectionHandler::onCreateOfferResult",
         45000,
         AlwaysTruePred(), h, evt));
      ASSERT_NE(strlen(evt.sdp.sdpString.c_str()), 0);
      aliceSdp = evt.sdp;
      std::cout << "alice sdp:" << std::endl << resip::Data(aliceSdp.sdpString, aliceSdp.sdpLen) << std::endl;
   }

   ASSERT_EQ(alice.peerConnection->setLocalDescription(alicePeerConn, aliceSdp), kSuccess);

   PeerConnectionHandle bobPeerConn = bob.peerConnection->createPeerConnection();
   bob.peerConnection->setHandler(bobPeerConn, (PeerConnectionHandler*)0xDEADBEEF);
   bob.peerConnection->setDefaultSettings(bobPeerConn, bobSettings);
   CPCAPI2::PeerConnection::MediaInfo bobMediaInfo;
   bobMediaInfo.mediaType = PeerConnection::MediaType_Audio;
   bobMediaInfo.mediaDirection = PeerConnection::MediaDirection_SendRecv;
   bobMediaInfo.mediaEncryptionOptions.mediaEncryptionMode = PeerConnection::MediaEncryptionMode_SRTP_DTLS_Encrypted;
   bobMediaInfo.mediaEncryptionOptions.secureMediaRequired = true;
   ASSERT_EQ(bob.peerConnection->configureMedia(bobPeerConn, bob.peerConnection->createMediaStream(), bobMediaInfo), 0);
   ASSERT_EQ(bob.peerConnection->setRemoteDescription(bobPeerConn, aliceSdp), kSuccess);

   {
      PeerConnectionHandle h;
      SetRemoteSessionDescriptionResult evt;
      ASSERT_TRUE(cpcExpectEvent(bob.peerConnEvents,
         "PeerConnectionHandler::onSetRemoteSessionDescriptionResult",
         10000,
         AlwaysTruePred(), h, evt));
   }

   ASSERT_EQ(bob.peerConnection->createAnswer(bobPeerConn), kSuccess);

   CPCAPI2::PeerConnection::SessionDescription bobSdp;
   {
      PeerConnectionHandle h;
      CreateAnswerResult evt;
      ASSERT_TRUE(cpcExpectEvent(bob.peerConnEvents,
         "PeerConnectionHandler::onCreateAnswerResult",
         30000,
         AlwaysTruePred(), h, evt));
      ASSERT_NE(strlen(evt.sdp.sdpString.c_str()), 0);
      bobSdp = evt.sdp;
      std::cout << "bob sdp:" << std::endl << resip::Data(bobSdp.sdpString, bobSdp.sdpLen) << std::endl;
   }   
   ASSERT_EQ(bob.peerConnection->setLocalDescription(bobPeerConn, bobSdp), kSuccess);

   ASSERT_EQ(alice.peerConnection->setRemoteDescription(alicePeerConn, bobSdp), kSuccess);

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   ASSERT_EQ(bob.peerConnection->close(bobPeerConn), kSuccess);
   ASSERT_EQ(alice.peerConnection->close(alicePeerConn), kSuccess);

   //
   //cpc::vector<AudioCodecInfo> codecs;
   //std::unique_ptr<TestAudioHandler> testAudioHandler(new TestAudioHandler(codecs));
   //alice.audio->setHandler(testAudioHandler.get());

   //ASSERT_EQ(alice.audio->queryCodecList(), kSuccess);
   //alice.media->process(MediaManager::kBlockingModeInfinite);
   //ASSERT_NE(codecs.size(), 0);

   //alice.audio->setCodecEnabled(codecs[0].id, false);
   //codecs.clear();
   //ASSERT_EQ(alice.audio->queryCodecList(), kSuccess);
   //alice.media->process(MediaManager::kBlockingModeInfinite);

   //ASSERT_NE(codecs.size(), 0);
   //ASSERT_EQ(codecs[0].enabled, false);
}

TEST_F(PeerConnectionTest, BasicAudioCall_NoRTCP) {

   TestAccount alice("alice");
   TestAccount bob("bob");

   PeerConnectionSettings aliceSettings;
   aliceSettings.certAor = alice.config.settings.username + "@" + alice.config.settings.domain;
   aliceSettings.natTraversalMode = PeerConnectionSettings::NatTraversalMode_None;
   //aliceSettings.natTraversalServerHostname = "stun.counterpath.com";
   aliceSettings.natTraversalServerPort = 3478;
   aliceSettings.sessionName = "peerconnectiontest";
   aliceSettings.rtcpEnabled = false;
   PeerConnectionSettings bobSettings;
   bobSettings = aliceSettings;
   bobSettings.certAor = bob.config.settings.username + "@" + bob.config.settings.domain;

   PeerConnectionHandle alicePeerConn = alice.peerConnection->createPeerConnection();
   alice.peerConnection->setHandler(alicePeerConn, (PeerConnectionHandler*)0xDEADBEEF);
   alice.peerConnection->setDefaultSettings(alicePeerConn, aliceSettings);
   CPCAPI2::PeerConnection::MediaInfo mediaInfo;
   mediaInfo.mediaType = PeerConnection::MediaType_Audio;
   mediaInfo.mediaDirection = PeerConnection::MediaDirection_SendRecv;
   mediaInfo.mediaEncryptionOptions.mediaEncryptionMode = PeerConnection::MediaEncryptionMode_SRTP_DTLS_Encrypted;
   mediaInfo.mediaEncryptionOptions.secureMediaRequired = true;
   alice.peerConnection->configureMedia(alicePeerConn, alice.peerConnection->createMediaStream(), mediaInfo);
   ASSERT_EQ(alice.peerConnection->createOffer(alicePeerConn), kSuccess);

   CPCAPI2::PeerConnection::SessionDescription aliceSdp;
   {
      PeerConnectionHandle h;
      CreateOfferResult evt;
      ASSERT_TRUE(cpcExpectEvent(alice.peerConnEvents,
         "PeerConnectionHandler::onCreateOfferResult",
         45000,
         AlwaysTruePred(), h, evt));
      ASSERT_NE(strlen(evt.sdp.sdpString.c_str()), 0);
      aliceSdp = evt.sdp;
      std::cout << "alice sdp:" << std::endl << resip::Data(aliceSdp.sdpString, aliceSdp.sdpLen) << std::endl;
   }

   ASSERT_EQ(alice.peerConnection->setLocalDescription(alicePeerConn, aliceSdp), kSuccess);

   PeerConnectionHandle bobPeerConn = bob.peerConnection->createPeerConnection();
   bob.peerConnection->setHandler(bobPeerConn, (PeerConnectionHandler*)0xDEADBEEF);
   bob.peerConnection->setDefaultSettings(bobPeerConn, bobSettings);
   CPCAPI2::PeerConnection::MediaInfo bobMediaInfo;
   bobMediaInfo.mediaType = PeerConnection::MediaType_Audio;
   bobMediaInfo.mediaDirection = PeerConnection::MediaDirection_SendRecv;
   bobMediaInfo.mediaEncryptionOptions.mediaEncryptionMode = PeerConnection::MediaEncryptionMode_SRTP_DTLS_Encrypted;
   bobMediaInfo.mediaEncryptionOptions.secureMediaRequired = true;
   ASSERT_EQ(bob.peerConnection->configureMedia(bobPeerConn, bob.peerConnection->createMediaStream(), bobMediaInfo), 0);
   ASSERT_EQ(bob.peerConnection->setRemoteDescription(bobPeerConn, aliceSdp), kSuccess);

   {
      PeerConnectionHandle h;
      SetRemoteSessionDescriptionResult evt;
      ASSERT_TRUE(cpcExpectEvent(bob.peerConnEvents,
         "PeerConnectionHandler::onSetRemoteSessionDescriptionResult",
         10000,
         AlwaysTruePred(), h, evt));
   }

   ASSERT_EQ(bob.peerConnection->createAnswer(bobPeerConn), kSuccess);

   CPCAPI2::PeerConnection::SessionDescription bobSdp;
   {
      PeerConnectionHandle h;
      CreateAnswerResult evt;
      ASSERT_TRUE(cpcExpectEvent(bob.peerConnEvents,
         "PeerConnectionHandler::onCreateAnswerResult",
         30000,
         AlwaysTruePred(), h, evt));
      ASSERT_NE(strlen(evt.sdp.sdpString.c_str()), 0);
      bobSdp = evt.sdp;
      std::cout << "bob sdp:" << std::endl << resip::Data(bobSdp.sdpString, bobSdp.sdpLen) << std::endl;
   }
   ASSERT_EQ(bob.peerConnection->setLocalDescription(bobPeerConn, bobSdp), kSuccess);

   ASSERT_EQ(alice.peerConnection->setRemoteDescription(alicePeerConn, bobSdp), kSuccess);

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   ASSERT_EQ(bob.peerConnection->close(bobPeerConn), kSuccess);
   ASSERT_EQ(alice.peerConnection->close(alicePeerConn), kSuccess);

   //
   //cpc::vector<AudioCodecInfo> codecs;
   //std::unique_ptr<TestAudioHandler> testAudioHandler(new TestAudioHandler(codecs));
   //alice.audio->setHandler(testAudioHandler.get());

   //ASSERT_EQ(alice.audio->queryCodecList(), kSuccess);
   //alice.media->process(MediaManager::kBlockingModeInfinite);
   //ASSERT_NE(codecs.size(), 0);

   //alice.audio->setCodecEnabled(codecs[0].id, false);
   //codecs.clear();
   //ASSERT_EQ(alice.audio->queryCodecList(), kSuccess);
   //alice.media->process(MediaManager::kBlockingModeInfinite);

   //ASSERT_NE(codecs.size(), 0);
   //ASSERT_EQ(codecs[0].enabled, false);
}

TEST_F(PeerConnectionTest, BasicAudioCall_ICE_DTLS) {

   resip::DnsStub::setDnsTimeoutAndTries(10, 10);

   TestAccount alice("alice");
   TestAccount bob("bob");

   PeerConnectionSettings aliceSettings;
   aliceSettings.certAor = alice.config.settings.username + "@" + alice.config.settings.domain;
   aliceSettings.natTraversalMode = PeerConnectionSettings::NatTraversalMode_ICE;
   aliceSettings.natTraversalServerType = PeerConnectionSettings::NatTraversalServerType_StunOnly;
   aliceSettings.natTraversalServerHostname = "stun.counterpath.com";
   aliceSettings.natTraversalServerPort = 3478;
   aliceSettings.sessionName = "peerconnectiontest";
   PeerConnectionSettings bobSettings;
   bobSettings = aliceSettings;
   bobSettings.certAor = bob.config.settings.username + "@" + bob.config.settings.domain;

   PeerConnectionHandle alicePeerConn = alice.peerConnection->createPeerConnection();
   alice.peerConnection->setHandler(alicePeerConn, (PeerConnectionHandler*)0xDEADBEEF);
   alice.peerConnection->setDefaultSettings(alicePeerConn, aliceSettings);
   CPCAPI2::PeerConnection::MediaInfo mediaInfo;
   mediaInfo.mediaType = PeerConnection::MediaType_Audio;
   mediaInfo.mediaDirection = PeerConnection::MediaDirection_SendRecv;
   mediaInfo.mediaEncryptionOptions.mediaEncryptionMode = PeerConnection::MediaEncryptionMode_SRTP_DTLS_Encrypted;
   mediaInfo.mediaEncryptionOptions.secureMediaRequired = true;
   alice.peerConnection->configureMedia(alicePeerConn, alice.peerConnection->createMediaStream(), mediaInfo);
   ASSERT_EQ(alice.peerConnection->createOffer(alicePeerConn), kSuccess);

   CPCAPI2::PeerConnection::SessionDescription aliceSdp;
   {
      PeerConnectionHandle h;
      CreateOfferResult evt;
      ASSERT_TRUE(cpcExpectEvent(alice.peerConnEvents,
         "PeerConnectionHandler::onCreateOfferResult",
         45000,
         AlwaysTruePred(), h, evt));
      ASSERT_NE(strlen(evt.sdp.sdpString.c_str()), 0);
      aliceSdp = evt.sdp;
      std::cout << "alice sdp:" << std::endl << resip::Data(aliceSdp.sdpString, aliceSdp.sdpLen) << std::endl;
   }

   ASSERT_EQ(alice.peerConnection->setLocalDescription(alicePeerConn, aliceSdp), kSuccess);

   PeerConnectionHandle bobPeerConn = bob.peerConnection->createPeerConnection();
   bob.peerConnection->setHandler(bobPeerConn, (PeerConnectionHandler*)0xDEADBEEF);
   bob.peerConnection->setDefaultSettings(bobPeerConn, bobSettings);
   CPCAPI2::PeerConnection::MediaInfo bobMediaInfo;
   bobMediaInfo.mediaType = PeerConnection::MediaType_Audio;
   bobMediaInfo.mediaDirection = PeerConnection::MediaDirection_SendRecv;
   bobMediaInfo.mediaEncryptionOptions.mediaEncryptionMode = PeerConnection::MediaEncryptionMode_SRTP_DTLS_Encrypted;
   bobMediaInfo.mediaEncryptionOptions.secureMediaRequired = true;
   ASSERT_EQ(bob.peerConnection->configureMedia(bobPeerConn, bob.peerConnection->createMediaStream(), bobMediaInfo), 0);
   ASSERT_EQ(bob.peerConnection->setRemoteDescription(bobPeerConn, aliceSdp), kSuccess);

   {
      PeerConnectionHandle h;
      SetRemoteSessionDescriptionResult evt;
      ASSERT_TRUE(cpcExpectEvent(bob.peerConnEvents,
         "PeerConnectionHandler::onSetRemoteSessionDescriptionResult",
         10000,
         AlwaysTruePred(), h, evt));
   }

   ASSERT_EQ(bob.peerConnection->createAnswer(bobPeerConn), kSuccess);

   CPCAPI2::PeerConnection::SessionDescription bobSdp;
   {
      PeerConnectionHandle h;
      CreateAnswerResult evt;
      ASSERT_TRUE(cpcExpectEvent(bob.peerConnEvents,
         "PeerConnectionHandler::onCreateAnswerResult",
         30000,
         AlwaysTruePred(), h, evt));
      ASSERT_NE(strlen(evt.sdp.sdpString.c_str()), 0);
      bobSdp = evt.sdp;
      std::cout << "bob sdp:" << std::endl << resip::Data(bobSdp.sdpString, bobSdp.sdpLen) << std::endl;
   }
   ASSERT_EQ(bob.peerConnection->setLocalDescription(bobPeerConn, bobSdp), kSuccess);

   ASSERT_EQ(alice.peerConnection->setRemoteDescription(alicePeerConn, bobSdp), kSuccess);

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   ASSERT_EQ(bob.peerConnection->close(bobPeerConn), kSuccess);
   ASSERT_EQ(alice.peerConnection->close(alicePeerConn), kSuccess);

   resip::DnsStub::setDnsTimeoutAndTries(0, 0);


   //
   //cpc::vector<AudioCodecInfo> codecs;
   //std::unique_ptr<TestAudioHandler> testAudioHandler(new TestAudioHandler(codecs));
   //alice.audio->setHandler(testAudioHandler.get());

   //ASSERT_EQ(alice.audio->queryCodecList(), kSuccess);
   //alice.media->process(MediaManager::kBlockingModeInfinite);
   //ASSERT_NE(codecs.size(), 0);

   //alice.audio->setCodecEnabled(codecs[0].id, false);
   //codecs.clear();
   //ASSERT_EQ(alice.audio->queryCodecList(), kSuccess);
   //alice.media->process(MediaManager::kBlockingModeInfinite);

   //ASSERT_NE(codecs.size(), 0);
   //ASSERT_EQ(codecs[0].enabled, false);
}

TEST_F(PeerConnectionTest, BasicAudioCall_RtcpMux) {

   resip::DnsStub::setDnsTimeoutAndTries(10, 10);

   TestAccount alice("alice");
   TestAccount bob("bob");

   PeerConnectionSettings aliceSettings;
   aliceSettings.certAor = alice.config.settings.username + "@" + alice.config.settings.domain;
   aliceSettings.natTraversalMode = PeerConnectionSettings::NatTraversalMode_ICE;
   aliceSettings.natTraversalServerType = PeerConnectionSettings::NatTraversalServerType_StunOnly;
   aliceSettings.natTraversalServerHostname = "stun.counterpath.com";
   aliceSettings.natTraversalServerPort = 3478;
   aliceSettings.sessionName = "peerconnectiontest";
   aliceSettings.rtcpMux = true;
   PeerConnectionSettings bobSettings;
   bobSettings = aliceSettings;
   bobSettings.certAor = bob.config.settings.username + "@" + bob.config.settings.domain;

   PeerConnectionHandle alicePeerConn = alice.peerConnection->createPeerConnection();
   alice.peerConnection->setHandler(alicePeerConn, (PeerConnectionHandler*)0xDEADBEEF);
   alice.peerConnection->setDefaultSettings(alicePeerConn, aliceSettings);
   CPCAPI2::PeerConnection::MediaInfo mediaInfo;
   mediaInfo.mediaType = PeerConnection::MediaType_Audio;
   mediaInfo.mediaDirection = PeerConnection::MediaDirection_SendRecv;
   mediaInfo.mediaEncryptionOptions.mediaEncryptionMode = PeerConnection::MediaEncryptionMode_SRTP_DTLS_Encrypted;
   mediaInfo.mediaEncryptionOptions.secureMediaRequired = true;
   alice.peerConnection->configureMedia(alicePeerConn, alice.peerConnection->createMediaStream(), mediaInfo);
   ASSERT_EQ(alice.peerConnection->createOffer(alicePeerConn), kSuccess);

   CPCAPI2::PeerConnection::SessionDescription aliceSdp;
   {
      PeerConnectionHandle h;
      CreateOfferResult evt;
      ASSERT_TRUE(cpcExpectEvent(alice.peerConnEvents,
         "PeerConnectionHandler::onCreateOfferResult",
         45000,
         AlwaysTruePred(), h, evt));
      ASSERT_NE(strlen(evt.sdp.sdpString.c_str()), 0);
      aliceSdp = evt.sdp;
      std::cout << "alice sdp:" << std::endl << resip::Data(aliceSdp.sdpString, aliceSdp.sdpLen) << std::endl;
   }

   ASSERT_EQ(alice.peerConnection->setLocalDescription(alicePeerConn, aliceSdp), kSuccess);

   PeerConnectionHandle bobPeerConn = bob.peerConnection->createPeerConnection();
   bob.peerConnection->setHandler(bobPeerConn, (PeerConnectionHandler*)0xDEADBEEF);
   bob.peerConnection->setDefaultSettings(bobPeerConn, bobSettings);
   CPCAPI2::PeerConnection::MediaInfo bobMediaInfo;
   bobMediaInfo.mediaType = PeerConnection::MediaType_Audio;
   bobMediaInfo.mediaDirection = PeerConnection::MediaDirection_SendRecv;
   bobMediaInfo.mediaEncryptionOptions.mediaEncryptionMode = PeerConnection::MediaEncryptionMode_SRTP_DTLS_Encrypted;
   bobMediaInfo.mediaEncryptionOptions.secureMediaRequired = true;
   ASSERT_EQ(bob.peerConnection->configureMedia(bobPeerConn, bob.peerConnection->createMediaStream(), bobMediaInfo), 0);
   ASSERT_EQ(bob.peerConnection->setRemoteDescription(bobPeerConn, aliceSdp), kSuccess);

   {
      PeerConnectionHandle h;
      SetRemoteSessionDescriptionResult evt;
      ASSERT_TRUE(cpcExpectEvent(bob.peerConnEvents,
         "PeerConnectionHandler::onSetRemoteSessionDescriptionResult",
         10000,
         AlwaysTruePred(), h, evt));
   }

   ASSERT_EQ(bob.peerConnection->createAnswer(bobPeerConn), kSuccess);

   CPCAPI2::PeerConnection::SessionDescription bobSdp;
   {
      PeerConnectionHandle h;
      CreateAnswerResult evt;
      ASSERT_TRUE(cpcExpectEvent(bob.peerConnEvents,
         "PeerConnectionHandler::onCreateAnswerResult",
         30000,
         AlwaysTruePred(), h, evt));
      ASSERT_NE(strlen(evt.sdp.sdpString.c_str()), 0);
      bobSdp = evt.sdp;
      std::cout << "bob sdp:" << std::endl << resip::Data(bobSdp.sdpString, bobSdp.sdpLen) << std::endl;
   }
   ASSERT_EQ(bob.peerConnection->setLocalDescription(bobPeerConn, bobSdp), kSuccess);

   ASSERT_EQ(alice.peerConnection->setRemoteDescription(alicePeerConn, bobSdp), kSuccess);

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   ASSERT_EQ(bob.peerConnection->close(bobPeerConn), kSuccess);
   ASSERT_EQ(alice.peerConnection->close(alicePeerConn), kSuccess);

   resip::DnsStub::setDnsTimeoutAndTries(0, 0);


   //
   //cpc::vector<AudioCodecInfo> codecs;
   //std::unique_ptr<TestAudioHandler> testAudioHandler(new TestAudioHandler(codecs));
   //alice.audio->setHandler(testAudioHandler.get());

   //ASSERT_EQ(alice.audio->queryCodecList(), kSuccess);
   //alice.media->process(MediaManager::kBlockingModeInfinite);
   //ASSERT_NE(codecs.size(), 0);

   //alice.audio->setCodecEnabled(codecs[0].id, false);
   //codecs.clear();
   //ASSERT_EQ(alice.audio->queryCodecList(), kSuccess);
   //alice.media->process(MediaManager::kBlockingModeInfinite);

   //ASSERT_NE(codecs.size(), 0);
   //ASSERT_EQ(codecs[0].enabled, false);
}

TEST_F(PeerConnectionTest, BasicAudioCall_OfferRtcpMux) {

   resip::DnsStub::setDnsTimeoutAndTries(10, 10);

   TestAccount alice("alice");
   TestAccount bob("bob");

   PeerConnectionSettings aliceSettings;
   aliceSettings.certAor = alice.config.settings.username + "@" + alice.config.settings.domain;
   aliceSettings.natTraversalMode = PeerConnectionSettings::NatTraversalMode_ICE;
   aliceSettings.natTraversalServerType = PeerConnectionSettings::NatTraversalServerType_StunOnly;
   aliceSettings.natTraversalServerHostname = "stun.counterpath.com";
   aliceSettings.natTraversalServerPort = 3478;
   aliceSettings.sessionName = "peerconnectiontest";
   aliceSettings.rtcpMux = true;
   PeerConnectionSettings bobSettings;
   bobSettings = aliceSettings;
   bobSettings.rtcpMux = false;
   bobSettings.certAor = bob.config.settings.username + "@" + bob.config.settings.domain;

   PeerConnectionHandle alicePeerConn = alice.peerConnection->createPeerConnection();
   alice.peerConnection->setHandler(alicePeerConn, (PeerConnectionHandler*)0xDEADBEEF);
   alice.peerConnection->setDefaultSettings(alicePeerConn, aliceSettings);
   CPCAPI2::PeerConnection::MediaInfo mediaInfo;
   mediaInfo.mediaType = PeerConnection::MediaType_Audio;
   mediaInfo.mediaDirection = PeerConnection::MediaDirection_SendRecv;
   mediaInfo.mediaEncryptionOptions.mediaEncryptionMode = PeerConnection::MediaEncryptionMode_SRTP_DTLS_Encrypted;
   mediaInfo.mediaEncryptionOptions.secureMediaRequired = true;
   alice.peerConnection->configureMedia(alicePeerConn, alice.peerConnection->createMediaStream(), mediaInfo);
   ASSERT_EQ(alice.peerConnection->createOffer(alicePeerConn), kSuccess);

   CPCAPI2::PeerConnection::SessionDescription aliceSdp;
   {
      PeerConnectionHandle h;
      CreateOfferResult evt;
      ASSERT_TRUE(cpcExpectEvent(alice.peerConnEvents,
         "PeerConnectionHandler::onCreateOfferResult",
         45000,
         AlwaysTruePred(), h, evt));
      ASSERT_NE(strlen(evt.sdp.sdpString.c_str()), 0);
      aliceSdp = evt.sdp;
      std::cout << "alice sdp:" << std::endl << resip::Data(aliceSdp.sdpString, aliceSdp.sdpLen) << std::endl;
   }

   ASSERT_EQ(alice.peerConnection->setLocalDescription(alicePeerConn, aliceSdp), kSuccess);

   PeerConnectionHandle bobPeerConn = bob.peerConnection->createPeerConnection();
   bob.peerConnection->setHandler(bobPeerConn, (PeerConnectionHandler*)0xDEADBEEF);
   bob.peerConnection->setDefaultSettings(bobPeerConn, bobSettings);
   CPCAPI2::PeerConnection::MediaInfo bobMediaInfo;
   bobMediaInfo.mediaType = PeerConnection::MediaType_Audio;
   bobMediaInfo.mediaDirection = PeerConnection::MediaDirection_SendRecv;
   bobMediaInfo.mediaEncryptionOptions.mediaEncryptionMode = PeerConnection::MediaEncryptionMode_SRTP_DTLS_Encrypted;
   bobMediaInfo.mediaEncryptionOptions.secureMediaRequired = true;
   ASSERT_EQ(bob.peerConnection->configureMedia(bobPeerConn, bob.peerConnection->createMediaStream(), bobMediaInfo), 0);
   ASSERT_EQ(bob.peerConnection->setRemoteDescription(bobPeerConn, aliceSdp), kSuccess);

   {
      PeerConnectionHandle h;
      SetRemoteSessionDescriptionResult evt;
      ASSERT_TRUE(cpcExpectEvent(bob.peerConnEvents,
         "PeerConnectionHandler::onSetRemoteSessionDescriptionResult",
         10000,
         AlwaysTruePred(), h, evt));
   }

   ASSERT_EQ(bob.peerConnection->createAnswer(bobPeerConn), kSuccess);

   CPCAPI2::PeerConnection::SessionDescription bobSdp;
   {
      PeerConnectionHandle h;
      CreateAnswerResult evt;
      ASSERT_TRUE(cpcExpectEvent(bob.peerConnEvents,
         "PeerConnectionHandler::onCreateAnswerResult",
         30000,
         AlwaysTruePred(), h, evt));
      ASSERT_NE(strlen(evt.sdp.sdpString.c_str()), 0);
      bobSdp = evt.sdp;
      std::cout << "bob sdp:" << std::endl << resip::Data(bobSdp.sdpString, bobSdp.sdpLen) << std::endl;
   }
   ASSERT_EQ(bob.peerConnection->setLocalDescription(bobPeerConn, bobSdp), kSuccess);

   ASSERT_EQ(alice.peerConnection->setRemoteDescription(alicePeerConn, bobSdp), kSuccess);

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   ASSERT_EQ(bob.peerConnection->close(bobPeerConn), kSuccess);
   ASSERT_EQ(alice.peerConnection->close(alicePeerConn), kSuccess);

   resip::DnsStub::setDnsTimeoutAndTries(0, 0);


   //
   //cpc::vector<AudioCodecInfo> codecs;
   //std::unique_ptr<TestAudioHandler> testAudioHandler(new TestAudioHandler(codecs));
   //alice.audio->setHandler(testAudioHandler.get());

   //ASSERT_EQ(alice.audio->queryCodecList(), kSuccess);
   //alice.media->process(MediaManager::kBlockingModeInfinite);
   //ASSERT_NE(codecs.size(), 0);

   //alice.audio->setCodecEnabled(codecs[0].id, false);
   //codecs.clear();
   //ASSERT_EQ(alice.audio->queryCodecList(), kSuccess);
   //alice.media->process(MediaManager::kBlockingModeInfinite);

   //ASSERT_NE(codecs.size(), 0);
   //ASSERT_EQ(codecs[0].enabled, false);
}

TEST_F(PeerConnectionTest, BasicAudioCall_RtcpMuxEnabledOnAnswererSide) {

   resip::DnsStub::setDnsTimeoutAndTries(10, 10);

   TestAccount alice("alice");
   TestAccount bob("bob");

   PeerConnectionSettings aliceSettings;
   aliceSettings.certAor = alice.config.settings.username + "@" + alice.config.settings.domain;
   aliceSettings.natTraversalMode = PeerConnectionSettings::NatTraversalMode_ICE;
   aliceSettings.natTraversalServerType = PeerConnectionSettings::NatTraversalServerType_StunOnly;
   aliceSettings.natTraversalServerHostname = "stun.counterpath.com";
   aliceSettings.natTraversalServerPort = 3478;
   aliceSettings.sessionName = "peerconnectiontest";
   aliceSettings.rtcpMux = false;
   PeerConnectionSettings bobSettings;
   bobSettings = aliceSettings;
   bobSettings.rtcpMux = true;
   bobSettings.certAor = bob.config.settings.username + "@" + bob.config.settings.domain;

   PeerConnectionHandle alicePeerConn = alice.peerConnection->createPeerConnection();
   alice.peerConnection->setHandler(alicePeerConn, (PeerConnectionHandler*)0xDEADBEEF);
   alice.peerConnection->setDefaultSettings(alicePeerConn, aliceSettings);
   CPCAPI2::PeerConnection::MediaInfo mediaInfo;
   mediaInfo.mediaType = PeerConnection::MediaType_Audio;
   mediaInfo.mediaDirection = PeerConnection::MediaDirection_SendRecv;
   mediaInfo.mediaEncryptionOptions.mediaEncryptionMode = PeerConnection::MediaEncryptionMode_SRTP_DTLS_Encrypted;
   mediaInfo.mediaEncryptionOptions.secureMediaRequired = true;
   alice.peerConnection->configureMedia(alicePeerConn, alice.peerConnection->createMediaStream(), mediaInfo);
   ASSERT_EQ(alice.peerConnection->createOffer(alicePeerConn), kSuccess);

   CPCAPI2::PeerConnection::SessionDescription aliceSdp;
   {
      PeerConnectionHandle h;
      CreateOfferResult evt;
      ASSERT_TRUE(cpcExpectEvent(alice.peerConnEvents,
         "PeerConnectionHandler::onCreateOfferResult",
         45000,
         AlwaysTruePred(), h, evt));
      ASSERT_NE(strlen(evt.sdp.sdpString.c_str()), 0);
      aliceSdp = evt.sdp;
      std::cout << "alice sdp:" << std::endl << resip::Data(aliceSdp.sdpString, aliceSdp.sdpLen) << std::endl;
   }

   ASSERT_EQ(alice.peerConnection->setLocalDescription(alicePeerConn, aliceSdp), kSuccess);

   PeerConnectionHandle bobPeerConn = bob.peerConnection->createPeerConnection();
   bob.peerConnection->setHandler(bobPeerConn, (PeerConnectionHandler*)0xDEADBEEF);
   bob.peerConnection->setDefaultSettings(bobPeerConn, bobSettings);
   CPCAPI2::PeerConnection::MediaInfo bobMediaInfo;
   bobMediaInfo.mediaType = PeerConnection::MediaType_Audio;
   bobMediaInfo.mediaDirection = PeerConnection::MediaDirection_SendRecv;
   bobMediaInfo.mediaEncryptionOptions.mediaEncryptionMode = PeerConnection::MediaEncryptionMode_SRTP_DTLS_Encrypted;
   bobMediaInfo.mediaEncryptionOptions.secureMediaRequired = true;
   ASSERT_EQ(bob.peerConnection->configureMedia(bobPeerConn, bob.peerConnection->createMediaStream(), bobMediaInfo), 0);
   ASSERT_EQ(bob.peerConnection->setRemoteDescription(bobPeerConn, aliceSdp), kSuccess);

   {
      PeerConnectionHandle h;
      SetRemoteSessionDescriptionResult evt;
      ASSERT_TRUE(cpcExpectEvent(bob.peerConnEvents,
         "PeerConnectionHandler::onSetRemoteSessionDescriptionResult",
         10000,
         AlwaysTruePred(), h, evt));
   }

   ASSERT_EQ(bob.peerConnection->createAnswer(bobPeerConn), kSuccess);

   CPCAPI2::PeerConnection::SessionDescription bobSdp;
   {
      PeerConnectionHandle h;
      CreateAnswerResult evt;
      ASSERT_TRUE(cpcExpectEvent(bob.peerConnEvents,
         "PeerConnectionHandler::onCreateAnswerResult",
         30000,
         AlwaysTruePred(), h, evt));
      ASSERT_NE(strlen(evt.sdp.sdpString.c_str()), 0);
      bobSdp = evt.sdp;
      std::cout << "bob sdp:" << std::endl << resip::Data(bobSdp.sdpString, bobSdp.sdpLen) << std::endl;
   }
   ASSERT_EQ(bob.peerConnection->setLocalDescription(bobPeerConn, bobSdp), kSuccess);

   ASSERT_EQ(alice.peerConnection->setRemoteDescription(alicePeerConn, bobSdp), kSuccess);

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   ASSERT_EQ(bob.peerConnection->close(bobPeerConn), kSuccess);
   ASSERT_EQ(alice.peerConnection->close(alicePeerConn), kSuccess);

   resip::DnsStub::setDnsTimeoutAndTries(0, 0);


   //
   //cpc::vector<AudioCodecInfo> codecs;
   //std::unique_ptr<TestAudioHandler> testAudioHandler(new TestAudioHandler(codecs));
   //alice.audio->setHandler(testAudioHandler.get());

   //ASSERT_EQ(alice.audio->queryCodecList(), kSuccess);
   //alice.media->process(MediaManager::kBlockingModeInfinite);
   //ASSERT_NE(codecs.size(), 0);

   //alice.audio->setCodecEnabled(codecs[0].id, false);
   //codecs.clear();
   //ASSERT_EQ(alice.audio->queryCodecList(), kSuccess);
   //alice.media->process(MediaManager::kBlockingModeInfinite);

   //ASSERT_NE(codecs.size(), 0);
   //ASSERT_EQ(codecs[0].enabled, false);
}

TEST_F(PeerConnectionTest, BasicAudioCall_ICE_TriggeredChecks) {

   resip::DnsStub::setDnsTimeoutAndTries(10, 10);

   TestAccount alice("alice");
   TestAccount bob("bob");

   PeerConnectionSettings aliceSettings;
   aliceSettings.certAor = alice.config.settings.username + "@" + alice.config.settings.domain;
   aliceSettings.natTraversalMode = PeerConnectionSettings::NatTraversalMode_ICE;
   aliceSettings.natTraversalServerType = PeerConnectionSettings::NatTraversalServerType_StunOnly;
   aliceSettings.natTraversalServerHostname = "stun.counterpath.com";
   aliceSettings.natTraversalServerPort = 3478;
   aliceSettings.sessionName = "peerconnectiontest";
   aliceSettings.useRandomPortsInIceCandidates = true;
   PeerConnectionSettings bobSettings;
   bobSettings = aliceSettings;
   bobSettings.useRandomPortsInIceCandidates = false;
   bobSettings.certAor = bob.config.settings.username + "@" + bob.config.settings.domain;

   PeerConnectionHandle alicePeerConn = alice.peerConnection->createPeerConnection();
   alice.peerConnection->setHandler(alicePeerConn, (PeerConnectionHandler*)0xDEADBEEF);
   alice.peerConnection->setDefaultSettings(alicePeerConn, aliceSettings);
   CPCAPI2::PeerConnection::MediaInfo mediaInfo;
   mediaInfo.mediaType = PeerConnection::MediaType_Audio;
   mediaInfo.mediaDirection = PeerConnection::MediaDirection_SendRecv;
   mediaInfo.mediaEncryptionOptions.mediaEncryptionMode = PeerConnection::MediaEncryptionMode_SRTP_DTLS_Encrypted;
   mediaInfo.mediaEncryptionOptions.secureMediaRequired = true;
   alice.peerConnection->configureMedia(alicePeerConn, alice.peerConnection->createMediaStream(), mediaInfo);
   ASSERT_EQ(alice.peerConnection->createOffer(alicePeerConn), kSuccess);

   CPCAPI2::PeerConnection::SessionDescription aliceSdp;
   {
      PeerConnectionHandle h;
      CreateOfferResult evt;
      ASSERT_TRUE(cpcExpectEvent(alice.peerConnEvents,
         "PeerConnectionHandler::onCreateOfferResult",
         45000,
         AlwaysTruePred(), h, evt));
      ASSERT_NE(strlen(evt.sdp.sdpString.c_str()), 0);
      aliceSdp = evt.sdp;
      std::cout << "alice sdp:" << std::endl << resip::Data(aliceSdp.sdpString, aliceSdp.sdpLen) << std::endl;
   }

   ASSERT_EQ(alice.peerConnection->setLocalDescription(alicePeerConn, aliceSdp), kSuccess);

   PeerConnectionHandle bobPeerConn = bob.peerConnection->createPeerConnection();
   bob.peerConnection->setHandler(bobPeerConn, (PeerConnectionHandler*)0xDEADBEEF);
   bob.peerConnection->setDefaultSettings(bobPeerConn, bobSettings);
   CPCAPI2::PeerConnection::MediaInfo bobMediaInfo;
   bobMediaInfo.mediaType = PeerConnection::MediaType_Audio;
   bobMediaInfo.mediaDirection = PeerConnection::MediaDirection_SendRecv;
   bobMediaInfo.mediaEncryptionOptions.mediaEncryptionMode = PeerConnection::MediaEncryptionMode_SRTP_DTLS_Encrypted;
   bobMediaInfo.mediaEncryptionOptions.secureMediaRequired = true;
   ASSERT_EQ(bob.peerConnection->configureMedia(bobPeerConn, bob.peerConnection->createMediaStream(), bobMediaInfo), 0);
   ASSERT_EQ(bob.peerConnection->setRemoteDescription(bobPeerConn, aliceSdp), kSuccess);

   {
      PeerConnectionHandle h;
      SetRemoteSessionDescriptionResult evt;
      ASSERT_TRUE(cpcExpectEvent(bob.peerConnEvents,
         "PeerConnectionHandler::onSetRemoteSessionDescriptionResult",
         10000,
         AlwaysTruePred(), h, evt));
   }

   ASSERT_EQ(bob.peerConnection->createAnswer(bobPeerConn), kSuccess);

   CPCAPI2::PeerConnection::SessionDescription bobSdp;
   {
      PeerConnectionHandle h;
      CreateAnswerResult evt;
      ASSERT_TRUE(cpcExpectEvent(bob.peerConnEvents,
         "PeerConnectionHandler::onCreateAnswerResult",
         30000,
         AlwaysTruePred(), h, evt));
      ASSERT_NE(strlen(evt.sdp.sdpString.c_str()), 0);
      bobSdp = evt.sdp;
      std::cout << "bob sdp:" << std::endl << resip::Data(bobSdp.sdpString, bobSdp.sdpLen) << std::endl;
   }
   ASSERT_EQ(bob.peerConnection->setLocalDescription(bobPeerConn, bobSdp), kSuccess);

   ASSERT_EQ(alice.peerConnection->setRemoteDescription(alicePeerConn, bobSdp), kSuccess);

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   ASSERT_EQ(bob.peerConnection->close(bobPeerConn), kSuccess);
   ASSERT_EQ(alice.peerConnection->close(alicePeerConn), kSuccess);

   resip::DnsStub::setDnsTimeoutAndTries(0, 0);

   //
   //cpc::vector<AudioCodecInfo> codecs;
   //std::unique_ptr<TestAudioHandler> testAudioHandler(new TestAudioHandler(codecs));
   //alice.audio->setHandler(testAudioHandler.get());

   //ASSERT_EQ(alice.audio->queryCodecList(), kSuccess);
   //alice.media->process(MediaManager::kBlockingModeInfinite);
   //ASSERT_NE(codecs.size(), 0);

   //alice.audio->setCodecEnabled(codecs[0].id, false);
   //codecs.clear();
   //ASSERT_EQ(alice.audio->queryCodecList(), kSuccess);
   //alice.media->process(MediaManager::kBlockingModeInfinite);

   //ASSERT_NE(codecs.size(), 0);
   //ASSERT_EQ(codecs[0].enabled, false);
}

/*
"v=0"
"o=peerconnectiontest 3988620238 0 IN IP4 **********"
"s=peerconnectiontest"
"t=0 0"
"m=video 56156 RTP/AVPF 126 127 115 34"
"c=IN IP4 **********"
"a=rtpmap:126 H264/90000"
"a=fmtp:126 profile-level-id=42801f;packetization-mode=1"
"a=rtpmap:127 H264/90000"
"a=fmtp:127 profile-level-id=42801f;packetization-mode=0"
"a=rtpmap:115 H263-1998/90000"
"a=fmtp:115 VGA=2;CIF=1;QCIF=1;CIF4=2;I=1;J=1;T=1"
"a=rtpmap:34 H263/90000"
"a=fmtp:34 CIF=2;QCIF=2;VGA=2;CIF4=2"
"a=mid:video"
"a=rtcp:56157 IN IP4 **********"
"a=rtcp-fb:* nack pli"
"a=recvonly"
*/
#if 0
TEST_F(PeerConnectionTest, VideoScreenShare) {

   TestAccount alice("alice");
   TestAccount bob("bob");

   // enable H.264
   TestVideoHandler videoHandler;
   alice.video->setHandler(&videoHandler);
   alice.video->queryCodecList();
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);
   CPCAPI2::Media::H264Config h264config;
   h264config.enableNonInterleavedMode = false;
   alice.video->setCodecConfig(h264config);
   bob.video->setHandler(&videoHandler);
   bob.video->queryCodecList();
   bob.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);

   alice.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   bob.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   alice.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   bob.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

#if _WIN32
   HWND hwndAliceCapture = NULL;
   ASSERT_EQ(0, ViECreateWindow(hwndAliceCapture, 0, 0, 352, 288, "Alice (capture)"));
   alice.video->setCaptureDevice(CPCAPI2::Media::kScreenCaptureDeviceId);
   alice.video->startCapture();
   alice.video->setLocalVideoRenderTarget(hwndAliceCapture);

   HWND hwndAliceRemote = NULL;
   ASSERT_EQ(0, ViECreateWindow(hwndAliceRemote, 356, 0, 352, 288, "Alice (incoming)"));
   alice.video->setIncomingVideoRenderTarget(hwndAliceRemote);

   HWND hwndBobCapture = NULL;
   ASSERT_EQ(0, ViECreateWindow(hwndBobCapture, 0, 292, 352, 288, "Bob (capture)"));
   bob.video->startCapture();
   bob.video->setLocalVideoRenderTarget(hwndBobCapture);

   HWND hwndBobRemote = NULL;
   ASSERT_EQ(0, ViECreateWindow(hwndBobRemote, 356, 292, 352, 288, "Bob (incoming)"));
   bob.video->setIncomingVideoRenderTarget(hwndBobRemote);
#else // _WIN32
   alice.video->startCapture();
   bob.video->startCapture();
#endif

   PeerConnectionSettings aliceSettings;
   aliceSettings.certAor = alice.config.settings.username + "@" + alice.config.settings.domain;
   aliceSettings.natTraversalMode = PeerConnectionSettings::NatTraversalMode_None;
   aliceSettings.natTraversalServerHostname = "stun.counterpath.com";
   aliceSettings.natTraversalServerPort = 3478;
   aliceSettings.secureMediaMode = PeerConnectionSettings::SecureMediaMode_None;
   aliceSettings.secureMediaRequired = false;
   aliceSettings.sessionName = "peerconnectiontest";
   PeerConnectionSettings bobSettings;
   bobSettings = aliceSettings;
   bobSettings.certAor = bob.config.settings.username + "@" + bob.config.settings.domain;

   PeerConnectionHandle alicePeerConn = alice.peerConnection->createPeerConnection();
   alice.peerConnection->setHandler(alicePeerConn, (PeerConnectionHandler*)0xDEADBEEF);
   alice.peerConnection->setDefaultSettings(alicePeerConn, aliceSettings);
   RtpTransceiverInit aliceVideoConfig;
   aliceVideoConfig.receive = false;
   aliceVideoConfig.send = true;
   ASSERT_GT(alice.peerConnection->addTransceiver(alicePeerConn, PeerConnection::MediaType_Video, aliceVideoConfig), 0);
   ASSERT_EQ(alice.peerConnection->createOffer(alicePeerConn), kSuccess);

   CPCAPI2::PeerConnection::SessionDescription aliceSdp;
   {
      PeerConnectionHandle h;
      CreateOfferResult evt;
      ASSERT_TRUE(cpcExpectEvent(alice.peerConnEvents,
         "PeerConnectionHandler::onCreateOfferResult",
         45000,
         AlwaysTruePred(), h, evt));
      ASSERT_NE(strlen(evt.sdp.sdpString), 0);
      aliceSdp = evt.sdp;
      std::cout << "alice sdp:" << std::endl << resip::Data(aliceSdp.sdpString, aliceSdp.sdpLen) << std::endl;
   }

   ASSERT_EQ(alice.peerConnection->setLocalDescription(alicePeerConn, aliceSdp), kSuccess);

   //PeerConnectionHandle bobPeerConn = bob.peerConnection->createPeerConnection();
   //bob.peerConnection->setHandler(bobPeerConn, (PeerConnectionHandler*)0xDEADBEEF);
   //bob.peerConnection->setDefaultSettings(bobPeerConn, bobSettings);
   //MediaStreamConfig bobVideoConfig = { MediaStreamConfig::MediaType_Video, MediaStreamConfig::MediaDirection_RecvOnly };
   //ASSERT_EQ(bob.peerConnection->configureStream(bobPeerConn, "video", bobVideoConfig), kSuccess);
   //ASSERT_EQ(bob.peerConnection->setRemoteDescription(bobPeerConn, aliceSdp), kSuccess);
   //ASSERT_EQ(bob.peerConnection->createAnswer(bobPeerConn), kSuccess);

   CPCAPI2::PeerConnection::SessionDescription bobSdp;
   {
      /*
      PeerConnectionHandle h;
      LocalSessionDescriptionCreatedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.peerConnEvents,
         "PeerConnectionHandler::onLocalSessionDescriptionCreated",
         10000,
         AlwaysTruePred(), h, evt));
      ASSERT_NE(strlen(evt.sdp.sdpString), 0);
      bobSdp = evt.sdp;
      */
      char* screenShareSdp = "v=0\r\n"
         "o=peerconnectiontest 3988620238 0 IN IP4 **********6\r\n"
         "s=peerconnectiontest\r\n"
         "t=0 0\r\n"
         "m=video 2114 RTP/AVPF 127\r\n"
         "c=IN IP4 **********6\r\n"
         "a=rtpmap:127 H264/90000\r\n"
         "a=fmtp:127 profile-level-id=42801f;packetization-mode=0\r\n"
         "a=mid:video\r\n"
         "a=rtcp:2114 IN IP4 **********6\r\n"
         "a=rtcp-fb:* nack pli\r\n"
         "a=recvonly\r\n";
      memcpy(bobSdp.sdpString, screenShareSdp, strlen(screenShareSdp));
      bobSdp.sdpLen = strlen(screenShareSdp);
      bobSdp.sdpType = SessionDescription::SessionDescriptionType_Answer;
      std::cout << "bob sdp:" << std::endl << resip::Data(bobSdp.sdpString, bobSdp.sdpLen) << std::endl;
#if _WIN32
      OutputDebugStringA(resip::Data(bobSdp.sdpString, bobSdp.sdpLen).c_str());
#endif
   }
   //ASSERT_EQ(bob.peerConnection->setLocalDescription(bobPeerConn, bobSdp), kSuccess);

   ASSERT_EQ(alice.peerConnection->setRemoteDescription(alicePeerConn, bobSdp), kSuccess);

   std::this_thread::sleep_for(std::chrono::milliseconds(90000));

   //ASSERT_EQ(bob.peerConnection->close(bobPeerConn), kSuccess);
   ASSERT_EQ(alice.peerConnection->close(alicePeerConn), kSuccess);
}
#endif

TEST_F(PeerConnectionTest, VideoOnlyCall) {

   resip::DnsStub::setDnsTimeoutAndTries(10, 10);

   TestAccount alice("alice");
   TestAccount bob("bob");

   // enable H.264
   alice.video->queryCodecList();
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);
   bob.video->queryCodecList();
   bob.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);

   alice.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   bob.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   alice.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   bob.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

#if _WIN32
   HWND hwndAliceCapture = NULL;
   ASSERT_EQ(0, ViECreateWindow(hwndAliceCapture, 0, 0, 352, 288, "Alice (capture)"));
   alice.video->startCapture();
   alice.video->setLocalVideoRenderTarget(hwndAliceCapture);

   HWND hwndAliceRemote = NULL;
   ASSERT_EQ(0, ViECreateWindow(hwndAliceRemote, 356, 0, 352, 288, "Alice (incoming)"));
   alice.video->setIncomingVideoRenderTarget(hwndAliceRemote);

   HWND hwndBobCapture = NULL;
   ASSERT_EQ(0, ViECreateWindow(hwndBobCapture, 0, 292, 352, 288, "Bob (capture)"));
   bob.video->startCapture();
   bob.video->setLocalVideoRenderTarget(hwndBobCapture);

   HWND hwndBobRemote = NULL;
   ASSERT_EQ(0, ViECreateWindow(hwndBobRemote, 356, 292, 352, 288, "Bob (incoming)"));
   bob.video->setIncomingVideoRenderTarget(hwndBobRemote);
#else // _WIN32
   alice.video->startCapture();
   bob.video->startCapture();
#endif

   PeerConnectionSettings aliceSettings;
   aliceSettings.certAor = alice.config.settings.username + "@" + alice.config.settings.domain;
   aliceSettings.natTraversalMode = PeerConnectionSettings::NatTraversalMode_None;
   aliceSettings.natTraversalServerHostname = "stun.counterpath.com";
   aliceSettings.natTraversalServerPort = 3478;
   aliceSettings.sessionName = "peerconnectiontest";
   PeerConnectionSettings bobSettings;
   bobSettings = aliceSettings;
   bobSettings.certAor = bob.config.settings.username + "@" + bob.config.settings.domain;

   PeerConnectionHandle alicePeerConn = alice.peerConnection->createPeerConnection();
   alice.peerConnection->setHandler(alicePeerConn, (PeerConnectionHandler*)0xDEADBEEF);
   alice.peerConnection->setDefaultSettings(alicePeerConn, aliceSettings);
   CPCAPI2::PeerConnection::MediaInfo mediaInfo;
   mediaInfo.mediaType = PeerConnection::MediaType_Video;
   mediaInfo.mediaDirection = PeerConnection::MediaDirection_SendRecv;
   mediaInfo.mediaEncryptionOptions.mediaEncryptionMode = PeerConnection::MediaEncryptionMode_Unencrypted;
   mediaInfo.mediaEncryptionOptions.secureMediaRequired = false;
   ASSERT_EQ(alice.peerConnection->configureMedia(alicePeerConn, alice.peerConnection->createMediaStream(), mediaInfo), 0);
   ASSERT_EQ(alice.peerConnection->createOffer(alicePeerConn), kSuccess);

   CPCAPI2::PeerConnection::SessionDescription aliceSdp;
   {
      PeerConnectionHandle h;
      CreateOfferResult evt;
      ASSERT_TRUE(cpcExpectEvent(alice.peerConnEvents,
         "PeerConnectionHandler::onCreateOfferResult",
         45000,
         AlwaysTruePred(), h, evt));
      ASSERT_NE(strlen(evt.sdp.sdpString.c_str()), 0);
      aliceSdp = evt.sdp;
      std::cout << "alice sdp:" << std::endl << resip::Data(aliceSdp.sdpString, aliceSdp.sdpLen) << std::endl;
   }

   ASSERT_EQ(alice.peerConnection->setLocalDescription(alicePeerConn, aliceSdp), kSuccess);
   //{
   //   PeerConnectionHandle h;
   //   SetLocalSessionDescriptionResult evt;
   //   ASSERT_TRUE(cpcExpectEvent(alice.peerConnEvents,
   //      "PeerConnectionHandler::onSetLocalSessionDescriptionResult",
   //      10000,
   //      AlwaysTruePred(), h, evt));
   //}

   PeerConnectionHandle bobPeerConn = bob.peerConnection->createPeerConnection();
   bob.peerConnection->setHandler(bobPeerConn, (PeerConnectionHandler*)0xDEADBEEF);
   bob.peerConnection->setDefaultSettings(bobPeerConn, bobSettings);
   CPCAPI2::PeerConnection::MediaInfo bobMediaInfo;
   bobMediaInfo.mediaType = PeerConnection::MediaType_Video;
   bobMediaInfo.mediaDirection = PeerConnection::MediaDirection_SendRecv;
   bobMediaInfo.mediaEncryptionOptions.mediaEncryptionMode = PeerConnection::MediaEncryptionMode_Unencrypted;
   bobMediaInfo.mediaEncryptionOptions.secureMediaRequired = false;
   ASSERT_EQ(bob.peerConnection->configureMedia(bobPeerConn, bob.peerConnection->createMediaStream(), bobMediaInfo), 0);
   ASSERT_EQ(bob.peerConnection->setRemoteDescription(bobPeerConn, aliceSdp), kSuccess);
   {
      PeerConnectionHandle h;
      SetRemoteSessionDescriptionResult evt;
      ASSERT_TRUE(cpcExpectEvent(bob.peerConnEvents,
         "PeerConnectionHandler::onSetRemoteSessionDescriptionResult",
         10000,
         AlwaysTruePred(), h, evt));
   }

   ASSERT_EQ(bob.peerConnection->createAnswer(bobPeerConn), kSuccess);

   CPCAPI2::PeerConnection::SessionDescription bobSdp;
   {
      PeerConnectionHandle h;
      CreateAnswerResult evt;
      ASSERT_TRUE(cpcExpectEvent(bob.peerConnEvents,
         "PeerConnectionHandler::onCreateAnswerResult",
         10000,
         AlwaysTruePred(), h, evt));
      ASSERT_NE(strlen(evt.sdp.sdpString.c_str()), 0);
      bobSdp = evt.sdp;
      std::cout << "bob sdp:" << std::endl << resip::Data(bobSdp.sdpString, bobSdp.sdpLen) << std::endl;
#if _WIN32
      OutputDebugStringA(resip::Data(bobSdp.sdpString, bobSdp.sdpLen).c_str());
#endif
   }
   ASSERT_EQ(bob.peerConnection->setLocalDescription(bobPeerConn, bobSdp), kSuccess);

   //std::this_thread::sleep_for(std::chrono::milliseconds(500));

   ASSERT_EQ(alice.peerConnection->setRemoteDescription(alicePeerConn, bobSdp), kSuccess);

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   ASSERT_EQ(bob.peerConnection->close(bobPeerConn), kSuccess);
   ASSERT_EQ(alice.peerConnection->close(alicePeerConn), kSuccess);

#if _WIN32
   alice.video->stopCapture();
   alice.video->setLocalVideoRenderTarget(NULL);
   DestroyWindow(hwndAliceCapture);
   DestroyWindow(hwndAliceRemote);
   bob.video->stopCapture();
   bob.video->setLocalVideoRenderTarget(NULL);
   DestroyWindow(hwndBobCapture);
   DestroyWindow(hwndBobRemote);
#endif // _WIN32

   resip::DnsStub::setDnsTimeoutAndTries(0, 0);

}

TEST_F(PeerConnectionTest, VideoOnlyCall_ICE_DTLS) {

   resip::DnsStub::setDnsTimeoutAndTries(10, 10);

   TestAccount alice("alice");
   TestAccount bob("bob");

   // enable H.264
   alice.video->queryCodecList();
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);
   
   bob.video->queryCodecList();
   bob.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);

   alice.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   bob.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   alice.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   bob.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

#if _WIN32
   HWND hwndAliceCapture = NULL;
   ASSERT_EQ(0, ViECreateWindow(hwndAliceCapture, 0, 0, 352, 288, "Alice (capture)"));
   alice.video->startCapture();
   alice.video->setLocalVideoRenderTarget(hwndAliceCapture);

   HWND hwndAliceRemote = NULL;
   ASSERT_EQ(0, ViECreateWindow(hwndAliceRemote, 356, 0, 352, 288, "Alice (incoming)"));
   alice.video->setIncomingVideoRenderTarget(hwndAliceRemote);

   HWND hwndBobCapture = NULL;
   ASSERT_EQ(0, ViECreateWindow(hwndBobCapture, 0, 292, 352, 288, "Bob (capture)"));
   bob.video->startCapture();
   bob.video->setLocalVideoRenderTarget(hwndBobCapture);

   HWND hwndBobRemote = NULL;
   ASSERT_EQ(0, ViECreateWindow(hwndBobRemote, 356, 292, 352, 288, "Bob (incoming)"));
   bob.video->setIncomingVideoRenderTarget(hwndBobRemote);
#else // _WIN32
   alice.video->startCapture();
   bob.video->startCapture();
#endif

   PeerConnectionSettings aliceSettings;
   aliceSettings.certAor = alice.config.settings.username + "@" + alice.config.settings.domain;
   aliceSettings.natTraversalMode = PeerConnectionSettings::NatTraversalMode_ICE;
   aliceSettings.natTraversalServerType = PeerConnectionSettings::NatTraversalServerType_StunOnly;
   aliceSettings.natTraversalServerHostname = "stun.counterpath.com";
   aliceSettings.natTraversalServerPort = 3478;
   aliceSettings.sessionName = "peerconnectiontest";
   PeerConnectionSettings bobSettings;
   bobSettings = aliceSettings;
   bobSettings.certAor = bob.config.settings.username + "@" + bob.config.settings.domain;

   PeerConnectionHandle alicePeerConn = alice.peerConnection->createPeerConnection();
   alice.peerConnection->setHandler(alicePeerConn, (PeerConnectionHandler*)0xDEADBEEF);
   alice.peerConnection->setDefaultSettings(alicePeerConn, aliceSettings);
   CPCAPI2::PeerConnection::MediaInfo mediaInfo;
   mediaInfo.mediaType = PeerConnection::MediaType_Video;
   mediaInfo.mediaDirection = PeerConnection::MediaDirection_SendRecv;
   mediaInfo.mediaEncryptionOptions.mediaEncryptionMode = PeerConnection::MediaEncryptionMode_SRTP_DTLS_Encrypted;
   mediaInfo.mediaEncryptionOptions.secureMediaRequired = true;
   ASSERT_EQ(alice.peerConnection->configureMedia(alicePeerConn, alice.peerConnection->createMediaStream(), mediaInfo), 0);
   ASSERT_EQ(alice.peerConnection->createOffer(alicePeerConn), kSuccess);

   CPCAPI2::PeerConnection::SessionDescription aliceSdp;
   {
      PeerConnectionHandle h;
      CreateOfferResult evt;
      ASSERT_TRUE(cpcExpectEvent(alice.peerConnEvents,
         "PeerConnectionHandler::onCreateOfferResult",
         45000,
         AlwaysTruePred(), h, evt));
      ASSERT_NE(strlen(evt.sdp.sdpString.c_str()), 0);
      aliceSdp = evt.sdp;
      std::cout << "alice sdp:" << std::endl << resip::Data(aliceSdp.sdpString, aliceSdp.sdpLen) << std::endl;
   }

   ASSERT_EQ(alice.peerConnection->setLocalDescription(alicePeerConn, aliceSdp), kSuccess);
   //{
   //   PeerConnectionHandle h;
   //   SetLocalSessionDescriptionResult evt;
   //   ASSERT_TRUE(cpcExpectEvent(alice.peerConnEvents,
   //      "PeerConnectionHandler::onSetLocalSessionDescriptionResult",
   //      10000,
   //      AlwaysTruePred(), h, evt));
   //}

   PeerConnectionHandle bobPeerConn = bob.peerConnection->createPeerConnection();
   bob.peerConnection->setHandler(bobPeerConn, (PeerConnectionHandler*)0xDEADBEEF);
   bob.peerConnection->setDefaultSettings(bobPeerConn, bobSettings);
   CPCAPI2::PeerConnection::MediaInfo bobMediaInfo;
   bobMediaInfo.mediaType = PeerConnection::MediaType_Video;
   bobMediaInfo.mediaDirection = PeerConnection::MediaDirection_SendRecv;
   bobMediaInfo.mediaEncryptionOptions.mediaEncryptionMode = PeerConnection::MediaEncryptionMode_SRTP_DTLS_Encrypted;
   bobMediaInfo.mediaEncryptionOptions.secureMediaRequired = true;
   ASSERT_EQ(bob.peerConnection->configureMedia(bobPeerConn, bob.peerConnection->createMediaStream(), bobMediaInfo), 0);
   ASSERT_EQ(bob.peerConnection->setRemoteDescription(bobPeerConn, aliceSdp), kSuccess);
   {
      PeerConnectionHandle h;
      SetRemoteSessionDescriptionResult evt;
      ASSERT_TRUE(cpcExpectEvent(bob.peerConnEvents,
         "PeerConnectionHandler::onSetRemoteSessionDescriptionResult",
         10000,
         AlwaysTruePred(), h, evt));
   }

   ASSERT_EQ(bob.peerConnection->createAnswer(bobPeerConn), kSuccess);

   CPCAPI2::PeerConnection::SessionDescription bobSdp;
   {
      PeerConnectionHandle h;
      CreateAnswerResult evt;
      ASSERT_TRUE(cpcExpectEvent(bob.peerConnEvents,
         "PeerConnectionHandler::onCreateAnswerResult",
         10000,
         AlwaysTruePred(), h, evt));
      ASSERT_NE(strlen(evt.sdp.sdpString.c_str()), 0);
      bobSdp = evt.sdp;
      std::cout << "bob sdp:" << std::endl << resip::Data(bobSdp.sdpString, bobSdp.sdpLen) << std::endl;
#if _WIN32
      OutputDebugStringA(resip::Data(bobSdp.sdpString, bobSdp.sdpLen).c_str());
#endif
   }
   ASSERT_EQ(bob.peerConnection->setLocalDescription(bobPeerConn, bobSdp), kSuccess);

   //std::this_thread::sleep_for(std::chrono::milliseconds(500));

   ASSERT_EQ(alice.peerConnection->setRemoteDescription(alicePeerConn, bobSdp), kSuccess);

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   ASSERT_EQ(bob.peerConnection->close(bobPeerConn), kSuccess);
   ASSERT_EQ(alice.peerConnection->close(alicePeerConn), kSuccess);

#if _WIN32
   alice.video->stopCapture();
   alice.video->setLocalVideoRenderTarget(NULL);
   DestroyWindow(hwndAliceCapture);
   DestroyWindow(hwndAliceRemote);
   bob.video->stopCapture();
   bob.video->setLocalVideoRenderTarget(NULL);
   DestroyWindow(hwndBobCapture);
   DestroyWindow(hwndBobRemote);
#endif // _WIN32

   resip::DnsStub::setDnsTimeoutAndTries(0, 0);


}

TEST_F(PeerConnectionTest, AudioCallForcedCodec) {

   TestAccount alice("alice");
   TestAccount bob("bob");

   PeerConnectionSettings aliceSettings;
   aliceSettings.certAor = alice.config.settings.username + "@" + alice.config.settings.domain;
   aliceSettings.natTraversalMode = PeerConnectionSettings::NatTraversalMode_None;
   //aliceSettings.natTraversalServerHostname = "stun.counterpath.com";
   aliceSettings.natTraversalServerPort = 3478;
   aliceSettings.sessionName = "peerconnectiontest";
   PeerConnectionSettings bobSettings;
   bobSettings = aliceSettings;
   bobSettings.certAor = bob.config.settings.username + "@" + bob.config.settings.domain;

   PeerConnectionHandle alicePeerConn = alice.peerConnection->createPeerConnection();
   alice.peerConnection->setHandler(alicePeerConn, (PeerConnectionHandler*)0xDEADBEEF);
   alice.peerConnection->setDefaultSettings(alicePeerConn, aliceSettings);
   CPCAPI2::PeerConnection::MediaInfo mediaInfo;
   mediaInfo.mediaType = PeerConnection::MediaType_Audio;
   mediaInfo.mediaDirection = PeerConnection::MediaDirection_SendRecv;
      
   // at a global level, only enable G711
   alice.enableOnlyThisCodec("G711 uLaw");
   bob.enableOnlyThisCodec("G711 uLaw");
   
   // force opus on for the peerconnection
   PeerConnection::MediaCodec opus;
   opus.codecPayloadName = "opus";
   opus.codecFrequency = 48000;
   mediaInfo.codecs.push_back(opus);
   
   alice.peerConnection->configureMedia(alicePeerConn, alice.peerConnection->createMediaStream(), mediaInfo);
   ASSERT_EQ(alice.peerConnection->createOffer(alicePeerConn), kSuccess);

   CPCAPI2::PeerConnection::SessionDescription aliceSdp;
   {
      PeerConnectionHandle h;
      CreateOfferResult evt;
      ASSERT_TRUE(cpcExpectEvent(alice.peerConnEvents,
         "PeerConnectionHandler::onCreateOfferResult",
         45000,
         AlwaysTruePred(), h, evt));
      ASSERT_NE(strlen(evt.sdp.sdpString.c_str()), 0);
      aliceSdp = evt.sdp;
      std::cout << "alice sdp:" << std::endl << resip::Data(aliceSdp.sdpString, aliceSdp.sdpLen) << std::endl;
   }

   ASSERT_EQ(alice.peerConnection->setLocalDescription(alicePeerConn, aliceSdp), kSuccess);

   PeerConnectionHandle bobPeerConn = bob.peerConnection->createPeerConnection();
   bob.peerConnection->setHandler(bobPeerConn, (PeerConnectionHandler*)0xDEADBEEF);
   bob.peerConnection->setDefaultSettings(bobPeerConn, bobSettings);
   CPCAPI2::PeerConnection::MediaInfo bobMediaInfo;
   bobMediaInfo.mediaType = PeerConnection::MediaType_Audio;
   bobMediaInfo.mediaDirection = PeerConnection::MediaDirection_SendRecv;
   
   // force opus on for the peerconnection
   bobMediaInfo.codecs.push_back(opus);
   
   ASSERT_EQ(bob.peerConnection->configureMedia(bobPeerConn, bob.peerConnection->createMediaStream(), bobMediaInfo), 0);
   ASSERT_EQ(bob.peerConnection->setRemoteDescription(bobPeerConn, aliceSdp), kSuccess);

   {
      PeerConnectionHandle h;
      SetRemoteSessionDescriptionResult evt;
      ASSERT_TRUE(cpcExpectEvent(bob.peerConnEvents,
         "PeerConnectionHandler::onSetRemoteSessionDescriptionResult",
         10000,
         AlwaysTruePred(), h, evt));
   }

   ASSERT_EQ(bob.peerConnection->createAnswer(bobPeerConn), kSuccess);

   CPCAPI2::PeerConnection::SessionDescription bobSdp;
   {
      PeerConnectionHandle h;
      CreateAnswerResult evt;
      ASSERT_TRUE(cpcExpectEvent(bob.peerConnEvents,
         "PeerConnectionHandler::onCreateAnswerResult",
         30000,
         AlwaysTruePred(), h, evt));
      ASSERT_NE(strlen(evt.sdp.sdpString.c_str()), 0);
      bobSdp = evt.sdp;
      std::cout << "bob sdp:" << std::endl << resip::Data(bobSdp.sdpString, bobSdp.sdpLen) << std::endl;
   }
   ASSERT_EQ(bob.peerConnection->setLocalDescription(bobPeerConn, bobSdp), kSuccess);

   ASSERT_EQ(alice.peerConnection->setRemoteDescription(alicePeerConn, bobSdp), kSuccess);

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   ASSERT_EQ(bob.peerConnection->close(bobPeerConn), kSuccess);
   ASSERT_EQ(alice.peerConnection->close(alicePeerConn), kSuccess);

   //
   //cpc::vector<AudioCodecInfo> codecs;
   //std::unique_ptr<TestAudioHandler> testAudioHandler(new TestAudioHandler(codecs));
   //alice.audio->setHandler(testAudioHandler.get());

   //ASSERT_EQ(alice.audio->queryCodecList(), kSuccess);
   //alice.media->process(MediaManager::kBlockingModeInfinite);
   //ASSERT_NE(codecs.size(), 0);

   //alice.audio->setCodecEnabled(codecs[0].id, false);
   //codecs.clear();
   //ASSERT_EQ(alice.audio->queryCodecList(), kSuccess);
   //alice.media->process(MediaManager::kBlockingModeInfinite);

   //ASSERT_NE(codecs.size(), 0);
   //ASSERT_EQ(codecs[0].enabled, false);
}

}  // namespace

#endif // CPCAPI2_PEER_CONNECTION_MODULE
