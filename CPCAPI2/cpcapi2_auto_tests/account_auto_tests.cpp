#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#include "cpcapi2_test_fixture.h"
#include "test_account_events.h"
#include "test_call_events.h"
#include "test_events.h"
#include "vccs_test_harness/VccsTestHarness.h"
#include "test_framework/android_bg_bookkeeper.h"
#include "impl/phone/BackgroundManager.h"

#include "impl/account/SipAccountManagerInternal.h"
#include "impl/account/SipAccountHandlerInternal.h"
#include "impl/account/SipAccountAwareFeature.h"
#include "impl/account/CPMessageDecorator.h"
#include "resip/stack/NameAddr.hxx"
#include "repro/Proxy.hxx"
#include "resip/stack/Helper.hxx"
#include "resip/stack/SipMessage.hxx"

#include "impl/util/BrandingHelper.h"
#include "dummy_tcplistener.h"

#include <repro/ReproRunner.hxx>

#include <vector>
#include <fstream>

using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::SipEvent;

namespace {

class AccountModuleTest : public CpcapiAutoTest
{
public:
   AccountModuleTest() {}
   virtual ~AccountModuleTest() {}

   void verifyViaContactHeaderPopulatedWithPort(TestAccount& alice, const int port, const bool exists);
   void verifyReleaseBindings(TestAccount& alice, const int accountEnableCount, const int expectRelease, const int networkChange);
};

repro::ReproRunner* runRepro(cpc::string config)
{
   repro::ReproRunner* res = new repro::ReproRunner();
   const char* const reproArgs[] = { "" };
   resip::Data configFile = (TestEnvironmentConfig::testResourcePath() + config).c_str();
   res->run(1, reproArgs, configFile);
   return res;
}

bool runOriginalRepro()
{
   const char* const reproArgs[] = { "" };
   resip::Data configFile = (TestEnvironmentConfig::testResourcePath() + "repro.config").c_str();
   return ReproHolder::instance()->run(1, reproArgs, configFile);
}

#ifndef ANDROID
TEST_F(AccountModuleTest, AccountEnableDisableDefault)
{
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_5060 = runRepro("repro_5060.config");

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.outboundProxy = "127.0.0.1";
   alice.enable();
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   delete repro_5060;
   runOriginalRepro();
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
}
#endif

TEST_F(AccountModuleTest, AccountEnableDisableShutdown) {
   TestAccount alice("alice", Account_NoInit, false);

   alice.init();
   alice.enable();

   // we want to let the TestAccount object measure SDK shutdown time; timing the call
   // here to TestAccount::shutdown isn't accurate because it also includes auto test cleanup outside the SDK
   alice.shutdown(3);
   if (HasFatalFailure())
   {
      // else further cleanup will crash
      abort();
   }
   ASSERT_FALSE(alice.didShutdownTimeout()) << "SDK should not take this long to shutdown against repro running locally";
}

// Using the decorator handler instead of the adornment handler, as the contact and via headers are
// not yet populated when the adornment callbacks are triggered.
class MySipAccountDecoratorHandler : public SipAccountMessageDecoratorHandler
{
public:
   MySipAccountDecoratorHandler() {}
   virtual ~MySipAccountDecoratorHandler() {}

   std::list<std::string> messages;
   std::mutex messageQueueMutex;

   std::list<SipAccountMessageDecoratedBindingReleasedEvent> bindingReleaseEvents;
   std::mutex bindingReleaseMutex;

   virtual int onMessageDecorated(SipAccountHandle account, const SipAccountMessageDecoratedEvent& args) OVERRIDE
   {
      if (args.request && (args.method == "REGISTER"))
      {
         safeCout("MySipAccountDecoratorHandler::onMessageDecorated(): account: " << account << " received REGISTER message: " << args.message.c_str());
         std::unique_lock<std::mutex> alock(messageQueueMutex);
         messages.push_back(args.message.c_str());
      }
      return kSuccess;
   }

   virtual int onBindingReleased(SipAccountHandle account, const SipAccountMessageDecoratedBindingReleasedEvent& args) OVERRIDE
   {
      std::stringstream ss;
      ss << std::endl;
      for (cpc::vector<cpc::string>::const_iterator i = args.releasedBindings.begin(); i != args.releasedBindings.end(); ++i)
      {
         ss << (*i) << std::endl;
      }
      safeCout("MySipAccountDecoratorHandler::onBindingReleased(): account: " << account << " released bindings: " << std::endl << ss.str().c_str());
      std::unique_lock<std::mutex> alock(bindingReleaseMutex);
      bindingReleaseEvents.push_back(args);
      return kSuccess;
   }

   void verifyReleaseBindings(const SipAccountMessageDecoratedBindingReleasedEvent& args)
   {
      for (cpc::vector<cpc::string>::const_iterator i = args.releasedBindings.begin(); i != args.releasedBindings.end(); ++i)
      {
         resip::Data data((*i).c_str());
         resip::Uri uri(data);

         ASSERT_TRUE(uri.host().size() > 0) << " - contact header host not populated";
         ASSERT_TRUE(uri.port() > 0) << " - contact header port not populated";
      }
   }

   void verifyViaContactHeader(const std::string& smessage, const int port, const bool portExists, int& rport)
   {
      resip::Data data(smessage.c_str());
      resip::SipMessage* message = resip::SipMessage::make(data.c_str());
      if (message)
      {
         message->parseAllHeaders();
      }

      if (message && message->isRequest() && (message->header(resip::h_RequestLine).method() == resip::REGISTER))
      {
         ASSERT_TRUE(message->exists(resip::h_Vias)) << " - via header not found";
         ASSERT_FALSE(message->const_header(resip::h_Vias).empty()) << " - via header not populated";

         resip::Via& topVia(message->header(resip::h_Vias).front());
         ASSERT_FALSE(topVia.transport().empty()) << " - via header transport not populated";
         ASSERT_TRUE(topVia.sentHost().size() > 0) << " - via header host not populated";

         if (port > 0)
         {
            ASSERT_EQ(topVia.sentPort(), port) << " - via header port: " << topVia.sentPort() << " does not match expected value: " << port;
         }
         else if (portExists)
         {
            ASSERT_TRUE(topVia.sentPort() > 0) << " - via header port not populated";
         }

         ASSERT_TRUE(message->exists(resip::h_Contacts)) << " - contact header not found";
         ASSERT_GT(message->header(resip::h_Contacts).size(), 0) << " - contact header list is empty"; // Could have multiple contacts due to release bindings functionality
         ASSERT_TRUE(message->header(resip::h_Contacts).front().uri().host().size() > 0) << " - contact header host not populated";
         ASSERT_TRUE(message->header(resip::h_Contacts).front().uri().port() > 0) << " - contact header port not populated";

         if (port > 0)
         {
            ASSERT_TRUE((message->header(resip::h_Contacts).front().uri().port() == port) || (message->header(resip::h_Contacts).front().uri().port() == rport)) << " - contact header port: " << message->header(resip::h_Contacts).front().uri().port() << " does not match expected port value: " << port << " or rport value: " << rport;
         }
         else if (portExists)
         {
            ASSERT_TRUE(message->header(resip::h_Contacts).front().uri().port() > 0) << " - contact header port not populated";
         }
      }
      else
      {
         safeCout("AccountModuleTest::VerifyViaHeaderPopulatedWithPort()::verifyViaContactHeader(): ignoring message verification - not applicable to message");
      }

      delete message;
   }
};

void AccountModuleTest::verifyViaContactHeaderPopulatedWithPort(TestAccount& alice, const int port, const bool exists)
{
   alice.init();

   std::unique_ptr<MySipAccountDecoratorHandler> aliceHandler(new MySipAccountDecoratorHandler());
   std::atomic_bool activeThread(true);
   int rport(0);
   auto accountEvent = std::async(std::launch::async, [&] ()
   {
      while (activeThread)
      {
         {
            std::unique_lock<std::mutex> alock(aliceHandler->messageQueueMutex);
            if (aliceHandler->messages.size() > 0)
            {
               std::string& message = aliceHandler->messages.front();
               aliceHandler->verifyViaContactHeader(message, port, exists, rport);
               aliceHandler->messages.pop_front();
            }
         }
         std::this_thread::sleep_for(std::chrono::milliseconds(100));
      }
   });

   // alice.account->setAdornmentHandler(alice.handle, aliceHandler.get());
   SipAccountManagerInternal* accountMgr = dynamic_cast<SipAccountManagerInternal*>(alice.account);
   ASSERT_TRUE(accountMgr != NULL);
   accountMgr->setDecoratorHandler(alice.handle, aliceHandler.get());

   ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);
   assertAccountRegisteringEx(alice);
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountRegistrationRportUpdateEvent evt;
      if (alice.accountEvents->expectEvent(__LINE__, "SipAccountHandlerInternal::onRegistrationRportUpdate", __FILE__, 5000, CPCAPI2::test::AlwaysTruePred(), h, evt))
      {
         ASSERT_EQ(alice.handle, h) << " account handle mismatch";
         rport = evt.rport;
         safeCout("AccountModuleTest::verifyViaContactHeaderPopulatedWithPort(): rport updated to: " << rport);
      }
   }

   assertAccountRegisteredEx(alice);

   std::this_thread::sleep_for(std::chrono::milliseconds(500));
   activeThread = false;
   accountMgr->setDecoratorHandler(alice.handle, NULL);
   alice.disable();
}

TEST_F(AccountModuleTest, VerifyViaHeaderPopulatedWithPortWithTLSWithMinSipPort)
{
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.sipTransportType = SipAccountTransportType::SipAccountTransport_TLS;
   alice.config.settings.minSipPort = 7777;
   alice.config.settings.domain = "autotest.cpcapi2";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.outboundProxy = "";
   alice.config.settings.additionalCertPeerNames.clear();
   alice.config.settings.acceptedCertPublicKeys.push_back("MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEA3Q/3AQAW9Z8thdtQlnxAG6ilSn1FVH81ViruEEEyJBbxk0WPzroavMqRgvXJ6sIE+7iR8hHqY2BowKPhTg2D+WCvycT+i5YC7L37f/oWsnVp5xZZqTtiTTTXc+rSHgl2WO5tGxyGGWlUsslq3UXnHym5H9VvEZ5R3EKJelxauvhO8uNfjEwjuKBYPFjNJbZUoiy9s+ZhFuM1a14ArRZlvQ/P96txt2GLHjScMMiZllO4a6NyLvMJQI7Ys0lI4JmKe5QEBdukvbSnFbnsaGMjwfeAksFiKyDRvHUjyc0EUaqU24hmhn+RZkYSjTqeBd567TlggPR696peqaA/tILWGUgcMhYBga9DFzkpgXc8NDaO6WqAFg2caM2/BAnZ9OyfzNYU7GXCyOAm6JNb+BaxYLg6rqCKr27cSipy4MG6LvgcOndRLAeQoNe55QMqi+lfH90v0nQsNIlIEgo8pXgwML6lef+dk98Ox46Egkq1txFKVrmmZ2bXBiLrdFQYqEhzpAMl8XlKg7Wq4R5q2NyOuVJwQ0RHVCbW6nHO/nT5eyldqEzvSY+pg6juxOIpgtD7XvlXRqq775OW0aVFGoL1ilKCjb//c3WU4oetWNcBizrkYb0BOny7P17s8Jx2ZVtvcup4KMOi3+NZJew1uRBKM8wgsSZ1CLmZq1ae/ECXI3kCAwEAAQ==");
   verifyViaContactHeaderPopulatedWithPort(alice, alice.config.settings.minSipPort, false);
}

TEST_F(AccountModuleTest, VerifyViaHeaderPopulatedWithPortWithTCPWithMinSipPort)
{
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.sipTransportType = SipAccountTransportType::SipAccountTransport_TCP;
   alice.config.settings.minSipPort = 7777;
   verifyViaContactHeaderPopulatedWithPort(alice, alice.config.settings.minSipPort, false);
}

TEST_F(AccountModuleTest, VerifyViaHeaderPopulatedWithPortWithUDPWithMinSipPort)
{
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.sipTransportType = SipAccountTransportType::SipAccountTransport_UDP;
   alice.config.settings.minSipPort = 7777;
   verifyViaContactHeaderPopulatedWithPort(alice, alice.config.settings.minSipPort, false);
}

TEST_F(AccountModuleTest, VerifyViaHeaderPopulatedWithPortWithTLS)
{
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.sipTransportType = SipAccountTransportType::SipAccountTransport_TLS;
   alice.config.settings.domain = "autotest.cpcapi2";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.outboundProxy = "";
   alice.config.settings.additionalCertPeerNames.clear();
   alice.config.settings.acceptedCertPublicKeys.push_back("MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEA3Q/3AQAW9Z8thdtQlnxAG6ilSn1FVH81ViruEEEyJBbxk0WPzroavMqRgvXJ6sIE+7iR8hHqY2BowKPhTg2D+WCvycT+i5YC7L37f/oWsnVp5xZZqTtiTTTXc+rSHgl2WO5tGxyGGWlUsslq3UXnHym5H9VvEZ5R3EKJelxauvhO8uNfjEwjuKBYPFjNJbZUoiy9s+ZhFuM1a14ArRZlvQ/P96txt2GLHjScMMiZllO4a6NyLvMJQI7Ys0lI4JmKe5QEBdukvbSnFbnsaGMjwfeAksFiKyDRvHUjyc0EUaqU24hmhn+RZkYSjTqeBd567TlggPR696peqaA/tILWGUgcMhYBga9DFzkpgXc8NDaO6WqAFg2caM2/BAnZ9OyfzNYU7GXCyOAm6JNb+BaxYLg6rqCKr27cSipy4MG6LvgcOndRLAeQoNe55QMqi+lfH90v0nQsNIlIEgo8pXgwML6lef+dk98Ox46Egkq1txFKVrmmZ2bXBiLrdFQYqEhzpAMl8XlKg7Wq4R5q2NyOuVJwQ0RHVCbW6nHO/nT5eyldqEzvSY+pg6juxOIpgtD7XvlXRqq775OW0aVFGoL1ilKCjb//c3WU4oetWNcBizrkYb0BOny7P17s8Jx2ZVtvcup4KMOi3+NZJew1uRBKM8wgsSZ1CLmZq1ae/ECXI3kCAwEAAQ==");
   verifyViaContactHeaderPopulatedWithPort(alice, 0, true);
}

TEST_F(AccountModuleTest, VerifyViaHeaderPopulatedWithPortWithTCP)
{
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.sipTransportType = SipAccountTransportType::SipAccountTransport_TCP;
   verifyViaContactHeaderPopulatedWithPort(alice, 0, true);
}

TEST_F(AccountModuleTest, VerifyViaHeaderPopulatedWithPortWithUDP)
{
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.sipTransportType = SipAccountTransportType::SipAccountTransport_UDP;
   verifyViaContactHeaderPopulatedWithPort(alice, 0, true);
}

TEST_F(AccountModuleTest, VerifyViaHeaderPopulatedWithPortWithTLSWithMutualAuthCertConfigured)
{
   CPCAPI2::ReproRunner* reproRunner = ReproHolder::instance();
   reproRunner->restart((TestEnvironmentConfig::testResourcePath() + "repro_mutualTlsAuth.config").c_str());

   TestAccount alice("test", Account_NoInit, false);
   alice.config.settings.username = "test";
   alice.config.settings.domain = "autotest.cpcapi2";
   alice.config.settings.outboundProxy = "autotest.cpcapi2";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_TLS;
   alice.config.settings.password = "";
   alice.config.settings.useRport = false;
   alice.config.settings.sslVersion = SipAccount::TLS_V1_2;
   alice.config.settings.userCertificatePEM = TestEnvironmentConfig::testResourcePath() + "SelfSignedCerts/client/user_cert_test.autotest.cpcapi2.pem";
   alice.config.settings.userPrivateKeyPEM = TestEnvironmentConfig::testResourcePath() + "SelfSignedCerts/client/user_key_test.autotest.cpcapi2.pem";
   alice.config.settings.sourceAddress = "127.0.0.1";

   CPCAPI2::SipAccount::SipAccountHandle h;
   CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

   alice.init();

   SipAccountManagerInternal* mi = dynamic_cast<SipAccountManagerInternal*>(alice.account);
   mi->setCertStorageLoadType(alice.handle, SipAccountManagerInternal::CertLoadStorageType_FileSystem);
   mi->setCertStorageFileSystemPath(alice.handle, TestEnvironmentConfig::testResourcePath() + "SelfSignedCerts/root");

   std::unique_ptr<MySipAccountDecoratorHandler> aliceHandler(new MySipAccountDecoratorHandler());
   std::atomic_bool activeThread(true);
   int rport(0);
   auto accountEvent = std::async(std::launch::async, [&] ()
   {
      while (activeThread)
      {
         {
            std::unique_lock<std::mutex> alock(aliceHandler->messageQueueMutex);
            if (aliceHandler->messages.size() > 0)
            {
               std::string& message = aliceHandler->messages.front();
               aliceHandler->verifyViaContactHeader(message, 0, true, rport);
               aliceHandler->messages.pop_front();
            }
         }
         std::this_thread::sleep_for(std::chrono::milliseconds(100));
      }
   });

   // Perform registration using TLS_V1_2
   SipAccountManagerInternal* accountMgr = dynamic_cast<SipAccountManagerInternal*>(alice.account);
   ASSERT_TRUE(accountMgr != NULL);
   accountMgr->setDecoratorHandler(alice.handle, aliceHandler.get());
   ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);
   assertAccountRegistering(alice);
   ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
   ASSERT_EQ(200, evt.signalingStatusCode);
   ASSERT_EQ(SipAccount::TLS_V1_2, evt.tlsInfo.sslVersion);

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   activeThread = false;
   accountMgr->setDecoratorHandler(alice.handle, NULL);
   alice.disable();

   // Restart proxy to default settings
   reproRunner->restart();

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
}

TEST_F(AccountModuleTest, VerifyViaHeaderPopulatedWithPortWithTLSWithNetworkChange)
{
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.sipTransportType = SipAccountTransportType::SipAccountTransport_TLS;
   alice.config.settings.domain = "autotest.cpcapi2";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.outboundProxy = "";
   alice.config.settings.additionalCertPeerNames.clear();
   alice.config.settings.acceptedCertPublicKeys.push_back("MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEA3Q/3AQAW9Z8thdtQlnxAG6ilSn1FVH81ViruEEEyJBbxk0WPzroavMqRgvXJ6sIE+7iR8hHqY2BowKPhTg2D+WCvycT+i5YC7L37f/oWsnVp5xZZqTtiTTTXc+rSHgl2WO5tGxyGGWlUsslq3UXnHym5H9VvEZ5R3EKJelxauvhO8uNfjEwjuKBYPFjNJbZUoiy9s+ZhFuM1a14ArRZlvQ/P96txt2GLHjScMMiZllO4a6NyLvMJQI7Ys0lI4JmKe5QEBdukvbSnFbnsaGMjwfeAksFiKyDRvHUjyc0EUaqU24hmhn+RZkYSjTqeBd567TlggPR696peqaA/tILWGUgcMhYBga9DFzkpgXc8NDaO6WqAFg2caM2/BAnZ9OyfzNYU7GXCyOAm6JNb+BaxYLg6rqCKr27cSipy4MG6LvgcOndRLAeQoNe55QMqi+lfH90v0nQsNIlIEgo8pXgwML6lef+dk98Ox46Egkq1txFKVrmmZ2bXBiLrdFQYqEhzpAMl8XlKg7Wq4R5q2NyOuVJwQ0RHVCbW6nHO/nT5eyldqEzvSY+pg6juxOIpgtD7XvlXRqq775OW0aVFGoL1ilKCjb//c3WU4oetWNcBizrkYb0BOny7P17s8Jx2ZVtvcup4KMOi3+NZJew1uRBKM8wgsSZ1CLmZq1ae/ECXI3kCAwEAAQ==");

   alice.init();

   std::unique_ptr<MySipAccountDecoratorHandler> aliceHandler(new MySipAccountDecoratorHandler());
   std::atomic_bool activeThread(true);
   int rport(0);
   auto accountEvent = std::async(std::launch::async, [&] ()
   {
      while (activeThread)
      {
         {
            std::unique_lock<std::mutex> alock(aliceHandler->messageQueueMutex);
            if (aliceHandler->messages.size() > 0)
            {
               std::string& message = aliceHandler->messages.front();
               aliceHandler->verifyViaContactHeader(message, 0, true, rport);
               aliceHandler->messages.pop_front();
            }
         }
         std::this_thread::sleep_for(std::chrono::milliseconds(100));
      }
   });

   SipAccountManagerInternal* accountMgr = dynamic_cast<SipAccountManagerInternal*>(alice.account);
   ASSERT_TRUE(accountMgr != NULL);
   accountMgr->setDecoratorHandler(alice.handle, aliceHandler.get());
   alice.enable();

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
   std::set<resip::Data> wwan;
   wwan.insert("*******");
   alice.network->setMockInterfaces(wwan);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   assertAccountRefreshing(alice);
   assertAccountRegistered(alice);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   activeThread = false;
   accountMgr->setDecoratorHandler(alice.handle, NULL);
   alice.disable();
}

void AccountModuleTest::verifyReleaseBindings(TestAccount& alice, const int accountEnableCount, const int expectRelease, const int networkChange)
{
   alice.init();

   std::unique_ptr<MySipAccountDecoratorHandler> aliceHandler(new MySipAccountDecoratorHandler());
   std::atomic_bool activeThread(true);
   std::atomic_bool threadDeactivated(false);
   auto accountEvents = std::async(std::launch::async, [&] ()
   {
      int receivedCount(0);
      while (activeThread)
      {
         {
            std::unique_lock<std::mutex> alock(aliceHandler->bindingReleaseMutex);
            if (expectRelease > 0)
            {
               if (aliceHandler->bindingReleaseEvents.size() > 0)
               {
                  SipAccountMessageDecoratedBindingReleasedEvent& args = aliceHandler->bindingReleaseEvents.front();
                  aliceHandler->verifyReleaseBindings(args);
                  aliceHandler->bindingReleaseEvents.pop_front();
                  receivedCount++;
               }
            }
         }
         std::this_thread::sleep_for(std::chrono::milliseconds(100));
      }

      if (expectRelease)
      {
         ASSERT_TRUE(receivedCount == expectRelease) << " received " << receivedCount << " release binding events when was expecting " << expectRelease;
      }
      else
      {
         ASSERT_TRUE(aliceHandler->bindingReleaseEvents.size() == 0) << " received " << aliceHandler->bindingReleaseEvents.size() << " release binding events when was expecting none";
      }

      threadDeactivated = true;
   });

   SipAccountManagerInternal* accountMgr = dynamic_cast<SipAccountManagerInternal*>(alice.account);
   ASSERT_TRUE(accountMgr != NULL);
   accountMgr->setDecoratorHandler(alice.handle, aliceHandler.get());

   int count = 0;
   while (count < accountEnableCount)
   {
      count++;
      ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);
      assertAccountRegisteringEx(alice);
      assertAccountRegisteredEx(alice);

      std::this_thread::sleep_for(std::chrono::milliseconds(10000));

      int networkChangeCount = 0;
      while (networkChangeCount < networkChange)
      {
         networkChangeCount++;
         NetworkTransport transport = ((networkChangeCount % 2 == 0) ? NetworkTransport::TransportWiFi : NetworkTransport::TransportWWAN);

         safeCout("AccountModuleTest::verifyReleaseBindings(): starting network change to: " << transport << " - index: " << networkChangeCount);
         std::this_thread::sleep_for(std::chrono::milliseconds(2000));

         alice.network->setNetworkTransport(transport);
         std::stringstream ss;
         ss << "2.2.2." << networkChangeCount;
         std::set<resip::Data> interfaces;
         interfaces.insert(ss.str().c_str());
         alice.network->setMockInterfaces(interfaces);
         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         assertAccountRefreshing(alice);
         assertAccountRegistered(alice);

         std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      }

      alice.disable();
   }

   activeThread = false;
   int waitCounter = 0;
   while (waitCounter < 20)
   {
      if (threadDeactivated)
      {
         accountMgr->setDecoratorHandler(alice.handle, NULL);
         break;
      }

      waitCounter++;
      std::this_thread::sleep_for(std::chrono::milliseconds(500));
   }
   ASSERT_LT(waitCounter, 20) << " did not receive notification that decorator handler thread was deactiviated successfully";
   waitFor(accountEvents);
}

TEST_F(AccountModuleTest, DISABLED_VerifyReleaseBindingsDecorator)
{
   TestAccount aliceAccount("alice", Account_NoInit);
   aliceAccount.init();

   std::unique_ptr<CPMessageDecorator> messageDecorator = std::make_unique<CPMessageDecorator>(aliceAccount.phone, aliceAccount.handle, resip::Data(), 0, false, false);
   ASSERT_TRUE(messageDecorator->getBindingReleaseDecorator() == NULL);
   messageDecorator->enableReleaseBindings();
   std::shared_ptr<CPCAPI2::SipAccount::CPBindingReleaseDecorator> bindingDecorator = messageDecorator->getBindingReleaseDecorator();
   ASSERT_FALSE(messageDecorator->getBindingReleaseDecorator() == NULL);

   std::string user = "alice";
   std::string host = "127.0.0.1";
   int port = 5060;
   std::string domain = "example.com";

   resip::NameAddr alice;
   alice.uri().user() = user.c_str();
   alice.uri().host() = domain.c_str();

   resip::NameAddr realm;
   realm.uri().host() = domain.c_str();

   resip::NameAddr binding;
   binding.uri().user() = user.c_str();
   binding.uri().host() = host.c_str();
   binding.uri().port() = port;

   resip::NameAddr binding2;
   binding.uri().user() = user.c_str();
   binding.uri().host() = host.c_str();
   int port2 = 5061;
   binding2.uri().port() = port2;

   /*
   resip::NameAddr binding3;
   std::string example1 = binding3.uri().toString().c_str();
   std::string example2 = binding3.uri().host().c_str();
   std::string example3 = binding3.uri().getAOR(false).c_str();
   std::string example4 = binding3.uri().getAor().c_str();
   std::string example5 = binding3.uri().getAorAsUri().toString().c_str();
   std::string example6 = binding3.uri().getAorNoReally().c_str();
   std::string example7 = binding3.uri().getAorNoPort().c_str();
   std::string example8 = binding3.uri().getAORKeepCase(false).c_str();

   resip::NameAddr addr1 = binding;
   resip::NameAddr addr2 = binding;
   addr2.uri().port() = 5061;
   resip::NameAddr addr3 = binding;
   addr3.uri().host() = "abc.com";
   resip::NameAddr addr4 = binding;
   addr4.uri().user() = "bob";
   resip::NameAddr addr5 = binding;
   addr5.uri().param(resip::p_transport) = "tcp";
   resip::NameAddr addr6 = binding;
   addr6.uri().scheme() = "sips";
   resip::NameAddr addr7 = binding;
   addr7.uri().param(resip::p_rinstance) = "ddd";
   bool isSame1 = (addr1 == addr1); // same
   bool isSame2 = (addr1 == addr2); // different port
   bool isSame3 = (addr1 == addr3); // different host
   bool isSame4 = (addr1 == addr4); // different user
   bool isSame5 = (addr1 == addr5); // different transport
   bool isSame6 = (addr1 == addr6); // different scheme
   bool isSame7 = (addr1 == addr7); // different rinstance
   */

   std::unique_ptr<resip::SipMessage> pRegisterMessage(resip::Helper::makeRegister(alice, alice, binding));
   resip::SipMessage& registerMessage = (*pRegisterMessage);
   registerMessage.header(resip::h_Expires).value() = 3600;

   std::unique_ptr<resip::SipMessage> pRegisterMessage2(resip::Helper::makeRegister(alice, alice, binding2));
   resip::SipMessage& registerMessage2 = (*pRegisterMessage2);
   registerMessage2.header(resip::h_Expires).value() = 3600;

   resip::SipMessage originalMessageWithoutRinstance = registerMessage;
   ASSERT_TRUE(resip::Data::from(originalMessageWithoutRinstance) == resip::Data::from(registerMessage));
   ASSERT_EQ(registerMessage.header(resip::h_Contacts).size(), 1);
   ASSERT_FALSE(registerMessage.header(resip::h_Contacts).front().exists(resip::p_expires));

   resip::SipMessage originalMessageWithRinstance = registerMessage;
   originalMessageWithRinstance.header(resip::h_Contacts).front().uri().param(resip::p_rinstance) = "ABCD1234";
   ASSERT_FALSE(resip::Data::from(originalMessageWithRinstance) == resip::Data::from(registerMessage));
   ASSERT_EQ(registerMessage.header(resip::h_Contacts).size(), 1);
   ASSERT_FALSE(registerMessage.header(resip::h_Contacts).front().exists(resip::p_expires));

   resip::SipMessage originalMessageWithRinstanceBinding2 = registerMessage2;
   originalMessageWithRinstanceBinding2.header(resip::h_Contacts).front().uri().param(resip::p_rinstance) = "ABCD1234";
   ASSERT_FALSE(resip::Data::from(originalMessageWithRinstanceBinding2) == resip::Data::from(registerMessage2));
   ASSERT_EQ(registerMessage2.header(resip::h_Contacts).size(), 1);
   ASSERT_FALSE(registerMessage2.header(resip::h_Contacts).front().exists(resip::p_expires));

   resip::SipMessage originalMessageWithRinstance2 = originalMessageWithRinstance;
   originalMessageWithRinstance2.header(resip::h_Contacts).front().uri().param(resip::p_rinstance) = "ABCD5678";

   std::unique_ptr<resip::SipMessage> pResponse407(resip::Helper::makeResponse(originalMessageWithRinstance, 407));
   std::unique_ptr<resip::SipMessage> pResponse407_rinstance2(resip::Helper::makeResponse(originalMessageWithRinstance2, 407));
   std::unique_ptr<resip::SipMessage> pResponse407_binding2(resip::Helper::makeResponse(originalMessageWithRinstanceBinding2, 407));
   std::unique_ptr<resip::SipMessage> pResponse401(resip::Helper::makeResponse(originalMessageWithRinstance, 401));
   std::unique_ptr<resip::SipMessage> pResponse200(resip::Helper::makeResponse(originalMessageWithRinstance, 200));

   SipRegistrationDialogSuccessEvent dialogSuccessEvent;
   dialogSuccessEvent.response = *pResponse200;

   safeCout("\nOriginal message: " << registerMessage << std::endl);

   resip::NameAddr originalContactWithoutRinstance = originalMessageWithoutRinstance.header(resip::h_Contacts).front();
   resip::NameAddr originalContactWithRinstance = originalMessageWithRinstance.header(resip::h_Contacts).front();
   originalContactWithRinstance.uri().param(resip::p_rinstance) = "ABCD1234";
   resip::NameAddr contactWithoutRinstance5061 = originalContactWithoutRinstance;
   resip::NameAddr contactWithRinstance5061 = originalContactWithRinstance;
   resip::NameAddr contactWithRinstance5062 = originalContactWithRinstance;
   resip::NameAddr contactWithRinstance5063 = originalContactWithRinstance;
   resip::NameAddr contactWithRinstance5064 = originalContactWithRinstance;
   contactWithoutRinstance5061.uri().port() = 5061;
   contactWithRinstance5061.uri().port() = 5061;
   contactWithRinstance5062.uri().port() = 5062;
   contactWithRinstance5063.uri().port() = 5063;
   contactWithRinstance5064.uri().port() = 5064;

   int scenario = 0;

   // Verify default register message
   ASSERT_EQ(registerMessage.header(resip::h_Contacts).size(), 1);
   ASSERT_EQ(binding.uri().port(), 5060);
   ASSERT_EQ(registerMessage.header(resip::h_Contacts).front().uri().port(), binding.uri().port());

   // Decorator should not decorate as release binding is not yet activated
   bindingDecorator->onRegistrationDialogSuccess(dialogSuccessEvent); // Reset the decorator
   bindingDecorator->decorateMessage(registerMessage);
   ASSERT_TRUE(resip::Data::from(originalMessageWithoutRinstance) == resip::Data::from(registerMessage));
   ASSERT_EQ(registerMessage.header(resip::h_Contacts).size(), 1);
   ASSERT_FALSE(registerMessage.header(resip::h_Contacts).front().exists(resip::p_expires));
   safeCout("\n***** SCENARIO: " << ++scenario << " ***** No change to register message - release binding not activated and rinstance not included:\n" << registerMessage << std::endl);

   // Activate the release binding, Decorator should not decorate as there are no bindings populated
   bindingDecorator->onRegistrationDialogSuccess(dialogSuccessEvent); // Reset the decorator
   bindingDecorator->setActivateCleanup(true);
   registerMessage = originalMessageWithoutRinstance;
   bindingDecorator->decorateMessage(registerMessage);
   ASSERT_TRUE(resip::Data::from(originalMessageWithoutRinstance) == resip::Data::from(registerMessage));
   ASSERT_EQ(registerMessage.header(resip::h_Contacts).size(), 1);
   ASSERT_FALSE(registerMessage.header(resip::h_Contacts).front().exists(resip::p_expires));
   safeCout("\n***** SCENARIO: " << ++scenario << " ***** No change to register message - release binding activated, rinstance not included but no release bindings populated:\n" << registerMessage << std::endl);

   // Add the release binding, Decorator should not decorate as the binding to be released is also part of the new contact binding
   bindingDecorator->onRegistrationDialogSuccess(dialogSuccessEvent); // Reset the decorator
   registerMessage = originalMessageWithoutRinstance;
   bindingDecorator->setActivateCleanup(true);
   bindingDecorator->addToReleaseBindingList(registerMessage.header(resip::h_Contacts).front());
   bindingDecorator->decorateMessage(registerMessage);
   ASSERT_TRUE(resip::Data::from(originalMessageWithoutRinstance) == resip::Data::from(registerMessage));
   ASSERT_EQ(registerMessage.header(resip::h_Contacts).size(), 1);
   ASSERT_FALSE(registerMessage.header(resip::h_Contacts).front().exists(resip::p_expires));
   safeCout("\n***** SCENARIO: " << ++scenario << " ***** No change to register message - as release binding matches new contact binding:\n" << registerMessage << std::endl);

   // Add a different release binding, Decorator should not decorate as the rinstance parameter is not present
   bindingDecorator->onRegistrationDialogSuccess(dialogSuccessEvent); // Reset the decorator
   registerMessage = originalMessageWithoutRinstance;
   bindingDecorator->setActivateCleanup(true);
   bindingDecorator->addToReleaseBindingList(contactWithoutRinstance5061);
   bindingDecorator->decorateMessage(registerMessage);
   ASSERT_TRUE(resip::Data::from(originalMessageWithoutRinstance) == resip::Data::from(registerMessage));
   ASSERT_EQ(registerMessage.header(resip::h_Contacts).size(), 1);
   ASSERT_FALSE(registerMessage.header(resip::h_Contacts).front().exists(resip::p_expires));
   safeCout("\n***** SCENARIO: " << ++scenario << " ***** No change to register message - release binding activated but rinstance not included:\n" << registerMessage << std::endl);

   // Add the rinstance parameter, Decorator should now add an additional contact header with an expires parameter
   bindingDecorator->onRegistrationDialogSuccess(dialogSuccessEvent); // Reset the decorator
   registerMessage = originalMessageWithRinstance;
   bindingDecorator->setActivateCleanup(true);
   bindingDecorator->addToReleaseBindingList(contactWithRinstance5061);
   bindingDecorator->decorateMessage(registerMessage);
   ASSERT_FALSE(resip::Data::from(originalMessageWithRinstance) == resip::Data::from(registerMessage));
   ASSERT_EQ(registerMessage.header(resip::h_Contacts).size(), 2) << "\nMessage: " << registerMessage << std::endl;
   ASSERT_FALSE(registerMessage.header(resip::h_Contacts).front().exists(resip::p_expires));
   ASSERT_TRUE(registerMessage.header(resip::h_Contacts).back().exists(resip::p_expires));
   safeCout("\n***** SCENARIO: " << ++scenario << " ***** Release binding added to register message:\n" << registerMessage << std::endl);

   // Add multiple bindings, Decorator should now add the additional contact headers with an expires parameter
   bindingDecorator->onRegistrationDialogSuccess(dialogSuccessEvent); // Reset the decorator
   registerMessage = originalMessageWithRinstance;
   bindingDecorator->setActivateCleanup(true);
   bindingDecorator->addToReleaseBindingList(contactWithRinstance5061);
   bindingDecorator->addToReleaseBindingList(contactWithRinstance5062);
   bindingDecorator->decorateMessage(registerMessage);
   ASSERT_FALSE(resip::Data::from(originalMessageWithRinstance) == resip::Data::from(registerMessage));
   ASSERT_EQ(registerMessage.header(resip::h_Contacts).size(), 3) << "\nMessage: " << registerMessage << std::endl;
   ASSERT_FALSE(registerMessage.header(resip::h_Contacts).front().exists(resip::p_expires));
   ASSERT_TRUE(registerMessage.header(resip::h_Contacts).back().exists(resip::p_expires));
   safeCout("\n***** SCENARIO: " << ++scenario << " ***** Multiple release bindings - 1 - added to register message:\n" << registerMessage << std::endl);

   // Add multiple bindings, Decorator should now add the additional contact headers with an expires parameter
   bindingDecorator->onRegistrationDialogSuccess(dialogSuccessEvent); // Reset the decorator
   registerMessage = originalMessageWithRinstance;
   bindingDecorator->setActivateCleanup(true);
   bindingDecorator->addToReleaseBindingList(contactWithRinstance5061);
   bindingDecorator->addToReleaseBindingList(contactWithRinstance5062);
   bindingDecorator->addToReleaseBindingList(contactWithRinstance5063);
   bindingDecorator->decorateMessage(registerMessage);
   ASSERT_FALSE(resip::Data::from(originalMessageWithRinstance) == resip::Data::from(registerMessage));
   ASSERT_EQ(registerMessage.header(resip::h_Contacts).size(), 4) << "\nMessage: " << registerMessage << std::endl;
   ASSERT_FALSE(registerMessage.header(resip::h_Contacts).front().exists(resip::p_expires));
   ASSERT_TRUE(registerMessage.header(resip::h_Contacts).back().exists(resip::p_expires));
   safeCout("\n***** SCENARIO: " << ++scenario << " ***** Mutiple release bindings - 2 - added to register message:\n" << registerMessage << std::endl);

   // Add multiple bindings, Decorator should now add the additional contact headers with an expires parameter.
   // Message could be challenged so the registration dialog should not complete, so decorator should add the
   // bindings again until the registration is successful.
   bindingDecorator->onRegistrationDialogSuccess(dialogSuccessEvent); // Reset the decorator
   registerMessage = originalMessageWithRinstance;
   bindingDecorator->setActivateCleanup(true);
   bindingDecorator->addToReleaseBindingList(contactWithRinstance5061);
   bindingDecorator->addToReleaseBindingList(contactWithRinstance5062);
   bindingDecorator->addToReleaseBindingList(contactWithRinstance5063);
   bindingDecorator->decorateMessage(registerMessage);
   ASSERT_FALSE(resip::Data::from(originalMessageWithRinstance) == resip::Data::from(registerMessage));
   ASSERT_EQ(registerMessage.header(resip::h_Contacts).size(), 4) << "\nMessage: " << registerMessage << std::endl;
   ASSERT_FALSE(registerMessage.header(resip::h_Contacts).front().exists(resip::p_expires));
   ASSERT_TRUE(registerMessage.header(resip::h_Contacts).back().exists(resip::p_expires));
   // Calling decorate again should again add the release bindings
   bindingDecorator->decorateMessage(registerMessage);
   ASSERT_FALSE(resip::Data::from(originalMessageWithRinstance) == resip::Data::from(registerMessage));
   ASSERT_EQ(registerMessage.header(resip::h_Contacts).size(), 4) << "\nMessage: " << registerMessage << std::endl;
   ASSERT_FALSE(registerMessage.header(resip::h_Contacts).front().exists(resip::p_expires));
   ASSERT_TRUE(registerMessage.header(resip::h_Contacts).back().exists(resip::p_expires));
   // Calling decorate again should again add the release bindings
   bindingDecorator->decorateMessage(registerMessage);
   ASSERT_FALSE(resip::Data::from(originalMessageWithRinstance) == resip::Data::from(registerMessage));
   ASSERT_EQ(registerMessage.header(resip::h_Contacts).size(), 4) << "\nMessage: " << registerMessage << std::endl;
   ASSERT_FALSE(registerMessage.header(resip::h_Contacts).front().exists(resip::p_expires));
   ASSERT_TRUE(registerMessage.header(resip::h_Contacts).back().exists(resip::p_expires));
   // Calling decorate again after a successful dialog should prevent release bindings to be added
   registerMessage = originalMessageWithRinstance; // Reset the message to remove the bindings already added
   bindingDecorator->onRegistrationDialogSuccess(dialogSuccessEvent);
   bindingDecorator->decorateMessage(registerMessage);
   ASSERT_TRUE(resip::Data::from(originalMessageWithRinstance) == resip::Data::from(registerMessage));
   ASSERT_EQ(registerMessage.header(resip::h_Contacts).size(), 1) << "\nMessage: " << registerMessage << std::endl;
   ASSERT_FALSE(registerMessage.header(resip::h_Contacts).front().exists(resip::p_expires));
   safeCout("\n***** SCENARIO: " << ++scenario << " ***** Mutiple release bindings with challenge - 1 - added to register message:\n" << registerMessage << std::endl);

   // Add multiple bindings, Decorator should now add the additional contact headers with an expires parameter.
   // Registration dialog is successful so decorator should not add the bindings again, e.g. no authentication challenge
   bindingDecorator->onRegistrationDialogSuccess(dialogSuccessEvent); // Reset the decorator
   registerMessage = originalMessageWithRinstance;
   bindingDecorator->setActivateCleanup(true);
   bindingDecorator->addToReleaseBindingList(contactWithRinstance5061);
   bindingDecorator->addToReleaseBindingList(contactWithRinstance5062);
   bindingDecorator->addToReleaseBindingList(contactWithRinstance5063);
   bindingDecorator->decorateMessage(registerMessage);
   ASSERT_FALSE(resip::Data::from(originalMessageWithRinstance) == resip::Data::from(registerMessage));
   ASSERT_EQ(registerMessage.header(resip::h_Contacts).size(), 4) << "\nMessage: " << registerMessage << std::endl;
   ASSERT_FALSE(registerMessage.header(resip::h_Contacts).front().exists(resip::p_expires));
   ASSERT_TRUE(registerMessage.header(resip::h_Contacts).back().exists(resip::p_expires));
   // Calling decorate again after a successful dialog should prevent new bindings
   registerMessage = originalMessageWithRinstance; // Reset the message to remove the bindings already added
   bindingDecorator->onRegistrationDialogSuccess(dialogSuccessEvent);
   bindingDecorator->decorateMessage(registerMessage);
   ASSERT_TRUE(resip::Data::from(originalMessageWithRinstance) == resip::Data::from(registerMessage));
   ASSERT_EQ(registerMessage.header(resip::h_Contacts).size(), 1) << "\nMessage: " << registerMessage << std::endl;
   ASSERT_FALSE(registerMessage.header(resip::h_Contacts).front().exists(resip::p_expires));
   safeCout("\n***** SCENARIO: " << ++scenario << " ***** Mutiple release bindings with no challenge - 2 - added to register message:\n" << registerMessage << std::endl);

   // Add multiple bindings with some duplicates, Decorator should now add the additional contact headers
   // with an expires parameter, but the duplicates should be ignored
   bindingDecorator->onRegistrationDialogSuccess(dialogSuccessEvent); // Reset the decorator
   registerMessage = originalMessageWithRinstance;
   bindingDecorator->setActivateCleanup(true);
   bindingDecorator->addToReleaseBindingList(contactWithRinstance5061);
   bindingDecorator->addToReleaseBindingList(contactWithRinstance5062);
   bindingDecorator->addToReleaseBindingList(contactWithRinstance5062);
   bindingDecorator->addToReleaseBindingList(contactWithRinstance5063);
   bindingDecorator->addToReleaseBindingList(contactWithRinstance5063);
   bindingDecorator->addToReleaseBindingList(contactWithRinstance5063);
   bindingDecorator->decorateMessage(registerMessage);
   ASSERT_FALSE(resip::Data::from(originalMessageWithRinstance) == resip::Data::from(registerMessage));
   ASSERT_EQ(registerMessage.header(resip::h_Contacts).size(), 4) << "\nMessage: " << registerMessage << std::endl;
   ASSERT_FALSE(registerMessage.header(resip::h_Contacts).front().exists(resip::p_expires));
   ASSERT_TRUE(registerMessage.header(resip::h_Contacts).back().exists(resip::p_expires));
   bindingDecorator->onRegistrationDialogSuccess(dialogSuccessEvent);
   safeCout("\n***** SCENARIO: " << ++scenario << " ***** Multiple release bindings added to register message with some duplicates:\n" << registerMessage << std::endl);

   // No binding in the register message, Decorator should add the release binding
   bindingDecorator->onRegistrationDialogSuccess(dialogSuccessEvent); // Reset the decorator
   registerMessage = originalMessageWithRinstance;
   registerMessage.header(resip::h_Contacts).clear();
   bindingDecorator->setActivateCleanup(true);
   bindingDecorator->addToReleaseBindingList(contactWithRinstance5061);
   bindingDecorator->decorateMessage(registerMessage);
   ASSERT_FALSE(resip::Data::from(originalMessageWithRinstance) == resip::Data::from(registerMessage));
   ASSERT_EQ(registerMessage.header(resip::h_Contacts).size(), 1) << "\nMessage: " << registerMessage << std::endl;
   ASSERT_TRUE(registerMessage.header(resip::h_Contacts).front().exists(resip::p_expires));
   safeCout("\n***** SCENARIO: " << ++scenario << " ***** No new contact binding in register message:\n" << registerMessage << std::endl);

   // Message decorator resets the released bindings as would be in case of account enable-disable, Decorator
   // should not update the release bindings
   bindingDecorator->onRegistrationDialogSuccess(dialogSuccessEvent); // Reset the decorator
   registerMessage = originalMessageWithRinstance;
   bindingDecorator->setActivateCleanup(true);
   bindingDecorator->addToReleaseBindingList(contactWithRinstance5061);
   bindingDecorator->addToReleaseBindingList(contactWithRinstance5062);
   bindingDecorator->addToReleaseBindingList(contactWithRinstance5063);
   bindingDecorator->resetBindingList();
   bindingDecorator->decorateMessage(registerMessage);
   ASSERT_TRUE(resip::Data::from(originalMessageWithRinstance) == resip::Data::from(registerMessage));
   ASSERT_EQ(registerMessage.header(resip::h_Contacts).size(), 1) << "\nMessage: " << registerMessage << std::endl;
   ASSERT_FALSE(registerMessage.header(resip::h_Contacts).front().exists(resip::p_expires));
   safeCout("\n***** SCENARIO: " << ++scenario << " ***** No release bindings added to register message as binding list reset:\n" << registerMessage << std::endl);

   // Decorator should not decorate as release binding is not yet activated but with rinstance present
   bindingDecorator->onRegistrationDialogSuccess(dialogSuccessEvent); // Reset the decorator
   registerMessage = originalMessageWithRinstance;
   bindingDecorator->decorateMessage(registerMessage);
   ASSERT_TRUE(resip::Data::from(originalMessageWithRinstance) == resip::Data::from(registerMessage));
   ASSERT_EQ(registerMessage.header(resip::h_Contacts).size(), 1);
   ASSERT_FALSE(registerMessage.header(resip::h_Contacts).front().exists(resip::p_expires));
   safeCout("\n***** SCENARIO: " << ++scenario << " ***** No release bindings added to register message as release binding not activated:\n" << registerMessage << std::endl);

   // Activate the release binding with rinstance present, Decorator should not decorate as there are no bindings populated
   bindingDecorator->onRegistrationDialogSuccess(dialogSuccessEvent); // Reset the decorator
   registerMessage = originalMessageWithRinstance;
   bindingDecorator->setActivateCleanup(true);
   bindingDecorator->decorateMessage(registerMessage);
   ASSERT_TRUE(resip::Data::from(originalMessageWithRinstance) == resip::Data::from(registerMessage));
   ASSERT_EQ(registerMessage.header(resip::h_Contacts).size(), 1);
   ASSERT_FALSE(registerMessage.header(resip::h_Contacts).front().exists(resip::p_expires));
   safeCout("\n***** SCENARIO: " << ++scenario << " ***** Release binding added to register message as release binding activated:\n" << registerMessage << std::endl);

   // Add the release binding with rinstance present, Decorator should not decorate as the binding to be released is also
   // part of the new contact binding
   bindingDecorator->onRegistrationDialogSuccess(dialogSuccessEvent); // Reset the decorator
   registerMessage = originalMessageWithRinstance;
   bindingDecorator->setActivateCleanup(true);
   bindingDecorator->addToReleaseBindingList(registerMessage.header(resip::h_Contacts).front());
   bindingDecorator->decorateMessage(registerMessage);
   ASSERT_TRUE(resip::Data::from(originalMessageWithRinstance) == resip::Data::from(registerMessage));
   ASSERT_EQ(registerMessage.header(resip::h_Contacts).size(), 1);
   ASSERT_FALSE(registerMessage.header(resip::h_Contacts).front().exists(resip::p_expires));
   safeCout("\n***** SCENARIO: " << ++scenario << " ***** No release bindings added to register message as release binding is also part of the new binding list:\n" << registerMessage << std::endl);
   
   // Add multiple bindings to be released and to be enabled, Decorator should now add the additional contact
   // headers with an expires parameter, except to the bindings that are to be enabled
   bindingDecorator->onRegistrationDialogSuccess(dialogSuccessEvent); // Reset the decorator
   registerMessage = originalMessageWithRinstance;
   registerMessage.header(resip::h_Contacts).push_back(contactWithRinstance5061);
   bindingDecorator->setActivateCleanup(true);
   bindingDecorator->addToReleaseBindingList(contactWithRinstance5062);
   bindingDecorator->addToReleaseBindingList(contactWithRinstance5063);
   bindingDecorator->decorateMessage(registerMessage);
   ASSERT_FALSE(resip::Data::from(originalMessageWithRinstance) == resip::Data::from(registerMessage));
   ASSERT_EQ(registerMessage.header(resip::h_Contacts).size(), 4) << "\nMessage: " << registerMessage << std::endl;
   ASSERT_FALSE(registerMessage.header(resip::h_Contacts).front().exists(resip::p_expires));
   int index = 0;
   for (resip::NameAddrs::iterator i = registerMessage.header(resip::h_Contacts).begin(); i != registerMessage.header(resip::h_Contacts).end(); i++)
   {
      resip::NameAddr binding = (*i);
      if (index == 0)
      {
         // Enabled binding
         ASSERT_EQ(binding.uri(), originalContactWithRinstance.uri());
         ASSERT_FALSE(binding.exists(resip::p_expires));
      }
      else if (index == 1)
      {
         // Enabled binding
         ASSERT_EQ(binding.uri(), contactWithRinstance5061.uri());
         ASSERT_FALSE(binding.exists(resip::p_expires));
      }
      else if (index == 2)
      {
         // Enabled binding
         ASSERT_EQ(binding.uri(), contactWithRinstance5062.uri());
         ASSERT_TRUE(binding.exists(resip::p_expires));
      }
      else
      {
         // Enabled binding
         ASSERT_EQ(binding.uri(), contactWithRinstance5063.uri());
         ASSERT_TRUE(binding.exists(resip::p_expires));
      }
      index++;
   }

   ASSERT_TRUE(registerMessage.header(resip::h_RequestLine).uri() == originalMessageWithRinstance.header(resip::h_RequestLine).uri());
   ASSERT_TRUE(registerMessage.header(resip::h_Vias).size() == 1);
   ASSERT_TRUE(registerMessage.header(resip::h_Vias).front().sentHost() == originalMessageWithRinstance.header(resip::h_Vias).front().sentHost());
   ASSERT_TRUE(registerMessage.header(resip::h_Vias).front().sentPort() == originalMessageWithRinstance.header(resip::h_Vias).front().sentPort());
   ASSERT_TRUE(registerMessage.header(resip::h_Vias).front().transport() == originalMessageWithRinstance.header(resip::h_Vias).front().transport());
   ASSERT_TRUE(registerMessage.header(resip::h_Vias).front().protocolName() == originalMessageWithRinstance.header(resip::h_Vias).front().protocolName());
   ASSERT_TRUE(registerMessage.header(resip::h_Vias).front().protocolVersion() == originalMessageWithRinstance.header(resip::h_Vias).front().protocolVersion());
   ASSERT_TRUE(registerMessage.header(resip::h_MaxForwards).value() == originalMessageWithRinstance.header(resip::h_MaxForwards).value());
   ASSERT_TRUE(registerMessage.header(resip::h_Contacts).front().uri().toString() == originalMessageWithRinstance.header(resip::h_Contacts).front().uri().toString());
   ASSERT_TRUE(registerMessage.header(resip::h_To) == originalMessageWithRinstance.header(resip::h_To));
   ASSERT_TRUE(registerMessage.header(resip::h_From) == originalMessageWithRinstance.header(resip::h_From));
   ASSERT_TRUE(registerMessage.header(resip::h_CallID) == originalMessageWithRinstance.header(resip::h_CallID));
   ASSERT_TRUE(registerMessage.header(resip::h_CSeq) == originalMessageWithRinstance.header(resip::h_CSeq));
   ASSERT_TRUE(registerMessage.header(resip::h_Expires).value() == originalMessageWithRinstance.header(resip::h_Expires).value());
   ASSERT_TRUE(registerMessage.header(resip::h_ContentLength).value() == originalMessageWithRinstance.header(resip::h_ContentLength).value());
   safeCout("\n***** SCENARIO: " << ++scenario << " ***** Multiple release bindings added to register message with multiple new bindings:\n" << registerMessage << std::endl);
}

TEST_F(AccountModuleTest, DISABLED_VerifyReleaseBindingsDuringAccountEnableWithTLS)
{
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.sipTransportType = SipAccountTransportType::SipAccountTransport_TLS;
   alice.config.settings.domain = "autotest.cpcapi2";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.outboundProxy = "";
   alice.config.settings.additionalCertPeerNames.clear();
   alice.config.settings.acceptedCertPublicKeys.push_back("MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEA3Q/3AQAW9Z8thdtQlnxAG6ilSn1FVH81ViruEEEyJBbxk0WPzroavMqRgvXJ6sIE+7iR8hHqY2BowKPhTg2D+WCvycT+i5YC7L37f/oWsnVp5xZZqTtiTTTXc+rSHgl2WO5tGxyGGWlUsslq3UXnHym5H9VvEZ5R3EKJelxauvhO8uNfjEwjuKBYPFjNJbZUoiy9s+ZhFuM1a14ArRZlvQ/P96txt2GLHjScMMiZllO4a6NyLvMJQI7Ys0lI4JmKe5QEBdukvbSnFbnsaGMjwfeAksFiKyDRvHUjyc0EUaqU24hmhn+RZkYSjTqeBd567TlggPR696peqaA/tILWGUgcMhYBga9DFzkpgXc8NDaO6WqAFg2caM2/BAnZ9OyfzNYU7GXCyOAm6JNb+BaxYLg6rqCKr27cSipy4MG6LvgcOndRLAeQoNe55QMqi+lfH90v0nQsNIlIEgo8pXgwML6lef+dk98Ox46Egkq1txFKVrmmZ2bXBiLrdFQYqEhzpAMl8XlKg7Wq4R5q2NyOuVJwQ0RHVCbW6nHO/nT5eyldqEzvSY+pg6juxOIpgtD7XvlXRqq775OW0aVFGoL1ilKCjb//c3WU4oetWNcBizrkYb0BOny7P17s8Jx2ZVtvcup4KMOi3+NZJew1uRBKM8wgsSZ1CLmZq1ae/ECXI3kCAwEAAQ==");
   int accountEnableCount(1);
   int releaseBindingsExpected(0);
   int networkChangeCount(0);
   verifyReleaseBindings(alice, accountEnableCount, releaseBindingsExpected, networkChangeCount);
}

TEST_F(AccountModuleTest, DISABLED_VerifyReleaseBindingsDuringMultipleAccountEnableWithTLS)
{
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.sipTransportType = SipAccountTransportType::SipAccountTransport_TLS;
   alice.config.settings.domain = "autotest.cpcapi2";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.outboundProxy = "";
   alice.config.settings.additionalCertPeerNames.clear();
   alice.config.settings.acceptedCertPublicKeys.push_back("MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEA3Q/3AQAW9Z8thdtQlnxAG6ilSn1FVH81ViruEEEyJBbxk0WPzroavMqRgvXJ6sIE+7iR8hHqY2BowKPhTg2D+WCvycT+i5YC7L37f/oWsnVp5xZZqTtiTTTXc+rSHgl2WO5tGxyGGWlUsslq3UXnHym5H9VvEZ5R3EKJelxauvhO8uNfjEwjuKBYPFjNJbZUoiy9s+ZhFuM1a14ArRZlvQ/P96txt2GLHjScMMiZllO4a6NyLvMJQI7Ys0lI4JmKe5QEBdukvbSnFbnsaGMjwfeAksFiKyDRvHUjyc0EUaqU24hmhn+RZkYSjTqeBd567TlggPR696peqaA/tILWGUgcMhYBga9DFzkpgXc8NDaO6WqAFg2caM2/BAnZ9OyfzNYU7GXCyOAm6JNb+BaxYLg6rqCKr27cSipy4MG6LvgcOndRLAeQoNe55QMqi+lfH90v0nQsNIlIEgo8pXgwML6lef+dk98Ox46Egkq1txFKVrmmZ2bXBiLrdFQYqEhzpAMl8XlKg7Wq4R5q2NyOuVJwQ0RHVCbW6nHO/nT5eyldqEzvSY+pg6juxOIpgtD7XvlXRqq775OW0aVFGoL1ilKCjb//c3WU4oetWNcBizrkYb0BOny7P17s8Jx2ZVtvcup4KMOi3+NZJew1uRBKM8wgsSZ1CLmZq1ae/ECXI3kCAwEAAQ==");
   int accountEnableCount(3);
   int releaseBindingsExpected(0);
   int networkChangeCount(0);
   verifyReleaseBindings(alice, accountEnableCount, releaseBindingsExpected, networkChangeCount);
}

TEST_F(AccountModuleTest, DISABLED_VerifyReleaseBindingsDuringNetworkChangeWithTLS)
{
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.sipTransportType = SipAccountTransportType::SipAccountTransport_TLS;
   alice.config.settings.domain = "autotest.cpcapi2";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.outboundProxy = "";
   alice.config.settings.additionalCertPeerNames.clear();
   alice.config.settings.acceptedCertPublicKeys.push_back("MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEA3Q/3AQAW9Z8thdtQlnxAG6ilSn1FVH81ViruEEEyJBbxk0WPzroavMqRgvXJ6sIE+7iR8hHqY2BowKPhTg2D+WCvycT+i5YC7L37f/oWsnVp5xZZqTtiTTTXc+rSHgl2WO5tGxyGGWlUsslq3UXnHym5H9VvEZ5R3EKJelxauvhO8uNfjEwjuKBYPFjNJbZUoiy9s+ZhFuM1a14ArRZlvQ/P96txt2GLHjScMMiZllO4a6NyLvMJQI7Ys0lI4JmKe5QEBdukvbSnFbnsaGMjwfeAksFiKyDRvHUjyc0EUaqU24hmhn+RZkYSjTqeBd567TlggPR696peqaA/tILWGUgcMhYBga9DFzkpgXc8NDaO6WqAFg2caM2/BAnZ9OyfzNYU7GXCyOAm6JNb+BaxYLg6rqCKr27cSipy4MG6LvgcOndRLAeQoNe55QMqi+lfH90v0nQsNIlIEgo8pXgwML6lef+dk98Ox46Egkq1txFKVrmmZ2bXBiLrdFQYqEhzpAMl8XlKg7Wq4R5q2NyOuVJwQ0RHVCbW6nHO/nT5eyldqEzvSY+pg6juxOIpgtD7XvlXRqq775OW0aVFGoL1ilKCjb//c3WU4oetWNcBizrkYb0BOny7P17s8Jx2ZVtvcup4KMOi3+NZJew1uRBKM8wgsSZ1CLmZq1ae/ECXI3kCAwEAAQ==");
   int accountEnableCount(1);
   int releaseBindingsExpected(1);
   int networkChangeCount(1);
   verifyReleaseBindings(alice, accountEnableCount, releaseBindingsExpected, networkChangeCount);
}

TEST_F(AccountModuleTest, DISABLED_VerifyReleaseBindingsDuringNetworkChangeWithTLSWithRportDisabled)
{
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.sipTransportType = SipAccountTransportType::SipAccountTransport_TLS;
   alice.config.settings.domain = "autotest.cpcapi2";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.outboundProxy = "";
   alice.config.settings.useRport = false;
   alice.config.settings.additionalCertPeerNames.clear();
   alice.config.settings.acceptedCertPublicKeys.push_back("MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEA3Q/3AQAW9Z8thdtQlnxAG6ilSn1FVH81ViruEEEyJBbxk0WPzroavMqRgvXJ6sIE+7iR8hHqY2BowKPhTg2D+WCvycT+i5YC7L37f/oWsnVp5xZZqTtiTTTXc+rSHgl2WO5tGxyGGWlUsslq3UXnHym5H9VvEZ5R3EKJelxauvhO8uNfjEwjuKBYPFjNJbZUoiy9s+ZhFuM1a14ArRZlvQ/P96txt2GLHjScMMiZllO4a6NyLvMJQI7Ys0lI4JmKe5QEBdukvbSnFbnsaGMjwfeAksFiKyDRvHUjyc0EUaqU24hmhn+RZkYSjTqeBd567TlggPR696peqaA/tILWGUgcMhYBga9DFzkpgXc8NDaO6WqAFg2caM2/BAnZ9OyfzNYU7GXCyOAm6JNb+BaxYLg6rqCKr27cSipy4MG6LvgcOndRLAeQoNe55QMqi+lfH90v0nQsNIlIEgo8pXgwML6lef+dk98Ox46Egkq1txFKVrmmZ2bXBiLrdFQYqEhzpAMl8XlKg7Wq4R5q2NyOuVJwQ0RHVCbW6nHO/nT5eyldqEzvSY+pg6juxOIpgtD7XvlXRqq775OW0aVFGoL1ilKCjb//c3WU4oetWNcBizrkYb0BOny7P17s8Jx2ZVtvcup4KMOi3+NZJew1uRBKM8wgsSZ1CLmZq1ae/ECXI3kCAwEAAQ==");
   int accountEnableCount(1);
   int releaseBindingsExpected(0); // Setting to 0 as the network change results in same port, so no release binding is added
   int networkChangeCount(1);
   verifyReleaseBindings(alice, accountEnableCount, releaseBindingsExpected, networkChangeCount);
}

TEST_F(AccountModuleTest, DISABLED_VerifyReleaseBindingsDuringMultipleNetworkChangeWithTLS)
{
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.sipTransportType = SipAccountTransportType::SipAccountTransport_TLS;
   alice.config.settings.domain = "autotest.cpcapi2";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.outboundProxy = "";
   alice.config.settings.additionalCertPeerNames.clear();
   alice.config.settings.acceptedCertPublicKeys.push_back("MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEA3Q/3AQAW9Z8thdtQlnxAG6ilSn1FVH81ViruEEEyJBbxk0WPzroavMqRgvXJ6sIE+7iR8hHqY2BowKPhTg2D+WCvycT+i5YC7L37f/oWsnVp5xZZqTtiTTTXc+rSHgl2WO5tGxyGGWlUsslq3UXnHym5H9VvEZ5R3EKJelxauvhO8uNfjEwjuKBYPFjNJbZUoiy9s+ZhFuM1a14ArRZlvQ/P96txt2GLHjScMMiZllO4a6NyLvMJQI7Ys0lI4JmKe5QEBdukvbSnFbnsaGMjwfeAksFiKyDRvHUjyc0EUaqU24hmhn+RZkYSjTqeBd567TlggPR696peqaA/tILWGUgcMhYBga9DFzkpgXc8NDaO6WqAFg2caM2/BAnZ9OyfzNYU7GXCyOAm6JNb+BaxYLg6rqCKr27cSipy4MG6LvgcOndRLAeQoNe55QMqi+lfH90v0nQsNIlIEgo8pXgwML6lef+dk98Ox46Egkq1txFKVrmmZ2bXBiLrdFQYqEhzpAMl8XlKg7Wq4R5q2NyOuVJwQ0RHVCbW6nHO/nT5eyldqEzvSY+pg6juxOIpgtD7XvlXRqq775OW0aVFGoL1ilKCjb//c3WU4oetWNcBizrkYb0BOny7P17s8Jx2ZVtvcup4KMOi3+NZJew1uRBKM8wgsSZ1CLmZq1ae/ECXI3kCAwEAAQ==");
   int accountEnableCount(1);
   int releaseBindingsExpected(3);
   int networkChangeCount(3);
   verifyReleaseBindings(alice, accountEnableCount, releaseBindingsExpected, networkChangeCount);
}

TEST_F(AccountModuleTest, DISABLED_VerifyReleaseBindingsDuringMultipleNetworkChangeWithTCP)
{
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.sipTransportType = SipAccountTransportType::SipAccountTransport_TCP;
   int accountEnableCount(1);
   int releaseBindingsExpected(3);
   int networkChangeCount(3);
   verifyReleaseBindings(alice, accountEnableCount, releaseBindingsExpected, networkChangeCount);
}

TEST_F(AccountModuleTest, DISABLED_VerifyReleaseBindingsDuringMultipleNetworkChangeWithUDP)
{
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.sipTransportType = SipAccountTransportType::SipAccountTransport_UDP;
   int accountEnableCount(1);
   int releaseBindingsExpected(0); // Setting to 0 as the network change results in same port, so no release binding is added
   int networkChangeCount(3);
   verifyReleaseBindings(alice, accountEnableCount, releaseBindingsExpected, networkChangeCount);
}

TEST_F(AccountModuleTest, DISABLED_VerifyReleaseBindingsAlianzaCPE2)
{
   /*
    transport: 1
    alwaysRouteViaOutboundProxy: 0
    auth_realm:
    auth_username:
    displayName:
    domain: alz000.sip.qa.us.cymbus.net
    enableRegeventDeregistration: 0
    excludeEncryptedTransports: 0
    ignoreCertVerification: 0
    ipVersion: V4
    enableNat64Support: 1
    sslVersion: SSL_HIGHEST
    cipherSuite: TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256:TLS_AES_128_GCM_SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-SHA384:ECDHE-RSA-AES128-SHA256:DHE-RSA-AES256-SHA256:DHE-RSA-AES128-SHA256:ECDHE-ECDSA-AES256-SHA384:ECDHE-ECDSA-AES128-SHA256:ECDHE-RSA-AES256-SHA:ECDHE-RSA-AES128-SHA:AES256-GCM-SHA384:AES128-GCM-SHA256:AES256-SHA256:AES128-SHA256:AES256-SHA:AES128-SHA:ECDHE-ECDSA-AES256-SHA:ECDHE-ECDSA-AES128-SHA:DHE-RSA-AES256-SHA:DHE-RSA-AES128-SHA
    maxSipPort: 0
    minimumRegistrationIntervalSeconds: 20
    maximumRegistrationIntervalSeconds: 3600
    minSipPort: 0
    enableRegeventDeregistration: 0
    enableDNSResetOnRegistrationRefresh: 0
    enableAuthResetUponDNSReset: 0
    nameServers: *******, *******
    otherNonEscapedCharsInUri:
    outboundProxy:
    registrationIntervalSeconds: 3600
    reRegisterOnResponseTypes: REGISTER/500, REGISTER/503, REGISTER/504, REGISTER/407, REGISTER/408
    sessionTimerMode: 1
    sessionTimeSeconds: 0
    sipQosSettings: 40
    sipTransportType: 1
    stunServer:
    stunServerSource: 0
    tcpKeepAliveTime: 30
    tunnelConfig: disabled
    udpKeepAliveTime: 30
    useGruu: 0
    useImsAuthHeader: 0
    useMethodParamInReferTo: 0
    useInstanceId: 0
    answerModeSupported: 0
    useOutbound: 0
    userAgent: Cymbus2 0.1.7447
    useRegistrar: 1
    username: sipUserThirdVineet
    useRport: 0
    XCAPRoot:
    useRinstance: 1
    transportHoldover: 0
    overrideMsecsTimerF: 0
   */

   TestAccount alice("<EMAIL>", Account_NoInit); // Extension 7769
   alice.config.settings.domain = "alz000.sip.qa.us.cymbus.net";
   alice.config.settings.outboundProxy = "";
   alice.config.settings.username = "sipUserSecondVineet";
   alice.config.settings.password = "c2QASipPass";
   alice.config.settings.useOutbound = false;
   alice.config.settings.useRport = true;
   alice.config.settings.useRinstance = true;
   alice.config.settings.useRegistrar = true;
   alice.config.settings.alwaysRouteViaOutboundProxy = false;
   alice.config.settings.displayName = "";
   alice.config.settings.registrationIntervalSeconds = 3600;
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.sslVersion = SSLVersion::SSL_HIGHEST;
   alice.config.settings.ipVersion = IpVersion::IpVersion_V4;
   alice.config.settings.auth_username = "";
   alice.config.settings.userAgent = "Cymbus2 0.1.7447";
   alice.config.settings.enableNat64Support = true;
   alice.config.settings.nameServers.push_back("*******");
   alice.config.settings.nameServers.push_back("*******");

   // alice.init();
   // alice.enable();

   int accountEnableCount(1);
   int networkChangeCount(1);
   // Setting to twice the number of network change as the network change registration will be challenged and the release-bindings will have to be added again
   int releaseBindingsExpected(2 * networkChangeCount);

   verifyReleaseBindings(alice, accountEnableCount, releaseBindingsExpected, networkChangeCount);

   TestAccount bob("<EMAIL>", Account_NoInit); // Extension 7751
   bob.config.settings.domain = "alz000.sip.qa.us.cymbus.net";
   bob.config.settings.outboundProxy = "";
   bob.config.settings.username = "sipUserSecondJason";
   bob.config.settings.password = "c2QASipPass";
   bob.config.settings.useOutbound = false;
   bob.config.settings.useRport = true;
   bob.config.settings.useRinstance = true;
   bob.config.settings.useRegistrar = true;
   bob.config.settings.alwaysRouteViaOutboundProxy = false;
   bob.config.settings.displayName = "";
   bob.config.settings.registrationIntervalSeconds = 3600;
   bob.config.settings.sipTransportType = SipAccountTransport_UDP;
   bob.config.settings.sslVersion = SSLVersion::SSL_HIGHEST;
   bob.config.settings.ipVersion = IpVersion::IpVersion_V4;
   bob.config.settings.auth_username = "";
   bob.config.settings.userAgent = "Cymbus2 0.1.7447";
   bob.config.settings.enableNat64Support = true;
   bob.config.settings.nameServers.push_back("*******");
   bob.config.settings.nameServers.push_back("*******");
   bob.init();
   bob.enable();

   std::this_thread::sleep_for(std::chrono::milliseconds(10000));

   // Bob calls Charlie then Charlie hangs up
   CPCAPI2::SipConversation::SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall, "sip:<EMAIL>"); // alice.config.uri());
   bob.conversation->start(bobCall);

   auto bobConversationEvents = std::async(std::launch::async, [&] ()
   {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing(bob, bobCall, "sip:<EMAIL>"); // alice.config.uri());
      assertConversationStateChanged(bob, bobCall, CPCAPI2::SipConversation::ConversationState_RemoteRinging);
      assertConversationMediaChanged(bob, bobCall, CPCAPI2::SipConversation::MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, CPCAPI2::SipConversation::ConversationState_Early);
      assertConversationStateChanged(bob, bobCall, CPCAPI2::SipConversation::ConversationState_Connected);
      assertConversationEnded(bob, bobCall, CPCAPI2::SipConversation::ConversationEndReason_UserTerminatedRemotely);
   });

   auto aliceConversationEvents = std::async(std::launch::async, [&] ()
   {
      // Wait for the expected state changes and for the call to end
      CPCAPI2::SipConversation::SipConversationHandle aliceCall;
      // assertNewConversationIncoming(alice, &aliceCall, "sip:7751@************"); // sip:7751@************ sip:<EMAIL>"); // bob.config.uri());
      {
         CPCAPI2::SipConversation::NewConversationEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onNewConversation", 15000, AlwaysTruePred(), aliceCall, evt));
         ASSERT_EQ(CPCAPI2::SipConversation::ConversationType_Incoming, evt.conversationType);
      }
      alice.conversation->sendRingingResponse(aliceCall);
      assertConversationStateChanged(alice, aliceCall, CPCAPI2::SipConversation::ConversationState_LocalRinging);
      alice.conversation->accept(aliceCall);
      assertConversationMediaChanged(alice, aliceCall, CPCAPI2::SipConversation::MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, CPCAPI2::SipConversation::ConversationState_Connected);
      alice.conversation->end(aliceCall);
      assertConversationEnded(alice, aliceCall, CPCAPI2::SipConversation::ConversationEndReason_UserTerminatedLocally);
   });

   waitFor2Ms(bobConversationEvents, aliceConversationEvents, std::chrono::milliseconds(60000));
}

TEST_F(AccountModuleTest, DISABLED_VerifyReleaseBindingsAlianzaCallAfterNetworkChangeCPE2)
{
   SipAccountSettings settings;
   settings.domain = "alz000.sip.qa.us.cymbus.net";
   settings.outboundProxy = "";
   settings.useOutbound = false;
   settings.useRport = true;
   settings.useRinstance = true;
   settings.useRegistrar = true;
   settings.alwaysRouteViaOutboundProxy = false;
   settings.displayName = "";
   settings.registrationIntervalSeconds = 3600;
   settings.sipTransportType = SipAccountTransport_UDP;
   settings.sslVersion = SSLVersion::SSL_HIGHEST;
   settings.ipVersion = IpVersion::IpVersion_V4;
   settings.auth_username = "";
   settings.userAgent = "Cymbus2 0.1.7447";
   settings.enableNat64Support = true;
   settings.nameServers.push_back("*******");
   settings.nameServers.push_back("*******");

   TestAccount alice("<EMAIL>", Account_NoInit); // Extension 7769
   alice.config.settings = settings;
   alice.config.settings.username = "sipUserSecondVineet";
   alice.config.settings.password = "c2QASipPass";
   alice.init();
   alice.enable();

   TestAccount bob("<EMAIL>", Account_NoInit); // Extension 7751
   bob.config.settings = settings;
   bob.config.settings.username = "sipUserSecondJason";
   bob.config.settings.password = "c2QASipPass";
   bob.init();
   bob.enable();

   std::this_thread::sleep_for(std::chrono::milliseconds(10000));

   {
      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> interfaces;
      interfaces.insert("*******");
      alice.network->setMockInterfaces(interfaces);
      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(10000));

   // Bob calls Charlie then Charlie hangs up
   CPCAPI2::SipConversation::SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall, "sip:<EMAIL>"); // alice.config.uri());
   bob.conversation->start(bobCall);

   auto bobConversationEvents = std::async(std::launch::async, [&] ()
   {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing(bob, bobCall, "sip:<EMAIL>"); // alice.config.uri());
      assertConversationStateChanged(bob, bobCall, CPCAPI2::SipConversation::ConversationState_RemoteRinging);
      assertConversationMediaChanged(bob, bobCall, CPCAPI2::SipConversation::MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, CPCAPI2::SipConversation::ConversationState_Early);
      assertConversationStateChanged(bob, bobCall, CPCAPI2::SipConversation::ConversationState_Connected);
      assertConversationEnded(bob, bobCall, CPCAPI2::SipConversation::ConversationEndReason_UserTerminatedRemotely);
   });

   auto aliceConversationEvents = std::async(std::launch::async, [&] ()
   {
      // Wait for the expected state changes and for the call to end
      CPCAPI2::SipConversation::SipConversationHandle aliceCall;
      {
         CPCAPI2::SipConversation::NewConversationEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onNewConversation", 15000, AlwaysTruePred(), aliceCall, evt));
         ASSERT_EQ(CPCAPI2::SipConversation::ConversationType_Incoming, evt.conversationType);
      }
      alice.conversation->sendRingingResponse(aliceCall);
      assertConversationStateChanged(alice, aliceCall, CPCAPI2::SipConversation::ConversationState_LocalRinging);
      alice.conversation->accept(aliceCall);
      assertConversationMediaChanged(alice, aliceCall, CPCAPI2::SipConversation::MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, CPCAPI2::SipConversation::ConversationState_Connected);
      alice.conversation->end(aliceCall);
      assertConversationEnded(alice, aliceCall, CPCAPI2::SipConversation::ConversationEndReason_UserTerminatedLocally);
   });

   waitFor2Ms(bobConversationEvents, aliceConversationEvents, std::chrono::milliseconds(60000));
}

TEST_F(AccountModuleTest, DISABLED_VerifyReleaseBindingsAlianzaOutgoingCallCPE2)
{
   SipAccountSettings settings;
   settings.domain = "alz000.sip.qa.us.cymbus.net";
   settings.outboundProxy = "";
   settings.useOutbound = false;
   settings.useRport = true;
   settings.useRinstance = true;
   settings.useRegistrar = true;
   settings.alwaysRouteViaOutboundProxy = false;
   settings.displayName = "";
   settings.registrationIntervalSeconds = 3600;
   settings.sipTransportType = SipAccountTransport_UDP;
   settings.sslVersion = SSLVersion::SSL_HIGHEST;
   settings.ipVersion = IpVersion::IpVersion_V4;
   settings.auth_username = "";
   settings.userAgent = "Cymbus2 0.1.7447";
   settings.enableNat64Support = true;
   settings.nameServers.push_back("*******");
   settings.nameServers.push_back("*******");

   TestAccount bob("<EMAIL>", Account_NoInit); // Extension 7751
   bob.config.settings = settings;
   bob.config.settings.username = "sipUserSecondJason";
   bob.config.settings.password = "c2QASipPass";
   bob.init();
   bob.enable();

   // Bob calls Charlie then Charlie hangs up
   CPCAPI2::SipConversation::SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall, "sip:<EMAIL>");
   bob.conversation->start(bobCall);

   auto bobConversationEvents = std::async(std::launch::async, [&] ()
   {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing(bob, bobCall, "sip:<EMAIL>");
      assertConversationStateChanged(bob, bobCall, CPCAPI2::SipConversation::ConversationState_RemoteRinging);
      assertConversationMediaChanged(bob, bobCall, CPCAPI2::SipConversation::MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, CPCAPI2::SipConversation::ConversationState_Early);
      assertConversationStateChanged(bob, bobCall, CPCAPI2::SipConversation::ConversationState_Connected);
      assertConversationEnded(bob, bobCall, CPCAPI2::SipConversation::ConversationEndReason_UserTerminatedRemotely);
   });

   waitForMs(bobConversationEvents, std::chrono::milliseconds(60000));
}

TEST_F(AccountModuleTest, DISABLED_VerifyReleaseBindingsAlianzaCPE1)
{
   TestAccount alice("<EMAIL>", Account_NoInit); // Extension 7769

   alice.config.settings.domain = "sbcrtp.d2.alianza.com";
   alice.config.settings.outboundProxy = "";
   alice.config.settings.username = "ylUHxL1YThqXcKesiqZG-Q";
   alice.config.settings.password = "hmWZDG29";
   alice.config.settings.useOutbound = false;
   alice.config.settings.useRport = true;
   alice.config.settings.useRinstance = true;
   alice.config.settings.useRegistrar = true;
   alice.config.settings.alwaysRouteViaOutboundProxy = false;
   alice.config.settings.displayName = "Alice Sdk";
   alice.config.settings.registrationIntervalSeconds = 3600;
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.sslVersion = SSLVersion::SSL_HIGHEST;
   alice.config.settings.ipVersion = IpVersion::IpVersion_V4;
   alice.config.settings.auth_username = "";
   alice.config.settings.userAgent = "Cymbus release 6.5.3 stamp 110171";
   alice.config.settings.enableNat64Support = true;
   alice.config.settings.nameServers.push_back("*******");
   alice.config.settings.nameServers.push_back("*******");

   int accountEnableCount(1);
   int releaseBindingsExpected(6);
   int networkChangeCount(3);
   verifyReleaseBindings(alice, accountEnableCount, releaseBindingsExpected, networkChangeCount);
}

TEST_F(AccountModuleTest, DISABLED_VerifyReleaseBindingsAlianzaCallAfterNetworkChangeCPE1)
{
   SipAccountSettings settings;
   settings.domain = "sbcrtp.d2.alianza.com";
   settings.outboundProxy = "";
   settings.useOutbound = false;
   settings.useRport = true;
   settings.useRinstance = true;
   settings.useRegistrar = true;
   settings.alwaysRouteViaOutboundProxy = false;
   settings.displayName = "";
   settings.registrationIntervalSeconds = 3600;
   settings.sipTransportType = SipAccountTransport_UDP;
   settings.sslVersion = SSLVersion::SSL_HIGHEST;
   settings.ipVersion = IpVersion::IpVersion_V4;
   settings.auth_username = "";
   settings.userAgent = "Cymbus2 0.1.7447";
   settings.enableNat64Support = true;
   settings.nameServers.push_back("*******");
   settings.nameServers.push_back("*******");

   TestAccount alice("alice", Account_NoInit); // Extension 7769
   alice.config.settings = settings;
   alice.config.settings.username = "ylUHxL1YThqXcKesiqZG-Q";
   alice.config.settings.password = "hmWZDG29";
   alice.init();
   alice.enable();

   TestAccount bob("bob", Account_NoInit); // Extension 7751
   bob.config.settings = settings;
   bob.config.settings.username = "CDil_IWhQu2eiWOBAolTBw";
   bob.config.settings.password = "c6xxzKkG";
   bob.init();
   bob.enable();

   std::this_thread::sleep_for(std::chrono::milliseconds(10000));

   {
      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> interfaces;
      interfaces.insert("*******");
      alice.network->setMockInterfaces(interfaces);
      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(10000));

   // Bob calls Charlie then Charlie hangs up
   CPCAPI2::SipConversation::SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall, "sip:<EMAIL>"); // alice.config.uri());
   bob.conversation->start(bobCall);

   auto bobConversationEvents = std::async(std::launch::async, [&] ()
   {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing(bob, bobCall, "sip:<EMAIL>"); // alice.config.uri());
      assertConversationStateChanged(bob, bobCall, CPCAPI2::SipConversation::ConversationState_RemoteRinging);
      assertConversationMediaChanged(bob, bobCall, CPCAPI2::SipConversation::MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, CPCAPI2::SipConversation::ConversationState_Connected);
      assertConversationEnded(bob, bobCall, CPCAPI2::SipConversation::ConversationEndReason_UserTerminatedRemotely);
   });

   auto aliceConversationEvents = std::async(std::launch::async, [&] ()
   {
      // Wait for the expected state changes and for the call to end
      CPCAPI2::SipConversation::SipConversationHandle aliceCall;
      {
         CPCAPI2::SipConversation::NewConversationEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onNewConversation", 15000, AlwaysTruePred(), aliceCall, evt));
         ASSERT_EQ(CPCAPI2::SipConversation::ConversationType_Incoming, evt.conversationType);
      }
      alice.conversation->sendRingingResponse(aliceCall);
      assertConversationStateChanged(alice, aliceCall, CPCAPI2::SipConversation::ConversationState_LocalRinging);
      alice.conversation->accept(aliceCall);
      assertConversationMediaChanged(alice, aliceCall, CPCAPI2::SipConversation::MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, CPCAPI2::SipConversation::ConversationState_Connected);
      alice.conversation->end(aliceCall);
      assertConversationEnded(alice, aliceCall, CPCAPI2::SipConversation::ConversationEndReason_UserTerminatedLocally);
   });

   waitFor2Ms(bobConversationEvents, aliceConversationEvents, std::chrono::milliseconds(60000));
}

TEST_F(AccountModuleTest, DISABLED_VerifyReleaseBindingsAlianzaOutgoingCallCPE1)
{
   SipAccountSettings settings;
   settings.domain = "sbcrtp.d2.alianza.com";
   settings.outboundProxy = "";
   settings.useOutbound = false;
   settings.useRport = true;
   settings.useRinstance = true;
   settings.useRegistrar = true;
   settings.alwaysRouteViaOutboundProxy = false;
   settings.displayName = "";
   settings.registrationIntervalSeconds = 3600;
   settings.sipTransportType = SipAccountTransport_UDP;
   settings.sslVersion = SSLVersion::SSL_HIGHEST;
   settings.ipVersion = IpVersion::IpVersion_V4;
   settings.auth_username = "";
   settings.userAgent = "Cymbus2 0.1.7447";
   settings.enableNat64Support = true;
   settings.nameServers.push_back("*******");
   settings.nameServers.push_back("*******");

   TestAccount bob("bob", Account_NoInit); // Extension 7751
   bob.config.settings = settings;
   bob.config.settings.username = "CDil_IWhQu2eiWOBAolTBw";
   bob.config.settings.password = "c6xxzKkG";
   bob.init();
   bob.enable();

   // Bob calls Charlie then Charlie hangs up
   CPCAPI2::SipConversation::SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall, "sip:<EMAIL>"); // alice.config.uri());
   bob.conversation->start(bobCall);

   auto bobConversationEvents = std::async(std::launch::async, [&] ()
   {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing(bob, bobCall, "sip:<EMAIL>"); // alice.config.uri());
      assertConversationStateChanged(bob, bobCall, CPCAPI2::SipConversation::ConversationState_RemoteRinging);
      assertConversationMediaChanged(bob, bobCall, CPCAPI2::SipConversation::MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, CPCAPI2::SipConversation::ConversationState_Connected);
      assertConversationEnded(bob, bobCall, CPCAPI2::SipConversation::ConversationEndReason_UserTerminatedRemotely);
   });

   waitForMs(bobConversationEvents, std::chrono::milliseconds(60000));
}

TEST_F(AccountModuleTest, MultipleFastEnableBeforeDisableComplete) {
   TestAccount alice("alice");

   for (size_t i=0; i<3; i++)
   {
      alice.account->disable(alice.handle);
      alice.account->enable(alice.handle);
      assertAccountDeregistering(alice);
      assertAccountDeregistered(alice);
      assertAccountRegistering(alice);
      assertAccountRegistered(alice);
   }
}

TEST_F(AccountModuleTest, AccountDisableSubscribe) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   // make an outgoing subscription from Alice to Bob using the demo.xten.com server
   SipEventSubscriptionHandle aliceSubs = alice.subs->createSubscription(alice.handle);
   SipEventSubscriptionSettings subsSettings;
   subsSettings.eventPackage = "cpcapi2test";
   subsSettings.expiresSeconds = 3600;
   subsSettings.supportedMimeTypes.push_back(CPCAPI2::MimeType("application", "cpcapi2test"));
   alice.subs->applySubscriptionSettings(aliceSubs, subsSettings);
   alice.subs->addParticipant(aliceSubs, bob.config.uri());
   alice.subs->start(aliceSubs);

   // Overview of Bob's thread:
   //  -
   auto bobEvents = std::async(std::launch::async, [&] () {
      SipEventSubscriptionHandle bobSubs = 0;
      {
         SipEventSubscriptionHandle h;
         NewSubscriptionEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.subsEvents, "SipEventSubscriptionHandler::onNewSubscription",
            5000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, bob.handle);
         bobSubs = h;
         ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
         ASSERT_EQ(evt.remoteDisplayName, alice.config.name);
         ASSERT_EQ(evt.supportedMimeTypes.size(), 1);
      }

      // Bob accepts the subscription (200 OK) -- this should trigger state transitions for both Bob and Alice
      SipEventState evt1234;
      evt1234.contentUTF8 = "1,2,3,4\r\n";
      evt1234.contentLength = 9;
      evt1234.eventPackage = "cpcapi2test";
      evt1234.expiresTimeMs = 3598;
      evt1234.mimeType = "application";
      evt1234.mimeSubType = "cpcapi2test";
      ASSERT_EQ(bob.subs->accept(bobSubs, evt1234), kSuccess);
      {
         SipEventSubscriptionHandle h;
         SubscriptionStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.subsEvents, "SipEventSubscriptionHandler::onSubscriptionStateChanged",
            15000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(500));

      SipEventState evt5678;
      evt5678.contentUTF8 = "5,6,7,8\r\n";
      evt5678.contentLength = 9;
      evt5678.eventPackage = "cpcapi2test";
      evt5678.expiresTimeMs = 3598;
      evt5678.mimeType = "application";
      evt5678.mimeSubType = "cpcapi2test";
      ASSERT_EQ(bob.subs->notify(bobSubs, evt5678), kSuccess);

      std::this_thread::sleep_for(std::chrono::milliseconds(500));
   });

   // Overview of Alice's thread:
   //  -
   auto aliceEvents = std::async(std::launch::async, [&] () {
      {
         SipEventSubscriptionHandle h;
         NewSubscriptionEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.subsEvents, "SipEventSubscriptionHandler::onNewSubscription",
            15000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, alice.handle);
         ASSERT_EQ(h, aliceSubs);
         ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Outgoing);
         ASSERT_EQ(evt.remoteAddress, bob.config.uri());
         ASSERT_EQ(evt.remoteDisplayName, "");
      }

      {
         SipEventSubscriptionHandle h;
         SubscriptionStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.subsEvents, "SipEventSubscriptionHandler::onSubscriptionStateChanged",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(aliceSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      {
         SipEventSubscriptionHandle h;
         IncomingEventStateEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.subsEvents, "SipEventSubscriptionHandler::onIncomingEventState",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(aliceSubs, h);
         ASSERT_EQ(evt.eventState.contentLength, 9);
         //std::cout << "content is " << evt.eventState.content << std::endl;
         ASSERT_EQ(strcmp(evt.eventState.contentUTF8.c_str(), "1,2,3,4\r\n"), 0);
         ASSERT_EQ(evt.eventState.eventPackage, "cpcapi2test");
         ASSERT_NEAR(evt.eventState.expiresTimeMs, 3598, 10);
         ASSERT_EQ(evt.eventState.mimeType, "application");
         ASSERT_EQ(evt.eventState.mimeSubType, "cpcapi2test");
      }

      {
         SipEventSubscriptionHandle h;
         IncomingEventStateEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.subsEvents, "SipEventSubscriptionHandler::onIncomingEventState",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(aliceSubs, h);
         ASSERT_EQ(evt.eventState.contentLength, 9);
         //std::cout << "content is " << evt.eventState.content << std::endl;
         ASSERT_EQ(strcmp(evt.eventState.contentUTF8.c_str(), "5,6,7,8\r\n"), 0);
         ASSERT_EQ(evt.eventState.eventPackage, "cpcapi2test");
         ASSERT_NEAR(evt.eventState.expiresTimeMs, 3598, 10);
         ASSERT_EQ(evt.eventState.mimeType, "application");
         ASSERT_EQ(evt.eventState.mimeSubType, "cpcapi2test");
      }

   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(10000));
}

TEST_F(AccountModuleTest, TestEnableLegacyServerConnect)
{
   TestAccount alice("alice", Account_NoInit);
   ASSERT_EQ(alice.config.settings.enableLegacyServerConnect, false);
   alice.config.settings.enableLegacyServerConnect = true;
   alice.init();
   ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);
   assertAccountRegistering(alice);
   assertAccountRegistered(alice);
}

TEST_F(AccountModuleTest, AccountReleaseSubscribe) {
   TestAccount alice("alice", Account_Enable, false);
   TestAccount bob("bob", Account_Enable, false);

   // make an outgoing subscription from Alice to Bob using the demo.xten.com server
   SipEventSubscriptionHandle aliceSubs = alice.subs->createSubscription(alice.handle);
   SipEventSubscriptionSettings subsSettings;
   subsSettings.eventPackage = "cpcapi2test";
   subsSettings.expiresSeconds = 3600;
   subsSettings.supportedMimeTypes.push_back(CPCAPI2::MimeType("application", "cpcapi2test"));
   alice.subs->applySubscriptionSettings(aliceSubs, subsSettings);
   alice.subs->addParticipant(aliceSubs, bob.config.uri());
   alice.subs->start(aliceSubs);

   // Overview of Bob's thread:
   //  -
   auto bobEvents = std::async(std::launch::async, [&] () {
      SipEventSubscriptionHandle bobSubs = 0;
      {
         SipEventSubscriptionHandle h;
         NewSubscriptionEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.subsEvents, "SipEventSubscriptionHandler::onNewSubscription",
            5000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, bob.handle);
         bobSubs = h;
         ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
         ASSERT_EQ(evt.remoteDisplayName, alice.config.name);
         ASSERT_EQ(evt.supportedMimeTypes.size(), 1);
      }

      // Bob accepts the subscription (200 OK) -- this should trigger state transitions for both Bob and Alice
      SipEventState evt1234;
      evt1234.contentUTF8 = "1,2,3,4\r\n";
      evt1234.contentLength = 9;
      evt1234.eventPackage = "cpcapi2test";
      evt1234.expiresTimeMs = 3598;
      evt1234.mimeType = "application";
      evt1234.mimeSubType = "cpcapi2test";
      ASSERT_EQ(bob.subs->accept(bobSubs, evt1234), kSuccess);
      {
         SipEventSubscriptionHandle h;
         SubscriptionStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.subsEvents, "SipEventSubscriptionHandler::onSubscriptionStateChanged",
            15000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }


      std::this_thread::sleep_for(std::chrono::milliseconds(500));

      SipEventState evt5678;
      evt5678.contentUTF8 = "5,6,7,8\r\n";
      evt5678.contentLength = 9;
      evt5678.eventPackage = "cpcapi2test";
      evt5678.expiresTimeMs = 3598;
      evt5678.mimeType = "application";
      evt5678.mimeSubType = "cpcapi2test";
      ASSERT_EQ(bob.subs->notify(bobSubs, evt5678), kSuccess);

      std::this_thread::sleep_for(std::chrono::milliseconds(500));
   });

   // Overview of Alice's thread:
   //  -
   auto aliceEvents = std::async(std::launch::async, [&] () {
      {
         SipEventSubscriptionHandle h;
         NewSubscriptionEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.subsEvents, "SipEventSubscriptionHandler::onNewSubscription",
            15000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, alice.handle);
         ASSERT_EQ(h, aliceSubs);
         ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Outgoing);
         ASSERT_EQ(evt.remoteAddress, bob.config.uri());
         ASSERT_EQ(evt.remoteDisplayName, "");
      }

      {
         SipEventSubscriptionHandle h;
         SubscriptionStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.subsEvents, "SipEventSubscriptionHandler::onSubscriptionStateChanged",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(aliceSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      {
         SipEventSubscriptionHandle h;
         IncomingEventStateEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.subsEvents, "SipEventSubscriptionHandler::onIncomingEventState",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(aliceSubs, h);
         ASSERT_EQ(evt.eventState.contentLength, 9);
         //std::cout << "content is " << evt.eventState.content << std::endl;
         ASSERT_EQ(strcmp(evt.eventState.contentUTF8.c_str(), "1,2,3,4\r\n"), 0);
         ASSERT_EQ(evt.eventState.eventPackage, "cpcapi2test");
         ASSERT_NEAR(evt.eventState.expiresTimeMs, 3598, 10);
         ASSERT_EQ(evt.eventState.mimeType, "application");
         ASSERT_EQ(evt.eventState.mimeSubType, "cpcapi2test");
      }

      {
         SipEventSubscriptionHandle h;
         IncomingEventStateEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.subsEvents, "SipEventSubscriptionHandler::onIncomingEventState",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(aliceSubs, h);
         ASSERT_EQ(evt.eventState.contentLength, 9);
         //std::cout << "content is " << evt.eventState.content << std::endl;
         ASSERT_EQ(strcmp(evt.eventState.contentUTF8.c_str(), "5,6,7,8\r\n"), 0);
         ASSERT_EQ(evt.eventState.eventPackage, "cpcapi2test");
         ASSERT_NEAR(evt.eventState.expiresTimeMs, 3598, 10);
         ASSERT_EQ(evt.eventState.mimeType, "application");
         ASSERT_EQ(evt.eventState.mimeSubType, "cpcapi2test");
      }

   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(10000));
}

TEST_F(AccountModuleTest, AccountDestroySubscribe) {
   TestAccount alice("alice", Account_Enable, false);
   TestAccount bob("bob", Account_Enable, false);

   // make an outgoing subscription from Alice to Bob using the demo.xten.com server
   SipEventSubscriptionHandle aliceSubs = alice.subs->createSubscription(alice.handle);
   SipEventSubscriptionSettings subsSettings;
   subsSettings.eventPackage = "cpcapi2test";
   subsSettings.expiresSeconds = 3600;
   subsSettings.supportedMimeTypes.push_back(CPCAPI2::MimeType("application", "cpcapi2test"));
   alice.subs->applySubscriptionSettings(aliceSubs, subsSettings);
   alice.subs->addParticipant(aliceSubs, bob.config.uri());
   alice.subs->start(aliceSubs);

   // Overview of Bob's thread:
   //  -
   auto bobEvents = std::async(std::launch::async, [&] () {
      SipEventSubscriptionHandle bobSubs = 0;
      {
         SipEventSubscriptionHandle h;
         NewSubscriptionEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.subsEvents, "SipEventSubscriptionHandler::onNewSubscription",
            5000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, bob.handle);
         bobSubs = h;
         ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
         ASSERT_EQ(evt.remoteDisplayName, alice.config.name);
         ASSERT_EQ(evt.supportedMimeTypes.size(), 1);
      }

      // Bob accepts the subscription (200 OK) -- this should trigger state transitions for both Bob and Alice
      SipEventState evt1234;
      evt1234.contentUTF8 = "1,2,3,4\r\n";
      evt1234.contentLength = 9;
      evt1234.eventPackage = "cpcapi2test";
      evt1234.expiresTimeMs = 3598;
      evt1234.mimeType = "application";
      evt1234.mimeSubType = "cpcapi2test";
      ASSERT_EQ(bob.subs->accept(bobSubs, evt1234), kSuccess);
      {
         SipEventSubscriptionHandle h;
         SubscriptionStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.subsEvents, "SipEventSubscriptionHandler::onSubscriptionStateChanged",
            15000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }


      std::this_thread::sleep_for(std::chrono::milliseconds(500));

      SipEventState evt5678;
      evt5678.contentUTF8 = "5,6,7,8\r\n";
      evt5678.contentLength = 9;
      evt5678.eventPackage = "cpcapi2test";
      evt5678.expiresTimeMs = 3598;
      evt5678.mimeType = "application";
      evt5678.mimeSubType = "cpcapi2test";
      ASSERT_EQ(bob.subs->notify(bobSubs, evt5678), kSuccess);

      std::this_thread::sleep_for(std::chrono::milliseconds(500));
   });

   // Overview of Alice's thread:
   //  -
   auto aliceEvents = std::async(std::launch::async, [&] () {
      {
         SipEventSubscriptionHandle h;
         NewSubscriptionEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.subsEvents, "SipEventSubscriptionHandler::onNewSubscription",
            15000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, alice.handle);
         ASSERT_EQ(h, aliceSubs);
         ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Outgoing);
         ASSERT_EQ(evt.remoteAddress, bob.config.uri());
         ASSERT_EQ(evt.remoteDisplayName, "");
      }

      {
         SipEventSubscriptionHandle h;
         SubscriptionStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.subsEvents, "SipEventSubscriptionHandler::onSubscriptionStateChanged",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(aliceSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      {
         SipEventSubscriptionHandle h;
         IncomingEventStateEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.subsEvents, "SipEventSubscriptionHandler::onIncomingEventState",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(aliceSubs, h);
         ASSERT_EQ(evt.eventState.contentLength, 9);
         //std::cout << "content is " << evt.eventState.content << std::endl;
         ASSERT_EQ(strcmp(evt.eventState.contentUTF8.c_str(), "1,2,3,4\r\n"), 0);
         ASSERT_EQ(evt.eventState.eventPackage, "cpcapi2test");
         ASSERT_NEAR(evt.eventState.expiresTimeMs, 3598, 10);
         ASSERT_EQ(evt.eventState.mimeType, "application");
         ASSERT_EQ(evt.eventState.mimeSubType, "cpcapi2test");
      }

      {
         SipEventSubscriptionHandle h;
         IncomingEventStateEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.subsEvents, "SipEventSubscriptionHandler::onIncomingEventState",
            5000,
            AlwaysTruePred(), h, evt));
         ASSERT_EQ(aliceSubs, h);
         ASSERT_EQ(evt.eventState.contentLength, 9);
         //std::cout << "content is " << evt.eventState.content << std::endl;
         ASSERT_EQ(strcmp(evt.eventState.contentUTF8.c_str(), "5,6,7,8\r\n"), 0);
         ASSERT_EQ(evt.eventState.eventPackage, "cpcapi2test");
         ASSERT_NEAR(evt.eventState.expiresTimeMs, 3598, 10);
         ASSERT_EQ(evt.eventState.mimeType, "application");
         ASSERT_EQ(evt.eventState.mimeSubType, "cpcapi2test");
      }

   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   alice.account->destroy(alice.handle);
   bob.account->destroy(bob.handle);

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(10000));
}

TEST_F(AccountModuleTest, AccountDoubleEnable) {
   TestAccount alice("alice");
   alice.account->enable(alice.handle);
}

TEST_F(AccountModuleTest, AccountDoubleDisable) {
   TestAccount alice("alice");
   alice.account->disable(alice.handle);
   assertAccountDeregistering(alice);
   assertAccountDeregistered(alice);
   // should be ignored
   alice.account->disable(alice.handle);

   CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
   ASSERT_FALSE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged",
      __FILE__, 5000, CPCAPI2::test::AlwaysTruePred(), alice.handle, evt));
      
   alice.enable();
}

TEST_F(AccountModuleTest, AccountEnableNoDisable) {
   TestAccount("alice", Account_Enable, false);
}

TEST_F(AccountModuleTest, AccountDestroy) {
   TestAccount alice("alice", Account_Enable, false);
   alice.account->disable(alice.handle);
   alice.account->destroy(alice.handle);
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
}

TEST_F(AccountModuleTest, AccountDestroyNoDisable) {
   TestAccount alice("alice", Account_Enable, false);
   alice.account->destroy(alice.handle);
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
}

TEST_F(AccountModuleTest, DisableInvalidAccount) {
   TestAccount alice("alice", Account_Init);
   alice.account->disable(0xdeaddead);
   PhoneErrorEvent evt;
   std::string module;
   assertPhoneError(alice, "SipAccountInterface");
}

TEST_F(AccountModuleTest, MultipleRegisterUnregisterNoReinit) {
   TestAccount alice("alice");

   for(size_t i=0; i<5; i++)
   {
      alice.account->disable(alice.handle);
      assertAccountDeregistering(alice);
      assertAccountDeregistered(alice);
      alice.account->enable(alice.handle);
      assertAccountRegistering(alice);
      assertAccountRegistered(alice);
      CPCAPI2::SipConversation::SipConversationHandle callh = alice.conversation->createConversation(alice.handle);
      alice.conversation->addParticipant(callh, "sip:<EMAIL>");
   }
}

TEST_F(AccountModuleTest, ReleaseWithoutDisable) {
   TestAccount alice("alice", Account_Init, false);
   alice.enable();
}

TEST_F(AccountModuleTest, RequestRefresh) {
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.registrationIntervalSeconds = 180;
   alice.enable();
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.account->requestRegistrationRefresh(alice.handle, 300);
   assertAccountRefreshing(alice);
   assertAccountRegistered(alice);
}

#ifndef ANDROID
TEST_F(AccountModuleTest, RequestRefreshDefault)
{
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_5060 = runRepro("repro_5060.config");

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.registrationIntervalSeconds = 180;
   alice.config.settings.outboundProxy = "127.0.0.1";
   alice.enable();
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.account->requestRegistrationRefresh(alice.handle, 300);
   assertAccountRefreshing(alice);
   assertAccountRegistered(alice);
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   delete repro_5060;
   runOriginalRepro();
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
}
#endif

TEST_F(AccountModuleTest, ReRegMultiServerWithAuth) {

   ReproHolder::destroyInstance();

   repro::ReproRunner* repro1 = new repro::ReproRunner();
   {
      const char* reproArgs[1];
      reproArgs[0] = "";
      resip::Data configFile = (TestEnvironmentConfig::testResourcePath() + "repro_auth1.config").c_str();
      repro1->run(1, reproArgs, configFile);
   }
   repro::ReproRunner* repro2 = new repro::ReproRunner();
   {
      const char* reproArgs[1];
      reproArgs[0] = "";
      resip::Data configFile = (TestEnvironmentConfig::testResourcePath() + "repro_auth2.config").c_str();
      repro2->run(1, reproArgs, configFile);
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.username = "alice123";
   alice.config.settings.password = "alice123";
   alice.config.settings.domain = "cp.local";
   alice.config.settings.outboundProxy = "prisecwithauth.local";
   alice.config.settings.sipTransportType = SipAccountTransport_TCP;
   alice.config.settings.autoRetryOnTransportDisconnect = true;
   alice.config.settings.useRport = false;
   alice.config.settings.useOutbound = false;
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.minimumRegistrationIntervalSeconds = 5;
   alice.config.settings.maximumRegistrationIntervalSeconds = 5;

   alice.init();
   alice.account->setTFTimerValueMs(10000);

   ASSERT_TRUE(repro1->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
                                                          alice.config.settings.domain.c_str(), "cp.local",
                                                          alice.config.settings.password.c_str(),
                                                          true, "Alice", "<EMAIL>"));

   ASSERT_TRUE(repro2->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
                                                          alice.config.settings.domain.c_str(), "cp.local",
                                                          alice.config.settings.password.c_str(),
                                                          true, "Alice", "<EMAIL>"));

   alice.enable();

   delete repro2;
   delete repro1;

#ifdef _WIN32
   // this delay seems to be caused by this code in SipAccountImpl:
   // else if (response.exists(h_Warnings) && response.header(h_Warnings).front().code() == 397)
   //
   // with comments:
   //    // Hold-Off on updating UI on internally generated transport error "No route to host" if have just experienced
   //    // a network change, and this is the first retry attempt. Likely transport layer gets reset after the first
   //    // registration attempt, as subsequent attempts are successful if the network is available.

   std::this_thread::sleep_for(std::chrono::milliseconds(20000));
#endif

   assertAccountWaitingToRegister(alice);

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   // reset nonce helper, so that on next repro run, BasicNonceHelper generates
   // a new private key. We want this to happen so the SDK is auth challenged when
   // it tries to register again, after the outage.
   resip::Helper::setNonceHelper(NULL);

   repro1 = new repro::ReproRunner();
   {
      const char* reproArgs[1];
      reproArgs[0] = "";
      resip::Data configFile = (TestEnvironmentConfig::testResourcePath() + "repro_auth1.config").c_str();
      repro1->run(1, reproArgs, configFile);
   }
   repro2 = new repro::ReproRunner();
   {
      const char* reproArgs[1];
      reproArgs[0] = "";
      resip::Data configFile = (TestEnvironmentConfig::testResourcePath() + "repro_auth2.config").c_str();
      repro2->run(1, reproArgs, configFile);
   }

   // repro seems to take over logging target, so re-adjust
   alice.phone->setLoggingEnabled(&AutoTestsLogger::instance(), false);
   alice.phone->setLoggingEnabled(&AutoTestsLogger::instance(), true);

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   int firstPort;

   // we need to iterate through and one by one consume each onClientAuth event
   for (;;)
   {
      // 407 Proxy Authentication Required
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountClientAuthEvent evt;
         ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandlerInternal::onClientAuth", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(alice.handle, h);
         if (evt.responseStatusCode == 408)
         {
            // a bunch of these may have fired due to timeouts; we
            // still need to check them, so we can find the events
            // we're really interested in (below)
            continue;
         }

         ASSERT_EQ(407, evt.responseStatusCode);
         // the SDK should send an updated request for this auth challenge
         ASSERT_TRUE(evt.willSendUpdatedRequest);
         firstPort = evt.responseSourcePort;
         break;
      }
   }

   // we need to iterate through and one by one consume each onAccountStatusChanged event
   for (;;)
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
      ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged",
         __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister ||
          evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering)
      {
         continue;
      }

      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
      break;
   }

   // 200 OK
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountClientAuthEvent evt;
      ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandlerInternal::onClientAuth", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(200, evt.responseStatusCode);
      // this is not a challenge, so the SDK should *not* send an updated request for this auth challenge
      ASSERT_FALSE(evt.willSendUpdatedRequest);

      // test currently fails here -- need to fix SDK
      ASSERT_EQ(firstPort, evt.responseSourcePort);
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(10000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   delete repro2;
   delete repro1;
   runOriginalRepro();
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
}

TEST_F(AccountModuleTest, RegisterWithUnreasonablyShortExpiry) {
   TestAccount alice("alice", Account_NoInit);
   // set expiry shorter than resip::ClientRegistration::UnreasonablyLowExpirationThreshold (7s)
   alice.config.settings.registrationIntervalSeconds = 6;
   alice.enable();
}

TEST_F(AccountModuleTest, RegisterWithAuthChallenge) {

   ReproHolder::destroyInstance();

   repro::ReproRunner* repro1 = new repro::ReproRunner();
   {
      const char* reproArgs[1];
      reproArgs[0] = "";
      resip::Data configFile = (TestEnvironmentConfig::testResourcePath() + "repro_auth3.config").c_str();
      repro1->run(1, reproArgs, configFile);
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.username = "alice123";
   alice.config.settings.password = "alice123";
   alice.config.settings.domain = "cp.local";
   alice.config.settings.outboundProxy = "prisecwithauth.local";
   alice.config.settings.sipTransportType = SipAccountTransport_TCP;
   alice.config.settings.autoRetryOnTransportDisconnect = true;
   alice.config.settings.useRport = false;
   alice.config.settings.useOutbound = false;
   alice.config.settings.registrationIntervalSeconds = 10;
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.minimumRegistrationIntervalSeconds = 5;
   alice.config.settings.maximumRegistrationIntervalSeconds = 5;

   ASSERT_TRUE(repro1->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(), alice.config.settings.domain.c_str(), "cp.local", alice.config.settings.password.c_str(), true, "Alice", "<EMAIL>"));

   alice.enable();

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   delete repro1;
   runOriginalRepro();
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
}

TEST_F(AccountModuleTest, AutoRetryOnTransportDisconnect) {

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.sipTransportType = SipAccountTransport_TCP;
   alice.config.settings.autoRetryOnTransportDisconnect = true;
   alice.config.settings.useRport = false; // reduce log spam
   alice.config.settings.useOutbound = false; // reduce log spam
   alice.config.settings.registrationIntervalSeconds = 3600; // ensure it isn't a re-REGISTER that discovers connection drop

   // use a very low registration retry interval; otherwise the SDK won't
   // fire account state going to Status_WaitingToRegister for 10s of seconds
   // due to filtering code in onRequestRetry(..) -- "// Hold-Off on updating UI on internally generated transport error .. "
   alice.config.settings.minimumRegistrationIntervalSeconds = 3; 
   alice.config.settings.maximumRegistrationIntervalSeconds = 3;

   alice.config.settings.overrideMsecsTimerF = 8000;
   alice.config.settings.tcpKeepAliveTime = 5; // reduce test run time -- detect connection loss more quickly
   
   alice.init();
   alice.enable();

   // restart repro -- this will break the active TCP connection
   ReproHolder::destroyInstance();

   // the SDK should detect the TCP connection being broken, and due to autoRetryOnTransportDisconnect
   // being enabled, immediately establish a new TCP connection and re-REGISTER
   assertAccountWaitingToRegister(alice);
   runOriginalRepro();
   assertAccountRegistering(alice);
   assertAccountRegistered(alice);
}



TEST_F(AccountModuleTest, WithOutbound) {
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.useOutbound = true;
   alice.config.settings.sipTransportType = SipAccountTransport_TCP;
   alice.init();
   alice.enable();

   std::this_thread::sleep_for(std::chrono::milliseconds(10000));
   alice.account->requestRegistrationRefresh(alice.handle, 300);
   assertAccountRefreshing(alice);
   assertAccountRegistered(alice);
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.account->requestRegistrationRefresh(alice.handle, 60);
   assertAccountRefreshing(alice);
   assertAccountRegistered(alice);
}

TEST_F(AccountModuleTest, DISABLED_ExcludeEncryptedTransports) {
   TestAccount alice("alice", Account_NoInit, false);
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.excludeEncryptedTransports = true;
   alice.init();

   ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);
   assertAccountRegistering(alice);

   // Expect successful registration but NOT with TLS transport
   CPCAPI2::SipAccount::SipAccountHandle h;
   CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
   ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged",
                              20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_EQ(200, evt.signalingStatusCode);
   ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
   ASSERT_NE(CPCAPI2::SipAccount::SipAccountTransportType::SipAccountTransport_TLS, evt.transportType);

   TestAccount bob("bob", Account_NoInit, false);
   bob.config.settings.sipTransportType = SipAccountTransport_TLS;
   bob.config.settings.excludeEncryptedTransports = true;
   bob.init();
   ASSERT_EQ(bob.account->enable(bob.handle), kSuccess);

   // Expect us to get an internally-generated 503
   ASSERT_TRUE(cpcExpectEvent(bob.accountEvents, "SipAccountHandler::onAccountStatusChanged",
                              20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(bob.handle, h);
   ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);
   ASSERT_EQ(0, evt.signalingStatusCode);

   ASSERT_TRUE(cpcExpectEvent(bob.accountEvents, "SipAccountHandler::onAccountStatusChanged",
                              20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(bob.handle, h);
   ASSERT_TRUE((CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Server_Response == evt.reason)
      || (CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Local_Timeout == evt.reason));
   ASSERT_EQ(503, evt.signalingStatusCode);
}
 
TEST_F(AccountModuleTest, RemoveAccountHandler) {
   Phone* phone = Phone::create();
   phone->initialize(LicenseInfo(), (PhoneHandler*)NULL);
   SipAccountManager* acct = SipAccountManager::getInterface(phone);

   TestAccountConfig aliceConfig("alice");
   SipAccountHandle aliceHandle = acct->create(aliceConfig.settings);

   class TestHandler : public SipAccountHandler {
   public:
      TestHandler(): receivedCallback(false) {}
      ~TestHandler()
      {
      }

      virtual int onAccountStatusChanged(CPCAPI2::SipAccount::SipAccountHandle account, const SipAccountStatusChangedEvent& args)
      {
         receivedCallback = true;
         return kSuccess;
      }
      virtual int onError(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::ErrorEvent& args){ return kSuccess; }
      virtual int onLicensingError(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::LicensingErrorEvent& args){ return kSuccess; }

      bool receivedCallback;
   };

   std::unique_ptr<TestHandler> aliceHandler(new TestHandler());
   acct->setHandler(aliceHandle, aliceHandler.get());
   acct->enable(aliceHandle);

   auto start = std::chrono::high_resolution_clock::now();

   std::atomic_bool threadStopFlag(false);
   auto accountEvent = std::async(std::launch::async, [&] ()
   {
      while (aliceHandler->receivedCallback == false)
      {
         phone->process(SipAccountManager::kBlockingModeNonBlocking);

         if (threadStopFlag) return;

         std::this_thread::sleep_for(std::chrono::milliseconds(100));
      }
   });


   flaggableWaitFor(accountEvent, threadStopFlag);
   auto end = std::chrono::high_resolution_clock::now();

   TestAccountConfig bobConfig("bob");
   SipAccountHandle bobHandle = acct->create(bobConfig.settings);

   std::unique_ptr<TestHandler> bobHandler(new TestHandler());
   acct->setHandler(bobHandle, bobHandler.get());
   acct->enable(bobHandle);
   acct->setHandler(bobHandle, NULL);
   bobHandler.reset();


   // wait about as long as it took before
   std::this_thread::sleep_for((end-start) * 2);

   phone->process(SipAccountManager::kBlockingModeNonBlocking);
   Phone::release(phone);
}

TEST_F(AccountModuleTest, RemoveAccountAdornmentHandler) {
   Phone* phone = Phone::create();
   phone->initialize(LicenseInfo(), (PhoneHandler*)NULL);
   SipAccountManager* acct = SipAccountManager::getInterface(phone);

   TestAccountConfig aliceConfig("alice");
   SipAccountHandle aliceAccountHandle = acct->create(aliceConfig.settings);


   class MySipAccountHandler : public SipAccountHandler {
   public:
      MySipAccountHandler() {}
      int onAccountStatusChanged(CPCAPI2::SipAccount::SipAccountHandle account, const SipAccountStatusChangedEvent& args) { return kSuccess; }
      int onError(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::ErrorEvent& args){ return kSuccess; }
   };
   std::unique_ptr<MySipAccountHandler> accountHandler;


   class MySipAccountAdornmentHandler : public SipAccountAdornmentHandler {
   public:
      MySipAccountAdornmentHandler(): receivedCallback(false) {}
      ~MySipAccountAdornmentHandler()
      {
      }

      virtual int onAccountAdornment(CPCAPI2::SipAccount::SipAccountHandle account, const SipAccountAdornmentEvent& args)
      {
         receivedCallback = true;
         return kSuccess;
      }

      bool receivedCallback;
   };

   std::unique_ptr<MySipAccountAdornmentHandler> aliceHandler(new MySipAccountAdornmentHandler());
   acct->setAdornmentHandler(aliceAccountHandle, aliceHandler.get());
   acct->setHandler(aliceAccountHandle, accountHandler.get()); // otherwise there will be annoying NULL events in the process queue
   acct->enable(aliceAccountHandle);

   auto start = std::chrono::high_resolution_clock::now();

   std::atomic_bool threadStopFlag(false);
   auto accountEvent = std::async(std::launch::async, [&] ()
   {
      while (aliceHandler->receivedCallback == false)
      {
         phone->process(SipAccountManager::kBlockingModeNonBlocking);

         if (threadStopFlag) return;

         std::this_thread::sleep_for(std::chrono::milliseconds(100));
      }
   });

   flaggableWaitFor(accountEvent, threadStopFlag);
   auto end = std::chrono::high_resolution_clock::now();



   TestAccountConfig bobConfig("bob");
   SipAccountHandle bobHandle = acct->create(bobConfig.settings);

   std::unique_ptr<MySipAccountAdornmentHandler> bobHandler(new MySipAccountAdornmentHandler());
   acct->setAdornmentHandler(bobHandle, bobHandler.get());
   acct->setHandler(aliceAccountHandle, accountHandler.get()); // otherwise there will be annoying NULL events in the process queue
   acct->enable(bobHandle);
   acct->setAdornmentHandler(bobHandle, NULL);
   bobHandler.reset();

   // wait about as long as it took before
   std::this_thread::sleep_for((end-start) * 2);

   phone->process(SipAccountManager::kBlockingModeNonBlocking);

   Phone::release(phone);
}

TEST_F(AccountModuleTest, ApplySettings)
{
   TestAccount alice("alice");
   SipAccountSettings settings = alice.config.settings;
   settings.udpKeepAliveTime = 10;
   settings.tcpKeepAliveTime = 20;

   settings.capabilities.push_back(SipParameterType("mobility", "fixed"));
   settings.capabilities.push_back(SipParameterType("video"));

   ASSERT_EQ(alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings), kSuccess);
   ASSERT_EQ(alice.account->configureTransportAccountSettings(alice.handle, settings, TransportWiFi), kSuccess);
   ASSERT_EQ(alice.account->applySettings(alice.handle), kSuccess);
   assertAccountDeregistering(alice);
   assertAccountDeregistered(alice);
   assertAccountRegistering(alice);
   assertAccountRegistered(alice);

   SipAccountSettings settings2 = alice.config.settings;
   settings2.useRport = !settings2.useRport;

   ASSERT_EQ(alice.account->configureDefaultAccountSettings(alice.handle, settings2), kSuccess);
   ASSERT_EQ(alice.account->applySettings(alice.handle), kSuccess);
   assertAccountDeregistering(alice);
   assertAccountDeregistered(alice);
   assertAccountRegistering(alice);
   assertAccountRegistered(alice);

   SipAccountSettings settings3 = alice.config.settings;
   settings3.registrationIntervalSeconds = 33;
   SipAccountSettings settings4 = alice.config.settings;
   settings4.registrationIntervalSeconds = 44;

   ASSERT_EQ(alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings), kSuccess);
   ASSERT_EQ(alice.account->configureTransportAccountSettings(alice.handle, settings3, TransportWiFi), kSuccess);
   ASSERT_EQ(alice.account->configureTransportAccountSettings(alice.handle, settings4, TransportWWAN), kSuccess);
   ASSERT_EQ(alice.account->applySettings(alice.handle), kSuccess);
   assertAccountDeregistering(alice);
   assertAccountDeregistered(alice);
   assertAccountRegistering(alice);
   assertAccountRegistered(alice);

   ASSERT_EQ(alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings), kSuccess);
   ASSERT_EQ(alice.account->configureTransportAccountSettings(alice.handle, settings3, TransportWiFi), kSuccess);
   ASSERT_EQ(alice.account->configureTransportAccountSettings(alice.handle, settings4, TransportWWAN), kSuccess);
   ASSERT_EQ(alice.account->applySettings(alice.handle), kSuccess);
}

#ifndef ANDROID
TEST_F(AccountModuleTest, ApplySettingsDefault)
{
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.outboundProxy = "127.0.0.1:6060";
   alice.enable();
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   SipAccountSettings settings = alice.config.settings;
   settings.udpKeepAliveTime = 10;
   settings.tcpKeepAliveTime = 20;

   settings.capabilities.push_back(SipParameterType("mobility", "fixed"));
   settings.capabilities.push_back(SipParameterType("video"));

   ASSERT_EQ(alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings), kSuccess);
   ASSERT_EQ(alice.account->configureTransportAccountSettings(alice.handle, settings, TransportWiFi), kSuccess);
   ASSERT_EQ(alice.account->applySettings(alice.handle), kSuccess);
   assertAccountDeregistering(alice);
   assertAccountDeregistered(alice);
   assertAccountRegistering(alice);
   assertAccountRegistered(alice);

   SipAccountSettings settings2 = alice.config.settings;
   settings2.useRport = !settings2.useRport;

   ASSERT_EQ(alice.account->configureDefaultAccountSettings(alice.handle, settings2), kSuccess);
   ASSERT_EQ(alice.account->applySettings(alice.handle), kSuccess);
   assertAccountDeregistering(alice);
   assertAccountDeregistered(alice);
   assertAccountRegistering(alice);
   assertAccountRegistered(alice);

   SipAccountSettings settings3 = alice.config.settings;
   settings3.sipTransportType = SipAccountTransport_TCP;

   ASSERT_EQ(alice.account->configureDefaultAccountSettings(alice.handle, settings3), kSuccess);
   ASSERT_EQ(alice.account->applySettings(alice.handle), kSuccess);
   assertAccountDeregistering(alice);
   assertAccountDeregistered(alice);
   assertAccountRegistering(alice);
   assertAccountRegistered(alice);

   SipAccountSettings settings4 = alice.config.settings;
   settings4.sipTransportType = SipAccountTransport_TLS;
   settings4.domain = "autotest.cpcapi2";
   settings4.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   settings4.outboundProxy = "";
   settings4.useRport = false;
   settings4.stunServerSource = SipAccount::StunServerSource_SRV;
   settings4.additionalCertPeerNames.clear();
  settings4.acceptedCertPublicKeys.push_back("MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEA3Q/3AQAW9Z8thdtQlnxAG6ilSn1FVH81ViruEEEyJBbxk0WPzroavMqRgvXJ6sIE+7iR8hHqY2BowKPhTg2D+WCvycT+i5YC7L37f/oWsnVp5xZZqTtiTTTXc+rSHgl2WO5tGxyGGWlUsslq3UXnHym5H9VvEZ5R3EKJelxauvhO8uNfjEwjuKBYPFjNJbZUoiy9s+ZhFuM1a14ArRZlvQ/P96txt2GLHjScMMiZllO4a6NyLvMJQI7Ys0lI4JmKe5QEBdukvbSnFbnsaGMjwfeAksFiKyDRvHUjyc0EUaqU24hmhn+RZkYSjTqeBd567TlggPR696peqaA/tILWGUgcMhYBga9DFzkpgXc8NDaO6WqAFg2caM2/BAnZ9OyfzNYU7GXCyOAm6JNb+BaxYLg6rqCKr27cSipy4MG6LvgcOndRLAeQoNe55QMqi+lfH90v0nQsNIlIEgo8pXgwML6lef+dk98Ox46Egkq1txFKVrmmZ2bXBiLrdFQYqEhzpAMl8XlKg7Wq4R5q2NyOuVJwQ0RHVCbW6nHO/nT5eyldqEzvSY+pg6juxOIpgtD7XvlXRqq775OW0aVFGoL1ilKCjb//c3WU4oetWNcBizrkYb0BOny7P17s8Jx2ZVtvcup4KMOi3+NZJew1uRBKM8wgsSZ1CLmZq1ae/ECXI3kCAwEAAQ==");
   ASSERT_EQ(alice.account->configureDefaultAccountSettings(alice.handle, settings4), kSuccess);
   ASSERT_EQ(alice.account->applySettings(alice.handle), kSuccess);
   assertAccountDeregistering(alice);
   assertAccountDeregistered(alice);
   assertAccountRegistering(alice);
   assertAccountRegistered(alice);

   SipAccountSettings settings5 = alice.config.settings;
   settings5.sipTransportType = SipAccountTransport_UDP;

   ASSERT_EQ(alice.account->configureDefaultAccountSettings(alice.handle, settings5), kSuccess);
   ASSERT_EQ(alice.account->applySettings(alice.handle), kSuccess);
   assertAccountDeregistering(alice);
   assertAccountDeregistered(alice);
   assertAccountRegistering(alice);
   assertAccountRegistered(alice);

   SipAccountSettings settings6 = alice.config.settings;
   settings6.registrationIntervalSeconds = 33;
   SipAccountSettings settings7 = alice.config.settings;
   settings7.registrationIntervalSeconds = 44;

   ASSERT_EQ(alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings), kSuccess);
   ASSERT_EQ(alice.account->configureTransportAccountSettings(alice.handle, settings6, TransportWiFi), kSuccess);
   ASSERT_EQ(alice.account->configureTransportAccountSettings(alice.handle, settings7, TransportWWAN), kSuccess);
   ASSERT_EQ(alice.account->applySettings(alice.handle), kSuccess);
   assertAccountDeregistering(alice);
   assertAccountDeregistered(alice);
   assertAccountRegistering(alice);
   assertAccountRegistered(alice);

   ASSERT_EQ(alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings), kSuccess);
   ASSERT_EQ(alice.account->configureTransportAccountSettings(alice.handle, settings6, TransportWiFi), kSuccess);
   ASSERT_EQ(alice.account->configureTransportAccountSettings(alice.handle, settings7, TransportWWAN), kSuccess);
   ASSERT_EQ(alice.account->applySettings(alice.handle), kSuccess);
}
#endif

TEST_F(AccountModuleTest, RegisterUsingInvalidDomain)
{
   TestAccount alice("alice", Account_NoInit, false);
   alice.config.settings.outboundProxy = "";
   alice.config.settings.domain = "demo.xten.con"; // Intentional typo
   alice.init();

   // Perform registration
   ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);

   CPCAPI2::SipAccount::SipAccountHandle h;
   CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

   // Expect a registering event
   ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged",
      20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);
   ASSERT_EQ(0, evt.signalingStatusCode);

   // Expect a 503 registration failure
   ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged",
      40000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_EQ(503, evt.signalingStatusCode);
   ASSERT_EQ(evt.reason, CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Dns_Lookup);
   ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);

}

TEST_F(AccountModuleTest, RegisterTimeoutRetrySuccess_Udp)
{
   ReproHolder::destroyInstance();

   TestAccount alice("alice", Account_NoInit, false);
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.init();

   ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);

   CPCAPI2::SipAccount::SipAccountHandle h;
   CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

   ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged",
      20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);
   ASSERT_EQ(0, evt.signalingStatusCode);

   
   ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged",
      40000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_EQ(408, evt.signalingStatusCode);
   ASSERT_EQ(evt.reason, CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Local_Timeout);
   ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
   
   
   // get repro running again; then we should have a good registration
   
   runOriginalRepro();

   ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged",
      20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);
   ASSERT_EQ(0, evt.signalingStatusCode);

   
   ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged",
      40000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_EQ(200, evt.signalingStatusCode);
   ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
}




// OBELISK-4968: adjust SDK to close transport connections on internally generated 408 request timeout
TEST_F(AccountModuleTest, RegisterTimeoutRetrySuccess_Tcp)
{
   ReproHolder::destroyInstance();

   DummyTcpListener::Config dtConfig;
   dtConfig.port = 6060; // match our default port in TestAccountConfig
   // to ensure we have a timeout at the SIP transaction level and generate an internal 408
   // vs TCP connection rejection and internal 503, we spin up the dummy TCP listener.
   DummyTcpListener tcpListener(dtConfig);
   ASSERT_GT(tcpListener.start(), 0);

   
   TestAccount alice("alice", Account_NoInit, false);
   alice.config.settings.sipTransportType = SipAccountTransport_TCP;
   alice.config.settings.overrideMsecsTimerF = 10; // make test run more quickly
   alice.init();

   ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);

   CPCAPI2::SipAccount::SipAccountHandle h;
   CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

   ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged",
      20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);
   ASSERT_EQ(0, evt.signalingStatusCode);

   
   const long waitMs = alice.account->getTFTimerValueMs() + /* buffer */ 10000;
   
   ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged",
      static_cast<int>(waitMs), CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
   ASSERT_EQ(408, evt.signalingStatusCode);
   // SDK should have closed its TCP connection at this point (although it's hard to verify that here)   
   
   tcpListener.stop();
   
   // get repro running again; then we should have a good registration
   ASSERT_TRUE(runOriginalRepro());
   assertAccountRegistering(alice);
   assertAccountRegistered(alice);
}

TEST_F(AccountModuleTest, RegisterUsingUnreachableNameserver)
{
   TestAccount alice("alice", Account_NoInit, false);
   alice.config.settings.outboundProxy = "";
   alice.config.settings.domain = "demo.xten.con"; // Intentional typo
#ifndef __APPLE__
   alice.config.settings.nameServers.push_back("*********"); // should not be routable -- RFC 5737
#else
   // on mac, ********* results in immediate failure whereas *********** times out (what we want).
   // ********* was previously used here, but we have since started bringing this up via ifconfig alias
   // for PTT tests.
   // https://en.wikipedia.org/wiki/Reserved_IP_addresses
   alice.config.settings.nameServers.push_back("***********");
#endif
   alice.init();

   // Perform registration
   ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);

   CPCAPI2::SipAccount::SipAccountHandle h;
   CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

   // Expect a registering event
   ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged",
                                 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);
   ASSERT_EQ(0, evt.signalingStatusCode);

   // Expect a 503 registration failure but without giving up
   ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged",
                              40000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_EQ(503, evt.signalingStatusCode);
   ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Local_Timeout, evt.reason);
   ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
}


// Requires a NAT64 setup, with a pre-determined external IP
TEST_F(AccountModuleTest, DISABLED_RegisterViaNAT64)
{
   TestAccount alice("alice", Account_NoInit, false);
   alice.config.settings.outboundProxy = "";
   alice.config.settings.domain = "opsip.silverstar.counterpath.net";
   alice.config.settings.enableNat64Support = true;
   alice.config.settings.ipVersion = IpVersion_Auto_PreferV6;
   alice.init();

   // Perform registration
   ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);

   CPCAPI2::SipAccount::SipAccountHandle h;
   CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

   // Expect a registering event
   ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);
   ASSERT_EQ(0, evt.signalingStatusCode);

   // Expect a registered event
   ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_EQ(200, evt.signalingStatusCode);
   ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
   resip::NameAddr contact(evt.accountBindingIpAddress.c_str());
   // ASSERT_EQ("***********", contact.uri().host());
}


TEST_F(AccountModuleTest, RegisterUsingInvalidOutboundProxy)
{
   TestAccount alice("alice", Account_NoInit, false);
   alice.config.settings.outboundProxy = "sip:http://qa-test6/qawiki/"; // bogus
   alice.config.settings.domain = "demo.xten.com";
   alice.init();

   // Perform registration
   ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);

   CPCAPI2::SipAccount::SipAccountHandle h;
   CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

   // Expect a registering event
   ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged",
      20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);
   ASSERT_EQ(0, evt.signalingStatusCode);

   // Expect a 503 registration failure
   ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged",
      20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Dns_Lookup, evt.reason);
   ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
   ASSERT_EQ(503, evt.signalingStatusCode);
}

TEST_F(AccountModuleTest, DISABLED_RegisterUsingValidServerWhichDoesNotSupportSip)
{
   TestAccount alice("alice", Account_NoInit, false);
   alice.config.settings.outboundProxy = "www.timescolonist.com";
   alice.config.settings.domain = "demo.xten.com";
   alice.config.settings.minimumRegistrationIntervalSeconds = 5;
   alice.init();

   // Perform registration
   ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);

   CPCAPI2::SipAccount::SipAccountHandle h;
   CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

   // Expect a registering event
   ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged",
      20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);
   ASSERT_EQ(0, evt.signalingStatusCode);

   // Expect a 408 registration failure, and the SDK to internally do retries
   ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged",
      40000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_EQ(408, evt.signalingStatusCode);
   ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);
   //ASSERT_NE(evt.timeUntilAutomaticRetry_Seconds, 0);

   std::this_thread::sleep_for(std::chrono::seconds(5));
}

TEST_F(AccountModuleTest, RegisterWithoutUsername)
{
   TestAccount alice("alice", Account_NoInit, false);
   alice.config.settings.username = "";
   alice.init();

   // Perform registration
   ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);
   assertAccountRegistering(alice);
   assertAccountRegistered(alice);
}

TEST_F(AccountModuleTest, DISABLED_RegisterTwiceWithInvalidDomain)
{
   TestAccount alice("alice", Account_NoInit, false);
   alice.config.settings.username = "sksjdflksjdf";
   alice.config.settings.domain = "fjsdoifjowiejf.com";
   alice.init();

   // Perform registration
   ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);
   assertAccountRegistering(alice);
   assertAccountDeregistered(alice);

#if 0
   alice.account->disable(alice.handle);
   assertAccountDeregistering(alice);
   assertAccountDeregistered(alice);
#endif

   // Perform registration
   ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);
   assertAccountRegistering(alice);
   assertAccountDeregistered(alice);
}

TEST_F(AccountModuleTest, ValidateTransportTypes)
{
   // UDP account
   TestAccount alice("alice", Account_NoInit, false);
   alice.config.settings.sipTransportType = SipAccount::SipAccountTransport_UDP;
   alice.init();

   // Perform registration
   ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);

   assertAccountRegistering(alice);

   // Expect registration success
   CPCAPI2::SipAccount::SipAccountHandle h;
   CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
   ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged",
                              20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_EQ(200, evt.signalingStatusCode);
   ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
   ASSERT_EQ(CPCAPI2::SipAccount::SipAccountTransportType::SipAccountTransport_UDP, evt.transportType);

   // TCP account
   TestAccount bob("bob", Account_NoInit, false);
   bob.config.settings.sipTransportType = SipAccount::SipAccountTransport_TCP;
   bob.init();

   // Perform registration
   ASSERT_EQ(bob.account->enable(alice.handle), kSuccess);

   assertAccountRegistering(bob);

   // Expect registration success
   ASSERT_TRUE(cpcExpectEvent(bob.accountEvents, "SipAccountHandler::onAccountStatusChanged",
                              20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(bob.handle, h);
   ASSERT_EQ(200, evt.signalingStatusCode);
   ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
   ASSERT_EQ(CPCAPI2::SipAccount::SipAccountTransportType::SipAccountTransport_TCP, evt.transportType);
}

TEST_F(AccountModuleTest, DISABLED_RegisterManyAccountsSimultaneously)
{
   std::vector<TestAccount*> accounts;
   const int accountCount = 20;

   for (int i = 0; i < accountCount; ++i)
   {
      std::stringstream ss;
      ss << "account " << i + 1;

      TestAccount* account = (i == 0) ? new TestAccount(ss.str(), Account_NoInit, false) :
                                        new TestAccount(ss.str(), Account_NoInit, false, accounts[0]->phone); // slave account

      ASSERT_TRUE(account->config.registerHandlers); // want to stress the fdset implementation

      account->init();

      accounts.push_back(account);
   }

   for (int i = 0; i < accountCount; ++i)
   {
      // Perform registration
      ASSERT_EQ(accounts[i]->account->enable(accounts[i]->handle), kSuccess);
   }


   std::vector< std::shared_future<void> > futureEvents;
   CPCAPI2::test::EventHandler* masterEventHandler = accounts[0]->accountEvents; // since all others are slaves (share same CPCAPI2::Phone)

   for (int accountIndex = 0; accountIndex < accountCount; ++accountIndex)
   {
      std::shared_future<void> events = std::async(std::launch::async, [=] ()
      {
         CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

         // Expect a registering event
         ASSERT_TRUE(cpcExpectEvent(masterEventHandler, "SipAccountHandler::onAccountStatusChanged",
                                    20000, HandleEqualsPred<SipAccountHandle>(accounts[accountIndex]->handle), accounts[accountIndex]->handle, evt));

         ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);
         ASSERT_EQ(0, evt.signalingStatusCode);

         // Expect a registered event
         ASSERT_TRUE(cpcExpectEvent(masterEventHandler, "SipAccountHandler::onAccountStatusChanged",
                                    20000, HandleEqualsPred<SipAccountHandle>(accounts[accountIndex]->handle), accounts[accountIndex]->handle, evt));

         ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
         ASSERT_EQ(200, evt.signalingStatusCode);


         accounts[accountIndex]->account->disable(accounts[accountIndex]->handle, false);

         // Expect a unregistering event
         ASSERT_TRUE(cpcExpectEvent(masterEventHandler, "SipAccountHandler::onAccountStatusChanged",
                                    20000, HandleEqualsPred<SipAccountHandle>(accounts[accountIndex]->handle), accounts[accountIndex]->handle, evt));

         ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Unregistering, evt.accountStatus);


         // Expect an unregistered event
         ASSERT_TRUE(cpcExpectEvent(masterEventHandler, "SipAccountHandler::onAccountStatusChanged",
                                    20000, HandleEqualsPred<SipAccountHandle>(accounts[accountIndex]->handle), accounts[accountIndex]->handle, evt));

         ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Unregistered, evt.accountStatus);
      });

      futureEvents.push_back(events);
   }

   for (std::shared_future<void> futureEvent : futureEvents)
   {
      ASSERT_EQ(futureEvent.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
      ASSERT_NO_THROW(futureEvent.get());
      ASSERT_EQ(futureEvent.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
      ASSERT_NO_THROW(futureEvent.get());
   }

   std::for_each(accounts.rbegin(), accounts.rend(), [](TestAccount* a)
   {
      delete a;
   });
}

TEST_F(AccountModuleTest, DestroyAccountWhileRegistering)
{
   TestAccount alice("alice", TestAccountInitMode::Account_Init);
   alice.account->enable(alice.handle);
   alice.account->destroy(alice.handle);
}

TEST_F(AccountModuleTest, DestroyAccountWhileUnregistering)
{
	TestAccount alice("alice", Account_Enable, false);
	alice.account->disable(alice.handle);
	alice.account->destroy(alice.handle);
}

TEST_F(AccountModuleTest, SipTimers)
{
   TestAccount alice("alice");
   alice.account->setT1TimerValueMs(800);
   alice.account->setT2TimerValueMs(800);
   alice.account->setT4TimerValueMs(800);
   alice.account->setTDTimerValueMs(800);

   ASSERT_EQ(alice.account->getT1TimerValueMs(), 800);
   ASSERT_EQ(alice.account->getT2TimerValueMs(), 800);
   ASSERT_EQ(alice.account->getT4TimerValueMs(), 800);
   ASSERT_EQ(alice.account->getTDTimerValueMs(), 800);
}

TEST_F(AccountModuleTest, TlsAdditionalPeerCertNames)
{
   // Attempt to register using TLS to autotest.cpcapi2 using incorrect (but valid) DNS name
   TestAccount alice("alice", Account_NoInit, false);
   alice.config.settings.domain = "127.0.0.1:6061"; // this would fail because cert is for autotest.cpcapi2
   alice.config.settings.sipTransportType = SipAccount::SipAccountTransport_TLS;
   alice.config.settings.outboundProxy = "";
   alice.config.settings.useRport = false;
   alice.config.settings.stunServerSource = SipAccount::StunServerSource_SRV;

   alice.init();

   // don't load certificates from the OS; use our versioned certificate so we maintain control
   SipAccountManagerInternal* mi = dynamic_cast<SipAccountManagerInternal*>(alice.account);
   mi->setCertStorageLoadType(alice.handle, SipAccountManagerInternal::CertLoadStorageType_FileSystem);
   mi->setCertStorageFileSystemPath(alice.handle, TestEnvironmentConfig::testResourcePath() + "SelfSignedCerts/root");

   // Perform registration
   ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);

   CPCAPI2::SipAccount::SipAccountHandle h;
   CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
   ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged",
      20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_EQ(0, evt.signalingStatusCode);
   ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);

   // expect failure
   ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged",
      20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
   ASSERT_EQ(evt.tlsInfo.certificateStatus, CPCAPI2::SipAccount::SipTLSConnectionInfo::CertificateStatus_WrongPeer);
   ASSERT_EQ("autotest.cpcapi2", evt.tlsInfo.server);
   ASSERT_EQ("Certificate name mismatch", evt.signalingResponseText);

   // allow cert DNS matching
   alice.config.settings.additionalCertPeerNames.push_back("autotest.cpcapi2");

   ASSERT_EQ(alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings), kSuccess);
   ASSERT_EQ(alice.account->applySettings(alice.handle), kSuccess);
   assertAccountDeregistering(alice);
   assertAccountDeregistered(alice);
   assertAccountRegistering(alice);
   assertAccountRegistered(alice);

   // certificate pinning
   alice.config.settings.requiredCertPublicKeys.push_back("bogus");
   ASSERT_EQ(alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings), kSuccess);
   ASSERT_EQ(alice.account->applySettings(alice.handle), kSuccess);
   assertAccountDeregistering(alice);
   assertAccountDeregistered(alice);
   assertAccountRegistering(alice);

   // expect failure
   ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged",
      20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
   ASSERT_EQ(evt.tlsInfo.certificateStatus, CPCAPI2::SipAccount::SipTLSConnectionInfo::CertificateStatus_WrongPubKey);
   ASSERT_EQ("autotest.cpcapi2", evt.tlsInfo.server);

   // clean up required certs
   alice.config.settings.requiredCertPublicKeys.clear();


   alice.account->disable(alice.handle);
   assertAccountDeregistering(alice);
   assertAccountDeregistered(alice);

   // don't load any certificates
   mi->setCertStorageLoadType(alice.handle, SipAccountManagerInternal::CertLoadStorageType_FileSystem);
   mi->setCertStorageFileSystemPath(alice.handle, "./bogus");

   // also try with server's public key
   alice.config.settings.additionalCertPeerNames.clear();
   alice.config.settings.acceptedCertPublicKeys.push_back("MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEA3Q/3AQAW9Z8thdtQlnxAG6ilSn1FVH81ViruEEEyJBbxk0WPzroavMqRgvXJ6sIE+7iR8hHqY2BowKPhTg2D+WCvycT+i5YC7L37f/oWsnVp5xZZqTtiTTTXc+rSHgl2WO5tGxyGGWlUsslq3UXnHym5H9VvEZ5R3EKJelxauvhO8uNfjEwjuKBYPFjNJbZUoiy9s+ZhFuM1a14ArRZlvQ/P96txt2GLHjScMMiZllO4a6NyLvMJQI7Ys0lI4JmKe5QEBdukvbSnFbnsaGMjwfeAksFiKyDRvHUjyc0EUaqU24hmhn+RZkYSjTqeBd567TlggPR696peqaA/tILWGUgcMhYBga9DFzkpgXc8NDaO6WqAFg2caM2/BAnZ9OyfzNYU7GXCyOAm6JNb+BaxYLg6rqCKr27cSipy4MG6LvgcOndRLAeQoNe55QMqi+lfH90v0nQsNIlIEgo8pXgwML6lef+dk98Ox46Egkq1txFKVrmmZ2bXBiLrdFQYqEhzpAMl8XlKg7Wq4R5q2NyOuVJwQ0RHVCbW6nHO/nT5eyldqEzvSY+pg6juxOIpgtD7XvlXRqq775OW0aVFGoL1ilKCjb//c3WU4oetWNcBizrkYb0BOny7P17s8Jx2ZVtvcup4KMOi3+NZJew1uRBKM8wgsSZ1CLmZq1ae/ECXI3kCAwEAAQ==");

   ASSERT_EQ(alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings), kSuccess);
   ASSERT_EQ(alice.account->applySettings(alice.handle), kSuccess);
   ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);
   assertAccountRegistering(alice);
   assertAccountRegistered(alice);
}

TEST_F(AccountModuleTest, TlsCertPinning)
{
#ifdef ANDROID
	std::string dnsServer = getenv("CPCAPI2_DNS_SERVER");
	ASSERT_NE("", dnsServer); // verify that the external DNS server has been set
#endif

   // Attempt to register using TLS to autotest.cpcapi2 using incorrect (but valid) DNS name
   TestAccount alice("alice", Account_NoInit, false);
   alice.config.settings.domain = "autotest.cpcapi2";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccount::SipAccountTransport_TLS;
   alice.config.settings.outboundProxy = "";
   alice.config.settings.useRport = false;
   alice.config.settings.stunServerSource = SipAccount::StunServerSource_SRV;

   alice.init();

   SipAccountManagerInternal* mi = dynamic_cast<SipAccountManagerInternal*>(alice.account);

   // don't load any certificates
   mi->setCertStorageLoadType(alice.handle, SipAccountManagerInternal::CertLoadStorageType_FileSystem);
   mi->setCertStorageFileSystemPath(alice.handle, "./bogus");

   // don't check OS trust store
   alice.config.settings.ignoreCertVerification = true;
   // certificate pinning
   alice.config.settings.requiredCertPublicKeys.push_back("bogus");
   ASSERT_EQ(alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings), kSuccess);
   ASSERT_EQ(alice.account->applySettings(alice.handle), kSuccess);
   
   alice.account->enable(alice.handle);
   
   assertAccountRegistering(alice);
   
   // expect failure
   CPCAPI2::SipAccount::SipAccountHandle h;
   CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
   
   ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged",
      20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
   ASSERT_EQ(evt.tlsInfo.certificateStatus, CPCAPI2::SipAccount::SipTLSConnectionInfo::CertificateStatus_WrongPubKey);
   ASSERT_EQ("autotest.cpcapi2", evt.tlsInfo.server);
}


TEST_F(AccountModuleTest, IpVersionSwap) {
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.ipVersion = CPCAPI2::SipAccount::IpVersion_V4;
   alice.config.settings.outboundProxy = "127.0.0.1:6060";
   alice.init();
   alice.enable();

   for (int i = 0; i < 10; ++i)
   {
      alice.config.settings.ipVersion = CPCAPI2::SipAccount::IpVersion_V6;
      alice.config.settings.outboundProxy = "[::1]:6060";
      alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings);
      alice.account->applySettings(alice.handle);

      assertAccountDeregistering(alice);
      assertAccountDeregistered(alice);
      assertAccountRegistering(alice);
      assertAccountRegistered(alice);

      alice.config.settings.ipVersion = CPCAPI2::SipAccount::IpVersion_V4;
      alice.config.settings.outboundProxy = "127.0.0.1:6060";
      alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings);
      alice.account->applySettings(alice.handle);

      assertAccountDeregistering(alice);
      assertAccountDeregistered(alice);
      assertAccountRegistering(alice);
      assertAccountRegistered(alice);
   }
}

TEST_F(AccountModuleTest, IpVersionSwapDefault)
{
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_5060 = runRepro("repro_5060.config");

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.outboundProxy = "127.0.0.1";
   alice.config.settings.ipVersion = CPCAPI2::SipAccount::IpVersion_V4;
   alice.init();
   alice.enable();

   for (int i = 0; i < 10; ++i)
   {
      alice.config.settings.ipVersion = CPCAPI2::SipAccount::IpVersion_V6;
      alice.config.settings.outboundProxy = "[::1]";
      alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings);
      alice.account->applySettings(alice.handle);

      assertAccountDeregistering(alice);
      assertAccountDeregistered(alice);
      assertAccountRegistering(alice);
      assertAccountRegistered(alice);

      alice.config.settings.ipVersion = CPCAPI2::SipAccount::IpVersion_V4;
      alice.config.settings.outboundProxy = "127.0.0.1";
      alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings);
      alice.account->applySettings(alice.handle);

      assertAccountDeregistering(alice);
      assertAccountDeregistered(alice);
      assertAccountRegistering(alice);
      assertAccountRegistered(alice);
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   delete repro_5060;
   runOriginalRepro();
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
}

TEST_F(AccountModuleTest, DecodeProvisioning)
{
   TestAccount alice("alice");
   std::ifstream in((TestEnvironmentConfig::testResourcePath() + "provisioningtests.json").c_str());
   assert(in.is_open());

   std::ostringstream iss;
   iss << in.rdbuf() << std::flush;

   cpc::string doc = iss.str().c_str();
   cpc::vector<SipAccountSettings> accounts;

   int result = alice.account->decodeProvisioningResponse(doc, accounts);
   ASSERT_EQ(kSuccess, result);

   ASSERT_EQ(accounts[0].username, "AutoTestUser");
   ASSERT_EQ(accounts[0].domain, "autotest.net");
   ASSERT_EQ(accounts[0].password, "AutoTestPwd");
   ASSERT_EQ(accounts[0].displayName, "AutoTest");
   ASSERT_EQ(accounts[0].auth_username, "AutoTestAuth");
   ASSERT_EQ(accounts[0].auth_realm, "realm.autotest.net");
   ASSERT_EQ(accounts[0].useRegistrar, false);
   ASSERT_EQ(accounts[0].outboundProxy, "proxy.autotest.net");
   ASSERT_EQ(accounts[0].alwaysRouteViaOutboundProxy, true);
   ASSERT_EQ(accounts[0].registrationIntervalSeconds, 3601);
   ASSERT_EQ(accounts[0].minimumRegistrationIntervalSeconds, 3599);
   ASSERT_EQ(accounts[0].maximumRegistrationIntervalSeconds, 3602);
   ASSERT_EQ(accounts[0].useRport, false);
   ASSERT_EQ(accounts[0].sipTransportType, SipAccountTransport_Unknown);
   ASSERT_EQ(accounts[0].excludeEncryptedTransports, true);
   ASSERT_EQ(accounts[0].userAgent, "AutoTest UA");
   ASSERT_EQ(accounts[0].udpKeepAliveTime, 31);
   ASSERT_EQ(accounts[0].tcpKeepAliveTime, 121);
   ASSERT_EQ(accounts[0].useOutbound, true);
   ASSERT_EQ(accounts[0].useGruu, true);
   ASSERT_EQ(accounts[0].otherNonEscapedCharsInUri, "$%^");
   ASSERT_EQ(accounts[0].nameServers[0], "***************");
   ASSERT_EQ(accounts[0].nameServers[1], "192.168.300.300");

   ASSERT_EQ(accounts[0].additionalNameServers[0], "***************");
   ASSERT_EQ(accounts[0].additionalNameServers[1], "***************");

   ASSERT_EQ(accounts[0].sessionTimerMode, SipAccountSessionTimerMode_Optional);
   ASSERT_EQ(accounts[0].sessionTimeSeconds, 601);
   ASSERT_EQ(accounts[0].stunServerSource, StunServerSource_Custom);
   ASSERT_EQ(accounts[0].stunServer, "stun.autotest.net");
   ASSERT_EQ(accounts[0].ignoreCertVerification, true);
   ASSERT_EQ(accounts[0].additionalCertPeerNames[0], "AutoTestAdditionalCertPeerName01");
   ASSERT_EQ(accounts[0].additionalCertPeerNames[1], "AutoTestAdditionalCertPeerName02");

   ASSERT_EQ(accounts[0].acceptedCertPublicKeys[0], "AutoTestAcceptedCertPublicKey01");
   ASSERT_EQ(accounts[0].acceptedCertPublicKeys[1], "AutoTestAcceptedCertPublicKey02");

   ASSERT_EQ(accounts[0].requiredCertPublicKeys[0], "AutoTestRequiredCertPublicKey03");
   ASSERT_EQ(accounts[0].requiredCertPublicKeys[1], "AutoTestRequiredCertPublicKey04");

   ASSERT_EQ(accounts[0].sipQosSettings, 41);
   ASSERT_EQ(accounts[0].useImsAuthHeader, true);
   ASSERT_EQ(accounts[0].minSipPort, 1);
   ASSERT_EQ(accounts[0].maxSipPort, 10000);
   ASSERT_EQ(accounts[0].useMethodParamInReferTo, true);
   ASSERT_EQ(accounts[0].useInstanceId, true);
   ASSERT_EQ(accounts[0].ipVersion, IpVersion_V6);
   ASSERT_EQ(accounts[0].sslVersion, SSL_HIGHEST);
   ASSERT_EQ(accounts[0].reRegisterOnResponseTypes[0].method, "AUTOTEST");
   ASSERT_EQ(accounts[0].reRegisterOnResponseTypes[0].responseCode, 505);
   ASSERT_EQ(accounts[0].reRegisterOnResponseTypes[1].method, "JUSTTEST");
   ASSERT_EQ(accounts[0].reRegisterOnResponseTypes[1].responseCode, 506);

   ASSERT_EQ(accounts[0].enableRegeventDeregistration, true);
   ASSERT_EQ(accounts[0].XCAPRoot, "/XCAP/autotest");

   ASSERT_EQ(accounts[0].tunnelConfig.useTunnel, true);
   ASSERT_EQ(accounts[0].tunnelConfig.tunnelType, TunnelType_TSCF);
   ASSERT_EQ(accounts[0].tunnelConfig.server, "tunnel.autotest.net");
   ASSERT_EQ(accounts[0].tunnelConfig.transportType, TunnelTransport_TCP);
   ASSERT_EQ(accounts[0].tunnelConfig.mediaTransportType, TunnelMediaTransport_StreamOnly);
   ASSERT_EQ(accounts[0].tunnelConfig.redundancyFactor, 1);
   ASSERT_EQ(accounts[0].tunnelConfig.doLoadBalancing, true);
   ASSERT_EQ(accounts[0].tunnelConfig.ignoreCertVerification, true);
   ASSERT_EQ(accounts[0].tunnelConfig.disableNagleAlgorithm, true);

   ASSERT_EQ(accounts[0].capabilities[0].name, "TEST1");
   ASSERT_EQ(accounts[0].capabilities[0].value, "AUTO");
   ASSERT_EQ(accounts[0].capabilities[1].name, "TEST2");
   ASSERT_EQ(accounts[0].capabilities[1].value, "AUTO");

   ASSERT_EQ(accounts[0].additionalFromParameters[0].name, "ADITIONALPARAMETER");
   ASSERT_EQ(accounts[0].additionalFromParameters[0].value, "FROM");
   ASSERT_EQ(accounts[0].additionalFromParameters[1].name, "FROMPARAMETER");
   ASSERT_EQ(accounts[0].additionalFromParameters[1].value, "ADDITIONAL");

   ASSERT_EQ(accounts[0].sourceAddress, "***************");
   ASSERT_EQ(accounts[0].preferPAssertedIdentity, true);
   ASSERT_EQ(accounts[0].autoRetryOnTransportDisconnect, true);
   ASSERT_EQ(accounts[0].keepAliveMode, KeepAliveMode_NoKeepAlives);
   ASSERT_EQ(accounts[0].useRinstance, false);
   ASSERT_EQ(accounts[0].enableNat64Support, false);
   ASSERT_EQ(accounts[0].userCertificatePEM, "-----BEGIN CERTIFICATE-----\n"
      "MIIF6jCCA9KgAwIBAgICEAQwDQYJKoZIhvcNAQELBQAwUjELMAkGA1UEBhMCUlMx\n"
      "DzANBgNVBAgMBlNlcmJpYTEXMBUGA1UECgwOQXV0b3Rlc3QgQ29ycC4xGTAXBgNV\n"
      "****************************************************************\n"
      "NTI3WjBWMQswCQYDVQQGEwJSUzEPMA0GA1UECAwGU2VyYmlhMRYwFAYDVQQKDA1B\n"
      "dXRvdGVzdCBUZXN0MR4wHAYDVQQDDBV0ZXN0QGF1dG90ZXN0LmNwY2FwaTIwggIi\n"
      "MA0GCSqGSIb3DQEBAQUAA4ICDwAwggIKAoICAQCmfOoMV8Ew6nYLk28fnRBLtYn/\n"
      "ChSwpy60hRE85FCgr0sprs2vgEcZRYkRYGOBDAVXlFfYm5KPvPvX1b89pMUtDvSU\n"
      "kz7hgH7Upr5IRWqwRNS+keM/QUsVUkijKQ4F+M2cFowhV8Pv7Ctp7RIJ8jqKa0pT\n"
      "dsLRzZvHWVcOP6orBGjAu7a6kPclTdH0SD1sAUbgirC1SkMU7OjTgXDz3iDcL1dY\n"
      "9S8t2Ft/2mnxtkuxG50yosWLU8IhgkX3Pf0P847TwwS+JXMca2h6rMGgcDyllhCf\n"
      "gg/Ty5MaTXLcTb/O333vUpGIhDsAr172AIpvHiKMekwD499oaOjEh702ChbhonGn\n"
      "wFXv52UjzIdOyDlV/i6KN33OvuhyVrY5Fs7SFpOcoDvWfdU4fyBdT0564Zc2cxwj\n"
      "mpZqbxuICb90CO7ck0EDoZKMQQzx0tmrpCQ5kSZA0D02biIkki7xyD9DzGBP5ssA\n"
      "IlPYAgAah6/cs72cCTgAQ9WSq/VHjLQQ8L+3sgFWlMW05H2Uz6F6s/p4PdKS/0pO\n"
      "Vrh4vSFIboptH1sZwGaWS2FYdmUWJr5ZQEIwB0sAzoohxRsbtaNwwzDw0TLgzPuG\n"
      "el99ekUatpQai5N8rtscn5YE8YP+9w6AUF9uGUX5MIABVLVMOivOryDLtGlMwDVH\n"
      "+DEtjkNuZvnBXGH6GwIDAQABo4HFMIHCMAkGA1UdEwQCMAAwEQYJYIZIAYb4QgEB\n"
      "BAQDAgWgMDMGCWCGSAGG+EIBDQQmFiRPcGVuU1NMIEdlbmVyYXRlZCBDbGllbnQg\n"
      "Q2VydGlmaWNhdGUwHQYDVR0OBBYEFAz0NMu+x89518Zv7cG41Mqlw4/JMB8GA1Ud\n"
      "IwQYMBaAFO01wovQJV+v/BGlIkTcBC0hvVWWMA4GA1UdDwEB/wQEAwIF4DAdBgNV\n"
      "HSUEFjAUBggrBgEFBQcDAgYIKwYBBQUHAwQwDQYJKoZIhvcNAQELBQADggIBAH09\n"
      "GklZWSElPvV44J3VmIOruRPeoll1SuBnBoPkoD9sakjlXc7YgTq+aUKBM4MhKocO\n"
      "TXQDtJ4MfCCwsCXpmkF07HWn1T0ZtyASf2OmOB7OJk0dQqH3rkDmhX4oE+tjCPCc\n"
      "1D7rYrViRPDP+jq3q4IWXLR9tyVqSa5hXpeagITTdZoIk/9Q1y07siDjpw6xZeSV\n"
      "GHCH2CRulaRbARK5o8k1hPlbMGKPObpXQ5/Y2IEazCp1kfPd20xTnKHghcH8aucR\n"
      "ILCWb/+JSTIBrCpg3+c9VuFo1yH+vtYNrya3pnN1Utq8rnopFBnZL6MZUF774ETe\n"
      "iVfBJ07EjcCQF404HPi5bnMR5PIygX9yb1WutLgXmeO20D97TNt8HTGnsU6ENmp1\n"
      "QOeKZT5JI62+5PzeCqYStwgwSJEMMVlAGf+qcYx+WgCepfaNqq6LarRPrvFXTSSr\n"
      "3wfbEWB4muD6hOTfnEmlMeo35aCmpYQGJmJv2pbZi3UcOt5psdas/fwctjZwg0w5\n"
      "E7xl5CdtZA2KpRJjDD6vkfq8qV5sKYzXrLoESQo9m2BCiJhlLb1yMuU7eHbQeYxR\n"
      "PTB/F6PiOYfJfQ6C25PjgcteRyYqM0LrOHdgAm5RfC7YCNC1fWvHBAKdXBTjnFet\n"
      "2qNlWjnQaqeDWk/giu+6PMrZuoAePB0bSYXOs2JD\n"
      "-----END CERTIFICATE-----");
   ASSERT_EQ(accounts[0].userPrivateKeyPEM, "-----BEGIN RSA PRIVATE KEY-----\n"
      "MIIJJwIBAAKCAgEApnzqDFfBMOp2C5NvH50QS7WJ/woUsKcutIURPORQoK9LKa7N\n"
      "r4BHGUWJEWBjgQwFV5RX2JuSj7z719W/PaTFLQ70lJM+4YB+1Ka+SEVqsETUvpHj\n"
      "P0FLFVJIoykOBfjNnBaMIVfD7+wrae0SCfI6imtKU3bC0c2bx1lXDj+qKwRowLu2\n"
      "upD3JU3R9Eg9bAFG4IqwtUpDFOzo04Fw894g3C9XWPUvLdhbf9pp8bZLsRudMqLF\n"
      "i1PCIYJF9z39D/OO08MEviVzHGtoeqzBoHA8pZYQn4IP08uTGk1y3E2/zt9971KR\n"
      "-----END RSA PRIVATE KEY-----");
}

// Intended for debugging only. Not to be run as regular unit test. Change settings depending on desired scenario and office location.
/*
Serbia network setup:
v6only.dunavska.com = IPv6 asterisk6.dunavska.com
v4only.dunavska.com = IPv4 asterisk.dunavska.com

v4noresp.dunavska.com = IPv4  ************** // bogus address
                      = IPv6 asterisk6.dunavska.com

v6noresp.dunavska.com = IPv4 asterisk.dunavska.com
                      = IPv6 2604:a880:1:20::6a3:dead // bogus adress
-----------------------------------------------------------------------

Canada (Ottawa) network setup:
v4only.cpsipv6  IN      1H      A       **************
v6only.cpsipv6  IN      1H      AAAA    2604:a880:1:20::6a3:8001

v6noresp.cpsipv6        IN      1H      A       **************
                        IN      1H      AAAA    2604:a880:1:20::6a3:dead; bogus address - simulate non-responsive server

v4noresp.cpsipv6        IN      1H      A       **************; bogus address - simulate non-responsive server
                        IN      1H      AAAA    2604:a880:1:20::6a3:8001
*/
TEST_F(AccountModuleTest, DISABLED_TryIpV6OverIpV4Network) {
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.ipVersion = CPCAPI2::SipAccount::IpVersion_Auto;
   alice.config.settings.outboundProxy = "v4noresp.dunavska.com";
   alice.config.settings.domain = "asterisk6.dunavska.com";
   alice.config.settings.username = "2000";
   alice.config.settings.password = "1111";
   alice.config.settings.nameServers.push_back("*************");
   alice.init();
   alice.enable();
}

// The SDK should send a DNS SRV record query over UDP, which should be resposned
// to with by unbound with a truncated flag set; the SDK should then re-query
// with DNS over TCP
TEST_F(AccountModuleTest, TcpDnsTest_Srv)
{
#ifdef ANDROID
	std::string dnsServer = getenv("CPCAPI2_DNS_SERVER");
	ASSERT_NE("", dnsServer); // verify that the external DNS server has been set
#endif

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.outboundProxy = "tcpdns.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.enable();
}

TEST_F(AccountModuleTest, TcpDnsTest_SrvV6)
{
#ifdef ANDROID
	std::string dnsServer = getenv("CPCAPI2_DNS_SERVER");
	ASSERT_NE("", dnsServer); // verify that the external DNS server has been set
#endif

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.outboundProxy = "tcpdnsv6.local";
   alice.config.settings.ipVersion = IpVersion_Auto;
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.enable();
}

// The SDK should send a DNS A record query over UDP, which should be resposned
// to with by unbound with a truncated flag set; the SDK should then re-query
// with DNS over TCP
TEST_F(AccountModuleTest, TcpDnsTest_A)
{
#ifdef ANDROID
	std::string dnsServer = getenv("CPCAPI2_DNS_SERVER");
	ASSERT_NE("", dnsServer); // verify that the external DNS server has been set
#endif

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.outboundProxy = "manyarecords.tcpdns.local:6060";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.enable();
}

TEST_F(AccountModuleTest, DnsTest_SrvEqualWeight)
{
   repro::ReproRunner* repro5070 = runRepro("repro_5070.config");
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   int numTimes6060 = 0;
   int numTimes5070 = 0;

   for (int i = 0; i < 20; ++i)
   {
      TestAccount alice("alice", Account_NoInit);
      alice.config.settings.outboundProxy = "equalweights.local";
      alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
      alice.config.settings.sipTransportType = SipAccountTransport_Auto;
      alice.enable();

      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountClientAuthEvent evt;
      ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandlerInternal::onClientAuth", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(200, evt.responseStatusCode);
      ASSERT_FALSE(evt.willSendUpdatedRequest);
      if (evt.responseSourcePort == 5070)
      {
         ++numTimes5070;
      }
      else if (evt.responseSourcePort == 6060)
      {
         ++numTimes6060;
      }

   }

   // Assuming actually random, 1% chance of failuure
   ASSERT_GE(numTimes6060, 4);
   ASSERT_GE(numTimes5070, 4);
   
   safeCout("numTimes6060:" << numTimes6060 << ", numTimes5070:" << numTimes5070);

   ASSERT_EQ(20, numTimes6060 + numTimes5070);

   delete repro5070;
}

void verifyStrettoTunnelEnableAccount(SipAccountTransportType sipTransportType)
{
   VccsTestHarness harness( TestEnvironmentConfig::testResourcePath() + "vccs_test_harness_scenarios/Stretto_Tunnel_Connect.dat", 8989, true );
   harness.start();

   {
      TestAccount alice("alice", Account_NoInit);
      alice.config.settings.useRegistrar = false;
      alice.config.settings.tunnelConfig.useTunnel = true;
      alice.config.settings.sipTransportType = sipTransportType;
      alice.config.settings.tunnelConfig.tunnelType = TunnelType_StrettoTunnel;
      alice.config.settings.tunnelConfig.strettoTunnelURL = "wss://127.0.0.1:8989";
      alice.config.settings.ignoreCertVerification = true;
      alice.config.settings.tunnelConfig.strettoTunnelSessionID = "tunnelSessionID_test";
      alice.config.settings.tunnelConfig.strettoTunnelToken = "tunnelToken_test";
      alice.init();
      alice.enable(false);
      assertAccountRegistering(alice);
      assertAccountRegistered(alice);
   }
}

TEST_F(AccountModuleTest, StrettoTunnelEnableAccount) {

   // the SipTransportType used shouldn't matter -- should be ignored

   // try separate registrations with each transport; each should succeed

   verifyStrettoTunnelEnableAccount(SipAccountTransport_UDP);
   verifyStrettoTunnelEnableAccount(SipAccountTransport_TCP);
   verifyStrettoTunnelEnableAccount(SipAccountTransport_TLS);
}

// OBELISK-5927
TEST_F(AccountModuleTest, AccountEnableNoNetworkAvailableDestroyAccount) {
   TestAccount alice("alice", Account_Init);

   class MySipAccountAdornmentHandler : public SipAccountAdornmentHandler {
   public:
      MySipAccountAdornmentHandler() {}
      ~MySipAccountAdornmentHandler()
      {
      }

      virtual int onAccountAdornment(CPCAPI2::SipAccount::SipAccountHandle account, const SipAccountAdornmentEvent& args)
      {
         ADD_FAILURE() << "Account should not send out any SIP traffic in this test";
         return kSuccess;
      }
   };
   std::unique_ptr<MySipAccountAdornmentHandler> aliceHandler(new MySipAccountAdornmentHandler());
   alice.account->setAdornmentHandler(alice.handle, aliceHandler.get());

   alice.network->setNetworkTransport(TransportNone);
   std::set<resip::Data> ifaces;
   ifaces.insert("**************");
   alice.network->setMockInterfaces(ifaces);
   // TODO: we should adjust the network change manager to use AutoTestReadCallback,
   // so we can avoid this sleep
   std::this_thread::sleep_for(std::chrono::seconds(10));

   // Step 1: enable an account with no network active
   alice.account->enable(alice.handle);

   CPCAPI2::SipAccount::SipAccountHandle aliceHandle;
   CPCAPI2::SipAccount::SipAccountEnabledEvent enabledEvt;
   // check for this internal event now, to clear it, since we also want to check for it later
   ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandlerInternal::onAccountEnabled",
                              20000, CPCAPI2::test::AlwaysTruePred(), aliceHandle, enabledEvt));

   CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
   // Step 2: verify account does not enable
   ASSERT_FALSE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged",
      __FILE__, 5000, CPCAPI2::test::AlwaysTruePred(), aliceHandle, evt));

   // Step 2: destroy the account
   // TODO: how can we programatically verify the account has been destroyed here?
   alice.account->destroy(alice.handle);
   std::this_thread::sleep_for(std::chrono::seconds(10));

   // Step 3: after some time, re-enable the network
   alice.network->setNetworkTransport(TransportWiFi);
   ifaces.insert("**************");
   alice.network->setMockInterfaces(ifaces);

   SipAccountDestroyedEvent destroyedEvt;
   ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandlerInternal::onAccountDestroyed",
                           10000, CPCAPI2::test::AlwaysTruePred(), alice.handle, destroyedEvt));

   // Step 4: wait and ensure the adornment handler does not receive any traffic
   std::this_thread::sleep_for(std::chrono::seconds(20));
}

// OBELISK-5927
TEST_F(AccountModuleTest, AccountNoEnableDestroy) {
   TestAccount alice("alice", Account_Init);

   alice.account->destroy(alice.handle);

   SipAccountDestroyedEvent evt;
   ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandlerInternal::onAccountDestroyed",
                           10000, CPCAPI2::test::AlwaysTruePred(), alice.handle, evt));

   alice.account = NULL;
}

// SCORE-1150
TEST_F(AccountModuleTest, AccountNoEnableDoubleDestroy) {
   TestAccount alice("alice", Account_Init);

   alice.account->destroy(alice.handle);
   // this second delete caused SipAccountImpl::destroyImpl to invoke reactorSafeRelease twice on the
   // same object, which was not safe and would lead to a crash.
   alice.account->destroy(alice.handle);

   SipAccountDestroyedEvent evt;
   ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandlerInternal::onAccountDestroyed",
                           10000, CPCAPI2::test::AlwaysTruePred(), alice.handle, evt));

   alice.account = NULL;
}

// SCORE-1150
TEST_F(AccountModuleTest, AccountEnableDoubleDestroy) {
   const bool disableOnDestruct = false;
   TestAccount alice("alice", Account_Enable, disableOnDestruct);

   alice.account->destroy(alice.handle);
   alice.account->destroy(alice.handle);

   SipAccountDestroyedEvent evt;
   ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandlerInternal::onAccountDestroyed",
                           10000, CPCAPI2::test::AlwaysTruePred(), alice.handle, evt));

   alice.account = NULL;
}

TEST_F(AccountModuleTest, AccountEnableNoNetworkAvailableToNetworkAvailable) {
   TestAccount alice("alice", Account_Init);

   alice.network->setNetworkTransport(TransportNone);
   std::set<resip::Data> ifaces;
   ifaces.insert("**************");
   alice.network->setMockInterfaces(ifaces);
   // TODO: we should adjust the network change manager to use AutoTestReadCallback,
   // so we can avoid this sleep
   std::this_thread::sleep_for(std::chrono::seconds(10));

   // Step 1: enable an account with no network active
   alice.account->enable(alice.handle);

   CPCAPI2::SipAccount::SipAccountHandle aliceHandle;
   CPCAPI2::SipAccount::SipAccountEnabledEvent enabledEvt;
   // check for this internal event now, to clear it, since we also want to check for it later
   ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandlerInternal::onAccountEnabled",
                              20000, CPCAPI2::test::AlwaysTruePred(), aliceHandle, enabledEvt));

   CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
   // Step 2: verify account does not enable
   ASSERT_FALSE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged",
      __FILE__, 5000, CPCAPI2::test::AlwaysTruePred(), aliceHandle, evt));

   // Step 3: bring network back
   // TODO: how can we programatically verify the account has been destroyed here?
   alice.network->setNetworkTransport(TransportWiFi);
   ifaces.insert("**************");
   alice.network->setMockInterfaces(ifaces);

   assertAccountRegistering(alice);
   assertAccountRegistered(alice);
}

TEST_F(AccountModuleTest, DomainWildcardMatch) {
   ASSERT_EQ(CPCAPI2::BrandingHelper::isDomainWildcardMatch("cpcapi2.net", "cpcapi2.net"), true);
   ASSERT_EQ(CPCAPI2::BrandingHelper::isDomainWildcardMatch("cpcapi2.net", "*.cpcapi2.net"), true);
   ASSERT_EQ(CPCAPI2::BrandingHelper::isDomainWildcardMatch("some.subdomain.cpcapi2.net", "*.cpcapi2.net"), true);
   ASSERT_EQ(CPCAPI2::BrandingHelper::isDomainWildcardMatch("some-subdomain.cpc-api2.net", "*.cpc-api2.net"), true);
   ASSERT_EQ(CPCAPI2::BrandingHelper::isDomainWildcardMatch("some.subdomain.cpcapi2.net", "*.subdomain.cpcapi2.net"), true);
   ASSERT_EQ(CPCAPI2::BrandingHelper::isDomainWildcardMatch("Some.SubDomain.CpcApi2.net", "*.CPCAPI2.net"), true);

   //branding typo?
   ASSERT_EQ(CPCAPI2::BrandingHelper::isDomainWildcardMatch("cpcapi2.net", "*cpcapi2.net"), true);
   ASSERT_EQ(CPCAPI2::BrandingHelper::isDomainWildcardMatch("subdomain.cpcapi2.net", "*cpcapi2.net"), false);
   ASSERT_EQ(CPCAPI2::BrandingHelper::isDomainWildcardMatch("somethingcpcapi2.net", "*cpcapi2.net"), false);

   //subdomain not allowed
   ASSERT_EQ(CPCAPI2::BrandingHelper::isDomainWildcardMatch("subdomain.cpcapi2.net", "cpcapi2.net"), false);
   //invalid domain syntax
   ASSERT_EQ(CPCAPI2::BrandingHelper::isDomainWildcardMatch("some_subdomain.cpc-api2.net", "*.cpc-api2.net"), false);
   ASSERT_EQ(CPCAPI2::BrandingHelper::isDomainWildcardMatch("*.cpcapi2.net", "*.cpcapi2.net"), false);
   ASSERT_EQ(CPCAPI2::BrandingHelper::isDomainWildcardMatch("st*r.cpc.cpcapi2.net", "*.cpcapi2.net"), false);
   ASSERT_EQ(CPCAPI2::BrandingHelper::isDomainWildcardMatch("wh?t.cpcapi2.net", "*.cpcapi2.net"), false);
   ASSERT_EQ(CPCAPI2::BrandingHelper::isDomainWildcardMatch("yes!.cpcapi2.net", "*.cpcapi2.net"), false);
   //top-level domain doesn't mach
   ASSERT_EQ(CPCAPI2::BrandingHelper::isDomainWildcardMatch("some.subdomain.cpcapi2.net", "*.cpcapi2.com"), false);
   //domain doesn't match
   ASSERT_EQ(CPCAPI2::BrandingHelper::isDomainWildcardMatch("some.subdomain.cpcapi2.net", "*.cpc-api2.net"), false);
   //try to cheat
   ASSERT_EQ(CPCAPI2::BrandingHelper::isDomainWildcardMatch("some.subdomain.cpcapi2.com.net", "*.cpcapi2.com"), false);
   //IRL
   ASSERT_EQ(CPCAPI2::BrandingHelper::isDomainWildcardMatch("auca.sip.us.pub.alzcc.net", "*.cymbus.net *.alzcc.net"), true);
   ASSERT_EQ(CPCAPI2::BrandingHelper::isDomainWildcardMatch(".auca.sip.us.pub.alzcc.net", "*.cymbus.net *.alzcc.net"), false);
}

TEST_F(AccountModuleTest, AccountDomainLockEnableSuccess) {
   TestAccount alice("alice", Account_Init);

   cpc::string domainLockStr = "*." + alice.config.settings.domain + " *.cymbus.net";
   safeCout("domain: " << alice.config.settings.domain << " CPCAPI2_BRAND_LICENSE_DOMAIN_LOCK: " << domainLockStr);
   CPCAPI2::SipAccount::SipAccountManagerInternal* mi = dynamic_cast<CPCAPI2::SipAccount::SipAccountManagerInternal*>(alice.account);
   mi->setDomainLockString(domainLockStr);

   ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);

   assertAccountRegistering(alice);
   assertAccountRegistered(alice);
}

TEST_F(AccountModuleTest, AccountDomainLockEnableFailure) {
   TestAccount alice("alice", Account_Init);

   cpc::string domainLockStr = "*.alianza.com *.cymbus.net";
   safeCout("domain: " << alice.config.settings.domain << " CPCAPI2_BRAND_LICENSE_DOMAIN_LOCK: " << domainLockStr);
   CPCAPI2::SipAccount::SipAccountManagerInternal* mi = dynamic_cast<CPCAPI2::SipAccount::SipAccountManagerInternal*>(alice.account);
   mi->setDomainLockString(domainLockStr);

   ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);

   CPCAPI2::SipAccount::SipAccountHandle h;
   CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
   ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged",
      20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Unregistered, evt.accountStatus);
   ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Domain_Locked, evt.reason);
}

TEST_F(AccountModuleTest, AccountNoDomainLockEnableSuccess) {
   TestAccount alice("alice", Account_Init);

   cpc::string domainLockStr = "";
   safeCout("domain: " << alice.config.settings.domain << " CPCAPI2_BRAND_LICENSE_DOMAIN_LOCK: " << domainLockStr);
   CPCAPI2::SipAccount::SipAccountManagerInternal* mi = dynamic_cast<CPCAPI2::SipAccount::SipAccountManagerInternal*>(alice.account);
   mi->setDomainLockString(domainLockStr);

   ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);

   assertAccountRegistering(alice);
   assertAccountRegistered(alice);
}

TEST_F(AccountModuleTest, AccountNoDomainLockEnableSuccess2) {
   TestAccount alice("alice", Account_Init);

   cpc::string domainLockStr = "%BRAND_LICENSE_DOMAIN_LOCK%";
   safeCout("domain: " << alice.config.settings.domain << " CPCAPI2_BRAND_LICENSE_DOMAIN_LOCK: " << domainLockStr);
   CPCAPI2::SipAccount::SipAccountManagerInternal* mi = dynamic_cast<CPCAPI2::SipAccount::SipAccountManagerInternal*>(alice.account);
   mi->setDomainLockString(domainLockStr);

   ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);

   assertAccountRegistering(alice);
   assertAccountRegistered(alice);
}

// DNS A records present only; no SRV records present
TEST_F(AccountModuleTest, DnsTest_ARecordOnly)
{
#ifdef ANDROID
	GTEST_SKIP();
#endif
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_5060 = runRepro("repro_5060.config");

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.outboundProxy = "working.cpcapi2.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.enable();

   delete repro_5060;
   runOriginalRepro();
}

TEST_F(AccountModuleTest, DnsTest_OverrideDefaultSipPort_UDP)
{
#ifdef ANDROID
	GTEST_SKIP();
#endif
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.defaultSipPort = 6060;     // non-secure transport
   alice.config.settings.outboundProxy = "working.cpcapi2.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.enable();
}

TEST_F(AccountModuleTest, DnsTest_OverrideDefaultSipPort_TCP)
{
#ifdef ANDROID
	GTEST_SKIP();
#endif
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.defaultSipPort = 6060;     // non-secure transport
   alice.config.settings.outboundProxy = "working.cpcapi2.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_TCP;
   alice.enable();
}

TEST_F(AccountModuleTest, DnsTest_OverrideDefaultSipPort_TLS)
{
#ifdef ANDROID
	GTEST_SKIP();
#endif
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.defaultSipsPort = 6061;    // secure transport
   alice.config.settings.outboundProxy = "working.cpcapi2.local";
   alice.config.settings.ignoreCertVerification = true;
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_TLS;
   alice.enable();
}

TEST_F(AccountModuleTest, DnsTest_NoOverrideDefaultSipPort_TCP)
{
#ifdef ANDROID
	GTEST_SKIP();
#endif

   DummyTcpListener::Config dtConfig;
   dtConfig.port = 5060; // match default SIP port that would be used initially below at (a)
   
   // to ensure we have a timeout at the SIP transaction level and generate an internal 408
   // vs TCP connection rejection and internal 503, we spin up the dummy TCP listener.
   DummyTcpListener tcpListener(dtConfig);
   ASSERT_GT(tcpListener.start(), 0);

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.overrideMsecsTimerF = 5000;
   alice.config.settings.outboundProxy = "working.cpcapi2.local"; // (a)
   alice.config.settings.ignoreCertVerification = true;
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_TCP;
   alice.enable(false);

   assertAccountRegistering(alice);
   std::this_thread::sleep_for(std::chrono::seconds(7)); // sleep for 7 seconds so the registering will time out (at 5 seconds set above)
   assertAccountWaitingToRegister(alice);

   alice.config.settings.defaultSipPort = 6060;    // non-secure transport
   ASSERT_EQ(alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings), kSuccess);
   ASSERT_EQ(alice.account->applySettings(alice.handle), kSuccess);

   // The default port setting only gets applied when the account is enabled, so we toggle to force setting it.
   alice.disable(false);
   alice.enable(true); 
}

#ifdef ANDROID

TEST_F(AccountModuleTest, AccountEnableDisable_AndroidBackgroundMgrSanity) {
   TestAccount alice("alice", Account_Init);
   alice.init();

   alice.enableAndroidBackgrounding(std::shared_ptr<AndroidBackgroundingBookkeeper>());

   alice.enable();
   std::this_thread::sleep_for(std::chrono::seconds(10));
   alice.disable();

   alice.disableAndroidBackgrounding();
}

TEST_F(AccountModuleTest, AccountEnableDisable_AndroidBackgroundMgrWakelockVerify) {
   TestAccount alice("alice", Account_Init);
   alice.init();

   std::shared_ptr<AndroidBackgroundingBookkeeper> bgBookkeeper = std::make_shared<AndroidBackgroundingBookkeeper>();
   alice.enableAndroidBackgrounding(bgBookkeeper);

   alice.enable();
   std::this_thread::sleep_for(std::chrono::seconds(60));
   alice.disable();

   safeCout("Wakelock count before clear: " << bgBookkeeper->getWakeAcquireCount());
   safeCout("Wakelock total duration before clear: " << bgBookkeeper->getTotalWakeAcquireDurationMs().count() << " ms");

   // there should be a moderate amount of wakelock activity with one SIP account enabled
   ASSERT_GE(bgBookkeeper->getTotalWakeAcquireDurationMs().count(), 50);
   // todo: are wakelocks being held for too long in this case? some test runs have reported 15+ sec
   ASSERT_LE(bgBookkeeper->getTotalWakeAcquireDurationMs().count(), 20000);
   //ASSERT_GE(bgBookkeeper->getWakeAcquireCount(), 3);
   ASSERT_LE(bgBookkeeper->getWakeAcquireCount(), 12);

   // clear the counters so we can observe wakelock behaviour with the account disabled
   bgBookkeeper->clearCounters();

   std::this_thread::sleep_for(std::chrono::seconds(150));

   // there should be minimal wakelock activity with no account enabled
   ASSERT_LE(bgBookkeeper->getTotalWakeAcquireDurationMs().count(), 100);
   ASSERT_LE(bgBookkeeper->getWakeAcquireCount(), 2);

   safeCout("Wakelock count after check: " << bgBookkeeper->getWakeAcquireCount());
   safeCout("Wakelock total after check: " << bgBookkeeper->getTotalWakeAcquireDurationMs().count() << " ms");

   alice.disableAndroidBackgrounding();
}
std::atomic_bool condition = false;
bool selectHook()
{
   condition = !condition;
   return condition;
}

TEST_F(AccountModuleTest, AccountEnableDisable_AndroidBackgroundMgrWakelockVerify2) {
   TestAccount alice("alice", Account_Init);
   alice.init();

   std::shared_ptr<AndroidBackgroundingBookkeeper> bgBookkeeper = std::make_shared<AndroidBackgroundingBookkeeper>();
   alice.enableAndroidBackgrounding(bgBookkeeper);
   //drop alternate onSelect() processing to force wake locks
   CPCAPI2::BackgroundManager::Instance()->setResipReactorSelectHook(selectHook);

   alice.enable();
   std::this_thread::sleep_for(std::chrono::seconds(45));
   alice.disable();
   
   safeCout("Wakelock count before clear: " << bgBookkeeper->getWakeAcquireCount());
   safeCout("Wakelock total duration before clear: " << bgBookkeeper->getTotalWakeAcquireDurationMs().count() << " ms");

   // in theory we should get at least 1 wakelock in 45s with selectHook
   ASSERT_GE(bgBookkeeper->getTotalWakeAcquireDurationMs().count(), 5000);
   ASSERT_GE(bgBookkeeper->getWakeAcquireCount(), 1);

   bgBookkeeper->clearCounters();

   condition = false;
   std::this_thread::sleep_for(std::chrono::seconds(185));

   // in theory we should get at least 1 wakelock in 185s with selectHook when account unregistered
   ASSERT_GE(bgBookkeeper->getTotalWakeAcquireDurationMs().count(), 60000);
   ASSERT_GE(bgBookkeeper->getWakeAcquireCount(), 1);

   safeCout("Wakelock count after check: " << bgBookkeeper->getWakeAcquireCount());
   safeCout("Wakelock total after check: " << bgBookkeeper->getTotalWakeAcquireDurationMs().count() << " ms");

   CPCAPI2::BackgroundManager::Instance()->setResipReactorSelectHook(NULL);
   std::this_thread::sleep_for(std::chrono::seconds(2));
   alice.disableAndroidBackgrounding();
}
#endif // ANDROID

TEST_F(AccountModuleTest, IPv4SettingIPv6NetworkChange)
{
   #if (defined ANDROID || TARGET_OS_IPHONE)
      GTEST_SKIP();
   #endif

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.ipVersion = IpVersion_V4;
   alice.config.settings.networkChangeFilter = NetworkChangeFilter_IpVer;
   alice.enable();

   std::set<resip::Data> ifacesOld, ifaces;
   ifaces.insert("**************");
   ifacesOld.insert("**************");
   ifacesOld.insert("2345:0425:2CA1:0000:0000:0567:5673:23b5");
   alice.network->setNetworkTransport(NetworkTransport::TransportWiFi);
   alice.network->setMockInterfaces(ifaces, &ifacesOld);
   CPCAPI2::SipAccount::SipAccountHandle h;
   CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
   ASSERT_FALSE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged",
      __FILE__, 10000, CPCAPI2::test::AlwaysTruePred(), h, evt));
}

TEST_F(AccountModuleTest, IPv4SettingIPv4NetworkChange)
{
   #if (defined ANDROID || TARGET_OS_IPHONE)
      GTEST_SKIP();
   #endif

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.ipVersion = IpVersion_V4;
   alice.config.settings.networkChangeFilter = NetworkChangeFilter_IpVer;
   alice.enable();

   std::set<resip::Data> ifacesOld, ifaces;
   ifaces.insert("**************");
   ifacesOld.insert("**************");
   ifacesOld.insert("**************");
   alice.network->setNetworkTransport(NetworkTransport::TransportWiFi);
   alice.network->setMockInterfaces(ifaces, &ifacesOld);

   assertAccountRefreshing(alice);
   assertAccountRegistered(alice);
}

TEST_F(AccountModuleTest, DISABLED_IPv6SettingIPv4NetworkChange)
{
   #if (defined ANDROID || TARGET_OS_IPHONE)
      GTEST_SKIP();
   #endif

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.ipVersion = IpVersion_V6;
   alice.config.settings.networkChangeFilter = NetworkChangeFilter_IpVer;
   alice.enable();

   std::set<resip::Data> ifacesOld, ifaces;
   ifaces.insert("2345:0425:2CA1:0000:0000:0567:5673:23b5");
   ifacesOld.insert("**************");
   ifacesOld.insert("2345:0425:2CA1:0000:0000:0567:5673:23b5");
   alice.network->setNetworkTransport(NetworkTransport::TransportWiFi);
   alice.network->setMockInterfaces(ifaces, &ifacesOld);
   CPCAPI2::SipAccount::SipAccountHandle h;
   CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
   ASSERT_FALSE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged",
      __FILE__, 10000, CPCAPI2::test::AlwaysTruePred(), h, evt));
}

TEST_F(AccountModuleTest, DISABLED_IPv6SettingIPv6NetworkChange)
{
   #if (defined ANDROID || TARGET_OS_IPHONE)
      GTEST_SKIP();
   #endif

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.ipVersion = IpVersion_V6;
   alice.config.settings.networkChangeFilter = NetworkChangeFilter_IpVer;
   alice.enable();

   std::set<resip::Data> ifacesOld, ifaces;
   ifaces.insert("2345:0425:2CA1:0000:0000:0567:5673:23b5");
   ifacesOld.insert("2345:0425:2CA1:0000:0000:0567:5673:23b5");
   ifacesOld.insert("2345:0425:2CA1:0000:0000:0567:5673:34c6");
   alice.network->setNetworkTransport(NetworkTransport::TransportWiFi);
   alice.network->setMockInterfaces(ifaces, &ifacesOld);

   assertAccountRefreshing(alice);
   assertAccountRegistered(alice);
}

TEST_F(AccountModuleTest, RegistrationCallID)
{
   TestAccount alice("alice", Account_Init);

   ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);
   assertAccountRegistering(alice);

   CPCAPI2::SipAccount::SipAccountHandle h;
   CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
   ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged",
      20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);

   safeCout("CallID: " << evt.callId << " addr: " << evt.localIpAddress << " port: " << evt.localPort);
   ASSERT_TRUE(!evt.localIpAddress.empty());
   ASSERT_TRUE(evt.localPort > 0);
   ASSERT_TRUE(!evt.callId.empty());
}

}  // namespace
