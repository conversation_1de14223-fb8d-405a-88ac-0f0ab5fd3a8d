#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#if (CPCAPI2_BRAND_BIEVENTS_MODULE == 1)

#include "cpcapi2_test_fixture.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>
#include "server_http.hpp"

#include "test_framework/cpcapi2_test_framework.h"
#include "test_framework/bare_bones_sip_endpoint.h"
#include "bi_test_harness/BITestHarness.h"

#include <cpcstl/string.h>
#include <string>
#include <thread>
#include <future>
#include <memory>

using namespace CPCAPI2;
using namespace CPCAPI2::BIEvents;
using namespace CPCAPI2::test;

#ifdef _WIN32
#include <Windows.h>
#else
#include <sys/time.h>
#endif

#define SDK_SOURCE "SDKUT"

namespace {

   class BIEventsModuleTest : public CpcapiAutoTest
   {
   public:
      BIEventsModuleTest() {}
      virtual ~BIEventsModuleTest() {}
      
      BIEventsSettings defaultTestSettings()
      {
         BIEventsSettings settings;
         settings.cacheDirectory = TestEnvironmentConfig::tempPath();
         
         return settings;
      }
      
   };

   TEST_F(BIEventsModuleTest, EvilTimestampTest) {

      // listens for requests at /test
      BITestHarness testHarness;
      testHarness.start();

      TestAccount alice("alice", Account_Init);

      BIEventsSettings settings(defaultTestSettings());
      settings.authUser          = "biuser";
      settings.authPass          = "fluffybunny";
      //settings.serverLocationURL = "https://bi.bria-x.net/API/1.0";
      settings.serverLocationURL = "http://127.0.0.1:9090/API/1.0";
      settings.httpVerboseLogging = true;
      settings.slidingWindowDeltaMillis = 100;
      //settings.cacheFileSize = 2048;

      // Create sessions
      alice.biEventManager->configureSettings( alice.biEventHandle, settings );
      alice.biEventManager->purgeCache( alice.biEventHandle );

      // Connect alice
      alice.biEventManager->enable( alice.biEventHandle );

      BIEventHeader eventHeader;
      eventHeader.Source  = SDK_SOURCE;
      eventHeader.Type    = "T_EVT";
      eventHeader.Group   = 1234;
      eventHeader.Context = 1234;
      eventHeader.Summary = "SDK Test Event";
      eventHeader.MillisSinceEpoch = INT64_MAX - 1000;

      BIEventBody eventBody;
      BIPair pair;
      pair.Name = "str_value";
      pair.Value.ValueType = BIValueType_string;
      pair.Value.StringValue = "Just a string value";
      eventBody.push_back( pair );

      EventID evtID;
      alice.biEventManager->postEvent( alice.biEventHandle, eventHeader, eventBody, evtID );

      // Alice waits for the sync event to know that the request has finished
      auto aliceEvents = std::async(std::launch::async, [&] () {
         BIEventsHandle h;
         OnPostSuccessEvent evt;
         ASSERT_TRUE( cpcWaitForEvent( 
            alice.biEvents,
            "BIEventsHandler::onEventSuccess",
            10000,
            AlwaysTruePred(),
            h, evt));
         ASSERT_TRUE( evt.id == evtID );
      });
      waitFor( aliceEvents );

      // Log out alice and bob
      alice.biEventManager->disable( alice.biEventHandle );
      alice.biEventManager->destroy( alice.biEventHandle );
      auto aliceDestroy = std::async(std::launch::async, [&] () {
         BIEventsHandle h;
         OnDestroyEvent evt;
         ASSERT_TRUE( cpcWaitForEvent( 
            alice.biEvents,
            "BIEventsHandler::onDestroy",
            10000,
            AlwaysTruePred(),
            h, evt));
      });
      waitFor( aliceDestroy );

      testHarness.stop();
      testHarness.waitForCompletion();
   }

   TEST_F(BIEventsModuleTest, PostTest) {

      // listens for requests at /test
      BITestHarness testHarness;
      testHarness.start();

      TestAccount alice("alice", Account_Init);

      BIEventsSettings settings(defaultTestSettings());
      settings.authUser          = "biuser";
      settings.authPass          = "fluffybunny";
      //settings.serverLocationURL = "https://bi.bria-x.net/API/1.0";
      settings.serverLocationURL = "http://127.0.0.1:9090/API/1.0";
      settings.slidingWindowDeltaMillis = 100;
      //settings.cacheFileSize = 2048;

      // Create sessions
      alice.biEventManager->configureSettings( alice.biEventHandle, settings );

      // Make sure everything is cleaned up prior to unit test
      alice.biEventManager->purgeCache( alice.biEventHandle );

      // Connect alice
      alice.biEventManager->enable( alice.biEventHandle );

      BIEventHeader eventHeader;
      eventHeader.Source  = SDK_SOURCE;
      eventHeader.Type    = "T_EVT";
      eventHeader.Group   = 1234;
      eventHeader.Context = 1234;
      eventHeader.Summary = "SDK Test Event";

      BIEventBody eventBody;
      BIPair pair;
      pair.Name = "str_value";
      pair.Value.ValueType = BIValueType_string;
      pair.Value.StringValue = "Just a string value";
      eventBody.push_back( pair );

      EventID evtID;
      alice.biEventManager->postEvent( alice.biEventHandle, eventHeader, eventBody, evtID );

      // Alice waits for the sync event to know that the request has finished
      auto aliceEvents = std::async(std::launch::async, [&] () {
         BIEventsHandle h;
         OnPostSuccessEvent evt;
         ASSERT_TRUE( cpcWaitForEvent( 
            alice.biEvents,
            "BIEventsHandler::onEventSuccess",
            10000,
            AlwaysTruePred(),
            h, evt));
         ASSERT_TRUE( evt.id == evtID );
      });
      waitFor( aliceEvents );

      // Log out alice and bob
      alice.biEventManager->disable( alice.biEventHandle );
      alice.biEventManager->destroy( alice.biEventHandle );
      auto aliceDestroy = std::async(std::launch::async, [&] () {
         BIEventsHandle h;
         OnDestroyEvent evt;
         ASSERT_TRUE( cpcWaitForEvent( 
            alice.biEvents,
            "BIEventsHandler::onDestroy",
            10000,
            AlwaysTruePred(),
            h, evt));
      });
      waitFor( aliceDestroy );

      testHarness.stop();
      testHarness.waitForCompletion();
   }

   TEST_F(BIEventsModuleTest, BadAddress) {

      TestAccount alice("alice", Account_Init);

      BIEventsSettings settings(defaultTestSettings());
      settings.authUser          = "biuser";
      settings.authPass          = "fluffybunny";
      settings.serverLocationURL = "https://bripha.bria-y.net/API/1.0";

      // Create sessions
      alice.biEventManager->configureSettings( alice.biEventHandle, settings );

      // Make sure everything is cleaned up prior to unit test
      alice.biEventManager->purgeCache( alice.biEventHandle );

      // Connect alice
      alice.biEventManager->enable( alice.biEventHandle );

      BIEventHeader eventHeader;
      eventHeader.Source  = SDK_SOURCE;
      eventHeader.Type    = "T_EVT";
      eventHeader.Group   = 1234;
      eventHeader.Context = 1234;
      eventHeader.Summary = "SDK Test Event";

      BIEventBody eventBody;
      BIPair pair;
      pair.Name = "str_value";
      pair.Value.ValueType = BIValueType_string;
      pair.Value.StringValue = "Just a string value";
      eventBody.push_back( pair );

      EventID evtID;
      alice.biEventManager->postEvent( alice.biEventHandle, eventHeader, eventBody, evtID );

      // Alice waits for the sync event to know that the request has finished
      auto aliceEvents = std::async(std::launch::async, [&] () {
         BIEventsHandle h;
         OnErrorEvent evt;
         ASSERT_TRUE( cpcWaitForEvent( 
            alice.biEvents,
            "BIEventsHandler::onError",
            10000,
            AlwaysTruePred(),
            h, evt));
      });
      waitFor( aliceEvents );

      // Log out alice and bob
      alice.biEventManager->disable( alice.biEventHandle );
      alice.biEventManager->destroy( alice.biEventHandle );
      auto aliceDestroy = std::async(std::launch::async, [&] () {
         BIEventsHandle h;
         OnDestroyEvent evt;
         ASSERT_TRUE( cpcWaitForEvent( 
            alice.biEvents,
            "BIEventsHandler::onDestroy",
            10000,
            AlwaysTruePred(),
            h, evt));
      });
      waitFor( aliceDestroy );
   }

   TEST_F(BIEventsModuleTest, SlowDruidResponseDuringShutdown)
   {
      BIEventBody emptyBody;
      BITestSimulationFailureOptions simFailureOptions;
      simFailureOptions.druidResponseDelayMs = 15000;
      
      BITestHarness testHarness( "/API/1.0", 9090, simFailureOptions );
      testHarness.start();
    
      TestAccount alice("alice", Account_Init);

      BIEventsSettings settings(defaultTestSettings());
      settings.authUser          = "biuser";
      settings.authPass          = "fluffybunny";
      settings.serverLocationURL = "http://127.0.0.1:9090/API/1.0";
      settings.slidingWindowDeltaMillis = 5000;
      
      // Create sessions
      alice.biEventManager->configureSettings( alice.biEventHandle, settings );

      // Make sure everything is cleaned up prior to unit test
      alice.biEventManager->purgeCache( alice.biEventHandle );

      // Connect alice
      alice.biEventManager->enable( alice.biEventHandle );

      EventID evtID1;
      
      alice.biEventHelper->postUIEvent( alice.biEventHandle, SDK_SOURCE, "", "1", CPCAPI2_BIEVENTS_UIVERB_SHOW, "Main Window Title", emptyBody, evtID1 );
      
      // wait a few seconds for the DRUID request to go out
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));

      alice.biEventManager->disable( alice.biEventHandle );
      alice.biEventManager->destroy( alice.biEventHandle );

      alice.shutdown();

      testHarness.stop();
      testHarness.waitForCompletion();
   }
   
   TEST_F(BIEventsModuleTest, SlowAuthResponseDuringShutdown)
   {
      BIEventBody emptyBody;
      BITestSimulationFailureOptions simFailureOptions;
      simFailureOptions.authResponseDelayMs = 15000;
      
      BITestHarness testHarness( "/API/1.0", 9090, simFailureOptions );
      testHarness.start();
    
      TestAccount alice("alice", Account_Init);

      BIEventsSettings settings(defaultTestSettings());
      settings.authUser          = "biuser";
      settings.authPass          = "fluffybunny";
      settings.serverLocationURL = "http://127.0.0.1:9090/API/1.0";
      settings.slidingWindowDeltaMillis = 5000;
      
      // Create sessions
      alice.biEventManager->configureSettings( alice.biEventHandle, settings );

      // Make sure everything is cleaned up prior to unit test
      alice.biEventManager->purgeCache( alice.biEventHandle );

      // Connect alice
      alice.biEventManager->enable( alice.biEventHandle );

      EventID evtID1;
      
      alice.biEventHelper->postUIEvent( alice.biEventHandle, SDK_SOURCE, "", "1", CPCAPI2_BIEVENTS_UIVERB_SHOW, "Main Window Title", emptyBody, evtID1 );
      
      // wait a few seconds for the auth request to go out
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));

      alice.biEventManager->disable( alice.biEventHandle );
      alice.biEventManager->destroy( alice.biEventHandle );

      alice.shutdown();

      testHarness.stop();
      testHarness.waitForCompletion();
   }

   TEST_F(BIEventsModuleTest, SlowEventResponseDuringShutdown)
   {
      BIEventBody emptyBody;
      BITestSimulationFailureOptions simFailureOptions;
      simFailureOptions.otherResponseDelayMs = 15000;
      
      BITestHarness testHarness( "/API/1.0", 9090, simFailureOptions );
      testHarness.start();
    
      TestAccount alice("alice", Account_Init);

      BIEventsSettings settings(defaultTestSettings());
      settings.authUser          = "biuser";
      settings.authPass          = "fluffybunny";
      settings.serverLocationURL = "http://127.0.0.1:9090/API/1.0";
      settings.slidingWindowDeltaMillis = 5000;
      
      // Create sessions
      alice.biEventManager->configureSettings( alice.biEventHandle, settings );

      // Make sure everything is cleaned up prior to unit test
      alice.biEventManager->purgeCache( alice.biEventHandle );

      // Connect alice
      alice.biEventManager->enable( alice.biEventHandle );

      EventID evtID1;
      
      alice.biEventHelper->postUIEvent( alice.biEventHandle, SDK_SOURCE, "", "1", CPCAPI2_BIEVENTS_UIVERB_SHOW, "Main Window Title", emptyBody, evtID1 );
      
      std::this_thread::sleep_for(std::chrono::milliseconds(settings.slidingWindowDeltaMillis + 2000));

      alice.biEventManager->disable( alice.biEventHandle );
      alice.biEventManager->destroy( alice.biEventHandle );

      alice.shutdown();

      testHarness.stop();
      testHarness.waitForCompletion();
   }

   TEST_F(BIEventsModuleTest, PostCustom) {

      // listens for requests at /test
      BIEventBody emptyBody;
      BITestHarness testHarness;
      testHarness.start();
    
      TestAccount alice("alice", Account_Init);

      BIEventsSettings settings(defaultTestSettings());
      settings.authUser          = "biuser";
      settings.authPass          = "fluffybunny";
      //settings.serverLocationURL = "https://bi.bria-x.net/API/1.0";
      settings.serverLocationURL = "http://127.0.0.1:9090/API/1.0";
      settings.slidingWindowDeltaMillis = 100;
      //settings.cacheFileSize = 2048;

      // Create sessions
      alice.biEventManager->configureSettings( alice.biEventHandle, settings );

      // Make sure everything is cleaned up prior to unit test
      alice.biEventManager->purgeCache( alice.biEventHandle );

      // Connect alice
      alice.biEventManager->enable( alice.biEventHandle );

      EventID evtID1;
      EventID evtID2;
      EventID evtID3;
      EventID evtID4;
      EventID evtID5;

      // Alice waits for the sync event to know that the request has finished
      alice.biEventHelper->postUIEvent( alice.biEventHandle, SDK_SOURCE, "", "1", CPCAPI2_BIEVENTS_UIVERB_SHOW, "Main Window Title", emptyBody, evtID1 );
      alice.biEventHelper->postModelEvent( alice.biEventHandle, SDK_SOURCE, CPCAPI2_BIEVENTS_MODELID_REMOTE_SYNC, CPCAPI2_BIEVENTS_MODELVERB_CREATED, 1, emptyBody, evtID2 );
      alice.biEventHelper->postSystemEvent( alice.biEventHandle, SDK_SOURCE, CPCAPI2_BIEVENTS_SYSTEMVERB_MEMORY_LOW, emptyBody, evtID3 );
      alice.biEventHelper->postCommerceEvent( alice.biEventHandle, SDK_SOURCE, CPCAPI2_BIEVENTS_COMMERCEVERB_PURCHASED, 1, emptyBody, evtID4 );
      alice.biEventHelper->postDebugEvent( alice.biEventHandle, SDK_SOURCE, CPCAPI2_BIEVENTS_DEBUGVERB_WARNING, 1, "Oopsie", "", emptyBody, evtID5 );

      auto aliceEvents = std::async(std::launch::async, [&] () {
         BIEventsHandle h;
         OnPostSuccessEvent evt;
         ASSERT_TRUE( cpcWaitForEvent( 
            alice.biEvents,
            "BIEventsHandler::onEventSuccess",
            10000,
            AlwaysTruePred(),
            h, evt));
         ASSERT_TRUE( evt.id == evtID1 );
      });
      waitFor( aliceEvents );

      // Alice waits for the sync event to know that the request has finished
      aliceEvents = std::async(std::launch::async, [&] () {
         BIEventsHandle h;
         OnPostSuccessEvent evt;
         ASSERT_TRUE( cpcWaitForEvent( 
            alice.biEvents,
            "BIEventsHandler::onEventSuccess",
            10000,
            AlwaysTruePred(),
            h, evt));
         ASSERT_TRUE( evt.id == evtID2 );
      });
      waitFor( aliceEvents );

      // Alice waits for the sync event to know that the request has finished
      aliceEvents = std::async(std::launch::async, [&] () {
         BIEventsHandle h;
         OnPostSuccessEvent evt;
         ASSERT_TRUE( cpcWaitForEvent( 
            alice.biEvents,
            "BIEventsHandler::onEventSuccess",
            10000,
            AlwaysTruePred(),
            h, evt));
         ASSERT_TRUE( evt.id == evtID3 );
      });
      waitFor( aliceEvents );

      // Alice waits for the sync event to know that the request has finished
      aliceEvents = std::async(std::launch::async, [&] () {
         BIEventsHandle h;
         OnPostSuccessEvent evt;
         ASSERT_TRUE( cpcWaitForEvent( 
            alice.biEvents,
            "BIEventsHandler::onEventSuccess",
            10000,
            AlwaysTruePred(),
            h, evt));
         ASSERT_TRUE( evt.id == evtID4 );
      });
      waitFor( aliceEvents );

      // Alice waits for the sync event to know that the request has finished
      aliceEvents = std::async(std::launch::async, [&] () {
         BIEventsHandle h;
         OnPostSuccessEvent evt;
         ASSERT_TRUE( cpcWaitForEvent( 
            alice.biEvents,
            "BIEventsHandler::onEventSuccess",
            10000,
            AlwaysTruePred(),
            h, evt));
         ASSERT_TRUE( evt.id == evtID5 );
      });
      waitFor( aliceEvents );

      // Log out alice and bob
      alice.biEventManager->disable( alice.biEventHandle );
      alice.biEventManager->destroy( alice.biEventHandle );
      auto aliceDestroy = std::async(std::launch::async, [&] () {
         BIEventsHandle h;
         OnDestroyEvent evt;
         ASSERT_TRUE( cpcWaitForEvent( 
            alice.biEvents,
            "BIEventsHandler::onDestroy",
            10000,
            AlwaysTruePred(),
            h, evt));
      });
      waitFor( aliceDestroy );

      testHarness.stop();
      testHarness.waitForCompletion();
   }

   TEST_F(BIEventsModuleTest, RollOver) {

      // listens for requests at /test
      BIEventBody emptyBody;
      BITestHarness testHarness;
      testHarness.start();
    
      TestAccount alice("alice", Account_Init);

      BIEventsSettings settings(defaultTestSettings());
      settings.authUser          = "biuser";
      settings.authPass          = "fluffybunny";
      //settings.serverLocationURL = "https://bi.bria-x.net/API/1.0";
      settings.serverLocationURL = "http://127.0.0.1:9090/API/1.0";
      settings.slidingWindowDeltaMillis = 100;
      settings.cacheFileSize = 400000;

      // Create sessions
      alice.biEventManager->configureSettings( alice.biEventHandle, settings );

      // Make sure everything is cleaned up prior to unit test
      alice.biEventManager->purgeCache( alice.biEventHandle );

      // Keep it disabled for now
      // alice.biEventManager->enable( alice.biEventHandle );

      for( int i = 0 ; i < 30000 ; ++i )
      {
         // Alice waits for the sync event to know that the request has finished
         EventID evtID1;
         alice.biEventHelper->postUIEvent( alice.biEventHandle, SDK_SOURCE, "", "1", CPCAPI2_BIEVENTS_UIVERB_SHOW, "Main Window Title", emptyBody, evtID1 );
         //std::this_thread::sleep_for(std::chrono::milliseconds(250));
      }

      alice.biEventManager->destroy( alice.biEventHandle );
      auto aliceDestroy = std::async(std::launch::async, [&] () {
         BIEventsHandle h;
         OnDestroyEvent evt;
         ASSERT_TRUE( cpcWaitForEvent( 
            alice.biEvents,
            "BIEventsHandler::onDestroy",
            30000, // 30000 events in 30 seconds -- hopefully the SDK can process 1000 events per second?
            AlwaysTruePred(),
            h, evt));
      });
      ASSERT_EQ(aliceDestroy.wait_for(std::chrono::milliseconds(80000)), std::future_status::ready);
      waitFor( aliceDestroy );

      testHarness.stop();
      testHarness.waitForCompletion();
   }

   // tests Buffering1 and Buffering2 are meant to be run sequentially
   TEST_F(BIEventsModuleTest, Buffering1) {

      // listens for requests at /test
      BIEventBody emptyBody;
      BITestHarness testHarness;
      testHarness.start();
    
      TestAccount alice("alice", Account_Init);

      BIEventsSettings settings(defaultTestSettings());
      settings.authUser          = "biuser";
      settings.authPass          = "fluffybunny";
      //settings.serverLocationURL = "https://bi.bria-x.net/API/1.0";
      settings.serverLocationURL = "http://127.0.0.1:9090/API/1.0";
      settings.slidingWindowDeltaMillis = 100;
      //settings.cacheFileSize = 2048;

      // Create sessions
      alice.biEventManager->configureSettings( alice.biEventHandle, settings );

      // Make sure everything is cleaned up prior to unit test
      alice.biEventManager->purgeCache( alice.biEventHandle );

      // Keep it disabled for now. Buffer up a bunch of stuff

      for( int i = 0 ; i < 1000 ; ++i )
      {
         // Alice waits for the sync event to know that the request has finished
         EventID evtID1;
         alice.biEventHelper->postUIEvent( alice.biEventHandle, SDK_SOURCE, "", "1", CPCAPI2_BIEVENTS_UIVERB_SHOW, "Main Window Title", emptyBody, evtID1 );
      }

      alice.biEventManager->destroy( alice.biEventHandle );
      auto aliceDestroy = std::async(std::launch::async, [&] () {
         BIEventsHandle h;
         OnDestroyEvent evt;
         ASSERT_TRUE( cpcWaitForEvent( 
            alice.biEvents,
            "BIEventsHandler::onDestroy",
            10000,
            AlwaysTruePred(),
            h, evt));
      });
      waitFor( aliceDestroy );

      testHarness.stop();
      testHarness.waitForCompletion();
   }

   // tests Buffering1 and Buffering2 are meant to be run sequentially
   TEST_F(BIEventsModuleTest, DISABLED_Buffering2) {

      // listens for requests at /test
      BITestHarness testHarness;
      testHarness.start();
    
      TestAccount alice("alice", Account_Init);

      BIEventsSettings settings(defaultTestSettings());
      settings.authUser          = "biuser";
      settings.authPass          = "fluffybunny";
      //settings.serverLocationURL = "https://bi.bria-x.net/API/1.0";
      settings.serverLocationURL = "http://127.0.0.1:9090/API/1.0";
      settings.slidingWindowDeltaMillis = 100;
      //settings.cacheFileSize = 2048;

      // Create sessions
      alice.biEventManager->configureSettings( alice.biEventHandle, settings );

      // Enable!
      alice.biEventManager->enable( alice.biEventHandle );

      // Alice receives all previously buffered events
      for( int i = 0 ; i < 1000 ; ++i )
      {
         auto aliceEvents = std::async(std::launch::async, [&] () {
            BIEventsHandle h;
            OnPostSuccessEvent evt;
            ASSERT_TRUE( cpcWaitForEvent( 
               alice.biEvents,
               "BIEventsHandler::onEventSuccess",
               10000,
               AlwaysTruePred(),
               h, evt));
         });
         waitFor( aliceEvents );
      }

      alice.biEventManager->destroy( alice.biEventHandle );
      auto aliceDestroy = std::async(std::launch::async, [&] () {
         BIEventsHandle h;
         OnDestroyEvent evt;
         ASSERT_TRUE( cpcWaitForEvent( 
            alice.biEvents,
            "BIEventsHandler::onDestroy",
            10000,
            AlwaysTruePred(),
            h, evt));
      });
      waitFor( aliceDestroy );

      testHarness.stop();
      testHarness.waitForCompletion();
   }

   TEST_F(BIEventsModuleTest, FailedUpload) {

      // listens for requests at /test
      BIEventBody emptyBody;
      BITestSimulationFailureOptions simFailures;
      simFailures.rejectedRequests = 30; // induce 30 failures
      BITestHarness testHarness( "/API/1.0", 9090, simFailures );
      testHarness.start();
    
      TestAccount alice("alice", Account_Init);

      BIEventsSettings settings(defaultTestSettings());
      settings.authUser          = "biuser";
      settings.authPass          = "fluffybunny";
      //settings.serverLocationURL = "https://bi.bria-x.net/API/1.0";
      settings.serverLocationURL = "http://127.0.0.1:9090/API/1.0";
      settings.slidingWindowDeltaMillis = 100;
      settings.chunkSize = 10;
      //settings.cacheFileSize = 2048;

      // Create sessions
      alice.biEventManager->configureSettings( alice.biEventHandle, settings );

      // Make sure everything is cleaned up prior to unit test
      alice.biEventManager->purgeCache( alice.biEventHandle );

      // Keep it disabled for now. Buffer up a bunch of stuff.
      // Since we set the chunk size to 10 there should be 100
      // chunks of size 10 each.

      for( int i = 0 ; i < 1000 ; ++i )
      {
         // Alice waits for the sync event to know that the request has finished
         EventID evtID1;
         alice.biEventHelper->postUIEvent( alice.biEventHandle, SDK_SOURCE, "", "1", CPCAPI2_BIEVENTS_UIVERB_SHOW, "Main Window Title", emptyBody, evtID1 );
      }

      // Enable!
      alice.biEventManager->enable( alice.biEventHandle );

      // There should be 30 failures
      // NB: the failures are per POST, not per event.
      for( int i = 0 ; i < 30 ; ++i )
      {
         auto aliceEvents = std::async(std::launch::async, [&] () {
            BIEventsHandle h;
            OnErrorEvent evt;
            ASSERT_TRUE( cpcWaitForEvent( 
               alice.biEvents,
               "BIEventsHandler::onError",
               10000,
               AlwaysTruePred(),
               h, evt));
            ASSERT_EQ( evt.errorCode, -32503 );
         });
         waitFor( aliceEvents );
      }

      // Remainder of successful events
      for( int i = 0 ; i < 700 ; ++i )
      {
         auto aliceEvents = std::async(std::launch::async, [&] () {
            BIEventsHandle h;
            OnPostSuccessEvent evt;
            ASSERT_TRUE( cpcWaitForEvent( 
               alice.biEvents,
               "BIEventsHandler::onEventSuccess",
               10000,
               AlwaysTruePred(),
               h, evt));
         });
         waitFor( aliceEvents );
      }

      alice.biEventManager->destroy( alice.biEventHandle );
      auto aliceDestroy = std::async(std::launch::async, [&] () {
         BIEventsHandle h;
         OnDestroyEvent evt;
         ASSERT_TRUE( cpcWaitForEvent( 
            alice.biEvents,
            "BIEventsHandler::onDestroy",
            10000,
            AlwaysTruePred(),
            h, evt));
      });
      waitFor( aliceDestroy );

      testHarness.stop();
      testHarness.waitForCompletion();
   }

   // This unit test is here only to populate the official server with some example data.
   // Hence it is normally disabled.
   TEST_F(BIEventsModuleTest, DISABLED_SampleData) {

      // listens for requests at /test
      BIEventBody emptyBody;
      BITestHarness testHarness;
      testHarness.start();
    
      TestAccount alice("alice", Account_Init);

      BIEventsSettings settings(defaultTestSettings());
      settings.authUser          = "biuser";
      settings.authPass          = "fluffybunny";
      settings.serverLocationURL = "https://bi.bria-x.net/API/1.0";
      //settings.serverLocationURL = "http://127.0.0.1:9090/API/1.0";
      settings.slidingWindowDeltaMillis = 100;
      //settings.cacheFileSize = 2048;

      // Create sessions
      alice.biEventManager->configureSettings( alice.biEventHandle, settings );

      // Make sure everything is cleaned up prior to unit test
      alice.biEventManager->purgeCache( alice.biEventHandle );

      // Connect alice
      alice.biEventManager->enable( alice.biEventHandle );

      EventID evtID1;
      int eventCount = 0;

      // Example UI events of clicking through a wizard
      {
         cpc::string rootID( "" );
         cpc::string wizWindowID( "WizardWindowID" );
         cpc::string wizPage1ID( "Page1ID" );
         cpc::string wizPage2ID( "Page2ID" );
         cpc::string nextButtonID( "NextID" );
         cpc::string cancelButtonID( "CancelID" );

         // The int groupings are almost redundant with the parent IDs. But
         // it was felt this was needed since the UI IDs might not be in
         // integer form. This could be revised if needed.
         int mainWindowGroup = alice.biEventHelper->createEventGroupID();
         int page1Group = alice.biEventHelper->createEventGroupID();
         int page2Group = alice.biEventHelper->createEventGroupID();

         // first the main wizard window is shown
         alice.biEventHelper->postUIEvent( alice.biEventHandle, SDK_SOURCE, rootID, wizWindowID, CPCAPI2_BIEVENTS_UIVERB_SHOW, "What's New", emptyBody, evtID1, mainWindowGroup ); eventCount++;

         // Then page 1 is shown (there could be show events for buttons too, if needed)
         alice.biEventHelper->postUIEvent( alice.biEventHandle, SDK_SOURCE, wizWindowID, wizPage1ID, CPCAPI2_BIEVENTS_UIVERB_SHOW, "Feature 1", emptyBody, evtID1, page1Group, mainWindowGroup ); eventCount++;

         // Then the user clicks "Next"
         alice.biEventHelper->postUIEvent( alice.biEventHandle, SDK_SOURCE, wizPage1ID, nextButtonID, CPCAPI2_BIEVENTS_UIVERB_ACTION, "Next", emptyBody, evtID1, 0, page1Group ); eventCount++;

         // Page 1 is hidden and page 2 is shown
         alice.biEventHelper->postUIEvent( alice.biEventHandle, SDK_SOURCE, wizWindowID, wizPage1ID, CPCAPI2_BIEVENTS_UIVERB_HIDE, "Feature 1", emptyBody, evtID1, page1Group, mainWindowGroup ); eventCount++;
         alice.biEventHelper->postUIEvent( alice.biEventHandle, SDK_SOURCE, wizWindowID, wizPage2ID, CPCAPI2_BIEVENTS_UIVERB_SHOW, "Feature 2", emptyBody, evtID1, page2Group, mainWindowGroup ); eventCount++;

         // The user presses "Cancel"
         alice.biEventHelper->postUIEvent( alice.biEventHandle, SDK_SOURCE, wizPage2ID, cancelButtonID, CPCAPI2_BIEVENTS_UIVERB_ACTION, "Cancel", emptyBody, evtID1, 0, page2Group ); eventCount++;

         // Page 2 is hidden, then the wizard window is hidden
         alice.biEventHelper->postUIEvent( alice.biEventHandle, SDK_SOURCE, wizWindowID, wizPage2ID, CPCAPI2_BIEVENTS_UIVERB_HIDE, "Feature 2", emptyBody, evtID1, page2Group, mainWindowGroup ); eventCount++;
         alice.biEventHelper->postUIEvent( alice.biEventHandle, SDK_SOURCE, rootID, wizWindowID, CPCAPI2_BIEVENTS_UIVERB_HIDE, "What's New", emptyBody, evtID1, mainWindowGroup ); eventCount++;
      }

      // Example events for SDK grouping
      {
         cpc::string registrationLabel( "register" );
         cpc::string audioLabel( "audio" );
         cpc::string videoLabel( "video" );
         cpc::string callLabel( "call" );
         cpc::string metricsLabel( "metrics" );

         cpc::vector< cpc::string > tagList;
         int registerGroup = alice.biEventHelper->createEventGroupID();
         int callGroup = alice.biEventHelper->createEventGroupID();

         // This would normally be filled with name/value pairs of things related to each event. But in this
         // case it will be empty because this is just a proof of concept.
         BIEventBody eventBody;
         BIPair pair;

         // First, the account was registered
         tagList.clear();
         tagList.push_back( registrationLabel );
         eventBody.clear();
         pair.Name = "Register";
         pair.Value.ValueType = BIValueType_string;
         pair.Value.StringValue = "Account Data";
         eventBody.push_back( pair );
         alice.biEventHelper->postSDKEvent( alice.biEventHandle, SDK_SOURCE, tagList, eventBody, evtID1, registerGroup ); eventCount++;

         // Next, an audio+video call was made on the account.
         tagList.clear();
         tagList.push_back( callLabel );
         tagList.push_back( audioLabel );
         tagList.push_back( videoLabel );
         eventBody.clear();
         pair.Name = "Call Start";
         pair.Value.ValueType = BIValueType_string;
         pair.Value.StringValue = "Call Data";
         eventBody.push_back( pair );
         alice.biEventHelper->postSDKEvent( alice.biEventHandle, SDK_SOURCE, tagList, eventBody, evtID1, callGroup, registerGroup ); eventCount++;

         // During mid-call, a number of metrics regarding audio jitter are collected.
         // the eventBody would have whatever properties are needed for the data.
         tagList.clear();
         tagList.push_back( audioLabel );
         tagList.push_back( metricsLabel );
         eventBody.clear();
         pair.Name = "Audio Jitter";
         pair.Value.ValueType = BIValueType_string;
         pair.Value.StringValue = "Jitter Data";
         eventBody.push_back( pair );
         alice.biEventHelper->postSDKEvent( alice.biEventHandle, SDK_SOURCE, tagList, eventBody, evtID1, 0, callGroup ); eventCount++;

         // End of call event complete with audio/video metrics
         tagList.clear();
         tagList.push_back( callLabel );
         tagList.push_back( audioLabel );
         tagList.push_back( videoLabel );
         tagList.push_back( metricsLabel );
         eventBody.clear();
         pair.Name = "Call End";
         pair.Value.ValueType = BIValueType_string;
         pair.Value.StringValue = "Call Summary Data";
         eventBody.push_back( pair );
         alice.biEventHelper->postSDKEvent( alice.biEventHandle, SDK_SOURCE, tagList, eventBody, evtID1, callGroup, registerGroup ); eventCount++;

         // User deregisters
         tagList.clear();
         tagList.push_back( registrationLabel );
         pair.Name = "Deregister";
         pair.Value.ValueType = BIValueType_string;
         pair.Value.StringValue = "Account Data";
         eventBody.push_back( pair );
         alice.biEventHelper->postSDKEvent( alice.biEventHandle, SDK_SOURCE, tagList, eventBody, evtID1, registerGroup ); eventCount++;
      }

      // Ensure everything got delivered.
      for( int i = 0 ; i < eventCount ; ++i )
      {
         auto aliceEvents = std::async(std::launch::async, [&] () {
            BIEventsHandle h;
            OnPostSuccessEvent evt;
            ASSERT_TRUE( cpcWaitForEvent( 
               alice.biEvents,
               "BIEventsHandler::onEventSuccess",
               10000,
               AlwaysTruePred(),
               h, evt));
         });
         waitFor( aliceEvents );
      }

      // Log out alice and bob
      alice.biEventManager->disable( alice.biEventHandle );
      alice.biEventManager->destroy( alice.biEventHandle );
      auto aliceDestroy = std::async(std::launch::async, [&] () {
         BIEventsHandle h;
         OnDestroyEvent evt;
         ASSERT_TRUE( cpcWaitForEvent( 
            alice.biEvents,
            "BIEventsHandler::onDestroy",
            10000,
            AlwaysTruePred(),
            h, evt));
      });
      waitFor( aliceDestroy );

      testHarness.stop();
      testHarness.waitForCompletion();
   }

}  // namespace

#endif // (CPCAPI2_BRAND_REMOTE_SYNC_MODULE == 1)
