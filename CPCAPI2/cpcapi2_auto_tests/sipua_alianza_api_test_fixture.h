#pragma once

#ifndef SIPUA_ALIANZA_API_TEST_FIXTURE_H
#define SIPUA_ALIANZA_API_TEST_FIXTURE_H

#include "brand_branded.h"
#include <cpcapi2.h>
#include "alianza_api/interface/public/alianza_api_types.h"

#define EVENT_VALIDATOR(TYPE) std::function<void(const TYPE& evt)>

struct AlianzaAccountConfig;

struct AlianzaCallingPlanInfo
{
   std::string callingPlanProductId = "";
   std::string startDate = "";
   int planMinutes = 0;
   int secondsRemaining = 0;
};

/**
 * Container struct to maintain the sipua identity and identity related attributes such
 * as the sipua name, telephone number, extension, etc.
 * 
 * It is also used to manage some of the alianza user and device specific handles
 * extracted from the alianza api responses. It is similar to the alianza api user and
 * device concept.
 * 
 * When the sipua-info is initialized, the following attributes get generated:
 *   - generate the sip username and sip password (based on sdk account name, e.g. <name>_<random>)
 *   - generate the extension (based on the extension length, e.g. with length of 4, range would be 1000-9999)
 *   - generate the phone number (based on a hard-coded range from ************** to **************)
 *   - generate the user first-name, last-name and email (based on sdk account name, e.g. <name><extension>)
 *   - instantiate the default sipua (based on the sdk account name, e.g. <name>_<random>
*/
struct AlianzaSipUaInfo : public std::enable_shared_from_this<AlianzaSipUaInfo>
{
public:
   AlianzaSipUaInfo(const std::string& name);
   virtual~ AlianzaSipUaInfo() {}
   void init(const AlianzaAccountConfig& config, bool numberEnabled = true);
   void initIdentity(const AlianzaAccountConfig& config, bool numberEnabled = true);
   void initSipUsername();
   void initIdentity();

   std::string username;
   std::string password;
   std::string name;
   std::string firstName;
   std::string lastName;
   std::string emailAddress;
   std::string phoneNumber;
   std::string businessName;

   std::string sipUsername;
   std::string sipPassword;
   std::string sipDomain;

   std::string pushSipUsername;
   std::string pushSipPassword;
   std::string pushSipDomain;
   std::string pushServerUrl;

   std::string conversationHoldMode;
   std::string conversationPrackMode;
   std::vector<std::string> conversationDtmfModes;

   std::string autoUpdateUrl;

   std::string extension;
   std::string userId;
   std::string voicemailId;
   std::string phoneId;

   std::string userAuthToken;
   std::string clientId;

   bool userCreated;
   bool deviceCreated;
private:
   AlianzaSipUaInfo(); // Blocked
   static std::string generateExtension(uint32_t extensionLength);
   static std::string generatePhoneNumber();
   static std::string generateSipUsername(std::string& name);
   static std::string generateClientId();
};

/**
 * Container struct to maintain the alianza backend account specific context information
 * that gets populated during the instantiation. It is also used to manage some of the
 * account-specific handles extracted from the alianza api responses. It is similar to the
 * alianza api account concept, and also maintains the list of sipua instances as is
 * required for extensions.
 * 
 * When the session-info is initialized, the following attributes get generated:
 *   - generate an account-number
 *   - instantiate the default sipua (based on the sdk account name)
*/
struct AlianzaSessionInfo
{
public:
   AlianzaSessionInfo(const std::string& name, bool numberEnabled = true);
   virtual~ AlianzaSessionInfo() {}
   void init(const AlianzaAccountConfig& config);

   /**
    * Add a new sipua context to the current list, based on the account configuration provided.
    * The name is used as the seed to create the unique identity attributes, if the name is not
    * populated, the default name - based on the sdk account name - is used from the session info.
    * 
    * Primary use of this function is to support multiple sipua contexts in a single alianza account.
    * This interface would be used when multiple extensions are required. A mock sdk account is
    * required to manage the alianza account creation. Each sdk account that requires an alianza
    * extension, would call this function on the AlianzaSessionInfo instance of the mock sdk account.
   */
   std::shared_ptr<AlianzaSipUaInfo> addUa(const AlianzaAccountConfig& config, std::string name = "");

   /**
    * Returns the sipua pointer for the specified extension. Returns default sipua, if extension is empty
   */
   std::shared_ptr<AlianzaSipUaInfo> getUa(std::string extension = "");
   void getUa(AlianzaSipUaInfo& info, std::string extension = "") const;
   void getExtensions(std::vector<std::string>& extensions) const;

   /**
    * Clears the sipua list, and instantiates a new sipua based on the passed in sipua attributes.
    * Required when an extension sdk account is created, and has to update it's default sipua
    * context with the attributes from the sipua instantiated from the mock sdk account.
   */
   void resetUa(const AlianzaSipUaInfo& updatedUa);
   void resetAccountNumber();

   std::string accountId;
   std::string accountNumber;
   std::string accountGroupName;
   AlianzaCallingPlanInfo plan;
   bool numberEnabled;
   bool extensionEnabled;
private:
   AlianzaSessionInfo(); // Blocked
   static std::string generateAccountNumber();
   static std::string generateAccountGroupName();
   std::vector<std::shared_ptr<AlianzaSipUaInfo>> ua;
};

/**
 * Container struct to maintain all the environment-specific account configuration
 * imported from the alianza sipua configuration file.
*/
struct AlianzaAccountConfig
{
public:
   AlianzaAccountConfig();
   virtual~ AlianzaAccountConfig() {}
   bool importConfig();

   std::string environmentId;
   std::string platformType;
   std::string environmentType;
   std::string partitionId;
   std::string carrierId;
   std::string sipGatewayProxy;
   std::string numberLeaserUrl;
   std::string inboundRatePlanProductId;
   std::string callingPlanProductId;
   std::string accountRoutePlanId;
   std::string sipOutboundProxy;
   std::string sipDomain;
   std::string regionId;
   std::string sipTransportType;
   uint32_t extensionLength;
   CPCAPI2::test::AlianzaApiConfig api;
private:
   bool initAlianzaApiCredentials();
};

std::ostream& operator<<(std::ostream& os, const AlianzaCallingPlanInfo& info);
std::ostream& operator<<(std::ostream& os, const AlianzaSipUaInfo& info);
std::ostream& operator<<(std::ostream& os, const AlianzaSessionInfo& info);
std::ostream& operator<<(std::ostream& os, const AlianzaAccountConfig& config);

#endif // SIPUA_ALIANZA_API_TEST_FIXTURE_H

