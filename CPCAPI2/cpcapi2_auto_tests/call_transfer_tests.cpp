#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#include "cpcapi2_test_fixture.h"
#include "test_call_events.h"
#include "test_account_events.h"

using namespace CPCAPI2;
using namespace CPCAPI2::SipConversation;
using namespace CPCAPI2::test;

namespace {

class CallTransferTests : public CpcapiAutoTest
{
public:
   CallTransferTests() {}
   virtual ~CallTransferTests() {}
};

TEST_F(CallTransferTests, Redirect) {
   TestAccount alice("alice");
   TestAccount bob("bob");
   TestAccount max("max");

   auto aliceEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->addParticipant(aliceCall, bob.config.uri());
      alice.conversation->start(aliceCall);

      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertRedirectRequest(alice, aliceCall, [&](const RedirectRequestEvent& evt){
         ASSERT_EQ(max.config.uri(), evt.targetAddress);
      });
      assertNewConversationOutgoing(alice, aliceCall, max.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertCallHadMedia(alice, aliceCall, true, false);
   });
   
   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->redirect(bobCall, max.config.uri(), ""));
      assertConversationEnded(bob, bobCall, ConversationEndReason_Redirected);
   });

   auto maxEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle maxCall;
      assertNewConversationIncoming(max, &maxCall, alice.config.uri());
      assertSuccess(max.conversation->sendRingingResponse(maxCall));
      assertConversationStateChanged(max, maxCall, ConversationState_LocalRinging);
      assertSuccess(max.conversation->accept(maxCall));
      assertConversationMediaChanged(max, maxCall, MediaDirection_SendReceive);
      assertConversationStateChanged(max, maxCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      TestCallEvents::expectMediaFlowing(__LINE__, max, maxCall, true, false, NULL);

      assertSuccess(max.conversation->end(maxCall));
      assertConversationEnded(max, maxCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor3(aliceEvents, bobEvents, maxEvents);
}

TEST_F(CallTransferTests, DoubleRedirect) {
   TestAccount alice("alice");
   TestAccount bob("bob");
   TestAccount carol("carol");
   TestAccount max("max");

   auto aliceEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->addParticipant(aliceCall, bob.config.uri());
      alice.conversation->start(aliceCall);

      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertRedirectRequest(alice, aliceCall, [&](const RedirectRequestEvent& evt){
         ASSERT_EQ(carol.config.uri(), evt.targetAddress);
      });
      assertNewConversationOutgoing(alice, aliceCall, carol.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      /*
      // Apparently not used for subsequent redirects???
      assertRedirectRequest(alice, aliceCall, [&](const RedirectRequestEvent& evt){
         ASSERT_EQ(max.config.uri(), evt.targetAddress);
      });
      assertNewConversationOutgoing(alice, aliceCall, max.config.uri());
      */
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->redirect(bobCall, carol.config.uri(), ""));
      assertConversationEnded(bob, bobCall, ConversationEndReason_Redirected);
   });

   auto carolEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle carolCall;
      assertNewConversationIncoming(carol, &carolCall, alice.config.uri());
      assertSuccess(carol.conversation->sendRingingResponse(carolCall));
      assertConversationStateChanged(carol, carolCall, ConversationState_LocalRinging);
      assertSuccess(carol.conversation->redirect(carolCall, max.config.uri(), ""));
      assertConversationEnded(carol, carolCall, ConversationEndReason_Redirected);
   });

   auto maxEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle maxCall;
      assertNewConversationIncoming(max, &maxCall, alice.config.uri());
      assertSuccess(max.conversation->sendRingingResponse(maxCall));
      assertConversationStateChanged(max, maxCall, ConversationState_LocalRinging);
      assertSuccess(max.conversation->accept(maxCall));
      assertConversationMediaChanged(max, maxCall, MediaDirection_SendReceive);
      assertConversationStateChanged(max, maxCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(500));
      assertSuccess(max.conversation->end(maxCall));
      assertConversationEnded(max, maxCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor4(aliceEvents, bobEvents, carolEvents, maxEvents);
}

TEST_F(CallTransferTests, RedirectThenHoldResume) {
   TestAccount alice("alice");
   TestAccount bob("bob");
   TestAccount max("max");

   auto aliceEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->addParticipant(aliceCall, bob.config.uri());
      alice.conversation->start(aliceCall);

      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertRedirectRequest(alice, aliceCall, [&](const RedirectRequestEvent& evt){
         ASSERT_EQ(max.config.uri(), evt.targetAddress);
      });
      assertNewConversationOutgoing(alice, aliceCall, max.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(500));

      assertSuccess(alice.conversation->hold(aliceCall));
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_TRUE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendOnly, evt.localMediaInfo[0].mediaDirection);
      });
      assertSuccess(alice.conversation->unhold(aliceCall));
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });

      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });
   
   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      assertSuccess(bob.conversation->redirect(bobCall, max.config.uri(), ""));
      assertConversationEnded(bob, bobCall, ConversationEndReason_Redirected);
   });

   auto maxEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle maxCall;
      assertNewConversationIncoming(max, &maxCall, alice.config.uri());
      assertSuccess(max.conversation->sendRingingResponse(maxCall));
      assertConversationStateChanged(max, maxCall, ConversationState_LocalRinging);
      assertSuccess(max.conversation->accept(maxCall));
      assertConversationMediaChanged(max, maxCall, MediaDirection_SendReceive);
      assertConversationStateChanged(max, maxCall, ConversationState_Connected);

      assertConversationMediaChangeRequest(max, maxCall, MediaDirection_SendOnly);
      assertSuccess(max.conversation->accept(maxCall));
      assertConversationMediaChanged_ex(max, maxCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_FALSE(evt.localHold);
         ASSERT_TRUE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_ReceiveOnly, evt.localMediaInfo[0].mediaDirection);
      });
      assertConversationMediaChangeRequest(max, maxCall, MediaDirection_SendReceive);
      assertSuccess(max.conversation->accept(maxCall));
      assertConversationMediaChanged_ex(max, maxCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });

      assertConversationEnded(max, maxCall, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor3(aliceEvents, bobEvents, maxEvents);
}

TEST_F(CallTransferTests, RedirectTo404) {
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.reRegisterOnResponseTypes.clear();
   alice.enable();

   TestAccount bob("bob");

   auto aliceEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->addParticipant(aliceCall, bob.config.uri());
      alice.conversation->start(aliceCall);

      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertRedirectRequest(alice, aliceCall, [&](const RedirectRequestEvent& evt){
         cpc::string redirectTarget = "sip:invalidinvalidinvalid23432@" + bob.config.settings.domain;
         ASSERT_EQ(redirectTarget, evt.targetAddress);
      });
      {
         SipConversationHandle h;
         ConversationEndedEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent("SipConversationHandler::onConversationEnded",
            45000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
         ASSERT_EQ(ConversationEndReason_ServerRejected, evt.endReason);
      }
   });
   
   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      cpc::string redirectTarget = "sip:invalidinvalidinvalid23432@" + bob.config.settings.domain;
      assertSuccess(bob.conversation->redirect(bobCall, redirectTarget, ""));
      assertConversationEnded(bob, bobCall, ConversationEndReason_Redirected);
   });

   waitFor2(aliceEvents, bobEvents);
}

TEST_F(CallTransferTests, RedirectNoRingingTo404) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   auto aliceEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->addParticipant(aliceCall, bob.config.uri());
      alice.conversation->start(aliceCall);

      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertRedirectRequest(alice, aliceCall, [&](const RedirectRequestEvent& evt){
         cpc::string redirectTarget = "sip:invalidinvalidinvalid23432@" + bob.config.settings.domain;
         ASSERT_EQ(redirectTarget, evt.targetAddress);
      });
      assertConversationEnded(alice, aliceCall, ConversationEndReason_ServerRejected);
   });
   
   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      cpc::string redirectTarget = "sip:invalidinvalidinvalid23432@" + bob.config.settings.domain;
      assertSuccess(bob.conversation->redirect(bobCall, redirectTarget, ""));
      assertConversationEnded(bob, bobCall, ConversationEndReason_Redirected);
   });

   waitFor2(aliceEvents, bobEvents);
}

TEST_F(CallTransferTests, BasicTransferBestEffortEncryption) {
   TestAccount alice("alice");
   TestAccount bob("bob");
   TestAccount max("max");

   auto aliceEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->addParticipant(aliceCall, bob.config.uri());
      alice.conversation->setBestEffortMediaEncryption(aliceCall, true);
      alice.conversation->start(aliceCall);

      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      SipConversationHandle aliceCallToMax;
      assertTransferRequest_ex(alice, aliceCall, &aliceCallToMax, [&](const TransferRequestEvent& evt){
         ASSERT_EQ(max.config.uri(), evt.transferTargetAddress);
         ASSERT_EQ("", evt.transferTargetDisplayName);
         ASSERT_NE(0, evt.transferTargetConversation);
      });

      assertSuccess(alice.conversation->acceptIncomingTransferRequest(aliceCallToMax));

      assertNewConversationOutgoing(alice, aliceCallToMax, max.config.uri());
      assertConversationStateChanged(alice, aliceCallToMax, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCallToMax, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCallToMax, ConversationState_Connected);

      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertConversationEnded(alice, aliceCallToMax, ConversationEndReason_UserTerminatedRemotely);
   });


   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      bob.conversation->setBestEffortMediaEncryption(bobCall, true);
      assertSuccess(bob.conversation->accept(bobCall));
         
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      assertSuccess(bob.conversation->transfer(bobCall, max.config.uri()));

      assertTransferTryingRingingConnected(bob, bobCall);

      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });


   auto maxEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle maxCall;
      assertNewConversationIncoming(max, &maxCall, alice.config.uri());
      assertSuccess(max.conversation->sendRingingResponse(maxCall));
      assertConversationStateChanged(max, maxCall, ConversationState_LocalRinging);
      max.conversation->setBestEffortMediaEncryption(maxCall, true);
      assertSuccess(max.conversation->accept(maxCall));
         
      assertConversationMediaChanged(max, maxCall, MediaDirection_SendReceive);
      assertConversationStateChanged(max, maxCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(500));

      assertSuccess(max.conversation->end(maxCall));
      assertConversationEnded(max, maxCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor3(aliceEvents, bobEvents, maxEvents);
}

TEST_F(CallTransferTests, AttendedTransferCrossAccount)
{
   TestAccount alice("alice");
   TestAccount bob("bob");
   TestAccount max("max");
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   CPCAPI2::SipAccount::SipAccountHandle bob2 = bob.account->create();
   bob.account->setHandler(bob2, (CPCAPI2::SipAccount::SipAccountHandler*)0xDEADBEEF);
   bob.conversation->setHandler(bob2, (CPCAPI2::SipConversation::SipConversationHandler*)0xDEADBEEF);
   CPCAPI2::SipAccount::SipAccountSettings bob2Settings = bob.config.settings;
   bob.account->configureDefaultAccountSettings(bob2, bob2Settings);
   bob.account->applySettings(bob2);
   bob.account->enable(bob2);
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
      ASSERT_TRUE(bob.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(bob2, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);
   }
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
      ASSERT_TRUE(bob.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(bob2, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   SipConversationHandle aliceCallToBob;
   SipConversationHandle aliceCallToMax;
   SipConversationHandle bobCallFromAlice1;
   SipConversationHandle bobCallFromAlice2;
   SipConversationHandle bobCallToMax;
   SipConversationHandle maxCallFromBob;
   SipConversationHandle maxCallFromAlice;

   aliceCallToBob = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCallToBob, bob.config.uri());
   alice.conversation->start(aliceCallToBob);

   auto aliceOutgoinCallEvents = std::async(std::launch::async, [&]()
   {
      assertNewConversationOutgoing(alice, aliceCallToBob, bob.config.uri());
      assertConversationStateChanged(alice, aliceCallToBob, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCallToBob, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCallToBob, ConversationState_Connected);
   });

   auto bobIncomingCallEvents = std::async(std::launch::async, [&]()
   {
      // Wait for both INVITEs
      NewConversationEvent evt1;
      SipConversationHandle fromAlice1;
      ASSERT_TRUE(bob.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onNewConversation", 30000, AlwaysTruePred(), fromAlice1, evt1)) << "missed inbound call event";
      ASSERT_EQ(ConversationType_Incoming, evt1.conversationType) << "wrong conversation type, expecting conversation type Incoming";
      ASSERT_EQ(alice.config.uri(), evt1.remoteAddress) << "remote address is incorrect";
      ASSERT_FALSE(evt1.isCodecsMismatched) << "Codec Mismatched";

      NewConversationEvent evt2;
      SipConversationHandle fromAlice2;
      ASSERT_TRUE(bob.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onNewConversation", 30000, AlwaysTruePred(), fromAlice2, evt2)) << "missed inbound call event";
      ASSERT_EQ(ConversationType_Incoming, evt2.conversationType) << "wrong conversation type, expecting conversation type Incoming";
      ASSERT_EQ(alice.config.uri(), evt2.remoteAddress) << "remote address is incorrect";
      ASSERT_FALSE(evt2.isCodecsMismatched) << "Codec Mismatched";

      if (evt1.account == bob.handle)
      {
         ASSERT_EQ(evt2.account, bob2);
         bobCallFromAlice1 = fromAlice1;
         bobCallFromAlice2 = fromAlice2;
      }
      else
      {
         ASSERT_EQ(evt1.account, bob2);
         ASSERT_EQ(evt2.account, bob.handle);
         bobCallFromAlice1 = fromAlice2;
         bobCallFromAlice2 = fromAlice1;
      }

      // Complete the call from the original registration (bob), will result in two different conversation managers when the transfer is triggered using the alternate registration (bob2)
      assertSuccess(bob.conversation->sendRingingResponse(bobCallFromAlice1));
      assertConversationStateChanged(bob, bobCallFromAlice1, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCallFromAlice1));
      assertConversationMediaChanged(bob, bobCallFromAlice1, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCallFromAlice1, ConversationState_Connected);
   });

   waitFor2(aliceOutgoinCallEvents, bobIncomingCallEvents);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   auto aliceRemoteHoldEvents = std::async(std::launch::async, [&]()
   {
      assertConversationMediaChangeRequest(alice, aliceCallToBob, MediaDirection_SendOnly);
      assertSuccess(alice.conversation->accept(aliceCallToBob));
      assertConversationMediaChanged(alice, aliceCallToBob, MediaDirection_ReceiveOnly);
   });

   auto bobLocalHoldEvents = std::async(std::launch::async, [&]()
   {
      assertSuccess(bob.conversation->hold(bobCallFromAlice1));
      assertConversationMediaChanged(bob, bobCallFromAlice1, MediaDirection_SendOnly);
   });

   waitFor2(aliceRemoteHoldEvents, bobLocalHoldEvents);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   auto bobOutgoingCallEvents = std::async(std::launch::async, [&]()
   {
      bobCallToMax = bob.conversation->createConversation(bob2);
      bob.conversation->addParticipant(bobCallToMax, max.config.uri());
      bob.conversation->start(bobCallToMax);

      assertNewConversationOutgoing(bob, bobCallToMax, max.config.uri());
      assertConversationStateChanged(bob, bobCallToMax, ConversationState_RemoteRinging);
      assertConversationMediaChanged(bob, bobCallToMax, MediaDirection_SendReceive);
      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(bob.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onConversationStateChanged", 15000, HandleEqualsPred<SipConversationHandle>(bobCallToMax), h, evt)) << "missed conversation changed event";
         if (evt.conversationState == ConversationState_RemoteRinging)
         {
            // Seen some test-runs with a second remote ringing notification before the connected event is received
            assertConversationStateChanged(bob, bobCallToMax, ConversationState_Connected);
         }
         else
         {
            ASSERT_EQ(ConversationState_Connected, evt.conversationState);
         }
      }
   });

   auto maxIncomingCallEvents = std::async(std::launch::async, [&]()
   {
      assertNewConversationIncoming(max, &maxCallFromBob, bob.config.uri());
      assertSuccess(max.conversation->sendRingingResponse(maxCallFromBob));
      assertConversationStateChanged(max, maxCallFromBob, ConversationState_LocalRinging);
      assertSuccess(max.conversation->accept(maxCallFromBob));
      assertConversationMediaChanged(max, maxCallFromBob, MediaDirection_SendReceive);
      assertConversationStateChanged(max, maxCallFromBob, ConversationState_Connected);
   });

   waitFor2(bobOutgoingCallEvents, maxIncomingCallEvents);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   auto aliceIncomingTransferRequestEvents = std::async(std::launch::async, [&]()
   {
      assertTransferRequest_ex(alice, aliceCallToBob, &aliceCallToMax, [&](const TransferRequestEvent& evt)
      {
         ASSERT_EQ(max.config.uri(), evt.transferTargetAddress);
         ASSERT_EQ(max.config.name, evt.transferTargetDisplayName);
         ASSERT_NE(0, evt.transferTargetConversation);
      });
      assertSuccess(alice.conversation->acceptIncomingTransferRequest(aliceCallToMax));

      assertNewConversationOutgoing(alice, aliceCallToMax, max.config.uri());
      assertConversationStateChanged(alice, aliceCallToMax, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCallToMax, MediaDirection_SendReceive);
      {
         // Seen some test-runs with multiple remote ringing notifications before the connected event is received
         int tries = 3;
         while (tries > 0)
         {
            SipConversationHandle h;
            ConversationStateChangedEvent evt;
            ASSERT_TRUE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onConversationStateChanged", 15000, HandleEqualsPred<SipConversationHandle>(aliceCallToMax), h, evt)) << "missed conversation changed event";
            if (evt.conversationState == ConversationState_RemoteRinging)
            {
               tries--;
               if (tries == 0)
               {
                  ASSERT_EQ(ConversationState_Connected, evt.conversationState); // Trigger failure
               }
            }
            else
            {
               ASSERT_EQ(ConversationState_Connected, evt.conversationState);
               break;
            }
         }
      }
   });

   auto bobOutgoingTransferRequestEvents = std::async(std::launch::async, [&]()
   {
      assertSuccess(bob.conversation->transfer(bobCallToMax, bobCallFromAlice1));
      assertTransferTryingRingingConnected(bob, bobCallFromAlice1);
   });

   auto maxIncomingCallTransferEvents = std::async(std::launch::async, [&]()
   {
      assertNewConversationIncomingTransfer_ex(max, &maxCallFromAlice, alice.config.uri(), [&](const NewConversationEvent& evt)
      {
         ASSERT_EQ(alice.config.name, evt.remoteDisplayName);
         ASSERT_EQ(maxCallFromBob, evt.conversationToReplace);
      });

      assertSuccess(max.conversation->sendRingingResponse(maxCallFromAlice));
      assertConversationStateChanged(max, maxCallFromAlice, ConversationState_LocalRinging);
      assertSuccess(max.conversation->accept(maxCallFromAlice));
      assertConversationMediaChanged(max, maxCallFromAlice, MediaDirection_SendReceive);
      assertConversationStateChanged(max, maxCallFromAlice, ConversationState_Connected);
   });

   waitFor3(aliceIncomingTransferRequestEvents, bobOutgoingTransferRequestEvents, maxIncomingCallTransferEvents);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   auto aliceCallTerminationEvents = std::async(std::launch::async, [&]()
   {
      assertConversationEnded(alice, aliceCallToMax, ConversationEndReason_UserTerminatedRemotely);
      assertConversationEnded(alice, aliceCallToBob, ConversationEndReason_UserTerminatedRemotely);
   });

   auto bobCallTerminationEvents = std::async(std::launch::async, [&]()
   {
      assertSuccess(bob.conversation->end(bobCallFromAlice2));
      assertConversationEnded(bob, bobCallFromAlice1, ConversationEndReason_UserTerminatedLocally);
      assertConversationEnded(bob, bobCallToMax, ConversationEndReason_UserTerminatedRemotely);
   });

   auto maxCallTerminationEvents = std::async(std::launch::async, [&]()
   {
      assertSuccess(max.conversation->end(maxCallFromAlice));
      assertConversationEnded(max, maxCallFromAlice, ConversationEndReason_UserTerminatedLocally);
      assertConversationEnded(max, maxCallFromBob, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor3(aliceCallTerminationEvents, bobCallTerminationEvents, maxCallTerminationEvents);
}

TEST_F(CallTransferTests, AttendedTransferDualAccount)
{
   TestAccount alice("alice");
   TestAccount bob("bob");
   TestAccount max("max");
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   CPCAPI2::SipAccount::SipAccountHandle bob2 = bob.account->create();
   bob.account->setHandler(bob2, (CPCAPI2::SipAccount::SipAccountHandler*)0xDEADBEEF);
   bob.conversation->setHandler(bob2, (CPCAPI2::SipConversation::SipConversationHandler*)0xDEADBEEF);
   CPCAPI2::SipAccount::SipAccountSettings bob2Settings = bob.config.settings;
   bob.account->configureDefaultAccountSettings(bob2, bob2Settings);
   bob.account->applySettings(bob2);
   bob.account->enable(bob2);
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
      ASSERT_TRUE(bob.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(bob2, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);
   }
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
      ASSERT_TRUE(bob.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(bob2, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   SipConversationHandle aliceCallToBob;
   SipConversationHandle aliceCallToMax;
   SipConversationHandle bobCallFromAlice1;
   SipConversationHandle bobCallFromAlice2;
   SipConversationHandle bobCallToMax;
   SipConversationHandle maxCallFromBob;
   SipConversationHandle maxCallFromAlice;

   aliceCallToBob = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCallToBob, bob.config.uri());
   alice.conversation->start(aliceCallToBob);

   auto aliceOutgoinCallEvents = std::async(std::launch::async, [&]()
   {
      assertNewConversationOutgoing(alice, aliceCallToBob, bob.config.uri());
      assertConversationStateChanged(alice, aliceCallToBob, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCallToBob, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCallToBob, ConversationState_Connected);
   });

   auto bobIncomingCallEvents = std::async(std::launch::async, [&]()
   {
      // Wait for both INVITEs
      NewConversationEvent evt1;
      SipConversationHandle fromAlice1;
      ASSERT_TRUE(bob.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onNewConversation", 30000, AlwaysTruePred(), fromAlice1, evt1)) << "missed inbound call event";
      ASSERT_EQ(ConversationType_Incoming, evt1.conversationType) << "wrong conversation type, expecting conversation type Incoming";
      ASSERT_EQ(alice.config.uri(), evt1.remoteAddress) << "remote address is incorrect";
      ASSERT_FALSE(evt1.isCodecsMismatched) << "Codec Mismatched";

      NewConversationEvent evt2;
      SipConversationHandle fromAlice2;
      ASSERT_TRUE(bob.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onNewConversation", 30000, AlwaysTruePred(), fromAlice2, evt2)) << "missed inbound call event";
      ASSERT_EQ(ConversationType_Incoming, evt2.conversationType) << "wrong conversation type, expecting conversation type Incoming";
      ASSERT_EQ(alice.config.uri(), evt2.remoteAddress) << "remote address is incorrect";
      ASSERT_FALSE(evt2.isCodecsMismatched) << "Codec Mismatched";

      if (evt1.account == bob.handle)
      {
         ASSERT_EQ(evt2.account, bob2);
         bobCallFromAlice1 = fromAlice1;
         bobCallFromAlice2 = fromAlice2;
      }
      else
      {
         ASSERT_EQ(evt1.account, bob2);
         ASSERT_EQ(evt2.account, bob.handle);
         bobCallFromAlice1 = fromAlice2;
         bobCallFromAlice2 = fromAlice1;
      }

      // Complete the call from the alternate registration (bob2), will result in the same conversation manager during transfer
      assertSuccess(bob.conversation->sendRingingResponse(bobCallFromAlice2));
      assertConversationStateChanged(bob, bobCallFromAlice2, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCallFromAlice2));
      assertConversationMediaChanged(bob, bobCallFromAlice2, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCallFromAlice2, ConversationState_Connected);
   });

   waitFor2(aliceOutgoinCallEvents, bobIncomingCallEvents);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   auto aliceRemoteHoldEvents = std::async(std::launch::async, [&]()
   {
      assertConversationMediaChangeRequest(alice, aliceCallToBob, MediaDirection_SendOnly);
      assertSuccess(alice.conversation->accept(aliceCallToBob));
      assertConversationMediaChanged(alice, aliceCallToBob, MediaDirection_ReceiveOnly);
   });

   auto bobLocalHoldEvents = std::async(std::launch::async, [&]()
   {
      assertSuccess(bob.conversation->hold(bobCallFromAlice2));
      assertConversationMediaChanged(bob, bobCallFromAlice2, MediaDirection_SendOnly);
   });

   waitFor2(aliceRemoteHoldEvents, bobLocalHoldEvents);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   auto bobOutgoingCallEvents = std::async(std::launch::async, [&]()
   {
      bobCallToMax = bob.conversation->createConversation(bob2);
      bob.conversation->addParticipant(bobCallToMax, max.config.uri());
      bob.conversation->start(bobCallToMax);

      assertNewConversationOutgoing(bob, bobCallToMax, max.config.uri());
      assertConversationStateChanged(bob, bobCallToMax, ConversationState_RemoteRinging);
      assertConversationMediaChanged(bob, bobCallToMax, MediaDirection_SendReceive);
      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(bob.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onConversationStateChanged", 15000, HandleEqualsPred<SipConversationHandle>(bobCallToMax), h, evt)) << "missed conversation changed event";
         if (evt.conversationState == ConversationState_RemoteRinging)
         {
            // Seen some test-runs with a second remote ringing notification before the connected event is received
            assertConversationStateChanged(bob, bobCallToMax, ConversationState_Connected);
         }
         else
         {
            ASSERT_EQ(ConversationState_Connected, evt.conversationState);
         }
      }
   });

   auto maxIncomingCallEvents = std::async(std::launch::async, [&]()
   {
      assertNewConversationIncoming(max, &maxCallFromBob, bob.config.uri());
      assertSuccess(max.conversation->sendRingingResponse(maxCallFromBob));
      assertConversationStateChanged(max, maxCallFromBob, ConversationState_LocalRinging);
      assertSuccess(max.conversation->accept(maxCallFromBob));
      assertConversationMediaChanged(max, maxCallFromBob, MediaDirection_SendReceive);
      assertConversationStateChanged(max, maxCallFromBob, ConversationState_Connected);
   });

   waitFor2(bobOutgoingCallEvents, maxIncomingCallEvents);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   auto aliceIncomingTransferRequestEvents = std::async(std::launch::async, [&]()
   {
      assertTransferRequest_ex(alice, aliceCallToBob, &aliceCallToMax, [&](const TransferRequestEvent& evt)
      {
         ASSERT_EQ(max.config.uri(), evt.transferTargetAddress);
         ASSERT_EQ(max.config.name, evt.transferTargetDisplayName);
         ASSERT_NE(0, evt.transferTargetConversation);
      });
      assertSuccess(alice.conversation->acceptIncomingTransferRequest(aliceCallToMax));

      assertNewConversationOutgoing(alice, aliceCallToMax, max.config.uri());
      assertConversationStateChanged(alice, aliceCallToMax, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCallToMax, MediaDirection_SendReceive);
      {
         // Seen some test-runs with multiple remote ringing notifications before the connected event is received
         int tries = 3;
         while (tries > 0)
         {
            SipConversationHandle h;
            ConversationStateChangedEvent evt;
            ASSERT_TRUE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onConversationStateChanged", 15000, HandleEqualsPred<SipConversationHandle>(aliceCallToMax), h, evt)) << "missed conversation changed event";
            if (evt.conversationState == ConversationState_RemoteRinging)
            {
               tries--;
               if (tries == 0)
               {
                  ASSERT_EQ(ConversationState_Connected, evt.conversationState); // Trigger failure
               }
            }
            else
            {
               ASSERT_EQ(ConversationState_Connected, evt.conversationState);
               break;
            }
         }
      }
   });

   auto bobOutgoingTransferRequestEvents = std::async(std::launch::async, [&]()
   {
      assertSuccess(bob.conversation->transfer(bobCallToMax, bobCallFromAlice2));
      assertTransferTryingRingingConnected(bob, bobCallFromAlice2);
   });

   auto maxIncomingCallTransferEvents = std::async(std::launch::async, [&]()
   {
      assertNewConversationIncomingTransfer_ex(max, &maxCallFromAlice, alice.config.uri(), [&](const NewConversationEvent& evt)
      {
         ASSERT_EQ(alice.config.name, evt.remoteDisplayName);
         ASSERT_EQ(maxCallFromBob, evt.conversationToReplace);
      });

      assertSuccess(max.conversation->sendRingingResponse(maxCallFromAlice));
      assertConversationStateChanged(max, maxCallFromAlice, ConversationState_LocalRinging);
      assertSuccess(max.conversation->accept(maxCallFromAlice));
      assertConversationMediaChanged(max, maxCallFromAlice, MediaDirection_SendReceive);
      assertConversationStateChanged(max, maxCallFromAlice, ConversationState_Connected);
   });

   waitFor3(aliceIncomingTransferRequestEvents, bobOutgoingTransferRequestEvents, maxIncomingCallTransferEvents);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   auto aliceCallTerminationEvents = std::async(std::launch::async, [&]()
   {
      assertConversationEnded(alice, aliceCallToMax, ConversationEndReason_UserTerminatedRemotely);
      assertConversationEnded(alice, aliceCallToBob, ConversationEndReason_UserTerminatedRemotely);
   });

   auto bobCallTerminationEvents = std::async(std::launch::async, [&]()
   {
      assertSuccess(bob.conversation->end(bobCallFromAlice2));
      assertConversationEnded(bob, bobCallFromAlice2, ConversationEndReason_UserTerminatedLocally);
      assertConversationEnded(bob, bobCallToMax, ConversationEndReason_UserTerminatedRemotely);
   });

   auto maxCallTerminationEvents = std::async(std::launch::async, [&]()
   {
      assertSuccess(max.conversation->end(maxCallFromAlice));
      assertConversationEnded(max, maxCallFromAlice, ConversationEndReason_UserTerminatedLocally);
      assertConversationEnded(max, maxCallFromBob, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor3(aliceCallTerminationEvents, bobCallTerminationEvents, maxCallTerminationEvents);
}

TEST_F(CallTransferTests, BasicTransferToInvalidAddress) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   cpc::string transferTargetAddress = "sip:INTENTIONALLY_INVALID_ADDRESS@" + alice.config.settings.domain;

   auto aliceEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      SipConversationHandle aliceCallToInvalid;
      assertTransferRequest_ex(alice, aliceCall, &aliceCallToInvalid, [&](const TransferRequestEvent& evt){
         ASSERT_EQ(transferTargetAddress, evt.transferTargetAddress);
         ASSERT_EQ("", evt.transferTargetDisplayName);
         ASSERT_NE(0, evt.transferTargetConversation);
      });

      assertSuccess(alice.conversation->acceptIncomingTransferRequest(aliceCallToInvalid));
      assertNewConversationOutgoing(alice, aliceCallToInvalid, transferTargetAddress);// RKK added line... should this be happening?
      assertConversationEnded(alice, aliceCallToInvalid, ConversationEndReason_ServerRejected);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
         
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      assertSuccess(bob.conversation->transfer(bobCall, transferTargetAddress));
      assertTransferProgress(bob, bobCall, TransferProgressEventType_Trying);
      assertTransferProgress(bob, bobCall, TransferProgressEventType_Failed);
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor2(aliceEvents, bobEvents);
}

TEST_F(CallTransferTests, BasicTransferToInvalidAddressOtherDirection) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   cpc::string transferTargetAddress = "sip:INTENTIONALLY_INVALID_ADDRESS@" + alice.config.settings.domain;

   auto aliceEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(2000));

      assertSuccess(alice.conversation->transfer(aliceCall, transferTargetAddress));
      assertTransferProgress(alice, aliceCall, TransferProgressEventType_Trying);
      assertTransferProgress(alice, aliceCall, TransferProgressEventType_Failed);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCallFromAlice;
      assertNewConversationIncoming(bob, &bobCallFromAlice, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCallFromAlice));
      assertConversationStateChanged(bob, bobCallFromAlice, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCallFromAlice));

      assertConversationMediaChanged(bob, bobCallFromAlice, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCallFromAlice, ConversationState_Connected);

      SipConversationHandle bobCallToInvalid;
      assertTransferRequest_ex(bob, bobCallFromAlice, &bobCallToInvalid, [&](const TransferRequestEvent& evt) {
         ASSERT_EQ(transferTargetAddress, evt.transferTargetAddress);
         ASSERT_EQ("", evt.transferTargetDisplayName);
         ASSERT_NE(0, evt.transferTargetConversation);
      });

      assertSuccess(bob.conversation->acceptIncomingTransferRequest(bobCallToInvalid));
      assertNewConversationOutgoing(bob, bobCallToInvalid, transferTargetAddress); // RKK added line... should this be happening?
      assertConversationEnded(bob, bobCallToInvalid, ConversationEndReason_ServerRejected);
      assertSuccess(bob.conversation->end(bobCallFromAlice));
      assertConversationEnded(bob, bobCallFromAlice, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor2(aliceEvents, bobEvents);
}

// IMPALA-2432: A creates call with B, A puts B on hold, A calls C, C redirects to D, D answers, A transfers C/D call to A
TEST_F(CallTransferTests, DISABLED_AttendedTransferToRedirect) {
   TestAccount alice("alice");
   TestAccount bob("bob");
   TestAccount carol("carol");
   TestAccount max("max");

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCallToBob = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCallToBob, bob.config.uri());
   alice.conversation->start(aliceCallToBob);

   auto aliceEvents = std::async(std::launch::async, [&]() {
      assertNewConversationOutgoing(alice, aliceCallToBob, bob.config.uri());
      assertConversationStateChanged(alice, aliceCallToBob, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCallToBob, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCallToBob, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      assertSuccess(alice.conversation->hold(aliceCallToBob));
      assertConversationMediaChanged(alice, aliceCallToBob, MediaDirection_SendOnly);

      SipConversationHandle aliceCallToCarol_Max = alice.conversation->createConversation(alice.handle);
      alice.conversation->addParticipant(aliceCallToCarol_Max, carol.config.uri());
      alice.conversation->start(aliceCallToCarol_Max);

      assertNewConversationOutgoing(alice, aliceCallToCarol_Max, carol.config.uri());
      assertConversationStateChanged(alice, aliceCallToCarol_Max, ConversationState_RemoteRinging);
      assertRedirectRequest(alice, aliceCallToCarol_Max, [&](const RedirectRequestEvent& evt){
         ASSERT_EQ(max.config.uri(), evt.targetAddress);
      });
      assertNewConversationOutgoing(alice, aliceCallToCarol_Max, max.config.uri());
      assertConversationStateChanged(alice, aliceCallToCarol_Max, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCallToCarol_Max, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCallToCarol_Max, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      assertSuccess(alice.conversation->transfer(aliceCallToCarol_Max, aliceCallToBob));
      assertTransferTryingRingingConnected(alice, aliceCallToBob);
      assertConversationEnded(alice, aliceCallToCarol_Max, ConversationEndReason_UserTerminatedRemotely);
      assertSuccess(alice.conversation->end(aliceCallToBob));
      assertConversationEnded(alice, aliceCallToBob, ConversationEndReason_UserTerminatedLocally);
   });


   auto bobEvents = std::async(std::launch::async, [&]() {
      SipConversationHandle bobCallFromAlice;
      assertNewConversationIncoming(bob, &bobCallFromAlice, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCallFromAlice));
      assertConversationStateChanged(bob, bobCallFromAlice, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCallFromAlice));
      assertConversationMediaChanged(bob, bobCallFromAlice, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCallFromAlice, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      assertConversationMediaChangeRequest(bob, bobCallFromAlice, MediaDirection_SendOnly);
      assertSuccess(bob.conversation->accept(bobCallFromAlice));
      assertConversationMediaChanged(bob, bobCallFromAlice, MediaDirection_ReceiveOnly);
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      SipConversationHandle bobCallToCarol_Max;
      assertTransferRequest_ex(bob, bobCallFromAlice, &bobCallToCarol_Max, [&](const TransferRequestEvent& evt){
         ASSERT_EQ(max.config.uri(), evt.transferTargetAddress);
         ASSERT_NE(0, evt.transferTargetConversation);
      });
      assertSuccess(bob.conversation->acceptIncomingTransferRequest(bobCallToCarol_Max));

      assertNewConversationOutgoing(bob, bobCallToCarol_Max, max.config.uri());
      assertConversationStateChanged(bob, bobCallToCarol_Max, ConversationState_RemoteRinging);
      assertConversationMediaChanged(bob, bobCallToCarol_Max, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCallToCarol_Max, ConversationState_Connected);
      assertConversationEnded(bob, bobCallFromAlice, ConversationEndReason_UserTerminatedRemotely);
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      assertConversationEnded(bob, bobCallToCarol_Max, ConversationEndReason_UserTerminatedRemotely);
   });

   auto carolEvents = std::async(std::launch::async, [&]() {
      SipConversationHandle carolCall;
      assertNewConversationIncoming(carol, &carolCall, alice.config.uri());
      assertSuccess(carol.conversation->sendRingingResponse(carolCall));
      assertConversationStateChanged(carol, carolCall, ConversationState_LocalRinging);
      assertSuccess(carol.conversation->redirect(carolCall, max.config.uri(), ""));
      assertConversationEnded(carol, carolCall, ConversationEndReason_Redirected);
   });

   auto maxEvents = std::async(std::launch::async, [&]() {

      SipConversationHandle maxCallFromAlice;
      assertNewConversationIncoming(max, &maxCallFromAlice, alice.config.uri());
      assertSuccess(max.conversation->sendRingingResponse(maxCallFromAlice));
      assertConversationStateChanged(max, maxCallFromAlice, ConversationState_LocalRinging);
      assertSuccess(max.conversation->accept(maxCallFromAlice));
      assertConversationMediaChanged(max, maxCallFromAlice, MediaDirection_SendReceive);
      assertConversationStateChanged(max, maxCallFromAlice, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      SipConversationHandle maxCallFromBob;
      assertNewConversationIncomingTransfer_ex(max, &maxCallFromBob, bob.config.uri(), [&](const NewConversationEvent& evt){
         ASSERT_EQ(bob.config.name, evt.remoteDisplayName);
         ASSERT_EQ(maxCallFromAlice, evt.conversationToReplace);
      });

      assertSuccess(max.conversation->sendRingingResponse(maxCallFromBob));
      assertConversationStateChanged(max, maxCallFromBob, ConversationState_LocalRinging);
      assertSuccess(max.conversation->accept(maxCallFromBob));
      assertConversationMediaChanged(max, maxCallFromBob, MediaDirection_SendReceive);
      assertConversationStateChanged(max, maxCallFromBob, ConversationState_Connected);
      assertConversationEnded(max, maxCallFromAlice, ConversationEndReason_UserTerminatedLocally);
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      assertSuccess(max.conversation->end(maxCallFromBob));
      assertConversationEnded(max, maxCallFromBob, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor4(aliceEvents, bobEvents, carolEvents, maxEvents);
}

// Created to test scenario from "OBELISK-3259 - Transfered call terminated after couple of seconds"
TEST_F(CallTransferTests, BasicTransferWithPrack) {
   TestAccount alice("alice", Account_Init);
   TestAccount bob("bob", Account_Init);
   TestAccount max("max", Account_Init);
   CPCAPI2::SipConversationSettings supportedConvSettings;
   CPCAPI2::SipConversationSettings requiredConvSettings;
   supportedConvSettings.prackMode = PrackMode_Supported;
   requiredConvSettings.prackMode = PrackMode_SupportUasAndUac;
   alice.conversation->setDefaultSettings(alice.handle, supportedConvSettings);
   bob.conversation->setDefaultSettings(bob.handle, supportedConvSettings);
   max.conversation->setDefaultSettings(max.handle, requiredConvSettings);

   alice.enable();
   bob.enable();
   max.enable();

   auto aliceEvents = std::async(std::launch::async, [&]() {
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->addParticipant(aliceCall, bob.config.uri());
      alice.conversation->start(aliceCall);

      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      assertSuccess(alice.conversation->transfer(aliceCall, max.config.uri()));

      assertTransferTryingRingingConnected(alice, aliceCall);

      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });


   auto bobEvents = std::async(std::launch::async, [&]() {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));

      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      SipConversationHandle bobCallToMax;
      assertTransferRequest_ex(bob, bobCall, &bobCallToMax, [&](const TransferRequestEvent& evt) {
         ASSERT_EQ(max.config.uri(), evt.transferTargetAddress);
         ASSERT_EQ("", evt.transferTargetDisplayName);
         ASSERT_NE(0, evt.transferTargetConversation);
      });

      assertSuccess(bob.conversation->acceptIncomingTransferRequest(bobCallToMax));

      assertNewConversationOutgoing(bob, bobCallToMax, max.config.uri());
      assertConversationStateChanged(bob, bobCallToMax, ConversationState_RemoteRinging);
      assertConversationMediaChanged(bob, bobCallToMax, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCallToMax, ConversationState_Connected);

      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
      assertConversationEnded(bob, bobCallToMax, ConversationEndReason_UserTerminatedRemotely);
   });


   auto maxEvents = std::async(std::launch::async, [&]() {
      SipConversationHandle maxCall;
      assertNewConversationIncoming(max, &maxCall, bob.config.uri());
      assertSuccess(max.conversation->sendRingingResponse(maxCall));
      assertConversationStateChanged(max, maxCall, ConversationState_LocalRinging);
      assertSuccess(max.conversation->accept(maxCall));

      assertConversationMediaChanged(max, maxCall, MediaDirection_SendReceive);
      assertConversationStateChanged(max, maxCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(500));

      assertSuccess(max.conversation->end(maxCall));
      assertConversationEnded(max, maxCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor3(aliceEvents, bobEvents, maxEvents);
}

TEST_F(CallTransferTests, BasicTransfer_ReferWarningHeader) {
   TestAccount alice("alice", Account_Init);
   TestAccount bob("bob");
   TestAccount max("max");
   
   const cpc::string warningHeaderValue = "399 007 \"Parked at: 19\"";
   
   class MySipConversationAdornmentHandler : public SipConversationAdornmentHandler
   {
   public:
      MySipConversationAdornmentHandler(CPCAPI2::SipConversation::SipConversationManager* convMgr,
                                     const cpc::string warningHeaderValue) :
                                       convMgr(convMgr),
                                       mWarningHeaderValue(warningHeaderValue) {}

      virtual int onConversationAdornment(SipConversationHandle conversation, const ConversationAdornmentEvent& args)
      {
         if (args.cseqMethod == "REFER" && args.method.empty())
         {
            CPCAPI2::SipHeader sipHeader;
            sipHeader.header = "Warning";
            sipHeader.value = mWarningHeaderValue;
            cpc::vector<CPCAPI2::SipHeader> sipHeaders;
            sipHeaders.push_back(sipHeader);
            convMgr->adornMessage(conversation, args.adornmentMessageId, sipHeaders);
         }
         return kSuccess;
      }

   private:
      CPCAPI2::SipConversation::SipConversationManager* convMgr;
      const cpc::string mWarningHeaderValue;
   };
   
   SipConversationSettings aliceConvSettings;
   aliceConvSettings.adornTransferMessages = true;
   alice.conversation->setDefaultSettings(alice.handle, aliceConvSettings);
   
   MySipConversationAdornmentHandler adornmentHandler(alice.conversation, warningHeaderValue);
   alice.conversation->setAdornmentHandler(alice.handle, &adornmentHandler);
   alice.enable();

   auto aliceEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->addParticipant(aliceCall, bob.config.uri());
      alice.conversation->start(aliceCall);

      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      SipConversationHandle aliceCallToMax;
      assertTransferRequest_ex(alice, aliceCall, &aliceCallToMax, [&](const TransferRequestEvent& evt){
         ASSERT_EQ(max.config.uri(), evt.transferTargetAddress);
         ASSERT_EQ("", evt.transferTargetDisplayName);
         ASSERT_NE(0, evt.transferTargetConversation);
      });

      assertSuccess(alice.conversation->acceptIncomingTransferRequest(aliceCallToMax));

      assertNewConversationOutgoing(alice, aliceCallToMax, max.config.uri());
      assertConversationStateChanged(alice, aliceCallToMax, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCallToMax, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCallToMax, ConversationState_Connected);

      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertConversationEnded(alice, aliceCallToMax, ConversationEndReason_UserTerminatedRemotely);
   });


   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
         
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      assertSuccess(bob.conversation->transfer(bobCall, max.config.uri()));
      
      SipConversationHandle h;
      TransferResponseEvent evt;
      ASSERT_TRUE(bob.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onTransferResponse", 15000, HandleEqualsPred<SipConversationHandle>(bobCall), h, evt));
      ASSERT_EQ(warningHeaderValue, evt.warningHeader);

      assertTransferTryingRingingConnected(bob, bobCall);

      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });


   auto maxEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle maxCall;
      assertNewConversationIncoming(max, &maxCall, alice.config.uri());
      assertSuccess(max.conversation->sendRingingResponse(maxCall));
      assertConversationStateChanged(max, maxCall, ConversationState_LocalRinging);
      assertSuccess(max.conversation->accept(maxCall));
         
      assertConversationMediaChanged(max, maxCall, MediaDirection_SendReceive);
      assertConversationStateChanged(max, maxCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(500));

      assertSuccess(max.conversation->end(maxCall));
      assertConversationEnded(max, maxCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor3(aliceEvents, bobEvents, maxEvents);
}

TEST_F(CallTransferTests, AttendedTransfer_ReferWarningHeader) {
   TestAccount alice("alice", Account_Init);
   TestAccount bob("bob");
   TestAccount max("max");
   
   const cpc::string warningHeaderValue = "399 007 \"Parked at: 19\"";
   
   class MySipConversationAdornmentHandler : public SipConversationAdornmentHandler
   {
   public:
      MySipConversationAdornmentHandler(CPCAPI2::SipConversation::SipConversationManager* convMgr,
                                     const cpc::string warningHeaderValue) :
                                       convMgr(convMgr),
                                       mWarningHeaderValue(warningHeaderValue) {}

      virtual int onConversationAdornment(SipConversationHandle conversation, const ConversationAdornmentEvent& args)
      {
         if (args.cseqMethod == "REFER" && args.method.empty())
         {
            CPCAPI2::SipHeader sipHeader;
            sipHeader.header = "Warning";
            sipHeader.value = mWarningHeaderValue;
            cpc::vector<CPCAPI2::SipHeader> sipHeaders;
            sipHeaders.push_back(sipHeader);
            convMgr->adornMessage(conversation, args.adornmentMessageId, sipHeaders);
         }
         return kSuccess;
      }

   private:
      CPCAPI2::SipConversation::SipConversationManager* convMgr;
      const cpc::string mWarningHeaderValue;
   };
   
   SipConversationSettings aliceConvSettings;
   aliceConvSettings.adornTransferMessages = true;
   alice.conversation->setDefaultSettings(alice.handle, aliceConvSettings);
   
   MySipConversationAdornmentHandler adornmentHandler(alice.conversation, warningHeaderValue);
   alice.conversation->setAdornmentHandler(alice.handle, &adornmentHandler);
   alice.enable();

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendOnly);
      assertSuccess(alice.conversation->accept(aliceCall));
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_ReceiveOnly);

      SipConversationHandle aliceCallToMax;
      assertTransferRequest_ex(alice, aliceCall, &aliceCallToMax, [&](const TransferRequestEvent& evt){
         ASSERT_EQ(max.config.uri(), evt.transferTargetAddress);
         ASSERT_EQ(max.config.name, evt.transferTargetDisplayName);
         ASSERT_NE(0, evt.transferTargetConversation);
      });
      assertSuccess(alice.conversation->acceptIncomingTransferRequest(aliceCallToMax));

      assertNewConversationOutgoing(alice, aliceCallToMax, max.config.uri());
      assertConversationStateChanged(alice, aliceCallToMax, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCallToMax, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCallToMax, ConversationState_Connected);
      assertConversationEnded(alice, aliceCallToMax, ConversationEndReason_UserTerminatedRemotely);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);

   });


   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      assertSuccess(bob.conversation->hold(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendOnly);

      SipConversationHandle bobCallToMax = bob.conversation->createConversation(bob.handle);
      bob.conversation->addParticipant(bobCallToMax, max.config.uri());
      bob.conversation->start(bobCallToMax);

      assertNewConversationOutgoing(bob, bobCallToMax, max.config.uri());
      assertConversationStateChanged(bob, bobCallToMax, ConversationState_RemoteRinging);
      assertConversationMediaChanged(bob, bobCallToMax, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCallToMax, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      assertSuccess(bob.conversation->transfer(bobCallToMax, bobCall));
      
      SipConversationHandle h;
      TransferResponseEvent evt;
      ASSERT_TRUE(bob.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onTransferResponse", 15000, HandleEqualsPred<SipConversationHandle>(bobCall), h, evt));
      ASSERT_EQ(warningHeaderValue, evt.warningHeader);
      
      assertTransferTryingRingingConnected(bob, bobCall);
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      assertConversationEnded(bob, bobCallToMax, ConversationEndReason_UserTerminatedRemotely);
   });

   auto maxEvents = std::async(std::launch::async, [&] () {

      SipConversationHandle maxCallFromBob;
      assertNewConversationIncoming(max, &maxCallFromBob, bob.config.uri());
      assertSuccess(max.conversation->sendRingingResponse(maxCallFromBob));
      assertConversationStateChanged(max, maxCallFromBob, ConversationState_LocalRinging);
      assertSuccess(max.conversation->accept(maxCallFromBob));
      assertConversationMediaChanged(max, maxCallFromBob, MediaDirection_SendReceive);
      assertConversationStateChanged(max, maxCallFromBob, ConversationState_Connected);

      SipConversationHandle maxCallFromAlice;
      assertNewConversationIncomingTransfer_ex(max, &maxCallFromAlice, alice.config.uri(), [&](const NewConversationEvent& evt){
         ASSERT_EQ(alice.config.name, evt.remoteDisplayName);
         ASSERT_EQ(maxCallFromBob, evt.conversationToReplace);
      });

      assertSuccess(max.conversation->sendRingingResponse(maxCallFromAlice));
      assertConversationStateChanged(max, maxCallFromAlice, ConversationState_LocalRinging);
      assertSuccess(max.conversation->accept(maxCallFromAlice));
      assertConversationMediaChanged(max, maxCallFromAlice, MediaDirection_SendReceive);
      assertConversationStateChanged(max, maxCallFromAlice, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      assertSuccess(max.conversation->end(maxCallFromAlice));
      assertConversationEnded(max, maxCallFromAlice, ConversationEndReason_UserTerminatedLocally);
      assertConversationEnded(max, maxCallFromBob, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor3(aliceEvents, bobEvents, maxEvents);
}

TEST_F(CallTransferTests, BasicTransferReferredBy) {
   TestAccount alice("alice");
   TestAccount bob("bob");
   TestAccount max("max");

   auto aliceEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->addParticipant(aliceCall, bob.config.uri());
      alice.conversation->start(aliceCall);

      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      SipConversationHandle aliceCallToMax;
      assertTransferRequest_ex(alice, aliceCall, &aliceCallToMax, [&](const TransferRequestEvent& evt){
         ASSERT_EQ(max.config.uri(), evt.transferTargetAddress);
         ASSERT_EQ("", evt.transferTargetDisplayName);
         ASSERT_NE(0, evt.transferTargetConversation);
      });

      assertSuccess(alice.conversation->acceptIncomingTransferRequest(aliceCallToMax));

      assertNewConversationOutgoing(alice, aliceCallToMax, max.config.uri());
      assertConversationStateChanged(alice, aliceCallToMax, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCallToMax, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCallToMax, ConversationState_Connected);

      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertConversationEnded(alice, aliceCallToMax, ConversationEndReason_UserTerminatedRemotely);
   });


   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
         
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      assertSuccess(bob.conversation->transfer(bobCall, max.config.uri()));

      assertTransferTryingRingingConnected(bob, bobCall);

      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });


   auto maxEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle maxCall;
      NewConversationEvent evt;
      ASSERT_TRUE(max.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onNewConversation", 20000, AlwaysTruePred(), maxCall, evt));
      ASSERT_EQ(ConversationType_Incoming, evt.conversationType);
      
      safeCout("referredByAddress: " << evt.referredByAddress << ", referredByDisplayname: " << evt.referredByDisplayname);
      ASSERT_EQ(bob.uri(), evt.referredByAddress);
      ASSERT_EQ(bob.config.settings.displayName, evt.referredByDisplayname);
      
      assertSuccess(max.conversation->sendRingingResponse(maxCall));
      assertConversationStateChanged(max, maxCall, ConversationState_LocalRinging);
      assertSuccess(max.conversation->accept(maxCall));
         
      assertConversationMediaChanged(max, maxCall, MediaDirection_SendReceive);
      assertConversationStateChanged(max, maxCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(500));

      assertSuccess(max.conversation->end(maxCall));
      assertConversationEnded(max, maxCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor3(aliceEvents, bobEvents, maxEvents);
}

   TEST_F(CallTransferTests, AttendedTransferWithPAssertedIdentity) {
      class MySipConversationAdornmentHandler : public SipConversationAdornmentHandler
      {
      public:
         MySipConversationAdornmentHandler(CPCAPI2::SipConversation::SipConversationManager* convMgr, const cpc::vector<cpc::string>& assertedIdentities) : convMgr(convMgr), mAssertedIdentities(assertedIdentities) {}

         virtual int onConversationAdornment(SipConversationHandle conversation, const ConversationAdornmentEvent& args)
         {
            if (args.cseqMethod == "INVITE") {
               cpc::vector<CPCAPI2::SipHeader> sipHeaders;
               for (const auto& identity : mAssertedIdentities) {
                  sipHeaders.push_back({"P-Asserted-Identity", identity});
               }
               convMgr->adornMessage(conversation, args.adornmentMessageId, sipHeaders);
            }
            return kSuccess;
         }

      private:
         CPCAPI2::SipConversation::SipConversationManager* convMgr;
         const cpc::vector<cpc::string> mAssertedIdentities;
      };

      TestAccount alice("alice");
      TestAccount bob("bob");
      TestAccount max("max", Account_Init);

      cpc::vector<cpc::string> maxAssertedIdentities;
      maxAssertedIdentities.push_back(max.config.uri());

      MySipConversationAdornmentHandler maxAdornmentHandler(max.conversation, maxAssertedIdentities);
      max.conversation->setAdornmentHandler(max.handle, &maxAdornmentHandler);
      max.enable();

      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->addParticipant(aliceCall, bob.config.uri());
      alice.conversation->start(aliceCall);

      auto aliceEvents = std::async(std::launch::async, [&] () {
         assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
         assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
         assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
         assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
         // remove next 3 lines if we don't want hold call
         assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendOnly);
         assertSuccess(alice.conversation->accept(aliceCall));
         assertConversationMediaChanged(alice, aliceCall, MediaDirection_ReceiveOnly);

         SipConversationHandle aliceCallToMax;
         assertTransferRequest_ex(alice, aliceCall, &aliceCallToMax, [&](const TransferRequestEvent& evt){
            ASSERT_EQ(max.config.uri(), evt.transferTargetAddress);
            ASSERT_EQ(max.config.name, evt.transferTargetDisplayName);
            ASSERT_NE(0, evt.transferTargetConversation);
         });

         assertSuccess(alice.conversation->acceptIncomingTransferRequest(aliceCallToMax));
         assertNewConversationOutgoing(alice, aliceCallToMax, max.config.uri());
         assertConversationStateChanged(alice, aliceCallToMax, ConversationState_RemoteRinging);

         {
            SipConversationHandle h;
            ConversationMediaChangedEvent evt;
            ASSERT_TRUE(alice.conversationEvents->expectEvent("SipConversationHandler::onConversationMediaChanged",
                                                              20000, HandleEqualsPred<SipConversationHandle>(aliceCallToMax), h, evt));
            ASSERT_EQ(h, aliceCallToMax);
            for (const auto& mi: evt.localMediaInfo) {
               ASSERT_EQ(MediaDirection_SendReceive, mi.mediaDirection);
            }

            ASSERT_EQ(evt.assertedIdentity, max.config.uri());
         }

         assertConversationStateChanged(alice, aliceCallToMax, ConversationState_Connected);
         assertConversationEnded(alice, aliceCallToMax, ConversationEndReason_UserTerminatedRemotely);
         assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      });

      auto bobEvents = std::async(std::launch::async, [&] () {
         SipConversationHandle bobCall;
         assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
         assertSuccess(bob.conversation->sendRingingResponse(bobCall));
         assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
         assertSuccess(bob.conversation->accept(bobCall));
         assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
         assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
         std::this_thread::sleep_for(std::chrono::milliseconds(1000));

         // remove next 2 lines if we don't want hold call
         assertSuccess(bob.conversation->hold(bobCall));
         assertConversationMediaChanged(bob, bobCall, MediaDirection_SendOnly);

         SipConversationHandle bobCallToMax = bob.conversation->createConversation(bob.handle);
         bob.conversation->addParticipant(bobCallToMax, max.config.uri());
         bob.conversation->start(bobCallToMax);

         assertNewConversationOutgoing(bob, bobCallToMax, max.config.uri());
         assertConversationStateChanged(bob, bobCallToMax, ConversationState_RemoteRinging);
         assertConversationMediaChanged(bob, bobCallToMax, MediaDirection_SendReceive);
         assertConversationStateChanged(bob, bobCallToMax, ConversationState_Connected);
         std::this_thread::sleep_for(std::chrono::milliseconds(1000));

         assertSuccess(bob.conversation->transfer(bobCallToMax, bobCall));

         SipConversationHandle h;
         TransferResponseEvent evt;
         ASSERT_TRUE(bob.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onTransferResponse", 15000, HandleEqualsPred<SipConversationHandle>(bobCall), h, evt));

         assertTransferTryingRingingConnected(bob, bobCall);
         assertSuccess(bob.conversation->end(bobCall));
         assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
         assertConversationEnded(bob, bobCallToMax, ConversationEndReason_UserTerminatedRemotely);
      });

      auto maxEvents = std::async(std::launch::async, [&] () {

         SipConversationHandle maxCallFromBob;
         assertNewConversationIncoming(max, &maxCallFromBob, bob.config.uri());
         assertSuccess(max.conversation->sendRingingResponse(maxCallFromBob));
         assertConversationStateChanged(max, maxCallFromBob, ConversationState_LocalRinging);
         assertSuccess(max.conversation->accept(maxCallFromBob));
         assertConversationMediaChanged(max, maxCallFromBob, MediaDirection_SendReceive);
         assertConversationStateChanged(max, maxCallFromBob, ConversationState_Connected);

         SipConversationHandle maxCallFromAlice;
         assertNewConversationIncomingTransfer_ex(max, &maxCallFromAlice, alice.config.uri(), [&](const NewConversationEvent& evt){
            ASSERT_EQ(alice.config.name, evt.remoteDisplayName);
            ASSERT_EQ(maxCallFromBob, evt.conversationToReplace);
         });

         assertSuccess(max.conversation->sendRingingResponse(maxCallFromAlice));
         assertConversationStateChanged(max, maxCallFromAlice, ConversationState_LocalRinging);
         assertSuccess(max.conversation->accept(maxCallFromAlice));
         assertConversationMediaChanged(max, maxCallFromAlice, MediaDirection_SendReceive);
         assertConversationStateChanged(max, maxCallFromAlice, ConversationState_Connected);
         std::this_thread::sleep_for(std::chrono::milliseconds(1000));

         assertSuccess(max.conversation->end(maxCallFromAlice));
         assertConversationEnded(max, maxCallFromAlice, ConversationEndReason_UserTerminatedLocally);
         assertConversationEnded(max, maxCallFromBob, ConversationEndReason_UserTerminatedLocally);
      });

      waitFor3(aliceEvents, bobEvents, maxEvents);
   }

}  // namespace

