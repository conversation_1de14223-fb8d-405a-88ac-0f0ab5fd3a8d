#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#if (CPCAPI2_BRAND_SIP_BUSY_LAMP_FIELD_MODULE == 1)

#include "cpcapi2_test_fixture.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>

#include "test_framework/cpcapi2_test_framework.h"
#include "test_framework/bare_bones_sip_endpoint.h"
#include "test_account_events.h"
#include "test_call_events.h"

#include <thread>
#include <future>

using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::SipEvent;
using namespace CPCAPI2::SipConversation;
using namespace CPCAPI2::SipDialogEvent;
using namespace CPCAPI2::SipBusyLampField;

namespace {

class AttendantTestAccount : public TestAccount
{
public:
   AttendantTestAccount(const std::string& name, bool disableOnDestruct = true) : TestAccount(name, Account_NoInit, disableOnDestruct)
   {
      if (name == "attendant")
      {
         config.settings.displayName = "**********";
         config.settings.username = "+1**********";
         config.settings.auth_username = "<EMAIL>";
         config.settings.domain = "csa1.luqdlab.com";
         config.settings.outboundProxy = "**********";
         config.settings.password = "newsys";
         config.settings.registrationIntervalSeconds = 3600;
      }
      else
      {
         assert(false);
      }

      init();

      busyLampFieldManager = SipBusyLampFieldManager::getInterface(phone);
      busyLampFieldManager->setHandler(handle, (SipBusyLampFieldHandler*) 0xDEADBEEF);
      busyLampFieldEvents = new test::EventHandler(name.c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(busyLampFieldManager));
      busyLampFieldStateManager = SipBusyLampFieldStateManager::getInterface(busyLampFieldManager);

      enable();
   }

   SipBusyLampFieldManager* busyLampFieldManager;
   CPCAPI2::test::EventHandler* busyLampFieldEvents;
   SipBusyLampFieldStateManager* busyLampFieldStateManager;
};

class MonitoredLineTestAccount : public TestAccount
{
public:
   MonitoredLineTestAccount(const std::string& name) : TestAccount(name, Account_NoInit)
   {
      if (name == "alice")
      {
         config.settings.displayName = "**********";
         config.settings.username = "+1**********";
         config.settings.auth_username = "<EMAIL>";
         config.settings.domain = "csa1.luqdlab.com";
         config.settings.outboundProxy = "**********";
         config.settings.password = "newsys";
         config.settings.registrationIntervalSeconds = 3600;
      }
      else if (name == "bob")
      {
         config.settings.displayName = "**********";
         config.settings.username = "+1**********";
         config.settings.auth_username = "<EMAIL>";
         config.settings.domain = "csa1.luqdlab.com";
         config.settings.outboundProxy = "**********";
         config.settings.password = "newsys";
         config.settings.registrationIntervalSeconds = 3600;
      }
      else if (name == "charlie")
      {
         config.settings.displayName = "**********";
         config.settings.username = "+***********";
         config.settings.auth_username = "<EMAIL>";
         config.settings.domain = "csa1.luqdlab.com";
         config.settings.outboundProxy = "**********";
         config.settings.password = "newsys";
         config.settings.registrationIntervalSeconds = 3600;
      }
      else if (name == "dave")
      {
         config.settings.displayName = "**********";
         config.settings.username = "+1**********";
         config.settings.auth_username = "<EMAIL>";
         config.settings.domain = "csa1.luqdlab.com";
         config.settings.outboundProxy = "**********";
         config.settings.password = "newsys";
         config.settings.registrationIntervalSeconds = 3600;
      }
      else
      {
         assert(false);
      }

      init();
      enable();
   }
};

class StandardLineTestAccount : public TestAccount
{
public:
   StandardLineTestAccount(const std::string& name) : TestAccount(name, Account_NoInit)
   {
      if (name == "bob")
      {
         config.settings.displayName = "**********";
         config.settings.username = "+***********";
         config.settings.auth_username = "<EMAIL>";
         config.settings.domain = "csa1.luqdlab.com";
         config.settings.outboundProxy = "**********";
         config.settings.password = "newsys";
         config.settings.registrationIntervalSeconds = 3600;
      }
      else
      {
         assert(false);
      }

      init();
      enable();
   }
};

#define assertPhoneError(account, errorText) \
   BusyLampFieldTest::expectPhoneError(__LINE__, account, errorText) 
#define assertDialogEventNewSubscription(account, remoteAddress, subscription) \
   BusyLampFieldTest::expectDialogEventNewSubscription(__LINE__, account, remoteAddress, subscription)
#define assertDialogEventSubscriptionStateChanged(account, subscription, subscriptionState) \
   BusyLampFieldTest::expectDialogEventSubscriptionStateChanged(__LINE__, account, subscription, subscriptionState)
#define assertDialogEventSubscriptionEnded(account, subscription) \
   BusyLampFieldTest::expectDialogEventSubscriptionEnded(__LINE__, account, subscription)
#define assertBLFError(account, remoteLineSet, errorText) \
   BusyLampFieldTest::expectBLFError(__LINE__, account, remoteLineSet, errorText)
#define assertBLFRemoteLineNewSubscription(account, remoteLineSet, remoteLine) \
   BusyLampFieldTest::expectBLFRemoteLineNewSubscription(__LINE__, account, remoteLineSet, remoteLine)
#define assertBLFRemoteLineState(account, remoteLineSet, remoteLine, subscriptionStarted, subscriptionState) \
   BusyLampFieldTest::expectBLFRemoteLineState(__LINE__, account, remoteLineSet, remoteLine, subscriptionStarted, subscriptionState, DialogState_NotSpecified)
#define assertBLFRemoteLineState_ex(account, remoteLineSet, remoteLine, subscriptionStarted, subscriptionState, dialogState) \
   BusyLampFieldTest::expectBLFRemoteLineState(__LINE__, account, remoteLineSet, remoteLine, subscriptionStarted, subscriptionState, dialogState)
#define assertBLFRemoteLineStateChanged(account, remoteLineSet, remoteLine) \
   do { cpc::string dialogId; BusyLampFieldTest::expectBLFRemoteLineStateChanged(__LINE__, account, remoteLineSet, remoteLine, DialogDirection_NotSpecified, DialogState_NotSpecified, "", "", dialogId); } while(0)
#define assertBLFRemoteLineStateChanged_ex(account, remoteLineSet, remoteLine, direction, dialogState, localAddress, remoteAddress, dialogId) \
   BusyLampFieldTest::expectBLFRemoteLineStateChanged(__LINE__, account, remoteLineSet, remoteLine, direction, dialogState, localAddress, remoteAddress, dialogId)
#define assertBLFRemoteLineSubscriptionStateChanged(account, remoteLineSet, remoteLine, subscriptionState) \
   BusyLampFieldTest::expectBLFRemoteLineSubscriptionStateChanged(__LINE__, account, remoteLineSet, remoteLine, subscriptionState)
#define assertBLFRemoteLineSubscriptionEnded(account, remoteLineSet, remoteLine) \
   BusyLampFieldTest::expectBLFRemoteLineSubscriptionEnded(__LINE__, account, remoteLineSet, remoteLine)
#define waitForConversationStateChanged(account, conversation, conversationState) \
   BusyLampFieldTest::waitForConversationStateChanged_(__LINE__, account, conversation, conversationState)

class BusyLampFieldTest : public CpcapiAutoTest
{
public:
   BusyLampFieldTest() {}
   virtual ~BusyLampFieldTest() {}

   static void expectPhoneError(int line, TestAccount& account, const cpc::string& errorText);
   static void expectDialogEventNewSubscription(int line, TestAccount& account, const cpc::string& remoteAddress, SipDialogEventSubscriptionHandle& subscription);
   static void expectDialogEventSubscriptionEnded(int line, TestAccount& account, SipDialogEventSubscriptionHandle subscription);
   static void expectDialogEventSubscriptionStateChanged(int line, TestAccount& account, SipDialogEventSubscriptionHandle subscription, SipSubscriptionState subscriptionState);
   static void expectBLFError(int line, AttendantTestAccount& account, SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const cpc::string& errorText);
   static void expectBLFRemoteLineNewSubscription(int line, AttendantTestAccount& account, SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const SipBusyLampFieldRemoteLineHandle& remoteLine);
   static void expectBLFRemoteLineState(int line, AttendantTestAccount& account, SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const SipBusyLampFieldRemoteLineHandle& remoteLine, bool subscriptionStarted, SipSubscriptionState subscriptionState, DialogState dialogState);
   static void expectBLFRemoteLineStateChanged(int line, AttendantTestAccount& account, SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const SipBusyLampFieldRemoteLineHandle& remoteLine, DialogDirection direction, DialogState dialogState, const cpc::string& localAddress, const cpc::string& remoteAddress, cpc::string& dialogId);
   static void expectBLFRemoteLineSubscriptionStateChanged(int line, AttendantTestAccount& account, SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const SipBusyLampFieldRemoteLineHandle& remoteLine, SipSubscriptionState subscriptionState);
   static void expectBLFRemoteLineSubscriptionEnded(int line, AttendantTestAccount& account, SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const SipBusyLampFieldRemoteLineHandle& remoteLine);
   static void waitForConversationStateChanged_(int line, TestAccount& account, SipConversationHandle conversation, ConversationState conversationState);
};

// Invalid account handle passed in
TEST_F(BusyLampFieldTest, SubscribeInvalidAccountFailure)
{
   AttendantTestAccount attendant("attendant");

   // Create a new remote line set using an invalid account
   SipBusyLampFieldRemoteLineSetSettings settings;
   SipBusyLampFieldRemoteLineSetHandle remoteLineSet = attendant.busyLampFieldManager->createBusyLampFieldRemoteLineSet(123, settings);

   // Expect an error
   assertPhoneError(attendant, "Creating remote line set with invalid account handle: 123, SipBusyLampFieldRemoteLineSetHandle invalid: " + cpc::to_string(remoteLineSet));
}

// Account passed in disabled
TEST_F(BusyLampFieldTest, SubscribeAccountDisabledFailure)
{
   AttendantTestAccount attendant("attendant", false);

   // Disable the account
   attendant.account->disable(attendant.handle);
   assertAccountDeregistering(attendant);
   assertAccountDeregistered(attendant);

   // Create a new remote line set with a disabled account
   SipBusyLampFieldRemoteLineSetSettings settings;
   SipBusyLampFieldRemoteLineSetHandle remoteLineSet = attendant.busyLampFieldManager->createBusyLampFieldRemoteLineSet(attendant.handle, settings);

   // Expect an error
   assertPhoneError(attendant, "Creating remote line set before account enabled: " + cpc::to_string(attendant.handle) + ", SipBusyLampFieldRemoteLineSetHandle invalid: " + cpc::to_string(remoteLineSet));
}

// ALU15 - No remote line specified
TEST_F(BusyLampFieldTest, SubscribeNoRemoteLineFailure) 
{
   AttendantTestAccount attendant("attendant");

   // Create a new remote line set
   SipBusyLampFieldRemoteLineSetSettings settings;
   SipDialogEventSubscriptionHandle remoteLineSet = attendant.busyLampFieldManager->createBusyLampFieldRemoteLineSet(attendant.handle, settings);
   ASSERT_TRUE(remoteLineSet > 0);

   // Start a subscription without any remote line
   assertSuccess(attendant.busyLampFieldManager->start(remoteLineSet));

   // Expect an error
   assertBLFError(attendant, remoteLineSet, "Cannot start subscriptions. No remote line have been added");
}

// ALU15 - Invalid URI passed as a remote line
TEST_F(BusyLampFieldTest, InvalidRemoteLineAddressFailure)
{
   AttendantTestAccount attendant("attendant");

   // Create a new remote line set
   SipBusyLampFieldRemoteLineSetSettings settings;
   settings.expires = 3600;
   SipBusyLampFieldRemoteLineSetHandle remoteLineSet = attendant.busyLampFieldManager->createBusyLampFieldRemoteLineSet(attendant.handle, settings);
   ASSERT_TRUE(remoteLineSet > 0);

   // Attendant subscribes to an invalid remote line
   SipBusyLampFieldRemoteLineHandle remoteLine = "sip:alice@invalid"; // Invalid URI
   assertSuccess(attendant.busyLampFieldManager->addRemoteLine(remoteLineSet, remoteLine));
   
   // Start the subscription
   assertSuccess(attendant.busyLampFieldManager->start(remoteLineSet));

   // Wait for the subscription state to transition to Terminated
   assertBLFRemoteLineSubscriptionEnded(attendant, remoteLineSet, remoteLine);
}

// ALU15 - Subscribe to one remote line which accepts the incoming subscription
TEST_F(BusyLampFieldTest, SubscribeRemoteLineAccepted)
{
   AttendantTestAccount attendant("attendant");
   MonitoredLineTestAccount alice("alice");

   SipBusyLampFieldRemoteLineHandle aliceRemoteLine = alice.config.uri();

   // Attendant subscribes to the remote line
   SipBusyLampFieldRemoteLineSetSettings settings;
   settings.expires = 3600;
   SipBusyLampFieldRemoteLineSetHandle remoteLineSet = attendant.busyLampFieldManager->createBusyLampFieldRemoteLineSet(attendant.handle, settings);
   ASSERT_TRUE(remoteLineSet > 0);
   assertSuccess(attendant.busyLampFieldManager->addRemoteLine(remoteLineSet, aliceRemoteLine));

   // Make sure there is no state info available for the remote line set yet   
   SipBusyLampFieldRemoteLineSetState remoteLineSetState;
   ASSERT_EQ(attendant.busyLampFieldStateManager->getState(remoteLineSet, remoteLineSetState), kError);

   // Start the subscription
   assertSuccess(attendant.busyLampFieldManager->start(remoteLineSet));
   
   // Wait for the new subscription notification
   assertBLFRemoteLineNewSubscription(attendant, remoteLineSet, aliceRemoteLine);

   // Validate the state of the remote line
   assertBLFRemoteLineState(attendant, remoteLineSet, aliceRemoteLine, true, SipSubscriptionState_Pending);

   // Wait for the subscription state change notification
   assertBLFRemoteLineSubscriptionStateChanged(attendant, remoteLineSet,aliceRemoteLine, SipSubscriptionState_Active);

   // Wait for line state change notifications
   assertBLFRemoteLineStateChanged(attendant, remoteLineSet, aliceRemoteLine);

   // Validate the state of the remote line
   assertBLFRemoteLineState(attendant, remoteLineSet, aliceRemoteLine, true, SipSubscriptionState_Active);

   // Terminate subscription
   assertSuccess(attendant.busyLampFieldManager->end(remoteLineSet));

   // Wait for the subscription termination notification
   assertBLFRemoteLineSubscriptionEnded(attendant, remoteLineSet, aliceRemoteLine);

   // Validate the state of the remote line
   assertBLFRemoteLineState(attendant, remoteLineSet, aliceRemoteLine, false, SipSubscriptionState_Terminated);
}

// ALU15 - Subscribe to several remote lines which all accept their respective incoming subscriptions
TEST_F(BusyLampFieldTest, SubscribeRemoteLinesAccepted)
{
   AttendantTestAccount attendant("attendant");
   MonitoredLineTestAccount remoteLineAccounts[] = { MonitoredLineTestAccount("alice"), MonitoredLineTestAccount("bob"), MonitoredLineTestAccount("charlie"), MonitoredLineTestAccount("dave") };
   int remoteLineCount = sizeof(remoteLineAccounts) / sizeof(MonitoredLineTestAccount);

   // Attendant subscribes to all the remote lines
   SipBusyLampFieldRemoteLineSetSettings settings;
   settings.expires = 3600;
   SipBusyLampFieldRemoteLineSetHandle remoteLineSet = attendant.busyLampFieldManager->createBusyLampFieldRemoteLineSet(attendant.handle, settings);
   ASSERT_TRUE(remoteLineSet > 0);
   for (int i = 0; i < remoteLineCount; i++)
   {
      MonitoredLineTestAccount& remoteLineAccount = remoteLineAccounts[i];
      SipBusyLampFieldRemoteLineHandle remoteLine = remoteLineAccount.config.uri();

      assertSuccess(attendant.busyLampFieldManager->addRemoteLine(remoteLineSet, remoteLine));
   }

   // Make sure there is no state info available for the remote line set yet
   SipBusyLampFieldRemoteLineSetState remoteLineSetState;
   ASSERT_EQ(attendant.busyLampFieldStateManager->getState(remoteLineSet, remoteLineSetState), kError);

   // Start the subscriptions
   assertSuccess(attendant.busyLampFieldManager->start(remoteLineSet));

   std::set<SipBusyLampFieldRemoteLineHandle> remoteLines;
   for (int i = 0; i < remoteLineCount; i++)
   {
      MonitoredLineTestAccount& remoteLineAccount = remoteLineAccounts[i];
      SipBusyLampFieldRemoteLineHandle remoteLine = remoteLineAccount.config.uri();
      remoteLines.insert(remoteLine);
   }

   {
      // Wait for line state change notifications
      std::set<SipBusyLampFieldRemoteLineHandle> tmpRemoteLines(remoteLines);
      for (unsigned int i = 0; i < remoteLines.size(); i++)
      {
         SipBusyLampFieldRemoteLineSetHandle h;
         RemoteLineStateChangedEvent evt;
         ASSERT_TRUE(cpcWaitForEvent(attendant.busyLampFieldEvents,
            "SipBusyLampFieldHandler::onRemoteLineStateChanged",
		      5000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(remoteLineSet, h);
         tmpRemoteLines.erase(evt.remoteLine);
      }
      ASSERT_TRUE(tmpRemoteLines.empty());
   }

   {
      // Validate the state of the remote lines
      std::set<SipBusyLampFieldRemoteLineHandle> tmpRemoteLines(remoteLines);
      SipBusyLampFieldRemoteLineSetState remoteLineSetState;
      assertSuccess(attendant.busyLampFieldStateManager->getState(remoteLineSet, remoteLineSetState));
      ASSERT_EQ(remoteLineSetState.account, attendant.handle);
      ASSERT_EQ(remoteLineSetState.remoteLineSet, remoteLineSet);
      for (unsigned int i = 0; i < remoteLines.size(); i++)
      {
         ASSERT_EQ(remoteLineSetState.remoteLineStates[i].remoteLine.empty(), false);
         ASSERT_EQ(remoteLineSetState.remoteLineStates[i].subscriptionStarted, true);
         ASSERT_EQ(remoteLineSetState.remoteLineStates[i].subscriptionState, SipSubscriptionState_Active);
         ASSERT_EQ(remoteLineSetState.remoteLineStates[i].calls.size(), 0);
         tmpRemoteLines.erase(remoteLineSetState.remoteLineStates[i].remoteLine);
      }
      ASSERT_TRUE(tmpRemoteLines.empty());
   }

   // Terminate all subscription
   assertSuccess(attendant.busyLampFieldManager->end(remoteLineSet));

   {
      // Wait for the subscription termination notifications
      std::set<SipBusyLampFieldRemoteLineHandle> tmpRemoteLines(remoteLines);
      for (unsigned int i = 0; i < remoteLines.size(); i++)
      {
		   SipBusyLampFieldRemoteLineSetHandle h;
		   RemoteLineSubscriptionEndedEvent evt;
         ASSERT_TRUE(cpcWaitForEvent(attendant.busyLampFieldEvents,
			   "SipBusyLampFieldHandler::onRemoteLineSubscriptionEnded",
			   15000,
			   AlwaysTruePred(), h, evt));
         ASSERT_EQ(remoteLineSet, h);
		   ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
         tmpRemoteLines.erase(evt.remoteLine);
      }
      ASSERT_TRUE(tmpRemoteLines.empty());
   }

   {
      // Validate the state of the remote lines
      std::set<cpc::string> tmpRemoteLines(remoteLines);
      SipBusyLampFieldRemoteLineSetState remoteLineSetState;
      assertSuccess(attendant.busyLampFieldStateManager->getState(remoteLineSet, remoteLineSetState));
      ASSERT_EQ(remoteLineSetState.account, attendant.handle);
      ASSERT_EQ(remoteLineSetState.remoteLineSet, remoteLineSet);
      for (unsigned int i = 0; i < remoteLines.size(); i++)
      {
         ASSERT_EQ(remoteLineSetState.remoteLineStates[i].remoteLine.empty(), false);
         ASSERT_EQ(remoteLineSetState.remoteLineStates[i].subscriptionStarted, false);
         ASSERT_EQ(remoteLineSetState.remoteLineStates[i].subscriptionState, SipSubscriptionState_Terminated);
         ASSERT_EQ(remoteLineSetState.remoteLineStates[i].calls.size(), 0);
         tmpRemoteLines.erase(remoteLineSetState.remoteLineStates[i].remoteLine);
      }
      ASSERT_TRUE(tmpRemoteLines.empty());
   }
}

// ALU15 - Subscribe to one remote line which accepts the incoming subscription
TEST_F(BusyLampFieldTest, SubscribeRemoteLineAcceptedWithCall)
{
   AttendantTestAccount attendant("attendant");
   MonitoredLineTestAccount alice("alice");
   StandardLineTestAccount bob("bob");

   SipBusyLampFieldRemoteLineHandle aliceRemoteLine = alice.config.uri();

   // Attendant subscribes to the remote line
   SipBusyLampFieldRemoteLineSetSettings settings;
   settings.expires = 3600;
   SipBusyLampFieldRemoteLineSetHandle remoteLineSet = attendant.busyLampFieldManager->createBusyLampFieldRemoteLineSet(attendant.handle, settings);
   ASSERT_TRUE(remoteLineSet > 0);
   assertSuccess(attendant.busyLampFieldManager->addRemoteLine(remoteLineSet, aliceRemoteLine));

   // Make sure there is no state info available for the remote line set yet
   SipBusyLampFieldRemoteLineSetState remoteLineSetState;
   ASSERT_EQ(attendant.busyLampFieldStateManager->getState(remoteLineSet, remoteLineSetState), kError);

   // Start the subscription
   assertSuccess(attendant.busyLampFieldManager->start(remoteLineSet));

   // Wait for the new subscription notification
   assertBLFRemoteLineNewSubscription(attendant, remoteLineSet, aliceRemoteLine);

   // Wait for the subscription state change notification
   assertBLFRemoteLineSubscriptionStateChanged(attendant, remoteLineSet, aliceRemoteLine, SipSubscriptionState_Active);

   // Wait for remote line state change notification
   assertBLFRemoteLineStateChanged(attendant, remoteLineSet, aliceRemoteLine);

   // Validate the state of the remote line
   assertBLFRemoteLineState(attendant, remoteLineSet, aliceRemoteLine, true, SipSubscriptionState_Active);

   // Alice calls Bob
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);
   SipConversationHandle bobCall;
   assertNewConversationIncoming(bob, &bobCall, aliceRemoteLine);
   bob.conversation->sendRingingResponse(bobCall);
   assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
   bob.conversation->accept(bobCall);
   assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
   assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
   assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
   assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
   assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
   assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

   // Bob terminates the call with Alice
   bob.conversation->end(bobCall);
   assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);   

   // Wait for remote line state change notification => Trying
   cpc::string aliceDialogId;
   assertBLFRemoteLineStateChanged_ex(attendant, remoteLineSet, aliceRemoteLine, DialogDirection_Initiator, DialogState_Trying, aliceRemoteLine, bob.config.uri(), aliceDialogId);

   // Validate the state of the remote line
   assertBLFRemoteLineState_ex(attendant, remoteLineSet, aliceRemoteLine, true, SipSubscriptionState_Active, DialogState_Trying);

   // Wait for remote line state change notification => Early
   assertBLFRemoteLineStateChanged_ex(attendant, remoteLineSet, aliceRemoteLine, DialogDirection_Initiator, DialogState_Early, aliceRemoteLine, bob.config.uri(), aliceDialogId);

   // Validate the state of the remote line
   assertBLFRemoteLineState_ex(attendant, remoteLineSet, aliceRemoteLine, true, SipSubscriptionState_Active, DialogState_Early);

   // Wait for remote line state change notification => Early
   assertBLFRemoteLineStateChanged_ex(attendant, remoteLineSet, aliceRemoteLine, DialogDirection_Initiator, DialogState_Confirmed, aliceRemoteLine, bob.config.uri(), aliceDialogId);

   // Validate the state of the remote line
   assertBLFRemoteLineState_ex(attendant, remoteLineSet, aliceRemoteLine, true, SipSubscriptionState_Active, DialogState_Confirmed);

   // Wait for remote line state change notification => Terminated
   assertBLFRemoteLineStateChanged_ex(attendant, remoteLineSet, aliceRemoteLine, DialogDirection_Initiator, DialogState_Terminated, aliceRemoteLine, bob.config.uri(), aliceDialogId);

   // Validate the state of the remote line
   assertBLFRemoteLineState_ex(attendant, remoteLineSet, aliceRemoteLine, true, SipSubscriptionState_Active, DialogState_Terminated);

   // Terminate subscription
   assertSuccess(attendant.busyLampFieldManager->end(remoteLineSet));

   // Wait for the subscription termination notification
   assertBLFRemoteLineSubscriptionEnded(attendant, remoteLineSet, aliceRemoteLine);

   // Validate the state of the remote line
   assertBLFRemoteLineState_ex(attendant, remoteLineSet, aliceRemoteLine, false, SipSubscriptionState_Terminated, DialogState_Terminated);
}

// ALU25- Subscribe to one remote line which accepts the incoming subscription. Answers the incoming call on the remote line.
TEST_F(BusyLampFieldTest, AnswerCallOnRemoteLine)
{
   AttendantTestAccount attendant("attendant");
   MonitoredLineTestAccount alice("alice");
   StandardLineTestAccount bob("bob");

   SipBusyLampFieldRemoteLineHandle aliceRemoteLine = alice.config.uri();

   // Attendant subscribes to the remote line
   SipBusyLampFieldRemoteLineSetSettings settings;
   settings.expires = 3600;
   SipBusyLampFieldRemoteLineSetHandle remoteLineSet = attendant.busyLampFieldManager->createBusyLampFieldRemoteLineSet(attendant.handle, settings);
   ASSERT_TRUE(remoteLineSet > 0);
   assertSuccess(attendant.busyLampFieldManager->addRemoteLine(remoteLineSet, aliceRemoteLine));

   // Start the subscription
   assertSuccess(attendant.busyLampFieldManager->start(remoteLineSet));

   // Wait for the new subscription notification
   assertBLFRemoteLineNewSubscription(attendant, remoteLineSet, aliceRemoteLine);

   // Wait for the new subscription notification
   assertBLFRemoteLineSubscriptionStateChanged(attendant, remoteLineSet, aliceRemoteLine, SipSubscriptionState_Active);

   // Wait for remote line (Alice) state change notification
   assertBLFRemoteLineStateChanged(attendant, remoteLineSet, aliceRemoteLine);

   // Bob calls Alice and stays in the 'ringing' state
   SipConversationHandle bobRingingCall;
   SipConversationHandle aliceRingingCall;
   bobRingingCall = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobRingingCall, aliceRemoteLine);
   bob.conversation->start(bobRingingCall);
   assertNewConversationIncoming(alice, &aliceRingingCall, bob.config.uri());
   alice.conversation->sendRingingResponse(aliceRingingCall);
   assertConversationStateChanged(alice, aliceRingingCall, ConversationState_LocalRinging);         
   assertNewConversationOutgoing(bob, bobRingingCall, aliceRemoteLine);
   assertConversationStateChanged(bob, bobRingingCall, ConversationState_RemoteRinging);

   // ===> Alice's phone is ringing
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   // Wait for remote line (Alice) state change notification => Trying
   cpc::string aliceDialogId;
   assertBLFRemoteLineStateChanged_ex(attendant, remoteLineSet, aliceRemoteLine, DialogDirection_Recipient, DialogState_Trying, aliceRemoteLine, bob.config.uri(), aliceDialogId);

   // Wait for remote line (Alice) state change notification => Early
   assertBLFRemoteLineStateChanged_ex(attendant, remoteLineSet, aliceRemoteLine, DialogDirection_Recipient, DialogState_Early, aliceRemoteLine, bob.config.uri(), aliceDialogId);

   // Answer the call
   SipConversationHandle conversation;
   conversation = attendant.busyLampFieldManager->answerCall(remoteLineSet, aliceRemoteLine, aliceDialogId);
   ASSERT_TRUE(conversation > 0);

   // Expect Alice's call to be terminated
   assertConversationEnded(alice, aliceRingingCall, ConversationEndReason_UserTerminatedRemotely);

   assertNewConversationOutgoing(attendant, conversation, aliceRemoteLine);

   // Wait for remote line (Alice) state change notification => Terminated
   assertBLFRemoteLineStateChanged_ex(attendant, remoteLineSet, aliceRemoteLine, DialogDirection_Recipient, DialogState_Terminated, aliceRemoteLine, bob.config.uri(), aliceDialogId);

   // Wait for the Attendant's conversation (w/ Bob) to be connected
   assertConversationMediaChanged(attendant, conversation, MediaDirection_ReceiveOnly);
   assertConversationStateChanged(attendant, conversation, ConversationState_Connected);
   assertConversationMediaChangeRequest(attendant, conversation, MediaDirection_SendReceive);
   attendant.conversation->accept(conversation);
   assertConversationMediaChanged(attendant, conversation, MediaDirection_SendReceive);

   // Wait for Bob's conversation (w/ Attendant) to be connected
   // NOTE: the sequence of events varies based on the timing of things
   waitForConversationStateChanged(bob, bobRingingCall, ConversationState_Connected);

   // ===> Attendant and Bob are now talking
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   // Terminate the call between Attendant and Bob
   bob.conversation->end(bobRingingCall);
   assertConversationEnded(bob, bobRingingCall, ConversationEndReason_UserTerminatedLocally);
   assertConversationEnded(attendant, conversation, ConversationEndReason_UserTerminatedRemotely);   

   // Terminate the subscription
   assertSuccess(attendant.busyLampFieldManager->end(remoteLineSet));

   // Wait for the subscription state to transition to Terminated
   assertBLFRemoteLineSubscriptionEnded(attendant, remoteLineSet, aliceRemoteLine);
}

// ALU26 - Subscribe to one remote line which accepts the incoming subscription. Joins the call on the remote line.
TEST_F(BusyLampFieldTest, JoinCallOnRemoteLine)
{
   AttendantTestAccount attendant("attendant");
   MonitoredLineTestAccount alice("alice");
   StandardLineTestAccount bob("bob");

   SipBusyLampFieldRemoteLineHandle aliceRemoteLine = alice.config.uri();

   // Attendant subscribes to the remote line
   SipBusyLampFieldRemoteLineSetSettings settings;
   settings.expires = 3600;
   SipBusyLampFieldRemoteLineSetHandle remoteLineSet = attendant.busyLampFieldManager->createBusyLampFieldRemoteLineSet(attendant.handle, settings);
   ASSERT_TRUE(remoteLineSet > 0);
   settings.allowedBargeInModes.push_back(BargeInMode_Normal);
   settings.allowedBargeInModes.push_back(BargeInMode_Whisper);
   settings.allowedBargeInModes.push_back(BargeInMode_Listen);
   assertSuccess(attendant.busyLampFieldManager->addRemoteLine(remoteLineSet, aliceRemoteLine));

   // Start the subscription
   assertSuccess(attendant.busyLampFieldManager->start(remoteLineSet));

   // Wait for the new subscription notification
   assertBLFRemoteLineNewSubscription(attendant, remoteLineSet, aliceRemoteLine);

   // Wait for the subscription state change notification
   assertBLFRemoteLineSubscriptionStateChanged(attendant, remoteLineSet, aliceRemoteLine, SipSubscriptionState_Active);

   // Wait for remote line (Alice) state change notification
   assertBLFRemoteLineStateChanged(attendant, remoteLineSet, aliceRemoteLine);

   // Bob calls Alice and stays in the 'confirmed' state
   SipConversationHandle bobCall;
   SipConversationHandle aliceCall;
   bobCall = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall, aliceRemoteLine);
   bob.conversation->start(bobCall);
   assertNewConversationIncoming(alice, &aliceCall, bob.config.uri());
   alice.conversation->sendRingingResponse(aliceCall);
   assertConversationStateChanged(alice, aliceCall, ConversationState_LocalRinging);
   alice.conversation->accept(aliceCall);
   assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
   assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
   assertNewConversationOutgoing(bob, bobCall, aliceRemoteLine);
   assertConversationStateChanged(bob, bobCall, ConversationState_RemoteRinging);
   assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
   assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

   // ===> Bob and Alice are now talking
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   // Wait for remote line (Alice) state change notification => Trying
   cpc::string aliceDialogId;
   assertBLFRemoteLineStateChanged_ex(attendant, remoteLineSet, aliceRemoteLine, DialogDirection_Recipient, DialogState_Trying, aliceRemoteLine, bob.config.uri(), aliceDialogId);

   // Wait for remote line (Alice) state change notification => Early
   assertBLFRemoteLineStateChanged_ex(attendant, remoteLineSet, aliceRemoteLine, DialogDirection_Recipient, DialogState_Early, aliceRemoteLine, bob.config.uri(), aliceDialogId);

   // Wait for remote line (Alice) state change notification => Confirmed
   assertBLFRemoteLineStateChanged_ex(attendant, remoteLineSet, aliceRemoteLine, DialogDirection_Recipient, DialogState_Confirmed, aliceRemoteLine, bob.config.uri(), aliceDialogId);

   // Join the call
   SipConversationHandle conversation;
   conversation = attendant.busyLampFieldManager->joinCall(remoteLineSet, aliceRemoteLine, aliceDialogId, MediaInfo(), BargeInMode_Normal, AlertMode_Normal);
   ASSERT_TRUE(conversation > 0);

   // Wait for Alice and Bob to join the conference
   assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendReceive);
   alice.conversation->accept(aliceCall);
   assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
      ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      ASSERT_EQ(MediaDirection_Inactive, evt.localMediaInfo[1].mediaDirection);
      });
   assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
   bob.conversation->accept(bobCall);
   assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
      ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      ASSERT_EQ(MediaDirection_Inactive, evt.localMediaInfo[1].mediaDirection);
      });

   assertNewConversationOutgoing(attendant, conversation, alice.config.uri());

   // Wait for Attendant to join the conference
   waitForConversationStateChanged(attendant, conversation, ConversationState_Connected);

   // ===> Attendant, Alice and Bob are now talking (in a conference)
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   // Terminate the call between Bob and the conference
   bob.conversation->end(bobCall);
   assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendReceive);
   alice.conversation->accept(aliceCall);
   assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
   assertBLFRemoteLineStateChanged(attendant, remoteLineSet, aliceRemoteLine);
   assertConversationMediaChanged(attendant, conversation, MediaDirection_SendReceive);

   // ===> Attendant and Alice are now talking (in a conference)
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   // Terminate the call between Alice and Attendant
   alice.conversation->end(aliceCall);
   assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   assertConversationEnded(attendant, conversation, ConversationEndReason_UserTerminatedRemotely);   

   // Terminate the subscription
   assertSuccess(attendant.busyLampFieldManager->end(remoteLineSet));

   // Wait for the subscription state to transition to Terminated
   assertBLFRemoteLineSubscriptionEnded(attendant, remoteLineSet, aliceRemoteLine);
}

// ALU26 - Subscribe to one remote line which accepts the incoming subscription. Joins the call on the remote line.
TEST_F(BusyLampFieldTest, JoinCallOnRemoteLine2)
{
   AttendantTestAccount attendant("attendant");
   MonitoredLineTestAccount alice("alice");
   StandardLineTestAccount bob("bob");

   SipBusyLampFieldRemoteLineHandle aliceRemoteLine = alice.config.uri();

   // Attendant subscribes to the remote line
   SipBusyLampFieldRemoteLineSetSettings settings;
   settings.expires = 3600;
   SipBusyLampFieldRemoteLineSetHandle remoteLineSet = attendant.busyLampFieldManager->createBusyLampFieldRemoteLineSet(attendant.handle, settings);
   ASSERT_TRUE(remoteLineSet > 0);
   settings.allowedBargeInModes.push_back(BargeInMode_Normal);
   settings.allowedBargeInModes.push_back(BargeInMode_Whisper);
   settings.allowedBargeInModes.push_back(BargeInMode_Listen);
   assertSuccess(attendant.busyLampFieldManager->addRemoteLine(remoteLineSet, aliceRemoteLine));

   // Start the subscription
   assertSuccess(attendant.busyLampFieldManager->start(remoteLineSet));

   // Wait for the new subscription notification
   assertBLFRemoteLineNewSubscription(attendant, remoteLineSet, aliceRemoteLine);

   // Wait for the subscription state change notification
   assertBLFRemoteLineSubscriptionStateChanged(attendant, remoteLineSet, aliceRemoteLine, SipSubscriptionState_Active);

   // Wait for remote line (Alice) state change notification
   assertBLFRemoteLineStateChanged(attendant, remoteLineSet, aliceRemoteLine);

   // Alice calls Bob and stays in the 'confirmed' state
   SipConversationHandle aliceCall;
   SipConversationHandle bobCall;
   aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);
   assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
   bob.conversation->sendRingingResponse(bobCall);
   assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
   bob.conversation->accept(bobCall);
   assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
   assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
   assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
   assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
   assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
   assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

   // ===> Bob and Alice are now talking
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   // Wait for remote line (Alice) state change notification => Trying
   cpc::string aliceDialogId;
   assertBLFRemoteLineStateChanged_ex(attendant, remoteLineSet, aliceRemoteLine, DialogDirection_Initiator, DialogState_Trying, aliceRemoteLine, bob.config.uri(), aliceDialogId);

   // Wait for remote line (Alice) state change notification => Early
   assertBLFRemoteLineStateChanged_ex(attendant, remoteLineSet, aliceRemoteLine, DialogDirection_Initiator, DialogState_Early, aliceRemoteLine, bob.config.uri(), aliceDialogId);

   // Wait for remote line (Alice) state change notification => Confirmed
   assertBLFRemoteLineStateChanged_ex(attendant, remoteLineSet, aliceRemoteLine, DialogDirection_Initiator, DialogState_Confirmed, aliceRemoteLine, bob.config.uri(), aliceDialogId);

   // Join the call
   SipConversationHandle conversation = attendant.busyLampFieldManager->joinCall(remoteLineSet, aliceRemoteLine, aliceDialogId, MediaInfo(), BargeInMode_Normal, AlertMode_Normal);
   ASSERT_TRUE(conversation > 0);

   // Wait for Alice and Bob to join the conference
   assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendReceive);
   alice.conversation->accept(aliceCall);
   assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
      ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
   assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
   bob.conversation->accept(bobCall);
   assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
      ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });

   // conference confirmed notification
   assertBLFRemoteLineStateChanged(attendant, remoteLineSet, aliceRemoteLine);

   // Wait for Attendant to join the conference
   assertNewConversationOutgoing(attendant, conversation, alice.config.uri());
   assertConversationMediaChanged(attendant, conversation, MediaDirection_SendReceive);
   assertConversationStateChanged(attendant, conversation, ConversationState_Connected);

   // idle notification, since attendant joined the conference
   assertBLFRemoteLineStateChanged(attendant, remoteLineSet, aliceRemoteLine);

   // ===> Attendant, Alice and Bob are now talking (in a conference)
   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   // Terminate the call between Attendant and the conference
   attendant.conversation->end(conversation);
   assertConversationEnded(attendant, conversation, ConversationEndReason_UserTerminatedLocally);

   // back to conference confirmed notification
   assertBLFRemoteLineStateChanged(attendant, remoteLineSet, aliceRemoteLine);
   // conference ended, alice and bob going back to regular call
   assertBLFRemoteLineStateChanged(attendant, remoteLineSet, aliceRemoteLine);

   assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendReceive);
   alice.conversation->accept(aliceCall);
   assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
      ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
   assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
      ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });

   // Wait for remote line (Alice) state change notification => Confirmed
   assertBLFRemoteLineStateChanged_ex(attendant, remoteLineSet, aliceRemoteLine, DialogDirection_Initiator, DialogState_Confirmed, aliceRemoteLine, bob.config.uri(), aliceDialogId);

   // ===> Bob and Alice are now talking
   std::this_thread::sleep_for(std::chrono::milliseconds(6000));

   // Join the call (again)
   SipConversationHandle conversation2 = attendant.busyLampFieldManager->joinCall(remoteLineSet, aliceRemoteLine, aliceDialogId, MediaInfo(), BargeInMode_Normal, AlertMode_Normal);
   ASSERT_TRUE(conversation2 > 0);

   // Wait for Alice and Bob to join the conference
   assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendReceive);
   alice.conversation->accept(aliceCall);
   assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
      ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
   });
   assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
   bob.conversation->accept(bobCall);
   assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
      ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
   });

   // conference confirmed notification
   assertBLFRemoteLineStateChanged(attendant, remoteLineSet, aliceRemoteLine);

   // Wait for Attendant to join the conference
   assertNewConversationOutgoing(attendant, conversation2, alice.config.uri());
   assertConversationMediaChanged(attendant, conversation2, MediaDirection_SendReceive);
   assertConversationStateChanged(attendant, conversation2, ConversationState_Connected);

   // idle notification, since attendant joined the conference
   assertBLFRemoteLineStateChanged(attendant, remoteLineSet, aliceRemoteLine);

   // ===> Attendant, Alice and Bob are now talking (in a conference)
   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   // Terminate the call between Attendant and the conference
   attendant.conversation->end(conversation2);
   assertConversationEnded(attendant, conversation2, ConversationEndReason_UserTerminatedLocally);

   // back to conference confirmed notification
   assertBLFRemoteLineStateChanged(attendant, remoteLineSet, aliceRemoteLine);
   // conference ended, alice and bob going back to regular call
   assertBLFRemoteLineStateChanged(attendant, remoteLineSet, aliceRemoteLine);

   assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendReceive);
   alice.conversation->accept(aliceCall);
   assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
      ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
   });
   assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
      ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
   });
   
   // ===> Bob and Alice are now talking
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));


   // Terminate the call between Bob and Alice
   bob.conversation->end(bobCall);
   assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);   

   // Wait for remote line (Alice) state change notification => Terminated
   assertBLFRemoteLineStateChanged_ex(attendant, remoteLineSet, aliceRemoteLine, DialogDirection_Initiator, DialogState_Confirmed, alice.config.uri(), bob.config.uri(), aliceDialogId);
   assertBLFRemoteLineStateChanged_ex(attendant, remoteLineSet, aliceRemoteLine, DialogDirection_Initiator, DialogState_Terminated, alice.config.uri(), bob.config.uri(), aliceDialogId);

   // Terminate the subscription
   assertSuccess(attendant.busyLampFieldManager->end(remoteLineSet));

   // Wait for the subscription state to transition to Terminated
   assertBLFRemoteLineSubscriptionEnded(attendant, remoteLineSet, aliceRemoteLine);
}

// Barge in mode with Listen and Whisper modes
// Commented out until implemented on ALU servers
/*TEST_F(BusyLampFieldTest, JoinCallOnRemoteLine2ListenMode)
{
   AttendantTestAccount attendant("attendant");
   MonitoredLineTestAccount alice("alice");
   StandardLineTestAccount bob("bob");

   SipBusyLampFieldRemoteLineHandle aliceRemoteLine = alice.config.uri();

   // Attendant subscribes to the remote line
   SipBusyLampFieldRemoteLineSetSettings settings;
   settings.expires = 3600;
   SipBusyLampFieldRemoteLineSetHandle remoteLineSet = attendant.busyLampFieldManager->createBusyLampFieldRemoteLineSet(attendant.handle, settings);
   ASSERT_TRUE(remoteLineSet > 0);
   settings.allowedBargeInModes.push_back(BargeInMode_Normal);
   settings.allowedBargeInModes.push_back(BargeInMode_Whisper);
   settings.allowedBargeInModes.push_back(BargeInMode_Listen);
   assertSuccess(attendant.busyLampFieldManager->addRemoteLine(remoteLineSet, aliceRemoteLine));

   // Start the subscription
   assertSuccess(attendant.busyLampFieldManager->start(remoteLineSet));

   // Wait for the new subscription notification
   assertBLFRemoteLineNewSubscription(attendant, remoteLineSet, aliceRemoteLine);

   // Wait for the subscription state change notification
   assertBLFRemoteLineSubscriptionStateChanged(attendant, remoteLineSet, aliceRemoteLine, SipSubscriptionState_Active);

   // Wait for remote line (Alice) state change notification
   assertBLFRemoteLineStateChanged(attendant, remoteLineSet, aliceRemoteLine);

   // Alice calls Bob and stays in the 'confirmed' state
   SipConversationHandle aliceCall;
   SipConversationHandle bobCall;
   aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);
   assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
   bob.conversation->sendRingingResponse(bobCall);
   assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
   bob.conversation->accept(bobCall);
   assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
   assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
   assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
   assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
   assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
   assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

   // ===> Bob and Alice are now talking
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   // Wait for remote line (Alice) state change notification => Trying
   cpc::string aliceDialogId;
   assertBLFRemoteLineStateChanged_ex(attendant, remoteLineSet, aliceRemoteLine, DialogDirection_Initiator, DialogState_Trying, aliceRemoteLine, bob.config.uri(), aliceDialogId);

   // Wait for remote line (Alice) state change notification => Early
   assertBLFRemoteLineStateChanged_ex(attendant, remoteLineSet, aliceRemoteLine, DialogDirection_Initiator, DialogState_Early, aliceRemoteLine, bob.config.uri(), aliceDialogId);

   // Wait for remote line (Alice) state change notification => Confirmed
   assertBLFRemoteLineStateChanged_ex(attendant, remoteLineSet, aliceRemoteLine, DialogDirection_Initiator, DialogState_Confirmed, aliceRemoteLine, bob.config.uri(), aliceDialogId);

   // Join the call
   SipConversationHandle conversation = attendant.busyLampFieldManager->joinCall(remoteLineSet, aliceRemoteLine, aliceDialogId, BargeInMode_Listen, AlertMode_Normal);
   ASSERT_TRUE(conversation > 0);

   // Wait for Alice and Bob to join the conference
   assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendReceive);
   alice.conversation->accept(aliceCall);
   assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
      ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      ASSERT_EQ(MediaDirection_Inactive, evt.localMediaInfo[1].mediaDirection);
   });
   assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
   bob.conversation->accept(bobCall);
   assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
      ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      ASSERT_EQ(MediaDirection_Inactive, evt.localMediaInfo[1].mediaDirection);
   });

   // conference confirmed notification
   assertBLFRemoteLineStateChanged(attendant, remoteLineSet, aliceRemoteLine);

   // Wait for Attendant to join the conference
   assertNewConversationOutgoing(attendant, conversation, alice.config.uri());
   assertConversationMediaChanged(attendant, conversation, MediaDirection_SendReceive);
   assertConversationStateChanged(attendant, conversation, ConversationState_Connected);

   // idle notification, since attendant joined the conference
   assertBLFRemoteLineStateChanged(attendant, remoteLineSet, aliceRemoteLine);

   // ===> Attendant, Alice and Bob are now talking (in a conference)
   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   // Terminate the call between Attendant and the conference
   attendant.conversation->end(conversation);
   assertConversationEnded(attendant, conversation, ConversationEndReason_UserTerminatedLocally);

   // back to conference confirmed notification
   assertBLFRemoteLineStateChanged(attendant, remoteLineSet, aliceRemoteLine);
   // conference ended, alice and bob going back to regular call
   assertBLFRemoteLineStateChanged(attendant, remoteLineSet, aliceRemoteLine);

   assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendReceive);
   alice.conversation->accept(aliceCall);
   assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
      ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      ASSERT_EQ(MediaDirection_Inactive, evt.localMediaInfo[1].mediaDirection);
   });
   assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
      ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      ASSERT_EQ(MediaDirection_Inactive, evt.localMediaInfo[1].mediaDirection);
   });

   // ===> Bob and Alice are now talking (in a conference)
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   // Terminate the call between Bob and Alice
   bob.conversation->end(bobCall);
   assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);   

   // Wait for remote line (Alice) state change notification => Terminated
   assertBLFRemoteLineStateChanged_ex(attendant, remoteLineSet, aliceRemoteLine, DialogDirection_Initiator, DialogState_Confirmed, alice.config.uri(), bob.config.uri(), aliceDialogId);
   assertBLFRemoteLineStateChanged_ex(attendant, remoteLineSet, aliceRemoteLine, DialogDirection_Initiator, DialogState_Terminated, alice.config.uri(), bob.config.uri(), aliceDialogId);

   // Terminate the subscription
   assertSuccess(attendant.busyLampFieldManager->end(remoteLineSet));

   // Wait for the subscription state to transition to Terminated
   assertBLFRemoteLineSubscriptionEnded(attendant, remoteLineSet, aliceRemoteLine);
}

// ALU26 - Subscribe to one remote line which accepts the incoming subscription. Joins the call on the remote line.
TEST_F(BusyLampFieldTest, JoinCallOnRemoteLine2WhisperMode)
{
   AttendantTestAccount attendant("attendant");
   MonitoredLineTestAccount alice("alice");
   StandardLineTestAccount bob("bob");
   SipConversationSettings convSettings;
   convSettings.includePAssertedIdentity = true;
   attendant.conversation->setDefaultSettings(attendant.handle, convSettings);

   SipBusyLampFieldRemoteLineHandle aliceRemoteLine = alice.config.uri();

   // Attendant subscribes to the remote line
   SipBusyLampFieldRemoteLineSetSettings settings;
   settings.expires = 3600;
   SipBusyLampFieldRemoteLineSetHandle remoteLineSet = attendant.busyLampFieldManager->createBusyLampFieldRemoteLineSet(attendant.handle, settings);
   ASSERT_TRUE(remoteLineSet > 0);
   settings.allowedBargeInModes.push_back(BargeInMode_Normal);
   settings.allowedBargeInModes.push_back(BargeInMode_Whisper);
   settings.allowedBargeInModes.push_back(BargeInMode_Listen);
   assertSuccess(attendant.busyLampFieldManager->addRemoteLine(remoteLineSet, aliceRemoteLine));

   // Start the subscription
   assertSuccess(attendant.busyLampFieldManager->start(remoteLineSet));

   // Wait for the new subscription notification
   assertBLFRemoteLineNewSubscription(attendant, remoteLineSet, aliceRemoteLine);

   // Wait for the subscription state change notification
   assertBLFRemoteLineSubscriptionStateChanged(attendant, remoteLineSet, aliceRemoteLine, SipSubscriptionState_Active);

   // Wait for remote line (Alice) state change notification
   assertBLFRemoteLineStateChanged(attendant, remoteLineSet, aliceRemoteLine);

   // Alice calls Bob and stays in the 'confirmed' state
   SipConversationHandle aliceCall;
   SipConversationHandle bobCall;
   aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);
   assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
   bob.conversation->sendRingingResponse(bobCall);
   assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
   bob.conversation->accept(bobCall);
   assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
   assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
   assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
   assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
   assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
   assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

   // ===> Bob and Alice are now talking
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   // Wait for remote line (Alice) state change notification => Trying
   cpc::string aliceDialogId;
   assertBLFRemoteLineStateChanged_ex(attendant, remoteLineSet, aliceRemoteLine, DialogDirection_Initiator, DialogState_Trying, aliceRemoteLine, bob.config.uri(), aliceDialogId);

   // Wait for remote line (Alice) state change notification => Early
   assertBLFRemoteLineStateChanged_ex(attendant, remoteLineSet, aliceRemoteLine, DialogDirection_Initiator, DialogState_Early, aliceRemoteLine, bob.config.uri(), aliceDialogId);

   // Wait for remote line (Alice) state change notification => Confirmed
   assertBLFRemoteLineStateChanged_ex(attendant, remoteLineSet, aliceRemoteLine, DialogDirection_Initiator, DialogState_Confirmed, aliceRemoteLine, bob.config.uri(), aliceDialogId);

   // Join the call
   SipConversationHandle conversation = attendant.busyLampFieldManager->joinCall(remoteLineSet, aliceRemoteLine, aliceDialogId, BargeInMode_Whisper, AlertMode_Normal);
   ASSERT_TRUE(conversation > 0);

   // Wait for Alice and Bob to join the conference
   assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendReceive);
   alice.conversation->accept(aliceCall);
   assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
      ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      ASSERT_EQ(MediaDirection_Inactive, evt.localMediaInfo[1].mediaDirection);
   });
   assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
   bob.conversation->accept(bobCall);
   assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
      ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      ASSERT_EQ(MediaDirection_Inactive, evt.localMediaInfo[1].mediaDirection);
   });

   // conference confirmed notification
   assertBLFRemoteLineStateChanged(attendant, remoteLineSet, aliceRemoteLine);

   // Wait for Attendant to join the conference
   assertNewConversationOutgoing(attendant, conversation, alice.config.uri());
   assertConversationMediaChanged(attendant, conversation, MediaDirection_SendReceive);
   assertConversationStateChanged(attendant, conversation, ConversationState_Connected);

   // idle notification, since attendant joined the conference
   assertBLFRemoteLineStateChanged(attendant, remoteLineSet, aliceRemoteLine);

   // ===> Attendant, Alice and Bob are now talking (in a conference)
   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   // Terminate the call between Attendant and the conference
   attendant.conversation->end(conversation);
   assertConversationEnded(attendant, conversation, ConversationEndReason_UserTerminatedLocally);

   // back to conference confirmed notification
   assertBLFRemoteLineStateChanged(attendant, remoteLineSet, aliceRemoteLine);
   // conference ended, alice and bob going back to regular call
   assertBLFRemoteLineStateChanged(attendant, remoteLineSet, aliceRemoteLine);

   assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendReceive);
   alice.conversation->accept(aliceCall);
   assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
      ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      ASSERT_EQ(MediaDirection_Inactive, evt.localMediaInfo[1].mediaDirection);
   });
   assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
      ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      ASSERT_EQ(MediaDirection_Inactive, evt.localMediaInfo[1].mediaDirection);
   });

   // ===> Bob and Alice are now talking (in a conference)
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   // Terminate the call between Bob and Alice
   bob.conversation->end(bobCall);
   assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);   

   // Wait for remote line (Alice) state change notification => Terminated
   assertBLFRemoteLineStateChanged_ex(attendant, remoteLineSet, aliceRemoteLine, DialogDirection_Initiator, DialogState_Confirmed, alice.config.uri(), bob.config.uri(), aliceDialogId);
   assertBLFRemoteLineStateChanged_ex(attendant, remoteLineSet, aliceRemoteLine, DialogDirection_Initiator, DialogState_Terminated, alice.config.uri(), bob.config.uri(), aliceDialogId);

   // Terminate the subscription
   assertSuccess(attendant.busyLampFieldManager->end(remoteLineSet));

   // Wait for the subscription state to transition to Terminated
   assertBLFRemoteLineSubscriptionEnded(attendant, remoteLineSet, aliceRemoteLine);
}
*/

void BusyLampFieldTest::expectPhoneError(int line, TestAccount& account, const cpc::string& errorText)
{
   PhoneErrorEvent evt;
   cpc::string module;
   ASSERT_TRUE(account.biEvents->expectEvent(line,
      "PhoneHandler::onError",
      15000, StrEqualsPred("SipAccountInterface"), module, evt));
   ASSERT_EQ(errorText, evt.errorText);
}

void BusyLampFieldTest::expectDialogEventNewSubscription(int line, TestAccount& account, const cpc::string& remoteAddress, SipDialogEventSubscriptionHandle& subscription)
{
   SipDialogEventSubscriptionHandle h;
	NewDialogEventSubscriptionEvent evt;
	ASSERT_TRUE(account.subsEvents->expectEvent(line,
		"SipDialogEventSubscriptionHandler::onNewSubscription",
		5000, AlwaysTruePred(), h, evt));
   ASSERT_EQ(account.handle, evt.account);
   subscription = h;
	ASSERT_EQ(SipSubscriptionType_Incoming, evt.subscriptionType);
   ASSERT_EQ(remoteAddress, evt.remoteAddress);
}

void BusyLampFieldTest::expectDialogEventSubscriptionEnded(int line, TestAccount& account, SipDialogEventSubscriptionHandle subscription)
{
	SipDialogEventSubscriptionHandle h;
	DialogEventSubscriptionEndedEvent evt;
	ASSERT_TRUE(account.subsEvents->expectEvent(line,
		"SipDialogEventSubscriptionHandler::onSubscriptionEnded",
		5000,
      AlwaysTruePred(), h, evt));
	ASSERT_EQ(subscription, h);
	ASSERT_EQ(SipSubscriptionEndReason_ServerEnded, evt.endReason);
}

void BusyLampFieldTest::expectDialogEventSubscriptionStateChanged(int line, TestAccount& account, SipDialogEventSubscriptionHandle subscription, SipSubscriptionState subscriptionState)
{
	SipDialogEventSubscriptionHandle h;
	DialogEventSubscriptionStateChangedEvent evt;
	ASSERT_TRUE(account.subsEvents->expectEvent(line,
		"SipDialogEventSubscriptionHandler::onSubscriptionStateChanged",
		15000,
		AlwaysTruePred(), h, evt));
	ASSERT_EQ(subscription, h);
   ASSERT_EQ(subscriptionState, evt.subscriptionState);
}

void BusyLampFieldTest::expectBLFError(int line, AttendantTestAccount& account, SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const cpc::string& errorText)
{
   SipBusyLampFieldRemoteLineSetHandle h;
   SipBusyLampField::ErrorEvent evt;
   ASSERT_TRUE(account.busyLampFieldEvents->expectEvent(line,
      "SipBusyLampFieldHandler::onError",
      5000, AlwaysTruePred(), h, evt));
   ASSERT_EQ(remoteLineSet, h);
	ASSERT_EQ(errorText, evt.errorText);
}

void BusyLampFieldTest::expectBLFRemoteLineNewSubscription(int line, AttendantTestAccount& account, SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const SipBusyLampFieldRemoteLineHandle& remoteLine)
{
	SipBusyLampFieldRemoteLineSetHandle h;
	RemoteLineNewSubscriptionEvent evt;
   ASSERT_TRUE(account.busyLampFieldEvents->expectEvent(line,
		"SipBusyLampFieldHandler::onRemoteLineNewSubscription",
		15000,
		AlwaysTruePred(), h, evt));
   ASSERT_EQ(remoteLineSet, h);
	ASSERT_EQ(remoteLine, evt.remoteLine);
}

void BusyLampFieldTest::expectBLFRemoteLineState(int line, AttendantTestAccount& account, SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const SipBusyLampFieldRemoteLineHandle& remoteLine, bool subscriptionStarted, SipSubscriptionState subscriptionState, DialogState dialogState)
{
   SipBusyLampFieldRemoteLineState remoteLineState;
   assertSuccess(account.busyLampFieldStateManager->getState(remoteLineSet, remoteLine, remoteLineState));
   ASSERT_EQ(remoteLine, remoteLineState.remoteLine);
   ASSERT_EQ(subscriptionStarted, remoteLineState.subscriptionStarted);
   ASSERT_EQ(subscriptionState, remoteLineState.subscriptionState);
   if (dialogState != DialogState_NotSpecified)
   {
      ASSERT_EQ(remoteLineState.calls.size(), 1);
      ASSERT_EQ(dialogState, remoteLineState.calls[0].dialog.stateInfo.state);
   }
}

void BusyLampFieldTest::expectBLFRemoteLineStateChanged(int line, AttendantTestAccount& account, SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const SipBusyLampFieldRemoteLineHandle& remoteLine, DialogDirection direction, DialogState dialogState, const cpc::string& localAddress, const cpc::string& remoteAddress, cpc::string& dialogId)
{
   SipBusyLampFieldRemoteLineSetHandle h;
   RemoteLineStateChangedEvent evt;
   ASSERT_TRUE(account.busyLampFieldEvents->expectEvent(line,
      "SipBusyLampFieldHandler::onRemoteLineStateChanged",
		5000, AlwaysTruePred(), h, evt));
   ASSERT_EQ(remoteLineSet, h);
   ASSERT_EQ(remoteLine, evt.remoteLine);
 
   if (direction != DialogDirection_NotSpecified)
   {
      ASSERT_EQ(evt.calls.size(), 1);
      SipDialogEvent::DialogInfo dialog = evt.calls[0].dialog;
      dialogId = dialog.id;
      ASSERT_EQ(direction, dialog.direction);
      ASSERT_EQ(dialogState, dialog.stateInfo.state);
      ASSERT_EQ(0, dialog.stateInfo.code);
      if (dialogState == DialogState_Trying)
      {
         ASSERT_FALSE(dialog.dialogId.callId.empty());
         ASSERT_FALSE(dialog.dialogId.localTag.empty());
      }
      else if (dialogState == DialogState_Early || dialogState == DialogState_Confirmed || dialogState == DialogState_Terminated)
      {
         ASSERT_FALSE(dialog.dialogId.callId.empty());
         ASSERT_FALSE(dialog.dialogId.localTag.empty());
         ASSERT_FALSE(dialog.dialogId.remoteTag.empty());
      }
      ASSERT_EQ(localAddress, dialog.localParticipant.identity.address);
      if (!cpc::string(remoteAddress).empty())
      {
         ASSERT_EQ(remoteAddress, dialog.remoteParticipant.identity.address);
      }
   }
}

void BusyLampFieldTest::expectBLFRemoteLineSubscriptionStateChanged(int line, AttendantTestAccount& account, SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const SipBusyLampFieldRemoteLineHandle& remoteLine, SipSubscriptionState subscriptionState)
{
	SipBusyLampFieldRemoteLineSetHandle h;
   RemoteLineSubscriptionStateChangedEvent evt;
   ASSERT_TRUE(account.busyLampFieldEvents->expectEvent(line,
		"SipBusyLampFieldHandler::onRemoteLineSubscriptionStateChanged",
		15000,
		AlwaysTruePred(), h, evt));
   ASSERT_EQ(remoteLineSet, h);
	ASSERT_EQ(remoteLine, evt.remoteLine);
   ASSERT_EQ(subscriptionState, evt.subscriptionState);
}

void BusyLampFieldTest::expectBLFRemoteLineSubscriptionEnded(int line, AttendantTestAccount& account, SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const SipBusyLampFieldRemoteLineHandle& remoteLine)
{
  	SipBusyLampFieldRemoteLineSetHandle h;
	RemoteLineSubscriptionEndedEvent evt;
   ASSERT_TRUE(account.busyLampFieldEvents->expectEvent(line,
		"SipBusyLampFieldHandler::onRemoteLineSubscriptionEnded",
		15000,
		AlwaysTruePred(), h, evt));
   ASSERT_EQ(remoteLineSet, h);
   ASSERT_EQ(remoteLine, evt.remoteLine);
	ASSERT_EQ(SipSubscriptionEndReason_ServerEnded, evt.endReason);
}

void BusyLampFieldTest::waitForConversationStateChanged_(int line, TestAccount& account, SipConversationHandle conversation, ConversationState conversationState)
{
   while(true)
   {
      SipConversationHandle h;
      ConversationStateChangedEvent evt;
      ASSERT_TRUE(account.conversationEvents->waitForEvent(line,
            "SipConversationHandler::onConversationStateChanged",
            5000, HandleEqualsPred<SipConversationHandle>(conversation), h, evt));
      ASSERT_EQ(h, conversation);
      if (evt.conversationState == conversationState)
      {
         break;
      }
   }
}

}

#endif // CPCAPI2_BRAND_SIP_BUSY_LAMP_FIELD_MODULE
