#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#if (CPCAPI2_BRAND_SIP_BUSY_LAMP_FIELD_MODULE == 1)

#include "cpcapi2_test_fixture.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>

#include "test_framework/cpcapi2_test_framework.h"
#include "test_framework/bare_bones_sip_endpoint.h"
#include "test_account_events.h"
#include "test_call_events.h"

#include <thread>
#include <future>

using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::SipEvent;
using namespace CPCAPI2::SipConversation;
using namespace CPCAPI2::SipDialogEvent;
using namespace CPCAPI2::SipBusyLampField;

namespace {

const cpc::string BROADSOFT_BLF_RESOURCE_LIST_URI = "sip:<EMAIL>";

class AttendantTestAccount : public TestAccount
{
public:
   AttendantTestAccount(const std::string& name, bool disableOnDestruct = true) : TestAccount(name, Account_NoInit, disableOnDestruct)
   {
      if (name == "attendant")
      {
         config.settings.displayName = "8195";
         config.settings.username = "**********";
         config.settings.auth_username = "counterpathuser15";
         config.settings.domain = "as.iop1.broadworks.net";
         config.settings.outboundProxy = "************";
         config.settings.password = "8Ik3020713J9OXZ97zcTS0F7FQ79iK9B7N3G2UX5";
         config.settings.registrationIntervalSeconds = 3600;
         config.settings.useRport = false;
      }
      else
      {
         assert(false);
      }

      init();

      busyLampFieldManager = SipBusyLampFieldManager::getInterface(phone);
      busyLampFieldManager->setHandler(handle, (SipBusyLampFieldHandler*) 0xDEADBEEF);
      busyLampFieldEvents = new test::EventHandler(name.c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(busyLampFieldManager));
      busyLampFieldStateManager = SipBusyLampFieldStateManager::getInterface(busyLampFieldManager);

      enable();
   }

   SipBusyLampFieldManager* busyLampFieldManager;
   CPCAPI2::test::EventHandler* busyLampFieldEvents;
   SipBusyLampFieldStateManager* busyLampFieldStateManager;
};

class MonitoredLineTestAccount : public TestAccount
{
public:
   MonitoredLineTestAccount(const std::string& name) : TestAccount(name, Account_NoInit)
   {
      if (name == "alice")
      {
         config.settings.displayName = "8196";
         config.settings.username = "**********";
         config.settings.auth_username = "counterpathuser16";         
         config.settings.password = "01X36wTH62L01M2v1MWcP316M6W06ZBA9JQ3q2O3";
         
      }
      else if (name == "bob")
      {
         config.settings.displayName = "8197";
         config.settings.username = "**********";
         config.settings.auth_username = "counterpathuser17";
         config.settings.password = "h73A96C7425Z91H6Q1Ta89dDJC97R87NF3CMiEv8";
      }
      else if (name == "charlie")
      {
         config.settings.displayName = "8198";
         config.settings.username = "**********";
         config.settings.auth_username = "counterpathuser18";         
         config.settings.password = "608Q371G0a5VUG7F7Hj3X7nKEW4G726b915BHlV7";
      }
      else
      {
         assert(false);
      }
      config.settings.useRport = false;
      config.settings.domain = "as.iop1.broadworks.net";
      config.settings.outboundProxy = "************";
      config.settings.registrationIntervalSeconds = 3600;

      blfLineHandle = "sip:" + config.settings.auth_username + "@" + config.settings.domain;

      init();
      enable();
   }

   SipBusyLampFieldRemoteLineHandle blfLineHandle;
};

class StandardLineTestAccount : public TestAccount
{
public:
   StandardLineTestAccount(const std::string& name) : TestAccount(name, Account_NoInit)
   {
      if (name == "dave")
      {
         config.settings.displayName = "9198";
         config.settings.username = "**********";
         config.settings.auth_username = "counterpathuser8";
         config.settings.domain = "as.iop1.broadworks.net";
         config.settings.outboundProxy = "************";
         config.settings.password = "W92JW44M10T5fD0KatW827Iq1743DFD9Z72c70kQ";
         config.settings.registrationIntervalSeconds = 3600;
         config.settings.useRport = false;
      }
      else
      {
         assert(false);
      }

      init();
      enable();
   }
};

#define assertPhoneError(account, errorText) \
   BroadsoftBlfTest::expectPhoneError(__LINE__, account, errorText) 
#define assertBLFError(account, remoteLineSet, errorText) \
   BroadsoftBlfTest::expectBLFError(__LINE__, account, remoteLineSet, errorText)
#define assertBLFRemoteLineNewSubscription(account, remoteLineSet, remoteLine) \
   BroadsoftBlfTest::expectBLFRemoteLineNewSubscription(__LINE__, account, remoteLineSet, remoteLine)
#define assertBLFRemoteLineState(account, remoteLineSet, remoteLine, subscriptionStarted, subscriptionState) \
   BroadsoftBlfTest::expectBLFRemoteLineState(__LINE__, account, remoteLineSet, remoteLine, subscriptionStarted, subscriptionState, DialogState_NotSpecified)
#define assertBLFRemoteLineState_ex(account, remoteLineSet, remoteLine, subscriptionStarted, subscriptionState, dialogState) \
   BroadsoftBlfTest::expectBLFRemoteLineState(__LINE__, account, remoteLineSet, remoteLine, subscriptionStarted, subscriptionState, dialogState)
#define assertBLFRemoteLineStateChanged(account, remoteLineSet, remoteLine) \
   BroadsoftBlfTest::expectBLFRemoteLineStateChanged(__LINE__, account, remoteLineSet, remoteLine, DialogDirection_NotSpecified, DialogState_NotSpecified, "", "", cpc::string())
#define assertBLFRemoteLineStateChanged_ex(account, remoteLineSet, remoteLine, direction, dialogState, localAddress, remoteAddress, dialogId) \
   BroadsoftBlfTest::expectBLFRemoteLineStateChanged(__LINE__, account, remoteLineSet, remoteLine, direction, dialogState, localAddress, remoteAddress, dialogId)
#define assertBLFRemoteLineSubscriptionStateChanged(account, remoteLineSet, remoteLine, subscriptionState) \
   BroadsoftBlfTest::expectBLFRemoteLineSubscriptionStateChanged(__LINE__, account, remoteLineSet, remoteLine, subscriptionState)
#define assertBLFRemoteLineSubscriptionEnded(account, remoteLineSet, remoteLine) \
   BroadsoftBlfTest::expectBLFRemoteLineSubscriptionEnded(__LINE__, account, remoteLineSet, remoteLine)
#define waitForConversationStateChanged(account, conversation, conversationState) \
   BroadsoftBlfTest::waitForConversationStateChanged_(__LINE__, account, conversation, conversationState)

class BroadsoftBlfTest : public CpcapiAutoTest
{
public:
   BroadsoftBlfTest() {}
   virtual ~BroadsoftBlfTest() {}

   static void expectPhoneError(int line, TestAccount& account, const cpc::string& errorText);
   static void expectBLFError(int line, AttendantTestAccount& account, SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const cpc::string& errorText);
   static void expectBLFRemoteLineNewSubscription(int line, AttendantTestAccount& account, SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const SipBusyLampFieldRemoteLineHandle& remoteLine);
   static void expectBLFRemoteLineState(int line, AttendantTestAccount& account, SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const SipBusyLampFieldRemoteLineHandle& remoteLine, bool subscriptionStarted, SipSubscriptionState subscriptionState, DialogState dialogState);
   static void expectBLFRemoteLineStateChanged(int line, AttendantTestAccount& account, SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const SipBusyLampFieldRemoteLineHandle& remoteLine, DialogDirection direction, DialogState dialogState, const cpc::string& localAddress, const cpc::string& remoteAddress, cpc::string& dialogId);
   static void expectBLFRemoteLineSubscriptionStateChanged(int line, AttendantTestAccount& account, SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const SipBusyLampFieldRemoteLineHandle& remoteLine, SipSubscriptionState subscriptionState);
   static void expectBLFRemoteLineSubscriptionEnded(int line, AttendantTestAccount& account, SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const SipBusyLampFieldRemoteLineHandle& remoteLine);
   static void waitForConversationStateChanged_(int line, TestAccount& account, SipConversationHandle conversation, ConversationState conversationState);
};


// Attempt to manually add remote line to a set that is configured for RLS subscription
TEST_F(BroadsoftBlfTest, AddRemoteLineRlsMode)
{
   AttendantTestAccount attendant("attendant");
   SipBusyLampFieldRemoteLineSetSettings settings;
   settings.expires = 3600;
   settings.resourceListAddress = BROADSOFT_BLF_RESOURCE_LIST_URI;
   SipBusyLampFieldRemoteLineSetHandle remoteLineSet = attendant.busyLampFieldManager->createBusyLampFieldRemoteLineSet(attendant.handle, settings);
   ASSERT_TRUE(remoteLineSet > 0);
   attendant.busyLampFieldManager->addRemoteLine(remoteLineSet, "sip:<EMAIL>");

   assertBLFError(attendant, remoteLineSet, "Cannot add remote lines when resource list URI is configured.");
}

TEST_F(BroadsoftBlfTest, SubscribeRlsAccepted)
{
   AttendantTestAccount attendant("attendant");
   MonitoredLineTestAccount alice("alice");
   MonitoredLineTestAccount bob("bob");
   MonitoredLineTestAccount charlie("charlie");

   SipBusyLampFieldRemoteLineHandle aliceRemoteLine = alice.blfLineHandle;
   SipBusyLampFieldRemoteLineHandle bobRemoteLine = bob.blfLineHandle;
   SipBusyLampFieldRemoteLineHandle charlieRemoteLine = charlie.blfLineHandle;

   // Attendant subscribes to the remote line
   SipBusyLampFieldRemoteLineSetSettings settings;
   settings.expires = 3600;
   settings.resourceListAddress = BROADSOFT_BLF_RESOURCE_LIST_URI;
   SipBusyLampFieldRemoteLineSetHandle remoteLineSet = attendant.busyLampFieldManager->createBusyLampFieldRemoteLineSet(attendant.handle, settings);
   ASSERT_TRUE(remoteLineSet > 0);

   // Start the subscription
   assertSuccess(attendant.busyLampFieldManager->start(remoteLineSet));
   
   // Wait for the new subscription notification
   assertBLFRemoteLineNewSubscription(attendant, remoteLineSet, aliceRemoteLine);
   // Validate the state of the remote line
   assertBLFRemoteLineState(attendant, remoteLineSet, aliceRemoteLine, true, SipSubscriptionState_Pending);
   // Wait for the subscription state change notification
   assertBLFRemoteLineSubscriptionStateChanged(attendant, remoteLineSet, aliceRemoteLine, SipSubscriptionState_Active);

   assertBLFRemoteLineNewSubscription(attendant, remoteLineSet, bobRemoteLine);
   // Validate the state of the remote line
   assertBLFRemoteLineState(attendant, remoteLineSet, bobRemoteLine, true, SipSubscriptionState_Pending);
   // Wait for the subscription state change notification
   assertBLFRemoteLineSubscriptionStateChanged(attendant, remoteLineSet, bobRemoteLine, SipSubscriptionState_Active);

   assertBLFRemoteLineNewSubscription(attendant, remoteLineSet, charlieRemoteLine);
   // Validate the state of the remote line
   assertBLFRemoteLineState(attendant, remoteLineSet, charlieRemoteLine, true, SipSubscriptionState_Pending);
   // Wait for the subscription state change notification
   assertBLFRemoteLineSubscriptionStateChanged(attendant, remoteLineSet, charlieRemoteLine, SipSubscriptionState_Active);

   // Wait for line state change notifications
   assertBLFRemoteLineStateChanged(attendant, remoteLineSet, aliceRemoteLine);
   assertBLFRemoteLineStateChanged(attendant, remoteLineSet, bobRemoteLine);
   assertBLFRemoteLineStateChanged(attendant, remoteLineSet, charlieRemoteLine);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   // Terminate subscription
   assertSuccess(attendant.busyLampFieldManager->end(remoteLineSet));

   // Wait for the subscription termination notification
   assertBLFRemoteLineSubscriptionEnded(attendant, remoteLineSet, aliceRemoteLine);
   assertBLFRemoteLineSubscriptionEnded(attendant, remoteLineSet, bobRemoteLine);
   assertBLFRemoteLineSubscriptionEnded(attendant, remoteLineSet, charlieRemoteLine);
}


//  Dialog state changes for monitoring an outgoing call
TEST_F(BroadsoftBlfTest, OutgoingCall)
{
   AttendantTestAccount attendant("attendant");
   MonitoredLineTestAccount alice("alice");
   MonitoredLineTestAccount bob("bob");
   MonitoredLineTestAccount charlie("charlie");
   StandardLineTestAccount dave("dave");

   SipBusyLampFieldRemoteLineHandle aliceRemoteLine = alice.blfLineHandle;
   SipBusyLampFieldRemoteLineHandle bobRemoteLine = bob.blfLineHandle;
   SipBusyLampFieldRemoteLineHandle charlieRemoteLine = charlie.blfLineHandle;

   // Attendant subscribes to the remote line
   SipBusyLampFieldRemoteLineSetSettings settings;
   settings.expires = 3600;
   settings.resourceListAddress = BROADSOFT_BLF_RESOURCE_LIST_URI;
   SipBusyLampFieldRemoteLineSetHandle remoteLineSet = attendant.busyLampFieldManager->createBusyLampFieldRemoteLineSet(attendant.handle, settings);
   ASSERT_TRUE(remoteLineSet > 0);

   // Start the subscription
   assertSuccess(attendant.busyLampFieldManager->start(remoteLineSet));

   // BLF events -> new subscription -> active subscription -> dialog state idle
   assertBLFRemoteLineNewSubscription(attendant, remoteLineSet, aliceRemoteLine);
   assertBLFRemoteLineSubscriptionStateChanged(attendant, remoteLineSet, aliceRemoteLine, SipSubscriptionState_Active);
   assertBLFRemoteLineStateChanged(attendant, remoteLineSet, aliceRemoteLine);

   assertBLFRemoteLineNewSubscription(attendant, remoteLineSet, bobRemoteLine);
   assertBLFRemoteLineSubscriptionStateChanged(attendant, remoteLineSet, bobRemoteLine, SipSubscriptionState_Active);
   assertBLFRemoteLineStateChanged(attendant, remoteLineSet, bobRemoteLine);

   assertBLFRemoteLineNewSubscription(attendant, remoteLineSet, charlieRemoteLine);
   assertBLFRemoteLineSubscriptionStateChanged(attendant, remoteLineSet, charlieRemoteLine, SipSubscriptionState_Active);
   assertBLFRemoteLineStateChanged(attendant, remoteLineSet, charlieRemoteLine);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   // Alice calls dave
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, dave.config.uri());
   alice.conversation->start(aliceCall);
   assertNewConversationOutgoing(alice, aliceCall, dave.config.uri());
   SipConversationHandle daveCall;
   {
      SipConversationHandle h;
      NewConversationEvent evt;
      ASSERT_TRUE(cpcExpectEvent(dave.conversationEvents,
         "SipConversationHandler::onNewConversation",
         10000,
         AlwaysTruePred(),
         h, evt));
      daveCall = h;
      ASSERT_EQ(evt.conversationState, ConversationState_RemoteOriginated);
      ASSERT_EQ(evt.conversationType, ConversationType_Incoming);
   }

   dave.conversation->sendRingingResponse(daveCall);
   assertConversationStateChanged(dave, daveCall, ConversationState_LocalRinging);
   dave.conversation->accept(daveCall);
   assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
   assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
   assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
   assertConversationMediaChanged(dave, daveCall, MediaDirection_SendReceive);
   assertConversationStateChanged(dave, daveCall, ConversationState_Connected);
   
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   // Bob terminates the call with Alice
   dave.conversation->end(daveCall);
   assertConversationEnded(dave, daveCall, ConversationEndReason_UserTerminatedLocally);
   assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);   

   // Wait for remote line state change notification => Proceeding
   cpc::string aliceDialogId;
   assertBLFRemoteLineStateChanged_ex(attendant, remoteLineSet, aliceRemoteLine, DialogDirection_Initiator, DialogState_Proceeding, aliceRemoteLine, "", aliceDialogId);

   // Validate the state of the remote line
   assertBLFRemoteLineState_ex(attendant, remoteLineSet, aliceRemoteLine, true, SipSubscriptionState_Active, DialogState_Proceeding);

   // Wait for remote line state change notification => Confirmed
   assertBLFRemoteLineStateChanged_ex(attendant, remoteLineSet, aliceRemoteLine, DialogDirection_Initiator, DialogState_Confirmed, aliceRemoteLine, "", aliceDialogId);

   // Validate the state of the remote line
   assertBLFRemoteLineState_ex(attendant, remoteLineSet, aliceRemoteLine, true, SipSubscriptionState_Active, DialogState_Confirmed);

   // Wait for remote line state change notification => Terminated
   assertBLFRemoteLineStateChanged_ex(attendant, remoteLineSet, aliceRemoteLine, DialogDirection_Initiator, DialogState_Terminated, aliceRemoteLine, "", aliceDialogId);

   // Validate the state of the remote line
   assertBLFRemoteLineState_ex(attendant, remoteLineSet, aliceRemoteLine, true, SipSubscriptionState_Active, DialogState_Terminated);

   // Terminate subscription
   assertSuccess(attendant.busyLampFieldManager->end(remoteLineSet));

   // Wait for the subscription termination notification
   assertBLFRemoteLineSubscriptionEnded(attendant, remoteLineSet, aliceRemoteLine);
   assertBLFRemoteLineSubscriptionEnded(attendant, remoteLineSet, bobRemoteLine);
   assertBLFRemoteLineSubscriptionEnded(attendant, remoteLineSet, charlieRemoteLine);
}


//  Dialog state changes for monitoring an outgoing call
TEST_F(BroadsoftBlfTest, IncomingCall)
{
   AttendantTestAccount attendant("attendant");
   MonitoredLineTestAccount alice("alice");
   MonitoredLineTestAccount bob("bob");
   MonitoredLineTestAccount charlie("charlie");
   StandardLineTestAccount dave("dave");

   SipBusyLampFieldRemoteLineHandle aliceRemoteLine = alice.blfLineHandle;
   SipBusyLampFieldRemoteLineHandle bobRemoteLine = bob.blfLineHandle;
   SipBusyLampFieldRemoteLineHandle charlieRemoteLine = charlie.blfLineHandle;

   // Attendant subscribes to the remote line
   SipBusyLampFieldRemoteLineSetSettings settings;
   settings.expires = 3600;
   settings.resourceListAddress = BROADSOFT_BLF_RESOURCE_LIST_URI;
   SipBusyLampFieldRemoteLineSetHandle remoteLineSet = attendant.busyLampFieldManager->createBusyLampFieldRemoteLineSet(attendant.handle, settings);
   ASSERT_TRUE(remoteLineSet > 0);

   // Start the subscription
   assertSuccess(attendant.busyLampFieldManager->start(remoteLineSet));

   // Wait for the new subscription notification
   assertBLFRemoteLineNewSubscription(attendant, remoteLineSet, aliceRemoteLine);
   assertBLFRemoteLineNewSubscription(attendant, remoteLineSet, bobRemoteLine);
   assertBLFRemoteLineNewSubscription(attendant, remoteLineSet, charlieRemoteLine);

   // Wait for line state change notifications
   assertBLFRemoteLineStateChanged(attendant, remoteLineSet, aliceRemoteLine);
   assertBLFRemoteLineStateChanged(attendant, remoteLineSet, bobRemoteLine);
   assertBLFRemoteLineStateChanged(attendant, remoteLineSet, charlieRemoteLine);


   // Dave calls alice
   SipConversationHandle daveCall = dave.conversation->createConversation(dave.handle);
   dave.conversation->addParticipant(daveCall, alice.config.uri());
   dave.conversation->start(daveCall);
   SipConversationHandle aliceCall;
   {
      SipConversationHandle h;
      NewConversationEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
         "SipConversationHandler::onNewConversation",
         10000,
         AlwaysTruePred(),
         h, evt));
      aliceCall = h;
      ASSERT_EQ(evt.conversationState, ConversationState_RemoteOriginated);
      ASSERT_EQ(evt.conversationType, ConversationType_Incoming);
   }

   alice.conversation->sendRingingResponse(aliceCall);
   assertConversationStateChanged(alice, aliceCall, ConversationState_LocalRinging);
   alice.conversation->accept(aliceCall);
   assertConversationStateChanged(dave, daveCall, ConversationState_RemoteRinging);
   assertConversationMediaChanged(dave, daveCall, MediaDirection_SendReceive);
   assertConversationStateChanged(dave, daveCall, ConversationState_Connected);
   assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
   assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

   // Dave terminates the call with Alice
   dave.conversation->end(daveCall);
   assertConversationEnded(dave, daveCall, ConversationEndReason_UserTerminatedLocally);
   assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);   

   // Wait for remote line state change notification => Proceeding
   cpc::string aliceDialogId;
   assertBLFRemoteLineStateChanged_ex(attendant, remoteLineSet, aliceRemoteLine, DialogDirection_Recipient, DialogState_Proceeding, aliceRemoteLine, "", aliceDialogId);

   // Validate the state of the remote line
   assertBLFRemoteLineState_ex(attendant, remoteLineSet, aliceRemoteLine, true, SipSubscriptionState_Active, DialogState_Proceeding);

   // Wait for remote line state change notification => Confirmed
   assertBLFRemoteLineStateChanged_ex(attendant, remoteLineSet, aliceRemoteLine, DialogDirection_Recipient, DialogState_Confirmed, aliceRemoteLine, "", aliceDialogId);

   // Validate the state of the remote line
   assertBLFRemoteLineState_ex(attendant, remoteLineSet, aliceRemoteLine, true, SipSubscriptionState_Active, DialogState_Confirmed);

   // Wait for remote line state change notification => Terminated
   assertBLFRemoteLineStateChanged_ex(attendant, remoteLineSet, aliceRemoteLine, DialogDirection_Recipient, DialogState_Terminated, aliceRemoteLine, "", aliceDialogId);

   // Validate the state of the remote line
   assertBLFRemoteLineState_ex(attendant, remoteLineSet, aliceRemoteLine, true, SipSubscriptionState_Active, DialogState_Terminated);

   // Terminate subscription
   assertSuccess(attendant.busyLampFieldManager->end(remoteLineSet));

   // Wait for the subscription termination notification
   assertBLFRemoteLineSubscriptionEnded(attendant, remoteLineSet, aliceRemoteLine);
   assertBLFRemoteLineSubscriptionEnded(attendant, remoteLineSet, bobRemoteLine);
   assertBLFRemoteLineSubscriptionEnded(attendant, remoteLineSet, charlieRemoteLine);
}


void BroadsoftBlfTest::expectPhoneError(int line, TestAccount& account, const cpc::string& errorText)
{
   PhoneErrorEvent evt;
   cpc::string module;
   ASSERT_TRUE(account.events->expectEvent(line,
      "PhoneHandler::onError",
      15000, StrEqualsPred("SipAccountInterface"), module, evt));
   ASSERT_EQ(errorText, evt.errorText);
}

void BroadsoftBlfTest::expectBLFError(int line, AttendantTestAccount& account, SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const cpc::string& errorText)
{
   SipBusyLampFieldRemoteLineSetHandle h;
   SipBusyLampField::ErrorEvent evt;
   ASSERT_TRUE(account.busyLampFieldEvents->expectEvent(line,
      "SipBusyLampFieldHandler::onError",
      5000, AlwaysTruePred(), h, evt));
   ASSERT_EQ(remoteLineSet, h);
	ASSERT_EQ(errorText, evt.errorText);
}

void BroadsoftBlfTest::expectBLFRemoteLineNewSubscription(int line, AttendantTestAccount& account, SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const SipBusyLampFieldRemoteLineHandle& remoteLine)
{
	SipBusyLampFieldRemoteLineSetHandle h;
	RemoteLineNewSubscriptionEvent evt;
   ASSERT_TRUE(account.busyLampFieldEvents->expectEvent(line,
		"SipBusyLampFieldHandler::onRemoteLineNewSubscription",
		15000,
		AlwaysTruePred(), h, evt));
   ASSERT_EQ(remoteLineSet, h);
	ASSERT_EQ(remoteLine, evt.remoteLine);
}

void BroadsoftBlfTest::expectBLFRemoteLineState(int line, AttendantTestAccount& account, SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const SipBusyLampFieldRemoteLineHandle& remoteLine, bool subscriptionStarted, SipSubscriptionState subscriptionState, DialogState dialogState)
{
   SipBusyLampFieldRemoteLineState remoteLineState;
   assertSuccess(account.busyLampFieldStateManager->getState(remoteLineSet, remoteLine, remoteLineState));
   ASSERT_EQ(remoteLine, remoteLineState.remoteLine);
   ASSERT_EQ(subscriptionStarted, remoteLineState.subscriptionStarted);
   ASSERT_EQ(subscriptionState, remoteLineState.subscriptionState);
   if (dialogState != DialogState_NotSpecified)
   {
      ASSERT_EQ(remoteLineState.calls.size(), 1);
      ASSERT_EQ(dialogState, remoteLineState.calls[0].dialog.stateInfo.state);
   }
}

void BroadsoftBlfTest::expectBLFRemoteLineStateChanged(int line, AttendantTestAccount& account, SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const SipBusyLampFieldRemoteLineHandle& remoteLine, DialogDirection direction, DialogState dialogState, const cpc::string& localAddress, const cpc::string& remoteAddress, cpc::string& dialogId)
{
   SipBusyLampFieldRemoteLineSetHandle h;
   RemoteLineStateChangedEvent evt;
   ASSERT_TRUE(account.busyLampFieldEvents->expectEvent(line,
      "SipBusyLampFieldHandler::onRemoteLineStateChanged",
		5000, AlwaysTruePred(), h, evt));
   ASSERT_EQ(remoteLineSet, h);
   ASSERT_EQ(remoteLine, evt.remoteLine);
 
   if (direction != DialogDirection_NotSpecified)
   {
      ASSERT_EQ(evt.calls.size(), 1);
      SipDialogEvent::DialogInfo dialog = evt.calls[0].dialog;
      dialogId = dialog.id;
      ASSERT_EQ(direction, dialog.direction);
      ASSERT_EQ(dialogState, dialog.stateInfo.state);
      ASSERT_EQ(0, dialog.stateInfo.code);
      ASSERT_EQ(localAddress, dialog.localParticipant.identity.address);
      if (!cpc::string(remoteAddress).empty())
      {
         ASSERT_EQ(remoteAddress, dialog.remoteParticipant.identity.address);
      }
   }
}

void BroadsoftBlfTest::expectBLFRemoteLineSubscriptionStateChanged(int line, AttendantTestAccount& account, SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const SipBusyLampFieldRemoteLineHandle& remoteLine, SipSubscriptionState subscriptionState)
{
	SipBusyLampFieldRemoteLineSetHandle h;
   RemoteLineSubscriptionStateChangedEvent evt;
   ASSERT_TRUE(account.busyLampFieldEvents->expectEvent(line,
		"SipBusyLampFieldHandler::onRemoteLineSubscriptionStateChanged",
		15000,
		AlwaysTruePred(), h, evt));
   ASSERT_EQ(remoteLineSet, h);
	ASSERT_EQ(remoteLine, evt.remoteLine);
   ASSERT_EQ(subscriptionState, evt.subscriptionState);
}

void BroadsoftBlfTest::expectBLFRemoteLineSubscriptionEnded(int line, AttendantTestAccount& account, SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const SipBusyLampFieldRemoteLineHandle& remoteLine)
{
  	SipBusyLampFieldRemoteLineSetHandle h;
	RemoteLineSubscriptionEndedEvent evt;
   ASSERT_TRUE(account.busyLampFieldEvents->expectEvent(line,
		"SipBusyLampFieldHandler::onRemoteLineSubscriptionEnded",
		15000,
		AlwaysTruePred(), h, evt));
   ASSERT_EQ(remoteLineSet, h);
   ASSERT_EQ(remoteLine, evt.remoteLine);
	ASSERT_EQ(SipSubscriptionEndReason_ServerEnded, evt.endReason);
}

void BroadsoftBlfTest::waitForConversationStateChanged_(int line, TestAccount& account, SipConversationHandle conversation, ConversationState conversationState)
{
   while(true)
   {
      SipConversationHandle h;
      ConversationStateChangedEvent evt;
      ASSERT_TRUE(account.conversationEvents->waitForEvent(line,
            "SipConversationHandler::onConversationStateChanged",
            5000, HandleEqualsPred<SipConversationHandle>(conversation), h, evt));
      ASSERT_EQ(h, conversation);
      if (evt.conversationState == conversationState)
      {
         break;
      }
   }
}

}

#endif // CPCAPI2_BRAND_SIP_BUSY_LAMP_FIELD_MODULE
