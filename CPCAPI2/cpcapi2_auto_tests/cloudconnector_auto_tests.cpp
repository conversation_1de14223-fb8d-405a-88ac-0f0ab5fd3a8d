#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#if (CPCAPI2_BRAND_CLOUD_CONNECTOR_MODULE == 1)

#include "cpcapi2_test_fixture.h"
#include "test_call_events.h"
#include "test_events.h"
#include "test_account_events.h"
#include "test_framework/xmpp_test_helper.h"

#include <cloudconnector/CloudConnector.h>
#include <cloudconnector/CloudConnectorHandler.h>
#include <cloudconnector/CloudConnectorTypes.h>
#include <orchestration_server/OrchestrationServer.h>
#include <cloudserviceconfig/CloudServiceConfig.h>
#include "xmpp/XmppAccountInterface.h"
#include "xmpp/XmppAccountJsonApi.h"
#include "xmpp/XmppChatJsonApi.h"
#include "xmpp_agent/XmppAgentHandler.h"
#include "xmpp_agent/XmppAgentJsonApi.h"
#include "pushnotification/PushNotificationClientManager.h"
#include "pushnotification/PushNotificationClientHandler.h"
#include "pushnotification/PushNotificationJsonApi.h"
//#include "pushnotification/PushNotificationManagerInterface.h"

#include "../../impl/util/CurlPPHelper.h"
#include "../../impl/util/CurlPPProgress.h"
#include "../../impl/util/CurlPPSSL.h"
#include "../../impl/auth_server/AuthServerJwtUtils.h"
#include "../../impl/auth_server/AuthServerDbAccess.h"

using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::CloudConnector;
using namespace CPCAPI2::CloudServiceConfig;
using namespace CPCAPI2::OrchestrationServer;
using namespace CPCAPI2::JsonApi;
using namespace CPCAPI2::XmppAccount;
using namespace CPCAPI2::XmppChat;
using namespace CPCAPI2::PushEndpoint;

class CloudConnectorTests : public CpcapiAutoTest
{
public:
   CloudConnectorTests() {}
   virtual ~CloudConnectorTests() {}
};

#if 0
TEST_F(CloudConnectorTests, ConnectCloudSuccess)
{
   XmppTestCloudAccount alice("alice");
   
   alice.enableCloud();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
}

TEST_F(CloudConnectorTests, ConnectCloudDestroySdkThreadResponsive)
{
   XmppTestCloudAccount alice("alice");
   
   alice.enableCloud();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   alice.destroy();
   
   class TestReactorExecuteHelper
   {
   public:
      TestReactorExecuteHelper() : mTestFunctionFinished(false) {}

      std::mutex mMutex;
      bool mTestFunctionFinished;

      static void staticTestFunction(void* context)
      {
         if (TestReactorExecuteHelper* instance = static_cast<TestReactorExecuteHelper*>(context))
         {
            instance->testFunction();
         }
      }

      void testFunction()
      {
         std::this_thread::sleep_for(std::chrono::milliseconds(2000));

         std::lock_guard<std::mutex> lock(mMutex);
         mTestFunctionFinished = true;
      }
   };

   // ensure SDK thread is still responsive
   TestReactorExecuteHelper reh;
   static_cast<PhoneInternal*>(alice.phone)->blockUntilRanOnSdkModuleThread(TestReactorExecuteHelper::staticTestFunction, reinterpret_cast<void*>(&reh));
      
}

TEST_F(CloudConnectorTests, ConnectCloudAndEnableAccountSuccess)
{
   XmppTestCloudAccount alice("alice");
   
   alice.enable();
   CPCAPI2::Phone* aliceCloudPhone = alice.getCloudPhone();
   
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   alice.disable();
   
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
}

TEST_F(CloudConnectorTests, ConnectCloudLogoutSuccess)
{
   XmppTestCloudAccount alice("alice");
   bool registerForPush = true;
   alice.enable(registerForPush);
   
   CloudConnector::CloudConnectorHandle cloudConnectorHandle = alice.getCloudConnectorHandle();
   
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   
   alice.agentJson->logout(alice.agentHandle);
   assertAgentLogoutForXmpp(alice, alice.agentHandle, [&](const CPCAPI2::XmppAgent::LogoutResult& evt)
   {
      ASSERT_TRUE(evt.success);
   });
   
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   
   auto agentEvents = std::async(std::launch::async, [&] ()
   {
#if (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
      JsonApi::LogoutEvent logoutEvent;
      JsonApi::JsonApiUserHandle jsonApiUser = 0;
   
      // Process the json server logout attempt, this would normally be handled by the CPCAPI runner application of the xmpp agent
      ASSERT_TRUE(cpcExpectEvent(alice.agentServer->jsonApiServerEvents, "JsonApiServerHandler::onLogout", 30000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, logoutEvent));
      ASSERT_NE(jsonApiUser, 0);
                                      
      CPCAPI2::JsonApi::LogoutResultEvent logoutResult;
      logoutResult.success = true;
      alice.agentServer->jsonApiServer->sendLogoutResult(jsonApiUser, logoutResult);
#endif
   });
   
   auto cloudEvents = std::async(std::launch::async, [&] ()
   {
      alice.cloudConnector->logout(cloudConnectorHandle);
      
      CloudConnector::LogoutResult logoutResult;
      ASSERT_TRUE(cpcExpectEvent(alice.cloudConnectorEvents, "CloudConnectorHandler::onLogout", 30000, CPCAPI2::test::AlwaysTruePred(), cloudConnectorHandle, logoutResult));
      ASSERT_TRUE(logoutResult.success);
   });
   
   waitFor2(agentEvents, cloudEvents);
   
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   
   // !jjg! can't do this -- the phone instance for the connection gets destroyed, 
   // which means we can't access alice.cloudConnectorEvents
   //
   //alice.cloudConnector->destroy(cloudConnectorHandle);
   //assertCloudDestroyedForXmpp(alice, cloudConnectorHandle, [&](const ServiceConnectionStatusEvent& evt)
   //{
   //   ASSERT_EQ(CloudConnector::ServiceConnectionStatus_Destroyed, evt.connectionStatus);
   //});
   
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.cleanup();
}

TEST_F(CloudConnectorTests, ConnectCloudReLoginLogoutSuccess)
{
   XmppTestCloudAccount alice("alice");
   bool registerForPush = true;
   alice.enable(registerForPush);

   CloudConnector::CloudConnectorHandle cloudConnectorHandle = alice.getCloudConnectorHandle();

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   cpc::string xmppAgentServiceId = CPCAPI2::XmppAgent::XmppAgentManager::getServiceId();
   cpc::string pushNotificationClientServiceId = CPCAPI2::PushEndpoint::PushNotificationEndpointManager::getServiceId();
   cpc::vector<cpc::string> permissions; permissions.push_back("*");
   alice.cloudConnector->disconnectService(cloudConnectorHandle, {"NA", xmppAgentServiceId});
   alice.cloudConnector->disconnectService(cloudConnectorHandle, {"NA", pushNotificationClientServiceId});
   assertCloudDisconnectedForXmpp(alice, cloudConnectorHandle, [&](const ServiceConnectionStatusEvent& evt)
   {
      ASSERT_EQ(CloudConnector::ServiceConnectionStatus_Disconnected, evt.connectionStatus);
   });

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   // alice.cloudConnector->setConnectionSettings(cloudConnectorHandle, clientCloudSettings);
   alice.cloudConnector->requestService(cloudConnectorHandle, {"NA", xmppAgentServiceId});
   alice.cloudConnector->requestService(cloudConnectorHandle, {"NA", pushNotificationClientServiceId});
   alice.cloudConnector->connectToServices(cloudConnectorHandle);

   auto agentEvent = std::async(std::launch::async, [&]()
   {
      NewLoginEvent args;
      LoginResultEvent loginResult;
      loginResult.success = true;
      CPCAPI2::JsonApi::JsonApiUserHandle oldJsonUserHandle = alice.jsonApiUserHandle;

      // Process the orchestrator login attempt over http (associate the context with an SDK instance)
#if (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
      ASSERT_TRUE(cpcExpectEvent(alice.agentServer->jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 20000, CPCAPI2::test::AlwaysTruePred(), alice.jsonApiUserHandle, args));
      ASSERT_NE(alice.jsonApiUserHandle, 0);
      ASSERT_NE(oldJsonUserHandle, 0);
      ASSERT_NE(alice.jsonApiUserHandle, oldJsonUserHandle);
      alice.agentServer->jsonApiServer->setJsonApiUserContext(alice.jsonApiUserHandle, alice.agentServer->phone, permissions);
      loginResult.requestId = args.requestId;
      alice.agentServer->jsonApiServer->sendLoginResult(alice.jsonApiUserHandle, loginResult);

      // Process the agent login attempt over websocket (associate the context with an SDK instance)
      ASSERT_TRUE(cpcExpectEvent(alice.agentServer->jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 20000, CPCAPI2::test::AlwaysTruePred(), alice.jsonApiUserHandle, args));
      ASSERT_NE(alice.jsonApiUserHandle, 0);
      ASSERT_NE(alice.jsonApiUserHandle, oldJsonUserHandle);
      alice.agentServer->jsonApiServer->setJsonApiUserContext(alice.jsonApiUserHandle, alice.agentServer->phone, permissions);
      loginResult.requestId = args.requestId;

      alice.agentServer->jsonApiServer->updateUser(oldJsonUserHandle, CPCAPI2::JsonApi::UpdateUserEvent(alice.jsonApiUserHandle, args.authToken, args.userIdentity));
      ReLoginEvent reloginEvent;
      ASSERT_TRUE(cpcExpectEvent(alice.agentServer->jsonApiServerEvents, "JsonApiServerHandler::onReLogin", 20000, CPCAPI2::test::AlwaysTruePred(), oldJsonUserHandle, reloginEvent));
      ASSERT_NE(reloginEvent.newJsonApiUser, 0);
      ASSERT_EQ(reloginEvent.newJsonApiUser, alice.jsonApiUserHandle);

      alice.agentServer->jsonApiServer->sendLoginResult(alice.jsonApiUserHandle, loginResult);
#endif
   });

   CPCAPI2::CloudConnector::ServiceConnectionStatusEvent args;
   auto clientEvent = std::async(std::launch::async, [&]()
   {
      {
         ASSERT_TRUE(cpcExpectEvent(alice.cloudConnectorEvents, "CloudConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), cloudConnectorHandle, args));
         ASSERT_EQ(CloudConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
      }

      {
         ASSERT_TRUE(cpcExpectEvent(alice.cloudConnectorEvents, "CloudConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), cloudConnectorHandle, args));
         ASSERT_EQ(CloudConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
      }

      {
         ASSERT_TRUE(cpcExpectEvent(alice.cloudConnectorEvents, "CloudConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), cloudConnectorHandle, args));
         ASSERT_EQ(CloudConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
      }

      // Should be same cloud phone as the connector was not destroyed just disconnected
      CPCAPI2::Phone* clientCloudPhone = alice.cloudConnector->getPhone(cloudConnectorHandle, { "NA", xmppAgentServiceId });
      ASSERT_FALSE(clientCloudPhone == NULL);
      alice.setCloudConnected(true);
   });

   waitFor2(agentEvent, clientEvent);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   auto agentLogoutEvent = std::async(std::launch::async, [&]()
   {
      LogoutEvent args;

#if (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
      ASSERT_TRUE(cpcExpectEvent(alice.agentServer->jsonApiServerEvents, "JsonApiServerHandler::onLogout", 20000, CPCAPI2::test::AlwaysTruePred(), alice.jsonApiUserHandle, args));
      CPCAPI2::JsonApi::LogoutResultEvent logoutResult;
      logoutResult.success = true;
      alice.agentServer->jsonApiServer->sendLogoutResult(alice.jsonApiUserHandle, logoutResult);
#endif
   });

   auto cloudLogoutEvent = std::async(std::launch::async, [&] ()
   {
      alice.cloudConnector->logout(cloudConnectorHandle);

      CloudConnector::LogoutResult logoutResult;
      ASSERT_TRUE(cpcExpectEvent(alice.cloudConnectorEvents, "CloudConnectorHandler::onLogout", 30000, CPCAPI2::test::AlwaysTruePred(), cloudConnectorHandle, logoutResult));
      ASSERT_TRUE(logoutResult.success);
   });

   waitFor2(agentLogoutEvent, cloudLogoutEvent);

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   // !jjg! can't do this -- the phone instance for the connection gets destroyed, 
   // which means we can't access alice.cloudConnectorEvents
   //
   //alice.cloudConnector->destroy(cloudConnectorHandle);
   //assertCloudDestroyedForXmpp(alice, cloudConnectorHandle, [&](const ServiceConnectionStatusEvent& evt)
   //{
   //   ASSERT_EQ(CloudConnector::ServiceConnectionStatus_Destroyed, evt.connectionStatus);
   //});

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.cleanup();
}

TEST_F(CloudConnectorTests, ConnectFailureAndDisconnect)
{
   cpc::string xmppAgentServiceId = CPCAPI2::XmppAgent::XmppAgentManager::getServiceId();
   cpc::string pushNotificationClientServiceId = CPCAPI2::PushEndpoint::PushNotificationEndpointManager::getServiceId();
   LoginResultEvent loginResult; loginResult.success = true;
   cpc::vector<cpc::string> permissions; permissions.push_back("*");
   
   // Max is the auth server and orchestration server
   TestAccount max("max", Account_Init);
   XmppTestAccountConfig::setupAuthServer(max);
   
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   
   auto maxSetupEvent = std::async(std::launch::async, [&]()
   {
   });
   
   // Maia is the xmpp agent and push notification server, starting it just so orch server has an instance,
   // but we don't want the client to get a connection, as we want to simulate a connection failure so provide
   // an pass an invalid xmpp agent port to the setup utility function
   XmppTestAccount maia("maia", Account_Init);
   auto maiaSetupEvent = std::async(std::launch::async, [&]()
   {
      cpc::string certificateFilePath = TestEnvironmentConfig::testResourcePath() + "p256-public-key-unit-tests.spki"; // pub key used to validate auth tokens
      JsonApi::JsonApiServerConfig jsonApiServCfg(9004, 18082, certificateFilePath);
      XmppTestAccountConfig::setupXmppAgent(maia, jsonApiServCfg);
   });
   
   waitFor2(maxSetupEvent, maiaSetupEvent);
   
   // Alice is a client SDK
   XmppTestAccount alice("alice", Account_Init);
   alice.config.useJsonProxy = true;
   
   CloudConnectorHandle aliceCloudConn = alice.cloudConnector->createCloudConnector();
   alice.cloudConnector->setHandler(aliceCloudConn, reinterpret_cast<CloudConnectorHandler*>(0xDEADBEEF));
   CloudConnectorSettings aliceCloudSettings;
   aliceCloudSettings.authServerUrl = "http://127.0.0.1:18084/login_v1";
   aliceCloudSettings.orchestrationServerUrl = "http://127.0.0.1:18082/jsonApi";
   aliceCloudSettings.regionCode = "NA";
   aliceCloudSettings.username = "user1";
   aliceCloudSettings.password = "1234";
   alice.cloudConnector->setConnectionSettings(aliceCloudConn, aliceCloudSettings);
   alice.cloudConnector->requestService(aliceCloudConn, { "NA", xmppAgentServiceId });
   alice.cloudConnector->requestService(aliceCloudConn, { "NA", pushNotificationClientServiceId });
   alice.cloudConnector->connectToServices(aliceCloudConn);
   
   auto maiaEvent = std::async(std::launch::async, [&]()
   {
      JsonApiUserHandle jsonApiUser = 0;
      NewLoginEvent args;

      // Maia has to process the login attempt (associate the context with an SDK instance)
      ASSERT_TRUE(cpcExpectEvent(maia.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 5000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args));
      ASSERT_NE(jsonApiUser, 0);
      maia.jsonApiServer->setJsonApiUserContext(jsonApiUser, maia.phone, permissions);
      maia.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);
   });
   
   auto aliceEvent = std::async(std::launch::async, [&]()
   {
      {
         CloudConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.cloudConnectorEvents, "CloudConnectorHandler::onServiceConnectionStatusChanged", 8000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(CloudConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
      }
      
      {
         CloudConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.cloudConnectorEvents, "CloudConnectorHandler::onServiceConnectionStatusChanged", 12000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(CloudConnector::ServiceConnectionStatus_ConnFailure, args.connectionStatus);
      }
      
      {
         CloudConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.cloudConnectorEvents, "CloudConnectorHandler::onServiceConnectionStatusChanged", 12000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(CloudConnector::ServiceConnectionStatus_ConnFailure, args.connectionStatus);
      }
      
      alice.cloudConnector->disconnectService(aliceCloudConn, { "NA", xmppAgentServiceId });
      alice.cloudConnector->disconnectService(aliceCloudConn, { "NA", pushNotificationClientServiceId });
      
      {
         CloudConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.cloudConnectorEvents, "CloudConnectorHandler::onServiceConnectionStatusChanged", 8000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(CloudConnector::ServiceConnectionStatus_Disconnecting, args.connectionStatus);
      }
      
      {
         CloudConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.cloudConnectorEvents, "CloudConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(CloudConnector::ServiceConnectionStatus_Disconnected, args.connectionStatus);
      }
      
      // !jjg! can't do this -- the phone instance for the connection gets destroyed, 
      // which means we can't access alice.cloudConnectorEvents
      //
      //alice.cloudConnector->destroy(aliceCloudConn);
      //assertCloudDestroyedForXmpp(alice, aliceCloudConn, [&](const ServiceConnectionStatusEvent& evt)
      //{
      //   ASSERT_EQ(CloudConnector::ServiceConnectionStatus_Destroyed, evt.connectionStatus);
      //});
      
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   });
   
   waitFor2(aliceEvent, maiaEvent);
   CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(maia.phone)->flushAll();
   CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(maia.phone)->shutdown();
   maia.jsonApiServer->shutdown();
   max.authServer->shutdown();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
}
#endif

/* TODO: Create a unit test case to handle a reconnect scenario
TEST_F(CloudConnectorTests, ConnectSuccessFailureReconnect)
{
   cpc::string xmppAgentServiceId = CPCAPI2::XmppAgent::XmppAgentManager::getServiceId();
   cpc::string pushNotificationClientServiceId = CPCAPI2::PushNotification::PushNotificationClientManager::getServiceId();
   LoginResultEvent loginResult; loginResult.success = true;
   cpc::vector<cpc::string> permissions; permissions.push_back("*");
   
   // Max is the auth server and orchestration server
   TestAccount max("max", Account_Init);
   setupAuthServer(max);
   
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   
   auto maxSetupEvent = std::async(std::launch::async, [&]()
   {
      JsonApiUserHandle jsonApiUser;
      NewLoginEvent args;
      
      // Max has to process the login attempt (associate the context with an SDK instance)
      cpcExpectEvent(max.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 15000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args);
      max.jsonApiServer->setJsonApiUserContext(jsonApiUser, max.phone, permissions);
      max.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);
      
      // There's one login per "setServerInfo" call
      cpcExpectEvent(max.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 5000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args);
      max.jsonApiServer->setJsonApiUserContext(jsonApiUser, max.phone, permissions);
      max.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);
   });
   
   // Maia is the xmpp agent and push notification server
   XmppTestAccount maia("maia", Account_Init);
   auto maiaSetupEvent = std::async(std::launch::async, [&]()
   {
      setupXmppAgentAndPushServer(maia);
   });
   
   waitFor2(maxSetupEvent, maiaSetupEvent);
   
   // Alice is a client SDK
   XmppTestAccount alice("alice", Account_Init);
   alice.config.useJsonProxy = true;
   
   CloudConnectorHandle aliceCloudConn = alice.cloudConnector->createCloudConnector();
   CloudConnectorSettings aliceCloudSettings;
   aliceCloudSettings.authServerUrl = "http://127.0.0.1:18084/login_v1";
   aliceCloudSettings.orchestrationServerUrl = "http://127.0.0.1:18085/jsonApi";
   aliceCloudSettings.regionCode = "NA";
   aliceCloudSettings.username = "user1";
   aliceCloudSettings.password = "1234";
   alice.cloudConnector->setConnectionSettings(aliceCloudConn, aliceCloudSettings);
   alice.cloudConnector->requestService(aliceCloudConn, { "NA", xmppAgentServiceId });
   alice.cloudConnector->requestService(aliceCloudConn, { "NA", pushNotificationClientServiceId });
   alice.cloudConnector->connectToServices(aliceCloudConn);
   
   auto maxEvent = std::async(std::launch::async, [&]()
   {
      JsonApiUserHandle jsonApiUser;
      NewLoginEvent args;
      
      // Max has to process the login attempt (associate the context with an SDK instance)
      cpcExpectEvent(max.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 5000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args);
      max.jsonApiServer->setJsonApiUserContext(jsonApiUser, max.phone, permissions);
      max.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);
   });
   
   auto maiaEvent = std::async(std::launch::async, [&]()
   {
      JsonApiUserHandle jsonApiUser = 0;
      NewLoginEvent args;
      
      // Maia has to process the login attempt (associate the context with an SDK instance)
      cpcExpectEvent(maia.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 5000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args);
      ASSERT_NE(jsonApiUser, 0);
      maia.jsonApiServer->setJsonApiUserContext(jsonApiUser, maia.phone, permissions);
      maia.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      maia.jsonApiServer->shutdown();
   });
   
   auto aliceEvent = std::async(std::launch::async, [&]()
   {
      {
         CloudConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         cpcExpectEvent(alice.cloudConnectorEvents, "CloudConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args);
         ASSERT_EQ(CloudConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
      }
      
      {
         CloudConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         cpcExpectEvent(alice.cloudConnectorEvents, "CloudConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args);
         ASSERT_EQ(CloudConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
      }
      
      {
         CloudConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         cpcExpectEvent(alice.cloudConnectorEvents, "CloudConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args);
         ASSERT_EQ(CloudConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
      }
      
      {
         CloudConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         cpcExpectEvent(alice.cloudConnectorEvents, "CloudConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args);
         ASSERT_EQ(CloudConnector::ServiceConnectionStatus_ConnFailure, args.connectionStatus);
      }
   });
   
   waitFor3(aliceEvent, maxEvent, maiaEvent);

   // Relaunch
   auto maxSetupEvent2 = std::async(std::launch::async, [&]()
   {
      JsonApiUserHandle jsonApiUser;
      NewLoginEvent args;
                                      
      // Max has to process the login attempt (associate the context with an SDK instance)
      cpcExpectEvent(max.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 15000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args);
      max.jsonApiServer->setJsonApiUserContext(jsonApiUser, max.phone, permissions);
      max.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);
                                      
      // There's one login per "setServerInfo" call
      cpcExpectEvent(max.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 5000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args);
      max.jsonApiServer->setJsonApiUserContext(jsonApiUser, max.phone, permissions);
      max.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);
   });
   
   // Maia is the xmpp agent and push notification server
   auto maiaSetupEvent2 = std::async(std::launch::async, [&]()
   {
      setupXmppAgentAndPushServer(maia);
   });
   
   waitFor2(maxSetupEvent2, maiaSetupEvent2);
   
   alice.cloudConnector->setConnectionSettings(aliceCloudConn, aliceCloudSettings);
   alice.cloudConnector->requestService(aliceCloudConn, { "NA", xmppAgentServiceId });
   alice.cloudConnector->requestService(aliceCloudConn, { "NA", pushNotificationClientServiceId });
   alice.cloudConnector->connectToServices(aliceCloudConn);
   
   auto maxEvent2 = std::async(std::launch::async, [&]()
   {
      JsonApiUserHandle jsonApiUser;
      NewLoginEvent args;

      // Max has to process the login attempt (associate the context with an SDK instance)
      cpcExpectEvent(max.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 5000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args);
      max.jsonApiServer->setJsonApiUserContext(jsonApiUser, max.phone, permissions);
      max.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);
   });
   
   auto maiaEvent2= std::async(std::launch::async, [&]()
   {
      JsonApiUserHandle jsonApiUser = 0;
      NewLoginEvent args;
                                  
      // Maia has to process the login attempt (associate the context with an SDK instance)
      cpcExpectEvent(maia.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 5000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args);
      ASSERT_NE(jsonApiUser, 0);
      maia.jsonApiServer->setJsonApiUserContext(jsonApiUser, maia.phone, permissions);
      maia.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);
   });
   
   auto aliceEvent2 = std::async(std::launch::async, [&]()
   {
      {
         CloudConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         cpcExpectEvent(alice.cloudConnectorEvents, "CloudConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args);
         ASSERT_EQ(CloudConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
      }
                                   
      {
         CloudConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         cpcExpectEvent(alice.cloudConnectorEvents, "CloudConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args);
         ASSERT_EQ(CloudConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
      }
                                   
      {
         CloudConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         cpcExpectEvent(alice.cloudConnectorEvents, "CloudConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args);
         ASSERT_EQ(CloudConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
      }
   });
   
   waitFor3(aliceEvent2, maxEvent2, maiaEvent2);
   
   maia.jsonApiServer->shutdown();
   max.orchestrationServer->flushAll();
   max.orchestrationServer->shutdown();
   max.authServer->shutdown();
   max.jsonApiServer->shutdown();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
}
*/

TEST_F(CloudConnectorTests, DISABLED_ConnectSuccessPublicCloud) {
   cpc::string xmppAgentServiceId = CPCAPI2::XmppAgent::XmppAgentManager::getServiceId();
   cpc::string pushNotificationClientServiceId = CPCAPI2::PushEndpoint::PushNotificationEndpointManager::getServiceId();

   //XmppTestAccountConfig::setupPublicOrchServer();

   //// Maia is the xmpp agent and push notification server
   //XmppTestAccount maia("maia", Account_Init);
   //XmppTestAccountConfig::setupXmppAgentAndPushServer(maia);

   // Alice is a client SDK
   XmppTestAccount alice("alice", Account_Init);
   alice.config.useJsonProxy = true;

   CloudConnectorHandle aliceCloudConn = alice.cloudConnector->createCloudConnector();
   CloudConnectorSettings aliceCloudSettings;
   aliceCloudSettings.authServerUrl = "http://cloudsdk1.bria-x.net:18082/login_v1"; //"http://127.0.0.1:18081/login_v1";
   aliceCloudSettings.orchestrationServerUrl = "http://localhost:18080/jsonApi"; //"http://127.0.0.1:18080/jsonApi";
   aliceCloudSettings.regionCode = "LOCAL";
   aliceCloudSettings.username = "counterpath_guest";
   aliceCloudSettings.password = "5e884898da28047151d0e56f8dc6292773603d0d6aabbdd62a11ef721d1542d8";
   alice.cloudConnector->setConnectionSettings(aliceCloudConn, aliceCloudSettings);
   alice.cloudConnector->requestService(aliceCloudConn, { "LOCAL", xmppAgentServiceId });
   alice.cloudConnector->requestService(aliceCloudConn, { "LOCAL", pushNotificationClientServiceId });
   alice.cloudConnector->connectToServices(aliceCloudConn);

   //auto maiaEvent = std::async(std::launch::async, [&]() {
   //   JsonApiUserHandle jsonApiUser = 0;
   //   NewLoginEvent args;

   //   // Maia has to process the login attempt (associate the context with an SDK instance)
   //   cpcExpectEvent(maia.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 5000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args);
   //   ASSERT_NE(jsonApiUser, 0);
   //   cpc::vector<cpc::string> permissions; permissions.push_back("*");
   //   maia.jsonApiServer->setJsonApiUserContext(jsonApiUser, maia.phone, permissions);
   //   LoginResultEvent loginResult;
   //   loginResult.success = true;
   //   maia.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);
   //});

   auto aliceEvent = std::async(std::launch::async, [&]() {
      {
         CloudConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         cpcExpectEvent(alice.cloudConnectorEvents, "CloudConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args);
         ASSERT_EQ(CloudConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
      }

      {
         CloudConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         cpcExpectEvent(alice.cloudConnectorEvents, "CloudConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args);
         ASSERT_EQ(CloudConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
      }

      {
         CloudConnectorHandle conn;
         ServiceConnectionStatusEvent args;
         cpcExpectEvent(alice.cloudConnectorEvents, "CloudConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args);
         ASSERT_EQ(CloudConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
      }

      CPCAPI2::XmppAccount::XmppAccountManagerJsonProxy* aliceXmppAccountProxyOrig = alice.accountJson;

      CPCAPI2::Phone* aliceXmppAgentPhone = alice.cloudConnector->getPhone(aliceCloudConn, { "LOCAL", xmppAgentServiceId });
      CPCAPI2::XmppAccount::XmppAccountManagerJsonProxy* aliceXmppAccountProxy = CPCAPI2::XmppAccount::XmppAccountManagerJsonProxy::getInterface(aliceXmppAgentPhone);
      alice.accountJson = aliceXmppAccountProxy; // replaces the instance of XmppAccountManagerJsonProxy created by the unit test framework

      alice.createAccountJson();
      alice.enable();
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      alice.disable();

      alice.accountJson = aliceXmppAccountProxyOrig; // restore the instance created by the unit test framework

   });

   waitFor(aliceEvent);

   //maia.jsonApiServer->shutdown();

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

}

#if (CPCAPI2_BRAND_AUTH_SERVER_MODULE == 1)
TEST_F(CloudConnectorTests, AddUserSuccess) {

   LoginResultEvent loginResult; loginResult.success = true;
   cpc::vector<cpc::string> permissions; permissions.push_back("*");

   // Max is the auth server
   TestAccount max("max", Account_Init);
   XmppTestAccountConfig::setupAuthServer(max, true);

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   // Alice is a client SDK
   XmppTestAccount alice("alice", Account_Init);
   alice.config.useJsonProxy = true;

   CloudConnectorHandle aliceCloudConn = alice.cloudConnector->createCloudConnector();
   alice.cloudConnector->setHandler(aliceCloudConn, reinterpret_cast<CloudConnectorHandler*>(0xDEADBEEF));
   AddUserRequest addUserReq;
   addUserReq.authServerUrl = "https://127.0.0.1:18084/addUser";
   addUserReq.authServerApiKey = TestEnvironmentConfig::testResourcePath() + "p256-public-key-unit-tests.spki"; // you could also specify the contents of this file here instead!
   addUserReq.username = "user1234";
   addUserReq.password = "1234";

   CloudConnectorSettings aliceCloudSettings;
   aliceCloudSettings.authServerUrl = "https://127.0.0.1:18084/login_v1";
   aliceCloudSettings.orchestrationServerUrl = "https://127.0.0.1:18082/jsonApi";
   aliceCloudSettings.regionCode = "NA";
   aliceCloudSettings.username = "user1";
   aliceCloudSettings.password = "1234";
   aliceCloudSettings.ignoreCertVerification = true;
   alice.cloudConnector->setConnectionSettings(aliceCloudConn, aliceCloudSettings);

   alice.cloudConnector->addUser(aliceCloudConn, addUserReq);

   {
      CloudConnectorHandle conn;
      AddUserResponse resp;
      ASSERT_TRUE(cpcExpectEvent(alice.cloudConnectorEvents, "CloudConnectorHandler::onAddUserResponse", 5000, CPCAPI2::test::AlwaysTruePred(), conn, resp));
      ASSERT_TRUE(resp.success);
   }

   max.authServer->shutdown();

   {
      CPCAPI2::AuthServer::DbAccess db;
      db.initialize("authserver.db");
      ASSERT_EQ(db.flushUsers(), 0);
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

}
#endif // CPCAPI2_BRAND_AUTH_SERVER_MODULE

TEST_F(CloudConnectorTests, AddUserSSLFail) {

   LoginResultEvent loginResult; loginResult.success = true;
   cpc::vector<cpc::string> permissions; permissions.push_back("*");

   // Max is the auth server
   TestAccount max("max", Account_Init);
   XmppTestAccountConfig::setupAuthServer(max, true);

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   // Alice is a client SDK
   XmppTestAccount alice("alice", Account_Init);
   alice.config.useJsonProxy = true;

   CloudConnectorHandle aliceCloudConn = alice.cloudConnector->createCloudConnector();
   alice.cloudConnector->setHandler(aliceCloudConn, reinterpret_cast<CloudConnectorHandler*>(0xDEADBEEF));
   AddUserRequest addUserReq;
   addUserReq.authServerUrl = "https://127.0.0.1:18084/addUser";
   addUserReq.authServerApiKey = TestEnvironmentConfig::testResourcePath() + "p256-public-key-unit-tests.spki"; // you could also specify the contents of this file here instead!
   addUserReq.username = "user1234";
   addUserReq.password = "1234";

   CloudConnectorSettings aliceCloudSettings;
   aliceCloudSettings.authServerUrl = "https://127.0.0.1:18084/login_v1";
   aliceCloudSettings.orchestrationServerUrl = "https://127.0.0.1:18082/jsonApi";
   aliceCloudSettings.regionCode = "NA";
   aliceCloudSettings.username = "user1";
   aliceCloudSettings.password = "1234";
   //aliceCloudSettings.ignoreCertVerification = true;
   alice.cloudConnector->setConnectionSettings(aliceCloudConn, aliceCloudSettings);

   alice.cloudConnector->addUser(aliceCloudConn, addUserReq);

   {
      CloudConnectorHandle conn;
      AddUserResponse resp;
      ASSERT_TRUE(cpcExpectEvent(alice.cloudConnectorEvents, "CloudConnectorHandler::onAddUserResponse", 5000, CPCAPI2::test::AlwaysTruePred(), conn, resp));
      ASSERT_FALSE(resp.success);
   }

   max.authServer->shutdown();

   {
      CPCAPI2::AuthServer::DbAccess db;
      db.initialize("authserver.db");
      ASSERT_EQ(db.flushUsers(), 0);
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

}

class MyCloudConnectorHandler : public CloudConnectorHandler
{
   bool mCallbackReceived;
public:
   MyCloudConnectorHandler() : mCallbackReceived(false) {}

   bool callbackReceived() const { return mCallbackReceived; }

   int onServiceConnectionStatusChanged(CloudConnectorHandle conn, const ServiceConnectionStatusEvent& args)
   {
      mCallbackReceived = true;
      return kSuccess;
   }

   int onAddUserResponse(CloudConnectorHandle conn, const AddUserResponse& args)
   {
      mCallbackReceived = true;
      return kSuccess;
   }

   int onLogout(CloudConnectorHandle conn, const LogoutResult& args)
   {
      mCallbackReceived = true;
      return kSuccess;
   }
};

// CloudConnectorInterface uses EventSource, and EventSource currently will *always* fire an event
// if we're building for the auto tests, regardless of whether setHandler is NULL or not.
// this combined with the test calling process(..) (not process_test) can result in crashes.
//
// to fix, we'd need to adjust EventSource to pay attention to actual app specified handler when
// building for the auto tests, but also update all test cases that use EventSource modules to
// specify a 0xDEADBEEF handler
TEST_F(CloudConnectorTests, DISABLED_RemoveHandler) {

   XmppTestAccount alice("alice", Account_Init);
   alice.config.useJsonProxy = true;

   // first ensure the event we'll test actually is fired
   {
      CloudConnectorHandle aliceCloudConn = alice.cloudConnector->createCloudConnector();
      alice.cloudConnector->logout(aliceCloudConn);
      
      {
         CloudConnectorHandle conn;
         LogoutResult resp;
         ASSERT_TRUE(cpcExpectEvent(alice.cloudConnectorEvents, "CloudConnectorHandler::onLogout", 5000, CPCAPI2::test::AlwaysTruePred(), conn, resp));
      }
   }

   // next, start again, but this time ensure our handler is *not* invoked
   {
      CloudConnectorHandle aliceCloudConn = alice.cloudConnector->createCloudConnector();
      
      MyCloudConnectorHandler ccHandler;
      alice.cloudConnector->setHandler(aliceCloudConn, &ccHandler);
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      alice.cloudConnector->setHandler(aliceCloudConn, NULL);
      alice.cloudConnector->logout(aliceCloudConn);

      for (int i = 0; i < 5; ++i)
      {
         alice.cloudConnector->process(500);
      }
      
      ASSERT_FALSE(ccHandler.callbackReceived());
   }
}


#endif
