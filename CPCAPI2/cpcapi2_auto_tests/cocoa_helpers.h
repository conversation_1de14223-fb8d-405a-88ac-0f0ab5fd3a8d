//
//  cocoa_helpers.h
//  CPCAPI2AutoTests
//
//  Created by <PERSON> on 2015-04-23.
//  Copyright (c) 2015 <PERSON>. All rights reserved.
//

#ifndef CPCAPI2AutoTests_cocoa_helpers_h
#define CPCAPI2AutoTests_cocoa_helpers_h

namespace CPCAPI2
{
namespace test
{
struct WindowAndLayerHolder
{
   void* window;
   void* layer;
};

class TestCocoaApp
{
public:
   TestCocoaApp();
   virtual ~TestCocoaApp();
   
   void startNSApplication();
   void run();
   void stopNSApplication();
   void freePool(void* pool);
   static WindowAndLayerHolder createWindow(int xPos,
                    int yPos, int width, int height,
                    const char* className);
   
   static void destroyWindow(void* vidView);
   
   static long secondsSinceLastRender(void* vidView);
   
private:
   void* mPool;

};

class TestCocoaAppScopedRunner
{
public:
   TestCocoaAppScopedRunner(TestCocoaApp& app) : mApp(app)
   {
   }

   ~TestCocoaAppScopedRunner()
   {
      mApp.stopNSApplication();
   }

private:
   TestCocoaApp& mApp;
};

}
}
#endif
