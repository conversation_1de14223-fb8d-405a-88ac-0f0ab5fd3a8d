#include "cpcapi2_test_fixture.h"
#include "test_account_events.h"
#include "test_events.h"
#include "test_call_events.h"
#include "sipua_alianza_api_test_fixture.h"

#include "test_framework/http_test_framework.h"

#include "impl/account/SipAccountManagerInternal.h"
#include "impl/account/SipAccountHandlerInternal.h"
#include "impl/account/SipAccountAwareFeature.h"

#include <sstream>

#include "util/CurlPPHelper.h"
#include "util/CurlPPProgress.h"
#include "util/CurlPPSSL.h"
#include "util/CurlURI.h"

// rapidjson
#include <writer.h>
#include <stringbuffer.h>
#include <document.h>


#include "alianza_api/interface/public/alianza_api_handler.h"
#include "alianza_api/interface/public/alianza_api_types.h"
#include "alianza_api/interface/public/alianza_api_manager.h"

#include <sstream>

#include "util/CurlPPHelper.h"
#include "util/CurlPPProgress.h"
#include "util/CurlPPSSL.h"
#include "util/CurlURI.h"

// rapidjson
#include <writer.h>
#include <stringbuffer.h>
#include <document.h>


using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::SipConversation;
using namespace std::chrono;
using namespace curlpp::options;


namespace
{
   class SipuaCallConferenceModuleTest : public CpcapiAutoTest
   {
   public:
      SipuaCallConferenceModuleTest() {}
      virtual ~SipuaCallConferenceModuleTest() {}

   };
}

TEST_F(SipuaCallConferenceModuleTest, ConferenceCall)
{
   TestAccount alice("alice");
   TestAccount bob("bob");
   TestAccount max("max");
   std::string environmentId = TestEnvironmentConfig::testEnvironmentId().c_str();

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.uri().c_str());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      assertNewConversationOutgoing(alice, aliceCall, bob.uri().c_str());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      if (environmentId == "repro")
      {
         assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
         assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendOnly);
         assertSuccess(alice.conversation->accept(aliceCall));

         assertConversationMediaChanged(alice, aliceCall, MediaDirection_ReceiveOnly);
         assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendReceive);
         assertSuccess(alice.conversation->accept(aliceCall));
      }
      else
      {
         assertConversationStateChanged(alice, aliceCall, ConversationState_Early);
         assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      }
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);

      std::this_thread::sleep_for(std::chrono::milliseconds(4000));
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.uri().c_str());
      // Bob sends 180 ringing -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      assertSuccess(bob.conversation->hold(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendOnly);

      // make an outgoing (audio only) call from Bob to Max using the demo.xten.com server
      SipConversationHandle bobCallToMax = bob.conversation->createConversation(bob.handle);
      bob.conversation->addParticipant(bobCallToMax, max.uri().c_str());
      bob.conversation->start(bobCallToMax);

      // BOB <=> MAX
      assertNewConversationOutgoing(bob, bobCallToMax, max.uri().c_str());
      assertConversationStateChanged(bob, bobCallToMax, ConversationState_RemoteRinging);
      assertConversationMediaChanged(bob, bobCallToMax, MediaDirection_SendReceive);
      if (environmentId != "repro")
      {
         assertConversationStateChanged(bob, bobCallToMax, ConversationState_Early);
      }
      assertConversationStateChanged(bob, bobCallToMax, ConversationState_Connected);

      // make this a conference by un-holding all calls
      assertSuccess(bob.conversation->unhold(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);

      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertSuccess(bob.conversation->end(bobCallToMax));
      assertConversationEnded(bob, bobCallToMax, ConversationEndReason_UserTerminatedLocally);

      // BOB <=> ALICE
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   // Overview of Max's thread:
   auto maxEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle maxCallFromBob;
      assertNewConversationIncoming(max, &maxCallFromBob, bob.uri().c_str());
      // Bob sends 180 ringing -- this should trigger state transitions for both Bob and Max
      assertSuccess(max.conversation->sendRingingResponse(maxCallFromBob));
      assertConversationStateChanged(max, maxCallFromBob, ConversationState_LocalRinging);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(max.conversation->accept(maxCallFromBob));
      assertConversationMediaChanged(max, maxCallFromBob, MediaDirection_SendReceive);
      assertConversationStateChanged(max, maxCallFromBob, ConversationState_Connected);
      assertConversationEnded(max, maxCallFromBob, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor3(aliceEvents, bobEvents, maxEvents);
}
