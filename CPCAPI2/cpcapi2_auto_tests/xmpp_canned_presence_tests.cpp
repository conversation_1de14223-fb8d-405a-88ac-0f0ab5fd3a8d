#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#if (CPCAPI2_BRAND_XMPP_ROSTER_MODULE == 1)

#include "cpcapi2_test_fixture.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>

#include "test_framework/cpcapi2_test_framework.h"

#include <thread>
#include <future>

using namespace CPCAPI2;
using namespace CPCAPI2::XmppRoster;
using namespace CPCAPI2::test;

namespace {

class XmppCannedPresenceModuleTest : public CpcapiAutoTest
{
public:
   XmppCannedPresenceModuleTest() {}
   virtual ~XmppCannedPresenceModuleTest() {}
};

// 
TEST_F(XmppCannedPresenceModuleTest, EncodeAvailable) {
	CPCAPI2::XmppRoster::PresenceType presenceType;
	CPCAPI2::XmppRoster::UserActivityGeneralType userActivityGeneralType;
	CPCAPI2::XmppRoster::UserActivitySpecificType userActivitySpecificType;
	cpc::string userActivityText;

	XmppCannedPresence::cannedStatusToPresence(CPCAPI2::XmppRoster::XmppCannedStatus_Available,
			presenceType,
			userActivityGeneralType,
			userActivitySpecificType,
			userActivityText);

	ASSERT_EQ(presenceType, CPCAPI2::XmppRoster::PresenceType_Available);
	ASSERT_EQ(userActivityGeneralType, CPCAPI2::XmppRoster::ActivityInvalidGeneralType);
	ASSERT_EQ(userActivitySpecificType, CPCAPI2::XmppRoster::ActivityInvalidSpecificType);
	ASSERT_EQ(userActivityText, "");
}

TEST_F(XmppCannedPresenceModuleTest, EncodeHavingLunch) {
	CPCAPI2::XmppRoster::PresenceType presenceType;
	CPCAPI2::XmppRoster::UserActivityGeneralType userActivityGeneralType;
	CPCAPI2::XmppRoster::UserActivitySpecificType userActivitySpecificType;
	cpc::string userActivityText;

	XmppCannedPresence::cannedStatusToPresence(CPCAPI2::XmppRoster::XmppCannedStatus_HavingLunch,
			presenceType,
			userActivityGeneralType,
			userActivitySpecificType,
			userActivityText);

	ASSERT_EQ(presenceType, CPCAPI2::XmppRoster::PresenceType_Away);
	ASSERT_EQ(userActivityGeneralType, CPCAPI2::XmppRoster::ActivityInactive);
	ASSERT_EQ(userActivitySpecificType, CPCAPI2::XmppRoster::ActivityHavingLunch);
	ASSERT_EQ(userActivityText, "");
}

TEST_F(XmppCannedPresenceModuleTest, EncodeOnThePhone) {
	CPCAPI2::XmppRoster::PresenceType presenceType;
	CPCAPI2::XmppRoster::UserActivityGeneralType userActivityGeneralType;
	CPCAPI2::XmppRoster::UserActivitySpecificType userActivitySpecificType;
	cpc::string userActivityText;

	XmppCannedPresence::cannedStatusToPresence(CPCAPI2::XmppRoster::XmppCannedStatus_OnThePhone,
			presenceType,
			userActivityGeneralType,
			userActivitySpecificType,
			userActivityText);

	ASSERT_EQ(presenceType, CPCAPI2::XmppRoster::PresenceType_DND);
	ASSERT_EQ(userActivityGeneralType, CPCAPI2::XmppRoster::ActivityTalking);
	ASSERT_EQ(userActivitySpecificType, CPCAPI2::XmppRoster::ActivityOnThePhone);
	ASSERT_EQ(userActivityText, "");
}

TEST_F(XmppCannedPresenceModuleTest, DecodeHavingLunch) {
	CPCAPI2::XmppRoster::RosterItem rosterItem;
	CPCAPI2::XmppRoster::ResourceItem resourceItem;

	resourceItem.resource = "iPhone";
	resourceItem.presenceStatusText = "Nom nom nom";
	XmppCannedPresence::cannedStatusToPresence(CPCAPI2::XmppRoster::XmppCannedStatus_HavingLunch,
			resourceItem.presenceType,
			resourceItem.userActivityGeneralType,
			resourceItem.userActivitySpecificType,
			resourceItem.userActivityText);

	rosterItem.address = "<EMAIL>";
	rosterItem.resources.push_back(resourceItem);

	XmppCannedPresence cannedPresence(rosterItem);
	ASSERT_EQ(cannedPresence.resource, "iPhone");
	ASSERT_EQ(cannedPresence.status, CPCAPI2::XmppRoster::XmppCannedStatus_HavingLunch);
	ASSERT_EQ(cannedPresence.note, "Nom nom nom");
}

TEST_F(XmppCannedPresenceModuleTest, DecodeAggregationOfHavingLunchAndOnThePhone) {
	CPCAPI2::XmppRoster::RosterItem rosterItem;
	CPCAPI2::XmppRoster::ResourceItem resourceItem1;
	CPCAPI2::XmppRoster::ResourceItem resourceItem2;

	resourceItem1.resource = "iPhone";
	resourceItem1.presenceStatusText = "Nom nom nom";
	XmppCannedPresence::cannedStatusToPresence(CPCAPI2::XmppRoster::XmppCannedStatus_HavingLunch,
			resourceItem1.presenceType,
			resourceItem1.userActivityGeneralType,
			resourceItem1.userActivitySpecificType,
			resourceItem1.userActivityText);

	resourceItem2.resource = "Mac";
	resourceItem2.presenceStatusText = "Blah blah blah";
	XmppCannedPresence::cannedStatusToPresence(CPCAPI2::XmppRoster::XmppCannedStatus_OnThePhone,
			resourceItem2.presenceType,
			resourceItem2.userActivityGeneralType,
			resourceItem2.userActivitySpecificType,
			resourceItem2.userActivityText);

	rosterItem.address = "<EMAIL>";
	rosterItem.resources.push_back(resourceItem1);
	rosterItem.resources.push_back(resourceItem2);

	XmppCannedPresence cannedPresence(rosterItem);
	ASSERT_EQ(cannedPresence.resource, "Mac");
	ASSERT_EQ(cannedPresence.status, CPCAPI2::XmppRoster::XmppCannedStatus_OnThePhone);
	ASSERT_EQ(cannedPresence.note, "Blah blah blah");
}

TEST_F(XmppCannedPresenceModuleTest, DecodeAggregationOfOnThePhoneAndHavingLunch) {
	CPCAPI2::XmppRoster::RosterItem rosterItem;
	CPCAPI2::XmppRoster::ResourceItem resourceItem1;
	CPCAPI2::XmppRoster::ResourceItem resourceItem2;

	resourceItem1.resource = "iPhone";
	resourceItem1.presenceStatusText = "Blah blah blah";
	XmppCannedPresence::cannedStatusToPresence(CPCAPI2::XmppRoster::XmppCannedStatus_OnThePhone,
			resourceItem1.presenceType,
			resourceItem1.userActivityGeneralType,
			resourceItem1.userActivitySpecificType,
			resourceItem1.userActivityText);

	resourceItem2.resource = "Mac";
	resourceItem2.presenceStatusText = "Nom nom nom";
	XmppCannedPresence::cannedStatusToPresence(CPCAPI2::XmppRoster::XmppCannedStatus_HavingLunch,
			resourceItem2.presenceType,
			resourceItem2.userActivityGeneralType,
			resourceItem2.userActivitySpecificType,
			resourceItem2.userActivityText);

	rosterItem.address = "<EMAIL>";
	rosterItem.resources.push_back(resourceItem1);
	rosterItem.resources.push_back(resourceItem2);

	XmppCannedPresence cannedPresence(rosterItem);
	ASSERT_EQ(cannedPresence.resource, "iPhone");
	ASSERT_EQ(cannedPresence.status, CPCAPI2::XmppRoster::XmppCannedStatus_OnThePhone);
	ASSERT_EQ(cannedPresence.note, "Blah blah blah");
}

TEST_F(XmppCannedPresenceModuleTest, DecodeAggregationOfHavingLunchTwice01) {
	CPCAPI2::XmppRoster::RosterItem rosterItem;
	CPCAPI2::XmppRoster::ResourceItem resourceItem1;
	CPCAPI2::XmppRoster::ResourceItem resourceItem2;

	resourceItem1.resource = "iPhone";
	resourceItem1.presenceStatusText = "Blah blah blah";
	XmppCannedPresence::cannedStatusToPresence(CPCAPI2::XmppRoster::XmppCannedStatus_HavingLunch,
			resourceItem1.presenceType,
			resourceItem1.userActivityGeneralType,
			resourceItem1.userActivitySpecificType,
			resourceItem1.userActivityText);

	resourceItem2.resource = "Mac";
	resourceItem2.presenceStatusText = "";
	XmppCannedPresence::cannedStatusToPresence(CPCAPI2::XmppRoster::XmppCannedStatus_HavingLunch,
			resourceItem2.presenceType,
			resourceItem2.userActivityGeneralType,
			resourceItem2.userActivitySpecificType,
			resourceItem2.userActivityText);

	rosterItem.address = "<EMAIL>";
	rosterItem.resources.push_back(resourceItem1);
	rosterItem.resources.push_back(resourceItem2);

	XmppCannedPresence cannedPresence(rosterItem);
	ASSERT_EQ(cannedPresence.resource, "iPhone");
	ASSERT_EQ(cannedPresence.status, CPCAPI2::XmppRoster::XmppCannedStatus_HavingLunch);
	ASSERT_EQ(cannedPresence.note, "Blah blah blah");
}

TEST_F(XmppCannedPresenceModuleTest, DecodeAggregationOfHavingLunchTwice02) {
	CPCAPI2::XmppRoster::RosterItem rosterItem;
	CPCAPI2::XmppRoster::ResourceItem resourceItem1;
	CPCAPI2::XmppRoster::ResourceItem resourceItem2;

	resourceItem1.resource = "iPhone";
	resourceItem1.presenceStatusText = "";
	XmppCannedPresence::cannedStatusToPresence(CPCAPI2::XmppRoster::XmppCannedStatus_HavingLunch,
			resourceItem1.presenceType,
			resourceItem1.userActivityGeneralType,
			resourceItem1.userActivitySpecificType,
			resourceItem1.userActivityText);

	resourceItem2.resource = "Mac";
	resourceItem2.presenceStatusText = "Blah blah blah";
	XmppCannedPresence::cannedStatusToPresence(CPCAPI2::XmppRoster::XmppCannedStatus_HavingLunch,
			resourceItem2.presenceType,
			resourceItem2.userActivityGeneralType,
			resourceItem2.userActivitySpecificType,
			resourceItem2.userActivityText);

	rosterItem.address = "<EMAIL>";
	rosterItem.resources.push_back(resourceItem1);
	rosterItem.resources.push_back(resourceItem2);

	XmppCannedPresence cannedPresence(rosterItem);
	ASSERT_EQ(cannedPresence.resource, "Mac");
	ASSERT_EQ(cannedPresence.status, CPCAPI2::XmppRoster::XmppCannedStatus_HavingLunch);
	ASSERT_EQ(cannedPresence.note, "Blah blah blah");
}

TEST_F(XmppCannedPresenceModuleTest, DecodeAggregationOfDifferentPriorities01) {
	CPCAPI2::XmppRoster::RosterItem rosterItem;
	CPCAPI2::XmppRoster::ResourceItem resourceItem1;
	CPCAPI2::XmppRoster::ResourceItem resourceItem2;
	CPCAPI2::XmppRoster::ResourceItem resourceItem3;

	resourceItem1.resource = "iPhone";
	resourceItem1.priority = 5;
	resourceItem1.presenceStatusText = "I'll be back";
	XmppCannedPresence::cannedStatusToPresence(CPCAPI2::XmppRoster::XmppCannedStatus_Away,
			resourceItem1.presenceType,
			resourceItem1.userActivityGeneralType,
			resourceItem1.userActivitySpecificType,
			resourceItem1.userActivityText);

	resourceItem2.resource = "Mac";
	resourceItem2.priority = 10;
	resourceItem2.presenceStatusText = "Blah blah blah";
	XmppCannedPresence::cannedStatusToPresence(CPCAPI2::XmppRoster::XmppCannedStatus_Available,
			resourceItem2.presenceType,
			resourceItem2.userActivityGeneralType,
			resourceItem2.userActivitySpecificType,
			resourceItem2.userActivityText);

	resourceItem3.resource = "iPad";
	resourceItem3.priority = 30;
	resourceItem3.presenceStatusText = "In a meeting";
	XmppCannedPresence::cannedStatusToPresence(CPCAPI2::XmppRoster::XmppCannedStatus_Busy,
			resourceItem3.presenceType,
			resourceItem3.userActivityGeneralType,
			resourceItem3.userActivitySpecificType,
			resourceItem3.userActivityText);

	rosterItem.address = "<EMAIL>";
	rosterItem.resources.push_back(resourceItem1);
	rosterItem.resources.push_back(resourceItem2);
	rosterItem.resources.push_back(resourceItem3);

	XmppCannedPresence cannedPresence(rosterItem);
	ASSERT_EQ(cannedPresence.resource, "iPad");
	ASSERT_EQ(cannedPresence.status, CPCAPI2::XmppRoster::XmppCannedStatus_Busy);
	ASSERT_EQ(cannedPresence.note, "In a meeting");
}

TEST_F(XmppCannedPresenceModuleTest, DecodeAggregationOfDifferentPriorities02) {
	CPCAPI2::XmppRoster::RosterItem rosterItem;
	CPCAPI2::XmppRoster::ResourceItem resourceItem1;
	CPCAPI2::XmppRoster::ResourceItem resourceItem2;
	CPCAPI2::XmppRoster::ResourceItem resourceItem3;

	resourceItem1.resource = "iPhone";
	resourceItem1.priority = 5;
	resourceItem1.presenceStatusText = "I'll be back";
	XmppCannedPresence::cannedStatusToPresence(CPCAPI2::XmppRoster::XmppCannedStatus_Away,
			resourceItem1.presenceType,
			resourceItem1.userActivityGeneralType,
			resourceItem1.userActivitySpecificType,
			resourceItem1.userActivityText);

	resourceItem2.resource = "iPad";
	resourceItem2.priority = 30;
	resourceItem2.presenceStatusText = "In a meeting";
	XmppCannedPresence::cannedStatusToPresence(CPCAPI2::XmppRoster::XmppCannedStatus_Busy,
			resourceItem2.presenceType,
			resourceItem2.userActivityGeneralType,
			resourceItem2.userActivitySpecificType,
			resourceItem2.userActivityText);

	resourceItem3.resource = "Mac";
	resourceItem3.priority = 10;
	resourceItem3.presenceStatusText = "Blah blah blah";
	XmppCannedPresence::cannedStatusToPresence(CPCAPI2::XmppRoster::XmppCannedStatus_Available,
			resourceItem3.presenceType,
			resourceItem3.userActivityGeneralType,
			resourceItem3.userActivitySpecificType,
			resourceItem3.userActivityText);

	rosterItem.address = "<EMAIL>";
	rosterItem.resources.push_back(resourceItem1);
	rosterItem.resources.push_back(resourceItem2);
	rosterItem.resources.push_back(resourceItem3);

	XmppCannedPresence cannedPresence(rosterItem);
	ASSERT_EQ(cannedPresence.resource, "iPad");
	ASSERT_EQ(cannedPresence.status, CPCAPI2::XmppRoster::XmppCannedStatus_Busy);
	ASSERT_EQ(cannedPresence.note, "In a meeting");
}

TEST_F(XmppCannedPresenceModuleTest, DecodeAggregationOfDifferentPriorities03) {
	CPCAPI2::XmppRoster::RosterItem rosterItem;
	CPCAPI2::XmppRoster::ResourceItem resourceItem1;
	CPCAPI2::XmppRoster::ResourceItem resourceItem2;
	CPCAPI2::XmppRoster::ResourceItem resourceItem3;

	resourceItem1.resource = "iPad";
	resourceItem1.priority = 30;
	resourceItem1.presenceStatusText = "In a meeting";
	XmppCannedPresence::cannedStatusToPresence(CPCAPI2::XmppRoster::XmppCannedStatus_Busy,
			resourceItem1.presenceType,
			resourceItem1.userActivityGeneralType,
			resourceItem1.userActivitySpecificType,
			resourceItem1.userActivityText);

	resourceItem2.resource = "iPhone";
	resourceItem2.priority = 5;
	resourceItem2.presenceStatusText = "I'll be back";
	XmppCannedPresence::cannedStatusToPresence(CPCAPI2::XmppRoster::XmppCannedStatus_Away,
			resourceItem2.presenceType,
			resourceItem2.userActivityGeneralType,
			resourceItem2.userActivitySpecificType,
			resourceItem2.userActivityText);

	resourceItem3.resource = "Mac";
	resourceItem3.priority = 10;
	resourceItem3.presenceStatusText = "Blah blah blah";
	XmppCannedPresence::cannedStatusToPresence(CPCAPI2::XmppRoster::XmppCannedStatus_Available,
			resourceItem3.presenceType,
			resourceItem3.userActivityGeneralType,
			resourceItem3.userActivitySpecificType,
			resourceItem3.userActivityText);

	rosterItem.address = "<EMAIL>";
	rosterItem.resources.push_back(resourceItem1);
	rosterItem.resources.push_back(resourceItem2);
	rosterItem.resources.push_back(resourceItem3);

	XmppCannedPresence cannedPresence(rosterItem);
	ASSERT_EQ(cannedPresence.resource, "iPad");
	ASSERT_EQ(cannedPresence.status, CPCAPI2::XmppRoster::XmppCannedStatus_Busy);
	ASSERT_EQ(cannedPresence.note, "In a meeting");
}

} // namespace
#endif // CPCAPI2_BRAND_XMPP_ROSTER_MODULE
