<!DOCTYPE html>
<html>
<head>
<meta name="keywords" content="<PERSON>, HTML5, JavaScript" />
<meta name="description" content="Simplest possible examples of HTML, CSS and JavaScript." />
<meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="chrome=1" />
<base target="_blank">
<title>RTCPeerConnection</title>
<style>
html { width: 100%; height: 100%; }
body { min-width: 100%; min-height: 100%; }
html {
	background-color: #D7D7D7;
}
body {
	margin:0; padding:0;
	font-family: Arial;
	background: #D7D7D7 url('images/Bkg.png') no-repeat 0 0;
	background-size: cover;
	height: 100%;
	position: fixed;
	width: 100%;
}
div#container {
	max-width: 100%;
}

h1 {
	margin:0;
	position: absolute;
	top:0;
	left:0;
	right:0;
	height: 60px;
	background: rgba(0,0,0, .3) url('images/Company_Logo.png') no-repeat 10px 50%;
	text-indent: -1000px;
}
videos {
	position: absolute;
	top:15%;
	left:50%;
	margin: 10px auto auto -180px;
	width: 360px;
}
videos.fillscreen {
	top:0;
	left:0;
	margin: auto 0;
	width: 100%;
	height: 100%;
}
videos:after {
	background: transparent url('images/Ic_Maximize.png') no-repeat 50% 50%;
	display: block;
	width: 30px; height: 30px;
	position: absolute;
	top: 9px; right: 9px;
	content: "";
	opacity: .7;
}
videos:hover:after {
	opacity: 1;
}
videos h5 {
	background: #535353;
	height: 60px;
	text-align: center;
	color: white;
	margin: -60px 0 0 0;
	font-weight: normal;
	font-size: 22px;
	line-height: 30px;
	display: none;
}
#remoteVideo {
	outline: 1px dashed #aaa;
	outline-offset: -1px;
	width: 355px;
	height: auto;
	margin: auto 0;
}
#localVideo {
	outline: 1px dashed #ccc;
	position: fixed;
	right: 5%;
	bottom: 5%;
	width: 120px;
	height: auto;
}
#controls {
	background: rgba(0,0,0,.5);
	position: absolute;
	bottom:5%;
	left:50%;
	margin-left: -114px;
	width: 228px;
	height: 77px;
	font-size: 0;
}
#controls button {
	margin:0; padding:0;
	padding-top:30px;
	height: 77px;
	width: 76px;
	border: 0;
	line-height: 65px;
	vertical-align: bottom;
	color: white;
	text-transform: lowercase;
}
#controls button[disabled] {
	opacity: .3;
}

#startButton { background: transparent url('images/Ic_StartVideo.png') no-repeat 50% 17px; }
#callButton { background: transparent url('images/Ic_Call.png') no-repeat 50% 15px; }
#hangupButton { background: transparent url('images/Ic_EndCall.png') no-repeat 50% 17px; }

</style>
</head>
<body>
<h1>CounterPath</h1>

<videos>
  <h5><b>Jack Shepard</b><br/>00:33</h5>
  <video id="localVideo" autoplay muted="true"></video>
  <video id="remoteVideo" autoplay poster="images/large-user-dummy-avatar.png"></video>
</videos>

  <div id=controls>
    <button id="startButton">Start</button>
    <button id="callButton">Call</button>
    <button id="hangupButton">Hang Up</button>
  </div>
  
  <script src="js/main.js"></script>

<div id="container"></div>
</body>
<script>
	vs=document.getElementsByTagName('videos')[0];
	vs.onclick = function(e){
		vs.classList.toggle('fillscreen');
	};
</script>
</html>
