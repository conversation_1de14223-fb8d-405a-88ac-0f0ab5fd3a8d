var socket;
var localStream;
var localPeerConnection = null;

var localVideo = document.getElementById("localVideo");
var remoteVideo = document.getElementById("remoteVideo");

var startButton = document.getElementById("startButton");
var callButton = document.getElementById("callButton");
var hangupButton = document.getElementById("hangupButton");
startButton.disabled = false;
callButton.disabled = true;
hangupButton.disabled = true;
startButton.onclick = start;
callButton.onclick = call;
hangupButton.onclick = hangup;

var total = '';
function trace(text) {
  total += text;
  console.log((performance.now() / 1000).toFixed(3) + ": " + text);
}

function gotStream(stream){
  trace("Received local stream");
  localVideo.src = URL.createObjectURL(stream);
  localStream = stream;
  callButton.disabled = false;
}

function start() {
  trace("Requesting local stream");
  startButton.disabled = true;
  navigator.getUserMedia = navigator.getUserMedia || navigator.webkitGetUserMedia || navigator.mozGetUserMedia;
  navigator.getUserMedia({audio:true,video:true}, gotStream,
    function(error) {
      trace("navigator.getUserMedia error: ", error);
    });
  
  // Create a new WebSocket.
  trace("window.location.host is " + window.location.host);
  socket = new WebSocket('ws://' + window.location.host.split(':')[0] + ':8887/');
  
  // Handle any errors that occur.
  socket.onerror = function(error) {
    console.log('WebSocket Error: ' + error);
  };

  // Show a connected message when the WebSocket is opened.
  socket.onopen = function(event) {
    trace("Connected to: " + event.currentTarget.URL);
  };
  
  var handleBye = function(message) {
    if (localPeerConnection != null) {
      callButton.disabled = false;
      hangupButton.disabled = true;
      localPeerConnection.close();
      localPeerConnection = null;
      remoteVideo.load();
    }
  };
  
  var handleAnswer = function(message) {
    trace("Incoming answer");
    var sessDesc = new mozRTCSessionDescription(JSON.parse(message));
    trace("Calling setRemoteDescription with: " + JSON.stringify(sessDesc));
    localPeerConnection.setRemoteDescription(
      sessDesc,
      function () {
        trace("setRemoteDescription success");
      },
      function (err) {
        trace("setRemoteDescription error: " + JSON.stringify(err));
      }
    );
  };
  
  var handleOffer = function(message) {
    trace("Incoming offer");
    var servers = null;
    localPeerConnection = new mozRTCPeerConnection(servers);
    localPeerConnection.addStream(localStream);
    trace("Created remote peer connection object localPeerConnection");
    localPeerConnection.onicecandidate = gotRemoteIceCandidate;
    localPeerConnection.onaddstream = gotRemoteStream;
    
    callButton.disabled = true;
    hangupButton.disabled = false;
    
    var sessDesc = new mozRTCSessionDescription(JSON.parse(message));
    trace("Calling setRemoteDescription with: " + JSON.stringify(sessDesc));
    localPeerConnection.setRemoteDescription(
      sessDesc,
      function () {
        trace("setRemoteDescription success");
        localPeerConnection.createAnswer(
          function (description) {
            localPeerConnection.setLocalDescription(description);
            trace("Answer from localPeerConnection: \n" + description.sdp);
            socket.send(JSON.stringify(description));
          },
          function (err) {
            trace("Error doing createAnswer: " + JSON.stringify(err));
          }
        );
      },
      function (err) {
        trace("setRemoteDescription error: " + JSON.stringify(err));
      }
    );
  };

  // Handle messages sent by the server.
  socket.onmessage = function(event) {
    var message = event.data;
    trace("OnMessage: " + message);
    if (message == "BYE") {
      handleBye(message);
    } else if (localPeerConnection != null) {
      handleAnswer(message);
    } else {
      handleOffer(message);
    }
  };

  // Show a disconnected message when the WebSocket is closed.
  socket.onclose = function(event) {
    if (localPeerConnection != null) {
      localPeerConnection.close();
      localPeerConnection = null;
	  remoteVideo.load();
    }
    trace("OnClose");
  };    
}

function call() {
  callButton.disabled = true;
  hangupButton.disabled = false;
  trace("Starting call");

  if (localStream.getVideoTracks().length > 0) {
    trace('Using video device: ' + localStream.getVideoTracks()[0].label);
  }
  if (localStream.getAudioTracks().length > 0) {
    trace('Using audio device: ' + localStream.getAudioTracks()[0].label);
  }

  var servers = null;

  localPeerConnection = new mozRTCPeerConnection(servers);
  trace("Created local peer connection object localPeerConnection");
  localPeerConnection.addStream(localStream);
  localPeerConnection.onicecandidate = gotLocalIceCandidate;
  localPeerConnection.onaddstream = gotRemoteStream;
  
  trace("Added localStream to localPeerConnection");
  localPeerConnection.createOffer(
    function (description){
      localPeerConnection.setLocalDescription(description);
      trace("Offer from localPeerConnection: \n" + description.sdp);
      trace("Sending SDP over websockets!");
      socket.send(JSON.stringify(description));
    }, 
    function (err) {
      error("Error doing RTC setLocalDescription:", err);
    },
    { 'mandatory': { 'OfferToReceiveAudio': true, 'OfferToReceiveVideo': true } }
  );
}

function hangup() {
  trace("Ending call");
  localPeerConnection.close();
  localPeerConnection = null;
  hangupButton.disabled = true;
  callButton.disabled = false;
  remoteVideo.load();
  
  if (socket != null) {
    socket.send("BYE");
  }
}

function gotRemoteStream(event){
  remoteVideo.src = URL.createObjectURL(event.stream);
  trace("Received remote stream");
}

function gotLocalIceCandidate(event){
  if (event.candidate) {
    //localPeerConnection.addIceCandidate(new RTCIceCandidate(event.candidate));
    trace("Local ICE candidate: \n" + event.candidate.candidate);
  }
}

function gotRemoteIceCandidate(event){
  if (event.candidate) {
    //localPeerConnection.addIceCandidate(new RTCIceCandidate(event.candidate));
    trace("Remote ICE candidate: \n " + event.candidate.candidate);
  }
}
