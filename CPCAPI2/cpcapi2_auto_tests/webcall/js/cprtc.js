var socket;
var localStream;
var localPeerConnection = null;
var activeCallId = 0;
var total = '';

var socketReady = false;
var socketPending = new Array();

function trace(text) {
  //total += text;
  //console.log((performance.now() / 1000).toFixed(3) + ": " + text);
  console.log(text);
}

function gotStream(stream) {
  trace("Received local stream");
  localStream = stream;
}

function Start() {
  trace("Requesting local stream");
  
  // Create a new WebSocket.
  var wsUri = 'ws://' + window.location.host.split(':')[0] + ':8887/';
  trace("Connecting WebSocket to " + wsUri);
  socket = new WebSocket(wsUri);
  
  // Handle any errors that occur.
  socket.onerror = function(error) {
    console.log('WebSocket Error: ' + error);
  };

  // Show a connected message when the WebSocket is opened.
  socket.onopen = function(event) {
    trace("WebSocket connected");
    socketReady = true;
    while(socketPending.length > 0) {
      var msg = socketPending.shift();
      trace("Sending queued data: " + msg);
      socket.send(msg);
    }
  };

  // Handle messages sent by the server.
  socket.onmessage = function(event) {
    var message = event.data;
    trace("OnMessage: " + message);
    if (message == "BYE") {
      if (localPeerConnection != null) {
        localPeerConnection.close();
        localPeerConnection = null;
        cprtc.fireConversationEnded(activeCallId);
      }
      return;
    }
    
    var sessDesc = new CPRTCSessionDescription(JSON.parse(message));
    if (localPeerConnection != null) {
      trace("Incoming answer");
      localPeerConnection.setRemoteDescription(sessDesc);
      cprtc.fireConversationStateChanged(activeCallId);
    } else {
      var servers = null;
      localPeerConnection = new CPRTCPeerConnection(servers);
      trace("Created local peer connection object localPeerConnection");
      var callId = cprtc.fireNewIncomingConversation(localPeerConnection, "bria@websocket", "Bria");
      trace("New incoming call with id " + callId);
      localPeerConnection.addStream(localStream);
      localPeerConnection.setRemoteDescription(
        sessDesc,
        function () {
          trace("setRemoteDescription success");
        },
        function (err) {
          trace("setRemoteDescription error: " + JSON.stringify(err));
        }
      );
    }
  };

  // Show a disconnected message when the WebSocket is closed.
  socket.onclose = function(event) {
    localPeerConnection.close();
    localPeerConnection = null;
    if (localPeerConnection != null) {
      localPeerConnection.close();
      localPeerConnection = null;
    }
    trace("OnClose");
  };    
}

function Answer(callId) {
  trace("Answering callId=" + callId);
  activeCallId = callId;
  localPeerConnection.createAnswer(
    function (description) {
      localPeerConnection.setLocalDescription(description);
      trace("Answer from localPeerConnection: \n" + description.sdp);
      socket.send(JSON.stringify(description));
      cprtc.fireConversationStateChanged(callId);
    },
    function (err) {
      trace("Error doing createAnswer: " + JSON.stringify(err));
    }
  );
}

function MakeCall(callId) {
  trace("Starting call with callId=" + callId);
  activeCallId = callId;
  
  // .jjg. idea:
  // cprtc.getConversation(callId).targetAddress (getter/setter)
  
  var servers = null;
  localPeerConnection = new CPRTCPeerConnection(servers);
  trace("Created local peer connection object localPeerConnection");
  //localPeerConnection.onicecandidate = gotLocalIceCandidate;

  localPeerConnection.addStream(localStream);
  trace("Added localStream to localPeerConnection");
  localPeerConnection.createOffer(function (description) {
    localPeerConnection.setLocalDescription(description);
    trace("Offer from localPeerConnection: \n" + description.sdp);
    var data = JSON.stringify(description);
    if(socketReady) {
      trace("Socket ready, sending data");
      socket.send(data);
    } else {
      trace("Socket not ready, queueing data");
      socketPending.push(data);
    }
  });
  cprtc.fireNewConversation(callId, localPeerConnection);
}

function Hangup(callId) {
  trace("Ending call: " + callId);
  localPeerConnection.close();
  localPeerConnection = null;
  cprtc.fireConversationEnded(callId);
  socket.send("BYE");
}

function gotRemoteStream(event){
  //remoteVideo.src = URL.createObjectURL(event.stream);
  //trace("Received remote stream");
}

function gotLocalIceCandidate(event){
}

Start();


