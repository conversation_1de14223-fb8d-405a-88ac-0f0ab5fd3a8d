<!DOCTYPE html>
<html>
<head>
  <meta charset='utf-8'>

  <title>TooTallNate/Java-WebSocket @ GitHub</title>

  <style type="text/css">
    body {
      margin-top: 1.0em;
      background-color: #3d84ba;
      font-family: Helvetica, Arial, FreeSans, san-serif;
      color: #ffffff;
    }
    #container {
      margin: 0 auto;
      width: 700px;
    }
    h1 { font-size: 3.8em; color: #c27b45; margin-bottom: 3px; }
    h1 .small { font-size: 0.4em; }
    h1 a { text-decoration: none }
    h2 { font-size: 1.5em; color: #c27b45; }
    h3 { text-align: center; color: #c27b45; }
    a { color: #c27b45; }
    .description { font-size: 1.2em; margin-bottom: 30px; margin-top: 30px; font-style: italic;}
    .download { float: right; }
    pre { background: #000; color: #fff; padding: 15px;}
    hr { border: 0; width: 80%; border-bottom: 1px solid #aaa}
    .footer { text-align:center; padding-top:30px; font-style: italic; }
  </style>
</head>

<body>
  <a href="https://github.com/TooTallNate/Java-WebSocket"><img style="position: absolute; top: 0; right: 0; border: 0;" src="http://s3.amazonaws.com/github/ribbons/forkme_right_darkblue_121621.png" alt="Fork me on GitHub" /></a>

  <div id="container">

    <div class="download">
      <a href="https://github.com/TooTallNate/Java-WebSocket/zipball/master">
        <img border="0" width="90" src="https://github.com/images/modules/download/zip.png"></a>
      <a href="https://github.com/TooTallNate/Java-WebSocket/tarball/master">
        <img border="0" width="90" src="https://github.com/images/modules/download/tar.png"></a>
    </div>

    <h1><a href="https://github.com/TooTallNate/Java-WebSocket">Java-WebSocket</a>
      <span class="small">by <a href="https://github.com/TooTallNate">TooTallNate</a></span></h1>

    <div class="description">
      A barebones WebSocket client and server implementation written in 100% Java.
    </div>

    

    

    

    

    
      <h2>Authors</h2>
      <p>Davidiusdadi (<EMAIL>)
<br/>Bob Corsaro (<EMAIL>)
<br/>Don Park (<EMAIL>)
<br/>David Rohmer (<EMAIL>)
<br/>swax (<EMAIL>)
<br/>Jarrod Ribble (<EMAIL>)
<br/>Julian Gautier (<EMAIL>)
<br/>Kristijan Sedlak (<EMAIL>)
<br/>morkai (<EMAIL>)
<br/>Nathaniel Michael (<EMAIL>)
<br/>Nathan Rajlich (<EMAIL>)
<br/>sippykup (<EMAIL>)
<br/>      </p>
    

    
      <h2>Contact</h2>
      <p>Nathan Rajlich (<EMAIL>)
<br/>      </p>
    

    <h2>Download</h2>
    <p>
      You can download this project in either
      <a href="https://github.com/TooTallNate/Java-WebSocket/zipball/master">zip</a> or
      <a href="https://github.com/TooTallNate/Java-WebSocket/tarball/master">tar formats.
    </p>
    <p>You can also clone the project with <a href="http://git-scm.com">Git</a>
      by running:
      <pre>$ git clone git://github.com/TooTallNate/Java-WebSocket</pre>
    </p>

    <div class="footer">
      get the source code on GitHub : <a href="https://github.com/TooTallNate/Java-WebSocket">TooTallNate/Java-WebSocket</a>
    </div>

  </div>

</body>
</html>
