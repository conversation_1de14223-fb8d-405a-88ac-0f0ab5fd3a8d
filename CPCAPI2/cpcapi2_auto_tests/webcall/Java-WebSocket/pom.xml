<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>org.sonatype.oss</groupId>
        <artifactId>oss-parent</artifactId>
        <version>7</version>
    </parent>
    <scm>
        <connection>scm:git:**************:TooTallNate/Java-WebSocket.git</connection>
        <developerConnection>scm:git:**************:TooTallNate/Java-WebSocket.git</developerConnection>
        <url>**************:TooTallNate/Java-WebSocket.git</url>
    </scm>
    <modelVersion>4.0.0</modelVersion>
    <groupId>org.java-websocket</groupId>
    <artifactId>Java-WebSocket</artifactId>
    <version>1.3.1-SNAPSHOT</version>
    <packaging>jar</packaging>
    <name>Java WebSocket</name>
    <url>http://java-websocket.org/</url>
    <description>A barebones WebSocket client and server implementation written in 100% Java</description>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>1.6</java.version>
    </properties>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>2.5.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>2.2.1</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>2.9</version>
                <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    <developers>
        <developer>
            <id>TooTallNate</id>
            <name>Nathan Rajlich</name>
            <email><EMAIL></email>
            <url>https://github.com/TooTallNate</url>
            <roles>
                <role>founder</role>
            </roles>
        </developer>
        <developer>
            <id>Davidiusdadi</id>
            <name>David Rohmer</name>
            <email><EMAIL></email>
            <url>https://github.com/Davidiusdadi</url>
            <roles>
                <role>maintainer</role>
            </roles>
        </developer>
    </developers>
    <licenses>
        <license>
            <name>MIT License</name>
            <url>http://github.com/TooTallNate/Java-WebSocket/blob/master/LICENSE</url>
        </license>
    </licenses>
    <profiles>                                                                                                                      
      <profile>                                                                                                                     
        <id>release-sign-artifacts</id>                                                                                             
        <activation>                                                                                                                
          <property><name>performRelease</name><value>true</value></property>                                                       
        </activation>                                                                                                               
        <build>                                                                                                                     
          <plugins>                                                                                                                 
            <plugin>                                                                                                                
              <groupId>org.apache.maven.plugins</groupId>                                                                           
              <artifactId>maven-gpg-plugin</artifactId>                                                                             
              <version>1.1</version>                                                                                                
              <executions>                                                                                                          
                <execution>                                                                                                         
                  <id>sign-artifacts</id>                                                                                           
                  <phase>verify</phase>                                                                                             
                  <goals>                                                                                                           
                    <goal>sign</goal>                                                                                               
                  </goals>                                                                                                          
                </execution>                                                                                                        
              </executions>                                                                                                         
              <configuration>                                                                                                       
                <keyname><EMAIL></keyname>                                                                                          
              </configuration>                                                                                                      
            </plugin>                                                                                                               
          </plugins>                                                                                                                
        </build>                                                                                                                    
      </profile>                                                                                                                    
    </profiles>     
</project>
