#include "cpcapi2_test_fixture.h"
#include "test_account_events.h"
#include "test_events.h"
#include "test_call_events.h"

#if (CPCAPI2_BRAND_PUSH_NOTIFICATION_SERVER_MODULE == 1)

#include "test_framework/http_test_framework.h"
#include "push_endpoint/PushNotificationEndpointManager.h"
#include "push_endpoint/PushNotificationEndpointHandler.h"
#include "push_endpoint/PushNotificationEndpointJsonApi.h"

// rapidjson
#include <writer.h>
#include <stringbuffer.h>
#include <document.h>

#include <boost/tokenizer.hpp>

#include <sstream>

using namespace CPCAPI2;
using namespace CPCAPI2::PushService;
using namespace CPCAPI2::PushEndpoint;
using namespace CPCAPI2::test;
using namespace std::chrono;

using PushService::PushNotificationRequest;
using PushService::CustomDataField;
using PushService::PushProviderSettings;
using PushEndpoint::PushNotificationRegistrationInfo;
using PushEndpoint::PushNotificationEndpointHandle;

namespace
{

   // uses SimpleWebServer from https://github.com/eidheim/Simple-Web-Server

   class PushNotificationTest : public CpcapiAutoTest
   {
   public:
      PushNotificationTest() : mListenPort(9090), mServer() {
         mServer.config.port = mListenPort;
      }
      virtual ~PushNotificationTest() {}
      
      cpc::string serverBaseUrl() const;
      
      void populatePushNotification(PushNotificationRequest& pushRequest, cpc::string messageContent);
      void configurePushServer(TestAccount& account);
      void populatePushRegistrationInfo(PushNotificationRegistrationInfo& info);
      void setupAPNServer();
      

      const int mListenPort;
      SimpleWeb::Server<SimpleWeb::HTTP> mServer;
   };
   
   cpc::string PushNotificationTest::serverBaseUrl() const
   {
      std::ostringstream ss;
      ss << "http://127.0.0.1:" << mListenPort;
      
      return ss.str().c_str();
   }
   
   std::string resourcePathRegex(const std::string& resourcePath)
   {
      std::stringstream ss;
      ss << resourcePath << "$";
      return ss.str();
   }

   void PushNotificationTest::populatePushNotification(PushNotificationRequest& pushRequest, cpc::string messageContent)
   {
      pushRequest.title_loc_key = "Incoming message from %@";
      pushRequest.title_loc_args.push_back( "Fred Flintstone" );
      
      pushRequest.customData.push_back(CustomDataField("event", "xmppIm"));
      pushRequest.customData.push_back(CustomDataField("messageId", "1"));
      pushRequest.customData.push_back(CustomDataField("fromUser", "Fred Flintstone"));
      pushRequest.customData.push_back(CustomDataField("messageContent", messageContent));
      pushRequest.customData.push_back(CustomDataField("chatHandle", (int64_t)2));
      pushRequest.customData.push_back(CustomDataField("messageHandle", (int64_t)3));
      pushRequest.customData.push_back(CustomDataField("threadId", "4"));
      pushRequest.customData.push_back(CustomDataField("toUser", "Wilma"));
      pushRequest.customData.push_back(CustomDataField("subject", "Mr. Slate"));
      pushRequest.customData.push_back(CustomDataField("isDelayedDelivery", false));
      pushRequest.customData.push_back(CustomDataField("timestamp", (uint64_t)::time(0)));
      pushRequest.customData.push_back(CustomDataField("millisecond", (uint16_t)100));
   }

   void PushNotificationTest::configurePushServer(TestAccount& account)
   {
      PushNotificationServiceManager* pushServer = CPCAPI2::PushService::PushNotificationServiceManager::getInterface(account.phone);
      PushDatabaseSettings pushDatabaseSettings;
      pushDatabaseSettings.redisIp = "pushdbsqlite";
      pushDatabaseSettings.redisPort = 9999; // special hack for the unit tests to start with a clean (fresh) database
      pushServer->configureDatabaseAccess(pushDatabaseSettings);
      PushNotificationEndpointManager* pushEndpoint = CPCAPI2::PushEndpoint::PushNotificationEndpointManager::getInterface(account.phone);
      pushEndpoint->setPushNotificationService(pushServer);
      PushProviderSettings pushProviderSettings;
      pushProviderSettings.pushNetworkType = PushEndpoint::PushNetworkType_Apple;
      pushProviderSettings.apnSettings.apnUrl = "http://127.0.0.1:9090/test";
      pushProviderSettings.apnSettings.p8file = "APNsAuthKey_462WVPCXB7.p8";
      pushProviderSettings.apnSandboxSettings.apnUrl = "invalid.";
      pushProviderSettings.apnSandboxSettings.p8file = "APNsAuthKey_462WVPCXB7.p8";
      pushServer->configurePushProvider(pushProviderSettings);
   }
   
   void PushNotificationTest::populatePushRegistrationInfo(PushNotificationRegistrationInfo& info)
   {
      info.pushNetworkType = PushEndpoint::PushNetworkType_Apple;
      info.deviceToken = "my_awesome_device"; // for API this is appended to the URL
      info.apnInfo.apnsTopic = "my-apns-topic";
      info.apnInfo.useSandbox = false;
      info.pushEndpointId = "alice:device1";
   }

   void PushNotificationTest::setupAPNServer()
   {
      const std::string resourcePath("/test/my_awesome_device");
      
      // listens for requests at /test
      mServer.resource[resourcePathRegex(resourcePath)]["POST"]=[](std::shared_ptr<HttpServer::Response> response, std::shared_ptr<HttpServer::Request> request)
      {
         // get body contents, and echo it back in the response
         
         auto content = request->content.string();
         safeCout("HTTP Server Received Message Content: " << content);
         
         std::shared_ptr<rapidjson::Document> jsonRequest(new rapidjson::Document);
         jsonRequest->Parse<0>(content.c_str());
         
         if (jsonRequest->HasParseError())
         {
            safeCout("Invalid message body format, parse error occured:" << jsonRequest->GetParseError());
            std::string failureResponse("");
            *response << "HTTP/1.1 400\r\nContent-Length: " << failureResponse.length() << "\r\n\r\n" << failureResponse;
         }
         else
         {
            if (jsonRequest->HasMember("cpc"))
            {
               if ((*jsonRequest)["cpc"].HasMember("messageContent"))
               {
                  const rapidjson::Value& messageContentVal = (*jsonRequest)["cpc"]["messageContent"];
                  if (messageContentVal.IsString())
                  {
                     std::string messageContent = messageContentVal.GetString();
                     
                     boost::tokenizer<boost::char_separator<char>> tokens (messageContent, boost::char_separator<char>(":"));
                     int count = 0;
                     std::string responseCode("");
                     std::string responseString("");
                     for (boost::tokenizer<boost::char_separator<char>>::iterator i = tokens.begin(); i != tokens.end(); ++i, count++)
                     {
                        if (count == 0)
                        {
                           responseCode = *i;
                        }
                        else if (count == 1)
                        {
                           responseString = *i;
                        }
                     }
                     
                     if (count == 2)
                     {
                        if (responseCode.compare("200") == 0)
                        {
                           *response << "HTTP/1.1 200 OK\r\nContent-Length: " << content.length() << "\r\n\r\n" << content;
                        }
                        else
                        {
                           std::string failureResponse("{\"reason\":\"");
                           failureResponse.append(responseString);
                           failureResponse.append("\"}");
                           std::string failurePrefix("HTTP/1.1 ");
                           failurePrefix.append(responseCode);
                           failurePrefix.append("r\nContent-Length: ");
                           *response << failurePrefix << failureResponse.length() << "\r\n\r\n" << failureResponse;
                        }
                     }
                     else
                     {
                        safeCout("Invalid message body, \"messageContent\" has invalid format");
                        std::string failureResponse("");
                        *response << "HTTP/1.1 400\r\nContent-Length: " << failureResponse.length() << "\r\n\r\n" << failureResponse;
                     }
                  }
                  else
                  {
                     safeCout("Invalid message body, \"messageContent\" is not a string");
                     std::string failureResponse("");
                     *response << "HTTP/1.1 400\r\nContent-Length: " << failureResponse.length() << "\r\n\r\n" << failureResponse;
                  }
               }
               else
               {
                  safeCout("Invalid message body, missing \"messageContent\" key");
                  std::string failureResponse("");
                  *response << "HTTP/1.1 400\r\nContent-Length: " << failureResponse.length() << "\r\n\r\n" << failureResponse;
               }
            }
            else
            {
               safeCout("Invalid message body, missing \"cpc\" key");
               std::string failureResponse("");
               *response << "HTTP/1.1 400\r\nContent-Length: " << failureResponse.length() << "\r\n\r\n" << failureResponse;
            }
         }
      };
   }

   TEST_F(PushNotificationTest, APNTest)
   {
      // NB: "alice" and "bob" are actually the same user (since syncing
      // really happens between different registrations of the same user)
      TestAccount alice("alice", Account_Init);
      
      const std::string resourcePath("/test/my_awesome_device");
   
      // listens for requests at /test
      mServer.resource[resourcePathRegex(resourcePath)]["POST"]=[](std::shared_ptr<HttpServer::Response> response, std::shared_ptr<HttpServer::Request> request)
      {
         // get body contents, and echo it back in the response
         
         auto content=request->content.string();
         *response << "HTTP/1.1 200 OK\r\nContent-Length: " << content.length() << "\r\n\r\n" << content;
      };
    
      thread server_thread([this]()
      {
         mServer.start();
      });
    
      // the SimpleWeb example code demonstrates waiting for 1 second to let the HTTP server start.
      // unsure if there we could instead rely on an event..
      this_thread::sleep_for(seconds(1));

      configurePushServer(alice);

      PushNotificationRegistrationInfo info;
      populatePushRegistrationInfo(info);

      PushNotificationEndpointHandle hDevice = alice.pushNotificationClientManager->createPushNotificationEndpoint();
      alice.pushNotificationClientManager->setHandler(hDevice, reinterpret_cast<PushNotificationEndpointHandler*>(0xDEADBEEF));
      alice.pushNotificationClientManager->registerForPushNotifications(hDevice, info);

      PushNotificationEndpointId alicePushEndpointId;
      {
         PushNotificationEndpointHandle h;
         PushRegistrationSuccessEvent args;
         cpcExpectEvent(alice.pushNotificationClientEvents, "PushNotificationEndpointHandler::onPushRegistrationSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, args);
         alicePushEndpointId = args.endpointId;
      }
      ASSERT_FALSE(alicePushEndpointId.empty());

      PushNotificationRequest pushRequest;
      populatePushNotification(pushRequest, "Wilmmaaaaa!!");

      alice.pushNotificationServerManager->sendPushNotification(alicePushEndpointId, pushRequest);
      auto alicePush = std::async(std::launch::async, [&] () {
         CPCAPI2::PushService::PushNotificationServiceHandle pushSvcHandle;
         CPCAPI2::PushService::NotificationSuccessEvent evt;
         ASSERT_TRUE( cpcWaitForEvent(
            alice.pushNotificationServerEvents,
            "PushNotificationServiceHandler::onNotificationSuccess",
            15000,
            AlwaysTruePred( ),
            pushSvcHandle, evt ) );
      });
      waitFor( alicePush );
      
      mServer.stop();
      server_thread.join();
    
      return;
   }
   
   TEST_F(PushNotificationTest, FCMTest)
   {
      // NB: "alice" and "bob" are actually the same user (since syncing
      // really happens between different registrations of the same user)
      TestAccount alice("alice", Account_Init);
      
      const std::string resourcePath("/test");
   
      // listens for requests at /test
      mServer.resource[resourcePathRegex(resourcePath)]["POST"]=[](std::shared_ptr<HttpServer::Response> response,
                                             std::shared_ptr<HttpServer::Request> request)
      {
         // get body contents, and echo it back in the response
         
         auto content=request->content.string();
         *response << "HTTP/1.1 200 OK\r\nContent-Length: " << content.length() << "\r\n\r\n" << content;
      };
    
      thread server_thread([this]()
      {
         mServer.start();
      });
    
      // the SimpleWeb example code demonstrates waiting for 1 second to let the HTTP server start.
      // unsure if there we could instead rely on an event..
      this_thread::sleep_for(seconds(1));
    
      /*
      PushProviderSettings settings;
      settings.pushNetworkType = PushEndpoint::PushNetworkType_FCM;
      settings.fcmSettings.fcmUrl = "https://127.0.0.1:9090/test";
      settings.fcmSettings.fcmKey = "super_long_test_key";
      alice.pushNotificationServerManager->configurePushProvider( settings );
      */
      PushNotificationServiceManager* alicePushServer = CPCAPI2::PushService::PushNotificationServiceManager::getInterface(alice.phone);
      PushDatabaseSettings pushDatabaseSettings;
      pushDatabaseSettings.redisIp = "pushdbsqlite";
      pushDatabaseSettings.redisPort = 9999; // special hack for the unit tests to start with a clean (fresh) database
      alicePushServer->configureDatabaseAccess(pushDatabaseSettings);
      PushNotificationEndpointManager* alicePushEndpoint = CPCAPI2::PushEndpoint::PushNotificationEndpointManager::getInterface(alice.phone);
      alicePushEndpoint->setPushNotificationService(alicePushServer);
      PushProviderSettings pushProviderSettings;
      pushProviderSettings.pushNetworkType = PushEndpoint::PushNetworkType_FCM;
      pushProviderSettings.fcmSettings.fcmUrl = "https://127.0.0.1:9090/test";
      pushProviderSettings.fcmSettings.fcmKey = "super_long_test_key";
      alicePushServer->configurePushProvider(pushProviderSettings);

      PushNotificationRegistrationInfo info;
      info.pushNetworkType = PushEndpoint::PushNetworkType_FCM;
      info.deviceToken = "my_awesome_device";
      info.pushEndpointId = "alice:device2";

      PushNotificationEndpointHandle hDevice = alice.pushNotificationClientManager->createPushNotificationEndpoint();
      alice.pushNotificationClientManager->setHandler(hDevice, reinterpret_cast<PushNotificationEndpointHandler*>(0xDEADBEEF));
      alice.pushNotificationClientManager->registerForPushNotifications(hDevice, info);

      PushNotificationEndpointId alicePushEndpointId;
      {
         PushNotificationEndpointHandle h;
         PushRegistrationSuccessEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.pushNotificationClientEvents, "PushNotificationEndpointHandler::onPushRegistrationSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, args));
         alicePushEndpointId = args.endpointId;
      }
      ASSERT_FALSE(alicePushEndpointId.empty());

      PushNotificationRequest pushRequest;
      populatePushNotification(pushRequest, "Wilmmaaaaa!!");

      alice.pushNotificationServerManager->sendPushNotification(alicePushEndpointId, pushRequest);
      auto alicePush = std::async(std::launch::async, [&] ()
      {
         PushService::PushNotificationServiceHandle pushSvcHandle;
         PushService::NotificationSuccessEvent evt;
         ASSERT_TRUE(cpcWaitForEvent(alice.pushNotificationServerEvents, "PushNotificationServiceHandler::onNotificationSuccess", 15000, AlwaysTruePred(), pushSvcHandle, evt));
      });
      waitFor(alicePush);
      
      mServer.stop();
      server_thread.join();
    
      return;
   }

   // Sends a REAL notification to an APN-connected device over the REAL APN network.
   // be careful to properly obtain the deviceToken and apnsTopic, apnUrl etc. before
   // enabling this. Recommend only performing manual runs of this unit test in order
   // to test actual client functionality (for the time being)
   TEST_F( PushNotificationTest, DISABLED_MockXMPPAgentOutage )
   {
      // NB: "alice" and "bob" are actually the same user (since syncing
      // really happens between different registrations of the same user)
      TestAccount alice("alice", Account_Init);
      
      // note: PushNotificationClientManager and PushNotificationServerManager are actually instances of the same SDK
      // module underneath; as such, if we have a CPCAPI2::test::EventHandler for each, they end up stealing each other's
      // events. for now, work around this by removing the handler for events we are not interested in.
      delete alice.pushNotificationClientEvents;
      alice.pushNotificationClientEvents = NULL;

      PushProviderSettings settings;
      settings.pushNetworkType       = PushEndpoint::PushNetworkType_Apple;
      settings.apnSettings.apnUrl    = "https://api.development.push.apple.com:443/3/device/";
      settings.apnSettings.p8file    = "APNsAuthKey_462WVPCXB7.p8"; // must be present in unit test directory.
      settings.apnSettings.authKeyId = "462WVPCXB7";
      settings.apnSettings.teamId    = "UQC9N9AMZM";

      PushNotificationRegistrationInfo info;
      info.pushNetworkType = settings.pushNetworkType;
      //info.deviceToken = "9ec75aaf3f1b4c19b9586bfb9b033a0f16f208104d83713d9c330d1afab33b3b"; // Need to replace this with the ACTUAL device ID
      info.apnInfo.apnsTopic = "com.counterpath.Advanced-Audio-Call-APN";
      info.pushEndpointId = "alice:device1";

      PushDatabaseSettings pushDatabaseSettings;
      pushDatabaseSettings.redisIp = "pushdbsqlite";
      pushDatabaseSettings.redisPort = 9999; // special hack for the unit tests to start with a clean (fresh) database
      alice.pushNotificationServerManager->configureDatabaseAccess(pushDatabaseSettings);
      PushNotificationEndpointManager* alicePushEndpoint = CPCAPI2::PushEndpoint::PushNotificationEndpointManager::getInterface(alice.phone);
      alicePushEndpoint->setPushNotificationService(alice.pushNotificationServerManager);

      alice.pushNotificationServerManager->configurePushProvider( settings );
      PushNotificationEndpointHandle hDevice = alice.pushNotificationClientManager->createPushNotificationEndpoint();
      alice.pushNotificationClientManager->registerForPushNotifications(hDevice, info);

      // Build a document from the event contents.
      PushNotificationRequest pushRequest;
      pushRequest.customData.push_back( CustomDataField("event", "service"));
      pushRequest.customData.push_back( CustomDataField("state", "Stopped"));
      pushRequest.customData.push_back( CustomDataField("reasonCode", 2)); // ReasonCode_Unknown. This is only hardcoded to avoid dependencies on the service notifications module
      pushRequest.customData.push_back( CustomDataField("serviceName", "XMPP_AGENT"));

      // Send the notification. Normally this would be sent broadcast, but since we
      // have to register the device anyways, we might as well use the direct form.
      alice.pushNotificationServerManager->sendPushNotification( info.pushEndpointId, pushRequest ); // !jjg! fixme: this unit test needs updating to get the actual push endpoint id from onPushRegistrationSuccess
      auto alicePush = std::async(std::launch::async, [&] ()
      {
         PushService::PushNotificationServiceHandle pushSvcHandle;
         PushService::NotificationSuccessEvent evt;
         ASSERT_TRUE(cpcWaitForEvent(alice.pushNotificationServerEvents, "PushNotificationServiceHandler::onNotificationSuccess", 15000, AlwaysTruePred(), pushSvcHandle, evt));
      });
      waitFor(alicePush);
      
      mServer.stop();
      return;
   }

   TEST_F(PushNotificationTest, APNTest_ExpiredDeviceToken)
   {
      TestAccount alice("alice", Account_Init);

      setupAPNServer();
      thread server_thread([this]()
      {
         mServer.start();
      });
      
      this_thread::sleep_for(seconds(1));

      configurePushServer(alice);

      PushNotificationRegistrationInfo info;
      populatePushRegistrationInfo(info);

      PushNotificationEndpointHandle hDevice = alice.pushNotificationClientManager->createPushNotificationEndpoint();
      alice.pushNotificationClientManager->setHandler(hDevice, reinterpret_cast<PushNotificationEndpointHandler*>(0xDEADBEEF));
      alice.pushNotificationClientManager->registerForPushNotifications(hDevice, info);

      PushNotificationEndpointId alicePushEndpointId;
      {
         PushNotificationEndpointHandle h;
         PushRegistrationSuccessEvent args;
         cpcExpectEvent(alice.pushNotificationClientEvents, "PushNotificationEndpointHandler::onPushRegistrationSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, args);
         alicePushEndpointId = args.endpointId;
      }
      ASSERT_FALSE(alicePushEndpointId.empty());

      PushNotificationRequest successfulPushRequest;
      populatePushNotification(successfulPushRequest, "200:OK");

      alice.pushNotificationServerManager->sendPushNotification(alicePushEndpointId, successfulPushRequest);
      auto alicePushSuccess = std::async(std::launch::async, [&] ()
      {
         CPCAPI2::PushService::PushNotificationServiceHandle pushSvcHandle;
         CPCAPI2::PushService::NotificationSuccessEvent evt;
         ASSERT_TRUE(cpcWaitForEvent(alice.pushNotificationServerEvents, "PushNotificationServiceHandler::onNotificationSuccess", 15000, AlwaysTruePred(), pushSvcHandle, evt));
      });
      waitFor(alicePushSuccess);

      PushNotificationRequest failedPushRequest;
      populatePushNotification(failedPushRequest, "410:Unregistered");

      alice.pushNotificationServerManager->sendPushNotification(alicePushEndpointId, failedPushRequest);
      auto alicePushFailure = std::async(std::launch::async, [&] ()
      {
         CPCAPI2::PushService::PushNotificationServiceHandle pushSvcHandle;
         CPCAPI2::PushService::NotificationFailureEvent evt;
         ASSERT_TRUE(cpcWaitForEvent(alice.pushNotificationServerEvents, "PushNotificationServiceHandler::onNotificationFailure", 15000, AlwaysTruePred(), pushSvcHandle, evt));
         ASSERT_EQ(evt.device, alicePushEndpointId);
         ASSERT_EQ(evt.reasonCode, ReasonCode_Unregistered);
         ASSERT_EQ(evt.statusCode, StatusCode_ExpiredDeviceToken);
      });
      waitFor(alicePushFailure);

      mServer.stop();
      server_thread.join();

      return;
   }
}

#endif
