/*
*  Copyright (c) 2012 The WebRTC project authors. All Rights Reserved.
*
*  Use of this source code is governed by a BSD-style license
*  that can be found in the LICENSE file in the root of the source
*  tree. An additional intellectual property rights grant can be found
*  in the file PATENTS.  All contributing project authors may
*  be found in the AUTHORS file in the root of the source tree.
*/

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wc++11-narrowing"
#endif

#include <memory>

#include "gmock/gmock.h"
#include "gtest/gtest.h"
#include "cpcapi2_test_framework.h"

#include <AudioToolbox/AudioToolbox.h>
#include <IOKit/audio/IOAudioTypes.h>

#include "audio_device_mac_new.h"

#include "webrtc/modules/audio_device/test/func_test_manager.h"


// Interface for processing the audio stream. Real implementations can e.g.
// run audio in loopback, read audio from a file or perform latency
// measurements.
class AudioStreamInterface {
 public:
  virtual void Write(const void* source, int num_frames) = 0;
  virtual void Read(void* destination, int num_frames) = 0;
    virtual ~AudioStreamInterface() {}
 protected:

};


// Wait for the callback sequence to stabilize by ignoring this amount of the
// initial callbacks (avoids initial FIFO access).
// Only used in the RunPlayoutAndRecordingInFullDuplex test.
static const int kNumIgnoreFirstCallbacks = 50;

class FifoAudioStream : public AudioStreamInterface {
 public:
  explicit FifoAudioStream(int frames_per_buffer)
      : frames_per_buffer_(frames_per_buffer),
        bytes_per_buffer_(frames_per_buffer_ * sizeof(int16_t)),
        fifo_(new AudioBufferList),
        largest_size_(0),
        total_written_elements_(0),
        write_count_(0) {
    EXPECT_NE(fifo_.get(), nullptr);
  }

  ~FifoAudioStream() {
    safeCout("Total written " << total_written_elements_ << " largest_size_ " << largest_size_ << " write_count_ " << write_count_);
    Flush();
  }

  // Allocate new memory, copy |num_frames| samples from |source| into memory
  // and add pointer to the memory location to end of the list.
  // Increases the size of the FIFO by one element.
  void Write(const void* source, int num_frames) override {
    ASSERT_EQ(num_frames, frames_per_buffer_);
    if (write_count_++ < kNumIgnoreFirstCallbacks) {
      return;
    }
    int16_t* memory = new int16_t[frames_per_buffer_];
    memcpy(static_cast<int16_t*> (&memory[0]),
           source,
           bytes_per_buffer_);
    std::lock_guard lock(lock_);
    fifo_->push_back(memory);
    const int size = fifo_->size();
    if (size > largest_size_) {
      largest_size_ = size;
      safeCout("Largest is " << largest_size_)
    }
    total_written_elements_ += size;
  }

  // Read pointer to data buffer from front of list, copy |num_frames| of stored
  // data into |destination| and delete the utilized memory allocation.
  // Decreases the size of the FIFO by one element.
  void Read(void* destination, int num_frames) override {
    ASSERT_EQ(num_frames, frames_per_buffer_);
    std::lock_guard gl(lock_);
    if (fifo_->empty()) {
      memset(destination, 0, bytes_per_buffer_);
    } else {
      int16_t* memory = fifo_->front();
      fifo_->pop_front();
      memcpy(destination,
             static_cast<int16_t*> (&memory[0]),
             bytes_per_buffer_);
      delete memory;
    }
  }

  int size() const {
    return fifo_->size();
  }

  int largest_size() const {
    return largest_size_;
  }

  int average_size() const {
    return (total_written_elements_ == 0) ? 0.0 : 0.5 + static_cast<float> (
      total_written_elements_) / (write_count_ - kNumIgnoreFirstCallbacks);
  }

 private:
  void Flush() {
    for (auto it = fifo_->begin(); it != fifo_->end(); ++it) {
      delete *it;
    }
    fifo_->clear();
  }

  using AudioBufferList = std::list<int16_t*>;
  std::mutex lock_;
  // rtc::CriticalSection lock_;
  const int frames_per_buffer_;
  const int bytes_per_buffer_;
  rtc::scoped_ptr<AudioBufferList> fifo_;
  int largest_size_;
  int total_written_elements_;
  int write_count_;
};


class AudioTransportAPI: public webrtc::AudioTransport {
 public:
  AudioTransportAPI()
      : rec_count_(0),
        play_count_(0) {
          audio_stream_ = new FifoAudioStream(480);
  }

  ~AudioTransportAPI() {
    delete audio_stream_;
  }

  virtual int32_t RecordedDataIsAvailable(
      const void* audioSamples,
      const uint32_t nSamples,
      const uint8_t nBytesPerSample,
      const uint8_t nChannels,
      const uint32_t sampleRate,
      const uint32_t totalDelay,
      const int32_t clockSkew,
      const uint32_t currentMicLevel,
      const bool keyPressed,
      uint32_t& newMicLevel) {
    rec_count_++;
    if (rec_count_ % 100 == 0) {
      if (nChannels == 1) {
        // mono
        safeCout("-");
      } else if ((nChannels == 2) && (nBytesPerSample == 2)) {
        // stereo but only using one channel
        safeCout("-|");
      } else {
        // stereo
        safeCout("--");
      }
    }
    if (audio_stream_) {
      audio_stream_->Write(audioSamples, nSamples);
    }
    return 0;
  }

  virtual int32_t NeedMorePlayData(
      const uint32_t nSamples,
      const uint8_t nBytesPerSample,
      const uint8_t nChannels,
      const uint32_t sampleRate,
      void* audioSamples,
      uint32_t& nSamplesOut,
      int64_t* elapsed_time_ms,
      int64_t* ntp_time_ms) {
    play_count_++;
    if (play_count_ % 100 == 0) {
      if (nChannels == 1) {
        safeCout("+");
      } else {
        safeCout("++");
      }
    }
    nSamplesOut = nSamples;
    if (audio_stream_) {
      audio_stream_->Read(audioSamples, nSamples);
    }


    return 0;
  }

  virtual int OnDataAvailable(const int voe_channels[],
                              int number_of_voe_channels,
                              const int16_t* audio_data,
                              int sample_rate,
                              int number_of_channels,
                              int number_of_frames,
                              int audio_delay_milliseconds,
                              int current_volume,
                              bool key_pressed,
                              bool need_audio_processing) {
    return 0;
  }

  virtual void PushCaptureData(int voe_channel, const void* audio_data,
                               int bits_per_sample, int sample_rate,
                               int number_of_channels,
                               int number_of_frames) {}

  virtual void PullRenderData(int bits_per_sample, int sample_rate,
                              int number_of_channels, int number_of_frames,
                              void* audio_data,
                              int64_t* elapsed_time_ms,
                              int64_t* ntp_time_ms) {}
 public:
  uint32_t rec_count_;
  uint32_t play_count_;
  AudioStreamInterface* audio_stream_;
};

AudioDeviceID get_default(UInt32 deviceType) {
  UInt32 size;
  size = sizeof(AudioDeviceID);
  AudioDeviceID result;

	OSStatus error = AudioHardwareGetProperty(deviceType,
											&size,
											&result);
	if (0 != error) {
		throw std::runtime_error("Can't get default device");
	}
	return result;
}
namespace webrtc {
	namespace {

		using ::testing::_;
		using ::testing::AllOf;
		using ::testing::ElementsAreArray;
		using ::testing::Field;
		using ::testing::IsEmpty;
		using ::testing::NaggyMock;
		using ::testing::NiceMock;
		using ::testing::Property;
		using ::testing::SizeIs;
		using ::testing::StrEq;
		using ::testing::StrictMock;
		using ::testing::UnorderedElementsAre;


	}  // namespace

	class СromiumLikeTest : public ::testing::Test {
	public:
		AudioDeviceID default_input;
		AudioDeviceID default_output;
	protected:
		СromiumLikeTest() {}
		void SetUp() {
			default_input = get_default(kAudioHardwarePropertyDefaultInputDevice);
			default_output = get_default(kAudioHardwarePropertyDefaultOutputDevice);
		}

	


	};

	TEST_F(СromiumLikeTest, Test) {
	  auto number_of_seconds = 5;
    webrtc::AudioDeviceBuffer audioDeviceBuffer;
    AudioTransportAPI audioCallback;
    ASSERT_EQ(0, audioDeviceBuffer.RegisterAudioCallback(&audioCallback));

    safeCout("Default input device is: " << default_input);
    safeCout("Default output device is: " << default_output);

    
    auto device_out = webrtc::AudioDeviceMacNew(default_output, false);
    auto device_in = webrtc::AudioDeviceMacNew(default_input, true);
    device_in.SetRecordingDevice(0);
    device_out.SetPlayoutDevice(0);
    device_out.AttachAudioBuffer(&audioDeviceBuffer);
    device_in.AttachAudioBuffer(&audioDeviceBuffer);
    device_out.Init();
    device_in.Init();
    ASSERT_EQ(0, device_out.InitPlayout());
    ASSERT_EQ(0, device_in.InitRecording());
    ASSERT_EQ(0, device_out.StartPlayout());
    ASSERT_EQ(0, device_in.StartRecording());
    std::this_thread::sleep_for(std::chrono::seconds(number_of_seconds));
    ASSERT_EQ(0, device_out.StopPlayout());
    ASSERT_EQ(0, device_in.StopRecording());
    ASSERT_EQ(0, device_in.Terminate());
    ASSERT_EQ(0, device_out.Terminate());
    ASSERT_GT(audioCallback.play_count_, 0);
    ASSERT_GT(audioCallback.rec_count_, 0);
	}


}  // namespace webrtc

#ifdef __clang__
#pragma clang diagnostic pop
#endif
