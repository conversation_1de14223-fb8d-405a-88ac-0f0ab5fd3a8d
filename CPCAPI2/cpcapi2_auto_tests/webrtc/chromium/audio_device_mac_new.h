/*
 *  Copyright (c) 2012 The WebRTC project authors. All Rights Reserved.
 *
 *  Use of this source code is governed by a BSD-style license
 *  that can be found in the LICENSE file in the root of the source
 *  tree. An additional intellectual property rights grant can be found
 *  in the file PATENTS.  All contributing project authors may
 *  be found in the AUTHORS file in the root of the source tree.
 */

#ifndef WEBRTC_AUDIO_DEVICE_AUDIO_DEVICE_IPHONE_H
#define WEBRTC_AUDIO_DEVICE_AUDIO_DEVICE_IPHONE_H

#include <objc/objc.h>
#include <AudioUnit/AudioUnit.h>
#import <pthread.h>

#include "webrtc/base/scoped_ptr.h"
#include "webrtc/modules/audio_device/audio_device_generic.h"
#include "webrtc/system_wrappers/interface/critical_section_wrapper.h"


namespace webrtc {
class ThreadWrapper;


const int kBufferSizeMs = 10;


const uint32_t N_REC_SAMPLES_PER_SEC = 48000;
const uint32_t N_PLAY_SAMPLES_PER_SEC = 48000;

const uint32_t N_REC_CHANNELS = 1;  // default is mono recording
const uint32_t N_PLAY_CHANNELS = 2;  // default is mono playout
const uint32_t N_DEVICE_CHANNELS = 64;
const uint32_t ENGINE_REC_BUF_SIZE_IN_SAMPLES =
    N_REC_SAMPLES_PER_SEC * kBufferSizeMs / 1000;
const uint32_t ENGINE_PLAY_BUF_SIZE_IN_SAMPLES =
    N_PLAY_SAMPLES_PER_SEC * kBufferSizeMs / 1000;

// Number of 10 ms recording blocks in recording buffer
const uint16_t N_REC_BUFFERS = 20;
	
// class AudioDeviceMacNew : public AudioDeviceGeneric {
class AudioDeviceMacNew  {
public:
    AudioDeviceMacNew(const int32_t id, bool is_input);
    ~AudioDeviceMacNew();
    virtual int32_t Init();
    virtual int32_t Terminate();
    virtual int32_t SetPlayoutDevice(uint16_t index);
    virtual int32_t SetRecordingDevice(uint16_t index);
    virtual int32_t InitPlayout();
    virtual int32_t InitRecording();
    virtual int32_t StartPlayout();
    virtual int32_t StopPlayout();
    virtual int32_t StartRecording();
    virtual int32_t StopRecording();

    virtual void AttachAudioBuffer(AudioDeviceBuffer* audioBuffer);


    int32_t InitRecordingInternal();
    int32_t InitPlayOrRecord();
    int32_t ShutdownPlayOrRecord();
    void UpdatePlayoutDelay();

    static OSStatus RecordProcess(void *inRefCon,
                                  AudioUnitRenderActionFlags *ioActionFlags,
                                  const AudioTimeStamp *timeStamp,
                                  UInt32 inBusNumber,
                                  UInt32 inNumberFrames,
                                  AudioBufferList *ioData);

    static OSStatus PlayoutProcess(void *inRefCon,
                                   AudioUnitRenderActionFlags *ioActionFlags,
                                   const AudioTimeStamp *timeStamp,
                                   UInt32 inBusNumber,
                                   UInt32 inNumberFrames,
                                   AudioBufferList *ioData);

    OSStatus RecordProcessImpl(AudioUnitRenderActionFlags *ioActionFlags,
                               const AudioTimeStamp *timeStamp,
                               uint32_t inBusNumber,
                               uint32_t inNumberFrames);

    OSStatus PlayoutProcessImpl(uint32_t inNumberFrames,
                                AudioBufferList *ioData);

    static bool RunCapture(void* ptrThis);
    bool CaptureWorkerThread();
    
private:
    AudioDeviceBuffer* _ptrAudioBuffer;

    CriticalSectionWrapper& _critSect;

    rtc::scoped_ptr<ThreadWrapper> _captureWorkerThread;
    uint32_t _captureWorkerThreadId;

    int32_t _id;
    bool _is_input;
    AudioUnit _auVoiceProcessing;
    
private:
    bool _initialized;
    bool _isShutDown;
    bool _recording;
    bool _playing;
    bool _recIsInitialized;
    bool _playIsInitialized;

    bool _recordingDeviceIsSpecified;
    bool _playoutDeviceIsSpecified;
    bool _micIsInitialized;
    bool _speakerIsInitialized;

    bool _AGC;

    // The sampling rate to use with Audio Device Buffer
    uint32_t _adbSampFreq;

    // Delay calculation
    uint32_t _recordingDelay;
    uint32_t _playoutDelay;
    uint32_t _playoutDelayMeasurementCounter;
    uint32_t _recordingDelayHWAndOS;
    uint32_t _recordingDelayMeasurementCounter;
    uint32_t _auLatency;

    // Errors and warnings count
    uint16_t _playWarning;
    uint16_t _playError;
    uint16_t _recWarning;
    uint16_t _recError;

    // Playout buffer, needed for 44.0 / 44.1 kHz mismatch
    int16_t _playoutBuffer[ENGINE_PLAY_BUF_SIZE_IN_SAMPLES];
    uint32_t  _playoutBufferUsed;  // How much is filled

    // Recording buffers
    int16_t
        _recordingBuffer[N_REC_BUFFERS][ENGINE_REC_BUF_SIZE_IN_SAMPLES];
    uint32_t _recordingLength[N_REC_BUFFERS];
    uint32_t _recordingSeqNumber[N_REC_BUFFERS];
    uint32_t _recordingCurrentSeq;

    // Current total size all data in buffers, used for delay estimate
    uint32_t _recordingBufferTotalSize;
    
    
    bool _interrupted;
    bool _interruptedByNativeCall;
    float _lastNativeCallTimeSecs;
    bool _micEnable;
    bool _speakerEnabled;
    bool _speakerMuted;
	
    long _interruptedRecordingDriftMs;
    int64_t _interruptedRecordingTimestamp;

    bool _softwareMicVolumeEnabled;
    float _softwareMicVolumeGain;
	
    bool _waitingAudioSessionIsActive;
    bool _audioSessionActivated;
    int _playAndRecordModeCntr;
    bool _appIsInBackground;
    bool _handlesAudioInterruptions;
    
    dispatch_queue_t _dispatchQueue;
    dispatch_block_t _activateBlock;
    pthread_mutex_t _activateMutex;
};

}  // namespace webrtc

#endif  // MODULES_AUDIO_DEVICE_MAIN_SOURCE_MAC_AUDIO_DEVICE_IPHONE_H_
