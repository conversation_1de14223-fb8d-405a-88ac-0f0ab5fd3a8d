#include "audio_device_mac_new.h"
#include <CoreAudio/CoreAudio.h>
#include "webrtc/base/checks.h"
#include "webrtc/system_wrappers/interface/thread_wrapper.h"
#include "webrtc/system_wrappers/interface/trace.h"
#include "webrtc/system_wrappers/interface/clock.h"


namespace webrtc {

   AudioDeviceMacNew::AudioDeviceMacNew(const int32_t id, bool is_input)
   :
   _ptrAudioBuffer(NULL),
   _critSect(*CriticalSectionWrapper::CreateCriticalSection()),
   _captureWorkerThreadId(0),
   _id(id),
   _is_input(is_input),
   _auVoiceProcessing(NULL),
   _initialized(false),
   _isShutDown(false),
   _recording(false),
   _playing(false),
   _recIsInitialized(false),
   _playIsInitialized(false),
   _recordingDeviceIsSpecified(false),
   _playoutDeviceIsSpecified(false),
   _micIsInitialized(false),
   _speakerIsInitialized(false),
   _AGC(false),
   _adbSampFreq(0),
   _recordingDelay(0),
   _playoutDelay(0),
   _playoutDelayMeasurementCounter(9999),
   _recordingDelayHWAndOS(0),
   _recordingDelayMeasurementCounter(9999),
   _playWarning(0),
   _playError(0),
   _recWarning(0),
   _recError(0),
   _playoutBufferUsed(0),
   _recordingCurrentSeq(0),
   _recordingBufferTotalSize(0),
   _interrupted(false),
   _interruptedByNativeCall(false),
   _lastNativeCallTimeSecs(0),
   _micEnable(true),
   _speakerEnabled(false),
   _interruptedRecordingDriftMs(0),
   _interruptedRecordingTimestamp(0),
   _softwareMicVolumeEnabled(false),
   _softwareMicVolumeGain(1.0f),
   _waitingAudioSessionIsActive(false),
   _audioSessionActivated(false),
   _playAndRecordModeCntr(0),
   _handlesAudioInterruptions(true),
   _dispatchQueue(NULL),
   _activateBlock(NULL)
   {
      WEBRTC_TRACE(kTraceMemory, kTraceAudioDevice, id,
                   "%s created", __FUNCTION__);
      
      memset(_playoutBuffer, 0, sizeof(_playoutBuffer));
      memset(_recordingBuffer, 0, sizeof(_recordingBuffer));
      memset(_recordingLength, 0, sizeof(_recordingLength));
      memset(_recordingSeqNumber, 0, sizeof(_recordingSeqNumber));
      
    //   _state = [[AudioDeviceIOSState alloc] init];
      _dispatchQueue = dispatch_queue_create("AudioDeviceMacNew", DISPATCH_QUEUE_SERIAL);
      pthread_mutex_init(&_activateMutex, NULL);
   }

   AudioDeviceMacNew::~AudioDeviceMacNew() {
      WEBRTC_TRACE(kTraceMemory, kTraceAudioDevice, _id,
                   "%s destroyed", __FUNCTION__);
      
      Terminate();
      _dispatchQueue = NULL;
      
      delete &_critSect;
   }

void AudioDeviceMacNew::AttachAudioBuffer(AudioDeviceBuffer* audioBuffer)
{

    CriticalSectionScoped lock(&_critSect);

    _ptrAudioBuffer = audioBuffer;

    // inform the AudioBuffer about default settings for this implementation
    _ptrAudioBuffer->SetRecordingSampleRate(N_REC_SAMPLES_PER_SEC);
    _ptrAudioBuffer->SetPlayoutSampleRate(N_PLAY_SAMPLES_PER_SEC);
    _ptrAudioBuffer->SetRecordingChannels(N_REC_CHANNELS);
 
    _ptrAudioBuffer->SetPlayoutChannels(N_PLAY_CHANNELS);
}
   int32_t AudioDeviceMacNew::Init() 
{

    CriticalSectionScoped lock(&_critSect);

    if (_initialized)
    {
        return 0;
    }


    _isShutDown = false;


    _playWarning = 0;
    _playError = 0;
    _recWarning = 0;
    _recError = 0;
    // get_mic_volume_counter_ms_ = 0;
    _initialized = true;

    return 0;
}
   
   int32_t AudioDeviceMacNew::Terminate() {
      WEBRTC_TRACE(kTraceModuleCall, kTraceAudioDevice, _id,
                     "%s", __FUNCTION__);
      if (!_initialized) {
         return -1;
      }
      
      // Shut down Audio Unit
      ShutdownPlayOrRecord();
      


      
      _isShutDown = true;
      _initialized = false;
      _speakerIsInitialized = false;
      _micIsInitialized = false;
      _playoutDeviceIsSpecified = false;
      _recordingDeviceIsSpecified = false;
      return 0;
   } 


     int32_t AudioDeviceMacNew::InitPlayOrRecord() {
        // return -1;
      WEBRTC_TRACE(kTraceModuleCall, kTraceAudioDevice, _id, "%s", __FUNCTION__);
      
      OSStatus result = -1;
    //   NSError *err = nil;

      // Check if already initialized
      if (NULL != _auVoiceProcessing) {
         // We already have initialized before and created any of the audio unit,
         // check that all exist
         WEBRTC_TRACE(kTraceInfo, kTraceAudioDevice, _id,
                      "  Already initialized");
         // todo: Call AudioUnitReset() here and empty all buffers?
         return 0;
      }
      
//       AVAudioSession *audioSession = [AVAudioSession sharedInstance];
      
//       if(_playAndRecordModeCntr == 0)
//       {
//          [[NSNotificationCenter defaultCenter] postNotificationName:kCpcAudioDeviceStateWillChange object:_state userInfo:@{kActive: @YES}];
         
//          SetPlayAndRecordAudioCategory();
//       }
//       else if([AVAudioSession sharedInstance].category != AVAudioSessionCategoryPlayAndRecord)
//       {
//          WEBRTC_TRACE(kTraceModuleCall, kTraceAudioDevice, _id, "Retrying Audio Category setup...");
//          SetPlayAndRecordAudioCategory();
//       }
      
//       if(![audioSession setInputGain:1.0 error:&err])
//       {
//          WEBRTC_TRACE(kTraceDebug, kTraceAudioDevice, _id, "audioSession setInputGain: %ld", err.code);
//          err = nil;
//       }
      
//       if(_interrupted)
//       {
//          _interrupted = false;
//          [[NSNotificationCenter defaultCenter] postNotificationName:kCpcAudioInterruption object:_state userInfo:@{kInterrupted: @(_interrupted)}];
//       }


      // Create Voice Processing Audio Unit
      AudioComponentDescription desc;
      AudioComponent comp;
      
      desc.componentType = kAudioUnitType_Output;
    //   desc.componentSubType = kAudioUnitSubType_VoiceProcessingIO;
      desc.componentSubType =  kAudioUnitSubType_HALOutput;
      desc.componentManufacturer = kAudioUnitManufacturer_Apple;
      desc.componentFlags = 0;
      desc.componentFlagsMask = 0;
      
      comp = AudioComponentFindNext(NULL, &desc);
      if (NULL == comp) {
         WEBRTC_TRACE(kTraceError, kTraceAudioDevice, _id,
                      "  Could not find audio component for Audio Unit");
         return -1;
      }
      
      result = AudioComponentInstanceNew(comp, &_auVoiceProcessing);
      if (0 != result) {
         WEBRTC_TRACE(kTraceError, kTraceAudioDevice, _id,
                      "  Could not create Audio Unit instance (result=%d)",
                      result);
         return -1;
      }
      
    //   // Set preferred hardware sample rate to 48 kHz
    //   AdjustSampleRate();
      
      Float64 sampleRate = 48000.0;
     
      //////////////////////
      // Setup Voice Processing Audio Unit
      
      // Note: For Signal Processing AU element 0 is output bus, element 1 is
      //       input bus for global scope element is irrelevant (always use
      //       element 0)
      
      // Enable IO on both elements
      
      // todo: Below we just log and continue upon error. We might want
      //       to close AU and return error for some cases.
      // todo: Log info about setup.
      
    //   UInt32 enableIO = (!_appIsInBackground || _playAndRecordModeCntr > 0) ? 1: 0;
      UInt32 enableIO = _is_input;
      result = AudioUnitSetProperty(_auVoiceProcessing,
                                    kAudioOutputUnitProperty_EnableIO,
                                    kAudioUnitScope_Input,
                                    1,  // input bus
                                    &enableIO,
                                    sizeof(enableIO));
      if (0 != result) {
         WEBRTC_TRACE(kTraceError, kTraceAudioDevice, _id,
                      "  Could not enable IO on input (result=%d)", result);
      }
      
      WEBRTC_TRACE(kTraceModuleCall, kTraceAudioDevice, _id,
                   "  Input bus is %d", enableIO);
      
      enableIO = !_is_input;
      result = AudioUnitSetProperty(_auVoiceProcessing,
                                    kAudioOutputUnitProperty_EnableIO,
                                    kAudioUnitScope_Output,
                                    0,   // output bus
                                    &enableIO,
                                    sizeof(enableIO));
      if (0 != result) {
         WEBRTC_TRACE(kTraceError, kTraceAudioDevice, _id,
                      "  Could not enable IO on output (result=%d)", result);
      }
      

        result = AudioUnitSetProperty(
      _auVoiceProcessing, kAudioOutputUnitProperty_CurrentDevice,
      kAudioUnitScope_Global, 0, &_id, sizeof(AudioDeviceID));
            if (0 != result) {
         WEBRTC_TRACE(kTraceError, kTraceAudioDevice, _id,
                      "  Set current device (result=%d)", result);
        return -1;
      }
      // Disable AU buffer allocation for the recorder, we allocate our own
      UInt32 flag = 0;
      result = AudioUnitSetProperty(
                                    _auVoiceProcessing, kAudioUnitProperty_ShouldAllocateBuffer,
                                    kAudioUnitScope_Output,  1, &flag, sizeof(flag));
      if (0 != result) {
         WEBRTC_TRACE(kTraceWarning, kTraceAudioDevice, _id,
                      "  Could not disable AU buffer allocation (result=%d)",
                      result);
         // Should work anyway
      }
      
      // Set recording callback
      AURenderCallbackStruct auCbS;
      memset(&auCbS, 0, sizeof(auCbS));
      auCbS.inputProc = RecordProcess;
      auCbS.inputProcRefCon = this;
      result = AudioUnitSetProperty(_auVoiceProcessing,
                                    kAudioOutputUnitProperty_SetInputCallback,
                                    kAudioUnitScope_Global, 1,
                                    &auCbS, sizeof(auCbS));
      if (0 != result) {
         WEBRTC_TRACE(kTraceError, kTraceAudioDevice, _id,
                      "  Could not set record callback for Audio Unit (result=%d)",
                      result);
      }
      
      // Set playout callback
      memset(&auCbS, 0, sizeof(auCbS));
      auCbS.inputProc = PlayoutProcess;
      auCbS.inputProcRefCon = this;
      // std::cout << _auVoiceProcessing->maximumFramesToRender << std::endl;
      result = AudioUnitSetProperty(_auVoiceProcessing,
                                    kAudioUnitProperty_SetRenderCallback,
                                    kAudioUnitScope_Global, 0,
                                    &auCbS, sizeof(auCbS));
      if (0 != result) {
         WEBRTC_TRACE(kTraceError, kTraceAudioDevice, _id,
                      "  Could not set play callback for Audio Unit (result=%d)",
                      result);
      }
      
      // Get stream format for out/0
      AudioStreamBasicDescription playoutDesc;
      UInt32 size = sizeof(playoutDesc);
      result = AudioUnitGetProperty(_auVoiceProcessing,
                                    kAudioUnitProperty_StreamFormat,
                                    kAudioUnitScope_Output, 0, &playoutDesc,
                                    &size);
      if (0 != result) {
         WEBRTC_TRACE(kTraceError, kTraceAudioDevice, _id,
                      "  Could not get stream format Audio Unit out/0 (result=%d)",
                      result);
      }
      WEBRTC_TRACE(kTraceModuleCall, kTraceAudioDevice, _id,
                   "  Audio Unit playout opened in sampling rate %f",
                   playoutDesc.mSampleRate);
      
      playoutDesc.mSampleRate = sampleRate;
      _adbSampFreq = sampleRate;
      std::cout << "SAMPLE RATE IS " << _adbSampFreq << std::endl;
      /*
      // Store the sampling frequency to use towards the Audio Device Buffer
      // todo: Add 48 kHz (increase buffer sizes). Other fs?
      if ((playoutDesc.mSampleRate > 44090.0)
          && (playoutDesc.mSampleRate < 44110.0)) {
         _adbSampFreq = 44000;
      } else if ((playoutDesc.mSampleRate > 15990.0)
                 && (playoutDesc.mSampleRate < 16010.0)) {
         _adbSampFreq = 16000;
      } else if ((playoutDesc.mSampleRate > 7990.0)
                 && (playoutDesc.mSampleRate < 8010.0)) {
         _adbSampFreq = 8000;
      } else {
         _adbSampFreq = 0;
         WEBRTC_TRACE(kTraceError, kTraceAudioDevice, _id,
                      "  Audio Unit out/0 opened in unknown sampling rate (%f)",
                      playoutDesc.mSampleRate);
         // todo: We should bail out here.
      }
      */
      
      // Set the audio device buffer sampling rate,
      // we assume we get the same for play and record
      if (_ptrAudioBuffer->SetRecordingSampleRate(_adbSampFreq) < 0) {
         WEBRTC_TRACE(kTraceError, kTraceAudioDevice, _id,
                      "  Could not set audio device buffer recording sampling rate (%d)",
                      _adbSampFreq);
      }
      
      if (_ptrAudioBuffer->SetPlayoutSampleRate(_adbSampFreq) < 0) {
         WEBRTC_TRACE(kTraceError, kTraceAudioDevice, _id,
                      "  Could not set audio device buffer playout sampling rate (%d)",
                      _adbSampFreq);
      }
      
      // Set stream format for in/0  (use same sampling frequency as for out/0)
      playoutDesc.mFormatFlags = kLinearPCMFormatFlagIsSignedInteger
      | kLinearPCMFormatFlagIsPacked
      | kLinearPCMFormatFlagIsNonInterleaved;
      playoutDesc.mBytesPerPacket = 2;
      playoutDesc.mFramesPerPacket = 1;
      playoutDesc.mBytesPerFrame = 2;
      playoutDesc.mChannelsPerFrame = 1;
      playoutDesc.mBitsPerChannel = 16;
      result = AudioUnitSetProperty(_auVoiceProcessing,
                                    kAudioUnitProperty_StreamFormat,
                                    kAudioUnitScope_Input, 0, &playoutDesc, size);
      if (0 != result) {
         WEBRTC_TRACE(kTraceError, kTraceAudioDevice, _id,
                      "  Could not set stream format Audio Unit in/0 (result=%d)",
                      result);
      }
      
      // Get stream format for in/1
      AudioStreamBasicDescription recordingDesc;
      size = sizeof(recordingDesc);
      result = AudioUnitGetProperty(_auVoiceProcessing,
                                    kAudioUnitProperty_StreamFormat,
                                    kAudioUnitScope_Input, 1, &recordingDesc,
                                    &size);
      if (0 != result) {
         WEBRTC_TRACE(kTraceError, kTraceAudioDevice, _id,
                      "  Could not get stream format Audio Unit in/1 (result=%d)",
                      result);
      }
      WEBRTC_TRACE(kTraceModuleCall, kTraceAudioDevice, _id,
                   "  Audio Unit recording opened in sampling rate %f",
                   recordingDesc.mSampleRate);
      
      recordingDesc.mSampleRate = sampleRate;
      
      // Set stream format for out/1 (use same sampling frequency as for in/1)
      recordingDesc.mFormatFlags = kLinearPCMFormatFlagIsSignedInteger
      | kLinearPCMFormatFlagIsPacked
      | kLinearPCMFormatFlagIsNonInterleaved;
      
      recordingDesc.mBytesPerPacket = 2;
      recordingDesc.mFramesPerPacket = 1;
      recordingDesc.mBytesPerFrame = 2;
      recordingDesc.mChannelsPerFrame = 1;
      recordingDesc.mBitsPerChannel = 16;
      result = AudioUnitSetProperty(_auVoiceProcessing,
                                    kAudioUnitProperty_StreamFormat,
                                    kAudioUnitScope_Output, 1, &recordingDesc,
                                    size);
      if (0 != result) {
         WEBRTC_TRACE(kTraceError, kTraceAudioDevice, _id,
                      "  Could not set stream format Audio Unit out/1 (result=%d)",
                      result);
      }
      
//      if(!SYSTEM_VERSION_LESS_THAN(@"10"))
//      {
//         UInt32 maxFramesPerSlice = 4096;
//         result = AudioUnitSetProperty(_auVoiceProcessing, kAudioUnitProperty_MaximumFramesPerSlice, kAudioUnitScope_Global, 0, &maxFramesPerSlice, sizeof(maxFramesPerSlice));
//         
//         if (0 != result) {
//            WEBRTC_TRACE(kTraceError, kTraceAudioDevice, _id,
//                         "  Could not set MaximumFramesPerSlice Audio Unit 0 (result=%d)",
//                         result);
//         }
//         
//         result  = AudioUnitSetProperty(_auVoiceProcessing, kAudioUnitProperty_MaximumFramesPerSlice, kAudioUnitScope_Global, 1, &maxFramesPerSlice, sizeof(maxFramesPerSlice));
//         
//         if (0 != result) {
//            WEBRTC_TRACE(kTraceError, kTraceAudioDevice, _id,
//                         "  Could not set MaximumFramesPerSlice Audio Unit 1 (result=%d)",
//                         result);
//         }
//      }

      // Initialize here already to be able to get/set stream properties.
      result = AudioUnitInitialize(_auVoiceProcessing);
      if (0 != result) {
         WEBRTC_TRACE(kTraceError, kTraceAudioDevice, _id,
                      "  Could not init Audio Unit (result=%d)", result);
        return -1;
      }
      
    //   // Get hardware sample rate for logging (see if we get what we asked for)
    //   double hardwareSampleRate = audioSession.sampleRate;
    //   WEBRTC_TRACE(kTraceModuleCall, kTraceAudioDevice, _id,
    //                "  Current HW sample rate is %f, ADB sample rate is %d",
    //                hardwareSampleRate, _adbSampFreq);
      
      // AU latency
      Float64 f64(0);
      size = sizeof(f64);
      result = AudioUnitGetProperty(_auVoiceProcessing,
                                    kAudioUnitProperty_Latency, kAudioUnitScope_Global, 0, &f64, &size);
      if (0 != result) {
         WEBRTC_TRACE(kTraceError, kTraceAudioDevice, _id,
                      "error AU latency (result=%d)", result);
      }
      _auLatency = static_cast<uint32_t>(f64 * 1000000);
      
//      SetMicStatus(_micEnable);
      
      if(_playAndRecordModeCntr == 0)
      {
        //  if(![audioSession setActive:YES error:&err])
        //  {
        //     WEBRTC_TRACE(kTraceWarning, kTraceAudioDevice, _id, "audioSession setActive: %ld", err.code);
        //     err = nil;
        //  }
      }
      else if(!_audioSessionActivated)
      {
         _waitingAudioSessionIsActive = true;
      }
		 
      return 0;
   }


      OSStatus
   AudioDeviceMacNew::RecordProcess(void *inRefCon,
                                    AudioUnitRenderActionFlags *ioActionFlags,
                                    const AudioTimeStamp *inTimeStamp,
                                    UInt32 inBusNumber,
                                    UInt32 inNumberFrames,
                                    AudioBufferList *ioData) {
      AudioDeviceMacNew* ptrThis = static_cast<AudioDeviceMacNew*>(inRefCon);
      
      return ptrThis->RecordProcessImpl(ioActionFlags,
                                        inTimeStamp,
                                        inBusNumber,
                                        inNumberFrames);
   }
   
   
   OSStatus
   AudioDeviceMacNew::RecordProcessImpl(
                                        AudioUnitRenderActionFlags *ioActionFlags,
                                        const AudioTimeStamp *inTimeStamp,
                                        uint32_t inBusNumber,
                                        uint32_t inNumberFrames) {
      if(!_recording)
         return 0;
      
      // Setup some basic stuff
      // Use temp buffer not to lock up recording buffer more than necessary
      // todo: Make dataTmp a member variable with static size that holds
      //       max possible frames?
      int16_t* dataTmp = new int16_t[inNumberFrames];
      memset(dataTmp, 0, 2*inNumberFrames);
      
      AudioBufferList abList;
      abList.mNumberBuffers = 1;
      abList.mBuffers[0].mData = dataTmp;
      abList.mBuffers[0].mDataByteSize = 2*inNumberFrames;  // 2 bytes/sample
      abList.mBuffers[0].mNumberChannels = 1;
      
      if(_micEnable)
      {
         // Get data from mic
         OSStatus res = AudioUnitRender(_auVoiceProcessing,
                                        ioActionFlags, inTimeStamp,
                                        inBusNumber, inNumberFrames, &abList);
         if (res != 0) {
            WEBRTC_TRACE(kTraceWarning, kTraceAudioDevice, _id,
                         "  Error getting rec data, error = %d", res);
            
            if (_recWarning > 0) {
               WEBRTC_TRACE(kTraceWarning, kTraceAudioDevice, _id,
                            "  Pending rec warning exists");
            }
            _recWarning = 1;
            
            delete [] dataTmp;
            return 0;
         }
      }
      
      // Insert all data in temp buffer into recording buffers
      // There is zero or one buffer partially full at any given time,
      // all others are full or empty
      // Full means filled with noSamp10ms samples.
      
      const unsigned int noSamp10ms = _adbSampFreq / 100;
      unsigned int dataPos = 0;
      uint16_t bufPos = 0;
      int16_t insertPos = -1;
      unsigned int nCopy = 0;  // Number of samples to copy
      
      while (dataPos < inNumberFrames) {
         // Loop over all recording buffers or
         // until we find the partially full buffer
         // First choice is to insert into partially full buffer,
         // second choice is to insert into empty buffer
         bufPos = 0;
         insertPos = -1;
         nCopy = 0;
         while (bufPos < N_REC_BUFFERS) {
            if ((_recordingLength[bufPos] > 0)
                && (_recordingLength[bufPos] < noSamp10ms)) {
               // Found the partially full buffer
               insertPos = static_cast<int16_t>(bufPos);
               // Don't need to search more, quit loop
               bufPos = N_REC_BUFFERS;
            } else if ((-1 == insertPos)
                       && (0 == _recordingLength[bufPos])) {
               // Found an empty buffer
               insertPos = static_cast<int16_t>(bufPos);
            }
            ++bufPos;
         }
         
         // Insert data into buffer
         if (insertPos > -1) {
            // We found a non-full buffer, copy data to it
            unsigned int dataToCopy = inNumberFrames - dataPos;
            unsigned int currentRecLen = _recordingLength[insertPos];
            unsigned int roomInBuffer = noSamp10ms - currentRecLen;
            nCopy = (dataToCopy < roomInBuffer ? dataToCopy : roomInBuffer);
            
            // <CP>
            if( _softwareMicVolumeEnabled )
            {
            //    // Apply software scaling (if any) to the samples.
            //    ScaleWithSat( _softwareMicVolumeGain, &dataTmp[dataPos], nCopy );
            }
            // </CP>

            memcpy(&_recordingBuffer[insertPos][currentRecLen],
                   &dataTmp[dataPos], nCopy*sizeof(int16_t));
            if (0 == currentRecLen) {
               _recordingSeqNumber[insertPos] = _recordingCurrentSeq;
               ++_recordingCurrentSeq;
            }
            _recordingBufferTotalSize += nCopy;
            // Has to be done last to avoid interrupt problems
            // between threads
            _recordingLength[insertPos] += nCopy;
            dataPos += nCopy;
         } else {
            // Didn't find a non-full buffer
            WEBRTC_TRACE(kTraceWarning, kTraceAudioDevice, _id,
                         "  Could not insert into recording buffer");
            if (_recWarning > 0) {
               WEBRTC_TRACE(kTraceWarning, kTraceAudioDevice, _id,
                            "  Pending rec warning exists");
            }
            _recWarning = 1;
            dataPos = inNumberFrames;  // Don't try to insert more
         }
      }
      
      delete [] dataTmp;
      
      return 0;
   }

   OSStatus
   AudioDeviceMacNew::PlayoutProcess(void *inRefCon,
                                     AudioUnitRenderActionFlags *ioActionFlags,
                                     const AudioTimeStamp *inTimeStamp,
                                     UInt32 inBusNumber,
                                     UInt32 inNumberFrames,
                                     AudioBufferList *ioData) {
      AudioDeviceMacNew* ptrThis = static_cast<AudioDeviceMacNew*>(inRefCon);
      
      return ptrThis->PlayoutProcessImpl(inNumberFrames, ioData);
   }
   

      OSStatus
   AudioDeviceMacNew::PlayoutProcessImpl(uint32_t inNumberFrames,
                                         AudioBufferList *ioData) {
      // Setup some basic stuff
      //    assert(sizeof(short) == 2); // Assumption for implementation
      
      int16_t* data =
      static_cast<int16_t*>(ioData->mBuffers[0].mData);
      unsigned int dataSizeBytes = ioData->mBuffers[0].mDataByteSize;
      unsigned int dataSize = dataSizeBytes/2;  // Number of samples
      // std::cout << "dataSizeBytes " << dataSizeBytes << " dataSize " << dataSize << std::endl;
      if (dataSize != inNumberFrames) {  // Should always be the same
         WEBRTC_TRACE(kTraceWarning, kTraceAudioDevice, _id,
                      "dataSize (%u) != inNumberFrames (%u)",
                      dataSize, (unsigned int)inNumberFrames);
         if (_playWarning > 0) {
            WEBRTC_TRACE(kTraceWarning, kTraceAudioDevice, _id,
                         "  Pending play warning exists");
         }
         _playWarning = 1;
      }
      memset(data, 0, dataSizeBytes);  // Start with empty buffer
      
      
      // Get playout data from Audio Device Buffer
      
      if (_playing) {
         unsigned int noSamp10ms = _adbSampFreq / 100;
         // unsigned int noSamp10ms = _adbSampFreq / 10;
         // todo: Member variable and allocate when samp freq is determined
         // int16_t* dataTmp = new int16_t[noSamp10ms];
         // PATCHED
         int16_t* dataTmp = new int16_t[dataSizeBytes];
         memset(dataTmp, 0, 2*noSamp10ms);
         unsigned int dataPos = 0;
         int noSamplesOut = 0;
         unsigned int nCopy = 0;
         
         // First insert data from playout buffer if any
         if (_playoutBufferUsed > 0) {
            nCopy = (dataSize < _playoutBufferUsed) ?
            dataSize : _playoutBufferUsed;
            if (nCopy != _playoutBufferUsed) {
               // todo: If dataSize < _playoutBufferUsed
               //       (should normally never be)
               //       we must move the remaining data
               WEBRTC_TRACE(kTraceWarning, kTraceAudioDevice, _id,
                            "nCopy (%u) != _playoutBufferUsed (%u)",
                            nCopy, _playoutBufferUsed);
               if (_playWarning > 0) {
                  WEBRTC_TRACE(kTraceWarning, kTraceAudioDevice, _id,
                               "  Pending play warning exists");
               }
               _playWarning = 1;
            }
            if (!_speakerMuted)
               memcpy(data, _playoutBuffer, 2*nCopy);
            dataPos = nCopy;
            memset(_playoutBuffer, 0, sizeof(_playoutBuffer));
            _playoutBufferUsed = 0;
         }
         
         // Now get the rest from Audio Device Buffer
         while (dataPos < dataSize) {
            // // Update playout delay
            UpdatePlayoutDelay();
            
            // Ask for new PCM data to be played out using the AudioDeviceBuffer
            noSamplesOut = _ptrAudioBuffer->RequestPlayoutData(noSamp10ms);
            
            // Get data from Audio Device Buffer
            noSamplesOut =
            _ptrAudioBuffer->GetPlayoutData(
                                            reinterpret_cast<int8_t*>(dataTmp));
            // Cast OK since only equality comparison
            if (noSamp10ms != (unsigned int)noSamplesOut) {
               // Should never happen
               WEBRTC_TRACE(kTraceWarning, kTraceAudioDevice, _id,
                            "noSamp10ms (%u) != noSamplesOut (%d)",
                            noSamp10ms, noSamplesOut);
               
               if (_playWarning > 0) {
                  WEBRTC_TRACE(kTraceWarning, kTraceAudioDevice, _id,
                               "  Pending play warning exists");
               }
               _playWarning = 1;
            }
            
            // Insert as much as fits in data buffer
            nCopy = (dataSize-dataPos) > noSamp10ms ?
            noSamp10ms : (dataSize-dataPos);
            if (!_speakerMuted)
               memcpy(&data[dataPos], dataTmp, 2*nCopy);
            
            // Save rest in playout buffer if any
            if (nCopy < noSamp10ms) {
               if (!_speakerMuted)
                  memcpy(_playoutBuffer, &dataTmp[nCopy], 2*(noSamp10ms-nCopy));
               _playoutBufferUsed = noSamp10ms - nCopy;
            }
            
            // Update loop/index counter, if we copied less than noSamp10ms
            // samples we shall quit loop anyway
            dataPos += noSamp10ms;
         }
         
         delete [] dataTmp;
      }
      
      return 0;
   }

      void AudioDeviceMacNew::UpdatePlayoutDelay() {
      ++_playoutDelayMeasurementCounter;
      
      if (_playoutDelayMeasurementCounter >= 100) {
         // Update HW and OS delay every second, unlikely to change
         
         uint32_t playoutDelay = 0;

// [AB] disabled due to possible few hundred ms delays in outputLatency and IOBufferDuration calls
//         AVAudioSession *audioSession = [AVAudioSession sharedInstance];
//         
//         // HW output latency
//         playoutDelay += static_cast<int>(audioSession.outputLatency * 1000000);
//         
//         // HW buffer duration
//         playoutDelay += static_cast<int>(audioSession.IOBufferDuration * 1000000);
         
         // AU latency
         playoutDelay += _auLatency;
         
         // To ms
         _playoutDelay = playoutDelay > 500 ? (playoutDelay - 500) / 1000: 0;
         
         // Reset counter
         _playoutDelayMeasurementCounter = 0;
      }
      
      // todo: Add playout buffer? (Only used for 44.1 kHz)
   }

    int32_t AudioDeviceMacNew::InitRecording() {
      WEBRTC_TRACE(kTraceModuleCall, kTraceAudioDevice, _id, "%s", __FUNCTION__);
       CriticalSectionScoped lock(&_critSect);
       return InitRecordingInternal();
   }
   int32_t AudioDeviceMacNew::SetRecordingDevice(uint16_t index) {
//      CriticalSectionScoped lock(&_critSect);
      
      WEBRTC_TRACE(kTraceModuleCall, kTraceAudioDevice, _id,
                   "AudioDeviceMacNew::SetRecordingDevice(index=%u)", index);
      
         if (_recIsInitialized) {
            WEBRTC_TRACE(kTraceError, kTraceAudioDevice, _id,
                         "  Recording already initialized");
            return -1;
         }
         
         if (index !=0) {
            WEBRTC_TRACE(kTraceWarning, kTraceAudioDevice, _id,
                         "  SetRecordingDevice invalid index");
            return -1;
         }
         
         _recordingDeviceIsSpecified = true;
         
         return 0;
   }
   

      int32_t AudioDeviceMacNew::InitRecordingInternal() {

      if (!_initialized) {
         WEBRTC_TRACE(kTraceError, kTraceAudioDevice, _id,
                      "  Not initialized");
         return -1;
      }
      
      if (_recording) {
         WEBRTC_TRACE(kTraceWarning, kTraceAudioDevice, _id,
                      "  Recording already started");
         return -1;
      }
      
      if (_recIsInitialized) {
         WEBRTC_TRACE(kTraceInfo, kTraceAudioDevice, _id,
                      "  Recording already initialized");
         return 0;
      }
      
      if (!_recordingDeviceIsSpecified) {
         WEBRTC_TRACE(kTraceError, kTraceAudioDevice, _id,
                      "  Recording device is not specified");
         return -1;
      }
      
      // Initialize the microphone
//      if (InitMicrophone() == -1) {
//         WEBRTC_TRACE(kTraceWarning, kTraceAudioDevice, _id,
//                      "  InitMicrophone() failed");
//      }
      _micIsInitialized = true;
      
      _recIsInitialized = true;
      
      if (!_playIsInitialized) {
         // Audio init
         if (InitPlayOrRecord() == -1) {
            // todo: Handle error
            WEBRTC_TRACE(kTraceWarning, kTraceAudioDevice, _id,
                         "  InitPlayOrRecord() failed");
         }
      } else {
         WEBRTC_TRACE(kTraceInfo, kTraceAudioDevice, _id,
                      "  Playout already initialized - InitPlayOrRecord() " \
                      "not called");
      }
      
      return 0;
   }


   int32_t AudioDeviceMacNew::StartRecording()
{
    WEBRTC_TRACE(kTraceApiCall, kTraceAudioDevice, _id,
                 "StartRecording()");
    

    CriticalSectionScoped lock(&_critSect);

    if (!_recIsInitialized)
    {
        return -1;
    }

    if (_recording)
    {
        return 0;
    }

    if (!_initialized)
    {
        WEBRTC_TRACE(kTraceError, kTraceAudioDevice, _id,
                     " Recording worker thread has not been started");
        return -1;
    }

    DCHECK(!_captureWorkerThread.get());
    _captureWorkerThread =
        ThreadWrapper::CreateThread(RunCapture, this, "CaptureWorkerThread");
    DCHECK(_captureWorkerThread.get());
    _captureWorkerThread->Start();
    _captureWorkerThread->SetPriority(kRealtimePriority);

      // Reset recording buffer
      memset(_recordingBuffer, 0, sizeof(_recordingBuffer));
      memset(_recordingLength, 0, sizeof(_recordingLength));
      memset(_recordingSeqNumber, 0, sizeof(_recordingSeqNumber));
      _recordingCurrentSeq = 0;
      _recordingBufferTotalSize = 0;
      _recordingDelay = 0;
      _recordingDelayHWAndOS = 0;
      // Make sure first call to update delay function will update delay
      _recordingDelayMeasurementCounter = 9999;
      _recWarning = 0;
      _recError = 0;
      
      if(_waitingAudioSessionIsActive)
      {
         WEBRTC_TRACE(kTraceInfo, kTraceAudioDevice, _id,
                      "  Waiting audio session is active");
         return 0;
      }
      
      if (!_playing) {
         // Start Audio Unit
         WEBRTC_TRACE(kTraceModuleCall, kTraceAudioDevice, _id,
                      "  Starting Audio Unit");
         
         OSStatus result = AudioOutputUnitStart(_auVoiceProcessing);
         if (0 != result) {
            WEBRTC_TRACE(kTraceCritical, kTraceAudioDevice, _id,
                           "  Error starting Audio Unit (result=%d)", result);
            return -1;
         }
         
      } else {
         // Try to restart audio unit, if audio bus is disabled an app is in foreground
         
         UInt32 enableIO = 0, enableIOSize = sizeof(enableIO);
         OSStatus result = AudioUnitGetProperty(_auVoiceProcessing,
                                       kAudioOutputUnitProperty_EnableIO,
                                       kAudioUnitScope_Input,
                                       1, // input bus
                                       &enableIO, &enableIOSize);
         if (0 != result) {
            WEBRTC_TRACE(kTraceWarning, kTraceAudioDevice, _id,
            "Failed to get kAudioOutputUnitProperty_EnableIO (result=%d)", result  );
         
         } else if (enableIO == 0 && !_appIsInBackground) {
            // try to restart audio with recording enabled
            WEBRTC_TRACE(kTraceModuleCall, kTraceAudioDevice, _id,
                         "  Restarting Audio Unit");
            
            result = AudioOutputUnitStop(_auVoiceProcessing);
            
            if (0 != result) {
               WEBRTC_TRACE(kTraceWarning, kTraceAudioDevice, _id,
               "AudioOutputUnitStop failed (result=%d)", result  );
            }
            
            result = AudioUnitUninitialize(_auVoiceProcessing);
            
            if (0 != result) {
               WEBRTC_TRACE(kTraceWarning, kTraceAudioDevice, _id,
               "AudioUnitUninitialize failed (result=%d)", result  );
            }
            
            enableIO = 1;
            result = AudioUnitSetProperty(_auVoiceProcessing,
                                          kAudioOutputUnitProperty_EnableIO,
                                          kAudioUnitScope_Input,
                                          1,  // input bus
                                          &enableIO,
                                          sizeof(enableIO));
            if (0 != result) {
               WEBRTC_TRACE(kTraceWarning, kTraceAudioDevice, _id,
               "Failed to set kAudioOutputUnitProperty_EnableIO (result=%d)", result  );
            }
            
            result = AudioUnitInitialize(_auVoiceProcessing);
            
            if (0 != result) {
               WEBRTC_TRACE(kTraceWarning, kTraceAudioDevice, _id,
               "AudioUnitInitialize failed (result=%d)", result  );
            }
            
            result = AudioOutputUnitStart(_auVoiceProcessing);
            if (0 != result) {
               WEBRTC_TRACE(kTraceWarning, kTraceAudioDevice, _id,
               "AudioOutputUnitStart failed (result=%d)", result  );
            }
         }
      }
      
    _recording = true;
    return 0;
}


   bool AudioDeviceMacNew::RunCapture(void* ptrThis) {
      return static_cast<AudioDeviceMacNew*>(ptrThis)->CaptureWorkerThread();
   }
   
   bool AudioDeviceMacNew::CaptureWorkerThread() {
      if (_recording || _waitingAudioSessionIsActive) {
         int bufPos = 0;
         unsigned int lowestSeq = 0;
         int lowestSeqBufPos = 0;
         bool foundBuf = true;
         const unsigned int noSamp10ms = _adbSampFreq / 100;
         
         while (foundBuf) {
            // Check if we have any buffer with data to insert
            // into the Audio Device Buffer,
            // and find the one with the lowest seq number
            foundBuf = false;
            for (bufPos = 0; bufPos < N_REC_BUFFERS; ++bufPos) {
               if (noSamp10ms == _recordingLength[bufPos]) {
                  if (!foundBuf) {
                     lowestSeq = _recordingSeqNumber[bufPos];
                     lowestSeqBufPos = bufPos;
                     foundBuf = true;
                  } else if (_recordingSeqNumber[bufPos] < lowestSeq) {
                     lowestSeq = _recordingSeqNumber[bufPos];
                     lowestSeqBufPos = bufPos;
                  }
               }
            }  // for
            
            // Insert data into the Audio Device Buffer if found any
            if (foundBuf) {
            //    // Update recording delay
            //    UpdateRecordingDelay();
               
               // Set the recorded buffer
               _ptrAudioBuffer->SetRecordedBuffer(
                                                  reinterpret_cast<int8_t*>(
                                                                            _recordingBuffer[lowestSeqBufPos]),
                                                  _recordingLength[lowestSeqBufPos]);
               
               // Don't need to set the current mic level in ADB since we only
               // support digital AGC,
               // and besides we cannot get or set the IOS mic level anyway.
               
               // Set VQE info, use clockdrift == 0
               _ptrAudioBuffer->SetVQEData(_playoutDelay, _recordingDelay, 0);
               
               // Deliver recorded samples at specified sample rate, mic level
               // etc. to the observer using callback
               _ptrAudioBuffer->DeliverRecordedData();
               
               // Make buffer available
               _recordingSeqNumber[lowestSeqBufPos] = 0;
               _recordingBufferTotalSize -= _recordingLength[lowestSeqBufPos];
               // Must be done last to avoid interrupt problems between threads
               _recordingLength[lowestSeqBufPos] = 0;
               
               _interruptedRecordingTimestamp = 0;
               _interruptedRecordingDriftMs = 0;
            }
            else if(_interrupted || _waitingAudioSessionIsActive)
            {
               long sleepMs = 0;

               int64_t current = Clock::GetRealTimeClock()->CurrentNtpInMilliseconds();
               
               if(_interruptedRecordingTimestamp == 0)
               {
                  memset(_recordingBuffer[0], 0, ENGINE_REC_BUF_SIZE_IN_SAMPLES * sizeof(int16_t));
                  sleepMs = 10;
               }
               else
               {
                  int64_t pastMs = current - _interruptedRecordingTimestamp;
                  _interruptedRecordingDriftMs += pastMs - 10;
                  
                  while (_interruptedRecordingDriftMs >= 10)
                  {
                     _ptrAudioBuffer->SetRecordedBuffer(reinterpret_cast<int8_t*>(_recordingBuffer[0]), noSamp10ms);
                     _ptrAudioBuffer->DeliverRecordedData();
                     _interruptedRecordingDriftMs -= 10;
                  }
                  
                  sleepMs = 10 - _interruptedRecordingDriftMs;
               }

               //fprintf(stderr, "!!!! slp=%ld, delta=%lld, drift=%ld\n", sleepMs, current - timestamp, driftMs);

               _interruptedRecordingTimestamp = current;

               _ptrAudioBuffer->SetRecordedBuffer(reinterpret_cast<int8_t*>(_recordingBuffer[0]), noSamp10ms);
               _ptrAudioBuffer->DeliverRecordedData();
               
               
               if(sleepMs > 1)
               {
                  timespec t;
                  t.tv_sec = 0;
                  t.tv_nsec = sleepMs * 1000 * 1000;
                  nanosleep(&t, NULL);
               }
               
               return true;
            }
         }  // while (foundBuf)
      }  // if (_recording)
      
      {
         // Normal case
         // Sleep thread (5ms) to let other threads get to work
         // todo: Is 5 ms optimal? Sleep shorter if inserted into the Audio
         //       Device Buffer?
         timespec t;
         t.tv_sec = 0;
         t.tv_nsec = 5*1000*1000;
         nanosleep(&t, NULL);
      }
      
      return true;
   }


      int32_t AudioDeviceMacNew::StopRecording() {
      
      if (!_recIsInitialized) {
         WEBRTC_TRACE(kTraceInfo, kTraceAudioDevice, _id,
                      "  Recording is not initialized");
         return 0;
      }
      
      _recording = false;
      
      // Stop capture thread
      if (_captureWorkerThread != NULL) {
         WEBRTC_TRACE(kTraceDebug, kTraceAudioDevice,
                      _id, "Stopping CaptureWorkerThread");
         bool res = _captureWorkerThread->Stop();
         WEBRTC_TRACE(kTraceDebug, kTraceAudioDevice,
                      _id, "CaptureWorkerThread stopped (res=%d)", res);
         _captureWorkerThread.reset();
      }
      
      if (!_playIsInitialized) {
         // Both playout and recording has stopped, shutdown the device
        //  [[NSNotificationCenter defaultCenter] postNotificationName:kCpcAudioDeviceStateWillChange object:_state userInfo:@{kActive: @NO}];
         
         ShutdownPlayOrRecord();
         
        //  [[NSNotificationCenter defaultCenter] postNotificationName:kCpcAudioDeviceStateChanged object:_state userInfo:@{kActive: @NO}];
      }
      
      _recIsInitialized = false;
      _micIsInitialized = false;
      _interruptedRecordingDriftMs = 0;
      _interruptedRecordingTimestamp = 0;
      
      return 0;
   }


      int32_t AudioDeviceMacNew::ShutdownPlayOrRecord() {
      WEBRTC_TRACE(kTraceModuleCall, kTraceAudioDevice, _id, "%s", __FUNCTION__);
      
      // Close and delete AU
      OSStatus result = -1;
      
      if (NULL != _auVoiceProcessing) {
         result = AudioOutputUnitStop(_auVoiceProcessing);
         if (0 != result) {
            WEBRTC_TRACE(kTraceWarning, kTraceAudioDevice, _id,
                         "  Error stopping Audio Unit (result=%d)", result);
         }
         result = AudioComponentInstanceDispose(_auVoiceProcessing);
         if (0 != result) {
            WEBRTC_TRACE(kTraceWarning, kTraceAudioDevice, _id,
                         "  Error disposing Audio Unit (result=%d)", result);
         }
         _auVoiceProcessing = NULL;
      }
      
    //   if(_playAndRecordModeCntr == 0)
    //   {
    //      RestoreAudioCategory();
    //   }
      
      return 0;
   }


      int32_t AudioDeviceMacNew::StartPlayout() {

      if (!_playIsInitialized) {
         WEBRTC_TRACE(kTraceError, kTraceAudioDevice, _id,
                      "  Playout not initialized");
         return -1;
      }
      
      if (_playing) {
         WEBRTC_TRACE(kTraceInfo, kTraceAudioDevice, _id,
                      "  Playing already started");
         return 0;
      }
      
      if(_waitingAudioSessionIsActive)
      {
         WEBRTC_TRACE(kTraceInfo, kTraceAudioDevice, _id,
                      "  Waiting audio session is active");
         return 0;
      }
      
      
      // Reset playout buffer
      memset(_playoutBuffer, 0, sizeof(_playoutBuffer));
      _playoutBufferUsed = 0;
      _playoutDelay = 0;
      // Make sure first call to update delay function will update delay
      _playoutDelayMeasurementCounter = 9999;
      _playWarning = 0;
      _playError = 0;
      
      if (!_recording) {
         // Start Audio Unit
         WEBRTC_TRACE(kTraceModuleCall, kTraceAudioDevice, _id,
                      "  Starting Audio Unit");
         
         OSStatus result = AudioOutputUnitStart(_auVoiceProcessing);
         if (0 != result) {
            
            if (false)
            {
               WEBRTC_TRACE(kTraceWarning, kTraceAudioDevice, _id,
                            "Failed to start audio output unit, but on native call; will retry when native call ends"  );
               _interrupted = true;
               _interruptedByNativeCall = true;
            }
            else
            {
               
               WEBRTC_TRACE(kTraceCritical, kTraceAudioDevice, _id,
                            "  Error starting Audio Unit (result=%d)", result);
               return 0;
            }
         }
         
         //iOS9 bluetooth mute issue - toggle loudspeaker
//         bool loudspeaker = false;
//         GetLoudspeakerStatus(loudspeaker);
//
//         if(!loudspeaker)
//         {
//            SetLoudspeakerStatus(true);
//            SetLoudspeakerStatus(false);
//         }
         
         // AVAudioSession* audioSess = [AVAudioSession sharedInstance];
         // WEBRTC_TRACE(kTraceModuleCall, kTraceAudioDevice, _id, "StartPlayout: HW sampling rate is %f", audioSess.sampleRate);
         
         // [[NSNotificationCenter defaultCenter] postNotificationName:kCpcAudioDeviceStateChanged object:_state userInfo:@{kActive: @YES, kInterrupted: @(_interrupted)}];
      }
      
      _playing = true;
      
      return 0;
   }
   
   int32_t AudioDeviceMacNew::StopPlayout() {
      
      if (!_playIsInitialized) {
         WEBRTC_TRACE(kTraceInfo, kTraceAudioDevice, _id,
                      "  Playout is not initialized");
         return 0;
      }
      
      _playing = false;
      
      if (!_recIsInitialized) {
         // Both playout and recording has stopped, signal shutdown the device
         // [[NSNotificationCenter defaultCenter] postNotificationName:kCpcAudioDeviceStateWillChange object:_state userInfo:@{kActive: @NO}];
         
         ShutdownPlayOrRecord();
         
         // [[NSNotificationCenter defaultCenter] postNotificationName:kCpcAudioDeviceStateChanged object:_state userInfo:@{kActive: @NO}];
      }
      
      _playIsInitialized = false;
      _speakerIsInitialized = false;
      
      return 0;
   }
   int32_t AudioDeviceMacNew::InitPlayout() {
      
      if (!_initialized) {
         WEBRTC_TRACE(kTraceError, kTraceAudioDevice, _id, "  Not initialized");
         return -1;
      }
      
      if (_playing) {
         WEBRTC_TRACE(kTraceWarning, kTraceAudioDevice, _id,
                      "  Playout already started");
         return -1;
      }
      
      if (_playIsInitialized) {
         WEBRTC_TRACE(kTraceInfo, kTraceAudioDevice, _id,
                      "  Playout already initialized");
         return 0;
      }
      
      if (!_playoutDeviceIsSpecified) {
         WEBRTC_TRACE(kTraceError, kTraceAudioDevice, _id,
                      "  Playout device is not specified");
         return -1;
      }
      
      // Initialize the speaker
//      if (InitSpeaker() == -1) {
//         WEBRTC_TRACE(kTraceWarning, kTraceAudioDevice, _id,
//                      "  InitSpeaker() failed");
//      }
      _speakerIsInitialized = true;
      
      _playIsInitialized = true;
      
      if (!_recIsInitialized) {
         // Audio init
         if (InitPlayOrRecord() == -1) {
            // todo: Handle error
            WEBRTC_TRACE(kTraceWarning, kTraceAudioDevice, _id,
                         "  InitPlayOrRecord() failed");
            return -1;
         }
      } else {
         WEBRTC_TRACE(kTraceInfo, kTraceAudioDevice, _id,
                      "  Recording already initialized - InitPlayOrRecord() not called");
      }
      
      return 0;
   }


   int32_t AudioDeviceMacNew::SetPlayoutDevice(uint16_t index) {
      // Actually doing nothing here
      CriticalSectionScoped lock(&_critSect);
      _playoutDeviceIsSpecified = true;
      return 0;
   }
   
}