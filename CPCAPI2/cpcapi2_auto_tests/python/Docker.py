import os
import platform
import shutil
import subprocess
import time

from CMake import CMake

class Docker(CMake):
    def __init__(self, architecture, compiler, sanitizer):
        super().__init__("linux", architecture, "linux-clang-" + architecture + ".cmake", sanitizer)
        self.DockerName = "ubuntu.clang";

        self.Compiler = compiler;
        self.Network="cpcapi2_auto_tests_network"
        self.UnboundProcess = None
        self.UnboundID=""
        self.UnboundIPv4=""
        self.UnboundIPv6=""
        self.BuildPath = self.BuildPath.replace('\\', '/')
        self.RootPath = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir, os.pardir, os.pardir))

    def NumberOfInstances(self):
        return 60

    def Init(self):
        # Check that auto test network exists
        command = "docker network inspect " + self.Network
        print(command)
        ret = subprocess.run(command, universal_newlines=True, check=False, shell=True)
        if 0 != ret.returncode:
            command = 'docker network create -d bridge --ipv6 --subnet="fd5e:f647:17bb:c336::/64" ' + self.Network
            print(command)
            subprocess.run(command, universal_newlines=True, check=True, shell=True)

    def BuildTests(self, rebuild, verbose, build_tool_verbose):
        if rebuild and os.path.isdir(self.BuildPath):
            shutil.rmtree(self.BuildPath)

        if not os.path.isdir(self.BuildPath):
            os.makedirs(self.BuildPath)

        # Build base image
        command = "docker build -t cpcapi2_builder:" + self.DockerName + " -f " + os.path.join(self.RootPath, "core", "projects", "docker_build", "Dockerfile." + self.DockerName) + " " + os.path.join(self.RootPath, "core", "projects", "docker_build")
        print(command)
        subprocess.run(command, universal_newlines=True, check=False, shell=True)

        command = "docker build -t cpcapi2_auto_tests_builder:" + self.DockerName + " -f " + os.path.join(self.RootPath, "core", "cpcapi2_auto_tests", "Linux", "Dockerfile.builder." + self.DockerName) + " " + os.path.join(self.RootPath, "core", "cpcapi2_auto_tests", "Linux")
        print(command)
        subprocess.run(command, universal_newlines=True, check=False, shell=True)

        # TODO ccache
        if platform.system() == 'Windows':
            mount = "-v " + self.RootPath + ":/CPCAPI2"
        else:
            mount = "--mount type=bind,source=" + self.RootPath + ",target=/CPCAPI2"

        workdir = '-w="/CPCAPI2/core/cpcapi2_auto_tests/' + self.BuildPath + '"'

        entry_point = 'cmake -G Ninja -DCMAKE_TOOLCHAIN_FILE=' + self.Toolchain
        if self.Sanitizer is not None:
            entry_point += ' -DSANITIZER=' + self.Sanitizer
        if build_tool_verbose:
            entry_point += " -DVerboseBuildTools=True"
        entry_point += ' ../../..'

        entry_point += ' && cmake --build .'
        if verbose:
            entry_point += " -- -v"

        command = "docker run --rm " + mount + " " + workdir + " --entrypoint bash cpcapi2_auto_tests_builder:" + self.DockerName + " -c \"" + entry_point + "\""
        print(command)
        subprocess.run(command, check=True, universal_newlines=False, shell=True)

        return 0

    def GetTests(self, filter):
        print("Filter:", filter)
        command = "docker build -t cpcapi2_auto_tests_runner:" + self.DockerName + " -f " + os.path.join(self.RootPath, "core", "cpcapi2_auto_tests", "Linux", "Dockerfile.run." + self.DockerName) + " " + os.path.join(self.RootPath, "core", "cpcapi2_auto_tests", "Linux")
        print(command)
        subprocess.run(command, universal_newlines=True, check=False, shell=True)

        if platform.system() == 'Windows':
            mount = "-v " + os.path.join(self.RootPath, "core", "cpcapi2_auto_tests") + ":/cpcapi2_auto_tests"
        else:
            mount = "--mount type=bind,source=" + os.path.join(self.RootPath, "core", "cpcapi2_auto_tests") + ",target=/cpcapi2_auto_tests"

        binary = "/cpcapi2_auto_tests/" + self.BuildPath + "/cpcapi2_auto_tests"
        entrypoint = "ASAN_OPTIONS=detect_leaks=0 " + binary + " --gtest_list_tests"
        if filter is not None:
            entrypoint += " --gtest_filter=" + filter
        command = "docker run --rm " + mount + " --entrypoint env cpcapi2_auto_tests_runner:" + self.DockerName + " " + entrypoint
        print(command)
        ret = subprocess.run(command, capture_output=True, universal_newlines=True, shell=True)
        if 0 != ret.returncode:
            print(ret.stderr)
            raise AssertionError("Could not run auto test executable")
        return str(ret.stdout)

    def StartUnbound(self):
        print("Starting Unbound")
        command = "docker build -t unbound:alpine -f " + os.path.join(self.RootPath, "core", "cpcapi2_auto_tests", "Linux", "Dockerfile.unbound.alpine") + " " + os.path.join(self.RootPath, "core", "cpcapi2_auto_tests", "Linux")
        print(command)
        subprocess.run(command, check=True, universal_newlines=True, shell=True)

        if platform.system() == 'Windows':
            mount = "-v " + os.path.join(self.RootPath, "core", "cpcapi2_auto_tests", "unbound") + ":/unbound"
        else:
            mount = "--mount type=bind,source=" + os.path.join(self.RootPath, "core", "cpcapi2_auto_tests", "unbound") + ",target=/unbound"

        command = "docker run -d --net=" + self.Network + " --rm " + mount + " unbound:alpine"
        print(command)
        ret = subprocess.run(command, capture_output=True, universal_newlines=True, check=False, shell=True)
        if 0 != ret.returncode:
            print(ret.stderr)
            raise AssertionError("Could not run unbound")
        self.UnboundID = ret.stdout
        print("Unbound container:", self.UnboundID)

        command = 'docker inspect -f "{{.NetworkSettings.Networks.' + self.Network + '.IPAddress}}" ' + self.UnboundID
        print(command)
        ret = subprocess.run(command, capture_output=True, universal_newlines=True, check=False, shell=True)
        if 0 != ret.returncode:
            print(ret.stderr)
            raise AssertionError("Could not get unbound IP")
        self.UnboundIPv4 = ret.stdout.lstrip().rstrip()

        command = 'docker inspect -f "{{.NetworkSettings.Networks.' + self.Network + '.GlobalIPv6Address}}" ' + self.UnboundID
        print(command)
        ret = subprocess.run(command, capture_output=True, universal_newlines=True, check=False, shell=True)
        if 0 != ret.returncode:
            print(ret.stderr)
            raise AssertionError("Could not get unbound IP")
        self.UnboundIPv6 = ret.stdout.lstrip().rstrip()
        print("Unbound container IP:", self.UnboundIPv4, self.UnboundIPv6)

    def StopUnbound(self):
        print("Stopping Unbound")
        command = "docker stop " + self.UnboundID
        print(command)
        subprocess.run(command, universal_newlines=True, check=False, shell=True)

    def RunTest(self, test_name):
        #print("Running", test_name)

        if platform.system() == 'Windows':
            mount = "-v " + os.path.join(self.RootPath, "core", "cpcapi2_auto_tests") + ":/cpcapi2_auto_tests"
        else:
            mount = "--mount type=bind,source=" + os.path.join(self.RootPath, "core", "cpcapi2_auto_tests") + ",target=/cpcapi2_auto_tests"

        binary = "/cpcapi2_auto_tests/" + self.BuildPath + "/cpcapi2_auto_tests"
        # TODO Enable leak detection
        entrypoint = "ASAN_OPTIONS=detect_leaks=0 CPCAPI2_DNS_SERVER=" + self.UnboundIPv4 + " CPCAPI2_DNS_SERVER_IPV6=" + self.UnboundIPv6 + " " + binary + " --gtest_filter=" + test_name
        command = "docker run --rm --net=" + self.Network + " --sysctl net.ipv6.conf.all.disable_ipv6=0 " + mount + " --entrypoint env cpcapi2_auto_tests_runner:" + self.DockerName + " " + entrypoint
        #print(command)
        begin = time.time()
        try:
            ret = subprocess.run(command, timeout=6*60, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, universal_newlines=True, shell=True )
        except subprocess.TimeoutExpired:
            print("Test", test_name, "timed out")
            return test_name, -2
        totalTime = int(round((time.time() - begin) * 1000))
        filename = test_name.replace("/", "_").replace("[", "_").replace("]", "_")
        with open(os.path.join("logs", filename + ".log"), "w") as file:
            file.write(ret.stdout)
        if 0 != ret.returncode:
            return test_name, -1
        else:
            return test_name, totalTime
