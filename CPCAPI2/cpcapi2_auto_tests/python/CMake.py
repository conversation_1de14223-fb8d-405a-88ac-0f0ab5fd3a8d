import os
import os.path
import platform
import shutil
import subprocess
import time

class CMake:
    def __init__(self, system, architecture, toolchain, sanitizer, environment = None):

        suffix = ""
        if sanitizer == "Address":
            suffix = "_asan"
        elif sanitizer == "Memory":
            suffix = "_msan"
        elif sanitizer == "Thread":
            suffix = "_tsan"
        elif sanitizer == "Undefined":
            suffix = "_ubsan"

        self.System = system
        self.BuildPath = os.path.join("build", system, architecture + suffix)
        self.Toolchain = "../../../../projects/cmake/toolchains/" + toolchain
        self.Sanitizer = sanitizer
        self.Environment = environment

    def NumberOfInstances(self):
        return 1

    def Init(self):
        return 0

    def BuildTests(self, rebuild, verbose, build_tool_verbose):
        if rebuild and os.path.isdir(self.BuildPath):
            shutil.rmtree(self.BuildPath)

        if not os.path.isdir(self.BuildPath):
            os.makedirs(self.BuildPath)
        os.chdir(self.BuildPath)

        command = "cmake -G Ninja -DCMAKE_BUILD_TYPE=RelWithDebInfo -DCMAKE_TOOLCHAIN_FILE=" + self.Toolchain
        if self.Sanitizer is not None:
            command += " -DSANITIZER=" + self.Sanitizer
        # Prebuilts prevent using static
        #if self.System == "windows":
        #    command += " -DMSVC_RUNTIME_LIBRARY=Static"
        if build_tool_verbose:
            command += " -DVerboseBuildTools=True"

        command += " ../../.."
        print(command)
        subprocess.run(command, check=True, env=self.Environment, universal_newlines=False, shell=True)

        command = "cmake --build ."
        if verbose:
            command += " -- -v"
        print(command)
        subprocess.run(command, check=True, env=self.Environment, universal_newlines=False, shell=True)
        os.chdir(os.path.join("..", "..", ".."))
        return 0

    def GetTests(self, filter):
        print("Filter:", filter)
        command = os.path.join(self.BuildPath, "cpcapi2_auto_tests") + " --gtest_list_tests"
        if filter is not None:
            command += " --gtest_filter=" + filter
        print(command)
        ret = subprocess.run(command, capture_output=True, env=self.Environment, universal_newlines=True, shell=True)
        if 0 != ret.returncode:
            print(ret.stderr)
            raise AssertionError("Could not run auto test executable")
        return str(ret.stdout)

    def RunTest(self, test_name):
        #print("Running", test_name)
        command = os.path.join(self.BuildPath, "cpcapi2_auto_tests") + " --gtest_filter=" + test_name
        #print(command)
        begin = time.time()
        try:
            ret = subprocess.run(command, timeout=6*60, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, env=self.Environment, universal_newlines=True, shell=True)
        except subprocess.TimeoutExpired:
            return test_name, -2
        totalTime = int(round((time.time() - begin) * 1000))
        filename = test_name.replace("/", "_").replace("[", "_").replace("]", "_")
        with open(os.path.join("logs", filename + ".log"), "w") as file:
            file.write(ret.stdout)
        if 0 != ret.returncode:
            return test_name, -1
        else:
            return test_name, totalTime
