import importlib.util
from os import mkdir
import os.path
import platform
import subprocess
import time
import sys
import shutil
import threading
import logging
import re
import json

from CMake import CMake

# class that consumes stdout/stderr output from gtest-parallel
# and modifies it before printing out
class LogPipe(threading.Thread):

    def __init__(self, totalTestCount):
        threading.Thread.__init__(self)
        #self.daemon = False
        #self.level = level
        self.fdRead, self.fdWrite = os.pipe()
        self.pipeReader = os.fdopen(self.fdRead)
        self.testNumber = 0
        self.totalTestCount = totalTestCount
        self.start()

    def fileno(self):
        return self.fdWrite

    def run(self):
        # match lines starting with [xx/yy], exclude duplicate lines including exit code
        regexpTestFinished = re.compile("(^\[[0-9]+\/[0-9]+\] )(?!.* returned/aborted with exit code)(.*)")
        # match lines starting with failed, but exclude "test, listed below:", exclude duplicate lines with test duration
        regexpTestFailed = re.compile("^\[  FAILED  \](?!.*test, listed below:)(?!.*\(.*[0-9]+ ms\))")
        for line in iter(self.pipeReader.readline, ''):
            match = regexpTestFailed.search(line)
            if match is not None:
                print(line.rstrip('\n'))
            else:
                match = regexpTestFinished.search(line)
                if match is not None:
                    m = match.group(2)
                    if not m == "Running tests...":
                        self.testNumber += 1
                        print(str(self.testNumber) + "/" + str(self.totalTestCount) + " " + match.group(2))
                # todo: print other output from gtest-parallel (e.g. warnings about incorrect gtest-parallel usage)

        self.pipeReader.close()

    def close(self):
        os.close(self.fdWrite)

class Docker(CMake):
    def __init__(self, architecture, compiler, sanitizer):
        if platform.system() != "Windows":
            raise AssertionError("Windows docker containers can only be run on a Windows host")

        if sanitizer is not None:
            raise AssertionError("Sanitizer support not yet implemented for Windows")

        file = os.path.abspath(os.path.dirname(os.path.realpath(__file__)) + "../../../tools/Prebuilts/Windows.py")
        spec = importlib.util.spec_from_file_location("PrebuiltsWindows", file)
        module  = importlib.util.module_from_spec(spec)
        sys.modules["PrebuiltsWindows"] = module
        spec.loader.exec_module(module)
        PrebuiltsWindows = __import__("PrebuiltsWindows")

        env = os.environ
        config = {}
        config['Generated'] = {}
        config['Generated']['Architecture'] = architecture

        env = PrebuiltsWindows.ConfigureEnvironment(config, env)

        super().__init__("windows", architecture, "windows-msvc-" + architecture + ".cmake", sanitizer, env)
        self.Network="cpcapi2_auto_tests_network_windows"
        self.UnboundProcess = None
        self.UnboundID=""
        self.UnboundIPv4=""
        self.UnboundIPv6=""
        self.DockerName = "windows"
        self.RootPath = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir, os.pardir, os.pardir))        

    def PreTestStart(self, totalTestCount):
        self.logpipe = LogPipe(totalTestCount)

    def TestBinaryName(self):
        return "cpcapi2_auto_tests.exe"

    def NumberOfInstances(self):
        return 16

    def HostTestWorkingDir(self):       
        return os.path.join(self.RootPath, "core", "build", "docker_build")

    def HostTestRuntimeResourcesDir(self):
        return os.path.join(self.RootPath, "core", "cpcapi2_auto_tests", "runtime_resources")

    def HostTestUnboundDir(self):
        return os.path.join(self.RootPath, "core", "cpcapi2_auto_tests", "unbound")

    def HostTestLogsDir(self):
        return os.path.join(self.RootPath, "core", "cpcapi2_auto_tests", "logs")

    def BaseDockerRunCmd(self, logToFile, runInBackground, optionalDockerCmds="", workingDirectory="autoTestsWorkingDir"):
        runInBgFlag = "-d " if runInBackground else ""
        loggingFlag = "--env CPCAPI2_SAVE_LOGGING_TO_FILE=1 --env CPCAPI2_LOGGING_FILE_PATH=logs" if logToFile else ""
        return ("docker run --net=" + self.Network + " --env CPCAPI2_NO_PAUSE=1 --env CPCAPI2_FORCE_DUMMY_AUDIO_DEVICE=1 " + runInBgFlag + ""
                "--label cpcapi2_container "
                "--env CPCAPI2_DNS_SERVER=" + self.UnboundIPv4 + " "
                "--env CPCAPI2_DNS_SERVER_IPV6=" + self.UnboundIPv6 + " "
                "--env CPCAPI2_DOCKER_CD_PATH=" + workingDirectory + " "
                "--env CPCAPI2_TEST_TIMER_WATCHDOG=1 "
                "--env CPCAPI2_DOCKER_CONTAINERIZED=1 "
                "--env CPCAPI2_NO_DRAW_LOCAL_VIDEO=1 "
                "" + loggingFlag + " "
                "" + optionalDockerCmds + " "
                "-v " + self.HostTestWorkingDir() + ":C:\\autoTestsWorkingDir "
                "-v " + self.HostTestRuntimeResourcesDir() + ":C:\\autoTestsWorkingDir\\runtime_resources "
                "-v " + self.HostTestLogsDir() + ":C:\\autoTestsWorkingDir\\logs "
                "-v " + self.HostTestUnboundDir() + ":C:\\autoTestsWorkingDir\\unbound "
                "--isolation=process windowsdockerbound")

    def Init(self):

        print("Stopping all existing containers")
        # todo: limit to autotest only containers
        ret = subprocess.run("FOR /f \"tokens=*\" %i IN ('docker ps --filter \"label=cpcapi2_container\" -q') DO docker stop %i", shell=True)

        for root, dirs, files in os.walk(self.HostTestLogsDir()):
            for f in files:
                os.unlink(os.path.join(root, f))
            for d in dirs:
                shutil.rmtree(os.path.join(root, d))

        # TODO Get host Windows version since the container verison must match
        #self.DockerWindowsImage = "mcr.microsoft.com/windows:20H2"
        #command = "docker pull --platform=windows " + self.DockerWindowsImage

        # Check that auto test network exists
        #print(command)
        #ret = subprocess.run(command, universal_newlines=True, check=False, shell=True)
        command = "docker network inspect " + self.Network
        print(command)
        ret = subprocess.run(command, universal_newlines=True, check=False, shell=True)
        if 0 != ret.returncode:
            command = 'docker network create -d nat ' + self.Network
            print(command)
            subprocess.run(command, universal_newlines=True, check=True, shell=True)
        return

    def BuildTests(self, rebuild, verbose, build_tool_verbose):
        if rebuild and os.path.isdir(self.BuildPath):
            shutil.rmtree(self.BuildPath)

        if not os.path.isdir(self.BuildPath):
            os.makedirs(self.BuildPath)

        #copy executable
        src = os.path.join(self.RootPath, "core", "build", "cpcapi2_auto_tests.exe")
        dest = os.path.join(self.RootPath, "core", "build", "docker_build")
        if not os.path.isdir(dest):
            os.mkdir(dest)
        shutil.copy2(src, dest)

        src = os.path.join(self.RootPath, "core", "cpcapi2_auto_tests", "docker", "Dockerfile.windows-run.bat")
        dest = os.path.join(self.RootPath, "core", "build", "docker_build")
        shutil.copy2(src, dest)

        src = os.path.join(self.RootPath, "core", "cpcapi2_auto_tests", "gtest_parallel.py")
        dest = os.path.join(self.RootPath, "core", "build", "docker_build")
        shutil.copy2(src, dest)

        crtDebugSourcePath = os.path.join(self.RootPath, "windows_libs", "x86", "crt", "debug")

        # VS2017 C++ runtime debug DLL; copied from VS2017 install folder; needed for running debug build of autotests
        src = os.path.join(crtDebugSourcePath, "msvcp140d.dll")
        dest = os.path.join(self.RootPath, "core", "build", "docker_build")
        shutil.copy2(src, dest)

        # VS2017 C++ runtime debug DLL; copied from VS2017 install folder; needed for running debug build of autotests
        src = os.path.join(crtDebugSourcePath, "vcruntime140d.dll")
        dest = os.path.join(self.RootPath, "core", "build", "docker_build")
        shutil.copy2(src, dest)

        # Windows 10 SDK C runtime debug DLL; copied from VS2017 install folder; needed for running debug build of autotests
        src = os.path.join(crtDebugSourcePath, "ucrtbased.dll")
        dest = os.path.join(self.RootPath, "core", "build", "docker_build")
        shutil.copy2(src, dest)

        #check files
        #print(os.listdir(dest))

        # Build base image
        command = "docker build -t windowsdockerbound -f " + os.path.join(self.RootPath, "core", "cpcapi2_auto_tests", "docker", "Dockerfile.windows ") + os.path.join(self.RootPath, "core", "build", "docker_build")
        print(command)
        subprocess.run(command, universal_newlines=True, check=False, shell=True)

    #grab tests and run
    def GetTests(self, filter):
        command = "docker volume create data"
        subprocess.run(command, universal_newlines=True, check=False, shell=True)

        print("Filter:", filter)
        #grab the list of tests
        if filter is not None:
            command = self.BaseDockerRunCmd(logToFile=False, runInBackground=False) + " " + self.TestBinaryName() + " --gtest_list_tests " + " --gtest_filter=" + filter

        else:
            command = self.BaseDockerRunCmd(logToFile=False, runInBackground=False) + " " + self.TestBinaryName() + " --gtest_list_tests"
        print(command)
        ret = subprocess.run(command, capture_output=True, universal_newlines=True, shell=True)
        if 0 != ret.returncode:
            print(ret.stdout)
            print(ret.stderr)
            raise AssertionError("Could not run auto test executable")
        print(ret.stdout)
        return str(ret.stdout)

    def StartUnbound(self):
        print("Starting Unbound")

        command = self.BaseDockerRunCmd(logToFile=False, runInBackground=True, optionalDockerCmds="--expose 53/udp", workingDirectory="c:\\autoTestsWorkingDir\\unbound\\win") + " .\\unbound.exe -c .\\unbound.conf -vv"
        print(command)
        begin = time.time()
        ret = subprocess.run(command, timeout=None, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, universal_newlines=True, shell=True )
        self.UnboundID = ret.stdout;
        print("Unbound container:", self.UnboundID)

        command = 'docker inspect -f "{{.NetworkSettings.Networks.' + self.Network + '.IPAddress}}" ' + self.UnboundID
        print(command)
        ret = subprocess.run(command, capture_output=True, universal_newlines=True, check=False, shell=True)
        if 0 != ret.returncode:
            print(ret.stderr)
            raise AssertionError("Could not get unbound IP")
        self.UnboundIPv4 = ret.stdout.lstrip().rstrip()

        command = 'docker inspect -f "{{.NetworkSettings.Networks.' + self.Network + '.GlobalIPv6Address}}" ' + self.UnboundID
        print(command)
        ret = subprocess.run(command, capture_output=True, universal_newlines=True, check=False, shell=True)
        if 0 != ret.returncode:
            print(ret.stderr)
            raise AssertionError("Could not get unbound IP")
        self.UnboundIPv6 = ret.stdout.lstrip().rstrip()
        print("Unbound container IP:", self.UnboundIPv4, self.UnboundIPv6)

    def StopUnbound(self):
        print("Stopping Unbound")
        command = "docker stop " + self.UnboundID
        print(command)
        #subprocess.run(command, universal_newlines=True, check=False, shell=True)

    def CleanUp(self):
        print("deleting docker build context folder")
        dockerpath = os.path.join(self.RootPath, "core", "build", "docker_build")

        ret = subprocess.run("FOR /f \"tokens=*\" %i IN ('docker ps --filter \"label=cpcapi2_container\" -q') DO docker stop %i", shell=True)
        ret = subprocess.run("FOR /f \"tokens=*\" %i IN ('docker ps --filter \"label=cpcapi2_container\" -q') DO docker rm %i", shell=True)

        self.logpipe.close()

        #shutil.rmtree(dockerpath)
    
    def RunTest(self, test_name):
        print("Running", test_name)
        #mount = "-v %cd%:D:\\cpcapi2_auto_tests"
        #executable = "/cpcapi2_auto_tests/" + self.BuildPath + "/cpcapi2_auto_tests.exe"
        #executable = "D:\\cpcapi2_auto_tests\\build\\windows\\x86_64\\cpcapi2_auto_tests.exe"
        #entrypoint = "CPCAPI2_NO_PAUSE=1 CPCAPI2_DNS_SERVER=" + self.UnboundIPv4 + " CPCAPI2_DNS_SERVER_IPV6=" + self.UnboundIPv6 + " " + executable + " --gtest_filter=" + test_name
        #command = "docker run --platform=windows --rm --net=" + self.Network + " " + mount + " " + self.DockerWindowsImage + " " + entrypoint

        command = self.BaseDockerRunCmd(logToFile=False, runInBackground=False) + " " + self.TestBinaryName() + " --gtest_filter=" + test_name
        print(command)
        begin = time.time()
        try:
            ret = subprocess.run(command, timeout=6*60, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, universal_newlines=True, shell=True )
        except subprocess.TimeoutExpired:
            print("Test", test_name, "timed out")
            return test_name, -2
        totalTime = int(round((time.time() - begin) * 1000))
        filename = test_name.replace("/", "_").replace("[", "_").replace("]", "_")
        with open("logs/" + filename + ".log", "w") as file:
            file.write(ret.stdout)
            #print(ret.stdout)
        if 0 != ret.returncode:
            return test_name, -1
        else:
            return test_name, totalTime

    # use gtest-parallel to run multiple tests within one container.
    # this is as opposed to running one test per container; currently docker windows containers
    # seem very slow (tens of seconds) to start and stop -- this might be related to 
    # https://github.com/moby/moby/issues/40832 however for us the slowness seems to affect
    # both process and hyperv isolation.
    def RunTests(self, test_names):
        containerGtestParallelLogDir = os.path.join("C:\\autoTestsWorkingDir", "logs", str(threading.get_ident()))
        hostPerContainerGtestParallelLogDir = os.path.join(self.HostTestLogsDir(), str(threading.get_ident()))
        # can't use os.path.join since it prepends host working directory
        containerTestResultsJsonFile = os.path.join(containerGtestParallelLogDir, "test_results.json")
        hostPerContainerTestResultsJsonFile = os.path.join(hostPerContainerGtestParallelLogDir, "test_results.json")
        if not os.path.isdir(hostPerContainerGtestParallelLogDir):
            os.mkdir(hostPerContainerGtestParallelLogDir)

        command = (self.BaseDockerRunCmd(logToFile=True, runInBackground=False) + " python gtest_parallel.py " + self.TestBinaryName() + " "
                  "--workers=1 --output_dir=" + containerGtestParallelLogDir + " "
                  "--dump_json_test_results=" + containerTestResultsJsonFile + " "
                  "--gtest_filter=" + ':'.join(test_names))
        print(command)
        begin = time.time()
        try:
            ret = subprocess.run(command, timeout=6*3600, stdout=self.logpipe, stderr=subprocess.STDOUT, universal_newlines=True, shell=True )
        except subprocess.TimeoutExpired:
            print("Test", test_names, "timed out")
            return test_names, -2
        totalTime = int(round((time.time() - begin) * 1000))
        # todo: how to log per test?
        # filename = test_names[0].replace("/", "_").replace("[", "_").replace("]", "_")
        # with open("logs/" + filename + ".log", "w") as file:
        #     file.write(ret.stdout)

        failed_tests = set()
        try:
            with open(hostPerContainerTestResultsJsonFile) as f:
                dt = f.read()
                if dt != "":
                    data = json.loads(dt)
                    for testSuiteName, testSuite in data["tests"].items():
                        for testCaseName, testCase in testSuite.items():
                            if (testCase["actual"] == "FAIL"):
                                failed_tests.add(testSuiteName + "." + testCaseName)
                else:
                    print("Got empty result for: " + ', '.join(test_names))
        except FileNotFoundError:
            print("Couldn't locate " + hostPerContainerTestResultsJsonFile + " for " + ":".join(test_names))
            failed_tests = test_names

        hostGtestParallelLogDir = os.path.join(self.HostTestLogsDir(), "gtest-parallel")
        srcPassedLogDir = os.path.join(hostPerContainerGtestParallelLogDir, "gtest-parallel-logs", "passed")
        dstPassedLogDir = os.path.join(hostGtestParallelLogDir, "passed")
        if not os.path.isdir(dstPassedLogDir):
            os.makedirs(dstPassedLogDir)
        if os.path.isdir(srcPassedLogDir):
            for filename in os.listdir(srcPassedLogDir):
                shutil.move(os.path.join(srcPassedLogDir, filename), dstPassedLogDir)
        srcFailedLogDir = os.path.join(hostPerContainerGtestParallelLogDir, "gtest-parallel-logs", "failed")
        dstFailedLogDir = os.path.join(hostGtestParallelLogDir, "failed")
        if not os.path.isdir(dstFailedLogDir):
            os.makedirs(dstFailedLogDir)
        if os.path.isdir(srcFailedLogDir):
            for filename in os.listdir(srcFailedLogDir):
                shutil.move(os.path.join(srcFailedLogDir, filename), dstFailedLogDir)

        shutil.rmtree(hostPerContainerGtestParallelLogDir);  

        if 0 != ret.returncode:
            return test_names, -1, failed_tests
        else:
            return test_names, totalTime, failed_tests
