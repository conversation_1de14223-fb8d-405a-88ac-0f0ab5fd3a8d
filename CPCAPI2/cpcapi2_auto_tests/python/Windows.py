import importlib.util
import os.path
import subprocess
import sys

from CMake import CMake

class Windows(CMake):
    def __init__(self, architecture, compiler, sanitizer):
        if sanitizer is not None:
            raise AssertionError("Sanitizer support not yet implemented for Windows")

        file = os.path.abspath(os.path.dirname(os.path.realpath(__file__)) + "../../../tools/Prebuilts/Windows.py")
        spec = importlib.util.spec_from_file_location("PrebuiltsWindows", file)
        module  = importlib.util.module_from_spec(spec)
        sys.modules["PrebuiltsWindows"] = module
        spec.loader.exec_module(module)
        PrebuiltsWindows = __import__("PrebuiltsWindows")

        env = os.environ
        config = {}
        config['Generated'] = {}
        config['Generated']['Architecture'] = architecture

        env = PrebuiltsWindows.ConfigureEnvironment(config, env)

        env['CPCAPI2_NO_PAUSE'] = '1'
        super().__init__("windows", architecture, "windows-msvc-" + architecture + ".cmake", sanitizer, env)
        self.UnboundProcess = None

    def StartUnbound(self):
        print("Starting Unbound")
        os.chdir(os.path.join("unbound", "win"))
        global UnboundProcess
        UnboundProcess = subprocess.Popen(["unbound.exe", "-c", "unbound.conf"])
        os.chdir(os.path.join("..", ".."))

    def StopUnbound(self):
        print("Stopping Unbound")
        if UnboundProcess is not None:
            UnboundProcess.terminate()
