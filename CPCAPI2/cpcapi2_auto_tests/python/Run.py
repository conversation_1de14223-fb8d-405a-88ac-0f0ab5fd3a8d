import argparse
import concurrent.futures
from concurrent.futures import thread
import os
import platform
import re
import sys
import time
from concurrent import futures

#Used to extract test names from the string returned by Android.GetTests()
def ProcessTestList(tests):
    full_tests = list()
    last_parent_case = ""
    for line in tests.splitlines():
        match = re.search(r'^([^. ]+).$', line)
        if match:
            last_parent_case = match.group(1)
        else:
            match = re.search(r'  ([^\#\ ]+)', line)
            if match:
                if not match.group(1).startswith("DISABLED"):
                    full_tests.append(last_parent_case + "." + match.group(1))
    return full_tests

if __name__ == '__main__':
    RED_TEXT = ""
    YELLOW_TEXT = ""
    GREEN_TEXT = ""
    CLEAR_STYLE = ""
    try:
        import colorama
        from colorama import Fore, Style
        RED_TEXT = Fore.RED
        YELLOW_TEXT = Fore.YELLOW
        GREEN_TEXT = Fore.GREEN
        CLEAR_STYLE = Style.RESET_ALL
    except ModuleNotFoundError:
        print("Install colorama for coloured text `pip install colorama`")
        pass

    parser = argparse.ArgumentParser()
    parser.add_argument('-s', '--System', action='store', help="Target OS (Windows, Linux, Docker, ...)")
    parser.add_argument('-a', '--Arch', '--Architecture', action='store', help="CPU Architecture (x86, x86_64, armv7, armv8)")
    parser.add_argument('-c', '--Compiler', action='store', help="Specify compiler (Linux: clang, devtoolset8, gcc-toolset-9)")
    parser.add_argument('-r', '--Rerun', action='store_true', help="Rerun failed tests to see if they are flaky")
    parser.add_argument('-f', '--Failed', action='store_true', help="Run only the failed tests from previous run")
    parser.add_argument('-b', '--Build', action='store_true', help="Build the autotests")
    parser.add_argument('--Clean', '--Rebuild', action='store_true', help="Clean and build the autotests")
    parser.add_argument('-v', '--Verbose', action='store_true', help="Verbose build")
    parser.add_argument('-vv', '--BuildToolVerbose', action='store_true', help="Pass verbose flag into build tools (Only tested with clang and lld)")
    parser.add_argument('--Sanitizer', action='store', help="Enable specified sanitizer (Address, Memory, Thread, Undefined)")
    parser.add_argument('--gtest_filter', action='store', help="Target filter")
    parser.add_argument('--multi', action='store_true', help="Re-uses an active container for multiple test runs, instead of spinning up one container per test")
    parser.add_argument('--Parallel_Count', action='store', help="Number of parallel docker container to run")
    parser.add_argument('--test_list', action='store_true', help="Prints the list of tests that will be run with previous run times")
    parser.add_argument('--no_sort', action='store_true', help="Does not sort test list by run time before running")

    args = parser.parse_args()

    architecture = "x86_64"
    if args.Arch is not None:
        architecture = args.Arch

    target_system = platform.system()
    if args.System is not None:
        target_system = args.System

    if target_system == 'Docker':
        Linux = __import__("Docker")
        system = Linux.Docker(architecture, args.Compiler, args.Sanitizer)
    elif target_system == 'Docker-Windows':
        Windows = __import__("Docker-Windows")
        system = Windows.Docker(architecture, args.Compiler, args.Sanitizer)
    elif target_system == 'Windows':
        Windows = __import__("Windows")
        system = Windows.Windows(architecture, args.Compiler, args.Sanitizer)
    elif target_system == 'Darwin':
        macOS = __import__("MacOS")
        system = macOS.macOS(architecture, args.Compiler, args.Sanitizer)
    elif target_system == 'Linux':
        Linux = __import__("Linux")
        system = Linux.Linux(architecture, args.Compiler, args.Sanitizer)
    elif target_system == 'Android':
        Android = __import__("Android")
        system = Android.Android(architecture, args.Compiler, args.Sanitizer)
    else:
        raise AssertionError("Unknown system", system)

    os.chdir(os.path.join(os.path.dirname(os.path.realpath(__file__)), ".."))

    system.Init()

    if args.Build:
        system.BuildTests(args.Clean, args.Verbose, args.BuildToolVerbose)

    tests = system.GetTests(args.gtest_filter)

    tests = ProcessTestList(tests)
    tests = list(set(tests))
    tests.sort()

    # Sort tests based on past run times
    # Failed tests first: -1
    # Unrecorded tests second: 0
    # Other tests longest first
    test_times = dict()
    try:
        with open("test_times.log", "r") as file:
            fileContents = file.readlines()
            for line in fileContents:
                v = line.split(":")
                test_times[v[0]] = int(v[1])
            if not args.no_sort:    #Checks args to see if test list should be sorted
                tests.sort(key=lambda k: 0 if (test_times.get(k) is None) else sys.maxsize if (-1 == test_times.get(k)) else test_times[k], reverse = True)
    except FileNotFoundError:
        pass

    if args.Failed:
        failed_tests = []
        for t in tests:
            if test_times.get(t) is not None:
                if test_times[t] < 1:
                    failed_tests.append(t)
        tests = failed_tests

    #If arg is present, print test list and previous run times before running tests
    if args.test_list:
        print("Tests to run:")
        for t in tests:
            if test_times.get(t) is not None:
                print(t, "(Previous run time: " + str(test_times.get(t)) + "ms)")
            else:
                print(t)


    #Stops unbound before starting to ensure it isn't still running from previous failure.
    system.StopUnbound()
    system.StartUnbound()
    
    if hasattr(system, "PreTestStart"):
        system.PreTestStart(totalTestCount=len(tests))

    print("Running tests...")
    beginTestRunEpochSec = time.time()
    flaky_tests = set()
    failed_tests = set()
    #As Android tests run on an emulated device, only one test can be run at a time
    if target_system == 'Android':
        parallelContainerCount = 1
    elif args.Parallel_Count is not None:
        parallelContainerCount = int(args.Parallel_Count)
    else:  
        parallelContainerCount = system.NumberOfInstances()
        print("Parallel Container Count = " + str(parallelContainerCount))

    try:
        os.remove("test_times.log")
    except OSError:
        pass

    #Creates log file in cpcapi2_auto_tests directory
    file = open("test_times.log", "w+")

    #Create ThreadPoolExecutor used to run tests
    executor = concurrent.futures.ThreadPoolExecutor(max_workers=parallelContainerCount)

    #Runs tests and stores results as futures
    futures = [executor.submit(system.RunTest, t) for t in tests]
    
    try:
        #This runs as each test completes and prints result
        for future in concurrent.futures.as_completed(futures):
            result = future.result()
            file.write(result[0] + ":" + str(result[1]) + "\n")

            if result[1] == -1:
                failed_tests.add(result[0])
                print(RED_TEXT + "FAILED:" + CLEAR_STYLE, result[0])
            elif result[1] == -2:
                failed_tests.add(result[0])
                print(RED_TEXT + "TIMED OUT:" + CLEAR_STYLE, result[0])
            else:
                print(GREEN_TEXT + "PASSED:" + CLEAR_STYLE, result[0], "(" + str(result[1]) + "ms)")
    except KeyboardInterrupt:
        print("Interrupted by keyboard. Stopping execution")
        executor.shutdown(wait=False)
        executor._threads.clear()
        concurrent.futures.thread._threads_queues.clear()

    file.close()
    system.StopUnbound()
    system.CleanUp()

    print("")
    #Prints the name of all flaky and failed tests.
    if len(flaky_tests) > 0:
        flaky_tests = list(flaky_tests)
        flaky_tests.sort()
        print("")
        print("Flaky Tests:" + YELLOW_TEXT)
        for t in flaky_tests:
            print(t)
        print(CLEAR_STYLE)

    if len(failed_tests) > 0:
        failed_tests = list(failed_tests)
        failed_tests.sort()
        print("")
        print("Failed Tests:" + RED_TEXT)
        for t in failed_tests:
            print("F:" + t)
        print(CLEAR_STYLE)

    #Calculates and prints total time for each test to run and total results
    totalTimeMin = int((round(time.time() - beginTestRunEpochSec) / 60))
    if len(flaky_tests) == 0 and len(failed_tests) == 0:
        print(GREEN_TEXT + "All Tests Passed")
    print("Took " + str(totalTimeMin) + " minutes to run.")
    print(CLEAR_STYLE)

    print("Number of tests run: " + str(len(tests)))
    print("Total seconds " + str(time.time() - beginTestRunEpochSec))

    if len(failed_tests) > 0:
        sys.exit(1)
    else:
        sys.exit(0)
