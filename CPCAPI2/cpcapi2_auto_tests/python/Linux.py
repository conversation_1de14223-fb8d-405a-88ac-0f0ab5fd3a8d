import os.path
import subprocess

from CMake import CMake

class Linux(CMake):
    def __init__(self, architecture, compiler, sanitizer):
        super().__init__("linux", architecture, "linux-clang-" + architecture + ".cmake", sanitizer)
        self.UnboundProcess = None

    def StartUnbound(self):
        print("Starting Unbound")
        os.chdir("unbound")
        global UnboundProcess
        UnboundProcess = subprocess.Popen(["unbound", "-c", "linux/unbound.conf"])
        os.chdir("..")

    def StopUnbound(self):
        print("Stopping Unbound")
        if UnboundProcess is not None:
            UnboundProcess.terminate()
