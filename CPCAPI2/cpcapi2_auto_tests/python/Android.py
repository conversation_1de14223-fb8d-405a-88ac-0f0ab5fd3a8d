import os.path
import subprocess
import time
import re
import socket
import platform
import shutil

from CMake import CMake

app_package_name = "com.counterpath.sdkdemo.advancedaudiocall"
app_files_location = "/storage/emulated/0/Android/data/" + app_package_name +"/files/"
app_activity_name = app_package_name + "/" + app_package_name + ".AdvAudioCallActivity"

def GetIPAddress():
    #Gets android device's IP address
    android_ip = str(subprocess.check_output("adb shell ifconfig wlan0", shell=True).strip(), 'utf-8')

    #Gets IP address with correct format from output
    android_ip = re.search('inet addr:\S+', android_ip).group().split(":")[1]

    #Returns the system's local IP address
    s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    s.connect((android_ip, 80))
    local_ip = s.getsockname()[0]
    s.close()

    return local_ip

#Copy SDK debug symbols to the directory where they are read from when processing crash dumps.
def CopyDsym():
    path_to_here = os.path.abspath(os.path.dirname(os.path.realpath(__file__)))

    #Creates the dsym directory which is used for logging crashed tests.
    if not os.path.exists(path_to_here + "/../../language_wrapper/Android/target/release/dsym"):
        os.makedirs(path_to_here + "/../../language_wrapper/Android/target/release/dsym")
    
    #Move the required file to the dsym directory and renames it
    #Required due cpcapi2_auto_tests.dbgsym.so in the arm64-v8a directory not being the expected name for the dsym utility, libcpcapi2.so 
    shutil.copyfile(path_to_here + "/../../language_wrapper/Android/target/release/arm64-v8a/cpcapi2_auto_tests.dbgsym.so", path_to_here + "/../../language_wrapper/Android/target/release/dsym/libcpcapi2.so")

#Logs results of Android device test case crashes to log files in the dumps directory
def CrashLogDump(path_to_here, test_name):
    
    #Waits until crash output has been found before recording it
    crash_output = ""
    loop_counter = 0
    while crash_output == "":
        time.sleep(1)
        if loop_counter == 60:
            print("Test crashed and logging for the crash failed. Exiting.")
            return
        loop_counter+=1
        #Gets adb logcat and writes the results to /dumps/{test_name}.log
        crash_output = subprocess.check_output("adb logcat -b crash -d", shell=True).decode('utf-8')

    with open(path_to_here + "/../dumps/" + test_name + ".log", "w+") as file:
        file.write(crash_output)

    try:
        sym_log_output = subprocess.check_output("$ANDROID_NDKS/android-ndk-r23/ndk-stack -sym " + path_to_here + "/../../language_wrapper/Android/target/release/dsym -dump " + path_to_here + "/../dumps/" + test_name + ".log", shell=True)
    except Exception as e:
        #If this is failing, it is likely due to the /language_wrapper/Android/target/release/dsym directory not being created .
        print("Test crashed and crash logging failed.")
        print(e.output)
        return

    with open(path_to_here + "/../dumps/" + test_name + "_sym.log", "w+") as file:
        file.write(sym_log_output.decode('utf-8'))

    #Gets adb logcat and writes the results to /dumps/{test_name}_logcat.log
    logcat_output = subprocess.check_output("adb logcat -d", shell=True)
    with open(path_to_here + "/../dumps/" + test_name + "_logcat.log", "w+") as file:
        file.write(logcat_output.decode('utf-8'))

def SaveLogFile(path_to_here, test_name):
    #If a log for this test is already stored, remove it
    try:
        os.remove(path_to_here + "/../logs/" + test_name + ".log")
    except OSError:
        pass
    
    #Saves a logs of the test to ../logs/ folder
    subprocess.run("adb pull " + app_files_location +  "AutoUnitTest.log " + path_to_here + "/../logs/" + test_name + ".log", shell=True, universal_newlines=True)

    #Empties the Android device log file to clean output for next tests.
    subprocess.run("adb shell '> " + app_files_location +  "AutoUnitTest.log'", shell=True, universal_newlines=True)

def PrepareAndUploadEnvVars():

    path_to_here = os.path.abspath(os.path.dirname(os.path.realpath(__file__)))

    #Variable required for Jenkins testing
    #TODO Hookup unbound DNS server support https://alianza.atlassian.net/browse/SCORE-1271
    JENKINS_NODE_IP_ADDR= GetIPAddress()
    JENKINS_NODE_LOGCAT_PORT=str(6000)

    print("Jenkins IP: " + JENKINS_NODE_IP_ADDR)
    print("Jenkins PORT: " + JENKINS_NODE_LOGCAT_PORT)

    #Deletes the runtime_resources/env_variables.txt file if it exists
    try:
        os.remove(path_to_here + "/../runtime_resources/env_variables.txt")
    except OSError:
        pass

    #Creates env_var.txt with required environment variables to run the testing app
    with open(path_to_here + "/../runtime_resources/env_variables.txt", "w") as file:
        file.write("CPCAPI2_REDIRECT_COUT_TO_FILE 1\n")
        file.write("CPCAPI2_TEST_TIMER_WATCHDOG 1\n")
        file.write("CPCAPI2_DNS_SERVER " + JENKINS_NODE_IP_ADDR + "\n")
        file.write("CPCAPI2_LOGCAT_SERVER_IP " + JENKINS_NODE_IP_ADDR + "\n")
        file.write("CPCAPI2_LOGCAT_SERVER_PORT " + JENKINS_NODE_LOGCAT_PORT + "\n") 


def BuildAndInstallApk():
    path_to_here = os.path.abspath(os.path.dirname(os.path.realpath(__file__)))

    #Path to where app code is located on local machine
    local_app_location = path_to_here + "/../Android/auto_unit_tests/androidUnitTestingApp/"

    #Removes older APK before building new version
    process = subprocess.run(local_app_location + "gradlew clean --stacktrace --info -p " + local_app_location, shell=True)
    if process.returncode != 0:
        print("Failed to install APK. Exiting")
        exit(1)

    #Builds the APK from the Android testing app and installs to Android device
    process = subprocess.run(local_app_location + "gradlew --stacktrace --info installDebug -p " + local_app_location, shell=True)
    if process.returncode != 0:
        print("Failed to install APK. Exiting")
        exit(1)
    
def SetPermissions():
    #Gives testing app required permissions to run 
    process = subprocess.run("adb shell pm grant " + app_package_name + " android.permission.RECORD_AUDIO", shell=True)
    if process.returncode != 0:
        print("Failed to grant RECORD_AUDIO permission. Exiting")
        exit(1)

    process = subprocess.run("adb shell pm grant " + app_package_name + " android.permission.READ_EXTERNAL_STORAGE", shell=True)
    if process.returncode != 0:
        print("Failed to grant READ_EXTERNAL_STORAGE permission. Exiting")
        exit(1)

    process = subprocess.run("adb shell pm grant " + app_package_name + " android.permission.WRITE_EXTERNAL_STORAGE", shell=True)
    if process.returncode != 0:
        print("Failed to grant WRITE_EXTERNAL_STORAGE permission. Exiting")
        exit(1)

    process = subprocess.run("adb shell pm grant " + app_package_name + " android.permission.CAMERA", shell=True)
    if process.returncode != 0:
        print("Failed to grant CAMERA permission. Exiting")
        exit(1)

    process = subprocess.run("adb shell pm grant " + app_package_name + " android.permission.READ_PHONE_STATE", shell=True)
    if process.returncode != 0:
        print("Failed to grant READ_PHONE_STATE permission. Exiting")
        exit(1)

class Android(CMake):
    def __init__(self, architecture, compiler, sanitizer):
        PrepareAndUploadEnvVars()

        BuildAndInstallApk()

        SetPermissions()

        CopyDsym()

    def GetTests(self, testFilter):
        #Delete AutoUnitTest.log if it already exists 
        process = subprocess.Popen("adb shell rm -f " + app_files_location + "AutoUnitTest.log || true", shell = True)
        process.wait()

        #Prints the test list to cout which is redirected to AutoUnitTest.log
        command = "adb shell am start -n " + app_activity_name + " -a android.intent.action.MAIN -c android.intent.category.LAUNCHER -e argv '\"--gtest_list_tests"
        if testFilter is not None:
            command += " --gtest_filter=" + testFilter + " --force_write_log_complete \"'"
        else:
            command += " --force_write_log_complete \"'"
        print(command)
        process = subprocess.Popen(command, shell=True)
        process.wait()


        finished_writing_tests = False
        last_line = ""
        itr = 0

        #Loops while checking last line in log to determine when all tests are found
        while not finished_writing_tests:
            time.sleep(3)
            try:
                last_line = str(subprocess.check_output("adb shell tail -1 " + app_files_location + "AutoUnitTest.log", shell=True))
            except:
                last_line = ""

            #If last line is [log write complete] continue. If not true for 10 iterations, exit as test list wasn't found
            if "[log write complete]" in last_line:
                finished_writing_tests = True
            else:
                if itr > 10:
                    print("Unable to retrieve test list. Exiting.")
                    exit(1)
                else:
                    itr+=1
        
        #Gets contents of AutoUnitTest.log and stores to ret.stdout
        ret = subprocess.run("adb shell cat  " + app_files_location + "AutoUnitTest.log", capture_output=True,
                             universal_newlines=True, shell=True)

        #Empties the Android device log file to clean output for next tests.
        subprocess.run("adb shell '> " + app_files_location +  "AutoUnitTest.log'", shell=True, universal_newlines=True)

        if 0 != ret.returncode:
            print(ret.stdout)
            print(ret.stderr)
            raise AssertionError("Could not run auto test executable")

        #Returns a string containing all unit test names
        return str(ret.stdout)

    def RunTest(self, test_name):
        #Clears Android logcat between test cases
        subprocess.run("adb logcat -c", shell=True)

        #Used to ensure the device stays awake
        subprocess.run("adb shell input keyevent KEYCODE_WAKEUP", shell=True, universal_newlines=True)
        subprocess.run("adb shell input keyevent 82", shell=True, universal_newlines=True)
        subprocess.run("adb shell input keyevent KEYCODE_BACK", shell=True, universal_newlines=True)
        subprocess.run("adb shell input keyevent KEYCODE_BACK", shell=True, universal_newlines=True)
        subprocess.run("adb shell input keyevent KEYCODE_BACK", shell=True, universal_newlines=True)

        print("Running", test_name)

        #Empties the Android device log file to ensure clean output for test.
        subprocess.run("adb shell '> " + app_files_location +  "AutoUnitTest.log'", shell=True, universal_newlines=True)

        path_to_here = os.path.abspath(os.path.dirname(os.path.realpath(__file__)))

        #Command used to start a test
        command = "adb shell am start -n " + app_activity_name + " -a android.intent.action.MAIN -c android.intent.category.LAUNCHER -e argv --gtest_filter=" + test_name
        
        begin = time.time()
        try:
            #Runs the test specified with test_name
            ret = subprocess.run(command, timeout=6*60, stdout=subprocess.PIPE,
                                 stderr=subprocess.STDOUT, universal_newlines=True, shell=True)
        except subprocess.TimeoutExpired:
            #If test does not finish in required time return -2 to signal that
            print("Test ", test_name, " timed out")
            return test_name, -2

        timer = 0

        #Loops until last line is [log write complete] and determine pass/fail/timeout
        while True:
            time.sleep(1)

            #Check if test process is still running
            try:
                pid = subprocess.check_output("adb shell pidof " + app_package_name, shell=True)
            except subprocess.CalledProcessError:
                pid = 0
            
            last_line = str(subprocess.check_output("adb shell tail -1 " + app_files_location + "AutoUnitTest.log", shell=True))
            
            if not "[log write complete]" in last_line:
                timer += 1
                
                if pid == 0:
                    CrashLogDump(path_to_here, test_name)
                    return test_name, -1

                if timer == 600:
                    SaveLogFile(path_to_here, test_name)
                    return test_name, -2
            else:
                #Finds total time taken for test
                totalTime = int(round((time.time() - begin) * 1000))

                SaveLogFile(path_to_here, test_name)    

                #TODO: Look at switching to reading XML or JSON output of gtest to determine pass/failed tests: 
                #https://google.github.io/googletest/advanced.html#generating-an-xml-report

                passed = False
                failed = False
                skipped = False

                #If test passed, return name and time taken, otherwise name and -1
                pattern = re.compile("\[  PASSED  \] 1 test")
                for line in enumerate(open(path_to_here + "/../logs/" + test_name + ".log")):
                    for match in re.finditer(pattern, line[1]):
                        passed = True

                pattern = re.compile("\[  FAILED  \] 1 test")
                for line in enumerate(open(path_to_here + "/../logs/" + test_name + ".log")):
                    for match in re.finditer(pattern, line[1]):
                        failed = True
                
                pattern = re.compile("\[  SKIPPED \] 1 test")
                for line in enumerate(open(path_to_here + "/../logs/" + test_name + ".log")):
                    for match in re.finditer(pattern, line[1]):
                        skipped = True
                
                if failed:
                    return test_name, -1
                elif passed:
                    return test_name, totalTime
                elif skipped:
                    return test_name, totalTime
                else:
                    CrashLogDump(path_to_here, test_name)
                    return test_name, -1

    def StartUnbound(self):
        print("Starting Unbound")
        path_to_here = os.path.abspath(os.path.dirname(os.path.realpath(__file__)))

        #Gets local IP address
        local_ip = GetIPAddress()

        #Determines system OS to determine how to start unbound server
        system_os = platform.system()

        if system_os == "Darwin":
            #Edits unbound.conf with found IP address and removes port
            with open(path_to_here + "/../unbound/mac/unbound.conf", "r") as file:
                content = file.read()
                file.close()

            content = content.replace("interface: 0.0.0.0", "interface: {}".format(local_ip))
            content = content.replace("interface: ::0", "#interface: ::0")

            with open(path_to_here + "/../unbound/mac/unbound.conf", "w") as file:
                file.write(content)
                file.close()

            #Runs unbound server
            unbound_process = subprocess.Popen("./run_unbound.sh", shell = True, cwd = path_to_here + "/../unbound/mac/")
            if unbound_process.returncode == 1:
                print(unbound_process.returncode)
                print("Failed to start unbound server. Exiting.")
                exit(1)
        elif system_os == "Windows":
            #Todo: Add code for starting unbound process on Windows platform
            print("Windows implementation of unbound server has not been implented. Exiting.")
            exit(1)
        elif system_os == "Linux":
            #Todo: Add code for starting unbound process on Linux platform
            print("Linux implementation of unbound server has not been implented. Exiting.")
            exit(1)
        else:
            print("Unable to determine system OS to start unbound server. Exiting.")
            exit(1)

    def StopUnbound(self):
        print("Stopping Unbound")

        path_to_here = os.path.abspath(os.path.dirname(os.path.realpath(__file__)))

        #The unbound process is root owned and requires root access/sudo to kill.
        #Finds system and kills unbound process in OS specific way.
        system_os = platform.system()
        if system_os == "Darwin":
            #Uses svn to restore unbound.conf from previous runs
            p = subprocess.run("svn revert " + path_to_here + "/../unbound/mac/unbound.conf", shell=True)
            if p.returncode != 0:
                print("Could not use SVN to revert changes to unbound.conf")
                exit(1)

            p = subprocess.run("sudo killall unbound", shell=True)
            if p.returncode != 0:
                print("Could not kill unbound server.")
        elif system_os == "Windows":
            #Todo: Add code for starting unbound process on Windows platform
            print("Windows implementation of unbound server has not been implented. Exiting.")
            exit(1)
        elif system_os == "Linux":
            #Todo: Add code for starting unbound process on Linux platform
            print("Linux implementation of unbound server has not been implented. Exiting.")
            exit(1)
        else:
            print("Unable to determine system OS to start unbound server. Exiting.")

    def CleanUp(self):
        print("CleanUp to be implemented")