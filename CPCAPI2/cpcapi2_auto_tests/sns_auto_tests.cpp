#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#if (CPCAPI2_BRAND_SNS_MODULE == 1)

#include "cpcapi2_test_fixture.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>

#include "test_framework/cpcapi2_test_framework.h"
#include "test_framework/bare_bones_sip_endpoint.h"
#include "vccs_test_harness/VccsTestHarness.h" // reuse this from vccs unit tests

#include <cpcstl/string.h>
#include <string>
#include <thread>
#include <future>
#include <memory>

using namespace CPCAPI2;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::Notification;
using namespace CPCAPI2::test;

#ifdef _WIN32
#include <Windows.h>
#else
#include <sys/time.h>
#endif

namespace {

   class SnsModuleTest : public CpcapiAutoTest
   {
   public:
      SnsModuleTest() {}
      virtual ~SnsModuleTest() {}
   };

   TEST_F(SnsModuleTest, SNSConnect) {

      // NB: "alice" and "bob" are actually the same user (since syncing
      // really happens between different registrations of the same user)
      TestAccount alice("alice", Account_Init);

      // Create settings for the websocket
      WebSocket::WebSocketSettings wsSettings;
      wsSettings.webSocketURL   = "ws://127.0.0.1:8989/notification/subscription";
      //wsSettings.webSocketURL   = "wss://logging.bria-x.net:18082/notification/subscription";
      wsSettings.certMode       = WebSocket::CertVerificationMode_None;

      // Set the settings
      ChannelSettings settings;
      settings.wsSettings            = wsSettings;
      settings.authInfo.groupName    = "group1.1";
      settings.authInfo.userName     = "user0000@group1.1";
      settings.authInfo.password     = "user0000@group1.1:1234";
      settings.deviceUUID            = "test-user0000-tablet-1";
      settings.buildInfo.licenseKey  = "test-user0000-license12345";

      alice.notificationService->configureChannelSettings( alice.notificationHandle, settings );
      alice.notificationService->applySettings( alice.notificationHandle ); // prob. not needed

      // Start the test harness.
      VccsTestHarness harness( TestEnvironmentConfig::testResourcePath() + "vccs_test_harness_scenarios/SNSConnect.dat" );
      harness.start();

      // Enable the account.
      alice.notificationService->connect( alice.notificationHandle );

      // Wait for the account to reach the registered state
      {
         auto aliceRegistered = std::async(std::launch::async, [&] () {
            ChannelStateChangedEvent evt;
            ChannelHandle h;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.notificationEvents,
               "NotificationHandler::onChannelStateChanged",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
            ASSERT_TRUE( evt.oldState == ChannelState_Disconnected );
            ASSERT_TRUE( evt.newState == ChannelState_Connecting );

            ASSERT_TRUE( cpcWaitForEvent(
               alice.notificationEvents,
               "NotificationHandler::onChannelStateChanged",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
            ASSERT_TRUE( evt.oldState == ChannelState_Connecting );
            ASSERT_TRUE( evt.newState == ChannelState_Connected );
         });
         waitFor( aliceRegistered );
      }

      // Receive a test event (real events will differ from this one)
      {
         auto aliceEvent = std::async(std::launch::async, [&] () {
            NotificationEvent evt;
            ChannelHandle h;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.notificationEvents,
               "NotificationHandler::onNotification",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
            ASSERT_TRUE( evt.eventDateMillis > 0 );
            ASSERT_TRUE( evt.eventType == EventType_ServiceMessage );
            ASSERT_TRUE( evt.eventData == "Service Message" ); // :-)
         });
         waitFor( aliceEvent );
      }

      // Disable the account, and wait for the state to go back to Unregistered
      alice.notificationService->disconnect( alice.notificationHandle );
      {
         auto aliceUnRegistered = std::async(std::launch::async, [&] () {
            ChannelStateChangedEvent evt;
            ChannelHandle h;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.notificationEvents,
               "NotificationHandler::onChannelStateChanged",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
            ASSERT_TRUE( evt.oldState == ChannelState_Connected );
            ASSERT_TRUE( evt.newState == ChannelState_Disconnected );
         });
         waitFor( aliceUnRegistered );
      }
   }

}  // namespace

#endif // (CPCAPI2_BRAND_REMOTE_SYNC_MODULE == 1)

