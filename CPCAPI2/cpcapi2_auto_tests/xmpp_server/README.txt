The current version of openfire is 4.3.2, which can be defined in Dockerfile.

1. To build a docker iamge with openfire, in the directory where stores Dockerfile and entrypoint.sh, issue the command:
     docker build -t imageName .
2. Start a docker container with the volume folder mounted to this container, the serverconf folder contains the setup and config info of openfire.
     docker run --name CONTAINER_NAME -d --restart=always --hostname=dockertest --publish 9090:9090 --publish 5222:5222 --publish 7777:7777 --volume FULL/PATH/TO/serverconf:/var/lib/openfire IMAGE_NAME

The Domain name, server host name, RESTAPI secret key can be editd in /var/lib/openfire/embedded-db/opefire.script
To enable RESTAPI:
INSERT INTO OFPROPERTY VALUES('plugin.restapi.enabled','true',0)


To set Secret key authentication:
INSERT INTO OFPROPERTY VALUES('plugin.restapi.httpAuth','secret',0)

To edit the secret ket of RESTAPI:
INSERT INTO OFPROPERTY VALUES('plugin.restapi.secret','testsecretkey',0)

To change the xmpp domain name:
INSERT INTO OFPROPERTY VALUES('xmpp.domain','sdktestdomain',0)


To change the hostname of docker container:
INSERT INTO OFPROPERTY VALUES('xmpp.fqdn','dockertest',0)

To skip the setup phase of the openfire server:
INSERT INTO OFPROPERTY VALUES('setup','true',0)
and in /var/lib/openfire/conf/opefire.xml, set setup to true:  <setup>true</setup> 



