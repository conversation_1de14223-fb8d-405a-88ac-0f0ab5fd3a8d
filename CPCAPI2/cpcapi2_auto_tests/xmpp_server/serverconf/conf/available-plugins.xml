
<available>
  <plugin name="Avatar Resizer" latest="1.0.1" changelog="http://www.igniterealtime.org/projects/openfire/plugins/avatarresizer/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/avatarResizer.jar" author="Gu<PERSON> der Kinderen" description="Ensures vCard-based avatars are not to large for comfort." icon="http://www.igniterealtime.org/projects/openfire/plugins/cache/avatarresizer.gif" readme="http://www.igniterealtime.org/projects/openfire/plugins/avatarresizer/readme.html" fileSize="10624"/> 
  <plugin name="Bookmarks" latest="1.0.0" changelog="http://www.igniterealtime.org/projects/openfire/plugins/bookmarks/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/bookmarks.jar" author="Ignite Realtime" description="Allows clients to store URL and group chat bookmarks (XEP-0048)" icon="http://www.igniterealtime.org/projects/openfire/plugins/cache/bookmarks.gif" minServerVersion="4.0.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/bookmarks/readme.html" fileSize="63857"/> 
  <plugin name="External Service Discovery" latest="1.0.1" changelog="http://www.igniterealtime.org/projects/openfire/plugins/externalservicediscovery/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/externalservicediscovery.jar" author="Guus der Kinderen" description="Allows XMPP entities to discover services external to the XMPP network, such as STUN and TURN servers." minServerVersion="4.2.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/externalservicediscovery/readme.html" fileSize="95337"/> 
  <plugin name="SIP Phone Plugin" latest="1.2.0" changelog="http://www.igniterealtime.org/projects/openfire/plugins/sip/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/sip.jar" author="Ignite Realtime" description="Provides support for SIP account management" icon="http://www.igniterealtime.org/projects/openfire/plugins/cache/sip.gif" minServerVersion="4.0.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/sip/readme.html" fileSize="588571"/> 
  <plugin name="STUN server plugin" latest="1.2.2" changelog="http://www.igniterealtime.org/projects/openfire/plugins/stunserver/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/stunserver.jar" author="Ignite Realtime" description="Adds STUN functionality to Openfire" icon="http://www.igniterealtime.org/projects/openfire/plugins/cache/stunserver.gif" minServerVersion="4.0.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/stunserver/readme.html" fileSize="101310"/> 
  <plugin name="Load Statistic" latest="1.2.0" changelog="http://www.igniterealtime.org/projects/openfire/plugins/loadstats/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/loadStats.jar" author="Jive Software" description="Logs load statistics to a file" icon="http://www.igniterealtime.org/projects/openfire/plugins/cache/loadstats.gif" minServerVersion="3.9.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/loadstats/readme.html" fileSize="11425"/> 
  <plugin name="Fastpath Service" latest="4.4.3" changelog="http://www.igniterealtime.org/projects/openfire/plugins/fastpath/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/fastpath.jar" author="Jive Software" description="Support for managed queued chat requests, such as a support team might use." icon="http://www.igniterealtime.org/projects/openfire/plugins/cache/fastpath.gif" minServerVersion="4.1.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/fastpath/readme.html" fileSize="1471957"/> 
  <plugin name="HTTP File Upload" latest="1.1.0" changelog="http://www.igniterealtime.org/projects/openfire/plugins/httpfileupload/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/httpFileUpload.jar" author="Guus der Kinderen" description="Allows clients to share files, as described in the XEP-0363 'HTTP File Upload' specification." readme="http://www.igniterealtime.org/projects/openfire/plugins/httpfileupload/readme.html" fileSize="2078942"/> 
  <plugin name="TikiToken" latest="0.2.0" changelog="http://www.igniterealtime.org/projects/openfire/plugins/tikitoken/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/tikitoken.jar" author="Tiki Wiki CMS Groupware" description="Allows users to authenticate with a Tiki token." minServerVersion="4.1.3" readme="http://www.igniterealtime.org/projects/openfire/plugins/tikitoken/readme.html" licenseType="gpl" fileSize="358495"/> 
  <plugin name="Content Filter" latest="1.8.0" changelog="http://www.igniterealtime.org/projects/openfire/plugins/contentfilter/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/contentFilter.jar" author="Conor Hayes" description="Scans message packets for defined patterns" icon="http://www.igniterealtime.org/projects/openfire/plugins/cache/contentfilter.gif" minServerVersion="4.0.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/contentfilter/readme.html" fileSize="27570"/> 
  <plugin name="REST API" latest="1.3.7" changelog="http://www.igniterealtime.org/projects/openfire/plugins/restapi/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/restAPI.jar" author="Roman Soldatow" description="Allows administration over a RESTful API." icon="http://www.igniterealtime.org/projects/openfire/plugins/cache/restapi.gif" minServerVersion="4.1.1" readme="http://www.igniterealtime.org/projects/openfire/plugins/restapi/readme.html" fileSize="3487085"/> 
  <plugin name="Hazelcast plugin" latest="2.3.0" changelog="http://www.igniterealtime.org/projects/openfire/plugins/hazelcast/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/hazelcast.jar" author="Tom Evans" description="Adds clustering support" icon="http://www.igniterealtime.org/projects/openfire/plugins/cache/hazelcast.gif" minServerVersion="4.0.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/hazelcast/readme.html" fileSize="7972050"/> 
  <plugin name="Broadcast" latest="1.9.0" changelog="http://www.igniterealtime.org/projects/openfire/plugins/broadcast/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/broadcast.jar" author="Jive Software" description="Broadcasts messages to users." icon="http://www.igniterealtime.org/projects/openfire/plugins/cache/broadcast.gif" minServerVersion="3.9.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/broadcast/readme.html" fileSize="14071"/> 
  <plugin name="IPFS" latest="0.0.1" changelog="http://www.igniterealtime.org/projects/openfire/plugins/ipfs/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/ipfs.jar" author="igniterealtime.org" description="Enables Openfire to become an IPFS node." icon="http://www.igniterealtime.org/projects/openfire/plugins/cache/ipfs.gif" minServerVersion="4.0.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/ipfs/readme.html" licenseType="Apache 2.0" fileSize="********"/> 
  <plugin name="Registration" latest="1.7.2" changelog="http://www.igniterealtime.org/projects/openfire/plugins/registration/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/registration.jar" author="Ryan Graham" description="Performs various actions whenever a new user account is created." icon="http://www.igniterealtime.org/projects/openfire/plugins/cache/registration.gif" minServerVersion="4.0.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/registration/readme.html" fileSize="55992"/> 
  <plugin name="Search" latest="1.7.1" changelog="http://www.igniterealtime.org/projects/openfire/plugins/search/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/search.jar" author="Ryan Graham" description="Provides support for Jabber Search (XEP-0055)" icon="http://www.igniterealtime.org/projects/openfire/plugins/cache/search.gif" minServerVersion="4.0.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/search/readme.html" fileSize="50694"/> 
  <plugin name="Candy" latest="2.2.0 Release 2" changelog="http://www.igniterealtime.org/projects/openfire/plugins/candy/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/candy.jar" author="Guus der Kinderen" description="Adds the (third-party) Candy web client to Openfire." minServerVersion="4.1.5" readme="http://www.igniterealtime.org/projects/openfire/plugins/candy/readme.html" fileSize="577491"/> 
  <plugin name="Client Control" latest="2.1.3" changelog="http://www.igniterealtime.org/projects/openfire/plugins/clientcontrol/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/clientControl.jar" author="Jive Software" description="Controls clients allowed to connect and available features" icon="http://www.igniterealtime.org/projects/openfire/plugins/cache/clientcontrol.gif" minServerVersion="4.0.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/clientcontrol/readme.html" fileSize="143448"/> 
  <plugin name="Non-SASL Authentication" latest="1.0.0" changelog="http://www.igniterealtime.org/projects/openfire/plugins/nonsaslauthentication/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/nonSaslAuthentication.jar" author="Guus der Kinderen" description="This plugin implements a the (obsolete!) XEP-0078 specification for authentication using the jabber:iq:auth namespace." minServerVersion="4.1.0 Alpha" readme="http://www.igniterealtime.org/projects/openfire/plugins/nonsaslauthentication/readme.html" fileSize="10803"/> 
  <plugin name="MotD (Message of the Day)" latest="1.2.1" changelog="http://www.igniterealtime.org/projects/openfire/plugins/motd/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/motd.jar" author="Ryan Graham" description="Allows admins to have a message sent to users each time they log in." icon="http://www.igniterealtime.org/projects/openfire/plugins/cache/motd.gif" minServerVersion="4.0.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/motd/readme.html" fileSize="30843"/> 
  <plugin name="Presence Service" latest="1.7.0" changelog="http://www.igniterealtime.org/projects/openfire/plugins/presence/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/presence.jar" author="Jive Software" description="Exposes presence information through HTTP." icon="http://www.igniterealtime.org/projects/openfire/plugins/cache/presence.gif" minServerVersion="4.0.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/presence/readme.html" fileSize="29076"/> 
  <plugin name="Subscription" latest="1.4.0" changelog="http://www.igniterealtime.org/projects/openfire/plugins/subscription/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/subscription.jar" author="Ryan Graham" description="Automatically accepts or rejects subscription requests" icon="http://www.igniterealtime.org/projects/openfire/plugins/cache/subscription.gif" minServerVersion="4.0.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/subscription/readme.html" fileSize="20654"/> 
  <plugin name="CallbackOnOffline" latest="1.2.0" changelog="http://www.igniterealtime.org/projects/openfire/plugins/callbackonoffline/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/callbackOnOffline.jar" author="Pavel Goski / Krzysztof Misztal" description="Url is called when recipient is offline" minServerVersion="2.3.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/callbackonoffline/readme.html" fileSize="2139175"/> 
  <plugin name="inVerse" latest="4.0.2 Release 1" changelog="http://www.igniterealtime.org/projects/openfire/plugins/inverse/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/inverse.jar" author="Guus der Kinderen" description="Adds the (third-party, Converse-based) inVerse web client to Openfire." minServerVersion="4.1.5" readme="http://www.igniterealtime.org/projects/openfire/plugins/inverse/readme.html" fileSize="1933928"/> 
  <plugin name="Email on Away" latest="1.0.3" changelog="http://www.igniterealtime.org/projects/openfire/plugins/emailonaway/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/emailOnAway.jar" author="Nick Mossie" description="Messages sent to alternate location when recipient is away" minServerVersion="2.3.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/emailonaway/readme.html" fileSize="5923"/> 
  <plugin name="XML Debugger Plugin" latest="1.7.1" changelog="http://www.igniterealtime.org/projects/openfire/plugins/xmldebugger/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/xmldebugger.jar" author="Jive Software" description="Prints XML traffic to the stdout (raw and interpreted XML)" minServerVersion="4.0.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/xmldebugger/readme.html" fileSize="1675349"/> 
  <plugin name="Certificate Manager" latest="1.0.0" changelog="http://www.igniterealtime.org/projects/openfire/plugins/certificatemanager/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/certificateManager.jar" author="Guus der Kinderen" description="Adds certificate management features." minServerVersion="4.0.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/certificatemanager/readme.html" fileSize="40718"/> 
  <plugin name="Kraken IM Gateway" latest="1.3.1" changelog="http://www.igniterealtime.org/projects/openfire/plugins/kraken/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/kraken.jar" author="Daniel Henninger" description="Adds transports to other IM networks." icon="http://www.igniterealtime.org/projects/openfire/plugins/cache/kraken.gif" minServerVersion="4.2.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/kraken/readme.html" licenseType="apache2" fileSize="5049368"/> 
  <plugin name="MUC Service" latest="0.2.3" changelog="http://www.igniterealtime.org/projects/openfire/plugins/mucservice/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/mucservice.jar" author="Roman Soldatow" description="(Deprecated) Please use the REST API Plugin. MUC administration over REST Interface" icon="http://www.igniterealtime.org/projects/openfire/plugins/cache/mucservice.gif" minServerVersion="3.9.1" readme="http://www.igniterealtime.org/projects/openfire/plugins/mucservice/readme.html" fileSize="2568767"/> 
  <plugin name="JmxWeb Plugin" latest="0.0.7" changelog="http://www.igniterealtime.org/projects/openfire/plugins/jmxweb/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/jmxweb.jar" author="igniterealtime.org" description="JmxWeb plugin is web based platform for managing and monitoring openfire via JMX." minServerVersion="4.1.5" readme="http://www.igniterealtime.org/projects/openfire/plugins/jmxweb/readme.html" licenseType="Apache 2.0" fileSize="9659320"/> 
  <plugin name="User Status Plugin" latest="1.2.0" changelog="http://www.igniterealtime.org/projects/openfire/plugins/userstatus/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/userStatus.jar" author="Stefan Reuter" description="Openfire plugin to save the user status to the database." icon="http://www.igniterealtime.org/projects/openfire/plugins/cache/userstatus.gif" minServerVersion="4.0.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/userstatus/readme.html" licenseType="gpl" fileSize="26234"/> 
  <plugin name="DB Access" latest="1.2.0" changelog="http://www.igniterealtime.org/projects/openfire/plugins/dbaccess/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/dbaccess.jar" author="Daniel Henninger" description="Provides administrators with a simple direct access interface to their Openfire DB." icon="http://www.igniterealtime.org/projects/openfire/plugins/cache/dbaccess.gif" minServerVersion="4.0.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/dbaccess/readme.html" fileSize="10309"/> 
  <plugin name="GoJara" latest="2.2.1" url="http://www.igniterealtime.org/projects/openfire/plugins/gojara.jar" author="Holger Bergunde / Daniel Henninger / Axel-F. Brand" description="XEP-0321: Remote Roster Management support" minServerVersion="4.0.0" fileSize="319125"/> 
  <plugin name="User Service" latest="2.1.0" changelog="http://www.igniterealtime.org/projects/openfire/plugins/userservice/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/userservice.jar" author="Roman Soldatow, Justin Hunt" description="(Deprecated) Please use the REST API Plugin. Allows administration of users via HTTP requests." icon="http://www.igniterealtime.org/projects/openfire/plugins/cache/userservice.gif" minServerVersion="4.0.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/userservice/readme.html" fileSize="2588031"/> 
  <plugin name="User Import Export" latest="2.6.2" changelog="http://www.igniterealtime.org/projects/openfire/plugins/userimportexport/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/userImportExport.jar" author="Ryan Graham" description="Enables import and export of user data" icon="http://www.igniterealtime.org/projects/openfire/plugins/cache/userimportexport.gif" minServerVersion="4.0.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/userimportexport/readme.html" fileSize="97576"/> 
  <plugin name="Rayo Plugin" latest="0.1.0" changelog="http://www.igniterealtime.org/projects/openfire/plugins/rayo/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/rayo.jar" author="Ignite Realtime Community" description="Provides support for XEP-0327" icon="http://www.igniterealtime.org/projects/openfire/plugins/cache/rayo.gif" minServerVersion="4.0.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/rayo/readme.html" fileSize="20758842"/> 
  <plugin name="Just married" latest="1.2.2" changelog="http://www.igniterealtime.org/projects/openfire/plugins/justmarried/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/justmarried.jar" author="Holger Bergunde" description="Allows admins to rename or copy users" minServerVersion="4.0.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/justmarried/readme.html" fileSize="373194"/> 
  <plugin name="NodeJs" latest="0.1.0" changelog="http://www.igniterealtime.org/projects/openfire/plugins/nodejs/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/nodejs.jar" author="igniterealtime.org" description="Integrates NodeJs Applications with Openfire." icon="http://www.igniterealtime.org/projects/openfire/plugins/cache/nodejs.gif" minServerVersion="4.0.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/nodejs/readme.html" licenseType="Apache 2.0" fileSize="15635100"/> 
  <plugin name="Openfire Meetings" latest="0.9.4" changelog="http://www.igniterealtime.org/projects/openfire/plugins/ofmeet/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/ofmeet.jar" author="Ignite Realtime" description="Provides high quality, scalable video conferences using Jitsi Meet and Videobridge." icon="http://www.igniterealtime.org/projects/openfire/plugins/cache/ofmeet.gif" minServerVersion="4.1.5" readme="http://www.igniterealtime.org/projects/openfire/plugins/ofmeet/readme.html" fileSize="45397740"/> 
  <plugin name="Packet Filter" latest="3.3.0" changelog="http://www.igniterealtime.org/projects/openfire/plugins/packetfilter/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/packetFilter.jar" author="Nate Putnam" description="Rules to enforce ethical communication" icon="http://www.igniterealtime.org/projects/openfire/plugins/cache/packetfilter.gif" minServerVersion="4.0.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/packetfilter/readme.html" fileSize="100700"/> 
  <plugin name="User Creation" latest="1.3.0" changelog="http://www.igniterealtime.org/projects/openfire/plugins/usercreation/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/userCreation.jar" author="Jive Software" description="Creates users and populates rosters." minServerVersion="4.0.0" fileSize="20930"/> 
  <plugin name="Openfire WebSocket" latest="1.2.1" changelog="http://www.igniterealtime.org/projects/openfire/plugins/websocket/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/websocket.jar" author="Tom Evans" description="Provides WebSocket support for Openfire." icon="http://www.igniterealtime.org/projects/openfire/plugins/cache/websocket.gif" minServerVersion="4.1.5" readme="http://www.igniterealtime.org/projects/openfire/plugins/websocket/readme.html" fileSize="122092"/> 
  <plugin name="Email Listener" latest="1.2.0" changelog="http://www.igniterealtime.org/projects/openfire/plugins/emaillistener/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/emailListener.jar" author="Jive Software" description="Listens for emails and sends alerts to specific users." icon="http://www.igniterealtime.org/projects/openfire/plugins/cache/emaillistener.gif" minServerVersion="4.0.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/emaillistener/readme.html" fileSize="21093"/> 
  <plugin name="Monitoring Service" latest="1.6.2" changelog="http://www.igniterealtime.org/projects/openfire/plugins/monitoring/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/monitoring.jar" author="IgniteRealtime // Jive Software" description="Monitors conversations and statistics of the server." icon="http://www.igniterealtime.org/projects/openfire/plugins/cache/monitoring.gif" minServerVersion="4.1.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/monitoring/readme.html" fileSize="10263517"/> 
  <plugin name="Openfire Focus Provider" latest="0.9.4" changelog="http://www.igniterealtime.org/projects/openfire/plugins/offocus/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/offocus.jar" author="Ignite Realtime" description="Instantiates a Jitsi Focus manager." icon="http://www.igniterealtime.org/projects/openfire/plugins/cache/offocus.gif" minServerVersion="4.1.5" readme="http://www.igniterealtime.org/projects/openfire/plugins/offocus/readme.html" fileSize="27570117"/> 
  <plugin name="Jingle Nodes Plugin" latest="0.2.0" changelog="http://www.igniterealtime.org/projects/openfire/plugins/jinglenodes/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/jingleNodes.jar" author="Jingle Nodes (Rodrigo Martins)" description="Provides support for Jingle Nodes" icon="http://www.igniterealtime.org/projects/openfire/plugins/cache/jinglenodes.gif" minServerVersion="4.0.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/jinglenodes/readme.html" fileSize="1038541"/>
</available>