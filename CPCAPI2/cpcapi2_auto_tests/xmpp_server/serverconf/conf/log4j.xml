<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE log4j:configuration SYSTEM "log4j.dtd">
<log4j:configuration xmlns:log4j="http://jakarta.apache.org/log4j/">

    <appender name="debug-out" class="org.apache.log4j.RollingFileAppender">
        <param name="File" value="${openfireHome}/logs/debug.log" />
        <param name="MaxFileSize" value="1024KB"/>
        <param name="MaxBackupIndex" value="5"/>
        <layout class="org.apache.log4j.PatternLayout">
            <param name="ConversionPattern" value="%d{yyyy.MM.dd HH:mm:ss} %c - %m%n" />
        </layout>
        <filter class="org.apache.log4j.varia.LevelRangeFilter">
            <param name="LevelMax" value="debug" />
            <param name="AcceptOnMatch" value="true" />
        </filter>
    </appender>

    <appender name="info-out" class="org.apache.log4j.RollingFileAppender">
        <param name="File" value="${openfireHome}/logs/info.log" />
        <param name="MaxFileSize" value="1024KB"/>
        <param name="MaxBackupIndex" value="5"/>
        <layout class="org.apache.log4j.PatternLayout">
            <param name="ConversionPattern" value="%d{yyyy.MM.dd HH:mm:ss} %c - %m%n" />
        </layout>
        <filter class="org.apache.log4j.varia.LevelRangeFilter">
            <param name="LevelMax" value="info" />
            <param name="LevelMin" value="info" />
            <param name="AcceptOnMatch" value="true" />
        </filter>
    </appender>

    <appender name="warn-out" class="org.apache.log4j.RollingFileAppender">
        <param name="File" value="${openfireHome}/logs/warn.log" />
        <param name="MaxFileSize" value="1024KB"/>
        <param name="MaxBackupIndex" value="5"/>
        <layout class="org.apache.log4j.PatternLayout">
            <param name="ConversionPattern" value="%d{yyyy.MM.dd HH:mm:ss} %c - %m%n" />
        </layout>
        <filter class="org.apache.log4j.varia.LevelRangeFilter">
            <param name="LevelMax" value="warn" />
            <param name="LevelMin" value="warn" />
            <param name="AcceptOnMatch" value="true" />
        </filter>
    </appender>
    
    <appender name="error-out" class="org.apache.log4j.RollingFileAppender">
        <param name="File" value="${openfireHome}/logs/error.log" />
        <param name="MaxFileSize" value="1024KB"/>
        <param name="MaxBackupIndex" value="5"/>
        <layout class="org.apache.log4j.PatternLayout">
            <param name="ConversionPattern" value="%d{yyyy.MM.dd HH:mm:ss} %c - %m%n" />
        </layout>
        <filter class="org.apache.log4j.varia.LevelRangeFilter">
            <param name="LevelMin" value="error" />
            <param name="AcceptOnMatch" value="true" />
        </filter>
    </appender>

    <appender name="all-out" class="org.apache.log4j.RollingFileAppender">
        <param name="File" value="${openfireHome}/logs/all.log" />
        <param name="MaxFileSize" value="1024KB"/>
        <param name="MaxBackupIndex" value="5"/>
        <layout class="org.apache.log4j.PatternLayout">
            <param name="ConversionPattern" value="%d{yyyy.MM.dd HH:mm:ss} %-5p [%t]: %c - %m%n" />
        </layout>
    </appender>

    <appender name="console" class="org.apache.log4j.ConsoleAppender">
        <layout class="org.apache.log4j.EnhancedPatternLayout">
            <param name="ConversionPattern" value="%m%n%throwable{0}" />
        </layout>
        <filter class="org.apache.log4j.varia.LevelRangeFilter">
            <param name="LevelMin" value="info" />
            <param name="AcceptOnMatch" value="true" />
        </filter>
    </appender>

    <!-- OF-1095: Uniform output of loading/unloading of plugins to std-out. -->
    <logger name="org.jivesoftware.openfire.container.PluginManager">
        <appender-ref ref="console"/>
    </logger>
    <logger name="org.jivesoftware.openfire.container.PluginMonitor">
        <appender-ref ref="console"/>
    </logger>

    <!-- OF-506: Jetty INFO messages are generally not useful. Ignore them by default. -->
    <logger name="org.eclipse.jetty">
        <level value="warn" />
    </logger>
    
    <root>
        <level value="info" />
        <appender-ref ref="all-out" />
        <appender-ref ref="debug-out" />
        <appender-ref ref="info-out" />
        <appender-ref ref="warn-out" />
        <appender-ref ref="error-out" />
    </root>
    
</log4j:configuration>
