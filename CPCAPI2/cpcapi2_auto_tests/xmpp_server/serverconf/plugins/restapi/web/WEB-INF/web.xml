<?xml version='1.0' encoding='ISO-8859-1'?>
<web-app xmlns="http://xmlns.jcp.org/xml/ns/javaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/javaee http://xmlns.jcp.org/xml/ns/javaee/web-app_3_1.xsd"
         version="3.1">
    <!-- Servlets -->
    <servlet>
        <servlet-name>JerseyWrapper</servlet-name>
        <servlet-class>org.jivesoftware.openfire.plugin.rest.service.JerseyWrapper</servlet-class>
    </servlet>

    <!-- Servlet mappings -->
    <servlet-mapping>
        <servlet-name>JerseyWrapper</servlet-name>
        <url-pattern>/v1/*</url-pattern>
    </servlet-mapping>

<context-param><param-name>org.eclipse.jetty.jsp.precompiled</param-name><param-value>true</param-value></context-param>

<!--
Automatically created by Apache Tomcat JspC.
Place this fragment in the web.xml before all icon, display-name,
description, distributable, and context-param elements.
-->

    <servlet>
        <servlet-name>org.jivesoftware.openfire.plugin.restAPI.rest_002dapi_jsp</servlet-name>
        <servlet-class>org.jivesoftware.openfire.plugin.restAPI.rest_002dapi_jsp</servlet-class>
    </servlet>

    <servlet-mapping>
        <servlet-name>org.jivesoftware.openfire.plugin.restAPI.rest_002dapi_jsp</servlet-name>
        <url-pattern>/rest-api.jsp</url-pattern>
    </servlet-mapping>

<!--
All session-config, mime-mapping, welcome-file-list, error-page, taglib,
resource-ref, security-constraint, login-config, security-role,
env-entry, and ejb-ref elements should follow this fragment.
-->
</web-app>
