<?xml version="1.0" encoding="UTF-8"?>

<plugin>
    <class>org.jivesoftware.openfire.plugin.rest.RESTServicePlugin</class>
    <name>REST API</name>
    <description>Allows administration over a RESTful API.</description>
    <author><PERSON>datow</author>
    <version>1.3.7</version>
    <date>08/30/2018</date>
    <minServerVersion>4.1.1</minServerVersion>
    <adminconsole>
        <tab id="tab-server">
            <sidebar id="sidebar-server-settings">
                <item id="rest-api" name="REST API" url="rest-api.jsp"
                     description="Click to manage the service that allows to configure the Openfire over a RESTFul API" />
            </sidebar>
        </tab>
    </adminconsole>

</plugin>
