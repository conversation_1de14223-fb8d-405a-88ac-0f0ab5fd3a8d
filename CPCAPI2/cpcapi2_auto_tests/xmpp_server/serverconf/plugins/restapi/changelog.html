<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">

<html>
<head>
    <title>REST API Plugin Changelog</title>
    <style type="text/css">
        BODY {
            font-size : 100%;
        }
        BOD<PERSON>, TD, TH {
            font-family : tahoma, verdana, arial, helvetica, sans-serif;
            font-size : 0.8em;
        }
        H2 {
             font-size : 10pt;
             font-weight : bold;
             padding-left : 1em;
        }
        A:hover {
            text-decoration : none;
        }
        H1 {
            font-family : tahoma, arial, helvetica, sans-serif;
            font-size : 1.4em;
            font-weight: bold;
            border-bottom : 1px #ccc solid;
            padding-bottom : 2px;
        }

        TT {
            font-family : courier new;
            font-weight : bold;
            color : #060;
        }
        PRE {
            font-family : courier new;
            font-size : 100%;
        }
    </style>
</head>
<body>

<h1>
REST API Plugin Changelog
</h1>

<p><b>1.3.7</b> -- August 30, 2018</p>
<ul>
    <li>When retrieving chat history on a nonexistent room, return the standard 404 Not found response code and a meaningful message.</li>
</ul>

<p><b>1.3.6</b> -- August 27, 2018</p>
<ul>
    <li>Fixed: Important security issue, that allowed to use REST API without authentication</li>
</ul>

<p><b>1.3.5</b> -- August 13, 2018</p>
<ul>
    <li>Added GET method for MUC Chat Room history</li>
    <li>Added minimal logging for MUCRoom and User APIs</li>
</ul>

<p><b>1.3.4</b> -- July 25, 2018</p>
<ul>
    <li>Replaced Log4J usage with SLF4J (which is not a functional change, but required for compatibility with Openfire 4.3.0).</li>
    <li>Requires Openfire 4.1.1.</li>
</ul>

<p><b>1.3.3</b> -- April 26, 2018</p>
<ul>
    <li>Fixed: Delete Room now propagated to other cluster nodes <a href="https://issues.igniterealtime.org/browse/OF-1540">OF-1540</a></li>
</ul>

<p><b>1.3.2</b> -- April 25th, 2018</p>
<ul>
     <li>Added: GZIP compression</li>
</ul>

<p><b>1.3.1</b> -- April 20th, 2018</p>
<ul>
     <li>Fixed: That created rooms were not propagated to other nodes <a href="https://issues.igniterealtime.org/browse/OF-1535">OF-1535</a></li>
     <li>Fixed: Missing documentation for "new" json mapping</li>
</ul>

<p><b>1.3.0</b> -- March 7th, 2018</p>
<ul>
     <li>Added: Security Audit endpoint to get the security logs</li>
     <li>Added: More details by error</li>
     <li>Improvement: Better JSON mapping (e.g. single element in an array bug)</li>
</ul>

<p><b>1.2.6</b> -- May 31, 2017</p>
<ul>
     <li>Updated to match new API in Openfire 4.2.0</li>
     <li>Slight optimization for copying the user properties.</li>
</ul>

<p><b>1.2.5</b> -- October 14th, 2016</p>
<ul>
     <li>Updated to match new API in Openfire 4.1.0</li>
     <li>Requires Openfire 4.1.0 or later.</li>
</ul>

<p><b>1.2.4</b> -- July 4th, 2016</p>
<ul>
     <li>Fixed: Send a presence by affiliation change</li>
</ul>

<p><b>1.2.3</b> -- May 3rd, 2016</p>
<ul>
     <li>Added: Add a group with role to a chat room</li>
     <li>Added: Occupants endpoint for chat room</li>
     <li>Fixed: Admin and Member list to group endpoint</li>
</ul>

<p><b>1.2.2</b> -- January 20th, 2016</p>
<ul>
     <li>Added: Presence status to the Session Enitity. E.g. Online, Away etc.</li>
     <li>Fixed: Node parameter in the Session Enitity. E.g. Local or Remote</li>
</ul>

<p><b>1.2.1</b> -- November 24th, 2015</p>
<ul>
     <li>Fixed: Cluster issue by creating a new chat room</li>
</ul>

<p><b>1.2.0</b> -- October 12th, 2015</p>
<ul>
    <li>[<a href='http://www.igniterealtime.org/issues/browse/OF-953'>OF-953</a>] - Updated JSP libraries.</li>
    <li>Requires Openfire 3.11.0.</li>
</ul>

<p><b>1.1.7</b> -- November 13th, 2015</p>
<ul>
     <li>Added: Provide the possibility to use a Custom Auth Filter</li>
     <li>Fixed: Preflight request will be not blocked by Authentication</li>
     <li>Added: Group names in ChatRoom Entity</li>
</ul>
<p><b>1.1.6</b> -- September 24th, 2015</p>
<ul>
     <li>Added: Endpoints to add / remove a user from a user group</li>
     <li>Fixed: Error response in JSON format</li>
</ul>

<p><b>1.1.5</b> -- September 1st, 2015</p>
<ul>
     <li>Added: Send broadcast message to all online users</li>
</ul>

<p><b>1.1.4</b> -- August 19th, 2015</p>
<ul>
     <li>Added: get concurrent sessions (local or cluster wide)</li>
</ul>

<p><b>1.1.3</b> -- August 15th, 2015</p>
<ul>
     <li>Added: get count of users unread messages</li>
</ul>

<p><b>1.1.2</b> -- August 4th, 2015</p>
<ul>
     <li>Added: CORS to all endpoints</li>
</ul>

<p><b>1.1.1</b> -- June 29th, 2015</p>
<ul>
     <li>Added: new endpoint to close user sessions</li>
</ul>

<p><b>1.1.0</b> -- June 3rd, 2015</p>
<ul>
     <li>Added: new endpoints for sessions (Get overview over all or specific user sessions)</li>
</ul>

<p><b>1.0.2</b> -- March 3rd, 2015</p>
<ul>
     <li>User will be kicked by a lockout (ban)</li>
     <li>Added: new endpoints for groups (Get overview over all or specific group and to create, update or delete a group)</li>
</ul>

<p><b>1.0.1</b> -- February 20th, 2015</p>
<ul>
     <li>Added possibility to rename a user (Thanks to JustMarried plugin)</li>
     <li>Adjusted HTTP Codes by conflict to HTTP CODE: 409</li>
     <li>Added subject to Chat room</li>
     <li>Disabled jersey logging on startup</li>
     <li>By create a new chat room the chat room service will be created if it was not there</li>
</ul>

<p><b>1.0.0</b> -- February 3rd, 2015</p>
<ul>
     <li>UserService plugin and MUC Service plugin are merged to the REST API plugin.</li>
     <li>Extended REST API with JSON data format.</li>
</ul>

<p><b>0.1.0</b> -- November 14th, 2014</p>
<ul>
     <li>Initial release of REST API Plugin with possibility to manage system properties.</li>
</ul>

</body>
</html>
