#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#if (CPCAPI2_BRAND_GENBAND_SOPI_MODULE == 1)
#define WITH_OPENSSL
#define WITH_NONAMESPACES
#include "cpcapi2_test_fixture.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>

#include "test_framework/cpcapi2_test_framework.h"
#include "test_framework/bare_bones_sip_endpoint.h"

#include <thread>
#include <future>

using namespace CPCAPI2;
using namespace CPCAPI2::GenbandSopi;
using namespace CPCAPI2::test;

namespace {

class GenbandSopiModuleTest : public CpcapiAutoTest
{
public:
   GenbandSopiModuleTest() {}
   virtual ~GenbandSopiModuleTest() {}
};

class SopiHandler : public GenbandSopiHandler
{
public:
   SopiHandler()
   {
      handle = 0;
      hasError = false;
   }
   
   int onPersonalAddressBookUpdated(GenbandSopiClientHandle client, const AddressBookUpdatedEvent& args)
   {
      handle = client;
      entries = args.entries;
      groups.clear();
      hasError = false;
      safeCout("Received event: onPersonalAddressBookUpdated");
      return kSuccess;
   }

   int onPersonalAddressBookGroupsUpdated(GenbandSopiClientHandle client, const AddressBookGroupsUpdatedEvent& args)
   {
      handle = client;
      groups = args.groups;
      entries.clear();
      hasError = false;
      safeCout("Received event: onPersonalAddressBookGroupsUpdated");
      return kSuccess;
   }

   int onGlobalAddressBookSearchResult(GenbandSopiClientHandle client, const SearchGlobalAddressBookResultEvent& args)
   {
      handle = client;
      entries = args.entries;
      groups.clear();
      hasError = false;
      safeCout("Received event: onGlobalAddressBookSearchResult");
      return kSuccess;
   }

   int onGetAuthorizedUsers(GenbandSopiClientHandle client, const GetAuthorizedUsersEvent& args)
   {
      handle = client;
//      entries = args.entries;
      groups.clear();
      hasError = false;
      safeCout("Received event: onGetAuthorizedUsers");
      return kSuccess;
   }

   int onGetBannedUsers(GenbandSopiClientHandle client, const GetBannedUsersEvent& args)
   {
      handle = client;
//      entries = args.entries;
      groups.clear();
      hasError = false;
      safeCout("Received event: onGetBannedUsers");
      return kSuccess;
   }

   int onGetPoliteBlockedUsers(GenbandSopiClientHandle client, const GetPoliteBlockedUsersEvent& args)
   {
      handle = client;
//      entries = args.entries;
      groups.clear();
      hasError = false;
      safeCout("Received event: onGetPoliteBlockedUsers");
      return kSuccess;
   }

   int onError(GenbandSopiClientHandle client, const ErrorEvent& args)
   {
      handle = client;
      entries.clear();
      groups.clear();
      hasError = true;
      errorText = args.errorText;
      safeCout("Received error: " << args.errorText);
      return kSuccess;
   }

   GenbandSopiClientHandle handle;
   cpc::vector<AddressBookEntry> entries;
   cpc::vector<cpc::string> groups;
   bool hasError;
   cpc::string errorText;

};

TEST_F(GenbandSopiModuleTest, GenbandSopiOperations) {
   TestAccount alice("alice");

   GenbandSopiClientSettings settings;
// gives error when using SSL: SSL_ERROR_SSL error : 1408F10B : SSL routines : ssl3_get_record:wrong version number
   settings.username = "<EMAIL>";
   settings.password = "1234";
   settings.serverUrl = "http://labproxy01.netas.com.tr:8444/sopi/services/";
   settings.ignoreCertValidation = true;
// gives error: Received error: Error in AddAddressBookEntry : com.nortelnetworks.ims.base.prov.opi.shared.InvalidDataException: Invalid Username or Password.
//   settings.username = "g18@netas-y";  // also tried g19@netas-y
//   settings.password = "1234";
//   settings.serverUrl = "http://labproxy02.netas.com.tr:8444/sopi/services/";

   GenbandSopiClientHandle handle = alice.genbandSopiManager->createClient(settings);

   std::unique_ptr<SopiHandler> mySopiHandler(new SopiHandler());
   alice.genbandSopiManager->setHandler(handle, mySopiHandler.get());

   // add new entry
   AddressBookEntry newEntry;
   newEntry.nickName = "CPCAPI2_TEST";
   newEntry.primaryContact = "<EMAIL>";
   newEntry.email = "<EMAIL>";
   newEntry.business = "************";
   alice.genbandSopiManager->addAddressBookEntry(handle, newEntry);

   std::this_thread::sleep_for(std::chrono::milliseconds(10000));
   alice.genbandSopiManager->process(1000);
   // ignore if the add failed due to a conflict
   if (mySopiHandler->errorText.find("Another entry with the nickname") == cpc::string::npos)
      ASSERT_FALSE(mySopiHandler->hasError);

   alice.genbandSopiManager->requestAddressBook(handle);
   alice.genbandSopiManager->process(10);
   alice.genbandSopiManager->requestAddressBook(handle);
   std::this_thread::sleep_for(std::chrono::milliseconds(3000));
   alice.genbandSopiManager->process(1000);

   cpc::vector<AddressBookEntry>::const_iterator it = mySopiHandler->entries.begin();
   ASSERT_TRUE(it != mySopiHandler->entries.end());
   AddressBookEntry updateEntry = *it;

   updateEntry.business = (updateEntry.business.find("************") == cpc::string::npos) ? "************" : "250 787 7887";
   alice.genbandSopiManager->updateAddressBookEntry(handle, updateEntry.nickName, updateEntry);

   std::this_thread::sleep_for(std::chrono::milliseconds(3000));
   alice.genbandSopiManager->process(1000);
   ASSERT_FALSE(mySopiHandler->hasError);

   // check if entry exists on server
   alice.genbandSopiManager->requestAddressBook(handle);
   std::this_thread::sleep_for(std::chrono::milliseconds(3000));
   alice.genbandSopiManager->process(1000);

   // expect some results
   ASSERT_TRUE(handle == mySopiHandler->handle);
   ASSERT_FALSE(mySopiHandler->hasError);
   ASSERT_TRUE(mySopiHandler->entries.size() > 0);

   bool foundMyEntry = false;
   it = mySopiHandler->entries.begin();
   for (; it != mySopiHandler->entries.end(); ++it)
   {
      AddressBookEntry candidate = *it;
      if (candidate.nickName == newEntry.nickName && candidate.business == updateEntry.business)
      {
         foundMyEntry = true;
         break;
      }
   }
   ASSERT_TRUE(foundMyEntry);

/* It looks like deleting an entry is either broken or I'm doing it wrong.
   alice.genbandSopiManager->deleteAddressBookEntry(handle, updateEntry.nickName);

   std::this_thread::sleep_for(std::chrono::milliseconds(3000));
   // Make sure the SDK observer got invoked
   alice.genbandSopiManager->process(1000);
   ASSERT_FALSE(mySopiHandler->hasError);

   // check if entry exists on server
   alice.genbandSopiManager->requestAddressBook(handle);
   std::this_thread::sleep_for(std::chrono::milliseconds(3000));
   alice.genbandSopiManager->process(1000);
   ASSERT_FALSE(mySopiHandler->hasError);

   foundMyEntry = false;
   it = mySopiHandler->entries.begin();
   for (; it != mySopiHandler->entries.end(); ++it)
   {
      AddressBookEntry candidate = *it;
      if (candidate.nickName == newEntry.nickName)
      {
         foundMyEntry = true;
         break;
      }
   }
   ASSERT_FALSE(foundMyEntry);      // if this goes off the delete above didn't work!
*/

   alice.genbandSopiManager->destroy(handle);
}

}

#endif
