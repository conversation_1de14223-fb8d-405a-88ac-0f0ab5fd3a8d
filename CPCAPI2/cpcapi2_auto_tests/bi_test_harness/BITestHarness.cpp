#include "BITestHarness.h"

#include <stringbuffer.h>
#include <writer.h>
#include <document.h>
#include <util/strettorpc/RPCTimestamp.h>

#define SESSION_COUNT 5

using namespace CPCAPI2::test;

BITestHarness::BITestHarness( const std::string & resourcePath, int serverPort, BITestSimulationFailureOptions simulateFailures )
   : m_ServerThread( NULL ),
     m_ResourcePath( resourcePath ),
     m_ServerPort( serverPort ),
     m_Server(),
     m_SimulateFailures( simulateFailures ),
     m_SessionCounter( SESSION_COUNT )
{
   m_Server.config.port = serverPort;
   m_Server.resource[resourcePathRegex(resourcePath)]["POST"]=[ this ](
      std::shared_ptr<HttpServer::Response> response,
      std::shared_ptr<HttpServer::Request> request)
   {
      std::string requestContent( request->content.string() );
      std::string responseContent;

      if( m_SessionCounter <= 0 )
      {
         // Every so often ask for a new sesesion from the client
         m_SessionCounter = SESSION_COUNT;
         responseContent  = "{\"strettorpc\":\"1.0\",\"error\":{\"code\":-32408,\"message\":\"Expired Session\"},\"id\":\"9329c8f624ee7fb56e78feb3a403ca4ae2702960a151f679f21a98bfe2180669\" }";
         *response << "HTTP/1.1 200 OK\r\nContent-Type: application/json\r\nContent-Length: " << responseContent.length() << "\r\n\r\n" << responseContent;
         return;
      }

      if( m_SimulateFailures.periodicErrorResponse )
      {
         static int i = 0;

         if( requestContent.find( "~auth~", 0 ) != std::string::npos )
         {
            // Behave normally for auth requests
            if( buildResponse( requestContent, responseContent ))
            {
               std::this_thread::sleep_for( std::chrono::milliseconds( m_SimulateFailures.authResponseDelayMs ) );
               *response << "HTTP/1.1 200 OK\r\nContent-Type: application/json\r\nContent-Length: " << responseContent.length() << "\r\n\r\n" << responseContent;
               --m_SessionCounter;
            }
            else
            {
               std::this_thread::sleep_for( std::chrono::milliseconds( m_SimulateFailures.otherResponseDelayMs ) );
               *response << "HTTP/1.1 500 Error\r\n\r\n";
            }
         }
         else if(( i++ % 3 ) == 0 )
         {
            std::this_thread::sleep_for( std::chrono::milliseconds( m_SimulateFailures.otherResponseDelayMs ) );
            
            *response << "HTTP/1.1 500 Error\r\n\r\n";
         }
         else if( buildResponse( requestContent, responseContent ))
         {
            std::this_thread::sleep_for( std::chrono::milliseconds( m_SimulateFailures.otherResponseDelayMs ) );
            
            // get body contents, and echo it back in the response
            *response << "HTTP/1.1 200 OK\r\nContent-Type: application/json\r\nContent-Length: " << responseContent.length() << "\r\n\r\n" << responseContent;
            --m_SessionCounter;
         }
         else
         {
            std::this_thread::sleep_for( std::chrono::milliseconds( m_SimulateFailures.otherResponseDelayMs ) );
         
            // oopsie
            *response << "HTTP/1.1 500 Error\r\n\r\n";
         }
      }
      else
      {
         if( buildResponse( requestContent, responseContent ))
         {
            *response << "HTTP/1.1 200 OK\r\nContent-Type: application/json\r\nContent-Length: " << responseContent.length() << "\r\n\r\n" << responseContent;
            --m_SessionCounter;
         }
         else
         {
            *response << "HTTP/1.1 500 Error\r\n\r\n";
         }
      }
   };
}

BITestHarness::~BITestHarness()
{
   delete m_ServerThread;
}

bool BITestHarness::start()
{
   if( m_ServerThread != NULL )
      return false;

   // What this code does: creates a new thread instance with a lambda
   // which defines a method which is run by the thread, that method just
   // calls m_Server.start(), which is a blocking method.
   m_ServerThread = new std::thread( [this]()
   {
      m_Server.start();
   });
   std::this_thread::sleep_for( std::chrono::seconds( 1 ));
   return true;
}

bool BITestHarness::stop()
{
   if( m_ServerThread == NULL )
      return false;

   m_Server.stop();
   return true;
}

bool BITestHarness::waitForCompletion()
{
   if( m_ServerThread == NULL )
      return false;

   m_ServerThread->join();
   return true;
}

bool BITestHarness::buildResponse( const std::string& request, std::string& outResponse )
{
   if( request.find( "~auth~", 0 ) != std::string::npos )
   {
      std::this_thread::sleep_for( std::chrono::milliseconds( m_SimulateFailures.authResponseDelayMs ) );
   
      // Kluge to separate the auth request from regular requests
      outResponse = "{\"strettorpc\":\"1.0\",\"result\":{\"session\":\"7ee844fd-4255-4a3b-8ecd-106be712680f\",\"expires\":\"2017-02-07 07:58:47\",\"token\":\"16f6cf6a-365c-4a6b-9581-0f30a5b58761\",\"scope\":\"[\\\"public\\\"]\"},\"id\":\"~auth~\"}";
      return true;
   }
   else if( request.find( "~end~", 0 ) != std::string::npos )
   {
      std::this_thread::sleep_for( std::chrono::milliseconds( m_SimulateFailures.otherResponseDelayMs ) );
   
      // Kluge to respond correctly to end requests.
      outResponse = "{\"strettorpc\":\"1.0\",\"result\":[ \"good bye\" ],\"id\":\"9329c8f624ee7fb56e78feb3a403ca4ae2702960a151f679f21a98bfe2180669\"}";
      return true;
   }
   else if( request.find( "druid_check", 0 ) != std::string::npos )
   {
      std::this_thread::sleep_for( std::chrono::milliseconds( m_SimulateFailures.druidResponseDelayMs ) );
   
      // Another kluge to separate the druid_check request from regular requests
      outResponse = "{\"strettorpc\":\"1.0\",\"result\":{\"druid\":\"d80d7113-38f7-4497-ad19-e68745c30b54\",\"status\":\"new\"},\"id\":\"9329c8f624ee7fb56e78feb3a403ca4ae2702960a151f679f21a98bfe2180669\"}";
      return true;
   }

   // First, parse the request in a JSONy way
   rapidjson::Document requestDoc;
   requestDoc.SetObject();
   requestDoc.Parse< 0 >( request.c_str() );
   if( requestDoc.HasParseError() )
      return false;

   // Get the params from the request
   if( !requestDoc.HasMember( "params" ))
      return false;

   // params->events
   rapidjson::Value &requestParamsVal = requestDoc[ "params" ];
   if( !requestParamsVal.HasMember( "events" ))
      return false;

   // grab the ID. remember for later
   if( !requestDoc.HasMember( "id" ))
      return false;

   rapidjson::Value &requestIDVal = requestDoc[ "id" ];

   if( m_SimulateFailures.rejectedRequests > 0 )
   {
      // Reject the request (at the JSON level) if the unit test asked us to
      outResponse;
      outResponse += "{\"strettorpc\":\"1.0\",\"error\":{\"code\":-32503,\"message\":\"Internal STRETTO-RPC error\"},\"id\" : \"";
      outResponse += requestIDVal.GetString();
      outResponse += "\"}";
      --m_SimulateFailures.rejectedRequests;
      return true;
   }

   // events->array of information
   rapidjson::Value &requestEventArray = requestParamsVal[ "events" ];
   if( !requestEventArray.IsArray() )
      return false;

   // Build the root response doc
   rapidjson::Document responseDoc;
   responseDoc.SetObject();
   responseDoc.AddMember( "strettorpc", "1.0", responseDoc.GetAllocator() );

   // Add the ID here
   rapidjson::Value idValue( requestIDVal.GetString(), responseDoc.GetAllocator() ); // copy semantics
   responseDoc.AddMember( "id", idValue, responseDoc.GetAllocator() );

   // Add a result section
   rapidjson::Value resultVal;
   resultVal.SetObject();

   // Add events array to result
   rapidjson::Value resultEventsVal;
   resultEventsVal.SetArray();

   // Now, for each event in the request, add a matching section in the response.
   for( rapidjson::SizeType i = 0 ; i < requestEventArray.Size() ; ++i )
   {
      rapidjson::Value& requestEvent( requestEventArray[ i ] );
      rapidjson::Value responseEvent;
      responseEvent.SetObject();

      if( !requestEvent.HasMember( "type" ))
         continue;

      // Copy the type from the request
      rapidjson::Value typeValue( requestEvent[ "type" ].GetString(), responseDoc.GetAllocator() ); // copy semantics
      responseEvent.AddMember( "type", typeValue, responseDoc.GetAllocator() );

      // Add a timestamp.
      RPCTimestamp ts;
      std::string timeStampStr;
      if( ts.toString( timeStampStr ))
      {
         rapidjson::Value tsValue( timeStampStr.c_str(), responseDoc.GetAllocator() ); // copy semantics
         responseEvent.AddMember( "time", tsValue, responseDoc.GetAllocator() );
      }

      // Declare it to be a success
      responseEvent.AddMember( "status", "SUCC", responseDoc.GetAllocator() );

      // Add to the result array
      resultEventsVal.PushBack( responseEvent, responseDoc.GetAllocator() );
   }

   // Add the array to the parent
   resultVal.AddMember( "events", resultEventsVal, responseDoc.GetAllocator() );

   // Add the result to the response
   responseDoc.AddMember( "result", resultVal, responseDoc.GetAllocator() );

   rapidjson::StringBuffer strbuf( 0, 1024 );
   rapidjson::Writer<rapidjson::StringBuffer> writer( strbuf );
   responseDoc.Accept( writer );
   
   std::this_thread::sleep_for( std::chrono::milliseconds( m_SimulateFailures.otherResponseDelayMs ) );
   
   outResponse = strbuf.GetString();
   return true;
}
