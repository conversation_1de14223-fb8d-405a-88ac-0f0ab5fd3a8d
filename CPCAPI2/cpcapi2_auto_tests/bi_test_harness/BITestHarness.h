#pragma once
#ifndef __BITESTHARNESS_H__
#define __BITESTHARNESS_H__

#include <sstream>
#include "server_http.hpp"

namespace CPCAPI2
{
   namespace test
   {
      typedef SimpleWeb::Server<SimpleWeb::HTTP> HttpServer;

      class BITestSimulationFailureOptions
      {
      public:
         int druidResponseDelayMs;
         int authResponseDelayMs;
         int otherResponseDelayMs;
         bool periodicErrorResponse;
         int rejectedRequests; // reject this many requests
         
         BITestSimulationFailureOptions()
         {
            druidResponseDelayMs = 0;
            authResponseDelayMs = 0;
            otherResponseDelayMs = 0;
            periodicErrorResponse = false;
            rejectedRequests = 0;
         }
      };

      class BITestHarness
      {
      public:

         BITestHarness( const std::string& resourcePath = "/API/1.0", int serverPort = 9090,
                        BITestSimulationFailureOptions simulateFailures = BITestSimulationFailureOptions() );
         virtual ~BITestHarness();

         // start/stop processing of the harness in another thread.
         bool start();
         bool stop();
         bool waitForCompletion();

         std::string serverBaseUrl() const
         {
            std::ostringstream ss;
            ss << "http://127.0.0.1:" << m_ServerPort;
            return ss.str().c_str();
         }

         std::string resourcePathRegex(const std::string& resourcePath)
         {
            std::stringstream ss;
            ss << resourcePath << "$";
            return ss.str();
         }

         bool buildResponse( const std::string& request, std::string& outResponse );

      private:
         std::thread                         *m_ServerThread;
         std::string                         m_ResourcePath;
         int                                 m_ServerPort;
         HttpServer                          m_Server;
         BITestSimulationFailureOptions      m_SimulateFailures;
         int                                 m_SessionCounter;
         int                                 m_RejectedRequests;
      };
   }
}


#endif /* __BITESTHARNESS_H__ */
