#pragma once
#ifndef __VCCSTESTHARNESS_H__
#define __VCCSTESTHARNESS_H__

#include <websocketpp/config/asio.hpp>
#include <websocketpp/server.hpp>

namespace CPCAPI2
{
   namespace test
   {
      class VccsTestHarness
      {
      public:

         VccsTestHarness( const char* scenarioFilePath, int serverPort = 8989, bool tlsServer = false );
         virtual ~VccsTestHarness();

         // start/stop processing of the harness in another thread.
         bool start();
         bool stop();
         bool waitForCompletion();

         typedef websocketpp::server< websocketpp::config::asio > server;
         typedef websocketpp::server< websocketpp::config::asio_tls > server_tls;

         bool read_another_line();
         bool send_document( websocketpp::connection_hdl hdl, const char *text );
         bool close_connection( websocketpp::connection_hdl hdl, const char *closeText );

         void on_open(websocketpp::connection_hdl hdl);
         void on_message(websocketpp::connection_hdl hdl, server::message_ptr msg);
         void on_close( websocketpp::connection_hdl hdl );
         websocketpp::lib::shared_ptr< boost::asio::ssl::context > on_tls_init(server_tls* s, websocketpp::connection_hdl hdl);

      private:
         void openScenarioFile(const std::string& scenarioFilePath);
         void closeScenarioFile();

         bool isInboundMessage( const char* text) const;
         bool isInboundPartialMessage(const char* text) const;

         std::string getCertPassword() const;

      private:
         websocketpp::lib::thread mHarnessThread;
         FILE *mScenarioFile;
         char *mScenarioLine;
         int   mServerPort;
         bool  mIsTLSServer;

         server mScenarioServer;
         server_tls mScenarioServerTLS;

         std::atomic<bool> mCloseOnStop;
         std::string mScenarioFilePath;
         
         int mConnectionOpenCount;
      };
   }
}


#endif /* __VCCSTESTHARNESS_H__ */