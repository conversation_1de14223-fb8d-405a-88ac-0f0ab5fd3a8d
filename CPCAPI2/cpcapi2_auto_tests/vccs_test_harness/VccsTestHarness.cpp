#include <stdio.h>
#include <iostream>
#include <string>
#include <fstream>

#include <document.h> // rapidjson
#include <stringbuffer.h> // rapidjson
#include <writer.h> // rapidjson

#include <utils/msrp_string.h> // strtok_r
#include "util/BoostTlsHelper.h"

#include "VccsTestHarness.h"
#include "../cpcapi2_test_fixture.h"

#define LINE_CHUNK  1024

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::VCCS

#include "rutil/Logger.hxx"
#include "util/LogSubsystems.h"

using namespace CPCAPI2::test;

VccsTestHarness::VccsTestHarness( const char* scenarioFilePath, int serverPort, bool tlsServer ) :
   mScenarioFile( NULL ),
   mScenarioLine( NULL ),
   mServerPort( serverPort ),
   mIsTLSServer( tlsServer ),
   mScenarioFilePath( scenarioFilePath ),
   mConnectionOpenCount( 0 )
{
   // open the scenario file
   openScenarioFile(mScenarioFilePath);
}

VccsTestHarness::~VccsTestHarness()
{
   stop();
   waitForCompletion();

   // close the scenario file
   closeScenarioFile();

   if( mScenarioLine != NULL )
   {
      free( mScenarioLine );
      mScenarioLine = NULL;
   }
}

void VccsTestHarness::openScenarioFile(const std::string& scenarioFilePath)
{
   mScenarioFile = fopen(scenarioFilePath.c_str(), "r");
   if (mScenarioFile == NULL)
   {
      std::cerr << "Could not open file: " << scenarioFilePath << " for reading." << std::endl;
      throw std::runtime_error("Could not open file: " + scenarioFilePath + " for reading.");
   }
}

void VccsTestHarness::closeScenarioFile()
{
   if (mScenarioFile != NULL)
   {
      fclose(mScenarioFile);
      mScenarioFile = NULL;
   }
}

bool VccsTestHarness::start()
{
   if( mIsTLSServer )
   {
      mScenarioServerTLS.set_access_channels(websocketpp::log::alevel::all);
      mScenarioServerTLS.clear_access_channels(websocketpp::log::alevel::all);

      mScenarioServerTLS.init_asio();

      mScenarioServerTLS.set_reuse_addr( true );
      mScenarioServerTLS.set_open_handler( std::bind( &VccsTestHarness::on_open, this, std::placeholders::_1 ));
      mScenarioServerTLS.set_message_handler( std::bind( &VccsTestHarness::on_message, this, std::placeholders::_1, std::placeholders::_2 ));
      mScenarioServerTLS.set_close_handler( std::bind( &VccsTestHarness::on_close, this, std::placeholders::_1 ));
      mScenarioServerTLS.set_tls_init_handler(std::bind(&VccsTestHarness::on_tls_init, this, &mScenarioServerTLS, std::placeholders::_1));

      mScenarioServerTLS.listen( mServerPort );
      mScenarioServerTLS.start_accept();
      mScenarioServerTLS.start_perpetual();

      std::this_thread::sleep_for( std::chrono::seconds( 1 ));
      mHarnessThread = websocketpp::lib::thread( &server_tls::run, &mScenarioServerTLS );
   }
   else
   {
      mScenarioServer.set_access_channels(websocketpp::log::alevel::all);
      mScenarioServer.clear_access_channels(websocketpp::log::alevel::all);

      mScenarioServer.init_asio();

      mScenarioServer.set_reuse_addr( true );
      mScenarioServer.set_open_handler( std::bind( &VccsTestHarness::on_open, this, std::placeholders::_1 ));
      mScenarioServer.set_message_handler( std::bind( &VccsTestHarness::on_message, this, std::placeholders::_1, std::placeholders::_2 ));
      mScenarioServer.set_close_handler( std::bind( &VccsTestHarness::on_close, this, std::placeholders::_1 ));

      mScenarioServer.listen( mServerPort );
      mScenarioServer.start_accept();
      mScenarioServer.start_perpetual();

      std::this_thread::sleep_for( std::chrono::seconds( 1 ));
      mHarnessThread = websocketpp::lib::thread( &server::run, &mScenarioServer );
   }
   return true;
}

bool VccsTestHarness::stop()
{
   if( mIsTLSServer )
   {
      mScenarioServerTLS.set_open_handler( nullptr );
      mScenarioServerTLS.set_message_handler( nullptr );
      mScenarioServerTLS.set_close_handler( nullptr );

      // When the connection is closed from the client, terminate the scenario server
      if( !mScenarioServerTLS.stopped() )
      {
         try
         {
            mScenarioServerTLS.stop_perpetual();
            if (mScenarioServerTLS.is_listening())
            {
               websocketpp::lib::error_code ec;
               mScenarioServerTLS.stop_listening(ec);
            }

            mScenarioServerTLS.stop();
         }
         catch (const boost::system::system_error& ex)
         {
            std::cerr << "Exception " << ex.what() << " while trying to stop scenario server";
         }
      }
   }
   else
   {
      mScenarioServer.set_open_handler( nullptr );
      mScenarioServer.set_message_handler( nullptr );
      mScenarioServer.set_close_handler( nullptr );

      // When the connection is closed from the client, terminate the scenario server
      if( !mScenarioServer.stopped() )
      {
         try
         {
            mScenarioServer.stop_perpetual();
            if (mScenarioServer.is_listening())
            {
               websocketpp::lib::error_code ec;
               mScenarioServer.stop_listening(ec);
            }
            mScenarioServer.stop();
         }
         catch (const boost::system::system_error& ex)
         {
            std::cerr << "Exception " << ex.what() << " while trying to stop scenario server";
         }
      }
   }


   return true;
}

bool VccsTestHarness::waitForCompletion()
{
   if( mHarnessThread.joinable() )
      mHarnessThread.join();

   return true;
}

// Reads another line into mScenarioLine. returns false when there are no more lines
// available to be read (end of file)
bool VccsTestHarness::read_another_line()
{
   if( mScenarioLine != NULL )
   {
      free( mScenarioLine );
      mScenarioLine = NULL;
   }

   mScenarioLine = ( char * ) malloc( LINE_CHUNK * sizeof( char ));
   if (NULL == mScenarioLine)
   {
      throw std::bad_alloc();
   }
   memset( mScenarioLine, '\n', LINE_CHUNK * sizeof( char ));

   int read = 0;
   int chunk_count = 1;
   while( fgets( mScenarioLine + read, LINE_CHUNK, mScenarioFile ) != NULL )
   {
      uint8_t *match = ( uint8_t * ) memchr( mScenarioLine + read, '\n', LINE_CHUNK );
      if( match == NULL )
      {
         read = ( chunk_count * LINE_CHUNK ) - 1;
         ++chunk_count;
         mScenarioLine = ( char * ) realloc( mScenarioLine, ( chunk_count * LINE_CHUNK * sizeof( char )));
         if (NULL == mScenarioLine)
         {
            throw std::bad_alloc();
         }
         memset( mScenarioLine + read, '\n', LINE_CHUNK * sizeof( char ));
      }
      else
      {
         // Entire contents of line was read. Replace the \n with \0.
         *match = '\0';

         // Trim the whole line
         mScenarioLine = ( char * ) realloc( mScenarioLine, strlen( mScenarioLine ) + 1 );
         DebugLog(<< "Read scenario line: " << mScenarioLine);
         return true;
      }
   }

   // Nothing else to read
   if( mScenarioLine != NULL )
   {
      free( mScenarioLine );
      mScenarioLine = NULL;
   }

   return false;
}

bool VccsTestHarness::send_document( websocketpp::connection_hdl hdl, const char *text )
{
   rapidjson::Document doc;
   doc.Parse<0>( text );
   if (doc.HasParseError())
   {
      throw std::runtime_error("Parse failure");
   }

   rapidjson::StringBuffer strbuf(0, 1024);
   rapidjson::Writer<rapidjson::StringBuffer> writer(strbuf);
   doc.Accept(writer);
   std::string message = strbuf.GetString();

   websocketpp::lib::error_code ec;

   if( mIsTLSServer )
      mScenarioServerTLS.send(hdl, message, websocketpp::frame::opcode::text, ec);
   else
      mScenarioServer.send(hdl, message, websocketpp::frame::opcode::text, ec);

   if( ec )
      return false;

   return true;
}

bool CPCAPI2::test::VccsTestHarness::close_connection( websocketpp::connection_hdl hdl, const char * closeText )
{
   websocketpp::close::status::value code( 0 );
   std::string reason( "Going away" );

   char *temp = strdup( closeText );
   char *context = NULL;
   char *token = NULL;

   // First, parse the code
   token = strtok_r( temp, " \r\n\t", &context );
   if( token != NULL )
   {
      code = atoi( token );

      // Try to parse the reason
      token = strtok_r( NULL, "\r\n", &context );
      if( token != NULL )
         reason = token;
   }
   free( temp );

   // Parse two tokens from the closeText line. The first is a close code, and the second is a close reason.
   try
   {
      if( mIsTLSServer )
         mScenarioServerTLS.close( hdl, code, reason );
      else
         mScenarioServer.close( hdl, code, reason );
   }
   catch (const std::exception& ex)
   {
      std::cerr << "Exception " << ex.what() << " while trying to close scenario server";
   }

   return true;
}

inline bool file_exists(const std::string& name) {
   std::ifstream f(name.c_str());
   return f.good();
}

std::string CPCAPI2::test::VccsTestHarness::getCertPassword() const
{
   // password for root_key_fluffyCA.pem
   return "password";
}

websocketpp::lib::shared_ptr<boost::asio::ssl::context> CPCAPI2::test::VccsTestHarness::on_tls_init( server_tls * s, websocketpp::connection_hdl hdl )
{
   // Stolen from JsonApiServer_WebSocket.cpp
   SslCipherOptions tlsSettings;
   websocketpp::lib::shared_ptr<boost::asio::ssl::context> context = initializeBoostTlsContext(tlsSettings.getTLSVersion(SslCipherUsageWebSockets), tlsSettings.getCiphers(SslCipherUsageWebSockets).c_str(), resip::SecurityTypes::TLSMode_TLS_Server, (TestEnvironmentConfig::testResourcePath() + "vccs_test_harness_dh2048.pem").c_str());
   try
   {
      // https://stackoverflow.com/questions/6452756/exception-running-boost-asio-ssl-example

      context->set_password_callback(std::bind(&VccsTestHarness::getCertPassword, this));

      const std::string chainFile = (TestEnvironmentConfig::testResourcePath() + "root_cert_fluffyCA.pem").c_str();
      if (file_exists(chainFile))
      {
         context->use_certificate_chain_file(chainFile);
      }

      const std::string privateKeyFile = (TestEnvironmentConfig::testResourcePath() + "root_key_fluffyCA.pem").c_str();
      if (file_exists(privateKeyFile))
      {
         context->use_private_key_file(privateKeyFile, boost::asio::ssl::context::pem);
      }
   }
   catch (std::exception& e)
   {
      std::cerr << "Encountered an exception while trying to setup TLS/certs for the websocket:" << e.what() << std::endl;
   }
   return context;
}

void VccsTestHarness::on_open(websocketpp::connection_hdl hdl)
{
   if (NULL == mScenarioFile)
   {
      throw std::runtime_error("Scenario file not open");
   }
   
   ++mConnectionOpenCount;

   if (mConnectionOpenCount == 1)
   {
      // if mConnectionOpenCount > 1, the client must have reconnected.
      // don't bother reading another line, because this harness would already
      // have the next expected line read into mScenarioLine.
      // this assumes a client reconnecting would send the first message,
      // not wait to receive a message over the websocket.
      while( read_another_line() )
      {
         // check the first three chars to see if they represent an incoming our
         // outgoing message.
         if( isInboundMessage( mScenarioLine ) )
            break; // inbound message expected

         // Process any close line found
         if( strncmp( mScenarioLine, "c: ", 3 ) == 0 )
         {
            close_connection( hdl, mScenarioLine + 3 );
            break;
         }

         // outbound message. Send to the remote side.
         if( !send_document( hdl, mScenarioLine + 3 ))
            break;
      }
   }
}

bool VccsTestHarness::isInboundMessage(const char* text) const
{
   return (strncmp(mScenarioLine, "i: ", 3) == 0 || // full match
           isInboundPartialMessage(text));
}

bool VccsTestHarness::isInboundPartialMessage(const char* text) const
{
   return strncmp(mScenarioLine, "I: ", 3) == 0;
}

void VccsTestHarness::on_message(websocketpp::connection_hdl hdl, server::message_ptr msg)
{

	const std::string message = msg->get_payload();
	std::cout << "Received: " << message << std::endl;

   if (NULL == mScenarioFile)
   {
      throw std::runtime_error("Scenario file not open");
   }
   if (NULL == mScenarioLine)
   {
      throw std::runtime_error("Scenario line is null");
   }

   // We are receiving an incoming message, so we should be expecting
   // an incoming line.
   bool expectIncoming( isInboundMessage( mScenarioLine ) );
   if (!expectIncoming)
   {
      throw std::invalid_argument("Expected incoming message");
   }

   // Get the expected line and compare it with the parsed contents
   const char *expectedLine = mScenarioLine + 3;
   //const std::string message = msg->get_payload();
   //std::cout << "Received: " << message << std::endl;

   bool equality = false;
   if (isInboundPartialMessage( mScenarioLine ) )
   {
      const char* expectedPrefix = expectedLine;
      equality = strncmp( expectedPrefix, message.c_str(), strlen( expectedPrefix ) ) == 0;
   }
   else
   {
      equality = ( strcmp( expectedLine, message.c_str() ) == 0 );
   }

   if( !equality )
   {
      std::stringstream err;
      err << "Expected line does not match actual line!" << std::endl
          << "\tExpected: " << expectedLine << std::endl
          << "\tActual  : " << message << std::endl;
      std::cerr << err.str();
      throw std::invalid_argument(err.str());
   }

   // Process all outgoing lines until we hit the end of file, or
   // another incoming line.
   while( read_another_line() )
   {
      // check the first three chars to see if they represent an incoming our
      // outgoing message.
      if( isInboundMessage( mScenarioLine ) )
         break; // inbound message expected

      // Process any close line found
      if( strncmp( mScenarioLine, "c: ", 3 ) == 0 )
      {
         close_connection( hdl, mScenarioLine + 3 );
         break;
      }

      // outbound message. Send to the remote side.
      if( !send_document( hdl, mScenarioLine + 3 ))
         break;
   }

   //std::cout << msg->get_payload() << std::endl;
}

void VccsTestHarness::on_close( websocketpp::connection_hdl hdl )
{
}
