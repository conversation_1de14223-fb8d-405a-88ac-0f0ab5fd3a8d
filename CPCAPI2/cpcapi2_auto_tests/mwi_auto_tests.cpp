#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#include "cpcapi2_test_fixture.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>
#include "event/SipEventManagerInternal.h"

#include "test_framework/cpcapi2_test_framework.h"
#include "test_framework/bare_bones_sip_endpoint.h"

#include <thread>
#include <future>
#include <sstream>

using namespace CPCAPI2;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::SipConversation;
using namespace CPCAPI2::SipMessageWaitingIndication;
using namespace CPCAPI2::SipEvent;
using namespace CPCAPI2::test;

namespace {

class MWIModuleTest : public CpcapiAutoTest
{
public:
   MWIModuleTest() {}
   virtual ~MWIModuleTest() {}
};

// disabled until we have a solid MWI server test account to run against
TEST_F(MWIModuleTest, DISABLED_CounterPathMWI) {

   TestAccount max("max", Account_NoInit);
   max.config.settings.domain = "***********";
   max.config.settings.username = "100";
   max.config.settings.password = "headway";
   max.config.settings.outboundProxy = "***********:6060";
   max.enable();

   SipMWISubscriptionHandle handle = max.mwi->createSubscription(max.handle);
   SipMWISubscriptionSettings mwiSubSettings;
   max.mwi->applySubscriptionSettings(handle, mwiSubSettings);
   max.mwi->start(handle);

   auto maxEvents = std::async(std::launch::async, [&] () {
      SipMWISubscriptionHandle h;
      IncomingMWIStatusEvent evt;
      ASSERT_TRUE(max.mwiEvents->expectEvent(
         "SipMessageWaitingIndicationHandler::onIncomingMWIStatus", 
         15000,
         AlwaysTruePred(),
         h, evt));
      std::cout << "hasMessages? " << evt.hasMessages << std::endl;
   });
   ASSERT_EQ(maxEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
   ASSERT_NO_THROW(maxEvents.get());
}

TEST_F(MWIModuleTest, RejectedMWI)
{
   // Test for OBELISK-110
   // Trying to subscribe to MWI for a server like ser.diesel.counterpath.net 
   // which doesn't act as a subscription agent. The subscribe is forwarded back to us and ends up being treated as a new incoming subscription.
   TestAccount max("max");
   SipMWISubscriptionHandle handle = max.mwi->createSubscription(max.handle);
   SipMWISubscriptionSettings mwiSubSettings;
   max.mwi->applySubscriptionSettings(handle, mwiSubSettings);
   max.mwi->start(handle);

   SipMWISubscriptionHandle h;
   {
      MWISubscriptionStateChangedEvent evt;
      ASSERT_TRUE(max.mwiEvents->expectEvent(
         "SipMessageWaitingIndicationHandler::onSubscriptionStateChanged", 
         15000, AlwaysTruePred(), h, evt));
   }
   {
      MWISubscriptionEndedEvent evt;
      ASSERT_TRUE(max.mwiEvents->expectEvent(
         "SipMessageWaitingIndicationHandler::onSubscriptionEnded", 
         15000, AlwaysTruePred(), h, evt));
      ASSERT_EQ(SipSubscriptionEndReason_ServerEnded, evt.endReason);
   }
}

TEST_F(MWIModuleTest, RejectedMWI2)
{
   TestAccount alice("alice");
   TestAccount server("server"); // Mock server to handle incoming subsription

   // Clear the default subscription event handler on the server for the MWI event package, so that the server can respond to the subscription
   if (server.mwi != NULL)
   {
       // Clear license success callback
       ((CPCAPI2::PhoneInterface*)server.phone)->process_test(0);
       server.mwi->setHandler(server.handle, NULL);
   }
   if (server.subs != NULL)
   {
       server.subs->setHandler(server.handle, "message-summary", (SipEventSubscriptionHandler*)0xDEADBEEF);
       server.phone->process(-1);
   }

   auto serverEvents = std::async(std::launch::async, [&] ()
   {
      SipEventSubscriptionHandle eventHandle;
      {
         NewSubscriptionEvent evt;
         ASSERT_TRUE(cpcExpectEvent(server.subsEvents, "SipEventSubscriptionHandler::onNewSubscription", 5000, AlwaysTruePred(), eventHandle, evt));
         ASSERT_EQ(evt.account, alice.handle);
         ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
         ASSERT_EQ(evt.remoteDisplayName, alice.config.name);
         ASSERT_EQ(evt.eventPackage, "message-summary");
         ASSERT_EQ(evt.eventPackageParams.size(), 0);
         ASSERT_EQ(evt.supportedMimeTypes.size(), 1);
         ASSERT_EQ(evt.supportedMimeTypes[0].mimeType, "application");
         ASSERT_EQ(evt.supportedMimeTypes[0].mimeSubType, "simple-message-summary");
      }
      
      /*
      // Can verify the both sip events and mwi events when the sip event manager supports multiple handlers for the same package.
      SipMWISubscriptionHandle mwiHandle;
      {
         MWISubscriptionStateChangedEvent evt;
         ASSERT_TRUE(server.mwiEvents->expectEvent("SipMessageWaitingIndicationHandler::onSubscriptionStateChanged", 15000, AlwaysTruePred(), mwiHandle, evt));
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Terminated);
      }
      */

      server.subs->reject(eventHandle, 489);
      {
         SubscriptionStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(server.subsEvents, "SipEventSubscriptionHandler::onSubscriptionStateChanged", 5000, AlwaysTruePred(), eventHandle, evt));
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Terminated);
      }
      
      {
         SubscriptionEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(server.subsEvents, "SipEventSubscriptionHandler::onSubscriptionEnded", 5000, AlwaysTruePred(), eventHandle, evt));
         ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
         ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
      }
   });

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      SipEventSubscriptionHandle subscription = alice.subs->createSubscription(alice.handle);
      SipEventSubscriptionSettings subscriptionSettings;
      subscriptionSettings.eventPackage = "message-summary";
      subscriptionSettings.expiresSeconds = 3600;
      subscriptionSettings.supportedMimeTypes.push_back(CPCAPI2::MimeType("application", "simple-message-summary"));
      alice.subs->applySubscriptionSettings(subscription, subscriptionSettings);
      alice.subs->addParticipant(subscription, server.config.uri());
      alice.subs->start(subscription);

      SipMWISubscriptionHandle mwiHandle;
      {
         MWISubscriptionEndedEvent evt;
         ASSERT_TRUE(alice.mwiEvents->expectEvent("SipMessageWaitingIndicationHandler::onSubscriptionEnded", 15000, AlwaysTruePred(), mwiHandle, evt));
         ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }
   
      /*
      // Can verify the actual SIP response only when the sip event manager supports multiple handlers for the same package.
      SipEventSubscriptionHandle eventHandle;
      {
         SubscriptionEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.subsEvents, "SipEventSubscriptionHandler::onSubscriptionEnded", 5000, AlwaysTruePred(), eventHandle, evt));
         ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
         ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Outgoing);
         ASSERT_EQ(evt.statusCode, 489); // Event Package Not Supported
         ASSERT_EQ(evt.remoteAddress, server.config.uri());
      }
      */
   });
   
   waitFor2(aliceEvents, serverEvents);
}

TEST_F(MWIModuleTest, AcceptedMWI)
{
   TestAccount alice("alice");
   TestAccount server("server"); // Mock server to handle incoming subsription
   
   // Clear the default subscription event handler on the server for the MWI event package, so that the server can respond to the subscription
   if (server.mwi != NULL)
   {
       // Clear license success callback
       ((CPCAPI2::PhoneInterface*)server.phone)->process_test(0);
       server.mwi->setHandler(server.handle, NULL);
   }
   if (server.subs != NULL)
   {
      server.subs->setHandler(server.handle, "message-summary", (SipEventSubscriptionHandler*)0xDEADBEEF);
      server.phone->process(-1);
   }
   
   auto serverEvents = std::async(std::launch::async, [&] ()
   {
      SipEventSubscriptionHandle eventHandle;
      {
         NewSubscriptionEvent evt;
         ASSERT_TRUE(cpcExpectEvent(server.subsEvents, "SipEventSubscriptionHandler::onNewSubscription", 5000, AlwaysTruePred(), eventHandle, evt));
         ASSERT_EQ(evt.account, alice.handle);
         ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
         ASSERT_EQ(evt.remoteDisplayName, alice.config.name);
         ASSERT_EQ(evt.eventPackage, "message-summary");
         ASSERT_EQ(evt.eventPackageParams.size(), 0);
         ASSERT_EQ(evt.supportedMimeTypes.size(), 1);
         ASSERT_EQ(evt.supportedMimeTypes[0].mimeType, "application");
         ASSERT_EQ(evt.supportedMimeTypes[0].mimeSubType, "simple-message-summary");

         SipEventState state;
         state.mimeType = "application";
         state.mimeSubType = "simple-message-summary";
         state.eventPackage = "message-summary";
         std::stringstream str;
         str << "Messages-Waiting: no\r\n";
         str << "Message-Account: " << alice.config.uri() << "\r\n";
         str << "Voice-Message: 0/0 (0/0)\r\n";
         str << "Fax-Message: 0/0 (0/0)\r\n";
         str << "None: 0/0 (0/0)\r\n";
         state.contentUTF8 = str.str().c_str();
         state.contentLength = state.contentUTF8.size();
         state.expiresTimeMs = 86400;
         server.subs->accept(eventHandle, state);
         
         // Triggers parse error
         /*
         SipEventState state2 = state;
         std::stringstream str2;
         str2 << "Messages-Waiting: yes\r\n";
         str2 << "Voicemail: 1/0\r\n";
         str2 << "Voice-Message: 1/0\r\n";
         */
         
         // Parsed successfully but breaks protocol RFC3842, Voice-Message should be right after Messages-Waiting
         /*
         std::stringstream str2;
         str2 << "Messages-Waiting: yes\r\n";
         str2 << "Voicemail: 1/0\r\n\r\n";
         str2 << "Voice-Message: 1/0\r\n";
         */
         
         // Parsed successfully
         SipEventState state2 = state;
         std::stringstream str2;
         str2 << "Messages-Waiting: yes\r\n";
         str2 << "Voice-Message: 1/0\r\n\r\n";
         str2 << "Voicemail: 1/0\r\n"; // voicemail won't be counted as MWI item due to double CRLF
         
         state2.contentUTF8 = str2.str().c_str();
         state2.contentLength = state2.contentUTF8.size();
         state2.expiresTimeMs = 86400;
         server.subs->notify(eventHandle, state2);
         
         server.subs->end(eventHandle);
      }
   });
   
   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      SipEventSubscriptionHandle subscription = alice.subs->createSubscription(alice.handle);
      SipEventSubscriptionSettings subscriptionSettings;
      subscriptionSettings.eventPackage = "message-summary";
      subscriptionSettings.expiresSeconds = 86400;
      subscriptionSettings.supportedMimeTypes.push_back(CPCAPI2::MimeType("application", "simple-message-summary"));
      alice.subs->applySubscriptionSettings(subscription, subscriptionSettings);
      alice.subs->addParticipant(subscription, server.config.uri());
      alice.subs->start(subscription);

      SipMWISubscriptionHandle mwiHandle;
      {
         NewMWISubscriptionEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.mwiEvents, "SipMessageWaitingIndicationHandler::onNewSubscription", 5000, AlwaysTruePred(), mwiHandle, evt));
         ASSERT_EQ(evt.account, alice.handle);
      }

      {
         MWISubscriptionStateChangedEvent evt;
         ASSERT_TRUE(alice.mwiEvents->expectEvent("SipMessageWaitingIndicationHandler::onSubscriptionStateChanged", 15000, AlwaysTruePred(), mwiHandle, evt));
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      {
         IncomingMWIStatusEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.mwiEvents, "SipMessageWaitingIndicationHandler::onIncomingMWIStatus", 5000, AlwaysTruePred(), mwiHandle, evt));
         ASSERT_FALSE(evt.hasMessages);
         ASSERT_EQ(evt.accountHandle, alice.handle);
         ASSERT_EQ(evt.items.size(), 3);
         ASSERT_EQ(evt.items[0].type, MessageWaitingItem::Voice);
         ASSERT_EQ(evt.items[0].newMessageCount, 0);
         ASSERT_EQ(evt.items[0].oldMessageCount, 0);
         ASSERT_EQ(evt.items[0].newUrgentMessageCount, 0);
         ASSERT_EQ(evt.items[0].oldUrgentMessageCount, 0);
         ASSERT_EQ(evt.items[1].type, MessageWaitingItem::Fax);
      }
      
      {
         IncomingMWIStatusEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.mwiEvents, "SipMessageWaitingIndicationHandler::onIncomingMWIStatus", 5000, AlwaysTruePred(), mwiHandle, evt));
         ASSERT_TRUE(evt.hasMessages);
         ASSERT_EQ(evt.accountHandle, alice.handle);
         ASSERT_EQ(evt.items.size(), 1);
         ASSERT_EQ(evt.items[0].type, MessageWaitingItem::Voice);
         ASSERT_EQ(evt.items[0].newMessageCount, 1);
         ASSERT_EQ(evt.items[0].oldMessageCount, 0);
         ASSERT_EQ(evt.items[0].newUrgentMessageCount, 0);
         ASSERT_EQ(evt.items[0].oldUrgentMessageCount, 0);
      }

      {
         MWISubscriptionEndedEvent evt;
         ASSERT_TRUE(alice.mwiEvents->expectEvent("SipMessageWaitingIndicationHandler::onSubscriptionEnded", 15000, AlwaysTruePred(), mwiHandle, evt));
         ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }
   });
   
   waitFor2(aliceEvents, serverEvents);
}

TEST_F(MWIModuleTest, AcceptedMWI_OutOfRFC3842)
{
   TestAccount alice("alice");
   TestAccount server("server"); // Mock server to handle incoming subsription

   // Clear the default subscription event handler on the server for the MWI event package, so that the server can respond to the subscription
   if (server.mwi != NULL)
   {
       // Clear license success callback
       ((CPCAPI2::PhoneInterface*)server.phone)->process_test(0);
       server.mwi->setHandler(server.handle, NULL);
   }
   if (server.subs != NULL)
   {
       server.subs->setHandler(server.handle, "message-summary", (SipEventSubscriptionHandler*)0xDEADBEEF);
       server.phone->process(-1);
   }

   auto serverEvents = std::async(std::launch::async, [&]()
   {
      SipEventSubscriptionHandle eventHandle;
      {
         NewSubscriptionEvent evt;
         ASSERT_TRUE(cpcExpectEvent(server.subsEvents, "SipEventSubscriptionHandler::onNewSubscription", 5000, AlwaysTruePred(), eventHandle, evt));
         ASSERT_EQ(evt.account, alice.handle);
         ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
         ASSERT_EQ(evt.remoteDisplayName, alice.config.name);
         ASSERT_EQ(evt.eventPackage, "message-summary");
         ASSERT_EQ(evt.eventPackageParams.size(), 0);
         ASSERT_EQ(evt.supportedMimeTypes.size(), 1);
         ASSERT_EQ(evt.supportedMimeTypes[0].mimeType, "application");
         ASSERT_EQ(evt.supportedMimeTypes[0].mimeSubType, "simple-message-summary");

         SipEventState state;
         state.mimeType = "application";
         state.mimeSubType = "simple-message-summary";
         state.eventPackage = "message-summary";
         std::stringstream str;
         str << "Messages-Waiting: yes\r\n";
         str << "Message-Account: " << alice.config.uri() << "\r\n";
         str << "Voice-Message: 2/1 (2/1)\r\n";
         str << "Fax-Message: 2/1 (2/1)\r\n";
         str << "None: 2/1 (2/1)\r\n";
         state.contentUTF8 = str.str().c_str();
         state.contentLength = state.contentUTF8.size();
         state.expiresTimeMs = 86400;
         server.subs->accept(eventHandle, state);

         SipEventState state2 = state;
         std::stringstream str2;
         str2 << "Messages-Waiting: yes\r\n";
         str2 << "Voice-Message: 4/3\r\n";
         str2 << "Voicemail: 4/3\r\n"; // OBELISK-5608 undefined message context class: voicemail
         str2 << "Fax-Message: 4/3 (4/3)\r\n";
         str2 << "None: 4/3 (4/3)"; // BRICE-6892 no CRLF at the end

         state2.contentUTF8 = str2.str().c_str();
         state2.contentLength = state2.contentUTF8.size();
         state2.expiresTimeMs = 86400;
         server.subs->notify(eventHandle, state2);

         server.subs->end(eventHandle);
      }
   });

   auto aliceEvents = std::async(std::launch::async, [&]()
   {
      SipEventSubscriptionHandle subscription = alice.subs->createSubscription(alice.handle);
      SipEventSubscriptionSettings subscriptionSettings;
      subscriptionSettings.eventPackage = "message-summary";
      subscriptionSettings.expiresSeconds = 86400;
      subscriptionSettings.supportedMimeTypes.push_back(CPCAPI2::MimeType("application", "simple-message-summary"));
      alice.subs->applySubscriptionSettings(subscription, subscriptionSettings);
      alice.subs->addParticipant(subscription, server.config.uri());
      alice.subs->start(subscription);

      SipMWISubscriptionHandle mwiHandle;
      {
         NewMWISubscriptionEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.mwiEvents, "SipMessageWaitingIndicationHandler::onNewSubscription", 5000, AlwaysTruePred(), mwiHandle, evt));
         ASSERT_EQ(evt.account, alice.handle);
      }

      {
         MWISubscriptionStateChangedEvent evt;
         ASSERT_TRUE(alice.mwiEvents->expectEvent("SipMessageWaitingIndicationHandler::onSubscriptionStateChanged", 15000, AlwaysTruePred(), mwiHandle, evt));
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      {
         IncomingMWIStatusEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.mwiEvents, "SipMessageWaitingIndicationHandler::onIncomingMWIStatus", 5000, AlwaysTruePred(), mwiHandle, evt));
         ASSERT_TRUE(evt.hasMessages);
         ASSERT_EQ(evt.accountHandle, alice.handle);
         ASSERT_EQ(evt.items.size(), 3);
         ASSERT_EQ(evt.items[0].type, MessageWaitingItem::Voice);
         ASSERT_EQ(evt.items[0].newMessageCount, 2);
         ASSERT_EQ(evt.items[0].oldMessageCount, 1);
         ASSERT_EQ(evt.items[0].newUrgentMessageCount, 2);
         ASSERT_EQ(evt.items[0].oldUrgentMessageCount, 1);
         ASSERT_EQ(evt.items[1].type, MessageWaitingItem::Fax);
      }

      {
         IncomingMWIStatusEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.mwiEvents, "SipMessageWaitingIndicationHandler::onIncomingMWIStatus", 5000, AlwaysTruePred(), mwiHandle, evt));
         ASSERT_TRUE(evt.hasMessages);
         ASSERT_EQ(evt.accountHandle, alice.handle);
         ASSERT_EQ(evt.items.size(), 4);
         ASSERT_EQ(evt.items[0].type, MessageWaitingItem::Voice);
         ASSERT_EQ(evt.items[0].newMessageCount, 4);
         ASSERT_EQ(evt.items[0].oldMessageCount, 3);
         ASSERT_EQ(evt.items[0].newUrgentMessageCount, 0);
         ASSERT_EQ(evt.items[0].oldUrgentMessageCount, 0);
      }

      {
         MWISubscriptionEndedEvent evt;
         ASSERT_TRUE(alice.mwiEvents->expectEvent("SipMessageWaitingIndicationHandler::onSubscriptionEnded", 15000, AlwaysTruePred(), mwiHandle, evt));
         ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }
   });

   waitFor2(aliceEvents, serverEvents);
}

TEST_F(MWIModuleTest, MwiTimedAccountDisable)
{
   TestAccount alice("alice");
   TestAccount server("server"); // Mock server to handle incoming subsription
   
   // Clear the default subscription event handler on the server for the MWI event package, so that the server can respond to the subscription
   if (server.mwi != NULL)
   {
       // Clear license success callback
       ((CPCAPI2::PhoneInterface*)server.phone)->process_test(0);
       server.mwi->setHandler(server.handle, NULL);
   }
   if (server.subs != NULL)
   {
       server.subs->setHandler(server.handle, "message-summary", (SipEventSubscriptionHandler*)0xDEADBEEF);
       server.phone->process(-1);
   }
   
   bool suppressNotify = true; // simulate e.g. CP PBX not sending SIP NOTIFY in response to MWI SUBSCRIBE
   
   auto serverEvents = std::async(std::launch::async, [&] ()
   {
      SipEventSubscriptionHandle eventHandle;
      {
         NewSubscriptionEvent evt;
         ASSERT_TRUE(cpcExpectEvent(server.subsEvents, "SipEventSubscriptionHandler::onNewSubscription", 5000, AlwaysTruePred(), eventHandle, evt));
         ASSERT_EQ(evt.account, alice.handle);
         ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
         ASSERT_EQ(evt.remoteDisplayName, alice.config.name);
         ASSERT_EQ(evt.eventPackage, "message-summary");
         ASSERT_EQ(evt.eventPackageParams.size(), 0);
         ASSERT_EQ(evt.supportedMimeTypes.size(), 1);
         ASSERT_EQ(evt.supportedMimeTypes[0].mimeType, "application");
         ASSERT_EQ(evt.supportedMimeTypes[0].mimeSubType, "simple-message-summary");

         SipEventState state;
         state.mimeType = "application";
         state.mimeSubType = "simple-message-summary";
         state.eventPackage = "message-summary";
         std::stringstream str;
         str << "Messages-Waiting: no\r\n";
         str << "Message-Account: " << alice.config.uri() << "\r\n";
         str << "Voice-Message: 0/0 (0/0)\r\n";
         str << "Fax-Message: 0/0 (0/0)\r\n";
         str << "None: 0/0 (0/0)\r\n";
         state.contentUTF8 = str.str().c_str();
         state.contentLength = state.contentUTF8.size();
         state.expiresTimeMs = 86400;
         
         dynamic_cast<SipEventManagerInternal*>(server.subs)->accept(eventHandle, state, suppressNotify);
      }
   });
   
   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      SipEventSubscriptionHandle subscription = alice.subs->createSubscription(alice.handle);
      SipEventSubscriptionSettings subscriptionSettings;
      subscriptionSettings.eventPackage = "message-summary";
      subscriptionSettings.expiresSeconds = 86400;
      subscriptionSettings.supportedMimeTypes.push_back(CPCAPI2::MimeType("application", "simple-message-summary"));
      alice.subs->applySubscriptionSettings(subscription, subscriptionSettings);
      alice.subs->addParticipant(subscription, server.config.uri());
      alice.subs->start(subscription);

      if (false == suppressNotify)
      {
         SipMWISubscriptionHandle mwiHandle;
         {
            NewMWISubscriptionEvent evt;
            ASSERT_TRUE(cpcExpectEvent(alice.mwiEvents, "SipMessageWaitingIndicationHandler::onNewSubscription", 5000, AlwaysTruePred(), mwiHandle, evt));
            ASSERT_EQ(evt.account, alice.handle);
         }

         {
            MWISubscriptionStateChangedEvent evt;
            ASSERT_TRUE(alice.mwiEvents->expectEvent("SipMessageWaitingIndicationHandler::onSubscriptionStateChanged", 15000, AlwaysTruePred(), mwiHandle, evt));
            ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
         }
      }
      
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      auto start = std::chrono::system_clock::now();
      alice.disable();
      auto end = std::chrono::system_clock::now();
      
      safeCout("Disable account took " << std::chrono::duration<double>(end-start).count() << " seconds");

      std::chrono::duration<double> diff = end-start;
      ASSERT_GE(3, diff.count()) << "SDK should not take this long to shutdown against repro running locally";

   });
   
   waitFor2(aliceEvents, serverEvents);
}

TEST_F(MWIModuleTest, DISABLED_AcceptedMWI2)
{
   TestAccount alice("alice");
   TestAccount server("server"); // Mock server to handle incoming subsription
   
   // Clear the default subscription event handler on the server for the MWI event package, so that the server can respond to the subscription
   if (server.mwi != NULL)
   {
       // Clear license success callback
       ((CPCAPI2::PhoneInterface*)server.phone)->process_test(0);
       server.mwi->setHandler(server.handle, NULL);
   }
   if (server.subs != NULL)
   {
       server.subs->setHandler(server.handle, "message-summary", (SipEventSubscriptionHandler*)0xDEADBEEF);
       server.phone->process(-1);
   }
   
   auto serverEvents = std::async(std::launch::async, [&] ()
   {
      SipEventSubscriptionHandle eventHandle;
      {
         NewSubscriptionEvent evt;
         ASSERT_TRUE(cpcExpectEvent(server.subsEvents, "SipEventSubscriptionHandler::onNewSubscription", 5000, AlwaysTruePred(), eventHandle, evt));
         ASSERT_EQ(evt.account, alice.handle);
         ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
         ASSERT_EQ(evt.remoteDisplayName, alice.config.name);
         ASSERT_EQ(evt.eventPackage, "message-summary");
         ASSERT_EQ(evt.eventPackageParams.size(), 0);
         ASSERT_EQ(evt.supportedMimeTypes.size(), 1);
         ASSERT_EQ(evt.supportedMimeTypes[0].mimeType, "application");
         ASSERT_EQ(evt.supportedMimeTypes[0].mimeSubType, "simple-message-summary");

         SipEventState state;
         state.mimeType = "application";
         state.mimeSubType = "simple-message-summary";
         state.eventPackage = "message-summary";
         std::stringstream str;
         str << "Messages-Waiting: no\r\n";
         str << "Message-Account: " << alice.config.uri() << "\r\n";
         str << "Voice-Message: 0/0 (0/0)\r\n";
         str << "Fax-Message: 0/0 (0/0)\r\n";
         str << "None: 0/0 (0/0)\r\n";
         state.contentUTF8 = str.str().c_str();
         state.contentLength = state.contentUTF8.size();
         state.expiresTimeMs = 86400;
         server.subs->accept(eventHandle, state);
         
         // Parsed successfully
         SipEventState state2 = state;
         std::stringstream str2;
         str2 << "Messages-Waiting: yes\r\n";
         str2 << "Voice-Message: 1/0\r\n\r\n";
         str2 << "Voicemail: 1/0\r\n";
         
         state2.contentUTF8 = str2.str().c_str();
         state2.contentLength = state2.contentUTF8.size();
         state2.expiresTimeMs = 86400;
         server.subs->notify(eventHandle, state2);
         
         server.subs->end(eventHandle);
      }
   });
   
   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      SipEventSubscriptionHandle subscription = alice.subs->createSubscription(alice.handle);
      SipEventSubscriptionSettings subscriptionSettings;
      subscriptionSettings.eventPackage = "message-summary";
      subscriptionSettings.expiresSeconds = 86400;
      subscriptionSettings.supportedMimeTypes.push_back(CPCAPI2::MimeType("application", "simple-message-summary"));
      alice.subs->applySubscriptionSettings(subscription, subscriptionSettings);
      alice.subs->addParticipant(subscription, server.config.uri());
      alice.subs->start(subscription);
      
      SipEventSubscriptionHandle eventHandle;
      {
         SubscriptionStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.subsEvents, "SipEventSubscriptionHandler::onSubscriptionStateChanged", 5000, AlwaysTruePred(), eventHandle, evt));
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      {
         IncomingEventStateEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.subsEvents, "SipEventSubscriptionHandler::onIncomingEventState", 5000, AlwaysTruePred(), eventHandle, evt));
         ASSERT_EQ(evt.eventState.mimeType, "application");
         ASSERT_EQ(evt.eventState.mimeSubType, "simple-message-summary");
         ASSERT_EQ(evt.eventState.eventPackage, "message-summary");
         ASSERT_EQ(evt.eventPackageParams.size(), 0);
      }

      {
         IncomingEventStateEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.subsEvents, "SipEventSubscriptionHandler::onIncomingEventState", 5000, AlwaysTruePred(), eventHandle, evt));
         ASSERT_EQ(evt.eventState.mimeType, "application");
         ASSERT_EQ(evt.eventState.mimeSubType, "simple-message-summary");
         ASSERT_EQ(evt.eventState.eventPackage, "message-summary");
         ASSERT_EQ(evt.eventPackageParams.size(), 0);
      }

      SipMWISubscriptionHandle mwiHandle;
      {
         MWISubscriptionEndedEvent evt;
         ASSERT_TRUE(alice.mwiEvents->expectEvent("SipMessageWaitingIndicationHandler::onSubscriptionEnded", 15000, AlwaysTruePred(), mwiHandle, evt));
         ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }

      /*
      // Can verify the actual SIP response only when the sip event manager supports multiple handlers for the same package.
      {
         SubscriptionEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.subsEvents, "SipEventSubscriptionHandler::onSubscriptionEnded", 5000, AlwaysTruePred(), eventHandle, evt));
         ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
         ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Outgoing);
         ASSERT_EQ(evt.statusCode, 0);
         ASSERT_EQ(evt.remoteAddress, server.config.uri());
      }
      */
   });
   
   waitFor2(aliceEvents, serverEvents);
}

TEST_F(MWIModuleTest, DISABLED_MWIMalformedBody)
{
   TestAccount alice("alice");
   TestAccount server("server"); // Mock server to handle incoming subsription
   
   // Clear the default subscription event handler on the server for the MWI event package, so that the server can respond to the subscription
   if (server.mwi != NULL)
   {
       // Clear license success callback
       ((CPCAPI2::PhoneInterface*)server.phone)->process_test(0);
       server.mwi->setHandler(server.handle, NULL);
   }
   if (server.subs != NULL)
   {
       server.subs->setHandler(server.handle, "message-summary", (SipEventSubscriptionHandler*)0xDEADBEEF);
       server.phone->process(-1);
   }
   
   auto serverEvents = std::async(std::launch::async, [&] ()
   {
      SipEventSubscriptionHandle eventHandle;
      {
         NewSubscriptionEvent evt;
         ASSERT_TRUE(cpcExpectEvent(server.subsEvents, "SipEventSubscriptionHandler::onNewSubscription", 5000, AlwaysTruePred(), eventHandle, evt));
         ASSERT_EQ(evt.account, alice.handle);
         ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
         ASSERT_EQ(evt.remoteDisplayName, alice.config.name);
         ASSERT_EQ(evt.eventPackage, "message-summary");
         ASSERT_EQ(evt.eventPackageParams.size(), 0);
         ASSERT_EQ(evt.supportedMimeTypes.size(), 1);
         ASSERT_EQ(evt.supportedMimeTypes[0].mimeType, "application");
         ASSERT_EQ(evt.supportedMimeTypes[0].mimeSubType, "simple-message-summary");

         SipEventState state;
         state.mimeType = "application";
         state.mimeSubType = "simple-message-summary";
         state.eventPackage = "message-summary";
         std::stringstream str;
         str << "Messages-Waiting: no\r\n";
         str << "Message-Account: " << alice.config.uri() << "\r\n";
         str << "Voice-Message: 0/0 (0/0)QQQQQQQbreakparser\r\n";
         str << "Fax-Message: 0/0 (0/0)\r\n";
         str << "None: 0/0 (0/0)\r\n";
         state.contentUTF8 = str.str().c_str();
         state.contentLength = state.contentUTF8.size();
         state.expiresTimeMs = 86400;
         server.subs->accept(eventHandle, state);
                  
         server.subs->end(eventHandle);
      }
   });
   
   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      SipEventSubscriptionHandle subscription = alice.subs->createSubscription(alice.handle);
      SipEventSubscriptionSettings subscriptionSettings;
      subscriptionSettings.eventPackage = "message-summary";
      subscriptionSettings.expiresSeconds = 86400;
      subscriptionSettings.supportedMimeTypes.push_back(CPCAPI2::MimeType("application", "simple-message-summary"));
      alice.subs->applySubscriptionSettings(subscription, subscriptionSettings);
      alice.subs->addParticipant(subscription, server.config.uri());
      alice.subs->start(subscription);

      SipMWISubscriptionHandle mwiHandle;
      {
         NewMWISubscriptionEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.mwiEvents, "SipMessageWaitingIndicationHandler::onNewSubscription", 5000, AlwaysTruePred(), mwiHandle, evt));
         ASSERT_EQ(evt.account, alice.handle);
      }

      {
         MWISubscriptionStateChangedEvent evt;
         ASSERT_TRUE(alice.mwiEvents->expectEvent("SipMessageWaitingIndicationHandler::onSubscriptionStateChanged", 15000, AlwaysTruePred(), mwiHandle, evt));
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      {
         MWISubscriptionEndedEvent evt;
         ASSERT_TRUE(alice.mwiEvents->expectEvent("SipMessageWaitingIndicationHandler::onSubscriptionEnded", 15000, AlwaysTruePred(), mwiHandle, evt));
         ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }
   });
   
   waitFor2(aliceEvents, serverEvents);
}

}  // namespace

