#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#if (CPCAPI2_BRAND_STRETTO_UEM_MODULE == 1)

#include "cpcapi2_test_fixture.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>

#include "test_framework/cpcapi2_test_framework.h"
#include "test_framework/bare_bones_sip_endpoint.h"
#include "test_framework/xmpp_test_helper.h"
#include "test_call_events.h"

#include <cpcstl/string.h>
#include <string>
#include <thread>
#include <future>
#include <memory>

#include <libxml/xpath.h>
#include <libxml/xpathInternals.h>
#include "../../impl/util/LibxmlSharedUsage.h"
#include "strettouem/StrettoUemInt.h"
#include "analytics1/AnalyticsManagerInt.h"

//#define SEND_TO_STRETTO_SERVER 1 // comment out to cause UEM module to NOT connect/send to Stretto server
static const cpc::string kStrettoServerUrl = "https://ccsdev.mobilevoiplive.com:18082/uxmetrics/submit/sdkdemo"; // also used in strettouemProvisioned.json


using namespace CPCAPI2;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::StrettoUem;
using namespace CPCAPI2::Analytics;
using namespace CPCAPI2::test;
using namespace CPCAPI2::XmppAccount;
using namespace CPCAPI2::SipConversation;

#if defined(__linux__) && !defined(ANDROID)
// don't run these on linux -- msrp_tree_get crashes intermittently
#else

namespace {

   class StrettoUemTest : public CpcapiAutoTest
   {
   public:
      StrettoUemTest() {}
      virtual ~StrettoUemTest() {}
      StrettoUemHandle openModule(StrettoUemManager* mgr, const StrettoUem::GeneralStats& generalStats=StrettoUem::GeneralStats());
      _xmlNode* getXmlNode(std::string doc, std::string path);
      std::string getVqmReportValue(std::string doc, std::string path);
      bool getXmlNodeExists(std::string doc, std::string path);
   };

   TEST_F(StrettoUemTest, LoginTestStrettoResponse)
   {
      TestAccount alice("alice", Account_NoInit);
      alice.config.disableAnalyticsModuleAutoTestEventHandling = true;
      alice.config.disableStrettoUemModuleAutoTestEventHandling = false;
      alice.init();
   
      StrettoUemManager* strettoUem = alice.strettoUemManager;
      StrettoUem::GeneralStats generalStats;
      StrettoUemHandle ah = openModule(strettoUem, generalStats);

      alice.enable();


      StrettoUemManagerInt* strettoUemInt = static_cast<StrettoUemManagerInt*>(strettoUem);

      AnalyticsManagerInt* analyticsManager = static_cast<AnalyticsManagerInt*>(strettoUemInt->analyticsManager());
      analyticsManager->instantMessageInfoFired(alice.handle, true, true);
      analyticsManager->instantMessageInfoFired(alice.handle, false, true);

      strettoUem->sendReport(ah);

      // Wait for response from report
      {
         auto sendReponse = std::async(std::launch::async, [&]()
         {
            AnalyticsHandle h;
            StrettoUem::OnReportResponseEvent evt;
            ASSERT_TRUE(cpcWaitForEvent(
               alice.strettoUemEvents,
               "StrettoUemHandler::onReportResponse",
               15000,
               AlwaysTruePred(),
               h, evt));

               ASSERT_EQ(evt.responseCode, 200);
         });
         waitFor(sendReponse);
      }

      strettoUem->close(ah);
   }
   
   TEST_F(StrettoUemTest, GeneralDataTest)
   {

      TestAccount alice("alice", Account_NoInit);
      alice.config.disableAnalyticsModuleAutoTestEventHandling = false;
      alice.config.disableStrettoUemModuleAutoTestEventHandling = true;
      alice.init();

      StrettoUemManager* strettoUem = alice.strettoUemManager;
      StrettoUem::GeneralStats generalStats;
      const std::string clientVersion("CPCAPI2 Test No Request Client");
      generalStats.clientVersion = clientVersion.c_str();
      const time_t testStartTime = time(0);
      std::stringstream startTestTimeStr;
      startTestTimeStr << testStartTime;
      generalStats.installationDate = testStartTime;
      generalStats.utcTimezoneOffsetMinutes = -1 * ((8 * 60) + 45);
      
      
#ifdef NO_SEND_TO_STRETTO_SERVER
      generalStats.serverUrl = kStrettoUemServerUrl;
#endif
      StrettoUemHandle ah = openModule(strettoUem, generalStats);

      alice.enable();

      StrettoUemManagerInt* strettoUemInt = static_cast<StrettoUemManagerInt*>(strettoUem);

      AnalyticsManagerInt* analyticsManager = static_cast<AnalyticsManagerInt*>(strettoUemInt->analyticsManager());
      analyticsManager->instantMessageInfoFired(alice.handle, true, true);
      analyticsManager->instantMessageInfoFired(alice.handle, false, true);

      analyticsManager->sendReport(ah);

      {
         auto sendReponse = std::async(std::launch::async, [&]() {
             AnalyticsHandle h;
             OnReportCreatedSuccessEvent evt;
             ASSERT_TRUE(cpcWaitForEvent(
             alice.analyticsEvents,
             "AnalyticsHandlerInt::onReportCreatedSuccess",
             15000,
             AlwaysTruePred(),
             h, evt));

             //make sure document is not empty
             ASSERT_NE(evt.content, cpc::string());
             //make sure settings_data tag exists
             bool exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/general[1]");
             ASSERT_TRUE(exists);


             _xmlNode* node = getXmlNode(evt.content.c_str(), "/cpc_usage_report/general[1]");
             //make sure relevant data tags exist and their content is not empty
             EXPECT_STREQ((const char *)node->properties->children->content, "deviceUUID"); //tag name
             //EXPECT_STREQ((const char *)node->properties->next->children->content, "518E27C9-A499-4A73-87EF-1595682F7772"); //tag value
             node = node->next; // get next data tag
             EXPECT_STREQ((const char *)node->properties->children->content, "clientVersion"); //tag name
             EXPECT_STREQ((const char *)node->properties->next->children->content, clientVersion.c_str()); //tag value
             node = node->next; // get next data tag
             EXPECT_STREQ((const char *)node->properties->children->content, "installationDate"); //tag name
             EXPECT_STREQ((const char *)node->properties->next->children->content, startTestTimeStr.str().c_str()); //tag value
             node = node->next; // get next data tag
             EXPECT_STREQ((const char *)node->properties->children->content, "osType"); //tag name
             //EXPECT_STREQ((const char *)node->properties->next->children->content, "Win32"); //tag value
             node = node->next; // get next data tag
             EXPECT_STREQ((const char *)node->properties->children->content, "osVersion"); //tag name
             //EXPECT_STREQ((const char *)node->properties->next->children->content, "7"); //tag value
             node = node->next; // get next data tag
             //EXPECT_STREQ((const char *)node->properties->children->content, "hardwareModel"); //tag name
             //EXPECT_STREQ((const char *)node->properties->next->children->content, ""); //tag value
             //node = node->next; // get next data tag
             //EXPECT_STREQ((const char *)node->properties->children->content, "clientPublicIpAddress"); //tag name
             //EXPECT_STREQ((const char *)node->properties->next->children->content, ""); //tag value
             //node = node->next; // get next data tag
             EXPECT_STREQ((const char *)node->properties->children->content, "clientLaunchTime"); //tag name
             //EXPECT_STREQ((const char *)node->properties->next->children->content, "1448476782"); //tag value
             node = node->next; // get next data tag
             EXPECT_STREQ((const char *)node->properties->children->content, "xmlTemplateVersion"); //tag name
             EXPECT_STREQ((const char *)node->properties->next->children->content, "1.8"); //tag value
             node = node->next; // get next data tag
             //EXPECT_STREQ((const char *)node->properties->children->content, "language"); //tag name
             //EXPECT_STREQ((const char *)node->properties->next->children->content, ""); //tag value
             //node = node->next; // get next data tag
             EXPECT_STREQ((const char *)node->properties->children->content, "timezone"); //tag name
             EXPECT_STREQ((const char *)node->properties->next->children->content, "UTC-8:45"); //tag value
            
            
            
         });
         waitFor(sendReponse);
      }

      analyticsManager->close(ah);
   }
   
   TEST_F(StrettoUemTest, TimezoneTest)
   {
      TestAccount alice("alice", Account_NoInit);
      alice.config.disableAnalyticsModuleAutoTestEventHandling = false;
      alice.config.disableStrettoUemModuleAutoTestEventHandling = true;
      alice.init();

      StrettoUemManager* strettoUem = alice.strettoUemManager;
      StrettoUem::GeneralStats generalStats;
      const std::string clientVersion("CPCAPI2 Test No Request Client");
      generalStats.clientVersion = clientVersion.c_str();
      const time_t testStartTime = time(0);
      std::stringstream startTestTimeStr;
      startTestTimeStr << testStartTime;
      generalStats.installationDate = testStartTime;
      generalStats.utcTimezoneOffsetMinutes = ((2 * 60) + 15);
      
#ifdef NO_SEND_TO_STRETTO_SERVER
      generalStats.clientVersion = kMagicNoSendClientVersion;
#endif
      StrettoUemHandle ah = openModule(strettoUem, generalStats);
      alice.enable();
      
      StrettoUemManagerInt* strettoUemInt = static_cast<StrettoUemManagerInt*>(strettoUem);
      AnalyticsManagerInt* analyticsManager = static_cast<AnalyticsManagerInt*>(strettoUemInt->analyticsManager());


      analyticsManager->sendReport(ah);

      {
         auto sendReponse = std::async(std::launch::async, [&]() {
             AnalyticsHandle h;
             OnReportCreatedSuccessEvent evt;
             ASSERT_TRUE(cpcWaitForEvent(
             alice.analyticsEvents,
             "AnalyticsHandlerInt::onReportCreatedSuccess",
             15000,
             AlwaysTruePred(),
             h, evt));

             //make sure document is not empty
             ASSERT_NE(evt.content, cpc::string());
             //make sure settings_data tag exists
             bool exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/general[1]");
             ASSERT_TRUE(exists);

             _xmlNode* node = getXmlNode(evt.content.c_str(), "/cpc_usage_report/general[1]");
            
             for (;;)
             {
               if (node == NULL)
               {
                  ASSERT_TRUE(0);
                  break;
               }
             
               if (strcmp((const char *)node->properties->children->content, "timezone") == 0)
               {
                  EXPECT_STREQ((const char *)node->properties->next->children->content, "UTC+2:15"); //tag value
                  break;
               }
               
               node = node->next;
             }

         });
         waitFor(sendReponse);
      }

      analyticsManager->close(ah);
   }

   TEST_F(StrettoUemTest, TimezoneTest2)
   {
      TestAccount alice("alice", Account_NoInit);
      alice.config.disableAnalyticsModuleAutoTestEventHandling = false;
      alice.config.disableStrettoUemModuleAutoTestEventHandling = true;
      alice.init();

      StrettoUemManager* strettoUem = alice.strettoUemManager;
      StrettoUem::GeneralStats generalStats;
      const std::string clientVersion("CPCAPI2 Test No Request Client");
      generalStats.clientVersion = clientVersion.c_str();
      const time_t testStartTime = time(0);
      std::stringstream startTestTimeStr;
      startTestTimeStr << testStartTime;
      generalStats.installationDate = testStartTime;
      generalStats.utcTimezoneOffsetMinutes = ((12 * 60) + 55);
      
      
#ifdef NO_SEND_TO_STRETTO_SERVER
      generalStats.clientVersion = kMagicNoSendClientVersion;
#endif
      StrettoUemHandle ah = openModule(strettoUem, generalStats);
      alice.enable();
      
      StrettoUemManagerInt* strettoUemInt = static_cast<StrettoUemManagerInt*>(strettoUem);
      AnalyticsManagerInt* analyticsManager = static_cast<AnalyticsManagerInt*>(strettoUemInt->analyticsManager());


      analyticsManager->sendReport(ah);

      {
         auto sendReponse = std::async(std::launch::async, [&]() {
             AnalyticsHandle h;
             OnReportCreatedSuccessEvent evt;
             ASSERT_TRUE(cpcWaitForEvent(
             alice.analyticsEvents,
             "AnalyticsHandlerInt::onReportCreatedSuccess",
             15000,
             AlwaysTruePred(),
             h, evt));

             //make sure document is not empty
             ASSERT_NE(evt.content, cpc::string());
             //make sure settings_data tag exists
             bool exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/general[1]");
             ASSERT_TRUE(exists);

             _xmlNode* node = getXmlNode(evt.content.c_str(), "/cpc_usage_report/general[1]");
            
             for (;;)
             {
               if (node == NULL)
               {
                  ASSERT_TRUE(0);
                  break;
               }
             
               if (strcmp((const char *)node->properties->children->content, "timezone") == 0)
               {
                  EXPECT_STREQ((const char *)node->properties->next->children->content, "UTC+12:55"); //tag value
                  break;
               }
               
               node = node->next;
             }

         });
         waitFor(sendReponse);
      }

      analyticsManager->close(ah);
   }
   
   TEST_F(StrettoUemTest, TimezoneTest3)
   {
      TestAccount alice("alice", Account_NoInit);
      alice.config.disableAnalyticsModuleAutoTestEventHandling = false;
      alice.config.disableStrettoUemModuleAutoTestEventHandling = true;
      alice.init();

      StrettoUemManager* strettoUem = alice.strettoUemManager;
      StrettoUem::GeneralStats generalStats;
      const std::string clientVersion("CPCAPI2 Test No Request Client");
      generalStats.clientVersion = clientVersion.c_str();
      const time_t testStartTime = time(0);
      std::stringstream startTestTimeStr;
      startTestTimeStr << testStartTime;
      generalStats.installationDate = testStartTime;
      // default to 0
      // generalStats.utcTimezoneOffsetMinutes = 0;
      
      
#ifdef NO_SEND_TO_STRETTO_SERVER
      generalStats.clientVersion = kMagicNoSendClientVersion;
#endif
      StrettoUemHandle ah = openModule(strettoUem, generalStats);
      alice.enable();
      
      StrettoUemManagerInt* strettoUemInt = static_cast<StrettoUemManagerInt*>(strettoUem);
      AnalyticsManagerInt* analyticsManager = static_cast<AnalyticsManagerInt*>(strettoUemInt->analyticsManager());


      analyticsManager->sendReport(ah);

      {
         auto sendReponse = std::async(std::launch::async, [&]() {
             AnalyticsHandle h;
             OnReportCreatedSuccessEvent evt;
             ASSERT_TRUE(cpcWaitForEvent(
             alice.analyticsEvents,
             "AnalyticsHandlerInt::onReportCreatedSuccess",
             15000,
             AlwaysTruePred(),
             h, evt));

             //make sure document is not empty
             ASSERT_NE(evt.content, cpc::string());
             //make sure settings_data tag exists
             bool exists = getXmlNodeExists(evt.content.c_str(), "/cpc_usage_report/general[1]");
             ASSERT_TRUE(exists);

             _xmlNode* node = getXmlNode(evt.content.c_str(), "/cpc_usage_report/general[1]");
            
             for (;;)
             {
               if (node == NULL)
               {
                  ASSERT_TRUE(0);
                  break;
               }
             
               if (strcmp((const char *)node->properties->children->content, "timezone") == 0)
               {
                  EXPECT_STREQ((const char *)node->properties->next->children->content, "UTC-0:00"); //tag value
                  break;
               }
               
               node = node->next;
             }

         });
         waitFor(sendReponse);
      }

      analyticsManager->close(ah);
   }
   
   TEST_F(StrettoUemTest, DecodeProvisioning)
   {
      TestAccount alice("alice");

      std::ifstream in((TestEnvironmentConfig::testResourcePath() + "strettouemProvisioned.json").c_str());
      ASSERT_TRUE(in.is_open());

      std::ostringstream iss;
      iss << in.rdbuf() << std::flush;

      cpc::string doc = iss.str().c_str();
      
      StrettoUemManager* strettoUem = alice.strettoUemManager;
      StrettoUemSettings strettoUemSettings;

      ASSERT_EQ(strettoUem->decodeProvisioningResponse(doc, strettoUemSettings), kSuccess);

      ASSERT_EQ(kStrettoServerUrl, strettoUemSettings.serverUrl);
      ASSERT_EQ("httpTestUsername", strettoUemSettings.httpUserName);
      ASSERT_EQ("httpTestPassword", strettoUemSettings.httpPassword);
      ASSERT_EQ("strettoTestUsername", strettoUemSettings.strettoUserName);
   }
   
   StrettoUemHandle StrettoUemTest::openModule(StrettoUemManager* mgr, const StrettoUem::GeneralStats& generalStats)
   {
      StrettoUemSettings serverSettings;

      serverSettings.strettoUserName = "t1@sdkdemo";
      serverSettings.httpUserName = "sdkuemtestun";
      serverSettings.httpPassword = "sdkuemtestpw";

#ifdef SEND_TO_STRETTO_SERVER
      serverSettings.serverUrl = kStrettoServerUrl;
#endif

      return mgr->open(serverSettings, generalStats);
   }

   //grabs the value of a specific tag inside the PATH parameter passed in. If the tag
   //does not exist then you get empty string back
   std::string StrettoUemTest::getVqmReportValue(std::string doc, std::string path) {

      const xmlChar *plainTextPath = (const xmlChar*)path.c_str();
      std::string stdPlain = "";

      // Use libxml + XPath to parse the document (hopefully the shortest way)
      LibxmlSharedUsage::addRef();

      xmlDocPtr xmlDoc = xmlParseMemory(doc.c_str(), doc.size());
      xmlXPathContextPtr xpathCtx = NULL;
      xmlXPathObjectPtr xpathObjPlain = NULL;
      xmlChar *dataName = NULL;
      xmlChar *dataValue = NULL;

      // Create xpath evaluation context
      xpathCtx = xmlXPathNewContext(xmlDoc);
      if (xpathCtx == NULL)
         goto done;

      // Evaluate xpath expression to get plaintext
      xpathObjPlain = xmlXPathEvalExpression(plainTextPath, xpathCtx);
      if (xpathObjPlain == NULL || xmlXPathNodeSetIsEmpty(xpathObjPlain->nodesetval))
         goto done;

      // get text value of a specific tag PATH
      if (xpathObjPlain->nodesetval->nodeTab[0]->children != NULL && xpathObjPlain->nodesetval->nodeTab[0]->children->children != NULL && xpathObjPlain->nodesetval->nodeTab[0]->children->children->content != NULL) {
         dataValue = xpathObjPlain->nodesetval->nodeTab[0]->children->children->content;

         stdPlain = (const char *)dataValue; // copy the plaintext
         xmlFree(dataValue);
      }

   done:
      if (xpathObjPlain != NULL)
      {
         xmlXPathFreeObject(xpathObjPlain);
         xpathObjPlain = NULL;
      }
      if (xpathCtx != NULL)
      {
         xmlXPathFreeContext(xpathCtx);
         xpathCtx = NULL;
      }
      if (xmlDoc != NULL)
      {
         xmlFreeDoc(xmlDoc);
         xmlDoc = NULL;
      }

      LibxmlSharedUsage::release();
      return stdPlain;
   }

   // _xmlNode returned is the pointer to the first DATA tag inside of the PATH provided.
   // from that pointer we can interate to each of the DATA tags inside PATH provided.
   // if the tag does not exist we get a NULL;
   _xmlNode* StrettoUemTest::getXmlNode(std::string doc, std::string path) {
      const xmlChar *plainTextPath = (const xmlChar*)path.c_str();
      std::string stdPlain;

      // Use libxml + XPath to parse the document (hopefully the shortest way)
      LibxmlSharedUsage::addRef();

      xmlDocPtr xmlDoc = xmlParseMemory(doc.c_str(), doc.size());
      xmlXPathContextPtr xpathCtx = NULL;
      xmlXPathObjectPtr xpathObjPlain = NULL;
      xmlChar *data = NULL;

      // Create xpath evaluation context
      xpathCtx = xmlXPathNewContext(xmlDoc);

      // Evaluate xpath expression to get plaintext
      xpathObjPlain = xmlXPathEvalExpression(plainTextPath, xpathCtx);
      if (xpathObjPlain == NULL || xmlXPathNodeSetIsEmpty(xpathObjPlain->nodesetval)) {
         return NULL;
      }

      LibxmlSharedUsage::release();

      return xpathObjPlain->nodesetval->nodeTab[0]->children;
   }

   // _xmlNode returned is the pointer to the first DATA tag inside of the PATH provided.
   // from that pointer we can interate to each of the DATA tags inside PATH provided.
   // if the tag does not exist we get a NULL;
   bool StrettoUemTest::getXmlNodeExists(std::string doc, std::string path) {
      const xmlChar *plainTextPath = (const xmlChar*)path.c_str();
      std::string stdPlain;

      // Use libxml + XPath to parse the document (hopefully the shortest way)
      LibxmlSharedUsage::addRef();

      xmlDocPtr xmlDoc = xmlParseMemory(doc.c_str(), doc.size());
      xmlXPathContextPtr xpathCtx = NULL;
      xmlXPathObjectPtr xpathObjPlain = NULL;
      xmlChar *data = NULL;
      bool exists = false;

      // Create xpath evaluation context
      xpathCtx = xmlXPathNewContext(xmlDoc);

      // Evaluate xpath expression to get plaintext
      xpathObjPlain = xmlXPathEvalExpression(plainTextPath, xpathCtx);
      if (xpathObjPlain != NULL && !xmlXPathNodeSetIsEmpty(xpathObjPlain->nodesetval)) {
         exists = true;
      }

      LibxmlSharedUsage::release();
      return exists;
   }
}

#endif // 

#endif // CPCAPI2_BRAND_STRETTO_UEM_MODULE
