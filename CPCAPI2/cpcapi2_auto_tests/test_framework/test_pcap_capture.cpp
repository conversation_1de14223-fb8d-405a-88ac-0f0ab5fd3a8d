
#include "test_pcap_capture.h"
#include "../cpcapi2_test_fixture.h"
#include <boost/algorithm/string.hpp>
#include <cpcapi2.h>
#include <cstdio>

using namespace CPCAPI2;
using namespace CPCAPI2::Pcap;
using namespace testing;


TestPcapCapture::TestPcapCapture(const std::string& testCasesForCapture)
{
   mPhone = CPCAPI2::Phone::create();
   mPhone->initialize(LicenseInfo(), static_cast<PhoneHandler*>(NULL));
   
   if (!testCasesForCapture.empty())
   {
      boost::split(mTestCasesForCapture, testCasesForCapture, boost::is_any_of(","));
   }
   
   mCapturing = false;
}

TestPcapCapture::~TestPcapCapture()
{
}

void TestPcapCapture::OnTestProgramEnd(const UnitTest&)
{
   CPCAPI2::Phone::release(mPhone);
   mPhone = NULL;
}

void TestPcapCapture::OnTestStart(const TestInfo& test_info)
{
   if (mTestCasesForCapture.find(test_info.test_case_name()) == mTestCasesForCapture.end())
   {
      std::stringstream ss;
      ss << test_info.test_case_name() << "." << test_info.name();
      if (mTestCasesForCapture.find(ss.str()) == mTestCasesForCapture.end())
      {
         return;
      }
   }

   if (PcapManager* pcapManager = PcapManager::getInterface(mPhone))
   {
#ifdef _WIN32
      const std::string delim = "\\";
#else
      const std::string delim = "/";
#endif
   
      std::stringstream captureFilename;
      if (!TestEnvironmentConfig::loggingFilePath().empty())
      {
         captureFilename << TestEnvironmentConfig::loggingFilePath().c_str() << delim;
      }
      
      captureFilename << test_info.test_case_name() << "." << test_info.name() << ".pcap";

      if (TestEnvironmentConfig::dockerContainerized())
      {
         // todo: configure per test?
         pcapManager->setCaptureInterface("lo");
      }
      mCaptureFilename = captureFilename.str();
      pcapManager->start(captureFilename.str().c_str());
      mCapturing = true;
   }
}

void TestPcapCapture::OnTestEnd(const TestInfo& test_info)
{
   if (mCapturing)
   {
      if (PcapManager* pcapManager = PcapManager::getInterface(mPhone))
      {
         pcapManager->stop();
         mCapturing = false;
      }

      if (test_info.result()->Passed())
      {
         remove(mCaptureFilename.c_str());
      }
   }
}
