#pragma once
#include <condition_variable>
#include <future>
#include <sstream>
#include <chrono>
#include <deque>
#include "../../CPCAPI2/impl/util/DumFpCommand.h"
#include "../../CPCAPI2/impl/util/AutoTestProcessor.h"

#include <cpcstl/string.h>

#define TEST_DEBUG_EVENTS

namespace CPCAPI2
{
namespace test
{

#if _WIN32
extern void SetThreadName(DWORD dwThreadID, LPCSTR szThreadName);
#endif

extern std::mutex safeCoutMutex;
#define safeCout(e) { using namespace CPCAPI2::test; using namespace CPCAPI2; std::lock_guard<std::mutex> lock(safeCoutMutex); std::cout.flush(); std::cerr.flush(); std::cout << e << std::endl; std::cout.flush(); }
#ifdef TEST_DEBUG_EVENTS
#define eventDebug(e) safeCout(e)
#else
#define eventDebug(e)
#endif

long getTimezone();
void setTimezone(long offset);

struct StrEqualsPred
{
   StrEqualsPred(const char* lhs) : mLhs(lhs) {}
   bool operator()(const cpc::string& rhs)
   {
      return (mLhs == rhs);
   }
   cpc::string mLhs;
};


template<typename THandle>
struct HandleEqualsPred
{
   HandleEqualsPred(THandle lhs) : mLhs(lhs) {}
   bool operator()(THandle rhs)
   {
      return (mLhs == rhs);
   }
   THandle mLhs;
};

struct AlwaysTruePred
{
   bool operator()(unsigned int)
   {
      return true;
   }
   bool operator()(cpc::string val)
   {
      return true;
   }
};

template<typename THandle>
struct AlwaysTruePredTyped
{
    bool operator()(THandle)
    {
        return true;
    }
};

template<typename THandle>
struct HandleNotEqualsPred
{
   HandleNotEqualsPred(THandle lhs) : mLhs(lhs) {}
   bool operator()(THandle rhs)
   {
      return (mLhs != rhs);
   }
   THandle mLhs;
};

class EventHandler
{
public:
   EventHandler(const char* handlerName, CPCAPI2::AutoTestProcessor* sai);
   virtual ~EventHandler();

   void startProcessThread();
   void addEvent(AutoTestReadCallback* fpCmd);
   void shutdown();
   std::string stripFilename(const std::string& sFileName);
   std::string getName() { return mHandlerName; }
   void clearCommands()
   {
      std::unique_lock<std::mutex> l(mEventMutex);
      CommandQueue commandsCpy = mCommands;
      if (commandsCpy.size() > 0)
      {
         std::ostringstream out;
         out << "EventHandler " << this << " " << mHandlerName << " - clearing unprocessed events:" << std::endl;
         CommandQueue::const_iterator itCmds = commandsCpy.begin();
         for (; itCmds != commandsCpy.end(); ++itCmds)
         {
            out << "   " << itCmds->cmd->eventName() << std::endl;
         }
         safeCout(out.str());
      }
      mCommands.clear();
   }
   bool doesEventExist(std::string& eventName)
   {
      std::unique_lock<std::mutex> l(mEventMutex);
      CommandQueue commandsCpy = mCommands;
      bool exists = false;
      if (commandsCpy.size() > 0)
      {
         for (CommandQueue::const_iterator i = commandsCpy.begin(); i != commandsCpy.end(); ++i)
         {
            if (eventName.compare(i->cmd->eventName()) == 0)
            {
               exists = true; break;
            }
         }
      }
      return exists;
   }
   bool doEventsExist(std::vector<std::string>& events)
   {
      std::unique_lock<std::mutex> l(mEventMutex);
      CommandQueue commandsCpy = mCommands;
      bool exists = false;
      
      if (commandsCpy.size() > 0)
      {
         for (CommandQueue::const_iterator i = commandsCpy.begin(); i != commandsCpy.end(); ++i)
         {
            std::string eventName = i->cmd->eventName();
            for (std::vector<std::string>::const_iterator j = events.begin(); j != events.end(); ++j)
            {
               if (eventName.compare(*j) == 0)
               {
                  exists = true; break;
               }
            }
            if (exists)
            {
               std::ostringstream out;
               out << "EventHandler " << this << " " << mHandlerName << " - event exists in unprocessed event queue: " << eventName << std::endl;
               safeCout(out.str());
               break;
            }
         }
      }
      return exists;
   }
   
#if 0
   bool expectEvent(
      CPCAPI2::SipAccount::SipAccountHandle h,
      const std::string& eventName,
      unsigned int timeoutMS,
      std::function<void(void)> f = std::function<void(void)>()
      );
#endif

   template<typename TArg0, typename TArg1, class Pred> bool expectEvent(
      const std::string& eventName,
      unsigned int timeoutMS,
      Pred pred,
      TArg0& arg0,
      TArg1& arg1
      )
   {
      return expectEvent(0, eventName, "", timeoutMS, pred, arg0, arg1);
   }

   template<typename TArg0, typename TArg1, class Pred> bool expectEvent(
      int line,
      const std::string& eventName,
      unsigned int timeoutMS,
      Pred pred,
      TArg0& arg0,
      TArg1& arg1
      )
   {
      return expectEvent(line, eventName, "", timeoutMS, pred, arg0, arg1);
   }

   template<typename TArg0, typename TArg1, class Pred> bool expectEvent(
      int line,
      const std::string& eventName,
      const std::string& fileName,
      unsigned int timeoutMS,
      Pred pred,
      TArg0& arg0,
      TArg1& arg1
      )
   {
      startProcessThread();

      std::chrono::time_point<std::chrono::system_clock> startTime = std::chrono::system_clock::now();
      std::time_t ttp = std::chrono::system_clock::to_time_t(startTime);
      std::string sFileName = stripFilename(fileName);
      eventDebug(mHandlerName << " " << this << " - expecting " << eventName << " at line " << sFileName << ":" << line << "; time=" << ttp);
      std::unique_lock<std::mutex> l(mEventMutex);
      mNumThreads++;

      std::chrono::duration<long long, std::milli> waitPeriod = std::chrono::milliseconds(0);

      do
      {
         CommandQueue::iterator itCmds = mCommands.begin();
         while (itCmds != mCommands.end())
         {
            std::shared_ptr<AutoTestReadCallback> cmd = itCmds->cmd;
            if (cmd->eventName().compare(eventName) == 0)
            {
               try
               {
                  if (pred(std::get<0>(cmd->args<TArg0, TArg1>())))
                  {
                     // safeCout( mHandlerName << " " << this << " - predicate matches, tie and erase " << cmd->eventName() << " expecting for " << eventName << " at line " << sFileName << ":" << line);
                     std::tie(arg0,arg1) = cmd->args<TArg0,TArg1>();
                     itCmds = mCommands.erase(itCmds);
                     mNumThreads--;
                     return true;
                  }
                  else if (1 == cmd->numArgs() && pred(std::get<0>(cmd->args<TArg0>())))
                  {
                     // safeCout( mHandlerName << " " << this << " - predicate matches, tie and erase " << cmd->eventName() << " expecting for " << eventName << " at line " << sFileName << ":" << line);
                     itCmds = mCommands.erase(itCmds);
                     mNumThreads--;
                     return true;
                  }
                  else if (0 == cmd->numArgs())
                  {
                     // safeCout( mHandlerName << " " << this << " - predicate matches, tie and erase " << cmd->eventName() << " expecting for " << eventName << " at line " << sFileName << ":" << line);
                     itCmds = mCommands.erase(itCmds);
                     mNumThreads--;
                     return true;
                  }
                  else
                  {
                     // event name matched, but the predicate failed;
                     // keep searching the queue for one that works
                     safeCout(mHandlerName << " " << this << " - expecting, not my event (" << eventName << "); pred failed at line " << sFileName << ":" << line);
                     itCmds++;
                  }
               }
               catch (const char* msg)
               {
                  safeCout(mHandlerName << " " << this << " - exception \"" << msg << "\" caught handling event (" << eventName << "); pred failed at line " << sFileName << ":" << line);
                  assert(false);
               }
            }
            else if (cmd->eventName().size() == 0)
            {
               // this is not really an event; it's just some function that
               // the SDK in it's wisdom decided should happen in the context of
               // the app (main) thread; consume it, and go on with life
               // safeCout(mHandlerName << " " << this << " - run-it " << cmd->eventName() << " and erase, expecting " << eventName << " at line " << sFileName << ":" << line);
               (*cmd)();
               itCmds = mCommands.erase(itCmds);
            }
            else
            {
               if (mNumThreads == 1)
               {
                  // safeCout(mHandlerName << " " << this << " - WARNING - not my event; wrong name. expected " << eventName << " at line " << sFileName << ":" << line << " but received " << cmd->eventName() );
               }
               else
               {
                  // there is another call to expectEvent from the same EventHandler.
                  // a common way to hit this is accidently checking for events from the same TestAccount instance (e.g. bob)
                  // from multiple std::async threads at the same time (perhaps from alice's std::async thread you tried to check an event from bob?)
                  assert(false);
               }
               itCmds++;
            }
         }

         if (!mAlive)
         {
            break;
         }

         std::chrono::time_point<std::chrono::system_clock> iterTime = std::chrono::system_clock::now();
         waitPeriod = std::chrono::duration_cast<std::chrono::milliseconds>(iterTime - startTime);
         if (waitPeriod >= std::chrono::milliseconds(timeoutMS))
         {
            break;
         }

         mEvent.wait_for(l, std::chrono::milliseconds(20));
      }
      while (true /*mEvent.wait_for(l, std::chrono::milliseconds(timeoutMS) - waitPeriod) == std::cv_status::no_timeout*/);

      ttp = std::chrono::system_clock::to_time_t(std::chrono::system_clock::now());
      eventDebug("EventHandler " << this << " " << mHandlerName << " - missed, expecting " << eventName << " at line " << sFileName << ":" << line << "; time=" << ttp);
      CommandQueue commandsCpy = mCommands;
      if (commandsCpy.size() > 0)
      {
         std::ostringstream out;
         out << "EventHandler " << this << " " << mHandlerName << " - expecting, unprocessed events:" << std::endl;
         CommandQueue::const_iterator itCmds = commandsCpy.begin();
         for (; itCmds != commandsCpy.end(); ++itCmds)
         {
            out << "   " << itCmds->cmd->eventName() << std::endl;
         }
         safeCout(out.str());
      }
      mNumThreads--;
      return false;
   }

   template<typename TArg0, class Pred> bool expectEvent(
      const std::string& eventName,
      unsigned int timeoutMS,
      Pred pred,
      TArg0& arg0
      )
   {
      return expectEvent(0, eventName, "", timeoutMS, pred, arg0);
   }

   template<typename TArg0, class Pred> bool expectEvent(
      int line,
      const std::string& eventName,
      unsigned int timeoutMS,
      Pred pred,
      TArg0& arg0
      )
   {
      return expectEvent(line, eventName, "", timeoutMS, pred, arg0);
   }

   template<typename TArg0, class Pred> bool expectEvent(
      int line,
      const std::string& eventName,
      const std::string& fileName,
      unsigned int timeoutMS,
      Pred pred,
      TArg0& arg0
      )
   {
      startProcessThread();

      std::chrono::time_point<std::chrono::system_clock> startTime = std::chrono::system_clock::now();
      std::time_t ttp = std::chrono::system_clock::to_time_t(startTime);
      std::string sFileName = stripFilename(fileName);
      eventDebug(mHandlerName << " " << this << " - expecting " << eventName << " at line " << sFileName << ":" << line << "; time=" << ttp);
      std::unique_lock<std::mutex> l(mEventMutex);
      mNumThreads++;

      std::chrono::duration<long long, std::milli> waitPeriod = std::chrono::milliseconds(0);

      do
      {
         CommandQueue::iterator itCmds = mCommands.begin();
         while (itCmds != mCommands.end())
         {
            std::shared_ptr<AutoTestReadCallback> cmd = itCmds->cmd;
            if (cmd->eventName().compare(eventName) == 0)
            {
               try
               {
                  if (pred(std::get<0>(cmd->args<TArg0>())))
                  {
                     // safeCout( mHandlerName << " " << this << " - predicate matches, tie and erase " << cmd->eventName() << " expecting for " << eventName << " at line " << sFileName << ":" << line);
                     std::tie(arg0) = cmd->args<TArg0>();
                     itCmds = mCommands.erase(itCmds);
                     mNumThreads--;
                     return true;
                  }
                  else
                  {
                     // event name matched, but the predicate failed;
                     // keep searching the queue for one that works
                     safeCout(mHandlerName << " " << this << " - expecting, not my event (" << eventName << "); pred failed at line " << sFileName << ":" << line);
                     itCmds++;
                  }
               }
               catch (const char* msg)
               {
                  safeCout(mHandlerName << " " << this << " - exception \"" << msg << "\" caught handling event (" << eventName << "); pred failed at line " << sFileName << ":" << line);
                  assert(false);
               }
            }
            else if (cmd->eventName().size() == 0)
            {
               // this is not really an event; it's just some function that
               // the SDK in it's wisdom decided should happen in the context of
               // the app (main) thread; consume it, and go on with life
               // safeCout(mHandlerName << " " << this << " - run-it " << cmd->eventName() << " and erase, expecting " << eventName << " at line " << sFileName << ":" << line);
               (*cmd)();
               itCmds = mCommands.erase(itCmds);
            }
            else
            {
               if (mNumThreads == 1)
               {
                  // safeCout(mHandlerName << " " << this << " - WARNING - not my event; wrong name. expected " << eventName << " at line " << sFileName << ":" << line << " but received " << cmd->eventName() );
               }
               else
               {
                  // there is another call to expectEvent from the same EventHandler.
                  // a common way to hit this is accidently checking for events from the same TestAccount instance (e.g. bob)
                  // from multiple std::async threads at the same time (perhaps from alice's std::async thread you tried to check an event from bob?)
                  assert(false);
               }
               itCmds++;
            }
         }

         if (!mAlive)
         {
            break;
         }

         std::chrono::time_point<std::chrono::system_clock> iterTime = std::chrono::system_clock::now();
         waitPeriod = std::chrono::duration_cast<std::chrono::milliseconds>(iterTime - startTime);
         if (waitPeriod >= std::chrono::milliseconds(timeoutMS))
         {
            break;
         }

         mEvent.wait_for(l, std::chrono::milliseconds(20));
      }
      while (true /*mEvent.wait_for(l, std::chrono::milliseconds(timeoutMS) - waitPeriod) == std::cv_status::no_timeout*/);

      ttp = std::chrono::system_clock::to_time_t(std::chrono::system_clock::now());
      eventDebug("EventHandler " << this << " " << mHandlerName << " - missed, expecting " << eventName << " at line " << sFileName << ":" << line << "; time=" << ttp);
      CommandQueue commandsCpy = mCommands;
      if (commandsCpy.size() > 0)
      {
         std::ostringstream out;
         out << "EventHandler " << this << " " << mHandlerName << " - expecting, unprocessed events:" << std::endl;
         CommandQueue::const_iterator itCmds = commandsCpy.begin();
         for (; itCmds != commandsCpy.end(); ++itCmds)
         {
            out << "   " << itCmds->cmd->eventName() << std::endl;
         }
         safeCout(out.str());
      }
      mNumThreads--;
      return false;
   }

   template<typename Pred>
   bool expectEvent(
      const std::string& eventName,
      unsigned int timeoutMS,
      Pred pred
   )
   {
      return expectEvent(0, eventName, "", timeoutMS, pred);
   }

   template<typename Pred>
   bool expectEvent(
      int line,
      const std::string& eventName,
      unsigned int timeoutMS,
      Pred pred
   )
   {
      return expectEvent(line, eventName, "", timeoutMS, pred);
   }

   template<typename Pred>
   bool expectEvent(
      int line,
      const std::string& eventName,
      const std::string& fileName,
      unsigned int timeoutMS,
      Pred pred
   )
   {
      startProcessThread();

      std::chrono::time_point<std::chrono::system_clock> startTime = std::chrono::system_clock::now();
      std::time_t ttp = std::chrono::system_clock::to_time_t(startTime);
      std::string sFileName = stripFilename(fileName);
      eventDebug(mHandlerName << " " << this << " - expecting " << eventName << " at line " << sFileName << ":" << line << "; time=" << ttp);
      std::unique_lock<std::mutex> l(mEventMutex);
      mNumThreads++;

      std::chrono::duration<long long, std::milli> waitPeriod = std::chrono::milliseconds(0);

      do
      {
         CommandQueue::iterator itCmds = mCommands.begin();
         while (itCmds != mCommands.end())
         {
            std::shared_ptr<AutoTestReadCallback> cmd = itCmds->cmd;
            if (cmd->eventName().compare(eventName) == 0)
            {
               try
               {
                  // safeCout( mHandlerName << " " << this << " - predicate matches, tie and erase " << cmd->eventName() << " expecting for " << eventName << " at line " << sFileName << ":" << line);
                  itCmds = mCommands.erase(itCmds);
                  mNumThreads--;
                  return true;
               }
               catch (const char* msg)
               {
                  safeCout(mHandlerName << " " << this << " - exception \"" << msg << "\" caught handling event (" << eventName << "); pred failed at line " << sFileName << ":" << line);
                  assert(false);
               }
            }
            else if (cmd->eventName().size() == 0)
            {
               // this is not really an event; it's just some function that
               // the SDK in it's wisdom decided should happen in the context of
               // the app (main) thread; consume it, and go on with life
               // safeCout(mHandlerName << " " << this << " - run-it " << cmd->eventName() << " and erase, expecting " << eventName << " at line " << sFileName << ":" << line);
               (*cmd)();
               itCmds = mCommands.erase(itCmds);
            }
            else
            {
               if (mNumThreads == 1)
               {
                  // safeCout(mHandlerName << " " << this << " - WARNING - not my event; wrong name. expected " << eventName << " at line " << sFileName << ":" << line << " but received " << cmd->eventName() );
               }
               else
               {
                  // there is another call to expectEvent from the same EventHandler.
                  // a common way to hit this is accidently checking for events from the same TestAccount instance (e.g. bob)
                  // from multiple std::async threads at the same time (perhaps from alice's std::async thread you tried to check an event from bob?)
                  assert(false);
               }
               itCmds++;
            }
         }

         if (!mAlive)
         {
            break;
         }

         std::chrono::time_point<std::chrono::system_clock> iterTime = std::chrono::system_clock::now();
         waitPeriod = std::chrono::duration_cast<std::chrono::milliseconds>(iterTime - startTime);
         if (waitPeriod >= std::chrono::milliseconds(timeoutMS))
         {
            break;
         }

         mEvent.wait_for(l, std::chrono::milliseconds(20));
      }
      while (true /*mEvent.wait_for(l, std::chrono::milliseconds(timeoutMS) - waitPeriod) == std::cv_status::no_timeout*/);

      ttp = std::chrono::system_clock::to_time_t(std::chrono::system_clock::now());
      eventDebug("EventHandler " << this << " " << mHandlerName << " - missed, expecting " << eventName << " at line " << sFileName << ":" << line << "; time=" << ttp);
      CommandQueue commandsCpy = mCommands;
      if (commandsCpy.size() > 0)
      {
         std::ostringstream out;
         out << "EventHandler " << this << " " << mHandlerName << " - expecting, unprocessed events:" << std::endl;
         CommandQueue::const_iterator itCmds = commandsCpy.begin();
         for (; itCmds != commandsCpy.end(); ++itCmds)
         {
            out << "   " << itCmds->cmd->eventName() << std::endl;
         }
         safeCout(out.str());
      }
      mNumThreads--;
      return false;
   }

   void processNonUnitTestEvents(unsigned int timeoutMS)
   {
      startProcessThread();

      //std::chrono::time_point<std::chrono::system_clock> startTime = std::chrono::system_clock::now();
      //std::time_t ttp = std::chrono::system_clock::to_time_t(startTime);
      std::unique_lock<std::mutex> l(mEventMutex);

      std::chrono::duration<long long, std::milli> waitPeriod = std::chrono::milliseconds(0);

      CommandQueue::iterator itCmds = mCommands.begin();
      while (itCmds != mCommands.end())
      {
         std::shared_ptr<AutoTestReadCallback> cmd = itCmds->cmd;
         if (cmd->eventName().size() == 0)
         {
            // this is not really an event; it's just some function that
            // the SDK in it's wisdom decided should happen in the context of
            // the app (main) thread; consume it, and go on with life
            (*cmd)();
            itCmds = mCommands.erase(itCmds);
         }
         else
         {
            itCmds++;
         }
      }
   }

   template<typename TArg0, typename TArg1, class Pred> bool waitForEvent(
      int line,
      const std::string& eventName,
      unsigned int timeoutMS,
      Pred pred,
      TArg0& arg0,
      TArg1& arg1
      )
   {
      return waitForEvent(line, eventName, "", timeoutMS, pred, arg0, arg1);
   }

   template<typename TArg0, typename TArg1, class Pred> bool waitForEvent(
      int line,
      const std::string& eventName,
      const std::string& fileName,
      unsigned int timeoutMS,
      Pred pred,
      TArg0& arg0,
      TArg1& arg1
      )
   {
      startProcessThread();

      std::string sFileName = stripFilename(fileName);
      eventDebug(mHandlerName << " " << this << " - waiting for " << eventName << " at line " << sFileName << ":" << line);
      std::unique_lock<std::mutex> l(mEventMutex);

      std::chrono::time_point<std::chrono::system_clock> startTime = std::chrono::system_clock::now();
      std::chrono::duration<long long, std::milli> waitPeriod = std::chrono::milliseconds(0);

      do
      {
         CommandQueue::iterator itCmds = mCommands.begin();
         while (itCmds != mCommands.end())
         {
            std::shared_ptr<AutoTestReadCallback> cmd = itCmds->cmd;
            if( cmd->eventName().size() == 0 )
            {
               // SDK function, run it and erase
               // safeCout(mHandlerName << " " << this << " - run-it " << cmd->eventName() << " and erase " << eventName << " at line " << sFileName << ":" << line);
               (*cmd)();
               itCmds = mCommands.erase( itCmds );
            }
            else if( cmd->eventName().compare( eventName ) != 0 )
            {
               // Name doesn't match, consume it and continue
               safeCout(mHandlerName << " " << this << " - consumed " << cmd->eventName() << " while waiting for " << eventName << " at line " << sFileName << ":" << line);
               itCmds = mCommands.erase( itCmds );
            }
            else // name matches
            {
               try
               {
                  if (pred(std::get<0>(cmd->args<TArg0, TArg1>())))
                  {
                     // safeCout(mHandlerName << " " << this << " - predicate matches, tie and erase " << cmd->eventName() << " waiting for " << eventName << " at line " << sFileName << ":" << line);
                     std::tie(arg0,arg1) = cmd->args<TArg0,TArg1>();
                     itCmds = mCommands.erase( itCmds );
                     return true;
                  }
                  else
                  {
                     // safeCout(mHandlerName << " " << this << " - predicate does not match, erase " << cmd->eventName() << " waiting for " << eventName << " at line " << sFileName << ":" << line);
                     // Predicate doesn't match, consume and go
                     itCmds = mCommands.erase( itCmds );
                  }
               }
               catch (const char* msg)
               {
                  safeCout(mHandlerName << " " << this << " - exception \"" << msg << "\" caught handling waiting for event (" << eventName << "); pred failed at line " << sFileName << ":" << line);
                  assert(false);
               }
            }
         }

         std::chrono::time_point<std::chrono::system_clock> iterTime = std::chrono::system_clock::now();
         waitPeriod = std::chrono::duration_cast<std::chrono::milliseconds>(iterTime - startTime);
         if (waitPeriod >= std::chrono::milliseconds(timeoutMS))
         {
            break;
         }
      }
      while (mEvent.wait_for(l, std::chrono::milliseconds(timeoutMS) - waitPeriod) == std::cv_status::no_timeout);

      safeCout("EventHandler " << this << " " << mHandlerName << " - missed " << eventName << " at line " << sFileName << ":" << line);
      CommandQueue commandsCpy = mCommands;
      if (commandsCpy.size() > 0)
      {
         std::ostringstream out;
         out << "EventHandler " << this << " " <<  mHandlerName << " - unprocessed events:" << std::endl;
         CommandQueue::const_iterator itCmds = commandsCpy.begin();
         for (; itCmds != commandsCpy.end(); ++itCmds)
         {
            out << "   " << itCmds->cmd->eventName() << std::endl;
         }
         safeCout(out.str());
      }
      return false;
   }

private:
   CPCAPI2::AutoTestProcessor* mSipAccountIf;
   std::string mHandlerName;
   struct TestEventItem
   {
      std::shared_ptr<CPCAPI2::AutoTestReadCallback> cmd;
      std::chrono::time_point<std::chrono::system_clock> enqueueTime; // !jjg! we should use steady_clock, but it seems broken in VS 2012
   };
   typedef std::deque<TestEventItem> CommandQueue;
   CommandQueue mCommands;
   std::condition_variable mEvent;
   std::mutex mEventMutex;
   std::future<void> mProcessFuture;
   bool mAlive;
   int mNumThreads;
};

#define cpcExpectEvent(eventHandler, eventName, timeoutMS, predicate, arg0, arg1) eventHandler->expectEvent(__LINE__, eventName, timeoutMS, predicate, arg0, arg1)
#define cpcWaitForEvent(eventHandler, eventName, timeoutMS, predicate, arg0, arg1) eventHandler->waitForEvent(__LINE__, eventName, timeoutMS, predicate, arg0, arg1)
}
}
