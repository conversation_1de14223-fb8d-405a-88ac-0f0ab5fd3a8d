#ifndef single_ownable_h
#define single_ownable_h

#include <mutex>

namespace CPCAPI2
{
namespace test
{
   class SingleOwnable
   {
   public:
      SingleOwnable() : mOwned(false) {}
      
      bool tryOwn()
      {
         const std::lock_guard<std::mutex> lock(mOwnedMutex);
         if (mOwned)
         {
            return false;
         }
         else
         {
            mOwned = true;
            return true;
         }
      }
      
      void releaseOwn()
      {
         const std::lock_guard<std::mutex> lock(mOwnedMutex);
         mOwned = false;
      }
   
   private:
      bool mOwned;
      std::mutex mOwnedMutex;
   };
}
}


#endif
