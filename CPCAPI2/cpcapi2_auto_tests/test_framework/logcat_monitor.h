#ifndef logcat_monitor_h
#define logcat_monitor_h

#include "cpcapi2_test_framework.h"

namespace CPCAPI2
{
namespace Logcat
{
   class LogcatMonitor;
}

namespace test
{

class LogcatMonitorHolder
{
public:
   static LogcatMonitorHolder* getInstance();
   
   CPCAPI2::Logcat::LogcatMonitor* monitor() { return mInstance; }
   EventHandler* events() { return &mEvents; }
   
private:
   LogcatMonitorHolder();
   static void initInstance();
   CPCAPI2::Logcat::LogcatMonitor* mInstance;
   EventHandler mEvents;
};

}
}


#endif /* logcat_monitor_h */
