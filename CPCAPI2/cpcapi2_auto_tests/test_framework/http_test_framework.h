#ifndef http_test_framework_hpp
#define http_test_framework_hpp

#include <iostream>
#include <condition_variable>

#include "server_http.hpp"
typedef SimpleWeb::Server<SimpleWeb::HTTP> HttpServer;

namespace CPCAPI2
{
   namespace HttpTestFramework
   {
      // can be used where a file stream would normally be used; e.g. used for resourceSendStream method below
      class FakeFileStream : public std::streambuf
      {
      public:
          FakeFileStream(const char* data, unsigned int lenBytes);
       
      private:
          int_type underflow();
          int_type uflow();
          int_type pbackfail(int_type ch);
          std::streamsize showmanyc();
       
          const char * const begin_;
          const char * const end_;
          const char * current_;
      };
      
      class FileSender
      {
      public:
         // uses SimpleWebServer to stream a file to a connected HTTP client.
         // can optionally specify a minimum download time, which will cause the server to send data slow
         // enough such that it takes at least that amount of time to download the file
         static void resourceSendStream(const HttpServer& server, const std::shared_ptr<HttpServer::Response>& response,
                                        const std::shared_ptr<std::istream>& ifs, size_t streamSizeBytes,
                                        std::chrono::duration<int> minDownloadTimeSec = std::chrono::seconds(0));
         
         struct CvWrapper
         {
            std::shared_ptr<std::condition_variable> cv;
            std::shared_ptr<std::mutex> mx;
         };
         
         static void resourceSendStreamHault(const HttpServer& server, const std::shared_ptr<HttpServer::Response>& response,
                                             std::chrono::system_clock::time_point hangPoint,
                                             const CvWrapper& hangCv /* trigger this to unblock from hang and abort the transfer */);

         // send streamSizeBytes of arbitrary data
         static void resourceSendStreamBogusBytes(const HttpServer& server, const std::shared_ptr<HttpServer::Response>& response,
                                             size_t streamSizeBytes);
   private:
         static void resourceSendStreamBogusBytes(const HttpServer& server, const std::shared_ptr<HttpServer::Response>& response,
                                                  size_t streamSizeBytes,
                                                  size_t totalRemainingBytes);
      };

   }
}




#endif /* http_test_framework_hpp */
