#include "cpcapi2_test_framework.h"

#include <future>
#include <sstream>

namespace CPCAPI2
{
namespace test
{

long getTimezone()
{
#if _MSC_VER > 1800
	return _timezone;
#else
	return timezone;
#endif
}

void setTimezone(long offset)
{
#if _MSC_VER > 1800
	_timezone = offset;
#else
   int offsetHour = offset / 3600;
   int offsetMinute = (offset % 3600) / 60;
   int offsetSecond = offset % 3600 % 60;
   std::stringstream timeZone;
   timeZone << "GMT";
   if (offset >= 0)
      timeZone << "+";
   else
      timeZone << "-";
   timeZone << offsetHour;
   timeZone << ":";
   timeZone << offsetMinute;
   timeZone << ":";
   timeZone << offsetSecond;
   setenv("TZ", timeZone.str().c_str(), 1);
   tzset();
	//timezone = offset; //On Linux, Local timezone information shall be set as though mktime() called tzset(). if TZ is unset, calling mktime() could cause timezone set back to system default
#endif
}

static int line;

EventHandler::EventHandler(const char* handlerName, CPCAPI2::AutoTestProcessor* sai):
   mHandlerName(handlerName),
   mSipAccountIf(sai),
   mAlive(true),
   mNumThreads(0)
{

}

EventHandler::~EventHandler()
{
   shutdown();
   if (mProcessFuture.valid())
   {
      mProcessFuture.get();
   }
}

void EventHandler::startProcessThread()
{
   if (mProcessFuture.valid())
   {
      // eventDebug("cpcapi2_test_framework::EventHandler::startProcessThread(): " << this << " - already started");
      return; // already started
   }

   if (mSipAccountIf)
   {
      // eventDebug("cpcapi2_test_framework::EventHandler::startProcessThread(): " << this << " - about to lauch");
      mProcessFuture = std::async(std::launch::async, [this]() {
#if _WIN32
         SetThreadName(::GetCurrentThreadId(), "EventHandlerProcessThread");
#endif
         bool alive = true;
         // eventDebug(mHandlerName << " cpcapi2_test_framework::EventHandler::startProcessThread(): " << this << " - starting process_test");
         while (alive)
         {
            {
               // process ALL pending callbacks (wait until NULL)
               AutoTestReadCallback* cmd = mSipAccountIf->process_test(20);
               if (NULL == cmd)
               {
                  mEventMutex.lock();
                  alive = mAlive;
                  mEventMutex.unlock();
                  continue;
               }
               // eventDebug(mHandlerName << " cpcapi2_test_framework::EventHandler::startProcessThread(): " << this << " - handling " << (cmd->eventName().size() > 0 ? cmd->eventName() : "{Unnamed Event}"));
               mEventMutex.lock();
               TestEventItem tei;
               tei.cmd = std::shared_ptr<AutoTestReadCallback>(cmd);
               tei.enqueueTime = std::chrono::system_clock::now();
               mCommands.push_back(tei);
               alive = mAlive;
               mEventMutex.unlock();
               mEvent.notify_all();
            }
         }
      });
   }
   else
   {
      eventDebug("cpcapi2_test_framework::EventHandler::startProcessThread(): " << this << " - Warning: EventHandler passed NULL AutoTestProcessor; will not process, mSipAccountIf: " << mSipAccountIf << " mHandlerName: " << mHandlerName);
   }
}

void EventHandler::addEvent(AutoTestReadCallback* fpCmd)
{
   mEventMutex.lock();
   TestEventItem tei;
   tei.cmd = std::shared_ptr<AutoTestReadCallback>(fpCmd);
   tei.enqueueTime = std::chrono::system_clock::now();
   mCommands.push_back(tei);
   mEventMutex.unlock();
   mEvent.notify_all();
}

void EventHandler::shutdown()
{
   // eventDebug("cpcapi2_test_framework::EventHandler::shutdown(): " << this);
   mEventMutex.lock();
   mAlive = false;
   mEventMutex.unlock();
   mEvent.notify_all();
   std::this_thread::sleep_for(std::chrono::milliseconds(100));
}

std::string EventHandler::stripFilename(const std::string& sFileName)
{
   size_t position = sFileName.rfind(".");
   std::string sFile = (std::string::npos == position) ? sFileName : sFileName.substr(0, position);
   position = sFileName.rfind("/");
   sFile = (std::string::npos == position) ? sFile : sFile.substr((position + 1), std::string::npos);
   return sFile;
}

std::mutex safeCoutMutex;

#if _WIN32
struct THREADNAME_INFO
{
   DWORD dwType;     // must be 0x1000
   LPCSTR szName;    // pointer to name (in user addr space)
   DWORD dwThreadID; // thread ID (-1 = caller thread)
   DWORD dwFlags;    // reserved for future use, must be zero
};
void SetThreadName(DWORD dwThreadID, LPCSTR szThreadName)
{
    THREADNAME_INFO info;
    info.dwType = 0x1000;
    info.szName = szThreadName;
    info.dwThreadID = dwThreadID;
    info.dwFlags = 0;

    __try
    {
        RaiseException(0x406D1388, 0, sizeof(info) / sizeof(DWORD),
                       (ULONG_PTR*)&info);
    }
    __except (EXCEPTION_CONTINUE_EXECUTION)
    {
    }
}
#endif // _WIN32

}
}
