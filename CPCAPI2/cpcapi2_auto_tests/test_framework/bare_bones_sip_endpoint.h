#pragma once

#include <string>
#include <functional>

#include <resip/stack/SipStack.hxx>
#include <resip/dum/DialogId.hxx>
#include <resip/stack/Uri.hxx>
#include <resip/stack/EventStackThread.hxx>
#include <resip/stack/TransactionUser.hxx>
#include <rutil/Data.hxx>

namespace CPCAPI2
{
namespace test
{
class BareBonesSipEndpoint : public resip::TransactionUser
{
public:
	BareBonesSipEndpoint();
	virtual ~BareBonesSipEndpoint();

	void initialize();

	bool registerEndpoint(
		const resip::Uri& aor,
		const resip::Data& password=resip::Data::Empty,
		const resip::Uri* proxy=NULL
		);

	bool expectRequest(
		const std::string& requestMethod,
		unsigned int timeoutMS,
		std::function<void(void)> f = std::function<void(void)>()
		);

	bool expectResponse(
		const std::string& requestMethod,
		unsigned int responseCode,
		unsigned int timeoutMS,
		std::function<void(void)> f = std::function<void(void)>()
		);

	resip::SipMessage* makeRequest(
		const std::string& requestMethod,
		const resip::Uri& to
		);

	resip::SipMessage* makeRequest(
		const std::string& requestMethod,
		resip::DialogId* dialogId
		);

	resip::SipMessage* makeResponse(
		unsigned int responseCode,
		resip::SipMessage* msg = 0
		);

	bool send(const resip::SipMessage& msg);

	const resip::SipMessage* lastRequest() const;
	const resip::SipMessage* lastResponse() const;

	// TransactionUser
	virtual const resip::Data& name() const { static resip::Data tuname("BareBonesSipEndpoint"); return tuname; }

private:
	resip::SipStack* mStack;
	resip::EventStackThread* mStackThread;
	resip::EventThreadInterruptor* mSelectInterruptor;
	resip::FdPollGrp* mFdPollGrp;

	resip::SipMessage* mLastRequest;
	resip::SipMessage* mLastResponse;
};
}
}
