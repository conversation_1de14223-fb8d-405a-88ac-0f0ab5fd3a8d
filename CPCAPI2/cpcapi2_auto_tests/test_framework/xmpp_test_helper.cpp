#include "brand_branded.h"

#include "xmpp_test_helper.h"
#include "../cpcapi2_test_fixture.h"
#include "network_utils.h"

#include <interface/experimental/xmpp/XmppAccountJsonProxy.h>
#include <interface/experimental/xmpp/XmppChatJsonProxy.h>
#include <interface/experimental/xmpp/XmppRosterJsonProxy.h>
#include <interface/experimental/xmpp/XmppVCardJsonProxy.h>
#include <interface/experimental/xmpp/XmppMultiUserChatJsonProxy.h>
#include <interface/experimental/cloudconnector/CloudConnector.h>
#include <interface/experimental/cloudconnector/CloudConnectorHandler.h>
#include <interface/experimental/cloudconnector/CloudConnectorTypes.h>
#include <interface/experimental/confconnector/ConferenceConnector.h>
#include <interface/experimental/confconnector/ConferenceConnectorHandler.h>
#include <interface/experimental/confconnector/ConferenceConnectorTypes.h>
#include <interface/experimental/orchestration_server/OrchestrationServer.h>
#include <interface/experimental/orchestration_server/OrchestrationServerHandler.h>
#include <interface/experimental/cloudwatchdog/CloudWatchdogService.h>
#include <interface/experimental/cloudwatchdog/CloudWatchdogServiceHandler.h>
#include <interface/experimental/cloudserviceconfig/CloudServiceConfig.h>
#include <interface/experimental/messagestore/MessageStoreManager.h>
#include <interface/experimental/messagestore/MessageStoreHandler.h>
#include <interface/experimental/messagestore/MessageStoreTypes.h>
#include <interface/experimental/remotesync_xmpp_helper/RemoteSyncXmppHelper.h>
#include <interface/experimental/jsonapi/JsonApiServerInternal.h>
#include "xmpp/XmppAccountInterface.h"
#include "xmpp/XmppAccountJsonApi.h"
#include "xmpp/XmppChatJsonApi.h"
#include "xmpp/XmppVCardJsonApi.h"
#include "xmpp/XmppRosterJsonApi.h"
#include "xmpp/XmppMultiUserChatJsonApi.h"
#include "xmpp_agent/XmppAgentHandler.h"
#include "xmpp_agent/XmppAgentJsonApi.h"
#include "xmpp_agent/XmppAgentJsonProxy.h"
#include "push_endpoint/PushNotificationEndpointJsonApi.h"
#include "push_endpoint/PushNotificationEndpointJsonProxy.h"
#include "phone/PhoneInterface.h"

#if (CPCAPI2_BRAND_REMOTE_SYNC_MODULE == 1)
using namespace CPCAPI2::RemoteSync;
#endif

#include "../../impl/util/CurlPPHelper.h"
#include "../../impl/util/CurlPPProgress.h"
#include "../../impl/util/CurlPPSSL.h"
#include "../../impl/auth_server/AuthServerJwtUtils.h"
#include "../../impl/auth_server/AuthServerDbAccess.h"

// rapidjson
#include <document.h>

#include <random>
#include <iostream>
#include <string>

#if (CPCAPI2_BRAND_XMPP_ACCOUNT_MODULE == 1)

using namespace ::testing;
using namespace CPCAPI2;
using namespace CPCAPI2::XmppAccount;
using namespace CPCAPI2::XmppRoster;
using namespace CPCAPI2::XmppChat;
using namespace CPCAPI2::XmppFileTransfer;
using namespace CPCAPI2::XmppVCard;
using namespace CPCAPI2::XmppMultiUserChat;
using namespace CPCAPI2::XmppIMCommand;
using namespace CPCAPI2::XmppAgent;
using namespace CPCAPI2::PushEndpoint;
using namespace CPCAPI2::PushService;
using namespace CPCAPI2::PushEndpoint;
using namespace CPCAPI2::JsonApi;
using namespace CPCAPI2::CloudConnector;
using namespace CPCAPI2::CloudServiceConfig;
using namespace CPCAPI2::OrchestrationServer;
using namespace CPCAPI2::CloudWatchdog;
using namespace CPCAPI2::CloudServiceConfig;
using namespace CPCAPI2::MessageStore;
using namespace CPCAPI2::XmppPush;


static std::once_flag sOnce;
static std::priority_queue<XmppTestUserInfo> sXmppAvailableUsers;

std::ostream& operator<<(std::ostream& os, const XmppTestUserInfo& user)
{
   os << "(" << user.username << ":" << user.password << ")";
   return os;
}

std::istream& operator>>(std::istream& is, XmppTestUserInfo& user)
{
   if (is.peek() == '(')
   {
      is.ignore(1, '(');
      std::getline(is, user.username, ':');
      std::getline(is, user.password, ')');
   }
   else
   {
      is >> user.username;
      user.password = user.username;
   }
   return is;
}

static void setupXmppTestUserInfos()
{
   const char* xmppUsersString = getenv("CPCAPI2_XMPP_USERS");
   if (xmppUsersString == NULL)
   {
      xmppUsersString = DefaultXmppUserList;
   }

   static std::vector<XmppTestUserInfo> xmppUsers;
   xmppUsers = split<XmppTestUserInfo>(xmppUsersString, ',');

   if (xmppUsersString == DefaultXmppUserList)
   {
#if 0
      // this is done only for performance testing
      // where we need a large number of endpoints
      xmppUsers.clear();
      for (int iusers = 0; iusers < 1050; iusers++)
      {
         std::stringstream userstr;
         userstr << "user" << iusers;
         xmppUsers.push_back(userstr.str());
      }
#endif
      // shuffle default list
      std::random_device rd;
      std::mt19937 generator(rd());
      std::shuffle(xmppUsers.begin(), xmppUsers.end(), generator);
   }

   int p=0;
   std::vector<XmppTestUserInfo>::iterator it;
   for (it=xmppUsers.begin(); it!=xmppUsers.end(); ++it)
   {
      it->priority = p++;
      sXmppAvailableUsers.push(*it);
   }
}

#define OPENFIRE_ADMIN_PORT 9093
static XmppTestUserInfo openfireRestApiAddUser(XmppTestUserInfo user) 
{
   std::call_once(sOnce, [] { setupXmppTestUserInfos(); });
   CurlPPHelper helper;
   curlpp::Easy easy;
   std::stringstream request;
   request << "{ \"username\":\"" << user.username << "\",\"password\":\"" << user.password << "\" }";

   std::stringstream url;
   url << "http://" << TestEnvironmentConfig::defaultXmppServer() << ":" << OPENFIRE_ADMIN_PORT << "/plugins/restapi/v1/users";

   helper.setDefaultOptions(easy, url.str(), "POST", request.str().size());
   easy.setOpt(new curlpp::options::PostFields(request.str()));

   std::list<std::string> headers;
   std::string key = std::string("Authorization: ") + TestEnvironmentConfig::openfireRestApiKey().c_str();
   headers.push_back(key);
   headers.push_back("Content-Type: application/json");
   easy.setOpt(new curlpp::options::HttpHeader(headers));

   std::stringstream response;
   easy.setOpt(new curlpp::options::WriteStream(&response));
   easy.perform();

   if (curlpp::infos::ResponseCode::get(easy) == 201)
   {
      return user;
   }
   else
   {
      std::cout << curlpp::infos::ResponseCode::get(easy) << " adding user failed " << std::endl;
   }

   assert(sXmppAvailableUsers.size() > 0);
   user = sXmppAvailableUsers.top();
   sXmppAvailableUsers.pop();

   return user;
}

static void openfireRestApiDeleteUser(XmppTestUserInfo user)
{
   CurlPPHelper helper;
   curlpp::Easy easy;

   std::stringstream url;
   url << "http://" << TestEnvironmentConfig::defaultXmppServer() << ":" << OPENFIRE_ADMIN_PORT << "/plugins/restapi/v1/users/" << user.username;

   helper.setDefaultOptions(easy, url.str(), "DELETE", 0);

   std::list<std::string> headers;
   std::string key = std::string("Authorization: ") + TestEnvironmentConfig::openfireRestApiKey().c_str();
   headers.push_back(key);
   easy.setOpt(new curlpp::options::HttpHeader(headers));

   std::stringstream response;
   easy.setOpt(new curlpp::options::WriteStream(&response));
   easy.perform();

   if (curlpp::infos::ResponseCode::get(easy) == 200)
   {
      return;
   }
   else
   {
      std::cout << curlpp::infos::ResponseCode::get(easy) << " deleting user failed " << std::endl;
   }

   assert(user.username.length() > 0);
   sXmppAvailableUsers.push(user);
}

static XmppTestUserInfo checkoutUser()
{
   if (!TestEnvironmentConfig::autoCreateXmppUsers()) 
   {
      std::call_once(sOnce, [] { setupXmppTestUserInfos(); });

      assert( sXmppAvailableUsers.size() > 0 );
      XmppTestUserInfo user = sXmppAvailableUsers.top();
      sXmppAvailableUsers.pop();
      return user;
   }
   else 
   {
      XmppTestUserInfo user;
      std::stringstream username;
      username << "test" << resip::Random::getCryptoRandomHex(20).c_str();
      user.username = username.str();
      user.password = user.username;
      return  openfireRestApiAddUser(user);
   }
}

static XmppTestUserInfo checkoutUser(const std::string& xmppUser)
{
   if (!TestEnvironmentConfig::autoCreateXmppUsers()) 
   {
      if (TestEnvironmentConfig::useRandomXmppUsernames())
      {
         XmppTestUserInfo ui;
         std::stringstream ss;
         ss << xmppUser.c_str() << resip::Random::getRandomHex(32).c_str();
         ui.username = ss.str();
         ui.password = "boguspassword";
         return ui;
      }
      else
      {
         std::call_once(sOnce, [] { setupXmppTestUserInfos(); });

         assert(xmppUser.size() > 0);
         assert(sXmppAvailableUsers.size() > 0);
         XmppTestUserInfo user(xmppUser);

         bool found = false;
         std::priority_queue<XmppTestUserInfo> newQueue;

         while (!sXmppAvailableUsers.empty())
         {
            XmppTestUserInfo user = sXmppAvailableUsers.top();
            sXmppAvailableUsers.pop();
            if (user.username == xmppUser)
            {
               found = true;
            }
            else
            {
               newQueue.push(user);
            }
         }

         assert(found);
         sXmppAvailableUsers = newQueue;

         return user;
      }
   }
   else 
   {
      XmppTestUserInfo user;
      if (TestEnvironmentConfig::useRandomXmppUsernames()) 
      {
         std::stringstream username;
         username << xmppUser.c_str() << resip::Random::getCryptoRandomHex(20).c_str();
         user.username = username.str();
         user.password = user.username;
      }
      else 
      {
         user.username = xmppUser;
         user.password = user.username;
      }
      return  openfireRestApiAddUser(user);
   }
}

static void checkinUser(const XmppTestUserInfo& user)
{
   if (!TestEnvironmentConfig::autoCreateXmppUsers()) 
   {
      if (!TestEnvironmentConfig::useRandomXmppUsernames())
      {
         assert(user.username.length() > 0);
         sXmppAvailableUsers.push(user);
      }
   }

   else 
   {
      openfireRestApiDeleteUser(user);
   }
}

#define OPENFIRE_XMPP_PORT 5224
XmppTestAccountConfig::XmppTestAccountConfig(const std::string& name, const std::string& xmppUser)
{
   this->name = (name.c_str());
   if (xmppUser.empty())
      this->info = checkoutUser();
   else
      this->info = checkoutUser(xmppUser);

   this->authToken = "";
   this->didCheckout = true;
   this->useJsonProxy = false;
   safeCout(this->name << " is using " << this->info);

   resip::Uri server (("server:" + TestEnvironmentConfig::defaultXmppServer()).c_str());
   settings.domain = server.host().c_str();
   settings.port = OPENFIRE_XMPP_PORT; // done to be consistent with above approach with restAPI port
   settings.resource = (name + "_" + cpc::to_string(std::rand()).c_str()).c_str();
   settings.proxy = TestEnvironmentConfig::defaultXmppServerProxy();
   settings.username = this->info.username.c_str();
   settings.password = this->info.password.c_str();
   settings.priority = 0;
   settings.ignoreCertVerification = true;
   settings.enableStreamResumption = false;
   settings.sslVersion = XmppAccount::TLS_V1_2;
   settings.enableCompression = (TestEnvironmentConfig::disableXmppCompression() == false);
   settings.logTlsEncryptionKey = TestEnvironmentConfig::logXmppTlsEncryptionKey();
   //settings.enableLocalSocks5Proxy = false;
   
   resip::Data rand = resip::Random::getCryptoRandomBase64(8);
   nick = cpc::string(name.c_str()) + "_" + cpc::string(rand.c_str());
}

XmppTestAccountConfig::XmppTestAccountConfig(const XmppTestAccountConfig& config, const std::string& resource)
   : name(config.name), info(config.info), authToken(""), didCheckout(false), useJsonProxy(false), settings(config.settings)
{
   if (!resource.empty()) settings.resource = (resource + "_" + cpc::to_string(std::rand()).c_str()).c_str();
   safeCout(this->name << "/" << settings.resource << " is using " << this->info);
}

XmppTestAccountConfig::~XmppTestAccountConfig()
{
   if (this->didCheckout)
   {
      checkinUser(this->info);
   }
}

const cpc::string XmppTestAccountConfig::full() const
{
   assert(!settings.username.empty());
   assert(!settings.domain.empty());

   return settings.username + "@" + settings.domain + (settings.resource.empty() ? "" : "/" + settings.resource);
}

const cpc::string XmppTestAccountConfig::bare() const
{
   assert(!settings.username.empty());
   assert(!settings.domain.empty());

   return settings.username + "@" + settings.domain;
}

void XmppTestAccountConfig::getXmppAgentServerUrls(std::string& authHttpUrl, std::string& orchHttpUrl, std::string& jsonWsUrl)
{
   std::stringstream authBuf;
   authBuf << "http://127.0.0.1:" << XMPP_AUTH_SERVER_HTTP_PORT << "/login_v1";
   authHttpUrl = authBuf.str().c_str();
   
   std::stringstream orchBuf;
   orchBuf << "http://127.0.0.1:" << XMPP_AGENT_ORCH_SERVER_HTTP_PORT << "/jsonApi";
   // orchBuf << "http://inproc.local:" << XMPP_AGENT_ORCH_SERVER_HTTP_PORT << "/jsonApi";
   orchHttpUrl = orchBuf.str().c_str();
   
   std::stringstream jsonBuf;
   jsonBuf << "wss://127.0.0.1:" << XMPP_AGENT_WS_PORT;
   jsonWsUrl = jsonBuf.str().c_str();
}

void XmppTestAccountConfig::getWatchdogServerUrls(std::string& authHttpUrl, std::string& orchHttpUrl, std::string& jsonWsUrl)
{
   std::stringstream authBuf;
   authBuf << "http://127.0.0.1:" << XMPP_AUTH_SERVER_HTTP_PORT << "/login_v1";
   authHttpUrl = authBuf.str().c_str();
   
   std::stringstream orchBuf;
   orchBuf << "http://127.0.0.1:" << XMPP_WATCHDOG_ORCH_SERVER_HTTP_PORT << "/jsonApi";
   // orchBuf << "http://inproc.local:" << XMPP_WATCHDOG_ORCH_SERVER_HTTP_PORT << "/jsonApi";
   orchHttpUrl = orchBuf.str().c_str();
   
   std::stringstream jsonBuf;
   jsonBuf << "ws://127.0.0.1:" << XMPP_WATCHDOG_WS_PORT;
   jsonWsUrl = jsonBuf.str().c_str();
}

void XmppTestAccountConfig::getDefaultWatchdogConfig(CPCAPI2::CloudWatchdog::CloudWatchdogConfig& watchdogConfig)
{
   cpc::string xmppAgentServiceId = CPCAPI2::XmppAgent::XmppAgentManager::getServiceId();
   cpc::string pushNotificationEndpointServiceId = CPCAPI2::PushEndpoint::PushNotificationEndpointManager::getServiceId();
   CPCAPI2::CloudWatchdog::ServerAndRegion agentService;
   agentService.server = xmppAgentServiceId;
   agentService.region = XMPP_AGENT_REGION;
   CPCAPI2::CloudWatchdog::ServerAndRegion pushService;
   pushService.server = pushNotificationEndpointServiceId;
   pushService.region = XMPP_AGENT_REGION;
   watchdogConfig.serversToMonitor.push_back(agentService);
   watchdogConfig.serversToMonitor.push_back(pushService);
   watchdogConfig.monitorFrequency = 60;
}

void XmppTestAccountConfig::getDefaultCloudServices(cpc::vector<CPCAPI2::CloudConnector::ServiceDesc>& services)
{
   cpc::string xmppAgentServiceId = CPCAPI2::XmppAgent::XmppAgentManager::getServiceId();
   cpc::string pushNotificationEndpointServiceId = CPCAPI2::PushEndpoint::PushNotificationEndpointManager::getServiceId();
   CPCAPI2::CloudConnector::ServiceDesc agentService(xmppAgentServiceId, XMPP_AGENT_REGION);
   CPCAPI2::CloudConnector::ServiceDesc pushService(pushNotificationEndpointServiceId, XMPP_AGENT_REGION);
   services.push_back(agentService);
   services.push_back(pushService);
}

void XmppTestAccountConfig::setCloudServicesInfo(CPCAPI2::Phone* phone, bool watchdog, bool started, bool shutdown)
{
#if (CPCAPI2_BRAND_CLOUD_SERVICE_CONFIG_MODULE == 1)
   cpc::string xmppAgentServiceId = CPCAPI2::XmppAgent::XmppAgentManager::getServiceId();
   cpc::string pushNotificationClientServiceId = CPCAPI2::PushEndpoint::PushNotificationEndpointManager::getServiceId();
   CloudServiceConfigManager* cloudConfigMgr = CPCAPI2::CloudServiceConfig::CloudServiceConfigManager::getInterface(phone);
   
   std::string authUrl("");
   std::string orchUrl("");
   std::string jsonWsUrl("");
   
   if (watchdog)
      getWatchdogServerUrls(authUrl, orchUrl, jsonWsUrl);
   else
      getXmppAgentServerUrls(authUrl, orchUrl, jsonWsUrl);
   
   orchUrl = "http://inproc.local";

   ServerInfo xmppAgentServerInfo;
   xmppAgentServerInfo.region = XMPP_AGENT_REGION;
   xmppAgentServerInfo.uri = jsonWsUrl.c_str();
   xmppAgentServerInfo.services.push_back(xmppAgentServiceId);
   xmppAgentServerInfo.started = started;
   xmppAgentServerInfo.shutdown = shutdown;
   ServerInfo pushClientServerInfo;
   pushClientServerInfo.region = XMPP_AGENT_REGION;
   pushClientServerInfo.uri = jsonWsUrl.c_str();
   pushClientServerInfo.services.push_back(pushNotificationClientServiceId);
   pushClientServerInfo.started = started;
   pushClientServerInfo.shutdown = shutdown;
   ServiceConfigSettings serviceConfigSettings;
   serviceConfigSettings.authServerUrl = authUrl.c_str();
   serviceConfigSettings.orchestrationServerUrl = orchUrl.c_str();
   serviceConfigSettings.username = "server";
   serviceConfigSettings.password = "server";

   test::EventHandler cloudConfigEvents((watchdog ? "watchdog" : "xmppagent"), dynamic_cast<CPCAPI2::AutoTestProcessor*>(cloudConfigMgr));
   
   cloudConfigMgr->setServerInfo(serviceConfigSettings, xmppAgentServerInfo);
   {
      CloudServiceConfigHandle h;
      CPCAPI2::CloudServiceConfig::SetServerInfoResult args;
      ASSERT_TRUE(cpcExpectEvent((&cloudConfigEvents), "CloudServiceConfigHandler::onSetServerInfoSuccess", 5000, CPCAPI2::test::AlwaysTruePred(), h, args));
   }

   cloudConfigMgr->setServerInfo(serviceConfigSettings, pushClientServerInfo);
   {
      CloudServiceConfigHandle h;
      CPCAPI2::CloudServiceConfig::SetServerInfoResult args;
      ASSERT_TRUE(cpcExpectEvent((&cloudConfigEvents), "CloudServiceConfigHandler::onSetServerInfoSuccess", 5000, CPCAPI2::test::AlwaysTruePred(), h, args));
   }
#endif
}

void XmppTestAccountConfig::setupAuthServer(TestAccount& auth, bool useHTTPS)
{
#if (CPCAPI2_BRAND_AUTH_SERVER_MODULE == 1)
   CPCAPI2::AuthServer::AuthServer* authServer = CPCAPI2::AuthServer::AuthServer::getInterface(auth.phone);
   
   CPCAPI2::AuthServer::DbAccess authDb;
   authDb.initialize("authserver.db");
   authDb.flushUsers();
   authDb.addUser("user1", "1234");
   authDb.addUser("server", "server");
   CPCAPI2::AuthServer::AuthServerConfig authServerConfig;
   authServerConfig.certificateFilePath = TestEnvironmentConfig::testResourcePath() + "p256-private-key-unit-tests.p8";
   authServerConfig.numThreads = 4;
   authServerConfig.port = XMPP_AUTH_SERVER_HTTP_PORT;
   
   if( useHTTPS )
   {
      authServerConfig.httpsCertificateFilePath = TestEnvironmentConfig::testResourcePath() + "cert.pem";
      authServerConfig.httpsPrivateKeyFilePath = TestEnvironmentConfig::testResourcePath() + "privkey.pem";
      authServerConfig.httpsDiffieHellmanParamsFilePath = TestEnvironmentConfig::testResourcePath() + "dh2048.pem";
   }
   
   authServer->start(authServerConfig);
#endif
}

void XmppTestAccountConfig::setupJsonApiServer(CPCAPI2::Phone* phone, int wsPort, int httpPort)
{
#if (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
   CPCAPI2::JsonApi::JsonApiServer* jsonServer = CPCAPI2::JsonApi::JsonApiServer::getInterface(phone);
   
   JsonApi::JsonApiServerConfig jsonApiServCfg;
   jsonApiServCfg.websocketPort = wsPort;
   jsonApiServCfg.httpPort = httpPort;
   jsonApiServCfg.certificateFilePath = TestEnvironmentConfig::testResourcePath() + "p256-public-key-unit-tests.spki";
   jsonApiServCfg.wssCertificateFilePath = TestEnvironmentConfig::testResourcePath() + "cert.pem";
   jsonApiServCfg.wssPrivateKeyFilePath = TestEnvironmentConfig::testResourcePath() + "privkey.pem";
   jsonApiServCfg.wssDiffieHellmanParamsFilePath = TestEnvironmentConfig::testResourcePath() + "dh2048.pem";

   jsonServer->start(jsonApiServCfg);
#endif
}

void XmppTestAccountConfig::setupOrchServer(CPCAPI2::Phone* phone)
{
#if (CPCAPI2_BRAND_ORCHESTRATION_SERVER_MODULE == 1)
   CPCAPI2::OrchestrationServer::OrchestrationServer* orchServer = CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(phone);
   
   CPCAPI2::OrchestrationServer::OrchestrationServerConfig serverConfig;
   serverConfig.redisIp = ORCH_REDIS_IP;
   serverConfig.redisPort = ORCH_REDIS_PORT;
   
   orchServer->start(serverConfig);
#endif
}

void XmppTestAccountConfig::setupPushServer(CPCAPI2::Phone* phone)
{
#if (CPCAPI2_BRAND_PUSH_NOTIFICATION_SERVER_MODULE == 1)
   PushNotificationServiceManager* pushMgr = CPCAPI2::PushService::PushNotificationServiceManager::getInterface(phone);
   
   PushDatabaseSettings pushDatabaseSettings;
   pushDatabaseSettings.redisIp = PUSH_REDIS_IP;
   pushDatabaseSettings.redisPort = PUSH_REDIS_PORT;
   PushProviderSettings pushProviderSettings;
   pushProviderSettings.pushNetworkType = PushNetworkType_WS;
   
   pushMgr->configureDatabaseAccess(pushDatabaseSettings);
   pushMgr->configurePushProvider(pushProviderSettings);
#endif
}

void XmppTestAccountConfig::setupWatchdogServer(TestAccount& watchdog, CPCAPI2::CloudWatchdog::CloudWatchdogConfig& watchdogConfig)
{
#if (CPCAPI2_BRAND_CLOUD_WATCHDOG_SERVER_MODULE == 1)
   setupJsonApiServer(watchdog.phone, XMPP_WATCHDOG_WS_PORT, XMPP_WATCHDOG_ORCH_SERVER_HTTP_PORT);
   CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(watchdog.phone)->setJsonApiServer(watchdog.jsonApiServer);
   
   setupPushServer(watchdog.phone);
   setupOrchServer(watchdog.phone);
   
   CPCAPI2::CloudWatchdog::CloudWatchdogService* watchdogServer = CPCAPI2::CloudWatchdog::CloudWatchdogService::getInterface(watchdog.phone);
   watchdogServer->start(watchdogConfig);
#endif
}

void XmppTestAccountConfig::setupWatchdogServer(TestAccount& watchdog)
{
#if (CPCAPI2_BRAND_CLOUD_WATCHDOG_SERVER_MODULE == 1)
   CPCAPI2::CloudWatchdog::CloudWatchdogConfig watchdogConfig;
   getDefaultWatchdogConfig(watchdogConfig);
   setupWatchdogServer(watchdog, watchdogConfig);
#endif
}

void XmppTestAccountConfig::destroyWatchdogServer(TestAccount& watchdog)
{
#if (CPCAPI2_BRAND_CLOUD_WATCHDOG_SERVER_MODULE == 1)
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   
   if (watchdog.orchestrationServer)
   {
      watchdog.orchestrationServer->flushAll();
      watchdog.orchestrationServer->shutdown();
   }
   
   if (watchdog.jsonApiServer)
      watchdog.jsonApiServer->shutdown();
   
   if (watchdog.cloudWatchdogServer)
      watchdog.cloudWatchdogServer->shutdown();
   
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
#endif
}

void XmppTestAccountConfig::setupXmppAgent(XmppTestAccount& agent, JsonApi::JsonApiServerConfig& jsonApiServCfg)
{
   agent.mLocalLogger.reset(new AutoTestsLocalLogger("XMPP agent"));
   static_cast<PhoneInternal*>(agent.phone)->setLocalCallbackLoggingEnabled(agent.mLocalLogger.get(), true);
   static_cast<PhoneInternal*>(agent.phone)->setLocalFileLoggingLevel(CPCAPI2::LogLevel_Max);

#if (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
   if (jsonApiServCfg.certificateFilePath.size() == 0)
   {
      jsonApiServCfg.certificateFilePath = TestEnvironmentConfig::testResourcePath() + "p256-public-key-unit-tests.spki"; // pub key used to validate auth tokens
      jsonApiServCfg.wssCertificateFilePath = TestEnvironmentConfig::testResourcePath() + "cert.pem";
      jsonApiServCfg.wssPrivateKeyFilePath = TestEnvironmentConfig::testResourcePath() + "privkey.pem";
      jsonApiServCfg.wssDiffieHellmanParamsFilePath = TestEnvironmentConfig::testResourcePath() + "dh2048.pem";
   }
   agent.jsonApiServer->start(jsonApiServCfg);
   CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(agent.phone)->setJsonApiServer(agent.jsonApiServer);

   CPCAPI2::PushService::PushNotificationServiceManager* pushServer = CPCAPI2::PushService::PushNotificationServiceManager::getInterface(agent.phone);
   CPCAPI2::XmppAgent::XmppAgentManager::getInterface(agent.phone)->setPushNotificationManager(pushServer);
   CPCAPI2::XmppAgent::XmppAgentManager::getInterface(agent.phone)->setJsonApiServer(agent.jsonApiServer);
   CPCAPI2::XmppAgent::XmppAgentJsonApi::getInterface(agent.phone);
   CPCAPI2::PushEndpoint::PushNotificationEndpointJsonApi::getInterface(agent.phone);
   CPCAPI2::XmppAccount::XmppAccountJsonApi::getInterface(agent.phone);
   CPCAPI2::XmppChat::XmppChatJsonApi::getInterface(agent.phone);
   CPCAPI2::XmppRoster::XmppRosterJsonApi::getInterface(agent.phone);
   CPCAPI2::XmppVCard::XmppVCardJsonApi::getInterface(agent.phone);
   CPCAPI2::XmppMultiUserChat::XmppMultiUserChatJsonApi::getInterface(agent.phone);

   setupPushServer(agent.phone);
   setupOrchServer(agent.phone);
   CPCAPI2::PushEndpoint::PushNotificationEndpointManager* pushEndpoint = CPCAPI2::PushEndpoint::PushNotificationEndpointManager::getInterface(agent.phone);
   pushEndpoint->setPushNotificationService(pushServer);

   setCloudServicesInfo(agent.phone, false, true);
#endif
}

void XmppTestAccountConfig::setupXmppAgent(XmppTestAccount& agent)
{
   cpc::string certificateFilePath = TestEnvironmentConfig::testResourcePath() + "p256-public-key-unit-tests.spki"; // pub key used to validate auth tokens
   JsonApi::JsonApiServerConfig jsonApiServCfg(XMPP_AGENT_WS_PORT, XMPP_AGENT_ORCH_SERVER_HTTP_PORT, certificateFilePath);
   jsonApiServCfg.wssCertificateFilePath = TestEnvironmentConfig::testResourcePath() + "cert.pem";
   jsonApiServCfg.wssPrivateKeyFilePath = TestEnvironmentConfig::testResourcePath() + "privkey.pem";
   jsonApiServCfg.wssDiffieHellmanParamsFilePath = TestEnvironmentConfig::testResourcePath() + "dh2048.pem";
   setupXmppAgent(agent, jsonApiServCfg);
}

void XmppTestAccountConfig::destroyXmppAgent(XmppTestAccount& agent)
{
#if (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   
   if (agent.orchestrationServer)
   {
      agent.orchestrationServer->flushAll();
      agent.orchestrationServer->shutdown();
   }
   
   if (agent.jsonApiServer)
      agent.jsonApiServer->shutdown();
   
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   if (!agent.isDestroyed())
   {
      agent.destroy();
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   }
   
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
#endif
}

void XmppTestAccountConfig::setupPublicOrchServer()
{
   cpc::string xmppAgentServiceId = CPCAPI2::XmppAgent::XmppAgentManager::getServiceId();
   cpc::string pushNotificationClientServiceId = CPCAPI2::PushEndpoint::PushNotificationEndpointManager::getServiceId();
   
   resip::Data authToken;
   try
   {
      CurlPPHelper helper;
      curlpp::Easy request;
      std::string messageBody = "{ \"username\": \"jgeras\", \"password\": \"5e884898da28047151d0e56f8dc6292773603d0d6aabbdd62a11ef721d1542d8\" }"; // memory needs to hang around until after perform
      
      std::stringstream url;
      url << "http://**************:" << XMPP_AUTH_SERVER_HTTP_PORT << "/login_v1"; // "http://*************:18082/login_v1";
      
      helper.setDefaultOptions(request, url.str(), "POST", messageBody.size());
      request.setOpt(new curlpp::options::PostFields(messageBody));

      CurlPPSSL cssl(SslCipherOptions(), CurlPPSSL::E_CERT_WHATEVER_ERROR);
      request.setOpt(new curlpp::options::SslCtxFunction(cssl));
      
      std::stringstream responseBody;
      request.setOpt(new curlpp::options::WriteStream(&responseBody));
      request.perform();
      
      ASSERT_EQ(curlpp::infos::ResponseCode::get(request), 200);
      //ASSERT_EQ(messageBody, responseBody.str());
      
      std::shared_ptr<rapidjson::Document> jsonRequest(new rapidjson::Document);
      jsonRequest->Parse<0>(responseBody.str().c_str());
      ASSERT_FALSE(jsonRequest->HasParseError());
      ASSERT_TRUE(jsonRequest->HasMember("token"));
      const rapidjson::Value& tokenVal = (*jsonRequest)["token"];
      ASSERT_TRUE(tokenVal.IsString());
      ASSERT_TRUE(tokenVal.GetStringLength() > 0);
      
      resip::Data tokenData(tokenVal.GetString(), tokenVal.GetStringLength());
      bool isValid = false;
      std::map<resip::Data, resip::Data> pubClaims;
      ASSERT_EQ(CPCAPI2::AuthServer::JwtUtils::VerifyJWT((TestEnvironmentConfig::testResourcePath() + "p256-public-key-unit-tests.spki").c_str(), tokenData, isValid, &pubClaims), 0);
      ASSERT_TRUE(isValid);
      ASSERT_NE(pubClaims.size(), 0);
      authToken = tokenData;
   }
   catch (curlpp::RuntimeError& e)
   {
      std::cerr << "Runtime Error: " << e.what();
   }
   
   try
   {
      /* now hit orchestration server */
      CurlPPHelper helper;
      curlpp::Easy orchRequest;
      std::stringstream orchMessageBody;
      orchMessageBody << "{"
      << "\"moduleId\":\"OrchestrationServer\","
      << "\"functionObject\" : {"
      << "\"functionName\":\"setServerInfo\","
      << "   \"serverInfo\" : {"
      << "      \"region\" : \"NA\","
      << "      \"uri\" : \"ws://127.0.0.1:" << XMPP_AGENT_WS_PORT << "\","
      << "      \"services\" : [\"" << xmppAgentServiceId << "\", \"" << pushNotificationClientServiceId << "\"]"
      << "}"
      << "}"
      << "}";
      
      std::stringstream jsonApiUrl;
      jsonApiUrl << "http://**************:" << XMPP_AGENT_ORCH_SERVER_HTTP_PORT << "/jsonApi";
      
      
      helper.setDefaultOptions(orchRequest, jsonApiUrl.str(), "POST", orchMessageBody.str().size());
      orchRequest.setOpt(new curlpp::options::PostFields(orchMessageBody.str()));
      
      std::list<std::string> header;
      header.push_back(std::string("Authorization: bearer ") + authToken.c_str());
      orchRequest.setOpt(new curlpp::options::HttpHeader(header));

      CurlPPSSL cssl(SslCipherOptions(), CurlPPSSL::E_CERT_WHATEVER_ERROR);
      orchRequest.setOpt(new curlpp::options::SslCtxFunction(cssl));
      
      std::stringstream orchResponseBody;
      orchRequest.setOpt(new curlpp::options::WriteStream(&orchResponseBody));
      orchRequest.perform();
      
      ASSERT_EQ(curlpp::infos::ResponseCode::get(orchRequest), 200);
   }
   catch (curlpp::RuntimeError& e)
   {
      std::cerr << "Runtime Error: " << e.what();
   }
}

void XmppTestAccountConfig::generateJwt(const resip::Data& p8file, const resip::Data& userIdentity, resip::Data& jwt)
{
   std::map<resip::Data, resip::Data> pubClaims;
   pubClaims["cp_user"] = userIdentity;
   pubClaims["device_uuid"] = resip::Random::getCryptoRandomBase64(8);
   CPCAPI2::AuthServer::JwtUtils::GenerateJWT(p8file, "CPCAPI2::AuthServer", pubClaims, 86400, jwt);
}

XmppTestCloudAccount::XmppTestCloudAccount(const std::string& name) :
XmppTestAccount(name, Account_Init),
authServer(NULL),
agentServer(NULL),
clientCloudPhone(NULL),
cloudXmppAccountEnabled(false),
cloudConnectorHandle(-1),
clientXmppAccountProxyOrig(NULL),
clientXmppVCardProxyOrig(NULL),
clientXmppRosterProxyOrig(NULL),
clientXmppChatProxyOrig(NULL),
clientXmppMUCProxyOrig(NULL),
clientXmppAgentProxyOrig(NULL),
clientPushEndpointProxyOrig(NULL)
{
   config.useJsonProxy = true;
}
   
XmppTestCloudAccount::~XmppTestCloudAccount()
{
   if ((agentServer != NULL) || (authServer != NULL))
   {
      XmppTestCloudAccount::destroy();
   }
}

void XmppTestCloudAccount::enable(bool withPush)
{
   if (!authServer)
      authServer = new TestAccount("auth", Account_Init); // This test account is the authentication server
   if (!agentServer)
      agentServer = new XmppTestAccount("agent", Account_Init); // This test account is the xmpp agent, orchestration server and push notification server

   clientXmppAccountProxyOrig = accountJson; // Instance created by the unit test framework
   clientXmppVCardProxyOrig = vcardJson; // Instance created by the unit test framework
   clientXmppRosterProxyOrig = rosterJson; // Instance created by the unit test framework
   clientXmppChatProxyOrig = chatJson; // Instance created by the unit test framework
   clientXmppMUCProxyOrig = mucJson; // Instance created by the unit test framework
   clientXmppAgentProxyOrig = agentJson; // Instance created by the unit test framework
   clientPushEndpointProxyOrig = pushEndpointJson; // Instance created by the unit test framework

   assertCloudXmppAccountEnabled(*authServer, *agentServer, *this, clientCloudPhone, withPush, cloudConnectorHandle, [&](const CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent& evt)
   {
      ASSERT_EQ(CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status_Connected, evt.accountStatus);
   });

   cloudXmppAccountEnabled = true;
   
   if (withPush)
   {
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      assertEndpointPushRegistrationSuccess(*this, [&](const CPCAPI2::PushEndpoint::PushRegistrationSuccessEvent& evt)
      {
         ASSERT_FALSE(evt.endpointId.empty());
      });
      
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      assertAgentPushRegistrationSuccess(*this, [&](const CPCAPI2::XmppAgent::XmppPushRegistrationSuccessEvent& evt)
      {
         ASSERT_EQ(evt.xmppAccountHandle, handle);
         ASSERT_FALSE(evt.pushEndpointId.empty());
         ASSERT_EQ(evt.pushEndpointId, pushEndpointId);
      });
   }
}

void XmppTestCloudAccount::enableCloud()
{
   if (!authServer)
      authServer = new TestAccount("auth", Account_Init); // This test account is the authentication server
   if (!agentServer)
      agentServer = new XmppTestAccount("agent", Account_Init); // This test account is the xmpp agent, orchestration server and push notification server

   clientXmppAccountProxyOrig = accountJson; // Instance created by the unit test framework
   clientXmppVCardProxyOrig = vcardJson; // Instance created by the unit test framework
   clientXmppRosterProxyOrig = rosterJson; // Instance created by the unit test framework
   clientXmppChatProxyOrig = chatJson; // Instance created by the unit test framework
   clientXmppMUCProxyOrig = mucJson; // Instance created by the unit test framework
   clientXmppAgentProxyOrig = agentJson; // Instance created by the unit test framework
   clientPushEndpointProxyOrig = pushEndpointJson; // Instance created by the unit test framework

   assertCloudConnectedForXmpp(*authServer, *agentServer, *this, clientCloudPhone, cloudConnectorHandle, [&](const ServiceConnectionStatusEvent& evt)
   {
      ASSERT_EQ(CloudConnector::ServiceConnectionStatus_Connected, evt.connectionStatus);
   });
}

void XmppTestCloudAccount::enableXmppAccount(bool withPush)
{
   assert(authServer);
   assert(agentServer);
   assert(clientCloudPhone);

   // Replace the instances of the proxy managers created by the unit test framework
   accountJson = CPCAPI2::XmppAccount::XmppAccountManagerJsonProxy::getInterface(clientCloudPhone);
   rosterJson = CPCAPI2::XmppRoster::XmppRosterManagerJsonProxy::getInterface(clientCloudPhone);
   vcardJson = CPCAPI2::XmppVCard::XmppVCardManagerJsonProxy::getInterface(clientCloudPhone);
   chatJson = CPCAPI2::XmppChat::XmppChatManagerJsonProxy::getInterface(clientCloudPhone);
   mucJson = CPCAPI2::XmppMultiUserChat::XmppMultiUserChatManagerJsonProxy::getInterface(clientCloudPhone);
   agentJson = CPCAPI2::XmppAgent::XmppAgentManagerJsonProxy::getInterface(clientCloudPhone);
   pushEndpointJson = CPCAPI2::PushEndpoint::PushNotificationEndpointManagerJsonProxy::getInterface(clientCloudPhone);

   createAccountJson();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   createRosterJson();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   createVCardJson(); // create the handle before enabling account, else a fetched event will be triggered with an unverifiable handle

   if (withPush)
   {
      createAgentJson();

      createPushEndpointJson();
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   XmppTestAccount::enable(false, [&](const CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent& evt)
   {
      ASSERT_EQ(CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status_Connected, evt.accountStatus);
   });

   cloudXmppAccountEnabled = true;
   
   if (withPush)
   {
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      assertEndpointPushRegistrationSuccess(*this, [&](const CPCAPI2::PushEndpoint::PushRegistrationSuccessEvent& evt)
      {
         ASSERT_FALSE(evt.endpointId.empty());
      });
      
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      assertAgentPushRegistrationSuccess(*this, [&](const CPCAPI2::XmppAgent::XmppPushRegistrationSuccessEvent& evt)
      {
         ASSERT_EQ(evt.xmppAccountHandle, handle);
         ASSERT_FALSE(evt.pushEndpointId.empty());
         ASSERT_EQ(evt.pushEndpointId, pushEndpointId);
      });
   }
}

void XmppTestCloudAccount::disable()
{
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   if (cloudXmppAccountEnabled)
   {
      XmppTestAccount::disable();
   }
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
}

void XmppTestCloudAccount::destroy(bool doCleanup)
{
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   if (!isDestroyed())
   {
      if (cloudXmppAccountEnabled)
      {
         XmppTestAccount::destroy();
      }

      cpc::string xmppAgentServiceId = CPCAPI2::XmppAgent::XmppAgentManager::getServiceId();
      cpc::string pushNotificationClientServiceId = CPCAPI2::PushEndpoint::PushNotificationEndpointManager::getServiceId();
      cloudConnector->disconnectService(cloudConnectorHandle, {XMPP_AGENT_REGION, xmppAgentServiceId});
      cloudConnector->disconnectService(cloudConnectorHandle, {XMPP_AGENT_REGION, pushNotificationClientServiceId});
      assertCloudDisconnectedForXmpp(*this, cloudConnectorHandle, [&](const ServiceConnectionStatusEvent& evt)
      {
         ASSERT_EQ(CloudConnector::ServiceConnectionStatus_Disconnected, evt.connectionStatus);
      });

      destroyEventHandlers();

      cloudConnector->destroy(cloudConnectorHandle);
      assertCloudDestroyedForXmpp(*this, cloudConnectorHandle, [&](const ServiceConnectionStatusEvent& evt)
      {
         ASSERT_EQ(CloudConnector::ServiceConnectionStatus_Destroyed, evt.connectionStatus);
      });
   }
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   if (doCleanup)
   {
      cleanup();
   }
}

void XmppTestCloudAccount::logout()
{
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   if (!isDestroyed())
   {
      ASSERT_FALSE(pushEndpointId.empty()); // Did not register push endpoint
      agentJson->logout(agentHandle);
      assertAgentLogoutForXmpp(*this, agentHandle, [&](const CPCAPI2::XmppAgent::LogoutResult& evt)
      {
         ASSERT_TRUE(evt.success);
      });

      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      // CloudConnectorHandler::onLogout will not be triggered as there is no xmpp agent application
      /*
      cloudConnector->logout(cloudConnectorHandle);
      assertCloudLogoutForXmpp(*this, cloudConnectorHandle, [&](const CPCAPI2::CloudConnector::LogoutResult& evt)
      {
         ASSERT_TRUE(evt.success);
      }); */

      // we can't destroy cloud connector, because the unit test framework depends on being able
      // to access the phone used for the connection in order to process events ...
      // 
      //cloudConnector->destroy(cloudConnectorHandle);
      //assertCloudDestroyedForXmpp(*this, cloudConnectorHandle, [&](const ServiceConnectionStatusEvent& evt)
      //{
      //   ASSERT_EQ(CloudConnector::ServiceConnectionStatus_Destroyed, evt.connectionStatus);
      //});
   }
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   cleanup();
}

void XmppTestCloudAccount::logoutAccount()
{
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   if (!isDestroyed() && cloudXmppAccountEnabled)
   {
      ASSERT_FALSE(pushEndpointId.empty()); // Did not register push endpoint
      agentJson->logoutAccount(handle);
      assertAgentLogoutForXmpp(*this, agentHandle, [&](const CPCAPI2::XmppAgent::LogoutResult& evt)
      {
         ASSERT_TRUE(evt.success);
      });

      cloudXmppAccountEnabled = false;
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   }
}

void XmppTestCloudAccount::cleanup()
{
   cloudXmppAccountEnabled = false;
   setCloudConnected(false);
   accountJson = clientXmppAccountProxyOrig; // Restore the instance created by the unit test framework
   vcardJson = clientXmppVCardProxyOrig; // Restore the instance created by the unit test framework
   rosterJson = clientXmppRosterProxyOrig; // Restore the instance created by the unit test framework
   chatJson = clientXmppChatProxyOrig; // Restore the instance created by the unit test framework
   mucJson = clientXmppMUCProxyOrig; // Restore the instance created by the unit test framework
   agentJson = clientXmppAgentProxyOrig; // Restore the instance created by the unit test framework
   pushEndpointJson = clientPushEndpointProxyOrig; // Restore the instance created by the unit test framework

#if (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)   
   if (agentServer)
   {
      CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(agentServer->phone)->flushAll();
      CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(agentServer->phone)->shutdown();
      agentServer->jsonApiServer->shutdown();
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      if (!agentServer->isDestroyed())
      {
         agentServer->destroy();
         std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      }
   }
#endif
#if (CPCAPI2_BRAND_AUTH_SERVER_MODULE == 1)
   if (authServer)
   {
      authServer->authServer->shutdown();
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   }
#endif
   delete authServer;
   authServer = NULL;
   delete agentServer;
   agentServer = NULL;
   clientCloudPhone = NULL;
   clientXmppAccountProxyOrig = NULL;
   clientXmppVCardProxyOrig = NULL;
   clientXmppRosterProxyOrig = NULL;
   clientXmppChatProxyOrig = NULL;
   clientXmppMUCProxyOrig = NULL;
   clientXmppAgentProxyOrig = NULL;
   clientPushEndpointProxyOrig = NULL;
}

XmppTestAccount::XmppTestAccount(const std::string& name, TestAccountInitMode initMode, const std::string& xmppUser, CPCAPI2::Phone* p) :
TestAccountBase(TestAccountType_XMPP),
config(name, xmppUser),
initialized(false),
enabled(false),
destroyed(false),
cloudConnected(false),
phoneReleased(false),
handle(-1),
phone(p),
slave(p!=NULL),
statusHandler(NULL)
{
   switch (initMode)
   {
      case Account_NoInit:
         break;
      case Account_Enable:
         enable();
         break;
      case Account_Init:
         init();
         break;
   }

   safeCout("XmppTestAccount constructor: user: " << config.name << " xmpp user: " << config.settings.username << " account: " << handle);
}

XmppTestAccount::XmppTestAccount(const XmppTestAccount& existingAccount, const std::string& resource, TestAccountInitMode initMode) :
TestAccountBase(TestAccountType_XMPP),
config(existingAccount.config, resource),
initialized(false),
enabled(false),
destroyed(false),
cloudConnected(false),
phoneReleased(false),
handle(-1),
slave(false),
statusHandler(NULL)
{
   phone = NULL;
   switch (initMode)
   {
      case Account_NoInit:
         break;
      case Account_Enable:
         enable();
         break;
      case Account_Init:
         init();
         break;
   }
   
   safeCout("XmppTestAccount constructor 2: user: " << config.name << " xmpp user: " << config.settings.username << " account: " << handle);
}

void XmppTestAccount::init()
{
   if (initialized)
   {
      return;
   }
   initialized = true;

   if (!slave)
   {
      assert(phone == NULL);
      phone = Phone::create();
      phone->initialize(LicenseInfo(), (PhoneHandler*)NULL);
      phone->setLoggingEnabled(&AutoTestsLogger::instance(), true);
      dynamic_cast<PhoneInternal*>(phone)->setCallOnDestructFn(onPhoneRelease, this);
   }
   else
      assert(phone != NULL);

   account = XmppAccountManager::getInterface(phone);
   std::ostringstream handlerNameString;
   handlerNameString << config.name;
   if (config.settings.resource != config.name) handlerNameString << "/" << config.settings.resource;
   events = new test::EventHandler(handlerNameString.str().c_str(), dynamic_cast<AutoTestProcessor*>(account));
   handle = account->create(config.settings);
   account->setHandler(handle, (XmppAccountHandler*)0xDEADBEEF);

   // bliu: CurlPPSSL requires non-empty certFolder to enable CERT_FILESYSTEM_STORAGE
   // however AndroidSecurity only accepts either of CERT_FILESYSTEM_STORAGE or CERT_OS_SPECIFIC_STORAGE but not both
   // therefore useCertFolderOnly must be assigned as true to disable CERT_OS_SPECIFIC_STORAGE
   XmppAccount::XmppAccountSettingsInternal settingsInternal;
   settingsInternal.certFolder = TestEnvironmentConfig::testResourcePath() + "/yvr";
   settingsInternal.useCertFolderOnly = true;
   XmppAccount::XmppAccountManagerInternal::getInternalInterface(phone)->setAccountSettingsInternal(handle, settingsInternal);

   roster = XmppRosterManager::getInterface(phone);
   if (roster != NULL)
   {
      roster->setHandler(handle, (XmppRosterHandler*)0xDEADBEEF);
      rosterHandle = roster->createRoster(handle);
   }

   chat = XmppChatManager::getInterface(phone);
   CPCAPI2::RemoteSyncXmppHelper::RemoteSyncXmppHelper::getInterface(phone); // need this to get created BEFORE the call to chat->setHandler(..) so that the SDK observers get propagated
   chat->setHandler(handle, (XmppChatHandler*)0xDEADBEEF);

   fileTransferManager = XmppFileTransferManager::getInterface(phone);
   if (fileTransferManager != NULL)
   {
      fileTransferManager->setHandler(handle, (XmppFileTransferHandler*)0xDEADBEEF);
   }

   vcardManager = XmppVCardManager::getInterface(phone);
   if (vcardManager != NULL)
   {
      vcardManager->setHandler(handle, (XmppVCardHandler*)0xDEADBEEF);
      vcardHandle = vcardManager->create(handle);
   }

#if (CPCAPI2_BRAND_MESSAGESTORE_MODULE == 1)
   messageStoreManager = MessageStore::MessageStoreManager::getInterface(phone);
   messageStoreEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(messageStoreManager));
   messageStoreManager->setHandler(reinterpret_cast<MessageStoreHandler*>(0XDEADBEEF));
   messageStoreManager->applySettings( config.msSettings );
#endif

   mucManager = XmppMultiUserChatManager::getInterface(phone);
   if (mucManager != NULL)
   {
      mucStateManager = XmppMultiUserChatStateManager::getInterface(mucManager);
      mucManager->setHandler(handle, (XmppMultiUserChatHandler*)0xDEADBEEF);
   }

   imCommandManager = XmppIMCommandManager::getInterface(phone);
   if (imCommandManager != NULL)
   {
      imCommandManager->setHandler(handle, (XmppChatIMCommandHandler*)0xDEADBEEF);
      imCommandManager->setHandler(handle, (XmppMultiUserChatIMCommandHandler*)0xDEADBEEF);
   }

#if (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
   jsonApiServer = CPCAPI2::JsonApi::JsonApiServerInternal::getInternalInterface(phone);
   jsonApiServerEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(jsonApiServer));
   jsonApiServer->setHandler((CPCAPI2::JsonApi::JsonApiServerHandler*)0xDEADBEEF);
#endif
   
#if (CPCAPI2_BRAND_ORCHESTRATION_SERVER_MODULE == 1)
   orchestrationServer = CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(phone);
   orchestrationServerEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(orchestrationServer));
   orchestrationServer->setHandler((CPCAPI2::OrchestrationServer::OrchestrationServerHandler*)0xDEADBEEF);
#endif
   
#if (CPCAPI2_BRAND_CLOUD_WATCHDOG_SERVER_MODULE == 1)
   cloudWatchdogServer = CPCAPI2::CloudWatchdog::CloudWatchdogService::getInterface(phone);
   cloudWatchdogServerEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(cloudWatchdogServer));
   cloudWatchdogServer->setHandler((CPCAPI2::CloudWatchdog::CloudWatchdogHandler*)0xDEADBEEF);
#endif
   
#if (CPCAPI2_BRAND_CLOUD_SERVICE_CONFIG_MODULE == 1)
   cloudServiceConfig = CPCAPI2::CloudServiceConfig::CloudServiceConfigManager::getInterface(phone);
   cloudServiceConfigEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(cloudServiceConfig));
   cloudServiceConfig->setHandler((CloudServiceConfigHandler*)0xDEADBEEF);
#endif

#if (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
   jsonApiClient = CPCAPI2::JsonApi::JsonApiClient::getInterface(phone);
   jsonApiClientEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(jsonApiClient));
   // jsonApiClient->setHandler((CPCAPI2::JsonApi::JsonApiClientHandler*)0xDEADBEEF);
   accountJson = XmppAccountManagerJsonProxy::getInterface(phone);
   chatJson = XmppChatManagerJsonProxy::getInterface(phone);
   rosterJson = XmppRosterManagerJsonProxy::getInterface(phone);
   vcardJson = XmppVCardManagerJsonProxy::getInterface(phone);
   mucJson = XmppMultiUserChatManagerJsonProxy::getInterface(phone);
   agentJson = XmppAgentManagerJsonProxy::getInterface(phone);
   agentJsonEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(agentJson));
   if (cloudServiceConfig)
   {
      cloudServiceConfig->setHandler((CPCAPI2::CloudServiceConfig::CloudServiceConfigHandler*)0xDEADBEEF);
   }
#endif

#if (CPCAPI2_BRAND_PUSH_NOTIFICATION_CLIENT_MODULE == 1)
   pushEndpointJson = PushEndpoint::PushNotificationEndpointManagerJsonProxy::getInterface(phone);
   pushJsonEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(pushEndpointJson));
#endif

#if (CPCAPI2_BRAND_PUSH_NOTIFICATION_SERVER_MODULE == 1)
   pushServer = PushService::PushNotificationServiceManager::getInterface(phone);
   pushServerEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(pushServer));
   // pushServer->setHandler(0, (CPCAPI2::PushService::PushNotificationServiceHandler*)0xDEADBEEF);
   // PushService::PushNotificationServiceManagerInternal::getInternalInterface(phone)->setCallbackHook(SdkManager_sdkCallbackHook, this);
#endif

#if (CPCAPI2_BRAND_REMOTE_SYNC_MODULE == 1)
   remoteSync = RemoteSyncManager::getInterface(phone);
   remoteSyncEvents = new CPCAPI2::test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(remoteSync));
   remoteSyncSession = remoteSync->create();

   remoteSyncJsonProxy = CPCAPI2::RemoteSync::RemoteSyncJsonProxy::getInterface(phone);
   remoteSyncJsonProxyEvents = new CPCAPI2::test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(remoteSyncJsonProxy));

   remoteSync->setHandler(remoteSyncSession, (RemoteSyncHandler*)0xDEADBEEF);
#endif

#if (CPCAPI2_BRAND_CLOUD_CONNECTOR_MODULE == 1)
   cloudConnector = CPCAPI2::CloudConnector::CloudConnectorManager::getInterface(phone);
   cloudConnectorEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(cloudConnector));
#endif

#if (CPCAPI2_BRAND_NETWORK_CHANGE_MODULE == 1)
   network = dynamic_cast<NetworkChangeManagerInterface*>(NetworkChangeManager::getInterface(phone))->getMockImpl();
#endif

#if (CPCAPI2_BRAND_XMPP_PUSH_MODULE == 1)
   xmppPush = XmppPushManager::getInterface(phone);
   xmppPush->setHandler(handle, (XmppPushHandler*)0xDEADBEEF);
#endif

}

void XmppTestAccount::createAccountJson()
{
   if (accountJson != NULL)
   {
      if (config.useJsonProxy)
      {
         std::ostringstream handlerNameString;
         handlerNameString << config.name;
         if (config.settings.resource != config.name) handlerNameString << "/" << config.settings.resource;

         delete events;
         events = new test::EventHandler(handlerNameString.str().c_str(), dynamic_cast<AutoTestProcessor*>(accountJson));
         events->startProcessThread();

         handle = accountJson->create(config.settings);
         accountJson->setHandler(handle, (XmppAccountHandler*)0xDEADBEEF);
         
         std::this_thread::sleep_for(std::chrono::milliseconds(1000));

         chatJson->setHandler(handle, (XmppChatHandler*)0xDEADBEEF);
         std::this_thread::sleep_for(std::chrono::milliseconds(1000));

         rosterJson->setHandler(handle, (XmppRosterHandler*)0xDEADBEEF);
         std::this_thread::sleep_for(std::chrono::milliseconds(1000));

         mucJson->setHandler(handle, (XmppMultiUserChatHandler*)0xDEADBEEF);
         std::this_thread::sleep_for(std::chrono::milliseconds(1000));

         vcardJson->setHandler(handle, (XmppVCardHandler*)0xDEADBEEF);
         std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      }
   }
}

XmppVCardHandle XmppTestAccount::createVCardJson()
{
   if (vcardJson)
      vcardHandle = vcardJson->create(handle);

   return vcardHandle;
}

XmppRosterHandle XmppTestAccount::createRosterJson()
{
   if (rosterJson)
      rosterHandle = rosterJson->createRoster(handle);

   return rosterHandle;
}

XmppPushRegistrationHandle XmppTestAccount::createAgentJson()
{
   // XmppAgent should have been replaced by the cloud phone based interface before calling this function
   if (agentJson)
   {
      delete agentJsonEvents;
      agentJsonEvents = new test::EventHandler(config.name.c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(agentJson));
      agentHandle = agentJson->createXmppPushRegistration();
      agentJson->setHandler(agentHandle, (XmppAgentHandler*)0xDEADBEEF);
   }

   return agentHandle;
}


PushNotificationEndpointHandle XmppTestAccount::createPushEndpointJson()
{
   // PushEndpoint should have been replaced by the cloud phone based interface before calling this function
   if (pushEndpointJson)
   {
      delete pushJsonEvents;
      pushJsonEvents = new test::EventHandler(config.name.c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(pushEndpointJson));
      pushEndpointHandle = pushEndpointJson->createPushNotificationEndpoint();
      pushEndpointJson->setHandler(pushEndpointHandle, (PushNotificationEndpointHandler*)0xDEADBEEF);
   }

   return pushEndpointHandle;
}

XmppTestAccount::~XmppTestAccount()
{
   safeCout("XmppTestAccount destructor: user: " << config.name << " xmpp user: " << config.settings.username << " account: " << handle);
   if (!initialized)
   {
      return;
   }

   // bliu: no more event will be processed if handler is unset, such as Disconnecting, Disconnected
   //account->setHandler(handle, NULL);

   destroy();
   
   // .jza. not necessary to disable logging, and we also want to keep logging going as long as possible on shutdown
   //phone->setLoggingEnabled("", false);
   
   destroyEventHandlers();

   // don't destroy cloudConnectorEvents in destroyEventHandlers, since
   // XmppTestCloudAccount::destroy still needs to use this event handler.
#if (CPCAPI2_BRAND_CLOUD_CONNECTOR_MODULE==1)
   delete cloudConnectorEvents;
   cloudConnectorEvents = NULL;
#endif

   unsigned long lastOpenSslError = 0;
   static_cast<PhoneInternal*>(phone)->checkSdkThreadOpenSslErrors(lastOpenSslError);
   // if this asserts, it's an indication that some code has left behind uncleaned up OpenSSL errors.
   // this can be confusing when checking for OpenSSL errors in other unrelated parts of our code,
   // so we should always try to clean up OpenSSL errrors.
   EXPECT_EQ(0, lastOpenSslError);

   statusHandler = NULL;
   if (slave)
   {
      phoneReleased = true;
   }
   else
   {
      EXPECT_FALSE(phoneReleased);
      CPCAPI2::Phone::release(phone);
   }

   EXPECT_TRUE(phoneReleased);
}

void XmppTestAccount::destroyEventHandlers()
{
   delete events;
   events = NULL;
#if (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE==1)
   delete jsonApiClientEvents;
   jsonApiClientEvents = NULL;
#endif
#if (CPCAPI2_BRAND_JSON_API_SERVER_MODULE==1)
   delete jsonApiServerEvents;
   jsonApiServerEvents = NULL;
#endif
#if (CPCAPI2_BRAND_ORCHESTRATION_SERVER_MODULE == 1)
   delete orchestrationServerEvents;
   orchestrationServerEvents = NULL;
#endif
#if (CPCAPI2_BRAND_CLOUD_WATCHDOG_SERVER_MODULE==1)
   delete cloudWatchdogServerEvents;
   cloudWatchdogServerEvents = NULL;
#endif
#if (CPCAPI2_BRAND_CLOUD_SERVICE_CONFIG_MODULE == 1)
   delete cloudServiceConfigEvents;
   cloudServiceConfigEvents = NULL;
#endif
#if (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE==1)
   delete agentJsonEvents;
   agentJsonEvents = NULL;
#endif
#if (CPCAPI2_BRAND_PUSH_NOTIFICATION_CLIENT_MODULE==1)
   delete pushJsonEvents;
   pushJsonEvents = NULL;
#endif
#if (CPCAPI2_BRAND_PUSH_NOTIFICATION_SERVER_MODULE == 1)
   delete pushServerEvents;
   pushServerEvents = NULL;
#endif
#if (CPCAPI2_BRAND_MESSAGESTORE_MODULE==1)
   delete messageStoreEvents;
   messageStoreEvents = NULL;
#endif
#if (CPCAPI2_BRAND_REMOTE_SYNC_MODULE == 1)
   delete remoteSyncEvents;
   remoteSyncEvents = NULL;
   delete remoteSyncJsonProxyEvents;
   remoteSyncJsonProxyEvents = NULL;
#endif

   for (std::map<CPCAPI2::XmppFileTransfer::XmppFileTransferHandle, CPCAPI2::test::EventHandler*>::iterator i = xmppFileTransferEventHandlers.begin(); i != xmppFileTransferEventHandlers.end(); i++)
   {
      delete (i->second);
   }
   xmppFileTransferEventHandlers.clear();
}

void XmppTestAccount::createXmppFileTransferEventHandlers(std::set<CPCAPI2::XmppFileTransfer::XmppFileTransferHandle>& handlers)
{
   for (auto transferHandle : handlers)
   {
      CPCAPI2::test::EventHandler* handler = new test::EventHandler(config.name.c_str(), dynamic_cast<AutoTestProcessor*>(fileTransferManager));
      xmppFileTransferEventHandlers[transferHandle] = handler;
   }
}

CPCAPI2::test::EventHandler* XmppTestAccount::addXmppFileTransferEventHandler(CPCAPI2::XmppFileTransfer::XmppFileTransferHandle transferHandle)
{
   CPCAPI2::test::EventHandler* transferHandler = NULL;
   std::map<CPCAPI2::XmppFileTransfer::XmppFileTransferHandle, CPCAPI2::test::EventHandler*>::iterator i = xmppFileTransferEventHandlers.find(transferHandle);
   if (i == xmppFileTransferEventHandlers.end())
   {
      // fileTransferManager = CPCAPI2::XmppFileTransfer::XmppFileTransferManager::getInterface(phone);
      transferHandler = new test::EventHandler(config.name.c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(fileTransferManager));
      xmppFileTransferEventHandlers[transferHandle] = transferHandler;
      // fileTransferManager->setHandler(transferHandle, (XmppFileTransferHandler*)0xDEADBEEF);
   }
   else
   {
      transferHandler = i->second;
   }
   safeCout("XmppTestAccount::addXmppFileTransferEventHandler(): " << this << " ********** adding transfer event handler for: " << config.settings.username.c_str() << " transfer-handle: " << transferHandle << " handler: " << transferHandler << " from total-handlers: " << xmppFileTransferEventHandlers.size());
   return transferHandler;
}

CPCAPI2::test::EventHandler* XmppTestAccount::getXmppFileTransferEventHandler(CPCAPI2::XmppFileTransfer::XmppFileTransferHandle transferHandle)
{
   CPCAPI2::test::EventHandler* transferHandler = NULL;
   std::map<CPCAPI2::XmppFileTransfer::XmppFileTransferHandle, CPCAPI2::test::EventHandler*>::iterator i = xmppFileTransferEventHandlers.find(transferHandle);
   if (i != xmppFileTransferEventHandlers.end())
   {
      transferHandler = i->second;
   }
   safeCout("XmppTestAccount::getXmppFileTransferEventHandler(): " << this << " ********** getting transfer event handler for: " << config.settings.username.c_str() << " transfer-handle: " << transferHandle << " handler: " << transferHandler << " from total-handlers: " << xmppFileTransferEventHandlers.size());
   return transferHandler;
}

void XmppTestAccount::enable(bool checkPresence, std::function<void(CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent& evt)> validator, bool assertRegistrationState)
{
   init();
   if (config.useJsonProxy)
   {
      ASSERT_NE(false, cloudConnected);
      ASSERT_NE(nullptr, accountJson);
      accountJson->enable(handle);
   }
   else
   {
      ASSERT_EQ(account->enable(handle), kSuccess);
   }
   if (assertRegistrationState)
   {
      assertXmppConnecting(*this);
      assertXmppConnectedEx(*this, validator);
   }
   
   if (!TestEnvironmentConfig::disableXmppSelfPresenceCheck()) 
   {
      if (!config.useJsonProxy && checkPresence) // as-is the XMPP JSON tests don't always hook up the roster module, so avoid this check for now
      {
         XmppRosterHandle h;
         XmppRosterPresenceEvent evt;
         ASSERT_TRUE(events->expectEvent(__LINE__, "XmppRosterHandler::onSelfPresence", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      }
   }

   enabled = true;

   std::this_thread::sleep_for(std::chrono::seconds(1));
}

void XmppTestAccount::disable(bool assertRegistrationState)
{
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   if (config.useJsonProxy)
   {
      ASSERT_NE(nullptr, accountJson);
      accountJson->disable(handle);
      
      if (cloudConnected && assertRegistrationState)
      {
         assertXmppDisconnecting(*this);
         assertXmppDisconnected(*this); // No response can be expected if cloud test setup can already been destroyed
      }
   }
   else
   {
      ASSERT_EQ(account->disable(handle), kSuccess);
      if (assertRegistrationState)
      {
         assertXmppDisconnecting(*this);
         assertXmppDisconnected(*this);
      }
   }
   
   enabled = false;
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
}

void XmppTestAccount::destroy(bool checkState)
{
   if (enabled)
   {
      disable(checkState);
   }
   
   if (!destroyed && (handle != (-1)))
   {
      if (config.useJsonProxy)
      {
         ASSERT_NE(nullptr, accountJson);
         
         accountJson->destroy(handle);
         if (checkState)
         {
            if (cloudConnected)
               assertXmppDestroyed(*this); // No response can be expected if cloud test setup can already been destroyed
         }
      }
      else
      {
         ASSERT_EQ(account->destroy(handle), kSuccess);
         if (checkState)
         {
            assertXmppDestroyed(*this);
         }
      }

      destroyed = true;
      handle = (-1);
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   }

}

void XmppTestAccount::closeSessions()
{
   CurlPPHelper helper;
   curlpp::Easy easy;

   std::stringstream url;
   url << "http://" << config.settings.domain << ":" << OPENFIRE_ADMIN_PORT << "/plugins/restapi/v1/sessions/" << config.settings.username;

   helper.setDefaultOptions(easy, url.str(), "DELETE", 0);

   std::list<std::string> headers;
   std::string key = std::string("Authorization: ") + TestEnvironmentConfig::openfireRestApiKey().c_str();
   headers.push_back(key);
   easy.setOpt(new curlpp::options::HttpHeader(headers));

   std::stringstream response;
   easy.setOpt(new curlpp::options::WriteStream(&response));
   easy.perform();

   if (curlpp::infos::ResponseCode::get(easy) == 200)
   {
      return;
   }
   else
   {
      std::cout << curlpp::infos::ResponseCode::get(easy) << " closing user sessions failed " << std::endl;
   }
}



void XmppTestAccount::disconnectNetwork(bool disconnectDockerContainerNetworkIfAvailable)
{
   std::cout << "XmppTestAccount::disconnectNetwork";
   if (disconnectDockerContainerNetworkIfAvailable && TestEnvironmentConfig::dockerContainerized())
   {
      ASSERT_EQ(0, test::NetworkUtils::setDockerContainerNetworkEnabled(false));
      ASSERT_EQ(0, test::NetworkUtils::shutdownTcpSockets());
   }
   
   network->setNetworkTransport(NetworkTransport::TransportNone);
   std::set<resip::Data> ifaces;
   // set empty set
   network->setMockInterfaces(ifaces);
}

void XmppTestAccount::connectNetwork(bool connectDockerContainerNetworkIfAvailable)
{
   if (connectDockerContainerNetworkIfAvailable && TestEnvironmentConfig::dockerContainerized())
   {
      test::NetworkUtils::setDockerContainerNetworkEnabled(true);
   }

   network->setNetworkTransport(NetworkTransport::TransportWiFi);
   std::set<resip::Data> ifaces;
   ifaces.insert("********");
   network->setMockInterfaces(ifaces);
}

void XmppTestAccount::onPhoneRelease(void* p)
{
   static_cast<XmppTestAccount*>(p)->phoneReleased = true;
}

void XmppTestAccount::setAccountStatusHandler(CPCAPI2::XmppAccount::XmppAccountHandler* handler)
{
   if (statusHandler && (handler != NULL))
   {
      safeCout("WARNING: Overriding existing handler for account: " << config.settings.username << " (" << handle << ")");
   }

   statusHandler = handler;
}

void XmppTestAccount::propagateXmppAccountStatusEvent(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent& event)
{
   if (statusHandler)
   {
      statusHandler->onAccountStatusChanged(account, event);
   }
}


/********************/
/*  TestXmppEvents  */
/********************/

void TestXmppEvents::expectXmppConnecting(int line, XmppTestAccount& account, std::function<void(CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent& evt)> validator)
{
   XmppAccountHandle h;
   XmppAccountStatusChangedEvent evt;
   ASSERT_TRUE(account.events->expectEvent(line, "XmppAccountHandler::onAccountStatusChanged", 40000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(account.handle, h);
   if (evt.accountStatus == CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status_Failure)
   {
      safeCout("Connect failed with code " << evt.errorCode << ": " << (evt.errorText));
   }
   ASSERT_EQ(CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status_Connecting, evt.accountStatus);

   if (validator)
   {
      validator(evt);
   }

   account.propagateXmppAccountStatusEvent(h, evt);
}

void TestXmppEvents::expectXmppConnected(int line, XmppTestAccount& account, std::function<void(CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent& evt)> validator)
{
   XmppAccountHandle h;
   XmppAccountStatusChangedEvent evt;
   ASSERT_TRUE(account.events->expectEvent(line, "XmppAccountHandler::onAccountStatusChanged", 40000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(account.handle, h);
   if (evt.accountStatus == CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status_Failure)
   {
      safeCout("Connect failed with code " << evt.errorCode << ": " << (evt.errorText));
   }
   ASSERT_EQ(CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status_Connected, evt.accountStatus);

   if (validator)
   {
      validator(evt);
   }

   account.propagateXmppAccountStatusEvent(h, evt);
}

void TestXmppEvents::expectXmppDisconnecting(int line, XmppTestAccount& account, std::function<void(CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent& evt)> validator)
{
   XmppAccountHandle h;
   XmppAccountStatusChangedEvent evt;
   ASSERT_TRUE(account.events->expectEvent(line, "XmppAccountHandler::onAccountStatusChanged", 40000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(account.handle, h);
   ASSERT_EQ(CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status_Disconnecting, evt.accountStatus);

   if (validator)
   {
      validator(evt);
   }

   account.propagateXmppAccountStatusEvent(h, evt);
}

void TestXmppEvents::expectXmppDisconnected(int line, XmppTestAccount& account, std::function<void(CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent& evt)> validator)
{
   XmppAccountHandle h;
   XmppAccountStatusChangedEvent evt;
   ASSERT_TRUE(account.events->expectEvent(line, "XmppAccountHandler::onAccountStatusChanged", 40000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(account.handle, h);
   ASSERT_EQ(CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status_Disconnected, evt.accountStatus);

   if (validator)
   {
      validator(evt);
   }

   account.propagateXmppAccountStatusEvent(h, evt);
}

void TestXmppEvents::expectXmppDestroyed(int line, XmppTestAccount& account, std::function<void(CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent& evt)> validator)
{
   XmppAccountHandle h;
   XmppAccountStatusChangedEvent evt;
   ASSERT_TRUE(account.events->expectEvent(line, "XmppAccountHandler::onAccountStatusChanged", 40000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(account.handle, h);
   ASSERT_EQ(CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status_Destroyed, evt.accountStatus);

   if (validator)
   {
      validator(evt);
   }

   account.propagateXmppAccountStatusEvent(h, evt);
}

void TestXmppEvents::expectXmppFailure(int line, XmppTestAccount& account, std::function<void(CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent& evt)> validator)
{
   XmppAccountHandle h;
   XmppAccountStatusChangedEvent evt;
   ASSERT_TRUE(account.events->expectEvent(line, "XmppAccountHandler::onAccountStatusChanged", 40000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(account.handle, h);
   ASSERT_EQ(CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status_Failure, evt.accountStatus);

   if (validator)
   {
      validator(evt);
   }

   account.propagateXmppAccountStatusEvent(h, evt);
}

void TestXmppEvents::expectXmppRosterUpdate(int line, XmppTestAccount& account,
         std::function<void(const CPCAPI2::XmppRoster::XmppRosterUpdateEvent& evt)> validator)
{
   XmppRosterHandle h;
   XmppRosterUpdateEvent evt;
   ASSERT_TRUE(account.events->expectEvent(line, "XmppRosterHandler::onRosterUpdate", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   if (validator)
   {
      validator(evt);
   }
}

void TestXmppEvents::expectXmppSelfPresence(int line, XmppTestAccount& account,
   std::function<void(CPCAPI2::XmppRoster::XmppRosterPresenceEvent& evt)> validator)
{
   XmppRosterHandle h;
   XmppRosterPresenceEvent evt;
   ASSERT_TRUE(account.events->expectEvent(line, "XmppRosterHandler::onSelfPresence", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   if (validator)
   {
      validator(evt);
   }
}

void TestXmppEvents::expectXmppRosterPresence(int line, XmppTestAccount& account,
   std::function<void(CPCAPI2::XmppRoster::XmppRosterPresenceEvent& evt)> validator)
{
   XmppRosterHandle h;
   XmppRosterPresenceEvent evt;
   ASSERT_TRUE(account.events->expectEvent(line, "XmppRosterHandler::onRosterPresence", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   if (validator)
   {
      validator(evt);
   }
}

void TestXmppEvents::expectXmppSubscriptionRequest(int line, XmppTestAccount& account,
         std::function<void(CPCAPI2::XmppRoster::XmppRosterSubscriptionRequestEvent& evt)> validator)
{
   XmppRosterHandle h;
   XmppRosterSubscriptionRequestEvent evt;
   ASSERT_TRUE(account.events->expectEvent(line, "XmppRosterHandler::onSubscriptionRequest", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   if (validator)
   {
      validator(evt);
   }
}

void TestXmppEvents::expectXmppVCardFetched(int line, XmppTestAccount& account,
         std::function<void(CPCAPI2::XmppVCard::VCardFetchedEvent& evt)> validator)
{
   XmppVCardHandle h;
   VCardFetchedEvent evt;
   ASSERT_TRUE(account.events->expectEvent(line, "XmppVCardHandler::onVCardFetched", 30000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   if (validator)
   {
      validator(evt);
   }
}

void TestXmppEvents::expectXmppVCardOperationResult(int line, XmppTestAccount& account,
         std::function<void(CPCAPI2::XmppVCard::VCardOperationResultEvent& evt)> validator)
{
   XmppVCardHandle h;
   VCardOperationResultEvent evt;
   ASSERT_TRUE(account.events->expectEvent(line, "XmppVCardHandler::onVCardOperationResult", 30000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   if (validator)
   {
      validator(evt);
   }
}

void TestXmppEvents::expectLoginResult(int line, XmppTestAccount& agentAccount, std::function<void(CPCAPI2::JsonApi::LoginResultEvent& evt)> validator)
{
#if (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
   LoginResultEvent loginResult;
   
   auto loginEvent = std::async(std::launch::async, [&]()
   {
      NewLoginEvent args;
      JsonApiUserHandle jsonApiUser = 0;
      loginResult.success = true;
      cpc::vector<cpc::string> permissions; permissions.push_back("*");
   
      // Process the orchestrator login (over http) or agent login (over ws) attempt and associate the context with an SDK instance)
      ASSERT_TRUE(cpcExpectEvent(agentAccount.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 10000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args));
      ASSERT_NE(jsonApiUser, 0);
      
      agentAccount.jsonApiServer->setJsonApiUserContext(jsonApiUser, agentAccount.phone, permissions);
      loginResult.requestId = args.requestId;
      agentAccount.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);
   });
   
   waitFor(loginEvent);
   
   if (validator)
   {
      validator(loginResult);
   }
#endif
}

void TestXmppEvents::expectCloudConnectedForXmpp(int line, TestAccount& authAccount, XmppTestAccount& agentAccount, XmppTestAccount& clientAccount, CPCAPI2::Phone*& clientCloudPhone, CPCAPI2::CloudConnector::CloudConnectorHandle& cloudConnectorHandle, std::function<void(CPCAPI2::CloudConnector::ServiceConnectionStatusEvent& evt)> validator)
{
   cpc::string xmppAgentServiceId = CPCAPI2::XmppAgent::XmppAgentManager::getServiceId();
   cpc::string pushNotificationClientServiceId = CPCAPI2::PushEndpoint::PushNotificationEndpointManager::getServiceId();
   cpc::vector<cpc::string> permissions; permissions.push_back("*");

   // Setup the auth server
   XmppTestAccountConfig::setupAuthServer(authAccount);

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   auto authSetupEvent = std::async(std::launch::async, [&]() {});

   // Setup the xmpp agent, orchestration and push notification server
   auto agentSetupEvent = std::async(std::launch::async, [&]()
   {
      XmppTestAccountConfig::setupXmppAgent(agentAccount);
   });

   waitFor2(authSetupEvent, agentSetupEvent);

   // Client uses the SDK json interface
   clientAccount.config.useJsonProxy = true;

   std::string authUrl("");
   std::string orchUrl("");
   std::string agentUrl("");
   XmppTestAccountConfig::getXmppAgentServerUrls(authUrl, orchUrl, agentUrl);

   cloudConnectorHandle = clientAccount.cloudConnector->createCloudConnector();
   clientAccount.cloudConnector->setHandler(cloudConnectorHandle, reinterpret_cast<CloudConnectorHandler*>(0xDEADBEEF));
   CPCAPI2::CloudConnector::CloudConnectorSettings clientCloudSettings;
   clientCloudSettings.authServerUrl = authUrl.c_str();
   clientCloudSettings.orchestrationServerUrl = orchUrl.c_str();
   clientCloudSettings.regionCode = XMPP_AGENT_REGION;
   clientCloudSettings.username = "user1";
   clientCloudSettings.password = "1234";
   clientCloudSettings.ignoreCertVerification = true;
   clientAccount.cloudConnector->setConnectionSettings(cloudConnectorHandle, clientCloudSettings);
   clientAccount.cloudConnector->requestService(cloudConnectorHandle, {XMPP_AGENT_REGION, xmppAgentServiceId});
   clientAccount.cloudConnector->requestService(cloudConnectorHandle, {XMPP_AGENT_REGION, pushNotificationClientServiceId});
   clientAccount.cloudConnector->connectToServices(cloudConnectorHandle);

   auto authEvent = std::async(std::launch::async, [&]() {});

   auto agentEvent = std::async(std::launch::async, [&]()
   {
      NewLoginEvent args;
      LoginResultEvent loginResult;
      loginResult.success = true;

      // Process the orchestrator login attempt over http (associate the context with an SDK instance)
#if (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
      ASSERT_TRUE(cpcExpectEvent(agentAccount.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 20000, CPCAPI2::test::AlwaysTruePred(), clientAccount.jsonApiUserHandle, args));
      ASSERT_NE(clientAccount.jsonApiUserHandle, 0);
      agentAccount.jsonApiServer->setJsonApiUserContext(clientAccount.jsonApiUserHandle, agentAccount.phone, permissions);
      loginResult.requestId = args.requestId;
      agentAccount.jsonApiServer->sendLoginResult(clientAccount.jsonApiUserHandle, loginResult);

      // Process the agent login attempt over websocket (associate the context with an SDK instance)
      ASSERT_TRUE(cpcExpectEvent(agentAccount.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 20000, CPCAPI2::test::AlwaysTruePred(), clientAccount.jsonApiUserHandle, args));
      ASSERT_NE(clientAccount.jsonApiUserHandle, 0);
      agentAccount.jsonApiServer->setJsonApiUserContext(clientAccount.jsonApiUserHandle, agentAccount.phone, permissions);
      loginResult.requestId = args.requestId;
      agentAccount.jsonApiServer->sendLoginResult(clientAccount.jsonApiUserHandle, loginResult);
#endif
   });

   CPCAPI2::CloudConnector::CloudConnectorHandle conn;
   CPCAPI2::CloudConnector::ServiceConnectionStatusEvent args;
   auto clientEvent = std::async(std::launch::async, [&]()
   {
      {
         ASSERT_TRUE(cpcExpectEvent(clientAccount.cloudConnectorEvents, "CloudConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(CloudConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
      }

      {
         ASSERT_TRUE(cpcExpectEvent(clientAccount.cloudConnectorEvents, "CloudConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(CloudConnector::ServiceConnectionStatus_Authenticating, args.connectionStatus);
      }

      {
         ASSERT_TRUE(cpcExpectEvent(clientAccount.cloudConnectorEvents, "CloudConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(CloudConnector::ServiceConnectionStatus_Connected, args.connectionStatus);
      }

      clientCloudPhone = clientAccount.cloudConnector->getPhone(cloudConnectorHandle, {XMPP_AGENT_REGION, xmppAgentServiceId});
      ASSERT_FALSE(clientCloudPhone == NULL);
      clientAccount.setCloudConnected(true);
   });

   waitFor3(authEvent, agentEvent, clientEvent);

   if (validator)
   {
      validator(args);
   }
}

void TestXmppEvents::expectCloudDisconnectedForXmpp(int line, XmppTestAccount& clientAccount, CPCAPI2::CloudConnector::CloudConnectorHandle cloudConnectorHandle, std::function<void(CPCAPI2::CloudConnector::ServiceConnectionStatusEvent& evt)> validator)
{
   CPCAPI2::CloudConnector::ServiceConnectionStatusEvent args;
   
   auto cloudEvent = std::async(std::launch::async, [&]()
   {
      {
         ASSERT_TRUE(cpcExpectEvent(clientAccount.cloudConnectorEvents, "CloudConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), cloudConnectorHandle, args));
         ASSERT_EQ(CloudConnector::ServiceConnectionStatus_Disconnecting, args.connectionStatus);
      }

      {
         ASSERT_TRUE(cpcExpectEvent(clientAccount.cloudConnectorEvents, "CloudConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), cloudConnectorHandle, args));
         ASSERT_EQ(CloudConnector::ServiceConnectionStatus_Disconnected, args.connectionStatus);
      }
      
      clientAccount.setCloudConnected(false);
   });
   
   waitFor(cloudEvent);
   
   if (validator)
   {
      validator(args);
   }
}

void TestXmppEvents::expectCloudDestroyedForXmpp(int line, XmppTestAccount& clientAccount, CPCAPI2::CloudConnector::CloudConnectorHandle cloudConnectorHandle, std::function<void(CPCAPI2::CloudConnector::ServiceConnectionStatusEvent& evt)> validator)
{
   CPCAPI2::CloudConnector::ServiceConnectionStatusEvent args;

   auto cloudEvent = std::async(std::launch::async, [&]()
   {
      {
         ASSERT_TRUE(cpcExpectEvent(clientAccount.cloudConnectorEvents, "CloudConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), cloudConnectorHandle, args));

         switch (args.connectionStatus)
         {
            case CloudConnector::ServiceConnectionStatus_Disconnecting:
            {
               ASSERT_TRUE(cpcExpectEvent(clientAccount.cloudConnectorEvents, "CloudConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), cloudConnectorHandle, args));

               switch (args.connectionStatus)
               {
                  case CloudConnector::ServiceConnectionStatus_Disconnected:
                  {
                     ASSERT_TRUE(cpcExpectEvent(clientAccount.cloudConnectorEvents, "CloudConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), cloudConnectorHandle, args));
                     ASSERT_EQ(CloudConnector::ServiceConnectionStatus_Destroyed, args.connectionStatus);
                     break;
                  }
                  default:
                  {
                     ASSERT_EQ(CloudConnector::ServiceConnectionStatus_Destroyed, args.connectionStatus);
                     break;
                  }
               }
               break;
            }
            case CloudConnector::ServiceConnectionStatus_Disconnected:
            {
               ASSERT_TRUE(cpcExpectEvent(clientAccount.cloudConnectorEvents, "CloudConnectorHandler::onServiceConnectionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), cloudConnectorHandle, args));
               ASSERT_EQ(CloudConnector::ServiceConnectionStatus_Destroyed, args.connectionStatus);
               break;
            }
            default:
            {
               ASSERT_EQ(CloudConnector::ServiceConnectionStatus_Destroyed, args.connectionStatus);
               break;
            }
         }

         clientAccount.setCloudConnected(false);
      }
   });

   waitFor(cloudEvent);

   if (validator)
   {
      validator(args);
   }
}

void TestXmppEvents::expectCloudLogoutForXmpp(int line, XmppTestAccount& clientAccount, CPCAPI2::CloudConnector::CloudConnectorHandle cloudConnectorHandle, std::function<void(CPCAPI2::CloudConnector::LogoutResult& evt)> validator)
{
   CPCAPI2::CloudConnector::LogoutResult args;

   auto cloudEvent = std::async(std::launch::async, [&]()
   {
      {
         ASSERT_TRUE(cpcExpectEvent(clientAccount.cloudConnectorEvents, "CloudConnectorHandler::onLogout", 5000, CPCAPI2::test::AlwaysTruePred(), cloudConnectorHandle, args));
         ASSERT_TRUE(args.success);
      }
   });

   waitFor(cloudEvent);

   if (validator)
   {
      validator(args);
   }
}

void TestXmppEvents::expectCloudXmppAccountEnabled(int line, TestAccount& authAccount, XmppTestAccount& agentAccount, XmppTestAccount& clientAccount, CPCAPI2::Phone*& clientCloudPhone, bool withPush,  CPCAPI2::CloudConnector::CloudConnectorHandle& cloudConnectorHandle, std::function<void(CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent& evt)> validator)
{
   auto cloudEvent = std::async(std::launch::async, [&]()
   {
      assertCloudConnectedForXmpp(authAccount, agentAccount, clientAccount, clientCloudPhone, cloudConnectorHandle, [&](const ServiceConnectionStatusEvent& evt)
      {
         ASSERT_EQ(CloudConnector::ServiceConnectionStatus_Connected, evt.connectionStatus);
      });
   });

   waitFor(cloudEvent);
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   CPCAPI2::XmppAccount::XmppAccountManagerJsonProxy* clientXmppAccountProxy = CPCAPI2::XmppAccount::XmppAccountManagerJsonProxy::getInterface(clientCloudPhone);
   CPCAPI2::XmppRoster::XmppRosterManagerJsonProxy* clientXmppRosterProxy = CPCAPI2::XmppRoster::XmppRosterManagerJsonProxy::getInterface(clientCloudPhone);
   CPCAPI2::XmppVCard::XmppVCardManagerJsonProxy* clientXmppVCardProxy = CPCAPI2::XmppVCard::XmppVCardManagerJsonProxy::getInterface(clientCloudPhone);
   CPCAPI2::XmppChat::XmppChatManagerJsonProxy* clientXmppChatProxy = CPCAPI2::XmppChat::XmppChatManagerJsonProxy::getInterface(clientCloudPhone);
   CPCAPI2::XmppMultiUserChat::XmppMultiUserChatManagerJsonProxy* clientXmppMUCProxy = CPCAPI2::XmppMultiUserChat::XmppMultiUserChatManagerJsonProxy::getInterface(clientCloudPhone);
   CPCAPI2::XmppAgent::XmppAgentManagerJsonProxy* clientXmppAgentProxy = CPCAPI2::XmppAgent::XmppAgentManagerJsonProxy::getInterface(clientCloudPhone);
   CPCAPI2::PushEndpoint::PushNotificationEndpointManagerJsonProxy* clientPushEndpointProxy = CPCAPI2::PushEndpoint::PushNotificationEndpointManagerJsonProxy::getInterface(clientCloudPhone);

   clientAccount.accountJson = clientXmppAccountProxy; // replaces the instance of XmppAccountManagerJsonProxy created by the unit test framework
   clientAccount.rosterJson = clientXmppRosterProxy; // replaces the instance of XmppRosterManagerJsonProxy created by the unit test framework
   clientAccount.vcardJson = clientXmppVCardProxy; // replaces the instance of XmppVCardManagerJsonProxy created by the unit test framework
   clientAccount.chatJson = clientXmppChatProxy; // replaces the instance of XmppChatManagerJsonProxy created by the unit test framework
   clientAccount.mucJson = clientXmppMUCProxy; // replaces the instance of XmppMultiUserChatManagerJsonProxy created by the unit test framework
   clientAccount.agentJson = clientXmppAgentProxy; // replaces the instance of XmppAgentManagerJsonProxy created by the unit test framework
   clientAccount.pushEndpointJson = clientPushEndpointProxy; // replaces the instance of XmppAgentManagerJsonProxy created by the unit test framework

   clientAccount.createAccountJson();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   clientAccount.createRosterJson();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   clientAccount.createVCardJson(); // create the handle before enabling account, else a fetched event will be triggered with an unverifiable handle
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   if (withPush)
   {
      clientAccount.createAgentJson();
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      clientAccount.createPushEndpointJson();
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   }

   clientAccount.enable(false, validator);
}

void TestXmppEvents::expectAgentLogoutForXmpp(int line, XmppTestAccount& clientAccount, CPCAPI2::XmppAgent::XmppPushRegistrationHandle pushRegistrationHandle, std::function<void(CPCAPI2::XmppAgent::LogoutResult& evt)> validator)
{
   CPCAPI2::XmppAgent::LogoutResult args;

   auto logoutEvent = std::async(std::launch::async, [&]()
   {
      {
         ASSERT_FALSE(clientAccount.pushEndpointId.empty()); // Did not register push endpoint
         ASSERT_TRUE(cpcExpectEvent(clientAccount.agentJsonEvents, "XmppAgentHandler::onLogout", 40000, CPCAPI2::test::AlwaysTruePred(), pushRegistrationHandle, args));
         ASSERT_TRUE(args.success);
      }
   });

   waitFor(logoutEvent);

   if (validator)
   {
      validator(args);
   }
}

void TestXmppEvents::expectEndpointPushRegistrationSuccess(int line, XmppTestAccount& clientAccount, std::function<void(CPCAPI2::PushEndpoint::PushRegistrationSuccessEvent& evt)> validator)
{
   PushRegistrationSuccessEvent args;
   auto registrationSuccess = std::async(std::launch::async, [&]()
   {
      PushEndpoint::PushNotificationRegistrationInfo regInfo;
      regInfo.pushNetworkType = PushEndpoint::PushNetworkType_WS;
      regInfo.deviceToken = "device_token";
      clientAccount.pushEndpointJson->registerForPushNotifications(clientAccount.pushEndpointHandle, regInfo);
   
      PushNotificationEndpointHandle h;
      ASSERT_TRUE(cpcExpectEvent(clientAccount.pushJsonEvents, "PushNotificationEndpointHandler::onPushRegistrationSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, args));
      ASSERT_FALSE(args.endpointId.empty());
      clientAccount.pushEndpointId = args.endpointId;
   });
   
   waitFor(registrationSuccess);
   
   if (validator)
   {
      validator(args);
   }
}

void TestXmppEvents::expectAgentPushRegistrationSuccess(int line, XmppTestAccount& clientAccount, std::function<void(CPCAPI2::XmppAgent::XmppPushRegistrationSuccessEvent& evt)> validator)
{
   XmppAgent::XmppPushRegistrationSuccessEvent args;
   auto registrationSuccess = std::async(std::launch::async, [&]()
   {
      XmppAgent::XmppPushRegistrationInfo info;
      info.jsonUserHandle = clientAccount.jsonApiUserHandle;
      info.pushNotificationDev = clientAccount.pushEndpointId;
      info.pushServerUrl = "localsdk";
      info.xmppAccountHandle = clientAccount.handle;
      clientAccount.agentJson->registerForXmppPushNotifications(clientAccount.agentHandle, info);

      XmppAgent::XmppPushRegistrationHandle h;
      ASSERT_TRUE(cpcExpectEvent(clientAccount.agentJsonEvents, "XmppAgentHandler::onPushRegistrationSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, args));
      ASSERT_EQ(args.xmppAccountHandle, clientAccount.handle);
      ASSERT_EQ(h, clientAccount.agentHandle);
      ASSERT_EQ(args.pushEndpointId, clientAccount.pushEndpointId);
   });
   
   waitFor(registrationSuccess);
   
   if (validator)
   {
      validator(args);
   }
}

void TestXmppEvents::expectJsonClientLoginSuccess(int line, XmppTestAccount& clientAccount, XmppTestAccount& agentAccount, std::function<void(CPCAPI2::JsonApi::LoginResultEvent& evt)> validator)
{
   // Presumes account and agent have already been initialized and configured

   JsonApi::LoginResultEvent args;
   auto loginSuccess = std::async(std::launch::async, [&]()
   {
      // Establish connection to the agent using the user account
      JsonApiClientSettings jsonApiClientSettings;
      jsonApiClientSettings.serverUri = "wss://localhost:9003";
      jsonApiClientSettings.ignoreCertVerification = true;
      clientAccount.jsonApiClient->configureDefaultSettings(jsonApiClientSettings);
      clientAccount.jsonApiClient->enable();

      {
         JsonApiLoginHandle h;
         StatusChangedEvent statusChangedEvt;
         cpcExpectEvent(clientAccount.jsonApiClientEvents, "JsonApiClientHandler::onStatusChanged", 10000, CPCAPI2::test::AlwaysTruePred(), h, statusChangedEvt);
         ASSERT_EQ(statusChangedEvt.status, StatusChangedEvent::Status_Connecting);
         cpcExpectEvent(clientAccount.jsonApiClientEvents, "JsonApiClientHandler::onStatusChanged", 10000, CPCAPI2::test::AlwaysTruePred(), h, statusChangedEvt);
         ASSERT_EQ(statusChangedEvt.status, StatusChangedEvent::Status_Connected);
         clientAccount.setCloudConnected(true);
      }

      // Initiate json client account login
      resip::Data accountJwt;
      if (clientAccount.config.authToken.size() == 0)
      {
         XmppTestAccountConfig::generateJwt((TestEnvironmentConfig::testResourcePath() + "p256-private-key-unit-tests.p8").c_str(), clientAccount.config.name.c_str(), accountJwt);
         clientAccount.config.authToken = accountJwt.c_str();
      }
      else
      {
         accountJwt = clientAccount.config.authToken.c_str();
      }
      clientAccount.jsonApiClient->login(accountJwt.c_str());

      {
         JsonApiUserHandle jsonApiUser;
         NewLoginEvent newLoginEvent;

         // Agent has to process the login attempt (associate the context with an SDK instance)
         cpcExpectEvent(agentAccount.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 5000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, newLoginEvent);
         ASSERT_NE(newLoginEvent.authToken.size(), 0);
         clientAccount.jsonApiUserHandle = jsonApiUser;
         cpc::vector<cpc::string> permissions; permissions.push_back("*");
         agentAccount.jsonApiServer->setJsonApiUserContext(jsonApiUser, agentAccount.phone, permissions);
         LoginResultEvent loginResultEvent;
         loginResultEvent.success = true;
         agentAccount.jsonApiServer->sendLoginResult(jsonApiUser, loginResultEvent);
      }

      {
         // Account then gets the result
         JsonApiLoginHandle h;
         cpcExpectEvent(clientAccount.jsonApiClientEvents, "JsonApiClientHandler::onLoginResult", 50000, CPCAPI2::test::AlwaysTruePred(), h, args);
      }
   });

   waitFor(loginSuccess);

   if (validator)
   {
      validator(args);
   }
}

#endif
