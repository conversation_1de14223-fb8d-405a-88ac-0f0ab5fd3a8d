//
//  screenshare_utils.h
//

#ifndef screenshare_utils_h
#define screenshare_utils_h

#include <stdio.h>
#include <unordered_set>

#include <webrtc/base/stream.h>
#include <webrtc/video_engine/vie_encoder.h>
#include <webrtc/modules/desktop_capture/desktop_capturer.h>

namespace CPCAPI2
{

namespace test
{

// used for unit testing screen capture dimensions
class FakeScreenCapturer : public webrtc::DesktopCapturer
{
public:
   FakeScreenCapturer(int _width, int _height) : width(_width), height(_height) {}

   void Start(Callback* callback) override { callback_ = callback; }

   void CaptureFrame() override
   {
//auto start = std::chrono::high_resolution_clock::now();
      
//safeCout("Creating " << width << "x" << height << " frame");
      std::unique_ptr<webrtc::DesktopFrame> desktopFrame = std::unique_ptr<webrtc::BasicDesktopFrame>(new webrtc::BasicDesktopFrame(webrtc::DesktopSize(width, height)));
      
      unsigned int stride = (width * bpp) / 8;
      rtc::MemoryStream* image = CreateBGRASample(width, height);
      desktopFrame->CopyPixelsFrom(reinterpret_cast<uint8_t*>(image->GetBuffer()), stride, webrtc::DesktopRect::MakeXYWH(0, 0, width, height));
      delete image;
//auto end = std::chrono::high_resolution_clock::now();
//std::chrono::duration<double> elapsed_seconds = end - start;
//safeCout("Created frame in " << elapsed_seconds.count() << "s");

      callback_->OnCaptureResult(Result::SUCCESS, std::move(desktopFrame));
      
//end = std::chrono::high_resolution_clock::now();
//elapsed_seconds = end - start;
//safeCout("Frame created and captured in " << elapsed_seconds.count() << "s");
   }

   void SetNextFrame(std::unique_ptr<webrtc::DesktopFrame> next_frame)
   {
      assert(0);
   }

   bool IsOccluded(const webrtc::DesktopVector& pos) override { return is_occluded_; }

   void set_is_occluded(bool value) { is_occluded_ = value; }

private:
   Callback* callback_ = nullptr;

   bool is_occluded_ = false;
   const int bpp = webrtc::DesktopFrame::kBytesPerPixel * 8;
   int width, height;

   rtc::MemoryStream* CreateBGRASample(uint32_t width, uint32_t height)
   {
      assert(bpp == 32);

      rtc::MemoryStream* ms(new rtc::MemoryStream);
      if (!ms->ReserveSize(width * height * bpp / 8))
      {
         delete ms;
         return NULL;
      }
           
      const uint8_t Avalue = 255;

      const uint8_t PurpleR = 218;
      const uint8_t PurpleG = 33;
      const uint8_t PurpleB = 143;
      const uint8_t YellowR = 250;
      const uint8_t YellowG = 250;
      const uint8_t YellowB = 33;
      const uint8_t GreenR = 33;
      const uint8_t GreenG = 218;
      const uint8_t GreenB = 33;
      
      uint8_t backgroundPixel[4] = { PurpleB, PurpleG, PurpleR, Avalue };
      uint8_t borderPixel[4] = { GreenB, GreenG, GreenR, Avalue };
      uint8_t centerPixel[4] = { YellowB, YellowG, YellowR, Avalue };

      const uint32_t LineWidth = std::max(2, (int)((width + height) / 500));

      for (uint32_t y = 0; y < height; y++)
      {
         for (uint32_t x = 0; x < width; x++)
         {
            uint8_t *pixel = backgroundPixel;

            if (x < LineWidth || x >= width-LineWidth || y < LineWidth || y >= height-LineWidth)
            {
               pixel = borderPixel;
            }

            if (abs((int)((width/2)-x)) < LineWidth && abs((int)((height/2)-y)) < LineWidth)
            {
               pixel = centerPixel;
            }

            ms->Write(pixel, 4, NULL, NULL);
         }
      }
      return ms;
   }
};

}
}


#endif /* screenshare_utils_h */
