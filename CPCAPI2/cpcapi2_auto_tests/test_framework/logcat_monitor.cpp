#include "logcat_monitor.h"

#include "cpcapi2_test_fixture.h"

#include <mutex>
#include <cpcapi2.h>

using namespace CPCAPI2::Logcat;
using namespace CPCAPI2::test;

static LogcatMonitorHolder* gInstance;
static std::once_flag gInstanceFlag;

void LogcatMonitorHolder::initInstance()
{
   gInstance = new LogcatMonitorHolder();
}

LogcatMonitorHolder* LogcatMonitorHolder::getInstance()
{
   std::call_once(gInstanceFlag, &LogcatMonitorHolder::initInstance);
   return gInstance;
}

LogcatMonitorHolder::LogcatMonitorHolder() :
   mInstance(LogcatMonitor::getInterface()),
   mEvents("logcatEvents", dynamic_cast<CPCAPI2::AutoTestProcessor*>(mInstance))
{
   mInstance->setHandler((Logcat::LogcatHandler*) 0xDEADBEEF);

   LogcatMonitorSettings settings;
   
   // run the following command on your host computer (the computer that is connected to the
   // android device via USB):
   // mkfifo logcatpipe
   // nc -k -l 6000 -vv < logcatpipe | /Path/to/Android/adb logcat > logcatpipe
   // logcatServerPort should then be 6000 and logcatServerIp should then be the IP address of your computer
   settings.ip = TestEnvironmentConfig::logcatServerIp();
   settings.port = TestEnvironmentConfig::logcatServerPort();
   mInstance->configureSettings(settings);
   
   mEvents.startProcessThread();
   mInstance->startMonitoring();
   
   Logcat::LogcatConnectedEvent event;
   int dummyHandle;
   bool b = (cpcExpectEvent((&mEvents), "LogcatHandler::onLogcatConnected", 5000, CPCAPI2::test::AlwaysTruePred(), dummyHandle, event));
   EXPECT_TRUE(b);
   std::cout << "Got " << b << " for LogcatHandler::onLogcatConnected" << std::endl;
}
