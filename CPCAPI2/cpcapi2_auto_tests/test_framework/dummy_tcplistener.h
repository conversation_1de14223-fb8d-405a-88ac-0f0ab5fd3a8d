#pragma once

#include <atomic>
#include <future>

// for socket member variable
#ifdef WIN32
#include <winsock2.h>
#include <ws2tcpip.h>
#include <wspiapi.h>
#else
#include <sys/socket.h>
#include <netinet/in.h>
#endif

namespace CPCAPI2
{
namespace test
{


// accepts inbound TCP connections but does not
// respond to any higher layer (e.g. SIP) traffic
class DummyTcpListener
{
public:
   struct Config
   {
      int port = 5060;
      bool tryOtherPorts = false;
   };

   DummyTcpListener(const Config& config);
   ~DummyTcpListener();

   // if successful returns the TCP port listening on; else returns -1
   int start();
   void stop();

private:
   std::atomic<bool> mServerShouldQuit = false;
   std::future<void> mListenerThread;

   Config mConfig;

   using socket_t = decltype(socket(0, 0, 0));
   socket_t mListenFd;

};

}
}