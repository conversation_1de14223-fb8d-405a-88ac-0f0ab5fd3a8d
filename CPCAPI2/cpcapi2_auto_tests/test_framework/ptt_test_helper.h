#pragma once
#ifndef PTT_TEST_HELPER_H
#define PTT_TEST_HELPER_H

#include "brand_branded.h"
#include <cpcapi2.h>

#include "../cpcapi2_test_fixture.h"
#include "impl/ptt/PushToTalkTypesInternal.h"
#include "impl/ptt/PushToTalkManagerInternal.h"

#define EVENT_VALIDATOR(TYPE) std::function<void(const TYPE& evt)>

class PttTestHelper
{

public:

#if !defined(ANDROID)
   static void getCloudServerUrls_ptt(std::string& authUrl, std::string& orchUrl, std::string& agentUrl);
   static void runWanServer(
      std::shared_ptr<TestAccount>& authServer,
      std::shared_ptr<TestAccount>& confServer,
      CPCAPI2::ConferenceBridge::ConferenceDetailsResult& rootConfDetails,
      std::atomic_bool& loginComplete,
      std::atomic_bool& rootConfDetailsHandlerActive,
      resip::Condition& rootDetailsReady);
   static void setupPttServer(TestAccount& maia);
   static void setupAuthServer_ptt(TestAccount& max);
   static CPCAPI2::VideoStreaming::VideoStreamingServerConfig getVideoStreamingServerConfig();
   static CPCAPI2::JsonApi::JsonApiServerConfig getPttServerConfig();
#endif // !ANDROID
   
   static CPCAPI2::PushToTalk::PushToTalkSessionHandle createPttRecipients(TestAccount& caller, CPCAPI2::PushToTalk::PushToTalkServiceHandle service, std::vector<TestAccount*>& recipients, int recipientCount);
   static void startupPtt(TestAccount& alice, TestAccount& bob, CPCAPI2::PushToTalk::PushToTalkManager*& alicePttManager, CPCAPI2::PushToTalk::PushToTalkManager*& bobPttManager,
      CPCAPI2::PushToTalk::PushToTalkSettingsInternal& aliceInternalSettings, CPCAPI2::PushToTalk::PushToTalkServiceSettings& aliceServiceSettings,
      CPCAPI2::PushToTalk::PushToTalkSettingsInternal& bobInternalSettings, CPCAPI2::PushToTalk::PushToTalkServiceSettings& bobServiceSettings);
   static void call(
      CPCAPI2::PushToTalk::PushToTalkSettingsInternal& aliceInternalSettings, CPCAPI2::PushToTalk::PushToTalkServiceSettings& aliceServiceSettings,
      CPCAPI2::PushToTalk::PushToTalkSettingsInternal& bobInternalSettings, CPCAPI2::PushToTalk::PushToTalkServiceSettings& bobServiceSettings,
      int callCount = 3);
   static void call_server_based(
      TestAccount& alice, CPCAPI2::PushToTalk::PushToTalkServiceHandle alicePttService, TestAccount& bob, CPCAPI2::PushToTalk::PushToTalkServiceHandle bobPttService, std::string channel = "channel01");
   static void call_server_based(
      CPCAPI2::PushToTalk::PushToTalkSettingsInternal& aliceInternalSettings, CPCAPI2::PushToTalk::PushToTalkServiceSettings& aliceServiceSettings,
      CPCAPI2::PushToTalk::PushToTalkSettingsInternal& bobInternalSettings, CPCAPI2::PushToTalk::PushToTalkServiceSettings& bobServiceSettings,
      int callCount = 3);
   static void callOneToOne(
      CPCAPI2::PushToTalk::PushToTalkSettingsInternal& aliceInternalSettings, CPCAPI2::PushToTalk::PushToTalkServiceSettings& aliceServiceSettings,
      CPCAPI2::PushToTalk::PushToTalkSettingsInternal& bobInternalSettings, CPCAPI2::PushToTalk::PushToTalkServiceSettings& bobServiceSettings,
      CPCAPI2::PushToTalk::PushToTalkSettingsInternal& maxInternalSettings, CPCAPI2::PushToTalk::PushToTalkServiceSettings& maxServiceSettings,
      const CPCAPI2::ConnectionPreferences& connectionPrefs = CPCAPI2::ConnectionPreferences());
   static void callOneToOne_server_based(
      CPCAPI2::PushToTalk::PushToTalkSettingsInternal& aliceInternalSettings, CPCAPI2::PushToTalk::PushToTalkServiceSettings& aliceServiceSettings,
      CPCAPI2::PushToTalk::PushToTalkSettingsInternal& bobInternalSettings, CPCAPI2::PushToTalk::PushToTalkServiceSettings& bobServiceSettings,
      CPCAPI2::PushToTalk::PushToTalkSettingsInternal& maxInternalSettings, CPCAPI2::PushToTalk::PushToTalkServiceSettings& maxServiceSettings);
   static void callOneToOneMediaInactive_server_based(
      CPCAPI2::PushToTalk::PushToTalkSettingsInternal& aliceInternalSettings, CPCAPI2::PushToTalk::PushToTalkServiceSettings& aliceServiceSettings,
      CPCAPI2::PushToTalk::PushToTalkSettingsInternal& bobInternalSettings, CPCAPI2::PushToTalk::PushToTalkServiceSettings& bobServiceSettings,
      CPCAPI2::PushToTalk::PushToTalkSettingsInternal& maxInternalSettings, CPCAPI2::PushToTalk::PushToTalkServiceSettings& maxServiceSettings);
   static void callOneToMany_server_based(
      CPCAPI2::PushToTalk::PushToTalkSettingsInternal& aliceInternalSettings, CPCAPI2::PushToTalk::PushToTalkServiceSettings& aliceServiceSettings,
      CPCAPI2::PushToTalk::PushToTalkSettingsInternal& bobInternalSettings, CPCAPI2::PushToTalk::PushToTalkServiceSettings& bobServiceSettings,
      CPCAPI2::PushToTalk::PushToTalkSettingsInternal& adamInternalSettings, CPCAPI2::PushToTalk::PushToTalkServiceSettings& adamServiceSettings,
      CPCAPI2::PushToTalk::PushToTalkSettingsInternal& tasiaInternalSettings, CPCAPI2::PushToTalk::PushToTalkServiceSettings& tasiaServiceSettings);
   static void callOneToMany_server_based_mute_unmute(
      CPCAPI2::PushToTalk::PushToTalkSettingsInternal& aliceInternalSettings, CPCAPI2::PushToTalk::PushToTalkServiceSettings& aliceServiceSettings,
      CPCAPI2::PushToTalk::PushToTalkSettingsInternal& bobInternalSettings, CPCAPI2::PushToTalk::PushToTalkServiceSettings& bobServiceSettings,
      CPCAPI2::PushToTalk::PushToTalkSettingsInternal& adamInternalSettings, CPCAPI2::PushToTalk::PushToTalkServiceSettings& adamServiceSettings,
      CPCAPI2::PushToTalk::PushToTalkSettingsInternal& tasiaInternalSettings, CPCAPI2::PushToTalk::PushToTalkServiceSettings& tasiaServiceSettings);
   static void callChannelOverride(
      CPCAPI2::PushToTalk::PushToTalkSettingsInternal& aliceInternalSettings, CPCAPI2::PushToTalk::PushToTalkServiceSettings& aliceServiceSettings,
      CPCAPI2::PushToTalk::PushToTalkSettingsInternal& bobInternalSettings, CPCAPI2::PushToTalk::PushToTalkServiceSettings& bobServiceSettings,
      CPCAPI2::PushToTalk::PushToTalkSettingsInternal& maxInternalSettings, CPCAPI2::PushToTalk::PushToTalkServiceSettings& maxServiceSettings);
   static void callReject(
      CPCAPI2::PushToTalk::PushToTalkSettingsInternal& aliceInternalSettings, CPCAPI2::PushToTalk::PushToTalkServiceSettings& aliceServiceSettings,
      CPCAPI2::PushToTalk::PushToTalkSettingsInternal& bobInternalSettings, CPCAPI2::PushToTalk::PushToTalkServiceSettings& bobServiceSettings);
   static void callIncomingClientConnectionTimeout(
      CPCAPI2::PushToTalk::PushToTalkSettingsInternal& aliceInternalSettings, CPCAPI2::PushToTalk::PushToTalkServiceSettings& aliceServiceSettings,
      CPCAPI2::PushToTalk::PushToTalkSettingsInternal& bobInternalSettings, CPCAPI2::PushToTalk::PushToTalkServiceSettings& bobServiceSettings);
   static void callIncomingOfferResponseTimeout(
      CPCAPI2::PushToTalk::PushToTalkSettingsInternal& aliceInternalSettings, CPCAPI2::PushToTalk::PushToTalkServiceSettings& aliceServiceSettings,
      CPCAPI2::PushToTalk::PushToTalkSettingsInternal& bobInternalSettings, CPCAPI2::PushToTalk::PushToTalkServiceSettings& bobServiceSettings);
   static void callIncomingRetryConnectionSuccess(
      CPCAPI2::PushToTalk::PushToTalkSettingsInternal& aliceInternalSettings, CPCAPI2::PushToTalk::PushToTalkServiceSettings& aliceServiceSettings,
      CPCAPI2::PushToTalk::PushToTalkSettingsInternal& bobInternalSettings, CPCAPI2::PushToTalk::PushToTalkServiceSettings& bobServiceSettings);
   static void callReceiverEnd(
      CPCAPI2::PushToTalk::PushToTalkSettingsInternal& aliceInternalSettings, CPCAPI2::PushToTalk::PushToTalkServiceSettings& aliceServiceSettings,
      CPCAPI2::PushToTalk::PushToTalkSettingsInternal& bobInternalSettings, CPCAPI2::PushToTalk::PushToTalkServiceSettings& bobServiceSettings);
   static void callTimeoutNoSpurt(
      CPCAPI2::PushToTalk::PushToTalkSettingsInternal& aliceInternalSettings, CPCAPI2::PushToTalk::PushToTalkServiceSettings& aliceServiceSettings,
      CPCAPI2::PushToTalk::PushToTalkSettingsInternal& bobInternalSettings, CPCAPI2::PushToTalk::PushToTalkServiceSettings& bobServiceSettings);
   static void callConnectionTimeout(
      CPCAPI2::PushToTalk::PushToTalkSettingsInternal& aliceInternalSettings, CPCAPI2::PushToTalk::PushToTalkServiceSettings& aliceServiceSettings);
   static void callMultipleRecipients(
      bool unicast, int recipientCount,
      CPCAPI2::PushToTalk::PushToTalkSettingsInternal& aliceInternalSettings, CPCAPI2::PushToTalk::PushToTalkServiceSettings& aliceServiceSettings);
#if (CPCAPI2_BRAND_NETWORK_CHANGE_MODULE == 1)
   static void networkChangeTest(
      CPCAPI2::PushToTalk::PushToTalkSettingsInternal& aliceInternalSettings, CPCAPI2::PushToTalk::PushToTalkServiceSettings& aliceServiceSettings,
      CPCAPI2::PushToTalk::PushToTalkSettingsInternal& bobInternalSettings, CPCAPI2::PushToTalk::PushToTalkServiceSettings& bobServiceSettings);
#endif
   static cpc::vector<CPCAPI2::PushToTalk::PushToTalkIpAddressRange> getUnicastIpRange(int numEndpoints);

};

#if !defined(ANDROID)
class RootConfSetupConfDetailsHandler : public CPCAPI2::ConferenceBridge::ConferenceDetailsHandler
{

public:

   RootConfSetupConfDetailsHandler();
   virtual~ RootConfSetupConfDetailsHandler();

   virtual int onConferenceDetails(CPCAPI2::ConferenceBridge::ConferenceHandle conference, const CPCAPI2::ConferenceBridge::ConferenceDetailsResult& args) OVERRIDE;
   virtual int onConferenceNotFound(CPCAPI2::ConferenceBridge::ConferenceHandle conference, const CPCAPI2::ConferenceBridge::ConferenceNotFoundResult& args) OVERRIDE;
   CPCAPI2::ConferenceBridge::ConferenceDetailsResult waitForResult();

private:

   CPCAPI2::ConferenceBridge::ConferenceDetailsResult result;
   resip::Mutex mMtx;
   resip::Condition mCond;

};

class BridgeConferenceListHandler : public CPCAPI2::ConferenceBridge::ConferenceListHandler
{

public:

   BridgeConferenceListHandler();
   virtual~ BridgeConferenceListHandler();

   virtual int onConferenceList(const CPCAPI2::ConferenceBridge::ConferenceListResult& args) OVERRIDE;
   CPCAPI2::ConferenceBridge::ConferenceListResult waitForResult();

private:

   CPCAPI2::ConferenceBridge::ConferenceListResult result;
   resip::Mutex mMtx;
   resip::Condition mCond;

};
#endif // !ANDROID

class TestPttEvents
{

public:

   static void expectPttServiceStartupCompleteLan(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkServiceHandle handle);
   #define assertPttServiceStartupCompleteLan(account, handle) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestPttEvents::expectPttServiceStartupCompleteLan(__LINE__, account, handle); \
   }

   static void expectPttServiceStartupCompleteWan(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkServiceHandle handle);
   #define assertPttServiceStartupCompleteWan(account, handle) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestPttEvents::expectPttServiceStartupCompleteWan(__LINE__, account, handle); \
   }
   
   static void expectPttServiceShutdownComplete(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkServiceHandle handle);
   #define assertPttServiceShutdownComplete(account, handle) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestPttEvents::expectPttServiceShutdownComplete(__LINE__, account, handle); \
   }

   static void expectPttServiceRestarted(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkServiceHandle handle, CPCAPI2::PushToTalk::PttRestartReasonType reason);
   #define assertPttServiceRestarted(account, handle, reason) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestPttEvents::expectPttServiceRestarted(__LINE__, account, handle, reason); \
   }

   static void expectPttServiceStatusChanged(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkServiceHandle handle, CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status status);
   static void expectPttServiceStatusChangedEx(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkServiceHandle handle, CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status status, CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Reason reason);
   #define assertPttServiceStatusChanged(account, handle, status) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestPttEvents::expectPttServiceStatusChanged(__LINE__, account, handle, status); \
   }
   #define assertPttServiceStatusChangedEx(account, handle, status, reason) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestPttEvents::expectPttServiceStatusChangedEx(__LINE__, account, handle, status, reason); \
   }

   static void expectPttEndpointList(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkServiceHandle& handle, int totalCount, int listCount, int offset, CPCAPI2::PushToTalk::PttIdentity& identity);
   static void expectPttEndpointListEx(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkServiceHandle& handle, int totalCount, int listCount, int offset);
   #define assertPttEndpointList(account, handle, totalCount, listCount, offset, identity) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestPttEvents::expectPttEndpointList(__LINE__, account, handle, totalCount, listCount, offset, identity); \
   }
   #define assertPttEndpointListEx(account, handle, totalCount, listCount, offset) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestPttEvents::expectPttEndpointListEx(__LINE__, account, handle, totalCount, listCount, offset); \
   }

   static void expectPttParticipantListUpdate(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkSessionHandle& handle, int listCount);
   #define assertPttParticipantListUpdate(account, handle, listCount) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestPttEvents::expectPttParticipantListUpdate(__LINE__, account, handle, listCount); \
   }

   static void expectPttSessionStateChanged(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkSessionHandle handle, CPCAPI2::PushToTalk::PttSessionStateType state);
   static void expectPttSessionStateChangedEx(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkSessionHandle handle, CPCAPI2::PushToTalk::PttSessionStateType state, unsigned int connectedCalls, unsigned int totalCalls);
   // static void expectPttSessionStateChangedEx2(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkSessionHandle handle, CPCAPI2::PushToTalk::PttSessionStateType state, std::string& userName);
   static void expectPttSessionStateChangedEx3(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkSessionHandle handle, CPCAPI2::PushToTalk::PttSessionStateType state, CPCAPI2::PushToTalk::PttSessionStateType& actualState);
   #define assertPttSessionStateChanged(account, handle, state) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestPttEvents::expectPttSessionStateChanged(__LINE__, account, handle, state); \
   }
   #define assertPttSessionStateChangedEx(account, handle, state, connectedCalls, totalCalls) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestPttEvents::expectPttSessionStateChangedEx(__LINE__, account, handle, state, connectedCalls, totalCalls); \
   }
   #define assertPttSessionStateChangedEx3(account, handle, state, actualState) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestPttEvents::expectPttSessionStateChangedEx3(__LINE__, account, handle, state, actualState); \
   }

   static void expectPttSessionError(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkSessionHandle handle, CPCAPI2::PushToTalk::PttSessionError errorCode);
   #define assertPttSessionError(account, handle, errorCode) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestPttEvents::expectPttSessionError(__LINE__, account, handle, errorCode); \
   }

   static void expectPttIncomingCall(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkSessionHandle& handle);
   static void expectPttIncomingCallEx(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkSessionHandle& handle, cpc::string& userName, cpc::string& displayName);
   static void expectPttIncomingCallEx2(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkSessionHandle& handle, cpc::string& userName, cpc::string& displayName, CPCAPI2::PushToTalk::PushToTalkServiceHandle& service);
   #define assertPttIncomingCall(account, handle) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestPttEvents::expectPttIncomingCall(__LINE__, account, handle); \
   }
   #define assertPttIncomingCallEx(account, handle, userName, displayName) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestPttEvents::expectPttIncomingCallEx(__LINE__, account, handle, userName, displayName); \
   }
   #define assertPttIncomingCallEx2(account, handle, userName, displayName, service) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestPttEvents::expectPttIncomingCallEx2(__LINE__, account, handle, userName, displayName, service); \
   }

   static void expectPttStatisticsEvent(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkServiceHandle& handle, cpc::string& userName, CPCAPI2::PushToTalk::PttStatisticsEventType statisticsType);
   #define assertPttStatisticsEvent(account, handle, userName, statisticsType) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestPttEvents::expectPttStatisticsEvent(__LINE__, account, handle, userName, statisticsType); \
   }

   static void expectPttMediaStatisticsEvent(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkSessionHandle& handle);
   #define assertPttMediaStatisticsEvent(account, handle) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestPttEvents::expectPttMediaStatisticsEvent(__LINE__, account, handle); \
   }

   static void expectPttReceiverDisconnected(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkSessionHandle& handle,
      const cpc::string& channel, CPCAPI2::PushToTalk::PttSessionStateType state, unsigned int connectedCalls, unsigned int totalCalls);
   #define assertPttReceiverDisconnected(account, handle, channel, state, connectedCalls, totalCalls) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestPttEvents::expectPttReceiverDisconnected(__LINE__, account, handle, channel, state, connectedCalls, totalCalls); \
   }

   static void expectPttQueryEndpointsRequestSent(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkServiceHandle& handle);
   #define assertPttQueryEndpointsRequestSent(account, handle) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestPttEvents::expectPttQueryEndpointsRequestSent(__LINE__, account, handle); \
   }

   static void expectPttQueryEndpointsRequestReceived(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkServiceHandle& handle);
   #define assertPttQueryEndpointsRequestReceived(account, handle) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestPttEvents::expectPttQueryEndpointsRequestReceived(__LINE__, account, handle); \
   }

   static void expectPttQueryEndpointsResponseSent(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkServiceHandle& handle, cpc::string& endpointIp);
   static void expectPttQueryEndpointsResponseSentEx(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkServiceHandle& handle, cpc::string& containsIp);
   #define assertPttQueryEndpointsResponseSent(account, handle, endpointIp) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestPttEvents::expectPttQueryEndpointsResponseSent(__LINE__, account, handle, endpointIp); \
   }
   #define assertPttQueryEndpointsResponseSentEx(account, handle, containsIp) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestPttEvents::expectPttQueryEndpointsResponseSentEx(__LINE__, account, handle, containsIp); \
   }

   static void expectPttQueryEndpointsResponseReceived(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkServiceHandle& handle, cpc::string& endpointIp);
   #define assertPttQueryEndpointsResponseReceived(account, handle, endpointIp) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestPttEvents::expectPttQueryEndpointsResponseReceived(__LINE__, account, handle, endpointIp); \
   }

   static void expectPttQueryEndpointsReportSent(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkServiceHandle& handle, int endpointCount);
   static void expectPttQueryEndpointsReportSentEx(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkServiceHandle& handle, std::vector<std::string>& endpointIps);
   #define assertPttQueryEndpointsReportSent(account, handle, endpointCount) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestPttEvents::expectPttQueryEndpointsReportSent(__LINE__, account, handle, endpointCount); \
   }
   #define assertPttQueryEndpointsReportSentEx(account, handle, endpointIps) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestPttEvents::expectPttQueryEndpointsReportSentEx(__LINE__, account, handle, endpointIps); \
   }

   static void expectPttQueryEndpointsReportReceived(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkServiceHandle& handle, int endpointCount);
   #define assertPttQueryEndpointsReportReceived(account, handle, endpointCount) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestPttEvents::expectPttQueryEndpointsReportReceived(__LINE__, account, handle, endpointCount); \
   }

   static void expectPttServiceConfigured(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkServiceHandle& handle);
   #define assertPttServiceConfigured(account, handle) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestPttEvents::expectPttServiceConfigured(__LINE__, account, handle); \
   }

   static void expectPttClientOfferEvent(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkSessionHandle& handle);
   #define assertPttClientOfferEvent(account, handle) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestPttEvents::expectPttClientOfferEvent(__LINE__, account, handle); \
   }

   static void expectPttClientOfferUpdateEvent(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkSessionHandle& handle, cpc::string& endpointIp);
   #define assertPttClientOfferUpdateEvent(account, handle, endpointIp) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestPttEvents::expectPttClientOfferUpdateEvent(__LINE__, account, handle, endpointIp); \
   }
};

#endif // PTT_TEST_HELPER_H
