//
//  network_utils.cpp


#include "network_utils.h"

#include <string>
#include <array>
#include <vector>
#include <sstream>
#ifndef _WIN32
#include <unistd.h>
#include <sys/socket.h>
#endif
#include <memory>

#include <resip/recon/RTPPortAllocator.hxx>
#include <reTurn/client/TurnAsyncUdpSocket_no_asio.hxx>

#include "cpcapi2defs.h"
#include "../cpcapi2_test_fixture.h"

using namespace CPCAPI2::test;


#ifdef _WIN32

int NetworkUtils::setDockerContainerNetworkEnabled(bool enabled)
{
   // todo: implement same behaviour as docker linux
   return 0;      // UNIT_TEST_WIN_DOCKER
}

int NetworkUtils::shutdownTcpSockets()
{
   // todo: implement same behaviour as docker linux
   return 0;      // UNIT_TEST_WIN_DOCKER
}

void NetworkUtils::listTcpSockets()
{
}

#elif (defined(__ANDROID__))

int NetworkUtils::setDockerContainerNetworkEnabled(bool enabled)
{
   return 1;
}

int NetworkUtils::shutdownTcpSockets()
{
   return 1;
}

void NetworkUtils::listTcpSockets()
{
}

#else

std::string exec(const char* cmd) {
    std::array<char, 128> buffer;
    std::string result;
    std::unique_ptr<FILE, decltype(&pclose)> pipe(popen(cmd, "r"), pclose);
    if (!pipe) {
        throw std::runtime_error("popen() failed!");
    }
    while (fgets(buffer.data(), buffer.size(), pipe.get()) != nullptr) {
        result += buffer.data();
    }
    return result;
}

std::vector<std::string> split_string(const std::string& str,
                                      const std::string& delimiter)
{
    std::vector<std::string> strings;

    std::string::size_type pos = 0;
    std::string::size_type prev = 0;
    while ((pos = str.find(delimiter, prev)) != std::string::npos)
    {
        strings.push_back(str.substr(prev, pos - prev));
        prev = pos + 1;
    }

    strings.push_back(str.substr(prev));

    return strings;
}


int NetworkUtils::setDockerContainerNetworkEnabled(bool enabled)
{
   if (enabled)
   {
      return system("sudo docker network connect bridge $HOSTNAME");
   }
   else
   {
      return system("sudo docker network disconnect -f bridge $HOSTNAME");
   }
}

// on CentOS 7:
// ss requires package iproute
// grep whires package grep
// these are automatically installed when using our docker based auto tests
int NetworkUtils::shutdownTcpSockets()
{
   std::stringstream ss;

   if (TestEnvironmentConfig::dockerContainerized())
   {
      // ss does not seem to return the correct pid when run from within a docker container,
      // so just grab all TCP FDs from all processes (likely just cpcapi2_auto_tests)
      ss << "ss --tcp --all --processes | grep -E \"pid=[0-9]+,\" | grep -oP '(?<=,fd=)([0-9]+)'";
   }
   else
   {
      ss << "ss --tcp --all --processes | grep -E \"pid=" << getpid() << ",\" | grep -oP '(?<=,fd=)([0-9]+)'";
   }

   std::string output = exec(ss.str().c_str());
   std::vector<std::string> fds = split_string(output, "\n");

   for (std::vector<std::string>::const_iterator it = fds.begin(); it != fds.end(); ++it)
   {
      int fd = atoi(it->c_str());
      shutdown(fd, SHUT_WR);
   }

   return kSuccess;
}

void NetworkUtils::listTcpSockets()
{
   std::stringstream ss;

   if (TestEnvironmentConfig::dockerContainerized())
   {
      // ss does not seem to return the correct pid when run from within a docker container,
      // so just grab all TCP FDs from all processes (likely just cpcapi2_auto_tests)
      ss << "ss --tcp --all --processes | grep -E \"pid=[0-9]+,\" | grep -oP '(?<=,fd=)([0-9]+)'";
   }
   else
   {
      ss << "ss --tcp --all --processes | grep -E \"pid=" << getpid() << ",\" | grep -oP '(?<=,fd=)([0-9]+)'";
   }

   std::string output = exec(ss.str().c_str());
   std::vector<std::string> fds = split_string(output, "\n");

   safeCout("========== NetworkUtils::listTcpSockets(): output: " << output << "==========");

   for (std::vector<std::string>::const_iterator it = fds.begin(); it != fds.end(); ++it)
   {
      int fd = atoi(it->c_str());
      safeCout("NetworkUtils::listTcpSockets(): fd: " << fd);
   }
}

#endif

ReconRtpPortSaturator::ReconRtpPortSaturator()
{
   recon::RTPPortAllocator* allocator = new recon::RTPPortAllocator();

   // DRL audio is 17000 to 17009 and dynamic is 49152 to 65535
   for (int port = 17000; port <= 17009; ++port)
   {
      mBlockedUDPPorts.insert(port);
      allocator->blockUDPPort(port);
   }
   for (int port = 49152; port <= 65535; ++port)
   {
      mBlockedUDPPorts.insert(port);
      allocator->blockUDPPort(port);
   }
}

ReconRtpPortSaturator::~ReconRtpPortSaturator()
{
   recon::RTPPortAllocator* allocator = new recon::RTPPortAllocator();
   
   for (std::unordered_set<unsigned int>::const_iterator it = mBlockedUDPPorts.begin(); it != mBlockedUDPPorts.end(); ++it)
   {
      allocator->unblockUDPPort(*it);
   }
}

static TurnAsyncUdpSocket_OutgoingPacketlossInducer* gUdpLossInstance = NULL;


static bool gUdpLossFn(const reTurn::StunTuple& destination, reTurn::DataBuffer* buffer)
{
   if (!gUdpLossInstance)
   {
      throw std::runtime_error("No active TurnAsyncUdpSocket_PacketlossInducer; test code bug?");
   }
   
   return gUdpLossInstance->shouldDropOutgoingPacket(&destination.getSockaddr());
}

bool TurnAsyncUdpSocket_OutgoingPacketlossInducer::shouldDropOutgoingPacket(const sockaddr* sa)
{
   /*
   std::string_view v(sa->sa_data);
   size_t hash = std::hash<std::string_view>()(v);
   DestStats* stats = NULL;
   
   {
      std::lock_guard<std::mutex> lck(mDestsMutex);
      std::unordered_map< std::size_t, DestStats >::iterator it = mDests.find(hash);
      if (it != mDests.end())
      {
         stats = &it->second;
      }
      else
      {
         mDests[hash] = DestStats();
         stats = &mDests[hash];
      }
   }
   */
   int r = std::abs(resip::Random::getCryptoRandom() % 100); // 0-99 inclusive
   if ((r + 1) <= mConfig.lossRatePct)
   {
      // drop
      return true;
   }
   
   return false;
}

TurnAsyncUdpSocket_OutgoingPacketlossInducer::TurnAsyncUdpSocket_OutgoingPacketlossInducer(const Config& config) :
   mConfig(config)
{
   if (gUdpLossInstance != NULL)
   {
      throw std::runtime_error("Only a single instance of TurnAsyncUdpSocket_PacketlossInducer at a time is currently supported");
   }
   safeCout("======= Test framework is introducing packetloss for all SDK RTP traffic @ " << config.lossRatePct << "% loss!")
   gUdpLossInstance = this;
   reTurn::TurnAsyncUdpSocket::setOutgoingUdpInterceptFn(&gUdpLossFn);
}

TurnAsyncUdpSocket_OutgoingPacketlossInducer::~TurnAsyncUdpSocket_OutgoingPacketlossInducer()
{
   gUdpLossInstance = NULL;
   reTurn::TurnAsyncUdpSocket::setOutgoingUdpInterceptFn(NULL);
}
