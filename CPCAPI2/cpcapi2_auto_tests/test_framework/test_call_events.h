#pragma once
#ifndef TEST_CALL_EVENTS_H
#define TEST_CALL_EVENTS_H

#include "../cpcapi2_test_fixture.h"

class TestCallEvents
{
public:
   static void expectNewConversationIncoming(int line, TestAccount& account, 
         CPCAPI2::SipConversation::SipConversationHandle& handle, const std::wstring& fromUri)
   {
	   NewConversationEvent evt;
	   ASSERT_TRUE(account.conversationEvents->expectEvent(line, "SipConversationHandler::onNewConversation",
		   30000, AlwaysTruePred(), handle, evt));
	   ASSERT_EQ(ConversationType_Incoming, evt.conversationType);
	   ASSERT_EQ(fromUri, evt.remoteAddress);
   }
   #define assertNewConversationIncoming(account, handle, fromUri) \
         TestCallEvents::expectNewConversationIncoming(__LINE__, account, handle, fromUri)
      
      
   static void expectNewConversationOutgoing(int line, TestAccount& account, 
         CPCAPI2::SipConversation::SipConversationHandle handle, const std::wstring& toUri)
   {
      CPCAPI2::SipConversation::SipConversationHandle h;
	   NewConversationEvent evt;
	   ASSERT_TRUE(account.conversationEvents->expectEvent(line, "SipConversationHandler::onNewConversation",
		   15000, HandleEqualsPred<SipConversationHandle>(handle), h, evt));
	   ASSERT_EQ(ConversationType_Outgoing, evt.conversationType);
	   ASSERT_EQ(toUri, evt.remoteAddress);
   }
   #define assertNewConversationOutgoing(account, handle, toUri) \
      TestCallEvents::expectNewConversationOutgoing(__LINE__, account, handle, toUri)
      

   static void expectConversationMediaChanged(int line, TestAccount& account, 
         CPCAPI2::SipConversation::SipConversationHandle handle)
   {
      CPCAPI2::SipConversation::SipConversationHandle h;
	   ConversationMediaChangedEvent evt;
	   ASSERT_TRUE(account.conversationEvents->expectEvent(line, "SipConversationHandler::onConversationMediaChanged",
		   15000, HandleEqualsPred<SipConversationHandle>(handle), h, evt));
   }
   #define assertConversationMediaChanged(account, handle) \
      TestCallEvents::expectConversationMediaChanged(__LINE__, account, handle)
      
   static void expectConversationStateChanged(int line, TestAccount& account, 
         CPCAPI2::SipConversation::SipConversationHandle handle, CPCAPI2::SipConversation::ConversationState state)
   {
      CPCAPI2::SipConversation::SipConversationHandle h;
      ConversationStateChangedEvent evt;
      ASSERT_TRUE(account.conversationEvents->expectEvent(line, "SipConversationHandler::onConversationStateChanged",
	      15000, HandleEqualsPred<SipConversationHandle>(handle), h, evt));
      ASSERT_EQ(state, evt.conversationState);
   }
   #define assertConversationRemoteRinging(account, handle) \
      TestCallEvents::expectConversationStateChanged(__LINE__, account, handle, ConversationState_RemoteRinging)
   #define assertConversationLocalRinging(account, handle) \
      TestCallEvents::expectConversationStateChanged(__LINE__, account, handle, ConversationState_LocalRinging)
   #define assertConversationConnected(account, handle) \
      TestCallEvents::expectConversationStateChanged(__LINE__, account, handle, ConversationState_Connected)

   static void expectConversationEnded(int line, TestAccount& account, 
         CPCAPI2::SipConversation::SipConversationHandle handle, CPCAPI2::SipConversation::ConversationEndReason endReason)
   {
      CPCAPI2::SipConversation::SipConversationHandle h;
      ConversationEndedEvent evt;
	   ASSERT_TRUE(account.conversationEvents->expectEvent(line, "SipConversationHandler::onConversationEnded",
		   60000, HandleEqualsPred<SipConversationHandle>(handle), h, evt));
	   ASSERT_EQ(endReason, evt.endReason);
   }
   #define assertConversationEndedRemotely(account, handle) \
      TestCallEvents::expectConversationEnded(__LINE__, account, handle, CPCAPI2::SipConversation::ConversationEndReason_UserTerminatedRemotely)
   #define assertConversationEndedLocally(account, handle) \
      TestCallEvents::expectConversationEnded(__LINE__, account, handle, CPCAPI2::SipConversation::ConversationEndReason_UserTerminatedLocally)

};

#endif //TEST_CALL_EVENTS_H
