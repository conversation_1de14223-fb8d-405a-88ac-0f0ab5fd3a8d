//
//  visqol_runner.h
//  CPCAPI2AutoTestsX
//
//  Created by <PERSON><PERSON><PERSON> on 2022-02-28.
//  Copyright © 2022 <PERSON>. All rights reserved.
//



#ifndef visqol_runner_h
#define visqol_runner_h
#include <string>

namespace CPCAPI2
{
namespace test
{
   class VisqolRunner
   {
   public:
      static void visqol_exec(const std::string& reference_file_path, const std::string& degraded_file_path, double& outMos);
   };
}
}
#endif /* visqol_runner_h */
