#include "test_framework/test_runtime_environment.h"

using namespace CPCAPI2::test;

static TestRuntimeEnvironment* gInstance = nullptr;

TestRuntimeEnvironment* TestRuntimeEnvironment::instance()
{
  if (nullptr == gInstance)
  {
    gInstance = new TestRuntimeEnvironment();
  }
  return gInstance;   
}

TestRuntimeEnvironment::TestRuntimeEnvironment() :
   mAndroidAppContext(nullptr)
{
}

bool TestRuntimeEnvironment::checkDnsServer()
{
   // we don't run the DNS server on the device, we run it on the host machine, so this tells Android where to find it
   // and in future we'd like to enhance this check to include making sure unbound is running on the platforms that need it
#ifdef ANDROID
   char* dnsServer = getenv("CPCAPI2_DNS_SERVER");
   // verify that the external DNS server has been set
   if (dnsServer == NULL || strlen(dnsServer) == 0)
   {
      return false;
   }
#endif
   return true;
}