#include "dummy_tcplistener.h"
#include "cpcapi2_test_framework.h"

#include <iostream>
#include "rutil/Socket.hxx"


using namespace CPCAPI2::test;

DummyTcpListener::DummyTcpListener(const Config& config) :
   mConfig(config)
{
}

DummyTcpListener::~DummyTcpListener()
{
   if (!mServerShouldQuit)
   {
      stop();
   }
}

int DummyTcpListener::start()
{
   struct sockaddr_in servaddr;

   mListenFd = ::socket(AF_INET,SOCK_STREAM,0);

   const int sockopt = 1;
#ifdef _WIN32
   ::setsockopt(mListenFd, SOL_SOCKET, SO_REUSEADDR, (const char*)&sockopt, sizeof(sockopt));
#else
   ::setsockopt(mListenFd, SOL_SOCKET, SO_REUSEADDR, (const void*)&sockopt, sizeof(sockopt));
#endif

   int tryPort = mConfig.port;
   const int maxTryCount = mConfig.tryOtherPorts ? 5 : 1;
   int lastBindRet = -1;
   for (int tries = 1; tries <= maxTryCount; ++tries)
   {
      memset(&servaddr, 0, sizeof(servaddr));
      //bzero(&servaddr, sizeof(servaddr));
      servaddr.sin_family = AF_INET;
      servaddr.sin_addr.s_addr=htonl(INADDR_ANY);
      servaddr.sin_port=htons(tryPort);
      lastBindRet = ::bind(mListenFd,(struct sockaddr *)&servaddr, sizeof(servaddr));
      if (lastBindRet != 0)
      {
         std::cout << "bind for startBrainDeadServer failed; trying next port" << std::endl;
         ++tryPort;
      }
      else
      {
         break;
      }
   }

   if (lastBindRet != 0)
   {
      safeCout("DummyTcpListener::start: Couldn't find a free port");
      return -1;
   }

   ::listen(mListenFd, 1024);
   
   mListenerThread = std::async(std::launch::async, [this] ()
   {
      sockaddr_storage client_addr;
      socklen_t client_addr_size = sizeof(client_addr);
      while (!mServerShouldQuit)
      {
         ::accept(mListenFd, (sockaddr *) &client_addr, &client_addr_size);
      }
   });

   return tryPort;
}

void DummyTcpListener::stop()
{
   mServerShouldQuit = true;

 #if !defined(_WIN32)
   ::shutdown(mListenFd, SHUT_RDWR);
#endif
   resip::closeSocket(mListenFd);

   mListenerThread.wait();
}