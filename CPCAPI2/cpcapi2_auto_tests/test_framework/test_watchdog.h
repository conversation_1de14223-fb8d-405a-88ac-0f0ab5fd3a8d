#pragma once

#include <rutil/Log.hxx>
#include <iostream>

class TestTimerWatchdog : public ::testing::EmptyTestEventListener 
{
public:
   TestTimerWatchdog(std::chrono::duration<int> maxTimePeriodSec) : mMaxTimePeriodSec(maxTimePeriodSec), mTestFinished(false)
   {
   }
   
   virtual void OnTestProgramStart(const ::testing::UnitTest& test_info) 
   {
      assert(!mTestFinished);
      mTestFinished = false;

      mCurrentTestWait = std::async(std::launch::async, [&]()
      {
         std::unique_lock<std::mutex> lock(mMutex);
         auto now = std::chrono::system_clock::now();
         if (mCv.wait_until(lock, now + mMaxTimePeriodSec, [&]() { return mTestFinished; }))
         {
            assert(mTestFinished);
         }
         else
         {
            std::stringstream ss;

            ss << "Test '" << test_info.current_test_info()->test_case_name() << "." << test_info.current_test_info()->name() << "' took longer than the maximum watchdog test timeout of " 
               << mMaxTimePeriodSec.count() << " seconds. Current time: " << resip::Log::timestamp();
            std::cerr << ss.str() << std::endl << std::flush;
            
            std::this_thread::sleep_for(std::chrono::seconds(3));

            // an exception is not good enough, as an exception would not be propagated until another thread
            // calls get() on mCurrentTestWait
            abort();
         }
      });
   }

   // Called after a failed assertion or a SUCCEED() invocation.
   virtual void OnTestPartResult(
      const ::testing::TestPartResult& test_part_result) 
   {   
   }

   // Called after a test ends.
   virtual void OnTestProgramEnd(const ::testing::UnitTest& test_info) 
   {
      mTestFinished = true;
      mCv.notify_all();
   }

private:
   std::mutex mMutex;
   std::condition_variable mCv;
   std::chrono::duration<int> mMaxTimePeriodSec;
   std::future<void> mCurrentTestWait;
   bool mTestFinished;
};