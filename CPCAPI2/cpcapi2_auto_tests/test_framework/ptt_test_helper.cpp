#if defined(__GNUC__) || defined(__clang__)
#pragma GCC diagnostic ignored "-Wdeprecated-declarations"
#endif

#include "ptt_test_helper.h"
#include "network_utils.h"
#include <orchestration_server/OrchestrationServer.h>
#include <cloudserviceconfig/CloudServiceConfig.h>
#include <confbridge/ConferenceBridgeJsonApi.h>
#include <confbridge/ConferenceBridgeManager.h>
#include <confbridge/ConferenceBridgeInternal.h>
#include "impl/confbridge/ConferenceBridgeHandlerInternal.h"
#include "impl/util/FileDescriptorMonitor.h"
#include "phone/PhoneInterface.h"

#include "impl/auth_server/AuthServerJwtUtils.h"
#include "impl/auth_server/AuthServerDbAccess.h"


using namespace std;
using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::SipConversation;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::Media;
using namespace CPCAPI2::PushToTalk;
using namespace CPCAPI2::ConferenceConnector;
using namespace CPCAPI2::CloudServiceConfig;
using namespace CPCAPI2::OrchestrationServer;
using namespace CPCAPI2::JsonApi;

#if !defined(ANDROID)

RootConfSetupConfDetailsHandler::RootConfSetupConfDetailsHandler()
{
}

RootConfSetupConfDetailsHandler::~RootConfSetupConfDetailsHandler()
{
   safeCout("PttTestHelper::RootConfSetupConfDetailsHandler(): destroyed");
   mMtx.unlock();
}

int RootConfSetupConfDetailsHandler::onConferenceDetails(CPCAPI2::ConferenceBridge::ConferenceHandle conference, const CPCAPI2::ConferenceBridge::ConferenceDetailsResult& args)
{
   safeCout("PttTestHelper::RootConfSetupConfDetailsHandler::onConferenceDetails(): conference: " << conference << " url: " << args.conferenceJoinUrl << " description: " << args.conferenceInfo.description << " owner: " << args.conferenceInfo.creatorDisplayName);
   mMtx.lock();
   result = args;
   mCond.signal();
   mMtx.unlock();
   return 0;
}

int RootConfSetupConfDetailsHandler::onConferenceNotFound(CPCAPI2::ConferenceBridge::ConferenceHandle conference, const CPCAPI2::ConferenceBridge::ConferenceNotFoundResult& args)
{
   safeCout("PttTestHelper::RootConfSetupConfDetailsHandler::onConferenceNotFound(): conference: " << conference);
   mMtx.lock();
   mCond.signal();
   mMtx.unlock();
   return 0;
}

CPCAPI2::ConferenceBridge::ConferenceDetailsResult RootConfSetupConfDetailsHandler::waitForResult()
{
   mMtx.lock();
   mCond.wait(mMtx);
   return result;
}

BridgeConferenceListHandler::BridgeConferenceListHandler()
{
}

BridgeConferenceListHandler::~BridgeConferenceListHandler()
{
   safeCout("PttTestHelper::BridgeConferenceListHandler(): destroyed");
   mMtx.unlock();
}

int BridgeConferenceListHandler::onConferenceList(const CPCAPI2::ConferenceBridge::ConferenceListResult& args)
{
   safeCout("PttTestHelper::BridgeConferenceListHandler::onConferenceList(): conference-list: " << args.conferences.size());
   mMtx.lock();
   result = args;
   mCond.signal();
   mMtx.unlock();
   return 0;
}

CPCAPI2::ConferenceBridge::ConferenceListResult BridgeConferenceListHandler::waitForResult()
{
   mMtx.lock();
   mCond.wait(mMtx);
   return result;
}

void PttTestHelper::getCloudServerUrls_ptt(std::string& authUrl, std::string& orchUrl, std::string& agentUrl)
{
   std::stringstream authBuf;
   authBuf << "https://127.0.0.1:" << 18084;
   authUrl = authBuf.str().c_str();

   std::stringstream orchBuf;
   orchBuf << "https://127.0.0.1:" << 18082 << "/jsonApi";
   // orchBuf << "http://inproc.local:" << XMPP_ORCH_SERVER_HTTP_PORT << "/jsonApi";
   orchUrl = orchBuf.str().c_str();

   std::stringstream agentBuf;
   agentBuf << "wss://127.0.0.1:" << 9003;
   agentUrl = agentBuf.str().c_str();
}

void PttTestHelper::setupAuthServer_ptt(TestAccount& max)
{
   CPCAPI2::AuthServer::DbAccess authDb;
   authDb.initialize("authserver.db");
   authDb.flushUsers();
   authDb.addUser("<EMAIL>", "1234");
   authDb.addUser("user2", "1234");
   authDb.addUser("<EMAIL>", "1234");
   authDb.addUser("<EMAIL>", "1234");
   authDb.addUser("<EMAIL>", "1234");
   authDb.addUser("<EMAIL>", "1234");
   authDb.addUser("<EMAIL>", "1234");
   authDb.addUser("server", "server");

   CPCAPI2::AuthServer::AuthServerConfig authServerConfig;
   authServerConfig.certificateFilePath = TestEnvironmentConfig::testResourcePath() + "p256-private-key-unit-tests.p8";
   authServerConfig.httpsCertificateFilePath = TestEnvironmentConfig::testResourcePath() + "cert.pem";
   authServerConfig.httpsPrivateKeyFilePath = TestEnvironmentConfig::testResourcePath() + "privkey.pem";
   authServerConfig.httpsDiffieHellmanParamsFilePath = TestEnvironmentConfig::testResourcePath() + "dh2048.pem";
   authServerConfig.numThreads = 4;
   authServerConfig.port = 18084;
   max.authServer->start(authServerConfig);
}

CPCAPI2::JsonApi::JsonApiServerConfig PttTestHelper::getPttServerConfig()
{
   cpc::string certificateFilePath = TestEnvironmentConfig::testResourcePath() + "p256-public-key-unit-tests.spki"; // pub key used to validate auth tokens
   CPCAPI2::JsonApi::JsonApiServerConfig jsonApiServCfg(9003, 18082, certificateFilePath);
   jsonApiServCfg.wssCertificateFilePath = TestEnvironmentConfig::testResourcePath() + "cert.pem";
   jsonApiServCfg.wssPrivateKeyFilePath = TestEnvironmentConfig::testResourcePath() + "privkey.pem";
   jsonApiServCfg.wssDiffieHellmanParamsFilePath = TestEnvironmentConfig::testResourcePath() + "dh2048.pem";
   jsonApiServCfg.httpsCertificateFilePath = TestEnvironmentConfig::testResourcePath() + "cert.pem";
   jsonApiServCfg.httpsPrivateKeyFilePath = TestEnvironmentConfig::testResourcePath() + "privkey.pem";
   jsonApiServCfg.httpsDiffieHellmanParamsFilePath = TestEnvironmentConfig::testResourcePath() + "dh2048.pem";

   if (jsonApiServCfg.certificateFilePath.size() == 0)
      jsonApiServCfg.certificateFilePath = TestEnvironmentConfig::testResourcePath() + "p256-public-key-unit-tests.spki"; // pub key used to validate auth tokens

   return jsonApiServCfg;
}

CPCAPI2::VideoStreaming::VideoStreamingServerConfig PttTestHelper::getVideoStreamingServerConfig()
{
   CPCAPI2::VideoStreaming::VideoStreamingServerConfig vsConfig;
   vsConfig.listenPort = 9005;
   vsConfig.wssCertificateFilePath = TestEnvironmentConfig::testResourcePath() + "cert.pem";
   vsConfig.wssPrivateKeyFilePath = TestEnvironmentConfig::testResourcePath() + "privkey.pem";
   vsConfig.wssDiffieHellmanParamsFilePath = TestEnvironmentConfig::testResourcePath() + "dh2048.pem";
   return vsConfig;
}

void PttTestHelper::setupPttServer(TestAccount& maia)
{
   cpc::string conferenceBridgeServiceId = "confbridge";
   CPCAPI2::JsonApi::JsonApiServerConfig jsonApiServCfg = getPttServerConfig();
   maia.jsonApiServer->start(jsonApiServCfg);
   CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(maia.phone)->setJsonApiServer(maia.jsonApiServer);
   CPCAPI2::ConferenceBridge::ConferenceBridgeJsonApi::getInterface(maia.phone);

   //CPCAPI2::VideoStreaming::VideoStreamingManager* videoStreamingMgr = CPCAPI2::VideoStreaming::VideoStreamingManager::getInterface(maia.phone);
   //if (videoStreamingMgr != NULL)
   //{
   //   videoStreamingMgr->setVideoStreamingServer(CPCAPI2::VideoStreaming::VideoStreamingManager::getInterface(maia.phone));
   //   CPCAPI2::VideoStreaming::VideoStreamingServerConfig vsConfig = getVideoStreamingServerConfig();
   //   videoStreamingMgr->startVideoStreamingServer(vsConfig);
   //}

   CPCAPI2::ConferenceBridge::ConferenceRegistrar* confRegistrar = CPCAPI2::ConferenceBridge::ConferenceRegistrar::getInterface(maia.phone);
   if (confRegistrar != NULL)
   {
      CPCAPI2::ConferenceBridge::ConferenceRegistrarConfig confRegistrarConfig;
      confRegistrarConfig.conferenceRegistrarServiceIp = cpc::string("127.0.0.1:") + std::to_string(jsonApiServCfg.httpPort).c_str();
      confRegistrarConfig.conferenceRegistrarServicePort = jsonApiServCfg.httpPort;
      confRegistrarConfig.joinClusterUrl = cpc::string("https://127.0.0.1:") + std::to_string(jsonApiServCfg.httpPort).c_str() + cpc::string("/statusApi/joinCluster");
      confRegistrarConfig.nodeId = 1;
      confRegistrarConfig.wsUrlBase = cpc::string("wss://127.0.0.1:") + std::to_string(jsonApiServCfg.websocketPort).c_str();
      confRegistrarConfig.authServiceUrl = "https://127.0.0.1:18084/login_v2";
      confRegistrarConfig.authServiceApiKey = "-----BEGIN PUBLIC KEY-----\n"
         "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiKzxgDrW3u9+XzGN4r/GI5o+BMtt\n"
         "y+63OT4lwEgFlkpedq5FY3eCzI4lB1WXDVip0OpCM1zYoCh6mqi7KaS1Vg==\n"
         "-----END PUBLIC KEY-----";
      confRegistrarConfig.urlMapFilename = maia.config.name + cpc::string("_urlmap_autotests.db");

      confRegistrar->start(confRegistrarConfig);
      CPCAPI2::ConferenceBridge::ConferenceRegistrarHandle confregh;
      CPCAPI2::ConferenceBridge::ConferenceRegistrarStartupResult confregargs;
      cpcExpectEvent(maia.conferenceRegistrarEvents, "ConferenceRegistrarHandler::onStartupComplete", 15000, CPCAPI2::test::AlwaysTruePred(), confregh, confregargs);
   }

   CPCAPI2::OrchestrationServer::OrchestrationServer* agentOrchServer = CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(maia.phone);
   CPCAPI2::OrchestrationServer::OrchestrationServerConfig serverConfig;
   serverConfig.redisIp = "mock";
   serverConfig.redisPort = 6379;
   agentOrchServer->start(serverConfig);

   //CPCAPI2::Media::MediaStackSettings mediaSettings;
   //mediaSettings.audioLayer = CPCAPI2::Media::AudioLayers_File;
   //mediaSettings.audioOutputDisabled = true;
   //mediaSettings.numAudioEncoderThreads = 4;
   CPCAPI2::Media::MediaManager* media = CPCAPI2::Media::MediaManager::getInterface(maia.phone);
   ASSERT_TRUE(media != NULL);
   CPCAPI2::Media::AudioExt* audioExt = CPCAPI2::Media::AudioExt::getInterface(media);
   ASSERT_TRUE(audioExt != NULL);
   //audioExt->setAudioDeviceFile(TestEnvironmentConfig::testResourcePath() + "silence16.pcm", "");
   //media->initializeMediaStack(mediaSettings);

   CPCAPI2::Media::Audio* audioIf = CPCAPI2::Media::Audio::getInterface(media);
   audioIf->setEchoCancellationMode(CPCAPI2::Media::AudioDeviceRole_Headset, CPCAPI2::Media::EchoCancellationMode_None);
   audioIf->setNoiseSuppressionMode(CPCAPI2::Media::AudioDeviceRole_Headset, CPCAPI2::Media::NoiseSuppressionMode_None);
   audioExt->setMicAGCEnabled(false);
   //audioIf->queryCodecList();
   //audioIf->setMicMute(true);
   //audioIf->setSpeakerMute(true);

   //Video::getInterface(mMedia)->setHandler(this);
   CPCAPI2::Media::Video::getInterface(media)->setCaptureDevice(CPCAPI2::Media::kCustomVideoSourceDeviceId);
   //CPCAPI2::Media::Video::getInterface(media)->startCapture();
   //Video::getInterface(mMedia)->setVideoMixMode(CPCAPI2::Media::VideoMixMode_MCU);
   //Video::getInterface(mMedia)->queryCodecList();

   CPCAPI2::Media::H264Config h264config;
   h264config.enableNonInterleavedMode = false;
   h264config.preferNonInterleavedMode = false;
   CPCAPI2::Media::Video::getInterface(media)->setCodecConfig(h264config);

   CloudServiceConfigManager* cloudConfigMgr = CPCAPI2::CloudServiceConfig::CloudServiceConfigManager::getInterface(maia.phone);
   test::EventHandler cloudConfigEvents(conferenceBridgeServiceId, dynamic_cast<CPCAPI2::AutoTestProcessor*>(cloudConfigMgr));

   std::string authUrl("");
   std::string orchUrl("");
   std::string agentUrl("");
   getCloudServerUrls_ptt(authUrl, orchUrl, agentUrl);
   orchUrl = "http://inproc.local";

   ConferenceBridge::ConferenceBridgeConfig bridgeSettings;
   bridgeSettings.httpJoinUrlBase = "https://127.0.0.1:18082";
   bridgeSettings.wsUrlBase = "wss://127.0.0.1:9003";
   bridgeSettings.userContext = maia.config.name;
   bridgeSettings.serverUid = "BC";
   bridgeSettings.mediaEncryptionMode = CPCAPI2::ConferenceBridge::ConferenceMediaEncryptionMode_SRTP_DTLS;
   bridgeSettings.mediaInactivityTimeoutMs = 2000;
   CPCAPI2::ConferenceBridge::ConferenceBridgeManager::getInterface(maia.phone)->start(bridgeSettings);

   ServerInfo xmppAgentServerInfo;
   xmppAgentServerInfo.region = "LOCAL";
   xmppAgentServerInfo.uri = agentUrl.c_str();
   xmppAgentServerInfo.services.push_back(conferenceBridgeServiceId);
   ServiceConfigSettings serviceConfigSettings;
   serviceConfigSettings.authServerUrl = std::string(authUrl + "/login_v1").c_str();
   serviceConfigSettings.orchestrationServerUrl = orchUrl.c_str();
   serviceConfigSettings.username = "server";
   serviceConfigSettings.password = "server";

   cloudConfigMgr->setServerInfo(serviceConfigSettings, xmppAgentServerInfo);
   {
      CloudServiceConfigHandle h;
      SetServerInfoResult args;
      ASSERT_TRUE(cpcExpectEvent((&cloudConfigEvents), "CloudServiceConfigHandler::onSetServerInfoSuccess", 5000, CPCAPI2::test::AlwaysTruePred(), h, args));
   }
}

void PttTestHelper::runWanServer(std::shared_ptr<TestAccount>& authServer, std::shared_ptr<TestAccount>& confServer, CPCAPI2::ConferenceBridge::ConferenceDetailsResult& rootConfDetails, std::atomic_bool& loginComplete, std::atomic_bool& rootConfDetailsHandlerActive, resip::Condition& rootDetailsReady)
{
   LoginResultEvent loginResult; loginResult.success = true;
   cpc::vector<cpc::string> permissions; permissions.push_back("*");

   authServer = std::make_shared<TestAccount>("authServer", Account_Init);
   setupAuthServer_ptt(*authServer);

   // Orchestration and confbridge server
   confServer = std::make_shared<TestAccount>("confServer", Account_NoInit);
   confServer->config.useFileAudioDevice = true;
   confServer->config.mediaSettings.audioLayer = CPCAPI2::Media::AudioLayers_File;
   confServer->config.mediaSettings.audioOutputDisabled = true;
   confServer->config.mediaSettings.numAudioEncoderThreads = std::thread::hardware_concurrency();
   confServer->init();
   setupPttServer(*confServer);

   CPCAPI2::ConferenceBridge::ConferenceBridgeManager* confBridgeMgr = CPCAPI2::ConferenceBridge::ConferenceBridgeManager::getInterface(confServer->phone);
   CPCAPI2::ConferenceBridge::ConferenceSettings rootConfSettings;
   rootConfSettings.conferenceToken = "root";
   rootConfSettings.label = "root";
   rootConfSettings.mixMode = CPCAPI2::ConferenceBridge::ConferenceMixMode_NoMixing;
   rootConfSettings.persistent = true;
   rootConfSettings.isPublic = true;
   CPCAPI2::ConferenceBridge::ConferenceHandle rootConf = confBridgeMgr->createConference(rootConfSettings);
   RootConfSetupConfDetailsHandler* confDetailsHandler = new RootConfSetupConfDetailsHandler();
   confBridgeMgr->queryConferenceDetails(rootConf, confDetailsHandler);
   // std::atomic_bool rootConfDetailsHandlerActive(true);

   auto loginEvent = std::async(std::launch::async, [&]()
   {
      JsonApiUserHandle jsonApiUser = 0;
      NewLoginEvent args;

      while (!loginComplete)
      {
         if (rootConfDetailsHandlerActive)
         {
            // the test case later uses EventHandler class, which would call process_test
            // which could conflict with this regular call to process.
            // also, AutoTestReadCallback posted with handler address of 0xDEADBEEF would
            // likely blow up here
            confBridgeMgr->process(kBlockingModeNonBlocking);
         }

         // Maia has to process the login attempt (associate the context with an SDK instance)
         if (cpcWaitForEvent(confServer->jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 1000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args))
         {
            ASSERT_NE(jsonApiUser, 0);
            confServer->jsonApiServer->setJsonApiUserContext(jsonApiUser, confServer->phone, permissions);
            confServer->jsonApiServer->sendLoginResult(jsonApiUser, loginResult);
         }
      }

   });

   rootConfDetails = confDetailsHandler->waitForResult();

   std::string confJoinUrlNoParams = rootConfDetails.conferenceJoinUrl.c_str();
   size_t qsPos = confJoinUrlNoParams.rfind("/conf/");
   if (qsPos != std::string::npos)
   {
      rootConfDetails.conferenceJoinUrl = confJoinUrlNoParams.substr(0, qsPos).c_str();
   }

   delete confDetailsHandler;
   rootDetailsReady.signal();
   rootConfDetailsHandlerActive = false;
   waitForMs(loginEvent, std::chrono::milliseconds(300000));
}

#endif // !ANDROID

PushToTalkSessionHandle PttTestHelper::createPttRecipients(TestAccount& caller, PushToTalkServiceHandle service, std::vector<TestAccount*>& recipients, int recipientCount)
{
   // Create the PTT group recipient accounts
   for (int i = 1; i <= recipientCount; i++)
   {
      std::stringstream user;
      user << "user_" << i;
      TestAccount* account = new TestAccount(user.str().c_str());
      recipients.push_back(account);
   }

   // Add the PTT recipients to the PTT session
   PushToTalkSessionHandle ptt = caller.ptt->createPttSession(service);
   for (std::vector<TestAccount*>::iterator i = recipients.begin(); i != recipients.end(); ++i)
   {
      PttIdentity identity;
      identity.userName = (*i)->config.uri();
      identity.displayName = identity.userName;
      caller.ptt->addRecipient(ptt, identity);
   }
   return ptt;
}

void PttTestHelper::startupPtt(
   TestAccount& alice, TestAccount& bob,
   PushToTalkManager*& alicePttManager,
   PushToTalkManager*& bobPttManager,
   PushToTalkSettingsInternal& aliceInternalSettings, PushToTalkServiceSettings& aliceServiceSettings,
   PushToTalkSettingsInternal& bobInternalSettings, PushToTalkServiceSettings& bobServiceSettings)
{
   alicePttManager = PushToTalkManager::getInterface(alice.phone);
   bobPttManager = PushToTalkManager::getInterface(bob.phone);
   PushToTalkManagerInternal* alicePttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(alicePttManager);
   PushToTalkManagerInternal* bobPttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(bobPttManager);

   PushToTalkServiceHandle alicePttService = alicePttManager->createPttService();
   alicePttManager->setHandler(alicePttService, (PushToTalkHandler*)0xDEADBEEF);
   alicePttManager->configureService(alicePttService, aliceServiceSettings);

   PushToTalkServiceHandle bobPttService = bobPttManager->createPttService();
   bobPttManager->setHandler(bobPttService, (PushToTalkHandler*)0xDEADBEEF);
   bobPttManager->configureService(bobPttService, bobServiceSettings);

   alicePttManagerInternal->setPttInternalSettings(alicePttService, aliceInternalSettings);
   bobPttManagerInternal->setPttInternalSettings(bobPttService, bobInternalSettings);

   alicePttManager->startService(alicePttService);
   bobPttManager->startService(bobPttService);
   assertPttServiceStartupCompleteLan(alice, alicePttService);
   assertPttServiceStartupCompleteLan(bob, bobPttService);
}

void PttTestHelper::call(
   PushToTalkSettingsInternal& aliceInternalSettings, PushToTalkServiceSettings& aliceServiceSettings,
   PushToTalkSettingsInternal& bobInternalSettings, PushToTalkServiceSettings& bobServiceSettings,
   int callCount)
{
   TestAccount alice("alice", Account_Init);

   PushToTalkManager* alicePttManager = PushToTalkManager::getInterface(alice.phone);
   PushToTalkServiceHandle alicePttService = alicePttManager->createPttService();
   alicePttManager->setHandler(alicePttService, (PushToTalkHandler*)0xDEADBEEF);
   alicePttManager->configureService(alicePttService, aliceServiceSettings);
   PushToTalkManagerInternal* alicePttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(alicePttManager);

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   // Bob is a PTT recipient
   TestAccount bob("bob", Account_Init);
   PushToTalkManager* bobPttManager = PushToTalkManager::getInterface(bob.phone);
   PushToTalkServiceHandle bobPttService = bobPttManager->createPttService();
   bobPttManager->setHandler(bobPttService, (PushToTalkHandler*)0xDEADBEEF);
   bobPttManager->configureService(bobPttService, bobServiceSettings);
   PushToTalkManagerInternal* bobPttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(bobPttManager);

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

#ifdef ANDROID
   int maxFdIncrease;
   if (aliceServiceSettings.unicastIpRanges.size() == 0 && bobServiceSettings.unicastIpRanges.size() == 0)
   {
      // will send to all of 127.0.0.x, so more file descriptors will be used
      maxFdIncrease = 100;
   }
   else
   {
      maxFdIncrease = 45;
   }
   CPCAPI2::Utils::FileDescriptorMonitor fdMon(maxFdIncrease);
#endif

   alicePttManagerInternal->setPttInternalSettings(alicePttService, aliceInternalSettings);
   bobPttManagerInternal->setPttInternalSettings(bobPttService, bobInternalSettings);

   alicePttManager->startService(alicePttService);
   bobPttManager->startService(bobPttService);

   auto aliceEvents = std::async(std::launch::async, [&]()
   {
      assertPttQueryEndpointsReportSent(alice, alicePttService, 2);
      assertPttServiceStartupCompleteLan(alice, alicePttService);
   });

   auto bobEvents = std::async(std::launch::async, [&]()
   {
      assertPttQueryEndpointsReportSent(bob, bobPttService, 2);
      assertPttServiceStartupCompleteLan(bob, bobPttService);
   });

   waitFor2(aliceEvents, bobEvents);

   std::this_thread::sleep_for(std::chrono::milliseconds(10000));

   for (int i = 0; i < callCount; i++)
   {
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));

      auto aliceEvents2 = std::async(std::launch::async, [&]()
      {
         PushToTalkSessionHandle alicePtt = alicePttManager->createPttSession(alicePttService);
         alicePttManager->setChannel(alicePtt, "channel01");
         alicePttManager->start(alicePtt);
         assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Initiated);
         assertPttClientOfferEvent(alice, alicePtt);
         assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Active);

         // Initiate talk spurt
         alicePttManager->startTalkSpurt(alicePtt);
         assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Talking);
         std::this_thread::sleep_for(std::chrono::milliseconds(4000));
         alicePttManager->endTalkSpurt(alicePtt);
         assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Active);

         alicePttManager->end(alicePtt);
         assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Ending);
         assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Idle);
      });

      auto bobEvents2 = std::async(std::launch::async, [&]()
      {
         PushToTalkSessionHandle bobPtt = 0;
         assertPttIncomingCallEx2(bob, bobPtt, aliceServiceSettings.localIdentities[0].userName, aliceServiceSettings.localIdentities[0].displayName, bobPttService);
         bobPttManager->accept(bobPtt);
         assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Initiated);
         assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Active);
         assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Talking);
         assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Ending);
         assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Idle);
      });

      waitFor2(aliceEvents2, bobEvents2);

      safeCout("======= attempt " << i << " complete ======");
   }
#ifdef ANDROID
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));
   ASSERT_EQ(fdMon.fdsOverThold(), 0);
#endif

   alicePttManager->shutdownService(alicePttService);
   bobPttManager->shutdownService(bobPttService);
   assertPttServiceShutdownComplete(alice, alicePttService);
   assertPttServiceShutdownComplete(bob, bobPttService);
}

void PttTestHelper::callOneToOne(
   PushToTalkSettingsInternal& aliceInternalSettings, PushToTalkServiceSettings& aliceServiceSettings,
   PushToTalkSettingsInternal& bobInternalSettings, PushToTalkServiceSettings& bobServiceSettings,
   PushToTalkSettingsInternal& maxInternalSettings, PushToTalkServiceSettings& maxServiceSettings,
   const CPCAPI2::ConnectionPreferences& connectionPrefs)
{
   // Alice is the PTT sender/initiator
   TestAccount alice("alice", Account_NoInit);
   alice.config.phoneInitConnectionPrefs = connectionPrefs;
   alice.init();
   

   PushToTalkManager* alicePttManager = PushToTalkManager::getInterface(alice.phone);
   PushToTalkServiceHandle alicePttService = alicePttManager->createPttService();
   alicePttManager->setHandler(alicePttService, (PushToTalkHandlerInternal*)0xDEADBEEF);
   alicePttManager->configureService(alicePttService, aliceServiceSettings);
   PushToTalkManagerInternal* alicePttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(alicePttManager);

   // Bob is a PTT recipient
   TestAccount bob("bob", Account_NoInit);
   bob.config.phoneInitConnectionPrefs = connectionPrefs;
   bob.init();
   
   PushToTalkManager* bobPttManager = PushToTalkManager::getInterface(bob.phone);
   PushToTalkServiceHandle bobPttService = bobPttManager->createPttService();
   bobPttManager->setHandler(bobPttService, (PushToTalkHandlerInternal*)0xDEADBEEF);
   bobPttManager->configureService(bobPttService, bobServiceSettings);
   PushToTalkManagerInternal* bobPttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(bobPttManager);

   // Max is a PTT recipient
   TestAccount max("max", Account_NoInit);
   max.config.phoneInitConnectionPrefs = connectionPrefs;
   max.init();
   
   PushToTalkManager* maxPttManager = PushToTalkManager::getInterface(max.phone);
   PushToTalkServiceHandle maxPttService = maxPttManager->createPttService();
   maxPttManager->setHandler(maxPttService, (PushToTalkHandlerInternal*)0xDEADBEEF);
   maxPttManager->configureService(maxPttService, maxServiceSettings);
   PushToTalkManagerInternal* maxPttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(maxPttManager);

   alicePttManagerInternal->setPttInternalSettings(alicePttService, aliceInternalSettings);
   bobPttManagerInternal->setPttInternalSettings(bobPttService, bobInternalSettings);
   maxPttManagerInternal->setPttInternalSettings(maxPttService, maxInternalSettings);
   alicePttManager->startService(alicePttService);
   bobPttManager->startService(bobPttService);
   maxPttManager->startService(maxPttService);

   auto aliceStartupEvents = std::async(std::launch::async, [&]()
   {
      assertPttServiceStatusChanged(alice, alicePttService, CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Connecting);
      assertPttServiceStatusChanged(alice, alicePttService, CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Connected);
      assertPttServiceStatusChanged(alice, alicePttService, CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Ready);
   });

   auto bobStartupEvents = std::async(std::launch::async, [&]()
   {
      assertPttServiceStatusChanged(bob, bobPttService, CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Connecting);
      assertPttServiceStatusChanged(bob, bobPttService, CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Connected);
      assertPttServiceStatusChanged(bob, bobPttService, CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Ready);
   });

   auto maxStartupEvents = std::async(std::launch::async, [&]()
   {
      assertPttServiceStatusChanged(max, maxPttService, CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Connecting);
      assertPttServiceStatusChanged(max, maxPttService, CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Connected);
      assertPttServiceStatusChanged(max, maxPttService, CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Ready);
   });

   waitFor3(aliceStartupEvents, bobStartupEvents, maxStartupEvents);

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      PushToTalkSessionHandle alicePtt = alicePttManager->createPttSession(alicePttService);
      alicePttManager->addRecipient(alicePtt, bobServiceSettings.localIdentities[0]); // Triggers a one to one call
      alicePttManager->start(alicePtt);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Initiated);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Active);

      // Initiate talk spurt
      alicePttManager->startTalkSpurt(alicePtt);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Talking);
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      alicePttManager->endTalkSpurt(alicePtt);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Active);
      alicePttManager->end(alicePtt);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Ending);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Idle);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      PushToTalkSessionHandle bobPtt = 0;
      assertPttIncomingCallEx(bob, bobPtt, aliceServiceSettings.localIdentities[0].userName, aliceServiceSettings.localIdentities[0].displayName);
      bobPttManager->accept(bobPtt);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Initiated);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Active);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Talking);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Ending);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Idle);
   });

   auto maxEvents = std::async(std::launch::async, [&] ()
   {
      // assertPttStatisticsEvent(max, maxPttService, aliceServiceSettings.senderIdentity.userName, PttStatisticsEventType_UnicastDiscard);
      assertPttSessionError(max, maxPttService, PttSessionError_UnicastDiscard);
   });

   waitFor3(aliceEvents, bobEvents, maxEvents);

   alicePttManager->shutdownService(alicePttService);
   bobPttManager->shutdownService(bobPttService);
   maxPttManager->shutdownService(maxPttService);
   assertPttServiceShutdownComplete(alice, alicePttService);
   assertPttServiceShutdownComplete(bob, bobPttService);
   assertPttServiceShutdownComplete(max, maxPttService);
}

void PttTestHelper::callChannelOverride(
   PushToTalkSettingsInternal& aliceInternalSettings, PushToTalkServiceSettings& aliceServiceSettings,
   PushToTalkSettingsInternal& bobInternalSettings, PushToTalkServiceSettings& bobServiceSettings,
   PushToTalkSettingsInternal& maxInternalSettings, PushToTalkServiceSettings& maxServiceSettings)
{
   // Alice is the PTT sender/initiator
   TestAccount alice("alice", Account_Init);

   PushToTalkManager* alicePttManager = PushToTalkManager::getInterface(alice.phone);
   PushToTalkServiceHandle alicePttService = alicePttManager->createPttService();
   alicePttManager->setHandler(alicePttService, (PushToTalkHandlerInternal*)0xDEADBEEF);
   alicePttManager->configureService(alicePttService, aliceServiceSettings);
   PushToTalkManagerInternal* alicePttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(alicePttManager);
   aliceInternalSettings.unicastInitRetryIntervalMsecs = 20000;

   // Bob is a PTT recipient
   TestAccount bob("bob", Account_Init);
   PushToTalkManager* bobPttManager = PushToTalkManager::getInterface(bob.phone);
   PushToTalkServiceHandle bobPttService = bobPttManager->createPttService();
   bobPttManager->setHandler(bobPttService, (PushToTalkHandlerInternal*)0xDEADBEEF);
   bobPttManager->configureService(bobPttService, bobServiceSettings);
   PushToTalkManagerInternal* bobPttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(bobPttManager);
   bobInternalSettings.channelOverrideDurationMs = 10000;
   bobInternalSettings.unicastInitRetryIntervalMsecs = 20000;

   // Max is a PTT recipient
   TestAccount max("max", Account_Init);
   PushToTalkManager* maxPttManager = PushToTalkManager::getInterface(max.phone);
   PushToTalkServiceHandle maxPttService = maxPttManager->createPttService();
   maxPttManager->setHandler(maxPttService, (PushToTalkHandlerInternal*)0xDEADBEEF);
   maxPttManager->configureService(maxPttService, maxServiceSettings);
   PushToTalkManagerInternal* maxPttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(maxPttManager);

   alicePttManagerInternal->setPttInternalSettings(alicePttService, aliceInternalSettings);
   bobPttManagerInternal->setPttInternalSettings(bobPttService, bobInternalSettings);
   maxPttManagerInternal->setPttInternalSettings(maxPttService, maxInternalSettings);

   auto aliceEvents = std::async(std::launch::async, [&]()
   {
      alicePttManager->startService(alicePttService);
      assertPttServiceStartupCompleteLan(alice, alicePttService);
   });

   auto bobEvents = std::async(std::launch::async, [&]()
   {
      bobPttManager->startService(bobPttService);
      assertPttServiceStartupCompleteLan(bob, bobPttService);
   });

   auto maxEvents = std::async(std::launch::async, [&]()
   {
      maxPttManager->startService(maxPttService);
      assertPttServiceStartupCompleteLan(max, maxPttService);
   });

   waitFor3(aliceEvents, bobEvents, maxEvents);

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   PushToTalkSessionHandle alicePtt = 0;
   auto aliceEvents1 = std::async(std::launch::async, [&] ()
   {
      alicePtt = alicePttManager->createPttSession(alicePttService);
      alicePttManager->setChannel(alicePtt, "channel01");
      alicePttManager->start(alicePtt);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Initiated);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Active);

      // Initiate talk spurt
      alicePttManager->startTalkSpurt(alicePtt);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Talking);
   });

   PushToTalkSessionHandle bobPtt = 0;
   auto bobEvents1 = std::async(std::launch::async, [&] ()
   {
      assertPttIncomingCallEx(bob, bobPtt, aliceServiceSettings.localIdentities[0].userName, aliceServiceSettings.localIdentities[0].displayName);
      bobPttManager->accept(bobPtt);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Initiated);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Active);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Talking);
   });

   PushToTalkSessionHandle maxPtt = 0;
   auto maxEvents1 = std::async(std::launch::async, [&]()
   {
   });

   waitFor3(aliceEvents1, bobEvents1, maxEvents1);

   auto aliceEvents2 = std::async(std::launch::async, [&] ()
   {
      assertPttReceiverDisconnected(alice, alicePtt, "channel01", PttSessionState_Talking, 0, 1);
      alicePttManager->end(alicePtt);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Ending);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Idle);
   });

   auto bobEvents2 = std::async(std::launch::async, [&] ()
   {
      assertPttSessionError(bob, bobPtt, PttSessionError_ChannelOverride);

      PushToTalkSessionHandle bobPtt2 = 0;
      assertPttIncomingCallEx(bob, bobPtt2, maxServiceSettings.localIdentities[0].userName, maxServiceSettings.localIdentities[0].displayName);
      bobPttManager->accept(bobPtt2);
      assertPttSessionStateChanged(bob, bobPtt2, PttSessionState_Initiated);
      assertPttSessionStateChanged(bob, bobPtt2, PttSessionState_Active);
      assertPttSessionStateChanged(bob, bobPtt2, PttSessionState_Talking);
      assertPttSessionStateChanged(bob, bobPtt2, PttSessionState_Ending);
      assertPttSessionStateChanged(bob, bobPtt2, PttSessionState_Idle);
   });

   auto maxEvents2 = std::async(std::launch::async, [&] ()
   {
      maxPtt = maxPttManager->createPttSession(maxPttService);
      maxPttManager->setChannel(maxPtt, "channel01");
      maxPttManager->start(maxPtt);

      assertPttSessionStateChanged(max, maxPtt, PttSessionState_Initiated);
      assertPttSessionStateChanged(max, maxPtt, PttSessionState_Active);

      // Initiate talk spurt
      maxPttManager->startTalkSpurt(maxPtt);
      assertPttSessionStateChanged(max, maxPtt, PttSessionState_Talking);
      std::this_thread::sleep_for(std::chrono::milliseconds(3000));
      maxPttManager->endTalkSpurt(maxPtt);
      assertPttSessionStateChanged(max, maxPtt, PttSessionState_Active);
      maxPttManager->end(maxPtt);
      assertPttSessionStateChanged(max, maxPtt, PttSessionState_Ending);
      assertPttSessionStateChanged(max, maxPtt, PttSessionState_Idle);
   });

   waitFor3(aliceEvents2, bobEvents2, maxEvents2);
   
   alicePttManager->shutdownService(alicePttService);
   bobPttManager->shutdownService(bobPttService);
   maxPttManager->shutdownService(maxPttService);
   assertPttServiceShutdownComplete(alice, alicePttService);
   assertPttServiceShutdownComplete(bob, bobPttService);
   assertPttServiceShutdownComplete(max, maxPttService);
}

void PttTestHelper::callReject(
   PushToTalkSettingsInternal& aliceInternalSettings, PushToTalkServiceSettings& aliceServiceSettings,
   PushToTalkSettingsInternal& bobInternalSettings, PushToTalkServiceSettings& bobServiceSettings)
{
   // Alice is the PTT sender/initiator
   TestAccount alice("alice", Account_Init);

   PushToTalkManager* alicePttManager = PushToTalkManager::getInterface(alice.phone);
   PushToTalkServiceHandle alicePttService = alicePttManager->createPttService();
   alicePttManager->setHandler(alicePttService, (PushToTalkHandler*)0xDEADBEEF);
   alicePttManager->configureService(alicePttService, aliceServiceSettings);
   PushToTalkManagerInternal* alicePttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(alicePttManager);

   // Bob is a PTT recipient
   TestAccount bob("bob", Account_Init);
   PushToTalkManager* bobPttManager = PushToTalkManager::getInterface(bob.phone);
   PushToTalkServiceHandle bobPttService = bobPttManager->createPttService();
   bobPttManager->setHandler(bobPttService, (PushToTalkHandler*)0xDEADBEEF);
   bobPttManager->configureService(bobPttService, bobServiceSettings);
   PushToTalkManagerInternal* bobPttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(bobPttManager);

   alicePttManagerInternal->setPttInternalSettings(alicePttService, aliceInternalSettings);
   bobPttManagerInternal->setPttInternalSettings(bobPttService, bobInternalSettings);
   alicePttManager->startService(alicePttService);
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   bobPttManager->startService(bobPttService);

   assertPttServiceStartupCompleteLan(alice, alicePttService);
   assertPttServiceStartupCompleteLan(bob, bobPttService);
   
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   // Call reject right after receiving the initiated status
   for (int i = 0; i < 3; i++)
   {
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      auto aliceEvents = std::async(std::launch::async, [&] ()
      {
         PushToTalkSessionHandle alicePtt = alicePttManager->createPttSession(alicePttService);
         alicePttManager->setChannel(alicePtt, "channel01");
         alicePttManager->start(alicePtt);
         assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Initiated);
         assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Ending);
         assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Idle);
      });

      auto bobEvents = std::async(std::launch::async, [&] ()
      {
         PushToTalkSessionHandle bobPtt = 0;
         assertPttIncomingCall(bob, bobPtt);
         assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Initiated);
         bobPttManager->reject(bobPtt);
         assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Ending);
         assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Idle);
      });

      waitFor2(aliceEvents, bobEvents);
   }

   // Call reject right after receiving the incoming call event
   for (int i = 0; i < 3; i++)
   {
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      auto aliceEvents = std::async(std::launch::async, [&] ()
      {
         PushToTalkSessionHandle alicePtt = alicePttManager->createPttSession(alicePttService);
         alicePttManager->setChannel(alicePtt, "channel01");
         alicePttManager->start(alicePtt);
         assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Initiated);
         assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Ending);
         assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Idle);
      });

      auto bobEvents = std::async(std::launch::async, [&] ()
      {
         PushToTalkSessionHandle bobPtt = 0;
         assertPttIncomingCall(bob, bobPtt);
         bobPttManager->reject(bobPtt);
         assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Initiated);
         assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Ending);
         assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Idle);
      });

      waitFor2(aliceEvents, bobEvents);
   }

   alicePttManager->shutdownService(alicePttService);
   bobPttManager->shutdownService(bobPttService);
   assertPttServiceShutdownComplete(alice, alicePttService);
   assertPttServiceShutdownComplete(bob, bobPttService);
}

void PttTestHelper::callIncomingClientConnectionTimeout(
   PushToTalkSettingsInternal& aliceInternalSettings, PushToTalkServiceSettings& aliceServiceSettings,
   PushToTalkSettingsInternal& bobInternalSettings, PushToTalkServiceSettings& bobServiceSettings)
{
   // Alice is the PTT sender/initiator
   TestAccount alice("alice", Account_Init);

   PushToTalkManager* alicePttManager = PushToTalkManager::getInterface(alice.phone);
   PushToTalkServiceHandle alicePttService = alicePttManager->createPttService();
   alicePttManager->setHandler(alicePttService, (PushToTalkHandler*)0xDEADBEEF);
   alicePttManager->configureService(alicePttService, aliceServiceSettings);
   PushToTalkManagerInternal* alicePttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(alicePttManager);

   // Bob is a PTT recipient
   TestAccount bob("bob", Account_Init);
   PushToTalkManager* bobPttManager = PushToTalkManager::getInterface(bob.phone);
   PushToTalkServiceHandle bobPttService = bobPttManager->createPttService();
   bobPttManager->setHandler(bobPttService, (PushToTalkHandler*)0xDEADBEEF);
   bobPttManager->configureService(bobPttService, bobServiceSettings);
   PushToTalkManagerInternal* bobPttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(bobPttManager);
   
   alicePttManagerInternal->setPttInternalSettings(alicePttService, aliceInternalSettings);
   bobPttManagerInternal->setPttInternalSettings(bobPttService, bobInternalSettings);
   alicePttManager->startService(alicePttService);
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   bobPttManager->startService(bobPttService);
     
   std::this_thread::sleep_for(std::chrono::milliseconds(10000));

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      PushToTalkSessionHandle alicePtt = alicePttManager->createPttSession(alicePttService);
      alicePttManager->setChannel(alicePtt, "channel01");
      alicePttManager->start(alicePtt);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Initiated);
      assertPttSessionError(alice, alicePtt, PttSessionError_ConnectionTimeout);
      alicePttManager->end(alicePtt);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Ending);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Idle);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      PushToTalkSessionHandle bobPtt = 0;
      assertPttIncomingCall(bob, bobPtt);
      bobPttManager->accept(bobPtt);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Initiated);
      assertPttSessionError(bob, bobPtt, PttSessionError_ConnectionTimeout);
      bobPttManager->end(bobPtt);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Ending);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Idle);
   });

   waitFor2(aliceEvents, bobEvents);
     
   alicePttManager->shutdownService(alicePttService);
   bobPttManager->shutdownService(bobPttService);
   assertPttServiceShutdownComplete(alice, alicePttService);
   assertPttServiceShutdownComplete(bob, bobPttService);
}

void PttTestHelper::callIncomingOfferResponseTimeout(
   PushToTalkSettingsInternal& aliceInternalSettings, PushToTalkServiceSettings& aliceServiceSettings,
   PushToTalkSettingsInternal& bobInternalSettings, PushToTalkServiceSettings& bobServiceSettings)
{
   // Alice is the PTT sender/initiator
   TestAccount alice("alice", Account_Init);

   PushToTalkManager* alicePttManager = PushToTalkManager::getInterface(alice.phone);
   PushToTalkServiceHandle alicePttService = alicePttManager->createPttService();
   alicePttManager->setHandler(alicePttService, (PushToTalkHandler*)0xDEADBEEF);
   alicePttManager->configureService(alicePttService, aliceServiceSettings);
   PushToTalkManagerInternal* alicePttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(alicePttManager);

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   // Bob is a PTT recipient
   TestAccount bob("bob", Account_Init);
   PushToTalkManager* bobPttManager = PushToTalkManager::getInterface(bob.phone);
   PushToTalkServiceHandle bobPttService = bobPttManager->createPttService();
   bobPttManager->setHandler(bobPttService, (PushToTalkHandler*)0xDEADBEEF);
   bobPttManager->configureService(bobPttService, bobServiceSettings);
   PushToTalkManagerInternal* bobPttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(bobPttManager);

   alicePttManagerInternal->setPttInternalSettings(alicePttService, aliceInternalSettings);
   bobPttManagerInternal->setPttInternalSettings(bobPttService, bobInternalSettings);
   alicePttManager->startService(alicePttService);
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   bobPttManager->startService(bobPttService);

   std::this_thread::sleep_for(std::chrono::milliseconds(10000));

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      PushToTalkSessionHandle alicePtt = alicePttManager->createPttSession(alicePttService);
      alicePttManager->setChannel(alicePtt, "channel01");
      alicePttManager->start(alicePtt);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Initiated);
      std::this_thread::sleep_for(std::chrono::milliseconds(3000));
      alicePttManager->end(alicePtt);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Ending);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Idle);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      PushToTalkSessionHandle bobPtt = 0;
      assertPttIncomingCall(bob, bobPtt);
      bobPttManager->accept(bobPtt);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Initiated);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Active);
      assertPttSessionError(bob, bobPtt, PttSessionError_OfferResponseTimeout);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Ending);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Idle);
   });

   waitFor2(aliceEvents, bobEvents);

   alicePttManager->shutdownService(alicePttService);
   bobPttManager->shutdownService(bobPttService);
   assertPttServiceShutdownComplete(alice, alicePttService);
   assertPttServiceShutdownComplete(bob, bobPttService);
}

void PttTestHelper::callIncomingRetryConnectionSuccess(
   PushToTalkSettingsInternal& aliceInternalSettings, PushToTalkServiceSettings& aliceServiceSettings,
   PushToTalkSettingsInternal& bobInternalSettings, PushToTalkServiceSettings& bobServiceSettings)
{
   // Alice is the PTT sender/initiator
   TestAccount alice("alice", Account_Init);

   PushToTalkManager* alicePttManager = PushToTalkManager::getInterface(alice.phone);
   PushToTalkServiceHandle alicePttService = alicePttManager->createPttService();
   alicePttManager->setHandler(alicePttService, (PushToTalkHandler*)0xDEADBEEF);
   alicePttManager->configureService(alicePttService, aliceServiceSettings);
   PushToTalkManagerInternal* alicePttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(alicePttManager);

   // Bob is a PTT recipient
   TestAccount bob("bob", Account_Init);
   PushToTalkManager* bobPttManager = PushToTalkManager::getInterface(bob.phone);
   PushToTalkServiceHandle bobPttService = bobPttManager->createPttService();
   bobPttManager->setHandler(bobPttService, (PushToTalkHandler*)0xDEADBEEF);
   bobPttManager->configureService(bobPttService, bobServiceSettings);
   PushToTalkManagerInternal* bobPttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(bobPttManager);

   alicePttManagerInternal->setPttInternalSettings(alicePttService, aliceInternalSettings);
   bobPttManagerInternal->setPttInternalSettings(bobPttService, bobInternalSettings);
   alicePttManager->startService(alicePttService);
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   bobPttManager->startService(bobPttService);

   std::this_thread::sleep_for(std::chrono::milliseconds(10000));

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      PushToTalkSessionHandle alicePtt = alicePttManager->createPttSession(alicePttService);
      alicePttManager->setChannel(alicePtt, "channel01");
      alicePttManager->start(alicePtt);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Initiated);
      // std::this_thread::sleep_for(std::chrono::milliseconds(3000));
      // alicePttManagerInternal->enableWebsocketServer(alicePttService);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Active);
      std::this_thread::sleep_for(std::chrono::milliseconds(500));
      alicePttManager->end(alicePtt);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Ending);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Idle);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      PushToTalkSessionHandle bobPtt = 0;
      assertPttIncomingCall(bob, bobPtt);
      bobPttManager->accept(bobPtt);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Initiated);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Active);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Talking);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Ending);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Idle);
   });

   waitFor2(aliceEvents, bobEvents);

   alicePttManager->shutdownService(alicePttService);
   bobPttManager->shutdownService(bobPttService);
   assertPttServiceShutdownComplete(alice, alicePttService);
   assertPttServiceShutdownComplete(bob, bobPttService);
}

void PttTestHelper::callReceiverEnd(
   PushToTalkSettingsInternal& aliceInternalSettings, PushToTalkServiceSettings& aliceServiceSettings,
   PushToTalkSettingsInternal& bobInternalSettings, PushToTalkServiceSettings& bobServiceSettings)
{
   // Alice is the PTT sender/initiator
   TestAccount alice("alice", Account_Init);

   PushToTalkManager* alicePttManager = PushToTalkManager::getInterface(alice.phone);
   PushToTalkServiceHandle alicePttService = alicePttManager->createPttService();
   alicePttManager->setHandler(alicePttService, (PushToTalkHandler*)0xDEADBEEF);
   alicePttManager->configureService(alicePttService, aliceServiceSettings);
   PushToTalkManagerInternal* alicePttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(alicePttManager);

   // Bob is a PTT recipient
   TestAccount bob("bob", Account_Init);
   PushToTalkManager* bobPttManager = PushToTalkManager::getInterface(bob.phone);
   PushToTalkServiceHandle bobPttService = bobPttManager->createPttService();
   bobPttManager->setHandler(bobPttService, (PushToTalkHandler*)0xDEADBEEF);
   bobPttManager->configureService(bobPttService, bobServiceSettings);
   PushToTalkManagerInternal* bobPttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(bobPttManager);
   
   alicePttManagerInternal->setPttInternalSettings(alicePttService, aliceInternalSettings);
   bobPttManagerInternal->setPttInternalSettings(bobPttService, bobInternalSettings);

   alicePttManager->startService(alicePttService);
   bobPttManager->startService(bobPttService);

   auto aliceStartEvents = std::async(std::launch::async, [&]()
   {
      assertPttServiceStatusChanged(alice, alicePttService, CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Connecting);
      assertPttServiceStatusChanged(alice, alicePttService, CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Connected);
      assertPttServiceStatusChanged(alice, alicePttService, CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Ready);
   });

   auto bobStartEvents = std::async(std::launch::async, [&]()
   {
      assertPttServiceStatusChanged(bob, bobPttService, CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Connecting);
      assertPttServiceStatusChanged(bob, bobPttService, CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Connected);
      assertPttServiceStatusChanged(bob, bobPttService, CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Ready);
   });

   waitFor2(aliceStartEvents, bobStartEvents);

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   for (int i = 0; i < 3; i++)
   {
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      auto aliceEvents2 = std::async(std::launch::async, [&] ()
      {
         PushToTalkSessionHandle alicePtt = alicePttManager->createPttSession(alicePttService);
         alicePttManager->setChannel(alicePtt, "channel01");
         alicePttManager->start(alicePtt);
         assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Initiated);
         assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Active);

         // Initiate talk spurt
         alicePttManager->startTalkSpurt(alicePtt);
         assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Talking);
         assertPttReceiverDisconnected(alice, alicePtt, "channel01", PttSessionState_Talking, 0, 1);
         alicePttManager->end(alicePtt);
         assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Ending);
         assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Idle);
      });

      auto bobEvents2 = std::async(std::launch::async, [&] ()
      {
         PushToTalkSessionHandle bobPtt = 0;
         assertPttIncomingCall(bob, bobPtt);
         bobPttManager->accept(bobPtt);
         assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Initiated);
         assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Active);
         assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Talking);
         std::this_thread::sleep_for(std::chrono::milliseconds(2000));
         bobPttManager->end(bobPtt);
         assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Ending);
         assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Idle);
      });

      waitFor2(aliceEvents2, bobEvents2);
   }

   alicePttManager->shutdownService(alicePttService);
   bobPttManager->shutdownService(bobPttService);
   assertPttServiceShutdownComplete(alice, alicePttService);
   assertPttServiceShutdownComplete(bob, bobPttService);
}

void PttTestHelper::callTimeoutNoSpurt(
   PushToTalkSettingsInternal& aliceInternalSettings, PushToTalkServiceSettings& aliceServiceSettings,
   PushToTalkSettingsInternal& bobInternalSettings, PushToTalkServiceSettings& bobServiceSettings)
{
   // Alice is the PTT sender/initiator
   TestAccount alice("alice", Account_Init);

   PushToTalkManager* alicePttManager = PushToTalkManager::getInterface(alice.phone);
   PushToTalkServiceHandle alicePttService = alicePttManager->createPttService();
   alicePttManager->setHandler(alicePttService, (PushToTalkHandler*)0xDEADBEEF);
   alicePttManager->configureService(alicePttService, aliceServiceSettings);
   PushToTalkManagerInternal* alicePttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(alicePttManager);

   // Bob is a PTT recipient
   TestAccount bob("bob", Account_Init);
   PushToTalkManager* bobPttManager = PushToTalkManager::getInterface(bob.phone);
   PushToTalkServiceHandle bobPttService = bobPttManager->createPttService();
   bobPttManager->setHandler(bobPttService, (PushToTalkHandler*)0xDEADBEEF);
   bobPttManager->configureService(bobPttService, bobServiceSettings);
   PushToTalkManagerInternal* bobPttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(bobPttManager);

   alicePttManagerInternal->setPttInternalSettings(alicePttService, aliceInternalSettings);
   bobPttManagerInternal->setPttInternalSettings(bobPttService, bobInternalSettings);

   alicePttManager->startService(alicePttService);
   bobPttManager->startService(bobPttService);

   auto aliceStartEvents = std::async(std::launch::async, [&]()
   {
      assertPttServiceStatusChanged(alice, alicePttService, CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Connecting);
      assertPttServiceStatusChanged(alice, alicePttService, CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Connected);
      assertPttServiceStatusChanged(alice, alicePttService, CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Ready);
   });

   auto bobStartEvents = std::async(std::launch::async, [&]()
   {
      assertPttServiceStatusChanged(bob, bobPttService, CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Connecting);
      assertPttServiceStatusChanged(bob, bobPttService, CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Connected);
      assertPttServiceStatusChanged(bob, bobPttService, CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Ready);
   });

   waitFor2(aliceStartEvents, bobStartEvents);

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   PushToTalkSessionHandle alicePtt = alicePttManager->createPttSession(alicePttService);

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      alicePttManager->start(alicePtt);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Initiated);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Active);
   });

   PushToTalkSessionHandle bobPtt = 0;
   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      assertPttIncomingCall(bob, bobPtt);
      bobPttManager->accept(bobPtt);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Initiated);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Active);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Talking);
   });

   waitFor2(aliceEvents, bobEvents);

   auto aliceEndEvents = std::async(std::launch::async, [&] ()
   {
      // As no talk spurt is initiated, ptt session will timeout due to session expiry
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Ending);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Idle);
   });

   auto bobEndEvents = std::async(std::launch::async, [&] ()
   {
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Ending);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Idle);
   });

   waitFor2(aliceEndEvents, bobEndEvents);

   alicePttManager->shutdownService(alicePttService);
   bobPttManager->shutdownService(bobPttService);
   assertPttServiceShutdownComplete(alice, alicePttService);
   assertPttServiceShutdownComplete(bob, bobPttService);
}

void PttTestHelper::callConnectionTimeout(
   PushToTalkSettingsInternal& aliceInternalSettings, PushToTalkServiceSettings& aliceServiceSettings)
{
   // Alice is the PTT sender/initiator
   TestAccount alice("alice", Account_Init);

   PushToTalkManager* alicePttManager = PushToTalkManager::getInterface(alice.phone);
   PushToTalkServiceHandle alicePttService = alicePttManager->createPttService();
   alicePttManager->setHandler(alicePttService, (PushToTalkHandler*)0xDEADBEEF);
   alicePttManager->configureService(alicePttService, aliceServiceSettings);
   PushToTalkManagerInternal* alicePttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(alicePttManager);

   alicePttManagerInternal->setPttInternalSettings(alicePttService, aliceInternalSettings);
   alicePttManager->startService(alicePttService);
   assertPttServiceStartupCompleteLan(alice, alicePttService);

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   PushToTalkSessionHandle alicePtt = alicePttManager->createPttSession(alicePttService);
   PttIdentity identity;
   identity.userName = "<EMAIL>";
   identity.displayName = identity.userName;
   
   // alicePttManager->addRecipient(alicePtt, identity);
   alicePttManager->start(alicePtt);
   assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Initiated);

   // As no connection responses are received, ptt session will timeout
   assertPttSessionError(alice, alicePtt, PttSessionError_ConnectionTimeout);
   assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Ending);
   assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Idle);
   
   alicePttManager->shutdownService(alicePttService);
   assertPttServiceShutdownComplete(alice, alicePttService);
}

void PttTestHelper::callMultipleRecipients(bool unicast, int recipientCount,
   PushToTalkSettingsInternal& aliceInternalSettings, PushToTalkServiceSettings& aliceServiceSettings)
{
   // Alice is the PTT sender/initiator
   TestAccount alice("alice", Account_Init);

   PushToTalkManager* alicePttManager = PushToTalkManager::getInterface(alice.phone);
   PushToTalkManagerInternal* alicePttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(alicePttManager);
   PushToTalkServiceHandle alicePttService = alicePttManager->createPttService();
   alicePttManager->setHandler(alicePttService, (PushToTalkHandler*)0xDEADBEEF);
   alicePttManager->configureService(alicePttService, aliceServiceSettings);
   alicePttManagerInternal->setPttInternalSettings(alicePttService, aliceInternalSettings);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   std::vector<std::shared_ptr<TestAccount>> recipientAccounts;
   std::vector< PushToTalkServiceHandle> recipientServiceHandles;
   std::vector<PushToTalkSessionHandle> recipientHandles;
   for (int i = 0; i < recipientCount; ++i)
   {
      std::stringstream ss;
      ss << "recipient_" << i;
      std::shared_ptr<TestAccount> account = std::make_shared<TestAccount>(ss.str(), Account_Init);
      PushToTalkManager* pttManager = PushToTalkManager::getInterface(account->phone);
      PushToTalkManagerInternal* pttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(pttManager);
      PushToTalkServiceHandle pttService = pttManager->createPttService();
      recipientServiceHandles.push_back(pttService);
      pttManager->setHandler(pttService, (PushToTalkHandler*)0xDEADBEEF);
      PttIdentity userIdentity(PushToTalk::PttIdentityType_XMPP, "<EMAIL>", "Bob");
      PushToTalkServiceSettings serviceSettings(userIdentity, aliceServiceSettings.unicastPort);

      const int ipOffset = 1;
      serviceSettings.unicastBindAddress = (resip::Data::from("127.0.0.") + resip::Data::from(i + 1 + ipOffset)).c_str(); // + 1 to account for i starting at 0
      serviceSettings.unicastIpRanges = PttTestHelper::getUnicastIpRange(recipientCount + ipOffset);
      serviceSettings.mediaInactivityIntervalSeconds = aliceServiceSettings.mediaInactivityIntervalSeconds;
      PushToTalkSettingsInternal internalSettings;
      internalSettings.incomingSetupMsecs = 10000;

      pttManager->configureService(pttService, serviceSettings);
      pttManagerInternal->setPttInternalSettings(pttService, internalSettings);

      pttManager->startService(pttService);
      assertPttServiceStartupCompleteLan(*account, pttService);
      recipientAccounts.push_back(account);
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   }

   alicePttManager->startService(alicePttService);
   assertPttServiceStartupCompleteLan(alice, alicePttService);
   std::this_thread::sleep_for(std::chrono::milliseconds(10000));

   PushToTalkSessionHandle alicePtt = alicePttManager->createPttSession(alicePttService);
   auto aliceStartEvents = std::async(std::launch::async, [&] ()
   {
      PttIdentity identity;
      identity.userName = "<EMAIL>";
      identity.displayName = identity.userName;

      // alicePttManager->addRecipient(alicePtt, identity);
      alicePttManager->start(alicePtt);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Initiated);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Active);
   });

   std::atomic<int> startedCount(0);
   for (int i = 0; i < recipientAccounts.size(); ++i)
   {
      auto userEvents = std::async(std::launch::async, [&] (int recipientIndex)
      {
         std::stringstream ss;
         ss << "recipient_" << recipientIndex;
         TestAccount* user = recipientAccounts[recipientIndex].get();
         PushToTalkManager* userPttManager = PushToTalkManager::getInterface(user->phone);
         PushToTalkSessionHandle userPtt = 0;
         assertPttIncomingCall(*user, userPtt);
         recipientHandles.push_back(userPtt);
         safeCout("PttTestHelper::PttCallMulticastMultipleRecipients(): setting up incoming call for " << ss.str().c_str() << " with ptt handle: " << userPtt);
         userPttManager->accept(userPtt);
         assertPttSessionStateChanged(*user, userPtt, PttSessionState_Initiated);
         assertPttSessionStateChanged(*user, userPtt, PttSessionState_Active);
         assertPttSessionStateChanged(*user, userPtt, PttSessionState_Talking);
         startedCount++;
      }, i);
   }

   auto userStartEvents = std::async(std::launch::async, [&] ()
   {
      // Ensure atleast half have responded
      bool deemConnected = ((((double)startedCount / (double)recipientAccounts.size()) * 100) >= (double)50);
      while (!deemConnected)
      {
         safeCout("PttTestHelper::PttCallMulticastMultipleRecipients(): have triggered " << startedCount << " ptt accepts out of total: " << recipientAccounts.size());
         std::this_thread::sleep_for(std::chrono::milliseconds(250));
      }
   });

   waitFor2(aliceStartEvents, userStartEvents);

   // Initiate talk spurt
   alicePttManager->startTalkSpurt(alicePtt);
   assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Talking);
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alicePttManager->endTalkSpurt(alicePtt);
   assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Active);

   std::this_thread::sleep_for(std::chrono::milliseconds(500));

   auto aliceEndEvents = std::async(std::launch::async, [&] ()
   {
      alicePttManager->end(alicePtt);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Ending);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Idle);
   });

   auto userEndEvents = std::async(std::launch::async, [&] ()
   {
      for (int i = 0; i < recipientAccounts.size(); ++i)
      {
         TestAccount* user = recipientAccounts[i].get();
         auto userEvents = std::async(std::launch::async, [&] ()
         {
            PushToTalkSessionHandle userPtt = recipientHandles[i];
            assertPttSessionStateChanged(*user, userPtt, PttSessionState_Ending);
            assertPttSessionStateChanged(*user, userPtt, PttSessionState_Idle);
         });
      }
   });

   waitFor2(aliceEndEvents, userEndEvents);

   for (int i = 0; i < recipientAccounts.size(); ++i)
   {
      TestAccount* user = recipientAccounts[i].get();
      PushToTalkManager* pttManager = PushToTalkManager::getInterface(user->phone);
      pttManager->shutdownService(recipientServiceHandles[i]);
      assertPttServiceShutdownComplete(*user, recipientServiceHandles[i]);
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   }

   alicePttManager->shutdownService(alicePttService);
   assertPttServiceShutdownComplete(alice, alicePttService);
}

#if (CPCAPI2_BRAND_NETWORK_CHANGE_MODULE == 1)

void PttTestHelper::networkChangeTest(
   PushToTalkSettingsInternal& aliceInternalSettings, PushToTalkServiceSettings& aliceServiceSettings,
   PushToTalkSettingsInternal& bobInternalSettings, PushToTalkServiceSettings& bobServiceSettings)
{
   // Alice is the PTT sender/initiator
   TestAccount alice("alice", Account_Init);
   CPCAPI2::NetworkChangeManager_Mock* alicenetwork = dynamic_cast<NetworkChangeManagerInterface*>(NetworkChangeManager::getInterface(alice.phone))->getMockImpl();

   PushToTalkManager* alicePttManager = PushToTalkManager::getInterface(alice.phone);
   PushToTalkServiceHandle alicePttService = alicePttManager->createPttService();
   alicePttManager->setHandler(alicePttService, (PushToTalkHandler*)0xDEADBEEF);
   alicePttManager->configureService(alicePttService, aliceServiceSettings);
   PushToTalkManagerInternal* alicePttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(alicePttManager);

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   // Bob is a PTT recipient
   TestAccount bob("bob", Account_Init);

   CPCAPI2::NetworkChangeManager_Mock* bobnetwork = dynamic_cast<NetworkChangeManagerInterface*>(NetworkChangeManager::getInterface(bob.phone))->getMockImpl();

   PushToTalkManager* bobPttManager = PushToTalkManager::getInterface(bob.phone);
   PushToTalkServiceHandle bobPttService = bobPttManager->createPttService();
   bobPttManager->setHandler(bobPttService, (PushToTalkHandler*)0xDEADBEEF);
   bobPttManager->configureService(bobPttService, bobServiceSettings);
   PushToTalkManagerInternal* bobPttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(bobPttManager);

   alicePttManagerInternal->setPttInternalSettings(alicePttService, aliceInternalSettings);
   bobPttManagerInternal->setPttInternalSettings(bobPttService, bobInternalSettings);

   alicePttManager->startService(alicePttService);
   bobPttManager->startService(bobPttService);

   auto aliceStartupEvents = std::async(std::launch::async, [&]()
   {
      assertPttServiceStatusChanged(alice, alicePttService, CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Connecting);
      assertPttServiceStatusChanged(alice, alicePttService, CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Connected);
      assertPttServiceStatusChanged(alice, alicePttService, CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Ready);
   });

   auto bobStartupEvents = std::async(std::launch::async, [&]()
   {
      assertPttServiceStatusChanged(bob, bobPttService, CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Connecting);
      assertPttServiceStatusChanged(bob, bobPttService, CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Connected);
      assertPttServiceStatusChanged(bob, bobPttService, CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Ready);
   });

   waitFor2(aliceStartupEvents, bobStartupEvents);

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   for (int i = 0; i < 3; i++)
   {
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      auto aliceEvents = std::async(std::launch::async, [&]()
      {
         PushToTalkSessionHandle alicePtt = alicePttManager->createPttSession(alicePttService);
         PttIdentity identity;
         identity.userName = "<EMAIL>";
         identity.displayName = identity.userName;

         // alicePttManager->addRecipient(alicePtt, identity);
         alicePttManager->setChannel(alicePtt, "channel01");
         alicePttManager->start(alicePtt);
         assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Initiated);
         assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Active);

         // Initiate talk spurt
         alicePttManager->startTalkSpurt(alicePtt);
         assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Talking);
         std::this_thread::sleep_for(std::chrono::milliseconds(1000));
         alicePttManager->endTalkSpurt(alicePtt);
         assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Active);

         alicePttManager->end(alicePtt);
         assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Ending);
         assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Idle);
      });

      auto bobEvents = std::async(std::launch::async, [&]()
      {
         PushToTalkSessionHandle bobPtt = 0;
         assertPttIncomingCallEx(bob, bobPtt, aliceServiceSettings.localIdentities[0].userName, aliceServiceSettings.localIdentities[0].displayName);
         bobPttManager->accept(bobPtt);
         assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Initiated);
         assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Active);
         assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Talking);
         assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Ending);
         assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Idle);
      });
      waitFor2(aliceEvents, bobEvents);
      safeCout("======= attempt " << i << " complete ======");
   }

   // Disable network

   std::cout << "PttModuleTest: disconnectNetwork";
   if (TestEnvironmentConfig::dockerContainerized())
   {
      ASSERT_EQ(0, test::NetworkUtils::setDockerContainerNetworkEnabled(false));
      ASSERT_EQ(0, test::NetworkUtils::shutdownTcpSockets());
   }
   alicenetwork->setNetworkTransport(NetworkTransport::TransportNone);
   bobnetwork->setNetworkTransport(NetworkTransport::TransportNone);
   std::set<resip::Data> ifaces;
   alicenetwork->setMockInterfaces(ifaces);
   bobnetwork->setMockInterfaces(ifaces);

   auto aliceServiceDownEvents = std::async(std::launch::async, [&]()
   {
      CPCAPI2::PushToTalk::PushToTalkServiceHandle h;
      CPCAPI2::PushToTalk::PttServiceStatusChangedEvent evt;
      ASSERT_FALSE(alice.pttEvents->expectEvent(__LINE__, "PushToTalkHandler::onPttServiceStatusChanged", 10000, HandleEqualsPred<CPCAPI2::PushToTalk::PushToTalkServiceHandle>(alicePttService), h, evt));
      // assertPttServiceStatusChanged(alice, alicePttService, CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Disconnecting);
      // assertPttServiceStatusChanged(alice, alicePttService, CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Disconnected);
   });

   auto bobServiceDownEvents = std::async(std::launch::async, [&]()
   {
      CPCAPI2::PushToTalk::PushToTalkServiceHandle h;
      CPCAPI2::PushToTalk::PttServiceStatusChangedEvent evt;
      ASSERT_FALSE(bob.pttEvents->expectEvent(__LINE__, "PushToTalkHandler::onPttServiceStatusChanged", 10000, HandleEqualsPred<CPCAPI2::PushToTalk::PushToTalkServiceHandle>(bobPttService), h, evt));
      // assertPttServiceStatusChanged(bob, bobPttService, CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Disconnecting);
      // assertPttServiceStatusChanged(bob, bobPttService, CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Disconnected);
   });

   waitFor2(aliceServiceDownEvents, bobServiceDownEvents);

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   // Enable network

   if (TestEnvironmentConfig::dockerContainerized())
   {
      ASSERT_EQ(0, test::NetworkUtils::setDockerContainerNetworkEnabled(true));
   }
   alicenetwork->setNetworkTransport(NetworkTransport::TransportWiFi);
   bobnetwork->setNetworkTransport(NetworkTransport::TransportWiFi);
   ifaces.insert("10.0.0.2");
   alicenetwork->setMockInterfaces(ifaces);
   bobnetwork->setMockInterfaces(ifaces);

   auto aliceServiceUpEvents = std::async(std::launch::async, [&]()
   {
      CPCAPI2::PushToTalk::PushToTalkServiceHandle h;
      CPCAPI2::PushToTalk::PttServiceStatusChangedEvent evt;
      ASSERT_FALSE(alice.pttEvents->expectEvent(__LINE__, "PushToTalkHandler::onPttServiceStatusChanged", 10000, HandleEqualsPred<CPCAPI2::PushToTalk::PushToTalkServiceHandle>(alicePttService), h, evt));
      // assertPttServiceStatusChanged(alice, alicePttService, CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Connecting);
      // assertPttServiceStatusChanged(alice, alicePttService, CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Connected);
      // assertPttServiceStatusChanged(alice, alicePttService, CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Ready);
   });

   auto bobServiceUpEvents = std::async(std::launch::async, [&]()
   {
      CPCAPI2::PushToTalk::PushToTalkServiceHandle h;
      CPCAPI2::PushToTalk::PttServiceStatusChangedEvent evt;
      ASSERT_FALSE(bob.pttEvents->expectEvent(__LINE__, "PushToTalkHandler::onPttServiceStatusChanged", 10000, HandleEqualsPred<CPCAPI2::PushToTalk::PushToTalkServiceHandle>(bobPttService), h, evt));
      // assertPttServiceStatusChanged(bob, bobPttService, CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Connecting);
      // assertPttServiceStatusChanged(bob, bobPttService, CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Connected);
      // assertPttServiceStatusChanged(bob, bobPttService, CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Ready);
   });

   waitFor2(aliceServiceUpEvents, bobServiceUpEvents);

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   for (int i = 0; i < 3; i++)
   {
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      auto aliceEvents = std::async(std::launch::async, [&]()
      {
         PushToTalkSessionHandle alicePtt = alicePttManager->createPttSession(alicePttService);
         PttIdentity identity;
         identity.userName = "<EMAIL>";
         identity.displayName = identity.userName;

         alicePttManager->setChannel(alicePtt, "channel01");
         alicePttManager->start(alicePtt);
         assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Initiated);
         assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Active);

         // Initiate talk spurt
         alicePttManager->startTalkSpurt(alicePtt);
         assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Talking);
         std::this_thread::sleep_for(std::chrono::milliseconds(1000));
         alicePttManager->endTalkSpurt(alicePtt);
         assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Active);

         alicePttManager->end(alicePtt);
         assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Ending);
         assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Idle);
      });

      auto bobEvents = std::async(std::launch::async, [&]()
      {
         PushToTalkSessionHandle bobPtt = 0;
         assertPttIncomingCallEx(bob, bobPtt, aliceServiceSettings.localIdentities[0].userName, aliceServiceSettings.localIdentities[0].displayName);
         bobPttManager->accept(bobPtt);
         assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Initiated);
         assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Active);
         assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Talking);
         assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Ending);
         assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Idle);
      });

      waitFor2(aliceEvents, bobEvents);
      safeCout("======= attempt " << i << " complete ======");
   }

   alicePttManager->shutdownService(alicePttService);
   bobPttManager->shutdownService(bobPttService);
   assertPttServiceShutdownComplete(alice, alicePttService);
   assertPttServiceShutdownComplete(bob, bobPttService);
}

#endif //CPCAPI2_BRAND_NETWORK_CHANGE_MODULE == 1

void PttTestHelper::call_server_based(TestAccount& alice, PushToTalkServiceHandle alicePttService, TestAccount& bob, PushToTalkServiceHandle bobPttService, std::string channel)
{
   PushToTalkManager* alicePttManager = PushToTalkManager::getInterface(alice.phone);
   PushToTalkManager* bobPttManager = PushToTalkManager::getInterface(bob.phone);
   ASSERT_GT(alice.pttSettings.localIdentities.size(), 0);
   ASSERT_GT(bob.pttSettings.localIdentities.size(), 0);
   PttIdentity aliceIdentity = alice.pttSettings.localIdentities[0];
   PttIdentity bobIdentity = bob.pttSettings.localIdentities[0];

   auto aliceEvents = std::async(std::launch::async, [&]()
   {
      PushToTalkSessionHandle alicePtt = alicePttManager->createPttSession(alicePttService);
      alicePttManager->setChannel(alicePtt, channel.c_str());
      alicePttManager->start(alicePtt);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Initiated);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Active);

      // Initiate talk spurt
      alicePttManager->startTalkSpurt(alicePtt);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Talking);
      std::this_thread::sleep_for(std::chrono::milliseconds(3000));
      alicePttManager->endTalkSpurt(alicePtt);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Active);
      alicePttManager->end(alicePtt);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Ending);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Idle);
   });

   auto bobEvents = std::async(std::launch::async, [&]()
   {
      PushToTalkSessionHandle bobPtt = 0;
      assertPttIncomingCallEx(bob, bobPtt, aliceIdentity.userName, aliceIdentity.displayName);
      bobPttManager->accept(bobPtt);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Initiated);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Active);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Talking);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Ending);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Idle);
   });

   waitFor2(aliceEvents, bobEvents);
}

void PttTestHelper::call_server_based(
   PushToTalkSettingsInternal& aliceInternalSettings, PushToTalkServiceSettings& aliceServiceSettings,
   PushToTalkSettingsInternal& bobInternalSettings, PushToTalkServiceSettings& bobServiceSettings,
   int callCount)
{
   TestAccount alice("alice", Account_Init);

   PushToTalkManager* alicePttManager = PushToTalkManager::getInterface(alice.phone);
   PushToTalkServiceHandle alicePttService = alicePttManager->createPttService();
   alicePttManager->setHandler(alicePttService, (PushToTalkHandler*)0xDEADBEEF);
   alicePttManager->configureService(alicePttService, aliceServiceSettings);
   PushToTalkManagerInternal* alicePttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(alicePttManager);

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   // Bob is a PTT recipient
   TestAccount bob("bob", Account_Init);
   PushToTalkManager* bobPttManager = PushToTalkManager::getInterface(bob.phone);
   PushToTalkServiceHandle bobPttService = bobPttManager->createPttService();
   bobPttManager->setHandler(bobPttService, (PushToTalkHandler*)0xDEADBEEF);
   bobPttManager->configureService(bobPttService, bobServiceSettings);
   PushToTalkManagerInternal* bobPttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(bobPttManager);

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

#ifdef ANDROID
   int maxFdIncrease;
   if (aliceServiceSettings.unicastIpRanges.size() == 0 && bobServiceSettings.unicastIpRanges.size() == 0)
   {
      // will send to all of 127.0.0.x, so more file descriptors will be used
      maxFdIncrease = 100;
   }
   else
   {
      maxFdIncrease = 45;
   }
   CPCAPI2::Utils::FileDescriptorMonitor fdMon(maxFdIncrease);
#endif

   alicePttManagerInternal->setPttInternalSettings(alicePttService, aliceInternalSettings);
   bobPttManagerInternal->setPttInternalSettings(bobPttService, bobInternalSettings);

   alicePttManager->startService(alicePttService);
   bobPttManager->startService(bobPttService);

   auto aliceEvents = std::async(std::launch::async, [&]()
   {
      assertPttQueryEndpointsReportSent(alice, alicePttService, 2);
      // assertPttServiceStarted(alice, alicePttService, 2);
      assertPttServiceStartupCompleteLan(alice, alicePttService);
   });

   auto bobEvents = std::async(std::launch::async, [&]()
   {
      assertPttQueryEndpointsReportSent(bob, bobPttService, 2);
      // assertPttServiceStarted(bob, bobPttService, 2);
      assertPttServiceStartupCompleteLan(bob, bobPttService);
   });

   waitFor2(aliceEvents, bobEvents);

   std::this_thread::sleep_for(std::chrono::milliseconds(10000));

   for (int i = 0; i < callCount; i++)
   {
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));

      auto aliceEvents2 = std::async(std::launch::async, [&]()
      {
         PushToTalkSessionHandle alicePtt = alicePttManager->createPttSession(alicePttService);
         alicePttManager->setChannel(alicePtt, "channel01");
         alicePttManager->start(alicePtt);
         assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Initiated);
         assertPttClientOfferEvent(alice, alicePtt);
         assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Active);

         // Initiate talk spurt
         alicePttManager->startTalkSpurt(alicePtt);
         assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Talking);
         std::this_thread::sleep_for(std::chrono::milliseconds(4000));
         alicePttManager->endTalkSpurt(alicePtt);
         assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Active);

         alicePttManager->end(alicePtt);
         assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Ending);
         assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Idle);
      });

      auto bobEvents2 = std::async(std::launch::async, [&]()
      {
         PushToTalkSessionHandle bobPtt = 0;
         assertPttIncomingCallEx2(bob, bobPtt, aliceServiceSettings.localIdentities[0].userName, aliceServiceSettings.localIdentities[0].displayName, bobPttService);
         bobPttManager->accept(bobPtt);
         assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Initiated);
         assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Active);
         assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Talking);
         assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Ending);
         assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Idle);
      });

      waitFor2(aliceEvents2, bobEvents2);

      safeCout("======= attempt " << i << " complete ======");
   }

#ifdef ANDROID
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));
   ASSERT_EQ(fdMon.fdsOverThold(), 0);
#endif

   alicePttManager->shutdownService(alicePttService);
   bobPttManager->shutdownService(bobPttService);
   assertPttServiceShutdownComplete(alice, alicePttService);
   assertPttServiceShutdownComplete(bob, bobPttService);
}

void PttTestHelper::callOneToOne_server_based(
   PushToTalkSettingsInternal& aliceInternalSettings, PushToTalkServiceSettings& aliceServiceSettings,
   PushToTalkSettingsInternal& bobInternalSettings, PushToTalkServiceSettings& bobServiceSettings,
   PushToTalkSettingsInternal& maxInternalSettings, PushToTalkServiceSettings& maxServiceSettings)
{
   // Alice is the PTT sender/initiator
   TestAccount alice("alice", Account_Init);

   PushToTalkManager* alicePttManager = PushToTalkManager::getInterface(alice.phone);
   PushToTalkServiceHandle alicePttService = alicePttManager->createPttService();
   alicePttManager->setHandler(alicePttService, (PushToTalkHandlerInternal*)0xDEADBEEF);
   alicePttManager->configureService(alicePttService, aliceServiceSettings);
   PushToTalkManagerInternal* alicePttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(alicePttManager);

   // Bob is a PTT recipient
   TestAccount bob("bob", Account_Init);
   PushToTalkManager* bobPttManager = PushToTalkManager::getInterface(bob.phone);
   PushToTalkServiceHandle bobPttService = bobPttManager->createPttService();
   bobPttManager->setHandler(bobPttService, (PushToTalkHandlerInternal*)0xDEADBEEF);
   bobPttManager->configureService(bobPttService, bobServiceSettings);
   PushToTalkManagerInternal* bobPttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(bobPttManager);

   alicePttManagerInternal->setPttInternalSettings(alicePttService, aliceInternalSettings);
   bobPttManagerInternal->setPttInternalSettings(bobPttService, bobInternalSettings);
   alicePttManager->startService(alicePttService);
   bobPttManager->startService(bobPttService);

   auto aliceStartupEvents = std::async(std::launch::async, [&]()
   {
      assertPttServiceStartupCompleteWan(alice, alicePttService);
   });

   auto bobStartupEvents = std::async(std::launch::async, [&]()
   {
      assertPttServiceStartupCompleteWan(bob, bobPttService);
   });

   waitFor2(aliceStartupEvents, bobStartupEvents);

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   auto aliceEvents = std::async(std::launch::async, [&]()
   {
      for (int i = 0; i < 2; i++)
      {
         PushToTalkSessionHandle alicePtt = alicePttManager->createPttSession(alicePttService);
         alicePttManager->addRecipient(alicePtt, bobServiceSettings.localIdentities[0]); // Triggers a one to one call
         alicePttManager->start(alicePtt);
         assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Initiated);
         assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Active);

         // Initiate talk spurt
         alicePttManager->startTalkSpurt(alicePtt);
         assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Talking);
         std::this_thread::sleep_for(std::chrono::milliseconds(2000));
         alicePttManager->endTalkSpurt(alicePtt);
         assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Active);
         std::this_thread::sleep_for(std::chrono::milliseconds(500));
         alicePttManager->end(alicePtt);
         assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Ending);
         assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Idle);
         std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      }

      PushToTalkSessionHandle aliceInPtt = 0;
      assertPttIncomingCallEx(alice, aliceInPtt, bobServiceSettings.localIdentities[0].userName, bobServiceSettings.localIdentities[0].displayName);
      alicePttManager->accept(aliceInPtt);
      assertPttSessionStateChanged(alice, aliceInPtt, PttSessionState_Initiated);
      assertPttSessionStateChanged(alice, aliceInPtt, PttSessionState_Active);
      assertPttSessionStateChanged(alice, aliceInPtt, PttSessionState_Talking);
      assertPttSessionStateChanged(alice, aliceInPtt, PttSessionState_Ending);
      assertPttSessionStateChanged(alice, aliceInPtt, PttSessionState_Idle);

   });

   auto bobEvents = std::async(std::launch::async, [&]()
   {
      for (int i = 0; i < 2; i++)
      {
         PushToTalkSessionHandle bobPtt = 0;
         assertPttIncomingCallEx(bob, bobPtt, aliceServiceSettings.localIdentities[0].userName, aliceServiceSettings.localIdentities[0].displayName);
         bobPttManager->accept(bobPtt);
         assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Initiated);
         assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Active);
         assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Talking);
         assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Ending);
         assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Idle);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(3000));
      PushToTalkSessionHandle bobOutPtt = bobPttManager->createPttSession(bobPttService);
      bobPttManager->addRecipient(bobOutPtt, aliceServiceSettings.localIdentities[0]); // Triggers a one to one call
      bobPttManager->start(bobOutPtt);
      assertPttSessionStateChanged(bob, bobOutPtt, PttSessionState_Initiated);
      assertPttSessionStateChanged(bob, bobOutPtt, PttSessionState_Active);

      // Initiate talk spurt
      bobPttManager->startTalkSpurt(bobOutPtt);
      assertPttSessionStateChanged(bob, bobOutPtt, PttSessionState_Talking);
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      bobPttManager->endTalkSpurt(bobOutPtt);
      assertPttSessionStateChanged(bob, bobOutPtt, PttSessionState_Active);
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      bobPttManager->end(bobOutPtt);
      assertPttSessionStateChanged(bob, bobOutPtt, PttSessionState_Ending);
      assertPttSessionStateChanged(bob, bobOutPtt, PttSessionState_Idle);

   });

   waitFor2(aliceEvents, bobEvents);

   alicePttManager->shutdownService(alicePttService);
   bobPttManager->shutdownService(bobPttService);
   assertPttServiceShutdownComplete(alice, alicePttService);
   assertPttServiceShutdownComplete(bob, bobPttService);
}

void PttTestHelper::callOneToOneMediaInactive_server_based(
   PushToTalkSettingsInternal& aliceInternalSettings, PushToTalkServiceSettings& aliceServiceSettings,
   PushToTalkSettingsInternal& bobInternalSettings, PushToTalkServiceSettings& bobServiceSettings,
   PushToTalkSettingsInternal& maxInternalSettings, PushToTalkServiceSettings& maxServiceSettings)
{
   // Alice is the PTT sender/initiator
   TestAccount alice("alice", Account_Init);

   PushToTalkManager* alicePttManager = PushToTalkManager::getInterface(alice.phone);
   PushToTalkServiceHandle alicePttService = alicePttManager->createPttService();
   alicePttManager->setHandler(alicePttService, (PushToTalkHandlerInternal*)0xDEADBEEF);
   alicePttManager->configureService(alicePttService, aliceServiceSettings);
   PushToTalkManagerInternal* alicePttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(alicePttManager);

   // Bob is a PTT recipient
   TestAccount bob("bob", Account_Init);
   PushToTalkManager* bobPttManager = PushToTalkManager::getInterface(bob.phone);
   PushToTalkServiceHandle bobPttService = bobPttManager->createPttService();
   bobPttManager->setHandler(bobPttService, (PushToTalkHandlerInternal*)0xDEADBEEF);
   bobPttManager->configureService(bobPttService, bobServiceSettings);
   PushToTalkManagerInternal* bobPttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(bobPttManager);

   // Max is a PTT recipient
   TestAccount max("max", Account_Init);
   PushToTalkManager* maxPttManager = PushToTalkManager::getInterface(max.phone);
   PushToTalkServiceHandle maxPttService = maxPttManager->createPttService();
   maxPttManager->setHandler(maxPttService, (PushToTalkHandlerInternal*)0xDEADBEEF);
   maxPttManager->configureService(maxPttService, maxServiceSettings);
   PushToTalkManagerInternal* maxPttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(maxPttManager);

   alicePttManagerInternal->setPttInternalSettings(alicePttService, aliceInternalSettings);
   bobPttManagerInternal->setPttInternalSettings(bobPttService, bobInternalSettings);
   maxPttManagerInternal->setPttInternalSettings(maxPttService, maxInternalSettings);
   alicePttManager->startService(alicePttService);
   bobPttManager->startService(bobPttService);
   maxPttManager->startService(maxPttService);

   auto aliceStartupEvents = std::async(std::launch::async, [&]()
   {
      assertPttServiceStartupCompleteWan(alice, alicePttService);
   });

   auto bobStartupEvents = std::async(std::launch::async, [&]()
   {
      assertPttServiceStartupCompleteWan(bob, bobPttService);
   });

   auto maxStartupEvents = std::async(std::launch::async, [&]()
   {
      assertPttServiceStartupCompleteWan(max, maxPttService);
   });

   waitFor3(aliceStartupEvents, bobStartupEvents, maxStartupEvents);

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   auto aliceEvents = std::async(std::launch::async, [&]()
   {
      PushToTalkSessionHandle alicePtt = alicePttManager->createPttSession(alicePttService);
      alicePttManager->addRecipient(alicePtt, bobServiceSettings.localIdentities[0]); // Triggers a one to one call
      alicePttManager->start(alicePtt);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Initiated);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Active);

      // Initiate talk spurt
      alicePttManager->startTalkSpurt(alicePtt);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Talking);

      // NOTE: No receiver-disconnected event in ptt wan
      // CPCAPI2::PushToTalk::PushToTalkSessionHandle h;
      // CPCAPI2::PushToTalk::PttReceiverDisconnectedEvent evt;
      // ASSERT_TRUE(alice.pttEvents->expectEvent(__LINE__, "PushToTalkHandler::onPttReceiverDisconnected", 30000, AlwaysTruePred(), h, evt));
      // ASSERT_EQ(PttSessionState_Talking, evt.currentState);

      assertPttParticipantListUpdate(alice, alicePtt, 1);
      assertPttParticipantListUpdate(alice, alicePtt, 2);
      // Expecting endpoint count to go back to 1 as the receiver participant should have disconnected due to media inactivity
      assertPttParticipantListUpdate(alice, alicePtt, 1);

      alicePttManager->endTalkSpurt(alicePtt);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Active);
      alicePttManager->end(alicePtt);

      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Ending);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Idle);
   });

   auto bobEvents = std::async(std::launch::async, [&]()
   {
      PushToTalkSessionHandle bobPtt = 0;
      assertPttIncomingCallEx(bob, bobPtt, aliceServiceSettings.localIdentities[0].userName, aliceServiceSettings.localIdentities[0].displayName);
      bobPttManager->accept(bobPtt);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Initiated);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Active);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Talking);
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      PushToTalkManagerInternal::dropIncomingMediaPackets(true);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Ending);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Idle);
   });

   auto maxEvents = std::async(std::launch::async, [&]()
   {
      // assertPttStatisticsEvent(max, maxPttService, aliceServiceSettings.senderIdentity.userName, PttStatisticsEventType_UnicastDiscard);
      // assertPttSessionError(max, maxPttService, PttSessionError_UnicastDiscard);
   });

   waitFor3(aliceEvents, bobEvents, maxEvents);

   PushToTalkManagerInternal::dropIncomingMediaPackets(false);

   alicePttManager->shutdownService(alicePttService);
   bobPttManager->shutdownService(bobPttService);
   maxPttManager->shutdownService(maxPttService);
   assertPttServiceShutdownComplete(alice, alicePttService);
   assertPttServiceShutdownComplete(bob, bobPttService);
   assertPttServiceShutdownComplete(max, maxPttService);
}

void PttTestHelper::callOneToMany_server_based(
   PushToTalkSettingsInternal& aliceInternalSettings, PushToTalkServiceSettings& aliceServiceSettings,
   PushToTalkSettingsInternal& bobInternalSettings, PushToTalkServiceSettings& bobServiceSettings,
   PushToTalkSettingsInternal& adamInternalSettings, PushToTalkServiceSettings& adamServiceSettings,
   PushToTalkSettingsInternal& tasiaInternalSettings, PushToTalkServiceSettings& tasiaServiceSettings)
{
   // Alice is the PTT sender/initiator
   TestAccount alice("alice", Account_Init);

   PushToTalkManager* alicePttManager = PushToTalkManager::getInterface(alice.phone);
   PushToTalkServiceHandle alicePttService = alicePttManager->createPttService();
   alicePttManager->setHandler(alicePttService, (PushToTalkHandlerInternal*)0xDEADBEEF);
   alicePttManager->configureService(alicePttService, aliceServiceSettings);
   PushToTalkManagerInternal* alicePttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(alicePttManager);

   // Bob is a PTT recipient
   TestAccount bob("bob", Account_Init);
   PushToTalkManager* bobPttManager = PushToTalkManager::getInterface(bob.phone);
   PushToTalkServiceHandle bobPttService = bobPttManager->createPttService();
   bobPttManager->setHandler(bobPttService, (PushToTalkHandlerInternal*)0xDEADBEEF);
   bobPttManager->configureService(bobPttService, bobServiceSettings);
   PushToTalkManagerInternal* bobPttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(bobPttManager);

   // Adam is a PTT recipient
   TestAccount adam("adam", Account_Init);
   PushToTalkManager* adamPttManager = PushToTalkManager::getInterface(adam.phone);
   PushToTalkServiceHandle adamPttService = adamPttManager->createPttService();
   adamPttManager->setHandler(adamPttService, (PushToTalkHandlerInternal*)0xDEADBEEF);
   adamPttManager->configureService(adamPttService, adamServiceSettings);
   PushToTalkManagerInternal* adamPttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(adamPttManager);

   // Tasia is a PTT recipient
   TestAccount tasia("tasia", Account_Init);
   PushToTalkManager* tasiaPttManager = PushToTalkManager::getInterface(tasia.phone);
   PushToTalkServiceHandle tasiaPttService = tasiaPttManager->createPttService();
   tasiaPttManager->setHandler(tasiaPttService, (PushToTalkHandlerInternal*)0xDEADBEEF);
   tasiaPttManager->configureService(tasiaPttService, tasiaServiceSettings);
   PushToTalkManagerInternal* tasiaPttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(tasiaPttManager);

   alicePttManagerInternal->setPttInternalSettings(alicePttService, aliceInternalSettings);
   bobPttManagerInternal->setPttInternalSettings(bobPttService, bobInternalSettings);
   adamPttManagerInternal->setPttInternalSettings(adamPttService, adamInternalSettings);
   tasiaPttManagerInternal->setPttInternalSettings(tasiaPttService, tasiaInternalSettings);

   cpc::vector<cpc::string> channelSubscriptions;
   channelSubscriptions.push_back("channel01");
   alicePttManager->setChannelSubscriptions(alicePttService, channelSubscriptions);
   bobPttManager->setChannelSubscriptions(bobPttService, channelSubscriptions);
   adamPttManager->setChannelSubscriptions(adamPttService, channelSubscriptions);
   // Tasia does not subscribe

   alicePttManager->startService(alicePttService);
   bobPttManager->startService(bobPttService);
   adamPttManager->startService(adamPttService);
   tasiaPttManager->startService(tasiaPttService);

   auto aliceStartEvents = std::async(std::launch::async, [&]()
   {
      assertPttServiceStartupCompleteWan(alice, alicePttService);
   });

   auto bobStartEvents = std::async(std::launch::async, [&]()
   {
      assertPttServiceStartupCompleteWan(bob, bobPttService);
   });

   auto adamStartEvents = std::async(std::launch::async, [&]()
   {
      assertPttServiceStartupCompleteWan(adam, adamPttService);
   });

   auto tasiaStartEvents = std::async(std::launch::async, [&]()
   {
      assertPttServiceStartupCompleteWan(tasia, tasiaPttService);
   });

   waitFor4(aliceStartEvents, bobStartEvents, adamStartEvents, tasiaStartEvents);

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   auto aliceEvents = std::async(std::launch::async, [&]()
   {
      PushToTalkSessionHandle alicePtt = alicePttManager->createPttSession(alicePttService);
      alicePttManager->setChannel(alicePtt, "channel01");
      alicePttManager->start(alicePtt);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Initiated);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Active);

      // Initiate talk spurt
      alicePttManager->startTalkSpurt(alicePtt);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Talking);
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      alicePttManager->endTalkSpurt(alicePtt);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Active);
      alicePttManager->end(alicePtt);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Ending);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Idle);

      // handle incoming
      PushToTalkSessionHandle aliceInPtt = 0;
      assertPttIncomingCallEx(alice, aliceInPtt, bobServiceSettings.localIdentities[0].userName, bobServiceSettings.localIdentities[0].displayName);
      alicePttManager->accept(aliceInPtt);
      assertPttSessionStateChanged(alice, aliceInPtt, PttSessionState_Initiated);
      assertPttSessionStateChanged(alice, aliceInPtt, PttSessionState_Active);
      assertPttSessionStateChanged(alice, aliceInPtt, PttSessionState_Talking);
      assertPttSessionStateChanged(alice, aliceInPtt, PttSessionState_Ending);
      assertPttSessionStateChanged(alice, aliceInPtt, PttSessionState_Idle);
   });

   auto bobEvents = std::async(std::launch::async, [&]()
   {
      PushToTalkSessionHandle bobPtt = 0;
      assertPttIncomingCallEx(bob, bobPtt, aliceServiceSettings.localIdentities[0].userName, aliceServiceSettings.localIdentities[0].displayName);
      bobPttManager->accept(bobPtt);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Initiated);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Active);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Talking);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Ending);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Idle);

      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      PushToTalkSessionHandle bobOutPtt = bobPttManager->createPttSession(bobPttService);
      bobPttManager->setChannel(bobOutPtt, "channel01");
      bobPttManager->start(bobOutPtt);
      assertPttSessionStateChanged(bob, bobOutPtt, PttSessionState_Initiated);
      assertPttSessionStateChanged(bob, bobOutPtt, PttSessionState_Active);

      // Initiate talk spurt
      bobPttManager->startTalkSpurt(bobOutPtt);
      assertPttSessionStateChanged(bob, bobOutPtt, PttSessionState_Talking);
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      bobPttManager->endTalkSpurt(bobOutPtt);
      assertPttSessionStateChanged(bob, bobOutPtt, PttSessionState_Active);
      bobPttManager->end(bobOutPtt);
      assertPttSessionStateChanged(bob, bobOutPtt, PttSessionState_Ending);
      assertPttSessionStateChanged(bob, bobOutPtt, PttSessionState_Idle);
   });

   auto adamEvents = std::async(std::launch::async, [&]()
   {
      PushToTalkSessionHandle adamPtt = 0;
      assertPttIncomingCallEx(adam, adamPtt, aliceServiceSettings.localIdentities[0].userName, aliceServiceSettings.localIdentities[0].displayName);
      adamPttManager->accept(adamPtt);
      assertPttSessionStateChanged(adam, adamPtt, PttSessionState_Initiated);
      assertPttSessionStateChanged(adam, adamPtt, PttSessionState_Active);
      assertPttSessionStateChanged(adam, adamPtt, PttSessionState_Talking);
      assertPttSessionStateChanged(adam, adamPtt, PttSessionState_Ending);
      assertPttSessionStateChanged(adam, adamPtt, PttSessionState_Idle);

      PushToTalkSessionHandle adamPtt2 = 0;
      assertPttIncomingCallEx(adam, adamPtt2, bobServiceSettings.localIdentities[0].userName, bobServiceSettings.localIdentities[0].displayName);
      adamPttManager->accept(adamPtt2);
      assertPttSessionStateChanged(adam, adamPtt2, PttSessionState_Initiated);
      assertPttSessionStateChanged(adam, adamPtt2, PttSessionState_Active);
      assertPttSessionStateChanged(adam, adamPtt2, PttSessionState_Talking);
      assertPttSessionStateChanged(adam, adamPtt2, PttSessionState_Ending);
      assertPttSessionStateChanged(adam, adamPtt2, PttSessionState_Idle);
   });

   auto tasiaEvents = std::async(std::launch::async, [&]()
   {
      // assertPttStatisticsEvent(max, maxPttService, aliceServiceSettings.senderIdentity.userName, PttStatisticsEventType_UnicastDiscard);
      //assertPttSessionError(max, maxPttService, PttSessionError_UnicastDiscard);
   });

   waitFor4(aliceEvents, bobEvents, adamEvents, tasiaEvents);

   alicePttManager->shutdownService(alicePttService);
   bobPttManager->shutdownService(bobPttService);
   adamPttManager->shutdownService(adamPttService);
   tasiaPttManager->shutdownService(tasiaPttService);
   assertPttServiceShutdownComplete(alice, alicePttService);
   assertPttServiceShutdownComplete(bob, bobPttService);
   assertPttServiceShutdownComplete(adam, adamPttService);
   assertPttServiceShutdownComplete(tasia, tasiaPttService);
}

void PttTestHelper::callOneToMany_server_based_mute_unmute(
   PushToTalkSettingsInternal& aliceInternalSettings, PushToTalkServiceSettings& aliceServiceSettings,
   PushToTalkSettingsInternal& bobInternalSettings, PushToTalkServiceSettings& bobServiceSettings,
   PushToTalkSettingsInternal& adamInternalSettings, PushToTalkServiceSettings& adamServiceSettings,
   PushToTalkSettingsInternal& tasiaInternalSettings, PushToTalkServiceSettings& tasiaServiceSettings)
{
   // Alice is the PTT sender/initiator
   TestAccount alice("alice", Account_Init);

   PushToTalkManager* alicePttManager = PushToTalkManager::getInterface(alice.phone);
   PushToTalkServiceHandle alicePttService = alicePttManager->createPttService();
   alicePttManager->setHandler(alicePttService, (PushToTalkHandlerInternal*)0xDEADBEEF);
   alicePttManager->configureService(alicePttService, aliceServiceSettings);
   PushToTalkManagerInternal* alicePttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(alicePttManager);

   // Bob is a PTT recipient
   TestAccount bob("bob", Account_Init);
   PushToTalkManager* bobPttManager = PushToTalkManager::getInterface(bob.phone);
   PushToTalkServiceHandle bobPttService = bobPttManager->createPttService();
   bobPttManager->setHandler(bobPttService, (PushToTalkHandlerInternal*)0xDEADBEEF);
   bobPttManager->configureService(bobPttService, bobServiceSettings);
   PushToTalkManagerInternal* bobPttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(bobPttManager);

   // Adam is a PTT recipient
   TestAccount adam("adam", Account_Init);
   PushToTalkManager* adamPttManager = PushToTalkManager::getInterface(adam.phone);
   PushToTalkServiceHandle adamPttService = adamPttManager->createPttService();
   adamPttManager->setHandler(adamPttService, (PushToTalkHandlerInternal*)0xDEADBEEF);
   adamPttManager->configureService(adamPttService, adamServiceSettings);
   PushToTalkManagerInternal* adamPttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(adamPttManager);

   // Tasia is a PTT recipient
   TestAccount tasia("tasia", Account_Init);
   PushToTalkManager* tasiaPttManager = PushToTalkManager::getInterface(tasia.phone);
   PushToTalkServiceHandle tasiaPttService = tasiaPttManager->createPttService();
   tasiaPttManager->setHandler(tasiaPttService, (PushToTalkHandlerInternal*)0xDEADBEEF);
   tasiaPttManager->configureService(tasiaPttService, tasiaServiceSettings);
   PushToTalkManagerInternal* tasiaPttManagerInternal = dynamic_cast<PushToTalkManagerInternal*>(tasiaPttManager);

   alicePttManagerInternal->setPttInternalSettings(alicePttService, aliceInternalSettings);
   bobPttManagerInternal->setPttInternalSettings(bobPttService, bobInternalSettings);
   adamPttManagerInternal->setPttInternalSettings(adamPttService, adamInternalSettings);
   tasiaPttManagerInternal->setPttInternalSettings(tasiaPttService, tasiaInternalSettings);

   cpc::vector<cpc::string> channelSubscriptions;
   channelSubscriptions.push_back("channel01");
   alicePttManager->setChannelSubscriptions(alicePttService, channelSubscriptions);
   bobPttManager->setChannelSubscriptions(bobPttService, channelSubscriptions);
   adamPttManager->setChannelSubscriptions(adamPttService, channelSubscriptions);
   // Tasia does not subscribe

   alicePttManager->startService(alicePttService);
   bobPttManager->startService(bobPttService);
   adamPttManager->startService(adamPttService);
   tasiaPttManager->startService(tasiaPttService);

   auto aliceStartEvents = std::async(std::launch::async, [&]()
   {
      assertPttServiceStartupCompleteWan(alice, alicePttService);
   });

   auto bobStartEvents = std::async(std::launch::async, [&]()
   {
      assertPttServiceStartupCompleteWan(bob, bobPttService);
   });

   auto adamStartEvents = std::async(std::launch::async, [&]()
   {
      assertPttServiceStartupCompleteWan(adam, adamPttService);
   });

   auto tasiaStartEvents = std::async(std::launch::async, [&]()
   {
      assertPttServiceStartupCompleteWan(tasia, tasiaPttService);
   });

   waitFor4(aliceStartEvents, bobStartEvents, adamStartEvents, tasiaStartEvents);

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   auto aliceEvents = std::async(std::launch::async, [&]()
   {
      PushToTalkSessionHandle alicePtt = alicePttManager->createPttSession(alicePttService);
      alicePttManager->setChannel(alicePtt, "channel01");
      alicePttManager->start(alicePtt);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Initiated);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Active);

      // Initiate talk spurt
      alicePttManager->startTalkSpurt(alicePtt);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Talking);
      std::this_thread::sleep_for(std::chrono::milliseconds(6000));
      alicePttManager->endTalkSpurt(alicePtt);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Active);
      alicePttManager->end(alicePtt);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Ending);
      assertPttSessionStateChanged(alice, alicePtt, PttSessionState_Idle);

   });

   auto bobEvents = std::async(std::launch::async, [&]()
   {
      PushToTalkSessionHandle bobPtt = 0;
      assertPttIncomingCallEx(bob, bobPtt, aliceServiceSettings.localIdentities[0].userName, aliceServiceSettings.localIdentities[0].displayName);
      bobPttManager->accept(bobPtt);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Initiated);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Active);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Talking);
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      bobPttManager->mutePttSession(bobPtt);
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      bobPttManager->unmutePttSession(bobPtt);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Ending);
      assertPttSessionStateChanged(bob, bobPtt, PttSessionState_Idle);
   });

   auto adamEvents = std::async(std::launch::async, [&]()
   {
      PushToTalkSessionHandle adamPtt = 0;
      assertPttIncomingCallEx(adam, adamPtt, aliceServiceSettings.localIdentities[0].userName, aliceServiceSettings.localIdentities[0].displayName);
      adamPttManager->accept(adamPtt);
      assertPttSessionStateChanged(adam, adamPtt, PttSessionState_Initiated);
      assertPttSessionStateChanged(adam, adamPtt, PttSessionState_Active);
      assertPttSessionStateChanged(adam, adamPtt, PttSessionState_Talking);
      assertPttSessionStateChanged(adam, adamPtt, PttSessionState_Ending);
      assertPttSessionStateChanged(adam, adamPtt, PttSessionState_Idle);

   });

   auto tasiaEvents = std::async(std::launch::async, [&]()
   {
      // assertPttStatisticsEvent(max, maxPttService, aliceServiceSettings.senderIdentity.userName, PttStatisticsEventType_UnicastDiscard);
      //assertPttSessionError(max, maxPttService, PttSessionError_UnicastDiscard);
   });

   waitFor4(aliceEvents, bobEvents, adamEvents, tasiaEvents);

   alicePttManager->shutdownService(alicePttService);
   bobPttManager->shutdownService(bobPttService);
   adamPttManager->shutdownService(adamPttService);
   tasiaPttManager->shutdownService(tasiaPttService);
   assertPttServiceShutdownComplete(alice, alicePttService);
   assertPttServiceShutdownComplete(bob, bobPttService);
   assertPttServiceShutdownComplete(adam, adamPttService);
   assertPttServiceShutdownComplete(tasia, tasiaPttService);
}

cpc::vector<PushToTalkIpAddressRange> PttTestHelper::getUnicastIpRange(int numEndpoints)
{
   cpc::vector<PushToTalkIpAddressRange> ranges;
   PushToTalkIpAddressRange range;
   range.ipAddrStart = "127.0.0.1";
   std::stringstream ss;
   ss << "127.0.0.";
   ss << numEndpoints;
   range.ipAddrEnd = ss.str().c_str();

   ranges.push_back(range);
   return ranges;
}

void TestPttEvents::expectPttServiceStartupCompleteLan(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkServiceHandle handle)
{
   CPCAPI2::PushToTalk::PushToTalkServiceHandle h;
   CPCAPI2::PushToTalk::PttServiceStatusChangedEvent evt;
   ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandler::onPttServiceStatusChanged", 30000, HandleEqualsPred<CPCAPI2::PushToTalk::PushToTalkServiceHandle>(handle), h, evt));
   ASSERT_EQ(CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Connecting, evt.status);
   ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandler::onPttServiceStatusChanged", 30000, HandleEqualsPred<CPCAPI2::PushToTalk::PushToTalkServiceHandle>(handle), h, evt));
   ASSERT_EQ(CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Connected, evt.status);
   ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandler::onPttServiceStatusChanged", 30000, HandleEqualsPred<CPCAPI2::PushToTalk::PushToTalkServiceHandle>(handle), h, evt));
   ASSERT_EQ(CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Ready, evt.status);
}

void TestPttEvents::expectPttServiceStartupCompleteWan(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkServiceHandle handle)
{
   CPCAPI2::PushToTalk::PushToTalkServiceHandle h;
   CPCAPI2::PushToTalk::PttServiceStatusChangedEvent evt;
   ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandler::onPttServiceStatusChanged", 30000, HandleEqualsPred<CPCAPI2::PushToTalk::PushToTalkServiceHandle>(handle), h, evt));
   ASSERT_EQ(CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Connecting, evt.status);
   ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandler::onPttServiceStatusChanged", 30000, HandleEqualsPred<CPCAPI2::PushToTalk::PushToTalkServiceHandle>(handle), h, evt));
   ASSERT_EQ(CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Authenticating, evt.status);
   ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandler::onPttServiceStatusChanged", 30000, HandleEqualsPred<CPCAPI2::PushToTalk::PushToTalkServiceHandle>(handle), h, evt));
   ASSERT_EQ(CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Connected, evt.status);
   ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandler::onPttServiceStatusChanged", 30000, HandleEqualsPred<CPCAPI2::PushToTalk::PushToTalkServiceHandle>(handle), h, evt));
   ASSERT_EQ(CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Ready, evt.status);
}

void TestPttEvents::expectPttServiceShutdownComplete(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkServiceHandle handle)
{
   CPCAPI2::PushToTalk::PushToTalkServiceHandle h;
   CPCAPI2::PushToTalk::PttServiceStatusChangedEvent evt;
   evt.status = CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Ready;
   while (evt.status != CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Disabled)
   {
      ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandler::onPttServiceStatusChanged", 40000, HandleEqualsPred<CPCAPI2::PushToTalk::PushToTalkServiceHandle>(handle), h, evt));
      if ((evt.status == CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Disconnecting)
         || (evt.status == CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Disconnected)
         || (evt.status == CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Disabled))
      {
         continue;
      }
      ASSERT_TRUE(false) << " invalid service status: " << evt.status;
   }
   /*
   ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandler::onPttServiceStatusChanged", 40000, HandleEqualsPred<CPCAPI2::PushToTalk::PushToTalkServiceHandle>(handle), h, evt));
   ASSERT_EQ(CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Disconnecting, evt.status);
   ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandler::onPttServiceStatusChanged", 40000, HandleEqualsPred<CPCAPI2::PushToTalk::PushToTalkServiceHandle>(handle), h, evt));
   ASSERT_EQ(CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Disconnected, evt.status);
   ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandler::onPttServiceStatusChanged", 40000, HandleEqualsPred<CPCAPI2::PushToTalk::PushToTalkServiceHandle>(handle), h, evt));
   ASSERT_EQ(CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status_Disabled, evt.status);
   */
}

void TestPttEvents::expectPttServiceRestarted(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkServiceHandle handle, CPCAPI2::PushToTalk::PttRestartReasonType reason)
{
   CPCAPI2::PushToTalk::PushToTalkServiceHandle h;
   CPCAPI2::PushToTalk::PttServiceRestartedEvent evt;
   ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandlerInternal::onPttServiceRestarted", 40000, HandleEqualsPred<CPCAPI2::PushToTalk::PushToTalkServiceHandle>(handle), h, evt));
   ASSERT_EQ(reason, evt.reason);
}

void TestPttEvents::expectPttServiceStatusChanged(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkServiceHandle handle, CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status status)
{
   CPCAPI2::PushToTalk::PushToTalkServiceHandle h;
   CPCAPI2::PushToTalk::PttServiceStatusChangedEvent evt;
   ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandler::onPttServiceStatusChanged", 40000, HandleEqualsPred<CPCAPI2::PushToTalk::PushToTalkServiceHandle>(handle), h, evt)) << " no service status event received for " << account.config.name;
   ASSERT_EQ(status, evt.status)  << " mismatched service status for " << account.config.name;
}

void TestPttEvents::expectPttServiceStatusChangedEx(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkServiceHandle handle, CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status status, CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Reason reason)
{
   CPCAPI2::PushToTalk::PushToTalkServiceHandle h;
   CPCAPI2::PushToTalk::PttServiceStatusChangedEvent evt;
   ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandler::onPttServiceStatusChanged", 40000, HandleEqualsPred<CPCAPI2::PushToTalk::PushToTalkServiceHandle>(handle), h, evt))  << " no service status event received for " << account.config.name;
   ASSERT_EQ(status, evt.status)   << " mismatched service status for " << account.config.name;
   ASSERT_EQ(reason, evt.reason);
}

void TestPttEvents::expectPttEndpointList(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkServiceHandle& handle, int totalCount, int listCount, int offset, CPCAPI2::PushToTalk::PttIdentity& identity)
{
   CPCAPI2::PushToTalk::PushToTalkServiceHandle h;
   CPCAPI2::PushToTalk::PttEndpointListEvent evt;
   ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandler::onPttEndpointList", 30000, AlwaysTruePred(), h, evt));
   ASSERT_EQ(handle, h);
   ASSERT_EQ(totalCount, evt.totalEndpointCount);
   ASSERT_EQ(listCount, evt.endpoints.size());
   ASSERT_EQ(offset, evt.offset);
   ASSERT_EQ(identity.userName, evt.localIdentity.userName);
   ASSERT_EQ(identity.displayName, evt.localIdentity.displayName);
   ASSERT_EQ(identity.ipAddress, evt.localIdentity.ipAddress);
   ASSERT_EQ(identity.identityType, evt.localIdentity.identityType);
}

void TestPttEvents::expectPttEndpointListEx(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkServiceHandle& handle, int totalCount, int listCount, int offset)
{
   CPCAPI2::PushToTalk::PushToTalkServiceHandle h;
   CPCAPI2::PushToTalk::PttEndpointListEvent evt;
   ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandler::onPttEndpointList", 30000, AlwaysTruePred(), h, evt));
   ASSERT_EQ(handle, h);
   ASSERT_EQ(totalCount, evt.totalEndpointCount);
   ASSERT_EQ(listCount, evt.endpoints.size());
   ASSERT_EQ(offset, evt.offset);
}

void TestPttEvents::expectPttParticipantListUpdate(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkSessionHandle& handle, int listCount)
{
   CPCAPI2::PushToTalk::PushToTalkSessionHandle h;
   CPCAPI2::PushToTalk::PttParticipantListUpdateEvent evt;
   ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandlerInternal::onPttParticipantListUpdate", 30000, AlwaysTruePred(), h, evt));
   ASSERT_EQ(handle, h);
   ASSERT_EQ(listCount, evt.participants.size());
}

void TestPttEvents::expectPttSessionStateChanged(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkSessionHandle handle, CPCAPI2::PushToTalk::PttSessionStateType state)
{
   CPCAPI2::PushToTalk::PushToTalkSessionHandle h;
   CPCAPI2::PushToTalk::PttSessionStateChangedEvent evt;
   ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandler::onPttSessionStateChanged", 30000, HandleEqualsPred<CPCAPI2::PushToTalk::PushToTalkSessionHandle>(handle), h, evt));
   ASSERT_EQ(state, evt.currentState);
}

void TestPttEvents::expectPttSessionStateChangedEx(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkSessionHandle handle, CPCAPI2::PushToTalk::PttSessionStateType state, unsigned int connectedCalls, unsigned int totalCalls)
{
   CPCAPI2::PushToTalk::PushToTalkSessionHandle h;
   CPCAPI2::PushToTalk::PttSessionStateChangedEvent evt;
   ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandler::onPttSessionStateChanged", 30000, HandleEqualsPred<CPCAPI2::PushToTalk::PushToTalkSessionHandle>(handle), h, evt));
   ASSERT_EQ(state, evt.currentState);
   ASSERT_EQ(connectedCalls, evt.connectedCalls);
   ASSERT_EQ(totalCalls, evt.totalCalls);
}

void TestPttEvents::expectPttSessionStateChangedEx3(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkSessionHandle handle, CPCAPI2::PushToTalk::PttSessionStateType state, CPCAPI2::PushToTalk::PttSessionStateType& actualState)
{
   CPCAPI2::PushToTalk::PushToTalkSessionHandle h;
   CPCAPI2::PushToTalk::PttSessionStateChangedEvent evt;
   ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandler::onPttSessionStateChanged", 30000, HandleEqualsPred<CPCAPI2::PushToTalk::PushToTalkSessionHandle>(handle), h, evt));
   ASSERT_EQ(state, evt.currentState);
   actualState = evt.currentState;
}

void TestPttEvents::expectPttSessionError(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkSessionHandle handle, CPCAPI2::PushToTalk::PttSessionError errorCode)
{
   CPCAPI2::PushToTalk::PushToTalkSessionHandle h;
   CPCAPI2::PushToTalk::PttSessionErrorEvent evt;
   ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandler::onPttSessionError", 60000, HandleEqualsPred<CPCAPI2::PushToTalk::PushToTalkSessionHandle>(handle), h, evt));
   ASSERT_EQ(errorCode, evt.errorCode);
}

void TestPttEvents::expectPttIncomingCall(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkSessionHandle& handle)
{
   CPCAPI2::PushToTalk::PushToTalkSessionHandle h;
   CPCAPI2::PushToTalk::PttIncomingCallEvent evt;
   ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandler::onPttIncomingCall", 60000, AlwaysTruePred(), h, evt));
   handle = h;

   // cpc::string callerAddress;
   // cpc::string callerName;
   // cpc::string channelId;
   // PttSessionState currentState;
}

void TestPttEvents::expectPttIncomingCallEx(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkSessionHandle& handle, cpc::string& userName, cpc::string& displayName)
{
   CPCAPI2::PushToTalk::PushToTalkSessionHandle h;
   CPCAPI2::PushToTalk::PttIncomingCallEvent evt;
   ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandler::onPttIncomingCall", 30000, AlwaysTruePred(), h, evt));
   handle = h;
   ASSERT_EQ(userName, evt.callerIdentity.userName);
   ASSERT_EQ(displayName, evt.callerIdentity.displayName);
}
  
void TestPttEvents::expectPttIncomingCallEx2(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkSessionHandle& handle, cpc::string& userName, cpc::string& displayName, CPCAPI2::PushToTalk::PushToTalkServiceHandle& service)
{
   CPCAPI2::PushToTalk::PushToTalkSessionHandle h;
   CPCAPI2::PushToTalk::PttIncomingCallEvent evt;
   ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandler::onPttIncomingCall", 30000, AlwaysTruePred(), h, evt));
   handle = h;
   ASSERT_EQ(userName, evt.callerIdentity.userName);
   ASSERT_EQ(displayName, evt.callerIdentity.displayName);
   ASSERT_EQ(service, evt.service);
   
}

void TestPttEvents::expectPttStatisticsEvent(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkServiceHandle& handle, cpc::string& userName, CPCAPI2::PushToTalk::PttStatisticsEventType statisticsType)
{
   CPCAPI2::PushToTalk::PushToTalkServiceHandle h;
   CPCAPI2::PushToTalk::PttStatisticsEvent evt;
   ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandler::onPttStatistics", 30000, AlwaysTruePred(), h, evt));
   ASSERT_EQ(userName, evt.callerIdentity.userName);
   ASSERT_EQ(statisticsType, evt.statisticsEventType);
}

void TestPttEvents::expectPttMediaStatisticsEvent(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkSessionHandle& handle)
{
   CPCAPI2::PushToTalk::PushToTalkSessionHandle h;
   CPCAPI2::PushToTalk::PttMediaStatisticsEvent stats;
   ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandlerInternal::onPttMediaStatistics", 20000, AlwaysTruePred(), h, stats));
   ASSERT_EQ(stats.mediaStreamStats.size(), 1);
   ASSERT_GT(stats.mediaStreamStats[0].rtpPacketCount, 0);
}

void TestPttEvents::expectPttReceiverDisconnected(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkSessionHandle& handle,
     const cpc::string& channel, CPCAPI2::PushToTalk::PttSessionStateType state, unsigned int connectedCalls, unsigned int totalCalls)
{
   CPCAPI2::PushToTalk::PushToTalkSessionHandle h;
   CPCAPI2::PushToTalk::PttReceiverDisconnectedEvent evt;
   ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandler::onPttReceiverDisconnected", 30000, AlwaysTruePred(), h, evt));
   handle = h;
   ASSERT_EQ(channel, evt.channelId);
   ASSERT_EQ(state, evt.currentState);
   ASSERT_EQ(connectedCalls, evt.connectedCalls);
   ASSERT_EQ(totalCalls, evt.totalCalls);
}
   
void TestPttEvents::expectPttQueryEndpointsRequestSent(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkServiceHandle& handle)
{
   CPCAPI2::PushToTalk::PushToTalkServiceHandle h;
   CPCAPI2::PushToTalk::PttQueryEndpointsRequest evt;
   ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandlerInternal::onPttQueryEndpointsRequestSent", 60000, AlwaysTruePred(), h, evt));
   ASSERT_EQ(handle, h);
}
 
void TestPttEvents::expectPttQueryEndpointsRequestReceived(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkServiceHandle& handle)
{
   CPCAPI2::PushToTalk::PushToTalkServiceHandle h;
   CPCAPI2::PushToTalk::PttQueryEndpointsRequest evt;
   ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandlerInternal::onPttQueryEndpointsRequestReceived", 60000, AlwaysTruePred(), h, evt));
   ASSERT_EQ(handle, h);
}

void TestPttEvents::expectPttQueryEndpointsResponseSent(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkServiceHandle& handle, cpc::string& endpointIp)
{
   CPCAPI2::PushToTalk::PushToTalkServiceHandle h;
   CPCAPI2::PushToTalk::PttQueryEndpointsResponse evt;
   ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandlerInternal::onPttQueryEndpointsResponseSent", 60000, AlwaysTruePred(), h, evt));
   ASSERT_EQ(handle, h);
   if (endpointIp.size() > 0)
   {
      ASSERT_EQ(endpointIp, evt.endpointIpAddress);
   }
}

void TestPttEvents::expectPttQueryEndpointsResponseSentEx(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkServiceHandle& handle, cpc::string& containsIp)
{
   CPCAPI2::PushToTalk::PushToTalkServiceHandle h;
   CPCAPI2::PushToTalk::PttQueryEndpointsResponse evt;
   ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandlerInternal::onPttQueryEndpointsResponseSent", 60000, AlwaysTruePred(), h, evt));
   ASSERT_EQ(handle, h);
   if (containsIp.size() > 0)
   {
      std::stringstream endpointList;
      bool found = false;
      for (cpc::vector<cpc::string>::iterator i = evt.endpoints.begin(); i != evt.endpoints.end(); ++i)
      {
         endpointList << (*i) << " ";
         if (containsIp == (*i))
         {
            found = true;
         }
      }

      ASSERT_TRUE(found) << " did not find endpoint: " << containsIp << " in endpoint list: " << endpointList.str();
   }
}

void TestPttEvents::expectPttQueryEndpointsResponseReceived(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkServiceHandle& handle, cpc::string& endpointIp)
{
   CPCAPI2::PushToTalk::PushToTalkServiceHandle h;
   CPCAPI2::PushToTalk::PttQueryEndpointsResponse evt;
   ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandlerInternal::onPttQueryEndpointsResponseReceived", 60000, AlwaysTruePred(), h, evt));
   ASSERT_EQ(handle, h);
   if (endpointIp.size() > 0)
   {
      ASSERT_EQ(endpointIp, evt.endpointIpAddress);
   }
}

void TestPttEvents::expectPttQueryEndpointsReportSent(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkServiceHandle& handle, int endpointCount)
{
   CPCAPI2::PushToTalk::PushToTalkServiceHandle h;
   CPCAPI2::PushToTalk::PttReportEndpointsEvent evt;
   ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandlerInternal::onPttQueryEndpointsReportSent", 90000, AlwaysTruePred(), h, evt));
   ASSERT_EQ(handle, h);
   ASSERT_EQ(endpointCount, evt.endpoints.size());
}

void TestPttEvents::expectPttQueryEndpointsReportSentEx(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkServiceHandle& handle, std::vector<std::string>& endpointIps)
{
   CPCAPI2::PushToTalk::PushToTalkServiceHandle h;
   CPCAPI2::PushToTalk::PttReportEndpointsEvent evt;
   int eventEndpointCount = 0;
   std::set<std::string> discoveredEndpoints;
   for (int i = 0; i < endpointIps.size(); ++i)
   {
      ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandlerInternal::onPttQueryEndpointsReportSent", 60000, AlwaysTruePred(), h, evt));
      ASSERT_EQ(handle, h);
      eventEndpointCount = evt.endpoints.size();
      if (endpointIps.size() == eventEndpointCount)
      {
         for (cpc::vector<cpc::string>::iterator j = evt.endpoints.begin(); j != evt.endpoints.end(); ++j)
         {
            discoveredEndpoints.insert((*j).c_str());
         }
         break;
      }
   }
   ASSERT_EQ(endpointIps.size(), discoveredEndpoints.size());
   bool mismatch = false;
   for (std::vector<std::string>::iterator i = endpointIps.begin(); i != endpointIps.end(); ++i)
   {
      if (discoveredEndpoints.find(*i) == discoveredEndpoints.end())
      {
         mismatch = true;
         break;
      }
   }

   std::stringstream endpointList;
   std::stringstream discoveredList;
   if (mismatch)
   {
      for (std::vector<std::string>::iterator i = endpointIps.begin(); i != endpointIps.end(); ++i) endpointList << (*i) << " ";
      for (std::set<std::string>::iterator j = discoveredEndpoints.begin(); j != discoveredEndpoints.end(); ++j) discoveredList << (*j) << " ";
   }
   ASSERT_FALSE(mismatch) << " expected endpoints: " << endpointList.str() << " discovered endpoints: " << discoveredList.str();
}

void TestPttEvents::expectPttQueryEndpointsReportReceived(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkServiceHandle& handle, int endpointCount)
{
   CPCAPI2::PushToTalk::PushToTalkServiceHandle h;
   CPCAPI2::PushToTalk::PttReportEndpointsEvent evt;
   ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandlerInternal::onPttQueryEndpointsReportReceived", 90000, AlwaysTruePred(), h, evt));
   ASSERT_EQ(handle, h);
   ASSERT_EQ(endpointCount, evt.endpoints.size());
}

void TestPttEvents::expectPttServiceConfigured(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkServiceHandle& handle)
{
   CPCAPI2::PushToTalk::PushToTalkServiceHandle h;
   CPCAPI2::PushToTalk::PttServiceConfiguredEvent evt;
   ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandler::onPttServiceConfigured", 30000, AlwaysTruePred(), h, evt));
   ASSERT_EQ(handle, h);
}

void TestPttEvents::expectPttClientOfferEvent(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkSessionHandle& handle)
{
   CPCAPI2::PushToTalk::PushToTalkSessionHandle h;
   CPCAPI2::PushToTalk::PttClientOfferEvent evt;
   ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandlerInternal::onPttClientOfferEvent", 60000, AlwaysTruePred(), h, evt));
   ASSERT_EQ(handle, h);
   bool dtxEnabled = true;
   std::size_t pos = evt.sessionDescription.find("usedtx=");
   if ((pos != std::string::npos) && (strcmp(evt.sessionDescription.substr(pos + 7, 1).c_str(), "0") == 0))
   {
      dtxEnabled = false;
   }
   ASSERT_FALSE(dtxEnabled);
}

void TestPttEvents::expectPttClientOfferUpdateEvent(int line, TestAccount& account, CPCAPI2::PushToTalk::PushToTalkSessionHandle& handle, cpc::string& endpointIp)
{
   CPCAPI2::PushToTalk::PushToTalkSessionHandle h;
   CPCAPI2::PushToTalk::PttClientOfferUpdateEvent evt;
   ASSERT_TRUE(account.pttEvents->expectEvent(line, "PushToTalkHandlerInternal::onPttClientOfferUpdateEvent", 30000, AlwaysTruePred(), h, evt));
   ASSERT_EQ(handle, h);
   ASSERT_EQ(endpointIp, evt.endpoint);
}
