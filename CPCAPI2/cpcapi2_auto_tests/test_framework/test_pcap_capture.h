#ifndef test_pcap_capture_hpp
#define test_pcap_capture_hpp

#include <gtest/gtest.h>
#include <unordered_set>
#include <string>

namespace CPCAPI2
{
   class Phone;
}

class TestPcapCapture : public ::testing::EmptyTestEventListener
{
public:
   TestPcapCapture(const std::string& testCasesForCapture);
   virtual ~TestPcapCapture();

   void OnTestStart(const testing::TestInfo& /*test_info*/);
   void OnTestEnd(const testing::TestInfo& /*test_info*/);
   void OnTestProgramEnd(const testing::UnitTest&);
   
private:
   CPCAPI2::Phone* mPhone;
   std::set<std::string> mTestCasesForCapture;
   bool mCapturing;
   std::string mCaptureFilename;
};


#endif /* test_pcap_capture_h */
