//
//  network_utils.h
//

#ifndef network_utils_h
#define network_utils_h

#include <stdio.h>
#include <unordered_set>
#include <unordered_map>
#include <functional>
#include <mutex>

#ifdef WIN32
#include <winsock2.h>
#include <ws2tcpip.h>
#include <wspiapi.h>
#else
#include <sys/socket.h>
#endif

namespace CPCAPI2
{

namespace test
{

class NetworkUtils
{
public:
   static int setDockerContainerNetworkEnabled(bool enabled);
   static int shutdownTcpSockets();
   static void listTcpSockets();
};

// When instantiated, allocates all RTP ports in recon such that future INVITES/re-INVITES will
// fail to find any free ports
class ReconRtpPortSaturator
{
public:
   ReconRtpPortSaturator();
   ~ReconRtpPortSaturator();
   
private:
   std::unordered_set<unsigned int> mBlockedUDPPorts;
};


class TurnAsyncUdpSocket_OutgoingPacketlossInducer
{
public:
   class Config
   {
   public:
      Config()
      {
         lossRatePct = 0;
      }
      int lossRatePct;
   };

   // warning: currently drops both RTP + RTCP when an instance of this class is active.
   // only one instance of this class is supported at a time.
   TurnAsyncUdpSocket_OutgoingPacketlossInducer(const Config&);
   ~TurnAsyncUdpSocket_OutgoingPacketlossInducer();
   
   // todo: make private
   bool shouldDropOutgoingPacket(const sockaddr* sa);

private:
   //class DestStats
   //{
   //public:
   //   DestStats()
   //};
   //std::unordered_map< std::size_t, DestStats > mDests;
   //std::mutex mDestsMutex;
   Config mConfig;
};


}
}


#endif /* network_utils_h */
