#include "sipp_runner.h"
#include "cpcapi2_test_framework.h"
#include "cpcapi2_test_fixture.h"

#include <array>

using namespace CPCAPI2::test;

SippRunner::SippRunner(SippRunnerSettings& settings) :
   mStarted(false),
   mSettings(settings)
{
   if (settings.transport == SippRunnerSettings::Transport_Tcp)
   {
      // tcp not yet supported -- probably just need to lookup the right flag to pass to sipp to add this support
      assert(false);
   }

}

SippRunner::~SippRunner()
{
   if (mStarted)
   {
      stop();
   }
}

std::string SippRunner::exec(const char* logPrefix, const char* cmd)
{
   std::string result;
#ifndef _WIN32
   std::array<char, 128> buffer;
   std::unique_ptr<FILE, decltype(&pclose)> pipe(popen(cmd, "r"), pclose);
   if (!pipe)
   {
      throw std::runtime_error("popen() failed!");
   }
   while (fgets(buffer.data(), buffer.size(), pipe.get()) != nullptr)
   {
      safeCout(">>> " << logPrefix << ": " << buffer.data());
   }
#endif
   return result;
}

int SippRunner::start()
{
#ifndef _WIN32
   if (mSettings.scenarioFileName.size() == 0)
   {
      return kError;
   }
   
#ifdef __APPLE__
   static bool sudoChecked = false;
   if (!sudoChecked) // system call below fails if called more than once
   {
      sudoChecked = true;
      int whoamiRet = system("sudo whoami");
      EXPECT_EQ(whoamiRet, 0) << "sudo failed; make sure you have adjusted via visudo for passwordless sudo";
      if (whoamiRet != 0)
      {
         return kError;
      }
   }
#endif

   mSippExec = std::async(std::launch::async, [&] () -> void
   {
      exec("sipp control", "sudo killall sipp");
      signal(SIGCHLD, SIG_IGN); // ignore SIGCHILD

      std::stringstream ss;
      
      // sudo is currently required for packet replay with sipp per https://github.com/SIPp/sipp/issues/368
#ifdef __APPLE__
      ss << "sudo " << TestEnvironmentConfig::testResourcePath() << "sipp/bin/sipp ";
#elif defined(__linux__) && !defined(ANDROID)
      ss << "sudo sipp ";
#else
      return;
#endif
      
      ss << "-i " << mSettings.sipListenIp << " -mi 127.0.0.1 " << mSettings.sipTargetIp << ":" << mSettings.sipTargetPort << " ";
      ss << "-p " << mSettings.sipListenPort << " -mp " << mSettings.mediaPort << " ";
      ss << "-r 1 -l 1 ";

      //    -timeout         : Global timeout. Default unit is seconds.  If this option is set, SIPp quits
      //                       after nb units (-timeout 20s quits after 20 seconds).
      ss << "-timeout " << mSettings.timeoutSec << " ";
      ss << "-m " << mSettings.callLimit << " "; // limit to x calls; i.e. don't repeat the scenario after x calls complete
      ss << "-s " << mSettings.requestUriUsername;
      ss << " -sf " << TestEnvironmentConfig::testResourcePath() << "sipp/scenarios/" << mSettings.scenarioFileName << " ";
      if (mSettings.transport == SippRunnerSettings::Transport_Tls)
      {
         ss << " -t l1 "; // per sipp docs: "In TLS multi socket mode (-t ln command line parameter), one secured TLS (Transport Layer Security)
                         //                 socket is opened for each new call between SIPp and the remote."
         ss << " -tls_cert " << TestEnvironmentConfig::testResourcePath() << "cert.pem -tls_key " << TestEnvironmentConfig::testResourcePath() << "key.pem ";
      }
      ss << "2>&1";

      mStarted = true;
      
      safeCout("About to run sipp with command line argument: " << ss.str().c_str());
      
      exec("sipp", ss.str().c_str());
      
      return;
   });
   
   if (mSettings.transport == SippRunnerSettings::Transport_Tls)
   {
      // sipp seems to take longer to startup if used in TLS mode; perhaps reading certificates, etc.
      // TODO: make this delay dynamic
      std::this_thread::sleep_for(std::chrono::seconds(3));
   }
#endif
   return kSuccess;
}

void SippRunner::stop()
{
#ifndef _WIN32
   if (mStarted)
   {
      exec("sipp control", "sudo killall sipp");
      waitFor(mSippExec);
      mStarted = false;
   }
#endif
}
