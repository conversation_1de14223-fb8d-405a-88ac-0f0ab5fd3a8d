#ifndef test_runtime_environment
#define test_runtime_environment

namespace CPCAPI2
{
namespace test
{   
   class TestRuntimeEnvironment
   {
   public:
      static TestRuntimeEnvironment* instance();

      // Android specific
      void setAndroidAppContext(void* context) { mAndroidAppContext = context; }
      void* getAndroidAppContext() { return mAndroidAppContext; }
      
      // helpers:
      bool checkDnsServer();

   private:
      TestRuntimeEnvironment();

      void* mAndroidAppContext;
   };
}
}

#endif /* test_runtime_environment */
