#include "http_test_framework.h"


using namespace CPCAPI2;
using namespace CPCAPI2::HttpTestFramework;


FakeFileStream::FakeFileStream(const char* data, unsigned int len)
: begin_(data), end_(data + len), current_(data)
{
}
 
FakeFileStream::int_type FakeFileStream::underflow() {
    if (current_ == end_) {
        return traits_type::eof();
    }
    return traits_type::to_int_type(*current_);
}
 
FakeFileStream::int_type FakeFileStream::uflow() {
    if (current_ == end_) {
        return traits_type::eof();
    }
    return traits_type::to_int_type(*current_++);
}
 
FakeFileStream::int_type FakeFileStream::pbackfail(int_type ch) {
    if (current_ == begin_ || (ch != traits_type::eof() && ch != current_[-1])) {
        return traits_type::eof();
    }
    return traits_type::to_int_type(*--current_);
}
 
std::streamsize FakeFileStream::showmanyc()
{
    return end_ - current_;
}


// based on sample code from https://github.com/eidheim/Simple-Web-Server/blob/master/http_examples.cpp
void FileSender::resourceSendStream(const HttpServer& server, const std::shared_ptr<HttpServer::Response>& response,
                                    const std::shared_ptr<std::istream>& ifs, size_t streamSizeBytes,
                                    std::chrono::duration<int> minDownloadTimeSec)
{
   // read and send 128 KB at a time
   static std::vector<char> buffer(131072); // assumes single thread access
   std::streamsize read_length = ifs->read(&buffer[0], buffer.size()).gcount();

   long numChunks = streamSizeBytes / buffer.size();
   std::chrono::duration<int, std::milli> chunkDelay = std::chrono::duration_cast<std::chrono::milliseconds>(minDownloadTimeSec) / numChunks;

   if (read_length > 0)
   {
      response->write(&buffer[0], read_length);
      if(read_length==static_cast<std::streamsize>(buffer.size()))
      {
         response->send([&server, response, ifs, streamSizeBytes,
                                chunkDelay, minDownloadTimeSec](const boost::system::error_code &ec)
         {
            // warning: occurs asynchronously
         
            if(!ec)
            {
               std::this_thread::sleep_for(chunkDelay);
               resourceSendStream(server, response, ifs, streamSizeBytes, minDownloadTimeSec);
            }
            else
            {
               std::cerr << "Connection interrupted" << std::endl;
            }
         });
      }
   }
}

void FileSender::resourceSendStreamBogusBytes(const HttpServer& server, const std::shared_ptr<HttpServer::Response>& response,
   size_t streamSizeBytes)
{
   resourceSendStreamBogusBytes(server, response, streamSizeBytes, streamSizeBytes);
}

void FileSender::resourceSendStreamBogusBytes(const HttpServer& server, const std::shared_ptr<HttpServer::Response>& response,
                                              size_t streamSizeBytes,
                                              size_t totalRemainingBytes)
{
   // read and send 128 KB at a time
   static std::vector<char> buffer(131072); // assumes single thread access
   //totalRemainingBytes -= buffer.size();
   //std::streamsize read_length = totalRemainingBytes ;
   size_t read_length = 0;
   if (totalRemainingBytes > buffer.size())
   {
      read_length = buffer.size();
   }
   else
   {
      read_length = totalRemainingBytes;
   }
   
   totalRemainingBytes -= read_length;

   
   if (read_length > 0)
   {
      response->write(&buffer[0], read_length);
      if(read_length==static_cast<std::streamsize>(buffer.size()))
      {
         response->send([&server, response, streamSizeBytes,
                                totalRemainingBytes](const boost::system::error_code &ec)
         {
            // warning: occurs asynchronously

            if(!ec)
            {
               resourceSendStreamBogusBytes(server, response, streamSizeBytes, totalRemainingBytes);
            }
            else
            {
               std::cerr << "Connection interrupted" << std::endl;
            }
         });
      }
   }
}

void FileSender::resourceSendStreamHault(const HttpServer& server, const std::shared_ptr<HttpServer::Response>& response,
                                         std::chrono::system_clock::time_point hangPoint, const CvWrapper& cv)
{
   // read and send 128 KB at a time
   static std::vector<char> buffer(131072); // assumes single thread access
   // this sender sends bogus data (verification of data by the receiver is not intended)
   
   response->write(&buffer[0], buffer.size());
   
   if (std::chrono::system_clock::now() >= hangPoint)
   {
      // hang until condition variable hit
      
      std::unique_lock<std::mutex> lk(*cv.mx.get());
      cv.cv->wait(lk);
   }
   else
   {
      // keep sending until we reach hangPoint
   
      response->send([&server, response, hangPoint, cv](const boost::system::error_code &ec)
      {
         // warning: occurs asynchronously
      
         if(!ec)
         {
            std::this_thread::sleep_for(std::chrono::milliseconds(250));
            resourceSendStreamHault(server, response, hangPoint, cv);
         }
         else
         {
            std::cerr << "Connection interrupted" << std::endl;
         }
      });
   }
}
