#include "android_bg_bookkeeper.h"
#include "cpcapi2_test_framework.h"

using namespace CPCAPI2::test;

AndroidBackgroundingBookkeeper::AndroidBackgroundingBookkeeper() :
   mWakeAcquireCount(0),
   mWakeReleaseCount(0),
   mWakeLockActiveCount(0),
   mTotalWakeAcquireDuration(std::chrono::milliseconds(0))
{
}

AndroidBackgroundingBookkeeper::~AndroidBackgroundingBookkeeper()
{
}

void AndroidBackgroundingBookkeeper::onWakelockAcquire()
{
   std::lock_guard<std::mutex> lock(mMutex);
   ++mWakeAcquireCount;
   ++mWakeLockActiveCount;
   if (mWakeLockActiveCount == 1)
   {
      safeCout("onWakelockAcquire - wakelock not previously held");
      mLastWakeAcquirePoint = std::chrono::system_clock::now();
   }
   else
   {
      safeCout("onWakelockAcquire - wakelock renew");
   }
}
void AndroidBackgroundingBookkeeper::onWakelockRelease()
{
   std::lock_guard<std::mutex> lock(mMutex);

   ++mWakeReleaseCount;
   --mWakeLockActiveCount;
   if (mWakeLockActiveCount == 0)
   {
         auto d = std::chrono::system_clock::now() - mLastWakeAcquirePoint;
         mTotalWakeAcquireDuration += std::chrono::duration_cast<std::chrono::milliseconds>(d);
         safeCout("onWakelockRelease - all wakelocks released; total duration: " << mTotalWakeAcquireDuration.count() << " ms");
   }
   else
   {
      std::chrono::milliseconds d = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now() - mLastWakeAcquirePoint);
      d += mTotalWakeAcquireDuration;
      safeCout("onWakelockRelease - " << mWakeLockActiveCount << " wakelocks still held; total duration: " << d.count() << " ms and counting");
   }
}

int AndroidBackgroundingBookkeeper::getWakeAcquireCount()
{
   std::lock_guard<std::mutex> lock(mMutex);
   return mWakeAcquireCount;
}

int AndroidBackgroundingBookkeeper::getWakeReleaseCount()
{
   std::lock_guard<std::mutex> lock(mMutex);
   return mWakeReleaseCount;
}

std::chrono::milliseconds AndroidBackgroundingBookkeeper::getTotalWakeAcquireDurationMs()
{
   std::lock_guard<std::mutex> lock(mMutex);
   if (mWakeLockActiveCount > 0)
   {
      // there is an active wakelock being held, so mTotalWakeAcquireDuration has not been fully updated
      std::chrono::milliseconds d = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now() - mLastWakeAcquirePoint);
      d += mTotalWakeAcquireDuration;
      return d;
   }
   else
   {
      return mTotalWakeAcquireDuration;
   }
}

void AndroidBackgroundingBookkeeper::clearCounters()
{
   std::lock_guard<std::mutex> lock(mMutex);

   mWakeAcquireCount = 0;
   mWakeReleaseCount = 0;
   // don't clear mWakeLockActiveCount

   mTotalWakeAcquireDuration = std::chrono::milliseconds(0);

   if (mWakeLockActiveCount > 0)
   {
      mLastWakeAcquirePoint = std::chrono::system_clock::now();
   }
}
