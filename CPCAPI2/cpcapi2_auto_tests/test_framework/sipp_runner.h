#ifndef sipp_runner_hpp
#define sipp_runner_hpp

#include <string>
#include <future>
#include <atomic>

#ifdef __APPLE__
// we haven't built/tested sipp for all platforms yet
#define CPCAPI2_PLATFORM_SUPPORTS_SIPP 1
#endif

namespace CPCAPI2
{
namespace test
{
   struct SippRunnerSettings
   {
      enum Transport
      {
         Transport_Udp,
         Transport_Tcp,
         Transport_Tls
      };
   
      int sipListenPort;
      std::string sipListenIp;
      int mediaPort;
      int timeoutSec;
      std::string scenarioFileName;
      std::string sipTargetIp; // target for scenarios where sipp sends an outgoing INVITE
      int sipTargetPort; // target for scenarios where sipp sends an outgoing INVITE
      std::string requestUriUsername;
      Transport transport;
      int callLimit = 1;
      
      
      SippRunnerSettings()
      {
         sipListenPort = 50010;
         sipListenIp = "127.0.0.1";
         mediaPort = 50020;
         timeoutSec = 30;
         sipTargetIp = "127.0.0.1";
         sipTargetPort = 55060;
         requestUriUsername = "alice";
         transport = Transport_Udp;
      }
   };

   class SippRunner
   {
   public:
      SippRunner(SippRunnerSettings& settings);
      virtual ~SippRunner();
      
      int start();
      void stop();
      
   private:
      std::string exec(const char* logPrefix, const char* cmd);
   
      std::atomic_bool mStarted;
      std::future<void> mSippExec;
      SippRunnerSettings mSettings;
   };
}
}


#include <stdio.h>

#endif /* sipp_runner_hpp */
