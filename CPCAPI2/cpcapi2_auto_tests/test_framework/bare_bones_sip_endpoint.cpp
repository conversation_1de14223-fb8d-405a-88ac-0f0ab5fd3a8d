#include "bare_bones_sip_endpoint.h"

#include <resip/stack/Helper.hxx>
#include <rutil/Logger.hxx>

using namespace resip;

#define RESIPROCATE_SUBSYSTEM resip::Subsystem::NONE

namespace CPCAPI2
{
namespace test
{
BareBonesSipEndpoint::BareBonesSipEndpoint()
	: mStack(NULL), mStackThread(NULL), mSelectInterruptor(NULL), mFdPollGrp(FdPollGrp::create(/*"fdset"*/)),
      mLastRequest(NULL), mLastResponse(NULL)
{
}

BareBonesSipEndpoint::~BareBonesSipEndpoint()
{
}

void BareBonesSipEndpoint::initialize()
{
	//SipStackOptions stackOptions;
	//DnsStub::NameserverList* nameservers = new DnsStub::NameserverList();
	//sockaddr_in sai_google_public_dns;
	//memset(&sai_google_public_dns, 0, sizeof(sockaddr_in));
	//inet_aton("*******\0", &sai_google_public_dns.sin_addr);
	//sai_google_public_dns.sin_port = htons(53);
	//sai_google_public_dns.sin_family = AF_INET;
	//nameservers->push_back(GenericIPAddress(sai_google_public_dns));
	//stackOptions.mExtraNameserverList = nameservers;
	//mSelectInterruptor = new EventThreadInterruptor(*mFdPollGrp);
	//mStackThread = new EventStackThread(*mSelectInterruptor, *mFdPollGrp);
	//stackOptions.mPollGrp = mFdPollGrp;
	//stackOptions.mAsyncProcessHandler = mSelectInterruptor;
	//mStack = new SipStack(stackOptions);

   mSelectInterruptor = new EventThreadInterruptor(*mFdPollGrp);
   mStackThread = new EventStackThread(*mSelectInterruptor, *mFdPollGrp);
   mStack = new SipStack(0, DnsStub::EmptyNameserverList, 0, false, 0, 0, 0, mFdPollGrp);

	mStackThread->addStack(*mStack);

	mStack->addTransport(resip::UDP, 0, V4);

	mStack->registerTransactionUser(*this);

	mStackThread->run();
}

bool BareBonesSipEndpoint::registerEndpoint(
	const resip::Uri& aor,
	const resip::Data& password,
	const resip::Uri* proxy
	)
{
	resip::SipMessage* registerMsg = Helper::makeRegister(NameAddr(aor), "UDP");
	InfoLog(<< "sending REGISTER: " << *registerMsg);
	mStack->send(std::unique_ptr<SipMessage>(registerMsg), this);
	resip::Message* msg = this->mFifo.getNext(32000);
	resip::SipMessage* sipMsg = dynamic_cast<SipMessage*>(msg);
	if (sipMsg)
	{
		if (sipMsg->isResponse())
		{
			if (sipMsg->header(h_StatusLine).responseCode() == 200)
			{
				return true;
			}
		}
	}
	return false;
}

bool BareBonesSipEndpoint::expectRequest(
	const std::string& requestMethod,
	unsigned int timeoutMS,
	std::function<void(void)> f
	)
{
	resip::Message* msg = this->mFifo.getNext(timeoutMS);
	resip::SipMessage* sipMsg = dynamic_cast<SipMessage*>(msg);
	if (sipMsg)
	{
		if (sipMsg->isRequest())
		{
			mLastRequest = sipMsg;
			if (sipMsg->header(h_RequestLine).method() == resip::getMethodType(requestMethod.c_str()))
			{
				f();
				return true;
			}
		}
	}
	return false;
}

bool BareBonesSipEndpoint::expectResponse(
	const std::string& requestMethod,
	unsigned int responseCode,
	unsigned int timeoutMS,
	std::function<void(void)> f
	)
{
	return true;
}

resip::SipMessage* BareBonesSipEndpoint::makeRequest(
	const std::string& requestMethod,
	const resip::Uri& to
	)
{
	return NULL;
}

resip::SipMessage* BareBonesSipEndpoint::makeRequest(
	const std::string& requestMethod,
	resip::DialogId* dialogId
	)
{
	return NULL;
}

resip::SipMessage* BareBonesSipEndpoint::makeResponse(
	unsigned int responseCode,
	resip::SipMessage* msg
	)
{
	return NULL;
}

bool BareBonesSipEndpoint::send(const resip::SipMessage& msg)
{
	return true;
}

const resip::SipMessage* BareBonesSipEndpoint::lastRequest() const
{
	return mLastRequest;
}

const resip::SipMessage* BareBonesSipEndpoint::lastResponse() const
{
	return mLastResponse;
}

}
}
