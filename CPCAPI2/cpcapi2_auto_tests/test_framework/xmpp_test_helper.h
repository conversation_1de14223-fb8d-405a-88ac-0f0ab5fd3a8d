#pragma once
#ifndef CPCAPI2_XMPP_TEST_HELPER_H
#define CPCAPI2_XMPP_TEST_HELPER_H

#include "../cpcapi2_test_fixture.h"

#define XMPP_AUTH_SERVER_HTTP_PORT 18084
#define XMPP_AGENT_ORCH_SERVER_HTTP_PORT 18082
#define XMPP_WATCHDOG_ORCH_SERVER_HTTP_PORT 18083
#define XMPP_AGENT_WS_PORT 9003
#define XMPP_WATCHDOG_WS_PORT 9004
#define ORCH_REDIS_IP "mock"
#define ORCH_REDIS_PORT 6379
#define PUSH_REDIS_IP "pushdbsqlite"
#define PUSH_REDIS_PORT 9999
#define XMPP_AGENT_REGION "NA"


static const char* DefaultXmppUserList = "user001,user002,user003,user004,user005,user006,user007";

struct XmppTestUserInfo
{
   std::string username;
   std::string password;
   int priority;

   XmppTestUserInfo() : priority(0) {}
   XmppTestUserInfo(const std::string& name) : username(name), password(name), priority(0)  {}
   bool operator<(const XmppTestUserInfo& otherUser) const { return priority > otherUser.priority; }
};

class XmppTestAccount;

struct XmppTestAccountConfig
{

   cpc::string name;
   XmppTestUserInfo info;
   CPCAPI2::XmppAccount::XmppAccountSettings settings;
   CPCAPI2::MessageStore::Settings msSettings;
   CPCAPI2::RemoteSync::RemoteSyncSettings remoteSyncSettings;
   cpc::string nick; // used for joining MUC rooms -- by default uses part random characters
   cpc::string authToken;
   bool didCheckout;
   bool useJsonProxy;

   XmppTestAccountConfig(const std::string& name, const std::string& xmppUser = "");
   XmppTestAccountConfig(const XmppTestAccountConfig& config, const std::string& resource);
   ~XmppTestAccountConfig();
   const cpc::string full() const; // full jid
   const cpc::string bare() const; // bare jid

   static void getXmppAgentServerUrls(std::string& authHttpUrl, std::string& orchHttpUrl, std::string& jsonWsUrl);
   static void getWatchdogServerUrls(std::string& authHttpUrl, std::string& orchHttpUrl, std::string& jsonWsUrl);
   static void getDefaultWatchdogConfig(CPCAPI2::CloudWatchdog::CloudWatchdogConfig& watchdogConfig);
   static void getDefaultCloudServices(cpc::vector<CPCAPI2::CloudConnector::ServiceDesc>& services);
   static void setCloudServicesInfo(CPCAPI2::Phone* phone, bool watchdog, bool started, bool shutdown = false);
   static void setupAuthServer(TestAccount& auth, bool useHTTPS = false);
   static void setupJsonApiServer(CPCAPI2::Phone* phone, int wsPort, int httpPort);
   static void setupOrchServer(CPCAPI2::Phone* phone);
   static void setupPushServer(CPCAPI2::Phone* phone);
   static void setupWatchdogServer(TestAccount& watchdog, CPCAPI2::CloudWatchdog::CloudWatchdogConfig& watchdogConfig);
   static void setupWatchdogServer(TestAccount& watchdog);
   static void destroyWatchdogServer(TestAccount& watchdog);
   static void setupXmppAgent(XmppTestAccount& agent, CPCAPI2::JsonApi::JsonApiServerConfig& jsonApiServCfg);
   static void setupXmppAgent(XmppTestAccount& agent);
   static void destroyXmppAgent(XmppTestAccount& agent);
   static void setupPublicOrchServer();
   static void generateJwt(const resip::Data& p8file, const resip::Data& userIdentity, resip::Data& jwt);

private:

   // don't support copying since it currently could result in problems with the checked out XMPP user
   XmppTestAccountConfig(const XmppTestAccountConfig& other);
   XmppTestAccountConfig& operator=(const XmppTestAccountConfig&);

};

class XmppTestAccount : public TestAccountBase
{

public:

   XmppTestAccount(const std::string& name, TestAccountInitMode initMode = Account_Enable, const std::string& xmppUser = "", CPCAPI2::Phone* p = NULL);

   // clone this account with different resource
   XmppTestAccount(const XmppTestAccount& existingAccount, const std::string& resource = "", TestAccountInitMode initMode = Account_Enable);

   virtual ~XmppTestAccount();

   void init();
   void createAccountJson();
   CPCAPI2::XmppVCard::XmppVCardHandle createVCardJson();
   CPCAPI2::XmppRoster::XmppRosterHandle createRosterJson();
   CPCAPI2::XmppAgent::XmppPushRegistrationHandle createAgentJson();
   CPCAPI2::PushEndpoint::PushNotificationEndpointHandle createPushEndpointJson();
   void createXmppFileTransferEventHandlers(std::set<CPCAPI2::XmppFileTransfer::XmppFileTransferHandle>& handlers);
   CPCAPI2::test::EventHandler* addXmppFileTransferEventHandler(CPCAPI2::XmppFileTransfer::XmppFileTransferHandle transferHandle);
   CPCAPI2::test::EventHandler* getXmppFileTransferEventHandler(CPCAPI2::XmppFileTransfer::XmppFileTransferHandle transferHandle);
   void enable(bool checkPresence = true, std::function<void(CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent& evt)> validator = NULL, bool assertRegistrationState = true);
   void disable(bool assertRegistrationState = true);
   void destroy(bool checkState = true);
   void closeSessions();

   // Warning: if docker is in use to run the auto tests, and disconnectDockerContainerNetworkIfAvailable
   // is enabled, tests may behave differently (e.g. changes to socket state may result in different account states)
   // compared to disconnectDockerContainerNetworkIfAvailable = false
   void disconnectNetwork(bool disconnectDockerContainerNetworkIfAvailable);
   void connectNetwork(bool connectDockerContainerNetworkIfAvailable);

   static void onPhoneRelease(void* p);

   CPCAPI2::Phone* phone;
   CPCAPI2::test::EventHandler* events;

   CPCAPI2::XmppAccount::XmppAccountManager* account;
   CPCAPI2::XmppAccount::XmppAccountHandle handle;
   CPCAPI2::XmppChat::XmppChatManager* chat;
   CPCAPI2::XmppRoster::XmppRosterManager* roster;
   CPCAPI2::XmppRoster::XmppRosterHandle rosterHandle;
   CPCAPI2::XmppFileTransfer::XmppFileTransferManager* fileTransferManager;
   CPCAPI2::XmppVCard::XmppVCardManager* vcardManager;
   CPCAPI2::XmppVCard::XmppVCardHandle vcardHandle;
   CPCAPI2::XmppMultiUserChat::XmppMultiUserChatManager* mucManager;
   CPCAPI2::XmppMultiUserChat::XmppMultiUserChatStateManager* mucStateManager;
   CPCAPI2::XmppIMCommand::XmppIMCommandManager* imCommandManager;
   CPCAPI2::XmppAgent::XmppPushRegistrationHandle agentHandle;
   std::map<CPCAPI2::XmppFileTransfer::XmppFileTransferHandle, CPCAPI2::test::EventHandler*> xmppFileTransferEventHandlers;
   CPCAPI2::XmppPush::XmppPushManager* xmppPush;

   CPCAPI2::JsonApi::JsonApiServer* jsonApiServer;
   CPCAPI2::test::EventHandler* jsonApiServerEvents;

   CPCAPI2::OrchestrationServer::OrchestrationServer* orchestrationServer = nullptr;
   CPCAPI2::test::EventHandler* orchestrationServerEvents;

   CPCAPI2::CloudWatchdog::CloudWatchdogService* cloudWatchdogServer;
   CPCAPI2::test::EventHandler* cloudWatchdogServerEvents;

   CPCAPI2::PushService::PushNotificationServiceManager* pushServer;
   CPCAPI2::test::EventHandler* pushServerEvents;

   CPCAPI2::CloudServiceConfig::CloudServiceConfigManager* cloudServiceConfig = nullptr;
   CPCAPI2::test::EventHandler* cloudServiceConfigEvents;

   CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUserHandle;

   CPCAPI2::JsonApi::JsonApiClient* jsonApiClient;
   CPCAPI2::test::EventHandler* jsonApiClientEvents;
   CPCAPI2::XmppAccount::XmppAccountManagerJsonProxy* accountJson;
   CPCAPI2::XmppChat::XmppChatManagerJsonProxy* chatJson;
   CPCAPI2::XmppRoster::XmppRosterManagerJsonProxy* rosterJson;
   CPCAPI2::XmppVCard::XmppVCardManagerJsonProxy* vcardJson;
   CPCAPI2::XmppMultiUserChat::XmppMultiUserChatManagerJsonProxy* mucJson;
   CPCAPI2::XmppAgent::XmppAgentManagerJsonProxy* agentJson;
   CPCAPI2::test::EventHandler* agentJsonEvents;

   CPCAPI2::PushEndpoint::PushNotificationEndpointHandle pushEndpointHandle;
   CPCAPI2::PushEndpoint::PushNotificationEndpointId pushEndpointId;
   CPCAPI2::PushEndpoint::PushNotificationEndpointManagerJsonProxy* pushEndpointJson;
   CPCAPI2::test::EventHandler* pushJsonEvents;

   CPCAPI2::CloudConnector::CloudConnectorManager* cloudConnector;
   CPCAPI2::test::EventHandler* cloudConnectorEvents;

   CPCAPI2::MessageStore::MessageStoreManager* messageStoreManager;
   CPCAPI2::test::EventHandler* messageStoreEvents;

#if 1 // (CPCAPI2_BRAND_REMOTE_SYNC_MODULE == 1)
   CPCAPI2::RemoteSync::RemoteSyncManager* remoteSync;
   CPCAPI2::test::EventHandler* remoteSyncEvents;
   CPCAPI2::RemoteSync::SessionHandle remoteSyncSession;

   CPCAPI2::RemoteSync::RemoteSyncJsonProxy* remoteSyncJsonProxy;
   CPCAPI2::test::EventHandler* remoteSyncJsonProxyEvents = nullptr;
   CPCAPI2::RemoteSync::SessionHandle remoteSyncJsonProxySession;
#endif

   CPCAPI2::NetworkChangeManager_Mock* network;

   XmppTestAccountConfig config;

   void setAccountStatusHandler(CPCAPI2::XmppAccount::XmppAccountHandler* handler);
   void propagateXmppAccountStatusEvent(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent& event);

   std::unique_ptr<AutoTestsLocalLogger> mLocalLogger;

   bool isDestroyed() { return destroyed; }
   bool isCloudConnected() { return cloudConnected; }
   void setCloudConnected(bool connected) { cloudConnected = connected; }
   void setInitialized(bool init) { initialized = init; }

protected:

   void destroyEventHandlers();

private:

   bool initialized;
   bool enabled;
   bool destroyed;
   bool cloudConnected;
   bool slave;
   bool phoneReleased;

   CPCAPI2::XmppAccount::XmppAccountHandler* statusHandler;

};

class XmppTestCloudAccount : public XmppTestAccount
{

public:

   XmppTestCloudAccount(const std::string& name);

   virtual ~XmppTestCloudAccount();

   void enable(bool withPush = false);
   void enableCloud();
   void enableXmppAccount(bool withPush = false);
   void disable();
   void destroy(bool doCleanup = true);
   void logout();
   void logoutAccount();

   void cleanup();
   CPCAPI2::Phone* getCloudPhone() { return clientCloudPhone; }
   CPCAPI2::CloudConnector::CloudConnectorHandle getCloudConnectorHandle() { return cloudConnectorHandle; }
   TestAccount* authServer;
   XmppTestAccount* agentServer;

private:

   XmppTestCloudAccount() : XmppTestAccount("xmpp") {};

   CPCAPI2::Phone* clientCloudPhone;
   bool cloudXmppAccountEnabled;
   CPCAPI2::CloudConnector::CloudConnectorHandle cloudConnectorHandle;
   CPCAPI2::XmppAccount::XmppAccountManagerJsonProxy* clientXmppAccountProxyOrig;
   CPCAPI2::XmppVCard::XmppVCardManagerJsonProxy* clientXmppVCardProxyOrig;
   CPCAPI2::XmppRoster::XmppRosterManagerJsonProxy* clientXmppRosterProxyOrig;
   CPCAPI2::XmppChat::XmppChatManagerJsonProxy* clientXmppChatProxyOrig;
   CPCAPI2::XmppMultiUserChat::XmppMultiUserChatManagerJsonProxy* clientXmppMUCProxyOrig;
   CPCAPI2::XmppAgent::XmppAgentManagerJsonProxy* clientXmppAgentProxyOrig;
   CPCAPI2::PushEndpoint::PushNotificationEndpointManagerJsonProxy* clientPushEndpointProxyOrig;

};

std::ostream& operator<<(std::ostream& os, const XmppTestUserInfo& user);
std::istream& operator>>(std::istream& is, XmppTestUserInfo& user);

template<typename TYPE> std::vector<TYPE> &split(const std::string &s, char delim, std::vector<TYPE> &elems)
{
   std::stringstream ss(s);
   std::string substr;
   while (std::getline(ss, substr, delim))
   {
      TYPE item;
      std::stringstream itemStream(substr);
      itemStream >> item;
      elems.push_back(item);
   }
   return elems;
}

template<typename TYPE> std::vector<TYPE> split(const std::string &s, char delim)
{
   std::vector<TYPE> elems;
   split(s, delim, elems);
   return elems;
}

class TestXmppEvents
{

public:

   static void expectXmppConnecting(int line, XmppTestAccount& account, std::function<void(CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent& evt)> validator = NULL);
   #define assertXmppConnecting(account) \
      { \
         SCOPED_TRACE((account).config.name); \
         TestXmppEvents::expectXmppConnecting(__LINE__, account); \
      }
   #define assertXmppConnectingEx(account, validator) \
      { \
         SCOPED_TRACE((account).config.name); \
         TestXmppEvents::expectXmppConnecting(__LINE__, account, validator); \
      }

   static void expectXmppConnected(int line, XmppTestAccount& account, std::function<void(CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent& evt)> validator = NULL);
   #define assertXmppConnected(account) \
      { \
         SCOPED_TRACE((account).config.name); \
         TestXmppEvents::expectXmppConnected(__LINE__, account); \
      }
   #define assertXmppConnectedEx(account, validator) \
      { \
         SCOPED_TRACE((account).config.name); \
         TestXmppEvents::expectXmppConnected(__LINE__, account, validator); \
      }

   static void expectXmppDisconnecting(int line, XmppTestAccount& account, std::function<void(CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent& evt)> validator = NULL);
   #define assertXmppDisconnecting(account) \
      { \
         SCOPED_TRACE((account).config.name); \
         TestXmppEvents::expectXmppDisconnecting(__LINE__, account); \
      }
   #define assertXmppDisconnectingEx(account, validator) \
      { \
         SCOPED_TRACE((account).config.name); \
         TestXmppEvents::expectXmppDisconnecting(__LINE__, account, validator); \
      }

   static void expectXmppDisconnected(int line, XmppTestAccount& account, std::function<void(CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent& evt)> validator = NULL);
   #define assertXmppDisconnected(account) \
      { \
         SCOPED_TRACE((account).config.name); \
         TestXmppEvents::expectXmppDisconnected(__LINE__, account); \
      }
   #define assertXmppDisconnectedEx(account, validator) \
      { \
         SCOPED_TRACE((account).config.name); \
         TestXmppEvents::expectXmppDisconnected(__LINE__, account, validator); \
      }

   static void expectXmppDestroyed(int line, XmppTestAccount& account, std::function<void(CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent& evt)> validator = NULL);
   #define assertXmppDestroyed(account) \
      { \
         SCOPED_TRACE((account).config.name); \
         TestXmppEvents::expectXmppDestroyed(__LINE__, account); \
      }
   #define assertXmppDestroyedEx(account, validator) \
      { \
         SCOPED_TRACE((account).config.name); \
         TestXmppEvents::expectXmppDestroyed(__LINE__, account, validator); \
      }

   static void expectXmppFailure(int line, XmppTestAccount& account, std::function<void(CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent& evt)> validator = NULL);
   #define assertXmppFailure(account) \
      { \
         SCOPED_TRACE((account).config.name); \
         TestXmppEvents::expectXmppFailure(__LINE__, account); \
      }
   #define assertXmppFailureEx(account, validator) \
      { \
         SCOPED_TRACE((account).config.name); \
         TestXmppEvents::expectXmppFailure(__LINE__, account, validator); \
      }

   static void expectXmppRosterUpdate(int line, XmppTestAccount& account,
         std::function<void(const CPCAPI2::XmppRoster::XmppRosterUpdateEvent& evt)> validator);
   #define assertXmppRosterUpdate(account, validator) \
      { \
         SCOPED_TRACE((account).config.name); \
         TestXmppEvents::expectXmppRosterUpdate(__LINE__, account, validator); \
      }

   static void expectXmppSelfPresence(int line, XmppTestAccount& account,
         std::function<void(CPCAPI2::XmppRoster::XmppRosterPresenceEvent& evt)> validator);
   #define assertXmppSelfPresence(account, validator) \
      { \
         SCOPED_TRACE((account).config.name); \
         TestXmppEvents::expectXmppSelfPresence(__LINE__, account, validator); \
      }

   static void expectXmppRosterPresence(int line, XmppTestAccount& account,
         std::function<void(CPCAPI2::XmppRoster::XmppRosterPresenceEvent& evt)> validator);
   #define assertXmppRosterPresence(account, validator) \
      { \
         SCOPED_TRACE((account).config.name); \
         TestXmppEvents::expectXmppRosterPresence(__LINE__, account, validator); \
      }

   static void expectXmppSubscriptionRequest(int line, XmppTestAccount& account,
         std::function<void(CPCAPI2::XmppRoster::XmppRosterSubscriptionRequestEvent& evt)> validator);
   #define assertXmppSubscriptionRequest(account, validator) \
      { \
         SCOPED_TRACE((account).config.name); \
         TestXmppEvents::expectXmppSubscriptionRequest(__LINE__, account, validator); \
      }

   static void expectXmppVCardFetched(int line, XmppTestAccount& account,
         std::function<void(CPCAPI2::XmppVCard::VCardFetchedEvent& evt)> validator);
   #define assertXmppVCardFetched(account, validator) \
      { \
         SCOPED_TRACE((account).config.name); \
         TestXmppEvents::expectXmppVCardFetched(__LINE__, account, validator); \
      }

   static void expectXmppVCardOperationResult(int line, XmppTestAccount& account,
         std::function<void(CPCAPI2::XmppVCard::VCardOperationResultEvent& evt)> validator);
   #define assertXmppVCardOperationResult(account, validator) \
      { \
         SCOPED_TRACE((account).config.name); \
         TestXmppEvents::expectXmppVCardOperationResult(__LINE__, account, validator); \
      }

   static void expectLoginResult(int line, XmppTestAccount& agentAccount, std::function<void(CPCAPI2::JsonApi::LoginResultEvent& evt)> validator);
   #define assertLoginResult(agentAccount, validator) \
         TestXmppEvents::expectLoginResult(__LINE__, agentAccount, validator)

   static void expectCloudConnectedForXmpp(int line, TestAccount& authAccount, XmppTestAccount& agentAccount, XmppTestAccount& clientAccount, CPCAPI2::Phone*& clientCloudPhone, CPCAPI2::CloudConnector::CloudConnectorHandle& cloudConnectorHandle,
         std::function<void(CPCAPI2::CloudConnector::ServiceConnectionStatusEvent& evt)> validator);
   #define assertCloudConnectedForXmpp(authAccount, agentAccount, clientAccount, clientCloudPhone, cloudConnectorHandle, validator) \
         TestXmppEvents::expectCloudConnectedForXmpp(__LINE__, authAccount, agentAccount, clientAccount, clientCloudPhone, cloudConnectorHandle, validator)

   static void expectCloudDisconnectedForXmpp(int line, XmppTestAccount& clientAccount, CPCAPI2::CloudConnector::CloudConnectorHandle cloudConnectorHandle,
         std::function<void(CPCAPI2::CloudConnector::ServiceConnectionStatusEvent& evt)> validator);
   #define assertCloudDisconnectedForXmpp(clientAccount, cloudConnectorHandle, validator) \
         TestXmppEvents::expectCloudDisconnectedForXmpp(__LINE__, clientAccount, cloudConnectorHandle, validator)

   static void expectCloudDestroyedForXmpp(int line, XmppTestAccount& clientAccount, CPCAPI2::CloudConnector::CloudConnectorHandle cloudConnectorHandle,
                                              std::function<void(CPCAPI2::CloudConnector::ServiceConnectionStatusEvent& evt)> validator);
   #define assertCloudDestroyedForXmpp(clientAccount, cloudConnectorHandle, validator) \
         TestXmppEvents::expectCloudDestroyedForXmpp(__LINE__, clientAccount, cloudConnectorHandle, validator)

   static void expectCloudXmppAccountEnabled(int line, TestAccount& authAccount, XmppTestAccount& agentAccount, XmppTestAccount& clientAccount, CPCAPI2::Phone*& clientCloudPhone, bool withPush, CPCAPI2::CloudConnector::CloudConnectorHandle& cloudConnectorHandle, std::function<void(CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent& evt)> validator);
   #define assertCloudXmppAccountEnabled(authAccount, agentAccount, clientAccount, clientCloudPhone, withPush, cloudConnectorHandle, validator) \
         TestXmppEvents::expectCloudXmppAccountEnabled(__LINE__, authAccount, agentAccount, clientAccount, clientCloudPhone, withPush, cloudConnectorHandle, validator)

   static void expectCloudLogoutForXmpp(int line, XmppTestAccount& clientAccount, CPCAPI2::CloudConnector::CloudConnectorHandle cloudConnectorHandle, std::function<void(CPCAPI2::CloudConnector::LogoutResult& evt)> validator);
   #define assertCloudLogoutForXmpp(clientAccount, cloudConnectorHandle, validator) \
         TestXmppEvents::expectCloudLogoutForXmpp(__LINE__, clientAccount, cloudConnectorHandle, validator)

   static void expectAgentLogoutForXmpp(int line, XmppTestAccount& clientAccount, CPCAPI2::XmppAgent::XmppPushRegistrationHandle pushRegistrationHandle, std::function<void(CPCAPI2::XmppAgent::LogoutResult& evt)> validator);
   #define assertAgentLogoutForXmpp(clientAccount, pushRegistrationHandle, validator) \
      TestXmppEvents::expectAgentLogoutForXmpp(__LINE__, clientAccount, pushRegistrationHandle, validator)
 
   static void expectEndpointPushRegistrationSuccess(int line, XmppTestAccount& clientAccount, std::function<void(CPCAPI2::PushEndpoint::PushRegistrationSuccessEvent& evt)> validator);
   #define assertEndpointPushRegistrationSuccess(clientAccount, validator) \
      TestXmppEvents::expectEndpointPushRegistrationSuccess(__LINE__, clientAccount, validator)
   
   static void expectAgentPushRegistrationSuccess(int line, XmppTestAccount& clientAccount, std::function<void(CPCAPI2::XmppAgent::XmppPushRegistrationSuccessEvent& evt)> validator);
   #define assertAgentPushRegistrationSuccess(clientAccount, validator) \
      TestXmppEvents::expectAgentPushRegistrationSuccess(__LINE__, clientAccount, validator)

   static void expectJsonClientLoginSuccess(int line, XmppTestAccount& clientAccount, XmppTestAccount& agentAccount, std::function<void(CPCAPI2::JsonApi::LoginResultEvent& evt)> validator);
   #define assertJsonClientLoginSuccess(clientAccount, agentAccount, validator) \
      TestXmppEvents::expectJsonClientLoginSuccess(__LINE__, clientAccount, agentAccount, validator)
};

#endif //CPCAPI2_XMPP_TEST_HELPER_H
