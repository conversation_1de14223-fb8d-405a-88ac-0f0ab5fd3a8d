#ifndef android_bg_bookkeeper
#define android_bg_bookkeeper

#include "../language_wrapper/Android/jni/AndroidBackgroundHandler.h"
#include <mutex>

namespace CPCAPI2
{
class Phone;

namespace test
{

class AndroidBackgroundingBookkeeper : public CPCAPI2::AndroidBackgroundHandlerInternal
{
public:
   AndroidBackgroundingBookkeeper();
   ~AndroidBackgroundingBookkeeper();

   void registerWithPhone(CPCAPI2::Phone* phone);

   /* 
    * Retrieve number of times a wakelock has been acquired or renewed. 
    * Resets to 0 when clearCounters is invoked.
    */
   int getWakeAcquireCount();
   
   /* 
    * Retrieve number of times a wakelock has been released.
    * Resets to 0 when clearCounters is invoked.
    */ 
   int getWakeReleaseCount();
   
   /* 
    * Retrieve total amount of time at least one wakelock has been held.
    * Resets to 0 when clearCounters is invoked.
    */
   std::chrono::milliseconds getTotalWakeAcquireDurationMs();

   /*
    * Clears above counters
    */
   void clearCounters();

private:
   void onWakelockAcquire();
   void onWakelockRelease();

private:
   int mWakeAcquireCount;
   int mWakeReleaseCount;
   int mWakeLockActiveCount;
   std::chrono::milliseconds mTotalWakeAcquireDuration;
   std::chrono::time_point<std::chrono::system_clock> mLastWakeAcquirePoint;
   std::mutex mMutex;
};



}
}

#endif // android_bg_bookkeeper