//
//  visqol_runner.cpp
//  CPCAPI2AutoTestsX
//
//  Created by <PERSON><PERSON><PERSON> on 2022-02-28.
//  Copyright © 2022 <PERSON>. All rights reserved.
//

//This class has only one method visqol_exec, which can only be used on Apple/Unix platforms, not on Windows, due to complications with file paths and the Unix specific functions used for creating child processes to execute the visqol binary. I tried using boost to overcome the boundaries restricting cross platform functionality, however boost process relies on boost filesystem, which is not included in our prebuilt boost library, and hence that approach was promptly abandoned. Hence, it would be wise, for instance, to surround any test using this class with guards such as ```#if defined ( __APPLE__)``` and ```#endif```.
//If full visqol functionality on Windows is verified, the code will be updated as such to accomodate for test functionality there as well.
//Path to visqol binary and required svr model is osx_libs/visqol. This file currently uses the binary from runtime_resources, where on my local machine I have the binary and model stored, but that won't be commited, hence be wary when running tests using this class.

#include "visqol_runner.h"
#include "cpcapi2_test_framework.h"
#include "cpcapi2_test_fixture.h"
#include "../external/rapidjson/document.h"
#include "../external/rapidjson/istreamwrapper.h"
#include <array>
#include <filesystem>

using namespace rapidjson;
using namespace CPCAPI2::test;

#if ((defined( __APPLE__) && defined(__aarch64__)) || (defined(__linux__) && !defined(ANDROID)))
void VisqolRunner::visqol_exec(const std::string& reference_file_path, const std::string& degraded_file_path, double& outMos) {
   cpc::string tempPath = TestEnvironmentConfig::testResourcePath();
   std::filesystem::path testPath;
   std::filesystem::path modelPath; //this will help derive a cross-platform path to the svr model for visqol
   
   if (tempPath.empty())
   {
      testPath = std::filesystem::current_path();
      modelPath = std::filesystem::current_path();
   }
   else
   {
      testPath = tempPath.c_str();
      modelPath = tempPath.c_str();
   }
   
   modelPath /= "visqol";
   modelPath /= "model";
   modelPath /= "libsvm_nu_svr_model.txt";
   std::string model_path = modelPath;
   
   safeCout("check testPath and modelPath");
   safeCout(testPath);
   safeCout(model_path);
   
   
   testPath /= "visqol";
   testPath /= "bin";
   testPath /= "visqol";
   std::string p = testPath; //p will be the entire command for executing the visqol binary
   p += " --reference_file ";
   p += reference_file_path;
   p += " --degraded_file ";
   p += degraded_file_path;
   p += " --verbose ";
   p += "--similarity_to_quality_model ";
   p += model_path;
   p += " --output_debug visqol_debug.json"; //this is optional, but makes for safer  and consistent extraction of mos
   
   
   //this step ensures that we don't append this time's output to a previous debug.json file
   if(!std::filesystem::remove("visqol_debug.json")) {
      safeCout("Error deleting visqol_debug.json");
   }
   
   safeCout("check visqol cmd");
   safeCout(p);
   
   
   const char * cmd = p.c_str();
   std::array<char, 128> buffer;
   std::string result;
   std::unique_ptr<FILE, decltype(&pclose)> pipe(popen(cmd, "r"), pclose);
   if(!pipe) {
      safeCout("popen() failed!");
      FAIL();
   }
   while (fgets(buffer.data(), buffer.size(), pipe.get()) != nullptr)
   {
      safeCout(buffer.data());
   }
   
   
   std::ifstream ifs("visqol_debug.json");
   IStreamWrapper isw(ifs);
   Document document;
   document.ParseStream(isw);
   ASSERT_TRUE(document.HasMember("moslqo"));
   ASSERT_TRUE(document["moslqo"].IsDouble());
   outMos = document["moslqo"].GetDouble();
   safeCout("moslqo = " << outMos);
}
#else // #if ((defined( __APPLE__) && defined(__aarch64__)) || (defined(__linux__) && !defined(ANDROID)))
   void VisqolRunner::visqol_exec(const std::string& reference_file_path, const std::string& degraded_file_path, double& outMos)
   {
      assert(0);
   }
#endif
