#include "breakpad_integration.h"
#include "cpcapi2_test_fixture.h"
#include "crash_dump_handling\cpcapi2_breakpad_server\cross_process_mutex_win32.h"

#include <iostream>
#include <cwchar>
#include <signal.h>

#include "client\windows\handler\exception_handler.h"


using namespace CPCAPI2::test;


bool FilterCallback(void* context, EXCEPTION_POINTERS* exinfo,
   MDRawAssertionInfo* assertion)
{
   return true;
}

bool MinidumpCallback(const wchar_t* dump_path,
   const wchar_t* minidump_id,
   void* context,
   EXCEPTION_POINTERS* exinfo,
   MDRawAssertionInfo* assertion,
   bool succeeded)
{
   return succeeded;
}

// lifted from breakpad's crash_generatin_client.cc
HANDLE ConnectToPipe(const wchar_t* pipe_name)
{
   const DWORD pipe_access = FILE_READ_DATA |
      FILE_WRITE_DATA |
      FILE_WRITE_ATTRIBUTES;

   const DWORD flags_attrs = SECURITY_IDENTIFICATION |
      SECURITY_SQOS_PRESENT;

   const int kPipeConnectMaxAttempts = 3;
   const int kPipeBusyWaitTimeoutMs = 2000;
   const int kPipeNoExistTimeoutMs = 500;

   for (int i = 0; i < kPipeConnectMaxAttempts; ++i) {
      HANDLE pipe = CreateFileW(pipe_name,
         pipe_access,
         0,
         NULL,
         OPEN_EXISTING,
         flags_attrs,
         NULL);
      if (pipe != INVALID_HANDLE_VALUE) 
      {
         return pipe;
      }
      else
      {
         ::Sleep(kPipeNoExistTimeoutMs);
         continue;
      }

      // Cannot continue retrying if error is something other than
      // ERROR_PIPE_BUSY.
      if (GetLastError() != ERROR_PIPE_BUSY && GetLastError() != ERROR_FILE_NOT_FOUND) {
         break;
      }

      // Cannot continue retrying if wait on pipe fails.
      if (!WaitNamedPipeW(pipe_name, kPipeBusyWaitTimeoutMs)) {
         break;
      }
   }

   return NULL;
}


void ForceCrashFn(int signalNumber)
{
   (void)signalNumber;

   int* i = 0;
   *i = 0;
}

void ForceCrashFn()
{
   int* i = 0;
   *i = 0;
}

void Breakpad::registerBreakpad()
{
   signal(SIGABRT, &ForceCrashFn);
   _set_purecall_handler(ForceCrashFn);  

   std::stringstream pipeName;
   pipeName << "\\\\.\\pipe\\cpcapi2\\cpcapi2_breakpad_server_" << resip::Random::getCryptoRandomHex(6).c_str();

   STARTUPINFOW si;
   PROCESS_INFORMATION pi;

   ZeroMemory(&si, sizeof(si));
   si.cb = sizeof(si);
   ZeroMemory(&pi, sizeof(pi));

   wchar_t workingDir[1024];
   GetCurrentDirectoryW(sizeof(workingDir), workingDir);

   std::wstringstream dumpDir;
   dumpDir << workingDir << "\\dumps";

   cpc::string nonce(resip::Random::getCryptoRandomHex(16).c_str());
   std::wstring mutexNonce(nonce);

   static CrossProcessMutex* mAutoTestsAliveMutex = NULL;
   if (!mAutoTestsAliveMutex)
   {
      mAutoTestsAliveMutex = new CrossProcessMutex(mutexNonce);
   }

   std::wstringstream command;
   std::string dumpFilename(TestEnvironmentConfig::crashDumpFileName());
   std::wstring dumpFilenameW(dumpFilename.begin(), dumpFilename.end());
   const std::wstring dq(L"\"");
   command << "cpcapi2_breakpad_server.exe " << dq << cpc::string(pipeName.str().c_str()) << dq << " " << dq << dumpDir.str() << dq
      << " " << dq << dumpFilenameW << dq << " " << dq << mutexNonce << dq;
   wchar_t cmd[512];
   wcscpy_s(cmd, 512, command.str().c_str());

   std::wofstream of("dumps\\client_inst.txt", std::fstream::in | std::fstream::out | std::fstream::app);
   of << "invoking: " << cmd << std::endl;
   of << std::endl;
   of.close();

   // Start the child process. 
   if (!CreateProcessW(NULL,   // No module name (use command line)
      cmd,        // Command line
      NULL,           // Process handle not inheritable
      NULL,           // Thread handle not inheritable
      FALSE,          // Set handle inheritance to FALSE
      0,              // No creation flags
      NULL,           // Use parent's environment block
      NULL,           // Use parent's starting directory 
      &si,            // Pointer to STARTUPINFO structure
      &pi)           // Pointer to PROCESS_INFORMATION structure
      )
   {
      std::cout << "CreateProcess failed (" << GetLastError() << ")." << std::endl;
      return;
   }


   HANDLE pipe = ConnectToPipe(std::wstring(cpc::string(pipeName.str().c_str())).c_str());

   _MINIDUMP_TYPE minidumpType = MiniDumpNormal;
   if (TestEnvironmentConfig::fullMemoryCrashdumps())
   {
      minidumpType = MiniDumpWithFullMemory;
   }

   google_breakpad::ExceptionHandler *pHandler = new google_breakpad::ExceptionHandler(
      dumpDir.str().c_str(),
      FilterCallback,
      MinidumpCallback,
      0,
      google_breakpad::ExceptionHandler::HANDLER_ALL,
      minidumpType,
      pipe,
      0);

   (void)pHandler;
}
