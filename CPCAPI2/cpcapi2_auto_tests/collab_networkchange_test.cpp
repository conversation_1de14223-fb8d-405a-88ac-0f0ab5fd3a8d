#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#if (CPCAPI2_BRAND_VCCS_MODULE == 1)

#include "cpcapi2_test_fixture.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>

#include "test_framework/bare_bones_sip_endpoint.h"
#include "test_framework/cpcapi2_test_framework.h"
#include "vccs_test_harness/VccsTestHarness.h"
#include "test_account_events.h"
#include "test_call_events.h"
#include "test_framework/xmpp_test_helper.h"
#include <vccs/VccsAccountManagerInterface.h>
#include "test_framework/xmpp_test_helper.h"
#include "impl/call/SipConversationManagerInternal.h"
#include "../experimental/audio_ext/AudioExt.h"
#include <cpcstl/string.h>
#include <string>
#include <thread>
#include <future>
#include <memory>
#include <boost/algorithm/string.hpp>


using namespace CPCAPI2;
using namespace CPCAPI2::VCCS;
using namespace CPCAPI2::test;

struct CollabNetworkChangeTestConfig
{
   cpc::vector<Account::VccsAccountSettings> vccsSettings;
   cpc::string vccsURL;
   int callDurationMilliSec;
   cpc::string wavFile;
   cpc::string xmppProxy;
   int waitTimeForConferenceUpdateMilliSec;
   bool skipXmppTests;
   SipConversation::MediaDirection videoMediaDirection;
   bool skipParticipantCallStatusCheck;
   cpc::string appId;

   CollabNetworkChangeTestConfig()
   {
      cpc::string vccsURL = "";
      int callDurationMilliSec = 10000;
      cpc::vector<Account::VccsAccountSettings> vccsAccountSettings;
      cpc::string wavFile;
      cpc::string xmppProxy;
      bool skipXmppTests = false;
      int waitTimeForConferenceUpdateMilliSec = 20000;
      videoMediaDirection = SipConversation::MediaDirection_SendReceive;
      bool skipParticipantCallStatusCheck = false;
   }
};

class CollabNetworkChangeTest : public CpcapiAutoTest
{
public:
   CollabNetworkChangeTest() : hwndAliceRemote(NULL), hwndAliceLocal(NULL){}
   virtual ~CollabNetworkChangeTest() {}

   void parseConfigfile(TestAccount& alice, CollabNetworkChangeTestConfig& outConfig);
   void setVccsAccount(TestAccount& alice, const Account::VccsAccountSettings& vccsAccountSettings);
   void subscribeConference(TestAccount& alice, CollabNetworkChangeTestConfig& config,
      Conference::ConferenceDetails& outDetails,
      Conference::VccsConferenceHandle& outConference,
      cpc::vector<CPCAPI2::SipAccount::SipAccountSettings>& outAccounts,
      cpc::vector<CPCAPI2::SipConversationSettings>& outConvs);
   void setupSipAccount(TestAccount& alice,
      const CPCAPI2::SipAccount::SipAccountSettings& accountSetting);
   void collabCall(TestAccount& alice, cpc::string confUri, Conference::VccsConferenceHandle hConference,
      int callDurationMilliSec, CPCAPI2::SipConversation::SipConversationHandle& aliceCall, const CollabNetworkChangeTestConfig& config);
   void collabCallEnd(TestAccount& alice, Conference::VccsConferenceHandle hConference,
      const CPCAPI2::SipConversation::SipConversationHandle& aliceCall, const CollabNetworkChangeTestConfig& config, const Conference::ConferenceDetails& confDetails);
   void testAudioVideoCollabCall(TestAccount& alice, Conference::VccsConferenceHandle hConference, const CPCAPI2::SipAccount::SipAccountSettings& sipAccountSettings,
      const CPCAPI2::SipConversationSettings& sipConvSettings, const Conference::ConferenceDetails& details, const CollabNetworkChangeTestConfig& config);

private:
   HWND hwndAliceRemote;
   HWND hwndAliceLocal;
};
class CollabConfigParse : public resip::ConfigParse
{
private:
   void parseCommandLine(int argc, char** argv, int skipCount = 0) {}
   void printHelpText(int argc, char **argv) {}
};

#define STATUS_LOG_FMT(x) "[[OK[" << x << "]]]"
#define FAIL_LOG_FMT(x) "[[NOK[" << x << "]]]"

TEST_F(CollabNetworkChangeTest, DISABLED_testCollabCallAndGroupChat) {

   TestAccount alice("alice", Account_NoInit);
   alice.config.phoneInitConnectionPrefs.networkChangeManagerType = ConnectionPreferences::NetworkChangeManagerType_PlatformDefault;
   alice.init();
   
   CollabNetworkChangeTestConfig collabConfig;
   ASSERT_NO_FATAL_FAILURE(parseConfigfile(alice, collabConfig));

   ASSERT_NE(collabConfig.vccsSettings.size(), 0) << FAIL_LOG_FMT("returned vccs account settings is empty");
   collabConfig.vccsSettings[0].autoSubscribeAfterNetworkChange = true;
   ASSERT_NO_FATAL_FAILURE(setVccsAccount(alice, collabConfig.vccsSettings[0]));
   
   Conference::ConferenceDetails details;
   Conference::VccsConferenceHandle hConference( -1 );

   cpc::vector<CPCAPI2::SipAccount::SipAccountSettings> accounts;
   cpc::vector<CPCAPI2::SipConversationSettings> convs;
      
   ASSERT_NO_FATAL_FAILURE(subscribeConference(alice, collabConfig, details, hConference, accounts, convs));
   ASSERT_NO_FATAL_FAILURE(testAudioVideoCollabCall(alice, hConference, accounts[0], convs[0], details, collabConfig));
}

void CollabNetworkChangeTest::testAudioVideoCollabCall(TestAccount& alice, Conference::VccsConferenceHandle hConference, const CPCAPI2::SipAccount::SipAccountSettings& sipAccountSettings,
                              const CPCAPI2::SipConversationSettings& sipConvSettings, const Conference::ConferenceDetails& details, const CollabNetworkChangeTestConfig& config)
{
   
   ASSERT_NO_FATAL_FAILURE(setupSipAccount(alice, sipAccountSettings));
   ASSERT_EQ(kSuccess, alice.conversation->setDefaultSettings(alice.handle, sipConvSettings));

   cpc::string confUri("sip:");
   confUri.append(details.lobbySipAddress);
   safeCout("conference uri is: " << confUri);
   CPCAPI2::SipConversation::SipConversationHandle aliceCall;
   ASSERT_NO_FATAL_FAILURE(collabCall(alice, confUri, hConference, config.callDurationMilliSec, aliceCall, config));
   ASSERT_NO_FATAL_FAILURE(collabCallEnd(alice, hConference, aliceCall, config, details));
}

void CollabNetworkChangeTest::parseConfigfile(TestAccount& alice, CollabNetworkChangeTestConfig& outConfig)
{
   CollabConfigParse configParse;
   configParse.parseConfig(0, NULL, (TestEnvironmentConfig::testResourcePath() + "cpcapi2_collab_networkchange_test.config").c_str());
   
   resip::Data accountSettingsFile;
   resip::Data collabLink;
   resip::Data wavFilePath;
   resip::Data xmppProxy;

   if (configParse.getConfigValue("accountSettingsFile", accountSettingsFile))
   {
      std::ifstream in((TestEnvironmentConfig::testResourcePath() + accountSettingsFile).c_str());
      assert(in.is_open());
      
      std::ostringstream iss;
      iss << in.rdbuf() << std::flush;
      
      cpc::string accountDoc = iss.str().c_str();
      
      ASSERT_EQ(kSuccess, alice.vccsAccountManager->decodeProvisioningResponse(accountDoc, outConfig.vccsSettings)) << FAIL_LOG_FMT("account provisioning file format is invalid");
   }
   if (configParse.getConfigValue("collabLink", collabLink))
   {
      outConfig.vccsURL = collabLink.c_str();
   }

   configParse.getConfigValue("callDurationMilliSec", outConfig.callDurationMilliSec);

   if (configParse.getConfigValue("wavFilePath", wavFilePath))
   {
      outConfig.wavFile = (TestEnvironmentConfig::testResourcePath() + wavFilePath).c_str();
   }
   
   if (configParse.getConfigValue("xmppProxy", xmppProxy))
   {
      cpc::string proxy = xmppProxy.c_str();
      if (proxy.size() != 0)
      {
         outConfig.xmppProxy = proxy;
         safeCout(STATUS_LOG_FMT(" using xmpp proxy: " << outConfig.xmppProxy ));
      }
      else
         safeCout("No xmpp proxy found in the config file, will be using the proxy value return from server");
   }
   else
      safeCout("No xmpp proxy option found in the config file, will be using the proxy value return from server");
   
   configParse.getConfigValue("waitTimeForConferenceUpdateMilliSec", outConfig.waitTimeForConferenceUpdateMilliSec);

   outConfig.skipXmppTests = false;
   if (configParse.getConfigValue("skipXmppTests", outConfig.skipXmppTests))
   {
      if (outConfig.skipXmppTests)
      {
         safeCout("skipXmppTests specified and true; skipping XMPP tests!");
      }
   }
   
   outConfig.videoMediaDirection = SipConversation::MediaDirection_SendReceive;
   resip::Data videoMediaDirectionData;
   configParse.getConfigValue("videoMediaDirection", videoMediaDirectionData);
   cpc::string videoMediaDirectionStr = videoMediaDirectionData.c_str();
   resip::Data appId;
   configParse.getConfigValue("appId", appId);
   outConfig.appId = appId.c_str();

   bool skipPartCheck;
   configParse.getConfigValue("skipParticipantCallStatusCheck", skipPartCheck);
   outConfig.skipParticipantCallStatusCheck = skipPartCheck;

   safeCout(STATUS_LOG_FMT("Done parsing config files"));
}

void CollabNetworkChangeTest::setVccsAccount(TestAccount& alice, const Account::VccsAccountSettings& vccsAccountsettings)
{
   int result = alice.vccsAccountManager->configureDefaultAccountSettings( alice.vccsAccountHandle, vccsAccountsettings);
   ASSERT_EQ(kSuccess, result) << FAIL_LOG_FMT("Config account settigns failed");
   alice.vccsAccountManager->applySettings( alice.vccsAccountHandle ); // prob. not needed


   result = alice.vccsAccountManager->enable( alice.vccsAccountHandle );
   ASSERT_EQ(kSuccess, result) << FAIL_LOG_FMT("Failed to enable account");

   // Wait for the account to reach the registered state
   {
      auto aliceRegistered = std::async(std::launch::async, [&] () {
         Account::VccsAccountStateChangedEvent evt;
         Account::VccsAccountHandle h;
         ASSERT_TRUE( cpcWaitForEvent(
                                      alice.vccsEvents,
                                      "VccsAccountHandler::onAccountStateChanged",
                                      15000,
                                      AlwaysTruePred( ),
                                      h, evt ) );
         ASSERT_TRUE( evt.oldState == Account::VccsAccountState_Unregistered ) << FAIL_LOG_FMT("account state is wrong, expect: Unregistered");
         ASSERT_TRUE( evt.newState == Account::VccsAccountState_Registering ) << FAIL_LOG_FMT("account state failed to change from Unregistered to Registering");
         //safeCout("old state: " << evt.oldState << " new state: " << evt.newState);
         safeCout(STATUS_LOG_FMT("Account is registering"));
         
         ASSERT_TRUE( cpcWaitForEvent(
                                      alice.vccsEvents,
                                      "VccsAccountHandler::onAccountStateChanged",
                                      15000,
                                      AlwaysTruePred( ),
                                      h, evt ) );
         //safeCout("old state: " << evt.oldState << " new state: " << evt.newState);
         ASSERT_TRUE( evt.oldState == Account::VccsAccountState_Registering )<< FAIL_LOG_FMT("account state is wrong, expect: Registering");
         ASSERT_TRUE( evt.newState == Account::VccsAccountState_Registered ) << FAIL_LOG_FMT("account state failed to change from Registering to Registered");
      });
      waitFor( aliceRegistered );
   }
   
   if (::testing::Test::HasFatalFailure())
   {
      FAIL() << FAIL_LOG_FMT("Account registration failed.");
   }
   safeCout(STATUS_LOG_FMT("VCCS account seems registered okay"));
}

void CollabNetworkChangeTest::subscribeConference(TestAccount& alice, CollabNetworkChangeTestConfig& config,
                         Conference::ConferenceDetails& outDetails,
                         Conference::VccsConferenceHandle& outConference,
                         cpc::vector<CPCAPI2::SipAccount::SipAccountSettings>& outAccounts,
                         cpc::vector<CPCAPI2::SipConversationSettings>& outConvs)
{
   int result;
   
   cpc::string outWebSocketURL;
   cpc::string outServerName;
   int outPortNumber;
   cpc::string outGroupName;
   cpc::string outSubscriptionCode;
   result = alice.vccsAccountManager->crackVCCSURL(config.vccsURL, false, outWebSocketURL, outServerName, outPortNumber, outGroupName, outSubscriptionCode);
   ASSERT_TRUE(result) << FAIL_LOG_FMT("crack VCCS URL failure");
   
   // Subscribe to the conference from which we want events
   VCCS::Conference::SubscriptionInfo info;
   info.conferenceCode = outSubscriptionCode;
   info.applicationID = config.appId;
   result = alice.vccsConferenceManager->subscribe( alice.vccsAccountHandle, info);
   ASSERT_EQ(kSuccess, result) << FAIL_LOG_FMT("Failed to subscribe to the conference");
   
   auto aliceSubscribe = std::async(std::launch::async, [&] () {
      Conference::SubscribeEvent evt;
      Account::VccsAccountHandle h;
      ASSERT_TRUE( cpcWaitForEvent(
                                   alice.vccsEvents,
                                   "VccsConferenceHandler::onSubscribe",
                                   15000,
                                   AlwaysTruePred( ),
                                   h, evt ) ) << FAIL_LOG_FMT("failed to subscribe to the conference ");
      
      ASSERT_TRUE(evt.hConference > 0);
      outConference = evt.hConference;
      ASSERT_EQ(alice.account->decodeProvisioningResponse(evt.directSIPProvisioning, outAccounts), kSuccess) << FAIL_LOG_FMT("sip account provisioning file format is invalid");
      ASSERT_NE(outAccounts.size(), 0) << FAIL_LOG_FMT("after subscribing to the conference, returned sip account settings is empty");
      ASSERT_EQ(alice.conversation->decodeProvisioningResponse(evt.directSIPProvisioning, outConvs), kSuccess) << FAIL_LOG_FMT("conversation provisioning file format is invalid");
      ASSERT_NE(outConvs.size(), 0) << FAIL_LOG_FMT("after subscribing to the conference, returned conversation settings is empty");
   });
   waitFor( aliceSubscribe );
   if (::testing::Test::HasFatalFailure())
   {
      FAIL() << FAIL_LOG_FMT("failed to subscribe to the conference ");
   }
   
   auto aliceConferenceUpdate = std::async(std::launch::async, [&] () {
      Conference::ConferenceListUpdatedEvent evt;
      Account::VccsAccountHandle h;
      ASSERT_TRUE( cpcWaitForEvent(
                                   alice.vccsEvents,
                                   "VccsConferenceHandler::onConferenceListUpdated",
                                   10000,
                                   AlwaysTruePred( ),
                                   h, evt ) ) << FAIL_LOG_FMT("missed Conference_Updated after subscription ");
      ASSERT_TRUE( evt.changes[0].conference.bridgeNumber.size() > 0 ) << FAIL_LOG_FMT("Bridge number is wrong");
      ASSERT_GE(evt.changes[0].conference.lobbySipAddress.size(), 0) << FAIL_LOG_FMT("SIP address to dial into the bridge is empty or not present (lobbySipAddress)");
      ASSERT_TRUE( evt.changes[0].conference.id != -1 );
      ASSERT_TRUE(evt.changes.size() != 0) << FAIL_LOG_FMT("no conference info");
      outDetails = evt.changes[0].conference;
   });
   waitFor(aliceConferenceUpdate);
   if (::testing::Test::HasFatalFailure())
   {
      FAIL() << FAIL_LOG_FMT("failure in ConferenceUpdate after subscribing to the conference");
   }
   
   auto aliceParticipantupdate = std::async(std::launch::async, [&] () {
      Conference::ParticipantListUpdatedEvent evt;
      Conference::VccsConferenceHandle h;
      ASSERT_TRUE( cpcWaitForEvent(
                                   alice.vccsEvents,
                                   "VccsConferenceHandler::onParticipantListUpdated",
                                   15000,
                                   AlwaysTruePred( ),
                                   h, evt ) ) << FAIL_LOG_FMT("missed Participant_Updated after subscription OR Participant_Updated was sent before conference_Updated and was ignored, the order should be conference_Updated then Participant_Updated");
      ASSERT_TRUE(evt.changes.size() != 0) << FAIL_LOG_FMT("no participant info");
      
      if (!config.skipParticipantCallStatusCheck)
      {
         ASSERT_EQ(evt.changes[0].participant.callStatus, 4) << FAIL_LOG_FMT("participant callStatus should be NONE");
      }
   });
   waitFor(aliceParticipantupdate);
   if (::testing::Test::HasFatalFailure())
   {
      FAIL() << FAIL_LOG_FMT("failure in participantUpated after subscribing to the conference");
   }
   safeCout(STATUS_LOG_FMT("subscribed to the conference"));
}

void CollabNetworkChangeTest::setupSipAccount(TestAccount& alice, const CPCAPI2::SipAccount::SipAccountSettings& accountSettings)
{
   alice.config.settings = accountSettings;
   
   alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings);
   alice.account->applySettings(alice.handle);
   alice.enable(false);
   ASSERT_NO_FATAL_FAILURE(assertAccountRegisteringEx(alice)) << FAIL_LOG_FMT("sip account account registration failure");
   ASSERT_NO_FATAL_FAILURE(assertAccountRegisteredEx(alice)) << FAIL_LOG_FMT("sip account registration failure");
   safeCout(STATUS_LOG_FMT("sip account seems registered okay"));
}

void CollabNetworkChangeTest::collabCall(TestAccount& alice, cpc::string confUri, Conference::VccsConferenceHandle hConference,
                int callDurationMilliSec, CPCAPI2::SipConversation::SipConversationHandle& aliceCall, const CollabNetworkChangeTestConfig& config)
{
   aliceCall = alice.conversation->createConversation(alice.handle);

   alice.video->queryCodecList();
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);
   alice.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_848x480p);
   alice.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_848x480p);
#if _WIN32
   ASSERT_EQ(0, ViECreateWindow(hwndAliceLocal, 0, 0, 530, 300, "Alice (local)"));
   ASSERT_EQ(0, ViECreateWindow(hwndAliceRemote, 0, 305, 848, 480, "Alice (incoming)"));
   alice.video->setIncomingVideoRenderTarget(hwndAliceRemote, Media::VideoSurfaceType_WindowsHWND);
   alice.video->setLocalVideoRenderTarget(hwndAliceLocal, Media::VideoSurfaceType_WindowsHWND);
#endif
   CPCAPI2::SipConversation::MediaInfo aliceVideoMedia;
   aliceVideoMedia.mediaDirection = config.videoMediaDirection;
   aliceVideoMedia.mediaType = CPCAPI2::SipConversation::MediaType_Video;
   aliceVideoMedia.mediaEncryptionOptions.mediaEncryptionMode = CPCAPI2::SipConversation::MediaEncryptionMode_SRTP_SDES_Encrypted;
   aliceVideoMedia.mediaEncryptionOptions.secureMediaRequired = true;
   
   CPCAPI2::SipConversation::MediaInfo aliceAudioMedia;
   aliceAudioMedia.mediaDirection = CPCAPI2::SipConversation::MediaDirection_SendReceive;
   aliceAudioMedia.mediaType = CPCAPI2::SipConversation::MediaType_Audio;
   aliceAudioMedia.mediaEncryptionOptions.mediaEncryptionMode = CPCAPI2::SipConversation::MediaEncryptionMode_SRTP_SDES_Encrypted;
   aliceAudioMedia.mediaEncryptionOptions.secureMediaRequired = true;
   
   //alice.video->setCaptureDevice(CPCAPI2::Media::kCustomVideoSourceDeviceId);
   int ret = alice.video->startCapture();
   ASSERT_EQ(ret, kSuccess) << FAIL_LOG_FMT("failed to start capture with custom device");
   
   ret = alice.conversation->addParticipant(aliceCall, confUri);
   ASSERT_EQ(kSuccess, ret) << FAIL_LOG_FMT("Failed to add participant");
   safeCout(STATUS_LOG_FMT("call in bridge"));

   ret = alice.conversation->configureMedia(aliceCall, aliceAudioMedia);
   ret = alice.conversation->configureMedia(aliceCall, aliceVideoMedia);
   ASSERT_EQ(ret, kSuccess) << "configure alice media failed";
   
   ret = alice.conversation->start(aliceCall);
   ASSERT_EQ(kSuccess, ret) << FAIL_LOG_FMT("Failed to start a conversation");
   safeCout(STATUS_LOG_FMT("making an audio/video call..."));

   ASSERT_NO_FATAL_FAILURE(assertConversationStateChanged(alice, aliceCall, CPCAPI2::SipConversation::ConversationState_Connected)) << FAIL_LOG_FMT("conversation not connected");
   safeCout(STATUS_LOG_FMT("conversation connected..."));

   //consume network change related account events:
   const unsigned int timeOutMs = 10000;
   auto netChanges = std::async(std::launch::async, [&] () {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
      for (int i = 0; i < (callDurationMilliSec / timeOutMs); i++)
      {
         if (cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged", timeOutMs, AlwaysTruePred(), h, evt))
         {
            ASSERT_EQ(alice.handle, h);
            safeCout("Account state changed to " << evt.accountStatus);
         }
      }
   });
   waitForMs(netChanges, std::chrono::milliseconds(callDurationMilliSec + timeOutMs));
   
   //std::this_thread::sleep_for(std::chrono::milliseconds(callDurationMilliSec));
   
   // check there is participant in the conference, and particpant status is in_conference
   auto aliceParticipantStatus = std::async(std::launch::async, [&] () {
      Conference::ParticipantListUpdatedEvent evt;
      Conference::VccsConferenceHandle h;
      ASSERT_TRUE( cpcWaitForEvent(
                                   alice.vccsEvents,
                                   "VccsConferenceHandler::onParticipantListUpdated",
                                   15000,
                                   AlwaysTruePred( ),
                                   h, evt ) ) << FAIL_LOG_FMT("missed Participant_Updated after call in the bridge");
      ASSERT_TRUE(evt.changes.size() != 0) << FAIL_LOG_FMT("no participant info");
      
      if (!config.skipParticipantCallStatusCheck)
      {
         ASSERT_EQ(evt.changes[0].participant.callStatus, 0) << FAIL_LOG_FMT("participant callStatus should be IN_CONFERENCE");
      }
      safeCout(STATUS_LOG_FMT("participant " << evt.changes[0].participant.sipUsername << " call status is IN_CONFERENCE"));
   });
   waitFor(aliceParticipantStatus);
   if (::testing::Test::HasFatalFailure())
   {
      FAIL() << FAIL_LOG_FMT("failed to join the conference");
   }
   
   CPCAPI2::SipConversation::SipConversationHandle h;
   CPCAPI2::SipConversation::ConversationStatisticsUpdatedEvent evt;
   CPCAPI2::SipConversation::SipConversationManagerInternal* convInternal = dynamic_cast<CPCAPI2::SipConversation::SipConversationManagerInternal*>(alice.conversation);
   
   convInternal->refreshConversationStatistics(aliceCall, true, true, true, true);
   
   ASSERT_TRUE(alice.conversationEvents->expectEvent("SipConversationHandler::onConversationStatisticsUpdated", 15000, HandleEqualsPred<CPCAPI2::SipConversation::SipConversationHandle>(aliceCall), h, evt)) << "missed updated conversation statistics event";
   {
      ASSERT_EQ(1, evt.conversationStatistics.audioChannels.size()) << FAIL_LOG_FMT("Audio channel must be at least 1");
      ASSERT_NE(evt.conversationStatistics.audioChannels[0].streamDataCounters.packetsReceived, 0) << FAIL_LOG_FMT("Audio - Packets received is 0");
      ASSERT_NE(evt.conversationStatistics.audioChannels[0].streamDataCounters.packetsSent, 0) << FAIL_LOG_FMT("Audio - Packets sent is 0");
      ASSERT_EQ(1, evt.conversationStatistics.videoChannels.size()) << FAIL_LOG_FMT("video channel must be at least 1");
      ASSERT_NE(evt.conversationStatistics.videoChannels[0].streamDataCounters.packetsReceived, 0) << FAIL_LOG_FMT("Video - Packets received is 0");
      ASSERT_NE(evt.conversationStatistics.videoChannels[0].streamDataCounters.packetsSent, 0) << FAIL_LOG_FMT("Video - Packets sent is 0");
   }
   
}

void CollabNetworkChangeTest::collabCallEnd(TestAccount& alice, Conference::VccsConferenceHandle hConference,
                   const CPCAPI2::SipConversation::SipConversationHandle& aliceCall,
                   const CollabNetworkChangeTestConfig& config,
                   const Conference::ConferenceDetails& confDetails)
{
   safeCout(STATUS_LOG_FMT("going to end the call-------------"));
   int result = alice.conversation->end(aliceCall);
   ASSERT_EQ(kSuccess, result) << FAIL_LOG_FMT("Failed to end the conversation");
   ASSERT_NO_FATAL_FAILURE(assertConversationEnded(alice, aliceCall, CPCAPI2::SipConversation::ConversationEndReason_UserTerminatedLocally)) << FAIL_LOG_FMT("first account outgoing call end signal failure");
   safeCout(STATUS_LOG_FMT("conversation  ends"));
   
   if (!config.skipParticipantCallStatusCheck)
   {
      Conference::ConferenceListUpdatedEvent evt;
      auto aliceConferenceUpdate = std::async(std::launch::async, [&] () {
         
         Account::VccsAccountHandle h;
         ASSERT_TRUE( cpcWaitForEvent(
                                      alice.vccsEvents,
                                      "VccsConferenceHandler::onConferenceListUpdated",
                                      config.waitTimeForConferenceUpdateMilliSec,
                                      AlwaysTruePred( ),
                                      h, evt ) ) << FAIL_LOG_FMT("missed Conference_Updated after collab call ended ");
         
         ASSERT_TRUE(evt.changes.size() != 0) << FAIL_LOG_FMT("no conference info");
         ASSERT_FALSE(evt.changes[0].conference.isConferenceLive) << FAIL_LOG_FMT("conference is still active");
      });
      waitFor(aliceConferenceUpdate);
      if (::testing::Test::HasFatalFailure())
      {
         FAIL() << FAIL_LOG_FMT("failed to end the conference");
      }
   }
   alice.vccsConferenceManager->unsubscribe( alice.vccsAccountHandle, confDetails.bridgeNumber );
   auto aliceUnsubscribe = std::async(std::launch::async, [&] () {
      Conference::UnsubscribeEvent evt;
      Account::VccsAccountHandle h;
      ASSERT_TRUE( cpcWaitForEvent(
                                   alice.vccsEvents,
                                   "VccsConferenceHandler::onUnsubscribe",
                                   15000,
                                   AlwaysTruePred( ),
                                   h, evt ) );
   });
   waitFor( aliceUnsubscribe );
   if (::testing::Test::HasFatalFailure())
   {
      FAIL() << FAIL_LOG_FMT("failed to unsubscribe the conference");
   }
   safeCout(STATUS_LOG_FMT("unsubscribed to the conference"));
   alice.vccsConferenceManager->queryConferenceDetails(alice.vccsAccountHandle, hConference);
   safeCout(STATUS_LOG_FMT("conference ended"));

#if _WIN32
   DestroyWindow(hwndAliceRemote);
   DestroyWindow(hwndAliceLocal);
#endif // _WIN32
}

#endif
