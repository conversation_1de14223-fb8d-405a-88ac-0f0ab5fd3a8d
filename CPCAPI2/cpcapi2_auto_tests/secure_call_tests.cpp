#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#include "cpcapi2_test_fixture.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>

#include "test_framework/cpcapi2_test_framework.h"
#include "test_framework/bare_bones_sip_endpoint.h"
#include "test_call_events.h"
#include "test_framework/sipp_runner.h"

#include <thread>
#include <future>

using namespace CPCAPI2;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::SipConversation;
using namespace CPCAPI2::test;

namespace {

class SecureCallTests : public CpcapiAutoTest
{
public:
   SecureCallTests() {}
   virtual ~SecureCallTests() {}

   struct VideoInfo
   {
#ifndef __linux__
      CpTestWindowHandle hwndAliceCapture;
      CpTestWindowHandle nswindowAliceCaptureWindow;
      CpTestWindowHandle hwndAliceRemote;
      CpTestWindowHandle nswindowAliceRemoteWindow;
      CpTestWindowHandle hwndBobCapture;
      CpTestWindowHandle nswindowBobCaptureWindow;
      CpTestWindowHandle hwndBobRemote;
      CpTestWindowHandle nswindowBobRemoteWindow;
#endif

      VideoInfo()
#ifndef __linux__
         :
         hwndAliceCapture(NULL),
         nswindowAliceCaptureWindow(NULL),
         hwndAliceRemote(NULL),
         nswindowAliceRemoteWindow(NULL),
         hwndBobCapture(NULL),
         nswindowBobCaptureWindow(NULL),
         hwndBobRemote(NULL),
         nswindowBobRemoteWindow(NULL)
#endif
         {}
   };

   static void configureMedia(MediaInfo& media, MediaType type, MediaDirection direction, MediaEncryptionMode mode, bool secure);
   static void configureMedia(MediaInfo& media, MediaType type, MediaDirection direction, MediaEncryptionMode mode, bool secure, MediaCryptoSuite crypto1);
   static void configureMedia(MediaInfo& media, MediaType type, MediaDirection direction, MediaEncryptionMode mode, bool secure, MediaCryptoSuite crypto1, MediaCryptoSuite crypto2);
   static void configureMedia(MediaInfo& media, MediaType type, MediaDirection direction, MediaEncryptionMode mode, bool secure, MediaCryptoSuite crypto1, MediaCryptoSuite crypto2, MediaCryptoSuite crypto3);
   static void configureMedia(MediaInfo& media, MediaType type, MediaDirection direction, MediaEncryptionMode mode, bool secure, MediaCryptoSuite crypto1, MediaCryptoSuite crypto2, MediaCryptoSuite crypto3, MediaCryptoSuite crypto4);
   static void configureMedia(MediaInfo& media, MediaType type, MediaDirection direction, MediaEncryptionMode mode, bool secure, cpc::vector<MediaCryptoSuite>& cryptos);

   void startVideo(SecureTestAccount& alice, VideoInfo& info);
   void startVideo(SecureTestAccount& alice, SecureTestAccount& bob, VideoInfo& info);
   void stopVideo(SecureTestAccount& alice, VideoInfo& info);
   void stopVideo(SecureTestAccount& alice, SecureTestAccount& bob, VideoInfo& info);
};

//NOTE: this test requires CP QA Cert installed to run
TEST_F(SecureCallTests, EnableAccount)
{
   SecureTestAccount alice("alice", Account_NoInit, false);
   alice.init();

   ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);

   // Perform registration
   CPCAPI2::SipAccount::SipAccountHandle h;
   CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
   ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged",
                              20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);

   // Expect registration success
   ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged",
                              20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_EQ(200, evt.signalingStatusCode);
   ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
   ASSERT_EQ(CPCAPI2::SipAccount::SipAccountTransportType::SipAccountTransport_TLS, evt.transportType);
}

TEST_F(SecureCallTests, BasicSecureCallWithDefaultCryptos)
{
   SecureTestAccount alice("alice");
   SecureTestAccount bob("bob");

   MediaInfo aliceAudio;
   aliceAudio.mediaDirection = MediaDirection_SendReceive;
   aliceAudio.mediaType = MediaType_Audio;
   aliceAudio.mediaEncryptionOptions.mediaEncryptionMode = MediaEncryptionMode_SRTP_SDES_Encrypted;
   aliceAudio.mediaEncryptionOptions.secureMediaRequired = true;

   MediaInfo bobAudio;
   bobAudio.mediaDirection = MediaDirection_SendReceive;
   bobAudio.mediaType = MediaType_Audio;
   bobAudio.mediaEncryptionOptions.mediaEncryptionMode = MediaEncryptionMode_SRTP_SDES_Encrypted;
   bobAudio.mediaEncryptionOptions.secureMediaRequired = true;

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());

   alice.conversation->configureMedia(aliceCall, aliceAudio);
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      assertNewConversationOutgoingAudio_crypto(alice, aliceCall, bob.config.uri(), aliceAudio, [](const NewConversationEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChangedAudio_crypto(alice, aliceCall, MediaCryptoSuite_AEAD_AES_128_GCM, aliceAudio, bobAudio, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertCallHadMedia(alice, aliceCall, true, false);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall;
      // TODO: assert crypto macro fails with default crypto suites, follow-up required on what should be the expected order as we changed from set to vector
      // assertNewConversationIncomingAudio_crypto(bob, &bobCall, alice.config.uri(), aliceAudio, [](const NewConversationEvent& evt){});
       assertNewConversationIncoming_ex(bob, &bobCall, alice.config.uri(), [](const NewConversationEvent& evt)
      {
         ASSERT_EQ(1, evt.remoteMediaInfo.size());
         ASSERT_EQ(MediaEncryptionMode_SRTP_SDES_Encrypted, evt.remoteMediaInfo[0].mediaEncryptionOptions.mediaEncryptionMode);
         ASSERT_TRUE(evt.remoteMediaInfo[0].mediaEncryptionOptions.secureMediaRequired);
         ASSERT_EQ(MediaCryptoSuite_None, evt.remoteMediaInfo[0].mediaCrypto);
         ASSERT_EQ(evt.remoteMediaInfo[0].mediaEncryptionOptions.mediaCryptoSuites.size(), 8);
         ASSERT_EQ(MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32, evt.remoteMediaInfo[0].mediaEncryptionOptions.mediaCryptoSuites.back());
      });
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      bob.conversation->configureMedia(bobCall, bobAudio);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Audio, true);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChangedAudio_crypto(bob, bobCall, MediaCryptoSuite_AEAD_AES_128_GCM, bobAudio, aliceAudio, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(12000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });


   waitFor2(aliceEvents, bobEvents);
}

// Checks that using MediaEncryptionOptions's default constructor results in expected behaviour
TEST_F(SecureCallTests, BasicSecureCallWithDefaultCryptos2)
{
   SecureTestAccount alice("alice");
   SecureTestAccount bob("bob");

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());

   MediaInfo aliceAudio;
   aliceAudio.mediaDirection = MediaDirection_SendReceive;
   aliceAudio.mediaType = MediaType_Audio;

   // verify defaults are sane with MediaEncryptionOptions's constructor
   MediaEncryptionOptions meo;
   meo.mediaEncryptionMode = MediaEncryptionMode_SRTP_SDES_Encrypted;
   meo.secureMediaRequired = true;

   aliceAudio.mediaEncryptionOptions = meo;

   MediaInfo bobAudio;
   bobAudio.mediaDirection = MediaDirection_SendReceive;
   bobAudio.mediaType = MediaType_Audio;
   bobAudio.mediaEncryptionOptions.mediaEncryptionMode = MediaEncryptionMode_SRTP_SDES_Encrypted;
   bobAudio.mediaEncryptionOptions.secureMediaRequired = true;

   alice.conversation->configureMedia(aliceCall, aliceAudio);
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      assertNewConversationOutgoingAudio_crypto(alice, aliceCall, bob.config.uri(), aliceAudio, [](const NewConversationEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChangedAudio_crypto(alice, aliceCall, MediaCryptoSuite_AEAD_AES_128_GCM, aliceAudio, bobAudio, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertCallHadMedia(alice, aliceCall, true, false);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall;
      // TODO: assert crypto macro fails with default crypto suites, follow-up required on what should be the expected order as we changed from set to vector
      // assertNewConversationIncomingAudio_crypto(bob, &bobCall, alice.config.uri(), aliceAudio, [](const NewConversationEvent& evt){});
      assertNewConversationIncoming_ex(bob, &bobCall, alice.config.uri(), [](const NewConversationEvent& evt)
      {
         ASSERT_EQ(1, evt.remoteMediaInfo.size());
         ASSERT_EQ(MediaEncryptionMode_SRTP_SDES_Encrypted, evt.remoteMediaInfo[0].mediaEncryptionOptions.mediaEncryptionMode);
         ASSERT_TRUE(evt.remoteMediaInfo[0].mediaEncryptionOptions.secureMediaRequired);
         ASSERT_EQ(MediaCryptoSuite_None, evt.remoteMediaInfo[0].mediaCrypto);
         ASSERT_EQ(evt.remoteMediaInfo[0].mediaEncryptionOptions.mediaCryptoSuites.size(), 8);
         ASSERT_EQ(MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32, evt.remoteMediaInfo[0].mediaEncryptionOptions.mediaCryptoSuites.back());
      });
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      bob.conversation->configureMedia(bobCall, bobAudio);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Audio, true);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChangedAudio_crypto(bob, bobCall, MediaCryptoSuite_AEAD_AES_128_GCM, bobAudio, aliceAudio, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(12000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });


   waitFor2(aliceEvents, bobEvents);
}

TEST_F(SecureCallTests, BasicSecureCallWeakCipherReinviteStrongCipher)
{
   SecureTestAccount alice("alice");
   SecureTestAccount bob("bob");

   VideoInfo videoInfo;
   startVideo(alice, bob, videoInfo);

   MediaInfo aliceAudio;
   MediaInfo aliceVideo;
   configureMedia(aliceAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);
   configureMedia(aliceVideo, MediaType_Video, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);

   MediaInfo aliceAudioUpdated;
   MediaInfo aliceVideoUpdated;
   configureMedia(aliceAudioUpdated, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80);
   configureMedia(aliceVideoUpdated, MediaType_Video, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80);

   MediaInfo bobAudio;
   MediaInfo bobVideo;
   configureMedia(bobAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);
   configureMedia(bobVideo, MediaType_Video, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);

   MediaInfo bobAudioUpdated;
   MediaInfo bobVideoUpdated;
   configureMedia(bobAudioUpdated, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80);
   configureMedia(bobVideoUpdated, MediaType_Video, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80);

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->configureMedia(aliceCall, aliceAudio);
   alice.conversation->configureMedia(aliceCall, aliceVideo);
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]()
   {
      assertNewConversationOutgoing_crypto(alice, aliceCall, bob.config.uri(), aliceAudio, aliceVideo, [](const NewConversationEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged_crypto(alice, aliceCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, aliceAudio, aliceVideo, bobAudio, bobVideo, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertMediaFlowing(alice, aliceCall, true, false);
      alice.conversation->configureMedia(aliceCall, aliceAudioUpdated);
      alice.conversation->configureMedia(aliceCall, aliceVideoUpdated);
      alice.conversation->sendMediaChangeRequest(aliceCall);
      assertConversationMediaChanged_crypto(alice, aliceCall, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, aliceAudioUpdated, aliceVideoUpdated, bobAudioUpdated, bobVideoUpdated, [](const ConversationMediaChangedEvent& evt) {});
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertCallHadMedia(alice, aliceCall, true, false);
   });

   auto bobEvents = std::async(std::launch::async, [&]()
   {
      SipConversationHandle bobCall;
      assertNewConversationIncoming_crypto(bob, &bobCall, alice.config.uri(), aliceAudio, aliceVideo, [](const NewConversationEvent& evt){});
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Audio, true);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_crypto(bob, bobCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, bobAudio, bobVideo, aliceAudio, aliceVideo, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertConversationMediaChangeRequest_crypto(bob, bobCall, aliceAudioUpdated, aliceVideoUpdated, [](const ConversationMediaChangeRequestEvent& evt) {});
      bob.conversation->configureMedia(bobCall, bobAudioUpdated);
      bob.conversation->configureMedia(bobCall, bobVideoUpdated);
      bob.conversation->accept(bobCall);
      std::this_thread::sleep_for(std::chrono::milliseconds(12000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });


   waitFor2(aliceEvents, bobEvents);

   stopVideo(alice, bob, videoInfo);
}

TEST_F(SecureCallTests, BasicSecureCallWithWeakCipher_AES_CM_128_HMAC_SHA1_80)
{
   SecureTestAccount alice("alice");
   SecureTestAccount bob("bob");

   VideoInfo videoInfo;
   startVideo(alice, bob, videoInfo);

   MediaInfo aliceAudio;
   MediaInfo aliceVideo;
   configureMedia(aliceAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);
   configureMedia(aliceVideo, MediaType_Video, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);

   MediaInfo bobAudio;
   MediaInfo bobVideo;
   configureMedia(bobAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);
   configureMedia(bobVideo, MediaType_Video, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->configureMedia(aliceCall, aliceAudio);
   alice.conversation->configureMedia(aliceCall, aliceVideo);
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      assertNewConversationOutgoing_crypto(alice, aliceCall, bob.config.uri(), aliceAudio, aliceVideo, [](const NewConversationEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged_crypto(alice, aliceCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, aliceAudio, aliceVideo, bobAudio, bobVideo, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertCallHadMedia(alice, aliceCall, true, false);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall;
      assertNewConversationIncoming_crypto(bob, &bobCall, alice.config.uri(), aliceAudio, aliceVideo, [](const NewConversationEvent& evt){});
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Audio, true);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_crypto(bob, bobCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, bobAudio, bobVideo, aliceAudio, aliceVideo, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(12000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });


   waitFor2(aliceEvents, bobEvents);

   stopVideo(alice, bob, videoInfo);
}

TEST_F(SecureCallTests, DISABLED_LongSecureHdVideoCallWithWeakCipherTriggerWithLocalRenegotiation_AES_CM_128_HMAC_SHA1_80)
{
   int kCallDurationSeconds = 60 * 60;

   SecureTestAccount alice("alice");
   SecureTestAccount bob("bob");

   VideoInfo videoInfo;
   startVideo(alice, bob, videoInfo);

   MediaInfo aliceAudio;
   MediaInfo aliceVideo;
   configureMedia(aliceAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true,
      MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);
   configureMedia(aliceVideo, MediaType_Video, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true,
      MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);

   MediaInfo bobAudio;
   MediaInfo bobVideo;
   configureMedia(bobAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);
   configureMedia(bobVideo, MediaType_Video, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->configureMedia(aliceCall, aliceAudio);
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      assertNewConversationOutgoingAudio_crypto(alice, aliceCall, bob.config.uri(), aliceAudio, [](const NewConversationEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChangedAudio_crypto(alice, aliceCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, aliceAudio, bobAudio, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertMediaFlowing(alice, aliceCall, true, false);
      alice.conversation->configureMedia(aliceCall, aliceVideo);
      alice.conversation->sendMediaChangeRequest(aliceCall);
      assertConversationMediaChanged_crypto(alice, aliceCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, aliceAudio, aliceVideo, bobAudio, bobVideo, [](const ConversationMediaChangedEvent& evt) {});
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertMediaFlowing(alice, aliceCall, true, true);
      alice.conversation->configureMedia(aliceCall, aliceVideo);
      alice.conversation->sendMediaChangeRequest(aliceCall);
      assertConversationMediaChanged_crypto(alice, aliceCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, aliceAudio, aliceVideo, bobAudio, bobVideo, [](const ConversationMediaChangedEvent& evt) {});
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertMediaFlowing(alice, aliceCall, true, true);
      std::this_thread::sleep_for(std::chrono::seconds(kCallDurationSeconds));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertCallHadMedia(alice, aliceCall, true, false);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall;
      assertNewConversationIncomingAudio_crypto(bob, &bobCall, alice.config.uri(), aliceAudio, [](const NewConversationEvent& evt){});
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      bob.conversation->configureMedia(bobCall, bobAudio);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Audio, true);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChangedAudio_crypto(bob, bobCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, bobAudio, aliceAudio, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
      bob.conversation->configureMedia(bobCall, bobVideo);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_crypto(bob, bobCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, bobAudio, bobVideo, aliceAudio, aliceVideo, [](const ConversationMediaChangedEvent& evt) {});
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
      bob.conversation->configureMedia(bobCall, bobVideo);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_crypto(bob, bobCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, bobAudio, bobVideo, aliceAudio, aliceVideo, [](const ConversationMediaChangedEvent& evt) {});
      std::this_thread::sleep_for(std::chrono::seconds(kCallDurationSeconds));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });


   waitFor2(aliceEvents, bobEvents);

   stopVideo(alice, bob, videoInfo);
}

TEST_F(SecureCallTests, DISABLED_LongSecureHdVideoCallWithWeakCipherTriggerWithRemoteRenegotiation_AES_CM_128_HMAC_SHA1_80)
{
   int kCallDurationSeconds = 60 * 60;

   SecureTestAccount alice("alice");
   SecureTestAccount bob("bob");

   VideoInfo videoInfo;
   startVideo(alice, bob, videoInfo);

   MediaInfo aliceAudio;
   MediaInfo aliceVideo;
   configureMedia(aliceAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true,
      MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);
   configureMedia(aliceVideo, MediaType_Video, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true,
      MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);

   MediaInfo bobAudio;
   MediaInfo bobVideo;
   configureMedia(bobAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);
   configureMedia(bobVideo, MediaType_Video, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->configureMedia(aliceCall, aliceAudio);
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      assertNewConversationOutgoingAudio_crypto(alice, aliceCall, bob.config.uri(), aliceAudio, [](const NewConversationEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChangedAudio_crypto(alice, aliceCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, aliceAudio, bobAudio, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertMediaFlowing(alice, aliceCall, true, false);
      assertConversationMediaChangeRequest_crypto(alice, aliceCall, bobAudio, bobVideo, [](const ConversationMediaChangeRequestEvent& evt) {});
      alice.conversation->configureMedia(aliceCall, aliceVideo);
      alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
      assertSuccess(alice.conversation->accept(aliceCall));
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertMediaFlowing(alice, aliceCall, true, true);
      assertConversationMediaChanged_crypto(alice, aliceCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, aliceAudio, aliceVideo, bobAudio, bobVideo, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationMediaChangeRequest_crypto(alice, aliceCall, bobAudio, bobVideo, [](const ConversationMediaChangeRequestEvent& evt) {});
      alice.conversation->configureMedia(aliceCall, aliceVideo);
      assertSuccess(alice.conversation->accept(aliceCall));
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertMediaFlowing(alice, aliceCall, true, true);
      assertConversationMediaChanged_crypto(alice, aliceCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, aliceAudio, aliceVideo, bobAudio, bobVideo, [](const ConversationMediaChangedEvent& evt) {});
      std::this_thread::sleep_for(std::chrono::seconds(kCallDurationSeconds));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertCallHadMedia(alice, aliceCall, true, false);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall;
      assertNewConversationIncomingAudio_crypto(bob, &bobCall, alice.config.uri(), aliceAudio, [](const NewConversationEvent& evt){});
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      bob.conversation->configureMedia(bobCall, bobAudio);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Audio, true);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChangedAudio_crypto(bob, bobCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, bobAudio, aliceAudio, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      bob.conversation->configureMedia(bobCall, bobVideo);
      bob.conversation->sendMediaChangeRequest(bobCall);
      assertConversationMediaChanged_crypto(bob, bobCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, bobAudio, bobVideo, aliceAudio, aliceVideo, [](const ConversationMediaChangedEvent& evt) {});
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      bob.conversation->configureMedia(bobCall, bobVideo);
      bob.conversation->sendMediaChangeRequest(bobCall);
      assertConversationMediaChanged_crypto(bob, bobCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, bobAudio, bobVideo, aliceAudio, aliceVideo, [](const ConversationMediaChangedEvent& evt) {});
      std::this_thread::sleep_for(std::chrono::seconds(kCallDurationSeconds));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });


   waitFor2(aliceEvents, bobEvents);

   stopVideo(alice, bob, videoInfo);
}

TEST_F(SecureCallTests, DISABLED_LongSecureHdVideoCallWithWeakCipherTriggerWithMultipleRemoteRenegotiations_AES_CM_128_HMAC_SHA1_80)
{
   SecureTestAccount alice("alice");
   SecureTestAccount bob("bob");

   VideoInfo videoInfo;
   startVideo(alice, bob, videoInfo);

   MediaInfo aliceAudio;
   MediaInfo aliceVideo;
   MediaInfo aliceAudioUpdated;
   MediaInfo aliceVideoUpdated;
   configureMedia(aliceAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true,
                  MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);
   configureMedia(aliceVideo, MediaType_Video, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true,
                  MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);
   configureMedia(aliceAudioUpdated, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80);
   configureMedia(aliceVideoUpdated, MediaType_Video, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80);

   MediaInfo bobAudio;
   MediaInfo bobVideo;
   MediaInfo bobAudioUpdated;
   MediaInfo bobVideoUpdated;
   configureMedia(bobAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);
   configureMedia(bobVideo, MediaType_Video, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);
   configureMedia(bobAudioUpdated, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80);
   configureMedia(bobVideoUpdated, MediaType_Video, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80);

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->configureMedia(aliceCall, aliceAudio);
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      assertNewConversationOutgoingAudio_crypto(alice, aliceCall, bob.config.uri(), aliceAudio, [](const NewConversationEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertMediaFlowing(alice, aliceCall, true, false);
      assertConversationMediaChangeRequest_crypto(alice, aliceCall, bobAudio, bobVideo, [](const ConversationMediaChangeRequestEvent& evt) {});

      alice.conversation->configureMedia(aliceCall, aliceVideo);
      alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
      assertSuccess(alice.conversation->accept(aliceCall));
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertMediaFlowing(alice, aliceCall, true, true);

      for (int i = 0; i < 1000; i++)
      {
         assertConversationMediaChangeRequest_crypto(alice, aliceCall, bobAudioUpdated, bobVideoUpdated, [](const ConversationMediaChangeRequestEvent& evt) {});
         alice.conversation->configureMedia(aliceCall, aliceAudioUpdated);
         alice.conversation->configureMedia(aliceCall, aliceVideoUpdated);
         assertSuccess(alice.conversation->accept(aliceCall));
         std::this_thread::sleep_for(std::chrono::milliseconds(10000));
         assertMediaFlowing(alice, aliceCall, true, true);
      }

      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertCallHadMedia(alice, aliceCall, true, false);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall;
      assertNewConversationIncomingAudio_crypto(bob, &bobCall, alice.config.uri(), aliceAudio, [](const NewConversationEvent& evt){});
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      bob.conversation->configureMedia(bobCall, bobAudio);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Audio, true);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      bob.conversation->configureMedia(bobCall, bobVideo);
      bob.conversation->sendMediaChangeRequest(bobCall);
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));

      for (int i = 0; i < 1000; i++)
      {
         bob.conversation->configureMedia(bobCall, bobAudioUpdated);
         bob.conversation->configureMedia(bobCall, bobVideoUpdated);
         bob.conversation->setMediaEnabled(bobCall, MediaType_Audio, true);
         bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
         bob.conversation->sendMediaChangeRequest(bobCall);
         std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      }

      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });


   waitFor2(aliceEvents, bobEvents);

   stopVideo(alice, bob, videoInfo);
}

TEST_F(SecureCallTests, DISABLED_LongSecureHdVideoCallWithWeakCipherTriggerWithLocalAndRemoteRenegotiation_AES_CM_128_HMAC_SHA1_80)
{
   int kCallDurationSeconds = 60 * 60;

   SecureTestAccount alice("alice");
   SecureTestAccount bob("bob");

   VideoInfo videoInfo;
   startVideo(alice, bob, videoInfo);

   MediaInfo aliceAudio;
   MediaInfo aliceVideo;
   configureMedia(aliceAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true,
      MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);
   configureMedia(aliceVideo, MediaType_Video, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true,
      MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);

   MediaInfo bobAudio;
   MediaInfo bobVideo;
   configureMedia(bobAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);
   configureMedia(bobVideo, MediaType_Video, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->configureMedia(aliceCall, aliceAudio);
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      assertNewConversationOutgoingAudio_crypto(alice, aliceCall, bob.config.uri(), aliceAudio, [](const NewConversationEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChangedAudio_crypto(alice, aliceCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, aliceAudio, bobAudio, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::seconds(5));
      alice.conversation->configureMedia(aliceCall, aliceVideo);
      alice.conversation->sendMediaChangeRequest(aliceCall);
      assertConversationMediaChanged_crypto(alice, aliceCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, aliceAudio, aliceVideo, bobAudio, bobVideo, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendReceive);
      alice.conversation->configureMedia(aliceCall, aliceVideo);
      assertSuccess(alice.conversation->accept(aliceCall));
      assertConversationMediaChanged_crypto(alice, aliceCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, aliceAudio, aliceVideo, bobAudio, bobVideo, [](const ConversationMediaChangedEvent& evt) {});
      std::this_thread::sleep_for(std::chrono::seconds(kCallDurationSeconds));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertCallHadMedia(alice, aliceCall, true, false);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall;
      assertNewConversationIncomingAudio_crypto(bob, &bobCall, alice.config.uri(), aliceAudio, [](const NewConversationEvent& evt){});
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      bob.conversation->configureMedia(bobCall, bobAudio);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Audio, true);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChangedAudio_crypto(bob, bobCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, bobAudio, aliceAudio, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
      bob.conversation->configureMedia(bobCall, bobVideo);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_crypto(bob, bobCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, bobAudio, bobVideo, aliceAudio, aliceVideo, [](const ConversationMediaChangedEvent& evt) {});
      std::this_thread::sleep_for(std::chrono::seconds(5));
      bob.conversation->configureMedia(bobCall, bobVideo);
      bob.conversation->sendMediaChangeRequest(bobCall);
      assertConversationMediaChanged_crypto(bob, bobCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, bobAudio, bobVideo, aliceAudio, aliceVideo, [](const ConversationMediaChangedEvent& evt) {});
      std::this_thread::sleep_for(std::chrono::seconds(kCallDurationSeconds));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });


   waitFor2(aliceEvents, bobEvents);

   stopVideo(alice, bob, videoInfo);
}

TEST_F(SecureCallTests, DISABLED_StrettoCollabCall)
{
   // number of media change requests to receive before ending the call. likely these
   // will be due to session timer refreshes by freeswitch
   int kMediaChangeReqMaxCount = 30;

   SecureTestAccount alice("alice", Account_NoInit);
   alice.config.settings.domain = "meetings6.softphone.com";
   alice.config.settings.outboundProxy = "";
   alice.config.settings.useRegistrar = false;
   alice.config.settings.username = "jason3";
   alice.config.settings.password = "1234aa4d2d";
   alice.config.settings.nameServers.clear();
   alice.init();
   alice.enable();

   const cpc::string confUri("sip:5000");

   VideoInfo videoInfo;
   startVideo(alice, videoInfo);

   MediaInfo aliceAudio;
   MediaInfo aliceVideo;
   configureMedia(aliceAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);
   configureMedia(aliceVideo, MediaType_Video, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);

   MediaInfo bobAudio;
   MediaInfo bobVideo;
   configureMedia(bobAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);
   configureMedia(bobVideo, MediaType_Video, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, confUri);
   alice.conversation->configureMedia(aliceCall, aliceAudio);
   alice.conversation->configureMedia(aliceCall, aliceVideo);
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      assertNewConversationOutgoing_crypto(alice, aliceCall, confUri + "@" + alice.config.settings.domain, aliceAudio, aliceVideo, [](const NewConversationEvent& evt) {});
      assertConversationMediaChanged_crypto(alice, aliceCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, aliceAudio, aliceVideo, bobAudio, bobVideo, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      // 53002

      alice.conversation->setDtmfMode(aliceCall, 0, DtmfMode_RFC2833);
      alice.conversation->startDtmfTone(aliceCall, 5, false);
      alice.conversation->startDtmfTone(aliceCall, 3, false);
      alice.conversation->startDtmfTone(aliceCall, 0, false);
      alice.conversation->startDtmfTone(aliceCall, 0, false);
      alice.conversation->startDtmfTone(aliceCall, 2, false);
      alice.conversation->startDtmfTone(aliceCall, 11 /* # */, false);

      for (int i = 0; i < kMediaChangeReqMaxCount; ++i)
      {
         // hack: loop waiting for media change requests, to handle session timer refresh re-INVITES from freeswitch
         SipConversationHandle h;
         ConversationMediaChangeRequestEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent("SipConversationHandler::onConversationMediaChangeRequest",
            1500000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));

         alice.conversation->accept(aliceCall);
      }

      alice.conversation->end(aliceCall);
   });


   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(1000000)), std::future_status::ready);
   noThrowCheck(aliceEvents);

   stopVideo(alice, videoInfo);
}


TEST_F(SecureCallTests, DISABLED_StrettoCollabCallRemoveVideoReAdd)
{
   SecureTestAccount alice("alice", Account_NoInit);
   alice.config.settings.domain = "meetings6.softphone.com";
   alice.config.settings.outboundProxy = "";
   alice.config.settings.useRegistrar = false;
   alice.config.settings.username = "jason";
   alice.config.settings.password = "1234aa4d2d";
   alice.config.settings.nameServers.clear();
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.init();
   alice.enable();

   const cpc::string confUri("sip:5000");

   VideoInfo videoInfo;
   startVideo(alice, videoInfo);

   MediaInfo aliceAudio;
   MediaInfo aliceVideo;
   configureMedia(aliceAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);
   configureMedia(aliceVideo, MediaType_Video, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);

   MediaInfo bobAudio;
   MediaInfo bobVideo;
   configureMedia(bobAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);
   configureMedia(bobVideo, MediaType_Video, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, confUri);
   alice.conversation->configureMedia(aliceCall, aliceAudio);
   alice.conversation->configureMedia(aliceCall, aliceVideo);
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      assertNewConversationOutgoing_crypto(alice, aliceCall, confUri + "@" + alice.config.settings.domain, aliceAudio, aliceVideo, [](const NewConversationEvent& evt) {});
      assertConversationMediaChanged_crypto(alice, aliceCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, aliceAudio, aliceVideo, bobAudio, bobVideo, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      // 53002

      alice.conversation->setDtmfMode(aliceCall, 0, DtmfMode_RFC2833);
      alice.conversation->startDtmfTone(aliceCall, 5, false);
      alice.conversation->startDtmfTone(aliceCall, 3, false);
      alice.conversation->startDtmfTone(aliceCall, 0, false);
      alice.conversation->startDtmfTone(aliceCall, 0, false);
      alice.conversation->startDtmfTone(aliceCall, 2, false);
      alice.conversation->startDtmfTone(aliceCall, 11 /* # */, false);

      for (int i = 0; i < 1; ++i)
      {
         // hack: loop waiting for media change requests, to handle session timer refresh re-INVITES from freeswitch
         SipConversationHandle h;
         ConversationMediaChangeRequestEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent("SipConversationHandler::onConversationMediaChangeRequest",
            1500000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));

         alice.conversation->accept(aliceCall);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(3000));

      // remove video
      alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, false);
      alice.conversation->sendMediaChangeRequest(aliceCall);
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(1, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });

      std::this_thread::sleep_for(std::chrono::milliseconds(10000));

      // re-add video
      alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
      alice.conversation->sendMediaChangeRequest(aliceCall);
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });

      for (int i = 0; i < 3; ++i)
      {
         // hack: loop waiting for media change requests, to handle session timer refresh re-INVITES from freeswitch
         SipConversationHandle h;
         ConversationMediaChangeRequestEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent("SipConversationHandler::onConversationMediaChangeRequest",
            1500000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));

         alice.conversation->accept(aliceCall);
      }

      alice.conversation->end(aliceCall);
   });


   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(1000000)), std::future_status::ready);
   noThrowCheck(aliceEvents);

   stopVideo(alice, videoInfo);
}

TEST_F(SecureCallTests, BasicSecureCallWithWeakCipher_AES_CM_128_HMAC_SHA1_32)
{
   SecureTestAccount alice("alice");
   SecureTestAccount bob("bob");

   MediaInfo aliceAudio;
   configureMedia(aliceAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);

   MediaInfo bobAudio;
   configureMedia(bobAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->configureMedia(aliceCall, aliceAudio);
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      assertNewConversationOutgoingAudio_crypto(alice, aliceCall, bob.config.uri(), aliceAudio, [](const NewConversationEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChangedAudio_crypto(alice, aliceCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32, aliceAudio, bobAudio, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertCallHadMedia(alice, aliceCall, true, false);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall;
      assertNewConversationIncomingAudio_crypto(bob, &bobCall, alice.config.uri(), aliceAudio, [](const NewConversationEvent& evt){});
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      bob.conversation->configureMedia(bobCall, bobAudio);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Audio, true);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChangedAudio_crypto(bob, bobCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32, bobAudio, aliceAudio, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });


   waitFor2(aliceEvents, bobEvents);
}

TEST_F(SecureCallTests, BasicSecureCallWithStrongerCipher_AES_CM_256_HMAC_SHA1_32)
{
   SecureTestAccount alice("alice");
   SecureTestAccount bob("bob");

   MediaInfo aliceAudio;
   configureMedia(aliceAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32);

   MediaInfo bobAudio;
   configureMedia(bobAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32);

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->configureMedia(aliceCall, aliceAudio);
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      assertNewConversationOutgoingAudio_crypto(alice, aliceCall, bob.config.uri(), aliceAudio, [](const NewConversationEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChangedAudio_crypto(alice, aliceCall, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32, aliceAudio, bobAudio, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertCallHadMedia(alice, aliceCall, true, false);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall;
      assertNewConversationIncomingAudio_crypto(bob, &bobCall, alice.config.uri(), aliceAudio, [](const NewConversationEvent& evt){});
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      bob.conversation->configureMedia(bobCall, bobAudio);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Audio, true);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChangedAudio_crypto(bob, bobCall, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32, bobAudio, aliceAudio, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });


   waitFor2(aliceEvents, bobEvents);
}

TEST_F(SecureCallTests, BasicSecureCallWithStrongerCipher_AES_CM_256_HMAC_SHA1_80)
{
   SecureTestAccount alice("alice");
   SecureTestAccount bob("bob");

   MediaInfo aliceAudio;
   configureMedia(aliceAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80);

   MediaInfo bobAudio;
   configureMedia(bobAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80);

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->configureMedia(aliceCall, aliceAudio);
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      assertNewConversationOutgoingAudio_crypto(alice, aliceCall, bob.config.uri(), aliceAudio, [](const NewConversationEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChangedAudio_crypto(alice, aliceCall, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, aliceAudio, bobAudio, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertCallHadMedia(alice, aliceCall, true, false);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall;
      assertNewConversationIncomingAudio_crypto(bob, &bobCall, alice.config.uri(), aliceAudio, [](const NewConversationEvent& evt){});
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      bob.conversation->configureMedia(bobCall, bobAudio);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Audio, true);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChangedAudio_crypto(bob, bobCall, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, bobAudio, aliceAudio, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });


   waitFor2(aliceEvents, bobEvents);
}

TEST_F(SecureCallTests, BasicSecureCall_AES_CM_192_HMAC_SHA1_32)
{
   SecureTestAccount alice("alice");
   SecureTestAccount bob("bob");

   MediaInfo aliceAudio;
   configureMedia(aliceAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_192_HMAC_SHA1_32);

   MediaInfo bobAudio;
   configureMedia(bobAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_192_HMAC_SHA1_32);

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->configureMedia(aliceCall, aliceAudio);
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      assertNewConversationOutgoingAudio_crypto(alice, aliceCall, bob.config.uri(), aliceAudio, [](const NewConversationEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChangedAudio_crypto(alice, aliceCall, MediaCryptoSuite_AES_CM_192_HMAC_SHA1_32, aliceAudio, bobAudio, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertCallHadMedia(alice, aliceCall, true, false);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall;
      assertNewConversationIncomingAudio_crypto(bob, &bobCall, alice.config.uri(), aliceAudio, [](const NewConversationEvent& evt){});
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      bob.conversation->configureMedia(bobCall, bobAudio);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Audio, true);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChangedAudio_crypto(bob, bobCall, MediaCryptoSuite_AES_CM_192_HMAC_SHA1_32, bobAudio, aliceAudio, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });


   waitFor2(aliceEvents, bobEvents);
}

TEST_F(SecureCallTests, BasicSecureCall_AES_CM_192_HMAC_SHA1_80)
{
   SecureTestAccount alice("alice");
   SecureTestAccount bob("bob");

   MediaInfo aliceAudio;
   configureMedia(aliceAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_192_HMAC_SHA1_80);

   MediaInfo bobAudio;
   configureMedia(bobAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_192_HMAC_SHA1_80);

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->configureMedia(aliceCall, aliceAudio);
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      assertNewConversationOutgoingAudio_crypto(alice, aliceCall, bob.config.uri(), aliceAudio, [](const NewConversationEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChangedAudio_crypto(alice, aliceCall, MediaCryptoSuite_AES_CM_192_HMAC_SHA1_80, aliceAudio, bobAudio, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertCallHadMedia(alice, aliceCall, true, false);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall;
      assertNewConversationIncomingAudio_crypto(bob, &bobCall, alice.config.uri(), aliceAudio, [](const NewConversationEvent& evt){});
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      bob.conversation->configureMedia(bobCall, bobAudio);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Audio, true);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChangedAudio_crypto(bob, bobCall, MediaCryptoSuite_AES_CM_192_HMAC_SHA1_80, bobAudio, aliceAudio, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });


   waitFor2(aliceEvents, bobEvents);
}

TEST_F(SecureCallTests, BasicSecureCall_AEAD_AES_128_GCM)
{
   SecureTestAccount alice("alice");
   SecureTestAccount bob("bob");

   MediaInfo aliceAudio;
   configureMedia(aliceAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AEAD_AES_128_GCM);

   MediaInfo bobAudio;
   configureMedia(bobAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AEAD_AES_128_GCM);

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->configureMedia(aliceCall, aliceAudio);
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      assertNewConversationOutgoingAudio_crypto(alice, aliceCall, bob.config.uri(), aliceAudio, [](const NewConversationEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChangedAudio_crypto(alice, aliceCall, MediaCryptoSuite_AEAD_AES_128_GCM, aliceAudio, bobAudio, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertCallHadMedia(alice, aliceCall, true, false);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall;
      assertNewConversationIncomingAudio_crypto(bob, &bobCall, alice.config.uri(), aliceAudio, [](const NewConversationEvent& evt){});
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      bob.conversation->configureMedia(bobCall, bobAudio);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Audio, true);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChangedAudio_crypto(bob, bobCall, MediaCryptoSuite_AEAD_AES_128_GCM, bobAudio, aliceAudio, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });


   waitFor2(aliceEvents, bobEvents);
}

TEST_F(SecureCallTests, BasicSecureCall_AEAD_AES_256_GCM)
{
   SecureTestAccount alice("alice");
   SecureTestAccount bob("bob");

   MediaInfo aliceAudio;
   configureMedia(aliceAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AEAD_AES_256_GCM);

   MediaInfo bobAudio;
   configureMedia(bobAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AEAD_AES_256_GCM);

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->configureMedia(aliceCall, aliceAudio);
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      assertNewConversationOutgoingAudio_crypto(alice, aliceCall, bob.config.uri(), aliceAudio, [](const NewConversationEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChangedAudio_crypto(alice, aliceCall, MediaCryptoSuite_AEAD_AES_256_GCM, aliceAudio, bobAudio, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertCallHadMedia(alice, aliceCall, true, false);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall;
      assertNewConversationIncomingAudio_crypto(bob, &bobCall, alice.config.uri(), aliceAudio, [](const NewConversationEvent& evt){});
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      bob.conversation->configureMedia(bobCall, bobAudio);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Audio, true);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChangedAudio_crypto(bob, bobCall, MediaCryptoSuite_AEAD_AES_256_GCM, bobAudio, aliceAudio, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });


   waitFor2(aliceEvents, bobEvents);
}

TEST_F(SecureCallTests, BasicSecureCallWithWeakerCryptoMatch)
{
   SecureTestAccount alice("alice");
   SecureTestAccount bob("bob");

   MediaInfo aliceAudio;
   configureMedia(aliceAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);

   MediaInfo bobAudio;
   configureMedia(bobAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->configureMedia(aliceCall, aliceAudio);
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      assertNewConversationOutgoingAudio_crypto(alice, aliceCall, bob.config.uri(), aliceAudio, [](const NewConversationEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChangedAudio_crypto(alice, aliceCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32, aliceAudio, bobAudio, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertCallHadMedia(alice, aliceCall, true, false);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall;
      assertNewConversationIncomingAudio_crypto(bob, &bobCall, alice.config.uri(), aliceAudio, [](const NewConversationEvent& evt){});
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      bob.conversation->configureMedia(bobCall, bobAudio);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Audio, true);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChangedAudio_crypto(bob, bobCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32, bobAudio, aliceAudio, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });


   waitFor2(aliceEvents, bobEvents);
}

TEST_F(SecureCallTests, BasicSecureCallWithMismatchedCryptos)
{
   SecureTestAccount alice("alice");
   SecureTestAccount bob("bob");

   MediaInfo aliceAudio;
   configureMedia(aliceAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32);

   MediaInfo bobAudio;
   configureMedia(bobAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->configureMedia(aliceCall, aliceAudio);
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      assertNewConversationOutgoingAudio_crypto(alice, aliceCall, bob.config.uri(), aliceAudio, [](const NewConversationEvent& evt) {});

      {
         SipConversationHandle h;
         ConversationEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents, "SipConversationHandler::onConversationEnded", 45000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
         ASSERT_EQ(evt.endReason, ConversationEndReason_ServerRejected);
         ASSERT_TRUE(evt.sipResponseCode == 488);
         ASSERT_EQ(evt.signallingEndEvent, "Not Acceptable Here");
      }
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall;
      assertNewConversationIncomingAudio_crypto(bob, &bobCall, alice.config.uri(), aliceAudio, [](const NewConversationEvent& evt){});
      bob.conversation->configureMedia(bobCall, bobAudio);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Audio, true);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });


   waitFor2(aliceEvents, bobEvents);
}

TEST_F(SecureCallTests, CallWithOptionalEncryption)
{
   SecureTestAccount alice("alice");
   SecureTestAccount bob("bob");

   MediaInfo aliceAudio;
   configureMedia(aliceAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, false, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);

   MediaInfo bobAudio;
   configureMedia(bobAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, false, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->configureMedia(aliceCall, aliceAudio);
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      assertNewConversationOutgoingAudio_crypto(alice, aliceCall, bob.config.uri(), aliceAudio, [](const NewConversationEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChangedAudio_crypto(alice, aliceCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, aliceAudio, bobAudio, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertCallHadMedia(alice, aliceCall, true, false);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall;
      assertNewConversationIncomingAudio_crypto(bob, &bobCall, alice.config.uri(), aliceAudio, [](const NewConversationEvent& evt){});
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      bob.conversation->configureMedia(bobCall, bobAudio);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChangedAudio_crypto(bob, bobCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, bobAudio, aliceAudio, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor2(aliceEvents, bobEvents);
}

TEST_F(SecureCallTests, SecureCallReinviteStaySecure)
{
   SecureTestAccount alice("alice");
   SecureTestAccount bob("bob");

   MediaInfo aliceAudio;
   configureMedia(aliceAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);
   MediaInfo aliceAudioUpdated;
   configureMedia(aliceAudioUpdated, MediaType_Audio, MediaDirection_ReceiveOnly, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);

   MediaInfo bobAudio;
   configureMedia(bobAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true);
   MediaInfo bobAudioUpdated;
   configureMedia(bobAudioUpdated, MediaType_Audio, MediaDirection_SendOnly, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->configureMedia(aliceCall, aliceAudio);
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      assertNewConversationOutgoingAudio_crypto(alice, aliceCall, bob.config.uri(), aliceAudio, [](const NewConversationEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChangedAudio_crypto(alice, aliceCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, aliceAudio, bobAudio, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertConversationMediaChangeRequestAudio_crypto(alice, aliceCall, bobAudioUpdated, [](const ConversationMediaChangeRequestEvent& evt) {});
      assertSuccess(alice.conversation->accept(aliceCall));
      assertConversationMediaChangedAudio_crypto(alice, aliceCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, aliceAudioUpdated, bobAudioUpdated, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertCallHadMedia(alice, aliceCall, true, false);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall;
      assertNewConversationIncomingAudio_crypto(bob, &bobCall, alice.config.uri(), aliceAudio, [](const NewConversationEvent& evt){});
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      bob.conversation->hold(bobCall);
      assertConversationMediaChangedAudio_crypto(bob, bobCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, bobAudioUpdated, aliceAudioUpdated, [](const ConversationMediaChangedEvent& evt) {});
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor2(aliceEvents, bobEvents);
}

TEST_F(SecureCallTests, CallWithOptionalEncryptionCalleeSaysNoEncryption)
{
   SecureTestAccount alice("alice");
   SecureTestAccount bob("bob");

   MediaInfo aliceAudio;
   configureMedia(aliceAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, false, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);

   MediaInfo bobAudio;
   configureMedia(bobAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_Unencrypted, false);

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->configureMedia(aliceCall, aliceAudio);
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      assertNewConversationOutgoingAudio_crypto(alice, aliceCall, bob.config.uri(), aliceAudio, [](const NewConversationEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertCallHadMedia(alice, aliceCall, true, false);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall;
      assertNewConversationIncomingAudio_crypto(bob, &bobCall, alice.config.uri(), aliceAudio, [](const NewConversationEvent& evt){});
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      bob.conversation->configureMedia(bobCall, bobAudio);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor2(aliceEvents, bobEvents);
}

TEST_F(SecureCallTests, CallWithOptionalEncryptionCalleeSaysNoEncryption_Hold) {

   TestAccount alice("alice");
   TestAccount bob("bob");

   MediaInfo aliceAudio;
   configureMedia(aliceAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, false, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);

   MediaInfo bobAudio;
   configureMedia(bobAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_Unencrypted, false);


   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->configureMedia(aliceCall, aliceAudio);

   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(500));

      assertSuccess(alice.conversation->hold(aliceCall));
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_TRUE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendOnly, evt.localMediaInfo[0].mediaDirection);
      });
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertSuccess(alice.conversation->unhold(aliceCall));
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });

      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertCallHadMedia(alice, aliceCall, true, false);
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());

      bob.conversation->configureMedia(bobCall, bobAudio);

      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });


      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendOnly);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_FALSE(evt.localHold);
         ASSERT_TRUE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_ReceiveOnly, evt.localMediaInfo[0].mediaDirection);
      });
      assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });

      std::this_thread::sleep_for(std::chrono::milliseconds(500));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor2(aliceEvents, bobEvents);
}


TEST_F(SecureCallTests, BasicSecureCallWeakCipherReinviteStrongCipher2)
{
   SecureTestAccount alice("alice");
   SecureTestAccount bob("bob");

   MediaInfo aliceAudio;
   configureMedia(aliceAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);
   MediaInfo aliceAudioUpdated;
   configureMedia(aliceAudioUpdated, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80);

   MediaInfo bobAudio;
   configureMedia(bobAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80);

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->configureMedia(aliceCall, aliceAudio);
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]()
   {
      assertNewConversationOutgoingAudio_crypto(alice, aliceCall, bob.config.uri(), aliceAudio, [](const NewConversationEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChangedAudio_crypto(alice, aliceCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, aliceAudio, bobAudio, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      alice.conversation->configureMedia(aliceCall, aliceAudioUpdated);
      alice.conversation->sendMediaChangeRequest(aliceCall);
      assertConversationMediaChangedAudio_crypto(alice, aliceCall, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, aliceAudioUpdated, bobAudio, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertCallHadMedia(alice, aliceCall, true, false);
   });

   auto bobEvents = std::async(std::launch::async, [&]()
   {
      SipConversationHandle bobCall;
      assertNewConversationIncomingAudio_crypto(bob, &bobCall, alice.config.uri(), aliceAudio, [](const NewConversationEvent& evt){});
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);

      bob.conversation->configureMedia(bobCall, bobAudio);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Audio, true);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChangedAudio_crypto(bob, bobCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, bobAudio, aliceAudio, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertConversationMediaChangeRequestAudio_crypto(bob, bobCall, aliceAudioUpdated, [](const ConversationMediaChangeRequestEvent& evt) {});
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChangedAudio_crypto(bob, bobCall, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, bobAudio, aliceAudioUpdated, [](const ConversationMediaChangedEvent& evt) {});
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });


   waitFor2(aliceEvents, bobEvents);
}

TEST_F(SecureCallTests, BasicSecureVideoCallWeakCipherReinviteStrongCipher)
{
   SecureTestAccount alice("alice");
   SecureTestAccount bob("bob");

   VideoInfo videoInfo;
   startVideo(alice, bob, videoInfo);

   // let the cameras get going
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   MediaInfo aliceAudio;
   MediaInfo aliceVideo;
   configureMedia(aliceAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);
   configureMedia(aliceVideo, MediaType_Video, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);
   MediaInfo aliceAudioUpdated;
   MediaInfo aliceVideoUpdated;
   configureMedia(aliceAudioUpdated, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80);
   configureMedia(aliceVideoUpdated, MediaType_Video, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80);

   MediaInfo bobAudio;
   MediaInfo bobVideo;
   configureMedia(bobAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80);
   configureMedia(bobVideo, MediaType_Video, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80);

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->configureMedia(aliceCall, aliceAudio);
   alice.conversation->configureMedia(aliceCall, aliceVideo);
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]()
   {
      assertNewConversationOutgoing_crypto(alice, aliceCall, bob.config.uri(), aliceAudio, aliceVideo, [](const NewConversationEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged_crypto(alice, aliceCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, aliceAudio, aliceVideo, bobAudio, bobVideo, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertMediaFlowing(alice, aliceCall, true, true);
      alice.conversation->configureMedia(aliceCall, aliceAudioUpdated);
      alice.conversation->configureMedia(aliceCall, aliceVideoUpdated);
      alice.conversation->sendMediaChangeRequest(aliceCall);
      assertConversationMediaChanged_crypto(alice, aliceCall, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, aliceAudioUpdated, aliceVideoUpdated, bobAudio, bobVideo, [](const ConversationMediaChangedEvent& evt) {});
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertCallHadMedia(alice, aliceCall, true, true);
   });

   auto bobEvents = std::async(std::launch::async, [&]()
   {
      SipConversationHandle bobCall;
      assertNewConversationIncoming_crypto(bob, &bobCall, alice.config.uri(), aliceAudio, aliceVideo, [](const NewConversationEvent& evt){});
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      bob.conversation->configureMedia(bobCall, bobAudio);
      bob.conversation->configureMedia(bobCall, bobVideo);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Audio, true);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_crypto(bob, bobCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, bobAudio, bobVideo, aliceAudio, aliceVideo, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
      bob.conversation->accept(bobCall);
      assertConversationMediaChanged_crypto(bob, bobCall, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, bobAudio, bobVideo, aliceAudioUpdated, aliceVideoUpdated, [](const ConversationMediaChangedEvent& evt) {});
      std::this_thread::sleep_for(std::chrono::milliseconds(12000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });


   waitFor2(aliceEvents, bobEvents);

   stopVideo(alice, bob, videoInfo);
}

TEST_F(SecureCallTests, BasicSecureVideoCallWithDifferentAudioVideoCiphers)
{
   SecureTestAccount alice("alice");
   SecureTestAccount bob("bob");

   VideoInfo videoInfo;
   startVideo(alice, bob, videoInfo);

   // let the cameras get going
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   MediaInfo aliceAudio;
   MediaInfo aliceVideo;
   configureMedia(aliceAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);
   configureMedia(aliceVideo, MediaType_Video, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32);

   MediaInfo bobAudio;
   MediaInfo bobVideo;
   configureMedia(bobAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);
   configureMedia(bobVideo, MediaType_Video, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32);

   // make an outgoing video call from Alice to Bob
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->configureMedia(aliceCall, aliceAudio);
   alice.conversation->configureMedia(aliceCall, aliceVideo);
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]()
   {
      assertNewConversationOutgoing_crypto(alice, aliceCall, bob.config.uri(), aliceAudio, aliceVideo, [](const NewConversationEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged_crypto(alice, aliceCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, aliceAudio, aliceVideo, bobAudio, bobVideo, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertCallHadMedia(alice, aliceCall, true, true);
   });

   auto bobEvents = std::async(std::launch::async, [&]()
   {
      SipConversationHandle bobCall;
      assertNewConversationIncoming_crypto(bob, &bobCall, alice.config.uri(), aliceAudio, aliceVideo, [](const NewConversationEvent& evt){});
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      bob.conversation->configureMedia(bobCall, bobAudio);
      bob.conversation->configureMedia(bobCall, bobVideo);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Audio, true);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_crypto(bob, bobCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, bobAudio, bobVideo, aliceAudio, aliceVideo, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(12000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });


   waitFor2(aliceEvents, bobEvents);

   stopVideo(alice, bob, videoInfo);
}

TEST_F(SecureCallTests, BasicSecureVideoCallWithDifferentAudioVideoCipherListSize)
{
   SecureTestAccount alice("alice");
   SecureTestAccount bob("bob");

   VideoInfo videoInfo;
   startVideo(alice, bob, videoInfo);

   // let the cameras get going
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   MediaInfo aliceAudio;
   MediaInfo aliceVideo;
   configureMedia(aliceAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);
   configureMedia(aliceVideo, MediaType_Video, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);

   MediaInfo bobAudio;
   MediaInfo bobVideo;
   configureMedia(bobAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);
   configureMedia(bobVideo, MediaType_Video, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);

   // make an outgoing video call from Alice to Bob
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->configureMedia(aliceCall, aliceAudio);
   alice.conversation->configureMedia(aliceCall, aliceVideo);
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]()
   {
      assertNewConversationOutgoing_crypto(alice, aliceCall, bob.config.uri(), aliceAudio, aliceVideo, [](const NewConversationEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged_crypto(alice, aliceCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, aliceAudio, aliceVideo, bobAudio, bobVideo, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      // Note: Intermittent failure for media flow validation
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertCallHadMedia(alice, aliceCall, true, true);
   });

   auto bobEvents = std::async(std::launch::async, [&]()
   {
      SipConversationHandle bobCall;
      assertNewConversationIncoming_crypto(bob, &bobCall, alice.config.uri(), aliceAudio, aliceVideo, [](const NewConversationEvent& evt){});
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      bob.conversation->configureMedia(bobCall, bobAudio);
      bob.conversation->configureMedia(bobCall, bobVideo);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Audio, true);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_crypto(bob, bobCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, bobAudio, bobVideo, aliceAudio, aliceVideo, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(12000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });


   waitFor2(aliceEvents, bobEvents);

   stopVideo(alice, bob, videoInfo);
}

TEST_F(SecureCallTests, BasicSecureVideoCallWithDifferentAudioVideoCiphersAndVideoCipherMismatch)
{
   SecureTestAccount alice("alice");
   SecureTestAccount bob("bob");

   MediaInfo aliceAudio;
   MediaInfo aliceVideo;
   configureMedia(aliceAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);
   configureMedia(aliceVideo, MediaType_Video, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32);

   MediaInfo bobAudio;
   MediaInfo bobVideo;
   configureMedia(bobAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);
   configureMedia(bobVideo, MediaType_Video, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);

   // make an outgoing video call from Alice to Bob
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->configureMedia(aliceCall, aliceAudio);
   alice.conversation->configureMedia(aliceCall, aliceVideo);
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]()
   {
      assertNewConversationOutgoing_crypto(alice, aliceCall, bob.config.uri(), aliceAudio, aliceVideo, [](const NewConversationEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged_crypto(alice, aliceCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_None, aliceAudio, aliceVideo, bobAudio, bobVideo, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
   });

   auto bobEvents = std::async(std::launch::async, [&]()
   {
      SipConversationHandle bobCall;
      assertNewConversationIncoming_crypto(bob, &bobCall, alice.config.uri(), aliceAudio, aliceVideo, [](const NewConversationEvent& evt){});
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      bob.conversation->configureMedia(bobCall, bobAudio);
      bob.conversation->configureMedia(bobCall, bobVideo);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Audio, true);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_crypto(bob, bobCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_None, bobAudio, bobVideo, aliceAudio, aliceVideo, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });


   waitFor2(aliceEvents, bobEvents);
}

TEST_F(SecureCallTests, BasicSecureVideoCallWithDifferentAudioVideoCiphersAndBothCiphersMismatch)
{
   SecureTestAccount alice("alice");
   SecureTestAccount bob("bob");

   MediaInfo aliceAudio;
   MediaInfo aliceVideo;
   configureMedia(aliceAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);
   configureMedia(aliceVideo, MediaType_Video, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32);

   MediaInfo bobAudio;
   MediaInfo bobVideo;
   configureMedia(bobAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32);
   configureMedia(bobVideo, MediaType_Video, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);

   // make an outgoing video call from Alice to Bob
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->configureMedia(aliceCall, aliceAudio);
   alice.conversation->configureMedia(aliceCall, aliceVideo);
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]()
   {
      assertNewConversationOutgoing_crypto(alice, aliceCall, bob.config.uri(), aliceAudio, aliceVideo, [](const NewConversationEvent& evt) {});
      {
         SipConversationHandle h;
         ConversationEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents, "SipConversationHandler::onConversationEnded", 5000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
         ASSERT_EQ(evt.endReason, ConversationEndReason_ServerRejected);
         ASSERT_TRUE(evt.sipResponseCode == 488);
         ASSERT_EQ(evt.signallingEndEvent, "Not Acceptable Here");
      }
   });

   auto bobEvents = std::async(std::launch::async, [&]()
   {
      SipConversationHandle bobCall;
      assertNewConversationIncoming_crypto(bob, &bobCall, alice.config.uri(), aliceAudio, aliceVideo, [](const NewConversationEvent& evt){});
      bob.conversation->configureMedia(bobCall, bobAudio);
      bob.conversation->configureMedia(bobCall, bobVideo);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Audio, true);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });


   waitFor2(aliceEvents, bobEvents);
}

TEST_F(SecureCallTests, BasicSecureVideoCallWithDifferentAudioVideoCiphersAndRenegotiation)
{
   SecureTestAccount alice("alice");
   SecureTestAccount bob("bob");

   VideoInfo videoInfo;
   startVideo(alice, bob, videoInfo);

   // let the cameras get going
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   MediaInfo aliceAudio;
   MediaInfo aliceVideo;
   configureMedia(aliceAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);
   configureMedia(aliceVideo, MediaType_Video, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32);
   MediaInfo aliceAudioUpdated;
   MediaInfo aliceVideoUpdated;
   configureMedia(aliceAudioUpdated, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32);
   configureMedia(aliceVideoUpdated, MediaType_Video, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);

   MediaInfo bobAudio;
   MediaInfo bobVideo;
   configureMedia(bobAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true,
      MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);
   configureMedia(bobVideo, MediaType_Video, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true,
      MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);

   // make an outgoing video call from Alice to Bob
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->configureMedia(aliceCall, aliceAudio);
   alice.conversation->configureMedia(aliceCall, aliceVideo);
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]()
   {
      assertNewConversationOutgoing_crypto(alice, aliceCall, bob.config.uri(), aliceAudio, aliceVideo, [](const NewConversationEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged_crypto(alice, aliceCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, aliceAudio, aliceVideo, bobAudio, bobVideo, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertMediaFlowing(alice, aliceCall, true, true);
      alice.conversation->configureMedia(aliceCall, aliceAudioUpdated);
      alice.conversation->configureMedia(aliceCall, aliceVideoUpdated);
      alice.conversation->sendMediaChangeRequest(aliceCall);
      assertConversationMediaChanged_crypto(alice, aliceCall, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32, aliceAudioUpdated, aliceVideoUpdated, bobAudio, bobVideo, [](const ConversationMediaChangedEvent& evt) {});
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertCallHadMedia(alice, aliceCall, true, true);
   });

   auto bobEvents = std::async(std::launch::async, [&]()
   {
      SipConversationHandle bobCall;
      assertNewConversationIncoming_crypto(bob, &bobCall, alice.config.uri(), aliceAudio, aliceVideo, [](const NewConversationEvent& evt){});
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      bob.conversation->configureMedia(bobCall, bobAudio);
      bob.conversation->configureMedia(bobCall, bobVideo);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Audio, true);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_crypto(bob, bobCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, bobAudio, bobVideo, aliceAudio, aliceVideo, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
      bob.conversation->accept(bobCall);
      assertConversationMediaChanged_crypto(bob, bobCall, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32, bobAudio, bobVideo, aliceAudioUpdated, aliceVideoUpdated, [](const ConversationMediaChangedEvent& evt) {});
      std::this_thread::sleep_for(std::chrono::milliseconds(12000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });


   waitFor2(aliceEvents, bobEvents);

   stopVideo(alice, bob, videoInfo);
}

TEST_F(SecureCallTests, BasicSecureVideoCallWithDifferentAudioVideoCiphersSetCryptos)
{
   SecureTestAccount alice("alice");
   SecureTestAccount bob("bob");

   VideoInfo videoInfo;
   startVideo(alice, bob, videoInfo);

   // let the cameras get going
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   MediaInfo aliceAudio;
   MediaInfo aliceVideo;
   configureMedia(aliceAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true);
   configureMedia(aliceVideo, MediaType_Video, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true);
   MediaInfo aliceAudioUpdated = aliceAudio;
   MediaInfo aliceVideoUpdated = aliceVideo;
   aliceAudioUpdated.mediaEncryptionOptions.mediaCryptoSuites.clear(); // Ensure no other cryptos are included
   aliceAudioUpdated.mediaEncryptionOptions.mediaCryptoSuites.push_back(MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);
   aliceAudioUpdated.mediaEncryptionOptions.mediaCryptoSuites.push_back(MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);
   aliceVideoUpdated.mediaEncryptionOptions.mediaCryptoSuites.clear(); // Ensure no other cryptos are included
   aliceVideoUpdated.mediaEncryptionOptions.mediaCryptoSuites.push_back(MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80);
   aliceVideoUpdated.mediaEncryptionOptions.mediaCryptoSuites.push_back(MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32);

   MediaInfo bobAudio;
   MediaInfo bobVideo;
   configureMedia(bobAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true);
   configureMedia(bobVideo, MediaType_Video, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true);
   MediaInfo bobAudioUpdated = bobAudio;
   MediaInfo bobVideoUpdated = bobVideo;
   bobAudioUpdated.mediaEncryptionOptions.mediaCryptoSuites.clear(); // Ensure no other cryptos are included
   bobAudioUpdated.mediaEncryptionOptions.mediaCryptoSuites.push_back(MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);
   bobAudioUpdated.mediaEncryptionOptions.mediaCryptoSuites.push_back(MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);
   bobVideoUpdated.mediaEncryptionOptions.mediaCryptoSuites.clear(); // Ensure no other cryptos are included
   bobVideoUpdated.mediaEncryptionOptions.mediaCryptoSuites.push_back(MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80);
   bobVideoUpdated.mediaEncryptionOptions.mediaCryptoSuites.push_back(MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32);

   // make an outgoing video call from Alice to Bob
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->configureMedia(aliceCall, aliceAudio);
   alice.conversation->configureMedia(aliceCall, aliceVideo);
   alice.conversation->setCryptoSuitesForMedia(aliceCall, aliceAudioUpdated.mediaType, aliceAudioUpdated.mediaEncryptionOptions.mediaCryptoSuites);
   alice.conversation->setCryptoSuitesForMedia(aliceCall, aliceVideoUpdated.mediaType, aliceVideoUpdated.mediaEncryptionOptions.mediaCryptoSuites);
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]()
   {
      assertNewConversationOutgoing_crypto(alice, aliceCall, bob.config.uri(), aliceAudioUpdated, aliceVideoUpdated, [](const NewConversationEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged_crypto(alice, aliceCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, aliceAudioUpdated, aliceVideoUpdated, bobAudioUpdated, bobVideoUpdated, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertCallHadMedia(alice, aliceCall, true, true);
   });

   auto bobEvents = std::async(std::launch::async, [&]()
   {
      SipConversationHandle bobCall;

      assertNewConversationIncoming_crypto(bob, &bobCall, alice.config.uri(), aliceAudioUpdated, aliceVideoUpdated, [](const NewConversationEvent& evt){});
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      bob.conversation->configureMedia(bobCall, bobAudio);
      bob.conversation->configureMedia(bobCall, bobVideo);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Audio, true);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      bob.conversation->setCryptoSuitesForMedia(bobCall, bobAudioUpdated.mediaType, bobAudioUpdated.mediaEncryptionOptions.mediaCryptoSuites);
      bob.conversation->setCryptoSuitesForMedia(bobCall, bobVideoUpdated.mediaType, bobVideoUpdated.mediaEncryptionOptions.mediaCryptoSuites);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_crypto(bob, bobCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, bobAudioUpdated, bobVideoUpdated, aliceAudioUpdated, aliceVideoUpdated, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(12000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });


   waitFor2(aliceEvents, bobEvents);

   stopVideo(alice, bob, videoInfo);
}

TEST_F(SecureCallTests, BasicSecureVideoCallWithDifferentAudioVideoCiphersAndRenegotiationSetCryptos)
{
   SecureTestAccount alice("alice");
   SecureTestAccount bob("bob");

   VideoInfo videoInfo;
   startVideo(alice, bob, videoInfo);

   // let the cameras get going
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   MediaInfo aliceAudio;
   MediaInfo aliceVideo;
   configureMedia(aliceAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true);
   configureMedia(aliceVideo, MediaType_Video, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true);
   MediaInfo aliceAudioUpdated = aliceAudio;
   MediaInfo aliceVideoUpdated = aliceVideo;
   aliceAudioUpdated.mediaEncryptionOptions.mediaCryptoSuites.clear(); // Ensure no other cryptos are included
   aliceAudioUpdated.mediaEncryptionOptions.mediaCryptoSuites.push_back(MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);
   aliceAudioUpdated.mediaEncryptionOptions.mediaCryptoSuites.push_back(MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);
   aliceVideoUpdated.mediaEncryptionOptions.mediaCryptoSuites.clear(); // Ensure no other cryptos are included
   aliceVideoUpdated.mediaEncryptionOptions.mediaCryptoSuites.push_back(MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80);
   aliceVideoUpdated.mediaEncryptionOptions.mediaCryptoSuites.push_back(MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32);
   MediaInfo aliceAudioUpdated2 = aliceAudio;
   MediaInfo aliceVideoUpdated2 = aliceVideo;
   aliceAudioUpdated2.mediaEncryptionOptions.mediaCryptoSuites.clear(); // Ensure no other cryptos are included
   aliceAudioUpdated2.mediaEncryptionOptions.mediaCryptoSuites.push_back(MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32);
   aliceVideoUpdated2.mediaEncryptionOptions.mediaCryptoSuites.clear(); // Ensure no other cryptos are included
   aliceVideoUpdated2.mediaEncryptionOptions.mediaCryptoSuites.push_back(MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);

   MediaInfo bobAudio;
   MediaInfo bobVideo;
   configureMedia(bobAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true);
   configureMedia(bobVideo, MediaType_Video, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true);
   MediaInfo bobAudioUpdated = bobAudio;
   MediaInfo bobVideoUpdated = bobVideo;
   bobAudioUpdated.mediaEncryptionOptions.mediaCryptoSuites.clear(); // Ensure no other cryptos are included
   bobAudioUpdated.mediaEncryptionOptions.mediaCryptoSuites.push_back(MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80);
   bobAudioUpdated.mediaEncryptionOptions.mediaCryptoSuites.push_back(MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32);
   bobAudioUpdated.mediaEncryptionOptions.mediaCryptoSuites.push_back(MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);
   bobAudioUpdated.mediaEncryptionOptions.mediaCryptoSuites.push_back(MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);
   bobVideoUpdated.mediaEncryptionOptions.mediaCryptoSuites.clear(); // Ensure no other cryptos are included
   bobVideoUpdated.mediaEncryptionOptions.mediaCryptoSuites.push_back(MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80);
   bobVideoUpdated.mediaEncryptionOptions.mediaCryptoSuites.push_back(MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32);
   bobVideoUpdated.mediaEncryptionOptions.mediaCryptoSuites.push_back(MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);
   bobVideoUpdated.mediaEncryptionOptions.mediaCryptoSuites.push_back(MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);

   // make an outgoing video call from Alice to Bob
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->configureMedia(aliceCall, aliceAudio);
   alice.conversation->configureMedia(aliceCall, aliceVideo);
   alice.conversation->setCryptoSuitesForMedia(aliceCall, aliceAudioUpdated.mediaType, aliceAudioUpdated.mediaEncryptionOptions.mediaCryptoSuites);
   alice.conversation->setCryptoSuitesForMedia(aliceCall, aliceVideoUpdated.mediaType, aliceVideoUpdated.mediaEncryptionOptions.mediaCryptoSuites);
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]()
   {
      assertNewConversationOutgoing_crypto(alice, aliceCall, bob.config.uri(), aliceAudioUpdated, aliceVideoUpdated, [](const NewConversationEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged_crypto(alice, aliceCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, aliceAudioUpdated, aliceVideoUpdated, bobAudioUpdated, bobVideoUpdated, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertMediaFlowing(alice, aliceCall, true, true);
      alice.conversation->setCryptoSuitesForMedia(aliceCall, aliceAudioUpdated2.mediaType, aliceAudioUpdated2.mediaEncryptionOptions.mediaCryptoSuites);
      alice.conversation->setCryptoSuitesForMedia(aliceCall, aliceVideoUpdated2.mediaType, aliceVideoUpdated2.mediaEncryptionOptions.mediaCryptoSuites);
      alice.conversation->sendMediaChangeRequest(aliceCall);
      assertConversationMediaChanged_crypto(alice, aliceCall, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32, aliceAudioUpdated2, aliceVideoUpdated2, bobAudioUpdated, bobVideoUpdated, [](const ConversationMediaChangedEvent& evt) {});
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertCallHadMedia(alice, aliceCall, true, true);
   });

   auto bobEvents = std::async(std::launch::async, [&]()
   {
      SipConversationHandle bobCall;
      assertNewConversationIncoming_crypto(bob, &bobCall, alice.config.uri(), aliceAudioUpdated, aliceVideoUpdated, [](const NewConversationEvent& evt){});
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      bob.conversation->configureMedia(bobCall, bobAudio);
      bob.conversation->configureMedia(bobCall, bobVideo);
      bob.conversation->setCryptoSuitesForMedia(bobCall, bobAudioUpdated.mediaType, bobAudioUpdated.mediaEncryptionOptions.mediaCryptoSuites);
      bob.conversation->setCryptoSuitesForMedia(bobCall, bobVideoUpdated.mediaType, bobVideoUpdated.mediaEncryptionOptions.mediaCryptoSuites);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Audio, true);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_crypto(bob, bobCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, bobAudioUpdated, bobVideoUpdated, aliceAudioUpdated, aliceVideoUpdated, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
      bob.conversation->accept(bobCall);
      assertConversationMediaChanged_crypto(bob, bobCall, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32, bobAudioUpdated, bobVideoUpdated, aliceAudioUpdated2, aliceVideoUpdated2, [](const ConversationMediaChangedEvent& evt) {});
      std::this_thread::sleep_for(std::chrono::milliseconds(12000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });


   waitFor2(aliceEvents, bobEvents);

   stopVideo(alice, bob, videoInfo);
}

TEST_F(SecureCallTests, BasicSecureVideoCallWithDifferentAudioVideoCiphersEncryptionDisabledOnVideo)
{
   SecureTestAccount alice("alice");
   SecureTestAccount bob("bob");

   VideoInfo videoInfo;
   startVideo(alice, bob, videoInfo);

   // let the cameras get going
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   MediaInfo aliceAudio;
   MediaInfo aliceVideo;
   configureMedia(aliceAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);
   configureMedia(aliceVideo, MediaType_Video, MediaDirection_SendReceive, MediaEncryptionMode_Unencrypted, false, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32);

   MediaInfo bobAudio;
   MediaInfo bobVideo;
   configureMedia(bobAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);
   configureMedia(bobVideo, MediaType_Video, MediaDirection_SendReceive, MediaEncryptionMode_Unencrypted, false, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32);

   // make an outgoing video call from Alice to Bob
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->configureMedia(aliceCall, aliceAudio);
   alice.conversation->configureMedia(aliceCall, aliceVideo);
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]()
   {
      assertNewConversationOutgoing_crypto(alice, aliceCall, bob.config.uri(), aliceAudio, aliceVideo, [](const NewConversationEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged_crypto(alice, aliceCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_None, aliceAudio, aliceVideo, bobAudio, bobVideo, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertCallHadMedia(alice, aliceCall, true, true);
   });

   auto bobEvents = std::async(std::launch::async, [&]()
   {
      SipConversationHandle bobCall;
      assertNewConversationIncoming_crypto(bob, &bobCall, alice.config.uri(), aliceAudio, aliceVideo, [](const NewConversationEvent& evt){});
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      bob.conversation->configureMedia(bobCall, bobAudio);
      bob.conversation->configureMedia(bobCall, bobVideo);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Audio, true);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_crypto(bob, bobCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_None, bobAudio, bobVideo, aliceAudio, aliceVideo, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(12000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });


   waitFor2(aliceEvents, bobEvents);

   stopVideo(alice, bob, videoInfo);
}

TEST_F(SecureCallTests, BasicSecureVideoCallWithDifferentAudioVideoCiphersEncryptionDisabledOnVideoOnCallerButRequiredOnCalleeMismatchCipherOnAudio)
{
   SecureTestAccount alice("alice");
   SecureTestAccount bob("bob");

   MediaInfo aliceAudio;
   MediaInfo aliceVideo;
   configureMedia(aliceAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);
   configureMedia(aliceVideo, MediaType_Video, MediaDirection_SendReceive, MediaEncryptionMode_Unencrypted, false, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32);

   MediaInfo bobAudio;
   MediaInfo bobVideo;
   configureMedia(bobAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32);
   configureMedia(bobVideo, MediaType_Video, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32);

   // make an outgoing video call from Alice to Bob
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->configureMedia(aliceCall, aliceAudio);
   alice.conversation->configureMedia(aliceCall, aliceVideo);
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]()
   {
      assertNewConversationOutgoing_crypto(alice, aliceCall, bob.config.uri(), aliceAudio, aliceVideo, [](const NewConversationEvent& evt) {});
      {
         SipConversationHandle h;
         ConversationEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents, "SipConversationHandler::onConversationEnded", 5000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
         ASSERT_EQ(evt.endReason, ConversationEndReason_ServerRejected);
         ASSERT_TRUE(evt.sipResponseCode == 488);
         ASSERT_EQ(evt.signallingEndEvent, "Not Acceptable Here");
      }
   });

   auto bobEvents = std::async(std::launch::async, [&]()
   {
      SipConversationHandle bobCall;
      assertNewConversationIncoming_crypto(bob, &bobCall, alice.config.uri(), aliceAudio, aliceVideo, [](const NewConversationEvent& evt){});
      bob.conversation->configureMedia(bobCall, bobAudio);
      bob.conversation->configureMedia(bobCall, bobVideo);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Audio, true);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });


   waitFor2(aliceEvents, bobEvents);
}

TEST_F(SecureCallTests, BasicSecureVideoCallWithDifferentAudioVideoCiphersEncryptionDisabledOnVideoEnabledInRenegotiation)
{
   SecureTestAccount alice("alice");
   SecureTestAccount bob("bob");

   VideoInfo videoInfo;
   startVideo(alice, bob, videoInfo);

   // let the cameras get going
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   MediaInfo aliceAudio;
   MediaInfo aliceVideo;
   configureMedia(aliceAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);
   configureMedia(aliceVideo, MediaType_Video, MediaDirection_SendReceive, MediaEncryptionMode_Unencrypted, false, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32);

   MediaInfo bobAudio;
   MediaInfo bobVideo;
   configureMedia(bobAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);
   configureMedia(bobVideo, MediaType_Video, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, false, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32);

   // make an outgoing video call from Alice to Bob
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->configureMedia(aliceCall, aliceAudio);
   alice.conversation->configureMedia(aliceCall, aliceVideo);
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]()
   {
      assertNewConversationOutgoing_crypto(alice, aliceCall, bob.config.uri(), aliceAudio, aliceVideo, [](const NewConversationEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged_crypto(alice, aliceCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_None, aliceAudio, aliceVideo, bobAudio, bobVideo, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertCallHadMedia(alice, aliceCall, true, true);
   });

   auto bobEvents = std::async(std::launch::async, [&]()
   {
      SipConversationHandle bobCall;
      assertNewConversationIncoming_crypto(bob, &bobCall, alice.config.uri(), aliceAudio, aliceVideo, [](const NewConversationEvent& evt){});
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      bob.conversation->configureMedia(bobCall, bobAudio);
      bob.conversation->configureMedia(bobCall, bobVideo);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Audio, true);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_crypto(bob, bobCall, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, MediaCryptoSuite_None, bobAudio, bobVideo, aliceAudio, aliceVideo, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(12000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });


   waitFor2(aliceEvents, bobEvents);

   stopVideo(alice, bob, videoInfo);
}

TEST_F(SecureCallTests, RemoveAddVideo)
{
   SecureTestAccount alice("alice");
   SecureTestAccount bob("bob");

   VideoInfo videoInfo;
   startVideo(alice, bob, videoInfo);

   // let the cameras get going
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   MediaInfo aliceAudio;
   MediaInfo aliceVideo;
   configureMedia(aliceAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);
   configureMedia(aliceVideo, MediaType_Video, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);

   MediaInfo bobAudio;
   MediaInfo bobVideo;
   configureMedia(bobAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);
   configureMedia(bobVideo, MediaType_Video, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->configureMedia(aliceCall, aliceAudio);
   alice.conversation->configureMedia(aliceCall, aliceVideo);
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(1, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      // add video
      alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
      alice.conversation->sendMediaChangeRequest(aliceCall);
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });

      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertMediaFlowing(alice, aliceCall, true, true);

      // remove video
      alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, false);
      alice.conversation->sendMediaChangeRequest(aliceCall);
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(1, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });

      std::this_thread::sleep_for(std::chrono::milliseconds(10000));

      // re-add video
      alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
      alice.conversation->sendMediaChangeRequest(aliceCall);
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertCallHadMedia(alice, aliceCall, true, true);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall;
      assertNewConversationIncoming_ex(bob, &bobCall, alice.config.uri(), [](const NewConversationEvent& evt)
      {
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
         ASSERT_EQ(MediaEncryptionMode_SRTP_SDES_Encrypted, evt.remoteMediaInfo[0].mediaEncryptionOptions.mediaEncryptionMode);
         ASSERT_TRUE(evt.remoteMediaInfo[0].mediaEncryptionOptions.secureMediaRequired);
         ASSERT_EQ(evt.remoteMediaInfo[0].mediaEncryptionOptions.mediaCryptoSuites.size(), 1);
         ASSERT_EQ(MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, *evt.remoteMediaInfo[0].mediaEncryptionOptions.mediaCryptoSuites.begin());
         ASSERT_EQ(MediaCryptoSuite_None, evt.remoteMediaInfo[0].mediaCrypto);

         ASSERT_EQ(MediaEncryptionMode_SRTP_SDES_Encrypted, evt.remoteMediaInfo[1].mediaEncryptionOptions.mediaEncryptionMode);
         ASSERT_TRUE(evt.remoteMediaInfo[1].mediaEncryptionOptions.secureMediaRequired);
         ASSERT_EQ(evt.remoteMediaInfo[1].mediaEncryptionOptions.mediaCryptoSuites.size(), 1);
         ASSERT_EQ(MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80, *evt.remoteMediaInfo[1].mediaEncryptionOptions.mediaCryptoSuites.begin());
         ASSERT_EQ(MediaCryptoSuite_None, evt.remoteMediaInfo[1].mediaCrypto);
      });
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(1, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertConversationMediaChangeRequest_ex(bob, bobCall, [](const ConversationMediaChangeRequestEvent& evt){
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.remoteMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.remoteMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.remoteMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.remoteMediaInfo[1].mediaDirection);
      });
      // accept the re-INVITE which adds video
      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      bob.conversation->accept(bobCall);
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });

      // accept the removal of video
      assertConversationMediaChangeRequest_ex(bob, bobCall, [](const ConversationMediaChangeRequestEvent& evt){
         ASSERT_EQ(1, evt.remoteMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.remoteMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.remoteMediaInfo[0].mediaDirection);
      });
      bob.conversation->accept(bobCall);
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(1, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });

      // accept the re-adding of video
      assertConversationMediaChangeRequest_ex(bob, bobCall, [](const ConversationMediaChangeRequestEvent& evt){
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.remoteMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.remoteMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.remoteMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.remoteMediaInfo[1].mediaDirection);
      });
      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      bob.conversation->accept(bobCall);
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });

      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });


   waitFor2(aliceEvents, bobEvents);

   stopVideo(alice, bob, videoInfo);
}

// sipp requires raw sockets in order to send RTP, and this requires sudo.
// as such, this test case assumes the user running the test binary has
// passwordless sudo access (via visudo NOPASSWD)

// this test is intended as a demo/proof of concept for using sipp with the autotests.
// currently only supported on mac; but with some work could work on other platforms.

#ifdef __APPLE__
TEST_F(SecureCallTests, BasicSecureCall_Sipp)
{
   SecureTestAccount alice("alice", Account_NoInit);
   
   bool useTls = true;
   
   if (useTls)
   {
      alice.config.settings.sipTransportType = SipAccountTransport_TLS;
   }
   else
   {
      alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   }
   alice.config.settings.ignoreCertVerification = true;
   alice.config.settings.useRegistrar = false;
   alice.config.settings.outboundProxy = "";
   alice.config.settings.domain = "127.0.0.1";
   alice.config.settings.sourceAddress = "127.0.0.1";
   alice.init();
   alice.enable();

   MediaInfo aliceAudio;
   MediaInfo aliceVideo;
   configureMedia(aliceAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80);
   
   SippRunnerSettings sippRunnerSettings;
   sippRunnerSettings.sipListenPort = 50010;
   sippRunnerSettings.mediaPort = 50020;
   sippRunnerSettings.timeoutSec = 120;
   sippRunnerSettings.scenarioFileName = "SecureCallTests.BasicSecureCall_Sipp.xml";
   
   if (useTls)
   {
      sippRunnerSettings.transport = SippRunnerSettings::Transport_Tls;
   }
   else
   {
      sippRunnerSettings.transport = SippRunnerSettings::Transport_Udp;
   }
   
   SippRunner sippRunner(sippRunnerSettings);
   ASSERT_EQ(kSuccess, sippRunner.start());

   std::stringstream sippUriNoPort;
   sippUriNoPort << "sip:sip@127.0.0.1";
   std::stringstream sippUriWithPort;
   sippUriWithPort << sippUriNoPort.str() << ":" << sippRunnerSettings.sipListenPort;

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, sippUriWithPort.str().c_str());
   alice.conversation->configureMedia(aliceCall, aliceAudio);
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      assertNewConversationOutgoingAudio_crypto(alice, aliceCall, sippUriNoPort.str().c_str(), aliceAudio, [](const NewConversationEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      
      std::this_thread::sleep_for(std::chrono::seconds(15));
      alice.conversation->end(aliceCall);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
      assertCallHadMedia(alice, aliceCall, true, false);
      
      // give time for sipp to response to BYE. is there a better way?
      std::this_thread::sleep_for(std::chrono::seconds(5));
      
      sippRunner.stop();
   });


   waitFor(aliceEvents);
}
#endif // __APPLE__

#ifdef __APPLE__
TEST_F(SecureCallTests, SAVPF_UAC)
{
   SecureTestAccount bob("alice", Account_NoInit);
   bob.config.settings.sipTransportType = SipAccountTransport_UDP;
   bob.config.settings.useRegistrar = false;
   bob.config.settings.outboundProxy = "";
   bob.config.settings.domain = "127.0.0.1";
   bob.config.settings.minSipPort = 55060;
   bob.config.settings.maxSipPort = 55060;
   bob.init();
   bob.enable();

   MediaInfo aliceAudio;
   MediaInfo bobAudio;
   configureMedia(aliceAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80);
   configureMedia(bobAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80);
   
   SippRunnerSettings sippRunnerSettings;
   sippRunnerSettings.sipListenPort = 50010;
   sippRunnerSettings.mediaPort = 50020;
   sippRunnerSettings.timeoutSec = 120;
   sippRunnerSettings.sipTargetPort = bob.config.settings.minSipPort;
   sippRunnerSettings.scenarioFileName = "SeucreCallTests.SAVPF_UAC.xml";
   
   SippRunner sippRunner(sippRunnerSettings);
   ASSERT_EQ(kSuccess, sippRunner.start());

   std::stringstream sippUriNoPort;
   sippUriNoPort << "sip:sipp@127.0.0.1";
   std::stringstream sippUriWithPort;
   sippUriWithPort << sippUriNoPort.str() << ":" << sippRunnerSettings.sipListenPort;

   SipConversationHandle bobCall;
   assertNewConversationIncoming_ex(bob, &bobCall, sippUriWithPort.str().c_str(), [](const NewConversationEvent& evt)
   {
      ASSERT_EQ(1, evt.remoteMediaInfo.size());
      ASSERT_EQ(MediaEncryptionMode_SRTP_SDES_Encrypted, evt.remoteMediaInfo[0].mediaEncryptionOptions.mediaEncryptionMode);
      ASSERT_TRUE(evt.remoteMediaInfo[0].mediaEncryptionOptions.secureMediaRequired);
      ASSERT_EQ(MediaCryptoSuite_None, evt.remoteMediaInfo[0].mediaCrypto);
      ASSERT_EQ(evt.remoteMediaInfo[0].mediaEncryptionOptions.mediaCryptoSuites.size(), 1);
      ASSERT_EQ(MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, evt.remoteMediaInfo[0].mediaEncryptionOptions.mediaCryptoSuites.back());
   });
   assertSuccess(bob.conversation->sendRingingResponse(bobCall));
   assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
   bob.conversation->configureMedia(bobCall, bobAudio);
   bob.conversation->setMediaEnabled(bobCall, MediaType_Audio, true);
   assertSuccess(bob.conversation->accept(bobCall));
   assertConversationMediaChangedAudio_crypto(bob, bobCall, MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80, bobAudio, aliceAudio, [](const ConversationMediaChangedEvent& evt) {});
   assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
   
   std::this_thread::sleep_for(std::chrono::seconds(5));
   assertMediaFlowing(bob, bobCall, true, false);
   bob.conversation->end(bobCall);
   assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   // give time for sipp to responsd to the BYE. is there a better way?
   std::this_thread::sleep_for(std::chrono::seconds(5));
}
#endif // __APPLE__

void SecureCallTests::configureMedia(MediaInfo& media, MediaType type, MediaDirection direction, MediaEncryptionMode mode, bool secure)
{
   media.mediaType = type;
   media.mediaDirection = direction;
   media.mediaEncryptionOptions.mediaEncryptionMode = mode;
   media.mediaEncryptionOptions.secureMediaRequired = secure;
}

void SecureCallTests::configureMedia(MediaInfo& media, MediaType type, MediaDirection direction, MediaEncryptionMode mode, bool secure, MediaCryptoSuite crypto1)
{
   cpc::vector<MediaCryptoSuite> cryptos;
   cryptos.push_back(crypto1);
   configureMedia(media, type, direction, mode, secure, cryptos);
}

void SecureCallTests::configureMedia(MediaInfo& media, MediaType type, MediaDirection direction, MediaEncryptionMode mode, bool secure, MediaCryptoSuite crypto1, MediaCryptoSuite crypto2)
{
   cpc::vector<MediaCryptoSuite> cryptos;
   cryptos.push_back(crypto1);
   cryptos.push_back(crypto2);
   configureMedia(media, type, direction, mode, secure, cryptos);
}

void SecureCallTests::configureMedia(MediaInfo& media, MediaType type, MediaDirection direction, MediaEncryptionMode mode, bool secure, MediaCryptoSuite crypto1, MediaCryptoSuite crypto2, MediaCryptoSuite crypto3)
{
   cpc::vector<MediaCryptoSuite> cryptos;
   cryptos.push_back(crypto1);
   cryptos.push_back(crypto2);
   cryptos.push_back(crypto3);
   configureMedia(media, type, direction, mode, secure, cryptos);
}

void SecureCallTests::configureMedia(MediaInfo& media, MediaType type, MediaDirection direction, MediaEncryptionMode mode, bool secure, MediaCryptoSuite crypto1, MediaCryptoSuite crypto2, MediaCryptoSuite crypto3, MediaCryptoSuite crypto4)
{
   cpc::vector<MediaCryptoSuite> cryptos;
   cryptos.push_back(crypto1);
   cryptos.push_back(crypto2);
   cryptos.push_back(crypto3);
   cryptos.push_back(crypto4);
   configureMedia(media, type, direction, mode, secure, cryptos);
}

void SecureCallTests::configureMedia(MediaInfo& media, MediaType type, MediaDirection direction, MediaEncryptionMode mode, bool secure, cpc::vector<MediaCryptoSuite>& cryptos)
{
   media.mediaType = type;
   media.mediaDirection = direction;
   media.mediaEncryptionOptions.mediaEncryptionMode = mode;
   media.mediaEncryptionOptions.secureMediaRequired = secure;
   media.mediaEncryptionOptions.mediaCryptoSuites = cryptos;
}

void SecureCallTests::startVideo(SecureTestAccount& alice, SecureTestAccount& bob, SecureCallTests::VideoInfo& info)
{
#ifndef __linux__
   ASSERT_EQ(0, ViECreateWindow(info.hwndAliceCapture, 0, 0, 352, 288, "Alice (capture)", info.nswindowAliceCaptureWindow));
   alice.video->setLocalVideoRenderTarget(info.hwndAliceCapture);

   ASSERT_EQ(0, ViECreateWindow(info.hwndAliceRemote, 356, 0, 352, 288, "Alice (incoming)", info.nswindowAliceRemoteWindow));
   alice.video->setIncomingVideoRenderTarget(info.hwndAliceRemote);

   ASSERT_EQ(0, ViECreateWindow(info.hwndBobCapture, 0, 292, 352, 288, "Bob (capture)", info.nswindowBobCaptureWindow));

   bob.video->setLocalVideoRenderTarget(info.hwndBobCapture);

   ASSERT_EQ(0, ViECreateWindow(info.hwndBobRemote, 356, 292, 352, 288, "Bob (incoming)", info.nswindowBobRemoteWindow));
   bob.video->setIncomingVideoRenderTarget(info.hwndBobRemote);

   alice.video->setLocalVideoPreviewResolution(TestEnvironmentConfig::defaultVideoRes());
   bob.video->setLocalVideoPreviewResolution(TestEnvironmentConfig::defaultVideoRes());

   alice.video->setPreferredResolution(cpc::hash(cpc::string("H264")), TestEnvironmentConfig::defaultVideoRes());
   bob.video->setPreferredResolution(cpc::hash(cpc::string("H264")), TestEnvironmentConfig::defaultVideoRes());

   alice.video->startCapture();
   bob.video->startCapture();

   alice.enableOnlyThisVideoCodec("H.264");
#else
   ASSERT_EQ(alice.video->startCapture(), kSuccess);
   ASSERT_EQ(bob.video->startCapture(), kSuccess);
#endif
}

void SecureCallTests::startVideo(SecureTestAccount& alice, SecureCallTests::VideoInfo& info)
{
#ifndef __linux__
   ASSERT_EQ(0, ViECreateWindow(info.hwndAliceCapture, 0, 0, 352, 288, "Alice (capture)", info.nswindowAliceCaptureWindow));
   alice.video->setLocalVideoRenderTarget(info.hwndAliceCapture);
   ASSERT_EQ(0, ViECreateWindow(info.hwndAliceRemote, 356, 0, 352, 288, "Alice (incoming)", info.nswindowAliceRemoteWindow));
   alice.video->setIncomingVideoRenderTarget(info.hwndAliceRemote);
   alice.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_HD_1920x1080p);
   alice.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_HD_1920x1080p);
   alice.video->startCapture();
   alice.enableOnlyThisVideoCodec("H.264");
#else
   ASSERT_EQ(alice.video->startCapture(), kSuccess);
#endif
}

void SecureCallTests::stopVideo(SecureTestAccount& alice, SecureTestAccount& bob, SecureCallTests::VideoInfo& info)
{
#ifndef __linux__
   alice.video->stopCapture();
   alice.video->setLocalVideoRenderTarget(NULL);
   ViEDestroyWindow(info.nswindowAliceCaptureWindow, info.hwndAliceCapture);
   ViEDestroyWindow(info.nswindowAliceRemoteWindow, info.hwndAliceRemote);
   bob.video->stopCapture();
   bob.video->setLocalVideoRenderTarget(NULL);
   ViEDestroyWindow(info.nswindowBobCaptureWindow, info.hwndBobCapture);
   ViEDestroyWindow(info.nswindowBobRemoteWindow, info.hwndBobRemote);
#else
   alice.video->stopCapture();
   bob.video->stopCapture();
#endif
}

void SecureCallTests::stopVideo(SecureTestAccount& alice, SecureCallTests::VideoInfo& info)
{
#ifndef __linux__
   alice.video->stopCapture();
   alice.video->setLocalVideoRenderTarget(NULL);
   ViEDestroyWindow(info.nswindowAliceCaptureWindow, info.hwndAliceCapture);
   ViEDestroyWindow(info.nswindowAliceRemoteWindow, info.hwndAliceRemote);
#else
   alice.video->stopCapture();
#endif
}

TEST_F(SecureCallTests, padBase64)
{
   int padError;
   resip::Data encoded = "e/AvqYJZMylrI48NDbjXu88FbhbqYjDZUg1aXQ";
   resip::Data padded = encoded.base64addpadding(&padError);
   safeCout("encoded: " << encoded << " padded:" << padded);
   ASSERT_EQ(0, padError);
   ASSERT_EQ(0, (padded.size()%4));

   //already padded
   encoded = "e/AvqYJZMylrI48NDbjXu88FbhbqYjDZUg1aXQ==";
   padded = encoded.base64addpadding(&padError);
   ASSERT_EQ(-1, padError);
   ASSERT_EQ(encoded, padded);

   // invalid data length
   encoded = "e/AvqYJZMylrI48NDbjXu88FbhbqYjDZUg1aX";
   padded = encoded.base64addpadding(&padError);
   ASSERT_EQ(-2, padError);
   ASSERT_EQ(encoded, padded);
}

}  // namespace
