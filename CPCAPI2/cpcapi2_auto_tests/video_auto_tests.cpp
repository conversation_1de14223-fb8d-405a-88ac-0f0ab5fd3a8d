#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#include "cpcapi2_test_fixture.h"

#include "test_framework/cpcapi2_test_framework.h"
#include "test_framework/bare_bones_sip_endpoint.h"

#include <thread>
#include <future>

using namespace CPCAPI2;
using namespace CPCAPI2::Media;
using namespace CPCAPI2::test;

namespace {

class VideoModuleTest : public CpcapiAutoTest
{
public:
   VideoModuleTest() {}
   virtual ~VideoModuleTest() {}
};

TEST_F(VideoModuleTest, CodecEnableDisable) {

   TestAccount alice("alice");
   
   CPCAPI2::Media::VideoCodecListUpdatedEvent evt;
   int handle = 0;
   
   ASSERT_EQ(alice.video->queryCodecList(), kSuccess);
   ASSERT_TRUE(alice.mediaEvents->expectEvent("VideoHandler::onVideoCodecListUpdated", 30000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
   ASSERT_NE(evt.codecInfo.size(), 0);
   alice.video->setCodecEnabled(evt.codecInfo[0].id, false);
   ASSERT_EQ(alice.video->queryCodecList(), kSuccess);
   ASSERT_TRUE(alice.mediaEvents->expectEvent("VideoHandler::onVideoCodecListUpdated", 30000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
   ASSERT_NE(evt.codecInfo.size(), 0);
   ASSERT_EQ(evt.codecInfo[0].enabled, false);

}


TEST_F(VideoModuleTest, CodecPriority) {

   TestAccount alice("alice");
   CPCAPI2::Media::VideoCodecListUpdatedEvent evt;
   int handle = 0;
   
   ASSERT_EQ(alice.video->queryCodecList(), kSuccess);
   ASSERT_TRUE(alice.mediaEvents->expectEvent("VideoHandler::onVideoCodecListUpdated", 30000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
   size_t size = evt.codecInfo.size();
   ASSERT_NE(0, size);

   safeCout("Before");
   for(cpc::vector<VideoCodecInfo>::const_iterator it = evt.codecInfo.begin(); it != evt.codecInfo.end(); it++)
   {
      safeCout("\t" << "id=" << it->id << " " << (it->codecName) << "  priority=" << it->priority);
   }

   unsigned int priorityId = evt.codecInfo[0].id;
   unsigned int priority = 10;
   alice.video->setCodecPriority(priorityId, priority);
   
   ASSERT_EQ(alice.video->queryCodecList(), kSuccess);
   ASSERT_TRUE(alice.mediaEvents->expectEvent("VideoHandler::onVideoCodecListUpdated", 30000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
   ASSERT_EQ(size, evt.codecInfo.size());

   safeCout("After");
   for(cpc::vector<VideoCodecInfo>::const_iterator it = evt.codecInfo.begin(); it != evt.codecInfo.end(); it++)
   {
      safeCout("\t" << "id=" << it->id << " " << (it->codecName) << "  priority=" << it->priority);
   }
   ASSERT_EQ(priorityId, evt.codecInfo[size-1].id);
   ASSERT_EQ(priority, evt.codecInfo[size-1].priority);
}


TEST_F(VideoModuleTest, VideoCaptureOnOff) {
   TestAccount alice("alice", Account_Init);
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
#if _WIN32
   HWND hwndAliceCaptureLeft = NULL;
   HWND hwndAliceCaptureRight = NULL;
   ASSERT_EQ(0, ViECreateWindow(hwndAliceCaptureLeft, 0, 0, 352, 288, "Alice (capture LEFT)"));
   ASSERT_EQ(0, ViECreateWindow(hwndAliceCaptureRight, 356, 0, 352, 288, "Alice (capture RIGHT)"));

   auto aliceEvents = std::async(std::launch::async, [&] () {
      alice.video->startCapture();
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      std::cout << "\nEXPECT: Video on LEFT" << std::endl;
      alice.video->setLocalVideoRenderTarget(hwndAliceCaptureLeft);
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      std::cout << "\nEXPECT: No Video (target=NULL)" << std::endl;
      alice.video->setLocalVideoRenderTarget(NULL);
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      std::cout << "\nEXPECT: Video on RIGHT" << std::endl;
      alice.video->setLocalVideoRenderTarget(hwndAliceCaptureRight);
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      std::cout << "\nEXPECT: No Video (capture stopped)" << std::endl;
      alice.video->stopCapture();
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      std::cout << "\nEXPECT: Video on RIGHT (capture resumed)" << std::endl;
      alice.video->startCapture();
      std::this_thread::sleep_for(std::chrono::milliseconds(3000));
      std::cout << "\nEXPECT: Video on LEFT (capture stopped, render switched to left, capture resumed)" << std::endl;
      alice.video->stopCapture();
      alice.video->setLocalVideoRenderTarget(hwndAliceCaptureLeft);
      alice.video->startCapture();
      std::this_thread::sleep_for(std::chrono::milliseconds(3000));
   });

   ASSERT_NO_THROW(aliceEvents.get());

   alice.video->stopCapture();
   alice.video->setLocalVideoRenderTarget(NULL);
   DestroyWindow(hwndAliceCaptureLeft);
   DestroyWindow(hwndAliceCaptureRight);

 #endif
}

TEST_F(VideoModuleTest, VideoCaptureThenChangePreviewResolution) {
   TestAccount alice("alice");
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
#if _WIN32
   HWND hwndAliceCaptureLeft = NULL;
   HWND hwndAliceCaptureRight = NULL;
   ASSERT_EQ(0, ViECreateWindow(hwndAliceCaptureLeft, 0, 0, 352, 288, "Alice (capture LEFT)"));
   ASSERT_EQ(0, ViECreateWindow(hwndAliceCaptureRight, 356, 0, 352, 288, "Alice (capture RIGHT)"));

   auto aliceEvents = std::async(std::launch::async, [&] () {
      alice.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_Low);

      alice.video->startCapture();
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      std::cout << "\nEXPECT: Video on LEFT" << std::endl;
      alice.video->setLocalVideoRenderTarget(hwndAliceCaptureLeft);
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      std::cout << "change resolution" << std::endl;
      alice.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

      std::this_thread::sleep_for(std::chrono::milliseconds(3000));
   });

   ASSERT_NO_THROW(aliceEvents.get());

   alice.video->stopCapture();
   alice.video->setLocalVideoRenderTarget(NULL);
   DestroyWindow(hwndAliceCaptureLeft);
   DestroyWindow(hwndAliceCaptureRight);

 #endif
}

}
