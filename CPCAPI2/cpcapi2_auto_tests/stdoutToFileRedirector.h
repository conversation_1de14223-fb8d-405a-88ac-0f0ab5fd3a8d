#ifndef STDOUTTOFILEREDIRECTOR_H
#define STDOUTTOFILEREDIRECTOR_H

#include "stdoutRedirector.h"
#include <atomic>

namespace CPCAPI2
{
    class StdoutToFileRedirector : public StdoutRedirector
    {
        public:
            StdoutToFileRedirector(const std::string& filePath);
            virtual ~StdoutToFileRedirector();
            void runLoggingThread();
            void setRedirectDebugLoggingEnabled(bool enabled);
            static void* thread_func(void*);

        private:
            static int pfd[2];
            static const char* LOGOUTPUT;
            int errNum;
            pthread_t thr;
            std::atomic_bool mShutdown;
            bool mRedirectDebugLoggingEnabled;
            std::string mFilePath;
    };
}

#endif
