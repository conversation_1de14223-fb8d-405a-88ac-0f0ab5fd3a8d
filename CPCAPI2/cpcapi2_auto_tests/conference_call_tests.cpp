#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#include "cpcapi2_test_fixture.h"
#include "test_call_events.h"
#include "test_events.h"

using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::SipConversation;
using namespace CPCAPI2::SipAccount;

#ifdef WIN32
#include <direct.h>
#define GetCurrentDir _getcwd
#else
#include <unistd.h>
#define GetCurrentDir getcwd
#endif

class ConferenceCallTests : public CpcapiAutoTest
{
public:
   ConferenceCallTests() {}
   virtual ~ConferenceCallTests() {}
};

TEST_F(ConferenceCallTests, ConferenceCallExitDuringConference) {
   TestAccount alice("alice");
   TestAccount* bob = new TestAccount("bob", Account_Enable, false);
   TestAccount max("max");

   // make an outgoing (audio only) call from <PERSON> to <PERSON> using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob->config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(alice, aliceCall, bob->config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendOnly);

      assertSuccess(alice.conversation->accept(aliceCall));
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_ReceiveOnly);
      assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendReceive);
      assertSuccess(alice.conversation->accept(aliceCall));
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);

      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(*bob, &bobCall, alice.config.uri());
      // Bob sends 180 ringing -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob->conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(*bob, bobCall, ConversationState_LocalRinging);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob->conversation->accept(bobCall));
      assertConversationMediaChanged(*bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(*bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      assertSuccess(bob->conversation->hold(bobCall));
      assertConversationMediaChanged(*bob, bobCall, MediaDirection_SendOnly);

      // make an outgoing (audio only) call from Bob to Max using the demo.xten.com server
      SipConversationHandle bobCallToMax = bob->conversation->createConversation(bob->handle);
      bob->conversation->addParticipant(bobCallToMax, max.config.uri());
      bob->conversation->start(bobCallToMax);
        
      // BOB <=> MAX
      assertNewConversationOutgoing(*bob, bobCallToMax, max.config.uri());
      assertConversationStateChanged(*bob, bobCallToMax, ConversationState_RemoteRinging);
      assertConversationMediaChanged(*bob, bobCallToMax, MediaDirection_SendReceive);
      assertConversationStateChanged(*bob, bobCallToMax, ConversationState_Connected);

      // make this a conference by un-holding all calls
      assertSuccess(bob->conversation->unhold(bobCall));
      assertConversationMediaChanged(*bob, bobCall, MediaDirection_SendReceive);

      std::this_thread::sleep_for(std::chrono::milliseconds(2000));

      //bob->conversation->end(bobCallToMax);
      //bob->conversation->end(bobCall);
      delete bob;

      //assertConversationEnded(*bob, bobCallToMax, ConversationEndReason_UserTerminatedLocally);

      // BOB <=> ALICE
      //assertConversationEnded(*bob, bobCall, ConversationEndReason_UserTerminatedLocally);



   });

   // Overview of Max's thread:
   auto maxEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle maxCallFromBob;
      assertNewConversationIncoming(max, &maxCallFromBob, bob->config.uri());
      // Bob sends 180 ringing -- this should trigger state transitions for both Bob and Max
      assertSuccess(max.conversation->sendRingingResponse(maxCallFromBob));
      assertConversationStateChanged(max, maxCallFromBob, ConversationState_LocalRinging);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(max.conversation->accept(maxCallFromBob));
      assertConversationMediaChanged(max, maxCallFromBob, MediaDirection_SendReceive);
      assertConversationStateChanged(max, maxCallFromBob, ConversationState_Connected);
      assertConversationEnded(max, maxCallFromBob, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor3(aliceEvents, bobEvents, maxEvents);

   std::this_thread::sleep_for(std::chrono::milliseconds(10000));
}

TEST_F(ConferenceCallTests, RecordedConferenceCallMerged) {
   TestAccount alice("alice");
   TestAccount bob("bob");
   TestAccount max("max");
   
   // this test has bob localling conferencing in alice and max. Bob records the conference.
   // right now there is no automated check in place to see if the recorded audio is correct or not.
   // to manually verify, run the test and then listen to the output of the ConferenceCallTests.RecordedConferenceCall
   // WAV file, and ensure both auto generated sound clips are heard.
   
   // note: could potentially be enhanced in the future to check via some automated means (idea: have alice and max play in-band
   // DTMF tones, and then have an automated tool check bob's wav file for the presence of certain DTMF tones)
   
   
   //alice.enableOnlyThisCodec("OPUS");
   //max.enableOnlyThisCodec("G.722");
   
   // make an outgoing (audio only) call from Alice to Bob
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);
   
   auto aliceEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      
      assertSuccess(alice.conversation->playSound(aliceCall, "file:" + TestEnvironmentConfig::testResourcePath() + "testA-44khz.wav", true));
      
      assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendOnly);
      
      assertSuccess(alice.conversation->accept(aliceCall));
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_ReceiveOnly);
      assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendReceive);
      assertSuccess(alice.conversation->accept(aliceCall));
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
   });
   
   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      // Bob sends 180 ringing -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      
      std::stringstream recordFileName;
      recordFileName << TestEnvironmentConfig::tempPath() << "ConferenceCallTests.RecordedConferenceCall-" << resip::Random::getCryptoRandomHex(6).c_str() << ".wav";
      CPCAPI2::Recording::RecorderHandle recorderHandle = bob.recording->audioRecorderCreate(recordFileName.str().c_str(), false);
      assertSuccess(bob.recording->recorderAddConversation(recorderHandle, bobCall));
      assertSuccess(bob.recording->recorderStart(recorderHandle));
      
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertSuccess(bob.conversation->hold(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendOnly);
      
      // make an outgoing (audio only) call from Bob to Max
      SipConversationHandle bobCallToMax = bob.conversation->createConversation(bob.handle);
      bob.conversation->addParticipant(bobCallToMax, max.config.uri());
      bob.conversation->start(bobCallToMax);
      
      // BOB <=> MAX
      assertNewConversationOutgoing(bob, bobCallToMax, max.config.uri());
      assertConversationStateChanged(bob, bobCallToMax, ConversationState_RemoteRinging);
      assertConversationMediaChanged(bob, bobCallToMax, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCallToMax, ConversationState_Connected);
      
      bob.setupRecorderHandler(recorderHandle);
      assertSuccess(bob.recording->recorderAddConversation(recorderHandle, bobCallToMax));
      
      // make this a conference by un-holding all calls
      assertSuccess(bob.conversation->unhold(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);

      // it doesn't appear that these sounds get recorded
      //assertSuccess(bob.conversation->playSound(bobCall, "file:testB-44khz.wav", true));
      //assertSuccess(bob.conversation->playSound(bobCallToMax, "file:testB-44khz.wav", true));
      
      std::this_thread::sleep_for(std::chrono::milliseconds(3000));
      
      // make sure we have one file containing both the local stream and the remote streams

      bob.recording->getRecorderFilenames(recorderHandle);

      {
         CPCAPI2::Recording::RecorderHandle conn;
         CPCAPI2::Recording::RecorderFilenamesEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.recordingEvents, "RecordingHandler::onRecorderFilenames", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.files.size(), 1);
         for (cpc::vector<CPCAPI2::Recording::RecorderFilenameInfo>::const_iterator it = args.files.begin(); it != args.files.end(); it++)
         {
            ASSERT_NE(access(it->filename.c_str(), 0 /*F_OK*/), -1);
         }
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(3000));

      assertSuccess(bob.recording->recorderDestroy(recorderHandle));
      
      // test for automatically sent filenames when recording completes before the call ends
      {
         CPCAPI2::Recording::RecorderHandle conn;
         CPCAPI2::Recording::RecorderFilenamesEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.recordingEvents, "RecordingHandler::onRecorderFilenames", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.files.size(), 1);
         for (cpc::vector<CPCAPI2::Recording::RecorderFilenameInfo>::const_iterator it = args.files.begin(); it != args.files.end(); it++)
         {
            ASSERT_EQ(it->completed, true);
            ASSERT_NE(access(it->filename.c_str(), 0 /*F_OK*/), -1);
            struct stat st;
            stat(it->filename.c_str(), &st);
            ASSERT_GE(st.st_size, 500);   // sometimes audio records only a header so check for a significant size
         }
      }

      // must clean up AFTER the event has been recieved in order to receive it
      bob.cleanupRecorderHandler(recorderHandle);

      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      assertSuccess(bob.conversation->end(bobCallToMax));
      assertConversationEnded(bob, bobCallToMax, ConversationEndReason_UserTerminatedLocally);
   });
   
   // Overview of Max's thread:
   auto maxEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle maxCallFromBob;
      assertNewConversationIncoming(max, &maxCallFromBob, bob.config.uri());
      // Bob sends 180 ringing -- this should trigger state transitions for both Bob and Max
      assertSuccess(max.conversation->sendRingingResponse(maxCallFromBob));
      assertConversationStateChanged(max, maxCallFromBob, ConversationState_LocalRinging);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(max.conversation->accept(maxCallFromBob));
      assertConversationMediaChanged(max, maxCallFromBob, MediaDirection_SendReceive);
      assertConversationStateChanged(max, maxCallFromBob, ConversationState_Connected);
      
      assertSuccess(max.conversation->playSound(maxCallFromBob, "file:" + TestEnvironmentConfig::testResourcePath() + "testC-44khz.wav", true));
      
      assertConversationEnded(max, maxCallFromBob, ConversationEndReason_UserTerminatedRemotely);
   });
   
   waitFor3(aliceEvents, bobEvents, maxEvents);
}

TEST_F(ConferenceCallTests, RecordedConferenceCallSplit) {
   TestAccount alice("alice");
   TestAccount bob("bob");
   TestAccount max("max");
   
   // this test has bob localling conferencing in alice and max. Bob records the conference.
   // right now there is no automated check in place to see if the recorded audio is correct or not.
   // to manually verify, run the test and then listen to the output of the ConferenceCallTests.RecordedConferenceCall
   // WAV file, and ensure both auto generated sound clips are heard.
   
   // note: could potentially be enhanced in the future to check via some automated means (idea: have alice and max play in-band
   // DTMF tones, and then have an automated tool check bob's wav file for the presence of certain DTMF tones)
   
   
   //alice.enableOnlyThisCodec("OPUS");
   //max.enableOnlyThisCodec("G.722");
   
   // make an outgoing (audio only) call from Alice to Bob
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);
   
   auto aliceEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      
      assertSuccess(alice.conversation->playSound(aliceCall, "file:" + TestEnvironmentConfig::testResourcePath() + "testA-44khz.wav", true));
      
      assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendOnly);
      
      assertSuccess(alice.conversation->accept(aliceCall));
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_ReceiveOnly);
      assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendReceive);
      assertSuccess(alice.conversation->accept(aliceCall));
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
   });
   
   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      // Bob sends 180 ringing -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      
      std::stringstream recordFileName;
      recordFileName << TestEnvironmentConfig::tempPath() << "ConferenceCallTests.RecordedConferenceCall-" << resip::Random::getCryptoRandomHex(6).c_str() << ".wav";
      CPCAPI2::Recording::RecorderHandle recorderHandle = bob.recording->audioRecorderCreate(recordFileName.str().c_str(), true);
      assertSuccess(bob.recording->recorderAddConversation(recorderHandle, bobCall));
      assertSuccess(bob.recording->recorderStart(recorderHandle));
      
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertSuccess(bob.conversation->hold(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendOnly);
      
      // make an outgoing (audio only) call from Bob to Max
      SipConversationHandle bobCallToMax = bob.conversation->createConversation(bob.handle);
      bob.conversation->addParticipant(bobCallToMax, max.config.uri());
      bob.conversation->start(bobCallToMax);
      
      // BOB <=> MAX
      assertNewConversationOutgoing(bob, bobCallToMax, max.config.uri());
      assertConversationStateChanged(bob, bobCallToMax, ConversationState_RemoteRinging);
      assertConversationMediaChanged(bob, bobCallToMax, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCallToMax, ConversationState_Connected);
      
      bob.setupRecorderHandler(recorderHandle);
      assertSuccess(bob.recording->recorderAddConversation(recorderHandle, bobCallToMax));
      
      // make this a conference by un-holding all calls
      assertSuccess(bob.conversation->unhold(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);

      // it doesn't appear that these sounds get recorded
      //assertSuccess(bob.conversation->playSound(bobCall, "file:testB-44khz.wav", true));
      //assertSuccess(bob.conversation->playSound(bobCallToMax, "file:testB-44khz.wav", true));
      
      // test for manually requested filenames
      bob.recording->getRecorderFilenames(recorderHandle);

      {
         CPCAPI2::Recording::RecorderHandle conn;
         CPCAPI2::Recording::RecorderFilenamesEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.recordingEvents, "RecordingHandler::onRecorderFilenames", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.files.size(), 2);
         for (cpc::vector<CPCAPI2::Recording::RecorderFilenameInfo>::const_iterator it = args.files.begin(); it != args.files.end(); it++)
         {
            ASSERT_EQ(it->completed, false);
            ASSERT_NE(access(it->filename.c_str(), 0 /*F_OK*/), -1);
         }
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(3000));
      
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      assertSuccess(bob.conversation->end(bobCallToMax));
      assertConversationEnded(bob, bobCallToMax, ConversationEndReason_UserTerminatedLocally);
      
      std::this_thread::sleep_for(std::chrono::milliseconds(3000));

      assertSuccess(bob.recording->recorderDestroy(recorderHandle));

      // test for automatically sent filenames when recording completes after the call ends
      {
         CPCAPI2::Recording::RecorderHandle conn;
         CPCAPI2::Recording::RecorderFilenamesEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.recordingEvents, "RecordingHandler::onRecorderFilenames", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.files.size(), 2);
         for (cpc::vector<CPCAPI2::Recording::RecorderFilenameInfo>::const_iterator it = args.files.begin(); it != args.files.end(); it++)
         {
            ASSERT_EQ(it->completed, true);
            ASSERT_NE(access(it->filename.c_str(), 0 /*F_OK*/), -1);
            struct stat st;
            stat(it->filename.c_str(), &st);
            ASSERT_GE(st.st_size, 500);   // sometimes audio records only a header so check for a significant size

            // when recording completes we should be free to do whatever we want with the file, this makes sure the file
            // is not still being held open
            char buf[FILENAME_MAX];
            GetCurrentDir(buf, sizeof(buf));
            if (buf[strlen(buf)-1] != '/' && buf[strlen(buf)-1] != '\\')
               strcat(buf, "/");
            strcat(buf, it->filename.c_str());
            ASSERT_EQ(remove(buf), 0);
         }
      }

      // must clean up AFTER the event has been recieved in order to receive it
      bob.cleanupRecorderHandler(recorderHandle);
   });
   
   // Overview of Max's thread:
   auto maxEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle maxCallFromBob;
      assertNewConversationIncoming(max, &maxCallFromBob, bob.config.uri());
      // Bob sends 180 ringing -- this should trigger state transitions for both Bob and Max
      assertSuccess(max.conversation->sendRingingResponse(maxCallFromBob));
      assertConversationStateChanged(max, maxCallFromBob, ConversationState_LocalRinging);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(max.conversation->accept(maxCallFromBob));
      assertConversationMediaChanged(max, maxCallFromBob, MediaDirection_SendReceive);
      assertConversationStateChanged(max, maxCallFromBob, ConversationState_Connected);
      
      assertSuccess(max.conversation->playSound(maxCallFromBob, "file:" + TestEnvironmentConfig::testResourcePath() + "testC-44khz.wav", true));
      
      assertConversationEnded(max, maxCallFromBob, ConversationEndReason_UserTerminatedRemotely);
   });
   
   waitFor3(aliceEvents, bobEvents, maxEvents);
}

TEST_F(ConferenceCallTests, RecordedConferenceCallBadPath) {
   TestAccount alice("alice");
   TestAccount bob("bob");
   TestAccount max("max");

   // this test has bob localling conferencing in alice and max. Bob records the conference.
   // right now there is no automated check in place to see if the recorded audio is correct or not.
   // to manually verify, run the test and then listen to the output of the ConferenceCallTests.RecordedConferenceCall
   // WAV file, and ensure both auto generated sound clips are heard.

   // note: could potentially be enhanced in the future to check via some automated means (idea: have alice and max play in-band
   // DTMF tones, and then have an automated tool check bob's wav file for the presence of certain DTMF tones)


   //alice.enableOnlyThisCodec("OPUS");
   //max.enableOnlyThisCodec("G.722");

   // make an outgoing (audio only) call from Alice to Bob
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]() {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      assertSuccess(alice.conversation->playSound(aliceCall, "file:" + TestEnvironmentConfig::testResourcePath() + "testA-44khz.wav", true));

      assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendOnly);

      assertSuccess(alice.conversation->accept(aliceCall));
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_ReceiveOnly);
      assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendReceive);
      assertSuccess(alice.conversation->accept(aliceCall));
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);

      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      // Bob sends 180 ringing -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::stringstream recordFileName;
      recordFileName << "/BadPath/ConferenceCallTests.RecordedConferenceCall-" << resip::Random::getCryptoRandomHex(6).c_str() << ".wav";
      CPCAPI2::Recording::RecorderHandle recorderHandle = bob.recording->audioRecorderCreate(recordFileName.str().c_str(), true);
      bob.setupRecorderHandler(recorderHandle);
      assertSuccess(bob.recording->recorderAddConversation(recorderHandle, bobCall));
      assertSuccess(bob.recording->recorderStart(recorderHandle));

      {
         CPCAPI2::Recording::RecorderHandle conn;
         CPCAPI2::Recording::RecordingStoppedEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.recordingEvents, "RecordingHandler::onRecordingStoppedEx", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.reason, CPCAPI2::Recording::RecordingStopReason_BadFile);
      }
      
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertSuccess(bob.conversation->hold(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendOnly);

      // make an outgoing (audio only) call from Bob to Max
      SipConversationHandle bobCallToMax = bob.conversation->createConversation(bob.handle);
      bob.conversation->addParticipant(bobCallToMax, max.config.uri());
      bob.conversation->start(bobCallToMax);

      // BOB <=> MAX
      assertNewConversationOutgoing(bob, bobCallToMax, max.config.uri());
      assertConversationStateChanged(bob, bobCallToMax, ConversationState_RemoteRinging);
      assertConversationMediaChanged(bob, bobCallToMax, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCallToMax, ConversationState_Connected);

      assertSuccess(bob.recording->recorderAddConversation(recorderHandle, bobCallToMax));

      // make this a conference by un-holding all calls
      assertSuccess(bob.conversation->unhold(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);

      // it doesn't appear that these sounds get recorded
      //assertSuccess(bob.conversation->playSound(bobCall, "file:testB-44khz.wav", true));
      //assertSuccess(bob.conversation->playSound(bobCallToMax, "file:testB-44khz.wav", true));

      // test for manually requested filenames
      bob.recording->getRecorderFilenames(recorderHandle);

      {
         CPCAPI2::Recording::RecorderHandle conn;
         CPCAPI2::Recording::RecorderFilenamesEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.recordingEvents, "RecordingHandler::onRecorderFilenames", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.files.size(), 2);
         for (cpc::vector<CPCAPI2::Recording::RecorderFilenameInfo>::const_iterator it = args.files.begin(); it != args.files.end(); it++)
         {
            ASSERT_EQ(it->completed, false);
            ASSERT_EQ(it->error, CPCAPI2::Recording::RecordingStreamError_InvalidPath);
            ASSERT_EQ(access(it->filename.c_str(), 0 /*F_OK*/), -1); // NOTE: we expect an error!
         }
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(3000));

      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      assertSuccess(bob.conversation->end(bobCallToMax));
      assertConversationEnded(bob, bobCallToMax, ConversationEndReason_UserTerminatedLocally);

      std::this_thread::sleep_for(std::chrono::milliseconds(3000));

      assertSuccess(bob.recording->recorderDestroy(recorderHandle));

      // test for automatically sent filenames when recording completes after the call ends
      {
         CPCAPI2::Recording::RecorderHandle conn;
         CPCAPI2::Recording::RecorderFilenamesEvent args;
         ASSERT_TRUE(cpcExpectEvent(bob.recordingEvents, "RecordingHandler::onRecorderFilenames", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
         ASSERT_EQ(args.files.size(), 2);
         for (cpc::vector<CPCAPI2::Recording::RecorderFilenameInfo>::const_iterator it = args.files.begin(); it != args.files.end(); it++)
         {
            ASSERT_EQ(it->completed, true);
            ASSERT_EQ(it->error, CPCAPI2::Recording::RecordingStreamError_InvalidPath);
            ASSERT_EQ(access(it->filename.c_str(), 0 /*F_OK*/), -1); // NOTE: we expect an error!
         }
      }      

      // must clean up AFTER the event has been recieved in order to receive it
      bob.cleanupRecorderHandler(recorderHandle);
   });

   // Overview of Max's thread:
   auto maxEvents = std::async(std::launch::async, [&]() {
      SipConversationHandle maxCallFromBob;
      assertNewConversationIncoming(max, &maxCallFromBob, bob.config.uri());
      // Bob sends 180 ringing -- this should trigger state transitions for both Bob and Max
      assertSuccess(max.conversation->sendRingingResponse(maxCallFromBob));
      assertConversationStateChanged(max, maxCallFromBob, ConversationState_LocalRinging);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(max.conversation->accept(maxCallFromBob));
      assertConversationMediaChanged(max, maxCallFromBob, MediaDirection_SendReceive);
      assertConversationStateChanged(max, maxCallFromBob, ConversationState_Connected);

      assertSuccess(max.conversation->playSound(maxCallFromBob, "file:" + TestEnvironmentConfig::testResourcePath() + "testC-44khz.wav", true));

      assertConversationEnded(max, maxCallFromBob, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor3(aliceEvents, bobEvents, maxEvents);
}
