cmake_minimum_required(VERSION 3.8.0)

if (CMAKE_BUILD_TYPE MATCHES Debug)
  # note this will also evaluate to true if cross compiling for Android on macOS
  if (APPLE)
    # enable ASan by for debug builds by default.
    # note that we need to set <PERSON><PERSON><PERSON><PERSON><PERSON> before configure.cmake is included otherwise it won't
    # be applied.
    set(<PERSON><PERSON><PERSON>ZER "Address" CACHE STRING "")
  endif()
endif() 

# warning: OS_xyz (e.g. OS_ANDROID) will not be set above this line for clean builds
include(${CMAKE_CURRENT_SOURCE_DIR}/../projects/cmake/toolchains/configure.cmake) # Must be included before project()

if(OS_ANDROID)
  add_definitions(-DCPCAPI2_AUTO_TEST -DCPCAPI2_INCLUDE_UNRELEASED_HEADERS -DUSE_WSS_JSON_SERVER=1 -DCPCAPI2_NO_SIMPLE_WEB_SERVER_HACK -DCPCAPI2_BRAND_AUTH_SERVER_MODULE -DCPCAPI2_NO_UNBOUND_SUPPORT)
else()
  add_definitions(-DC<PERSON>API2_AUTO_TEST -DCPC<PERSON>I2_INCLUDE_UNRELEASED_HEADERS -DUSE_WSS_JSON_SERVER=1)
endif()

option(DISABLE_CPCAPI2_SDK_LICENSING "Set brand option CPCAPI2_BRAND_SDK_LICENSING to 0" OFF)

if(DISABLE_CPCAPI2_SDK_LICENSING EQUAL 1)
  message("Setting CPCAPI2_AUTOTESTS_NO_SDK_LICENSING=1")
  add_definitions(-DCPCAPI2_AUTOTESTS_NO_SDK_LICENSING=1)
endif()

if (OS_WINDOWS)
  add_definitions(-DWIN32)
  set(BREAKPAD_INTEGRATION TRUE)
else()
  set(BREAKPAD_INTEGRATION FALSE)
endif()

project (cpcapi2_auto_tests LANGUAGES ${CPCAPI2_LANGUAGES})
init_project()

set(ROOT_PATH         ${CMAKE_CURRENT_SOURCE_DIR}/../)
set(CPCAPI2_PATH      ${CMAKE_CURRENT_SOURCE_DIR}/../CPCAPI2)
set(SHARED_PATH       ${CMAKE_CURRENT_SOURCE_DIR}/../shared/)
set(RESIP_PATH        ${SHARED_PATH}/sipfoundry/main)
set(WEBRTC_PATH       ${SHARED_PATH}/WebRTCv/trunk/webrtc)
set(EXTERNAL_PATH     ${CMAKE_CURRENT_SOURCE_DIR}/../external)

set(TEST_FILE_BUILD_LIST
  sipua_alianza_api_auto_tests.cpp
  sipua_alianza_api_account_auto_tests.cpp
  sipua_account_auto_tests.cpp
  sipua_call_auto_tests.cpp
  sipua_call_transfer_auto_tests.cpp
  sipua_call_forking_auto_tests.cpp
  sipua_call_conference_auto_tests.cpp
  sipua_network_change_auto_tests.cpp
  account_auto_tests.cpp
  address_transformer_auto_test.cpp
  analytics1_auto_tests.cpp
  audio_auto_tests.cpp
  audio_quality_tests.cpp
  auth_server_tests.cpp
  basic_call_tests.cpp
  blf_local_auto_tests.cpp
#  broadsoft_xsi_auto_tests.cpp
#  busylampfield_auto_tests.cpp	    these tests have not been maintained and require third party accounts
  call_sanity_test.cpp
  call_state_tests.cpp
  call_statistics_tests.cpp
  call_transfer_tests.cpp
  call_quality_reporter_tests.cpp
  capability_auto_tests.cpp
  chat_auto_tests.cpp
  cpu_usage_tests.cpp
  orchestration_server_auto_tests.cpp
  confbridge_manager_auto_tests.cpp
  conference_call_tests.cpp
  conference_connector_auto_tests.cpp
  cpcapi2_auto_tests.cpp
  cpprovisioning_auto_tests.cpp
  dialogevent_auto_tests.cpp
  dns_failover_auto_tests.cpp
  dns_auto_test.cpp
  eventqueue_auto_tests.cpp
  event_sanity_tests.cpp
  event_subscription_tests.cpp
  filetransfer_auto_tests.cpp
  fileutils_auto_tests.cpp
  forking_call_tests.cpp
  genband_auto_tests.cpp
  hddiskid_auto_tests.cpp
  http_auto_tests.cpp
  http_auto_tests2.cpp
  https_auto_tests.cpp
  im_auto_tests.cpp
  intercom_call_auto_tests.cpp
  iscomposing_auto_tests.cpp
  json_api_call_tests.cpp
  json_rpc_tests.cpp
  ldap_auto_tests.cpp
  license_auto_tests.cpp
  licensing_auto_tests.cpp
  machineidentification_auto_tests.cpp
  message_store_auto_tests.cpp
  mwi_auto_tests.cpp
  network_change_auto_tests.cpp
  peer_connection_auto_tests.cpp
  phone_auto_tests.cpp
  prack_call_tests.cpp
  presence_publish_tests.cpp
  presence_sanity_tests.cpp
  presence_subscription_tests.cpp
  presence_xml_tests.cpp
  provision_auto_tests.cpp
  regevent_auto_tests.cpp
  regutils_auto_tests.cpp
  registryutils_auto_tests.cpp
  remotesync_auto_tests.cpp
  secure_call_tests.cpp
  server_conference_auto_tests.cpp
  sharedcallappearance_auto_tests.cpp
  sipp_auto_tests.cpp
  speech_quality_test.cpp
  standalone_messaging_auto_tests.cpp
  strettouem_auto_tests.cpp
  tls_auto_tests.cpp
  tscf_auto_tests.cpp
  vccs_auto_tests.cpp
  video_auto_tests.cpp
  video_call_tests.cpp
  watchdog_server_auto_tests.cpp
  watcher_info_auto_tests.cpp
  webcall_auto_tests.cpp
  websocket_server_auto_tests.cpp
  xcap_auto_tests.cpp
  vccs_test_harness/VccsTestHarness.cpp
  xmpp_account_tests.cpp
  xmpp_chat_tests.cpp
  xmpp_roster_tests.cpp
  xmpp_vcard_tests.cpp
  xmpp_multiuserchat_tests.cpp
  xmpp_agent_auto_tests.cpp
  xmpp_push_tests.cpp
  pushnotification_auto_tests.cpp
  ptt_auto_tests.cpp
  xmpp_filetransfer_auto_tests.cpp
  webrtc/rtcp_nack_stats_unittest.cc
  webrtc/rtcp_packet_unittest.cc
  webrtc/rtcp_receiver_unittest.cc
  webrtc/rtcp_sender_unittest.cc
  webrtc/rtcp_packet/rtcp_packet_parser.cc
  webrtc/rtcp_packet/app_unittest.cc
  webrtc/rtcp_packet/bye_unittest.cc
  webrtc/rtcp_packet/common_header_unittest.cc
  webrtc/rtcp_packet/compound_packet_unittest.cc
  webrtc/rtcp_packet/dlrr_unittest.cc
  webrtc/rtcp_packet/extended_jitter_report_unittest.cc
  webrtc/rtcp_packet/extended_reports_unittest.cc
  webrtc/rtcp_packet/fir_unittest.cc
  webrtc/rtcp_packet/nack_unittest.cc
  webrtc/rtcp_packet/pli_unittest.cc
  webrtc/rtcp_packet/rapid_resync_request_unittest.cc
  webrtc/rtcp_packet/receiver_report_unittest.cc
  webrtc/rtcp_packet/remb_unittest.cc
  webrtc/rtcp_packet/report_block_unittest.cc
  webrtc/rtcp_packet/rpsi_unittest.cc
  webrtc/rtcp_packet/rrtr_unittest.cc
  webrtc/rtcp_packet/sdes_unittest.cc
  webrtc/rtcp_packet/sender_report_unittest.cc
  webrtc/rtcp_packet/sli_unittest.cc
  webrtc/rtcp_packet/target_bitrate_unittest.cc
  webrtc/rtcp_packet/tmmbn_unittest.cc
  webrtc/rtcp_packet/tmmbr_unittest.cc
  webrtc/rtcp_packet/transport_feedback_unittest.cc
  webrtc/rtcp_packet/voip_metric_unittest.cc
  test_tools/sip_service_monitoring.cpp
  test_tools/sip_push_server_load_test_tool.cpp
)

if (NOT OS_WINDOWS)
  set(TEST_FILE_BUILD_LIST ${TEST_FILE_BUILD_LIST}
    watchdog_auto_tests.cpp
  )
endif()

if (CPCAPI2_BRAND_AUTH_SERVER_MODULE)
  set(TEST_FILE_BUILD_LIST ${TEST_FILE_BUILD_LIST}
    cloudconnector_auto_tests.cpp
  )
endif()

message("${CMAKE_CURRENT_SOURCE_DIR}/${OVERRIDE_TEST_FILE_BUILD_LIST}")
if (NOT "${OVERRIDE_TEST_FILE_BUILD_LIST}" STREQUAL "")
    if (NOT EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/${OVERRIDE_TEST_FILE_BUILD_LIST}")
      message( FATAL_ERROR "OVERRIDE_TEST_FILE_BUILD_LIST specified and couldn't find ${CMAKE_CURRENT_SOURCE_DIR}/${OVERRIDE_TEST_FILE_BUILD_LIST}" )
    endif()
  message("override files")
  set(TEST_FILE_BUILD_LIST
  "${OVERRIDE_TEST_FILE_BUILD_LIST}"
)
endif()

set(SOURCES
  ${TEST_FILE_BUILD_LIST}
  cpcapi2_auto_tests.cpp
  ReproRunner.cpp
  test_call_events.cpp
  test_events.cpp
  TestVideoHelper.cpp
  script_runner.cpp
  test_framework/cpcapi2_test_framework.cpp
  test_framework/xmpp_test_helper.cpp
  test_framework/http_test_framework.cpp
  cpcapi2_test_fixture.cpp
  tscf_test_fixture.cpp
  stdoutToLogcatRedirector.cpp
  stdoutToFileRedirector.cpp
  ${RESIP_PATH}/resip/dum/InMemorySyncRegDb.cxx
  ${RESIP_PATH}/resip/dum/TlsPeerAuthManager.cxx
  test_framework/test_pcap_capture.cpp
  test_framework/network_utils.cpp
  test_framework/ptt_test_helper.cpp
  test_framework/test_runtime_environment.cpp
  test_framework/android_bg_bookkeeper.cpp
  test_framework/visqol_runner.cpp
  test_framework/sipp_runner.cpp
  test_framework/dummy_tcplistener.cpp
  sipua_alianza_api_test_events.cpp
  sipua_alianza_api_test_helper.cpp
  sipua_alianza_api_test_fixture.cpp
  alianza_api/impl/alianza_api_types.cpp
  alianza_api/impl/alianza_api_manager_interface.cpp
  alianza_api/impl/alianza_api_account_fsm.cpp
  alianza_api/impl/alianza_api_account_state.cpp
  alianza_api/impl/alianza_api_manager_module.cpp
  # do not add test case files here; add above as part of TEST_FILE_BUILD_LIST
)

if (BREAKPAD_INTEGRATION)
  set(SOURCES ${SOURCES}
    breakpad_integration.cpp
    crash_dump_handling/cpcapi2_breakpad_server/cross_process_mutex_win32.cpp
  )
endif()

set(CMAKE_THREAD_PREFER_PTHREAD ON)
set(THREADS_PREFER_PTHREAD_FLAG ON)
find_package(Threads REQUIRED)

if (OS_LINUX)
  SET(USE_X11 ON CACHE BOOL "Build with X11 support")
  SET(USE_PULSEAUDIO OFF CACHE BOOL "Build with PusleAudio support")
endif()

if(OS_ANDROID)
set(SOURCES
  ${SOURCES}
  ${CPCAPI2_PATH}/impl/util/FileDescriptorMonitor.cpp
  ${CPCAPI2_PATH}/impl/logcat/LogcatMonitorInterface.cpp
  ${CPCAPI2_PATH}/impl/logcat/LogcatMonitorModule.cpp
  test_framework/logcat_monitor.cpp
)
elseif(OS_WINDOWS)
set(SOURCES
  ${SOURCES}
  utils/win_proc_utils.cpp
#  crashpad_integration.cpp
)
elseif(OS_MACOS)
set(SOURCES
  ${SOURCES}
  utils/mac_proc_utils.cpp
  crashpad_integration.cpp
  cocoa_helpers.mm
  webrtc/chromium/chromium_like_unittest.cc
  webrtc/chromium/string_utils.cc
  webrtc/chromium/audio_device_mac_new.cc
  ${ROOT_PATH}/projects/xcode/CPCAPI2/CPCObjAPI2/CPCVideoView_Mac.mm
)
set_source_files_properties( 
  cocoa_helpers.mm
  PROPERTIES 
    COMPILE_FLAGS -fno-objc-arc
    SKIP_PRECOMPILE_HEADERS ON
)

elseif(USE_X11)
set(SOURCES
  ${SOURCES}
  x11_helpers.cpp
)
endif()


include(${CMAKE_CURRENT_SOURCE_DIR}/../projects/cmake/CPCAPI2/CPCAPI2.cmake)
include(${CMAKE_CURRENT_SOURCE_DIR}/../projects/cmake/resip/repro.cmake)

if(OS_ANDROID)
  add_library(${CMAKE_PROJECT_NAME} SHARED ${SOURCES})
else()
  add_executable(${CMAKE_PROJECT_NAME} ${SOURCES})
endif()

if (NOT "${OVERRIDE_TEST_FILE_BUILD_LIST}" STREQUAL "")
  target_compile_definitions(${CMAKE_PROJECT_NAME} PRIVATE CPCAPI2_OVERRIDE_TEST_LIST_BUILD)
endif()

set(GOOGLETEST_ROOT "${CONAN_GOOGLETEST_ROOT}")
if(OS_WINDOWS)
  set(NPCAP_ROOT "${CONAN_NPCAP_ROOT}")
  set(DETOURS_ROOT "${CONAN_DETOURS_ROOT}")
elseif (OS_MACOS)
  set(SIPP_ROOT "${CONAN_SIPP_ROOT}")
  set(CRASHPAD_ROOT "${CONAN_CRASHPAD_ROOT}")
endif()
if(OS_LINUX OR OS_MACOS)
  set(VISQOL_ROOT "${CONAN_VISQOL_ROOT}")
 endif()
if (OS_LINUX)
  set(X11_ROOT "${CONAN_X11_ROOT}")
endif()

if((OS_LINUX OR OS_MACOS) AND NOT ARCH_ARM64)
  file(COPY "${VISQOL_ROOT}/bin/visqol" DESTINATION "${CMAKE_CURRENT_SOURCE_DIR}/runtime_resources/visqol/bin" 
       FILE_PERMISSIONS OWNER_READ OWNER_WRITE OWNER_EXECUTE GROUP_READ GROUP_WRITE GROUP_EXECUTE)
  file(COPY "${VISQOL_ROOT}/bin/visqol" DESTINATION "${CMAKE_CURRENT_SOURCE_DIR}/test_tools/testing_the_tests/sip_service_test_tool/visqol/bin"
       FILE_PERMISSIONS OWNER_READ OWNER_WRITE OWNER_EXECUTE GROUP_READ GROUP_WRITE GROUP_EXECUTE)
endif()
if (OS_MACOS)
  # todo: sipp support on other platforms
  file(COPY "${SIPP_ROOT}/bin/sipp" DESTINATION "${CMAKE_CURRENT_SOURCE_DIR}/runtime_resources/sipp/bin"
       FILE_PERMISSIONS OWNER_READ OWNER_WRITE OWNER_EXECUTE GROUP_READ GROUP_WRITE GROUP_EXECUTE)
endif()
  
if(OS_ANDROID)
  if (DEFINED LIBRARY_OUTPUT_PATH)
    set_target_properties(${CMAKE_PROJECT_NAME} PROPERTIES PREFIX ""
                                                  OUTPUT_NAME "cpcapi2_auto_tests"
                                                  LIBRARY_OUTPUT_DIRECTORY "${LIBRARY_OUTPUT_PATH}")
  else()
    set_target_properties(${CMAKE_PROJECT_NAME} PROPERTIES PREFIX "" OUTPUT_NAME "cpcapi2_auto_tests")
  endif()

  target_include_directories(${CMAKE_PROJECT_NAME}
          PRIVATE "${CPCAPI2_PATH}/impl"
                  "${EXTERNAL_PATH}/googletest/googlemock/include"
                  "${EXTERNAL_PATH}/googletest/googletest/include"
                  "${GOOGLETEST_ROOT}/include"
                  "${ROOT_PATH}/cpcapi2_auto_tests"
                  "${ROOT_PATH}/cpcapi2_auto_tests/test_framework"
                  "${ROOT_PATH}/cpcapi2_auto_tests/alianza_api/interface/public"
                  "${ROOT_PATH}/cpcapi2_auto_tests/alianza_api/interface/experimental"
                  "${ROOT_PATH}/cpcapi2_auto_tests/alianza_api/impl"
                  "${RESIP_PATH}/rutil/dns/ares"
                  "${RESIP_PATH}/resip/recon"
                  "${WEBRTC_PATH}/video_engine/include"
  )
elseif(OS_MACOS)
  target_include_directories(${CMAKE_PROJECT_NAME}
          PRIVATE "${GOOGLETEST_ROOT}/include"
                  "${CRASHPAD_ROOT}/include"
                  "${EXTERNAL_PATH}/libmsrp"
                  "${EXTERNAL_PATH}/websocketpp"
                  "${EXTERNAL_PATH}/Simple-Web-Server"
                  "${EXTERNAL_PATH}/rapidjson"
                  "${CPCAPI2_PATH}"
                  "${SHARED_PATH}/sipfoundry/main"
                  "${SHARED_PATH}/sipfoundry/main/resip/recon"
                  "${SHARED_PATH}/gloox/src"
                  "${CPCAPI2_PATH}/interface/public"
                  "${CPCAPI2_PATH}/interface/experimental"
                  "${ROOT_PATH}/projects/xcode/CPCAPI2/CPCObjAPI2/interface/public"
                  "${CPCAPI2_PATH}/impl"
                  "${EXTERNAL_PATH}/fmt/include"
                  "${EXTERNAL_PATH}/spdlog/include"
                  "${GOOGLETEST_ROOT}/include"
                  "${ROOT_PATH}/cpcapi2_auto_tests"
                  "${ROOT_PATH}/cpcapi2_auto_tests/utils"
                  "${ROOT_PATH}/cpcapi2_auto_tests/test_framework"
                  "${ROOT_PATH}/cpcapi2_auto_tests/alianza_api/interface/public"
                  "${ROOT_PATH}/cpcapi2_auto_tests/alianza_api/interface/experimental"
                  "${ROOT_PATH}/cpcapi2_auto_tests/alianza_api/impl"
  )
elseif(OS_WINDOWS)
  set_target_properties(${CMAKE_PROJECT_NAME} PROPERTIES LINK_FLAGS "/DELAYLOAD:wpcap.dll /LARGEADDRESSAWARE")
  target_include_directories(${CMAKE_PROJECT_NAME}
          PRIVATE "${CPCAPI2_PATH}/impl"
                  "${EXTERNAL_PATH}/googletest/googlemock/include"
                  "${EXTERNAL_PATH}/googletest/googletest/include"
                  "${GOOGLETEST_ROOT}/include"
                  "${ROOT_PATH}/cpcapi2_auto_tests"
                  "${ROOT_PATH}/cpcapi2_auto_tests/test_framework"
                  "${ROOT_PATH}/cpcapi2_auto_tests/alianza_api/interface/public"
                  "${ROOT_PATH}/cpcapi2_auto_tests/alianza_api/interface/experimental"
                  "${ROOT_PATH}/cpcapi2_auto_tests/alianza_api/impl"
                  "${RESIP_PATH}/resip/recon"
                  "${WEBRTC_PATH}/video_engine/include"
                  "${DETOURS_ROOT}/include"
                  "${NPCAP_ROOT}/include"
  )
else()
  target_include_directories(${CMAKE_PROJECT_NAME}
          PRIVATE "${CPCAPI2_PATH}/impl"
                  "${EXTERNAL_PATH}/googletest/googlemock/include"
                  "${EXTERNAL_PATH}/googletest/googletest/include"
                  "${GOOGLETEST_ROOT}/include"
                  "${ROOT_PATH}/cpcapi2_auto_tests"
                  "${ROOT_PATH}/cpcapi2_auto_tests/test_framework"
                  "${ROOT_PATH}/cpcapi2_auto_tests/alianza_api/interface/public"
                  "${ROOT_PATH}/cpcapi2_auto_tests/alianza_api/interface/experimental"
                  "${ROOT_PATH}/cpcapi2_auto_tests/alianza_api/impl"
                  "${RESIP_PATH}/resip/recon"
                  "${WEBRTC_PATH}/video_engine/include"
  )
endif()

set_property(TARGET ${CMAKE_PROJECT_NAME} PROPERTY CXX_STANDARD ${CPCAPI2_CXX_STANDARD})

if (BREAKPAD_INTEGRATION)
  target_include_directories(${CMAKE_PROJECT_NAME}
                     PRIVATE "${EXTERNAL_PATH}/breakpad"
  )

  set(BREAKPAD_ROOT "${CONAN_BREAKPAD_ROOT}/lib")

  
  target_link_libraries(${CMAKE_PROJECT_NAME}
                PRIVATE "${BREAKPAD_ROOT}/${CMAKE_STATIC_LIBRARY_PREFIX}breakpad_common${CMAKE_STATIC_LIBRARY_SUFFIX}"
                        "${BREAKPAD_ROOT}/${CMAKE_STATIC_LIBRARY_PREFIX}crash_generation_client${CMAKE_STATIC_LIBRARY_SUFFIX}"
                        "${BREAKPAD_ROOT}/${CMAKE_STATIC_LIBRARY_PREFIX}crash_report_sender${CMAKE_STATIC_LIBRARY_SUFFIX}"
                        "${BREAKPAD_ROOT}/${CMAKE_STATIC_LIBRARY_PREFIX}exception_handler${CMAKE_STATIC_LIBRARY_SUFFIX}"
  )

  set(BREAKPAD_SOURCES
    crash_dump_handling/cpcapi2_breakpad_server/cpcapi2_breakpad_server.cpp
  )

  if (OS_WINDOWS)
    set(BREAKPAD_SOURCES ${BREAKPAD_SOURCES}
      crash_dump_handling/cpcapi2_breakpad_server/cross_process_mutex_win32.cpp
    )
  endif()

  add_executable(cpcapi2_breakpad_server ${BREAKPAD_SOURCES})

  target_include_directories(cpcapi2_breakpad_server
          PRIVATE "${EXTERNAL_PATH}/breakpad"
  )

  target_link_libraries(cpcapi2_breakpad_server
                PRIVATE "${BREAKPAD_ROOT}/${CMAKE_STATIC_LIBRARY_PREFIX}breakpad_common${CMAKE_STATIC_LIBRARY_SUFFIX}"
                        "${BREAKPAD_ROOT}/${CMAKE_STATIC_LIBRARY_PREFIX}crash_generation_client${CMAKE_STATIC_LIBRARY_SUFFIX}"
                        "${BREAKPAD_ROOT}/${CMAKE_STATIC_LIBRARY_PREFIX}crash_generation_server${CMAKE_STATIC_LIBRARY_SUFFIX}"
                        "${BREAKPAD_ROOT}/${CMAKE_STATIC_LIBRARY_PREFIX}exception_handler${CMAKE_STATIC_LIBRARY_SUFFIX}"
  )

  add_dependencies(${CMAKE_PROJECT_NAME} cpcapi2_breakpad_server)
endif()

if ("${CMAKE_CXX_COMPILER_ID}" STREQUAL "GNU" OR "${CMAKE_CXX_COMPILER_ID}" STREQUAL "Clang")
    target_compile_options(${CMAKE_PROJECT_NAME} PRIVATE "-Wno-sign-compare") # g-test warning
endif()

if (OS_WINDOWS)
    target_compile_definitions(${CMAKE_PROJECT_NAME} PRIVATE "_SILENCE_EXPERIMENTAL_FILESYSTEM_DEPRECATION_WARNING")
endif()

target_compile_definitions(${CMAKE_PROJECT_NAME} PRIVATE CPCAPI2_NO_DEPRECATIONS)

if(USE_X11 AND OS_LINUX)
   target_include_directories(${CMAKE_PROJECT_NAME} PRIVATE "${X11_ROOT}/include")
   #link_directories(${X11_LIBRARIES})
endif()

target_link_libraries(${CMAKE_PROJECT_NAME} PRIVATE CPCAPI2_Static boost repro soci::sqlite3 rapidjson Threads::Threads absl::bad_optional_access ${X11_LIBRARIES})

# googletest 1.12.1 update dropped "d" from debug static lib name
set(GTEST_STATIC_LIBRARY_SUFFIX "${CMAKE_STATIC_LIBRARY_SUFFIX}")

target_link_libraries(${CMAKE_PROJECT_NAME}
                      PRIVATE "${GOOGLETEST_ROOT}/lib/${CMAKE_STATIC_LIBRARY_PREFIX}gmock${GTEST_STATIC_LIBRARY_SUFFIX}"
                      PRIVATE "${GOOGLETEST_ROOT}/lib/${CMAKE_STATIC_LIBRARY_PREFIX}gtest${GTEST_STATIC_LIBRARY_SUFFIX}"
)

if (OS_WINDOWS)
  target_link_libraries(${CMAKE_PROJECT_NAME} PRIVATE 
             Pdh.lib PowrProf.lib delayimp.lib
             "${DETOURS_ROOT}/lib/detours.lib"
  )
elseif (OS_MACOS)
  target_link_libraries(${CMAKE_PROJECT_NAME} PRIVATE
                        "${CRASHPAD_ROOT}/lib/libbase.a"
                        "${CRASHPAD_ROOT}/lib/libclient.a"
                        "${CRASHPAD_ROOT}/lib/libmach.a"
                        "${CRASHPAD_ROOT}/lib/libutil.a"
  )
endif()

# Detect libstdc++ which requires seperate libstdc++fs
INCLUDE(CheckCXXSourceCompiles)
CHECK_CXX_SOURCE_COMPILES(
  "#include <iostream>
  int ret =
  #ifdef __GLIBCXX__
    0;
  #else
    error;
  #endif
  int main(int c, char* a[]) { return ret; }"
  IS_LIBSTDCXX
)

if (IS_LIBSTDCXX)
  target_link_libraries(${CMAKE_PROJECT_NAME} PRIVATE stdc++fs)
endif()

# copy a launch.json with reasonable defaults for the user, since it looks like they don't have one already.
# this should not clobber a user's own/customized .vscode/launch.json
if(NOT EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/.vscode/launch.json")
  file(COPY ${CMAKE_CURRENT_SOURCE_DIR}/.vscode/templates/launch.json
     DESTINATION ${CMAKE_CURRENT_SOURCE_DIR}/.vscode)
endif() 
