//
//  cocoa_helpers.mm
//  CPCAPI2AutoTests
//
//  Created by <PERSON> on 2015-04-23.
//  Copyright (c) 2015 <PERSON>. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <QuartzCore/QuartzCore.h>

#if TARGET_OS_IPHONE == 1
#else
#import <AppKit/AppKit.h>
#endif

#include "cocoa_helpers.h"

#if TARGET_OS_IPHONE == 1
#else
#import "media/video/CPCVideoView.h"
#endif

@interface MyCPCVideoView : CPCVideoView

- (instancetype)retain OBJC_ARC_UNAVAILABLE;
- (oneway void)release OBJC_ARC_UNAVAILABLE;

@end

@implementation MyCPCVideoView

- (instancetype)retain;
{
   return [super retain];
}
- (oneway void)release;
{
   return [super release];
}

- (void)dealloc
{
   [self.rendererView removeFromSuperlayer];
   [super dealloc];
}

@end


namespace CPCAPI2
{
namespace test
{
TestCocoaApp::TestCocoaApp() : mPool(NULL)
{
}

TestCocoaApp::~TestCocoaApp()
{
   if (mPool)
   {
      NSAutoreleasePool* p = (NSAutoreleasePool*)mPool;
      [p release];
   }
}

void TestCocoaApp::startNSApplication()
{
    mPool = (void*)[[NSAutoreleasePool alloc] init];
}

void TestCocoaApp::run()
{
#if TARGET_OS_IPHONE == 1
#else
    NSApplication* app = [NSApplication sharedApplication];
    [app run];
#endif
}

void TestCocoaApp::stopNSApplication()
{
#if TARGET_OS_IPHONE == 1
#else
   dispatch_async(dispatch_get_main_queue(), ^
   {
      // we want to stop the main runloop, such that our main() function can resume control,
      // and return a return code indicating the presence of test failures.
      // per Apple documentation, NSApp:stop only triggers exit of the runloop after an event
      // is finished processing; so, send a dummy event
      [NSApp stop:nil];
      NSEvent* event = [NSEvent otherEventWithType: NSEventTypeApplicationDefined
                                          location: NSMakePoint(0,0)
                                     modifierFlags: 0
                                         timestamp: 0.0
                                      windowNumber: 0
                                           context: nil
                                           subtype: 0
                                             data1: 0
                                             data2: 0];
      [NSApp postEvent: event atStart: true];
   });
#endif
}

void TestCocoaApp::freePool(void* pool)
{
   NSAutoreleasePool* pl = (NSAutoreleasePool*)pool;
   [pl release];
}

   
WindowAndLayerHolder TestCocoaApp::createWindow(int xPos, int yPos,
                                int width, int height,
                                const char* className)
{
@autoreleasepool
{
    __block WindowAndLayerHolder holder;
#if TARGET_OS_IPHONE == 1
#else
    MyCPCVideoView* vidView = [MyCPCVideoView alloc];
    dispatch_sync(dispatch_get_main_queue(), ^{
       [vidView initWithFrame: NSMakeRect(xPos, yPos, width, height)];
       NSWindow* window = [[NSWindow alloc]
           initWithContentRect: NSMakeRect(xPos, yPos, width, height)
           styleMask: NSWindowStyleMaskTitled | NSWindowStyleMaskMiniaturizable
           backing: NSBackingStoreBuffered
           defer: NO];
       [window setReleasedWhenClosed:YES];
       [vidView setWantsLayer:YES];
       [window setContentView:vidView];
       [window setTitle: [NSString stringWithUTF8String:className]];
       //[window center];
       [window makeKeyAndOrderFront:nil];
       holder.window = (void*)[vidView window];
       holder.layer = (void*)[vidView rendererView];
    });
#endif
    return holder;
}
}

void TestCocoaApp::destroyWindow(void* vidView)
{
#if TARGET_OS_IPHONE == 1
#else
dispatch_async(dispatch_get_main_queue(), ^{
@autoreleasepool
{
    NSWindow* wind = (NSWindow*)vidView;
    CPCVideoView* v = (CPCVideoView*)wind.contentView;
    if (![v isMetalRenderView])
    {
      [wind.contentView release]; // balances alloc in TestCocoaApp::createWindow
      [wind setContentView:nil];
      [wind close]; // we have set setReleasedWhenClosed
   
      [CATransaction flush];
    }
}
});

#endif
}

long TestCocoaApp::secondsSinceLastRender(void* vidView)
{
#if TARGET_OS_IPHONE == 1
   // not supported
   return 0;
#else
   NSWindow* wind = (NSWindow*)vidView;
   CPCVideoView* v = (CPCVideoView*)wind.contentView;
   return [v timeSinceLastRender];
#endif
}

}
}
