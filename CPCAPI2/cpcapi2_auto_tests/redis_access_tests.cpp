#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#if (CPCAPI2_BRAND_ORCHESTRATION_SERVER_MODULE == 1)

#include "cpcapi2_test_fixture.h"
#include "test_call_events.h"
#include "test_events.h"
#include "test_account_events.h"

#include "../../impl/util/CurlPPHelper.h"
#include "../../impl/util/CurlPPProgress.h"
#include "../../impl/util/CurlPPSSL.h"
#include "../../impl/orchestration_server/OrchestrationServerRedisAccess.h"

using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::OrchestrationServer;

class RedisAccessTests : public CpcapiAutoTest
{
public:
   RedisAccessTests() {}
   virtual ~RedisAccessTests() {}
};

// needs a redis server
TEST_F(RedisAccessTests, DISABLED_AddServer) {

   RedisAccessConfig serverConfig;
   serverConfig.ip = "************"; // "**************";
   serverConfig.port = 6379;

   RedisAccess redisAccess;
   std::promise<void> initdP;
   std::future<void> initdF = initdP.get_future();
   int initRes = redisAccess.initialize(serverConfig, [&initdP](bool success, const cpc::string& msg) {
      ASSERT_TRUE(success);
      initdP.set_value();
   });
   ASSERT_EQ(initRes, 0);
   initdF.get();

   std::promise<RedisAccess::AddServerResult> serverIdP;
   std::future<RedisAccess::AddServerResult> serverIdRes = serverIdP.get_future();

   RedisAccess::AddServerArgs addServerArgs;
   addServerArgs.uri = "wss://xmppagent01.counterpath.net:9003";
   addServerArgs.region = "NorthAmerica";
   addServerArgs.services.push_back("xmpp");
   addServerArgs.services.push_back("xmppagent");
   redisAccess.addServer(addServerArgs, [&serverIdP](bool addServerSuccess, const RedisAccess::AddServerResult& asr) {
      ASSERT_TRUE(addServerSuccess);
      serverIdP.set_value(asr);
   });

   RedisAccess::AddServerResult asr = serverIdRes.get();
   ASSERT_EQ(asr.result, 0);

   std::promise<RedisAccess::QueryAvailableServersResult> queryAvailableRespP;
   std::future<RedisAccess::QueryAvailableServersResult> queryAvailableRespF = queryAvailableRespP.get_future();

   RedisAccess::QueryAvailableServersArgs queryAvailableServersArgs;
   queryAvailableServersArgs.service = "xmpp";
   queryAvailableServersArgs.region = "NorthAmerica";
   redisAccess.queryAvailableServers(queryAvailableServersArgs, [&queryAvailableRespP](bool queryAvailableServersSuccess, const RedisAccess::QueryAvailableServersResult& queryAvailResp) {
      ASSERT_TRUE(queryAvailableServersSuccess);
      queryAvailableRespP.set_value(queryAvailResp);
   });
   
   RedisAccess::QueryAvailableServersResult queryAvailableResp = queryAvailableRespF.get();
   ASSERT_GT(queryAvailableResp.availableServers.size(), 0);
   ASSERT_EQ(strcmp(queryAvailableResp.availableServers[0].uri.c_str(), "wss://xmppagent01.counterpath.net:9003"), 0);

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   redisAccess.shutdown();
}



#endif
