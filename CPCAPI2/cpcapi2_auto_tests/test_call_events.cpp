#include "test_call_events.h"
#include "impl/call/SipConversationManagerInternal.h"
#include "impl/ptt/PushToTalkTypesInternal.h"


using namespace CPCAPI2::test;
using namespace CPCAPI2::SipConversation;

void TestCallEvents::expectNewConversationIncomingTransfer(int line, TestAccount& account, 
      SipConversationHandle* handle, const cpc::string& fromUri,
      EVENT_VALIDATOR(NewConversationEvent) validator)
{
   NewConversationEvent evt;
   ASSERT_TRUE(account.conversationEvents->expectEvent(line, "SipConversationHandler::onNewConversation",
      30000, AlwaysTruePred(), *handle, evt));
   ASSERT_EQ(ConversationType_IncomingTransferRequest, evt.conversationType);
   ASSERT_EQ(fromUri, evt.remoteAddress);
   if (nullptr != validator)
   {
      validator(evt);
   }
}

bool verifyFromUri(const cpc::string& fromUri, const cpc::string& remoteAddress)
{
   std::string environmentId = TestEnvironmentConfig::testEnvironmentId().c_str();
   std::string remoteNumber = remoteAddress.c_str();
   std::string expectedNumber = fromUri.c_str();

   if (environmentId != "repro")
   {
      // Just comparing the numbers as a work around for the mismatch in the remote-uri taken from from-uri,
      // as the domain may not include the domain prefix or the region component of the uri or even the port
      // e.g. 
      // Expected: sip:<EMAIL>
      // Actual:   sip:<EMAIL>:5065

      std::size_t pos1 = remoteNumber.find_first_of("@");
      if (pos1 != std::string::npos)
      {
         remoteNumber = remoteNumber.substr(0, pos1);
      }
      std::size_t pos2 = expectedNumber.find_first_of("@");
      if (pos2 != std::string::npos)
      {
         expectedNumber = expectedNumber.substr(0, pos2);
      }
   }
   bool match = (expectedNumber.compare(remoteNumber) == 0);
   return match;
}

void TestCallEvents::expectNewConversationIncoming(int line, TestAccount& account, 
      SipConversationHandle* handle, const cpc::string& fromUri,
      EVENT_VALIDATOR(NewConversationEvent) validator, bool checkFromUri, int timeMs)
{
   NewConversationEvent evt;
   ASSERT_TRUE(account.conversationEvents->expectEvent(line, "SipConversationHandler::onNewConversation", timeMs, AlwaysTruePred(), *handle, evt)) << "missed inbound call event";
   ASSERT_EQ(ConversationType_Incoming, evt.conversationType) << "wrong conversation type, expecting conversation type Incoming";
   if (checkFromUri)
   {
      ASSERT_TRUE(verifyFromUri(fromUri, evt.remoteAddress)) << " mismatch: remote-id: " << evt.remoteAddress.c_str() << " expected id: " << fromUri.c_str();
   }

   ASSERT_FALSE(evt.isCodecsMismatched) << "Codec Mismatched";
   if (nullptr != validator)
   {
      validator(evt);
   }
}

void TestCallEvents::expectNewConversationIncoming_answermode(int line, TestAccount& account,
      SipConversationHandle* handle, const cpc::string& fromUri, AnswerModeSettings& answerMode,
      EVENT_VALIDATOR(NewConversationEvent) validator, bool checkFromUri)
{
   NewConversationEvent evt;
   ASSERT_TRUE(account.conversationEvents->expectEvent(line, "SipConversationHandler::onNewConversation", 30000, AlwaysTruePred(), *handle, evt)) << "missed inbound call event";
   ASSERT_EQ(ConversationType_Incoming, evt.conversationType) << "wrong conversation type, expecting conversation type Incoming";
   if (checkFromUri)
   {
      ASSERT_TRUE(verifyFromUri(fromUri, evt.remoteAddress)) << " mismatch: remote-id: " << evt.remoteAddress.c_str() << " expected id: " << fromUri.c_str();
   }

   ASSERT_FALSE(evt.isCodecsMismatched) << "Codec Mismatched";
   ASSERT_EQ(answerMode.mode, evt.answerMode.mode);
   ASSERT_EQ(answerMode.privileged, evt.answerMode.privileged);
   ASSERT_EQ(answerMode.required, evt.answerMode.required);
   if (nullptr != validator)
   {
      validator(evt);
   }
}

void TestCallEvents::expectNewConversationIncoming_crypto(
   int line,
   TestAccount& account,
   SipConversationHandle* handle,
   const cpc::string& fromUri,
   const CPCAPI2::SipConversation::MediaInfo& audio,
   const CPCAPI2::SipConversation::MediaInfo& video,
   EVENT_VALIDATOR(NewConversationEvent) validator)
{
   NewConversationEvent evt;
   ASSERT_TRUE(account.conversationEvents->expectEvent(line, "SipConversationHandler::onNewConversation", 30000, AlwaysTruePred(), *handle, evt));
   ASSERT_EQ(ConversationType_Incoming, evt.conversationType);
   ASSERT_TRUE(verifyFromUri(fromUri, evt.remoteAddress)) << " mismatch: remote-id: " << evt.remoteAddress.c_str() << " expected id: " << fromUri.c_str();
   ASSERT_FALSE(evt.isCodecsMismatched);

   ASSERT_EQ(evt.remoteMediaInfo.size(), 2);

   ASSERT_EQ(audio.mediaDirection, evt.remoteMediaInfo[0].mediaDirection);
   ASSERT_EQ(audio.mediaType, evt.remoteMediaInfo[0].mediaType);
   ASSERT_EQ(audio.mediaCrypto, MediaCryptoSuite_None);
   ASSERT_EQ(audio.mediaEncryptionOptions.secureMediaRequired, evt.remoteMediaInfo[0].mediaEncryptionOptions.secureMediaRequired);

   if (audio.mediaEncryptionOptions.mediaEncryptionMode == CPCAPI2::SipConversation::MediaEncryptionMode_Unencrypted)
   {
      ASSERT_EQ(CPCAPI2::SipConversation::MediaEncryptionMode_Unencrypted, evt.remoteMediaInfo[0].mediaEncryptionOptions.mediaEncryptionMode);
      ASSERT_EQ(0, evt.remoteMediaInfo[0].mediaEncryptionOptions.mediaCryptoSuites.size());
   }
   else
   {
      ASSERT_EQ(audio.mediaEncryptionOptions.mediaEncryptionMode, evt.remoteMediaInfo[0].mediaEncryptionOptions.mediaEncryptionMode);
      ASSERT_EQ(audio.mediaEncryptionOptions.mediaCryptoSuites.size(), evt.remoteMediaInfo[0].mediaEncryptionOptions.mediaCryptoSuites.size());

      int cryptoCount = 0;
      for (cpc::vector<MediaCryptoSuite>::const_iterator i = audio.mediaEncryptionOptions.mediaCryptoSuites.begin(); i != audio.mediaEncryptionOptions.mediaCryptoSuites.end(); i++, cryptoCount++)
      {
         ASSERT_EQ((*i), evt.remoteMediaInfo[0].mediaEncryptionOptions.mediaCryptoSuites[cryptoCount]);
      }
   }
   
   ASSERT_EQ(video.mediaDirection, evt.remoteMediaInfo[1].mediaDirection);
   ASSERT_EQ(video.mediaType, evt.remoteMediaInfo[1].mediaType);
   ASSERT_EQ(video.mediaCrypto, MediaCryptoSuite_None);
   ASSERT_EQ(video.mediaEncryptionOptions.secureMediaRequired, evt.remoteMediaInfo[1].mediaEncryptionOptions.secureMediaRequired);
   
   if (video.mediaEncryptionOptions.mediaEncryptionMode == CPCAPI2::SipConversation::MediaEncryptionMode_Unencrypted)
   {
      ASSERT_EQ(CPCAPI2::SipConversation::MediaEncryptionMode_Unencrypted, evt.remoteMediaInfo[1].mediaEncryptionOptions.mediaEncryptionMode);
      ASSERT_EQ(0, evt.remoteMediaInfo[1].mediaEncryptionOptions.mediaCryptoSuites.size());
   }
   else
   {
      ASSERT_EQ(video.mediaEncryptionOptions.mediaEncryptionMode, evt.remoteMediaInfo[1].mediaEncryptionOptions.mediaEncryptionMode);
      ASSERT_EQ(video.mediaEncryptionOptions.mediaCryptoSuites.size(), evt.remoteMediaInfo[1].mediaEncryptionOptions.mediaCryptoSuites.size());

      int cryptoCount = 0;
      for (cpc::vector<MediaCryptoSuite>::const_iterator i = video.mediaEncryptionOptions.mediaCryptoSuites.begin(); i != video.mediaEncryptionOptions.mediaCryptoSuites.end(); i++, cryptoCount++)
      {
         ASSERT_EQ((*i), evt.remoteMediaInfo[1].mediaEncryptionOptions.mediaCryptoSuites[cryptoCount]);
      }
   }

   if (nullptr != validator)
   {
      validator(evt);
   }
}

void TestCallEvents::expectNewConversationIncomingAudio_crypto(
   int line,
   TestAccount& account,
   SipConversationHandle* handle,
   const cpc::string& fromUri,
   const CPCAPI2::SipConversation::MediaInfo& audio,
   EVENT_VALIDATOR(NewConversationEvent) validator, bool checkFromUri)
{
   NewConversationEvent evt;
   ASSERT_TRUE(account.conversationEvents->expectEvent(line, "SipConversationHandler::onNewConversation", 30000, AlwaysTruePred(), *handle, evt));
   ASSERT_EQ(ConversationType_Incoming, evt.conversationType) << "wrong conversation type, expecting conversation type Incoming";
   if (checkFromUri)
   {
      ASSERT_TRUE(verifyFromUri(fromUri, evt.remoteAddress)) << " mismatch: remote-id: " << evt.remoteAddress.c_str() << " expected id: " << fromUri.c_str();
   }

   ASSERT_FALSE(evt.isCodecsMismatched) << "Codec Mismatched";

   ASSERT_EQ(evt.remoteMediaInfo.size(), 1);

   ASSERT_EQ(audio.mediaDirection, evt.remoteMediaInfo[0].mediaDirection);
   ASSERT_EQ(audio.mediaType, evt.remoteMediaInfo[0].mediaType);
   ASSERT_EQ(audio.mediaCrypto, MediaCryptoSuite_None);
   ASSERT_EQ(audio.mediaEncryptionOptions.secureMediaRequired, evt.remoteMediaInfo[0].mediaEncryptionOptions.secureMediaRequired);

   if (audio.mediaEncryptionOptions.mediaEncryptionMode == CPCAPI2::SipConversation::MediaEncryptionMode_Unencrypted)
   {
      ASSERT_EQ(CPCAPI2::SipConversation::MediaEncryptionMode_Unencrypted, evt.remoteMediaInfo[0].mediaEncryptionOptions.mediaEncryptionMode) << "Incoming INVITE SDP asked for SRTP, but we did not expect SRTP";
      ASSERT_EQ(0, evt.remoteMediaInfo[0].mediaEncryptionOptions.mediaCryptoSuites.size()) << "Incoming INVITE SDP asked for SRTP, but we did not expect SRTP";
   }
   else
   {
      ASSERT_EQ(audio.mediaEncryptionOptions.mediaEncryptionMode, evt.remoteMediaInfo[0].mediaEncryptionOptions.mediaEncryptionMode) << "Incoming INVITE SDP had different crypto mode than expected";
      ASSERT_EQ(audio.mediaEncryptionOptions.mediaCryptoSuites.size(), evt.remoteMediaInfo[0].mediaEncryptionOptions.mediaCryptoSuites.size()) << "Incoming INVITE SDP had different crypto mode count than expected";

      int cryptoCount = 0;
      for (cpc::vector<MediaCryptoSuite>::const_iterator i = audio.mediaEncryptionOptions.mediaCryptoSuites.begin(); i != audio.mediaEncryptionOptions.mediaCryptoSuites.end(); i++, cryptoCount++)
      {
         ASSERT_EQ((*i), evt.remoteMediaInfo[0].mediaEncryptionOptions.mediaCryptoSuites[cryptoCount]) << "Incoming INVITE SDP had unexpected crypto type";
      }
   }

   if (nullptr != validator)
   {
      validator(evt);
   }
}

void TestCallEvents::expectNewConversationOutgoing(int line, TestAccount& account, 
      SipConversationHandle handle, const cpc::string& toUri,
      EVENT_VALIDATOR(NewConversationEvent) validator)
{
   SipConversationHandle h;
   NewConversationEvent evt;
   ASSERT_TRUE(account.conversationEvents->expectEvent(line, "SipConversationHandler::onNewConversation",
      15000, AlwaysTruePred(), h, evt)) << "missed outgoing call event";
   ASSERT_EQ(handle, h) << "Conversation handle does not match";
   ASSERT_EQ(ConversationType_Outgoing, evt.conversationType) << "Conversation type does not match with outgoing";
   ASSERT_EQ(toUri, evt.remoteAddress) << "Remote address does not match";
   if (nullptr != validator)
   {
      validator(evt);
   }
}

void TestCallEvents::expectNewConversationOutgoing_time(
   int line,
   TestAccount& account,
   CPCAPI2::SipConversation::SipConversationHandle handle,
   const cpc::string& toUri,
   EVENT_VALIDATOR(CPCAPI2::SipConversation::NewConversationEvent) validator,
   unsigned int timeMS)
{
   SipConversationHandle h;
   NewConversationEvent evt;
   ASSERT_TRUE(account.conversationEvents->expectEvent(line, "SipConversationHandler::onNewConversation",
      timeMS, AlwaysTruePred(), h, evt));
   ASSERT_EQ(handle, h);
   ASSERT_EQ(ConversationType_Outgoing, evt.conversationType);
   ASSERT_EQ(toUri, evt.remoteAddress);
   if (nullptr != validator)
   {
      validator(evt);
   }
}

void TestCallEvents::expectNewConversationOutgoing_crypto(
   int line,
   TestAccount& account,
   CPCAPI2::SipConversation::SipConversationHandle handle,
   const cpc::string& toUri,
   const CPCAPI2::SipConversation::MediaInfo& audio,
   const CPCAPI2::SipConversation::MediaInfo& video,
   EVENT_VALIDATOR(CPCAPI2::SipConversation::NewConversationEvent) validator)
{
   SipConversationHandle h;
   NewConversationEvent evt;
   ASSERT_TRUE(account.conversationEvents->expectEvent(line, "SipConversationHandler::onNewConversation", 15000, AlwaysTruePred(), h, evt));
   ASSERT_EQ(handle, h);
   ASSERT_EQ(ConversationType_Outgoing, evt.conversationType);
   ASSERT_EQ(toUri, evt.remoteAddress);
   
   ASSERT_EQ(2, evt.localMediaInfo.size());
      
   ASSERT_EQ(audio.mediaDirection, evt.localMediaInfo[0].mediaDirection);
   ASSERT_EQ(audio.mediaType, evt.localMediaInfo[0].mediaType);
   ASSERT_EQ(audio.mediaCrypto, MediaCryptoSuite_None);
   ASSERT_EQ(audio.mediaEncryptionOptions.mediaEncryptionMode, evt.localMediaInfo[0].mediaEncryptionOptions.mediaEncryptionMode);
   ASSERT_EQ(audio.mediaEncryptionOptions.secureMediaRequired, evt.localMediaInfo[0].mediaEncryptionOptions.secureMediaRequired);
   ASSERT_EQ(audio.mediaEncryptionOptions.mediaCryptoSuites.size(), evt.localMediaInfo[0].mediaEncryptionOptions.mediaCryptoSuites.size());
      
   // TODO: Should the incoming list also be in the order of priority of the cryptos as originaly configured, currently it is reversed
   int cryptoCount = 0;
   for (cpc::vector<MediaCryptoSuite>::const_iterator i = audio.mediaEncryptionOptions.mediaCryptoSuites.begin(); i != audio.mediaEncryptionOptions.mediaCryptoSuites.end(); i++, cryptoCount++)
   {
      ASSERT_EQ(evt.localMediaInfo[0].mediaEncryptionOptions.mediaCryptoSuites[cryptoCount], (*i));
   }
   
   ASSERT_EQ(video.mediaDirection, evt.localMediaInfo[1].mediaDirection);
   ASSERT_EQ(video.mediaType, evt.localMediaInfo[1].mediaType);
   ASSERT_EQ(video.mediaCrypto, MediaCryptoSuite_None);
   ASSERT_EQ(video.mediaEncryptionOptions.mediaEncryptionMode, evt.localMediaInfo[1].mediaEncryptionOptions.mediaEncryptionMode);
   ASSERT_EQ(video.mediaEncryptionOptions.secureMediaRequired, evt.localMediaInfo[1].mediaEncryptionOptions.secureMediaRequired);
   ASSERT_EQ(video.mediaEncryptionOptions.mediaCryptoSuites.size(), evt.localMediaInfo[1].mediaEncryptionOptions.mediaCryptoSuites.size());

   cryptoCount = 0;
   for (cpc::vector<MediaCryptoSuite>::const_iterator i = video.mediaEncryptionOptions.mediaCryptoSuites.begin(); i != video.mediaEncryptionOptions.mediaCryptoSuites.end(); i++, cryptoCount++)
   {
      ASSERT_EQ((*i), evt.localMediaInfo[1].mediaEncryptionOptions.mediaCryptoSuites[cryptoCount]);
   }
   
   if (nullptr != validator)
   {
      validator(evt);
   }
}

void TestCallEvents::expectNewConversationOutgoingAudio_crypto(
   int line,
   TestAccount& account,
   CPCAPI2::SipConversation::SipConversationHandle handle,
   const cpc::string& toUri,
   const CPCAPI2::SipConversation::MediaInfo& audio,
   EVENT_VALIDATOR(CPCAPI2::SipConversation::NewConversationEvent) validator)
{
   SipConversationHandle h;
   NewConversationEvent evt;
   ASSERT_TRUE(account.conversationEvents->expectEvent(line, "SipConversationHandler::onNewConversation", 15000, AlwaysTruePred(), h, evt));
   ASSERT_EQ(handle, h);
   ASSERT_EQ(ConversationType_Outgoing, evt.conversationType);
   ASSERT_EQ(toUri, evt.remoteAddress);
   
   ASSERT_EQ(1, evt.localMediaInfo.size());
   
   ASSERT_EQ(audio.mediaDirection, evt.localMediaInfo[0].mediaDirection);
   ASSERT_EQ(audio.mediaType, evt.localMediaInfo[0].mediaType);
   ASSERT_EQ(audio.mediaCrypto, MediaCryptoSuite_None);
   ASSERT_EQ(audio.mediaEncryptionOptions.mediaEncryptionMode, evt.localMediaInfo[0].mediaEncryptionOptions.mediaEncryptionMode);
   ASSERT_EQ(audio.mediaEncryptionOptions.secureMediaRequired, evt.localMediaInfo[0].mediaEncryptionOptions.secureMediaRequired);
   ASSERT_EQ(audio.mediaEncryptionOptions.mediaCryptoSuites.size(), evt.localMediaInfo[0].mediaEncryptionOptions.mediaCryptoSuites.size());

   int cryptoCount = 0;
   for (cpc::vector<MediaCryptoSuite>::const_iterator i = audio.mediaEncryptionOptions.mediaCryptoSuites.begin(); i != audio.mediaEncryptionOptions.mediaCryptoSuites.end(); i++, cryptoCount++)
   {
      ASSERT_EQ(evt.localMediaInfo[0].mediaEncryptionOptions.mediaCryptoSuites[cryptoCount], (*i));
   }
   
   if (nullptr != validator)
   {
      validator(evt);
   }
}

void TestCallEvents::expectNewConversationOutgoing_answermode(int line, TestAccount& account,
      SipConversationHandle handle, const cpc::string& toUri, AnswerModeSettings& answerMode,
      EVENT_VALIDATOR(NewConversationEvent) validator)
{
   SipConversationHandle h;
   NewConversationEvent evt;
   ASSERT_TRUE(account.conversationEvents->expectEvent(line, "SipConversationHandler::onNewConversation",
      15000, AlwaysTruePred(), h, evt)) << "missed outgoing call event";
   ASSERT_EQ(handle, h) << "Conversation handle does not match";
   ASSERT_EQ(ConversationType_Outgoing, evt.conversationType) << "Conversation type does not match with outgoing";
   ASSERT_EQ(toUri, evt.remoteAddress) << "Remote address does not match";
   ASSERT_EQ(answerMode.mode, evt.answerMode.mode);
   ASSERT_EQ(answerMode.privileged, evt.answerMode.privileged);
   ASSERT_EQ(answerMode.required, evt.answerMode.required);
   if (nullptr != validator)
   {
      validator(evt);
   }
}

void TestCallEvents::expectConversationMediaChanged(int line, TestAccount& account, 
      SipConversationHandle handle, 
      MediaDirection direction)
{
   SipConversationHandle h;
   ConversationMediaChangedEvent evt;
   ASSERT_TRUE(account.conversationEvents->expectEvent(line, "SipConversationHandler::onConversationMediaChanged",
      15000, HandleEqualsPred<SipConversationHandle>(handle), h, evt)) << "missed conversation state changed event";
   ASSERT_LE((size_t)1, evt.localMediaInfo.size()) << "local media info size cannot be empty" ;
   for(cpc::vector<MediaInfo>::const_iterator it=evt.localMediaInfo.begin(); it != evt.localMediaInfo.end(); it++)
   {
      ASSERT_EQ(direction, it->mediaDirection) <<"Media Direction is wrong";
   }
}

void TestCallEvents::expectConversationMediaChanged_time(int line, TestAccount& account, 
      SipConversationHandle handle, 
      MediaDirection direction,
      unsigned int timeMS)
{
   SipConversationHandle h;
   ConversationMediaChangedEvent evt;
   ASSERT_TRUE(account.conversationEvents->expectEvent(line, "SipConversationHandler::onConversationMediaChanged",
      timeMS, HandleEqualsPred<SipConversationHandle>(handle), h, evt)) << "missed conversation state changed event";
   ASSERT_LE((size_t)1, evt.localMediaInfo.size()) << "local media info size cannot be empty" ;
   for(cpc::vector<MediaInfo>::const_iterator it=evt.localMediaInfo.begin(); it != evt.localMediaInfo.end(); it++)
   {
      ASSERT_EQ(direction, it->mediaDirection) <<"Media Direction is wrong";
   }
}

void TestCallEvents::expectConversationMediaChanged_ex(int line, TestAccount& account, 
      SipConversationHandle handle, 
      EVENT_VALIDATOR(ConversationMediaChangedEvent) validator)
{
   SipConversationHandle h;
   ConversationMediaChangedEvent evt;
   ASSERT_TRUE(account.conversationEvents->expectEvent(line, "SipConversationHandler::onConversationMediaChanged",
      15000, HandleEqualsPred<SipConversationHandle>(handle), h, evt));
   validator(evt);
}

void TestCallEvents::expectConversationMediaChanged_ex_time(int line, TestAccount& account,
   CPCAPI2::SipConversation::SipConversationHandle handle,
   EVENT_VALIDATOR(CPCAPI2::SipConversation::ConversationMediaChangedEvent) validator,
   unsigned int timeMS)
{
   SipConversationHandle h;
   ConversationMediaChangedEvent evt;
   ASSERT_TRUE(account.conversationEvents->expectEvent(line, "SipConversationHandler::onConversationMediaChanged",
      timeMS, HandleEqualsPred<SipConversationHandle>(handle), h, evt));
   validator(evt);
}

void TestCallEvents::expectConversationMediaChanged_crypto(
   int line,
   TestAccount& account,
   CPCAPI2::SipConversation::SipConversationHandle handle,
   CPCAPI2::SipConversation::MediaCryptoSuite audioCrypto,
   CPCAPI2::SipConversation::MediaCryptoSuite videoCrypto,
   const CPCAPI2::SipConversation::MediaInfo& localAudio,
   const CPCAPI2::SipConversation::MediaInfo& localVideo,
   const CPCAPI2::SipConversation::MediaInfo& remoteAudio,
   const CPCAPI2::SipConversation::MediaInfo& remoteVideo,
   EVENT_VALIDATOR(CPCAPI2::SipConversation::ConversationMediaChangedEvent) validator)
{
   SipConversationHandle h;
   ConversationMediaChangedEvent evt;
   ASSERT_TRUE(account.conversationEvents->expectEvent(line, "SipConversationHandler::onConversationMediaChanged", 15000, HandleEqualsPred<SipConversationHandle>(handle), h, evt));

   ASSERT_EQ(MediaType_Audio, localAudio.mediaType);
   ASSERT_EQ(MediaType_Audio, remoteAudio.mediaType);
   ASSERT_EQ(MediaType_Video, localVideo.mediaType);
   ASSERT_EQ(MediaType_Video, localVideo.mediaType);
   
   int audioMediaIndex = -1;
   int videoMediaIndex = -1;
   if ((videoCrypto == MediaCryptoSuite_None) && (audioCrypto == MediaCryptoSuite_None))
   {
      ASSERT_EQ(0, evt.localMediaInfo.size());
      ASSERT_EQ(0, evt.remoteMediaInfo.size());
   }
   else if (videoCrypto == MediaCryptoSuite_None)
   {
      // TODO: Local media could be 1 or 2, if secured video is disabled on remote end, may want to have media size passed in
      // ASSERT_EQ(1, evt.localMediaInfo.size());
      ASSERT_GE(evt.localMediaInfo.size(), 1);
      ASSERT_GE(evt.remoteMediaInfo.size(), 1);
      audioMediaIndex = 0;
   }
   else if (audioCrypto == MediaCryptoSuite_None)
   {
      ASSERT_GE(evt.localMediaInfo.size(), 1);
      ASSERT_GE(evt.remoteMediaInfo.size(), 1);
      videoMediaIndex = 0;
   }
   else
   {
      ASSERT_EQ(2, evt.localMediaInfo.size());
      ASSERT_EQ(2, evt.remoteMediaInfo.size());
      audioMediaIndex = 0;
      videoMediaIndex = 1;
   }
    
   compareMediaInfoInConversationMediaChanged_crypto(audioMediaIndex, audioCrypto, localAudio, remoteAudio, evt);
   compareMediaInfoInConversationMediaChanged_crypto(videoMediaIndex, videoCrypto, localVideo, remoteVideo, evt);
   
   validator(evt);
}

void TestCallEvents::compareMediaInfoInConversationMediaChanged_crypto(
   int mediaIndex,
   CPCAPI2::SipConversation::MediaCryptoSuite crypto,
   const CPCAPI2::SipConversation::MediaInfo& localMedia,
   const CPCAPI2::SipConversation::MediaInfo& remoteMedia,
   CPCAPI2::SipConversation::ConversationMediaChangedEvent& evt)
{
   // TODO: remove this check, pass in param instead
   // TODO: perform applicable media checks regardless of crypto
   if (crypto != MediaCryptoSuite_None)
   {
      ASSERT_EQ(localMedia.mediaDirection, evt.localMediaInfo[mediaIndex].mediaDirection);
      ASSERT_EQ(localMedia.mediaType, evt.localMediaInfo[mediaIndex].mediaType);
      ASSERT_EQ(crypto, evt.localMediaInfo[mediaIndex].mediaCrypto);
      ASSERT_EQ(localMedia.mediaEncryptionOptions.mediaEncryptionMode, evt.localMediaInfo[mediaIndex].mediaEncryptionOptions.mediaEncryptionMode);
      // TODO: Secure media required setting in the callback returns based on what the encyption mode is, rather than what was configured
      // ASSERT_EQ(localVideo.mediaEncryptionOptions.secureMediaRequired, evt.localMediaInfo[videoMediaIndex].mediaEncryptionOptions.secureMediaRequired);
      if (localMedia.mediaEncryptionOptions.mediaEncryptionMode == MediaEncryptionMode_Unencrypted)
      {
         ASSERT_FALSE(evt.localMediaInfo[mediaIndex].mediaEncryptionOptions.secureMediaRequired);
      }
      else
      {
         ASSERT_TRUE(evt.localMediaInfo[mediaIndex].mediaEncryptionOptions.secureMediaRequired);
      }
      ASSERT_EQ(localMedia.mediaEncryptionOptions.mediaCryptoSuites.size(), evt.localMediaInfo[mediaIndex].mediaEncryptionOptions.mediaCryptoSuites.size());
      int cryptoCount = 0;
      for (cpc::vector<MediaCryptoSuite>::const_iterator i = localMedia.mediaEncryptionOptions.mediaCryptoSuites.begin(); i != localMedia.mediaEncryptionOptions.mediaCryptoSuites.end(); i++, cryptoCount++)
      {
         ASSERT_EQ((*i), evt.localMediaInfo[mediaIndex].mediaEncryptionOptions.mediaCryptoSuites[cryptoCount]);
      }
      
      ASSERT_EQ(remoteMedia.mediaDirection, evt.remoteMediaInfo[mediaIndex].mediaDirection);
      ASSERT_EQ(remoteMedia.mediaType, evt.remoteMediaInfo[mediaIndex].mediaType);
      ASSERT_EQ(crypto, evt.remoteMediaInfo[mediaIndex].mediaCrypto);
      if (localMedia.mediaEncryptionOptions.mediaEncryptionMode == CPCAPI2::SipConversation::MediaEncryptionMode_Unencrypted)
      {
         ASSERT_EQ(CPCAPI2::SipConversation::MediaEncryptionMode_Unencrypted, evt.remoteMediaInfo[mediaIndex].mediaEncryptionOptions.mediaEncryptionMode);
      }
      else
      {
         ASSERT_EQ(remoteMedia.mediaEncryptionOptions.mediaEncryptionMode, evt.remoteMediaInfo[mediaIndex].mediaEncryptionOptions.mediaEncryptionMode);
      }
      
      // TODO: Secure media required setting in the callback returns based on what the encyption mode is, rather than what was configured
      // ASSERT_EQ(remoteVideo.mediaEncryptionOptions.secureMediaRequired, evt.remoteMediaInfo[videoMediaIndex].mediaEncryptionOptions.secureMediaRequired);
      if (remoteMedia.mediaEncryptionOptions.mediaEncryptionMode == MediaEncryptionMode_Unencrypted)
      {
         ASSERT_FALSE(evt.remoteMediaInfo[mediaIndex].mediaEncryptionOptions.secureMediaRequired);
      }
      else
      {
         ASSERT_TRUE(evt.remoteMediaInfo[mediaIndex].mediaEncryptionOptions.secureMediaRequired);
      }
      
      if (crypto == CPCAPI2::SipConversation::MediaCryptoSuite_None)
      {
         ASSERT_EQ(0, evt.remoteMediaInfo[mediaIndex].mediaEncryptionOptions.mediaCryptoSuites.size());
      }
      else
      {
         ASSERT_EQ(1, evt.remoteMediaInfo[mediaIndex].mediaEncryptionOptions.mediaCryptoSuites.size());
         ASSERT_EQ(crypto, evt.remoteMediaInfo[mediaIndex].mediaEncryptionOptions.mediaCryptoSuites[0]);
      }
   }
}
                                                       
void TestCallEvents::expectConversationMediaChangedAudio_crypto(
   int line,
   TestAccount& account,
   CPCAPI2::SipConversation::SipConversationHandle handle,
   CPCAPI2::SipConversation::MediaCryptoSuite audioCrypto,
   const CPCAPI2::SipConversation::MediaInfo& localAudio,
   const CPCAPI2::SipConversation::MediaInfo& remoteAudio,
   EVENT_VALIDATOR(CPCAPI2::SipConversation::ConversationMediaChangedEvent) validator)
{
   SipConversationHandle h;
   ConversationMediaChangedEvent evt;
   ASSERT_TRUE(account.conversationEvents->expectEvent(line, "SipConversationHandler::onConversationMediaChanged", 15000, HandleEqualsPred<SipConversationHandle>(handle), h, evt));

   if (audioCrypto != MediaCryptoSuite_None)
   {
      ASSERT_EQ(1, evt.localMediaInfo.size());
      ASSERT_EQ(localAudio.mediaDirection, evt.localMediaInfo[0].mediaDirection);
      ASSERT_EQ(localAudio.mediaType, evt.localMediaInfo[0].mediaType);
      ASSERT_EQ(audioCrypto, evt.localMediaInfo[0].mediaCrypto);
      // TODO: Mode returned is original configuration, should it not be the mode as negotiated with remote party, e.g. if remote party had encryption disabled, the local media encyption state is Unencrypted, even though configured Enecrypted
      ASSERT_EQ(localAudio.mediaEncryptionOptions.mediaEncryptionMode, evt.localMediaInfo[0].mediaEncryptionOptions.mediaEncryptionMode);
      // TODO: Secure media required setting in the callback returns based on what the encyption mode is, rather than what was configured
      // ASSERT_EQ(localAudio.mediaEncryptionOptions.secureMediaRequired, evt.localMediaInfo[0].mediaEncryptionOptions.secureMediaRequired);
      if (localAudio.mediaEncryptionOptions.mediaEncryptionMode == MediaEncryptionMode_Unencrypted)
      {
         ASSERT_FALSE(evt.localMediaInfo[0].mediaEncryptionOptions.secureMediaRequired);
      }
      else
      {
         ASSERT_TRUE(evt.localMediaInfo[0].mediaEncryptionOptions.secureMediaRequired);
      }
      ASSERT_EQ(localAudio.mediaEncryptionOptions.mediaCryptoSuites.size(), evt.localMediaInfo[0].mediaEncryptionOptions.mediaCryptoSuites.size());
      int cryptoCount = 0;
      for (cpc::vector<MediaCryptoSuite>::const_iterator i = localAudio.mediaEncryptionOptions.mediaCryptoSuites.begin(); i != localAudio.mediaEncryptionOptions.mediaCryptoSuites.end(); i++, cryptoCount++)
      {
         ASSERT_EQ((*i), evt.localMediaInfo[0].mediaEncryptionOptions.mediaCryptoSuites[cryptoCount]);
      }
      
      ASSERT_EQ(remoteAudio.mediaDirection, evt.remoteMediaInfo[0].mediaDirection);
      ASSERT_EQ(remoteAudio.mediaType, evt.remoteMediaInfo[0].mediaType);
      ASSERT_EQ(audioCrypto, evt.remoteMediaInfo[0].mediaCrypto);
      if (localAudio.mediaEncryptionOptions.mediaEncryptionMode == CPCAPI2::SipConversation::MediaEncryptionMode_Unencrypted)
      {
         ASSERT_EQ(CPCAPI2::SipConversation::MediaEncryptionMode_Unencrypted, evt.remoteMediaInfo[0].mediaEncryptionOptions.mediaEncryptionMode);
      }
      else
      {
         ASSERT_EQ(remoteAudio.mediaEncryptionOptions.mediaEncryptionMode, evt.remoteMediaInfo[0].mediaEncryptionOptions.mediaEncryptionMode);
      }
      
      // TODO: Secure media required setting in the callback returns based on what the encyption mode is, rather than what was configured
      // ASSERT_EQ(remoteAudio.mediaEncryptionOptions.secureMediaRequired, evt.remoteMediaInfo[0].mediaEncryptionOptions.secureMediaRequired);
      if (remoteAudio.mediaEncryptionOptions.mediaEncryptionMode == MediaEncryptionMode_Unencrypted)
      {
         ASSERT_FALSE(evt.remoteMediaInfo[0].mediaEncryptionOptions.secureMediaRequired);
      }
      else
      {
         ASSERT_TRUE(evt.remoteMediaInfo[0].mediaEncryptionOptions.secureMediaRequired);
      }
      
      if (audioCrypto == CPCAPI2::SipConversation::MediaCryptoSuite_None)
      {
         ASSERT_EQ(0, evt.remoteMediaInfo[0].mediaEncryptionOptions.mediaCryptoSuites.size());
      }
      else
      {
         ASSERT_EQ(1, evt.remoteMediaInfo[0].mediaEncryptionOptions.mediaCryptoSuites.size());
         ASSERT_EQ(audioCrypto, evt.remoteMediaInfo[0].mediaEncryptionOptions.mediaCryptoSuites[0]);
      }
   }
   
   validator(evt);
}

void TestCallEvents::expectConversationMediaChangeRequest(int line, TestAccount& account, 
      SipConversationHandle handle,
      MediaDirection direction,
      int timeoutMs)
{
   SipConversationHandle h;
   ConversationMediaChangeRequestEvent evt;
   ASSERT_TRUE(account.conversationEvents->expectEvent(line, "SipConversationHandler::onConversationMediaChangeRequest",
      timeoutMs, HandleEqualsPred<SipConversationHandle>(handle), h, evt));
   
   ASSERT_LE((size_t)1, evt.remoteMediaInfo.size());
   for(cpc::vector<MediaInfo>::const_iterator it=evt.remoteMediaInfo.begin(); it != evt.remoteMediaInfo.end(); it++)
   {
      ASSERT_EQ(direction, it->mediaDirection);
   }
}
      

void TestCallEvents::expectConversationMediaChangeRequest_ex(int line, TestAccount& account, 
      SipConversationHandle handle,
      EVENT_VALIDATOR(ConversationMediaChangeRequestEvent) validator)
{
   SipConversationHandle h;
   ConversationMediaChangeRequestEvent evt;
   ASSERT_TRUE(account.conversationEvents->expectEvent(line, "SipConversationHandler::onConversationMediaChangeRequest",
      15000, HandleEqualsPred<SipConversationHandle>(handle), h, evt));
   validator(evt);
}

void TestCallEvents::expectConversationMediaChangeRequestAudio_crypto(
   int line,
   TestAccount& account,
   CPCAPI2::SipConversation::SipConversationHandle handle,
   const CPCAPI2::SipConversation::MediaInfo& remoteAudio,
   EVENT_VALIDATOR(CPCAPI2::SipConversation::ConversationMediaChangeRequestEvent) validator)
{
   SipConversationHandle h;
   ConversationMediaChangeRequestEvent evt;
   ASSERT_TRUE(account.conversationEvents->expectEvent(line, "SipConversationHandler::onConversationMediaChangeRequest", 15000, HandleEqualsPred<SipConversationHandle>(handle), h, evt));

   ASSERT_EQ(1, evt.remoteMediaInfo.size());
   
   compareMediaInfoInConversationMediaChangeRequest_crypto(0, remoteAudio, evt);
   validator(evt);
}

void TestCallEvents::expectConversationMediaChangeRequest_crypto(
   int line,
   TestAccount& account,
   CPCAPI2::SipConversation::SipConversationHandle handle,
   const CPCAPI2::SipConversation::MediaInfo& remoteAudio,
   const CPCAPI2::SipConversation::MediaInfo& remoteVideo,
   EVENT_VALIDATOR(CPCAPI2::SipConversation::ConversationMediaChangeRequestEvent) validator)
{
   SipConversationHandle h;
   ConversationMediaChangeRequestEvent evt;
   ASSERT_TRUE(account.conversationEvents->expectEvent(line, "SipConversationHandler::onConversationMediaChangeRequest", 15000, HandleEqualsPred<SipConversationHandle>(handle), h, evt));

   ASSERT_EQ(2, evt.remoteMediaInfo.size());
   
   compareMediaInfoInConversationMediaChangeRequest_crypto(0, remoteAudio, evt);
   compareMediaInfoInConversationMediaChangeRequest_crypto(1, remoteVideo, evt);
   validator(evt);
}

void TestCallEvents::compareMediaInfoInConversationMediaChangeRequest_crypto(
   int mediaIndex,
   const CPCAPI2::SipConversation::MediaInfo& remoteMedia,
   CPCAPI2::SipConversation::ConversationMediaChangeRequestEvent& evt)
{
   ASSERT_EQ(remoteMedia.mediaType, evt.remoteMediaInfo[mediaIndex].mediaType);
   ASSERT_EQ(remoteMedia.mediaDirection, evt.remoteMediaInfo[mediaIndex].mediaDirection);
   ASSERT_EQ(remoteMedia.mediaEncryptionOptions.mediaEncryptionMode, evt.remoteMediaInfo[mediaIndex].mediaEncryptionOptions.mediaEncryptionMode);
   ASSERT_EQ(remoteMedia.mediaEncryptionOptions.mediaCryptoSuites.size(), evt.remoteMediaInfo[mediaIndex].mediaEncryptionOptions.mediaCryptoSuites.size());
   ASSERT_EQ(remoteMedia.mediaCrypto, MediaCryptoSuite_None /*evt.remoteMediaInfo[mediaIndex].mediaEncryptionOptions.mediaCryptoSuites[0]*/);
   
   // TODO: Secure media required setting in the callback returns based on what the encyption mode is, rather than what was configured
   // ASSERT_EQ(localAudio.mediaEncryptionOptions.secureMediaRequired, it->mediaEncryptionOptions.secureMediaRequired);
   if (remoteMedia.mediaEncryptionOptions.mediaEncryptionMode == MediaEncryptionMode_Unencrypted)
   {
      ASSERT_FALSE(evt.remoteMediaInfo[mediaIndex].mediaEncryptionOptions.secureMediaRequired);
   }
   else
   {
      ASSERT_TRUE(evt.remoteMediaInfo[mediaIndex].mediaEncryptionOptions.secureMediaRequired);
   }

   int cryptoCount = 0;
   for (cpc::vector<MediaCryptoSuite>::const_iterator i = remoteMedia.mediaEncryptionOptions.mediaCryptoSuites.begin(); i != remoteMedia.mediaEncryptionOptions.mediaCryptoSuites.end(); i++, cryptoCount++)
   {
      ASSERT_EQ((*i), evt.remoteMediaInfo[mediaIndex].mediaEncryptionOptions.mediaCryptoSuites[cryptoCount]);
   }
}

cpc::string TestCallEvents::convertion(ConversationState state){
   cpc::string stateDescription;
   switch(state) {
   case(ConversationState_None): stateDescription = "ConversationState_None";
      break;
   case(ConversationState_LocalOriginated): stateDescription = "ConversationState_LocalOriginated";
      break;
   case(ConversationState_RemoteOriginated): stateDescription = "ConversationState_RemoteOriginated";
      break;
   case(ConversationState_RemoteRinging): stateDescription = "ConversationState_RemoteRinging";
      break;
   case(ConversationState_LocalRinging): stateDescription = "ConversationState_LocalRinging";
      break;
   case(ConversationState_Connected): stateDescription = "ConversationState_Connected";
      break;
   case(ConversationState_Early): stateDescription = "ConversationState_Early";
      break;
   case(ConversationState_Ended): stateDescription = "ConversationState_Ended";
      break;
   default: stateDescription = "invalid state";
   }
   return stateDescription;
}

void TestCallEvents::expectConversationStateChanged(int line, TestAccount& account, 
      SipConversationHandle handle,
      ConversationState state)
{
   SipConversationHandle h;
   ConversationStateChangedEvent evt;
   ASSERT_TRUE(account.conversationEvents->expectEvent(line, "SipConversationHandler::onConversationStateChanged",
      15000, HandleEqualsPred<SipConversationHandle>(handle), h, evt)) << "missed conversation changed event";
   ASSERT_EQ(state, evt.conversationState) << "Conversation state does not match, current state is " << convertion(evt.conversationState).c_str() << ". expecting " << convertion(state).c_str();
}

void TestCallEvents::expectConversationStateChanged_time(int line, TestAccount& account,
   SipConversationHandle handle,
   ConversationState state,
   unsigned int timeMS)
{
   SipConversationHandle h;
   ConversationStateChangedEvent evt;
   ASSERT_TRUE(account.conversationEvents->expectEvent(line, "SipConversationHandler::onConversationStateChanged",
      timeMS, HandleEqualsPred<SipConversationHandle>(handle), h, evt));
   ASSERT_EQ(state, evt.conversationState);
}

void TestCallEvents::expectConversationStateChanged_ex(int line, TestAccount& account, 
   SipConversationHandle handle,
   EVENT_VALIDATOR(ConversationStateChangedEvent) validator)
{
   SipConversationHandle h;
   ConversationStateChangedEvent evt;
   ASSERT_TRUE(account.conversationEvents->expectEvent(line, "SipConversationHandler::onConversationStateChanged",
      15000, HandleEqualsPred<SipConversationHandle>(handle), h, evt));
   validator(evt);
}

void TestCallEvents::expectConversationStateChanged_answermode(int line, TestAccount& account,
   SipConversationHandle handle,
   ConversationState state,
   AnswerModeSettings& answerMode,
   EVENT_VALIDATOR(ConversationStateChangedEvent) validator)
{
   SipConversationHandle h;
   ConversationStateChangedEvent evt;
   ASSERT_TRUE(account.conversationEvents->expectEvent(line, "SipConversationHandler::onConversationStateChanged", 15000, HandleEqualsPred<SipConversationHandle>(handle), h, evt)) << "Conversation " << handle << " missed conversation changed event";
   ASSERT_EQ(state, evt.conversationState) << "Conversation " << handle << " state does not match, current state is " << convertion(evt.conversationState).c_str() << ". expecting " << convertion(state).c_str();
   ASSERT_EQ(answerMode.mode, evt.answerMode.mode);
   ASSERT_EQ(answerMode.privileged, evt.answerMode.privileged);
   ASSERT_EQ(answerMode.required, evt.answerMode.required);
   if (nullptr != validator)
   {
      validator(evt);
   }
}

void TestCallEvents::expectConversationStateChangedUptilConnected(int line, TestAccount& account,
   SipConversationHandle handle,
   EVENT_VALIDATOR(ConversationStateChangedEvent) validator)
{
   SipConversationHandle h;
   ConversationStateChangedEvent evt;
   bool success = false;
   int count = 4;
   // Seen scenarios with cpe large tests where the outgoing call-state varies from
   // Ringing, Early or Connected.

   while (!success && (count > 0))
   {
      count--;
      ASSERT_TRUE(account.conversationEvents->expectEvent(line, "SipConversationHandler::onConversationStateChanged", 15000, HandleEqualsPred<SipConversationHandle>(handle), h, evt)) << "Conversation " << handle << " missed conversation changed event";
      if (evt.conversationState == ConversationState_Connected)
      {
         success = true;  
      }
      else
      {
         safeCout("TestCallEvents::expectConversationStateChangedUptilConnected(): conversation " << handle << " current state is " << convertion(evt.conversationState).c_str());
      }
   }
   ASSERT_TRUE(success) << "Conversation " << handle << " state does not match, current state is " << convertion(evt.conversationState).c_str() << ". expecting " << convertion(ConversationState_Connected).c_str();

   if (nullptr != validator)
   {
      validator(evt);
   }
}

void TestCallEvents::expectConversationEnded(int line, TestAccount& account, 
   SipConversationHandle handle,
   ConversationEndReason endReason,
   EVENT_VALIDATOR(ConversationEndedEvent) validator)
{
   SipConversationHandle h;
   ConversationEndedEvent evt;
   ASSERT_TRUE(account.conversationEvents->expectEvent(line, "SipConversationHandler::onConversationEnded",
      15000, HandleEqualsPred<SipConversationHandle>(handle), h, evt)) << "Conversation " << handle << " missed ended event";
   ASSERT_EQ(endReason, evt.endReason) << "Coversation " << handle << " ended with incorrect reason";
   if (nullptr != validator)
   {
      validator(evt);
   }
}

void TestCallEvents::expectConversationEnded_time(int line, TestAccount& account,
   CPCAPI2::SipConversation::SipConversationHandle handle,
   CPCAPI2::SipConversation::ConversationEndReason endReason,
   EVENT_VALIDATOR(CPCAPI2::SipConversation::ConversationEndedEvent) validator,
   unsigned int timeMS)
{
   SipConversationHandle h;
   ConversationEndedEvent evt;
   ASSERT_TRUE(account.conversationEvents->expectEvent(line, "SipConversationHandler::onConversationEnded",
      timeMS, HandleEqualsPred<SipConversationHandle>(handle), h, evt));
   ASSERT_EQ(endReason, evt.endReason) << "Coversation " << handle << " end reason mismatch";;
   if (nullptr != validator)
   {
      validator(evt);
   }
}

void TestCallEvents::expectConversationError(int line, TestAccount& account,
      SipConversationHandle handle)
{
   SipConversationHandle h;
   CPCAPI2::SipConversation::ErrorEvent evt;
   ASSERT_TRUE(account.conversationEvents->expectEvent(line, "SipConversationHandler::onError",
      15000, HandleEqualsPred<SipConversationHandle>(handle), h, evt));
}


void TestCallEvents::expectConversationStaticsticsUpdated(int line, TestAccount& account, 
      SipConversationHandle handle,
      EVENT_VALIDATOR(ConversationStatisticsUpdatedEvent) validator)
{
   SipConversationHandle h;
   ConversationStatisticsUpdatedEvent evt;
   ASSERT_TRUE(account.conversationEvents->expectEvent(line, "SipConversationHandler::onConversationStatisticsUpdated",
      15000, HandleEqualsPred<SipConversationHandle>(handle), h, evt));
   validator(evt);
}

void TestCallEvents::expectTransferProgress(int line, TestAccount& account, 
      SipConversationHandle handle,
      TransferProgressEventType type)
{
   SipConversationHandle h;
   TransferProgressEvent evt;
   ASSERT_TRUE(account.conversationEvents->expectEvent(line, "SipConversationHandler::onTransferProgress",
      30000, HandleEqualsPred<SipConversationHandle>(handle),h, evt));
   ASSERT_EQ(type, evt.progressEventType);
}

void TestCallEvents::expectTransferRequest(int line, TestAccount& account, 
      SipConversationHandle handle,
      CPCAPI2::SipConversation::SipConversationHandle* transferTargetConversation,
      EVENT_VALIDATOR(CPCAPI2::SipConversation::TransferRequestEvent) validator)
{
   SipConversationHandle h;
   TransferRequestEvent evt;
   ASSERT_TRUE(account.conversationEvents->expectEvent(line, "SipConversationHandler::onIncomingTransferRequest",
      30000, HandleEqualsPred<SipConversationHandle>(handle), h, evt));
   if (NULL != transferTargetConversation)
   {
      *transferTargetConversation = evt.transferTargetConversation;
   }
   if (nullptr != validator)
   {
      validator(evt);
   }
}

// if the SDK re-transmits a 180 Ringing, it will cause the 180 Ringing NOTIFY transfer progress
// to also be re-transmitted, which will results in onTransferProgress firing repeatedly with
// TransferProgressEventType_Ringing. we account for this here
void TestCallEvents::expectTransferTryingRingingConnected(int line, TestAccount& account, SipConversationHandle callHandle)
{
   expectTransferProgress(line, account, callHandle, TransferProgressEventType_Trying);
   expectTransferProgress(line, account, callHandle, TransferProgressEventType_Ringing);
   {
      // Seen some test-runs with multiple ringing notifications before the connected progress event is received
      int tries = 3;
      while (tries > 0)
      {
         SipConversationHandle h;
         TransferProgressEvent evt;
         ASSERT_TRUE(account.conversationEvents->expectEvent(line, "SipConversationHandler::onTransferProgress", 30000, HandleEqualsPred<SipConversationHandle>(callHandle), h, evt));
         if (evt.progressEventType == TransferProgressEventType_Ringing)
         {
            tries--;
            if (tries == 0)
            {
               ASSERT_EQ(TransferProgressEventType_Connected, evt.progressEventType); // Trigger failure
            }
         }
         else
         {
            ASSERT_EQ(TransferProgressEventType_Connected, evt.progressEventType);
            break;
         }
      }
   }
}

void TestCallEvents::expectRedirectRequest(int line, TestAccount& account, 
      CPCAPI2::SipConversation::SipConversationHandle handle,
      EVENT_VALIDATOR(CPCAPI2::SipConversation::RedirectRequestEvent) validator)
{
   SipConversationHandle h;
   RedirectRequestEvent evt;
   ASSERT_TRUE(account.conversationEvents->expectEvent(line, "SipConversationHandler::onIncomingRedirectRequest",
      15000, HandleEqualsPred<SipConversationHandle>(handle), h, evt));
   if (nullptr != validator)
   {
      validator(evt);
   }
}

void TestCallEvents::expectMediaFlowing(int line, TestAccount& account, CPCAPI2::SipConversation::SipConversationHandle handle, bool audio, bool video, EVENT_VALIDATOR(CPCAPI2::SipConversation::ConversationStatisticsUpdatedEvent) validator)
{
   SipConversationHandle h;
   ConversationStatisticsUpdatedEvent evt;

   CPCAPI2::SipConversation::SipConversationManagerInternal* convInternal = dynamic_cast<CPCAPI2::SipConversation::SipConversationManagerInternal*>(account.conversation);

   convInternal->refreshConversationStatistics(handle, true, true, true, true);

   ASSERT_TRUE(account.conversationEvents->expectEvent("SipConversationHandler::onConversationStatisticsUpdated", 15000, HandleEqualsPred<SipConversationHandle>(handle), h, evt)) << "missed updated conversation statistics event";
   if (audio)
   {
      ASSERT_EQ(1, evt.conversationStatistics.audioChannels.size()) << "audio channel must be at least 1";
      ASSERT_NE(evt.conversationStatistics.audioChannels[0].streamDataCounters.packetsReceived, 0) << "Packets received is 0";
      ASSERT_NE(evt.conversationStatistics.audioChannels[0].streamDataCounters.packetsSent, 0) << "Packets sent is 0";
   }
   if (video)
   {
#ifndef CPCAPI2_AUTO_TESTS_NO_VIDEO_STATS
      ASSERT_EQ(1, evt.conversationStatistics.videoChannels.size());
      ASSERT_NE(evt.conversationStatistics.videoChannels[0].streamDataCounters.packetsReceived, 0);
      ASSERT_NE(evt.conversationStatistics.videoChannels[0].streamDataCounters.packetsSent, 0);
#endif
   }

   if (nullptr != validator)
   {
      validator(evt);
   }
}

void TestCallEvents::expectMediaDisabled(int line, TestAccount& account, CPCAPI2::SipConversation::SipConversationHandle handle, bool audio, bool video, EVENT_VALIDATOR(CPCAPI2::SipConversation::ConversationStatisticsUpdatedEvent) validator)
{
   SipConversationHandle h1;
   ConversationStatisticsUpdatedEvent evt1;

   CPCAPI2::SipConversation::SipConversationManagerInternal* convInternal = dynamic_cast<CPCAPI2::SipConversation::SipConversationManagerInternal*>(account.conversation);

   convInternal->refreshConversationStatistics(handle, true, true, true, true);

   ASSERT_TRUE(account.conversationEvents->expectEvent("SipConversationHandler::onConversationStatisticsUpdated", 15000, HandleEqualsPred<SipConversationHandle>(handle), h1, evt1)) << "missed updated conversation statistics event";
 
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   SipConversationHandle h2;
   ConversationStatisticsUpdatedEvent evt2;

   convInternal->refreshConversationStatistics(handle, true, true, true, true);

   ASSERT_TRUE(account.conversationEvents->expectEvent("SipConversationHandler::onConversationStatisticsUpdated", 15000, HandleEqualsPred<SipConversationHandle>(handle), h2, evt2)) << "missed updated conversation statistics event";
   if (audio)
   {
      ASSERT_EQ(evt1.conversationStatistics.audioChannels.size(), evt2.conversationStatistics.audioChannels.size()) << "Audio channel count changed";
      for (int i = 0; i < evt1.conversationStatistics.audioChannels.size(); i++)
      {
         IPEndpoint endpoint1 = evt1.conversationStatistics.audioChannels[i].endpoint;
         IPEndpoint endpoint2 = evt2.conversationStatistics.audioChannels[i].endpoint;
         std::stringstream address1; address1 << endpoint1.ipAddress << ":" << endpoint1.port;
         std::stringstream address2; address2 << endpoint2.ipAddress << ":" << endpoint2.port;
         ASSERT_EQ(address1.str().c_str(), address2.str().c_str()) << "Address mismatch in audio channels at index: " << i;
         ASSERT_EQ(evt1.conversationStatistics.audioChannels[i].streamDataCounters.packetsReceived, evt2.conversationStatistics.audioChannels[i].streamDataCounters.packetsReceived) << "Change detected in audio packets received: index: " << i << " address: " << address1.str().c_str() << " channel-list size: " << evt1.conversationStatistics.audioChannels.size();
         ASSERT_EQ(evt1.conversationStatistics.audioChannels[i].streamDataCounters.packetsSent, evt2.conversationStatistics.audioChannels[i].streamDataCounters.packetsSent) << "Change detected in audio packets sent: index: " << i << " address: " << address1.str().c_str() << " channel-list size: " << evt1.conversationStatistics.audioChannels.size();
      }
   }
   if (video)
   {
#ifndef CPCAPI2_AUTO_TESTS_NO_VIDEO_STATS
      ASSERT_EQ(evt1.conversationStatistics.videoChannels.size(), evt2.conversationStatistics.videoChannels.size()) << "Video channel count changed";
      for (int i = 0; i < evt1.conversationStatistics.videoChannels.size(); i++)
      {
         IPEndpoint endpoint1 = evt1.conversationStatistics.videoChannels[i].endpoint;
         IPEndpoint endpoint2 = evt2.conversationStatistics.videoChannels[i].endpoint;
         std::stringstream address1; address1 << endpoint1.ipAddress << ":" << endpoint1.port;
         std::stringstream address2; address2 << endpoint2.ipAddress << ":" << endpoint2.port;
         ASSERT_EQ(address1.str().c_str(), address2.str().c_str()) << "Address mismatch in video channels at index: " << i;
         ASSERT_EQ(evt1.conversationStatistics.videoChannels[i].streamDataCounters.packetsReceived, evt2.conversationStatistics.videoChannels[i].streamDataCounters.packetsReceived) << "Change detected in video packets received: index: " << i << " address: " << address1.str().c_str() << " channel-list size: " << evt1.conversationStatistics.videoChannels.size();
         ASSERT_EQ(evt1.conversationStatistics.videoChannels[i].streamDataCounters.packetsSent, evt2.conversationStatistics.videoChannels[i].streamDataCounters.packetsSent) << "Change detected in video packets sent: index: " << i << " address: " << address1.str().c_str() << " channel-list size: " << evt1.conversationStatistics.videoChannels.size();
      }
#endif
   }

   if (nullptr != validator)
   {
      validator(evt2);
   }
}

void TestCallEvents::expectCallHadMedia(int line, TestAccount& account, CPCAPI2::SipConversation::SipConversationHandle handle, bool audio, bool video)
{
   SipConversationState currState;
   ASSERT_EQ(account.conversationState->getState(handle, currState), CPCAPI2::kSuccess) << "cannot get Conversation state";
   if (audio)
   {
      ASSERT_EQ(1, currState.statistics.audioChannels.size()) << "audio channel must be at least 1";
      ASSERT_NE(currState.statistics.audioChannels[0].streamDataCounters.packetsReceived, 0) << "Packets received is 0";
      ASSERT_NE(currState.statistics.audioChannels[0].streamDataCounters.packetsSent, 0) << "Packets sent is 0";
      ASSERT_NE(strlen(currState.statistics.audioChannels[0].encoder.plname), 0) << "encoder codec cannot be empty";
      ASSERT_NE(strlen(currState.statistics.audioChannels[0].decoder.plname), 0) << "decoder codec cannot be empty";
      ASSERT_NE(0, currState.statistics.remoteAudioChannels.size()) << "remote account's audio channel must be at least 1";
   }
   if (video)
   {
      ASSERT_EQ(1, currState.statistics.videoChannels.size());
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsReceived, 0);
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsSent, 0);
      ASSERT_NE(0, currState.statistics.remoteVideoChannels.size());
      if (currState.statistics.videoChannels[0].streamDataCounters.packetsSent > 0)
      {
         ASSERT_NE(strlen(currState.statistics.videoChannels[0].encoder.plName), 0);
      }
      if (currState.statistics.videoChannels[0].streamDataCounters.packetsReceived > 0)
      {
         ASSERT_NE(strlen(currState.statistics.videoChannels[0].decoder.plName), 0);
      }
   }
}

void TestCallEvents::expectCallHadIncomingMedia(int line, TestAccount& account, CPCAPI2::SipConversation::SipConversationHandle handle, bool audio, bool video)
{
   SipConversationState currState;
   ASSERT_EQ(account.conversationState->getState(handle, currState), CPCAPI2::kSuccess);
   if (audio)
   {
      ASSERT_EQ(1, currState.statistics.audioChannels.size());
      ASSERT_NE(currState.statistics.audioChannels[0].streamDataCounters.packetsReceived, 0);
      ASSERT_NE(strlen(currState.statistics.audioChannels[0].encoder.plname), 0);
      ASSERT_NE(strlen(currState.statistics.audioChannels[0].decoder.plname), 0);
      ASSERT_NE(0, currState.statistics.remoteAudioChannels.size());
   }
   if (video)
   {
      ASSERT_EQ(1, currState.statistics.videoChannels.size());
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsReceived, 0);
      ASSERT_NE(0, currState.statistics.remoteVideoChannels.size());
      if (currState.statistics.videoChannels[0].streamDataCounters.packetsReceived > 0)
      {
         ASSERT_NE(strlen(currState.statistics.videoChannels[0].decoder.plName), 0);
      }
   }
}

void TestCallEvents::expectCallHadOutgoingMedia(int line, TestAccount& account, CPCAPI2::SipConversation::SipConversationHandle handle, bool audio, bool video)
{
   SipConversationState currState;
   ASSERT_EQ(account.conversationState->getState(handle, currState), CPCAPI2::kSuccess);
   if (audio)
   {
      ASSERT_EQ(1, currState.statistics.audioChannels.size());
      ASSERT_NE(currState.statistics.audioChannels[0].streamDataCounters.packetsSent, 0);
      ASSERT_NE(strlen(currState.statistics.audioChannels[0].encoder.plname), 0);
      ASSERT_NE(strlen(currState.statistics.audioChannels[0].decoder.plname), 0);
      ASSERT_NE(0, currState.statistics.remoteAudioChannels.size());
   }
   if (video)
   {
      ASSERT_EQ(1, currState.statistics.videoChannels.size());
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsSent, 0);
      ASSERT_NE(0, currState.statistics.remoteVideoChannels.size());
      if (currState.statistics.videoChannels[0].streamDataCounters.packetsSent > 0)
      {
         ASSERT_NE(strlen(currState.statistics.videoChannels[0].encoder.plName), 0);
      }
   }
}

void TestCallEvents::expectRTCP(int line, TestAccount& account, CPCAPI2::SipConversation::SipConversationHandle handle, bool audio, bool video )
{
   std::string environmentId = TestEnvironmentConfig::testEnvironmentId().c_str();
   if (environmentId != "repro")
   {
      // Note that this is required as CPE currently does not support RTCP, we should
      // remove this filter if RTCP support is added to CPE in the future
      return;
   }

   // Check that bobCall video has RTCP. We do this by getting two reports and comparing stamps
   uint64_t audioRTCPStamp = 0;
   uint64_t videoRTCPStamp = 0;

   const int kWaitIntervalMs = 3000;
   const int kComparisonMaxChances = 4;

   // check that audio and or video RTCP last received timestamps are increasing.
   // for each type (audio or video), check the timestamp increases two times.
   // for each type, we try up to kComparisonMaxChances times to wait for the
   // timestamp to increase. This lets us wait longer for a timestamp increase,
   // but only if necessary.

   for( int i = 0 ; i < 2 ; ++i )
   {
      SipConversationHandle h;
      ConversationStatisticsUpdatedEvent evt;

      std::this_thread::sleep_for(std::chrono::milliseconds(kWaitIntervalMs));
      ASSERT_EQ(0, account.conversation->refreshConversationStatistics(handle, true, false, true));
      ASSERT_TRUE(account.conversationEvents->expectEvent("SipConversationHandler::onConversationStatisticsUpdated",
         15000, HandleEqualsPred<SipConversationHandle>(handle), h, evt));

      if( audio )
      {
         for (int chance = 1; chance <= kComparisonMaxChances; ++chance)
         {
            ASSERT_EQ(1, evt.conversationStatistics.audioChannels.size());
            if (evt.conversationStatistics.remoteAudioChannels[0].lastRtcpReceived > audioRTCPStamp)
            {
               audioRTCPStamp = evt.conversationStatistics.remoteAudioChannels[0].lastRtcpReceived;
               break;
            }
            else if (chance == kComparisonMaxChances)
            {
               // out of chances; this should assert
               ASSERT_GT(evt.conversationStatistics.remoteAudioChannels[0].lastRtcpReceived, audioRTCPStamp);
            }
            else
            {
               // wait longer and check again
               std::this_thread::sleep_for(std::chrono::milliseconds(kWaitIntervalMs));
               ASSERT_EQ(0, account.conversation->refreshConversationStatistics(handle, true, false, true));
               ASSERT_TRUE(account.conversationEvents->expectEvent("SipConversationHandler::onConversationStatisticsUpdated",
                  15000, HandleEqualsPred<SipConversationHandle>(handle), h, evt));
            }
         }
      }

      if( video )
      {
         for (int chance = 1; chance <= kComparisonMaxChances; ++chance)
         {
            ASSERT_EQ(1, evt.conversationStatistics.videoChannels.size());
            if (evt.conversationStatistics.remoteVideoChannels[0].lastRtcpReceived > videoRTCPStamp)
            {
               videoRTCPStamp = evt.conversationStatistics.remoteVideoChannels[0].lastRtcpReceived;
               break;
            }
            else if (chance == kComparisonMaxChances)
            {
               // out of chances; this should assert
               ASSERT_GT(evt.conversationStatistics.remoteVideoChannels[0].lastRtcpReceived, videoRTCPStamp);
            }
            else
            {
               std::this_thread::sleep_for(std::chrono::milliseconds(kWaitIntervalMs));
               ASSERT_EQ(0, account.conversation->refreshConversationStatistics(handle, true, false, true));
               ASSERT_TRUE(account.conversationEvents->expectEvent("SipConversationHandler::onConversationStatisticsUpdated",
                  15000, HandleEqualsPred<SipConversationHandle>(handle), h, evt));
            }
         }
      }
   }
}
