#include "cpcapi2_test_fixture.h"
#include "test_account_events.h"
#include "test_events.h"
#include "test_call_events.h"
#include "sipua_alianza_api_test_fixture.h"
#include "sipua_alianza_api_test_events.h"
#include "sipua_alianza_api_test_helper.h"

#include "test_framework/http_test_framework.h"

#include "alianza_api/interface/public/alianza_api_handler.h"
#include "alianza_api/interface/public/alianza_api_types.h"
#include "alianza_api/interface/experimental/alianza_api_manager_internal.h"

#include "gtest/gtest-spi.h"

#include "util/ReactorHelpers.h"

#include <sstream>

using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::SipAccount;
using namespace std::chrono;

#define GTEST_SKIP_REPRO() if (TestEnvironmentConfig::testEnvironmentId() == "repro") { GTEST_SKIP(); }

namespace
{
   class SipuaAlianzaAccountModuleTest : public CpcapiAutoTest
   {
   public:
      SipuaAlianzaAccountModuleTest() {}
      virtual ~SipuaAlianzaAccountModuleTest() {}

      void SetUp() override
      {
         if (TestEnvironmentConfig::cpeTestLevel() > TestEnvironmentConfig::CpeTestLevel::Exhaustive)
         {
            GTEST_SKIP();
         }
      }

      std::string reserveNumber(TestAccount& account);

      void setupQueryNumberState(TestAccount& account, AlianzaApiAccountHandle& handle, std::shared_ptr<CPCAPI2::test::AlianzaApiAccountFsm>& fsm);
      void setupReserveNumberState(TestAccount& account, AlianzaApiAccountHandle& handle, std::shared_ptr<CPCAPI2::test::AlianzaApiAccountFsm>& fsm);
      void setupCreateAccountState(TestAccount& account, AlianzaApiAccountHandle& handle, std::shared_ptr<CPCAPI2::test::AlianzaApiAccountFsm>& fsm);
      void setupAddUserState(TestAccount& account, AlianzaApiAccountHandle& handle, std::shared_ptr<CPCAPI2::test::AlianzaApiAccountFsm>& fsm);
      void setupAddNumberState(TestAccount& account, AlianzaApiAccountHandle& handle, std::shared_ptr<CPCAPI2::test::AlianzaApiAccountFsm>& fsm);
      void setupUpdateCallerIdState(TestAccount& account, AlianzaApiAccountHandle& handle, std::shared_ptr<CPCAPI2::test::AlianzaApiAccountFsm>& fsm);
      void setupUpdateUserState(TestAccount& account, AlianzaApiAccountHandle& handle, std::shared_ptr<CPCAPI2::test::AlianzaApiAccountFsm>& fsm);
      void setupEnabledState(TestAccount& account, AlianzaApiAccountHandle& handle, std::shared_ptr<CPCAPI2::test::AlianzaApiAccountFsm>& fsm);
      void setupRemoveNumberState(TestAccount& account, AlianzaApiAccountHandle& handle, std::shared_ptr<CPCAPI2::test::AlianzaApiAccountFsm>& fsm);
      void setupDeleteAccountState(TestAccount& account, AlianzaApiAccountHandle& handle, std::shared_ptr<CPCAPI2::test::AlianzaApiAccountFsm>& fsm);
      void setupReleaseNumberState(TestAccount& account, AlianzaApiAccountHandle& handle, std::shared_ptr<CPCAPI2::test::AlianzaApiAccountFsm>& fsm);
      void setupQueryUserConfigState(TestAccount& account, AlianzaApiAccountHandle& handle, std::shared_ptr<CPCAPI2::test::AlianzaApiAccountFsm>& fsm);

      void reserveNumberWithError(TestAccount& account, AlianzaApiAccountHandle& handle, std::shared_ptr<CPCAPI2::test::AlianzaApiAccountFsm>& fsm, const std::string& errorStr);
      void addNumberWithError(TestAccount& account, AlianzaApiAccountHandle& handle, std::shared_ptr<CPCAPI2::test::AlianzaApiAccountFsm>& fsm, const std::string& errorStr);

      static void cleanupNumber(TestAccount& account, AlianzaApiAccountHandle handle);
      static void cleanupAccountAndNumber(TestAccount& account, AlianzaApiAccountHandle handle);
   };

   void SipuaAlianzaAccountModuleTest::cleanupNumber(TestAccount& account, AlianzaApiAccountHandle handle)
   {
      resip::MultiReactor& reactor = dynamic_cast<PhoneInterface*>(account.phone)->getSdkModuleThread();
      std::shared_ptr<CPCAPI2::test::AlianzaApiManagerInternal> manager = (std::dynamic_pointer_cast<AlianzaApiManagerInternal>(account.alianzaApiManager));
      std::shared_ptr<CPCAPI2::test::AlianzaApiAccountFsm> fsm = manager->getAccountFsm(handle);
      AlianzaApiHttpResponseEvent evt(204, "");
      AlianzaApiTestHelper::createErrorResponse(evt.rc, "OK", evt.response);

      reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
      assertAlianzaApiAccountStatusEvent(account, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_ReleaseNumber);
      reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
      assertAlianzaApiAccountStatusEvent(account, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);

      manager->disableAccount(handle);
      ASSERT_EQ(account.alianzaApiAccountHandle, 0);
      fsm.reset();
      std::this_thread::sleep_for(std::chrono::milliseconds(3000));
      account.alianzaApiEvents->clearCommands();
   }

   void SipuaAlianzaAccountModuleTest::cleanupAccountAndNumber(TestAccount& account, AlianzaApiAccountHandle handle)
   {
      resip::MultiReactor& reactor = dynamic_cast<PhoneInterface*>(account.phone)->getSdkModuleThread();
      std::shared_ptr<CPCAPI2::test::AlianzaApiManagerInternal> manager = (std::dynamic_pointer_cast<AlianzaApiManagerInternal>(account.alianzaApiManager));
      std::shared_ptr<CPCAPI2::test::AlianzaApiAccountFsm> fsm = manager->getAccountFsm(handle);
      AlianzaApiHttpResponseEvent evt(204, "");
      AlianzaApiTestHelper::createErrorResponse(evt.rc, "OK", evt.response);

      reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
      assertAlianzaApiAccountStatusEvent(account, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_DeleteAccount);
      SipuaAlianzaAccountModuleTest::cleanupNumber(account, handle);
   }

   TEST_F(SipuaAlianzaAccountModuleTest, CreateBackendAccount)
   {
      GTEST_SKIP_REPRO();
      TestAccount alice("alice", Account_Init);
      alice.alianzaApiManager->start(alice.config.alianzaConfig.api);

      alice.alianzaApiManager->authorize();
      assertAuthorizationSuccess(alice.alianzaApiEvents);

      alice.alianzaApiAccountHandle = alice.alianzaApiManager->createAccount(&alice);
      alice.alianzaApiManager->enableAccount(alice.alianzaApiAccountHandle);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_QueryNumber);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_ReserveNumber);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_CreateAccount);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_SetGroupName);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_AddUser);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_UserAuth);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_QueryClientConfig);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_AddNumber);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_CheckNumberInAccount);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_UpdateCallerId);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_UpdateUser);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Enabled);

      alice.alianzaApiManager->disableAccount(alice.alianzaApiAccountHandle);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_RemoveNumber);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_DeleteAccount);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_ReleaseNumber);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);
      alice.alianzaApiAccountHandle = 0;
   }

   TEST_F(SipuaAlianzaAccountModuleTest, CreateBackendAccountHttpTimeout)
   {
      GTEST_SKIP_REPRO();
      TestAccount alice("alice", Account_Init);
      alice.alianzaApiManager->start(alice.config.alianzaConfig.api);

      alice.alianzaApiManager->authorize();
      assertAuthorizationSuccess(alice.alianzaApiEvents);

      alice.alianzaApiAccountHandle = alice.alianzaApiManager->createAccount(&alice);
      std::shared_ptr<CPCAPI2::test::AlianzaApiManagerInternal> internalManager = (std::dynamic_pointer_cast<AlianzaApiManagerInternal>(CPCAPI2::test::AlianzaApiManager::getInterface(&alice)));
      internalManager->disableHttpRequestTransmission(alice.alianzaApiAccountHandle);
      alice.alianzaApiManager->enableAccount(alice.alianzaApiAccountHandle);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_QueryNumber);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);
      alice.alianzaApiAccountHandle = 0;
   }

   // Attempts to enable an account with unreachble public API such that
   // we validate the test frameworks fails the test due to auth success failure
   TEST_F(SipuaAlianzaAccountModuleTest, EnableUnreachablePublicApiUrl)
   {
      GTEST_SKIP_REPRO();

      TestAccount alice("alice", Account_Init);
      alice.config.alianzaConfig.api.publicApiUrl = "https://*********/"; // non-routable

      // adapted from EXPECT_FATAL_FAILURE -- we can't use EXPECT_FATAL_FAILURE here because
      // it fails if there is more than one gtest assert triggered (and alice.enable triggers more than one
      // for this test case).

      // capture all expect failures in test result array
      ::testing::TestPartResultArray gtest_failures;
      // run method in its own scope
      ::testing::ScopedFakeTestPartResultReporter gtest_reporter(
         ::testing::ScopedFakeTestPartResultReporter::
         INTERCEPT_ONLY_CURRENT_THREAD, &gtest_failures);

      // run method that we expect will internally trigger gtest asserts
      alice.enable();

      // since we set publicApiUrl to a non-routable address, we expect our test framework to fail relevant gtest asserts
      ASSERT_GE(gtest_failures.size(), 2) << "Did not have at least two failures";
      bool foundAuthFailure;
      bool foundCreateAccountFailed;
      for (int i = 0; i < gtest_failures.size(); ++i)
      {
         // We expect at least two failures:

         // 1) expectAuthorizationSuccess assertion failing due to authorize HTTP request failing  
         if (std::string(gtest_failures.GetTestPartResult(i).message()).find("Expected: AlianzaApiTestEvents::expectAuthorizationSuccess") != std::string::npos)
         {
            foundAuthFailure = true;
         }

         // 2) TestAccount::createAlianzaAccountInBackend() detecting the failure from (1)
         if (std::string(gtest_failures.GetTestPartResult(i).message()).find("Expected: createAlianzaAccountInBackend()") != std::string::npos)
         {
            foundCreateAccountFailed = true;
         }

         // We do *not* expect any SIP account state change failures, since the test framework should have not continued to SIP account
         // enable since Alianza backend account creation failed.
         ASSERT_EQ(std::string(gtest_failures.GetTestPartResult(i).message()).find("SipAccountHandler::onAccountStatusChanged"), std::string::npos);

      }

      ASSERT_TRUE(foundAuthFailure) << "Didn't find expected auth failure event";
      ASSERT_TRUE(foundCreateAccountFailed) << "Didn't find expected account creation failure event";

   }

   TEST_F(SipuaAlianzaAccountModuleTest, CreateBackendAccountNumberAlreadyExistsRetry)
   {
      GTEST_SKIP_REPRO();
      TestAccount alice("alice", Account_Init);
      alice.alianzaApiManager->start(alice.config.alianzaConfig.api);

      alice.alianzaApiManager->authorize();
      assertAuthorizationSuccess(alice.alianzaApiEvents);

      std::string number = reserveNumber(alice);
      ASSERT_TRUE(number.size() != 0);

      // Let's try to proceed with enabling the account normally with that same number, as it
      // is already reserved, the number used should not be the same as the one reserved
      alice.alianzaApiAccountHandle = alice.alianzaApiManager->createAccount(&alice);
      alice.alianzaApiManager->enableAccount(alice.alianzaApiAccountHandle);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_QueryNumber);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_ReserveNumber);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_CreateAccount);
      ASSERT_TRUE(number != alice.config.alianzaSession.getUa()->phoneId) << " number: " << number << " phoneId: " << alice.config.alianzaSession.getUa()->phoneId;

      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_SetGroupName);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_AddUser);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_UserAuth);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_QueryClientConfig);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_AddNumber);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_CheckNumberInAccount);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_UpdateCallerId);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_UpdateUser);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Enabled);

      alice.alianzaApiManager->disableAccount(alice.alianzaApiAccountHandle);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_RemoveNumber);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_DeleteAccount);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_ReleaseNumber);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);
      alice.alianzaApiAccountHandle = 0;

      {
         alice.alianzaApiEvents->clearCommands();
         std::string messageBody("");
         std::string url("");
         AlianzaAccountConfig config = alice.config.alianzaConfig;
         AlianzaSessionInfo session = alice.config.alianzaSession;
         session.getUa()->phoneId = number;
         session.getUa()->phoneNumber = number;
         AlianzaApiTestHelper::createMessageToDeleteNumberInPartition(config, session, *session.getUa(), url, messageBody);

         alice.alianzaApiManager->sendDestroyMessage(url.c_str(), messageBody.c_str());
         assertNumberDeletedInPartition(alice.alianzaApiEvents, session.getUa()->phoneId);
      }
   }

   TEST_F(SipuaAlianzaAccountModuleTest, CreateBackendAccountWithExtension)
   {
      GTEST_SKIP_REPRO();
      TestAccount alice("alice", Account_Init);
      alice.config.alianzaSession.numberEnabled = false;
      alice.alianzaApiManager->start(alice.config.alianzaConfig.api);

      alice.alianzaApiManager->authorize();
      assertAuthorizationSuccess(alice.alianzaApiEvents);

      alice.alianzaApiAccountHandle = alice.alianzaApiManager->createAccount(&alice);
      alice.alianzaApiManager->enableAccount(alice.alianzaApiAccountHandle);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_CreateAccount);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_SetGroupName);

      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_AddUser);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_UserAuth);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_QueryClientConfig);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Enabled);

      alice.alianzaApiManager->disableAccount(alice.alianzaApiAccountHandle);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_DeleteAccount);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);
      alice.alianzaApiAccountHandle = 0;
   }

   TEST_F(SipuaAlianzaAccountModuleTest, CreateBackendDuplicateUsernameRetry)
   {
      GTEST_SKIP_REPRO();
      TestAccount alice("alice", Account_Init);
      alice.config.alianzaSession.numberEnabled = false;
      std::shared_ptr<AlianzaSipUaInfo> ua1 = alice.config.alianzaSession.getUa();
      std::shared_ptr<AlianzaSipUaInfo> ua2 = alice.config.alianzaSession.addUa(alice.config.alianzaConfig);
      ua2->username = ua1->username;
      // ua2->emailAddress = ua1->emailAddress; // causes EmailAddressInUse error

      alice.alianzaApiManager->start(alice.config.alianzaConfig.api);

      alice.alianzaApiManager->authorize();
      assertAuthorizationSuccess(alice.alianzaApiEvents);

      alice.alianzaApiAccountHandle = alice.alianzaApiManager->createAccount(&alice);
      alice.alianzaApiManager->enableAccount(alice.alianzaApiAccountHandle);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_CreateAccount);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_SetGroupName);

      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_AddUser);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_UserAuth);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_QueryClientConfig);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_AddUser);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_UserAuth);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_QueryClientConfig);

      assertAlianzaApiIdentityResetEvent(alice);

      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Enabled);

      alice.alianzaApiManager->disableAccount(alice.alianzaApiAccountHandle);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_DeleteAccount);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);
      alice.alianzaApiAccountHandle = 0;
   }

   TEST_F(SipuaAlianzaAccountModuleTest, CreateBackendAccountWithMultipleExtensions)
   {
      GTEST_SKIP_REPRO();
      TestAccount alice("alice", Account_Init);
      alice.config.alianzaSession.numberEnabled = false;
      alice.config.alianzaSession.addUa(alice.config.alianzaConfig);
      alice.config.alianzaSession.addUa(alice.config.alianzaConfig);

      alice.alianzaApiManager->start(alice.config.alianzaConfig.api);

      alice.alianzaApiManager->authorize();
      assertAuthorizationSuccess(alice.alianzaApiEvents);

      alice.alianzaApiAccountHandle = alice.alianzaApiManager->createAccount(&alice);
      alice.alianzaApiManager->enableAccount(alice.alianzaApiAccountHandle);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_CreateAccount);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_SetGroupName);

      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_AddUser);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_UserAuth);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_QueryClientConfig);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_AddUser);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_UserAuth);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_QueryClientConfig);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_AddUser);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_UserAuth);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_QueryClientConfig);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Enabled);

      alice.alianzaApiManager->disableAccount(alice.alianzaApiAccountHandle);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_DeleteAccount);
      assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);
      alice.alianzaApiAccountHandle = 0;
   }

   void SipuaAlianzaAccountModuleTest::setupQueryNumberState(TestAccount& account, AlianzaApiAccountHandle& handle, std::shared_ptr<CPCAPI2::test::AlianzaApiAccountFsm>& fsm)
   {
      std::shared_ptr<CPCAPI2::test::AlianzaApiManagerInternal> manager = (std::dynamic_pointer_cast<AlianzaApiManagerInternal>(account.alianzaApiManager));
      handle = manager->createAccount(&account);
      fsm = manager->getAccountFsm(handle);
      ASSERT_TRUE(fsm != NULL);
      manager->disableHttpRequestTransmission(handle);
      manager->enableAccount(handle);
      assertAlianzaApiAccountStatusEvent(account, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_QueryNumber);
   }

   TEST_F(SipuaAlianzaAccountModuleTest, CreateBackendAccountVerifyQueryNumberState)
   {
      GTEST_SKIP_REPRO();

      TestAccount alice("alice", Account_Init);
      std::shared_ptr<CPCAPI2::test::AlianzaApiManager> manager = alice.alianzaApiManager;
      resip::MultiReactor& reactor = dynamic_cast<PhoneInterface*>(alice.phone)->getSdkModuleThread();
      AlianzaSessionInfo& session = alice.config.alianzaSession;
      AlianzaAccountConfig& config = alice.config.alianzaConfig;

      alice.alianzaApiManager->start(config.api);
      alice.alianzaApiManager->authorize();
      assertAuthorizationSuccess(alice.alianzaApiEvents);

      AlianzaApiAccountHandle handle = 0;
      std::shared_ptr<CPCAPI2::test::AlianzaApiAccountFsm> fsm;
      int scenario = 0;

      // General error
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyQueryNumberState(): scenario: " << ++scenario << " ***** random error");
         setupQueryNumberState(alice, handle, fsm);
         AlianzaApiHttpResponseEvent evt(403, "");
         AlianzaApiTestHelper::createErrorResponse(403, "Random 403", evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);

         manager->disableAccount(handle);
         ASSERT_EQ(alice.alianzaApiAccountHandle, 0);
         fsm.reset();
         std::this_thread::sleep_for(std::chrono::milliseconds(3000));
         alice.alianzaApiEvents->clearCommands();
      }

      // 404 error but unhandled reason
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyQueryNumberState(): scenario: " << ++scenario << " ***** error 404 random reason");
         setupQueryNumberState(alice, handle, fsm);
         AlianzaApiHttpResponseEvent evt(404, "");
         AlianzaApiTestHelper::createErrorResponse(404, "Random 404", evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);

         manager->disableAccount(handle);
         ASSERT_EQ(alice.alianzaApiAccountHandle, 0);
         fsm.reset();
         std::this_thread::sleep_for(std::chrono::milliseconds(3000));
         alice.alianzaApiEvents->clearCommands();
      }

      // 404 error with particular EntityNotFound error, i.e. success, number is available
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyQueryNumberState(): scenario: " << ++scenario << " ***** error 404 reason EntityNotFound, i.e. success case");
         setupQueryNumberState(alice, handle, fsm);
         AlianzaApiHttpResponseEvent evt(404, "");
         AlianzaApiTestHelper::createErrorResponseWithDataObject(404, "EntityNotFound", "TelephoneNumberInventory", session.getUa()->phoneNumber, evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_ReserveNumber);

         manager->disableAccount(handle);
         ASSERT_EQ(alice.alianzaApiAccountHandle, 0);
         fsm.reset();
         std::this_thread::sleep_for(std::chrono::milliseconds(3000));
         alice.alianzaApiEvents->clearCommands();
      }

      // 200 response, i.e. number not available, 2nd try successful
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyQueryNumberState(): scenario: " << ++scenario << " ***** 200 response as number exists, i.e. initial failure but success in retry");
         setupQueryNumberState(alice, handle, fsm);
         std::string number1 = session.getUa()->phoneNumber;
         AlianzaApiHttpResponseEvent evt(200, "");
         AlianzaApiTestHelper::createErrorResponse(200, "TelephoneNumberExists", evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiIdentityResetEventVerifyNumber(alice, number1);

         evt.rc = 404;
         AlianzaApiTestHelper::createErrorResponseWithDataObject(404, "EntityNotFound", "TelephoneNumberInventory", session.getUa()->phoneNumber, evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_ReserveNumber);

         manager->disableAccount(handle);
         ASSERT_EQ(alice.alianzaApiAccountHandle, 0);
         fsm.reset();
         std::this_thread::sleep_for(std::chrono::milliseconds(3000));
         alice.alianzaApiEvents->clearCommands();
      }

      // 200 response, i.e. number not available, all attempts failed
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyQueryNumberState(): scenario: " << ++scenario << " ***** 200 response to all requests, i.e. could not find a number to reserve");
         setupQueryNumberState(alice, handle, fsm);
         std::string number1 = session.getUa()->phoneNumber;
         AlianzaApiHttpResponseEvent evt(200, "");
         AlianzaApiTestHelper::createErrorResponse(200, "TelephoneNumberExists", evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiIdentityResetEventVerifyNumber(alice, number1);

         std::string number2 = session.getUa()->phoneNumber;
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiIdentityResetEventVerifyNumber(alice, number2);

         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);

         manager->disableAccount(handle);
         ASSERT_EQ(alice.alianzaApiAccountHandle, 0);
         fsm.reset();
         std::this_thread::sleep_for(std::chrono::milliseconds(3000));
         alice.alianzaApiEvents->clearCommands();
      }

      // Http timeout
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyQueryNumberState(): scenario: " << ++scenario << " ***** http timeout");
         setupQueryNumberState(alice, handle, fsm);
         assertAlianzaApiHttpResponseTimeoutEvent(alice);
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);

         manager->disableAccount(handle);
         ASSERT_EQ(alice.alianzaApiAccountHandle, 0);
         fsm.reset();
         std::this_thread::sleep_for(std::chrono::milliseconds(3000));
         alice.alianzaApiEvents->clearCommands();
      }
   }

   void SipuaAlianzaAccountModuleTest::setupReserveNumberState(TestAccount& account, AlianzaApiAccountHandle& handle, std::shared_ptr<CPCAPI2::test::AlianzaApiAccountFsm>& fsm)
   {
      setupQueryNumberState(account, handle, fsm);
      resip::MultiReactor& reactor = dynamic_cast<PhoneInterface*>(account.phone)->getSdkModuleThread();
      AlianzaSessionInfo& session = account.config.alianzaSession;
      AlianzaAccountConfig& config = account.config.alianzaConfig;
      AlianzaApiHttpResponseEvent evt(404, "");
      AlianzaApiTestHelper::createErrorResponseWithDataObject(404, "EntityNotFound", "TelephoneNumberInventory", session.getUa()->phoneNumber, evt.response);
      reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
      assertAlianzaApiAccountStatusEvent(account, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_ReserveNumber);
   }

   void SipuaAlianzaAccountModuleTest::reserveNumberWithError(TestAccount& account, AlianzaApiAccountHandle& handle, std::shared_ptr<CPCAPI2::test::AlianzaApiAccountFsm>& fsm, const std::string& errorStr)
   {
      resip::MultiReactor& reactor = dynamic_cast<PhoneInterface*>(account.phone)->getSdkModuleThread();
      std::shared_ptr<CPCAPI2::test::AlianzaApiManager> manager = account.alianzaApiManager;
      AlianzaSessionInfo& session = account.config.alianzaSession;

      setupReserveNumberState(account, handle, fsm);
      std::string number1 = session.getUa()->phoneNumber;
      AlianzaApiHttpResponseEvent evt(400, "");
      AlianzaApiTestHelper::createErrorResponse(400, errorStr, evt.response);
      reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
      assertAlianzaApiIdentityResetEventVerifyNumber(account, number1);
      assertAlianzaApiAccountStatusEvent(account, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_QueryNumber);

      evt.rc = 404;
      AlianzaApiTestHelper::createErrorResponseWithDataObject(404, "EntityNotFound", "TelephoneNumberInventory", session.getUa()->phoneNumber, evt.response);
      reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
      assertAlianzaApiAccountStatusEvent(account, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_ReserveNumber);

      evt.rc = 201;
      AlianzaApiTestHelper::createResponseWithId(session.getUa()->phoneNumber, evt.response);
      reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
      assertAlianzaApiAccountStatusEvent(account, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_CreateAccount);
      ASSERT_TRUE(fsm->hasNumberBeenReservedInPartition());

      reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::disable, fsm));
      assertAlianzaApiAccountStatusEvent(account, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_ReleaseNumber);
      evt.rc = 204;
      AlianzaApiTestHelper::createErrorResponse(204, "OK", evt.response);
      reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
      assertAlianzaApiAccountStatusEvent(account, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);

      manager->disableAccount(handle);
      ASSERT_EQ(account.alianzaApiAccountHandle, 0);
      fsm.reset();
      std::this_thread::sleep_for(std::chrono::milliseconds(3000));
      account.alianzaApiEvents->clearCommands();
   }

   TEST_F(SipuaAlianzaAccountModuleTest, CreateBackendAccountVerifyReserveNumberState)
   {
      GTEST_SKIP_REPRO();

      TestAccount alice("alice", Account_Init);
      std::shared_ptr<CPCAPI2::test::AlianzaApiManager> manager = alice.alianzaApiManager;
      resip::MultiReactor& reactor = dynamic_cast<PhoneInterface*>(alice.phone)->getSdkModuleThread();
      AlianzaSessionInfo& session = alice.config.alianzaSession;
      AlianzaAccountConfig& config = alice.config.alianzaConfig;

      alice.alianzaApiManager->start(config.api);
      alice.alianzaApiManager->authorize();
      assertAuthorizationSuccess(alice.alianzaApiEvents);

      AlianzaApiAccountHandle handle = 0;
      std::shared_ptr<CPCAPI2::test::AlianzaApiAccountFsm> fsm;
      int scenario = 0;

      // General error 
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyReserveNumberState(): scenario: " << ++scenario << " ***** random error");
         setupReserveNumberState(alice, handle, fsm);
         AlianzaApiHttpResponseEvent evt(403, "");
         AlianzaApiTestHelper::createErrorResponse(403, "Random 403", evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);

         manager->disableAccount(handle);
         ASSERT_EQ(alice.alianzaApiAccountHandle, 0);
         fsm.reset();
         std::this_thread::sleep_for(std::chrono::milliseconds(3000));
         alice.alianzaApiEvents->clearCommands();
      }

      // 400 error but unhandled reason
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyReserveNumberState(): scenario: " << ++scenario << " ***** error 400 random reason");
         setupReserveNumberState(alice, handle, fsm);
         AlianzaApiHttpResponseEvent evt(400, "");
         AlianzaApiTestHelper::createErrorResponse(400, "Random 400", evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);

         manager->disableAccount(handle);
         ASSERT_EQ(alice.alianzaApiAccountHandle, 0);
         fsm.reset();
         std::this_thread::sleep_for(std::chrono::milliseconds(3000));
         alice.alianzaApiEvents->clearCommands();
      }

      // 400 error with particular TNDisconnectEventInProgress error
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyReserveNumberState(): scenario: " << ++scenario << " ***** error 400 reason TNDisconnectEventInProgress, will reset identity");
         reserveNumberWithError(alice, handle, fsm, "TNDisconnectEventInProgress");
      }

      // 400 error with particular PhoneNumberAlreadyExists error, i.e. retry request after a delay, 2nd try successful
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyReserveNumberState(): scenario: " << ++scenario << " ***** error 400 reason PhoneNumberAlreadyExists, i.e. failed will try again later");
         reserveNumberWithError(alice, handle, fsm, "PhoneNumberAlreadyExists");
      }

      // 400 error with particular PhoneNumberAlreadyExists error, all attempts failed
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyReserveNumberState(): scenario: " << ++scenario << " ***** error 400 reason PhoneNumberAlreadyExists - all attempts failed");
         setupReserveNumberState(alice, handle, fsm);
         AlianzaApiHttpResponseEvent evt(400, "");

         std::string number = session.getUa()->phoneNumber;
         for (int i = 0; i < ALIANZA_API_NUMBER_RESERVE_RETRIES; ++i)
         {
            evt.rc = 400;
            AlianzaApiTestHelper::createErrorResponse(400, "PhoneNumberAlreadyExists", evt.response);
            reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
            assertAlianzaApiIdentityResetEventVerifyNumber(alice, number);
            assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_QueryNumber);

            evt.rc = 404;
            AlianzaApiTestHelper::createErrorResponseWithDataObject(404, "EntityNotFound", "TelephoneNumberInventory", session.getUa()->phoneNumber, evt.response);
            reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
            number = session.getUa()->phoneNumber;
            assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_ReserveNumber);
         }

         evt.rc = 400;
         AlianzaApiTestHelper::createErrorResponse(400, "PhoneNumberAlreadyExists", evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);

         manager->disableAccount(handle);
         ASSERT_EQ(alice.alianzaApiAccountHandle, 0);
         fsm.reset();
         std::this_thread::sleep_for(std::chrono::milliseconds(3000));
         alice.alianzaApiEvents->clearCommands();
      }

      // 201 response, number reserved successfully
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyReserveNumberState(): scenario: " << ++scenario << " ***** 201 success response, i.e. number reserved successfully");
         setupReserveNumberState(alice, handle, fsm);
         AlianzaApiHttpResponseEvent evt(201, "OK");
         AlianzaApiTestHelper::createResponseWithId(session.getUa()->phoneNumber, evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_CreateAccount);
         ASSERT_TRUE(fsm->hasNumberBeenReservedInPartition());

         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::disable, fsm));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_ReleaseNumber);
         evt.rc = 204;
         AlianzaApiTestHelper::createErrorResponse(204, "OK", evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);

         manager->disableAccount(handle);
         ASSERT_EQ(alice.alianzaApiAccountHandle, 0);
         fsm.reset();
         std::this_thread::sleep_for(std::chrono::milliseconds(3000));
         alice.alianzaApiEvents->clearCommands();
      }

      // Http timeout
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyReserveNumberState(): scenario: " << ++scenario << " ***** http timeout");
         setupReserveNumberState(alice, handle, fsm);
         assertAlianzaApiHttpResponseTimeoutEvent(alice);
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);

         manager->disableAccount(handle);
         ASSERT_EQ(alice.alianzaApiAccountHandle, 0);
         fsm.reset();
         std::this_thread::sleep_for(std::chrono::milliseconds(3000));
         alice.alianzaApiEvents->clearCommands();
      }
   }

   void SipuaAlianzaAccountModuleTest::setupCreateAccountState(TestAccount& account, AlianzaApiAccountHandle& handle, std::shared_ptr<CPCAPI2::test::AlianzaApiAccountFsm>& fsm)
   {
      setupReserveNumberState(account, handle, fsm);
      resip::MultiReactor& reactor = dynamic_cast<PhoneInterface*>(account.phone)->getSdkModuleThread();
      AlianzaSessionInfo& session = account.config.alianzaSession;
      AlianzaAccountConfig& config = account.config.alianzaConfig;
      AlianzaApiHttpResponseEvent evt(201, "OK");
      AlianzaApiTestHelper::createResponseWithId(session.getUa()->phoneNumber, evt.response);
      reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
      assertAlianzaApiAccountStatusEvent(account, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_CreateAccount);
   }

   TEST_F(SipuaAlianzaAccountModuleTest, CreateBackendAccountVerifyCreateAccountState)
   {
      GTEST_SKIP_REPRO();

      TestAccount alice("alice", Account_Init);
      std::shared_ptr<CPCAPI2::test::AlianzaApiManager> manager = alice.alianzaApiManager;
      resip::MultiReactor& reactor = dynamic_cast<PhoneInterface*>(alice.phone)->getSdkModuleThread();
      AlianzaSessionInfo& session = alice.config.alianzaSession;
      AlianzaAccountConfig& config = alice.config.alianzaConfig;

      alice.alianzaApiManager->start(config.api);
      alice.alianzaApiManager->authorize();
      assertAuthorizationSuccess(alice.alianzaApiEvents);

      AlianzaApiAccountHandle handle = 0;
      std::shared_ptr<CPCAPI2::test::AlianzaApiAccountFsm> fsm;
      int scenario = 0;

      // General error
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyCreateAccountState(): scenario: " << ++scenario << " ***** random error");
         setupCreateAccountState(alice, handle, fsm);
         AlianzaApiHttpResponseEvent evt(403, "");
         AlianzaApiTestHelper::createErrorResponse(403, "Random 403", evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_ReleaseNumber);

         evt.rc = 204;
         AlianzaApiTestHelper::createErrorResponse(204, "OK", evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);

         manager->disableAccount(handle);
         ASSERT_EQ(alice.alianzaApiAccountHandle, 0);
         fsm.reset();
         std::this_thread::sleep_for(std::chrono::milliseconds(3000));
         alice.alianzaApiEvents->clearCommands();
      }

      // 400 error but unhandled reason
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyCreateAccountState(): scenario: " << ++scenario << " ***** error 400 random reason");
         setupCreateAccountState(alice, handle, fsm);
         AlianzaApiHttpResponseEvent evt(400, "");
         AlianzaApiTestHelper::createErrorResponse(400, "Random 400", evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_ReleaseNumber);

         evt.rc = 204;
         AlianzaApiTestHelper::createErrorResponse(204, "OK", evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);

         manager->disableAccount(handle);
         ASSERT_EQ(alice.alianzaApiAccountHandle, 0);
         fsm.reset();
         std::this_thread::sleep_for(std::chrono::milliseconds(3000));
         alice.alianzaApiEvents->clearCommands();
      }

      // 400 error with particular DuplicateAccountNumber error, i.e. retry request after a delay, 2nd try successful
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyCreateAccountState(): scenario: " << ++scenario << " ***** error 400 reason DuplicateAccountNumber, i.e. failed will try again later");
         setupCreateAccountState(alice, handle, fsm);
         std::string accountNumber1 = session.accountNumber;
         AlianzaApiHttpResponseEvent evt(400, "");
         AlianzaApiTestHelper::createErrorResponse(400, "DuplicateAccountNumber", evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));

         evt.rc = 201;
         AlianzaApiTestHelper::createResponseWithId("Account-Id-123", evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_SetGroupName);
         evt.rc = 200;
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_AddUser);
         ASSERT_TRUE(fsm->hasAccountBeenCreatedInPartition());
         ASSERT_TRUE(accountNumber1.compare(session.accountNumber) != 0);

         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::disable, fsm));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_DeleteAccount);
         evt.rc = 204;
         AlianzaApiTestHelper::createErrorResponse(204, "OK", evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_ReleaseNumber);
         evt.rc = 204;
         AlianzaApiTestHelper::createErrorResponse(204, "OK", evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);

         manager->disableAccount(handle);
         ASSERT_EQ(alice.alianzaApiAccountHandle, 0);
         fsm.reset();
         std::this_thread::sleep_for(std::chrono::milliseconds(3000));
         alice.alianzaApiEvents->clearCommands();
      }

      // 400 error with particular DuplicateAccountNumber error, all attempts failed
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyCreateAccountState(): scenario: " << ++scenario << " ***** error 400 reason DuplicateAccountNumber - all attempts failed");
         setupCreateAccountState(alice, handle, fsm);
         std::string accountNumber1 = session.accountNumber;
         AlianzaApiHttpResponseEvent evt(400, "");
         AlianzaApiTestHelper::createErrorResponse(400, "DuplicateAccountNumber", evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         AlianzaApiTestHelper::createErrorResponse(400, "DuplicateAccountNumber", evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_ReleaseNumber);
         ASSERT_TRUE(accountNumber1.compare(session.accountNumber) != 0);

         evt.rc = 204;
         AlianzaApiTestHelper::createErrorResponse(204, "OK", evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);

         manager->disableAccount(handle);
         ASSERT_EQ(alice.alianzaApiAccountHandle, 0);
         fsm.reset();
         std::this_thread::sleep_for(std::chrono::milliseconds(3000));
         alice.alianzaApiEvents->clearCommands();
      }

      // 201 response, account created successfully
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyCreateAccountState(): scenario: " << ++scenario << " ***** 201 success response, i.e. account created successfully");
         setupCreateAccountState(alice, handle, fsm);
         AlianzaApiHttpResponseEvent evt(201, "OK");
         AlianzaApiTestHelper::createResponseWithId("Account-Id-123", evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_SetGroupName);
         evt.rc = 200;
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_AddUser);
         ASSERT_TRUE(fsm->hasAccountBeenCreatedInPartition());

         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::disable, fsm));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_DeleteAccount);
         evt.rc = 204;
         AlianzaApiTestHelper::createErrorResponse(204, "OK", evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_ReleaseNumber);
         evt.rc = 204;
         AlianzaApiTestHelper::createErrorResponse(204, "OK", evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);

         manager->disableAccount(handle);
         ASSERT_EQ(alice.alianzaApiAccountHandle, 0);
         fsm.reset();
         std::this_thread::sleep_for(std::chrono::milliseconds(3000));
         alice.alianzaApiEvents->clearCommands();
      }

      // Http timeout
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyCreateAccountState(): scenario: " << ++scenario << " ***** http timeout");
         setupCreateAccountState(alice, handle, fsm);
         assertAlianzaApiHttpResponseTimeoutEvent(alice);
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);

         manager->disableAccount(handle);
         ASSERT_EQ(alice.alianzaApiAccountHandle, 0);
         fsm.reset();
         std::this_thread::sleep_for(std::chrono::milliseconds(3000));
         alice.alianzaApiEvents->clearCommands();
      }
   }

   void SipuaAlianzaAccountModuleTest::setupAddUserState(TestAccount& account, AlianzaApiAccountHandle& handle, std::shared_ptr<CPCAPI2::test::AlianzaApiAccountFsm>& fsm)
   {
      setupCreateAccountState(account, handle, fsm);
      resip::MultiReactor& reactor = dynamic_cast<PhoneInterface*>(account.phone)->getSdkModuleThread();
      AlianzaSessionInfo& session = account.config.alianzaSession;
      AlianzaAccountConfig& config = account.config.alianzaConfig;
      std::shared_ptr<AlianzaSipUaInfo> ua = session.getUa();
      ua->userCreated = false;
      AlianzaApiHttpResponseEvent evt(201, "OK");
      AlianzaApiTestHelper::createResponseWithId("Account-Id-123", evt.response);
      reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
      assertAlianzaApiAccountStatusEvent(account, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_SetGroupName);
      evt.rc = 200;
      reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
      assertAlianzaApiAccountStatusEvent(account, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_AddUser);
   }

   TEST_F(SipuaAlianzaAccountModuleTest, CreateBackendAccountVerifyAddUserState)
   {
      GTEST_SKIP_REPRO();

      TestAccount alice("alice", Account_Init);
      std::shared_ptr<CPCAPI2::test::AlianzaApiManager> manager = alice.alianzaApiManager;
      resip::MultiReactor& reactor = dynamic_cast<PhoneInterface*>(alice.phone)->getSdkModuleThread();
      AlianzaSessionInfo& session = alice.config.alianzaSession;
      AlianzaAccountConfig& config = alice.config.alianzaConfig;
      std::shared_ptr<AlianzaSipUaInfo> ua = session.getUa();

      alice.alianzaApiManager->start(config.api);
      alice.alianzaApiManager->authorize();
      assertAuthorizationSuccess(alice.alianzaApiEvents);

      AlianzaApiAccountHandle handle = 0;
      std::shared_ptr<CPCAPI2::test::AlianzaApiAccountFsm> fsm;
      int scenario = 0;

      // General error
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyAddUserState(): scenario: " << ++scenario << " ***** random error");
         setupAddUserState(alice, handle, fsm);
         AlianzaApiHttpResponseEvent evt(403, "");
         AlianzaApiTestHelper::createErrorResponse(403, "Random 403", evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_DeleteAccount);
         SipuaAlianzaAccountModuleTest::cleanupNumber(alice, handle);
      }

      // 400 error with particular DuplicateUsername error, i.e. retry request after a delay, 2nd try successful
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyAddUserState(): scenario: " << ++scenario << " ***** error 400 reason DuplicateUsername, i.e. failed will try again later");
         setupAddUserState(alice, handle, fsm);
         std::string emailAddress = session.getUa()->emailAddress;
         std::string username = session.getUa()->username;
         std::string sipUsername = session.getUa()->sipUsername;
         std::string sipPassword = session.getUa()->sipPassword;
         AlianzaApiHttpResponseEvent evt(400, "");
         AlianzaApiTestHelper::createErrorResponse(400, "DuplicateUsername", evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));

         evt.rc = 201;
         AlianzaApiTestHelper::createResponseForAddUserRequest("User-Id-123", "Voicemail-Box-Id-123", "Calling-Plan-Product-Id-123", ua->extension, evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_UserAuth);
         ASSERT_TRUE(emailAddress.compare(session.getUa()->emailAddress) != 0);
         ASSERT_TRUE(username.compare(session.getUa()->username) != 0);
         ASSERT_TRUE(sipUsername.compare(session.getUa()->sipUsername) != 0);
         ASSERT_TRUE(sipPassword.compare(session.getUa()->sipPassword) != 0);

         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::disable, fsm));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_DeleteAccount);
         SipuaAlianzaAccountModuleTest::cleanupNumber(alice, handle);
      }

      // 201 response, user added successfully
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyAddUserState(): scenario: " << ++scenario << " ***** 201 success response, i.e. user added successfully");
         setupAddUserState(alice, handle, fsm);
         AlianzaApiHttpResponseEvent evt(201, "OK");
         AlianzaApiTestHelper::createResponseForAddUserRequest("User-Id-123", "Voicemail-Box-Id-123", "Calling-Plan-Product-Id-123", ua->extension, evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_UserAuth);

         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::disable, fsm));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_DeleteAccount);
         evt.rc = 204;
         AlianzaApiTestHelper::createErrorResponse(204, "OK", evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_ReleaseNumber);
         evt.rc = 204;
         AlianzaApiTestHelper::createErrorResponse(204, "OK", evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);

         manager->disableAccount(handle);
         ASSERT_EQ(alice.alianzaApiAccountHandle, 0);
         fsm.reset();
         std::this_thread::sleep_for(std::chrono::milliseconds(3000));
         alice.alianzaApiEvents->clearCommands();
      }

      // Http timeout
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyAddUserState(): scenario: " << ++scenario << " ***** http timeout");
         setupAddUserState(alice, handle, fsm);
         assertAlianzaApiHttpResponseTimeoutEvent(alice);
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);

         manager->disableAccount(handle);
         ASSERT_EQ(alice.alianzaApiAccountHandle, 0);
         fsm.reset();
         std::this_thread::sleep_for(std::chrono::milliseconds(3000));
         alice.alianzaApiEvents->clearCommands();
      }
   }

   void SipuaAlianzaAccountModuleTest::setupAddNumberState(TestAccount& account, AlianzaApiAccountHandle& handle, std::shared_ptr<CPCAPI2::test::AlianzaApiAccountFsm>& fsm)
   {
      setupAddUserState(account, handle, fsm);
      resip::MultiReactor& reactor = dynamic_cast<PhoneInterface*>(account.phone)->getSdkModuleThread();
      AlianzaSessionInfo& session = account.config.alianzaSession;
      AlianzaAccountConfig& config = account.config.alianzaConfig;
      std::shared_ptr<AlianzaSipUaInfo> ua = session.getUa();
      AlianzaApiHttpResponseEvent evt(201, "OK");
      AlianzaApiTestHelper::createResponseForAddUserRequest("User-Id-123", "Voicemail-Box-Id-123", "Calling-Plan-Product-Id-123", ua->extension, evt.response);
      reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
      assertAlianzaApiAccountStatusEvent(account, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_UserAuth);
      evt.rc = 201;
      AlianzaApiTestHelper::createResponseForUserAuthRequest("authToken1", evt.response);
      reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
      assertAlianzaApiAccountStatusEvent(account, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_QueryClientConfig);
      evt.rc = 200;
      AlianzaApiTestHelper::createResponseForQueryUserConfigRequest("sipUsername", "sipPassword", "sipDomain.net", evt.response);
      reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
      assertAlianzaApiAccountStatusEvent(account, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_AddNumber);
   }

   void SipuaAlianzaAccountModuleTest::addNumberWithError(TestAccount& account, AlianzaApiAccountHandle& handle, std::shared_ptr<CPCAPI2::test::AlianzaApiAccountFsm>& fsm, const std::string& errorStr)
   {
      resip::MultiReactor& reactor = dynamic_cast<PhoneInterface*>(account.phone)->getSdkModuleThread();
      std::shared_ptr<CPCAPI2::test::AlianzaApiManager> manager = account.alianzaApiManager;
      AlianzaSessionInfo& session = account.config.alianzaSession;

      setupAddNumberState(account, handle, fsm);
      std::string number1 = session.getUa()->phoneNumber;
      AlianzaApiHttpResponseEvent evt(400, "");
      AlianzaApiTestHelper::createErrorResponse(400, errorStr, evt.response);
      reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
      assertAlianzaApiIdentityResetEventVerifyNumber(account, number1);
      assertAlianzaApiAccountStatusEvent(account, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_QueryNumber);
      ASSERT_FALSE(fsm->hasNumberBeenReservedInPartition());

      evt.rc = 404;
      AlianzaApiTestHelper::createErrorResponseWithDataObject(404, "EntityNotFound", "TelephoneNumberInventory", session.getUa()->phoneNumber, evt.response);
      reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
      assertAlianzaApiAccountStatusEvent(account, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_ReserveNumber);

      evt.rc = 201;
      AlianzaApiTestHelper::createResponseWithId(session.getUa()->phoneNumber, evt.response);
      reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
      assertAlianzaApiAccountStatusEvent(account, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_AddNumber);
      ASSERT_TRUE(fsm->hasNumberBeenReservedInPartition());

      evt.rc = 201;
      evt.response = "";
      reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
      assertAlianzaApiAccountStatusEvent(account, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_CheckNumberInAccount);

      evt.rc = 200;
      AlianzaApiTestHelper::createResponseForCheckNumberInAccountRequest("ACTIVE", evt.response);
      reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
      assertAlianzaApiAccountStatusEvent(account, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_UpdateCallerId);

      reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::disable, fsm));
      assertAlianzaApiAccountStatusEvent(account, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_RemoveNumber);
      SipuaAlianzaAccountModuleTest::cleanupAccountAndNumber(account, handle);
   }

   TEST_F(SipuaAlianzaAccountModuleTest, CreateBackendAccountVerifyAddNumberState)
   {
      GTEST_SKIP_REPRO();

      TestAccount alice("alice", Account_Init);
      std::shared_ptr<CPCAPI2::test::AlianzaApiManager> manager = alice.alianzaApiManager;
      resip::MultiReactor& reactor = dynamic_cast<PhoneInterface*>(alice.phone)->getSdkModuleThread();
      AlianzaSessionInfo& session = alice.config.alianzaSession;
      AlianzaAccountConfig& config = alice.config.alianzaConfig;

      alice.alianzaApiManager->start(config.api);
      alice.alianzaApiManager->authorize();
      assertAuthorizationSuccess(alice.alianzaApiEvents);

      AlianzaApiAccountHandle handle = 0;
      std::shared_ptr<CPCAPI2::test::AlianzaApiAccountFsm> fsm;
      int scenario = 0;

      // General error
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyAddNumberState(): scenario: " << ++scenario << " ***** random error");
         setupAddNumberState(alice, handle, fsm);
         AlianzaApiHttpResponseEvent evt(403, "");
         AlianzaApiTestHelper::createErrorResponse(403, "Random 403", evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_DeleteAccount);
         SipuaAlianzaAccountModuleTest::cleanupNumber(alice, handle);
      }

      // 400 error but unhandled reason
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyAddNumberState(): scenario: " << ++scenario << " ***** error 400 random reason");
         setupAddNumberState(alice, handle, fsm);
         AlianzaApiHttpResponseEvent evt(400, "");
         AlianzaApiTestHelper::createErrorResponse(400, "Random 400", evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_DeleteAccount);
         SipuaAlianzaAccountModuleTest::cleanupNumber(alice, handle);
      }

      // 400 error with particular TNDisconnectEventInProgress error
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyAddNumberState(): scenario: " << ++scenario << " ***** error 400 reason TNDisconnectEventInProgress, will reset identity");
         addNumberWithError(alice, handle, fsm, "TNDisconnectEventInProgress");
      }

      // 400 error with particular PhoneNumberAlreadyExists error, i.e. retry request after a delay, 2nd try successful
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyAddNumberState(): scenario: " << ++scenario << " ***** error 400 reason PhoneNumberAlreadyExists, i.e. failed will try again later");
         addNumberWithError(alice, handle, fsm, "PhoneNumberAlreadyExists");
      }

      // 400 error with particular PhoneNumberAlreadyExists error, all attempts failed
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyAddNumberState(): scenario: " << ++scenario << " ***** error 400 reason PhoneNumberAlreadyExists - all attempts failed");
         setupAddNumberState(alice, handle, fsm);
         AlianzaApiHttpResponseEvent evt(400, "");
         AlianzaApiTestHelper::createErrorResponse(400, "PhoneNumberAlreadyExists", evt.response);

         for (int i = 0; i < ALIANZA_API_ADD_NUMBER_RETRIES; ++i)
         {
            std::string number = session.getUa()->phoneNumber;
            reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
            assertAlianzaApiIdentityResetEventVerifyNumber(alice, number);
            assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_QueryNumber);
            ASSERT_FALSE(fsm->hasNumberBeenReservedInPartition());

            AlianzaApiHttpResponseEvent evt2(404, "");
            AlianzaApiTestHelper::createErrorResponseWithDataObject(404, "EntityNotFound", "TelephoneNumberInventory", session.getUa()->phoneNumber, evt2.response);
            reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt2));
            assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_ReserveNumber);

            AlianzaApiHttpResponseEvent evt3(201, "");
            AlianzaApiTestHelper::createResponseWithId(session.getUa()->phoneNumber, evt3.response);
            reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt3));
            assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_AddNumber);
            ASSERT_TRUE(fsm->hasNumberBeenReservedInPartition());
         }

         evt.rc = 400;
         AlianzaApiTestHelper::createErrorResponse(400, "PhoneNumberAlreadyExists", evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_DeleteAccount);
         SipuaAlianzaAccountModuleTest::cleanupNumber(alice, handle);
      }

      // 201 response, number added successfully
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyAddNumberState(): scenario: " << ++scenario << " ***** 201 success response, i.e. number added successfully");
         setupAddNumberState(alice, handle, fsm);
         AlianzaApiHttpResponseEvent evt(201, "");
         ASSERT_TRUE(fsm->hasNumberBeenReservedInPartition());
         ASSERT_TRUE(fsm->hasAccountBeenCreatedInPartition());
         ASSERT_FALSE(fsm->hasNumberBeenAddedInAccount());

         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_CheckNumberInAccount);

         evt.rc = 200;
         AlianzaApiTestHelper::createResponseForCheckNumberInAccountRequest("ACTIVE", evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_UpdateCallerId);
         ASSERT_TRUE(fsm->hasNumberBeenAddedInAccount());

         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::disable, fsm));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_RemoveNumber);
         SipuaAlianzaAccountModuleTest::cleanupAccountAndNumber(alice, handle);
      }

      // Http timeout
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyAddNumberState(): scenario: " << ++scenario << " ***** http timeout");
         setupAddNumberState(alice, handle, fsm);
         assertAlianzaApiHttpResponseTimeoutEvent(alice);
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);

         manager->disableAccount(handle);
         ASSERT_EQ(alice.alianzaApiAccountHandle, 0);
         fsm.reset();
         std::this_thread::sleep_for(std::chrono::milliseconds(3000));
         alice.alianzaApiEvents->clearCommands();
      }
   }

   void SipuaAlianzaAccountModuleTest::setupUpdateCallerIdState(TestAccount& account, AlianzaApiAccountHandle& handle, std::shared_ptr<CPCAPI2::test::AlianzaApiAccountFsm>& fsm)
   {
      setupAddNumberState(account, handle, fsm);
      resip::MultiReactor& reactor = dynamic_cast<PhoneInterface*>(account.phone)->getSdkModuleThread();
      AlianzaSessionInfo& session = account.config.alianzaSession;
      AlianzaAccountConfig& config = account.config.alianzaConfig;
      AlianzaApiHttpResponseEvent evt(201, "");
      reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
      assertAlianzaApiAccountStatusEvent(account, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_CheckNumberInAccount);
      evt.rc = 200;
      AlianzaApiTestHelper::createResponseForCheckNumberInAccountRequest("ACTIVE", evt.response);
      reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
      assertAlianzaApiAccountStatusEvent(account, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_UpdateCallerId);
   }

   TEST_F(SipuaAlianzaAccountModuleTest, CreateBackendAccountVerifyUpdateCallerIdState)
   {
      GTEST_SKIP_REPRO();

      TestAccount alice("alice", Account_Init);
      std::shared_ptr<CPCAPI2::test::AlianzaApiManager> manager = alice.alianzaApiManager;
      resip::MultiReactor& reactor = dynamic_cast<PhoneInterface*>(alice.phone)->getSdkModuleThread();
      AlianzaSessionInfo& session = alice.config.alianzaSession;
      AlianzaAccountConfig& config = alice.config.alianzaConfig;

      alice.alianzaApiManager->start(config.api);
      alice.alianzaApiManager->authorize();
      assertAuthorizationSuccess(alice.alianzaApiEvents);

      AlianzaApiAccountHandle handle = 0;
      std::shared_ptr<CPCAPI2::test::AlianzaApiAccountFsm> fsm;
      int scenario = 0;

      // General error
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyUpdateCallerIdState(): scenario: " << ++scenario << " ***** random error");
         setupUpdateCallerIdState(alice, handle, fsm);
         assertAlianzaApiHttpDelayRequestTimeoutEvent(alice);

         AlianzaApiHttpResponseEvent evt(403, "");
         AlianzaApiTestHelper::createErrorResponse(403, "Random 403", evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_RemoveNumber);
         SipuaAlianzaAccountModuleTest::cleanupAccountAndNumber(alice, handle);
      }

      // 400 error but unhandled reason
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyUpdateCallerIdState(): scenario: " << ++scenario << " ***** error 400 random reason");
         setupUpdateCallerIdState(alice, handle, fsm);
         assertAlianzaApiHttpDelayRequestTimeoutEvent(alice);

         AlianzaApiHttpResponseEvent evt(400, "");
         AlianzaApiTestHelper::createErrorResponse(400, "Random 400", evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_RemoveNumber);
         SipuaAlianzaAccountModuleTest::cleanupAccountAndNumber(alice, handle);
      }

      // 400 error with particular InvalidStatus error, i.e. retry request after a delay, 2nd try successful
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyUpdateCallerIdState(): scenario: " << ++scenario << " ***** error 400 reason InvalidStatus, i.e. failed will try again later");
         setupUpdateCallerIdState(alice, handle, fsm);
         assertAlianzaApiHttpDelayRequestTimeoutEvent(alice);

         AlianzaApiHttpResponseEvent evt(400, "");
         AlianzaApiTestHelper::createErrorResponse(400, "InvalidStatus", evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiHttpDelayRequestTimeoutEvent(alice);

         evt.rc = 200;
         evt.response = "";
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_UpdateUser);

         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::disable, fsm));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_RemoveNumber);
         SipuaAlianzaAccountModuleTest::cleanupAccountAndNumber(alice, handle);
      }

      // 400 error with particular InvalidStatus error, all attempts failed
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyUpdateCallerIdState(): scenario: " << ++scenario << " ***** error 400 reason InvalidStatus - all attempts failed");
         setupUpdateCallerIdState(alice, handle, fsm);
         assertAlianzaApiHttpDelayRequestTimeoutEvent(alice);

         AlianzaApiHttpResponseEvent evt(400, "");
         AlianzaApiTestHelper::createErrorResponse(400, "InvalidStatus", evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiHttpDelayRequestTimeoutEvent(alice);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiHttpDelayRequestTimeoutEvent(alice);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiHttpDelayRequestTimeoutEvent(alice);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_RemoveNumber);
         SipuaAlianzaAccountModuleTest::cleanupAccountAndNumber(alice, handle);
      }

      // 200 response, caller-id update successfully
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyUpdateCallerIdState): scenario: " << ++scenario << " ***** 200 success response, i.e. caller-id updated successfully");
         setupUpdateCallerIdState(alice, handle, fsm);
         ASSERT_TRUE(fsm->hasNumberBeenReservedInPartition());
         ASSERT_TRUE(fsm->hasAccountBeenCreatedInPartition());
         ASSERT_TRUE(fsm->hasNumberBeenAddedInAccount());

         AlianzaApiHttpResponseEvent evt(200, "");
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_UpdateUser);

         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::disable, fsm));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_RemoveNumber);
         SipuaAlianzaAccountModuleTest::cleanupAccountAndNumber(alice, handle);
      }

      // Http timeout
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyUpdateCallerIdState(): scenario: " << ++scenario << " ***** http timeout");
         setupUpdateCallerIdState(alice, handle, fsm);
         assertAlianzaApiHttpResponseTimeoutEvent(alice);
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);

         manager->disableAccount(handle);
         ASSERT_EQ(alice.alianzaApiAccountHandle, 0);
         fsm.reset();
         std::this_thread::sleep_for(std::chrono::milliseconds(3000));
         alice.alianzaApiEvents->clearCommands();
      }
   }

   void SipuaAlianzaAccountModuleTest::setupUpdateUserState(TestAccount& account, AlianzaApiAccountHandle& handle, std::shared_ptr<CPCAPI2::test::AlianzaApiAccountFsm>& fsm)
   {
      setupUpdateCallerIdState(account, handle, fsm);
      resip::MultiReactor& reactor = dynamic_cast<PhoneInterface*>(account.phone)->getSdkModuleThread();
      AlianzaSessionInfo& session = account.config.alianzaSession;
      AlianzaAccountConfig& config = account.config.alianzaConfig;
      AlianzaApiHttpResponseEvent evt(200, "");
      reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
      assertAlianzaApiAccountStatusEvent(account, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_UpdateUser);
   }

   TEST_F(SipuaAlianzaAccountModuleTest, CreateBackendAccountVerifyUpdateUserState)
   {
      GTEST_SKIP_REPRO();

      TestAccount alice("alice", Account_Init);
      std::shared_ptr<CPCAPI2::test::AlianzaApiManager> manager = alice.alianzaApiManager;
      resip::MultiReactor& reactor = dynamic_cast<PhoneInterface*>(alice.phone)->getSdkModuleThread();
      AlianzaSessionInfo& session = alice.config.alianzaSession;
      AlianzaAccountConfig& config = alice.config.alianzaConfig;

      alice.alianzaApiManager->start(config.api);
      alice.alianzaApiManager->authorize();
      assertAuthorizationSuccess(alice.alianzaApiEvents);

      AlianzaApiAccountHandle handle = 0;
      std::shared_ptr<CPCAPI2::test::AlianzaApiAccountFsm> fsm;
      int scenario = 0;

      // General error
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyUpdateUserState(): scenario: " << ++scenario << " ***** random error");
         setupUpdateUserState(alice, handle, fsm);
         AlianzaApiHttpResponseEvent evt(403, "");
         AlianzaApiTestHelper::createErrorResponse(403, "Random 403", evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_RemoveNumber);
         AlianzaApiTestHelper::createErrorResponse(204, "OK", evt.response);
         SipuaAlianzaAccountModuleTest::cleanupAccountAndNumber(alice, handle);
      }

      // 200 response, user update successfully
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyUpdateUserState): scenario: " << ++scenario << " ***** 200 success response, i.e. user updated successfully");
         setupUpdateUserState(alice, handle, fsm);
         ASSERT_TRUE(fsm->hasNumberBeenReservedInPartition());
         ASSERT_TRUE(fsm->hasAccountBeenCreatedInPartition());
         ASSERT_TRUE(fsm->hasNumberBeenAddedInAccount());

         AlianzaApiHttpResponseEvent evt(200, "");
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Enabled);

         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::disable, fsm));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_RemoveNumber);
         SipuaAlianzaAccountModuleTest::cleanupAccountAndNumber(alice, handle);
      }

      // Http timeout
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyUpdateUserState(): scenario: " << ++scenario << " ***** http timeout");
         setupUpdateUserState(alice, handle, fsm);
         assertAlianzaApiHttpResponseTimeoutEvent(alice);
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);

         manager->disableAccount(handle);
         ASSERT_EQ(alice.alianzaApiAccountHandle, 0);
         fsm.reset();
         std::this_thread::sleep_for(std::chrono::milliseconds(3000));
         alice.alianzaApiEvents->clearCommands();
      }
   }

   void SipuaAlianzaAccountModuleTest::setupEnabledState(TestAccount& account, AlianzaApiAccountHandle& handle, std::shared_ptr<CPCAPI2::test::AlianzaApiAccountFsm>& fsm)
   {
      setupUpdateUserState(account, handle, fsm);
      resip::MultiReactor& reactor = dynamic_cast<PhoneInterface*>(account.phone)->getSdkModuleThread();
      AlianzaSessionInfo& session = account.config.alianzaSession;
      AlianzaAccountConfig& config = account.config.alianzaConfig;
      AlianzaApiHttpResponseEvent evt(200, "");
      reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
      assertAlianzaApiAccountStatusEvent(account, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Enabled);
   }

   TEST_F(SipuaAlianzaAccountModuleTest, CreateBackendAccountVerifyEnabledState)
   {
      GTEST_SKIP_REPRO();

      TestAccount alice("alice", Account_Init);
      std::shared_ptr<CPCAPI2::test::AlianzaApiManager> manager = alice.alianzaApiManager;
      resip::MultiReactor& reactor = dynamic_cast<PhoneInterface*>(alice.phone)->getSdkModuleThread();
      AlianzaSessionInfo& session = alice.config.alianzaSession;
      AlianzaAccountConfig& config = alice.config.alianzaConfig;

      alice.alianzaApiManager->start(config.api);
      alice.alianzaApiManager->authorize();
      assertAuthorizationSuccess(alice.alianzaApiEvents);

      AlianzaApiAccountHandle handle = 0;
      std::shared_ptr<CPCAPI2::test::AlianzaApiAccountFsm> fsm;
      int scenario = 0;

      // General error
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyEnabledState(): scenario: " << ++scenario << " ***** random error");
         setupEnabledState(alice, handle, fsm);
         AlianzaApiHttpResponseEvent evt(403, "");
         AlianzaApiTestHelper::createErrorResponse(403, "Random 403", evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));

         {
            AlianzaApiAccountStatusEvent evt;
            ASSERT_FALSE(alice.alianzaApiEvents->expectEvent(__LINE__, "AlianzaApiHandler::onAlianzaApiAccountStatusEvent", 5000, AlwaysTruePred(), handle, evt));
         }

         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::disable, fsm));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_RemoveNumber);
         SipuaAlianzaAccountModuleTest::cleanupAccountAndNumber(alice, handle);
      }

      // Http timeout
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyEnabledState(): scenario: " << ++scenario << " ***** http timeout");
         setupEnabledState(alice, handle, fsm);
         AlianzaApiHttpResponseTimeoutEvent evt;
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponseTimeout, fsm, 0, evt));

         {
            AlianzaApiAccountStatusEvent evt;
            ASSERT_FALSE(alice.alianzaApiEvents->expectEvent(__LINE__, "AlianzaApiHandler::onAlianzaApiAccountStatusEvent", 5000, AlwaysTruePred(), handle, evt));
         }

         {
            reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::disable, fsm));
            assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_RemoveNumber);
            SipuaAlianzaAccountModuleTest::cleanupAccountAndNumber(alice, handle);
         }
      }
   }

   void SipuaAlianzaAccountModuleTest::setupRemoveNumberState(TestAccount& account, AlianzaApiAccountHandle& handle, std::shared_ptr<CPCAPI2::test::AlianzaApiAccountFsm>& fsm)
   {
      setupEnabledState(account, handle, fsm);
      resip::MultiReactor& reactor = dynamic_cast<PhoneInterface*>(account.phone)->getSdkModuleThread();
      AlianzaSessionInfo& session = account.config.alianzaSession;
      AlianzaAccountConfig& config = account.config.alianzaConfig;
      reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::disable, fsm));
      assertAlianzaApiAccountStatusEvent(account, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_RemoveNumber);
   }

   TEST_F(SipuaAlianzaAccountModuleTest, CreateBackendAccountVerifyRemoveNumberState)
   {
      GTEST_SKIP_REPRO();

      TestAccount alice("alice", Account_Init);
      std::shared_ptr<CPCAPI2::test::AlianzaApiManager> manager = alice.alianzaApiManager;
      resip::MultiReactor& reactor = dynamic_cast<PhoneInterface*>(alice.phone)->getSdkModuleThread();
      AlianzaSessionInfo& session = alice.config.alianzaSession;
      AlianzaAccountConfig& config = alice.config.alianzaConfig;

      alice.alianzaApiManager->start(config.api);
      alice.alianzaApiManager->authorize();
      assertAuthorizationSuccess(alice.alianzaApiEvents);

      AlianzaApiAccountHandle handle = 0;
      std::shared_ptr<CPCAPI2::test::AlianzaApiAccountFsm> fsm;
      int scenario = 0;

      // General error
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyRemoveNumberState(): scenario: " << ++scenario << " ***** random error");
         setupRemoveNumberState(alice, handle, fsm);

         AlianzaApiHttpResponseEvent evt(403, "");
         AlianzaApiTestHelper::createErrorResponse(403, "Random 403", evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);

         manager->disableAccount(handle);
         ASSERT_EQ(alice.alianzaApiAccountHandle, 0);
         fsm.reset();
         std::this_thread::sleep_for(std::chrono::milliseconds(3000));
         alice.alianzaApiEvents->clearCommands();
      }

      // 400 error but unhandled reason
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyRemoveNumberState(): scenario: " << ++scenario << " ***** error 400 random reason");
         setupRemoveNumberState(alice, handle, fsm);

         AlianzaApiHttpResponseEvent evt(400, "");
         AlianzaApiTestHelper::createErrorResponse(400, "Random 400", evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);

         manager->disableAccount(handle);
         ASSERT_EQ(alice.alianzaApiAccountHandle, 0);
         fsm.reset();
         std::this_thread::sleep_for(std::chrono::milliseconds(3000));
         alice.alianzaApiEvents->clearCommands();
      }

      // 400 error with particular ServiceActivationEventInProgress error, i.e. retry request after a delay, 2nd try successful
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyRemoveNumberState(): scenario: " << ++scenario << " ***** error 400 reason ServiceActivationEventInProgress, i.e. failed will try again later");
         setupRemoveNumberState(alice, handle, fsm);

         AlianzaApiHttpResponseEvent evt(400, "");
         AlianzaApiTestHelper::createErrorResponse(400, "ServiceActivationEventInProgress", evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiHttpDelayRequestTimeoutEvent(alice);
         SipuaAlianzaAccountModuleTest::cleanupAccountAndNumber(alice, handle);
      }

      // 400 error with particular ServiceActivationEventInProgress error, all attempts failed
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyRemoveNumberState(): scenario: " << ++scenario << " ***** error 400 reason ServiceActivationEventInProgress - all attempts failed");
         setupRemoveNumberState(alice, handle, fsm);

         AlianzaApiHttpResponseEvent evt(400, "");
         AlianzaApiTestHelper::createErrorResponse(400, "ServiceActivationEventInProgress", evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiHttpDelayRequestTimeoutEvent(alice);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiHttpDelayRequestTimeoutEvent(alice);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);

         manager->disableAccount(handle);
         ASSERT_EQ(alice.alianzaApiAccountHandle, 0);
         fsm.reset();
         std::this_thread::sleep_for(std::chrono::milliseconds(3000));
         alice.alianzaApiEvents->clearCommands();
      }

      // 200 response, removed number successfully
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyRemoveNumberState): scenario: " << ++scenario << " ***** 204 success response, i.e. removed number successfully");
         setupRemoveNumberState(alice, handle, fsm);
         ASSERT_TRUE(fsm->hasNumberBeenReservedInPartition());
         ASSERT_TRUE(fsm->hasAccountBeenCreatedInPartition());
         ASSERT_TRUE(fsm->hasNumberBeenAddedInAccount());

         AlianzaApiHttpResponseEvent evt(204, "");
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_DeleteAccount);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         ASSERT_FALSE(fsm->hasNumberBeenAddedInAccount());
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_ReleaseNumber);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);

         manager->disableAccount(handle);
         ASSERT_EQ(alice.alianzaApiAccountHandle, 0);
         fsm.reset();
         std::this_thread::sleep_for(std::chrono::milliseconds(3000));
         alice.alianzaApiEvents->clearCommands();
      }

      // Http timeout
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyRemoveNumberState(): scenario: " << ++scenario << " ***** http timeout");
         setupRemoveNumberState(alice, handle, fsm);
         assertAlianzaApiHttpResponseTimeoutEvent(alice);
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);

         manager->disableAccount(handle);
         ASSERT_EQ(alice.alianzaApiAccountHandle, 0);
         fsm.reset();
         std::this_thread::sleep_for(std::chrono::milliseconds(3000));
         alice.alianzaApiEvents->clearCommands();
      }
   }

   void SipuaAlianzaAccountModuleTest::setupDeleteAccountState(TestAccount& account, AlianzaApiAccountHandle& handle, std::shared_ptr<CPCAPI2::test::AlianzaApiAccountFsm>& fsm)
   {
      setupRemoveNumberState(account, handle, fsm);
      resip::MultiReactor& reactor = dynamic_cast<PhoneInterface*>(account.phone)->getSdkModuleThread();
      AlianzaSessionInfo& session = account.config.alianzaSession;
      AlianzaAccountConfig& config = account.config.alianzaConfig;
      AlianzaApiHttpResponseEvent evt(204, "");
      reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
      assertAlianzaApiAccountStatusEvent(account, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_DeleteAccount);
   }

   TEST_F(SipuaAlianzaAccountModuleTest, CreateBackendAccountVerifyDeleteAccountState)
   {
      GTEST_SKIP_REPRO();

      TestAccount alice("alice", Account_Init);
      std::shared_ptr<CPCAPI2::test::AlianzaApiManager> manager = alice.alianzaApiManager;
      resip::MultiReactor& reactor = dynamic_cast<PhoneInterface*>(alice.phone)->getSdkModuleThread();
      AlianzaSessionInfo& session = alice.config.alianzaSession;
      AlianzaAccountConfig& config = alice.config.alianzaConfig;

      alice.alianzaApiManager->start(config.api);
      alice.alianzaApiManager->authorize();
      assertAuthorizationSuccess(alice.alianzaApiEvents);

      AlianzaApiAccountHandle handle = 0;
      std::shared_ptr<CPCAPI2::test::AlianzaApiAccountFsm> fsm;
      int scenario = 0;

      // General error
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyDeleteAccountState(): scenario: " << ++scenario << " ***** random error");
         setupDeleteAccountState(alice, handle, fsm);

         AlianzaApiHttpResponseEvent evt(403, "");
         AlianzaApiTestHelper::createErrorResponse(403, "Random 403", evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);

         manager->disableAccount(handle);
         ASSERT_EQ(alice.alianzaApiAccountHandle, 0);
         fsm.reset();
         std::this_thread::sleep_for(std::chrono::milliseconds(3000));
         alice.alianzaApiEvents->clearCommands();
      }

      // 400 error but unhandled reason
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyDeleteAccountState(): scenario: " << ++scenario << " ***** error 400 random reason");
         setupDeleteAccountState(alice, handle, fsm);

         AlianzaApiHttpResponseEvent evt(400, "");
         AlianzaApiTestHelper::createErrorResponse(400, "Random 400", evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);

         manager->disableAccount(handle);
         ASSERT_EQ(alice.alianzaApiAccountHandle, 0);
         fsm.reset();
         std::this_thread::sleep_for(std::chrono::milliseconds(3000));
         alice.alianzaApiEvents->clearCommands();
      }

      // 400 error with particular TelephoneNumbersExist error, i.e. retry request after a delay, 2nd try successful
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyDeleteAccountState(): scenario: " << ++scenario << " ***** error 400 reason TelephoneNumbersExist, i.e. failed will try again later");
         setupDeleteAccountState(alice, handle, fsm);

         AlianzaApiHttpResponseEvent evt(400, "");
         AlianzaApiTestHelper::createErrorResponse(400, "TelephoneNumbersExist", evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiHttpDelayRequestTimeoutEvent(alice);
         SipuaAlianzaAccountModuleTest::cleanupNumber(alice, handle);
      }

      // 400 error with particular TelephoneNumbersExist error, all attempts failed
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyDeleteAccountState(): scenario: " << ++scenario << " ***** error 400 reason TelephoneNumbersExist - all attempts failed");
         setupDeleteAccountState(alice, handle, fsm);

         AlianzaApiHttpResponseEvent evt(400, "");
         AlianzaApiTestHelper::createErrorResponse(400, "TelephoneNumbersExist", evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiHttpDelayRequestTimeoutEvent(alice);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);

         manager->disableAccount(handle);
         ASSERT_EQ(alice.alianzaApiAccountHandle, 0);
         fsm.reset();
         std::this_thread::sleep_for(std::chrono::milliseconds(3000));
         alice.alianzaApiEvents->clearCommands();
      }

      // 204 response, deleted account successfully
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyDeleteAccountState): scenario: " << ++scenario << " ***** 204 success response, i.e. deleted account successfully");
         setupDeleteAccountState(alice, handle, fsm);
         ASSERT_TRUE(fsm->hasNumberBeenReservedInPartition());
         ASSERT_TRUE(fsm->hasAccountBeenCreatedInPartition());
         ASSERT_FALSE(fsm->hasNumberBeenAddedInAccount());
         SipuaAlianzaAccountModuleTest::cleanupNumber(alice, handle);
      }

      // Http timeout
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyDeleteAccountState(): scenario: " << ++scenario << " ***** http timeout");
         setupDeleteAccountState(alice, handle, fsm);
         assertAlianzaApiHttpResponseTimeoutEvent(alice);
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);

         manager->disableAccount(handle);
         ASSERT_EQ(alice.alianzaApiAccountHandle, 0);
         fsm.reset();
         std::this_thread::sleep_for(std::chrono::milliseconds(3000));
         alice.alianzaApiEvents->clearCommands();
      }
   }

   void SipuaAlianzaAccountModuleTest::setupReleaseNumberState(TestAccount& account, AlianzaApiAccountHandle& handle, std::shared_ptr<CPCAPI2::test::AlianzaApiAccountFsm>& fsm)
   {
      setupDeleteAccountState(account, handle, fsm);
      resip::MultiReactor& reactor = dynamic_cast<PhoneInterface*>(account.phone)->getSdkModuleThread();
      AlianzaSessionInfo& session = account.config.alianzaSession;
      AlianzaAccountConfig& config = account.config.alianzaConfig;
      AlianzaApiHttpResponseEvent evt(204, "");
      reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
      assertAlianzaApiAccountStatusEvent(account, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_ReleaseNumber);
   }

   TEST_F(SipuaAlianzaAccountModuleTest, CreateBackendAccountVerifyReleaseNumberState)
   {
      GTEST_SKIP_REPRO();

      TestAccount alice("alice", Account_Init);
      std::shared_ptr<CPCAPI2::test::AlianzaApiManager> manager = alice.alianzaApiManager;
      resip::MultiReactor& reactor = dynamic_cast<PhoneInterface*>(alice.phone)->getSdkModuleThread();
      AlianzaSessionInfo& session = alice.config.alianzaSession;
      AlianzaAccountConfig& config = alice.config.alianzaConfig;

      alice.alianzaApiManager->start(config.api);
      alice.alianzaApiManager->authorize();
      assertAuthorizationSuccess(alice.alianzaApiEvents);

      AlianzaApiAccountHandle handle = 0;
      std::shared_ptr<CPCAPI2::test::AlianzaApiAccountFsm> fsm;
      int scenario = 0;

      // General error
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyReleaseNumberState(): scenario: " << ++scenario << " ***** random error");
         setupReleaseNumberState(alice, handle, fsm);

         AlianzaApiHttpResponseEvent evt(403, "");
         AlianzaApiTestHelper::createErrorResponse(403, "Random 403", evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);

         manager->disableAccount(handle);
         ASSERT_EQ(alice.alianzaApiAccountHandle, 0);
         fsm.reset();
         std::this_thread::sleep_for(std::chrono::milliseconds(3000));
         alice.alianzaApiEvents->clearCommands();
      }

      // 204 response, release number successfully
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyReleaseNumberState): scenario: " << ++scenario << " ***** 204 success response, i.e. release number successfully");
         setupReleaseNumberState(alice, handle, fsm);
         ASSERT_TRUE(fsm->hasNumberBeenReservedInPartition());
         ASSERT_FALSE(fsm->hasAccountBeenCreatedInPartition());
         ASSERT_FALSE(fsm->hasNumberBeenAddedInAccount());

         AlianzaApiHttpResponseEvent evt(204, "");
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);

         manager->disableAccount(handle);
         ASSERT_EQ(alice.alianzaApiAccountHandle, 0);
         fsm.reset();
         std::this_thread::sleep_for(std::chrono::milliseconds(3000));
         alice.alianzaApiEvents->clearCommands();
      }

      // Http timeout
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyReleaseNumberState(): scenario: " << ++scenario << " ***** http timeout");
         setupReleaseNumberState(alice, handle, fsm);
         assertAlianzaApiHttpResponseTimeoutEvent(alice);
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);

         manager->disableAccount(handle);
         ASSERT_EQ(alice.alianzaApiAccountHandle, 0);
         fsm.reset();
         std::this_thread::sleep_for(std::chrono::milliseconds(3000));
         alice.alianzaApiEvents->clearCommands();
      }
   }

   std::string SipuaAlianzaAccountModuleTest::reserveNumber(TestAccount& account)
   {
      std::string number("");

      {
         AlianzaAccountConfig& config = account.config.alianzaConfig;
         AlianzaSessionInfo& session = account.config.alianzaSession;

         int tries = 10;
         for (int i = 0; i < 10; ++i)
         {
            AlianzaApiHttpResponseEvent evt;
            AlianzaApiHandle handle = 0;
            std::string messageBody("");
            std::string url("");
            AlianzaApiTestHelper::createMessageToAddNumberInPartition(config, session, url, messageBody);

            account.alianzaApiManager->sendCreateMessage(url.c_str(), messageBody.c_str());
            cpcExpectEvent(account.alianzaApiEvents, "AlianzaApiHandler::onAlianzaApiHttpResponse", 5000, CPCAPI2::test::AlwaysTruePred(), handle, evt);
            if (evt.rc == 201)
            {
               number = session.getUa()->phoneNumber;
               session.getUa()->phoneId = session.getUa()->phoneNumber;
               break;
            }
            else
            {
               tries--;
               if (tries != 0)
               {
                  session.getUa()->initIdentity(account.config.alianzaConfig);
               }
               safeCout("SipuaAlianzaAccountModuleTest::reserveNumber(): error response: " << evt.rc << " in reserving number in partition, retries pending: " << tries);
            }
         }
      }

      return number;
   }

   void SipuaAlianzaAccountModuleTest::setupQueryUserConfigState(TestAccount& account, AlianzaApiAccountHandle& handle, std::shared_ptr<CPCAPI2::test::AlianzaApiAccountFsm>& fsm)
   {
      setupAddUserState(account, handle, fsm);
      resip::MultiReactor& reactor = dynamic_cast<PhoneInterface*>(account.phone)->getSdkModuleThread();
      AlianzaSessionInfo& session = account.config.alianzaSession;
      AlianzaAccountConfig& config = account.config.alianzaConfig;
      std::shared_ptr<AlianzaSipUaInfo> ua = session.getUa();
      AlianzaApiHttpResponseEvent evt(201, "OK");
      AlianzaApiTestHelper::createResponseForAddUserRequest("User-Id-123", "Voicemail-Box-Id-123", "Calling-Plan-Product-Id-123", ua->extension, evt.response);
      reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
      assertAlianzaApiAccountStatusEvent(account, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_UserAuth);
      evt.rc = 201;
      AlianzaApiTestHelper::createResponseForUserAuthRequest("authToken1", evt.response);
      reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
      assertAlianzaApiAccountStatusEvent(account, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_QueryClientConfig);
   }

   TEST_F(SipuaAlianzaAccountModuleTest, CreateBackendAccountVerifyQueryUserConfigState)
   {
      GTEST_SKIP_REPRO();

      TestAccount alice("alice", Account_Init);
      std::shared_ptr<CPCAPI2::test::AlianzaApiManager> manager = alice.alianzaApiManager;
      resip::MultiReactor& reactor = dynamic_cast<PhoneInterface*>(alice.phone)->getSdkModuleThread();
      AlianzaSessionInfo& session = alice.config.alianzaSession;
      AlianzaAccountConfig& config = alice.config.alianzaConfig;

      alice.alianzaApiManager->start(config.api);
      alice.alianzaApiManager->authorize();
      assertAuthorizationSuccess(alice.alianzaApiEvents);

      AlianzaApiAccountHandle handle = 0;
      std::shared_ptr<CPCAPI2::test::AlianzaApiAccountFsm> fsm;
      int scenario = 0;

      // 403 error
      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyQueryUserConfigState(): scenario: " << ++scenario << " ***** 403 error");
         setupQueryUserConfigState(alice, handle, fsm);
         AlianzaApiHttpResponseEvent evt(403, "");
         AlianzaApiTestHelper::createErrorResponse(403, "Forbidden", evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiHttpDelayRequestTimeoutEvent(alice);
         evt.rc = 200;
         AlianzaApiTestHelper::createResponseForQueryUserConfigRequest("sipUsername", "sipPassword", "sipDomain.net", evt.response);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEvent(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_AddNumber);

         manager->disableAccount(handle);
         ASSERT_EQ(alice.alianzaApiAccountHandle, 0);
         fsm.reset();
         std::this_thread::sleep_for(std::chrono::milliseconds(3000));
         alice.alianzaApiEvents->clearCommands();
      }

      {
         safeCout("SipuaAlianzaAccountModuleTest::CreateBackendAccountVerifyQueryUserConfigState(): scenario: " << ++scenario << " ***** 403 error repeated");

         AlianzaApiHttpResponseEvent evt(403, "");
         AlianzaApiTestHelper::createErrorResponse(403, "Forbidden", evt.response);

         std::set<AlianzaApiAccountFsmStateType> allowedPrevStates;
         allowedPrevStates.insert(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_DeleteAccount);

         setupQueryUserConfigState(alice, handle, fsm);

         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiHttpDelayRequestTimeoutEvent(alice);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiHttpDelayRequestTimeoutEvent(alice);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiHttpDelayRequestTimeoutEvent(alice);
         reactor.post(resip::resip_bind(&AlianzaApiAccountFsm::onAlianzaApiHttpResponse, fsm, 0, evt));
         assertAlianzaApiAccountStatusEventAllowPrecedingEvents(alice, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle, allowedPrevStates);

         manager->disableAccount(handle);
         ASSERT_EQ(alice.alianzaApiAccountHandle, 0);
         fsm.reset();
         std::this_thread::sleep_for(std::chrono::milliseconds(3000));
         alice.alianzaApiEvents->clearCommands();
      }
   }
}
