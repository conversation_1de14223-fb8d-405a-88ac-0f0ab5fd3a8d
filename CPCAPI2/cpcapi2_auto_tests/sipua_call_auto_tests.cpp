#include "cpcapi2_test_fixture.h"
#include "test_account_events.h"
#include "test_events.h"
#include "test_call_events.h"
#include "sipua_alianza_api_test_fixture.h"

#include "test_framework/http_test_framework.h"

#include "impl/account/SipAccountManagerInternal.h"
#include "impl/account/SipAccountHandlerInternal.h"
#include "impl/account/SipAccountAwareFeature.h"

#include <sstream>

#include "util/CurlPPHelper.h"
#include "util/CurlPPProgress.h"
#include "util/CurlPPSSL.h"
#include "util/CurlURI.h"

// rapidjson
#include <writer.h>
#include <stringbuffer.h>
#include <document.h>


#include "alianza_api/interface/public/alianza_api_handler.h"
#include "alianza_api/interface/public/alianza_api_types.h"
#include "alianza_api/interface/public/alianza_api_manager.h"
#include "sipua_alianza_api_test_events.h"

#include <sstream>
#include <atomic>

#include "util/CurlPPHelper.h"
#include "util/CurlPPProgress.h"
#include "util/CurlPPSSL.h"
#include "util/CurlURI.h"

// rapidjson
#include <writer.h>
#include <stringbuffer.h>
#include <document.h>


using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::SipConversation;
using namespace std::chrono;
using namespace curlpp::options;

#define GTEST_SKIP_REPRO(args) if (TestEnvironmentConfig::testEnvironmentId() == "repro") { GTEST_SKIP(); }

namespace
{
   class SipuaCallModuleTest : public CpcapiAutoTest
   {
   public:
      SipuaCallModuleTest() {}
      virtual ~SipuaCallModuleTest() {}

      void setAucaAccountSettings(TestAccount& account) {
         account.config.settings.enableNat64Support = false;
         account.config.settings.registrationIntervalSeconds = 3600;
         account.config.settings.sipTransportType = 1;
         account.config.settings.ipVersion = IpVersion::IpVersion_V4;
         account.config.settings.useInstanceId = 0;
         account.config.settings.useOutbound = 0;
         account.config.settings.useRinstance = 1;
         account.config.settings.preferPAssertedIdentity = 1;
         account.config.settings.sipQosSettings = 40;
         account.config.settings.sslVersion = SSLVersion::SSL_HIGHEST;
         account.config.settings.cipherSuite = "TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256:TLS_AES_128_GCM_SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-SHA384:ECDHE-RSA-AES128-SHA256:DHE-RSA-AES256-SHA256:DHE-RSA-AES128-SHA256:ECDHE-ECDSA-AES256-SHA384:ECDHE-ECDSA-AES128-SHA256:ECDHE-RSA-AES256-SHA:ECDHE-RSA-AES128-SHA:AES256-GCM-SHA384:AES128-GCM-SHA256:AES256-SHA256:AES128-SHA256:AES256-SHA:AES128-SHA:ECDHE-ECDSA-AES256-SHA:ECDHE-ECDSA-AES128-SHA:DHE-RSA-AES256-SHA:DHE-RSA-AES128-SHA";
      }

      void setAucaAccountSettingsAndEnable(TestAccount& account) {
         setAucaAccountSettings(account);
         account.init();
         account.enable();
      }

      void setAucaConversationSettings(TestAccount& account) {
         SipConversationSettings settings;
         settings.natTraversalMode = NatTraversalMode_None;
         settings.natTraversalServerSource = NatTraversalServerSource_None;
         settings.natTraversalServerType = NatTraversalServerType_StunOnly;
         account.conversation->setDefaultSettings(account.handle, settings);
      }
   };


   TEST_F(SipuaCallModuleTest, BasicCall)
   {
      
      TestAccount alice("alice", Account_NoInit);
      setAucaAccountSettingsAndEnable(alice);
      if (HasFatalFailure()) return;
      TestAccount bob("bob", Account_NoInit);
      setAucaAccountSettingsAndEnable(bob);
      if (HasFatalFailure()) return;

      std::atomic_bool bobMediaFlowChecked = false;

      // Alice calls Bob
      CPCAPI2::SipConversation::SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      
      setAucaConversationSettings(alice);
      setAucaConversationSettings(bob);

      alice.conversation->addParticipant(aliceCall, bob.uri().c_str());
      alice.conversation->start(aliceCall);

      auto aliceConversationEvents = std::async(std::launch::async, [&] ()
      {
         assertNewConversationOutgoing(alice, aliceCall, bob.uri().c_str());
         assertConversationStateChangedUptilConnected(alice, aliceCall); // Seen Ringing, Early, Connected at this stage

         std::this_thread::sleep_for(std::chrono::milliseconds(7000));
         assertMediaFlowing(alice, aliceCall, true, false);
         for (int i = 0; i < 10; ++i)
         {
            // wait until bob is done checking since proceeding to end the call before bob has checked
            // can cause bob's media flowing check to fail
            if (bobMediaFlowChecked)
            {
               break;
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
         }
         alice.conversation->end(aliceCall);
         assertConversationEnded(alice, aliceCall, CPCAPI2::SipConversation::ConversationEndReason_UserTerminatedLocally);
      });

      auto bobConversationEvents = std::async(std::launch::async, [&] ()
      {
         CPCAPI2::SipConversation::SipConversationHandle bobCall;
         {
            CPCAPI2::SipConversation::NewConversationEvent evt;
            ASSERT_TRUE(bob.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onNewConversation", 30000, AlwaysTruePred(), bobCall, evt));
            ASSERT_EQ(CPCAPI2::SipConversation::ConversationType_Incoming, evt.conversationType);
         }
         bob.conversation->sendRingingResponse(bobCall);
         assertConversationStateChanged(bob, bobCall, CPCAPI2::SipConversation::ConversationState_LocalRinging);
         bob.conversation->accept(bobCall);
         assertConversationMediaChanged(bob, bobCall, CPCAPI2::SipConversation::MediaDirection_SendReceive);
         assertConversationStateChanged(bob, bobCall, CPCAPI2::SipConversation::ConversationState_Connected);
         std::this_thread::sleep_for(std::chrono::milliseconds(7000));
         assertMediaFlowing(bob, bobCall, true, false);
         bobMediaFlowChecked = true;
         assertConversationEnded(bob, bobCall, CPCAPI2::SipConversation::ConversationEndReason_UserTerminatedRemotely);
      });

      waitFor2Ms(aliceConversationEvents, bobConversationEvents, std::chrono::milliseconds(45000));

      alice.destroyAlianzaAccountInBackend();
      bob.destroyAlianzaAccountInBackend();
   }

   TEST_F(SipuaCallModuleTest, BasicCallWithExtensions)
   {
      GTEST_SKIP_REPRO();
      // TODO:
      // The creation of the mock test account is solely to create the backend accounts. This workaround is
      // required only for extension based dialing, as extension calling is only possibly between sipua
      // extensions belonging to the same account. This approach is a temporary workaround until the
      // ownership of the backend extension can be managed by the test account created to use the extension.
      TestAccount mock("mock", Account_NoInit);
      mock.config.alianzaSession.numberEnabled = false;
      std::shared_ptr<AlianzaSipUaInfo> bobUa = mock.config.alianzaSession.addUa(mock.config.alianzaConfig, "bob");
      std::shared_ptr<AlianzaSipUaInfo> maxUa = mock.config.alianzaSession.addUa(mock.config.alianzaConfig, "max");

      mock.init();
      ASSERT_NO_FATAL_FAILURE(mock.createAlianzaAccountInBackend());

      TestAccount bob("bob", Account_NoInit);
      bob.skipAutoCreateAlianzaAccountInBackend();
      setAucaAccountSettings(bob);
      if (HasFatalFailure()) return;
      TestAccount max("max", Account_NoInit);
      max.skipAutoCreateAlianzaAccountInBackend();
      setAucaAccountSettings(max);
      if (HasFatalFailure()) return;

      ASSERT_NO_FATAL_FAILURE(bob.applyAlianzaExtensionUa(*bobUa, mock.config.alianzaSession));
      ASSERT_NO_FATAL_FAILURE(max.applyAlianzaExtensionUa(*maxUa, mock.config.alianzaSession));

      bob.init();
      max.init();

      // we need to manually start alianzaApiManager and trigger authentication so bob and max's
      // alianzaApiManager instances have an auth token for when they go to check their registration
      // status with the platform in enable()
      bob.alianzaApiManager->start(mock.config.alianzaConfig.api);
      bob.alianzaApiManager->authorize();
      assertAuthorizationSuccess(bob.alianzaApiEvents);

      max.alianzaApiManager->start(mock.config.alianzaConfig.api);
      max.alianzaApiManager->authorize();
      assertAuthorizationSuccess(max.alianzaApiEvents);

      ASSERT_NO_FATAL_FAILURE(bob.enable());
      ASSERT_NO_FATAL_FAILURE(max.enable());

      // Bob calls Max
      CPCAPI2::SipConversation::SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
      setAucaConversationSettings(bob);
      setAucaConversationSettings(max);
      bob.conversation->addParticipant(bobCall, max.uri().c_str());
      bob.conversation->start(bobCall);

      auto bobConversationEvents = std::async(std::launch::async, [&] ()
      {
         assertNewConversationOutgoing(bob, bobCall, max.uri().c_str()); 
         assertConversationStateChangedUptilConnected(bob, bobCall); // Seen Ringing, Early, Connected at this stage

         std::this_thread::sleep_for(std::chrono::milliseconds(7000));
         assertMediaFlowing(bob, bobCall, true, false);

         bob.conversation->end(bobCall);
         assertConversationEnded(bob, bobCall, CPCAPI2::SipConversation::ConversationEndReason_UserTerminatedLocally);
      });

      auto maxConversationEvents = std::async(std::launch::async, [&] ()
      {
         CPCAPI2::SipConversation::SipConversationHandle maxCall;
         assertNewConversationIncoming(max, &maxCall, bob.uri().c_str());
         max.conversation->sendRingingResponse(maxCall);
         assertConversationStateChanged(max, maxCall, CPCAPI2::SipConversation::ConversationState_LocalRinging);
         max.conversation->accept(maxCall);
         assertConversationMediaChanged(max, maxCall, CPCAPI2::SipConversation::MediaDirection_SendReceive);
         assertConversationStateChanged(max, maxCall, CPCAPI2::SipConversation::ConversationState_Connected);
         std::this_thread::sleep_for(std::chrono::milliseconds(7000));
         assertMediaFlowing(max, maxCall, true, false);

         assertConversationEnded(max, maxCall, CPCAPI2::SipConversation::ConversationEndReason_UserTerminatedRemotely);
      });

      waitFor2Ms(bobConversationEvents, maxConversationEvents, std::chrono::milliseconds(45000));

      std::this_thread::sleep_for(std::chrono::milliseconds(2000));

      bob.disable();
      max.disable();

      std::this_thread::sleep_for(std::chrono::milliseconds(3000));
      ASSERT_NO_FATAL_FAILURE(mock.destroyAlianzaAccountInBackend());
   }

   TEST_F(SipuaCallModuleTest, BasicCallReject)
   {
      TestAccount alice("alice", Account_NoInit);
      setAucaAccountSettingsAndEnable(alice);
      if (HasFatalFailure()) return;
      TestAccount bob("bob", Account_NoInit);
      setAucaAccountSettingsAndEnable(bob);
      if (HasFatalFailure()) return;

      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);

      setAucaConversationSettings(alice);
      setAucaConversationSettings(bob);

      alice.conversation->addParticipant(aliceCall, bob.uri().c_str());
      alice.conversation->start(aliceCall);

      auto aliceEvents = std::async(std::launch::async, [&] ()
      {
         assertNewConversationOutgoing(alice, aliceCall, bob.uri().c_str());
         // Sometimes the call will be rejected without the 180 Ringing
         // assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
         assertConversationEnded(alice, aliceCall, ConversationEndReason_ServerRejected);
      });

      auto bobEvents = std::async(std::launch::async, [&] ()
      {
         SipConversationHandle bobCall;
         assertNewConversationIncoming(bob, &bobCall, alice.uri().c_str());
         // Bob sends 180 ringing -- this should trigger state transitions for both Bob and Alice
         assertSuccess(bob.conversation->sendRingingResponse(bobCall));
         assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
         // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
         assertSuccess(bob.conversation->reject(bobCall));
         assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      });

      waitFor2(aliceEvents, bobEvents);

      alice.destroyAlianzaAccountInBackend();
      bob.destroyAlianzaAccountInBackend();
   }

   TEST_F(SipuaCallModuleTest, BasicCallHold)
   {
      TestAccount alice("alice", Account_NoInit);
      setAucaAccountSettingsAndEnable(alice);
      if (HasFatalFailure()) return;
      TestAccount bob("bob", Account_NoInit);
      setAucaAccountSettingsAndEnable(bob);
      if (HasFatalFailure()) return;

      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);

      setAucaConversationSettings(alice);
      setAucaConversationSettings(bob);

      alice.conversation->addParticipant(aliceCall, bob.uri().c_str());
      alice.conversation->start(aliceCall);

      auto aliceEvents = std::async(std::launch::async, [&] ()
      {
         assertNewConversationOutgoing(alice, aliceCall, bob.uri().c_str());
         assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt)
         {
            ASSERT_FALSE(evt.localHold);
            ASSERT_FALSE(evt.remoteHold);
            ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         });
         assertConversationStateChangedUptilConnected(alice, aliceCall); // Seen Ringing, Early, Connected at this stage

         // With CPE2, a hold does not trigger any associated signalling on the remote UA
         std::string environmentId = TestEnvironmentConfig::testEnvironmentId().c_str();
         if ((environmentId == "repro") || (alice.config.alianzaConfig.platformType.compare("CPE1") == 0))
         {
            assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendOnly);
            assertSuccess(alice.conversation->accept(aliceCall));
            assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt)
            {
               ASSERT_FALSE(evt.localHold);
               ASSERT_TRUE(evt.remoteHold);
               ASSERT_EQ(MediaDirection_ReceiveOnly, evt.localMediaInfo[0].mediaDirection);
            });

            assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendReceive);
            assertSuccess(alice.conversation->accept(aliceCall));
            assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt)
            {
               ASSERT_FALSE(evt.localHold);
               ASSERT_FALSE(evt.remoteHold);
               ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
            });

            if (alice.config.alianzaConfig.platformType.compare("CPE1") == 0)
            {
               // INVITE offer triggered after the 200 OK (offer) sent by Bob in response to the empty INVITE received
               assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendReceive);
               assertSuccess(alice.conversation->accept(aliceCall));
               assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt)
               {
                  ASSERT_FALSE(evt.localHold);
                  ASSERT_FALSE(evt.remoteHold);
                  ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
               });
            }
         }

         assertConversationEnded_time(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely, 40000);
      });

      auto bobEvents = std::async(std::launch::async, [&] ()
      {
         SipConversationHandle bobCall;
         assertNewConversationIncoming(bob, &bobCall, alice.uri().c_str());
         assertSuccess(bob.conversation->accept(bobCall));
         assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt)
         {
            ASSERT_FALSE(evt.localHold);
            ASSERT_FALSE(evt.remoteHold);
            ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         });
         assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
         assertSuccess(bob.conversation->hold(bobCall));
         assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt)
         {
            ASSERT_TRUE(evt.localHold);
            ASSERT_FALSE(evt.remoteHold);
            ASSERT_EQ(MediaDirection_SendOnly, evt.localMediaInfo[0].mediaDirection);
         });

         if (bob.config.alianzaConfig.platformType.compare("CPE1") == 0)
         {
            // Following SDP offer was received after the remote-hold offer-answer was handled by alice
            // o=AlianzaCallDirector 1236 3878739722 IN IP4 **************
            // s=Alianza Call Director Hold Session
            // c=IN IP4 0.0.0.0
            assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendOnly);
            assertSuccess(bob.conversation->accept(bobCall));
            assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt)
            {
               ASSERT_TRUE(evt.localHold);
               ASSERT_TRUE(evt.remoteHold);
               ASSERT_EQ(MediaDirection_Inactive, evt.localMediaInfo[0].mediaDirection);
            });
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(5000));
         bob.conversationEvents->clearCommands();

         assertSuccess(bob.conversation->unhold(bobCall));

         {
            if (bob.config.alianzaConfig.platformType.compare("CPE1") == 0)
            {
               // Empty INVITE received after the unhold offer-answer is completed
               assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt)
               {
                  ASSERT_FALSE(evt.localHold);
                  ASSERT_TRUE(evt.remoteHold);
                  ASSERT_EQ(MediaDirection_ReceiveOnly, evt.localMediaInfo[0].mediaDirection);
               });
            }

            assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt)
            {
               ASSERT_FALSE(evt.localHold);
               ASSERT_FALSE(evt.remoteHold);
               ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
            });
         }

         // Check that bobCall video has RTCP. We do this by getting two reports and comparing stamps
         TestCallEvents::expectRTCP( __LINE__, bob, bobCall );

         assertSuccess(bob.conversation->end(bobCall));
         assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      });

      waitFor2(aliceEvents, bobEvents);

      alice.destroyAlianzaAccountInBackend();
      bob.destroyAlianzaAccountInBackend();
   }

   // OBELISK-4745
   TEST_F(SipuaCallModuleTest, BasicCallReInviteHold)
   {
      TestAccount alice("alice", Account_NoInit);
      setAucaAccountSettingsAndEnable(alice);
      if (HasFatalFailure()) return;
      TestAccount bob("bob", Account_NoInit);
      setAucaAccountSettingsAndEnable(bob);
      if (HasFatalFailure()) return;

      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);

      setAucaConversationSettings(alice);
      setAucaConversationSettings(bob);

      alice.conversation->addParticipant(aliceCall, bob.uri().c_str());
      alice.conversation->start(aliceCall);

      std::atomic<bool> bobTriggeredReinvite(false);

      auto aliceEvents = std::async(std::launch::async, [&] ()
      {
         assertNewConversationOutgoing(alice, aliceCall, bob.uri().c_str());
         assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt)
         {
            ASSERT_FALSE(evt.localHold);
            ASSERT_FALSE(evt.remoteHold);
            ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         });
         assertConversationStateChangedUptilConnected(alice, aliceCall); // Seen Ringing, Early, Connected at this stage

         // delay only added to make it easier to set brekapoints after call setup
         std::this_thread::sleep_for(std::chrono::milliseconds(6000));

         // simulates customer's server sending re-INVITE shortly after call setup
         alice.conversation->sendMediaChangeRequest(aliceCall);

         bobTriggeredReinvite = true;

         // when bob receives the re-INVITE, and tries to hold before accepting the re-INVITE,
         // the SDK marks the hold as pending and sends it after this initial offer/answer is processed
         assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt)
         {
            ASSERT_FALSE(evt.localHold);
            ASSERT_FALSE(evt.remoteHold);
            ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         });

         std::this_thread::sleep_for(std::chrono::milliseconds(3000));

         // With CPE2, a hold does not trigger any associated signalling on the remote UA
         std::string environmentId = TestEnvironmentConfig::testEnvironmentId().c_str();
         if ((environmentId == "repro") || (alice.config.alianzaConfig.platformType.compare("CPE1") == 0))
         {
            assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendOnly);
            assertSuccess(alice.conversation->accept(aliceCall));
            assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt)
            {
               ASSERT_FALSE(evt.localHold);
               ASSERT_TRUE(evt.remoteHold);
               ASSERT_EQ(MediaDirection_ReceiveOnly, evt.localMediaInfo[0].mediaDirection);
            });

            assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendReceive);
            assertSuccess(alice.conversation->accept(aliceCall));
            assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt)
            {
               ASSERT_FALSE(evt.localHold);
               ASSERT_FALSE(evt.remoteHold);
               ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
            });

            if (alice.config.alianzaConfig.platformType.compare("CPE1") == 0)
            {
               // Another INVITE expected resulting from the 200 OK Offer sent by Bob in response to the empty INVITE
               assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendReceive);
               assertSuccess(alice.conversation->accept(aliceCall));
               assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt)
               {
                  ASSERT_FALSE(evt.localHold);
                  ASSERT_FALSE(evt.remoteHold);
                  ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
               });
            }
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      });

      auto bobEvents = std::async(std::launch::async, [&] ()
      {
         SipConversationHandle bobCall;
         assertNewConversationIncoming(bob, &bobCall, alice.uri().c_str());
         assertSuccess(bob.conversation->accept(bobCall));
         assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt)
         {
            ASSERT_FALSE(evt.localHold);
            ASSERT_FALSE(evt.remoteHold);
            ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         });
         assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

         while (!bobTriggeredReinvite)
         {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
         }

         std::this_thread::sleep_for(std::chrono::milliseconds(500));

         assertSuccess(bob.conversation->hold(bobCall));

         std::string environmentId = TestEnvironmentConfig::testEnvironmentId().c_str();
         if ((environmentId == "repro") || (bob.config.alianzaConfig.platformType.compare("CPE1") == 0))
         {
            assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
            assertSuccess(bob.conversation->accept(bobCall));
            assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt)
            {
               ASSERT_TRUE(evt.localHold);
               ASSERT_FALSE(evt.remoteHold);
               ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection); // SendReceive because the hold attempt failed (incorrect state since an incoming offer is being processed)
            });

            assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt)
            {
               ASSERT_TRUE(evt.localHold);
               ASSERT_FALSE(evt.remoteHold);
               ASSERT_EQ(MediaDirection_SendOnly, evt.localMediaInfo[0].mediaDirection); // SendReceive because the hold attempt failed (incorrect state since an incoming offer is being processed)
            });
         }

         if (bob.config.alianzaConfig.platformType.compare("CPE1") == 0)
         {
            // Following SDP offer was received after the remote-hold offer-answer was handled by alice
            // s=Alianza Call Director Hold Session
            // c=IN IP4 0.0.0.0
            assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendOnly);
            assertSuccess(bob.conversation->accept(bobCall));
            assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt)
            {
               ASSERT_TRUE(evt.localHold);
               ASSERT_TRUE(evt.remoteHold);
               ASSERT_EQ(MediaDirection_Inactive, evt.localMediaInfo[0].mediaDirection);
            });
         }
         std::this_thread::sleep_for(std::chrono::milliseconds(5000));
         bob.conversationEvents->clearCommands();

         assertSuccess(bob.conversation->unhold(bobCall));

         if (bob.config.alianzaConfig.platformType.compare("CPE1") == 0)
         {
            // Response to unhold was an SDP with an inactive media status
            assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt)
            {
               ASSERT_FALSE(evt.localHold);
               ASSERT_TRUE(evt.remoteHold);
               ASSERT_EQ(MediaDirection_ReceiveOnly, evt.localMediaInfo[0].mediaDirection);
            });

            // Media change resulting from the SDP answer received in ACK
            assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt)
            {
               ASSERT_FALSE(evt.localHold);
               ASSERT_FALSE(evt.remoteHold);
               ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
            });
         }
         else
         {
            assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt)
            {
               ASSERT_FALSE(evt.localHold);
               ASSERT_FALSE(evt.remoteHold);
               ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
            });
         }
         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         assertSuccess(bob.conversation->end(bobCall));
         assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      });

      waitFor2(aliceEvents, bobEvents);

      alice.destroyAlianzaAccountInBackend();
      bob.destroyAlianzaAccountInBackend();
   }

   TEST_F(SipuaCallModuleTest, BasicCallHoldRejected)
   {
      TestAccount alice("alice", Account_NoInit);
      setAucaAccountSettingsAndEnable(alice);
      if (HasFatalFailure()) return;
      TestAccount bob("bob", Account_NoInit);
      setAucaAccountSettingsAndEnable(bob);
      if (HasFatalFailure()) return;

      std::string environmentId = TestEnvironmentConfig::testEnvironmentId().c_str();

      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      
      setAucaConversationSettings(alice);
      setAucaConversationSettings(bob);

      alice.conversation->addParticipant(aliceCall, bob.uri().c_str());
      alice.conversation->start(aliceCall);

      auto aliceEvents = std::async(std::launch::async, [&] ()
      {
         assertNewConversationOutgoing(alice, aliceCall, bob.uri().c_str());
         assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt)
         {
            ASSERT_FALSE(evt.localHold);
            ASSERT_FALSE(evt.remoteHold);
            ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         });
         assertConversationStateChangedUptilConnected(alice, aliceCall); // Seen Ringing, Early, Connected at this stage

         // With CPE2, a hold does not trigger any associated signalling on the remote UA,
         // as such nothing to reject - possible candidate for gtest-skip for CPE2
         if ((environmentId == "repro") || (alice.config.alianzaConfig.platformType.compare("CPE1") == 0))
         {
            assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendOnly);
            assertSuccess(alice.conversation->reject(aliceCall));

            if (alice.config.alianzaConfig.platformType.compare("CPE1") == 0)
            {
               // Another INVITE expected resulting from the 200 OK Offer sent by Bob in response to the empty INVITE
               assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendReceive);
               assertSuccess(alice.conversation->accept(aliceCall));
               assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt)
               {
                  ASSERT_FALSE(evt.localHold);
                  ASSERT_FALSE(evt.remoteHold);
                  ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
               });
            }
         }

         assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
         alice.conversationEvents->clearCommands();
      });

      auto bobEvents = std::async(std::launch::async, [&] ()
      {
         SipConversationHandle bobCall;
         assertNewConversationIncoming(bob, &bobCall, alice.uri().c_str());
         assertSuccess(bob.conversation->accept(bobCall));
         assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt)
         {
            ASSERT_FALSE(evt.localHold);
            ASSERT_FALSE(evt.remoteHold);
            ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         });
         assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
         assertSuccess(bob.conversation->hold(bobCall));
         if (environmentId.compare("repro") == 0)
         {
            assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt)
            {
               ASSERT_TRUE(evt.localHold);
               ASSERT_FALSE(evt.remoteHold);
               ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
            });
         }
         else
         {
            // As the proxy accepts the hold request before getting response from Alice, the send-only
            // response is received, even though Alice rejects the hold
            assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt)
            {
               ASSERT_TRUE(evt.localHold);
               ASSERT_FALSE(evt.remoteHold);
               ASSERT_EQ(MediaDirection_SendOnly, evt.localMediaInfo[0].mediaDirection);
            });

            if (bob.config.alianzaConfig.platformType.compare("CPE1") == 0)
            {
               // Following SDP offer was received after the hold offer-answer was completed
               // s=Alianza Call Director Hold Session
               // c=IN IP4 0.0.0.0
               assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendOnly);
               assertSuccess(bob.conversation->accept(bobCall));
               assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt)
               {
                  ASSERT_TRUE(evt.localHold);
                  ASSERT_TRUE(evt.remoteHold);
                  ASSERT_EQ(MediaDirection_Inactive, evt.localMediaInfo[0].mediaDirection);
               });
            }
         }
         std::this_thread::sleep_for(std::chrono::milliseconds(10000));
         bob.conversationEvents->clearCommands();
         assertSuccess(bob.conversation->end(bobCall));
         assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      });

      waitFor2(aliceEvents, bobEvents);

      alice.destroyAlianzaAccountInBackend();
      bob.destroyAlianzaAccountInBackend();
   }

}

TEST_F(SipuaCallModuleTest, BasicCall404withSessionId)
{
   GTEST_SKIP_REPRO();
   TestAccount alice("alice", Account_NoInit);
   setAucaAccountSettingsAndEnable(alice);
   if (HasFatalFailure()) return;

   const cpc::string remoteUri = (std::string("sip:bad_") + std::string(alice.uri().c_str()).substr(4)).c_str();

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   setAucaConversationSettings(alice);
   alice.conversation->addParticipant(aliceCall, remoteUri.c_str());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]()
      {
         assertNewConversationOutgoing(alice, aliceCall, remoteUri.c_str());

         SipConversationHandle h;
         ConversationEndedEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onConversationEnded",
            15000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt)) << "Conversation " << aliceCall << " missed ended event";
         ASSERT_EQ(ConversationEndReason_ServerRejected, evt.endReason) << "Coversation " << aliceCall << " ended with incorrect reason";
         ASSERT_FALSE(evt.sessionId.empty());
         safeCout("SessionId: " << evt.sessionId);
         alice.conversationEvents->clearCommands();
      });

   waitFor(aliceEvents);
}
