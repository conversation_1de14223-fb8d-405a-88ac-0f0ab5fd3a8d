import re
import sys
from pprint import pprint

#
# a script to help discover which individual unit tests took the longest to run, based on console output
# from jenkins.
#
# usage example: python sort-cpcapi2-auto-test-result.py < console.txt
# where console.txt was the console output from gtest-parallel or jenkins + gtest-parallel
# i.e. has lines like:
# [145/422] DnsFailoverTest.IPv6_DnsRecordInvalidDomain (15257 ms)
# note that there can be other lines as well, but this script will only search for lines like the above
# with a time duration at the end
#

pattern = re.compile(r"\(([0-9]*) .*ms\)")
def sorter(pair):
	m = pattern.search(pair)
	if m is not None:
		duration = pattern.search(pair).groups()[0]
		if duration.isdigit():
			return int(duration)
		else:
			print "not an int: '" + duration + "' , from line: " + pair

testResultLines = [s for s in sys.stdin if pattern.search(s)]

pprint(sorted(testResultLines, key=sorter))
