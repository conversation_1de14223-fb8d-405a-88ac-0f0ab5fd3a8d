#include "alianza_api_manager_interface.h"
#include "alianza_api_account_fsm.h"
#include "alianza_api_account_state.h"
#include "cpcapi2_test_fixture.h"
#include "util/CurlPPHelper.h"
#include "util/CurlPPProgress.h"
#include "util/CurlPPSSL.h"
#include "util/CurlURI.h"

#include "cpcapi2utils.h"
#include "phone/PhoneInterface.h"
#include "util/cpc_logger.h"
#include "util/CharEncodingHelper.h"
#include "util/cpc_thread.h"
#include "util/ReactorHelpers.h"

// rapidjson
#include <writer.h>
#include <stringbuffer.h>
#include <document.h>

#include <sstream>

using namespace CPCAPI2;
using namespace CPCAPI2::SipAccount;
using namespace std::chrono;
using namespace curlpp::options;

#define ALIANZA_API_HTTP_DEBUG_ENABLED false
#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::EXTERNAL

#define ApiLog(args_) if (AlianzaApiManagerInterface::isDebugEnabled()) { DebugLog(<< args_); }

namespace CPCAPI2
{

namespace test
{

bool AlianzaApiManagerInterface::mDebugEnabled = false;

AlianzaApiManagerInterface::AlianzaApiManagerInterface(Phone* phone) :
mShutdown(false),
mAppHandler(NULL),
mPhone(dynamic_cast<PhoneInterface*>(phone))
{
   mAccountFactory.reset(new AlianzaApiAccountFsmFactory());
}

AlianzaApiManagerInterface::~AlianzaApiManagerInterface()
{
   mShutdown = true;

   assert(mAccountFsmList.size() == 0);
   mAccountFsmList.clear();
   mAccountFactory.reset();
   mObservers.clear();
}

AutoTestReadCallback* AlianzaApiManagerInterface::process_test(int timeout)
{
   // -1 == no wait
   if (mShutdown)
   {
      return NULL;
   }
   resip::ReadCallbackBase* rcb = mCallbackFifo.getNext(timeout);
   AutoTestReadCallback* fpCmd = dynamic_cast<AutoTestReadCallback*>(rcb);
   if (fpCmd != NULL)
   {
      return fpCmd;
   }
   if (rcb != NULL)
   {
      return new AutoTestReadCallback(rcb, "", std::make_tuple(0,0));
   }
   return NULL;
}

void AlianzaApiManagerInterface::postCallback(resip::ReadCallbackBase* rcb)
{
   mCallbackFifo.add(rcb);
}

int AlianzaApiManagerInterface::setHandler(AlianzaApiHandler* handler)
{
   mPhone->getSdkModuleThread().post(resip::resip_bind(&AlianzaApiManagerInterface::setHandlerImpl, shared_from_this(), handler));
   return kSuccess;
}

void AlianzaApiManagerInterface::setHandlerImpl(AlianzaApiHandler* handler)
{
   mAppHandler = handler;
}

int AlianzaApiManagerInterface::addObserver(std::shared_ptr<AlianzaApiHandler> observer)
{
   mPhone->getSdkModuleThread().post(resip::resip_bind(&AlianzaApiManagerInterface::addObserverImpl, shared_from_this(), observer));
   return kSuccess;
}

void AlianzaApiManagerInterface::addObserverImpl(std::shared_ptr<AlianzaApiHandler> observer)
{
   mObservers.insert(std::weak_ptr<AlianzaApiHandler>(observer));
}

int AlianzaApiManagerInterface::removeObserver(std::shared_ptr<AlianzaApiHandler> observer)
{
   mPhone->getSdkModuleThread().post(resip::resip_bind(&AlianzaApiManagerInterface::removeObserverImpl, shared_from_this(), observer));
   return kSuccess;
}

void AlianzaApiManagerInterface::removeObserverImpl(std::shared_ptr<AlianzaApiHandler> observer)
{
   for (auto i = mObservers.begin(); i != mObservers.end(); ++i)
   {
      if (std::shared_ptr<AlianzaApiHandler> handler = (*i).lock())
      {
         if (handler.get() == observer.get())
         {
            mObservers.erase(i);
            break;
         }
      }
   }  
}

int AlianzaApiManagerInterface::start(const AlianzaApiConfig& config)
{
   mPhone->getSdkModuleThread().post(resip::resip_bind(&AlianzaApiManagerInterface::startImpl, shared_from_this(), config));
   return kSuccess;
}

void AlianzaApiManagerInterface::startImpl(const AlianzaApiConfig& config)
{
   initDebugFlag();
   mConfig = config;
}

int AlianzaApiManagerInterface::shutdown()
{
   mPhone->getSdkModuleThread().post(resip::resip_bind(&AlianzaApiManagerInterface::shutdownImpl, shared_from_this()));
   return kSuccess;
}

void AlianzaApiManagerInterface::shutdownImpl()
{
   mShutdown = true;
}

int AlianzaApiManagerInterface::authorize()
{
   mPhone->getSdkModuleThread().post(resip::resip_bind(&AlianzaApiManagerInterface::authorizeImpl, shared_from_this()));
   return kSuccess;
}

void AlianzaApiManagerInterface::authorizeImpl()
{
   std::stringstream url;
   url << mConfig.publicApiUrl << "authorize";

   safeCout("AlianzaApiManagerInterface::authorizeImpl(): command: " << url.str().c_str());

   std::stringstream messageBody;
   messageBody << "{\"username\":\"" << mConfig.apiUsername << "\",\"password\":\"" << mConfig.apiPassword << "\"}";

   AlianzaApiHttpResponseEvent evt;
   std::string msgType("POST");
   HttpRequestOptions options;
   options.debugLogging = true; // troubleshoot authorize request failed with rc = 0
   options.timeoutSec = 6;

   for (int i = 0; i < 3; ++i)
   {
      sendApiRequestImpl(url.str().c_str(), messageBody.str().c_str(), msgType, evt, options);
      if (evt.rc == 0)
      {
         // there can be multiple A records available for the server we're trying to connect to;
         // at present curl/curlpp does not appear to try more than one DNS entry
         // (https://stackoverflow.com/questions/68315302/how-to-try-all-servers-in-dns-using-libcurl)
         //
         // as such we'll retry several times here in a loop
      }
      else
      {
         break;
      }
   }

   fireHttpResponseEvent(evt);

   if (evt.rc != 201 && evt.rc != 200)
   {
      safeCout("AlianzaApiManagerInterface::authorizeImpl(): error in http response: rc: " << evt.rc << " aborting http authorize request, reason: " << evt.response);
      return;
   }

   std::shared_ptr<rapidjson::Document> jsonResponse(new rapidjson::Document);
   jsonResponse->Parse<0>(evt.response.c_str());

   if (jsonResponse->HasParseError())
   {
      safeCout("AlianzaApiManagerInterface::authorizeImpl(): invalid request format, parse error occured:" << jsonResponse->GetParseError() << " aborting decode");
      return;
   }

   if (!jsonResponse->HasMember("authToken"))
   {
      safeCout("AlianzaApiManagerInterface::authorizeImpl(): node missing: authToken, aborting decode");
      return;
   }

   const rapidjson::Value& moduleIdVal = (*jsonResponse)["authToken"];
   if (!moduleIdVal.IsString())
   {
      safeCout("AlianzaApiManagerInterface::authorizeImpl(): invalid authToken format, aborting decode");
      return;
   }

   mInfo.authToken = (*jsonResponse)["authToken"].GetString();

   if (mInfo.authToken.size() == 0)
   {
      safeCout("AlianzaApiManagerInterface::authorizeImpl(): authToken not populated, aborting decode");
      return;
   }
   ApiLog("AlianzaApiManagerInterface::authorizeImpl(): authToken: " << mInfo.authToken);
}

int AlianzaApiManagerInterface::sendCreateMessage(const std::string& url, const std::string& message)
{
   std::string msgType("POST");
   const std::string randReqId = resip::Random::getCryptoRandomHex(4).c_str();
   DebugLog(<< "Requesting sendMessageImpl for reqId: " << randReqId);
   mPhone->getSdkModuleThread().post(resip::resip_bind(&AlianzaApiManagerInterface::sendMessageImpl, shared_from_this(), url, message, msgType, randReqId, std::string("")));
   return kSuccess;
}

int AlianzaApiManagerInterface::sendUpdateMessage(const std::string& url, const std::string& message, const std::string& authToken)
{
   std::string msgType("PUT");
   const std::string randReqId = resip::Random::getCryptoRandomHex(4).c_str();
   DebugLog(<< "Requesting sendMessageImpl for reqId: " << randReqId);
   mPhone->getSdkModuleThread().post(resip::resip_bind(&AlianzaApiManagerInterface::sendMessageImpl, shared_from_this(), url, message, msgType, randReqId, authToken));
   return kSuccess;
}

int AlianzaApiManagerInterface::sendDestroyMessage(const std::string& url, const std::string& message)
{
   std::string msgType("DELETE");
   const std::string randReqId = resip::Random::getCryptoRandomHex(4).c_str();
   DebugLog(<< "Requesting sendMessageImpl for reqId: " << randReqId);
   mPhone->getSdkModuleThread().post(resip::resip_bind(&AlianzaApiManagerInterface::sendMessageImpl, shared_from_this(), url, message, msgType, randReqId, std::string("")));
   return kSuccess;
}

int AlianzaApiManagerInterface::sendQueryMessage(const std::string& url, const std::string& message)
{
   std::string msgType("GET");
   const std::string randReqId = resip::Random::getCryptoRandomHex(4).c_str();
   DebugLog(<< "Requesting sendMessageImpl for reqId: " << randReqId);
   mPhone->getSdkModuleThread().post(resip::resip_bind(&AlianzaApiManagerInterface::sendMessageImpl, shared_from_this(), url, message, msgType, randReqId, std::string("")));
   return kSuccess;
}

void AlianzaApiManagerInterface::sendMessageImpl(const std::string& url, const std::string& message, const std::string& msgType, const std::string& reqId, const std::string& authToken)
{
   AlianzaApiHttpResponseEvent evt;
   evt.requestId = reqId;

   DebugLog(<< "AlianzaApiManagerInterface::sendMessageImpl(): reqId: " << reqId.c_str() << " sending request : " << url << " debug context string: " << mConfig.debugContext << " " << msgType << " request body: " << message);

   for (int i = 0; i < 3; ++i)
   {
      sendApiRequestImpl(url.c_str(), message.c_str(), msgType.c_str(), evt, HttpRequestOptions(), authToken);
      if (evt.rc == 504)
      {
         // {"message": "Endpoint request timed out"} responses seen intermittently; retry
      }
      else
      {
         break;
      }
   }

   if ((evt.rc != 200) && (evt.rc != 201) && (evt.rc != 204))
   {
      DebugLog(<< "AlianzaApiManagerInterface::sendMessageImpl(): reqId: " << reqId << " received response: " << url << " received response: error in http response: rc: " << evt.rc << " aborting http request, reason: " << evt.response << ", debug context string: " << mConfig.debugContext);
   }
   else
   {
      DebugLog(<< "AlianzaApiManagerInterface::sendMessageImpl(): reqId: " << reqId << " received response: " << url << " received response: " << evt.rc << ", debug context string: " << mConfig.debugContext << " response body: " << evt.response);
   }

   fireHttpResponseEvent(evt);
}

AlianzaApiAccountHandle AlianzaApiManagerInterface::createAccount(TestAccount* account)
{
   auto hF = mAlianzaApiAccountHandleFuture.get_future();
   mPhone->getSdkModuleThread().post(resip::resip_bind(&AlianzaApiManagerInterface::createAccountImpl, shared_from_this(), account));
   AlianzaApiAccountHandle h = hF.get();
   mAlianzaApiAccountHandleFuture = std::promise<AlianzaApiAccountHandle>(); // reset for next use
   return h;
}

void AlianzaApiManagerInterface::createAccountImpl(TestAccount* account)
{
   AlianzaApiAccountHandle accountHandle = AlianzaApiAccountFsmFactory::getNext();
   std::shared_ptr<AlianzaApiAccountFsm> fsm = mAccountFactory->createFsm(account, accountHandle);
   mAccountFsmList[accountHandle] = fsm;
   ApiLog("AlianzaApiManagerInterface::createAccountImpl(): created account: " << accountHandle);
   mAlianzaApiAccountHandleFuture.set_value(accountHandle);
}

int AlianzaApiManagerInterface::enableAccount(AlianzaApiAccountHandle account)
{
   mPhone->getSdkModuleThread().post(resip::resip_bind(&AlianzaApiManagerInterface::enableAccountImpl, shared_from_this(), account));
   return kSuccess;
}

void AlianzaApiManagerInterface::enableAccountImpl(AlianzaApiAccountHandle account)
{
   AlianzaAccountFsmList::iterator i = mAccountFsmList.find(account);

   if (i != mAccountFsmList.end())
   {
      i->second->enable();
   }
   else
   {
      ApiLog("AlianzaApiManagerInterface::enableAccountImpl(): invalid account: " << account);
   }
}

int AlianzaApiManagerInterface::disableAccount(AlianzaApiAccountHandle account)
{
   mPhone->getSdkModuleThread().post(resip::resip_bind(&AlianzaApiManagerInterface::disableAccountImpl, shared_from_this(), account));
   return kSuccess;
}

void AlianzaApiManagerInterface::disableAccountImpl(AlianzaApiAccountHandle account)
{
   AlianzaAccountFsmList::iterator i = mAccountFsmList.find(account);

   if (i != mAccountFsmList.end())
   {
      i->second->disable();
   }
   else
   {
      ApiLog("AlianzaApiManagerInterface::disableAccountImpl(): invalid account: " << account);
   }
}

int AlianzaApiManagerInterface::destroyAccount(AlianzaApiAccountHandle account)
{
   mPhone->getSdkModuleThread().post(resip::resip_bind(&AlianzaApiManagerInterface::destroyAccountImpl, shared_from_this(), account));
   return kSuccess;
}

void AlianzaApiManagerInterface::destroyAccountImpl(AlianzaApiAccountHandle account)
{
   AlianzaAccountFsmList::iterator i = mAccountFsmList.find(account);

   if (i != mAccountFsmList.end())
   {
      mAccountFsmList.erase(account);
   }
   else
   {
      ApiLog("AlianzaApiManagerInterface::destroyAccountImpl(): invalid account: " << account);
   }
}

int AlianzaApiManagerInterface::disableHttpRequestTransmission(AlianzaApiAccountHandle account)
{
   mPhone->getSdkModuleThread().post(resip::resip_bind(&AlianzaApiManagerInterface::disableHttpRequestTransmissionImpl, shared_from_this(), account));
   return kSuccess;
}

void AlianzaApiManagerInterface::disableHttpRequestTransmissionImpl(AlianzaApiAccountHandle account)
{
   AlianzaAccountFsmList::iterator i = mAccountFsmList.find(account);

   if (i != mAccountFsmList.end())
   {
      i->second->disableHttpRequestTransmission();
   }
   else
   {
      ApiLog("AlianzaApiManagerInterface::disableHttpRequestTransmissionImpl(): invalid account: " << account);
   }
}

int AlianzaApiManagerInterface::enableHttpRequestTransmission(AlianzaApiAccountHandle account)
{
   mPhone->getSdkModuleThread().post(resip::resip_bind(&AlianzaApiManagerInterface::enableHttpRequestTransmissionImpl, shared_from_this(), account));
   return kSuccess;
}

void AlianzaApiManagerInterface::enableHttpRequestTransmissionImpl(AlianzaApiAccountHandle account)
{
   AlianzaAccountFsmList::iterator i = mAccountFsmList.find(account);

   if (i != mAccountFsmList.end())
   {
      i->second->enableHttpRequestTransmission();
   }
   else
   {
      ApiLog("AlianzaApiManagerInterface::enableHttpRequestTransmissionImpl(): invalid account: " << account);
   }
}

void AlianzaApiManagerInterface::fireHttpResponseEvent(const AlianzaApiHttpResponseEvent& args)
{
   ApiLog("About to fire onAlianzaHttpResponse for reqId: " << args.requestId);

   if (mAppHandler)
   { 
      resip::ReadCallbackBase* rcb = makeFpCommandNew("AlianzaApiHandler::onAlianzaApiHttpResponse", &AlianzaApiHandler::onAlianzaApiHttpResponse, (AlianzaApiHandler*)0xDEADBEEF, args.handle, args);
      postCallback(rcb);
   }

   for (auto i = mObservers.begin(); i != mObservers.end(); ++i)
   { 
      if (std::shared_ptr<AlianzaApiHandler> observer = (*i).lock())
      {
         resip::ReadCallbackBase* cb = resip::resip_bind(&AlianzaApiHandler::onAlianzaApiHttpResponse, observer.get(), args.handle, args);
         (*cb)();
         delete cb;
      }
      else
      {
         i = mObservers.erase(i);
      }
   }

   ApiLog("Fired onAlianzaHttpResponse for reqId " << args.requestId << " mAppHandler: " << mAppHandler << " mObservers count: " << mObservers.size());
}

void AlianzaApiManagerInterface::fireAlianzaApiAccountStatusEvent(AlianzaApiAccountHandle account, const AlianzaApiAccountStatusEvent& args)
{
   if (mAppHandler)
   { 
      resip::ReadCallbackBase* rcb = makeFpCommandNew("AlianzaApiHandler::onAlianzaApiAccountStatusEvent", &AlianzaApiHandler::onAlianzaApiAccountStatusEvent, (AlianzaApiHandler*)0xDEADBEEF, account, args);
      postCallback(rcb);
   }
}

void AlianzaApiManagerInterface::fireAlianzaApiIdentityResetEvent(AlianzaApiAccountHandle account, const AlianzaApiIdentityResetEvent& args)
{
   if (mAppHandler)
   { 
      resip::ReadCallbackBase* rcb = makeFpCommandNew("AlianzaApiHandler::onAlianzaApiIdentityReset", &AlianzaApiHandler::onAlianzaApiIdentityReset, (AlianzaApiHandler*)0xDEADBEEF, account, args);
      postCallback(rcb);
   }
}

void AlianzaApiManagerInterface::fireAlianzaApiHttpResponseTimeoutEvent(AlianzaApiAccountHandle account, const AlianzaApiHttpResponseTimeoutEvent& args)
{
   if (mAppHandler)
   { 
      resip::ReadCallbackBase* rcb = makeFpCommandNew("AlianzaApiHandler::onAlianzaApiHttpResponseTimeout", &AlianzaApiHandler::onAlianzaApiHttpResponseTimeout, (AlianzaApiHandler*)0xDEADBEEF, account, args);
      postCallback(rcb);
   }
}

void AlianzaApiManagerInterface::fireAlianzaApiHttpDelayRequestTimeoutEvent(AlianzaApiAccountHandle account, const AlianzaApiHttpDelayRequestTimeoutEvent& args)
{
   if (mAppHandler)
   { 
      resip::ReadCallbackBase* rcb = makeFpCommandNew("AlianzaApiHandler::onAlianzaApiHttpDelayRequestTimeout", &AlianzaApiHandler::onAlianzaApiHttpDelayRequestTimeout, (AlianzaApiHandler*)0xDEADBEEF, account, args);
      postCallback(rcb);
   }
}

void AlianzaApiManagerInterface::sendApiRequestImpl(const std::string& url, const std::string& messageBody, const std::string& msgType, AlianzaApiHttpResponseEvent& evt, const HttpRequestOptions& options, const std::string& authToken)
{
   std::list<std::string> header;
   header.push_back("Content-Type: application/json");
   std::string authenticationToken = authToken.size() > 0 ? authToken : mInfo.authToken;
   if (authenticationToken.size() > 0)
   {
      std::stringstream authTokenHeader;
      authTokenHeader << "X-AUTH-TOKEN: " << authenticationToken;
      header.push_back(authTokenHeader.str());
   }

   curlpp::Cleanup clean;
   curlpp::Easy request;
   std::ostringstream response;
   int rc = 0;

   CurlPPHelper helper;
   helper.setDefaultOptions(request, url);

   if (msgType.compare("PUT") == 0)
   {
      request.setOpt(new curlpp::options::CustomRequest{"PUT"});
   }
   else if (msgType.compare("GET") == 0)
   {
      request.setOpt(new curlpp::options::CustomRequest{"GET"});
   }
   else if (msgType.compare("DELETE") == 0)
   {
      request.setOpt(new curlpp::options::CustomRequest{"DELETE"});
   }
   else if (msgType.compare("POST") == 0)
   {
   }
   request.setOpt(new curlpp::options::HttpHeader(header));
   request.setOpt(new curlpp::options::PostFields(messageBody.c_str()));
   request.setOpt(new curlpp::options::PostFieldSize(messageBody.length()));
   request.setOpt(new curlpp::options::Verbose(false /*isDebugEnabled()*/));
   request.setOpt(new curlpp::options::WriteStream(&response));

   if (options.debugLogging)
   {
      helper.setDebugLoggingOptions(request, true);
   }
   if (options.timeoutSec > 0)
   {
      helper.setTimeoutOption(request, options.timeoutSec);
   }

   // see ALIANZA_API_HTTP_REQUEST_TIMEOUT_MSECS instead
   //const int timeoutSeconds = 15;
   //helper.setTimeoutOption(request, timeoutSeconds);

   int acceptableCertFailures = 0; // accept no failures by default
   bool ignoreCertErrors = false;
   if (ignoreCertErrors)
   {
      acceptableCertFailures = CurlPPSSL::E_CERT_WHATEVER_ERROR;
   }

   SslCipherOptions tlsSettings;
   CurlPPSSL cssl(tlsSettings, acceptableCertFailures);
   // required on Windows to load root certificates
   request.setOpt(new curlpp::options::SslCtxFunction(cssl));

   try
   {
      request.perform();
      rc = curlpp::infos::ResponseCode::get(request);
   }
   catch (curlpp::LibcurlRuntimeError ex)
   {
      rc = 0;
      ApiLog("AlianzaApiManagerInterface::sendApiRequestImpl(): encountered curlpp::LibcurlRuntimeError " << ex.what());
   }

   evt.rc = rc;
   evt.response = response.str().c_str();
}

std::shared_ptr<AlianzaApiAccountFsm> AlianzaApiManagerInterface::getAccountFsm(AlianzaApiAccountHandle account)
{
   AlianzaAccountFsmList::iterator i = mAccountFsmList.find(account);

   if (i != mAccountFsmList.end())
   {
      return (i->second);
   }
   return NULL;
}

bool AlianzaApiManagerInterface::isDebugEnabled()
{
   return mDebugEnabled;
}

void AlianzaApiManagerInterface::initDebugFlag()
{
   mDebugEnabled = ALIANZA_API_HTTP_DEBUG_ENABLED;
   const char* sEnabled = getenv("CPCAPI2_TEST_ALIANZA_API_HTTP_DEBUG_ENABLED");
   if (sEnabled)
   {
      mDebugEnabled = ((strncmp(sEnabled, "1", 1) == 0) ? true : false);
   }
}

void AlianzaApiManagerInterface::printMessage(const std::string& url, const std::string& messageBody)
{
   std::size_t found = url.find_last_of("/");
   if (found == std::string::npos)
   {
      DebugLog(<< "AlianzaApiManagerInterface::printMessage(): could not identity request command");
   }
   else
   {
      std::string command = url.substr(found + 1);
      if (command.compare("authorize") == 0)
      {
         DebugLog(<< "AlianzaApiManagerInterface::printMessage(): message-body: {\"username\":\"*****\",\"password\":\"*****\"}");
      }
      else
      {
         DebugLog(<< "AlianzaApiManagerInterface::printMessage(): message-body: " << messageBody);
      }
   }
}



}

}
