#pragma once

#ifndef CPCAPI2_TEST_ALIANZA_API_ACCOUNT_FSM_H
#define CPCAPI2_TEST_ALIANZA_API_ACCOUNT_FSM_H

#include "brand_branded.h"
#include <cpcapi2.h>
#include "alianza_api/interface/public/alianza_api_types.h"
#include "alianza_api/interface/public/alianza_api_handler.h"
#include "sipua_alianza_api_test_fixture.h"
#include <rutil/MultiReactor.hxx>
#include <rutil/DeadlineTimer.hxx>


class TestAccount;
struct TestAccountConfig;

namespace CPCAPI2
{

namespace test
{

class AlianzaApiAccountFsmFactory;
class AlianzaApiAccountFsmStateFactory;
class AlianzaApiAccountFsmStateRequestFactory;
class AlianzaApiManagerInterface;

class AlianzaApiAccountFsm : public resip::DeadlineTimer<PERSON>and<PERSON>,
                             public CPCAPI2::test::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
                             public std::enable_shared_from_this<AlianzaApiAccountFsm>
{

public:

   AlianzaApiAccountFsm(TestAccount* account, AlianzaApiAccountHandle fsm);
   virtual~ AlianzaApiAccountFsm();

   void changeState(AlianzaApiAccountFsmStateType newStateType, int subState = -1);
   void sendAccountStatusUpdate(AlianzaApiAccountFsmStateType newStateType);
   AlianzaApiAccountHandle getHandle();
   std::string getName();
   std::string getState();
   AlianzaApiAccountFsmStateType getStateType();
   AlianzaSessionInfo& getSessionInfo();
   AlianzaAccountConfig& getConfig();

   void createRequest(std::string& url, std::string& messageBody);
   void sendCreateRequest(const std::string& url, const std::string& messageBody);
   void sendUpdateRequest(const std::string& url, const std::string& messageBody);
   void sendDestroyRequest(const std::string& url, const std::string& messageBody);
   void sendQueryRequest(const std::string& url, const std::string& messageBody);
   bool hasNumberBeenReservedInPartition() const;
   bool hasNumberBeenAddedInAccount() const;
   bool hasAccountBeenCreatedInPartition() const;

   void setNumberReservedInPartition(bool reserved);
   void setNumberAddedInAccount(bool added);
   void setAccountCreatedInPartition(bool created);
   void setUserCreatedForExtension(const std::string& extension);
   void setDeviceCreatedForExtension(const std::string& extension);
   void setCurrentExtension(const std::string& extension);
   std::string getCurrentExtension() const;
   std::string getNextExtension() const;
   void updateExtensionsAttemptedForUser(const std::string& extension);
   void updateExtensionsAttemptedForDevice(const std::string& extension);
   bool getUaToAddUser(AlianzaSipUaInfo& info) const;
   bool getUaToAddDevice(AlianzaSipUaInfo& info) const;
   bool updateUa(const AlianzaSipUaInfo& info);
   void enableHttpRequestTransmission();
   void disableHttpRequestTransmission();
   void fireAlianzaApiIdentityResetEvent(const AlianzaApiIdentityResetEvent& args);
   void fireAlianzaApiHttpResponseTimeoutEvent(const AlianzaApiHttpResponseTimeoutEvent& args);
   void fireAlianzaApiHttpDelayRequestTimeoutEvent(const AlianzaApiHttpDelayRequestTimeoutEvent& args);

   void cancelTimers();
   void resetHttpRequestTimer();
   void resetDelayRequestTimer();
   void resetDelayRegistrationTimer();
   void cancelHttpRequestTimer();
   void cancelDelayRequestTimer();
   void cancelDelayRegistrationTimer();
   void setHttpRequestTimeoutMsecs(unsigned int msecs);
   void setDelayRequestTimeoutMsecs(unsigned int msecs);
   void setDelayRegistrationTimeoutMsecs(unsigned int msecs);

   // Associated to AlianzaApiManager
   int enable();
   int disable();
   void addObserver();
   void removeObserver();

   // DeadlineTimerHandler
   virtual void onTimer(unsigned short timerId, void* appState) OVERRIDE;

   // AlianzaApiHandler
   virtual void onAlianzaApiHttpResponse(test::AlianzaApiHandle handle, const AlianzaApiHttpResponseEvent& evt) OVERRIDE;
   virtual void onAlianzaApiAccountStatusEvent(test::AlianzaApiAccountHandle account, const AlianzaApiAccountStatusEvent& evt) OVERRIDE {}
   virtual void onAlianzaApiIdentityReset(test::AlianzaApiAccountHandle account, const AlianzaApiIdentityResetEvent& evt) OVERRIDE {}
   virtual void onAlianzaApiHttpResponseTimeout(test::AlianzaApiAccountHandle handle, const AlianzaApiHttpResponseTimeoutEvent& evt) OVERRIDE {}
   virtual void onAlianzaApiHttpDelayRequestTimeout(test::AlianzaApiAccountHandle handle, const AlianzaApiHttpDelayRequestTimeoutEvent& evt) OVERRIDE {}


   void decrementNumberQueryRetries() { mNumberQueryRetriesPending--; }
   void decrementNumberReserveRetries() { mNumberReserveRetriesPending--; }
   void decrementNumberCreateAccountRetries() { mNumberCreateAccountRetriesPending--; }
   void decrementAddUserRetries() { mAddUserRetriesPending--; }
   void decrementAddDeviceRetries() { mAddDeviceRetriesPending--; }
   void decrementAddNumberRetries() { mAddNumberRetriesPending--; }
   void decrementNumberUpdateCallerIdRetries() { mNumberUpdateCallerIdRetriesPending--; }
   void decrementRemoveNumberRetries() { mRemoveNumberRetriesPending--; }
   void decrementDeleteAccountRetries() { mDeleteAccountRetriesPending--; }
   void decrementCheckNUmberInAccountRetries() { mCheckNumberInAccountRetriesPending--; }
   void decrementQueryClientConfigRetries() { mQueryClientConfigRetriesPending--; }
   bool noNumberQueryRetriesPending() const { return (mNumberQueryRetriesPending <= 0); }
   bool noNumberReserveRetriesPending() const { return (mNumberReserveRetriesPending <= 0); }
   bool noNumberCreateAccountRetriesPending() const { return (mNumberCreateAccountRetriesPending <= 0); }
   bool noAddUserRetriesPending() const { return (mAddUserRetriesPending <= 0); }
   bool noAddDeviceRetriesPending() const { return (mAddDeviceRetriesPending <= 0); }
   bool noAddNumberRetriesPending() const { return (mAddNumberRetriesPending <= 0); }
   bool noNumberUpdateCallerIdRetriesPending() const { return (mNumberUpdateCallerIdRetriesPending <= 0); }
   bool noRemoveNumberRetriesPending() const { return (mRemoveNumberRetriesPending <= 0); }
   bool noDeleteAccountRetriesPending() const { return (mDeleteAccountRetriesPending <= 0); }
   bool noCheckNumberInAccountRetriesPending() const { return (mCheckNumberInAccountRetriesPending <= 0); }
   bool noQueryClientConfigRetriesPending() const { return (mQueryClientConfigRetriesPending <= 0); }

private:

   AlianzaApiAccountFsm();

   void onHttpRequestTimeout();
   void onDelayRequestTimeout();
   void onDelayRegistrationTimeout();

   TestAccount* mAccount;
   AlianzaApiAccountHandle mHandle;
   AlianzaApiAccountFsmStateType mState;
   std::unique_ptr<AlianzaApiAccountFsmStateFactory> mStateFactory;
   std::unique_ptr<AlianzaApiAccountFsmStateRequestFactory> mRequestFactory;
   std::shared_ptr<AlianzaApiManagerInterface> mManager;
   bool mNumberReservedInPartition;
   bool mNumberAddedInAccount;
   bool mAccountCreatedInPartition;
   bool mHttpTransmissionEnabled;
   std::string mCurrentExtension;
   std::set<std::string> mExtensionsAttemptedUser;
   std::set<std::string> mExtensionsAttemptedDevice;

   int mNumberQueryRetriesPending;
   int mNumberReserveRetriesPending;
   int mNumberCreateAccountRetriesPending;
   int mAddUserRetriesPending;
   int mAddDeviceRetriesPending;
   int mAddNumberRetriesPending;
   int mNumberUpdateCallerIdRetriesPending;
   int mRemoveNumberRetriesPending;
   int mDeleteAccountRetriesPending;
   int mCheckNumberInAccountRetriesPending;
   int mQueryClientConfigRetriesPending;

   resip::DeadlineTimer<resip::MultiReactor> mHttpRequestTimer;
   resip::DeadlineTimer<resip::MultiReactor> mDelayRequestTimer;
   resip::DeadlineTimer<resip::MultiReactor> mDelayRegistrationTimer;
   unsigned int mHttpRequestTimeout;
   unsigned int mDelayRequestTimeout;
   unsigned int mDelayRegistrationTimeout;

};

class AlianzaApiAccountFsmFactory
{

public:

   AlianzaApiAccountFsmFactory();
   virtual~ AlianzaApiAccountFsmFactory();

   static test::AlianzaApiAccountHandle getNext();
   virtual std::shared_ptr<AlianzaApiAccountFsm> createFsm(TestAccount* account, test::AlianzaApiAccountHandle fsm);

protected:

   static test::AlianzaApiAccountHandle sNextAlianzaApiAccountHandle;

};

}

}

#endif // CPCAPI2_TEST_ALIANZA_API_ACCOUNT_FSM_H

