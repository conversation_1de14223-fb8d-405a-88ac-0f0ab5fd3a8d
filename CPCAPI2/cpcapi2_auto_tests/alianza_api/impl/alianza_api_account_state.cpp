#include "alianza_api_account_state.h"
#include "alianza_api_account_fsm.h"
#include "alianza_api_types.h"
#include "alianza_api_manager_interface.h"
#include "sipua_alianza_api_test_helper.h"
#include "cpcapi2_test_fixture.h"
#include "util/CurlPPHelper.h"
#include "util/CurlPPProgress.h"
#include "util/CurlPPSSL.h"
#include "util/CurlURI.h"

#include "cpcapi2utils.h"
#include "phone/PhoneInterface.h"
#include "util/cpc_logger.h"
#include "util/CharEncodingHelper.h"
#include "util/cpc_thread.h"

// rapidjson
#include <writer.h>
#include <stringbuffer.h>
#include <document.h>

#include <sstream>

using namespace CPCAPI2;
using namespace CPCAPI2::SipAccount;
using namespace std::chrono;
using namespace curlpp::options;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::EXTERNAL

#define ApiLog(args_) if (AlianzaApiManagerInterface::isDebugEnabled()) { DebugLog(<< args_); }
#define STATE_LOG_PREFIX "fsm: " << fsm->getConfig().api.debugContext << " state: " << fsm->getState() << " username: " << fsm->getName()


namespace CPCAPI2
{

namespace test
{

AlianzaApiAccountFsmIdleState::AlianzaApiAccountFsmIdleState() :
AlianzaApiAccountFsmState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle)
{
}

AlianzaApiAccountFsmIdleState::~AlianzaApiAccountFsmIdleState()
{
}

void AlianzaApiAccountFsmIdleState::onEntry(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   ApiLog("AlianzaApiAccountFsmIdleState::onEntry(): " << STATE_LOG_PREFIX);
   fsm->removeObserver();
   fsm->cancelTimers();
   fsm->setNumberReservedInPartition(false);
   fsm->setNumberAddedInAccount(false);
   fsm->setAccountCreatedInPartition(false);
}

void AlianzaApiAccountFsmIdleState::onExit(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   ApiLog("AlianzaApiAccountFsmIdleState::onExit(): " << STATE_LOG_PREFIX);
   fsm->addObserver();
}

int AlianzaApiAccountFsmIdleState::enable(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   ApiLog("AlianzaApiAccountFsmIdleState::enable(): " << STATE_LOG_PREFIX);
   if (fsm->getSessionInfo().numberEnabled)
   {
      fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_QueryNumber);
   }
   else
   {
      fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_CreateAccount);
   }
   return kSuccess;
}

void AlianzaApiAccountFsmIdleState::sendRequest(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   ApiLog("AlianzaApiAccountFsmIdleState::sendRequest(): " << STATE_LOG_PREFIX << " no request applicable to this state");
}

AlianzaApiAccountFsmQueryNumberState::AlianzaApiAccountFsmQueryNumberState() :
AlianzaApiAccountFsmState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_QueryNumber)
{
}

AlianzaApiAccountFsmQueryNumberState::~AlianzaApiAccountFsmQueryNumberState()
{
}

void AlianzaApiAccountFsmQueryNumberState::onEntry(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   ApiLog("AlianzaApiAccountFsmQueryNumberState::onEntry(): " << STATE_LOG_PREFIX);
   sendRequest(fsm);
}

void AlianzaApiAccountFsmQueryNumberState::onExit(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   ApiLog("AlianzaApiAccountFsmQueryNumberState::onExit(): " << STATE_LOG_PREFIX);
   fsm->cancelTimers();
}

void AlianzaApiAccountFsmQueryNumberState::onAlianzaApiHttpResponse(std::shared_ptr<AlianzaApiAccountFsm> fsm, const AlianzaApiHttpResponseEvent& evt)
{
   ApiLog("AlianzaApiAccountFsmQueryNumberState::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX);
   if ((evt.rc == 404) && AlianzaApiTestHelper::isErrorTelephoneNumberEntityNotFound(evt.response))
   {
      fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_ReserveNumber);
   }
   else if (evt.rc == 200)
   {
      if (fsm->noNumberQueryRetriesPending())
      {
         ApiLog("AlianzaApiAccountFsmQueryNumberState::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX << " no more retries pending");
         fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);
      }
      else
      {
         fsm->decrementNumberQueryRetries();

         AlianzaApiIdentityResetEvent resetEvt;
         resetEvt.previousNumber = fsm->getSessionInfo().getUa()->phoneNumber;
         resetEvt.previousUsername = fsm->getSessionInfo().getUa()->username;
         fsm->getSessionInfo().getUa()->initIdentity(fsm->getConfig());
         resetEvt.currentNumber = fsm->getSessionInfo().getUa()->phoneNumber;
         resetEvt.currentUsername = fsm->getSessionInfo().getUa()->username;

         fsm->fireAlianzaApiIdentityResetEvent(resetEvt);
         sendRequest(fsm);
      }
   }
   else
   {
      ApiLog("AlianzaApiAccountFsmQueryNumberState::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX << " unexpected response: " << evt.rc << " message: " << evt.response);
      fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);
   }
}

void AlianzaApiAccountFsmQueryNumberState::sendRequest(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   std::string messageBody("");
   std::string url("");
   fsm->createRequest(url, messageBody);
   fsm->sendQueryRequest(url, messageBody);
   fsm->resetHttpRequestTimer();
}

AlianzaApiAccountFsmReserveNumberState::AlianzaApiAccountFsmReserveNumberState() :
AlianzaApiAccountFsmState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_ReserveNumber)
{
}

AlianzaApiAccountFsmReserveNumberState::~AlianzaApiAccountFsmReserveNumberState()
{
}

void AlianzaApiAccountFsmReserveNumberState::onEntry(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   ApiLog("AlianzaApiAccountFsmReserveNumberState::onEntry(): " << STATE_LOG_PREFIX);
   sendRequest(fsm);
}

void AlianzaApiAccountFsmReserveNumberState::onExit(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   ApiLog("AlianzaApiAccountFsmReserveNumberState::onExit(): " << STATE_LOG_PREFIX);
   fsm->cancelTimers();
}

void AlianzaApiAccountFsmReserveNumberState::onAlianzaApiHttpResponse(std::shared_ptr<AlianzaApiAccountFsm> fsm, const AlianzaApiHttpResponseEvent& evt)
{
   ApiLog("AlianzaApiAccountFsmReserveNumberState::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX);
   if (evt.rc == 201)
   {
      bool success = AlianzaApiTestHelper::extractPhoneIdFromCreateNumberInPartitionResponse(evt.response, fsm->getSessionInfo().getUa()->phoneId);
      if (success)
      {
         fsm->setNumberReservedInPartition(true);
         if (fsm->hasAccountBeenCreatedInPartition())
         {
            // Presuming user and device added, as only re-query has only been seen in particular add number scenario
            fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_AddNumber);
         }
         else
         {
            fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_CreateAccount);
         }
      }
      else
      {
         ApiLog("AlianzaApiAccountFsmReserveNumberState::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX << " could not extract phoneId");
         fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_ReleaseNumber);
      }
   }
   else if ((evt.rc == 400) && (AlianzaApiTestHelper::isErrorPhoneNumberAlreadyExists(evt.response)
      || AlianzaApiTestHelper::isErrorTNDisconnectEventInProgress(evt.response)))
   {
      if (fsm->noNumberReserveRetriesPending())
      {
         ApiLog("AlianzaApiAccountFsmReserveNumberState::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX << " no more reserve retries pending - number exists");
         fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);
      }
      else
      {
         fsm->decrementNumberReserveRetries();

         AlianzaApiIdentityResetEvent resetEvt;
         resetEvt.previousNumber = fsm->getSessionInfo().getUa()->phoneNumber;
         resetEvt.previousUsername = fsm->getSessionInfo().getUa()->username;
         fsm->getSessionInfo().getUa()->initIdentity(fsm->getConfig());
         resetEvt.currentNumber = fsm->getSessionInfo().getUa()->phoneNumber;
         resetEvt.currentUsername = fsm->getSessionInfo().getUa()->username;

         fsm->fireAlianzaApiIdentityResetEvent(resetEvt);
         fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_QueryNumber);
      }
   }
   else
   {
      ApiLog("AlianzaApiAccountFsmReserveNumberState::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX << " unexpected response: " << evt.rc << " message: " << evt.response);
      fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);
   }
}

void AlianzaApiAccountFsmReserveNumberState::sendRequest(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   std::string messageBody("");
   std::string url("");
   fsm->createRequest(url, messageBody);
   fsm->sendCreateRequest(url, messageBody);
   fsm->resetHttpRequestTimer();
}

AlianzaApiAccountFsmCreateAccountState::AlianzaApiAccountFsmCreateAccountState() :
AlianzaApiAccountFsmState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_CreateAccount)
{
}

AlianzaApiAccountFsmCreateAccountState::~AlianzaApiAccountFsmCreateAccountState()
{
}

void AlianzaApiAccountFsmCreateAccountState::onEntry(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   ApiLog("AlianzaApiAccountFsmCreateAccountState::onEntry(): " << STATE_LOG_PREFIX);
   sendRequest(fsm);
}

void AlianzaApiAccountFsmCreateAccountState::onExit(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   ApiLog("AlianzaApiAccountFsmCreateAccountState::onExit(): " << STATE_LOG_PREFIX);
   fsm->cancelTimers();
}

void AlianzaApiAccountFsmCreateAccountState::onAlianzaApiHttpResponse(std::shared_ptr<AlianzaApiAccountFsm> fsm, const AlianzaApiHttpResponseEvent& evt)
{
   ApiLog("AlianzaApiAccountFsmCreateAccountState::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX);
   if (evt.rc == 201)
   {
      bool success = AlianzaApiTestHelper::extractAccountIdFromCreateAccountInPartitionResponse(evt.response, fsm->getSessionInfo().accountId);
      if (success)
      {
         fsm->setAccountCreatedInPartition(true);
         fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_SetGroupName);
      }
      else
      {
         ApiLog("AlianzaApiAccountFsmCreateAccountState::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX << " could not extract accountId");
         if (fsm->getSessionInfo().numberEnabled)
         {
            fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_ReleaseNumber);
         }
         else
         {
            fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_DeleteAccount);
         }
      }
   }
   else if ((evt.rc == 400) && AlianzaApiTestHelper::isErrorDuplicateAccountNumber(evt.response))
   {
      if (fsm->noNumberCreateAccountRetriesPending())
      {
         ApiLog("AlianzaApiAccountFsmCreateAccountState::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX << " duplicate account number - reset accountId");
         if (fsm->getSessionInfo().numberEnabled)
         {
            fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_ReleaseNumber);
         }
         else
         {
            fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_DeleteAccount);
         }
      }
      else
      {
         ApiLog("AlianzaApiAccountFsmCreateAccountState::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX << " error not handled: " << evt.rc);
         fsm->decrementNumberCreateAccountRetries();
         fsm->getSessionInfo().resetAccountNumber();
         sendRequest(fsm);
      }
   }
   else
   {
      ApiLog("AlianzaApiAccountFsmCreateAccountState::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX << " unexpected response: " << evt.rc << " message: " << evt.response);
      if (fsm->getSessionInfo().numberEnabled)
      {
         fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_ReleaseNumber);
      }
      else
      {
         fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_DeleteAccount);
      }
   }
}

void AlianzaApiAccountFsmCreateAccountState::sendRequest(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   std::string messageBody("");
   std::string url("");
   fsm->createRequest(url, messageBody);
   fsm->sendCreateRequest(url, messageBody);
   fsm->resetHttpRequestTimer();
}

AlianzaApiAccountFsmAddUserState::AlianzaApiAccountFsmAddUserState() :
AlianzaApiAccountFsmState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_AddUser)
{
}

AlianzaApiAccountFsmAddUserState::~AlianzaApiAccountFsmAddUserState()
{
}

void AlianzaApiAccountFsmAddUserState::onEntry(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   std::string extension = fsm->getNextExtension();
   assert(!extension.empty()); // should not be in this state if there are no more extensions

   ApiLog("AlianzaApiAccountFsmAddUserState::onEntry(): " << STATE_LOG_PREFIX << " extension: " << extension);
   fsm->setCurrentExtension(extension);
   fsm->updateExtensionsAttemptedForUser(extension);
   sendRequest(fsm);
   fsm->resetHttpRequestTimer();
}

void AlianzaApiAccountFsmAddUserState::onExit(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   ApiLog("AlianzaApiAccountFsmAddUserState::onExit(): " << STATE_LOG_PREFIX);
   fsm->cancelTimers();
}

void AlianzaApiAccountFsmAddUserState::onAlianzaApiHttpResponse(std::shared_ptr<AlianzaApiAccountFsm> fsm, const AlianzaApiHttpResponseEvent& evt)
{
   ApiLog("AlianzaApiAccountFsmAddUserState::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX);
   if (evt.rc == 201)
   {
      AlianzaSessionInfo& session = fsm->getSessionInfo();
      AlianzaSipUaInfo& ua = *(session.getUa(fsm->getCurrentExtension()));
      bool success = AlianzaApiTestHelper::extractDataFromAddUserInAccountResponse(evt.response, ua.userId, ua.voicemailId, ua.extension, session.plan);
      if (success)
      {
         ua.userCreated = true;
         
         fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_UserAuth);
      }
      else
      {
         ApiLog("AlianzaApiAccountFsmAddUserState::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX << " could not extract data from add user response");
         fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_DeleteAccount);
      }
   }
   else if ((evt.rc == 400) && (AlianzaApiTestHelper::isErrorDuplicateUsername(evt.response)
      || AlianzaApiTestHelper::isErrorEmailAddressInUse(evt.response)))
   {
      if (fsm->noAddUserRetriesPending())
      {
         ApiLog("AlianzaApiAccountFsmAddUserState::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX << " no more add user retries pending - user exists");
         fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_DeleteAccount);
      }
      else
      {
         fsm->decrementAddUserRetries();

         AlianzaApiIdentityResetEvent resetEvt;
         resetEvt.previousNumber = fsm->getSessionInfo().getUa(fsm->getCurrentExtension())->phoneNumber;
         resetEvt.previousUsername = fsm->getSessionInfo().getUa(fsm->getCurrentExtension())->username;
         fsm->getSessionInfo().getUa(fsm->getCurrentExtension())->initSipUsername();
         resetEvt.currentNumber = fsm->getSessionInfo().getUa(fsm->getCurrentExtension())->phoneNumber;
         resetEvt.currentUsername = fsm->getSessionInfo().getUa(fsm->getCurrentExtension())->username;

         fsm->fireAlianzaApiIdentityResetEvent(resetEvt);
         sendRequest(fsm);
      }
   }
   else
   {
      ApiLog("AlianzaApiAccountFsmAddUserState::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX << " unexpected response: " << evt.rc << " message: " << evt.response);
      fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_DeleteAccount);
   }
}

void AlianzaApiAccountFsmAddUserState::sendRequest(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   std::string messageBody("");
   std::string url("");
   fsm->createRequest(url, messageBody);
   fsm->sendCreateRequest(url, messageBody);
   fsm->resetHttpRequestTimer();
}

AlianzaApiAccountFsmAddNumberState::AlianzaApiAccountFsmAddNumberState() :
AlianzaApiAccountFsmState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_AddNumber)
{
}

AlianzaApiAccountFsmAddNumberState::~AlianzaApiAccountFsmAddNumberState()
{
}

void AlianzaApiAccountFsmAddNumberState::onEntry(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   ApiLog("AlianzaApiAccountFsmAddNumberState::onEntry(): " << STATE_LOG_PREFIX);
   sendRequest(fsm);
}

void AlianzaApiAccountFsmAddNumberState::onExit(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   ApiLog("AlianzaApiAccountFsmAddNumberState::onExit(): " << STATE_LOG_PREFIX);
   fsm->cancelTimers();
}

void AlianzaApiAccountFsmAddNumberState::onAlianzaApiHttpResponse(std::shared_ptr<AlianzaApiAccountFsm> fsm, const AlianzaApiHttpResponseEvent& evt)
{
   ApiLog("AlianzaApiAccountFsmAddNumberState::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX);
   if (evt.rc == 201)
   {
      fsm->setNumberAddedInAccount(true);
      fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_CheckNumberInAccount);
   }
   else if ((evt.rc == 400) && (AlianzaApiTestHelper::isErrorPhoneNumberAlreadyExists(evt.response)
      || AlianzaApiTestHelper::isErrorTNDisconnectEventInProgress(evt.response)
      || AlianzaApiTestHelper::isErrorPortRequiredTelephoneNumberNotInInventory(evt.response)))
   {
      if (fsm->noAddNumberRetriesPending())
      {
         ApiLog("AlianzaApiAccountFsmAddNumberState::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX << " no more add number retries pending - number exists");
         fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_DeleteAccount);
      }
      else
      {
         fsm->decrementAddNumberRetries();
         fsm->setNumberReservedInPartition(false);

         std::string extension = fsm->getSessionInfo().getUa()->extension;
         std::string firstName = fsm->getSessionInfo().getUa()->firstName;
         std::string lastName = fsm->getSessionInfo().getUa()->lastName;
         AlianzaApiIdentityResetEvent resetEvt;
         resetEvt.previousNumber = fsm->getSessionInfo().getUa()->phoneNumber;
         resetEvt.previousUsername = fsm->getSessionInfo().getUa()->username;
         fsm->getSessionInfo().getUa()->initIdentity(fsm->getConfig());
         fsm->getSessionInfo().getUa()->extension = extension; // reuse existing extension
         fsm->getSessionInfo().getUa()->firstName = firstName; // reuse existing firstName
         fsm->getSessionInfo().getUa()->lastName = lastName; // reuse existing lastName
         resetEvt.currentNumber = fsm->getSessionInfo().getUa()->phoneNumber;
         resetEvt.currentUsername = fsm->getSessionInfo().getUa()->username;

         fsm->fireAlianzaApiIdentityResetEvent(resetEvt);
         fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_QueryNumber);
      }
   }
   else
   {
      ApiLog("AlianzaApiAccountFsmAddNumberState::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX << " unexpected response: " << evt.rc << " message: " << evt.response);
      fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_DeleteAccount);
   }
}

void AlianzaApiAccountFsmAddNumberState::sendRequest(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   std::string messageBody("");
   std::string url("");
   fsm->createRequest(url, messageBody);
   fsm->sendCreateRequest(url, messageBody);
   fsm->resetHttpRequestTimer();
}

AlianzaApiAccountFsmUpdateCallerIdState::AlianzaApiAccountFsmUpdateCallerIdState() :
AlianzaApiAccountFsmState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_UpdateCallerId)
{
}

AlianzaApiAccountFsmUpdateCallerIdState::~AlianzaApiAccountFsmUpdateCallerIdState()
{
}

void AlianzaApiAccountFsmUpdateCallerIdState::onEntry(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   ApiLog("AlianzaApiAccountFsmUpdateCallerIdState::onEntry(): " << STATE_LOG_PREFIX);
   fsm->resetDelayRequestTimer();
}

void AlianzaApiAccountFsmUpdateCallerIdState::onExit(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   ApiLog("AlianzaApiAccountFsmUpdateCallerIdState::onExit(): " << STATE_LOG_PREFIX);
   fsm->cancelTimers();
}

void AlianzaApiAccountFsmUpdateCallerIdState::onAlianzaApiHttpResponse(std::shared_ptr<AlianzaApiAccountFsm> fsm, const AlianzaApiHttpResponseEvent& evt)
{
   ApiLog("AlianzaApiAccountFsmUpdateCallerIdState::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX);
   if (evt.rc == 200)
   {
      fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_UpdateUser);
   }
   else if ((evt.rc == 400) && AlianzaApiTestHelper::isErrorInvalidStatus(evt.response))
   {
      ApiLog("AlianzaApiAccountFsmUpdateCallerIdState::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX << " invalid status response received");
      if (fsm->noNumberUpdateCallerIdRetriesPending())
      {
         ApiLog("AlianzaApiAccountFsmUpdateCallerIdState::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX << " no more update caller-id retries pending - invalid status");
         if (fsm->getSessionInfo().numberEnabled)
         {
            fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_RemoveNumber);
         }
         else
         {
            fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_DeleteAccount);
         }
      }
      else
      {
         fsm->decrementNumberUpdateCallerIdRetries();
         fsm->cancelTimers();
         fsm->resetDelayRequestTimer();
      }
   }
   else
   {
      ApiLog("AlianzaApiAccountFsmUpdateCallerIdState::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX << " unexpected response: " << evt.rc << " message: " << evt.response);
      if (fsm->getSessionInfo().numberEnabled)
      {
         fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_RemoveNumber);
      }
      else
      {
         fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_DeleteAccount);
      }
   }
}

void AlianzaApiAccountFsmUpdateCallerIdState::sendRequest(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   std::string messageBody("");
   std::string url("");
   fsm->createRequest(url, messageBody);
   fsm->sendUpdateRequest(url, messageBody);
   fsm->resetHttpRequestTimer();
}

AlianzaApiAccountFsmUpdateUserState::AlianzaApiAccountFsmUpdateUserState() :
AlianzaApiAccountFsmState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_UpdateUser)
{
}

AlianzaApiAccountFsmUpdateUserState::~AlianzaApiAccountFsmUpdateUserState()
{
}

void AlianzaApiAccountFsmUpdateUserState::onEntry(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   ApiLog("AlianzaApiAccountFsmUpdateUserState::onEntry(): " << STATE_LOG_PREFIX);
   sendRequest(fsm);
}

void AlianzaApiAccountFsmUpdateUserState::onExit(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   ApiLog("AlianzaApiAccountFsmUpdateUserState::onExit(): " << STATE_LOG_PREFIX);
   fsm->cancelTimers();
}

void AlianzaApiAccountFsmUpdateUserState::onAlianzaApiHttpResponse(std::shared_ptr<AlianzaApiAccountFsm> fsm, const AlianzaApiHttpResponseEvent& evt)
{
   ApiLog("AlianzaApiAccountFsmUpdateUserState::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX);
   if (evt.rc == 200)
   {
      fsm->cancelTimers(); 
      // Adding the delay between when the account is created, and when the account is registered
      fsm->resetDelayRegistrationTimer();
   }
   else
   {
      ApiLog("AlianzaApiAccountFsmUpdateUserState::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX << " unexpected response: " << evt.rc << " message: " << evt.response);
      if (fsm->getSessionInfo().numberEnabled)
      {
         fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_RemoveNumber);
      }
      else
      {
         fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_DeleteAccount);
      }
   }
}

void AlianzaApiAccountFsmUpdateUserState::onDelayRegistrationTimeout(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   ApiLog("AlianzaApiAccountFsmUpdateUserState::onDelayRegistrationTimeout(): " << STATE_LOG_PREFIX << " delay registration timeout");
   fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Enabled);
}

void AlianzaApiAccountFsmUpdateUserState::sendRequest(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   std::string messageBody("");
   std::string url("");
   fsm->createRequest(url, messageBody);
   fsm->sendUpdateRequest(url, messageBody);
   fsm->resetHttpRequestTimer();
}

AlianzaApiAccountFsmEnabledState::AlianzaApiAccountFsmEnabledState() :
AlianzaApiAccountFsmState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Enabled)
{
}

AlianzaApiAccountFsmEnabledState::~AlianzaApiAccountFsmEnabledState()
{
}

void AlianzaApiAccountFsmEnabledState::onEntry(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   ApiLog("AlianzaApiAccountFsmEnabledState::onEntry(): " << STATE_LOG_PREFIX);
}

void AlianzaApiAccountFsmEnabledState::onExit(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   ApiLog("AlianzaApiAccountFsmEnabledState::onExit(): " << STATE_LOG_PREFIX);
}

void AlianzaApiAccountFsmEnabledState::sendRequest(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   ApiLog("AlianzaApiAccountFsmEnabledState::sendRequest(): " << STATE_LOG_PREFIX << " no request applicable to this state");
}

AlianzaApiAccountFsmRemoveNumberState::AlianzaApiAccountFsmRemoveNumberState() :
AlianzaApiAccountFsmState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_RemoveNumber)
{
}

AlianzaApiAccountFsmRemoveNumberState::~AlianzaApiAccountFsmRemoveNumberState()
{
}

void AlianzaApiAccountFsmRemoveNumberState::onEntry(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   ApiLog("AlianzaApiAccountFsmRemoveNumberState::onEntry(): " << STATE_LOG_PREFIX);
   sendRequest(fsm);
}

void AlianzaApiAccountFsmRemoveNumberState::onExit(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   ApiLog("AlianzaApiAccountFsmRemoveNumberState::onExit(): " << STATE_LOG_PREFIX);
   fsm->cancelTimers();
}

void AlianzaApiAccountFsmRemoveNumberState::onAlianzaApiHttpResponse(std::shared_ptr<AlianzaApiAccountFsm> fsm, const AlianzaApiHttpResponseEvent& evt)
{
   ApiLog("AlianzaApiAccountFsmRemoveNumberState::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX);
   if (evt.rc == 204 || evt.rc == 404 /* seen once; uncertain why */)
   {
      if (evt.rc == 404)
      {
         ApiLog("Warning: remove number request returned 404");
      }
      fsm->setNumberAddedInAccount(false);
      fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_DeleteAccount);
   }
   else if ((evt.rc == 400) && AlianzaApiTestHelper::isErrorServiceActivationEventInProgress(evt.response))
   {
      ApiLog("AlianzaApiAccountFsmRemoveNumberState::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX << " service activation event in progress");
      if (fsm->noRemoveNumberRetriesPending())
      {
         ApiLog("AlianzaApiAccountFsmAddNumberState::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX << " no more add number retries pending - disconnect in progress");
         // Can't delete account until number is removed from account, just go straight to idle
         fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);
      }
      else
      {
         fsm->decrementRemoveNumberRetries();
         ApiLog("AlianzaApiAccountFsmAddNumberState::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX << " retry after delay - disconnect in progress");
         fsm->resetDelayRequestTimer();
         fsm->cancelHttpRequestTimer();
      }
   }
   else
   {
      ApiLog("AlianzaApiAccountFsmRemoveNumberState::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX << " failure removing number from account with response: " << evt.rc << " message: " << evt.response);
      fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);
   }
}

void AlianzaApiAccountFsmRemoveNumberState::sendRequest(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   std::string messageBody("");
   std::string url("");
   fsm->createRequest(url, messageBody);
   fsm->sendDestroyRequest(url, messageBody);
   fsm->resetHttpRequestTimer();
}

/*
Sample Response:
400 response: {
  "status" : 400,
  "messages" : [ "TelephoneNumbersExist" ]
}
*/
AlianzaApiAccountFsmDeleteAccountState::AlianzaApiAccountFsmDeleteAccountState() :
AlianzaApiAccountFsmState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_DeleteAccount)
{
}

AlianzaApiAccountFsmDeleteAccountState::~AlianzaApiAccountFsmDeleteAccountState()
{
}

void AlianzaApiAccountFsmDeleteAccountState::onEntry(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   ApiLog("AlianzaApiAccountFsmDeleteAccountState::onEntry(): " << STATE_LOG_PREFIX);
   sendRequest(fsm);
}

void AlianzaApiAccountFsmDeleteAccountState::onExit(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   ApiLog("AlianzaApiAccountFsmDeleteAccountState::onExit(): " << STATE_LOG_PREFIX);
   fsm->cancelTimers();
}

void AlianzaApiAccountFsmDeleteAccountState::onAlianzaApiHttpResponse(std::shared_ptr<AlianzaApiAccountFsm> fsm, const AlianzaApiHttpResponseEvent& evt)
{
   ApiLog("AlianzaApiAccountFsmDeleteAccountState::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX);
   if (evt.rc == 204 || evt.rc == 404 /* seen once; uncertain why */)
   {
      if (evt.rc == 404)
      {
         ApiLog("Warning: delete account request returned 404");
      }
   
      fsm->setAccountCreatedInPartition(false);
      if (fsm->getSessionInfo().numberEnabled)
      {
         fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_ReleaseNumber);
      }
      else
      {
         fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);
      }
   }
   else if ((evt.rc == 400) && AlianzaApiTestHelper::isErrorTelephoneNumbersExist(evt.response))
   {
      ApiLog("AlianzaApiAccountFsmDeleteAccountState::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX << " telephone numbers exist");
      if (fsm->noDeleteAccountRetriesPending())
      {
         ApiLog("AlianzaApiAccountFsmDeleteAccountState::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX << " no more delete account retries pending - telephone numbers exist");
         // Can't release numbers in partition when number still exist in account, just go straight to idle
         fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);
      }
      else
      {
         fsm->decrementDeleteAccountRetries();
         ApiLog("AlianzaApiAccountFsmDeleteAccountState::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX << " retry delete account");
         fsm->resetDelayRequestTimer();
         fsm->cancelHttpRequestTimer();
      }
   }
   else
   {
      ApiLog("AlianzaApiAccountFsmDeleteAccountState::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX << " failured deleting account in partition with response: " << evt.rc << " message: " << evt.response);
      fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);
   }
}

void AlianzaApiAccountFsmDeleteAccountState::sendRequest(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   std::string messageBody("");
   std::string url("");
   fsm->createRequest(url, messageBody);
   fsm->sendDestroyRequest(url, messageBody);
   fsm->resetHttpRequestTimer();
}

AlianzaApiAccountFsmReleaseNumberState::AlianzaApiAccountFsmReleaseNumberState() :
AlianzaApiAccountFsmState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_AddNumber)
{
}

AlianzaApiAccountFsmReleaseNumberState::~AlianzaApiAccountFsmReleaseNumberState()
{
}

void AlianzaApiAccountFsmReleaseNumberState::onEntry(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   ApiLog("AlianzaApiAccountFsmReleaseNumberState::onEntry(): " << STATE_LOG_PREFIX);
   sendRequest(fsm);
}

void AlianzaApiAccountFsmReleaseNumberState::onExit(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   ApiLog("AlianzaApiAccountFsmReleaseNumberState::onExit(): " << STATE_LOG_PREFIX);
   fsm->cancelHttpRequestTimer();
}

void AlianzaApiAccountFsmReleaseNumberState::onAlianzaApiHttpResponse(std::shared_ptr<AlianzaApiAccountFsm> fsm, const AlianzaApiHttpResponseEvent& evt)
{
   ApiLog("AlianzaApiAccountFsmReleaseNumberState::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX);
   if (evt.rc == 204)
   {
      fsm->setNumberReservedInPartition(false);
      fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);
   }
   else
   {
      ApiLog("AlianzaApiAccountFsmReleaseNumberState::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX << " failure releasing number from partition with response: " << evt.rc << " message: " << evt.response);
      fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);
   }
}

void AlianzaApiAccountFsmReleaseNumberState::sendRequest(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   std::string messageBody("");
   std::string url("");
   fsm->createRequest(url, messageBody);
   fsm->sendDestroyRequest(url, messageBody);
   fsm->resetHttpRequestTimer();
}

AlianzaApiAccountFsmUserAuthState::AlianzaApiAccountFsmUserAuthState() :
   AlianzaApiAccountFsmState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_UserAuth)
{
}

AlianzaApiAccountFsmUserAuthState::~AlianzaApiAccountFsmUserAuthState()
{
}

void AlianzaApiAccountFsmUserAuthState::onEntry(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   ApiLog("AlianzaApiAccountFsmUserAuthState::onEntry(): " << STATE_LOG_PREFIX);
   sendRequest(fsm);
}

void AlianzaApiAccountFsmUserAuthState::onExit(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   ApiLog("AlianzaApiAccountFsmUserAuthState::onExit(): " << STATE_LOG_PREFIX);
   fsm->cancelHttpRequestTimer();
}

void AlianzaApiAccountFsmUserAuthState::onAlianzaApiHttpResponse(std::shared_ptr<AlianzaApiAccountFsm> fsm, const AlianzaApiHttpResponseEvent& evt)
{
   ApiLog("AlianzaApiAccountFsmUserAuthState::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX);
   if (evt.rc == 201)
   {
      bool success = AlianzaApiTestHelper::extractUserAuthTokenFromUserAuthResponse(evt.response, fsm->getSessionInfo().getUa(fsm->getCurrentExtension())->userAuthToken);
      if (success)
      {
         fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_QueryClientConfig);
      }
      else
      {
         ApiLog("AlianzaApiAccountFsmUserAuthState::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX << " could not extract user auth token");
         if (fsm->getSessionInfo().numberEnabled)
         {
            fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_ReleaseNumber);
         }
         else
         {
            fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_DeleteAccount);
         }
      }
   }
   else
   {
      ApiLog("AlianzaApiAccountFsmUserAuthState::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX << " failure authenticating user with response: " << evt.rc << " message: " << evt.response);
      fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);
   }
}

void AlianzaApiAccountFsmUserAuthState::sendRequest(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   std::string messageBody("");
   std::string url("");
   fsm->createRequest(url, messageBody);
   fsm->sendCreateRequest(url, messageBody);
   fsm->resetHttpRequestTimer();
}

AlianzaApiAccountFsmSetGroupNameState::AlianzaApiAccountFsmSetGroupNameState() :
AlianzaApiAccountFsmState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_SetGroupName)
{
}

AlianzaApiAccountFsmSetGroupNameState::~AlianzaApiAccountFsmSetGroupNameState()
{
}

void AlianzaApiAccountFsmSetGroupNameState::onEntry(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   ApiLog("AlianzaApiAccountFsmSetGroupNameState::onEntry(): " << STATE_LOG_PREFIX);
   sendRequest(fsm);
}

void AlianzaApiAccountFsmSetGroupNameState::onExit(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   ApiLog("AlianzaApiAccountFsmSetGroupNameState::onExit(): " << STATE_LOG_PREFIX);
   fsm->cancelHttpRequestTimer();
}

void AlianzaApiAccountFsmSetGroupNameState::onAlianzaApiHttpResponse(std::shared_ptr<AlianzaApiAccountFsm> fsm, const AlianzaApiHttpResponseEvent& evt)
{
   ApiLog("AlianzaApiAccountFsmSetGroupNameState::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX);
   if (evt.rc == 200)
   {
      fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_AddUser);
   }
   else
   {
      ApiLog("AlianzaApiAccountFsmSetGroupNameState::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX << " failure setting group name with response: " << evt.rc << " message: " << evt.response);
      fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);
   }
}

void AlianzaApiAccountFsmSetGroupNameState::sendRequest(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   std::string messageBody("");
   std::string url("");
   fsm->createRequest(url, messageBody);
   fsm->sendUpdateRequest(url, messageBody);
   fsm->resetHttpRequestTimer();
}

AlianzaApiAccountFsmQueryClientConfigState::AlianzaApiAccountFsmQueryClientConfigState() :
AlianzaApiAccountFsmState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_QueryClientConfig)
{
}

AlianzaApiAccountFsmQueryClientConfigState::~AlianzaApiAccountFsmQueryClientConfigState()
{
}

void AlianzaApiAccountFsmQueryClientConfigState::onEntry(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   ApiLog("AlianzaApiAccountFsmQueryClientConfigState::onEntry(): " << STATE_LOG_PREFIX);
   sendRequest(fsm);
}

void AlianzaApiAccountFsmQueryClientConfigState::onExit(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   ApiLog("AlianzaApiAccountFsmQueryClientConfigState::onExit(): " << STATE_LOG_PREFIX);
   fsm->cancelHttpRequestTimer();
}

void AlianzaApiAccountFsmQueryClientConfigState::onAlianzaApiHttpResponse(std::shared_ptr<AlianzaApiAccountFsm> fsm, const AlianzaApiHttpResponseEvent& evt)
{
   ApiLog("AlianzaApiAccountFsmQueryClientConfigState::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX);
   bool success = false;
   if (evt.rc == 200)
   {
      AlianzaSipUaInfo& ua = *(fsm->getSessionInfo().getUa(fsm->getCurrentExtension()));
      success = AlianzaApiTestHelper::extractClientConfigFromQueryClientConfigResponse(evt.response, ua);
      if (success)
      {
         if (fsm->getSessionInfo().numberEnabled)
         {
            // TODO: Maybe future support for having a phone number and also multiple extensions
            fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_AddNumber);
         }
         else
         {
            if (fsm->getNextExtension().empty())
            {
               // Adding the delay between when the account is created, and when the account is registered
               fsm->cancelTimers();
               fsm->resetDelayRegistrationTimer();
            }
            else
            {
               fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_AddUser);
            }
         }
      }
      else
      {
         ApiLog("AlianzaApiAccountFsmQueryClientConfigState::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX << " could not extract user configuration");
      }
   }
   else if (evt.rc == 403) //SCORE-1446
   {
      if (fsm->noQueryClientConfigRetriesPending())
      {
         ApiLog("AlianzaApiAccountFsmQueryClientConfigState::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX << " no more query client config retries pending.");
      }
      else
      {
         fsm->decrementQueryClientConfigRetries();
         fsm->cancelTimers();
         fsm->resetDelayRequestTimer();
         return;
      }
   }
   else
   {
      ApiLog("AlianzaApiAccountFsmSetGroupNameState::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX << " failure querying config with response: " << evt.rc << " message: " << evt.response);
   }

   if (!success)
   {
      if (fsm->getSessionInfo().numberEnabled)
      {
         fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_ReleaseNumber);
      }
      else
      {
         fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_DeleteAccount);
      }
   }
}
void AlianzaApiAccountFsmQueryClientConfigState::onDelayRegistrationTimeout(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   ApiLog("AlianzaApiAccountFsmQueryClientConfigState::onDelayRegistrationTimeout(): " << STATE_LOG_PREFIX << " delay registration timeout");
   fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Enabled);
}

void AlianzaApiAccountFsmQueryClientConfigState::sendRequest(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   std::string messageBody("");
   std::string url("");
   fsm->createRequest(url, messageBody);
   fsm->sendUpdateRequest(url, messageBody/*, fsm->getSessionInfo().userAuthToken*/);
   fsm->resetHttpRequestTimer();
}

AlianzaApiAccountFsmCheckNumberInAccount::AlianzaApiAccountFsmCheckNumberInAccount() :
   AlianzaApiAccountFsmState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_CheckNumberInAccount)
{
}

AlianzaApiAccountFsmCheckNumberInAccount::~AlianzaApiAccountFsmCheckNumberInAccount()
{
}

void AlianzaApiAccountFsmCheckNumberInAccount::onEntry(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   ApiLog("AlianzaApiAccountFsmCheckNumberInAccount::onEntry(): " << STATE_LOG_PREFIX);
   sendRequest(fsm);
}

void AlianzaApiAccountFsmCheckNumberInAccount::onExit(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   ApiLog("AlianzaApiAccountFsmCheckNumberInAccount::onExit(): " << STATE_LOG_PREFIX);
   fsm->cancelHttpRequestTimer();
}

void AlianzaApiAccountFsmCheckNumberInAccount::onAlianzaApiHttpResponse(std::shared_ptr<AlianzaApiAccountFsm> fsm, const AlianzaApiHttpResponseEvent& evt)
{
   ApiLog("AlianzaApiAccountFsmCheckNumberInAccount::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX);
   if (evt.rc == 200)
   {
      std::string serviceStatus;
      if (AlianzaApiTestHelper::extractServiceStatusForNumberInAccountResponse(evt.response, serviceStatus))
      {
         ApiLog("AlianzaApiAccountFsmCheckNumberInAccount::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX << " number in account is " << serviceStatus);
         if (serviceStatus == "ACTIVE")
         {
            fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_UpdateCallerId);
            return;
         }
         else
         {
            if (fsm->noCheckNumberInAccountRetriesPending())
            {
               ApiLog("AlianzaApiAccountFsmCheckNumberInAccount::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX << " no more check number in account retries pending.");
            }
            else
            {
               fsm->decrementCheckNUmberInAccountRetries();
               fsm->cancelTimers();
               fsm->resetDelayRequestTimer();
               return;
            }
         }
      }
      else
      {
         ApiLog("AlianzaApiAccountFsmCheckNumberInAccount::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX << " failure parsing number in account status");
      }
   }
   else
   {
      ApiLog("AlianzaApiAccountFsmCheckNumberInAccount::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX << " failure checking number in account with response: " << evt.rc << " message: " << evt.response);
   }

   if (fsm->getSessionInfo().numberEnabled)
   {
      fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_ReleaseNumber);
   }
   else
   {
      fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_DeleteAccount);
   }
}

void AlianzaApiAccountFsmCheckNumberInAccount::sendRequest(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   std::string messageBody("");
   std::string url("");
   fsm->createRequest(url, messageBody);
   fsm->sendQueryRequest(url, messageBody);
   fsm->resetHttpRequestTimer();
}

AlianzaApiAccountFsmState::AlianzaApiAccountFsmState(AlianzaApiAccountFsmStateType type) :
mState(type)
{
}

AlianzaApiAccountFsmState::~AlianzaApiAccountFsmState()
{
}

AlianzaApiAccountFsmStateType AlianzaApiAccountFsmState::getType()
{
   return mState;
}

std::string AlianzaApiAccountFsmState::getName(AlianzaApiAccountFsmStateType state)
{
   std::string name = "Unknown";
   switch (state)
   {
      case AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle: name = "Idle"; break;
      case AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_QueryNumber: name = "QueryNumber"; break;
      case AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_ReserveNumber: name = "ReserveNumber"; break;
      case AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_CreateAccount: name = "CreateAccount"; break;
      case AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_AddUser: name = "AddUser"; break;
      case AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_AddNumber: name = "AddNumber"; break;
      case AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_UpdateCallerId: name = "UpdateCallerId"; break;
      case AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_UpdateUser: name = "UpdateUser"; break;
      case AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Enabled: name = "Enabled"; break;
      case AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_RemoveNumber: name = "RemoveNumber"; break;
      case AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_DeleteAccount: name = "DeleteAccount"; break;
      case AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_ReleaseNumber: name = "ReleaseNumber"; break;
      case AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_SetGroupName: name = "SetGroupName"; break;
      case AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_UserAuth: name = "UserAuth"; break;
      case AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_QueryClientConfig: name = "QueryClientConfig"; break;
      case AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_CheckNumberInAccount: name = "CheckNumberInAccount"; break;
      default: break;
   }

   return name;
}

std::string AlianzaApiAccountFsmState::getName()
{
   return (AlianzaApiAccountFsmState::getName(mState));
}

int AlianzaApiAccountFsmState::enable(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   ApiLog("AlianzaApiAccountFsmState::enable(): " << STATE_LOG_PREFIX << " ignoring request as not handled in current state");
   return kSuccess;
}

int AlianzaApiAccountFsmState::disable(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   ApiLog("AlianzaApiAccountFsmState::disable(): " << STATE_LOG_PREFIX);
   if (fsm->hasNumberBeenAddedInAccount())
   {
      fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_RemoveNumber);
   }
   else if (fsm->hasAccountBeenCreatedInPartition())
   {
      fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_DeleteAccount);
   }
   else if (fsm->hasNumberBeenReservedInPartition())
   {
      fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_ReleaseNumber);
   }
   else
   {
      fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);
   }
   return kSuccess;
}

void AlianzaApiAccountFsmState::onHttpRequestTimeout(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   ApiLog("AlianzaApiAccountFsmState::onHttpRequestTimeout(): " << STATE_LOG_PREFIX << " http request timeout");
   // Any value in attempting account or number cleanup, if getting http request timeouts  
   fsm->changeState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);
}

void AlianzaApiAccountFsmState::onDelayRequestTimeout(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   ApiLog("AlianzaApiAccountFsmState::onDelayRequestTimeout(): " << STATE_LOG_PREFIX << " delay request timeout");
   sendRequest(fsm);
}

void AlianzaApiAccountFsmState::onDelayRegistrationTimeout(std::shared_ptr<AlianzaApiAccountFsm> fsm)
{
   ApiLog("AlianzaApiAccountFsmState::onDelayRegistrationTimeout(): " << STATE_LOG_PREFIX << " no handler for delay registration timeout in this state");
   sendRequest(fsm);
}

void AlianzaApiAccountFsmState::onAlianzaApiHttpResponse(std::shared_ptr<AlianzaApiAccountFsm> fsm, const AlianzaApiHttpResponseEvent& evt)
{
   ApiLog("AlianzaApiAccountFsmState::onAlianzaApiHttpResponse(): " << STATE_LOG_PREFIX << " no handler for http response in this state");
}

AlianzaApiAccountFsmStateFactory::AlianzaApiAccountFsmStateFactory()
{
}

AlianzaApiAccountFsmStateFactory::~AlianzaApiAccountFsmStateFactory()
{
   for (AlianzaApiAccountFsmStates::iterator i = mStates.begin(); i != mStates.end(); ++i)
   {
      delete (i->second);
   }
   mStates.clear();
}

void AlianzaApiAccountFsmStateFactory::createStates()
{
   if (mStates.size() > 0)
   {
      ApiLog("AlianzaApiAccountFsmStateFactory::createStates(): " << this << " state factory already initialized");
      return;
   }

   mStates[AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle] = createState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle);
   mStates[AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_QueryNumber] = createState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_QueryNumber);
   mStates[AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_ReserveNumber] = createState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_ReserveNumber);
   mStates[AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_CreateAccount] = createState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_CreateAccount);
   mStates[AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_AddUser] = createState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_AddUser);
   mStates[AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_AddNumber] = createState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_AddNumber);
   mStates[AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_UpdateCallerId] = createState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_UpdateCallerId);
   mStates[AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_UpdateUser] = createState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_UpdateUser);
   mStates[AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Enabled] = createState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Enabled);
   mStates[AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_RemoveNumber] = createState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_RemoveNumber);
   mStates[AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_DeleteAccount] = createState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_DeleteAccount);
   mStates[AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_ReleaseNumber] = createState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_ReleaseNumber);
   mStates[AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_SetGroupName] = createState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_SetGroupName);
   mStates[AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_UserAuth] = createState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_UserAuth);
   mStates[AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_QueryClientConfig] = createState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_QueryClientConfig);
   mStates[AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_CheckNumberInAccount] = createState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_CheckNumberInAccount);
   for (AlianzaApiAccountFsmStates::iterator i = mStates.begin(); i != mStates.end(); ++i)
   {
      ApiLog("AlianzaApiAccountFsmStateFactory::create(): " << AlianzaApiAccountFsmState::getName(i->first));
   }
}

AlianzaApiAccountFsmState* AlianzaApiAccountFsmStateFactory::createState(AlianzaApiAccountFsmStateType type)
{
   AlianzaApiAccountFsmState* state = NULL;
   switch (type)
   {
      case AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle: state = new AlianzaApiAccountFsmIdleState(); break;
      case AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_QueryNumber: state = new AlianzaApiAccountFsmQueryNumberState(); break;
      case AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_ReserveNumber: state = new AlianzaApiAccountFsmReserveNumberState(); break;
      case AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_CreateAccount: state = new AlianzaApiAccountFsmCreateAccountState(); break;
      case AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_AddUser: state = new AlianzaApiAccountFsmAddUserState(); break;
      case AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_AddNumber: state = new AlianzaApiAccountFsmAddNumberState(); break;
      case AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_UpdateCallerId: state = new AlianzaApiAccountFsmUpdateCallerIdState(); break;
      case AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_UpdateUser: state = new AlianzaApiAccountFsmUpdateUserState(); break;
      case AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Enabled: state = new AlianzaApiAccountFsmEnabledState(); break;
      case AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_RemoveNumber: state = new AlianzaApiAccountFsmRemoveNumberState(); break;
      case AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_DeleteAccount: state = new AlianzaApiAccountFsmDeleteAccountState(); break;
      case AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_ReleaseNumber: state = new AlianzaApiAccountFsmReleaseNumberState(); break;
      case AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_SetGroupName: state = new AlianzaApiAccountFsmSetGroupNameState(); break;
      case AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_UserAuth: state = new AlianzaApiAccountFsmUserAuthState(); break;
      case AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_QueryClientConfig: state = new AlianzaApiAccountFsmQueryClientConfigState(); break;
      case AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_CheckNumberInAccount: state = new AlianzaApiAccountFsmCheckNumberInAccount(); break;
      default: break;
   }

   return state;
}

AlianzaApiAccountFsmState* AlianzaApiAccountFsmStateFactory::getState(AlianzaApiAccountFsmStateType type)
{
   if (mStates.size() == 0)
   {
      ApiLog("AlianzaApiAccountFsmStateFactory::getState(): " << this << " factory not initialized");
      return NULL;
   }
   return mStates[type];
}

AlianzaApiAccountFsmStateRequestFactory::AlianzaApiAccountFsmStateRequestFactory()
{
}

AlianzaApiAccountFsmStateRequestFactory::~AlianzaApiAccountFsmStateRequestFactory()
{
}

void AlianzaApiAccountFsmStateRequestFactory::createRequest(std::shared_ptr<AlianzaApiAccountFsm> fsm, std::string& url, std::string& messageBody)
{
   AlianzaAccountConfig& config = fsm->getConfig();
   AlianzaSessionInfo& session = fsm->getSessionInfo();
   AlianzaSipUaInfo& ua = *(session.getUa(fsm->getCurrentExtension()));
   AlianzaApiAccountFsmStateType type = fsm->getStateType();
   switch (type)
   {
      case AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_QueryNumber: AlianzaApiTestHelper::createMessageToGetNumberInPartition(config, session, url, messageBody); break;
      case AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_ReserveNumber: AlianzaApiTestHelper::createMessageToAddNumberInPartition(config, session, url, messageBody); break;
      case AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_CreateAccount: AlianzaApiTestHelper::createMessageToAddAccountInPartition(config, session, url, messageBody); break;
      case AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_AddUser: AlianzaApiTestHelper::createMessageToAddUserInAccount(config, session, ua, url, messageBody); break;
      case AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_AddNumber: AlianzaApiTestHelper::createMessageToAddNumberInAccount(config, session, ua, url, messageBody); break;
      case AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_UpdateCallerId: AlianzaApiTestHelper::createMessageToUpdateNumberInAccount(config, session, ua, url, messageBody); break;
      case AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_UpdateUser: AlianzaApiTestHelper::createMessageToUpdateUserInAccount(config, session, ua, url, messageBody); break;
      case AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_RemoveNumber: AlianzaApiTestHelper::createMessageToDeleteNumberInAccount(config, session, ua, url, messageBody); break;
      case AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_DeleteAccount: AlianzaApiTestHelper::createMessageToDeleteAccountInPartition(config, session, url, messageBody); break;
      case AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_ReleaseNumber: AlianzaApiTestHelper::createMessageToDeleteNumberInPartition(config, session, ua, url, messageBody); break;
      case AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_SetGroupName: AlianzaApiTestHelper::createMessageToUpdateGroupNameInAccount(config, session, url, messageBody); break;
      case AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_UserAuth: AlianzaApiTestHelper::createMessageForAuthorization(ua.username, ua.password, config, session, url, messageBody); break;
      case AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_QueryClientConfig: AlianzaApiTestHelper::createMessageToFetchConfigurationInAccount(config, session, ua, url, messageBody); break;
      case AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_CheckNumberInAccount: AlianzaApiTestHelper::createMessageToGetNumberInAccount(config, session, ua, url, messageBody); break;
      default: break;
   }

   if (messageBody.size() == 0)
   {
      ApiLog("AlianzaApiAccountFsmStateRequestFactory::createRequest(): " << this << " no request applicable for state: " << fsm->getState());
   }
}

}

}
