#include "alianza_api_types.h"
#include "alianza_api_account_state.h"
#include "cpcapi2_test_fixture.h"

#include "cpcapi2utils.h"
#include "phone/PhoneInterface.h"

#include <sstream>

using namespace CPCAPI2;
using namespace std::chrono;

namespace CPCAPI2
{

namespace test
{

std::ostream& operator<<(std::ostream& os, const AlianzaApiAccountFsmStateType& state)
{
   os << AlianzaApiAccountFsmState::getName(state);
   return os;
}

std::ostream& operator<<(std::ostream& os, const AlianzaApiConfig& config)
{
   os << "publicApiUrl: " << config.publicApiUrl
      << " apiUsername: " << config.apiUsername
      << " apiPassword: " << "*****";
   return os;  
}

}

}
