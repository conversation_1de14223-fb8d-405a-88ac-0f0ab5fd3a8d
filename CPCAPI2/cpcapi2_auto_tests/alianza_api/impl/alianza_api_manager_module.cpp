#include "brand_branded.h"
#include "alianza_api/interface/public/alianza_api_manager.h"
#include "cpcapi2_test_fixture.h"

#if defined(CPCAPI2_AUTO_TEST)
#if (CPCAPI2_BRAND_ALIANZA_API_MODULE == 1)
#include "alianza_api/impl/alianza_api_manager_interface.h"
#endif
#endif

namespace CPCAPI2
{

namespace test
{

std::shared_ptr<AlianzaApiManager> AlianzaApiManager::getInterface(TestAccount* account)
{
#if defined(CPCAPI2_AUTO_TEST) && (CPCAPI2_BRAND_ALIANZA_API_MODULE == 1)
   if (account && account->phone)
   {
      if (!account->alianzaApiManager)
      {
         account->alianzaApiManager = std::make_shared<CPCAPI2::test::AlianzaApiManagerInterface>(account->phone);
      }
      return account->alianzaApiManager;
   }
   else
      return NULL;
#else
   return NULL;
#endif
}

}

}
