#pragma once

#ifndef CPCAPI2_TEST_ALIANZA_API_ACCOUNT_STATE_H
#define CPCAPI2_TEST_ALIANZA_API_ACCOUNT_STATE_H

#include "brand_branded.h"
#include <cpcapi2.h>
#include "alianza_api/interface/public/alianza_api_types.h"
#include "alianza_api/interface/public/alianza_api_handler.h"
#include <rutil/MultiReactor.hxx>
#include <rutil/DeadlineTimer.hxx>


class TestAccount;

namespace CPCAPI2
{

namespace test
{

class AlianzaApiAccountFsm;
class AlianzaApiAccountFsmStateFactory;
class AlianzaApiAccountFsmStateRequestFactory;

class AlianzaApiAccountFsmState
{

public:

   AlianzaApiAccountFsmState(AlianzaApiAccountFsmStateType state);
   virtual~ AlianzaApiAccountFsmState();
   virtual void onEntry(std::shared_ptr<AlianzaApiAccountFsm> fsm) = 0;
   virtual void onExit(std::shared_ptr<AlianzaApiAccountFsm> fsm) = 0;
   virtual void sendRequest(std::shared_ptr<AlianzaApiAccountFsm> fsm) = 0;
   virtual void setSubState(int subState) {}
   virtual AlianzaApiAccountFsmStateType getType();
   virtual std::string getName();

   static std::string getName(AlianzaApiAccountFsmStateType type);

   // Associated to AliazaApiManager
   virtual int enable(std::shared_ptr<AlianzaApiAccountFsm> fsm);
   virtual int disable(std::shared_ptr<AlianzaApiAccountFsm> fsm);

   // Associated to AlianzaApiHandler
   virtual void onAlianzaApiHttpResponse(std::shared_ptr<AlianzaApiAccountFsm> fsm, const AlianzaApiHttpResponseEvent& evt);

   virtual void onHttpRequestTimeout(std::shared_ptr<AlianzaApiAccountFsm> fsm);
   virtual void onDelayRequestTimeout(std::shared_ptr<AlianzaApiAccountFsm> fsm);
   virtual void onDelayRegistrationTimeout(std::shared_ptr<AlianzaApiAccountFsm> fsm);

protected:

   AlianzaApiAccountFsmStateType mState;

};

class AlianzaApiAccountFsmIdleState : public AlianzaApiAccountFsmState
{

public:

   AlianzaApiAccountFsmIdleState();
   virtual~ AlianzaApiAccountFsmIdleState();
   virtual void onEntry(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;
   virtual void onExit(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;

   virtual int enable(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;

protected:

   virtual void sendRequest(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;
};

class AlianzaApiAccountFsmQueryNumberState : public AlianzaApiAccountFsmState
{

public:

   AlianzaApiAccountFsmQueryNumberState();
   virtual~ AlianzaApiAccountFsmQueryNumberState();
   virtual void onEntry(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;
   virtual void onExit(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;
   virtual void onAlianzaApiHttpResponse(std::shared_ptr<AlianzaApiAccountFsm> fsm, const AlianzaApiHttpResponseEvent& evt) OVERRIDE;

protected:

   virtual void sendRequest(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;

};

class AlianzaApiAccountFsmReserveNumberState : public AlianzaApiAccountFsmState
{

public:

   AlianzaApiAccountFsmReserveNumberState();
   virtual~ AlianzaApiAccountFsmReserveNumberState();
   virtual void onEntry(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;
   virtual void onExit(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;
   virtual void onAlianzaApiHttpResponse(std::shared_ptr<AlianzaApiAccountFsm> fsm, const AlianzaApiHttpResponseEvent& evt) OVERRIDE;

protected:

   virtual void sendRequest(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;

};

class AlianzaApiAccountFsmCreateAccountState : public AlianzaApiAccountFsmState
{

public:

   AlianzaApiAccountFsmCreateAccountState();
   virtual~ AlianzaApiAccountFsmCreateAccountState();
   virtual void onEntry(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;
   virtual void onExit(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;
   virtual void onAlianzaApiHttpResponse(std::shared_ptr<AlianzaApiAccountFsm> fsm, const AlianzaApiHttpResponseEvent& evt) OVERRIDE;

protected:

   virtual void sendRequest(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;

};

class AlianzaApiAccountFsmAddUserState : public AlianzaApiAccountFsmState
{

public:

   AlianzaApiAccountFsmAddUserState();
   virtual~ AlianzaApiAccountFsmAddUserState();
   virtual void onEntry(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;
   virtual void onExit(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;
   virtual void onAlianzaApiHttpResponse(std::shared_ptr<AlianzaApiAccountFsm> fsm, const AlianzaApiHttpResponseEvent& evt) OVERRIDE;

protected:

   virtual void sendRequest(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;

};

class AlianzaApiAccountFsmAddNumberState : public AlianzaApiAccountFsmState
{

public:

   AlianzaApiAccountFsmAddNumberState();
   virtual~ AlianzaApiAccountFsmAddNumberState();
   virtual void onEntry(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;
   virtual void onExit(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;
   virtual void onAlianzaApiHttpResponse(std::shared_ptr<AlianzaApiAccountFsm> fsm, const AlianzaApiHttpResponseEvent& evt) OVERRIDE;

protected:

   virtual void sendRequest(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;

};

class AlianzaApiAccountFsmUpdateCallerIdState : public AlianzaApiAccountFsmState
{

public:

   AlianzaApiAccountFsmUpdateCallerIdState();
   virtual~ AlianzaApiAccountFsmUpdateCallerIdState();
   virtual void onEntry(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;
   virtual void onExit(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;
   virtual void onAlianzaApiHttpResponse(std::shared_ptr<AlianzaApiAccountFsm> fsm, const AlianzaApiHttpResponseEvent& evt) OVERRIDE;

protected:

   virtual void sendRequest(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;

};

class AlianzaApiAccountFsmUpdateUserState : public AlianzaApiAccountFsmState
{

public:

   AlianzaApiAccountFsmUpdateUserState();
   virtual~ AlianzaApiAccountFsmUpdateUserState();
   virtual void onEntry(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;
   virtual void onExit(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;
   virtual void onAlianzaApiHttpResponse(std::shared_ptr<AlianzaApiAccountFsm> fsm, const AlianzaApiHttpResponseEvent& evt) OVERRIDE;
   virtual void onDelayRegistrationTimeout(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;

protected:

   virtual void sendRequest(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;

};

class AlianzaApiAccountFsmEnabledState : public AlianzaApiAccountFsmState
{

public:

   AlianzaApiAccountFsmEnabledState();
   virtual~ AlianzaApiAccountFsmEnabledState();
   virtual void onEntry(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;
   virtual void onExit(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;

protected:

   virtual void sendRequest(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;

};

class AlianzaApiAccountFsmRemoveNumberState : public AlianzaApiAccountFsmState
{

public:

   AlianzaApiAccountFsmRemoveNumberState();
   virtual~ AlianzaApiAccountFsmRemoveNumberState();
   virtual void onEntry(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;
   virtual void onExit(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;
   virtual void onAlianzaApiHttpResponse(std::shared_ptr<AlianzaApiAccountFsm> fsm, const AlianzaApiHttpResponseEvent& evt) OVERRIDE;

protected:

   virtual void sendRequest(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;

};

class AlianzaApiAccountFsmReleaseNumberState : public AlianzaApiAccountFsmState
{

public:

   AlianzaApiAccountFsmReleaseNumberState();
   virtual~ AlianzaApiAccountFsmReleaseNumberState();
   virtual void onEntry(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;
   virtual void onExit(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;
   virtual void onAlianzaApiHttpResponse(std::shared_ptr<AlianzaApiAccountFsm> fsm, const AlianzaApiHttpResponseEvent& evt) OVERRIDE;

protected:

   virtual void sendRequest(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;

};

class AlianzaApiAccountFsmDeleteAccountState : public AlianzaApiAccountFsmState
{

public:

   AlianzaApiAccountFsmDeleteAccountState();
   virtual~ AlianzaApiAccountFsmDeleteAccountState();
   virtual void onEntry(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;
   virtual void onExit(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;
   virtual void onAlianzaApiHttpResponse(std::shared_ptr<AlianzaApiAccountFsm> fsm, const AlianzaApiHttpResponseEvent& evt) OVERRIDE;

protected:

   virtual void sendRequest(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;

};

class AlianzaApiAccountFsmSetGroupNameState : public AlianzaApiAccountFsmState
{

public:

   AlianzaApiAccountFsmSetGroupNameState();
   virtual~AlianzaApiAccountFsmSetGroupNameState();
   virtual void onEntry(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;
   virtual void onExit(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;
   virtual void onAlianzaApiHttpResponse(std::shared_ptr<AlianzaApiAccountFsm> fsm, const AlianzaApiHttpResponseEvent& evt) OVERRIDE;

protected:

   virtual void sendRequest(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;

};

class AlianzaApiAccountFsmUserAuthState : public AlianzaApiAccountFsmState
{

public:

   AlianzaApiAccountFsmUserAuthState();
   virtual~AlianzaApiAccountFsmUserAuthState();
   virtual void onEntry(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;
   virtual void onExit(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;
   virtual void onAlianzaApiHttpResponse(std::shared_ptr<AlianzaApiAccountFsm> fsm, const AlianzaApiHttpResponseEvent& evt) OVERRIDE;

protected:

   virtual void sendRequest(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;

};

class AlianzaApiAccountFsmQueryClientConfigState : public AlianzaApiAccountFsmState
{

public:

   AlianzaApiAccountFsmQueryClientConfigState();
   virtual~AlianzaApiAccountFsmQueryClientConfigState();
   virtual void onEntry(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;
   virtual void onExit(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;
   virtual void onAlianzaApiHttpResponse(std::shared_ptr<AlianzaApiAccountFsm> fsm, const AlianzaApiHttpResponseEvent& evt) OVERRIDE;
   virtual void onDelayRegistrationTimeout(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;
protected:

   virtual void sendRequest(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;

};

class AlianzaApiAccountFsmCheckNumberInAccount : public AlianzaApiAccountFsmState
{

public:

   AlianzaApiAccountFsmCheckNumberInAccount();
   virtual~AlianzaApiAccountFsmCheckNumberInAccount();
   virtual void onEntry(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;
   virtual void onExit(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;
   virtual void onAlianzaApiHttpResponse(std::shared_ptr<AlianzaApiAccountFsm> fsm, const AlianzaApiHttpResponseEvent& evt) OVERRIDE;
protected:

   virtual void sendRequest(std::shared_ptr<AlianzaApiAccountFsm> fsm) OVERRIDE;

};

class AlianzaApiAccountFsmStateFactory
{

public:

   AlianzaApiAccountFsmStateFactory();
   virtual~ AlianzaApiAccountFsmStateFactory();

   virtual void createStates();
   virtual AlianzaApiAccountFsmState* getState(AlianzaApiAccountFsmStateType type);

protected:

   AlianzaApiAccountFsmState* createState(AlianzaApiAccountFsmStateType type);
   typedef std::map<AlianzaApiAccountFsmStateType, AlianzaApiAccountFsmState*> AlianzaApiAccountFsmStates;
   AlianzaApiAccountFsmStates mStates;

};

class AlianzaApiAccountFsmStateRequestFactory
{

public:

   AlianzaApiAccountFsmStateRequestFactory();
   virtual~ AlianzaApiAccountFsmStateRequestFactory();

   virtual void createRequest(std::shared_ptr<AlianzaApiAccountFsm> fsm, std::string& url, std::string& messageBody);

};

}

}

#endif // CPCAPI2_TEST_ALIANZA_API_ACCOUNT_STATE_H

