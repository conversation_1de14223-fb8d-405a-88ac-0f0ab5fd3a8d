#pragma once

#ifndef CPCAPI2_TEST_ALIANZA_API_MANAGER_INTERFACE_H
#define CPCAPI2_TEST_ALIANZA_API_MANAGER_INTERFACE_H

#include "cpcapi2defs.h"
#include "brand_branded.h"
#include <cpcapi2.h>
#include "alianza_api/interface/experimental/alianza_api_manager_internal.h"
#include "cpcapi2_test_fixture.h"
#include "alianza_api/impl/alianza_api_account_fsm.h"
#include "phone/PhoneModule.h"
#include "phone/PhoneInterface.h"
#include <map>
#include <set>
#include <future>


namespace CPCAPI2
{

namespace test
{

class AlianzaApiAccountStatusHandler;
class AlianzaApiAccountFsmFactory;
class AlianzaApiAccountFsm;

class AlianzaApiManagerInterface : public AlianzaApiManagerInternal,
                                   public CPCAPI2::AutoTestProcessor,
                                   public std::enable_shared_from_this<AlianzaApiManagerInterface>
{
public:

   AlianzaApiManagerInterface(Phone* phone = NULL);
   virtual~ AlianzaApiManagerInterface();

   // AlianzaApiManager
   virtual int setHandler(AlianzaApiHandler* handler) OVERRIDE;
   virtual int start(const AlianzaApiConfig& config) OVERRIDE;
   virtual int shutdown() OVERRIDE;
   virtual int authorize() OVERRIDE;

   virtual int sendCreateMessage(const std::string& url, const std::string& message) OVERRIDE;
   virtual int sendUpdateMessage(const std::string& url, const std::string& message, const std::string& authToken = std::string("")) OVERRIDE;
   virtual int sendDestroyMessage(const std::string& url, const std::string& message) OVERRIDE;
   virtual int sendQueryMessage(const std::string& url, const std::string& message) OVERRIDE;

   virtual AlianzaApiAccountHandle createAccount(TestAccount* account) OVERRIDE;
   virtual int enableAccount(AlianzaApiAccountHandle account) OVERRIDE;
   virtual int disableAccount(AlianzaApiAccountHandle account) OVERRIDE;

   virtual int enableHttpRequestTransmission(AlianzaApiAccountHandle account) OVERRIDE;
   virtual int disableHttpRequestTransmission(AlianzaApiAccountHandle account) OVERRIDE;

   // AlianzaApiManagerInternal
   virtual void fireHttpResponseEvent(const AlianzaApiHttpResponseEvent& args) OVERRIDE;
   virtual void fireAlianzaApiAccountStatusEvent(AlianzaApiAccountHandle account, const AlianzaApiAccountStatusEvent& args) OVERRIDE;
   virtual void fireAlianzaApiIdentityResetEvent(AlianzaApiAccountHandle account, const AlianzaApiIdentityResetEvent& args) OVERRIDE;
   virtual void fireAlianzaApiHttpResponseTimeoutEvent(AlianzaApiAccountHandle account, const AlianzaApiHttpResponseTimeoutEvent& args) OVERRIDE;
   virtual void fireAlianzaApiHttpDelayRequestTimeoutEvent(AlianzaApiAccountHandle account, const AlianzaApiHttpDelayRequestTimeoutEvent& args) OVERRIDE;

   virtual int addObserver(std::shared_ptr<AlianzaApiHandler> observer) OVERRIDE;
   virtual int removeObserver(std::shared_ptr<AlianzaApiHandler> observer) OVERRIDE;
   virtual int destroyAccount(AlianzaApiAccountHandle account) OVERRIDE;
   virtual std::shared_ptr<AlianzaApiAccountFsm> getAccountFsm(AlianzaApiAccountHandle account) OVERRIDE;

   // AutoTestProcessor
   virtual AutoTestReadCallback* process_test(int timeout) OVERRIDE;

   static bool isDebugEnabled();

private:

   struct AlianzaApiSessionInfo
   {
      std::string authToken = "";
   };

   struct HttpRequestOptions
   {
      bool debugLogging = false;
      int timeoutSec = -1;
   };

   void setHandlerImpl(AlianzaApiHandler* handler);
   void addObserverImpl(std::shared_ptr<AlianzaApiHandler> observer);
   void removeObserverImpl(std::shared_ptr<AlianzaApiHandler> observer);
   void startImpl(const AlianzaApiConfig& config);
   void authorizeImpl();
   void shutdownImpl();
   void sendMessageImpl(const std::string& url, const std::string& message, const std::string& msgType, const std::string& reqId, const std::string& authToken);
   void sendApiRequestImpl(const std::string& url, const std::string& messageBody, const std::string& msgType, AlianzaApiHttpResponseEvent& evt, const HttpRequestOptions& options, const std::string& authToken = std::string(""));
   void createAccountImpl(TestAccount* account);
   void enableAccountImpl(AlianzaApiAccountHandle account);
   void disableAccountImpl(AlianzaApiAccountHandle account);
   void destroyAccountImpl(AlianzaApiAccountHandle account);
   void enableHttpRequestTransmissionImpl(AlianzaApiAccountHandle account);
   void disableHttpRequestTransmissionImpl(AlianzaApiAccountHandle account);

   void postCallback(resip::ReadCallbackBase* rcb);
   void initDebugFlag();
   void printMessage(const std::string& url, const std::string& messageBody);

   bool mShutdown;
   static bool mDebugEnabled;
   AlianzaApiHandler* mAppHandler;
   std::set<std::weak_ptr<AlianzaApiHandler>, std::owner_less<std::weak_ptr<AlianzaApiHandler>>> mObservers;
   AlianzaApiConfig mConfig;
   AlianzaApiSessionInfo mInfo;
   std::unique_ptr<AlianzaApiAccountFsmFactory> mAccountFactory;
   std::promise<AlianzaApiAccountHandle> mAlianzaApiAccountHandleFuture;
   // std::promise<AlianzaApiAccountHandle> mAlianzaApiAccountCleanupFuture;
   typedef std::map<AlianzaApiAccountHandle, std::shared_ptr<AlianzaApiAccountFsm>> AlianzaAccountFsmList;
   AlianzaAccountFsmList mAccountFsmList; 
   resip::Fifo<resip::ReadCallbackBase> mCallbackFifo;
   PhoneInterface* mPhone;
};

}

}

#endif // CPCAPI2_TEST_ALIANZA_API_MANAGER_INTERFACE_H

