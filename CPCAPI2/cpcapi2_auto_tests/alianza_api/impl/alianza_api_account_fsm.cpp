#include "alianza_api_account_fsm.h"
#include "alianza_api_account_state.h"
#include "alianza_api_types.h"
#include "alianza_api_manager_interface.h"
#include "sipua_alianza_api_test_helper.h"
#include "cpcapi2_test_fixture.h"
#include "util/CurlPPHelper.h"
#include "util/CurlPPProgress.h"
#include "util/CurlPPSSL.h"
#include "util/CurlURI.h"

#include "cpcapi2utils.h"
#include "phone/PhoneInterface.h"
#include "util/cpc_logger.h"
#include "util/CharEncodingHelper.h"
#include "util/cpc_thread.h"

// rapidjson
#include <writer.h>
#include <stringbuffer.h>
#include <document.h>

#include <sstream>

using namespace CPCAPI2;
using namespace CPCAPI2::SipAccount;
using namespace std::chrono;
using namespace curlpp::options;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::EXTERNAL

#define ApiLog(args_) if (AlianzaApiManagerInterface::isDebugEnabled()) { DebugLog(<< args_); }
#define FSM_LOG_PREFIX "fsm: " << getConfig().api.debugContext << " state: " << getState() << " username: " << getName()

namespace CPCAPI2
{

namespace test
{

AlianzaApiAccountHandle AlianzaApiAccountFsmFactory::sNextAlianzaApiAccountHandle = 1;

AlianzaApiAccountFsmFactory::AlianzaApiAccountFsmFactory()
{
}

AlianzaApiAccountFsmFactory::~AlianzaApiAccountFsmFactory()
{
}

AlianzaApiAccountHandle AlianzaApiAccountFsmFactory::getNext()
{
   return (::time(NULL) + (sNextAlianzaApiAccountHandle++));
}

std::shared_ptr<AlianzaApiAccountFsm> AlianzaApiAccountFsmFactory::createFsm(TestAccount* account, AlianzaApiAccountHandle fsm)
{
   return (std::make_shared<AlianzaApiAccountFsm>(account, fsm));
}

AlianzaApiAccountFsm::AlianzaApiAccountFsm(TestAccount* account, AlianzaApiAccountHandle handle) :
mState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle),
mAccount(account),
mManager(std::dynamic_pointer_cast<AlianzaApiManagerInterface>(account->alianzaApiManager)),
mHandle(handle),
mNumberReservedInPartition(false),
mNumberAddedInAccount(false),
mAccountCreatedInPartition(false),
mCurrentExtension(""),
mHttpRequestTimer(dynamic_cast<PhoneInterface*>(account->phone)->getSdkModuleThread()),
mDelayRequestTimer(dynamic_cast<PhoneInterface*>(account->phone)->getSdkModuleThread()),
mDelayRegistrationTimer(dynamic_cast<PhoneInterface*>(account->phone)->getSdkModuleThread()),
mHttpTransmissionEnabled(true),
mNumberQueryRetriesPending(ALIANZA_API_NUMBER_QUERY_RETRIES),
mNumberReserveRetriesPending(ALIANZA_API_NUMBER_RESERVE_RETRIES),
mNumberCreateAccountRetriesPending(ALIANZA_API_CREATE_ACCOUNT_RETRIES),
mAddUserRetriesPending(ALIANZA_API_ADD_USER_RETRIES),
mAddDeviceRetriesPending(ALIANZA_API_ADD_DEVICE_RETRIES),
mAddNumberRetriesPending(ALIANZA_API_ADD_NUMBER_RETRIES),
mNumberUpdateCallerIdRetriesPending(ALIANZA_API_UPDATE_CALLER_ID_RETRIES),
mRemoveNumberRetriesPending(ALIANZA_API_REMOVE_NUMBER_RETRIES),
mDeleteAccountRetriesPending(ALIANZA_API_DELETE_ACCOUNT_RETRIES),
mHttpRequestTimeout(ALIANZA_API_HTTP_REQUEST_TIMEOUT_MSECS),
mDelayRequestTimeout(ALIANZA_API_DELAY_REQUEST_TIMEOUT_MSECS),
mDelayRegistrationTimeout(ALIANZA_API_DELAY_REGISTRATION_TIMEOUT_MSECS),
mCheckNumberInAccountRetriesPending(ALIANZA_API_CHECK_NUMBER_IN_ACCOUNT_RETRIES),
mQueryClientConfigRetriesPending(ALIANZA_API_QUERY_CLIENT_CONFIG_RETRIES)
{
   mStateFactory.reset(new AlianzaApiAccountFsmStateFactory());
   mStateFactory->createStates();
   mRequestFactory.reset(new AlianzaApiAccountFsmStateRequestFactory());
}

AlianzaApiAccountFsm::~AlianzaApiAccountFsm()
{
   cancelHttpRequestTimer();
   ApiLog("AlianzaApiAccountFsm::~AlianzaApiAccountFsm(): " << FSM_LOG_PREFIX << " destroyed fsm");
}

AlianzaApiAccountHandle AlianzaApiAccountFsm::getHandle()
{
   return mHandle;
}

std::string AlianzaApiAccountFsm::getName()
{
   return mAccount->config.uri().c_str();
}

std::string AlianzaApiAccountFsm::getState()
{
   return AlianzaApiAccountFsmState::getName(mState);
}

AlianzaApiAccountFsmStateType AlianzaApiAccountFsm::getStateType()
{
   return mState;
}

AlianzaSessionInfo& AlianzaApiAccountFsm::getSessionInfo()
{
   return (mAccount->config.alianzaSession);
}

AlianzaAccountConfig& AlianzaApiAccountFsm::getConfig()
{
   return (mAccount->config.alianzaConfig);
}

void AlianzaApiAccountFsm::createRequest(std::string& url, std::string& messageBody)
{
   mRequestFactory->createRequest(shared_from_this(), url, messageBody);
}

void AlianzaApiAccountFsm::sendCreateRequest(const std::string& url, const std::string& messageBody)
{
   if (mHttpTransmissionEnabled)
   {
      mManager->sendCreateMessage(url.c_str(), messageBody.c_str());
   }
}

void AlianzaApiAccountFsm::sendUpdateRequest(const std::string& url, const std::string& messageBody)
{
   if (mHttpTransmissionEnabled)
   {
      std::string authToken = (mState == AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_QueryClientConfig) ? getSessionInfo().getUa(getCurrentExtension())->userAuthToken : "";
      mManager->sendUpdateMessage(url.c_str(), messageBody.c_str(), authToken);
   }
}

void AlianzaApiAccountFsm::sendDestroyRequest(const std::string& url, const std::string& messageBody)
{
   if (mHttpTransmissionEnabled)
   {
      mManager->sendDestroyMessage(url.c_str(), messageBody.c_str());
   }
}

void AlianzaApiAccountFsm::sendQueryRequest(const std::string& url, const std::string& messageBody)
{
   if (mHttpTransmissionEnabled)
   {
      mManager->sendQueryMessage(url.c_str(), messageBody.c_str());
   }
}

bool AlianzaApiAccountFsm::hasNumberBeenReservedInPartition() const
{
   return mNumberReservedInPartition;
}

bool AlianzaApiAccountFsm::hasNumberBeenAddedInAccount() const
{
   return mNumberAddedInAccount;
}

bool AlianzaApiAccountFsm::hasAccountBeenCreatedInPartition() const
{
   return mAccountCreatedInPartition;
}

void AlianzaApiAccountFsm::setNumberReservedInPartition(bool reserved)
{
   mNumberReservedInPartition = reserved;
}

void AlianzaApiAccountFsm::setNumberAddedInAccount(bool added)
{
   mNumberAddedInAccount = added;
}

void AlianzaApiAccountFsm::setAccountCreatedInPartition(bool created)
{
   mAccountCreatedInPartition = created;
}

void AlianzaApiAccountFsm::setUserCreatedForExtension(const std::string& extension)
{
   std::vector<std::string> extensions;
   getSessionInfo().getExtensions(extensions);
   for (std::vector<std::string>::iterator i = extensions.begin(); i != extensions.end(); ++i)
   {
      if ((*i) == extension)
      {
         getSessionInfo().getUa(*i)->userCreated = true;
         break;
      }
   }
}

void AlianzaApiAccountFsm::setDeviceCreatedForExtension(const std::string& extension)
{
   std::vector<std::string> extensions;
   getSessionInfo().getExtensions(extensions);
   for (std::vector<std::string>::iterator i = extensions.begin(); i != extensions.end(); ++i)
   {
      if ((*i) == extension)
      {
         getSessionInfo().getUa(*i)->deviceCreated = true;
         break;
      }
   }
}

std::string AlianzaApiAccountFsm::getNextExtension() const
{
   std::string extension("");
   AlianzaSipUaInfo ua("");
   if (getUaToAddUser(ua))
   {
      extension = ua.extension;
   }
   return extension;
}

std::string AlianzaApiAccountFsm::getCurrentExtension() const
{
   return mCurrentExtension;
}

void AlianzaApiAccountFsm::setCurrentExtension(const std::string& extension)
{
   mCurrentExtension = extension;
}

void AlianzaApiAccountFsm::updateExtensionsAttemptedForUser(const std::string& extension)
{
   mExtensionsAttemptedUser.insert(extension);
}

void AlianzaApiAccountFsm::updateExtensionsAttemptedForDevice(const std::string& extension)
{
   mExtensionsAttemptedDevice.insert(extension);
}

bool AlianzaApiAccountFsm::getUaToAddUser(AlianzaSipUaInfo& info) const
{
   std::vector<std::string> extensions;
   mAccount->config.alianzaSession.getExtensions(extensions);
   for (std::vector<std::string>::iterator i = extensions.begin(); i != extensions.end(); ++i)
   {
      AlianzaSipUaInfo& ua = *(mAccount->config.alianzaSession.getUa(*i));
      if (ua.userCreated == false)
      {
         if (mExtensionsAttemptedUser.find(*i) == mExtensionsAttemptedUser.end())
         {
            info = ua;
            return true;
         }
      }
   }
   return false;
}

bool AlianzaApiAccountFsm::getUaToAddDevice(AlianzaSipUaInfo& info) const
{
   std::vector<std::string> extensions;
   // mAccount->config.alianzaSession getSessionInfo().getExtensions(extensions);
   mAccount->config.alianzaSession.getExtensions(extensions);
   for (std::vector<std::string>::iterator i = extensions.begin(); i != extensions.end(); ++i)
   {
      AlianzaSipUaInfo& ua = *(mAccount->config.alianzaSession.getUa(*i));
      if (ua.deviceCreated == false)
      {
         if (mExtensionsAttemptedDevice.find(*i) == mExtensionsAttemptedDevice.end())
         {
            info = ua;
            return true;
         }
      }
   }
   return false;
}

bool AlianzaApiAccountFsm::updateUa(const AlianzaSipUaInfo& info)
{
   std::vector<std::string> extensions;
   getSessionInfo().getExtensions(extensions);
   for (std::vector<std::string>::iterator i = extensions.begin(); i != extensions.end(); ++i)
   {
      if ((*i) == info.extension)
      {
         AlianzaSipUaInfo& ua = *(getSessionInfo().getUa(*i));
         ua = info;
         return true;
      }
   }
   return false;
}

void AlianzaApiAccountFsm::disableHttpRequestTransmission()
{
   mHttpTransmissionEnabled = false;
}

void AlianzaApiAccountFsm::enableHttpRequestTransmission()
{
   mHttpTransmissionEnabled = true;
}

void AlianzaApiAccountFsm::fireAlianzaApiIdentityResetEvent(const AlianzaApiIdentityResetEvent& args)
{
   mManager->fireAlianzaApiIdentityResetEvent(mHandle, args);
}

void AlianzaApiAccountFsm::fireAlianzaApiHttpResponseTimeoutEvent(const AlianzaApiHttpResponseTimeoutEvent& args)
{
   mManager->fireAlianzaApiHttpResponseTimeoutEvent(mHandle, args);
}

void AlianzaApiAccountFsm::fireAlianzaApiHttpDelayRequestTimeoutEvent(const AlianzaApiHttpDelayRequestTimeoutEvent& args)
{
   mManager->fireAlianzaApiHttpDelayRequestTimeoutEvent(mHandle, args);
}

int AlianzaApiAccountFsm::enable()
{
   AlianzaApiAccountFsmState* state = mStateFactory->getState(mState);
   if (state)
   {
      ApiLog("AlianzaApiAccountFsm::enable(): " << FSM_LOG_PREFIX);
      state->enable(shared_from_this());
   }
   return kSuccess;
}

int AlianzaApiAccountFsm::disable()
{
   AlianzaApiAccountFsmState* state = mStateFactory->getState(mState);
   if (state)
   {
      ApiLog("AlianzaApiAccountFsm::disable(): " << FSM_LOG_PREFIX);
      state->disable(shared_from_this());
   }
   return kSuccess;
}

// DeadlineTimerHandler

void AlianzaApiAccountFsm::onTimer(unsigned short timerId, void* appState)
{
   switch (timerId)
   {
      case ALIANZA_API_HTTP_REQUEST_TIMER_ID: onHttpRequestTimeout(); break;
      case ALIANZA_API_DELAY_REQUEST_TIMER_ID: onDelayRequestTimeout(); break;
      case ALIANZA_API_DELAY_REGISTRATION_TIMER_ID: onDelayRegistrationTimeout(); break;
      default:
      {
         ApiLog("AlianzaApiAccountFsm::onTimer(): " << FSM_LOG_PREFIX << " invalid timer-id: " << timerId);
         assert(0);
         break;
      }
   }
}

void AlianzaApiAccountFsm::resetHttpRequestTimer()
{
   mHttpRequestTimer.cancel();
   mHttpRequestTimer.expires_from_now(mHttpRequestTimeout);
   mHttpRequestTimer.async_wait(this, ALIANZA_API_HTTP_REQUEST_TIMER_ID, NULL);
}

void AlianzaApiAccountFsm::cancelHttpRequestTimer()
{
   mHttpRequestTimer.cancel();
}

void AlianzaApiAccountFsm::resetDelayRequestTimer()
{
   mDelayRequestTimer.cancel();
   mDelayRequestTimer.expires_from_now(mDelayRequestTimeout);
   mDelayRequestTimer.async_wait(this, ALIANZA_API_DELAY_REQUEST_TIMER_ID, NULL);
}

void AlianzaApiAccountFsm::cancelDelayRequestTimer()
{
   mDelayRequestTimer.cancel();
}

void AlianzaApiAccountFsm::resetDelayRegistrationTimer()
{
   mDelayRegistrationTimer.cancel();
   mDelayRegistrationTimer.expires_from_now(mDelayRegistrationTimeout);
   mDelayRegistrationTimer.async_wait(this, ALIANZA_API_DELAY_REGISTRATION_TIMER_ID, NULL);
}

void AlianzaApiAccountFsm::cancelDelayRegistrationTimer()
{
   mDelayRegistrationTimer.cancel();
}

void AlianzaApiAccountFsm::cancelTimers()
{
   cancelHttpRequestTimer();
   cancelDelayRequestTimer();
   cancelDelayRegistrationTimer();
}

void AlianzaApiAccountFsm::setHttpRequestTimeoutMsecs(unsigned int msecs)
{
   mHttpRequestTimeout = msecs;
}

void AlianzaApiAccountFsm::setDelayRequestTimeoutMsecs(unsigned int msecs)
{
   mDelayRequestTimeout = msecs;
}

void AlianzaApiAccountFsm::setDelayRegistrationTimeoutMsecs(unsigned int msecs)
{
   mDelayRegistrationTimeout = msecs;
}

void AlianzaApiAccountFsm::onHttpRequestTimeout()
{
   AlianzaApiAccountFsmState* state = mStateFactory->getState(mState);
   if (state)
   {
      ApiLog("AlianzaApiAccountFsm::onHttpRequestTimeout(): " << FSM_LOG_PREFIX);
      AlianzaApiHttpResponseTimeoutEvent evt(getHandle());
      fireAlianzaApiHttpResponseTimeoutEvent(evt);
      state->onHttpRequestTimeout(shared_from_this());
   }
}

void AlianzaApiAccountFsm::onDelayRequestTimeout()
{
   AlianzaApiAccountFsmState* state = mStateFactory->getState(mState);
   if (state)
   {
      ApiLog("AlianzaApiAccountFsm::onDelayRequestTimeout(): " << FSM_LOG_PREFIX);
      AlianzaApiHttpDelayRequestTimeoutEvent evt(getHandle());
      fireAlianzaApiHttpDelayRequestTimeoutEvent(evt);
      state->onDelayRequestTimeout(shared_from_this());
   }
}

void AlianzaApiAccountFsm::onDelayRegistrationTimeout()
{
   AlianzaApiAccountFsmState* state = mStateFactory->getState(mState);
   if (state)
   {
      ApiLog("AlianzaApiAccountFsm::onDelayRegistrationTimeout(): " << FSM_LOG_PREFIX);
      state->onDelayRegistrationTimeout(shared_from_this());
   }
}

void AlianzaApiAccountFsm::onAlianzaApiHttpResponse(test::AlianzaApiHandle handle, const AlianzaApiHttpResponseEvent& evt)
{
   AlianzaApiAccountFsmState* state = mStateFactory->getState(mState);
   if (state)
   {
      ApiLog("AlianzaApiAccountFsm::onAlianzaApiHttpResponse(): " << FSM_LOG_PREFIX << " reqId: " << evt.requestId); 
      state->onAlianzaApiHttpResponse(shared_from_this(), evt);

      // TODO:
      // There could be some other that might possibly require default handlers
      // in the base state, or possibly only pass on particular success-failure callbacks rather
      // than the generic http response callback, e.g. onSuccess, onFailure, etc., e.g.
      // if ((evt.rc == 404) && AlianzaApiTestHelper::isErrorTNDisconnectEventInProgress(evt.response))
      // {
      //    state->onTNDisconnectEventInProgress(shared_from_this(), evt);
      // }
      // else
      // {
      //    state->onAlianzaApiHttpResponse(shared_from_this(), evt);
      // }
      // AlianzaApiTestHelper::isErrorPhoneNumberAlreadyExists(evt.response)
      // AlianzaApiTestHelper::isErrorDuplicateAccountNumber(evt.response)
      // AlianzaApiTestHelper::isErrorDuplicateUsername(evt.response)
      // AlianzaApiTestHelper::isErrorSipUsernameInUse(evt.response)
      // AlianzaApiTestHelper::isErrorEmailAddressInUse(evt.response)
      // AlianzaApiTestHelper::isErrorTNDisconnectEventInProgress(evt.response)
      // AlianzaApiTestHelper::isErrorInvalidStatus(evt.response)
      // AlianzaApiTestHelper::isErrorTelephoneNumbersExist(evt.response)
      // AlianzaApiTestHelper::isErrorServiceActivationEventInProgress(evt.response)
      // AlianzaApiTestHelper::isErrorTelephoneNumberEntityNotFound(evt.response)
      // else if ((evt.rc >= 200) || (evt.rc <= 299))
      // else if ((evt.rc >= 300) || (evt.rc <= 699))
   }
   else
   {
      ErrLog(<< "AlianzaApiAccountFsm::onAlianzaApiHttpResponse() unhandled HTTP response");
   }
}

void AlianzaApiAccountFsm::changeState(AlianzaApiAccountFsmStateType newStateType, int subState)
{
   AlianzaApiAccountFsmStateType currentStateType = mState;
   ApiLog("AlianzaApiAccountFsm::changeState(): " << FSM_LOG_PREFIX << " changing state of session from: " << AlianzaApiAccountFsmState::getName(currentStateType) << " to: " << AlianzaApiAccountFsmState::getName(newStateType) << " sub-state: " << subState);

   if (!mStateFactory)
   {
      ApiLog("AlianzaApiAccountFsm::changeState(): " << FSM_LOG_PREFIX << " factory not initialized");
      return;
   }

   AlianzaApiAccountFsmState* currentState = mStateFactory->getState(currentStateType);
   if (currentState)
   {
      currentState->onExit(shared_from_this());
   }

   mState = newStateType;

   AlianzaApiAccountStatusEvent evt;
   evt.currentState = newStateType;
   evt.previousState = currentStateType;

   mManager->fireAlianzaApiAccountStatusEvent(getHandle(), evt);

   AlianzaApiAccountFsmState* newState = mStateFactory->getState(newStateType);
   if (newState)
   {
      newState->setSubState(subState);
      newState->onEntry(shared_from_this());
   }

   if (newStateType == AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle)
   {
      ApiLog("AlianzaApiAccountFsm::changeState(): " << FSM_LOG_PREFIX << " destroying fsm");

      cancelTimers();
      mManager->destroyAccount(getHandle());
   }
}

void AlianzaApiAccountFsm::addObserver()
{
   mManager->addObserver(shared_from_this());
}

void AlianzaApiAccountFsm::removeObserver()
{
   mManager->removeObserver(shared_from_this());
}

}

}
