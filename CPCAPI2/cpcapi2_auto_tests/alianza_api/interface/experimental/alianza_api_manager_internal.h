#pragma once

#ifndef CPCAPI2_TEST_ALIANZA_API_MANAGER_INTERNAL_H
#define CPCAPI2_TEST_ALIANZA_API_MANAGER_INTERNAL_H

#include "cpcapi2defs.h"
#include "brand_branded.h"
#include <cpcapi2.h>
#include "alianza_api/interface/public/alianza_api_manager.h"
#include "alianza_api/interface/public/alianza_api_handler.h"
#include "alianza_api/interface/public/alianza_api_types.h"
#include "alianza_api/impl/alianza_api_account_fsm.h"
#include "cpcapi2_test_fixture.h"
#include "phone/PhoneModule.h"
#include "phone/PhoneInterface.h"
#include <map>
#include <set>
#include <future>


class TestAccount;

namespace CPCAPI2
{

namespace test
{

class AlianzaApiManagerInternal : public AlianzaApiManager
{
public:

   AlianzaApiManagerInternal(Phone* phone = NULL) {}
   virtual~ AlianzaApiManagerInternal() {}

   virtual void fireHttpResponseEvent(const AlianzaApiHttpResponseEvent& args) = 0;
   virtual void fireAlianzaApiAccountStatusEvent(AlianzaApiAccountHandle account, const AlianzaApiAccountStatusEvent& args) = 0;
   virtual void fireAlianzaApiIdentityResetEvent(AlianzaApiAccountHandle account, const AlianzaApiIdentityResetEvent& args) = 0;
   virtual void fireAlianzaApiHttpResponseTimeoutEvent(AlianzaApiAccountHandle account, const AlianzaApiHttpResponseTimeoutEvent& args) = 0;
   virtual void fireAlianzaApiHttpDelayRequestTimeoutEvent(AlianzaApiAccountHandle account, const AlianzaApiHttpDelayRequestTimeoutEvent& args) = 0;

   virtual int addObserver(std::shared_ptr<AlianzaApiHandler> observer) = 0;
   virtual int removeObserver(std::shared_ptr<AlianzaApiHandler> observer) = 0;
   virtual int destroyAccount(AlianzaApiAccountHandle account) = 0;

   // For Alianza API unit tests
   virtual std::shared_ptr<AlianzaApiAccountFsm> getAccountFsm(AlianzaApiAccountHandle account) = 0;
   virtual int enableHttpRequestTransmission(AlianzaApiAccountHandle account) = 0;
   virtual int disableHttpRequestTransmission(AlianzaApiAccountHandle account) = 0;

};

}

}

#endif // CPCAPI2_TEST_ALIANZA_API_MANAGER_INTERNAL_H

