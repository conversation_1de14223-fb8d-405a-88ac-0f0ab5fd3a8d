#pragma once

#ifndef CPCAPI2_TEST_ALIANZA_API_TYPES_H
#define CPCAPI2_TEST_ALIANZA_API_TYPES_H

#include "brand_branded.h"
#include <cpcapi2.h>

#define ALIANZA_API_HTTP_REQUEST_TIMER_ID            1
#define ALIANZA_API_DELAY_REQUEST_TIMER_ID           2
#define ALIANZA_API_DELAY_REGISTRATION_TIMER_ID      3

#define ALIANZA_API_HTTP_REQUEST_TIMEOUT_MSECS       5000
#define ALIANZA_API_DELAY_REQUEST_TIMEOUT_MSECS      5000
#define ALIANZA_API_DELAY_REGISTRATION_TIMEOUT_MSECS 3000

#define ALIANZA_API_NUMBER_QUERY_RETRIES             5
#define ALIANZA_API_NUMBER_RESERVE_RETRIES           5
#define ALIANZA_API_CREATE_ACCOUNT_RETRIES           3
#define ALIANZA_API_ADD_USER_RETRIES                 1
#define ALIANZA_API_ADD_DEVICE_RETRIES               1
#define ALIANZA_API_ADD_NUMBER_RETRIES               5
#define ALIANZA_API_UPDATE_CALLER_ID_RETRIES         3
#define ALIANZA_API_REMOVE_NUMBER_RETRIES            5
#define ALIANZA_API_DELETE_ACCOUNT_RETRIES           1
#define ALIANZA_API_CHECK_NUMBER_IN_ACCOUNT_RETRIES  10
#define ALIANZA_API_QUERY_CLIENT_CONFIG_RETRIES      3

namespace CPCAPI2
{

namespace test
{

typedef unsigned int AlianzaApiHandle;
typedef unsigned int AlianzaApiAccountHandle;

enum class AlianzaApiAccountFsmStateType
{
   AlianzaApiAccountFsmStateType_Idle,
   AlianzaApiAccountFsmStateType_QueryNumber,
   AlianzaApiAccountFsmStateType_ReserveNumber,
   AlianzaApiAccountFsmStateType_CreateAccount,
   AlianzaApiAccountFsmStateType_AddUser,
   AlianzaApiAccountFsmStateType_AddNumber,
   AlianzaApiAccountFsmStateType_UpdateCallerId,
   AlianzaApiAccountFsmStateType_UpdateUser,
   AlianzaApiAccountFsmStateType_Enabled,
   AlianzaApiAccountFsmStateType_RemoveNumber, // remove number from account
   AlianzaApiAccountFsmStateType_DeleteAccount,
   AlianzaApiAccountFsmStateType_ReleaseNumber, // release number in partition
   AlianzaApiAccountFsmStateType_SetGroupName,
   AlianzaApiAccountFsmStateType_UserAuth,
   AlianzaApiAccountFsmStateType_QueryClientConfig,
   AlianzaApiAccountFsmStateType_CheckNumberInAccount
};

struct AlianzaApiConfig
{
   // d2/dev:  std::string baseUrl = "https://api.d2.alianza.com/v2/";
   // q2/qa:   std::string baseUrl = "https://api.q2.alianza.com/v2/";
   // b2/beta: std::string baseUrl = "https://api.b2.alianza.com/v2/";
   // p2/prod: std::string baseUrl = "https://api.alianza.com/v2/";

   // The base url of the admin portal, that is required for the REST api requests.
   // The base url is unique to the dev, qa, beta and prod test environments
   std::string publicApiUrl = "";

   // Alianza API username and password required for authorization to retrieve
   // the auth-token necessary for subsequent requests to the admin portal
   std::string apiUsername = "";
   std::string apiPassword = "";

   std::string debugContext; // for logging
};

struct AlianzaApiHttpResponseEvent
{
   int rc;
   std::string response;
   AlianzaApiHandle handle;
   std::string requestId; // set in AlianzaApiManagerInterface::sendMessageImpl to aide in debug tracing
   AlianzaApiHttpResponseEvent() : rc(0), response("") {}
   AlianzaApiHttpResponseEvent(int rc_, std::string response_) : rc(rc_), response(response_) {}
   virtual~ AlianzaApiHttpResponseEvent() {}
};

struct AlianzaApiIdentityResetEvent
{
   std::string previousNumber;
   std::string currentNumber;
   std::string previousUsername;
   std::string currentUsername;
   AlianzaApiAccountHandle handle;
   AlianzaApiIdentityResetEvent() : handle(0), previousNumber(""), currentNumber(""), previousUsername(""), currentUsername("") {}
   AlianzaApiIdentityResetEvent(AlianzaApiAccountHandle handle_, std::string previousNumber_, std::string currentNumber_, std::string previousUsername_ = "", std::string currentUsername_ = "") :
      handle(handle_),
      previousNumber(previousNumber_),
      currentNumber(currentNumber_),
      previousUsername(previousUsername_),
      currentUsername(currentUsername_) {}
   virtual~ AlianzaApiIdentityResetEvent() {}
};

struct AlianzaApiHttpResponseTimeoutEvent
{
   AlianzaApiAccountHandle handle;
   AlianzaApiHttpResponseTimeoutEvent() : handle(0) {}
   AlianzaApiHttpResponseTimeoutEvent(AlianzaApiAccountHandle handle_) : handle(handle_) {}
   virtual~ AlianzaApiHttpResponseTimeoutEvent() {}
};

struct AlianzaApiHttpDelayRequestTimeoutEvent
{
   AlianzaApiAccountHandle handle;
   AlianzaApiHttpDelayRequestTimeoutEvent() : handle(0) {}
   AlianzaApiHttpDelayRequestTimeoutEvent(AlianzaApiAccountHandle handle_) : handle(handle_) {}
   virtual~ AlianzaApiHttpDelayRequestTimeoutEvent() {}
};

struct AlianzaApiAccountStatusEvent
{
   AlianzaApiAccountStatusEvent() :
      currentState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle),
      previousState(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle) {}
   virtual~ AlianzaApiAccountStatusEvent() {}

   AlianzaApiAccountFsmStateType currentState;
   AlianzaApiAccountFsmStateType previousState;
   AlianzaApiAccountHandle handle;
   // std::string numberReserved;
};

std::ostream& operator<<(std::ostream& os, const AlianzaApiAccountFsmStateType& state);
std::ostream& operator<<(std::ostream& os, const AlianzaApiConfig& config);

}

}

#endif // CPCAPI2_TEST_ALIANZA_API_TYPES_H

