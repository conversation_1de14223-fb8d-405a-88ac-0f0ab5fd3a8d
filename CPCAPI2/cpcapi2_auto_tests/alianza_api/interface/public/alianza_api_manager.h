#pragma once

#ifndef CPCAPI2_TEST_ALIANZA_API_MANAGER_H
#define CPCAPI2_TEST_ALIANZA_API_MANAGER_H

#include "cpcapi2defs.h"
#include "brand_branded.h"
#include <cpcapi2.h>
#include "alianza_api/interface/public/alianza_api_handler.h"
#include "alianza_api/interface/public/alianza_api_types.h"
#include "cpcapi2_test_fixture.h"
#include "phone/PhoneModule.h"
#include "phone/PhoneInterface.h"
#include <map>
#include <set>
#include <future>


class TestAccount;

namespace CPCAPI2
{

namespace test
{

/**
 * The AlianzaApiManager manages the configuration, authorization and transmission of the http
 * requests to make use of the Alianza REST API. The manager also provides the interfaces to
 * create, enable and disable the accounts in the Alianza backend. The application has access to
 * the responses from the Alianza admin portal through the Alianza API handler event callbacks.
*/
class AlianzaApiManager
{
public:

   /**
   * Get a reference to the AlianzaApiManager interface.
   */
   static std::shared_ptr<AlianzaApiManager> getInterface(TestAccount* account);

   AlianzaApiManager(Phone* phone = NULL) {}
   virtual~ AlianzaApiManager() {}

   virtual int setHandler(AlianzaApiHandler* handler) = 0;

   /**
    * Start the manager with the configuration parameters required to make use of the Alianza API.
   */
   virtual int start(const AlianzaApiConfig& config) = 0;

   virtual int shutdown() = 0;

   /**
    * Trigger the manager to make an authorization request with the neccessary credentials, in
    * order to retrieve the authentication-token that will be required in all subsequent requests
    * made to the Alianza API.
   */
   virtual int authorize() = 0;

   /**
    * Trigger the manager to send the specified message to the Alianza API.
    * 
    * @param url Alianza API URL that the message should be sent to (base-url + request-postfix)
    * @param message Message body that conforms to the specific Alianza API json-request
   */
   virtual int sendCreateMessage(const std::string& url, const std::string& message) = 0;
   virtual int sendUpdateMessage(const std::string& url, const std::string& message, const std::string& authToken = std::string("")) = 0;
   virtual int sendDestroyMessage(const std::string& url, const std::string& message) = 0;
   virtual int sendQueryMessage(const std::string& url, const std::string& message) = 0;

   /**
    * Returns the Alianza backend account handle for the account instantiation in this manager.
   */
   virtual AlianzaApiAccountHandle createAccount(TestAccount* account) = 0;

   /**
    * Triggers the enablement of the backend account which includes:
    * - query number availability and reserving the number in environment partition
    * - create the account and the associated device and user
    * - add the reserved number to the account, and update the user caller-id
   */
   virtual int enableAccount(AlianzaApiAccountHandle account) = 0;

   /**
    * Triggers the disablement of the backend account which includes:
    * - removing the reserved number from the account
    * - deleting the backend account - this deletes the associated user and device
    * - releases the number from the environment partition
   */
   virtual int disableAccount(AlianzaApiAccountHandle account) = 0;

};

}

}

#endif // CPCAPI2_TEST_ALIANZA_API_MANAGER_H

