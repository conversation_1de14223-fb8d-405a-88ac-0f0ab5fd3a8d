#pragma once

#ifndef CPCAPI2_TEST_ALIANZA_API_HANDLER_H
#define CPCAPI2_TEST_ALIANZA_API_HANDLER_H

#include "brand_branded.h"
#include <cpcapi2.h>
#include "alianza_api_types.h"


namespace CPCAPI2
{

namespace test
{

class AlianzaApiHandler
{
public:
   AlianzaApiHandler() {};
   virtual~ AlianzaApiHandler() {};

   // This event is triggered everytime an http response is received
   virtual void onAlianzaApiHttpResponse(test::AlianzaApiHandle handle, const AlianzaApiHttpResponseEvent& evt) = 0;

   // This event is triggered everytime the identity is reset - phone number, sip username
   virtual void onAlianzaApiIdentityReset(test::AlianzaApiAccountHandle handle, const AlianzaApiIdentityResetEvent& evt) = 0;

   // This event is triggered everytime the http response timeout occurs
   virtual void onAlianzaApiHttpResponseTimeout(test::AlianzaApiAccountHandle handle, const AlianzaApiHttpResponseTimeoutEvent& evt) = 0;

   // This event is triggered everytime the http request is resent
   virtual void onAlianzaApiHttpDelayRequestTimeout(test::AlianzaApiAccountHandle handle, const AlianzaApiHttpDelayRequestTimeoutEvent& evt) = 0;

   // This event is triggered everytime the account fsm changes state
   virtual void onAlianzaApiAccountStatusEvent(test::AlianzaApiAccountHandle account, const AlianzaApiAccountStatusEvent& evt) = 0;

};

}

}

#endif // CPCAPI2_TEST_ALIANZA_API_HANDLER_H

