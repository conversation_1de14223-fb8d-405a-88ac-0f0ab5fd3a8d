#include "crashpad_integration.h"
#include "client/crashpad_client.h"
#include "client/crash_report_database.h"
#include "client/simulate_crash_mac.h"
#include "client/settings.h"
#include "util/file/file_io.h"

using namespace crashpad;

bool CPCAPI2::test::Crashpad::registerCrashpad(const char* testName)
{

   CrashpadClient client;
   bool rc;

   std::map<std::string, std::string> annotations;
   std::vector<std::string> arguments;

   /*
    * ENSURE THIS VALUE IS CORRECT.
    *
    * This is the directory you will use to store and queue crash data.
    */
   std::string db_path("./dumps");

   /*
    * ENSURE THIS VALUE IS CORRECT.
    *
    * Crashpad has the ability to support crashes both in-process and out-of-process.
    * The out-of-process handler is significantly more robust than traditional in-process
    * crash handlers. This path may be relative.
    */
   std::string handler_path("../../osx_libs/crashpad/bin/crashpad_handler");

   /*
    * YOU MUST CHANGE THIS VALUE.
    *
    * This should point to your server dump submission port (labeled as "http/writer"
    * in the listener configuration pane. Preferrably, the SSL enabled port should
    * be used. If Backtrace is hosting your instance, the default port is 6098.
    */
   std::string url("http://www.counterpath.com/reports");

   /*
    * THE FOLLOWING ANNOTATIONS MUST BE SET.
    *
    * Backtrace supports many file formats. Set format to minidump so it knows
    * how to process the incoming dump.
    */
   annotations["format"] = "minidump";

   // The string "$TkFutcubSe_TestName$" acts as a key that stores the test name as its value.
   std::string testName_str(testName);
   annotations["$TkFutcubSe_TestName"] = testName_str + "$";

   /*
    * REMOVE THIS FOR ACTUAL BUILD.
    *
    * We disable crashpad rate limiting for this example.
    */
   arguments.push_back("--no-rate-limit");

   base::FilePath db(db_path);
   base::FilePath handler(handler_path);

   std::unique_ptr<CrashReportDatabase> database =
   crashpad::CrashReportDatabase::Initialize(db);
   if (database == nullptr || database->GetSettings() == NULL)
      return false;

   /* Enable automated uploads. */
   database->GetSettings()->SetUploadsEnabled(false);

   rc = client.StartHandler(handler,
                            db,
                            db,
                            url,
                            annotations,
                            arguments,
                            true,
                            true);
   if (rc == false)
      return false;
   
   return true;
}
