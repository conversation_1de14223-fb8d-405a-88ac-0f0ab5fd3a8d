#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#include "cpcapi2_test_fixture.h"
#include "test_events.h"

using namespace CPCAPI2;
using namespace CPCAPI2::SipEvent;

namespace {

class EventSanityTests : public CpcapiAutoTest
{
public:
   EventSanityTests() {}
   virtual ~EventSanityTests() {}
};

TEST_F(EventSanityTests, DisabledAccountCreateSub) {
   TestAccount alice("alice", Account_Init);
   PhoneErrorEvent evt;
   std::string module;
   SipEventSubscriptionHandle aliceSubs = alice.subs->createSubscription(alice.handle);
   assertPhoneError(alice, "SipAccountInterface");
}

}

