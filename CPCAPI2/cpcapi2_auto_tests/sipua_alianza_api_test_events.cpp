#include "sipua_alianza_api_test_events.h"
#include "test_account_events.h"
#include "test_events.h"
#include "test_call_events.h"
#include "sipua_alianza_api_test_helper.h"

#include <sstream>

#include "util/CurlPPHelper.h"
#include "util/CurlPPProgress.h"
#include "util/CurlPPSSL.h"
#include "util/CurlURI.h"

// rapidjson
#include <writer.h>
#include <stringbuffer.h>
#include <document.h>


using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::SipAccount;
using namespace ::testing;

using namespace std::chrono;
using namespace curlpp::options;


AlianzaApiTestEvents::AlianzaApiTestEvents()
{
}

AlianzaApiTestEvents::~AlianzaApiTestEvents()
{
}

void AlianzaApiTestEvents::expectAuthorizationSuccess(
   int line,
   CPCAPI2::test::EventHandler* apiEvents,
   EVENT_VALIDATOR(AlianzaApiHttpResponseEvent) validator)
{
   AlianzaApiHttpResponseEvent evt;

   AlianzaApiHandle handle = 0;
   cpcExpectEvent(apiEvents, "AlianzaApiHandler::onAlianzaApiHttpResponse", 15000, CPCAPI2::test::AlwaysTruePred(), handle, evt);
   ASSERT_EQ(evt.rc, 201) << " error in http response: status: " << evt.rc << " Alianza API HTTP authorize request failed";

   if (nullptr != validator)
   {
      validator(evt);
   }
}

void AlianzaApiTestEvents::expectAuthorizationSuccessWithToken(
   int line,
   CPCAPI2::test::EventHandler* apiEvents,
   std::string& authToken,
   EVENT_VALIDATOR(AlianzaApiHttpResponseEvent) validator)
{
   AlianzaApiHttpResponseEvent evt;

   AlianzaApiHandle handle = 0;
   cpcExpectEvent(apiEvents, "AlianzaApiHandler::onAlianzaApiHttpResponse", 10000, CPCAPI2::test::AlwaysTruePred(), handle, evt);
   ASSERT_EQ(evt.rc, 201) << " error in http response: status: " << evt.rc << " aborting http authorize request";

   AlianzaApiTestHelper::extractUserAuthTokenFromUserAuthResponse(evt.response, authToken);

   if (nullptr != validator)
   {
      validator(evt);
   }
}

void AlianzaApiTestEvents::expectNumberCreatedInPartition(
   int line,
   CPCAPI2::test::EventHandler* apiEvents,
   const std::string& phoneNumber,
   std::string& phoneId,
   EVENT_VALIDATOR(AlianzaApiHttpResponseEvent) validator)
{
   AlianzaApiHttpResponseEvent evt;
   AlianzaApiHandle handle = 0;

   cpcExpectEvent(apiEvents, "AlianzaApiHandler::onAlianzaApiHttpResponse", 10000, CPCAPI2::test::AlwaysTruePred(), handle, evt);
   // safeCout("expectNumberCreatedInPartition(): " << evt.rc << " response: " << evt.response.c_str());
   ASSERT_EQ(evt.rc, 201) << " error in http response: status: " << evt.rc << " aborting http partition number create request";

   /*
         if (rc == 400)
         {
            if (reasonMatch(response.str().c_str(), "PhoneNumberAlreadyExists"))
            {
               // TODO: manage number of attempts to retry
               safeCout("expectNumberCreatedInPartition(): retrying attempt to create number in partition due to duplicate number response");
               return createNumberInPartition(api, sipua);
            }
         }

         400 response: {
         "status" : 400,
         "messages" : [ "TNDisconnectEventInProgress" ]
   */

   std::shared_ptr<rapidjson::Document> jsonResponse(new rapidjson::Document);
   jsonResponse->Parse<0>(evt.response.c_str());
   ASSERT_FALSE(jsonResponse->HasParseError()) << " invalid request format, parse error occured:" << jsonResponse->GetParseError() << " aborting decode";
   ASSERT_TRUE(jsonResponse->HasMember("id")) << " node missing: id, aborting decode.";
   const rapidjson::Value& moduleIdVal = (*jsonResponse)["id"];
   ASSERT_TRUE(moduleIdVal.IsString()) << " invalid id format, aborting decode.";
   phoneId = (*jsonResponse)["id"].GetString();
   // safeCout("expectNumberCreatedInPartition(): phoneId: " << phoneId);

   if (nullptr != validator)
   {
      validator(evt);
   }
}

void AlianzaApiTestEvents::expectNumberCreatedInAccount(
   int line,
   CPCAPI2::test::EventHandler* apiEvents,
   const std::string& phoneId,
   EVENT_VALIDATOR(AlianzaApiHttpResponseEvent) validator)
{
   AlianzaApiHttpResponseEvent evt;
   AlianzaApiHandle handle = 0;

   cpcExpectEvent(apiEvents, "AlianzaApiHandler::onAlianzaApiHttpResponse", 10000, CPCAPI2::test::AlwaysTruePred(), handle, evt);
   // safeCout("expectNumberCreatedInAccount(): " << evt.rc << " response: " << evt.response.c_str());
   ASSERT_EQ(evt.rc, 201) << " error in http response: status: " << evt.rc << " aborting http account number create request";

   /*
         if (rc == 400)
         {
            if (reasonMatch(response.str().c_str(), "PhoneNumberAlreadyExists"))
            {
               // TODO: manage number of attempts to retry
               safeCout("expectNumberCreatedInAccount(): retrying attempt to create number in account due to duplicate number response");
               return createNumberInAccount(api, sipua);
            }
         }
   */

   if (nullptr != validator)
   {
      validator(evt);
   }
}

void AlianzaApiTestEvents::expectNumberUpdatedInAccount(
   int line,
   CPCAPI2::test::EventHandler* apiEvents,
   const std::string& phoneId,
   EVENT_VALIDATOR(AlianzaApiHttpResponseEvent) validator)
{
   AlianzaApiHttpResponseEvent evt;
   AlianzaApiHandle handle = 0;

   cpcExpectEvent(apiEvents, "AlianzaApiHandler::onAlianzaApiHttpResponse", 10000, CPCAPI2::test::AlwaysTruePred(), handle, evt);
   // safeCout("expectNumberUpdatedInAccount(): " << evt.rc << " response: " << evt.response.c_str());
   ASSERT_EQ(evt.rc, 200) << " error in http response: status: " << evt.rc << " aborting http account number update request";
   // safeCout("expectNumberUpdatedInAccount(): phone number updated with phoneId: " << phoneId);

   if (nullptr != validator)
   {
      validator(evt);
   }
}

void AlianzaApiTestEvents::expectUserUpdatedInAccount(
   int line,
   CPCAPI2::test::EventHandler* apiEvents,
   const std::string& phoneId,
   EVENT_VALIDATOR(AlianzaApiHttpResponseEvent) validator)
{
   AlianzaApiHttpResponseEvent evt;
   AlianzaApiHandle handle = 0;

   cpcExpectEvent(apiEvents, "AlianzaApiHandler::onAlianzaApiHttpResponse", 10000, CPCAPI2::test::AlwaysTruePred(), handle, evt);
   // safeCout("expectUserUpdatedInAccount(): " << evt.rc << " response: " << evt.response.c_str());
   ASSERT_EQ(evt.rc, 200) << " error in http response: status: " << evt.rc << " aborting http account user update request";
   // safeCout("expectUserUpdatedInAccount(): account user updated with phoneId: " << phoneId);

   if (nullptr != validator)
   {
      validator(evt);
   }
}

void AlianzaApiTestEvents::expectAccountCreatedInPartition(
   int line,
   CPCAPI2::test::EventHandler* apiEvents,
   std::string& accountId,
   EVENT_VALIDATOR(AlianzaApiHttpResponseEvent) validator)
{
   AlianzaApiHttpResponseEvent evt;
   AlianzaApiHandle handle = 0;

   cpcExpectEvent(apiEvents, "AlianzaApiHandler::onAlianzaApiHttpResponse", 10000, CPCAPI2::test::AlwaysTruePred(), handle, evt);
   // safeCout("expectAccountCreatedInPartition(): " << evt.rc << " response: " << evt.response.c_str());
   ASSERT_EQ(evt.rc, 201) << " error in http response: status: " << evt.rc << " aborting http account create request";

   /*
         if (rc == 400)
         {
            if (reasonMatch(response.str().c_str(), "DuplicateAccountNumber"))
            {
               // TODO: manage number of attempts to retry
               safeCout("expectAccountCreatedInPartition(): retrying attempt to create account due to duplicate account-number response");
               return createAccount(api, sipua);
            }
         } 
   */

   std::shared_ptr<rapidjson::Document> jsonResponse(new rapidjson::Document);
   jsonResponse->Parse<0>(evt.response.c_str());
   ASSERT_FALSE(jsonResponse->HasParseError()) << " invalid request format, parse error occured:" << jsonResponse->GetParseError() << " aborting decode";
   ASSERT_TRUE(jsonResponse->HasMember("id")) << " node missing: id, aborting decode.";
   const rapidjson::Value& moduleIdVal = (*jsonResponse)["id"];
   ASSERT_TRUE(moduleIdVal.IsString()) << " invalid id format, aborting decode.";
   accountId = (*jsonResponse)["id"].GetString();
   // safeCout("expectAccountCreatedInPartition(): accountId: " << accountId);

   if (nullptr != validator)
   {
      validator(evt);
   }
}

void AlianzaApiTestEvents::expectUserCreatedInAccount(
   int line,
   CPCAPI2::test::EventHandler* apiEvents,
   std::string& userId,
   std::string& voicemailId,
   AlianzaCallingPlanInfo& callingPlan,
   EVENT_VALIDATOR(AlianzaApiHttpResponseEvent) validator)
{
   AlianzaApiHttpResponseEvent evt;
   AlianzaApiHandle handle = 0;

   cpcExpectEvent(apiEvents, "AlianzaApiHandler::onAlianzaApiHttpResponse", 10000, CPCAPI2::test::AlwaysTruePred(), handle, evt);
   // safeCout("expectUserCreatedInAccount(): " << evt.rc << " response: " << evt.response.c_str());
   ASSERT_EQ(evt.rc, 201) << " error in http response: status: " << evt.rc << " aborting http account user create request";

   std::shared_ptr<rapidjson::Document> jsonResponse(new rapidjson::Document);
   jsonResponse->Parse<0>(evt.response.c_str());
   ASSERT_FALSE(jsonResponse->HasParseError()) << " invalid request format, parse error occured:" << jsonResponse->GetParseError() << " aborting decode";
   ASSERT_TRUE(jsonResponse->HasMember("id")) << " node missing: id, aborting decode";
   const rapidjson::Value& moduleIdVal = (*jsonResponse)["id"];
   ASSERT_TRUE(moduleIdVal.IsString()) << " invalid id format, aborting decode";
   userId = (*jsonResponse)["id"].GetString();

   ASSERT_TRUE(jsonResponse->HasMember("voicemailBoxId")) << " node missing: voicemailBoxId, aborting decode";
   const rapidjson::Value& voicemailBoxIdVal = (*jsonResponse)["voicemailBoxId"];
   ASSERT_TRUE(voicemailBoxIdVal.IsString()) << " invalid voicemailBoxId format, aborting decode";
   voicemailId = voicemailBoxIdVal.GetString();
   // safeCout("expectUserCreatedInAccount(): userId: " << userId << " voicemailId: " << voicemailId);

   // Populate calling plan
   ASSERT_TRUE(jsonResponse->HasMember("callingPlans")) << " node missing: callingPlans, aborting decode";
   const rapidjson::Value& callingPlansVal = (*jsonResponse)["callingPlans"];
   ASSERT_TRUE(callingPlansVal.IsArray()) << " invalid callingPlans type, aborting decode";
   for (rapidjson::Value::ConstValueIterator i = callingPlansVal.Begin(); i != callingPlansVal.End(); ++i)
   {
      ASSERT_TRUE(i->IsObject()) << " invalid callingPlans format, aborting decode";
      ASSERT_TRUE(i->HasMember("callingPlanProductId")) << " node missing: callingPlanProductId, aborting decode";
      const rapidjson::Value& callingPlanProductIdVal = (*i)["callingPlanProductId"];
      ASSERT_TRUE(callingPlanProductIdVal.IsString()) << " invalid callingPlanProductId format, aborting decode";
      callingPlan.callingPlanProductId = callingPlanProductIdVal.GetString();
      ASSERT_TRUE(i->HasMember("startDate")) << " node missing: startDate, aborting decode";
      const rapidjson::Value& startDateVal = (*i)["startDate"];
      ASSERT_TRUE(startDateVal.IsString()) << " invalid startDate format, aborting decode";
      callingPlan.startDate = startDateVal.GetString();
      ASSERT_TRUE(i->HasMember("planMinutes")) << " node missing: planMinutes, aborting decode";
      const rapidjson::Value& planMinutesVal = (*i)["planMinutes"];
      ASSERT_TRUE(planMinutesVal.IsInt()) << "invalid planMinutes format, aborting decode";
      callingPlan.planMinutes = planMinutesVal.GetInt();
      ASSERT_TRUE(i->HasMember("secondsRemaining")) << " node missing: secondsRemaining, aborting decode";
      const rapidjson::Value& secondsRemainingVal = (*i)["secondsRemaining"];
      ASSERT_TRUE(secondsRemainingVal.IsInt()) << "invalid secondsRemaining format, aborting decode";
      callingPlan.secondsRemaining = secondsRemainingVal.GetInt();
      break;
   }

   if (nullptr != validator)
   {
      validator(evt);
   }
}


void AlianzaApiTestEvents::expectConfigurationFetchedInAccount(
   int line,
   CPCAPI2::test::EventHandler* apiEvents,
   EVENT_VALIDATOR(AlianzaApiHttpResponseEvent) validator)
{
   AlianzaApiHttpResponseEvent evt;
   AlianzaApiHandle handle = 0;

   cpcExpectEvent(apiEvents, "AlianzaApiHandler::onAlianzaApiHttpResponse", 10000, CPCAPI2::test::AlwaysTruePred(), handle, evt);
   // safeCout("expectDeviceCreatedInAccount(): " << evt.rc << " response: " << evt.response.c_str());
   ASSERT_EQ(evt.rc, 200) << " error in http response: status: " << evt.rc;

   AlianzaSipUaInfo uaInfo("test");
   ASSERT_TRUE(AlianzaApiTestHelper::extractClientConfigFromQueryClientConfigResponse(evt.response, uaInfo));

   if (nullptr != validator)
   {
      validator(evt);
   }
}

void AlianzaApiTestEvents::expectGroupNameUpdatedInAccount(
   int line,
   CPCAPI2::test::EventHandler* apiEvents,
   EVENT_VALIDATOR(CPCAPI2::test::AlianzaApiHttpResponseEvent) validator)
{
   AlianzaApiHttpResponseEvent evt;
   AlianzaApiHandle handle = 0;

   cpcExpectEvent(apiEvents, "AlianzaApiHandler::onAlianzaApiHttpResponse", 10000, CPCAPI2::test::AlwaysTruePred(), handle, evt);
   // safeCout("expectDeviceCreatedInAccount(): " << evt.rc << " response: " << evt.response.c_str());
   ASSERT_EQ(evt.rc, 200) << " error in http response: status: " << evt.rc;


   std::shared_ptr<rapidjson::Document> jsonResponse(new rapidjson::Document);
   jsonResponse->Parse<0>(evt.response.c_str());
   ASSERT_FALSE(jsonResponse->HasParseError()) << " invalid request format, parse error occured:" << jsonResponse->GetParseError() << " aborting decode";

   if (nullptr != validator)
   {
      validator(evt);
   }
}

 /*
         AlianzaApiManager::sendApiRequest(): 404 response: {
         "status" : 404,
         "messages" : [ "EntityNotFound" ],
         "data" : {
         "type" : "TelephoneNumberInventory",
         "id" : "***********"
         }
         }
         */

void AlianzaApiTestEvents::expectNumberDoesNotExistInPartition(
   int line,
   CPCAPI2::test::EventHandler* apiEvents,
   const std::string& phoneId,
   EVENT_VALIDATOR(AlianzaApiHttpResponseEvent) validator)
{
   AlianzaApiHttpResponseEvent evt;
   AlianzaApiHandle handle = 0;

   cpcExpectEvent(apiEvents, "AlianzaApiHandler::onAlianzaApiHttpResponse", 10000, CPCAPI2::test::AlwaysTruePred(), handle, evt);
   // safeCout("expectNumberDoesNotExistInPartition(): " << evt.rc << " response: " << evt.response.c_str());
   ASSERT_EQ(evt.rc, 404) << " did not receive expected http response: status: " << evt.rc << " for expected query request for phoneId: " << phoneId;

   std::shared_ptr<rapidjson::Document> jsonResponse(new rapidjson::Document);
   jsonResponse->Parse<0>(evt.response.c_str());
   ASSERT_FALSE(jsonResponse->HasParseError()) << " invalid request format, parse error occured:" << jsonResponse->GetParseError() << " aborting decode";
   ASSERT_TRUE(jsonResponse->HasMember("messages")) << " node missing: messages, aborting decode";
   const rapidjson::Value& messagesVal = (*jsonResponse)["messages"];
   ASSERT_TRUE(messagesVal.IsArray()) << " invalid messages format, aborting decode";
   for (rapidjson::Value::ConstValueIterator i = messagesVal.Begin(); i != messagesVal.End(); ++i)
   {
      ASSERT_TRUE(i->IsString()) << " invalid messages array member format, aborting decode";
      std::string messages = i->GetString();
      ASSERT_TRUE(messages.compare("EntityNotFound") == 0) << " invalid messages array member value: " << messages.c_str() << ", aborting decode";
   }

   ASSERT_TRUE(jsonResponse->HasMember("data")) << " node missing: data, aborting decode";
   const rapidjson::Value& dataVal = (*jsonResponse)["data"];
   ASSERT_TRUE(dataVal.IsObject()) << " invalid data format, aborting decode";

   ASSERT_TRUE(dataVal.HasMember("type")) << " node missing: type, aborting decode";
   const rapidjson::Value& dataTypeVal = dataVal["type"];
   ASSERT_TRUE(dataTypeVal.IsString()) << " invalid type format, aborting decode";
   std::string dataType = dataTypeVal.GetString();
   ASSERT_TRUE(dataType.compare("TelephoneNumberInventory") == 0) << " invalid data-type value: " << dataType.c_str() << ", aborting decode";

   ASSERT_TRUE(dataVal.HasMember("id")) << " node missing: id, aborting decode";
   const rapidjson::Value& dataIdVal = dataVal["id"];
   ASSERT_TRUE(dataIdVal.IsString()) << " invalid id format, aborting decode";
   std::string dataId = dataIdVal.GetString();
   ASSERT_TRUE(dataId.compare(phoneId) == 0) << " invalid id value: " << dataId.c_str() << ", aborting decode";

   // safeCout("expectDeviceCreatedInAccount(): deviceId: " << deviceId);

   if (nullptr != validator)
   {
      validator(evt);
   }
}

void AlianzaApiTestEvents::expectNumberRetrievedFromPartition(
   int line,
   CPCAPI2::test::EventHandler* apiEvents,
   const std::string& phoneId,
   EVENT_VALIDATOR(AlianzaApiHttpResponseEvent) validator)
{
   AlianzaApiHttpResponseEvent evt;
   AlianzaApiHandle handle = 0;

   cpcExpectEvent(apiEvents, "AlianzaApiHandler::onAlianzaApiHttpResponse", 10000, CPCAPI2::test::AlwaysTruePred(), handle, evt);
   // safeCout("expectNumberRetrievedFromPartition(): " << evt.rc << " response: " << evt.response.c_str());
   ASSERT_EQ(evt.rc, 200) << " error in http response: status: " << evt.rc << " for query to get number for partition";

   std::shared_ptr<rapidjson::Document> jsonResponse(new rapidjson::Document);
   jsonResponse->Parse<0>(evt.response.c_str());
   ASSERT_FALSE(jsonResponse->HasParseError()) << " invalid request format, parse error occured:" << jsonResponse->GetParseError() << " aborting decode";
   ASSERT_TRUE(jsonResponse->HasMember("id")) << " node missing: id, aborting decode";
   const rapidjson::Value& idVal = (*jsonResponse)["id"];
   ASSERT_TRUE(idVal.IsString()) << " invalid id format, aborting decode";
   std::string id = idVal.GetString();
   ASSERT_TRUE(id.compare(phoneId) == 0) << " invalid id value: " << id.c_str() << ", aborting decode";

   if (nullptr != validator)
   {
      validator(evt);
   }
}

void AlianzaApiTestEvents::expectNumberDeletedInPartition(
   int line,
   CPCAPI2::test::EventHandler* apiEvents,
   const std::string& phoneId,
   EVENT_VALIDATOR(AlianzaApiHttpResponseEvent) validator)
{
   AlianzaApiHttpResponseEvent evt;
   AlianzaApiHandle handle = 0;

   cpcExpectEvent(apiEvents, "AlianzaApiHandler::onAlianzaApiHttpResponse", 10000, CPCAPI2::test::AlwaysTruePred(), handle, evt);
   // safeCout("expectNumberDeletedInPartition(): " << evt.rc << " response: " << evt.response.c_str());
   ASSERT_EQ(evt.rc, 204) << " error in http response: status: " << evt.rc << " aborting http partition number delete request for phoneId: " << phoneId;

   if (nullptr != validator)
   {
      validator(evt);
   }
}

void AlianzaApiTestEvents::expectAccountDeletedInPartition(
   int line,
   CPCAPI2::test::EventHandler* apiEvents,
   const std::string& accountId,
   EVENT_VALIDATOR(AlianzaApiHttpResponseEvent) validator)
{
   AlianzaApiHttpResponseEvent evt;
   AlianzaApiHandle handle = 0;

   cpcExpectEvent(apiEvents, "AlianzaApiHandler::onAlianzaApiHttpResponse", 10000, CPCAPI2::test::AlwaysTruePred(), handle, evt);
   // safeCout("expectAccountDeletedInPartition(): " << evt.rc << " response: " << evt.response.c_str());
   ASSERT_EQ(evt.rc, 204) << " error in http response: status: " << evt.rc << " aborting http partition number delete request for accountId: " << accountId;

   if (nullptr != validator)
   {
      validator(evt);
   }
}

void AlianzaApiTestEvents::expectNumberDeletedInAccount(
   int line,
   CPCAPI2::test::EventHandler* apiEvents,
   const std::string& phoneId,
   EVENT_VALIDATOR(AlianzaApiHttpResponseEvent) validator)
{
   AlianzaApiHttpResponseEvent evt;
   AlianzaApiHandle handle = 0;

   cpcExpectEvent(apiEvents, "AlianzaApiHandler::onAlianzaApiHttpResponse", 10000, CPCAPI2::test::AlwaysTruePred(), handle, evt);
   // safeCout("expectNumberDeletedInAccount(): " << evt.rc << " response: " << evt.response.c_str());
   ASSERT_EQ(evt.rc, 204) << " error in http response: status: " << evt.rc << " aborting http account number delete request for phoneId: " << phoneId;

   if (nullptr != validator)
   {
      validator(evt);
   }
}

void AlianzaApiTestEvents::expectAccountsRetrievedFromPartition(
   int line,
   CPCAPI2::test::EventHandler* apiEvents,
   EVENT_VALIDATOR(AlianzaApiHttpResponseEvent) validator)
{
   AlianzaApiHttpResponseEvent evt;
   AlianzaApiHandle handle = 0;

   cpcExpectEvent(apiEvents, "AlianzaApiHandler::onAlianzaApiHttpResponse", 10000, CPCAPI2::test::AlwaysTruePred(), handle, evt);
   // safeCout("expectAccountsRetrievedFromPartition(): " << evt.rc << " response: " << evt.response.c_str());
   ASSERT_EQ(evt.rc, 200) << " error in http response: status: " << evt.rc << " for query to get accounts in partition";

   if (nullptr != validator)
   {
      validator(evt);
   }
}

void AlianzaApiTestEvents::expectAccountRetrievedFromPartition(
   int line,
   CPCAPI2::test::EventHandler* apiEvents,
   EVENT_VALIDATOR(AlianzaApiHttpResponseEvent) validator)
{
   AlianzaApiHttpResponseEvent evt;
   AlianzaApiHandle handle = 0;

   cpcExpectEvent(apiEvents, "AlianzaApiHandler::onAlianzaApiHttpResponse", 10000, CPCAPI2::test::AlwaysTruePred(), handle, evt);
   // safeCout("expectAccountRetrievedFromPartition(): " << evt.rc << " response: " << evt.response.c_str());
   ASSERT_EQ(evt.rc, 200) << " error in http response: status: " << evt.rc << " for query to get info for account";

   if (nullptr != validator)
   {
      validator(evt);
   }
}

void AlianzaApiTestEvents::expectUserRetrievedFromAccount(
   int line,
   CPCAPI2::test::EventHandler* apiEvents,
   EVENT_VALIDATOR(AlianzaApiHttpResponseEvent) validator)
{
   AlianzaApiHttpResponseEvent evt;
   AlianzaApiHandle handle = 0;

   cpcExpectEvent(apiEvents, "AlianzaApiHandler::onAlianzaApiHttpResponse", 10000, CPCAPI2::test::AlwaysTruePred(), handle, evt);
   safeCout("expectUserRetrievedFromAccount(): " << evt.rc << " response: " << evt.response.c_str());
   ASSERT_EQ(evt.rc, 200) << " error in http response: status: " << evt.rc << " for query to get user for account";

   if (nullptr != validator)
   {
      validator(evt);
   }
}

void AlianzaApiTestEvents::expectClientRegistrationStatusRetrieved(
   int line,
   CPCAPI2::test::EventHandler* apiEvents,
   bool& sipRegistered,
   bool& pushRegistered,
   EVENT_VALIDATOR(CPCAPI2::test::AlianzaApiHttpResponseEvent) validator)
{
   AlianzaApiHttpResponseEvent evt;
   AlianzaApiHandle handle = 0;

   cpcExpectEvent(apiEvents, "AlianzaApiHandler::onAlianzaApiHttpResponse", 10000, CPCAPI2::test::AlwaysTruePred(), handle, evt);
   // safeCout("expectClientRegistrationStatusRetrieved(): " << evt.rc << " response: " << evt.response.c_str());
   ASSERT_EQ(evt.rc, 200) << " error in http response: status: " << evt.rc << " for query to get client for account";

   if (nullptr != validator)
   {
      validator(evt);
   }

   if (evt.rc == 200)
   {
      std::shared_ptr<rapidjson::Document> jsonResponse(new rapidjson::Document);
      jsonResponse->Parse<0>(evt.response.c_str());
      ASSERT_FALSE(jsonResponse->HasParseError()) << " invalid request format, parse error occured:" << jsonResponse->GetParseError() << " aborting decode";

      rapidjson::Value& responseObj = jsonResponse->GetObj();
      if (responseObj.IsArray())
      {
         if (responseObj.Size() > 1) safeCout("AlianzaApiTestEvents::expectClientRegistrationStatusRetrieved(): WARNING: multiple registration statuses recieved!");
         if (responseObj.Size() < 1)
         {
            safeCout("AlianzaApiTestEvents::expectClientRegistrationStatusRetrieved(): no registration statuses recieved, aborting");
            return;
         }
         responseObj = jsonResponse->GetArray()[0];
      }

      ASSERT_TRUE(responseObj.HasMember("traditionalCredentialsRegistered")) << " node missing: traditionalCredentialsRegistered, aborting decode";
      const rapidjson::Value& sipVal = responseObj["traditionalCredentialsRegistered"];
      ASSERT_TRUE(sipVal.IsBool()) << " invalid traditionalCredentialsRegistered format, aborting decode";
      sipRegistered = sipVal.GetBool();
      ASSERT_TRUE(responseObj.HasMember("pushCredentialsRegistered")) << " node missing: pushCredentialsRegistered, aborting decode";
      const rapidjson::Value& pushVal = responseObj["pushCredentialsRegistered"];
      ASSERT_TRUE(pushVal.IsBool()) << " invalid pushCredentialsRegistered format, aborting decode";
      pushRegistered = pushVal.GetBool();
   }
   else
   {
      sipRegistered = false;
      pushRegistered = false;
   }
}

void AlianzaApiTestEvents::expectNumberInAccountStatusRetrieved(
   int line,
   CPCAPI2::test::EventHandler* apiEvents,
   std::string& status,
   EVENT_VALIDATOR(CPCAPI2::test::AlianzaApiHttpResponseEvent) validator)
{
   AlianzaApiHttpResponseEvent evt;
   AlianzaApiHandle handle = 0;

   cpcExpectEvent(apiEvents, "AlianzaApiHandler::onAlianzaApiHttpResponse", 10000, CPCAPI2::test::AlwaysTruePred(), handle, evt);
   // safeCout("expectNumberInAccountStatusRetrieved(): " << evt.rc << " response: " << evt.response.c_str());
   ASSERT_EQ(evt.rc, 200) << " error in http response: status: " << evt.rc;
   ASSERT_TRUE(AlianzaApiTestHelper::extractServiceStatusForNumberInAccountResponse(evt.response, status));
   if (nullptr != validator)
   {
      validator(evt);
   }
}

void AlianzaApiTestEvents::expectNumberRetrievedFromAccount(
   int line,
   CPCAPI2::test::EventHandler* apiEvents,
   const std::string& phoneId,
   EVENT_VALIDATOR(AlianzaApiHttpResponseEvent) validator)
{
   AlianzaApiHttpResponseEvent evt;
   AlianzaApiHandle handle = 0;

   cpcExpectEvent(apiEvents, "AlianzaApiHandler::onAlianzaApiHttpResponse", 10000, CPCAPI2::test::AlwaysTruePred(), handle, evt);
   // safeCout("expectNumberRetrievedFromAccount(): " << evt.rc << " response: " << evt.response.c_str());
   ASSERT_EQ(evt.rc, 200) << " error in http response: status: " << evt.rc << " for query to get number for account";

   std::shared_ptr<rapidjson::Document> jsonResponse(new rapidjson::Document);
   jsonResponse->Parse<0>(evt.response.c_str());
   ASSERT_FALSE(jsonResponse->HasParseError()) << " invalid request format, parse error occured:" << jsonResponse->GetParseError() << " aborting decode";
   ASSERT_TRUE(jsonResponse->HasMember("id")) << " node missing: id, aborting decode";
   const rapidjson::Value& idVal = (*jsonResponse)["id"];
   ASSERT_TRUE(idVal.IsString()) << " invalid id format, aborting decode";
   std::string id = idVal.GetString();
   ASSERT_TRUE(id.compare(phoneId) == 0) << " invalid id value: " << id.c_str() << ", aborting decode";

   if (nullptr != validator)
   {
      validator(evt);
   }
}

void AlianzaApiTestEvents::expectAlianzaApiIdentityResetEvent(
   int line,
   CPCAPI2::test::EventHandler* apiEvents,
   std::string number,
   std::string username,
   EVENT_VALIDATOR(CPCAPI2::test::AlianzaApiIdentityResetEvent) validator)
{
   AlianzaApiIdentityResetEvent evt;
   AlianzaApiAccountHandle handle = 0;

   ASSERT_TRUE(apiEvents->expectEvent(line, "AlianzaApiHandler::onAlianzaApiIdentityReset", 20000, AlwaysTruePred(), handle, evt));
   safeCout("expectAlianzaApiIdentityResetEvent(): previousNumber: " << evt.previousNumber << " currentNumber: " << evt.currentNumber);
   if (!number.empty())
   {
      ASSERT_TRUE(evt.currentNumber != number) << " old-number: " << number << " new-number: " << evt.currentNumber;
   }
   if (!username.empty())
   {
      ASSERT_TRUE(evt.currentUsername != username) << " old-username: " << username << " new-username: " << evt.currentUsername;
   }

   if (nullptr != validator)
   {
      validator(evt);
   }
}

void AlianzaApiTestEvents::expectAlianzaApiHttpResponseTimeoutEvent(
   int line,
   CPCAPI2::test::EventHandler* apiEvents,
   EVENT_VALIDATOR(CPCAPI2::test::AlianzaApiHttpResponseTimeoutEvent) validator)
{
   AlianzaApiHttpResponseTimeoutEvent evt;
   AlianzaApiAccountHandle handle = 0;

   ASSERT_TRUE(apiEvents->expectEvent(line, "AlianzaApiHandler::onAlianzaApiHttpResponseTimeout", 20000, AlwaysTruePred(), handle, evt));

   if (nullptr != validator)
   {
      validator(evt);
   }
}

void AlianzaApiTestEvents::expectAlianzaApiHttpDelayRequestTimeoutEvent(
   int line,
   CPCAPI2::test::EventHandler* apiEvents,
   EVENT_VALIDATOR(CPCAPI2::test::AlianzaApiHttpDelayRequestTimeoutEvent) validator)
{
   AlianzaApiHttpDelayRequestTimeoutEvent evt;
   AlianzaApiAccountHandle handle = 0;

   ASSERT_TRUE(apiEvents->expectEvent(line, "AlianzaApiHandler::onAlianzaApiHttpDelayRequestTimeout", 20000, AlwaysTruePred(), handle, evt));

   if (nullptr != validator)
   {
      validator(evt);
   }
}

void AlianzaApiTestEvents::expectAlianzaApiAccountStatusEventAllowPrecedingEvents(
   int line,
   CPCAPI2::test::EventHandler* apiEvents,
   const AlianzaApiAccountFsmStateType state,
   EVENT_VALIDATOR(CPCAPI2::test::AlianzaApiAccountStatusEvent) validator,
   std::set<CPCAPI2::test::AlianzaApiAccountFsmStateType> allowedPrecedingEvents)
{
   AlianzaApiAccountStatusEvent evt;
   AlianzaApiAccountHandle handle = 0;

   int waitForSec = 20;
   if (TestEnvironmentConfig::testEnvironmentId() == "qa")
   {
      if (time(NULL) < **********) // hopefully fixed by March 24, 2023
      {
         // intermittent delays in QA environment: https://teamalianza.slack.com/archives/C0L0237HS/p1674245280149659
         waitForSec = 60 * 3;
      }
   }

   for (;;)
   {
      ASSERT_TRUE(apiEvents->expectEvent(line, "AlianzaApiHandler::onAlianzaApiAccountStatusEvent", waitForSec * 1000, AlwaysTruePred(), handle, evt));
      // cpcExpectEvent(apiEvents, "AlianzaApiHandler::onAlianzaApiAccountStatusEvent", 10000, CPCAPI2::test::AlwaysTruePred(), handle, evt);
      safeCout("expectAlianzaApiAccountStatusEvent(): previous-state: " << evt.previousState << " current-state: " << evt.currentState << " predicted-state: " << state);

      if (evt.currentState != state && allowedPrecedingEvents.find(evt.currentState) != allowedPrecedingEvents.end())
      {
         // found a preceding event we'll allow
         continue;
      }

      ASSERT_EQ(evt.currentState, state) << " predicted-state: " << state << " actual-state: " << evt.currentState << ". Check in log for Alianza API response failure";

      if (nullptr != validator)
      {
         validator(evt);
      }

      break;
   }
}

void AlianzaApiTestEvents::expectAlianzaApiAccountStatusEvent(
   int line,
   CPCAPI2::test::EventHandler* apiEvents,
   const AlianzaApiAccountFsmStateType state,
   EVENT_VALIDATOR(CPCAPI2::test::AlianzaApiAccountStatusEvent) validator)
{
   expectAlianzaApiAccountStatusEventAllowPrecedingEvents(line, apiEvents, state, validator, std::set<CPCAPI2::test::AlianzaApiAccountFsmStateType>());
}



void AlianzaApiTestEvents::expectAlianzaApiAccountStatusUptilDisabledEvent(
   int line,
   CPCAPI2::test::EventHandler* apiEvents,
   AlianzaSessionInfo& session,
   EVENT_VALIDATOR(CPCAPI2::test::AlianzaApiAccountStatusEvent) validator)
{
   AlianzaApiAccountStatusEvent evt;
   AlianzaApiAccountHandle handle = 0;

   ASSERT_TRUE(apiEvents->expectEvent(line, "AlianzaApiHandler::onAlianzaApiAccountStatusEvent", 20000, AlwaysTruePred(), handle, evt));
   safeCout("expectAlianzaApiAccountStatusEvent(): previous-state: " << evt.previousState << " current-state: " << evt.currentState);
   if (evt.currentState == AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_RemoveNumber)
   {
      expectAlianzaApiAccountStatusEvent(line, apiEvents, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_DeleteAccount, validator);
      expectAlianzaApiAccountStatusEvent(line, apiEvents, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_ReleaseNumber, validator);
   }
   else if (evt.currentState == AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_DeleteAccount)
   {
      if (session.numberEnabled)
      {
         expectAlianzaApiAccountStatusEvent(line, apiEvents, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_ReleaseNumber, validator);
      }
   }
   expectAlianzaApiAccountStatusEvent(line, apiEvents, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Idle, validator);

   if (nullptr != validator)
   {
      validator(evt);
   }
}
