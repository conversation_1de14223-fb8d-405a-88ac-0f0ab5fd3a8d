#pragma once

#ifndef SIPUA_ALIANZA_API_TEST_EVENTS_H
#define SIPUA_ALIANZA_API_TEST_EVENTS_H

#include "brand_branded.h"
#include <cpcapi2.h>
#include "cpcapi2_test_fixture.h"
#include "sipua_alianza_api_test_fixture.h"
#include "alianza_api/interface/public/alianza_api_handler.h"
#include "alianza_api/interface/public/alianza_api_types.h"
#include "alianza_api/interface/public/alianza_api_manager.h"


#define EVENT_VALIDATOR(TYPE) std::function<void(const TYPE& evt)>


class AlianzaApiTestEvents
{
public:

   AlianzaApiTestEvents();
   virtual~ AlianzaApiTestEvents();

   static void expectAuthorizationSuccess(
      int line,
      CPCAPI2::test::EventHandler* apiEvents,
      EVENT_VALIDATOR(CPCAPI2::test::AlianzaApiHttpResponseEvent) validator);

   #define assertAuthorizationSuccess(apiEvents) \
   { \
      SCOPED_TRACE(apiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectAuthorizationSuccess(__LINE__, apiEvents, nullptr)); \
   }

   #define assertAuthorizationSuccessEx(apiEvents, validator) \
   { \
      SCOPED_TRACE(apiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectAuthorizationSuccess(__LINE__, apiEvents, validator)); \
   }

   static void expectAuthorizationSuccessWithToken(
      int line,
      CPCAPI2::test::EventHandler* apiEvents,
      std::string& authToken,
      EVENT_VALIDATOR(CPCAPI2::test::AlianzaApiHttpResponseEvent) validator);

   #define assertAuthorizationSuccessWithToken(apiEvents, authToken) \
   { \
      SCOPED_TRACE(apiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectAuthorizationSuccessWithToken(__LINE__, apiEvents, authToken, nullptr)); \
   }

   #define assertAuthorizationSuccessWithTokenEx(apiEvents, authToken, validator) \
   { \
      SCOPED_TRACE(apiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectAuthorizationSuccessWithToken(__LINE__, apiEvents, authToken, validator)); \
   }

   static void expectNumberCreatedInPartition(
      int line,
      CPCAPI2::test::EventHandler* apiEvents,
      const std::string& phoneNumber,
      std::string& phoneId,
      EVENT_VALIDATOR(CPCAPI2::test::AlianzaApiHttpResponseEvent) validator);

   #define assertNumberCreatedInPartition(apiEvents, phoneNumber, phoneId) \
   { \
      SCOPED_TRACE(apiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectNumberCreatedInPartition(__LINE__, apiEvents, phoneNumber, phoneId, nullptr)); \
   }

   #define assertNumberCreatedInPartitionEx(apiEvents, phoneNumber, phoneId, validator) \
   { \
      SCOPED_TRACE(apiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectNumberCreatedInPartition(__LINE__, apiEvents, phoneNumber, phoneId, validator)); \
   }

   static void expectAccountCreatedInPartition(
      int line,
      CPCAPI2::test::EventHandler* apiEvents,
      std::string& accountId,
      EVENT_VALIDATOR(CPCAPI2::test::AlianzaApiHttpResponseEvent) validator);

   #define assertAccountCreatedInPartition(apiEvents, accountId) \
   { \
      SCOPED_TRACE(apiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectAccountCreatedInPartition(__LINE__, apiEvents, accountId, nullptr)); \
   }

   #define assertAccountCreatedInPartitionEx(apiEvents, accountId, validator) \
   { \
      SCOPED_TRACE(apiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectAccountCreatedInPartition(__LINE__, apiEvents, accountId, validator)); \
   }

   static void expectUserCreatedInAccount(
      int line,
      CPCAPI2::test::EventHandler* apiEvents,
      std::string& userId,
      std::string& voicemailId,
      AlianzaCallingPlanInfo& callingPlan,
      EVENT_VALIDATOR(CPCAPI2::test::AlianzaApiHttpResponseEvent) validator);

   #define assertUserCreatedInAccount(apiEvents, userId, voicemailId, callingPlan) \
   { \
      SCOPED_TRACE(apiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectUserCreatedInAccount(__LINE__, apiEvents, userId, voicemailId, callingPlan, nullptr)); \
   }

   #define assertUserCreatedInAccountEx(apiEvents, userId, voicemailId, callingPlan, validator) \
   { \
      SCOPED_TRACE(apiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectUserCreatedInAccount(__LINE__, apiEvents, userId, voicemailId, callingPlan, validator)); \
   }

   static void expectNumberCreatedInAccount(
      int line,
      CPCAPI2::test::EventHandler* apiEvents,
      const std::string& phoneId,
      EVENT_VALIDATOR(CPCAPI2::test::AlianzaApiHttpResponseEvent) validator);

   #define assertNumberCreatedInAccount(apiEvents, phoneId) \
   { \
      SCOPED_TRACE(apiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectNumberCreatedInAccount(__LINE__, apiEvents, phoneId, nullptr)); \
   }

   #define assertNumberCreatedInAccountEx(apiEvents, phoneId, validator) \
   { \
      SCOPED_TRACE(apiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectNumberCreatedInAccount(__LINE__, apiEvents, phoneId, validator)); \
   }

   static void expectNumberUpdatedInAccount(
      int line,
      CPCAPI2::test::EventHandler* apiEvents,
      const std::string& phoneId,
      EVENT_VALIDATOR(CPCAPI2::test::AlianzaApiHttpResponseEvent) validator);

   #define assertNumberUpdatedInAccount(apiEvents, phoneId) \
   { \
      SCOPED_TRACE(apiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectNumberUpdatedInAccount(__LINE__, apiEvents, phoneId, nullptr)); \
   }

   #define assertNumberUpdatedInAccountEx(apiEvents, phoneId, validator) \
   { \
      SCOPED_TRACE(apiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectNumberUpdatedInAccount(__LINE__, apiEvents, phoneId, validator)); \
   }

   static void expectUserUpdatedInAccount(
      int line,
      CPCAPI2::test::EventHandler* apiEvents,
      const std::string& phoneId,
      EVENT_VALIDATOR(CPCAPI2::test::AlianzaApiHttpResponseEvent) validator);

   #define assertUserUpdatedInAccount(apiEvents, phoneId) \
   { \
      SCOPED_TRACE(apiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectUserUpdatedInAccount(__LINE__, apiEvents, phoneId, nullptr)); \
   }

   #define assertUserUpdatedInAccountEx(apiEvents, phoneId, validator) \
   { \
      SCOPED_TRACE(apiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectUserUpdatedInAccount(__LINE__, apiEvents, phoneId, validator)); \
   }

   static void expectConfigurationFetchedInAccount(
      int line,
      CPCAPI2::test::EventHandler* apiEvents,
      EVENT_VALIDATOR(CPCAPI2::test::AlianzaApiHttpResponseEvent) validator);

   #define assertConfigurationFetchedInAccount(apiEvents) \
   { \
      SCOPED_TRACE(apiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectConfigurationFetchedInAccount(__LINE__, apiEvents, nullptr)); \
   }

   #define assertConfigurationFetchedInAccountEx(apiEvents, validator) \
   { \
      SCOPED_TRACE(apiEvents->getName()); \
      AlianzaApiTestEvents::expectConfigurationFetchedInAccount(__LINE__, apiEvents, validator); \
   }

   static void expectGroupNameUpdatedInAccount(
      int line,
      CPCAPI2::test::EventHandler* apiEvents,
      EVENT_VALIDATOR(CPCAPI2::test::AlianzaApiHttpResponseEvent) validator);

   #define assertGroupNameUpdatedInAccount(apiEvents) \
   { \
      SCOPED_TRACE(apiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectGroupNameUpdatedInAccount(__LINE__, apiEvents, nullptr)); \
   }

   #define assertGroupNameUpdatedInAccountEx(apiEvents, validator) \
   { \
      SCOPED_TRACE(apiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectAccountGroupNameUpdatedInAccount(__LINE__, apiEvents, validator)); \
   }

   static void expectNumberDeletedInPartition(
      int line,
      CPCAPI2::test::EventHandler* apiEvents,
      const std::string& phoneId,
      EVENT_VALIDATOR(CPCAPI2::test::AlianzaApiHttpResponseEvent) validator);

   #define assertNumberDeletedInPartition(apiEvents, phoneId) \
   { \
      SCOPED_TRACE(apiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectNumberDeletedInPartition(__LINE__, apiEvents, phoneId, nullptr)); \
   }

   #define assertNumberDeletedInPartitionEx(apiEvents, phoneId, validator) \
   { \
      SCOPED_TRACE(apiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectNumberDeletedInPartition(__LINE__, apiEvents, phoneId, validator)); \
   }

   static void expectNumberDoesNotExistInPartition(
      int line,
      CPCAPI2::test::EventHandler* apiEvents,
      const std::string& phoneId,
      EVENT_VALIDATOR(CPCAPI2::test::AlianzaApiHttpResponseEvent) validator);

   #define assertNumberDoesNotExistInPartition(apiEvents, phoneId) \
   { \
      SCOPED_TRACE(apiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectNumberDoesNotExistInPartition(__LINE__, apiEvents, phoneId, nullptr)); \
   }

   #define assertNumberDoesNotExistInPartitionEx(apiEvents, phoneId, validator) \
   { \
      SCOPED_TRACE(apiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectNumberDoesNotExistInPartition(__LINE__, apiEvents, phoneId, validator)); \
   }

   static void expectNumberRetrievedFromPartition(
      int line,
      CPCAPI2::test::EventHandler* apiEvents,
      const std::string& phoneId,
      EVENT_VALIDATOR(CPCAPI2::test::AlianzaApiHttpResponseEvent) validator);

   #define assertNumberRetrievedFromPartition(apiEvents, phoneId) \
   { \
      SCOPED_TRACE(apiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectNumberRetrievedFromPartition(__LINE__, apiEvents, phoneId, nullptr)); \
   }

   #define assertNumberRetrievedFromPartitionEx(apiEvents, phoneId, validator) \
   { \
      SCOPED_TRACE(apiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectNumberRetrievedFromPartition(__LINE__, apiEvents, phoneId, validator)); \
   }

   static void expectNumberDeletedInAccount(
      int line,
      CPCAPI2::test::EventHandler* apiEvents,
      const std::string& phoneId,
      EVENT_VALIDATOR(CPCAPI2::test::AlianzaApiHttpResponseEvent) validator);

   #define assertNumberDeletedInAccount(apiEvents, phoneId) \
   { \
      SCOPED_TRACE(apiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectNumberDeletedInAccount(__LINE__, apiEvents, phoneId, nullptr)); \
   }

   #define assertNumberDeletedInAccountEx(apiEvents, phoneId, validator) \
   { \
      SCOPED_TRACE(apiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectNumberDeletedInAccount(__LINE__, apiEvents, phoneId, validator)); \
   }

   static void expectAccountDeletedInPartition(
      int line,
      CPCAPI2::test::EventHandler* apiEvents,
      const std::string& accountId,
      EVENT_VALIDATOR(CPCAPI2::test::AlianzaApiHttpResponseEvent) validator);

   #define assertAccountDeletedInPartition(apiEvents, accountId) \
   { \
      SCOPED_TRACE(apiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectAccountDeletedInPartition(__LINE__, apiEvents, accountId, nullptr)); \
   }

   #define assertAccountDeletedInPartitionEx(apiEvents, accountId, validator) \
   { \
      SCOPED_TRACE(apiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectAccountDeletedInPartition(__LINE__, apiEvents, accountId, validator)); \
   }

   static void expectAccountsRetrievedFromPartition(
      int line,
      CPCAPI2::test::EventHandler* apiEvents,
      EVENT_VALIDATOR(CPCAPI2::test::AlianzaApiHttpResponseEvent) validator);

   #define assertAccountsRetrievedFromPartition(apiEvents) \
   { \
      SCOPED_TRACE(apiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectAccountsRetrievedFromPartition(__LINE__, apiEvents, nullptr)); \
   }

   #define assertAccountsRetrievedFromPartitionEx(apiEvents, validator) \
   { \
      SCOPED_TRACE(apiEvents->getName()); \
      AlianzaApiTestEvents::expectAccountsRetrievedFromPartition(__LINE__, apiEvents, validator); \
   }

   static void expectAccountRetrievedFromPartition(
      int line,
      CPCAPI2::test::EventHandler* apiEvents,
      EVENT_VALIDATOR(CPCAPI2::test::AlianzaApiHttpResponseEvent) validator);

   #define assertAccountRetrievedFromPartition(apiEvents) \
   { \
      SCOPED_TRACE(apiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectAccountRetrievedFromPartition(__LINE__, apiEvents, nullptr)); \
   }

   #define assertAccountRetrievedFromPartitionEx(apiEvents, validator) \
   { \
      SCOPED_TRACE(apiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectAccountRetrievedFromPartition(__LINE__, apiEvents, validator)); \
   }

   static void expectUserRetrievedFromAccount(
      int line,
      CPCAPI2::test::EventHandler* apiEvents,
      EVENT_VALIDATOR(CPCAPI2::test::AlianzaApiHttpResponseEvent) validator);

   #define assertUserRetrievedFromAccount(apiEvents) \
   { \
      SCOPED_TRACE(apiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectUserRetrievedFromAccount(__LINE__, apiEvents, nullptr)); \
   }

   #define assertUserRetrievedFromAccountEx(apiEvents, validator) \
   { \
      SCOPED_TRACE(apiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectUserRetrievedFromAccount(__LINE__, apiEvents, validator)); \
   }

   static void expectClientRegistrationStatusRetrieved(
      int line,
      CPCAPI2::test::EventHandler* apiEvents,
      bool& sipRegistered,
      bool& pushRegistered,
      EVENT_VALIDATOR(CPCAPI2::test::AlianzaApiHttpResponseEvent) validator);

   #define assertClientRegistrationStatusRetrieved(apiEvents, sipRegistered, pushRegistered) \
   { \
      SCOPED_TRACE(apiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectClientRegistrationStatusRetrieved(__LINE__, apiEvents, sipRegistered, pushRegistered, nullptr)); \
   }

   static void expectNumberInAccountStatusRetrieved(
      int line,
      CPCAPI2::test::EventHandler* apiEvents,
      std::string& status,
      EVENT_VALIDATOR(CPCAPI2::test::AlianzaApiHttpResponseEvent) validator);

   #define assertNumberInAccountStatusRetrieved(apiEvents, status) \
   { \
      SCOPED_TRACE(apiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectNumberInAccountStatusRetrieved(__LINE__, apiEvents, status, nullptr)); \
   }

   static void expectNumberRetrievedFromAccount(
      int line,
      CPCAPI2::test::EventHandler* apiEvents,
      const std::string& phoneId,
      EVENT_VALIDATOR(CPCAPI2::test::AlianzaApiHttpResponseEvent) validator);

   #define assertNumberRetrievedFromAccount(apiEvents, phoneId) \
   { \
      SCOPED_TRACE(apiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectNumberRetrievedFromAccount(__LINE__, apiEvents, phoneId, nullptr)); \
   }

   #define assertNumberRetrievedFromAccountEx(apiEvents, validator) \
   { \
      SCOPED_TRACE(apiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectNumberRetrievedFromAccount(__LINE__, apiEvents, phoneId, validator)); \
   }

   static void expectAlianzaApiIdentityResetEvent(
      int line,
      CPCAPI2::test::EventHandler* apiEvents,
      std::string number,
      std::string username,
      EVENT_VALIDATOR(CPCAPI2::test::AlianzaApiIdentityResetEvent) validator);

   #define assertAlianzaApiIdentityResetEvent(account) \
   { \
      SCOPED_TRACE((account).alianzaApiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectAlianzaApiIdentityResetEvent(__LINE__, (account).alianzaApiEvents, "", "", nullptr)); \
   }

   #define assertAlianzaApiIdentityResetEventEx(account, number, username, validator) \
   { \
      SCOPED_TRACE((account).alianzaApiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectAlianzaApiIdentityResetEvent(__LINE__, (account).alianzaApiEvents, number, username, validator)); \
   }

   #define assertAlianzaApiIdentityResetEventVerifyUsername(account, username) \
   { \
      SCOPED_TRACE((account).alianzaApiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectAlianzaApiIdentityResetEvent(__LINE__, (account).alianzaApiEvents, "", username, nullptr)); \
   }

   #define assertAlianzaApiIdentityResetEventVerifyNumber(account, number) \
   { \
      SCOPED_TRACE((account).alianzaApiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectAlianzaApiIdentityResetEvent(__LINE__, (account).alianzaApiEvents, number, "", nullptr)); \
   }

   static void expectAlianzaApiHttpResponseTimeoutEvent(
      int line,
      CPCAPI2::test::EventHandler* apiEvents,
      EVENT_VALIDATOR(CPCAPI2::test::AlianzaApiHttpResponseTimeoutEvent) validator);

   #define assertAlianzaApiHttpResponseTimeoutEvent(account) \
   { \
      SCOPED_TRACE((account).alianzaApiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectAlianzaApiHttpResponseTimeoutEvent(__LINE__, (account).alianzaApiEvents, nullptr)); \
   }

   #define assertAlianzaApiHttpResponseTimeoutEventEx(account, validator) \
   { \
      scoped_trace((account).alianzaApiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectAlianzaApiHttpResponseTimeoutEvent(__LINE__, (account).alianzaApiEvents, validator)); \
   }

   static void expectAlianzaApiHttpDelayRequestTimeoutEvent(
      int line,
      CPCAPI2::test::EventHandler* apiEvents,
      EVENT_VALIDATOR(CPCAPI2::test::AlianzaApiHttpDelayRequestTimeoutEvent) validator);

   #define assertAlianzaApiHttpDelayRequestTimeoutEvent(account) \
   { \
      SCOPED_TRACE((account).alianzaApiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectAlianzaApiHttpDelayRequestTimeoutEvent(__LINE__, (account).alianzaApiEvents, nullptr)); \
   }

   #define assertAlianzaApiHttpDelayRequestTimeoutEventEx(account, validator) \
   { \
      scoped_trace((account).alianzaApiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectAlianzaApiHttpDelayRequestTimeoutEvent(__LINE__, (account).alianzaApiEvents, validator)); \
   }

   static void expectAlianzaApiAccountStatusEvent(
      int line,
      CPCAPI2::test::EventHandler* apiEvents,
      const CPCAPI2::test::AlianzaApiAccountFsmStateType state,
      EVENT_VALIDATOR(CPCAPI2::test::AlianzaApiAccountStatusEvent) validator);

   static void expectAlianzaApiAccountStatusEventAllowPrecedingEvents(
      int line,
      CPCAPI2::test::EventHandler* apiEvents,
      const CPCAPI2::test::AlianzaApiAccountFsmStateType state,
      EVENT_VALIDATOR(CPCAPI2::test::AlianzaApiAccountStatusEvent) validator,
      std::set<CPCAPI2::test::AlianzaApiAccountFsmStateType> allowedPrecedingEvents /* allow any of these events to precede 'state' */);

   #define assertAlianzaApiAccountStatusEvent(account, state) \
   { \
      SCOPED_TRACE((account).alianzaApiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectAlianzaApiAccountStatusEvent(__LINE__, (account).alianzaApiEvents, state, nullptr)); \
   }

   #define assertAlianzaApiAccountStatusEventAllowPrecedingEvents(account, state, precedingEvents) \
   { \
      SCOPED_TRACE((account).alianzaApiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectAlianzaApiAccountStatusEventAllowPrecedingEvents(__LINE__, (account).alianzaApiEvents, state, nullptr, precedingEvents)); \
   }

   #define assertAlianzaApiAccountStatusEventEx(account, state, validator) \
   { \
      SCOPED_TRACE((account).alianzaApiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectAlianzaApiAccountStatusEvent(__LINE__, (account).alianzaApiEvents, state, validator)); \
   }

   static void expectAlianzaApiAccountStatusUptilDisabledEvent(
      int line,
      CPCAPI2::test::EventHandler* apiEvents,
      AlianzaSessionInfo& session,
      EVENT_VALIDATOR(CPCAPI2::test::AlianzaApiAccountStatusEvent) validator);

   #define assertAlianzaApiAccountStatusUptilDisabledEvent(account) \
   { \
      SCOPED_TRACE((account).alianzaApiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectAlianzaApiAccountStatusUptilDisabledEvent(__LINE__, (account).alianzaApiEvents, (account).config.alianzaSession, nullptr)); \
   }

   #define assertAlianzaApiAccountStatusUptilDisabledEventEx(account, state, validator) \
   { \
      SCOPED_TRACE((account).alianzaApiEvents->getName()); \
      ASSERT_NO_FATAL_FAILURE(AlianzaApiTestEvents::expectAlianzaApiAccountStatusUptilDisabledEvent(__LINE__, (account).alianzaApiEvents, (account).config.alianzaSession, validator)); \
   }
};

#endif // SIPUA_ALIANZA_API_TEST_EVENTS_H

