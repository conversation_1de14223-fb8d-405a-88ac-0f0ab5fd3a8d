#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#if (CPCAPI2_BRAND_LICENSING_MODULE == 1)

#include "cpcapi2_test_fixture.h"
#include "test_account_events.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>

#include "impl/licensing/LicensingClientManagerInterface.h"
#include "test_framework/cpcapi2_test_framework.h"
#include "test_framework/bare_bones_sip_endpoint.h"

#include <thread>
#include <future>

#include "test_framework/http_test_framework.h"
#include "../../impl/util/HttpClient.h"
#include "../../impl/util/RegistryHelpers.h"

using namespace CPCAPI2;
using namespace CPCAPI2::Licensing;
using namespace CPCAPI2::test;

#if TARGET_OS_IPHONE
extern const char* GetTempDir(void);
#endif

namespace {

class LicensingModuleTest : public CpcapiAutoTest
{
public:
   LicensingModuleTest() : mListenPort(9090), mServer() {
      mServer.config.port = mListenPort;
   }
   virtual ~LicensingModuleTest() {}
   cpc::string serverBaseUrl() const;
   const int mListenPort;
   SimpleWeb::Server<SimpleWeb::HTTP> mServer;
};

class MyHandler : public LicensingClientHandler
{
public:
   MyHandler()
   {
      handle = 0;
      onValidateLicensesSuccessInvoked = false;
      onValidateLicensesFailureInvoked = false;
      onErrorInvoked = false;
   }

   int onValidateLicensesSuccess(LicensingClientHandle client, const ValidateLicensesSuccessEvent& args)
   {
      handle = client;
      onValidateLicensesSuccessInvoked = true;
      validateLicensesSuccessEvent = args;
      return kSuccess;
   }

   int onValidateLicensesFailure(LicensingClientHandle client, const ValidateLicensesFailureEvent& args)
   {
      handle = client;
      onValidateLicensesFailureInvoked = true;
      validateLicensesFailureEvent = args;
      return kSuccess;
   }

   virtual int onError(LicensingClientHandle client, const CPCAPI2::Licensing::ErrorEvent& args)
   {
      handle = client;
      onErrorInvoked = true;
      return kSuccess;
   }

   int handle;
   bool onValidateLicensesSuccessInvoked;
   bool onValidateLicensesFailureInvoked;
   bool onErrorInvoked;
   ValidateLicensesSuccessEvent validateLicensesSuccessEvent;
   ValidateLicensesFailureEvent validateLicensesFailureEvent;
};

class MySdkObserver : public LicensingClientHandler
{
public:
   MySdkObserver()
   {
      onValidateLicensesSuccessInvoked = false;
      onValidateLicensesFailureInvoked = false;
      onErrorInvoked = false;
   }

   virtual int onValidateLicensesSuccess(LicensingClientHandle client, const ValidateLicensesSuccessEvent& args)
   {
      onValidateLicensesSuccessInvoked = true;
      return kSuccess;
   }

   virtual int onValidateLicensesFailure(LicensingClientHandle client, const ValidateLicensesFailureEvent& args)
   {
      onValidateLicensesFailureInvoked = true;
      return kSuccess;
   }

   virtual int onError(LicensingClientHandle client, const CPCAPI2::Licensing::ErrorEvent& args)
   {
      onErrorInvoked = true;
      return kSuccess;
   }

   bool onValidateLicensesSuccessInvoked;
   bool onValidateLicensesFailureInvoked;
   bool onErrorInvoked;
};

class MyPhoneErrorHandler : public PhoneHandler
{
public:
   MyPhoneErrorHandler()
   {
      onLicenseErrorInvoked = false;
      onErrorInvoked = false;
      onLicenseSuccessInvoked = false;
   }

   virtual int onError(const cpc::string& sourceModule, const PhoneErrorEvent& args)
   {
      onErrorInvoked = true;
      return kSuccess;
   }

   virtual int onLicensingError(const CPCAPI2::LicensingErrorEvent& args)
   {
      onLicenseErrorInvoked = true;
      errorReason = args.errorReason;
      return kSuccess;
   }
   
   virtual int onLicensingSuccess()
   {
      onLicenseSuccessInvoked = true;
      return kSuccess;
   }

   bool onLicenseErrorInvoked;
   bool onErrorInvoked;
   bool onLicenseSuccessInvoked;
   CPCAPI2::LicensingErrorEvent::LicensingErrorReason errorReason;
};

std::string resourcePathRegex(const std::string& resourcePath)
{
   std::stringstream ss;
   ss << resourcePath << "$";
   return ss.str();
}
cpc::string LicensingModuleTest::serverBaseUrl() const
{
   std::ostringstream ss;
   ss << "http://127.0.0.1:" << mListenPort;

   return ss.str().c_str();
}

#if (CPCAPI2_BRAND_SDK_LICENSING == 0)
TEST_F(LicensingModuleTest, LicensingAPISdkObserver) {
   TestAccount alice("alice");

   // Create the client
   LicensingClientManagerInterface* licensingClient = (LicensingClientManagerInterface*) LicensingClientManager::getInterface(alice.phone);
   LicensingClientHandle client = licensingClient->create();
   ASSERT_TRUE(client > 0);

   // Set the handler
   std::unique_ptr<MyHandler> licensingClientHandler(new MyHandler());
   licensingClient->setHandler(client, licensingClientHandler.get());

   // Add the observer
   MySdkObserver sdkObserver;
   licensingClient->addSdkObserver(&sdkObserver);

   // Configure the client
   LicensingClientSettings settings;
   ASSERT_EQ(licensingClient->applySettings(client, settings), kSuccess);

   // Validate licenses
   ASSERT_EQ(licensingClient->validateLicenses(client), kSuccess);

   // Make sure the SDK observer got invoked
   licensingClient->process(LicensingClientManager::kBlockingModeInfinite);
   ASSERT_EQ(sdkObserver.onValidateLicensesSuccessInvoked, true);
   ASSERT_EQ(sdkObserver.onValidateLicensesFailureInvoked, false);
   ASSERT_EQ(sdkObserver.onErrorInvoked, false);

   // Expect no valid license key and no invalid one
   licensingClient->process(1000);
   ASSERT_EQ(client, licensingClientHandler->handle);
   ASSERT_TRUE(licensingClientHandler->onValidateLicensesSuccessInvoked);
   ASSERT_EQ(0, licensingClientHandler->validateLicensesSuccessEvent.licenses.size());
   ASSERT_EQ(0, licensingClientHandler->validateLicensesSuccessEvent.invalidLicenses.size());
   ASSERT_FALSE(licensingClientHandler->validateLicensesSuccessEvent.timeLimitLicense);
   ASSERT_EQ(0, licensingClientHandler->validateLicensesSuccessEvent.timeLimitRemaining);

   // Destroy the client
   ASSERT_EQ(licensingClient->destroy(client), kSuccess);
}

TEST_F(LicensingModuleTest, LicensingAPIGetHardwareId)
{
   TestAccount alice("alice");

   // Create the client
   LicensingClientManagerInterface* licensingClient = (LicensingClientManagerInterface*) LicensingClientManager::getInterface(alice.phone);
   LicensingClientHandle client = licensingClient->create();
   ASSERT_TRUE(client > 0);

   // Get the hardware ID
   cpc::string hardwareId;
   licensingClient->getHardwareId(client, hardwareId);

   ASSERT_TRUE(hardwareId.size() > 0);

   // Destroy the client
   ASSERT_EQ(licensingClient->destroy(client), kSuccess);
}

TEST_F(LicensingModuleTest, LicensingAPIValidTimeLimitLicense) {
   TestAccount alice("alice");

   // Create a client
   LicensingClientManagerInterface* licensingClient = (LicensingClientManagerInterface*) LicensingClientManager::getInterface(alice.phone);
   LicensingClientHandle client = licensingClient->create();
   ASSERT_TRUE(client > 0);

   // Set the handler
   std::unique_ptr<MyHandler> licensingClientHandler(new MyHandler());
   licensingClient->setHandler(client, licensingClientHandler.get());

   // Configure the client
   LicensingClientSettings settings;
   settings.expiryYear = 2029;
   settings.expiryMonth = 12;
   settings.expiryDay = 31;
   ASSERT_EQ(licensingClient->applySettings(client, settings), kSuccess);

   // Validate licenses
   ASSERT_EQ(licensingClient->validateLicenses(client), kSuccess);

   // Expect valid time limit
   licensingClient->process(LicensingClientManager::kBlockingModeInfinite);
   ASSERT_EQ(licensingClientHandler->handle, client);
   ASSERT_TRUE(licensingClientHandler->onValidateLicensesSuccessInvoked);
   ASSERT_TRUE(licensingClientHandler->validateLicensesSuccessEvent.timeLimitLicense);
   ASSERT_TRUE(licensingClientHandler->validateLicensesSuccessEvent.timeLimitRemaining > 0);
   ASSERT_EQ(0, licensingClientHandler->validateLicensesSuccessEvent.licenses.size());
   ASSERT_EQ(0, licensingClientHandler->validateLicensesSuccessEvent.invalidLicenses.size());

   // Destroy the client
   ASSERT_EQ(licensingClient->destroy(client), kSuccess);
}

TEST_F(LicensingModuleTest, LicensingAPIExpiredTimeLimitLicense) {
   TestAccount alice("alice");

   // Create the client
   LicensingClientManagerInterface* licensingClient = (LicensingClientManagerInterface*) LicensingClientManager::getInterface(alice.phone);
   LicensingClientHandle client = licensingClient->create();
   ASSERT_TRUE(client > 0);

   // Set the handler
   std::unique_ptr<MyHandler> licensingClientHandler(new MyHandler());
   licensingClient->setHandler(client, licensingClientHandler.get());

   // Configure the client
   LicensingClientSettings settings;
   settings.expiryYear = 1999;
   settings.expiryMonth = 12;
   settings.expiryDay = 31;
   ASSERT_EQ(licensingClient->applySettings(client, settings), kSuccess);

   // Validate licenses
   ASSERT_EQ(licensingClient->validateLicenses(client), kSuccess);

   // Expect expired time limit
   licensingClient->process(LicensingClientManager::kBlockingModeInfinite);
   ASSERT_EQ(licensingClientHandler->handle, client);
   ASSERT_TRUE(licensingClientHandler->onValidateLicensesFailureInvoked);
   ASSERT_EQ(licensingClientHandler->validateLicensesFailureEvent.status, LicenseStatus_Expired);

   // Destroy the client
   ASSERT_EQ(licensingClient->destroy(client), kSuccess);
}
#endif // #if (CPCAPI2_BRAND_SDK_LICENSING == 0)

#if defined(_WIN32) || defined(__linux__) || defined(__APPLE__)

#define LICENSE_AOR "<EMAIL>"
#define INVALID_LICENSE_URL "https://invalid.domain.cp"
#ifdef _WIN32
#define LICENSE_KEY_1 "180WTV8PN1AV6RE99MP3N0228"
#define LICENSE_KEY_2 "180T0QK9NCW807N19MP3N0220"
#define LICENSE_KEY_3 "1802TY304QYGVYNK9MP3N0211"
#define INVALID_LICENSE_KEY "180MXZ3SKS2NRMZJ9MP3N0228"
#define LICENSE_DOCUMENT_LOCATION "C:\\Windows\\Temp"
#endif

#ifdef __linux__
#define LICENSE_KEY_1 "180464N3P5BJYP2K9MP3N0230"
#define LICENSE_KEY_2 "180J6WY31SY21PY39MP3N0205"
#define LICENSE_KEY_3 "1802MDVHK7DNGJ7P9MP3N0232"
#define INVALID_LICENSE_KEY "180MXZ3SKS2NRMZJ9MP3N0228"
#define LICENSE_DOCUMENT_LOCATION "/tmp"
#endif

#if TARGET_OS_IPHONE
#define LICENSE_KEY_1 "1808998YM4XER2809MP3N0234"
#define LICENSE_KEY_2 "1802CMXWBMTE23B99MP3N0211"
#define LICENSE_KEY_3 "1800NKGSNR3B8JQ69MP3N0210"
#define INVALID_LICENSE_KEY "180MXZ3SKS2NRMZJ9MP3N0228"
#define LICENSE_DOCUMENT_LOCATION GetTempDir()
#elif TARGET_OS_MAC
#define LICENSE_KEY_1 "180MEKEZSR1T0HB29MP3N0233"
#define LICENSE_KEY_2 "1802QQ7Y36NMJ09B9MP3N0236"
#define LICENSE_KEY_3 "1804KYBYJE0W9JT39MP3N0210"
#define INVALID_LICENSE_KEY "180MXZ3SKS2NRMZJ9MP3N0228"
#define LICENSE_DOCUMENT_LOCATION "/tmp"
#endif
 
#if (CPCAPI2_BRAND_SDK_LICENSING == 1)
TEST_F(LicensingModuleTest, SdkLicensingValidLicenseKey) {
   TestAccount alice("alice", Account_NoInit);
   alice.config.license = LICENSE_KEY_1;
   alice.config.licenseDocumentLocation = LICENSE_DOCUMENT_LOCATION;
   alice.config.licenseAor = LICENSE_AOR;

   std::unique_ptr<MyPhoneErrorHandler> errorHandler(new MyPhoneErrorHandler());
   alice.init(errorHandler.get());

   alice.phone->process(10000);
   ASSERT_TRUE(errorHandler->onLicenseSuccessInvoked);
   ASSERT_FALSE(errorHandler->onErrorInvoked);
   ASSERT_FALSE(errorHandler->onLicenseErrorInvoked);
}

TEST_F(LicensingModuleTest, SdkLicensingInvalidLicenseKey) {
   TestAccount alice("alice", Account_NoInit, false);
   alice.config.license = INVALID_LICENSE_KEY; // Invalid key
   alice.config.licenseDocumentLocation = LICENSE_DOCUMENT_LOCATION;
   alice.config.licenseAor = LICENSE_AOR;
   std::unique_ptr<MyPhoneErrorHandler> errorHandler(new MyPhoneErrorHandler());
   alice.init(errorHandler.get());

#if (CPCAPI2_BRAND_USE_LICENSE_SERVER == 1)
   alice.phone->process(10000);
   // expect license error
   ASSERT_TRUE(errorHandler->onLicenseErrorInvoked);
   ASSERT_EQ(errorHandler->errorReason, LicensingErrorEvent::LicensingErrorReason_Invalid);
#else
   alice.phone->process(10000);
   ASSERT_TRUE(errorHandler->onLicenseSuccessInvoked);
   ASSERT_FALSE(errorHandler->onErrorInvoked);
   ASSERT_FALSE(errorHandler->onLicenseErrorInvoked);
#endif
}

#ifdef _WIN32
// bliu: CPCAPI2_BRAND_USE_LICENSE_SERVER is disable in auto test cases, which shall simulate Swisscom's case
// this test case verifies if registry is updated properly with CPCAPI2_BRAND_USE_LICENSE_SERVER=0 after licensing succeeds
TEST_F(LicensingModuleTest, SCORE_247_local)
{
   RegistryHelpers::WriteRegistryStringValue("hid", "");
   RegistryHelpers::WriteRegistryStringValue("h0", "");
   RegistryHelpers::WriteRegistryStringValue("m0", "");
   auto v = RegistryHelpers::ReadRegistryStringValue("hid");
   ASSERT_TRUE(v.empty());
   v = RegistryHelpers::ReadRegistryStringValue("h0");
   ASSERT_TRUE(v.empty());
   v = RegistryHelpers::ReadRegistryStringValue("m0");
   ASSERT_TRUE(v.empty());

   auto phone = static_cast<PhoneInterface*>(Phone::create());
   test::EventHandler* events = new test::EventHandler(::testing::UnitTest::GetInstance()->current_test_info()->name(), phone);
   phone->initialize({ LICENSE_KEY_1, LICENSE_DOCUMENT_LOCATION, LICENSE_AOR }, (PhoneHandler*)0xDEADBEEF);

   ASSERT_TRUE(events->expectEvent("PhoneHandler::onLicensingSuccess", 5000, test::AlwaysTruePred()));

   v = RegistryHelpers::ReadRegistryStringValue("hid");
   ASSERT_FALSE(v.empty());
   v = RegistryHelpers::ReadRegistryStringValue("h0");
   ASSERT_FALSE(v.empty());
   v = RegistryHelpers::ReadRegistryStringValue("m0");
   ASSERT_FALSE(v.empty());

   delete events;
   Phone::release(phone);
}
#endif

#else // #if (CPCAPI2_BRAND_SDK_LICENSING == 1)

TEST_F(LicensingModuleTest, LicensingAPINoLicenseDocumentLocation) {
   TestAccount alice("alice");

   // Create the client
   LicensingClientManagerInterface* licensingClient = (LicensingClientManagerInterface*) LicensingClientManager::getInterface(alice.phone);
   LicensingClientHandle client = licensingClient->create();
   ASSERT_TRUE(client > 0);

   // Set the handler
   std::unique_ptr<MyHandler> licensingClientHandler(new MyHandler());
   licensingClient->setHandler(client, licensingClientHandler.get());

   // Configure the client
   LicensingClientSettings settings;
   settings.licenseKeys.push_back(LICENSE_KEY_1);
   ASSERT_EQ(licensingClient->applySettings(client, settings), kSuccess);

   // Validate licenses
   ASSERT_EQ(licensingClient->validateLicenses(client), kSuccess);

   // Expect an error
   licensingClient->process(LicensingClientManager::kBlockingModeInfinite);
   ASSERT_EQ(client, licensingClientHandler->handle);
   ASSERT_TRUE(licensingClientHandler->onErrorInvoked);

   // Destroy the client
   ASSERT_EQ(licensingClient->destroy(client), kSuccess);
}

TEST_F(LicensingModuleTest, LicensingAPINoLicense) {
   TestAccount alice("alice");

   // Create the client
   LicensingClientManagerInterface* licensingClient = (LicensingClientManagerInterface*) LicensingClientManager::getInterface(alice.phone);
   LicensingClientHandle client = licensingClient->create();
   ASSERT_TRUE(client > 0);

   // Set the handler
   std::unique_ptr<MyHandler> licensingClientHandler(new MyHandler());
   licensingClient->setHandler(client, licensingClientHandler.get());

   // Configure the client
   LicensingClientSettings settings;
   ASSERT_EQ(licensingClient->applySettings(client, settings), kSuccess);

   // Validate licenses
   ASSERT_EQ(licensingClient->validateLicenses(client), kSuccess);

   // Expect no valid license key and no invalid one
   licensingClient->process(LicensingClientManager::kBlockingModeInfinite);
   ASSERT_EQ(client, licensingClientHandler->handle);
   ASSERT_TRUE(licensingClientHandler->onValidateLicensesSuccessInvoked);
   ASSERT_EQ(0, licensingClientHandler->validateLicensesSuccessEvent.licenses.size());
   ASSERT_EQ(0, licensingClientHandler->validateLicensesSuccessEvent.invalidLicenses.size());
   ASSERT_FALSE(licensingClientHandler->validateLicensesSuccessEvent.timeLimitLicense);
   ASSERT_EQ(0, licensingClientHandler->validateLicensesSuccessEvent.timeLimitRemaining);

   // Destroy the client
   ASSERT_EQ(licensingClient->destroy(client), kSuccess);
}

TEST_F(LicensingModuleTest, LicensingAPIBadFormatKey) {
   TestAccount alice("alice");

   // Create the client
   LicensingClientManagerInterface* licensingClient = (LicensingClientManagerInterface*) LicensingClientManager::getInterface(alice.phone);
   LicensingClientHandle client = licensingClient->create();
   ASSERT_TRUE(client > 0);

   // Set the handler
   std::unique_ptr<MyHandler> licensingClientHandler(new MyHandler());
   licensingClient->setHandler(client, licensingClientHandler.get());

   // Configure the client
   LicensingClientSettings settings;
   settings.licenseKeys.push_back("badkey");
   settings.licenseDocumentLocation = LICENSE_DOCUMENT_LOCATION;
   settings.provisioningId = LICENSE_AOR;
   settings.keySourceUrl = "http://license.counterpath.com/generate";
   ASSERT_EQ(licensingClient->applySettings(client, settings), kSuccess);

   // Validate licenses
   ASSERT_EQ(licensingClient->validateLicenses(client), kSuccess);

   // Expect failure callback
   licensingClient->process(LicensingClientManager::kBlockingModeInfinite);
   ASSERT_EQ(client, licensingClientHandler->handle);
   ASSERT_FALSE(licensingClientHandler->onValidateLicensesSuccessInvoked);
   ASSERT_TRUE(licensingClientHandler->onValidateLicensesFailureInvoked);
   ASSERT_FALSE(licensingClientHandler->onErrorInvoked);
   
   // Destroy the client
   ASSERT_EQ(licensingClient->destroy(client), kSuccess);
}

TEST_F(LicensingModuleTest, LicensingAPIValidLicenseKey) {
   TestAccount alice("alice");

   // Create the client
   LicensingClientManagerInterface* licensingClient = (LicensingClientManagerInterface*) LicensingClientManager::getInterface(alice.phone);
   LicensingClientHandle client = licensingClient->create();
   ASSERT_TRUE(client > 0);

   // Set the handler
   std::unique_ptr<MyHandler> licensingClientHandler(new MyHandler());
   licensingClient->setHandler(client, licensingClientHandler.get());

   // Configure the client
   LicensingClientSettings settings;
   settings.licenseKeys.push_back(LICENSE_KEY_1);
   settings.licenseDocumentLocation = LICENSE_DOCUMENT_LOCATION;
   settings.provisioningId = LICENSE_AOR;
   settings.osVersion = "Windows for Workgroups 3.1";
   settings.keySourceUrl = "http://license.counterpath.com/generate";
   ASSERT_EQ(licensingClient->applySettings(client, settings), kSuccess);

   // Validate licenses
   ASSERT_EQ(licensingClient->validateLicenses(client), kSuccess);

   // Expect one valid license and no invalid one
   licensingClient->process(LicensingClientManager::kBlockingModeInfinite);
   ASSERT_EQ(client, licensingClientHandler->handle);
   ASSERT_TRUE(licensingClientHandler->onValidateLicensesSuccessInvoked);
   ASSERT_FALSE(licensingClientHandler->validateLicensesSuccessEvent.hardwareId.empty());
   ASSERT_EQ(1, licensingClientHandler->validateLicensesSuccessEvent.licenses.size());
   ASSERT_EQ(0, licensingClientHandler->validateLicensesSuccessEvent.invalidLicenses.size());
   ASSERT_FALSE(licensingClientHandler->validateLicensesSuccessEvent.timeLimitLicense);
   ASSERT_EQ(0, licensingClientHandler->validateLicensesSuccessEvent.timeLimitRemaining);

   // Destroy the client
   ASSERT_EQ(licensingClient->destroy(client), kSuccess);
}

TEST_F(LicensingModuleTest, LicensingQuickShutdown) {
   
   Phone* phone = Phone::create();
   phone->initialize(CPCAPI2::LicenseInfo(), (PhoneHandler*)NULL);

   // Create the client
   LicensingClientManagerInterface* licensingClient = (LicensingClientManagerInterface*) LicensingClientManager::getInterface(phone);
   LicensingClientHandle client = licensingClient->create();
   ASSERT_TRUE(client > 0);


   // Configure the client
   LicensingClientSettings settings;
   settings.licenseKeys.push_back(LICENSE_KEY_1);
   settings.licenseDocumentLocation = "/bogus";
   settings.forceRemoteCheck = true;
   settings.provisioningId = LICENSE_AOR;
   settings.osVersion = "Windows for Workgroups 3.1";
   settings.keySourceUrl = "http://license.counterpath.com/generate";
   ASSERT_EQ(licensingClient->applySettings(client, settings), kSuccess);

   // Validate licenses
   ASSERT_EQ(licensingClient->validateLicenses(client), kSuccess);
   
   //std::this_thread::sleep_for(std::chrono::milliseconds(50));

   // Destroy the client
   ASSERT_EQ(licensingClient->destroy(client), kSuccess);
   
   Phone::release(phone);
}

TEST_F(LicensingModuleTest, DISABLED_LicensingAPIInvalidLicenseKey) {
   TestAccount alice("alice");

   // Create the client
   LicensingClientManagerInterface* licensingClient = (LicensingClientManagerInterface*) LicensingClientManager::getInterface(alice.phone);
   LicensingClientHandle client = licensingClient->create();
   ASSERT_TRUE(client > 0);

   // Configure the client
   LicensingClientSettings settings;
   settings.licenseKeys.push_back(INVALID_LICENSE_KEY); // Invalid key
   settings.licenseDocumentLocation = LICENSE_DOCUMENT_LOCATION;
   ASSERT_EQ(licensingClient->applySettings(client, settings), kSuccess);

   // Set the handler
   std::unique_ptr<MyHandler> licensingClientHandler(new MyHandler());
   licensingClient->setHandler(client, licensingClientHandler.get());

   // Validate licenses
   ASSERT_EQ(licensingClient->validateLicenses(client), kSuccess);

   // Expect no valid license and one invalid one

   licensingClient->process(LicensingClientManager::kBlockingModeInfinite);
   ASSERT_EQ(client, licensingClientHandler->handle);
   ASSERT_TRUE(licensingClientHandler->onValidateLicensesSuccessInvoked);
   ASSERT_FALSE(licensingClientHandler->validateLicensesSuccessEvent.hardwareId.empty());
   ASSERT_EQ(0, licensingClientHandler->validateLicensesSuccessEvent.licenses.size());
   ASSERT_EQ(1, licensingClientHandler->validateLicensesSuccessEvent.invalidLicenses.size());
   ASSERT_FALSE(licensingClientHandler->validateLicensesSuccessEvent.timeLimitLicense);
   ASSERT_EQ(0, licensingClientHandler->validateLicensesSuccessEvent.timeLimitRemaining);

   // Destroy the client
   ASSERT_EQ(licensingClient->destroy(client), kSuccess);
}

TEST_F(LicensingModuleTest, LicensingAPIMultipleLicenseKeysAllValid) {
   TestAccount alice("alice");

   // Create the client
   LicensingClientManagerInterface* licensingClient = (LicensingClientManagerInterface*) LicensingClientManager::getInterface(alice.phone);
   LicensingClientHandle client = licensingClient->create();
   ASSERT_TRUE(client > 0);

   // Set the handler
   std::unique_ptr<MyHandler> licensingClientHandler(new MyHandler());
   licensingClient->setHandler(client, licensingClientHandler.get());

   // Configure the client
   LicensingClientSettings settings;
   settings.licenseKeys.push_back(LICENSE_KEY_1);
   settings.licenseKeys.push_back(LICENSE_KEY_2);
   settings.licenseKeys.push_back(LICENSE_KEY_3);
   settings.licenseDocumentLocation = LICENSE_DOCUMENT_LOCATION;
   ASSERT_EQ(licensingClient->applySettings(client, settings), kSuccess);

   // Validate licenses
   ASSERT_EQ(licensingClient->validateLicenses(client), kSuccess);

   // Expect one valid license and no invalid one
   licensingClient->process(LicensingClientManager::kBlockingModeInfinite);
   ASSERT_EQ(client, licensingClientHandler->handle);
   ASSERT_TRUE(licensingClientHandler->onValidateLicensesSuccessInvoked);
   ASSERT_FALSE(licensingClientHandler->validateLicensesSuccessEvent.hardwareId.empty());
   ASSERT_EQ(3, licensingClientHandler->validateLicensesSuccessEvent.licenses.size());
   ASSERT_EQ(0, licensingClientHandler->validateLicensesSuccessEvent.invalidLicenses.size());
   ASSERT_FALSE(licensingClientHandler->validateLicensesSuccessEvent.timeLimitLicense);
   ASSERT_EQ(0, licensingClientHandler->validateLicensesSuccessEvent.timeLimitRemaining);

   // Destroy the client
   ASSERT_EQ(licensingClient->destroy(client), kSuccess);
}

TEST_F(LicensingModuleTest, DISABLED_LicensingAPIMultipleLicenseKeysOneInvalid) {
   TestAccount alice("alice");

   // Create the client
   LicensingClientManagerInterface* licensingClient = (LicensingClientManagerInterface*) LicensingClientManager::getInterface(alice.phone);
   LicensingClientHandle client = licensingClient->create();
   ASSERT_TRUE(client > 0);

   // Set the handler
   std::unique_ptr<MyHandler> licensingClientHandler(new MyHandler());
   licensingClient->setHandler(client, licensingClientHandler.get());

   // Configure the client
   LicensingClientSettings settings;
   settings.licenseKeys.push_back(LICENSE_KEY_1);
   settings.licenseKeys.push_back(INVALID_LICENSE_KEY); // Invalid key
   settings.licenseKeys.push_back(LICENSE_KEY_3);
   settings.licenseDocumentLocation = LICENSE_DOCUMENT_LOCATION;
   ASSERT_EQ(licensingClient->applySettings(client, settings), kSuccess);

   // Validate licenses
   ASSERT_EQ(licensingClient->validateLicenses(client), kSuccess);

   // Expect one valid license and no invalid one
   licensingClient->process(LicensingClientManager::kBlockingModeInfinite);
   ASSERT_EQ(client, licensingClientHandler->handle);
   ASSERT_TRUE(licensingClientHandler->onValidateLicensesSuccessInvoked);
   ASSERT_FALSE(licensingClientHandler->validateLicensesSuccessEvent.hardwareId.empty());
   ASSERT_EQ(2, licensingClientHandler->validateLicensesSuccessEvent.licenses.size());
   ASSERT_EQ(1, licensingClientHandler->validateLicensesSuccessEvent.invalidLicenses.size());
   ASSERT_FALSE(licensingClientHandler->validateLicensesSuccessEvent.timeLimitLicense);
   ASSERT_EQ(0, licensingClientHandler->validateLicensesSuccessEvent.timeLimitRemaining);

   // Destroy the client
   ASSERT_EQ(licensingClient->destroy(client), kSuccess);
}

TEST_F(LicensingModuleTest, LicensingAPIValidTimeLimitWithValidLicenseKey) {
   TestAccount alice("alice");

   // Create the client
   LicensingClientManagerInterface* licensingClient = (LicensingClientManagerInterface*) LicensingClientManager::getInterface(alice.phone);
   LicensingClientHandle client = licensingClient->create();
   ASSERT_TRUE(client > 0);

   // Set the handler
   std::unique_ptr<MyHandler> licensingClientHandler(new MyHandler());
   licensingClient->setHandler(client, licensingClientHandler.get());

   // Configure the client
   LicensingClientSettings settings;
   settings.expiryYear = 2029;
   settings.expiryMonth = 12;
   settings.expiryDay = 31;
   settings.licenseDocumentLocation = LICENSE_DOCUMENT_LOCATION;
   settings.licenseKeys.push_back(LICENSE_KEY_1);
   ASSERT_EQ(licensingClient->applySettings(client, settings), kSuccess);

   // Validate licenses
   ASSERT_EQ(licensingClient->validateLicenses(client), kSuccess);

   // Expect one valid license and no invalid one
   licensingClient->process(LicensingClientManager::kBlockingModeInfinite);
   ASSERT_EQ(client, licensingClientHandler->handle);
   ASSERT_TRUE(licensingClientHandler->onValidateLicensesSuccessInvoked);
   ASSERT_FALSE(licensingClientHandler->validateLicensesSuccessEvent.hardwareId.empty());
   ASSERT_EQ(1, licensingClientHandler->validateLicensesSuccessEvent.licenses.size());
   ASSERT_EQ(0, licensingClientHandler->validateLicensesSuccessEvent.invalidLicenses.size());
   ASSERT_TRUE(licensingClientHandler->validateLicensesSuccessEvent.timeLimitLicense);
   ASSERT_TRUE(licensingClientHandler->validateLicensesSuccessEvent.timeLimitRemaining > 0);

   // Destroy the client
   ASSERT_EQ(licensingClient->destroy(client), kSuccess);
}

TEST_F(LicensingModuleTest, LicensingAPIExpiredTimeLimitWithValidLicenseKey) {
   TestAccount alice("alice");

   // Create the client
   LicensingClientManagerInterface* licensingClient = (LicensingClientManagerInterface*) LicensingClientManager::getInterface(alice.phone);
   LicensingClientHandle client = licensingClient->create();
   ASSERT_TRUE(client > 0);

   // Set the handler
   std::unique_ptr<MyHandler> licensingClientHandler(new MyHandler());
   licensingClient->setHandler(client, licensingClientHandler.get());

   // Configure the client
   LicensingClientSettings settings;
   settings.expiryYear = 1999;
   settings.expiryMonth = 12;
   settings.expiryDay = 31;
   settings.licenseDocumentLocation = LICENSE_DOCUMENT_LOCATION;
   settings.licenseKeys.push_back(LICENSE_KEY_1);
   ASSERT_EQ(licensingClient->applySettings(client, settings), kSuccess);

   // Validate licenses
   ASSERT_EQ(licensingClient->validateLicenses(client), kSuccess);

   // Expect time timit expired. No license key validation performed as a result.
   licensingClient->process(LicensingClientManager::kBlockingModeInfinite);
   ASSERT_EQ(licensingClientHandler->handle, client);
   ASSERT_TRUE(licensingClientHandler->onValidateLicensesFailureInvoked);
   ASSERT_EQ(licensingClientHandler->validateLicensesFailureEvent.status, LicenseStatus_Expired);

   // Destroy the client
   ASSERT_EQ(licensingClient->destroy(client), kSuccess);
}

TEST_F(LicensingModuleTest, DISABLED_LicensingAPIValidTimeLimitWithInvalidLicenseKey) {
   TestAccount alice("alice");

   // Create the client
   LicensingClientManagerInterface* licensingClient = (LicensingClientManagerInterface*) LicensingClientManager::getInterface(alice.phone);
   LicensingClientHandle client = licensingClient->create();
   ASSERT_TRUE(client > 0);

   // Set the handler
   std::unique_ptr<MyHandler> licensingClientHandler(new MyHandler());
   licensingClient->setHandler(client, licensingClientHandler.get());

   // Configure the client
   LicensingClientSettings settings;
   settings.expiryYear = 2029;
   settings.expiryMonth = 12;
   settings.expiryDay = 31;
   settings.licenseDocumentLocation = LICENSE_DOCUMENT_LOCATION;
   settings.licenseKeys.push_back(INVALID_LICENSE_KEY);
   ASSERT_EQ(licensingClient->applySettings(client, settings), kSuccess);

   // Validate licenses
   ASSERT_EQ(licensingClient->validateLicenses(client), kSuccess);

   // Expect invalid license key
   licensingClient->process(LicensingClientManager::kBlockingModeInfinite);
   ASSERT_EQ(client, licensingClientHandler->handle);
   ASSERT_TRUE(licensingClientHandler->onValidateLicensesSuccessInvoked);
   ASSERT_FALSE(licensingClientHandler->validateLicensesSuccessEvent.hardwareId.empty());
   ASSERT_EQ(0, licensingClientHandler->validateLicensesSuccessEvent.licenses.size());
   ASSERT_EQ(1, licensingClientHandler->validateLicensesSuccessEvent.invalidLicenses.size());
   ASSERT_TRUE(licensingClientHandler->validateLicensesSuccessEvent.timeLimitLicense);
   ASSERT_TRUE(licensingClientHandler->validateLicensesSuccessEvent.timeLimitRemaining > 0);

   // Destroy the client
   ASSERT_EQ(licensingClient->destroy(client), kSuccess);
}

TEST_F(LicensingModuleTest, LicensingAPIAccounts) {
   TestAccount alice("alice");

   // Create the client
   LicensingClientManagerInterface* licensingClient = (LicensingClientManagerInterface*) LicensingClientManager::getInterface(alice.phone);
   LicensingClientHandle client = licensingClient->create();
   ASSERT_TRUE(client > 0);

   // Set the handler
   std::unique_ptr<MyHandler> licensingClientHandler(new MyHandler());
   licensingClient->setHandler(client, licensingClientHandler.get());

   // Configure the client
   LicensingClientSettings settings;
   settings.licenseKeys.push_back(LICENSE_KEY_1);
   settings.licenseDocumentLocation = LICENSE_DOCUMENT_LOCATION;
   settings.provisioningId = LICENSE_AOR;
   settings.keySourceUrl = "http://license.counterpath.com/generate";
   AccountInfo account1;
   account1.type = "SIP";
   account1.enabled = true;
   account1.user = "sip:<EMAIL>";
   account1.defaultAccount = true;
   settings.accounts.push_back(account1);
   AccountInfo account2;
   account2.type = "XMPP";
   account2.enabled = false;
   account2.user = "<EMAIL>";
   account2.defaultAccount = false;
   settings.accounts.push_back(account2);
   ASSERT_EQ(licensingClient->applySettings(client, settings), kSuccess);

   // Validate licenses
   ASSERT_EQ(licensingClient->validateLicenses(client), kSuccess);

   // Expect one valid license and no invalid one
   licensingClient->process(LicensingClientManager::kBlockingModeInfinite);
   ASSERT_EQ(client, licensingClientHandler->handle);
   ASSERT_TRUE(licensingClientHandler->onValidateLicensesSuccessInvoked);
   ASSERT_FALSE(licensingClientHandler->validateLicensesSuccessEvent.hardwareId.empty());
   ASSERT_EQ(1, licensingClientHandler->validateLicensesSuccessEvent.licenses.size());
   ASSERT_EQ(0, licensingClientHandler->validateLicensesSuccessEvent.invalidLicenses.size());
   ASSERT_FALSE(licensingClientHandler->validateLicensesSuccessEvent.timeLimitLicense);
   ASSERT_EQ(0, licensingClientHandler->validateLicensesSuccessEvent.timeLimitRemaining);

   // Destroy the client
   ASSERT_EQ(licensingClient->destroy(client), kSuccess);
}

TEST_F(LicensingModuleTest, LicensingAPIBadCharacter) {
   TestAccount alice("alice");

   mServer.default_resource["POST"] = [](std::shared_ptr<HttpServer::Response> response,
      std::shared_ptr<HttpServer::Request> request)
   {
      // here we make sure that the invalid characters were replaced with the placeholder
      if (request->content.string().find("CP Ver \u0000FFFD 1.00") != std::string::npos &&
          request->content.string().find("user\<EMAIL>") != std::string::npos &&
          request->content.string().find("user@\u0000FFFDcounterpath.com") != std::string::npos)
      {
         std::cout << "SUCCESS!!!!" << std::endl;
         SUCCEED();
      }
      else
      {
         FAIL();
      }
   };

   thread server_thread([this]()
   {
      mServer.start();
   });

   // the SimpleWeb example code demonstrates waiting for 1 second to let the HTTP server start.
   // unsure if there we could instead rely on an event..
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   // Create the client
   LicensingClientManagerInterface* licensingClient = (LicensingClientManagerInterface*)LicensingClientManager::getInterface(alice.phone);
   LicensingClientHandle client = licensingClient->create();
   ASSERT_TRUE(client > 0);

   // Set the handler
   std::unique_ptr<MyHandler> licensingClientHandler(new MyHandler());
   licensingClient->setHandler(client, licensingClientHandler.get());

   // Configure the client
   LicensingClientSettings settings;
   settings.licenseKeys.push_back(LICENSE_KEY_1);
   settings.licenseDocumentLocation = LICENSE_DOCUMENT_LOCATION;
   settings.provisioningId = LICENSE_AOR;
   settings.keySourceUrl = "http://license.counterpath.com/generate";
   settings.licenseUrl = serverBaseUrl().c_str();
   settings.forceRemoteCheck = true;
   settings.clientVersion = "CP Ver \x07 1.00";       // note the \x07 invalid XML character
   AccountInfo account1;
   account1.type = "SIP";
   account1.enabled = true;
   account1.user = "sip:user\<EMAIL>";  // note the \u0010 invalid XML character
   account1.defaultAccount = true;
   settings.accounts.push_back(account1);
   AccountInfo account2;
   account2.type = "XMPP";
   account2.enabled = false;
   account2.user = "user@\x0Bcounterpath.com";        // note the \x0B invalid XML character
   account2.defaultAccount = false;
   settings.accounts.push_back(account2);
   ASSERT_EQ(licensingClient->applySettings(client, settings), kSuccess);

   // Validate licenses
   ASSERT_EQ(licensingClient->validateLicenses(client), kSuccess);

   // Expect one valid license and no invalid one
   licensingClient->process(LicensingClientManager::kBlockingModeInfinite);

   // Destroy the client
   ASSERT_EQ(licensingClient->destroy(client), kSuccess);

   mServer.stop();
   server_thread.join();
}

TEST_F(LicensingModuleTest, DISABLED_LicensingAPIValidateLicensesCalledTwice) {
   TestAccount alice("alice");

   // Create the client
   LicensingClientManagerInterface* licensingClient = (LicensingClientManagerInterface*) LicensingClientManager::getInterface(alice.phone);
   LicensingClientHandle client = licensingClient->create();
   ASSERT_TRUE(client > 0);

   // Set the handler
   std::unique_ptr<MyHandler> licensingClientHandler(new MyHandler());
   licensingClient->setHandler(client, licensingClientHandler.get());

   // Configure the client with invalid license key
   LicensingClientSettings settings;
   settings.licenseKeys.push_back(INVALID_LICENSE_KEY); // Invalid key
   settings.licenseDocumentLocation = LICENSE_DOCUMENT_LOCATION;
   ASSERT_EQ(licensingClient->applySettings(client, settings), kSuccess);

   // Validate licenses
   ASSERT_EQ(licensingClient->validateLicenses(client), kSuccess);

   // Expect no valid license and one invalid one
   licensingClient->process(LicensingClientManager::kBlockingModeInfinite);
   ASSERT_EQ(client, licensingClientHandler->handle);
   ASSERT_TRUE(licensingClientHandler->onValidateLicensesSuccessInvoked);
   ASSERT_FALSE(licensingClientHandler->validateLicensesSuccessEvent.hardwareId.empty());
   ASSERT_EQ(0, licensingClientHandler->validateLicensesSuccessEvent.licenses.size());
   ASSERT_EQ(1, licensingClientHandler->validateLicensesSuccessEvent.invalidLicenses.size());
   ASSERT_FALSE(licensingClientHandler->validateLicensesSuccessEvent.timeLimitLicense);
   ASSERT_EQ(0, licensingClientHandler->validateLicensesSuccessEvent.timeLimitRemaining);

   // Configure the client with a valid license key this time
   settings.licenseKeys.clear();
   settings.licenseKeys.push_back(LICENSE_KEY_1);
   ASSERT_EQ(licensingClient->applySettings(client, settings), kSuccess);

   // Validate licenses
   ASSERT_EQ(licensingClient->validateLicenses(client), kSuccess);

   // Expect one valid license and no invalid one
   licensingClient->process(LicensingClientManager::kBlockingModeInfinite);
   ASSERT_EQ(client, licensingClientHandler->handle);
   ASSERT_TRUE(licensingClientHandler->onValidateLicensesSuccessInvoked);
   ASSERT_FALSE(licensingClientHandler->validateLicensesSuccessEvent.hardwareId.empty());
   ASSERT_EQ(1, licensingClientHandler->validateLicensesSuccessEvent.licenses.size());
   ASSERT_EQ(0, licensingClientHandler->validateLicensesSuccessEvent.invalidLicenses.size());
   ASSERT_FALSE(licensingClientHandler->validateLicensesSuccessEvent.timeLimitLicense);
   ASSERT_EQ(0, licensingClientHandler->validateLicensesSuccessEvent.timeLimitRemaining);

   // Destroy the client
   ASSERT_EQ(licensingClient->destroy(client), kSuccess);
}

TEST_F(LicensingModuleTest, LicensingAPISecondaryServer) {
   TestAccount alice("alice", Account_NoInit);
   alice.init();

   // Create the client
   LicensingClientManagerInterface* licensingClient = (LicensingClientManagerInterface*) LicensingClientManager::getInterface(alice.phone);
   LicensingClientHandle client = licensingClient->create();
   ASSERT_TRUE(client > 0);

   // Set the handler
   std::unique_ptr<MyHandler> licensingClientHandler(new MyHandler());
   licensingClient->setHandler(client, licensingClientHandler.get());

   // Configure the client
   LicensingClientSettings settings;
   settings.licenseKeys.push_back(LICENSE_KEY_1);
   settings.licenseDocumentLocation = LICENSE_DOCUMENT_LOCATION;
   settings.provisioningId = LICENSE_AOR;
   settings.keySourceUrl = "http://license.counterpath.com/generate";
   settings.licenseUrl = INVALID_LICENSE_URL;
   settings.licenseUrlSecondary = CPCAPI2_BRAND_LICENSE_URL;
   ASSERT_EQ(licensingClient->applySettings(client, settings), kSuccess);

   // Validate licenses
   ASSERT_EQ(licensingClient->validateLicenses(client), kSuccess);

   // Expect one valid license and no invalid one
   licensingClient->process(LicensingClientManager::kBlockingModeInfinite);
   ASSERT_EQ(client, licensingClientHandler->handle);
   ASSERT_TRUE(licensingClientHandler->onValidateLicensesSuccessInvoked);
   ASSERT_FALSE(licensingClientHandler->validateLicensesSuccessEvent.hardwareId.empty());
   ASSERT_EQ(1, licensingClientHandler->validateLicensesSuccessEvent.licenses.size());
   ASSERT_EQ(0, licensingClientHandler->validateLicensesSuccessEvent.invalidLicenses.size());
   ASSERT_FALSE(licensingClientHandler->validateLicensesSuccessEvent.timeLimitLicense);
   ASSERT_EQ(0, licensingClientHandler->validateLicensesSuccessEvent.timeLimitRemaining);

   // Destroy the client
   ASSERT_EQ(licensingClient->destroy(client), kSuccess);
}

TEST_F(LicensingModuleTest, SecondaryServer1xx)
{
   TestAccount alice("alice");

   const std::string primary("/primary");
   const std::string secondary("/secondary");
   static bool isPrimaryHit = false;
   static bool isSecondaryHit = false;
   static bool isOrdered = false;

   // listens for requests at /primary
   mServer.resource[resourcePathRegex(primary)]["POST"] = [](std::shared_ptr<HttpServer::Response> response,
      std::shared_ptr<HttpServer::Request> request)
   {
      isPrimaryHit = true;
      std::cout << "Send 100 Continue." << std::endl;
      cpc::string content = "LicensingModuleTest::SecondaryServer1xx - Primary test response 100 Continue.";
      *response << "HTTP/1.1 100 Continue\r\nContent-Length: " << content.size() << "\r\n\r\n" << "Content-Type: application/xml" << "\r\n\r\n" << content;

      cpc::string appStr = "application/xml";
      auto it = request->header.find("Content-Type");
      if (it != request->header.end())
      {
         if (!it->second.empty() && (it->second.compare(0, appStr.size(), appStr) == 0))
         {
            std::cout << "SUCCESS!!!!" << std::endl;
            SUCCEED();
         }
         else
         {
            FAIL();
         }
      }
      else
      {
         FAIL();
      }
   };

   // listens for requests at /secondary
   mServer.resource[resourcePathRegex(secondary)]["POST"] = [](std::shared_ptr<HttpServer::Response> response,
      std::shared_ptr<HttpServer::Request> request)
   {
      isSecondaryHit = true;
      isOrdered = isPrimaryHit;
      std::cout << "Send 200 OK." << std::endl;
      cpc::string content = "LicensingModuleTest::SecondaryServer1xx - Secondary test response 200 OK.";
      *response << "HTTP/1.1 200 OK\r\nContent-Length: " << content.size() << "\r\n\r\n" << "Content-Type: application/xml" << "\r\n\r\n" << content;

      cpc::string appStr = "application/xml";
      auto it = request->header.find("Content-Type");
      if (it != request->header.end())
      {
         if (!it->second.empty() && (it->second.compare(0, appStr.size(), appStr) == 0))
         {
            std::cout << "SUCCESS!!!!" << std::endl;
            SUCCEED();
         }
         else
         {
            FAIL();
         }
      }
      else
      {
         FAIL();
      }
   };

   // listens for any other requests
   mServer.default_resource["POST"] = [](std::shared_ptr<HttpServer::Response> response,
      std::shared_ptr<HttpServer::Request> request)
   {
      FAIL();
   };

   thread server_thread([this]()
   {
      mServer.start();
   });

   // the SimpleWeb example code demonstrates waiting for 1 second to let the HTTP server start.
   // unsure if there we could instead rely on an event..
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   std::stringstream primaryUrl, secondaryUrl;
   primaryUrl << serverBaseUrl() << primary;
   secondaryUrl << serverBaseUrl() << secondary;

   // Create the client
   LicensingClientManagerInterface* licensingClient = (LicensingClientManagerInterface*)LicensingClientManager::getInterface(alice.phone);
   LicensingClientHandle client = licensingClient->create();
   ASSERT_TRUE(client > 0);

   // Set the handler
   std::unique_ptr<MyHandler> licensingClientHandler(new MyHandler());
   licensingClient->setHandler(client, licensingClientHandler.get());

   // Configure the client
   LicensingClientSettings settings;
   settings.licenseKeys.push_back(LICENSE_KEY_1);
   settings.licenseDocumentLocation = LICENSE_DOCUMENT_LOCATION;
   settings.provisioningId = LICENSE_AOR;
   settings.keySourceUrl = "http://license.counterpath.com/generate";
   settings.licenseUrl = primaryUrl.str().c_str();
   settings.licenseUrlSecondary = secondaryUrl.str().c_str();
   settings.forceRemoteCheck = true;
   ASSERT_EQ(licensingClient->applySettings(client, settings), kSuccess);

   // Validate licenses
   ASSERT_EQ(licensingClient->validateLicenses(client), kSuccess);

   // Expect one valid license and no invalid one
   licensingClient->process(LicensingClientManager::kBlockingModeInfinite);

   // Destroy the client
   ASSERT_EQ(licensingClient->destroy(client), kSuccess);

   ASSERT_TRUE(isPrimaryHit);
   ASSERT_TRUE(isSecondaryHit);
   ASSERT_TRUE(isOrdered);

   mServer.stop();
   server_thread.join();
   return;
}

TEST_F(LicensingModuleTest, SecondaryServer3xx)
{
   TestAccount alice("alice");

   const std::string primary("/primary");
   const std::string secondary("/secondary");
   static bool isPrimaryHit = false;
   static bool isSecondaryHit = false;
   static bool isOrdered = false;

   // listens for requests at /primary
   mServer.resource[resourcePathRegex(primary)]["POST"] = [](std::shared_ptr<HttpServer::Response> response,
      std::shared_ptr<HttpServer::Request> request)
   {
      isPrimaryHit = true;
      std::cout << "Send 300 Multiple Choices." << std::endl;
      cpc::string content = "LicensingModuleTest::SecondaryServer3xx - Primary test response 300 Multiple Choices.";
      *response << "HTTP/1.1 300 Multiple Choices\r\nContent-Length: " << content.size() << "\r\n\r\n" << "Content-Type: application/xml" << "\r\n\r\n" << content;

      cpc::string appStr = "application/xml";
      auto it = request->header.find("Content-Type");
      if (it != request->header.end())
      {
         if (!it->second.empty() && (it->second.compare(0, appStr.size(), appStr) == 0))
         {
            std::cout << "SUCCESS!!!!" << std::endl;
            SUCCEED();
         }
         else
         {
            FAIL();
         }
      }
      else
      {
         FAIL();
      }
   };

   // listens for requests at /secondary
   mServer.resource[resourcePathRegex(secondary)]["POST"] = [](std::shared_ptr<HttpServer::Response> response,
      std::shared_ptr<HttpServer::Request> request)
   {
      isSecondaryHit = true;
      isOrdered = isPrimaryHit;
      std::cout << "Send 200 OK." << std::endl;
      cpc::string content = "LicensingModuleTest::SecondaryServer1xx - Secondary test response 200 OK.";
      *response << "HTTP/1.1 200 OK\r\nContent-Length: " << content.size() << "\r\n\r\n" << "Content-Type: application/xml" << "\r\n\r\n" << content;

      cpc::string appStr = "application/xml";
      auto it = request->header.find("Content-Type");
      if (it != request->header.end())
      {
         if (!it->second.empty() && (it->second.compare(0, appStr.size(), appStr) == 0))
         {
            std::cout << "SUCCESS!!!!" << std::endl;
            SUCCEED();
         }
         else
         {
            FAIL();
         }
      }
      else
      {
         FAIL();
      }
   };

   // listens for any other requests
   mServer.default_resource["POST"] = [](std::shared_ptr<HttpServer::Response> response,
      std::shared_ptr<HttpServer::Request> request)
   {
      FAIL();
   };

   thread server_thread([this]()
   {
      mServer.start();
   });

   // the SimpleWeb example code demonstrates waiting for 1 second to let the HTTP server start.
   // unsure if there we could instead rely on an event..
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   std::stringstream primaryUrl, secondaryUrl;
   primaryUrl << serverBaseUrl() << primary;
   secondaryUrl << serverBaseUrl() << secondary;

   // Create the client
   LicensingClientManagerInterface* licensingClient = (LicensingClientManagerInterface*)LicensingClientManager::getInterface(alice.phone);
   LicensingClientHandle client = licensingClient->create();
   ASSERT_TRUE(client > 0);

   // Set the handler
   std::unique_ptr<MyHandler> licensingClientHandler(new MyHandler());
   licensingClient->setHandler(client, licensingClientHandler.get());

   // Configure the client
   LicensingClientSettings settings;
   settings.licenseKeys.push_back(LICENSE_KEY_1);
   settings.licenseDocumentLocation = LICENSE_DOCUMENT_LOCATION;
   settings.provisioningId = LICENSE_AOR;
   settings.keySourceUrl = "http://license.counterpath.com/generate";
   settings.licenseUrl = primaryUrl.str().c_str();
   settings.licenseUrlSecondary = secondaryUrl.str().c_str();
   settings.forceRemoteCheck = true;
   ASSERT_EQ(licensingClient->applySettings(client, settings), kSuccess);

   // Validate licenses
   ASSERT_EQ(licensingClient->validateLicenses(client), kSuccess);

   // Expect one valid license and no invalid one
   licensingClient->process(LicensingClientManager::kBlockingModeInfinite);

   // Destroy the client
   ASSERT_EQ(licensingClient->destroy(client), kSuccess);

   ASSERT_TRUE(isPrimaryHit);
   ASSERT_TRUE(isSecondaryHit);
   ASSERT_TRUE(isOrdered);

   mServer.stop();
   server_thread.join();
   return;
}

TEST_F(LicensingModuleTest, SecondaryServer4xx)
{
   TestAccount alice("alice");

   const std::string primary("/primary");
   const std::string secondary("/secondary");
   static bool isPrimaryHit = false;
   static bool isSecondaryHit = false;
   static bool isOrdered = false;

   // listens for requests at /primary
   mServer.resource[resourcePathRegex(primary)]["POST"] = [](std::shared_ptr<HttpServer::Response> response,
      std::shared_ptr<HttpServer::Request> request)
   {
      isPrimaryHit = true;
      std::cout << "Send 400 Bad Request." << std::endl;
      cpc::string content = "LicensingModuleTest::SecondaryServer4xx - Primary test response 400 Bad Request.";
      *response << "HTTP/1.1 400 Bad Request\r\nContent-Length: " << content.size() << "\r\n\r\n" << "Content-Type: application/xml" << "\r\n\r\n" << content;

      cpc::string appStr = "application/xml";
      auto it = request->header.find("Content-Type");
      if (it != request->header.end())
      {
         if (!it->second.empty() && (it->second.compare(0, appStr.size(), appStr) == 0))
         {
            std::cout << "SUCCESS!!!!" << std::endl;
            SUCCEED();
         }
         else
         {
            FAIL();
         }
      }
      else
      {
         FAIL();
      }
   };

   // listens for requests at /secondary
   mServer.resource[resourcePathRegex(secondary)]["POST"] = [](std::shared_ptr<HttpServer::Response> response,
      std::shared_ptr<HttpServer::Request> request)
   {
      isSecondaryHit = true;
      isOrdered = isPrimaryHit;
      std::cout << "Send 200 OK." << std::endl;
      cpc::string content = "LicensingModuleTest::SecondaryServer1xx - Secondary test response 200 OK.";
      *response << "HTTP/1.1 200 OK\r\nContent-Length: " << content.size() << "\r\n\r\n" << "Content-Type: application/xml" << "\r\n\r\n" << content;

      cpc::string appStr = "application/xml";
      auto it = request->header.find("Content-Type");
      if (it != request->header.end())
      {
         if (!it->second.empty() && (it->second.compare(0, appStr.size(), appStr) == 0))
         {
            std::cout << "SUCCESS!!!!" << std::endl;
            SUCCEED();
         }
         else
         {
            FAIL();
         }
      }
      else
      {
         FAIL();
      }
   };

   // listens for any other requests
   mServer.default_resource["POST"] = [](std::shared_ptr<HttpServer::Response> response,
      std::shared_ptr<HttpServer::Request> request)
   {
      FAIL();
   };

   thread server_thread([this]()
   {
      mServer.start();
   });

   // the SimpleWeb example code demonstrates waiting for 1 second to let the HTTP server start.
   // unsure if there we could instead rely on an event..
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   std::stringstream primaryUrl, secondaryUrl;
   primaryUrl << serverBaseUrl() << primary;
   secondaryUrl << serverBaseUrl() << secondary;

   // Create the client
   LicensingClientManagerInterface* licensingClient = (LicensingClientManagerInterface*)LicensingClientManager::getInterface(alice.phone);
   LicensingClientHandle client = licensingClient->create();
   ASSERT_TRUE(client > 0);

   // Set the handler
   std::unique_ptr<MyHandler> licensingClientHandler(new MyHandler());
   licensingClient->setHandler(client, licensingClientHandler.get());

   // Configure the client
   LicensingClientSettings settings;
   settings.licenseKeys.push_back(LICENSE_KEY_1);
   settings.licenseDocumentLocation = LICENSE_DOCUMENT_LOCATION;
   settings.provisioningId = LICENSE_AOR;
   settings.keySourceUrl = "http://license.counterpath.com/generate";
   settings.licenseUrl = primaryUrl.str().c_str();
   settings.licenseUrlSecondary = secondaryUrl.str().c_str();
   settings.forceRemoteCheck = true;
   ASSERT_EQ(licensingClient->applySettings(client, settings), kSuccess);

   // Validate licenses
   ASSERT_EQ(licensingClient->validateLicenses(client), kSuccess);

   // Expect one valid license and no invalid one
   licensingClient->process(LicensingClientManager::kBlockingModeInfinite);

   // Destroy the client
   ASSERT_EQ(licensingClient->destroy(client), kSuccess);

   ASSERT_TRUE(isPrimaryHit);
   ASSERT_TRUE(isSecondaryHit);
   ASSERT_TRUE(isOrdered);

   mServer.stop();
   server_thread.join();
   return;
}

TEST_F(LicensingModuleTest, SecondaryServer5xx)
{
   TestAccount alice("alice");

   const std::string primary("/primary");
   const std::string secondary("/secondary");
   static bool isPrimaryHit = false;
   static bool isSecondaryHit = false;
   static bool isOrdered = false;

   // listens for requests at /primary
   mServer.resource[resourcePathRegex(primary)]["POST"] = [](std::shared_ptr<HttpServer::Response> response,
      std::shared_ptr<HttpServer::Request> request)
   {
      isPrimaryHit = true;
      std::cout << "Send 500 Internal Server Error." << std::endl;
      cpc::string content = "LicensingModuleTest::SecondaryServer5xx - Primary test response 500 Internal Server Error.";
      *response << "HTTP/1.1 500 Internal Server Error\r\nContent-Length: " << content.size() << "\r\n\r\n" << "Content-Type: application/xml" << "\r\n\r\n" << content;

      cpc::string appStr = "application/xml";
      auto it = request->header.find("Content-Type");
      if (it != request->header.end())
      {
         if (!it->second.empty() && (it->second.compare(0, appStr.size(), appStr) == 0))
         {
            std::cout << "SUCCESS!!!!" << std::endl;
            SUCCEED();
         }
         else
         {
            FAIL();
         }
      }
      else
      {
         FAIL();
      }
   };

   // listens for requests at /secondary
   mServer.resource[resourcePathRegex(secondary)]["POST"] = [](std::shared_ptr<HttpServer::Response> response,
      std::shared_ptr<HttpServer::Request> request)
   {
      isSecondaryHit = true;
      isOrdered = isPrimaryHit;
      std::cout << "Send 200 OK." << std::endl;
      cpc::string content = "LicensingModuleTest::SecondaryServer1xx - Secondary test response 200 OK.";
      *response << "HTTP/1.1 200 OK\r\nContent-Length: " << content.size() << "\r\n\r\n" << "Content-Type: application/xml" << "\r\n\r\n" << content;

      cpc::string appStr = "application/xml";
      auto it = request->header.find("Content-Type");
      if (it != request->header.end())
      {
         if (!it->second.empty() && (it->second.compare(0, appStr.size(), appStr) == 0))
         {
            std::cout << "SUCCESS!!!!" << std::endl;
            SUCCEED();
         }
         else
         {
            FAIL();
         }
      }
      else
      {
         FAIL();
      }
   };

   // listens for any other requests
   mServer.default_resource["POST"] = [](std::shared_ptr<HttpServer::Response> response,
      std::shared_ptr<HttpServer::Request> request)
   {
      FAIL();
   };

   thread server_thread([this]()
   {
      mServer.start();
   });

   // the SimpleWeb example code demonstrates waiting for 1 second to let the HTTP server start.
   // unsure if there we could instead rely on an event..
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   std::stringstream primaryUrl, secondaryUrl;
   primaryUrl << serverBaseUrl() << primary;
   secondaryUrl << serverBaseUrl() << secondary;

   // Create the client
   LicensingClientManagerInterface* licensingClient = (LicensingClientManagerInterface*)LicensingClientManager::getInterface(alice.phone);
   LicensingClientHandle client = licensingClient->create();
   ASSERT_TRUE(client > 0);

   // Set the handler
   std::unique_ptr<MyHandler> licensingClientHandler(new MyHandler());
   licensingClient->setHandler(client, licensingClientHandler.get());

   // Configure the client
   LicensingClientSettings settings;
   settings.licenseKeys.push_back(LICENSE_KEY_1);
   settings.licenseDocumentLocation = LICENSE_DOCUMENT_LOCATION;
   settings.provisioningId = LICENSE_AOR;
   settings.keySourceUrl = "http://license.counterpath.com/generate";
   settings.licenseUrl = primaryUrl.str().c_str();
   settings.licenseUrlSecondary = secondaryUrl.str().c_str();
   settings.forceRemoteCheck = true;
   ASSERT_EQ(licensingClient->applySettings(client, settings), kSuccess);

   // Validate licenses
   ASSERT_EQ(licensingClient->validateLicenses(client), kSuccess);

   // Expect one valid license and no invalid one
   licensingClient->process(LicensingClientManager::kBlockingModeInfinite);

   // Destroy the client
   ASSERT_EQ(licensingClient->destroy(client), kSuccess);

   ASSERT_TRUE(isPrimaryHit);
   ASSERT_TRUE(isSecondaryHit);
   ASSERT_TRUE(isOrdered);

   mServer.stop();
   server_thread.join();
   return;
}

// No response from primary server; fail over to secondary server
TEST_F(LicensingModuleTest, SecondaryServerNoResponse)
{
   TestAccount alice("alice", Account_Init);

   const std::string primary("/primary");
   const std::string secondary("/secondary");
   bool isSecondaryHit = false;
   std::mutex secondaryHitMutex;
   std::chrono::time_point<std::chrono::system_clock> secondaryHitTime;

   // listens for requests at /secondary
   mServer.resource[resourcePathRegex(secondary)]["POST"] = [&](std::shared_ptr<HttpServer::Response> response,
      std::shared_ptr<HttpServer::Request> request)
   {
      std::cout << "Send 200 OK." << std::endl;
      cpc::string content = "LicensingModuleTest::SecondaryServer1xx - Secondary test response 200 OK.";
      *response << "HTTP/1.1 200 OK\r\nContent-Length: " << content.size() << "\r\n\r\n" << "Content-Type: application/xml" << "\r\n\r\n" << content;

      cpc::string appStr = "application/xml";
      auto it = request->header.find("Content-Type");
      if (it != request->header.end())
      {
         if (!it->second.empty() && (it->second.compare(0, appStr.size(), appStr) == 0))
         {
            std::cout << "SUCCESS!!!!" << std::endl;
            std::lock_guard<std::mutex> lock(secondaryHitMutex);
            {
               isSecondaryHit = true;
               secondaryHitTime = std::chrono::system_clock::now();
            }
            SUCCEED();
         }
         else
         {
            FAIL();
         }
      }
      else
      {
         FAIL();
      }
   };

   // listens for any other requests
   mServer.default_resource["POST"] = [](std::shared_ptr<HttpServer::Response> response,
      std::shared_ptr<HttpServer::Request> request)
   {
      FAIL();
   };

   thread server_thread([this]()
   {
      mServer.start();
   });

   // the SimpleWeb example code demonstrates waiting for 1 second to let the HTTP server start.
   // unsure if there we could instead rely on an event..
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   std::stringstream primaryUrl, secondaryUrl;
   primaryUrl << "";
#ifndef __APPLE__
   primaryUrl << "*********"; // should not be routable -- RFC 5737
#else
   // on mac, ********* results in immediate failure whereas *********** times out (what we want).
   // ********* was previously used here, but we have since started bringing this up via ifconfig alias
   // for PTT tests.
   // https://en.wikipedia.org/wiki/Reserved_IP_addresses
   alice.config.settings.nameServers.push_back("***********");
   primaryUrl << "***********";
#endif
   primaryUrl << primary;
   secondaryUrl << serverBaseUrl() << secondary;

   // Create the client
   LicensingClientManagerInterface* licensingClient = (LicensingClientManagerInterface*)LicensingClientManager::getInterface(alice.phone);
   LicensingClientHandle client = licensingClient->create();
   ASSERT_TRUE(client > 0);

   // Set the handler
   std::unique_ptr<MyHandler> licensingClientHandler(new MyHandler());
   licensingClient->setHandler(client, licensingClientHandler.get());

   // Configure the client
   LicensingClientSettings settings;
   settings.licenseKeys.push_back(LICENSE_KEY_1);
   settings.licenseDocumentLocation = LICENSE_DOCUMENT_LOCATION;
   settings.provisioningId = LICENSE_AOR;
   settings.keySourceUrl = "http://license.counterpath.com/generate";
   settings.licenseUrl = primaryUrl.str().c_str();
   settings.licenseUrlSecondary = secondaryUrl.str().c_str();
   settings.forceRemoteCheck = true;
   ASSERT_EQ(licensingClient->applySettings(client, settings), kSuccess);

   std::chrono::time_point<std::chrono::system_clock> startTime = std::chrono::system_clock::now();

   // Validate licenses
   ASSERT_EQ(licensingClient->validateLicenses(client), kSuccess);

   // Expect one valid license and no invalid one
   licensingClient->process(LicensingClientManager::kBlockingModeInfinite);

   std::this_thread::sleep_for(std::chrono::seconds(20));

   // Destroy the client
   ASSERT_EQ(licensingClient->destroy(client), kSuccess);

   std::lock_guard<std::mutex> lock(secondaryHitMutex);
   {
      ASSERT_TRUE(isSecondaryHit);
      std::chrono::seconds deltaSec = std::chrono::duration_cast<std::chrono::seconds>(secondaryHitTime - startTime);
      // should be about 10 seconds
      ASSERT_LT(deltaSec.count(), 12);
      ASSERT_GT(deltaSec.count(), 8);
      safeCout("Took " << deltaSec.count() << " sec to fail over to secondary license server");
   }
   mServer.stop();
   server_thread.join();
   return;
}

TEST_F(LicensingModuleTest, SecondaryServerNonXmlFail)
{
   TestAccount alice("alice");

   const std::string primary("/primary");
   const std::string secondary("/secondary");
   static bool isPrimaryHit = false;
   static bool isSecondaryHit = false;
   static bool isOrdered = false;

   // listens for requests at /primary
   mServer.resource[resourcePathRegex(primary)]["POST"] = [](std::shared_ptr<HttpServer::Response> response,
      std::shared_ptr<HttpServer::Request> request)
   {
      isPrimaryHit = true;
      std::cout << "Send response with non-XML content. " << std::endl;
      cpc::string content = "LicensingModuleTest::SecondaryServerNonXmlFail - Primary test response with non-200 class response containing non-XML content message.";
      *response << "HTTP/1.1 500 Internal Server Error\r\nContent-Length: " << content.size() << "\r\n\r\n" << "Content-Type: text" << "\r\n\r\n" << content;

      cpc::string appStr = "application/xml";
      auto it = request->header.find("Content-Type");
      if (it != request->header.end())
      {
         if (!it->second.empty() && (it->second.compare(0, appStr.size(), appStr) == 0))
         {
            std::cout << "SUCCESS!!!!" << std::endl;
            SUCCEED();
         }
         else
         {
            FAIL();
         }
      }
      else
      {
         FAIL();
      }
   };

   // listens for requests at /secondary
   mServer.resource[resourcePathRegex(secondary)]["POST"] = [](std::shared_ptr<HttpServer::Response> response,
      std::shared_ptr<HttpServer::Request> request)
   {
      isSecondaryHit = true;
      isOrdered = isPrimaryHit;
      std::cout << "Send 200 OK." << std::endl;
      cpc::string content = "LicensingModuleTest::SecondaryServer1xx - Secondary test response 200 OK.";
      *response << "HTTP/1.1 200 OK\r\nContent-Length: " << content.size() << "\r\n\r\n" << "Content-Type: application/xml" << "\r\n\r\n" << content;

      cpc::string appStr = "application/xml";
      auto it = request->header.find("Content-Type");
      if (it != request->header.end())
      {
         if (!it->second.empty() && (it->second.compare(0, appStr.size(), appStr) == 0))
         {
            std::cout << "SUCCESS!!!!" << std::endl;
            SUCCEED();
         }
         else
         {
            FAIL();
         }
      }
      else
      {
         FAIL();
      }
   };

   // listens for any other requests
   mServer.default_resource["POST"] = [](std::shared_ptr<HttpServer::Response> response,
      std::shared_ptr<HttpServer::Request> request)
   {
      FAIL();
   };

   thread server_thread([this]()
   {
      mServer.start();
   });

   // the SimpleWeb example code demonstrates waiting for 1 second to let the HTTP server start.
   // unsure if there we could instead rely on an event..
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   std::stringstream primaryUrl, secondaryUrl;
   primaryUrl << serverBaseUrl() << primary;
   secondaryUrl << serverBaseUrl() << secondary;

   // Create the client
   LicensingClientManagerInterface* licensingClient = (LicensingClientManagerInterface*)LicensingClientManager::getInterface(alice.phone);
   LicensingClientHandle client = licensingClient->create();
   ASSERT_TRUE(client > 0);

   // Set the handler
   std::unique_ptr<MyHandler> licensingClientHandler(new MyHandler());
   licensingClient->setHandler(client, licensingClientHandler.get());

   // Configure the client
   LicensingClientSettings settings;
   settings.licenseKeys.push_back(LICENSE_KEY_1);
   settings.licenseDocumentLocation = LICENSE_DOCUMENT_LOCATION;
   settings.provisioningId = LICENSE_AOR;
   settings.keySourceUrl = "http://license.counterpath.com/generate";
   settings.licenseUrl = primaryUrl.str().c_str();
   settings.licenseUrlSecondary = secondaryUrl.str().c_str();
   settings.forceRemoteCheck = true;
   ASSERT_EQ(licensingClient->applySettings(client, settings), kSuccess);

   // Validate licenses
   ASSERT_EQ(licensingClient->validateLicenses(client), kSuccess);

   // Expect one valid license and no invalid one
   licensingClient->process(LicensingClientManager::kBlockingModeInfinite);

   // Destroy the client
   ASSERT_EQ(licensingClient->destroy(client), kSuccess);

   ASSERT_TRUE(isPrimaryHit);
   ASSERT_TRUE(isSecondaryHit);
   ASSERT_TRUE(isOrdered);

   mServer.stop();
   server_thread.join();
   return;
}

TEST_F(LicensingModuleTest, SecondaryServerNonXmlSuccess)
{
   TestAccount alice("alice");

   const std::string primary("/primary");
   const std::string secondary("/secondary");
   static bool isPrimaryHit = false;

   // listens for requests at /primary
   mServer.resource[resourcePathRegex(primary)]["POST"] = [](std::shared_ptr<HttpServer::Response> response,
      std::shared_ptr<HttpServer::Request> request)
   {
      isPrimaryHit = true;
      std::cout << "Send 200 OK." << std::endl;
      cpc::string content = "LicensingModuleTest::SecondaryServerNonXmlSuccess - Secondary test response 200 OK.";
      *response << "HTTP/1.1 200 OK\r\nContent-Length: " << content.size() << "\r\n\r\n" << "Content-Type: text" << "\r\n\r\n" << content;

      cpc::string appStr = "application/xml";
      auto it = request->header.find("Content-Type");
      if (it != request->header.end())
      {
         if (!it->second.empty() && (it->second.compare(0, appStr.size(), appStr) == 0))
         {
            std::cout << "SUCCESS!!!!" << std::endl;
            SUCCEED();
         }
         else
         {
            FAIL();
         }
      }
      else
      {
         FAIL();
      }
   };

   mServer.resource[resourcePathRegex(secondary)]["POST"] = [](std::shared_ptr<HttpServer::Response> response,
      std::shared_ptr<HttpServer::Request> request)
   {
      FAIL();
   };

   // listens for any other requests
   mServer.default_resource["POST"] = [](std::shared_ptr<HttpServer::Response> response,
      std::shared_ptr<HttpServer::Request> request)
   {
      FAIL();
   };

   thread server_thread([this]()
   {
      mServer.start();
   });

   // the SimpleWeb example code demonstrates waiting for 1 second to let the HTTP server start.
   // unsure if there we could instead rely on an event..
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   std::stringstream primaryUrl, secondaryUrl;
   primaryUrl << serverBaseUrl() << primary;
   secondaryUrl << serverBaseUrl() << secondary;

   // Create the client
   LicensingClientManagerInterface* licensingClient = (LicensingClientManagerInterface*)LicensingClientManager::getInterface(alice.phone);
   LicensingClientHandle client = licensingClient->create();
   ASSERT_TRUE(client > 0);

   // Set the handler
   std::unique_ptr<MyHandler> licensingClientHandler(new MyHandler());
   licensingClient->setHandler(client, licensingClientHandler.get());

   // Configure the client
   LicensingClientSettings settings;
   settings.licenseKeys.push_back(LICENSE_KEY_1);
   settings.licenseDocumentLocation = LICENSE_DOCUMENT_LOCATION;
   settings.provisioningId = LICENSE_AOR;
   settings.keySourceUrl = "http://license.counterpath.com/generate";
   settings.licenseUrl = primaryUrl.str().c_str();
   settings.licenseUrlSecondary = secondaryUrl.str().c_str();
   settings.forceRemoteCheck = true;
   ASSERT_EQ(licensingClient->applySettings(client, settings), kSuccess);

   // Validate licenses
   ASSERT_EQ(licensingClient->validateLicenses(client), kSuccess);

   // Expect one valid license and no invalid one
   licensingClient->process(LicensingClientManager::kBlockingModeInfinite);

   // Destroy the client
   ASSERT_EQ(licensingClient->destroy(client), kSuccess);

   ASSERT_TRUE(isPrimaryHit);

   mServer.stop();
   server_thread.join();
   return;
}

TEST_F(LicensingModuleTest, SecondaryServerNoData)
{
   TestAccount alice("alice");

   const std::string primary("/primary");
   const std::string secondary("/secondary");
   static bool isPrimaryHit = false;
   static bool isSecondaryHit = false;
   static bool isOrdered = false;

   // listens for requests at /primary
   mServer.resource[resourcePathRegex(primary)]["POST"] = [](std::shared_ptr<HttpServer::Response> response,
      std::shared_ptr<HttpServer::Request> request)
   {
      isPrimaryHit = true;
      std::cout << "Send response with no content. " << std::endl;
      cpc::string content = "";
      *response << "HTTP/1.1 100 Continue\r\nContent-Length: " << content.size() << "\r\n\r\n" << "Content-Type: application/xml" << "\r\n\r\n" << content;

      cpc::string appStr = "application/xml";
      auto it = request->header.find("Content-Type");
      if (it != request->header.end())
      {
         if (!it->second.empty() && (it->second.compare(0, appStr.size(), appStr) == 0))
         {
            std::cout << "SUCCESS!!!!" << std::endl;
            SUCCEED();
         }
         else
         {
            FAIL();
         }
      }
      else
      {
         FAIL();
      }
   };

   // listens for requests at /secondary
   mServer.resource[resourcePathRegex(secondary)]["POST"] = [](std::shared_ptr<HttpServer::Response> response,
      std::shared_ptr<HttpServer::Request> request)
   {
      isSecondaryHit = true;
      isOrdered = isPrimaryHit;
      std::cout << "Send 200 OK." << std::endl;
      cpc::string content = "LicensingModuleTest::SecondaryServer1xx - Secondary test response 200 OK.";
      *response << "HTTP/1.1 200 OK\r\nContent-Length: " << content.size() << "\r\n\r\n" << "Content-Type: application/xml" << "\r\n\r\n" << content;

      cpc::string appStr = "application/xml";
      auto it = request->header.find("Content-Type");
      if (it != request->header.end())
      {
         if (!it->second.empty() && (it->second.compare(0, appStr.size(), appStr) == 0))
         {
            std::cout << "SUCCESS!!!!" << std::endl;
            SUCCEED();
         }
         else
         {
            FAIL();
         }
      }
      else
      {
         FAIL();
      }
   };

   // listens for any other requests
   mServer.default_resource["POST"] = [](std::shared_ptr<HttpServer::Response> response,
      std::shared_ptr<HttpServer::Request> request)
   {
      FAIL();
   };

   thread server_thread([this]()
   {
      mServer.start();
   });

   // the SimpleWeb example code demonstrates waiting for 1 second to let the HTTP server start.
   // unsure if there we could instead rely on an event..
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   std::stringstream primaryUrl, secondaryUrl;
   primaryUrl << serverBaseUrl() << primary;
   secondaryUrl << serverBaseUrl() << secondary;

   // Create the client
   LicensingClientManagerInterface* licensingClient = (LicensingClientManagerInterface*)LicensingClientManager::getInterface(alice.phone);
   LicensingClientHandle client = licensingClient->create();
   ASSERT_TRUE(client > 0);

   // Set the handler
   std::unique_ptr<MyHandler> licensingClientHandler(new MyHandler());
   licensingClient->setHandler(client, licensingClientHandler.get());

   // Configure the client
   LicensingClientSettings settings;
   settings.licenseKeys.push_back(LICENSE_KEY_1);
   settings.licenseDocumentLocation = LICENSE_DOCUMENT_LOCATION;
   settings.provisioningId = LICENSE_AOR;
   settings.keySourceUrl = "http://license.counterpath.com/generate";
   settings.licenseUrl = primaryUrl.str().c_str();
   settings.licenseUrlSecondary = secondaryUrl.str().c_str();
   settings.forceRemoteCheck = true;
   ASSERT_EQ(licensingClient->applySettings(client, settings), kSuccess);

   // Validate licenses
   ASSERT_EQ(licensingClient->validateLicenses(client), kSuccess);

   // Expect one valid license and no invalid one
   licensingClient->process(LicensingClientManager::kBlockingModeInfinite);

   // Destroy the client
   ASSERT_EQ(licensingClient->destroy(client), kSuccess);

   ASSERT_TRUE(isPrimaryHit);
   ASSERT_TRUE(isSecondaryHit);
   ASSERT_TRUE(isOrdered);

   mServer.stop();
   server_thread.join();
   return;
}

cpc::string fromFile(const cpc::string& path)
{
   std::ifstream in((TestEnvironmentConfig::testResourcePath() + path).c_str());
   assert(in.is_open());
   std::ostringstream iss;
   iss << in.rdbuf() << std::flush;
   return iss.str().c_str();
}

TEST_F(LicensingModuleTest, LicenseResponseVerifyInvalid) {

   const std::string invalid("/invalid");
   //load an invalid (not signed by our special certificate authority) xml response from file (from OBELISK-4833):
   mServer.resource[resourcePathRegex(invalid)]["POST"] = [](std::shared_ptr<HttpServer::Response> response,
      std::shared_ptr<HttpServer::Request> request)
   {
      cpc::string content = fromFile("response1.xml");
      *response << "HTTP/1.1 200 OK\r\nContent-Length: " << content.size() << "\r\nContent-Type: application/xml\r\n\r\n" << content;
   };
   thread server_thread([this]()
   {
      mServer.start();
   });
   this_thread::sleep_for(chrono::seconds(1));

   TestAccount alice("alice", Account_NoInit);
   alice.init();
   alice.enable();

   LicensingClientManagerInterface* licensingClient = (LicensingClientManagerInterface*)LicensingClientManager::getInterface(alice.phone);
   LicensingClientHandle client = licensingClient->create();
   ASSERT_TRUE(client > 0);

   std::unique_ptr<MyHandler> licensingClientHandler(new MyHandler());
   licensingClient->setHandler(client, licensingClientHandler.get());

   LicensingClientSettings settings;
   settings.licenseKeys.push_back(LICENSE_KEY_1);
   settings.licenseDocumentLocation = LICENSE_DOCUMENT_LOCATION;
   settings.licenseUrl = serverBaseUrl() + invalid.c_str();
   settings.forceRemoteCheck = true;
   ASSERT_EQ(licensingClient->applySettings(client, settings), kSuccess);

   ASSERT_EQ(licensingClient->validateLicenses(client), kSuccess);

   licensingClient->process(LicensingClientManager::kBlockingModeInfinite);
   ASSERT_EQ(client, licensingClientHandler->handle);
   ASSERT_TRUE(licensingClientHandler->onValidateLicensesFailureInvoked);
   ASSERT_FALSE(licensingClientHandler->onValidateLicensesSuccessInvoked);
   ASSERT_TRUE(licensingClientHandler->validateLicensesFailureEvent.status == LicenseStatus_ServerBadData);
   
   // licensing failure should trigger account disable
   {
      SipAccount::SipAccountHandle h;
      SipAccount::SipAccountStatusChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged",
         4000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Unregistering, evt.accountStatus);
   }

   {
      SipAccount::SipAccountHandle h;
      SipAccount::SipAccountStatusChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged",
         4000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Unregistered, evt.accountStatus);
   }
   
   bool force = false;
   bool validateRegistrationState = false; // account is already disabled
   alice.disable(force, validateRegistrationState);

   ASSERT_EQ(licensingClient->destroy(client), kSuccess);

   mServer.stop();
   server_thread.join();
}

TEST_F(LicensingModuleTest, LicenseResponseVerifyValid) {
   
   const std::string valid("/valid");
   //load a valid (signed by our special certificate authority) xml response from file (grabbed from an earlier test):
   mServer.resource[resourcePathRegex(valid)]["POST"] = [](std::shared_ptr<HttpServer::Response> response,
      std::shared_ptr<HttpServer::Request> request)
   {
      cpc::string content = fromFile("response2.xml");
      *response << "HTTP/1.1 200 OK\r\nContent-Length: " << content.size() << "\r\nContent-Type: application/xml\r\n\r\n" << content;
   };
   thread server_thread([this]()
   {
      mServer.start();
   });
   this_thread::sleep_for(chrono::seconds(1));

   TestAccount alice("alice", Account_NoInit);
   alice.init();
   alice.enable();

   LicensingClientManagerInterface* licensingClient = (LicensingClientManagerInterface*)LicensingClientManager::getInterface(alice.phone);
   LicensingClientHandle client = licensingClient->create();
   ASSERT_TRUE(client > 0);

   std::unique_ptr<MyHandler> licensingClientHandler(new MyHandler());
   licensingClient->setHandler(client, licensingClientHandler.get());

   LicensingClientSettings settings;
   settings.licenseKeys.push_back(LICENSE_KEY_1);
   settings.licenseDocumentLocation = LICENSE_DOCUMENT_LOCATION;
   settings.licenseUrl = serverBaseUrl() + valid.c_str();
   settings.forceRemoteCheck = true;
   ASSERT_EQ(licensingClient->applySettings(client, settings), kSuccess);

   ASSERT_EQ(licensingClient->validateLicenses(client), kSuccess);

   licensingClient->process(LicensingClientManager::kBlockingModeInfinite);
   ASSERT_EQ(client, licensingClientHandler->handle);
   ASSERT_TRUE(licensingClientHandler->onValidateLicensesSuccessInvoked);

   alice.disable();

   ASSERT_EQ(licensingClient->destroy(client), kSuccess);

   mServer.stop();
   server_thread.join();
}

#endif // !CPCAPI2_BRAND_USE_SDK_LICENSING

#endif // _WIN32 || __linux__

}  // namespace

#endif // CPCAPI2_BRAND_LICENSING_MODULE
