//
//  memoryUsageHelper.h
//  CPCAPI2AutoTests
//
//  Created by j<PERSON><PERSON> on 2018-10-04.
//  Copyright © 2018 <PERSON>. All rights reserved.
//
#pragma once
#include <stdio.h>
namespace memoryUsageHelper
{
   struct MemoryUsageInfo
   {
      double currentProcessMemoryUsage; // pss
   };

   double bytesToMegabytes(uint64_t bytes);
   #ifdef _WIN32
   int getWin32MemoryUsage(MemoryUsageInfo& info);
   #elif __APPLE__
   int getMacMemoryUsage(MemoryUsageInfo& info);
   #endif

   int getMemoryUsage(MemoryUsageInfo &info)
   {
      #ifdef _WIN32
      return getWin32MemoryUsage(info);
      #elif __APPLE__
      return getMacMemoryUsage(info);
      #endif
   }

   #ifdef _WIN32
   #include "windows.h"
   #include "psapi.h"

   int getWin32MemoryUsage(MemoryUsageInfo& info)
   {
      PROCESS_MEMORY_COUNTERS pmc;
      HANDLE process = GetCurrentProcess();
      if (process == NULL)
      {
         return -1;
      }

      if (GetProcessMemoryInfo(process, &pmc, sizeof(PROCESS_MEMORY_COUNTERS)) != 0)
      {
         info.currentProcessMemoryUsage = bytesToMegabytes(pmc.WorkingSetSize);
         return 0;
      }
      else
      {
         return -1;
      }
   }

   #elif __APPLE__
   #include <mach/mach.h>
   #include <sys/types.h>
   #include <sys/sysctl.h>
   int getMacMemoryUsage(MemoryUsageInfo& info)
   {
      int ret;
      task_vm_info_data_t vmInfo;
      mach_msg_type_number_t count = TASK_VM_INFO_COUNT;
      if (task_info(mach_task_self(), TASK_VM_INFO, (task_info_t) &vmInfo, &count) == KERN_SUCCESS)
      {
         info.currentProcessMemoryUsage = bytesToMegabytes(vmInfo.phys_footprint); // pss
         ret = 0;
      }
      else
      {
         ret = -1;
      }

      return ret;
   }
   #endif

   double bytesToMegabytes(uint64_t bytes)
   {
      return (double)bytes/1000000;
   }
}
