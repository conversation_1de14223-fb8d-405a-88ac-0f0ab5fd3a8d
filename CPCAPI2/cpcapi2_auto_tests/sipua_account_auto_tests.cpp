#include "cpcapi2_test_fixture.h"
#include "test_account_events.h"
#include "test_events.h"
#include "test_call_events.h"
#include "sipua_alianza_api_test_fixture.h"
#include "sipua_alianza_api_test_events.h"
#include "sipua_alianza_api_test_helper.h"

#include "test_framework/http_test_framework.h"

#include "alianza_api/interface/public/alianza_api_handler.h"
#include "alianza_api/interface/public/alianza_api_types.h"
#include "alianza_api/interface/public/alianza_api_manager.h"

#include <sstream>

using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::SipAccount;
using namespace std::chrono;

#define GTEST_SKIP_REPRO() if (TestEnvironmentConfig::testEnvironmentId() == "repro") { GTEST_SKIP(); }

namespace
{
   class SipuaAccountModuleTest : public CpcapiAutoTest
   {
   public:
      SipuaAccountModuleTest() {}
      virtual ~SipuaAccountModuleTest() {}
   };

   TEST_F(SipuaAccountModuleTest, AccountNoInit)
   {
      TestAccount alice("alice", Account_NoInit);
   }

   TEST_F(SipuaAccountModuleTest, AccountInit)
   {
      TestAccount alice("alice", Account_Init);
   }

   TEST_F(SipuaAccountModuleTest, AccountEnableDisable)
   {
      TestAccount alice("alice", Account_NoInit);
      alice.enable();
      std::this_thread::sleep_for(std::chrono::milliseconds(3000));
      alice.disable();
   }

   TEST_F(SipuaAccountModuleTest, AccountEnableDisableAuto)
   {
      TestAccount alice("alice");
   }

   TEST_F(SipuaAccountModuleTest, MultipleRegisterUnregister)
   {
      TestAccount alice("alice", Account_Init);
      for (size_t i = 0; i < 5; i++)
      {
         alice.enable();
         alice.disable(false, true, false);
      }
   }

   TEST_F(SipuaAccountModuleTest, AccountEnableDisableWithExtensions)
   {
      GTEST_SKIP_REPRO();

      // TODO:
      // The creation of the mock test account is solely to create the backend accounts. This workaround is
      // required only for extension based dialing, as extension calling is only possibly between sipua
      // extensions belonging to the same account. This approach is a temporary workaround until the
      // ownership of the backend extension can be managed by the test account created to use the extension.
      TestAccount mock("mock", Account_NoInit);
      mock.config.alianzaSession.numberEnabled = false;
      std::shared_ptr<AlianzaSipUaInfo> bobUa = mock.config.alianzaSession.addUa(mock.config.alianzaConfig, "bob");
      std::shared_ptr<AlianzaSipUaInfo> maxUa = mock.config.alianzaSession.addUa(mock.config.alianzaConfig, "max");

      mock.init();
      ASSERT_NO_FATAL_FAILURE(mock.createAlianzaAccountInBackend());

      TestAccount bob("bob", Account_NoInit);
      bob.skipAutoCreateAlianzaAccountInBackend();
      TestAccount max("max", Account_NoInit);
      max.skipAutoCreateAlianzaAccountInBackend();

      bob.applyAlianzaExtensionUa(*bobUa, mock.config.alianzaSession);
      max.applyAlianzaExtensionUa(*maxUa, mock.config.alianzaSession);

      bob.init();
      max.init();

      // we need to manually start alianzaApiManager and trigger authentication so bob and max's
      // alianzaApiManager instances have an auth token for when they go to check their registration
      // status with the platform in enable().
      // Ideally this would be managed by the test framework instead
      bob.alianzaApiManager->start(mock.config.alianzaConfig.api);
      bob.alianzaApiManager->authorize();
      assertAuthorizationSuccess(bob.alianzaApiEvents);

      max.alianzaApiManager->start(mock.config.alianzaConfig.api);
      max.alianzaApiManager->authorize();
      assertAuthorizationSuccess(max.alianzaApiEvents);

      bob.enable();
      max.enable();

      std::this_thread::sleep_for(std::chrono::milliseconds(3000));
      bob.disable();
      max.disable();

      std::this_thread::sleep_for(std::chrono::milliseconds(3000));
      ASSERT_NO_FATAL_FAILURE(mock.destroyAlianzaAccountInBackend());
   }
}

