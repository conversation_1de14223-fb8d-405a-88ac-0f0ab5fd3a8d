#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#if (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1 && CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)

#if defined(__GNUC__) || defined(__clang__)
#pragma GCC diagnostic ignored "-Wdeprecated-declarations"
#endif

#include "cpcapi2_test_fixture.h"
#include "test_call_events.h"
#include "test_events.h"
#include "test_account_events.h"

#include "impl/call/SipConversationHandlerInternal.h"
#include "impl/remotesync/RemoteSyncHandlerAdapter.h"
#include "interface/experimental/remotesync/RemoteSyncJsonApi.h"
#include "interface/experimental/remotesync/RemoteSyncHandler.h"
#include "impl/remotesync/jsonapi/RemoteSyncJsonServerInterface.h"
#include "../../impl/auth_server/AuthServerJwtUtils.h"

using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::SipConversation;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::JsonApi;

class JsonApiCallTests : public CpcapiAutoTest
{
public:
   JsonApiCallTests() {}
   virtual ~JsonApiCallTests() {}
};

static void generateJwt(const resip::Data& p8file, const resip::Data& userIdentity, resip::Data& jwt)
{
   std::map<resip::Data, resip::Data> pubClaims;
   pubClaims["cp_user"] = userIdentity;
   CPCAPI2::AuthServer::JwtUtils::GenerateJWT(p8file, "CPCAPI2::AuthServer", pubClaims, 86400, jwt);
}

TEST_F(JsonApiCallTests, BasicCall) {
   const int call_duration_ms = 10000;

   // Max is the remote SDK
   TestAccount max("max", Account_Init);
   JsonApiServerConfig maxJsonApiConfig;
   maxJsonApiConfig.certificateFilePath = TestEnvironmentConfig::testResourcePath() + "p256-public-key-unit-tests.spki";
   maxJsonApiConfig.wssCertificateFilePath = TestEnvironmentConfig::testResourcePath() + "cert.pem";
   maxJsonApiConfig.wssPrivateKeyFilePath = TestEnvironmentConfig::testResourcePath() + "privkey.pem";
   maxJsonApiConfig.wssDiffieHellmanParamsFilePath = TestEnvironmentConfig::testResourcePath() + "dh2048.pem";
   max.jsonApiServer->start(maxJsonApiConfig);
   CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(max.phone)->setJsonApiServer(max.jsonApiServer);
   CPCAPI2::SipAccount::SipAccountJsonApi::getInterface(max.phone);
   CPCAPI2::SipConversation::SipConversationJsonApi::getInterface(max.phone);   

   // Alice is a client that uses the remote SDK
   TestAccount alice("alice", Account_Init);
   JsonApiClientSettings jsonApiClientSettings;
   jsonApiClientSettings.serverUri = "wss://localhost:9003";
   jsonApiClientSettings.ignoreCertVerification = true;
   alice.jsonApiClient->configureDefaultSettings(jsonApiClientSettings);
   alice.jsonApiClient->enable();
   {
      JsonApiConnectionHandle h;
      StatusChangedEvent args;
      cpcExpectEvent(alice.jsonApiClientEvents, "JsonApiClientHandler::onStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), h, args);
      cpcExpectEvent(alice.jsonApiClientEvents, "JsonApiClientHandler::onStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), h, args);
   }

   resip::Data aliceJwt;
   generateJwt((TestEnvironmentConfig::testResourcePath() + "p256-private-key-unit-tests.p8").c_str(), "alice", aliceJwt);
   alice.jsonApiClient->login(aliceJwt.c_str());

   {
      JsonApiUserHandle jsonApiUser;
      NewLoginEvent args;

      // Max has to process the login attempt (associate the context with an SDK instance)
      cpcExpectEvent(max.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 5000, AlwaysTruePred(), jsonApiUser, args);
      cpc::vector<cpc::string> permissions; permissions.push_back("*");
      max.jsonApiServer->setJsonApiUserContext(jsonApiUser, max.phone, permissions);
      LoginResultEvent loginResult;
      loginResult.success = true;
      max.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);
   }
   {
      // Alice then gets the result
      JsonApiLoginHandle h;
      LoginResultEvent args;
      cpcExpectEvent(alice.jsonApiClientEvents, "JsonApiClientHandler::onLoginResult", 50000, AlwaysTruePred(), h, args);
   }

   // Alice is now logged in to the Remote SDK.
   // Alice creates a SIP account using the Remote SDK and enables it (Remote SDK sends REGISTER).
   alice.handle = alice.sipAccountJsonProxy->create(alice.config.settings);
   alice.sipAccountJsonProxy->setHandler(alice.handle, (SipAccountHandler*)0xDEADBEEF);
   alice.sipConvJsonProxy->setHandler(alice.handle, (SipConversationHandler*)0xDEADBEEF);
   alice.sipAccountJsonProxy->enable(alice.handle);
   {
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
         cpcExpectEvent(alice.sipAccountJsonProxyEvents, "SipAccountHandler::onAccountStatusChanged", 20000, AlwaysTruePred(), h, evt);
         ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);
      }
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
         cpcExpectEvent(alice.sipAccountJsonProxyEvents, "SipAccountHandler::onAccountStatusChanged", 20000, AlwaysTruePred(), h, evt);
         ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
      }   
   }

   // Bob is a regular endpoint with his own local SDK (just used so we have someone to make a call to)
   TestAccount bob("bob");

   // make an outgoing (audio only) call from Alice to Bob
   SipConversationHandle aliceCall = alice.sipConvJsonProxy->createConversation(alice.handle);
   alice.sipConvJsonProxy->addParticipant(aliceCall, bob.config.uri());
   alice.sipConvJsonProxy->start(aliceCall);

   // Overview of Bob's thread:
   //  - wait for onNewConversation (triggered when Bob gets the incoming INVITE)
   //  - send 180 ringing
   //  - wait for onConversationStateChanged (LocalRinging)
   //  - answer the call (200 OK)
   //  - wait for onConversationMediaChanged (Audio -> SendReceive)
   //  - wait for onConversationStateChanged (Connected)
   //  - end the call (BYE)
   //  - wait for onConversationEnded
   auto bobEvents = std::async(std::launch::async, [&]() {
      SipConversationHandle bobCall = 0;
      {
         SipConversationHandle h;
         NewConversationEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
            "SipConversationHandler::onNewConversation",
            15000,
            AlwaysTruePred(),
            h, evt));
         bobCall = h;
         ASSERT_EQ(evt.account, bob.handle);
         ASSERT_EQ(evt.conversationState, ConversationState_RemoteOriginated);
         ASSERT_EQ(evt.conversationType, ConversationType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
         //ASSERT_EQ(evt.remoteDisplayName, alice.config.name);
         ASSERT_EQ(evt.remoteMediaInfo.size(), 1);
      }

      // Bob sends 180 ringing -- this should trigger state transitions for both Bob and Alice
      ASSERT_EQ(bob.conversation->sendRingingResponse(bobCall), kSuccess);

      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
            "SipConversationHandler::onConversationStateChanged",
            5000,
            HandleEqualsPred<SipConversationHandle>(bobCall),
            h, evt));
         ASSERT_EQ(evt.conversationState, ConversationState_LocalRinging);
      }

      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      ASSERT_EQ(bob.conversation->accept(bobCall), kSuccess);

      {
         SipConversationHandle h;
         ConversationMediaChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
            "SipConversationHandler::onConversationMediaChanged",
            15000,
            HandleEqualsPred<SipConversationHandle>(bobCall),
            h, evt));
         ASSERT_EQ(evt.localMediaInfo.size(), 1);
         ASSERT_EQ(evt.localMediaInfo[0].mediaType, MediaType_Audio);
         ASSERT_EQ(evt.localMediaInfo[0].mediaDirection, MediaDirection_SendReceive);
         std::string strLocalPlname(evt.localMediaInfo[0].audioCodec.plname);
         //ASSERT_EQ(strLocalPlname, "opus");
         std::string strRemotePlname(evt.remoteMediaInfo[0].audioCodec.plname);
         //ASSERT_EQ(strRemotePlname, "opus");
      }

      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
            "SipConversationHandler::onConversationStateChanged",
            5000,
            HandleEqualsPred<SipConversationHandle>(bobCall),
            h, evt));
         ASSERT_EQ(evt.conversationState, ConversationState_Connected);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(call_duration_ms));

      ASSERT_EQ(bob.conversation->end(bobCall), kSuccess);

      {
         SipConversationHandle h;
         ConversationEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
            "SipConversationHandler::onConversationEnded",
            5000,
            HandleEqualsPred<SipConversationHandle>(bobCall),
            h, evt));
         ASSERT_EQ(evt.endReason, ConversationEndReason_UserTerminatedLocally);
      }
   });

   // Overview of Alice's thread:
   //  - wait for onNewConversation (triggered as soon as Alice sends the INVITE)
   //  - wait for onConversationStateChanged (RemoteRinging) (triggered when Alice receives the 180 from Bob)
   //  - wait for onConversationMediaChanged (Audio -> SendReceive) (triggered when Alice receives the 200 OK from Bob)
   //  - wait for onConversationStateChanged (Connected) (also triggered when Alice receives the 200 OK from Bob)
   //  - wait for onConversationEnded (triggered when Alice receives the BYE from Bob)
   auto aliceEvents = std::async(std::launch::async, [&]() {
      SipConversationState prevState;

      {
         SipConversationHandle h;
         NewConversationEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.sipAccountJsonProxyEvents,
            "SipConversationHandler::onNewConversation",
            15000,
            HandleEqualsPred<SipConversationHandle>(aliceCall),
            h, evt));
         ASSERT_EQ(evt.account, alice.handle);
         ASSERT_EQ(evt.conversationType, ConversationType_Outgoing);
         ASSERT_EQ(evt.remoteAddress, bob.config.uri());
         ASSERT_EQ(evt.remoteDisplayName, "");
         ASSERT_EQ(evt.localMediaInfo.size(), 1);
      }

      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.sipAccountJsonProxyEvents,
            "SipConversationHandler::onConversationStateChanged",
            5000,
            HandleEqualsPred<SipConversationHandle>(aliceCall),
            h, evt));
         ASSERT_EQ(evt.conversationState, ConversationState_RemoteRinging);
      }

      {
         SipConversationHandle h;
         ConversationMediaChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.sipAccountJsonProxyEvents,
            "SipConversationHandler::onConversationMediaChanged",
            5000,
            HandleEqualsPred<SipConversationHandle>(aliceCall),
            h, evt));
         ASSERT_EQ(evt.localMediaInfo.size(), 1);
         ASSERT_EQ(evt.localMediaInfo[0].mediaType, MediaType_Audio);
         ASSERT_EQ(evt.localMediaInfo[0].mediaDirection, MediaDirection_SendReceive);
      }

      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.sipAccountJsonProxyEvents,
            "SipConversationHandler::onConversationStateChanged",
            5000,
            HandleEqualsPred<SipConversationHandle>(aliceCall),
            h, evt));
         ASSERT_EQ(evt.conversationState, ConversationState_Connected);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(call_duration_ms));

      {
         SipConversationHandle h;
         ConversationEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.sipAccountJsonProxyEvents,
            "SipConversationHandler::onConversationEnded",
            call_duration_ms + 1000,
            HandleEqualsPred<SipConversationHandle>(aliceCall),
            h, evt));
         ASSERT_EQ(evt.endReason, ConversationEndReason_UserTerminatedRemotely);
      }
   });

   waitFor2(bobEvents, aliceEvents);

   max.jsonApiServer->shutdown();

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

}

void doRemoteSDKLoginSequence(TestAccount* testAcctClient, TestAccount* testAcctServer, TestAccount* testAcctRemoteSdkContext)
{
   testAcctClient->jsonApiClient->connect("ws://localhost:9003");
   {
      JsonApiConnectionHandle h;
      ConnectResultEvent args;
      ASSERT_TRUE(cpcExpectEvent(testAcctClient->jsonApiClientEvents, "JsonApiClientHandler::onConnectResult", 5000, AlwaysTruePred(), h, args));
   }

   testAcctClient->jsonApiClient->login("{\
                                          \"authProvider\" : \"fake\",\
                                          \"username\" : \"alice@localhost\",\
                                          \"password\" : \"foo\"\
                                          }");
   {
      JsonApiUserHandle jsonApiUser;
      NewLoginEvent args;
      ASSERT_TRUE(cpcExpectEvent(testAcctServer->jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 5000, AlwaysTruePred(), jsonApiUser, args));
      cpc::vector<cpc::string> permissions; permissions.push_back("*");
      testAcctServer->jsonApiServer->setJsonApiUserContext(jsonApiUser, testAcctRemoteSdkContext->phone, permissions);
      LoginResultEvent loginResult;
      loginResult.success = true;
      testAcctServer->jsonApiServer->sendLoginResult(jsonApiUser, loginResult);
   }
   {
      JsonApiLoginHandle h;
      LoginResultEvent args;
      ASSERT_TRUE(cpcExpectEvent(testAcctClient->jsonApiClientEvents, "JsonApiClientHandler::onLoginResult", 5000, AlwaysTruePred(), h, args));
   }
}

class MySipConversationHandler : public CPCAPI2::EventSyncHandler<CPCAPI2::SipConversation::SipConversationHandlerInternal>
{
public:
   MySipConversationHandler() : mConvHandle(-1) {}
   virtual ~MySipConversationHandler() {}

   // Inherited via SipConversationHandlerInternal
   virtual int onNewConversation(SipConversationHandle conversation, const NewConversationEvent & args) override
   {
      resip::Lock lck(mMtx);
      mConvHandle = conversation;
      safeCout("MySipConversationHandler::onNewConversation(): conversation handle: " << mConvHandle);
      mNewConversationCond.signal();
      return 0;
   }

   virtual int onConversationEnded(SipConversationHandle conversation, const ConversationEndedEvent & args) override
   {
      safeCout("MySipConversationHandler::onConversationEnded(): conversation handle: " << mConvHandle);
      return 0;
   }

   virtual int onIncomingTransferRequest(SipConversationHandle conversation, const TransferRequestEvent & args) override
   {
      safeCout("MySipConversationHandler::onIncomingTransferRequest(): conversation handle: " << mConvHandle);
      return 0;
   }

   virtual int onIncomingRedirectRequest(SipConversationHandle conversation, const RedirectRequestEvent & args) override
   {
      safeCout("MySipConversationHandler::onIncomingRedirectRequest(): conversation handle: " << mConvHandle);
      return 0;
   }

   virtual int onIncomingTargetChangeRequest(SipConversationHandle conversation, const TargetChangeRequestEvent & args) override
   {
      safeCout("MySipConversationHandler::onIncomingTargetChangeRequest(): conversation handle: " << mConvHandle);
      return 0;
   }

   virtual int onIncomingHangupRequest(SipConversationHandle conversation, const HangupRequestEvent & args) override
   {
      safeCout("MySipConversationHandler::onIncomingHangupRequest(): conversation handle: " << mConvHandle);
      return 0;
   }

   virtual int onIncomingBroadsoftTalkRequest(SipConversationHandle conversation, const BroadsoftTalkEvent & args) override
   {
      safeCout("MySipConversationHandler::onIncomingBroadsoftTalkRequest(): conversation handle: " << mConvHandle);
      return 0;
   }

   virtual int onIncomingBroadsoftHoldRequest(SipConversationHandle conversation, const BroadsoftHoldEvent & args) override
   {
      safeCout("MySipConversationHandler::onIncomingBroadsoftHoldRequest(): conversation handle: " << mConvHandle);
      return 0;
   }

   virtual int onTransferProgress(SipConversationHandle conversation, const TransferProgressEvent & args) override
   {
      safeCout("MySipConversationHandler::onTransferProgress(): conversation handle: " << mConvHandle);
      return 0;
   }

   virtual int onConversationStateChangeRequest(SipConversationHandle conversation, const ConversationStateChangeRequestEvent & args) override
   {
      safeCout("MySipConversationHandler::onConversationStateChangeRequest(): conversation handle: " << mConvHandle);
      return 0;
   }

   virtual int onConversationStateChanged(SipConversationHandle conversation, const ConversationStateChangedEvent & args) override
   {
      safeCout("MySipConversationHandler::onConversationStateChanged(): conversation handle: " << mConvHandle);
      return 0;
   }

   virtual int onConversationMediaChangeRequest(SipConversationHandle conversation, const ConversationMediaChangeRequestEvent & args) override
   {
      safeCout("MySipConversationHandler::onConversationMediaChangeRequest(): conversation handle: " << mConvHandle);
      return 0;
   }

   virtual int onConversationMediaChanged(SipConversationHandle conversation, const ConversationMediaChangedEvent & args) override
   {
      safeCout("MySipConversationHandler::onConversationMediaChanged(): conversation handle: " << mConvHandle);
      return 0;
   }

   virtual int onConversationStatisticsUpdated(SipConversationHandle conversation, const ConversationStatisticsUpdatedEvent & args) override
   {
      safeCout("MySipConversationHandler::onConversationStatisticsUpdated(): conversation handle: " << mConvHandle);
      return 0;
   }

   virtual int onError(SipConversationHandle conversation, const CPCAPI2::SipConversation::ErrorEvent & args) override
   {
      safeCout("MySipConversationHandler::onError(): conversation handle: " << mConvHandle);
      return 0;
   }

   virtual int onConversationInitiated(SipConversationHandle conversation, const ConversationInitiatedEvent & args) override
   {
      safeCout("MySipConversationHandler::onConversationInitiated(): conversation handle: " << mConvHandle);
      return 0;
   }

   SipConversationHandle mConvHandle;
   resip::Condition mNewConversationCond;
   resip::Mutex mMtx;

};

class MyRemoteSyncHandler : public CPCAPI2::RemoteSync::RemoteSyncHandlerAdapter
{
public:
   MyRemoteSyncHandler() : mSessionHandle(-1) {}
   virtual~ MyRemoteSyncHandler() {}
   
   virtual int onSetAccounts(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::SetAccountsEvent& evt) OVERRIDE
   {
      safeCout("MyRemoteSyncHandler::onSetAccounts(): session handle: " << sessionHandle);
      
      resip::Lock lock(mMutex);
      mSessionHandle = sessionHandle;
      mCond.signal();
      return kSuccess;
   }
   
   virtual int onNotificationUpdate(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::NotificationUpdateEvent& evt) OVERRIDE
   {
      safeCout("MyRemoteSyncHandler::onNotificationUpdate(): session handle: " << sessionHandle);
      return kSuccess;
   }
   
   virtual int onMessageReactions(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::MessageReactionsEvent& evt) OVERRIDE
   {
      safeCout("MyRemoteSyncHandler::onMessageReactions(): session handle: " << sessionHandle);
      return kSuccess;
   }
   
   virtual int onFetchMessagesReactionsComplete( const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::FetchMessagesReactionsCompleteEvent& evt )
   {
      safeCout("MyRemoteSyncHandler::onFetchMessagesReactionsComplete(): session handle: " << sessionHandle);
      return kSuccess;
   }

   virtual int onSyncItemsComplete(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::SyncItemsCompleteEvent& evt) OVERRIDE
   {
      safeCout("MyRemoteSyncHandler::onSyncItemsComplete(): session handle: " << sessionHandle);
      return kSuccess;
   }
   
   virtual int onUpdateItemComplete(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::UpdateItemCompleteEvent& evt) OVERRIDE
   {
      safeCout("MyRemoteSyncHandler::onUpdateItemComplete(): session handle: " << sessionHandle);
      return kSuccess;
   }
   
   virtual int onFetchRangeComplete(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::FetchRangeCompleteEvent& evt) OVERRIDE
   {
      safeCout("MyRemoteSyncHandler::onFetchRangeComplete(): session handle: " << sessionHandle);
      return kSuccess;
   }
   
   virtual int onFetchConversationsComplete(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::FetchConversationsCompleteEvent& evt) OVERRIDE
   {
      safeCout("MyRemoteSyncHandler::onFetchConversationsComplete(): session handle: " << sessionHandle);
      return kSuccess;
   }
   
   virtual int onConversationUpdated(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::ConversationUpdatedEvent& evt) OVERRIDE
   {
      safeCout("MyRemoteSyncHandler::onConversationUpdated(): session handle: " << sessionHandle);
      return kSuccess;
   }
   
   virtual int onMessageCount(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::MessageCountEvent& evt) OVERRIDE
   {
      safeCout("MyRemoteSyncHandler::onMessageCount(): session handle: " << sessionHandle);
      return kSuccess;
   }
   
   virtual int onError(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::OnErrorEvent& evt) OVERRIDE
   {
      safeCout("MyRemoteSyncHandler::onError(): session handle: " << sessionHandle);
      return kSuccess;
   }
   
   virtual int onConnectionState( const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::OnConnectionStateEvent& evt ) OVERRIDE
   {
      safeCout("MyRemoteSyncHandler::onConnectionState(): session handle: " << sessionHandle);
      return kSuccess;
   }
   
   virtual int onTimestampDelta( const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::OnTimestampDeltaEvent& evt ) OVERRIDE
   {
      safeCout("MyRemoteSyncHandler::onTimestampDelta(): session handle: " << sessionHandle);
      return kSuccess;
   }
   
   CPCAPI2::RemoteSync::SessionHandle mSessionHandle;
   resip::Condition mCond;
   resip::Mutex mMutex;
};

// .jza. -- commented out for now; accessed SDK impl files and fails due to
// header search paths of the auto tests not setup to expect internal SDK includes.

//TEST_F(JsonApiCallTests, DISABLED_IncomingCallPush) {
//   const int call_duration_ms = 10000;
//
//   // Max is the SDK server
//   TestAccount max("max", Account_Init);
//   max.jsonApiServer->start();
//
//   // AliceRemote is Alice's SDK instance that runs remotely (served by Max)
//   TestAccount aliceRemote("alice", Account_NoInit);
//   aliceRemote.config.useFileAudioDevice = true;
//   aliceRemote.init();
//   CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(aliceRemote.phone)->setJsonApiServer(max.jsonApiServer);
//   CPCAPI2::SipAccount::SipAccountJsonApi::getInterface(aliceRemote.phone);
//   CPCAPI2::SipConversation::SipConversationJsonApi::getInterface(aliceRemote.phone);
//
//   SipConversationManager* aliceRemoteSipConvMgr = CPCAPI2::SipConversation::SipConversationManager::getInterface(aliceRemote.phone);
//   SipAVConversationManagerInterface* aliceRemoteSipConvMgrIf = dynamic_cast<SipAVConversationManagerInterface*>(aliceRemoteSipConvMgr);
//   std::unique_ptr<MySipConversationHandler> aliceRemoteConvObserver(new MySipConversationHandler);
//   aliceRemoteSipConvMgrIf->addSdkObserver(aliceRemoteConvObserver.get());
//
//   // Alice is a client that uses the remote SDK
//   std::unique_ptr<TestAccount> alice(new TestAccount("alice", Account_Init, false));
//   alice->sipConvJsonProxy->setStateHandler((SipConversationJsonProxyStateHandler*)0xDEADBEEF);
//   cpc::string aliceUsername = alice->config.settings.username;
//   ASSERT_NO_FATAL_FAILURE(doRemoteSDKLoginSequence(alice.get(), &max, &aliceRemote));
//
//   // *** Alice is now logged in to the Remote SDK ***
//
//   // Alice creates a SIP account using the Remote SDK and enables it (Remote SDK sends REGISTER).
//   alice->handle = alice->sipAccountJsonProxy->create(alice->config.settings);
//   alice->sipAccountJsonProxy->setHandler(alice->handle, (SipAccountHandler*)0xDEADBEEF);
//   alice->sipConvJsonProxy->setHandler(alice->handle, (SipConversationHandler*)0xDEADBEEF);
//   alice->sipAccountJsonProxy->enable(alice->handle);
//   SipAccountHandle aliceAcct = 0;
//   {
//      {
//         CPCAPI2::SipAccount::SipAccountHandle h;
//         CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
//         cpcExpectEvent(alice->sipAccountJsonProxyEvents, "SipAccountHandler::onAccountStatusChanged", 20000, AlwaysTruePred(), h, evt);
//         ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);
//      }
//      {
//         CPCAPI2::SipAccount::SipAccountHandle h;
//         CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
//         cpcExpectEvent(alice->sipAccountJsonProxyEvents, "SipAccountHandler::onAccountStatusChanged", 20000, AlwaysTruePred(), h, evt);
//         ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
//         aliceAcct = h;
//      }
//   }
//
//   // Simulate Alice getting suspended by the OS;
//   // Alice remains registered with the SIP server via her Remote SDK instance (i.e. Max)
//   alice->jsonApiClient->shutdown();
//   alice.reset(new TestAccount("alice", Account_Init));
//   alice->sipConvJsonProxy->setStateHandler((SipConversationJsonProxyStateHandler*)0xDEADBEEF);
//   // the unit test framework assumes we want different (randomly generated) user ids each time we create a TestAccount,
//   // but here we want it to be the same one as before
//   alice->config.settings.username = aliceUsername;
//
//   // Bob is a regular endpoint with his own local SDK (just used so we have someone to make a call to)
//   TestAccount bob("bob");
//
//   // make an outgoing (audio only) call from Bob to Alice
//   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
//   bob.conversation->addParticipant(bobCall, alice->config.uri());
//   bob.conversation->start(bobCall);
//
//   // Since Alice is suspended, AliceRemote handles the incoming call from Bob on her behalf
//   SipConversationHandle maxCall = 0;
//   {
//      resip::Lock lck(aliceRemoteConvObserver->mMtx);
//      aliceRemoteConvObserver->mNewConversationCond.wait(aliceRemoteConvObserver->mMtx);
//      maxCall = aliceRemoteConvObserver->mConvHandle;
//   }
//
//   // AliceRemote sends 180 ringing on behalf of Alice
//   ASSERT_EQ(aliceRemote.conversation->sendRingingResponse(maxCall), kSuccess);
//   aliceRemote.events->processNonUnitTestEvents(5000);
//
//   // *** A push request would normally be sent at this point to wake poor Alice up ***
//
//   auto aliceEvents = std::async(std::launch::async, [&]() {
//      // Alice gets the push request, wakes up, and logs back in to the Remote SDK
//      ASSERT_NO_FATAL_FAILURE(doRemoteSDKLoginSequence(alice.get(), &max, &aliceRemote));
//
//      // *** Alice is now (once again) logged in to the Remote SDK ***
//
//      // this generates a PhoneHandler::onError, but we ignore it
//      alice->sipConvJsonProxy->setHandler(aliceAcct, (SipConversationHandler*)0xDEADBEEF);
//
//      // Alice does state synchronization
//      SipConversationHandle aliceCall = 0;
//      alice->sipConvJsonProxy->requestStateAllConversations();
//      aliceRemote.events->processNonUnitTestEvents(5000);
//      {
//         JsonApiConnectionHandle h;
//         JsonProxyConversationStateEvent evt;
//         ASSERT_TRUE(cpcExpectEvent(alice->sipAccountJsonProxyEvents,
//            "SipConversationJsonProxyStateHandler::onConversationState",
//            5000,
//            AlwaysTruePred(),
//            h, evt));
//         ASSERT_EQ(evt.conversationState.size(), 1);
//         ASSERT_EQ(evt.conversationState[0].conversationState, ConversationState_LocalRinging); // Max sent 180 Ringing on behalf of Alice earlier
//         ASSERT_EQ(evt.conversationState[0].remoteAddress, bob.config.uri());
//         aliceCall = evt.conversationState[0].conversation;
//      }
//
//      // Alice sets up media between herself and her Remote SDK
//      alice->sipConvJsonProxy->joinRemoteConversation(aliceCall, RemoteMediaMode_Bypass);
//
//      // TODO: there should be an event to indicate that media is set up
//      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
//
//      // Alice answers the call (200 OK)
//      ASSERT_EQ(alice->sipConvJsonProxy->accept(aliceCall), kSuccess);
//
//      {
//         SipConversationHandle h;
//         ConversationMediaChangedEvent evt;
//         ASSERT_TRUE(cpcExpectEvent(alice->sipAccountJsonProxyEvents,
//            "SipConversationHandler::onConversationMediaChanged",
//            15000,
//            HandleEqualsPred<SipConversationHandle>(aliceCall),
//            h, evt));
//         ASSERT_EQ(evt.localMediaInfo.size(), 1);
//         ASSERT_EQ(evt.localMediaInfo[0].mediaType, MediaType_Audio);
//         ASSERT_EQ(evt.localMediaInfo[0].mediaDirection, MediaDirection_SendReceive);
//         std::string strLocalPlname(evt.localMediaInfo[0].audioCodec.plname);
//         //ASSERT_EQ(strLocalPlname, "opus");
//         std::string strRemotePlname(evt.remoteMediaInfo[0].audioCodec.plname);
//         //ASSERT_EQ(strRemotePlname, "opus");
//      }
//
//      {
//         SipConversationHandle h;
//         ConversationStateChangedEvent evt;
//         ASSERT_TRUE(cpcExpectEvent(alice->sipAccountJsonProxyEvents,
//            "SipConversationHandler::onConversationStateChanged",
//            5000,
//            HandleEqualsPred<SipConversationHandle>(aliceCall),
//            h, evt));
//         ASSERT_EQ(evt.conversationState, ConversationState_Connected);
//      }
//
//      std::this_thread::sleep_for(std::chrono::milliseconds(call_duration_ms));
//
//      ASSERT_EQ(alice->sipConvJsonProxy->end(aliceCall), kSuccess);
//
//      {
//         SipConversationHandle h;
//         ConversationEndedEvent evt;
//         ASSERT_TRUE(cpcExpectEvent(alice->sipAccountJsonProxyEvents,
//            "SipConversationHandler::onConversationEnded",
//            5000,
//            HandleEqualsPred<SipConversationHandle>(aliceCall),
//            h, evt));
//         ASSERT_EQ(evt.endReason, ConversationEndReason_UserTerminatedLocally);
//      }
//   });
//
//   auto bobEvents = std::async(std::launch::async, [&]() {
//      SipConversationState prevState;
//
//      {
//         SipConversationHandle h;
//         NewConversationEvent evt;
//         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
//            "SipConversationHandler::onNewConversation",
//            15000,
//            HandleEqualsPred<SipConversationHandle>(bobCall),
//            h, evt));
//         ASSERT_EQ(evt.account, alice->handle);
//         ASSERT_EQ(evt.conversationType, ConversationType_Outgoing);
//         ASSERT_EQ(evt.remoteAddress, alice->config.uri());
//         ASSERT_EQ(evt.remoteDisplayName, "");
//         ASSERT_EQ(evt.localMediaInfo.size(), 1);
//      }
//
//      {
//         SipConversationHandle h;
//         ConversationStateChangedEvent evt;
//         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
//            "SipConversationHandler::onConversationStateChanged",
//            5000,
//            HandleEqualsPred<SipConversationHandle>(bobCall),
//            h, evt));
//         ASSERT_EQ(evt.conversationState, ConversationState_RemoteRinging);
//      }
//
//      {
//         SipConversationHandle h;
//         ConversationMediaChangedEvent evt;
//         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
//            "SipConversationHandler::onConversationMediaChanged",
//            25000,
//            HandleEqualsPred<SipConversationHandle>(bobCall),
//            h, evt));
//         ASSERT_EQ(evt.localMediaInfo.size(), 1);
//         ASSERT_EQ(evt.localMediaInfo[0].mediaType, MediaType_Audio);
//         ASSERT_EQ(evt.localMediaInfo[0].mediaDirection, MediaDirection_SendReceive);
//      }
//
//      {
//         SipConversationHandle h;
//         ConversationStateChangedEvent evt;
//         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
//            "SipConversationHandler::onConversationStateChanged",
//            5000,
//            HandleEqualsPred<SipConversationHandle>(bobCall),
//            h, evt));
//         ASSERT_EQ(evt.conversationState, ConversationState_Connected);
//      }
//
//      std::this_thread::sleep_for(std::chrono::milliseconds(call_duration_ms));
//
//      {
//         SipConversationHandle h;
//         ConversationEndedEvent evt;
//         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
//            "SipConversationHandler::onConversationEnded",
//            call_duration_ms + 1000,
//            HandleEqualsPred<SipConversationHandle>(bobCall),
//            h, evt));
//         ASSERT_EQ(evt.endReason, ConversationEndReason_UserTerminatedRemotely);
//      }
//   });
//
//   waitFor2(bobEvents, aliceEvents);
//
//   max.jsonApiServer->shutdown();
//
//   std::this_thread::sleep_for(std::chrono::milliseconds(5000));
//
//}

TEST_F(JsonApiCallTests, DISABLED_RemoteSyncLogin)
{
   const int session_duration_ms = 10000;
   
   // Max is the SDK server
   TestAccount max("max", Account_Init);
   max.jsonApiServer->start();
   
   // AliceRemote is Alice's SDK instance that runs remotely (served by Max)
   TestAccount aliceRemote("alice", Account_NoInit);
   aliceRemote.init();
   CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(aliceRemote.phone)->setJsonApiServer(max.jsonApiServer);
   CPCAPI2::SipAccount::SipAccountJsonApi::getInterface(aliceRemote.phone);
   CPCAPI2::RemoteSync::RemoteSyncJsonApi::getInterface(aliceRemote.phone);
   
   CPCAPI2::RemoteSync::RemoteSyncManager* aliceRemoteSyncMgr = CPCAPI2::RemoteSync::RemoteSyncManager::getInterface(aliceRemote.phone);
   std::unique_ptr<MyRemoteSyncHandler> aliceRemoteSyncObserver(new MyRemoteSyncHandler);
   aliceRemoteSyncMgr->setHandler(aliceRemote.remoteSyncSession, (CPCAPI2::RemoteSync::RemoteSyncHandler*)aliceRemoteSyncObserver.get());

   // Alice is a client that uses the remote SDK
   std::unique_ptr<TestAccount> alice(new TestAccount("alice", Account_Init, false));
   cpc::string aliceUsername = alice->config.settings.username;
   ASSERT_NO_FATAL_FAILURE(doRemoteSDKLoginSequence(alice.get(), &max, &aliceRemote));
   
   // Alice is now logged in to the Remote SDK
   alice->remoteSyncJsonProxySession = alice->remoteSyncJsonProxy->create();
   alice->remoteSyncJsonProxy->setHandler(alice->remoteSyncJsonProxySession, (CPCAPI2::RemoteSync::RemoteSyncHandler*)0xDEADBEEF);
   
   const cpc::string host("127.0.0.1");
   const int port(8989);
   const cpc::string group("group1.1" );
   const cpc::string password("user0001@group1.1:1234");
   const cpc::string user1("user0001@group1.1");
   const cpc::string user2("user0002@group1.1");
   
   cpc::string wsURL;
   wsURL  = "ws://";
   wsURL += host;
   wsURL += ":";
   wsURL += std::to_string(port).c_str();
   wsURL += "/sync/sock/";
   wsURL += group;
   wsURL += "/";
   wsURL += user1;
   
   CPCAPI2::RemoteSync::RemoteSyncSettings settings;
   settings.wsSettings.certMode = WebSocket::CertVerificationMode_None;
   settings.wsSettings.webSocketURL = wsURL;
   settings.password = password;
   settings.accounts.push_back(user1);
   alice->remoteSyncJsonProxy->configureSettings(alice->remoteSyncJsonProxySession, settings);
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice->remoteSyncJsonProxy->connect(alice->remoteSyncJsonProxySession);
   
   CPCAPI2::RemoteSync::SessionHandle h;
   CPCAPI2::RemoteSync::OnErrorEvent evt;
   ASSERT_TRUE(cpcWaitForEvent(alice->remoteSyncJsonProxyEvents, "RemoteSyncHandler::onError", 10000, AlwaysTruePred(), h, evt));
   
   max.jsonApiServer->shutdown();
   
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));
}

#endif
