#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#if (CPCAPI2_BRAND_WEB_CALL_MODULE == 1)
#include "cpcapi2_test_fixture.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>
#include <resip/recon/AVOfferAnswerSession.hxx>

#include "test_framework/cpcapi2_test_framework.h"
#include "test_framework/bare_bones_sip_endpoint.h"

#include "impl/media/MediaManagerInterface.h"
#include "impl/phone/PhoneInterface.h"
#include "../shared/webrtc_recon/MediaStackImpl.hxx"

#include "impl/util/IpHelpers.h"

#include <thread>
#include <future>

using namespace CPCAPI2;
using namespace CPCAPI2::SipEvent;
using namespace CPCAPI2::SipPresence;
using namespace CPCAPI2::test;
using namespace resip;
using namespace recon;

namespace {

class AVOfferAnswerSessionTest : public CpcapiAutoTest
{
public:
   AVOfferAnswerSessionTest() {}
   virtual ~AVOfferAnswerSessionTest() {}
};

class SdpReadyHandler : public recon::AVOfferAnswerSession::SdpReady
{
public:
   SdpReadyHandler() {}
   virtual ~SdpReadyHandler() {}

   virtual void onSdpReady()
   {
      std::unique_lock<std::mutex> l(mWaitMutex);
      mWait.notify_one();
   }

   void wait()
   {
      //for (int i=0; i<5; i++)
      {
         std::unique_lock<std::mutex> l(mWaitMutex);
         if (mWait.wait_for(l, std::chrono::milliseconds(10000)) == std::cv_status::no_timeout)
         {
            return;
         }
      }
   }

   std::condition_variable mWait;
   std::mutex mWaitMutex;
};

TEST_F(AVOfferAnswerSessionTest, BasicOfferAnswerSession) {
   TestAccount alice("alice", Account_Init);
   TestAccount bob("bob", Account_Init);

   recon::AVSessionConfig avconfig;
   CPCAPI2::IpHelpers::getPreferredLocalIpAddress(resip::Tuple("*******", 53, resip::V4), avconfig.ipAddress);
   ASSERT_NE(avconfig.ipAddress, "");
   avconfig.sessionName = "avofferanswertest";
   avconfig.secureMediaRequired = true;
   avconfig.secureMediaMode = 2;
   avconfig.certAor = "<EMAIL>";

   resip::MultiReactor& reactor = dynamic_cast<CPCAPI2::PhoneInterface*>(alice.phone)->getSdkModuleThread();
   recon::AVOfferAnswerSession aliceSession(dynamic_cast<recon::MediaStack*>(dynamic_cast<CPCAPI2::Media::MediaManagerInterface*>(alice.media)->media_stack()), reactor);
   SdpReadyHandler sdpReadyHandlerAlice;
   aliceSession.setHandler(&sdpReadyHandlerAlice);
   aliceSession.applyConfiguration(avconfig);

   resip::MultiReactor& reactorBob = dynamic_cast<CPCAPI2::PhoneInterface*>(bob.phone)->getSdkModuleThread();
   recon::AVOfferAnswerSession bobSession(dynamic_cast<recon::MediaStack*>(dynamic_cast<CPCAPI2::Media::MediaManagerInterface*>(bob.media)->media_stack()), reactorBob);
   SdpReadyHandler sdpReadyHandlerBob;
   bobSession.setHandler(&sdpReadyHandlerBob);
   bobSession.applyConfiguration(avconfig);

   aliceSession.configureAudioStream("primaryAudio", AVOfferAnswerSession::MediaDirection_SendRecv);
   ASSERT_EQ(aliceSession.createOffer(false), 0);
   sdpReadyHandlerAlice.wait();
   resip::SdpContents aliceOffer = *sdpReadyHandlerAlice.sdp;
   std::cout << "aliceOffer: " << std::endl << aliceOffer << std::endl;

   ASSERT_EQ(aliceSession.setLocalDescription(aliceOffer, recon::AVOfferAnswerSession::SdpDisposition_Offer), 0);

   // alice sends offer to bob
   bobSession.configureAudioStream("primaryAudio", AVOfferAnswerSession::MediaDirection_SendRecv);
   ASSERT_EQ(bobSession.setRemoteDescription(aliceOffer, recon::AVOfferAnswerSession::SdpDisposition_Offer), 0);
   ASSERT_EQ(bobSession.createAnswer(), 0);
   sdpReadyHandlerBob.wait();
   resip::SdpContents bobAnswer = *sdpReadyHandlerBob.sdp;
   std::cout << "bobAnswer: " << std::endl << bobAnswer << std::endl;
   ASSERT_EQ(bobSession.setLocalDescription(bobAnswer, recon::AVOfferAnswerSession::SdpDisposition_Answer), 0);

   // bob sends answer to alice
   ASSERT_EQ(aliceSession.setRemoteDescription(bobAnswer, recon::AVOfferAnswerSession::SdpDisposition_Answer), 0);

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   ASSERT_EQ(aliceSession.close(), 0);
   ASSERT_EQ(bobSession.close(), 0);

   std::this_thread::sleep_for(std::chrono::milliseconds(500));
}

TEST_F(AVOfferAnswerSessionTest, BasicOfferAnswerSessionWithICE) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   recon::AVSessionConfig avconfig;
   CPCAPI2::IpHelpers::getPreferredLocalIpAddress(resip::Tuple("*******", 53, resip::V4), avconfig.ipAddress);
   ASSERT_NE(avconfig.ipAddress, "");
   avconfig.sessionName = "avofferanswertest";
   avconfig.secureMediaRequired = true;
   avconfig.secureMediaMode = 2;
   avconfig.certAor = "<EMAIL>";
   avconfig.natTraversalMode = 3;
   avconfig.natTraversalServerHostname = "stun.counterpath.com";
   avconfig.natTraversalServerPort = 3478;

   resip::MultiReactor& reactor = dynamic_cast<CPCAPI2::PhoneInterface*>(alice.phone)->getSdkModuleThread();
   recon::AVOfferAnswerSession aliceSession(dynamic_cast<recon::MediaStack*>(dynamic_cast<CPCAPI2::Media::MediaManagerInterface*>(alice.media)->media_stack()), reactor);
   SdpReadyHandler sdpReadyHandlerAlice;
   aliceSession.setHandler(&sdpReadyHandlerAlice);
   aliceSession.applyConfiguration(avconfig);

   resip::MultiReactor& reactorBob = dynamic_cast<CPCAPI2::PhoneInterface*>(bob.phone)->getSdkModuleThread();
   recon::AVOfferAnswerSession bobSession(dynamic_cast<recon::MediaStack*>(dynamic_cast<CPCAPI2::Media::MediaManagerInterface*>(bob.media)->media_stack()), reactorBob);
   SdpReadyHandler sdpReadyHandlerBob;
   bobSession.setHandler(&sdpReadyHandlerBob);
   bobSession.applyConfiguration(avconfig);

   aliceSession.configureAudioStream("primaryAudio", AVOfferAnswerSession::MediaDirection_SendRecv);
   ASSERT_EQ(aliceSession.createOffer(false), 0);
   sdpReadyHandlerAlice.wait();
   resip::SdpContents aliceOffer = *sdpReadyHandlerAlice.sdp;
   std::cout << "aliceOffer: " << std::endl << aliceOffer << std::endl;

   ASSERT_EQ(aliceSession.setLocalDescription(aliceOffer, recon::AVOfferAnswerSession::SdpDisposition_Offer), 0);

   // alice sends offer to bob
   bobSession.configureAudioStream("primaryAudio", AVOfferAnswerSession::MediaDirection_SendRecv);
   ASSERT_EQ(bobSession.setRemoteDescription(aliceOffer, recon::AVOfferAnswerSession::SdpDisposition_Offer), 0);
   ASSERT_EQ(bobSession.createAnswer(), 0);
   sdpReadyHandlerBob.wait();
   resip::SdpContents bobAnswer = *sdpReadyHandlerBob.sdp;
   std::cout << "bobAnswer: " << std::endl << bobAnswer << std::endl;
   ASSERT_EQ(bobSession.setLocalDescription(bobAnswer, recon::AVOfferAnswerSession::SdpDisposition_Answer), 0);

   // bob sends answer to alice
   ASSERT_EQ(aliceSession.setRemoteDescription(bobAnswer, recon::AVOfferAnswerSession::SdpDisposition_Answer), 0);

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   ASSERT_EQ(aliceSession.close(), 0);
   ASSERT_EQ(bobSession.close(), 0);

   std::this_thread::sleep_for(std::chrono::milliseconds(500));
}

}

#endif
