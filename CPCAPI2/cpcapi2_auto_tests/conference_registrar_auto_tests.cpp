#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#if (CPCAPI2_BRAND_CONFERENCE_BRIDGE_REGISTRAR_MODULE == 1)

#include "cpcapi2_test_fixture.h"
#include "test_call_events.h"
#include "test_events.h"
#include "test_account_events.h"

#include "../../impl/util/CurlPPHelper.h"
#include "../../impl/util/CurlPPProgress.h"
#include "../../impl/util/CurlPPSSL.h"
#include "../../impl/auth_server/AuthServerJwtUtils.h"
#include "../../impl/auth_server/AuthServerDbAccess.h"

#include "confbridge/ConferenceRegistrar.h"

#include <mutex>
#include <condition_variable>

using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::ConferenceBridge;

class ConferenceRegistrarTests : public CpcapiAutoTest
{
public:
   ConferenceRegistrarTests() {}
   virtual ~ConferenceRegistrarTests() {}
};

void ConferenceRegistrarTests_setupAuthServer(TestAccount& max)
{
   CPCAPI2::AuthServer::DbAccess authDb;
   authDb.initialize("authserver.db");
   authDb.flushUsers();
   authDb.addUser("user1", "1234");
   authDb.addUser("user2", "1234");
   authDb.addUser("server", "server");

   CPCAPI2::AuthServer::AuthServerConfig authServerConfig;
   authServerConfig.certificateFilePath = TestEnvironmentConfig::testResourcePath() + "p256-private-key-unit-tests.p8";
   authServerConfig.httpsCertificateFilePath = TestEnvironmentConfig::testResourcePath() + "cert.pem";
   authServerConfig.httpsPrivateKeyFilePath = TestEnvironmentConfig::testResourcePath() + "privkey.pem";
   authServerConfig.httpsDiffieHellmanParamsFilePath = TestEnvironmentConfig::testResourcePath() + "dh2048.pem";
   authServerConfig.numThreads = 4;
   authServerConfig.port = 18084;
   max.authServer->start(authServerConfig);
}

void setupJsonApiServer(TestAccount& maia, int jsonApiWsPort, int jsonApiHttpPort)
{
   cpc::string certificateFilePath = TestEnvironmentConfig::testResourcePath() + "p256-public-key-unit-tests.spki"; // pub key used to validate auth tokens
   JsonApi::JsonApiServerConfig jsonApiServCfg(jsonApiWsPort, jsonApiHttpPort, certificateFilePath);
   jsonApiServCfg.wssCertificateFilePath = TestEnvironmentConfig::testResourcePath() + "cert.pem";
   jsonApiServCfg.wssPrivateKeyFilePath = TestEnvironmentConfig::testResourcePath() + "privkey.pem";
   jsonApiServCfg.wssDiffieHellmanParamsFilePath = TestEnvironmentConfig::testResourcePath() + "dh2048.pem";
   jsonApiServCfg.httpsCertificateFilePath = TestEnvironmentConfig::testResourcePath() + "cert.pem";
   jsonApiServCfg.httpsPrivateKeyFilePath = TestEnvironmentConfig::testResourcePath() + "privkey.pem";
   jsonApiServCfg.httpsDiffieHellmanParamsFilePath = TestEnvironmentConfig::testResourcePath() + "dh2048.pem";

   cpc::string conferenceBridgeServiceId = "confbridge";

   if (jsonApiServCfg.certificateFilePath.size() == 0)
      jsonApiServCfg.certificateFilePath = TestEnvironmentConfig::testResourcePath() + "p256-public-key-unit-tests.spki"; // pub key used to validate auth tokens
   maia.jsonApiServer->start(jsonApiServCfg);
   CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(maia.phone)->setJsonApiServer(maia.jsonApiServer);
}

class MyRegisterConferenceResultHandler : public RegisterConferenceResultHandler
{
public:
   MyRegisterConferenceResultHandler(const MyRegisterConferenceResultHandler& rhs) = delete;
   MyRegisterConferenceResultHandler(ConferenceRegistrar* module) : mResult(false), mResultSet(false), mModule(module)
   {}
   virtual ~MyRegisterConferenceResultHandler() {}

   virtual int onRegisterConferenceComplete(ConferenceRegistrarHandle h, const RegisterConferenceResult& args) override {
      safeCout("onRegisterConferenceComplete");
      mResult = args.success;
      mResultSet = true;
      return kSuccess;
   }

   bool getResult() const {
      return mResult;
   }

   bool isResultSet() const {
      return mResultSet;
   }

private:
   bool mResult;
   bool mResultSet;
   ConferenceRegistrar* mModule;
};

class MyLookupConferenceResultHandler : public LookupConferenceResultHandler
{
public:
   MyLookupConferenceResultHandler(const MyLookupConferenceResultHandler& rhs) = delete;
   MyLookupConferenceResultHandler(ConferenceRegistrar* module) : mResultSet(false), mModule(module)
   {}
   virtual ~MyLookupConferenceResultHandler() {}

   virtual int onLookupConferenceComplete(ConferenceRegistrarHandle registrar, const LookupConferenceResult& args) override {
      safeCout("onLookupConferenceComplete");
      mResult = args;
      mResultSet = true;
      return kSuccess;
   }

   LookupConferenceResult getResult() const {
      return mResult;
   }

   bool isResultSet() const {
      return mResultSet;
   }

private:
   LookupConferenceResult mResult;
   bool mResultSet;
   ConferenceRegistrar* mModule;
};

class ConfRegTests_ListNodesHandler : public CPCAPI2::ConferenceBridge::ListNodesHandler
{
public:
   ConfRegTests_ListNodesHandler() {}
   virtual ~ConfRegTests_ListNodesHandler() {}
   bool synchronous() const override {
      return true;
   }

   virtual int onListNodesComplete(CPCAPI2::ConferenceBridge::ConferenceRegistrarHandle registrar, const CPCAPI2::ConferenceBridge::ListNodesResult& args)
   {
      std::unique_lock<std::mutex> lck(mCondWait);
      std::stringstream ss;
      safeCout("nodes list for " << registrar);
      for (size_t i=0; i<args.nodes.size(); i++)
      {
         const cpc::string& n = args.nodes[i];
         safeCout("node: " << n);
         ss << n;
         if (i < args.nodes.size() - 1)
         {
            ss << ",";
         }
      }
      mListAsString = ss.str();
      mCond.notify_all();
      return 0;
   }

   void waitForEvent(const std::chrono::seconds& dur)
   {
      std::unique_lock<std::mutex> lck(mCondWait);
      mCond.wait_for(lck, dur);
   }

   const std::string& result() const {
      return mListAsString;
   }

private:
   std::condition_variable mCond;
   std::mutex mCondWait;
   std::string mListAsString;
};

TEST_F(ConferenceRegistrarTests, BasicCluster) {
   TestAccount alice("alice", Account_Init);
   TestAccount bob("bob", Account_Init);
   TestAccount max("max", Account_Init);

   ConferenceRegistrarTests_setupAuthServer(alice);
   setupJsonApiServer(alice, 9003, 18087);
   setupJsonApiServer(bob, 9004, 18088);
   setupJsonApiServer(max, 9005, 18089);

   ConferenceRegistrarConfig registrarConfig;
   registrarConfig.conferenceRegistrarServiceIp = "127.0.0.1:18087";
   registrarConfig.conferenceRegistrarServicePort = -1;
   registrarConfig.joinClusterUrl = "https://127.0.0.1:18087/statusApi/joinCluster";
   registrarConfig.nodeId = 1;
   registrarConfig.wsUrlBase = "wss://alice";
   registrarConfig.authServiceUrl = "https://127.0.0.1:18084/login_v2";
   registrarConfig.authServiceApiKey = "-----BEGIN PUBLIC KEY-----\n"
      "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiKzxgDrW3u9+XzGN4r/GI5o+BMtt\n"
      "y+63OT4lwEgFlkpedq5FY3eCzI4lB1WXDVip0OpCM1zYoCh6mqi7KaS1Vg==\n"
      "-----END PUBLIC KEY-----";
   registrarConfig.urlMapFilename = "alice_urlmap_autotests.db";

   alice.conferenceRegistrar->start(registrarConfig);
   {
      ConferenceRegistrarHandle h;
      ConferenceRegistrarStartupResult args;
      ASSERT_TRUE(cpcExpectEvent(alice.conferenceRegistrarEvents, "ConferenceRegistrarHandler::onStartupComplete", 15000, CPCAPI2::test::AlwaysTruePred(), h, args));
   }

   registrarConfig.conferenceRegistrarServiceIp = "127.0.0.1:18088";
   registrarConfig.conferenceRegistrarServicePort = -1;
   registrarConfig.joinClusterUrl = "https://127.0.0.1:18087/statusApi/joinCluster";
   registrarConfig.nodeId = 2;
   registrarConfig.wsUrlBase = "wss://bob";
   registrarConfig.urlMapFilename = "bob_urlmap_autotests.db";

   bob.conferenceRegistrar->start(registrarConfig);
   {
      ConferenceRegistrarHandle h;
      ConferenceRegistrarStartupResult args;
      ASSERT_TRUE(cpcExpectEvent(bob.conferenceRegistrarEvents, "ConferenceRegistrarHandler::onStartupComplete", 15000, CPCAPI2::test::AlwaysTruePred(), h, args));
   }

   registrarConfig.conferenceRegistrarServiceIp = "127.0.0.1:18089";
   registrarConfig.conferenceRegistrarServicePort = -1;
   registrarConfig.joinClusterUrl = "https://127.0.0.1:18087/statusApi/joinCluster";
   registrarConfig.nodeId = 3;
   registrarConfig.wsUrlBase = "wss://max";
   registrarConfig.urlMapFilename = "max_urlmap_autotests.db";

   max.conferenceRegistrar->start(registrarConfig);
   {
      ConferenceRegistrarHandle h;
      ConferenceRegistrarStartupResult args;
      ASSERT_TRUE(cpcExpectEvent(max.conferenceRegistrarEvents, "ConferenceRegistrarHandler::onStartupComplete", 15000, CPCAPI2::test::AlwaysTruePred(), h, args));
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   bob.conferenceRegistrar->lookupConference("ABCD", NULL);
   {
      ConferenceRegistrarHandle h;
      LookupConferenceResult args;
      ASSERT_TRUE(cpcExpectEvent(bob.conferenceRegistrarEvents, "LookupConferenceResultHandler::onLookupConferenceComplete", 15000, CPCAPI2::test::AlwaysTruePred(), h, args));
      ASSERT_TRUE(args.success);
      ASSERT_EQ(args.wsUrl, "wss://bob/ABCD");
   }

   alice.authServer->shutdown();

   alice.conferenceRegistrar->shutdown();
   bob.conferenceRegistrar->shutdown();
   max.conferenceRegistrar->shutdown();

   alice.jsonApiServer->shutdown();
   bob.jsonApiServer->shutdown();
   max.jsonApiServer->shutdown();

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));
}

TEST_F(ConferenceRegistrarTests, RegisterThenTakeDownNode) {
   TestAccount alice("alice", Account_Init);
   TestAccount bob("bob", Account_Init);
   TestAccount max("max", Account_Init);

   ConferenceRegistrarTests_setupAuthServer(alice);
   setupJsonApiServer(alice, 9003, 18087);
   setupJsonApiServer(bob, 9004, 18088);
   setupJsonApiServer(max, 9005, 18089);

   ConferenceRegistrarConfig registrarConfig;
   registrarConfig.conferenceRegistrarServiceIp = "127.0.0.1:18087";
   registrarConfig.conferenceRegistrarServicePort = -1;
   registrarConfig.joinClusterUrl = "https://127.0.0.1:18087/statusApi/joinCluster";
   registrarConfig.nodeId = 1;
   registrarConfig.wsUrlBase = "wss://alice";
   registrarConfig.authServiceUrl = "https://127.0.0.1:18084/login_v2";
   registrarConfig.authServiceApiKey = "-----BEGIN PUBLIC KEY-----\n"
      "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiKzxgDrW3u9+XzGN4r/GI5o+BMtt\n"
      "y+63OT4lwEgFlkpedq5FY3eCzI4lB1WXDVip0OpCM1zYoCh6mqi7KaS1Vg==\n"
      "-----END PUBLIC KEY-----";
   registrarConfig.urlMapFilename = "alice_urlmap_autotests.db";

   alice.conferenceRegistrar->start(registrarConfig);
   {
      ConferenceRegistrarHandle h;
      ConferenceRegistrarStartupResult args;
      ASSERT_TRUE(cpcExpectEvent(alice.conferenceRegistrarEvents, "ConferenceRegistrarHandler::onStartupComplete", 15000, CPCAPI2::test::AlwaysTruePred(), h, args));
   }

   registrarConfig.conferenceRegistrarServiceIp = "127.0.0.1:18088";
   registrarConfig.conferenceRegistrarServicePort = -1;
   registrarConfig.joinClusterUrl = "https://127.0.0.1:18087/statusApi/joinCluster";
   registrarConfig.nodeId = 2;
   registrarConfig.wsUrlBase = "wss://bob";
   registrarConfig.urlMapFilename = "bob_urlmap_autotests.db";

   bob.conferenceRegistrar->start(registrarConfig);
   {
      ConferenceRegistrarHandle h;
      ConferenceRegistrarStartupResult args;
      ASSERT_TRUE(cpcExpectEvent(bob.conferenceRegistrarEvents, "ConferenceRegistrarHandler::onStartupComplete", 15000, CPCAPI2::test::AlwaysTruePred(), h, args));
   }

   registrarConfig.conferenceRegistrarServiceIp = "127.0.0.1:18089";
   registrarConfig.conferenceRegistrarServicePort = -1;
   registrarConfig.joinClusterUrl = "https://127.0.0.1:18087/statusApi/joinCluster";
   registrarConfig.nodeId = 3;
   registrarConfig.wsUrlBase = "wss://max";
   registrarConfig.urlMapFilename = "max_urlmap_autotests.db";

   max.conferenceRegistrar->start(registrarConfig);
   {
      ConferenceRegistrarHandle h;
      ConferenceRegistrarStartupResult args;
      ASSERT_TRUE(cpcExpectEvent(max.conferenceRegistrarEvents, "ConferenceRegistrarHandler::onStartupComplete", 15000, CPCAPI2::test::AlwaysTruePred(), h, args));
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   max.conferenceRegistrar->lookupConference("ABCD", NULL);
   {
      ConferenceRegistrarHandle h;
      LookupConferenceResult args;
      ASSERT_TRUE(cpcExpectEvent(max.conferenceRegistrarEvents, "LookupConferenceResultHandler::onLookupConferenceComplete", 15000, CPCAPI2::test::AlwaysTruePred(), h, args));
      ASSERT_TRUE(args.success);
      ASSERT_EQ(args.wsUrl, "wss://max/ABCD");
   }

   max.conferenceRegistrar->shutdown();

   std::this_thread::sleep_for(std::chrono::milliseconds(15000));

   bob.conferenceRegistrar->lookupConference("ABCD", NULL);
   {
      ConferenceRegistrarHandle h;
      LookupConferenceResult args;
      ASSERT_TRUE(cpcExpectEvent(bob.conferenceRegistrarEvents, "LookupConferenceResultHandler::onLookupConferenceComplete", 15000, CPCAPI2::test::AlwaysTruePred(), h, args));
      ASSERT_TRUE(args.success);
      ASSERT_EQ(args.wsUrl, "wss://bob/ABCD");
   }

   alice.authServer->shutdown();

   alice.conferenceRegistrar->shutdown();
   bob.conferenceRegistrar->shutdown();

   alice.jsonApiServer->shutdown();
   bob.jsonApiServer->shutdown();
   max.jsonApiServer->shutdown();

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));
}

// OBELISK-6179: failing on windows test runs
TEST_F(ConferenceRegistrarTests, DISABLED_TakeDownLeader) {
   TestAccount alice("alice", Account_Init);
   TestAccount bob("bob", Account_Init);
   TestAccount max("max", Account_Init);

   ConferenceRegistrarTests_setupAuthServer(alice);
   setupJsonApiServer(alice, 9003, 18087);
   setupJsonApiServer(bob, 9004, 18088);
   setupJsonApiServer(max, 9005, 18089);

   ConferenceRegistrarConfig registrarConfig;
   registrarConfig.conferenceRegistrarServiceIp = "127.0.0.1:18087";
   registrarConfig.conferenceRegistrarServicePort = -1;
   registrarConfig.joinClusterUrl = "https://127.0.0.1:18087/statusApi/joinCluster";
   registrarConfig.nodeId = 1;
   registrarConfig.wsUrlBase = "wss://alice";
   registrarConfig.authServiceUrl = "https://127.0.0.1:18084/login_v2";
   registrarConfig.authServiceApiKey = "-----BEGIN PUBLIC KEY-----\n"
      "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiKzxgDrW3u9+XzGN4r/GI5o+BMtt\n"
      "y+63OT4lwEgFlkpedq5FY3eCzI4lB1WXDVip0OpCM1zYoCh6mqi7KaS1Vg==\n"
      "-----END PUBLIC KEY-----";
   registrarConfig.urlMapFilename = "alice_urlmap_autotests.db";

   alice.conferenceRegistrar->start(registrarConfig);
   {
      ConferenceRegistrarHandle h;
      ConferenceRegistrarStartupResult args;
      ASSERT_TRUE(cpcExpectEvent(alice.conferenceRegistrarEvents, "ConferenceRegistrarHandler::onStartupComplete", 15000, CPCAPI2::test::AlwaysTruePred(), h, args));
   }

   registrarConfig.conferenceRegistrarServiceIp = "127.0.0.1:18088";
   registrarConfig.conferenceRegistrarServicePort = -1;
   registrarConfig.joinClusterUrl = "https://127.0.0.1:18087/statusApi/joinCluster";
   registrarConfig.nodeId = 2;
   registrarConfig.wsUrlBase = "wss://bob";
   registrarConfig.urlMapFilename = "bob_urlmap_autotests.db";

   bob.conferenceRegistrar->start(registrarConfig);
   {
      ConferenceRegistrarHandle h;
      ConferenceRegistrarStartupResult args;
      ASSERT_TRUE(cpcExpectEvent(bob.conferenceRegistrarEvents, "ConferenceRegistrarHandler::onStartupComplete", 15000, CPCAPI2::test::AlwaysTruePred(), h, args));
   }

   registrarConfig.conferenceRegistrarServiceIp = "127.0.0.1:18089";
   registrarConfig.conferenceRegistrarServicePort = -1;
   registrarConfig.joinClusterUrl = "https://127.0.0.1:18087/statusApi/joinCluster";
   registrarConfig.nodeId = 3;
   registrarConfig.wsUrlBase = "wss://max";
   registrarConfig.urlMapFilename = "max_urlmap_autotests.db";

   max.conferenceRegistrar->start(registrarConfig);
   {
      ConferenceRegistrarHandle h;
      ConferenceRegistrarStartupResult args;
      ASSERT_TRUE(cpcExpectEvent(max.conferenceRegistrarEvents, "ConferenceRegistrarHandler::onStartupComplete", 15000, CPCAPI2::test::AlwaysTruePred(), h, args));
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(15000));

   ConfRegTests_ListNodesHandler aliceListNodesHandler;
   alice.conferenceRegistrar->listNodes(&aliceListNodesHandler);
   aliceListNodesHandler.waitForEvent(std::chrono::seconds(1));
   ASSERT_EQ(std::string("L1,2,3"), aliceListNodesHandler.result());
   ConfRegTests_ListNodesHandler bobListNodesHandler;
   bob.conferenceRegistrar->listNodes(&bobListNodesHandler);
   bobListNodesHandler.waitForEvent(std::chrono::seconds(1));
   ASSERT_EQ(std::string("2,L1"), bobListNodesHandler.result());
   ConfRegTests_ListNodesHandler maxListNodesHandler;
   max.conferenceRegistrar->listNodes(&maxListNodesHandler);
   maxListNodesHandler.waitForEvent(std::chrono::seconds(1));
   ASSERT_EQ(std::string("3,L1"), maxListNodesHandler.result());

   max.conferenceRegistrar->lookupConference("ABCD", NULL);
   {
      ConferenceRegistrarHandle h;
      LookupConferenceResult args;
      ASSERT_TRUE(cpcExpectEvent(max.conferenceRegistrarEvents, "LookupConferenceResultHandler::onLookupConferenceComplete", 15000, CPCAPI2::test::AlwaysTruePred(), h, args));
      ASSERT_TRUE(args.success);
      ASSERT_EQ(args.wsUrl, "wss://max/ABCD");
   }

   alice.conferenceRegistrar->shutdown();

   std::this_thread::sleep_for(std::chrono::milliseconds(15000));
   bob.conferenceRegistrar->listNodes(&bobListNodesHandler);
   bobListNodesHandler.waitForEvent(std::chrono::seconds(1));
   ASSERT_EQ(std::string("2,L1"), bobListNodesHandler.result());

   max.conferenceRegistrar->listNodes(&maxListNodesHandler);
   maxListNodesHandler.waitForEvent(std::chrono::seconds(1));
   ASSERT_EQ(std::string("3,L1"), maxListNodesHandler.result());

   bob.conferenceRegistrar->lookupConference("ABCD", NULL);
   {
      ConferenceRegistrarHandle h;
      LookupConferenceResult args;
      ASSERT_TRUE(cpcExpectEvent(bob.conferenceRegistrarEvents, "LookupConferenceResultHandler::onLookupConferenceComplete", 15000, CPCAPI2::test::AlwaysTruePred(), h, args));
      ASSERT_TRUE(args.success);
      ASSERT_EQ(args.wsUrl, "wss://max/ABCD");
   }

   registrarConfig.conferenceRegistrarServiceIp = "127.0.0.1:18087";
   registrarConfig.conferenceRegistrarServicePort = -1;
   registrarConfig.joinClusterUrl = "https://127.0.0.1:18087/statusApi/joinCluster";
   registrarConfig.nodeId = 1;
   registrarConfig.wsUrlBase = "wss://alice";
   registrarConfig.authServiceUrl = "https://127.0.0.1:18084/login_v2";
   registrarConfig.authServiceApiKey = "-----BEGIN PUBLIC KEY-----\n"
      "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiKzxgDrW3u9+XzGN4r/GI5o+BMtt\n"
      "y+63OT4lwEgFlkpedq5FY3eCzI4lB1WXDVip0OpCM1zYoCh6mqi7KaS1Vg==\n"
      "-----END PUBLIC KEY-----";
   alice.conferenceRegistrar->start(registrarConfig);
   {
      ConferenceRegistrarHandle h;
      ConferenceRegistrarStartupResult args;
      ASSERT_TRUE(cpcExpectEvent(alice.conferenceRegistrarEvents, "ConferenceRegistrarHandler::onStartupComplete", 15000, CPCAPI2::test::AlwaysTruePred(), h, args));
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(45000));
   alice.conferenceRegistrar->listNodes(&aliceListNodesHandler);
   aliceListNodesHandler.waitForEvent(std::chrono::seconds(1));
   ASSERT_EQ(std::string("L1,2,3"), aliceListNodesHandler.result());

   bob.conferenceRegistrar->listNodes(&bobListNodesHandler);
   bobListNodesHandler.waitForEvent(std::chrono::seconds(1));
   ASSERT_EQ(std::string("2,L1"), bobListNodesHandler.result());

   max.conferenceRegistrar->listNodes(&maxListNodesHandler);
   maxListNodesHandler.waitForEvent(std::chrono::seconds(1));
   ASSERT_EQ(std::string("3,L1"), maxListNodesHandler.result());

   alice.authServer->shutdown();

   alice.conferenceRegistrar->shutdown();
   bob.conferenceRegistrar->shutdown();

   alice.jsonApiServer->shutdown();
   bob.jsonApiServer->shutdown();
   max.jsonApiServer->shutdown();

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));
}

#endif
