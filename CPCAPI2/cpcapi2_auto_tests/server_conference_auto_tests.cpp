#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#if (CPCAPI2_BRAND_CONFERENCE_MODULE == 1)

#include "cpcapi2_test_fixture.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>

#include "test_call_events.h"
#include "test_events.h"

using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::SipConversation;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::SipConference;

const cpc::string SERVERCONF_TESTS_CONFERENCE_FACTORY_URI = "sip:<EMAIL>";

class ConferenceTestAccount : public TestAccount
{
public:
   ConferenceTestAccount(const std::string& name, bool disableOnDestruct = true) : TestAccount(name, Account_NoInit, disableOnDestruct)
   {
      if (name == "alice")
      {
         config.settings.displayName = "**********";
         config.settings.username = "+1**********";
         config.settings.auth_username = "**********";//@csa1.luqdlab.com";
         config.settings.domain = "csa1.luqdlab.com";
         config.settings.outboundProxy = "**********";
         config.settings.password = "newsys";
         config.settings.registrationIntervalSeconds = 36000;
         config.settings.useOutbound = false;
         config.settings.useInstanceId = true;
      }
      else if (name == "charlie")
      {
         config.settings.displayName = "**********";
         config.settings.username = "+1**********";
         config.settings.auth_username = "**********";//@csa1.luqdlab.com";
         config.settings.domain = "csa1.luqdlab.com";
         config.settings.outboundProxy = "**********";
         config.settings.password = "newsys";
         config.settings.registrationIntervalSeconds = 36000;
         config.settings.useOutbound = false;
         config.settings.useInstanceId = true;
      }
      else
      {
         assert(false);
      }

      init();

      confManager = SipConferenceManager::getInterface(phone);
      confManager->setHandler(handle, (SipConferenceHandler*) 0xDEADBEEF);

      enable();
   }

   SipConferenceManager* confManager;
};

class StandardLineTestAccount : public TestAccount
{
public:
   StandardLineTestAccount(const std::string& name) : TestAccount(name, Account_NoInit)
   {
      if (name == "charlie")
      {
         config.settings.displayName = "**********";
         config.settings.username = "+1**********";
         config.settings.auth_username = "**********";//@csa1.luqdlab.com";
         config.settings.domain = "csa1.luqdlab.com";
         config.settings.outboundProxy = "**********";
         config.settings.password = "newsys";
         config.settings.registrationIntervalSeconds = 36000;
         config.settings.useOutbound = false;
         config.settings.useInstanceId = true;
      }
      else if (name == "dave")
      {
         config.settings.displayName = "**********";
         config.settings.username = "+***********";
         config.settings.auth_username = "**********";//@csa1.luqdlab.com";
         config.settings.domain = "csa1.luqdlab.com";
         config.settings.outboundProxy = "**********";
         config.settings.password = "newsys";
         config.settings.registrationIntervalSeconds = 36000;
         config.settings.useOutbound = false;
         config.settings.useInstanceId = true;
      }
      else
      {
         assert(false);
      }

      init();
      enable();
   }
};


class ServerConferenceTests : public CpcapiAutoTest
{
public:
   ServerConferenceTests() {}
   virtual ~ServerConferenceTests() {}
};

TEST_F(ServerConferenceTests, DISABLED_AddOneToOneCallToConference) {
   ConferenceTestAccount alice("alice");
   StandardLineTestAccount charlie("charlie");

   // Alice's thread:
   //  - Alice calls Charlie, who answers, and they connect
   //  - Alice calls the conference factory, which answers and they connect
   //  - Alice transfers Charlie into the conference
   //     -> The original call with Charlie ends
   //  - After some time, Alice exits the conference by hanging up
   auto aliceEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->addParticipant(aliceCall, charlie.config.uri());
      alice.conversation->start(aliceCall);
      {
         SipConversationHandle h;
         NewConversationEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
            "SipConversationHandler::onNewConversation",
            15000,
            HandleEqualsPred<SipConversationHandle>(aliceCall),
            h, evt));
         ASSERT_EQ(evt.account, alice.handle);
         ASSERT_EQ(evt.conversationType, ConversationType_Outgoing);
         ASSERT_EQ(evt.remoteAddress, charlie.config.uri());
         ASSERT_EQ(evt.remoteDisplayName, "");
         ASSERT_EQ(evt.localMediaInfo.size(), 1);
      }

      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
            "SipConversationHandler::onConversationStateChanged",
            5000,
            HandleEqualsPred<SipConversationHandle>(aliceCall),
            h, evt));
         ASSERT_EQ(evt.conversationState, ConversationState_RemoteRinging);
      }

      {
         SipConversationHandle h;
         ConversationMediaChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
            "SipConversationHandler::onConversationMediaChanged",
            5000,
            HandleEqualsPred<SipConversationHandle>(aliceCall),
            h, evt));
         ASSERT_EQ(evt.localMediaInfo.size(),1);
         ASSERT_EQ(evt.localMediaInfo[0].mediaType, MediaType_Audio);
         ASSERT_EQ(evt.localMediaInfo[0].mediaDirection, MediaDirection_SendReceive);
      }

      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
            "SipConversationHandler::onConversationStateChanged",
            5000,
            HandleEqualsPred<SipConversationHandle>(aliceCall),
            h, evt));
         ASSERT_EQ(evt.conversationState, ConversationState_Connected);
      }

      SipConversationHandle aliceConference = alice.conferenceManager->createServerConference(alice.handle);
      alice.conferenceManager->setConferenceFactoryAddress(aliceConference, SERVERCONF_TESTS_CONFERENCE_FACTORY_URI);
      alice.conferenceManager->start(aliceConference);

      {
         SipConversationHandle h;
         NewConversationEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
            "SipConversationHandler::onNewConversation",
            15000,
            HandleEqualsPred<SipConversationHandle>(aliceConference),
            h, evt));
         ASSERT_EQ(evt.account, alice.handle);
         ASSERT_EQ(evt.conversationType, ConversationType_Outgoing);
         ASSERT_EQ(evt.remoteAddress, SERVERCONF_TESTS_CONFERENCE_FACTORY_URI);
         ASSERT_EQ(evt.remoteDisplayName, "");
         ASSERT_EQ(evt.localMediaInfo.size(), 1);
      }

      {
         SipConversationHandle h;
         ConversationMediaChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
            "SipConversationHandler::onConversationMediaChanged",
            5000,
            HandleEqualsPred<SipConversationHandle>(aliceConference),
            h, evt));
         ASSERT_EQ(evt.localMediaInfo.size(),1);
         ASSERT_EQ(evt.localMediaInfo[0].mediaType, MediaType_Audio);
         ASSERT_EQ(evt.localMediaInfo[0].mediaDirection, MediaDirection_SendReceive);
      }

      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
            "SipConversationHandler::onConversationStateChanged",
            5000,
            HandleEqualsPred<SipConversationHandle>(aliceConference),
            h, evt));
         ASSERT_EQ(evt.conversationState, ConversationState_Connected);
      }

      alice.conferenceManager->addToServerConference(aliceConference, aliceCall);

      assertTransferProgress(alice, aliceConference, TransferProgressEventType_Trying);
      // Charlie accepts the INVITE w/Replaces, which terminates the original call between him and Alice
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertTransferProgress(alice, aliceConference, TransferProgressEventType_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(3000));

      alice.conversation->end(aliceConference);
      assertConversationEnded(alice, aliceConference, ConversationEndReason_UserTerminatedLocally);
   });

   // Charlie's thread:
   //  - Alice calls Charlie, who answers, and they connect
   //  - Alice transfers Charlie into the conference
   //     -> Charlie gets a new incoming call (from the conference server) which replaces his original call with Alice; he accepts
   //     -> Charlies original call with Alice ends
   //  - After some time, Charlie exits the conference by hanging up
   auto charlieEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle charlieCall = 0;
      {
         SipConversationHandle h;
         NewConversationEvent evt;
         ASSERT_TRUE(cpcExpectEvent(charlie.conversationEvents,
            "SipConversationHandler::onNewConversation",
            5000,
            AlwaysTruePred(),
            h, evt));
         charlieCall = h;
         ASSERT_EQ(evt.account, charlie.handle);
         ASSERT_EQ(evt.conversationState, ConversationState_RemoteOriginated);
         ASSERT_EQ(evt.conversationType, ConversationType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
         ASSERT_EQ(evt.remoteDisplayName, alice.config.settings.displayName);
         ASSERT_EQ(evt.remoteMediaInfo.size(), 1);
      }

      // charlie sends 180 ringing -- this should trigger state transitions for both charlie and Alice
      ASSERT_EQ(charlie.conversation->sendRingingResponse(charlieCall), kSuccess);

      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(charlie.conversationEvents,
            "SipConversationHandler::onConversationStateChanged",
            5000,
            HandleEqualsPred<SipConversationHandle>(charlieCall),
            h, evt));
         ASSERT_EQ(evt.conversationState, ConversationState_LocalRinging);
      }

      // charlie answers the call (200 OK) -- this should trigger state transitions for both charlie and Alice
      ASSERT_EQ(charlie.conversation->accept(charlieCall), kSuccess);

      {
         SipConversationHandle h;
         ConversationMediaChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(charlie.conversationEvents,
            "SipConversationHandler::onConversationMediaChanged",
            15000,
            HandleEqualsPred<SipConversationHandle>(charlieCall),
            h, evt));
         ASSERT_EQ(evt.localMediaInfo.size(),1);
         ASSERT_EQ(evt.localMediaInfo[0].mediaType, MediaType_Audio);
         ASSERT_EQ(evt.localMediaInfo[0].mediaDirection, MediaDirection_SendReceive);
         std::string strLocalPlname(evt.localMediaInfo[0].audioCodec.plname);
         std::string strRemotePlname(evt.remoteMediaInfo[0].audioCodec.plname);
      }

      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(charlie.conversationEvents,
            "SipConversationHandler::onConversationStateChanged",
            5000,
            HandleEqualsPred<SipConversationHandle>(charlieCall),
            h, evt));
         ASSERT_EQ(evt.conversationState, ConversationState_Connected);
      }

      // charlie gets transferred into the conference
      assertConversationMediaChangeRequest(charlie, charlieCall, MediaDirection_SendReceive);
      charlie.conversation->acceptIncomingTransferRequest(charlieCall);
      assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);

      {
         NewConversationEvent evt;
         SipConversationHandle h;
         ASSERT_TRUE(cpcExpectEvent(charlie.conversationEvents,
            "SipConversationHandler::onNewConversation",
            30000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(ConversationType_Outgoing, evt.conversationType);
         ASSERT_EQ(h, charlieCall);
      }
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));

      assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor2(charlieEvents, aliceEvents);

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
}



#endif
