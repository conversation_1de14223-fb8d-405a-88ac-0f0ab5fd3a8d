#pragma once

#ifndef TSCF_TEST_FIXTURE_H
#define TSCF_TEST_FIXTURE_H

#include "cpcapi2_test_fixture.h"

struct TscfAccountConfig : public TestAccountConfig
{
   cpc::string password;
   TscfAccountConfig(const std::string& name, const std::string& password);
   static TscfAccountConfig makeSecureConfig(const std::string& name, const std::string& password);
};

class TscfAccount : public TestAccount
{
public:
   TscfAccount(const std::string& name, const std::string& password, TestAccountInitMode initMode=Account_Enable, bool disableOnDestruct=true, CPCAPI2::Phone* phone=NULL);

   void enable(bool assertRegistrationState = true);
   
};


#endif
