﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="cpcapi2_breakpad_server.cpp" />
    <ClCompile Include="stdafx.cpp" />
    <ClCompile Include="cross_process_mutex_win32.cpp">
      <Filter>util</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\CPCAPI2\impl\cpcstl\allocator.cpp">
      <Filter>cpcstl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\CPCAPI2\impl\cpcstl\string.cpp">
      <Filter>cpcstl</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="stdafx.h" />
    <ClInclude Include="targetver.h" />
    <ClInclude Include="cross_process_mutex_win32.h">
      <Filter>util</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\CPCAPI2\interface\public\cpcstl\allocator.h">
      <Filter>cpcstl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\CPCAPI2\interface\public\cpcstl\string.h">
      <Filter>cpcstl</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Text Include="ReadMe.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="util">
      <UniqueIdentifier>{615fd8cd-e793-4792-8712-077dabfdb456}</UniqueIdentifier>
    </Filter>
    <Filter Include="cpcstl">
      <UniqueIdentifier>{fa75051d-4445-4b11-9e4b-25ab5a0f43b7}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>