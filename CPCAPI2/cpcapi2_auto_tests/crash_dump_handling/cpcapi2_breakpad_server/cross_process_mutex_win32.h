#ifndef CPCAPI2_AUTO_TESTS_CROSS_PROCESS_MUTEX_H
#define CPCAPI2_AUTO_TESTS_CROSS_PROCESS_MUTEX_H

#include <Windows.h>
#include <string>
#include <memory>

namespace CPCAPI2
{
namespace test
{
   class CrossProcessMutex;
   typedef std::unique_ptr<CrossProcessMutex> CrossProcessMutexPtr;

   class CrossProcessMutex
   {
   public:
      CrossProcessMutex(const std::wstring& name);
      ~CrossProcessMutex();

      bool locked() const;
      std::wstring mutexName() const;

      static bool testIsMutexLocked(const std::wstring& name);

   private:
      static HANDLE createWin32Mutex(const std::wstring& name);

      HANDLE mHandle;
      DWORD mLastError;
      bool mLocked;
      const std::wstring mMutexName;
   };
}
}


#endif // CPCAPI2_AUTO_TESTS_CROSS_PROCESS_MUTEX_H