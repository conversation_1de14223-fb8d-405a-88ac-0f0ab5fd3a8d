#include "stdafx.h"
#include "cross_process_mutex_win32.h"
#include <sddl.h>

using namespace CPCAPI2::test;

CrossProcessMutex::CrossProcessMutex(const std::wstring& name) :
   mLocked(false),
   mMutexName(name)
{
   mHandle = createWin32Mutex(name);

   DWORD waitResult = ::WaitForSingleObject(mHandle, INFINITE);
   if (waitResult == WAIT_OBJECT_0)
   {
      mLocked = true;
   }
   else
   {
      ::CloseHandle(mHandle);
   }

}

HANDLE
CrossProcessMutex::createWin32Mutex(const std::wstring& name)
{
   SECURITY_ATTRIBUTES sa;
   sa.nLength = sizeof(sa);
   sa.bInheritHandle = FALSE;

   PSECURITY_DESCRIPTOR pSD;
   ConvertStringSecurityDescriptorToSecurityDescriptorA(
      "S:(ML;;NW;;;LW)", // "low integrity ssdl sacl"
      SDDL_REVISION_1,
      &pSD,
      NULL);

   sa.lpSecurityDescriptor = pSD;

   return ::CreateMutexW(&sa, FALSE, name.c_str());
}

bool
CrossProcessMutex::testIsMutexLocked(const std::wstring& name)
{
   bool locked = false;

   HANDLE handle = ::OpenMutexW(SYNCHRONIZE, FALSE, name.c_str());
   if (handle)
   {
      DWORD waitResult = ::WaitForSingleObject(handle, 10);
      if (waitResult == WAIT_TIMEOUT)
      {
         locked = true;
      }
      else if (waitResult == WAIT_OBJECT_0)
      {
         ::ReleaseMutex(handle);
         locked = false;
      }

      ::CloseHandle(handle);
   }
   else
   {
      // what to do here ??
      locked = false;
   }

   return locked;
}

bool
CrossProcessMutex::locked() const
{
   return mLocked;
}

std::wstring
CrossProcessMutex::mutexName() const
{
   return mMutexName;
}

CrossProcessMutex::~CrossProcessMutex()
{
   try
   {
      if (mLocked)
      {
         ::ReleaseMutex(mHandle);
      }

      ::CloseHandle(mHandle);
   }
   catch (...)
   {
      // have seen CloseHandle throw exceptions if the other process is terminated via debugger stop
   }
}
