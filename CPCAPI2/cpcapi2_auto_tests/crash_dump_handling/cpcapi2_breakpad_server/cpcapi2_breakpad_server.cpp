
#include "stdafx.h"

#include <codecvt>
#include <sstream>
#include <iostream>
#include <fstream>

#include "cross_process_mutex_win32.h"

#include "client/windows/crash_generation/crash_generation_server.h"
#include "client/windows/handler/exception_handler.h"

static std::wstring gDumpFilename;
static bool gShutdown = false;


void onClientConnected(void* context, const google_breakpad::ClientInfo* client_info)
{
}

void onClientDumpRequest(void* context, const google_breakpad::ClientInfo* client_info, const std::wstring* file_path)
{
   std::wstringstream logPath;
   logPath << gDumpFilename << ".txt";

   DeleteFileW(gDumpFilename.c_str());
   BOOL moveSuccess = MoveFileW(file_path->c_str(), gDumpFilename.c_str());

   std::wfstream of("dumps\\dump_log.txt", std::fstream::in | std::fstream::out | std::fstream::app);
   of << "Dump written; original filename: " << file_path->c_str() << " renamed name: " << gDumpFilename.c_str()
      << ". Move success: " << moveSuccess << " GetLastError: " << GetLastError() << std::endl;

   std::cout << "dump written!" << std::endl;

   of.close();

   gShutdown = true;
}

void onClientExited(void* context, const google_breakpad::ClientInfo* client_info)
{
   gShutdown = true;
}

void onClientUploadRequest(void* context, const DWORD crash_id)
{
}


bool onExceptionFilter(void* context, EXCEPTION_POINTERS* exinfo,
   MDRawAssertionInfo* assertion)
{
   return true;
}

bool onMinidumpDumped(const wchar_t* dump_path,
   const wchar_t* minidump_id,
   void* context,
   EXCEPTION_POINTERS* exinfo,
   MDRawAssertionInfo* assertion,
   bool succeeded)
{
   return succeeded;
}


bool
initBreakpad(const std::string& pipeName, const std::string& dumpFolder, const std::string& dumpName)
{
   std::wstring_convert<std::codecvt_utf8_utf16<wchar_t>> cvt;
   std::wstringstream ss;
   ss << cvt.from_bytes(dumpFolder) << "\\" << cvt.from_bytes(dumpName);
   gDumpFilename = ss.str();

   google_breakpad::CrashGenerationServer *pCrashServer =
      new google_breakpad::CrashGenerationServer(cvt.from_bytes(pipeName),
         NULL,
         onClientConnected,
         NULL,
         onClientDumpRequest,
         NULL,
         onClientExited,
         NULL,
         onClientUploadRequest,
         NULL,
         true,
         &cvt.from_bytes(dumpFolder));

   if (pCrashServer == NULL) {
      return false;
   }

   // If you have a server has been started, the restart will fail 
   if (!pCrashServer->Start()) {
      delete pCrashServer;
      pCrashServer = NULL;
   }

   google_breakpad::ExceptionHandler *pCrashHandler =
      new google_breakpad::ExceptionHandler(cvt.from_bytes(dumpFolder),
         onExceptionFilter,
         onMinidumpDumped,
         NULL,
         google_breakpad::ExceptionHandler::HANDLER_ALL,
         MiniDumpNormal,
         (pCrashServer == NULL) ? cvt.from_bytes(pipeName).c_str() : NULL, // If the server, are used to process dump 
         NULL);

   if (pCrashHandler == NULL) {
      return false;
   }

   return true;
}


int main(int argc, char* argv[])
{
   std::wofstream of("dumps\\server_inst.txt", std::fstream::in | std::fstream::out | std::fstream::app);
   of << "new instance. arguments: ";
   for (int i = 0; i < argc; ++i)
   {
      of << argv[i] << " ";
   }
   of << std::endl;
   of.close();

   if (argc >= 5)
   {
      std::string pipeName = argv[1];
      std::string dumpFolder = argv[2];
      std::string dumpName = argv[3];
      std::string mutexName = argv[4];

      initBreakpad(pipeName, dumpFolder, dumpName);

      for (;;)
      {
         if (gShutdown)
         {
            break;
         }
         
         std::wstring_convert<std::codecvt_utf8_utf16<wchar_t>> cvt;
         if (!CPCAPI2::test::CrossProcessMutex::testIsMutexLocked(cvt.from_bytes(mutexName)))
         {
            break;
         }

         ::Sleep(1000);
      }

      return 0;
   }
   else
   {
      std::cout << "usage: cpcapi2_breakpad_dump_server pipename dumpFolder dumpName mutexName" << std::endl;
      return -1;
   }
}

