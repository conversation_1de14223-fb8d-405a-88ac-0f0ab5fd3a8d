import re
import os
import getopt
import sys

minidumpLoc = ""
key = "$TkFutcubSe_TestName"
usage = "Usage: python /path/to/renameDump.py --dump=/path/to/25954d5d-ec8e-4f9e-a190-498532d1b400.dmp"

try:
    opts, args = getopt.getopt(sys.argv[1:], "h:d", ["help", "dump="])

    if(len(sys.argv) != 2):
        print usage
        sys.exit()

except getopt.GetoptError:
    print usage
    sys.exit(2)

for opt, arg in opts:
    if opt in ("-h","--help"):
        print usage
        sys.exit()
    elif opt in ("--dump"):
        if os.path.exists(arg):
            minidumpLoc = arg
        else:
            sys.exit(2)

with open(minidumpLoc) as f:
    for line in f:
        if key in line:

            line = re.sub(r'[^\x00-\x7f]', r'', line)
            line = line.split('$')

            for element in line:
                if key[1:] in element:

                    tempVal = element.replace("\x00", "")
                    tempVal = tempVal.replace("\x15", "")

                    tempVal = tempVal.replace(key[1:], "")
                    tempVal = tempVal.split(".")

                    final = re.sub('[^a-zA-Z]+', '', tempVal[0]) + "." + tempVal[1] + ".dmp"

                    dir_path = os.path.dirname(os.path.realpath(minidumpLoc))
                    final = os.path.join(dir_path, final)

                    os.rename(minidumpLoc, final)
                    print "Old dmp file: " + minidumpLoc
                    print "New dmp file: " + final