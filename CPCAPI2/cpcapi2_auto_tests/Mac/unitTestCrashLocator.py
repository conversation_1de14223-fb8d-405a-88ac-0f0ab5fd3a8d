import os
import os.path
import subprocess
import tempfile
import sys
import getopt

dSymFileLoc = ""
minidumpLoc = ""
tempFolder = ""
dump_symsLoc = "../../../osx_libs/crashpad/bin/dump_syms"
mindump_stackwalkLoc = "../../../osx_libs/crashpad/bin/minidump_stackwalk"
usage = "Usage: python /path/to/unitTestCrashLocator.py --dSYM=/path/to/CPCPTest.dSYM --dump=/path/to/CPCTestCrash.dmp"

try:
    opts, args = getopt.getopt(sys.argv[1:], "h:sd", ["help", "dSYM=", "dump="])

    if(len(sys.argv) != 3):
        print usage
        sys.exit()

except getopt.GetoptError:
    print usage
    sys.exit(2)

for opt, arg in opts:
    if opt in ("-h","--help"):
        print usage
        sys.exit()
    elif opt in ("--dump"):
        print "dump is " + arg
        if os.path.exists(arg):
            minidumpLoc = arg
        else:
            sys.exit(2)
    elif opt in ("--dSYM"):
        print "dsym is " + arg
        if os.path.exists(arg):
            dSymFileLoc = arg
        else:
            sys.exit(2)

val = dSymFileLoc.split("/")
val = val[-1]
val = val.replace("dSYM", "sym")

tempFolder = tempfile.mkdtemp()
symFileLoc = os.path.join(tempFolder, val)

os.system(dump_symsLoc + ' ' + dSymFileLoc + ' > ' + symFileLoc)

# Extract the folder structure from the generated sym file.

with open(symFileLoc, 'r') as f:

    line = f.readline().split()
    debug_identifier = line[3]
    debug_file_name = line[4]

    # Generate the appropriate folder structure for minidump_stackwalk to work.

    os.system('mkdir ' + os.path.join(tempFolder,debug_file_name))
    os.system('mkdir ' + os.path.join(tempFolder, debug_file_name,debug_identifier))
    os.system('cp ' + symFileLoc + ' ' + os.path.join(tempFolder, debug_file_name, debug_identifier, debug_file_name + ".sym"))

# Execute minidump_stackwalk to get the required the line number of the crash.

os.system(mindump_stackwalkLoc + ' '  + minidumpLoc + ' ' + tempFolder + ' 2>&1')
