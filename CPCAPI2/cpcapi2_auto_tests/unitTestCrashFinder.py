import os
import os.path
import subprocess
import tempfile
import sys

if len(sys.argv) == 2 and sys.argv[1] == "--help":
    print "The arguments should be: python unitTestCrashFinder.py {location of minidump file} {location of the binary} {location of the dSYM file}"

else:
    if len(sys.argv) == 4:
        tempFolder = tempfile.mkdtemp()
        symFileLoc = tempFolder + "/tempFile.sym"

        minidumpLoc = sys.argv[1]
        binaryLoc = sys.argv[2]
        dSymFileLoc = sys.argv[3]

        dump_symsLoc = "../../osx_libs/crashpad/bin/dump_syms"
        mindump_stackwalkLoc = "../../osx_libs/crashpad/bin/minidump_stackwalk"

        os.system(dump_symsLoc + ' -a x86_64 ' + ' -g ' + dSymFileLoc + ' ' + binaryLoc + ' > ' + symFileLoc)

        # Extract the folder structure from the generated sym file.
        with open(symFileLoc, 'r') as f:
            line = f.readline().split()
            debug_file_name = line[4]
            debug_identifier = line[3]

            # Generate the appropriate folder structure for minidump_stackwalk to work.
            os.system('mkdir ' + tempFolder + '/' + debug_file_name)
            os.system('mkdir ' + tempFolder + '/' + debug_file_name + '/' + debug_identifier)
            os.system('cp ' + symFileLoc + ' ' + tempFolder + '/' + debug_file_name + '/' + debug_identifier + '/' + debug_file_name + ".sym")
            os.system('cp ' + minidumpLoc + ' ' + tempFolder)

        # Execute minidump_stackwalk to get the required the line number of the crash.
        os.system(mindump_stackwalkLoc + ' ' + minidumpLoc + ' ' + tempFolder +' 2>&1')

    else:
        print "The arguments entered are incorrect."
        print "The arguments should be: python unitTestCrashFinder.py {location of minidump file} {location of the binary} {location of the dSYM file}"
