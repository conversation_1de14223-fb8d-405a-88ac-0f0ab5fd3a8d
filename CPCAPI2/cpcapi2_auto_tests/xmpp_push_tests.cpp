#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#include "test_framework/xmpp_test_helper.h"
#include "xmpp/XmppAccountInterface.h"

#if (CPCAPI2_BRAND_XMPP_PUSH_MODULE == 1)

#if defined(__GNUC__) || defined(__clang__)
#pragma GCC diagnostic ignored "-Wdeprecated-declarations"
#endif

using namespace CPCAPI2;
using namespace CPCAPI2::XmppAccount;
using namespace CPCAPI2::XmppPush;

namespace {

class XmppPushModuleTest : public CpcapiAutoTest
{
public:
   XmppPushModuleTest() {}
   virtual ~XmppPushModuleTest() {}
   static void SetUpTestCase() {}
   static void TearDownTestCase() {}
   virtual void SetUp() {}
   virtual void TearDown() {}
};

#if (CPCAPI2_BRAND_XMPP_PUSH_MODULE == 1)
TEST_F(XmppPushModuleTest, XmppPushRegisterUnregister)
{
   XmppTestAccount alice("alice", Account_Init);
   alice.config.settings.domain = "tigase.im";
   alice.config.settings.username = "sdk-test";
   alice.config.settings.password = "sdk-test-123";
   alice.config.settings.port = 5222;
   alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings);
   alice.account->applySettings(alice.handle);
   alice.enable();

   cpc::string token = "testdevicetoken123";
   PushConfigEvent cevt;
   PushEvent evt;
   XmppAccountHandle h;
   ASSERT_TRUE(alice.events->expectEvent(__LINE__, "XmppPushHandler::onPushConfigured", 20000, CPCAPI2::test::AlwaysTruePred(), h, cevt));
   ASSERT_EQ(alice.handle, h);
   
   alice.xmppPush->pushRegister(alice.handle, token);
   ASSERT_TRUE(alice.events->expectEvent(__LINE__, "XmppPushHandler::onPushRegistered", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_EQ(evt.deviceToken, token);
   alice.xmppPush->enableNotifications(alice.handle);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   alice.xmppPush->disableNotifications(alice.handle);
   alice.xmppPush->pushUnregister(alice.handle, token);
   ASSERT_TRUE(alice.events->expectEvent(__LINE__, "XmppPushHandler::onPushUnregistered", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_EQ(evt.deviceToken, token);
}

TEST_F(XmppPushModuleTest, XmppPushSettings)
{
   XmppTestAccount alice("alice", Account_Init);
   alice.config.settings.domain = "tigase.im";
   alice.config.settings.username = "sdk-test";
   alice.config.settings.password = "sdk-test-123";
   alice.config.settings.port = 5222;
   alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings);
   alice.account->applySettings(alice.handle);
   alice.enable();

   cpc::string token = "testdevicetoken123";
   PushConfigEvent cevt;
   PushEvent evt;
   XmppAccountHandle h;
   ASSERT_TRUE(alice.events->expectEvent(__LINE__, "XmppPushHandler::onPushConfigured", 20000, CPCAPI2::test::AlwaysTruePred(), h, cevt));
   ASSERT_EQ(alice.handle, h);

   XmppPushSettings settings;
   settings.ignoreUnknownSender = true;
   settings.sendNotificationsWhileAway = true;
   settings.mutedContacts.push_back("<EMAIL>");
   settings.mutedContacts.push_back("<EMAIL>");
   GroupChatFilter gcf;
   gcf.jid = "<EMAIL>";
   gcf.nick = "pizzamaster";
   gcf.rule = GroupChatFilterRule::Mentioned;
   settings.groupChatFilter.push_back(gcf);
   gcf.jid = "<EMAIL>";
   gcf.rule = GroupChatFilterRule::Always;
   settings.groupChatFilter.push_back(gcf);
   
   alice.xmppPush->configurePushSettings(alice.handle, settings);
   alice.xmppPush->applyPushSettings(alice.handle);
   
   alice.xmppPush->pushRegister(alice.handle, token);
   ASSERT_TRUE(alice.events->expectEvent(__LINE__, "XmppPushHandler::onPushRegistered", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_EQ(evt.deviceToken, token);
   alice.xmppPush->enableNotifications(alice.handle);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   alice.xmppPush->disableNotifications(alice.handle);
   alice.xmppPush->pushUnregister(alice.handle, token);
   ASSERT_TRUE(alice.events->expectEvent(__LINE__, "XmppPushHandler::onPushUnregistered", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_EQ(evt.deviceToken, token);
}

TEST_F(XmppPushModuleTest, XmppPushBadProvider)
{
   XmppTestAccount alice("alice", Account_Init);
   alice.config.settings.domain = "tigase.im";
   alice.config.settings.username = "sdk-test";
   alice.config.settings.password = "sdk-test-123";
   alice.config.settings.port = 5222;
   alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings);
   alice.account->applySettings(alice.handle);
   alice.enable();

   cpc::string token = "testdevicetoken123";
   PushConfigEvent cevt;
   PushErrorEvent evt;
   XmppAccountHandle h;
   ASSERT_TRUE(alice.events->expectEvent(__LINE__, "XmppPushHandler::onPushConfigured", 20000, CPCAPI2::test::AlwaysTruePred(), h, cevt));
   ASSERT_EQ(alice.handle, h);

   XmppPushSettings settings;
   settings.ignoreUnknownSender = true;
   settings.sendNotificationsWhileAway = true;
   settings.mutedContacts.push_back("<EMAIL>");
   settings.mutedContacts.push_back("<EMAIL>");
   GroupChatFilter gcf;
   gcf.jid = "<EMAIL>";
   gcf.nick = "pizzamaster";
   gcf.rule = GroupChatFilterRule::Mentioned;
   settings.groupChatFilter.push_back(gcf);
   gcf.jid = "<EMAIL>";
   gcf.rule = GroupChatFilterRule::Always;
   settings.groupChatFilter.push_back(gcf);
   settings.provider = "BadProvider";
   
   alice.xmppPush->configurePushSettings(alice.handle, settings);
   alice.xmppPush->applyPushSettings(alice.handle);
   
   alice.xmppPush->pushRegister(alice.handle, token);
   ASSERT_TRUE(alice.events->expectEvent(__LINE__, "XmppPushHandler::onPushConfigError", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_EQ(evt.errorText, "Unsupported push provider In context 103");
   ASSERT_EQ(evt.errorCode, Error_AdhocDiscovery);
}

TEST_F(XmppPushModuleTest, XmppPushFilters)
{
   XmppTestAccount alice("alice", Account_Init);
   alice.config.settings.domain = "tigase.im";
   alice.config.settings.username = "sdk-test";
   alice.config.settings.password = "sdk-test-123";
   alice.config.settings.port = 5222;
   alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings);
   alice.account->applySettings(alice.handle);
   alice.enable();

   cpc::string token = "testdevicetoken123";
   PushConfigEvent cevt;
   PushEvent evt;
   XmppAccountHandle h;
   ASSERT_TRUE(alice.events->expectEvent(__LINE__, "XmppPushHandler::onPushConfigured", 20000, CPCAPI2::test::AlwaysTruePred(), h, cevt));
   ASSERT_EQ(alice.handle, h);

   XmppPushSettings settings;
   settings.ignoreUnknownSender = true;
   settings.sendNotificationsWhileAway = true;
   settings.mutedContacts.push_back("<EMAIL>");
   settings.mutedContacts.push_back("<EMAIL>");
   GroupChatFilter gcf;
   gcf.jid = "<EMAIL>";
   gcf.nick = "roomie";
   gcf.rule = GroupChatFilterRule::Mentioned;
   settings.groupChatFilter.push_back(gcf);
   gcf.jid = "<EMAIL>";
   gcf.rule = GroupChatFilterRule::Always;
   settings.groupChatFilter.push_back(gcf);
   
   alice.xmppPush->configurePushSettings(alice.handle, settings);
   alice.xmppPush->applyPushSettings(alice.handle);
   
   alice.xmppPush->pushRegister(alice.handle, token);
   ASSERT_TRUE(alice.events->expectEvent(__LINE__, "XmppPushHandler::onPushRegistered", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_EQ(evt.deviceToken, token);
   alice.xmppPush->enableNotifications(alice.handle);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.xmppPush->setIgnoreUnknownSender(alice.handle, false);
   alice.xmppPush->setSendNotificationsWhileAway(alice.handle, false);
   alice.xmppPush->setContactMuted(alice.handle, "<EMAIL>", false);
   alice.xmppPush->setGroupChatFilter(alice.handle, "<EMAIL>", GroupChatFilterRule::Never);
   alice.xmppPush->enableNotifications(alice.handle);
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   alice.xmppPush->disableNotifications(alice.handle);
   alice.xmppPush->pushUnregister(alice.handle, token);
   ASSERT_TRUE(alice.events->expectEvent(__LINE__, "XmppPushHandler::onPushUnregistered", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_EQ(evt.deviceToken, token);
}

TEST_F(XmppPushModuleTest, XmppPushMucRegistration)
{
   XmppTestAccount alice("alice", Account_Init);
   alice.config.settings.domain = "tigase.im";
   alice.config.settings.username = "sdk-test";
   alice.config.settings.password = "sdk-test-123";
   alice.config.settings.port = 5222;
   alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings);
   alice.account->applySettings(alice.handle);
   alice.enable();

   cpc::string token = "testdevicetoken123";
   PushConfigEvent cevt;
   PushEvent evt;
   PushMucRegistrationEvent revt;
   XmppAccountHandle h;
   ASSERT_TRUE(alice.events->expectEvent(__LINE__, "XmppPushHandler::onPushConfigured", 20000, CPCAPI2::test::AlwaysTruePred(), h, cevt));
   ASSERT_EQ(alice.handle, h);

   std::string mucRoom = "a1b2c3";

   XmppPushSettings settings;
   GroupChatFilter gcf;
   gcf.jid = (mucRoom + "@muc.tigase.im").c_str();
   gcf.nick = "sdk-unit-test";
   gcf.rule = GroupChatFilterRule::Mentioned;
   settings.groupChatFilter.push_back(gcf);

   alice.xmppPush->configurePushSettings(alice.handle, settings);
   alice.xmppPush->applyPushSettings(alice.handle);

   alice.xmppPush->pushRegister(alice.handle, token);
   ASSERT_TRUE(alice.events->expectEvent(__LINE__, "XmppPushHandler::onPushRegistered", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_EQ(evt.deviceToken, token);
   alice.xmppPush->enableNotifications(alice.handle);

   XmppMultiUserChat::XmppMultiUserChatHandle aliceChat = alice.mucManager->create(alice.handle);
   alice.mucManager->join(aliceChat, gcf.jid, gcf.nick);

   XmppMultiUserChat::XmppMultiUserChatHandle much;
   XmppMultiUserChat::MultiUserChatReadyEvent mucevt;
   ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 5000, CPCAPI2::test::AlwaysTruePred(), much, mucevt));
   ASSERT_EQ(aliceChat, much);
   ASSERT_EQ(mucevt.room, mucRoom.c_str());
   ASSERT_TRUE(!mucevt.isNewRoom);
   
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   alice.xmppPush->registerMucOfflineMessageDelivery(alice.handle, gcf.jid, gcf.nick);
   
   ASSERT_TRUE(alice.events->expectEvent(__LINE__, "XmppPushHandler::onMucRegistrationResult", 20000, CPCAPI2::test::AlwaysTruePred(), h, revt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_EQ(true, revt.success);
   ASSERT_EQ(gcf.jid, revt.roomJid);

   alice.xmppPush->disableNotifications(alice.handle);
   alice.xmppPush->pushUnregister(alice.handle, token);
   ASSERT_TRUE(alice.events->expectEvent(__LINE__, "XmppPushHandler::onPushUnregistered", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_EQ(evt.deviceToken, token);
}

TEST_F(XmppPushModuleTest, XmppPushDisableAccountBeforeOnPushUnregistered)
{
   XmppTestAccount alice("alice", Account_Init);
   alice.config.settings.domain = "tigase.im";
   alice.config.settings.username = "sdk-test";
   alice.config.settings.password = "sdk-test-123";
   alice.config.settings.port = 5222;
   alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings);
   alice.account->applySettings(alice.handle);
   alice.enable();

   cpc::string token = "testdevicetoken123";
   PushConfigEvent cevt;
   PushEvent evt;
   XmppAccountHandle h;
   ASSERT_TRUE(alice.events->expectEvent(__LINE__, "XmppPushHandler::onPushConfigured", 20000, CPCAPI2::test::AlwaysTruePred(), h, cevt));
   ASSERT_EQ(alice.handle, h);
   
   alice.xmppPush->pushRegister(alice.handle, token);
   ASSERT_TRUE(alice.events->expectEvent(__LINE__, "XmppPushHandler::onPushRegistered", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_EQ(evt.deviceToken, token);
   alice.xmppPush->enableNotifications(alice.handle);

   alice.xmppPush->disableNotifications(alice.handle);
   alice.xmppPush->pushUnregister(alice.handle, token);
}

TEST_F(XmppPushModuleTest, XmppPushAccountDisableEnable)
{
   XmppTestAccount alice("alice", Account_Init);
   alice.config.settings.domain = "tigase.im";
   alice.config.settings.username = "sdk-test";
   alice.config.settings.password = "sdk-test-123";
   alice.config.settings.port = 5222;
   alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings);
   alice.account->applySettings(alice.handle);
   alice.enable();

   cpc::string token = "testdevicetoken123";
   PushConfigEvent cevt;
   PushEvent evt;
   XmppAccountHandle h;
   ASSERT_TRUE(alice.events->expectEvent(__LINE__, "XmppPushHandler::onPushConfigured", 20000, CPCAPI2::test::AlwaysTruePred(), h, cevt));
   ASSERT_EQ(alice.handle, h);
   
   alice.xmppPush->pushRegister(alice.handle, token);
   ASSERT_TRUE(alice.events->expectEvent(__LINE__, "XmppPushHandler::onPushRegistered", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_EQ(evt.deviceToken, token);
   alice.xmppPush->enableNotifications(alice.handle);

   alice.xmppPush->disableNotifications(alice.handle);
   alice.xmppPush->pushUnregister(alice.handle, token);
   alice.disable();

   alice.enable();
   
   alice.xmppPush->pushRegister(alice.handle, token);
   ASSERT_TRUE(alice.events->expectEvent(__LINE__, "XmppPushHandler::onPushRegistered", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_EQ(evt.deviceToken, token);
   alice.xmppPush->enableNotifications(alice.handle);

   alice.xmppPush->disableNotifications(alice.handle);
   alice.xmppPush->pushUnregister(alice.handle, token);
}
#endif
}  // namespace

#endif
