#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#include "test_framework/xmpp_test_helper.h"
#include "cpcapi2_test_fixture.h"

#include "xmpp/XmppAccountInterface.h"
#include "xmpp/XmppAccountJsonApi.h"
#include "xmpp/XmppChatJsonApi.h"
#include "xmpp/XmppRosterJsonApi.h"
#include "xmpp_agent/XmppAgentHandler.h"
#include "xmpp_agent/XmppAgentJsonApi.h"
#include "../../impl/auth_server/AuthServerJwtUtils.h"
#include "phone/PhoneInterface.h"

#include "xmpp/XmppConnection.h"

#include <atomic>
#include <boost/asio.hpp>
#include <rutil/Socket.hxx>
#include <rutil/dns/ares/ares.h>
#include <rutil/dns/ares/ares_dns.h>

#ifndef ANDROID
//define USE_CPCAPI2_JSON_TESTS 1
#endif


#if (CPCAPI2_BRAND_XMPP_ACCOUNT_MODULE == 1)

using namespace CPCAPI2;
using namespace CPCAPI2::XmppAccount;
using namespace CPCAPI2::JsonApi;
using namespace CPCAPI2::PushEndpoint;

namespace {

static std::atomic<bool> brainDeadServerDidStart(false);
static std::atomic<int>  brainDeadServerPort(0);
static std::atomic<bool> brainDeadServerShouldQuit(false);
static std::future<void> startBrainDeadServer(int serverPort)
{
   brainDeadServerDidStart = false;
   brainDeadServerShouldQuit = false;
   return std::async(std::launch::async, [] (int serverPort) {
#if _WIN32
      SOCKET listenFd;
#else
      int listenFd;
#endif // _WIN32
      struct sockaddr_in servaddr;

      listenFd=::socket(AF_INET,SOCK_STREAM,0);

      for (int tries = 0; tries <= 5; ++tries)
      {
         memset(&servaddr, 0, sizeof(servaddr));
         //bzero(&servaddr, sizeof(servaddr));
         servaddr.sin_family = AF_INET;
         servaddr.sin_addr.s_addr=htonl(INADDR_ANY);
         servaddr.sin_port=htons(serverPort);
         int ret = ::bind(listenFd,(struct sockaddr *)&servaddr, sizeof(servaddr));
         if (ret != 0)
         {
            std::cout << "bind for startBrainDeadServer failed; trying next port" << std::endl;
            ++serverPort;
         }
         else
         {
            break;
         }
      }

      brainDeadServerPort = serverPort;

      // don't need to listen ::listen(listenFd, 1024);
      brainDeadServerDidStart = true;

      // don't accept new connection; just wait until test is finished
      while (!brainDeadServerShouldQuit)
      {
         std::this_thread::sleep_for(std::chrono::milliseconds(500));
      }

      resip::closeSocket(listenFd);
   },
   serverPort);
}

class XmppAccountModuleTest : public CpcapiAutoTest
{
public:
   XmppAccountModuleTest() {}
   virtual ~XmppAccountModuleTest() {}
};

TEST_F(XmppAccountModuleTest, XmppEnableDisable)
{
   XmppTestAccount alice("alice");
}

TEST_F(XmppAccountModuleTest, DecodeProvisioning)
{
   XmppTestAccount alice("alice");
   std::ifstream in((TestEnvironmentConfig::testResourcePath() + "provisioningtests.json").c_str());
   assert(in.is_open());

   std::ostringstream iss;
   iss << in.rdbuf() << std::flush;

   cpc::string doc = iss.str().c_str();
   cpc::vector<XmppAccountSettings> accounts;

   alice.account->decodeProvisioningResponse(doc, accounts);
   ASSERT_EQ(accounts[0].username, "test_alice");
   ASSERT_EQ(accounts[0].domain, "autotest.net");
   ASSERT_EQ(accounts[0].password, "123");
   ASSERT_EQ(accounts[0].port, 0);
   ASSERT_EQ(accounts[0].priority, 0);
   ASSERT_EQ(accounts[0].softwareName, "CPCAPI2-based Client");
   ASSERT_EQ(accounts[0].softwareVersion, "1.0");
   ASSERT_EQ(accounts[0].identityCategory, "client");
   ASSERT_EQ(accounts[0].identityType, "phone");
   ASSERT_EQ(accounts[0].connectTimeOut, 10);
   ASSERT_EQ(accounts[0].keepAliveTime, 30);
   ASSERT_EQ(accounts[0].usePingKeepAlive, false);
   ASSERT_EQ(accounts[0].enableLocalSocks5Proxy, true);
   ASSERT_EQ(accounts[0].enableRemoteStreamHostDiscovery, true);
   ASSERT_EQ(accounts[0].sslVersion, XmppAccount::TLS_V1_0);
   ASSERT_EQ(accounts[0].ignoreCertVerification, false);

   ASSERT_EQ(accounts[0].additionalCertPeerNames.size(), 2);
   ASSERT_EQ(accounts[0].additionalCertPeerNames[0], "AutoTestAdditionalCertPeerName01");
   ASSERT_EQ(accounts[0].additionalCertPeerNames[1], "AutoTestAdditionalCertPeerName02");
   ASSERT_EQ(accounts[0].acceptedCertPublicKeys.size(), 2);
   ASSERT_EQ(accounts[0].acceptedCertPublicKeys[0], "AutoTestAcceptedCertPublicKey01");
   ASSERT_EQ(accounts[0].acceptedCertPublicKeys[1], "AutoTestAcceptedCertPublicKey02");

   ASSERT_EQ(accounts[0].logXmppStanzas, true);
   ASSERT_EQ(accounts[0].ipVersion, IpVersion_V4);

   ASSERT_EQ(accounts[0].nameServers.size(), 1);
   ASSERT_EQ(accounts[0].nameServers[0], "0.0.0.0");

   ASSERT_EQ(accounts[0].enableStreamManagement, true);
   ASSERT_EQ(accounts[0].enableStreamResumption, false);
   ASSERT_EQ(accounts[0].streamManagementSequence, 0);
   ASSERT_EQ(accounts[0].publishInitialPresenceAsAvailable, true);
   ASSERT_EQ(accounts[0].fallbackOnResourceConflict, false);
   ASSERT_EQ(accounts[0].enableCompression, true);
   ASSERT_EQ(accounts[0].enableXmppPresence, true);
   ASSERT_EQ(accounts[0].enableXmppStanza, true);
}

TEST_F(XmppAccountModuleTest, XmppEnableDestroy)
{
   XmppTestAccount alice("alice");
   alice.destroy();
}

TEST_F(XmppAccountModuleTest, XmppEnableDestroyBeforeDisable)
{
   XmppTestAccount alice("alice");
   ASSERT_EQ(alice.account->destroy(alice.handle), kSuccess);

   XmppAccountHandle h;
   XmppAccount::ErrorEvent evt;
   ASSERT_TRUE(alice.events->expectEvent("XmppAccountHandler::onError", 2000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_EQ(evt.errorText, "Not disconnected");

   alice.destroy();
}

TEST_F(XmppAccountModuleTest, XmppEnableShutdown) {
   XmppTestAccount *alice = new XmppTestAccount("alice");
   alice->mucManager->getRoomList(alice->handle);

   auto start = std::chrono::system_clock::now();
   delete alice;
   auto end = std::chrono::system_clock::now();

   std::chrono::duration<double> diff = end-start;

   // r155921 introduced an extra 2 seconds of delay in the call to XmppTestAccount::disable()
   // Additional delay in handling account destroy, rather than just disable
   const int XmppTestAccountAddedDelaySec = 4;
   ASSERT_GE(3 + XmppTestAccountAddedDelaySec, diff.count()) << "SDK should not take this long to shutdown (seconds)";
}

TEST_F(XmppAccountModuleTest, XmppEnableDisableLoop) {
   XmppTestAccount alice("alice", Account_Init);
   for (int i=0; i<3; ++i)
   {
      alice.enable();
      alice.disable();
   }
}

TEST_F(XmppAccountModuleTest, XmppQuickDisableEnable) {
   XmppTestAccount alice("alice", Account_Init);

   alice.account->disable(alice.handle);

   // don't wait for any account status events
   alice.account->enable(alice.handle);

   XmppAccountHandle h;
   XmppAccountStatusChangedEvent evt;
   for (;;)
   {
      ASSERT_TRUE(alice.events->expectEvent(__LINE__, "XmppAccountHandler::onAccountStatusChanged", 10000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      safeCout("Received XMPP account status change event for status " << evt.accountStatus);
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status_Connected)
      {
         break;
      }
   }

   alice.disable();
}

TEST_F(XmppAccountModuleTest, XmppQuickDisableEnableTwice) {
   XmppTestAccount alice("alice", Account_Init);

   alice.account->disable(alice.handle);
   alice.account->enable(alice.handle);
   alice.account->disable(alice.handle);
   alice.account->enable(alice.handle);

   assertXmppDisconnecting(alice);
   assertXmppDisconnected(alice);
   assertXmppDisconnecting(alice);
   assertXmppDisconnected(alice);
   assertXmppConnecting(alice);
   assertXmppConnected(alice);

   alice.disable();
}

TEST_F(XmppAccountModuleTest, XmppQuickEnableDisable) {
   XmppTestAccount alice("alice", Account_Init);

   alice.account->enable(alice.handle);

   // don't wait for any account status events
   alice.account->disable(alice.handle);

   assertXmppDisconnecting(alice);
   assertXmppDisconnected(alice);

   XmppAccountHandle h;
   XmppAccountStatusChangedEvent evt;

   ASSERT_FALSE(alice.events->expectEvent(__LINE__, "XmppAccountHandler::onAccountStatusChanged", 10000, CPCAPI2::test::AlwaysTruePred(), h, evt));

   alice.destroy(false); // without this we'll get test failure because the next event is not as expected
}

TEST_F(XmppAccountModuleTest, XmppQuickEnableDisableTwice) {
   XmppTestAccount alice("alice", Account_Init);

   alice.account->enable(alice.handle);
   alice.account->disable(alice.handle);
   alice.account->enable(alice.handle);
   alice.account->disable(alice.handle);

   assertXmppDisconnecting(alice);
   assertXmppDisconnected(alice);
   assertXmppDisconnecting(alice);
   assertXmppDisconnected(alice);

   XmppAccountHandle h;
   XmppAccountStatusChangedEvent evt;

   ASSERT_FALSE(alice.events->expectEvent(__LINE__, "XmppAccountHandler::onAccountStatusChanged", 10000, CPCAPI2::test::AlwaysTruePred(), h, evt));

   alice.destroy(false); // without this we'll get test failure because the next event is not as expected
}

TEST_F(XmppAccountModuleTest, XmppHostNotFound) {
   XmppTestAccount alice("alice", Account_NoInit);
   // should try to use our built-in auto tests DNS server, such that timing is consistent
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.domain = "nowhere.null";
   alice.init();
   alice.account->enable(alice.handle);
   assertXmppConnecting(alice);
   assertXmppFailure(alice);
   alice.account->disable(alice.handle);
   assertXmppDisconnecting(alice);
   assertXmppDisconnected(alice);
}

TEST_F(XmppAccountModuleTest, XmppConnectTimeout) {
   std::srand(std::time(0));
   int serverPort = 32000 + (std::rand() % 1001);

   // start thread to create TCP listener but don't accept connection
   auto serverHandle = startBrainDeadServer(serverPort);

   while (!brainDeadServerDidStart)
   {
      std::this_thread::sleep_for(std::chrono::milliseconds(500));
   }

   serverPort = brainDeadServerPort;

   XmppTestAccount alice("alice", Account_NoInit);
   alice.config.settings.domain = "127.0.0.1";
   alice.config.settings.port = serverPort;
   alice.config.settings.connectTimeOut = 5;

   alice.init();
   alice.account->enable(alice.handle);
   assertXmppConnecting(alice);

   const unsigned int waitMs = (alice.config.settings.connectTimeOut + 2) * 1000;
   XmppAccountHandle h;
   XmppAccountStatusChangedEvent evt;
   // use expectEvent directly so we can specify our own timeout
   ASSERT_TRUE(alice.events->expectEvent(__LINE__, "XmppAccountHandler::onAccountStatusChanged", waitMs, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_EQ(CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status_Failure, evt.accountStatus);

   alice.account->disable(alice.handle);
   // destroy but don't check events; the XMPP account has a random reconnect timer, so events
   // are a bit unpredictable in this case
   alice.destroy(false);

   brainDeadServerShouldQuit = true;
   serverHandle.wait();
}

TEST_F(XmppAccountModuleTest, DISABLED_XmppCancelConnect) {
   std::srand(std::time(0));
   int serverPort = 32000 + (std::rand() % 1001);

   // start thread to create TCP listener but don't accept connection
   auto serverHandle = startBrainDeadServer(serverPort);

   while (!brainDeadServerDidStart)
   {
      std::this_thread::sleep_for(std::chrono::milliseconds(500));
   }

   serverPort = brainDeadServerPort;

   XmppTestAccount alice("alice", Account_NoInit);
   alice.config.settings.domain = "127.0.0.1";
   alice.config.settings.port = serverPort;

   alice.init();
   alice.account->enable(alice.handle);
   assertXmppConnecting(alice);
   assertXmppFailure(alice);
   alice.account->disable(alice.handle);
   assertXmppDisconnecting(alice);
   assertXmppDisconnected(alice);
   brainDeadServerShouldQuit = true;
   serverHandle.wait();
}

TEST_F(XmppAccountModuleTest, XmppApplySettings) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob", Account_NoInit);

   XmppAccountSettings& settings = bob.config.settings;
   alice.account->configureDefaultAccountSettings(alice.handle, settings);
   alice.account->applySettings(alice.handle);

   assertXmppDisconnecting(alice);
   assertXmppDisconnected(alice);
   assertXmppConnecting(alice);
   assertXmppConnected(alice);
}

TEST_F(XmppAccountModuleTest, XmppLogXmppStanzaPostAccountEnable) {
   XmppTestAccount alice("alice", Account_NoInit);
   alice.config.settings.logXmppStanzas = false;
   alice.init();
   alice.enable();

   alice.config.settings.logXmppStanzas = true;
   alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings);
   alice.account->applySettings(alice.handle);

   assertXmppDisconnecting(alice);
   assertXmppDisconnected(alice);
   assertXmppConnecting(alice);
   assertXmppConnected(alice);
}

TEST_F(XmppAccountModuleTest, DISABLED_IPVersion) {
   XmppTestAccount alice("alice", Account_Init);
   XmppAccountSettings& settings = alice.config.settings;

   auto changeIPVersion = [&](CPCAPI2::XmppAccount::IpVersion ipVersion) {
      settings.ipVersion = ipVersion;

      alice.account->configureDefaultAccountSettings(alice.handle, settings);
      alice.account->applySettings(alice.handle);
#if 0
      assertXmppDisconnecting(alice);
      assertXmppDisconnected(alice);
      assertXmppConnecting(alice);
      assertXmppConnected(alice);
#else
      alice.enable();
#endif

   };

   //changeIPVersion(IpVersion_V4); // setting is not changed from default
   //changeIPVersion(IpVersion_V6);
   //changeIPVersion(IpVersion_Auto_PreferV4);
   changeIPVersion(CPCAPI2::XmppAccount::IpVersion_Auto_PreferV6);
}

TEST_F(XmppAccountModuleTest, DISABLED_XmppKeepAliveTime) { // need to manually check ping interval
   XmppTestAccount alice("alice", Account_Init);
   alice.config.settings.keepAliveTime = 1;
   alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings);
   alice.account->applySettings(alice.handle);
   alice.enable();

   std::this_thread::sleep_for(std::chrono::seconds(30));

   alice.config.settings.keepAliveTime = 3;
   alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings);
   alice.account->applySettings(alice.handle);

   std::this_thread::sleep_for(std::chrono::seconds(30));
}

TEST_F(XmppAccountModuleTest, Reconnect) {
   XmppTestAccount alice("alice");

   alice.closeSessions();

   {
      XmppAccountHandle h;
      XmppAccountStatusChangedEvent evt;
      ASSERT_TRUE(alice.events->expectEvent("XmppAccountHandler::onAccountStatusChanged",
         60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status_Failure, evt.accountStatus);
   }

   {
      XmppAccountHandle h;
      XmppAccountStatusChangedEvent evt;

      for (int i = 0;; ++i)
      {
         assertXmppConnecting(alice);

         ASSERT_TRUE(alice.events->expectEvent("XmppAccountHandler::onAccountStatusChanged",
            120000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(alice.handle, h);

         if (evt.accountStatus == CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status_Failure)
         {
            safeCout("Connect failed with code " << evt.errorCode << ": " << (evt.errorText));
            ASSERT_TRUE(i < 10);
         }
         else break;
      }
      ASSERT_EQ(CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status_Connected, evt.accountStatus);
   }
}

TEST_F(XmppAccountModuleTest, NetworkChangeLoseNetwork)
{
   XmppTestAccount alice("alice");;

   alice.network->setNetworkTransport(NetworkTransport::TransportNone);
   std::set<resip::Data> ifaces;
   alice.network->setMockInterfaces(ifaces);
   assertXmppDisconnecting(alice);
   assertXmppDisconnected(alice);

   alice.network->setNetworkTransport(NetworkTransport::TransportWiFi);
   ifaces.clear();
   ifaces.insert("********");
   alice.network->setMockInterfaces(ifaces);
   assertXmppDisconnecting(alice); // strange -- why does the XMPP module fire these again?
   assertXmppDisconnected(alice);
   assertXmppConnecting(alice);
   assertXmppConnected(alice);
}

// this test won't work currently -- the XMPP module
// does its own real detection of network interface used
TEST_F(XmppAccountModuleTest, DISABLED_NetworkChangeSwitchNetwork)
{
   XmppTestAccount alice("alice");;

   std::set<resip::Data> ifaces;
   ifaces.insert("********");
   alice.network->setMockInterfaces(ifaces);
   alice.network->setNetworkTransport(NetworkTransport::TransportWiFi);
   assertXmppDisconnecting(alice);
   assertXmppDisconnected(alice);

   ifaces.clear();
   ifaces.insert("********");
   alice.network->setMockInterfaces(ifaces);
   alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
   assertXmppDisconnecting(alice); // strange -- why does the XMPP module fire these again?
   assertXmppDisconnected(alice);
   assertXmppConnecting(alice);
   assertXmppConnected(alice);
}

TEST_F(XmppAccountModuleTest, DISABLED_XmppUsingProxy) {
   /*
    * Needs a test server that is reachable by a name/address other than the
    * domain or domain-derived name/address identified using DNS SRV resolution.
    */
   XmppTestAccount alice("alice", Account_NoInit);
   alice.config.settings.domain = "ryans-mac-mini.local";
   alice.config.settings.proxy = "************";
   alice.init();
   alice.enable();
}

TEST_F(XmppAccountModuleTest, DISABLED_ValidateCertificates) {
   // Double check certificate status if any of these fail
   XmppTestAccount blah("blah", Account_NoInit);
   blah.config.settings.username = "user001";
   blah.config.settings.domain = "blah.im";
   blah.config.settings.password = "user001";
   blah.config.settings.ignoreCertVerification = false;
   blah.init();
   blah.enable();

   /*
   XmppTestAccount chilli("chilli", Account_NoInit);
   chilli.config.settings.username = "user001";
   chilli.config.settings.domain = "jabber.hot-chilli.net";
   chilli.config.settings.password = "user001";
   chilli.config.settings.ignoreCertVerification = false;
   chilli.init();
   chilli.enable();

   XmppTestAccount jp("jp", Account_NoInit);
   jp.config.settings.username = "user001";
   jp.config.settings.domain = "xmpp.jp";
   jp.config.settings.password = "user001";
   jp.config.settings.ignoreCertVerification = false;
   jp.init();
   jp.enable();

   XmppTestAccount cp("cp", Account_NoInit);
   cp.config.settings.username = "myusername";
   cp.config.settings.domain = "counterpath.com";
   cp.config.settings.password = "mypassword";
   cp.config.settings.ignoreCertVerification = false;
   cp.init();
   cp.enable();
   */
}

TEST_F(XmppAccountModuleTest, InvalidCredential) {
   XmppTestAccount alice("alice", Account_NoInit);
   alice.config.settings.username = "invalid";
   alice.config.settings.password = "invalid";
   alice.init();

   for (int i = 0; i < 5; ++i)
   {
      alice.enable(true, NULL, false);   // don't check registration state as we're doing that below
      assertXmppConnecting(alice);

      XmppAccountHandle h;
      XmppAccountStatusChangedEvent evt;
      ASSERT_TRUE(alice.events->expectEvent("XmppAccountHandler::onAccountStatusChanged",
         20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(evt.accountStatus, CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status_Failure);
      ASSERT_TRUE(evt.errorCode == Error_AuthenticationFailed || evt.errorCode == Error_UnsupportedAuthMech);

      alice.disable();
   }
}

#if (CPCAPI2_BRAND_NETWORK_CHANGE_MODULE == 1)
TEST_F(XmppAccountModuleTest, OnOffRestrictedNetwork) {
   XmppTestAccount alice("alice");

   PhoneInterface* pi = dynamic_cast<PhoneInterface*>(alice.phone);
   NetworkChangeManager_Mock* network = dynamic_cast<NetworkChangeManagerInterface*>(NetworkChangeManager::getInterface(pi))->getMockImpl();
   ASSERT_EQ(network->networkTransport(), TransportWiFi);

   // Blacklist TransportWWAN and make it the new network => account should disconnect
   alice.account->setNetworkRestriction(alice.handle, CPCAPI2::TransportWWAN, true);
   network->setNetworkTransport(NetworkTransport::TransportWWAN);
   std::set<resip::Data> ifaces;
   ifaces.insert("**************");
   network->setMockInterfaces(ifaces);
   std::this_thread::sleep_for(std::chrono::milliseconds(10000));
   assertXmppDisconnecting(alice);
   assertXmppDisconnected(alice);

   // Remove network restriction => account should re-connect
   alice.account->setNetworkRestriction(alice.handle, CPCAPI2::TransportWWAN, false);
   assertXmppDisconnecting(alice);
   assertXmppDisconnected(alice);
   assertXmppConnecting(alice);
   assertXmppConnected(alice);
}
#endif

#if (CPCAPI2_BRAND_NETWORK_CHANGE_MODULE == 1)
TEST_F(XmppAccountModuleTest, EnableWithNetworkRestriction) {
   XmppTestAccount alice("alice", Account_Init);

   PhoneInterface* pi = dynamic_cast<PhoneInterface*>(alice.phone);
   NetworkChangeManager_Mock* network = dynamic_cast<NetworkChangeManagerInterface*>(NetworkChangeManager::getInterface(pi))->getMockImpl();
   ASSERT_EQ(network->networkTransport(), TransportWiFi);
   alice.account->setNetworkRestriction(alice.handle, CPCAPI2::TransportWiFi, true);
   ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);

   {
      XmppAccountHandle h;
      XmppAccount::ErrorEvent evt;
      ASSERT_TRUE(alice.events->expectEvent("XmppAccountHandler::onError",
                                         5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(evt.errorText, "Restricted network");
   }

   alice.account->disable(alice.handle);
   assertXmppDisconnecting(alice);
   assertXmppDisconnected(alice);
}
#endif

// disabled -- server is not responding as of this commit
TEST_F(XmppAccountModuleTest, DISABLED_XmppTlsFailNameIncorrect)
{
   XmppTestAccount alice("alice", Account_NoInit);
   alice.config.settings.domain = "104.251.100.203"; // this would fail because cert is for jabber.diesel.counterpath.net
   alice.config.settings.port = 5224;
   alice.config.settings.ignoreCertVerification = false;
   alice.init();
   // Perform registration
   ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);

   assertXmppConnecting(alice);
   assertXmppFailure(alice);

   alice.account->disable(alice.handle);
   assertXmppDisconnecting(alice);
   assertXmppDisconnected(alice);
}

TEST_F(XmppAccountModuleTest, DISABLED_XmppTlsFailCertificateUnknown)
{
   XmppTestAccount alice("alice", Account_NoInit);
   alice.config.settings.username = "test5";
   alice.config.settings.password = "test1234";
   alice.config.settings.domain = "ccsdev-chat.mobilevoiplive.com";
   alice.init();
   // Perform registration
   ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);

   XmppAccountHandle h;
   XmppAccountStatusChangedEvent evt;

   assertXmppConnecting(alice);

   ASSERT_TRUE(alice.events->expectEvent(__LINE__, "XmppAccountHandler::onAccountStatusChanged", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_EQ(CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status_Connected, evt.accountStatus);
   ASSERT_EQ(CPCAPI2::XmppAccount::XmppTLSConnectionInfo::CertificateStatus_Ok, evt.tlsInfo.certificateStatus);

   alice.config.settings.requiredCertPublicKeys.push_back("bogus");
   ASSERT_EQ(alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings), kSuccess);
   ASSERT_EQ(alice.account->applySettings(alice.handle), kSuccess);

   assertXmppDisconnecting(alice);
   assertXmppDisconnected(alice);
   assertXmppConnecting(alice);
   assertXmppFailure(alice);
}

TEST_F(XmppAccountModuleTest, DISABLED_XmppTlsPassValidcertificate) {
   XmppTestAccount alice("alice", Account_NoInit);
   alice.config.settings.username = "test5";
   alice.config.settings.password = "test1234";
   alice.config.settings.domain = "ccsdev-chat.mobilevoiplive.com";
   alice.init();
   alice.enable();
}

TEST_F(XmppAccountModuleTest, XmppTlsVersion) {
   XmppAccountHandle h;
   XmppAccountStatusChangedEvent evt;

   XmppTestAccount alice("alice", Account_Init);

   auto routine = [&](XmppAccount::SSLVersion sslVersion, std::function<void(const XmppAccountStatusChangedEvent&)> validator) {
      alice.config.settings.sslVersion = sslVersion;
      alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings);
      alice.account->applySettings(alice.handle);
      alice.enable(true, NULL, false);   // don't check registration state as we're doing that below

      ASSERT_TRUE(alice.events->expectEvent(__LINE__, "XmppAccountHandler::onAccountStatusChanged", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status_Connecting, evt.accountStatus);

      ASSERT_TRUE(alice.events->expectEvent(__LINE__, "XmppAccountHandler::onAccountStatusChanged", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      //ASSERT_EQ(CPCAPI2::XmppAccount::XmppTLSConnectionInfo::CertificateStatus_Ok, evt.tlsInfo.certificateStatus);
      validator(evt);
      alice.disable();
   };

#if 0 // bliu: inconsistent result
   routine(SSL_V2, [&](const XmppAccountStatusChangedEvent& evt) {
      ASSERT_EQ(CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status_Failure, evt.accountStatus);
      ASSERT_EQ(evt.tlsInfo.protocol, "SSLv2");
   });
#endif

#if 0 // bliu: Openfire doesn't support this
   routine(SSL_V3, [&](const XmppAccountStatusChangedEvent& evt) {
      ASSERT_EQ(CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status_Connected, evt.accountStatus);
      ASSERT_EQ(evt.tlsInfo.protocol, "SSLv3");
   });
#endif

   routine(CPCAPI2::XmppAccount::TLS_V1_0, [&](const XmppAccountStatusChangedEvent& evt) {
      ASSERT_EQ(CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status_Connected, evt.accountStatus);
      ASSERT_EQ(evt.tlsInfo.protocol, "TLSv1");
   });

   routine(CPCAPI2::XmppAccount::TLS_V1_1, [&](const XmppAccountStatusChangedEvent& evt) {
      ASSERT_EQ(CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status_Connected, evt.accountStatus);
      ASSERT_EQ(evt.tlsInfo.protocol, "TLSv1.1");
   });

   routine(CPCAPI2::XmppAccount::TLS_V1_2, [&](const XmppAccountStatusChangedEvent& evt) {
      ASSERT_EQ(CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status_Connected, evt.accountStatus);
      ASSERT_EQ(evt.tlsInfo.protocol, "TLSv1.2");
   });

   routine(CPCAPI2::XmppAccount::SSL_HIGHEST, [&](const XmppAccountStatusChangedEvent& evt) {
      ASSERT_EQ(CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status_Connected, evt.accountStatus);
      ASSERT_EQ(evt.tlsInfo.protocol, "TLSv1.2");
   });
}

TEST_F(XmppAccountModuleTest, DISABLED_Hiberation) {
   XmppTestAccount alice("alice");
   alice.account->setHibernationState(alice.handle, true);
   std::this_thread::sleep_for(std::chrono::seconds(30));
   alice.account->setHibernationState(alice.handle, false);
   std::this_thread::sleep_for(std::chrono::seconds(30));
   alice.disable();
}

TEST_F(XmppAccountModuleTest, GetServerTime) {
   XmppTestAccount alice("alice");
   std::this_thread::sleep_for(std::chrono::seconds(2));

   alice.account->getEntityTime(alice.handle);

   EntityTimeEvent evt;
   XmppAccountHandle h;
   ASSERT_TRUE(alice.events->expectEvent(__LINE__, "XmppAccountHandler::onEntityTime", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_NE(evt.timestamp, 0);

   time_t t = evt.timestamp;
   std::cout << ctime(&t) << std::endl;
}

TEST_F(XmppAccountModuleTest, DISABLED_GetEntityTime) {
   XmppTestAccount alice("alice");
   std::this_thread::sleep_for(std::chrono::seconds(2));

   alice.account->getEntityTime(alice.handle, alice.config.bare());

   EntityTimeEvent evt;
   XmppAccountHandle h;
   ASSERT_TRUE(alice.events->expectEvent(__LINE__, "XmppAccountHandler::onEntityTime", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_NE(evt.timestamp, 0);

   time_t t = evt.timestamp;
   std::cout << ctime(&t) << std::endl;
}

TEST_F(XmppAccountModuleTest, DISABLED_ServerEntityFeature) {
   XmppTestAccount alice("alice");

   EntityFeatureEvent evt;
   XmppAccountHandle h;
   ASSERT_TRUE(alice.events->expectEvent(__LINE__, "XmppAccountHandler::onEntityFeature", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_TRUE(evt.entity.empty());
}

TEST_F(XmppAccountModuleTest, DISABLED_CertificateWildcard)
{
   auto matchCertificateName = [&](const std::string& m_name, const std::string& m_server) -> bool
   {
      static const char delimiter = '.';
      static const char wildcard = '*';

      std::string me = m_server;
      std::string name = m_name;
      std::transform(me.begin(), me.end(), me.begin(), tolower);
      std::transform(name.begin(), name.end(), name.begin(), tolower);

      if (name.length() > 1 && name.at(0) == wildcard && name.at(1) == delimiter)
      {
         size_t numCertificateTokens = std::count(name.begin(), name.end(), delimiter);
         size_t numMyTokens = std::count(me.begin(), me.end(), delimiter);

         if (numCertificateTokens > 0 && numCertificateTokens == numMyTokens)
         {
            std::string certificateDomain = name.substr(2 /* after wildcard */, std::string::npos);
            std::string myDomain = me.substr(me.find(delimiter) + 1, std::string::npos);

            if (certificateDomain.length() > 0)
            {
               return certificateDomain == myDomain;
            }
         }
      }

      return name == me;
   };

   ASSERT_TRUE(matchCertificateName("*.bria.com", "www.bria.com"));
}

static void generateJwt(const resip::Data& p8file, const resip::Data& userIdentity, resip::Data& jwt)
{
   std::map<resip::Data, resip::Data> pubClaims;
   pubClaims["cp_user"] = userIdentity;
   CPCAPI2::AuthServer::JwtUtils::GenerateJWT(p8file, "CPCAPI2::AuthServer", pubClaims, 86400, jwt);
}

#if USE_CPCAPI2_JSON_TESTS
TEST_F(XmppAccountModuleTest, EnableDisable_JSON)
{
   XmppTestAccount max("max", Account_Init);
   JsonApiServerConfig maxJsonApiConfig;
   maxJsonApiConfig.certificateFilePath = TestEnvironmentConfig::testResourcePath() + "p256-public-key-unit-tests.spki";
   maxJsonApiConfig.wssCertificateFilePath = TestEnvironmentConfig::testResourcePath() + "cert.pem";
   maxJsonApiConfig.wssPrivateKeyFilePath = TestEnvironmentConfig::testResourcePath() + "privkey.pem";
   maxJsonApiConfig.wssDiffieHellmanParamsFilePath = TestEnvironmentConfig::testResourcePath() + "dh2048.pem";
   max.jsonApiServer->start(maxJsonApiConfig);
   CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(max.phone)->setJsonApiServer(max.jsonApiServer);
   CPCAPI2::XmppAgent::XmppAgentManager::getInterface(max.phone)->setJsonApiServer(max.jsonApiServer);
   CPCAPI2::XmppAccount::XmppAccountJsonApi::getInterface(max.phone);
   CPCAPI2::XmppRoster::XmppRosterJsonApi::getInterface(max.phone);

   XmppTestAccount alice("alice", Account_Init);
   JsonApiClientSettings jsonApiClientSettings;
   jsonApiClientSettings.serverUri = "wss://localhost:9003";
   jsonApiClientSettings.ignoreCertVerification = true;
   alice.jsonApiClient->configureDefaultSettings(jsonApiClientSettings);
   alice.jsonApiClient->enable();

   {
      JsonApiConnectionHandle h;
      StatusChangedEvent args;
      cpcExpectEvent(alice.jsonApiClientEvents, "JsonApiClientHandler::onStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), h, args);
      cpcExpectEvent(alice.jsonApiClientEvents, "JsonApiClientHandler::onStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), h, args);
   }

   resip::Data aliceJwt;
   generateJwt((TestEnvironmentConfig::testResourcePath() + "p256-private-key-unit-tests.p8").c_str(), "alice", aliceJwt);
   alice.jsonApiClient->login(aliceJwt.c_str());

   {
      JsonApiUserHandle jsonApiUser;
      NewLoginEvent args;

      // Max has to process the login attempt (associate the context with an SDK instance)
      cpcExpectEvent(max.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 5000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args);
      cpc::vector<cpc::string> permissions; permissions.push_back("*");
      max.jsonApiServer->setJsonApiUserContext(jsonApiUser, max.phone, permissions);
      LoginResultEvent loginResult;
      loginResult.success = true;
      max.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);
   }

   {
      // Alice then gets the result
      JsonApiLoginHandle h;
      LoginResultEvent args;
      cpcExpectEvent(alice.jsonApiClientEvents, "JsonApiClientHandler::onLoginResult", 50000, CPCAPI2::test::AlwaysTruePred(), h, args);
   }

   alice.setCloudConnected(true);
   alice.config.useJsonProxy = true;
   alice.createAccountJson();

   alice.enable();
   alice.disable();
   alice.destroy();
   alice.jsonApiClient->disable();
   alice.setCloudConnected(false);

   max.jsonApiServer->shutdown();
}

TEST_F(XmppAccountModuleTest, XmppEnableDestroy_JSON)
{
   XmppTestCloudAccount alice("alice");
   alice.enable();

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   alice.destroy();
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
}

TEST_F(XmppAccountModuleTest, XmppEnableLogout_JSON)
{
   XmppTestCloudAccount alice("alice");
   bool registerForPush = true;
   alice.enable(registerForPush);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.logout();
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
}

TEST_F(XmppAccountModuleTest, XmppEnableLogoutAccount_JSON)
{
   XmppTestCloudAccount alice("alice");
   bool registerForPush = true;
   alice.enable(registerForPush);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.logoutAccount();
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
}

TEST_F(XmppAccountModuleTest, XmppEnableDisableLogout_JSON)
{
   XmppTestCloudAccount alice("alice");
   bool registerForPush = true;
   alice.enable(registerForPush);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   alice.logout();
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
}

TEST_F(XmppAccountModuleTest, XmppEnableDestroyLogout_JSON)
{
   XmppTestCloudAccount alice("alice");
   bool registerForPush = true;
   alice.enable(registerForPush);
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   ((XmppTestAccount)alice).destroy();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   alice.logout();
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
}

TEST_F(XmppAccountModuleTest, DISABLED_XmppEnableAgentLogoutAccountDestroy_JSON)
{
   XmppTestCloudAccount alice("alice");
   bool registerForPush = true;
   alice.enable(registerForPush);
   CloudConnector::CloudConnectorHandle cloudConnectorHandle = alice.getCloudConnectorHandle();

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   alice.agentJson->logout(alice.agentHandle);
   // std::this_thread::sleep_for(std::chrono::milliseconds(5));
   alice.agentJson->unregisterForRemoteSync(alice.agentHandle);
   alice.agentJson->unregisterForXmppPushNotifications(alice.agentHandle);
   alice.accountJson->destroy(alice.handle);
   assertAgentLogoutForXmpp(alice, alice.agentHandle, [&](const CPCAPI2::XmppAgent::LogoutResult& evt)
   {
      ASSERT_TRUE(evt.success);
   });

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   auto agentEvents = std::async(std::launch::async, [&] ()
   {
#if (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
      JsonApi::LogoutEvent logoutEvent;
      JsonApi::JsonApiUserHandle jsonApiUser = 0;

      // Process the json server logout attempt, this would normally be handled by the CPCAPI runner application of the xmpp agent
      ASSERT_TRUE(cpcExpectEvent(alice.agentServer->jsonApiServerEvents, "JsonApiServerHandler::onLogout", 30000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, logoutEvent));
      ASSERT_NE(jsonApiUser, 0);

      CPCAPI2::JsonApi::LogoutResultEvent logoutResult;
      logoutResult.success = true;
      alice.agentServer->jsonApiServer->sendLogoutResult(jsonApiUser, logoutResult);
#endif
   });

   auto cloudEvents = std::async(std::launch::async, [&] ()
   {
      alice.cloudConnector->logout(cloudConnectorHandle);

      CloudConnector::LogoutResult logoutResult;
      ASSERT_TRUE(cpcExpectEvent(alice.cloudConnectorEvents, "CloudConnectorHandler::onLogout", 30000, CPCAPI2::test::AlwaysTruePred(), cloudConnectorHandle, logoutResult));
      ASSERT_TRUE(logoutResult.success);
   });

   waitFor2(agentEvents, cloudEvents);

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   alice.cloudConnector->destroy(cloudConnectorHandle);
   assertCloudDestroyedForXmpp(alice, cloudConnectorHandle, [&](const CloudConnector::ServiceConnectionStatusEvent& evt)
   {
      ASSERT_EQ(CloudConnector::ServiceConnectionStatus_Destroyed, evt.connectionStatus);
   });

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.cleanup();
}
#endif // USE_CPCAPI2_JSON_TESTS

#if USE_CPCAPI2_JSON_TESTS
#if (CPCAPI2_BRAND_PUSH_NOTIFICATION_SERVER_MODULE == 1)
TEST_F(XmppAccountModuleTest, PushNotificationAgent) {
   XmppTestAccount max("max", Account_Init);
   JsonApiServerConfig maxJsonApiConfig;
   maxJsonApiConfig.certificateFilePath = TestEnvironmentConfig::testResourcePath() + "p256-public-key-unit-tests.spki";
   maxJsonApiConfig.wssCertificateFilePath = TestEnvironmentConfig::testResourcePath() + "cert.pem";
   maxJsonApiConfig.wssPrivateKeyFilePath = TestEnvironmentConfig::testResourcePath() + "privkey.pem";
   maxJsonApiConfig.wssDiffieHellmanParamsFilePath = TestEnvironmentConfig::testResourcePath() + "dh2048.pem";
   max.jsonApiServer->start(maxJsonApiConfig);
   CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(max.phone)->setJsonApiServer(max.jsonApiServer);
   CPCAPI2::XmppAgent::XmppAgentManager* xmppAgentMgr = CPCAPI2::XmppAgent::XmppAgentManager::getInterface(max.phone);
   CPCAPI2::XmppAgent::XmppAgentJsonApi::getInterface(max.phone);
   CPCAPI2::PushService::PushNotificationServiceManager::getInterface(max.phone);
   CPCAPI2::PushEndpoint::PushNotificationEndpointManager* pushEndpointMgr = CPCAPI2::PushEndpoint::PushNotificationEndpointManager::getInterface(max.phone);

   pushEndpointMgr->setPushNotificationService(max.pushServer);
   xmppAgentMgr->setPushNotificationManager(max.pushServer);
   xmppAgentMgr->setJsonApiServer(max.jsonApiServer);

   XmppTestAccount alice("alice", Account_Init);
   JsonApiClientSettings jsonApiClientSettings;
   jsonApiClientSettings.serverUri = "wss://localhost:9003";
   jsonApiClientSettings.ignoreCertVerification = true;
   alice.jsonApiClient->configureDefaultSettings(jsonApiClientSettings);
   alice.jsonApiClient->enable();

   {
      JsonApiConnectionHandle h;
      StatusChangedEvent args;
      cpcExpectEvent(alice.jsonApiClientEvents, "JsonApiClientHandler::onStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), h, args);
      cpcExpectEvent(alice.jsonApiClientEvents, "JsonApiClientHandler::onStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), h, args);
   }

   resip::Data aliceJwt;
   generateJwt((TestEnvironmentConfig::testResourcePath() + "p256-private-key-unit-tests.p8").c_str(), "alice", aliceJwt);
   alice.jsonApiClient->login(aliceJwt.c_str());

   {
      JsonApiUserHandle jsonApiUser;
      NewLoginEvent args;

      // Max has to process the login attempt (associate the context with an SDK instance)
      cpcExpectEvent(max.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 5000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args);
      cpc::vector<cpc::string> permissions; permissions.push_back("*");
      max.jsonApiServer->setJsonApiUserContext(jsonApiUser, max.phone, permissions);
      LoginResultEvent loginResult;
      loginResult.success = true;
      max.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);
   }

   {
      // Alice then gets the result
      JsonApiLoginHandle h;
      LoginResultEvent args;
      cpcExpectEvent(alice.jsonApiClientEvents, "JsonApiClientHandler::onLoginResult", 50000, CPCAPI2::test::AlwaysTruePred(), h, args);
   }

   XmppAgent::XmppPushRegistrationInfo info;
   XmppAgent::XmppPushRegistrationHandle alicePush = alice.agentJson->createXmppPushRegistration();
   alice.agentJson->registerForXmppPushNotifications(alicePush, info);

   {
      XmppAgent::XmppPushRegistrationHandle h;
      XmppAgent::XmppPushRegistrationSuccessEvent args;
      cpcExpectEvent(alice.agentJsonEvents, "XmppAgentHandler::onPushRegistrationSuccess", 1500, CPCAPI2::test::AlwaysTruePred(), h, args);
   }

   alice.agentJson->setHandler(alicePush, (XmppAgent::XmppAgentHandler*)0xDEADBEEF);
   alice.agentJson->requestEventHistory(alicePush);
   {
      XmppAgent::XmppPushRegistrationHandle h;
      CPCAPI2::XmppAgent::XmppEventHistory evt;
      ASSERT_TRUE(cpcExpectEvent(alice.agentJsonEvents, "XmppAgentHandler::onEventHistory", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   }

   alice.jsonApiClient->disable();
   max.jsonApiServer->shutdown();
}
#endif // #if (CPCAPI2_BRAND_PUSH_NOTIFICATION_SERVER_MODULE == 1)
#endif // USE_CPCAPI2_JSON_TESTS

TEST_F(XmppAccountModuleTest, DISABLED_Resume) { // require manual operation
   XmppTestAccount alice("alice");

   while (true)
   {
      XmppAccountHandle h;
      StreamManagementStateEvent evt;
      if (!alice.events->expectEvent(__LINE__, "XmppAccountHandler::onStreamManagementState", 3000, CPCAPI2::test::AlwaysTruePred(), h, evt)) break;

      alice.config.settings.streamManagementSequence = evt.sequence;
      if (alice.config.settings.streamManagementId.empty()) alice.config.settings.streamManagementId = evt.id;
   }

   std::cout << "Waiting for termination of alice's xmpp connection (the only ESTABLISHED connection under cpcapi2_auto_tests.exe) by using TCPView.exe" << std::endl;

   {
      XmppAccountHandle h;
      XmppAccountStatusChangedEvent evt;
      ASSERT_TRUE(alice.events->expectEvent(__LINE__, "XmppAccountHandler::onAccountStatusChanged",
         300000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status_Failure, evt.accountStatus);
   }

   alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings);
   alice.account->applySettings(alice.handle);
   assertXmppDisconnecting(alice);
   assertXmppDisconnected(alice);
   assertXmppConnecting(alice);
   assertXmppConnected(alice);
}

TEST_F(XmppAccountModuleTest, Resume_NotResumable) {
   XmppTestAccount alice("alice", Account_Init);

   alice.config.settings.streamManagementId = "invalid";
   alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings);
   alice.account->applySettings(alice.handle);
   alice.enable();
}

TEST_F(XmppAccountModuleTest, Notification) {
   XmppTestAccount alice("alice");

   // bliu: need to wait for this event otherwise the following enable/disableNotification() might occur at random order due to postMs() implementation
   EntityFeatureEvent evt;
   XmppAccountHandle h;
   ASSERT_TRUE(alice.events->expectEvent(__LINE__, "XmppAccountHandler::onEntityFeature", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_TRUE(evt.entity.empty());

   XmppAccount::XmppDataForm dataform;
   alice.account->enableNotification(alice.handle, "node", dataform);

   XmppAccount::XmppDataFormField field;
   field.type = -1;
   field.name = "secret";
   field.values.push_back("123");
   dataform.fields.push_back(field);
   alice.account->enableNotification(alice.handle, "node", dataform);

   alice.account->disableNotification(alice.handle, "node");

   std::this_thread::sleep_for(std::chrono::seconds(5));
}

TEST_F(XmppAccountModuleTest, DISABLED_CannedPresence) {
   XmppTestAccount alice("alice");
   //XmppTestAccount bob("bob");
   std::cin.get();
   alice.account->publishCannedPresence(alice.handle, XmppRoster::XmppCannedStatus_Busy, "XmppCannedStatus_Busy2");
   std::cin.get();
   alice.account->publishCannedPresence(alice.handle, XmppRoster::XmppCannedStatus_Away, "XmppCannedStatus_Away3");
   std::cin.get();
   alice.account->publishCannedPresence(alice.handle, XmppRoster::XmppCannedStatus_OnThePhone, "XmppCannedStatus_OnThePhone4");
   std::cin.get();
   alice.account->publishCannedPresence(alice.handle, XmppRoster::XmppCannedStatus_NotAvailableForCalls, "XmppCannedStatus_NotAvailableForCalls5");
   std::cin.get();
   alice.account->publishCannedPresence(alice.handle, XmppRoster::XmppCannedStatus_DoNotDisturb, "XmppCannedStatus_DoNotDisturb6");
   std::cin.get();
   alice.account->publishCannedPresence(alice.handle, XmppRoster::XmppCannedStatus_AppearOffline, "XmppCannedStatus_AppearOffline7");
   std::cin.get();
   alice.account->publishCannedPresence(alice.handle, XmppRoster::XmppCannedStatus_Offline, "XmppCannedStatus_Offline8");
   std::cin.get();
   alice.account->publishCannedPresence(alice.handle, XmppRoster::XmppCannedStatus_Available, "XmppCannedStatus_Available1");
   std::cin.get();
}

TEST_F(XmppAccountModuleTest, ResourceReusage) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob(alice);
   assertXmppFailure(alice);

   // alice shouldn't have any account status change afterwards as not further reconnection attempts from alice
   {
      XmppAccountHandle h;
      XmppAccountStatusChangedEvent evt;

      bool status = alice.events->expectEvent(__LINE__, "XmppAccountHandler::onAccountStatusChanged", 10000, CPCAPI2::test::AlwaysTruePred(), h, evt);
      if (status)
      {
         safeCout("XmppAccountHandler::onAccountStatusChanged received event on account: " << h << " status: " << evt.accountStatus << " error: " << evt.errorCode << " description: " << evt.errorText);
      }
      ASSERT_FALSE(status);
   }

   alice.enable();
   assertXmppFailure(bob);

   // alice shouldn't have any account status change afterwards as not further reconnection attempts from bob
   {
      XmppAccountHandle h;
      XmppAccountStatusChangedEvent evt;
      bool status = alice.events->expectEvent(__LINE__, "XmppAccountHandler::onAccountStatusChanged", 10000, CPCAPI2::test::AlwaysTruePred(), h, evt);
      if (status)
      {
         safeCout("XmppAccountHandler::onAccountStatusChanged received event on account: " << h << " status: " << evt.accountStatus << " error: " << evt.errorCode << " description: " << evt.errorText);
      }
      ASSERT_FALSE(status);
   }
}

#if 1 // bliu: DNS / SRV test cases
static bool checkDnsServer()
{
#ifdef ANDROID
   char* dnsServer = getenv("CPCAPI2_DNS_SERVER");
   // verify that the external DNS server has been set
   if (!dnsServer || strlen(dnsServer) == 0)
   {
      return false;
   }
#endif
   return true;
}

TEST_F(XmppAccountModuleTest, SRVFailover) {
   ASSERT_TRUE(checkDnsServer());
   XmppTestAccount alice("alice", Account_NoInit);
   alice.config.settings.proxy = "xmpp.srv.failover";
   alice.config.settings.port = 0;
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.enable();
}

TEST_F(XmppAccountModuleTest, DNSFailover) {
   ASSERT_TRUE(checkDnsServer());
   XmppTestAccount alice("alice", Account_NoInit);
   alice.config.settings.proxy = "xmpp.dns.failover";
   alice.config.settings.port = 0;
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.enable();
}

TEST_F(XmppAccountModuleTest, BlankSRVFailover) {
   ASSERT_TRUE(checkDnsServer());
   XmppTestAccount alice("alice", Account_NoInit);
   alice.config.settings.proxy = "xmpp.blanksrv.failover";
   alice.config.settings.port = 0;
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.enable();
}

TEST_F(XmppAccountModuleTest, NoSRVFailover) {
   ASSERT_TRUE(checkDnsServer());
   XmppTestAccount alice("alice", Account_NoInit);
   alice.config.settings.proxy = "xmpp.nosrv.failover";
   alice.config.settings.port = 5224; // need to specify the port or 5222 will be chosen and authentication will fail
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.enable();
}

// OBELISK-5824
TEST_F(XmppAccountModuleTest, DISABLED_UnresponsiveNameserver) {
   XmppTestAccount alice("alice", Account_NoInit);
   alice.config.settings.nameServers.push_back(TestEnvironmentConfig::unreachableV4Ip());
   alice.init();
   alice.account->enable(alice.handle);
   assertXmppConnecting(alice);
   assertXmppFailure(alice);
}

TEST_F(XmppAccountModuleTest, UnresponsiveNameserver2) {
   XmppTestAccount alice("alice", Account_NoInit);
   alice.config.settings.nameServers.push_back(TestEnvironmentConfig::unreachableV4Ip());
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.init();
   alice.enable();
}

// the SDK will not failover in case that handshake has been establish and the connection is broken afterwards
TEST_F(XmppAccountModuleTest, DISABLED_DNSFailoverAfterHandshake) {
   boost::asio::io_service ios;
   boost::asio::ip::tcp::acceptor server(ios, boost::asio::ip::tcp::endpoint(boost::asio::ip::tcp::v4(), 5224));
   auto future = std::async(std::launch::async, [&]() {
      boost::asio::ip::tcp::socket client (ios);
      boost::system::error_code e; // in case server is closed prior to accept() returns.
      server.accept(client, e);
      client.close();
   });

   ASSERT_TRUE(checkDnsServer());
   XmppTestAccount alice("alice", Account_NoInit);
   alice.config.settings.proxy = "xmpp.dns.failover.afterhandshake";
   alice.config.settings.port = 0;
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.enable();

   server.close();
   ios.stop();
   future.wait();
}
#endif

class DummyXmppAccountHandler : public CPCAPI2::XmppAccount::XmppAccountHandler {
public:

   virtual int onAccountStatusChanged(XmppAccountHandle account, const XmppAccountStatusChangedEvent& args) override { return kSuccess;}
   virtual int onError(XmppAccountHandle account, const CPCAPI2::XmppAccount::ErrorEvent& args) override { return kSuccess; }
   virtual int onEntityTime(XmppAccountHandle account, const CPCAPI2::XmppAccount::EntityTimeEvent& args)override { return kSuccess; }
   virtual int onEntityFeature(XmppAccountHandle account, const CPCAPI2::XmppAccount::EntityFeatureEvent& args)override { return kSuccess; }
   virtual int onPrivateStorageData(XmppAccountHandle account, const CPCAPI2::XmppAccount::PrivateStorageDataEvent& args)override { return kSuccess; }

};
TEST_F(XmppAccountModuleTest, SCORE_502) {
   XmppTestAccount alice("alice");
   alice.fileTransferManager->setHandler(alice.handle, NULL);
   alice.roster->setHandler(alice.handle, NULL);
   alice.vcardManager->setHandler(alice.handle, NULL);
   alice.chat->setHandler(alice.handle, NULL);
   alice.mucManager->setHandler(alice.handle, NULL);

   XmppAccount::XmppAccountManagerInternal::getInternalInterface(alice.phone)->simulateNetworkLoss(alice.handle);

   for (size_t ind = 0; ind < 200; ++ind) {
      auto allocated_handler = new DummyXmppAccountHandler();
      alice.account->setHandler(alice.handle, allocated_handler);
      alice.account->process(-1);
      XmppAccount::XmppAccountManagerInternal::getInternalInterface(alice.phone)->simulateNetworkRestriction(alice.handle, false);
      XmppAccount::XmppAccountManagerInternal::getInternalInterface(alice.phone)->simulateNetworkRestriction(alice.handle, true);
      XmppAccount::XmppAccountManagerInternal::getInternalInterface(alice.phone)->simulateNetworkLoss(alice.handle);
      std::thread deletor([&](){
         alice.account->setHandler(alice.handle, NULL);
         delete allocated_handler;
      });
      deletor.join();
      alice.account->process(-1);
   }
   alice.account->disable(alice.handle);
   alice.destroy(false);
}

TEST_F(XmppAccountModuleTest, OBELISK_5761) {
   const bool usingDocker = TestEnvironmentConfig::dockerContainerized();

   XmppTestAccount alice("alice");

   XmppAccount::XmppAccountManagerInternal::getInternalInterface(alice.phone)->simulateNetworkLoss(alice.handle);
   //alice.disconnectNetwork(usingDocker);
   assertXmppFailure(alice);

   std::this_thread::sleep_for(std::chrono::milliseconds(4000 + std::rand() % 5000));

   alice.disconnectNetwork(usingDocker);

   std::this_thread::sleep_for(std::chrono::seconds(20));

   alice.connectNetwork(usingDocker);

   std::this_thread::sleep_for(std::chrono::seconds(5));

   alice.events->clearCommands();
}



TEST_F(XmppAccountModuleTest, OBELISK_5937)
{
   class AresDnsInboundPacketInspector
   {
   public:
      AresDnsInboundPacketInspector()
      {
         set_incoming_udp_intercept_fn(&AresDnsInboundPacketInspector::intercept, NULL);
      }
   
      ~AresDnsInboundPacketInspector()
      {
         set_incoming_udp_intercept_fn(NULL, NULL);
      }
      
      static int intercept(unsigned char *abuf, int abuf_capacity, int abuf_length, void* /*context*/)
      {
         if (abuf_length > 50 && abuf[50] == 0x21) // SRV
         {
            // OBELISK-5937: set an rcode that ares previously did not treat as an error
            abuf[3] = 6;
            
            // ares helper function doesn't work here since it seems to assume
            // zero'd out starting memory
            // DNS_HEADER_SET_RCODE(abuf, 6);
         }

         return abuf_length;
      }
   
   };
   
   XmppTestAccount alice("alice", Account_NoInit);
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.proxy = "xmpp.onlyarecords";
   alice.config.settings.port = 0; // force SRV lookup
   alice.init();
   {
      AresDnsInboundPacketInspector inspect;
      // expect that the SRV lookup fails, and SDK switches to A record lookup, which succeeds
      alice.enable();
      alice.disable();
   }
}

struct ConnectionDataHandler : public gloox::ConnectionDataHandler
{
   virtual void handleReceivedData( const gloox::ConnectionBase* connection, const std::string& data ) override {}
   virtual void handleConnect( const gloox::ConnectionBase* connection ) override {}
   virtual void handleDisconnect( const gloox::ConnectionBase* connection, gloox::ConnectionError reason ) override {}
};

static void createConnection(XmppConnectionTcpClient*& c, resip::MultiReactor& reactor, ConnectionDataHandler& cdh, gloox::LogSink& logSink, XmppTestAccount& account)
{
   c = new XmppConnectionTcpClient(
      reactor,
      &cdh,
      logSink,
      account.config.settings.domain.c_str(),
      account.config.settings.port
   );

   c->connect();
};

TEST_F(XmppAccountModuleTest, OBELISK_6136)
{
   XmppConnectionTcpClient* c = NULL;
   ConnectionDataHandler cdh;
   gloox::LogSink logSink;

   // must appear after the above variables in the stack
   XmppTestAccount alice("alice", Account_Init);

   auto& sdkReactor = static_cast<PhoneInterface*>(alice.phone)->getSdkModuleThread();

   sdkReactor.post(resip::resip_static_bind(createConnection, std::ref(c), std::ref(sdkReactor), std::ref(cdh), std::ref(logSink), std::ref(alice)));

   std::this_thread::sleep_for(std::chrono::seconds(3));

#ifdef _WIN32
   ::shutdown(c->socket(), SD_BOTH);
#else
   ::shutdown(c->socket(), SHUT_WR);
#endif

   c->send(" ");

   std::this_thread::sleep_for(std::chrono::milliseconds(20));

   c->_release();
}

TEST_F(XmppAccountModuleTest, NetworkChangeStreamResumption) {
   XmppTestAccount alice("alice", Account_NoInit);
   alice.config.settings.enableStreamManagement = true;
   alice.config.settings.enableStreamResumption = true;
   alice.enable();
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   XmppAccount::XmppAccountManagerInternal::getInternalInterface(alice.phone)->simulateNetworkLoss(alice.handle);
   // make sure simulateNetworkLoss is executed before adding network restriction
   std::this_thread::sleep_for(std::chrono::milliseconds(500));
   XmppAccount::XmppAccountManagerInternal::getInternalInterface(alice.phone)->simulateNetworkRestriction(alice.handle, true);

   XmppAccountHandle h;
   XmppAccountStatusChangedEvent evt;
   ASSERT_TRUE(alice.events->expectEvent(__LINE__, "XmppAccountHandler::onAccountStatusChanged", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_EQ(CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status_Resuming, evt.accountStatus);

   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   XmppAccount::XmppAccountManagerInternal::getInternalInterface(alice.phone)->simulateNetworkRestriction(alice.handle, false);
   ASSERT_TRUE(alice.events->expectEvent(__LINE__, "XmppAccountHandler::onAccountStatusChanged", 60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_EQ(CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status_Resumed, evt.accountStatus);
}

}  // namespace

#endif // CPCAPI2_BRAND_XMPP_ACCOUNT_MODULE
