#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#include "cpcapi2_test_fixture.h"
#include "test_account_events.h"
#include "test_events.h"

#include "impl/account/SipAccountManagerInternal.h"

#include <vector>

#define RESIPROCATE_SUBSYSTEM resip::Subsystem::NONE

using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::SipEvent;

namespace {

   class TlsModuleTest : public CpcapiAutoTest
   {
   public:
      TlsModuleTest() {}
      virtual ~TlsModuleTest() {}
   };

   TEST_F(TlsModuleTest, TlsFailNameIncorrect)
   {
      // Attempt to register using TLS to autotest.cpcapi2 using incorrect (but valid) DNS name
      TestAccount alice("alice", Account_NoInit, false);
      alice.config.settings.domain = "127.0.0.1:6061"; // this would fail because cert is for autotest.cpcapi2
      alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
      alice.config.settings.sipTransportType = SipAccount::SipAccountTransport_TLS;
      alice.config.settings.outboundProxy = "";
      alice.config.settings.useRport = false;
      alice.config.settings.stunServerSource = SipAccount::StunServerSource_SRV;
      alice.init();
      // Perform registration
      ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);

      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(0, evt.signalingStatusCode);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);

      // expect failure
      ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
      ASSERT_GT(evt.tlsInfo.certificateStatus, CPCAPI2::SipAccount::SipTLSConnectionInfo::CertificateStatus_Ok);
      ASSERT_EQ("autotest.cpcapi2", evt.tlsInfo.server);
      ASSERT_EQ("Certificate name mismatch", evt.signalingResponseText);
   }

   TEST_F(TlsModuleTest, TlsPassValidcertificate)
   {
      // Attempt to register using TLS to autotest.cpcapi2 using incorrect (but valid) DNS name
      TestAccount alice("alice", Account_NoInit, false);
      alice.config.settings.domain = "autotest.cpcapi2";
      alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
      alice.config.settings.sipTransportType = SipAccount::SipAccountTransport_TLS;
      alice.config.settings.outboundProxy = "";
      alice.config.settings.useRport = false;
      alice.config.settings.stunServerSource = SipAccount::StunServerSource_SRV;
      alice.config.settings.additionalCertPeerNames.clear();
      alice.config.settings.acceptedCertPublicKeys.push_back("MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEA3Q/3AQAW9Z8thdtQlnxAG6ilSn1FVH81ViruEEEyJBbxk0WPzroavMqRgvXJ6sIE+7iR8hHqY2BowKPhTg2D+WCvycT+i5YC7L37f/oWsnVp5xZZqTtiTTTXc+rSHgl2WO5tGxyGGWlUsslq3UXnHym5H9VvEZ5R3EKJelxauvhO8uNfjEwjuKBYPFjNJbZUoiy9s+ZhFuM1a14ArRZlvQ/P96txt2GLHjScMMiZllO4a6NyLvMJQI7Ys0lI4JmKe5QEBdukvbSnFbnsaGMjwfeAksFiKyDRvHUjyc0EUaqU24hmhn+RZkYSjTqeBd567TlggPR696peqaA/tILWGUgcMhYBga9DFzkpgXc8NDaO6WqAFg2caM2/BAnZ9OyfzNYU7GXCyOAm6JNb+BaxYLg6rqCKr27cSipy4MG6LvgcOndRLAeQoNe55QMqi+lfH90v0nQsNIlIEgo8pXgwML6lef+dk98Ox46Egkq1txFKVrmmZ2bXBiLrdFQYqEhzpAMl8XlKg7Wq4R5q2NyOuVJwQ0RHVCbW6nHO/nT5eyldqEzvSY+pg6juxOIpgtD7XvlXRqq775OW0aVFGoL1ilKCjb//c3WU4oetWNcBizrkYb0BOny7P17s8Jx2ZVtvcup4KMOi3+NZJew1uRBKM8wgsSZ1CLmZq1ae/ECXI3kCAwEAAQ==");
      alice.init();
      // Perform registration
      ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);
      assertAccountRegistering(alice);
      assertAccountRegistered(alice);
   }

   TEST_F(TlsModuleTest, TlsFailCertificateUnknown2)
   {
      // Attempt to register using TLS to autotest.cpcapi2, with the SDK set to load certificates ONLY from an empty directory.
      // registration should FAIL

      TestAccount alice("alice", Account_NoInit, false);
      alice.config.settings.domain = "autotest.cpcapi2";
      alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
      alice.config.settings.outboundProxy = "";
      alice.config.settings.sipTransportType = SipAccount::SipAccountTransport_TLS;

      alice.init();
      SipAccountManagerInternal* mi = dynamic_cast<SipAccountManagerInternal*>(alice.account);
      mi->setCertStorageLoadType(alice.handle, SipAccountManagerInternal::CertLoadStorageType_FileSystem);
      mi->setCertStorageFileSystemPath(alice.handle, "./bogus");

      // Perform registration
      ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);
      assertAccountRegistering(alice);

      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

      // expect failure
      ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
      ASSERT_EQ(evt.tlsInfo.certificateStatus, CPCAPI2::SipAccount::SipTLSConnectionInfo::CertificateStatus_Invalid);
      ASSERT_EQ("autotest.cpcapi2", evt.tlsInfo.server);
      //ASSERT_EQ("Certificate name mismatch", evt.signalingResponseText);
   }

   TEST_F(TlsModuleTest, TlsIgnoreCertErrors)
   {
      // Attempt to register using TLS to autotest.cpcapi2, with the SDK set to load certificates ONLY from an empty directory.
      // ignore cert verification is turned on; registration should succeed

      TestAccount alice("alice", Account_NoInit, false);
      alice.config.settings.domain = "autotest.cpcapi2";
      alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
      alice.config.settings.outboundProxy = "";
      alice.config.settings.sipTransportType = SipAccount::SipAccountTransport_TLS;
      alice.config.settings.ignoreCertVerification = true;
      alice.config.settings.useRport = false;

      alice.init();
      SipAccountManagerInternal* mi = dynamic_cast<SipAccountManagerInternal*>(alice.account);
      mi->setCertStorageLoadType(alice.handle, SipAccountManagerInternal::CertLoadStorageType_FileSystem);
      mi->setCertStorageFileSystemPath(alice.handle, "./bogus");

      // Perform registration
      ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);
      assertAccountRegistering(alice);
      assertAccountRegistered(alice);
   }

   TEST_F(TlsModuleTest, TlsVerifyViaFileSystemCert)
   {
      TestAccount alice("alice", Account_NoInit, false);
      alice.config.settings.domain = "autotest.cpcapi2";
      alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
      alice.config.settings.outboundProxy = "";
      alice.config.settings.sipTransportType = SipAccount::SipAccountTransport_TLS;
      alice.config.settings.useRport = false;

      alice.init();
      SipAccountManagerInternal* mi = dynamic_cast<SipAccountManagerInternal*>(alice.account);
      mi->setCertStorageLoadType(alice.handle, SipAccountManagerInternal::CertLoadStorageType_FileSystem);
      // this folder should contain the correct cert to trust to get validation to succeed
      mi->setCertStorageFileSystemPath(alice.handle, TestEnvironmentConfig::testResourcePath() + "SelfSignedCerts/root");

      ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);
      assertAccountRegistering(alice);
      assertAccountRegistered(alice);
   }

   TEST_F(TlsModuleTest, TlsProtocolVersionNegotiationInvalidSSL)
   {
      TestAccount alice("alice", Account_NoInit, false);
      alice.config.settings.domain = "127.0.0.1";
      alice.config.settings.outboundProxy = "pritls.local";
      alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
      alice.config.settings.sipTransportType = SipAccountTransport_TLS;
      alice.config.settings.ignoreCertVerification = true;
      alice.config.settings.useRport = false;
      alice.config.settings.sslVersion = SipAccount::SSL_NONE;

      alice.init();
      ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);

      assertAccountError(alice, "Unsupported TLS version NoSSL");
      assertAccountRegistering(alice);
      assertAccountDeregistered(alice);
      
      // ensure config tweak to fix registration works
      alice.config.settings.sslVersion = SipAccount::SSL_HIGHEST;
      alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings);
      alice.account->applySettings(alice.handle);
      alice.account->disable(alice.handle);
      alice.enable();
   }
 
   TEST_F(TlsModuleTest, TlsProtocolVersionNegotiationInvalidSSL2)
   {
      TestAccount alice("alice", Account_NoInit, false);
      alice.config.settings.domain = "127.0.0.1";
      alice.config.settings.outboundProxy = "pritls.local";
      alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
      alice.config.settings.sipTransportType = SipAccountTransport_TLS;
      alice.config.settings.ignoreCertVerification = true;
      alice.config.settings.useRport = false;
      alice.config.settings.sslVersion = SipAccount::SSL_V2;

      alice.init();
      ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);

      assertAccountError(alice, "Unsupported SecurityTypes::SSLType");
      assertAccountRegistering(alice);
      assertAccountDeregistered(alice);
   }

   TEST_F(TlsModuleTest, TlsProtocolVersionNegotiationInvalidSSL3)
   {
      TestAccount alice("alice", Account_NoInit, false);
      alice.config.settings.domain = "127.0.0.1";
      alice.config.settings.outboundProxy = "pritls.local";
      alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
      alice.config.settings.sipTransportType = SipAccountTransport_TLS;
      alice.config.settings.ignoreCertVerification = true;
      alice.config.settings.useRport = false;
      alice.config.settings.sslVersion = SipAccount::SSL_V3;

      alice.init();
      ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);

      assertAccountError(alice, "Unsupported SecurityTypes::SSLType");
      assertAccountRegistering(alice);
      assertAccountDeregistered(alice);
   }

   TEST_F(TlsModuleTest, TlsProtocolVersionNegotiationTLSv1_0)
   {
      // Set proxy to TLS_V1_0
      CPCAPI2::ReproRunner* reproRunner = ReproHolder::instance();
      reproRunner->restart((TestEnvironmentConfig::testResourcePath() + "repro_tlsv1_0.config").c_str());

      TestAccount alice("alice", Account_NoInit, false);
      alice.config.settings.domain = "127.0.0.1";
      alice.config.settings.outboundProxy = "pritls.local";
      alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
      alice.config.settings.sipTransportType = SipAccountTransport_TLS;
      alice.config.settings.ignoreCertVerification = true;
      alice.config.settings.useRport = false;
      alice.config.settings.sslVersion = SipAccount::TLS_V1_2;

      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

      alice.init();

      SipAccountManagerInternal* mi = dynamic_cast<SipAccountManagerInternal*>(alice.account);
      mi->setCertStorageLoadType(alice.handle, SipAccountManagerInternal::CertLoadStorageType_FileSystem);
      mi->setCertStorageFileSystemPath(alice.handle, "./bogus");

      // Perform registration using TLS_V1_2
      ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);
      assertAccountRegistering(alice);

      // Expect failure
      ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Transport_Protocol_Mismatch, evt.reason);
      ASSERT_EQ(503, evt.signalingStatusCode);
      ASSERT_EQ("Transport Protocol Mismatch", evt.signalingResponseText);
      ASSERT_EQ(SipAccount::SSL_NONE, evt.tlsInfo.sslVersion);

      // Perform registration using TLS_V1_3
      alice.config.settings.sslVersion = SipAccount::TLS_V1_3;
      ASSERT_EQ(alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings), kSuccess);
      ASSERT_EQ(alice.account->applySettings(alice.handle), kSuccess);
      assertAccountDeregistering(alice);
      assertAccountDeregistered(alice);
      assertAccountRegistering(alice);

      // Expect failure
      ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Transport_Protocol_Mismatch, evt.reason);
      ASSERT_EQ(503, evt.signalingStatusCode);
      ASSERT_EQ("Transport Protocol Mismatch", evt.signalingResponseText);
      ASSERT_EQ(SipAccount::SSL_NONE, evt.tlsInfo.sslVersion);

      // Perform registration using TLS_V1_1
      alice.config.settings.sslVersion = SipAccount::TLS_V1_1;
      ASSERT_EQ(alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings), kSuccess);
      ASSERT_EQ(alice.account->applySettings(alice.handle), kSuccess);
      assertAccountDeregistering(alice);
      assertAccountDeregistered(alice);
      assertAccountRegistering(alice);

      // Expect failure
      ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Transport_Protocol_Mismatch, evt.reason);
      ASSERT_EQ(503, evt.signalingStatusCode);
      ASSERT_EQ("Transport Protocol Mismatch", evt.signalingResponseText);
      ASSERT_EQ(SipAccount::SSL_NONE, evt.tlsInfo.sslVersion);

      // Perform registration using TLS_V1_0
      alice.config.settings.sslVersion = SipAccount::TLS_V1_0;
      ASSERT_EQ(alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings), kSuccess);
      ASSERT_EQ(alice.account->applySettings(alice.handle), kSuccess);
      assertAccountDeregistering(alice);
      assertAccountDeregistered(alice);
      assertAccountRegistering(alice);

      // Expect success
      ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(200, evt.signalingStatusCode);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
      ASSERT_EQ(SipAccount::TLS_V1_0, evt.tlsInfo.sslVersion);

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      alice.disable();

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      // Restart proxy to default settings
      reproRunner->restart();

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   }

   TEST_F(TlsModuleTest, TlsProtocolVersionNegotiationTLSv1_1)
   {
      // Set proxy to TLS_V1_1
      CPCAPI2::ReproRunner* reproRunner = ReproHolder::instance();
      reproRunner->restart((TestEnvironmentConfig::testResourcePath() + "repro_tlsv1_1.config").c_str());

      TestAccount alice("alice", Account_NoInit, false);
      alice.config.settings.domain = "127.0.0.1";
      alice.config.settings.outboundProxy = "pritls.local";
      alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
      alice.config.settings.sipTransportType = SipAccountTransport_TLS;
      alice.config.settings.ignoreCertVerification = true;
      alice.config.settings.useRport = false;
      alice.config.settings.sslVersion = SipAccount::TLS_V1_2;

      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

      alice.init();

      SipAccountManagerInternal* mi = dynamic_cast<SipAccountManagerInternal*>(alice.account);
      mi->setCertStorageLoadType(alice.handle, SipAccountManagerInternal::CertLoadStorageType_FileSystem);
      mi->setCertStorageFileSystemPath(alice.handle, "./bogus");

      // Perform registration using TLS_V1_2
      ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);
      assertAccountRegistering(alice);

      // Expect failure
      ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Transport_Protocol_Mismatch, evt.reason);
      ASSERT_EQ(503, evt.signalingStatusCode);
      ASSERT_EQ("Transport Protocol Mismatch", evt.signalingResponseText);
      ASSERT_EQ(SipAccount::SSL_NONE, evt.tlsInfo.sslVersion);

      // Perform registration using TLS_V1_3
      alice.config.settings.sslVersion = SipAccount::TLS_V1_3;
      ASSERT_EQ(alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings), kSuccess);
      ASSERT_EQ(alice.account->applySettings(alice.handle), kSuccess);
      assertAccountDeregistering(alice);
      assertAccountDeregistered(alice);
      assertAccountRegistering(alice);

      // Expect failure
      ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Transport_Protocol_Mismatch, evt.reason);
      ASSERT_EQ(503, evt.signalingStatusCode);
      ASSERT_EQ("Transport Protocol Mismatch", evt.signalingResponseText);
      ASSERT_EQ(SipAccount::SSL_NONE, evt.tlsInfo.sslVersion);

      // Perform registration using TLS_V1_0
      alice.config.settings.sslVersion = SipAccount::TLS_V1_0;
      ASSERT_EQ(alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings), kSuccess);
      ASSERT_EQ(alice.account->applySettings(alice.handle), kSuccess);
      assertAccountDeregistering(alice);
      assertAccountDeregistered(alice);
      assertAccountRegistering(alice);

      // Expect failure
      ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Transport_Protocol_Mismatch, evt.reason);
      ASSERT_EQ(503, evt.signalingStatusCode);
      ASSERT_EQ("Transport Protocol Mismatch", evt.signalingResponseText);
      ASSERT_EQ(SipAccount::SSL_NONE, evt.tlsInfo.sslVersion);


      // Perform registration using TLS_V1_1
      alice.config.settings.sslVersion = SipAccount::TLS_V1_1;
      ASSERT_EQ(alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings), kSuccess);
      ASSERT_EQ(alice.account->applySettings(alice.handle), kSuccess);
      assertAccountDeregistering(alice);
      assertAccountDeregistered(alice);
      assertAccountRegistering(alice);

      // Expect success
      ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(200, evt.signalingStatusCode);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
      ASSERT_EQ(SipAccount::TLS_V1_1, evt.tlsInfo.sslVersion);

      std::this_thread::sleep_for(std::chrono::milliseconds(2000));

      alice.disable();

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      // Restart proxy to default settings
      reproRunner->restart();

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   }

   TEST_F(TlsModuleTest, TlsProtocolVersionNegotiationTLSv1_2)
   {
      // Set proxy to TLS_V1_2
      CPCAPI2::ReproRunner* reproRunner = ReproHolder::instance();
      reproRunner->restart((TestEnvironmentConfig::testResourcePath() + "repro_tlsv1_2.config").c_str());

      TestAccount alice("alice", Account_NoInit, false);
      alice.config.settings.domain = "127.0.0.1";
      alice.config.settings.outboundProxy = "pritls.local";
      alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
      alice.config.settings.sipTransportType = SipAccountTransport_TLS;
      alice.config.settings.ignoreCertVerification = true;
      alice.config.settings.useRport = false;
      alice.config.settings.sslVersion = SipAccount::TLS_V1_1;

      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

      alice.init();

      SipAccountManagerInternal* mi = dynamic_cast<SipAccountManagerInternal*>(alice.account);
      mi->setCertStorageLoadType(alice.handle, SipAccountManagerInternal::CertLoadStorageType_FileSystem);
      mi->setCertStorageFileSystemPath(alice.handle, "./bogus");

      // Perform registration using TLS_V1_1
      ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);
      assertAccountRegistering(alice);

      // Expect failure
      ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Transport_Protocol_Mismatch, evt.reason);
      ASSERT_EQ(503, evt.signalingStatusCode);
      ASSERT_EQ("Transport Protocol Mismatch", evt.signalingResponseText);
      ASSERT_EQ(SipAccount::SSL_NONE, evt.tlsInfo.sslVersion);

      // Perform registration using TLS_V1_3
      alice.config.settings.sslVersion = SipAccount::TLS_V1_3;
      ASSERT_EQ(alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings), kSuccess);
      ASSERT_EQ(alice.account->applySettings(alice.handle), kSuccess);
      assertAccountDeregistering(alice);
      assertAccountDeregistered(alice);
      assertAccountRegistering(alice);

      // Expect failure
      ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Transport_Protocol_Mismatch, evt.reason);
      ASSERT_EQ(503, evt.signalingStatusCode);
      ASSERT_EQ("Transport Protocol Mismatch", evt.signalingResponseText);
      ASSERT_EQ(SipAccount::SSL_NONE, evt.tlsInfo.sslVersion);

      // Perform registration using TLS_V1_0
      alice.config.settings.sslVersion = SipAccount::TLS_V1_0;
      ASSERT_EQ(alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings), kSuccess);
      ASSERT_EQ(alice.account->applySettings(alice.handle), kSuccess);
      assertAccountDeregistering(alice);
      assertAccountDeregistered(alice);
      assertAccountRegistering(alice);

      // Expect failure
      ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Transport_Protocol_Mismatch, evt.reason);
      ASSERT_EQ(503, evt.signalingStatusCode);
      ASSERT_EQ("Transport Protocol Mismatch", evt.signalingResponseText);
      ASSERT_EQ(SipAccount::SSL_NONE, evt.tlsInfo.sslVersion);

      // Perform registration using TLS_V1_2
      alice.config.settings.sslVersion = SipAccount::TLS_V1_2;
      ASSERT_EQ(alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings), kSuccess);
      ASSERT_EQ(alice.account->applySettings(alice.handle), kSuccess);
      assertAccountDeregistering(alice);
      assertAccountDeregistered(alice);
      assertAccountRegistering(alice);

      // Expect success
      ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(200, evt.signalingStatusCode);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
      ASSERT_EQ(SipAccount::TLS_V1_2, evt.tlsInfo.sslVersion);

      std::this_thread::sleep_for(std::chrono::milliseconds(2000));

      alice.disable();

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      // Restart proxy to default settings
      reproRunner->restart();

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   }

   TEST_F(TlsModuleTest, TlsProtocolVersionNegotiationTLSv1_3)
   {
      // Set proxy to TLS_V1_3
      CPCAPI2::ReproRunner* reproRunner = ReproHolder::instance();
      reproRunner->restart((TestEnvironmentConfig::testResourcePath() + "repro_tlsv1_3.config").c_str());

      TestAccount alice("alice", Account_NoInit, false);
      alice.config.settings.domain = "127.0.0.1";
      alice.config.settings.outboundProxy = "pritls.local";
      alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
      alice.config.settings.sipTransportType = SipAccountTransport_TLS;
      alice.config.settings.ignoreCertVerification = true;
      alice.config.settings.useRport = false;
      alice.config.settings.sslVersion = SipAccount::TLS_V1_1;

      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

      alice.init();

      SipAccountManagerInternal* mi = dynamic_cast<SipAccountManagerInternal*>(alice.account);
      mi->setCertStorageLoadType(alice.handle, SipAccountManagerInternal::CertLoadStorageType_FileSystem);
      mi->setCertStorageFileSystemPath(alice.handle, "./bogus");

      // Perform registration using TLS_V1_1
      ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);
      assertAccountRegistering(alice);

      // Expect failure
      ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Transport_Protocol_Mismatch, evt.reason);
      ASSERT_EQ(503, evt.signalingStatusCode);
      ASSERT_EQ("Transport Protocol Mismatch", evt.signalingResponseText);
      ASSERT_EQ(SipAccount::SSL_NONE, evt.tlsInfo.sslVersion);

      // Perform registration using TLS_V1_2
      alice.config.settings.sslVersion = SipAccount::TLS_V1_2;
      ASSERT_EQ(alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings), kSuccess);
      ASSERT_EQ(alice.account->applySettings(alice.handle), kSuccess);
      assertAccountDeregistering(alice);
      assertAccountDeregistered(alice);
      assertAccountRegistering(alice);

      // Expect failure
      ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Transport_Protocol_Mismatch, evt.reason);
      ASSERT_EQ(503, evt.signalingStatusCode);
      ASSERT_EQ("Transport Protocol Mismatch", evt.signalingResponseText);
      ASSERT_EQ(SipAccount::SSL_NONE, evt.tlsInfo.sslVersion);

      // Perform registration using TLS_V1_0
      alice.config.settings.sslVersion = SipAccount::TLS_V1_0;
      ASSERT_EQ(alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings), kSuccess);
      ASSERT_EQ(alice.account->applySettings(alice.handle), kSuccess);
      assertAccountDeregistering(alice);
      assertAccountDeregistered(alice);
      assertAccountRegistering(alice);

      // Expect failure
      ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Transport_Protocol_Mismatch, evt.reason);
      ASSERT_EQ(503, evt.signalingStatusCode);
      ASSERT_EQ("Transport Protocol Mismatch", evt.signalingResponseText);
      ASSERT_EQ(SipAccount::SSL_NONE, evt.tlsInfo.sslVersion);

      // Perform registration using TLS_V1_3
      alice.config.settings.sslVersion = SipAccount::TLS_V1_3;
      ASSERT_EQ(alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings), kSuccess);
      ASSERT_EQ(alice.account->applySettings(alice.handle), kSuccess);
      assertAccountDeregistering(alice);
      assertAccountDeregistered(alice);
      assertAccountRegistering(alice);

      // Expect success
      ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(200, evt.signalingStatusCode);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
      ASSERT_EQ(SipAccount::TLS_V1_3, evt.tlsInfo.sslVersion);

      std::this_thread::sleep_for(std::chrono::milliseconds(2000));

      alice.disable();

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      // Restart proxy to default settings
      reproRunner->restart();

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   }

   TEST_F(TlsModuleTest, TlsProtocolVersionNegotiationHighestTransportSSL)
   {
      // It is assumed that the Proxy supports the Highest SSL version, i.e. TLS_V1_2

      TestAccount alice("alice", Account_NoInit, false);
      alice.config.settings.sourceAddress = "127.0.0.1"; // This should trigger use of the transport SSL context instead of the Security SSL context
      alice.config.settings.domain = "127.0.0.1";
      alice.config.settings.outboundProxy = "pritls.local";
      alice.config.settings.sipTransportType = SipAccount::SipAccountTransport_TLS;
      alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
      alice.config.settings.ignoreCertVerification = true;
      alice.config.settings.useRport = false;
      alice.config.settings.sslVersion = SipAccount::TLS_V1_2;

      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

      alice.init();

      // Transport creation would fail if a domain cert is not found:
      // With domain + source address specified, security would search for the default domain cert
      // leaving the default settings.userCertificatePEM = "none" prevents this

      SipAccountManagerInternal* mi = dynamic_cast<SipAccountManagerInternal*>(alice.account);
      mi->setCertStorageLoadType(alice.handle, SipAccountManagerInternal::CertLoadStorageType_FileSystem);
      mi->setCertStorageFileSystemPath(alice.handle, "./bogus");

      // Perform registration using TLS_V1_2
      ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);
      assertAccountRegistering(alice);

      // Expect success
      ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(200, evt.signalingStatusCode);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
      ASSERT_EQ(SipAccount::TLS_V1_2, evt.tlsInfo.sslVersion);

      // Perform registration using TLS_V1_3
      alice.config.settings.sslVersion = SipAccount::TLS_V1_3;
      ASSERT_EQ(alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings), kSuccess);
      ASSERT_EQ(alice.account->applySettings(alice.handle), kSuccess);
      assertAccountDeregistering(alice);
      assertAccountDeregistered(alice);
      assertAccountRegistering(alice);

      // Expect success
      ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(200, evt.signalingStatusCode);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
      ASSERT_EQ(SipAccount::TLS_V1_3, evt.tlsInfo.sslVersion);

      // Perform registration using TLS_V1_1
      alice.config.settings.sslVersion = SipAccount::TLS_V1_1;
      ASSERT_EQ(alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings), kSuccess);
      ASSERT_EQ(alice.account->applySettings(alice.handle), kSuccess);
      assertAccountDeregistering(alice);
      assertAccountDeregistered(alice);
      assertAccountRegistering(alice);

      // Expect success
      ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(200, evt.signalingStatusCode);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
      ASSERT_EQ(SipAccount::TLS_V1_1, evt.tlsInfo.sslVersion);

      // Perform registration using TLS_V1_0
      alice.config.settings.sslVersion = SipAccount::TLS_V1_0;
      ASSERT_EQ(alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings), kSuccess);
      ASSERT_EQ(alice.account->applySettings(alice.handle), kSuccess);
      assertAccountDeregistering(alice);
      assertAccountDeregistered(alice);
      assertAccountRegistering(alice);

      // Expect success
      ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(200, evt.signalingStatusCode);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
      ASSERT_EQ(SipAccount::TLS_V1_0, evt.tlsInfo.sslVersion);

      // Perform registration using SSL_HIGHEST
      alice.config.settings.sslVersion = SSL_HIGHEST;
      ASSERT_EQ(alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings), kSuccess);
      ASSERT_EQ(alice.account->applySettings(alice.handle), kSuccess);
      assertAccountDeregistering(alice);
      assertAccountDeregistered(alice);
      assertAccountRegistering(alice);

      // Expect success
      ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(200, evt.signalingStatusCode);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
      ASSERT_EQ(SipAccount::TLS_V1_3, evt.tlsInfo.sslVersion);

      std::this_thread::sleep_for(std::chrono::milliseconds(2000));

      alice.disable();
   }

   TEST_F(TlsModuleTest, TlsCiphers)
   {
      CPCAPI2::ReproRunner* reproRunner = ReproHolder::instance();
      reproRunner->restart((TestEnvironmentConfig::testResourcePath() + "repro_tlsv1_2.config").c_str());

      TestAccount alice("alice", Account_NoInit, false);
      alice.config.settings.domain = "127.0.0.1";
      alice.config.settings.outboundProxy = "pritls.local";
      alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
      alice.config.settings.sipTransportType = SipAccountTransport_TLS;
      alice.config.settings.ignoreCertVerification = true;
      alice.config.settings.useRport = false;
      alice.config.settings.sslVersion = SipAccount::TLS_V1_2;

      alice.init();

      SipAccountManagerInternal* mi = dynamic_cast<SipAccountManagerInternal*>(alice.account);
      mi->setCertStorageLoadType(alice.handle, SipAccountManagerInternal::CertLoadStorageType_FileSystem);
      mi->setCertStorageFileSystemPath(alice.handle, "./bogus");

      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

      std::string ciphers = CPCAPI2::CipherSuiteLegacy.c_str();

      size_t pos = 0;
      std::string cipher;
      while ( (pos = ciphers.find(":")) != std::string::npos )
      {
         cipher = ciphers.substr(0, pos);
         ciphers.erase(0, pos + 1);

         //  Skip TLS 1.3 ciphers since we support specifying a single cipher cuite for TLS 1.3
         if ((cipher.compare("TLS_AES_256_GCM_SHA384") == 0)
            || (cipher.compare("TLS_AES_128_GCM_SHA256") == 0)
            || (cipher.compare("TLS_CHACHA20_POLY1305_SHA256") == 0))
            continue;

         InfoLog(<< "Testing cipher " << cipher);
         std::cerr << "Testing cipher " << cipher << std::endl;

         if (std::string::npos != cipher.find("ECDSA"))
         {
           reproRunner->restart((TestEnvironmentConfig::testResourcePath() + "repro_tlsv1_2_ecdsa.config").c_str(), resip::BaseSecurity::CipherList(cipher.c_str()));
         }
         else
         {
            reproRunner->restart((TestEnvironmentConfig::testResourcePath() + "repro_tlsv1_2.config").c_str(), resip::BaseSecurity::CipherList(cipher.c_str()));
         }

         ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);
         assertAccountRegistering(alice);
         //expect success
         ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(alice.handle, h);
         ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
         ASSERT_EQ(200, evt.signalingStatusCode);
         ASSERT_EQ(SipAccount::TLS_V1_2, evt.tlsInfo.sslVersion);
         ASSERT_EQ(0, cipher.compare(evt.tlsInfo.cipher));

         ASSERT_EQ(alice.account->disable(alice.handle, false), kSuccess);
         assertAccountDeregistering(alice);
         assertAccountDeregistered(alice);
      }

      InfoLog(<< "Testing non supported cipher (SRP-AES-128-CBC-SHA) fails");
      std::cerr << "Testing non supported cipher (SRP-AES-128-CBC-SHA) fails" << std::endl;

      reproRunner->restart((TestEnvironmentConfig::testResourcePath() + "repro_tlsv1_2.config").c_str(), resip::BaseSecurity::CipherList("SRP-AES-128-CBC-SHA"));

      ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);
      assertAccountRegistering(alice);
      // Expect failure
      ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_TLS_Cipher_Mismatch, evt.reason);
      ASSERT_EQ(503, evt.signalingStatusCode);
      ASSERT_EQ("No Shared TLS Cipher", evt.signalingResponseText);
      ASSERT_EQ(SipAccount::SSL_NONE, evt.tlsInfo.sslVersion);

      // Restart proxy to default settings
      reproRunner->restart();

   }

   TEST_F(TlsModuleTest, TlsMutualAuthentication)
   {
      CPCAPI2::ReproRunner* reproRunner = ReproHolder::instance();
      reproRunner->restart((TestEnvironmentConfig::testResourcePath() + "repro_mutualTlsAuth.config").c_str());

      TestAccount alice("test", Account_NoInit, false);
      alice.config.settings.username = "test";
      alice.config.settings.domain = "autotest.cpcapi2";
      alice.config.settings.outboundProxy = "autotest.cpcapi2";
      alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
      alice.config.settings.sipTransportType = SipAccountTransport_TLS;
      alice.config.settings.password = "";
      alice.config.settings.useRport = false;
      alice.config.settings.sslVersion = SipAccount::TLS_V1_0;
      alice.config.settings.userCertificatePEM = TestEnvironmentConfig::testResourcePath() + "SelfSignedCerts/client/user_cert_test.autotest.cpcapi2.pem";
      alice.config.settings.userPrivateKeyPEM = TestEnvironmentConfig::testResourcePath() + "SelfSignedCerts/client/user_key_test.autotest.cpcapi2.pem";

      alice.config.settings.sourceAddress = "127.0.0.1";

      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

      alice.init();

      SipAccountManagerInternal* mi = dynamic_cast<SipAccountManagerInternal*>(alice.account);
      mi->setCertStorageLoadType(alice.handle, SipAccountManagerInternal::CertLoadStorageType_FileSystem);
      mi->setCertStorageFileSystemPath(alice.handle, TestEnvironmentConfig::testResourcePath() + "SelfSignedCerts/root");

      // Perform registration using TLS_V1_0
      ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);
      assertAccountRegistering(alice);
      //expect success
      ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
      ASSERT_EQ(200, evt.signalingStatusCode);
      ASSERT_EQ(SipAccount::TLS_V1_0, evt.tlsInfo.sslVersion);

      // Perform registration using TLS_V1_1
      alice.config.settings.sslVersion = SipAccount::TLS_V1_1;
      ASSERT_EQ(alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings), kSuccess);
      ASSERT_EQ(alice.account->applySettings(alice.handle), kSuccess);
      assertAccountDeregistering(alice);
      assertAccountDeregistered(alice);
      assertAccountRegistering(alice);
      //expect success
      ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
      ASSERT_EQ(200, evt.signalingStatusCode);
      ASSERT_EQ(SipAccount::TLS_V1_1, evt.tlsInfo.sslVersion);

      // Perform registration using TLS_V1_2
      alice.config.settings.sslVersion = SipAccount::TLS_V1_2;
      ASSERT_EQ(alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings), kSuccess);
      ASSERT_EQ(alice.account->applySettings(alice.handle), kSuccess);
      assertAccountDeregistering(alice);
      assertAccountDeregistered(alice);
      assertAccountRegistering(alice);
      //expect success
      ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
      ASSERT_EQ(200, evt.signalingStatusCode);
      ASSERT_EQ(SipAccount::TLS_V1_2, evt.tlsInfo.sslVersion);

      // Perform registration using SSL_HIGHEST
      alice.config.settings.sslVersion = SSL_HIGHEST;
      ASSERT_EQ(alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings), kSuccess);
      ASSERT_EQ(alice.account->applySettings(alice.handle), kSuccess);
      assertAccountDeregistering(alice);
      assertAccountDeregistered(alice);
      assertAccountRegistering(alice);
      //expect success
      ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
      ASSERT_EQ(200, evt.signalingStatusCode);
      ASSERT_EQ(SipAccount::TLS_V1_3, evt.tlsInfo.sslVersion);

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      // Restart proxy to default settings
      reproRunner->restart();

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   }

   TEST_F(TlsModuleTest, TlsMutualAuthFromString)
   {
      CPCAPI2::ReproRunner* reproRunner = ReproHolder::instance();
      reproRunner->restart((TestEnvironmentConfig::testResourcePath() + "repro_mutualTlsAuth.config").c_str());

      TestAccount alice("test", Account_NoInit, false);
      alice.config.settings.username = "test";
      alice.config.settings.domain = "autotest.cpcapi2";
      alice.config.settings.outboundProxy = "autotest.cpcapi2";
      alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
      alice.config.settings.sipTransportType = SipAccountTransport_TLS;
      alice.config.settings.password = "";
      alice.config.settings.useRport = false;
      alice.config.settings.sslVersion = SipAccount::TLS_V1_0;
      alice.config.settings.userCertificatePEM =   "-----BEGIN CERTIFICATE-----\n"
                                                   "MIIF6jCCA9KgAwIBAgICEAQwDQYJKoZIhvcNAQELBQAwUjELMAkGA1UEBhMCUlMx\n"
                                                   "DzANBgNVBAgMBlNlcmJpYTEXMBUGA1UECgwOQXV0b3Rlc3QgQ29ycC4xGTAXBgNV\n"
                                                   "****************************************************************\n"
                                                   "NTI3WjBWMQswCQYDVQQGEwJSUzEPMA0GA1UECAwGU2VyYmlhMRYwFAYDVQQKDA1B\n"
                                                   "dXRvdGVzdCBUZXN0MR4wHAYDVQQDDBV0ZXN0QGF1dG90ZXN0LmNwY2FwaTIwggIi\n"
                                                   "MA0GCSqGSIb3DQEBAQUAA4ICDwAwggIKAoICAQCmfOoMV8Ew6nYLk28fnRBLtYn/\n"
                                                   "ChSwpy60hRE85FCgr0sprs2vgEcZRYkRYGOBDAVXlFfYm5KPvPvX1b89pMUtDvSU\n"
                                                   "kz7hgH7Upr5IRWqwRNS+keM/QUsVUkijKQ4F+M2cFowhV8Pv7Ctp7RIJ8jqKa0pT\n"
                                                   "dsLRzZvHWVcOP6orBGjAu7a6kPclTdH0SD1sAUbgirC1SkMU7OjTgXDz3iDcL1dY\n"
                                                   "9S8t2Ft/2mnxtkuxG50yosWLU8IhgkX3Pf0P847TwwS+JXMca2h6rMGgcDyllhCf\n"
                                                   "gg/Ty5MaTXLcTb/O333vUpGIhDsAr172AIpvHiKMekwD499oaOjEh702ChbhonGn\n"
                                                   "wFXv52UjzIdOyDlV/i6KN33OvuhyVrY5Fs7SFpOcoDvWfdU4fyBdT0564Zc2cxwj\n"
                                                   "mpZqbxuICb90CO7ck0EDoZKMQQzx0tmrpCQ5kSZA0D02biIkki7xyD9DzGBP5ssA\n"
                                                   "IlPYAgAah6/cs72cCTgAQ9WSq/VHjLQQ8L+3sgFWlMW05H2Uz6F6s/p4PdKS/0pO\n"
                                                   "Vrh4vSFIboptH1sZwGaWS2FYdmUWJr5ZQEIwB0sAzoohxRsbtaNwwzDw0TLgzPuG\n"
                                                   "el99ekUatpQai5N8rtscn5YE8YP+9w6AUF9uGUX5MIABVLVMOivOryDLtGlMwDVH\n"
                                                   "+DEtjkNuZvnBXGH6GwIDAQABo4HFMIHCMAkGA1UdEwQCMAAwEQYJYIZIAYb4QgEB\n"
                                                   "BAQDAgWgMDMGCWCGSAGG+EIBDQQmFiRPcGVuU1NMIEdlbmVyYXRlZCBDbGllbnQg\n"
                                                   "Q2VydGlmaWNhdGUwHQYDVR0OBBYEFAz0NMu+x89518Zv7cG41Mqlw4/JMB8GA1Ud\n"
                                                   "IwQYMBaAFO01wovQJV+v/BGlIkTcBC0hvVWWMA4GA1UdDwEB/wQEAwIF4DAdBgNV\n"
                                                   "HSUEFjAUBggrBgEFBQcDAgYIKwYBBQUHAwQwDQYJKoZIhvcNAQELBQADggIBAH09\n"
                                                   "GklZWSElPvV44J3VmIOruRPeoll1SuBnBoPkoD9sakjlXc7YgTq+aUKBM4MhKocO\n"
                                                   "TXQDtJ4MfCCwsCXpmkF07HWn1T0ZtyASf2OmOB7OJk0dQqH3rkDmhX4oE+tjCPCc\n"
                                                   "1D7rYrViRPDP+jq3q4IWXLR9tyVqSa5hXpeagITTdZoIk/9Q1y07siDjpw6xZeSV\n"
                                                   "GHCH2CRulaRbARK5o8k1hPlbMGKPObpXQ5/Y2IEazCp1kfPd20xTnKHghcH8aucR\n"
                                                   "ILCWb/+JSTIBrCpg3+c9VuFo1yH+vtYNrya3pnN1Utq8rnopFBnZL6MZUF774ETe\n"
                                                   "iVfBJ07EjcCQF404HPi5bnMR5PIygX9yb1WutLgXmeO20D97TNt8HTGnsU6ENmp1\n"
                                                   "QOeKZT5JI62+5PzeCqYStwgwSJEMMVlAGf+qcYx+WgCepfaNqq6LarRPrvFXTSSr\n"
                                                   "3wfbEWB4muD6hOTfnEmlMeo35aCmpYQGJmJv2pbZi3UcOt5psdas/fwctjZwg0w5\n"
                                                   "E7xl5CdtZA2KpRJjDD6vkfq8qV5sKYzXrLoESQo9m2BCiJhlLb1yMuU7eHbQeYxR\n"
                                                   "PTB/F6PiOYfJfQ6C25PjgcteRyYqM0LrOHdgAm5RfC7YCNC1fWvHBAKdXBTjnFet\n"
                                                   "2qNlWjnQaqeDWk/giu+6PMrZuoAePB0bSYXOs2JD\n"
                                                   "-----END CERTIFICATE-----";
      alice.config.settings.userPrivateKeyPEM =    "-----BEGIN RSA PRIVATE KEY-----\n"
                                                   "MIIJJwIBAAKCAgEApnzqDFfBMOp2C5NvH50QS7WJ/woUsKcutIURPORQoK9LKa7N\n"
                                                   "r4BHGUWJEWBjgQwFV5RX2JuSj7z719W/PaTFLQ70lJM+4YB+1Ka+SEVqsETUvpHj\n"
                                                   "P0FLFVJIoykOBfjNnBaMIVfD7+wrae0SCfI6imtKU3bC0c2bx1lXDj+qKwRowLu2\n"
                                                   "upD3JU3R9Eg9bAFG4IqwtUpDFOzo04Fw894g3C9XWPUvLdhbf9pp8bZLsRudMqLF\n"
                                                   "i1PCIYJF9z39D/OO08MEviVzHGtoeqzBoHA8pZYQn4IP08uTGk1y3E2/zt9971KR\n"
                                                   "iIQ7AK9e9gCKbx4ijHpMA+PfaGjoxIe9NgoW4aJxp8BV7+dlI8yHTsg5Vf4uijd9\n"
                                                   "zr7ocla2ORbO0haTnKA71n3VOH8gXU9OeuGXNnMcI5qWam8biAm/dAju3JNBA6GS\n"
                                                   "jEEM8dLZq6QkOZEmQNA9Nm4iJJIu8cg/Q8xgT+bLACJT2AIAGoev3LO9nAk4AEPV\n"
                                                   "kqv1R4y0EPC/t7IBVpTFtOR9lM+herP6eD3Skv9KTla4eL0hSG6KbR9bGcBmlkth\n"
                                                   "WHZlFia+WUBCMAdLAM6KIcUbG7WjcMMw8NEy4Mz7hnpffXpFGraUGouTfK7bHJ+W\n"
                                                   "BPGD/vcOgFBfbhlF+TCAAVS1TDorzq8gy7RpTMA1R/gxLY5Dbmb5wVxh+hsCAwEA\n"
                                                   "AQKCAgB+rDxH99z+IuZ+GEdMfmyPrii47Cqh3hIjN7vN4MmX+Mfvhxjol2mBSO7p\n"
                                                   "UbVj0omeATh1jqkMxHW52uAPPvbnOaHOCdqmKOZXVichRe/O/hAAj2+gCUXfiyPJ\n"
                                                   "QMtyr8OBJ4anN+fU/JRrm/1FpA/2kXuT1aCVkSMdolQiez42TLyHKEv316SztWox\n"
                                                   "u2VAcBhFcEDTLxWrLjZX9vzCEBhgb4S+Io8rCptTxBMn3dPphWEtzbsQL0JdzIj/\n"
                                                   "fSK2qFHbvV42brgOv4pXDGqmb781HeC3/HWeaB4cvSDPjjiKZnGjTed9P17FPqm5\n"
                                                   "8t9hfEQ9Li/4JcEt5+Ry4/SK0VJmUNBG0YF4OZuevclxz1urRC1n76htSB/iRcYG\n"
                                                   "ttePCKmjXcUqh0w/mSs9PIEOp9vGFcp76Ml1pHZnRbHQcOTUSMKyR8zDwoa87wD2\n"
                                                   "PVEwtAuOZy2G2ce3ZgAVFjBC6mMB/jvq9P50Ll+QsmO6/WCtBMCbOtGR/yLe+1WQ\n"
                                                   "wcxlY55Tu5iq0N0WqqHXR6lz3n/meEPK9bCfIIXRjHySq0iYEi85lBO1TCZznDO/\n"
                                                   "0Bp+oUuyclRMr2K2LjV1uTsv2sf/niygeL3KCbDbOpxpO5n3Qwhddz11La1bihsM\n"
                                                   "HFzi28aEeO7xZY6lb7mkYQiKVK9VOk2KVMckhlPpCul3Iz47AQKCAQEAz+EBbfzJ\n"
                                                   "rOA58i+52aAo+6EKIXxCJ2WxxiouQ9gzIp6EEz4KNmOCZ7R5z9WvfeX2m3pwXNw8\n"
                                                   "D38A1CrOAs8SQIMHZLclxW+z1ce9cl6ZkqkFdcxixP6k4te9Jt28qQRhdqhLGStA\n"
                                                   "NCx+J6B7dhsdLSDq91FsYrKc+U8fgHUMVFwLO4eSTNC+LP0cVtYaW2B+5gCowxEz\n"
                                                   "zjPiaGn8x9jfKT1NBi5cjbyXdWUSFFuXfMkUo087jWo/uiO1D52wLg0h82/5N7qz\n"
                                                   "dXUfDWM20v+R4x/zQ0FufxpQnl22mzjJFmPaCsXwQvewJaeGw2L8JEmFGfjJDeVY\n"
                                                   "TUq8gdXl+MCPcQKCAQEAzQcPs9nb9INkXNN0xMzfI4elo/02F4u8DwvgICVfFdaP\n"
                                                   "FjWSYthlcJ1Y4djtWd0f4Wdendk9Sw4H/TmFHJA31TDt7pRJcuTqQUKITei128MA\n"
                                                   "CBzi5uB2s7Jo9vJsWIQ9yD7AxxEij4BngYLDhQol8+IExT4OdgDrj3gaRGaPrvLX\n"
                                                   "T6aBqmeDA8YpKxJmeAJriiQhupywaYBCpjvhCxk7etNNxXBhEbXtNABLyvWs+a07\n"
                                                   "5jHo3kfkesLyifcClp8PHQEQnwXeT5bTuK1GqacANagvElhieaDgC8VAM9ohyKLz\n"
                                                   "vMVj+FEqPNqXtZ/vM9PUFe/qDzZOBIL8kcrtG6U0SwKCAQB3/OQaXEL8aiCITkvz\n"
                                                   "9aIyO+3hDhwRKX3HaCl0N6KsZNrPUIwMgLuQqHd3I1w8SzRg4fdhYi24tBICZQKs\n"
                                                   "VGGwovDtjVlb8PHYf4Y2MaxrHuxr5iFMm78IwIYJnQt8c27eUp2mVXSlqNjYfqVE\n"
                                                   "srnQhqOZRYJOJdK9hJImT2lieA5zufkUJZjf1uk4pjRocfZNRCwlDBTe/Mu+gQ7L\n"
                                                   "O77zytJesvvxQ94YgNJCtetmt5oV+XHiWmWWR24HKXm72xCxtvwe0sErFwNpcs7+\n"
                                                   "U04dzx9WvrnM25+VCXqRdeF77v64ITBhNx6RXXVH6ianu1f8ecFzJ+IkGvNb7d7k\n"
                                                   "CiHBAoIBACVfRiN4cLmPQboR6VT+KyZk0XKk8zkzhWIQvhfd+AkZBRf9R+Owojz2\n"
                                                   "0X7JdepNfkQt/Xc0ZBHMSvSDiZn9R9AajSRR71l8FJ80q3fYFV4rm1PfQBTpkXmL\n"
                                                   "6copwkoYQNLBbY5btzsFItjepxkXEnf0GP6DI0urO0T5lYAmWcaACXNBXJ1dJOqJ\n"
                                                   "MnPhdMAnYMbbczJ64UU51exLOeg1/zuSTLjSlT8PNF+oNiiWw6L3StXY/yOVYzZl\n"
                                                   "VqSDW/tsnuqgiRETlsRXL9yX6St/f3BIadwkND8Lbgt3Nw68ki/qVBdyu5hF4zcY\n"
                                                   "eVLoBkgiMqSVCMshEPONlNBWdwX1BSUCggEAQJOjy1P9mrO6qDa57/wzsA6wJjpj\n"
                                                   "2+lLbQjlBG/VSVoHSVNexvt0m2X+E6um0ZBADeFIr1yXRnyar0HXhWUBzcmtW85o\n"
                                                   "QWV3gs/uxNNbqpopuq8QxfQTT8QO6IP1W6r8ub1EyiX4rAP6GLaTxKsCmoy+sZ2Y\n"
                                                   "d4F2zO9NeMqPboHQCPIAwOxMLXTVKimUpCHOvZme750rMhMXGYjzEiXUI0rfgp98\n"
                                                   "gLC5ETrbOf1cxJSrQ5MZMqrPzSu8dAmW4bDXhF8aMKhKeHhOzCfeYvMkZCsYUWVU\n"
                                                   "JyupKfz138DlDzC1GWr0wgkQN/xzT0gOvDu2d3B6hbIzgbyzN/bQIHcXGw==\n"
                                                   "-----END RSA PRIVATE KEY-----";
      alice.config.settings.sourceAddress = "127.0.0.1";

      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

      alice.init();

      SipAccountManagerInternal* mi = dynamic_cast<SipAccountManagerInternal*>(alice.account);
      mi->setCertStorageLoadType(alice.handle, SipAccountManagerInternal::CertLoadStorageType_FileSystem);
      mi->setCertStorageFileSystemPath(alice.handle, TestEnvironmentConfig::testResourcePath() + "SelfSignedCerts/root");

      // Perform registration using TLS_V1_0
      ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);
      assertAccountRegistering(alice);
      //expect success
      ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
      ASSERT_EQ(200, evt.signalingStatusCode);
      ASSERT_EQ(SipAccount::TLS_V1_0, evt.tlsInfo.sslVersion);

      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      alice.disable();
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      // Restart proxy to default settings
      reproRunner->restart();

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   }

   TEST_F(TlsModuleTest, TlsMutualAuthenticationBadClientCert)
   {
      CPCAPI2::ReproRunner* reproRunner = ReproHolder::instance();
      reproRunner->restart((TestEnvironmentConfig::testResourcePath() + "repro_mutualTlsAuth.config").c_str());

      TestAccount alice("test", Account_NoInit, false);
      alice.config.settings.username = "test";
      alice.config.settings.domain = "autotest.cpcapi2";
      alice.config.settings.outboundProxy = "autotest.cpcapi2";
      alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
      alice.config.settings.sipTransportType = SipAccountTransport_TLS;
      alice.config.settings.password = "";
      alice.config.settings.useRport = false;
      alice.config.settings.sslVersion = SSL_HIGHEST;
      alice.config.settings.userCertificatePEM = TestEnvironmentConfig::testResourcePath() + "SelfSignedCerts/client/user_cert_bad.autotest.cpcapi2.pem";
      alice.config.settings.userPrivateKeyPEM = TestEnvironmentConfig::testResourcePath() + "SelfSignedCerts/client/user_key_bad.autotest.cpcapi2.pem";
      alice.config.settings.sourceAddress = "127.0.0.1";

      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

      alice.init();

      SipAccountManagerInternal* mi = dynamic_cast<SipAccountManagerInternal*>(alice.account);
      mi->setCertStorageLoadType(alice.handle, SipAccountManagerInternal::CertLoadStorageType_FileSystem);
      mi->setCertStorageFileSystemPath(alice.handle, TestEnvironmentConfig::testResourcePath() + "SelfSignedCerts/root");

      // Perform registration with invalid client certificate
      ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);
      assertAccountRegistering(alice);
      //expect failure
      ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Unregistered, evt.accountStatus);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Server_Response, evt.reason);
      ASSERT_EQ(403, evt.signalingStatusCode);
      ASSERT_EQ("Authorization Failed for peer cert", evt.signalingResponseText);
      ASSERT_EQ(SipAccount::TLS_V1_3, evt.tlsInfo.sslVersion);

      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      alice.disable();
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      // Restart proxy to default settings
      reproRunner->restart((TestEnvironmentConfig::testResourcePath() + "repro.config").c_str());

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   }

   TEST_F(TlsModuleTest, TlsMutualAuthenticationBadClientCert2)
   {
      TestAccount alice("test", Account_NoInit, false);
      alice.config.settings.username = "test";
      alice.config.settings.domain = "autotest.cpcapi2";
      alice.config.settings.outboundProxy = "autotest.cpcapi2";
      alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
      alice.config.settings.sipTransportType = SipAccountTransport_TLS;
      alice.config.settings.password = "";
      alice.config.settings.useRport = false;
      alice.config.settings.sslVersion = SSL_HIGHEST;
      alice.config.settings.userCertificatePEM = "-----BEGIN CERTIFICATE-----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\nz4aG1JhJNlfVaOKRIirpdYfxa3ePyw==\n-----END CERTIFICATE-----";
**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
      alice.config.settings.sourceAddress = "127.0.0.1";

      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::ErrorEvent evt;

      alice.init();

      SipAccountManagerInternal* mi = dynamic_cast<SipAccountManagerInternal*>(alice.account);
      mi->setCertStorageLoadType(alice.handle, SipAccountManagerInternal::CertLoadStorageType_FileSystem);
      mi->setCertStorageFileSystemPath(alice.handle, TestEnvironmentConfig::testResourcePath() + "SelfSignedCerts/root");

      // Perform registration with invalid client certificate
      ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);
      assertAccountRegistering(alice);
      //expect failure
      ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onError", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
   }
}
