#if _WIN32
#include "stdafx.h"
#endif

#ifdef _WIN32

#include "cpcapi2_test_fixture.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>
#include "../impl/util/MachineIdentification.h"
#include <string>

#include "test_framework/cpcapi2_test_framework.h"

using namespace CPCAPI2;
using namespace CPCAPI2::test;

namespace {
   class MachineIdentificationTest : public CpcapiAutoTest
   {
   public:
      MachineIdentificationTest() {}
      ~MachineIdentificationTest() {}
   };

   TEST_F(MachineIdentificationTest, BasicMachineIdentification) {
      std::string hwid = MachineIdentification::GetHardwareId();
      std::string cname = MachineIdentification::ComputerName();
      std::string mac = MachineIdentification::GetLocalMACAddress();
      std::string mobo = MachineIdentification::GetMotherboardInfo();
      std::vector<std::string> allmacs = MachineIdentification::GetAllMACs_Win32();

      EXPECT_FALSE(hwid.empty());
      EXPECT_FALSE(cname.empty());
      EXPECT_FALSE(mac.empty());
      //EXPECT_FALSE(mobo.empty());
      ASSERT_TRUE(allmacs.size() > 0);
      EXPECT_FALSE(allmacs[0].empty());
   }
}

#endif
