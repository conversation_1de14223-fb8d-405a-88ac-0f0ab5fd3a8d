#include "cpcapi2_test_fixture.h"
#include "test_account_events.h"
#include "test_events.h"
#include "test_call_events.h"
#include "sipua_alianza_api_test_fixture.h"

#include "test_framework/http_test_framework.h"

#include "impl/account/SipAccountManagerInternal.h"
#include "impl/account/SipAccountHandlerInternal.h"
#include "impl/account/SipAccountAwareFeature.h"

#include <sstream>

#include "util/CurlPPHelper.h"
#include "util/CurlPPProgress.h"
#include "util/CurlPPSSL.h"
#include "util/CurlURI.h"

// rapidjson
#include <writer.h>
#include <stringbuffer.h>
#include <document.h>


#include "alianza_api/interface/public/alianza_api_handler.h"
#include "alianza_api/interface/public/alianza_api_types.h"
#include "alianza_api/interface/public/alianza_api_manager.h"

#include <sstream>

#include "util/CurlPPHelper.h"
#include "util/CurlPPProgress.h"
#include "util/CurlPPSSL.h"
#include "util/CurlURI.h"

// rapidjson
#include <writer.h>
#include <stringbuffer.h>
#include <document.h>


using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::SipConversation;
using namespace std::chrono;
using namespace curlpp::options;


namespace
{
   class SipuaNetworkChangeModuleTest : public CpcapiAutoTest
   {
   public:
      SipuaNetworkChangeModuleTest() {}
      virtual ~SipuaNetworkChangeModuleTest() {}

   };

   void configureMedia(MediaInfo& media, MediaType type, MediaDirection direction, MediaEncryptionMode mode, bool secure, cpc::vector<MediaCryptoSuite>& cryptos)
   {
      media.mediaType = type;
      media.mediaDirection = direction;
      media.mediaEncryptionOptions.mediaEncryptionMode = mode;
      media.mediaEncryptionOptions.secureMediaRequired = secure;
      media.mediaEncryptionOptions.mediaCryptoSuites = cryptos;
   }

   void configureMedia(MediaInfo& media, MediaType type, MediaDirection direction, MediaEncryptionMode mode, bool secure, MediaCryptoSuite crypto1)
   {
      cpc::vector<MediaCryptoSuite> cryptos;
      cryptos.push_back(crypto1);
      configureMedia(media, type, direction, mode, secure, cryptos);
   }

   struct NetworkChangeTestParam
   {
      bool mSecureCall;
      NetworkChangeTestParam(bool secureCall) : mSecureCall(secureCall) {}
   };

   class SipuaParameterizedNetworkChangeTest : public CpcapiAutoTestWithParam<NetworkChangeTestParam>
   {
   };

   INSTANTIATE_TEST_SUITE_P(SipuaSecureAndNonSecureCalls, SipuaParameterizedNetworkChangeTest,
      ::testing::Values(true, false)
   );

   TEST_P(SipuaParameterizedNetworkChangeTest, BasicCallLoseNetworkRegainNetwork)
   {
      auto environmentId = TestEnvironmentConfig::testEnvironmentId();

      bool secureCall = GetParam().mSecureCall;

      // CPE2 does not support secure call, hence skip the test for non-repro (qa, beta, etc) targets
      if (secureCall && !(environmentId == "repro")) GTEST_SKIP();

      std::unique_ptr<TestAccount> bob;
      ASSERT_NO_FATAL_FAILURE(bob = secureCall ? std::unique_ptr<SecureTestAccount>(new SecureTestAccount("bob")) : std::unique_ptr<TestAccount>(new TestAccount("bob")));

      std::unique_ptr<TestAccount> charlie;
      ASSERT_NO_FATAL_FAILURE(charlie = secureCall ? std::unique_ptr<SecureTestAccount>(new SecureTestAccount("charlie")) : std::unique_ptr<TestAccount>(new TestAccount("charlie")));

      const MediaCryptoSuite cryptoSuite = secureCall ? MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80 : MediaCryptoSuite_None;

      MediaInfo bobAudio;
      bobAudio.mediaEncryptionOptions.mediaCryptoSuites.clear();
      if (secureCall)
      {
         configureMedia(bobAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, cryptoSuite);
      }
      MediaInfo charlieAudio;
      charlieAudio.mediaEncryptionOptions.mediaCryptoSuites.clear();
      if (secureCall)
      {
         configureMedia(charlieAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, cryptoSuite);
      }

      // Initial network
      ASSERT_EQ(charlie->network->networkTransport(), NetworkTransport::TransportWiFi);

      // Bob calls Charlie then Charlie hangs up
      SipConversationHandle bobCall = bob->conversation->createConversation(bob->handle);
      bob->conversation->configureMedia(bobCall, bobAudio);
      bob->conversation->addParticipant(bobCall, charlie->uri().c_str());
      bob->conversation->start(bobCall);
      auto bobConversationEvents = std::async(std::launch::async, [&] () {
         assertNewConversationOutgoingAudio_crypto(*bob.get(), bobCall, charlie->uri().c_str(), bobAudio, [](const NewConversationEvent& evt) {});
         assertConversationStateChanged(*bob.get(), bobCall, ConversationState_RemoteRinging);
         assertConversationMediaChangedAudio_crypto(*bob.get(), bobCall, cryptoSuite, bobAudio, bobAudio, [](const ConversationMediaChangedEvent& evt) {});
         if (!(environmentId == "repro")) assertConversationStateChanged(*bob.get(), bobCall, ConversationState_Early);
         assertConversationStateChanged(*bob.get(), bobCall, ConversationState_Connected);
         std::this_thread::sleep_for(std::chrono::milliseconds(10000));
         assertMediaFlowing(*bob.get(), bobCall, true, false);

         if (environmentId == "repro")
         {
            // for network change re-INVITE
            assertConversationMediaChangeRequestAudio_crypto(*bob.get(), bobCall, charlieAudio, [](const ConversationMediaChangeRequestEvent& evt) {});
            assertSuccess(bob->conversation->accept(bobCall));
         }

         assertConversationMediaChangedAudio_crypto(*bob.get(), bobCall, cryptoSuite, bobAudio, charlieAudio, [](const ConversationMediaChangedEvent& evt) {});

         assertConversationEnded(*bob.get(), bobCall, ConversationEndReason_UserTerminatedRemotely);
      });

      auto charlieConversationEvents = std::async(std::launch::async, [&] () {
         // Wait for the expected state changes and for the call to end
         SipConversationHandle charlieCall;
         assertNewConversationIncomingAudio_crypto(*charlie.get(), &charlieCall, bob->uri().c_str(), bobAudio, [](const NewConversationEvent& evt) {});
         assertSuccess(charlie->conversation->sendRingingResponse(charlieCall));
         assertConversationStateChanged(*charlie.get(), charlieCall, ConversationState_LocalRinging);
         assertSuccess(charlie->conversation->accept(charlieCall));
         assertConversationMediaChanged(*charlie.get(), charlieCall, MediaDirection_SendReceive);
         assertConversationStateChanged(*charlie.get(), charlieCall, ConversationState_Connected);

         // Lose network access
         charlie->network->setNetworkTransport(NetworkTransport::TransportNone);
         std::set<resip::Data> none;
         charlie->network->setMockInterfaces(none);
         std::this_thread::sleep_for(std::chrono::milliseconds(10000));

         // Regain network access
         charlie->network->setNetworkTransport(NetworkTransport::TransportWiFi);
         std::set<resip::Data> wifi;
         wifi.insert("*******");
         charlie->network->setMockInterfaces(wifi);
         std::this_thread::sleep_for(std::chrono::milliseconds(10000));

         assertAccountRefreshing(*charlie.get());
         assertAccountRegistered(*charlie.get());
         assertConversationMediaChangedAudio_crypto(*charlie.get(), charlieCall, cryptoSuite, charlieAudio, bobAudio, [](const ConversationMediaChangedEvent& evt) {});

         charlie->conversation->refreshConversationStatistics(charlieCall, true, true, true);

         {
            SipConversationHandle h;
            ConversationStatisticsUpdatedEvent evt;
            ASSERT_TRUE(charlie->conversationEvents->expectEvent("SipConversationHandler::onConversationStatisticsUpdated",
                                                  60000, HandleEqualsPred<SipConversationHandle>(charlieCall), h, evt));
         }

         charlie->conversation->end(charlieCall);
         assertConversationEnded(*charlie.get(), charlieCall, ConversationEndReason_UserTerminatedLocally);

         // expect registration refresh after network change during a call
         assertAccountRefreshing(*charlie.get());
         assertAccountRegistered(*charlie.get());
      });
      waitFor2(bobConversationEvents, charlieConversationEvents);
   }
}

