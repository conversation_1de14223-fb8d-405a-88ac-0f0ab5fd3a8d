#include "sipua_alianza_api_test_fixture.h"
#include "sipua_alianza_api_test_events.h"
#include "sipua_alianza_api_test_helper.h"
#include "alianza_api/interface/public/alianza_api_manager.h"
#include "test_account_events.h"
#include "test_events.h"
#include "test_call_events.h"

#include <sstream>

#include "util/CurlPPHelper.h"
#include "util/CurlPPProgress.h"
#include "util/CurlPPSSL.h"
#include "util/CurlURI.h"

// rapidjson
#include <writer.h>
#include <stringbuffer.h>
#include <document.h>


using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::SipAccount;
using namespace ::testing;

using namespace std::chrono;
using namespace curlpp::options;


AlianzaSipUaInfo::AlianzaSipUaInfo(const std::string& name_) :
sipUsername(""),
sipPassword(""),
name(name_),
username(""),
firstName(""),
lastName(""),
emailAddress(""),
phoneNumber(""),
businessName(name_),
extension(""),
userId(""),
clientId(""),
voicemailId(""),
phoneId(""),
userAuthToken(""),
userCreated(false),
deviceCreated(false)
{
}

void AlianzaSipUaInfo::init(const AlianzaAccountConfig& config, bool numberEnabled)
{
   initSipUsername();
   initIdentity(config, numberEnabled);
   clientId = generateClientId();
}

void AlianzaSipUaInfo::initIdentity(const AlianzaAccountConfig& config, bool numberEnabled)
{
   extension = generateExtension(config.extensionLength);
   if (numberEnabled)
   {
      phoneNumber = generatePhoneNumber();
   }
   std::stringstream id;
   id << name << extension;
   firstName = id.str();
   lastName = id.str();
}

void AlianzaSipUaInfo::initSipUsername()
{
   sipUsername = generateSipUsername(name);
   if (TestEnvironmentConfig::testEnvironmentId() == "repro")
   {
      sipPassword = "";
   }
   else
   {
      sipPassword = sipUsername;
   }
   emailAddress = sipUsername + "@alianza.com";
   username = sipUsername;
   password = sipPassword;
}

std::string AlianzaSipUaInfo::generatePhoneNumber()
{
   // Book number in range from 1222899-0000 to 1222899-9999
   std::stringstream ss;
   ss.fill('0');
   ss.width(4);
   int postfix = (resip::Random::getRandom() % 10000);
   ss << postfix;
   std::string number = "1222899" + ss.str();
   // std::string number = "1222396" + ss.str(); // prod might need to be in a specific number range
   return number;
}

std::string AlianzaSipUaInfo::generateSipUsername(std::string& name)
{
   std::stringstream ss;
   ss << name << "_" << (resip::Random::getCryptoRandomHex(6).c_str());
   return ss.str();
}

std::string AlianzaSipUaInfo::generateExtension(uint32_t extensionLength)
{
   /*
   // Tend to get the following error when the extension is below 2000 or higher than 9000
   400 response: 
   {
     "status" : 400,
     "messages" : [ "InvalidExtension" ]
   }
   */
   // TODO: no verification on valid extension in extension lengths other than 4, against CPE

   // Book number in range based on extension length, e.g. with extension length of 3, the range would be 100 to 999
   if ((extensionLength <= 0) || (extensionLength >= 10))
   {
      extensionLength = 4;
      safeCout("AlianzaSipUaInfo::generateExtension(): invalid extensionLength value, overriding with a value of " << extensionLength);
   }
   uint32_t min = pow(10.0, extensionLength - 1);
   uint32_t max = pow(10.0, extensionLength);
   if (extensionLength == 4)
   {
      // Workaround due to the issue described above
      min = 2000;
      max = 8999;
   }
   uint32_t range = (max - min) + 1;
   uint32_t extension = (resip::Random::getRandom() % range) + min;
   std::stringstream ss;
   ss << extension;
   return ss.str();
}

std::string AlianzaSipUaInfo::generateClientId()
{
   std::stringstream ss;
   ss << resip::Random::getCryptoRandomHex(40).c_str();
   return ss.str();
}

AlianzaSessionInfo::AlianzaSessionInfo(const std::string& name, bool numberEnabled_) :
accountId(""),
accountNumber(""),
accountGroupName(""),
plan(),
numberEnabled(numberEnabled_),
extensionEnabled(false)
{
   ua.push_back(std::make_shared<AlianzaSipUaInfo>(name));
}

std::string AlianzaSessionInfo::generateAccountNumber()
{
   std::stringstream ss;
   ss << (resip::Random::getCryptoRandomHex(6).c_str());
   return ss.str();
}

std::string AlianzaSessionInfo::generateAccountGroupName()
{
   std::stringstream ss;
   ss << "cpcapi2-" << resip::Random::getCryptoRandomHex(6).c_str();
   return ss.str();
}

void AlianzaSessionInfo::init(const AlianzaAccountConfig& config)
{
   for (std::vector<std::shared_ptr<AlianzaSipUaInfo>>::iterator i = ua.begin(); i != ua.end(); ++i)
   {
      (*i)->init(config, numberEnabled);
   }
   accountNumber = generateAccountNumber();
   accountGroupName = generateAccountGroupName();
}

void AlianzaSessionInfo::resetAccountNumber()
{
   accountNumber = generateAccountNumber();
}

void AlianzaSessionInfo::resetUa(const AlianzaSipUaInfo& updatedUa)
{
   ua.clear();
   ua.push_back(std::make_shared<AlianzaSipUaInfo>(updatedUa));
}

std::shared_ptr<AlianzaSipUaInfo> AlianzaSessionInfo::addUa(const AlianzaAccountConfig& config, std::string name_)
{
   assert(ua.size() > 0);
   std::string name = name_;
   if (name.empty())
   {
      name = ua.front()->name;
   }
   std::shared_ptr<AlianzaSipUaInfo> info = std::make_shared<AlianzaSipUaInfo>(name);
   info->init(config, numberEnabled);

   bool checkExtension = true;
   while (checkExtension)
   {
      checkExtension = false;
      for (std::vector<std::shared_ptr<AlianzaSipUaInfo>>::iterator i = ua.begin(); i != ua.end(); ++i)
      {
         if ((*i)->extension == info->extension)
         {
            // Reset the extension if same already exists
            checkExtension = true;
            info->initIdentity(config, numberEnabled);
            break;
         }
      }
   }
   ua.push_back(info);
   return info;
}

std::shared_ptr<AlianzaSipUaInfo> AlianzaSessionInfo::getUa(std::string extension)
{
   assert(ua.size() > 0);
   if (extension.empty())
   {
      return ua.front();
   }

   for (std::vector<std::shared_ptr<AlianzaSipUaInfo>>::iterator i = ua.begin(); i != ua.end(); ++i)
   {
      if ((*i)->extension == extension)
      {
         return (*i);
      }
   }
   assert(false); // need to isolate where invalid extensions are being generated
}

void AlianzaSessionInfo::getUa(AlianzaSipUaInfo& info, std::string extension) const
{
   assert(ua.size() > 0);
   if (extension.empty())
   {
      info = (*ua.front());
      return;
   }

   for (std::vector<std::shared_ptr<AlianzaSipUaInfo>>::const_iterator i = ua.begin(); i != ua.end(); ++i)
   {
      if ((*i)->extension == extension)
      {
         info = *(*i);
         return;
      }
   }
   assert(false); // need to isolate where invalid extensions are being generated
}

void AlianzaSessionInfo::getExtensions(std::vector<std::string>& extensions) const
{
   for (std::vector<std::shared_ptr<AlianzaSipUaInfo>>::const_iterator i = ua.begin(); i != ua.end(); ++i)
   {
      extensions.push_back((*i)->extension);
   }
}

AlianzaAccountConfig::AlianzaAccountConfig() :
environmentId("repro"),
platformType("REPRO"),
environmentType("DEV"),
partitionId(""),
carrierId(""),
sipGatewayProxy(""),
numberLeaserUrl(""),
inboundRatePlanProductId(""),
callingPlanProductId(""),
accountRoutePlanId(""),
sipOutboundProxy("127.0.0.1:6060"),
sipDomain("cp.local"),
regionId(""),
sipTransportType("UDP"),
extensionLength(4)
{
}

bool AlianzaAccountConfig::importConfig()
{
   std::string environmentIdFromEnv = TestEnvironmentConfig::testEnvironmentId().c_str();
   safeCout("AlianzaAccountConfig::importConfig(): environmentId: " << environmentIdFromEnv);
   std::string configFile(TestEnvironmentConfig::testResourcePath().c_str());
   configFile += "sipuaLargeTestConfig.json";
   std::ifstream in(configFile.c_str());
   if (!in.is_open())
   {
      safeCout("AlianzaAccountConfig::importConfig(): failure opening config file: " << configFile.c_str());
      return false;
   }

   bool envConfigFound = false;
   std::ostringstream iss;
   iss << in.rdbuf() << std::flush;
 
   std::shared_ptr<rapidjson::Document> json(new rapidjson::Document);
   json->Parse<0>(iss.str().c_str());

   if (json->HasParseError())
   {
      safeCout("AlianzaAccountConfig::importConfig(): invalid json format, parse error occured:" << json->GetParseError() << " aborting import");
      return false;
   }

   if (!json->HasMember("sipuaLargeTestConfig"))
   {
      safeCout("AlianzaAccountConfig::importConfig(): node missing: sipuaLargeTestConfig, aborting import"); 
      return false;
   }
   const rapidjson::Value& sipuaLargeTestConfigVal = (*json)["sipuaLargeTestConfig"];
   if (!sipuaLargeTestConfigVal.IsArray())
   {
      safeCout("AlianzaAccountConfig::importConfig(): invalid sipuaLargeTestConfig format, aborting import");
      return false;
   }
   for (rapidjson::Value::ConstValueIterator i = sipuaLargeTestConfigVal.Begin(); i != sipuaLargeTestConfigVal.End(); ++i)
   {
      if (!i->IsObject())
      {
         safeCout("AlianzaAccountConfig::importConfig(): invalid sipuaLargeTestConfig format, aborting import");
         return false;
      }
      if (!i->HasMember("environmentId"))
      {
         safeCout("AlianzaAccountConfig::importConfig(): node missing: environmentId, aborting import"); 
         return false;
      }
      const rapidjson::Value& environmentIdVal = (*i)["environmentId"];
      if (!environmentIdVal.IsString())
      {
         safeCout("AlianzaAccountConfig::importConfig(): invalid environmentId format, aborting import");
         return false;
      }

      if (environmentIdFromEnv == environmentIdVal.GetString())
      {
         envConfigFound = true;

         if (!i->HasMember("platformType"))
         {
            safeCout("AlianzaAccountConfig::importConfig(): node missing: platformType, aborting import");
            return false;
         }
         const rapidjson::Value& platformTypeVal = (*i)["platformType"];
         if (!platformTypeVal.IsString())
         {
            safeCout("AlianzaAccountConfig::importConfig(): invalid platformType format, aborting import");
            return false;
         }

         if (!i->HasMember("environmentType"))
         {
            safeCout("AlianzaAccountConfig::importConfig(): node missing: environmentType, aborting import");
            return false;
         }
         const rapidjson::Value& environmentTypeVal = (*i)["environmentType"];
         if (!environmentTypeVal.IsString())
         {
            safeCout("AlianzaAccountConfig::importConfig(): invalid environmentType format, aborting import");
            return false;
         }

         if (!i->HasMember("partitionId"))
         {
            safeCout("AlianzaAccountConfig::importConfig(): node missing: partitionId, aborting import");
            return false;
         }
         const rapidjson::Value& partitionIdVal = (*i)["partitionId"];
         if (!partitionIdVal.IsString())
         {
            safeCout("AlianzaAccountConfig::importConfig(): invalid partitionId format, aborting import");
            return false;
         }

         if (!i->HasMember("carrierId"))
         {
            safeCout("AlianzaAccountConfig::importConfig(): node missing: carrierId, aborting import");
            return false;
         }
         const rapidjson::Value& carrierIdVal = (*i)["carrierId"];
         if (!carrierIdVal.IsString())
         {
            safeCout("AlianzaAccountConfig::importConfig(): invalid carrierId format, aborting import");
            return false;
         }

         if (!i->HasMember("sipGatewayProxy"))
         {
            safeCout("AlianzaAccountConfig::importConfig(): node missing: sipGatewayProxy, aborting import");
            return false;
         }
         const rapidjson::Value& sipGatewayProxyVal = (*i)["sipGatewayProxy"];
         if (!sipGatewayProxyVal.IsString())
         {
            safeCout("AlianzaAccountConfig::importConfig(): invalid sipGatewayProxy format, aborting import");
            return false;
         }

         if (!i->HasMember("numberLeaserUrl"))
         {
            safeCout("AlianzaAccountConfig::importConfig(): node missing: numberLeaserUrl, aborting import");
            return false;
         }
         const rapidjson::Value& numberLeaserUrlVal = (*i)["numberLeaserUrl"];
         if (!numberLeaserUrlVal.IsString())
         {
            safeCout("AlianzaAccountConfig::importConfig(): invalid numberLeaserUrl format, aborting import");
            return false;
         }

         if (!i->HasMember("inboundRatePlanProductId"))
         {
            safeCout("AlianzaAccountConfig::importConfig(): node missing: inboundRatePlanProductId, aborting import");
            return false;
         }
         const rapidjson::Value& inboundRatePlanProductIdVal = (*i)["inboundRatePlanProductId"];
         if (!inboundRatePlanProductIdVal.IsString())
         {
            safeCout("AlianzaAccountConfig::importConfig(): invalid inboundRatePlanProductId format, aborting import");
            return false;
         }

         if (!i->HasMember("callIngPlanProductId"))
         {
            safeCout("AlianzaAccountConfig::importConfig(): node missing: callingPlanProductId, aborting import");
            return false;
         }
         const rapidjson::Value& callingPlanProductIdVal = (*i)["callIngPlanProductId"];
         if (!callingPlanProductIdVal.IsString())
         {
            safeCout("AlianzaAccountConfig::importConfig(): invalid callingPlanProductId format, aborting import");
            return false;
         }

         if (!i->HasMember("accountRoutePlanId"))
         {
            safeCout("AlianzaAccountConfig::importConfig(): node missing: accountRoutePlanId, aborting import");
            return false;
         }
         const rapidjson::Value& accountRoutePlanIdVal = (*i)["accountRoutePlanId"];
         if (!accountRoutePlanIdVal.IsString())
         {
            safeCout("AlianzaAccountConfig::importConfig(): invalid accountRoutePlanId format, aborting import");
            return false;
         }

         if (!i->HasMember("sipOutboundProxy"))
         {
            safeCout("AlianzaAccountConfig::importConfig(): node missing: sipOutboundProxy, aborting import");
            return false;
         }
         const rapidjson::Value& sipOutboundProxyVal = (*i)["sipOutboundProxy"];
         if (!sipOutboundProxyVal.IsString())
         {
            safeCout("AlianzaAccountConfig::importConfig(): invalid sipOutboundProxy format, aborting import");
            return false;
         }

         if (!i->HasMember("sipDomain"))
         {
            safeCout("AlianzaAccountConfig::importConfig(): node missing: domain, defaulting domain to sipOutboundProxy");
         }
         const rapidjson::Value& sipDomainVal = ((i->HasMember("sipDomain")) ? (*i)["sipDomain"] : sipOutboundProxyVal);
         if (!sipDomainVal.IsString())
         {
            safeCout("AlianzaAccountConfig::importConfig(): invalid sipDomain format, aborting import");
            return false;
         }

         if (!i->HasMember("sipTransportType"))
         {
            safeCout("AlianzaAccountConfig::importConfig(): node missing: sipTransportType, aborting import");
            return false;
         }
         const rapidjson::Value& sipTransportTypeVal = (*i)["sipTransportType"];
         if (!sipTransportTypeVal.IsString())
         {
            safeCout("AlianzaAccountConfig::importConfig(): invalid sipTransportType format, aborting import");
            return false;
         }

         if (!i->HasMember("regionId"))
         {
            safeCout("AlianzaAccountConfig::importConfig(): node missing: regionId, aborting import");
            return false;
         }
         const rapidjson::Value& regionIdVal = (*i)["regionId"];
         if (!regionIdVal.IsString())
         {
            safeCout("AlianzaAccountConfig::importConfig(): invalid regionId format, aborting import");
            return false;
         }

         if (!i->HasMember("publicApiUrl"))
         {
            safeCout("AlianzaAccountConfig::importConfig(): node missing: publicApiUrl, aborting import");
            return false;
         }
         const rapidjson::Value& publicApiUrlVal = (*i)["publicApiUrl"];
         if (!publicApiUrlVal.IsString())
         {
            safeCout("AlianzaAccountConfig::importConfig(): invalid publicApiUrl format, aborting import");
            return false;
         }

         if (!i->HasMember("extensionLength"))
         {
            safeCout("AlianzaAccountConfig::importConfig(): node missing: extensionLength, aborting import");
            return false;
         }
         const rapidjson::Value& extensionLengthVal = (*i)["extensionLength"];
         if (!extensionLengthVal.IsUint())
         {
            safeCout("AlianzaAccountConfig::importConfig(): invalid extensionLength format, aborting import");
            return false;
         }

         /*
         if (!i->HasMember("apiUsername"))
         {
            safeCout("AlianzaAccountConfig::importConfig(): node missing: apiUsername, aborting import");
            return false;
         }
         const rapidjson::Value& apiUsernameVal = (*i)["apiUsername"];
         if (!apiUsernameVal.IsString())
         {
            safeCout("AlianzaAccountConfig::importConfig(): invalid apiUsername format, aborting import");
            return false;
         }

         if (!i->HasMember("apiPassword"))
         {
            safeCout("AlianzaAccountConfig::importConfig(): node missing: apiPassword, aborting import");
            return false;
         }
         const rapidjson::Value& apiPasswordVal = (*i)["apiPassword"];
         if (!apiPasswordVal.IsString())
         {
            safeCout("AlianzaAccountConfig::importConfig(): invalid apiPassword format, aborting import");
            return false;
         }
         */

         environmentId = environmentIdVal.GetString();
         platformType = platformTypeVal.GetString();
         environmentType = environmentTypeVal.GetString();
         partitionId = partitionIdVal.GetString();
         carrierId = carrierIdVal.GetString();
         sipGatewayProxy = sipGatewayProxyVal.GetString();
         numberLeaserUrl = numberLeaserUrlVal.GetString();
         inboundRatePlanProductId = inboundRatePlanProductIdVal.GetString();
         callingPlanProductId = callingPlanProductIdVal.GetString();
         accountRoutePlanId = accountRoutePlanIdVal.GetString();
         sipOutboundProxy = sipOutboundProxyVal.GetString();
         sipDomain = sipDomainVal.GetString();
         sipTransportType = sipTransportTypeVal.GetString();
         regionId = regionIdVal.GetString();
         extensionLength = extensionLengthVal.GetUint();
         api.publicApiUrl = publicApiUrlVal.GetString();

         if ((extensionLength <= 0) || (extensionLength >= 10))
         {
            extensionLength = 4;
            safeCout("AlianzaAccountConfig::importConfig(): invalid extensionLength value, overriding with a value of " << extensionLength);
         }
         // alianzaConfig.api.apiUsername = apiUsername.GetString();
         // alianzaConfig.api.apiPassword = apiPassword.GetString();
      }
   }

   if (!envConfigFound)
   {
      safeCout("AlianzaAccountConfig::importConfig(): environment-id: " << environmentIdFromEnv << " not found in configuration file");
      return false;
   }

   if (environmentIdFromEnv.compare("repro") != 0)
   {
      if (!initAlianzaApiCredentials())
      {
         safeCout("AlianzaAccountConfig::importConfig(): error initializing alianza api credentials");
         return false;
      }
   }

   safeCout("AlianzaAccountConfig::importConfig(): environment-id: " << environmentIdFromEnv << " configuration settings:\n" << *this);

   return true;
}

bool AlianzaAccountConfig::initAlianzaApiCredentials()
{
   std::string environmentIdFromEnv = TestEnvironmentConfig::testEnvironmentId().c_str();
   if (environmentIdFromEnv.compare("repro") == 0)
   {
      // Not applicable
      return false;
   }

   const char* username = getenv("CPCAPI2_TEST_ALIANZA_API_USERNAME");
   if (username == NULL)
   {
      safeCout("AlianzaAccountConfig::initAlianzaApiCredentials(): could not retrieve alianza api username");
      return false;
   }
   api.apiUsername = username;
   if (api.apiUsername.size() == 0)
   {
      safeCout("AlianzaAccountConfig::initAlianzaApiCredentials(): alianza api username not populated");
      return false;
   }

   const char* password = getenv("CPCAPI2_TEST_ALIANZA_API_PASSWORD");
   if (password == NULL)
   {
      safeCout("AlianzaAccountConfig::initAlianzaApiCredentials(): could not retrieve alianza api password");
      return false;
   }
   api.apiPassword = password;
   if (api.apiPassword.size() == 0)
   {
      safeCout("AlianzaAccountConfig::initAlianzaApiCredentials(): alianza api password not populated");
      return false;
   }

   return true;
}

std::ostream& operator<<(std::ostream& os, const AlianzaCallingPlanInfo& info)
{
   os << "callingPlanProductId:" << info.callingPlanProductId
      << " startDate: " << info.startDate
      << " planMinutes: " << info.planMinutes
      << " secondsRemaining: " << info.secondsRemaining;
   return os;  
}

std::ostream& operator<<(std::ostream& os, const AlianzaSipUaInfo& info)
{
   os << "sipUsername: " << info.sipUsername
      << " sipPassword: " << "*****"
      << " name: " << info.name
      << " username: " << info.username
      << " firstName: " << info.firstName
      << " lastName: " << info.lastName
      << " emailAddress: " << info.emailAddress
      << " phoneNumber: " << info.phoneNumber
      << " businessName:< " << info.businessName
      << " extension: " << info.extension
      << " userId: " << info.userId
      << " clientId: " << info.clientId
      << " voicemailId: " << info.voicemailId
      << " phoneId: " << info.phoneId;
   return os;  
}

std::ostream& operator<<(std::ostream& os, const AlianzaSessionInfo& info)
{
   os << "accountId: " << info.accountId
      << " accountNumber: " << info.accountNumber
      << " ua: [";

   std::vector<std::string> extensions;
   info.getExtensions(extensions);
   for (std::vector<std::string>::const_iterator i = extensions.begin(); i != extensions.end(); ++i)
   {
      AlianzaSipUaInfo ua("");
      info.getUa(ua, *i);

      os << " {" << ua << "}";
   }
   os << "]"
      << " plan: {" << info.plan << "}";
   return os;  
}

std::ostream& operator<<(std::ostream& os, const AlianzaAccountConfig& config)
{
   os << "environmentId: " << config.environmentId
      << " platformType: " << config.platformType
      << " environmentType: " << config.environmentType
      << " partitionId: " << config.partitionId
      << " carrierId: " << config.carrierId
      << " sipGatewayProxy: " << config.sipGatewayProxy
      << " numberLeaserUrl: " << config.numberLeaserUrl
      << " inboundRatePlanProductId: " << config.inboundRatePlanProductId
      << " callingPlanProductId: " << config.callingPlanProductId
      << " accountRoutePlanId: " << config.accountRoutePlanId
      << " sipOutboundProxy: " << config.sipOutboundProxy
      << " sipDomain: " << config.sipDomain
      << " regionId: " << config.regionId
      << " sipTransportType: " << config.sipTransportType
      << " extensionLength: " << config.extensionLength
      << " api: {" << config.api << "}";
   return os;  
}
