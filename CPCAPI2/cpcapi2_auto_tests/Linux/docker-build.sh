set -e
if [ ! -d build ]
then
  mkdir build
fi

#app="cpcapi2_auto_tests_builder"
#if docker ps -all | awk -v app="$app" 'NR > 1 && $NF == app{ret=1; exit} END{exit !ret}'; then
#  docker stop "$app" && docker rm -f "$app"
#fi


docker stop cpcapi2_auto_tests_builder || true
docker build -t cpcapi2_builder:ubuntu.clang -f ../../projects/docker_build/Dockerfile.ubuntu.clang ../../projects/docker_build
docker build -t cpcapi2_auto_tests_builder:ubuntu.clang -f Dockerfile.builder.ubuntu.clang .

mkdir -p $HOME/.ccache
mkdir -p $HOME/.conan

docker run --user $(id -u):$(id -g) --name cpcapi2_auto_tests_builder --rm --mount type=bind,source="$(pwd)"/../../../,target=/CPCAPI2 --mount type=bind,source=$HOME/.ccache/,target=/.ccache --mount type=bind,source=$HOME/.conan/,target=/tmp/conan/.conan --env CPCAPI2_CONAN_USER --env CPCAPI2_CONAN_APIKEY cpcapi2_auto_tests_builder:ubuntu.clang "$@"
