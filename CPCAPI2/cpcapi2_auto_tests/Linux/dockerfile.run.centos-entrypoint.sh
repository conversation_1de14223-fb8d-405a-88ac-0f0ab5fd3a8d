#!/bin/sh
errorLog() {
	 ret=$4
	 if [ $ret -lt 1 ]; then
		 ret=1
	 fi
	 if [ -z "$fileName" ]; then
	 	 fileName=$TARGETS
		 fileName=${fileName////-}
		 fileName=$(echo $fileName |awk -F' ' '{print $1}')
	 fi
    if [ ! -d /cpcapi2_auto_tests_root_folder/logs/ ]
    then
      mkdir -p /cpcapi2_auto_tests_root_folder/logs/
    fi
	echo "[ Entrypoint ] Error in $1 for test $2 : failed on dockerfile.run.centos-entrypoint.sh line $3" >> /cpcapi2_auto_tests_root_folder/logs/$fileName.txt
    exit $ret
}
trap 'errorLog $0 $TARGETS $LINENO $ret' ERR
ret=0

testStartDate=`date +%s`

TARGETS="$2"
name="$3"
logPrefix="$4"

mkdir -p /cpcapi2_auto_tests_root_folder/logs
mkdir -p /cpcapi2_auto_tests_root_folder/runtime_artifacts
mkdir -p /cpcapi2_auto_tests_root_folder/tmp

echo "TARGETS: $TARGETS"
#get lists of excluded tests
if [ ! -f /cpcapi2_auto_tests_root_folder/tests.txt ]
then
  # filter_test.sh needs to write a temporary file, so cd to a directory it can write to
  # (since we aren't running as root user in the container)
  cd /cpcapi2_auto_tests_root_folder/tmp
  /cpcapi2_auto_tests_root_folder/Linux/filter_test.sh "$TARGETS"
  exit 0
fi


trap - ERR
group=false
if [[ $TARGETS == *"*"* ]]; then #contains more than one tests
        group=true
        test=$name
else
        test=$TARGETS
fi

#remove '/' in logfile name so that logfileName of parameterized tests is legal
fileName=${test////-}
fileName=$(echo $fileName |awk -F' ' '{print $1}')
fileName=$logPrefix$fileName


#enable core dumps
ulimit -c unlimited
# !warning! this modifies the *host's* core pattern!
sudo sh -c 'echo "/cpcapi2_auto_tests_root_folder/tmp/core.%p.dmp" > /proc/sys/kernel/core_pattern'
sudo sh -c 'echo "0" > /proc/sys/kernel/core_uses_pid'
# needed because we are calling setcap on cpcapi2_auto_tests below
sudo sh -c 'echo 1 > /proc/sys/fs/suid_dumpable'
alias gdbbt="gdb -q -n -ex bt -batch"
#rm -rf /cpcapi2_auto_tests_root_folder/tmp/$fileName.dmp


#allow packet capture
sudo setcap cap_net_raw,cap_net_admin=eip /cpcapi2_auto_tests_root_folder/Linux/build/cpcapi2_auto_tests

#setup ipv6
sudo sysctl -w net.ipv6.conf.all.disable_ipv6=0

#start unbound
cd /cpcapi2_auto_tests_root_folder/unbound/linux
sh run_unbound.sh &
cd ../../

# warm up unbound; sometimes it seems slow to start, which can cause tests to fail
dig AAAA ipv4only.arpa @127.0.0.1

export CPCAPI2_RESOURCE_PATH=/cpcapi2_auto_tests_root_folder/runtime_resources/
export CPCAPI2_DOCKER_CONTAINERIZED=1
export CPCAPI2_NO_DRAW_LOCAL_VIDEO=1
#for TestPcapCapture / saving pcaps
export CPCAPI2_LOGGING_FILE_PATH=/cpcapi2_auto_tests_root_folder/logs
export CPCAPI2_ARTIFACT_FILE_PATH=/cpcapi2_auto_tests_root_folder/runtime_artifacts


cd /tmp
mkdir cpcapi2_auto_tests
cd cpcapi2_auto_tests
ln -s /cpcapi2_auto_tests_root_folder/runtime_resources/ runtime_resources
ln -s /cpcapi2_auto_tests_root_folder/logs logs
/cpcapi2_auto_tests_root_folder/Linux/build/cpcapi2_auto_tests "--gtest_filter=$TARGETS" >> /cpcapi2_auto_tests_root_folder/logs/$fileName.txt 2>&1 &
procId=$!
#gdb -ex='set confirm on' -ex=run -ex=quit --args /cpcapi2_auto_tests_root_folder/Linux/build/cpcapi2_auto_tests "--gtest_filter=$TARGETS"
# needed to get return code since we already ran $! (which overwrites $?)
wait $!
ret=$?
trap 'errorLog $0 $TARGETS $LINENO $ret' ERR

testEndDate=`date +%s`
deltaMin=$(((testEndDate-testStartDate)/60))
echo "About to write test times"
normalizedTestName=$test
if [[ "$normalizedTestName" == *. ]]
then
	# make sure wildcards end in .*
	normalizedTestName="$normalizedTestName*"
fi	

echo "$deltaMin min /$normalizedTestName" >> /cpcapi2_auto_tests_root_folder/Linux/testTimes.txt

if [[ $ret -gt 1 ]]; then #crash
	mkdir -p /cpcapi2_auto_tests_root_folder/dumps
        dumpName=core.$procId.dmp
 
        if test -f "/cpcapi2_auto_tests_root_folder/tmp/$dumpName"; then
		while [[ $(fuser -f /cpcapi2_auto_tests_root_folder/tmp/$dumpName) ]]
                do
                        echo "[ Entrypoint ] Waiting for dump to finish writing.." >> /cpcapi2_auto_tests_root_folder/logs/$fileName.txt 
			sleep 1
                done
                
		mv /cpcapi2_auto_tests_root_folder/tmp/$dumpName /cpcapi2_auto_tests_root_folder/dumps/$fileName.dmp

                # since gdb seems to be quite resource intensive, use flock to lock a file such that only one container is running gdb at a time
		flock /cpcapi2_auto_tests_root_folder/tmp/gdb.lock -c "gdb -ex \"thread apply all bt\" -batch -n /cpcapi2_auto_tests_root_folder/Linux/build/cpcapi2_auto_tests /cpcapi2_auto_tests_root_folder/dumps/$fileName.dmp > /cpcapi2_auto_tests_root_folder/dumps/$fileName.dmp.txt"
        else
		echo "[ Entrypoint ] cpcapi2_auto_tests exited with code $ret but no core dump available" >> /cpcapi2_auto_tests_root_folder/logs/$fileName.txt 
        fi
fi

exit $ret
