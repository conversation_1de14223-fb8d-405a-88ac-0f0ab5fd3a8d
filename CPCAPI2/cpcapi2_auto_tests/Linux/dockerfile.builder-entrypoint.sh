#!/bin/bash
set -e
NUMBER_OF_CPUS=`grep -c ^processor "/proc/cpuinfo"`
CMAKE=cmake
#git clone https://chromium.googlesource.com/chromium/tools/depot_tools.git
export PATH=$PATH:/path/to/depot_tools
export CCACHE_DIR=/.ccache  # mount is setup by docker-build.sh
export CCACHE_BASEDIR=/CPCAPI2 # mount is setup by docker-build.sh


if command -v cmake3 >/dev/null 2>&1;
then
  CMAKE=cmake3
fi

DISABLE_CPCAPI2_SDK_LICENSING=0
USE_CLANG_TIDY=FALSE
TARGETS=${*}
while getopts ":o:lj:t" opt; do
   case $opt in
    o)
      echo "build only this test source file: $OPTARG"
      OVERRIDE_TEST_FILE_BUILD_LIST="$OPTARG"
      TARGETS=${*:3}
      echo "targets are : $TARGETS"
      ;;
   \?)
      echo "invalid option, building full tests"
      ;;
   l)
      echo "build with CPCAPI2_BRAND_SDK_LICENSING brand option set to 0"
      DISABLE_CPCAPI2_SDK_LICENSING=1
      TARGETS=${*:3}
      ;;
   j)
      PARALLEL_BUILD_THREAD_ARG="-j $OPTARG"
      echo "overriding number of parallel build threads to $OPTARG"
      ;;
   t)
      USE_CLANG_TIDY=TRUE
      echo "Clang tidy enabled"
      ;;
   :)
      echo "building full tests"
      TARGETS=${*:3}
      ;;
   esac
done


echo "Building ${*}"
TARGETS_BUILT=
if [[ $TARGETS == *'RelWithDebInfo'* ]] || [[ -z "$TARGETS" ]] 
then
   echo "Build_Type=RelWithDebInfo"
   echo $OVERRIDE_TEST_FILE_BUILD_LIST
   $CMAKE -G Ninja -DOVERRIDE_TEST_FILE_BUILD_LIST=$OVERRIDE_TEST_FILE_BUILD_LIST -DDISABLE_CPCAPI2_SDK_LICENSING=$DISABLE_CPCAPI2_SDK_LICENSING -DCMAKE_BUILD_TYPE=RelWithDebInfo -DCPCAPI2_CONAN=1 -DENABLE_CLANG_TIDY=$USE_CLANG_TIDY $PARALLEL_BUILD_THREAD_ARG ../../
elif [[ $TARGETS == *'release'* ]]
then
   echo "Build_Type=Release"
   echo $OVERRIDE_TEST_FILE_BUILD_LIST
   $CMAKE -G Ninja -DOVERRIDE_TEST_FILE_BUILD_LIST=$OVERRIDE_TEST_FILE_BUILD_LIST -DDISABLE_CPCAPI2_SDK_LICENSING=$DISABLE_CPCAPI2_SDK_LICENSING -DCMAKE_BUILD_TYPE=Release -DCPCAPI2_CONAN=1 -DENABLE_CLANG_TIDY=$USE_CLANG_TIDY $PARALLEL_BUILD_THREAD_ARG ../../
elif [[ $TARGETS == *'Debug'* ]]
then
   echo "Build_Type=Debug"
   echo $OVERRIDE_TEST_FILE_BUILD_LIST
   $CMAKE -G Ninja -DOVERRIDE_TEST_FILE_BUILD_LIST=$OVERRIDE_TEST_FILE_BUILD_LIST -DDISABLE_CPCAPI2_SDK_LICENSING=$DISABLE_CPCAPI2_SDK_LICENSING -DCMAKE_BUILD_TYPE=Debug -DCPCAPI2_CONAN=1 -DENABLE_CLANG_TIDY=$USE_CLANG_TIDY $PARALLEL_BUILD_THREAD_ARG ../../
fi

NEW_TARGETS=
for target in $TARGETS
do
   if [ ! $target == 'RelWithDebInfo' ] &&  [ ! $target == 'release' ] && [ ! $target == 'debug' ]
   then
      NEW_TARGETS+=$target
   fi
done
TARGETS=$NEW_TARGETS

time ninja --verbose $TARGETS
#time make -j$NUMBER_OF_CPUS $TARGETS

set +e
ccache -s
set -e

objcopy --only-keep-debug cpcapi2_auto_tests cpcapi2_auto_tests.debug
objcopy --strip-debug cpcapi2_auto_tests cpcapi2_auto_tests
objcopy --add-gnu-debuglink=cpcapi2_auto_tests.debug cpcapi2_auto_tests


echo Build Complete
echo Built$TARGETS_BUILT
