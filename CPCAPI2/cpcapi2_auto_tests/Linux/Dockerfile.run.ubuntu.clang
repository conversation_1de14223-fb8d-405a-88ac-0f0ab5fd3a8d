FROM ubuntu:20.04

RUN apt-get update && apt-get dist-upgrade -y

RUN apt-get update && apt-get install -y wget gnupg
RUN wget -O - https://apt.llvm.org/llvm-snapshot.gpg.key | apt-key add -
RUN echo "deb http://apt.llvm.org/focal/ llvm-toolchain-focal-11 main" >> /etc/apt/sources.list.d/llvm.list

RUN apt-get update && apt-get install -y clang-11 lldb-11 lld-11

# X11
RUN apt-get update && apt-get install -y libx11-6 libxext6

# Break caching to force update on every run
#ADD "https://www.random.org/cgi-bin/randbyte?nbytes=10&format=h" skipcache

RUN apt-get update && apt-get dist-upgrade -y

WORKDIR /cpcapi2_auto_tests
