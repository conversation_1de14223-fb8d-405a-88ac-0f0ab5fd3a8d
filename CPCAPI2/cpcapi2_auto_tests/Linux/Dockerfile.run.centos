FROM centos:7

RUN yum install -y \
# Install Mozilla's root CA certs
ca-certificates \
# Install gdb and net-tools for debugging and setting up ipv6
gdb net-tools sudo \
# is a DNS server used by the auto tests
unbound \
# for video enabled SDKs
libX11 libXext \
bind-utils \
# for ss command (list socket usage)
iproute \
# for PcapManager SDK module
libpcap \
# for fuser (used to see if dump file is still open)
psmisc \
# for flock
util-linux \
# for sipp
&& yum install -y epel-release \
&& yum install -y sipp


WORKDIR /home/<USER>

ADD dockerfile.run.centos-entrypoint.sh dockerfile.run.centos-entrypoint.sh

# we'll run as the user docker inside the container, and allow it to sudo
RUN adduser docker
RUN usermod -a -G wheel docker
RUN echo '%docker ALL=(ALL) NOPASSWD:ALL' >> /etc/sudoers
RUN chmod +rx ./dockerfile.run.centos-entrypoint.sh

USER docker

ENTRYPOINT ["sh", "-c", "./dockerfile.run.centos-entrypoint.sh \"$@\"", "--"]
