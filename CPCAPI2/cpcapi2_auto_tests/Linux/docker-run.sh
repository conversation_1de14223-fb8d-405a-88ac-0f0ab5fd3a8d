#!/bin/bash


tooManyCrashes()
{
      shopt -s nullglob
      dumpFiles=( ../dumps/*.dmp )
      noDumpFiles=${#dumpFiles[@]}
      if [ "$noDumpFiles" -gt 20 ]; then
         return 0;
      fi
      shopt -u nullglob

      return 1;
}

waitForContainerCount()
{
   activeContainers=$(docker ps -q | wc -l)
   
   while [ "$activeContainers" -gt $1 ]
   do
      sleep 1
      activeContainers=$(docker ps -q | wc -l)
   done
}

removeAllContainers()
{
   # todo: don't touch non auto test containers
   DOCKER_CONTAINER=`docker ps -aq`
   if [[ ! -z "$DOCKER_CONTAINER" ]];
   then
      docker stop $DOCKER_CONTAINER > /dev/null || true
      docker rm $DOCKER_CONTAINER > /dev/null || true
   fi
}

failure() {
  local lineno=$1
  local msg=$2
  echo "Failed at $lineno: $msg"
}
trap 'failure ${LINENO} "$BASH_COMMAND"' ERR


export MSYS_NO_PATHCONV=1

set -e

# adds default grouping parameters
set -- "$@ --group=NACKStringBuilderTest.* --group=Rtcp*.*"

# increase max number of processes the current user can run, since we'll use that same
# user to run the docker containers as.
# TODO: worthwhile to try restoring old value on exit?
ulimit -u 8000 || { echo "Incrase of max processes for user failed. Try logging out and in from your shell session and try again."; exit 1; }

PARALLEL_SINGLE_TEST_COUNT=100
GTEST_FILTER=""
for i in $@; do
  case $i in
    --parallel_single_test=*) # runs a single test many times in parallel
      PARALLEL_SINGLE_TEST=true
      PARALLEL_SINGLE_TEST_NAME=${i#*=} 
      ;;
    --parallel_single_test_count=*)
      PARALLEL_SINGLE_TEST_COUNT=${i#*=}
      ;;
    --gtest_filter=*)
      GTEST_FILTER=${i#*=}
      ;;
    --group=*)
      # if '--group=xxxTest' is passed in, remove '--group=' and add 'xxxTest' to $GROUP
      GROUP="${i#*=} ${GROUP}"
      # warning: even though groups are parsed out here, they need to remain for use in TARGETS
      # later
      ;;
    *)
      ;;
  esac
done

# string to array
GROUP=($GROUP)

TARGETS="$@"
echo $TARGETS

[ ! -e ./../failedTests.txt ]||rm ./../failedTests.txt
for j in $(docker container ls -q) ; do echo "kill $j" && (docker kill "$j" || true); done
#for j in $(docker ps -aq) ; do docker kill "$j"; done
for j in $(docker ps -aq) ; do echo "rm $j" && (docker rm "$j" || true) || true; done

[ ! -e ./../tests.txt ] || rm ./../tests.txt

if [[ -z "${CPCAPI2_DOCKER_PARALLEL_TEST_COUNT}" ]]; then
  PARALLEL_TEST_COUNT=120
else
  PARALLEL_TEST_COUNT="${CPCAPI2_DOCKER_PARALLEL_TEST_COUNT}"
fi

rm -rf ../logs/*
rm -rf ../dumps/*

# stop and remove all containers (TODO: what if other non-auto test containers are running?)
DOCKER_CONTAINER=`docker ps -aq`
echo "Running Docker containers: $DOCKER_CONTAINER"
if [ ! -z "$DOCKER_CONTAINER"]
then
  docker stop $DOCKER_CONTAINER > /dev/null || true
  docker rm $DOCKER_CONTAINER > /dev/null || true
fi


docker build -t cpcapi2_auto_tests:centos -f Dockerfile.run.centos .

echo "Will run maximum of ${PARALLEL_TEST_COUNT} tests in parallel docker containers"

#get number of total tests
if [[ ! -z "$GTEST_FILTER" ]]; then
  GTEST_FILTER_ARG=--gtest_filter="$GTEST_FILTER"
fi

if [ "$PARALLEL_SINGLE_TEST" = true ] ; then
   totalTestsCount=$PARALLEL_SINGLE_TEST_COUNT
else

   total=$(docker run --user $(id -u):$(id -g) -i -v /var/run/docker.sock:/var/run/docker.sock -v /usr/bin/docker:/usr/bin/docker --rm --privileged --security-opt seccomp=unconfined --mount type=bind,source=$(pwd)/../,target=/cpcapi2_auto_tests_root_folder --entrypoint /cpcapi2_auto_tests_root_folder/Linux/build/cpcapi2_auto_tests cpcapi2_auto_tests:centos --gtest_list_tests $GTEST_FILTER_ARG)
   IFS=$'\n' arr=($total)
   totalTest=( ${arr[@]#*'.'} )
   totalTest=( ${totalTest[@]##*'DISABLED_'*} ) #exclude disabled tests when counting the number of total tests
   totalTestsCount=$(echo ${#totalTest[@]})
fi

failedTests=""
fail=0
if [[ $TARGETS == *'debug'* ]]
then
   docker run --user $(id -u):$(id -g) -it -v /var/run/docker.sock:/var/run/docker.sock -v /usr/bin/docker:/usr/bin/docker --rm --privileged --security-opt seccomp=unconfined --mount type=bind,source="$(pwd)"/../,target=/cpcapi2_auto_tests_root_folder cpcapi2_auto_tests:centos sh &
   return 0
else

   n=0
   i=1

   if [ "$PARALLEL_SINGLE_TEST" = true ] ; then
      testsRunInOneContainer=$PARALLEL_SINGLE_TEST_NAME
      for (( c=0; c<$PARALLEL_SINGLE_TEST_COUNT; c++ )) do
         testList="$testList $PARALLEL_SINGLE_TEST_NAME"
      done

      # in non-loop mode, we run the binary once to get a list of excluded tests; we don't need that here, so just fake it
      touch ./../tests.txt

   else

      # the only purpose of running this docker container is to run cpcapi2_auto_tests to generate a list of all tests it knows about
      # (with --gtest_list_tests). the container will then generate tests.txt (in the host located at ../tests.txt) with a list of
      # tests we need to run.
      docker run --user $(id -u):$(id -g) -i -v /var/run/docker.sock:/var/run/docker.sock -v /usr/bin/docker:/usr/bin/docker --rm --privileged --security-opt seccomp=unconfined --mount type=bind,source="$(pwd)"/../,target=/cpcapi2_auto_tests_root_folder cpcapi2_auto_tests:centos ./dockerfile.run.centos-entrypoint.sh "$TARGETS"

      testListToRunPath="./../tests.txt"
      sortedPreviousTestListPath="sortedTestTimes.txt"
      if [ ! -f "$testListToRunPath" ]; then
         echo "Unable to optimize based on previous test runs; $testListToRunPath not found"
      elif [ ! -w "$testListToRunPath" ]; then
         echo "Unable to optimize based on previous test runs; $testListToRunPath not writable"
      elif [ ! -f "$sortedPreviousTestListPath" ]; then
         echo "Unable to optimize based on previous test runs; $sortedPreviousTestListPath not found"
      elif [[ ! -z "$GTEST_FILTER" ]]; then
         echo "Not optimizing test order since --gtest_filter used"
      else
         echo "Optimizing based on previous test run times.."
         python sort-tests.py -u "$testListToRunPath" -s "$sortedPreviousTestListPath" -o "$testListToRunPath"
      fi

      testList=$(cat $testListToRunPath)
      if [ -z "$testList" ]; then
         echo "No test found to run! Double check test passed to --gtest_filter if specified"
         exit 1
      fi

      # don't erase optimized test order if --gtest_filter is being used;
      # we might run again soon without gtest_filter being used
      if [[ -z "$GTEST_FILTER" ]]; then
         rm -rf testTimes.txt sortedTestTimes.txt
      fi

      testsRunInOneContainer=$(echo $testList | awk -v c=$i -F' ' '{print $c}') #parse out the ith test. tests are seperated by space in testList
   fi


   testIndex=0
   dockerRunPidIndex=0
   while [ ! -z "$testsRunInOneContainer" ]
   do
      if tooManyCrashes
      then
         echo "Too many test cases have crashed; aborting run"
         break
      fi

      #remove '*' and ':' in $test cause docker container name cannot contain special characters
      if [[ $testsRunInOneContainer == *"*"* ]]; then
         for group in "${GROUP[@]}"; do
            if [[ $testsRunInOneContainer == $group ]]; then
     	       testsNameWithGrouping=$group
	    fi
         done
         testsNameWithGrouping=${testsNameWithGrouping//'*'/}
         testsNameWithGrouping=${testsNameWithGrouping//:/}
      else
         testsNameWithGrouping=$testsRunInOneContainer #groupName is just the test name if the container will only run a single test
      fi

      if echo "$testsNameWithGrouping" | grep -q "_NPE"; then
         cpuutil=$(top -b -n1 -p 1 | fgrep "Cpu(s)" | tail -1 | awk -F'id,' -v prefix="$prefix" '{ split($1, vs, ","); v=vs[length(vs)]; sub("%", "", v); printf "%s%d\n", prefix, (100 - v)*100 }')
         while [ "$cpuutil" -ge "5000" ]
         do
            echo "waiting for CPU utilization to drop $cpuutil"
            sleep 1
            cpuutil=$(top -b -n1 -p 1 | fgrep "Cpu(s)" | tail -1 | awk -F'id,' -v prefix="$prefix" '{ split($1, vs, ","); v=vs[length(vs)]; sub("%", "", v); printf "%s%d\n", prefix, (100 - v)*100 }')
         done
      fi

      # TODO: progress indicator. right now we can't tell how many tests run in a group, so
      # we can't properly update testIndex
      #echo "[$testIndex/$totalTestsCount] start $name"
      echo "start $testsNameWithGrouping"
      testIndex=$((testIndex+1))

      if [ "$PARALLEL_SINGLE_TEST" = true ] ; then
         # when in this mode we need to differentiate log file names, since each test has the same name
         testLogPrefix=$testIndex
      else
         testLogPrefix=""
      fi

      docker run --user $(id -u):$(id -g) -d --env-file env.list --env CPCAPI2_TEST_ALIANZA_API_USERNAME --env CPCAPI2_TEST_ALIANZA_API_PASSWORD --env CPCAPI2_SIPUA_TEST_ENVIRONMENT_ID --env CPCAPI2_CPE_TEST_LEVEL --env CPCAPI2_TEST_ALIANZA_API_HTTP_DEBUG_ENABLED --env CPCAPI2_PCAP_CAPTURE_TEST_CASES -i -v /var/run/docker.sock:/var/run/docker.sock -v /usr/bin/docker:/usr/bin/docker --privileged --security-opt seccomp=unconfined --mount type=bind,source="$(pwd)"/../,target=/cpcapi2_auto_tests_root_folder cpcapi2_auto_tests:centos ./dockerfile.run.centos-entrypoint.sh "$testsRunInOneContainer" "$testsNameWithGrouping" "$testLogPrefix" > /dev/null 2>&1 & 
      dockerRunPids[${dockerRunPidIndex}]=$!
      dockerRunPidIndex=$((dockerRunPidIndex+1))

      n=$((n+1))
      while [ "$n" -ge ${PARALLEL_TEST_COUNT} ]
      do
         # first we need to wait for all docker run commands to actually have completed,
         # so that we get accurate results from querying the number of active docker conatiners
         for pid in ${dockerRunPids[*]}; do
            wait $pid || true
         done

         unset dockerRunPids
         dockerRunPidIndex=0

	 # now we can repeatedly re-count the number of active docker containers, to wait for
         # some to free up

         n=0
         for j in $(docker ps -q)
         do
            n=$((n+1))
         done
         sleep 0.2
      done
      i=$((i+1))
      testsRunInOneContainer=$(echo $testList | awk -v c=$i -F' ' '{print $c}')
   done
fi

echo -ne "Waiting for docker containers to finish..\r"

# make sure there are no background docker run tasks still starting up
for pid in ${dockerRunPids[*]}; do
   wait $pid || true
done

activeContainers=$(docker ps -q | wc -l)
while [ "$activeContainers" -gt 0 ]
do
   if tooManyCrashes 
   then
      echo -ne "\r\nToo many test cases have crashed; waiting for 10 more containers to finish then aborting run. There will not be a full amount of symbolicated dump files available."
      containerWaitCount=$((activeContainers - 10))
      waitForContainerCount $containerWaitCount
      removeAllContainers
      exit 1
   else
      echo -ne "Waiting for docker containers to finish.. still active: $activeContainers   \r"
      sleep 1
   fi
   activeContainers=$(docker ps -q | wc -l)
done

echo -ne '\n\r\nDone!\n\n'

# check exit code of each container before removing it
#number of failed tests
failedTestsCount=0

allContainerIds=$(docker ps -aq)

allStatusCodes=$(docker inspect ${allContainerIds[@]} --format='{{.State.Status}}')
allExitCodes=$(docker inspect ${allContainerIds[@]} --format='{{.State.ExitCode}}')
allArgs=$(docker inspect ${allContainerIds[@]} --format='{{join .Args ","}}')

# convert to array
readarray -t allStatusCodes <<<"$allStatusCodes"
readarray -t allExitCodes <<<"$allExitCodes"
readarray -t allArgs <<<"$allArgs"

containerIndex=0
for k in $allContainerIds
do
      ret=${allExitCodes[$containerIndex]}
      args=${allArgs[$containerIndex]}

      testNameInCurrentContainer=$(echo $args | awk -F',' '{print $(NF-2)}')
      groupNameInCurrentContainer=$(echo $args | awk -F',' '{print $(NF-1)}')

      if [[ $ret -eq 1 ]]; then #failure
         if [ "$groupNameInCurrentContainer" != "$testNameInCurrentContainer" ]; then
            fileName=${groupNameInCurrentContainer////-}
            fileName=$(echo $fileName |awk -F' ' '{print $1}')
            failed=$(grep -r "FAILED  ] " ./../logs/$fileName.txt | grep "[^:)]$")
                                failed=( $failed )
                                for faildTest in "${failed[@]}" #loop through $failed array in case there are multiple tests failed in this group
                                do
                                        failedTests="$failedTests\nF:"$faildTest""
                                        failedTestsCount=$((failedTestsCount+1))
                                done
         else
                                failedTestsCount=$((failedTestsCount+1))
            if [ "$PARALLEL_SINGLE_TEST" = true ] ; then
               failedTests="$failedTests\nF:"$containerIndex-$testNameInCurrentContainer""
	    else
               failedTests="$failedTests\nF:"$testNameInCurrentContainer""
            fi
         fi
      elif [[ $ret -gt 1 ]]; then #crash
         failedTestsCount=$((failedTestsCount+1))
         failedTests="$failedTests\nC:"$testNameInCurrentContainer""
      fi

      containerIndex=$((containerIndex+1))
done

docker rm $(docker ps -aq) > /dev/null 2>&1 || true

sort -r -s -n -k 1,1 testTimes.txt > sortedTestTimes.txt

testSummary="FAILED TESTS: $failedTestsCount/$totalTestsCount"
testSummary="$testSummary\n\n$failedTests"

printf "$testSummary"
printf "$testSummary" > .phabricator-comment


printf "\n\n\nTop 5 longtest test run times:\n";
head --lines=5 sortedTestTimes.txt



if [[ ! -z "$failedTests" ]]
then
  exit 1
else
  exit 0
fi
