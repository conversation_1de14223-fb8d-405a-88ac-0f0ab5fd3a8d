#!/bin/sh
GTEST_FILTER="*.*"

for i in $@; do
  case $i in
    --gtest_filter=*)
      GTEST_FILTER=${i#???????????????} # Remove --gtest_filter= using fixed number of characters
      ;;
    --group=*)
      # if '--group=xxxTest' is passed in, remove '--group=' and add 'xxxTest' to $GROUP
    	GROUP="${i#*=} ${GROUP}" 
    	;;
    *)
    	break
    	;;
  esac
  shift
done

# Bash will expand * before the case statement
GTEST_FILTER=`echo "$GTEST_FILTER" | sed -e "s/\*/@/g"`

include=`echo "$GTEST_FILTER" | sed -e "s/\-.*//g"`
exclude=${GTEST_FILTER#$include}
exclude=${exclude#?} # Strip -

includedTestCases=`echo "$include" | sed -e "s/:/\n/g"`
excludedTestCases=`echo "$exclude" | sed -e "s/:/\n/g"`

if [ "$GTEST_FILTER" == "@.@" ]; then  #if no --gtest_filter passed in, get all tests specified by override_base_test_list in cpcapi2_auto_tests.cpp 
   /cpcapi2_auto_tests_root_folder/Linux/build/cpcapi2_auto_tests "--gtest_list_tests" > test_lists.txt 
else  #if --gtest_filter is passed in, get all tests
   /cpcapi2_auto_tests_root_folder/Linux/build/cpcapi2_auto_tests "--gtest_filter=*.*" "--gtest_list_tests" > test_lists.txt
fi

testsList=$(cat test_lists.txt)
allTests=""

inGroup=false
testGroup=""
prevGroup=""
while read testcase; do
  case $testcase in
    DISABLED* )
      continue
      ;;
    *. )
      testGroup=${testcase%?}
		
      for group in $GROUP; do
			a=${group%.*}		
 	      if [[ $testGroup == $a ]]; then #cheack if testGroup is specied in $GROUP
	         	inGroup=true
					# $testGroup is specified in $GROUP, and $testGroup belongs to the same group as the previous $testGroup
					# e.g RtcpNackStatsTest belongs to the same group as RtcpPacketTest does
					if [[ $group == $prevGroup ]]; then 
                  existingGroup=true			
					else
						newGroup=true
					fi
					prevGroup=$group
	         continue 2
	      else
	         inGroup=false
	      fi
      done
      continue
      ;;
  esac
  
  #remove the comments, the parameterized tests name is followed by some comments. 
  #e.g. "SecureAndNonSecureCalls/ParameterizedNetworkChangeTest.BasicCallLoseNetworkRegainNetwork/1  # GetParam() = 1-byte object <00>"
  #testcase here should be "SecureAndNonSecureCalls/ParameterizedNetworkChangeTest.BasicCallLoseNetworkRegainNetwork/1"
  testcase=${testcase%%' '*} 

  fullTest=$testGroup.$testcase
  for excludeTestCase in $excludedTestCases; do
    excludeTestCase=`echo "$excludeTestCase" | sed -e "s/@/\*/g"`
    case $fullTest in
      $excludeTestCase )
        echo "Skipping $fullTest due to -$excludeTestCase"
        continue 2
        ;;
    esac
  done

  for includeTestCase in $includedTestCases; do
    includeTestCase=`echo "$includeTestCase" | sed -e "s/@/\*/g"`
    case $fullTest in
      $includeTestCase )
		# if $fullTest should be in one of the groups, then don't append it to $allTest since the $testGroup is added in $allTests already.
		#  e.g. do not add RtcpNackStatsTest.Request since RtcpNackStatsTest.* is in $allTests already
		if [ "$inGroup" = false ]; then
			allTests="$allTests $fullTest"
		elif [ "$existingGroup" = true ]; then
			allTests="$allTests$testGroup.*:" # append $testcase to $alltests without space, so that in dockerfile.run-entrypoint.sh, tests not separated by space will be passed to a same container	
			existingGroup=false
		elif [ "$newGroup" = true ]; then
			allTests="$allTests $testGroup.*:"
			newGroup=false
		fi
        continue 2
        ;;
    esac
  done
done <test_lists.txt

#if tests are not in alphabetical order, some tests in the group might be seperated.
for group in $GROUP; do
	for test in $allTests; do
    	if [[ $test == *"*"* ]]; then
			if [[ $test == $group ]]; then
            	tempTests=$tempTests$test
                allTests=${allTests/"${test}"/}
			fi
		fi
	done
	allTests="$allTests $tempTests "
	tempTests=""
done

echo "$allTests" > /cpcapi2_auto_tests_root_folder/tests.txt

	# the host running docker  (orchestrating all parallel docker containers)
	# needs to be able to read tests.txt. that host may be running docker-run.sh
	# as a different user than docker is running as, so work around this by
	# granting all users read permission to this file.
	# note: /cpcapi2_auto_tests_root_folder/ is currently mounted to the host
chmod o+w /cpcapi2_auto_tests_root_folder/tests.txt

