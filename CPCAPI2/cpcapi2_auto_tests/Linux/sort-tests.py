# sorts a list of tests names using previous test run time results.
# the idea is we want to run tests that took a long time first, to
# take advantage of parallel processing

import collections
import sys, getopt
import argparse

def main(argv):
   unorderedTestsToRunFilePath = ''
   sortedPreviousTestsFilePath = ''
   outputFilePath = ''
   #try:
   #   opts, args = getopt.getopt(argv,"u:s:o:",["unorderedTestFile=","sortedPreviousTestsFile=","outputFile="])
   #except getopt.GetoptError:
   #   print 'test.py -u <unorderedTestsToRunFilePath> -s <sortedPreviousTestsFilePath> -o <outputFilePath>'
   #   sys.exit(2)
   #for opt, arg in opts:
   #   if opt in ("-u", "--unorderedTestFile"):
   #      unorderedTestsToRunFilePath = arg
   #   elif opt in ("-s", "--sortedPreviousTestsFile"):
   #      sortedPreviousTestsFilePath = arg
   #   elif opt in ("-o", "--outputFile"):
   #      outputFilePath = arg

   parser = argparse.ArgumentParser()
   parser.add_argument("-u", "--unorderedTestsToRunFilePath", required=True)
   parser.add_argument("-s", "--sortedPreviousTestsFilePath", required=True)
   parser.add_argument("-o", "--outputFilePath", required=True)
   args = vars(parser.parse_args())

   unorderedTestsToRunFilePath = args["unorderedTestsToRunFilePath"]
   sortedPreviousTestsFilePath = args["sortedPreviousTestsFilePath"]
   outputFilePath = args["outputFilePath"]

   testList = []


   with open(unorderedTestsToRunFilePath) as f:
      for l in f:
         if l:
            tests = l.strip().split()
            for test in tests:
               # TODO: why do grouped tests end up with a colon at the end, when in this list? 
               test = test.rstrip(':')
               testList.append(test)
   f.close()

   previousSortedTests = collections.OrderedDict()

   # the file should be ordered longest test first
   with open(sortedPreviousTestsFilePath) as f:
      for l in f:
         if l:
            testLineInfo = l.split(" ")
            testDurationMins = float(testLineInfo[0])
            testName = testLineInfo[2].lstrip('/').rstrip('\n')
            #print("discovered test " + testName);
            previousSortedTests[testName] = testDurationMins
   f.close()

   testRunOrderDict = {}
   foundTests = 0

   # get the rank of how long the test we want to run ran last time
   # e.g. the 1st ranked test took longest to run last time
   for testName in testList:
      if testName in previousSortedTests.keys():
         if "_NPE" in testName:
            previousLongestRunnerRank = 99999999
            foundTests = foundTests + 1
         else:
            #print("Assigning test " + testName + " rank " + str(previousSortedTests.keys().index(testName)) + 1)
            previousLongestRunnerRank = previousSortedTests.keys().index(testName) + 1
            foundTests = foundTests + 1
      else:
         print(testName + " not found")
         previousLongestRunnerRank = 99999999
      
      testRunOrderDict[testName] = previousLongestRunnerRank


   # sort the tests we want to run -- tests that took longest last time should start earlier than those that
   # took less time
   testRunOrderedDict = sorted(testRunOrderDict.items(), key=lambda kv: kv[1])
   f = open(outputFilePath, "w")

   for test in testRunOrderedDict:
      #print("Test name: " + test[0] + " time: " + str(test[1]))
      f.write(test[0] + " ")

   f.close()

   print("Optimized order of " + str(foundTests) + " tests")

   #print("Tests: " + str(testList))

   #print("Sorted previous tests: " + str(

if __name__ == "__main__":
   main(sys.argv[1:])
