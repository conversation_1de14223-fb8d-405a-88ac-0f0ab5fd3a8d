version: "2.1"
services:
   unbound:
      image: "sapcc/unbound:latest"
   cpcapi2_auto_tests:
      build: .
networks:
   default:
      enable_ipv6: true
      driver_opts:
         com.docker.network.enable_ipv6: "true"
   ipam:
      driver: default
      config:
         - subnet: **********/16
           gateway: **********
         - subnet: 2001:x:y:z:0002::/80
           gateway: 2001:x:y:z:0002::1
