#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif
#include <sys/types.h>
#include <sys/stat.h>
#include <chrono>
#include <ctime>
#include <iostream>
#include <memory>
#include "cpcapi2_test_fixture.h"
#include "test_account_events.h"
#include <utils/msrp_strcasestr.h>
#include "impl/phone/PhoneInterface.h"
#include "log/LocalLoggerHandler.h"
#include "impl/call/SipConversationManagerInternal.h"
#include "test_framework/test_runtime_environment.h"
#include "test_framework/android_bg_bookkeeper.h"
#include "alianza_api/impl/alianza_api_manager_interface.h"
#include "sipua_alianza_api_test_helper.h"
#include "sipua_alianza_api_test_events.h"

#include <boost/algorithm/string/case_conv.hpp>

#include <stdio.h>  /* defines FILENAME_MAX */
#ifdef WIN32
#include <direct.h>
#define GetCurrentDir _getcwd
#else
#include <unistd.h>
#define GetCurrentDir getcwd
#endif
#include <cctype>

#ifdef ANDROID
#include "../../../core/language_wrapper/Android/jni/AndroidNetworking.h"
#include "../../../core/language_wrapper/Android/jni/AndroidBackgroundManagerImpl.h"
#include "../../../core/language_wrapper/Android/jni/AndroidBackgroundHandler.h"
#include "impl/phone/NetworkChangeManager_Android.h"
#endif

using namespace CPCAPI2;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::SipConversation;
using namespace CPCAPI2::SipPresence;
using namespace CPCAPI2::SipEvent;
#if (CPCAPI2_BRAND_SIP_FILE_TRANSFER_MODULE == 1)
using namespace CPCAPI2::SipFileTransfer;
#endif
using namespace CPCAPI2::SipInstantMessage;
using namespace CPCAPI2::SipMessageWaitingIndication;
#if (CPCAPI2_BRAND_SIP_CHAT_MODULE == 1)
using namespace CPCAPI2::SipChat;
#endif
#if (CPCAPI2_BRAND_SIP_STANDALONE_MESSAGING_MODULE == 1)
using namespace CPCAPI2::SipStandaloneMessaging;
#endif
using namespace CPCAPI2::RcsCapabilityDiscovery;
using namespace CPCAPI2::RcsProvision;
using namespace CPCAPI2::Media;
using namespace CPCAPI2::PeerConnection;
#if (CPCAPI2_BRAND_WEB_CALL_MODULE == 1)
using namespace CPCAPI2::WebCall;
#endif
#if (CPCAPI2_BRAND_PTT_MODULE == 1)
using namespace CPCAPI2::PushToTalk;
#endif
#if (CPCAPI2_GENBAND_MODULE == 1)
using namespace CPCAPI2::Genband;
#endif
#if (CPCAPI2_BRAND_GENBAND_SOPI_MODULE == 1)
using namespace CPCAPI2::GenbandSopi;
#endif
#if (CPCAPI2_BRAND_XCAP_MODULE == 1)
using namespace CPCAPI2::XCAP;
#endif
#if (CPCAPI2_BRAND_WATCHER_INFO_MODULE == 1)
using namespace CPCAPI2::WatcherInfo;
#endif
#if (CPCAPI2_BRAND_CONFERENCE_MODULE == 1)
using namespace CPCAPI2::SipConference;
#endif
#if (CPCAPI2_BRAND_REMOTE_SYNC_MODULE == 1)
using namespace CPCAPI2::RemoteSync;
#endif
#if (CPCAPI2_BRAND_ANALYTICS_MODULE == 1)
using namespace CPCAPI2::Analytics;
#endif
#if (CPCAPI2_BRAND_STRETTO_UEM_MODULE == 1)
using namespace CPCAPI2::StrettoUem;
#endif
#if (CPCAPI2_BRAND_VCCS_MODULE == 1)
using namespace CPCAPI2::VCCS;
#endif
#if (CPCAPI2_BRAND_SNS_MODULE == 1)
using namespace CPCAPI2::Notification;
#endif
#if (CPCAPI2_BRAND_BIEVENTS_MODULE == 1)
using namespace CPCAPI2::BIEvents;
#endif
#if (CPCAPI2_BRAND_STRETTO_PROVISIONING_MODULE == 1)
using namespace CPCAPI2::StrettoProvisioning;
#endif
#if (CPCAPI2_BRAND_PUSH_NOTIFICATION_SERVER_MODULE == 1) || (CPCAPI2_BRAND_PUSH_NOTIFICATION_CLIENT_MODULE == 1)
using namespace CPCAPI2::PushService;
using namespace CPCAPI2::PushEndpoint;
#endif
#if (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1 || CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
using namespace CPCAPI2::JsonApi;
#endif
#if (CPCAPI2_BRAND_ORCHESTRATION_SERVER_MODULE == 1)
using namespace CPCAPI2::OrchestrationServer;
#endif
#if (CPCAPI2_BRAND_CLOUD_WATCHDOG_SERVER_MODULE == 1)
using namespace CPCAPI2::CloudWatchdog;
#endif
#if (CPCAPI2_BRAND_CLOUD_SERVICE_CONFIG_MODULE == 1)
using namespace CPCAPI2::CloudServiceConfig;
#endif
#if (CPCAPI2_BRAND_CONFERENCE_BRIDGE_MODULE == 1) || (CPCAPI2_BRAND_CONFERENCE_BRIDGE_REGISTRAR_MODULE == 1)
using namespace CPCAPI2::ConferenceBridge;
#endif

using namespace CPCAPI2::test;

using namespace ::testing;

cpc::string TestEnvironmentConfig::mLogFileName = "";
cpc::string TestEnvironmentConfig::mCrashDumpFileName = "";
cpc::string TestEnvironmentConfig::mTestEnvironmentId = "";
PhoneShutdownInfo gShutdownInfo;
std::vector<std::string> gCommandLineArgs;

int32_t gNextRTPPort = 7000;
int32_t gNextRTPAudioPort = 17000;
int32_t gNextRTPVideoPort = 27000;


CpcapiAutoTestBase::CpcapiAutoTestBase() :
fdCheckEnabled(false)
{
}

CpcapiAutoTestBase::~CpcapiAutoTestBase()
{
}

void CpcapiAutoTestBase::start()
{
   // CPCAPI2::Utils::FileDescriptorMonitor::logOpenFileDescriptors("CpcapiAutoTestBase::start()");
   fd.reset(new CPCAPI2::Utils::FileDescriptorsCheckpoint);
}

void CpcapiAutoTestBase::end()
{
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   if (fd)
   {
      if (fdCheckEnabled)
      {
         ASSERT_TRUE(checkFdLeaks(*fd)) << " File Descriptor Leak !!!";
      }
      else
      {
         checkFdLeaks(*fd);
      }
   }
   // CPCAPI2::Utils::FileDescriptorMonitor::logOpenFileDescriptors("CpcapiAutoTestBase::end()");
}

bool CpcapiAutoTestBase::checkFdLeaks(CPCAPI2::Utils::FileDescriptorsCheckpoint& check)
{
   std::vector<std::string> skipItems;
   skipItems.push_back("counterpath.CPCAPI2AutoTestsX.savedState");  // ignore unit test state files
   //      skipItems.push_back("/dev/random");                               // ignore these?
   skipItems.push_back("/dev/urandom"); // OpenSSL appears to deallocate this on process exit (at global static destruction time)

   if (check.newCheckpoint("Process about to exit", skipItems))
   {
      // DRL We still have left over file descriptors so we can't fail here yet.
      //         FAIL() << "File descriptor leak!";
      if (!fdCheckEnabled)
      {
         safeCout("CpcapiAutoTestBase::checkFdLeaks(): File Descriptor Leak !!!" << std::endl << std::endl);
      }
      return false;
   }

   // safeCout("CpcapiAutoTestBase::checkFdLeaks(): No file descriptor leaks" << std::endl << std::endl);
   return true;
}

bool TestEnvironmentConfig::pauseAfterTests()
{
   const char* noPause = getenv("CPCAPI2_NO_PAUSE");
   return noPause == NULL;
}

bool TestEnvironmentConfig::includeAudioQualityTests()
{
   const char* includeAudioQualityTests = getenv("CPCAPI2_INCLUDE_AUDIO_QUALITY_TESTS");
   return includeAudioQualityTests != NULL;
}

bool TestEnvironmentConfig::isCpInternalNetwork()
{
   const char* internal = getenv("CPCAPI2_INTERNAL_NETWORK");
   return internal != NULL;
}

bool TestEnvironmentConfig::logToConsole()
{
   if (const char* logToConsole = getenv("CPCAPI2_LOG_TO_CONSOLE"))
   {
      return strcmp(logToConsole, "1") == 0;
   }

   return true; // default as true
}

bool TestEnvironmentConfig::saveLoggingToFile()
{
   const char* saveToFile = getenv("CPCAPI2_SAVE_LOGGING_TO_FILE");
   return saveToFile != NULL;
}
//if gflags pageheap enabled
bool TestEnvironmentConfig::pageHeap()
{
	const char* saveToFile = getenv("CPCAPI2_PAGEHEAP");
	if (saveToFile != NULL) 
   {
		if (strcmp(saveToFile, "1") == 0)
			return true;
		else // if (strcmp(saveToFile, "0") == 0)
			return false;
	}
	else 
		return false;
}

bool TestEnvironmentConfig::redirectCoutToFile()
{
   const char* redirect = getenv("CPCAPI2_REDIRECT_COUT_TO_FILE");
   return redirect != NULL;
}

bool TestEnvironmentConfig::saveCrashdumpsToFile()
{
   const char* saveToFile = getenv("CPCAPI2_SAVE_CRASH_DUMPS_TO_FILE");
   return saveToFile != NULL;
}

bool TestEnvironmentConfig::fullMemoryCrashdumps()
{
   const char* opt = getenv("CPCAPI2_FULL_MEMORY_CRASH_DUMPS");
   if (opt != NULL)
   {
      if (strcmp(opt, "1") == 0)
      {
         return true;
      }
   }
   return false;
}

bool TestEnvironmentConfig::testTimerWatchdog()
{
   const char* timerWatchdog = getenv("CPCAPI2_TEST_TIMER_WATCHDOG");
   return timerWatchdog != NULL;
}

bool TestEnvironmentConfig::modifyPowerSchemeAsNeeded()
{
   const char* modifyPowerScheme = getenv("CPCAPI2_MODIFY_POWER_SCHEME_AS_NEEDED");
   return modifyPowerScheme != NULL;
}

bool TestEnvironmentConfig::drawLocalVideo()
{
   bool drawLocalVideo = true;
   const char* noDrawLocalVideo = getenv("CPCAPI2_NO_DRAW_LOCAL_VIDEO");
   if (noDrawLocalVideo == NULL)
   {
      drawLocalVideo = true;
   }
   else if (strcmp(noDrawLocalVideo, "0") == 0)
   {
      drawLocalVideo = true;
   }
   else if (strcmp(noDrawLocalVideo, "1") == 0)
   {
      drawLocalVideo = false;
   }

   return drawLocalVideo;
}

void TestEnvironmentConfig::setDrawLocalVideo(bool draw)
{
#ifdef _WIN32
   _putenv_s("CPCAPI2_NO_DRAW_LOCAL_VIDEO", !draw ? "1" : "0");
#else
   setenv("CPCAPI2_NO_DRAW_LOCAL_VIDEO", !draw ? "1" : "0", 1);
#endif
}

bool TestEnvironmentConfig::forceDummyAudioDevice()
{
   const char* forceDummyAudioDevice = getenv("CPCAPI2_FORCE_DUMMY_AUDIO_DEVICE");
   return forceDummyAudioDevice != NULL;
}

VideoCaptureResolution TestEnvironmentConfig::defaultVideoRes()
{
#ifdef __linux__
   // try to reduce load with so many tests running at once with docker
   return CPCAPI2::Media::VideoCaptureResolution_Standard;
#else
   return CPCAPI2::Media::VideoCaptureResolution_MaxSupported;
#endif
}

bool TestEnvironmentConfig::useRandomXmppUsernames()
{
   const char* useRandom = getenv("CPCAPI2_USE_RANDOM_XMPP_USERNAMES");
   return useRandom != NULL;
}

bool TestEnvironmentConfig::autoCreateXmppUsers()
{
   const char* autoCreateXmppUsers = getenv("CPCAPI2_AUTO_CREATE_XMPP_USERS");
   if (autoCreateXmppUsers != NULL) 
   {
      if (strcmp(autoCreateXmppUsers, "1") == 0)
         return true;
      else // if (strcmp(autoCreateXmppUsers, "0") == 0)
         return false;
   }
   else
      return true;
}

bool TestEnvironmentConfig::disableXmppSelfPresenceCheck()
{
   const char* disableXmppSelfPresenceCheck = getenv("CPCAPI2_DISABLE_XMPP_SELF_PRESENCE_CHECK");
   if (disableXmppSelfPresenceCheck != NULL)
   {
      if (strcmp(disableXmppSelfPresenceCheck, "1") == 0)
         return true;
      else // if (strcmp(disableXmppSelfPresenceCheck, "0") == 0)
         return false;
   }
   else
      return true;
}

bool TestEnvironmentConfig::disableXmppCompression()
{
   const char* env = getenv("CPCAPI2_DISABLE_XMPP_COMPRESSION");
   return env != NULL;
}

bool TestEnvironmentConfig::logXmppTlsEncryptionKey()
{
   const char* env = getenv("CPCAPI2_LOG_XMPP_TLS_ENCRYPTION_KEY");
   return env != NULL;
}

bool TestEnvironmentConfig::disableDtlsByDefault()
{
   const char* env = getenv("CPCAPI2_DISABLE_DTLS_BY_DEFAULT");
   return env != NULL;
}

bool TestEnvironmentConfig::dockerContainerized()
{
   const char* env = getenv("CPCAPI2_DOCKER_CONTAINERIZED");
   return env != NULL;
}

bool TestEnvironmentConfig::macLoopbackAliasesSet()
{
   const char* env = getenv("CPCAPI2_MAC_LOOPBACKALIASES_SET");
   return env != NULL;
}

void TestEnvironmentConfig::setLogFileName(const cpc::string& logFileName)
{
   mLogFileName = logFileName;
}

cpc::string TestEnvironmentConfig::logFileName()
{
   return mLogFileName;
}

cpc::string TestEnvironmentConfig::loggingFilePath()
{
   const char* logFilePath = getenv("CPCAPI2_LOGGING_FILE_PATH");
   if (logFilePath)
   {
      return logFilePath;
   }
   else
   {
      return "";
   }
}

std::filesystem::path TestEnvironmentConfig::artifactFilePath()
{
   const char* path = getenv("CPCAPI2_ARTIFACT_FILE_PATH");
   if (path)
   {
      return std::filesystem::path(path);
   }
   else
   {
      return std::filesystem::current_path() / "runtime_artifacts";
   }
}

void TestEnvironmentConfig::setCrashDumpFileName(const cpc::string& crashDumpFileName)
{
   mCrashDumpFileName = crashDumpFileName;
}

cpc::string TestEnvironmentConfig::crashDumpFileName()
{
   return mCrashDumpFileName;
}

cpc::string TestEnvironmentConfig::openfireRestApiKey()
{
   const char* openfireRESTAPIKey = getenv("CPCAPI2_OPENFIRE_RESTAPI_KEY");
   if (openfireRESTAPIKey)
   {
      return openfireRESTAPIKey;
   }
   else
   {
      return "sdkrestapitestkey";
   }
}

cpc::string TestAccountConfig::defaultDnsServer(IpVersion ipversion)
{
   const char* dnsServer =
      (ipversion == IpVersion_V6) ?
      getenv("CPCAPI2_DNS_SERVER_IPV6") : getenv("CPCAPI2_DNS_SERVER");
   if (dnsServer)
   {
      return dnsServer;
   }
   else
   {
      return ((ipversion == IpVersion_V6) ? "::1" : "127.0.0.1");
   }
}

bool TestEnvironmentConfig::getResourceFile(const std::string configFileName, std::string& configFile)
{
   resip::Data completeFilename = (TestEnvironmentConfig::testResourcePath() + configFileName.c_str()).c_str();
   configFile = "";
   struct stat status;
   bool exists = (stat(completeFilename.c_str(), &status) == 0);
   if (exists)
   {
      configFile = completeFilename.c_str();
      safeCout("TestEnvironmentConfig::getResourceFile(): using runtime resource file: " << configFile);
   }
   else
   {
      exists = (stat(configFileName.c_str(), &status) == 0);
      if (exists)
      {
         configFile = configFileName;
         safeCout("TestEnvironmentConfig::getResourceFile(): using resource file: " << configFile);
      }
      else
      {
         safeCout("TestEnvironmentConfig::getResourceFile(): could not find resource file: " << configFileName);
      }
   }
   return exists;
}

cpc::string TestEnvironmentConfig::testResourcePath()
{
   const char* p = getenv("CPCAPI2_RESOURCE_PATH");
   if (p == NULL)
   {
      return "./runtime_resources/";
   }
   return p;
}

cpc::string TestEnvironmentConfig::tempPath()
{
   const char* p = getenv("CPCAPI2_TEMP_PATH");
   if (p == NULL)
   {
      return ".";
   }
   return p;
}

cpc::string TestEnvironmentConfig::defaultXmppServer()
{
   const char* xmppServer = getenv("CPCAPI2_XMPP_SERVER");
   if (xmppServer)
   {
      return xmppServer;
   }

   // return "192.168.1.249";
   return "yvr-sdkxmpp01.cp.local";
}

cpc::string TestEnvironmentConfig::defaultXmppServerProxy()
{
   const char* xmppServerProxy = getenv("CPCAPI2_XMPP_SERVER_PROXY");
   if (xmppServerProxy)
   {
      return xmppServerProxy;
   }

   return "";
}

cpc::string TestEnvironmentConfig::defaultXcapServer()
{
   const char* xcapServer = getenv("CPCAPI2_XCAP_SERVER");
   if (xcapServer)
   {
      return xcapServer;
   }

   return "socrates.cp.local";
}

cpc::string TestEnvironmentConfig::unreachableV4Ip()
{
#ifndef __APPLE__
   return "*********"; // should not be routable -- RFC 5737
#else
   // on mac, ********* results in immediate failure whereas *********** times out (what we want).
   // ********* was previously used here, but we have since started bringing this up via ifconfig alias
   // for PTT tests.
   return "***********";
#endif
}

cpc::string TestEnvironmentConfig::unreachableV6Ip()
{
   // we want an address that doesn't result in immediate failure on socket open / socket send
   // .jza. this seems to often return no route to host (not what we want);
   // this behaviour could change depending on OS behaviour / config
   return "100::";
}

cpc::string TestEnvironmentConfig::pcapCaptureTestCases()
{
   const char* env = getenv("CPCAPI2_PCAP_CAPTURE_TEST_CASES");
   return env ? env : "";
}

cpc::string TestEnvironmentConfig::logcatServerIp()
{
   const char* env = getenv("CPCAPI2_LOGCAT_SERVER_IP");
   return env ? env : "";
}

unsigned int TestEnvironmentConfig::logcatServerPort()
{
   const char* env = getenv("CPCAPI2_LOGCAT_SERVER_PORT");
   return env ? atoi(env) : 0;
}

cpc::string TestEnvironmentConfig::expectedAutoTestsVersion()
{
   const char* env = getenv("CPCAPI2_EXPECTED_AUTO_TESTS_VERSION");
   return env ? env : "";
}


const std::vector<std::string>& TestEnvironmentConfig::commandLineArgs()
{
   return gCommandLineArgs;
}

void TestEnvironmentConfig::setCommandLineArgs(const std::vector<std::string>& args)
{
   gCommandLineArgs = args;
}

cpc::string TestEnvironmentConfig::testEnvironmentId()
{
   if (mTestEnvironmentId == "")
   {
      const char* environmentId = getenv("CPCAPI2_SIPUA_TEST_ENVIRONMENT_ID");
      if (environmentId)
      {
         safeCout("TestEnvironmentConfig::testEnvironmentId(): initialize from environment: " << environmentId);
         mTestEnvironmentId = environmentId;
      }
      else
      {
         safeCout("TestEnvironmentConfig::testEnvironmentId(): initialize to default repro");
         mTestEnvironmentId = "repro";
      }
   }

   // safeCout("TestEnvironmentConfig::testEnvironmentId(): " << mTestEnvironmentId.c_str());
   return mTestEnvironmentId;
}

TestEnvironmentConfig::CpeTestLevel TestEnvironmentConfig::cpeTestLevel()
{
   const char* env = getenv("CPCAPI2_CPE_TEST_LEVEL");
   if (env)
   {
      std::string level(env);
      boost::algorithm::to_lower(level);

      if (level == "exhaustive")
      {
         return CpeTestLevel::Exhaustive;
      }
      else if (level == "standard")
      {
         return CpeTestLevel::Standard;
      }
      else if (level == "production")
      {
         return CpeTestLevel::Production;
      }
   }

   return CpeTestLevel::Production;
}

TestAccountConfig::TestAccountConfig(const std::string& n) :
alianzaSession(n)
{
   license = "";
   name = (n.c_str());
   registerHandlers = true;

   if (!alianzaConfig.importConfig())
   {
      safeCout("TestAccountConfig(): error importing configuration");
   }

   alianzaSession.init(alianzaConfig);

   if (alianzaConfig.sipTransportType.compare("TCP") == 0)
   {
      settings.sipTransportType = CPCAPI2::SipAccount::SipAccountTransport_TCP;
   }
   else if (alianzaConfig.sipTransportType.compare("TLS") == 0)
   {
      settings.sipTransportType = CPCAPI2::SipAccount::SipAccountTransport_TLS;
   }
   else
   {
      settings.sipTransportType = CPCAPI2::SipAccount::SipAccountTransport_UDP;
   }
   settings.userAgent = "SDK Auto Test Framework";
   settings.displayName = alianzaSession.getUa()->name.c_str();
   settings.domain = alianzaConfig.sipDomain.c_str();
   settings.outboundProxy = alianzaConfig.sipOutboundProxy.c_str();
   settings.username = alianzaSession.getUa()->sipUsername.c_str();
   settings.password = alianzaSession.getUa()->sipPassword.c_str();

   settings.useRegistrar = true;
   settings.useRport = false;
   //settings.useOutbound = true;
   settings.registrationIntervalSeconds = 45;

   //settings.domain = "127.0.0.1";
   //settings.sipTransportType = SipAccountTransport_TCP;
   //settings.alwaysRouteViaOutboundProxy = false;

   xcapSettings.domain = TestEnvironmentConfig::defaultXcapServer();
   xcapSettings.password = "Ch@ngeMe";
   xcapSettings.username = "milos";
   xcapSettings.xcapRoot = "xcap-root";
   xcapSettings.port = "6060";
   disableAnalyticsModuleAutoTestEventHandling = false;
   disableStrettoUemModuleAutoTestEventHandling = true;
   useFileAudioDevice = false;
   useAAudio = false;

   if (TestEnvironmentConfig::disableDtlsByDefault())
   {
      // initializeDtlsFactory can take several seconds to initialize
      // when running the linux auto tests in parallel, throwing off
      // test timing
      dtlsSupported = false;
   }
   else
   {
      dtlsSupported = true;
   }

   cpc::string randId = (resip::Random::getCryptoRandomHex(6).c_str());
   settings.localGroup= "Local-Group-" + randId;
}

cpc::string TestAccountConfig::uri()
{
   return "sip:" + settings.username + "@" + settings.domain;
}

TestAccountConfig
TestAccountConfig::makeSecureConfig(const std::string& name)
{
   TestAccountConfig config(name);
   config.settings.domain = "autotest.cpcapi2";
   config.settings.outboundProxy = "";
   config.settings.nameServers.push_back(defaultDnsServer());
   config.settings.sipTransportType = SipAccountTransport_TLS;
   config.settings.ignoreCertVerification = true;
   return config;
}

SecureTestAccount::SecureTestAccount(const std::string& name, TestAccountInitMode initMode, bool disableOnDestruct, CPCAPI2::Phone* p)
      : TestAccount(name, Account_NoInit, disableOnDestruct, p)
{
   config = TestAccountConfig::makeSecureConfig(name);
   switch(initMode)
   {
      case Account_Enable:
         enable();
         break;
      case Account_Init:
         init();
         break;
      case Account_NoInit:
         break;
   }
}

SecureTestAccount::~SecureTestAccount()
{
}

SingleOwnable TestAccount::sSystemAudioOwner;
SingleOwnable TestAccount::sSystemCameraOwner;

TestAccount::TestAccount(const std::string& name, TestAccountInitMode initMode, bool disableOnDestruct, CPCAPI2::Phone* p, CPCAPI2::Media::MediaTransportsReactorFactory* mediaReactorFactory, bool initSlave, bool useVideoHelper)
:
TestAccountBase(TestAccountType_SIP),
config(name),
initialized(false),
enabled(false),
shouldDisableOnDestruct(disableOnDestruct),
phone(p), mediaReactorFac(mediaReactorFactory),
slave(p!=NULL),
shutdownTimedout(false),
phoneReleased(false),
initSlave(initSlave),
mOwnsSystemAudio(false),
mOwnsSystemCamera(false),
mUsingCustomVideoSource(false),
mAndroidBackgroundingEnabled(false),
mUseVideoHelper(useVideoHelper),
alianzaApiAccountHandle(0)
{
   gShutdownInfo.cv.reset(new std::condition_variable());
   gShutdownInfo.mutex.reset(new std::mutex());
   gShutdownInfo.shutdown = false;

   recordingEvents = NULL;
   callQualityReport = NULL;
   callQualityEvents = NULL;

   switch(initMode)
   {
      case Account_Enable:
         enable(true);
         break;
      case Account_Init:
         init();
         break;
      case Account_NoInit:
         break;
   }
}

static AutoTestsLogger* gInstance;
static std::once_flag gInstanceFlag;

AutoTestsLogger& AutoTestsLogger::instance()
{
   std::call_once(gInstanceFlag, &AutoTestsLogger::initInstance);
   return *gInstance;
}

void AutoTestsLogger::initInstance()
{
   gInstance = new AutoTestsLogger();
}

AutoTestsLogger::AutoTestsLogger() : mBadLogMessageCount(0)
{
   std::lock_guard<std::mutex> lock(mMutex);
   if (TestEnvironmentConfig::saveLoggingToFile())
   {
      if (mLogFile.is_open())
      {
         return;
      }

#ifdef _WIN32
      const std::string delim = "\\";
#else
      const std::string delim = "/";
#endif
      std::stringstream logFilePath;
      if (TestEnvironmentConfig::loggingFilePath().empty()){
         logFilePath << TestEnvironmentConfig::loggingFilePath().c_str() << TestEnvironmentConfig::logFileName().c_str();
      }
      else{
         logFilePath << TestEnvironmentConfig::loggingFilePath().c_str() << delim << TestEnvironmentConfig::logFileName().c_str();
      }
      mLogFile.open(logFilePath.str(), std::ios::out);
      if (mLogFile.is_open())
      {
         safeCout("log file open successfully");
      }
      else
      {
         safeCout("log file open failed. Path was " << logFilePath.str());
      }
   }
}
AutoTestsLogger::~AutoTestsLogger() {}


AutoTestsLocalLogger::AutoTestsLocalLogger(const std::string& name) : mName(name)
{
}

// LocalLoggerHandler
void AutoTestsLocalLogger::onLogMessage(LocalLogMessage& msg)
{
   std::stringstream logStr;
   logStr << "LOCALLOG ((" << mName << ")) " << msg.getMessage();
   
   if (!TestEnvironmentConfig::saveLoggingToFile())
   {
      safeCout(logStr.str());
   }
}


template <typename Duration>
std::string get_time(tm t, Duration fraction)
{
   using namespace std;
   using namespace std::chrono;

   system_clock::time_point now = system_clock::now();
   system_clock::duration tp = now.time_since_epoch();

   tp -= duration_cast<seconds>(tp);

   using namespace std::chrono;
   char buf[512];
   std::snprintf(buf, 512, "[%04u-%02u-%02u %02u:%02u:%02u.%03u]", t.tm_year + 1900,
             t.tm_mon + 1, t.tm_mday, t.tm_hour, t.tm_min, t.tm_sec,
             static_cast<unsigned>(fraction / milliseconds(1)));

   return buf;

    // VS2013's library has a bug which may require you to replace
    // "fraction / milliseconds(1)" with
    // "duration_cast<milliseconds>(fraction).count()"
}

void AutoTestsLogger::setBadLogMessageCheckFunction(const std::function<bool(const char *message, CPCAPI2::LogLevel level)>& externalBadLogMessageCheckFunction)
{
   mExternalBadLogMessageCheckFn = externalBadLogMessageCheckFunction;
   mBadLogMessageCount = 0;
}

void AutoTestsLogger::setLogMessageListenerFunction(const std::function<void(const char *message, const char* subsystem, CPCAPI2::LogLevel level)>& logMessageListenerFunction)
{
   mExternalListenerLogMessageCheckFn = logMessageListenerFunction;
}

bool AutoTestsLogger::operator()(CPCAPI2::LogLevel level, const char *subsystem, const char *appName, const char *file,
                        int line, const char *message, const char *messageWithHeaders)
{
   if (resip::isEqualNoCase("cpmhelper.cpp", file) || resip::isEqualNoCase("sipfiletransfermanagerimpl.cpp", file))
   {
      return true;
   }
   resip::Data logstr;
   {
      resip::DataStream ds(logstr);
      std::stringstream date;

      using namespace std;
      using namespace std::chrono;

      system_clock::time_point now = system_clock::now();
      system_clock::duration tp = now.time_since_epoch();

      tp -= duration_cast<seconds>(tp);

      time_t tt = system_clock::to_time_t(now);

      #if !defined(LINUX)
      date << get_time(*localtime(&tt), tp);
      #endif

      std::string levelStr;
      switch (level)
      {
         case LogLevel_None:
            levelStr = "None";
            break;
         case LogLevel_Error:
            levelStr = "Error";
            break;
         case LogLevel_Warning:
            levelStr = "Warning";
            break;
         case LogLevel_Info:
            levelStr = "Info";
            break;
         case LogLevel_Debug:
            levelStr = "Debug";
            break;
         case LogLevel_Max:
            levelStr = "Max";
            break;
      }

      ds << date.str() << "(" << levelStr << ")(" << file << ":" << line << ") " << message << std::endl;
   }
   if (resip::isEqualNoCase("sipavconversationmanagerimpl.cpp", file) && line == 333)
   {
      eventDebug(logstr);
   }

   if (TestEnvironmentConfig::logToConsole())
   {
#if WIN32
      OutputDebugStringA(logstr.c_str());
#else
      safeCout(logstr.c_str());
#endif
   }

   if (TestEnvironmentConfig::saveLoggingToFile())
   {
      std::lock_guard<std::mutex> lock(mMutex);

      mLogFile << logstr;
   }

   if (mExternalBadLogMessageCheckFn)
   {
      mBadLogMessageCount += mExternalBadLogMessageCheckFn(message, level) ? 1 : 0;
   }

   if (mExternalListenerLogMessageCheckFn)
   {
      mExternalListenerLogMessageCheckFn(message, subsystem, level);
   }

   return true;
}

int AutoTestsLogger::getBadMessagesCount()
{
   return mBadLogMessageCount;
}

static void phoneShutdown(void* context)
{
   std::unique_lock<std::mutex> lock(*(gShutdownInfo.mutex.get()));
   gShutdownInfo.shutdown = true;
   gShutdownInfo.cv->notify_all();
}

void TestAccount::createAlianzaAccountInBackend()
{
   if (!initialized)
   {
      safeCout("TestAccount::createAlianzaAccountInBackend(): account not initialized");
      return;
   }

   if (config.alianzaSession.extensionEnabled)
   {
      safeCout("TestAccount::createAlianzaAccountInBackend(): account is for extensions only");
      return;
   }

   if (alianzaApiAccountHandle != 0)
   {
      safeCout("TestAccount::createAlianzaAccountInBackend(): account already created");
      return;
   }

   config.alianzaConfig.api.debugContext = "TestAccount(name=" + config.name + ")";

   alianzaApiManager->start(config.alianzaConfig.api);

   alianzaApiManager->authorize();
   assertAuthorizationSuccess(alianzaApiEvents);

   alianzaApiAccountHandle = alianzaApiManager->createAccount(this);
   alianzaApiManager->enableAccount(alianzaApiAccountHandle);
   if (config.alianzaSession.numberEnabled)
   {
      assertAlianzaApiAccountStatusEvent(*this, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_QueryNumber);
      assertAlianzaApiAccountStatusEvent(*this, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_ReserveNumber);
      
      // a number we fail to reserve might result in the state machine going back to query another number and attempt to reserve again
      std::set<AlianzaApiAccountFsmStateType> allowedPrecedingEvents;
      allowedPrecedingEvents.insert(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_QueryNumber);
      allowedPrecedingEvents.insert(AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_ReserveNumber);
      assertAlianzaApiAccountStatusEventAllowPrecedingEvents(*this, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_CreateAccount, allowedPrecedingEvents);
      
      assertAlianzaApiAccountStatusEvent(*this, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_SetGroupName);
      assertAlianzaApiAccountStatusEvent(*this, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_AddUser);
      assertAlianzaApiAccountStatusEvent(*this, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_UserAuth);
      assertAlianzaApiAccountStatusEvent(*this, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_QueryClientConfig);
      assertAlianzaApiAccountStatusEvent(*this, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_AddNumber);
      assertAlianzaApiAccountStatusEvent(*this, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_CheckNumberInAccount);
      assertAlianzaApiAccountStatusEvent(*this, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_UpdateCallerId);
      assertAlianzaApiAccountStatusEvent(*this, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_UpdateUser);
      assertAlianzaApiAccountStatusEvent(*this, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Enabled);
   }
   else
   {
      assertAlianzaApiAccountStatusEvent(*this, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_CreateAccount);
      assertAlianzaApiAccountStatusEvent(*this, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_SetGroupName);
      std::vector<std::string> extensions;
      config.alianzaSession.getExtensions(extensions);
      for (auto i = 0; i != extensions.size(); ++i)
      {
         assertAlianzaApiAccountStatusEvent(*this, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_AddUser);
         assertAlianzaApiAccountStatusEvent(*this, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_UserAuth);
         assertAlianzaApiAccountStatusEvent(*this, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_QueryClientConfig);
      }
      assertAlianzaApiAccountStatusEvent(*this, AlianzaApiAccountFsmStateType::AlianzaApiAccountFsmStateType_Enabled);
   }
}

void TestAccount::destroyAlianzaAccountInBackend()
{
   if (!initialized)
   {
      safeCout("TestAccount::destroyAlianzaAccountInBackend(): account not initialized");
      return;
   }

   if (config.alianzaSession.extensionEnabled)
   {
      safeCout("TestAccount::destroyAlianzaAccountInBackend(): account is for extensions only");
      return;
   }

   if (alianzaApiAccountHandle == 0)
   {
      safeCout("TestAccount::destroyAlianzaAccountInBackend(): account not created");
   }
   else
   {
      alianzaApiManager->disableAccount(alianzaApiAccountHandle);
      assertAlianzaApiAccountStatusUptilDisabledEvent(*this);
      /*
      assertAlianzaApiAccountStatusEvent(*this, AlianzaApiAccountFsmStateType_RemoveNumber);
      assertAlianzaApiAccountStatusEvent(*this, AlianzaApiAccountFsmStateType_DeleteAccount);
      assertAlianzaApiAccountStatusEvent(*this, AlianzaApiAccountFsmStateType_ReleaseNumber);
      assertAlianzaApiAccountStatusEvent(*this, AlianzaApiAccountFsmStateType_Idle);
      */
      alianzaApiAccountHandle = 0;
   }
}

void TestAccount::applyAlianzaExtensionUa(const AlianzaSipUaInfo& ua, const AlianzaSessionInfo& sessionInfo)
{
   config.settings.displayName = ua.name.c_str();
   config.settings.username = ua.sipUsername.c_str();
   config.settings.password = ua.sipPassword.c_str();
   config.settings.domain = ua.sipDomain.c_str();
   config.alianzaSession.resetUa(ua);
   config.alianzaSession.extensionEnabled = true;
   config.alianzaSession.accountId = sessionInfo.accountId;
   config.alianzaSession.accountNumber = sessionInfo.accountNumber;
}

void TestAccount::init(PhoneHandler* phoneHandler, NetworkTransport transport)
{
   if (initialized)
   {
      return;
   }
   initialized = true;

   if (mUseVideoHelper)
   {
      videoHelper.reset(new CPCAPI2::test::TestVideoHelper(config.name.c_str()));
   }

   if (!slave)
   {
      phone = Phone::create();
      LicenseInfo licenseInfo;
      licenseInfo.licenseKey = config.license.c_str();
      licenseInfo.licenseDocumentLocation = config.licenseDocumentLocation.c_str();
      licenseInfo.licenseAor = config.licenseAor.c_str();

      if (nullptr == phoneHandler)
      {
        phoneHandler = (PhoneHandler*)0xDEADBEEF;
      }
      dynamic_cast<PhoneInternal*>(phone)->initialize(licenseInfo, phoneHandler, config.phoneInitConnectionPrefs, transport);
   }
   if (!slave || initSlave)
   {
      static_cast<PhoneInternal*>(phone)->setPhoneName(config.name);
      dynamic_cast<PhoneInternal*>(phone)->setCallOnDestructFn(onPhoneRelease, this);

      // AutoTestsLogger can log to either the console, or a file
      phone->setLoggingEnabled(&AutoTestsLogger::instance(), true);

      // true 'per phone' SDK logging
      mLocalLogger.reset(new AutoTestsLocalLogger(this->config.name.c_str()));
      static_cast<PhoneInternal*>(phone)->setLocalCallbackLoggingEnabled(mLocalLogger.get(), true);
   }

   alianzaApiManager = CPCAPI2::test::AlianzaApiManager::getInterface(this);
   alianzaApiEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(alianzaApiManager.get()));
   alianzaApiManager->setHandler((AlianzaApiHandler*)0xDEADBEEF);

#if (CPCAPI2_BRAND_ANALYTICS_MODULE == 1)
   analyticsManager = AnalyticsManager::getInterface(phone);
   if (config.disableAnalyticsModuleAutoTestEventHandling)
   {
      analyticsEvents = NULL;
   }
   else
   {
      analyticsEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(analyticsManager));
      analyticsManager->setHandler((AnalyticsHandler *)0xDEADBEEF);
      AnalyticsSettings serverSettings;
      Analytics::GeneralStats generalStats;
      analyticsHandle = analyticsManager->open(serverSettings, generalStats);
   }
#endif

#if (CPCAPI2_BRAND_STRETTO_UEM_MODULE == 1)
   strettoUemManager = StrettoUemManager::getInterface(phone);
   if (config.disableStrettoUemModuleAutoTestEventHandling)
   {
      strettoUemEvents = NULL;
   }
   else
   {
      strettoUemEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(strettoUemManager));
      strettoUemManager->setHandler((StrettoUemHandler *)0xDEADBEEF);
   }
#endif

   phoneEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(phone));

   account = SipAccountManager::getInterface(phone);
   accountEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(account));
   handle = account->create();
   account->configureDefaultAccountSettings(handle, config.settings);
   account->applySettings(handle);

   safeCout("TestAccount::init(): account: " << handle << " user: " << config.name);

   presence = SipPresenceManager::getInterface(phone);
   presenceEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(presence));
   subs = SipEventManager::getInterface(phone);
   subsEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(subs));
#if (CPCAPI2_BRAND_SIP_FILE_TRANSFER_MODULE == 1)
   fileTransferManager = SipFileTransferManager::getInterface(phone);
   fileTransferEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(fileTransferManager));
#endif
   conversation = SipConversationManager::getInterface(phone);
   conversationEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(conversation));

   SipConversationSettings conversationSettings;
   conversationSettings.minRtpPort = gNextRTPPort;
   conversationSettings.maxRtpPort = gNextRTPPort + 9;
   conversationSettings.minRtpPortAudio = gNextRTPAudioPort;
   conversationSettings.maxRtpPortAudio = gNextRTPAudioPort + 9;
   conversationSettings.minRtpPortVideo = gNextRTPVideoPort;
   conversationSettings.maxRtpPortVideo = gNextRTPVideoPort + 9;

   gNextRTPPort += 10;
   gNextRTPAudioPort += 10;
   gNextRTPVideoPort += 10;

   if (gNextRTPPort == 8000)
   {
      gNextRTPPort = 7000;
      gNextRTPAudioPort = 17000;
      gNextRTPVideoPort = 27000;
   }
   conversation->setDefaultSettings(handle, conversationSettings);

   conversationState = SipConversationStateManager::getInterface(conversation);
   conversationStateEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(conversationState));
   mwi = SipMessageWaitingIndicationManager::getInterface(phone);
   mwiEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(mwi));

   if (mediaReactorFac == NULL)
   {
      media = MediaManager::getInterface(phone);
   }
   else
   {
      media = dynamic_cast<MediaManager*>(MediaManagerInternal::getInterface(phone, mediaReactorFac));
   }
   
   mediaEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(media));

   if (!slave || initSlave)
   {
      if (config.useFileAudioDevice)
      {
         AudioExt* audioExt = AudioExt::getInterface(media);
         audioExt->setAudioDeviceFile(TestEnvironmentConfig::testResourcePath() + "silence16.pcm", "");
         config.mediaSettings.audioLayer = CPCAPI2::Media::AudioLayers_File;
      }
      else
      {
         if (TestEnvironmentConfig::forceDummyAudioDevice())
         {
            config.mediaSettings.audioLayer = CPCAPI2::Media::AudioLayers_Dummy;
         }
         else
         {
#if defined(ANDROID)
            // Android does not support having multiple instances of AudioRecord,
            // so only let one TestAccount at a time use the real audio backend
            if (TestAccount::sSystemAudioOwner.tryOwn())
            {
               mOwnsSystemAudio = true;
               if (config.useAAudio)
                  config.mediaSettings.audioLayer = CPCAPI2::Media::AudioLayers_AAudio;
               else
                  config.mediaSettings.audioLayer = CPCAPI2::Media::AudioLayers_PlatformDefault;
            }
            else
            {
               mOwnsSystemAudio = false;
               config.mediaSettings.audioLayer = CPCAPI2::Media::AudioLayers_Dummy;
            }
#endif
         }
      }

      media->initializeMediaStack(config.mediaSettings);
   }
   audio = Audio::getInterface(media);
   audio->setHandler((AudioHandler*) 0xDEADBEEF);
   
   video = Video::getInterface(media);
   video->setHandler((VideoHandler*) 0xDEADBEEF);
   
   mwi = SipMessageWaitingIndicationManager::getInterface(phone);
   im = SipInstantMessageManager::getInterface(phone);
   imEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(im));
   recording = Recording::RecordingManager::getInterface(phone);
   recordingEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(recording));
#if (CPCAPI2_BRAND_CALL_QUALITY_REPORT_MODULE == 1)
   // call createCallQualityReporter() to init
   callQualityReport = NULL;
#endif
#if (CPCAPI2_BRAND_NETWORK_CHANGE_MODULE == 1)
   network = dynamic_cast<NetworkChangeManagerInterface*>(NetworkChangeManager::getInterface(phone))->getMockImpl();
   networkChangePublic = NetworkChangeManager::getInterface(phone);
   networkChangeHandle = networkChangePublic->create();
   networkChangeEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(networkChangePublic));
   
#endif
#if (CPCAPI2_BRAND_SIP_CHAT_MODULE == 1)
   chatManager = SipChatManager::getInterface(phone);
   chatEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(chatManager));
#endif
#if (CPCAPI2_BRAND_SIP_STANDALONE_MESSAGING_MODULE == 1)
   standaloneMessagingManager = SipStandaloneMessagingManager::getInterface(phone);
   standaloneMessagingEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(standaloneMessagingManager));
#endif
#if (CPCAPI2_BRAND_CAPABILITY_DISCOVERY_MODULE == 1)
   capability = RcsCapabilityDiscoveryManager::getInterface(phone);
   capabilityEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(capability));
#endif
#if (CPCAPI2_BRAND_PEER_CONNECTION_MODULE == 1)
   peerConnection = PeerConnectionManager::getInterface(phone);
   peerConnEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(peerConnection));
#endif
#if (CPCAPI2_BRAND_WEB_CALL_MODULE == 1)
   webCall = WebCallManager::getInterface(phone);
   webCallEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(webCall));
#endif
#if (CPCAPI2_BRAND_PTT_MODULE == 1)
   ptt = PushToTalkManager::getInterface(phone);
   pttEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(ptt));
#endif
#ifdef CPCAPI2_GENBAND_MODULE
   genbandManager = GenbandRestAPIInterface::getInterface(phone);
#endif
#if (CPCAPI2_BRAND_GENBAND_SOPI_MODULE == 1)
   genbandSopiManager = GenbandSopiManager::getInterface(phone);
#endif
#if (CPCAPI2_BRAND_RESOURCE_LIST_MODULE == 1)
   xcapResourceListManager = XcapResourceListManager::getInterface(phone);
   xcapResourceListEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(xcapResourceListManager));
#endif
#if (CPCAPI2_BRAND_WATCHER_INFO_MODULE == 1)
   winfoManager = WatcherInfoManager::getInterface(phone);
   winfoEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(winfoManager));
#endif
#if (CPCAPI2_BRAND_RCSPROVISIONING_MODULE == 1)
   provision = RcsProvisionManager::getInterface(phone);
#endif
#if (CPCAPI2_BRAND_CONFERENCE_MODULE == 1)
   conferenceManager = SipConferenceManager::getInterface(phone);
#endif
#if (CPCAPI2_BRAND_REMOTE_SYNC_MODULE == 1)
   remoteSync = RemoteSyncManager::getInterface(phone);
   remoteSyncEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(remoteSync));
   remoteSyncSession = remoteSync->create();
#endif
#if (CPCAPI2_BRAND_VCCS_MODULE == 1)
   vccsAccountManager = Account::VccsAccountManager::getInterface(phone);
   vccsConferenceManager = Conference::VccsConferenceManager::getInterface(phone);
   vccsEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(vccsAccountManager));
   vccsAccountHandle = vccsAccountManager->create();
#endif
#if (CPCAPI2_BRAND_SNS_MODULE == 1)
   notificationService = NotificationService::getInterface(phone);
   notificationEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(notificationService));
   notificationHandle = notificationService->createChannel();
#endif
#if (CPCAPI2_BRAND_BIEVENTS_MODULE == 1)
   biEventManager = BIEventsManager::getInterface(phone);
   biEventHelper = BIEventsHelper::getInterface( biEventManager );
   biEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(biEventManager));
   biEventHandle = biEventManager->create();
#endif
#if (CPCAPI2_BRAND_STRETTO_PROVISIONING_MODULE == 1)
   provisioningManager = CPCAPI2::StrettoProvisioning::StrettoProvisioning::getInterface(phone);
   provisioningEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(provisioningManager));
   provisioningHandle = provisioningManager->create();
#endif
#if (CPCAPI2_BRAND_PUSH_NOTIFICATION_SERVER_MODULE == 1)
   pushNotificationServerManager = PushNotificationServiceManager::getInterface(phone);
   pushNotificationServerEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(pushNotificationServerManager));
#endif
#if (CPCAPI2_BRAND_PUSH_NOTIFICATION_CLIENT_MODULE == 1)
   pushNotificationClientManager = PushNotificationEndpointManager::getInterface(phone);
   pushNotificationClientEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(pushNotificationClientManager));
#endif
#if (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
   jsonApiServer = CPCAPI2::JsonApi::JsonApiServer::getInterface(phone);
   jsonApiServerEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(jsonApiServer));
#endif
#if (CPCAPI2_BRAND_ORCHESTRATION_SERVER_MODULE == 1)
   orchestrationServer = CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(phone);
   orchestrationServerEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(orchestrationServer));
#endif
#if (CPCAPI2_BRAND_CLOUD_WATCHDOG_SERVER_MODULE == 1)
   cloudWatchdogServer = CPCAPI2::CloudWatchdog::CloudWatchdogService::getInterface(phone);
   cloudWatchdogServerEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(cloudWatchdogServer));
#endif
#if (CPCAPI2_BRAND_CLOUD_SERVICE_CONFIG_MODULE == 1)
   cloudServiceConfig = CPCAPI2::CloudServiceConfig::CloudServiceConfigManager::getInterface(phone);
   cloudServiceConfigEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(cloudServiceConfig));
#endif
#if (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
   jsonApiClient = CPCAPI2::JsonApi::JsonApiClient::getInterface(phone);
   jsonApiClientEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(jsonApiClient));
   sipAccountJsonProxy = CPCAPI2::SipAccount::SipAccountManagerJsonProxy::getInterface(phone);
   sipAccountJsonProxyEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(sipAccountJsonProxy));
   sipConvJsonProxy = CPCAPI2::SipConversation::SipConversationManagerJsonProxy::getInterface(phone);
#endif
#if (CPCAPI2_BRAND_REMOTE_SYNC_MODULE == 1)
   remoteSyncJsonProxy = CPCAPI2::RemoteSync::RemoteSyncJsonProxy::getInterface(phone);
   remoteSyncJsonProxyEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(remoteSyncJsonProxy));
#endif
#if (CPCAPI2_BRAND_AUTH_SERVER_MODULE == 1)
   authServer = CPCAPI2::AuthServer::AuthServer::getInterface(phone);
   authServerEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(authServer));
#endif
#if (CPCAPI2_BRAND_CONFERENCE_CONNECTOR_MODULE == 1)
   // init with createConferenceConnector()
   conferenceConnector = NULL;
   conferenceConnectorEvents = NULL;
#endif
#if (CPCAPI2_BRAND_CLOUD_RELAY_CONNECTOR_MODULE == 1)
   cloudRelayConnector = CPCAPI2::CloudRelayConnector::CloudRelayConnector::getInterface(phone);
   cloudRelayConnectorEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(cloudRelayConnector));
#endif
#if (CPCAPI2_BRAND_CONFERENCE_BRIDGE_MODULE == 1)
   conferenceBridge = CPCAPI2::ConferenceBridge::ConferenceBridgeManager::getInterface(phone);
   conferenceBridgeEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(conferenceBridge));
#endif
#if (CPCAPI2_BRAND_CONFERENCE_BRIDGE_REGISTRAR_MODULE == 1)
   conferenceRegistrar = CPCAPI2::ConferenceBridge::ConferenceRegistrar::getInterface(phone);
   conferenceRegistrarEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(conferenceRegistrar));
#endif

   if (config.registerHandlers)
   {
      #if (CPCAPI2_BRAND_PTT_MODULE == 1)
      // PushToTalkServiceHandle alicePttService = ptt->createPushToTalkService();
      // ptt->setHandler(alicePttService, (PushToTalkHandler*)0xDEADBEEF);
      #endif
      account->setHandler(handle, (SipAccountHandler*)0xDEADBEEF);
      conversation->setHandler(handle, (SipConversationHandler*)0xDEADBEEF);

      if (!config.dtlsSupported)
      {
         // must be called after SipConversationManager::setHandler
         dynamic_cast<SipConversationManagerInternal*>(conversation)->setDtlsSupported(handle, false);
      }

      if (subs != NULL) {
         subs->setHandler(handle, "cpcapi2test", (SipEventSubscriptionHandler*)0xDEADBEEF);
      }
#if (CPCAPI2_BRAND_SIP_FILE_TRANSFER_MODULE == 1)
      fileTransferManager->setHandler( handle, (SipFileTransferHandler*)0xDEADBEEF);
#endif
      if (presence != NULL) {
         presence->setHandler(handle, (SipPresenceSubscriptionHandler*)0xDEADBEEF);
         presence->setPublicationHandler(handle, (SipPresencePublicationHandler*)0xDEADBEEF);
      }
      if (im != NULL) {
         im->setHandler(handle, (SipInstantMessageHandler*)0xDEADBEEF);
      }
      if (mwi != NULL) {
         mwi->setHandler(handle, (SipMessageWaitingIndicationHandler*)0xDEADBEEF);
      }
#if (CPCAPI2_BRAND_SIP_CHAT_MODULE == 1)
      chatManager->setHandler(handle, (SipChatHandler*) 0xDEADBEEF);
#endif
#if (CPCAPI2_BRAND_SIP_STANDALONE_MESSAGING_MODULE == 1)
      standaloneMessagingManager->setHandler(handle, (SipStandaloneMessagingHandler*) 0xDEADBEEF);
#endif
#if (CPCAPI2_BRAND_CAPABILITY_DISCOVERY_MODULE == 1)
      capability->setHandler(handle, (RcsCapabilityDiscoveryHandler*)0xDEADBEEF);
#endif
#ifdef CPCAPI2_GENBAND_MODULE
     //genbandManager->setHandler(handle, (GenbandRestAPIHandler*)0xDEADBEEF);
#endif
#if (CPCAPI2_BRAND_RESOURCE_LIST_MODULE == 1)
      xcapResourceListManager->setHandler(handle, (XcapResourceListHandler*)0xDEADBEEF);
#endif
#if (CPCAPI2_BRAND_WATCHER_INFO_MODULE == 1)
      winfoManager->setHandler(handle, (WatcherInfoSubscriptionHandler*)0xDEADBEEF);
#endif
#if (CPCAPI2_BRAND_CONFERENCE_MODULE == 1)
      conferenceManager->setHandler(handle, (SipConferenceHandler*)0xDEADBEEF );
#endif
#if (CPCAPI2_BRAND_REMOTE_SYNC_MODULE == 1)
      remoteSync->setHandler( remoteSyncSession, ( RemoteSyncHandler * ) 0xDEADBEEF);
#endif
#if (CPCAPI2_BRAND_VCCS_MODULE == 1)
      vccsAccountManager->setHandler( vccsAccountHandle, ( Account::VccsAccountHandler* ) 0xDEADBEEF );
      vccsConferenceManager->setHandler( vccsAccountHandle, ( Conference::VccsConferenceHandler* ) 0xDEADBEEF );
#endif
#if (CPCAPI2_BRAND_SNS_MODULE == 1)
      notificationService->setHandler( notificationHandle, ( Notification::NotificationHandler* ) 0xDEADBEEF );
#endif
#if (CPCAPI2_BRAND_BIEVENTS_MODULE == 1)
      biEventManager->setHandler( biEventHandle, ( BIEvents::BIEventsHandler* ) 0xDEADBEEF );
#endif
#if (CPCAPI2_BRAND_STRETTO_PROVISIONING_MODULE == 1)
      provisioningManager->setHandler( provisioningHandle, ( StrettoProvisioningHandler* ) 0xDEADBEEF );
#endif
#if (CPCAPI2_BRAND_PUSH_NOTIFICATION_SERVER_MODULE == 1)
      pushNotificationServerManager->setHandler(0, ( PushNotificationServiceHandler* ) 0xDEADBEEF );
#endif
#if (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
      jsonApiServer->setHandler((JsonApiServerHandler*)0xDEADBEEF);
#endif
#if (CPCAPI2_BRAND_ORCHESTRATION_SERVER_MODULE == 1)
      orchestrationServer->setHandler((OrchestrationServerHandler*)0xDEADBEEF);
#endif
#if (CPCAPI2_BRAND_CLOUD_WATCHDOG_SERVER_MODULE == 1)
      cloudWatchdogServer->setHandler((CloudWatchdogHandler*)0xDEADBEEF);
#endif
#if (CPCAPI2_BRAND_CLOUD_SERVICE_CONFIG_MODULE == 1)
      cloudServiceConfig->setHandler((CloudServiceConfigHandler*)0xDEADBEEF);
#endif
#if (CPCAPI2_BRAND_NETWORK_CHANGE_MODULE == 1)
      if (networkChangePublic)
      {
         networkChangePublic->setHandler(networkChangeHandle, (NetworkChangeHandler*)0xDEADBEEF);
      }
#endif
#if (CPCAPI2_BRAND_CONFERENCE_BRIDGE_REGISTRAR_MODULE == 1)
   conferenceRegistrar->setHandler((ConferenceRegistrarHandler*)0xDEADBEEF);
#endif
   }

   if (videoHelper.get() != NULL)
   {
      videoHelper->setTestAccount(this);
   }
   
#if (defined(__ANDROID__))
   if (TestAccount::sSystemCameraOwner.tryOwn())
   {
      mOwnsSystemCamera = true;
      setCaptureDeviceMatching("back");
   }
   else
   {
      mOwnsSystemCamera = false;
      setCustomVideoSourceDeviceCapture();
   }
#else
   if (TestEnvironmentConfig::dockerContainerized())
   {
      setCustomVideoSourceDeviceCapture();
   }
   else
   {
      setManyCamCapture();
   }
#endif

   static_cast<PhoneInternal*>(phone)->setCallOnDestructFn(phoneShutdown, reinterpret_cast<void*>(&gShutdownInfo));
   
   if (config.phoneInitConnectionPrefs.networkChangeManagerType == ConnectionPreferences::NetworkChangeManagerType_PlatformDefault)
   {
#ifdef ANDROID
      CPCAPI2::Android::Networking::setNetworkManagerContext();
      CPCAPI2::NetworkChangeManager_Android* netChangeMgr = dynamic_cast<CPCAPI2::NetworkChangeManagerInterface*>(CPCAPI2::NetworkChangeManager::getInterface(phone))->getAndroidImpl();
      netChangeMgr->enableAndroidJavaNetworkChangeManager();
      CPCAPI2::Android::Networking::enableNetworkChangeManager();
#endif
   }
}


TestAccount::~TestAccount()
{
   shutdown();

   if (initialized && (!slave || initSlave))
      assert(phoneReleased);
      
   if (mOwnsSystemAudio)
   {
      TestAccount::sSystemAudioOwner.releaseOwn();
   }
   
   if (mOwnsSystemCamera)
   {
      TestAccount::sSystemCameraOwner.releaseOwn();
   }
}

#define DELETE_EVENT_HANDLER(name) \
if (name) \
{ \
  name->shutdown(); \
  delete name; \
  name = NULL; \
}

void TestAccount::shutdown(int timeoutSeconds, bool skipOpenSslCheck)
{
   if (phone == NULL)
      return;

   if (!initialized)
      return;

   if (enabled && shouldDisableOnDestruct)
   {
      disable(true);
   }

   if (alianzaApiAccountHandle != 0)
   {
      destroyAlianzaAccountInBackend();
   }

   if (handle != -1)
   {
      if (account)   // at least one unit test sets this to NULL directly
         ASSERT_EQ(account->destroy(handle), kSuccess);
      handle = -1;
   }
   //std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   if (!skipOpenSslCheck)
   {
      unsigned long lastOpenSslError = 0;
      static_cast<PhoneInternal*>(phone)->checkSdkThreadOpenSslErrors(lastOpenSslError);
      // if this asserts, it's an indication that some code has left behind uncleaned up OpenSSL errors.
      // this can be confusing when checking for OpenSSL errors in other unrelated parts of our code,
      // so we should always try to clean up OpenSSL errrors.
      EXPECT_EQ(0, lastOpenSslError);
   }

   if (!slave || initSlave)
   {
      // attempt to keep logging going as long as possible on shutdown;
      // also, since all CPCAPI2 instances end up sharing the same underlying
      // resip logger (due to resip's usage of statics), turning off logging here
      // can distrupt logging for other TestAccount instances.
      // finally, our logging target is a static instance of AutoTestsLogger
      // which should survive until process termination, so it should be safe to
      // not disable logging here.
      //phone->setLoggingEnabled("", false);
      DELETE_EVENT_HANDLER(phoneEvents)
   }

   DELETE_EVENT_HANDLER(alianzaApiEvents)
   DELETE_EVENT_HANDLER(accountEvents)

   disableAndroidBackgrounding();

   for (cpc::vector<CPCAPI2::Recording::RecorderHandle>::const_iterator it = recordingHandles.begin();
        it != recordingHandles.end(); ++it)
   {
      recording->setHandler(*it, NULL);
   }

   DELETE_EVENT_HANDLER(mwiEvents)
   DELETE_EVENT_HANDLER(presenceEvents)
   DELETE_EVENT_HANDLER(subsEvents)
   delete mediaEvents;
   DELETE_EVENT_HANDLER(conversationEvents)
   DELETE_EVENT_HANDLER(conversationStateEvents)
   DELETE_EVENT_HANDLER(imEvents)
   delete recordingEvents;
   delete callQualityEvents;

#if (CPCAPI2_BRAND_SIP_FILE_TRANSFER_MODULE == 1)
   DELETE_EVENT_HANDLER(fileTransferEvents)
#endif

#if (CPCAPI2_BRAND_CAPABILITY_DISCOVERY_MODULE == 1)
   DELETE_EVENT_HANDLER(capabilityEvents)
#endif

#if (CPCAPI2_BRAND_PEER_CONNECTION_MODULE == 1)
   delete peerConnEvents;
#endif

#if (CPCAPI2_BRAND_WEB_CALL_MODULE == 1)
   delete webCallEvents;
#endif

#if (CPCAPI2_BRAND_PTT_MODULE == 1)
   delete pttEvents;
#endif
#if (CPCAPI2_BRAND_REMOTE_SYNC_MODULE == 1)
   delete remoteSyncEvents;
#endif
#if (CPCAPI2_BRAND_ANALYTICS_MODULE == 1)
   delete analyticsEvents;
#endif
#if (CPCAPI2_BRAND_STRETTO_UEM_MODULE == 1)
   delete strettoUemEvents;
#endif
#if (CPCAPI2_BRAND_VCCS_MODULE == 1)
   delete vccsEvents;
#endif
#if (CPCAPI2_BRAND_SNS_MODULE == 1)
   delete notificationEvents;
#endif
#if (CPCAPI2_BRAND_BIEVENTS_MODULE == 1)
   delete biEvents;
#endif
#if (CPCAPI2_BRAND_STRETTO_PROVISIONING_MODULE == 1)
   delete provisioningEvents;
#endif
#if (CPCAPI2_BRAND_PUSH_NOTIFICATION_SERVER_MODULE == 1)
   delete pushNotificationServerEvents;
#endif
#if (CPCAPI2_BRAND_PUSH_NOTIFICATION_CLIENT_MODULE == 1)
   delete pushNotificationClientEvents;
#endif
#if (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
   delete jsonApiServerEvents;
#endif
#if (CPCAPI2_BRAND_ORCHESTRATION_SERVER_MODULE == 1)
   delete orchestrationServerEvents;
#endif
#if (CPCAPI2_BRAND_CLOUD_WATCHDOG_SERVER_MODULE == 1)
   delete cloudWatchdogServerEvents;
#endif
#if (CPCAPI2_BRAND_CLOUD_SERVICE_CONFIG_MODULE == 1)
   delete cloudServiceConfigEvents;
#endif
#if (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
   delete sipAccountJsonProxyEvents;
   delete remoteSyncJsonProxyEvents;
   delete jsonApiClientEvents;
#endif
#if (CPCAPI2_BRAND_AUTH_SERVER_MODULE == 1)
   delete authServerEvents;
#endif
#if (CPCAPI2_BRAND_CONFERENCE_CONNECTOR_MODULE==1)
   delete conferenceConnectorEvents;
#endif
#if (CPCAPI2_BRAND_CLOUD_RELAY_CONNECTOR_MODULE==1)
   delete cloudRelayConnectorEvents;
#endif
#if (CPCAPI2_BRAND_NETWORK_CHANGE_MODULE==1)
   delete networkChangeEvents;
#endif
#if (CPCAPI2_BRAND_SIP_CHAT_MODULE == 1)
   DELETE_EVENT_HANDLER(chatEvents)
#endif
#if (CPCAPI2_BRAND_SIP_STANDALONE_MESSAGING_MODULE == 1)
   DELETE_EVENT_HANDLER(standaloneMessagingEvents)
#endif
#if (CPCAPI2_BRAND_CONFERENCE_BRIDGE_MODULE == 1)
   delete conferenceBridgeEvents;
#endif
#if (CPCAPI2_BRAND_CONFERENCE_BRIDGE_REGISTRAR_MODULE == 1)
   delete conferenceRegistrarEvents;
#endif
#if (CPCAPI2_BRAND_RESOURCE_LIST_MODULE == 1)
   DELETE_EVENT_HANDLER(xcapResourceListEvents)
#endif
#if (CPCAPI2_BRAND_WATCHER_INFO_MODULE == 1)
   DELETE_EVENT_HANDLER(winfoEvents)
#endif

   videoHelper.reset();

   if (!slave || initSlave)
   {
      std::chrono::time_point<std::chrono::system_clock> preShutdown = std::chrono::system_clock::now();
      CPCAPI2::Phone::release(phone);
      long durationSec = std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now() - preShutdown).count();
      ASSERT_LE(durationSec, timeoutSeconds);
      long remainingTimeoutSeconds = timeoutSeconds - durationSec;
            
      {
         std::unique_lock<std::mutex> lock(*(gShutdownInfo.mutex));
         if (gShutdownInfo.shutdown)
         {
            shutdownTimedout = false;
         }
         else
         {
            gShutdownInfo.cv->wait_until(lock, std::chrono::system_clock::now() + std::chrono::seconds(remainingTimeoutSeconds));

            // Timed-out during shutdown, might have dangling modules
            shutdownTimedout = !gShutdownInfo.shutdown;
         }
      }
   }

   phone = NULL;
}

void TestAccount::setCustomVideoSourceDeviceCapture()
{
   if (video)
   {
      video->queryDeviceList();
      video->setCaptureDevice(CPCAPI2::Media::kCustomVideoSourceDeviceId);
      mUsingCustomVideoSource = true;
   }
}

void TestAccount::setManyCamCapture()
{
   if (video)
   {
      video->queryDeviceList();
      video->setCaptureDevice(**********); // ManyCam
   }

   //class TestVideoHandler : public CPCAPI2::Media::VideoHandler
   //{
   //public:
   //   TestVideoHandler() : mManyCamDeviceId(0) {}
   //   virtual ~TestVideoHandler() {}
   //   virtual int onVideoDeviceListUpdated(const VideoDeviceListUpdatedEvent& args)
   //   {
   //      cpc::vector<CPCAPI2::Media::VideoDeviceInfo>::const_iterator it = args.deviceInfo.begin();
   //      for (; it != args.deviceInfo.end(); ++it)
   //      {
   //         if (resip::isEqualNoCase(resip::Data(it->friendlyName.c_str(), std::min<unsigned int>(7,it->friendlyName.size())), "ManyCam"))
   //         {
   //            mManyCamDeviceId = it->id;
   //         }
   //      }
   //      return CPCAPI2::kSuccess;
   //   }
   //   virtual int onVideoCodecListUpdated(const VideoCodecListUpdatedEvent& args) { return CPCAPI2::kSuccess; }

   //   unsigned int mManyCamDeviceId;
   //};

   //TestVideoHandler* testVideoHandler = new TestVideoHandler();
   //video->setHandler(testVideoHandler);
   //video->queryDeviceList();
   //media->process(CPCAPI2::Media::MediaManager::kBlockingModeInfinite);

   //if (testVideoHandler->mManyCamDeviceId != 0)
   //{
   //   video->setCaptureDevice(testVideoHandler->mManyCamDeviceId);
   //}

   //video->setHandler(NULL);
}

cpc::string TestAccount::uri()
{
   std::string proxy("");
   std::string environmentId = TestEnvironmentConfig::testEnvironmentId().c_str();
   if (environmentId == "repro")
   {
      proxy = config.uri();
   }
   else
   {
      proxy = config.alianzaConfig.sipOutboundProxy;
      std::size_t pos = proxy.find_last_of(":");
      if (pos != std::string::npos)
      {
         proxy = proxy.substr(0, pos);
      }
      std::stringstream ss;
      if (config.alianzaSession.extensionEnabled)
      {
         ss << "sip:" << config.alianzaSession.getUa()->extension << "@" << proxy;
      }
      else
      {
         ss << "sip:" << config.alianzaSession.getUa()->phoneNumber << "@" << proxy;
      }
      proxy = ss.str();
   }

   return proxy.c_str();
}

bool findStringIgnoreCase(const std::string & strHaystack, const std::string & strNeedle)
{
  auto it = std::search(
    strHaystack.begin(), strHaystack.end(),
    strNeedle.begin(),   strNeedle.end(),
    [](char ch1, char ch2) { return std::toupper(ch1) == std::toupper(ch2); }
  );
  return (it != strHaystack.end() );
}

int TestAccount::setCaptureDeviceMatching(const std::string& friendlyNameSubstring)
{
   EXPECT_EQ(video->queryDeviceList(), kSuccess);
   VideoDeviceListUpdatedEvent evt;
   int dummyHandle = 0;
   EXPECT_TRUE(mediaEvents->expectEvent("VideoHandler::onVideoDeviceListUpdated", 30000, CPCAPI2::test::AlwaysTruePred(), dummyHandle, evt));
   EXPECT_NE(evt.deviceInfo.size(), 0);
   bool found = false;
   for (cpc::vector<VideoDeviceInfo>::const_iterator it = evt.deviceInfo.begin(); it != evt.deviceInfo.end(); ++it)
   {
      std::string friendlyName = it->friendlyName.c_str();
      if (findStringIgnoreCase(friendlyName, friendlyNameSubstring))
      {
         video->setCaptureDevice(it->id);
         found = true;
         break;
      }
   }

   if (found)
   {
      return kSuccess;
   }
   EXPECT_TRUE(found);
   return kError;
}

// when we switch to CPE config service API we'll need to switch this to hit
// cymbus-clients/partition/:partitionId/account/:accountId/cymbus/:cymbusClientId/registrationstatus
// from https://alianza.atlassian.net/browse/PROV-4409
//
// partitionId we have
// accountId we have
// clientId will be the ClientUniqueGUID we generated and passed to /v2/configuration/cymbus-clients/configuration API from
// https://alianza.atlassian.net/wiki/spaces/DEV/pages/**********/TDD+RTC+SDK+Large+Tests+for+CPE+SIP+Backend+-+Version+1.1
// 
void TestAccount::checkAlianzaAccountRegistrationStatus(bool checkSip, bool checkPush)
{
   ASSERT_TRUE(checkSip || checkPush) << "checkAlianzaAccountRegistrationStatus() called with nothing to check";

   std::shared_ptr<AlianzaSipUaInfo> ua = config.alianzaSession.getUa();
   std::string messageBody("");
   std::string url("");

   AlianzaApiTestHelper::createMessageToGetClientRegistrationStatus(config.alianzaConfig, config.alianzaSession, *ua, url, messageBody);

   bool sipRegistered = false;
   bool pushRegistered = false;
   
   for (int i = 0; i < 10; ++i)
   {
      alianzaApiEvents->clearCommands();
      alianzaApiManager->sendQueryMessage(url.c_str(), messageBody.c_str());
      assertClientRegistrationStatusRetrieved(alianzaApiEvents, sipRegistered, pushRegistered);

      if ((sipRegistered && !checkPush) || (pushRegistered && !checkSip) || (sipRegistered && pushRegistered))
      {
         safeCout("Registration verified on try number " << i + 1);
         break;
      }
      std::this_thread::sleep_for(std::chrono::seconds(3));
   }

   if (checkSip)
      ASSERT_TRUE(sipRegistered) << "Platform does not report this account has SIP registered after wait";
   if (checkPush)
      ASSERT_TRUE(pushRegistered) << "Platform does not report this account has SIP registered for PUSH after wait";
}

void TestAccount::enable(bool assertRegistrationState)
{
   init();

   std::string environmentId = TestEnvironmentConfig::testEnvironmentId().c_str();
   if (environmentId != "repro" && !mSkipAutoCreateAlianzaAccountInBackend)
   {
      ASSERT_NO_FATAL_FAILURE(createAlianzaAccountInBackend());

      AlianzaSipUaInfo ua = *config.alianzaSession.getUa();
      config.settings.username = ua.sipUsername.c_str();
      config.settings.password = ua.sipPassword.c_str();
      config.settings.domain = ua.sipDomain.c_str();

      ASSERT_EQ(account->configureDefaultAccountSettings(handle, config.settings), kSuccess);
      ASSERT_EQ(account->applySettings(handle), kSuccess);

   }

   ASSERT_EQ(account->enable(handle), kSuccess);
   if (assertRegistrationState)
   {
      assertAccountRegisteringEx(*this);
      assertAccountRegisteredEx(*this);
   }

   if (environmentId != "repro")
   {
      // without this check the platform might not be ready to route calls to the SIP account we've
      // just registered, which can cause flaky test cases.
      checkAlianzaAccountRegistrationStatus();
   }

   enabled = true;
}

void TestAccount::disable(bool force, bool assertRegistrationState, bool destroyBackendAccount)
{
   safeCout("disable " << config.name);

   if (!initialized) return;

   //std::this_thread::sleep_for(std::chrono::milliseconds(200));
   ASSERT_EQ(account->disable(handle, force), kSuccess);
   if (assertRegistrationState)
   {
      assertAccountDeregisteringEx(*this);
      assertAccountDeregisteredEx(*this);
   }
   enabled = false;

   if ((alianzaApiAccountHandle != 0) && destroyBackendAccount)
   {
      destroyAlianzaAccountInBackend();
   }
}

void TestAccount::enableOnlyThisCodec(const resip::Data& codecName)
{
   CPCAPI2::Media::AudioCodecListUpdatedEvent evt;
   int handle = 0;
   audio->queryCodecList();
   ASSERT_TRUE(mediaEvents->expectEvent("AudioHandler::onAudioCodecListUpdated", 30000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
   ASSERT_NE(evt.codecInfo.size(), 0);

   bool wasEnabled = false;

   cpc::vector<AudioCodecInfo>::const_iterator itCodecs = evt.codecInfo.begin();
   for (; itCodecs != evt.codecInfo.end(); ++itCodecs)
   {
      if (resip::isEqualNoCase(itCodecs->codecName.c_str(), codecName))
      {
         wasEnabled = true;
         audio->setCodecEnabled(itCodecs->id, true);
      }
      else
      {
         audio->setCodecEnabled(itCodecs->id, false);
      }
   }

   ASSERT_EQ(wasEnabled, true);
}

void TestAccount::enableOnlyThisVideoCodec(const resip::Data& codecName)
{
   CPCAPI2::Media::VideoCodecListUpdatedEvent evt;
   int handle = 0;
   
   ASSERT_EQ(video->queryCodecList(), kSuccess);
   ASSERT_TRUE(mediaEvents->expectEvent("VideoHandler::onVideoCodecListUpdated", 30000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
   ASSERT_NE(evt.codecInfo.size(), 0);

   bool wasEnabled = false;

   cpc::vector<VideoCodecInfo>::const_iterator itCodecs = evt.codecInfo.begin();
   for (; itCodecs != evt.codecInfo.end(); ++itCodecs)
   {
      if (resip::isEqualNoCase(itCodecs->codecName.c_str(), codecName))
      {
         wasEnabled = true;
         video->setCodecEnabled(itCodecs->id, true);
      }
      else
      {
         video->setCodecEnabled(itCodecs->id, false);
      }
   }

   ASSERT_EQ(wasEnabled, true);
}

void TestAccount::enableCodec(const resip::Data& codecName)
{
   CPCAPI2::Media::AudioCodecListUpdatedEvent evt;
   int handle = 0;
   audio->queryCodecList();
   ASSERT_TRUE(mediaEvents->expectEvent("AudioHandler::onAudioCodecListUpdated", 30000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
   ASSERT_NE(evt.codecInfo.size(), 0);

   bool wasEnabled = false;

   cpc::vector<AudioCodecInfo>::const_iterator itCodecs = evt.codecInfo.begin();
   for (; itCodecs != evt.codecInfo.end(); ++itCodecs)
   {
      if (resip::isEqualNoCase(itCodecs->codecName.c_str(), codecName))
      {
         wasEnabled = true;
         audio->setCodecEnabled(itCodecs->id, true);
      }
   }

   ASSERT_EQ(wasEnabled, true);
}

void TestAccount::setCodecPriority(const resip::Data& codecName, unsigned int priority)
{
   CPCAPI2::Media::AudioCodecListUpdatedEvent evt;
   int handle = 0;
   audio->queryCodecList();
   ASSERT_TRUE(mediaEvents->expectEvent("AudioHandler::onAudioCodecListUpdated", 30000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
   ASSERT_NE(evt.codecInfo.size(), 0);

   bool wasSet = false;

   cpc::vector<AudioCodecInfo>::const_iterator itCodecs = evt.codecInfo.begin();
   for (; itCodecs != evt.codecInfo.end(); ++itCodecs)
   {
      if (resip::isEqualNoCase(itCodecs->codecName.c_str(), codecName))
      {
         wasSet = true;
         audio->setCodecPriority(itCodecs->id, priority);
      }
   }

   ASSERT_EQ(wasSet, true);
}

void TestAccount::disableCodec(const resip::Data& codecName)
{
   CPCAPI2::Media::AudioCodecListUpdatedEvent evt;
   int handle = 0;
   audio->queryCodecList();
   ASSERT_TRUE(mediaEvents->expectEvent("AudioHandler::onAudioCodecListUpdated", 30000, CPCAPI2::test::AlwaysTruePred(), handle, evt));

   ASSERT_GT(evt.codecInfo.size(), 0);

   bool wasDisabled = false;

   cpc::vector<AudioCodecInfo>::const_iterator itCodecs = evt.codecInfo.begin();
   for (; itCodecs != evt.codecInfo.end(); ++itCodecs)
   {
      if (resip::isEqualNoCase(itCodecs->codecName.c_str(), codecName))
      {
         wasDisabled = true;
         audio->setCodecEnabled(itCodecs->id, false);
      }
   }

   ASSERT_EQ(wasDisabled, true);
}

void TestAccount::disableAllAudioCodecs()
{
   CPCAPI2::Media::AudioCodecListUpdatedEvent evt;
   int handle = 0;
   audio->queryCodecList();
   ASSERT_TRUE(mediaEvents->expectEvent("AudioHandler::onAudioCodecListUpdated", 30000, CPCAPI2::test::AlwaysTruePred(), handle, evt));

   ASSERT_GT(evt.codecInfo.size(), 0);

   cpc::vector<AudioCodecInfo>::const_iterator itCodecs = evt.codecInfo.begin();
   for (; itCodecs != evt.codecInfo.end(); ++itCodecs)
   {
      audio->setCodecEnabled(itCodecs->id, false);
   }
}

void TestAccount::onPhoneRelease(void* p)
{
   static_cast<TestAccount*>(p)->phoneReleased = true;
}

void TestAccount::initiateVideo(bool createLocalWindow, bool createIncomingWindow)
{
   if (videoHelper)
   {
      videoHelper->startVideo(createLocalWindow, createIncomingWindow);
      if (createLocalWindow)
      {
         this->video->startCapture();
         if (TestEnvironmentConfig::drawLocalVideo())
         {
            this->video->setLocalVideoRenderTarget(videoHelper->getHwndLocalWindow());
         }
      }
      if (createIncomingWindow)
      {
         if (TestEnvironmentConfig::drawLocalVideo())
         {
            this->video->setIncomingVideoRenderTarget(videoHelper->getHwndIncomingWindow());
         }
      }
   }
}
            
void TestAccount::setupRecorderHandler(CPCAPI2::Recording::RecorderHandle handle)
{
   if (recording != NULL)
   {
      recordingHandles.push_back(handle);
      recording->setHandler(handle, (Recording::RecordingHandler*)0xDEADBEEF);
   }
}
      
void TestAccount::cleanupRecorderHandler(CPCAPI2::Recording::RecorderHandle handle)
{
   if (recording != NULL)
   {
      for (cpc::vector<CPCAPI2::Recording::RecorderHandle>::iterator it = recordingHandles.begin();
           it != recordingHandles.end(); )
      {
         if (*it == handle)
         {
            recording->setHandler(handle, NULL);
            it = recordingHandles.erase(it);
         }
         else
            ++it;
      }
   }
}

void TestAccount::enableAndroidBackgrounding(const std::shared_ptr<CPCAPI2::test::AndroidBackgroundingBookkeeper>& bk)
{
#ifdef ANDROID
   CPCAPI2::AndroidBackgroundManagerInterface* backgroundManager = CPCAPI2::AndroidBackgroundManagerInterface::getInterface(phone);
   jobject androidAppContext = reinterpret_cast<jobject>(TestRuntimeEnvironment::instance()->getAndroidAppContext());
   ASSERT_NE(nullptr, androidAppContext);

   if (bk)
   {
      AndroidBackgroundManagerImpl* impl = static_cast<AndroidBackgroundManagerImpl*>(backgroundManager);
      impl->setObserver(bk.get());
   }

   // this line is eventually evoked when an Android app invokes enableBackgroundingSupport on SipPhoneAndroid;
   // we replicate this by invoking it directly here.
   backgroundManager->enableBackgroundingSupport(androidAppContext);

   mAndroidBackgroundingEnabled = true;
#endif // ANDROID
}

void TestAccount::disableAndroidBackgrounding()
{
#ifdef ANDROID
   if (mAndroidBackgroundingEnabled)
   {
      CPCAPI2::AndroidBackgroundManagerInterface* backgroundManager = CPCAPI2::AndroidBackgroundManagerInterface::getInterface(phone);
      backgroundManager->disableBackgroundingSupport();
      mAndroidBackgroundingEnabled = false;
   }
#endif // ANDROID
}

ConferenceConnector::ConferenceConnectorHandle TestAccount::createConferenceConnector()
{
   if (conferenceConnector == NULL)
   {
      conferenceConnector = CPCAPI2::ConferenceConnector::ConferenceConnectorManager::getInterface(phone);
      conferenceConnectorEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(conferenceConnector));
   }
   ConferenceConnector::ConferenceConnectorHandle h = conferenceConnector->createConferenceConnector();
   conferenceConnector->setHandler(h, reinterpret_cast<CPCAPI2::ConferenceConnector::ConferenceConnectorHandler*>(0xDEADBEEF));
   return h;
}

CallQuality::CallQualityReporterHandle TestAccount::createCallQualityReporter()
{
   if (callQualityReport == NULL)
   {
      callQualityReport = CallQuality::CallQualityReportManager::getInterface(phone);
      callQualityEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(callQualityReport));
   }
   CallQuality::CallQualityReporterHandle h = callQualityReport->createCallQualityReporter();
   callQualityReport->setHandler(h, reinterpret_cast<CallQuality::CallQualityReportHandler*>(0xDEADBEEF));
   return h;
}
