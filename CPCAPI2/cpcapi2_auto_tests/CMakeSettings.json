﻿{
  "configurations": [
    {
      "name": "_AutoTests-Windows-x86-Debug",
      "generator": "Ninja",
      "configurationType": "Debug",
      "inheritEnvironments": [ "msvc_x86_x64" ],
      "buildRoot": "${projectDir}\\out\\build\\Windows-x86-Debug",
      "installRoot": "${projectDir}\\out\\install\\${name}",
      "cmakeCommandArgs": "",
      "buildCommandArgs": "",
      "ctestCommandArgs": "",
      "variables": [],
      "cmakeToolchain": "${projectDir}/../projects/cmake/toolchains/windows-msvc-x86.cmake"
    },
    {
      "name": "_AutoTests-Windows-x86-Release",
      "generator": "Ninja",
      "configurationType": "RelWithDebInfo",
      "buildRoot": "${projectDir}\\out\\build\\Windows-x86-Release",
      "installRoot": "${projectDir}\\out\\install\\${name}",
      "cmakeCommandArgs": "",
      "buildCommandArgs": "",
      "ctestCommandArgs": "",
      "inheritEnvironments": [ "msvc_x86_x64" ],
      "variables": [],
      "cmakeToolchain": "${projectDir}/../projects/cmake/toolchains/windows-msvc-x86.cmake"
    },
    {
      "name": "_AutoTests-Windows-x86_64-Debug",
      "generator": "Ninja",
      "configurationType": "Debug",
      "buildRoot": "${projectDir}\\out\\build\\Windows-x86_64-Debug",
      "installRoot": "${projectDir}\\out\\install\\${name}",
      "cmakeCommandArgs": "",
      "buildCommandArgs": "",
      "ctestCommandArgs": "",
      "inheritEnvironments": [ "msvc_x64_x64" ],
      "variables": [],
      "cmakeToolchain": "${projectDir}/../projects/cmake/toolchains/windows-msvc-x86_64.cmake"
    },
    {
      "name": "_AutoTests-Windows-x86_64-Release",
      "generator": "Ninja",
      "configurationType": "RelWithDebInfo",
      "buildRoot": "${projectDir}\\out\\build\\Windows-x86_64-Release",
      "installRoot": "${projectDir}\\out\\install\\${name}",
      "cmakeCommandArgs": "",
      "buildCommandArgs": "",
      "ctestCommandArgs": "",
      "inheritEnvironments": [ "msvc_x64_x64" ],
      "variables": [],
      "cmakeToolchain": "${projectDir}/../projects/cmake/toolchains/windows-msvc-x86_64.cmake"
    },
    {
      "name": "_AutoTests-CentOS-x86_64-Debug",
      "generator": "Ninja",
      "configurationType": "Debug",
      "buildRoot": "${projectDir}\\out\\build\\CentOS-x86_64-Debug",
      "installRoot": "${projectDir}\\out\\install\\${name}",
      "cmakeExecutable": "/usr/bin/cmake",
      "cmakeCommandArgs": "",
      "buildCommandArgs": "",
      "ctestCommandArgs": "",
      "inheritEnvironments": [ "linux_x64" ],
      "wslPath": "${defaultWSLPath}",
      "addressSanitizerRuntimeFlags": "detect_leaks=0",
      "variables": [],
      "cmakeToolchain": "${projectDir}/../projects/cmake/toolchains/centos-devtoolset8-x86_64.cmake"
    },
    {
      "name": "_AutoTests-CentOS-x86_64-Release",
      "generator": "Ninja",
      "configurationType": "RelWithDebInfo",
      "buildRoot": "${projectDir}\\out\\build\\CentOS-x86_64-Release",
      "installRoot": "${projectDir}\\out\\install\\${name}",
      "cmakeExecutable": "/usr/bin/cmake",
      "cmakeCommandArgs": "",
      "buildCommandArgs": "",
      "ctestCommandArgs": "",
      "inheritEnvironments": [ "linux_x64" ],
      "wslPath": "${defaultWSLPath}",
      "addressSanitizerRuntimeFlags": "detect_leaks=0",
      "variables": [],
      "cmakeToolchain": "${projectDir}/../projects/cmake/toolchains/centos-devtoolset8-x86_64.cmake"
    }
  ]
}