#if _WIN32
#include "stdafx.h"
#endif

#if FALSE // SdkLicensing doesn't seem to be defined anywhere
//#ifdef _WIN32

#include "cpcapi2_test_fixture.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>
#include <impl/phone/LicensingImpl.h>
#include <string>

#include "test_framework/cpcapi2_test_framework.h"

using namespace CPCAPI2;
using namespace CPCAPI2::test;

namespace {
   class LicenseAutoTest : public CpcapiAutoTest
   {
   public:
      LicenseAutoTest() {}
      ~LicenseAutoTest() {}
   };

   TEST_F(LicenseAutoTest, BasicLicenseAutoTest) {
      SdkLicensing lic;

      lic.Initialize(L"c:\\tmp", L"");

       CPCAPI2::ELicenseStatus st = lic.SetLicense(L"180J65PQFRD63W969MP3N0228");

      // Need a callback
       ASSERT_TRUE(st == CPCAPI2::ELS_Valid);
   }
}

#endif
