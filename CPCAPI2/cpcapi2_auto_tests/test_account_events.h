#pragma once
#ifndef TEST_ACCOUNT_EVENTS_H
#define TEST_ACCOUNT_EVENTS_H

#include "cpcapi2_test_fixture.h"
#include <condition_variable>
#include <deque>
#include "interface/experimental/account/SipNetworkProbeHandler.h"
#include "impl/account/CPDialogDnsResultManager.h"
#include "impl/account/SipAccountManagerInternal.h"

class TestAccountEvents
{

public:

   static void expectAccountRegisteringEx(int line, const std::string& fileName, TestAccount& account)
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
      ASSERT_TRUE(account.accountEvents->expectEvent(line, "SipAccountHandler::onAccountStatusChanged",
         fileName, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(account.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);
   }
   static void expectAccountRegistering(int line, TestAccount& account)
   {
      expectAccountRegisteringEx(line, "", account);
   }
   #define assertAccountRegistering(account) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestAccountEvents::expectAccountRegistering(__LINE__, account); \
   }
   #define assertAccountRegisteringEx(account) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestAccountEvents::expectAccountRegisteringEx(__LINE__, __FILE__, account); \
   }


   static void expectAccountRefreshingEx(int line, const std::string& fileName, TestAccount& account)
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
      ASSERT_TRUE(account.accountEvents->expectEvent(line, "SipAccountHandler::onAccountStatusChanged",
         fileName, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(account.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Refreshing, evt.accountStatus);
   }
   static void expectAccountRefreshing(int line, TestAccount& account)
   {
      expectAccountRefreshingEx(line, "", account);
   }
   #define assertAccountRefreshing(account) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestAccountEvents::expectAccountRefreshing(__LINE__, account); \
   }
   #define assertAccountRefreshingEx(account) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestAccountEvents::expectAccountRefreshingEx(__LINE__, __FILE__, account); \
   }


   static void expectAccountRegisteredEx(int line, const std::string& fileName, TestAccount& account)
   {
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
         ASSERT_TRUE(account.accountEvents->expectEvent(line, "SipAccountHandler::onAccountStatusChanged",
            fileName, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt)) << " error in account registration";
         ASSERT_EQ(account.handle, h) << " " << fileName << ": " << line << " error in account registration";
         if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
         {
            safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
         }
         ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus) << " account cannot be registered";
         if (account.config.settings.useRegistrar)
         {
            ASSERT_TRUE(evt.localContactBinding.size() > 0) << " registered event received with empty local binding";
         }
         else
         {
            ASSERT_EQ(evt.localContactBinding.size(), 0) << " registered event received with local binding for no-registrar configuration";
         }
      }
   }

   static void expectAccountRegistered(int line, TestAccount& account)
   {
      expectAccountRegisteredEx(line, "", account);
   }

   #define assertAccountRegistered(account) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestAccountEvents::expectAccountRegistered(__LINE__, account); \
   }

   #define assertAccountRegisteredEx(account) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestAccountEvents::expectAccountRegisteredEx(__LINE__, __FILE__, account); \
   }

   static void expectAccountStatusUptilRegisteredEx(int line, const std::string& fileName, TestAccount& account, CPCAPI2::SipAccount::IpVersion version, CPCAPI2::SipAccount::SipAccountTransportType transport)
   {
      for (;;)
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
         ASSERT_TRUE(account.accountEvents->expectEvent(line, "SipAccountHandler::onAccountStatusChanged", fileName, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt)) << "error in account registration";
         ASSERT_EQ(account.handle, h) << "error in account registration";
         if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister || evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering)
         {
            safeCout("Continue waiting as registration is in progress, received account status: " << evt.accountStatus);
            continue;
         }
         if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
         {
            safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
         }

         ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus) << "account is not registered";
         ASSERT_EQ(version, evt.ipVersionInUse) << "registered but ip version mismatch";
         ASSERT_EQ(transport, evt.transportType) << "registered but transport type mismatch";
         break;
      }
   }

   static void expectAccountStatusUptilRegistered(int line, TestAccount& account, CPCAPI2::SipAccount::IpVersion version, CPCAPI2::SipAccount::SipAccountTransportType transport)
   {
      expectAccountStatusUptilRegisteredEx(line, "", account, version, transport);
   }

   #define assertAccountStatusUptilRegistered(account) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestAccountEvents::expectAccountStatusUptilRegistered(__LINE__, account, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_UDP); \
   }

   #define assertAccountStatusUptilRegisteredEx(account) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestAccountEvents::expectAccountStatusUptilRegisteredEx(__LINE__, __FILE__, account, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_UDP); \
   }

   #define assertAccountStatusUptilRegisteredEx2(account, version, transport) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestAccountEvents::expectAccountStatusUptilRegisteredEx(__LINE__, __FILE__, account, version, transport); \
   }

   static void expectAccountDeregisteringEx(int line, const std::string& fileName, TestAccount& account)
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
      ASSERT_TRUE(account.accountEvents->expectEvent(line, "SipAccountHandler::onAccountStatusChanged",
         fileName, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt)) << "error in unregistering account";
      ASSERT_EQ(account.handle, h) << "error in unregistering account";
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Unregistering, evt.accountStatus) << "account is not in unregistering status";
   }
   static void expectAccountDeregistering(int line, TestAccount& account)
   {
      expectAccountDeregisteringEx(line, "", account);
   }
   #define assertAccountDeregistering(account) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestAccountEvents::expectAccountDeregistering(__LINE__, account); \
   }
   #define assertAccountDeregisteringEx(account) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestAccountEvents::expectAccountDeregisteringEx(__LINE__, __FILE__, account); \
   }

   static void expectAccountDeregisteredEx(int line, const std::string& fileName, TestAccount& account)
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
      ASSERT_TRUE(account.accountEvents->expectEvent(line, "SipAccountHandler::onAccountStatusChanged",
         fileName, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt))<< "account is not deregistered";
      ASSERT_EQ(account.handle, h)<< "account is not deregistered";
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Unregistered, evt.accountStatus) << "account is not deregistered";
   }
   static void expectAccountDeregistered(int line, TestAccount& account)
   {
      expectAccountDeregisteredEx(line, "", account);
   }
   #define assertAccountDeregistered(account) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestAccountEvents::expectAccountDeregistered(__LINE__, account); \
   }
   #define assertAccountDeregisteredEx(account) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestAccountEvents::expectAccountDeregisteredEx(__LINE__, __FILE__, account); \
   }

   static void expectAccountWaitingToRegisterEx(int line, const std::string& fileName, TestAccount& account)
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
      ASSERT_TRUE(account.accountEvents->expectEvent(line, "SipAccountHandler::onAccountStatusChanged",
         fileName, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(account.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
   }
   static void expectAccountWaitingToRegister(int line, TestAccount& account)
   {
      expectAccountWaitingToRegisterEx(line, "", account);
   }
   #define assertAccountWaitingToRegister(account) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestAccountEvents::expectAccountWaitingToRegister(__LINE__, account); \
   }
   #define assertAccountWaitingToRegisterEx(account) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestAccountEvents::expectAccountWaitingToRegisterEx(__LINE__, __FILE__, account); \
   }

   static void expectAccountErrorEx2(int line, const std::string& fileName, TestAccount& account)
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::ErrorEvent evt;
      ASSERT_TRUE(account.accountEvents->expectEvent(line, "SipAccountHandler::onError", fileName, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(account.handle, h) << evt.errorText;
   }
   static void expectAccountErrorEx(int line, const cpc::string& errorStr, const std::string& fileName, TestAccount& account)
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::ErrorEvent evt;
      ASSERT_TRUE(account.accountEvents->expectEvent(line, "SipAccountHandler::onError", fileName, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(account.handle, h);
      ASSERT_EQ(errorStr, evt.errorText) << evt.errorText;
   }
   static void expectAccountError(int line, const cpc::string& errorStr, TestAccount& account)
   {
      expectAccountErrorEx(line, errorStr, "", account);
   }
   #define assertAccountError(account, errorStr) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestAccountEvents::expectAccountError(__LINE__, errorStr, account); \
   }
   #define assertAccountErrorEx(account, errorStr) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestAccountEvents::expectAccountErrorEx(__LINE__, errorStr, __FILE__, account); \
   }
   #define assertAccountErrorEx2(account) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestAccountEvents::expectAccountErrorEx2(__LINE__, __FILE__, account); \
   }

   static void expectOnClientAuthEx(int line, const std::string& fileName, TestAccount& account, int port, int statusCode, bool willUpdate)
   {
      for (;;)
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountClientAuthEvent evt;
         ASSERT_TRUE(account.accountEvents->expectEvent(line, "SipAccountHandlerInternal::onClientAuth", fileName, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(account.handle, h) << "account handle mismatch: " << fileName << ":" << line;

         if ((evt.responseStatusCode == 408) || (evt.responseStatusCode == 503))
         {
            safeCout("Received internal response code " << evt.responseStatusCode << " continue waiting for response");
            continue;
         }

         ASSERT_EQ(statusCode, evt.responseStatusCode) << "status code mismatch: " << fileName << ":" << line;
         if (willUpdate)
         {
            ASSERT_TRUE(evt.willSendUpdatedRequest) << "true update request mismatch: " << fileName << ":" << line;
         }
         else
         {
            ASSERT_FALSE(evt.willSendUpdatedRequest) << "false update request mismatch: " << fileName << ":" << line;
         }
         ASSERT_EQ(port, evt.responseSourcePort) << "source port mismatch: " << fileName << ":" << line;
         break;
      }
   }
   static void expectOnClientAuth(int line, TestAccount& account, int port, int statusCode, bool willUpdate)
   {
      expectOnClientAuthEx(line, "", account, port, statusCode, willUpdate);
   }
   #define assertOnClientAuth(account, port, statusCode, willUpdate) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestAccountEvents::expectOnClientAuth(__LINE__, account, port, statusCode, willUpdate); \
   }
   #define assertOnClientAuthEx(account, port, statusCode, willUpdate) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestAccountEvents::expectOnClientAuthEx(__LINE__, __FILE__, account, port, statusCode, willUpdate); \
   }

   static void expectOnClientAuthUntilEx(int line, const std::string& fileName, TestAccount& account, int port, int statusCode, bool willUpdate)
   {
      for (;;)
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountClientAuthEvent evt;
         ASSERT_TRUE(account.accountEvents->expectEvent(line, "SipAccountHandlerInternal::onClientAuth", fileName, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(account.handle, h) << "account handle mismatch: " << fileName << ":" << line;

         if (evt.responseStatusCode != statusCode)
         {
            safeCout("Received client auth with response code " << evt.responseStatusCode << " continue waiting for another response");
            continue;
         }

         if (willUpdate != evt.willSendUpdatedRequest)
         {
            safeCout("Received client auth with willSendUpdateRequest " << evt.willSendUpdatedRequest << " continue waiting for another response");
            continue;
         }

         if (port != evt.responseSourcePort)
         {
            safeCout("Received client auth with source port " << evt.responseSourcePort << " continue waiting for another response");
            continue;
         }

         break;
      }
   }
   static void expectOnClientAuthUntil(int line, TestAccount& account, int port, int statusCode, bool willUpdate)
   {
      expectOnClientAuthUntilEx(line, "", account, port, statusCode, willUpdate);
   }
   #define assertOnClientAuthUntil(account, port, statusCode, willUpdate) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestAccountEvents::expectOnClientAuthUntil(__LINE__, account, port, statusCode, willUpdate); \
   }
   #define assertOnClientAuthUntilEx(account, port, statusCode, willUpdate) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestAccountEvents::expectOnClientAuthUntilEx(__LINE__, __FILE__, account, port, statusCode, willUpdate); \
   }

   static void expectOnDnsResetEx(int line, const std::string& fileName, TestAccount& account, int port, CPCAPI2::SipAccount::IpVersion version, CPCAPI2::SipAccount::SipAccountTransportType transport)
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountDnsResetEvent evt;
      ASSERT_TRUE(account.accountEvents->expectEvent(line, "SipAccountHandlerInternal::onDnsReset", fileName, 60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(account.handle, h) << "account handle mismatch: " << fileName << ":" << line;
      // ASSERT_EQ(port, evt.currentPort);
      // ASSERT_EQ(version, evt.currentIpVersion);
      // ASSERT_EQ(transport, evt.currentTransportType);
      ASSERT_EQ(port, evt.preferredPort) << "port mismatch: " << fileName << ":" << line;
      ASSERT_EQ(version, evt.preferredIpVersion) << "ip version mismatch: " << fileName << ":" << line;
      ASSERT_EQ(transport, evt.preferredTransportType) << "transport mismatch: " << fileName << ":" << line;
   }
   static void expectOnDnsReset(int line, TestAccount& account, int port, CPCAPI2::SipAccount::IpVersion version, CPCAPI2::SipAccount::SipAccountTransportType transport)
   {
      expectOnDnsResetEx(line, "", account, port, version, transport);
   }
   #define assertOnDnsReset(account, port, version, transport) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestAccountEvents::expectOnDnsReset(__LINE__, account, port, version, transport); \
   }
   #define assertOnDnsResetEx(account, port, version, transport) \
   { \
      SCOPED_TRACE((account).config.name); \
      TestAccountEvents::expectOnDnsResetEx(__LINE__, __FILE__, account, port, version, transport); \
   }
};

class MySipNetworkProbeHandler : public CPCAPI2::SipAccount::SipNetworkProbeHandler
{

public:

   MySipNetworkProbeHandler(TestAccount* account) : mAccount(account)
   {
      safeCout("MySipNetworkProbeHandler::MySipNetworkProbeHandler()");
   }

   virtual ~MySipNetworkProbeHandler()
   {
      safeCout("MySipNetworkProbeHandler::~MySipNetworkProbeHandler()");
      if (mAccount)
      {
         dynamic_cast<CPCAPI2::SipAccount::SipAccountManagerInternal*>(mAccount->account)->setProbeHandler(mAccount->handle, NULL);
      }
   }

   /** Will be triggered from the SDK thread. */
   virtual int onNetworkProbeStatusChanged(CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::SipAccount::SipNetworkProbeStatusChangedEvent& receivedEvent)
   {
      safeCout("MySipNetworkProbeHandler::onNetworkProbeStatusChanged(): Probe status: " << receivedEvent);
      std::lock_guard<std::mutex> lock(mEventMutex);
      mReceivedEvents.push_back(receivedEvent);
      mEventCondition.notify_all();
      return CPCAPI2::kSuccess;
   }

   /** Will be running on a seperate auto-test thread. */
   bool expectEvent(CPCAPI2::SipAccount::SipNetworkProbeStatusChangedEvent& expectedEvent, int waitTimeInMsecs, int line)
   {
      std::unique_lock<std::mutex> lock(mEventMutex);
      safeCout("network_change_auto_tests::expectEvent(): waiting for SipNetworkProbeStatusChangedEvent (" << expectedEvent.status << ":" << expectedEvent.reason << ") at line: " << line);

      std::chrono::time_point<std::chrono::system_clock> startTime = std::chrono::system_clock::now();
      std::chrono::duration<long long, std::milli> waitPeriod = std::chrono::milliseconds(0);
      do
      {
         std::deque<CPCAPI2::SipAccount::SipNetworkProbeStatusChangedEvent>::iterator it = mReceivedEvents.begin();
         while (it != mReceivedEvents.end())
         {
            CPCAPI2::SipAccount::SipNetworkProbeStatusChangedEvent receivedEvent = (*it);

            if ((receivedEvent.status == expectedEvent.status) && (receivedEvent.reason == expectedEvent.reason))
            {
               safeCout("network_change_auto_tests::expectEvent(): received SipNetworkProbeStatusChangedEvent (" << receivedEvent.status << ":" << receivedEvent.reason << ") at line: " << line);
               expectedEvent = receivedEvent;

               it = mReceivedEvents.erase(it);

               return true;
            }

            safeCout("network_change_auto_tests::expectEvent(): received SipNetworkProbeStatusChangedEvent (" << receivedEvent.status << ":"
                     << receivedEvent.reason << ") while expecting (" << expectedEvent.status << ":" << expectedEvent.reason << ") at line: " << line);

            ++it;
         }

         std::chrono::time_point<std::chrono::system_clock> receivedTime = std::chrono::system_clock::now();
         waitPeriod = std::chrono::duration_cast<std::chrono::milliseconds>(receivedTime - startTime);
         if (waitPeriod >= std::chrono::milliseconds(waitTimeInMsecs))
         {
            safeCout("network_change_auto_tests::expectEvent(): missed SipNetworkProbeStatusChangedEvent as wait period has reached limit (" << expectedEvent.status << ":" << expectedEvent.reason << ") at line: " << line);
            break;
         }
      } while (mEventCondition.wait_for(lock, std::chrono::milliseconds(waitTimeInMsecs) - waitPeriod) == std::cv_status::no_timeout);

      safeCout("network_change_auto_tests::expectEvent(): missed SipNetworkProbeStatusChangedEvent (" << expectedEvent.status << ":" << expectedEvent.reason << ") at line: " << line);
      return false;
   }

private:

   MySipNetworkProbeHandler() : mAccount(NULL) {}
   std::deque<CPCAPI2::SipAccount::SipNetworkProbeStatusChangedEvent> mReceivedEvents;
   std::condition_variable mEventCondition;
   std::mutex mEventMutex;
   TestAccount* mAccount;

};

#define EXPECT_PROBE_EVENT(HANDLER, PROBE_EVENT, WAIT_TIME_IN_MSECS) HANDLER->expectEvent(PROBE_EVENT, WAIT_TIME_IN_MSECS, __LINE__)

class MyDialogDnsResultHandler : public CPCAPI2::SipAccount::DialogDnsResultHandler
{

public:

   MyDialogDnsResultHandler(TestAccount* account) : mAccount(account)
   {
      safeCout("MyDialogDnsResultHandler::MyDialogDnsResultHandler()");
   }

   virtual ~MyDialogDnsResultHandler()
   {
      safeCout("MyDialogDnsResultHandler::~MyDialogDnsResultHandler()");
      if (mAccount)
      {
         dynamic_cast<CPCAPI2::SipAccount::SipAccountManagerInternal*>(mAccount->account)->setDnsHandler(mAccount->handle, NULL);
      }
   }

   /** Will be triggered from the SDK thread. */
   virtual int onDialogDnsResetStatus(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::DnsResetStatusChangedEvent& args)
   {
      safeCout("MyDialogDnsResultHandler::onDialogDnsResetStatus(): DNS reset status: " << args.status);
      std::lock_guard<std::mutex> lock(mEventMutex);
      mReceivedEvents.push_back(args);
      mEventCondition.notify_all();
      return CPCAPI2::kSuccess;
   }

   /** Will be running on a seperate auto-test thread. */
   bool expectEvent(CPCAPI2::SipAccount::DnsResetStatusChangedEvent& expectedEvent, int waitTimeInMsecs, const std::string& fileName, int line)
   {
      std::unique_lock<std::mutex> lock(mEventMutex);
      safeCout("MyDialogDnsResultHandler::expectEvent(): waiting for DnsResetStatusChangedEvent (" << expectedEvent.status << ") at " << fileName << ":" << line);

      std::chrono::time_point<std::chrono::system_clock> startTime = std::chrono::system_clock::now();
      std::chrono::duration<long long, std::milli> waitPeriod = std::chrono::milliseconds(0);
      do
      {
         std::deque<CPCAPI2::SipAccount::DnsResetStatusChangedEvent>::iterator it = mReceivedEvents.begin();
         while (it != mReceivedEvents.end())
         {
            CPCAPI2::SipAccount::DnsResetStatusChangedEvent receivedEvent = (*it);

            if (receivedEvent.status == expectedEvent.status)
            {
               safeCout("MyDialogDnsResultHandler::expectEvent(): received DnsResetStatusChangedEvent (" << receivedEvent.status << ") at " << fileName << ":" << line);
               expectedEvent = receivedEvent;

               it = mReceivedEvents.erase(it);

               return true;
            }

            safeCout("MyDialogDnsResultHandler::expectEvent(): received DnsResetStatusChangedEvent (" << receivedEvent.status << ") while expecting (" << expectedEvent.status << ") at " << fileName << ":" << line);

            ++it;
         }

         std::chrono::time_point<std::chrono::system_clock> receivedTime = std::chrono::system_clock::now();
         waitPeriod = std::chrono::duration_cast<std::chrono::milliseconds>(receivedTime - startTime);
         if (waitPeriod >= std::chrono::milliseconds(waitTimeInMsecs))
         {
            safeCout("MyDialogDnsResultHandler::expectEvent(): missed DnsResetStatusChangedEvent as wait period has reached limit (" << expectedEvent.status << ") at " << fileName << ":" << line);
            break;
         }
      } while (mEventCondition.wait_for(lock, std::chrono::milliseconds(waitTimeInMsecs) - waitPeriod) == std::cv_status::no_timeout);

      safeCout("MyDialogDnsResultHandler::expectEvent(): missed DnsResetStatusChangedEvent (" << expectedEvent.status << ") at " << fileName << ":" << line);
      return false;
   }

private:

   MyDialogDnsResultHandler() : mAccount(NULL) {}
   std::deque<CPCAPI2::SipAccount::DnsResetStatusChangedEvent> mReceivedEvents;
   std::condition_variable mEventCondition;
   std::mutex mEventMutex;
   TestAccount* mAccount;

};

#define EXPECT_DNS_RESET_STATUS_EVENT(HANDLER, DNS_RESET_STATUS_EVENT, WAIT_TIME_IN_MSECS) HANDLER->expectEvent(DNS_RESET_STATUS_EVENT, WAIT_TIME_IN_MSECS, __FILE__, __LINE__)

#endif //TEST_ACCOUNT_EVENTS_H
