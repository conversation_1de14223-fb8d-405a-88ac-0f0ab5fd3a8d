#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#if __linux__
#include <unistd.h>
#define Sleep( X ) usleep(( useconds_t )( X * 1000 ))
#endif

#include "cpcapi2_test_fixture.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>
#include <boost/algorithm/string.hpp>

#include "test_framework/cpcapi2_test_framework.h"
#include "test_framework/bare_bones_sip_endpoint.h"
#include "test_call_events.h"
#include "phone/PhoneInterface.h"

#define WEBRTC_INCLUDE_INTERNAL_AUDIO_DEVICE 1
#include "webrtc/modules/audio_device/audio_device_impl.h"

#include "media/AudioInterface.h"

#include <thread>
#include <future>
#include <string.h>
#include <regex>

#ifdef ANDROID
#include "../common/Java/JniHelper.h"
#endif

#if defined (__APPLE__) && !(TARGET_OS_IPHONE)
#include "webrtc/modules/audio_device/mac/audio_utils_mac.h"
#endif // #if defined (__APPLE__) && !(TARGET_OS_IPHONE)


using namespace CPCAPI2;
using namespace CPCAPI2::Media;
using namespace CPCAPI2::SipConversation;
using namespace CPCAPI2::test;

namespace {

class AudioModuleTest : public CpcapiAutoTest
{
public:
   AudioModuleTest() {}
   virtual ~AudioModuleTest() {}
};

bool audioCodecInfoMatches(const AudioCodecInfo& a, const AudioCodecInfo& b)
{
   if (a.codecName == b.codecName &&
       a.id == b.id &&
       a.enabled == b.enabled &&
       a.samplingRate == b.samplingRate &&
       a.minBandwidth == b.minBandwidth &&
       a.maxBandwidth == b.maxBandwidth &&
       a.priority == b.priority &&
       a.payloadType == b.payloadType)
    {
      return true;
    }
   
    return false;
}

int playSound(Audio* audio, AudioDeviceRole role, const cpc::string& resourceUri, bool repeat = false)
{
   // we need to call the version of playSound that takes an audio usage, so it will work on Android

   const int audioUsage = 3;
   return static_cast<AudioInterface*>(audio)->playSound(role, audioUsage, resourceUri, repeat);
}


TEST_F(AudioModuleTest, TestReInitMediaStack) {

   TestAccount alice("alice");

   // basic initial sanity
   CPCAPI2::Media::AudioCodecListUpdatedEvent evt;
   int handle = 0;
   alice.audio->queryCodecList();
   ASSERT_TRUE(alice.mediaEvents->expectEvent("AudioHandler::onAudioCodecListUpdated", 30000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
   ASSERT_NE(evt.codecInfo.size(), 0);
   cpc::vector<AudioCodecInfo> firstCodecList = evt.codecInfo;

   ASSERT_EQ(alice.audio->queryCodecList(), kSuccess);
   ASSERT_TRUE(alice.mediaEvents->expectEvent("AudioHandler::onAudioCodecListUpdated", 30000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
   ASSERT_NE(evt.codecInfo.size(), 0);
   cpc::vector<AudioCodecInfo> secondCodecList = evt.codecInfo;

   // ensure codec list is still the same
   ASSERT_EQ(secondCodecList.size(), firstCodecList.size());
   for (int i = 0; i < secondCodecList.size(); ++i)
   {
      ASSERT_TRUE(audioCodecInfoMatches(secondCodecList[i], firstCodecList[i]));
   }


   // now re-init the media stack
   MediaStackSettings mediaStackSettings;
#ifdef _WIN32
   mediaStackSettings.audioLayer = AudioLayers_WindowsWave;
#else
   mediaStackSettings.audioLayer = AudioLayers_PlatformDefault;
#endif
   alice.media->updateMediaSettings(mediaStackSettings);

   // ... and see if the same sanity test works
   ASSERT_EQ(alice.audio->queryCodecList(), kSuccess);
   ASSERT_TRUE(alice.mediaEvents->expectEvent("AudioHandler::onAudioCodecListUpdated", 30000, CPCAPI2::test::AlwaysTruePred(), handle, evt));

   ASSERT_NE(evt.codecInfo.size(), 0);
   cpc::vector<AudioCodecInfo> thirdCodecList = evt.codecInfo;
   
   // ensure codec list is still the same
   
   // TODO: the codec list order may not be the same:
   // - some codecs (e.g. speex) do not define a relative quality difference across different sampling rates
   // - some codecs (e.g. pcmu, pcma) define the same relative quality difference
   // with this, webrtc_recon::CodecComparator order of codecs can sometimes differ
   //ASSERT_EQ(secondCodecList.size(), thirdCodecList.size());
   //for (int i = 0; i < secondCodecList.size(); ++i)
   //{
   //   ASSERT_TRUE(audioCodecInfoMatches(secondCodecList[i], thirdCodecList[i]));
   //}

   TestAccount bob("bob");

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor2(aliceEvents, bobEvents);
}

TEST_F(AudioModuleTest, CodecEnableDisable) {

   TestAccount alice("alice", Account_Init);

   CPCAPI2::Media::AudioCodecListUpdatedEvent evt;
   int handle = 0;
   alice.audio->queryCodecList();
   ASSERT_TRUE(alice.mediaEvents->expectEvent("AudioHandler::onAudioCodecListUpdated", 30000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
   ASSERT_NE(evt.codecInfo.size(), 0);

   alice.audio->setCodecEnabled(evt.codecInfo[0].id, false);
   ASSERT_EQ(alice.audio->queryCodecList(), kSuccess);
   ASSERT_TRUE(alice.mediaEvents->expectEvent("AudioHandler::onAudioCodecListUpdated", 30000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
   ASSERT_NE(evt.codecInfo.size(), 0);
   ASSERT_EQ(evt.codecInfo[0].enabled, false);
}


TEST_F(AudioModuleTest, CodecPriority) {

   TestAccount alice("alice", Account_Init);

   CPCAPI2::Media::AudioCodecListUpdatedEvent evt;
   int handle = 0;
   alice.audio->queryCodecList();
   ASSERT_TRUE(alice.mediaEvents->expectEvent("AudioHandler::onAudioCodecListUpdated", 30000, CPCAPI2::test::AlwaysTruePred(), handle, evt));

   size_t size = evt.codecInfo.size();
   ASSERT_NE(0, size);

   safeCout("Before ");
   for(cpc::vector<AudioCodecInfo>::const_iterator it = evt.codecInfo.begin(); it != evt.codecInfo.end(); it++)
   {
      safeCout("\t" << "id=" << it->id << " " << (it->codecName) << "  priority=" << it->priority);
   }

   unsigned int priorityId = evt.codecInfo[0].id;
   unsigned int priority = 10;
   alice.audio->setCodecPriority(priorityId, priority);
   ASSERT_EQ(alice.audio->queryCodecList(), kSuccess);
   ASSERT_TRUE(alice.mediaEvents->expectEvent("AudioHandler::onAudioCodecListUpdated", 30000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
   ASSERT_EQ(size, evt.codecInfo.size());

   safeCout("After ");
   for(cpc::vector<AudioCodecInfo>::const_iterator it = evt.codecInfo.begin(); it != evt.codecInfo.end(); it++)
   {
      safeCout("\t" << "id=" << it->id << " " << (it->codecName)
            << "  priority=" << it->priority);
   }

   ASSERT_EQ(priorityId, evt.codecInfo[size-1].id);
   ASSERT_EQ(priority, evt.codecInfo[size-1].priority);
}

TEST_F(AudioModuleTest, PlayDTMFTones) {

   TestAccount alice("alice", Account_Init);
   
   // onPlaySoundComplete event not compatible with EventSource
   
   safeCout("Put on your headset and listen for DTMF Tones...");
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   { // manually stop playing
      int clip = playSound(alice.audio, CPCAPI2::Media::AudioDeviceRole_Headset, "tone:0;duration=10000");
      std::this_thread::sleep_for(std::chrono::milliseconds(3000));
      alice.audio->stopPlaySound(clip);

      PlaySoundHandle completedHandle;
      int dummy = 0; // to satisfy our eventing test framework, since this event only has 1 param
      ASSERT_TRUE(alice.mediaEvents->expectEvent("AudioHandler::onPlaySoundComplete", 30000, CPCAPI2::test::AlwaysTruePred(), dummy, completedHandle));
      ASSERT_EQ(clip, completedHandle);
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
}

TEST_F(AudioModuleTest, PlayRepeatDTMFTones)
{
   {
      TestAccount alice("alice", Account_Init);

      for (int i = 0; i < 20; ++i)
      {
         int clipId = playSound(alice.audio, CPCAPI2::Media::AudioDeviceRole_Headset, "tone:32", true);
         std::this_thread::sleep_for(std::chrono::milliseconds(500));
         alice.audio->stopPlaySound(clipId);
         std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      }
   }
   
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));
}

TEST_F(AudioModuleTest, PlaySimultaneousDTMFTones) {

   TestAccount alice("alice");

   safeCout("Put on your headset and listen for DTMF Tones...");
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   { // manually stop playing
      int clip = playSound(alice.audio, CPCAPI2::Media::AudioDeviceRole_Headset, "tone:0;duration=10000");
      int clip2 = playSound(alice.audio, CPCAPI2::Media::AudioDeviceRole_Headset, "tone:1;duration=10000");
      alice.audio->stopPlaySound(clip);
      alice.audio->stopPlaySound(clip2);

      PlaySoundHandle completedHandle;
      int dummy = 0; // to satisfy our eventing test framework, since this event only has 1 param
      ASSERT_TRUE(alice.mediaEvents->expectEvent("AudioHandler::onPlaySoundComplete", 30000, CPCAPI2::test::AlwaysTruePred(), dummy, completedHandle));
      ASSERT_EQ(clip, completedHandle);

      ASSERT_TRUE(alice.mediaEvents->expectEvent("AudioHandler::onPlaySoundComplete", 30000, CPCAPI2::test::AlwaysTruePred(), dummy, completedHandle));
      ASSERT_EQ(clip2, completedHandle);
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   { // automatically stop playing
      int clip = playSound(alice.audio, CPCAPI2::Media::AudioDeviceRole_Headset, "tone:0;duration=3000");
      int clip2 = playSound(alice.audio, CPCAPI2::Media::AudioDeviceRole_Headset, "tone:1;duration=4000");

      PlaySoundHandle completedHandle;
      int dummy = 0; // to satisfy our eventing test framework, since this event only has 1 param
      ASSERT_TRUE(alice.mediaEvents->expectEvent("AudioHandler::onPlaySoundComplete", 30000, CPCAPI2::test::AlwaysTruePred(), dummy, completedHandle));
      ASSERT_EQ(clip, completedHandle);

      ASSERT_TRUE(alice.mediaEvents->expectEvent("AudioHandler::onPlaySoundComplete", 30000, CPCAPI2::test::AlwaysTruePred(), dummy, completedHandle));
      ASSERT_EQ(clip2, completedHandle);
   }
}

TEST_F(AudioModuleTest, PlayExtendedDTMFTones) {

   TestAccount alice("alice", Account_Init);
   // onPlaySoundComplete event not compatible with EventSource
   
   safeCout("Put on your headset and listen for DTMF Tones...");
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   { // manually stop playing
      int clip = playSound(alice.audio, CPCAPI2::Media::AudioDeviceRole_Headset, "tone:31;duration=10000");
      std::this_thread::sleep_for(std::chrono::milliseconds(3000));
      alice.audio->stopPlaySound(clip);

      PlaySoundHandle completedHandle;
      int dummy = 0; // to satisfy our eventing test framework, since this event only has 1 param
      ASSERT_TRUE(alice.mediaEvents->expectEvent("AudioHandler::onPlaySoundComplete", 30000, CPCAPI2::test::AlwaysTruePred(), dummy, completedHandle));
      ASSERT_EQ(clip, completedHandle);
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   { // automatically stop playing
      int clip = playSound(alice.audio, CPCAPI2::Media::AudioDeviceRole_Headset, "tone:31;duration=3000");

      PlaySoundHandle completedHandle;
      int dummy = 0; // to satisfy our eventing test framework, since this event only has 1 param
      ASSERT_TRUE(alice.mediaEvents->expectEvent("AudioHandler::onPlaySoundComplete", 30000, CPCAPI2::test::AlwaysTruePred(), dummy, completedHandle));
      ASSERT_EQ(clip, completedHandle);
   }
}

TEST_F(AudioModuleTest, PlayDifferentTones) {

   TestAccount alice("alice", Account_Init);

   safeCout("Put on your headset and listen for DTMF Tones...");
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   { // manually stop playing the first tone
      int clip = playSound(alice.audio, CPCAPI2::Media::AudioDeviceRole_Headset, "tone:0;duration=10000");
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      alice.audio->stopPlaySound(clip);

      PlaySoundHandle completedHandle;
      int dummy = 0; // to satisfy our eventing test framework, since this event only has 1 param
      ASSERT_TRUE(alice.mediaEvents->expectEvent("AudioHandler::onPlaySoundComplete", 30000, CPCAPI2::test::AlwaysTruePred(), dummy, completedHandle));
      ASSERT_EQ(clip, completedHandle);
   }

   { // manually stop playing the second tone
      int clip = playSound(alice.audio, CPCAPI2::Media::AudioDeviceRole_Headset, "tone:31;duration=10000");
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      alice.audio->stopPlaySound(clip);

      PlaySoundHandle completedHandle;
      int dummy = 0; // to satisfy our eventing test framework, since this event only has 1 param
      ASSERT_TRUE(alice.mediaEvents->expectEvent("AudioHandler::onPlaySoundComplete", 30000, CPCAPI2::test::AlwaysTruePred(), dummy, completedHandle));
      ASSERT_EQ(clip, completedHandle);
   }
}

TEST_F(AudioModuleTest, PlayWavFile) {

   TestAccount alice("alice", Account_Init);

   safeCout("Put on your headset and listen for Wav sound...");
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   { // manually stop playing
      PlaySoundHandle clip = playSound(alice.audio, CPCAPI2::Media::AudioDeviceRole_Headset, (TestEnvironmentConfig::testResourcePath() + "test.wav").c_str(), true);
      std::this_thread::sleep_for(std::chrono::milliseconds(3000));
      alice.audio->stopPlaySound(clip);

      PlaySoundHandle completedHandle;
      int dummy = 0; // to satisfy our eventing test framework, since this event only has 1 param
      ASSERT_TRUE(alice.mediaEvents->expectEvent("AudioHandler::onPlaySoundComplete", 30000, CPCAPI2::test::AlwaysTruePred(), dummy, completedHandle));
      ASSERT_EQ(clip, completedHandle);
   }

   { // automatically stop playing
      PlaySoundHandle clip = playSound(alice.audio, CPCAPI2::Media::AudioDeviceRole_Headset, (TestEnvironmentConfig::testResourcePath() + "test.wav").c_str());

      PlaySoundHandle completedHandle;
      int dummy = 0; // to satisfy our eventing test framework, since this event only has 1 param
      ASSERT_TRUE(alice.mediaEvents->expectEvent("AudioHandler::onPlaySoundComplete", 30000, CPCAPI2::test::AlwaysTruePred(), dummy, completedHandle));
      ASSERT_EQ(clip, completedHandle);
   }
}

#if _WIN32
TEST_F(AudioModuleTest, PlayWavFile44kHz) {

   TestAccount alice("alice", Account_Init);

   safeCout("Put on your headset and listen for Wav sound...");
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   { // manually stop playing
      PlaySoundHandle clip = playSound(alice.audio, CPCAPI2::Media::AudioDeviceRole_Headset, (TestEnvironmentConfig::testResourcePath() + "testA-44khz.wav").c_str(), true);
      std::this_thread::sleep_for(std::chrono::milliseconds(3000));
      alice.audio->stopPlaySound(clip);

      PlaySoundHandle completedHandle;
      int dummy = 0; // to satisfy our eventing test framework, since this event only has 1 param
      ASSERT_TRUE(alice.mediaEvents->expectEvent("AudioHandler::onPlaySoundComplete", 30000, CPCAPI2::test::AlwaysTruePred(), dummy, completedHandle));
      ASSERT_EQ(clip, completedHandle);
   }

   //{ // automatically stop playing
   //   PlaySoundHandle clip = alice.audio->playSound(CPCAPI2::Media::AudioDeviceRole_Headset, (TestEnvironmentConfig::testResourcePath() + "testA-44khz.wav").c_str());

   //   auto aliceEvents = std::async(std::launch::async, [&]() {
   //      alice.media->process(MediaManager::kBlockingModeInfinite);
   //   });

   //   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
   //   safeCout("clip id: " << testAudioHandler->completedClip);
   //   ASSERT_EQ(testAudioHandler->completedClip, clip);
   //}
}
#endif

TEST_F(AudioModuleTest, PlayManyWavFile) {

   TestAccount alice("alice", Account_Init);

   safeCout("Put on your headset and listen for Wav sound...");
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   for (int i = 0; i < 1; ++i)
   {
      PlaySoundHandle clip = playSound(alice.audio, CPCAPI2::Media::AudioDeviceRole_Headset, (TestEnvironmentConfig::testResourcePath() + "test.wav").c_str());

      PlaySoundHandle completedHandle;
      int dummy = 0; // to satisfy our eventing test framework, since this event only has 1 param
      ASSERT_TRUE(alice.mediaEvents->expectEvent("AudioHandler::onPlaySoundComplete", 30000, CPCAPI2::test::AlwaysTruePred(), dummy, completedHandle));
      ASSERT_EQ(clip, completedHandle);
   }
}


#ifdef ANDROID
TEST_F(AudioModuleTest, PlayManyWavFile_Android_RawResource) {

   TestAccount alice("alice", Account_Init);

   safeCout("Put on your headset and listen for Wav sound...");
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   for (int i = 0; i < 100; ++i)
   {
      PlaySoundHandle clip = playSound(alice.audio, CPCAPI2::Media::AudioDeviceRole_Headset, "android.resource://com.counterpath.sdkdemo.advancedaudiocall/raw/new_im");

      std::this_thread::sleep_for(std::chrono::milliseconds(200));
      safeCout("Iteration " << i);
   }
}
#endif

TEST_F(AudioModuleTest, PlaySeqTonesInCall) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall, alice.config.uri());
   bob.conversation->start(bobCall);

   auto bobConversationEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(bob, bobCall, alice.config.uri());
      assertConversationStateChanged(bob, bobCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      bob.conversation->end(bobCall);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto aliceConversationEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle aliceCall;
      assertNewConversationIncoming(alice, &aliceCall, bob.config.uri());
      alice.conversation->sendRingingResponse(aliceCall);
      assertConversationStateChanged(alice, aliceCall, ConversationState_LocalRinging);
      alice.conversation->accept(aliceCall);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      alice.conversation->playSound(aliceCall, "seq:silence=1000;tone=850,850,200;silence=200;tone=850,850,200;silence=1000;silence=4000", true);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor2(bobConversationEvents, aliceConversationEvents);
}

TEST_F(AudioModuleTest, MultipleFastPlayEndWavFile){
   TestAccount alice("alice");
   
   safeCout("Put on your headset and listen for Wav sound...");
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));
   PlaySoundHandle completedHandle;
   int dummy = 0; // to satisfy our eventing test framework, since this event only has 1 param
      
   for (int i = 0;i<100;i++)
   {
      PlaySoundHandle clip = playSound(alice.audio, CPCAPI2::Media::AudioDeviceRole_Ringing, (TestEnvironmentConfig::testResourcePath() + "test.wav").c_str(), false);
      std::this_thread::sleep_for(std::chrono::milliseconds(100));
      alice.audio->stopPlaySound(clip);

      ASSERT_TRUE(alice.mediaEvents->expectEvent("AudioHandler::onPlaySoundComplete", 30000, CPCAPI2::test::AlwaysTruePred(), dummy, completedHandle));
      ASSERT_EQ(clip, completedHandle);
   }
   ASSERT_FALSE(alice.mediaEvents->expectEvent("AudioHandler::onPlaySoundFailure", 1000, CPCAPI2::test::AlwaysTruePred(), dummy, completedHandle));
}

class AudioCallback : public webrtc::AudioTransport
{
private:
   uint8_t* mSamples = nullptr;
   uint32_t mSamplesSize = 0;
   uint32_t mRecordPosition = 0;
   uint32_t mPlaybackPosition = 0;
   uint32_t mSeconds = 0;

public:
   AudioCallback(uint8_t seconds)
   {
      mSeconds = seconds;
   }

   virtual ~AudioCallback()
   {
      delete mSamples;
      mSamples = nullptr;
   }

   virtual int32_t RecordedDataIsAvailable(const void* audioSamples,
                                           const uint32_t nSamples,
                                           const uint8_t nBytesPerSample,
                                           const uint8_t nChannels,
                                           const uint32_t samplesPerSec,
                                           const uint32_t totalDelayMS,
                                           const int32_t clockDrift,
                                           const uint32_t currentMicLevel,
                                           const bool keyPressed,
                                           uint32_t& newMicLevel)
   {
      if (nullptr == mSamples)
      {
         safeCout("Starting to record for " << mSeconds << " seconds");
         mSamplesSize = nBytesPerSample * samplesPerSec * mSeconds;
         mSamples = new uint8_t[mSamplesSize];
      }

      if (mSamplesSize > mRecordPosition)
      {
         if (mRecordPosition + (nBytesPerSample * nSamples) > mSamplesSize)
         {
            // buffer overrun
            return 0;
         }
      
         memcpy(&mSamples[mRecordPosition], audioSamples, nBytesPerSample * nSamples);
         mRecordPosition += nBytesPerSample * nSamples;
      }

      return 0;
   }

   virtual int32_t NeedMorePlayData(const uint32_t nSamples,
                                    const uint8_t nBytesPerSample,
                                    const uint8_t nChannels,
                                    const uint32_t samplesPerSec,
                                    void* audioSamples,
                                    uint32_t& nSamplesOut,
                                    int64_t* elapsed_time_ms,
                                    int64_t* ntp_time_ms)
   {
      if (0 == mPlaybackPosition)
      {
         safeCout("Starting to playback for " << mSeconds << " seconds");
      }

      if (mSamplesSize > mPlaybackPosition)
      {
         if (mPlaybackPosition + (nBytesPerSample * nSamples) > mSamplesSize)
         {
            // buffer overrun
            nSamplesOut = 0;
            return 0;
         }

         memcpy(audioSamples, &mSamples[mPlaybackPosition], nBytesPerSample * nSamples);
         mPlaybackPosition += nBytesPerSample * nSamples;
         nSamplesOut = nSamples;
      }

      return 0;
   }
};

TEST_F(AudioModuleTest, TestPlatformDefaultAudioDevice) {
   // Uncomment for WebRTC logging
   //TestAccount alice("alice", Account_Init);
   
#if _WIN32
   // no audio system devices currently available with our docker windows environment
   if (TestEnvironmentConfig::dockerContainerized()) GTEST_SKIP() << "Test not supported in docker windows environments";
#endif

   uint8_t number_of_seconds = 5;

   webrtc::AudioDeviceModule* audioDevice = webrtc::AudioDeviceModuleImpl::Create(0, webrtc::AudioDeviceModule::AudioLayer::kPlatformDefaultAudio);
   audioDevice->Init();

   AudioCallback* audioCallback = new AudioCallback(number_of_seconds);
   ASSERT_EQ(0, audioDevice->RegisterAudioCallback(audioCallback));

   ASSERT_EQ(0, audioDevice->SetRecordingDevice(0));
   ASSERT_EQ(0, audioDevice->InitRecording());
   ASSERT_EQ(0, audioDevice->StartRecording());
   std::this_thread::sleep_for(std::chrono::seconds(number_of_seconds));
   ASSERT_EQ(0, audioDevice->StopRecording());

   ASSERT_EQ(0, audioDevice->SetPlayoutDevice(0));
   ASSERT_EQ(0, audioDevice->InitPlayout());
   ASSERT_EQ(0, audioDevice->StartPlayout());
   std::this_thread::sleep_for(std::chrono::seconds(number_of_seconds));
   ASSERT_EQ(0, audioDevice->StopPlayout());

   ASSERT_EQ(0, audioDevice->Terminate());
   delete audioCallback;
}

bool RepeatQueryDeviceVolume_BadLogParseFunction(const char *message, CPCAPI2::LogLevel)
{
   bool result = false;
   if (NULL != strstr(message, "GetSpeakerVolume()")) result = true;
   else if (NULL != strstr(message, "output: volume=")) result = true;
   else if (NULL != strstr(message, "output: maxVolume=")) result = true;
   else if (NULL != strstr(message, "GetMicVolume()")) result = true;
   return result;
}

TEST_F(AudioModuleTest, RepeatQueryDeviceVolume) {

   AutoTestsLogger::ScopedSetBadLogMessageCheckFunction blm(&RepeatQueryDeviceVolume_BadLogParseFunction);

   TestAccount alice("alice", Account_Init);
   
   for (int i = 0; i < 10; ++i)
   {
      alice.audio->queryDeviceVolume();
      CPCAPI2::Media::AudioDeviceVolumeEvent evt;
      int handle = 0;
      ASSERT_TRUE(alice.mediaEvents->expectEvent("AudioHandler::onAudioDeviceVolume", 30000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
      
      std::this_thread::sleep_for(std::chrono::seconds(1));
   }
   
   ASSERT_EQ(AutoTestsLogger::instance().getBadMessagesCount(), 0);

}

TEST_F(AudioModuleTest, SetAudioDevices) {

   TestAccount alice("alice");
   
   CPCAPI2::Media::AudioDeviceListUpdatedEvent evt;
   int handle = 0;
   ASSERT_EQ(alice.audio->queryDeviceList(), kSuccess);
   ASSERT_TRUE(alice.mediaEvents->expectEvent("AudioHandler::onAudioDeviceListUpdated", 30000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
   
   for (cpc::vector<AudioDeviceInfo>::const_iterator it = evt.deviceInfo.begin(); it != evt.deviceInfo.end(); ++it)
   {
      if (it->deviceType == Media::MediaDeviceType_Capture)
      {
         alice.audio->setCaptureDevice(it->id, it->role);
         break;
      }
   }
   
   for (cpc::vector<AudioDeviceInfo>::const_iterator it = evt.deviceInfo.begin(); it != evt.deviceInfo.end(); ++it)
   {
      if (it->deviceType == Media::MediaDeviceType_Render)
      {
         alice.audio->setRenderDevice(it->id, it->role);
         break;
      }
   }
   
   std::this_thread::sleep_for(std::chrono::seconds(10));
}

#if defined (__APPLE__) && !(TARGET_OS_IPHONE)
// OBELISK-6160
TEST_F(AudioModuleTest, QueryAudioDevices_NoDefaultAudioDevice_Mac) {

   TestAccount alice("alice");
   
   CPCAPI2::Media::AudioDeviceListUpdatedEvent evt;
   int handle = 0;
   ASSERT_EQ(alice.audio->queryDeviceList(), kSuccess);
   ASSERT_TRUE(alice.mediaEvents->expectEvent("AudioHandler::onAudioDeviceListUpdated", 30000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
   
   for (cpc::vector<AudioDeviceInfo>::const_iterator it = evt.deviceInfo.begin(); it != evt.deviceInfo.end(); ++it)
   {
      std::regex self_regex("default (.*)",
              std::regex_constants::ECMAScript | std::regex_constants::icase);
      
      ASSERT_FALSE(std::regex_search(it->friendlyName.c_str(), self_regex)) << "Device '" << it->friendlyName << "' should have been filtered out by the SDK";
   }
}
#endif // #if defined (__APPLE__) && !(TARGET_OS_IPHONE)

TEST_F(AudioModuleTest, UpdateMediaSettings_SwitchAudioLayer) {

   TestAccount alice("alice");

   PlaySoundHandle clip = playSound(alice.audio, CPCAPI2::Media::AudioDeviceRole_Headset, (TestEnvironmentConfig::testResourcePath() + "test.wav").c_str());

   PlaySoundHandle completedHandle;
   int dummy = 0; // to satisfy our eventing test framework, since this event only has 1 param
   ASSERT_TRUE(alice.mediaEvents->expectEvent("AudioHandler::onPlaySoundComplete", 30000, CPCAPI2::test::AlwaysTruePred(), dummy, completedHandle));
   ASSERT_EQ(clip, completedHandle);
   
   MediaStackSettings settings = alice.config.mediaSettings;
   settings.audioLayer = AudioLayers_Dummy;
   alice.media->updateMediaSettings(settings);

   // ensure playSound still works after switching audio layers
   
   clip = playSound(alice.audio, CPCAPI2::Media::AudioDeviceRole_Headset, (TestEnvironmentConfig::testResourcePath() + "test.wav").c_str());

   ASSERT_TRUE(alice.mediaEvents->expectEvent("AudioHandler::onPlaySoundComplete", 30000, CPCAPI2::test::AlwaysTruePred(), dummy, completedHandle));
   ASSERT_EQ(clip, completedHandle);
}

 class SetAudioDevices_LogParseFunction
   {
   public:
      SetAudioDevices_LogParseFunction() : mFoundWebRtcLog(false)
      {
      }
      
      void loggingListener(const char *message, const char* subsystem, CPCAPI2::LogLevel level)
      {
         if (boost::trim_left_copy(std::string(subsystem)) == "MEDIA_STACK")
         {
            if (std::string(message).find("; SetPlayoutDevice(index=") != std::string::npos)
            {
               mFoundWebRtcLog = true;
            }
         }
      }
      
      std::atomic_bool mFoundWebRtcLog;
   };

   // OBELISK-6046
   // child phone clobbers parent phone's WebRTC logging enablement
   // such that after the child phone is initialized, no further WebRTC
   // logging is received by the parent phone.
   TEST_F(AudioModuleTest, SetAudioDevices_ChildPhoneLogging)
   {
      SetAudioDevices_LogParseFunction lpf;
   
      std::function<void(const char *message, const char* subsystem, CPCAPI2::LogLevel level)> f = std::bind(&SetAudioDevices_LogParseFunction::loggingListener,
                                                                                                         &lpf, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);
   
      AutoTestsLogger::ScopedMessageListenerFunction smlf(f);
   
      TestAccount alice("alice", Account_Init);
      
      PhoneInternal* alicePhoneInternal = static_cast<PhoneInternal*>(alice.phone);
      
      // would fail -- mOwnSdkLoggerThread set to true in the child phone
      //PhoneInternal* aliceChildPhoneInternal = PhoneInternal::create(alicePhoneInternal);
      
      PhoneInternal* aliceChildPhoneInternal = CPCAPI2::PhoneInterface::create(alicePhoneInternal, &((dynamic_cast<PhoneInterface*>(alice.phone))->getSdkLoggerThread()));
      
      LicenseInfo li;
      aliceChildPhoneInternal->initialize(li, static_cast<PhoneHandler*>(NULL));
      
      std::this_thread::sleep_for(std::chrono::seconds(5));
            
      ASSERT_FALSE(lpf.mFoundWebRtcLog);
      
      alice.audio->setRenderDevice(CPCAPI2::Media::kAudioDefaultSystemDeviceId, CPCAPI2::Media::AudioDeviceRole_Headset);
      
      std::this_thread::sleep_for(std::chrono::seconds(5));
      
      ASSERT_TRUE(lpf.mFoundWebRtcLog);
   }

#if defined (__APPLE__) && !(TARGET_OS_IPHONE)
 
TEST_F(AudioModuleTest, MacBookModelParse_Mac_Small)
{
   int outMajorVersion;
   ASSERT_EQ(0, webrtc::AudioUtilsMac::macBookProMajorVersion("MacBookPro18,2", outMajorVersion));
   ASSERT_EQ(18, outMajorVersion);
   ASSERT_EQ(0, webrtc::AudioUtilsMac::macBookProMajorVersion("MacBookPro15,15", outMajorVersion));
   ASSERT_EQ(15, outMajorVersion);
   ASSERT_EQ(0, webrtc::AudioUtilsMac::macBookProMajorVersion("MacBookPro333,1", outMajorVersion));
   ASSERT_EQ(333, outMajorVersion);
   ASSERT_EQ(0, webrtc::AudioUtilsMac::macBookProMajorVersion("MacBookPro9,0", outMajorVersion));
   ASSERT_EQ(9, outMajorVersion);
   ASSERT_EQ(0, webrtc::AudioUtilsMac::macBookProMajorVersion("MacBookPro12,5,1", outMajorVersion));
   ASSERT_EQ(12, outMajorVersion);
   ASSERT_EQ(0, webrtc::AudioUtilsMac::macBookProMajorVersion("MacBookPro2,", outMajorVersion));
   ASSERT_EQ(2, outMajorVersion);
   ASSERT_EQ(-1, webrtc::AudioUtilsMac::macBookProMajorVersion("MacBookPro,", outMajorVersion));
   ASSERT_EQ(-1, webrtc::AudioUtilsMac::macBookProMajorVersion("MacBookPro,42", outMajorVersion));
   ASSERT_EQ(-1, webrtc::AudioUtilsMac::macBookProMajorVersion("MacBook22,5", outMajorVersion));
   ASSERT_EQ(-1, webrtc::AudioUtilsMac::macBookProMajorVersion("", outMajorVersion));
   ASSERT_EQ(-1, webrtc::AudioUtilsMac::macBookProMajorVersion(" ", outMajorVersion));
}

#endif // #if defined (__APPLE__) && !(TARGET_OS_IPHONE)



} // namespace
