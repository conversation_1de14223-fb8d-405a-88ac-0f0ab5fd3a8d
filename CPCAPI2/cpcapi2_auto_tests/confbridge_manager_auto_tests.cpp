#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#define HWND void*
#endif

#if (CPCAPI2_BRAND_CONFERENCE_BRIDGE_MODULE == 1)

#include "cpcapi2_test_fixture.h"
#include "test_call_events.h"
#include "test_events.h"
#include "test_account_events.h"
#include "test_framework/xmpp_test_helper.h"
#include "test_framework/screenshare_utils.h"

#include <webrtc/modules/desktop_capture/desktop_capturer.h>
#include <webrtc/base/stream.h>
#include <webrtc/video_engine/vie_encoder.h>

#include <confconnector/ConferenceConnector.h>
#include <confconnector/ConferenceConnectorHandler.h>
#include <confconnector/ConferenceConnectorInternal.h>
#include <confconnector/ConferenceConnectorTypes.h>
#include <orchestration_server/OrchestrationServer.h>
#include <cloudserviceconfig/CloudServiceConfig.h>
#include <confbridge/ConferenceBridgeJsonApi.h>
#include <confbridge/ConferenceBridgeManager.h>
#include <confbridge/ConferenceBridgeSyncHandler.h>
#include <auth_server/AuthServerInternal.h>

#include "../../impl/media/VideoInterface.h"
#include "../../impl/util/CurlPPHelper.h"
#include "../../impl/util/CurlPPProgress.h"
#include "../../impl/util/CurlPPSSL.h"
#include "../../impl/auth_server/AuthServerJwtUtils.h"
#include "../../impl/auth_server/AuthServerDbAccess.h"
#include "../../impl/jsonapi/JsonApiServerInterface.h"

#include <boost/algorithm/string.hpp>
#include <memory.h>

using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::ConferenceConnector;
using namespace CPCAPI2::ConferenceBridge;
using namespace CPCAPI2::CloudServiceConfig;
using namespace CPCAPI2::OrchestrationServer;
using namespace CPCAPI2::JsonApi;
using namespace CPCAPI2::SipConversation;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::PeerConnection;
using namespace webrtc;

class ConfBridgeManagerTests : public CpcapiAutoTest
{
public:
   ConfBridgeManagerTests() {}
   virtual ~ConfBridgeManagerTests() {}
};

void ConfBridgeManagerTests_getCloudServerUrls(std::string& authUrl, std::string& orchUrl, std::string& agentUrl, int authPort=18084, int jsonApiHttpPort=18082, int jsonApiWsPort=9003)
{
   std::stringstream authBuf;
   authBuf << "https://127.0.0.1:" << authPort;
   authUrl = authBuf.str().c_str();

   std::stringstream orchBuf;
   orchBuf << "https://127.0.0.1:" << jsonApiHttpPort << "/jsonApi";
   // orchBuf << "http://inproc.local:" << XMPP_ORCH_SERVER_HTTP_PORT << "/jsonApi";
   orchUrl = orchBuf.str().c_str();

   std::stringstream agentBuf;
   agentBuf << "wss://127.0.0.1:" << jsonApiWsPort;
   agentUrl = agentBuf.str().c_str();
}

void ConfBridgeManagerTests_setupAuthServer(TestAccount& max)
{
   CPCAPI2::AuthServer::DbAccess authDb;
   authDb.initialize("authserver.db");
   authDb.flushUsers();
   authDb.addUser("user1", "1234");
   authDb.addUser("user2", "1234");
   authDb.addUser("server", "server");

   CPCAPI2::AuthServer::AuthServerConfig authServerConfig;
   authServerConfig.certificateFilePath = TestEnvironmentConfig::testResourcePath() + "p256-private-key-unit-tests.p8";
   authServerConfig.httpsCertificateFilePath = TestEnvironmentConfig::testResourcePath() + "cert.pem";
   authServerConfig.httpsPrivateKeyFilePath = TestEnvironmentConfig::testResourcePath() + "privkey.pem";
   authServerConfig.httpsDiffieHellmanParamsFilePath = TestEnvironmentConfig::testResourcePath() + "dh2048.pem";
   authServerConfig.numThreads = 4;
   authServerConfig.port = 18084;
   max.authServer->start(authServerConfig);
}

class ConfBridgeManagerTests_RegisterConferenceResultHandlerFuncObj : public CPCAPI2::ConferenceBridge::RegisterConferenceResultHandler
{
public:
   ConfBridgeManagerTests_RegisterConferenceResultHandlerFuncObj(const std::function<void(CPCAPI2::ConferenceBridge::ConferenceRegistrarHandle, const CPCAPI2::ConferenceBridge::RegisterConferenceResult&)>& func) : mFunc(func) {}
   virtual ~ConfBridgeManagerTests_RegisterConferenceResultHandlerFuncObj() {}

   virtual int onRegisterConferenceComplete(CPCAPI2::ConferenceBridge::ConferenceRegistrarHandle registrar, const CPCAPI2::ConferenceBridge::RegisterConferenceResult& args) override {
      mFunc(registrar, args);
      delete this;
      return kSuccess;
   }
   virtual bool synchronous() const override {
      return true;
   }

private:
   std::function<void(CPCAPI2::ConferenceBridge::ConferenceRegistrarHandle, const CPCAPI2::ConferenceBridge::RegisterConferenceResult&)> mFunc;
};

void ConfBridgeManagerTests_setupConfBridgeServer(TestAccount& maia, int jsonApiHttpPort=18082, int jsonApiWsPort=9003, int videoStreamingPort=9005, int raftPort=18082, int raftNodeId=1)
{
   cpc::string certificateFilePath = TestEnvironmentConfig::testResourcePath() + "p256-public-key-unit-tests.spki"; // pub key used to validate auth tokens
   JsonApi::JsonApiServerConfig jsonApiServCfg(jsonApiWsPort, jsonApiHttpPort, certificateFilePath);
   jsonApiServCfg.wssCertificateFilePath = TestEnvironmentConfig::testResourcePath() + "cert.pem";
   jsonApiServCfg.wssPrivateKeyFilePath = TestEnvironmentConfig::testResourcePath() + "privkey.pem";
   jsonApiServCfg.wssDiffieHellmanParamsFilePath = TestEnvironmentConfig::testResourcePath() + "dh2048.pem";
   jsonApiServCfg.httpsCertificateFilePath = TestEnvironmentConfig::testResourcePath() + "cert.pem";
   jsonApiServCfg.httpsPrivateKeyFilePath = TestEnvironmentConfig::testResourcePath() + "privkey.pem";
   jsonApiServCfg.httpsDiffieHellmanParamsFilePath = TestEnvironmentConfig::testResourcePath() + "dh2048.pem";

   cpc::string conferenceBridgeServiceId = "confbridge";

   if (jsonApiServCfg.certificateFilePath.size() == 0)
      jsonApiServCfg.certificateFilePath = TestEnvironmentConfig::testResourcePath() + "p256-public-key-unit-tests.spki"; // pub key used to validate auth tokens
   maia.jsonApiServer->start(jsonApiServCfg);
   CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(maia.phone)->setJsonApiServer(maia.jsonApiServer);
   CPCAPI2::ConferenceBridge::ConferenceBridgeJsonApi::getInterface(maia.phone);

   CPCAPI2::VideoStreaming::VideoStreamingManager* videoStreamingMgr = CPCAPI2::VideoStreaming::VideoStreamingManager::getInterface(maia.phone);
   if (videoStreamingMgr != NULL)
   {
      videoStreamingMgr->setVideoStreamingServer(CPCAPI2::VideoStreaming::VideoStreamingManager::getInterface(maia.phone));
      CPCAPI2::VideoStreaming::VideoStreamingServerConfig vsConfig;
      vsConfig.listenPort = videoStreamingPort;
      vsConfig.wssCertificateFilePath = TestEnvironmentConfig::testResourcePath() + "cert.pem";
      vsConfig.wssPrivateKeyFilePath = TestEnvironmentConfig::testResourcePath() + "privkey.pem";
      vsConfig.wssDiffieHellmanParamsFilePath = TestEnvironmentConfig::testResourcePath() + "dh2048.pem";
      videoStreamingMgr->startVideoStreamingServer(vsConfig);
   }

   CPCAPI2::ConferenceBridge::ConferenceRegistrar* confRegistrar = CPCAPI2::ConferenceBridge::ConferenceRegistrar::getInterface(maia.phone);
   if (confRegistrar != NULL)
   {
      CPCAPI2::ConferenceBridge::ConferenceRegistrarConfig confRegistrarConfig;
      confRegistrarConfig.conferenceRegistrarServiceIp = cpc::string("127.0.0.1:") + std::to_string(jsonApiHttpPort).c_str();
      confRegistrarConfig.conferenceRegistrarServicePort = raftPort;
      confRegistrarConfig.joinClusterUrl = cpc::string("https://127.0.0.1:") + std::to_string(raftPort).c_str() + cpc::string("/statusApi/joinCluster");
      confRegistrarConfig.nodeId = raftNodeId;
      confRegistrarConfig.wsUrlBase = cpc::string("wss://127.0.0.1:") + std::to_string(jsonApiWsPort).c_str();
      confRegistrarConfig.jsonApiHostname = "cpclientapi.softphone.com";
      confRegistrarConfig.authServiceUrl = "https://127.0.0.1:18084/login_v2";
      confRegistrarConfig.authServiceApiKey = "-----BEGIN PUBLIC KEY-----\n"
         "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiKzxgDrW3u9+XzGN4r/GI5o+BMtt\n"
         "y+63OT4lwEgFlkpedq5FY3eCzI4lB1WXDVip0OpCM1zYoCh6mqi7KaS1Vg==\n"
         "-----END PUBLIC KEY-----";
      confRegistrarConfig.urlMapFilename = maia.config.name + cpc::string("_urlmap_autotests.db");

      //confRegistrarConfig.httpUrlBase = cpc::string("https://127.0.0.1:") + cpc::string(std::to_string(jsonApiHttpPort).c_str()) + cpc::string("/screenshare");
      confRegistrar->start(confRegistrarConfig);
      {
         CPCAPI2::ConferenceBridge::ConferenceRegistrarHandle h;
         CPCAPI2::ConferenceBridge::ConferenceRegistrarStartupResult args;
         ASSERT_TRUE(cpcExpectEvent(maia.conferenceRegistrarEvents, "ConferenceRegistrarHandler::onStartupComplete", 15000, CPCAPI2::test::AlwaysTruePred(), h, args));
      }
   }

   CPCAPI2::OrchestrationServer::OrchestrationServer* agentOrchServer = CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(maia.phone);
   CPCAPI2::OrchestrationServer::OrchestrationServerConfig serverConfig;
   serverConfig.redisIp = "mock";
   serverConfig.redisPort = 6379;
   agentOrchServer->start(serverConfig);

   CPCAPI2::Media::MediaStackSettings mediaSettings;
   mediaSettings.audioLayer = CPCAPI2::Media::AudioLayers_File;
   mediaSettings.audioOutputDisabled = true;
   mediaSettings.numAudioEncoderThreads = 4;
   CPCAPI2::Media::MediaManager* media = CPCAPI2::Media::MediaManager::getInterface(maia.phone);
   ASSERT_TRUE(media != NULL);
   CPCAPI2::Media::AudioExt* audioExt = CPCAPI2::Media::AudioExt::getInterface(media);
   ASSERT_TRUE(audioExt != NULL);
   audioExt->setAudioDeviceFile(TestEnvironmentConfig::testResourcePath() + "silence16.pcm", "");
   media->initializeMediaStack(mediaSettings);

   CPCAPI2::Media::Audio* audioIf = CPCAPI2::Media::Audio::getInterface(media);
   audioIf->setEchoCancellationMode(CPCAPI2::Media::AudioDeviceRole_Headset, CPCAPI2::Media::EchoCancellationMode_None);
   audioIf->setNoiseSuppressionMode(CPCAPI2::Media::AudioDeviceRole_Headset, CPCAPI2::Media::NoiseSuppressionMode_None);
   audioExt->setMicAGCEnabled(false);
   //audioIf->queryCodecList();
   //audioIf->setMicMute(true);
   //audioIf->setSpeakerMute(true);

   //Video::getInterface(mMedia)->setHandler(this);
   CPCAPI2::Media::Video::getInterface(media)->setCaptureDevice(CPCAPI2::Media::kCustomVideoSourceDeviceId);
   CPCAPI2::Media::Video::getInterface(media)->startCapture();
   //Video::getInterface(mMedia)->setVideoMixMode(CPCAPI2::Media::VideoMixMode_MCU);
   //Video::getInterface(mMedia)->queryCodecList();


   CloudServiceConfigManager* cloudConfigMgr = CPCAPI2::CloudServiceConfig::CloudServiceConfigManager::getInterface(maia.phone);
   test::EventHandler cloudConfigEvents(conferenceBridgeServiceId, dynamic_cast<CPCAPI2::AutoTestProcessor*>(cloudConfigMgr));

   std::string authUrl("");
   std::string orchUrl("");
   std::string agentUrl("");
   ConfBridgeManagerTests_getCloudServerUrls(authUrl, orchUrl, agentUrl, 18084, jsonApiHttpPort, jsonApiWsPort);
   orchUrl = "http://inproc.local";

   ConferenceBridge::ConferenceBridgeConfig bridgeSettings;
   bridgeSettings.httpJoinUrlBase = cpc::string("https://127.0.0.1:") + std::to_string(jsonApiHttpPort).c_str();
   bridgeSettings.wsUrlBase = cpc::string("wss://127.0.0.1:") + std::to_string(jsonApiWsPort).c_str();
   bridgeSettings.serverUid = "BC";
   //bridgeSettings.natTraversalServerInfo.natTraversalServerHostname = "cpsipv6.counterpath.net";
   //bridgeSettings.natTraversalServerInfo.natTraversalServerPort = 3478;
   //bridgeSettings.natTraversalServerInfo.natTraversalServerType = ConferenceBridge::ConferenceNatTraversalServerInfo::NatTraversalServerType_StunOnly;
   //bridgeSettings.natTraversalServerInfo.natTraversalServerType2 = ConferenceBridge::ConferenceNatTraversalServerInfo::NatTraversalServerType_StunAndTurn;
   //bridgeSettings.natTraversalServerInfo.natTraversalServerUsername = "cpcapi2";
   //bridgeSettings.natTraversalServerInfo.natTraversalServerPassword = "echo$8";
   //bridgeSettings.natTraversalServerInfo.serverPublicIpAddress = "**************";

   resip::Data user1enc = CPCAPI2::JsonApi::JsonApiServerInterface::doEncrypt("user1");
   bridgeSettings.userContext = user1enc.c_str();

   CPCAPI2::ConferenceBridge::ConferenceBridgeManager::getInterface(maia.phone)->start(bridgeSettings);

   ServerInfo xmppAgentServerInfo;
   xmppAgentServerInfo.region = "NA";
   xmppAgentServerInfo.uri = agentUrl.c_str();
   xmppAgentServerInfo.services.push_back(conferenceBridgeServiceId);
   ServiceConfigSettings serviceConfigSettings;
   serviceConfigSettings.authServerUrl = std::string(authUrl + "/login_v1").c_str();
   serviceConfigSettings.orchestrationServerUrl = orchUrl.c_str();
   serviceConfigSettings.username = "server";
   serviceConfigSettings.password = "server";

   cloudConfigMgr->setServerInfo(serviceConfigSettings, xmppAgentServerInfo);
   {
      CloudServiceConfigHandle h;
      SetServerInfoResult args;
      ASSERT_TRUE(cpcExpectEvent((&cloudConfigEvents), "CloudServiceConfigHandler::onSetServerInfoSuccess", 5000, CPCAPI2::test::AlwaysTruePred(), h, args));
   }
}

class TestPeerConnectionAnswerHandler : public CPCAPI2::ConferenceBridge::PeerConnectionAnswerHandler,
                                        public CPCAPI2::ConferenceBridge::ConferenceBridgeSyncHandler
{
public:
   TestPeerConnectionAnswerHandler()
   {
   }
   virtual ~TestPeerConnectionAnswerHandler() {}
   virtual int onPeerConnectionAnswer(ConferenceHandle conference, const PeerConnectionAnswerEvent& args) OVERRIDE
   {
      mEvt = args;
      mWait.notify_all();
      return kSuccess;
   }

   void waitForEvent() {
      std::unique_lock<std::mutex> lck(mWaitMtx);
      mWait.wait(lck);
   }

   const PeerConnectionAnswerEvent& event() const {
      return mEvt;
   }
private:
   PeerConnectionAnswerEvent mEvt;
   std::mutex mWaitMtx;
   std::condition_variable mWait;
};

TEST_F(ConfBridgeManagerTests, BasicAudioCall_NPE)
{
	LoginResultEvent loginResult; loginResult.success = true;
	cpc::vector<cpc::string> permissions; permissions.push_back("*");

	// Max is the auth server
	TestAccount max("max", Account_Init);
	ConfBridgeManagerTests_setupAuthServer(max);

	// Maia is the orchestration and confbridge server
	TestAccount maia("maia", Account_NoInit);
   maia.config.useFileAudioDevice = true;
   maia.config.mediaSettings.audioOutputDisabled = true;
   maia.config.mediaSettings.numAudioEncoderThreads = 4;
   maia.init();
	ConfBridgeManagerTests_setupConfBridgeServer(maia);

   // Alice is participant A (the caller)
   TestAccount alice("alice", Account_Init);

   // Bob is participant B (the callee)
   TestAccount bob("bob", Account_Init);

// -----------------

   PeerConnectionSettings aliceSettings;
   aliceSettings.certAor = alice.config.settings.username + "@" + alice.config.settings.domain;
   aliceSettings.natTraversalMode = PeerConnectionSettings::NatTraversalMode_ICE;
   //aliceSettings.natTraversalServerHostname = "stun.counterpath.com";
   aliceSettings.natTraversalServerPort = 3478;
   aliceSettings.sessionName = "peerconnectiontest";

   PeerConnectionHandle alicePeerConn = alice.peerConnection->createPeerConnection();
   alice.peerConnection->setHandler(alicePeerConn, (PeerConnectionHandler*)0xDEADBEEF);
   alice.peerConnection->setDefaultSettings(alicePeerConn, aliceSettings);
   CPCAPI2::PeerConnection::MediaInfo mediaInfo;
   mediaInfo.mediaType = PeerConnection::MediaType_Audio;
   mediaInfo.mediaDirection = PeerConnection::MediaDirection_SendRecv;
   mediaInfo.mediaEncryptionOptions.mediaEncryptionMode = PeerConnection::MediaEncryptionMode_Unencrypted;
   mediaInfo.mediaEncryptionOptions.secureMediaRequired = false;
   alice.peerConnection->configureMedia(alicePeerConn, alice.peerConnection->createMediaStream(), mediaInfo);
   ASSERT_EQ(alice.peerConnection->createOffer(alicePeerConn), kSuccess);

   CPCAPI2::PeerConnection::SessionDescription aliceSdp;
   {
      PeerConnectionHandle h;
      CreateOfferResult evt;
      ASSERT_TRUE(cpcExpectEvent(alice.peerConnEvents,
         "PeerConnectionHandler::onCreateOfferResult",
         45000,
         AlwaysTruePred(), h, evt));
      ASSERT_NE(strlen(evt.sdp.sdpString.c_str()), 0);
      aliceSdp = evt.sdp;
      std::cout << "alice sdp:" << std::endl << resip::Data(aliceSdp.sdpString, aliceSdp.sdpLen) << std::endl;
   }

   ASSERT_EQ(alice.peerConnection->setLocalDescription(alicePeerConn, aliceSdp), kSuccess);

// -----------------

   CPCAPI2::ConferenceBridge::ConferenceSettings confSettings;
   confSettings.persistent = false;
   confSettings.mixMode = CPCAPI2::ConferenceBridge::ConferenceMixMode_SFU_BiDi;
   confSettings.tags.push_back(0x4000); // audio only
   ConferenceHandle conf = maia.conferenceBridge->createConference(confSettings);

   ConferenceParticipantHandle incomingPart = maia.conferenceBridge->createWebParticipant(conf);
   /*
   CPCAPI2::PeerConnection::SessionDescription sdpOffer;
   const char* sdpStr = "v=0\r\n"
      "o=user1 53655765 2353687637 IN IP4 127.0.0.1\r\n"
      "s=-\r\n"
      "c=IN IP4 127.0.0.1\r\n"
      "t=0 0\r\n"
      "m=audio 59998 RTP/AVP 120 101\r\n"
      "a=rtpmap:120 opus/48000/2\r\n"
      "a=fmtp:120 useinbandfec=1\r\n"
      "a=rtpmap:101 telephone-event/8000\r\n"
      "a=fmtp:101 0-16\r\n"
      "a=ptime:20";
   sdpOffer.sdpString = sdpStr;
   sdpOffer.sdpLen = strlen(sdpStr);
   */
   std::unique_ptr<TestPeerConnectionAnswerHandler> answerHandler1(new TestPeerConnectionAnswerHandler());
   maia.conferenceBridge->sendPeerConnectionOffer(incomingPart, aliceSdp, answerHandler1.get());
   answerHandler1->waitForEvent();
   ASSERT_TRUE(answerHandler1->event().sdpAnswer.sdpLen > 0);
   ASSERT_EQ(alice.peerConnection->setRemoteDescription(alicePeerConn, answerHandler1->event().sdpAnswer), kSuccess);

   /* other call leg */
   PeerConnectionSettings bobSettings;
   bobSettings = aliceSettings;
   bobSettings.certAor = bob.config.settings.username + "@" + bob.config.settings.domain;

   ConferenceParticipantHandle outgoingPart = maia.conferenceBridge->createWebParticipant(conf);
   std::unique_ptr<TestPeerConnectionAnswerHandler> offerHandler1(new TestPeerConnectionAnswerHandler());
   maia.conferenceBridge->generateLocalOffer(outgoingPart, offerHandler1.get());
   offerHandler1->waitForEvent();
   ASSERT_TRUE(offerHandler1->event().sdpAnswer.sdpLen > 0);

   // ----------------

   PeerConnectionHandle bobPeerConn = bob.peerConnection->createPeerConnection();
   bob.peerConnection->setHandler(bobPeerConn, (PeerConnectionHandler*)0xDEADBEEF);
   bob.peerConnection->setDefaultSettings(bobPeerConn, bobSettings);
   CPCAPI2::PeerConnection::MediaInfo bobMediaInfo;
   bobMediaInfo.mediaType = PeerConnection::MediaType_Audio;
   bobMediaInfo.mediaDirection = PeerConnection::MediaDirection_SendRecv;
   bobMediaInfo.mediaEncryptionOptions.mediaEncryptionMode = PeerConnection::MediaEncryptionMode_Unencrypted;
   bobMediaInfo.mediaEncryptionOptions.secureMediaRequired = false;
   ASSERT_EQ(bob.peerConnection->configureMedia(bobPeerConn, bob.peerConnection->createMediaStream(), bobMediaInfo), 0);
   ASSERT_EQ(bob.peerConnection->setRemoteDescription(bobPeerConn, offerHandler1->event().sdpAnswer /* is actually an offer */), kSuccess);

   {
      PeerConnectionHandle h;
      SetRemoteSessionDescriptionResult evt;
      ASSERT_TRUE(cpcExpectEvent(bob.peerConnEvents,
         "PeerConnectionHandler::onSetRemoteSessionDescriptionResult",
         10000,
         AlwaysTruePred(), h, evt));
   }

   ASSERT_EQ(bob.peerConnection->createAnswer(bobPeerConn), kSuccess);

   CPCAPI2::PeerConnection::SessionDescription bobSdp;
   {
      PeerConnectionHandle h;
      CreateAnswerResult evt;
      ASSERT_TRUE(cpcExpectEvent(bob.peerConnEvents,
         "PeerConnectionHandler::onCreateAnswerResult",
         30000,
         AlwaysTruePred(), h, evt));
      ASSERT_NE(strlen(evt.sdp.sdpString.c_str()), 0);
      bobSdp = evt.sdp;
      std::cout << "bob sdp:" << std::endl << resip::Data(bobSdp.sdpString, bobSdp.sdpLen) << std::endl;
   }   
   ASSERT_EQ(bob.peerConnection->setLocalDescription(bobPeerConn, bobSdp), kSuccess);

   // ----------------

   maia.conferenceBridge->sendPeerConnectionAnswer(outgoingPart, bobSdp);

   std::this_thread::sleep_for(std::chrono::seconds(5));

   alice.peerConnection->close(alicePeerConn);
   bob.peerConnection->close(bobPeerConn);

	CPCAPI2::ConferenceBridge::ConferenceBridgeManager::getInterface(maia.phone)->shutdown();
	CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(maia.phone)->flushAll();
	CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(maia.phone)->shutdown();
	maia.jsonApiServer->shutdown();

	max.authServer->shutdown();

	std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   
}



#endif
