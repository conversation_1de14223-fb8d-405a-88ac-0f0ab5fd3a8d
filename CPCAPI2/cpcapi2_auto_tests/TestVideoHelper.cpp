#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif
#include "TestVideoHelper.h"
#include "cpcapi2_test_fixture.h"

using namespace CPCAPI2::test;

unsigned int TestVideoHelper::accountCount = 0;
CpTestWindowHandle TestVideoHelper::sFixedLocalVideoRenderHandle = NULL;
CpTestWindowHandle TestVideoHelper::sFixedRemoteVideoRenderHandle = NULL;
std::atomic_bool TestVideoHelper::sFixedLocalVideoRenderInuse = false;
std::atomic_bool TestVideoHelper::sFixedRemoteVideoRenderInuse = false;

TestVideoHelper::TestVideoHelper(const std::string& name)
{
   accountName = name;
   accountIndex = accountCount++;
   localOffset = OFFSET*(accountIndex / 2);
   hwndLocalWindow = NULL;
   hwndIncomingWindow = NULL;
   nsLocalWindow = NULL;
   nsIncomingWindow = NULL;
   ownsFixedLocalVideoRenderHandle = false;
   ownsFixedRemoteVideoRenderHandle = false;
}

TestVideoHelper::~TestVideoHelper()
{
   if (testAccount)
   {
      if (testAccount->video)
      {
         testAccount->video->stopCapture();
         testAccount->video->setLocalVideoRenderTarget(NULL);
      }
   }
   ViEDestroyWindow(nsLocalWindow, hwndLocalWindow);
   ViEDestroyWindow(nsIncomingWindow, hwndIncomingWindow);
   accountCount--;
   testAccount = NULL;
   if (ownsFixedLocalVideoRenderHandle)
   {
      sFixedLocalVideoRenderInuse = false;
   }
   if (ownsFixedRemoteVideoRenderHandle)
   {
      sFixedRemoteVideoRenderInuse = false;
   }
}

void TestVideoHelper::startVideo(bool createLocalWindow, bool createIncomingWindow)
{
   xPosition = localOffset;
   yPosition = localOffset + (accountIndex % 2)*(WINDOW_HEIGHT + 4);

   std::string caption = accountName;
   caption.append(" (capture)");
   char *localCaption = new char[caption.size() + 1];
   strcpy(localCaption, caption.c_str());
   caption = accountName;
   caption.append(" (incoming)");
   char *incomingCaption = new char[caption.size() + 1];
   strcpy(incomingCaption, caption.c_str());
   
   if (createLocalWindow)
   {
      if (TestEnvironmentConfig::drawLocalVideo())
      {
         if (sFixedLocalVideoRenderHandle)
         {
            // used only on Android at the moment
            if (!sFixedLocalVideoRenderInuse)
            {
               hwndLocalWindow = sFixedLocalVideoRenderHandle;
               sFixedLocalVideoRenderInuse = true;
               ownsFixedLocalVideoRenderHandle = true;
            }
            else
            {
               hwndLocalWindow = NULL;
            }
         }
         else
         {
            ASSERT_EQ(0, ViECreateWindow(hwndLocalWindow, xPosition, yPosition, WINDOW_WIDTH, WINDOW_HEIGHT, localCaption, nsLocalWindow));
         }
      }
   }
   xPosition += WINDOW_WIDTH + 4;
   if (createIncomingWindow)
   {
      if (TestEnvironmentConfig::drawLocalVideo())
      {
         if (sFixedRemoteVideoRenderHandle)
         {
            // used only on Android at the moment
            if (!sFixedRemoteVideoRenderInuse)
            {
               hwndIncomingWindow = sFixedRemoteVideoRenderHandle;
               sFixedRemoteVideoRenderInuse = true;
               ownsFixedRemoteVideoRenderHandle = true;
            }
            else
            {
               hwndIncomingWindow = NULL;
            }
         }
         else
         {
            ASSERT_EQ(0, ViECreateWindow(hwndIncomingWindow, xPosition, yPosition, WINDOW_WIDTH, WINDOW_HEIGHT, incomingCaption, nsIncomingWindow));
         }
      }
   }
}

