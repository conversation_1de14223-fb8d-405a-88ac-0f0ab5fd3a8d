#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#if 0 // tests disabled -- rely on external server that no longer exists
#if (CPCAPI2_BRAND_XCAP_MODULE == 1) && (CPCAPI2_BRAND_RESOURCE_LIST_MODULE == 1)

#include "cpcapi2_test_fixture.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>
#include <libxml/parser.h>
#include "xcap/XcapSubscriptionHandler.h"
#include "../../../../CPCAPI2/impl/xcap/XcapInternalInterface.h"
#include "../../../../CPCAPI2/impl/xcap/XcapResourceListManagerInterface.h"

#include "test_framework/cpcapi2_test_framework.h"
#include "test_framework/bare_bones_sip_endpoint.h"

#include <thread>
#include <future>
#include <cstdio>

using namespace CPCAPI2;
using namespace CPCAPI2::XCAP;
using namespace CPCAPI2::SipEvent;
using namespace CPCAPI2::test;

namespace 
{

class XcapTest : public CpcapiAutoTest
{
public:
   XcapTest() {}
   virtual ~XcapTest() {}
};

/** 
* Test wheter xcap module established comunication with server. 
* For this we only need result error code. If result code is 200, communication is working well.
*/
TEST_F(XcapTest, XcapCommunication) 
{
   TestAccount alice("alice");
   XcapResourceListManagerInterface* rlMenagerImpl = dynamic_cast<XcapResourceListManagerInterface*>(alice.xcapResourceListManager);

   XCAPResult result;
   XcapRequestComponents xcapRequest = {"index", "", "xcap-caps", "", true};
   result = rlMenagerImpl->getXcapManager()->read(alice.config.xcapSettings, xcapRequest);
   ASSERT_EQ(result.errorCode, 200);
}

/**
* Test wheter xcap write function works. At the same time this is test for document write.
*/
TEST_F(XcapTest, XcapAddDocument) 
{
   TestAccount alice("alice");
   XCAPResult result;
   XcapResourceListManagerInterface* rlMenagerIf = dynamic_cast<XcapResourceListManagerInterface*>(alice.xcapResourceListManager);
   XcapRequestComponents xcapRequest = {"index", "", "resource-lists", "application/resource-lists+xml", false};
   resip::Data filePath = "resource-list-example.xml";

   result = rlMenagerIf->getXcapManager()->add(alice.config.xcapSettings, xcapRequest, filePath);
   ASSERT_TRUE((result.errorCode == 201) || (result.errorCode == 200));
}

/** 
* Test wheter xcap read function works. At the same time this is test for document read.
* For this we need to check if returned value is consistant with xml file syntax. If result 
* represents xml file contents than result is valid, if it doesn't it's invalid.
*/
TEST_F(XcapTest, XcapReadDocument) 
{
   TestAccount alice("alice");
   XcapResourceListManagerInterface* rlMenagerIf = dynamic_cast<XcapResourceListManagerInterface*>(alice.xcapResourceListManager);
   XCAPResult result;
   XcapRequestComponents xcapRequest = {"index", "", "resource-lists", "", false};

   result = rlMenagerIf->getXcapManager()->read(alice.config.xcapSettings, xcapRequest);
   xmlParserCtxtPtr xmlDoc = ::xmlCreateDocParserCtxt((unsigned char*)result.result.c_str());
   int i = (xmlDoc == NULL)?0:1;
   ASSERT_NE(i, NULL);
}

/** 
* Test wheter xcap read function with no parameter works. 
* For this we need to check if returned value is consistant with xml file syntax. If result 
* represents xml file contents than result is valid, if it doesn't it's invalid.
*/
TEST_F(XcapTest, XcapReadElement) 
{
   TestAccount alice("alice");
   XCAPResult result;
   XcapResourceListManagerInterface* rlMenagerIf = dynamic_cast<XcapResourceListManagerInterface*>(alice.xcapResourceListManager);
   XcapRequestComponents xcapRequest = {"index", "resource-lists/list[@name=\"default\"]", "resource-lists", "application/xcap-el+xml", false};

   result = rlMenagerIf->getXcapManager()->read(alice.config.xcapSettings, xcapRequest);
   ASSERT_EQ(200, result.errorCode);
}

TEST_F(XcapTest, XcapAddElement) 
{
   TestAccount alice("alice");
   XCAPResult result;
   XcapResourceListManagerInterface* rlMenagerIf = dynamic_cast<XcapResourceListManagerInterface*>(alice.xcapResourceListManager);
   XcapRequestComponents xcapRequest = {"index", "resource-lists/list[@name=\"enemies\"]", "resource-lists", "application/xcap-el+xml", false};
   cpc::string resourceData = "<list name=\"enemies\"></list>\0";

   result = rlMenagerIf->getXcapManager()->add(alice.config.xcapSettings, xcapRequest, resourceData);
   ASSERT_TRUE((result.errorCode == 201) || (result.errorCode == 200));
}

TEST_F(XcapTest, XcapReadElement1) 
{
   TestAccount alice("alice");
   XCAPResult result;
   XcapResourceListManagerInterface* rlMenagerIf = dynamic_cast<XcapResourceListManagerInterface*>(alice.xcapResourceListManager);
   XcapRequestComponents xcapRequest = {"index", "resource-lists/list[@name=\"enemies\"]", "resource-lists", "application/xcap-el+xml", false};

   result = rlMenagerIf->getXcapManager()->read(alice.config.xcapSettings, xcapRequest);
   ASSERT_EQ(200, result.errorCode);
}

TEST_F(XcapTest, XcapDeleteElement) 
{
   TestAccount alice("alice");
   XCAPResult result;
   XcapResourceListManagerInterface* rlMenagerIf = dynamic_cast<XcapResourceListManagerInterface*>(alice.xcapResourceListManager);
   XcapRequestComponents xcapRequest = {"index", "resource-lists/list[@name=\"enemies\"]", "resource-lists", "application/xcap-el+xml", false};

   result = rlMenagerIf->getXcapManager()->remove(alice.config.xcapSettings, xcapRequest);
   ASSERT_EQ(200, result.errorCode);
}

/**
* Test wheter xcap delete function works. At the same time this is test for document delete.
*/
TEST_F(XcapTest, XcapDeleteDocument) 
{
   TestAccount alice("alice");
   XCAPResult result;
   XcapResourceListManagerInterface* rlMenagerIf = dynamic_cast<XcapResourceListManagerInterface*>(alice.xcapResourceListManager);
   XcapRequestComponents xcapRequest = {"index", "", "resource-lists", "", false};

   result = rlMenagerIf->getXcapManager()->remove(alice.config.xcapSettings, xcapRequest);
   ASSERT_EQ(result.errorCode, 200);
}

}
#endif //CPCAPI2_BRAND_XCAP_MODULE
#endif // #if 0 // tests disabled -- rely on external server that no longer exists
