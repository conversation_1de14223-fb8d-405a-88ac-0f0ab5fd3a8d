#!/bin/zsh -l

# DRL This was required on some Mac systems to initialize the environment when invoked
# from XCode
source ~/.zshrc

err_report() {
    echo "Error on line $1"
}

trap 'err_report $LINENO' ERR

which cmake || echo "Please ensure your default shell is set to zsh and cmake is installed and set in your PATH .zshrc or .zprofile"

echo "Generating projects for indexing"
rm -rf indexingCPCAPI2
mkdir indexingCPCAPI2
cd indexingCPCAPI2

cmake --trace-redirect=Trace.log -Wdev -G Xcode -S .. -B . -DCMAKE_BUILD_TYPE=Debug -DSKIP_COMPRESS=1 -DCMAKE_TOOLCHAIN_FILE="../projects/cmake/toolchains/osx-clang-armv8.cmake"
cd ..

