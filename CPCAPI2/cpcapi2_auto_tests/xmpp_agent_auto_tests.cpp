#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#if 0 //(CPCAPI2_BRAND_XMPP_AGENT_MODULE == 1) && (CPCAPI2_BRAND_PUSH_NOTIFICATION_CLIENT_MODULE == 1)

#include "cpcapi2_test_fixture.h"
#include "test_call_events.h"
#include "test_events.h"
#include "test_account_events.h"

#include "test_framework/xmpp_test_helper.h"
#include "vccs_test_harness/VccsTestHarness.h"

#include "xmpp/XmppAccountInterface.h"
#include "xmpp/XmppAccountJsonApi.h"
#include "xmpp/XmppChatJsonApi.h"
#include "xmpp/XmppRosterJsonApi.h"
#include "xmpp/XmppVCardJsonApi.h"
#include "xmpp/XmppMultiUserChatJsonApi.h"
#include "xmpp_agent/XmppAgentHandler.h"
#include "xmpp_agent/XmppAgentJsonApi.h"
#include "xmpp_agent/XmppAgentInternal.h"
#include "jsonapi/JsonApiServerInternal.h"
#include "push_endpoint/PushNotificationEndpointManager.h"
#include "push_endpoint/PushNotificationEndpointHandler.h"
#include "push_endpoint/PushNotificationEndpointManagerInternal.h"
#include "push_endpoint/PushNotificationEndpointJsonApi.h"
#include "push_service/PushNotificationServiceInterface.h"
#include "push_service/PushServiceRedisAccessIf.h"

#include "../../impl/util/CurlPPHelper.h"
#include "../../impl/util/CurlPPProgress.h"
#include "../../impl/util/CurlPPSSL.h"
#include "../../impl/auth_server/AuthServerJwtUtils.h"

#include <rutil/MultiReactor.hxx>
#include <rutil/HighPerfReactor.hxx>

#include <auth_server/AuthServer.h>

#include <atomic>

// rapidjson
#include <writer.h>
#include <stringbuffer.h>
#include <document.h>


#ifndef __APPLE__ // OBELISK-3342
#define REGISTER_FOR_REMOTESYNC 1
#endif

using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::AuthServer;
using namespace CPCAPI2::XmppAccount;
using namespace CPCAPI2::XmppChat;
using namespace CPCAPI2::XmppRoster;
using namespace CPCAPI2::PushService;
using namespace CPCAPI2::PushEndpoint;
using namespace CPCAPI2::JsonApi;
using namespace CPCAPI2::RemoteSync;


class XmppAgentTests : public CpcapiAutoTest
{
public:
   XmppAgentTests() {}
   virtual ~XmppAgentTests() {
   }
};

static void generateJwt(const resip::Data& p8file, const resip::Data& userIdentity, resip::Data& jwt)
{
   std::map<resip::Data, resip::Data> pubClaims;
   pubClaims["cp_user"] = userIdentity;
   pubClaims["device_uuid"] = resip::Random::getCryptoRandomBase64(8);
   CPCAPI2::AuthServer::JwtUtils::GenerateJWT(p8file, "CPCAPI2::AuthServer", pubClaims, 86400, jwt);
}

TEST_F(XmppAgentTests, ClientRegisterAndRecvPush) {
   XmppTestAccount bob("bob", Account_Init);
   static_cast<PhoneInternal*>(bob.phone)->setLocalFileLoggingEnabled(bob.config.name, true);
   static_cast<PhoneInternal*>(bob.phone)->setLocalFileLoggingLevel(CPCAPI2::LogLevel_Debug);
   bob.enable();

   XmppTestAccount max("max", Account_Init);

   static_cast<PhoneInternal*>(max.phone)->setLocalFileLoggingEnabled(max.config.name, true);
   static_cast<PhoneInternal*>(max.phone)->setLocalFileLoggingLevel(CPCAPI2::LogLevel_Debug);

   JsonApiServerConfig maxJsonApiConfig;
   maxJsonApiConfig.certificateFilePath = TestEnvironmentConfig::testResourcePath() + "p256-public-key-unit-tests.spki";
   maxJsonApiConfig.wssCertificateFilePath = TestEnvironmentConfig::testResourcePath() + "cert.pem";
   maxJsonApiConfig.wssPrivateKeyFilePath = TestEnvironmentConfig::testResourcePath() + "privkey.pem";
   maxJsonApiConfig.wssDiffieHellmanParamsFilePath = TestEnvironmentConfig::testResourcePath() + "dh2048.pem";
   max.jsonApiServer->start(maxJsonApiConfig);
   CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(max.phone)->setJsonApiServer(max.jsonApiServer);
   CPCAPI2::XmppAgent::XmppAgentManager::getInterface(max.phone)->setPushNotificationManager(CPCAPI2::PushService::PushNotificationServiceManager::getInterface(max.phone));
   CPCAPI2::XmppAgent::XmppAgentManager::getInterface(max.phone)->setJsonApiServer(max.jsonApiServer);

   CPCAPI2::XmppAgent::XmppAgentJsonApi::getInterface(max.phone);
   CPCAPI2::PushEndpoint::PushNotificationEndpointJsonApi::getInterface(max.phone);
   CPCAPI2::XmppAccount::XmppAccountJsonApi::getInterface(max.phone);
   CPCAPI2::XmppChat::XmppChatJsonApi::getInterface(max.phone);
   CPCAPI2::XmppRoster::XmppRosterJsonApi::getInterface(max.phone);

   PushNotificationServiceManager* maxPushServer = CPCAPI2::PushService::PushNotificationServiceManager::getInterface(max.phone);
   PushDatabaseSettings pushDatabaseSettings;
   pushDatabaseSettings.redisIp = "pushdbsqlite";
   pushDatabaseSettings.redisPort = 9999; // special hack for the unit tests to start with a clean (fresh) database
   maxPushServer->configureDatabaseAccess(pushDatabaseSettings);

   PushNotificationEndpointManager* maxPushEndpoint = CPCAPI2::PushEndpoint::PushNotificationEndpointManager::getInterface(max.phone);
   maxPushEndpoint->setPushNotificationService(maxPushServer);
   PushProviderSettings pushProviderSettings;
   pushProviderSettings.pushNetworkType = PushNetworkType_WS;
   maxPushServer->configurePushProvider(pushProviderSettings);

   XmppTestAccount alice("alice", Account_Init);
   alice.config.useJsonProxy = true;
   JsonApiClientSettings jsonApiClientSettings;
   jsonApiClientSettings.serverUri = "wss://localhost:9003";
   jsonApiClientSettings.ignoreCertVerification = true;
   alice.jsonApiClient->configureDefaultSettings(jsonApiClientSettings);
   alice.jsonApiClient->enable();

   {
      // virtual int onStatusChanged(JsonApiLoginHandle h, StatusChangedEvent& args)
      JsonApiLoginHandle h;
      StatusChangedEvent args;
      cpcExpectEvent(alice.jsonApiClientEvents, "JsonApiClientHandler::onStatusChanged", 10000, CPCAPI2::test::AlwaysTruePred(), h, args);
      ASSERT_EQ(args.status, StatusChangedEvent::Status_Connecting);
      cpcExpectEvent(alice.jsonApiClientEvents, "JsonApiClientHandler::onStatusChanged", 10000, CPCAPI2::test::AlwaysTruePred(), h, args);
      ASSERT_EQ(args.status, StatusChangedEvent::Status_Connected);
      alice.setCloudConnected(true);
   }

   resip::Data aliceJwt;
   generateJwt((TestEnvironmentConfig::testResourcePath() + "p256-private-key-unit-tests.p8").c_str(), "alice", aliceJwt);
   alice.jsonApiClient->login(aliceJwt.c_str());

   {
      JsonApiUserHandle jsonApiUser;
      NewLoginEvent args;

      // Max has to process the login attempt (associate the context with an SDK instance)
      cpcExpectEvent(max.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 5000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args);
      ASSERT_NE(args.authToken.size(), 0);
      cpc::vector<cpc::string> permissions; permissions.push_back("*");
      max.jsonApiServer->setJsonApiUserContext(jsonApiUser, max.phone, permissions);
      LoginResultEvent loginResult;
      loginResult.success = true;
      max.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);
   }

   {
      // Alice then gets the result
      JsonApiLoginHandle h;
      LoginResultEvent args;
      cpcExpectEvent(alice.jsonApiClientEvents, "JsonApiClientHandler::onLoginResult", 50000, CPCAPI2::test::AlwaysTruePred(), h, args);
   }

#if REGISTER_FOR_REMOTESYNC
   const cpc::string host("127.0.0.1");
   const int         port(8989);
   const cpc::string group("group1.1");
   const cpc::string password("user0001@group1.1:1234");
   const cpc::string user1("user0001@group1.1");
   const cpc::string user2("user0002@group1.1");

   cpc::string wsURL;
   wsURL = "ws://";
   wsURL += host;
   wsURL += ":";
   wsURL += std::to_string(port).c_str();
   wsURL += "/sync/sock/";
   wsURL += group;
   wsURL += "/";
   wsURL += user1;

   RemoteSyncSettings aliceRemoteSyncSettings;
   aliceRemoteSyncSettings.wsSettings.certMode = WebSocket::CertVerificationMode_None;
   aliceRemoteSyncSettings.wsSettings.webSocketURL = wsURL;
   aliceRemoteSyncSettings.password = password;
   aliceRemoteSyncSettings.accounts.push_back(user1);

   VccsTestHarness aliceHarness(TestEnvironmentConfig::testResourcePath() + "vccs_test_harness_scenarios/XmppAgentTests_ClientRegisterAndRecvPush_alice.dat");
   aliceHarness.start();
#endif // REGISTER_FOR_REMOTESYNC

   alice.createAccountJson();

#if REGISTER_FOR_REMOTESYNC
   // RemoteSyncXmppHelper currently relies on the app to call process(..) --
   // we need to drive that here, so RemoteSyncXmppHelper running on max
   // capture's alice's account creation above.
   max.events->startProcessThread();
   std::this_thread::sleep_for(std::chrono::milliseconds(500));
   max.events->processNonUnitTestEvents(0);
#endif

   alice.enable();

   PushNotificationEndpointHandle pushDev = alice.pushEndpointJson->createPushNotificationEndpoint();
   //ASSERT_GT(pushDev, 0); // pushDev is unsigned so this comparison is meaningless

   alice.pushEndpointJson->setHandler(pushDev, (PushNotificationEndpointHandler*)0xDEADBEEF);

   PushEndpoint::PushNotificationRegistrationInfo regInfo;
   regInfo.pushNetworkType = PushEndpoint::PushNetworkType_WS;
   regInfo.deviceToken = "device_token";
   alice.pushEndpointJson->registerForPushNotifications(pushDev, regInfo);
   PushNotificationEndpointId alicePushEndpointId;

   {
      PushNotificationEndpointHandle h;
      PushRegistrationSuccessEvent args;
      cpcExpectEvent(alice.pushJsonEvents, "PushNotificationEndpointHandler::onPushRegistrationSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, args);
      alicePushEndpointId = args.endpointId;
   }
   ASSERT_FALSE(alicePushEndpointId.empty());

   XmppAgent::XmppPushRegistrationInfo info;
   info.xmppAccountHandle = alice.handle;
   info.pushNotificationDev = alicePushEndpointId;
   info.pushServerUrl = "localsdk";
   XmppAgent::XmppPushRegistrationHandle alicePush = alice.agentJson->createXmppPushRegistration();

   alice.agentJson->setHandler(alicePush, (XmppAgent::XmppAgentHandler*)0xDEADBEEF);
   alice.agentJson->registerForXmppPushNotifications(alicePush, info);

   {
      XmppAgent::XmppPushRegistrationHandle h;
      XmppAgent::XmppPushRegistrationSuccessEvent args;
      ASSERT_TRUE(cpcExpectEvent(alice.agentJsonEvents, "XmppAgentHandler::onPushRegistrationSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, args));
      ASSERT_EQ(args.xmppAccountHandle, alice.handle);
      ASSERT_EQ(args.pushEndpointId, alicePushEndpointId);
   }

#if REGISTER_FOR_REMOTESYNC
   alice.agentJson->registerForRemoteSync(alicePush, aliceRemoteSyncSettings);   

   {
      XmppAgent::XmppPushRegistrationHandle h;
      XmppAgent::XmppAgentRemoteSyncRegisterResult args;
      ASSERT_TRUE(cpcExpectEvent(alice.agentJsonEvents, "XmppAgentHandler::onRemoteSyncRegisterResult", 15000, CPCAPI2::test::AlwaysTruePred(), h, args));      
   }
#endif // REGISTER_FOR_REMOTESYNC

   std::promise<bool> aliceReconnected;

   auto aliceEvent = std::async(std::launch::async, [&]() noexcept {
      XmppRosterHandle aliceRoster = alice.rosterJson->createRoster(alice.handle);
      cpc::vector<XmppRoster::RosterItem> items;
      alice.rosterJson->getRosterState(aliceRoster, items);

      XmppChatHandle aliceChat = alice.chatJson->createChat(alice.handle);
      alice.chatJson->addParticipant(aliceChat, bob.config.bare());
      alice.chatJson->start(aliceChat);

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewChat", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(aliceChat, h);
         ASSERT_EQ(evt.chatType, ChatType_Outgoing);
      }

      XmppChatMessageHandle cm1 = alice.chatJson->sendMessage(aliceChat, "test123");

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::SendMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onSendMessageSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.message, cm1);
      }

      cpc::string remoteSyncFromID = alice.chatJson->getRemoteSyncFromID(aliceChat, cm1);
      ASSERT_GT(remoteSyncFromID.size(), 2);

      //{
      //   // Wait for the message delivery notification (from Bob)
      //   XmppChatHandle h;
      //   MessageDeliveredEvent evt;
      //   ASSERT_TRUE(cpcExpectEvent(alice.chatEvents, "XmppChatHandler::onMessageDelivered", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      //   ASSERT_EQ(evt.message, cm1);
      //}

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::IsComposingMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onIsComposingMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.state, CPCAPI2::XmppChat::IsComposingMessageState_Active);
      }

      {
         PushNotificationEndpointHandle h;
         CPCAPI2::PushEndpoint::PushNotificationEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.pushJsonEvents, "PushNotificationEndpointHandler::onPushNotification", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      }

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::NewMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.from, bob.config.bare());
         ASSERT_EQ(evt.messageContent, "test456");
         //XmppChatMessageHandle aliceRecvdCm1 = evt.message;
         //alice.chat->notifyMessageDelivered(aliceChat, aliceRecvdCm1, CPCAPI2::XmppChat::MessageDeliveryStatus_Delivered);
      }

      // new json api testing

      alice.jsonApiClient->disable();

      {
         JsonApiLoginHandle h;
         StatusChangedEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.jsonApiClientEvents, "JsonApiClientHandler::onStatusChanged", 10000, CPCAPI2::test::AlwaysTruePred(), h, args));
         ASSERT_EQ(args.status, StatusChangedEvent::Status_Disconnected);
         alice.setCloudConnected(false);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      alice.jsonApiClient->enable();

      {
         JsonApiLoginHandle h;
         StatusChangedEvent args;
         do
         {
            ASSERT_TRUE(cpcExpectEvent(alice.jsonApiClientEvents, "JsonApiClientHandler::onStatusChanged", 10000, CPCAPI2::test::AlwaysTruePred(), h, args));
         } while (args.status != StatusChangedEvent::Status_Connected);
         alice.setCloudConnected(true);
      }

      alice.jsonApiClient->login(aliceJwt.c_str());

      {
         JsonApiUserHandle jsonApiUser;
         NewLoginEvent args;

         // Max has to process the login attempt (associate the context with an SDK instance)
         ASSERT_TRUE(cpcExpectEvent(max.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 15000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args));
         cpc::vector<cpc::string> permissions; permissions.push_back("*");
         max.jsonApiServer->setJsonApiUserContext(jsonApiUser, max.phone, permissions);
         LoginResultEvent loginResult;
         loginResult.success = true;
         max.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);
      }

      {
         // Alice then gets the result
         JsonApiLoginHandle h;
         LoginResultEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.jsonApiClientEvents, "JsonApiClientHandler::onLoginResult", 50000, CPCAPI2::test::AlwaysTruePred(), h, args));
      }

      PushEndpoint::PushNotificationRegistrationInfo regInfo2;
      regInfo2.deviceToken = "device_token";
      regInfo2.pushNetworkType = PushEndpoint::PushNetworkType_WS;
      //regInfo2.pushEndpointId = alicePushEndpointId; // NOT REQUIRED -- the auth token used for alice's websocket connection is used to map to her (already allocated) push endpoint ID
      alice.pushEndpointJson->registerForPushNotifications(pushDev, regInfo2);

      {
         PushNotificationEndpointHandle h;
         PushRegistrationSuccessEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.pushJsonEvents, "PushNotificationEndpointHandler::onPushRegistrationSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, args));
         alicePushEndpointId = args.endpointId;
      }
      ASSERT_FALSE(alicePushEndpointId.empty());


      // simulate current client behaviour of re-registering for XMPP push notifications
      // (not actually required)
      info.pushNotificationDev = alicePushEndpointId;
      alicePush = alice.agentJson->createXmppPushRegistration();
      alice.agentJson->setHandler(alicePush, (XmppAgent::XmppAgentHandler*)0xDEADBEEF);
      alice.agentJson->registerForXmppPushNotifications(alicePush, info);

      {
         XmppAgent::XmppPushRegistrationHandle h;
         XmppAgent::XmppPushRegistrationSuccessEvent args;
         ASSERT_TRUE(cpcExpectEvent(alice.agentJsonEvents, "XmppAgentHandler::onPushRegistrationSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, args));
         ASSERT_EQ(args.xmppAccountHandle, info.xmppAccountHandle);
         ASSERT_EQ(args.pushEndpointId, info.pushNotificationDev);
      }

      // end simulate

      aliceReconnected.set_value(true);

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::NewMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.from, bob.config.bare());
         ASSERT_EQ(evt.messageContent, "test789");
      }

      {
         PushNotificationEndpointHandle h;
         CPCAPI2::PushEndpoint::PushNotificationEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.pushJsonEvents, "PushNotificationEndpointHandler::onPushNotification", 45000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      }


      alice.chatJson->end(aliceChat);

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onChatEnded", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedLocally);
      }

   });

   auto bobEvent = std::async(std::launch::async, [&]() noexcept {
      XmppChatHandle bobChat = 0;

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewChat", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_NE(h, 0);
         ASSERT_EQ(evt.chatType, ChatType_Incoming);
         bobChat = h;
      }

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::NewMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.from, alice.config.bare());
         ASSERT_EQ(evt.messageContent, "test123");
         ASSERT_TRUE(evt.message != 0);
         XmppChatMessageHandle bobRecvdCm1 = evt.message;
         bob.chat->notifyMessageDelivered(bobChat, bobRecvdCm1, CPCAPI2::XmppChat::MessageDeliveryStatus_Delivered);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      bob.chat->setIsComposingMessage(bobChat);
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      XmppChatMessageHandle bobCm1 = bob.chat->sendMessage(bobChat, "test456");

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::SendMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onSendMessageSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.message, bobCm1);
      }

      std::future<bool> aliceReconnectedFV = aliceReconnected.get_future();
      aliceReconnectedFV.get();

      XmppChatMessageHandle bobCm2 = bob.chat->sendMessage(bobChat, "test789");

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::SendMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onSendMessageSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.message, bobCm2);
      }


      //{
      //   // Wait for the message delivery notification (from Bob)
      //   XmppChatHandle h;
      //   MessageDeliveredEvent evt;
      //   ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onMessageDelivered", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      //   ASSERT_EQ(evt.message, bobCm1);
      //}

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onChatEnded", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedRemotely);
      }
   });

   waitFor2(aliceEvent, bobEvent);

   alice.disable();
   alice.destroy();
   alice.jsonApiClient->disable();
   alice.setCloudConnected(false);

   bob.disable();
   
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   max.jsonApiServer->shutdown();
}

TEST_F(XmppAgentTests, LoginLogoutDelayWebsocketDisconnect)
{
   XmppTestAccount max("max", Account_Init);

   static_cast<PhoneInternal*>(max.phone)->setLocalFileLoggingEnabled(max.config.name, true);
   static_cast<PhoneInternal*>(max.phone)->setLocalFileLoggingLevel(CPCAPI2::LogLevel_Debug);

   JsonApiServerConfig maxJsonApiConfig;
   maxJsonApiConfig.certificateFilePath = TestEnvironmentConfig::testResourcePath() + "p256-public-key-unit-tests.spki";
   maxJsonApiConfig.wssCertificateFilePath = TestEnvironmentConfig::testResourcePath() + "cert.pem";
   maxJsonApiConfig.wssPrivateKeyFilePath = TestEnvironmentConfig::testResourcePath() + "privkey.pem";
   maxJsonApiConfig.wssDiffieHellmanParamsFilePath = TestEnvironmentConfig::testResourcePath() + "dh2048.pem";
   max.jsonApiServer->start(maxJsonApiConfig);
   CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(max.phone)->setJsonApiServer(max.jsonApiServer);

   XmppTestAccount alice("alice", Account_Init);
   alice.config.useJsonProxy = true;
   JsonApiClientSettings jsonApiClientSettings;
   jsonApiClientSettings.serverUri = "wss://localhost:9003";
   jsonApiClientSettings.ignoreCertVerification = true;
   alice.jsonApiClient->configureDefaultSettings(jsonApiClientSettings);
   alice.jsonApiClient->enable();

   {
      JsonApiLoginHandle h;
      StatusChangedEvent args;
      cpcExpectEvent(alice.jsonApiClientEvents, "JsonApiClientHandler::onStatusChanged", 10000, CPCAPI2::test::AlwaysTruePred(), h, args);
      ASSERT_EQ(args.status, StatusChangedEvent::Status_Connecting);
      cpcExpectEvent(alice.jsonApiClientEvents, "JsonApiClientHandler::onStatusChanged", 10000, CPCAPI2::test::AlwaysTruePred(), h, args);
      ASSERT_EQ(args.status, StatusChangedEvent::Status_Connected);
      alice.setCloudConnected(true);
   }

   resip::Data aliceJwt;
   generateJwt((TestEnvironmentConfig::testResourcePath() + "p256-private-key-unit-tests.p8").c_str(), "alice", aliceJwt);
   alice.jsonApiClient->login(aliceJwt.c_str());

   {
      JsonApiUserHandle jsonApiUser;
      NewLoginEvent args;

      // Max has to process the login attempt (associate the context with an SDK instance)
      cpcExpectEvent(max.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 5000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args);
      ASSERT_NE(args.authToken.size(), 0);
      cpc::vector<cpc::string> permissions; permissions.push_back("*");
      max.jsonApiServer->setJsonApiUserContext(jsonApiUser, max.phone, permissions);
      LoginResultEvent loginResult;
      loginResult.success = true;
      max.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);
   }

   {
      // Alice then gets the result
      JsonApiLoginHandle h;
      LoginResultEvent args;
      cpcExpectEvent(alice.jsonApiClientEvents, "JsonApiClientHandler::onLoginResult", 50000, CPCAPI2::test::AlwaysTruePred(), h, args);
   }

   // probably not necessary
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   alice.jsonApiClient->logout();

   // necessary to some degree; we want logout to finish and the user's service
   // side phone to destruct before tearing down the websocket
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   alice.jsonApiClient->disable();
   alice.setCloudConnected(false);

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   max.jsonApiServer->shutdown();
}

TEST_F(XmppAgentTests, DISABLED_LoginStressTest)
{
   XmppTestAccount max("max", Account_Init);

   static_cast<PhoneInternal*>(max.phone)->setLocalFileLoggingEnabled(max.config.name, true);
   static_cast<PhoneInternal*>(max.phone)->setLocalFileLoggingLevel(CPCAPI2::LogLevel_Debug);

   JsonApiServerConfig maxJsonApiConfig;
   maxJsonApiConfig.certificateFilePath = TestEnvironmentConfig::testResourcePath() + "p256-public-key-unit-tests.spki";
   maxJsonApiConfig.wssCertificateFilePath = TestEnvironmentConfig::testResourcePath() + "cert.pem";
   maxJsonApiConfig.wssPrivateKeyFilePath = TestEnvironmentConfig::testResourcePath() + "privkey.pem";
   maxJsonApiConfig.wssDiffieHellmanParamsFilePath = TestEnvironmentConfig::testResourcePath() + "dh2048.pem";
   max.jsonApiServer->start(maxJsonApiConfig);
   CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(max.phone)->setJsonApiServer(max.jsonApiServer);

   std::atomic<bool> maxProcessActive(true);
   auto maxProcess = std::async(std::launch::async, [&]() noexcept
   {
      do
      {
         JsonApiUserHandle jsonApiUser;
         NewLoginEvent args;

         // Max has to process the login attempt (associate the context with an SDK instance)
         bool eventReceived = cpcExpectEvent(max.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 5000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, args);
         if (eventReceived)
         {
            ASSERT_NE(args.authToken.size(), 0);
            cpc::vector<cpc::string> permissions; permissions.push_back("*");
            max.jsonApiServer->setJsonApiUserContext(jsonApiUser, max.phone, permissions);
            LoginResultEvent loginResult;
            loginResult.success = true;
            max.jsonApiServer->sendLoginResult(jsonApiUser, loginResult);
         }
      } while (maxProcessActive);
   });

   XmppTestAccount masterClient("fred", Account_Init);

   for (int k = 0; k < 3; ++k)
   {
      std::vector< std::future<void> > futures;
      for (int i = 0; i < 40; ++i)
      {
         futures.push_back(std::async(std::launch::async, [&]() noexcept
         {
            // re-use an existing master phone to avoid disrupting resip logging
            CPCAPI2::Phone* phone = CPCAPI2::PhoneInternal::create(static_cast<PhoneInternal*>(masterClient.phone));
            phone->initialize(LicenseInfo(), static_cast<PhoneHandler*>(NULL));

            JsonApiClient* jsonApiClient = JsonApiClient::getInterface(phone);
            EventHandler* jsonApiClientEventsPtr = new EventHandler("jsonApiClientEvents", dynamic_cast<CPCAPI2::AutoTestProcessor*>(jsonApiClient));

            // alice.config.useJsonProxy = true;
            JsonApiClientSettings jsonApiClientSettings;
            jsonApiClientSettings.serverUri = "wss://localhost:9003";
            jsonApiClientSettings.ignoreCertVerification = true;
            jsonApiClient->configureDefaultSettings(jsonApiClientSettings);
            jsonApiClient->enable();

            {
               JsonApiLoginHandle h;
               StatusChangedEvent args;
               cpcExpectEvent(jsonApiClientEventsPtr, "JsonApiClientHandler::onStatusChanged", 10000, CPCAPI2::test::AlwaysTruePred(), h, args);
               ASSERT_EQ(args.status, StatusChangedEvent::Status_Connecting);
               cpcExpectEvent(jsonApiClientEventsPtr, "JsonApiClientHandler::onStatusChanged", 10000, CPCAPI2::test::AlwaysTruePred(), h, args);
               ASSERT_EQ(args.status, StatusChangedEvent::Status_Connected);
            }

            resip::Data aliceJwt;
            std::stringstream ss;
            ss << "alice_" << i;
            generateJwt((TestEnvironmentConfig::testResourcePath() + "p256-private-key-unit-tests.p8").c_str(), ss.str().c_str(), aliceJwt);
            jsonApiClient->login(aliceJwt.c_str());

            {
               // Alice then gets the result
               JsonApiLoginHandle h;
               LoginResultEvent args;
               cpcExpectEvent(jsonApiClientEventsPtr, "JsonApiClientHandler::onLoginResult", 50000, CPCAPI2::test::AlwaysTruePred(), h, args);
            }

            // probably not necessary
            std::this_thread::sleep_for(std::chrono::milliseconds(500));

            jsonApiClient->disable();

            delete jsonApiClientEventsPtr;
            Phone::release(phone);
         }));
      }

      for (std::vector< std::future<void> >::const_iterator it = futures.begin(); it != futures.end(); ++it)
      {
         ASSERT_EQ(it->wait_for(std::chrono::milliseconds(20000)), std::future_status::ready);
      }
   }

   max.jsonApiServer->setHandler(NULL);
   maxProcessActive = false;
   max.jsonApiServer->shutdown();   
}

TEST_F(XmppAgentTests, MultipleAccountLoginAndPushRegistrations)
{
   // Setup the json server to mimic an xmpp agent
   XmppTestAccount agent("agent", Account_Init);
   XmppTestAccountConfig::setupXmppAgent(agent);

   // Establish connection using first account
   XmppTestAccount alice("alice", Account_Init);

   assertJsonClientLoginSuccess(alice, agent, [&](const CPCAPI2::JsonApi::LoginResultEvent& evt)
   {
      ASSERT_TRUE(evt.success);
   });

   PushNotificationEndpointHandle pushDevAlice = alice.createPushEndpointJson();
   PushNotificationEndpointId pushDevAliceId;
   assertEndpointPushRegistrationSuccess(alice, [&](const CPCAPI2::PushEndpoint::PushRegistrationSuccessEvent& evt)
   {
      ASSERT_FALSE(evt.endpointId.empty());
      pushDevAliceId = evt.endpointId;
   });

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   CPCAPI2::XmppAgent::XmppPushRegistrationHandle xmppPushAlice = alice.createAgentJson();
   assertAgentPushRegistrationSuccess(alice, [&](const CPCAPI2::XmppAgent::XmppPushRegistrationSuccessEvent& evt)
   {
      ASSERT_EQ(evt.pushEndpointId, pushDevAliceId);
   });

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   // Establish connection using second account
   XmppTestAccount bob("bob", Account_Init);

   assertJsonClientLoginSuccess(bob, agent, [&](const CPCAPI2::JsonApi::LoginResultEvent& evt)
   {
      ASSERT_TRUE(evt.success);
   });

   PushNotificationEndpointHandle pushDevBob = bob.createPushEndpointJson();
   PushNotificationEndpointId pushDevBobId;
   assertEndpointPushRegistrationSuccess(bob, [&](const CPCAPI2::PushEndpoint::PushRegistrationSuccessEvent& evt)
   {
      ASSERT_FALSE(evt.endpointId.empty());
      pushDevBobId = evt.endpointId;
   });

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   CPCAPI2::XmppAgent::XmppPushRegistrationHandle xmppPushBob = bob.createAgentJson();
   assertAgentPushRegistrationSuccess(bob, [&](const CPCAPI2::XmppAgent::XmppPushRegistrationSuccessEvent& evt)
   {
      ASSERT_EQ(evt.pushEndpointId, pushDevBobId);
   });

   alice.jsonApiClient->logout();

   // necessary to some degree; we want logout to finish and the user's service
   // side phone to destruct before tearing down the websocket
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   alice.jsonApiClient->disable();
   alice.setCloudConnected(false);

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   bob.jsonApiClient->logout();

   // necessary to some degree; we want logout to finish and the user's service
   // side phone to destruct before tearing down the websocket
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   bob.jsonApiClient->disable();
   bob.setCloudConnected(false);

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   XmppTestAccountConfig::destroyXmppAgent(agent);
}

TEST_F(XmppAgentTests, SameAccountMultipleWebSocketConnections)
{
   // Setup the json server to mimic an xmpp agent
   XmppTestAccount agent("agent", Account_Init);
   XmppTestAccountConfig::setupXmppAgent(agent);

   // Establish first web socket connection
   XmppTestAccount alice("alice", Account_Init);

   assertJsonClientLoginSuccess(alice, agent, [&](const CPCAPI2::JsonApi::LoginResultEvent& evt)
   {
      ASSERT_TRUE(evt.success);
   });

   PushNotificationEndpointHandle pushDevAlice = alice.createPushEndpointJson();
   PushNotificationEndpointId pushDevAliceId;
   assertEndpointPushRegistrationSuccess(alice, [&](const CPCAPI2::PushEndpoint::PushRegistrationSuccessEvent& evt)
   {
      ASSERT_FALSE(evt.endpointId.empty());
      pushDevAliceId = evt.endpointId;
   });

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   CPCAPI2::XmppAgent::XmppPushRegistrationHandle xmppPushAlice = alice.createAgentJson();
   assertAgentPushRegistrationSuccess(alice, [&](const CPCAPI2::XmppAgent::XmppPushRegistrationSuccessEvent& evt)
   {
      ASSERT_EQ(evt.pushEndpointId, pushDevAliceId);
   });

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   // Keep the existing web-socket open, and create a new one using the same auth-token
   CPCAPI2::Phone* oldPhone = alice.phone;
   CPCAPI2::JsonApi::JsonApiClient* oldJsonApiClient = alice.jsonApiClient;
   CPCAPI2::test::EventHandler* oldJsonApiClientEvents = alice.jsonApiClientEvents;

   alice.phone = Phone::create();
   alice.phone->initialize(LicenseInfo(), (PhoneHandler*)NULL);
   alice.phone->setLoggingEnabled(&AutoTestsLogger::instance(), true);
   alice.jsonApiClient = CPCAPI2::JsonApi::JsonApiClient::getInterface(alice.phone);
   alice.jsonApiClientEvents = new test::EventHandler((alice.config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(alice.jsonApiClient));

   CPCAPI2::Phone* newPhone = alice.phone;
   CPCAPI2::JsonApi::JsonApiClient* newJsonApiClient = alice.jsonApiClient;
   CPCAPI2::test::EventHandler* newJsonApiClientEvents = alice.jsonApiClientEvents;

   assertJsonClientLoginSuccess(alice, agent, [&](const CPCAPI2::JsonApi::LoginResultEvent& evt)
   {
      ASSERT_TRUE(evt.success);
   });

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   alice.jsonApiClient->logout();
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));
   alice.jsonApiClient->disable();
   alice.setCloudConnected(false);

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   
   oldJsonApiClient->logout();
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));
   oldJsonApiClient->disable();

   alice.phone = oldPhone;
   alice.jsonApiClient = oldJsonApiClient;
   alice.jsonApiClientEvents = oldJsonApiClientEvents;

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete newJsonApiClientEvents;
   newJsonApiClientEvents = NULL;
   CPCAPI2::Phone::release(newPhone);

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   XmppTestAccountConfig::destroyXmppAgent(agent);
}

TEST_F(XmppAgentTests, DanglingWebSocketConnection)
{
   // Setup the json server to mimic an xmpp agent
   XmppTestAccount agent("agent", Account_Init);
   XmppTestAccountConfig::setupXmppAgent(agent);

   // Establish first web socket connection
   XmppTestAccount alice("alice", Account_Init);

   assertJsonClientLoginSuccess(alice, agent, [&](const CPCAPI2::JsonApi::LoginResultEvent& evt)
   {
      ASSERT_TRUE(evt.success);
   });

   PushNotificationEndpointHandle pushDevAlice = alice.createPushEndpointJson();
   PushNotificationEndpointId pushDevAliceId;
   assertEndpointPushRegistrationSuccess(alice, [&](const CPCAPI2::PushEndpoint::PushRegistrationSuccessEvent& evt)
   {
      ASSERT_FALSE(evt.endpointId.empty());
      pushDevAliceId = evt.endpointId;
   });

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   CPCAPI2::XmppAgent::XmppPushRegistrationHandle xmppPushAlice = alice.createAgentJson();
   assertAgentPushRegistrationSuccess(alice, [&](const CPCAPI2::XmppAgent::XmppPushRegistrationSuccessEvent& evt)
   {
      ASSERT_EQ(evt.pushEndpointId, pushDevAliceId);
   });

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   // Keep the existing web-socket open, and create a new one using the same auth-token
   CPCAPI2::Phone* oldPhone = alice.phone;
   CPCAPI2::JsonApi::JsonApiClient* oldJsonApiClient = alice.jsonApiClient;
   CPCAPI2::test::EventHandler* oldJsonApiClientEvents = alice.jsonApiClientEvents;

   alice.phone = Phone::create();
   alice.phone->initialize(LicenseInfo(), (PhoneHandler*)NULL);
   alice.phone->setLoggingEnabled(&AutoTestsLogger::instance(), true);
   alice.jsonApiClient = CPCAPI2::JsonApi::JsonApiClient::getInterface(alice.phone);
   alice.jsonApiClientEvents = new test::EventHandler((alice.config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(alice.jsonApiClient));

   assertJsonClientLoginSuccess(alice, agent, [&](const CPCAPI2::JsonApi::LoginResultEvent& evt)
   {
      ASSERT_TRUE(evt.success);
   });

   CPCAPI2::Phone* newPhone = alice.phone;
   CPCAPI2::JsonApi::JsonApiClient* newJsonApiClient = alice.jsonApiClient;
   CPCAPI2::test::EventHandler* newJsonApiClientEvents = alice.jsonApiClientEvents;
   CPCAPI2::JsonApi::JsonApiUserHandle newJsonApiUserHandle = alice.jsonApiUserHandle;

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   alice.jsonApiClient->logout();
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));
   alice.jsonApiClient->disable();
   alice.setCloudConnected(false);

   alice.phone = oldPhone;
   alice.jsonApiClient = oldJsonApiClient;
   alice.jsonApiClientEvents = oldJsonApiClientEvents;

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete newJsonApiClientEvents;
   newJsonApiClientEvents = NULL;
   CPCAPI2::Phone::release(newPhone);

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   XmppTestAccountConfig::destroyXmppAgent(agent);
}

class MyAppRunner : public AppRunner
{

public:

   MyAppRunner(XmppTestAccount* account_, bool& destroyed_, resip::Condition& condition_, resip::Mutex& mutex_) :
   _account(account_),
   _destroyed(destroyed_),
   _condition(condition_),
   _mutex(mutex_) {}

   virtual~ MyAppRunner()
   {
      if (_account) delete _account;
      _account = NULL;
   }

   // AppRunner
   virtual int shutdown() OVERRIDE
   {
      resip::Lock lock(_mutex);
      safeCout("MyAppRunner::shutdown(): runner phone: " << _account->phone);
      delete _account;
      _account = NULL;
      _destroyed = true;
      _condition.signal();
      delete this;
      return kSuccess;
   }

   XmppTestAccount* _account;
   bool& _destroyed;
   resip::Condition& _condition;
   resip::Mutex& _mutex;

};

TEST_F(XmppAgentTests, AppRunnerShutdown)
{
   // Setup the json server to mimic an xmpp agent
   XmppTestAccount agent("agent", Account_Init);
   XmppTestAccountConfig::setupXmppAgent(agent);
   XmppTestAccount* aliceRemote = NULL;
   MyAppRunner* runner = NULL;
   bool destroyed_ = false;
   resip::Condition condition_;
   resip::Mutex mutex_;
   std::string userIdentity("");

   // Establish first web socket connection
   XmppTestAccount alice("alice", Account_Init);

   JsonApi::LoginResultEvent args;
   auto login = std::async(std::launch::async, [&]()
   {
      // Establish connection to the agent using the user account
      JsonApiClientSettings jsonApiClientSettings;
      jsonApiClientSettings.serverUri = "wss://localhost:9003";
      jsonApiClientSettings.ignoreCertVerification = true;
      alice.jsonApiClient->configureDefaultSettings(jsonApiClientSettings);
      alice.jsonApiClient->enable();

      {
         JsonApiLoginHandle h;
         StatusChangedEvent statusChangedEvt;
         cpcExpectEvent(alice.jsonApiClientEvents, "JsonApiClientHandler::onStatusChanged", 10000, CPCAPI2::test::AlwaysTruePred(), h, statusChangedEvt);
         ASSERT_EQ(statusChangedEvt.status, StatusChangedEvent::Status_Connecting);
         cpcExpectEvent(alice.jsonApiClientEvents, "JsonApiClientHandler::onStatusChanged", 10000, CPCAPI2::test::AlwaysTruePred(), h, statusChangedEvt);
         ASSERT_EQ(statusChangedEvt.status, StatusChangedEvent::Status_Connected);
         alice.setCloudConnected(true);
      }

      // Initiate json client account login
      resip::Data accountJwt;
      XmppTestAccountConfig::generateJwt((TestEnvironmentConfig::testResourcePath() + "p256-private-key-unit-tests.p8").c_str(), alice.config.name.c_str(), accountJwt);
      alice.config.authToken = accountJwt.c_str();
      alice.jsonApiClient->login(accountJwt.c_str());

      {
         // Account then gets the result
         JsonApiLoginHandle h;
         cpcExpectEvent(alice.jsonApiClientEvents, "JsonApiClientHandler::onLoginResult", 50000, CPCAPI2::test::AlwaysTruePred(), h, args);
      }
   });

   auto handleLogin = std::async(std::launch::async, [&]()
   {
      JsonApiUserHandle jsonApiUser;
      NewLoginEvent newLoginEvent;

      // Agent has to process the login attempt (associate the context with an SDK instance)
      cpcExpectEvent(agent.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 5000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, newLoginEvent);
      ASSERT_NE(newLoginEvent.authToken.size(), 0);
      userIdentity = newLoginEvent.userIdentity;

      aliceRemote = new XmppTestAccount("aliceRemote", Account_NoInit);
      runner = new MyAppRunner(aliceRemote, destroyed_, condition_, mutex_);
      aliceRemote->init();

      CPCAPI2::JsonApi::JsonApiServerSendTransport* jsonServerTransport = CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(aliceRemote->phone);
      CPCAPI2::XmppAgent::XmppAgentManagerInternal* xmppAgentManager = CPCAPI2::XmppAgent::XmppAgentManagerInternal::getInternalInterface(aliceRemote->phone);
      CPCAPI2::PushEndpoint::PushNotificationEndpointManagerInternal* pushEndpointManager = CPCAPI2::PushEndpoint::PushNotificationEndpointManagerInternal::getInternalInterface(aliceRemote->phone);

      jsonServerTransport->setJsonApiServer(agent.jsonApiServer);
      xmppAgentManager->setPushNotificationManager(agent.pushServer);
      xmppAgentManager->setJsonApiServer(agent.jsonApiServer);
      pushEndpointManager->setPushNotificationService(agent.pushServer);

      cpc::vector<cpc::string> permissions; permissions.push_back("*");
      agent.jsonApiServer->setJsonApiUserContext(jsonApiUser, aliceRemote->phone, permissions);
      LoginResultEvent loginResultEvent;
      loginResultEvent.success = true;
      loginResultEvent.requestId = newLoginEvent.requestId;
      agent.jsonApiServer->sendLoginResult(jsonApiUser, loginResultEvent);
   });

   waitFor2(login, handleLogin);

   alice.jsonApiClient->logout();

   auto handleLogout = std::async(std::launch::async, [&]()
   {
      JsonApiUserHandle jsonApiUser;
      LogoutEvent logoutEvent;

      cpcExpectEvent(agent.jsonApiServerEvents, "JsonApiServerHandler::onLogout", 5000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, logoutEvent);
      std::string logoutIdentity = logoutEvent.userIdentity.c_str();
      ASSERT_EQ(userIdentity, logoutIdentity);
   });

   waitFor(handleLogout);

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));
   alice.jsonApiClient->disable();
   alice.setCloudConnected(false);

   auto handleSessionState = std::async(std::launch::async, [&]()
   {
      JsonApiUserHandle jsonApiUser;
      SessionStateEvent sessionStateEvent;

      cpcExpectEvent(agent.jsonApiServerEvents, "JsonApiServerHandler::onSessionState", 5000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, sessionStateEvent);
      ASSERT_FALSE(sessionStateEvent.isActive);

      cpc::vector<CPCAPI2::JsonApi::JsonApiUserHandle> jsonUsers;
      jsonUsers.push_back(jsonApiUser);
      static_cast<JsonApiServerInternal*>(agent.jsonApiServer)->destroyRunner(new CPCAPI2::JsonApi::JsonApiServerInternal::RunnerInfo(runner, jsonUsers));
      condition_.wait(mutex_, 5000);
      ASSERT_TRUE(destroyed_);
   });

   waitFor(handleSessionState);

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   XmppTestAccountConfig::destroyXmppAgent(agent);
}

TEST_F(XmppAgentTests, DISABLED_AppRunnerShutdownMultipleWebSocketConnections)
{
   // Setup the json server to mimic an xmpp agent
   XmppTestAccount agent("agent", Account_Init);
   XmppTestAccountConfig::setupXmppAgent(agent);
   XmppTestAccount* aliceRemote = NULL;
   MyAppRunner* runner = NULL;
   bool destroyed_ = false;
   resip::Condition condition_;
   resip::Mutex mutex_;
   std::string authToken("");
   std::string userIdentity("");
   std::string remoteAddress("");
   cpc::vector<CPCAPI2::JsonApi::JsonApiUserHandle> jsonUsers;

   // Establish first web socket connection
   XmppTestAccount alice("alice", Account_Init);

   JsonApi::LoginResultEvent args;
   auto login = std::async(std::launch::async, [&]()
   {
      // Establish connection to the agent using the user account
      JsonApiClientSettings jsonApiClientSettings;
      jsonApiClientSettings.serverUri = "wss://localhost:9003";
      jsonApiClientSettings.ignoreCertVerification = true;
      alice.jsonApiClient->configureDefaultSettings(jsonApiClientSettings);
      alice.jsonApiClient->enable();

      {
         JsonApiLoginHandle h;
         StatusChangedEvent statusChangedEvt;
         cpcExpectEvent(alice.jsonApiClientEvents, "JsonApiClientHandler::onStatusChanged", 10000, CPCAPI2::test::AlwaysTruePred(), h, statusChangedEvt);
         ASSERT_EQ(statusChangedEvt.status, StatusChangedEvent::Status_Connecting);
         cpcExpectEvent(alice.jsonApiClientEvents, "JsonApiClientHandler::onStatusChanged", 10000, CPCAPI2::test::AlwaysTruePred(), h, statusChangedEvt);
         ASSERT_EQ(statusChangedEvt.status, StatusChangedEvent::Status_Connected);
         alice.setCloudConnected(true);
      }

      // Initiate json client account login
      resip::Data accountJwt;
      XmppTestAccountConfig::generateJwt((TestEnvironmentConfig::testResourcePath() + "p256-private-key-unit-tests.p8").c_str(), alice.config.name.c_str(), accountJwt);
      alice.config.authToken = accountJwt.c_str();
      alice.jsonApiClient->login(accountJwt.c_str());

      {
         // Account then gets the result
         JsonApiLoginHandle h;
         cpcExpectEvent(alice.jsonApiClientEvents, "JsonApiClientHandler::onLoginResult", 50000, CPCAPI2::test::AlwaysTruePred(), h, args);
      }
   });

   auto handleLogin = std::async(std::launch::async, [&]()
   {
      JsonApiUserHandle jsonApiUser;
      NewLoginEvent newLoginEvent;

      // Agent has to process the login attempt (associate the context with an SDK instance)
      cpcExpectEvent(agent.jsonApiServerEvents, "JsonApiServerHandler::onNewLogin", 5000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, newLoginEvent);
      ASSERT_NE(newLoginEvent.authToken.size(), 0);
      authToken = newLoginEvent.authToken;
      userIdentity = newLoginEvent.userIdentity;

      aliceRemote = new XmppTestAccount("aliceRemote", Account_NoInit);
      runner = new MyAppRunner(aliceRemote, destroyed_, condition_, mutex_);
      aliceRemote->init();

      CPCAPI2::JsonApi::JsonApiServerSendTransport* jsonServerTransport = CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(aliceRemote->phone);
      CPCAPI2::XmppAgent::XmppAgentManagerInternal* xmppAgentManager = CPCAPI2::XmppAgent::XmppAgentManagerInternal::getInternalInterface(aliceRemote->phone);
      CPCAPI2::PushEndpoint::PushNotificationEndpointManagerInternal* pushEndpointManager = CPCAPI2::PushEndpoint::PushNotificationEndpointManagerInternal::getInternalInterface(aliceRemote->phone);

      jsonServerTransport->setJsonApiServer(agent.jsonApiServer);
      xmppAgentManager->setPushNotificationManager(agent.pushServer);
      xmppAgentManager->setJsonApiServer(agent.jsonApiServer);
      pushEndpointManager->setPushNotificationService(agent.pushServer);

      cpc::vector<cpc::string> permissions; permissions.push_back("*");
      agent.jsonApiServer->setJsonApiUserContext(jsonApiUser, aliceRemote->phone, permissions);
      LoginResultEvent loginResultEvent;
      loginResultEvent.success = true;
      loginResultEvent.requestId = newLoginEvent.requestId;
      agent.jsonApiServer->sendLoginResult(jsonApiUser, loginResultEvent);
      jsonUsers.push_back(jsonApiUser);
      alice.jsonApiUserHandle = jsonApiUser;
   });

   waitFor2(login, handleLogin);

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   // Keep the existing web-socket open, and create several more using the same auth-token
   CPCAPI2::Phone* oldPhone = alice.phone;
   CPCAPI2::JsonApi::JsonApiClient* oldJsonApiClient = alice.jsonApiClient;
   CPCAPI2::test::EventHandler* oldJsonApiClientEvents = alice.jsonApiClientEvents;
   CPCAPI2::JsonApi::JsonApiUserHandle oldJsonApiUserHandle = alice.jsonApiUserHandle;

   alice.phone = Phone::create();
   alice.phone->initialize(LicenseInfo(), (PhoneHandler*)NULL);
   alice.phone->setLoggingEnabled(&AutoTestsLogger::instance(), true);
   alice.jsonApiClient = CPCAPI2::JsonApi::JsonApiClient::getInterface(alice.phone);
   alice.jsonApiClientEvents = new test::EventHandler((alice.config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(alice.jsonApiClient));

   assertJsonClientLoginSuccess(alice, agent, [&](const CPCAPI2::JsonApi::LoginResultEvent& evt)
   {
      ASSERT_TRUE(evt.success);
      jsonUsers.push_back(alice.jsonApiUserHandle);
      agent.jsonApiServer->updateUser(oldJsonApiUserHandle, JsonApi::UpdateUserEvent(alice.jsonApiUserHandle, authToken.c_str(), userIdentity.c_str()));
   });

   CPCAPI2::Phone* newPhone = alice.phone;
   CPCAPI2::JsonApi::JsonApiClient* newJsonApiClient = alice.jsonApiClient;
   CPCAPI2::test::EventHandler* newJsonApiClientEvents = alice.jsonApiClientEvents;
   CPCAPI2::JsonApi::JsonApiUserHandle newJsonApiUserHandle = alice.jsonApiUserHandle;

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   alice.phone = Phone::create();
   alice.phone->initialize(LicenseInfo(), (PhoneHandler*)NULL);
   alice.phone->setLoggingEnabled(&AutoTestsLogger::instance(), true);
   alice.jsonApiClient = CPCAPI2::JsonApi::JsonApiClient::getInterface(alice.phone);
   alice.jsonApiClientEvents = new test::EventHandler((alice.config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(alice.jsonApiClient));

   assertJsonClientLoginSuccess(alice, agent, [&](const CPCAPI2::JsonApi::LoginResultEvent& evt)
   {
      ASSERT_TRUE(evt.success);
      jsonUsers.push_back(alice.jsonApiUserHandle);
      agent.jsonApiServer->updateUser(newJsonApiUserHandle, JsonApi::UpdateUserEvent(alice.jsonApiUserHandle, authToken.c_str(), userIdentity.c_str()));
   });

   CPCAPI2::Phone* newPhone2 = alice.phone;
   CPCAPI2::JsonApi::JsonApiClient* newJsonApiClient2 = alice.jsonApiClient;
   CPCAPI2::test::EventHandler* newJsonApiClientEvents2 = alice.jsonApiClientEvents;
   CPCAPI2::JsonApi::JsonApiUserHandle newJsonApiUserHandle2 = alice.jsonApiUserHandle;

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   alice.phone = Phone::create();
   alice.phone->initialize(LicenseInfo(), (PhoneHandler*)NULL);
   alice.phone->setLoggingEnabled(&AutoTestsLogger::instance(), true);
   alice.jsonApiClient = CPCAPI2::JsonApi::JsonApiClient::getInterface(alice.phone);
   alice.jsonApiClientEvents = new test::EventHandler((alice.config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(alice.jsonApiClient));

   assertJsonClientLoginSuccess(alice, agent, [&](const CPCAPI2::JsonApi::LoginResultEvent& evt)
   {
      ASSERT_TRUE(evt.success);
      jsonUsers.push_back(alice.jsonApiUserHandle);
      agent.jsonApiServer->updateUser(newJsonApiUserHandle2, JsonApi::UpdateUserEvent(alice.jsonApiUserHandle, authToken.c_str(), userIdentity.c_str()));
   });

   CPCAPI2::Phone* newPhone3 = alice.phone;
   CPCAPI2::JsonApi::JsonApiClient* newJsonApiClient3 = alice.jsonApiClient;
   CPCAPI2::test::EventHandler* newJsonApiClientEvents3 = alice.jsonApiClientEvents;
   CPCAPI2::JsonApi::JsonApiUserHandle newJsonApiUserHandle3 = alice.jsonApiUserHandle;

   // Disable one of the connections, so the server still has additional connections to the same context
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));
   alice.jsonApiClient->disable();
   alice.setCloudConnected(false);

   auto handleDisable = std::async(std::launch::async, [&]()
   {
      JsonApiUserHandle jsonApiUser;
      SessionStateEvent sessionStateEvent;

      cpcExpectEvent(agent.jsonApiServerEvents, "JsonApiServerHandler::onSessionState", 5000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, sessionStateEvent);
      ASSERT_FALSE(sessionStateEvent.isActive);
      ASSERT_EQ(jsonApiUser, newJsonApiUserHandle3);
      agent.jsonApiServer->destroyUser(jsonApiUser);

      // Reset to the original json client
      alice.phone = oldPhone;
      alice.jsonApiClient = oldJsonApiClient;
      alice.jsonApiClientEvents = oldJsonApiClientEvents;
   });

   waitFor(handleDisable);

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   // Logout from the context, this should close all associated connections and trigger
   // the destruction of the runner from the session state callback
   alice.jsonApiClient->logout();

   auto handleLogout = std::async(std::launch::async, [&]()
   {
      JsonApiUserHandle jsonApiUser;
      LogoutEvent logoutEvent;

      cpcExpectEvent(agent.jsonApiServerEvents, "JsonApiServerHandler::onLogout", 5000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, logoutEvent);
      std::string logoutIdentity = logoutEvent.userIdentity.c_str();
      ASSERT_EQ(jsonApiUser, oldJsonApiUserHandle);
      ASSERT_EQ(userIdentity, logoutIdentity);
   });

   waitFor(handleLogout);

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));
   alice.jsonApiClient->disable();
   alice.setCloudConnected(false);

   auto handleSessionState = std::async(std::launch::async, [&]()
   {
      JsonApiUserHandle jsonApiUser;
      SessionStateEvent sessionStateEvent;

      cpcExpectEvent(agent.jsonApiServerEvents, "JsonApiServerHandler::onSessionState", 5000, CPCAPI2::test::AlwaysTruePred(), jsonApiUser, sessionStateEvent);
      ASSERT_FALSE(sessionStateEvent.isActive);

      static_cast<JsonApiServerInternal*>(agent.jsonApiServer)->destroyRunner(new CPCAPI2::JsonApi::JsonApiServerInternal::RunnerInfo(runner, jsonUsers));
      condition_.wait(mutex_, 10000);
      ASSERT_TRUE(destroyed_);
   });

   waitFor(handleSessionState);

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   // Disable the web socket connections on client side, note that the server side would
   // have already closed the connections due to the runner destruction
   newJsonApiClient->disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   newJsonApiClient2->disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   newJsonApiClient3->disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete newJsonApiClientEvents;
   newJsonApiClientEvents = NULL;
   CPCAPI2::Phone::release(newPhone);

   delete newJsonApiClientEvents2;
   newJsonApiClientEvents2 = NULL;
   CPCAPI2::Phone::release(newPhone2);

   delete newJsonApiClientEvents3;
   newJsonApiClientEvents3 = NULL;
   CPCAPI2::Phone::release(newPhone3);

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   XmppTestAccountConfig::destroyXmppAgent(agent);
}

#endif
