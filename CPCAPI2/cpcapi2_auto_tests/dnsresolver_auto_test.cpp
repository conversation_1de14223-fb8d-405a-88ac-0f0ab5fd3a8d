#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#include "cpcapi2_test_fixture.h"
#include "test_call_events.h"
#include "test_events.h"

#include <gtest/gtest.h>
#include <cpcapi2.h>

#include "test_framework/cpcapi2_test_framework.h"
#include "test_account_events.h"
#include "test_framework/xmpp_test_helper.h"
#include "util/DnsClient.h"
#include "util/SecureDns.h"

using namespace CPCAPI2;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::XmppAccount;
using namespace CPCAPI2::Utils;
using namespace CPCAPI2::test;

namespace {

   class DnsResolverTest : public CpcapiAutoTest
   {
   public:
      DnsResolverTest() {}
      virtual ~DnsResolverTest() {}
   };

   TEST_F(DnsResolverTest, DnsClientTest)
   {
      resip::DnsStub::DnsSettings settings;
      DnsClient client(settings);
      
      resip::DnsStub::enableDnsFeatures(resip::ExternalDns::Features::UseExternalResolver);
      DnsAorAAAARecord record = client.getDnsAorAAAARecord("counterpath.com", SipAccount::IpVersion_V4);

      ASSERT_TRUE(record.valid);
      ASSERT_TRUE(record.ipAddr.length() > 0);
      ASSERT_TRUE(record.ipAddr.isVersion4());
   }

   char* bogus = "*********";
   TEST_F(DnsResolverTest, SipTest)
   {
      SipAccountHandle h;
      SipAccountStatusChangedEvent evt;
      LicenseInfo li;
      ConnectionPreferences cp;

      TestAccountConfig config("alice");
      config.settings.domain = "opsip.silverstar.counterpath.net:5070";
      config.settings.outboundProxy = "";
      config.settings.nameServers.push_back(bogus);

      Phone *p = NULL;
      PhoneInternal *pi = NULL;

      cp.externalResolver = ConnectionPreferences::ExternalResolverUsage_Disable;
      p = Phone::create();
      pi = static_cast<PhoneInternal*>(p);
      pi->initialize(li, NULL, cp);

      TestAccount alice("alice", Account_NoInit, true, p, NULL, true);
      alice.config = config;
      alice.init();

      ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);
      assertAccountRegistering(alice);
      ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged", 20000, AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
      ASSERT_EQ(503, evt.signalingStatusCode);

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      alice.disable();
      alice.shutdown();

      cp.externalResolver = ConnectionPreferences::ExternalResolverUsage_Force;
      p = Phone::create();
      pi = static_cast<PhoneInternal*>(p);
      pi->initialize(li, NULL, cp);

      TestAccount alicEx("alicEx", Account_NoInit, true, p, NULL, true);
      alicEx.config = config;
      alicEx.init();

      ASSERT_EQ(alicEx.account->enable(alicEx.handle), kSuccess);
      assertAccountRegistering(alicEx);
      ASSERT_TRUE(cpcExpectEvent(alicEx.accountEvents, "SipAccountHandler::onAccountStatusChanged", 20000, AlwaysTruePred(), h, evt));
      ASSERT_EQ(alicEx.handle, h);
      ASSERT_EQ(SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
      ASSERT_EQ(200, evt.signalingStatusCode);

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      alicEx.disable();
      alicEx.shutdown();
   }

   TEST_F(DnsResolverTest, XmppTest)
   {
      XmppAccountHandle h;
      XmppAccountStatusChangedEvent evt;
      LicenseInfo li;
      ConnectionPreferences cp;

      Phone *p = NULL;
      PhoneInternal *pi = NULL;

      cp.externalResolver = ConnectionPreferences::ExternalResolverUsage_Disable;
      p = Phone::create();
      pi = static_cast<PhoneInternal*>(p);
      pi->initialize(li, NULL, cp);

      XmppTestAccount alice("alice", Account_NoInit, "", p);
      alice.config.settings.nameServers.push_back(bogus);
      alice.init();

      ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);
      assertXmppConnecting(alice);
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppAccountHandler::onAccountStatusChanged", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(XmppAccountStatusChangedEvent::Status_Failure, evt.accountStatus);

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      alice.disable();
      alice.destroy();

      cp.externalResolver = ConnectionPreferences::ExternalResolverUsage_Force;
      p = Phone::create();
      pi = static_cast<PhoneInternal*>(p);
      pi->initialize(li, NULL, cp);

      XmppTestAccount alicEx("alicEx", Account_NoInit, "", p);
      alicEx.config.settings.nameServers.push_back(bogus);
      alicEx.init();
      alicEx.enable();

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      alicEx.disable();
      alicEx.destroy();
   }

   TEST_F(DnsResolverTest, noCurloptResolveTest)
   {
      LicenseInfo li;
      ConnectionPreferences cp;
      struct curl_slist *hostaddrs = NULL;
      char *host = "http://counterpath.com";

      cp.externalResolver = ConnectionPreferences::ExternalResolverUsage_Disable;
      Phone *p = Phone::create();
      PhoneInternal *pi = static_cast<PhoneInternal*>(p);
      pi->initialize(li, NULL, cp);

      ASSERT_FALSE(Utils::SecureDns::doCurloptResolve(host, hostaddrs));

      curl_slist_free_all(hostaddrs);
   }

   TEST_F(DnsResolverTest, doCurloptResolveTest)
   {
      LicenseInfo li;
      ConnectionPreferences cp;
      struct curl_slist *hostaddrs = NULL;
      char *host = "http://counterpath.com";

      cp.externalResolver = ConnectionPreferences::ExternalResolverUsage_Force;
      Phone *p = Phone::create();
      PhoneInternal *pi = static_cast<PhoneInternal*>(p);
      pi->initialize(li, NULL, cp);

      ASSERT_TRUE(Utils::SecureDns::doCurloptResolve(host, hostaddrs));

      curl_slist_free_all(hostaddrs);
   }
}
