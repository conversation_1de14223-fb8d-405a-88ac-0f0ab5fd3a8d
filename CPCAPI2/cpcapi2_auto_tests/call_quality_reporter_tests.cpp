#if _WIN32
#include "stdafx.h"
#include "TCHAR.h"
#include "pdh.h"
#else
#include "brand_branded.h"
#endif

#ifdef __APPLE__
#include <mach/mach_init.h>
#include <mach/mach_error.h>
#include <mach/mach_host.h>
#include <mach/vm_map.h>
#include "utils/mac_proc_utils.h"
#endif

#ifdef _WIN32
#include "utils/win_proc_utils.h"
#endif


#include "rutil/ConfigParse.hxx"

#include "cpcapi2_test_fixture.h"
#include "test_call_events.h"
#include "test_events.h"
#include "test_framework/network_utils.h"

using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::SipConversation;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::CallQuality;
using namespace CPCAPI2::SipEvent;

const int kCpcStrNpos = -1; // workaround for kCpcStrNpos being undefined on xcode builds

class CallQualityReporterTests : public CpcapiAutoTest
{
public:
   CallQualityReporterTests() {}
   virtual ~CallQualityReporterTests() {}
};

struct AudioCodecInfo
{
   cpc::string name;
   cpc::string pd;
   int pps;
};

class CallQualityReporterTests_P : public CpcapiAutoTestWithParam<AudioCodecInfo>
{
public:
   CallQualityReporterTests_P() {}
   virtual ~CallQualityReporterTests_P() {}
};

#if CPCAPI2_BRAND_CALL_QUALITY_REPORT_MODULE == 1
TEST_F(CallQualityReporterTests, BasicAudioCallReporterPublish) {
   TestAccount bob("bob");

   TestAccount alice("alice", Account_Init);
   CallQualityReporterHandle aliceCqr = alice.createCallQualityReporter();
   CallQualityReporterConfig aliceCqrConfig;
   aliceCqrConfig.reportingIntervalSeconds = 20;
   aliceCqrConfig.reportingServiceSipUri = bob.config.uri();
   aliceCqrConfig.sipAccount = alice.handle;
   aliceCqrConfig.ignoreFailures = true;
   alice.callQualityReport->configureCallQualityReporter(aliceCqr, aliceCqrConfig);
   alice.callQualityReport->startCallQualityReporter(aliceCqr);
   alice.enable();

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, true);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertSuccess(alice.conversation->refreshConversationStatistics(aliceCall, true, true, true));
      {
         SipConversationHandle h;
         ConversationStatisticsUpdatedEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent("SipConversationHandler::onConversationStatisticsUpdated",
            15000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
         ASSERT_EQ(1, evt.conversationStatistics.audioChannels.size());
         ASSERT_NE(evt.conversationStatistics.audioChannels[0].streamDataCounters.packetsReceived, 0);
         ASSERT_NE(evt.conversationStatistics.audioChannels[0].streamDataCounters.packetsSent, 0);
      }

      {
         CallQualityReporterHandle h;
         CallQualityReportGeneratedEvent evt;
         ASSERT_TRUE(alice.callQualityEvents->expectEvent("CallQualityReportHandler::onCallQualityReportGenerated",
            60000, HandleEqualsPred<CallQualityReporterHandle>(aliceCqr), h, evt));
         ASSERT_NE(evt.callQualityReport.find("OrigID: " + alice.config.uri()), kCpcStrNpos);
         ASSERT_NE(evt.callQualityReport.find("LocalID: " + alice.config.uri()), kCpcStrNpos);
         ASSERT_NE(evt.callQualityReport.find("RemoteID: " + bob.config.uri()), kCpcStrNpos);
         ASSERT_NE(evt.callQualityReport.find("LocalGroup: " + alice.config.settings.localGroup), kCpcStrNpos);
         ASSERT_NE(evt.callQualityReport.find("RemoteGroup: \r\n"), kCpcStrNpos);
      }

      {
         CallQualityReporterHandle h;
         CallQualityReportGeneratedEvent evt;
         ASSERT_TRUE(alice.callQualityEvents->expectEvent("CallQualityReportHandler::onCallQualityReportGenerated",
            60000, HandleEqualsPred<CallQualityReporterHandle>(aliceCqr), h, evt));
         ASSERT_NE(evt.callQualityReport.find("OrigID: " + alice.config.uri()), kCpcStrNpos);
         ASSERT_NE(evt.callQualityReport.find("LocalID: " + alice.config.uri()), kCpcStrNpos);
         ASSERT_NE(evt.callQualityReport.find("RemoteID: " + bob.config.uri()), kCpcStrNpos);
         ASSERT_NE(evt.callQualityReport.find("LocalGroup: " + alice.config.settings.localGroup), kCpcStrNpos);
         ASSERT_NE(evt.callQualityReport.find("RemoteGroup: \r\n"), kCpcStrNpos);
      }


      {
         CallQualityReporterHandle h;
         CallQualityReportGeneratedEvent evt;
         ASSERT_TRUE(alice.callQualityEvents->expectEvent("CallQualityReportHandler::onCallQualityReportGenerated",
            60000, HandleEqualsPred<CallQualityReporterHandle>(aliceCqr), h, evt));
         ASSERT_NE(evt.callQualityReport.find("OrigID: " + alice.config.uri()), kCpcStrNpos);
         ASSERT_NE(evt.callQualityReport.find("LocalID: " + alice.config.uri()), kCpcStrNpos);
         ASSERT_NE(evt.callQualityReport.find("RemoteID: " + bob.config.uri()), kCpcStrNpos);
         ASSERT_NE(evt.callQualityReport.find("LocalGroup: " + alice.config.settings.localGroup), kCpcStrNpos);
         ASSERT_NE(evt.callQualityReport.find("RemoteGroup: \r\n"), kCpcStrNpos);
      }



      //std::this_thread::sleep_for(std::chrono::seconds(60));

      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);

      alice.callQualityReport->stopCallQualityReporter(aliceCqr);
      std::cout << "alice thread exit" << std::endl;
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      assertConversationEnded_time(bob, bobCall, ConversationEndReason_UserTerminatedRemotely, 180000);
      std::cout << "bob thread exit" << std::endl;
   });

   waitFor2Ms(aliceEvents, bobEvents, std::chrono::minutes(4));
   bob.disable();
   alice.disable();
}

TEST_F(CallQualityReporterTests, BasicAudioCallReporterPublishMultiAccounts) {
   TestAccount bob("bob");

   TestAccount alice("alice", Account_Init);
   CallQualityReporterHandle aliceCqr = alice.createCallQualityReporter();
   CallQualityReporterConfig aliceCqrConfig;
   aliceCqrConfig.reportingIntervalSeconds = 10;
   aliceCqrConfig.reportingServiceSipUri = "sip:<EMAIL>";
   aliceCqrConfig.sipAccount = alice.handle;
   aliceCqrConfig.ignoreFailures = true;
   alice.callQualityReport->configureCallQualityReporter(aliceCqr, aliceCqrConfig);
   alice.callQualityReport->startCallQualityReporter(aliceCqr);
   alice.enable();
   
   // slave
   SipAccountHandle aliceSecondaryAccount = alice.account->create();
   SipAccountSettings aliceSecondaryConfig = alice.config.settings;
   aliceSecondaryConfig.username = alice.config.settings.username + "_2";
   
   alice.account->configureDefaultAccountSettings(aliceSecondaryAccount, aliceSecondaryConfig);
   alice.account->applySettings(aliceSecondaryAccount);
   
   CallQualityReporterHandle aliceCqrSecondary = alice.createCallQualityReporter();
   CallQualityReporterConfig aliceCqrConfigSecondary;
   aliceCqrConfigSecondary.reportingIntervalSeconds = 10;
   aliceCqrConfigSecondary.reportingServiceSipUri = "sip:<EMAIL>";
   aliceCqrConfigSecondary.sipAccount = aliceSecondaryAccount;
   aliceCqrConfigSecondary.ignoreFailures = true;
   alice.callQualityReport->configureCallQualityReporter(aliceCqrSecondary, aliceCqrConfigSecondary);
   alice.callQualityReport->startCallQualityReporter(aliceCqrSecondary);
   alice.account->enable(aliceSecondaryAccount);
   
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   
   
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, true);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertSuccess(alice.conversation->refreshConversationStatistics(aliceCall, true, true, true));
      {
         SipConversationHandle h;
         ConversationStatisticsUpdatedEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent("SipConversationHandler::onConversationStatisticsUpdated",
            15000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
         ASSERT_EQ(1, evt.conversationStatistics.audioChannels.size());
         ASSERT_NE(evt.conversationStatistics.audioChannels[0].streamDataCounters.packetsReceived, 0);
         ASSERT_NE(evt.conversationStatistics.audioChannels[0].streamDataCounters.packetsSent, 0);
      }

      {
         CallQualityReporterHandle h;
         CallQualityReportGeneratedEvent evt;
         ASSERT_TRUE(alice.callQualityEvents->expectEvent("CallQualityReportHandler::onCallQualityReportGenerated",
            60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(h, aliceCqr);

      }

      {
         CallQualityReporterHandle h;
         CallQualityReportGeneratedEvent evt;
         ASSERT_TRUE(alice.callQualityEvents->expectEvent("CallQualityReportHandler::onCallQualityReportGenerated",
            60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(h, aliceCqr);
      }


      {
         CallQualityReporterHandle h;
         CallQualityReportGeneratedEvent evt;
         ASSERT_TRUE(alice.callQualityEvents->expectEvent("CallQualityReportHandler::onCallQualityReportGenerated",
            60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(h, aliceCqr);
      }



      //std::this_thread::sleep_for(std::chrono::seconds(60));

      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);

      alice.callQualityReport->stopCallQualityReporter(aliceCqr);
      std::cout << "alice thread exit" << std::endl;
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      assertConversationEnded_time(bob, bobCall, ConversationEndReason_UserTerminatedRemotely, 180000);
      std::cout << "bob thread exit" << std::endl;
   });

   waitFor2(aliceEvents, bobEvents);
   bob.disable();
   alice.disable();
}

TEST_F(CallQualityReporterTests, MultipleReporters) {
   TestAccount bob("bob");

   TestAccount alice("alice", Account_Init);
   CallQualityReporterHandle aliceCqr = alice.createCallQualityReporter();
   CallQualityReporterConfig aliceCqrConfig;
   aliceCqrConfig.reportingIntervalSeconds = 20;
   aliceCqrConfig.reportingServiceSipUri = bob.config.uri();
   aliceCqrConfig.sipAccount = alice.handle;
   aliceCqrConfig.ignoreFailures = true;
   alice.callQualityReport->configureCallQualityReporter(aliceCqr, aliceCqrConfig);
   alice.callQualityReport->startCallQualityReporter(aliceCqr);
   CallQualityReporterHandle aliceCqrLarge = alice.createCallQualityReporter();
   aliceCqrConfig.reportingIntervalSeconds = 30;
   alice.callQualityReport->configureCallQualityReporter(aliceCqrLarge, aliceCqrConfig);
   alice.callQualityReport->startCallQualityReporter(aliceCqrLarge);
   alice.enable();

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, true);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]() {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertSuccess(alice.conversation->refreshConversationStatistics(aliceCall, true, true, true));
      {
         SipConversationHandle h;
         ConversationStatisticsUpdatedEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent("SipConversationHandler::onConversationStatisticsUpdated",
            15000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
         ASSERT_EQ(1, evt.conversationStatistics.audioChannels.size());
         ASSERT_NE(evt.conversationStatistics.audioChannels[0].streamDataCounters.packetsReceived, 0);
         ASSERT_NE(evt.conversationStatistics.audioChannels[0].streamDataCounters.packetsSent, 0);
      }

      //{
      //   CallQualityReporterHandle h;
      //   CallQualityReportFailureEvent evt;
      //   ASSERT_TRUE(alice.callQualityEvents->expectEvent("CallQualityReportHandler::onCallQualityReportFailure",
      //      30000, HandleEqualsPred<CallQualityReporterHandle>(aliceCqr), h, evt));
      //}

      // at the 20 second mark
      {
         CallQualityReporterHandle h;
         CallQualityReportGeneratedEvent evt;
         ASSERT_TRUE(alice.callQualityEvents->expectEvent("CallQualityReportHandler::onCallQualityReportGenerated",
            60000, HandleEqualsPred<CallQualityReporterHandle>(aliceCqr), h, evt));
      }

      // at the 30 second mark
      {
         CallQualityReporterHandle h;
         CallQualityReportGeneratedEvent evt;
         ASSERT_TRUE(alice.callQualityEvents->expectEvent("CallQualityReportHandler::onCallQualityReportGenerated",
            60000, HandleEqualsPred<CallQualityReporterHandle>(aliceCqrLarge), h, evt));
      }

      //{
      //   SipEventPublicationHandle h;
      //   PublicationFailureEvent evt;
      //   alice.events->expectEvent("SipEventPublicationHandler::onPublicationFailure", 30000, AlwaysTruePred(), h, evt);
      //}
      //{
      //   CallQualityReporterHandle h;
      //   CallQualityReportFailureEvent evt;
      //   ASSERT_TRUE(alice.callQualityEvents->expectEvent("CallQualityReportHandler::onCallQualityReportFailure",
      //      30000, HandleEqualsPred<CallQualityReporterHandle>(aliceCqr), h, evt));
      //}


      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);

      alice.callQualityReport->stopCallQualityReporter(aliceCqr);
      std::cout << "alice thread exit" << std::endl;
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      assertConversationEnded_time(bob, bobCall, ConversationEndReason_UserTerminatedRemotely, 90000);
      std::cout << "bob thread exit" << std::endl;
   });

   waitFor2(aliceEvents, bobEvents);
   bob.disable();
   alice.disable();
}

class OpusPacsizeLogChecker
{
public:
   OpusPacsizeLogChecker() : mFoundLogLine(false)
   {
   }

   void loggingListener(const char* message, const char* subsystem, CPCAPI2::LogLevel level)
   {
      // [2021-10-22 12:36:39.274](Info)(RtpStreamImpl.cxx:1921) 0 t41987 Changing opus pacsize to 1920 due to high packet loss (at local end)
      if (std::string(message).find("Changing opus pacsize to 1920 due to high packet loss (at local end)") != std::string::npos)
      {
         mFoundLogLine = true;
      }
   }

   std::atomic_bool mFoundLogLine;
};

int getPPSFromCQR(const cpc::string& cqr)
{
   std::string copy = cqr.c_str();

   auto begin = copy.find("PPS=");
   if (begin == std::string::npos) return -1;

   begin += 4; // strlen of "PPS="

   auto end = copy.find(" ", begin);
   if (end == std::string::npos) return -1;

   std::string s = copy.substr(begin, end - begin);

   try {
      return std::stoi(s);
   }
   catch (...)
   {
      return -1;
   }
}

TEST_P(CallQualityReporterTests_P, BasicAudioCallReporterPublish_OBELISK6019) {
   OpusPacsizeLogChecker bobLogchecker;

   std::function<void(const char* message, const char* subsystem, CPCAPI2::LogLevel level)> f =
      std::bind(&OpusPacsizeLogChecker::loggingListener,
         &bobLogchecker, std::placeholders::_1,
         std::placeholders::_2, std::placeholders::_3);

   AutoTestsLogger::ScopedMessageListenerFunction smlf(f);

   TestAccount alice("alice", Account_Init);
   TestAccount bob("bob", Account_Init);

   CallQualityReporterConfig cqrConfig;
   cqrConfig.reportingIntervalSeconds = 10; // minimum value is 10

   cqrConfig.reportingServiceSipUri = bob.config.uri();
   cqrConfig.sipAccount = alice.handle;
   cqrConfig.ignoreFailures = true;

   CallQualityReporterHandle aliceCqr = alice.createCallQualityReporter();
   alice.callQualityReport->configureCallQualityReporter(aliceCqr, cqrConfig);
   // bliu: turn off some VQmonHelper for easier tracing
   //alice.callQualityReport->startCallQualityReporter(aliceCqr);
   alice.media->setRtcpXrVoIPMetricsReportsEnabled(false);

   cqrConfig.reportingServiceSipUri = alice.config.uri();
   cqrConfig.sipAccount = bob.handle;
   cqrConfig.ignoreFailures = true;

   CallQualityReporterHandle bobCqr = bob.createCallQualityReporter();
   bob.callQualityReport->configureCallQualityReporter(bobCqr, cqrConfig);
   bob.callQualityReport->startCallQualityReporter(bobCqr);

   alice.enableOnlyThisCodec(GetParam().name.c_str());
   alice.enable();

   bob.enableOnlyThisCodec(GetParam().name.c_str());
   bob.enable();

   if (GetParam().name == "G.729")
   {
      Media::G729Config config;
      config.useAnnexB = false;
      alice.audio->setCodecConfig(config);
   }

   SipConversationSettings settings;
   // need to set same non-empty session name to enable opus dynamic bitrate adjustment
   settings.sessionName = "cpcapi2tests";
   alice.conversation->setDefaultSettings(alice.handle, settings);
   bob.conversation->setDefaultSettings(bob.handle, settings);

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, true);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());

   SipConversationHandle bobCall;
   assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
   // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
   assertSuccess(bob.conversation->accept(bobCall));

   assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
   assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

   assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
   assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

   std::string soundClipUri;
#ifdef ANDROID
   soundClipUri = "android.resource://com.counterpath.sdkdemo.advancedaudiocall/raw/baseball";
#else
   soundClipUri = "file:" + TestEnvironmentConfig::testResourcePath() + "baseball.wav";
#endif

   alice.conversation->playSound(aliceCall, soundClipUri.c_str(), true);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   assertSuccess(alice.conversation->refreshConversationStatistics(aliceCall, true, true, true));
   {
      SipConversationHandle h;
      ConversationStatisticsUpdatedEvent evt;
      ASSERT_TRUE(alice.conversationEvents->expectEvent("SipConversationHandler::onConversationStatisticsUpdated",
         15000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
      ASSERT_EQ(1, evt.conversationStatistics.audioChannels.size());
      ASSERT_NE(evt.conversationStatistics.audioChannels[0].streamDataCounters.packetsReceived, 0);
      ASSERT_NE(evt.conversationStatistics.audioChannels[0].streamDataCounters.packetsSent, 0);
   }

   // PPS without packet loss
   {
      CallQualityReporterHandle h;
      CallQualityReportGeneratedEvent evt;
      ASSERT_TRUE(bob.callQualityEvents->expectEvent("CallQualityReportHandler::onCallQualityReportGenerated",
         60000, HandleEqualsPred<CallQualityReporterHandle>(bobCqr), h, evt));
      ASSERT_NE(evt.callQualityReport.find("OrigID: " + alice.config.uri()), kCpcStrNpos);
      ASSERT_NE(evt.callQualityReport.find("LocalID: " + bob.config.uri()), kCpcStrNpos);
      ASSERT_NE(evt.callQualityReport.find("RemoteID: " + alice.config.uri()), kCpcStrNpos);
      ASSERT_NE(evt.callQualityReport.find("LocalGroup: " + bob.config.settings.localGroup), kCpcStrNpos);
      ASSERT_NE(evt.callQualityReport.find("RemoteGroup: \r\n"), kCpcStrNpos);
      ASSERT_NE(evt.callQualityReport.find("PD=" + GetParam().pd), kCpcStrNpos);
      ASSERT_EQ(getPPSFromCQR(evt.callQualityReport), GetParam().pps);
   }

   // PPS with packet loss turned ON
   {
      TurnAsyncUdpSocket_OutgoingPacketlossInducer::Config udpLossConfig;
      udpLossConfig.lossRatePct = 25;
      TurnAsyncUdpSocket_OutgoingPacketlossInducer plu(udpLossConfig);

      auto now = std::chrono::steady_clock::now();
      bool found = false;
      while (std::chrono::steady_clock::now() - now < std::chrono::seconds(25))
      {
         CallQualityReporterHandle h;
         CallQualityReportGeneratedEvent evt;
         ASSERT_TRUE(bob.callQualityEvents->expectEvent("CallQualityReportHandler::onCallQualityReportGenerated",
            60000, HandleEqualsPred<CallQualityReporterHandle>(bobCqr), h, evt));
         ASSERT_NE(evt.callQualityReport.find("OrigID: " + alice.config.uri()), kCpcStrNpos);
         ASSERT_NE(evt.callQualityReport.find("LocalID: " + bob.config.uri()), kCpcStrNpos);
         ASSERT_NE(evt.callQualityReport.find("RemoteID: " + alice.config.uri()), kCpcStrNpos);
         ASSERT_NE(evt.callQualityReport.find("LocalGroup: " + bob.config.settings.localGroup), kCpcStrNpos);
         ASSERT_NE(evt.callQualityReport.find("RemoteGroup: \r\n"), kCpcStrNpos);
         ASSERT_NE(evt.callQualityReport.find("PD=" + GetParam().pd), kCpcStrNpos);
         const int pps = GetParam().name == "OPUS" ? GetParam().pps / 2 : GetParam().pps;
         // pps currently fluctuates for opus even during periods of constant packetloss due to current
         // handling in RtpStreamImpl::handleLoss
         if (getPPSFromCQR(evt.callQualityReport) == pps)
         {
            found = true;
            break;
         }
      }
      ASSERT_TRUE(found);

      if (GetParam().name == "OPUS") ASSERT_TRUE(bobLogchecker.mFoundLogLine);
   }

   // PPS with packet loss turned OFF
   {
      auto now = std::chrono::steady_clock::now();
      while (std::chrono::steady_clock::now() - now < std::chrono::seconds(25))
      {
         CallQualityReporterHandle h;
         CallQualityReportGeneratedEvent evt;
         ASSERT_TRUE(bob.callQualityEvents->expectEvent("CallQualityReportHandler::onCallQualityReportGenerated",
            60000, HandleEqualsPred<CallQualityReporterHandle>(bobCqr), h, evt));
         ASSERT_NE(evt.callQualityReport.find("OrigID: " + alice.config.uri()), kCpcStrNpos);
         ASSERT_NE(evt.callQualityReport.find("LocalID: " + bob.config.uri()), kCpcStrNpos);
         ASSERT_NE(evt.callQualityReport.find("RemoteID: " + alice.config.uri()), kCpcStrNpos);
         ASSERT_NE(evt.callQualityReport.find("LocalGroup: " + bob.config.settings.localGroup), kCpcStrNpos);
         ASSERT_NE(evt.callQualityReport.find("RemoteGroup: \r\n"), kCpcStrNpos);
         ASSERT_NE(evt.callQualityReport.find("PD=" + GetParam().pd), kCpcStrNpos);

         // bliu: PPS of OPUS after packet loss is turned off is unpredictable from time to time
         // hence turn off this check for OPUS temporarily
         if (!(GetParam().name == "OPUS")) ASSERT_EQ(getPPSFromCQR(evt.callQualityReport), GetParam().pps);
      }
   }

   assertSuccess(alice.conversation->end(aliceCall));
   assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);

   alice.callQualityReport->stopCallQualityReporter(aliceCqr);

   assertConversationEnded_time(bob, bobCall, ConversationEndReason_UserTerminatedRemotely, 180000);

   bob.callQualityReport->stopCallQualityReporter(bobCqr);
}

static const AudioCodecInfo AudioCodecInfos [] = {
   { "OPUS", "audio/opus", 50 },
#if (CPCAPI2_BRAND_CODEC_G729 == 1)
   { "G.729", "audio/G729", 50 },
#endif
   { "G711 aLaw", "audio/PCMA", 50 }
};

INSTANTIATE_TEST_SUITE_P(
   BasicAudioCallReporter_AllCodecs,
   CallQualityReporterTests_P,
   ::testing::ValuesIn(AudioCodecInfos)
);

#endif
