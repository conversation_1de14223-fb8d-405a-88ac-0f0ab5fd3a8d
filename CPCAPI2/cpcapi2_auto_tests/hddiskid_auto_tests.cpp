#if _WIN32
#include "stdafx.h"
#endif

#ifdef _WIN32

#include "cpcapi2_test_fixture.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>
#include "../impl/util/HDDiskId.h"
#include <string>

#include "test_framework/cpcapi2_test_framework.h"

using namespace CPCAPI2;
using namespace CPCAPI2::test;

namespace {

class HDDiskIdTest : public CpcapiAutoTest
{
public:
   HDDiskIdTest() {}
   ~HDDiskIdTest() {}
};

TEST_F(HDDiskIdTest, HardDriveId) {
   std::string hddid = HDDiskId::GetPrimaryMasterHDDId();
   ASSERT_FALSE(hddid.empty());

   std::vector<std::string> hddids = HDDiskId::GetAllHddInfo();
   ASSERT_TRUE(hddids.size() > 0);
   ASSERT_FALSE(hddids[0].empty());
}

}
#endif
