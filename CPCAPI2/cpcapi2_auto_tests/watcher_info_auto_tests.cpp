#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#if (CPCAPI2_BRAND_WATCHER_INFO_MODULE == 1)

#include "cpcapi2_test_fixture.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>
#include <libxml/parser.h>
#include "xcap/XcapSubscriptionHandler.h"
#include "watcherinfo/WatcherInfoManager.h"
#include "presence/SipPresenceManager.h"
#include "../../../../CPCAPI2/impl/xcap/XcapInternalInterface.h"
#include "../../../../CPCAPI2/impl/watcherinfo/WatcherInfoManagerInterface.h"

#include "test_framework/cpcapi2_test_framework.h"
#include "test_framework/bare_bones_sip_endpoint.h"

#include <thread>
#include <future>
#include <cstdio>

using namespace CPCAPI2;
using namespace CPCAPI2::XCAP;
using namespace CPCAPI2::WatcherInfo;
using namespace CPCAPI2::SipPresence;
using namespace CPCAPI2::SipEvent;
using namespace CPCAPI2::test;

namespace 
{
void setXcapSettings(TestAccount& acc)
{
   acc.config.settings.domain = TestEnvironmentConfig::defaultXcapServer();
   acc.config.settings.outboundProxy = acc.config.settings.domain;
   acc.config.settings.username = acc.config.name;
   acc.config.settings.password = acc.config.name + "123";
   acc.config.xcapSettings.username = acc.config.name;
   acc.config.xcapSettings.password = acc.config.name + "123";
}
/***
* Four test accounts are made for watcher info tests on socrates.cp.local Kamailio server. 
* username     password
* alice        alice123
* bob          bob123
* tom          tom123
* jane         jane123
*/

class WatcherInfoTest : public CpcapiAutoTest
{
public:
   WatcherInfoTest() {}
   virtual ~WatcherInfoTest() {}
};

static void sleepawhile( void )
{
   std::this_thread::sleep_for( std::chrono::seconds( 10 ));
}

TEST_F(WatcherInfoTest, OBELISK_4703)
{
   // This test case is here just to ensure that this condition doesn't happen.
   // So we will try to engineer a situation where it might happen and test that
   // the SDK/framework does not allow it.
   //
   // The ticket shows that the watcher info interface is destroyed before
   // the sethandler callback is invoked in the command queue.
   //
   // So in order to do this we will try to do the following:
   // 1) Create the TestAccount(s)
   // 2) Put into the command queue, an item which calls sleep
   // 3) Put into the command queue a call to setHandler
   // 4) Allow the TestAccount to be destroyed
   
   TestAccount alice("alice", TestAccountInitMode::Account_NoInit);
   //setXcapSettings(alice);
   alice.init();
   alice.enable();
   PhoneInterface *phoneIf = dynamic_cast< PhoneInterface* >( alice.phone );
   phoneIf->runOnSdkModuleThread( sleepawhile );
   alice.winfoManager->setHandler( alice.handle, NULL );
}

TEST_F(WatcherInfoTest, DISABLED_WinfoAddPresRulesFile)
{
   /*TestAccount alice("alice", TestAccountInitMode::Account_NoInit);
   setXcapSettings(alice);
   alice.enable();
   WatcherInfoManagerInterface* winfoManagerIf = dynamic_cast<WatcherInfoManagerInterface*>(alice.winfoManager);*/
   TestAccount bob("bob", TestAccountInitMode::Account_NoInit);
   setXcapSettings(bob);
   bob.enable();
   WatcherInfoManagerInterface* winfoManagerIf = dynamic_cast<WatcherInfoManagerInterface*>(bob.winfoManager);
   cpc::string filePath = "cp-pres-rules.xml";
   std::string line = "";
   cpc::string wholeFile = "";
   std::ifstream xmlFile;

   xmlFile.open(filePath.c_str());
   if (!xmlFile.is_open())
   {
      ASSERT_EQ("Can't open file: ", filePath.c_str());
   }
   while ( getline(xmlFile,line) )
   {
      wholeFile.append(line.c_str());
   }
   xmlFile.close();

   //winfoManagerIf->setPresenceAuthenticationRules(alice.handle, wholeFile);
   winfoManagerIf->setPresenceAuthenticationRules(bob.handle, wholeFile);
   XCAPResult result;
   XcapRequestComponents xcapRequest = {"index", "", "pres-rules", "application/auth-policy+xml", false};
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   //result = winfoManagerIf->getXcapManager()->read(alice.config.xcapSettings, xcapRequest);
   result = winfoManagerIf->getXcapManager()->read(bob.config.xcapSettings, xcapRequest);
   xmlParserCtxtPtr xmlDoc = ::xmlCreateDocParserCtxt((unsigned char*)result.result.c_str());
   int i = (xmlDoc == NULL)?0:1;
   ASSERT_NE(i, NULL);

}

TEST_F(WatcherInfoTest, DISABLED_WinfoAddRemoveSubscriber)
{
   TestAccount alice("alice", TestAccountInitMode::Account_NoInit);
   setXcapSettings(alice);
   alice.enable();
   cpc::string bob0Uri = "sip:<EMAIL>";
   cpc::string bob1Uri = "sip:<EMAIL>";
   cpc::string bob2Uri = "sip:<EMAIL>";
   cpc::string bob3Uri = "sip:<EMAIL>";
   XCAPResult result;
   WatcherInfoSubscriptionHandle subscription;
   WatcherInfoManagerInterface* winfoManagerIf = dynamic_cast<WatcherInfoManagerInterface*>(alice.winfoManager);

   winfoManagerIf->addWatcher(alice.handle, bob0Uri);
   winfoManagerIf->addWatcher(alice.handle, bob1Uri);
   winfoManagerIf->addWatcher(alice.handle, bob2Uri);
   winfoManagerIf->addWatcher(alice.handle, bob3Uri);
   winfoManagerIf->removeWatcher(alice.handle, bob0Uri);
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   winfoManagerIf->setPresenceAuthenticationRules(alice.handle);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   XcapRequestComponents xcapRequest = {"index", "", "pres-rules", "application/auth-policy+xml", false};

   result = winfoManagerIf->getXcapManager()->read(alice.config.xcapSettings, xcapRequest);
   xmlParserCtxtPtr xmlDoc = ::xmlCreateDocParserCtxt((unsigned char*)result.result.c_str());
   int i = (xmlDoc == NULL)?0:1;
   ASSERT_NE(i, NULL);

   cpc::string wholeFile = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><ruleset xmlns=\"urn:ietf:params:xml:ns:common-policy\" xmlns:pr=\"urn:ietf:params:xml:ns:pres-rules\" ><rule id=\"whitelist\"><conditions><identity><one id=\"sip:<EMAIL>\"/><one id=\"sip:<EMAIL>\"/><one id=\"sip:<EMAIL>\"/></identity></conditions><actions><pr:sub-handling>allow</pr:sub-handling></actions><transformations><pr:provide-services><pr:all-services/></pr:provide-services><pr:provide-persons><pr:all-persons/></pr:provide-persons><pr:provide-devices><pr:all-devices/></pr:provide-devices><pr:provide-all-attributes/></transformations></rule></ruleset>";
   ASSERT_EQ(strcmp(wholeFile.c_str(), result.result.c_str()), 0);
}

TEST_F(WatcherInfoTest, DISABLED_WinfoAddRemoveSubscriberEmpty)
{
   TestAccount alice("alice", TestAccountInitMode::Account_NoInit);
   setXcapSettings(alice);
   alice.enable();
   cpc::string bob0Uri = "sip:<EMAIL>";
   cpc::string bob1Uri = "sip:<EMAIL>";
   cpc::string bob2Uri = "sip:<EMAIL>";
   cpc::string bob3Uri = "sip:<EMAIL>";
   XCAPResult result;
   WatcherInfoSubscriptionHandle subscription;
   WatcherInfoManagerInterface* winfoManagerIf = dynamic_cast<WatcherInfoManagerInterface*>(alice.winfoManager);

   winfoManagerIf->addWatcher(alice.handle, bob0Uri);
   winfoManagerIf->addWatcher(alice.handle, bob1Uri);
   winfoManagerIf->addWatcher(alice.handle, bob2Uri);
   winfoManagerIf->addWatcher(alice.handle, bob3Uri);
   winfoManagerIf->removeWatcher(alice.handle, bob0Uri);
   winfoManagerIf->removeWatcher(alice.handle, bob1Uri);
   winfoManagerIf->removeWatcher(alice.handle, bob2Uri);
   winfoManagerIf->removeWatcher(alice.handle, bob3Uri);
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   winfoManagerIf->setPresenceAuthenticationRules(alice.handle);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   XcapRequestComponents xcapRequest = {"index", "", "pres-rules", "application/auth-policy+xml", false};

   result = winfoManagerIf->getXcapManager()->read(alice.config.xcapSettings, xcapRequest);
   xmlParserCtxtPtr xmlDoc = ::xmlCreateDocParserCtxt((unsigned char*)result.result.c_str());
   int i = (xmlDoc == NULL)?0:1;
   ASSERT_NE(i, NULL);

   cpc::string wholeFile = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><ruleset xmlns=\"urn:ietf:params:xml:ns:common-policy\" xmlns:pr=\"urn:ietf:params:xml:ns:pres-rules\" ><rule id=\"whitelist\"><conditions><identity><many/></identity></conditions><actions><pr:sub-handling>allow</pr:sub-handling></actions><transformations><pr:provide-services><pr:all-services/></pr:provide-services><pr:provide-persons><pr:all-persons/></pr:provide-persons><pr:provide-devices><pr:all-devices/></pr:provide-devices><pr:provide-all-attributes/></transformations></rule></ruleset>";
   ASSERT_EQ(strcmp(wholeFile.c_str(), result.result.c_str()), 0);
}

TEST_F(WatcherInfoTest, DISABLED_WinfoSubscribe)
{
   TestAccount alice("alice", TestAccountInitMode::Account_NoInit);
   setXcapSettings(alice);
   alice.enable();
   WatcherInfoSubscriptionHandle subscription;
   WatcherInfoEventSubscriptionSettings winfoSettings;

   winfoSettings.expiresSeconds = 3600;

   subscription = alice.winfoManager->createSubscription(alice.handle);
   alice.winfoManager->applySubscriptionSettings(subscription, winfoSettings);
   alice.winfoManager->addParticipant(subscription, alice.config.uri());
   alice.winfoManager->start(subscription);

   {
      NewWatcherInfoSubscriptionEvent evt;
      ASSERT_TRUE(alice.winfoEvents->expectEvent("WatcherInfoSubscriptionHandler::onNewSubscription", 20000, AlwaysTruePred(), subscription, evt));
   }

   {
      WatcherInfoSubscriptionStateChangedEvent evt;
      ASSERT_TRUE(alice.winfoEvents->expectEvent("WatcherInfoSubscriptionHandler::onSubscriptionStateChanged", 20000, AlwaysTruePred(), subscription, evt));
      ASSERT_EQ(evt.subscriptionState, WatcherInfoSubscriptionState::WatcherInfoSubscriptionState_Active);
   }

   {
      IncomingWatcherInfoEvent evt;
      ASSERT_TRUE(alice.winfoEvents->expectEvent("WatcherInfoSubscriptionHandler::onIncomingWatcherInfo", 20000, AlwaysTruePred(), subscription, evt));
      ASSERT_EQ(strcmp(evt.eventState.eventPackage.c_str(), "presence.winfo"), 0);
   }
}

TEST_F(WatcherInfoTest, DISABLED_WinfoSubscribeNotifyServer)
{
   TestAccount alice("alice", TestAccountInitMode::Account_NoInit);
   setXcapSettings(alice);
   alice.enable();
   TestAccount bob("bob", TestAccountInitMode::Account_NoInit);
   setXcapSettings(bob);
   bob.enable();
   WatcherInfoSubscriptionHandle subscription;
   WatcherInfoEventSubscriptionSettings winfoSettings;
   SipEventSubscriptionHandle bobSubscription;
   SipPresenceSubscriptionSettings presenceSettings;

   winfoSettings.expiresSeconds = 3600;

   subscription = alice.winfoManager->createSubscription(alice.handle);
   alice.winfoManager->applySubscriptionSettings(subscription, winfoSettings);
   alice.winfoManager->addParticipant(subscription, alice.config.uri());
   alice.winfoManager->start(subscription);

   {
      NewWatcherInfoSubscriptionEvent evt;
      ASSERT_TRUE(alice.winfoEvents->expectEvent("WatcherInfoSubscriptionHandler::onNewSubscription", 20000, AlwaysTruePred(), subscription, evt));
   }

   {
      WatcherInfoSubscriptionStateChangedEvent evt;
      ASSERT_TRUE(alice.winfoEvents->expectEvent("WatcherInfoSubscriptionHandler::onSubscriptionStateChanged", 20000, AlwaysTruePred(), subscription, evt));
      ASSERT_EQ(evt.subscriptionState, WatcherInfoSubscriptionState::WatcherInfoSubscriptionState_Active);
   }

   {
      IncomingWatcherInfoEvent evt;
      ASSERT_TRUE(alice.winfoEvents->expectEvent("WatcherInfoSubscriptionHandler::onIncomingWatcherInfo", 20000, AlwaysTruePred(), subscription, evt));
      ASSERT_EQ(strcmp(evt.eventState.eventPackage.c_str(), "presence.winfo"), 0);
   }

   bobSubscription = bob.presence->createSubscription(bob.handle);
   bob.presence->applySubscriptionSettings(bobSubscription, presenceSettings);
   bob.presence->addParticipant(bobSubscription, alice.config.uri());
   bob.presence->start(bobSubscription);

   {
      IncomingWatcherInfoEvent evt;
      ASSERT_TRUE(alice.winfoEvents->expectEvent("WatcherInfoSubscriptionHandler::onIncomingWatcherInfo", 20000, AlwaysTruePred(), subscription, evt));
      ASSERT_EQ(strcmp(evt.eventState.eventPackage.c_str(), "presence.winfo"), 0);
      ASSERT_EQ(strcmp(evt.eventState.winfo.watcherLists[0].package.c_str(), "presence"), 0);
      ASSERT_EQ(strcmp(evt.eventState.winfo.watcherLists[0].watchers[0].watcherURI.c_str(), bob.config.uri().c_str()), 0);
   }
}

TEST_F(WatcherInfoTest, DISABLED_BasicPresenceSubscribe_BuildSanity)
{
   TestAccount alice("alice", TestAccountInitMode::Account_NoInit);
   setXcapSettings(alice);
   alice.enable();
   TestAccount bob("bob", TestAccountInitMode::Account_NoInit);
   setXcapSettings(bob);
   bob.enable();

	// make an outgoing subscription from Alice to Bob using the demo.xten.com server
	SipEventSubscriptionHandle aliceSubs = alice.subs->createSubscription(alice.handle);
   SipPresenceSubscriptionSettings subsSettings;
   subsSettings.expiresSeconds = 3600;
   alice.presence->applySubscriptionSettings(aliceSubs, subsSettings);
	alice.presence->addParticipant(aliceSubs, bob.config.uri());
	alice.presence->start(aliceSubs);

	// Overview of Alice's thread:
	//  - 
	auto aliceEvents = std::async(std::launch::async, [&] () {

      {
		SipEventSubscriptionHandle h;
		PresenceSubscriptionStateChangedEvent evt;
		ASSERT_TRUE(alice.presenceEvents->expectEvent(
			"SipPresenceSubscriptionHandler::onSubscriptionStateChanged",
			5000,
         HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs),
         h, evt));
   	    ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

	});
	ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
	ASSERT_NO_THROW(aliceEvents.get());

	// not needed, but handy sometimes when debugging ...
	//std::this_thread::sleep_for(std::chrono::milliseconds(10000));
}

}

#endif //CPCAPI2_BRAND_WATCHER_INFO_MODULE
