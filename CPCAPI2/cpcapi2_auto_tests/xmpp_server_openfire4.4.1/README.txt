YVR-SDKXMPP01.cp.local deployment instructions:

To re-deploy a fresh Openfire 4.4.1 instance on YVR-SDKXMPP01.cp.local:

1. Use docker container ls command to view currently running Openfire 4.4.1 instance; use docker container stop, docker container rm to remove this container

1. svn revert /home/<USER>/xmpp_server_openfire4.4.1

2. Run svn update on /home/<USER>/xmpp_server_openfire4.4.1

3. Execute the following command:

sudo docker run --name openfire4.4.1 -d --restart=always --hostname=yvr-sdkxmpp01.cp.local --publish 9093:9090 --publish 9094:9091 --publish 5224:5222 --publish 7779:7777 --publish 7443:7443 --volume /home/<USER>/xmpp_server_openfire4.4.1/conf:/var/lib/openfire openfire4.4.1


With any luck, no extra configuration / login to admin UI should be required.

Below more general README instructions have been kept.




--------------------------------------------------------------------------------
Updated README supplemental text
--------------------------------------------------------------------------------

In order for me to install and configure openfire 4.4.1, I had to do the following:

First, all of the files in this directory (xmpp_server_openfire4.4.1) have to
be located on the target machine, where docker is installed. Being new to
docker that wasn't obvious to me, but that is the case.

Once that is done, you need to build the openfire image. The original readme
says that the following command should be used:

     docker build -t openfire4.4.1 .

This is done from INSIDE the xmpp_server_openfire4.4.1 directory, which I
assume you have already transferred to the target machine. That is correct,
however the default configuration of openfire will not work out-of-the-box with
our unit tests. Some things need to be done configuration-wise:

1) The HTTP Upload plugin needs to be installed and enabled
2) The default file size of the HTTP needs to be extended beyond 100MB
3) The REST API plugin needs to be installed and enabled
4) The REST API plugin secret key needs to be set and selected as the method of
   authentication

The original readme says that these configuration items can be controlled by
modifying the file:

conf/embedded-db/openfire.script

Even though most of the suggested properties were in the aforementioned file,
That the config did not "take" and I had to manually provision the openfire
server using the administration user interface.

Also note that it is completely possible to lock yourself out of the admin
interface by mistakenly modifying the ports from the default setup! You need to
keep in mind that the docker instance uses all the default ports, but the
mapping is achieved via the docker container (like a firewall port remap). So
from the admin configuration standpoint the ports are default, and they are
mapped after the facy using the docker "start" command in order to co-exist
with parallel openfire installations.

The command I used to start the openfire container differs from the provided example. Here it is:

docker run --name openfire4.4.1 -d --restart=always --hostname=yvr-sdkxmpp01.cp.local --publish 9092:9090 --publish 9093:9091 --publish 5224:5222 --publish 7779:7777 --publish 7443:7443 --volume /home/<USER>/svnwork/CPCAPI2/core/cpcapi2_auto_tests/xmpp_server_openfire4.4.1/conf:/var/lib/openfire openfire4.4.1

Note that the folder
/home/<USER>/svnwork/CPCAPI2/core/cpcapi2_auto_tests/xmpp_server_openfire4.4.1/conf
must exist on your target machine (it could be different for you)

The --publish flag is VERY important. It describes the ports which will be
accessible for your docker instance. The first number on the left is the port
value which will be occupied on the hosting machine and the second number is
the port inside the docker instance. This is how we achieve multiple openfire
servers running in parallel. In this case I have moved up every port by two (2)
from the defaults, unless it was unoccuptied by the previous server (which was
the case for port 7443).

Once your docker instance spins up you will probably have to configure it. In
my case the admin console was located at:

http://***********:9093/login.jsp

To be honest I was never fully sure why this was http and not https at port 9093.
Once you find this page the login/password will be:

admin/admin

Once you are logged in, perform the following steps:

1) Goto Plugins->Available Plugins
2) Install "HTTP File Upload" if not already installed
3) Install "REST API" if not already installed
4) The plugins should automatically start, if not you might have to either press
   a button on the installed plugins page, or (worst case) restart the server.
5) Goto Server->Server Settings->REST API
6) Ensure that "Enabled" is selected, as is "Secret Key Auth", and that the secret
   key is set to "sdkrestapitestkey". NOTE: *NOT* "testsecretkey" as described below
   in the original README
7) Goto Server->Server Settings->File Transfer
8) Ensure that it is enabled
9) Goto Server->Server Manager->System Properties
10) Ensure that there is a value, "plugin.httpfileupload.maxFileSize" and that it
    is set to a value of "110000000". If you added this or modified it, go back
    to the plugins page and restart the HTTP File Upload Plugin


--------------------------------------------------------------------------------
The original version of the README follows:
--------------------------------------------------------------------------------
The current version of openfire is 4.4.1, which can be defined in Dockerfile.

Before starting the docker container:
The Domain name, server host name, RESTAPI secret key can be editd in conf/embedded-db/opefire.script
To enable RESTAPI:
INSERT INTO OFPROPERTY VALUES('plugin.restapi.enabled','true',0)


To set Secret key authentication, set plugin.restapi.httpAuth to secret:
INSERT INTO OFPROPERTY VALUES('plugin.restapi.httpAuth','secret',0)

To edit the secret ket of RESTAPI, change testsecretkey to the actual api key:
INSERT INTO OFPROPERTY VALUES('plugin.restapi.secret','testsecretkey',0)

To change the xmpp domain name, change the value **********
INSERT INTO OFPROPERTY VALUES('xmpp.domain','**********',0)

To skip the setup phase of the openfire server:
INSERT INTO OFPROPERTY VALUES('setup','true',0)
and in conf/conf/opefire.xml, set setup to true:  <setup>true</setup> 

To punish a port, add the port after the EXPOSE command in dockerfile.  Also add --publish option when starting the container, e.g. --publish 9090:9090 

Plugins:
RESTAPI: 1.3.9
HTTP File Upload: 1.1.0
To install plugins, copy the restapi.jar and httpfileupload.jar file to PATH/TO/conf/plugins

1. To build a docker iamge with openfire, in the directory where stores Dockerfile and entrypoint.sh, issue the command:
     docker build -t imageName .
2. Start a docker container with the volume folder mounted to this container, the conf folder contains the setup and config info of openfire.  Change ********** in --hostname=yvr-sdkxmpp01.cp.local to the correct host name.
     docker run --name openfire4 -d --restart=always --hostname=********** --publish 9090:9090 --publish 5222:5222 --publish 7777:7777 --publish 7070:7070 --publish 7443:7443 --volume FULL/PATH/TO/conf:/var/lib/openfire imageName

The login credential is username:admin, password:admin.
If the server is not running properly, stop the container first, then In conf/conf/opefire.xml, set setup to false:  <setup>false</setup>
