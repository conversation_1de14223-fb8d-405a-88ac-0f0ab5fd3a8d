<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>REST API</title>
  <link rel="stylesheet" href="https://stackedit.io/style.css" />
</head>

<body class="stackedit">
  <div class="stackedit__left">
    <div class="stackedit__toc">
      
<ul>
<li><a href="#rest-api-plugin-readme">REST API Plugin Readme</a>
<ul>
<li><a href="#feature-list">Feature list</a></li>
<li><a href="#available-rest-api-clients">Available REST API clients</a></li>
<li><a href="#installation">Installation</a></li>
<li><a href="#explanation-of-rest">Explanation of REST</a></li>
<li><a href="#authentication">Authentication</a></li>
</ul>
</li>
<li><a href="#user-related-rest-endpoints">User related REST Endpoints</a>
<ul>
<li><a href="#retrieve-users">Retrieve users</a></li>
<li><a href="#retrieve-a-user">Retrieve a user</a></li>
<li><a href="#create-a-user">Create a user</a></li>
<li><a href="#delete-a-user">Delete a user</a></li>
<li><a href="#update-a-user">Update a user</a></li>
<li><a href="#retrieve-all-user-groups">Retrieve all user groups</a></li>
<li><a href="#add-user-to-groups">Add user to groups</a></li>
<li><a href="#add-user-to-group">Add user to group</a></li>
<li><a href="#delete-a-user-from-a-groups">Delete a user from a groups</a></li>
<li><a href="#delete-a-user-from-a-group">Delete a user from a group</a></li>
<li><a href="#lockout-a-user">Lockout a user</a></li>
<li><a href="#unlock-a-user">Unlock a user</a></li>
<li><a href="#retrieve-user-roster">Retrieve user roster</a></li>
<li><a href="#create-a-user-roster-entry">Create a user roster entry</a></li>
<li><a href="#delete-a-user-roster-entry">Delete a user roster entry</a></li>
<li><a href="#update-a-user-roster-entry">Update a user roster entry</a></li>
</ul>
</li>
<li><a href="#chat-room-related-rest-endpoints">Chat room related REST Endpoints</a>
<ul>
<li><a href="#retrieve-all-chat-rooms">Retrieve all chat rooms</a></li>
<li><a href="#retrieve-a-chat-room">Retrieve a chat room</a></li>
<li><a href="#retrieve-chat-room-participants">Retrieve chat room participants</a></li>
<li><a href="#retrieve-chat-room-occupants">Retrieve chat room occupants</a></li>
<li><a href="#retrieve-chat-room-history">Retrieve chat room message history</a></li>
<li><a href="#create-a-chat-room">Create a chat room</a></li>
<li><a href="#delete-a-chat-room">Delete a chat room</a></li>
<li><a href="#update-a-chat-room">Update a chat room</a></li>
<li><a href="#add-user-with-role-to-chat-room">Add user with role to chat room</a></li>
<li><a href="#add-group-with-role-to-chat-room">Add group with role to chat room</a></li>
<li><a href="#delete-a-user-from-a-chat-room">Delete a user from a chat room</a></li>
</ul>
</li>
<li><a href="#system-related-rest-endpoints">System related REST Endpoints</a>
<ul>
<li><a href="#retrieve-all-system-properties">Retrieve all system properties</a></li>
<li><a href="#retrieve-system-property">Retrieve system property</a></li>
<li><a href="#create-a-system-property">Create a system property</a></li>
<li><a href="#delete-a-system-property">Delete a system property</a></li>
<li><a href="#update-a-system-property">Update a system property</a></li>
<li><a href="#retrieve-concurrent-sessions">Retrieve concurrent sessions</a></li>
</ul>
</li>
<li><a href="#group-related-rest-endpoints">Group related REST Endpoints</a>
<ul>
<li><a href="#retrieve-all-groups">Retrieve all groups</a></li>
<li><a href="#retrieve-a-group">Retrieve a group</a></li>
<li><a href="#create-a-group">Create a group</a></li>
<li><a href="#delete-a-group">Delete a group</a></li>
<li><a href="#update-a-group">Update a group</a></li>
</ul>
</li>
<li><a href="#session-related-rest-endpoints">Session related REST Endpoints</a>
<ul>
<li><a href="#retrieve-all-user-session">Retrieve all user session</a></li>
<li><a href="#retrieve-the-user-sessions">Retrieve the user sessions</a></li>
<li><a href="#close-all-user-sessions">Close all user sessions</a></li>
</ul>
</li>
<li><a href="#message-related-rest-endpoints">Message related REST Endpoints</a>
<ul>
<li><a href="#send-a-broadcast-message">Send a broadcast message</a></li>
</ul>
</li>
<li><a href="#security-audit-related-rest-endpoints">Security Audit related REST Endpoints</a>
<ul>
<li><a href="#retrieve-the-security-audit-logs">Retrieve the Security audit logs</a></li>
</ul>
</li>
<li><a href="#data-format">Data format</a>
<ul>
<li><a href="#data-types">Data types</a></li>
</ul>
</li>
<li><a href="#deprecated-user-service-plugin-readme">(Deprecated) User Service Plugin Readme</a>
<ul>
<li><a href="#overview">Overview</a></li>
<li><a href="#installation-1">Installation</a></li>
<li><a href="#configuration">Configuration</a></li>
<li><a href="#using-the-plugin">Using the Plugin</a></li>
<li><a href="#sample-html">Sample HTML</a></li>
<li><a href="#server-reply">Server Reply</a></li>
</ul>
</li>
</ul>

    </div>
  </div>
  <div class="stackedit__right">
    <div class="stackedit__html">
      <h1 id="rest-api-plugin-readme">REST API Plugin Readme</h1>
<p>The REST API Plugin provides the ability to manage Openfire by sending an REST/HTTP request to the server. This plugin’s functionality is useful for applications that need to administer Openfire outside of the Openfire admin console.</p>
<h2 id="feature-list">Feature list</h2>
<ul>
    <li>Get overview over all or specific user and to create, update or delete a user</li>
    <li>Get overview over all or specific group and to create, update or delete a group</li>
    <li>Get overview over all user roster entries and to add, update or delete a roster entry</li>
    <li>Add user to a group and remove a user from a group</li>
    <li>Lockout, unlock or kick the user (enable / disable)</li>
    <li>Get overview over all or specific system properties and to create, update or delete system property</li>
    <li>Get overview over all or specific chat room and to create, update or delete a chat room</li>
    <li>Get overview over all or specific user sessions</li>
    <li>Send broadcast message to all online users</li>
    <li>Get overview of all or specific security audit logs</li>
    <li>Get chat message history from a Multi User Chatroom</li>
</ul>
<h2 id="available-rest-api-clients">Available REST API clients</h2>
<p>REST API clients are implementations of the REST API in a specific programming language.</p>
<h3 id="official">Official</h3>
<ul>
<li>JAVA: <a href="https://github.com/igniterealtime/REST-API-Client">https://github.com/igniterealtime/REST-API-Client</a></li>
</ul>
<h3 id="third-party">Third party</h3>
<ul>
<li>PHP: <a href="https://github.com/gidkom/php-openfire-restapi">https://github.com/gidkom/php-openfire-restapi</a> (partly implemented)</li>
<li>PHP: <a href="https://github.com/gnello/php-openfire-restapi">https://github.com/gnello/php-openfire-restapi</a> (partly implemented)</li>
<li>GO Lang: <a href="https://github.com/Urethramancer/fireman">https://github.com/Urethramancer/fireman</a> (partly implemented)</li>
<li>Python: <a href="https://github.com/seamus-45/openfire-restapi">https://github.com/seamus-45/openfire-restapi</a> (partly implemented)</li>
</ul>
<h2 id="installation">Installation</h2>
<p>Copy restAPI.jar into the plugins directory of your Openfire server. The plugin will be automatically deployed. To upgrade to a newer version, overwrite the restAPI.jar file with the new one.</p>
<h2 id="explanation-of-rest">Explanation of REST</h2>
<p>To provide a standard way of accessing the data the plugin is using REST.</p>

<table>
<thead>
<tr>
<th>HTTP Method</th>
<th>Usage</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>GET</strong></td>
<td>Receive a read-only data</td>
</tr>
<tr>
<td><strong>PUT</strong></td>
<td>Overwrite an existing resource</td>
</tr>
<tr>
<td><strong>POST</strong></td>
<td>Creates a new resource</td>
</tr>
<tr>
<td><strong>DELETE</strong></td>
<td>Deletes the given resource</td>
</tr>
</tbody>
</table><h2 id="authentication">Authentication</h2>
<p>All REST Endpoint are secured and must be authenticated. There are two ways to authenticate:</p>
<ul>
<li><a href="http://en.wikipedia.org/wiki/Basic_access_authentication">Basic HTTP Authentication</a></li>
<li>Shared secret key</li>
</ul>
<p>The configuration can be done in Openfire Admin console under Server &gt; Server Settings &gt; REST API.</p>
<h3 id="basic-http-authentication">Basic HTTP Authentication</h3>
<p>To access the endpoints is that required to send the Username and Password of a Openfire Admin account in your HTTP header request.</p>
<p>E.g. <strong>Header:</strong> Authorization: Basic YWRtaW46MTIzNDU=  (username: admin / password: 12345)</p>
<h3 id="shared-secret-key">Shared secret key</h3>
<p>To access the endpoints is that required to send the secret key in your header request.<br>
The secret key can be defined in Openfire Admin console under Server &gt; Server Settings &gt; REST API.</p>
<p>E.g. <strong>Header:</strong> Authorization: s3cretKey</p>
<h1 id="user-related-rest-endpoints">User related REST Endpoints</h1>
<h2 id="retrieve-users">Retrieve users</h2>
<p>Endpoint to get all or filtered users</p>
<blockquote>
<p><strong>GET</strong> /users</p>
</blockquote>
<p><strong>Payload:</strong> none<br>
<strong>Return value:</strong> Users</p>
<h3 id="possible-parameters">Possible parameters</h3>

<table>
<thead>
<tr>
<th>Parameter</th>
<th>Parameter Type</th>
<th>Description</th>
<th>Default value</th>
</tr>
</thead>
<tbody>
<tr>
<td>search</td>
<td>@QueryParam</td>
<td>Search/Filter by username. <br> This act like the wildcard search %String%</td>
<td></td>
</tr>
<tr>
<td>propertyKey</td>
<td>@QueryParam</td>
<td>Filter by user propertyKey.</td>
<td></td>
</tr>
<tr>
<td>propertyValue</td>
<td>@QueryParam</td>
<td>Filter by user propertyKey and propertyValue. <br><strong>Note:</strong> It can only be used within propertyKey parameter</td>
<td></td>
</tr>
</tbody>
</table><h3 id="examples">Examples</h3>
<blockquote>
<p><strong>Header</strong>: Authorization: Basic YWRtaW46MTIzNDU=</p>
</blockquote>
<blockquote>
<p><strong>GET</strong> <a href="http://example.org:9090/plugins/restapi/v1/users">http://example.org:9090/plugins/restapi/v1/users</a><br>
<strong>GET</strong> <a href="http://example.org:9090/plugins/restapi/v1/users?search=testuser">http://example.org:9090/plugins/restapi/v1/users?search=testuser</a><br>
<strong>GET</strong> <a href="http://example.org:9090/plugins/restapi/v1/users?propertyKey=keyname">http://example.org:9090/plugins/restapi/v1/users?propertyKey=keyname</a><br>
<strong>GET</strong> <a href="http://example.org:9090/plugins/restapi/v1/users?propertyKey=keyname&amp;propertyValue=keyvalue">http://example.org:9090/plugins/restapi/v1/users?propertyKey=keyname&amp;propertyValue=keyvalue</a></p>
</blockquote>
<p>If you want to get a JSON format result, please add “<strong>Accept: application/json</strong>” to the <strong>Header</strong>.</p>
<h2 id="retrieve-a-user">Retrieve a user</h2>
<p>Endpoint to get information over a specific user</p>
<blockquote>
<p><strong>GET</strong> /users/{username}</p>
</blockquote>
<p><strong>Payload:</strong> none<br>
<strong>Return value:</strong> User</p>
<h3 id="possible-parameters-1">Possible parameters</h3>

<table>
<thead>
<tr>
<th>Parameter</th>
<th>Parameter Type</th>
<th>Description</th>
<th>Default value</th>
</tr>
</thead>
<tbody>
<tr>
<td>username</td>
<td>@Path</td>
<td>Exact username</td>
<td></td>
</tr>
</tbody>
</table><h3 id="examples-1">Examples</h3>
<p><strong>Header:</strong> Authorization: Basic YWRtaW46MTIzNDU=</p>
<p><strong>GET</strong> <a href="http://example.org:9090/plugins/restapi/v1/users/testuser">http://example.org:9090/plugins/restapi/v1/users/testuser</a></p>
<h2 id="create-a-user">Create a user</h2>
<p>Endpoint to create a new user</p>
<blockquote>
<p><strong>POST</strong> /users</p>
</blockquote>
<p><strong>Payload:</strong> User<br>
<strong>Return value:</strong> HTTP status 201 (Created)</p>
<h3 id="examples-2">Examples</h3>
<h4 id="xml-examples">XML Examples</h4>
<blockquote>
<p><strong>Header:</strong> Authorization: Basic YWRtaW46MTIzNDU=<br>
<strong>Header:</strong> Content-Type: application/<strong>xml</strong></p>
</blockquote>
<blockquote>
<p><strong>POST</strong> <a href="http://example.org:9090/plugins/restapi/v1/users">http://example.org:9090/plugins/restapi/v1/users</a></p>
</blockquote>
<p><strong>Payload Example 1 (required parameters):</strong></p>
<pre class=" language-xml"><code class="prism  language-xml"><span class="token prolog">&lt;?xml version="1.0" encoding="UTF-8" standalone="yes"?&gt;</span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>user</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>username</span><span class="token punctuation">&gt;</span></span>test3<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>username</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>password</span><span class="token punctuation">&gt;</span></span>p4ssword<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>password</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>user</span><span class="token punctuation">&gt;</span></span>
</code></pre>
<p><strong>Payload Example 2 (available parameters):</strong></p>
<pre class=" language-xml"><code class="prism  language-xml"><span class="token prolog">&lt;?xml version="1.0" encoding="UTF-8" standalone="yes"?&gt;</span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>user</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>username</span><span class="token punctuation">&gt;</span></span>testuser<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>username</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>password</span><span class="token punctuation">&gt;</span></span>p4ssword<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>password</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>name</span><span class="token punctuation">&gt;</span></span>Test User<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>name</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>email</span><span class="token punctuation">&gt;</span></span><EMAIL><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>email</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>properties</span><span class="token punctuation">&gt;</span></span>
        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>property</span> <span class="token attr-name">key</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>keyname<span class="token punctuation">"</span></span> <span class="token attr-name">value</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>value<span class="token punctuation">"</span></span><span class="token punctuation">/&gt;</span></span>
        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>property</span> <span class="token attr-name">key</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>anotherkey<span class="token punctuation">"</span></span> <span class="token attr-name">value</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>value<span class="token punctuation">"</span></span><span class="token punctuation">/&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>properties</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>user</span><span class="token punctuation">&gt;</span></span>
</code></pre>
<h4 id="json-examples">JSON Examples</h4>
<blockquote>
<p><strong>Header:</strong> Authorization: Basic YWRtaW46MTIzNDU=<br>
<strong>Header:</strong> Content-Type: application/<strong>json</strong></p>
</blockquote>
<blockquote>
<p><strong>POST</strong> <a href="http://example.org:9090/plugins/restapi/v1/users">http://example.org:9090/plugins/restapi/v1/users</a></p>
</blockquote>
<p><strong>Payload Example 1 (required parameters):</strong></p>
<pre class=" language-json"><code class="prism  language-json"><span class="token punctuation">{</span>
    <span class="token string">"username"</span><span class="token punctuation">:</span> <span class="token string">"admin"</span><span class="token punctuation">,</span>
    <span class="token string">"password"</span><span class="token punctuation">:</span> <span class="token string">"p4ssword"</span>
<span class="token punctuation">}</span>
</code></pre>
<p><strong>Payload Example 2 (available parameters):</strong></p>
<pre class=" language-json"><code class="prism  language-json"><span class="token punctuation">{</span>
    <span class="token string">"username"</span><span class="token punctuation">:</span> <span class="token string">"admin"</span><span class="token punctuation">,</span>
    <span class="token string">"password"</span><span class="token punctuation">:</span> <span class="token string">"p4ssword"</span><span class="token punctuation">,</span>
    <span class="token string">"name"</span><span class="token punctuation">:</span> <span class="token string">"Administrator"</span><span class="token punctuation">,</span>
    <span class="token string">"email"</span><span class="token punctuation">:</span> <span class="token string">"<EMAIL>"</span><span class="token punctuation">,</span>
    <span class="token string">"properties"</span><span class="token punctuation">:</span> <span class="token punctuation">{</span>
        <span class="token string">"property"</span><span class="token punctuation">:</span> <span class="token punctuation">[</span>
            <span class="token punctuation">{</span>
                <span class="token string">"@key"</span><span class="token punctuation">:</span> <span class="token string">"console.rows_per_page"</span><span class="token punctuation">,</span>
                <span class="token string">"@value"</span><span class="token punctuation">:</span> <span class="token string">"user-summary=8"</span>
            <span class="token punctuation">}</span><span class="token punctuation">,</span>
            <span class="token punctuation">{</span>
                <span class="token string">"@key"</span><span class="token punctuation">:</span> <span class="token string">"console.order"</span><span class="token punctuation">,</span>
                <span class="token string">"@value"</span><span class="token punctuation">:</span> <span class="token string">"session-summary=1"</span>
            <span class="token punctuation">}</span>
        <span class="token punctuation">]</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>
</code></pre>
<p><strong>REST API Version 1.3.0 and later - Payload Example 2 (available parameters):</strong></p>
<pre class=" language-json"><code class="prism  language-json"><span class="token punctuation">{</span>
    <span class="token string">"users"</span><span class="token punctuation">:</span> <span class="token punctuation">[</span>
        <span class="token punctuation">{</span>
            <span class="token string">"username"</span><span class="token punctuation">:</span> <span class="token string">"admin"</span><span class="token punctuation">,</span>
            <span class="token string">"name"</span><span class="token punctuation">:</span> <span class="token string">"Administrator"</span><span class="token punctuation">,</span>
            <span class="token string">"email"</span><span class="token punctuation">:</span> <span class="token string">"<EMAIL>"</span><span class="token punctuation">,</span>
            <span class="token string">"password"</span><span class="token punctuation">:</span> <span class="token string">"p4ssword"</span><span class="token punctuation">,</span>
            <span class="token string">"properties"</span><span class="token punctuation">:</span> <span class="token punctuation">[</span>
                <span class="token punctuation">{</span>
                    <span class="token string">"key"</span><span class="token punctuation">:</span> <span class="token string">"console.order"</span><span class="token punctuation">,</span>
                    <span class="token string">"value"</span><span class="token punctuation">:</span> <span class="token string">"session-summary=0"</span>
                <span class="token punctuation">}</span>
            <span class="token punctuation">]</span>
        <span class="token punctuation">}</span><span class="token punctuation">,</span>
        <span class="token punctuation">{</span>
            <span class="token string">"username"</span><span class="token punctuation">:</span> <span class="token string">"test"</span><span class="token punctuation">,</span>
            <span class="token string">"name"</span><span class="token punctuation">:</span> <span class="token string">"Test"</span><span class="token punctuation">,</span>
            <span class="token string">"password"</span><span class="token punctuation">:</span> <span class="token string">"p4ssword"</span>
        <span class="token punctuation">}</span>
    <span class="token punctuation">]</span>
<span class="token punctuation">}</span>
</code></pre>
<h2 id="delete-a-user">Delete a user</h2>
<p>Endpoint to delete a user</p>
<blockquote>
<p><strong>DELETE</strong> /users/{username}</p>
</blockquote>
<p><strong>Payload:</strong> none<br>
<strong>Return value:</strong> HTTP status 200 (OK)</p>
<h3 id="possible-parameters-2">Possible parameters</h3>

<table>
<thead>
<tr>
<th>Parameter</th>
<th>Parameter Type</th>
<th>Description</th>
<th>Default value</th>
</tr>
</thead>
<tbody>
<tr>
<td>username</td>
<td>@Path</td>
<td>Exact username</td>
<td></td>
</tr>
</tbody>
</table><h3 id="examples-3">Examples</h3>
<blockquote>
<p><strong>Header:</strong> Authorization: Basic YWRtaW46MTIzNDU=<br>
<strong>DELETE</strong> <a href="http://example.org:9090/plugins/restapi/v1/users/testuser">http://example.org:9090/plugins/restapi/v1/users/testuser</a></p>
</blockquote>
<h2 id="update-a-user">Update a user</h2>
<p>Endpoint to update / rename a user</p>
<blockquote>
<p><strong>PUT</strong> /users/{username}</p>
</blockquote>
<p><strong>Payload:</strong> User<br>
<strong>Return value:</strong> HTTP status 200 (OK)</p>
<h3 id="possible-parameters-3">Possible parameters</h3>

<table>
<thead>
<tr>
<th>Parameter</th>
<th>Parameter Type</th>
<th>Description</th>
<th>Default value</th>
</tr>
</thead>
<tbody>
<tr>
<td>username</td>
<td>@Path</td>
<td>Exact username</td>
<td></td>
</tr>
</tbody>
</table><h3 id="examples-4">Examples</h3>
<h4 id="xml-example">XML Example</h4>
<blockquote>
<p><strong>Header:</strong> Authorization: Basic YWRtaW46MTIzNDU=<br>
<strong>Header:</strong> Content-Type application/xml</p>
</blockquote>
<blockquote>
<p><strong>PUT</strong> <a href="http://example.org:9090/plugins/restapi/v1/users/testuser">http://example.org:9090/plugins/restapi/v1/users/testuser</a></p>
</blockquote>
<p><strong>Payload:</strong></p>
<pre class=" language-xml"><code class="prism  language-xml"><span class="token prolog">&lt;?xml version="1.0" encoding="UTF-8" standalone="yes"?&gt;</span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>user</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>username</span><span class="token punctuation">&gt;</span></span>testuser<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>username</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>name</span><span class="token punctuation">&gt;</span></span>Test User edit<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>name</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>email</span><span class="token punctuation">&gt;</span></span><EMAIL><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>email</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>properties</span><span class="token punctuation">&gt;</span></span>
        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>property</span> <span class="token attr-name">key</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>keyname<span class="token punctuation">"</span></span> <span class="token attr-name">value</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>value<span class="token punctuation">"</span></span><span class="token punctuation">/&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>properties</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>user</span><span class="token punctuation">&gt;</span></span>
</code></pre>
<h4 id="rename-example">Rename Example</h4>
<blockquote>
<p><strong>Header:</strong> Authorization: Basic YWRtaW46MTIzNDU=<br>
<strong>Header:</strong> Content-Type application/xml</p>
</blockquote>
<blockquote>
<p><strong>PUT</strong> <a href="http://example.org:9090/plugins/restapi/v1/users/oldUsername">http://example.org:9090/plugins/restapi/v1/users/oldUsername</a></p>
</blockquote>
<p><strong>Payload:</strong></p>
<pre class=" language-xml"><code class="prism  language-xml"><span class="token prolog">&lt;?xml version="1.0" encoding="UTF-8" standalone="yes"?&gt;</span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>user</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>username</span><span class="token punctuation">&gt;</span></span>newUsername<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>username</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>name</span><span class="token punctuation">&gt;</span></span>Test User edit<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>name</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>email</span><span class="token punctuation">&gt;</span></span><EMAIL><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>email</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>properties</span><span class="token punctuation">&gt;</span></span>
        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>property</span> <span class="token attr-name">key</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>keyname<span class="token punctuation">"</span></span> <span class="token attr-name">value</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>value<span class="token punctuation">"</span></span><span class="token punctuation">/&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>properties</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>user</span><span class="token punctuation">&gt;</span></span>
</code></pre>
<h4 id="json-example">JSON Example</h4>
<blockquote>
<p><strong>Header:</strong> Authorization: Basic YWRtaW46MTIzNDU=<br>
<strong>Header:</strong> Content-Type application/json</p>
</blockquote>
<blockquote>
<p><strong>PUT</strong> <a href="http://example.org:9090/plugins/restapi/v1/users/testuser">http://example.org:9090/plugins/restapi/v1/users/testuser</a></p>
</blockquote>
<p><strong>Payload:</strong></p>
<pre class=" language-json"><code class="prism  language-json"><span class="token punctuation">{</span>
    <span class="token string">"username"</span><span class="token punctuation">:</span> <span class="token string">"testuser"</span><span class="token punctuation">,</span>
    <span class="token string">"name"</span><span class="token punctuation">:</span> <span class="token string">"Test User edit"</span><span class="token punctuation">,</span>
    <span class="token string">"email"</span><span class="token punctuation">:</span> <span class="token string">"<EMAIL>"</span><span class="token punctuation">,</span>
    <span class="token string">"properties"</span><span class="token punctuation">:</span> <span class="token punctuation">{</span>
        <span class="token string">"property"</span><span class="token punctuation">:</span> <span class="token punctuation">{</span>
            <span class="token string">"@key"</span><span class="token punctuation">:</span> <span class="token string">"keyname"</span><span class="token punctuation">,</span>
            <span class="token string">"@value"</span><span class="token punctuation">:</span> <span class="token string">"value"</span>
        <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>
</code></pre>
<p><strong>REST API Version 1.3.0 and later - Payload Example 2 (available parameters):</strong></p>
<pre class=" language-json"><code class="prism  language-json"><span class="token punctuation">{</span>
    <span class="token string">"username"</span><span class="token punctuation">:</span> <span class="token string">"testuser"</span><span class="token punctuation">,</span>
    <span class="token string">"name"</span><span class="token punctuation">:</span> <span class="token string">"Test User edit"</span><span class="token punctuation">,</span>
    <span class="token string">"email"</span><span class="token punctuation">:</span> <span class="token string">"<EMAIL>"</span><span class="token punctuation">,</span>
    <span class="token string">"properties"</span><span class="token punctuation">:</span> <span class="token punctuation">[</span>
        <span class="token punctuation">{</span>
            <span class="token string">"key"</span><span class="token punctuation">:</span> <span class="token string">"keyname"</span><span class="token punctuation">,</span>
            <span class="token string">"value"</span><span class="token punctuation">:</span> <span class="token string">"value"</span>
        <span class="token punctuation">}</span>
    <span class="token punctuation">]</span>
<span class="token punctuation">}</span>
</code></pre>
<h2 id="retrieve-all-user-groups">Retrieve all user groups</h2>
<p>Endpoint to get group names of a specific user</p>
<blockquote>
<p><strong>GET</strong> /users/{username}/groups</p>
</blockquote>
<p><strong>Payload:</strong> none<br>
<strong>Return value:</strong> Groups</p>
<h3 id="possible-parameters-4">Possible parameters</h3>

<table>
<thead>
<tr>
<th>Parameter</th>
<th>Parameter Type</th>
<th>Description</th>
<th>Default value</th>
</tr>
</thead>
<tbody>
<tr>
<td>username</td>
<td>@Path</td>
<td>Exact username</td>
<td></td>
</tr>
</tbody>
</table><h3 id="examples-5">Examples</h3>
<blockquote>
<p><strong>Header:</strong> Authorization: Basic YWRtaW46MTIzNDU=</p>
</blockquote>
<blockquote>
<p>**GET ** <a href="http://example.org:9090/plugins/restapi/v1/users/testuser/groups">http://example.org:9090/plugins/restapi/v1/users/testuser/groups</a></p>
</blockquote>
<h2 id="add-user-to-groups">Add user to groups</h2>
<p>Endpoint to add user to a groups</p>
<blockquote>
<p><strong>POST</strong> /users/{username}/groups</p>
</blockquote>
<p><strong>Payload:</strong> Groups<br>
<strong>Return value:</strong> HTTP status 201 (Created)</p>
<h3 id="possible-parameters-5">Possible parameters</h3>

<table>
<thead>
<tr>
<th>Parameter</th>
<th>Parameter Type</th>
<th>Description</th>
<th>Default value</th>
</tr>
</thead>
<tbody>
<tr>
<td>username</td>
<td>@Path</td>
<td>Exact username</td>
<td></td>
</tr>
</tbody>
</table><h3 id="examples-6">Examples</h3>
<blockquote>
<p><strong>Header:</strong> Authorization: Basic YWRtaW46MTIzNDU=<br>
<strong>Header:</strong> Content-Type application/xml</p>
</blockquote>
<blockquote>
<p><strong>POST</strong> <a href="http://example.org:9090/plugins/restapi/v1/users/testuser/groups">http://example.org:9090/plugins/restapi/v1/users/testuser/groups</a></p>
</blockquote>
<p><strong>Payload:</strong></p>
<pre class=" language-xml"><code class="prism  language-xml"><span class="token prolog">&lt;?xml version="1.0" encoding="UTF-8" standalone="yes"?&gt;</span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>groups</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>groupname</span><span class="token punctuation">&gt;</span></span>Admins<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>groupname</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>groupname</span><span class="token punctuation">&gt;</span></span>Support<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>groupname</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>groups</span><span class="token punctuation">&gt;</span></span>
</code></pre>
<h2 id="add-user-to-group">Add user to group</h2>
<p>Endpoint to add user to a group</p>
<blockquote>
<p><strong>POST</strong> /users/{username}/groups/{groupName}</p>
</blockquote>
<p><strong>Payload:</strong> none<br>
<strong>Return value:</strong> HTTP status 201 (Created)</p>
<h3 id="possible-parameters-6">Possible parameters</h3>

<table>
<thead>
<tr>
<th>Parameter</th>
<th>Parameter Type</th>
<th>Description</th>
<th>Default value</th>
</tr>
</thead>
<tbody>
<tr>
<td>username</td>
<td>@Path</td>
<td>Exact username</td>
<td></td>
</tr>
<tr>
<td>groupName</td>
<td>@Path</td>
<td>Exact group name</td>
<td></td>
</tr>
</tbody>
</table><h3 id="examples-7">Examples</h3>
<blockquote>
<p><strong>Header:</strong> Authorization: Basic YWRtaW46MTIzNDU=<br>
<strong>Header:</strong> Content-Type application/xml</p>
</blockquote>
<blockquote>
<p><strong>POST</strong> <a href="http://example.org:9090/plugins/restapi/v1/users/testuser/groups/testGroup">http://example.org:9090/plugins/restapi/v1/users/testuser/groups/testGroup</a></p>
</blockquote>
<h2 id="delete-a-user-from-a-groups">Delete a user from a groups</h2>
<p>Endpoint to remove a user from a groups</p>
<blockquote>
<p><strong>DELETE</strong> /users/{username}/groups</p>
</blockquote>
<p><strong>Payload:</strong> Groups<br>
<strong>Return value:</strong> HTTP status 200 (OK)</p>
<h3 id="possible-parameters-7">Possible parameters</h3>

<table>
<thead>
<tr>
<th>Parameter</th>
<th>Parameter Type</th>
<th>Description</th>
<th>Default value</th>
</tr>
</thead>
<tbody>
<tr>
<td>username</td>
<td>@Path</td>
<td>Exact username</td>
<td></td>
</tr>
</tbody>
</table><h3 id="examples-8">Examples</h3>
<blockquote>
<p><strong>Header:</strong> Authorization: Basic YWRtaW46MTIzNDU=<br>
<strong>Header:</strong> Content-Type application/xml</p>
</blockquote>
<blockquote>
<p><strong>DELETE</strong> <a href="http://example.org:9090/plugins/restapi/v1/users/testuser/groups">http://example.org:9090/plugins/restapi/v1/users/testuser/groups</a></p>
</blockquote>
<p><strong>Payload:</strong></p>
<pre class=" language-xml"><code class="prism  language-xml"><span class="token prolog">&lt;?xml version="1.0" encoding="UTF-8" standalone="yes"?&gt;</span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>groups</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>groupname</span><span class="token punctuation">&gt;</span></span>Admins<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>groupname</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>groupname</span><span class="token punctuation">&gt;</span></span>Support<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>groupname</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>groups</span><span class="token punctuation">&gt;</span></span>
</code></pre>
<h2 id="delete-a-user-from-a-group">Delete a user from a group</h2>
<p>Endpoint to remove a user from a group</p>
<blockquote>
<p><strong>DELETE</strong> /users/{username}/groups/{groupName}</p>
</blockquote>
<p><strong>Payload:</strong> none<br>
<strong>Return value:</strong> HTTP status 200 (OK)</p>
<h3 id="possible-parameters-8">Possible parameters</h3>

<table>
<thead>
<tr>
<th>Parameter</th>
<th>Parameter Type</th>
<th>Description</th>
<th>Default value</th>
</tr>
</thead>
<tbody>
<tr>
<td>username</td>
<td>@Path</td>
<td>Exact username</td>
<td></td>
</tr>
<tr>
<td>groupName</td>
<td>@Path</td>
<td>Exact group name</td>
<td></td>
</tr>
</tbody>
</table><h3 id="examples-9">Examples</h3>
<blockquote>
<p><strong>Header:</strong> Authorization: Basic YWRtaW46MTIzNDU=<br>
<strong>Header:</strong> Content-Type application/xml</p>
</blockquote>
<blockquote>
<p><strong>DELETE</strong> <a href="http://example.org:9090/plugins/restapi/v1/users/testuser/groups/testGroup">http://example.org:9090/plugins/restapi/v1/users/testuser/groups/testGroup</a></p>
</blockquote>
<h2 id="lockout-a-user">Lockout a user</h2>
<p>Endpoint to lockout / ban the user from the chat server. The user will be kicked if the user is online.</p>
<blockquote>
<p><strong>POST</strong> /lockouts/{username}</p>
</blockquote>
<p><strong>Payload:</strong> none<br>
<strong>Return value:</strong> HTTP status 201 (Created)</p>
<h3 id="possible-parameters-9">Possible parameters</h3>

<table>
<thead>
<tr>
<th>Parameter</th>
<th>Parameter Type</th>
<th>Description</th>
<th>Default value</th>
</tr>
</thead>
<tbody>
<tr>
<td>username</td>
<td>@Path</td>
<td>Exact username</td>
<td></td>
</tr>
</tbody>
</table><h3 id="examples-10">Examples</h3>
<blockquote>
<p><strong>Header:</strong> Authorization: Basic YWRtaW46MTIzNDU=</p>
</blockquote>
<blockquote>
<p><strong>POST</strong> <a href="http://example.org:9090/plugins/restapi/v1/lockouts/testuser">http://example.org:9090/plugins/restapi/v1/lockouts/testuser</a></p>
</blockquote>
<h2 id="unlock-a-user">Unlock a user</h2>
<p>Endpoint to unlock / unban the user</p>
<blockquote>
<p><strong>DELETE</strong> /lockouts/{username}</p>
</blockquote>
<p><strong>Payload:</strong> none<br>
<strong>Return value:</strong> HTTP status 200 (OK)</p>
<h3 id="possible-parameters-10">Possible parameters</h3>

<table>
<thead>
<tr>
<th>Parameter</th>
<th>Parameter Type</th>
<th>Description</th>
<th>Default value</th>
</tr>
</thead>
<tbody>
<tr>
<td>username</td>
<td>@Path</td>
<td>Exact username</td>
<td></td>
</tr>
</tbody>
</table><h3 id="examples-11">Examples</h3>
<blockquote>
<p><strong>Header:</strong> Authorization: Basic YWRtaW46MTIzNDU=</p>
</blockquote>
<blockquote>
<p><strong>DELETE</strong> <a href="http://example.org:9090/plugins/restapi/v1/lockouts/testuser">http://example.org:9090/plugins/restapi/v1/lockouts/testuser</a></p>
</blockquote>
<h2 id="retrieve-user-roster">Retrieve user roster</h2>
<p>Endpoint to get roster entries (buddies) from a specific user</p>
<blockquote>
<p><strong>GET</strong> /users/{username}/roster</p>
</blockquote>
<p><strong>Payload:</strong> none<br>
<strong>Return value:</strong> Roster</p>
<h3 id="possible-parameters-11">Possible parameters</h3>

<table>
<thead>
<tr>
<th>Parameter</th>
<th>Parameter Type</th>
<th>Description</th>
<th>Default value</th>
</tr>
</thead>
<tbody>
<tr>
<td>username</td>
<td>@Path</td>
<td>Exact username</td>
<td></td>
</tr>
</tbody>
</table><h3 id="examples-12">Examples</h3>
<blockquote>
<p><strong>Header:</strong> Authorization: Basic YWRtaW46MTIzNDU=</p>
</blockquote>
<blockquote>
<p><strong>GET</strong><a href="http://example.org:9090/plugins/restapi/v1/users/testuser/roster">http://example.org:9090/plugins/restapi/v1/users/testuser/roster</a></p>
</blockquote>
<h2 id="create-a-user-roster-entry">Create a user roster entry</h2>
<p>Endpoint to add a new roster entry to a user</p>
<blockquote>
<p><strong>POST</strong> /users/{username}/roster</p>
</blockquote>
<p><strong>Payload:</strong> RosterItem<br>
<strong>Return value:</strong> HTTP status 201 (Created)</p>
<h3 id="possible-parameters-12">Possible parameters</h3>

<table>
<thead>
<tr>
<th>Parameter</th>
<th>Parameter Type</th>
<th>Description</th>
<th>Default value</th>
</tr>
</thead>
<tbody>
<tr>
<td>username</td>
<td>@Path</td>
<td>Exact username</td>
<td></td>
</tr>
</tbody>
</table><h3 id="examples-13">Examples</h3>
<blockquote>
<p><strong>Header:</strong> Authorization: Basic YWRtaW46MTIzNDU=<br>
<strong>Header:</strong> Content-Type application/xml</p>
</blockquote>
<blockquote>
<p><strong>POST</strong> <a href="http://example.org:9090/plugins/restapi/v1/users/testuser/roster">http://example.org:9090/plugins/restapi/v1/users/testuser/roster</a></p>
</blockquote>
<p><strong>Payload:</strong><br>
Payload Example 1 (required parameters):</p>
<pre class=" language-xml"><code class="prism  language-xml"><span class="token prolog">&lt;?xml version="1.0" encoding="UTF-8" standalone="yes"?&gt;</span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>rosterItem</span><span class="token punctuation">&gt;</span></span>
	<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>jid</span><span class="token punctuation">&gt;</span></span><EMAIL><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>jid</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>rosterItem</span><span class="token punctuation">&gt;</span></span>
</code></pre>
<p>Payload Example 2 (available parameters):</p>
<pre class=" language-xml"><code class="prism  language-xml"><span class="token prolog">&lt;?xml version="1.0" encoding="UTF-8" standalone="yes"?&gt;</span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>rosterItem</span><span class="token punctuation">&gt;</span></span>
	<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>jid</span><span class="token punctuation">&gt;</span></span><EMAIL><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>jid</span><span class="token punctuation">&gt;</span></span>
	<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>nickname</span><span class="token punctuation">&gt;</span></span>Peter1<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>nickname</span><span class="token punctuation">&gt;</span></span>
	<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>subscriptionType</span><span class="token punctuation">&gt;</span></span>3<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>subscriptionType</span><span class="token punctuation">&gt;</span></span>
	<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>groups</span><span class="token punctuation">&gt;</span></span>
		<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>group</span><span class="token punctuation">&gt;</span></span>Friends<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>group</span><span class="token punctuation">&gt;</span></span>
	<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>groups</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>rosterItem</span><span class="token punctuation">&gt;</span></span>
</code></pre>
<h2 id="delete-a-user-roster-entry">Delete a user roster entry</h2>
<p>Endpoint to remove a roster entry from a user</p>
<blockquote>
<p><strong>DELETE</strong> /users/{username}/roster/{jid}</p>
</blockquote>
<p><strong>Payload:</strong> none<br>
<strong>Return value:</strong> HTTP status 200 (OK)</p>
<h3 id="possible-parameters-13">Possible parameters</h3>

<table>
<thead>
<tr>
<th>Parameter</th>
<th>Parameter Type</th>
<th>Description</th>
<th>Default value</th>
</tr>
</thead>
<tbody>
<tr>
<td>username</td>
<td>@Path</td>
<td>Exact username</td>
<td></td>
</tr>
<tr>
<td>jid</td>
<td>@Path</td>
<td>JID of the roster item</td>
<td></td>
</tr>
</tbody>
</table><h3 id="examples-14">Examples</h3>
<blockquote>
<p><strong>Header:</strong> Authorization: Basic YWRtaW46MTIzNDU=</p>
</blockquote>
<blockquote>
<p><strong>DELETE</strong> <a href="http://example.org:9090/plugins/restapi/v1/users/testuser/roster/<EMAIL>">http://example.org:9090/plugins/restapi/v1/users/testuser/roster/<EMAIL></a></p>
</blockquote>
<h2 id="update-a-user-roster-entry">Update a user roster entry</h2>
<p>Endpoint to update a roster entry</p>
<blockquote>
<p><strong>PUT</strong> /users/{username}/roster/{jid}</p>
</blockquote>
<p><strong>Payload:</strong> RosterItem<br>
<strong>Return value:</strong> HTTP status 200 (OK)</p>
<h3 id="possible-parameters-14">Possible parameters</h3>

<table>
<thead>
<tr>
<th>Parameter</th>
<th>Parameter Type</th>
<th>Description</th>
<th>Default value</th>
</tr>
</thead>
<tbody>
<tr>
<td>username</td>
<td>@Path</td>
<td>Exact username</td>
<td></td>
</tr>
<tr>
<td>jid</td>
<td>@Path</td>
<td>JID of the roster item</td>
<td></td>
</tr>
</tbody>
</table><h3 id="examples-15">Examples</h3>
<blockquote>
<p><strong>Header:</strong> Authorization: Basic YWRtaW46MTIzNDU=<br>
<strong>Header:</strong> Content-Type application/xml</p>
</blockquote>
<blockquote>
<p><strong>PUT</strong> <a href="http://example.org:9090/plugins/restapi/v1/users/testuser/roster/<EMAIL>">http://example.org:9090/plugins/restapi/v1/users/testuser/roster/<EMAIL></a></p>
</blockquote>
<p><strong>Payload:</strong></p>
<pre class=" language-xml"><code class="prism  language-xml"><span class="token prolog">&lt;?xml version="1.0" encoding="UTF-8" standalone="yes"?&gt;</span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>rosterItem</span><span class="token punctuation">&gt;</span></span>
	<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>jid</span><span class="token punctuation">&gt;</span></span><EMAIL><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>jid</span><span class="token punctuation">&gt;</span></span>
	<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>nickname</span><span class="token punctuation">&gt;</span></span>Peter Pan<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>nickname</span><span class="token punctuation">&gt;</span></span>
	<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>subscriptionType</span><span class="token punctuation">&gt;</span></span>0<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>subscriptionType</span><span class="token punctuation">&gt;</span></span>
	<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>groups</span><span class="token punctuation">&gt;</span></span>
		<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>group</span><span class="token punctuation">&gt;</span></span>Support<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>group</span><span class="token punctuation">&gt;</span></span>
	<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>groups</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>rosterItem</span><span class="token punctuation">&gt;</span></span>
</code></pre>
<h1 id="chat-room-related-rest-endpoints">Chat room related REST Endpoints</h1>
<h2 id="retrieve-all-chat-rooms">Retrieve all chat rooms</h2>
<p>Endpoint to get all chat rooms</p>
<blockquote>
<p><strong>GET</strong> /chatrooms</p>
</blockquote>
<p><strong>Payload:</strong> none<br>
<strong>Return value:</strong> Chatrooms</p>
<h3 id="possible-parameters-15">Possible parameters</h3>

<table>
<thead>
<tr>
<th>Parameter</th>
<th>Parameter Type</th>
<th>Description</th>
<th>Default value</th>
</tr>
</thead>
<tbody>
<tr>
<td>servicename</td>
<td>@QueryParam</td>
<td>The name of the Group Chat Service</td>
<td>conference</td>
</tr>
<tr>
<td>type</td>
<td>@QueryParam</td>
<td><strong>public:</strong> Only as List Room in Directory set rooms <br> <strong>all:</strong> All rooms.</td>
<td>public</td>
</tr>
<tr>
<td>search</td>
<td>@QueryParam</td>
<td>Search/Filter by room name. <br> This act like the wildcard search %String%</td>
<td></td>
</tr>
</tbody>
</table><h3 id="examples-16">Examples</h3>
<blockquote>
<p><strong>Header</strong>: Authorization: Basic YWRtaW46MTIzNDU=</p>
</blockquote>
<blockquote>
<p><strong>GET</strong> <a href="http://example.org:9090/plugins/restapi/v1/chatrooms">http://example.org:9090/plugins/restapi/v1/chatrooms</a><br>
<strong>GET</strong> <a href="http://example.org:9090/plugins/restapi/v1/chatrooms?type=all">http://example.org:9090/plugins/restapi/v1/chatrooms?type=all</a><br>
<strong>GET</strong> <a href="http://example.org:9090/plugins/restapi/v1/chatrooms?type=all&amp;servicename=privateconf">http://example.org:9090/plugins/restapi/v1/chatrooms?type=all&amp;servicename=privateconf</a><br>
<strong>GET</strong> <a href="http://example.org:9090/plugins/restapi/v1/chatrooms?search=test">http://example.org:9090/plugins/restapi/v1/chatrooms?search=test</a></p>
</blockquote>
<h2 id="retrieve-a-chat-room">Retrieve a chat room</h2>
<p>Endpoint to get information over specific chat room</p>
<blockquote>
<p><strong>GET</strong> /chatrooms<span>/{roomName}</span></p>
</blockquote>
<p><strong>Payload:</strong> none<br>
<strong>Return value:</strong> Chatroom</p>
<h3 id="possible-parameters-16">Possible parameters</h3>

<table>
<thead>
<tr>
<th>Parameter</th>
<th>Parameter Type</th>
<th>Description</th>
<th>Default value</th>
</tr>
</thead>
<tbody>
<tr>
<td>roomname</td>
<td>@Path</td>
<td>Exact room name</td>
<td></td>
</tr>
<tr>
<td>servicename</td>
<td>@QueryParam</td>
<td>The name of the Group Chat Service</td>
<td>conference</td>
</tr>
</tbody>
</table><h3 id="examples-17">Examples</h3>
<blockquote>
<p><strong>Header:</strong> Authorization: Basic YWRtaW46MTIzNDU=</p>
</blockquote>
<blockquote>
<p><strong>GET</strong> <a href="http://example.org:9090/plugins/restapi/v1/chatrooms/test">http://example.org:9090/plugins/restapi/v1/chatrooms/test</a><br>
<strong>GET</strong> <a href="http://example.org:9090/plugins/restapi/v1/chatrooms/test?servicename=privateconf">http://example.org:9090/plugins/restapi/v1/chatrooms/test?servicename=privateconf</a></p>
</blockquote>
<h2 id="retrieve-chat-room-participants">Retrieve chat room participants</h2>
<p>Endpoint to get all participants with a role of specified room.</p>
<blockquote>
<p><strong>GET</strong> /chatrooms/{roomName}/participants</p>
</blockquote>
<p><strong>Payload:</strong> none<br>
<strong>Return value:</strong> Participants</p>
<h3 id="possible-parameters-17">Possible parameters</h3>

<table>
<thead>
<tr>
<th>Parameter</th>
<th>Parameter Type</th>
<th>Description</th>
<th>Default value</th>
</tr>
</thead>
<tbody>
<tr>
<td>roomname</td>
<td>@Path</td>
<td>Exact room name</td>
<td></td>
</tr>
<tr>
<td>servicename</td>
<td>@QueryParam</td>
<td>The name of the Group Chat Service</td>
<td>conference</td>
</tr>
</tbody>
</table><h3 id="examples-18">Examples</h3>
<blockquote>
<p><strong>Header:</strong> Authorization: Basic YWRtaW46MTIzNDU=</p>
</blockquote>
<blockquote>
<p><strong>GET</strong> <a href="http://example.org:9090/plugins/restapi/v1/chatrooms/room1/participants">http://example.org:9090/plugins/restapi/v1/chatrooms/room1/participants</a></p>
</blockquote>
<h2 id="retrieve-chat-room-occupants">Retrieve chat room occupants</h2>
<p>Endpoint to get all occupants (all roles / affiliations) of a specified room.</p>
<blockquote>
<p><strong>GET</strong> /chatrooms/{roomName}/occupants</p>
</blockquote>
<p><strong>Payload:</strong> none<br>
<strong>Return value:</strong> Occupants</p>
<h3 id="possible-parameters-18">Possible parameters</h3>

<table>
<thead>
<tr>
<th>Parameter</th>
<th>Parameter Type</th>
<th>Description</th>
<th>Default value</th>
</tr>
</thead>
<tbody>
<tr>
<td>roomname</td>
<td>@Path</td>
<td>Exact room name</td>
<td></td>
</tr>
<tr>
<td>servicename</td>
<td>@QueryParam</td>
<td>The name of the Group Chat Service</td>
<td>conference</td>
</tr>
</tbody>
</table><h3 id="examples-19">Examples</h3>
<blockquote>
<p><strong>Header:</strong> Authorization: Basic YWRtaW46MTIzNDU=</p>
</blockquote>
<blockquote>
<p><strong>GET</strong> <a href="http://example.org:9090/plugins/restapi/v1/chatrooms/room1/occupants">http://example.org:9090/plugins/restapi/v1/chatrooms/room1/occupants</a></p>
</blockquote>
        <h2 id="retrieve-chat-room-history">Retrieve chat room message history</h2>
        <p>Endpoint to get the chat message history of a specified room.</p>
        <blockquote>
            <p>
                <strong>GET</strong> /chatrooms/{roomName}/chathistory</p>
        </blockquote>
        <p>
            <strong>Payload:</strong> none<br>
            <strong>Return value:</strong> Chat History</p>
        <h3 id="possible-parameters-18">Possible parameters</h3>

        <table>
            <thead>
            <tr>
                <th>Parameter</th>
                <th>Parameter Type</th>
                <th>Description</th>
                <th>Default value</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td>roomname</td>
                <td>@Path</td>
                <td>Exact room name</td>
                <td/>
            </tr>
            <tr>
                <td>servicename</td>
                <td>@QueryParam</td>
                <td>The name of the Group Chat Service</td>
                <td>conference</td>
            </tr>
            </tbody>
        </table>
        </p>

<h2 id="create-a-chat-room">Create a chat room</h2>
<p>Endpoint to create a new chat room.</p>
<blockquote>
<p><strong>POST</strong> /chatrooms</p>
</blockquote>
<p><strong>Payload:</strong> Chatroom<br>
<strong>Return value:</strong> HTTP status 201 (Created)</p>
<h3 id="possible-parameters-19">Possible parameters</h3>

<table>
<thead>
<tr>
<th>Parameter</th>
<th>Parameter Type</th>
<th>Description</th>
<th>Default value</th>
</tr>
</thead>
<tbody>
<tr>
<td>servicename</td>
<td>@QueryParam</td>
<td>The name of the Group Chat Service</td>
<td>conference</td>
</tr>
</tbody>
</table><h3 id="xml-examples-1">XML Examples</h3>
<blockquote>
<p><strong>Header:</strong> Authorization: Basic YWRtaW46MTIzNDU=<br>
<strong>Header:</strong> Content-Type: application/xml</p>
</blockquote>
<blockquote>
<p><strong>POST</strong> <a href="http://example.org:9090/plugins/restapi/v1/chatrooms">http://example.org:9090/plugins/restapi/v1/chatrooms</a></p>
</blockquote>
<p><strong>Payload Example 1 (required parameters):</strong></p>
<pre class=" language-xml"><code class="prism  language-xml"><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>chatRoom</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>naturalName</span><span class="token punctuation">&gt;</span></span>global-1<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>naturalName</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>roomName</span><span class="token punctuation">&gt;</span></span>global<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>roomName</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>description</span><span class="token punctuation">&gt;</span></span>Global Chat Room<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>description</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>chatRoom</span><span class="token punctuation">&gt;</span></span>
</code></pre>
<p><strong>Payload Example 2 (available parameters):</strong></p>
<pre class=" language-xml"><code class="prism  language-xml"><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>chatRoom</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>roomName</span><span class="token punctuation">&gt;</span></span>global<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>roomName</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>naturalName</span><span class="token punctuation">&gt;</span></span>global-2<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>naturalName</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>description</span><span class="token punctuation">&gt;</span></span>Global Chat Room<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>description</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>subject</span><span class="token punctuation">&gt;</span></span>global-2 Subject<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>subject</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>creationDate</span><span class="token punctuation">&gt;</span></span>2014-02-12T15:52:37.592+01:00<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>creationDate</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>modificationDate</span><span class="token punctuation">&gt;</span></span>2014-09-12T15:35:54.702+02:00<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>modificationDate</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>maxUsers</span><span class="token punctuation">&gt;</span></span>0<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>maxUsers</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>persistent</span><span class="token punctuation">&gt;</span></span>true<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>persistent</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>publicRoom</span><span class="token punctuation">&gt;</span></span>true<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>publicRoom</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>registrationEnabled</span><span class="token punctuation">&gt;</span></span>false<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>registrationEnabled</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>canAnyoneDiscoverJID</span><span class="token punctuation">&gt;</span></span>false<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>canAnyoneDiscoverJID</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>canOccupantsChangeSubject</span><span class="token punctuation">&gt;</span></span>false<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>canOccupantsChangeSubject</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>canOccupantsInvite</span><span class="token punctuation">&gt;</span></span>false<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>canOccupantsInvite</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>canChangeNickname</span><span class="token punctuation">&gt;</span></span>false<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>canChangeNickname</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>logEnabled</span><span class="token punctuation">&gt;</span></span>true<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>logEnabled</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>loginRestrictedToNickname</span><span class="token punctuation">&gt;</span></span>false<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>loginRestrictedToNickname</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>membersOnly</span><span class="token punctuation">&gt;</span></span>false<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>membersOnly</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>moderated</span><span class="token punctuation">&gt;</span></span>false<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>moderated</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>broadcastPresenceRoles</span><span class="token punctuation">&gt;</span></span>
        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>broadcastPresenceRole</span><span class="token punctuation">&gt;</span></span>moderator<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>broadcastPresenceRole</span><span class="token punctuation">&gt;</span></span>
        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>broadcastPresenceRole</span><span class="token punctuation">&gt;</span></span>participant<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>broadcastPresenceRole</span><span class="token punctuation">&gt;</span></span>
        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>broadcastPresenceRole</span><span class="token punctuation">&gt;</span></span>visitor<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>broadcastPresenceRole</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>broadcastPresenceRoles</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>owners</span><span class="token punctuation">&gt;</span></span>
        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>owner</span><span class="token punctuation">&gt;</span></span>owner@localhost<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>owner</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>owners</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>admins</span><span class="token punctuation">&gt;</span></span>
        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>admin</span><span class="token punctuation">&gt;</span></span>admin@localhost<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>admin</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>admins</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>members</span><span class="token punctuation">&gt;</span></span>
        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>member</span><span class="token punctuation">&gt;</span></span>member2@localhost<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>member</span><span class="token punctuation">&gt;</span></span>
        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>member</span><span class="token punctuation">&gt;</span></span>member1@localhost<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>member</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>members</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>outcasts</span><span class="token punctuation">&gt;</span></span>
        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>outcast</span><span class="token punctuation">&gt;</span></span>outcast1@localhost<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>outcast</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>outcasts</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>chatRoom</span><span class="token punctuation">&gt;</span></span>
</code></pre>
<h3 id="json-examples-1">JSON Examples</h3>
<blockquote>
<p><strong>Header:</strong> Authorization: Basic YWRtaW46MTIzNDU=<br>
<strong>Header:</strong> Content-Type: application/json</p>
</blockquote>
<blockquote>
<p><strong>POST</strong> <a href="http://example.org:9090/plugins/restapi/v1/chatrooms">http://example.org:9090/plugins/restapi/v1/chatrooms</a></p>
</blockquote>
<p><strong>Payload Example 1 (required parameters):</strong></p>
<pre class=" language-json"><code class="prism  language-json"><span class="token punctuation">{</span>
	<span class="token string">"roomName"</span><span class="token punctuation">:</span> <span class="token string">"global"</span><span class="token punctuation">,</span>
	<span class="token string">"naturalName"</span><span class="token punctuation">:</span> <span class="token string">"global-2"</span><span class="token punctuation">,</span>
	<span class="token string">"description"</span><span class="token punctuation">:</span> <span class="token string">"Global chat room"</span>
<span class="token punctuation">}</span>
</code></pre>
<p><strong>Payload Example 2 (available parameters):</strong></p>
<pre class=" language-json"><code class="prism  language-json"><span class="token punctuation">{</span>
    <span class="token string">"roomName"</span><span class="token punctuation">:</span> <span class="token string">"global-1"</span><span class="token punctuation">,</span>
    <span class="token string">"naturalName"</span><span class="token punctuation">:</span> <span class="token string">"global-1_test_hello"</span><span class="token punctuation">,</span>
    <span class="token string">"description"</span><span class="token punctuation">:</span> <span class="token string">"Global chat room"</span><span class="token punctuation">,</span>
    <span class="token string">"subject"</span><span class="token punctuation">:</span> <span class="token string">"Global chat room subject"</span><span class="token punctuation">,</span>
    <span class="token string">"creationDate"</span><span class="token punctuation">:</span> <span class="token string">"2012-10-18T16:55:12.803+02:00"</span><span class="token punctuation">,</span>
    <span class="token string">"modificationDate"</span><span class="token punctuation">:</span> <span class="token string">"2014-07-10T09:49:12.411+02:00"</span><span class="token punctuation">,</span>
    <span class="token string">"maxUsers"</span><span class="token punctuation">:</span> <span class="token string">"0"</span><span class="token punctuation">,</span>
    <span class="token string">"persistent"</span><span class="token punctuation">:</span> <span class="token string">"true"</span><span class="token punctuation">,</span>
    <span class="token string">"publicRoom"</span><span class="token punctuation">:</span> <span class="token string">"true"</span><span class="token punctuation">,</span>
    <span class="token string">"registrationEnabled"</span><span class="token punctuation">:</span> <span class="token string">"false"</span><span class="token punctuation">,</span>
    <span class="token string">"canAnyoneDiscoverJID"</span><span class="token punctuation">:</span> <span class="token string">"true"</span><span class="token punctuation">,</span>
    <span class="token string">"canOccupantsChangeSubject"</span><span class="token punctuation">:</span> <span class="token string">"false"</span><span class="token punctuation">,</span>
    <span class="token string">"canOccupantsInvite"</span><span class="token punctuation">:</span> <span class="token string">"false"</span><span class="token punctuation">,</span>
    <span class="token string">"canChangeNickname"</span><span class="token punctuation">:</span> <span class="token string">"false"</span><span class="token punctuation">,</span>
    <span class="token string">"logEnabled"</span><span class="token punctuation">:</span> <span class="token string">"true"</span><span class="token punctuation">,</span>
    <span class="token string">"loginRestrictedToNickname"</span><span class="token punctuation">:</span> <span class="token string">"true"</span><span class="token punctuation">,</span>
    <span class="token string">"membersOnly"</span><span class="token punctuation">:</span> <span class="token string">"false"</span><span class="token punctuation">,</span>
    <span class="token string">"moderated"</span><span class="token punctuation">:</span> <span class="token string">"false"</span><span class="token punctuation">,</span>
    <span class="token string">"broadcastPresenceRoles"</span><span class="token punctuation">:</span> <span class="token punctuation">{</span>
        <span class="token string">"broadcastPresenceRole"</span><span class="token punctuation">:</span> <span class="token punctuation">[</span>
            <span class="token string">"moderator"</span><span class="token punctuation">,</span>
            <span class="token string">"participant"</span><span class="token punctuation">,</span>
            <span class="token string">"visitor"</span>
        <span class="token punctuation">]</span>
    <span class="token punctuation">}</span><span class="token punctuation">,</span>
    <span class="token string">"owners"</span><span class="token punctuation">:</span> <span class="token punctuation">{</span>
        <span class="token string">"owner"</span><span class="token punctuation">:</span> <span class="token string">"owner@localhost"</span>
    <span class="token punctuation">}</span><span class="token punctuation">,</span>
    <span class="token string">"admins"</span><span class="token punctuation">:</span> <span class="token punctuation">{</span>
        <span class="token string">"admin"</span><span class="token punctuation">:</span> <span class="token punctuation">[</span>
            <span class="token string">"admin@localhost"</span><span class="token punctuation">,</span>
            <span class="token string">"admin2@localhost"</span>
        <span class="token punctuation">]</span>
    <span class="token punctuation">}</span><span class="token punctuation">,</span>
    <span class="token string">"members"</span><span class="token punctuation">:</span> <span class="token punctuation">{</span>
        <span class="token string">"member"</span><span class="token punctuation">:</span> <span class="token punctuation">[</span>
            <span class="token string">"member@localhost"</span><span class="token punctuation">,</span>
            <span class="token string">"member2@localhost"</span>
        <span class="token punctuation">]</span>
    <span class="token punctuation">}</span><span class="token punctuation">,</span>
    <span class="token string">"outcasts"</span><span class="token punctuation">:</span> <span class="token punctuation">{</span>
        <span class="token string">"outcast"</span><span class="token punctuation">:</span> <span class="token punctuation">[</span>
            <span class="token string">"outcast@localhost"</span><span class="token punctuation">,</span>
            <span class="token string">"outcast2@localhost"</span>
        <span class="token punctuation">]</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>
</code></pre>
<p><strong>REST API Version 1.3.0 and later - Payload Example 2 (available parameters):</strong></p>
<pre class=" language-json"><code class="prism  language-json"><span class="token punctuation">{</span>
    <span class="token string">"roomName"</span><span class="token punctuation">:</span> <span class="token string">"global-1"</span><span class="token punctuation">,</span>
    <span class="token string">"naturalName"</span><span class="token punctuation">:</span> <span class="token string">"global-1_test_hello"</span><span class="token punctuation">,</span>
    <span class="token string">"description"</span><span class="token punctuation">:</span> <span class="token string">"Global chat room"</span><span class="token punctuation">,</span>
    <span class="token string">"subject"</span><span class="token punctuation">:</span> <span class="token string">"Global chat room subject"</span><span class="token punctuation">,</span>
    <span class="token string">"creationDate"</span><span class="token punctuation">:</span> <span class="token string">"2012-10-18T16:55:12.803+02:00"</span><span class="token punctuation">,</span>
    <span class="token string">"modificationDate"</span><span class="token punctuation">:</span> <span class="token string">"2014-07-10T09:49:12.411+02:00"</span><span class="token punctuation">,</span>
    <span class="token string">"maxUsers"</span><span class="token punctuation">:</span> <span class="token string">"0"</span><span class="token punctuation">,</span>
    <span class="token string">"persistent"</span><span class="token punctuation">:</span> <span class="token string">"true"</span><span class="token punctuation">,</span>
    <span class="token string">"publicRoom"</span><span class="token punctuation">:</span> <span class="token string">"true"</span><span class="token punctuation">,</span>
    <span class="token string">"registrationEnabled"</span><span class="token punctuation">:</span> <span class="token string">"false"</span><span class="token punctuation">,</span>
    <span class="token string">"canAnyoneDiscoverJID"</span><span class="token punctuation">:</span> <span class="token string">"true"</span><span class="token punctuation">,</span>
    <span class="token string">"canOccupantsChangeSubject"</span><span class="token punctuation">:</span> <span class="token string">"false"</span><span class="token punctuation">,</span>
    <span class="token string">"canOccupantsInvite"</span><span class="token punctuation">:</span> <span class="token string">"false"</span><span class="token punctuation">,</span>
    <span class="token string">"canChangeNickname"</span><span class="token punctuation">:</span> <span class="token string">"false"</span><span class="token punctuation">,</span>
    <span class="token string">"logEnabled"</span><span class="token punctuation">:</span> <span class="token string">"true"</span><span class="token punctuation">,</span>
    <span class="token string">"loginRestrictedToNickname"</span><span class="token punctuation">:</span> <span class="token string">"true"</span><span class="token punctuation">,</span>
    <span class="token string">"membersOnly"</span><span class="token punctuation">:</span> <span class="token string">"false"</span><span class="token punctuation">,</span>
    <span class="token string">"moderated"</span><span class="token punctuation">:</span> <span class="token string">"false"</span><span class="token punctuation">,</span>
    <span class="token string">"broadcastPresenceRoles"</span><span class="token punctuation">:</span> <span class="token punctuation">[</span>
	    <span class="token string">"moderator"</span><span class="token punctuation">,</span>
	    <span class="token string">"participant"</span><span class="token punctuation">,</span>
	    <span class="token string">"visitor"</span>
    <span class="token punctuation">]</span><span class="token punctuation">,</span>
    <span class="token string">"owners"</span><span class="token punctuation">:</span> <span class="token punctuation">[</span>
	   <span class="token string">"owner@localhost"</span>
    <span class="token punctuation">]</span><span class="token punctuation">,</span>
    <span class="token string">"admins"</span><span class="token punctuation">:</span> <span class="token punctuation">[</span>
       <span class="token string">"admin@localhost"</span>
    <span class="token punctuation">]</span><span class="token punctuation">,</span>
    <span class="token string">"members"</span><span class="token punctuation">:</span> <span class="token punctuation">[</span>
		<span class="token string">"member@localhost"</span>
    <span class="token punctuation">]</span><span class="token punctuation">,</span>
    <span class="token string">"outcasts"</span><span class="token punctuation">:</span> <span class="token punctuation">[</span>
		<span class="token string">"outcast@localhost"</span>
	<span class="token punctuation">]</span>
<span class="token punctuation">}</span>
</code></pre>
<h2 id="delete-a-chat-room">Delete a chat room</h2>
<p>Endpoint to delete a chat room.</p>
<blockquote>
<p><strong>DELETE</strong> /chatrooms/{roomName}</p>
</blockquote>
<p><strong>Payload:</strong> none<br>
<strong>Return value:</strong> HTTP status 200 (OK)</p>
<h3 id="possible-parameters-20">Possible parameters</h3>

<table>
<thead>
<tr>
<th>Parameter</th>
<th>Parameter Type</th>
<th>Description</th>
<th>Default value</th>
</tr>
</thead>
<tbody>
<tr>
<td>roomname</td>
<td>@Path</td>
<td>Exact room name</td>
<td></td>
</tr>
<tr>
<td>servicename</td>
<td>@QueryParam</td>
<td>The name of the Group Chat Service</td>
<td>conference</td>
</tr>
</tbody>
</table><h3 id="examples-20">Examples</h3>
<blockquote>
<p><strong>Header:</strong> Authorization: Basic YWRtaW46MTIzNDU=</p>
</blockquote>
<blockquote>
<p><strong>DELETE</strong> <a href="http://example.org:9090/plugins/restapi/v1/chatrooms/testroom">http://example.org:9090/plugins/restapi/v1/chatrooms/testroom</a><br>
<strong>DELETE</strong> <a href="http://example.org:9090/plugins/restapi/v1/chatrooms/testroom?servicename=privateconf">http://example.org:9090/plugins/restapi/v1/chatrooms/testroom?servicename=privateconf</a></p>
</blockquote>
<h2 id="update-a-chat-room">Update a chat room</h2>
<p>Endpoint to update a chat room.</p>
<blockquote>
<p><strong>PUT</strong> /chatrooms/{roomName}</p>
</blockquote>
<p><strong>Payload:</strong> Chatroom<br>
<strong>Return value:</strong> HTTP status 200 (OK)</p>
<h3 id="possible-parameters-21">Possible parameters</h3>

<table>
<thead>
<tr>
<th>Parameter</th>
<th>Parameter Type</th>
<th>Description</th>
<th>Default value</th>
</tr>
</thead>
<tbody>
<tr>
<td>roomname</td>
<td>@Path</td>
<td>Exact room name</td>
<td></td>
</tr>
<tr>
<td>servicename</td>
<td>@QueryParam</td>
<td>The name of the Group Chat Service</td>
<td>conference</td>
</tr>
</tbody>
</table><h3 id="examples-21">Examples</h3>
<blockquote>
<p><strong>Header:</strong> Authorization: Basic YWRtaW46MTIzNDU=<br>
<strong>Header:</strong> Content-Type application/xml</p>
</blockquote>
<blockquote>
<p><strong>PUT</strong> <a href="http://example.org:9090/plugins/restapi/v1/chatrooms/global">http://example.org:9090/plugins/restapi/v1/chatrooms/global</a></p>
</blockquote>
<p><strong>Payload:</strong></p>
<pre class=" language-xml"><code class="prism  language-xml"><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>chatRoom</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>roomName</span><span class="token punctuation">&gt;</span></span>global<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>roomName</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>naturalName</span><span class="token punctuation">&gt;</span></span>global-2<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>naturalName</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>description</span><span class="token punctuation">&gt;</span></span>Global Chat Room edit<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>description</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>subject</span><span class="token punctuation">&gt;</span></span>New subject<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>subject</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>password</span><span class="token punctuation">&gt;</span></span>test<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>password</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>creationDate</span><span class="token punctuation">&gt;</span></span>2014-02-12T15:52:37.592+01:00<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>creationDate</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>modificationDate</span><span class="token punctuation">&gt;</span></span>2014-09-12T14:20:56.286+02:00<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>modificationDate</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>maxUsers</span><span class="token punctuation">&gt;</span></span>0<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>maxUsers</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>persistent</span><span class="token punctuation">&gt;</span></span>true<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>persistent</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>publicRoom</span><span class="token punctuation">&gt;</span></span>true<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>publicRoom</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>registrationEnabled</span><span class="token punctuation">&gt;</span></span>false<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>registrationEnabled</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>canAnyoneDiscoverJID</span><span class="token punctuation">&gt;</span></span>false<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>canAnyoneDiscoverJID</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>canOccupantsChangeSubject</span><span class="token punctuation">&gt;</span></span>false<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>canOccupantsChangeSubject</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>canOccupantsInvite</span><span class="token punctuation">&gt;</span></span>false<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>canOccupantsInvite</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>canChangeNickname</span><span class="token punctuation">&gt;</span></span>false<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>canChangeNickname</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>logEnabled</span><span class="token punctuation">&gt;</span></span>true<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>logEnabled</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>loginRestrictedToNickname</span><span class="token punctuation">&gt;</span></span>false<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>loginRestrictedToNickname</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>membersOnly</span><span class="token punctuation">&gt;</span></span>false<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>membersOnly</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>moderated</span><span class="token punctuation">&gt;</span></span>false<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>moderated</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>broadcastPresenceRoles</span><span class="token punctuation">/&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>owners</span><span class="token punctuation">&gt;</span></span>
        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>owner</span><span class="token punctuation">&gt;</span></span>owner@localhost<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>owner</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>owners</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>admins</span><span class="token punctuation">&gt;</span></span>
        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>admin</span><span class="token punctuation">&gt;</span></span>admin@localhost<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>admin</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>admins</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>members</span><span class="token punctuation">&gt;</span></span>
        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>member</span><span class="token punctuation">&gt;</span></span>member2@localhost<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>member</span><span class="token punctuation">&gt;</span></span>
        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>member</span><span class="token punctuation">&gt;</span></span>member1@localhost<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>member</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>members</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>outcasts</span><span class="token punctuation">&gt;</span></span>
        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>outcast</span><span class="token punctuation">&gt;</span></span>outcast1@localhost<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>outcast</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>outcasts</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>chatRoom</span><span class="token punctuation">&gt;</span></span>
</code></pre>
<h2 id="add-user-with-role-to-chat-room">Add user with role to chat room</h2>
<p>Endpoint to add a new user with role to a room.</p>
<blockquote>
<p><strong>POST</strong> /chatrooms/{roomName}/{roles}/{name}</p>
</blockquote>
<p><strong>Payload:</strong> none<br>
<strong>Return value:</strong> HTTP status 201 (Created)</p>
<h3 id="possible-parameters-22">Possible parameters</h3>

<table>
<thead>
<tr>
<th>Parameter</th>
<th>Parameter Type</th>
<th>Description</th>
<th>Default value</th>
</tr>
</thead>
<tbody>
<tr>
<td>roomname</td>
<td>@Path</td>
<td>Exact room name</td>
<td></td>
</tr>
<tr>
<td>name</td>
<td>@Path</td>
<td>The local username or the user JID</td>
<td></td>
</tr>
<tr>
<td>roles</td>
<td>@Path</td>
<td>Available roles: <br><strong>owners</strong>  <br> <strong>admins</strong> <br> <strong>members</strong> <br> <strong>outcasts</strong></td>
<td></td>
</tr>
<tr>
<td>servicename</td>
<td>@QueryParam</td>
<td>The name of the Group Chat Service</td>
<td>conference</td>
</tr>
</tbody>
</table><h3 id="examples-22">Examples</h3>
<blockquote>
<p><strong>Header:</strong> Authorization: Basic YWRtaW46MTIzNDU=<br>
<strong>Header:</strong> Content-Type application/xml</p>
</blockquote>
<blockquote>
<p><strong>POST</strong> <a href="http://example.org:9090/plugins/restapi/v1/chatrooms/global/owners/testUser">http://example.org:9090/plugins/restapi/v1/chatrooms/global/owners/testUser</a><br>
<strong>POST</strong> <a href="http://example.org:9090/plugins/restapi/v1/chatrooms/global/owners/<EMAIL>">http://example.org:9090/plugins/restapi/v1/chatrooms/global/owners/<EMAIL></a><br>
<strong>POST</strong> <a href="http://example.org:9090/plugins/restapi/v1/chatrooms/global/admins/testUser">http://example.org:9090/plugins/restapi/v1/chatrooms/global/admins/testUser</a><br>
<strong>POST</strong> <a href="http://example.org:9090/plugins/restapi/v1/chatrooms/global/members/testUser">http://example.org:9090/plugins/restapi/v1/chatrooms/global/members/testUser</a><br>
<strong>POST</strong> <a href="http://example.org:9090/plugins/restapi/v1/chatrooms/global/outcasts/testUser">http://example.org:9090/plugins/restapi/v1/chatrooms/global/outcasts/testUser</a><br>
<strong>POST</strong> <a href="http://example.org:9090/plugins/restapi/v1/chatrooms/global/owners/testUser?servicename=privateconf">http://example.org:9090/plugins/restapi/v1/chatrooms/global/owners/testUser?servicename=privateconf</a></p>
</blockquote>
<h2 id="add-group-with-role-to-chat-room">Add group with role to chat room</h2>
<p>Endpoint to add a new group with role to a room.</p>
<blockquote>
<p><strong>POST</strong> /chatrooms/{roomName}/{roles}/group/{name}</p>
</blockquote>
<p><strong>Payload:</strong> none<br>
<strong>Return value:</strong> HTTP status 201 (Created)</p>
<h3 id="possible-parameters-23">Possible parameters</h3>

<table>
<thead>
<tr>
<th>Parameter</th>
<th>Parameter Type</th>
<th>Description</th>
<th>Default value</th>
</tr>
</thead>
<tbody>
<tr>
<td>roomname</td>
<td>@Path</td>
<td>Exact room name</td>
<td></td>
</tr>
<tr>
<td>name</td>
<td>@Path</td>
<td>The group name</td>
<td></td>
</tr>
<tr>
<td>roles</td>
<td>@Path</td>
<td>Available roles: <br><strong>owners</strong>  <br> <strong>admins</strong> <br> <strong>members</strong> <br> <strong>outcasts</strong></td>
<td></td>
</tr>
<tr>
<td>servicename</td>
<td>@QueryParam</td>
<td>The name of the Group Chat Service</td>
<td>conference</td>
</tr>
</tbody>
</table><h3 id="examples-23">Examples</h3>
<blockquote>
<p><strong>Header:</strong> Authorization: Basic YWRtaW46MTIzNDU=<br>
<strong>Header:</strong> Content-Type application/xml</p>
</blockquote>
<blockquote>
<p><strong>POST</strong> <a href="http://example.org:9090/plugins/restapi/v1/chatrooms/global/owners/group/testGroup">http://example.org:9090/plugins/restapi/v1/chatrooms/global/owners/group/testGroup</a><br>
<strong>POST</strong> <a href="http://example.org:9090/plugins/restapi/v1/chatrooms/global/admins/group/testGroup">http://example.org:9090/plugins/restapi/v1/chatrooms/global/admins/group/testGroup</a><br>
<strong>POST</strong> <a href="http://example.org:9090/plugins/restapi/v1/chatrooms/global/members/group/testGroup">http://example.org:9090/plugins/restapi/v1/chatrooms/global/members/group/testGroup</a><br>
<strong>POST</strong> <a href="http://example.org:9090/plugins/restapi/v1/chatrooms/global/outcasts/group/testGroup">http://example.org:9090/plugins/restapi/v1/chatrooms/global/outcasts/group/testGroup</a><br>
<strong>POST</strong> <a href="http://example.org:9090/plugins/restapi/v1/chatrooms/global/owners/group/testUser?servicename=privateconf">http://example.org:9090/plugins/restapi/v1/chatrooms/global/owners/group/testUser?servicename=privateconf</a></p>
</blockquote>
<h2 id="delete-a-user-from-a-chat-room">Delete a user from a chat room</h2>
<p>Endpoint to remove a room user role.<br>
DELETE /chatrooms/{roomName}/{roles}/{name}</p>
<p><strong>Payload:</strong> none<br>
<strong>Return value:</strong> HTTP status 200 (OK)</p>
<h3 id="possible-parameters-24">Possible parameters</h3>

<table>
<thead>
<tr>
<th>Parameter</th>
<th>Parameter Type</th>
<th>Description</th>
<th>Default value</th>
</tr>
</thead>
<tbody>
<tr>
<td>roomname</td>
<td>@Path</td>
<td>Exact room name</td>
<td></td>
</tr>
<tr>
<td>name</td>
<td>@Path</td>
<td>The local username or the user JID</td>
<td></td>
</tr>
<tr>
<td>roles</td>
<td>@Path</td>
<td>Available roles: <br><strong>owners</strong>  <br> <strong>admins</strong> <br> <strong>members</strong> <br> <strong>outcasts</strong></td>
<td></td>
</tr>
<tr>
<td>servicename</td>
<td>@QueryParam</td>
<td>The name of the Group Chat Service</td>
<td>conference</td>
</tr>
</tbody>
</table><h3 id="examples-24">Examples</h3>
<blockquote>
<p><strong>Header:</strong> Authorization: Basic YWRtaW46MTIzNDU=<br>
<strong>Header:</strong> Content-Type application/xml</p>
</blockquote>
<blockquote>
<p><strong>DELETE</strong> <a href="http://example.org:9090/plugins/restapi/v1/chatrooms/global/owners/testUser">http://example.org:9090/plugins/restapi/v1/chatrooms/global/owners/testUser</a><br>
<strong>DELETE</strong> <a href="http://example.org:9090/plugins/restapi/v1/chatrooms/global/owners/<EMAIL>">http://example.org:9090/plugins/restapi/v1/chatrooms/global/owners/<EMAIL></a><br>
<strong>DELETE</strong> <a href="http://example.org:9090/plugins/restapi/v1/chatrooms/global/admins/testUser">http://example.org:9090/plugins/restapi/v1/chatrooms/global/admins/testUser</a><br>
<strong>DELETE</strong> <a href="http://example.org:9090/plugins/restapi/v1/chatrooms/global/members/testUser">http://example.org:9090/plugins/restapi/v1/chatrooms/global/members/testUser</a><br>
<strong>DELETE</strong> <a href="http://example.org:9090/plugins/restapi/v1/chatrooms/global/outcasts/testUser">http://example.org:9090/plugins/restapi/v1/chatrooms/global/outcasts/testUser</a><br>
<strong>DELETE</strong> <a href="http://example.org:9090/plugins/restapi/v1/chatrooms/global/owners/testUser?servicename=privateconf">http://example.org:9090/plugins/restapi/v1/chatrooms/global/owners/testUser?servicename=privateconf</a></p>
</blockquote>
<h1 id="system-related-rest-endpoints">System related REST Endpoints</h1>
<h2 id="retrieve-all-system-properties">Retrieve all system properties</h2>
<p>Endpoint to get all system properties</p>
<blockquote>
<p><strong>GET</strong> /system/properties</p>
</blockquote>
<p><strong>Payload:</strong> none<br>
<strong>Return value:</strong> System properties</p>
<h3 id="examples-25">Examples</h3>
<blockquote>
<p><strong>Header</strong>: Authorization: Basic YWRtaW46MTIzNDU=</p>
</blockquote>
<blockquote>
<p><strong>GET</strong> <a href="http://example.org:9090/plugins/restapi/v1/system/properties">http://example.org:9090/plugins/restapi/v1/system/properties</a></p>
</blockquote>
<h2 id="retrieve-system-property">Retrieve system property</h2>
<p>Endpoint to get information over specific system property</p>
<blockquote>
<p><strong>GET</strong> /system/properties/{propertyName}</p>
</blockquote>
<p><strong>Payload:</strong> none<br>
<strong>Return value:</strong> System property</p>
<h3 id="possible-parameters-25">Possible parameters</h3>

<table>
<thead>
<tr>
<th>Parameter</th>
<th>Parameter Type</th>
<th>Description</th>
<th>Default value</th>
</tr>
</thead>
<tbody>
<tr>
<td>propertyName</td>
<td>@Path</td>
<td>The name of system property</td>
<td></td>
</tr>
</tbody>
</table><h3 id="examples-26">Examples</h3>
<p><strong>Header:</strong> Authorization: Basic YWRtaW46MTIzNDU=</p>
<p><strong>GET</strong> <a href="http://example.org:9090/plugins/restapi/v1/system/properties/xmpp.domain">http://example.org:9090/plugins/restapi/v1/system/properties/xmpp.domain</a></p>
<h2 id="create-a-system-property">Create a system property</h2>
<p>Endpoint to create a system property</p>
<blockquote>
<p><strong>POST</strong> system/properties</p>
</blockquote>
<p><strong>Payload:</strong> System Property<br>
<strong>Return value:</strong> HTTP status 201 (Created)</p>
<h3 id="examples-27">Examples</h3>
<blockquote>
<p><strong>Header:</strong> Authorization: Basic YWRtaW46MTIzNDU=<br>
<strong>Header:</strong> Content-Type: application/xml</p>
</blockquote>
<blockquote>
<p><strong>POST</strong> <a href="http://example.org:9090/plugins/restapi/v1/system/properties">http://example.org:9090/plugins/restapi/v1/system/properties</a></p>
</blockquote>
<p><strong>Payload Example:</strong></p>
<pre class=" language-xml"><code class="prism  language-xml"><span class="token prolog">&lt;?xml version="1.0" encoding="UTF-8" standalone="yes"?&gt;</span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>property</span> <span class="token attr-name">key</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>propertyName<span class="token punctuation">"</span></span> <span class="token attr-name">value</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>propertyValue<span class="token punctuation">"</span></span><span class="token punctuation">/&gt;</span></span>
</code></pre>
<h2 id="delete-a-system-property">Delete a system property</h2>
<p>Endpoint to delete a system property</p>
<blockquote>
<p><strong>DELETE</strong> /system/properties/{propertyName}</p>
</blockquote>
<p><strong>Payload:</strong> none<br>
<strong>Return value:</strong> HTTP status 200 (OK)</p>
<h3 id="possible-parameters-26">Possible parameters</h3>

<table>
<thead>
<tr>
<th>Parameter</th>
<th>Parameter Type</th>
<th>Description</th>
<th>Default value</th>
</tr>
</thead>
<tbody>
<tr>
<td>propertyName</td>
<td>@Path</td>
<td>The name of system property</td>
<td></td>
</tr>
</tbody>
</table><h3 id="examples-28">Examples</h3>
<blockquote>
<p><strong>Header:</strong> Authorization: Basic YWRtaW46MTIzNDU=</p>
</blockquote>
<blockquote>
<p><strong>DELETE</strong> <a href="http://example.org:9090/plugins/restapi/v1/system/properties/propertyName">http://example.org:9090/plugins/restapi/v1/system/properties/propertyName</a></p>
</blockquote>
<h2 id="update-a-system-property">Update a system property</h2>
<p>Endpoint to update / overwrite a system property</p>
<blockquote>
<p><strong>PUT</strong> /system/properties/{propertyName}</p>
</blockquote>
<p><strong>Payload:</strong> System property<br>
<strong>Return value:</strong> HTTP status 200 (OK)</p>
<h3 id="possible-parameters-27">Possible parameters</h3>

<table>
<thead>
<tr>
<th>Parameter</th>
<th>Parameter Type</th>
<th>Description</th>
<th>Default value</th>
</tr>
</thead>
<tbody>
<tr>
<td>propertyName</td>
<td>@Path</td>
<td>The name of system property</td>
<td></td>
</tr>
</tbody>
</table><h3 id="examples-29">Examples</h3>
<blockquote>
<p><strong>Header:</strong> Authorization: Basic YWRtaW46MTIzNDU=<br>
<strong>Header:</strong> Content-Type application/xml</p>
</blockquote>
<blockquote>
<p><strong>PUT</strong> <a href="http://example.org:9090/plugins/restapi/v1/system/properties/propertyName">http://example.org:9090/plugins/restapi/v1/system/properties/propertyName</a></p>
</blockquote>
<p><strong>Payload:</strong></p>
<pre class=" language-xml"><code class="prism  language-xml"><span class="token prolog">&lt;?xml version="1.0" encoding="UTF-8" standalone="yes"?&gt;</span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>property</span> <span class="token attr-name">key</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>propertyName<span class="token punctuation">"</span></span> <span class="token attr-name">value</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>anotherValue<span class="token punctuation">"</span></span><span class="token punctuation">/&gt;</span></span>
</code></pre>
<h2 id="retrieve-concurrent-sessions">Retrieve concurrent sessions</h2>
<p>Endpoint to get count of concurrent sessions</p>
<blockquote>
<p><strong>GET</strong> /system/statistics/sessions</p>
</blockquote>
<p><strong>Payload:</strong> none<br>
<strong>Return value:</strong> Sessions count</p>
<h3 id="examples-30">Examples</h3>
<p><strong>Header:</strong> Authorization: Basic YWRtaW46MTIzNDU=</p>
<p><strong>GET</strong> <a href="http://example.org:9090/plugins/restapi/v1/system/statistics/sessions">http://example.org:9090/plugins/restapi/v1/system/statistics/sessions</a></p>
<h1 id="group-related-rest-endpoints">Group related REST Endpoints</h1>
<h2 id="retrieve-all-groups">Retrieve all groups</h2>
<p>Endpoint to get all groups</p>
<blockquote>
<p><strong>GET</strong> /groups</p>
</blockquote>
<p><strong>Payload:</strong> none<br>
<strong>Return value:</strong> Groups</p>
<h3 id="examples-31">Examples</h3>
<blockquote>
<p><strong>Header</strong>: Authorization: Basic YWRtaW46MTIzNDU=</p>
</blockquote>
<blockquote>
<p><strong>GET</strong> <a href="http://example.org:9090/plugins/restapi/v1/groups">http://example.org:9090/plugins/restapi/v1/groups</a></p>
</blockquote>
<h2 id="retrieve-a-group">Retrieve a group</h2>
<p>Endpoint to get information over specific group</p>
<blockquote>
<p><strong>GET</strong> /groups/{groupName}</p>
</blockquote>
<p><strong>Payload:</strong> none<br>
<strong>Return value:</strong> Group</p>
<h3 id="possible-parameters-28">Possible parameters</h3>

<table>
<thead>
<tr>
<th>Parameter</th>
<th>Parameter Type</th>
<th>Description</th>
<th>Default value</th>
</tr>
</thead>
<tbody>
<tr>
<td>groupName</td>
<td>@Path</td>
<td>The name of the group</td>
<td></td>
</tr>
</tbody>
</table><h3 id="examples-32">Examples</h3>
<p><strong>Header:</strong> Authorization: Basic YWRtaW46MTIzNDU=</p>
<p><strong>GET</strong> <a href="http://example.org:9090/plugins/restapi/v1/groups/moderators">http://example.org:9090/plugins/restapi/v1/groups/moderators</a></p>
<h2 id="create-a-group">Create a group</h2>
<p>Endpoint to create a new group</p>
<blockquote>
<p><strong>POST</strong> /groups</p>
</blockquote>
<p><strong>Payload:</strong> Group<br>
<strong>Return value:</strong> HTTP status 201 (Created)</p>
<h3 id="examples-33">Examples</h3>
<blockquote>
<p><strong>Header:</strong> Authorization: Basic YWRtaW46MTIzNDU=<br>
<strong>Header:</strong> Content-Type: application/xml</p>
</blockquote>
<blockquote>
<p><strong>POST</strong> <a href="http://example.org:9090/plugins/restapi/v1/groups">http://example.org:9090/plugins/restapi/v1/groups</a></p>
</blockquote>
<p><strong>Payload Example:</strong></p>
<pre class=" language-xml"><code class="prism  language-xml"><span class="token prolog">&lt;?xml version="1.0" encoding="UTF-8" standalone="yes"?&gt;</span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>group</span><span class="token punctuation">&gt;</span></span>
	<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>name</span><span class="token punctuation">&gt;</span></span>GroupName<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>name</span><span class="token punctuation">&gt;</span></span>
	<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>description</span><span class="token punctuation">&gt;</span></span>Some description<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>description</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>group</span><span class="token punctuation">&gt;</span></span>
</code></pre>
<h2 id="delete-a-group">Delete a group</h2>
<p>Endpoint to delete a group</p>
<blockquote>
<p><strong>DELETE</strong> /groups/{groupName}</p>
</blockquote>
<p><strong>Payload:</strong> none<br>
<strong>Return value:</strong> HTTP status 200 (OK)</p>
<h3 id="possible-parameters-29">Possible parameters</h3>

<table>
<thead>
<tr>
<th>Parameter</th>
<th>Parameter Type</th>
<th>Description</th>
<th>Default value</th>
</tr>
</thead>
<tbody>
<tr>
<td>groupName</td>
<td>@Path</td>
<td>The name of the group</td>
<td></td>
</tr>
</tbody>
</table><h3 id="examples-34">Examples</h3>
<blockquote>
<p><strong>Header:</strong> Authorization: Basic YWRtaW46MTIzNDU=</p>
</blockquote>
<blockquote>
<p><strong>DELETE</strong> <a href="http://example.org:9090/plugins/restapi/v1/groups/groupToDelete">http://example.org:9090/plugins/restapi/v1/groups/groupToDelete</a></p>
</blockquote>
<h2 id="update-a-group">Update a group</h2>
<p>Endpoint to update / overwrite a group</p>
<blockquote>
<p><strong>PUT</strong> /groups/{groupName}</p>
</blockquote>
<p><strong>Payload:</strong> Group<br>
<strong>Return value:</strong> HTTP status 200 (OK)</p>
<h3 id="possible-parameters-30">Possible parameters</h3>

<table>
<thead>
<tr>
<th>Parameter</th>
<th>Parameter Type</th>
<th>Description</th>
<th>Default value</th>
</tr>
</thead>
<tbody>
<tr>
<td>groupName</td>
<td>@Path</td>
<td>The name of the group</td>
<td></td>
</tr>
</tbody>
</table><h3 id="examples-35">Examples</h3>
<blockquote>
<p><strong>Header:</strong> Authorization: Basic YWRtaW46MTIzNDU=<br>
<strong>Header:</strong> Content-Type application/xml</p>
</blockquote>
<blockquote>
<p><strong>PUT</strong> <a href="http://example.org:9090/plugins/restapi/v1/groups/groupNameToUpdate">http://example.org:9090/plugins/restapi/v1/groups/groupNameToUpdate</a></p>
</blockquote>
<p><strong>Payload:</strong></p>
<pre class=" language-xml"><code class="prism  language-xml"><span class="token prolog">&lt;?xml version="1.0" encoding="UTF-8" standalone="yes"?&gt;</span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>group</span><span class="token punctuation">&gt;</span></span>
	<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>name</span><span class="token punctuation">&gt;</span></span>groupNameToUpdate<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>name</span><span class="token punctuation">&gt;</span></span>
	<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>description</span><span class="token punctuation">&gt;</span></span>New description<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>description</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>group</span><span class="token punctuation">&gt;</span></span>
</code></pre>
<h1 id="session-related-rest-endpoints">Session related REST Endpoints</h1>
<h2 id="retrieve-all-user-session">Retrieve all user session</h2>
<p>Endpoint to get all user sessions</p>
<blockquote>
<p><strong>GET</strong> /sessions</p>
</blockquote>
<p><strong>Payload:</strong> none<br>
<strong>Return value:</strong> Sessions</p>
<h3 id="examples-36">Examples</h3>
<blockquote>
<p><strong>Header</strong>: Authorization: Basic YWRtaW46MTIzNDU=</p>
</blockquote>
<blockquote>
<p><strong>GET</strong> <a href="http://example.org:9090/plugins/restapi/v1/sessions">http://example.org:9090/plugins/restapi/v1/sessions</a></p>
</blockquote>
<h2 id="retrieve-the-user-sessions">Retrieve the user sessions</h2>
<p>Endpoint to get sessions from a user</p>
<blockquote>
<p><strong>GET</strong> /sessions/{username}</p>
</blockquote>
<p><strong>Payload:</strong> none<br>
<strong>Return value:</strong> Sessions</p>
<h3 id="possible-parameters-31">Possible parameters</h3>

<table>
<thead>
<tr>
<th>Parameter</th>
<th>Parameter Type</th>
<th>Description</th>
<th>Default value</th>
</tr>
</thead>
<tbody>
<tr>
<td>username</td>
<td>@Path</td>
<td>The username of the user</td>
<td></td>
</tr>
</tbody>
</table><h3 id="examples-37">Examples</h3>
<p><strong>Header:</strong> Authorization: Basic YWRtaW46MTIzNDU=</p>
<p><strong>GET</strong> <a href="http://example.org:9090/plugins/restapi/v1/sessions/testuser">http://example.org:9090/plugins/restapi/v1/sessions/testuser</a></p>
<h2 id="close-all-user-sessions">Close all user sessions</h2>
<p>Endpoint to close/kick sessions from a user</p>
<blockquote>
<p><strong>DELETE</strong> /sessions/{username}</p>
</blockquote>
<p><strong>Payload:</strong> none<br>
<strong>Return value:</strong> HTTP status 200 (OK)</p>
<h3 id="possible-parameters-32">Possible parameters</h3>

<table>
<thead>
<tr>
<th>Parameter</th>
<th>Parameter Type</th>
<th>Description</th>
<th>Default value</th>
</tr>
</thead>
<tbody>
<tr>
<td>username</td>
<td>@Path</td>
<td>The username of the user</td>
<td></td>
</tr>
</tbody>
</table><h3 id="examples-38">Examples</h3>
<p><strong>Header:</strong> Authorization: Basic YWRtaW46MTIzNDU=</p>
<p><strong>DELETE</strong> <a href="http://example.org:9090/plugins/restapi/v1/sessions/testuser">http://example.org:9090/plugins/restapi/v1/sessions/testuser</a></p>
<h1 id="message-related-rest-endpoints">Message related REST Endpoints</h1>
<h2 id="send-a-broadcast-message">Send a broadcast message</h2>
<p>Endpoint to send a broadcast/server message to all online users</p>
<blockquote>
<p><strong>POST</strong> /messages/users</p>
</blockquote>
<p><strong>Payload:</strong> Message<br>
<strong>Return value:</strong> HTTP status 201 (Created)</p>
<h3 id="examples-39">Examples</h3>
<blockquote>
<p><strong>Header</strong>: Authorization: Basic YWRtaW46MTIzNDU=</p>
</blockquote>
<blockquote>
<p><strong>POST</strong> <a href="http://example.org:9090/plugins/restapi/v1/messages/users">http://example.org:9090/plugins/restapi/v1/messages/users</a></p>
</blockquote>
<p><strong>Payload:</strong></p>
<pre class=" language-xml"><code class="prism  language-xml"><span class="token prolog">&lt;?xml version="1.0" encoding="UTF-8" standalone="yes"?&gt;</span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>message</span><span class="token punctuation">&gt;</span></span>
	<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>body</span><span class="token punctuation">&gt;</span></span>Your message<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>body</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>message</span><span class="token punctuation">&gt;</span></span>
</code></pre>
<h1 id="security-audit-related-rest-endpoints">Security Audit related REST Endpoints</h1>
<h2 id="retrieve-the-security-audit-logs">Retrieve the Security audit logs</h2>
<p>Endpoint to get security audit logs</p>
<blockquote>
<p><strong>GET</strong> /logs/security</p>
</blockquote>
<p><strong>Payload:</strong> none<br>
<strong>Return value:</strong> Security Audit Logs</p>
<h3 id="possible-parameters-33">Possible parameters</h3>

<table>
<thead>
<tr>
<th>Parameter</th>
<th>Parameter Type</th>
<th>Description</th>
<th>Default value</th>
</tr>
</thead>
<tbody>
<tr>
<td>username</td>
<td>@QueryParam</td>
<td>Username of user to look up</td>
<td></td>
</tr>
<tr>
<td>startTime</td>
<td>@QueryParam</td>
<td>Oldest timestamp of range of logs to retrieve</td>
<td></td>
</tr>
<tr>
<td>endTime</td>
<td>@QueryParam</td>
<td>Most recent timestamp of range of logs to retrieve</td>
<td>0 (until now)</td>
</tr>
<tr>
<td>offset</td>
<td>@QueryParam</td>
<td>Number of logs to skip</td>
<td></td>
</tr>
<tr>
<td>limit</td>
<td>@QueryParam</td>
<td>Number of logs to retrieve</td>
<td></td>
</tr>
</tbody>
</table><h3 id="examples-40">Examples</h3>
<blockquote>
<p><strong>Header</strong>: Authorization: Basic YWRtaW46MTIzNDU=</p>
</blockquote>
<blockquote>
<p><strong>GET</strong> <a href="http://example.org:9090/plugins/restapi/v1/logs/security">http://example.org:9090/plugins/restapi/v1/logs/security</a></p>
</blockquote>
<h1 id="data-format">Data format</h1>
<p>Openfire REST API provides XML and JSON as data format. The default data format is XML.<br>
To get a JSON result, please add “<strong>Accept: application/json</strong>” to the request header.<br>
If you want to create a resource with JSON data format, please add “<strong>Content-Type: application/json</strong>”.<br>
Since version RESP API 1.3.2 you can also use GZIP to compress the payload of the request or and the response.<br>
“<strong>Content-Encoding: gzip</strong>” for the request. “<strong>Accept-Encoding: gzip</strong>” for the response.</p>
<h2 id="data-types">Data types</h2>
<h3 id="user">User</h3>

<table>
<thead>
<tr>
<th>Parameter</th>
<th>Optional</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>username</td>
<td>No</td>
<td>The username of the user</td>
</tr>
<tr>
<td>name</td>
<td>Yes</td>
<td>The name of the user</td>
</tr>
<tr>
<td>email</td>
<td>Yes</td>
<td>The email of the user</td>
</tr>
<tr>
<td>password</td>
<td>No</td>
<td>The password of the user</td>
</tr>
<tr>
<td>properties</td>
<td>Yes</td>
<td>List of properties. Property is a key / value object. The key must to be per user unique</td>
</tr>
</tbody>
</table><h3 id="rosteritem">RosterItem</h3>

<table>
<thead>
<tr>
<th>Parameter</th>
<th>Optional</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>jid</td>
<td>No</td>
<td>The JID of the roster item</td>
</tr>
<tr>
<td>nickname</td>
<td>Yes</td>
<td>The nickname for the user when used in this roster</td>
</tr>
<tr>
<td>subscriptionType</td>
<td>Yes</td>
<td>The subscription type <br> Possible numeric values are: -1 (remove), 0 (none), 1 (to), 2 (from), 3 (both)</td>
</tr>
<tr>
<td>groups</td>
<td>No</td>
<td>A list of groups to organize roster entries under (e.g. friends, co-workers, etc.)</td>
</tr>
</tbody>
</table><h3 id="chatroom">Chatroom</h3>

<table>
<thead>
<tr>
<th>Parameter</th>
<th>Optional</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>roomName</td>
<td>No</td>
<td>The name/id of the room. Can only contains lowercase and alphanumeric characters.</td>
</tr>
<tr>
<td>naturalName</td>
<td>No</td>
<td>Also the name of the room, but can contains non alphanumeric characters. It’s mainly used for users while discovering rooms hosted by the Multi-User Chat service.</td>
</tr>
<tr>
<td>description</td>
<td>No</td>
<td>Description text of the room.</td>
</tr>
<tr>
<td>subject</td>
<td>Yes</td>
<td>Subject of the room.</td>
</tr>
<tr>
<td>password</td>
<td>Yes</td>
<td>The password that the user must provide to enter the room</td>
</tr>
<tr>
<td>creationDate</td>
<td>Yes</td>
<td>The date when the room was created. Will be automatically set by creation. Example: 2014-07-10T09:49:12.411+02:00</td>
</tr>
<tr>
<td>modificationDate</td>
<td>Yes</td>
<td>The last date when the room’s configuration was modified. If the room’s configuration  was never modified then the initial value will be the same as the creation date. Will be automatically set by update. Example: 2014-07-10T09:49:12.411+02:00</td>
</tr>
<tr>
<td>maxUsers</td>
<td>Yes</td>
<td>the maximum number of occupants that can be simultaneously in the room. 0 means unlimited number of occupants.</td>
</tr>
<tr>
<td>persistent</td>
<td>Yes</td>
<td>Can be “true” or “false”. Persistent rooms are saved to the database to make their configurations persistent together with the affiliation of the users. Otherwise the room will be destroyed if the last occupant leave the room.</td>
</tr>
<tr>
<td>publicRoom</td>
<td>Yes</td>
<td>Can be “true” or “false”. True if the room is searchable and visible through service discovery.</td>
</tr>
<tr>
<td>registrationEnabled</td>
<td>Yes</td>
<td>Can be “true” or “false”. True if users are allowed to register with the room. By default, room registration is enabled.</td>
</tr>
<tr>
<td>canAnyoneDiscoverJID</td>
<td>Yes</td>
<td>Can be “true” or “false”. True if every presence packet will include the JID of every occupant.</td>
</tr>
<tr>
<td>canOccupantsChangeSubject</td>
<td>Yes</td>
<td>Can be “true” or “false”. True if participants are allowed to change the room’s subject.</td>
</tr>
<tr>
<td>canOccupantsInvite</td>
<td>Yes</td>
<td>Can be “true” or “false”. True if occupants can invite other users to the room. If the room does not require an invitation to enter (i.e. is not members-only) then any occupant can send invitations. On the other hand, if the room is members-only and occupants cannot send invitation then only the room owners and admins are allowed to send invitations.</td>
</tr>
<tr>
<td>canChangeNickname</td>
<td>Yes</td>
<td>Can be “true” or “false”. True if room occupants are allowed to change their nicknames in the room. By default, occupants are allowed to change their nicknames.</td>
</tr>
<tr>
<td>logEnabled</td>
<td>Yes</td>
<td>Can be “true” or “false”. True if the room’s conversation is being logged. If logging is activated the room conversation will be saved to the database every couple of minutes. The saving frequency is the same for all the rooms and can be configured by changing the property “xmpp.muc.tasks.log.timeout”.</td>
</tr>
<tr>
<td>loginRestrictedToNickname</td>
<td>Yes</td>
<td>Can be “true” or “false”. True if registered users can only join the room using their registered nickname. By default, registered users can join the room using any nickname.</td>
</tr>
<tr>
<td>membersOnly</td>
<td>Yes</td>
<td>Can be “true” or “false”. True if the room requires an invitation to enter. That is if the room is members-only.</td>
</tr>
<tr>
<td>moderated</td>
<td>Yes</td>
<td>Can be “true” or “false”. True if the room in which only those with “voice” may send messages to all occupants.</td>
</tr>
<tr>
<td>broadcastPresenceRoles</td>
<td>Yes</td>
<td>The list of roles of which presence will be broadcasted to the rest of the occupants.</td>
</tr>
<tr>
<td>owners</td>
<td>Yes</td>
<td>A collection with the current list of owners. The collection contains the bareJID of the users with owner affiliation.</td>
</tr>
<tr>
<td>admins</td>
<td>Yes</td>
<td>A collection with the current list of admins. The collection contains the bareJID of the users with admin affiliation.</td>
</tr>
<tr>
<td>members</td>
<td>Yes</td>
<td>A collection with the current list of room members. The collection contains the bareJID of the users with member affiliation. If the room is not members-only then the list  will contain the users that registered with the room and therefore they may have reserved a nickname.</td>
</tr>
<tr>
<td>outcasts</td>
<td>Yes</td>
<td>A collection with the current list of outcast users. An outcast user is not allowed to join the room again. The collection contains the bareJID of the users with outcast affiliation.</td>
</tr>
<tr>
<td>ownerGroups</td>
<td>Yes</td>
<td>A collection with the current list of groups with owner affiliation. The collection contains the name only.</td>
</tr>
<tr>
<td>adminGroups</td>
<td>Yes</td>
<td>A collection with the current list of groups with admin affiliation. The collection contains the name only.</td>
</tr>
<tr>
<td>memberGroups</td>
<td>Yes</td>
<td>A collection with the current list of groups with member affiliation. The collection contains the name only.</td>
</tr>
<tr>
<td>outcastGroups</td>
<td>Yes</td>
<td>A collection with the current list of groups with outcast affiliation. The collection contains the name only.</td>
</tr>
</tbody>
</table><h3 id="group">Group</h3>

<table>
<thead>
<tr>
<th>Parameter</th>
<th>Optional</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>name</td>
<td>No</td>
<td>The name of the group</td>
</tr>
<tr>
<td>description</td>
<td>No</td>
<td>The description of the group</td>
</tr>
<tr>
<td>admins</td>
<td>Yes</td>
<td>A collection with current admins of the group</td>
</tr>
<tr>
<td>members</td>
<td>Yes</td>
<td>A collection with current members of the group</td>
</tr>
</tbody>
</table><h3 id="system-property">System Property</h3>

<table>
<thead>
<tr>
<th>Parameter</th>
<th>Optional</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>key</td>
<td>No</td>
<td>The name of the system property</td>
</tr>
<tr>
<td>value</td>
<td>No</td>
<td>The value of the system property</td>
</tr>
</tbody>
</table><h3 id="session">Session</h3>

<table>
<thead>
<tr>
<th>Parameter</th>
<th>Optional</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>sessionId</td>
<td>No</td>
<td>Full JID of a user e.g. (<a href="mailto:<EMAIL>"><EMAIL></a>/SomeRessource)</td>
</tr>
<tr>
<td>username</td>
<td>No</td>
<td>The username associated with this session. Can be also “Anonymous”.</td>
</tr>
<tr>
<td>ressource</td>
<td>Yes</td>
<td>Ressource name</td>
</tr>
<tr>
<td>node</td>
<td>No</td>
<td>Can be “Local” or “Remote”</td>
</tr>
<tr>
<td>sessionStatus</td>
<td>No</td>
<td>The current status of this session. Can be “Closed”, “Connected”, “Authenticated” or “Unknown”.</td>
</tr>
<tr>
<td>presenceStatus</td>
<td>No</td>
<td>The status of this presence packet, a natural-language description of availability status.</td>
</tr>
<tr>
<td>priority</td>
<td>No</td>
<td>The priority of the session. The valid priority range is -128 through 128.</td>
</tr>
<tr>
<td>hostAddress</td>
<td>No</td>
<td>Tthe IP address string in textual presentation.</td>
</tr>
<tr>
<td>hostName</td>
<td>No</td>
<td>The host name for this IP address.</td>
</tr>
<tr>
<td>creationDate</td>
<td>No</td>
<td>The date the session was created.</td>
</tr>
<tr>
<td>lastActionDate</td>
<td>No</td>
<td>The time the session last had activity.</td>
</tr>
<tr>
<td>secure</td>
<td>No</td>
<td>Is “true” if this connection is secure.</td>
</tr>
</tbody>
</table><h3 id="sessions-count">Sessions count</h3>

<table>
<thead>
<tr>
<th>Parameter</th>
<th>Optional</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>clusterSessions</td>
<td>No</td>
<td>Number of client sessions that are authenticated with the server. This includes anonymous and non-anoymous users from the whole cluster.</td>
</tr>
<tr>
<td>localSessions</td>
<td>No</td>
<td>Number of client sessions that are authenticated with the server. This includes anonymous and non-anoymous users.</td>
</tr>
</tbody>
</table><h3 id="security-audit-logs">Security Audit Logs</h3>

<table>
<thead>
<tr>
<th>Parameter</th>
<th>Optional</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>logId</td>
<td>No</td>
<td>unique ID of this log</td>
</tr>
<tr>
<td>username</td>
<td>No</td>
<td>the username of the user who performed this event</td>
</tr>
<tr>
<td>timestamp</td>
<td>No</td>
<td>the time stamp of when this event occurred</td>
</tr>
<tr>
<td>summary</td>
<td>No</td>
<td>the summary, or short description of what transpired in the event</td>
</tr>
<tr>
<td>node</td>
<td>No</td>
<td>the node that triggered the event, usually a hostname or IP address</td>
</tr>
<tr>
<td>details</td>
<td>No</td>
<td>detailed information about what occurred in the event</td>
</tr>
</tbody>
</table><h1 id="deprecated-user-service-plugin-readme">(Deprecated) User Service Plugin Readme</h1>
<h2 id="overview">Overview</h2>
<p>The User Service Plugin provides the ability to add,edit,delete users and manage their rosters by sending an http request to the server. It is intended to be used by applications automating the user administration process. This plugin’s functionality is useful for applications that need to administer users outside of the Openfire admin console. An example of such an application might be a live sports reporting application that uses XMPP as its transport, and creates/deletes users according to the receipt, or non receipt, of a subscription fee.</p>
<h2 id="installation-1">Installation</h2>
<p>Copy userservice.jar into the plugins directory of your Openfire server. The plugin will then be automatically deployed. To upgrade to a new version, copy the new userservice.jar file over the existing file.</p>
<h2 id="configuration">Configuration</h2>
<p>Access to the service is restricted with a “secret” that can be viewed and set from the User Service page in the Openfire admin console. This page is located on the admin console under “Server” and then “Server Settings”. This should really only be considered weak security. The plugin was initially written with the assumption that http access to the Openfire service was only available to trusted machines. In the case of the plugin’s author, a web application running on the same server as Openfire makes the request.</p>
<h2 id="using-the-plugin">Using the Plugin</h2>
<p>To administer users, submit HTTP requests to the userservice service. The service address is [hostname]plugins/restapi/userservice. For example, if your server name is “<a href="http://example.com">example.com</a>”, the URL is <a href="http://example.com/plugins/restapi/userservice">http://example.com/plugins/restapi/userservice</a></p>
<p>The following parameters can be passed into the request:</p>

<table>
<thead>
<tr>
<th>Name</th>
<th></th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>type</td>
<td>Required</td>
<td>The admin service required. Possible values are ‘add’, ‘delete’, ‘update’, ‘enable’, ‘disable’, ‘add_roster’, ‘update_roster’, ‘delete_roster’, ‘grouplist’, ‘usergrouplist’.</td>
</tr>
<tr>
<td>secret</td>
<td>Required</td>
<td>The secret key that allows access to the User Service.</td>
</tr>
<tr>
<td>username</td>
<td>Required</td>
<td>The username of the user to ‘add’, ‘delete’, ‘update’, ‘enable’, ‘disable’, ‘add_roster’, ‘update_roster’, ‘delete_roster’. ie the part before the @ symbol.</td>
</tr>
<tr>
<td>password</td>
<td>Required for ‘add’ operation</td>
<td>The password of the new user or the user being updated.</td>
</tr>
<tr>
<td>name</td>
<td>Optional</td>
<td>The display name of the new user or the user being updated. For ‘add_roster’, ‘update_roster’ operations specifies the nickname of the roster item.</td>
</tr>
<tr>
<td>email</td>
<td>Optional</td>
<td>The email address of the new user or the user being updated.</td>
</tr>
<tr>
<td>groups</td>
<td>Optional</td>
<td>List of groups where the user is a member. Values are comma delimited. When used with types “add” or “update”, it adds the user to shared groups and auto-creates new groups. When used with ‘add_roster’ and ‘update_roster’, it adds the user to roster groups provided the group name does not clash with an existing shared group.</td>
</tr>
<tr>
<td>item_jid</td>
<td>Required for ‘add_roster’, ‘update_roster’, ‘delete_roster’ operations.</td>
<td>The JID of the roster item</td>
</tr>
<tr>
<td>subscription</td>
<td>Optional</td>
<td>Type of subscription for ‘add_roster’, ‘update_roster’ operations. Possible numeric values are: -1(remove), 0(none), 1(to), 2(from), 3(both).</td>
</tr>
</tbody>
</table><h2 id="sample-html">Sample HTML</h2>
<p>The following example adds a user</p>
<p><a href="http://example.com:9090/plugins/restapi/userservice?type=add&amp;secret=bigsecret&amp;username=kafka&amp;password=drowssap&amp;name=franz&amp;email=<EMAIL>">http://example.com:9090/plugins/restapi/userservice?type=add&amp;secret=bigsecret&amp;username=kafka&amp;password=drowssap&amp;name=franz&amp;email=<EMAIL></a></p>
<p>The following example adds a user, adds two shared groups (if not existing) and adds the user to both groups.</p>
<p><a href="http://example.com:9090/plugins/restapi/userservice?type=add&amp;secret=bigsecret&amp;username=kafka&amp;password=drowssap&amp;name=franz&amp;email=<EMAIL>&amp;groups=support,finance">http://example.com:9090/plugins/restapi/userservice?type=add&amp;secret=bigsecret&amp;username=kafka&amp;password=drowssap&amp;name=franz&amp;email=<EMAIL>&amp;groups=support,finance</a></p>
<p>The following example deletes a user and all roster items of the user.</p>
<p><a href="http://example.com:9090/plugins/restapi/userservice?type=delete&amp;secret=bigsecret&amp;username=kafka">http://example.com:9090/plugins/restapi/userservice?type=delete&amp;secret=bigsecret&amp;username=kafka</a></p>
<p>The following example disables a user (lockout)</p>
<p><a href="http://example.com:9090/plugins/restapi/userservice?type=disable&amp;secret=bigsecret&amp;username=kafka">http://example.com:9090/plugins/restapi/userservice?type=disable&amp;secret=bigsecret&amp;username=kafka</a></p>
<p>The following example enables a user (removes lockout)</p>
<p><a href="http://example.com:9090/plugins/restapi/userservice?type=enable&amp;secret=bigsecret&amp;username=kafka">http://example.com:9090/plugins/restapi/userservice?type=enable&amp;secret=bigsecret&amp;username=kafka</a></p>
<p>The following example updates a user</p>
<p><a href="http://example.com:9090/plugins/restapi/userservice?type=update&amp;secret=bigsecret&amp;username=kafka&amp;password=drowssap&amp;name=franz&amp;email=<EMAIL>">http://example.com:9090/plugins/restapi/userservice?type=update&amp;secret=bigsecret&amp;username=kafka&amp;password=drowssap&amp;name=franz&amp;email=<EMAIL></a></p>
<p>The following example adds new roster item with subscription ‘both’ for user ‘kafka’</p>
<p><a href="http://example.com:9090/plugins/restapi/userservice?type=add_roster&amp;secret=bigsecret&amp;username=kafka&amp;item_jid=<EMAIL>&amp;name=franz&amp;subscription=3">http://example.com:9090/plugins/restapi/userservice?type=add_roster&amp;secret=bigsecret&amp;username=kafka&amp;item_jid=<EMAIL>&amp;name=franz&amp;subscription=3</a></p>
<p>The following example adds new roster item with subscription ‘both’ for user ‘kafka’ and adds kafka to roster groups ‘family’ and ‘friends’</p>
<p><a href="http://example.com:9090/plugins/restapi/userservice?type=add_roster&amp;secret=bigsecret&amp;username=kafka&amp;item_jid=<EMAIL>&amp;name=franz&amp;subscription=3&amp;groups=family,friends">http://example.com:9090/plugins/restapi/userservice?type=add_roster&amp;secret=bigsecret&amp;username=kafka&amp;item_jid=<EMAIL>&amp;name=franz&amp;subscription=3&amp;groups=family,friends</a></p>
<p>The following example updates existing roster item to subscription ‘none’ for user ‘kafka’</p>
<p><a href="http://example.com:9090/plugins/restapi/userservice?type=update_roster&amp;secret=bigsecret&amp;username=kafka&amp;item_jid=<EMAIL>&amp;name=franz&amp;subscription=0">http://example.com:9090/plugins/restapi/userservice?type=update_roster&amp;secret=bigsecret&amp;username=kafka&amp;item_jid=<EMAIL>&amp;name=franz&amp;subscription=0</a></p>
<p>The following example deletes a specific roster item ‘<EMAIL>’ for user ‘kafka’</p>
<p><a href="http://example.com:9090/plugins/restapi/userservice?type=delete_roster&amp;secret=bigsecret&amp;username=kafka&amp;item_jid=<EMAIL>">http://example.com:9090/plugins/restapi/userservice?type=delete_roster&amp;secret=bigsecret&amp;username=kafka&amp;item_jid=<EMAIL></a></p>
<p>The following example gets all groups</p>
<p><a href="http://example.com:9090/plugins/restapi/userservice?type=grouplist&amp;secret=bigsecret">http://example.com:9090/plugins/restapi/userservice?type=grouplist&amp;secret=bigsecret</a><br>
Which replies an XML group list formatted like this:</p>
<pre class=" language-xml"><code class="prism  language-xml"><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>result</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>groupname</span><span class="token punctuation">&gt;</span></span>group1<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>groupname</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>groupname</span><span class="token punctuation">&gt;</span></span>group2<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>groupname</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>result</span><span class="token punctuation">&gt;</span></span>
</code></pre>
<p>The following example gets all groups for a specific user</p>
<p><a href="http://example.com:9090/plugins/restapi/userservice?type=usergrouplist&amp;secret=bigsecret&amp;username=kafka">http://example.com:9090/plugins/restapi/userservice?type=usergrouplist&amp;secret=bigsecret&amp;username=kafka</a><br>
Which replies an XML group list formatted like this:</p>
<pre class=" language-xml"><code class="prism  language-xml"><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>result</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>groupname</span><span class="token punctuation">&gt;</span></span>usergroup1<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>groupname</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>groupname</span><span class="token punctuation">&gt;</span></span>usergroup2<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>groupname</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>result</span><span class="token punctuation">&gt;</span></span>
</code></pre>
<p>* When sending double characters (Chinese/Japanese/Korean etc) you should URLEncode the string as utf8.<br>
In Java this is done like this<br>
URLEncoder.encode(username, “UTF-8”));<br>
If the strings are encoded incorrectly, double byte characters will look garbeled in the Admin Console.</p>
<h2 id="server-reply">Server Reply</h2>
<p>The server will reply to all User Service requests with an XML result page. If the request was processed successfully the return will be a “result” element with a text body of “OK”, or an XML grouplist formatted like in the example for “grouplist” and “usergrouplist” above. If the request was unsuccessful, the return will be an “error” element with a text body of one of the following error strings.</p>

<table>
<thead>
<tr>
<th>Error String</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>IllegalArgumentException</td>
<td>One of the parameters passed in to the User Service was bad.</td>
</tr>
<tr>
<td>UserNotFoundException</td>
<td>No user of the name specified, for a delete or update operation, exists on this server. For ‘update_roster’ operation, roster item to be updated was not found.</td>
</tr>
<tr>
<td>UserAlreadyExistsException</td>
<td>A user with the same name as the user about to be added, already exists. For ‘add_roster’ operation, roster item with the same JID already exists.</td>
</tr>
<tr>
<td>RequestNotAuthorised</td>
<td>The supplied secret does not match the secret specified in the Admin Console or the requester is not a valid IP address.</td>
</tr>
<tr>
<td>UserServiceDisabled</td>
<td>The User Service is currently set to disabled in the Admin Console.</td>
</tr>
<tr>
<td>SharedGroupException</td>
<td>Roster item can not be added/deleted to/from a shared group for operations with roster.</td>
</tr>
</tbody>
</table>
    </div>
  </div>
</body>

</html>
