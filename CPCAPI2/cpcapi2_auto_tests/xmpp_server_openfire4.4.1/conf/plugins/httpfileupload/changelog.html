<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">

<html>
<head>
    <title>HTTP File Upload Plugin Changelog</title>
    <style type="text/css">
        BODY {
            font-size : 100%;
        }
        BOD<PERSON>, TD, TH {
            font-family : tahoma, verdana, arial, helvetica, sans-serif;
            font-size : 0.8em;
        }
        H2 {
             font-size : 10pt;
             font-weight : bold;
             padding-left : 1em;
        }
        A:hover {
            text-decoration : none;
        }
        H1 {
            font-family : tahoma, arial, helvetica, sans-serif;
            font-size : 1.4em;
            font-weight: bold;
            border-bottom : 1px #ccc solid;
            padding-bottom : 2px;
        }
        TT {
            font-family : courier new;
            font-weight : bold;
            color : #060;
        }
        PRE {
            font-family : courier new;
            font-size : 100%;
        }
    </style>
</head>
<body>

<h1>HTTP File Upload Plugin Changelog</h1>

<p><b>1.1.0</b> -- February 19, 2018</p>
<ul>
    <li>Updated to HttpFileUploadComponent v1.1.3, which resolves:</li>
    <ul>
        <li><a href="https://github.com/guusdk/httpfileuploadcomponent/issues/5">Issue #5:</a> Maximum allowable file size should be configurable.</li>
        <li><a href="https://github.com/guusdk/httpfileuploadcomponent/issues/6">Issue #6:</a> Fix for NPE when client uses XEP v0.3 or higher.</li>
        <li><a href="https://github.com/guusdk/httpfileuploadcomponent/issues/7">Issue #7:</a> Responses to slot requests should use syntax of the request.</li>
        <li><a href="https://github.com/guusdk/httpfileuploadcomponent/issues/8">Issue #8:</a> Add maximum file size to service discovery responses.</li>
    </ul>
</ul>


<p><b>1.0.0</b> -- December 14, 2017</p>
<ul>
     <li>Initial release.</li>
</ul>

</body>
</html>
