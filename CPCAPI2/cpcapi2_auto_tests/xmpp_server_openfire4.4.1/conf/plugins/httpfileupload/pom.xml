<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>plugins</artifactId>
        <groupId>org.igniterealtime.openfire</groupId>
        <version>4.2.0</version>
    </parent>
    <groupId>org.igniterealtime.openfire.plugins</groupId>
    <artifactId>httpfileupload</artifactId>
    <name>HTTP File Upload Plugin</name>
    <description>Allows clients to share files, as described in the XEP-0363 'HTTP File Upload' specification.</description>
    <version>1.1.0</version>

    <build>
        <sourceDirectory>src/java</sourceDirectory>
        <plugins>
            <plugin>
                <artifactId>maven-assembly-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <dependencies>
        <dependency>
            <groupId>nl.goodbytes.xmpp.xep</groupId>
            <artifactId>httpfileuploadcomponent</artifactId>
            <version>1.1.3</version>
        </dependency>
    </dependencies>
</project>
