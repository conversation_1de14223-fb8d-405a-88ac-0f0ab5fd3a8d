<?xml version="1.0" encoding="UTF-8"?>

<!--
    This file stores bootstrap properties needed by Openfire.
    Property names must be in the format: "prop.name.is.blah=value"
    That will be stored as:
        <prop>
            <name>
                <is>
                    <blah>value</blah>
                </is>
            </name>
        </prop>

    Most properties are stored in the Openfire database. A
    property viewer and editor is included in the admin console.
-->
<!-- root element, all properties must be under this element -->
<jive> 
  <adminConsole> 
    <!-- Disable either port by setting the value to -1 -->  
    <port>9090</port>  
    <securePort>9091</securePort> 
  </adminConsole>  
  <locale>en</locale>  
  <!-- Network settings. By default, Openfire will bind to all network interfaces.
      Alternatively, you can specify a specific network interfaces that the server
      will listen on. For example, 127.0.0.1. This setting is generally only useful
       on multi-homed servers. -->  
  <!--
    <network>
        <interface></interface>
    </network>
    -->  
  <!--
        One time token to gain temporary access to the admin console.
    -->  
  <!--
    <oneTimeAccessToken>secretToken</oneTimeAccessToken>
    -->  
  <connectionProvider> 
    <className>org.jivesoftware.database.EmbeddedConnectionProvider</className> 
  </connectionProvider>  
  <setup>true</setup>  
  <fqdn>yvr-sdkxmpp01.cp.local</fqdn> 
</jive>
