
<available>
  <plugin name="Bookmarks" latest="1.0.3" changelog="http://www.igniterealtime.org/projects/openfire/plugins/1.0.3/bookmarks/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/1.0.3/bookmarks.jar" author="Ignite Realtime" description="Allows clients to store URL and group chat bookmarks (XEP-0048)" icon="http://www.igniterealtime.org/projects/openfire/plugins/1.0.3/bookmarks/logo_small.gif" minServerVersion="4.0.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/1.0.3/bookmarks/readme.html" fileSize="64208"/> 
  <plugin name="External Service Discovery" latest="1.0.1" changelog="http://www.igniterealtime.org/projects/openfire/plugins/1.0.1/externalservicediscovery/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/1.0.1/externalservicediscovery.jar" author="<PERSON><PERSON><PERSON>" description="Allows XMPP entities to discover services external to the XMPP network, such as STUN and TURN servers." icon="http://www.igniterealtime.org/projects/openfire/plugins/1.0.1/externalservicediscovery/logo_small.png" minServerVersion="4.2.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/1.0.1/externalservicediscovery/readme.html" fileSize="95337"/> 
  <plugin name="MUC Service Discovery Extensions" latest="1.0.0" changelog="http://www.igniterealtime.org/projects/openfire/plugins/1.0.0/mucextinfo/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/1.0.0/mucextinfo.jar" author="Guus der Kinderen" description="Allows an admin to configure Extended Service Discovery information to Multi User Chat entities." minServerVersion="4.5.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/1.0.0/mucextinfo/readme.html" fileSize="52992"/> 
  <plugin name="SIP Phone Plugin" latest="1.2.0" changelog="http://www.igniterealtime.org/projects/openfire/plugins/1.2.0/sip/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/1.2.0/sip.jar" author="Ignite Realtime" description="Provides support for SIP account management" icon="http://www.igniterealtime.org/projects/openfire/plugins/1.2.0/sip/logo_small.gif" minServerVersion="4.0.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/1.2.0/sip/readme.html" fileSize="588571"/> 
  <plugin name="Fastpath Service" latest="4.4.5" changelog="http://www.igniterealtime.org/projects/openfire/plugins/4.4.5/fastpath/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/4.4.5/fastpath.jar" author="Jive Software" description="Support for managed queued chat requests, such as a support team might use." icon="http://www.igniterealtime.org/projects/openfire/plugins/4.4.5/fastpath/logo_small.gif" minServerVersion="4.1.1" readme="http://www.igniterealtime.org/projects/openfire/plugins/4.4.5/fastpath/readme.html" fileSize="1698700"/> 
  <plugin name="TikiToken" latest="0.2.0" changelog="http://www.igniterealtime.org/projects/openfire/plugins/0.2.0/tikitoken/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/0.2.0/tikitoken.jar" author="Tiki Wiki CMS Groupware" description="Allows users to authenticate with a Tiki token." icon="http://www.igniterealtime.org/projects/openfire/plugins/0.2.0/tikitoken/logo_small.png" minServerVersion="4.1.3" readme="http://www.igniterealtime.org/projects/openfire/plugins/0.2.0/tikitoken/readme.html" licenseType="gpl" fileSize="358495"/> 
  <plugin name="HTTP File Upload" latest="1.1.3" changelog="http://www.igniterealtime.org/projects/openfire/plugins/1.1.3/httpfileupload/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/1.1.3/httpfileupload.jar" author="Guus der Kinderen" description="Allows clients to share files, as described in the XEP-0363 'HTTP File Upload' specification." icon="http://www.igniterealtime.org/projects/openfire/plugins/1.1.3/httpfileupload/logo_small.png" minServerVersion="4.1.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/1.1.3/httpfileupload/readme.html" fileSize="5584886"/> 
  <plugin name="Hazelcast Plugin" latest="2.4.2" changelog="http://www.igniterealtime.org/projects/openfire/plugins/2.4.2/hazelcast/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/2.4.2/hazelcast.jar" author="Ignite Realtime" description="Adds clustering support" icon="http://www.igniterealtime.org/projects/openfire/plugins/2.4.2/hazelcast/logo_small.png" minServerVersion="4.0.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/2.4.2/hazelcast/readme.html" fileSize="10370010"/> 
  <plugin name="Draw-IO" latest="0.0.1" changelog="http://www.igniterealtime.org/projects/openfire/plugins/0.0.1/draw/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/0.0.1/draw.jar" author="Ignite Realtime" description="Web Diagramming Tool that uses SVG and HTML for rendering" icon="http://www.igniterealtime.org/projects/openfire/plugins/0.0.1/draw/logo_small.gif" minServerVersion="4.0.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/0.0.1/draw/readme.html" licenseType="Apache 2.0" fileSize="40136123"/> 
  <plugin name="IPFS" latest="0.0.1" changelog="http://www.igniterealtime.org/projects/openfire/plugins/0.0.1/ipfs/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/0.0.1/ipfs.jar" author="igniterealtime.org" description="Enables Openfire to become an IPFS node." icon="http://www.igniterealtime.org/projects/openfire/plugins/0.0.1/ipfs/logo_small.gif" minServerVersion="4.0.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/0.0.1/ipfs/readme.html" licenseType="Apache 2.0" fileSize="********"/> 
  <plugin name="Registration" latest="1.7.2" changelog="http://www.igniterealtime.org/projects/openfire/plugins/1.7.2/registration/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/1.7.2/registration.jar" author="Ryan Graham" description="Performs various actions whenever a new user account is created." icon="http://www.igniterealtime.org/projects/openfire/plugins/1.7.2/registration/logo_small.gif" minServerVersion="4.0.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/1.7.2/registration/readme.html" fileSize="55992"/> 
  <plugin name="Search" latest="1.7.3" changelog="http://www.igniterealtime.org/projects/openfire/plugins/1.7.3/search/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/1.7.3/search.jar" author="Ryan Graham" description="Provides support for Jabber Search (XEP-0055)" icon="http://www.igniterealtime.org/projects/openfire/plugins/1.7.3/search/logo_small.gif" minServerVersion="4.1.1" readme="http://www.igniterealtime.org/projects/openfire/plugins/1.7.3/search/readme.html" fileSize="71803"/> 
  <plugin name="Client Control" latest="2.1.3" changelog="http://www.igniterealtime.org/projects/openfire/plugins/2.1.3/clientControl/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/2.1.3/clientControl.jar" author="Jive Software" description="Controls clients allowed to connect and available features" icon="http://www.igniterealtime.org/projects/openfire/plugins/2.1.3/clientControl/logo_small.gif" minServerVersion="4.0.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/2.1.3/clientControl/readme.html" fileSize="143448"/> 
  <plugin name="Candy" latest="2.2.0 Release 2" changelog="http://www.igniterealtime.org/projects/openfire/plugins/2.2.0-Release-2/candy/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/2.2.0-Release-2/candy.jar" author="Guus der Kinderen" description="Adds the (third-party) Candy web client to Openfire." icon="http://www.igniterealtime.org/projects/openfire/plugins/2.2.0-Release-2/candy/logo_small.png" minServerVersion="4.1.5" readme="http://www.igniterealtime.org/projects/openfire/plugins/2.2.0-Release-2/candy/readme.html" fileSize="577491"/> 
  <plugin name="Presence Service" latest="1.7.0" changelog="http://www.igniterealtime.org/projects/openfire/plugins/1.7.0/presence/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/1.7.0/presence.jar" author="Jive Software" description="Exposes presence information through HTTP." icon="http://www.igniterealtime.org/projects/openfire/plugins/1.7.0/presence/logo_small.gif" minServerVersion="4.0.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/1.7.0/presence/readme.html" fileSize="29076"/> 
  <plugin name="Subscription" latest="1.4.0" changelog="http://www.igniterealtime.org/projects/openfire/plugins/1.4.0/subscription/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/1.4.0/subscription.jar" author="Ryan Graham" description="Automatically accepts or rejects subscription requests" icon="http://www.igniterealtime.org/projects/openfire/plugins/1.4.0/subscription/logo_small.gif" minServerVersion="4.0.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/1.4.0/subscription/readme.html" fileSize="20654"/> 
  <plugin name="inVerse" latest="5.0.5 Release 1" changelog="http://www.igniterealtime.org/projects/openfire/plugins/*******/inverse/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/*******/inverse.jar" author="Guus der Kinderen" description="Adds the (third-party, Converse-based) inVerse web client to Openfire." icon="http://www.igniterealtime.org/projects/openfire/plugins/*******/inverse/logo_small.png" minServerVersion="4.1.5" readme="http://www.igniterealtime.org/projects/openfire/plugins/*******/inverse/readme.html" fileSize="4708703"/> 
  <plugin name="Email on Away" latest="1.0.3" changelog="http://www.igniterealtime.org/projects/openfire/plugins/1.0.3/emailOnAway/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/1.0.3/emailOnAway.jar" author="Nick Mossie" description="Messages sent to alternate location when recipient is away" minServerVersion="2.3.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/1.0.3/emailOnAway/readme.html" fileSize="5923"/> 
  <plugin name="Certificate Manager" latest="1.1.0" changelog="http://www.igniterealtime.org/projects/openfire/plugins/1.1.0/certificatemanager/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/1.1.0/certificatemanager.jar" author="Guus der Kinderen" description="Adds certificate management features." icon="http://www.igniterealtime.org/projects/openfire/plugins/1.1.0/certificatemanager/logo_small.png" minServerVersion="4.3.0 Alpha" readme="http://www.igniterealtime.org/projects/openfire/plugins/1.1.0/certificatemanager/readme.html" fileSize="47750"/> 
  <plugin name="Random Avatar Generator Plugin" latest="1.0.0" changelog="http://www.igniterealtime.org/projects/openfire/plugins/1.0.0/randomavatar/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/1.0.0/randomavatar.jar" author="Guus der Kinderen" description="Generates semi-random avatar images." icon="http://www.igniterealtime.org/projects/openfire/plugins/1.0.0/randomavatar/logo_small.gif" minServerVersion="4.1.5" readme="http://www.igniterealtime.org/projects/openfire/plugins/1.0.0/randomavatar/readme.html" fileSize="423022"/> 
  <plugin name="JmxWeb Plugin" latest="0.0.7" changelog="http://www.igniterealtime.org/projects/openfire/plugins/0.0.7/jmxweb/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/0.0.7/jmxweb.jar" author="igniterealtime.org" description="JmxWeb plugin is web based platform for managing and monitoring openfire via JMX." minServerVersion="4.1.5" readme="http://www.igniterealtime.org/projects/openfire/plugins/0.0.7/jmxweb/readme.html" licenseType="Apache 2.0" fileSize="9659320"/> 
  <plugin name="MUC Service" latest="0.2.3" changelog="http://www.igniterealtime.org/projects/openfire/plugins/0.2.3/mucservice/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/0.2.3/mucservice.jar" author="Roman Soldatow" description="MUC administration over REST Interface" icon="http://www.igniterealtime.org/projects/openfire/plugins/0.2.3/mucservice/logo_small.gif" minServerVersion="3.9.1" readme="http://www.igniterealtime.org/projects/openfire/plugins/0.2.3/mucservice/readme.html" fileSize="2568312"/> 
  <plugin name="User Status Plugin" latest="1.2.2" changelog="http://www.igniterealtime.org/projects/openfire/plugins/1.2.2/userstatus/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/1.2.2/userstatus.jar" author="Stefan Reuter" description="Openfire plugin to save the user status to the database." icon="http://www.igniterealtime.org/projects/openfire/plugins/1.2.2/userstatus/logo_small.gif" minServerVersion="4.0.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/1.2.2/userstatus/readme.html" licenseType="gpl" fileSize="28372"/> 
  <plugin name="GoJara" latest="2.2.1" changelog="http://www.igniterealtime.org/projects/openfire/plugins/2.2.1/gojara/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/2.2.1/gojara.jar" author="Holger Bergunde / Daniel Henninger / Axel-F. Brand" description="XEP-0321: Remote Roster Management support" icon="http://www.igniterealtime.org/projects/openfire/plugins/2.2.1/gojara/logo_small.png" minServerVersion="4.0.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/2.2.1/gojara/readme.html" fileSize="319125"/> 
  <plugin name="User Service" latest="2.1.0" changelog="http://www.igniterealtime.org/projects/openfire/plugins/2.1.0/userservice/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/2.1.0/userservice.jar" author="Roman Soldatow, Justin Hunt" description="(Deprecated) Please use the REST API Plugin. Allows administration of users via HTTP requests." icon="http://www.igniterealtime.org/projects/openfire/plugins/2.1.0/userservice/logo_small.gif" minServerVersion="4.0.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/2.1.0/userservice/readme.html" fileSize="2588031"/> 
  <plugin name="Openfire Meetings" latest="0.9.5" changelog="http://www.igniterealtime.org/projects/openfire/plugins/0.9.5/ofmeet/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/0.9.5/ofmeet.jar" author="Ignite Realtime" description="Provides high quality, scalable video conferences." icon="http://www.igniterealtime.org/projects/openfire/plugins/0.9.5/ofmeet/logo_small.gif" minServerVersion="4.3.2" readme="http://www.igniterealtime.org/projects/openfire/plugins/0.9.5/ofmeet/readme.html" fileSize="113148396"/> 
  <plugin name="Packet Filter" latest="3.3.0" changelog="http://www.igniterealtime.org/projects/openfire/plugins/3.3.0/packetFilter/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/3.3.0/packetFilter.jar" author="Nate Putnam" description="Rules to enforce ethical communication" icon="http://www.igniterealtime.org/projects/openfire/plugins/3.3.0/packetFilter/logo_small.gif" minServerVersion="4.0.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/3.3.0/packetFilter/readme.html" fileSize="100700"/> 
  <plugin name="User Creation" latest="1.3.0" changelog="http://www.igniterealtime.org/projects/openfire/plugins/1.3.0/userCreation/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/1.3.0/userCreation.jar" author="Jive Software" description="Creates users and populates rosters." minServerVersion="4.0.0" fileSize="20930"/> 
  <plugin name="Openfire WebSocket" latest="1.2.1" changelog="http://www.igniterealtime.org/projects/openfire/plugins/1.2.1/websocket/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/1.2.1/websocket.jar" author="Tom Evans" description="Provides WebSocket support for Openfire." icon="http://www.igniterealtime.org/projects/openfire/plugins/1.2.1/websocket/logo_small.gif" minServerVersion="4.1.5" readme="http://www.igniterealtime.org/projects/openfire/plugins/1.2.1/websocket/readme.html" fileSize="122092"/> 
  <plugin name="Email Listener" latest="1.1.0" changelog="http://www.igniterealtime.org/projects/openfire/plugins/1.1.0/emaillistener/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/1.1.0/emaillistener.jar" author="Jive Software" description="Listens for emails and sends alerts to specific users." icon="http://www.igniterealtime.org/projects/openfire/plugins/1.1.0/emaillistener/logo_small.gif" minServerVersion="3.9.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/1.1.0/emaillistener/readme.html" fileSize="13146"/> 
  <plugin name="Openfire Focus Provider" latest="0.9.4" changelog="http://www.igniterealtime.org/projects/openfire/plugins/0.9.4/offocus/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/0.9.4/offocus.jar" author="Ignite Realtime" description="Instantiates a Jitsi Focus manager." icon="http://www.igniterealtime.org/projects/openfire/plugins/0.9.4/offocus/logo_small.gif" minServerVersion="4.1.5" readme="http://www.igniterealtime.org/projects/openfire/plugins/0.9.4/offocus/readme.html" fileSize="27570117"/> 
  <plugin name="Thread Dump" latest="1.0.0" changelog="http://www.igniterealtime.org/projects/openfire/plugins/1.0.0/threaddump/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/1.0.0/threaddump.jar" author="Ignite Realtime" description="A plugin that can be used to generate diagnostics." icon="http://www.igniterealtime.org/projects/openfire/plugins/1.0.0/threaddump/logo_small.png" readme="http://www.igniterealtime.org/projects/openfire/plugins/1.0.0/threaddump/readme.html" fileSize="44861"/> 
  <plugin name="Avatar Resizer" latest="1.0.1" changelog="http://www.igniterealtime.org/projects/openfire/plugins/1.0.1/avatarResizer/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/1.0.1/avatarResizer.jar" author="Guus der Kinderen" description="Ensures vCard-based avatars are not to large for comfort." icon="http://www.igniterealtime.org/projects/openfire/plugins/1.0.1/avatarResizer/logo_small.gif" readme="http://www.igniterealtime.org/projects/openfire/plugins/1.0.1/avatarResizer/readme.html" fileSize="10624"/> 
  <plugin name="Load Statistic" latest="1.2.0" changelog="http://www.igniterealtime.org/projects/openfire/plugins/1.2.0/loadStats/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/1.2.0/loadStats.jar" author="Jive Software" description="Logs load statistics to a file" icon="http://www.igniterealtime.org/projects/openfire/plugins/1.2.0/loadStats/logo_small.gif" minServerVersion="3.9.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/1.2.0/loadStats/readme.html" fileSize="8474"/> 
  <plugin name="STUN server plugin" latest="1.2.2" changelog="http://www.igniterealtime.org/projects/openfire/plugins/1.2.2/stunserver/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/1.2.2/stunserver.jar" author="Ignite Realtime" description="Adds STUN functionality to Openfire" icon="http://www.igniterealtime.org/projects/openfire/plugins/1.2.2/stunserver/logo_small.gif" minServerVersion="4.0.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/1.2.2/stunserver/readme.html" fileSize="101310"/> 
  <plugin name="Content Filter" latest="1.8.0" changelog="http://www.igniterealtime.org/projects/openfire/plugins/1.8.0/contentFilter/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/1.8.0/contentFilter.jar" author="Conor Hayes" description="Scans message packets for defined patterns" icon="http://www.igniterealtime.org/projects/openfire/plugins/1.8.0/contentFilter/logo_small.gif" minServerVersion="4.0.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/1.8.0/contentFilter/readme.html" fileSize="27570"/> 
  <plugin name="REST API" latest="1.3.9" changelog="http://www.igniterealtime.org/projects/openfire/plugins/1.3.9/restAPI/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/1.3.9/restAPI.jar" author="Roman Soldatow" description="Allows administration over a RESTful API." icon="http://www.igniterealtime.org/projects/openfire/plugins/1.3.9/restAPI/logo_small.gif" minServerVersion="4.1.1" readme="http://www.igniterealtime.org/projects/openfire/plugins/1.3.9/restAPI/readme.html" fileSize="3488590"/> 
  <plugin name="Broadcast" latest="1.9.2" changelog="http://www.igniterealtime.org/projects/openfire/plugins/1.9.2/broadcast/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/1.9.2/broadcast.jar" author="Ignite Realtime" description="The broadcast plugin broadcasts messages to all users in the system or to specific groups" icon="http://www.igniterealtime.org/projects/openfire/plugins/1.9.2/broadcast/logo_small.gif" minServerVersion="4.0.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/1.9.2/broadcast/readme.html" fileSize="15163"/> 
  <plugin name="Non-SASL Authentication" latest="1.0.0" changelog="http://www.igniterealtime.org/projects/openfire/plugins/1.0.0/nonSaslAuthentication/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/1.0.0/nonSaslAuthentication.jar" author="Guus der Kinderen" description="This plugin implements a the (obsolete!) XEP-0078 specification for authentication using the jabber:iq:auth namespace." minServerVersion="4.1.0 Alpha" readme="http://www.igniterealtime.org/projects/openfire/plugins/1.0.0/nonSaslAuthentication/readme.html" fileSize="10803"/> 
  <plugin name="MotD (Message of the Day)" latest="1.2.2" changelog="http://www.igniterealtime.org/projects/openfire/plugins/1.2.2/motd/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/1.2.2/motd.jar" author="Ryan Graham" description="Allows admins to have a message sent to users each time they log in." icon="http://www.igniterealtime.org/projects/openfire/plugins/1.2.2/motd/logo_small.gif" minServerVersion="4.0.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/1.2.2/motd/readme.html" fileSize="31207"/> 
  <plugin name="CallbackOnOffline" latest="1.2.1" changelog="http://www.igniterealtime.org/projects/openfire/plugins/1.2.1/callbackOnOffline/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/1.2.1/callbackOnOffline.jar" author="Pavel Goski / Krzysztof Misztal" description="Url is called when recipient is offline" minServerVersion="4.0.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/1.2.1/callbackOnOffline/readme.html" fileSize="4322732"/> 
  <plugin name="Rdp" latest="0.0.1" changelog="http://www.igniterealtime.org/projects/openfire/plugins/0.0.1/rdp/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/0.0.1/rdp.jar" author="igniterealtime.org" description="RDP Gateway for Remote Desktop Control Changelog" minServerVersion="4.0.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/0.0.1/rdp/readme.html" licenseType="Apache 2.0" fileSize="27297204"/> 
  <plugin name="XML Debugger Plugin" latest="1.7.2" changelog="http://www.igniterealtime.org/projects/openfire/plugins/1.7.2/xmldebugger/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/1.7.2/xmldebugger.jar" author="Ignite Realtime" description="Prints XML traffic to the stdout (raw and interpreted XML)" minServerVersion="4.3.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/1.7.2/xmldebugger/readme.html" fileSize="20568"/> 
  <plugin name="DB Access" latest="1.2.2" changelog="http://www.igniterealtime.org/projects/openfire/plugins/1.2.2/dbaccess/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/1.2.2/dbaccess.jar" author="Daniel Henninger" description="Provides administrators with a simple direct access interface to their Openfire DB." icon="http://www.igniterealtime.org/projects/openfire/plugins/1.2.2/dbaccess/logo_small.gif" minServerVersion="4.0.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/1.2.2/dbaccess/readme.html" fileSize="11336"/> 
  <plugin name="User Import Export" latest="2.7.0" changelog="http://www.igniterealtime.org/projects/openfire/plugins/2.7.0/userImportExport/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/2.7.0/userImportExport.jar" author="Ryan Graham" description="Enables import and export of user data" icon="http://www.igniterealtime.org/projects/openfire/plugins/2.7.0/userImportExport/logo_small.gif" minServerVersion="4.3.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/2.7.0/userImportExport/readme.html" fileSize="842649"/> 
  <plugin name="Just married" latest="1.2.2" changelog="http://www.igniterealtime.org/projects/openfire/plugins/1.2.2/justmarried/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/1.2.2/justmarried.jar" author="Holger Bergunde" description="Allows admins to rename or copy users" icon="http://www.igniterealtime.org/projects/openfire/plugins/1.2.2/justmarried/logo_small.png" minServerVersion="4.0.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/1.2.2/justmarried/readme.html" fileSize="373194"/> 
  <plugin name="NodeJs" latest="0.1.0" changelog="http://www.igniterealtime.org/projects/openfire/plugins/0.1.0/nodejs/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/0.1.0/nodejs.jar" author="igniterealtime.org" description="Integrates NodeJs Applications with Openfire." icon="http://www.igniterealtime.org/projects/openfire/plugins/0.1.0/nodejs/logo_small.gif" minServerVersion="4.0.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/0.1.0/nodejs/readme.html" licenseType="Apache 2.0" fileSize="15635100"/> 
  <plugin name="Monitoring Service" latest="1.8.1" changelog="http://www.igniterealtime.org/projects/openfire/plugins/1.8.1/monitoring/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/1.8.1/monitoring.jar" author="IgniteRealtime // Jive Software" description="Monitors conversations and statistics of the server." icon="http://www.igniterealtime.org/projects/openfire/plugins/1.8.1/monitoring/logo_small.gif" minServerVersion="4.4.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/1.8.1/monitoring/readme.html" fileSize="10645406"/> 
  <plugin name="Jingle Nodes Plugin" latest="0.2.0" changelog="http://www.igniterealtime.org/projects/openfire/plugins/0.2.0/jingleNodes/changelog.html" url="http://www.igniterealtime.org/projects/openfire/plugins/0.2.0/jingleNodes.jar" author="Jingle Nodes (Rodrigo Martins)" description="Provides support for Jingle Nodes" icon="http://www.igniterealtime.org/projects/openfire/plugins/0.2.0/jingleNodes/logo_small.gif" minServerVersion="4.0.0" readme="http://www.igniterealtime.org/projects/openfire/plugins/0.2.0/jingleNodes/readme.html" fileSize="1038541"/>
</available>