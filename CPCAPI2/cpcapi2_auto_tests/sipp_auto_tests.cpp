#include "cpcapi2_test_fixture.h"
#include "test_call_events.h"
#include "test_events.h"
#include "test_account_events.h"
#include "repro/Proxy.hxx"
#include "test_framework/sipp_runner.h"
#include "impl/call/SipConversationManagerInternal.h"

using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::SipConversation;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::Media;

#if defined(__APPLE__) || (defined(__linux) && !defined(ANDROID)) 
#define CPCAPI2_SIPP_SUPPORTED 1
#endif

const int kCpcStrNpos = -1; // workaround for kCpcStrNpos being undefined on xcode builds

class SippTests : public CpcapiAutoTest
{
public:
   SippTests() {}
   virtual ~SippTests() {}
};

#if CPCAPI2_SIPP_SUPPORTED
// used to investigate audio problem report mentioned in OBELISK-5919

// uses subset of RTP from Ext_48609-2021-06-01_13.09.48.pcap (provided by Zebra)
// Time=******** to Time=********
// 120 seconds of RTP in total
TEST_F(SippTests, DISABLED_CernerTamperedOpus_ScenarioA_Instance1)
{
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.useRegistrar = false;
   alice.config.settings.outboundProxy = "";
   alice.config.settings.domain = "127.0.0.1";
   alice.config.settings.minSipPort = 55060;
   alice.config.settings.maxSipPort = 55060;
   alice.init();
   alice.enable();

   MediaInfo aliceAudio;
   MediaInfo aliceVideo;
   
   SippRunnerSettings sippRunnerSettings;
   sippRunnerSettings.sipListenPort = 50010;
   sippRunnerSettings.mediaPort = 50020;
   sippRunnerSettings.timeoutSec = 120;
   sippRunnerSettings.sipTargetPort = alice.config.settings.minSipPort;
   sippRunnerSettings.scenarioFileName = "BasicCallTests.CernerTamperedOpus_ScenarioA_Instance1.xml";
   
   SippRunner sippRunner(sippRunnerSettings);
   ASSERT_EQ(kSuccess, sippRunner.start());

   std::stringstream sippUriNoPort;
   sippUriNoPort << "sip:sip@127.0.0.1";
   std::stringstream sippUriWithPort;
   sippUriWithPort << sippUriNoPort.str() << ":" << sippRunnerSettings.sipListenPort;

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, sippUriWithPort.str().c_str());
   alice.conversation->start(aliceCall);
   
   const time_t callStartSec = time(0);

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      assertNewConversationOutgoingAudio_crypto(alice, aliceCall, sippUriNoPort.str().c_str(), aliceAudio, [](const NewConversationEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      
      for (int i = 0; i < 100; ++i)
      {
         // large delta around t=21 sec
         safeCout("Refreshing conversation stats at call time t=" << (time(0) - callStartSec));
         assertSuccess(alice.conversation->refreshConversationStatistics(aliceCall, true, true, true));
         {
            SipConversationHandle h;
            ConversationStatisticsUpdatedEvent evt;
         
            ASSERT_TRUE(alice.conversationEvents->expectEvent("SipConversationHandler::onConversationStatisticsUpdated",
               15000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
         }
         
         std::this_thread::sleep_for(std::chrono::seconds(5));
      }
      
      alice.conversation->end(aliceCall);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
      assertCallHadMedia(alice, aliceCall, true, false);
      
      // give time for sipp to response to BYE. is there a better way?
      std::this_thread::sleep_for(std::chrono::seconds(5));
      
      sippRunner.stop();
   });

   waitForMs(aliceEvents, std::chrono::minutes(3));
}

// uses subset of RTP from Ext-48650-2021-06-01_13.08.11.pcap (provided by Zebra)
// Time=******** to Time=********
// 108 seconds of RTP in total
TEST_F(SippTests, DISABLED_CernerTamperedOpus_ScenarioA_Instance2)
{
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.useRegistrar = false;
   alice.config.settings.outboundProxy = "";
   alice.config.settings.domain = "127.0.0.1";
   alice.config.settings.minSipPort = 55060;
   alice.config.settings.maxSipPort = 55060;
   alice.init();
   alice.enable();

   MediaInfo aliceAudio;
   MediaInfo aliceVideo;
   
   SippRunnerSettings sippRunnerSettings;
   sippRunnerSettings.sipListenPort = 50010;
   sippRunnerSettings.mediaPort = 50020;
   sippRunnerSettings.timeoutSec = 120;
   sippRunnerSettings.sipTargetPort = alice.config.settings.minSipPort;
   sippRunnerSettings.scenarioFileName = "BasicCallTests.CernerTamperedOpus_ScenarioA_Instance2.xml";
   
   SippRunner sippRunner(sippRunnerSettings);
   ASSERT_EQ(kSuccess, sippRunner.start());

   std::stringstream sippUriNoPort;
   sippUriNoPort << "sip:sip@127.0.0.1";
   std::stringstream sippUriWithPort;
   sippUriWithPort << sippUriNoPort.str() << ":" << sippRunnerSettings.sipListenPort;

   alice.audio->queryCodecList();
   alice.audio->setCodecPayloadType(cpc::hash(cpc::string("opus48000")), 102);

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, sippUriWithPort.str().c_str());
   alice.conversation->start(aliceCall);
   
   const time_t callStartSec = time(0);

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      assertNewConversationOutgoingAudio_crypto(alice, aliceCall, sippUriNoPort.str().c_str(), aliceAudio, [](const NewConversationEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      
      for (int i = 0; i < 100; ++i)
      {
         // large delta around t=7 sec
         safeCout("Refreshing conversation stats at call time t=" << (time(0) - callStartSec));
         assertSuccess(alice.conversation->refreshConversationStatistics(aliceCall, true, true, true));
         {
            SipConversationHandle h;
            ConversationStatisticsUpdatedEvent evt;
         
            ASSERT_TRUE(alice.conversationEvents->expectEvent("SipConversationHandler::onConversationStatisticsUpdated",
               15000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
         }
         
         std::this_thread::sleep_for(std::chrono::seconds(5));
      }
      
      alice.conversation->end(aliceCall);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
      assertCallHadMedia(alice, aliceCall, true, false);
      
      // give time for sipp to response to BYE. is there a better way?
      std::this_thread::sleep_for(std::chrono::seconds(5));
      
      sippRunner.stop();
   });

   waitForMs(aliceEvents, std::chrono::minutes(3));
}

// uses subset of RTP from Ext-27429-2021-05-19_08.30.19.pcap (provided by Zebra)
// Time=******** to Time=********
// 108 seconds of RTP in total
TEST_F(SippTests, DISABLED_CernerTamperedOpus_ScenarioB)
{
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.useRegistrar = false;
   alice.config.settings.outboundProxy = "";
   alice.config.settings.domain = "127.0.0.1";
   alice.config.settings.minSipPort = 55060;
   alice.config.settings.maxSipPort = 55060;
   alice.init();
   alice.enable();

   MediaInfo aliceAudio;
   MediaInfo aliceVideo;
   
   SippRunnerSettings sippRunnerSettings;
   sippRunnerSettings.sipListenPort = 50010;
   sippRunnerSettings.mediaPort = 50020;
   sippRunnerSettings.timeoutSec = 120;
   sippRunnerSettings.sipTargetPort = alice.config.settings.minSipPort;
   sippRunnerSettings.scenarioFileName = "BasicCallTests.CernerTamperedOpus_ScenarioB.xml";
   
   SippRunner sippRunner(sippRunnerSettings);
   ASSERT_EQ(kSuccess, sippRunner.start());

   std::stringstream sippUriNoPort;
   sippUriNoPort << "sip:sip@127.0.0.1";
   std::stringstream sippUriWithPort;
   sippUriWithPort << sippUriNoPort.str() << ":" << sippRunnerSettings.sipListenPort;

   alice.audio->queryCodecList();
   alice.audio->setCodecPayloadType(cpc::hash(cpc::string("opus48000")), 102);

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, sippUriWithPort.str().c_str());
   alice.conversation->start(aliceCall);
   
   const time_t callStartSec = time(0);

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      assertNewConversationOutgoingAudio_crypto(alice, aliceCall, sippUriNoPort.str().c_str(), aliceAudio, [](const NewConversationEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      
      for (int i = 0; i < 100; ++i)
      {
         // large delta around t=7 sec
         safeCout("Refreshing conversation stats at call time t=" << (time(0) - callStartSec));
         assertSuccess(alice.conversation->refreshConversationStatistics(aliceCall, true, true, true));
         {
            SipConversationHandle h;
            ConversationStatisticsUpdatedEvent evt;
         
            ASSERT_TRUE(alice.conversationEvents->expectEvent("SipConversationHandler::onConversationStatisticsUpdated",
               15000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
         }
         
         std::this_thread::sleep_for(std::chrono::seconds(5));
      }
      
      alice.conversation->end(aliceCall);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
      assertCallHadMedia(alice, aliceCall, true, false);
      
      // give time for sipp to response to BYE. is there a better way?
      std::this_thread::sleep_for(std::chrono::seconds(5));
      
      sippRunner.stop();
   });

   waitForMs(aliceEvents, std::chrono::minutes(3));
}

#ifndef __linux__ // we don't have the VQmon lib for Linux
TEST_F(SippTests, IncomingFreeswitchDtxOpus)
{
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.useRegistrar = false;
   alice.config.settings.outboundProxy = "";
   alice.config.settings.domain = "127.0.0.1";
   alice.config.settings.minSipPort = 55060;
   alice.config.settings.maxSipPort = 55060;
   
   alice.init();
   
   CPCAPI2::CallQuality::CallQualityReporterHandle aliceCqr = alice.createCallQualityReporter();
   CPCAPI2::CallQuality::CallQualityReporterConfig aliceCqrConfig;
   aliceCqrConfig.reportingIntervalSeconds = 10;
   aliceCqrConfig.sipAccount = alice.handle;
   aliceCqrConfig.ignoreFailures = true;
   alice.callQualityReport->configureCallQualityReporter(aliceCqr, aliceCqrConfig);
   alice.callQualityReport->startCallQualityReporter(aliceCqr);
   
   alice.enable();

   MediaInfo aliceAudio;
   MediaInfo aliceVideo;
   
   SippRunnerSettings sippRunnerSettings;
   sippRunnerSettings.sipListenPort = 50010;
   sippRunnerSettings.mediaPort = 50020;
   sippRunnerSettings.timeoutSec = 120;
   sippRunnerSettings.sipTargetPort = alice.config.settings.minSipPort;
   sippRunnerSettings.scenarioFileName = "SippTests.IncomingFreeswitchDtxOpus.xml";
   
   SippRunner sippRunner(sippRunnerSettings);
   ASSERT_EQ(kSuccess, sippRunner.start());

   std::stringstream sippUriNoPort;
   sippUriNoPort << "sip:sip@127.0.0.1";
   std::stringstream sippUriWithPort;
   sippUriWithPort << sippUriNoPort.str() << ":" << sippRunnerSettings.sipListenPort;

   alice.audio->queryCodecList();
   alice.audio->setCodecPayloadType(cpc::hash(cpc::string("opus48000")), 102);

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, sippUriWithPort.str().c_str());
   alice.conversation->start(aliceCall);
   
   const time_t callStartSec = time(0);

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      assertNewConversationOutgoingAudio_crypto(alice, aliceCall, sippUriNoPort.str().c_str(), aliceAudio, [](const NewConversationEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      
      {
         CPCAPI2::CallQuality::CallQualityReporterHandle h;
         CPCAPI2::CallQuality::CallQualityReportGeneratedEvent evt;
         ASSERT_TRUE(alice.callQualityEvents->expectEvent("CallQualityReportHandler::onCallQualityReportGenerated",
                                                          60000, HandleEqualsPred<CPCAPI2::CallQuality::CallQualityReporterHandle>(aliceCqr), h, evt));
         
         // we might want to update the test to find MOS, loss rate values from the report and make sure they are at expected levels
      }
      
      alice.conversation->end(aliceCall);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);

      SipConversationState currState;
      ASSERT_EQ(alice.conversationState->getState(aliceCall, currState), kSuccess);
      ASSERT_EQ(1, currState.statistics.audioChannels.size());
      ASSERT_GT(currState.statistics.audioChannels[0].streamDataCounters.packetsReceived, 0);
      ASSERT_EQ(currState.statistics.audioChannels[0].streamStatistics.cumulativeLost, 0);
      ASSERT_EQ(currState.statistics.audioChannels.begin()->XRvoipMetrics.lossRate, 0);
      ASSERT_GT(currState.statistics.audioChannels.begin()->XRvoipMetrics.MOSLQ, 3);
      
      alice.callQualityReport->stopCallQualityReporter(aliceCqr);

      // give time for sipp to response to BYE. is there a better way?
      std::this_thread::sleep_for(std::chrono::seconds(5));
      
      sippRunner.stop();
   });

   waitForMs(aliceEvents, std::chrono::minutes(3));
}
#endif // #ifndef __linux__

#ifndef __linux__ // we don't have the VQmon lib for Linux
TEST_F(SippTests, IncomingPacketLossOpus)
{
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.useRegistrar = false;
   alice.config.settings.outboundProxy = "";
   alice.config.settings.domain = "127.0.0.1";
   alice.config.settings.minSipPort = 55060;
   alice.config.settings.maxSipPort = 55060;
   
   alice.init();
   
   CPCAPI2::CallQuality::CallQualityReporterHandle aliceCqr = alice.createCallQualityReporter();
   CPCAPI2::CallQuality::CallQualityReporterConfig aliceCqrConfig;
   aliceCqrConfig.reportingIntervalSeconds = 10;
   aliceCqrConfig.sipAccount = alice.handle;
   aliceCqrConfig.ignoreFailures = true;
   alice.callQualityReport->configureCallQualityReporter(aliceCqr, aliceCqrConfig);
   alice.callQualityReport->startCallQualityReporter(aliceCqr);
   
   alice.enable();

   MediaInfo aliceAudio;
   MediaInfo aliceVideo;
   
   SippRunnerSettings sippRunnerSettings;
   sippRunnerSettings.sipListenPort = 50010;
   sippRunnerSettings.mediaPort = 50020;
   sippRunnerSettings.timeoutSec = 120;
   sippRunnerSettings.sipTargetPort = alice.config.settings.minSipPort;
   sippRunnerSettings.scenarioFileName = "SippTests.9PercentLossOpus.xml";

   SippRunner sippRunner(sippRunnerSettings);
   sippRunner.start();

   std::stringstream sippUriNoPort;
   sippUriNoPort << "sip:sip@127.0.0.1";
   std::stringstream sippUriWithPort;
   sippUriWithPort << sippUriNoPort.str() << ":" << sippRunnerSettings.sipListenPort;

   alice.audio->queryCodecList();
   alice.audio->setCodecPayloadType(cpc::hash(cpc::string("opus48000")), 120);

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, sippUriWithPort.str().c_str());
   alice.conversation->start(aliceCall);
   
   const time_t callStartSec = time(0);

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      assertNewConversationOutgoingAudio_crypto(alice, aliceCall, sippUriNoPort.str().c_str(), aliceAudio, [](const NewConversationEvent& evt) {});
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      
      {
         CPCAPI2::CallQuality::CallQualityReporterHandle h;
         CPCAPI2::CallQuality::CallQualityReportGeneratedEvent evt;
         ASSERT_TRUE(alice.callQualityEvents->expectEvent("CallQualityReportHandler::onCallQualityReportGenerated",
                                                          60000, HandleEqualsPred<CPCAPI2::CallQuality::CallQualityReporterHandle>(aliceCqr), h, evt));
         
         // we might want to update the test to find MOS, loss rate values from the report and make sure they are at expected levels
      }
      
      alice.conversation->end(aliceCall);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);

      SipConversationState currState;
      ASSERT_EQ(alice.conversationState->getState(aliceCall, currState), kSuccess);
      ASSERT_EQ(1, currState.statistics.audioChannels.size());
      ASSERT_GT(currState.statistics.audioChannels[0].streamDataCounters.packetsReceived, 0);
      ASSERT_GT(currState.statistics.audioChannels[0].streamStatistics.cumulativeLost, 0);
      ASSERT_GT(currState.statistics.audioChannels.begin()->XRvoipMetrics.lossRate, 0);
      ASSERT_GT(currState.statistics.audioChannels.begin()->XRvoipMetrics.MOSLQ, 3);
      
      alice.callQualityReport->stopCallQualityReporter(aliceCqr);

      // give time for sipp to response to BYE. is there a better way?
      std::this_thread::sleep_for(std::chrono::seconds(5));
      
      sippRunner.stop();
   });

   waitForMs(aliceEvents, std::chrono::minutes(3));
}
#endif // #ifndef __linux__

// OBELISK-6181: forked 200 OK (different To tag vs 180 Ringing) causes SDK to report wrong local mediaEncryptionMode
#ifndef __linux__ // sipp binary retrieved from CentOS 7 epel doesn't seem to send 200 OK -- could be a bug/support problem with forking and sipp
TEST_F(SippTests, SwissCom_Srtp_Forked200Ok)
{
   SecureTestAccount alice("alice", Account_NoInit);
   
   bool useTls = true;
   
   if (useTls)
   {
      alice.config.settings.sipTransportType = SipAccountTransport_TLS;
   }
   else
   {
      alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   }
   alice.config.settings.ignoreCertVerification = true;
   alice.config.settings.useRegistrar = false;
   alice.config.settings.outboundProxy = "";
   alice.config.settings.domain = "127.0.0.1";
   alice.config.settings.sourceAddress = "127.0.0.1";
   alice.init();
   alice.enable();

   MediaInfo aliceAudio;
   aliceAudio.mediaDirection = MediaDirection_SendReceive;
   aliceAudio.mediaType = MediaType_Audio;
   aliceAudio.mediaEncryptionOptions.mediaEncryptionMode = MediaEncryptionMode_SRTP_SDES_Encrypted;
   aliceAudio.mediaEncryptionOptions.secureMediaRequired = true;

   MediaInfo bobAudio;
   bobAudio.mediaDirection = MediaDirection_SendReceive;
   bobAudio.mediaType = MediaType_Audio;
   bobAudio.mediaEncryptionOptions.mediaEncryptionMode = MediaEncryptionMode_SRTP_SDES_Encrypted;
   bobAudio.mediaEncryptionOptions.secureMediaRequired = true;
   
   SippRunnerSettings sippRunnerSettings;
   sippRunnerSettings.sipListenPort = 50010;
   sippRunnerSettings.mediaPort = 50020;
   sippRunnerSettings.timeoutSec = 120;
   sippRunnerSettings.scenarioFileName = "SippTests.SwissCom_Srtp_Forked200Ok.xml";
   sippRunnerSettings.callLimit = 2; // increase for the forked call
   
   if (useTls)
   {
      sippRunnerSettings.transport = SippRunnerSettings::Transport_Tls;
   }
   else
   {
      sippRunnerSettings.transport = SippRunnerSettings::Transport_Udp;
   }
   
   SippRunner sippRunner(sippRunnerSettings);
   ASSERT_EQ(kSuccess, sippRunner.start());

   std::stringstream sippUriNoPort;
   sippUriNoPort << "sip:sip@127.0.0.1";
   std::stringstream sippUriWithPort;
   sippUriWithPort << sippUriNoPort.str() << ":" << sippRunnerSettings.sipListenPort;

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, sippUriWithPort.str().c_str());
   alice.conversation->configureMedia(aliceCall, aliceAudio);
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      assertNewConversationOutgoingAudio_crypto(alice, aliceCall, sippUriNoPort.str().c_str(), aliceAudio, [](const NewConversationEvent& evt) {});

      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaEncryptionMode_SRTP_SDES_Encrypted, evt.localMediaInfo[0].mediaEncryptionOptions.mediaEncryptionMode);
         ASSERT_EQ(MediaEncryptionMode_SRTP_SDES_Encrypted, evt.remoteMediaInfo[0].mediaEncryptionOptions.mediaEncryptionMode);
      });

      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      
      std::this_thread::sleep_for(std::chrono::seconds(15));
      alice.conversation->end(aliceCall);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
      
      // give time for sipp to response to BYE. is there a better way?
      std::this_thread::sleep_for(std::chrono::seconds(5));
      
      sippRunner.stop();
   });


   waitFor(aliceEvents);
}
#endif // #ifndef __linux__

// SCORE-933: *111 caller id incorrectly showing for call pulls on network changes if original call was forked by
// Swisscom
#ifndef __linux__ // sipp binary retrieved from CentOS 7 epel doesn't seem to send 200 OK -- could be a bug/support problem with forking and sipp
TEST_F(SippTests, SwissCom_Srtp_Forked200Ok_NetworkChange)
{
   const std::string starcode = "*111";
   TestAccount conf(starcode.c_str(), Account_Init);


   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;

   alice.config.settings.ignoreCertVerification = true;
   alice.config.settings.useRegistrar = false;
   alice.config.settings.outboundProxy = "";
   alice.config.settings.domain = "127.0.0.1";
   alice.config.settings.sourceAddress = "127.0.0.1";
   alice.init();
   alice.enable();

   SipAccountManagerInternal* accountMgr = dynamic_cast<SipAccountManagerInternal*>(alice.account);
   ASSERT_TRUE(accountMgr != NULL);
   accountMgr->setIgnoreNetworkChangeStarcodeFilter(alice.handle, false);
   accountMgr->setSkipResetTransportOnNetworkChange(alice.handle, true); // need to avoid UDP port in use exception on macOS
                                                                         // (see method header for further detail)

   
   SippRunnerSettings sippRunnerSettings;
   sippRunnerSettings.sipListenPort = 50010;
   sippRunnerSettings.mediaPort = 50020;
   sippRunnerSettings.timeoutSec = 120;
   sippRunnerSettings.scenarioFileName = "SippTests.SwissCom_Srtp_Forked200Ok_NetworkChange.xml";
   sippRunnerSettings.transport = SippRunnerSettings::Transport_Udp;
   
   SippRunner sippRunner(sippRunnerSettings);
   ASSERT_EQ(kSuccess, sippRunner.start());

   std::stringstream sippUriNoPort;
   sippUriNoPort << "sip:sip@127.0.0.1";
   std::stringstream sippUriWithPort;
   sippUriWithPort << sippUriNoPort.str() << ":" << sippRunnerSettings.sipListenPort;

   SipConversationSettings settings;
   settings.networkChangeHandoverMode = NetworkChangeHandoverMode_Starcode;
   settings.networkChangeHandoverStarcode = conf.config.settings.username;
   alice.conversation->setDefaultSettings(alice.handle, settings);

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);

   // Reconnection may result with same binding, ignore the filter to ensure that starcode handling is triggered
   SipConversationManagerInternal* convMgr = dynamic_cast<SipConversationManagerInternal*>(alice.conversation);
   ASSERT_TRUE(convMgr != NULL);
   convMgr->ignoreBindingFilterForStarcodeHandover(aliceCall);

   alice.conversation->addParticipant(aliceCall, sippUriWithPort.str().c_str());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      assertNewConversationOutgoing(alice, aliceCall, sippUriNoPort.str().c_str());

      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> wwan;
      wwan.insert("*******");
      alice.network->setMockInterfaces(wwan);

      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);

      // App receives the NewConversationEvent event for the starcode dialog, resulting from the network change
      SipConversationHandle aliceCallToConf = 0;
      {
         NewConversationEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onNewConversation", 15000, AlwaysTruePred(), aliceCallToConf, evt)) << " missed outgoing call event";
         ASSERT_EQ(aliceCall, evt.conversationToReplace) << " replaced conversation handle does not match";
         ASSERT_NE(aliceCall, aliceCallToConf);
         ASSERT_EQ(ConversationType_OutgoingNetworkChangeHandover, evt.conversationType) << " conversation type does not match with outgoing";

         // crux of SCORE-933 -- remote address was empty pre-fix, which will later result in *111 being provided as
         // remote address to the app (not what we want)
         ASSERT_EQ(sippUriWithPort.str(), std::string(evt.remoteAddress.c_str()));
         ASSERT_EQ("John Kimble", evt.remoteDisplayName); // specified in SIPp scenario file

         // note it's expected the *111 outbound call will fail since SIPp doesn't support handling of multiple dialogs in a single scenario
      }
      
      sippRunner.stop();
   });


   waitFor(aliceEvents);
}
#endif // #ifndef __linux__


// OBELISK-6340: forked call ended early by app causing crash
TEST_F(SippTests, SwissCom_Srtp_Forked_EndCallEarly)
{
   SecureTestAccount alice("alice", Account_NoInit);
   
   bool useTls = true;
   
   if (useTls)
   {
      alice.config.settings.sipTransportType = SipAccountTransport_TLS;
   }
   else
   {
      alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   }
   alice.config.settings.ignoreCertVerification = true;
   alice.config.settings.useRegistrar = false;
   alice.config.settings.outboundProxy = "";
   alice.config.settings.domain = "127.0.0.1";
   alice.config.settings.sourceAddress = "127.0.0.1";
   alice.init();
   alice.enable();

   MediaInfo aliceAudio;
   aliceAudio.mediaDirection = MediaDirection_SendReceive;
   aliceAudio.mediaType = MediaType_Audio;
   aliceAudio.mediaEncryptionOptions.mediaEncryptionMode = MediaEncryptionMode_SRTP_SDES_Encrypted;
   aliceAudio.mediaEncryptionOptions.secureMediaRequired = true;

   MediaInfo bobAudio;
   bobAudio.mediaDirection = MediaDirection_SendReceive;
   bobAudio.mediaType = MediaType_Audio;
   bobAudio.mediaEncryptionOptions.mediaEncryptionMode = MediaEncryptionMode_SRTP_SDES_Encrypted;
   bobAudio.mediaEncryptionOptions.secureMediaRequired = true;
   
   SippRunnerSettings sippRunnerSettings;
   sippRunnerSettings.sipListenPort = 50010;
   sippRunnerSettings.mediaPort = 50020;
   sippRunnerSettings.timeoutSec = 120;
   sippRunnerSettings.scenarioFileName = "SippTests.SwissCom_Srtp_Forked_EndCallEarly.xml";
   
   if (useTls)
   {
      sippRunnerSettings.transport = SippRunnerSettings::Transport_Tls;
   }
   else
   {
      sippRunnerSettings.transport = SippRunnerSettings::Transport_Udp;
   }
   
   SippRunner sippRunner(sippRunnerSettings);
   ASSERT_EQ(kSuccess, sippRunner.start());

   std::stringstream sippUriNoPort;
   sippUriNoPort << "sip:sip@127.0.0.1";
   std::stringstream sippUriWithPort;
   sippUriWithPort << sippUriNoPort.str() << ":" << sippRunnerSettings.sipListenPort;

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, sippUriWithPort.str().c_str());
   alice.conversation->configureMedia(aliceCall, aliceAudio);
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      assertNewConversationOutgoingAudio_crypto(alice, aliceCall, sippUriNoPort.str().c_str(), aliceAudio, [](const NewConversationEvent& evt) {});

      // hit the OBELISK-6340 crash on Jason's 2021 MacBook Pro with sleep of 35 ms.
      // to increase chance of hitting on other machines, go with a random sleep within
      // rough range.
      const int minMs = 20;
      const int maxMs = 50;
      const int rangeMs = maxMs - minMs + 1;
      const int numMs = rand() % rangeMs + minMs;

      std::this_thread::sleep_for(std::chrono::milliseconds(numMs));
      alice.conversation->end(aliceCall);

      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
      
      // give time for sipp to response to BYE. is there a better way?
      std::this_thread::sleep_for(std::chrono::seconds(2));
      
      sippRunner.stop();
   });


   waitFor(aliceEvents);
}

// OBELISK-6349: forked 200 OK (different To tag vs 180 Ringing) causes SDK to report wrong local conversation state
TEST_F(SippTests, SwissCom_Srtp_Forked200Ok_RemoteHold)
{
   SecureTestAccount alice("alice", Account_NoInit);
   
   bool useTls = true;
   
   if (useTls)
   {
      alice.config.settings.sipTransportType = SipAccountTransport_TLS;
   }
   else
   {
      alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   }
   alice.config.settings.ignoreCertVerification = true;
   alice.config.settings.useRegistrar = false;
   alice.config.settings.outboundProxy = "";
   alice.config.settings.domain = "127.0.0.1";
   alice.config.settings.sourceAddress = "127.0.0.1";
   alice.init();
   alice.enable();

   MediaInfo aliceAudio;
   aliceAudio.mediaDirection = MediaDirection_SendReceive;
   aliceAudio.mediaType = MediaType_Audio;
   aliceAudio.mediaEncryptionOptions.mediaEncryptionMode = MediaEncryptionMode_SRTP_SDES_Encrypted;
   aliceAudio.mediaEncryptionOptions.secureMediaRequired = true;

   MediaInfo bobAudio;
   bobAudio.mediaDirection = MediaDirection_SendReceive;
   bobAudio.mediaType = MediaType_Audio;
   bobAudio.mediaEncryptionOptions.mediaEncryptionMode = MediaEncryptionMode_SRTP_SDES_Encrypted;
   bobAudio.mediaEncryptionOptions.secureMediaRequired = true;
   
   SippRunnerSettings sippRunnerSettings;
   sippRunnerSettings.sipListenPort = 50010;
   sippRunnerSettings.mediaPort = 50020;
   sippRunnerSettings.timeoutSec = 120;
   sippRunnerSettings.scenarioFileName = "SippTests.SwissCom_Srtp_Forked200Ok_RemoteHold.xml";
   
   if (useTls)
   {
      sippRunnerSettings.transport = SippRunnerSettings::Transport_Tls;
   }
   else
   {
      sippRunnerSettings.transport = SippRunnerSettings::Transport_Udp;
   }
   
   SippRunner sippRunner(sippRunnerSettings);
   ASSERT_EQ(kSuccess, sippRunner.start());

   std::stringstream sippUriNoPort;
   sippUriNoPort << "sip:sipp@127.0.0.1";
   std::stringstream sippUriWithPort;
   sippUriWithPort << sippUriNoPort.str() << ":" << sippRunnerSettings.sipListenPort;

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, sippUriWithPort.str().c_str());
   alice.conversation->configureMedia(aliceCall, aliceAudio);
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      assertNewConversationOutgoingAudio_crypto(alice, aliceCall, sippUriNoPort.str().c_str(), aliceAudio, [](const NewConversationEvent& evt) {});

      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaEncryptionMode_SRTP_SDES_Encrypted, evt.localMediaInfo[0].mediaEncryptionOptions.mediaEncryptionMode);
         ASSERT_EQ(MediaEncryptionMode_SRTP_SDES_Encrypted, evt.remoteMediaInfo[0].mediaEncryptionOptions.mediaEncryptionMode);
      });

      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      // scenario places alice on hold
      assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendOnly);
      assertSuccess(alice.conversation->accept(aliceCall));
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_FALSE(evt.localHold);
         ASSERT_TRUE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_ReceiveOnly, evt.localMediaInfo[0].mediaDirection);
      });

      CPCAPI2::SipConversation::SipConversationHandle h;
      ConversationStateChangedEvent convStateChangedEvt;
      
      // we should *not* expect onConversationStateChanged to be fired at this point, since the target address has not changed
      // since the original 200 OK. Prior to the fix for OBELISK-6349, onConversationStateChanged would fire here becuase
      // the SDK had not properly stored the target address due to 180/200 OK forking.
      ASSERT_FALSE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onConversationStateChanged",
	      2000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, convStateChangedEvt));


      // scenario takes alice off hold
      assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendReceive);
      assertSuccess(alice.conversation->accept(aliceCall));
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });

      // again; expect no onConversationStateChanged event
      ASSERT_FALSE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onConversationStateChanged",
	      2000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, convStateChangedEvt));


      // re-INVITE with same SDP but new target address; expect onConversationStateChanged to be fired just after
      assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendReceive);
      assertSuccess(alice.conversation->accept(aliceCall));
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });

      ASSERT_TRUE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onConversationStateChanged",
	      5000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, convStateChangedEvt));
      EXPECT_EQ(convStateChangedEvt.remoteDisplayName, "Display Name Now Present");

      
      std::this_thread::sleep_for(std::chrono::seconds(2));
      alice.conversation->end(aliceCall);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
      
      // give time for sipp to respond to the BYE. is there a better way?
      std::this_thread::sleep_for(std::chrono::seconds(5));
      
      sippRunner.stop();
   });


   waitFor(aliceEvents);
}

// OBELISK-6242: SIP server repeatedly sending auth challenge with new nonce, stale=true,
// causing non-stop tight REGISTER loop
TEST_F(SippTests, RepeatStaleAuthNonce)
{
   const int sippListenPort = 50010;

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.useRegistrar = true;
   alice.config.settings.domain = "irak.com.bd";
   alice.config.settings.outboundProxy = ("127.0.0.1:" + std::to_string(sippListenPort)).c_str();
   alice.config.settings.minSipPort = 55060;
   alice.config.settings.maxSipPort = 55060;
   
   alice.init();

   SippRunnerSettings sippRunnerSettings;
   sippRunnerSettings.sipListenPort = sippListenPort;
   sippRunnerSettings.mediaPort = 50020;
   sippRunnerSettings.timeoutSec = 120;
   sippRunnerSettings.sipTargetPort = alice.config.settings.minSipPort;
   sippRunnerSettings.scenarioFileName = "SippTests.RepeatStaleAuthNonce.xml";

   SippRunner sippRunner(sippRunnerSettings);
   sippRunner.start();

   alice.enable(false);
   assertAccountRegistering(alice);

   for (int i = 0; i < 3; ++i)
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountClientAuthEvent evt;
      ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandlerInternal::onClientAuth", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(401, evt.responseStatusCode);
      ASSERT_TRUE(evt.willSendUpdatedRequest);
   }

   CPCAPI2::SipAccount::SipAccountHandle h;
   CPCAPI2::SipAccount::SipAccountClientAuthEvent evt;
   ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandlerInternal::onClientAuth", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_EQ(401, evt.responseStatusCode);
   // SDK breaks the loop with OBELISK-6242 fix; won't auth retry
   ASSERT_FALSE(evt.willSendUpdatedRequest);

   assertAccountDeregistered(alice);
}

void SippTestsAccountResponseTime(bool withAuth)
{
   const int sippListenPort = 50010;

   SippRunnerSettings sippRunnerSettings;
   sippRunnerSettings.sipListenPort = sippListenPort;
   sippRunnerSettings.mediaPort = 50020;
   sippRunnerSettings.timeoutSec = 120;
   sippRunnerSettings.sipTargetPort = 55060;

   if (withAuth)
   {
      sippRunnerSettings.scenarioFileName = "SippTests.RegisterResponseTimeWithAuth.xml";
   }
   else
   {
      sippRunnerSettings.scenarioFileName = "SippTests.RegisterResponseTimeNoAuth.xml";
   }

   SippRunner sippRunner(sippRunnerSettings);
   sippRunner.start();

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.useRegistrar = true;
   alice.config.settings.domain = "sipp.local";
   alice.config.settings.outboundProxy = ("127.0.0.1:" + std::to_string(sippListenPort)).c_str();
   alice.config.settings.minSipPort = sippRunnerSettings.sipTargetPort;
   alice.config.settings.maxSipPort = sippRunnerSettings.sipTargetPort;
   alice.init();

   alice.enable(false);
   assertAccountRegistering(alice);

   CPCAPI2::SipAccount::SipAccountHandle h;
   CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
   ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged",
                              20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   ASSERT_EQ(200, evt.signalingStatusCode);
   ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
   ASSERT_GT(evt.responseTimeMs, 500); // scenario file has 500 ms sleep
}

TEST_F(SippTests, AccountResponseTimeNoAuth)
{
   bool withAuthChallenge = false;
   SippTestsAccountResponseTime(withAuthChallenge);
}

TEST_F(SippTests, AccountResponseTimeWithAuth)
{
   bool withAuthChallenge = true;
   SippTestsAccountResponseTime(withAuthChallenge);
}

void SippTestsInviteResponseTime(bool withAuth, bool with180)
{
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.useRegistrar = false;
   alice.config.settings.outboundProxy = "";
   alice.config.settings.domain = "127.0.0.1";
   alice.config.settings.minSipPort = 55060;
   alice.config.settings.maxSipPort = 55060;
   
   alice.init();
   
   alice.enable();

   MediaInfo aliceAudio;
   MediaInfo aliceVideo;
   
   SippRunnerSettings sippRunnerSettings;
   sippRunnerSettings.sipListenPort = 50010;
   sippRunnerSettings.mediaPort = 50020;
   sippRunnerSettings.timeoutSec = 120;
   sippRunnerSettings.sipTargetPort = alice.config.settings.minSipPort;

   int expectedInviteResponseTimeMs = 0;
   if (withAuth)
   {
      if (with180)
      {
         sippRunnerSettings.scenarioFileName = "SippTests.InviteResponseTimeWithAuth.xml";
         expectedInviteResponseTimeMs = 500 + 500; // scenario has two 500 ms pauses
      }
      else
      {
         sippRunnerSettings.scenarioFileName = "SippTests.InviteResponseTimeWithAuthNo180.xml";
         expectedInviteResponseTimeMs = 500 + 500; // scenario has two 500 ms pauses
      }
   }
   else
   {
      if (with180)
      {
         sippRunnerSettings.scenarioFileName = "SippTests.InviteResponseTimeNoAuth.xml";
         expectedInviteResponseTimeMs = 500; // scenario has one 500 ms pause
      }
      else
      {
         sippRunnerSettings.scenarioFileName = "SippTests.InviteResponseTimeNoAuthNo180.xml";
         expectedInviteResponseTimeMs = 500; // scenario has one 500 ms pause
      }
   }
   
   SippRunner sippRunner(sippRunnerSettings);
   ASSERT_EQ(kSuccess, sippRunner.start());

   std::stringstream sippUriNoPort;
   sippUriNoPort << "sip:sip@127.0.0.1";
   std::stringstream sippUriWithPort;
   sippUriWithPort << sippUriNoPort.str() << ":" << sippRunnerSettings.sipListenPort;

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, sippUriWithPort.str().c_str());
   alice.conversation->start(aliceCall);
   
   const time_t callStartSec = time(0);

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      assertNewConversationOutgoingAudio_crypto(alice, aliceCall, sippUriNoPort.str().c_str(), aliceAudio, [](const NewConversationEvent& evt) {});
      assertConversationStateChanged_ex(alice, aliceCall, [&](const ConversationStateChangedEvent& evt)
      {
         ASSERT_EQ(with180 ? ConversationState_RemoteRinging : ConversationState_Connected, evt.conversationState);
         ASSERT_GE(evt.responseTimeMs, expectedInviteResponseTimeMs);
      });

      if (with180)
      {
         assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      }

      alice.conversation->end(aliceCall);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);

      // give time for sipp to response to BYE. is there a better way?
      std::this_thread::sleep_for(std::chrono::seconds(5));
      
      sippRunner.stop();
   });

   waitForMs(aliceEvents, std::chrono::minutes(3));
}

TEST_F(SippTests, InviteResponseTimeNoAuth)
{
   bool withAuthChallenge = false;
   bool with180 = true;
   SippTestsInviteResponseTime(withAuthChallenge, with180);
}

TEST_F(SippTests, InviteResponseTimeNoAuthNo180)
{
   bool withAuthChallenge = false;
   bool with180 = false;
   SippTestsInviteResponseTime(withAuthChallenge, with180);
}

TEST_F(SippTests, InviteResponseTimeWithAuth)
{
   bool withAuthChallenge = true;
   bool with180 = true;
   SippTestsInviteResponseTime(withAuthChallenge, with180);
}

TEST_F(SippTests, InviteResponseTimeWithAuthNo180)
{
   bool withAuthChallenge = true;
   bool with180 = false;
   SippTestsInviteResponseTime(withAuthChallenge, with180);
}

TEST_F(SippTests, UnRegister481)
{
   const int sippListenPort = 50010;

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.useRegistrar = true;
   alice.config.settings.domain = "irak.com.bd";
   alice.config.settings.outboundProxy = ("127.0.0.1:" + std::to_string(sippListenPort)).c_str();
   alice.config.settings.minSipPort = 55060;
   alice.config.settings.maxSipPort = 55060;
   
   alice.init();

   SippRunnerSettings sippRunnerSettings;
   sippRunnerSettings.sipListenPort = sippListenPort;
   sippRunnerSettings.mediaPort = 50020;
   sippRunnerSettings.timeoutSec = 120;
   sippRunnerSettings.sipTargetPort = alice.config.settings.minSipPort;
   sippRunnerSettings.scenarioFileName = "SippTests.UnRegister481.xml";

   SippRunner sippRunner(sippRunnerSettings);
   sippRunner.start();

   alice.enable();

   alice.disable(false, false);
   assertAccountDeregistering(alice);

   // expect Status_Unregistered w/ 481 response code
   CPCAPI2::SipAccount::SipAccountHandle h;
   CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
   ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged",
      __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt))<< "account is not deregistered";
   ASSERT_EQ(alice.handle, h)<< "account is not deregistered";
   ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Unregistered, evt.accountStatus);
   ASSERT_EQ(481, evt.signalingStatusCode);

   // expect no further account status events
   ASSERT_FALSE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged",
      __FILE__, 30000, CPCAPI2::test::AlwaysTruePred(), h, evt));
}

TEST_F(SippTests, IntermittentAuthChallenge)
{
   const int sippListenPort = 50010;

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.useRegistrar = true;
   alice.config.settings.domain = "irak.com.bd";
   alice.config.settings.outboundProxy = ("127.0.0.1:" + std::to_string(sippListenPort)).c_str();
   alice.config.settings.minSipPort = 55060;
   alice.config.settings.maxSipPort = 55060;
   
   alice.init();

   SippRunnerSettings sippRunnerSettings;
   sippRunnerSettings.sipListenPort = sippListenPort;
   sippRunnerSettings.mediaPort = 50020;
   sippRunnerSettings.timeoutSec = 120;
   sippRunnerSettings.sipTargetPort = alice.config.settings.minSipPort;
   sippRunnerSettings.scenarioFileName = "SippTests.IntermittentAuthChallenge.xml";

   SippRunner sippRunner(sippRunnerSettings);
   sippRunner.start();

   alice.enable();

   // sipp scenario has 5 separate 401 auth challenges interleaved -- should *not* trigger SDK to give up re-registering automatically
   SipAccountStatusChangedEvent evt;
   int waitTimeSec = 50;
   ASSERT_FALSE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged", __FILE__, waitTimeSec * 1000, CPCAPI2::test::AlwaysTruePred(), alice.handle, evt));
   
   alice.disable();
}

// SCORE-1363: Simulate registrar being slow to respond to un-REGISTER request; if the app tries to re-enable
// the account during this time, and the un-REGISTER request completes in less than 6 seconds (currently
// hard coded value in SipAccountImpl.cpp), the SDK will send out two separate REGISTER requests on re-enable (bad)
TEST_F(SippTests, DISABLED_DelayedShutdown)
{
   ReproHolder::destroyInstance();

   class MySipAccountAdornmentHandler : public SipAccountAdornmentHandler {
   public:
      MySipAccountAdornmentHandler() {}
      ~MySipAccountAdornmentHandler()
      {
      }

      virtual int onAccountAdornment(CPCAPI2::SipAccount::SipAccountHandle account, const SipAccountAdornmentEvent& args)
      {
         resip::Data data(args.message.c_str());
         resip::SipMessage* message = resip::SipMessage::make(data.c_str());

         if (message && message->isRequest() && (message->header(resip::h_RequestLine).method() == resip::REGISTER))
         {
            EXPECT_TRUE(message->exists(resip::h_CallId)) << " - Call-ID header not found";
            
            resip::CallID& callId(message->header(resip::h_CallId));

            std::lock_guard<std::mutex> lock(mMutex);
            mRegistrationCallIds.insert(callId.value().c_str());
         }

         return kSuccess;
      }

      std::mutex mMutex;
      std::set<std::string> mRegistrationCallIds;
   };

   const int sippListenPort = 50010;

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.useRegistrar = true;
   alice.config.settings.useRport = false;
   alice.config.settings.useOutbound = false;
   alice.config.settings.domain = "foo.bogus.net";
   alice.config.settings.outboundProxy = ("127.0.0.1:" + std::to_string(sippListenPort)).c_str();
   alice.config.settings.minSipPort = 55060;
   alice.config.settings.maxSipPort = 55060;
   
   alice.init();

   std::unique_ptr<MySipAccountAdornmentHandler> adornmentHandler(new MySipAccountAdornmentHandler());
   alice.account->setAdornmentHandler(alice.handle, adornmentHandler.get());

   SippRunnerSettings sippRunnerSettings;
   sippRunnerSettings.sipListenPort = sippListenPort;
   sippRunnerSettings.mediaPort = 50020;
   sippRunnerSettings.timeoutSec = 120;
   sippRunnerSettings.sipTargetPort = alice.config.settings.minSipPort;
   sippRunnerSettings.scenarioFileName = "RegisterDelayedUnregister.xml";

   SippRunner sippRunner(sippRunnerSettings);
   sippRunner.start();

   alice.enable(false);
   assertAccountRegistering(alice);
   assertAccountRegistered(alice);

   alice.disable(false, false);
   alice.enable(false);
   assertAccountDeregistering(alice);
   assertAccountDeregistered(alice);

   assertAccountRegistering(alice);
   
   sippRunner.stop();

   std::this_thread::sleep_for(std::chrono::seconds(6));

   sippRunner.start();

   assertAccountRegistered(alice);

   std::lock_guard<std::mutex> lock(adornmentHandler->mMutex);
   ASSERT_EQ(adornmentHandler->mRegistrationCallIds.size(), 2); // currently failing
}
#endif // #if CPCAPI2_SIPP_SUPPORTED
