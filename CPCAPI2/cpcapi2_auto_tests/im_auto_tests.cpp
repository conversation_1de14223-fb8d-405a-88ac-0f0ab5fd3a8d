#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#include "cpcapi2_test_fixture.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>

#include "test_framework/cpcapi2_test_framework.h"
#include "test_framework/bare_bones_sip_endpoint.h"

#include <thread>
#include <future>

using namespace CPCAPI2;
using namespace CPCAPI2::SipEvent;
using namespace CPCAPI2::SipInstantMessage;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::test;

namespace {

const std::string HiBob = "Hi Bob";
const std::string HelloBob = "Hello Bob";
const std::string HiAlice = "Hi Alice";
const std::string HelloAlice = "Hello Alice";

bool BadLogMessageCheckFunction(const char *message, CPCAPI2::LogLevel)
{
   int result = 0;
   if (NULL != strstr(message, HiBob.c_str())) result = true;
   if (NULL != strstr(message, HelloBob.c_str())) result = true;
   if (NULL != strstr(message, HiAlice.c_str())) result = true;
   if (NULL != strstr(message, HelloAlice.c_str())) result = true;
   return result;
}

class InstantMessageModuleTest : public CpcapiAutoTest
{
public:
   InstantMessageModuleTest() 
   {
      AutoTestsLogger::instance().setBadLogMessageCheckFunction(&BadLogMessageCheckFunction);
   }
   virtual ~InstantMessageModuleTest()
   {
      AutoTestsLogger::instance().setBadLogMessageCheckFunction(NULL);
   }

   static const bool checkDateTime;
};

const bool InstantMessageModuleTest::checkDateTime = true;

// test a basic message sent from alice to bob
TEST_F(InstantMessageModuleTest, BasicIM) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   auto aliceEvents = std::async(std::launch::async, [&] () {
      SipInstantMessageHandle msgHdl;
      SipAccountHandle hdl;
      { // send message to bob
         const std::string msg = HelloBob/*"Hello bob"*/;
         msgHdl = alice.im->sendMessage(alice.handle, bob.config.uri(), msg.c_str(), msg.size(), SipInstantMessageManager::MimeType_TextPlain);
      }
      { // expect success sending
         SipOutgoingInstantMessageEvent evt;
         ASSERT_TRUE(alice.imEvents->expectEvent( "SipInstantMessageHandler::onOutgoingInstantMessageSuccess",
            15000, HandleEqualsPred<SipAccountHandle>(alice.handle), hdl, evt));
         ASSERT_EQ(msgHdl, evt.im);
      }
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipAccountHandle hdl;
      { // expect to receive message
         SipIncomingInstantMessageEvent evt;
         ASSERT_TRUE(bob.imEvents->expectEvent( "SipInstantMessageHandler::onIncomingInstantMessage",
            15000, HandleEqualsPred<SipAccountHandle>(bob.handle), hdl, evt));
         ASSERT_EQ(evt.account, bob.handle);
         ASSERT_EQ(HelloBob.c_str()/*"Hello bob"*/, evt.content);
         ASSERT_EQ(evt.from.address, alice.config.uri());
         ASSERT_EQ(evt.to.address, bob.config.uri());
         ASSERT_EQ(evt.messageType, "");
         
         // delay answering so alice re-transmits; want to ensure retransmissions also
         // don't log full contents of the message
         std::this_thread::sleep_for(std::chrono::seconds(5));
         
         // accept the message so alice gets success
         ASSERT_EQ(kSuccess, bob.im->acceptIncoming(evt.im));
      }
   });

   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   // checks to make sure IM contents do not show up in our logging
   ASSERT_EQ(0, AutoTestsLogger::instance().getBadMessagesCount());
}

// test a message sent from alice to bob which is rejected
TEST_F(InstantMessageModuleTest, RejectIM) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   auto aliceEvents = std::async(std::launch::async, [&] () {
      SipInstantMessageHandle msgHdl;
      SipAccountHandle hdl;
      { // send message to bob
         const std::string msg = "Hello bob";
         msgHdl = alice.im->sendMessage(alice.handle, bob.config.uri(), msg.c_str(), msg.size(), SipInstantMessageManager::MimeType_TextPlain);
      }
      { // expect failure sending
         SipOutgoingInstantMessageEvent evt;
         ASSERT_TRUE(alice.imEvents->expectEvent( "SipInstantMessageHandler::onOutgoingInstantMessageFailure",
            15000, HandleEqualsPred<SipAccountHandle>(alice.handle), hdl, evt));
         ASSERT_EQ(msgHdl, evt.im);
         ASSERT_EQ("Bad Request", evt.signalingResponseText);
         ASSERT_EQ(400, evt.signalingStatusCode);
      }
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipAccountHandle hdl;
      { // expect to receive message
         SipIncomingInstantMessageEvent evt;
         ASSERT_TRUE(bob.imEvents->expectEvent( "SipInstantMessageHandler::onIncomingInstantMessage",
            15000, HandleEqualsPred<SipAccountHandle>(bob.handle), hdl, evt));
         ASSERT_EQ("Hello bob", evt.content);
         ASSERT_EQ(evt.from.address, alice.config.uri());
         ASSERT_EQ(evt.to.address, bob.config.uri());
         ASSERT_EQ(evt.messageType, "");
         // reject the message so alice gets failure
         ASSERT_EQ(kSuccess, bob.im->rejectIncoming(evt.im, 400, "reasonText is not used"));
      }
   });

   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
   ASSERT_EQ(0, AutoTestsLogger::instance().getBadMessagesCount());

}


// test a message sent with unexpected mime type from alice to bob
TEST_F(InstantMessageModuleTest, RejectMime) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   auto aliceEvents = std::async(std::launch::async, [&] () {
      SipInstantMessageHandle msgHdl;
      SipAccountHandle hdl;
      { // send message to bob
         const std::string msg = "[notevenXML]Hello bob[\\:crazyMadeUpMarkup]";
         msgHdl = alice.im->sendMessage(alice.handle, bob.config.uri(), msg.c_str(), msg.size(), SipInstantMessageManager::MimeType_TextHtml);
      }
      { // expect failure sending
         SipOutgoingInstantMessageEvent evt;
         ASSERT_TRUE(alice.imEvents->expectEvent( "SipInstantMessageHandler::onOutgoingInstantMessageFailure",
            15000, HandleEqualsPred<SipAccountHandle>(alice.handle), hdl, evt));
         ASSERT_EQ(msgHdl, evt.im);
         ASSERT_EQ(415, evt.signalingStatusCode);
         ASSERT_EQ("Unsupported Media Type", evt.signalingResponseText);
         ASSERT_EQ(1, evt.headers.acceptableMimeTypes.size());
         ASSERT_EQ("text/plain", evt.headers.acceptableMimeTypes[0]);
      }
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipAccountHandle hdl;
      { // expect to receive message
         SipIncomingInstantMessageEvent evt;
         ASSERT_TRUE(bob.imEvents->expectEvent( "SipInstantMessageHandler::onIncomingInstantMessage",
            15000, HandleEqualsPred<SipAccountHandle>(bob.handle), hdl, evt));
         //ASSERT_EQ("Hello bob", evt.content);
         ASSERT_EQ(evt.from.address, alice.config.uri());
         ASSERT_EQ(evt.to.address, bob.config.uri());
         ASSERT_EQ(evt.messageType, "");
         // reject the message so alice gets failure
         cpc::vector<cpc::string> acceptable;
         acceptable.push_back("text/plain");
         ASSERT_EQ(kSuccess, bob.im->rejectIncomingMimeType(evt.im, acceptable));
      }
   });

   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
   ASSERT_EQ(0, AutoTestsLogger::instance().getBadMessagesCount());
}

// test a message sent with unexpected mime type from alice to bob when bob has pre-prepared his acceptable mime types list
TEST_F(InstantMessageModuleTest, DISABLED_SdkRejectMime) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   auto aliceEvents = std::async(std::launch::async, [&] () {
      SipInstantMessageHandle msgHdl;
      SipAccountHandle hdl;
      { // send message to bob
         const std::string msg = "[notevenXML]Hello bob[\\:crazyMadeUpMarkup]";
         msgHdl = alice.im->sendMessage(alice.handle, bob.config.uri(), msg.c_str(), msg.size(), "application/crazyMarkup");
      }
      { // expect failure sending
         SipOutgoingInstantMessageEvent evt;
         ASSERT_TRUE(alice.imEvents->expectEvent( "SipInstantMessageHandler::onOutgoingInstantMessageFailure",
            15000, HandleEqualsPred<SipAccountHandle>(alice.handle), hdl, evt));
         ASSERT_EQ(msgHdl, evt.im);
         ASSERT_EQ(415, evt.signalingStatusCode);
         ASSERT_EQ("Unsupported Media Type", evt.signalingResponseText);
         ASSERT_EQ(2, evt.headers.acceptableMimeTypes.size());
         ASSERT_EQ("text/plain", evt.headers.acceptableMimeTypes[0]);
         ASSERT_EQ("text/html", evt.headers.acceptableMimeTypes[1]);
      }
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      {
         bob.im->acceptMimeType(bob.handle, SipInstantMessageManager::MimeType_TextPlain);
         bob.im->acceptMimeType(bob.handle, SipInstantMessageManager::MimeType_TextHtml);
      }
      // invalid message should be rejected before it leaves the sdk layer, bob should see no events
   });

   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
   ASSERT_EQ(0, AutoTestsLogger::instance().getBadMessagesCount());
}

// test the sending of a 'isComposing' notification from Alice to the wrong destination SIP address.
TEST_F(InstantMessageModuleTest, SetIsComposingMessageFailure) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   // Send the isComposing notification to the wrong address
   cpc::string wrongBobAddress = "sip:wrong_bob@" + bob.config.settings.domain;
   alice.im->setIsComposingMessage(alice.handle, wrongBobAddress, SipInstantMessageManager::MimeType_TextPlain);

   // Wait for the response (from Alice)
   SetIsComposingMessageFailureEvent evt;
   ASSERT_TRUE(cpcExpectEvent(alice.imEvents,
      "SipInstantMessageHandler::onSetIsComposingMessageFailure", 
      5000, HandleEqualsPred<SipAccountHandle>(alice.handle), alice.handle, evt));
   ASSERT_EQ(0, AutoTestsLogger::instance().getBadMessagesCount());
}

// test the successful sending of a 'isComposing' notification followed by a message from Alice to bob.
TEST_F(InstantMessageModuleTest, DISABLED_BasicIMWithIsComposingNotification) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   // Bob's thread
   auto bobEvents = std::async(std::launch::async, [&] () {

      {
         // Wait for the isComposing notification [Active] (from Alice)
         IsComposingMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.imEvents,
            "SipInstantMessageHandler::onIsComposingMessage", 
            5000, HandleEqualsPred<SipAccountHandle>(bob.handle), bob.handle, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Active);
         ASSERT_EQ(evt.mimeType, "text/plain");
         ASSERT_EQ(evt.from.address, alice.config.uri());
         ASSERT_EQ(evt.to.address, bob.config.uri());
      }
      
      {
         // Wait for the isComposing notification [Idle] (from Alice)
         IsComposingMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.imEvents,
            "SipInstantMessageHandler::onIsComposingMessage", 
            5000, HandleEqualsPred<SipAccountHandle>(bob.handle), bob.handle, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Idle);
         ASSERT_EQ(evt.mimeType, "text/plain");
         ASSERT_EQ(evt.from.address, alice.config.uri());
         ASSERT_EQ(evt.to.address, bob.config.uri());
      }

      {
         // Wait for the new message notification (from Alice)
         SipIncomingInstantMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.imEvents,
            "SipInstantMessageHandler::onIncomingInstantMessage", 
            15000, HandleEqualsPred<SipAccountHandle>(bob.handle), bob.handle, evt));
         ASSERT_EQ(evt.account, bob.handle);
         ASSERT_EQ(evt.mimeType, "text/plain");
         ASSERT_EQ(evt.content, "Hi Bob");
         ASSERT_EQ(evt.from.address, alice.config.uri());
         ASSERT_EQ(evt.to.address, bob.config.uri());
         ASSERT_EQ(evt.messageType, "");

         // Accept the message from Alice
         int ret = bob.im->acceptIncoming(evt.im);
         ASSERT_EQ(ret, kSuccess);
      }
   });

   // Alice's thread
   auto aliceEvents = std::async(std::launch::async, [&] () {
      // Notify Bob that Alice has started typing/composing text
      alice.im->setIsComposingMessage(alice.handle, bob.config.uri(), SipInstantMessageManager::MimeType_TextPlain);

      {
         // Wait for the response (from Alice)
         SetIsComposingMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.imEvents,
            "SipInstantMessageHandler::onSetIsComposingMessageSuccess", 
            5000, HandleEqualsPred<SipAccountHandle>(alice.handle), alice.handle, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Active);
      }

      // Send message to Bob
      const std::string msg = "Hi Bob";
      SipInstantMessageHandle newMessageHandle = alice.im->sendMessage(alice.handle, bob.config.uri(), msg.c_str(), msg.size(), SipInstantMessageManager::MimeType_TextPlain);

      { 
         // Wait for the response (from Alice)
         SipOutgoingInstantMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.imEvents,
            "SipInstantMessageHandler::onOutgoingInstantMessageSuccess", 
            15000, HandleEqualsPred<SipAccountHandle>(alice.handle), alice.handle, evt));
         ASSERT_EQ(evt.im, newMessageHandle);
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
   ASSERT_EQ(0, AutoTestsLogger::instance().getBadMessagesCount());
}

// test the successful sending of a 'isComposing' notification followed by a message from Alice to bob using a custom MIME type.
TEST_F(InstantMessageModuleTest, BasicIMWithIsComposingNotificationUsingCustomMimeType) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   // Bob's thread
   auto bobEvents = std::async(std::launch::async, [&] () {

      {
         // Wait for the isComposing notification [Active] (from Alice)
         IsComposingMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.imEvents,
            "SipInstantMessageHandler::onIsComposingMessage", 
            5000, HandleEqualsPred<SipAccountHandle>(bob.handle), bob.handle, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Active);
         ASSERT_EQ(evt.mimeType, "mytype/mysubtype");
         ASSERT_EQ(evt.from.address, alice.config.uri());
         ASSERT_EQ(evt.to.address, bob.config.uri());
      }
      
      {
         // Wait for the isComposing notification [Idle] (from Alice)
         IsComposingMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.imEvents,
            "SipInstantMessageHandler::onIsComposingMessage", 
            5000, HandleEqualsPred<SipAccountHandle>(bob.handle), bob.handle, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Idle);
         ASSERT_EQ(evt.mimeType, "mytype/mysubtype");
         ASSERT_EQ(evt.from.address, alice.config.uri());
         ASSERT_EQ(evt.to.address, bob.config.uri());
      }

      {
         // Wait for the new message notification (from Alice)
         SipIncomingInstantMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.imEvents,
            "SipInstantMessageHandler::onIncomingInstantMessage", 
            15000, HandleEqualsPred<SipAccountHandle>(bob.handle), bob.handle, evt));
         ASSERT_EQ(evt.account, bob.handle);
         ASSERT_EQ(evt.mimeType, "mytype/mysubtype");
         ASSERT_EQ("Hi Bob", evt.content);
         ASSERT_EQ(evt.from.address, alice.config.uri());
         ASSERT_EQ(evt.to.address, bob.config.uri());
         ASSERT_EQ(evt.messageType, "");

         // Accept the message from Alice
         int ret = bob.im->acceptIncoming(evt.im);
         ASSERT_EQ(ret, kSuccess);
      }
   });

   // Alice's thread
   auto aliceEvents = std::async(std::launch::async, [&] () {
      // Notify Bob that Alice has started typing/composing text
      alice.im->setIsComposingMessage(alice.handle, bob.config.uri(), "mytype/mysubtype");

      {
         // Wait for the response (from Alice)
         SetIsComposingMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.imEvents,
            "SipInstantMessageHandler::onSetIsComposingMessageSuccess", 
            5000, HandleEqualsPred<SipAccountHandle>(alice.handle), alice.handle, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Active);
      }

      // Send message to Bob
      const std::string msg = "Hi Bob";
      SipInstantMessageHandle newMessageHandle = alice.im->sendMessage(alice.handle, bob.config.uri(), msg.c_str(), msg.size(), "mytype/mysubtype");

      { 
         // Wait for the response (from Alice)
         SipOutgoingInstantMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.imEvents,
            "SipInstantMessageHandler::onOutgoingInstantMessageSuccess", 
            15000, HandleEqualsPred<SipAccountHandle>(alice.handle), alice.handle, evt));
         ASSERT_EQ(evt.im, newMessageHandle);
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
   ASSERT_EQ(0, AutoTestsLogger::instance().getBadMessagesCount());
}

// test the isComposing transition from Active to Idle.
TEST_F(InstantMessageModuleTest, DISABLED_IsComposingNotificationActiveToIdle) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   // Bob's thread
   auto bobEvents = std::async(std::launch::async, [&] () {
      {
         // Wait for the isComposing notification [Active] (from Alice)
         IsComposingMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.imEvents,
         "SipInstantMessageHandler::onIsComposingMessage", 
         5000, HandleEqualsPred<SipAccountHandle>(bob.handle), bob.handle, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Active);
         ASSERT_EQ(evt.mimeType, "text/plain");
         ASSERT_EQ(evt.from.address, alice.config.uri());
         ASSERT_EQ(evt.to.address, bob.config.uri());
      }

      {
         // Wait for the isComposing notification [Idle] (from Alice)
         IsComposingMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.imEvents,
         "SipInstantMessageHandler::onIsComposingMessage", 
         10000, HandleEqualsPred<SipAccountHandle>(bob.handle), bob.handle, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Idle);
         ASSERT_EQ(evt.mimeType, "text/plain");
         ASSERT_EQ(evt.from.address, alice.config.uri());
         ASSERT_EQ(evt.to.address, bob.config.uri());
      }
   });

   // Alice's thread
   auto aliceEvents = std::async(std::launch::async, [&] () {
      alice.im->setIsComposingMessage(alice.handle, bob.config.uri(), SipInstantMessageManager::MimeType_TextPlain, 0, 10, 5);

      {
         // Wait for the send success notification [Active] (from Alice)
         SetIsComposingMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.imEvents,
            "SipInstantMessageHandler::onSetIsComposingMessageSuccess", 
            10000, HandleEqualsPred<SipAccountHandle>(alice.handle), alice.handle, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Active);
      }

      // State will fall back to Idle after 5 seconds

      {
         // Wait for the send success notification [Idle]  (from Alice)
         SetIsComposingMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.imEvents,
            "SipInstantMessageHandler::onSetIsComposingMessageSuccess", 
            10000, HandleEqualsPred<SipAccountHandle>(alice.handle), alice.handle, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Idle);
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
   ASSERT_EQ(0, AutoTestsLogger::instance().getBadMessagesCount());
}

// test the isComposing refresh timer on the composer (Alice) side.
TEST_F(InstantMessageModuleTest, DISABLED_IsComposingNotificationComposerRefreshTimerTest) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   // Bob's thread
   auto bobEvents = std::async(std::launch::async, [&] () {
      {
         // Wait for the isComposing notification [Active] (from Alice)
         IsComposingMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.imEvents,
         "SipInstantMessageHandler::onIsComposingMessage", 
         5000, HandleEqualsPred<SipAccountHandle>(bob.handle), bob.handle, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Active);
         ASSERT_EQ(evt.mimeType, "text/plain");
         ASSERT_EQ(evt.from.address, alice.config.uri());
         ASSERT_EQ(evt.to.address, bob.config.uri());
      }

      {
         // Wait for the isComposing notification [Idle] (from Alice)
         IsComposingMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.imEvents,
         "SipInstantMessageHandler::onIsComposingMessage", 
         10000, HandleEqualsPred<SipAccountHandle>(bob.handle), bob.handle, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Idle);
         ASSERT_EQ(evt.mimeType, "text/plain");
         ASSERT_EQ(evt.from.address, alice.config.uri());
         ASSERT_EQ(evt.to.address, bob.config.uri());

      }
   });

   // Alice's thread
   auto aliceEvents = std::async(std::launch::async, [&] () {
      // Generate the sending of an Active state notification every 2 seconds
      alice.im->setIsComposingMessage(alice.handle, bob.config.uri(), SipInstantMessageManager::MimeType_TextPlain, 0, 2, 7);

      {
         // Wait for the send success notification [Active] - right away (from Alice)
         SetIsComposingMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.imEvents,
            "SipInstantMessageHandler::onSetIsComposingMessageSuccess", 
            5000, HandleEqualsPred<SipAccountHandle>(alice.handle), alice.handle, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Active);
      }

      {
         // Wait for the send success notification [Active] - 2nd sec (from Alice)
         SetIsComposingMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.imEvents,
            "SipInstantMessageHandler::onSetIsComposingMessageSuccess", 
            5000, HandleEqualsPred<SipAccountHandle>(alice.handle), alice.handle, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Active);
      }

      {
         // Wait for the send success notification [Active] - 4th sec (from Alice)
         SetIsComposingMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.imEvents,
            "SipInstantMessageHandler::onSetIsComposingMessageSuccess", 
            5000, HandleEqualsPred<SipAccountHandle>(alice.handle), alice.handle, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Active);
      }

      {
         // Wait for the send success notification [Active] - 6th sec (from Alice)
         SetIsComposingMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.imEvents,
            "SipInstantMessageHandler::onSetIsComposingMessageSuccess", 
            5000, HandleEqualsPred<SipAccountHandle>(alice.handle), alice.handle, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Active);
      }

      {
         // Wait for the send success notification [Idle] - 7th sec (from Alice)
         SetIsComposingMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.imEvents,
            "SipInstantMessageHandler::onSetIsComposingMessageSuccess", 
            5000, HandleEqualsPred<SipAccountHandle>(alice.handle), alice.handle, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Idle);
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
   ASSERT_EQ(0, AutoTestsLogger::instance().getBadMessagesCount());
}

// simulate typing on/off on Alice side and verify that the right count and sequence of isComposing notifications are received by bob.
TEST_F(InstantMessageModuleTest, DISABLED_IsComposingNotificationUserTyping) {
   long origTimezone = getTimezone();

   TestAccount alice("alice");
   TestAccount bob("bob");

   // Bob's thread
   auto bobEvents = std::async(std::launch::async, [&] () {
      // User is typing for a while, wait for the Active state notif then back to Idle

      {
         // Wait for the isComposing notification [Active] (from Alice)
         IsComposingMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.imEvents,
         "SipInstantMessageHandler::onIsComposingMessage", 
         10000, HandleEqualsPred<SipAccountHandle>(bob.handle), bob.handle, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Active);
         ASSERT_EQ(evt.mimeType, "text/plain");
         ASSERT_EQ(evt.from.address, alice.config.uri());
         ASSERT_EQ(evt.to.address, bob.config.uri());
         if (checkDateTime)
         {
            ASSERT_EQ(evt.lastActive.tm_year, 70);
            ASSERT_EQ(evt.lastActive.tm_mon, 0);
            ASSERT_EQ(evt.lastActive.tm_mday, 1);
            ASSERT_EQ(evt.lastActive.tm_hour, 0);
            ASSERT_EQ(evt.lastActive.tm_min, 20);
            ASSERT_EQ(evt.lastActive.tm_sec, 34);
         }
      }

      {
         // Wait for the isComposing notification [Idle] (from Alice)
         IsComposingMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.imEvents,
         "SipInstantMessageHandler::onIsComposingMessage", 
         10000, HandleEqualsPred<SipAccountHandle>(bob.handle), bob.handle, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Idle);
         ASSERT_EQ(evt.mimeType, "text/plain");
         ASSERT_EQ(evt.from.address, alice.config.uri());
         ASSERT_EQ(evt.to.address, bob.config.uri());
         if (checkDateTime)
         {
            ASSERT_EQ(evt.lastActive.tm_year, 70);
            ASSERT_EQ(evt.lastActive.tm_mon, 0);
            ASSERT_EQ(evt.lastActive.tm_mday, 1);
            ASSERT_EQ(evt.lastActive.tm_hour, 0);
            ASSERT_EQ(evt.lastActive.tm_min, 20);
            ASSERT_EQ(evt.lastActive.tm_sec, 38);
         }
      }

      // User is typing again for a short period, wait for the Active state notif then back to Idle

      {
         // Wait for the isComposing notification [Active] (from Alice)
         IsComposingMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.imEvents,
         "SipInstantMessageHandler::onIsComposingMessage", 
         10000, HandleEqualsPred<SipAccountHandle>(bob.handle), bob.handle, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Active);
         ASSERT_EQ(evt.mimeType, "text/plain");
         ASSERT_EQ(evt.from.address, alice.config.uri());
         ASSERT_EQ(evt.to.address, bob.config.uri());
         if (checkDateTime)
         {
            ASSERT_EQ(evt.lastActive.tm_year, 70);
            ASSERT_EQ(evt.lastActive.tm_mon, 0);
            ASSERT_EQ(evt.lastActive.tm_mday, 1);
            ASSERT_EQ(evt.lastActive.tm_hour, 0);
            ASSERT_EQ(evt.lastActive.tm_min, 20);
            ASSERT_EQ(evt.lastActive.tm_sec, 43);
         }
      }

      {
         // Wait for the isComposing notification [Idle] (from Alice)
         IsComposingMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.imEvents,
         "SipInstantMessageHandler::onIsComposingMessage", 
         10000, HandleEqualsPred<SipAccountHandle>(bob.handle), bob.handle, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Idle);
         ASSERT_EQ(evt.mimeType, "text/plain");
         ASSERT_EQ(evt.from.address, alice.config.uri());
         ASSERT_EQ(evt.to.address, bob.config.uri());
         if (checkDateTime)
         {
            ASSERT_EQ(evt.lastActive.tm_year, 70);
            ASSERT_EQ(evt.lastActive.tm_mon, 0);
            ASSERT_EQ(evt.lastActive.tm_mday, 1);
            ASSERT_EQ(evt.lastActive.tm_hour, 0);
            ASSERT_EQ(evt.lastActive.tm_min, 20);
            ASSERT_EQ(evt.lastActive.tm_sec, 43);
         }
      }
   });

   // Set the date/time used in the IsComposingNotifications
   struct tm isComposingDateTime;
   isComposingDateTime.tm_year = 70;
   isComposingDateTime.tm_mon = 0;
   isComposingDateTime.tm_mday = 1;
   isComposingDateTime.tm_hour = 0;
   isComposingDateTime.tm_min = 20;
   isComposingDateTime.tm_sec = 34;
   setTimezone(0); // Local: GMT

   // Alice's thread
   auto aliceEvents = std::async(std::launch::async, [&] () {
 
      // User is typing
      std::cout << "User is typing" << std::endl;
      alice.im->setIsComposingMessage(alice.handle, bob.config.uri(), SipInstantMessageManager::MimeType_TextPlain, &isComposingDateTime, 90, 5);

      {
         // Wait for the send success notification [Active]  (from Alice)
         SetIsComposingMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.imEvents,
            "SipInstantMessageHandler::onSetIsComposingMessageSuccess", 
            10000, HandleEqualsPred<SipAccountHandle>(alice.handle), alice.handle, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Active);
      }

      // Wait 1 second
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      isComposingDateTime.tm_sec += 1;

      // User is typing
      std::cout << "User is typing" << std::endl;
      alice.im->setIsComposingMessage(alice.handle, bob.config.uri(), SipInstantMessageManager::MimeType_TextPlain, &isComposingDateTime, 90, 5);

      // Wait 1 second
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      isComposingDateTime.tm_sec += 1;

      // User is typing
      std::cout << "User is typing" << std::endl;
      alice.im->setIsComposingMessage(alice.handle, bob.config.uri(), SipInstantMessageManager::MimeType_TextPlain, &isComposingDateTime, 90, 5);

      // Wait 1 second
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      isComposingDateTime.tm_sec += 1;

      // User is typing
      std::cout << "User is typing" << std::endl;
      alice.im->setIsComposingMessage(alice.handle, bob.config.uri(), SipInstantMessageManager::MimeType_TextPlain, &isComposingDateTime, 90, 5);

      // Wait 1 second
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      isComposingDateTime.tm_sec += 1;

      // User is typing
      std::cout << "User is typing" << std::endl;
      alice.im->setIsComposingMessage(alice.handle, bob.config.uri(), SipInstantMessageManager::MimeType_TextPlain, &isComposingDateTime, 90, 5);

      // User has stopped typing
      std::cout << "User stopped typing" << std::endl;

      {
         // Wait for the send success notification [Idle] (from Alice)
         SetIsComposingMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.imEvents,
            "SipInstantMessageHandler::onSetIsComposingMessageSuccess", 
            10000, HandleEqualsPred<SipAccountHandle>(alice.handle), alice.handle, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Idle);
      }

      // User started typing again, 5 seconds later
      isComposingDateTime.tm_sec += 5;
      std::cout << "User is typing again" << std::endl;
      alice.im->setIsComposingMessage(alice.handle, bob.config.uri(), SipInstantMessageManager::MimeType_TextPlain, &isComposingDateTime, 90, 5);

      {
         // Wait for the send success notification [Active]  (from Alice)
         SetIsComposingMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.imEvents,
            "SipInstantMessageHandler::onSetIsComposingMessageSuccess", 
            10000, HandleEqualsPred<SipAccountHandle>(alice.handle), alice.handle, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Active);
      }

      // User has stopped typing
      std::cout << "User stopped typing" << std::endl;

      {
         // Wait for the send success notification [Idle] (from Alice)
         SetIsComposingMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.imEvents,
            "SipInstantMessageHandler::onSetIsComposingMessageSuccess", 
            10000, HandleEqualsPred<SipAccountHandle>(alice.handle), alice.handle, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Idle);
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
   ASSERT_EQ(0, AutoTestsLogger::instance().getBadMessagesCount());

   setTimezone(origTimezone);
}

// simulate both users starting to type around the same time, and then composing states going to idle
TEST_F(InstantMessageModuleTest, DISABLED_IsComposingNotificationBothUsersTypingThenIdle) {

   TestAccount alice("alice");
   TestAccount bob("bob");

   // Bob's thread
   auto bobEvents = std::async(std::launch::async, [&] () {
      
      {
         // User is typing
         std::cout << "bob is typing" << std::endl;
         bob.im->setIsComposingMessage(bob.handle, alice.config.uri(), SipInstantMessageManager::MimeType_TextPlain, NULL, 90, 15);

         {
            // Wait for the send success notification [Active]  (from Bob)
            SetIsComposingMessageSuccessEvent evt;
            ASSERT_TRUE(cpcExpectEvent(bob.imEvents,
               "SipInstantMessageHandler::onSetIsComposingMessageSuccess", 
               10000, HandleEqualsPred<SipAccountHandle>(bob.handle), bob.handle, evt));
            ASSERT_EQ(evt.state, IsComposingMessageState_Active);
         }

         // Wait for the isComposing notification [Active] (from Alice)
         IsComposingMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.imEvents, "SipInstantMessageHandler::onIsComposingMessage",
         10000, HandleEqualsPred<SipAccountHandle>(bob.handle), bob.handle, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Active);
         ASSERT_EQ(evt.mimeType, "text/plain");
         ASSERT_EQ(evt.from.address, alice.config.uri());
         ASSERT_EQ(evt.to.address, bob.config.uri());
      }

      std::cout << "Waiting for idle iscomposing notification from Alice" << std::endl;

      {
         // Wait for the isComposing notification [Idle] (from Alice)
         IsComposingMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.imEvents,
         "SipInstantMessageHandler::onIsComposingMessage", 
         20000, HandleEqualsPred<SipAccountHandle>(bob.handle), bob.handle, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Idle);
         ASSERT_EQ(evt.mimeType, "text/plain");
         ASSERT_EQ(evt.from.address, alice.config.uri());
         ASSERT_EQ(evt.to.address, bob.config.uri());
      }
   });



   // Alice's thread
   auto aliceEvents = std::async(std::launch::async, [&] () {

      // User is typing
      std::cout << "alice is typing" << std::endl;
      alice.im->setIsComposingMessage(alice.handle, bob.config.uri(), SipInstantMessageManager::MimeType_TextPlain, NULL, 90, 15);

      {
         // Wait for the send success notification [Active]  (from Alice)
         SetIsComposingMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.imEvents,
            "SipInstantMessageHandler::onSetIsComposingMessageSuccess", 
            10000, HandleEqualsPred<SipAccountHandle>(alice.handle), alice.handle, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Active);
      }

      {
         // Wait for the isComposing notification [Active] (from Bob)
         IsComposingMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.imEvents,
         "SipInstantMessageHandler::onIsComposingMessage", 
         10000, HandleEqualsPred<SipAccountHandle>(alice.handle), alice.handle, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Active);
         ASSERT_EQ(evt.mimeType, "text/plain");
         ASSERT_EQ(evt.from.address, bob.config.uri());
         ASSERT_EQ(evt.to.address, alice.config.uri());
      }

      std::cout << "Waiting for idle iscomposing notification from Bob" << std::endl;

      {
         // Wait for the isComposing notification [Idle] (from Alice)
         IsComposingMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.imEvents,
         "SipInstantMessageHandler::onIsComposingMessage", 
         20000, HandleEqualsPred<SipAccountHandle>(alice.handle), alice.handle, evt));
         ASSERT_EQ(evt.state, IsComposingMessageState_Idle);
         ASSERT_EQ(evt.mimeType, "text/plain");
         ASSERT_EQ(evt.from.address, bob.config.uri());
         ASSERT_EQ(evt.to.address, alice.config.uri());
      }

   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
   ASSERT_EQ(0, AutoTestsLogger::instance().getBadMessagesCount());
}

// test a basic message sent from alice to bob specifying a messageType.
TEST_F(InstantMessageModuleTest, BasicIMUsingMessageType) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   auto aliceEvents = std::async(std::launch::async, [&] () {
      SipInstantMessageHandle msgHdl;
      SipAccountHandle hdl;
      { // send message to bob
         const std::string msg = "Hello bob";
         msgHdl = alice.im->sendMessage(alice.handle, bob.config.uri(), msg.c_str(), msg.size(), SipInstantMessageManager::MimeType_TextPlain, "IM");
      }
      { // expect success sending
         SipOutgoingInstantMessageEvent evt;
         ASSERT_TRUE(alice.imEvents->expectEvent( "SipInstantMessageHandler::onOutgoingInstantMessageSuccess",
            15000, HandleEqualsPred<SipAccountHandle>(alice.handle), hdl, evt));
         ASSERT_EQ(msgHdl, evt.im);
      }
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipAccountHandle hdl;
      { // expect to receive message
         SipIncomingInstantMessageEvent evt;
         ASSERT_TRUE(bob.imEvents->expectEvent( "SipInstantMessageHandler::onIncomingInstantMessage",
            15000, HandleEqualsPred<SipAccountHandle>(bob.handle), hdl, evt));
         ASSERT_EQ(evt.account, bob.handle);
         ASSERT_EQ("Hello bob", evt.content);
         ASSERT_EQ(evt.from.address, alice.config.uri());
         ASSERT_EQ(evt.to.address, bob.config.uri());
         ASSERT_EQ(evt.messageType, "IM");
         // accept the message so alice gets success
         ASSERT_EQ(kSuccess, bob.im->acceptIncoming(evt.im));
      }
   });

   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
   ASSERT_EQ(0, AutoTestsLogger::instance().getBadMessagesCount());
}

// test a basic message sent from alice to bob specifying a custom MIME and a messageType.
TEST_F(InstantMessageModuleTest, BasicIMUsingCustomMimeTypeAndMessageType) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   auto aliceEvents = std::async(std::launch::async, [&] () {
      SipInstantMessageHandle msgHdl;
      SipAccountHandle hdl;
      { // send message to bob
         const std::string msg = "Hello bob";
         msgHdl = alice.im->sendMessage(alice.handle, bob.config.uri(), msg.c_str(), msg.size(), "mytype/mysubtype", "IM");
      }
      { // expect success sending
         SipOutgoingInstantMessageEvent evt;
         ASSERT_TRUE(alice.imEvents->expectEvent( "SipInstantMessageHandler::onOutgoingInstantMessageSuccess",
            15000, HandleEqualsPred<SipAccountHandle>(alice.handle), hdl, evt));
         ASSERT_EQ(msgHdl, evt.im);
      }
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipAccountHandle hdl;
      { // expect to receive message
         SipIncomingInstantMessageEvent evt;
         ASSERT_TRUE(bob.imEvents->expectEvent( "SipInstantMessageHandler::onIncomingInstantMessage",
            15000, HandleEqualsPred<SipAccountHandle>(bob.handle), hdl, evt));
         ASSERT_EQ(evt.account, bob.handle);
         ASSERT_EQ("Hello bob", evt.content);
         ASSERT_EQ(evt.from.address, alice.config.uri());
         ASSERT_EQ(evt.to.address, bob.config.uri());
         ASSERT_EQ(evt.messageType, "IM");
         // accept the message so alice gets success
         ASSERT_EQ(kSuccess, bob.im->acceptIncoming(evt.im));
      }
   });

   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
   ASSERT_EQ(0, AutoTestsLogger::instance().getBadMessagesCount());
}

// test a basic message sent from alice to bob. Set up accounts to use STUN server.
TEST_F(InstantMessageModuleTest, DISABLED_BasicIMUsingStun) {
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.useRport = false;
   alice.config.settings.useOutbound = false;
   alice.config.settings.stunServerSource = StunServerSource_Custom;
   alice.config.settings.stunServer = "stun.counterpath.com";
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.enable();

   TestAccount bob("bob", Account_NoInit);
   bob.config.settings.useRport = false;
   bob.config.settings.useOutbound = false;
   bob.config.settings.stunServerSource = StunServerSource_Custom;
   bob.config.settings.stunServer = "stun.counterpath.com";
   bob.config.settings.sipTransportType = SipAccountTransport_UDP;
   bob.enable();

   auto aliceEvents = std::async(std::launch::async, [&] () {
      SipInstantMessageHandle msgHdl;
      SipAccountHandle hdl;
      { // send message to bob
         const std::string msg = "Hello bob";
         msgHdl = alice.im->sendMessage(alice.handle, bob.config.uri(), msg.c_str(), msg.size(), SipInstantMessageManager::MimeType_TextPlain);
      }
      { // expect success sending
         SipOutgoingInstantMessageEvent evt;
         ASSERT_TRUE(alice.imEvents->expectEvent( "SipInstantMessageHandler::onOutgoingInstantMessageSuccess",
            15000, HandleEqualsPred<SipAccountHandle>(alice.handle), hdl, evt));
         ASSERT_EQ(msgHdl, evt.im);
      }
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipAccountHandle hdl;
      { // expect to receive message
         SipIncomingInstantMessageEvent evt;
         ASSERT_TRUE(bob.imEvents->expectEvent( "SipInstantMessageHandler::onIncomingInstantMessage",
            15000, HandleEqualsPred<SipAccountHandle>(bob.handle), hdl, evt));
         ASSERT_EQ(evt.account, bob.handle);
         ASSERT_EQ("Hello bob", evt.content);
         ASSERT_EQ(evt.from.address, alice.config.uri());
         ASSERT_EQ(evt.to.address, bob.config.uri());
         ASSERT_EQ(evt.messageType, "");
         // accept the message so alice gets success
         ASSERT_EQ(kSuccess, bob.im->acceptIncoming(evt.im));
      }
   });

   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
   ASSERT_EQ(0, AutoTestsLogger::instance().getBadMessagesCount());
}

// test a basic message sent from alice to bob. Set up accounts to use STUN server via DNS SRV.
TEST_F(InstantMessageModuleTest, DISABLED_BasicIMUsingStunViaDnsSrv) {
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.useRport = false;
   alice.config.settings.useOutbound = false;
   alice.config.settings.stunServerSource = StunServerSource_SRV;
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.enable();

   TestAccount bob("bob", Account_NoInit);
   bob.config.settings.useRport = false;
   bob.config.settings.useOutbound = false;
   bob.config.settings.stunServerSource = StunServerSource_SRV;
   bob.config.settings.sipTransportType = SipAccountTransport_UDP;
   bob.enable();

   auto aliceEvents = std::async(std::launch::async, [&] () {
      SipInstantMessageHandle msgHdl;
      SipAccountHandle hdl;
      { // send message to bob
         const std::string msg = "Hello bob";
         msgHdl = alice.im->sendMessage(alice.handle, bob.config.uri(), msg.c_str(), msg.size(), SipInstantMessageManager::MimeType_TextPlain);
      }
      { // expect success sending
         SipOutgoingInstantMessageEvent evt;
         ASSERT_TRUE(alice.imEvents->expectEvent( "SipInstantMessageHandler::onOutgoingInstantMessageSuccess",
            15000, HandleEqualsPred<SipAccountHandle>(alice.handle), hdl, evt));
         ASSERT_EQ(msgHdl, evt.im);
      }
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipAccountHandle hdl;
      { // expect to receive message
         SipIncomingInstantMessageEvent evt;
         ASSERT_TRUE(bob.imEvents->expectEvent( "SipInstantMessageHandler::onIncomingInstantMessage",
            15000, HandleEqualsPred<SipAccountHandle>(bob.handle), hdl, evt));
         ASSERT_EQ(evt.account, bob.handle);
         ASSERT_EQ("Hello bob", evt.content);
         ASSERT_EQ(evt.from.address, alice.config.uri());
         ASSERT_EQ(evt.to.address, bob.config.uri());
         ASSERT_EQ(evt.messageType, "");
         // accept the message so alice gets success
         ASSERT_EQ(kSuccess, bob.im->acceptIncoming(evt.im));
      }
   });

   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
   ASSERT_EQ(0, AutoTestsLogger::instance().getBadMessagesCount());
}


TEST_F(InstantMessageModuleTest, DISABLED_AccountDisableThenIM) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   auto aliceEvents = std::async(std::launch::async, [&] () {
      SipInstantMessageHandle msgHdl;
      SipAccountHandle hdl;
      { // send message to bob
         const std::string msg = "Hello bob";
         msgHdl = alice.im->sendMessage(alice.handle, bob.config.uri(), msg.c_str(), msg.size(), SipInstantMessageManager::MimeType_TextPlain);
      }
      { // expect success sending
         SipOutgoingInstantMessageEvent evt;
         ASSERT_TRUE(alice.imEvents->expectEvent( "SipInstantMessageHandler::onOutgoingInstantMessageSuccess",
            15000, HandleEqualsPred<SipAccountHandle>(alice.handle), hdl, evt));
         ASSERT_EQ(msgHdl, evt.im);
      }

      alice.disable();

      { // send message to bob
         const std::string msg = "Hello bob 2";
         msgHdl = alice.im->sendMessage(alice.handle, bob.config.uri(), msg.c_str(), msg.size(), SipInstantMessageManager::MimeType_TextPlain);
      }
      { // expect failure sending
         SipOutgoingInstantMessageEvent evt;
         ASSERT_TRUE(alice.imEvents->expectEvent( "SipInstantMessageHandler::onOutgoingInstantMessageFailure",
            15000, HandleEqualsPred<SipAccountHandle>(alice.handle), hdl, evt));
         ASSERT_EQ(msgHdl, evt.im);
      }
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipAccountHandle hdl;
      { // expect to receive message
         SipIncomingInstantMessageEvent evt;
         ASSERT_TRUE(bob.imEvents->expectEvent( "SipInstantMessageHandler::onIncomingInstantMessage",
            15000, HandleEqualsPred<SipAccountHandle>(bob.handle), hdl, evt));
         ASSERT_EQ(evt.account, bob.handle);
         ASSERT_EQ("Hello bob", evt.content);
         ASSERT_EQ(evt.from.address, alice.config.uri());
         ASSERT_EQ(evt.to.address, bob.config.uri());
         ASSERT_EQ(evt.messageType, "");
         // accept the message so alice gets success
         ASSERT_EQ(kSuccess, bob.im->acceptIncoming(evt.im));
      }
   });

   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
   ASSERT_EQ(0, AutoTestsLogger::instance().getBadMessagesCount());
}

// test a lot of messages sent from alice to bob
TEST_F(InstantMessageModuleTest, DISABLED_MassiveIM) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   const auto count = 256;

   auto aliceEvents = std::async(std::launch::async, [&] () {
      for (auto i = 0; i < count; ++i)
      {
         SipInstantMessageHandle msgHdl;
         SipAccountHandle hdl;
         { // send message to bob
            const std::string msg = "Hello bob";
            msgHdl = alice.im->sendMessage(alice.handle, bob.config.uri(), msg.c_str(), msg.size(), SipInstantMessageManager::MimeType_TextPlain);
         }
         { // expect success sending
            SipOutgoingInstantMessageEvent evt;
            ASSERT_TRUE(alice.imEvents->expectEvent( "SipInstantMessageHandler::onOutgoingInstantMessageSuccess",
               15000, HandleEqualsPred<SipAccountHandle>(alice.handle), hdl, evt));
            ASSERT_EQ(msgHdl, evt.im);
         }
      }
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      for (auto i = 0; i < count; ++i)
      {
         SipAccountHandle hdl;
         { // expect to receive message
            SipIncomingInstantMessageEvent evt;
            ASSERT_TRUE(bob.imEvents->expectEvent( "SipInstantMessageHandler::onIncomingInstantMessage",
               15000, HandleEqualsPred<SipAccountHandle>(bob.handle), hdl, evt));
            ASSERT_EQ(evt.account, bob.handle);
            ASSERT_EQ("Hello bob", evt.content);
            ASSERT_EQ(evt.from.address, alice.config.uri());
            ASSERT_EQ(evt.to.address, bob.config.uri());
            ASSERT_EQ(evt.messageType, "");
            // accept the message so alice gets success
            ASSERT_EQ(kSuccess, bob.im->acceptIncoming(evt.im));
         }
      }
   });

   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(150000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(150000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
   ASSERT_EQ(0, AutoTestsLogger::instance().getBadMessagesCount());
}
   
TEST_F(InstantMessageModuleTest, IMRemoveHandler) {
   Phone* phone = Phone::create();
   phone->initialize(LicenseInfo(), (PhoneHandler*)NULL);
   
   CPCAPI2::SipAccount::SipAccountManager* acct = CPCAPI2::SipAccount::SipAccountManager::getInterface(phone);
   
   TestAccountConfig config("alice");
   CPCAPI2::SipAccount::SipAccountHandle accountHandle = acct->create(config.settings);
   
   class MySipInstantMessageHandler : public SipInstantMessageHandler
   {
   public:
      MySipInstantMessageHandler() : receivedEvent(false) {}
      
      bool receivedEvent;
      
      int onIncomingInstantMessage(CPCAPI2::SipAccount::SipAccountHandle account, const SipIncomingInstantMessageEvent& args) { return kSuccess; }
      int onError(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipInstantMessage::ErrorEvent& args) { return kSuccess; }
      int onOutgoingInstantMessageFailure(CPCAPI2::SipAccount::SipAccountHandle account, const SipOutgoingInstantMessageEvent& args) { receivedEvent = true; return kSuccess; }
   };
   
   
   MySipInstantMessageHandler* handler = new MySipInstantMessageHandler();
   SipInstantMessageManager* imMgr = SipInstantMessageManager::getInterface(phone);
   imMgr->setHandler(accountHandle, handler);
   acct->enable(accountHandle);

   imMgr->sendMessage(accountHandle, "sip:bogus238234982", "0", 1, SipInstantMessageManager::MimeType_TextPlain);
   
   std::atomic_bool threadStopFlag(false);
   auto start = std::chrono::high_resolution_clock::now();
   auto imEvent = std::async(std::launch::async, [&] ()
   {
      while (handler->receivedEvent == false)
      {
        phone->process(CPCAPI2::Phone::kBlockingModeNonBlocking);

        if (threadStopFlag) return;

        std::this_thread::sleep_for(std::chrono::milliseconds(100));
      }
   });
   
   flaggableWaitFor(imEvent, threadStopFlag);
   auto end = std::chrono::high_resolution_clock::now();
   
   imMgr->setHandler(accountHandle, NULL);
   delete handler;
   
   imMgr->sendMessage(accountHandle, "sip:bogus238234982", "0", 1, SipInstantMessageManager::MimeType_TextPlain);
   
   // wait about as long as it took before
   std::this_thread::sleep_for((end-start) * 2);
   
   phone->process(CPCAPI2::Phone::kBlockingModeNonBlocking);
   
   Phone::release(phone);
}
   
   
// Bob sends an IM to alice, which is accepted. Alice them sets its IM handler to NULL, and Bob sends another
// message to Alice, which is auto rejected by the SDK
TEST_F(InstantMessageModuleTest, IMNoHandler) {
  
   Phone* alicePhone = Phone::create();
   alicePhone->initialize(LicenseInfo(), (PhoneHandler*)NULL);
   
   CPCAPI2::SipAccount::SipAccountManager* aliceAcct = CPCAPI2::SipAccount::SipAccountManager::getInterface(alicePhone);
   
   TestAccountConfig aliceConfig("alice");
   TestAccount bob("bob");
   CPCAPI2::SipAccount::SipAccountHandle aliceAccountHandle = aliceAcct->create(aliceConfig.settings);
   
   class MySipInstantMessageHandler : public SipInstantMessageHandler
   {
   public:
      MySipInstantMessageHandler(Phone* phone) : mReceivedCallback(false), mPhone(phone) {}
      
      bool mReceivedCallback;
      Phone* mPhone;
      
      int onIncomingInstantMessage(CPCAPI2::SipAccount::SipAccountHandle account, const SipIncomingInstantMessageEvent& args)
      {
         SipInstantMessageManager::getInterface(mPhone)->acceptIncoming(args.im);
         mReceivedCallback = true;
         return kSuccess;
      }
      int onError(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipInstantMessage::ErrorEvent& args) { return kSuccess; }
      int onOutgoingInstantMessageFailure(CPCAPI2::SipAccount::SipAccountHandle account, const SipOutgoingInstantMessageEvent& args) { return kSuccess; }
   };
   
   class MySipAccountHandler : public SipAccountHandler {
   public:
      MySipAccountHandler() : mRegistered(false) {}
      ~MySipAccountHandler()
      {
      }
      
      virtual int onAccountStatusChanged(CPCAPI2::SipAccount::SipAccountHandle account, const SipAccountStatusChangedEvent& args)
      {
         if (args.accountStatus == SipAccountStatusChangedEvent::Status_Registered)
         {
            mRegistered = true;
         }
         return kSuccess;
      }
      virtual int onError(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::ErrorEvent& args){ return kSuccess; }
      virtual int onLicensingError(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::LicensingErrorEvent& args){ return kSuccess; }
      
      bool mRegistered;
   };
   
   std::unique_ptr<MySipAccountHandler> aliceAcctHandler(new MySipAccountHandler());
   aliceAcct->setHandler(aliceAccountHandle, aliceAcctHandler.get());
   aliceAcct->enable(aliceAccountHandle);
   
   std::atomic_bool threadStopFlag(false);
   auto aliceRegisterEvent = std::async(std::launch::async, [&]()
   {
      while (aliceAcctHandler->mRegistered == false)
      {
         alicePhone->process(CPCAPI2::Phone::kBlockingModeNonBlocking);

         if (threadStopFlag) break;

         std::this_thread::sleep_for(std::chrono::milliseconds(100));
      }
   });
   flaggableWaitFor(aliceRegisterEvent, threadStopFlag);
   
   const std::string msg = "Hello alice";
   SipInstantMessageHandle failureImHandle = bob.im->sendMessage(aliceAccountHandle, aliceConfig.uri(), msg.c_str(), msg.size(), SipInstantMessageManager::MimeType_TextPlain);

   auto bobEvents = std::async(std::launch::async, [&]() {
      SipAccountHandle hdl;
      { // expect message send failure, since Alice's IM handler was never set
         SipOutgoingInstantMessageEvent evt;
         ASSERT_TRUE(bob.imEvents->expectEvent("SipInstantMessageHandler::onOutgoingInstantMessageFailure",
                                             15000, HandleEqualsPred<SipAccountHandle>(bob.handle), hdl, evt));
         ASSERT_EQ(failureImHandle, evt.im);
         ASSERT_EQ(405, evt.signalingStatusCode);
      }
   });
   
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   
   Phone::release(alicePhone);
}


// Bob sends an IM to alice, which is accepted. Alice them sets its IM handler to NULL, and Bob sends another 
// message to Alice, which is auto rejected by the SDK
TEST_F(InstantMessageModuleTest, IMRemoveHandler2) {
    Phone* alicePhone = Phone::create();
    alicePhone->initialize(LicenseInfo(), (PhoneHandler*)NULL);

    CPCAPI2::SipAccount::SipAccountManager* aliceAcct = CPCAPI2::SipAccount::SipAccountManager::getInterface(alicePhone);

    TestAccountConfig aliceConfig("alice");
    TestAccount bob("bob");
    CPCAPI2::SipAccount::SipAccountHandle aliceAccountHandle = aliceAcct->create(aliceConfig.settings);

    class MySipInstantMessageHandler : public SipInstantMessageHandler
    {
    public:
        MySipInstantMessageHandler(Phone* phone) : mReceivedCallback(false), mPhone(phone) {}

        bool mReceivedCallback;
        Phone* mPhone;

        int onIncomingInstantMessage(CPCAPI2::SipAccount::SipAccountHandle account, const SipIncomingInstantMessageEvent& args) 
        { 
            SipInstantMessageManager::getInterface(mPhone)->acceptIncoming(args.im);
            mReceivedCallback = true;
            return kSuccess; 
        }
        int onError(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipInstantMessage::ErrorEvent& args) { return kSuccess; }
        int onOutgoingInstantMessageFailure(CPCAPI2::SipAccount::SipAccountHandle account, const SipOutgoingInstantMessageEvent& args) { return kSuccess; }
    };

    class MySipAccountHandler : public SipAccountHandler {
    public:
        MySipAccountHandler() : mRegistered(false) {}
        ~MySipAccountHandler()
        {
        }

        virtual int onAccountStatusChanged(CPCAPI2::SipAccount::SipAccountHandle account, const SipAccountStatusChangedEvent& args)
        {
            if (args.accountStatus == SipAccountStatusChangedEvent::Status_Registered)
            {
                mRegistered = true;
            }
            return kSuccess;
        }
        virtual int onError(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::ErrorEvent& args){ return kSuccess; }
        virtual int onLicensingError(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::LicensingErrorEvent& args){ return kSuccess; }

        bool mRegistered;
    };

    MySipInstantMessageHandler* aliceImHandler = new MySipInstantMessageHandler(alicePhone);
    SipInstantMessageManager* aliceImMgr = SipInstantMessageManager::getInterface(alicePhone);
    aliceImMgr->setHandler(aliceAccountHandle, aliceImHandler);
    
    std::unique_ptr<MySipAccountHandler> aliceAcctHandler(new MySipAccountHandler());
    aliceAcct->setHandler(aliceAccountHandle, aliceAcctHandler.get());
    aliceAcct->enable(aliceAccountHandle);

    std::atomic_bool threadStopFlag(false);
    auto aliceRegisterEvent = std::async(std::launch::async, [&]()
    {
        while (aliceAcctHandler->mRegistered == false)
        {
            alicePhone->process(CPCAPI2::Phone::kBlockingModeNonBlocking);

            if (threadStopFlag) return;

            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    });
    flaggableWaitFor(aliceRegisterEvent, threadStopFlag);

    const std::string msg = "Hello alice";
    SipInstantMessageHandle successImHandle = bob.im->sendMessage(aliceAccountHandle, aliceConfig.uri(), msg.c_str(), msg.size(), SipInstantMessageManager::MimeType_TextPlain);

    threadStopFlag = false;
    auto start = std::chrono::high_resolution_clock::now();
    auto imEvent = std::async(std::launch::async, [&]()
    {
        while (aliceImHandler->mReceivedCallback == false)
        {
            alicePhone->process(CPCAPI2::Phone::kBlockingModeNonBlocking);

            if (threadStopFlag) return;

            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    });

    flaggableWaitFor(imEvent, threadStopFlag);
    auto end = std::chrono::high_resolution_clock::now();

    auto bobEvents = std::async(std::launch::async, [&]() {
        SipAccountHandle hdl;
        { // expect message send success
            SipOutgoingInstantMessageEvent evt;
            ASSERT_TRUE(bob.imEvents->expectEvent("SipInstantMessageHandler::onOutgoingInstantMessageSuccess",
                15000, HandleEqualsPred<SipAccountHandle>(bob.handle), hdl, evt));
            ASSERT_EQ(evt.im, successImHandle);
        }
    });

    ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
    ASSERT_NO_THROW(bobEvents.get());

    aliceImMgr->setHandler(aliceAccountHandle, NULL);
   
    aliceAcct->disable(aliceAccountHandle);
    aliceAcctHandler->mRegistered = false;
    aliceAcct->enable(aliceAccountHandle);
   
    threadStopFlag = false;
    aliceRegisterEvent = std::async(std::launch::async, [&]()
    {
       while (aliceAcctHandler->mRegistered == false)
       {
          alicePhone->process(CPCAPI2::Phone::kBlockingModeNonBlocking);

          if (threadStopFlag) return;

          std::this_thread::sleep_for(std::chrono::milliseconds(100));
       }
    });
    flaggableWaitFor(aliceRegisterEvent, threadStopFlag);

    SipInstantMessageHandle failureImHandle = bob.im->sendMessage(aliceAccountHandle, aliceConfig.uri(), msg.c_str(), msg.size(), SipInstantMessageManager::MimeType_TextPlain);

    // wait about as long as it took before
    std::this_thread::sleep_for((end - start) * 2);

    // just fishing for no crashes here (i.e. SDK should not be sending a callback to our deleted handler)
    alicePhone->process(CPCAPI2::Phone::kBlockingModeNonBlocking);

    bobEvents = std::async(std::launch::async, [&]() {
        SipAccountHandle hdl;
        { // expect message send failure since Alice's handler was set to NULL
            SipOutgoingInstantMessageEvent evt;
            ASSERT_TRUE(bob.imEvents->expectEvent("SipInstantMessageHandler::onOutgoingInstantMessageFailure",
                15000, HandleEqualsPred<SipAccountHandle>(bob.handle), hdl, evt));
            ASSERT_EQ(failureImHandle, evt.im);
            ASSERT_EQ(405, evt.signalingStatusCode);
        }
    });

    ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
    ASSERT_NO_THROW(bobEvents.get());

    Phone::release(alicePhone);
}

//TEST_F(InstantMessageModuleTest, DISABLED_IMWithUnknownHeaders) {
//   TestAccount alice("alice");
//   TestAccount bob("bob");
//
//   auto aliceEvents = std::async(std::launch::async, [&]() {
//      SipInstantMessageHandle msgHdl;
//      SipAccountHandle hdl;
//      { // send message to bob
//         const std::string msg = "Hello bob";
//         msgHdl = alice.im->sendMessage(alice.handle, bob.config.uri(), msg.c_str(), msg.size(), SipInstantMessageManager::MimeType_TextPlain);
//      }
//      { // expect success sending
//         SipOutgoingInstantMessageEvent evt;
//         ASSERT_TRUE(alice.imEvents->expectEvent("SipInstantMessageHandler::onOutgoingInstantMessageSuccess",
//            15000, HandleEqualsPred<SipAccountHandle>(alice.handle), hdl, evt));
//         ASSERT_EQ(msgHdl, evt.im);
//      }
//   });
//
//   auto bobEvents = std::async(std::launch::async, [&]() {
//      SipAccountHandle hdl;
//      { // expect to receive message
//         SipIncomingInstantMessageEvent evt;
//         ASSERT_TRUE(bob.imEvents->expectEvent("SipInstantMessageHandler::onIncomingInstantMessage",
//            15000, HandleEqualsPred<SipAccountHandle>(bob.handle), hdl, evt));
//         ASSERT_EQ(evt.account, bob.handle);
//         ASSERT_EQ("Hello bob", evt.content);
//         ASSERT_EQ(evt.from.address, alice.config.uri());
//         ASSERT_EQ(evt.to.address, bob.config.uri());
//         ASSERT_TRUE(!evt.nonStandardHeaders.empty());
//         bool exists=false;
//         for (unsigned i = 0; i<evt.nonStandardHeaders.size();i++) 
//         {
//            if (strcmp(evt.nonStandardHeaders[i].header.c_str(), "X-SMS-To")==0)
//            {
//               exists = true; 
//            }
//         }
//         ASSERT_TRUE(exists);
//         ASSERT_EQ(kSuccess, bob.im->acceptIncoming(evt.im));
//      }
//   });
//
//   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
//   ASSERT_NO_THROW(bobEvents.get());
//   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
//   ASSERT_NO_THROW(aliceEvents.get());
//}
}
