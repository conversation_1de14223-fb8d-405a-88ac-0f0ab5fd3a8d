#include "cpcapi2_test_fixture.h"
#include "test_account_events.h"
#include "test_events.h"
#include "test_call_events.h"
#include "sipua_alianza_api_test_fixture.h"

#include "test_framework/http_test_framework.h"

#include "impl/account/SipAccountManagerInternal.h"
#include "impl/account/SipAccountHandlerInternal.h"
#include "impl/account/SipAccountAwareFeature.h"

#include <sstream>

#include "util/CurlPPHelper.h"
#include "util/CurlPPProgress.h"
#include "util/CurlPPSSL.h"
#include "util/CurlURI.h"

// rapidjson
#include <writer.h>
#include <stringbuffer.h>
#include <document.h>


#include "alianza_api/interface/public/alianza_api_handler.h"
#include "alianza_api/interface/public/alianza_api_types.h"
#include "alianza_api/interface/public/alianza_api_manager.h"

#include <sstream>

#include "util/CurlPPHelper.h"
#include "util/CurlPPProgress.h"
#include "util/CurlPPSSL.h"
#include "util/CurlURI.h"

// rapidjson
#include <writer.h>
#include <stringbuffer.h>
#include <document.h>


using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::SipConversation;
using namespace std::chrono;
using namespace curlpp::options;


namespace
{
   class SipuaCallForkingModuleTest : public CpcapiAutoTest
   {
   public:
      SipuaCallForkingModuleTest() {}
      virtual ~SipuaCallForkingModuleTest() {}

   };

   TEST_F(SipuaCallForkingModuleTest, BasicCallForking) {
      auto environmentId = TestEnvironmentConfig::testEnvironmentId();

      // CPE2 does not support multiple accounts using the same SIP credentials
      // while call fork relies on multiple accounts with the same SIP credentials to work properly
      // hence skip this call forking test for non-repro (qa, beta, etc) targets
      // related ticket: https://alianza.atlassian.net/browse/SCORE-1199
      if (!(environmentId == "repro")) GTEST_SKIP();

      TestAccount alice("alice");
      TestAccount bob("bob");
      TestAccount max("max", Account_NoInit);
      max.config.settings.username = bob.config.settings.username;
      ASSERT_NO_FATAL_FAILURE(max.enable());

      for (int i=0; i<10; i++)
      {
         SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
         alice.conversation->addParticipant(aliceCall, bob.uri().c_str());
         alice.conversation->start(aliceCall);

         auto aliceEvents = std::async(std::launch::async, [&] () {
            //assertNewConversationOutgoing(alice, aliceCall, bob.uri().c_str());
            {
               SipConversationHandle h;
               NewConversationEvent evt;
               ASSERT_TRUE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onNewConversation",
                  15000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
               ASSERT_EQ(aliceCall, h);
               ASSERT_EQ(ConversationType_Outgoing, evt.conversationType);
               ASSERT_EQ(bob.uri().c_str(), evt.remoteAddress);
            }

            assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
            assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
            assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
            if (!(environmentId == "repro")) assertConversationStateChanged(alice, aliceCall, ConversationState_Early);
            assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

            // give some time for media to start flowing in both directions
            std::this_thread::sleep_for(std::chrono::seconds(3));

            assertSuccess(alice.conversation->refreshConversationStatistics(aliceCall));
            assertConversationStaticsticsUpdated(alice, aliceCall, [](const ConversationStatisticsUpdatedEvent& evt){
               ASSERT_EQ(1, evt.conversationStatistics.audioChannels.size());
               ASSERT_TRUE(strlen(evt.conversationStatistics.audioChannels[0].encoder.plname) > 0);
               ASSERT_TRUE(strlen(evt.conversationStatistics.audioChannels[0].decoder.plname) > 0);
               ASSERT_EQ(0, evt.conversationStatistics.videoChannels.size());
            });

            {
               SipConversationHandle h;
               ConversationEndedEvent evt;
               ASSERT_TRUE(alice.conversationEvents->expectEvent("SipConversationHandler::onConversationEnded",
                  15000, AlwaysTruePred(), h, evt));
               ASSERT_EQ(aliceCall, h);
               ASSERT_EQ(ConversationEndReason_UserTerminatedRemotely, evt.endReason);
            }
         });

         auto bobEvents = std::async(std::launch::async, [&] () {
            SipConversationHandle bobCall;
            assertNewConversationIncoming(bob, &bobCall, alice.uri().c_str());

            assertSuccess(bob.conversation->sendRingingResponse(bobCall));
            assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);

            std::this_thread::sleep_for(std::chrono::milliseconds(10000));
            assertSuccess(bob.conversation->accept(bobCall));
            assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
            assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

            std::this_thread::sleep_for(std::chrono::milliseconds(10000));
            assertSuccess(bob.conversation->end(bobCall));
            assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
         });

         // Max's thread (is Bob Two):
         auto maxEvents = std::async(std::launch::async, [&] () {
            SipConversationHandle maxCall;
            assertNewConversationIncoming(max, &maxCall, alice.uri().c_str());
            assertSuccess(max.conversation->sendRingingResponse(maxCall));
            assertConversationStateChanged(max, maxCall, ConversationState_LocalRinging);
            assertConversationEnded(max, maxCall, ConversationEndReason_UserTerminatedRemotely);
         });

         waitFor3(aliceEvents, bobEvents, maxEvents);
      } // for loop
   }
}

