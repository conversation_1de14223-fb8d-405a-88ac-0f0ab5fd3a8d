# fmedia v0.16


# codepage for non-Unicode text: win1251 | win1252
codepage win1252

# single instance mode for fmedia-gui:
# . off: allow multiple instances
# . add: add files to the currently active queue
# . play: add files and start playback of the first added track
# . clear_play: clear the active queue, add files and start playback
instance_mode play

mod_conf "#file.in" {
	buffer_size 64k
	buffers 2
	# align 4k
}

mod_conf "#file.out" {
	buffer_size 64k
	preallocate 2m
}

mod_conf "net.icy" {
	# Buffer size and the number of buffers.  Larger values result in longer audio preload time.
	bufsize 32k
	buffers 2

	# Minimum number of bytes in buffer before processing it: 1..bufsize
	buffer_lowat 8k

	timeout 5000

	# HTTP header User-Agent: off | name_only | full
	user_agent off

	# Maximum number of HTTP redirects
	max_redirect 10

	# Support dynamic titles
	meta true
}

mod "net.in"

mod "mixer.in"

mod_conf "mixer.out" {
	format int16
	channels 2
	rate 44100

	# buffer size (in msec)
	buffer 1000
}

mod "#soundmod.conv"
mod "#soundmod.conv-soxr"
mod "#soundmod.gain"
mod "#soundmod.until"
mod "#soundmod.peaks"

# analyze PCM peaks in real-time
mod "#soundmod.rtpeak"

mod_conf "#tui.tui" {
	# Disable terminal input echo on Linux.
	# Set to "false" if you experience problems when terminal echo isn't restored after fmedia process was killed.
	echo_off true
}

mod "#queue.track"

mod_conf "gui.gui" {
	record {
		output "%APPDATA%\\fmedia\\rec-$date-$time.flac"
		gain 0

		ogg_quality 5.0
		mpeg_quality 2
		flac_complevel 6
	}

	convert {
		output "$filepath\\$tracknumber. $artist - $title.ogg"
		ogg_quality 5.0
		mpeg_quality 2
		aac_quality 192
		flac_complevel 6
		pcm_rate 0
		gain 0
		overwrite 0
		preserve_date 1
	}
}

mod "wav.in"
mod "wav.rawin"
mod "wav.out"

mod_conf "mpeg.decode" {
}

mod_conf "mpeg.encode" {
	# VBR quality: 9..0 or CBR bitrate: 64..320
	quality 2

	min_meta_size 1000
}

mod_conf "ogg-vorbis.decode" {
	seekable true
}

mod_conf "ogg-vorbis.encode" {
	# minimum size of Vorbis comments (add padding if necessary)
	min_tag_size 1000

	# -1.0 .. 10.0
	quality "5.0"

	# OGG page size (1..64k)
	page_size 8k
}

mod "flac.decode"

mod_conf "flac.encode" {
	# compression level: 0..8
	compression 6

	# audio interval (in seconds) for seek table (0=disable)
	seektable_interval 1

	# minimum size of meta data (add padding block if needed)
	min_meta_size 1000

	# generate MD5 checksum of uncompressed data
	md5 true
}

mod "mp4.decode"

mod_conf "mp4.aac-encode" {
	profile 2

	# AAC encoding quality: 1..5 (VBR) or 8..800 (CBR, kbit/s)
	quality 256

	# Better quality at the cost of additional encoding time
	afterburner 1

	# Frequency cut-off (max. 20000Hz);  0: default setting.
	bandwidth 0
}

mod "wavpack.decode"

mod "ape.decode"

mod "plist.dir"
mod "plist.m3u"
mod "plist.cue"


mod_conf "wasapi.out" {
	device_index 0
	exclusive_mode 0
	buffer_length 500
}

mod_conf "wasapi.in" {
	device_index 0

	# 0: disabled, 1: allowed, 2: always
	exclusive_mode 0

	# in msec
	buffer_length 100

	latency_autocorrect false
}

# mod_conf "direct-sound.in" {
# 	device_index 0
# 	buffer_length 500
# }

# mod_conf "direct-sound.out" {
# 	device_index 0
# 	buffer_length 500
# }


mod_conf "alsa.out" {
	device_index 0
	buffer_length 500
}

mod_conf "alsa.in" {
	device_index 0
	buffer_length 500
}


# Module used for playback
output "wasapi.out"
output "alsa.out"

# Module used for recording
input "wasapi.in"
input "alsa.in"

# Default audio format for recording
record_format {
	# Audio format: int8 | int16 | int24 | int32 | float32
	format int16
	# Channels number: 2 (stereo) | 1 (mono) | left | right
	channels 2
	rate 44100
}


input_ext {
	# mod ext...
	"wav.in" wav
	"wav.rawin" raw
	"ogg-vorbis.decode" ogg
	"mpeg.decode" mp3
	"flac.decode" flac
	"mp4.decode" m4a mp4
	"wavpack.decode" wv
	"ape.decode" ape

	"plist.m3u" m3u m3u8
	"plist.cue" cue
}

output_ext {
	# mod ext...
	"wav.out" wav
	"ogg-vorbis.encode" ogg
	"mp4.aac-encode" m4a
	"mpeg.encode" mp3
	"flac.encode" flac
}
