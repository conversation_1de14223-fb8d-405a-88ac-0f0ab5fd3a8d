# fmedia v0.16 GUI

menu mfile {
	item "&Open..." {
		action OPEN
		hotkey "Ctrl+O"
	}
	item "&Add..." {
		action ADD
		hotkey "Insert"
	}
	item "Add &URL..." {
		action ADDURL
		hotkey "Ctrl+U"
	}
	item "-" {
	}
	item "&New Tab" {
		action QUE_NEW
		hotkey "Ctrl+N"
	}
	item "Close Tab" {
		action QUE_DEL
		hotkey "Ctrl+W"
	}
	item "&Save Playlist..." {
		action SAVELIST
		hotkey "Ctrl+S"
	}
	item "Select &All" {
		action SELALL
		hotkey "Ctrl+A"
	}
	item "In&vert Selection" {
		action SELINVERT
	}
	item "&Remove" {
		action REMOVE
		hotkey "Delete"
	}
	item "&Clear List" {
		action CLEAR
	}
	item "-" {
	}
	item "Show Media Info" {
		action SHOWINFO
		hotkey "I"
	}
	item "C&opy to Clipboard" {
		action COPYFILE
		hotkey "Ctrl+C"
	}
	item "Copy File&name to Clipboard" {
		action COPYFN
	}
	item "Open Fo&lder" {
		action SHOWDIR
	}
	item "&Delete From Disk" {
		action DELFILE
		hotkey "Shift+Delete"
	}
	item "-" {
	}
	item "&Minimize to Tray" {
		action HIDE
		hotkey "Ctrl+M"
	}
	item "E&xit" {
		action QUIT
	}
}

menu mplay {
	item "&Play/Pause" {
		action PAUSE
		hotkey "Space"
	}
	item "&Stop" {
		action STOP
		hotkey "S"
	}
	item "Stop After Current" {
		action STOP_AFTER
		hotkey "Shift+S"
	}
	item "&Next" {
		action NEXT
		hotkey "N"
	}
	item "P&revious" {
		action PREV
		hotkey "P"
	}
	item "-" {
	}
	item "Seek &Forward" {
		action FFWD
		hotkey "Right"
	}
	item "Seek &Back" {
		action RWND
		hotkey "Left"
	}
	item "Go To..." {
		action GOTO_SHOW
		hotkey "Ctrl+G"
	}
	item "Set Marker" {
		action SETGOPOS
		hotkey "Shift+G"
	}
	item "Jump To Marker" {
		action GOPOS
		hotkey "G"
	}
	item "-" {
	}
	item "Volume &Up" {
		action VOLUP
		hotkey "Up"
	}
	item "Volume &Down" {
		action VOLDOWN
		hotkey "Down"
	}
}

menu mrec {
	item "&Record..." {
		action REC_SETS
		hotkey "Ctrl+Shift+R"
	}
	item "&Quick Start/Stop" {
		action REC
		hotkey "Ctrl+R"
	}
	item "&Play and Record" {
		action PLAYREC
	}
	item "Play &Mixed and Record" {
		action MIXREC
	}
	item "Open Recordings Fo&lder" {
		action SHOWRECS
	}
}

menu mconvert {
	item "&Convert..." {
		action SHOWCONVERT
		hotkey "Ctrl+T"
	}
	item "Set Seek Position" {
		action SETCONVPOS_SEEK
		hotkey "["
	}
	item "Set Until Position" {
		action SETCONVPOS_UNTIL
		hotkey "]"
	}
}

menu mhelp {
	item "Edit Default Settings..." {
		action CONF_EDIT
	}
	item "Edit User Settings..." {
		action USRCONF_EDIT
	}
	item "Edit GUI..." {
		action FMEDGUI_EDIT
	}
	item "-" {
	}
	item "Show Readme File..." {
		action README_SHOW
	}
	item "Show Changelog..." {
		action CHANGES_SHOW
	}
	item "&About" {
		action ABOUT
	}
}

menu mtray {
	item "fmedia" {
		style default
		action SHOW
	}
	item "Playback" {
		submenu mplay
	}
	item "Exit" {
		action QUIT
	}
}

dialog dlg {
	filter "Input (*.mp3;*.ogg;*.flac;*.m4a;*.mp4;*.wv;*.ape;*.wav;*.m3u;*.m3u8;*.cue)\x00*.mp3;*.ogg;*.flac;*.m4a;*.mp4;*.wv;*.ape;*.wav;*.m3u;*.m3u8;*.cue\x00Output (*.ogg;*.mp3;*.flac;*.m4a;*.wav)\x00*.ogg;*.mp3;*.flac;*.m4a;*.wav\x00Playlists (*.m3u8;*.m3u)\x00*.m3u8;*.m3u\x00All (*.*)\x00*.*\x00\x00"
}

window wmain {
	title "fmedia"
	position 100 100 380 300
	borderstick 7
	# style visible
	icon {
		filename "fmedia.ico"
	}
	font {
		name Arial
		height 10
	}

	mainmenu mm {
		item "&File" {
			submenu mfile
		}
		item "&Playback" {
			submenu mplay
		}
		item "&Record" {
			submenu mrec
		}
		item "&Convert" {
			submenu mconvert
		}
		item "&Help" {
			submenu mhelp
		}
	}

	button bpause {
		position 0 0 20 20
		style visible
		text "►"
		tooltip "Play/Pause"
		action PAUSE
		font {
			name Arial
			height 10
		}
	}

	button bstop {
		position 25 0 20 20
		style visible
		text "■"
		tooltip "Stop"
		action STOP
		font {
			name Arial
			height 13
		}
	}

	button bprev {
		position 50 0 20 20
		style visible
		text "<<"
		tooltip "Previous"
		action PREV
	}

	button bnext {
		position 75 0 20 20
		style visible
		text ">>"
		tooltip "Next"
		action NEXT
	}

	label lpos {
		position 100 0 200 20
		style visible
		font {
			name Arial
			height 13
			style bold
		}
	}

	trackbar tpos {
		position 0 20 300 20
		style no_ticks both visible
		range 0
		page_size 5
		onscrolling SEEKING
		onscroll SEEK
	}

	trackbar tvol {
		position 300 0 60 20
		style no_ticks both visible
		range 125
		value 100
		page_size 5
		onscrolling VOL
	}

	tab tabs {
		position 0 45 300 20
		style visible
		onchange QUE_SEL
	}

	listview vlist {
		position 0 65 360 175
		style explorer_theme grid_lines multi_select visible
		dblclick PLAY
		popupmenu mfile

		column "#" {
			width 35
			align right
		}
		column Artist {
		}
		column Title {
			width 200
		}
		column Duration {
			width 50
			align right
		}
		column Info {
		}
		column Filename {
		}
	}

	paned pntop {
		child tvol {
			move x
		}
	}
	paned pnpos {
		child tpos {
			resize cx
		}
	}
	paned pntabs {
		child tabs {
			resize cx
		}
	}
	paned pnlist {
		child vlist {
			resize cx cy
		}
	}

	statusbar stbar {
		style visible
		parts 100 -1
	}

	trayicon tray_icon {
		icon {
			filename "fmedia.ico"
		}
		popupmenu mtray
		lclick SHOW
	}
}

window wconvert {
	parent wmain
	position 300 100 300 200
	style popup
	title "Convert"

	font {
		name Arial
		height 10
	}

	mainmenu mmconv {
		item "&Convert!" {
			action CONVERT
		}
	}

	editbox eout {
		position 0 0 200 25
		style visible
	}

	button boutbrowse {
		position 220 0 35 25
		style visible
		text "..."
		action OUTBROWSE
	}

	listview vsets {
		position 0 25 250 100
		style edit_labels explorer_theme grid_lines visible

		lclick CVT_SETS_EDIT

		column Setting {
		}
		column Value {
		}
		column Description {
		}
	}

	paned pnout {
		child eout {
			resize cx
		}
		child boutbrowse {
			move x
		}
	}

	paned pnsets {
		child vsets {
			resize cx cy
		}
	}
}

window wrec {
	parent wmain
	position 300 100 300 200
	style popup
	title "Record"

	font {
		name Arial
		height 10
	}

	mainmenu mmrec {
		item "&Start/Stop" {
			action REC
		}
	}

	editbox eout {
		position 0 0 200 25
		style visible
	}

	button boutbrowse {
		position 220 0 35 25
		style visible
		text "..."
		action OUTBROWSE
	}

	listview vsets {
		position 0 25 250 100
		style edit_labels explorer_theme grid_lines visible

		lclick CVT_SETS_EDIT

		column Setting {
		}
		column Value {
		}
		column Description {
		}
	}

	paned pnout {
		child eout {
			resize cx
		}
		child boutbrowse {
			move x
		}
	}

	paned pnsets {
		child vsets {
			resize cx cy
		}
	}
}

window winfo {
	parent wmain
	position 300 100 300 500
	style popup

	listview vinfo {
		position 0 0 100 100
		style edit_labels explorer_theme grid_lines multi_select visible

		lclick INFOEDIT

		column Name {
		}
		column Value {
			width 200
		}
	}

	paned pninfo {
		child vinfo {
			resize cx cy
		}
	}
}

window wgoto {
	parent wmain
	title "Go To"
	position 300 300 100 100
	style popup
	opacity 90
	font {
		name Arial
		height 10
	}

	editbox etime {
		position 0 0 100 25
		style visible
	}

	button bgo {
		position 0 25 50 25
		style visible
		text "Go!"
		action GOTO
	}
}

window wabout {
	parent wmain
	title "fmedia"
	position 300 300 300 200
	style popup
	opacity 90

	label labout {
		position 15 15 300 60
		style visible
		font {
			name Arial
			height 13
			style bold
		}
	}
}

window wlog {
	parent wmain
	title "Log"
	position 300 300 400 200
	style popup

	text tlog {
		position 0 0 100 100
		style visible
		font {
			name "Courier New"
			height 8
		}
	}

	paned pnlog {
		child tlog {
			resize cx cy
		}
	}
}

window wuri {
	parent wmain
	title "Add URL"
	position 300 300 400 100
	style popup
	font {
		name Arial
		height 10
	}

	editbox turi {
		position 0 0 100 25
		style visible
	}

	button bok {
		position 0 25 50 25
		style visible
		text "OK"
		action URL_ADD
	}

	button bcancel {
		position 50 25 50 25
		style visible
		text "Cancel"
		action URL_CLOSE
	}

	paned pnuri {
		child turi {
			resize cx
		}
	}
}
