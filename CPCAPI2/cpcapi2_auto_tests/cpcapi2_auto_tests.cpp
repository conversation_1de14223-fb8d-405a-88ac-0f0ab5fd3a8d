// cpcapi2_auto_tests.cpp : Defines the entry point for the console application. 
#if _WIN32
#include "stdafx.h"
#endif

#ifndef CPCAPI2_NO_INTERNAL_PROXY
#include "ReproRunner.h"
#endif /* CPCAPI2_NO_INTERNAL_PROXY */

#include <gtest/gtest.h>
#include <cpcapi2.h>
#include <filesystem>

#include "cpcapi2_auto_tests_ver.h"
#include "cpcapi2_test_fixture.h"
#include "TestVideoHelper.h"
#include "test_framework/cpcapi2_test_framework.h"
#include "test_framework/bare_bones_sip_endpoint.h"
#include "test_framework/test_watchdog.h"
#include "test_framework/test_pcap_capture.h"
#include "test_framework/test_runtime_environment.h"
#include "cocoa_helpers.h"
#include "script_runner.h"
#include <fstream>
#include "util/FileDescriptorMonitor.h"

#ifdef WIN32
#include "breakpad_integration.h"
#endif

#ifdef ANDROID
#include <thread>
#include <future>
#include "JniHelper.h"
#include "../language_wrapper/Android/jni/gen/jni/com_counterpath_sdk_android_UnitTests.h"
#include <android/log.h>
#include "stdoutToLogcatRedirector.h"
#include "stdoutToFileRedirector.h"
#endif

#ifdef __APPLE__
#include "crashpad_integration.h"
#include "mac_proc_utils.h"
#endif

using namespace CPCAPI2;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::SipConversation;
using namespace CPCAPI2::test;
using namespace std;

#ifdef __APPLE__
__attribute__((visibility("default"))) __attribute__((no_sanitize_address)) extern "C" const char *__asan_default_options() 
{
   // asan container overflow seen at rtc::RollingAccumulator<unsigned long long>::AddSample(unsigned long long) rollingaccumulator.h:73
   // when running video call tests on mac.
   // unknown as to why; access is to vector element 0; capacity and size are both 30.
   // does not seem to be known false positive of some code built with asan enabled, some not
   return "detect_container_overflow=0";
}
#endif


#ifndef CPCAPI2_NO_INTERNAL_PROXY
namespace CPCAPI2
{
   namespace test
   {
      CPCAPI2::ReproRunner* ReproHolder::s_reproRunner = NULL;

      CPCAPI2::ReproRunner* ReproHolder::instance()
      {
         if (s_reproRunner == NULL)
         {
            s_reproRunner = new CPCAPI2::ReproRunner();
         }
         return s_reproRunner;
      }

      void ReproHolder::destroyInstance()
      {
         if (NULL == s_reproRunner)
            return;
         if (s_reproRunner->isRunning())
            s_reproRunner->shutdown();
         delete s_reproRunner;
         s_reproRunner = NULL;
      }
   }
}
#endif


#ifdef _WIN32
int setenv(const char *name, const char *value, int overwrite)
{
   int errcode = 0;
   if(!overwrite) {
      size_t envsize = 0;
      errcode = getenv_s(&envsize, NULL, 0, name);
      if(errcode || envsize) return errcode;
   }
   return _putenv_s(name, value);
}
#endif

void handleArgs(int argc, char *argv[])
{
   bool setLogFile = false;
   std::vector<std::string> commandLineArgs;
   for (int i = 0; i < argc; ++i)
   {
      std::string gtestFilterPrefix("--gtest_filter=");
      std::string loggingNamePrefix("--sdkLogFileName=");
      std::string arg = argv[i];

      commandLineArgs.push_back(arg);

      if (!arg.compare(0, gtestFilterPrefix.size(), gtestFilterPrefix))
      {
         std::string gtestFilterParam = (arg.substr(gtestFilterPrefix.size()).c_str());

         std::stringstream logName, crashName;

         logName << gtestFilterParam << ".txt";
         crashName << gtestFilterParam << ".dmp";

         // when gtest-parallel runs us, it will use gtest_filter to run one
         // test at a time; leverage this to save a log with that test's name
         if (TestEnvironmentConfig::saveLoggingToFile() && !setLogFile)
         {
            TestEnvironmentConfig::setLogFileName(logName.str().c_str());
         }
         if (TestEnvironmentConfig::saveCrashdumpsToFile())
         {
            TestEnvironmentConfig::setCrashDumpFileName(crashName.str().c_str());
#if (defined(__APPLE__) && TARGET_OS_IPHONE == 0)
            /*
             * Ensure that the following commands are run on the MAC so that dumps are generated properly.
             *
             * $ launchctl unload -w /System/Library/LaunchAgents/com.apple.ReportCrash.plist
             * $ sudo launchctl unload -w /System/Library/LaunchDaemons/com.apple.ReportCrash.Root.plist
             *
             */

            if(Crashpad::registerCrashpad(logName.str().c_str()) == false)
            {
               std::cout<<"Unable to start Crashpad.\n";
            }
            else
            {
               std::cout<<"Started Crashpad.\n";
            }
#endif // #if (defined(__APPLE__) && TARGET_OS_IPHONE == 0)
         }
      }
      else if (!arg.compare(0, loggingNamePrefix.size(), loggingNamePrefix))
      {
         // C++ complains if you pass a string literal directly into putenv
         setenv( "CPCAPI2_SAVE_LOGGING_TO_FILE", "1", 1 );

         std::string fileName = (arg.substr(loggingNamePrefix.size()).c_str());
         std::stringstream logFile;
         logFile << fileName << ".txt";
         TestEnvironmentConfig::setLogFileName(logFile.str().c_str());
         setLogFile = true;
      }
   }

   TestEnvironmentConfig::setCommandLineArgs(commandLineArgs);
}

void checkFdLeaks(CPCAPI2::Utils::FileDescriptorsCheckpoint& check)
{
   std::vector<std::string> skipItems;
   skipItems.push_back("counterpath.CPCAPI2AutoTestsX.savedState");  // ignore unit test state files
   //      skipItems.push_back("/dev/random");                               // ignore these?
   skipItems.push_back("/dev/urandom"); // OpenSSL appears to deallocate this on process exit (at global static destruction time)

   if (check.newCheckpoint("Process about to exit", skipItems))
   {
      // DRL We still have left over file descriptors so we can't fail here yet.
      //         FAIL() << "File descriptor leak!";
      std::cout << std::endl << "File descriptor leak!" << std::endl << std::endl;
   }
   else
   {
      std::cout << std::endl << "No file descriptor leaks" << std::endl << std::endl;
   }
}

void prepareArtifactDirectory()
{
   // skip unil we have a need to use runtime_artifacts on Android
#if !defined(ANDROID)
   // in future we should consider moving logging output directory to a subfolder of the artifact directory
   try
   {
      std::filesystem::create_directories(TestEnvironmentConfig::artifactFilePath());
   }
   catch (filesystem::filesystem_error ex)
   {
      safeCout("prepareArtifactDirectory error: " << ex.what());
   }
#endif
}

int initUnitTests(std::string reproConfigPath, int& argc, char * argv[])
{
    int retcode = 0;

 #if defined(__APPLE__)
   CPCAPI2::test::TestCocoaApp cocoaApp;
   cocoaApp.startNSApplication();
    
   char cwd[PATH_MAX];
   if (getcwd(cwd, sizeof(cwd)) != NULL)
   {
      if (std::string(cwd) == "/")
      {
         std::cerr << "Working directory does not appear correct; tests will likely fail to run. Check instructions at https://alianza.atlassian.net/wiki/spaces/DEV/pages/2529067122/macOS+SDK+build for working directory setup" << std::endl;
         return -1;
      }
   }
 #endif

   handleArgs(argc, argv);

 #ifdef _WIN32
    if (TestEnvironmentConfig::saveCrashdumpsToFile())
    {
       Breakpad::registerBreakpad();
    }
 #endif

 #ifndef _WIN32
   // ignore SIGPIPE
   if (signal(SIGPIPE, SIG_IGN) == SIG_ERR)
   {
      std::cerr << "Error assigning signal handler for SIGPIPE" << std::endl;
   }
 #endif

   auto sdkThread = std::async(std::launch::async, [&]() noexcept {
#ifndef CPCAPI2_NO_INTERNAL_PROXY
   CPCAPI2::ReproRunner* reproRunner = NULL;
   // std::cout << std::endl << "repro runner is being instantiated\n";
#else
   // std::cout << std::endl << "repro runner will not be instantiated\n";
#endif /* CPCAPI2_NO_INTERNAL_PROXY */

   // Some useful examples of things you can do
   //::testing::GTEST_FLAG(filter) = "BasicCallTests.BasicCancelCallToInvalid";
   // ::testing::GTEST_FLAG(repeat) = 10;
   //::testing::GTEST_FLAG(break_on_failure) = true;

   // set if a platform wishes to specify a specific list of test suites to run.
   // useful on platforms where not many tests work yet.
   std::string override_base_test_list = "";

   // set if a platform wishes to exclude specific tests thare are known to have
   // problems on that platform
   std::string tests_exclude;

#ifdef __APPLE__
   TestCocoaAppScopedRunner cocoaAppRunner(cocoaApp);

   tests_exclude += ":";
   tests_exclude += "VccsModuleTest.*:RemoteSyncModuleTest.*"; // OBELISK-3342
   tests_exclude += ":";


   // seeing kernel panics running PTT WAN tests on M1 based Mac Mini; either
   // regardless of whether tests are running under Rosetta or not
   if (MacSystemInfo::cpuBrandString() == "Apple M1")
   {
      tests_exclude += "PttWanAutoTest.*";
      tests_exclude += ":";
      tests_exclude += "Analytics1ModuleTest.PttWanCallSuccess";
      tests_exclude += ":";
   }

   if (!TestEnvironmentConfig::macLoopbackAliasesSet())
   {
      // for these tests to work, you need to run something like the following bash script on your mac:
      // #!/bin/bash
      // for ((i=2;i<256;i++))
      // do
      //     sudo ifconfig lo0 alias 127.0.0.$i up
      // done

      tests_exclude += "PttLanAutoTest.*";
      tests_exclude += ":";
      tests_exclude += "DnsTest.AresServerShift_NoAAAAResponse";
      tests_exclude += ":";
   }
 #elif defined(ANDROID)
   override_base_test_list += "PhoneModuleTest.*:AccountModuleTest.*:HttpsTests.*:BasicCallTests.*:HttpModuleTest.*:HttpModuleTest2.*:BIEventsModuleTest.*:DnsResolverTest.*:LdapModuleTest.*";
   override_base_test_list += ":";
   override_base_test_list += "DnsFailoverTest.*:XmppRosterModuleTest.*:XmppChatModuleTest.*:XmppAccountModuleTest.*:XmppMultiUserChatModuleTest.*:XmppFileTransferTest.*:PttLanAutoTest.*:VideoCallTests.*";

#elif defined(_WIN32)
   if (TestEnvironmentConfig::dockerContainerized())
   {
      tests_exclude += "PttWanAutoTest.*"; // currently failing for unknown reasons on docker windows runs
   }
 #elif defined(__linux__) && !defined(ANDROID)
   // temporarily running in separate job for linux runs until stability is sorted out
   tests_exclude += "PttWanAutoTest.*";
   tests_exclude += ":";
   tests_exclude += "WatchdogTest.*";
   tests_exclude += ":";
   tests_exclude += "VideoCallTests.TestExtQueryScreenshareDeviceList:VideoCallTests.AudioCallAddVideoBothSidesGlare:VideoCallTests.AudioCallAddVideoInvalidSurface2";
   tests_exclude += ":";
   // No ManyCam webcam on Linux. No G.729
   tests_exclude += "CallStatisticsTests.AudioWidebandThenNarrowbandCalls:CPUUsageTests.VideoCallCpuUsage*:CPUUsageTests.AudioCallCpuUsageG729";
   tests_exclude += ":";
   //when using dummy audio device, playSound() function with wav file cannot stop playing automatically
   tests_exclude += "AudioModuleTest.PlayWavFile:AudioModuleTest.PlayManyWavFile";
   tests_exclude += ":";
   tests_exclude += "MessageStoreModuleTest.MassMessageStore";
 #endif

   std::string tests_include;
   // useful for locally changing to test specific sets of tests while still avoiding problematic tests
   if (override_base_test_list.empty())
   {
      tests_include = "*.*";
   }
   else
   {
      tests_include = override_base_test_list;
   }

   ::testing::GTEST_FLAG(filter) = tests_include + "-" + tests_exclude;

   ::testing::GTEST_FLAG(catch_exceptions) = false;
   //::RoInitialize(RO_INIT_MULTITHREADED);
   ::testing::InitGoogleTest(&argc, argv);

       if (false == ::testing::GTEST_FLAG(list_tests))
       {
          prepareArtifactDirectory();
    #ifndef CPCAPI2_NO_INTERNAL_PROXY
          reproRunner = ReproHolder::instance();
          const char* const reproArgs[] = { "" };
          resip::Data configFile = (TestEnvironmentConfig::testResourcePath() + "repro.config").c_str();
          std::cout << std::endl << "starting repro runner: " << reproRunner << " config-file: " << configFile.c_str() << std::endl;
          // TODO: re-introduce ASSERT_TRUE below once we prevent it from failing for our test tool builds (e.g. standalone SIP test tool)
          reproRunner->run(1, reproArgs, configFile);
    #endif /* CPCAPI2_NO_INTERNAL_PROXY */
       }

 #ifdef _WIN32
       // Start ejabberd and maradns scripts
       script_runner *scriptRunner = new script_runner();
       scriptRunner->runPrerequisiteScripts();
 #endif

      ::testing::TestEventListeners& listeners = ::testing::UnitTest::GetInstance()->listeners();
      if (TestEnvironmentConfig::testTimerWatchdog())
      {
         // Google Test takes the ownership
         listeners.Append(new TestTimerWatchdog(std::chrono::minutes(10)));
      }
      if (TestEnvironmentConfig::pcapCapture())
      {
         listeners.Append(new TestPcapCapture(TestEnvironmentConfig::pcapCaptureTestCases().c_str()));
      }

      cpc::string expectedAutoTestsVersion = TestEnvironmentConfig::expectedAutoTestsVersion();
      cpc::string actualAutoTestsVersion = CPCAPI2_AUTO_TESTS_VERSION;

      if (expectedAutoTestsVersion.empty() || expectedAutoTestsVersion == actualAutoTestsVersion)
      {
         if (expectedAutoTestsVersion.empty())
         {
            std::cerr << std::endl << "Not checking build version" << std::endl;
         }

         retcode = RUN_ALL_TESTS();
      }
      else
      {
         std::cerr << std::endl << "Expected build version \"" << expectedAutoTestsVersion <<
            "\" doesn't match actual version \"" << actualAutoTestsVersion << "\"!" << std::endl;
         retcode = 1;
      }

#ifndef CPCAPI2_NO_INTERNAL_PROXY
      // tests may have re-created repro
      reproRunner = ReproHolder::instance();
      if (reproRunner != NULL && reproRunner->isRunning())
      {
         std::cout << std::endl << "shutting down repro runner" << std::endl;
         reproRunner->shutdown();
         delete reproRunner;
      }
#endif /* CPCAPI2_NO_INTERNAL_PROXY */

 #ifdef _WIN32
       // Terminate scripts
       scriptRunner->stopPrerequisiteScripts();
 #endif

       if (TestEnvironmentConfig::pauseAfterTests())
       {
 #if _WIN32
          ::system("pause");
 #else
          // std::string answer;
          // std::cerr << "Please press <return> key to continue ..." << std::flush;
          // std::getline(std::cin, answer);
 #endif
       }
    });
 #if defined(__APPLE__)
    cocoaApp.run();
 #endif

   sdkThread.wait();
   sdkThread.get();

   if (false == ::testing::GTEST_FLAG(list_tests))
   {
   }

   return retcode;
}

#ifdef ANDROID
extern "C" {

   JNIEXPORT jint JNICALL Java_com_counterpath_sdk_android_UnitTests_runUnitTests(JNIEnv * env, jobject obj,
                                                                                 jobjectArray gtestArgs, 
                                                                                 /* android.content.Context */ jobject androidAppContext,
                                                                                 jstring storagePath )
   {
      const std::string cppStoragePath = CPCAPI2::Jni::JavaToStdString(storagePath);
      std::stringstream logPath;
      logPath << cppStoragePath << "/AutoUnitTest.log";

      std::ofstream alog;
      alog.open(logPath.str(), std::ofstream::out | std::ofstream::trunc);
      alog.close();

      std::stringstream envVariablesPath;
      envVariablesPath << TestEnvironmentConfig::testResourcePath() << "/env_variables.txt";
      
      // env_variables.txt is used by Jenkins jobs to apply test framework specific environment variables.
      // When running the autotests via Android Studio typically a developer would set environment variables via android.system.Os.setenv(..)
      // in Java code at core/cpcapi2_auto_tests/Android/auto_unit_tests/androidUnitTestingApp 
      std::ifstream env_var(envVariablesPath.str());
      __android_log_print(ANDROID_LOG_INFO, "Debugging: ", "Checking for presence of env_variables.txt at %s", envVariablesPath.str().c_str());
      if (env_var)
      {
         __android_log_print(ANDROID_LOG_INFO, "Debugging: ", "Android autotests: env_variables.txt found!");
         string line;
         string name;
         string val;
         while (getline(env_var, line) )
         {
            stringstream stream(line);
            stream >> name;
            stream >> val;
            setenv(name.c_str(), val.c_str(), 1);
            __android_log_print(ANDROID_LOG_INFO, "Debugging: ", "setting env variable %s to %s", name.c_str(), val.c_str());
         }
         env_var.close();
      }
      else
      {
         __android_log_print(ANDROID_LOG_INFO, "Debugging: ", "Android autotests: env_variables.txt not found (ok for non-Jenkins runs).");
      }

      if (!gtestArgs)
         __android_log_print(ANDROID_LOG_INFO, "Debugging: ", "========>>> pathToReproconfig is NULL! <<<========");
      if(!env)
         __android_log_print(ANDROID_LOG_INFO, "Debugging: ", "========>>> JNIEvn* env is NULL! <<<========");
      // Get the number of args
      jsize ArgCount = env->GetArrayLength(gtestArgs);
      char **argv = new char*[ArgCount];

      bool listingTests = false;
      bool forceWriteLogComplete = false;

      for (int i = 0; i < ArgCount; ++i )
      {
         jstring string = (jstring)(env->GetObjectArrayElement( gtestArgs, i));
         const char *cstring = env->GetStringUTFChars(string, 0);
         if (strcmp(cstring, "--gtest_list_tests") == 0)
         {
            listingTests = true;
         }
         else if (strcmp(cstring, "--force_write_log_complete") == 0)
         {
            forceWriteLogComplete = true;
         }
         argv[ i ] = strdup( cstring );
         env->ReleaseStringUTFChars(string, cstring );
         env->DeleteLocalRef(string );
      }
      std::string reproArgs(argv[0]);

      CPCAPI2::Jni::ScopedGlobalRef<jobject> globalAndroidAppContext(androidAppContext);
      TestRuntimeEnvironment::instance()->setAndroidAppContext(*globalAndroidAppContext);

      std::unique_ptr<StdoutRedirector> stdoutRedirector;

      if (TestEnvironmentConfig::redirectCoutToFile())
      {
         __android_log_print(ANDROID_LOG_INFO, "Debugging: ", "cpcapi2_auto_tests: redirecting stdout to file");
         stdoutRedirector.reset(new StdoutToFileRedirector(logPath.str()));
         if (!listingTests || forceWriteLogComplete)
         {
            (static_cast<StdoutToFileRedirector*>(stdoutRedirector.get()))->setRedirectDebugLoggingEnabled(true);
         }
      }
      else
      {
         __android_log_print(ANDROID_LOG_INFO, "Debugging: ", "cpcapi2_auto_tests: redirecting stdout to logcat");
         stdoutRedirector.reset(new StdoutToLogcatRedirector());
      }

      // !warning! InitGoogleTest may modify argv, argc. As a result, ArgCount may be different after
      // initUnitTests returns
      int retcode = initUnitTests(reproArgs, ArgCount, argv);
      for ( int i = 0; i < ArgCount; ++i )
      {
         free(argv[i]);
      }
      delete[] argv;

      stdoutRedirector.reset();

      return 0;
   }

   static unsigned int sAndroidVideoRenderTargetHandle = 1;

   JNIEXPORT jint JNICALL Java_com_counterpath_sdk_android_UnitTests_registerLocalRenderTarget(JNIEnv* jniEnv, jobject obj, jobject surfaceView)
   {
      int handle = sAndroidVideoRenderTargetHandle++;
      // todo: is cleanup required? probably not if we only allocate one per unit test process run?
      void* t = jniEnv->NewGlobalRef(surfaceView);
      TestVideoHelper::setFixedLocalVideoRenderHandle(t);
      return handle;
   }

   JNIEXPORT jint JNICALL Java_com_counterpath_sdk_android_UnitTests_registerRemoteRenderTarget(JNIEnv* jniEnv, jobject obj, jobject surfaceView)
   {
      int handle = sAndroidVideoRenderTargetHandle++;
      // todo: is cleanup required? probably not if we only allocate one per unit test process run?
      void* t = jniEnv->NewGlobalRef(surfaceView);
      TestVideoHelper::setFixedRemoteVideoRenderHandle(t);
      return handle;
   }

}  // extern "C"

#else // #ifdef ANDROID

int main(int argc, char * argv[])
{
   std::string reproArgs = TestEnvironmentConfig::testResourcePath().c_str();
   int retcode = initUnitTests(reproArgs, argc, argv);

   return retcode;
}

#endif
