#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#if (CPCAPI2_BRAND_VCCS_MODULE == 1)

#if defined(__GNUC__) || defined(__clang__)
#pragma GCC diagnostic ignored "-Wdeprecated-declarations"
#endif

#include "cpcapi2_test_fixture.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>

#include "test_framework/cpcapi2_test_framework.h"
#include "test_framework/bare_bones_sip_endpoint.h"
#include "vccs_test_harness/VccsTestHarness.h"
#include "test_account_events.h"
#include "test_call_events.h"

#include "vccs/VccsAccountManagerInterface.h"
#include "impl/call/SipConversationManagerInternal.h"

#include <cpcstl/string.h>
#include <string>
#include <thread>
#include <future>
#include <memory>

// For getcwd function
#ifdef _WIN32
#include <direct.h>
#else
#include <unistd.h>
#endif

using namespace CPCAPI2;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::VCCS;
using namespace CPCAPI2::test;

#ifdef _WIN32
#include <Windows.h>
#else
#include <sys/time.h>
#endif

namespace {

   class VccsModuleTest : public CpcapiAutoTest
   {
   public:
      VccsModuleTest() {}
      virtual ~VccsModuleTest() {}
   };

   // disabled until we have a solid RemoteSync server test account to run against
   TEST_F(VccsModuleTest, Vertical1) {

      // NB: "alice" and "bob" are actually the same user (since syncing
      // really happens between different registrations of the same user)
      TestAccount alice("alice", Account_Init);

      // Set the settings
      Account::VccsAccountSettings settings;
      //settings.webSocketURL   = "ws://***********:8989/join";
      settings.wsSettings.webSocketURL   = "wss://127.0.0.1:8989/join";
      settings.wsSettings.certMode       = WebSocket::CertVerificationMode_None;
      settings.group          = "imap.mobilevoiplive.com";
      settings.userName       = "<EMAIL>";
      settings.password       = "vccs0007";
      settings.displayName    = "Smokey the Bear";
      // sip Aor is "+<EMAIL>";

      alice.vccsAccountManager->configureDefaultAccountSettings( alice.vccsAccountHandle, settings );
      alice.vccsAccountManager->applySettings( alice.vccsAccountHandle ); // prob. not needed

      // Start the test harness.
      VccsTestHarness harness( TestEnvironmentConfig::testResourcePath() + "vccs_test_harness_scenarios/VCCS_Vertical1.dat", 8989, true );
      harness.start();

      // Enable the account.
      alice.vccsAccountManager->enable( alice.vccsAccountHandle );

      // Wait for the account to reach the registered state
      {
         auto aliceRegistered = std::async(std::launch::async, [&] () {
            Account::VccsAccountStateChangedEvent evt;
            Account::VccsAccountHandle h;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
            ASSERT_TRUE( evt.oldState == Account::VccsAccountState_Unregistered );
            ASSERT_TRUE( evt.newState == Account::VccsAccountState_Registering );

            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
            ASSERT_TRUE( evt.oldState == Account::VccsAccountState_Registering );
            ASSERT_TRUE( evt.newState == Account::VccsAccountState_Registered );
         });
         waitFor( aliceRegistered );
      }

      // Query for the list of conferences
      alice.vccsConferenceManager->queryConferenceList( alice.vccsAccountHandle );

      // Wait for the list of conferences to be returned
      {
         auto aliceConfQuery = std::async(std::launch::async, [&] () {
            Conference::ConferenceListUpdatedEvent evt;
            Account::VccsAccountHandle h;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsConferenceHandler::onConferenceListUpdated",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
         });
         waitFor( aliceConfQuery );
      }

      // Disable the account, and wait for the state to go back to Unregistered
      alice.vccsAccountManager->disable( alice.vccsAccountHandle );
      {
         auto aliceUnRegistered = std::async(std::launch::async, [&] () {
            Account::VccsAccountStateChangedEvent evt;
            Account::VccsAccountHandle h;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
            ASSERT_TRUE( evt.oldState == Account::VccsAccountState_Registered );
            ASSERT_TRUE( evt.newState == Account::VccsAccountState_Unregistered );
         });
         waitFor( aliceUnRegistered );
      }
   }

   TEST_F(VccsModuleTest, BadCredentials) {

      // NB: "alice" and "bob" are actually the same user (since syncing
      // really happens between different registrations of the same user)
      TestAccount alice("alice", Account_Init);

      // Set the settings
      Account::VccsAccountSettings settings;
      //settings.webSocketURL   = "ws://***********:8989/join";
      settings.wsSettings.webSocketURL   = "wss://127.0.0.1:8989/join";
      settings.wsSettings.certMode       = WebSocket::CertVerificationMode_None;
      settings.group          = "imap.mobilevoiplive.com";
      settings.userName       = "<EMAIL>";
      settings.password       = "vccs0007";
      settings.displayName    = "Smokey the Bear";
      // sip Aor is "+<EMAIL>";

      alice.vccsAccountManager->configureDefaultAccountSettings( alice.vccsAccountHandle, settings );
      alice.vccsAccountManager->applySettings( alice.vccsAccountHandle ); // prob. not needed

      // Start the test harness.
      VccsTestHarness harness( TestEnvironmentConfig::testResourcePath() + "vccs_test_harness_scenarios/VCCS_BadCredentials.dat", 8989, true );
      harness.start();

      // Enable the account.
      alice.vccsAccountManager->enable( alice.vccsAccountHandle );

      // Wait for the account to reach the registered state
      {
         auto aliceRegistered = std::async(std::launch::async, [&] () noexcept {
            Account::VccsAccountStateChangedEvent evt;
            Account::VccsAccountHandle h;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
            ASSERT_TRUE( evt.oldState == Account::VccsAccountState_Unregistered );
            ASSERT_TRUE( evt.newState == Account::VccsAccountState_Registering );

            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
            ASSERT_TRUE( evt.oldState == Account::VccsAccountState_Registering );
            ASSERT_TRUE( evt.newState == Account::VccsAccountState_Unregistered );
         });
         waitFor( aliceRegistered );
      }
   }

   TEST_F(VccsModuleTest, ConnectionTimeout) {

      // NB: "alice" and "bob" are actually the same user (since syncing
      // really happens between different registrations of the same user)
      TestAccount alice("alice", Account_Init);

      // Set the settings
      Account::VccsAccountSettings settings;
      //settings.webSocketURL   = "ws://***********:8989/join";
      settings.wsSettings.webSocketURL   = "wss://" + TestEnvironmentConfig::unreachableV4Ip() + ":8989/join";
      settings.wsSettings.certMode       = WebSocket::CertVerificationMode_None;
      settings.group          = "imap.mobilevoiplive.com";
      settings.userName       = "<EMAIL>";
      settings.password       = "vccs0007";
      settings.displayName    = "Smokey the Bear";
      // sip Aor is "+<EMAIL>";

      alice.vccsAccountManager->configureDefaultAccountSettings( alice.vccsAccountHandle, settings );
      alice.vccsAccountManager->applySettings( alice.vccsAccountHandle ); // prob. not needed

      // don't start the test harness
      
      // Enable the account.
      alice.vccsAccountManager->enable( alice.vccsAccountHandle );

      {
         auto aliceRegistered = std::async(std::launch::async, [&] () noexcept {
            Account::VccsAccountStateChangedEvent evt;
            Account::VccsAccountHandle h;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
            ASSERT_TRUE( evt.oldState == Account::VccsAccountState_Unregistered );
            ASSERT_TRUE( evt.newState == Account::VccsAccountState_Registering );

            std::this_thread::sleep_for(std::chrono::seconds(30));
         });
         waitFor( aliceRegistered );
      }
   }

   // This is intended to be run with an invalid DNS server so DNS queries will timeout.
   TEST_F(VccsModuleTest, HardShutdown) {

      // NB: "alice" and "bob" are actually the same user (since syncing
      // really happens between different registrations of the same user)
      TestAccount alice("alice", Account_Init);

      // Set the settings
      Account::VccsAccountSettings settings;
      //settings.webSocketURL   = "ws://***********:8989/join";
      settings.wsSettings.webSocketURL = "wss://foobar1.net:8989/join";
      settings.wsSettings.certMode = WebSocket::CertVerificationMode_None;
      settings.group = "imap.mobilevoiplive.com";
      settings.userName = "<EMAIL>";
      settings.password = "vccs0007";
      settings.displayName = "Smokey the Bear";
      // sip Aor is "+<EMAIL>";

      alice.vccsAccountManager->configureDefaultAccountSettings(alice.vccsAccountHandle, settings);
      alice.vccsAccountManager->applySettings(alice.vccsAccountHandle); // prob. not needed

      // Start the test harness.
      VccsTestHarness harness(TestEnvironmentConfig::testResourcePath() + "vccs_test_harness_scenarios/VCCS_BadCredentials.dat", 8989, true);
      harness.start();

      alice.vccsAccountManager->enable(alice.vccsAccountHandle);
      std::this_thread::sleep_for(std::chrono::milliseconds(4000));
      alice.vccsAccountManager->destroy(alice.vccsAccountHandle);

      auto aliceUnRegistered = std::async(std::launch::async, [&]() {
         Account::VccsAccountStateChangedEvent evt;
         Account::VccsAccountHandle h;
         ASSERT_TRUE(cpcWaitForEvent(
            alice.vccsEvents,
            "VccsAccountHandler::onAccountStateChanged",
            15000,
            AlwaysTruePred(),
            h, evt));
         ASSERT_TRUE(evt.oldState == Account::VccsAccountState_Unregistered);
         ASSERT_TRUE(evt.newState == Account::VccsAccountState_Registering);
      });
      waitFor(aliceUnRegistered);
   }

   TEST_F(VccsModuleTest, DecodeProvisioning)
   {
      TestAccount alice("alice", Account_Init);
      
      std::ifstream in((TestEnvironmentConfig::testResourcePath() + "vccsAccountSettings.json").c_str());
      assert(in.is_open());
      
      std::ostringstream iss;
      iss << in.rdbuf() << std::flush;
      
      cpc::string doc = iss.str().c_str();
      cpc::vector<Account::VccsAccountSettings> decodedsettings;
      
      int ret = alice.vccsAccountManager->decodeProvisioningResponse(doc, decodedsettings);
      ASSERT_EQ(ret, kSuccess);
      ASSERT_EQ(decodedsettings[0].userName, "AutoTest");
      ASSERT_EQ(decodedsettings[0].password, "AutoTestPwd");
      ASSERT_EQ(decodedsettings[0].group, "counterpath.com");
      ASSERT_EQ(decodedsettings[0].wsSettings.pingIntervalSeconds, 50);
      ASSERT_EQ(decodedsettings[0].wsSettings.initialRetryIntervalSeconds, 5);
      ASSERT_EQ(decodedsettings[0].wsSettings.maxRetryIntervalSeconds, 800);
      ASSERT_EQ(decodedsettings[0].wsSettings.certMode, 0);
      ASSERT_EQ(decodedsettings[0].wsSettings.logPayload, true);
      ASSERT_EQ(decodedsettings[0].wsSettings.backgroundSocketsIfPossible, false);
      ASSERT_EQ(decodedsettings[0].wsSettings.isLoginRequired, true);
   }
   
   TEST_F(VccsModuleTest, ConferenceControls) {

      // NB: "alice" and "bob" are actually the same user (since syncing
      // really happens between different registrations of the same user)
      TestAccount alice("alice", Account_Init);

      // Set the settings
      Account::VccsAccountSettings settings;
      //settings.webSocketURL   = "ws://***********:8989/join";
      settings.wsSettings.webSocketURL   = "wss://127.0.0.1:8989/join";
      settings.wsSettings.certMode       = WebSocket::CertVerificationMode_None;
      settings.group          = "imap.mobilevoiplive.com";
      settings.userName       = "<EMAIL>";
      settings.password       = "vccs0007";
      settings.displayName    = "Smokey the Bear";
      // sip Aor is "+<EMAIL>";

      alice.vccsAccountManager->configureDefaultAccountSettings( alice.vccsAccountHandle, settings );
      alice.vccsAccountManager->applySettings( alice.vccsAccountHandle ); // prob. not needed

      // Start the test harness.
      VccsTestHarness harness( TestEnvironmentConfig::testResourcePath() + "vccs_test_harness_scenarios/VCCS_ConferenceControls.dat", 8989, true );
      harness.start();

      // Enable the account.
      alice.vccsAccountManager->enable( alice.vccsAccountHandle );

      // Wait for the account to reach the registered state
      {
         auto aliceRegistered = std::async(std::launch::async, [&] () {
            Account::VccsAccountStateChangedEvent evt;
            Account::VccsAccountHandle h;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
            ASSERT_TRUE( evt.oldState == Account::VccsAccountState_Unregistered );
            ASSERT_TRUE( evt.newState == Account::VccsAccountState_Registering );

            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
            ASSERT_TRUE( evt.oldState == Account::VccsAccountState_Registering );
            ASSERT_TRUE( evt.newState == Account::VccsAccountState_Registered );
         });
         waitFor( aliceRegistered );
      }

      // Query for the list of conferences
      alice.vccsConferenceManager->queryConferenceList( alice.vccsAccountHandle );

      // Fetch these from the first conference we find
      cpc::string bridgeNumber;
      cpc::string pin;
      bool isModerator( false );
      Conference::VccsConferenceHandle hConference( -1 );

      // Wait for the list of conferences to be returned
      {
         Conference::ConferenceListUpdatedEvent evt;
         Account::VccsAccountHandle h;

         auto aliceConfQuery = std::async(std::launch::async, [&] () {
            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsConferenceHandler::onConferenceListUpdated",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
         });
         waitFor( aliceConfQuery );

         for( cpc::vector< Conference::ConferenceListChange >::iterator iter = evt.changes.begin() ;
              iter != evt.changes.end() ; ++iter )
         {
            if( iter->changeType != Conference::ChangeType_Query )
               continue;

            // remember the information we need for this conference
            Conference::ConferenceDetails &details( iter->conference );
            bridgeNumber = details.bridgeNumber;
            pin = details.moderatorPin; // *I'M SPARTACUS!*
            hConference = details.id;
            isModerator = details.socketModerator;
            break;
         }
      }

      ASSERT_TRUE( isModerator );
      ASSERT_TRUE( bridgeNumber.size() > 0 );
      ASSERT_TRUE( pin.size() > 0 );

      // Subscribe to the conference from which we want events
      alice.vccsConferenceManager->subscribe( alice.vccsAccountHandle, bridgeNumber, pin );
      auto aliceSubscribe = std::async(std::launch::async, [&] () {
         Conference::SubscribeEvent evt;
         Account::VccsAccountHandle h;
         ASSERT_TRUE( cpcWaitForEvent(
            alice.vccsEvents,
            "VccsConferenceHandler::onSubscribe",
            15000,
            AlwaysTruePred( ),
            h, evt ) );
      });
      waitFor( aliceSubscribe );

      // Set the mute lock on a conference
      alice.vccsConferenceManager->setMuteLock( alice.vccsAccountHandle, hConference, true );
      auto aliceMuteLock = std::async(std::launch::async, [&] () {
         Conference::ConferenceListUpdatedEvent evt;
         Account::VccsAccountHandle h;
         ASSERT_TRUE( cpcWaitForEvent(
            alice.vccsEvents,
            "VccsConferenceHandler::onConferenceListUpdated",
            15000,
            AlwaysTruePred( ),
            h, evt ) );
      });
      waitFor( aliceMuteLock );

      // Unset the mute lock
      alice.vccsConferenceManager->setMuteLock( alice.vccsAccountHandle, hConference, false );
      auto aliceMuteUnLock = std::async(std::launch::async, [&] () {
         Conference::ConferenceListUpdatedEvent evt;
         Account::VccsAccountHandle h;
         ASSERT_TRUE( cpcWaitForEvent(
            alice.vccsEvents,
            "VccsConferenceHandler::onConferenceListUpdated",
            15000,
            AlwaysTruePred( ),
            h, evt ) );
      });
      waitFor( aliceMuteUnLock );

      // Set the participant lock on a conference
      alice.vccsConferenceManager->setParticipantLock( alice.vccsAccountHandle, hConference, true );
      auto aliceParticipantLock = std::async(std::launch::async, [&] () {
         Conference::ConferenceListUpdatedEvent evt;
         Account::VccsAccountHandle h;
         ASSERT_TRUE( cpcWaitForEvent(
            alice.vccsEvents,
            "VccsConferenceHandler::onConferenceListUpdated",
            15000,
            AlwaysTruePred( ),
            h, evt ) );
      });
      waitFor( aliceParticipantLock );

      // Unset the participant lock on a conference
      alice.vccsConferenceManager->setParticipantLock( alice.vccsAccountHandle, hConference, false );
      auto aliceParticipantUnLock = std::async(std::launch::async, [&] () {
         Conference::ConferenceListUpdatedEvent evt;
         Account::VccsAccountHandle h;
         ASSERT_TRUE( cpcWaitForEvent(
            alice.vccsEvents,
            "VccsConferenceHandler::onConferenceListUpdated",
            15000,
            AlwaysTruePred( ),
            h, evt ) );
      });
      waitFor( aliceParticipantUnLock );

      // Set the conference as 'hosted'
      alice.vccsConferenceManager->setHosted( alice.vccsAccountHandle, hConference, true );
      auto aliceHosted = std::async(std::launch::async, [&] () {
         Conference::ConferenceListUpdatedEvent evt;
         Account::VccsAccountHandle h;
         ASSERT_TRUE( cpcWaitForEvent(
            alice.vccsEvents,
            "VccsConferenceHandler::onConferenceListUpdated",
            15000,
            AlwaysTruePred( ),
            h, evt ) );
      });
      waitFor( aliceHosted );

      // Set the conference as 'unhosted'
      alice.vccsConferenceManager->setHosted( alice.vccsAccountHandle, hConference, false );
      auto aliceUnHosted = std::async(std::launch::async, [&] () {
         Conference::ConferenceListUpdatedEvent evt;
         Account::VccsAccountHandle h;
         ASSERT_TRUE( cpcWaitForEvent(
            alice.vccsEvents,
            "VccsConferenceHandler::onConferenceListUpdated",
            15000,
            AlwaysTruePred( ),
            h, evt ) );
      });
      waitFor( aliceUnHosted );

      // Disable entry exit tones
      alice.vccsConferenceManager->setEntryExitTonesEnabled(alice.vccsAccountHandle, hConference, false);
      auto aliceTonesDisable = std::async(std::launch::async, [&]() {
         Conference::ConferenceListUpdatedEvent evt;
         Account::VccsAccountHandle h;
         ASSERT_TRUE(cpcWaitForEvent(
            alice.vccsEvents,
            "VccsConferenceHandler::onConferenceListUpdated",
            15000,
            AlwaysTruePred(),
            h, evt));
      });
      waitFor(aliceTonesDisable);

      // Enable entry exit tones
      alice.vccsConferenceManager->setEntryExitTonesEnabled(alice.vccsAccountHandle, hConference, true);
      auto aliceTonesEnable = std::async(std::launch::async, [&]() {
         Conference::ConferenceListUpdatedEvent evt;
         Account::VccsAccountHandle h;
         ASSERT_TRUE(cpcWaitForEvent(
            alice.vccsEvents,
            "VccsConferenceHandler::onConferenceListUpdated",
            15000,
            AlwaysTruePred(),
            h, evt));
      });
      waitFor(aliceTonesEnable);

      // Wait for an unsolicited notification about the conference 'mode'
      auto aliceConfMode = std::async(std::launch::async, [&]() {
         Conference::ConferenceModeEvent evt;
         Conference::VccsConferenceHandle h;
         ASSERT_TRUE(cpcWaitForEvent(
            alice.vccsEvents,
            "VccsConferenceHandler::onConferenceModeUpdated",
            15000,
            AlwaysTruePred(),
            h, evt));
         ASSERT_TRUE(evt.participantLock);
      });
      waitFor(aliceConfMode);
      
      // Wait for an unsolicited notification about the conference 'mode' -- this time without participantLock specified
      aliceConfMode = std::async(std::launch::async, [&]() {
         Conference::ConferenceModeEvent evt;
         Conference::VccsConferenceHandle h;
         ASSERT_TRUE(cpcWaitForEvent(
            alice.vccsEvents,
            "VccsConferenceHandler::onConferenceModeUpdated",
            15000,
            AlwaysTruePred(),
            h, evt));
         ASSERT_FALSE(evt.participantLock);
      });
      waitFor(aliceConfMode);

      // Unsubscribe from events
      alice.vccsConferenceManager->unsubscribe( alice.vccsAccountHandle, bridgeNumber );
      auto aliceUnsubscribe = std::async(std::launch::async, [&] () {
         Conference::UnsubscribeEvent evt;
         Account::VccsAccountHandle h;
         ASSERT_TRUE( cpcWaitForEvent(
            alice.vccsEvents,
            "VccsConferenceHandler::onUnsubscribe",
            15000,
            AlwaysTruePred( ),
            h, evt ) );
      });
      waitFor( aliceUnsubscribe );

      // Disable the account, and wait for the state to go back to Unregistered
      alice.vccsAccountManager->disable( alice.vccsAccountHandle );
      {
         auto aliceUnRegistered = std::async(std::launch::async, [&] () {
            Account::VccsAccountStateChangedEvent evt;
            Account::VccsAccountHandle h;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
            ASSERT_TRUE( evt.oldState == Account::VccsAccountState_Registered );
            ASSERT_TRUE( evt.newState == Account::VccsAccountState_Unregistered );
         });
         waitFor( aliceUnRegistered );
      }
   }

   TEST_F(VccsModuleTest, ParticipantControls) {

      // NB: "alice" and "bob" are actually the same user (since syncing
      // really happens between different registrations of the same user)
      TestAccount alice("alice", Account_Init);

      // Set the settings
      Account::VccsAccountSettings settings;
      //settings.webSocketURL   = "ws://***********:8989/join";
      settings.wsSettings.webSocketURL   = "wss://127.0.0.1:8989/join";
      settings.wsSettings.certMode       = WebSocket::CertVerificationMode_None;
      settings.group          = "imap.mobilevoiplive.com";
      settings.userName       = "<EMAIL>";
      settings.password       = "vccs0007";
      settings.displayName    = "Smokey the Bear";
      // sip Aor is "+<EMAIL>";

      alice.vccsAccountManager->configureDefaultAccountSettings( alice.vccsAccountHandle, settings );
      alice.vccsAccountManager->applySettings( alice.vccsAccountHandle ); // prob. not needed

      // Start the test harness.
      VccsTestHarness harness( TestEnvironmentConfig::testResourcePath() + "vccs_test_harness_scenarios/VCCS_ParticipantControls.dat", 8989, true );
      harness.start();

      // Enable the account.
      alice.vccsAccountManager->enable( alice.vccsAccountHandle );

      // Wait for the account to reach the registered state
      {
         auto aliceRegistered = std::async(std::launch::async, [&] () {
            Account::VccsAccountStateChangedEvent evt;
            Account::VccsAccountHandle h;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
            ASSERT_TRUE( evt.oldState == Account::VccsAccountState_Unregistered );
            ASSERT_TRUE( evt.newState == Account::VccsAccountState_Registering );

            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
            ASSERT_TRUE( evt.oldState == Account::VccsAccountState_Registering );
            ASSERT_TRUE( evt.newState == Account::VccsAccountState_Registered );
         });
         waitFor( aliceRegistered );
      }

      // Query for the list of conferences
      alice.vccsConferenceManager->queryConferenceList( alice.vccsAccountHandle );

      // Fetch these from the first conference we find
      Conference::ConferenceDetails details;

      // Wait for the list of conferences to be returned
      {
         Conference::ConferenceListUpdatedEvent evt;
         Account::VccsAccountHandle h;

         auto aliceConfQuery = std::async(std::launch::async, [&] () {
            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsConferenceHandler::onConferenceListUpdated",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
         });
         waitFor( aliceConfQuery );

         for( cpc::vector< Conference::ConferenceListChange >::iterator iter = evt.changes.begin() ;
              iter != evt.changes.end() ; ++iter )
         {
            if( iter->changeType != Conference::ChangeType_Query )
               continue;

            // remember the information we need for this conference
            details = iter->conference;
            break;
         }
      }

      ASSERT_TRUE( details.socketModerator );
      ASSERT_TRUE( details.bridgeNumber.size() > 0 );
      ASSERT_TRUE( details.moderatorPin.size() > 0 );
      ASSERT_TRUE( details.id != -1 );
      ASSERT_TRUE( details.participants.size() > 0 );

      // Make sure we have some capabilities for the participant
      ASSERT_TRUE( details.participants[ 0 ].capabilities != 0 );

      // Subscribe to the conference from which we want events
      alice.vccsConferenceManager->subscribe( alice.vccsAccountHandle, details.bridgeNumber, details.moderatorPin );
      auto aliceSubscribe = std::async(std::launch::async, [&] () {
         Conference::SubscribeEvent evt;
         Account::VccsAccountHandle h;
         ASSERT_TRUE( cpcWaitForEvent(
            alice.vccsEvents,
            "VccsConferenceHandler::onSubscribe",
            15000,
            AlwaysTruePred( ),
            h, evt ) );
      });
      waitFor( aliceSubscribe );

      // Mute the first participant
      alice.vccsConferenceManager->muteParticipant( alice.vccsAccountHandle, details.id, details.participants[ 0 ].participantNumber, true );
      auto aliceMuteParticipant = std::async(std::launch::async, [&] () {
         Conference::ParticipantListUpdatedEvent evt;
         Conference::VccsConferenceHandle h;
         ASSERT_TRUE( cpcWaitForEvent(
            alice.vccsEvents,
            "VccsConferenceHandler::onParticipantListUpdated",
            15000,
            AlwaysTruePred( ),
            h, evt ) );
      });
      waitFor( aliceMuteParticipant );

      // Unmute the first participant
      alice.vccsConferenceManager->muteParticipant( alice.vccsAccountHandle, details.id, details.participants[ 0 ].participantNumber, false );
      auto aliceUnmuteParticipant = std::async(std::launch::async, [&] () {
         Conference::ParticipantListUpdatedEvent evt;
         Conference::VccsConferenceHandle h;
         ASSERT_TRUE( cpcWaitForEvent(
            alice.vccsEvents,
            "VccsConferenceHandler::onParticipantListUpdated",
            15000,
            AlwaysTruePred( ),
            h, evt ) );
      });
      waitFor( aliceUnmuteParticipant );

      // Set the participant to recording state
      alice.vccsConferenceManager->setIsRecording( alice.vccsAccountHandle, details.id, details.participants[ 0 ].participantNumber, true );
      auto aliceSetRecording = std::async(std::launch::async, [&] () {
         Conference::ParticipantListUpdatedEvent evt;
         Conference::VccsConferenceHandle h;
         ASSERT_TRUE( cpcWaitForEvent(
            alice.vccsEvents,
            "VccsConferenceHandler::onParticipantListUpdated",
            15000,
            AlwaysTruePred( ),
            h, evt ) );
      });
      waitFor( aliceSetRecording );

      // Set the participant to NOT recording state
      alice.vccsConferenceManager->setIsRecording( alice.vccsAccountHandle, details.id, details.participants[ 0 ].participantNumber, false );
      auto aliceSetNotRecording = std::async(std::launch::async, [&] () {
         Conference::ParticipantListUpdatedEvent evt;
         Conference::VccsConferenceHandle h;
         ASSERT_TRUE( cpcWaitForEvent(
            alice.vccsEvents,
            "VccsConferenceHandler::onParticipantListUpdated",
            15000,
            AlwaysTruePred( ),
            h, evt ) );
      });
      waitFor( aliceSetNotRecording );

      // Kick the participant from the conference
      alice.vccsConferenceManager->kickParticipant( alice.vccsAccountHandle, details.id, details.participants[ 0 ].participantNumber );
      auto aliceKickParticipant = std::async(std::launch::async, [&] () {
         Conference::ParticipantListUpdatedEvent evt;
         Conference::VccsConferenceHandle h;
         ASSERT_TRUE( cpcWaitForEvent(
            alice.vccsEvents,
            "VccsConferenceHandler::onParticipantListUpdated",
            15000,
            AlwaysTruePred( ),
            h, evt ) );

         Conference::ConferenceListUpdatedEvent clue;
         Account::VccsAccountHandle ah;
         ASSERT_TRUE( cpcWaitForEvent(
            alice.vccsEvents,
            "VccsConferenceHandler::onConferenceListUpdated",
            15000,
            AlwaysTruePred( ),
            ah, clue ) );
      });
      waitFor( aliceKickParticipant );

      // Unsubscribe from events
      alice.vccsConferenceManager->unsubscribe( alice.vccsAccountHandle, details.bridgeNumber );
      auto aliceUnsubscribe = std::async(std::launch::async, [&] () {
         Conference::UnsubscribeEvent evt;
         Account::VccsAccountHandle h;
         ASSERT_TRUE( cpcWaitForEvent(
            alice.vccsEvents,
            "VccsConferenceHandler::onUnsubscribe",
            15000,
            AlwaysTruePred( ),
            h, evt ) );
      });
      waitFor( aliceUnsubscribe );

      // Disable the account, and wait for the state to go back to Unregistered
      alice.vccsAccountManager->disable( alice.vccsAccountHandle );
      {
         auto aliceUnRegistered = std::async(std::launch::async, [&] () {
            Account::VccsAccountStateChangedEvent evt;
            Account::VccsAccountHandle h;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
            ASSERT_TRUE( evt.oldState == Account::VccsAccountState_Registered );
            ASSERT_TRUE( evt.newState == Account::VccsAccountState_Unregistered );
         });
         waitFor( aliceUnRegistered );
      }
   }

   TEST_F(VccsModuleTest, URLCracking) {

      // NB: "alice" and "bob" are actually the same user (since syncing
      // really happens between different registrations of the same user)
      TestAccount alice("alice", Account_Init);

      const char* urls[] = {
         "http://vccs.counterpath.com:8989/join/**********",
         "http://vccs.counterpath.com:8989/join/**********/group.com",
         "https://vccs.counterpath.com:8989/join/**********/group.com",
         "http://vccs.counterpath.com:8989/join/**********?vccs=true",
         "http://vccs.counterpath.com:8989/join/**********/group.com?vccs=true",
         "https://vccs.counterpath.com:8989/join/**********/group.com?vccs=true",
         "vccs://vccs.counterpath.com:8989/join/**********/group.com",
         "vccs://vccs.counterpath.com:8989/join/**********?vccs=true",
         "asdf978a4wbna345@%^&^:/234//2345//23762"
      };

      // validity of the URLs
      const bool results[] = {
         false,
         true,
         true,
         false,
         true,
         true,
         true,
         false,
         false
      };

      cpc::string outWebSocketURL;
      cpc::string outServerName;
      int outPortNumber;
      cpc::string outGroupName;
      cpc::string outSubscriptionCode;
      bool result;

      for( int i = 0 ; i < 9 ; ++i )
      {
         const cpc::string url( urls[ i ] );

         result = alice.vccsAccountManager->crackVCCSURL( url, false, outWebSocketURL, outServerName, outPortNumber, outGroupName, outSubscriptionCode );
         ASSERT_TRUE( result == results[ i ] );
      }
      
      cpc::string url;
      
      url = "vccs://vccs.counterpath.com:8989/join/**********/group.com";
      ASSERT_TRUE( alice.vccsAccountManager->crackVCCSURL( url, "vccs", false, outWebSocketURL, outServerName, outPortNumber, outGroupName, outSubscriptionCode ) );
      ASSERT_EQ(outPortNumber, 8989);
      
      url = "vccs://vccs.counterpath.com/join/**********/group.com";
      ASSERT_TRUE( alice.vccsAccountManager->crackVCCSURL( url, "vccs", false, outWebSocketURL, outServerName, outPortNumber, outGroupName, outSubscriptionCode ) );
      ASSERT_EQ(outPortNumber, 443);
      
      url = "https://vccs.counterpath.com:8989/join/**********/group.com";
      ASSERT_TRUE( alice.vccsAccountManager->crackVCCSURL( url, "vccs", false, outWebSocketURL, outServerName, outPortNumber, outGroupName, outSubscriptionCode ) );
      ASSERT_EQ(outPortNumber, 8989);
      
      url = "https://vccs.counterpath.com/join/**********/group.com";
      ASSERT_TRUE( alice.vccsAccountManager->crackVCCSURL( url, "vccs", false, outWebSocketURL, outServerName, outPortNumber, outGroupName, outSubscriptionCode ) );
      ASSERT_EQ(outPortNumber, 443);
   }

   TEST_F(VccsModuleTest, Invite) {

      // NB: "alice" and "bob" are actually the same user (since syncing
      // really happens between different registrations of the same user)
      TestAccount alice("alice", Account_Init);

      // Set the settings
      Account::VccsAccountSettings settings;
      //settings.webSocketURL   = "ws://***********:8989/join";
      settings.wsSettings.webSocketURL   = "wss://127.0.0.1:8989/join";
      settings.wsSettings.certMode       = WebSocket::CertVerificationMode_None;
      settings.group          = "imap.mobilevoiplive.com";
      settings.userName       = "<EMAIL>";
      settings.password       = "vccs0007";
      settings.displayName    = "Smokey the Bear";
      // sip Aor is "+<EMAIL>";

      alice.vccsAccountManager->configureDefaultAccountSettings( alice.vccsAccountHandle, settings );
      alice.vccsAccountManager->applySettings( alice.vccsAccountHandle ); // prob. not needed

      // Start the test harness.
      VccsTestHarness harness( TestEnvironmentConfig::testResourcePath() + "vccs_test_harness_scenarios/VCCS_Invite.dat", 8989, true );
      harness.start();

      // Enable the account.
      alice.vccsAccountManager->enable( alice.vccsAccountHandle );

      // Wait for the account to reach the registered state
      {
         auto aliceRegistered = std::async(std::launch::async, [&] () {
            Account::VccsAccountStateChangedEvent evt;
            Account::VccsAccountHandle h;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
            ASSERT_TRUE( evt.oldState == Account::VccsAccountState_Unregistered );
            ASSERT_TRUE( evt.newState == Account::VccsAccountState_Registering );

            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
            ASSERT_TRUE( evt.oldState == Account::VccsAccountState_Registering );
            ASSERT_TRUE( evt.newState == Account::VccsAccountState_Registered );
         });
         waitFor( aliceRegistered );
      }

      // Query for the list of conferences
      alice.vccsConferenceManager->queryConferenceList( alice.vccsAccountHandle );

      // Fetch these from the first conference we find
      Conference::VccsConferenceHandle hConference( -1 );

      // Wait for the list of conferences to be returned
      {
         Conference::ConferenceListUpdatedEvent evt;
         Account::VccsAccountHandle h;

         auto aliceConfQuery = std::async(std::launch::async, [&] () {
            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsConferenceHandler::onConferenceListUpdated",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
         });
         waitFor( aliceConfQuery );

         for( cpc::vector< Conference::ConferenceListChange >::iterator iter = evt.changes.begin() ;
              iter != evt.changes.end() ; ++iter )
         {
            if( iter->changeType != Conference::ChangeType_Query )
               continue;

            // remember the information we need for this conference
            Conference::ConferenceDetails &details( iter->conference );
            hConference = details.id;
            break;
         }
      }

      // Wait for the invite to be returned
      Conference::ConferenceInviteEvent cie;
      alice.vccsConferenceManager->queryConferenceInvite( alice.vccsAccountHandle, hConference );
      {
         Conference::VccsConferenceHandle h;

         auto aliceConfInvite = std::async(std::launch::async, [&] () {
            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsConferenceHandler::onQueryConferenceInvite",
               15000,
               AlwaysTruePred( ),
               h, cie ) );
         });
         waitFor( aliceConfInvite );
      }

      ASSERT_TRUE( cie.htmlInvite.size() > 0 );
      ASSERT_TRUE( cie.textInvite.size() > 0 );
      ASSERT_TRUE( cie.joinUrl.size() > 0 );

      // Close the session with the VCCS
      alice.vccsAccountManager->disable( alice.vccsAccountHandle );
      {
         auto aliceUnRegistered = std::async(std::launch::async, [&] () {
            Account::VccsAccountStateChangedEvent evt;
            Account::VccsAccountHandle h;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
            ASSERT_TRUE( evt.oldState == Account::VccsAccountState_Registered );
            ASSERT_TRUE( evt.newState == Account::VccsAccountState_Unregistered );
         });
         waitFor( aliceUnRegistered );
      }
   }

   TEST_F(VccsModuleTest, NetworkChange) {

      // NB: "alice" and "bob" are actually the same user (since syncing
      // really happens between different registrations of the same user)
      TestAccount alice("alice", Account_Init);

      // Set the settings
      Account::VccsAccountSettings settings;
      //settings.webSocketURL   = "ws://***********:8989/join";
      settings.wsSettings.webSocketURL = "wss://127.0.0.1:8989/join";
      settings.wsSettings.certMode = WebSocket::CertVerificationMode_None;
      settings.group = "imap.mobilevoiplive.com";
      settings.userName = "<EMAIL>";
      settings.password = "vccs0007";
      settings.displayName = "Smokey the Bear";
      // sip Aor is "+<EMAIL>";

      alice.vccsAccountManager->configureDefaultAccountSettings(alice.vccsAccountHandle, settings);
      alice.vccsAccountManager->applySettings(alice.vccsAccountHandle); // prob. not needed

                                                                        // Start the test harness.
      VccsTestHarness harness( TestEnvironmentConfig::testResourcePath() + "vccs_test_harness_scenarios/VCCS_NetworkChange.dat", 8989, true);
      harness.start();

      // Enable the account.
      alice.vccsAccountManager->enable(alice.vccsAccountHandle);

      // Wait for the account to reach the registered state
      {
         auto aliceRegistered = std::async(std::launch::async, [&]() {
            Account::VccsAccountStateChangedEvent evt;
            Account::VccsAccountHandle h;
            ASSERT_TRUE(cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred(),
               h, evt));
            ASSERT_TRUE(evt.oldState == Account::VccsAccountState_Unregistered);
            ASSERT_TRUE(evt.newState == Account::VccsAccountState_Registering);

            ASSERT_TRUE(cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred(),
               h, evt));
            ASSERT_TRUE(evt.oldState == Account::VccsAccountState_Registering);
            ASSERT_TRUE(evt.newState == Account::VccsAccountState_Registered);
         });
         waitFor(aliceRegistered);
      }


      // simulate a network change
      std::set<resip::Data> ifaces;
      ifaces.insert("**************");
      alice.network->setMockInterfaces(ifaces);
      alice.network->setNetworkTransport(NetworkTransport::TransportWiFi);

      // Wait for the account to reach the registered state
      {
         auto aliceRegistered = std::async(std::launch::async, [&]() {
            Account::VccsAccountStateChangedEvent evt;
            Account::VccsAccountHandle h;

            ASSERT_TRUE(cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred(),
               h, evt));
            ASSERT_TRUE(evt.oldState == Account::VccsAccountState_Registered);
            ASSERT_TRUE(evt.newState == Account::VccsAccountState_WaitingToRegister);

            ASSERT_TRUE(cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred(),
               h, evt));
            ASSERT_TRUE(evt.oldState == Account::VccsAccountState_WaitingToRegister);
            ASSERT_TRUE(evt.newState == Account::VccsAccountState_Registering);

            ASSERT_TRUE(cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred(),
               h, evt));
            ASSERT_TRUE(evt.oldState == Account::VccsAccountState_Registering);
            ASSERT_TRUE(evt.newState == Account::VccsAccountState_Registered);
         });
         waitFor(aliceRegistered);
      }
   }

   TEST_F(VccsModuleTest, GroupChatConfig) {

      // NB: "alice" and "bob" are actually the same user (since syncing
      // really happens between different registrations of the same user)
      TestAccount alice("alice", Account_Init);

      // Set the settings
      Account::VccsAccountSettings settings;
      //settings.webSocketURL   = "ws://***********:8989/join";
      settings.wsSettings.webSocketURL   = "wss://127.0.0.1:8989/join";
      settings.wsSettings.certMode       = WebSocket::CertVerificationMode_None;
      settings.group          = "imap.mobilevoiplive.com";
      settings.userName       = "<EMAIL>";
      settings.password       = "vccs0007";
      settings.displayName    = "Smokey the Bear";
      // sip Aor is "+<EMAIL>";

      alice.vccsAccountManager->configureDefaultAccountSettings( alice.vccsAccountHandle, settings );
      alice.vccsAccountManager->applySettings( alice.vccsAccountHandle ); // prob. not needed

      // Start the test harness.
      VccsTestHarness harness( TestEnvironmentConfig::testResourcePath() + "vccs_test_harness_scenarios/VCCS_GroupChatConfig.dat", 8989, true );
      harness.start();

      // Enable the account.
      alice.vccsAccountManager->enable( alice.vccsAccountHandle );

      // Wait for the account to reach the registered state
      {
         auto aliceRegistered = std::async(std::launch::async, [&] () {
            Account::VccsAccountStateChangedEvent evt;
            Account::VccsAccountHandle h;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
            ASSERT_TRUE( evt.oldState == Account::VccsAccountState_Unregistered );
            ASSERT_TRUE( evt.newState == Account::VccsAccountState_Registering );

            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
            ASSERT_TRUE( evt.oldState == Account::VccsAccountState_Registering );
            ASSERT_TRUE( evt.newState == Account::VccsAccountState_Registered );
         });
         waitFor( aliceRegistered );
      }

      // Query for the list of conferences
      alice.vccsConferenceManager->queryConferenceList( alice.vccsAccountHandle );

      // Fetch these from the first conference we find
      Conference::VccsConferenceHandle hConference( -1 );

      // Wait for the list of conferences to be returned
      {
         Conference::ConferenceListUpdatedEvent evt;
         Account::VccsAccountHandle h;

         auto aliceConfQuery = std::async(std::launch::async, [&] () {
            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsConferenceHandler::onConferenceListUpdated",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
         });
         waitFor( aliceConfQuery );

         for( cpc::vector< Conference::ConferenceListChange >::iterator iter = evt.changes.begin() ;
              iter != evt.changes.end() ; ++iter )
         {
            if( iter->changeType != Conference::ChangeType_Query )
               continue;

            // remember the information we need for this conference
            Conference::ConferenceDetails &details( iter->conference );
            hConference = details.id;
            break;
         }
      }

      // Wait for the config to be returned
      Conference::XMPPAccountInfoEvent evt;
      alice.vccsConferenceManager->getXMPPAccountInfo( alice.vccsAccountHandle, hConference );
      {
         Conference::VccsConferenceHandle h;
         auto aliceAccountInfo = std::async(std::launch::async, [&] () {
            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsConferenceHandler::onXMPPAccountInfo",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
         });
         waitFor( aliceAccountInfo );
      }

      // Check that we got something back.
      ASSERT_TRUE( evt.domain.size() > 0 );
      ASSERT_TRUE( evt.chatRoomJid.size() > 0 );
      ASSERT_TRUE( evt.username.size() > 0 );
      //ASSERT_TRUE( evt.password.size() > 0 ); // optional
      ASSERT_TRUE( evt.displayName.size() > 0 );
      ASSERT_TRUE( evt.port >= 0 );
      //ASSERT_TRUE( evt.proxy.size() > 0 ); // optional

      // Close the session with the VCCS
      alice.vccsAccountManager->disable( alice.vccsAccountHandle );
      {
         auto aliceUnRegistered = std::async(std::launch::async, [&] () {
            Account::VccsAccountStateChangedEvent evt;
            Account::VccsAccountHandle h;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
            ASSERT_TRUE( evt.oldState == Account::VccsAccountState_Registered );
            ASSERT_TRUE( evt.newState == Account::VccsAccountState_Unregistered );
         });
         waitFor( aliceUnRegistered );
      }
   }
   
   void Test_SubscribeUnsubscribeSuspendNetworkChange(bool autoSubscribeAfterNetworkChange)
   {
      TestAccount alice("alice", Account_Init);

      const cpc::string joinUrl = "https://127.0.0.1:8989/join/MLLUUHOFOQ/imap.mobilevoiplive.com";
      
      // Set the settings
      Account::VccsAccountSettings settings;
      
      cpc::string server;
      cpc::string group;
      cpc::string subCode;
      int port;
      alice.vccsAccountManager->crackVCCSURL(joinUrl, "vccs", true, settings.wsSettings.webSocketURL, server, port, settings.group, subCode);
      
      settings.wsSettings.certMode       = WebSocket::CertVerificationMode_None;
      settings.group          = "imap.mobilevoiplive.com";
      settings.userName       = "<EMAIL>";
      settings.password       = "vccs0007";
      settings.displayName    = "Smokey the Bear";
      
      settings.autoSubscribeAfterNetworkChange = autoSubscribeAfterNetworkChange;

      alice.vccsAccountManager->configureDefaultAccountSettings( alice.vccsAccountHandle, settings );
      alice.vccsAccountManager->applySettings( alice.vccsAccountHandle ); // prob. not needed

      // Start the test harness.
      VccsTestHarness harness( TestEnvironmentConfig::testResourcePath() + "vccs_test_harness_scenarios/VCCS_SubscribeUnsubscribeSuspendNetworkChange.dat", 8989, true );
      harness.start();

      // Enable the account.
      alice.vccsAccountManager->enable( alice.vccsAccountHandle );

      // Wait for the account to reach the registered state
      {
         auto aliceRegistered = std::async(std::launch::async, [&] () {
            Account::VccsAccountStateChangedEvent evt;
            Account::VccsAccountHandle h;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
            ASSERT_TRUE( evt.oldState == Account::VccsAccountState_Unregistered );
            ASSERT_TRUE( evt.newState == Account::VccsAccountState_Registering );

            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
            ASSERT_TRUE( evt.oldState == Account::VccsAccountState_Registering );
            ASSERT_TRUE( evt.newState == Account::VccsAccountState_Registered );
         });
         waitFor( aliceRegistered );
      }
      

      // Query for the list of conferences
      alice.vccsConferenceManager->queryConferenceList( alice.vccsAccountHandle );

      // Fetch these from the first conference we find
      cpc::string bridgeNumber;
      cpc::string pin;
      bool isModerator( false );
      Conference::VccsConferenceHandle hConference( -1 );

      // Wait for the list of conferences to be returned
      {
         Conference::ConferenceListUpdatedEvent evt;
         Account::VccsAccountHandle h;

         auto aliceConfQuery = std::async(std::launch::async, [&] () {
            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsConferenceHandler::onConferenceListUpdated",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
         });
         waitFor( aliceConfQuery );

         for( cpc::vector< Conference::ConferenceListChange >::iterator iter = evt.changes.begin() ;
              iter != evt.changes.end() ; ++iter )
         {
            if( iter->changeType != Conference::ChangeType_Query )
               continue;

            // remember the information we need for this conference
            Conference::ConferenceDetails &details( iter->conference );
            bridgeNumber = details.bridgeNumber;
            pin = details.moderatorPin; // *I'M SPARTACUS!*
            hConference = details.id;
            isModerator = details.socketModerator;
            break;
         }
      }

      ASSERT_TRUE( isModerator );
      ASSERT_TRUE( bridgeNumber.size() > 0 );
      ASSERT_TRUE( pin.size() > 0 );

      
      Conference::SubscriptionInfo subInfo;
      subInfo.conferenceCode = subCode;
      
      alice.vccsConferenceManager->subscribe(alice.vccsAccountHandle, subInfo);
      auto aliceSubscribe = std::async(std::launch::async, [&] () {
         Conference::SubscribeEvent evt;
         Account::VccsAccountHandle h;
         ASSERT_TRUE( cpcWaitForEvent(
            alice.vccsEvents,
            "VccsConferenceHandler::onSubscribe",
            15000,
            AlwaysTruePred( ),
            h, evt ) );
      });
      waitFor( aliceSubscribe );

      // Set the mute lock on a conference
      alice.vccsConferenceManager->setMuteLock( alice.vccsAccountHandle, hConference, true );
      auto aliceMuteLock = std::async(std::launch::async, [&] () {
         Conference::ConferenceListUpdatedEvent evt;
         Account::VccsAccountHandle h;
         ASSERT_TRUE( cpcWaitForEvent(
            alice.vccsEvents,
            "VccsConferenceHandler::onConferenceListUpdated",
            15000,
            AlwaysTruePred( ),
            h, evt ) );
      });
      waitFor( aliceMuteLock );

      // Unset the mute lock
      alice.vccsConferenceManager->setMuteLock( alice.vccsAccountHandle, hConference, false );
      auto aliceMuteUnLock = std::async(std::launch::async, [&] () {
         Conference::ConferenceListUpdatedEvent evt;
         Account::VccsAccountHandle h;
         ASSERT_TRUE( cpcWaitForEvent(
            alice.vccsEvents,
            "VccsConferenceHandler::onConferenceListUpdated",
            15000,
            AlwaysTruePred( ),
            h, evt ) );
      });
      waitFor( aliceMuteUnLock );

      // Set the participant lock on a conference
      alice.vccsConferenceManager->setParticipantLock( alice.vccsAccountHandle, hConference, true );
      auto aliceParticipantLock = std::async(std::launch::async, [&] () {
         Conference::ConferenceListUpdatedEvent evt;
         Account::VccsAccountHandle h;
         ASSERT_TRUE( cpcWaitForEvent(
            alice.vccsEvents,
            "VccsConferenceHandler::onConferenceListUpdated",
            15000,
            AlwaysTruePred( ),
            h, evt ) );
      });
      waitFor( aliceParticipantLock );

      // Unset the participant lock on a conference
      alice.vccsConferenceManager->setParticipantLock( alice.vccsAccountHandle, hConference, false );
      auto aliceParticipantUnLock = std::async(std::launch::async, [&] () {
         Conference::ConferenceListUpdatedEvent evt;
         Account::VccsAccountHandle h;
         ASSERT_TRUE( cpcWaitForEvent(
            alice.vccsEvents,
            "VccsConferenceHandler::onConferenceListUpdated",
            15000,
            AlwaysTruePred( ),
            h, evt ) );
      });
      waitFor( aliceParticipantUnLock );

      // Set the conference as 'hosted'
      alice.vccsConferenceManager->setHosted( alice.vccsAccountHandle, hConference, true );
      auto aliceHosted = std::async(std::launch::async, [&] () {
         Conference::ConferenceListUpdatedEvent evt;
         Account::VccsAccountHandle h;
         ASSERT_TRUE( cpcWaitForEvent(
            alice.vccsEvents,
            "VccsConferenceHandler::onConferenceListUpdated",
            15000,
            AlwaysTruePred( ),
            h, evt ) );
      });
      waitFor( aliceHosted );

      // Set the conference as 'unhosted'
      alice.vccsConferenceManager->setHosted( alice.vccsAccountHandle, hConference, false );
      auto aliceUnHosted = std::async(std::launch::async, [&] () {
         Conference::ConferenceListUpdatedEvent evt;
         Account::VccsAccountHandle h;
         ASSERT_TRUE( cpcWaitForEvent(
            alice.vccsEvents,
            "VccsConferenceHandler::onConferenceListUpdated",
            15000,
            AlwaysTruePred( ),
            h, evt ) );
      });
      waitFor( aliceUnHosted );

      // Disable entry exit tones
      alice.vccsConferenceManager->setEntryExitTonesEnabled(alice.vccsAccountHandle, hConference, false);
      auto aliceTonesDisable = std::async(std::launch::async, [&]() {
         Conference::ConferenceListUpdatedEvent evt;
         Account::VccsAccountHandle h;
         ASSERT_TRUE(cpcWaitForEvent(
            alice.vccsEvents,
            "VccsConferenceHandler::onConferenceListUpdated",
            15000,
            AlwaysTruePred(),
            h, evt));
      });
      waitFor(aliceTonesDisable);

      // Enable entry exit tones
      alice.vccsConferenceManager->setEntryExitTonesEnabled(alice.vccsAccountHandle, hConference, true);
      auto aliceTonesEnable = std::async(std::launch::async, [&]() {
         Conference::ConferenceListUpdatedEvent evt;
         Account::VccsAccountHandle h;
         ASSERT_TRUE(cpcWaitForEvent(
            alice.vccsEvents,
            "VccsConferenceHandler::onConferenceListUpdated",
            15000,
            AlwaysTruePred(),
            h, evt));
      });
      waitFor(aliceTonesEnable);

      // Wait for an unsolicited notification about the conference 'mode'
      auto aliceConfMode = std::async(std::launch::async, [&]() {
         Conference::ConferenceModeEvent evt;
         Conference::VccsConferenceHandle h;
         ASSERT_TRUE(cpcWaitForEvent(
            alice.vccsEvents,
            "VccsConferenceHandler::onConferenceModeUpdated",
            15000,
            AlwaysTruePred(),
            h, evt));
         evt = evt;
      });
      waitFor(aliceConfMode);

      // Unsubscribe from events
      alice.vccsConferenceManager->unsubscribe( alice.vccsAccountHandle, bridgeNumber );
      auto aliceUnsubscribe = std::async(std::launch::async, [&] () {
         Conference::UnsubscribeEvent evt;
         Account::VccsAccountHandle h;
         ASSERT_TRUE( cpcWaitForEvent(
            alice.vccsEvents,
            "VccsConferenceHandler::onUnsubscribe",
            15000,
            AlwaysTruePred( ),
            h, evt ) );
      });
      waitFor( aliceUnsubscribe );

      alice.vccsAccountManager->setSuspendable(alice.vccsAccountHandle, true);

      // simulate a network change
      std::set<resip::Data> ifaces;
      ifaces.insert("**************");
      alice.network->setMockInterfaces(ifaces);
      alice.network->setNetworkTransport(NetworkTransport::TransportWiFi);

      std::this_thread::sleep_for(std::chrono::seconds(10));
      alice.vccsAccountManager->setSuspendable(alice.vccsAccountHandle, false);

      // OBELISK-5975: the critical point; the VCCS module should *not* send out any
      // subscribe at this point, since no conference has been subscribed to.
      aliceSubscribe = std::async(std::launch::async, [&] () {
         Conference::SubscribeEvent evt;
         Account::VccsAccountHandle h;
         ASSERT_FALSE( cpcWaitForEvent(
            alice.vccsEvents,
            "VccsConferenceHandler::onSubscribe",
            15000,
            AlwaysTruePred( ),
            h, evt ) );
      });
      waitFor( aliceSubscribe );


      // Disable the account, and wait for the state to go back to Unregistered
      alice.vccsAccountManager->disable( alice.vccsAccountHandle );
      {
         auto aliceUnRegistered = std::async(std::launch::async, [&] () {
            Account::VccsAccountStateChangedEvent evt;
            Account::VccsAccountHandle h;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
            ASSERT_TRUE( evt.oldState == Account::VccsAccountState_Registered );
            ASSERT_TRUE( evt.newState == Account::VccsAccountState_Unregistered );
         });
         waitFor( aliceUnRegistered );
      }
   }
   
   // OBELISK-5975: the VCCS module was incorrectly sending out a VCCS/collab SUBSCRIBE
   // message after a network change.
   TEST_F(VccsModuleTest, SubscribeUnsubscribeSuspendNetworkChange_AutoSubAfterNetChange) {
   
      bool autoSubscribeAfterNetworkChange = true;
      Test_SubscribeUnsubscribeSuspendNetworkChange(autoSubscribeAfterNetworkChange);
   }
   
   TEST_F(VccsModuleTest, SubscribeUnsubscribeSuspendNetworkChange) {
   
      bool autoSubscribeAfterNetworkChange = false;
      Test_SubscribeUnsubscribeSuspendNetworkChange(autoSubscribeAfterNetworkChange);
   }

   TEST_F(VccsModuleTest, DirectDetails) {

      TestAccount alice("alice", Account_Init);

      //const char *vccsURL = "http://***********:8990/join/APAMZBVTHA/imap.mobilevoiplive.com";
      const char *vccsURL = "https://127.0.0.1:8989/join/APAMZBVTHA/imap.mobilevoiplive.com";
      cpc::string outWebSocketURL;
      cpc::string outServerName;
      int outPortNumber;
      cpc::string outGroupName;
      cpc::string outSubscriptionCode;
      bool result;

      result = alice.vccsAccountManager->crackVCCSURL( vccsURL, true, outWebSocketURL, outServerName, outPortNumber, outGroupName, outSubscriptionCode );
      ASSERT_TRUE( result );

      // Set the settings
      Account::VccsAccountSettings settings;
      settings.wsSettings.webSocketURL = outWebSocketURL;
      settings.wsSettings.certMode     = WebSocket::CertVerificationMode_None;
      settings.group                   = outGroupName;

      alice.vccsAccountManager->configureDefaultAccountSettings( alice.vccsAccountHandle, settings );
      alice.vccsAccountManager->applySettings( alice.vccsAccountHandle ); // prob. not needed

      // Start the test harness.
      VccsTestHarness harness( TestEnvironmentConfig::testResourcePath() + "vccs_test_harness_scenarios/VCCS_DirectDetails.dat", 8989, true );
      harness.start();

      // Enable the account.
      alice.vccsAccountManager->enable( alice.vccsAccountHandle );

      // Wait for the account to reach the registered state
      {
         auto aliceRegistered = std::async(std::launch::async, [&] () {
            Account::VccsAccountStateChangedEvent evt;
            Account::VccsAccountHandle h;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
            ASSERT_TRUE( evt.oldState == Account::VccsAccountState_Unregistered );
            ASSERT_TRUE( evt.newState == Account::VccsAccountState_Registering );

            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
            ASSERT_TRUE( evt.oldState == Account::VccsAccountState_Registering );
            ASSERT_TRUE( evt.newState == Account::VccsAccountState_Registered );
         });
         waitFor( aliceRegistered );
      }

      Conference::VccsConferenceHandle hConference( -1 );
      cpc::string conferencePin( "0707" );

      // Obtain the conference connection info
      alice.vccsConferenceManager->getConferenceConnectionInfo( alice.vccsAccountHandle, outSubscriptionCode );
      auto aliceCxnInfo = std::async(std::launch::async, [&] () {
         Conference::ConferenceConnectionInfoEvent evt;
         Account::VccsAccountHandle h;
         ASSERT_TRUE( cpcWaitForEvent(
            alice.vccsEvents,
            "VccsConferenceHandler::onConferenceConnectionInfo",
            15000,
            AlwaysTruePred( ),
            h, evt ) );

         ASSERT_TRUE( evt.pinRequired );
      });
      waitFor( aliceCxnInfo );

      // Subscribe to the conference using the subscription code
      alice.vccsConferenceManager->subscribe2( alice.vccsAccountHandle, outSubscriptionCode, conferencePin );
      auto aliceSubscribe = std::async(std::launch::async, [&] () {
         Conference::SubscribeEvent evt;
         Account::VccsAccountHandle h;
         ASSERT_TRUE( cpcWaitForEvent(
            alice.vccsEvents,
            "VccsConferenceHandler::onSubscribe",
            15000,
            AlwaysTruePred( ),
            h, evt ) );

         hConference = evt.hConference;

         // check that we obtained provisioning information
         ASSERT_TRUE( evt.directSIPProvisioning.size() > 0 );
      });
      waitFor( aliceSubscribe );

      // Now that we have the conference handle, get the details directly.
      alice.vccsConferenceManager->queryConferenceDetails( alice.vccsAccountHandle, hConference );
      auto aliceQueryDetails = std::async(std::launch::async, [&] () {
         Conference::ConferenceListUpdatedEvent evt;
         Account::VccsAccountHandle h;
         ASSERT_TRUE( cpcWaitForEvent(
            alice.vccsEvents,
            "VccsConferenceHandler::onConferenceListUpdated",
            15000,
            AlwaysTruePred( ),
            h, evt ) );
      });
      waitFor( aliceQueryDetails );

      // Close the session with the VCCS
      alice.vccsAccountManager->disable( alice.vccsAccountHandle );
      {
         auto aliceUnRegistered = std::async(std::launch::async, [&] () {
            Account::VccsAccountStateChangedEvent evt;
            Account::VccsAccountHandle h;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
            ASSERT_TRUE( evt.oldState == Account::VccsAccountState_Registered );
            ASSERT_TRUE( evt.newState == Account::VccsAccountState_Unregistered );
         });
         waitFor( aliceUnRegistered );
      }
   }

   TEST_F(VccsModuleTest, HostScreenShare) {

      // NB: "alice" and "bob" are actually the same user (since syncing
      // really happens between different registrations of the same user)
      TestAccount alice("alice", Account_Init);

      // Set the settings
      Account::VccsAccountSettings settings;
      //settings.webSocketURL   = "ws://***********:8989/join";
      settings.wsSettings.webSocketURL   = "wss://127.0.0.1:8989/join";
      settings.wsSettings.certMode       = WebSocket::CertVerificationMode_None;
      settings.group          = "imap.mobilevoiplive.com";
      settings.userName       = "<EMAIL>";
      settings.password       = "vccs0007";
      settings.displayName    = "Smokey the Bear";
      // sip Aor is "+<EMAIL>";

      alice.vccsAccountManager->configureDefaultAccountSettings( alice.vccsAccountHandle, settings );
      alice.vccsAccountManager->applySettings( alice.vccsAccountHandle ); // prob. not needed

      // Start the test harness.
      VccsTestHarness harness( TestEnvironmentConfig::testResourcePath() + "vccs_test_harness_scenarios/VCCS_HostScreenShare.dat", 8989, true );
      harness.start();

      // Enable the account.
      alice.vccsAccountManager->enable( alice.vccsAccountHandle );

      // Wait for the account to reach the registered state
      {
         auto aliceRegistered = std::async(std::launch::async, [&] () {
            Account::VccsAccountStateChangedEvent evt;
            Account::VccsAccountHandle h;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
            ASSERT_TRUE( evt.oldState == Account::VccsAccountState_Unregistered );
            ASSERT_TRUE( evt.newState == Account::VccsAccountState_Registering );

            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
            ASSERT_TRUE( evt.oldState == Account::VccsAccountState_Registering );
            ASSERT_TRUE( evt.newState == Account::VccsAccountState_Registered );
         });
         waitFor( aliceRegistered );
      }

      // Query for the list of conferences
      alice.vccsConferenceManager->queryConferenceList( alice.vccsAccountHandle );

      // Fetch these from the first conference we find
      Conference::ConferenceDetails details;

      // Wait for the list of conferences to be returned
      {
         Conference::ConferenceListUpdatedEvent evt;
         Account::VccsAccountHandle h;

         auto aliceConfQuery = std::async(std::launch::async, [&] () {
            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsConferenceHandler::onConferenceListUpdated",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
         });
         waitFor( aliceConfQuery );

         for( cpc::vector< Conference::ConferenceListChange >::iterator iter = evt.changes.begin() ;
              iter != evt.changes.end() ; ++iter )
         {
            if( iter->changeType != Conference::ChangeType_Query )
               continue;

            // remember the information we need for this conference
            details = iter->conference;
            break;
         }
      }

      ASSERT_TRUE( details.socketModerator );
      ASSERT_TRUE( details.bridgeNumber.size() > 0 );
      ASSERT_TRUE( details.moderatorPin.size() > 0 );
      ASSERT_TRUE( details.id != -1 );
      ASSERT_TRUE( details.participants.size() > 0 );

      // Subscribe to the conference from which we want events
      alice.vccsConferenceManager->subscribe( alice.vccsAccountHandle, details.bridgeNumber, details.moderatorPin );
      auto aliceSubscribe = std::async(std::launch::async, [&] () {
         Conference::SubscribeEvent evt;
         Account::VccsAccountHandle h;
         ASSERT_TRUE( cpcWaitForEvent(
            alice.vccsEvents,
            "VccsConferenceHandler::onSubscribe",
            15000,
            AlwaysTruePred( ),
            h, evt ) );
      });
      waitFor( aliceSubscribe );

      // Set the presenter (assume they accept) and wait for a config update
      alice.vccsConferenceManager->setScreenSharePresenter( alice.vccsAccountHandle, details.id, details.participants[ 0 ].participantNumber );
      auto aliceConfigChanged =  std::async(std::launch::async, [&] () {
         Conference::ScreenShareConfigEvent evt;
         Conference::VccsConferenceHandle h;
         ASSERT_TRUE( cpcWaitForEvent(
            alice.vccsEvents,
            "VccsConferenceHandler::onScreenShareConfigChanged",
            15000,
            AlwaysTruePred( ),
            h, evt ) );

         ASSERT_TRUE( evt.screenShareActive );
         ASSERT_TRUE( evt.config.size() > 0 );
      });
      waitFor( aliceConfigChanged );

      // Wait for the presenter to stop
      auto aliceShareStopped =  std::async(std::launch::async, [&] () {
         Conference::ScreenShareConfigEvent evt;
         Conference::VccsConferenceHandle h;
         ASSERT_TRUE( cpcWaitForEvent(
            alice.vccsEvents,
            "VccsConferenceHandler::onScreenShareConfigChanged",
            15000,
            AlwaysTruePred( ),
            h, evt ) );

         ASSERT_TRUE( !evt.screenShareActive );
      });
      waitFor( aliceShareStopped );

      // Unsubscribe from events
      alice.vccsConferenceManager->unsubscribe( alice.vccsAccountHandle, details.bridgeNumber );
      auto aliceUnsubscribe = std::async(std::launch::async, [&] () {
         Conference::UnsubscribeEvent evt;
         Account::VccsAccountHandle h;
         ASSERT_TRUE( cpcWaitForEvent(
            alice.vccsEvents,
            "VccsConferenceHandler::onUnsubscribe",
            15000,
            AlwaysTruePred( ),
            h, evt ) );
      });
      waitFor( aliceUnsubscribe );

      // Disable the account, and wait for the state to go back to Unregistered
      alice.vccsAccountManager->disable( alice.vccsAccountHandle );
      {
         auto aliceUnRegistered = std::async(std::launch::async, [&] () {
            Account::VccsAccountStateChangedEvent evt;
            Account::VccsAccountHandle h;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
            ASSERT_TRUE( evt.oldState == Account::VccsAccountState_Registered );
            ASSERT_TRUE( evt.newState == Account::VccsAccountState_Unregistered );
         });
         waitFor( aliceUnRegistered );
      }
   }

   TEST_F(VccsModuleTest, MixerOptions) {

      // NB: "alice" and "bob" are actually the same user (since syncing
      // really happens between different registrations of the same user)
      TestAccount alice("alice", Account_Init);

      // Set the settings
      Account::VccsAccountSettings settings;
      //settings.wsSettings.webSocketURL   = "ws://***********:8989/join";
      settings.wsSettings.webSocketURL   = "wss://127.0.0.1:8989/join";
      settings.wsSettings.certMode = WebSocket::CertVerificationMode_None;
      settings.group = "imap.mobilevoiplive.com";
      settings.userName = "<EMAIL>";
      settings.password = "vccs0007";
      settings.displayName = "Smokey the Bear";
      // sip Aor is "+<EMAIL>";

      alice.vccsAccountManager->configureDefaultAccountSettings(alice.vccsAccountHandle, settings);
      alice.vccsAccountManager->applySettings(alice.vccsAccountHandle); // prob. not needed

      // Start the test harness.
      VccsTestHarness harness( TestEnvironmentConfig::testResourcePath() + "vccs_test_harness_scenarios/VCCS_MixerOptions.dat", 8989, true);
      harness.start();

      // Enable the account.
      alice.vccsAccountManager->enable(alice.vccsAccountHandle);

      // Wait for the account to reach the registered state
      {
         auto aliceRegistered = std::async(std::launch::async, [&]() {
            Account::VccsAccountStateChangedEvent evt;
            Account::VccsAccountHandle h;
            ASSERT_TRUE(cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred(),
               h, evt));
            ASSERT_TRUE(evt.oldState == Account::VccsAccountState_Unregistered);
            ASSERT_TRUE(evt.newState == Account::VccsAccountState_Registering);

            ASSERT_TRUE(cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred(),
               h, evt));
            ASSERT_TRUE(evt.oldState == Account::VccsAccountState_Registering);
            ASSERT_TRUE(evt.newState == Account::VccsAccountState_Registered);
         });
         waitFor(aliceRegistered);
      }

      // Query for the list of conferences
      alice.vccsConferenceManager->queryConferenceList(alice.vccsAccountHandle);

      // Fetch these from the first conference we find
      cpc::string bridgeNumber;
      cpc::string pin;
      bool isModerator(false);
      Conference::VccsConferenceHandle hConference(-1);

      // Wait for the list of conferences to be returned
      {
         Conference::ConferenceListUpdatedEvent evt;
         Account::VccsAccountHandle h;

         auto aliceConfQuery = std::async(std::launch::async, [&]() {
            ASSERT_TRUE(cpcWaitForEvent(
               alice.vccsEvents,
               "VccsConferenceHandler::onConferenceListUpdated",
               15000,
               AlwaysTruePred(),
               h, evt));
         });
         waitFor(aliceConfQuery);

         for (cpc::vector< Conference::ConferenceListChange >::iterator iter = evt.changes.begin();
            iter != evt.changes.end(); ++iter)
         {
            if (iter->changeType != Conference::ChangeType_Query)
               continue;

            // remember the information we need for this conference
            Conference::ConferenceDetails &details(iter->conference);
            bridgeNumber = details.bridgeNumber;
            pin = details.moderatorPin; // *I'M SPARTACUS!*
            hConference = details.id;
            isModerator = details.socketModerator;
         }
      }

      ASSERT_TRUE(isModerator);
      ASSERT_TRUE(bridgeNumber.size() > 0);

      // Subscribe to the conference from which we want events
      alice.vccsConferenceManager->subscribe(alice.vccsAccountHandle, bridgeNumber, pin);
      auto aliceSubscribe = std::async(std::launch::async, [&]() {
         Conference::SubscribeEvent evt;
         Account::VccsAccountHandle h;
         ASSERT_TRUE(cpcWaitForEvent(
            alice.vccsEvents,
            "VccsConferenceHandler::onSubscribe",
            15000,
            AlwaysTruePred(),
            h, evt));
      });
      waitFor(aliceSubscribe);


      cpc::vector<Conference::VideoLayout> videoLayouts;
      Conference::VideoResolution videoResolution;
      {
         auto aliceQueryDetails = std::async(std::launch::async, [&]() {
            Conference::ConferenceListUpdatedEvent evt;
            Account::VccsAccountHandle h;
            ASSERT_TRUE(cpcWaitForEvent(
               alice.vccsEvents,
               "VccsConferenceHandler::onConferenceListUpdated",
               15000,
               AlwaysTruePred(),
               h, evt));

            bool found = false;
            for (cpc::vector< Conference::ConferenceListChange >::iterator iter = evt.changes.begin();
               iter != evt.changes.end(); ++iter)
            {
               if (iter->changeType == Conference::ChangeType_Update)
               {
                  // ensure we can parse the video layouts array
                  Conference::ConferenceDetails &details(iter->conference);
                  videoLayouts = details.videoLayouts;
                  videoResolution = details.videoResolution;
                  found = true;
                  break;
               }
            }
            ASSERT_TRUE(found);
         });
         waitFor(aliceQueryDetails);
      }
      
      // initial resolution on bridge join
      ASSERT_TRUE(videoLayouts.size() > 0);
      ASSERT_EQ(Conference::VideoResolution_848x480, videoResolution);


      // Now that we have the conference handle, get the details directly.
      alice.vccsConferenceManager->queryConferenceDetails(alice.vccsAccountHandle, hConference);
      
      {
         auto aliceQueryDetails = std::async(std::launch::async, [&]() {
            Conference::ConferenceListUpdatedEvent evt;
            Account::VccsAccountHandle h;
            ASSERT_TRUE(cpcWaitForEvent(
               alice.vccsEvents,
               "VccsConferenceHandler::onConferenceListUpdated",
               15000,
               AlwaysTruePred(),
               h, evt));

            bool found = false;
            for (cpc::vector< Conference::ConferenceListChange >::iterator iter = evt.changes.begin();
               iter != evt.changes.end(); ++iter)
            {
               if (iter->changeType == Conference::ChangeType_Query)
               {
                  // ensure we can parse the video layouts array
                  Conference::ConferenceDetails &details(iter->conference);
                  videoLayouts = details.videoLayouts;
                  videoResolution = details.videoResolution;
                  found = true;
                  break;
               }
            }
            ASSERT_TRUE(found);
         });
         waitFor(aliceQueryDetails);
      }

      ASSERT_TRUE(videoLayouts.size() > 0);
      ASSERT_EQ(Conference::VideoResolution_848x480, videoResolution);

      alice.vccsConferenceManager->setVideoLayout(alice.vccsAccountHandle, hConference, Conference::VideoLayout_Grid);

      {
         const CPCAPI2::VCCS::Conference::VideoResolution requestedVideoRes = Conference::VideoResolution_640x480;
         alice.vccsConferenceManager->setVideoResolution(alice.vccsAccountHandle, hConference, requestedVideoRes);
         
         {
            auto aliceQueryDetails = std::async(std::launch::async, [&]() {
               Conference::ConferenceListUpdatedEvent evt;
               Account::VccsAccountHandle h;
               ASSERT_TRUE(cpcWaitForEvent(
                  alice.vccsEvents,
                  "VccsConferenceHandler::onConferenceListUpdated",
                  15000,
                  AlwaysTruePred(),
                  h, evt));

               bool found = false;
               for (cpc::vector< Conference::ConferenceListChange >::iterator iter = evt.changes.begin();
                  iter != evt.changes.end(); ++iter)
               {
                  if (iter->changeType == Conference::ChangeType_Update)
                  {
                     // ensure we can parse the video layouts array
                     Conference::ConferenceDetails &details(iter->conference);
                     videoLayouts = details.videoLayouts;
                     videoResolution = details.videoResolution;
                     found = true;
                     break;
                  }
               }
               ASSERT_TRUE(found);
            });
            waitFor(aliceQueryDetails);
         }
         
         ASSERT_TRUE(videoLayouts.size() > 0);
         ASSERT_EQ(requestedVideoRes, videoResolution);
      }

      {
         const CPCAPI2::VCCS::Conference::VideoResolution requestedVideoRes = Conference::VideoResolution_848x480;
         alice.vccsConferenceManager->setVideoResolution(alice.vccsAccountHandle, hConference, requestedVideoRes);
         
         {
            auto aliceQueryDetails = std::async(std::launch::async, [&]() {
               Conference::ConferenceListUpdatedEvent evt;
               Account::VccsAccountHandle h;
               ASSERT_TRUE(cpcWaitForEvent(
                  alice.vccsEvents,
                  "VccsConferenceHandler::onConferenceListUpdated",
                  15000,
                  AlwaysTruePred(),
                  h, evt));

               bool found = false;
               for (cpc::vector< Conference::ConferenceListChange >::iterator iter = evt.changes.begin();
                  iter != evt.changes.end(); ++iter)
               {
                  if (iter->changeType == Conference::ChangeType_Update)
                  {
                     // ensure we can parse the video layouts array
                     Conference::ConferenceDetails &details(iter->conference);
                     videoLayouts = details.videoLayouts;
                     videoResolution = details.videoResolution;
                     found = true;
                     break;
                  }
               }
               ASSERT_TRUE(found);
            });
            waitFor(aliceQueryDetails);
         }
         
         ASSERT_TRUE(videoLayouts.size() > 0);
         ASSERT_EQ(requestedVideoRes, videoResolution);
      }
      
      {
         const CPCAPI2::VCCS::Conference::VideoResolution requestedVideoRes = Conference::VideoResolution_854x480;
         alice.vccsConferenceManager->setVideoResolution(alice.vccsAccountHandle, hConference, requestedVideoRes);
         
         {
            auto aliceQueryDetails = std::async(std::launch::async, [&]() {
               Conference::ConferenceListUpdatedEvent evt;
               Account::VccsAccountHandle h;
               ASSERT_TRUE(cpcWaitForEvent(
                  alice.vccsEvents,
                  "VccsConferenceHandler::onConferenceListUpdated",
                  15000,
                  AlwaysTruePred(),
                  h, evt));

               bool found = false;
               for (cpc::vector< Conference::ConferenceListChange >::iterator iter = evt.changes.begin();
                  iter != evt.changes.end(); ++iter)
               {
                  if (iter->changeType == Conference::ChangeType_Update)
                  {
                     // ensure we can parse the video layouts array
                     Conference::ConferenceDetails &details(iter->conference);
                     videoLayouts = details.videoLayouts;
                     videoResolution = details.videoResolution;
                     found = true;
                     break;
                  }
               }
               ASSERT_TRUE(found);
            });
            waitFor(aliceQueryDetails);
         }
         
         ASSERT_TRUE(videoLayouts.size() > 0);
         ASSERT_EQ(requestedVideoRes, videoResolution);
      }

      // Unsubscribe from events
      alice.vccsConferenceManager->unsubscribe(alice.vccsAccountHandle, bridgeNumber);
      auto aliceUnsubscribe = std::async(std::launch::async, [&]() {
         Conference::UnsubscribeEvent evt;
         Account::VccsAccountHandle h;
         ASSERT_TRUE(cpcWaitForEvent(
            alice.vccsEvents,
            "VccsConferenceHandler::onUnsubscribe",
            15000,
            AlwaysTruePred(),
            h, evt));
      });
      waitFor(aliceUnsubscribe);

      // Disable the account, and wait for the state to go back to Unregistered
      alice.vccsAccountManager->disable(alice.vccsAccountHandle);
      {
         auto aliceUnRegistered = std::async(std::launch::async, [&]() {
            Account::VccsAccountStateChangedEvent evt;
            Account::VccsAccountHandle h;
            ASSERT_TRUE(cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred(),
               h, evt));
            ASSERT_TRUE(evt.oldState == Account::VccsAccountState_Registered);
            ASSERT_TRUE(evt.newState == Account::VccsAccountState_Unregistered);
         });
         waitFor(aliceUnRegistered);
      }
   }

   TEST_F(VccsModuleTest, ConferenceConfig) {

      // NB: "alice" and "bob" are actually the same user (since syncing
      // really happens between different registrations of the same user)
      TestAccount alice("alice", Account_Init);

      // Set the settings
      Account::VccsAccountSettings settings;
      //settings.wsSettings.webSocketURL   = "ws://***********:8989/join";
      settings.wsSettings.webSocketURL   = "wss://127.0.0.1:8989/join";
      settings.wsSettings.certMode = WebSocket::CertVerificationMode_None;
      settings.group = "imap.mobilevoiplive.com";
      settings.userName = "<EMAIL>";
      settings.password = "vccs0007";
      settings.displayName = "Smokey the Bear";

      alice.vccsAccountManager->configureDefaultAccountSettings(alice.vccsAccountHandle, settings);
      alice.vccsAccountManager->applySettings(alice.vccsAccountHandle); // prob. not needed

      // Start the test harness.
      VccsTestHarness harness( TestEnvironmentConfig::testResourcePath() + "vccs_test_harness_scenarios/VCCS_ConferenceConfig.dat", 8989, true );
      harness.start();

      // Enable the account.
      alice.vccsAccountManager->enable(alice.vccsAccountHandle);

      // Wait for the account to reach the registered state
      {
         auto aliceRegistered = std::async(std::launch::async, [&]() {
            Account::VccsAccountStateChangedEvent evt;
            Account::VccsAccountHandle h;
            ASSERT_TRUE(cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred(),
               h, evt));
            ASSERT_TRUE(evt.oldState == Account::VccsAccountState_Unregistered);
            ASSERT_TRUE(evt.newState == Account::VccsAccountState_Registering);

            ASSERT_TRUE(cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred(),
               h, evt));
            ASSERT_TRUE(evt.oldState == Account::VccsAccountState_Registering);
            ASSERT_TRUE(evt.newState == Account::VccsAccountState_Registered);
         });
         waitFor(aliceRegistered);
      }

      // Query for the conference list
      alice.vccsConferenceManager->queryConferenceList( alice.vccsAccountHandle );

      // Fetch these from the first conference we find
      bool isModerator(false);
      Conference::VccsConferenceHandle hConference(-1);

      // Wait for the list of conferences to be returned
      {
         Conference::ConferenceListUpdatedEvent evt;
         Account::VccsAccountHandle h;

         auto aliceConfQuery = std::async(std::launch::async, [&]() {
            ASSERT_TRUE(cpcWaitForEvent(
               alice.vccsEvents,
               "VccsConferenceHandler::onConferenceListUpdated",
               15000,
               AlwaysTruePred(),
               h, evt));
         });
         waitFor(aliceConfQuery);

         for( cpc::vector< Conference::ConferenceListChange >::iterator iter = evt.changes.begin();
              iter != evt.changes.end() ; ++iter )
         {
            if (iter->changeType != Conference::ChangeType_Query)
               continue;

            // remember the information we need for this conference
            Conference::ConferenceDetails &details(iter->conference);
            hConference = details.id;
            isModerator = details.socketModerator;
         }
      }

      ASSERT_TRUE(isModerator);

      // Now that we have the conference handle, get the config directly.
      alice.vccsConferenceManager->queryConferenceConfig(alice.vccsAccountHandle, hConference);
      auto aliceQueryDetails = std::async(std::launch::async, [&]() {
         Conference::ConferenceConfigEvent evt;
         Conference::VccsConferenceHandle h;
         ASSERT_TRUE(cpcWaitForEvent(
            alice.vccsEvents,
            "VccsConferenceHandler::onQueryConferenceConfig",
            15000,
            AlwaysTruePred(),
            h, evt));

         Conference::ConferenceConfiguration& config( evt.config );
      });
      waitFor(aliceQueryDetails);

      // Disable the account, and wait for the state to go back to Unregistered
      alice.vccsAccountManager->disable(alice.vccsAccountHandle);
      {
         auto aliceUnRegistered = std::async(std::launch::async, [&]() {
            Account::VccsAccountStateChangedEvent evt;
            Account::VccsAccountHandle h;
            ASSERT_TRUE(cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred(),
               h, evt));
            ASSERT_TRUE(evt.oldState == Account::VccsAccountState_Registered);
            ASSERT_TRUE(evt.newState == Account::VccsAccountState_Unregistered);
         });
         waitFor(aliceUnRegistered);
      }
   }

   TEST_F(VccsModuleTest, ConferenceHistory) {

      // NB: "alice" and "bob" are actually the same user (since syncing
      // really happens between different registrations of the same user)
      TestAccount alice("alice", Account_Init);

      // Set the settings
      Account::VccsAccountSettings settings;
      //settings.wsSettings.webSocketURL   = "ws://***********:8989/join";
      settings.wsSettings.webSocketURL   = "wss://127.0.0.1:8989/join";
      settings.wsSettings.certMode = WebSocket::CertVerificationMode_None;
      settings.group = "imap.mobilevoiplive.com";
      settings.userName = "<EMAIL>";
      settings.password = "vccs0007";
      settings.displayName = "Smokey the Bear";
      // sip Aor is "+<EMAIL>";

      alice.vccsAccountManager->configureDefaultAccountSettings(alice.vccsAccountHandle, settings);
      alice.vccsAccountManager->applySettings(alice.vccsAccountHandle); // prob. not needed

      // Start the test harness.
      VccsTestHarness harness( TestEnvironmentConfig::testResourcePath() + "vccs_test_harness_scenarios/VCCS_ConferenceHistory.dat", 8989, true);
      harness.start();

      // Enable the account.
      alice.vccsAccountManager->enable(alice.vccsAccountHandle);

      // Wait for the account to reach the registered state
      {
         auto aliceRegistered = std::async(std::launch::async, [&]() {
            Account::VccsAccountStateChangedEvent evt;
            Account::VccsAccountHandle h;
            ASSERT_TRUE(cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred(),
               h, evt));
            ASSERT_TRUE(evt.oldState == Account::VccsAccountState_Unregistered);
            ASSERT_TRUE(evt.newState == Account::VccsAccountState_Registering);

            ASSERT_TRUE(cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred(),
               h, evt));
            ASSERT_TRUE(evt.oldState == Account::VccsAccountState_Registering);
            ASSERT_TRUE(evt.newState == Account::VccsAccountState_Registered);
         });
         waitFor(aliceRegistered);
      }

      // Query for the conference list
      alice.vccsConferenceManager->queryConferenceList( alice.vccsAccountHandle );

      // Fetch these from the first conference we find
      bool isModerator(false);
      Conference::VccsConferenceHandle hConference(-1);

      // Wait for the list of conferences to be returned
      {
         Conference::ConferenceListUpdatedEvent evt;
         Account::VccsAccountHandle h;

         auto aliceConfQuery = std::async(std::launch::async, [&]() {
            ASSERT_TRUE(cpcWaitForEvent(
               alice.vccsEvents,
               "VccsConferenceHandler::onConferenceListUpdated",
               15000,
               AlwaysTruePred(),
               h, evt));
         });
         waitFor(aliceConfQuery);

         for( cpc::vector< Conference::ConferenceListChange >::iterator iter = evt.changes.begin();
              iter != evt.changes.end() ; ++iter )
         {
            if (iter->changeType != Conference::ChangeType_Query)
               continue;

            // remember the information we need for this conference
            Conference::ConferenceDetails &details(iter->conference);
            hConference = details.id;
            isModerator = details.socketModerator;
         }
      }

      ASSERT_TRUE(isModerator);

      // Now that we have the conference handle, get the config directly.
      alice.vccsConferenceManager->queryConferenceHistory(alice.vccsAccountHandle, hConference);
      auto aliceQueryDetails = std::async(std::launch::async, [&]() {
         Conference::ConferenceHistoryEvent evt;
         Conference::VccsConferenceHandle h;
         ASSERT_TRUE(cpcWaitForEvent(
            alice.vccsEvents,
            "VccsConferenceHandler::onQueryConferenceHistory",
            15000,
            AlwaysTruePred(),
            h, evt));

         for( auto iter = evt.historyEntries.begin() ; iter != evt.historyEntries.end() ; ++iter )
         {
            Conference::ConferenceHistoryEntry& entry( *iter );
            std::cout << "HistoryID is : " << entry.historyID << std::endl;
            std::cout << "ConferenceStart is : " << entry.conferenceStart << std::endl;
            std::cout << "ConferenceEnd is : " << entry.conferenceEnd << std::endl;
            std::cout << "WebParticipantCount is : " << entry.webParticipantCount << std::endl;
            std::cout << "DialInParticipantCount is : " << entry.dialInParticipantCount << std::endl;
            std::cout << "DesktopCount is : " << entry.desktopCount << std::endl;
            std::cout << "MobileCount is : " << entry.mobileCount << std::endl;
            std::cout << "TabletCount is : " << entry.tabletCount << std::endl;
            std::cout << "TotalParticipants is : " << entry.totalParticipants << std::endl;
            std::cout << "KickedParticipants is : " << entry.kickedParticipants << std::endl;
            std::cout << "ScreenshareUsage is : " << entry.screenshareUsage << std::endl;
            std::cout << "RecordingUrl is : " << entry.recordingUrl << std::endl;
         }
      });
      waitFor(aliceQueryDetails);

      // Disable the account, and wait for the state to go back to Unregistered
      alice.vccsAccountManager->disable(alice.vccsAccountHandle);
      {
         auto aliceUnRegistered = std::async(std::launch::async, [&]() {
            Account::VccsAccountStateChangedEvent evt;
            Account::VccsAccountHandle h;
            ASSERT_TRUE(cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred(),
               h, evt));
            ASSERT_TRUE(evt.oldState == Account::VccsAccountState_Registered);
            ASSERT_TRUE(evt.newState == Account::VccsAccountState_Unregistered);
         });
         waitFor(aliceUnRegistered);
      }
   }
   
   TEST_F(VccsModuleTest, DISABLED_TimedConnectTest) {

      TestAccount alice("alice", Account_Init);

      // Set the settings
      Account::VccsAccountSettings settings;
      settings.wsSettings.webSocketURL   = "https://imap.mobilevoiplive.com:8990/join";
      settings.wsSettings.certMode       = WebSocket::CertVerificationMode_Peer;
      settings.group          = "imap.mobilevoiplive.com";
      settings.userName       = "<EMAIL>";
      settings.password       = "vccs1031";
      settings.displayName    = "Smokey the Bear";
      
      alice.vccsAccountManager->configureDefaultAccountSettings( alice.vccsAccountHandle, settings );
      alice.vccsAccountManager->applySettings( alice.vccsAccountHandle ); // prob. not needed
   
      auto start = std::chrono::system_clock::now();
      alice.vccsAccountManager->enable( alice.vccsAccountHandle );
   
   
      // Wait for the account to reach the registered state
      {
         auto aliceRegistered = std::async(std::launch::async, [&] () {
            Account::VccsAccountStateChangedEvent evt;
            Account::VccsAccountHandle h;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
            ASSERT_TRUE( evt.oldState == Account::VccsAccountState_Unregistered );
            ASSERT_TRUE( evt.newState == Account::VccsAccountState_Registering );

            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
            ASSERT_TRUE( evt.oldState == Account::VccsAccountState_Registering );
            ASSERT_TRUE( evt.newState == Account::VccsAccountState_Registered );
         });
         waitFor( aliceRegistered );
      }
      
      auto end = std::chrono::system_clock::now();
      std::chrono::duration<double> elapsed_seconds = end-start;
      
      std::cout << "Took " << elapsed_seconds.count() << " seconds to connect to VCCS server over secure websocket";
      
   }
   
   TEST_F(VccsModuleTest, InvalidConferenceCode) {

      TestAccount alice("alice", Account_Init);

      //const char *vccsURL = "http://***********:8990/join/XXXXXXXXXX/imap.mobilevoiplive.com";
      const char *vccsURL = "http://127.0.0.1:8989/join/XXXXXXXXXX/imap.mobilevoiplive.com";
      cpc::string outWebSocketURL;
      cpc::string outServerName;
      int outPortNumber;
      cpc::string outGroupName;
      cpc::string outSubscriptionCode;
      bool result;

      result = alice.vccsAccountManager->crackVCCSURL(vccsURL, false, outWebSocketURL, outServerName, outPortNumber, outGroupName, outSubscriptionCode);
      ASSERT_TRUE(result);

      // Set the settings
      Account::VccsAccountSettings settings;
      settings.wsSettings.webSocketURL = outWebSocketURL;
      settings.wsSettings.certMode = WebSocket::CertVerificationMode_None;
      settings.group = outGroupName;

      alice.vccsAccountManager->configureDefaultAccountSettings(alice.vccsAccountHandle, settings);
      alice.vccsAccountManager->applySettings(alice.vccsAccountHandle); // prob. not needed

                                                                        // Start the test harness.
      VccsTestHarness harness(TestEnvironmentConfig::testResourcePath() + "vccs_test_harness_scenarios/VCCS_InvalidConverenceCode.dat");
      harness.start();

      // Enable the account.
      alice.vccsAccountManager->enable(alice.vccsAccountHandle);

      // Wait for the account to reach the registered state
      {
         auto aliceRegistered = std::async(std::launch::async, [&]() {
            Account::VccsAccountStateChangedEvent evt;
            Account::VccsAccountHandle h;
            ASSERT_TRUE(cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred(),
               h, evt));
            ASSERT_TRUE(evt.oldState == Account::VccsAccountState_Unregistered);
            ASSERT_TRUE(evt.newState == Account::VccsAccountState_Registering);

            ASSERT_TRUE(cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred(),
               h, evt));
            ASSERT_TRUE(evt.oldState == Account::VccsAccountState_Registering);
            ASSERT_TRUE(evt.newState == Account::VccsAccountState_Registered);
         });
         waitFor(aliceRegistered);
      }

      // Obtain the conference connection info
      alice.vccsConferenceManager->getConferenceConnectionInfo(alice.vccsAccountHandle, outSubscriptionCode);
      auto aliceCxnInfo = std::async(std::launch::async, [&]() {
         Conference::ConferenceFailureEvent evt;
         Account::VccsAccountHandle h;
         ASSERT_TRUE(cpcWaitForEvent(
            alice.vccsEvents,
            "VccsConferenceHandler::onConferenceConnectionInfoFailure",
            15000,
            AlwaysTruePred(),
            h, evt));
         ASSERT_EQ(evt.errorCode, Conference::ErrorCode::ErrorCode_ConferenceNotFound);
      });
      waitFor(aliceCxnInfo);
   }

   // Our cert is self-signed so, we cannot have this cert pinning pass test
   // actuall work because it fails the preverify step. More measure have to
   // be taken.
   TEST_F(VccsModuleTest, CertAcceptedPass) {

      // NB: "alice" and "bob" are actually the same user (since syncing
      // really happens between different registrations of the same user)
      TestAccount alice("alice", Account_Init);

      // Set the settings
      Account::VccsAccountSettings settings;
      //settings.webSocketURL   = "ws://***********:8989/join";
      settings.wsSettings.webSocketURL   = "wss://127.0.0.1:8989/join";
      settings.wsSettings.certMode       = WebSocket::CertVerificationMode_Fail_If_No_Peer_Cert;
      settings.group          = "imap.mobilevoiplive.com";
      settings.userName       = "<EMAIL>";
      settings.password       = "vccs0007";
      settings.displayName    = "Smokey the Bear";
      settings.wsSettings.acceptedCertPublicKeys.push_back( "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAwTPzTNNjXeXe8l2D0AbSL5j5ghMkD1eR5sPcKaOu+2tjZLT84/TooNwb2rtUj2WtHenpuXG+bUZ68lWeE1tinZYWCI3d3BGNsKB0GrrfbxrkgQCaD/XDLAM4iYvheHBZKAql4JqeH9S+gqhG3qGPhq26AQ3673V4RCoBZcUCsa7w2bIEzzlK/MFirYWtmq9xMgUZ+z2ozLMOOklevlzrAhJb84wEFR0qN1rm6cQtTrHC3Sq2JE+GYl3Fhf7YFIvobrnUWVFjXBZ4cSoaeClbJsG+sGe+PewMzghaYxaMcmDcywLAm5zrvTCt6toEgrhRWOd18daElq5GQ4B8T4o0YQIDAQAB" );
      // sip Aor is "+<EMAIL>";

      alice.vccsAccountManager->configureDefaultAccountSettings( alice.vccsAccountHandle, settings );
      alice.vccsAccountManager->applySettings( alice.vccsAccountHandle ); // prob. not needed

      // Start the test harness.
      VccsTestHarness harness( TestEnvironmentConfig::testResourcePath() + "vccs_test_harness_scenarios/VCCS_Vertical1.dat", 8989, true ); // scenario file doesn't matter
      harness.start();

      // Enable the account.
      alice.vccsAccountManager->enable( alice.vccsAccountHandle );

      // Wait for the account to reach the registered state
      {
         auto aliceRegistered = std::async(std::launch::async, [&] () {
            Account::VccsAccountStateChangedEvent evt;
            Account::VccsAccountHandle h;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
            ASSERT_TRUE( evt.oldState == Account::VccsAccountState_Unregistered );
            ASSERT_TRUE( evt.newState == Account::VccsAccountState_Registering );

            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
            ASSERT_TRUE( evt.oldState == Account::VccsAccountState_Registering );
            ASSERT_TRUE( evt.newState == Account::VccsAccountState_Registered );
         });
         waitFor( aliceRegistered );
      }

      // Disable the account, and wait for the state to go back to Unregistered
      alice.vccsAccountManager->disable( alice.vccsAccountHandle );
      {
         auto aliceUnRegistered = std::async(std::launch::async, [&] () {
            Account::VccsAccountStateChangedEvent evt;
            Account::VccsAccountHandle h;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
            ASSERT_TRUE( evt.oldState == Account::VccsAccountState_Registered );
            ASSERT_TRUE( evt.newState == Account::VccsAccountState_Unregistered );
         });
         waitFor( aliceUnRegistered );
      }
   }

   // Load the certs via the file mechanism so that our self-signed cert
   // verifies.
   TEST_F(VccsModuleTest, CertFileSystem) {

      // NB: "alice" and "bob" are actually the same user (since syncing
      // really happens between different registrations of the same user)
      TestAccount alice("alice", Account_Init);

      // Set the settings
      Account::VccsAccountSettings settings;
      //settings.webSocketURL   = "ws://***********:8989/join";
      settings.wsSettings.webSocketURL   = "wss://127.0.0.1:8989/join";
      settings.wsSettings.certMode       = WebSocket::CertVerificationMode_Fail_If_No_Peer_Cert;
      settings.group          = "imap.mobilevoiplive.com";
      settings.userName       = "<EMAIL>";
      settings.password       = "vccs0007";
      settings.displayName    = "Smokey the Bear";
      settings.wsSettings.certStorageLoadType = WebSocket::CertStorageLoadType_FileSystem;

      settings.wsSettings.certStorageFileSystemPath = TestEnvironmentConfig::testResourcePath();

      //settings.wsSettings.acceptedCertPublicKeys.push_back( "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA5BtZAZStnWW4JazRdr8nbc79QMiMEbd2Pfs56czCTzNeuH1TNgXYutRjNcp2ZjBmUi4Tr7qqaAaZirPD8mENBjoYfPS0ti7eJpq4Jze09wudnyIZudVg9y4peuqiVhb/CdyBvBZHzHXidyuChnIJrEa/SbBB3HgYEDrZRENw+mGEwVA8MGX1ss1ohpJfpC8l4yvAbpLVqAvArSuHWe7KOOWdfFvwJuEMEzTl3LVr1OGwn4sCUesljttnD3q88XVGif50FvKpU1r6OTGlQaNt33KUwt0YQXUotWUsdGzUnweUBxPh1fM4K51p2joBE0+rOmEewJTQShS0/uvAXYZN7wIDAQAB" );
      // sip Aor is "+<EMAIL>";

      alice.vccsAccountManager->configureDefaultAccountSettings( alice.vccsAccountHandle, settings );
      alice.vccsAccountManager->applySettings( alice.vccsAccountHandle ); // prob. not needed

      // Start the test harness.
      VccsTestHarness harness( TestEnvironmentConfig::testResourcePath() + "vccs_test_harness_scenarios/VCCS_Vertical1.dat", 8989, true ); // scenario file doesn't matter
      harness.start();

      // Enable the account.
      alice.vccsAccountManager->enable( alice.vccsAccountHandle );

      // Wait for the account to reach the registered state
      {
         auto aliceRegistered = std::async(std::launch::async, [&] () {
            Account::VccsAccountStateChangedEvent evt;
            Account::VccsAccountHandle h;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
            ASSERT_TRUE( evt.oldState == Account::VccsAccountState_Unregistered );
            ASSERT_TRUE( evt.newState == Account::VccsAccountState_Registering );

            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
            ASSERT_TRUE( evt.oldState == Account::VccsAccountState_Registering );
            ASSERT_TRUE( evt.newState == Account::VccsAccountState_Registered );
         });
         waitFor( aliceRegistered );
      }

      // Disable the account, and wait for the state to go back to Unregistered
      alice.vccsAccountManager->disable( alice.vccsAccountHandle );
      {
         auto aliceUnRegistered = std::async(std::launch::async, [&] () {
            Account::VccsAccountStateChangedEvent evt;
            Account::VccsAccountHandle h;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
            ASSERT_TRUE( evt.oldState == Account::VccsAccountState_Registered );
            ASSERT_TRUE( evt.newState == Account::VccsAccountState_Unregistered );
         });
         waitFor( aliceUnRegistered );
      }
   }

   TEST_F(VccsModuleTest, CertPinningFail) {

      // NB: "alice" and "bob" are actually the same user (since syncing
      // really happens between different registrations of the same user)
      TestAccount alice("alice", Account_Init);

      // Set the settings
      Account::VccsAccountSettings settings;
      //settings.webSocketURL   = "ws://***********:8989/join";
      settings.wsSettings.webSocketURL   = "wss://127.0.0.1:8989/join";
      settings.wsSettings.certMode       = WebSocket::CertVerificationMode_Fail_If_No_Peer_Cert;
      settings.group          = "imap.mobilevoiplive.com";
      settings.userName       = "<EMAIL>";
      settings.password       = "vccs0007";
      settings.displayName    = "Smokey the Bear";
      settings.wsSettings.requiredCertPublicKeys.push_back( "bogus" );
      // sip Aor is "+<EMAIL>";

      alice.vccsAccountManager->configureDefaultAccountSettings( alice.vccsAccountHandle, settings );
      alice.vccsAccountManager->applySettings( alice.vccsAccountHandle ); // prob. not needed

      // Start the test harness.
      VccsTestHarness harness( TestEnvironmentConfig::testResourcePath() + "vccs_test_harness_scenarios/VCCS_Vertical1.dat", 8989, true ); // scenario file doesn't matter
      harness.start();

      // Enable the account.
      alice.vccsAccountManager->enable( alice.vccsAccountHandle );

      // Wait for the account to reach the registered state
      {
         auto aliceRegistered = std::async(std::launch::async, [&] () {
            Account::VccsAccountStateChangedEvent evt;
            Account::VccsAccountHandle h;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
            ASSERT_TRUE( evt.oldState == Account::VccsAccountState_Unregistered );
            ASSERT_TRUE( evt.newState == Account::VccsAccountState_Registering );

            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
            ASSERT_TRUE( evt.oldState == Account::VccsAccountState_Registering );
            ASSERT_TRUE( evt.newState == Account::VccsAccountState_WaitingToRegister );
         });
         waitFor( aliceRegistered );
      }

      // Disable the account, and wait for the state to go back to Unregistered
      alice.vccsAccountManager->disable( alice.vccsAccountHandle );
      {
         auto aliceUnRegistered = std::async(std::launch::async, [&] () {
            Account::VccsAccountStateChangedEvent evt;
            Account::VccsAccountHandle h;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
            ASSERT_TRUE( evt.oldState == Account::VccsAccountState_WaitingToRegister );
            ASSERT_TRUE( evt.newState == Account::VccsAccountState_Unregistered );
         });
         waitFor( aliceUnRegistered );
      }
   }
   
   #define assertParticipantAdded(account, displayName, callStatus) \
   { \
      ASSERT_NO_FATAL_FAILURE(expectParticipantAdded(__LINE__, account, displayName, callStatus)); \
   }
   
   void expectParticipantAdded(int line, TestAccount& account, const cpc::string& displayName, Conference::CallStatus callStatus = Conference::CallStatus_InConference)
   {
      Conference::ParticipantListUpdatedEvent evt;
      Conference::VccsConferenceHandle h;
      ASSERT_TRUE( account.vccsEvents->waitForEvent(
         line,
         "VccsConferenceHandler::onParticipantListUpdated",
         15000,
         AlwaysTruePred( ),
         h, evt ) );
      ASSERT_EQ(evt.changes.size(), 1);
      ASSERT_EQ(evt.changes[0].participant.displayName, displayName);
      ASSERT_EQ(evt.changes[0].changeType, Conference::ChangeType_Add);
      ASSERT_EQ(evt.changes[0].participant.callStatus, callStatus);

   }
   
   #define assertParticipantUpdated(account, displayName, callStatus) \
   { \
      ASSERT_NO_FATAL_FAILURE(expectParticipantUpdated(__LINE__, account, displayName, callStatus)); \
   }
   
   void expectParticipantUpdated(int line, TestAccount& account, const cpc::string& displayName, Conference::CallStatus callStatus = Conference::CallStatus_InConference)
   {
      Conference::ParticipantListUpdatedEvent evt;
      Conference::VccsConferenceHandle h;
      ASSERT_TRUE( account.vccsEvents->waitForEvent(
         line,
         "VccsConferenceHandler::onParticipantListUpdated",
         15000,
         AlwaysTruePred( ),
         h, evt ) );
      ASSERT_EQ(evt.changes.size(), 1);
      ASSERT_EQ(evt.changes[0].changeType, Conference::ChangeType_Update);
      ASSERT_EQ(evt.changes[0].participant.displayName, displayName);
      ASSERT_EQ(evt.changes[0].participant.callStatus, callStatus);
   }
   
   TEST_F(VccsModuleTest, VerifyParticipantUpdates) {

      // NB: "alice" and "bob" are actually the same user (since syncing
      // really happens between different registrations of the same user)
      TestAccount alice("alice", Account_Init);

      // Set the settings
      Account::VccsAccountSettings settings;
      settings.wsSettings.webSocketURL   = "ws://127.0.0.1:8989/join";
      settings.wsSettings.certMode       = WebSocket::CertVerificationMode_None;
      settings.group          = "counterpath.com";
      settings.userName       = "<EMAIL>";
      settings.password       = "vccs0007";
      settings.displayName    = "Jason Zablotny";
      // sip Aor is "+<EMAIL>";

      alice.vccsAccountManager->configureDefaultAccountSettings( alice.vccsAccountHandle, settings );
      alice.vccsAccountManager->applySettings( alice.vccsAccountHandle ); // prob. not needed

      // note the scenario has one participant update fired before SUBSCRIBE request completes.
      // OBELISK-5655 tracked an issue where this would result in broken subsequent participant updates
      VccsTestHarness harness( TestEnvironmentConfig::testResourcePath() + "vccs_test_harness_scenarios/VCCS_VerifyParticipantUpdates.dat", 8989, false );
      harness.start();

      // Enable the account.
      alice.vccsAccountManager->enable( alice.vccsAccountHandle );

      // Wait for the account to reach the registered state
      {
         auto aliceRegistered = std::async(std::launch::async, [&] () {
            Account::VccsAccountStateChangedEvent evt;
            Account::VccsAccountHandle h;
            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
            ASSERT_TRUE( evt.oldState == Account::VccsAccountState_Unregistered );
            ASSERT_TRUE( evt.newState == Account::VccsAccountState_Registering );

            ASSERT_TRUE( cpcWaitForEvent(
               alice.vccsEvents,
               "VccsAccountHandler::onAccountStateChanged",
               15000,
               AlwaysTruePred( ),
               h, evt ) );
            ASSERT_TRUE( evt.oldState == Account::VccsAccountState_Registering );
            ASSERT_TRUE( evt.newState == Account::VccsAccountState_Registered );
         });
         waitFor( aliceRegistered );
      }

      // Query for the list of conferences
      alice.vccsConferenceManager->queryConferenceList( alice.vccsAccountHandle );

      Conference::SubscriptionInfo subInfo;
      subInfo.applicationID = "6728074311085997861129ef7fddb563121bbdd365ceff4092d04628b7d";
      subInfo.conferenceCode = "SXHQZTGEBX";
      subInfo.participantType = CPCAPI2::VCCS::Conference::ParticipantType_BriaDesktop;
      subInfo.capabilities = 30;

      // Subscribe to the conference from which we want events
      alice.vccsConferenceManager->subscribe( alice.vccsAccountHandle, subInfo );
      auto aliceSubscribe = std::async(std::launch::async, [&] () {
         Conference::SubscribeEvent evt;
         Account::VccsAccountHandle h;
         ASSERT_TRUE( cpcWaitForEvent(
            alice.vccsEvents,
            "VccsConferenceHandler::onSubscribe",
            15000,
            AlwaysTruePred( ),
            h, evt ) );
      });
      waitFor( aliceSubscribe );
      
      auto participantUpdates = std::async(std::launch::async, [&] ()
      {
         assertParticipantAdded(alice,    "Bill Liu",          Conference::CallStatus_InConference);
         assertParticipantAdded(alice,    "Fang Yang",         Conference::CallStatus_InConference);
         assertParticipantAdded(alice,    "Dominique Lacerte", Conference::CallStatus_InConference);
         assertParticipantAdded(alice,    "Jeremy Geras",      Conference::CallStatus_InConference);
         assertParticipantAdded(alice,    "R0NN13 J3N53N",     Conference::CallStatus_InConference);
         assertParticipantAdded(alice,    "George Kwei",       Conference::CallStatus_InConference);
         assertParticipantAdded(alice,    "Rahim Rehmat",      Conference::CallStatus_None);
         assertParticipantAdded(alice,    "Jason Zablotny",    Conference::CallStatus_None);
         assertParticipantUpdated(alice,  "Rahim Rehmat",      Conference::CallStatus_InConference);
         assertParticipantAdded(alice,    "Milan Dimitrijevic",Conference::CallStatus_None);
         assertParticipantAdded(alice,    "Daniel Shi",        Conference::CallStatus_None);
         assertParticipantUpdated(alice,  "Jason Zablotny",    Conference::CallStatus_InConference);
         assertParticipantAdded(alice,    "Carol Zhang",       Conference::CallStatus_None);
         assertParticipantUpdated(alice,  "Daniel Shi",        Conference::CallStatus_InConference);
         assertParticipantAdded(alice,    "Sanja Pekec",       Conference::CallStatus_InConference);
         assertParticipantUpdated(alice,  "Carol Zhang",       Conference::CallStatus_InConference);
         assertParticipantUpdated(alice,  "Milan Dimitrijevic",Conference::CallStatus_InConference);
         
         assertParticipantUpdated(alice,  "Dominique Lacerte", Conference::CallStatus_Ended);
         assertParticipantUpdated(alice,  "Daniel Shi",        Conference::CallStatus_Ended);
      });
      waitFor(participantUpdates);
      
      // Unsubscribe from events
      alice.vccsConferenceManager->unsubscribe( alice.vccsAccountHandle, "1172" );
      auto aliceUnsubscribe = std::async(std::launch::async, [&] () {
         Conference::UnsubscribeEvent evt;
         Account::VccsAccountHandle h;
         ASSERT_TRUE( cpcWaitForEvent(
            alice.vccsEvents,
            "VccsConferenceHandler::onUnsubscribe",
            15000,
            AlwaysTruePred( ),
            h, evt ) );
      });
      waitFor(aliceUnsubscribe);
      
   }

   // below is borrowed from collab_test_toolcpp

   #define STATUS_LOG_FMT(x) "[[OK[" << x << "]]]"
   #define FAIL_LOG_FMT(x) "[[NOK[" << x << "]]]"

   struct CollabTestToolConfig
   {
      cpc::vector<Account::VccsAccountSettings> vccsSettings;
      cpc::string vccsURL;
      int callDurationMilliSec;
      
      CollabTestToolConfig()
      {
         cpc::string vccsURL = "";
         cpc::vector<Account::VccsAccountSettings> vccsAccountSettings;
         callDurationMilliSec = 10000;
      }
   };

   void setVccsAccount(TestAccount& alice, const Account::VccsAccountSettings& vccsAccountsettings)
   {
      int result = alice.vccsAccountManager->configureDefaultAccountSettings( alice.vccsAccountHandle, vccsAccountsettings);
      ASSERT_EQ(kSuccess, result) << FAIL_LOG_FMT("Config account settigns failed");
      alice.vccsAccountManager->applySettings( alice.vccsAccountHandle ); // prob. not needed


      result = alice.vccsAccountManager->enable( alice.vccsAccountHandle );
      ASSERT_EQ(kSuccess, result) << FAIL_LOG_FMT("Failed to enable account");

      // Wait for the account to reach the registered state
      {
         auto aliceRegistered = std::async(std::launch::async, [&] () {
            Account::VccsAccountStateChangedEvent evt;
            Account::VccsAccountHandle h;
            ASSERT_TRUE( cpcWaitForEvent(
                                         alice.vccsEvents,
                                         "VccsAccountHandler::onAccountStateChanged",
                                         15000,
                                         AlwaysTruePred( ),
                                         h, evt ) );
            ASSERT_TRUE( evt.oldState == Account::VccsAccountState_Unregistered ) << FAIL_LOG_FMT("account state is wrong, expect: Unregistered");
            ASSERT_TRUE( evt.newState == Account::VccsAccountState_Registering ) << FAIL_LOG_FMT("account state failed to change from Unregistered to Registering");
            safeCout(STATUS_LOG_FMT("Account is registering"));
            
            ASSERT_TRUE( cpcWaitForEvent(
                                         alice.vccsEvents,
                                         "VccsAccountHandler::onAccountStateChanged",
                                         15000,
                                         AlwaysTruePred( ),
                                         h, evt ) );
            ASSERT_TRUE( evt.oldState == Account::VccsAccountState_Registering )<< FAIL_LOG_FMT("account state is wrong, expect: Registering");
            ASSERT_TRUE( evt.newState == Account::VccsAccountState_Registered ) << FAIL_LOG_FMT("account state failed to change from Registering to Registered");
         });
         waitFor( aliceRegistered );
      }
      
      if (::testing::Test::HasFatalFailure())
      {
         FAIL() << FAIL_LOG_FMT("Account registration failed.");
      }
      safeCout(STATUS_LOG_FMT("VCCS account seems registered okay"));
   }

   void subscribeConference(TestAccount& alice, CollabTestToolConfig& config,
                            Conference::ConferenceDetails& outDetails,
                            Conference::VccsConferenceHandle& outConference,
                            cpc::vector<CPCAPI2::SipAccount::SipAccountSettings>& outAccounts,
                            cpc::vector<CPCAPI2::SipConversationSettings>& outConvs)
   {
      int result;

      cpc::string outWebSocketURL;
      cpc::string outServerName;
      int outPortNumber;
      cpc::string outGroupName;
      cpc::string outSubscriptionCode;
      result = alice.vccsAccountManager->crackVCCSURL(config.vccsURL, false, outWebSocketURL, outServerName, outPortNumber, outGroupName, outSubscriptionCode);
      ASSERT_TRUE(result) << FAIL_LOG_FMT("crack VCCS URL failure");

      // Subscribe to the conference from which we want events
      VCCS::Conference::SubscriptionInfo info;
      info.conferenceCode = outSubscriptionCode;
      info.applicationID = "";//config.appId;
      result = alice.vccsConferenceManager->subscribe( alice.vccsAccountHandle, info);
      ASSERT_EQ(kSuccess, result) << FAIL_LOG_FMT("Failed to subscribe to the conference");

      auto aliceSubscribe = std::async(std::launch::async, [&] () {
         Conference::SubscribeEvent evt;
         Account::VccsAccountHandle h;
         ASSERT_TRUE( cpcWaitForEvent(
                                      alice.vccsEvents,
                                      "VccsConferenceHandler::onSubscribe",
                                      15000,
                                      AlwaysTruePred( ),
                                      h, evt ) ) << FAIL_LOG_FMT("failed to subscribe to the conference ");

         ASSERT_TRUE(evt.hConference > 0);
         outConference = evt.hConference;
         //ASSERT_EQ(evt.directSIPAddress, "<EMAIL>") << FAIL_LOG_FMT("Direct sip address is wrong");
         ASSERT_EQ(alice.account->decodeProvisioningResponse(evt.directSIPProvisioning, outAccounts), kSuccess) << FAIL_LOG_FMT("sip account provisioning file format is invalid");
         ASSERT_NE(outAccounts.size(), 0) << FAIL_LOG_FMT("after subscribing to the conference, returned sip account settings is empty");
         ASSERT_EQ(alice.conversation->decodeProvisioningResponse(evt.directSIPProvisioning, outConvs), kSuccess) << FAIL_LOG_FMT("conversation provisioning file format is invalid");
         ASSERT_NE(outConvs.size(), 0) << FAIL_LOG_FMT("after subscribing to the conference, returned conversation settings is empty");
      });
      waitFor( aliceSubscribe );
      if (::testing::Test::HasFatalFailure())
      {
         FAIL() << FAIL_LOG_FMT("failed to subscribe to the conference ");
      }

      auto aliceConferenceUpdate = std::async(std::launch::async, [&] () {
         Conference::ConferenceListUpdatedEvent evt;
         Account::VccsAccountHandle h;
         ASSERT_TRUE( cpcWaitForEvent(
                                      alice.vccsEvents,
                                      "VccsConferenceHandler::onConferenceListUpdated",
                                      10000,
                                      AlwaysTruePred( ),
                                      h, evt ) ) << FAIL_LOG_FMT("missed Conference_Updated after subscription ");
         ASSERT_TRUE( evt.changes[0].conference.bridgeNumber.size() > 0 ) << FAIL_LOG_FMT("Bridge number is wrong");
         ASSERT_GE(evt.changes[0].conference.lobbySipAddress.size(), 0) << FAIL_LOG_FMT("SIP address to dial into the bridge is empty or not present (lobbySipAddress)");
         ASSERT_TRUE( evt.changes[0].conference.id != -1 );
         ASSERT_TRUE(evt.changes.size() != 0) << FAIL_LOG_FMT("no conference info");
         outDetails = evt.changes[0].conference;
      });
      waitFor(aliceConferenceUpdate);
      if (::testing::Test::HasFatalFailure())
      {
         FAIL() << FAIL_LOG_FMT("failure in ConferenceUpdate after subscribing to the conference");
      }
      
      auto aliceParticipantupdate = std::async(std::launch::async, [&] () {
         Conference::ParticipantListUpdatedEvent evt;
         Conference::VccsConferenceHandle h;
         ASSERT_TRUE( cpcWaitForEvent(
                                      alice.vccsEvents,
                                      "VccsConferenceHandler::onParticipantListUpdated",
                                      15000,
                                      AlwaysTruePred( ),
                                      h, evt ) ) << FAIL_LOG_FMT("missed Participant_Updated after subscription OR Participant_Updated was sent before conference_Updated and was ignored, the order should be conference_Updated then Participant_Updated");
         //   ASSERT_TRUE(evt.changes[0].participant.isModerator);
         ASSERT_TRUE(evt.changes.size() != 0) << FAIL_LOG_FMT("no participant info");

      });
      waitFor(aliceParticipantupdate);
      if (::testing::Test::HasFatalFailure())
      {
         FAIL() << FAIL_LOG_FMT("failure in participantUpated after subscribing to the conference");
      }
      safeCout(STATUS_LOG_FMT("subscribed to the conference"));
   }

   void setupSipAccount(TestAccount& alice, const CPCAPI2::SipAccount::SipAccountSettings& accountSettings)
   {
      alice.config.settings = accountSettings;

      alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings);
      alice.account->applySettings(alice.handle);
      alice.enable(false);
      ASSERT_NO_FATAL_FAILURE(assertAccountRegisteringEx(alice)) << FAIL_LOG_FMT("sip account account registration failure");
      ASSERT_NO_FATAL_FAILURE(assertAccountRegisteredEx(alice)) << FAIL_LOG_FMT("sip account registration failure");
      safeCout(STATUS_LOG_FMT("sip account seems registered okay"));
   }

   void collabCall(TestAccount& alice, cpc::string confUri, Conference::VccsConferenceHandle hConference,
                   int callDurationMilliSec, CPCAPI2::SipConversation::SipConversationHandle& aliceCall, const CollabTestToolConfig& config)
   {
      aliceCall = alice.conversation->createConversation(alice.handle);
      CPCAPI2::SipConversation::MediaInfo aliceVideoMedia;
      aliceVideoMedia.mediaDirection = CPCAPI2::SipConversation::MediaDirection_SendReceive;
      aliceVideoMedia.mediaType = CPCAPI2::SipConversation::MediaType_Video;
      aliceVideoMedia.mediaEncryptionOptions.mediaEncryptionMode = CPCAPI2::SipConversation::MediaEncryptionMode_SRTP_SDES_Encrypted;
      aliceVideoMedia.mediaEncryptionOptions.secureMediaRequired = true;

      CPCAPI2::SipConversation::MediaInfo aliceAudioMedia;
      aliceAudioMedia.mediaDirection = CPCAPI2::SipConversation::MediaDirection_SendReceive;
      aliceAudioMedia.mediaType = CPCAPI2::SipConversation::MediaType_Audio;
      aliceAudioMedia.mediaEncryptionOptions.mediaEncryptionMode = CPCAPI2::SipConversation::MediaEncryptionMode_SRTP_SDES_Encrypted;
      aliceAudioMedia.mediaEncryptionOptions.secureMediaRequired = true;

      //alice.video->setCaptureDevice(CPCAPI2::Media::kCustomVideoSourceDeviceId);
      //int ret = alice.video->startCapture();
      //ASSERT_EQ(ret, kSuccess) << FAIL_LOG_FMT("failed to start capture with custom device");

      int ret = alice.conversation->addParticipant(aliceCall, confUri);
      ASSERT_EQ(kSuccess, ret) << FAIL_LOG_FMT("Failed to add participant");
      safeCout(STATUS_LOG_FMT("call in bridge"));

      ret = alice.conversation->configureMedia(aliceCall, aliceAudioMedia);
      ret = alice.conversation->configureMedia(aliceCall, aliceVideoMedia);
      ASSERT_EQ(ret, kSuccess) << "confgiure alice media failed";

      ret = alice.conversation->start(aliceCall);
      ASSERT_EQ(kSuccess, ret) << FAIL_LOG_FMT("Failed to start a conversation");
      safeCout(STATUS_LOG_FMT("making an audio/video call..."));

      ASSERT_NO_FATAL_FAILURE(assertConversationStateChanged(alice, aliceCall, CPCAPI2::SipConversation::ConversationState_Connected)) << FAIL_LOG_FMT("conversation not connected");
      safeCout(STATUS_LOG_FMT("conversation connected..."));
      std::this_thread::sleep_for(std::chrono::milliseconds(callDurationMilliSec));

      // check there is participant in the conference, and particpant status is in_conference
      auto aliceParticipantStatus = std::async(std::launch::async, [&] () {
         Conference::ParticipantListUpdatedEvent evt;
         Conference::VccsConferenceHandle h;
         ASSERT_TRUE( cpcWaitForEvent(
                                      alice.vccsEvents,
                                      "VccsConferenceHandler::onParticipantListUpdated",
                                      15000,
                                      AlwaysTruePred( ),
                                      h, evt ) ) << FAIL_LOG_FMT("missed Participant_Updated after call in the bridge");
         ASSERT_TRUE(evt.changes.size() != 0) << FAIL_LOG_FMT("no participant info");
         safeCout(STATUS_LOG_FMT("participant " << evt.changes[0].participant.sipUsername << " call status is IN_CONFERENCE"));
      });
      waitFor(aliceParticipantStatus);
      if (::testing::Test::HasFatalFailure())
      {
         FAIL() << FAIL_LOG_FMT("failed to join the conference");
      }

      CPCAPI2::SipConversation::SipConversationHandle h;
      CPCAPI2::SipConversation::ConversationStatisticsUpdatedEvent evt;
      CPCAPI2::SipConversation::SipConversationManagerInternal* convInternal = dynamic_cast<CPCAPI2::SipConversation::SipConversationManagerInternal*>(alice.conversation);
   }

   void collabCallEnd(TestAccount& alice, Conference::VccsConferenceHandle hConference,
                      const CPCAPI2::SipConversation::SipConversationHandle& aliceCall,
                      const CollabTestToolConfig& config,
                      const Conference::ConferenceDetails& confDetails)
   {
      safeCout(STATUS_LOG_FMT("going to end the call-------------"));
      int result = alice.conversation->end(aliceCall);
      ASSERT_EQ(kSuccess, result) << FAIL_LOG_FMT("Failed to end the conversation");
      ASSERT_NO_FATAL_FAILURE(assertConversationEnded(alice, aliceCall, CPCAPI2::SipConversation::ConversationEndReason_UserTerminatedLocally)) << FAIL_LOG_FMT("first account outgoing call end signal failure");
      safeCout(STATUS_LOG_FMT("conversation  ends"));

      alice.vccsConferenceManager->unsubscribe( alice.vccsAccountHandle, confDetails.bridgeNumber );
      auto aliceUnsubscribe = std::async(std::launch::async, [&] () {
         Conference::UnsubscribeEvent evt;
         Account::VccsAccountHandle h;
         ASSERT_TRUE( cpcWaitForEvent(
                                      alice.vccsEvents,
                                      "VccsConferenceHandler::onUnsubscribe",
                                      15000,
                                      AlwaysTruePred( ),
                                      h, evt ) );
      });
      waitFor( aliceUnsubscribe );
      if (::testing::Test::HasFatalFailure())
      {
         FAIL() << FAIL_LOG_FMT("failed to unsubscribe the conference");
      }
      safeCout(STATUS_LOG_FMT("unsubscribed to the conference"));
      alice.vccsConferenceManager->queryConferenceDetails(alice.vccsAccountHandle, hConference);
      safeCout(STATUS_LOG_FMT("conference ended"));
   }

   void testAudioVideoCollabCall(TestAccount& alice, Conference::VccsConferenceHandle hConference, const CPCAPI2::SipAccount::SipAccountSettings& sipAccountSettings,
                                 const CPCAPI2::SipConversationSettings& sipConvSettings, const Conference::ConferenceDetails& details, const CollabTestToolConfig& config)
   {
      
      ASSERT_NO_FATAL_FAILURE(setupSipAccount(alice, sipAccountSettings));
      ASSERT_EQ(kSuccess, alice.conversation->setDefaultSettings(alice.handle, sipConvSettings));
      
     
      cpc::string confUri("sip:");
      confUri.append(details.lobbySipAddress);
      safeCout("conference uri is: " << confUri);
      CPCAPI2::SipConversation::SipConversationHandle aliceCall;
      ASSERT_NO_FATAL_FAILURE(collabCall(alice, confUri, hConference, config.callDurationMilliSec, aliceCall, config));
      ASSERT_NO_FATAL_FAILURE(collabCallEnd(alice, hConference, aliceCall, config, details));
   }

   TEST_F(VccsModuleTest, DISABLED_ManualCollabCall) {

      TestAccount alice("alice", Account_Init);
      
      alice.initiateVideo();
      
      CollabTestToolConfig collabTestToolConfig;
      VCCS::Account::VccsAccountSettings vccsAccountSettings;
      vccsAccountSettings.group = "counterpath.com";
      
      vccsAccountSettings.userName = getenv("CPCAPI2_VCCS_USERNAME");
      vccsAccountSettings.password = getenv("CPCAPI2_VCCS_PASSWORD");
      ASSERT_FALSE(vccsAccountSettings.userName.empty()) << "You need to populate CPCAPI2_VCCS_USERNAME and CPCAPI2_VCCS_PASSWORD env variables for this test";
      
      vccsAccountSettings.displayName = "CPCAPI2";
      vccsAccountSettings.wsSettings.webSocketURL = "https://collab.cloudprovisioning.com:443/join";
      vccsAccountSettings.wsSettings.isLoginRequired = true;
      
      collabTestToolConfig.vccsSettings.push_back(vccsAccountSettings);
      collabTestToolConfig.vccsURL = "https://collab.cloudprovisioning.com:443/join/MWTFOMFEGL/counterpath.com";
      
     // ASSERT_NE(collabTestToolConfig.vccsSettings.size(), 0) << FAIL_LOG_FMT("returned vccs account settings is empty");
      ASSERT_NO_FATAL_FAILURE(setVccsAccount(alice, vccsAccountSettings));
      
      Conference::ConferenceDetails details;
      Conference::VccsConferenceHandle hConference( -1 );

      cpc::vector<CPCAPI2::SipAccount::SipAccountSettings> accounts;
      cpc::vector<CPCAPI2::SipConversationSettings> convs;
         
      ASSERT_NO_FATAL_FAILURE(subscribeConference(alice, collabTestToolConfig, details, hConference, accounts, convs));

      ASSERT_NO_FATAL_FAILURE(testAudioVideoCollabCall(alice, hConference, accounts[0], convs[0], details, collabTestToolConfig));
   }

}  // namespace

#endif // (CPCAPI2_BRAND_VCCS_MODULE == 1)

