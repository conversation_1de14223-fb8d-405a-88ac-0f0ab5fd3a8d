package com.counterpath.sdkdemo.advancedaudiocall;


import android.Manifest;
import android.app.Activity;
import android.content.pm.PackageManager;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;
import android.os.PowerManager;
import android.system.ErrnoException;
import android.util.Log;
import android.view.SurfaceView;
import android.view.View;
import android.widget.*;

import com.counterpath.sdk.SipVideo;
import com.counterpath.sdk.android.NetworkChangeManager;
import com.counterpath.sdk.android.SipPhoneAndroid;

import com.counterpath.sdk.android.SipVideoAndroid;
import com.counterpath.sdk.android.UnitTests;
import com.counterpath.sdk.android.VideoRenderer;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.FileNotFoundException;
import android.content.res.AssetManager;

public class AdvAudioCallActivity extends Activity {


    PowerManager.WakeLock mScreenLock;
    LinearLayout mDnsWarning;

    @Override
    protected void onCreate(Bundle savedInstanceState) {

        super.onCreate(savedInstanceState);
        setContentView(R.layout.audio_call);
        mDnsWarning = (LinearLayout) findViewById(R.id.dnsWarning);

        android.os.PowerManager pm = (android.os.PowerManager)getSystemService(android.content.Context.POWER_SERVICE);
        mScreenLock = pm.newWakeLock(android.view.WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON  | android.os.PowerManager.ON_AFTER_RELEASE, "CPCAPI2AutoTests:Screenlock");
        mScreenLock.acquire();

        UnitTests unittests = new UnitTests();
        Boolean permissions = checkPermission();
        if(!permissions) {
            finishAndRemoveTask();
            System.exit(1);
        }

        copyAssetFolder(getAssets(), "autotestresources",
                getFilesDir().getAbsolutePath() + "/runtime_resources");

        String pathToReproconfig = getApplicationContext().getFilesDir().getAbsolutePath().toString() + java.io.File.separator;
        try {
            SipPhoneAndroid.setInternalContext(getApplicationContext());

        } catch (SecurityException e) {
            Log.e("ERROR: ", "========>>> Instantiating SipPhoneAndroid() failed <<<========");
            e.printStackTrace();
        }

        FrameLayout localVideoBox = (FrameLayout) findViewById(R.id.localVideo);
        FrameLayout remoteVideoBox = (FrameLayout) findViewById(R.id.remoteVideo);

        SurfaceView localSurface = VideoRenderer.CreateRenderer(getApplicationContext(), true);
        SurfaceView remoteSurface = VideoRenderer.CreateRenderer(getApplicationContext(), true);

        int localSurfaceHandle = unittests.registerLocalRenderTarget(localSurface);
        int remoteSurfaceHandle = unittests.registerRemoteRenderTarget(remoteSurface);

        localVideoBox.addView(localSurface);
        remoteVideoBox.addView(remoteSurface);

        String argsDelim = " ";
        String args = pathToReproconfig;
        args += argsDelim + getIntent().getStringExtra("argv");
        // uncomment to have gtest break on failures
        // args += argsDelim + "--gtest_break_on_failure";
        // args += argsDelim + "--gtest_filter=PttModuleTestUnicast.DisableWifi";

        String tempPath = "/data/data/" + getApplicationContext().getPackageName() + "/files/";
        try
        {
            android.system.Os.setenv("CPCAPI2_RESOURCE_PATH", "/data/user/0/" + getApplicationContext().getPackageName() + "/files/runtime_resources/", true);
            android.system.Os.setenv("CPCAPI2_TEMP_PATH", tempPath, true);
            //android.system.Os.setenv("CPCAPI2_VCCS_USERNAME", "<EMAIL>", true);
            //android.system.Os.setenv("CPCAPI2_VCCS_PASSWORD", "", true);
            // run unbound on your PC, uncomment below line and set to IP address of your PC if running
            // tests that require unbound
            // android.system.Os.setenv("CPCAPI2_DNS_SERVER", "unbound dns ip", true);
            // android.system.Os.setenv("CPCAPI2_DNS_SERVER", "**************", true);
        }
        catch (android.system.ErrnoException ex)
        {
            Log.e("ERROR:", "Couldn't set environment variables for test");
        }

        // work around issue where BI module fails to read saved cache files if there was a 32->64-bit app transition
        File dcache = new File(tempPath + "DCACHE");
        dcache.delete();
        File ecache = new File(tempPath + "ECACHE");
        ecache.delete();


        String cpcapi2DnsServerEnv = android.system.Os.getenv("CPCAPI2_DNS_SERVER");
        if (cpcapi2DnsServerEnv == null || cpcapi2DnsServerEnv.length() == 0)
        {
            mDnsWarning.setVisibility(View.VISIBLE);
        }
        else
        {
            mDnsWarning.setVisibility(View.GONE);
        }


        class RunThread implements Runnable {
            String mArgs;
            Activity mActivity;


            RunThread(String args, Activity activity) {
                mArgs = args;
                mActivity = activity;
            }

            @Override
            public void run() {
                String argv[] = mArgs.split(" ");
                unittests.runUnitTests(argv, mActivity.getApplicationContext(), mActivity.getExternalFilesDir(null).toString());
                mActivity.finishAndRemoveTask();
                System.exit(0);
            }
        }

        AsyncTask.execute(new RunThread(args, this));
    }

    private static boolean copyAssetFolder(AssetManager assetManager,
                                           String fromAssetPath, String toPath) {
        try {
            String[] files = assetManager.list(fromAssetPath);
            new File(toPath).mkdirs();
            boolean res = true;
            for (String file : files)
                if (file.contains("."))
                    res &= copyAsset(assetManager,
                            fromAssetPath + "/" + file,
                            toPath + "/" + file);
                else
                    res &= copyAssetFolder(assetManager,
                            fromAssetPath + "/" + file,
                            toPath + "/" + file);
            return res;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    private void copyAssets() {
        android.content.res.AssetManager assetManager = getAssets();
        String[] files = null;
        try {
            files = assetManager.list("");
        } catch (IOException e) {
            Log.e("tag", "Failed to get asset file list.", e);
        }
        if (files != null) for (String filename : files) {
            InputStream in = null;
            OutputStream out = null;
            try {
                in = assetManager.open(filename);
                File outFile = new File(getFilesDir().getAbsolutePath(), filename);
                out = new FileOutputStream(outFile);
                copyFile(in, out);
            } catch(IOException e) {
                Log.e("tag", "Failed to copy asset file: " + filename, e);
            }
            finally {
                if (in != null) {
                    try {
                        in.close();
                    } catch (IOException e) {
                        // NOOP
                    }
                }
                if (out != null) {
                    try {
                        out.close();
                    } catch (IOException e) {
                        // NOOP
                    }
                }
            }
        }
    }

    private static boolean copyAsset(AssetManager assetManager,
                                     String fromAssetPath, String toPath) {
        InputStream in = null;
        OutputStream out = null;
        try {
            in = assetManager.open(fromAssetPath);
            new File(toPath).createNewFile();
            out = new FileOutputStream(toPath);
            copyFile(in, out);
            in.close();
            in = null;
            out.flush();
            out.close();
            out = null;
            return true;
        } catch(Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    private static void copyFile(InputStream in, OutputStream out) throws IOException {
        byte[] buffer = new byte[1024];
        int read;
        while((read = in.read(buffer)) != -1){
            out.write(buffer, 0, read);
        }
    }


    Boolean checkPermission() {
        if (Build.VERSION.SDK_INT >= 23) {
            if (checkSelfPermission(Manifest.permission.READ_EXTERNAL_STORAGE) == PackageManager.PERMISSION_DENIED ||
                    checkSelfPermission(Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_DENIED||
                    checkSelfPermission(Manifest.permission.RECORD_AUDIO) == PackageManager.PERMISSION_DENIED||
                    checkSelfPermission(Manifest.permission.READ_PHONE_STATE)== PackageManager.PERMISSION_DENIED ||
                    checkSelfPermission(Manifest.permission.CAMERA) == PackageManager.PERMISSION_DENIED) {
                Log.e("ERROR: ", "===============>>> Permissions are not Granted//////////////////////////////////////////////////////////////////////////////////////////");
                return false;
            }
        }
    return true;
    }
}
