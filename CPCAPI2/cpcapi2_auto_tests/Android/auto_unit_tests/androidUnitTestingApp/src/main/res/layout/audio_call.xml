<?xml version="1.0" encoding="utf-8"?>

<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <TextView
            android:id="@+id/textView2"
            android:layout_width="383dp"
            android:layout_height="71dp"
            android:layout_marginStart="2dp"
            android:layout_marginTop="55dp"
            android:text="Auto tests should automatically start" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="161dp"
            android:orientation="horizontal">

            <FrameLayout
                android:id="@+id/localVideo"
                android:layout_width="189dp"
                android:layout_height="150dp"
                android:visibility="visible">

            </FrameLayout>

            <FrameLayout
                android:id="@+id/remoteVideo"
                android:layout_width="189dp"
                android:layout_height="150dp"
                android:visibility="visible">

            </FrameLayout>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/dnsWarning"
            android:layout_width="match_parent"
            android:layout_height="292dp"
            android:orientation="vertical"
            tools:visibility="invisible">

            <TextView
                android:id="@+id/textView"
                android:layout_width="383dp"
                android:layout_height="71dp"
                android:layout_marginStart="0dp"
                android:layout_marginTop="55dp"
                android:text="Warning: CPCAPI2_DNS_SERVER not set!"
                android:textColor="#FF2B2B"
                android:textSize="24sp" />

            <TextView
                android:id="@+id/textView3"
                android:layout_width="380dp"
                android:layout_height="149dp"
                android:layout_marginStart="2dp"
                android:layout_marginTop="0dp"
                android:text="CPCAPI2_DNS_SERVER environment variable not set. Any auto tests that rely on our unbound DNS server will likely fail. You can run unbound on your PC, then set CPCAPI2_DNS_SERVER env variable in Java auto test init code with IP address of your PC" />
        </LinearLayout>

    </LinearLayout>

</RelativeLayout>