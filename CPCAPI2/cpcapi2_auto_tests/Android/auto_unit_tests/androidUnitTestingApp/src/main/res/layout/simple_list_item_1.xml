<?xml version="1.0" encoding="utf-8"?>

<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="?android:attr/listPreferredItemHeight"
    android:padding="6dip" >

    <ImageView
        android:id="@+id/icon"
        android:layout_width="wrap_content"
        android:layout_height="fill_parent"
        android:layout_alignParentBottom="true"
        android:layout_alignParentTop="true"
        android:layout_marginRight="6dip"
        android:contentDescription="TODO"
        android:src="@drawable/ic_launcher" />

    <TextView
        android:id="@+id/firstLine"
        android:layout_width="200dp"
        android:layout_height="fill_parent"
        android:layout_alignParentTop="true"
        android:layout_marginRight="0dip"
        android:layout_alignWithParentIfMissing="true"
        android:layout_toRightOf="@id/icon"
        android:gravity="center_vertical"
        android:text="Example application"
        android:textSize="16sp"
        android:ellipsize="end"
        android:maxLines="1"
        android:singleLine="true"/>

    <CheckBox
        android:id="@+id/enableCodec"
        android:layout_width="wrap_content"
        android:layout_height="fill_parent"
        android:layout_alignParentRight="true"
        android:layout_alignWithParentIfMissing="true"
        android:layout_toRightOf="@id/firstLine"
        android:gravity="center_vertical" />

</RelativeLayout> 