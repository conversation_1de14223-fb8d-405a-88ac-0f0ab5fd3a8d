<?xml version="1.0" encoding="utf-8"?>
<resources>

    <string name="app_name">CPCAPI2 AdvancedAudioCall</string>
    <string name="username">Username: </string>
    <string name="domain">Domain:</string>
    <string name="licensekey">License Key:</string>
    <string name="register">Register</string>
    <string name="unregister">Disconnect</string>
    <string name="password">Password: </string>

    <string name="status_registered">Registered</string>
    <string name="status_unregistered">Unregistered</string>
    <string name="status_registering">Registering</string>
    <string name="status_unregistering">Unregistering</string>
    <string name="status_waiting">Failed to register; automatically retrying after wait.</string>

    <string name="callTo">Call To:</string>
    <string name="call">Call</string>
    <string name="endCall">End</string>
    <string name="holdCall">Toggle Hold</string>
    <string name="transferCall">Transfer</string>
    <string name="speakerphone">Speaker<PERSON>hone</string>
    <string name="micMute">Mute Mic</string>
    <string name="spkMute">Mute Speaker</string>

    <string name="callDialogTitle">Incoming Call</string>
    <string name="callDialogMessage">Incoming Call from: </string>
    <string name="callDialogAcceptCall">Answer</string>
    <string name="callDialogDeclineCall">Decline</string>

    <string name="defaultUser"></string>
    <string name="defaultDomain"></string>
    <string name="defaultTo"></string>

    <string name="natServer">NAT Server</string>
    <string name="natTraversal">NAT Traversal</string>
    <string name="natTraversalPrompt">Select NAT Traversal</string>
    <string-array name="natTraversalValues">
        <item>AUTO</item>
        <item>STUN</item>
        <item>TURN</item>
        <item>ICE</item>
        <item>NONE</item>
    </string-array>

    <string name="dtmfMode">DTMF Mode</string>
    <string name="dtmfModePrompt">Select DTMF Mode</string>
    <string-array name="dtmfModeValues">
        <item>2833</item>
        <item>in-band</item>
        <item>SIP INFO</item>
    </string-array>
    <string name="btDtmf1">1</string>
    <string name="btDtmf2">2</string>
    <string name="btDtmf3">3</string>
    <string name="btDtmf4">4</string>
    <string name="btDtmf5">5</string>
    <string name="btDtmf6">6</string>
    <string name="btDtmf7">7</string>
    <string name="btDtmf8">8</string>
    <string name="btDtmf9">9</string>
    <string name="btDtmfAst">*</string>
    <string name="btDtmfZero">0</string>
    <string name="btDtmfHash">#</string>

    <string name="sipTransport">SIP Transport</string>
    <string name="transport">SIP Transport</string>
    <string name="transportPrompt">Select SIP Transport</string>
    <string-array name="transportValues">
        <item>UDP</item>
        <item>TCP</item>
        <item>TLS</item>
    </string-array>

    <string name="outboundProxy">Outbound Proxy</string>
    <string name="authUsername">Auth Username</string>
    <string name="useRport">Use RPORT</string>
    <string name="useOutbound">Use Outbound Proxy</string>

    <string name="audioDevice">Audio Device</string>
    <string name="audioDevicePrompt">Select Audio Device</string>
    <string name="enableMicrophoneGain">Enable Microphone Gain Control</string>
    <string name="microphoneTargetLevel">Microphone Target Level (-30 to 0 dB)</string>
    <string name="defaultMicrophoneTargetLevel">-3</string>
    <string name="speakerTargetLevel">Speaker Target Level (-30 to 0 dB)</string>
    <string name="defaultSpeakerTargetLevel">-3</string>
    <string name="microphoneMaxCompression">Microphone Compression Gain (0 to 90 dB)</string>
    <string name="defaultMicrophoneMaxCompression">3</string>
    <string name="speakerMaxCompression">Speaker Compression Gain (0 to 90 dB)</string>
    <string name="defaultSpeakerMaxCompression">3</string>
    <string name="enableSpeakerGain">Enable Speaker Gain Control</string>
    <string name="audioGainPrompt">Show Audio Gain Settings</string>

    <string name="showCodecLayout">Show Available Codecs</string>
    
    
    <string name="SRTP">Select Media Encryption (TLS Only)</string>
    <string name="onlyEncryptedCalls">Make and Accept only encrypted calls</string>
    <string name="noEncrpyptedCalls">Do not allow encrypted calls</string>

    <string name="sslVersion">SSL Version (for TLS only)</string>
    <string name="sslVersionPrompt">Select SSL Version</string>
    <string name="username_uem">Username</string>
    <string name="password_uem">Password</string>
    <string name="login">Login</string>
    <string-array name="sslVersionValues">
        <item>TLS_V1_0</item>
        <item>TLS_V1_1</item>
        <item>TLS_V1_2</item>
        <item>SSL_HIGHEST</item>
    </string-array>
</resources>


