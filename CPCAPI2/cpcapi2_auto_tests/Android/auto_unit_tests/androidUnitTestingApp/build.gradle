buildscript {
    repositories {
        maven { url 'https://repo1.maven.org/maven2' }
        jcenter()
        google()
    }
    dependencies {
        // latest can be found http://search.maven.org/#search%7Cga%7C1%7Ccom.android.tools.build%20gradle
        classpath 'com.android.tools.build:gradle:7.3.1'
    }
}
apply plugin: 'android'

dependencies {
    implementation files('libs/CPCAPI2_Android.jar')
}

allprojects {
    repositories {
        google()
        jcenter()
    }
}

android {
    compileSdkVersion 33
    defaultConfig {
        applicationId 'com.counterpath.sdkdemo.advancedaudiocall'
        minSdkVersion 23
        targetSdkVersion 33
    }

    ndkVersion "21.0.6113669"

    signingConfigs {
        release {
            storeFile = new File("release.keystore")
            storePassword = "android"
            keyAlias = "sdkdemo"
            keyPassword = "android"
        }
    }

    buildTypes {
        debug {
            jniDebuggable true
            // seemingly needed otherwise building for only
            // armeabi-v7a and running on ARM64 capable device
            // will result in Android running the app in ARM64 mode
            ndk {
                abiFilters "armeabi-v7a", "arm64-v8a"
            }
        }

        release {
            signingConfig signingConfigs.release
            // seemingly needed otherwise building for only
            // armeabi-v7a and running on ARM64 capable device
            // will result in Android running the app in ARM64 mode
            ndk {
                abiFilters "armeabi-v7a", "arm64-v8a"
            }
        }
    }
    sourceSets {
        main {
            delete "assets"
            assets.srcDirs = ['src/main/assets', 'assets/']
            jniLibs.srcDir new File(projectDir, 'libs')
            copy{
                from file('../../../runtime_resources')
                into file('assets/autotestresources')
            }
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    namespace 'com.counterpath.sdkdemo.advancedaudiocall'
}