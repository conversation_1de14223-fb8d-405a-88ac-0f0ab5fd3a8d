set NDK_BUILD_JOBS=6
cd $ANDROID_PATH && $ANT_BUILD sample-debug -DrunAutotests=true -DtestsInclude=AccountModuleTest.*
cd src && javah -d $JNI_PATH com.counterpath.sdk.android.UnitTests


BUILD_ID=dontKillMe
$ADB_PATH shell am force-stop com.counterpath.sdkdemo.advancedaudiocall
cd $UNIT_TEST_PATH && ./gradlew assembleDebug && ./gradlew installDebug
$ADB_PATH push $UNIT_TEST_PATH/build/outputs/apk/androidUnitTestingApp-debug.apk /data/local/tmp/com.counterpath.sdkdemo.advancedaudiocall
$ADB_PATH shell pm install -t -r "/data/local/tmp/com.counterpath.sdkdemo.advancedaudiocall"
{
$ADB_PATH shell rm /sdcard/result.txt
set +x
}||{
set +x
}
$ADB_PATH logcat -c
$ADB_PATH logcat CPCAPI2_AUTOTESTS DEBUG &
$ADB_PATH shell am start -n "com.counterpath.sdkdemo.advancedaudiocall/com.counterpath.sdkdemo.advancedaudiocall.AdvAudioCallActivity" -a android.intent.action.MAIN -c android.intent.category.LAUNCHER
sleep 15
pid=$($ADB_PATH shell pidof com.counterpath.sdkdemo.advancedaudiocall)
while [ ! -z "$pid" ]
	do
	{
	pid=$($ADB_PATH shell pidof com.counterpath.sdkdemo.advancedaudiocall)
	}||{
		pid=
	}
done
set -x
$ADB_PATH shell am force-stop com.counterpath.sdkdemo.advancedaudiocall
result=$($ADB_PATH shell cat /sdcard/result.txt)
if [ "$result" -eq 1 ]
	then
    exit 1
fi
