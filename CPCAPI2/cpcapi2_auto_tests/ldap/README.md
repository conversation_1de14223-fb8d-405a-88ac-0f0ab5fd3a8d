# LDAP  testing environment

## Intro

To test LDAP functionalities, we will use OpenLDAP running in Docker. This will 
provide greater flexibility than targeting ActiveDirectory. Our build machines 
already have Docker installed, and it is easy to install it on any developer machine. 

Goal is to be able to run LDAP locally, so unit-test will target local machine. It is 
not a problem to run it on any test/build machine, however there will be one specific 
limitation in this case - certificates.

[<PERSON><PERSON><PERSON><PERSON>](Dockerfile) makes self-signed root CA which is then used to sign three 
certificates made for different FQDNs:
* ldap.sdk.local - this one is valid certificate
* expired.sdk.local - this one is expired
* invalid.sdk.local - this one is based on valid but with tampered bytes

These FQDNs have to exist in local **/etc/hosts** (and Windows equivalent).

We rebuild docker image each time there is no `ldap.sdk.local.pem` 
or is it about to expire in 4 weeks. It is also possible to force rebuild if needed. 
On each build, those certificates are going to be added to host keychain/store. This 
means that if we wanted some "central" LDAP to run somewhere and unit-tests to target 
it, tests would have to pull those certificates and import them to local keychain/store 
on change. So for now, we are not doing this.

Additional thing to note is: preparing computer to be able to execute LDAP tests is done 
using [Ansible](https://docs.ansible.com/). Since only requirement for Ansible is Python3, 
and we would still have to perform some calls from C++ to check certificates, manage them 
in keychain and eventually build and run Docker image - it made sense to offload most of 
these steps into Ansible. In some future we may separate "infrastructure" from "testing" 
tasks.

Since we cant run Docker on Android/iOS, on those platforms LDAP unit-tests are going to 
be disabled.

## Prerequisites

Prerequisites are as follows:
* Python 3.10+
* Docker 26.1+ with docker-buildx installed

Some platforms we use reached EOL, so for them, we have to manually install dependencies.
This step is also automated via Ansible for some of them, but for the record, manual steps are as follows.

#### 1. CentOS
```shell
sudo yum install gcc openssl-devel bzip2-devel libffi-devel perl zlib-devel -y

cd /tmp
SDK_ROOT=${HOME}/sdkvenv

SSL_LIB="openssl-1.1.1w.tar.gz"
curl -Lo "${SSL_LIB}" "https://github.com/openssl/openssl/releases/download/OpenSSL_1_1_1w/${SSL_LIB}"
tar zxf "${SSL_LIB}"
cd openssl-1.1.1w
./config --prefix="${SDK_ROOT}" --openssldir="${SDK_ROOT}/ssl"
make
make install_sw
cd ..

PYTHON_VER="3.10.9"
curl -Lo "Python-${PYTHON_VER}.tgz" https://www.python.org/ftp/python/${PYTHON_VER}/Python-${PYTHON_VER}.tgz
tar xf "Python-${PYTHON_VER}.tgz"
cd Python-${PYTHON_VER}
LDFLAGS="${LDFLAGS} -Wl,-rpath=${SDK_ROOT}/lib" ./configure --prefix="${SDK_ROOT}" --enable-optimizations --with-openssl="${SDK_ROOT}"
make
make install

sudo yum install -y yum-utils device-mapper-persistent-data lvm2
sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
sudo yum install docker-ce
sudo systemctl enable docker
sudo systemctl start docker
sudo usermod -a -G docker `whoami`
newgrp docker

mkdir -p ~/.docker/cli-plugins
curl -Lo ~/.docker/cli-plugins/docker-buildx https://github.com/docker/buildx/releases/download/v0.23.0/buildx-v0.23.0.linux-amd64
chmod +x ~/.docker/cli-plugins/docker-buildx

cat << EOF > ~/.docker/config.json
{
    "cliPluginsExtraDirs": [
        "~/docker/cli-plugins"
    ]
}
EOF
```