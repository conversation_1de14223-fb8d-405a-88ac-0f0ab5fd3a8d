# syntax=docker/dockerfile:1

FROM debian:stable-slim AS builder
LABEL maintainer="<PERSON> <nikola.r<PERSON><PERSON><PERSON><PERSON>@alianza.com>"

ARG DOMAIN="sdk.local"
ARG LDAP_ADMIN_PASS="secret"
ENV DEBIAN_FRONTEND="noninteractive"

# If using Podman, before build make sure to run/set: export BUILDAH_FORMAT=docker
SHELL ["/bin/bash", "-c"]

# Install packages
RUN <<EOF
apt-get update
apt-get install -y faketime openssl slapd
EOF

# Create valid certificates
RUN <<EOF
mkdir -p /artifacts/valid && cd /artifacts/valid
# create CA private (ca.key) and public (ca.pem) keys
openssl genrsa -out ca.key 2048
openssl req -new -key ca.key -x509 -days 1095 -subj "/C=US/OU=sdk" -out ca.pem
# create LDAP server private (ldapserver.key) and public (ldapserver.pem) keys
openssl genrsa -out ldapserver.key
openssl req -new -key ldapserver.key -subj "/CN=ldap.${DOMAIN}/C=US/OU=sdk" -out ldapserver.csr
openssl x509 -req -days 2000 -in ldapserver.csr -CA ca.pem -CAkey ca.key -CAcreateserial -out ldapserver.pem
EOF

# Create expired certificates
RUN <<EOF
mkdir -p /artifacts/expired && cd /artifacts/expired
# create CA private (ca.key) and public (ca.pem) keys
openssl genrsa -out ca.key 2048
faketime 'last week' openssl req -new -key ca.key -x509 -days 1095 -subj "/C=US/OU=sdk" -out ca.pem
# create LDAP server private (ldapserver.key) and public (ldapserver.pem) keys
openssl genrsa -out ldapserver.key
openssl req -new -key ldapserver.key -subj "/CN=expired.${DOMAIN}/C=US/OU=sdk" -out ldapserver.csr
faketime '3 days ago' openssl x509 -req -days 1 -in ldapserver.csr -CA ca.pem -CAkey ca.key -CAcreateserial -out ldapserver.pem
EOF

# Create invalid certificates (https://security.stackexchange.com/questions/60804/creating-an-x-509-certificate-with-an-invalid-signature)
RUN <<EOF
mkdir -p /artifacts/invalid && cd /artifacts/invalid
# copy CA certificates first
cp /artifacts/valid/ca* .
# create LDAP server private (ldapserver.key) and public (ldapserver.pem) keys
openssl genrsa -out ldapserver.key
openssl req -new -key ldapserver.key -subj "/CN=invalid.${DOMAIN}/C=US/OU=sdk" -out ldapserver.csr
openssl x509 -req -days 2000 -in ldapserver.csr -CA ca.pem -CAkey ca.key -CAcreateserial -out ldapserver.pem
# dump file content into array of lines
readarray -t lines < ldapserver.pem 
# get total lines count and index of line before last
lines_count=${#lines[@]}
change_line_index=$((lines_count - 2))
# convert line to change into an array so we can iterate over it
change_line=(`echo ${lines[change_line_index]} | sed 's/./& /g'`)
# find index of first = sign (if any)
for ((i=0; i<${#change_line[@]}; i++)); do if [ "${change_line[i]}" == "=" ]; then break; fi; done
# calculate new (invalid) character value five postions left of last sign that is not = sign: 
# set to R in all casses except if original value is also R - than use Q
if [ "${change_line[i-5]}" == "R" ]; then \
  change_line[i-5]="Q"; \
else \
  change_line[i-5]="R"; \
fi; \
# Rewrite ldapserver.pem
echo "" > ldapserver.pem
for ((i=0; i<${change_line_index}; i++)); do echo -e "${lines[i]}" >> ldapserver.pem; done
printf "%s" "${change_line[@]}" >> ldapserver.pem
echo -e "\n${lines[lines_count-1]}" >> ldapserver.pem
# compare signatures
valid_signature=`openssl x509 -in /artifacts/valid/ldapserver.pem -text -noout | tac | sed '/.*Signature Algorithm:/q' | tac`
invalid_signature=`openssl x509 -in /artifacts/invalid/ldapserver.pem -text -noout | tac | sed '/.*Signature Algorithm:/q' | tac`
if [ "$valid_signature" == "$invalid_signature" ]; then
  echo "Signature not changed"
  exit 1
fi
EOF

# Append hashed password to SLAPD defaults file
RUN SLAPD_PWD_HASH=$(slappasswd -s "$LDAP_ADMIN_PASS") &&\
 echo "SLAPD_PWD_HASH=${SLAPD_PWD_HASH}" >> /etc/default/slapd

RUN apt-get install -y less vim procps

# Activate modules
RUN . /etc/default/slapd && cat <<EOF > /artifacts/modules.ldif
dn: cn=module{0},cn=config
changetype: modify
add: olcModuleLoad
olcModuleLoad: memberof

dn: cn=module{0},cn=config
changetype: modify
add: olcModuleLoad
olcModuleLoad: refint
EOF

# Create administrative user LDIF
RUN . /etc/default/slapd && cat <<EOF > /artifacts/config.ldif
dn: olcDatabase={0}config,cn=config
changetype: modify
replace: olcRootDN
olcRootDN: cn=admin,cn=config

dn: olcDatabase={0}config,cn=config
changetype: modify
replace: olcRootPW
olcRootPW: ${SLAPD_PWD_HASH}
EOF

# Create certificates LDIF
RUN cat <<EOF > /artifacts/certs.ldif
dn: cn=config
add: olcTLSCACertificateFile
olcTLSCACertificateFile: /etc/ldap/certs/ca.pem
-
add: olcTLSCertificateFile
olcTLSCertificateFile: /etc/ldap/certs/ldapserver.pem
-
add: olcTLSCertificateKeyFile
olcTLSCertificateKeyFile: /etc/ldap/certs/ldapserver.key
EOF

# Create access LDIF
RUN cat <<EOF > /artifacts/access.ldif
dn: olcDatabase={1}mdb,cn=config
changetype: modify
replace: olcAccess
olcAccess: {0}to attrs=userPassword,shadowLastChange
    by self write by anonymous auth
    by group.exact="cn=admin,ou=groups,dc=sdk,dc=local" write
    by dn="cn=admin,dc=sdk,dc=local" write
    by * none
olcAccess: {1}to dn.base=""
    by * read
olcAccess: {2}to *
    by self write
    by group.exact="cn=admin,ou=groups,dc=sdk,dc=local" write
    by dn="cn=admin,dc=sdk,dc=local" write
    by * read
-
replace: olcRootDN
olcRootDN: cn=admin,dc=sdk,dc=local
-
replace: olcSuffix
olcSuffix: dc=sdk,dc=local
EOF

# Create RootPW LDIF
RUN . /etc/default/slapd && cat <<EOF > /artifacts/rootpw.ldif
dn: olcDatabase={1}mdb,cn=config
changetype: modify
replace: olcRootPW
olcRootPW: ${SLAPD_PWD_HASH}
EOF

# Create organization LDIF
RUN cat <<EOF > /artifacts/org.ldif
dn: dc=sdk,dc=local
objectClass: top
objectClass: dcObject
objectClass: organization
o: SDK
EOF

# Create organization data LDIF
RUN . /etc/default/slapd && cat <<EOF > /artifacts/data.ldif
dn: ou=users,dc=sdk,dc=local
objectClass: top
objectClass: organizationalUnit
ou: Users

dn: ou=groups,dc=sdk,dc=local
ou: groups
objectClass: organizationalUnit
objectClass: top

dn: ou=clients,dc=sdk,dc=local
ou: clients
objectClass: organizationalUnit
objectClass: top

dn: ou=computers,dc=sdk,dc=local
ou: computers
objectClass: organizationalUnit
objectClass: top

dn: uid=admin,ou=users,dc=sdk,dc=local
uid: admin
objectClass: inetOrgPerson
objectClass: posixAccount
cn: uid=admin,ou=users,dc=sdk,dc=local
givenName: Sdk
sn: Admin
mail: <EMAIL>
loginShell: /bin/bash
gecos: Sdk Admin
gidNumber: 1000
uidNumber: 1000
homeDirectory: /home/<USER>
userPassword: $SLAPD_PWD_HASH

dn: cn=ldap,dc=sdk,dc=local
cn: ldap
objectClass: organizationalRole
objectClass: simpleSecurityObject
userPassword: $SLAPD_PWD_HASH
description: LDAP bind user

dn: cn=admin,ou=groups,dc=sdk,dc=local
member: uid=admin,ou=users,dc=sdk,dc=local
objectClass: groupOfNames
objectClass: top
cn: admin

dn: cn=guest,ou=groups,dc=sdk,dc=local
member:
cn: guest
objectClass: groupOfNames
objectClass: top

dn: cn=alianza,ou=clients,dc=sdk,dc=local
objectClass: groupOfUniqueNames
objectClass: top
cn: alianza
uniqueMember: uid=admin,ou=users,dc=sdk,dc=local
EOF

# Create LDIF with large number of users
RUN for n in $(seq 1 2001); do \
  echo -e "dn: uid=user${n},ou=users,dc=sdk,dc=local\n\
uid: user${n}\n\
objectClass: inetOrgPerson\n\
objectClass: posixAccount\n\
cn: uid=user${n},ou=users,dc=sdk,dc=local\n\
givenName: Sdk\n\
sn: user${n}\n\
mail: user${n}@sdk.local\n\
loginShell: /bin/bash\n\
gecos: user${n}\n\
gidNumber: $((n+1000))\n\
uidNumber: $((n+1000))\n\
homeDirectory: /home/<USER>
" >> /artifacts/users.ldif; \
    done;

# Create sales group with members LDIF
RUN echo "dn: cn=sales,ou=groups,dc=sdk,dc=local" > /artifacts/sales.ldif;

RUN for n in $(seq 1 10); do \
  echo -e "member: uid=user${n},ou=users,dc=sdk,dc=local\
" >> /artifacts/sales.ldif; \
    done;

    RUN echo -e "cn: sales\n\
objectClass: groupOfNames\n\
objectClass: top\n\
" >> /artifacts/sales.ldif;

# Create deployment group with members LDIF
RUN echo "dn: cn=deployment,ou=groups,dc=sdk,dc=local" > /artifacts/deployment.ldif;

RUN for n in $(seq 8 30); do \
  echo -e "member: uid=user${n},ou=users,dc=sdk,dc=local\
" >> /artifacts/deployment.ldif; \
    done;

RUN echo -e "cn: deployment\n\
objectClass: groupOfNames\n\
objectClass: top\n\
" >> /artifacts/deployment.ldif;

# Create developer group with members LDIF
RUN echo "dn: cn=developer,ou=groups,dc=sdk,dc=local" > /artifacts/developer.ldif;

RUN for n in $(seq 40 2000); do \
  echo -e "member: uid=user${n},ou=users,dc=sdk,dc=local\
" >> /artifacts/developer.ldif; \
    done;

RUN echo -e "cn: developer\n\
objectClass: groupOfNames\n\
objectClass: top\n\
" >> /artifacts/developer.ldif;

# Create overlays LDIF
RUN . /etc/default/slapd && cat <<EOF > /artifacts/overlays.ldif
dn: olcOverlay=memberof,olcDatabase={1}mdb,cn=config
objectClass: olcMemberOf
objectClass: olcOverlayConfig
objectClass: olcConfig
objectClass: top
olcOverlay: memberof
olcMemberOfDangling: ignore
olcMemberOfRefInt: TRUE
olcMemberOfGroupOC: groupOfNames
olcMemberOfMemberAD: member
olcMemberOfMemberOfAD: memberOf

dn: olcOverlay={1}refint,olcDatabase={1}mdb,cn=config
objectClass: olcOverlayConfig
objectClass: olcRefintConfig
olcOverlay: {1}refint
olcRefintAttribute: owner
olcRefintAttribute: manager
olcRefintAttribute: uniqueMember
olcRefintAttribute: member
olcRefintAttribute: memberOf
EOF

# Configure and initialize LDAP
RUN <<EOF
mkdir -p /etc/ldap/certs
cp /artifacts/expired/* /etc/ldap/certs

. /etc/default/slapd
SLAPD_CONF=/etc/ldap/slapd.d
SLAPD_PIDFILE=`sed -ne 's/^olcPidFile:[[:space:]]\+\(.\+\)[[:space:]]*/\1/p' "$SLAPD_CONF"/'cn=config.ldif'`

slapd -F "${SLAPD_CONF}" -h "ldapi:///"

ldapadd -Y EXTERNAL -H ldapi:/// -f /artifacts/modules.ldif
ldapadd -Y EXTERNAL -H ldapi:/// -f /artifacts/config.ldif
ldapmodify -Y EXTERNAL -H ldapi:/// -f /artifacts/certs.ldif
ldapadd -Y EXTERNAL -H ldapi:/// -f /artifacts/access.ldif
ldapadd -Y EXTERNAL -H ldapi:/// -f /artifacts/rootpw.ldif
ldapadd -Y EXTERNAL -H ldapi:/// -f /artifacts/overlays.ldif
ldapadd -x -w "$LDAP_ADMIN_PASS" -H ldapi:/// -D "cn=admin,dc=sdk,dc=local" -f /artifacts/org.ldif
ldapadd -x -w "$LDAP_ADMIN_PASS" -H ldapi:/// -D "cn=admin,dc=sdk,dc=local" -f /artifacts/data.ldif
ldapadd -x -w "$LDAP_ADMIN_PASS" -H ldapi:/// -D "cn=admin,dc=sdk,dc=local" -f /artifacts/users.ldif
ldapadd -x -w "$LDAP_ADMIN_PASS" -H ldapi:/// -D "cn=admin,dc=sdk,dc=local" -f /artifacts/sales.ldif
ldapadd -x -w "$LDAP_ADMIN_PASS" -H ldapi:/// -D "cn=admin,dc=sdk,dc=local" -f /artifacts/deployment.ldif
ldapadd -x -w "$LDAP_ADMIN_PASS" -H ldapi:/// -D "cn=admin,dc=sdk,dc=local" -f /artifacts/developer.ldif

if [ -f "$SLAPD_PIDFILE" ]; then
  while kill -9 `cat $SLAPD_PIDFILE`; do
    sleep 1
  done
fi

# create dumps for configuration and data
slapcat -n 0 -l /artifacts/config_dump.ldif
slapcat -a '(entryDN:dnSubtreeMatch:=dc=sdk,dc=local)' -n 1 -l /artifacts/database_dump.ldif
EOF

# Create entrypoint script
RUN cat <<EOF > /artifacts/run.sh
#!/bin/bash

. /etc/default/slapd

mkdir -p /etc/ldap/certs
case \$CERTS in
  "valid" | "invalid" | "expired")
    cp /artifacts/\${CERTS}/ca.pem /etc/ldap/certs/
    cp /artifacts/\${CERTS}/ldapserver.key /etc/ldap/certs/
    cp /artifacts/\${CERTS}/ldapserver.pem /etc/ldap/certs/
    ;;
  *)
    echo -n "not supported"
    exit 1
    ;;
esac

chown -R openldap:openldap /etc/ldap/certs

slapd -F /etc/ldap/slapd.d/ -h "ldap://0.0.0.0:1389 ldaps://0.0.0.0:1636" -u openldap -g openldap -d 1
EOF

FROM debian:stable-slim
LABEL maintainer="Nikola Radovanovic <<EMAIL>>"

# CERTS can be one of: valid, invalid, expired
ENV CERTS="valid" 
ENV TERM="xterm"
ENV DEBIAN_FRONTEND="noninteractive"

COPY --from=builder /artifacts /artifacts

RUN <<EOF
# Install and configure LDAP server

# Install packages
apt-get update
apt-get install -y --no-install-recommends gnutls-bin slapd ldap-utils procps net-tools vim

# Cleanup package cache
rm -rf *.tar.gz
apt-get clean autoclean
apt-get autoremove --yes
rm -rf /var/lib/{apt,dpkg,cache,log}/

# Import configuration and data
. /etc/default/slapd
rm -rf /etc/ldap/slapd.d/*
slapadd -n 0 -F /etc/ldap/slapd.d -l /artifacts/config_dump.ldif
slapadd -n 1 -l /artifacts/database_dump.ldif


# Set permissions
chown -R openldap:openldap /etc/ldap/slapd.d/
chown -R openldap:openldap /artifacts
chown -R openldap:openldap /etc/ldap

# Set executable flag and copy entrypoint script
chmod +x /artifacts/run.sh
cp /artifacts/run.sh /usr/local/bin

EOF

EXPOSE 1389 1636

USER openldap

#VOLUME ["/artifacts"]

ENTRYPOINT ["/usr/local/bin/run.sh"]

# Usage:
#   docker build --build-arg DOMAIN=sdk.local -t ldap .
#   docker run --rm -it -p 389:1389 -p 636:1636 --hostname=expired.sdk.local -e CERTS=expired --name ldap ldap
#   docker run --rm -it -p 389:1389 -p 636:1636 --hostname=ldap.sdk.local -e CERTS=valid --name ldap ldap
