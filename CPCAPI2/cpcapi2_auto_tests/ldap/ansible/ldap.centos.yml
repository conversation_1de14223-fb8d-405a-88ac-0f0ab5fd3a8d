- name: Check if {{ server_cert }} exists
  stat:
    path: "/tmp/{{ server_cert }}"
  register: server_cert_result

- name: Check if {{ server_cert }} will expire soon
  community.crypto.x509_certificate_info:
    path: "/tmp/{{ server_cert }}"
    valid_at:
      one_month: "+4w"
  register: server_cert_info
  when:
    - server_cert_result.stat.exists

- name: Rebuild Docker image and register certificates
  block:
    - name: Build OpenLDAP image
      community.docker.docker_image_build:
        name: ldap
        rebuild: always
        nocache: true
        path: ..

    - name: Create OpenLDAP container
      community.docker.docker_container:
        name: ldap
        image: ldap
        state: stopped
        recreate: true

    - name: Register CA certificate
      # become: true
      ansible.builtin.shell:
        cmd: |
          docker cp ldap:/artifacts/valid/ca.pem /tmp/ca.pem
      # docker cp ldap:/artifacts/valid/ca.pem ca.crt
      # mv ca.crt /etc/pki/ca-trust/source/anchors/
      # update-ca-trust extract

    - name: Register OpenLDAP server certificates
      ansible.builtin.shell:
        cmd: |
          docker cp ldap:/artifacts/{{ item.key }}/ldapserver.pem /tmp/{{ item.value }}.pem
      loop: "{{ fqdns | dict2items }}"
  always:
    - name: Remove OpenLDAP container
      community.docker.docker_container:
        name: ldap
        state: absent
  when:
    - (docker_no_cache|bool) or (not server_cert_result.stat.exists) or (not server_cert_info.valid_at.one_month)
