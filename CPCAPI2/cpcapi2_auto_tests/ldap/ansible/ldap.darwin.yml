- name: Check if {{ server_cert }} exists
  stat:
    path: "/tmp/{{ server_cert }}"
  register: server_cert_result

- name: Check if {{ server_cert }} will expire soon
  community.crypto.x509_certificate_info:
    path: "/tmp/{{ server_cert }}"
    valid_at:
      one_month: "+4w"
  register: server_cert_info
  when:
    - server_cert_result.stat.exists

- name: Rebuild Docker image and register certificates
  block:
    - name: Build OpenLDAP image
      community.docker.docker_image_build:
        docker_host: "{{ docker_host }}"
        name: ldap
        rebuild: always
        nocache: true
        path: ..

    - name: Create OpenLDAP container
      community.docker.docker_container:
        docker_host: "{{ docker_host }}"
        name: ldap
        image: ldap
        state: stopped
        recreate: true

    - name: Turn GUI authorization check OFF
      become: true
      ansible.builtin.shell: security authorizationdb write com.apple.trust-settings.admin allow

    - name: (Re)create {{ keychain }}
      ansible.builtin.shell:
        cmd: |
          security delete-keychain {{ keychain }} || true
          security create-keychain -p 'secret' {{ keychain }}

    - name: Register CA certificate
      ansible.builtin.shell:
        cmd: |
          docker cp ldap:/artifacts/valid/ca.pem /tmp/ca.pem
          security add-trusted-cert -d -r trustRoot -k ~/Library/Keychains/{{ keychain }}-db /tmp/ca.pem

    - name: Register OpenLDAP server certificates
      ansible.builtin.shell:
        cmd: |
          docker cp ldap:/artifacts/{{ item.key }}/ldapserver.pem /tmp/{{ item.value }}.pem
          security add-trusted-cert -d -r trustAsRoot -k ~/Library/Keychains/{{ keychain }}-db /tmp/{{ item.value }}.pem
      loop: "{{ fqdns | dict2items }}"

  always:
    - name: Turn GUI authorization check back ON
      become: true
      ansible.builtin.shell: security authorizationdb remove com.apple.trust-settings.admin

    - name: Remove OpenLDAP container
      community.docker.docker_container:
        docker_host: "{{ docker_host }}"
        name: ldap
        state: absent
  vars:
    docker_host: "unix://{{ ansible_env.HOME }}/.colima/default/docker.sock"
    keychain: "SDK.keychain"
  when:
    - (docker_no_cache|bool) or (not server_cert_result.stat.exists) or (not server_cert_info.valid_at.one_month)
