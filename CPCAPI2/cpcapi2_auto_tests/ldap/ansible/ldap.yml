---
- hosts: centosbuild, macosbuild, localhost
  gather_facts: true
  vars:
    server_cert: "ldap.sdk.local.pem"
    docker_no_cache: false
    fqdns:
      valid: "ldap.sdk.local"
      expired: "expired.sdk.local"
      invalid: "invalid.sdk.local"

  tasks:
    - name: Import platform specific tasks
      ansible.builtin.import_tasks: ldap.darwin.yml
      vars:
        ansible_python_interpreter: "{{ ansible_env.HOME }}/sdkvenv/bin/python3"
      when: ansible_distribution == 'MacOSX'

    - name: Import platform specific tasks
      ansible.builtin.import_tasks: ldap.centos.yml
      vars:
        sdk_path: "{{ ansible_env.HOME }}/sdkvenv"
        ansible_python_interpreter: "{{ ansible_env.HOME }}/sdkvenv/bin/python3"
      environment:
        PATH: "{{ sdk_path }}/bin:{{ ansible_env.PATH }}"
      when: ansible_distribution == 'CentOS'