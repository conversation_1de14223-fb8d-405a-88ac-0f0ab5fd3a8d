---
- hosts: centosbuild, macosbuild, localhost
  gather_facts: true
  # become: false

  tasks:
    # - name: Install pip3 requirements
    #   ansible.builtin.pip:
    #     name:
    #       - requests==2.31.0
    #       - ansible-core==2.15.13
    #       - docker==7.1.0

    - name: Install necessary software
      ansible.builtin.import_tasks: prerequisites.darwin.yml
      when: ansible_distribution == 'MacOSX'

    # - name: Install necessary software
    #   become: true
    #   ansible.builtin.import_tasks: prerequisites.centos.yml
    #   vars:
    #     sdk_path: "{{ ansible_env.HOME }}/sdkvenv"
    #     docker_dir: "{{ ansible_env.HOME }}/.docker"
    #     docker_config: "{{ docker_dir }}/config.json"
    #   environment:
    #     PATH: "{{ sdk_path }}/bin:{{ ansible_env.PATH }}"
    #   when: ansible_distribution == 'CentOS'