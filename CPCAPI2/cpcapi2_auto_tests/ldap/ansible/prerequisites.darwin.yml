- name: Check if homebrew is available
  block:
    - name: Try using homebrew in check_mode (no changes)
      homebrew:
        update_homebrew: false
      check_mode: true
  rescue:
    - name: No homebrew found. Install it.
      shell: /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
      delay: 3

- name: Install Docker
  community.general.homebrew_cask:
    name: docker
    state: present

- name: Copy Docker service files
  become: true
  copy:
    src: "{{ item }}"
    dest: "/Library/PrivilegedHelperTools"
  loop:
    - /Applications/Docker.app/Contents/Library/LaunchServices/com.docker.vmnetd
    - /Applications/Docker.app/Contents/MacOS/com.docker.socket

- name: Install necessary packages
  community.general.homebrew:
    name: "{{ item }}"
    state: present
  loop:
    - colima
    - docker-buildx

- name: Load ~/.docker/config.json
  slurp:
    src: ~/.docker/config.json
  register: imported_json

- name: Add cliPluginsExtraDirs to ~/.docker/config.json
  set_fact:
    imported_json: "{{ imported_json.content | b64decode | from_json | combine(item, recursive=True)}}"
  loop:
    - { "cliPluginsExtraDirs": ["/opt/homebrew/lib/docker/cli-plugins"] }

- name: Write updated ~/.docker/config.json
  copy:
    content: "{{ imported_json | to_nice_json }}"
    dest: ~/.docker/config.json

- name: Start colima
  community.general.homebrew_services:
    name: colima
    state: restarted

- name: Add FQDNs to /etc/hosts
  become: true
  blockinfile:
    state: present
    dest: /etc/hosts
    content: |
      {{ ansible_facts['default_ipv4']['address'] }}  ldap.sdk.local expired.sdk.local invalid.sdk.local
