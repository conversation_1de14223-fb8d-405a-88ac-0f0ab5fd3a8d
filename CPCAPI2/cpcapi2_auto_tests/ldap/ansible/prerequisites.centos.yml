- name: Add docker-ce repository
  ansible.builtin.yum_repository:
    name: docker-ce
    description: docker-ce repo
    baseurl: https://download.docker.com/linux/centos/$releasever/$basearch/stable
    enabled: true
    gpgcheck: true
    gpgkey: https://download.docker.com/linux/centos/gpg

- name: Install packages
  ansible.builtin.yum:
    name: "{{ item }}"
    state: present
  loop:
    - gcc
    # - openssl-devel
    - bzip2-devel
    - libffi-devel
    - perl
    - zlib-devel
    # - yum-utils
    - device-mapper-persistent-data
    - lvm2
    - docker-ce

- name: Check for python3.10 in {{ sdk_path }}/bin
  stat:
    path: "{{ sdk_path }}/bin/python3.10"
  register: python3_result

- name: Build Python3.10
  block:
    - name: Download and unpack openssl-{{ ssl_ver }}
      ansible.builtin.unarchive:
        src: "https://github.com/openssl/openssl/releases/download/OpenSSL_1_1_1w/openssl-{{ ssl_ver }}.tar.gz"
        dest: /tmp
        remote_src: yes

    - name: Configure openssl-{{ ssl_ver }}
      ansible.builtin.shell:
        cmd: ./config --prefix={{ sdk_path }} --openssldir={{ sdk_path }}/ssl
        chdir: "{{ ssl_dir }}"

    - name: Build openssl-{{ ssl_ver }}
      community.general.make:
        chdir: "{{ ssl_dir }}"

    - name: Install openssl-{{ ssl_ver }}
      community.general.make:
        chdir: "{{ ssl_dir }}"
        target: install_sw

    - name: Download and unpack Python-{{ python_ver }}
      ansible.builtin.unarchive:
        src: "https://www.python.org/ftp/python/{{ python_ver }}/Python-{{ python_ver }}.tgz"
        dest: /tmp
        remote_src: yes

    - name: Configure Python-{{ python_ver }}
      ansible.builtin.shell:
        cmd: LDFLAGS="${LDFLAGS} -Wl,-rpath={{ sdk_path }}/lib" ./configure --prefix="{{ sdk_path }}" --enable-optimizations --with-openssl="{{ sdk_path }}"
        chdir: "{{ python_dir }}"

    - name: Build Python-{{ python_ver }}
      community.general.make:
        chdir: "{{ python_dir }}"

    - name: Install Python-{{ python_ver }}
      community.general.make:
        chdir: "{{ python_dir }}"
        target: install
  vars:
    ssl_ver: 1.1.1w
    ssl_dir: "/tmp/openssl-{{ ssl_ver }}"
    python_ver: 3.10.9
    python_dir: "/tmp/Python-{{ python_ver }}"
  when:
    - not python3_result.stat.exists

- name: Enable Docker service
  ansible.builtin.systemd_service:
    name: docker
    state: started
    enabled: true

- name: Adding user to group docker
  user:
    name: "{{ ansible_user }}"
    # name: "{{ lookup('pipe', 'whoami') }}"
    groups: docker
    append: yes

- name: Create {{ docker_dir }}/cli-plugins directory if it does not exist
  ansible.builtin.file:
    path: "{{ docker_dir }}/cli-plugins"
    state: directory
    owner: "{{ ansible_user }}"
    group: "{{ ansible_user }}"
    mode: '0755'

- name: Download docker-buildx
  ansible.builtin.get_url:
    url: https://github.com/docker/buildx/releases/download/v0.23.0/buildx-v0.23.0.linux-amd64
    dest: "{{ docker_dir }}/cli-plugins/docker-buildx"
    owner: "{{ ansible_user }}"
    group: "{{ ansible_user }}"
    mode: '0755'

- name: Check if {{ docker_config }} exists
  stat:
    path: "{{ docker_config }}"
  register: docker_config_result

- name: Configure {{ docker_config }}
  copy:
    dest: "{{ docker_config }}"
    content: |
      {
        "cliPluginsExtraDirs": [
          "~/docker/cli-plugins"
        ]
      }
  when:
    - not docker_config_result.stat.exists

- name: Configure {{ docker_config }}
  block:
    - name: Load {{ docker_config }}
      slurp:
        src: "{{ docker_config }}"
      register: imported_json

    - name: Add cliPluginsExtraDirs to {{ docker_config }}
      set_fact:
        imported_json: "{{ imported_json.content | b64decode | from_json | combine(item, recursive=True)}}"
      loop:
        - { "cliPluginsExtraDirs": [ "~/.docker/cli-plugins" ] }

    - name: Write updated {{ docker_config }}
      copy:
        content: "{{ imported_json | to_nice_json }}"
        dest: "{{ docker_config }}"
  when:
    - docker_config_result.stat.exists

- name: Add FQDNs to /etc/hosts
  blockinfile:
    state: present
    dest: /etc/hosts
    content: |
      {{ ansible_facts['default_ipv4']['address'] }}  ldap.sdk.local expired.sdk.local invalid.sdk.local
