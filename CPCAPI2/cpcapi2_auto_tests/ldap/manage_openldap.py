from abc import ABC, abstractmethod
from argparse import ArgumentParser
from collections import namedtuple
import sys
from os import environ, pathsep
from os.path import dirname, exists, isdir, realpath, join
from subprocess import call
from typing import List
from pathlib import Path


Playbook = namedtuple("Playbook", ["path", "args"])


class Application(ABC):
    def __init__(self, venv_manager) -> None:
        self._venv_manager = venv_manager

    @abstractmethod
    def run(self, playbooks: List[str] = None):
        raise NotImplemented


class VenvManager(object):

    def __init__(self, target_dir: str = None) -> None:
        self._venv_dir = self._venv_dir_path(target_dir)
        self._venv_python = self._python_exec_path()

    @property
    def venv_dir(self) -> Path:
        return self._venv_dir

    @property
    def is_venv(self) -> bool:
        return Path(sys.prefix).resolve(strict=False) == self.venv_dir

    @property
    def venv_python(self) -> str:
        return self._venv_python

    @staticmethod
    def _venv_dir_path(venv_dir: str) -> Path:
        if venv_dir and isdir(venv_dir):
            dir_path = join(dirname(realpath(venv_dir)), venv_dir)
        else:
            dir_path = join(dirname(realpath(sys.argv[0])), venv_dir or "venv")

        return Path(dir_path).resolve(strict=False)

    def _python_exec_path(self) -> str:
        if sys.platform.startswith("win"):
            return join(self.venv_dir, "Scripts", "python.exe")
        else:
            return join(self.venv_dir, "bin", "python3")

    def install(self):
        if not exists(self.venv_python):
            call([sys.executable, "-m", "venv", self.venv_dir])
        # else:
        #     print("found virtual python: " + self.venv_python)

    def install_requirements(self, pip_requirements: List[str] = None, ansible_requirements: List[str] = None):
        if not self.is_venv:
            raise RuntimeError("Installing requirements is only allowed in virtual environment")

        if pip_requirements:
            call([sys.executable, "-m", "pip", "install"] + pip_requirements)

        if ansible_requirements:
            call(["ansible-galaxy", "collection", "install"] + ansible_requirements)

    def restart_under_venv(self):
        env = environ.copy()
        env["PATH"] = dirname(self.venv_python) + pathsep + env["PATH"]
        env["ANSIBLE_CONFIG"] = join(dirname(realpath(sys.argv[0])), "ansible", "ansible.cfg")
        call([self.venv_python, __file__] + sys.argv[1:], env=env)
        exit(0)


class OpenLdapApplication(Application):

    def run(self, playbooks: List[Playbook] = None):
        for playbook in playbooks or []:
            call(["ansible-playbook", "--connection=local", "--inventory", "localhost,", playbook.path] + playbook.args)


if __name__ == "__main__":
    parser = ArgumentParser(description="Manage OpenLDAP server in Docker. Install all dependencies via Ansible.")
    parser.add_argument("--venv", default=Path(join(Path.home(), "sdkvenv")).resolve(strict=False), help="Path to folder containing virtual environment")
    parser.add_argument("--force-rebuild", action="store_true", help="Rebuild OpenLDAP docker image")

    args = parser.parse_args()

    venv_manager = VenvManager(str(args.venv))
    if not venv_manager.is_venv:
        print(f'Restart under venv. Current: {sys.prefix}')
        venv_manager.install()
        venv_manager.restart_under_venv()
    else:
        pip_requirements = [
            "requests==2.31.0",
            "ansible-core==2.15.13",
            # "ansible-runner==2.4.1",
            "docker==7.1.0",
        ]

        ansible_requirements = [
            "community.general==10.6.0",
            "community.docker==4.6.0",
            "community.crypto==2.26.1",
        ]

        playbooks = [
            Playbook(path=f"ansible/ldap.yml", args=["--extra-vars", f"docker_no_cache={args.force_rebuild}"]),
        ]

        venv_manager.install_requirements(pip_requirements=pip_requirements, ansible_requirements=ansible_requirements)
        application = OpenLdapApplication(venv_manager=venv_manager)
        application.run(playbooks=playbooks)
