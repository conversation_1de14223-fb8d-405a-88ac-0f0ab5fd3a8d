#include "cpcapi2_test_fixture.h"
#include "test_account_events.h"
#include "test_events.h"
#include "test_call_events.h"
#include "sipua_alianza_api_test_fixture.h"

#include "test_framework/http_test_framework.h"

#include "impl/account/SipAccountManagerInternal.h"
#include "impl/account/SipAccountHandlerInternal.h"
#include "impl/account/SipAccountAwareFeature.h"

#include <sstream>

#include "util/CurlPPHelper.h"
#include "util/CurlPPProgress.h"
#include "util/CurlPPSSL.h"
#include "util/CurlURI.h"

// rapidjson
#include <writer.h>
#include <stringbuffer.h>
#include <document.h>


#include "alianza_api/interface/public/alianza_api_handler.h"
#include "alianza_api/interface/public/alianza_api_types.h"
#include "alianza_api/interface/public/alianza_api_manager.h"

#include <sstream>

#include "util/CurlPPHelper.h"
#include "util/CurlPPProgress.h"
#include "util/CurlPPSSL.h"
#include "util/CurlURI.h"

// rapidjson
#include <writer.h>
#include <stringbuffer.h>
#include <document.h>


using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::SipConversation;
using namespace std::chrono;
using namespace curlpp::options;


namespace
{
   class SipuaCallTransferModuleTest : public CpcapiAutoTest
   {
   public:
      SipuaCallTransferModuleTest() {}
      virtual ~SipuaCallTransferModuleTest() {}

   };

   TEST_F(SipuaCallTransferModuleTest, BasicTransfer)
   {
      TestAccount alice("alice");
      TestAccount bob("bob");
      TestAccount max("max");
      std::string environmentId = TestEnvironmentConfig::testEnvironmentId().c_str();

      auto aliceEvents = std::async(std::launch::async, [&] ()
      {
         SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
         alice.conversation->addParticipant(aliceCall, bob.uri().c_str());
         alice.conversation->start(aliceCall);

         assertNewConversationOutgoing(alice, aliceCall, bob.uri().c_str());

         if (environmentId == "repro")
         {
            assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
            assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);

            SipConversationHandle aliceCallToMax = 0;
            assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
            assertTransferRequest_ex(alice, aliceCall, &aliceCallToMax, [&](const TransferRequestEvent& evt)
            {
               ASSERT_EQ(max.uri().c_str(), evt.transferTargetAddress);
               ASSERT_EQ("", evt.transferTargetDisplayName);
               ASSERT_NE(0, evt.transferTargetConversation);
            });

            assertSuccess(alice.conversation->acceptIncomingTransferRequest(aliceCallToMax));
            assertNewConversationOutgoing(alice, aliceCallToMax, max.uri().c_str());
            assertConversationStateChanged(alice, aliceCallToMax, ConversationState_RemoteRinging);
            assertConversationMediaChanged(alice, aliceCallToMax, MediaDirection_SendReceive);
            assertConversationStateChanged(alice, aliceCallToMax, ConversationState_Connected);

            assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
            assertConversationEnded(alice, aliceCallToMax, ConversationEndReason_UserTerminatedRemotely);
         }
         else
         {
            assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
            assertConversationStateChangedUptilConnected(alice, aliceCall); // Seen Ringing, Early, Connected at this stage
            assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
            assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
         }
      });

      auto bobEvents = std::async(std::launch::async, [&] ()
      {
         SipConversationHandle bobCall;
         assertNewConversationIncoming(bob, &bobCall, alice.uri().c_str());
         assertSuccess(bob.conversation->sendRingingResponse(bobCall));
         assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
         assertSuccess(bob.conversation->accept(bobCall));

         assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
         assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

         std::this_thread::sleep_for(std::chrono::milliseconds(1000));
         assertSuccess(bob.conversation->transfer(bobCall, max.uri().c_str()));

         if (environmentId == "repro")
         {
            assertTransferTryingRingingConnected(bob, bobCall);
         }
         else
         {
            assertTransferProgress(bob, bobCall, TransferProgressEventType_Connected);
         }

         assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      });

      auto maxEvents = std::async(std::launch::async, [&] ()
      {
         SipConversationHandle maxCall;
         assertNewConversationIncoming(max, &maxCall, alice.uri().c_str());
         assertSuccess(max.conversation->sendRingingResponse(maxCall));
         assertConversationStateChanged(max, maxCall, ConversationState_LocalRinging);
         assertSuccess(max.conversation->accept(maxCall));

         assertConversationMediaChanged(max, maxCall, MediaDirection_SendReceive);
         assertConversationStateChanged(max, maxCall, ConversationState_Connected);

         std::this_thread::sleep_for(std::chrono::milliseconds(500));

         assertSuccess(max.conversation->end(maxCall));
         assertConversationEnded(max, maxCall, ConversationEndReason_UserTerminatedLocally);
      });

      waitFor3(aliceEvents, bobEvents, maxEvents);
   }

   TEST_F(SipuaCallTransferModuleTest, AttendedTransfer)
   {
      TestAccount alice("alice");
      TestAccount bob("bob");
      TestAccount max("max");
      std::string environmentId = TestEnvironmentConfig::testEnvironmentId().c_str();

      // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->addParticipant(aliceCall, bob.uri().c_str());
      alice.conversation->start(aliceCall);

      auto aliceEvents = std::async(std::launch::async, [&] ()
      {
         assertNewConversationOutgoing(alice, aliceCall, bob.uri().c_str());
         if (environmentId == "repro")
         {
            assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
            assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
            assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
            assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendOnly);
            assertSuccess(alice.conversation->accept(aliceCall));
            assertConversationMediaChanged(alice, aliceCall, MediaDirection_ReceiveOnly);

            SipConversationHandle aliceCallToMax;
            assertTransferRequest_ex(alice, aliceCall, &aliceCallToMax, [&](const TransferRequestEvent& evt)
            {
               ASSERT_EQ(max.uri().c_str(), evt.transferTargetAddress);
               ASSERT_EQ(max.config.name, evt.transferTargetDisplayName);
               ASSERT_NE(0, evt.transferTargetConversation);
            });
            assertSuccess(alice.conversation->acceptIncomingTransferRequest(aliceCallToMax));
            assertNewConversationOutgoing(alice, aliceCallToMax, max.uri().c_str());
            assertConversationStateChanged(alice, aliceCallToMax, ConversationState_RemoteRinging);
            assertConversationMediaChanged(alice, aliceCallToMax, MediaDirection_SendReceive);
            assertConversationStateChanged(alice, aliceCallToMax, ConversationState_Connected);
            assertConversationEnded(alice, aliceCallToMax, ConversationEndReason_UserTerminatedRemotely);
            assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
         }
         else
         {
            assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
            assertConversationStateChangedUptilConnected(alice, aliceCall); // Seen Ringing, Early, Connected at this stage
            assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
            assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
         }
      });

      auto bobEvents = std::async(std::launch::async, [&] ()
      {
         SipConversationHandle bobCall;
         assertNewConversationIncoming(bob, &bobCall, alice.uri().c_str());
         assertSuccess(bob.conversation->sendRingingResponse(bobCall));
         assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
         assertSuccess(bob.conversation->accept(bobCall));
         assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
         if (environmentId != "repro")
         {
            // assertConversationStateChanged(bob, bobCall, ConversationState_Early);
         }
         assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
         std::this_thread::sleep_for(std::chrono::milliseconds(1000));

         assertSuccess(bob.conversation->hold(bobCall));
         assertConversationMediaChanged(bob, bobCall, MediaDirection_SendOnly);

         SipConversationHandle bobCallToMax = bob.conversation->createConversation(bob.handle);
         bob.conversation->addParticipant(bobCallToMax, max.uri().c_str());
         bob.conversation->start(bobCallToMax);

         assertNewConversationOutgoing(bob, bobCallToMax, max.uri().c_str());
         assertConversationStateChanged(bob, bobCallToMax, ConversationState_RemoteRinging);
         assertConversationMediaChanged(bob, bobCallToMax, MediaDirection_SendReceive);
         if (environmentId != "repro")
         {
            assertConversationStateChanged(bob, bobCallToMax, ConversationState_Early);
         }
         assertConversationStateChanged(bob, bobCallToMax, ConversationState_Connected);
         std::this_thread::sleep_for(std::chrono::milliseconds(1000));

         assertSuccess(bob.conversation->transfer(bobCallToMax, bobCall));
         if (environmentId == "repro")
         {
            assertTransferTryingRingingConnected(bob, bobCall);
         }
         else
         {
            assertTransferProgress(bob, bobCall, TransferProgressEventType_Connected);
         }

         assertSuccess(bob.conversation->end(bobCall));
         assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
         assertConversationEnded(bob, bobCallToMax, ConversationEndReason_UserTerminatedRemotely);
      });

      auto maxEvents = std::async(std::launch::async, [&] ()
      {
         SipConversationHandle maxCallFromBob;
         assertNewConversationIncoming(max, &maxCallFromBob, bob.uri().c_str());
         assertSuccess(max.conversation->sendRingingResponse(maxCallFromBob));
         assertConversationStateChanged(max, maxCallFromBob, ConversationState_LocalRinging);
         assertSuccess(max.conversation->accept(maxCallFromBob));
         assertConversationMediaChanged(max, maxCallFromBob, MediaDirection_SendReceive);
         assertConversationStateChanged(max, maxCallFromBob, ConversationState_Connected);

         if (environmentId == "repro")
         {
            SipConversationHandle maxCallFromAlice;
            assertNewConversationIncomingTransfer_ex(max, &maxCallFromAlice, alice.uri().c_str(), [&](const NewConversationEvent& evt)
            {
               ASSERT_EQ(alice.config.name, evt.remoteDisplayName);
               ASSERT_EQ(maxCallFromBob, evt.conversationToReplace);
            });

            assertSuccess(max.conversation->sendRingingResponse(maxCallFromAlice));
            assertConversationStateChanged(max, maxCallFromAlice, ConversationState_LocalRinging);
            assertSuccess(max.conversation->accept(maxCallFromAlice));
            assertConversationMediaChanged(max, maxCallFromAlice, MediaDirection_SendReceive);
            assertConversationStateChanged(max, maxCallFromAlice, ConversationState_Connected);
            std::this_thread::sleep_for(std::chrono::milliseconds(1000));

            assertSuccess(max.conversation->end(maxCallFromAlice));
            assertConversationEnded(max, maxCallFromAlice, ConversationEndReason_UserTerminatedLocally);
            assertConversationEnded(max, maxCallFromBob, ConversationEndReason_UserTerminatedLocally);
         }
         else
         {
            assertConversationMediaChangeRequest(max, maxCallFromBob, MediaDirection_SendReceive);
            assertSuccess(max.conversation->accept(maxCallFromBob));
            assertConversationMediaChanged(max, maxCallFromBob, MediaDirection_SendReceive);
            assertConversationStateChanged(max, maxCallFromBob, ConversationState_Connected);
            std::this_thread::sleep_for(std::chrono::milliseconds(1000));

            assertSuccess(max.conversation->end(maxCallFromBob));
            assertConversationEnded(max, maxCallFromBob, ConversationEndReason_UserTerminatedLocally);
         }
      });

      waitFor3(aliceEvents, bobEvents, maxEvents);
   }

}

