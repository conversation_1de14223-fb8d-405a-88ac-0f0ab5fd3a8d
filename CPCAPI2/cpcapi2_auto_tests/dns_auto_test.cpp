#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#include "cpcapi2_test_fixture.h"
#include "test_account_events.h"

#include <gtest/gtest.h>
#include <cpcapi2.h>

#include <rutil/dns/DnsStub.hxx>
#include <rutil/dns/QueryTypes.hxx>
#include <rutil/dns/DnsSrvRecord.hxx>

#include "util/DnsClient.h"

using namespace CPCAPI2;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::test;

namespace {

   class DnsTestClient : public resip::DnsResultSink
   {
   public:
      DnsTestClient(const resip::DnsStub::DnsSettings& dnsSettings)
      {
         mDns = new resip::DnsStub(dnsSettings);
      }
      ~DnsTestClient() { delete mDns; }

      void getDnsSrvRecord(const cpc::string& domain)
      {
         mOnDnsResultSrvCalled = false;
         mDns->lookup<resip::RR_SRV>(domain.c_str(), resip::Protocol::Sip, this);
         resip::FdSet dnsFdSet;
         UInt64 lastTime = resip::Timer::getTimeMs();
         while ((resip::Timer::getTimeMs() - lastTime) < 5 * 1000)
         {
            mDns->buildFdSet(dnsFdSet);
            dnsFdSet.selectMilliSeconds(100);
            mDns->process(dnsFdSet);
            if (mOnDnsResultSrvCalled)
            {
               break;
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
         }
      }
      bool isDnsResultSrvCalled() { return mOnDnsResultSrvCalled; }
      size_t dnsSrvRecordsSize() { return mDnsSrvRecords.size(); }

   private:
      resip::DnsStub* mDns;
      std::vector<resip::DnsSrvRecord> mDnsSrvRecords;
      bool mOnDnsResultSrvCalled;

      // DnsResultSink
      virtual void onDnsResult(const resip::DNSResult<resip::DnsHostRecord>&) {}
      virtual void onDnsResult(const resip::DNSResult<resip::DnsSrvRecord>& result)
      {
         mDnsSrvRecords.clear();
         if (result.status == 0)
         {
            mDnsSrvRecords = result.records;
         }
         mOnDnsResultSrvCalled = true;
      }
      virtual void onDnsResult(const resip::DNSResult<resip::DnsAAAARecord>&) {}
      virtual void onDnsResult(const resip::DNSResult<resip::DnsNaptrRecord>&) {}
      virtual void onDnsResult(const resip::DNSResult<resip::DnsCnameRecord>&) {}
   };

   class DnsTest : public CpcapiAutoTest
   {
   public:
      DnsTest() {}
      virtual ~DnsTest() {}
   };

   TEST_F(DnsTest, DnsRfc2782Test)
   {
#if _WIN32
      if (TestEnvironmentConfig::dockerContainerized()) GTEST_SKIP(); // UNIT_TEST_WIN_DOCKER_SKIP
#endif

      //wait for unbound
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      resip::DnsStub::DnsSettings settings;
      resip::Tuple t("127.0.0.1", 53, resip::UDP);
      settings.nameServers.push_back(resip::GenericIPAddress(t.toGenericIPAddress()));
      settings.includeSystemDnsServers = false;

      DnsTestClient dns(settings);

      dns.getDnsSrvRecord("_stun._udp.rfc2782.local");
      ASSERT_TRUE(dns.isDnsResultSrvCalled());
      ASSERT_TRUE(dns.dnsSrvRecordsSize() > 0);

      dns.getDnsSrvRecord("_sip._udp.rfc2782.local");
      ASSERT_TRUE(dns.isDnsResultSrvCalled());
      ASSERT_TRUE(dns.dnsSrvRecordsSize() == 0);

      dns.getDnsSrvRecord("_sip._tcp.rfc2782.local");
      ASSERT_TRUE(dns.isDnsResultSrvCalled());
      ASSERT_TRUE(dns.dnsSrvRecordsSize() == 0);

      dns.getDnsSrvRecord("_sips._tcp.rfc2782.local");
      ASSERT_TRUE(dns.isDnsResultSrvCalled());
      ASSERT_TRUE(dns.dnsSrvRecordsSize() == 0);
   }

// For packet sniffing, check if SDK sends out <root> A queries
   TEST_F(DnsTest, DISABLED_AccountRfc2782Registration)
   {
/* Wireshark results (no <root> A query):
//   4279	234.707320	127.0.0.1	127.0.0.1	DNS	73	Standard query 0x0004 SRV _sip._udp.rfc2782.local
//   4280	234.707508	127.0.0.1	127.0.0.1	DNS	92	Standard query response 0x0004 SRV _sip._udp.rfc2782.local SRV 0 0 0 <Root>
//   4289	234.708095	127.0.0.1	127.0.0.1	DNS	63	Standard query 0x0005 A rfc2782.local
//   4290	234.708203	127.0.0.1	127.0.0.1	DNS	63	Standard query response 0x0005 A rfc2782.local
//   6371	239.709887	127.0.0.1	127.0.0.1	DNS	73	Standard query 0x0006 SRV _sip._udp.rfc2782.local
//   6372	239.710053	127.0.0.1	127.0.0.1	DNS	92	Standard query response 0x0006 SRV _sip._udp.rfc2782.local SRV 0 0 0 <Root>
//   6381	239.710427	127.0.0.1	127.0.0.1	DNS	63	Standard query 0x0007 A rfc2782.local
//   6382	239.710538	127.0.0.1	127.0.0.1	DNS	63	Standard query response 0x0007 A rfc2782.local
*/
      TestAccount alice("alice", Account_NoInit, false);
      alice.config.settings.domain = "127.0.0.1";
      alice.config.settings.outboundProxy = "rfc2782.local";
      alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
      
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

      alice.init();

      ASSERT_EQ(alice.account->enable(alice.handle), kSuccess);
      assertAccountRegistering(alice);

      ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(503, evt.signalingStatusCode);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
   }

   TEST_F(DnsTest, AresServerShift) //OBELISK-5253
   {
      TestAccount alice("alice", Account_NoInit);
      alice.config.settings.domain = "127.0.0.1";
      alice.config.settings.outboundProxy = "cpcapi2.local";
      alice.config.settings.ipVersion = IpVersion_Auto;
      alice.config.settings.nameServers.push_back(TestEnvironmentConfig::unreachableV4Ip());

      alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
      alice.init();
      auto start = std::chrono::high_resolution_clock::now();
      alice.enable();
      auto end = std::chrono::high_resolution_clock::now();
      std::chrono::duration<double> elapsed_seconds = end - start;

      safeCout("Registered in " << elapsed_seconds.count() << "s");
      // since only the SRV lookup targets the unresponsive server
      // (A record lookup should start with the first responsive server)
      // we should register under 2 * dns timeout:
      ASSERT_LT(elapsed_seconds.count(), (1.9 * CPCAPI2_DNS_TIMEOUT));
   }
   
   TEST_F(DnsTest, AresServerShift2) //OBELISK-5253
   {
      TestAccount alice("alice", Account_NoInit);
      alice.config.settings.domain = "127.0.0.1";
      alice.config.settings.outboundProxy = "cpcapi2.local";
      alice.config.settings.ipVersion = IpVersion_Auto;
      alice.config.settings.nameServers.push_back(TestEnvironmentConfig::unreachableV6Ip());
      alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
      alice.init();
      auto start = std::chrono::high_resolution_clock::now();
      alice.enable();
      auto end = std::chrono::high_resolution_clock::now();
      std::chrono::duration<double> elapsed_seconds = end - start;

      safeCout("Registered in " << elapsed_seconds.count() << "s");
      // since only the SRV lookup targets the unresponsive server
      // (A record lookup should start with the first responsive server)
      // we should register under 2 * dns timeout:
      ASSERT_LT(elapsed_seconds.count(), (1.9 * CPCAPI2_DNS_TIMEOUT));
   }
   
   // OBELISK-5933
   TEST_F(DnsTest, DISABLED_AresServerShift_NoAAAAResponse)
   {
      TestAccount alice("alice", Account_NoInit);
      alice.config.settings.domain = "127.0.0.1";
      alice.config.settings.outboundProxy = "dualstacknoaaaaresponse.local";
      alice.config.settings.ipVersion = IpVersion_Auto;

      alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
      
      // on mac you need to ensure you run sudo ifconfig lo0 alias ********* up
      alice.config.settings.nameServers.push_back("*********");
      alice.init();
      auto start = std::chrono::high_resolution_clock::now();
      alice.enable();
      auto end = std::chrono::high_resolution_clock::now();
      std::chrono::duration<double> elapsed_seconds = end - start;

      safeCout("Registered in " << elapsed_seconds.count() << "s");
      // since only the SRV lookup targets the unresponsive server
      // (A record lookup should start with the first responsive server)
      // we should register under 2 * dns timeout:
      ASSERT_LT(elapsed_seconds.count(), (1.9 * CPCAPI2_DNS_TIMEOUT));
   }
}
