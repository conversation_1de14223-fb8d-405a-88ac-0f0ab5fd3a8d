#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#include "cpcapi2_test_fixture.h"
#include "test_call_events.h"
#include "test_events.h"
#include "test_account_events.h"
#include "test_framework/logcat_monitor.h"
#include "impl/call/SipConversationHandlerInternal.h"
#include "impl/call/SipAVConversationManagerInterface.h"
#include "../shared/webrtc_recon/MediaStackImpl.hxx"
#include "../shared/webrtc_recon/CodecFactoryImpl.hxx"
#include "../shared/webrtc_recon/codecs/OpusCodecImpl.hxx"
#include "impl/media/MediaManagerInterface.h"
#include <fstream>
#include "repro/Proxy.hxx"
#include "test_framework/sipp_runner.h"
#include "test_framework/network_utils.h"
#include "test_framework/visqol_runner.h"

#include "webrtc/modules/audio_device/dummy/audio_device_dummy.h"

#include "util/SipHelpers.h"

#ifdef _WIN32
#include "detours/detours.h"
#include "Winsock2.h"
#endif


using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::SipConversation;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::Media;

class BasicCallTests : public CpcapiAutoTest
{
public:
   BasicCallTests() {}
   virtual ~BasicCallTests() {}
};


TEST_F(BasicCallTests, BasicCancelCallToInvalid) {
   TestAccount alice("alice");

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, "sip:<EMAIL>");
   alice.conversation->start(aliceCall);

   assertSuccess(alice.conversation->end(aliceCall));
   assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
}


TEST_F(BasicCallTests, ExtraAccept) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->accept(bobCall));

      // without this second accept, this is a normal call flow
      // the extra accept should be a noop
      assertSuccess(bob.conversation->accept(bobCall));

      //Note: "not my event" may appear if the mediaChange event happens before the onError event, this is OK
      assertConversationError(bob, bobCall);

      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor2(aliceEvents, bobEvents);
}

TEST_F(BasicCallTests, BasicCallDisableDuringCall)
{
   TestAccount bob("bob");
   TestAccount charlie("charlie");

   // Bob calls Charlie then Charlie hangs up
   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall, charlie.config.uri());
   bob.conversation->start(bobCall);
   auto bobConversationEvents = std::async(std::launch::async, [&] () {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing(bob, bobCall, charlie.config.uri());
      assertConversationStateChanged(bob, bobCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   auto charlieConversationEvents = std::async(std::launch::async, [&] () {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle charlieCall;
      assertNewConversationIncoming(charlie, &charlieCall, bob.config.uri());
      charlie.conversation->sendRingingResponse(charlieCall);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_LocalRinging);
      charlie.conversation->accept(charlieCall);
      assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_Connected);
      charlie.account->disable(charlie.handle);
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      charlie.conversation->end(charlieCall);
      assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedLocally);
   });
   waitFor2(bobConversationEvents, charlieConversationEvents);
}


TEST_F(BasicCallTests, DISABLED_CallDuringEnableDisable)
{
   //OBELISK-5696: assure 480 is not sent out during account disable-enable (4-5 mins to receive a 408):
   TestAccount bob("bob");
   TestAccount charlie("charlie", Account_Init);

   charlie.enable(false);
   charlie.disable(false, false);

   std::this_thread::sleep_for(std::chrono::seconds(1));

   // Bob calls Charlie then Charlie hangs up
   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall, charlie.config.uri());
   bob.conversation->start(bobCall);
   auto bobConversationEvents = std::async(std::launch::async, [&] () {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing(bob, bobCall, charlie.config.uri());
      //Wait for the request to time out (call should end with a 408)
      SipConversationHandle h;
      ConversationEndedEvent evt;
      ASSERT_TRUE(bob.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onConversationEnded",
         280000, HandleEqualsPred<SipConversationHandle>(bobCall), h, evt));
      ASSERT_EQ(ConversationEndReason_ServerRejected, evt.endReason);
      {
         // causes push call to be missed in OBELISK-5696
         ASSERT_NE(evt.sipResponseCode, 480);
         safeCout("Call ended with: " << evt.sipResponseCode);
      }
   });
   
   auto charlieConversationEvents = std::async(std::launch::async, [&] ()
   {
      // TODO: what account events if any should we expect?
   });
   waitFor2Ms(bobConversationEvents, charlieConversationEvents, std::chrono::milliseconds(300000));
}

TEST_F(BasicCallTests, CallDuringEnableDisable2)
{
   //OBELISK-5696: assure call is not ended/rejected during account disable-enable:
   TestAccount bob("bob");
   TestAccount charlie("charlie", Account_Init);

   charlie.enable(false);
   charlie.disable(false, false);

   std::this_thread::sleep_for(std::chrono::seconds(1));

   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall, charlie.config.uri());
   bob.conversation->start(bobCall);
   auto bobConversationEvents = std::async(std::launch::async, [&]() {
      assertNewConversationOutgoing(bob, bobCall, charlie.config.uri());
      SipConversationHandle h;
      ConversationEndedEvent evt;
      bool endEventReceived = bob.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onConversationEnded",
         10000, HandleEqualsPred<SipConversationHandle>(bobCall), h, evt);
      if (endEventReceived)
      {
         // due to timing, sometimes charlie will already have un-registered;
         // don't fail the test case when this happens.
         ASSERT_EQ(404, evt.sipResponseCode);
      }
   });

   auto charlieConversationEvents = std::async(std::launch::async, [&]()
   {
      // TODO: what account events if any should we expect?
   });

   waitFor2(bobConversationEvents, charlieConversationEvents);
}

TEST_F(BasicCallTests, BasicCallDisableEnableDuringCallSequence)
{
   TestAccount bob("bob");
   TestAccount charlie("charlie");

   // Bob calls Charlie then Charlie hangs up
   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall, charlie.config.uri());
   bob.conversation->start(bobCall);
   auto bobConversationEvents = std::async(std::launch::async, [&] () {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing(bob, bobCall, charlie.config.uri());
      assertConversationStateChanged(bob, bobCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   auto charlieConversationEvents = std::async(std::launch::async, [&] () {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle charlieCall;
      assertNewConversationIncoming(charlie, &charlieCall, bob.config.uri());
      charlie.conversation->sendRingingResponse(charlieCall);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_LocalRinging);
      charlie.conversation->accept(charlieCall);
      assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_Connected);
      charlie.account->disable(charlie.handle);
      charlie.account->enable(charlie.handle);
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      charlie.conversation->end(charlieCall);
      assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedLocally);
   });
   waitFor2(bobConversationEvents, charlieConversationEvents);

   // Bob calls Charlie then Charlie hangs up
   SipConversationHandle bobCall2 = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall2, charlie.config.uri());
   bob.conversation->start(bobCall2);
   auto bobConversation2Events = std::async(std::launch::async, [&] () {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing(bob, bobCall2, charlie.config.uri());
      assertConversationStateChanged(bob, bobCall2, ConversationState_RemoteRinging);
      assertConversationMediaChanged(bob, bobCall2, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall2, ConversationState_Connected);
      assertConversationEnded(bob, bobCall2, ConversationEndReason_UserTerminatedRemotely);
   });

   auto charlieConversation2Events = std::async(std::launch::async, [&] () {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle charlieCall;
      assertNewConversationIncoming(charlie, &charlieCall, bob.config.uri());
      charlie.conversation->sendRingingResponse(charlieCall);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_LocalRinging);
      charlie.conversation->accept(charlieCall);
      assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_Connected);
      charlie.conversation->end(charlieCall);
      assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedLocally);
   });
   waitFor2(bobConversation2Events, charlieConversation2Events);
}

TEST_F(BasicCallTests, BasicCallCalleeHangsUp)
{
   TestAccount bob("bob");
   TestAccount charlie("charlie");

   // Bob calls Charlie then Charlie hangs up
   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall, charlie.config.uri());
   bob.conversation->start(bobCall);
   auto bobConversationEvents = std::async(std::launch::async, [&] () {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing(bob, bobCall, charlie.config.uri());
      assertConversationStateChanged(bob, bobCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });
   auto charlieConversationEvents = std::async(std::launch::async, [&] () {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle charlieCall;
      assertNewConversationIncoming(charlie, &charlieCall, bob.config.uri());
      charlie.conversation->sendRingingResponse(charlieCall);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_LocalRinging);
      charlie.conversation->accept(charlieCall);
      assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_Connected);
      charlie.conversation->end(charlieCall);
      assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedLocally);
   });
   waitFor2(bobConversationEvents, charlieConversationEvents);
}

TEST_F(BasicCallTests, BasicCallEarlyInfo)
{
   TestAccount bob("bob");
   TestAccount charlie("charlie");

   // Bob calls Charlie then Charlie hangs up
   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall, charlie.config.uri());
   bob.conversation->start(bobCall);
   auto bobConversationEvents = std::async(std::launch::async, [&] () {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing(bob, bobCall, charlie.config.uri());
      assertConversationStateChanged(bob, bobCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });
   auto charlieConversationEvents = std::async(std::launch::async, [&] () {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle charlieCall;
      assertNewConversationIncoming(charlie, &charlieCall, bob.config.uri());
      charlie.conversation->sendRingingResponse(charlieCall);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_LocalRinging);
      
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));

      // if sent before the 183, will be rejected since no dialog has been established yet
      dynamic_cast<SipConversationManagerExt*>(charlie.conversation)->sendInfo(charlieCall, "");
      
      charlie.conversation->accept(charlieCall);
      assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_Connected);
      charlie.conversation->end(charlieCall);
      assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedLocally);
   });
   waitFor2(bobConversationEvents, charlieConversationEvents);
}

TEST_F(BasicCallTests, BasicCallDouble180)
{
   TestAccount bob("bob");
   TestAccount charlie("charlie");

   // Bob calls Charlie then Charlie hangs up
   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall, charlie.config.uri());
   bob.conversation->start(bobCall);
   auto bobConversationEvents = std::async(std::launch::async, [&] () {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing(bob, bobCall, charlie.config.uri());
      assertConversationStateChanged(bob, bobCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });
   auto charlieConversationEvents = std::async(std::launch::async, [&] () {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle charlieCall;
      assertNewConversationIncoming(charlie, &charlieCall, bob.config.uri());
      // not expected applications will do this; we are doing it here because it simulates re-transmission of
      // 180 ringing from charlie to bob.
      charlie.conversation->sendRingingResponse(charlieCall);
      charlie.conversation->sendRingingResponse(charlieCall);
      // one for each sendRingingResponse
      assertConversationStateChanged(charlie, charlieCall, ConversationState_LocalRinging);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_LocalRinging);
      charlie.conversation->accept(charlieCall);
      assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_Connected);
      charlie.conversation->end(charlieCall);
      assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedLocally);
   });
   waitFor2(bobConversationEvents, charlieConversationEvents);
}

TEST_F(BasicCallTests, BasicTelCallDomainWithPort)
{
   TestAccount bob("bob", Account_NoInit);
   bob.config.settings.domain = "127.0.0.1:6060";
   bob.config.settings.outboundProxy = "";
   bob.init();
   bob.enable();
   
   TestAccount charlie("charlie", Account_NoInit);
   charlie.config.settings.domain = "127.0.0.1:6060";
   charlie.config.settings.outboundProxy = "";
   charlie.init();
   charlie.enable();

   // Bob calls Charlie then Charlie hangs up
   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
   const cpc::string telUri = "tel:" + charlie.config.settings.username + "@" + charlie.config.settings.domain;
   bob.conversation->addParticipant(bobCall, telUri);
   bob.conversation->start(bobCall);
   auto bobConversationEvents = std::async(std::launch::async, [&] () {
      // Wait for the expected state changes and for the call to end
      const cpc::string expectedURI = "sip:" + charlie.config.settings.username + "@127.0.0.1";

      assertNewConversationOutgoing(bob, bobCall, expectedURI); // manual transform
      assertConversationStateChanged(bob, bobCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });
   auto charlieConversationEvents = std::async(std::launch::async, [&] () {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle charlieCall;
      assertNewConversationIncoming(charlie, &charlieCall, bob.config.uri());
      charlie.conversation->sendRingingResponse(charlieCall);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_LocalRinging);
      charlie.conversation->accept(charlieCall);
      assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_Connected);
      charlie.conversation->end(charlieCall);
      assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedLocally);
   });
   waitFor2(bobConversationEvents, charlieConversationEvents);
}

TEST_F(BasicCallTests, DISABLED_BasicCallSessionTimer)
{
   TestAccount bob("bob");
   TestAccount charlie("charlie");

   // Bob calls Charlie then Charlie hangs up
   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall, charlie.config.uri());
   bob.conversation->start(bobCall);
   auto bobConversationEvents = std::async(std::launch::async, [&]() {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing(bob, bobCall, charlie.config.uri());
      assertConversationStateChanged(bob, bobCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(120000));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });
   auto charlieConversationEvents = std::async(std::launch::async, [&]() {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle charlieCall;
      assertNewConversationIncoming(charlie, &charlieCall, bob.config.uri());
      charlie.conversation->sendRingingResponse(charlieCall);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_LocalRinging);
      charlie.conversation->accept(charlieCall);
      assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_Connected);

      // Disable the network interface card after a delay of ~ 30s.
      std::string command = "netsh interface set interface \"Ethernet\" disabled";
      system(command.c_str());

      std::this_thread::sleep_for(std::chrono::milliseconds(120000));

      //assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedLocally);
      assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedRemotely);

      command = "netsh interface set interface \"Ethernet\" enabled";
      system(command.c_str());
   });

}

TEST_F(BasicCallTests, BasicCallCancelWithPendingDnsQueries)
{
   TestAccount bob("bob");
   TestAccount charlie("charlie", Account_NoInit);

   // intended to reproduce crash reported in OBELISK-3031:
   // the idea here is to conigure Charlie such that when he receives the CANCEL, he'll have an
   // active DNS query pending (DNS query to resolve bogus.invalid for a STUN server -- however
   // since Charlie has a bogus nameserver, the SDK should be waiting on a response that will never come).

   charlie.config.settings.nameServers.clear();
   charlie.config.settings.nameServers.push_back("*********"); // should not be routable -- RFC 5737
   charlie.config.settings.nameServers.push_back("*********");
   charlie.enable();

   SipConversationSettings settings;
   settings.natTraversalMode = NatTraversalMode_STUN;
   settings.natTraversalServerSource = NatTraversalServerSource_Custom;
   settings.natTraversalServer = "bogus.invalid";
   charlie.conversation->setDefaultSettings(charlie.handle, settings);


   // Bob calls Charlie then Charlie hangs up
   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall, charlie.config.uri());
   bob.conversation->start(bobCall);
   auto bobConversationEvents = std::async(std::launch::async, [&] () {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing(bob, bobCall, charlie.config.uri());
      assertConversationStateChanged(bob, bobCall, ConversationState_RemoteRinging);
      bob.conversation->end(bobCall);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });
   auto charlieConversationEvents = std::async(std::launch::async, [&] () {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle charlieCall;
      assertNewConversationIncoming(charlie, &charlieCall, bob.config.uri());
      charlie.conversation->sendRingingResponse(charlieCall);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_LocalRinging);
      assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedRemotely);
   });
   waitFor2(bobConversationEvents, charlieConversationEvents);
}

TEST_F(BasicCallTests, BasicCallRepeated) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   const int callCount = 60;
   for (int i=0; i < callCount; i++)
   {
      // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->addParticipant(aliceCall, bob.config.uri());
      alice.conversation->start(aliceCall);

      auto aliceEvents = std::async(std::launch::async, [&] () {
         assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
         assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
         assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
         assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
         assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      });

      auto bobEvents = std::async(std::launch::async, [&] () {
         SipConversationHandle bobCall;
         assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
         // Bob sends 180 ringing -- this should trigger state transitions for both Bob and Alice
         assertSuccess(bob.conversation->sendRingingResponse(bobCall));
         assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
         // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
         assertSuccess(bob.conversation->accept(bobCall));

         assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
         assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
         if (i < callCount)
         {
            std::this_thread::sleep_for(std::chrono::milliseconds(200));
         }
         else
         {
            safeCout("pausing a bit -- you should check to see if audio is working");
            std::this_thread::sleep_for(std::chrono::milliseconds(6000));
         }
         assertSuccess(bob.conversation->end(bobCall));
         assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      });

      waitFor2(aliceEvents, bobEvents);
   }
}

#if defined __APPLE__ || defined _WIN32
#include "memoryUsageHelper.h"

TEST_F(BasicCallTests, BasicCallRepeatedWithMemoryUsageCheck)
{
   double MAX_MEMORY_INCREASE_MEGABYTE = 0;
   if (TestEnvironmentConfig::pageHeap())
   {
      MAX_MEMORY_INCREASE_MEGABYTE = 65;
   }
   else
   {
      MAX_MEMORY_INCREASE_MEGABYTE = 4;
   }
   bool memoryCheckSucceeded;
   memoryUsageHelper::MemoryUsageInfo Info;  // contains memory usage after the first call
   double firstCallMemInMegabyte = 0;
   double lastCallMemInMegabyte = 0;
   TestAccount alice("alice");
   TestAccount bob("bob");

   for (int i=0; i<10; i++)
   {
      // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->addParticipant(aliceCall, bob.config.uri());
      alice.conversation->start(aliceCall);

      auto aliceEvents = std::async(std::launch::async, [&] ()
      {
         assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
         assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
         assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
         assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
         assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      });

      auto bobEvents = std::async(std::launch::async, [&] ()
      {
         SipConversationHandle bobCall;
         assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
         // Bob sends 180 ringing -- this should trigger state transitions for both Bob and Alice
         assertSuccess(bob.conversation->sendRingingResponse(bobCall));
         assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
         // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
         assertSuccess(bob.conversation->accept(bobCall));

         assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
         assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

         if (i < 9)
         {
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
         }
         else
         {
            safeCout("pausing a bit -- you should check to see if audio is working");
            std::this_thread::sleep_for(std::chrono::milliseconds(10000));
         }
         assertSuccess(bob.conversation->end(bobCall));
         assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
         if (i == 0)
         {
            if (memoryUsageHelper::getMemoryUsage(Info) == 0)
            {
               firstCallMemInMegabyte = Info.currentProcessMemoryUsage;
               memoryCheckSucceeded = true;
            }
            else
            {
               memoryCheckSucceeded = false;
            }
         }
      });

      waitFor2(aliceEvents, bobEvents);
   }
   memoryUsageHelper:: MemoryUsageInfo lastCallInfo;
   if (memoryUsageHelper::getMemoryUsage(lastCallInfo) == 0)
   {
      lastCallMemInMegabyte = lastCallInfo.currentProcessMemoryUsage;
      memoryCheckSucceeded = true;
   }
   else
   {
      memoryCheckSucceeded = false;
   }

   safeCout("After last call " << lastCallMemInMegabyte << "  -  " << firstCallMemInMegabyte << " threshold " << MAX_MEMORY_INCREASE_MEGABYTE);

   ASSERT_LT(lastCallMemInMegabyte - firstCallMemInMegabyte, MAX_MEMORY_INCREASE_MEGABYTE);

   ASSERT_TRUE(memoryCheckSucceeded);
}

#endif

TEST_F(BasicCallTests, BasicCallCallerEnds_BuildSanity) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertAudioFlowing(alice, aliceCall);
      std::this_thread::sleep_for(std::chrono::milliseconds(500));
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
      assertCallHadAudio(alice, aliceCall);
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(3000));
      assertAudioFlowing(bob, bobCall);

      {
         SipConversationHandle h;
         ConversationEndedEvent evt;
         ASSERT_TRUE(bob.conversationEvents->expectEvent("SipConversationHandler::onConversationEnded",
            35000, HandleEqualsPred<SipConversationHandle>(bobCall), h, evt));
         ASSERT_EQ(ConversationEndReason_UserTerminatedRemotely, evt.endReason);
      }
        assertCallHadAudio(bob, bobCall);
   });

   waitFor2(aliceEvents, bobEvents);
}

TEST_F(BasicCallTests, BasicCallCall_CheckEscapedChars) {
   TestAccount alice("alice");
   TestAccount bob("bob", Account_NoInit);
   bob.config.settings.username = "1234#";
   bob.init();
   bob.enable();

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);


   auto aliceEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged_ex(alice, aliceCall, [&](const ConversationStateChangedEvent& evt)
      {
         ASSERT_EQ(ConversationState_RemoteRinging, evt.conversationState);
         ASSERT_EQ(bob.config.uri(), evt.remoteAddress);
      });

      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged_ex(alice, aliceCall, [&](const ConversationStateChangedEvent& evt)
      {
         ASSERT_EQ(ConversationState_Connected, evt.conversationState);
         ASSERT_EQ(bob.config.uri(), evt.remoteAddress);
      });

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertAudioFlowing(alice, aliceCall);
      std::this_thread::sleep_for(std::chrono::milliseconds(500));
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
      assertCallHadAudio(alice, aliceCall);
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(3000));
      assertAudioFlowing(bob, bobCall);

      {
         SipConversationHandle h;
         ConversationEndedEvent evt;
         ASSERT_TRUE(bob.conversationEvents->expectEvent("SipConversationHandler::onConversationEnded",
            35000, HandleEqualsPred<SipConversationHandle>(bobCall), h, evt));
         ASSERT_EQ(ConversationEndReason_UserTerminatedRemotely, evt.endReason);
      }
        assertCallHadAudio(bob, bobCall);
   });

   waitFor2(aliceEvents, bobEvents);
}

TEST_F(BasicCallTests, BasicCallCallerEndsIPv6_BuildSanity) {
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.ipVersion = CPCAPI2::SipAccount::IpVersion_V6;
   alice.config.settings.outboundProxy = "[::1]:6060";
   alice.init();
   alice.enable();
   TestAccount bob("bob", Account_NoInit);
   bob.config.settings.ipVersion = CPCAPI2::SipAccount::IpVersion_V6;
   bob.config.settings.outboundProxy = "[::1]:6060";
   bob.init();
   bob.enable();

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]() {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertAudioFlowing(alice, aliceCall);
      std::this_thread::sleep_for(std::chrono::milliseconds(500));
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
      assertCallHadAudio(alice, aliceCall);
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(3000));
      assertAudioFlowing(bob, bobCall);

      {
         SipConversationHandle h;
         ConversationEndedEvent evt;
         ASSERT_TRUE(bob.conversationEvents->expectEvent("SipConversationHandler::onConversationEnded",
            35000, HandleEqualsPred<SipConversationHandle>(bobCall), h, evt));
         ASSERT_EQ(ConversationEndReason_UserTerminatedRemotely, evt.endReason);
      }
      assertCallHadAudio(bob, bobCall);
   });

   waitFor2(aliceEvents, bobEvents);
}

TEST_F(BasicCallTests, BasicCallCallerEnds_ICE) { // NOTE! this test is designed to work even if stun.counterpath.com is not reachable! please don't disable it if there's an issue, report it to jgeras instead!
   TestAccount alice("alice");
   TestAccount bob("bob");

   SipConversationSettings settings;
   settings.natTraversalMode = NatTraversalMode_ICE;
   settings.natTraversalServerSource = NatTraversalServerSource_Custom;
   settings.natTraversalServer = "stun.counterpath.com";
   settings.natTraversalServerType = NatTraversalServerType_StunOnly;
   alice.conversation->setDefaultSettings(alice.handle, settings);
   bob.conversation->setDefaultSettings(bob.handle, settings);

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]() {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent("SipConversationHandler::onConversationStateChanged",
            30000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
         ASSERT_EQ(ConversationState_RemoteRinging, evt.conversationState);
      }
      {
         SipConversationHandle h;
         ConversationMediaChangedEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent("SipConversationHandler::onConversationMediaChanged",
            20000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
         ASSERT_LE((size_t)1, evt.localMediaInfo.size());
         for (cpc::vector<MediaInfo>::const_iterator it = evt.localMediaInfo.begin(); it != evt.localMediaInfo.end(); it++)
         {
            ASSERT_EQ(MediaDirection_SendReceive, it->mediaDirection);
         }
      }
      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent("SipConversationHandler::onConversationStateChanged",
            30000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
         ASSERT_EQ(ConversationState_Connected, evt.conversationState);
      }
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertAudioFlowing(alice, aliceCall);
      std::this_thread::sleep_for(std::chrono::milliseconds(500));
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
      assertCallHadAudio(alice, aliceCall);
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(bob.conversationEvents->expectEvent("SipConversationHandler::onConversationStateChanged",
            30000, HandleEqualsPred<SipConversationHandle>(bobCall), h, evt));
         ASSERT_EQ(ConversationState_Connected, evt.conversationState);
      }
      std::this_thread::sleep_for(std::chrono::milliseconds(3000));
      assertAudioFlowing(bob, bobCall);

      {
         SipConversationHandle h;
         ConversationEndedEvent evt;
         ASSERT_TRUE(bob.conversationEvents->expectEvent("SipConversationHandler::onConversationEnded",
            45000, HandleEqualsPred<SipConversationHandle>(bobCall), h, evt));
         ASSERT_EQ(ConversationEndReason_UserTerminatedRemotely, evt.endReason);
      }
      assertCallHadAudio(bob, bobCall);
   });

   waitFor2(aliceEvents, bobEvents);
}

TEST_F(BasicCallTests, BasicCallCallerEnds_ICE_DTLS) { // NOTE! this test is designed to work even if stun.counterpath.com is not reachable! please don't disable it if there's an issue, report it to jgeras instead!
   TestAccount alice("alice", Account_NoInit);
   TestAccount bob("bob", Account_NoInit);
   
   alice.config.dtlsSupported = true;
   alice.init();
   alice.enable();
   bob.config.dtlsSupported = true;
   bob.init();
   bob.enable();

   SipConversationSettings settings;
   settings.natTraversalMode = NatTraversalMode_ICE;
   settings.natTraversalServerSource = NatTraversalServerSource_Custom;
   settings.natTraversalServer = "stun.counterpath.com";
   settings.natTraversalServerType = NatTraversalServerType_StunOnly;
   alice.conversation->setDefaultSettings(alice.handle, settings);
   bob.conversation->setDefaultSettings(bob.handle, settings);

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());

   MediaInfo aliceMedia;
   aliceMedia.mediaDirection = MediaDirection_SendReceive;
   aliceMedia.mediaType = MediaType_Audio;
   aliceMedia.mediaEncryptionOptions.mediaEncryptionMode = MediaEncryptionMode_SRTP_DTLS_Encrypted;
   aliceMedia.mediaEncryptionOptions.secureMediaRequired = true;
   aliceMedia.mediaEncryptionOptions.mediaCryptoSuites.clear(); // Ensure no other cryptos are included
   aliceMedia.mediaEncryptionOptions.mediaCryptoSuites.push_back(MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);
   alice.conversation->configureMedia(aliceCall, aliceMedia);

   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]() {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent("SipConversationHandler::onConversationStateChanged",
            30000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
         ASSERT_EQ(ConversationState_RemoteRinging, evt.conversationState);
      }
      {
         SipConversationHandle h;
         ConversationMediaChangedEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent("SipConversationHandler::onConversationMediaChanged",
            20000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
         ASSERT_LE((size_t)1, evt.localMediaInfo.size());
         for (cpc::vector<MediaInfo>::const_iterator it = evt.localMediaInfo.begin(); it != evt.localMediaInfo.end(); it++)
         {
            ASSERT_EQ(MediaDirection_SendReceive, it->mediaDirection);
         }
      }
      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent("SipConversationHandler::onConversationStateChanged",
            30000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
         ASSERT_EQ(ConversationState_Connected, evt.conversationState);
      }
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertAudioFlowing(alice, aliceCall);
      std::this_thread::sleep_for(std::chrono::milliseconds(500));
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
      assertCallHadAudio(alice, aliceCall);
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);

      MediaInfo bobMedia;
      bobMedia.mediaDirection = MediaDirection_SendReceive;
      bobMedia.mediaType = MediaType_Audio;
      bobMedia.mediaEncryptionOptions.mediaEncryptionMode = MediaEncryptionMode_SRTP_DTLS_Encrypted;
      bobMedia.mediaEncryptionOptions.secureMediaRequired = true;
      bobMedia.mediaEncryptionOptions.mediaCryptoSuites.clear(); // Ensure no other cryptos are included
      bobMedia.mediaEncryptionOptions.mediaCryptoSuites.push_back(MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);
      bob.conversation->configureMedia(bobCall, bobMedia);

      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(bob.conversationEvents->expectEvent("SipConversationHandler::onConversationStateChanged",
            30000, HandleEqualsPred<SipConversationHandle>(bobCall), h, evt));
         ASSERT_EQ(ConversationState_Connected, evt.conversationState);
      }
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertAudioFlowing(bob, bobCall);

      {
         SipConversationHandle h;
         ConversationEndedEvent evt;
         ASSERT_TRUE(bob.conversationEvents->expectEvent("SipConversationHandler::onConversationEnded",
            45000, HandleEqualsPred<SipConversationHandle>(bobCall), h, evt));
         ASSERT_EQ(ConversationEndReason_UserTerminatedRemotely, evt.endReason);
      }
      assertCallHadAudio(bob, bobCall);
   });

   waitFor2(aliceEvents, bobEvents);
}

TEST_F(BasicCallTests, BasicCallCallerEnds_DTLS) { // NOTE! this test is designed to work even if stun.counterpath.com is not reachable! please don't disable it if there's an issue, report it to jgeras instead!
   TestAccount alice("alice", Account_NoInit);
   TestAccount bob("bob", Account_NoInit);
   
   alice.config.dtlsSupported = true;
   alice.init();
   alice.enable();
   bob.config.dtlsSupported = true;
   bob.init();
   bob.enable();

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());

   MediaInfo aliceMedia;
   aliceMedia.mediaDirection = MediaDirection_SendReceive;
   aliceMedia.mediaType = MediaType_Audio;
   aliceMedia.mediaEncryptionOptions.mediaEncryptionMode = MediaEncryptionMode_SRTP_DTLS_Encrypted;
   aliceMedia.mediaEncryptionOptions.secureMediaRequired = true;
   aliceMedia.mediaEncryptionOptions.mediaCryptoSuites.clear(); // Ensure no other cryptos are included
   aliceMedia.mediaEncryptionOptions.mediaCryptoSuites.push_back(MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);
   alice.conversation->configureMedia(aliceCall, aliceMedia);

   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]() {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent("SipConversationHandler::onConversationStateChanged",
            30000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
         ASSERT_EQ(ConversationState_RemoteRinging, evt.conversationState);
      }
      {
         SipConversationHandle h;
         ConversationMediaChangedEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent("SipConversationHandler::onConversationMediaChanged",
            15000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
         ASSERT_LE((size_t)1, evt.localMediaInfo.size());
         for (cpc::vector<MediaInfo>::const_iterator it = evt.localMediaInfo.begin(); it != evt.localMediaInfo.end(); it++)
         {
            ASSERT_EQ(MediaDirection_SendReceive, it->mediaDirection);
         }
      }
      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent("SipConversationHandler::onConversationStateChanged",
            30000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
         ASSERT_EQ(ConversationState_Connected, evt.conversationState);
      }
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertAudioFlowing(alice, aliceCall);
      std::this_thread::sleep_for(std::chrono::milliseconds(500));
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
      assertCallHadAudio(alice, aliceCall);
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);

      MediaInfo bobMedia;
      bobMedia.mediaDirection = MediaDirection_SendReceive;
      bobMedia.mediaType = MediaType_Audio;
      bobMedia.mediaEncryptionOptions.mediaEncryptionMode = MediaEncryptionMode_SRTP_DTLS_Encrypted;
      bobMedia.mediaEncryptionOptions.secureMediaRequired = true;
      bobMedia.mediaEncryptionOptions.mediaCryptoSuites.clear(); // Ensure no other cryptos are included
      bobMedia.mediaEncryptionOptions.mediaCryptoSuites.push_back(MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);
      bob.conversation->configureMedia(bobCall, bobMedia);

      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(3000));
      assertAudioFlowing(bob, bobCall);

      {
         SipConversationHandle h;
         ConversationEndedEvent evt;
         ASSERT_TRUE(bob.conversationEvents->expectEvent("SipConversationHandler::onConversationEnded",
            45000, HandleEqualsPred<SipConversationHandle>(bobCall), h, evt));
         ASSERT_EQ(ConversationEndReason_UserTerminatedRemotely, evt.endReason);
      }
      assertCallHadAudio(bob, bobCall);
   });

   waitFor2(aliceEvents, bobEvents);
}

TEST_F(BasicCallTests, DISABLED_BasicCallMonitorLevels) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      alice.conversation->startMonitoringAudioDeviceLevels(aliceCall);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertAudioFlowing(alice, aliceCall);
      std::this_thread::sleep_for(std::chrono::milliseconds(25000));
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
      assertCallHadAudio(alice, aliceCall);
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(3000));
      assertAudioFlowing(bob, bobCall);

      {
         SipConversationHandle h;
         ConversationEndedEvent evt;
         ASSERT_TRUE(bob.conversationEvents->expectEvent("SipConversationHandler::onConversationEnded",
            35000, HandleEqualsPred<SipConversationHandle>(bobCall), h, evt));
         ASSERT_EQ(ConversationEndReason_UserTerminatedRemotely, evt.endReason);
      }
      assertCallHadAudio(bob, bobCall);
   });

   waitFor2(aliceEvents, bobEvents);
}

TEST_F(BasicCallTests, BasicCallPlaySoundDuring) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      PlaySoundHandle clip = alice.audio->playSound(CPCAPI2::Media::AudioDeviceRole_Headset, "test.wav", true);
      std::this_thread::sleep_for(std::chrono::milliseconds(100));
      PlaySoundHandle clipTwo = alice.audio->playSound(CPCAPI2::Media::AudioDeviceRole_Headset, "test.wav", true);
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      alice.audio->stopPlaySound(clip);
      alice.audio->stopPlaySound(clipTwo);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor2(aliceEvents, bobEvents);
}



TEST_F(BasicCallTests, BasicCallAfterSetRenderDevice) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   CPCAPI2::Media::AudioDeviceListUpdatedEvent evt;
   int handle = 0;
   ASSERT_EQ(alice.audio->queryDeviceList(), kSuccess);
   ASSERT_TRUE(alice.mediaEvents->expectEvent("AudioHandler::onAudioDeviceListUpdated", 30000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
   ASSERT_NE(evt.deviceInfo.size(), 0);
   int numRenderDevs = evt.deviceInfo.size();

   for (int i=0; i<numRenderDevs; i++)
   {
      if (evt.deviceInfo[i].deviceType != CPCAPI2::Media::MediaDeviceType_Render)
      {
         continue;
      }

      alice.audio->setRenderDevice(evt.deviceInfo[i].id, CPCAPI2::Media::AudioDeviceRole_Headset);
      safeCout("**********************************");
      safeCout("set render device: " << evt.deviceInfo[i].id << ", " << evt.deviceInfo[i].friendlyName);
      safeCout("**********************************");

      // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->addParticipant(aliceCall, bob.config.uri());
      alice.conversation->start(aliceCall);

      auto aliceEvents = std::async(std::launch::async, [&] () {
         assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
         assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
         assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
         assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
         std::this_thread::sleep_for(std::chrono::milliseconds(5000));
         assertSuccess(alice.conversation->end(aliceCall));
         assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
      });

      auto bobEvents = std::async(std::launch::async, [&] () {
         SipConversationHandle bobCall;
         assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
         assertSuccess(bob.conversation->sendRingingResponse(bobCall));
         assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
         assertSuccess(bob.conversation->accept(bobCall));
         assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
         assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
         assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
      });

      waitFor2(aliceEvents, bobEvents);

   }
}


#ifdef ANDROID
// requires Zebra device
TEST_F(BasicCallTests, DISABLED_BasicCallSetRenderDeviceDuringCall) {

   if (!TestEnvironmentConfig::logcatServerIp().empty())
   {
      TestAccount alice("alice");
      TestAccount bob("bob");

      CPCAPI2::Media::AudioDeviceListUpdatedEvent evt;
      int handle = 0;
      ASSERT_EQ(alice.audio->queryDeviceList(), kSuccess);
      ASSERT_TRUE(alice.mediaEvents->expectEvent("AudioHandler::onAudioDeviceListUpdated", 30000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
      ASSERT_NE(evt.deviceInfo.size(), 0);

      AudioDeviceInfo speakerDevice;
      AudioDeviceInfo earpieceDevice;
      for (size_t i=0; i< evt.deviceInfo.size(); i++)
      {
         if (evt.deviceInfo[i].deviceType == CPCAPI2::Media::MediaDeviceType_Render)
         {
            if (evt.deviceInfo[i].role == Media::AudioDeviceRole_SpeakerPhone)
            {
               speakerDevice = evt.deviceInfo[i];
            }
            else if (evt.deviceInfo[i].role == Media::AudioDeviceRole_Headset)
            {
               earpieceDevice = evt.deviceInfo[i];
            }
         }
      }
      
      // ensure logcat monitor is up and running
      LogcatMonitorHolder::getInstance();
      
      LogcatMonitorHolder::getInstance()->monitor()->logcatTestMonitor();
      Logcat::LogcatTestMonitorSuccessEvent event;
      int dummyHandle;
      ASSERT_TRUE(cpcExpectEvent(LogcatMonitorHolder::getInstance()->events(), "LogcatHandler::onLogcatTestMonitorSuccess", 5000, CPCAPI2::test::AlwaysTruePred(), dummyHandle, event));
      
      // alice should be using real android audio backend
      ASSERT_EQ(AudioLayers_PlatformDefault, alice.config.mediaSettings.audioLayer);
      
      alice.audio->setRenderDevice(earpieceDevice.id, earpieceDevice.role);

      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->addParticipant(aliceCall, bob.config.uri());
      alice.conversation->start(aliceCall);

      
      auto aliceEvents = std::async(std::launch::async, [&] () {
         assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
         assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
         assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
         assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
         
         Logcat::LogcatAudioModeChangeEvent event;
         int dummyHandle;
         ASSERT_TRUE(cpcExpectEvent(LogcatMonitorHolder::getInstance()->events(), "LogcatHandler::onAudioModeChange", 5000, CPCAPI2::test::AlwaysTruePred(), dummyHandle, event));
         ASSERT_EQ(event.newMode, 3);
         
         std::this_thread::sleep_for(std::chrono::milliseconds(5000));
         alice.audio->setRenderDevice(speakerDevice.id, speakerDevice.role);
         std::this_thread::sleep_for(std::chrono::milliseconds(1000));
         alice.audio->setRenderDevice(earpieceDevice.id, earpieceDevice.role);
         std::this_thread::sleep_for(std::chrono::milliseconds(1000));
         alice.audio->setRenderDevice(speakerDevice.id, speakerDevice.role);
         
         
         // should be *no* change
         ASSERT_FALSE(cpcExpectEvent(LogcatMonitorHolder::getInstance()->events(), "LogcatHandler::onAudioModeChange", 10000, CPCAPI2::test::AlwaysTruePred(), dummyHandle, event));
         
         assertSuccess(alice.conversation->end(aliceCall));
         assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
         
         ASSERT_TRUE(cpcExpectEvent(LogcatMonitorHolder::getInstance()->events(), "LogcatHandler::onAudioModeChange", 5000, CPCAPI2::test::AlwaysTruePred(), dummyHandle, event));
         ASSERT_EQ(event.newMode, 0);
         
         ASSERT_FALSE(cpcExpectEvent(LogcatMonitorHolder::getInstance()->events(), "LogcatHandler::onAudioModeChange", 15000, CPCAPI2::test::AlwaysTruePred(), dummyHandle, event));
      });

      auto bobEvents = std::async(std::launch::async, [&] () {
         SipConversationHandle bobCall;
         assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
         assertSuccess(bob.conversation->sendRingingResponse(bobCall));
         assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
         assertSuccess(bob.conversation->accept(bobCall));
         assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
         assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
         
         std::this_thread::sleep_for(std::chrono::milliseconds(17000));
         
         assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
      });

      waitFor2(aliceEvents, bobEvents);
   }
}
#endif // #ifdef ANDROID


TEST_F(BasicCallTests, BasicCallWithDtmf) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   alice.disableCodec("OPUS"); // for the moment inband DTMF is not supported using 48000
   alice.conversation->setDtmfMode(alice.handle, 0, DtmfMode_InBand_SIP_INFO);

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(2000));

      alice.conversation->startDtmfTone(aliceCall, 3, true);
      std::this_thread::sleep_for(std::chrono::milliseconds(10));
      alice.conversation->stopDtmfTone();

      alice.conversation->startDtmfTone(aliceCall, 2, true);
      std::this_thread::sleep_for(std::chrono::milliseconds(10));
      alice.conversation->stopDtmfTone();

      alice.conversation->startDtmfTone(aliceCall, 3, true);
      std::this_thread::sleep_for(std::chrono::milliseconds(10));
      alice.conversation->stopDtmfTone();

      alice.conversation->startDtmfTone(aliceCall, 2, true);
      std::this_thread::sleep_for(std::chrono::milliseconds(10));
      alice.conversation->stopDtmfTone();

      alice.conversation->startDtmfTone(aliceCall, 3, true);
      std::this_thread::sleep_for(std::chrono::milliseconds(10));
      alice.conversation->stopDtmfTone();

      alice.conversation->startDtmfTone(aliceCall, 2, true);
      std::this_thread::sleep_for(std::chrono::milliseconds(10));
      alice.conversation->stopDtmfTone();

      alice.conversation->startDtmfTone(aliceCall, 3, true);
      std::this_thread::sleep_for(std::chrono::milliseconds(10));
      alice.conversation->stopDtmfTone();

      alice.conversation->startDtmfTone(aliceCall, 2, true);
      std::this_thread::sleep_for(std::chrono::milliseconds(10));
      alice.conversation->stopDtmfTone();

      alice.conversation->startDtmfTone(aliceCall, 1, true);
      std::this_thread::sleep_for(std::chrono::milliseconds(10));
      alice.conversation->stopDtmfTone();

      alice.conversation->startDtmfTone(aliceCall, 2, true);
      std::this_thread::sleep_for(std::chrono::milliseconds(10));
      alice.conversation->stopDtmfTone();

      alice.conversation->startDtmfTone(aliceCall, 3, true);
      std::this_thread::sleep_for(std::chrono::milliseconds(30));
      alice.conversation->stopDtmfTone();

      alice.conversation->startDtmfTone(aliceCall, 2, true);
      std::this_thread::sleep_for(std::chrono::milliseconds(10));
      alice.conversation->stopDtmfTone();

      alice.conversation->startDtmfTone(aliceCall, 2, true);

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor2(aliceEvents, bobEvents);
}


const int IncludeDTMF = 1;       // test DTMF tones (note this will lengthen the test)
const int IncludeMute = 2;       // test some mute
const int CheckMOSLQ = 4;        // check MOSLQ
const int CheckLoss = 8;         //check for loss on Bob's end (some known scenarios skipped)
const int SkipPlaysound = 16;

std::pair<SipConversationHandle, SipConversationHandle> doBasicCall(TestAccount& alice, TestAccount& bob, const resip::Data& assertAliceCodec, const resip::Data& assertBobCodec, int options)
{
#if CPCAPI2_BRAND_VQMONEP_MOBILE != 1
   options &= ~CheckMOSLQ;             // depends on VQmon library
#endif
   
   std::string soundClipUri;
#ifdef ANDROID
   soundClipUri = "android.resource://com.counterpath.sdkdemo.advancedaudiocall/raw/baseball";
#else
   soundClipUri = "file:" + TestEnvironmentConfig::testResourcePath() + "baseball.wav";
#endif

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   safeCout("doBasicCall: handle: " << aliceCall << ", aliceCodec: " << assertAliceCodec << ((options & IncludeDTMF) ? " with DTMF" : " without DTMF") << ((options & IncludeMute) ? " and with mute" : " and without mute"));
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);
   
   const int muteDurationSec = 10;

   auto aliceEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      if (!(options & SkipPlaysound))
      {
         alice.conversation->playSound(aliceCall, soundClipUri.c_str(), true);
      }

      SipConversationState aliceConvState;
      alice.conversationState->getState(aliceCall, aliceConvState);
      ASSERT_NE(aliceConvState.localMediaInfo.size(), 0);
      ASSERT_TRUE(resip::isEqualNoCase(resip::Data(aliceConvState.localMediaInfo[0].audioCodec.plname), assertAliceCodec));

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      if (options & IncludeMute)
      {
         if (!(options & SkipPlaysound))
         {
            alice.conversation->stopPlaySound(aliceCall); // mute doesn't seem to affect playSound(..)
         }
         alice.audio->setMicMute(true);
      }
      std::this_thread::sleep_for(std::chrono::seconds(muteDurationSec));
      if (options & IncludeMute)
      {
         alice.audio->setMicMute(false);
         if (!(options & SkipPlaysound))
         {
            alice.conversation->playSound(aliceCall, soundClipUri.c_str(), true);
         }
      }
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      if (options & IncludeDTMF) alice.conversation->startDtmfTone(aliceCall, 3, true);
      std::this_thread::sleep_for(std::chrono::milliseconds(4000));

      // call quality report needs at least 10 seconds total to fire so we add 4s (12s total, 2s safety)
      if (options & CheckMOSLQ || options & CheckLoss)
         std::this_thread::sleep_for(std::chrono::milliseconds(4000));

      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   SipConversationHandle bobCall;
   auto bobEvents = std::async(std::launch::async, [&] () {
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      SipConversationState bobConvState;
      ASSERT_EQ(bob.conversationState->getState(bobCall, bobConvState), kSuccess);
      ASSERT_NE(bobConvState.localMediaInfo.size(), 0);
      ASSERT_TRUE(resip::isEqualNoCase(resip::Data(bobConvState.localMediaInfo[0].audioCodec.plname), assertBobCodec));
      std::this_thread::sleep_for(std::chrono::seconds(muteDurationSec));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);

      if (options & CheckMOSLQ || options & CheckLoss)
      {
         ASSERT_EQ(bob.conversationState->getState(bobCall, bobConvState), kSuccess);

         if (options & CheckLoss)
         {
            ASSERT_EQ(1, bobConvState.statistics.audioChannels.size());
            ASSERT_GT(bobConvState.statistics.audioChannels[0].streamDataCounters.packetsReceived, 0);
            ASSERT_EQ(bobConvState.statistics.audioChannels
                [0].streamStatistics.cumulativeLost, 0);

            // DRL FIXIT! We see loss here in DTMF cases - because our DTMF packets don't have increasing timestamps?
            if (!(options & IncludeDTMF))
               ASSERT_EQ(bobConvState.statistics.audioChannels.begin()->XRvoipMetrics.lossRate, 0);
         }

         if (options & CheckMOSLQ)
            ASSERT_GT(bobConvState.statistics.audioChannels.begin()->XRvoipMetrics.MOSLQ, 3);
      }
   });

   noReturnWaitFor2(aliceEvents, bobEvents);

   // In case the caller wants to check stats after the call, etc.
   return std::pair<SipConversationHandle, SipConversationHandle>(aliceCall, bobCall);
}

#if CPCAPI2_BRAND_CODEC_SPEEX == 1
TEST_F(BasicCallTests, BasicCallTestSpeexWideband) {
   TestAccount alice("alice");
   alice.enableOnlyThisCodec("SPEEX Wideband");
   TestAccount bob("bob");
   bob.enableOnlyThisCodec("SPEEX Wideband");

   // not checking call quality
   doBasicCall(alice, bob, "speex", "speex", 0);
   doBasicCall(alice, bob, "speex", "speex", IncludeDTMF);
}
#endif

#if CPCAPI2_BRAND_CODEC_SPEEX == 1
TEST_F(BasicCallTests, BasicCallTestSpeexNarrowband) {
   TestAccount alice("alice");
   alice.enableOnlyThisCodec("SPEEX Narrowband");
   TestAccount bob("bob");
   bob.enableOnlyThisCodec("SPEEX Narrowband");

   // not checking call quality
   doBasicCall(alice, bob, "speex", "speex", 0);
   doBasicCall(alice, bob, "speex", "speex", IncludeDTMF);
}
#endif

#if (CPCAPI2_BRAND_CODEC_G729==1)
TEST_F(BasicCallTests, BasicCallTestG729_Muted) {
   TestAccount alice("alice");
   alice.enableOnlyThisCodec("G.729");
   TestAccount bob("bob");
   bob.enableOnlyThisCodec("G.729");

   std::pair<SipConversationHandle, SipConversationHandle> conversations;
   SipConversationState state;
   CPCAPI2::Media::G729Config g729config;

   // To verify if annexb is working:
   // Mute mic, sniff traffic and check
   //   single packet rtp bursts only => annexb
   //   multi-packet rtp bursts => not annexb

   alice.audio->setMicMute(true);
   bob.audio->setMicMute(true);

   // annexb not negotiated => rtp flows normally
   g729config.useAnnexB = false;
   alice.audio->setCodecConfig(g729config);
   bob.audio->setCodecConfig(g729config); // default useAnnexB=true
   conversations = doBasicCall(alice, bob, "g729", "g729", IncludeDTMF | SkipPlaysound);
   ASSERT_EQ(alice.conversationState->getState(conversations.first, state), kSuccess);
   ASSERT_GT(state.statistics.audioChannels[0].streamDataCounters.packetsReceived, 10);
   ASSERT_GT(state.statistics.audioChannels[0].streamDataCounters.packetsSent, 10);
   ASSERT_EQ(bob.conversationState->getState(conversations.second, state), kSuccess);
   ASSERT_GT(state.statistics.audioChannels[0].streamDataCounters.packetsReceived, 10);
   ASSERT_GT(state.statistics.audioChannels[0].streamDataCounters.packetsSent, 10);

   // annexb not negotiated => rtp flows normally
   g729config.useAnnexB = true;
   alice.audio->setCodecConfig(g729config);
   g729config.useAnnexB = false;
   bob.audio->setCodecConfig(g729config);
   conversations = doBasicCall(alice, bob, "g729", "g729", IncludeDTMF | SkipPlaysound);
   ASSERT_EQ(alice.conversationState->getState(conversations.first, state), kSuccess);
   ASSERT_GT(state.statistics.audioChannels[0].streamDataCounters.packetsReceived, 10);
   ASSERT_GT(state.statistics.audioChannels[0].streamDataCounters.packetsSent, 10);
   ASSERT_EQ(bob.conversationState->getState(conversations.second, state), kSuccess);
   ASSERT_GT(state.statistics.audioChannels[0].streamDataCounters.packetsReceived, 10);
   ASSERT_GT(state.statistics.audioChannels[0].streamDataCounters.packetsSent, 10);

   // annexb not negotiated => rtp flows normally
   // nit: bob sends sdp answer with annexb=yes
   g729config.useAnnexB = false;
   alice.audio->setCodecConfig(g729config);
   g729config.useAnnexB = true;
   bob.audio->setCodecConfig(g729config);
   conversations = doBasicCall(alice, bob, "g729", "g729", IncludeDTMF | SkipPlaysound);
   ASSERT_EQ(alice.conversationState->getState(conversations.first, state), kSuccess);
   ASSERT_GT(state.statistics.audioChannels[0].streamDataCounters.packetsReceived, 10);
   ASSERT_GT(state.statistics.audioChannels[0].streamDataCounters.packetsSent, 10);
   ASSERT_EQ(bob.conversationState->getState(conversations.second, state), kSuccess);
   ASSERT_GT(state.statistics.audioChannels[0].streamDataCounters.packetsReceived, 10);
   ASSERT_GT(state.statistics.audioChannels[0].streamDataCounters.packetsSent, 10);

   // annexb negotiated => annexb rtp
   g729config.useAnnexB = true;
   alice.audio->setCodecConfig(g729config);
   bob.audio->setCodecConfig(g729config);  // remains useAnnexB=true
   conversations = doBasicCall(alice, bob, "g729", "g729", IncludeDTMF | SkipPlaysound);
   ASSERT_EQ(alice.conversationState->getState(conversations.first, state), kSuccess);

   // for OBELISK-3994, the first time we send RTP keep-alive packets, we send 12.
   // since g729b will result in us pausing sending RTP, we will send RTP keep-alive
   // packets during this call.
   const int initialRtpKeepaliveCount = 12;
   const int midCallRtpKeepaliveCount = 3 * 3; /* as of r181095, 3 RTP packets are sent all at once per keep-alive interval */
   // approximate guess -- currently from observation
   const int maxG729RtpPacketsSent = 30;

   const int maxTotalPacketsSent = initialRtpKeepaliveCount + midCallRtpKeepaliveCount + maxG729RtpPacketsSent;

   ASSERT_LT(state.statistics.audioChannels[0].streamDataCounters.packetsReceived, maxTotalPacketsSent);
   ASSERT_LT(state.statistics.audioChannels[0].streamDataCounters.packetsSent, maxTotalPacketsSent);
   ASSERT_EQ(bob.conversationState->getState(conversations.second, state), kSuccess);
   ASSERT_LT(state.statistics.audioChannels[0].streamDataCounters.packetsReceived, maxTotalPacketsSent);
   ASSERT_LT(state.statistics.audioChannels[0].streamDataCounters.packetsSent, maxTotalPacketsSent);
}

TEST_F(BasicCallTests, BasicCallTestG729_PartialMute) {
   TestAccount alice("alice");
   alice.enableOnlyThisCodec("G.729");
   TestAccount bob("bob");
   bob.enableOnlyThisCodec("G.729");

   std::pair<SipConversationHandle, SipConversationHandle> conversations;
   SipConversationState state;
   CPCAPI2::Media::G729Config g729config;

   // annexb not negotiated => rtp flows normally
   g729config.useAnnexB = false;
   alice.audio->setCodecConfig(g729config);
   bob.audio->setCodecConfig(g729config); // default useAnnexB=true
   conversations = doBasicCall(alice, bob, "g729", "g729", IncludeDTMF | IncludeMute | CheckLoss); // DTMF fails MOSLQ

   // annexb not negotiated => rtp flows normally
   g729config.useAnnexB = true;
   alice.audio->setCodecConfig(g729config);
   g729config.useAnnexB = false;
   bob.audio->setCodecConfig(g729config);
   conversations = doBasicCall(alice, bob, "g729", "g729", IncludeDTMF | IncludeMute | CheckLoss); // DTMF fails MOSLQ

   // annexb not negotiated => rtp flows normally
   // nit: bob sends sdp answer with annexb=yes
   g729config.useAnnexB = false;
   alice.audio->setCodecConfig(g729config);
   g729config.useAnnexB = true;
   bob.audio->setCodecConfig(g729config);
   conversations = doBasicCall(alice, bob, "g729", "g729", IncludeDTMF | IncludeMute | CheckLoss); // DTMF fails MOSLQ

   // annexb negotiated => annexb rtp
   g729config.useAnnexB = true;
   alice.audio->setCodecConfig(g729config);
   bob.audio->setCodecConfig(g729config);  // remains useAnnexB=true
   conversations = doBasicCall(alice, bob, "g729", "g729", IncludeDTMF | IncludeMute | CheckLoss); // DTMF fails MOSLQ
}
#endif // CPCAPI2_BRAND_CODEC_G729


#if (CPCAPI2_BRAND_CODEC_G729==1)
TEST_F(BasicCallTests, BasicCallTestG729_VerifyMos) {
   TestAccount alice("alice");
   alice.enableOnlyThisCodec("G.729");
   TestAccount bob("bob");
   bob.enableOnlyThisCodec("G.729");


   CPCAPI2::CallQuality::CallQualityReporterHandle aliceCqr = alice.createCallQualityReporter();
   CPCAPI2::CallQuality::CallQualityReporterConfig aliceCqrConfig;
   aliceCqrConfig.reportingIntervalSeconds = 10;
   aliceCqrConfig.sipAccount = alice.handle;
   aliceCqrConfig.ignoreFailures = true;
   alice.callQualityReport->configureCallQualityReporter(aliceCqr, aliceCqrConfig);
   alice.callQualityReport->startCallQualityReporter(aliceCqr);

   SipConversationState state;
   CPCAPI2::Media::G729Config g729config;
   
   g729config.useAnnexB = true;
   alice.audio->setCodecConfig(g729config);
   bob.audio->setCodecConfig(g729config);

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);
   
   std::string soundClipUri;
#ifdef ANDROID
   soundClipUri = "android.resource://com.counterpath.sdkdemo.advancedaudiocall/raw/new_im";
#else
   soundClipUri = "file:" + TestEnvironmentConfig::testResourcePath() + "testA-44khz.wav";
#endif
   

   auto aliceEvents = std::async(std::launch::async, [&]() {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      alice.audio->setMicMute(true);

      alice.conversation->playSound(aliceCall, soundClipUri.c_str(), true);
      
      SipConversationState aliceConvState;
      alice.conversationState->getState(aliceCall, aliceConvState);
      ASSERT_NE(aliceConvState.localMediaInfo.size(), 0);
      ASSERT_TRUE(resip::isEqualNoCase(resip::Data(aliceConvState.localMediaInfo[0].audioCodec.plname), "g729"));

      std::this_thread::sleep_for(std::chrono::milliseconds(25000));

      {
         CPCAPI2::CallQuality::CallQualityReporterHandle h;
         CPCAPI2::CallQuality::CallQualityReportGeneratedEvent evt;
         ASSERT_TRUE(alice.callQualityEvents->expectEvent("CallQualityReportHandler::onCallQualityReportGenerated",
                                                          60000, HandleEqualsPred<CPCAPI2::CallQuality::CallQualityReporterHandle>(aliceCqr), h, evt));
      }

      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
      
      SipConversationState state;
      ASSERT_EQ(alice.conversationState->getState(aliceCall, state), kSuccess);
      ASSERT_EQ(1, state.statistics.audioChannels.size());
      ASSERT_GT(state.statistics.audioChannels[0].streamDataCounters.packetsReceived, 0);
//      ASSERT_LE(state.statistics.audioChannels[0].streamDataCounters.packetsReceived, maxAudioPacketsReceived);
      ASSERT_EQ(state.statistics.audioChannels
          [0].streamStatistics.cumulativeLost, 0);
      ASSERT_EQ(state.statistics.audioChannels.begin()->XRvoipMetrics.lossRate, 0);
      ASSERT_GT(state.statistics.audioChannels.begin()->XRvoipMetrics.MOSLQ, 3);
      
      alice.callQualityReport->stopCallQualityReporter(aliceCqr);
   });

   SipConversationHandle bobCall;
   auto bobEvents = std::async(std::launch::async, [&]() {
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      SipConversationState bobConvState;
      bob.conversationState->getState(bobCall, bobConvState);
      ASSERT_NE(bobConvState.localMediaInfo.size(), 0);
      ASSERT_TRUE(resip::isEqualNoCase(resip::Data(bobConvState.localMediaInfo[0].audioCodec.plname), "g729"));
      
      // send minimal audio to make sure annex B kicks in on bob->alice audio
      bob.audio->setMicMute(true);

      // short talk spurt at the start of the call
      std::stringstream file;
      bob.conversation->playSound(bobCall, soundClipUri.c_str(), false);

      // 15 seconds of silence
      std::this_thread::sleep_for(std::chrono::milliseconds(15000));

      // another short talk spurt 15 seconds in. 15 seconds of silence should have been enough to trigger neteq_impl.cc's MAX_NO_STATS_TIME_SEC of 10 seconds
      // and trigger an RTCP-XR packet / stats update
      bob.conversation->playSound(bobCall, soundClipUri.c_str(), false);
      
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));

      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   noReturnWaitFor2(aliceEvents, bobEvents);

   {
      // TODO: currently fails. MOSLQ is 0 since OnPeriodicUpdate is not firing, since alice is not receiving any packets due to annexB active
      ASSERT_EQ(alice.conversationState->getState(aliceCall, state), kSuccess);
      ASSERT_GT(state.statistics.audioChannels.begin()->XRvoipMetrics.MOSLQ, 3);
   }
   {
      ASSERT_EQ(bob.conversationState->getState(bobCall, state), kSuccess);
      ASSERT_GT(state.statistics.audioChannels.begin()->XRvoipMetrics.MOSLQ, 3);
   }

}
#endif // CPCAPI2_BRAND_CODEC_G729


#if (CPCAPI2_BRAND_CODEC_G729==1)


// disabled: our g729 lib on mac now seems to have less pauses in RTP when g729 is enabled.
// this makes us not send RTP keepalives
TEST_F(BasicCallTests, DISABLED_BasicCallTestRtpKeepAlive) {
   TestAccount alice("alice");
   alice.enableOnlyThisCodec("G.729");
   TestAccount bob("bob");
   bob.enableOnlyThisCodec("G.729");

   std::pair<SipConversationHandle, SipConversationHandle> conversations;
   SipConversationState state;
   CPCAPI2::Media::G729Config g729config;

   alice.audio->setMicMute(true);
   bob.audio->setMicMute(true);

   // first test with default RTP keepalive value (not set)
   {
      // annexb negotiated => annexb rtp
      g729config.useAnnexB = true;
      alice.audio->setCodecConfig(g729config);
      bob.audio->setCodecConfig(g729config);  // remains useAnnexB=true
      conversations = doBasicCall(alice, bob, "g729", "g729", IncludeDTMF | CheckLoss); // DTMF fails MOSLQ
      ASSERT_EQ(alice.conversationState->getState(conversations.first, state), kSuccess);

      // for OBELISK-3994, the first time we send RTP keep-alive packets, we send 12.
      // since g729b will result in us pausing sending RTP, we will send RTP keep-alive
      // packets during this call.
      const int initialRtpKeepaliveCount = 12;
      const int midCallRtpKeepaliveCount = 3 * 3; /* as of r181095, 3 RTP packets are sent all at once per keep-alive interval */
      // approximate guess -- currently from observation, only 1 seen sent for this part of the test
      const int maxG729RtpPacketsSent = 1;

      const int maxTotalPacketsSent = initialRtpKeepaliveCount + midCallRtpKeepaliveCount + maxG729RtpPacketsSent;

      ASSERT_LT(state.statistics.audioChannels[0].streamDataCounters.packetsReceived, maxTotalPacketsSent);
      ASSERT_LT(state.statistics.audioChannels[0].streamDataCounters.packetsSent, maxTotalPacketsSent);
      ASSERT_EQ(bob.conversationState->getState(conversations.second, state), kSuccess);
      ASSERT_LT(state.statistics.audioChannels[0].streamDataCounters.packetsReceived, maxTotalPacketsSent);
      ASSERT_LT(state.statistics.audioChannels[0].streamDataCounters.packetsSent, maxTotalPacketsSent);
   }

   // now test with RTP keepalive disabled
   {
      // annexb negotiated => annexb rtp
      g729config.useAnnexB = true;
      alice.audio->setCodecConfig(g729config);
      bob.audio->setCodecConfig(g729config);  // remains useAnnexB=true

      // magic value to disable RTP keep-alive
      alice.media->setRtpKeepAliveIntervalSeconds(0xFFFFFFFF);
      bob.media->setRtpKeepAliveIntervalSeconds(0xFFFFFFFF);

      conversations = doBasicCall(alice, bob, "g729", "g729", IncludeDTMF | CheckLoss); // DTMF fails MOSLQ
      ASSERT_EQ(alice.conversationState->getState(conversations.first, state), kSuccess);

      const int rtpKeepaliveCount = 0;
      // approximate guess -- currently from observation, only 1 seen sent for this part of the test
      const int maxG729RtpPacketsSent = 3;

      const int maxTotalPacketsSent = rtpKeepaliveCount + maxG729RtpPacketsSent;

      ASSERT_LT(state.statistics.audioChannels[0].streamDataCounters.packetsReceived, maxTotalPacketsSent);
      ASSERT_LT(state.statistics.audioChannels[0].streamDataCounters.packetsSent, maxTotalPacketsSent);
      ASSERT_EQ(bob.conversationState->getState(conversations.second, state), kSuccess);
      ASSERT_LT(state.statistics.audioChannels[0].streamDataCounters.packetsReceived, maxTotalPacketsSent);
      ASSERT_LT(state.statistics.audioChannels[0].streamDataCounters.packetsSent, maxTotalPacketsSent);
   }
}
#endif // ANDROID

TEST_F(BasicCallTests, BasicCallTestPCMA) {
   TestAccount alice("alice");
   alice.enableOnlyThisCodec("G711 aLaw");
   TestAccount bob("bob");
   bob.enableOnlyThisCodec("G711 aLaw");

   doBasicCall(alice, bob, "PCMA", "PCMA", CheckLoss | CheckMOSLQ);
   doBasicCall(alice, bob, "PCMA", "PCMA", IncludeDTMF | CheckLoss); // DTMF fails MOSLQ
}

#if (CPCAPI2_BRAND_CODEC_AMRWB==1)
#if (!(defined(__ANDROID__) && (defined(__x86_64__) || defined(__aarch64__)))) // AMR-WB not supported on Android 64-bit ARM
TEST_F(BasicCallTests, BasicCallTestAMRWB) {
   TestAccount alice("alice");
   alice.enableOnlyThisCodec("AMR Wideband");
   TestAccount bob("bob");
   bob.enableOnlyThisCodec("AMR Wideband");

   doBasicCall(alice, bob, "AMR-WB", "AMR-WB", CheckLoss | CheckMOSLQ);
   doBasicCall(alice, bob, "AMR-WB", "AMR-WB", IncludeDTMF | CheckLoss);   // DTMF fails MOSLQ
}
#endif
#endif // CPCAPI2_BRAND_CODEC_AMRWB

#if(CPCAPI2_BRAND_CODEC_SILK==1)
TEST_F(BasicCallTests, BasicCallTestSILKNB) {
   TestAccount alice("alice");
   alice.enableOnlyThisCodec("SILK Narrowband");
   TestAccount bob("bob");
   bob.enableOnlyThisCodec("SILK Narrowband");

   doBasicCall(alice, bob, "silk", "silk", CheckLoss | CheckMOSLQ);
   doBasicCall(alice, bob, "silk", "silk", IncludeDTMF | CheckLoss); // DTMF fails MOSLQ
}
#endif // CPCAPI2_BRAND_CODEC_SILK

#if(CPCAPI2_BRAND_CODEC_SILK==1)
TEST_F(BasicCallTests, BasicCallTestSILKWB) {
   TestAccount alice("alice");
   alice.enableOnlyThisCodec("SILK Wideband");
   TestAccount bob("bob");
   bob.enableOnlyThisCodec("SILK Wideband");

   doBasicCall(alice, bob, "silk", "silk", CheckLoss | CheckMOSLQ);
   doBasicCall(alice, bob, "silk", "silk", IncludeDTMF | CheckLoss); // DTMF fails MOSLQ
}
#endif // CPCAPI2_BRAND_CODEC_SILK

#if(CPCAPI2_BRAND_CODEC_SILK==1)
TEST_F(BasicCallTests, BasicCallTestSILKSWB) {
   TestAccount alice("alice");
   alice.enableOnlyThisCodec("SILK Super-Wideband");
   TestAccount bob("bob");
   bob.enableOnlyThisCodec("SILK Super-Wideband");

   doBasicCall(alice, bob, "silk", "silk", CheckLoss | CheckMOSLQ);
   doBasicCall(alice, bob, "silk", "silk", IncludeDTMF | CheckLoss); // DTMF fails MOSLQ
}
#endif // CPCAPI2_BRAND_CODEC_SILK 

TEST_F(BasicCallTests, BasicCallTestOpus) {
   TestAccount alice("alice");
   alice.enableOnlyThisCodec("OPUS");
   alice.conversation->setDtmfMode(alice.handle, 0, DtmfMode_InBand);

   TestAccount bob("bob");
   bob.enableOnlyThisCodec("OPUS");

   doBasicCall(alice, bob, "opus", "opus", CheckLoss | CheckMOSLQ);
   doBasicCall(alice, bob, "opus", "opus", IncludeDTMF | CheckLoss); // DTMF fails MOSLQ
}

#if(CPCAPI2_BRAND_CODEC_GSM==1)
TEST_F(BasicCallTests, BasicCallTestGSM) {
   TestAccount alice("alice");
   alice.enableOnlyThisCodec("GSM");
   alice.conversation->setDtmfMode(alice.handle, 0, DtmfMode_InBand);

   TestAccount bob("bob");
   bob.enableOnlyThisCodec("GSM");

   std::pair<SipConversationHandle, SipConversationHandle> handlePair =
      doBasicCall(alice, bob, "GSM", "GSM", CheckLoss);  // MOSLQ not working for GSM??
   assertCallHadMedia(alice, handlePair.first, true, false);
   assertCallHadMedia(bob, handlePair.second, true, false);

   handlePair = doBasicCall(alice, bob, "GSM", "GSM", IncludeDTMF | CheckLoss);  // DTMF fails MOSLQ
   assertCallHadMedia(alice, handlePair.first, true, false);
   assertCallHadMedia(bob, handlePair.second, true, false);
}
#endif // CPCAPI2_BRAND_CODEC_GSM

TEST_F(BasicCallTests, BasicCallTestForceOpus) {
   TestAccount alice("alice");
   alice.enableOnlyThisCodec("G.722"); // NOT a typo
   alice.conversation->setDtmfMode(alice.handle, 0, DtmfMode_InBand);

   TestAccount bob("bob");
   bob.enableOnlyThisCodec("OPUS");

   resip::Data assertAliceCodec = "opus";
   resip::Data assertBobCodec = "opus";

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   safeCout("doBasicCall: handle: " << aliceCall << ", aliceCodec: " << assertAliceCodec);
   SipConversation::MediaInfo miAudio;
   miAudio.mediaType = SipConversation::MediaType_Audio;
   miAudio.mediaDirection = SipConversation::MediaDirection_SendReceive;
   miAudio.audioCodec.channels = 1;
   strcpy(miAudio.audioCodec.plname, "OPUS");
   miAudio.audioCodec.plfreq = 48000;
   alice.conversation->configureMedia(aliceCall, miAudio);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]() {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      SipConversationState aliceConvState;
      alice.conversationState->getState(aliceCall, aliceConvState);
      ASSERT_NE(aliceConvState.localMediaInfo.size(), 0);
      ASSERT_TRUE(resip::isEqualNoCase(resip::Data(aliceConvState.localMediaInfo[0].audioCodec.plname), assertAliceCodec));
      std::this_thread::sleep_for(std::chrono::milliseconds(4000));
      alice.conversation->startDtmfTone(aliceCall, 3, true);
      std::this_thread::sleep_for(std::chrono::milliseconds(4000));
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   SipConversationHandle bobCall;
   auto bobEvents = std::async(std::launch::async, [&]() {
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      SipConversationState bobConvState;
      bob.conversationState->getState(bobCall, bobConvState);
      ASSERT_NE(bobConvState.localMediaInfo.size(), 0);
      ASSERT_TRUE(resip::isEqualNoCase(resip::Data(bobConvState.localMediaInfo[0].audioCodec.plname), assertBobCodec));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   noReturnWaitFor2(aliceEvents, bobEvents);

   bob.enableOnlyThisCodec("G.722");

   doBasicCall(alice, bob, "G722", "G722", CheckLoss | CheckMOSLQ);
   doBasicCall(alice, bob, "G722", "G722", IncludeDTMF | CheckLoss); // DTMF fails MOSLQ
}


// packet count used in DTX related tests is not reliable on parallel linux test runs --
// outgoing RTP does not have consistent timing; possibly due to too much load
#if !(defined(__linux__) && !defined(ANDROID))

// both sides have opus w/ dtx enabled, both sides have mic muted
// should be less packets compared to non-dtx opus muted call
TEST_F(BasicCallTests, BasicCallTestOpus_DtxOnBothSides) {
   TestAccount alice("alice", Account_NoInit);
   //alice.config.useFileAudioDevice = true;
   alice.init();
   
   CPCAPI2::CallQuality::CallQualityReporterHandle aliceCqr = alice.createCallQualityReporter();
   CPCAPI2::CallQuality::CallQualityReporterConfig aliceCqrConfig;
   aliceCqrConfig.reportingIntervalSeconds = 10;
   aliceCqrConfig.sipAccount = alice.handle;
   aliceCqrConfig.ignoreFailures = true;
   alice.callQualityReport->configureCallQualityReporter(aliceCqr, aliceCqrConfig);
   alice.callQualityReport->startCallQualityReporter(aliceCqr);

   alice.enable();
   alice.enableOnlyThisCodec("OPUS");
 
   TestAccount bob("bob", Account_NoInit);
   //bob.config.useFileAudioDevice = true;
   bob.init();
   bob.enable();
   bob.enableOnlyThisCodec("OPUS");
   
   resip::Data assertAliceCodec = "opus";
   resip::Data assertBobCodec = "opus";

   /* critical setup for test */
   OpusConfig opusConfig;
   opusConfig.enableDtx = true;
   alice.audio->setMicMute(true);
   bob.audio->setMicMute(true);
   alice.audio->setCodecConfig(opusConfig);
   bob.audio->setCodecConfig(opusConfig);
   
   // with opus dtx + mic mute on
   const int maxAudioPacketsReceived = 65;
   /* /critical setup for test */
   
   const int callLengthSec = 10;


   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   safeCout("doBasicCall: handle: " << aliceCall << ", aliceCodec: " << assertAliceCodec);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      SipConversationState aliceConvState;
      alice.conversationState->getState(aliceCall, aliceConvState);
      ASSERT_NE(aliceConvState.localMediaInfo.size(), 0);
      ASSERT_TRUE(resip::isEqualNoCase(resip::Data(aliceConvState.localMediaInfo[0].audioCodec.plname), assertAliceCodec));
      std::this_thread::sleep_for(std::chrono::seconds(callLengthSec));

      {
         CPCAPI2::CallQuality::CallQualityReporterHandle h;
         CPCAPI2::CallQuality::CallQualityReportGeneratedEvent evt;
         ASSERT_TRUE(alice.callQualityEvents->expectEvent("CallQualityReportHandler::onCallQualityReportGenerated",
                                                          60000, HandleEqualsPred<CPCAPI2::CallQuality::CallQualityReporterHandle>(aliceCqr), h, evt));
      }

      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
      
      SipConversationState state;
      ASSERT_EQ(alice.conversationState->getState(aliceCall, state), kSuccess);
      ASSERT_EQ(1, state.statistics.audioChannels.size());
      ASSERT_GT(state.statistics.audioChannels[0].streamDataCounters.packetsReceived, 0);
      ASSERT_LE(state.statistics.audioChannels[0].streamDataCounters.packetsReceived, maxAudioPacketsReceived);
      ASSERT_EQ(state.statistics.audioChannels
          [0].streamStatistics.cumulativeLost, 0);
      ASSERT_EQ(state.statistics.audioChannels.begin()->XRvoipMetrics.lossRate, 0);
      ASSERT_GT(state.statistics.audioChannels.begin()->XRvoipMetrics.MOSLQ, 3);
      
      alice.callQualityReport->stopCallQualityReporter(aliceCqr);
   });

   SipConversationHandle bobCall;
   auto bobEvents = std::async(std::launch::async, [&] () {
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      SipConversationState bobConvState;
      bob.conversationState->getState(bobCall, bobConvState);
      ASSERT_NE(bobConvState.localMediaInfo.size(), 0);
      ASSERT_TRUE(resip::isEqualNoCase(resip::Data(bobConvState.localMediaInfo[0].audioCodec.plname), assertBobCodec));
      std::this_thread::sleep_for(std::chrono::seconds(callLengthSec));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
      
      SipConversationState state;
      ASSERT_EQ(bob.conversationState->getState(bobCall, state), kSuccess);
      ASSERT_LE(state.statistics.audioChannels[0].streamDataCounters.packetsReceived, maxAudioPacketsReceived);
   });

   noReturnWaitFor2(aliceEvents, bobEvents);
}

// both sides have opus w/ dtx enabled, both sides have mic muted
// should be less packets compared to non-dtx opus muted call
TEST_F(BasicCallTests, BasicCallTestOpus_DtxOffBothSides) {
   TestAccount alice("alice");
   alice.enableOnlyThisCodec("OPUS");
 
   TestAccount bob("bob");
   bob.enableOnlyThisCodec("OPUS");
   
   resip::Data assertAliceCodec = "opus";
   resip::Data assertBobCodec = "opus";

   /* critical setup for test */
   OpusConfig opusConfig;
   opusConfig.enableDtx = false;
   alice.audio->setMicMute(true);
   bob.audio->setMicMute(true);
   alice.audio->setCodecConfig(opusConfig);
   bob.audio->setCodecConfig(opusConfig);
   
   // with opus dtx off + mic mute on
   const int minAudioPacketsReceived = 275;
   /* /critical setup for test */
   
   const int callLengthSec = 15;


   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   safeCout("doBasicCall: handle: " << aliceCall << ", aliceCodec: " << assertAliceCodec);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      SipConversationState aliceConvState;
      alice.conversationState->getState(aliceCall, aliceConvState);
      ASSERT_NE(aliceConvState.localMediaInfo.size(), 0);
      ASSERT_TRUE(resip::isEqualNoCase(resip::Data(aliceConvState.localMediaInfo[0].audioCodec.plname), assertAliceCodec));
      std::this_thread::sleep_for(std::chrono::seconds(callLengthSec));
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
      
      SipConversationState state;
      ASSERT_EQ(alice.conversationState->getState(aliceCall, state), kSuccess);
      ASSERT_GE(state.statistics.audioChannels[0].streamDataCounters.packetsReceived, minAudioPacketsReceived);
   });

   SipConversationHandle bobCall;
   auto bobEvents = std::async(std::launch::async, [&] () {
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      SipConversationState bobConvState;
      bob.conversationState->getState(bobCall, bobConvState);
      ASSERT_NE(bobConvState.localMediaInfo.size(), 0);
      ASSERT_TRUE(resip::isEqualNoCase(resip::Data(bobConvState.localMediaInfo[0].audioCodec.plname), assertBobCodec));
      std::this_thread::sleep_for(std::chrono::seconds(callLengthSec));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
      
      SipConversationState state;
      ASSERT_EQ(bob.conversationState->getState(bobCall, state), kSuccess);
      ASSERT_GE(state.statistics.audioChannels[0].streamDataCounters.packetsReceived, minAudioPacketsReceived);
   });

   noReturnWaitFor2(aliceEvents, bobEvents);
}

TEST_F(BasicCallTests, BasicCallTestOpus_DtxOffOneSide) {
   TestAccount alice("alice");
   alice.enableOnlyThisCodec("OPUS");
 
   TestAccount bob("bob");
   bob.enableOnlyThisCodec("OPUS");
   
   resip::Data assertAliceCodec = "opus";
   resip::Data assertBobCodec = "opus";

   /* critical setup for test */
   OpusConfig opusConfig;
   
   // alice has dtx enabled, but should see bob sent usedtx=0, so not use dtx
   opusConfig.enableDtx = true;
   alice.audio->setMicMute(true);
   alice.audio->setCodecConfig(opusConfig);
   int minAliceAudioPacketsRecv = 275;
   
   // bob has dtx disabled, so won't send opus dtx regardless of what alice offers
   opusConfig.enableDtx = false;
   bob.audio->setMicMute(true);
   bob.audio->setCodecConfig(opusConfig);
   int minBobAudioPacketsRecv = 275;
   /* /critical setup for test */
   
   const int callLengthSec = 15;



   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   safeCout("doBasicCall: handle: " << aliceCall << ", aliceCodec: " << assertAliceCodec);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      SipConversationState aliceConvState;
      alice.conversationState->getState(aliceCall, aliceConvState);
      ASSERT_NE(aliceConvState.localMediaInfo.size(), 0);
      ASSERT_TRUE(resip::isEqualNoCase(resip::Data(aliceConvState.localMediaInfo[0].audioCodec.plname), assertAliceCodec));
      std::this_thread::sleep_for(std::chrono::seconds(callLengthSec));
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
      
      SipConversationState state;
      ASSERT_EQ(alice.conversationState->getState(aliceCall, state), kSuccess);
      
      ASSERT_GE(state.statistics.audioChannels[0].streamDataCounters.packetsReceived, minAliceAudioPacketsRecv);
   });

   SipConversationHandle bobCall;
   auto bobEvents = std::async(std::launch::async, [&] () {
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      SipConversationState bobConvState;
      bob.conversationState->getState(bobCall, bobConvState);
      ASSERT_NE(bobConvState.localMediaInfo.size(), 0);
      ASSERT_TRUE(resip::isEqualNoCase(resip::Data(bobConvState.localMediaInfo[0].audioCodec.plname), assertBobCodec));
      std::this_thread::sleep_for(std::chrono::seconds(callLengthSec));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
      
      SipConversationState state;
      ASSERT_EQ(bob.conversationState->getState(bobCall, state), kSuccess);
      ASSERT_GE(state.statistics.audioChannels[0].streamDataCounters.packetsReceived, minBobAudioPacketsRecv);
   });

   noReturnWaitFor2(aliceEvents, bobEvents);
}

static void setOpusFmtpOmitDtx(void* context)
{
   TestAccount& account = *static_cast<TestAccount*>(context);

   recon::MediaStack* bobMediaStack = dynamic_cast<recon::MediaStack*>(dynamic_cast<CPCAPI2::Media::MediaManagerInterface*>(account.media)->media_stack());
   std::shared_ptr<webrtc_recon::CodecFactoryImpl> codecFactory = std::dynamic_pointer_cast<webrtc_recon::CodecFactoryImpl>(bobMediaStack->codecFactory());
   webrtc_recon::CodecFactoryImpl::Codecs audioCodecs = codecFactory->audioCodecs();
   webrtc_recon::CodecFactoryImpl::Codecs::iterator itAudioCodecs = audioCodecs.begin();
   for (; itAudioCodecs != audioCodecs.end(); ++itAudioCodecs)
   {
      std::shared_ptr<webrtc_recon::OpusCodecImpl> opus_codec = std::dynamic_pointer_cast<webrtc_recon::OpusCodecImpl>(*itAudioCodecs);
      if (opus_codec)
      {
         // omit usedtx param
         opus_codec->allSettings().front().fmtp = "useinbandfec=1; maxaveragebitrate=64000";
         break;
      }
   }
}

TEST_F(BasicCallTests, BasicCallTestOpus_DtxOmittedOneSide) {
   TestAccount alice("alice");
   alice.enableOnlyThisCodec("OPUS");
 
   TestAccount bob("bob");
   bob.enableOnlyThisCodec("OPUS");
   
   resip::Data assertAliceCodec = "opus";
   resip::Data assertBobCodec = "opus";

   /* critical setup for test */
   OpusConfig opusConfig;
   
   opusConfig.enableDtx = true;
   alice.audio->setMicMute(true);
   alice.audio->setCodecConfig(opusConfig);
   // alice offers usedtx, and bob has dtx enabled, so should see lower packet count
   int maxAliceAudioPacketsRecv = 85;
   
   // bob has dtx enabled but omits usedtx in sdp answer
   opusConfig.enableDtx = true;
   bob.audio->setMicMute(true);
   bob.audio->setCodecConfig(opusConfig);
   // bob offers with no usedtx, so alice should not send with dtx. higher packet count expected
   int minBobAudioPacketsRecv = 275;
   /* /critical setup for test */
   
   const int callDurationSec = 15;
   
   // needs to be called after setCodecConfig(..), so changes don't get overwritten
   static_cast<PhoneInternal*>(bob.phone)->runOnSdkModuleThread(setOpusFmtpOmitDtx, &bob);

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   safeCout("doBasicCall: handle: " << aliceCall << ", aliceCodec: " << assertAliceCodec);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      SipConversationState aliceConvState;
      alice.conversationState->getState(aliceCall, aliceConvState);
      ASSERT_NE(aliceConvState.localMediaInfo.size(), 0);
      ASSERT_TRUE(resip::isEqualNoCase(resip::Data(aliceConvState.localMediaInfo[0].audioCodec.plname), assertAliceCodec));
      std::this_thread::sleep_for(std::chrono::seconds(callDurationSec));
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
      
      SipConversationState state;
      ASSERT_EQ(alice.conversationState->getState(aliceCall, state), kSuccess);
      
      ASSERT_LE(state.statistics.audioChannels[0].streamDataCounters.packetsReceived, maxAliceAudioPacketsRecv);
   });

   SipConversationHandle bobCall;
   auto bobEvents = std::async(std::launch::async, [&] () {
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      SipConversationState bobConvState;
      bob.conversationState->getState(bobCall, bobConvState);
      ASSERT_NE(bobConvState.localMediaInfo.size(), 0);
      ASSERT_TRUE(resip::isEqualNoCase(resip::Data(bobConvState.localMediaInfo[0].audioCodec.plname), assertBobCodec));
      std::this_thread::sleep_for(std::chrono::seconds(callDurationSec));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
      
      SipConversationState state;
      ASSERT_EQ(bob.conversationState->getState(bobCall, state), kSuccess);
      ASSERT_GE(state.statistics.audioChannels[0].streamDataCounters.packetsReceived, minBobAudioPacketsRecv);
   });

   noReturnWaitFor2(aliceEvents, bobEvents);
}

#endif // #if defined(__linux__) && !defined(ANDROID)

#if defined(ANDROID)
TEST_F(BasicCallTests, BasicCallTestOpusAAudio_DtxOnBothSides) {
   TestAccount alice("alice", Account_NoInit);
   //alice.config.useFileAudioDevice = true;
   alice.config.useAAudio = true;
   alice.init();
   alice.enable();
   alice.enableOnlyThisCodec("OPUS");

   TestAccount bob("bob", Account_NoInit);
   //bob.config.useFileAudioDevice = true;
   bob.init();
   bob.enable();
   bob.enableOnlyThisCodec("OPUS");

   resip::Data assertAliceCodec = "opus";
   resip::Data assertBobCodec = "opus";

   /* critical setup for test */
   OpusConfig opusConfig;
   opusConfig.enableDtx = true;
   alice.audio->setMicMute(true);
   bob.audio->setMicMute(true);
   alice.audio->setCodecConfig(opusConfig);
   bob.audio->setCodecConfig(opusConfig);

   // with opus dtx + mic mute on
   const int maxAudioPacketsReceived = 65;
   /* /critical setup for test */

   const int callLengthSec = 10;


   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   safeCout("doBasicCall: handle: " << aliceCall << ", aliceCodec: " << assertAliceCodec);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]() {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      SipConversationState aliceConvState;
      alice.conversationState->getState(aliceCall, aliceConvState);
      ASSERT_NE(aliceConvState.localMediaInfo.size(), 0);
      ASSERT_TRUE(resip::isEqualNoCase(resip::Data(aliceConvState.localMediaInfo[0].audioCodec.plname), assertAliceCodec));
      std::this_thread::sleep_for(std::chrono::seconds(callLengthSec));
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);

      SipConversationState state;
      ASSERT_EQ(alice.conversationState->getState(aliceCall, state), kSuccess);
      ASSERT_LE(state.statistics.audioChannels[0].streamDataCounters.packetsReceived, maxAudioPacketsReceived);
      });

   SipConversationHandle bobCall;
   auto bobEvents = std::async(std::launch::async, [&]() {
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      SipConversationState bobConvState;
      bob.conversationState->getState(bobCall, bobConvState);
      ASSERT_NE(bobConvState.localMediaInfo.size(), 0);
      ASSERT_TRUE(resip::isEqualNoCase(resip::Data(bobConvState.localMediaInfo[0].audioCodec.plname), assertBobCodec));
      std::this_thread::sleep_for(std::chrono::seconds(callLengthSec));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);

      SipConversationState state;
      ASSERT_EQ(bob.conversationState->getState(bobCall, state), kSuccess);
      ASSERT_LE(state.statistics.audioChannels[0].streamDataCounters.packetsReceived, maxAudioPacketsReceived);
      });

   noReturnWaitFor2(aliceEvents, bobEvents);
}
#endif // #if defined(ANDROID)

void doAVCall(TestAccount& alice, TestAccount& bob, const resip::Data& assertAliceAudioCodec, const resip::Data& assertAliceVideoCodec, const resip::Data& assertBobAudioCodec, const resip::Data& assertBobVideoCodec, bool forceAliceH264)
{
   // make an outgoing (audio/video) call from Alice to Bob
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   safeCout("doBasicCall: handle: " << aliceCall << ", aliceAudioCodec: " << assertAliceAudioCodec);
   SipConversation::MediaInfo miAudio;
   miAudio.mediaType = SipConversation::MediaType_Audio;
   miAudio.mediaDirection = SipConversation::MediaDirection_SendReceive;
   alice.conversation->configureMedia(aliceCall, miAudio);
   SipConversation::MediaInfo miVideo;
   miVideo.mediaType = SipConversation::MediaType_Video;
   miVideo.mediaDirection = SipConversation::MediaDirection_SendReceive;
   if (forceAliceH264)
   {
      strcpy(miVideo.videoCodec.plName, "H264");
   }
   alice.conversation->configureMedia(aliceCall, miVideo);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]() {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      SipConversationState aliceConvState;
      alice.conversationState->getState(aliceCall, aliceConvState);
      ASSERT_EQ(aliceConvState.localMediaInfo.size(), 2);
      ASSERT_TRUE(resip::isEqualNoCase(resip::Data(aliceConvState.localMediaInfo[0].audioCodec.plname), assertAliceAudioCodec));
      ASSERT_TRUE(resip::isEqualNoCase(resip::Data(aliceConvState.localMediaInfo[1].videoCodec.plName), assertAliceVideoCodec));
      std::this_thread::sleep_for(std::chrono::milliseconds(4000));
      alice.conversation->startDtmfTone(aliceCall, 3, true);
      std::this_thread::sleep_for(std::chrono::milliseconds(4000));
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   SipConversationHandle bobCall;
   auto bobEvents = std::async(std::launch::async, [&]() {
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      SipConversationState bobConvState;
      bob.conversationState->getState(bobCall, bobConvState);
      ASSERT_NE(bobConvState.localMediaInfo.size(), 0);
      ASSERT_TRUE(resip::isEqualNoCase(resip::Data(bobConvState.localMediaInfo[0].audioCodec.plname), assertBobAudioCodec));
      ASSERT_TRUE(resip::isEqualNoCase(resip::Data(bobConvState.localMediaInfo[1].videoCodec.plName), assertBobVideoCodec));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   noReturnWaitFor2(aliceEvents, bobEvents);
}

TEST_F(BasicCallTests, BasicCallTestForceH264) {
   TestAccount alice("alice");
   alice.enableOnlyThisCodec("OPUS");
   alice.enableOnlyThisVideoCodec("VP8"); // not a typo
   alice.conversation->setDtmfMode(alice.handle, 0, DtmfMode_InBand);

   TestAccount bob("bob");
   bob.enableOnlyThisCodec("OPUS");
   bob.enableOnlyThisVideoCodec("H.264");

   resip::Data assertAliceCodec = "opus";
   resip::Data assertBobCodec = "opus";

   // make an outgoing (audio/video) call from Alice to Bob
   doAVCall(alice, bob, "opus", "H264", "opus", "H264", true);

   bob.enableOnlyThisVideoCodec("VP8");

   doAVCall(alice, bob, "opus", "VP8", "opus", "VP8", false);
   //doBasicCall(alice, bob, "G722", "G722");
}


TEST_F(BasicCallTests, BasicCallBeforeAfterCodecEnableDisable) {
   TestAccount alice("alice");
   alice.enableOnlyThisCodec("G.722");
   TestAccount bob("bob");
   bob.enableOnlyThisCodec("G.722");

   doBasicCall(alice, bob, "G722", "G722", CheckLoss | CheckMOSLQ);
   doBasicCall(alice, bob, "G722", "G722", IncludeDTMF | CheckLoss); // DTMF fails MOSLQ

   alice.enableCodec("OPUS");
   alice.audio->queryCodecList();
   bob.enableCodec("OPUS");

   doBasicCall(alice, bob, "OPUS", "OPUS", CheckLoss | CheckMOSLQ);
   doBasicCall(alice, bob, "OPUS", "OPUS", IncludeDTMF | CheckLoss); // DTMF fails MOSLQ

   alice.disableCodec("OPUS");
   alice.audio->queryCodecList();
   bob.disableCodec("OPUS");

   doBasicCall(alice, bob, "G722", "G722", CheckLoss | CheckMOSLQ);
   doBasicCall(alice, bob, "G722", "G722", IncludeDTMF | CheckLoss); // DTMF fails MOSLQ
}

TEST_F(BasicCallTests, BasicCallBeforeAfterCodecPriorityChange) {
   TestAccount alice("alice");
   alice.enableOnlyThisCodec("G.722");
   alice.enableCodec("OPUS");
   TestAccount bob("bob");
   bob.enableOnlyThisCodec("G.722");
   bob.enableCodec("OPUS");

   doBasicCall(alice, bob, "OPUS", "OPUS", CheckLoss | CheckMOSLQ);
   doBasicCall(alice, bob, "OPUS", "OPUS", IncludeDTMF | CheckLoss); // DTMF fails MOSLQ

   alice.audio->queryCodecList();

   alice.setCodecPriority("G.722", 100000);
   bob.setCodecPriority("G.722", 100000);

   doBasicCall(alice, bob, "G722", "G722", CheckLoss | CheckMOSLQ);
   doBasicCall(alice, bob, "G722", "G722", IncludeDTMF | CheckLoss); // DTMF fails MOSLQ

   alice.setCodecPriority("OPUS", 100001);
   bob.setCodecPriority("OPUS", 100001);
   alice.audio->queryCodecList();

   doBasicCall(alice, bob, "OPUS", "OPUS", CheckLoss | CheckMOSLQ);
   doBasicCall(alice, bob, "OPUS", "OPUS", IncludeDTMF | CheckLoss); // DTMF fails MOSLQ
}


TEST_F(BasicCallTests, BasicCallBeforeAfterCodecPriorityChange2) {
   TestAccount alice("alice");
   alice.enableOnlyThisCodec("G.722");
   alice.enableCodec("OPUS");

   TestAccount bob("bob");
   bob.enableOnlyThisCodec("G.722");

   bob.account->disable(bob.handle);
   assertAccountDeregistering(bob);
   assertAccountDeregistered(bob);
   bob.account->enable(bob.handle);
   assertAccountRegistering(bob);
   assertAccountRegistered(bob);

   bob.enableCodec("OPUS");
   doBasicCall(alice, bob, "OPUS", "OPUS", CheckLoss | CheckMOSLQ);
   doBasicCall(alice, bob, "OPUS", "OPUS", IncludeDTMF | CheckLoss); // DTMF fails MOSLQ
}


TEST_F(BasicCallTests, BasicCallCallerExitsWhileStillRinging) {
   TestAccount* alice = new TestAccount("alice", Account_Enable, false);
   TestAccount bob("bob");

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice->conversation->createConversation(alice->handle);
   alice->conversation->addParticipant(aliceCall, bob.config.uri());
   alice->conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(*alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(*alice, aliceCall, ConversationState_RemoteRinging);
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      delete alice;
      //assertConversationEnded(*alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice->config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor2(aliceEvents, bobEvents);
}

TEST_F(BasicCallTests, BasicCallCallerEndsBeforeAfterDisable) {
   TestAccount alice("alice", Account_Enable, true);
   TestAccount bob("bob", Account_Enable, true);

   for (int i=0; i<6; i++)
   {
      // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->addParticipant(aliceCall, bob.config.uri());
      alice.conversation->start(aliceCall);

      auto aliceEvents = std::async(std::launch::async, [&] () {
         assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
         assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
         assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
         assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
         assertSuccess(alice.conversation->end(aliceCall));
         assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
      });

      auto bobEvents = std::async(std::launch::async, [&] () {
         SipConversationHandle bobCall;
         assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
         assertSuccess(bob.conversation->sendRingingResponse(bobCall));
         assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
         assertSuccess(bob.conversation->accept(bobCall));
         assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
         assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
         assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
      });

      waitFor2(aliceEvents, bobEvents);

      alice.account->disable(alice.handle);
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
		   CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
		   ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged",
			   20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(alice.handle, h);
         ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Unregistering, evt.accountStatus);
      }
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
		   CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
		   ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged",
			   20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(alice.handle, h);
         ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Unregistered, evt.accountStatus);
      }

      bob.account->disable(bob.handle);
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
		   CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
		   ASSERT_TRUE(bob.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged",
			   20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(bob.handle, h);
         ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Unregistering, evt.accountStatus);
      }
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
		   CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
		   ASSERT_TRUE(bob.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged",
			   20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(bob.handle, h);
         ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Unregistered, evt.accountStatus);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(2000));

      alice.account->enable(alice.handle);
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
		   CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
		   ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged",
			   20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(alice.handle, h);
         ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);
      }
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
		   CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
		   ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged",
			   20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(alice.handle, h);
         ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
      }

      bob.account->enable(bob.handle);
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
		   CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
		   ASSERT_TRUE(bob.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged",
			   20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(bob.handle, h);
         ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);
      }
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
		   CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
		   ASSERT_TRUE(bob.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged",
			   20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(bob.handle, h);
         ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
      }
   }
}

TEST_F(BasicCallTests, BasicCallAnonymous) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->setAnonymousMode(aliceCall, 1);
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall;
      assertNewConversationIncoming_ex(bob, &bobCall, "sip:<EMAIL>", [](const NewConversationEvent& evt){
         ASSERT_EQ("Anonymous", evt.remoteDisplayName);
      });
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(500));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor2(aliceEvents, bobEvents);
}


void configureMedia(MediaInfo& media, MediaType type, MediaDirection direction, MediaEncryptionMode mode, bool secure, cpc::vector<MediaCryptoSuite>& cryptos)
{
   media.mediaType = type;
   media.mediaDirection = direction;
   media.mediaEncryptionOptions.mediaEncryptionMode = mode;
   media.mediaEncryptionOptions.secureMediaRequired = secure;
   media.mediaEncryptionOptions.mediaCryptoSuites = cryptos;
}

void configureMedia(MediaInfo& media, MediaType type, MediaDirection direction, MediaEncryptionMode mode, bool secure, MediaCryptoSuite crypto1)
{
   cpc::vector<MediaCryptoSuite> cryptos;
   cryptos.push_back(crypto1);
   configureMedia(media, type, direction, mode, secure, cryptos);
}

TEST_F(BasicCallTests, DISABLED_BasicCallHold_Swisscom_MMVAS) {

   TestAccount alice("alice", Account_NoInit);
   TestAccount bob("bob", Account_NoInit);

   MediaInfo media;
   media.mediaEncryptionOptions.mediaCryptoSuites.clear();
   cpc::vector<MediaCryptoSuite> cryptos;
   cryptos.push_back(MediaCryptoSuite_AEAD_AES_128_GCM);
   cryptos.push_back(MediaCryptoSuite_AEAD_AES_256_GCM);
   cryptos.push_back(MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);
   cryptos.push_back(MediaCryptoSuite_AES_CM_192_HMAC_SHA1_80);
   cryptos.push_back(MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80);
   cryptos.push_back(MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);
   cryptos.push_back(MediaCryptoSuite_AES_CM_192_HMAC_SHA1_32);
   cryptos.push_back(MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32);
   configureMedia(media, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, cryptos);

   alice.config.settings.domain = "imst.swisscom.ch";
   alice.config.settings.outboundProxy = "ssc1.imst.swisscom.ch:5076";
   alice.config.settings.username = "+***********-01";
   alice.config.settings.password = R"()E[OTS^\a9=i >)";
   alice.config.settings.useOutbound = false;
   alice.config.settings.useRport = false;
   alice.config.settings.displayName = "";
   alice.config.settings.registrationIntervalSeconds = 900;
   alice.config.settings.sipTransportType = SipAccountTransport_TLS;
   alice.config.settings.auth_username = "<EMAIL>";
   alice.config.settings.userAgent = "Softphone - Enterprise Telephony";
   const cpc::string aliceExt = "sip:+<EMAIL>;user=phone";
   alice.init();
   alice.enable();


   bob.config.settings.domain = "imst.swisscom.ch";
   bob.config.settings.outboundProxy = "ssc1.imst.swisscom.ch:5076";
   bob.config.settings.username = "+***********-01";
   bob.config.settings.password = "YsrS&_tMsDp}UM";
   bob.config.settings.useOutbound = false;
   bob.config.settings.useRport = false;
   bob.config.settings.displayName = "";
   bob.config.settings.registrationIntervalSeconds = 900;
   bob.config.settings.sipTransportType = SipAccountTransport_TLS;
   bob.config.settings.auth_username = "<EMAIL>";
   bob.config.settings.userAgent = "Softphone - Enterprise Telephony";
   const cpc::string bobExt = "sip:+<EMAIL>";
   bob.init();
   bob.enable();


   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->configureMedia(aliceCall, media);
   alice.conversation->addParticipant(aliceCall, bobExt.c_str());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(alice, aliceCall, bobExt.c_str());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendOnly);
      assertSuccess(alice.conversation->accept(aliceCall));
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_FALSE(evt.localHold);
         ASSERT_TRUE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_ReceiveOnly, evt.localMediaInfo[0].mediaDirection);
      });
      
      CPCAPI2::SipConversation::SipConversationHandle h;
      ConversationStateChangedEvent convStateChangedEvt;
      
      // we should *not* expect onConversationStateChanged to be fired at this point, since the target address has not changed
      // since the original 200 OK. Prior to the fix for OBELISK-6349, onConversationStateChanged would fire here becuase
      // the SDK had not properly stored the target address due to 180/200 OK forking.
      ASSERT_FALSE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onConversationStateChanged",
	      2000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, convStateChangedEvt));

      // platform sends a re-INVITE with no media direction
      assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendReceive);
      assertSuccess(alice.conversation->accept(aliceCall));
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });

      // again; expect no onConversationStateChanged event
      ASSERT_FALSE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onConversationStateChanged",
	      2000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, convStateChangedEvt));

      // another re-INVITE from bob taking the call off hold (even though from alice's perspective the call is already off hold)
      assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendReceive);
      assertSuccess(alice.conversation->accept(aliceCall));

      // again; expect no onConversationStateChanged event
      ASSERT_FALSE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onConversationStateChanged",
	      2000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, convStateChangedEvt));

      assertConversationEnded_time(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely, 30000);
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, aliceExt.c_str());
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertSuccess(bob.conversation->hold(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_TRUE(evt.localHold);
         ASSERT_TRUE(evt.remoteHold); // modded to TRUE to to inactive?
         ASSERT_EQ(MediaDirection_Inactive, evt.localMediaInfo[0].mediaDirection); // modded to 4
      });
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertSuccess(bob.conversation->unhold(bobCall));
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });

      // Check that bobCall video has RTCP. We do this by getting two reports and comparing stamps
      TestCallEvents::expectRTCP( __LINE__, bob, bobCall );

      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor2(aliceEvents, bobEvents);
}

TEST_F(BasicCallTests, BasicCallInactiveHoldUnhold) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]() {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_Inactive);
      assertSuccess(alice.conversation->accept(aliceCall));
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_EQ(MediaDirection_Inactive, evt.localMediaInfo[0].mediaDirection);
      });

      assertSuccess(alice.conversation->hold(aliceCall));
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_TRUE(evt.localHold);
         ASSERT_TRUE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_Inactive, evt.localMediaInfo[0].mediaDirection);
      });
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertSuccess(alice.conversation->unhold(aliceCall));
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });



      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      CPCAPI2::SipConversation::MediaInfo bobAudioInactive;
      bobAudioInactive.mediaType = MediaType_Audio;
      bobAudioInactive.mediaDirection = MediaDirection_Inactive;
      bob.conversation->configureMedia(bobCall, bobAudioInactive);
      bob.conversation->sendMediaChangeRequest(bobCall);
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_EQ(MediaDirection_Inactive, evt.localMediaInfo[0].mediaDirection);
      });

      assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_Inactive);
      assertSuccess(bob.conversation->accept(bobCall));

      assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
      CPCAPI2::SipConversation::MediaInfo bobAudioSR;
      bobAudioSR.mediaType = MediaType_Audio;
      bobAudioSR.mediaDirection = MediaDirection_SendReceive;
      bob.conversation->configureMedia(bobCall, bobAudioSR);
      assertSuccess(bob.conversation->accept(bobCall));


      std::this_thread::sleep_for(std::chrono::milliseconds(500));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor2(aliceEvents, bobEvents);
}

TEST_F(BasicCallTests, BasicCallHoldRejectedNoMedia) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendOnly);
      assertSuccess(alice.conversation->reject(aliceCall));
      assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendReceive);
      assertSuccess(alice.conversation->reject(aliceCall));
      {
         SipConversationHandle h;
         ConversationEndedEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent("SipConversationHandler::onConversationEnded",
            25000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
         ASSERT_EQ(ConversationEndReason_UserTerminatedRemotely, evt.endReason);
      }
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      safeCout("============ HOLD ============");
      assertSuccess(bob.conversation->hold(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_TRUE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      std::this_thread::sleep_for(std::chrono::milliseconds(8000));
      safeCout("============ UNHOLD ============");
      assertSuccess(bob.conversation->unhold(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      std::this_thread::sleep_for(std::chrono::milliseconds(8000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor2(aliceEvents, bobEvents);
}

TEST_F(BasicCallTests, BasicCallHoldRejectedNoMedia2) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendOnly);
      assertSuccess(alice.conversation->accept(aliceCall));
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_FALSE(evt.localHold);
         ASSERT_TRUE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_ReceiveOnly, evt.localMediaInfo[0].mediaDirection);
      });

      assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendReceive);

      MediaInfo mediaDesc;
      mediaDesc.mediaDirection = MediaDirection_Inactive;
      mediaDesc.mediaType = MediaType_Audio;
      assertSuccess(alice.conversation->configureMedia(aliceCall, mediaDesc));
      assertSuccess(alice.conversation->accept(aliceCall));
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_FALSE(evt.localHold);
         ASSERT_TRUE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_Inactive, evt.localMediaInfo[0].mediaDirection);
      });

      {
         SipConversationHandle h;
         ConversationEndedEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent("SipConversationHandler::onConversationEnded",
            25000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
         ASSERT_EQ(ConversationEndReason_UserTerminatedRemotely, evt.endReason);
      }
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      safeCout("============ HOLD ============");
      assertSuccess(bob.conversation->hold(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_TRUE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendOnly, evt.localMediaInfo[0].mediaDirection);
      });
      std::this_thread::sleep_for(std::chrono::milliseconds(8000));
      safeCout("============ UNHOLD ============");
      assertSuccess(bob.conversation->unhold(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_FALSE(evt.localHold);
         ASSERT_TRUE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_Inactive, evt.localMediaInfo[0].mediaDirection);
      });
      std::this_thread::sleep_for(std::chrono::milliseconds(8000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor2(aliceEvents, bobEvents);
}

TEST_F(BasicCallTests, BasicCallHoldRejectedNoMedia3) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendReceive);
      assertSuccess(alice.conversation->reject(aliceCall));
      assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendReceive);
      assertSuccess(alice.conversation->reject(aliceCall));
      {
         SipConversationHandle h;
         ConversationEndedEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent("SipConversationHandler::onConversationEnded",
            25000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
         ASSERT_EQ(ConversationEndReason_UserTerminatedRemotely, evt.endReason);
      }
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      safeCout("============ HOLD ============");
      assertSuccess(bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true));
      assertSuccess(bob.conversation->sendMediaChangeRequest(bobCall));
      assertSuccess(bob.conversation->hold(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_TRUE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      std::this_thread::sleep_for(std::chrono::milliseconds(8000));
      safeCout("============ UNHOLD ============");
      assertSuccess(bob.conversation->unhold(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      std::this_thread::sleep_for(std::chrono::milliseconds(8000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor2(aliceEvents, bobEvents);
}


TEST_F(BasicCallTests, BasicCallTo404Address) {
   TestAccount alice("alice");
   // make an outgoing (audio only) call from Alice to an unknown address
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, "sip:invalidinvalidinvalid12345@" + alice.config.settings.domain);
   alice.conversation->start(aliceCall);
   {
      SipConversationHandle h;
      NewConversationEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
         "SipConversationHandler::onNewConversation",
         5000,
         HandleEqualsPred<SipConversationHandle>(aliceCall),
         h, evt));
      ASSERT_EQ(evt.conversationType, ConversationType_Outgoing);
   }
   assertConversationEnded_ex(alice, aliceCall, ConversationEndReason_ServerRejected, [](const ConversationEndedEvent& evt){
      ASSERT_TRUE( evt.sipResponseCode == 480 || evt.sipResponseCode == 404 );
   });
}


TEST_F(BasicCallTests, BasicCallToUnresponsiveDomain) {
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.reRegisterOnResponseTypes.clear();
   alice.enable();

   // make an outgoing (audio only) call from Alice to an unknown address
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   //alice.conversation->addParticipant(aliceCall, "sip:<EMAIL>");
   alice.conversation->addParticipant(aliceCall, "sip:<EMAIL>");
   alice.conversation->start(aliceCall);
   {
      SipConversationHandle h;
      NewConversationEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
         "SipConversationHandler::onNewConversation",
         5000,
         HandleEqualsPred<SipConversationHandle>(aliceCall),
         h, evt));
      ASSERT_EQ(evt.conversationType, ConversationType_Outgoing);
   }
   {
      SipConversationHandle h;
      ConversationEndedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
         "SipConversationHandler::onConversationEnded",
         45000,
         HandleEqualsPred<SipConversationHandle>(aliceCall),
         h, evt));
      ASSERT_EQ(evt.endReason, ConversationEndReason_ServerRejected);
      ASSERT_TRUE(evt.sipResponseCode == 408);
   }
}

TEST_F(BasicCallTests, BasicCallToUnresponsiveDomainCancel) {
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.reRegisterOnResponseTypes.clear();
   alice.enable();

   // make an outgoing (audio only) call from Alice to an unknown address
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, "sip:<EMAIL>");
   alice.conversation->start(aliceCall);
   {
      SipConversationHandle h;
      NewConversationEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
         "SipConversationHandler::onNewConversation",
         5000,
         HandleEqualsPred<SipConversationHandle>(aliceCall),
         h, evt));
      ASSERT_EQ(evt.conversationType, ConversationType_Outgoing);
   }
   alice.conversation->end(aliceCall);
   {
      SipConversationHandle h;
      ConversationEndedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
         "SipConversationHandler::onConversationEnded",
         45000,
         HandleEqualsPred<SipConversationHandle>(aliceCall),
         h, evt));
      ASSERT_EQ(evt.endReason, ConversationEndReason_UserTerminatedLocally);
   }
}


TEST_F(BasicCallTests, BasicCallCancelBeforeAnswer) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor2(aliceEvents, bobEvents);
}

TEST_F(BasicCallTests, IncomingInviteWithResourcePriority) {
   TestAccount alice("alice", Account_Init);
   TestAccount bob("bob");

   class MySipConversationAdornmentHandler : public SipConversationAdornmentHandler
   {
   public:
      MySipConversationAdornmentHandler(CPCAPI2::SipConversation::SipConversationManager* convMgr) : convMgr(convMgr) {}

      virtual int onConversationAdornment(SipConversationHandle conversation, const ConversationAdornmentEvent& args)
      {
         CPCAPI2::SipHeader sipHeader;
         sipHeader.header = "Resource-Priority";
         sipHeader.value = "dsn.routine";
         cpc::vector<CPCAPI2::SipHeader> sipHeaders;
         sipHeaders.push_back(sipHeader);
         convMgr->adornMessage(conversation, args.adornmentMessageId, sipHeaders);
         return kSuccess;
      }

   private:
      CPCAPI2::SipConversation::SipConversationManager* convMgr;
   };
   MySipConversationAdornmentHandler adornmentHandler(alice.conversation);
   alice.conversation->setAdornmentHandler(alice.handle, &adornmentHandler);
   alice.enable();

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]() {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      SipConversationHandle bobCall;
      std::function<void(NewConversationEvent)> remoteResourcePriValidator = [](NewConversationEvent evt) { 
         ASSERT_EQ(evt.resourcePriority.size(), 1); 
         ASSERT_EQ(evt.resourcePriority[0], "dsn.routine");
      };
      TestCallEvents::expectNewConversationIncoming(__LINE__, bob, &bobCall, alice.config.uri(), remoteResourcePriValidator);
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor2(aliceEvents, bobEvents);
}

TEST_F(BasicCallTests, IncomingInviteWithHistoryInfo) {
   TestAccount alice("alice", Account_Init);
   TestAccount bob("bob");

   class MySipConversationAdornmentHandler : public SipConversationAdornmentHandler
   {
   public:
      MySipConversationAdornmentHandler(CPCAPI2::SipConversation::SipConversationManager* convMgr) : convMgr(convMgr) {}

      virtual int onConversationAdornment(SipConversationHandle conversation, const ConversationAdornmentEvent& args)
      {
         cpc::vector<CPCAPI2::SipHeader> sipHeaders;
         CPCAPI2::SipHeader sipHeader;
         sipHeader.header = "History-Info";
         sipHeader.value = "<sip:<EMAIL>?Reason=SIP%3Bcause%3D302>;index=1, <sip:<EMAIL>;user=phone;cause=302>;index=1.1";
         sipHeaders.push_back(sipHeader);
         convMgr->adornMessage(conversation, args.adornmentMessageId, sipHeaders);
         return kSuccess;
      }

   private:
      CPCAPI2::SipConversation::SipConversationManager* convMgr;
   };
   MySipConversationAdornmentHandler adornmentHandler(alice.conversation);
   alice.conversation->setAdornmentHandler(alice.handle, &adornmentHandler);
   alice.enable();

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]() {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      SipConversationHandle bobCall;
      std::function<void(NewConversationEvent)> remoteHistInfoValidator = [](NewConversationEvent evt) {
         ASSERT_EQ(evt.historyInfo.size(), 2);
         ASSERT_EQ(evt.historyInfo[0].remoteAddress, "sip:<EMAIL>");
         ASSERT_EQ(evt.historyInfo[0].reason, "SIP;cause=302");
         ASSERT_EQ(evt.historyInfo[1].remoteAddress, "sip:<EMAIL>");
         ASSERT_EQ(evt.historyInfo[1].reason, "");
      };
      TestCallEvents::expectNewConversationIncoming(__LINE__, bob, &bobCall, alice.config.uri(), remoteHistInfoValidator);
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor2(aliceEvents, bobEvents);
}

void BasicCallTests_IncomingInviteWithPCalledPartyId(const cpc::string& pCalledPatyIdHeaderValue, const std::function<void(NewConversationEvent)> pCalledPartyIdValidator)
{
   TestAccount alice("alice", Account_Init);
   TestAccount bob("bob");

   class MySipConversationAdornmentHandler : public SipConversationAdornmentHandler
   {
   public:
      MySipConversationAdornmentHandler(CPCAPI2::SipConversation::SipConversationManager* convMgr, const cpc::string& headerValue) :
         convMgr(convMgr), m_pCalledPatyIdHeaderValue(headerValue) {}

      cpc::string m_pCalledPatyIdHeaderValue;

      virtual int onConversationAdornment(SipConversationHandle conversation, const ConversationAdornmentEvent& args)
      {
         if (!m_pCalledPatyIdHeaderValue.empty())
         {
            cpc::vector<CPCAPI2::SipHeader> sipHeaders;
            CPCAPI2::SipHeader sipHeader;
            sipHeader.header = "P-Called-Party-ID";
            sipHeader.value = m_pCalledPatyIdHeaderValue;
            sipHeaders.push_back(sipHeader);
            convMgr->adornMessage(conversation, args.adornmentMessageId, sipHeaders);
         }
         return kSuccess;
      }

   private:
      CPCAPI2::SipConversation::SipConversationManager* convMgr;
   };
   MySipConversationAdornmentHandler adornmentHandler(alice.conversation, pCalledPatyIdHeaderValue);
   alice.conversation->setAdornmentHandler(alice.handle, &adornmentHandler);
   alice.enable();

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]() {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      SipConversationHandle bobCall;
      TestCallEvents::expectNewConversationIncoming(__LINE__, bob, &bobCall, alice.config.uri(), pCalledPartyIdValidator);
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor2(aliceEvents, bobEvents);
}

TEST_F(BasicCallTests, IncomingInviteWithPCalledPartyId) {

   {
      std::function<void(NewConversationEvent)> pCalledPartyIdValidator = [](NewConversationEvent evt)
      {
         ASSERT_EQ(evt.pCalledPartyIdAddress, "sip:<EMAIL>");
         ASSERT_EQ(evt.pCalledPartyIdDisplayname, "DisplayName");
      };
      
      BasicCallTests_IncomingInviteWithPCalledPartyId("\"DisplayName\"<sip:<EMAIL>>", pCalledPartyIdValidator);
   }
   
   {
      std::function<void(NewConversationEvent)> pCalledPartyIdValidator = [](NewConversationEvent evt)
      {
         ASSERT_EQ(evt.pCalledPartyIdAddress, "sip:<EMAIL>");
         ASSERT_EQ(evt.pCalledPartyIdDisplayname, "");
      };
      
      BasicCallTests_IncomingInviteWithPCalledPartyId("<sip:<EMAIL>>", pCalledPartyIdValidator);
   }
   
   {
      std::function<void(NewConversationEvent)> pCalledPartyIdValidator = [](NewConversationEvent evt)
      {
         ASSERT_EQ(evt.pCalledPartyIdAddress, "tel:+60388888888");
         ASSERT_EQ(evt.pCalledPartyIdDisplayname, "");
      };
      
      BasicCallTests_IncomingInviteWithPCalledPartyId("<tel:+60388888888>", pCalledPartyIdValidator);
   }
   
   {
      std::function<void(NewConversationEvent)> pCalledPartyIdValidator = [](NewConversationEvent evt)
      {
         ASSERT_EQ(evt.pCalledPartyIdAddress, "sip:<EMAIL>");
         ASSERT_EQ(evt.pCalledPartyIdDisplayname, "John Doe");
      };
      
      BasicCallTests_IncomingInviteWithPCalledPartyId("\"John Doe\"<sip:<EMAIL>>;a=b", pCalledPartyIdValidator);
   }
   
   {
      std::function<void(NewConversationEvent)> pCalledPartyIdValidator = [](NewConversationEvent evt)
      {
         ASSERT_EQ(evt.pCalledPartyIdAddress, "");
         ASSERT_EQ(evt.pCalledPartyIdDisplayname, "");
      };
      
      BasicCallTests_IncomingInviteWithPCalledPartyId("", pCalledPartyIdValidator);
   }
}


// This unit test only works with an external proxy server e.g. demo.xten.com

// .jza. this unit test currently appears to NOT work, when enabled, since the SDK
// binds to 127.0.0.1 and tries to send out STUN packets from this interface to stun.counterpath.com
// (which fails)
TEST_F(BasicCallTests, DISABLED_BasicCallCallerEndsUsingStun) {
   TestAccount alice("alice", Account_NoInit);
   TestAccount bob("bob", Account_NoInit);

   alice.init();
   bob.init();

   SipConversationSettings settings;
   settings.natTraversalMode = NatTraversalMode_STUN;
   settings.natTraversalServerSource = NatTraversalServerSource_Custom;
   settings.natTraversalServer = "stun.counterpath.com";
   alice.conversation->setDefaultSettings(alice.handle, settings);
   bob.conversation->setDefaultSettings(bob.handle, settings);

   alice.enable();
   bob.enable();

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]() {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor2(aliceEvents, bobEvents);
}

// This unit test only works with a domain for which a SRV record is available e.g. counterpath.com
TEST_F(BasicCallTests, DISABLED_BasicCallCallerEndsUsingStunViaDnsSrv) {
   TestAccount alice("alice", Account_NoInit);
   TestAccount bob("bob", Account_NoInit);

   alice.init();
   bob.init();

   SipConversationSettings settings;
   settings.natTraversalMode = NatTraversalMode_STUN;
   settings.natTraversalServerSource = NatTraversalServerSource_SRV;
   alice.conversation->setDefaultSettings(alice.handle, settings);
   bob.conversation->setDefaultSettings(bob.handle, settings);

   alice.enable();
   bob.enable();

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor2(aliceEvents, bobEvents);
}

// This unit test only works with an external proxy server e.g. demo.xten.com
TEST_F(BasicCallTests, DISABLED_BasicCallCallerEndsUsingPAssertedIdentity) {
   TestAccount alice("alice", Account_Init);
   TestAccount bob("bob");

   class MySipConversationAdornmentHandler : public SipConversationAdornmentHandler
   {
   public:
      MySipConversationAdornmentHandler(CPCAPI2::SipConversation::SipConversationManager* convMgr) : convMgr(convMgr) {}

      virtual int onConversationAdornment(SipConversationHandle conversation, const ConversationAdornmentEvent& args)
      {
         CPCAPI2::SipHeader sipHeader;
         sipHeader.header = "P-Asserted-Identity";
         sipHeader.value = "\"Alice Smith\" <sip:<EMAIL>>";
         cpc::vector<CPCAPI2::SipHeader> sipHeaders;
         sipHeaders.push_back(sipHeader);
         convMgr->adornMessage(conversation, args.adornmentMessageId, sipHeaders);
         return kSuccess;
      }

   private:
      CPCAPI2::SipConversation::SipConversationManager* convMgr;
   };
   MySipConversationAdornmentHandler adornmentHandler(alice.conversation);
   alice.conversation->setAdornmentHandler(alice.handle, &adornmentHandler);
   alice.enable();

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall;
      std::function<void(NewConversationEvent)> remoteDisplayNameValidator = [] (NewConversationEvent evt) { ASSERT_EQ(evt.remoteDisplayName, "Alice Smith"); };
      TestCallEvents::expectNewConversationIncoming(__LINE__, bob, &bobCall, "sip:<EMAIL>", remoteDisplayNameValidator);
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor2(aliceEvents, bobEvents);
}

TEST_F(BasicCallTests, DISABLED_ApplySettingsAfterCallStarted) {
   TestAccount alice("alice", Account_Init);
   TestAccount bob("bob");

   class MySipConversationAdornmentHandler : public SipConversationAdornmentHandler
   {
   public:
      bool mAdornmentCalled;
      MySipConversationAdornmentHandler(CPCAPI2::SipConversation::SipConversationManager* convMgr) :
            mAdornmentCalled(false),
            convMgr(convMgr) {}

      virtual int onConversationAdornment(SipConversationHandle conversation, const ConversationAdornmentEvent& args)
      {
         mAdornmentCalled = true;
         return kSuccess;
      }

   private:
      CPCAPI2::SipConversation::SipConversationManager* convMgr;
   };
   MySipConversationAdornmentHandler adornmentHandler(alice.conversation);
   alice.conversation->setAdornmentHandler(alice.handle, &adornmentHandler);
   alice.enable();

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   // OBELISK-3314 -- seen in the field from and SDK customer -- calling applySettings just after
   // starting a conversation resulted in no adornment callback firing.
   // note that in their case, they appeared to be calling configureDefaultAccountSettings / configureTransportAccountSettings
   // the second time with identical settings, so it's not clear why applySettings(..) in their case was
   // causing the account to disable/enable. Could possibly be attributed to the Java language wrapper
   // resulting in slightly different C++ account settings objects on the second time through.

   alice.config.settings.password = "123";
   alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings);
   alice.account->applySettings(alice.handle);

   auto aliceEvents = std::async(std::launch::async, [&]() {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      ASSERT_TRUE(adornmentHandler.mAdornmentCalled);
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      SipConversationHandle bobCall;
      std::function<void(NewConversationEvent)> remoteDisplayNameValidator = [](NewConversationEvent evt) {};
      TestCallEvents::expectNewConversationIncoming(__LINE__, bob, &bobCall, alice.config.uri(), remoteDisplayNameValidator);
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor2(aliceEvents, bobEvents);
}

TEST_F(BasicCallTests, BestEffortSRTP_SAVPCall)
{
   TestAccount bob("bob");
   TestAccount charlie("charlie");

   // Make a call with media encryption set to 'Best Effort'
   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall, charlie.config.uri());
   bob.conversation->setBestEffortMediaEncryption(bobCall, true);
   bob.conversation->start(bobCall);
   auto bobConversationEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(bob, bobCall, charlie.config.uri());
      assertConversationStateChanged(bob, bobCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertConversationEnded_time(bob, bobCall, ConversationEndReason_UserTerminatedRemotely, 30000);
   });

   auto charlieConversationEvents = std::async(std::launch::async, [&] () {
      // UA media encryption is set to 'Always' so accept the SAVP incoming call
      SipConversationHandle charlieCall;
      std::function<void(NewConversationEvent)> mediaInfoValidator = [] (NewConversationEvent evt) {
         // Expect SAVP request from incoming call
         ASSERT_EQ(MediaEncryptionMode_SRTP_SDES_Encrypted, evt.remoteMediaInfo[0].mediaEncryptionOptions.mediaEncryptionMode);
         ASSERT_TRUE(evt.remoteMediaInfo[0].mediaEncryptionOptions.secureMediaRequired);
      };
      assertNewConversationIncoming_ex(charlie, &charlieCall, bob.config.uri(), mediaInfoValidator);
      charlie.conversation->sendRingingResponse(charlieCall);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_LocalRinging);
      charlie.conversation->accept(charlieCall);
      assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_Connected);

      TestCallEvents::expectRTCP( __LINE__, charlie, charlieCall );

      charlie.conversation->end(charlieCall);
      assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedLocally);
   });
   waitFor2(bobConversationEvents, charlieConversationEvents);
}

TEST_F(BasicCallTests, BestEffortSRTP_AVPCall)
{
   TestAccount bob("bob");
   TestAccount charlie("charlie");

   // Make a call with media encryption set to 'Best Effort'
   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall, charlie.config.uri());
   bob.conversation->setBestEffortMediaEncryption(bobCall, true);
   bob.conversation->start(bobCall);
   auto bobConversationEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(bob, bobCall, charlie.config.uri());
      assertConversationStateChanged(bob, bobCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   auto charlieConversationEvents = std::async(std::launch::async, [&] () {
      // UA media encryption is set to 'None' so reject the incoming call with 488/Not Acceptable Here response
      // since it should be SAVP
      SipConversationHandle charlieCall;
      std::function<void(NewConversationEvent)> mediaInfoValidator = [] (NewConversationEvent evt) {
         // Expect SAVP request from incoming call
         ASSERT_EQ(MediaEncryptionMode_SRTP_SDES_Encrypted, evt.remoteMediaInfo[0].mediaEncryptionOptions.mediaEncryptionMode);
         ASSERT_TRUE(evt.remoteMediaInfo[0].mediaEncryptionOptions.secureMediaRequired);
      };
      assertNewConversationIncoming_ex(charlie, &charlieCall, bob.config.uri(), mediaInfoValidator);
      charlie.conversation->reject(charlieCall, 488);
      assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedLocally);

      // Now expect the second incoming call to be AVP
      SipConversationHandle charlieCall2;
      std::function<void(NewConversationEvent)> mediaInfoValidator2 = [] (NewConversationEvent evt) {
         // Expect SAVP request from incoming call
         ASSERT_EQ(MediaEncryptionMode_Unencrypted, evt.remoteMediaInfo[0].mediaEncryptionOptions.mediaEncryptionMode);
         ASSERT_FALSE(evt.remoteMediaInfo[0].mediaEncryptionOptions.secureMediaRequired);
      };
      assertNewConversationIncoming_ex(charlie, &charlieCall2, bob.config.uri(), mediaInfoValidator2);
      charlie.conversation->sendRingingResponse(charlieCall2);
      assertConversationStateChanged(charlie, charlieCall2, ConversationState_LocalRinging);
      charlie.conversation->accept(charlieCall2);
      assertConversationMediaChanged(charlie, charlieCall2, MediaDirection_SendReceive);
      assertConversationStateChanged(charlie, charlieCall2, ConversationState_Connected);
      charlie.conversation->end(charlieCall2);
      assertConversationEnded(charlie, charlieCall2, ConversationEndReason_UserTerminatedLocally);
   });
   waitFor2(bobConversationEvents, charlieConversationEvents);
}

TEST_F(BasicCallTests, TwoSimultaneousCalls)
{
   TestAccount alice("alice");
   TestAccount bob("bob");
   TestAccount charlie("charlie");

   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall, alice.config.uri());
   bob.conversation->start(bobCall);
   auto bobConversationEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(bob, bobCall, alice.config.uri());
      assertConversationStateChanged(bob, bobCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   // Wait a bit before making the second call to alice
   std::this_thread::sleep_for(std::chrono::milliseconds(500));

   SipConversationHandle charlieCall = charlie.conversation->createConversation(charlie.handle);
   charlie.conversation->addParticipant(charlieCall, alice.config.uri());
   charlie.conversation->start(charlieCall);
   auto charlieConversationEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(charlie, charlieCall, alice.config.uri());
      assertConversationStateChanged(charlie, charlieCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_Connected);
      assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedRemotely);
   });

   auto aliceConversationEvents = std::async(std::launch::async, [&] () {
      // Receive call from bob
      SipConversationHandle aliceCall1;
      assertNewConversationIncoming(alice, &aliceCall1, bob.config.uri());
      alice.conversation->sendRingingResponse(aliceCall1);
      assertConversationStateChanged(alice, aliceCall1, ConversationState_LocalRinging);

      // Receive call from charlie
      SipConversationHandle aliceCall2;
      assertNewConversationIncoming(alice, &aliceCall2, charlie.config.uri());
      alice.conversation->sendRingingResponse(aliceCall2);
      assertConversationStateChanged(alice, aliceCall2, ConversationState_LocalRinging);

      // Accept call from bob
      alice.conversation->accept(aliceCall1);
      assertConversationMediaChanged(alice, aliceCall1, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall1, ConversationState_Connected);

      // Now accept call from charlie
      alice.conversation->accept(aliceCall2);
      assertConversationMediaChanged(alice, aliceCall2, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall2, ConversationState_Connected);

      // Wait a bit before tearing down the two calls
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      // Terminate both conversations
      alice.conversation->end(aliceCall1);
      assertConversationEnded(alice, aliceCall1, ConversationEndReason_UserTerminatedLocally);
      alice.conversation->end(aliceCall2);
      assertConversationEnded(alice, aliceCall2, ConversationEndReason_UserTerminatedLocally);
   });
   waitFor3(bobConversationEvents, charlieConversationEvents, aliceConversationEvents);
}

#ifndef ANDROID
TEST_F(BasicCallTests, BasicCallWith491Response) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendOnly);
      assertSuccess(alice.conversation->reject(aliceCall, 491)); // Send a 491 SIP response back to force the remote endpoint's SIP stack to automatically resend the re-INVITE
      assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendOnly);
      assertSuccess(alice.conversation->accept(aliceCall));
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_FALSE(evt.localHold);
         ASSERT_TRUE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_ReceiveOnly, evt.localMediaInfo[0].mediaDirection);
      });

      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertSuccess(bob.conversation->hold(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_TRUE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendOnly, evt.localMediaInfo[0].mediaDirection);
      });
      std::this_thread::sleep_for(std::chrono::milliseconds(500));

      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor2(aliceEvents, bobEvents);
}
#endif //ANDROID

TEST_F(BasicCallTests, BasicCallCallerEndsCustomPortRange) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   SipConversationSettings settings;
   settings.minRtpPort = 7000;
   settings.maxRtpPort = 7100;
   settings.minRtpPortAudio = 57000;
   settings.maxRtpPortAudio = 57100;
   settings.minRtpPortVideo = 47000;
   settings.maxRtpPortVideo = 47100;
   alice.conversation->setDefaultSettings(alice.handle, settings);
   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor2(aliceEvents, bobEvents);
}

TEST_F(BasicCallTests, BasicUriUnescapeTest)
{
   TestAccount alice("alice");

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, "sip:<*#-_.!~*(')>@demo.xten.com");
   alice.conversation->start(aliceCall);

   assertNewConversationOutgoing_ex(alice, aliceCall, "sip:<*#-_.!~*(')>@demo.xten.com", [](const NewConversationEvent& evt){
      ASSERT_EQ(evt.remoteAddress, "sip:<*#-_.!~*(')>@demo.xten.com");
   });
   assertSuccess(alice.conversation->end(aliceCall));
   //assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
}

TEST_F(BasicCallTests, DecodeProvisioning)
{
   TestAccount alice("alice");

   alice.conversation->createConversation(alice.handle);

   std::ifstream in((TestEnvironmentConfig::testResourcePath() + "provisioningtests.json").c_str());
   assert(in.is_open());

   std::ostringstream iss;
   iss << in.rdbuf() << std::flush;

   cpc::string doc = iss.str().c_str();
   cpc::vector<SipConversationSettings> convs;

   alice.conversation->decodeProvisioningResponse(doc, convs);

   ASSERT_EQ(convs[0].sessionName, "foo");
   ASSERT_EQ(convs[0].natTraversalMode, NatTraversalMode_STUN);
   ASSERT_EQ(convs[0].natTraversalServerSource, NatTraversalServerSource_SRV);
   ASSERT_EQ(convs[0].natTraversalServer, "Bar");
   ASSERT_EQ(convs[0].holdMode, HoldMode_RFC2543);
   ASSERT_EQ(convs[0].prackMode, PrackMode_SupportUasAndUac);
   ASSERT_EQ(convs[0].minRtpPort, 100);
   ASSERT_EQ(convs[0].maxRtpPort, 10000);
   ASSERT_EQ(convs[0].minRtpPortAudio, 200);
   ASSERT_EQ(convs[0].maxRtpPortAudio, 20000);
   ASSERT_EQ(convs[0].minRtpPortVideo, 30);
   ASSERT_EQ(convs[0].maxRtpPortVideo, 300);
   ASSERT_EQ(convs[0].turnUsername, "pete");
   ASSERT_EQ(convs[0].turnPassword, "007");
   ASSERT_EQ(convs[0].includePPreferredIdentity, true);
   ASSERT_EQ(convs[0].includePAssertedIdentity, true);
   ASSERT_EQ(convs[0].includeAttribsForStaticPLs, true);
}

TEST_F(BasicCallTests, BasicCallNoRegistrar)
{
   TestAccount bob("bob", Account_NoInit);
   bob.config.settings.useRegistrar = false;
   bob.config.settings.domain = "bogus.invalid";
   bob.config.settings.outboundProxy = "";
   bob.init();
   bob.enable();


   TestAccount charlie("charlie", Account_NoInit);

   resip::Uri charlieTarget;
   charlieTarget.user() = charlie.config.settings.username;
   charlieTarget.host() = "127.0.0.1";
   charlieTarget.port() = 5055;

   charlie.config.settings.minSipPort = charlieTarget.port();
   charlie.config.settings.maxSipPort = charlieTarget.port();
   charlie.init();
   charlie.enable();

   // Bob calls Charlie then Charlie hangs up
   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);

   bob.conversation->addParticipant(bobCall, charlieTarget.getAOR(true).c_str());
   bob.conversation->start(bobCall);
   auto bobConversationEvents = std::async(std::launch::async, [&] () {
      // Wait for the expected state changes and for the call to end

      SipConversationHandle h;
      NewConversationEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
         "SipConversationHandler::onNewConversation",
         5000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(evt.account, bob.handle);

      assertConversationStateChanged(bob, bobCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });
   auto charlieConversationEvents = std::async(std::launch::async, [&] () {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle charlieCall;
      assertNewConversationIncoming(charlie, &charlieCall, bob.config.uri());
      charlie.conversation->sendRingingResponse(charlieCall);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_LocalRinging);
      charlie.conversation->accept(charlieCall);
      assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_Connected);
      charlie.conversation->end(charlieCall);
      assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedLocally);
   });
   waitFor2(bobConversationEvents, charlieConversationEvents);
}

TEST_F(BasicCallTests, BasicCallNoRegistrarTCP)
{
   TestAccount bob("bob", Account_NoInit);
   bob.config.settings.useRegistrar = false;
   bob.config.settings.domain = "bogus.invalid";
   bob.config.settings.outboundProxy = "";
   bob.config.settings.sipTransportType = SipAccountTransport_TCP;
   bob.init();
   bob.enable();


   TestAccount charlie("charlie", Account_NoInit);

   resip::Uri charlieTarget;
   charlieTarget.user() = charlie.config.settings.username;
   charlieTarget.host() = "127.0.0.1";
   charlieTarget.port() = 5055;

   charlie.config.settings.sipTransportType = SipAccountTransport_TCP;
   charlie.config.settings.minSipPort = charlieTarget.port();
   charlie.config.settings.maxSipPort = charlieTarget.port();
   charlie.config.settings.useRport = false;
   charlie.init();
   charlie.enable();

   // Bob calls Charlie then Charlie hangs up
   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);

   bob.conversation->addParticipant(bobCall, charlieTarget.getAOR(true).c_str());
   bob.conversation->start(bobCall);
   auto bobConversationEvents = std::async(std::launch::async, [&] () {
      // Wait for the expected state changes and for the call to end

      SipConversationHandle h;
      NewConversationEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
         "SipConversationHandler::onNewConversation",
         5000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(evt.account, bob.handle);

      assertConversationStateChanged(bob, bobCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      // note we need to end it from bob; if we end from Charlie, the BYE will fail as
      // bob does not have a port in his Contact header.
      bob.conversation->end(bobCall);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });
   auto charlieConversationEvents = std::async(std::launch::async, [&] () {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle charlieCall;
      assertNewConversationIncoming(charlie, &charlieCall, bob.config.uri());
      charlie.conversation->sendRingingResponse(charlieCall);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_LocalRinging);
      charlie.conversation->accept(charlieCall);
      assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_Connected);
      assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedRemotely);
   });
   waitFor2(bobConversationEvents, charlieConversationEvents);
}

TEST_F(BasicCallTests, BasicCallBothSidesNoRegistrarTCP)
{
   TestAccount bob("bob", Account_NoInit);
   bob.config.settings.useRegistrar = false;
   bob.config.settings.domain = "bogus.invalid";
   bob.config.settings.outboundProxy = "";
   bob.config.settings.sipTransportType = SipAccountTransport_TCP;
   bob.init();
   bob.enable();


   TestAccount charlie("charlie", Account_NoInit);

   resip::Uri charlieTarget;
   charlieTarget.user() = charlie.config.settings.username;
   charlieTarget.host() = "127.0.0.1";
   charlieTarget.port() = 5055;

   charlie.config.settings.sipTransportType = SipAccountTransport_TCP;
   charlie.config.settings.minSipPort = charlieTarget.port();
   charlie.config.settings.maxSipPort = charlieTarget.port();
   charlie.config.settings.useRegistrar = false;
   charlie.init();
   charlie.enable();

   // Bob calls Charlie then Charlie hangs up
   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);

   bob.conversation->addParticipant(bobCall, charlieTarget.getAOR(true).c_str());
   bob.conversation->start(bobCall);
   auto bobConversationEvents = std::async(std::launch::async, [&] () {
      // Wait for the expected state changes and for the call to end

      SipConversationHandle h;
      NewConversationEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
         "SipConversationHandler::onNewConversation",
         5000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(evt.account, bob.handle);

      assertConversationStateChanged(bob, bobCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      // note we need to end it from bob; if we end from Charlie, the BYE will fail as
      // bob does not have a port in his Contact header.
      bob.conversation->end(bobCall);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });
   auto charlieConversationEvents = std::async(std::launch::async, [&] () {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle charlieCall;
      assertNewConversationIncoming(charlie, &charlieCall, bob.config.uri());
      charlie.conversation->sendRingingResponse(charlieCall);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_LocalRinging);
      charlie.conversation->accept(charlieCall);
      assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_Connected);
      assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedRemotely);
   });
   waitFor2(bobConversationEvents, charlieConversationEvents);
}

TEST_F(BasicCallTests, BasicCallBothSidesNoRegistrarUDP)
{
   TestAccount bob("bob", Account_NoInit);
   bob.config.settings.useRegistrar = false;
   bob.config.settings.domain = "bogus.invalid";
   bob.config.settings.outboundProxy = "";
   bob.config.settings.sipTransportType = SipAccountTransport_UDP;
   bob.init();
   bob.enable();


   TestAccount charlie("charlie", Account_NoInit);

   resip::Uri charlieTarget;
   charlieTarget.user() = charlie.config.settings.username;
   charlieTarget.host() = "127.0.0.1";
   charlieTarget.port() = 5055;

   charlie.config.settings.sipTransportType = SipAccountTransport_UDP;
   charlie.config.settings.minSipPort = charlieTarget.port();
   charlie.config.settings.maxSipPort = charlieTarget.port();
   charlie.config.settings.useRegistrar = false;
   charlie.init();
   charlie.enable();

   // Bob calls Charlie then Charlie hangs up
   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);

   bob.conversation->addParticipant(bobCall, charlieTarget.getAOR(true).c_str());
   bob.conversation->start(bobCall);
   auto bobConversationEvents = std::async(std::launch::async, [&]() {
      // Wait for the expected state changes and for the call to end

      SipConversationHandle h;
      NewConversationEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
         "SipConversationHandler::onNewConversation",
         5000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(evt.account, bob.handle);

      assertConversationStateChanged(bob, bobCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      // note we need to end it from bob; if we end from Charlie, the BYE will fail as
      // bob does not have a port in his Contact header.
      bob.conversation->end(bobCall);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });
   auto charlieConversationEvents = std::async(std::launch::async, [&]() {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle charlieCall;
      assertNewConversationIncoming(charlie, &charlieCall, bob.config.uri());
      charlie.conversation->sendRingingResponse(charlieCall);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_LocalRinging);
      charlie.conversation->accept(charlieCall);
      assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_Connected);
      assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedRemotely);
   });
   waitFor2(bobConversationEvents, charlieConversationEvents);
}

#if (defined(__APPLE__) && TARGET_OS_IPHONE == 1)
TEST_F(BasicCallTests, NoTCPListen)
{
   // OBELISK-4199

   TestAccount bob("bob", Account_NoInit);
   bob.config.settings.useRegistrar = false;
   bob.config.settings.domain = "bogus.invalid";
   bob.config.settings.outboundProxy = "";
   bob.config.settings.sipTransportType = SipAccountTransport_TCP;
   bob.init();
   bob.enable();


   TestAccount charlie("charlie", Account_NoInit);

   resip::Uri charlieTarget;
   charlieTarget.user() = charlie.config.settings.username;
   charlieTarget.host() = "127.0.0.1";
   charlieTarget.port() = 5055;

   charlie.config.settings.sipTransportType = SipAccountTransport_TCP;
   charlie.config.settings.minSipPort = charlieTarget.port();
   charlie.config.settings.maxSipPort = charlieTarget.port();

   // charlie should NOT be listening for incoming TCP connections
   charlie.config.settings.useRport = true;
   charlie.config.settings.useRegistrar = true;

   charlie.init();
   charlie.enable();

   // Bob calls Charlie then Charlie hangs up
   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);

   bob.conversation->addParticipant(bobCall, charlieTarget.getAOR(true).c_str());
   bob.conversation->start(bobCall);
   auto bobConversationEvents = std::async(std::launch::async, [&] () {

      SipConversationHandle h;
      ConversationEndedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
         "SipConversationHandler::onConversationEnded",
         5000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(h, bobCall);
      ASSERT_EQ(ConversationEndReason_ServerError, evt.endReason);
      ASSERT_EQ(503, evt.sipResponseCode);
   });
   waitFor(bobConversationEvents);
}
#endif

/* OBELISK-3287 - Crash in resip::DialogUsageManager::makeUacDialogSet(...) function
 * Replicate crash that is caused by initiating call while account is disabling and SDK crashes
 * due to assert in DUM that checks if DUM is shutting down.
 *
 * All we need from this test is to catch conversation error event and confirm that SDK handles
 * faulty behavior before it causes crash.
 */
TEST_F(BasicCallTests, BasicCallWhileDisabling)
{
   TestAccount bob("bob");
   TestAccount charlie("charlie");

   // Bob calls Charlie then Charlie hangs up
   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall, charlie.config.uri());
   bob.config.settings.password = "123";
   bob.account->configureDefaultAccountSettings(bob.handle, bob.config.settings);
   bob.account->applySettings(bob.handle);
   bob.conversation->start(bobCall);

   auto bobConversationEvents = std::async(std::launch::async, [&]() {
      assertConversationError(bob, bobCall);
   });
   waitFor(bobConversationEvents);
}

/* OBELISK-2027 - SDK gracefully handling malformed SIP headers
 * Make sure SDK handles even malformed Call-Info headers that contain "answer-after=0" parameter.
 */
TEST_F(BasicCallTests, BasicCallWithCallInfo)
{
   TestAccount bob("bob", Account_Init);
   TestAccount charlie("charlie");

   class MySipConversationAdornmentHandler : public SipConversationAdornmentHandler
   {
   public:
      MySipConversationAdornmentHandler(CPCAPI2::SipConversation::SipConversationManager* convMgr) : convMgr(convMgr) {}

      virtual int onConversationAdornment(SipConversationHandle conversation, const ConversationAdornmentEvent& args)
      {
         CPCAPI2::SipHeader sipHeader;
         sipHeader.header = "Call-Info";
         sipHeader.value = "sip:;answer-after=0"; //invalid CallInfo value
         cpc::vector<CPCAPI2::SipHeader> sipHeaders;
         sipHeaders.push_back(sipHeader);
         convMgr->adornMessage(conversation, args.adornmentMessageId, sipHeaders);
         return kSuccess;
      }

   private:
      CPCAPI2::SipConversation::SipConversationManager* convMgr;
   };
   MySipConversationAdornmentHandler adornmentHandler(bob.conversation);
   bob.conversation->setAdornmentHandler(bob.handle, &adornmentHandler);
   bob.enable();
   // Bob calls Charlie then Charlie hangs up
   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall, charlie.config.uri());
   bob.conversation->start(bobCall);
   auto bobConversationEvents = std::async(std::launch::async, [&]() {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing(bob, bobCall, charlie.config.uri());
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });
   auto charlieConversationEvents = std::async(std::launch::async, [&]() {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle charlieCall;
      std::function<void(NewConversationEvent)> autoAnswerValidator = [](NewConversationEvent evt) { ASSERT_EQ(evt.autoAnswer, true); };
      TestCallEvents::expectNewConversationIncoming(__LINE__, charlie, &charlieCall, bob.config.uri(), autoAnswerValidator);
      charlie.conversation->accept(charlieCall);
      assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_Connected);
      charlie.conversation->end(charlieCall);
      assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedLocally);
   });
   waitFor2(bobConversationEvents, charlieConversationEvents);
}

TEST_F(BasicCallTests, BasicCallWithAnswerModeManual)
{
   TestAccount bob("bob", Account_NoInit);
   TestAccount charlie("charlie");

   bob.config.settings.answerModeSupported = true;
   bob.init();
   bob.enable();

   // Bob calls Charlie then Charlie hangs up
   SipConversationSettings conversationSettings;
   conversationSettings.answerMode.mode = SipConversation::AnswerMode_Manual;
   bob.conversation->setDefaultSettings(bob.handle, conversationSettings);
   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall, charlie.config.uri());
   bob.conversation->start(bobCall);
   auto bobConversationEvents = std::async(std::launch::async, [&]()
   {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing_answermode(bob, bobCall, charlie.config.uri(), conversationSettings.answerMode, NULL);
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged_answermode(bob, bobCall, ConversationState_RemoteRinging, conversationSettings.answerMode, NULL);
      assertConversationStateChanged_answermode(bob, bobCall, ConversationState_Connected, conversationSettings.answerMode, NULL);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   auto charlieConversationEvents = std::async(std::launch::async, [&]()
   {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle charlieCall;
      assertNewConversationIncoming_answermode(charlie, &charlieCall, bob.config.uri(), conversationSettings.answerMode, NULL);
      charlie.conversation->sendRingingResponse(charlieCall);
      assertConversationStateChanged_answermode(charlie, charlieCall, ConversationState_LocalRinging, conversationSettings.answerMode, NULL);
      charlie.conversation->accept(charlieCall);

      assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);
      assertConversationStateChanged_answermode(charlie, charlieCall, ConversationState_Connected, conversationSettings.answerMode, NULL);
      charlie.conversation->end(charlieCall);
      assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor2(bobConversationEvents, charlieConversationEvents);
}

TEST_F(BasicCallTests, BasicCallWithAnswerModeManualRequired)
{
   TestAccount bob("bob", Account_NoInit);
   TestAccount charlie("charlie");

   bob.config.settings.answerModeSupported = true;
   bob.init();
   bob.enable();

   // Bob calls Charlie then Charlie hangs up
   SipConversationSettings conversationSettings;
   conversationSettings.answerMode.mode = SipConversation::AnswerMode_Manual;
   conversationSettings.answerMode.required = true;
   bob.conversation->setDefaultSettings(bob.handle, conversationSettings);
   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall, charlie.config.uri());
   bob.conversation->start(bobCall);
   auto bobConversationEvents = std::async(std::launch::async, [&]()
   {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing_answermode(bob, bobCall, charlie.config.uri(), conversationSettings.answerMode, NULL);
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged_answermode(bob, bobCall, ConversationState_RemoteRinging, conversationSettings.answerMode, NULL);
      assertConversationStateChanged_answermode(bob, bobCall, ConversationState_Connected, conversationSettings.answerMode, NULL);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   auto charlieConversationEvents = std::async(std::launch::async, [&]()
   {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle charlieCall;
      assertNewConversationIncoming_answermode(charlie, &charlieCall, bob.config.uri(), conversationSettings.answerMode, NULL);
      charlie.conversation->sendRingingResponse(charlieCall);
      assertConversationStateChanged_answermode(charlie, charlieCall, ConversationState_LocalRinging, conversationSettings.answerMode, NULL);
      charlie.conversation->accept(charlieCall);

      assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);
      assertConversationStateChanged_answermode(charlie, charlieCall, ConversationState_Connected, conversationSettings.answerMode, NULL);
      charlie.conversation->end(charlieCall);
      assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor2(bobConversationEvents, charlieConversationEvents);
}

TEST_F(BasicCallTests, BasicCallWithAnswerModeManualPrivileged)
{
   TestAccount bob("bob", Account_NoInit);
   TestAccount charlie("charlie");

   bob.config.settings.answerModeSupported = true;
   bob.init();
   bob.enable();

   // Bob calls Charlie then Charlie hangs up
   SipConversationSettings conversationSettings;
   conversationSettings.answerMode.mode = SipConversation::AnswerMode_Manual;
   conversationSettings.answerMode.privileged = true;
   bob.conversation->setDefaultSettings(bob.handle, conversationSettings);
   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall, charlie.config.uri());
   bob.conversation->start(bobCall);
   auto bobConversationEvents = std::async(std::launch::async, [&]()
   {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing_answermode(bob, bobCall, charlie.config.uri(), conversationSettings.answerMode, NULL);
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged_answermode(bob, bobCall, ConversationState_RemoteRinging, conversationSettings.answerMode, NULL);
      assertConversationStateChanged_answermode(bob, bobCall, ConversationState_Connected, conversationSettings.answerMode, NULL);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   auto charlieConversationEvents = std::async(std::launch::async, [&]()
   {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle charlieCall;
      assertNewConversationIncoming_answermode(charlie, &charlieCall, bob.config.uri(), conversationSettings.answerMode, NULL);
      charlie.conversation->sendRingingResponse(charlieCall);
      assertConversationStateChanged_answermode(charlie, charlieCall, ConversationState_LocalRinging, conversationSettings.answerMode, NULL);
      charlie.conversation->accept(charlieCall);

      assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);
      assertConversationStateChanged_answermode(charlie, charlieCall, ConversationState_Connected, conversationSettings.answerMode, NULL);
      charlie.conversation->end(charlieCall);
      assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor2(bobConversationEvents, charlieConversationEvents);
}

TEST_F(BasicCallTests, BasicCallWithAnswerModeManualPrivilegedRequired)
{
   TestAccount bob("bob", Account_NoInit);
   TestAccount charlie("charlie");

   bob.config.settings.answerModeSupported = true;
   bob.init();
   bob.enable();

   // Bob calls Charlie then Charlie hangs up
   SipConversationSettings conversationSettings;
   conversationSettings.answerMode.mode = SipConversation::AnswerMode_Manual;
   conversationSettings.answerMode.privileged = true;
   conversationSettings.answerMode.required = true;
   bob.conversation->setDefaultSettings(bob.handle, conversationSettings);
   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall, charlie.config.uri());
   bob.conversation->start(bobCall);
   auto bobConversationEvents = std::async(std::launch::async, [&]()
   {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing_answermode(bob, bobCall, charlie.config.uri(), conversationSettings.answerMode, NULL);
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged_answermode(bob, bobCall, ConversationState_RemoteRinging, conversationSettings.answerMode, NULL);
      assertConversationStateChanged_answermode(bob, bobCall, ConversationState_Connected, conversationSettings.answerMode, NULL);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   auto charlieConversationEvents = std::async(std::launch::async, [&]()
   {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle charlieCall;
      assertNewConversationIncoming_answermode(charlie, &charlieCall, bob.config.uri(), conversationSettings.answerMode, NULL);
      charlie.conversation->sendRingingResponse(charlieCall);
      assertConversationStateChanged_answermode(charlie, charlieCall, ConversationState_LocalRinging, conversationSettings.answerMode, NULL);
      charlie.conversation->accept(charlieCall);

      assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);
      assertConversationStateChanged_answermode(charlie, charlieCall, ConversationState_Connected, conversationSettings.answerMode, NULL);
      charlie.conversation->end(charlieCall);
      assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor2(bobConversationEvents, charlieConversationEvents);
}

TEST_F(BasicCallTests, BasicCallWithAnswerModeAuto)
{
   TestAccount bob("bob", Account_NoInit);
   TestAccount charlie("charlie", Account_NoInit);

   bob.config.settings.answerModeSupported = true;
   bob.init();
   bob.enable();

   charlie.config.settings.answerModeSupported = true;
   charlie.init();
   charlie.enable();

   SipConversationSettings conversationSettings;
   conversationSettings.answerMode.required = false;
   conversationSettings.answerMode.privileged = false;
   conversationSettings.answerMode.mode = SipConversation::AnswerMode_Auto;
   bob.conversation->setDefaultSettings(bob.handle, conversationSettings);

   // Bob calls Charlie who has blocked manual calls, e.g. auto-attendant or answering machine only
   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall, charlie.config.uri());
   bob.conversation->start(bobCall);
   auto bobConversationEvents = std::async(std::launch::async, [&]()
   {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing_answermode(bob, bobCall, charlie.config.uri(), conversationSettings.answerMode, NULL);
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged_answermode(bob, bobCall, ConversationState_RemoteRinging, conversationSettings.answerMode, NULL);
      assertConversationStateChanged_answermode(bob, bobCall, ConversationState_Connected, conversationSettings.answerMode, NULL);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   auto charlieConversationEvents = std::async(std::launch::async, [&]()
   {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle charlieCall;
      assertNewConversationIncoming_answermode(charlie, &charlieCall, bob.config.uri(), conversationSettings.answerMode, NULL);
      charlie.conversation->sendRingingResponse(charlieCall);
      assertConversationStateChanged_answermode(charlie, charlieCall, ConversationState_LocalRinging, conversationSettings.answerMode, NULL);
      charlie.conversation->accept(charlieCall);

      assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);
      assertConversationStateChanged_answermode(charlie, charlieCall, ConversationState_Connected, conversationSettings.answerMode, NULL);
      charlie.conversation->end(charlieCall);
      assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor2(bobConversationEvents, charlieConversationEvents);

   conversationSettings.answerMode.required = true;
   conversationSettings.answerMode.privileged = false;
   bob.conversation->setDefaultSettings(bob.handle, conversationSettings);

   SipConversationHandle bobCall2 = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall2, charlie.config.uri());
   bob.conversation->start(bobCall2);
   auto bobConversationEvents2 = std::async(std::launch::async, [&]()
   {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing_answermode(bob, bobCall2, charlie.config.uri(), conversationSettings.answerMode, NULL);
      assertConversationMediaChanged(bob, bobCall2, MediaDirection_SendReceive);
      assertConversationStateChanged_answermode(bob, bobCall2, ConversationState_RemoteRinging, conversationSettings.answerMode, NULL);
      assertConversationStateChanged_answermode(bob, bobCall2, ConversationState_Connected, conversationSettings.answerMode, NULL);
      assertConversationEnded(bob, bobCall2, ConversationEndReason_UserTerminatedRemotely);
   });

   auto charlieConversationEvents2 = std::async(std::launch::async, [&]()
   {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle charlieCall;
      assertNewConversationIncoming_answermode(charlie, &charlieCall, bob.config.uri(), conversationSettings.answerMode, NULL);
      charlie.conversation->sendRingingResponse(charlieCall);
      assertConversationStateChanged_answermode(charlie, charlieCall, ConversationState_LocalRinging, conversationSettings.answerMode, NULL);
      charlie.conversation->accept(charlieCall);

      assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);
      assertConversationStateChanged_answermode(charlie, charlieCall, ConversationState_Connected, conversationSettings.answerMode, NULL);
      charlie.conversation->end(charlieCall);
      assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor2(bobConversationEvents2, charlieConversationEvents2);

   conversationSettings.answerMode.required = false;
   conversationSettings.answerMode.privileged = true;
   bob.conversation->setDefaultSettings(bob.handle, conversationSettings);

   SipConversationHandle bobCall3 = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall3, charlie.config.uri());
   bob.conversation->start(bobCall3);
   auto bobConversationEvents3 = std::async(std::launch::async, [&]()
   {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing_answermode(bob, bobCall3, charlie.config.uri(), conversationSettings.answerMode, NULL);
      assertConversationMediaChanged(bob, bobCall3, MediaDirection_SendReceive);
      assertConversationStateChanged_answermode(bob, bobCall3, ConversationState_RemoteRinging, conversationSettings.answerMode, NULL);
      assertConversationStateChanged_answermode(bob, bobCall3, ConversationState_Connected, conversationSettings.answerMode, NULL);
      assertConversationEnded(bob, bobCall3, ConversationEndReason_UserTerminatedRemotely);
   });

   auto charlieConversationEvents3 = std::async(std::launch::async, [&]()
   {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle charlieCall;
      assertNewConversationIncoming_answermode(charlie, &charlieCall, bob.config.uri(), conversationSettings.answerMode, NULL);
      charlie.conversation->sendRingingResponse(charlieCall);
      assertConversationStateChanged_answermode(charlie, charlieCall, ConversationState_LocalRinging, conversationSettings.answerMode, NULL);
      charlie.conversation->accept(charlieCall);

      assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);
      assertConversationStateChanged_answermode(charlie, charlieCall, ConversationState_Connected, conversationSettings.answerMode, NULL);
      charlie.conversation->end(charlieCall);
      assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor2(bobConversationEvents3, charlieConversationEvents3);

   conversationSettings.answerMode.required = true;
   conversationSettings.answerMode.privileged = true;
   bob.conversation->setDefaultSettings(bob.handle, conversationSettings);

   SipConversationHandle bobCall4 = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall4, charlie.config.uri());
   bob.conversation->start(bobCall4);
   auto bobConversationEvents4 = std::async(std::launch::async, [&]()
   {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing_answermode(bob, bobCall4, charlie.config.uri(), conversationSettings.answerMode, NULL);
      assertConversationMediaChanged(bob, bobCall4, MediaDirection_SendReceive);
      assertConversationStateChanged_answermode(bob, bobCall4, ConversationState_RemoteRinging, conversationSettings.answerMode, NULL);
      assertConversationStateChanged_answermode(bob, bobCall4, ConversationState_Connected, conversationSettings.answerMode, NULL);
      assertConversationEnded(bob, bobCall4, ConversationEndReason_UserTerminatedRemotely);
   });

   auto charlieConversationEvents4 = std::async(std::launch::async, [&]()
   {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle charlieCall;
      assertNewConversationIncoming_answermode(charlie, &charlieCall, bob.config.uri(), conversationSettings.answerMode, NULL);
      charlie.conversation->sendRingingResponse(charlieCall);
      assertConversationStateChanged_answermode(charlie, charlieCall, ConversationState_LocalRinging, conversationSettings.answerMode, NULL);
      charlie.conversation->accept(charlieCall);

      assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);
      assertConversationStateChanged_answermode(charlie, charlieCall, ConversationState_Connected, conversationSettings.answerMode, NULL);
      charlie.conversation->end(charlieCall);
      assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor2(bobConversationEvents4, charlieConversationEvents4);
}

TEST_F(BasicCallTests, BasicCallWithAnswerModeManualRejected)
{
   TestAccount bob("bob", Account_NoInit);
   TestAccount charlie("charlie", Account_NoInit);

   bob.config.settings.answerModeSupported = true;
   bob.init();
   bob.enable();

   charlie.config.settings.answerModeSupported = true;
   charlie.init();
   charlie.enable();

   SipConversationSettings bobConversationSettings;
   bobConversationSettings.answerMode.mode = SipConversation::AnswerMode_Manual;
   bobConversationSettings.answerMode.required = true;
   bob.conversation->setDefaultSettings(bob.handle, bobConversationSettings);

   SipConversationSettings charlieConversationSettings;
   charlieConversationSettings.answerMode.allowManual = false;
   charlie.conversation->setDefaultSettings(charlie.handle, charlieConversationSettings);

   // Bob calls Charlie who has blocked manual calls, e.g. auto-attendant or answering machine only
   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall, charlie.config.uri());
   bob.conversation->start(bobCall);
   auto bobConversationEvents = std::async(std::launch::async, [&]()
   {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing_answermode(bob, bobCall, charlie.config.uri(), bobConversationSettings.answerMode, NULL);
      std::function<void(ConversationEndedEvent)> endedReasonValidator = [] (ConversationEndedEvent evt)
      {
         ASSERT_EQ(evt.sipResponseCode, 403);
         ASSERT_EQ(evt.signallingEndWarningCode, 399);
         ASSERT_EQ(evt.signallingEndWarning, "manual answer forbidden");
      };
      assertConversationEnded_ex(bob, bobCall, ConversationEndReason_ServerRejected, endedReasonValidator);
   });

   waitFor(bobConversationEvents);

   bobConversationSettings.answerMode.privileged = true;
   bob.conversation->setDefaultSettings(bob.handle, bobConversationSettings);

   SipConversationHandle bobCall2 = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall2, charlie.config.uri());
   bob.conversation->start(bobCall2);
   auto bobConversationEvents2 = std::async(std::launch::async, [&]()
   {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing_answermode(bob, bobCall2, charlie.config.uri(), bobConversationSettings.answerMode, NULL);
      std::function<void(ConversationEndedEvent)> endedReasonValidator = [] (ConversationEndedEvent evt)
      {
         ASSERT_EQ(evt.sipResponseCode, 403);
         ASSERT_EQ(evt.signallingEndWarningCode, 399);
         ASSERT_EQ(evt.signallingEndWarning, "manual answer forbidden");
      };
      assertConversationEnded_ex(bob, bobCall2, ConversationEndReason_ServerRejected, endedReasonValidator);
   });

   waitFor(bobConversationEvents2);
}

TEST_F(BasicCallTests, BasicCallWithAnswerModeAutoRejected)
{
   TestAccount bob("bob", Account_NoInit);
   TestAccount charlie("charlie", Account_NoInit);

   bob.config.settings.answerModeSupported = true;
   bob.init();
   bob.enable();

   charlie.config.settings.answerModeSupported = true;
   charlie.init();
   charlie.enable();

   SipConversationSettings bobConversationSettings;
   bobConversationSettings.answerMode.mode = SipConversation::AnswerMode_Auto;
   bobConversationSettings.answerMode.required = true;
   bob.conversation->setDefaultSettings(bob.handle, bobConversationSettings);

   SipConversationSettings charlieConversationSettings;
   charlieConversationSettings.answerMode.allowAuto = false;
   charlie.conversation->setDefaultSettings(charlie.handle, charlieConversationSettings);

   // Bob calls Charlie who has blocked manual calls, e.g. auto-attendant or answering machine only
   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall, charlie.config.uri());
   bob.conversation->start(bobCall);
   auto bobConversationEvents = std::async(std::launch::async, [&]()
   {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing_answermode(bob, bobCall, charlie.config.uri(), bobConversationSettings.answerMode, NULL);
      std::function<void(ConversationEndedEvent)> endedReasonValidator = [] (ConversationEndedEvent evt)
      {
         ASSERT_EQ(evt.sipResponseCode, 403);
         ASSERT_EQ(evt.signallingEndWarningCode, 399);
         ASSERT_EQ(evt.signallingEndWarning, "automatic answer forbidden");
      };
      assertConversationEnded_ex(bob, bobCall, ConversationEndReason_ServerRejected, endedReasonValidator);
   });

   waitFor(bobConversationEvents);

   bobConversationSettings.answerMode.privileged = true;
   bob.conversation->setDefaultSettings(bob.handle, bobConversationSettings);

   SipConversationHandle bobCall2 = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall2, charlie.config.uri());
   bob.conversation->start(bobCall2);
   auto bobConversationEvents2 = std::async(std::launch::async, [&]()
   {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing_answermode(bob, bobCall2, charlie.config.uri(), bobConversationSettings.answerMode, NULL);
      std::function<void(ConversationEndedEvent)> endedReasonValidator = [] (ConversationEndedEvent evt)
      {
         ASSERT_EQ(evt.sipResponseCode, 403);
         ASSERT_EQ(evt.signallingEndWarningCode, 399);
         ASSERT_EQ(evt.signallingEndWarning, "automatic answer forbidden");
      };
      assertConversationEnded_ex(bob, bobCall2, ConversationEndReason_ServerRejected, endedReasonValidator);
   });

   waitFor(bobConversationEvents2);
}

TEST_F(BasicCallTests, BasicCallWithAnswerModePrivilegedRejected)
{
   TestAccount bob("bob", Account_NoInit);
   TestAccount charlie("charlie", Account_NoInit);

   bob.config.settings.answerModeSupported = true;
   bob.init();
   bob.enable();

   charlie.config.settings.answerModeSupported = true;
   charlie.init();
   charlie.enable();

   SipConversationSettings bobConversationSettings;
   bobConversationSettings.answerMode.mode = SipConversation::AnswerMode_Manual;
   bobConversationSettings.answerMode.privileged = true;
   bobConversationSettings.answerMode.required = true;
   bob.conversation->setDefaultSettings(bob.handle, bobConversationSettings);

   SipConversationSettings charlieConversationSettings;
   charlieConversationSettings.answerMode.allowPrivileged = false;
   charlie.conversation->setDefaultSettings(charlie.handle, charlieConversationSettings);

   // Bob calls Charlie who has blocked manual calls, e.g. auto-attendant or answering machine only
   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall, charlie.config.uri());
   bob.conversation->start(bobCall);
   auto bobConversationEvents = std::async(std::launch::async, [&]()
   {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing_answermode(bob, bobCall, charlie.config.uri(), bobConversationSettings.answerMode, NULL);
      std::function<void(ConversationEndedEvent)> endedReasonValidator = [] (ConversationEndedEvent evt)
      {
         ASSERT_EQ(evt.sipResponseCode, 403);
         ASSERT_EQ(evt.signallingEndWarningCode, 399);
         ASSERT_EQ(evt.signallingEndWarning, "manual answer forbidden");
      };
      assertConversationEnded_ex(bob, bobCall, ConversationEndReason_ServerRejected, endedReasonValidator);
   });

   waitFor(bobConversationEvents);

   bobConversationSettings.answerMode.mode = SipConversation::AnswerMode_Auto;
   bob.conversation->setDefaultSettings(bob.handle, bobConversationSettings);

   SipConversationHandle bobCall2 = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall2, charlie.config.uri());
   bob.conversation->start(bobCall2);
   auto bobConversationEvents2 = std::async(std::launch::async, [&]()
   {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing_answermode(bob, bobCall2, charlie.config.uri(), bobConversationSettings.answerMode, NULL);
      std::function<void(ConversationEndedEvent)> endedReasonValidator = [] (ConversationEndedEvent evt)
      {
         ASSERT_EQ(evt.sipResponseCode, 403);
         ASSERT_EQ(evt.signallingEndWarningCode, 399);
         ASSERT_EQ(evt.signallingEndWarning, "automatic answer forbidden");
      };
      assertConversationEnded_ex(bob, bobCall2, ConversationEndReason_ServerRejected, endedReasonValidator);
   });

   waitFor(bobConversationEvents2);
}

class MySipConversationHandler : public CPCAPI2::EventSyncHandler<CPCAPI2::SipConversation::SipConversationHandlerInternal>
{

public:

   MySipConversationHandler() : mConvHandle(-1), mOfferAnswerEvt(NULL) {}
   virtual ~MySipConversationHandler() {}

   // Inherited via SipConversationHandlerInternal
   virtual int onNewConversation(SipConversationHandle conversation, const NewConversationEvent & args) override
   {
      mConvHandle = conversation;
      safeCout("MySipConversationHandler::onNewConversation(): conversation handle: " << mConvHandle);
      return 0;
   }

   virtual int onConversationEnded(SipConversationHandle conversation, const ConversationEndedEvent & args) override
   {
      safeCout("MySipConversationHandler::onConversationEnded(): conversation handle: " << mConvHandle);
      return 0;
   }

   virtual int onIncomingTransferRequest(SipConversationHandle conversation, const TransferRequestEvent & args) override
   {
      safeCout("MySipConversationHandler::onIncomingTransferRequest(): conversation handle: " << mConvHandle);
      return 0;
   }

   virtual int onIncomingRedirectRequest(SipConversationHandle conversation, const RedirectRequestEvent & args) override
   {
      safeCout("MySipConversationHandler::onIncomingRedirectRequest(): conversation handle: " << mConvHandle);
      return 0;
   }

   virtual int onIncomingTargetChangeRequest(SipConversationHandle conversation, const TargetChangeRequestEvent & args) override
   {
      safeCout("MySipConversationHandler::onIncomingTargetChangeRequest(): conversation handle: " << mConvHandle);
      return 0;
   }

   virtual int onIncomingHangupRequest(SipConversationHandle conversation, const HangupRequestEvent & args) override
   {
      safeCout("MySipConversationHandler::onIncomingHangupRequest(): conversation handle: " << mConvHandle);
      return 0;
   }

   virtual int onIncomingBroadsoftTalkRequest(SipConversationHandle conversation, const BroadsoftTalkEvent & args) override
   {
      safeCout("MySipConversationHandler::onIncomingBroadsoftTalkRequest(): conversation handle: " << mConvHandle);
      return 0;
   }

   virtual int onIncomingBroadsoftHoldRequest(SipConversationHandle conversation, const BroadsoftHoldEvent & args) override
   {
      safeCout("MySipConversationHandler::onIncomingBroadsoftHoldRequest(): conversation handle: " << mConvHandle);
      return 0;
   }

   virtual int onTransferProgress(SipConversationHandle conversation, const TransferProgressEvent & args) override
   {
      safeCout("MySipConversationHandler::onTransferProgress(): conversation handle: " << mConvHandle);
      return 0;
   }

   virtual int onConversationStateChangeRequest(SipConversationHandle conversation, const ConversationStateChangeRequestEvent & args) override
   {
      safeCout("MySipConversationHandler::onConversationStateChangeRequest(): conversation handle: " << mConvHandle);
      delete mOfferAnswerEvt; mOfferAnswerEvt = NULL;
      return 0;
   }

   virtual int onConversationStateChanged(SipConversationHandle conversation, const ConversationStateChangedEvent & args) override
   {
      safeCout("MySipConversationHandler::onConversationStateChanged(): conversation handle: " << mConvHandle);
      delete mOfferAnswerEvt; mOfferAnswerEvt = NULL;
      return 0;
   }

   virtual int onConversationMediaChangeRequest(SipConversationHandle conversation, const ConversationMediaChangeRequestEvent & args) override
   {
      safeCout("MySipConversationHandler::onConversationMediaChangeRequest(): conversation handle: " << mConvHandle);
      delete mOfferAnswerEvt; mOfferAnswerEvt = NULL;
      return 0;
   }

   virtual int onConversationMediaChanged(SipConversationHandle conversation, const ConversationMediaChangedEvent & args) override
   {
      safeCout("MySipConversationHandler::onConversationMediaChanged(): conversation handle: " << mConvHandle);
      delete mOfferAnswerEvt; mOfferAnswerEvt = NULL;
      return 0;
   }

   virtual int onConversationStatisticsUpdated(SipConversationHandle conversation, const ConversationStatisticsUpdatedEvent & args) override
   {
      safeCout("MySipConversationHandler::onConversationStatisticsUpdated(): conversation handle: " << mConvHandle);
      return 0;
   }

   virtual int onError(SipConversationHandle conversation, const CPCAPI2::SipConversation::ErrorEvent & args) override
   {
      safeCout("MySipConversationHandler::onError(): conversation handle: " << mConvHandle);
      return 0;
   }

   virtual int onConversationInitiated(SipConversationHandle conversation, const ConversationInitiatedEvent & args) override
   {
      safeCout("MySipConversationHandler::onConversationInitiated(): conversation handle: " << mConvHandle);
      return 0;
   }

   virtual int onSdpOfferAnswer(SipConversationHandle conversation, const SdpOfferAnswerEvent& args) override
   {
      safeCout("MySipConversationHandler::onSdpOfferAnswer(): conversation handle: " << mConvHandle);
      resip::Lock lck(mMtx);
      if (mOfferAnswerEvt)
      {
         delete mOfferAnswerEvt; mOfferAnswerEvt = NULL;
      }
      mOfferAnswerEvt = new SdpOfferAnswerEvent(args.sdp, args.answerMode);
      mNewConversationCond.signal();
      return 0;
   }

   virtual int onLocalSdpOffer(SipConversationHandle conversation, const LocalSdpOfferEvent& args) override
   {
      safeCout("MySipConversationHandler::onLocalSdpOffer(): conversation handle: " << mConvHandle);
      return 0;
   }

   virtual int onLocalSdpAnswer(SipConversationHandle conversation, const LocalSdpAnswerEvent& args) override
   {
      safeCout("MySipConversationHandler::onLocalSdpAnswer(): conversation handle: " << mConvHandle);
      return 0;
   }

   virtual int onIntervalReport(SipConversationHandle conversation, const IntervalReportEvent& args) override
   {
      safeCout("MySipConversationHandler::onIntervalReport(): conversation handle: " << mConvHandle);
      return 0;
   }

   SipConversationHandle mConvHandle;
   resip::Condition mNewConversationCond;
   resip::Mutex mMtx;
   SdpOfferAnswerEvent* mOfferAnswerEvt;

};

TEST_F(BasicCallTests, DISABLED_BasicCallWithAnswerModeHold)
{
   TestAccount bob("bob", Account_NoInit);
   TestAccount charlie("charlie");

   bob.config.settings.answerModeSupported = true;
   bob.init();
   bob.enable();

   CPCAPI2::SipConversation::SipConversationManager* charlieConvMgr = CPCAPI2::SipConversation::SipConversationManager::getInterface(charlie.phone);
   CPCAPI2::SipConversation::SipAVConversationManagerInterface* charlieConvMgrIf = dynamic_cast<CPCAPI2::SipConversation::SipAVConversationManagerInterface*>(charlieConvMgr);
   std::unique_ptr<MySipConversationHandler> charlieConvObserver(new MySipConversationHandler);
   charlieConvMgrIf->addSdkObserver(charlieConvObserver.get());

   // Bob calls Charlie then Charlie hangs up
   SipConversationSettings conversationSettings;
   conversationSettings.answerMode.mode = SipConversation::AnswerMode_Auto;
   bob.conversation->setDefaultSettings(bob.handle, conversationSettings);
   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall, charlie.config.uri());
   bob.conversation->start(bobCall);
   auto bobConversationEvents = std::async(std::launch::async, [&]()
   {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing_answermode(bob, bobCall, charlie.config.uri(), conversationSettings.answerMode, NULL);
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged_answermode(bob, bobCall, ConversationState_RemoteRinging, conversationSettings.answerMode, NULL);
      assertConversationStateChanged_answermode(bob, bobCall, ConversationState_Connected, conversationSettings.answerMode, NULL);

      assertSuccess(bob.conversation->hold(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt)
      {
         ASSERT_TRUE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendOnly, evt.localMediaInfo[0].mediaDirection);
      });
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertSuccess(bob.conversation->unhold(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt)
      {
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });

      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   auto charlieConversationEvents = std::async(std::launch::async, [&]()
   {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle charlieCall;
      assertNewConversationIncoming_answermode(charlie, &charlieCall, bob.config.uri(), conversationSettings.answerMode, NULL);
      charlie.conversation->sendRingingResponse(charlieCall);
      assertConversationStateChanged_answermode(charlie, charlieCall, ConversationState_LocalRinging, conversationSettings.answerMode, NULL);
      charlie.conversation->accept(charlieCall);
      assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);
      assertConversationStateChanged_answermode(charlie, charlieCall, ConversationState_Connected, conversationSettings.answerMode, NULL);

      assertConversationMediaChangeRequest(charlie, charlieCall, MediaDirection_SendOnly);

      {
         resip::Lock lck(charlieConvObserver->mMtx);
         bool timeout = charlieConvObserver->mNewConversationCond.wait(charlieConvObserver->mMtx, 500);
         safeCout("BasicCallTests.BasicCallWithAnswerModeHold(): media request received, waiting for onSdpOfferAnswer(): conversation handle: " << charlieConvObserver->mConvHandle << " timeout: " << timeout);
         SipConversation::SdpOfferAnswerEvent evt;

         ASSERT_EQ(false, (charlieConvObserver->mOfferAnswerEvt == NULL));
         evt = *(charlieConvObserver->mOfferAnswerEvt);
         ASSERT_EQ(SipConversation::AnswerMode_Disabled, evt.answerMode.mode);
         ASSERT_EQ(false, evt.answerMode.privileged);
         ASSERT_EQ(false, evt.answerMode.required);
      }

      assertSuccess(charlie.conversation->accept(charlieCall));
      assertConversationMediaChanged_ex(charlie, charlieCall, [](const ConversationMediaChangedEvent& evt)
      {
         ASSERT_FALSE(evt.localHold);
         ASSERT_TRUE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_ReceiveOnly, evt.localMediaInfo[0].mediaDirection);
      });

      assertConversationMediaChangeRequest(charlie, charlieCall, MediaDirection_SendReceive);
      assertSuccess(charlie.conversation->accept(charlieCall));
      assertConversationMediaChanged_ex(charlie, charlieCall, [](const ConversationMediaChangedEvent& evt)
      {
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });

      // Check that bobCall video has RTCP. We do this by getting two reports and comparing stamps
      TestCallEvents::expectRTCP(__LINE__, charlie, charlieCall);

      charlie.conversation->end(charlieCall);
      assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor2(bobConversationEvents, charlieConversationEvents);
}

TEST_F(BasicCallTests, BasicCallWithAnswerModeSetAnswerMode)
{
   TestAccount bob("bob", Account_NoInit);
   TestAccount charlie("charlie", Account_NoInit);

   bob.config.settings.answerModeSupported = true;
   bob.init();
   bob.enable();

   charlie.config.settings.answerModeSupported = true;
   charlie.init();
   charlie.enable();

   SipConversationSettings conversationSettings;
   conversationSettings.answerMode.required = false;
   conversationSettings.answerMode.privileged = false;
   conversationSettings.answerMode.mode = SipConversation::AnswerMode_Auto;
   bob.conversation->setDefaultSettings(bob.handle, conversationSettings);

   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall, charlie.config.uri());
   bob.conversation->start(bobCall);
   auto bobConversationEvents = std::async(std::launch::async, [&]()
   {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing_answermode(bob, bobCall, charlie.config.uri(), conversationSettings.answerMode, NULL);
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged_answermode(bob, bobCall, ConversationState_RemoteRinging, conversationSettings.answerMode, NULL);
      assertConversationStateChanged_answermode(bob, bobCall, ConversationState_Connected, conversationSettings.answerMode, NULL);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   auto charlieConversationEvents = std::async(std::launch::async, [&]()
   {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle charlieCall;
      assertNewConversationIncoming_answermode(charlie, &charlieCall, bob.config.uri(), conversationSettings.answerMode, NULL);
      charlie.conversation->sendRingingResponse(charlieCall);
      assertConversationStateChanged_answermode(charlie, charlieCall, ConversationState_LocalRinging, conversationSettings.answerMode, NULL);
      charlie.conversation->accept(charlieCall);

      assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);
      assertConversationStateChanged_answermode(charlie, charlieCall, ConversationState_Connected, conversationSettings.answerMode, NULL);
      charlie.conversation->end(charlieCall);
      assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor2(bobConversationEvents, charlieConversationEvents);

   conversationSettings.answerMode.required = true;
   conversationSettings.answerMode.privileged = false;
   bob.conversation->setAnswerMode(bob.handle, conversationSettings.answerMode, TransportNone);

   SipConversationHandle bobCall2 = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall2, charlie.config.uri());
   bob.conversation->start(bobCall2);
   auto bobConversationEvents2 = std::async(std::launch::async, [&]()
   {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing_answermode(bob, bobCall2, charlie.config.uri(), conversationSettings.answerMode, NULL);
      assertConversationMediaChanged(bob, bobCall2, MediaDirection_SendReceive);
      assertConversationStateChanged_answermode(bob, bobCall2, ConversationState_RemoteRinging, conversationSettings.answerMode, NULL);
      assertConversationStateChanged_answermode(bob, bobCall2, ConversationState_Connected, conversationSettings.answerMode, NULL);
      assertConversationEnded(bob, bobCall2, ConversationEndReason_UserTerminatedRemotely);
   });

   auto charlieConversationEvents2 = std::async(std::launch::async, [&]()
   {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle charlieCall;
      assertNewConversationIncoming_answermode(charlie, &charlieCall, bob.config.uri(), conversationSettings.answerMode, NULL);
      charlie.conversation->sendRingingResponse(charlieCall);
      assertConversationStateChanged_answermode(charlie, charlieCall, ConversationState_LocalRinging, conversationSettings.answerMode, NULL);
      charlie.conversation->accept(charlieCall);

      assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);
      assertConversationStateChanged_answermode(charlie, charlieCall, ConversationState_Connected, conversationSettings.answerMode, NULL);
      charlie.conversation->end(charlieCall);
      assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor2(bobConversationEvents2, charlieConversationEvents2);

   conversationSettings.answerMode.required = false;
   conversationSettings.answerMode.privileged = true;
   bob.conversation->setAnswerMode(bob.handle, conversationSettings.answerMode, TransportNone);

   SipConversationHandle bobCall3 = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall3, charlie.config.uri());
   bob.conversation->start(bobCall3);
   auto bobConversationEvents3 = std::async(std::launch::async, [&]()
   {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing_answermode(bob, bobCall3, charlie.config.uri(), conversationSettings.answerMode, NULL);
      assertConversationMediaChanged(bob, bobCall3, MediaDirection_SendReceive);
      assertConversationStateChanged_answermode(bob, bobCall3, ConversationState_RemoteRinging, conversationSettings.answerMode, NULL);
      assertConversationStateChanged_answermode(bob, bobCall3, ConversationState_Connected, conversationSettings.answerMode, NULL);
      assertConversationEnded(bob, bobCall3, ConversationEndReason_UserTerminatedRemotely);
   });

   auto charlieConversationEvents3 = std::async(std::launch::async, [&]()
   {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle charlieCall;
      assertNewConversationIncoming_answermode(charlie, &charlieCall, bob.config.uri(), conversationSettings.answerMode, NULL);
      charlie.conversation->sendRingingResponse(charlieCall);
      assertConversationStateChanged_answermode(charlie, charlieCall, ConversationState_LocalRinging, conversationSettings.answerMode, NULL);
      charlie.conversation->accept(charlieCall);

      assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);
      assertConversationStateChanged_answermode(charlie, charlieCall, ConversationState_Connected, conversationSettings.answerMode, NULL);
      charlie.conversation->end(charlieCall);
      assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor2(bobConversationEvents3, charlieConversationEvents3);

   conversationSettings.answerMode.required = true;
   conversationSettings.answerMode.privileged = true;
   bob.conversation->setAnswerMode(bob.handle, conversationSettings.answerMode, TransportNone);

   SipConversationHandle bobCall4 = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall4, charlie.config.uri());
   bob.conversation->start(bobCall4);
   auto bobConversationEvents4 = std::async(std::launch::async, [&]()
   {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing_answermode(bob, bobCall4, charlie.config.uri(), conversationSettings.answerMode, NULL);
      assertConversationMediaChanged(bob, bobCall4, MediaDirection_SendReceive);
      assertConversationStateChanged_answermode(bob, bobCall4, ConversationState_RemoteRinging, conversationSettings.answerMode, NULL);
      assertConversationStateChanged_answermode(bob, bobCall4, ConversationState_Connected, conversationSettings.answerMode, NULL);
      assertConversationEnded(bob, bobCall4, ConversationEndReason_UserTerminatedRemotely);
   });

   auto charlieConversationEvents4 = std::async(std::launch::async, [&]()
   {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle charlieCall;
      assertNewConversationIncoming_answermode(charlie, &charlieCall, bob.config.uri(), conversationSettings.answerMode, NULL);
      charlie.conversation->sendRingingResponse(charlieCall);
      assertConversationStateChanged_answermode(charlie, charlieCall, ConversationState_LocalRinging, conversationSettings.answerMode, NULL);
      charlie.conversation->accept(charlieCall);

      assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);
      assertConversationStateChanged_answermode(charlie, charlieCall, ConversationState_Connected, conversationSettings.answerMode, NULL);
      charlie.conversation->end(charlieCall);
      assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor2(bobConversationEvents4, charlieConversationEvents4);
}

/**
* Test created for OBELISK-3627. Not to be run automatically because it uses specific customer provided accounts.
* It should be marked as DISABLED unless unit test is ran locally with provided accounts.
*/
TEST_F(BasicCallTests, DISABLED_BasicCallCallerDisplayNameChangeViaINFO)
{
   TestAccount bob("bob", Account_NoInit);
   TestAccount charlie("charlie", Account_NoInit);

   bob.config.settings.username = "210";
   bob.config.settings.password = "WC9WAio5Pc";
   bob.config.settings.domain = "486709.crexendo.com";
   bob.config.settings.ipVersion = IpVersion_V4;
   bob.config.settings.displayName = "Crexendo Demo";
   bob.config.settings.ignoreCertVerification = true;
   bob.config.settings.alwaysRouteViaOutboundProxy = false;
   bob.config.settings.maxSipPort = 0;
   bob.config.settings.minSipPort = 0;
   bob.config.settings.outboundProxy = "***************";
   bob.config.settings.registrationIntervalSeconds = 3600;
   bob.config.settings.tcpKeepAliveTime = 30;
   bob.config.settings.udpKeepAliveTime = 30;
   bob.config.settings.sipTransportType = SipAccountTransport_Auto;
   bob.config.settings.useOutbound = false;
   bob.config.settings.useRinstance = true;
   bob.config.settings.useRport = true;

   charlie.config.settings.username = "211";
   charlie.config.settings.password = "9DQ6aYigPY";
   charlie.config.settings.domain = "486709.crexendo.com";
   charlie.config.settings.ipVersion = IpVersion_V4;
   charlie.config.settings.displayName = "Crexendo Demo";
   charlie.config.settings.ignoreCertVerification = true;
   charlie.config.settings.registrationIntervalSeconds = 20;
   charlie.config.settings.alwaysRouteViaOutboundProxy = false;
   charlie.config.settings.maxSipPort = 0;
   charlie.config.settings.minSipPort = 0;
   charlie.config.settings.outboundProxy = "***************";
   charlie.config.settings.registrationIntervalSeconds = 3600;
   charlie.config.settings.tcpKeepAliveTime = 30;
   charlie.config.settings.udpKeepAliveTime = 30;
   charlie.config.settings.sipTransportType = SipAccountTransport_Auto;
   charlie.config.settings.useOutbound = false;
   charlie.config.settings.useRinstance = true;
   charlie.config.settings.useRport = true;

   bob.enable();
   charlie.enable();

   SipConversationHandle h;
   ConversationStateChangedEvent evt;

   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall, "sip:<EMAIL>");
   bob.conversation->start(bobCall);
   auto bobConversationEvents = std::async(std::launch::async, [&]() {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing(bob, bobCall, "sip:<EMAIL>");
      assertConversationStateChanged(bob, bobCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Early);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      ASSERT_TRUE(bob.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onConversationStateChanged",
         15000, HandleEqualsPred<SipConversationHandle>(bobCall), h, evt));
      ASSERT_NE(strcmp(evt.remoteDisplayName.c_str(), ""), 0);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });
   auto charlieConversationEvents = std::async(std::launch::async, [&]() {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle charlieCall;
      assertNewConversationIncoming(charlie, &charlieCall, bob.config.uri());
      charlie.conversation->sendRingingResponse(charlieCall);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_LocalRinging);
      charlie.conversation->accept(charlieCall);
      assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      charlie.conversation->end(charlieCall);
      assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedLocally);
   });
   waitFor2(bobConversationEvents, charlieConversationEvents);
}

/**
Following tests created for OBELISK-5288: ACK not sent for 200 OK during call setup when TLS in use

Issue was caused by a mismatch in the transport type between the via header (tls) and the contact header (tcp), and was
fixed using a hack where the contact header in the incoming signalling was updated to "tls" if the mismatch was detected.
Test cases were made to ensure that dialog mapping is maintained correctly with this change, and use air canada test
accounts so are disable dy default.
*/

TEST_F(BasicCallTests, DISABLED_AirCanadaIncomingCall)
{
   // This test requires that the sdk be modified to mimic the Rogers Hosted PBX transport mismatch to verify the fix, this
   // can be done by updating the transport-type in the TransportSelector::transmit() function in resip.

   /*
           alwaysRouteViaOutboundProxy: 0
                            auth_realm:
                         auth_username: 0003*1012
                           displayName: Kieran Hurley
                                domain: airc1.pbx.myrbs.ca
          enableRegeventDeregistration: 0
            excludeEncryptedTransports: 0
                ignoreCertVerification: 1
                             ipVersion: V4
                    enableNat64Support: 1
                            sslVersion: SSL_HIGHEST
                           cipherSuite: TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256:TLS_AES_128_GCM_SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-SHA384:ECDHE-RSA-AES128-SHA256:DHE-RSA-AES256-SHA256:DHE-RSA-AES128-SHA256:ECDHE-ECDSA-AES256-SHA384:ECDHE-ECDSA-AES128-SHA256:ECDHE-RSA-AES256-SHA:ECDHE-RSA-AES128-SHA:AES256-GCM-SHA384:AES128-GCM-SHA256:AES256-SHA256:AES128-SHA256:AES256-SHA:AES128-SHA:ECDHE-ECDSA-AES256-SHA:ECDHE-ECDSA-AES128-SHA:DHE-RSA-AES256-SHA:DHE-RSA-AES128-SHA
                            maxSipPort: 0
    minimumRegistrationIntervalSeconds: 20
    maximumRegistrationIntervalSeconds: 0
                            minSipPort: 0
          enableRegeventDeregistration: 0
   enableDNSResetOnRegistrationRefresh: 0
           enableAuthResetUponDNSReset: 0
                           nameServers:
             otherNonEscapedCharsInUri:
                         outboundProxy: csbc-pub.sip.rogers.com
           registrationIntervalSeconds: 180
             reRegisterOnResponseTypes: REGISTER/500, REGISTER/503, REGISTER/504, REGISTER/407, REGISTER/408, REGISTER/480, REGISTER/486, REGISTER/502
                      sessionTimerMode: 1
                    sessionTimeSeconds: 0
                        sipQosSettings: 40
                      sipTransportType: 4
                            stunServer:
                      stunServerSource: 0
                      tcpKeepAliveTime: 5
                          tunnelConfig: disabled
                      udpKeepAliveTime: 5
                               useGruu: 0
                      useImsAuthHeader: 0
               useMethodParamInReferTo: 0
                         useInstanceId: 0
                           useOutbound: 1
                             userAgent: Bria Enterprise release 6.0.3 stamp 102646
                          useRegistrar: 1
                              username: 0003*1012
                              useRport: 0
                              XCAPRoot:
                          useRinstance: 1
                     transportHoldover: 0

   */

   /*
   INVITE sip:0008*0004@***************:56709;rinstance=da58840040a1ae2a;transport=tls SIP/2.0
   Via: SIP/2.0/TLS *************:5061;branch=z9hG4bK+4599d56aec32922df3d3ae423cb7368e1+sip+1+b09f3a40
   Max-Forwards: 68
   Contact: <sip:aac462f3354bd412a6a7736623e65b91@*************:5061;transport=tcp>
   To: <sip:0008*0004@*************:5060>
   From: "Air Canada Test Phone" <sip:**********@*************>;tag=*************+1+7f49e398+7d39adb8
   Call-ID: 0gQAAC8WAAACBAAALxYAABarJ4istKVPByRAQp+Ded5c+sVOA/Lae5rUr3J5xReZIW9Id/fcUTb9NGRv3O79UhBcUrqEwb6W1rbuOzRZc8U-@*************
   CSeq: 102 INVITE
   Expires: 90
   Accept: application/sdp, application/dtmf-relay
   Allow: INVITE, ACK, CANCEL, OPTIONS, BYE, REFER, SUBSCRIBE, NOTIFY, INFO, PUBLISH
   Call-Info: <sip:*************:5061>;method="NOTIFY;Event=telephone-event;Duration=2000"
   */

   resip::Uri bobUri;
   bobUri.user() = "bob";
   bobUri.host() = "127.0.0.1";
   bobUri.port() = 5055;

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.useRegistrar = false;
   alice.config.settings.domain = "autotest.cpcapi2";
   alice.config.settings.outboundProxy = "";
   alice.config.settings.ipVersion = IpVersion_V4;
   alice.config.settings.sslVersion = SSL_HIGHEST;
   alice.config.settings.useRinstance = true;
   alice.config.settings.useOutbound = true;
   alice.config.settings.sipTransportType = SipAccountTransport_TLS;
   alice.config.settings.ignoreCertVerification = true;
   alice.config.settings.enableNat64Support = true;
   alice.config.settings.sessionTimerMode = SipAccountSessionTimerMode_Inactive;
   alice.config.settings.registrationIntervalSeconds = 3600;
   alice.config.settings.sipQosSettings = 40;
   alice.config.settings.additionalCertPeerNames.push_back("autotest.cpcapi2");
   alice.config.settings.acceptedCertPublicKeys.push_back("MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEA3Q/3AQAW9Z8thdtQlnxAG6ilSn1FVH81ViruEEEyJBbxk0WPzroavMqRgvXJ6sIE+7iR8hHqY2BowKPhTg2D+WCvycT+i5YC7L37f/oWsnVp5xZZqTtiTTTXc+rSHgl2WO5tGxyGGWlUsslq3UXnHym5H9VvEZ5R3EKJelxauvhO8uNfjEwjuKBYPFjNJbZUoiy9s+ZhFuM1a14ArRZlvQ/P96txt2GLHjScMMiZllO4a6NyLvMJQI7Ys0lI4JmKe5QEBdukvbSnFbnsaGMjwfeAksFiKyDRvHUjyc0EUaqU24hmhn+RZkYSjTqeBd567TlggPR696peqaA/tILWGUgcMhYBga9DFzkpgXc8NDaO6WqAFg2caM2/BAnZ9OyfzNYU7GXCyOAm6JNb+BaxYLg6rqCKr27cSipy4MG6LvgcOndRLAeQoNe55QMqi+lfH90v0nQsNIlIEgo8pXgwML6lef+dk98Ox46Egkq1txFKVrmmZ2bXBiLrdFQYqEhzpAMl8XlKg7Wq4R5q2NyOuVJwQ0RHVCbW6nHO/nT5eyldqEzvSY+pg6juxOIpgtD7XvlXRqq775OW0aVFGoL1ilKCjb//c3WU4oetWNcBizrkYb0BOny7P17s8Jx2ZVtvcup4KMOi3+NZJew1uRBKM8wgsSZ1CLmZq1ae/ECXI3kCAwEAAQ==");
   alice.config.settings.userCertificatePEM = TestEnvironmentConfig::testResourcePath() + "SelfSignedCerts/server/domain_cert_autotest.cpcapi2.pem";
   alice.config.settings.userPrivateKeyPEM = TestEnvironmentConfig::testResourcePath() + "SelfSignedCerts/server/domain_key_autotest.cpcapi2.pem";

   TestAccount bob("bob", Account_NoInit);
   bob.config.settings = alice.config.settings;
   bob.config.settings.minSipPort = bobUri.port();
   bob.config.settings.maxSipPort = bobUri.port();
   bob.config.settings.additionalCertPeerNames.push_back("cp.sipit.net");

   alice.init();
   alice.enable();
   bob.init();
   bob.enable();

   SipConversation::SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);

   alice.conversation->addParticipant(aliceCall, bobUri.getAOR(true).c_str());
   alice.conversation->start(aliceCall);
   auto aliceConversationEvents = std::async(std::launch::async, [&]()
   {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing(alice, aliceCall, bobUri.getAOR(false).c_str());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertConversationEnded_time(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely, 60000);
   });

   auto bobConversationEvents = std::async(std::launch::async, [&]()
   {
      SipConversationHandle bobCall;
      TestCallEvents::expectNewConversationIncoming(__LINE__, bob, &bobCall, alice.config.uri(), nullptr, false);
      bob.conversation->sendRingingResponse(bobCall);
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      bob.conversation->accept(bobCall);
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      bob.conversation->end(bobCall);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor2(aliceConversationEvents, bobConversationEvents);
}

TEST_F(BasicCallTests, DISABLED_AirCanadaTrunkCall)
{
   // Domain: airc1.pbx.myrbs.ca Proxy: csbc-pub.sip.rogers.com
   // Account:                                                                      -              Password                     -              DID
   // Test.Account1@AirCanada   (sip: 0008*0002 pwd: 87eh8vn9uwj9uvjw98k0ik)        -              Test.Account156              -              **********
   // Test.Account2@AirCanada   (sip: 0008*1000 pwd: 78hw9vn9um0jj09k09k909k)       -              Test.Account136              -              **********
   // Test.Account3@AirCanada   (sip: 0008*0004 pwd: 98ejw8mewmv09j09kj09k09)       -              Test.Account196              -              4164790964z

   TestAccount alice("Test.Account1", Account_NoInit);
   alice.config.settings.useRegistrar = true;
   alice.config.settings.domain = "airc1.pbx.myrbs.ca";
   alice.config.settings.outboundProxy = "*************:5061"; // csbc-pub.sip.rogers.com"; // sbc.pbx.myrbs.ca";
   alice.config.settings.username = "0008*0002";
   alice.config.settings.auth_username = "0008*0002";
   alice.config.settings.displayName = "Test.Account1";
   alice.config.settings.password = "87eh8vn9uwj9uvjw98k0ik";
   alice.config.settings.ipVersion = IpVersion_V4;
   alice.config.settings.sslVersion = SSL_HIGHEST;
   alice.config.settings.useRinstance = true;
   alice.config.settings.useOutbound = true;
   alice.config.settings.sipTransportType = SipAccountTransport_TLS;
   alice.config.settings.ignoreCertVerification = true;
   alice.config.settings.enableNat64Support = true;
   alice.config.settings.sessionTimerMode = SipAccountSessionTimerMode_Inactive;
   alice.config.settings.registrationIntervalSeconds = 3600;
   alice.config.settings.sipQosSettings = 40;

   TestAccount bob("Test.Account2", Account_NoInit);
   bob.config.settings = alice.config.settings;
   bob.config.settings.username = "0008*1000";
   bob.config.settings.auth_username = "0008*1000";
   bob.config.settings.displayName = "Test.Account2";
   bob.config.settings.password = "78hw9vn9um0jj09k09k909k";

   alice.init();
   alice.enable();
   bob.init();
   bob.enable();

   SipConversation::SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);

   alice.conversation->addParticipant(aliceCall, "sip:<enter phone number here>@airc1.pbx.myrbs.ca");
   alice.conversation->start(aliceCall);
   auto aliceConversationEvents = std::async(std::launch::async, [&]()
   {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing(alice, aliceCall, "sip:<enter phone number here>@airc1.pbx.myrbs.ca");
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Early);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      alice.conversation->end(aliceCall);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor(aliceConversationEvents);
}

TEST_F(BasicCallTests, DISABLED_AirCanadaPbxCall)
{
   TestAccount alice("Test.Account1", Account_NoInit);
   alice.config.settings.useRegistrar = true;
   alice.config.settings.domain = "airc1.pbx.myrbs.ca";
   alice.config.settings.outboundProxy = "*************:5061"; // csbc-pub.sip.rogers.com"; // sbc.pbx.myrbs.ca";
   alice.config.settings.username = "0008*0002";
   alice.config.settings.auth_username = "0008*0002";
   alice.config.settings.displayName = "Test.Account1";
   alice.config.settings.password = "87eh8vn9uwj9uvjw98k0ik";
   alice.config.settings.ipVersion = IpVersion_V4;
   alice.config.settings.sslVersion = SSL_HIGHEST;
   alice.config.settings.useRinstance = true;
   alice.config.settings.useOutbound = true;
   alice.config.settings.sipTransportType = SipAccountTransport_TLS;
   alice.config.settings.ignoreCertVerification = true;
   alice.config.settings.enableNat64Support = true;
   alice.config.settings.sessionTimerMode = SipAccountSessionTimerMode_Inactive;
   alice.config.settings.registrationIntervalSeconds = 3600;
   alice.config.settings.sipQosSettings = 40;

   TestAccount bob("Test.Account2", Account_NoInit);
   bob.config.settings = alice.config.settings;
   bob.config.settings.username = "0008*1000";
   bob.config.settings.auth_username = "0008*1000";
   bob.config.settings.displayName = "Test.Account2";
   bob.config.settings.password = "78hw9vn9um0jj09k09k909k";

   alice.init();
   alice.enable();
   bob.init();
   bob.enable();

   resip::Uri aliceUri;
   aliceUri.user() = "**********";
   aliceUri.host() = "*************";

   SipConversation::SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);

   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);
   auto aliceConversationEvents = std::async(std::launch::async, [&]()
   {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      alice.conversation->end(aliceCall);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobConversationEvents = std::async(std::launch::async, [&]()
   {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, aliceUri.getAOR(false).c_str());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor2(aliceConversationEvents, bobConversationEvents);
}

TEST_F(BasicCallTests, DISABLED_AirCanadaPbxCallSessionTimer)
{
   int sessionTimeSeconds = 180;
   TestAccount alice("Test.Account1", Account_NoInit);
   alice.config.settings.useRegistrar = true;
   alice.config.settings.domain = "airc1.pbx.myrbs.ca";
   alice.config.settings.outboundProxy = "*************:5061"; // csbc-pub.sip.rogers.com"; // sbc.pbx.myrbs.ca";
   alice.config.settings.username = "0008*0002";
   alice.config.settings.auth_username = "0008*0002";
   alice.config.settings.displayName = "Test.Account1";
   alice.config.settings.password = "87eh8vn9uwj9uvjw98k0ik";
   alice.config.settings.ipVersion = IpVersion_V4;
   alice.config.settings.sslVersion = SSL_HIGHEST;
   alice.config.settings.useRinstance = true;
   alice.config.settings.useOutbound = true;
   alice.config.settings.sipTransportType = SipAccountTransport_TLS;
   alice.config.settings.ignoreCertVerification = true;
   alice.config.settings.enableNat64Support = true;
   alice.config.settings.sessionTimerMode = SipAccountSessionTimerMode_Always; // SipAccountSessionTimerMode_Required not implemented;
   alice.config.settings.sessionTimeSeconds = sessionTimeSeconds;
   alice.config.settings.registrationIntervalSeconds = 3600;
   alice.config.settings.sipQosSettings = 40;

   TestAccount bob("Test.Account2", Account_NoInit);
   bob.config.settings = alice.config.settings;
   bob.config.settings.username = "0008*1000";
   bob.config.settings.auth_username = "0008*1000";
   bob.config.settings.displayName = "Test.Account2";
   bob.config.settings.password = "78hw9vn9um0jj09k09k909k";

   alice.init();
   alice.enable();
   bob.init();
   bob.enable();

   resip::Uri aliceUri;
   aliceUri.user() = "**********";
   aliceUri.host() = "*************";

   SipConversation::SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);

   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);
   auto aliceConversationEvents = std::async(std::launch::async, [&]()
   {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(sessionTimeSeconds * 1000));
      alice.conversation->end(aliceCall);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobConversationEvents = std::async(std::launch::async, [&]()
   {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, aliceUri.getAOR(false).c_str());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertConversationEnded_time(bob, bobCall, ConversationEndReason_UserTerminatedRemotely, ((sessionTimeSeconds * 1000) + 10000));
   });

   waitFor2Ms(aliceConversationEvents, bobConversationEvents, std::chrono::milliseconds((sessionTimeSeconds * 1000) + 20000));
}

TEST_F(BasicCallTests, DISABLED_AirCanadaPbxCallHold)
{
   TestAccount alice("Test.Account1", Account_NoInit);
   alice.config.settings.useRegistrar = true;
   alice.config.settings.domain = "airc1.pbx.myrbs.ca";
   alice.config.settings.outboundProxy = "*************:5061"; // csbc-pub.sip.rogers.com"; // sbc.pbx.myrbs.ca";
   alice.config.settings.username = "0008*0002";
   alice.config.settings.auth_username = "0008*0002";
   alice.config.settings.displayName = "Test.Account1";
   alice.config.settings.password = "87eh8vn9uwj9uvjw98k0ik";
   alice.config.settings.ipVersion = IpVersion_V4;
   alice.config.settings.sslVersion = SSL_HIGHEST;
   alice.config.settings.useRinstance = true;
   alice.config.settings.useOutbound = true;
   alice.config.settings.sipTransportType = SipAccountTransport_TLS;
   alice.config.settings.ignoreCertVerification = true;
   alice.config.settings.enableNat64Support = true;
   alice.config.settings.sessionTimerMode = SipAccountSessionTimerMode_Inactive;
   alice.config.settings.registrationIntervalSeconds = 3600;
   alice.config.settings.sipQosSettings = 40;

   TestAccount bob("Test.Account2", Account_NoInit);
   bob.config.settings = alice.config.settings;
   bob.config.settings.username = "0008*1000";
   bob.config.settings.auth_username = "0008*1000";
   bob.config.settings.displayName = "Test.Account2";
   bob.config.settings.password = "78hw9vn9um0jj09k09k909k";

   alice.init();
   alice.enable();
   bob.init();
   bob.enable();

   resip::Uri aliceUri;
   aliceUri.user() = "**********";
   aliceUri.host() = "*************";

   SipConversation::SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);

   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);
   auto aliceConversationEvents = std::async(std::launch::async, [&]()
   {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      // Do not get the recv-only response for the hold request from the Rogers Hosted PBX,
      // seems to ignore the request and does not propagate it to the remote-party
      assertSuccess(alice.conversation->hold(aliceCall));
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt)
      {
         ASSERT_TRUE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendOnly, evt.localMediaInfo[0].mediaDirection);
      });
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertSuccess(alice.conversation->unhold(aliceCall));
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt)
      {
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      alice.conversation->end(aliceCall);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobConversationEvents = std::async(std::launch::async, [&]()
   {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, aliceUri.getAOR(false).c_str());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      /*
      // Do not get the hold request from the Rogers Hosted PBX
      assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendOnly);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt)
      {
         ASSERT_FALSE(evt.localHold);
         ASSERT_TRUE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_ReceiveOnly, evt.localMediaInfo[0].mediaDirection);
      });
      assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt)
      {
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      */

      assertConversationEnded_time(bob, bobCall, ConversationEndReason_UserTerminatedRemotely, 30000);
   });

   waitFor2(aliceConversationEvents, bobConversationEvents);
}

TEST_F(BasicCallTests, DISABLED_AirCanadaPbxCallCodecChange)
{
   TestAccount alice("Test.Account1", Account_NoInit);
   alice.config.settings.useRegistrar = true;
   alice.config.settings.domain = "airc1.pbx.myrbs.ca";
   alice.config.settings.outboundProxy = "*************:5061"; // csbc-pub.sip.rogers.com"; // sbc.pbx.myrbs.ca";
   alice.config.settings.username = "0008*0002";
   alice.config.settings.auth_username = "0008*0002";
   alice.config.settings.displayName = "Test.Account1";
   alice.config.settings.password = "87eh8vn9uwj9uvjw98k0ik";
   alice.config.settings.ipVersion = IpVersion_V4;
   alice.config.settings.sslVersion = SSL_HIGHEST;
   alice.config.settings.useRinstance = true;
   alice.config.settings.useOutbound = true;
   alice.config.settings.sipTransportType = SipAccountTransport_TLS;
   alice.config.settings.ignoreCertVerification = true;
   alice.config.settings.enableNat64Support = true;
   alice.config.settings.sessionTimerMode = SipAccountSessionTimerMode_Inactive;
   alice.config.settings.registrationIntervalSeconds = 3600;
   alice.config.settings.sipQosSettings = 40;

   TestAccount bob("Test.Account2", Account_NoInit);
   bob.config.settings = alice.config.settings;
   bob.config.settings.username = "0008*1000";
   bob.config.settings.auth_username = "0008*1000";
   bob.config.settings.displayName = "Test.Account2";
   bob.config.settings.password = "78hw9vn9um0jj09k09k909k";

   alice.init();
   alice.enable();
   bob.init();
   bob.enable();

   resip::Uri aliceUri;
   aliceUri.user() = "**********";
   aliceUri.host() = "*************";

   // alice.enableOnlyThisCodec("G711 aLaw");
   // bob.enableOnlyThisCodec("G711 aLaw");

   SipConversation::SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);

   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceConversationEvents = std::async(std::launch::async, [&]()
   {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      // Rogers Hosted PBX rejected request to add video media
      // assertSuccess(alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true));

      // Rogers Hosted PBX rejected request to use crypto suites
      // MediaInfo aliceMedia;
      // aliceMedia.mediaDirection = MediaDirection_SendReceive;
      // aliceMedia.mediaType = MediaType_Audio;
      // aliceMedia.mediaEncryptionOptions.mediaEncryptionMode = MediaEncryptionMode_SRTP_DTLS_Encrypted;
      // aliceMedia.mediaEncryptionOptions.secureMediaRequired = true;
      // aliceMedia.mediaEncryptionOptions.mediaCryptoSuites.clear(); // Ensure no other cryptos are included
      // aliceMedia.mediaEncryptionOptions.mediaCryptoSuites.push_back(MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);

      // Rogers Hosted PBX did not trigger re-INVITEs due to B2BUA behaviour
      MediaInfo aliceMedia;
      aliceMedia.mediaDirection = MediaDirection_SendReceive;
      aliceMedia.mediaType = MediaType_Audio;
      strcpy(aliceMedia.audioCodec.plname, "G711 uLaw");
      alice.enableOnlyThisCodec("G711 uLaw");

      assertSuccess(alice.conversation->configureMedia(aliceCall, aliceMedia));
      assertSuccess(alice.conversation->sendMediaChangeRequest(aliceCall));
      // Do not get the recv-only response for the hold request from the Rogers Hosted PBX,
      // seems to ignore the request and does not propagate it to the remote-party
      // assertSuccess(alice.conversation->hold(aliceCall));
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt)
      {
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      alice.conversation->end(aliceCall);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobConversationEvents = std::async(std::launch::async, [&]()
   {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, aliceUri.getAOR(false).c_str());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertConversationEnded_time(bob, bobCall, ConversationEndReason_UserTerminatedRemotely, 60000);
   });

   waitFor2(aliceConversationEvents, bobConversationEvents);
}

TEST_F(BasicCallTests, DISABLED_AirCanadaPbxCallTransfer)
{
   TestAccount alice("Test.Account1", Account_NoInit);
   alice.config.settings.useRegistrar = true;
   alice.config.settings.domain = "airc1.pbx.myrbs.ca";
   alice.config.settings.outboundProxy = "*************:5061"; // csbc-pub.sip.rogers.com"; // sbc.pbx.myrbs.ca";
   alice.config.settings.username = "0008*0002";
   alice.config.settings.auth_username = "0008*0002";
   alice.config.settings.displayName = "Test.Account1";
   alice.config.settings.password = "87eh8vn9uwj9uvjw98k0ik";
   alice.config.settings.ipVersion = IpVersion_V4;
   alice.config.settings.sslVersion = SSL_HIGHEST;
   alice.config.settings.useRinstance = true;
   alice.config.settings.useOutbound = true;
   alice.config.settings.sipTransportType = SipAccountTransport_TLS;
   alice.config.settings.ignoreCertVerification = true;
   alice.config.settings.enableNat64Support = true;
   alice.config.settings.sessionTimerMode = SipAccountSessionTimerMode_Inactive;
   alice.config.settings.registrationIntervalSeconds = 3600;
   alice.config.settings.sipQosSettings = 40;

   TestAccount bob("Test.Account2", Account_NoInit);
   bob.config.settings = alice.config.settings;
   bob.config.settings.username = "0008*1000";
   bob.config.settings.auth_username = "0008*1000";
   bob.config.settings.displayName = "Test.Account2";
   bob.config.settings.password = "78hw9vn9um0jj09k09k909k";

   TestAccount max("Test.Account3", Account_NoInit);
   max.config.settings = alice.config.settings;
   max.config.settings.username = "0008*0004";
   max.config.settings.auth_username = "0008*0004";
   max.config.settings.displayName = "Test.Account3";
   max.config.settings.password = "98ejw8mewmv09j09kj09k09";

   alice.init();
   alice.enable();
   bob.init();
   bob.enable();
   max.init();
   max.enable();

   resip::Uri aliceUri;
   aliceUri.user() = "**********";
   aliceUri.host() = "*************";

   SipConversation::SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);

   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);
   auto aliceConversationEvents = std::async(std::launch::async, [&]()
   {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      /*
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      SipConversationHandle aliceCallToMax;
      assertTransferRequest_ex(alice, aliceCall, &aliceCallToMax, [&](const TransferRequestEvent& evt)
      {
         ASSERT_EQ(max.config.uri(), evt.transferTargetAddress);
         ASSERT_EQ("", evt.transferTargetDisplayName);
         ASSERT_NE(0, evt.transferTargetConversation);
      });

      assertSuccess(alice.conversation->acceptIncomingTransferRequest(aliceCallToMax));
      assertNewConversationOutgoing(alice, aliceCallToMax, max.config.uri());
      assertConversationStateChanged(alice, aliceCallToMax, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCallToMax, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCallToMax, ConversationState_Connected);

      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertConversationEnded(alice, aliceCallToMax, ConversationEndReason_UserTerminatedRemotely);
      */
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);  // Rogers Hosted PBX B2BUA behaviour, instead of transfer flow
   });

   auto bobConversationEvents = std::async(std::launch::async, [&]()
   {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, aliceUri.getAOR(false).c_str());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertSuccess(bob.conversation->transfer(bobCall, max.config.uri()));
      assertTransferProgress(bob, bobCall, TransferProgressEventType_Ringing);
      assertTransferProgress(bob, bobCall, TransferProgressEventType_Connected);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto maxConversationEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle maxCall;
      assertNewConversationIncoming(max, &maxCall, aliceUri.getAOR(false).c_str());
      assertSuccess(max.conversation->sendRingingResponse(maxCall));
      assertConversationStateChanged(max, maxCall, ConversationState_LocalRinging);
      assertSuccess(max.conversation->accept(maxCall));
      assertConversationMediaChanged(max, maxCall, MediaDirection_SendReceive);
      assertConversationStateChanged(max, maxCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      assertSuccess(max.conversation->end(maxCall));
      assertConversationEnded(max, maxCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor3(aliceConversationEvents, bobConversationEvents, maxConversationEvents);
}

/**
Following tests created for OBELISK-5581: ACK not sent for 200 OK during call setup when TLS in use but UDP specified in contact header
*/

TEST_F(BasicCallTests, DISABLED_VeloxNetworksBasicCall)
{
   resip::Uri bobUri;
   bobUri.user() = "9004";
   bobUri.host() = "myvelox.com";
   bobUri.port() = 5055;

   TestAccount alice("Dev Account 1 (9003)", Account_NoInit);
   alice.config.settings.displayName = "Dev Account 1 (9003)";
   alice.config.settings.username = "9003";
   alice.config.settings.password = "Z=OOn}NWn;-OPE7;[x}BK_Ti";
   alice.config.settings.domain = "myvelox.com";
   alice.config.settings.outboundProxy = "voice.myvelox.com:5061";
   alice.config.settings.ignoreCertVerification = true;
   alice.config.settings.ipVersion = IpVersion_V4;
   alice.config.settings.enableNat64Support = true;
   alice.config.settings.sslVersion = SSL_HIGHEST;
   alice.config.settings.registrationIntervalSeconds = 300;
   alice.config.settings.sessionTimerMode = SipAccountSessionTimerMode_Inactive;
   alice.config.settings.sessionTimeSeconds = 1800;
   alice.config.settings.sipTransportType = SipAccountTransport_TLS;
   alice.config.settings.tcpKeepAliveTime = 120;
   alice.config.settings.udpKeepAliveTime = 120;
   alice.config.settings.useGruu = false;
   alice.config.settings.useImsAuthHeader = false;
   alice.config.settings.useInstanceId = true;
   alice.config.settings.useRinstance = true;
   alice.config.settings.useOutbound = false;
   alice.config.settings.useRport = true;
   alice.config.settings.auth_username = "<EMAIL>";
   alice.config.settings.sipQosSettings = 0;
   alice.config.settings.userAgent = "Velox Softphone";

   alice.config.settings.additionalCertPeerNames.push_back("autotest.cpcapi2");
   alice.config.settings.acceptedCertPublicKeys.push_back("MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEA3Q/3AQAW9Z8thdtQlnxAG6ilSn1FVH81ViruEEEyJBbxk0WPzroavMqRgvXJ6sIE+7iR8hHqY2BowKPhTg2D+WCvycT+i5YC7L37f/oWsnVp5xZZqTtiTTTXc+rSHgl2WO5tGxyGGWlUsslq3UXnHym5H9VvEZ5R3EKJelxauvhO8uNfjEwjuKBYPFjNJbZUoiy9s+ZhFuM1a14ArRZlvQ/P96txt2GLHjScMMiZllO4a6NyLvMJQI7Ys0lI4JmKe5QEBdukvbSnFbnsaGMjwfeAksFiKyDRvHUjyc0EUaqU24hmhn+RZkYSjTqeBd567TlggPR696peqaA/tILWGUgcMhYBga9DFzkpgXc8NDaO6WqAFg2caM2/BAnZ9OyfzNYU7GXCyOAm6JNb+BaxYLg6rqCKr27cSipy4MG6LvgcOndRLAeQoNe55QMqi+lfH90v0nQsNIlIEgo8pXgwML6lef+dk98Ox46Egkq1txFKVrmmZ2bXBiLrdFQYqEhzpAMl8XlKg7Wq4R5q2NyOuVJwQ0RHVCbW6nHO/nT5eyldqEzvSY+pg6juxOIpgtD7XvlXRqq775OW0aVFGoL1ilKCjb//c3WU4oetWNcBizrkYb0BOny7P17s8Jx2ZVtvcup4KMOi3+NZJew1uRBKM8wgsSZ1CLmZq1ae/ECXI3kCAwEAAQ==");
   alice.config.settings.userCertificatePEM = TestEnvironmentConfig::testResourcePath() + "SelfSignedCerts/server/domain_cert_autotest.cpcapi2.pem";
   alice.config.settings.userPrivateKeyPEM = TestEnvironmentConfig::testResourcePath() + "SelfSignedCerts/server/domain_key_autotest.cpcapi2.pem";

   TestAccount bob("Dev Account 2 (9004) iOS", Account_NoInit);
   bob.config.settings = alice.config.settings;
   bob.config.settings.displayName = "Dev Account 2 (9004) iOS";
   bob.config.settings.username = "9004";
   bob.config.settings.auth_username = "<EMAIL>";
   bob.config.settings.password = "e=@c:-23K%lC5sdlsR+,a[+|";
   bob.config.settings.additionalCertPeerNames.push_back("cp.sipit.net");

   alice.init();
   alice.enable();

   bob.init();
   bob.enable();

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   SipConversation::SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);

   alice.conversation->addParticipant(aliceCall, bobUri.getAOR(true).c_str());
   alice.conversation->start(aliceCall);
   auto aliceConversationEvents = std::async(std::launch::async, [&]()
   {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing(alice, aliceCall, bobUri.getAOR(false).c_str());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertConversationEnded_time(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely, 60000);
   });

   auto bobConversationEvents = std::async(std::launch::async, [&]()
   {
      SipConversationHandle bobCall;
      TestCallEvents::expectNewConversationIncoming(__LINE__, bob, &bobCall, alice.config.uri(), nullptr, false);
      bob.conversation->sendRingingResponse(bobCall);
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      bob.conversation->accept(bobCall);
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      bob.conversation->end(bobCall);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor2(aliceConversationEvents, bobConversationEvents);
   alice.disable();
   bob.disable();
}

TEST_F(BasicCallTests, DISABLED_BasicCallWithAuth) {

   ReproHolder::destroyInstance();

   repro::ReproRunner* repro1 = new repro::ReproRunner();
   {
      const char* reproArgs[1];
      reproArgs[0] = "";
      resip::Data configFile = (TestEnvironmentConfig::testResourcePath() + "repro_auth1.config").c_str();
      repro1->run(1, reproArgs, configFile);
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   TestAccount bob("alice", Account_NoInit);
   bob.config.settings.username = "bob123";
   bob.config.settings.password = "bob123";
   bob.config.settings.domain = "cp.local";
   bob.config.settings.outboundProxy = "prisecwithauth.local";
   bob.config.settings.sipTransportType = SipAccountTransport_TCP;
   bob.config.settings.autoRetryOnTransportDisconnect = true;
   bob.config.settings.useRport = false;
   bob.config.settings.useOutbound = false;
   bob.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   bob.config.settings.minimumRegistrationIntervalSeconds = 5;
   bob.config.settings.maximumRegistrationIntervalSeconds = 5;

   ASSERT_TRUE(repro1->getProxy()->getUserStore().addUser(bob.config.settings.username.c_str(), bob.config.settings.domain.c_str(), "cp.local", bob.config.settings.password.c_str(), true, "Bob", "<EMAIL>"));

   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   bob.enable();
   
   
   TestAccount charlie("bob", Account_NoInit);
   charlie.config.settings.username = "bob123";
   charlie.config.settings.password = "bob123";
   bob.config.settings.domain = "cp.local";
   charlie.config.settings.outboundProxy = "prisecwithauth.local";
   charlie.config.settings.sipTransportType = SipAccountTransport_TCP;
   charlie.config.settings.autoRetryOnTransportDisconnect = true;
   charlie.config.settings.useRport = false;
   charlie.config.settings.useOutbound = false;
   charlie.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   charlie.config.settings.minimumRegistrationIntervalSeconds = 5;
   charlie.config.settings.maximumRegistrationIntervalSeconds = 5;

   ASSERT_TRUE(repro1->getProxy()->getUserStore().addUser(bob.config.settings.username.c_str(), bob.config.settings.domain.c_str(), "cp.local", bob.config.settings.password.c_str(), true, "Charlie", "<EMAIL>"));

   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   charlie.enable();
   
   
   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall, charlie.config.uri());
   bob.conversation->start(bobCall);
   auto bobConversationEvents = std::async(std::launch::async, [&] () {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing(bob, bobCall, charlie.config.uri());
      assertConversationStateChanged(bob, bobCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });
   auto charlieConversationEvents = std::async(std::launch::async, [&] () {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle charlieCall;
      assertNewConversationIncoming(charlie, &charlieCall, bob.config.uri());
      charlie.conversation->sendRingingResponse(charlieCall);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_LocalRinging);
      charlie.conversation->accept(charlieCall);
      assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_Connected);
      charlie.conversation->end(charlieCall);
      assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedLocally);
   });
   waitFor2(bobConversationEvents, charlieConversationEvents);
   

   std::this_thread::sleep_for(std::chrono::minutes(5));

   // restart original repro
   {
      const char* reproArgs[1];
      reproArgs[0] = "";
      resip::Data configFile = (TestEnvironmentConfig::testResourcePath() + "repro.config").c_str();
      ReproHolder::instance()->run(1, reproArgs, configFile);
   }

}

TEST_F(BasicCallTests, ByeWithQ850ReasonHeader) {
   TestAccount alice("alice", Account_Init);
   TestAccount bob("bob");

   class MySipConversationAdornmentHandler : public SipConversationAdornmentHandler
   {
   public:
      MySipConversationAdornmentHandler(CPCAPI2::SipConversation::SipConversationManager* convMgr) : convMgr(convMgr) {}

      virtual int onConversationAdornment(SipConversationHandle conversation, const ConversationAdornmentEvent& args)
      {
         if (args.method == "BYE")
         {
            CPCAPI2::SipHeader sipHeader;
            sipHeader.header = "Reason";
            sipHeader.value = "Q.850;cause=19;text=\"NO_ANSWER\"";
            cpc::vector<CPCAPI2::SipHeader> sipHeaders;
            sipHeaders.push_back(sipHeader);
            convMgr->adornMessage(conversation, args.adornmentMessageId, sipHeaders);
         }
         return kSuccess;
      }

   private:
      CPCAPI2::SipConversation::SipConversationManager* convMgr;
   };
   MySipConversationAdornmentHandler adornmentHandler(alice.conversation);
   alice.conversation->setAdornmentHandler(alice.handle, &adornmentHandler);
   alice.enable();

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]() {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      SipConversationHandle bobCall;
      std::function<void(NewConversationEvent)> remoteResourcePriValidator = [](NewConversationEvent evt) {
      };
      TestCallEvents::expectNewConversationIncoming(__LINE__, bob, &bobCall, alice.config.uri(), remoteResourcePriValidator);
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      SipConversationHandle h;
      ConversationEndedEvent evt;
      ASSERT_TRUE(bob.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onConversationEnded",
         15000, HandleEqualsPred<SipConversationHandle>(bobCall), h, evt)) << "missed conversation ended event";
      ASSERT_EQ(evt.signallingEndReason, "Q.850;cause=19;text=\"NO_ANSWER\"");

   });

   waitFor2(aliceEvents, bobEvents);
}

#ifdef __APPLE__
TEST_F(BasicCallTests, MavenirAck_Sipp)
{
   // to reproduce Mavenir's issue (ACK being ignored), adjust the ACK in BasicCallTests.MavenirAck.xml
   // from
   // To: <tel:[service]>[peer_tag_param]
   // to
   // To: tel:[service][peer_tag_param]
   //
   // and watch the resip discard the ACK because the to tag gets placed into the userinfo portion
   // of the SIP URI, rather than being treated as a To header parameter.

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.useRegistrar = false;
   alice.config.settings.outboundProxy = "";
   alice.config.settings.domain = "127.0.0.1";
   alice.config.settings.minSipPort = 55060;
   alice.config.settings.maxSipPort = 55060;
   alice.init();
   alice.enable();

   MediaInfo aliceAudio;
   MediaInfo aliceVideo;
   
   SippRunnerSettings sippRunnerSettings;
   sippRunnerSettings.sipListenPort = 50010;
   sippRunnerSettings.mediaPort = 50020;
   sippRunnerSettings.timeoutSec = 120;
   sippRunnerSettings.sipTargetPort = alice.config.settings.minSipPort;
   sippRunnerSettings.scenarioFileName = "BasicCallTests.MavenirAck.xml";
   
   SippRunner sippRunner(sippRunnerSettings);
   ASSERT_EQ(kSuccess, sippRunner.start());

   std::stringstream sippUriNoPort;
   sippUriNoPort << "sip:sip@127.0.0.1";
   std::stringstream sippUriWithPort;
   sippUriWithPort << sippUriNoPort.str() << ":" << sippRunnerSettings.sipListenPort;

   auto aliceEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle aliceCall;
      assertNewConversationIncoming(alice, &aliceCall, "sip:sipp@127.0.0.1:50010");
      assertSuccess(alice.conversation->accept(aliceCall));

      // without this second accept, this is a normal call flow
      // the extra accept should be a noop
      assertSuccess(alice.conversation->accept(aliceCall));

      //Note: "not my event" may appear if the mediaChange event happens before the onError event, this is OK
      assertConversationError(alice, aliceCall);

      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      
      std::this_thread::sleep_for(std::chrono::seconds(5));
      alice.conversation->end(aliceCall);
      
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
      
      // give time for sipp to response to BYE. is there a better way?
      std::this_thread::sleep_for(std::chrono::seconds(5));
      
      sippRunner.stop();
   });

   waitFor(aliceEvents);
}
#endif // __APPLE__

static repro::ReproRunner* runRepro(cpc::string config)
{
   repro::ReproRunner* res = new repro::ReproRunner();
   const char* const reproArgs[] = { "" };
   resip::Data configFile = (TestEnvironmentConfig::testResourcePath() + config).c_str();
   res->run(1, reproArgs, configFile);
   return res;
}

static void runOriginalRepro()
{
   const char* const reproArgs[] = { "" };
   resip::Data configFile = (TestEnvironmentConfig::testResourcePath() + "repro.config").c_str();
   ReproHolder::instance()->run(1, reproArgs, configFile);
}

TEST_F(BasicCallTests, StrettoTunnelCall) {

   ReproHolder::destroyInstance();

   // need repro to force all traffic through it, otherwise Charlie won't be
   // able to e.g. send a BYE reques to Bob
   runRepro("repro_force_recordroute.config");

   TestAccount bob("alice", Account_NoInit);
   bob.config.settings.useRegistrar = false;
   bob.config.settings.tunnelConfig.useTunnel = true;
   bob.config.settings.sipTransportType = SipAccountTransport_UDP;
   bob.config.settings.tunnelConfig.tunnelType = TunnelType_StrettoTunnel;
   // repro.config has been adjsuted to WSS listen on port 7061
   bob.config.settings.tunnelConfig.strettoTunnelURL = "wss://127.0.0.1:7061";
   bob.config.settings.ignoreCertVerification = true;
   bob.config.settings.tunnelConfig.strettoTunnelSkipHandshake = true;
   bob.config.settings.sourceAddress = "127.0.0.1";


   bob.init();
   bob.enable(false);
   assertAccountRegistering(bob);
   assertAccountRegistered(bob);
   TestAccount charlie("charlie");

   // Bob calls Charlie then Charlie hangs up
   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall, charlie.config.uri());
   bob.conversation->start(bobCall);
   auto bobConversationEvents = std::async(std::launch::async, [&] () {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing(bob, bobCall, charlie.config.uri());
      assertConversationStateChanged(bob, bobCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      // assertMediaFlowing(bob, bobCall, true, false);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);

      assertCallHadMedia(bob, bobCall, true, false);
   });
   auto charlieConversationEvents = std::async(std::launch::async, [&] () {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle charlieCall;
      assertNewConversationIncoming(charlie, &charlieCall, bob.config.uri());
      charlie.conversation->sendRingingResponse(charlieCall);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_LocalRinging);
      charlie.conversation->accept(charlieCall);
      assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      // assertMediaFlowing(charlie, charlieCall, true, false);
      charlie.conversation->end(charlieCall);
      assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedLocally);

      assertCallHadMedia(charlie, charlieCall, true, false);
   });
   waitFor2(bobConversationEvents, charlieConversationEvents);

   runOriginalRepro();

}

class OpusPacsizeLogChecker
{
public:
   OpusPacsizeLogChecker() : mFoundLogLine(false)
   {
   }
   
   void loggingListener(const char *message, const char* subsystem, CPCAPI2::LogLevel level)
   {
      // [2021-10-22 12:36:39.274](Info)(RtpStreamImpl.cxx:1921) 0 t41987 Changing opus pacsize to 1920 due to high packet loss (at local end)
      if (std::string(message).find("Changing opus pacsize to 1920 due to high packet loss (at local end)") != std::string::npos)
      {
         mFoundLogLine = true;
      }
   }
   
   std::atomic_bool mFoundLogLine;
};

TEST_F(BasicCallTests, BasicCallTestOpus_Lossy) {
   TestAccount alice("alice");
   alice.enableOnlyThisCodec("OPUS");
   
   TestAccount bob("bob");
   bob.enableOnlyThisCodec("OPUS");
   
   OpusPacsizeLogChecker bobLogchecker;
   std::function<void(const char *message, const char* subsystem, CPCAPI2::LogLevel level)> f = std::bind(&OpusPacsizeLogChecker::loggingListener,
                                                                                                      &bobLogchecker, std::placeholders::_1,
                                                                                                      std::placeholders::_2, std::placeholders::_3);

   AutoTestsLogger::ScopedMessageListenerFunction smlf(f);
   
   TurnAsyncUdpSocket_OutgoingPacketlossInducer::Config udpLossConfig;
   udpLossConfig.lossRatePct = 25;
   TurnAsyncUdpSocket_OutgoingPacketlossInducer plu(udpLossConfig);
   
   SipConversationSettings settings;
   // need to set same non-empty session name to enable opus dynamic bitrate adjustment
   settings.sessionName = "cpcapi2tests";
   alice.conversation->setDefaultSettings(alice.handle, settings);
   bob.conversation->setDefaultSettings(bob.handle, settings);
   
   
   std::string soundClipUri;
#ifdef ANDROID
   soundClipUri = "android.resource://com.counterpath.sdkdemo.advancedaudiocall/raw/baseball";
#else
   soundClipUri = "file:" + TestEnvironmentConfig::testResourcePath() + "baseball.wav";
#endif

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);
   
   const int callDurationSec = 30;

   auto aliceEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      
      alice.conversation->playSound(aliceCall, soundClipUri.c_str(), true);
      
      SipConversationState aliceConvState;
      alice.conversationState->getState(aliceCall, aliceConvState);
      ASSERT_NE(aliceConvState.localMediaInfo.size(), 0);

      std::this_thread::sleep_for(std::chrono::seconds(callDurationSec));

      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   SipConversationHandle bobCall;
   auto bobEvents = std::async(std::launch::async, [&] () {
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      SipConversationState bobConvState;
      ASSERT_EQ(bob.conversationState->getState(bobCall, bobConvState), kSuccess);
      ASSERT_NE(bobConvState.localMediaInfo.size(), 0);

      std::this_thread::sleep_for(std::chrono::seconds(callDurationSec));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);

      ASSERT_EQ(bob.conversationState->getState(bobCall, bobConvState), kSuccess);

      ASSERT_EQ(1, bobConvState.statistics.audioChannels.size());
      ASSERT_GT(bobConvState.statistics.audioChannels[0].streamDataCounters.packetsReceived, 0);
      
      if (udpLossConfig.lossRatePct > 0)
      {
         ASSERT_GT(udpLossConfig.lossRatePct, 3);
         int expectedPacketloss = udpLossConfig.lossRatePct - /* allow underreporting of loss */ 5;
      
         ASSERT_GE(bobConvState.statistics.audioChannels[0].streamStatistics.cumulativeLost, 0);
         
         float cumulativeLost = bobConvState.statistics.audioChannels[0].streamStatistics.cumulativeLost;
         float cumulativeReceived = bobConvState.statistics.audioChannels[0].streamDataCounters.packetsReceived;
         ASSERT_GE(cumulativeLost / cumulativeReceived * 100, expectedPacketloss);
         
// VQmon not currently available on Linux
#if (CPCAPI2_BRAND_VQMONEP_MOBILE == 1)
         ASSERT_GE(bobConvState.statistics.audioChannels.begin()->XRvoipMetrics.lossRate, expectedPacketloss);
         ASSERT_LE(bobConvState.statistics.audioChannels.begin()->XRvoipMetrics.MOSLQ, 25);
#endif
         

         // caveat: currently monitors logging for both alice and bob
         ASSERT_TRUE(bobLogchecker.mFoundLogLine);
      }
   });

   noReturnWaitFor2(aliceEvents, bobEvents);
}

#ifdef _WIN32

static int(WSAAPI* Original_bind)(_In_ SOCKET s, _In_reads_bytes_(namelen) const struct sockaddr FAR* name, _In_ int namelen) = bind;
static std::atomic_bool gFailBindCalls = false;
int WSAAPI Intercept_bind(_In_ SOCKET s, _In_reads_bytes_(namelen) const struct sockaddr FAR* name, _In_ int namelen)
{
   if (gFailBindCalls)
   {
      if (namelen == sizeof(sockaddr_in))
      {
         const sockaddr_in* i = reinterpret_cast<const sockaddr_in*>(name);
         // only fail specific bind calls set to use source port 50000
         if (/*i->sin_family == AF_INET &&*/ ntohs(i->sin_port) == 50000)
         {
            GenericLog(resip::Subsystem::SIP, resip::Log::Info, << "Detour intercepting bind on socket " << s << ", " << resip::DnsUtil::inet_ntop(i->sin_addr) << ":" << ntohs(i->sin_port));
            return -1;
         }
      }
   }

   // calls windows ::bind(..)
   return Original_bind(s, name, namelen);
}

TEST_F(BasicCallTests, DISABLED_PortInuse_NoRegistrar) {

   TestAccount bob("bob", Account_NoInit);
   bob.config.settings.useRegistrar = false;
   bob.config.settings.domain = "bogus.invalid";
   bob.config.settings.outboundProxy = "";
   bob.init();
   bob.enable();

   // this code allows us intercept calls to the system ::bind function
   // from anywhere in the current process
   DetourTransactionBegin();
   DetourUpdateThread(GetCurrentThread());
   DetourAttach(&(PVOID&)Original_bind, Intercept_bind);
   LONG error = DetourTransactionCommit();

   gFailBindCalls = true;

   TestAccount charlie("charlie", Account_NoInit);

   resip::Uri charlieTarget;
   charlieTarget.user() = charlie.config.settings.username;
   charlieTarget.host() = "127.0.0.1";
   charlieTarget.port() = 50001;

   charlie.config.settings.minSipPort = charlieTarget.port() - 1;
   charlie.config.settings.maxSipPort = charlieTarget.port() - 1;
   charlie.init();
   charlie.enable();

   // Charlie should be reachable on port 50001 given the bind for 50000 failed
   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);

   bob.conversation->addParticipant(bobCall, charlieTarget.getAOR(true).c_str());
   bob.conversation->start(bobCall);
   auto bobConversationEvents = std::async(std::launch::async, [&]() {
      // Wait for the expected state changes and for the call to end

      SipConversationHandle h;
      NewConversationEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
         "SipConversationHandler::onNewConversation",
         5000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(evt.account, bob.handle);

      assertConversationStateChanged(bob, bobCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });
   auto charlieConversationEvents = std::async(std::launch::async, [&]() {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle charlieCall;
      assertNewConversationIncoming(charlie, &charlieCall, bob.config.uri());
      charlie.conversation->sendRingingResponse(charlieCall);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_LocalRinging);
      charlie.conversation->accept(charlieCall);
      assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_Connected);
      charlie.conversation->end(charlieCall);
      assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedLocally);
   });
   waitFor2(bobConversationEvents, charlieConversationEvents);

   // TODO: cleanup detours
} 

TEST_F(BasicCallTests, NetworkChangePortInuse_NoRegistrar)
{
   TestAccount bob("bob", Account_NoInit);
   bob.config.settings.useRegistrar = false;
   bob.config.settings.domain = "bogus.invalid";
   bob.config.settings.outboundProxy = "";
   bob.config.settings.sipTransportType = SipAccountTransport_UDP;
   bob.init();
   bob.enable();

   TestAccount charlie("charlie", Account_NoInit);

   const int charlieOriginalListenPort = 50000;

   resip::Uri charlieTarget;
   charlieTarget.user() = charlie.config.settings.username;
   charlieTarget.host() = "127.0.0.1";
   // + 1 because this test will block charlie's re-bind to charlieOriginalListenPort post network change,
   // causing the SDK to move up a port
   charlieTarget.port() = charlieOriginalListenPort + 1;

   charlie.config.settings.sipTransportType = SipAccountTransport_UDP;
   charlie.config.settings.minSipPort = charlieOriginalListenPort;
   charlie.config.settings.maxSipPort = charlieOriginalListenPort;
   charlie.config.settings.useRegistrar = false;
   charlie.init();
   charlie.enable();

   // this code allows us intercept calls to the system ::bind function
   // from anywhere in the current process
   DetourTransactionBegin();
   DetourUpdateThread(GetCurrentThread());
   DetourAttach(&(PVOID&)Original_bind, Intercept_bind);
   LONG error = DetourTransactionCommit();

   gFailBindCalls = true;

   charlie.network->setNetworkTransport(TransportWWAN);

   assertAccountDeregistering(charlie);
   assertAccountDeregistered(charlie);
   //extra unregistered event for useRegistrar=false?
   assertAccountDeregistered(charlie);
   assertAccountRegistering(charlie);
   assertAccountRegistered(charlie);

   // Bob calls Charlie then Charlie hangs up
   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);

   bob.conversation->addParticipant(bobCall, charlieTarget.getAOR(true).c_str());
   bob.conversation->start(bobCall);
   auto bobConversationEvents = std::async(std::launch::async, [&]() {
      // Wait for the expected state changes and for the call to end

      SipConversationHandle h;
      NewConversationEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
         "SipConversationHandler::onNewConversation",
         5000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(evt.account, bob.handle);

      assertConversationStateChanged(bob, bobCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      // note we need to end it from bob; if we end from Charlie, the BYE will fail as
      // bob does not have a port in his Contact header.
      bob.conversation->end(bobCall);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });
   auto charlieConversationEvents = std::async(std::launch::async, [&]() {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle charlieCall;
      assertNewConversationIncoming(charlie, &charlieCall, bob.config.uri());
      charlie.conversation->sendRingingResponse(charlieCall);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_LocalRinging);
      charlie.conversation->accept(charlieCall);
      assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_Connected);
      assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedRemotely);
   });
   waitFor2(bobConversationEvents, charlieConversationEvents);

   gFailBindCalls = false;
   DetourTransactionBegin();
   DetourUpdateThread(GetCurrentThread());
   DetourDetach(&(PVOID&)Original_bind, Intercept_bind);
   error = DetourTransactionCommit();

   bob.disable(false, false);
   charlie.disable(false, false);
}


#endif // #ifdef _WIN32

// OBELISK-6205: disabled until AudioImpl timer is updated not to fire when no audio session is active
TEST_F(BasicCallTests, DISABLED_OBELISK_6116) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   CPCAPI2::Media::AudioDeviceListUpdatedEvent evt;
   int handle = 0;
   ASSERT_EQ(alice.audio->queryDeviceList(), kSuccess);
   ASSERT_TRUE(alice.mediaEvents->expectEvent("AudioHandler::onAudioDeviceListUpdated", 30000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
   ASSERT_NE(evt.deviceInfo.size(), 0);

   alice.enableOnlyThisCodec("OPUS");

   MediaStackSettings mediaStackSettings;
   mediaStackSettings.audioLayer = AudioLayers_Dummy;
   alice.media->updateMediaSettings(mediaStackSettings);
   bob.media->updateMediaSettings(mediaStackSettings);

   bob.audio->setSpeakerVolume(100);
   alice.audio->setEchoCancellationMode(AudioDeviceRole_Headset, EchoCancellationMode_None);
   alice.audio->setNoiseSuppressionMode(AudioDeviceRole_Headset, NoiseSuppressionMode_None);
   bob.audio->setEchoCancellationMode(AudioDeviceRole_Headset, EchoCancellationMode_None);
   bob.audio->setNoiseSuppressionMode(AudioDeviceRole_Headset, NoiseSuppressionMode_None);

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   MediaInfo aliceMedia;
   aliceMedia.mediaDirection = MediaDirection_SendOnly;
   alice.conversation->configureMedia(aliceCall, aliceMedia);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]() {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendOnly);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      alice.conversation->playSound(aliceCall, "file:" + TestEnvironmentConfig::testResourcePath() + "baseball.wav", true);

      SystemAudioServiceErrorEvent evt;
      ASSERT_TRUE(alice.mediaEvents->expectEvent("AudioHandler::onSystemAudioServiceError", 10000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
      ASSERT_EQ(evt.errorLevel, SystemAudioServiceErrorLevel_ActiveRecording);
      ASSERT_FALSE(alice.mediaEvents->expectEvent("AudioHandler::onSystemAudioServiceError", 10000, CPCAPI2::test::AlwaysTruePred(), handle, evt));

      {
         std::shared_ptr<void> scoped = { nullptr, [](void*){
            webrtc::AudioDeviceDummy::setAudioDeviceDummyMicrophoneAudioFlowControl(NULL);
         }};

         webrtc::AudioDeviceDummy::setAudioDeviceDummyMicrophoneAudioFlowControl([](){ return false; });

         ASSERT_TRUE(alice.mediaEvents->expectEvent("AudioHandler::onSystemAudioServiceError", 10000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
         ASSERT_EQ(evt.errorLevel, SystemAudioServiceErrorLevel_InactiveRecording);
         ASSERT_FALSE(alice.mediaEvents->expectEvent("AudioHandler::onSystemAudioServiceError", 10000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
      }

      ASSERT_TRUE(alice.mediaEvents->expectEvent("AudioHandler::onSystemAudioServiceError", 10000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
      ASSERT_EQ(evt.errorLevel, SystemAudioServiceErrorLevel_ActiveRecording);
      ASSERT_FALSE(alice.mediaEvents->expectEvent("AudioHandler::onSystemAudioServiceError", 10000, CPCAPI2::test::AlwaysTruePred(), handle, evt));

      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      SipConversationHandle bobCall;

      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      MediaInfo bobMedia;
      bobMedia.mediaDirection = MediaDirection_ReceiveOnly;
      bob.conversation->configureMedia(bobCall, bobMedia);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_ReceiveOnly);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      bob.audio->setMicMute(true);

      std::stringstream recordFileName;
      recordFileName << "AudioQualityTests.FileOpusTest-" << resip::Random::getCryptoRandomHex(6).c_str() << ".wav";
      CPCAPI2::Recording::RecorderHandle recorderHandle = bob.recording->audioRecorderCreate(recordFileName.str().c_str());
      assertSuccess(bob.recording->recorderAddConversation(recorderHandle, bobCall));
      assertSuccess(bob.recording->recorderStart(recorderHandle));

      std::this_thread::sleep_for(std::chrono::seconds(50));

      assertSuccess(bob.recording->recorderDestroy(recorderHandle));

      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor2(aliceEvents, bobEvents);
}

#if ((defined( __APPLE__) && defined(__aarch64__)) || (defined(__linux__) && !defined(ANDROID)))
TEST_F(BasicCallTests, Visqol_Quality_Test) {

#if defined(__linux)
   GTEST_SKIP() << "This test returns very low MOS values in Linux test runs on jenkins; skipping";
#endif


   TestAccount alice("alice");
   alice.enableOnlyThisCodec("OPUS");
   
   TestAccount bob("bob");
   bob.enableOnlyThisCodec("OPUS");
   
   OpusPacsizeLogChecker bobLogchecker;
   std::function<void(const char *message, const char* subsystem, CPCAPI2::LogLevel level)> f = std::bind(&OpusPacsizeLogChecker::loggingListener,
                                                                                                      &bobLogchecker, std::placeholders::_1,
                                                                                                      std::placeholders::_2, std::placeholders::_3);

   AutoTestsLogger::ScopedMessageListenerFunction smlf(f);
   
   TurnAsyncUdpSocket_OutgoingPacketlossInducer::Config udpLossConfig;
   udpLossConfig.lossRatePct = 0;
   TurnAsyncUdpSocket_OutgoingPacketlossInducer plu(udpLossConfig);
   
   SipConversationSettings settings;
   // need to set same non-empty session name to enable opus dynamic bitrate adjustment
   settings.sessionName = "cpcapi2tests";
   alice.conversation->setDefaultSettings(alice.handle, settings);
   bob.conversation->setDefaultSettings(bob.handle, settings);
   alice.audio->setEchoCancellationMode(AudioDeviceRole_Headset, EchoCancellationMode_None);
   alice.audio->setNoiseSuppressionMode(AudioDeviceRole_Headset, NoiseSuppressionMode_None);
   bob.audio->setEchoCancellationMode(AudioDeviceRole_Headset, EchoCancellationMode_None);
   bob.audio->setNoiseSuppressionMode(AudioDeviceRole_Headset, NoiseSuppressionMode_None);
   alice.audio->setMicMute(true);
   bob.audio->setMicMute(true);
   
   
   std::string soundClipUri;
#ifdef ANDROID
   soundClipUri = "android.resource://com.counterpath.sdkdemo.advancedaudiocall/raw/baseball";
#else
   soundClipUri = "file:" + TestEnvironmentConfig::testResourcePath() + "audio/f2btrop6.0.wav";
   //Make sure audio file is either 16khz or 48khz. ViSQOL audio mode prefers 48khz, but it works fine with 16khz, speech mode prefers 16khz, unreliable results with 48khz. Make sure recorded file has the same wavelength as this file allocated by path to "soundClipUri"
#endif

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);
   
   const int callDurationSec = 8; //visqol limits wav file length to 8-10 seconds

   auto aliceEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      
      alice.conversation->playSound(aliceCall, soundClipUri.c_str(), true);
      
      SipConversationState aliceConvState;
      alice.conversationState->getState(aliceCall, aliceConvState);
      ASSERT_NE(aliceConvState.localMediaInfo.size(), 0);

      std::this_thread::sleep_for(std::chrono::seconds(callDurationSec));

      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });
   std::stringstream recordFileName;
   SipConversationHandle bobCall;
   auto bobEvents = std::async(std::launch::async, [&] () {
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      SipConversationState bobConvState;
      ASSERT_EQ(bob.conversationState->getState(bobCall, bobConvState), kSuccess);
      ASSERT_NE(bobConvState.localMediaInfo.size(), 0);

      //recording logic from AudioQualityTests, DISABLED_FileOpusTest
      
      recordFileName << "f2btrop30p" << resip::Random::getCryptoRandomHex(6).c_str() << ".wav";
      CPCAPI2::Recording::RecorderHandle recorderHandle = bob.recording->audioRecorderCreate(recordFileName.str().c_str());
      assertSuccess(bob.recording->recorderAddConversation(recorderHandle, bobCall));
      assertSuccess(bob.recording->recorderStart(recorderHandle));
      
      
      std::this_thread::sleep_for(std::chrono::seconds(callDurationSec));
      
      
      assertSuccess(bob.recording->recorderDestroy(recorderHandle));
      
      
      
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);

      ASSERT_EQ(bob.conversationState->getState(bobCall, bobConvState), kSuccess);

      ASSERT_EQ(1, bobConvState.statistics.audioChannels.size());
      ASSERT_GT(bobConvState.statistics.audioChannels[0].streamDataCounters.packetsReceived, 0);
      
      if (udpLossConfig.lossRatePct > 0)
      {
         ASSERT_GT(udpLossConfig.lossRatePct, 3);
         int expectedPacketloss = udpLossConfig.lossRatePct - /* allow underreporting of loss */ 5;
      
         ASSERT_GE(bobConvState.statistics.audioChannels[0].streamStatistics.cumulativeLost, 0);
         
         float cumulativeLost = bobConvState.statistics.audioChannels[0].streamStatistics.cumulativeLost;
         float cumulativeReceived = bobConvState.statistics.audioChannels[0].streamDataCounters.packetsReceived;
         ASSERT_GE(cumulativeLost / cumulativeReceived * 100, expectedPacketloss);
         
      }
   });

   noReturnWaitFor2(aliceEvents, bobEvents);
   
#ifdef ANDROID
   soundClipUri = "android.resource://com.counterpath.sdkdemo.advancedaudiocall/raw/baseball";
#else
   soundClipUri = "runtime_resources/audio/f2btrop6.0.wav";
#endif
   std::string testQuality = recordFileName.str();
   double threshold_mos = 4.2;
#if defined(__linux)
   threshold_mos = 3.6; // linux builds on jenkins currently show lower mos value; needs investigation
#endif

   double test_mos = 0;
   VisqolRunner::visqol_exec(soundClipUri, testQuality, test_mos);
   safeCout("printing mos: " << test_mos);
   ASSERT_GE(test_mos, threshold_mos); //example test for threshold, may vary depending on packet loss or sample rate
}
#endif // #if ((defined( __APPLE__) && defined(__aarch64__)) || (defined(__linux__) && !defined(ANDROID)))


#if ((defined( __APPLE__) && defined(__aarch64__)) || (defined(__linux__) && !defined(ANDROID)))
TEST_F(BasicCallTests, ConferenceCallVisqol) {

#if defined(__linux)
   GTEST_SKIP() << "This test returns very low MOS values in Linux test runs on jenkins; skipping";
#endif


   TestAccount alice("alice");
   TestAccount bob("bob");
   TestAccount max("max");
   alice.enableOnlyThisCodec("OPUS");
   bob.enableOnlyThisCodec("OPUS");
   max.enableOnlyThisCodec("OPUS");
   SipConversationSettings settings;
   // need to set same non-empty session name to enable opus dynamic bitrate adjustment
   settings.sessionName = "cpcapi2tests";
   max.conversation->setDefaultSettings(max.handle, settings);
   alice.conversation->setDefaultSettings(alice.handle, settings);
   bob.conversation->setDefaultSettings(bob.handle, settings);

   max.audio->setEchoCancellationMode(AudioDeviceRole_Headset, EchoCancellationMode_None);
   max.audio->setNoiseSuppressionMode(AudioDeviceRole_Headset, NoiseSuppressionMode_None);
   alice.audio->setEchoCancellationMode(AudioDeviceRole_Headset, EchoCancellationMode_None);
   alice.audio->setNoiseSuppressionMode(AudioDeviceRole_Headset, NoiseSuppressionMode_None);
   bob.audio->setEchoCancellationMode(AudioDeviceRole_Headset, EchoCancellationMode_None);
   bob.audio->setNoiseSuppressionMode(AudioDeviceRole_Headset, NoiseSuppressionMode_None);

   max.audio->setMicMute(true);
   alice.audio->setMicMute(true);
   bob.audio->setMicMute(true);

   std::string referenceSoundClipUri;
#ifdef ANDROID
   referenceSoundClipUri = "android.resource://com.counterpath.sdkdemo.advancedaudiocall/raw/baseball";
#else
   referenceSoundClipUri = "file:" + TestEnvironmentConfig::testResourcePath() + "audio/f2btrop6.0.wav";
   //Make sure audio file is either 16khz or 48khz. ViSQOL audio mode prefers 48khz, but it works fine with 16khz, speech mode prefers 16khz, unreliable results with 48khz. Make sure recorded file has the same wavelength as this file allocated by path to "soundClipUri"
#endif

   std::atomic_bool isConference = false;
   std::atomic_bool maxCallEnded = false;

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);
   const int callDurationSec = 8;

   auto aliceEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendOnly);

      assertSuccess(alice.conversation->accept(aliceCall));
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_ReceiveOnly);
      assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendReceive);
      assertSuccess(alice.conversation->accept(aliceCall));
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);

      while (!isConference) {
         std::this_thread::yield();
      }
      alice.conversation->playSound(aliceCall, referenceSoundClipUri.c_str(), true);

      SipConversationState aliceConvState;
      alice.conversationState->getState(aliceCall, aliceConvState);
      ASSERT_NE(aliceConvState.localMediaInfo.size(), 0);

      std::this_thread::sleep_for(std::chrono::seconds(callDurationSec));
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });
   std::stringstream recordAliceToBob;
   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle aliceCallToBob;
      assertNewConversationIncoming(bob, &aliceCallToBob, alice.config.uri());
      // Bob sends 180 ringing -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->sendRingingResponse(aliceCallToBob));
      assertConversationStateChanged(bob, aliceCallToBob, ConversationState_LocalRinging);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->accept(aliceCallToBob));
      assertConversationMediaChanged(bob, aliceCallToBob, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, aliceCallToBob, ConversationState_Connected);
      SipConversationState bobConvState;
            ASSERT_EQ(bob.conversationState->getState(aliceCallToBob, bobConvState), kSuccess);
            ASSERT_NE(bobConvState.localMediaInfo.size(), 0);
      assertSuccess(bob.conversation->hold(aliceCallToBob));
      assertConversationMediaChanged(bob, aliceCallToBob, MediaDirection_SendOnly);

      // make an outgoing (audio only) call from Bob to Max
      SipConversationHandle bobCallToMax = bob.conversation->createConversation(bob.handle);
      bob.conversation->addParticipant(bobCallToMax, max.config.uri());
      bob.conversation->start(bobCallToMax);

      // BOB <=> MAX
      assertNewConversationOutgoing(bob, bobCallToMax, max.config.uri());
      assertConversationStateChanged(bob, bobCallToMax, ConversationState_RemoteRinging);
      assertConversationMediaChanged(bob, bobCallToMax, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCallToMax, ConversationState_Connected);

      // make this a conference by un-holding all calls
      assertSuccess(bob.conversation->unhold(aliceCallToBob));
      assertConversationMediaChanged(bob, aliceCallToBob, MediaDirection_SendReceive);

      isConference = true;
      while (!maxCallEnded) {
         std::this_thread::yield();
      }
      assertSuccess(bob.conversation->end(bobCallToMax));
      assertConversationEnded(bob, bobCallToMax, ConversationEndReason_UserTerminatedLocally);

      // BOB <=> ALICE
      assertConversationEnded(bob, aliceCallToBob, ConversationEndReason_UserTerminatedRemotely);
   });

   // Overview of Max's thread:
   std::stringstream recordAliceToMax;
   auto maxEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle maxCallFromBob;
      assertNewConversationIncoming(max, &maxCallFromBob, bob.config.uri());
      // Bob sends 180 ringing -- this should trigger state transitions for both Bob and Max
      assertSuccess(max.conversation->sendRingingResponse(maxCallFromBob));
      assertConversationStateChanged(max, maxCallFromBob, ConversationState_LocalRinging);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(max.conversation->accept(maxCallFromBob));
      assertConversationMediaChanged(max, maxCallFromBob, MediaDirection_SendReceive);
      assertConversationStateChanged(max, maxCallFromBob, ConversationState_Connected);

      while (!isConference) {
         std::this_thread::yield();
      }
      SipConversationState maxConvState;
      ASSERT_EQ(max.conversationState->getState(maxCallFromBob, maxConvState), kSuccess);
      ASSERT_NE(maxConvState.localMediaInfo.size(), 0);

      //recording logic from AudioQualityTests, DISABLED_FileOpusTest
      recordAliceToMax << "BasicCallTests.ConferenceCallVisqol_" << resip::Random::getCryptoRandomHex(6).c_str() << ".wav";
      CPCAPI2::Recording::RecorderHandle recorderHandle = max.recording->audioRecorderCreate(recordAliceToMax.str().c_str(), false);
      assertSuccess(max.recording->recorderAddConversation(recorderHandle, maxCallFromBob));
      assertSuccess(max.recording->recorderStart(recorderHandle));
      std::this_thread::sleep_for(std::chrono::seconds(callDurationSec));

      assertSuccess(max.recording->recorderDestroy(recorderHandle));
      maxCallEnded = true;

      assertConversationEnded(max, maxCallFromBob, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor3(aliceEvents, bobEvents, maxEvents);
#ifdef ANDROID
   soundClipUri = "android.resource://com.counterpath.sdkdemo.advancedaudiocall/raw/baseball";
#else
   referenceSoundClipUri = "runtime_resources/audio/f2btrop6.0.wav";
#endif

   double aliceToMaxMOS = 0.0;
   VisqolRunner::visqol_exec(referenceSoundClipUri, recordAliceToMax.str(), aliceToMaxMOS);
   safeCout("Alice to Max MOS-LQO: " << aliceToMaxMOS);
   double threshold_mos = 4.1;
#if defined(__linux)
   threshold_mos = 2.0; // linux builds on jenkins currently show lower mos value; needs investigation
#endif
   std::filesystem::rename(std::filesystem::path(recordAliceToMax.str()), TestEnvironmentConfig::artifactFilePath() / recordAliceToMax.str());

   ASSERT_GE(aliceToMaxMOS, threshold_mos); 
}
#endif // #if ((defined( __APPLE__) && defined(__aarch64__)) || (defined(__linux__) && !defined(ANDROID)))

TEST_F(BasicCallTests, DISABLED_SwisscomLocalHoldRemoteHoldTwice)
{
   std::string starcode = "*111";

   TestAccount alice("alice", Account_NoInit);
   TestAccount bob("bob", Account_NoInit);

   MediaInfo media;
   media.mediaEncryptionOptions.mediaCryptoSuites.clear();
   cpc::vector<MediaCryptoSuite> cryptos;
   cryptos.push_back(MediaCryptoSuite_AEAD_AES_128_GCM);
   cryptos.push_back(MediaCryptoSuite_AEAD_AES_256_GCM);
   cryptos.push_back(MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);
   cryptos.push_back(MediaCryptoSuite_AES_CM_192_HMAC_SHA1_80);
   cryptos.push_back(MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80);
   cryptos.push_back(MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);
   cryptos.push_back(MediaCryptoSuite_AES_CM_192_HMAC_SHA1_32);
   cryptos.push_back(MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32);
   configureMedia(media, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, cryptos);

   alice.config.settings.domain = "imst.swisscom.ch";
   alice.config.settings.outboundProxy = "ssc1.imst.swisscom.ch:5076";
   alice.config.settings.username = "+***********-01";
   alice.config.settings.password = R"()E[OTS^\a9=i >)";
   alice.config.settings.useOutbound = false;
   alice.config.settings.useRport = false;
   alice.config.settings.displayName = "";
   alice.config.settings.registrationIntervalSeconds = 900;
   alice.config.settings.sipTransportType = SipAccountTransport_TLS;
   alice.config.settings.auth_username = "<EMAIL>";
   alice.config.settings.userAgent = "Softphone - Enterprise Telephony";
   const cpc::string aliceExt = "sip:+<EMAIL>;user=phone";

   SipConversationSettings settings;
   settings.networkChangeHandoverMode = NetworkChangeHandoverMode_Starcode;
   settings.networkChangeHandoverStarcode = starcode.c_str();
   std::stringstream starcodeUrl;
   starcodeUrl << "sip:" << starcode << "@" << alice.config.settings.domain;

   alice.init();

   // SwisscomConversationAdornmentHandler adornmentHandler(alice);
   // alice.conversation->setAdornmentHandler(alice.handle, &adornmentHandler);
   SipAccountManagerInternal* accountMgr = dynamic_cast<SipAccountManagerInternal*>(alice.account);
   ASSERT_TRUE(accountMgr != NULL);
   accountMgr->setIgnoreNetworkChangeStarcodeFilter(alice.handle, false);

   alice.enable();

   bob.config.settings.domain = "imst.swisscom.ch";
   bob.config.settings.outboundProxy = "ssc1.imst.swisscom.ch:5076";
   bob.config.settings.username = "+***********-01";
   bob.config.settings.password = "YsrS&_tMsDp}UM";
   bob.config.settings.useOutbound = false;
   bob.config.settings.useRport = false;
   bob.config.settings.displayName = "";
   bob.config.settings.registrationIntervalSeconds = 900;
   bob.config.settings.sipTransportType = SipAccountTransport_TLS;
   bob.config.settings.auth_username = "<EMAIL>";
   bob.config.settings.userAgent = "Softphone - Enterprise Telephony";
   const cpc::string bobExt = "sip:+<EMAIL>";
   bob.init();
   bob.enable();

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   int maxNetworkChanges = 1;

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      // Establish call between alice and bob
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->setDefaultSettings(alice.handle, settings);
      alice.conversation->configureMedia(aliceCall, media);
      alice.conversation->addParticipant(aliceCall, bobExt.c_str());
      alice.conversation->start(aliceCall);

      assertNewConversationOutgoing(alice, aliceCall, bobExt.c_str());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(6000));
      assertMediaFlowing(alice, aliceCall, true, false);

      // Sequence #1: Local Hold
      assertSuccess(alice.conversation->hold(aliceCall));
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt)
      {
         ASSERT_TRUE(evt.localHold);
         ASSERT_TRUE(evt.remoteHold); // Swisscom returns inactive status in SDP
         ASSERT_EQ(MediaDirection_Inactive, evt.localMediaInfo[0].mediaDirection);
      });
      // std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      // Sequence #4: Remote Hold
      // Did not receive any signaling resulting from the Bob holding the held call
      /*
      assertConversationMediaChangeRequest_time(alice, aliceCall, MediaDirection_SendOnly, 30000);
      assertSuccess(alice.conversation->accept(aliceCall));
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt)
      {
         ASSERT_TRUE(evt.localHold);
         ASSERT_TRUE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_Inactive, evt.localMediaInfo[0].mediaDirection);
      });

      // Received an extra send-recv INVITE for MoH from Swisscom
      assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendReceive);
      assertSuccess(alice.conversation->accept(aliceCall));
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt)
      {
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      */

      assertSuccess(alice.conversation->unhold(aliceCall));
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt)
      {
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold); // Swisscom responded with send-recv even though Bob was on local hold
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });

      assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendOnly);
      assertSuccess(alice.conversation->accept(aliceCall));
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt)
      {
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCall, true, false);

      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall;

      assertNewConversationIncoming(bob, &bobCall, aliceExt.c_str());
      // assertNewConversationIncoming(bob, &bobCall, "sip:+<EMAIL>");
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));

      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      // Sequence #2: Remote Hold
      assertConversationMediaChangeRequest_time(bob, bobCall, MediaDirection_SendOnly, 30000);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt)
      {
         ASSERT_FALSE(evt.localHold);
         ASSERT_TRUE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_ReceiveOnly, evt.localMediaInfo[0].mediaDirection);
      });

      // Received an extra send-recv INVITE for MoH from Swisscom
      assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt)
      {
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });

      // Sequence #3: Local Hold
      assertSuccess(bob.conversation->hold(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt)
      {
         ASSERT_TRUE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold); // Swisscom returns send-only status in SDP
         ASSERT_EQ(MediaDirection_SendOnly, evt.localMediaInfo[0].mediaDirection);
      });
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendOnly);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt)
      {
         ASSERT_TRUE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });

      assertSuccess(bob.conversation->unhold(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt)
      {
         ASSERT_TRUE(evt.localHold);
         ASSERT_TRUE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(bob, bobCall, true, false);

      std::this_thread::sleep_for(std::chrono::milliseconds(10000));

      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor2Ms(aliceEvents, bobEvents, std::chrono::milliseconds(60000 + (30000 * maxNetworkChanges)));
}

TEST_F(BasicCallTests, DISABLED_SwisscomMultipleIncomingCalls)
{
   std::string starcode = "*111";

   TestAccount alice("alice", Account_NoInit);
   TestAccount bob("bob", Account_NoInit);
   TestAccount max("max", Account_NoInit);

   MediaInfo media;
   media.mediaEncryptionOptions.mediaCryptoSuites.clear();
   cpc::vector<MediaCryptoSuite> cryptos;
   cryptos.push_back(MediaCryptoSuite_AEAD_AES_128_GCM);
   cryptos.push_back(MediaCryptoSuite_AEAD_AES_256_GCM);
   cryptos.push_back(MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);
   cryptos.push_back(MediaCryptoSuite_AES_CM_192_HMAC_SHA1_80);
   cryptos.push_back(MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80);
   cryptos.push_back(MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);
   cryptos.push_back(MediaCryptoSuite_AES_CM_192_HMAC_SHA1_32);
   cryptos.push_back(MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32);
   configureMedia(media, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, cryptos);

   alice.config.settings.domain = "imst.swisscom.ch";
   alice.config.settings.outboundProxy = "ssc1.imst.swisscom.ch:5076";
   alice.config.settings.username = "+***********-01";
   alice.config.settings.password = R"()E[OTS^\a9=i >)";
   alice.config.settings.useOutbound = false;
   alice.config.settings.useRport = false;
   alice.config.settings.displayName = "";
   alice.config.settings.registrationIntervalSeconds = 900;
   alice.config.settings.sipTransportType = SipAccountTransport_TLS;
   alice.config.settings.auth_username = "<EMAIL>";
   alice.config.settings.userAgent = "Softphone - Enterprise Telephony";
   const cpc::string aliceExt = "sip:+<EMAIL>;user=phone";

   SipConversationSettings settings;
   settings.networkChangeHandoverMode = NetworkChangeHandoverMode_Starcode;
   settings.networkChangeHandoverStarcode = starcode.c_str();
   std::stringstream starcodeUrl;
   starcodeUrl << "sip:" << starcode << "@" << alice.config.settings.domain;

   alice.init();

   SipAccountManagerInternal* accountMgr = dynamic_cast<SipAccountManagerInternal*>(alice.account);
   ASSERT_TRUE(accountMgr != NULL);
   accountMgr->setIgnoreNetworkChangeStarcodeFilter(alice.handle, false);

   alice.enable();

   bob.config.settings.domain = "imst.swisscom.ch";
   bob.config.settings.outboundProxy = "ssc1.imst.swisscom.ch:5076";
   bob.config.settings.username = "+***********-01";
   bob.config.settings.password = "YsrS&_tMsDp}UM";
   bob.config.settings.useOutbound = false;
   bob.config.settings.useRport = false;
   bob.config.settings.displayName = "";
   bob.config.settings.registrationIntervalSeconds = 900;
   bob.config.settings.sipTransportType = SipAccountTransport_TLS;
   bob.config.settings.auth_username = "<EMAIL>";
   bob.config.settings.userAgent = "Softphone - Enterprise Telephony";
   const cpc::string bobExt = "sip:+<EMAIL>";
   bob.init();
   bob.enable();

   /*
   max.config.settings.domain = "imst.swisscom.ch";
   max.config.settings.outboundProxy = "ssc1.imst.swisscom.ch:5076";
   max.config.settings.username = "+***********-01";
   max.config.settings.password = "";
   max.config.settings.useOutbound = false;
   max.config.settings.useRport = false;
   max.config.settings.displayName = "";
   max.config.settings.registrationIntervalSeconds = 900;
   max.config.settings.sipTransportType = SipAccountTransport_TLS;
   max.config.settings.auth_username = "<EMAIL>";
   max.config.settings.userAgent = "Softphone - Enterprise Telephony";
   const cpc::string maxExt = "sip:+<EMAIL>";
   max.init();
   max.enable(); */

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   int maxNetworkChanges = 1;

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      // Establish call between alice and bob
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->setDefaultSettings(alice.handle, settings);
      alice.conversation->configureMedia(aliceCall, media);
      alice.conversation->addParticipant(aliceCall, bobExt.c_str());
      alice.conversation->start(aliceCall);

      assertNewConversationOutgoing(alice, aliceCall, bobExt.c_str());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(6000));
      assertMediaFlowing(alice, aliceCall, true, false);

      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall;

      assertNewConversationIncoming(bob, &bobCall, aliceExt.c_str());
      // assertNewConversationIncoming(bob, &bobCall, "sip:+<EMAIL>");
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));

      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor2Ms(aliceEvents, bobEvents, std::chrono::milliseconds(60000 + (30000 * maxNetworkChanges)));
}

TEST_F(BasicCallTests, IncomingInviteWithSessionId) {
   TestAccount alice("alice", Account_Init);
   TestAccount bob("bob");

   const cpc::string expectedSessionId = "0923191831474657b431c3d173ec5042972f1932";

   class MySipConversationAdornmentHandler : public SipConversationAdornmentHandler
   {
   public:
      MySipConversationAdornmentHandler(CPCAPI2::SipConversation::SipConversationManager* convMgr, const cpc::string& sessionId) : mConvMgr(convMgr), mSessionId(sessionId) {}

      virtual int onConversationAdornment(SipConversationHandle conversation, const ConversationAdornmentEvent& args)
      {
         CPCAPI2::SipHeader sipHeader;
         sipHeader.header = "Session-ID";
         sipHeader.value = mSessionId;
         cpc::vector<CPCAPI2::SipHeader> sipHeaders;
         sipHeaders.push_back(sipHeader);
         mConvMgr->adornMessage(conversation, args.adornmentMessageId, sipHeaders);
         return kSuccess;
      }

   private:
      CPCAPI2::SipConversation::SipConversationManager* mConvMgr;
      const cpc::string mSessionId;
   };
   MySipConversationAdornmentHandler adornmentHandler(alice.conversation, expectedSessionId);
   alice.conversation->setAdornmentHandler(alice.handle, &adornmentHandler);
   alice.enable();

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]() {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      SipConversationHandle bobCall;
      std::function<void(NewConversationEvent)> sessionIdValidator = [&](NewConversationEvent evt) { 
         ASSERT_EQ(evt.sessionId, expectedSessionId);
      };
      TestCallEvents::expectNewConversationIncoming(__LINE__, bob, &bobCall, alice.config.uri(), sessionIdValidator);
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor2(aliceEvents, bobEvents);
}

TEST_F(BasicCallTests, OutgoingInviteWithSessionId) {
   TestAccount alice("alice");
   TestAccount bob("bob", Account_Init);

   const cpc::string expectedSessionId = "0923191831474657b431c3d173ec5042972f1932";

   class MySipConversationAdornmentHandler : public SipConversationAdornmentHandler
   {
   public:
      MySipConversationAdornmentHandler(CPCAPI2::SipConversation::SipConversationManager* convMgr, const cpc::string& sessionId) : mConvMgr(convMgr), mSessionId(sessionId) {}

      virtual int onConversationAdornment(SipConversationHandle conversation, const ConversationAdornmentEvent& args)
      {
         CPCAPI2::SipHeader sipHeader;
         sipHeader.header = "Session-ID";
         sipHeader.value = mSessionId;
         cpc::vector<CPCAPI2::SipHeader> sipHeaders;
         sipHeaders.push_back(sipHeader);
         mConvMgr->adornMessage(conversation, args.adornmentMessageId, sipHeaders);
         return kSuccess;
      }

   private:
      CPCAPI2::SipConversation::SipConversationManager* mConvMgr;
      const cpc::string mSessionId;
   };
   MySipConversationAdornmentHandler adornmentHandler(bob.conversation, expectedSessionId);
   bob.conversation->setAdornmentHandler(bob.handle, &adornmentHandler);
   bob.enable();

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]() {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());

      assertConversationStateChanged_ex(alice, aliceCall, [&](const ConversationStateChangedEvent& evt)
      {
         ASSERT_EQ(evt.sessionId, expectedSessionId);
         ASSERT_EQ(evt.conversationState, ConversationState_RemoteRinging);
      });

      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);

      assertConversationStateChanged_ex(alice, aliceCall, [&](const ConversationStateChangedEvent& evt)
      {
         ASSERT_EQ(evt.sessionId, expectedSessionId);
         ASSERT_EQ(evt.conversationState, ConversationState_Connected);
      });

      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      SipConversationHandle bobCall;      
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor2(aliceEvents, bobEvents);
}

TEST_F(BasicCallTests, PopulateDiversion) {
   resip::SipMessage msg;
   cpc::string displayName, address, reason;

   cpc::string diversionHdrValue = "<sip:<EMAIL>>";
   SipHelpers::setHeader(msg, "Diversion", diversionHdrValue);
   SipHelpers::populateDiversion(msg, address, displayName, reason);
   ASSERT_EQ("sip:<EMAIL>", address);
   ASSERT_EQ("", displayName);
   ASSERT_EQ("", reason);

   diversionHdrValue = "\"Charles\"<sip:<EMAIL>>";
   SipHelpers::setHeader(msg, "Diversion", diversionHdrValue);
   SipHelpers::populateDiversion(msg, address, displayName, reason);
   ASSERT_EQ("sip:<EMAIL>", address);
   ASSERT_EQ("Charles", displayName);
   ASSERT_EQ("", reason);

   diversionHdrValue = "\"Charles\"<sip:<EMAIL>>;reason=unconditional";
   SipHelpers::setHeader(msg, "Diversion", diversionHdrValue);
   SipHelpers::populateDiversion(msg, address, displayName, reason);
   ASSERT_EQ("sip:<EMAIL>", address);
   ASSERT_EQ("Charles", displayName);
   ASSERT_EQ("unconditional", reason);

   displayName = "";
   diversionHdrValue = "<sip:<EMAIL>>;privacy=full;reason=unconditional;screen=no";
   SipHelpers::setHeader(msg, "Diversion", diversionHdrValue);
   SipHelpers::populateDiversion(msg, address, displayName, reason);
   ASSERT_EQ("sip:<EMAIL>", address);
   ASSERT_EQ("", displayName);
   ASSERT_EQ("unconditional", reason);

   diversionHdrValue = "\"Charles\"<sip:<EMAIL>>;privacy=full;reason=unconditional;screen=no";
   SipHelpers::setHeader(msg, "Diversion", diversionHdrValue);
   SipHelpers::populateDiversion(msg, address, displayName, reason);
   ASSERT_EQ("sip:<EMAIL>", address);
   ASSERT_EQ("Charles", displayName);
   ASSERT_EQ("unconditional", reason);

   displayName = "";
   reason = "";
   diversionHdrValue = "<sip:+<EMAIL>;user=phone>";
   SipHelpers::setHeader(msg, "Diversion", diversionHdrValue);
   SipHelpers::populateDiversion(msg, address, displayName, reason);
   ASSERT_EQ("sip:+<EMAIL>", address);
   ASSERT_EQ("", displayName);
   ASSERT_EQ("", reason);

   diversionHdrValue = "\"Andrea Barzini-Corleone\"<sip:+<EMAIL>;user=phone>;reason=deflection;counter=1";
   SipHelpers::setHeader(msg, "Diversion", diversionHdrValue);
   SipHelpers::populateDiversion(msg, address, displayName, reason);
   ASSERT_EQ("sip:+<EMAIL>", address);
   ASSERT_EQ("Andrea Barzini-Corleone", displayName);
   ASSERT_EQ("deflection", reason);
}

TEST_F(BasicCallTests, InviteWithDiversion) {
   TestAccount alice("alice", Account_Init);
   TestAccount bob("bob");

   const cpc::string diversionName = "Charlie";
   const cpc::string diversionAddr = "sip:<EMAIL>";
   const cpc::string diversionReason = "unconditional";
   const cpc::string diversionValue = "\"" + diversionName + "\"<" + diversionAddr + ">;reason=" + diversionReason;
   safeCout(diversionValue);

   class MySipConversationAdornmentHandler : public SipConversationAdornmentHandler
   {
   public:
      MySipConversationAdornmentHandler(CPCAPI2::SipConversation::SipConversationManager* convMgr, const cpc::string& value) : mConvMgr(convMgr), mValue(value) {}

      virtual int onConversationAdornment(SipConversationHandle conversation, const ConversationAdornmentEvent& args)
      {
         CPCAPI2::SipHeader sipHeader;
         sipHeader.header = "Diversion";
         sipHeader.value = mValue;
         cpc::vector<CPCAPI2::SipHeader> sipHeaders;
         sipHeaders.push_back(sipHeader);
         mConvMgr->adornMessage(conversation, args.adornmentMessageId, sipHeaders);
         return kSuccess;
      }

   private:
      CPCAPI2::SipConversation::SipConversationManager* mConvMgr;
      const cpc::string mValue;
   };
   MySipConversationAdornmentHandler adornmentHandler(alice.conversation, diversionValue);
   alice.conversation->setAdornmentHandler(alice.handle, &adornmentHandler);
   alice.enable();

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]() {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      SipConversationHandle bobCall;
      assertNewConversationIncoming_ex(bob, &bobCall, alice.config.uri(), [&](const NewConversationEvent& evt)
      {
         ASSERT_EQ(evt.conversationState, ConversationState_RemoteOriginated);
         ASSERT_EQ(evt.diversionAddress, diversionAddr);
         ASSERT_EQ(evt.diversionDisplayname, diversionName);
         ASSERT_EQ(evt.diversionReason, diversionReason);
      });
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor2(aliceEvents, bobEvents);
}
