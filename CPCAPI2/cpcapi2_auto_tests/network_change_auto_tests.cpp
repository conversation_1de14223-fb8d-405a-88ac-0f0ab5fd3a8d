#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#if (CPCAPI2_BRAND_NETWORK_CHANGE_MODULE == 1)

#include "cpcapi2_test_fixture.h"
#include "test_call_events.h"
#include "test_events.h"

#include <condition_variable>
#include <deque>

#include <gtest/gtest.h>
#include <cpcapi2.h>

#include "test_framework/cpcapi2_test_framework.h"
#include "test_framework/bare_bones_sip_endpoint.h"
#include "test_framework/network_utils.h"
#include "test_account_events.h"
#include "impl/account/SipAccountHandlerInternal.h"
#include "impl/call/SipConversationManagerInternal.h"
#include "impl/call/SipAVConversationManagerInterface.h"
#include "interface/experimental/account/SipNetworkProbeHandler.h"
#include "impl/call/SipConversationHandlerInternal.h"
#include "impl/account/SipAccountManagerInternal.h"

#include "resip/stack/SipMessage.hxx"
#include "resip/stack/SdpContents.hxx"
#include "resip/stack/Helper.hxx"
#include "resip/recon/RTPPortAllocator.hxx"

#include <thread>
#include <future>
#include <atomic>

using namespace CPCAPI2;
using namespace CPCAPI2::Media;
using namespace CPCAPI2::SipConversation;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::test;

namespace {

class MyNetworkChangePollingHandler;

class NetworkChangeTest : public CpcapiAutoTest
{
public:
   NetworkChangeTest() {}
   virtual ~NetworkChangeTest() {}

   static void expectNetworkInterfaceQueryEvent(int line, MyNetworkChangePollingHandler* pollingHandler, NetworkTransport transport, std::set<std::string>& interfaces, int waitMsecs = 5000);
   static void expectNetworkChangePublishEvent(int line, MyNetworkChangePollingHandler* pollingHandler, NetworkTransport transport, std::set<std::string>& interfaces, int waitMsecs = 5000);
   static void expectPollingStatusEvent(int line, MyNetworkChangePollingHandler* pollingHandler, NetworkTransport transport, NetworkChangePollingStateType status, int timeoutMs = 5000);
   static bool expectPollingStatusEventReceived(int line, MyNetworkChangePollingHandler* pollingHandler, int timeoutMs = 5000);
   static void expectPollingNotificationResetEvent(int line, MyNetworkChangePollingHandler* pollingHandler, int waitMsecs = 5000);
   static bool expectPollingNotificationResetEventReceived(int line, MyNetworkChangePollingHandler* pollingHandler, int waitMsecs = 5000);

   static void starcodeNetworkChange(TestAccount& account, std::string starcodeUrl, SipConversationHandle& newCall, SipConversationHandle callToReplace, NetworkTransport transport, int index);
   static void starcodeNetworkChangeNotTriggered(TestAccount& account, SipConversationHandle existingCall, NetworkTransport transport, int index);
};

repro::ReproRunner* runRepro(cpc::string config)
{
   repro::ReproRunner* res = new repro::ReproRunner();
   const char* const reproArgs[] = { "" };
   resip::Data configFile = (TestEnvironmentConfig::testResourcePath() + config).c_str();
   res->run(1, reproArgs, configFile);
   return res;
}

void runOriginalRepro(repro::ReproRunner* oldRepro = NULL)
{
   if (oldRepro)
   {
      oldRepro->shutdown();
      delete oldRepro;
      oldRepro = NULL;
   }

   const char* const reproArgs[] = { "" };
   resip::Data configFile = (TestEnvironmentConfig::testResourcePath() + "repro.config").c_str();
   ReproHolder::instance()->run(1, reproArgs, configFile);
}

TEST_F(NetworkChangeTest, ProbingDualStackNetworkV4PreferredV4RespondsFirst)
{
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.outboundProxy = "dualstack.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.ipVersion = IpVersion_Auto;
   alice.init();

   std::unique_ptr<MySipNetworkProbeHandler> aliceHandler(new MySipNetworkProbeHandler(&alice));
   CPCAPI2::SipAccount::SipAccountManagerInternal* mi = dynamic_cast<CPCAPI2::SipAccount::SipAccountManagerInternal*>(alice.account);
   mi->setProbeHandler(alice.handle, aliceHandler.get());
   mi->setProbeMockDelay(alice.handle, IpVersion_V6, 1250);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      alice.account->enable(alice.handle);

      CPCAPI2::SipAccount::SipNetworkProbeStatusChangedEvent evt;

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Probing;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ProbesSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 5000));
      ASSERT_EQ(evt.ipVersionPreferred, IpVersion_V4);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_PreferredResponseReceived;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 3000));
      ASSERT_EQ(evt.probeIpVersion, IpVersion_V4);
      ASSERT_EQ(evt.ipVersionSelected, IpVersion_V4);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_RegisterSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 2000));

      mi->setProbeHandler(alice.handle, NULL);
   });

   waitFor(aliceEvents);
}

TEST_F(NetworkChangeTest, ProbingDualStackNetworkV6PreferredV6RespondsFirst)
{
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.outboundProxy = "dualstack.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.ipVersion = IpVersion_Auto_PreferV6;
   alice.init();

   std::unique_ptr<MySipNetworkProbeHandler> aliceHandler(new MySipNetworkProbeHandler(&alice));
   CPCAPI2::SipAccount::SipAccountManagerInternal* mi = dynamic_cast<CPCAPI2::SipAccount::SipAccountManagerInternal*>(alice.account);
   mi->setProbeHandler(alice.handle, aliceHandler.get());
   mi->setProbeMockDelay(alice.handle, IpVersion_V4, 1250);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      alice.account->enable(alice.handle);

      CPCAPI2::SipAccount::SipNetworkProbeStatusChangedEvent evt;

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Probing;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ProbesSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 5000));
      ASSERT_EQ(evt.ipVersionPreferred, IpVersion_V6);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_PreferredResponseReceived;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 3000));
      ASSERT_EQ(evt.probeIpVersion, IpVersion_V6);
      ASSERT_EQ(evt.ipVersionSelected, IpVersion_V6);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_RegisterSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 2000));

      mi->setProbeHandler(alice.handle, NULL);
   });

   waitFor(aliceEvents);
}

TEST_F(NetworkChangeTest, ProbingDualStackNetworkV4PreferredV6RespondsFirst)
{
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.outboundProxy = "dualstack.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.ipVersion = IpVersion_Auto;
   alice.init();

   std::unique_ptr<MySipNetworkProbeHandler> aliceHandler(new MySipNetworkProbeHandler(&alice));
   CPCAPI2::SipAccount::SipAccountManagerInternal* mi = dynamic_cast<CPCAPI2::SipAccount::SipAccountManagerInternal*>(alice.account);
   mi->setProbeHandler(alice.handle, aliceHandler.get());
   mi->setProbeMockDelay(alice.handle, IpVersion_V4, 1250);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      alice.account->enable(alice.handle);

      CPCAPI2::SipAccount::SipNetworkProbeStatusChangedEvent evt;

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Probing;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ProbesSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 5000));
      ASSERT_EQ(evt.ipVersionPreferred, IpVersion_V4);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_ResponseReceived;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ResponseReceived;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 3000));
      ASSERT_EQ(evt.probeIpVersion, IpVersion_V6);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_PreferredResponseReceived;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 3000));
      ASSERT_EQ(evt.probeIpVersion, IpVersion_V4);
      ASSERT_EQ(evt.ipVersionSelected, IpVersion_V4);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_RegisterSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 2000));

      mi->setProbeHandler(alice.handle, NULL);
   });

   waitFor(aliceEvents);
}

TEST_F(NetworkChangeTest, ProbingDualStackNetworkV6PreferredV4RespondsFirst)
{
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.outboundProxy = "dualstack.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.ipVersion = IpVersion_Auto_PreferV6;
   alice.init();

   std::unique_ptr<MySipNetworkProbeHandler> aliceHandler(new MySipNetworkProbeHandler(&alice));
   CPCAPI2::SipAccount::SipAccountManagerInternal* mi = dynamic_cast<CPCAPI2::SipAccount::SipAccountManagerInternal*>(alice.account);
   mi->setProbeHandler(alice.handle, aliceHandler.get());
   mi->setProbeMockDelay(alice.handle, IpVersion_V6, 1250);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      alice.account->enable(alice.handle);

      CPCAPI2::SipAccount::SipNetworkProbeStatusChangedEvent evt;

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Probing;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ProbesSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 5000));
      ASSERT_EQ(evt.ipVersionPreferred, IpVersion_V6);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_ResponseReceived;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ResponseReceived;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 3000));
      ASSERT_EQ(evt.probeIpVersion, IpVersion_V4);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_PreferredResponseReceived;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 5000));
      ASSERT_EQ(evt.probeIpVersion, IpVersion_V6);
      ASSERT_EQ(evt.ipVersionSelected, IpVersion_V6);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_RegisterSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 2000));

      mi->setProbeHandler(alice.handle, NULL);
   });

   waitFor(aliceEvents);
}

TEST_F(NetworkChangeTest, ProbingDualStackNetworkV4PreferredNoDNSResultsOnlyV6Responds)
{
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.outboundProxy = "priv6.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.ipVersion = IpVersion_Auto;
   alice.init();

   std::unique_ptr<MySipNetworkProbeHandler> aliceHandler(new MySipNetworkProbeHandler(&alice));
   CPCAPI2::SipAccount::SipAccountManagerInternal* mi = dynamic_cast<CPCAPI2::SipAccount::SipAccountManagerInternal*>(alice.account);
   mi->setProbeHandler(alice.handle, aliceHandler.get());
   mi->setProbeMockDelay(alice.handle, IpVersion_V6, 1250);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      alice.account->enable(alice.handle);

      CPCAPI2::SipAccount::SipNetworkProbeStatusChangedEvent evt;

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Probing;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ProbesSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 5000));
      ASSERT_EQ(evt.ipVersionPreferred, IpVersion_V4);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_ResponseReceived;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_PreferredResponseReceived;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 3000));
      ASSERT_EQ(evt.probeIpVersion, IpVersion_V4);
      ASSERT_EQ(evt.signallingStatusCode, 503);
      ASSERT_EQ(evt.signallingResponseText, "No DNS results");

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ResponseReceived;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 4000));
      ASSERT_EQ(evt.probeIpVersion, IpVersion_V6);
      ASSERT_EQ(evt.ipVersionSelected, IpVersion_V6);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_RegisterSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 2000));

      mi->setProbeHandler(alice.handle, NULL);
   });

   waitFor(aliceEvents);
}

TEST_F(NetworkChangeTest, ProbingDualStackNetworkV6PreferredNoDNSResultsOnlyV4Responds)
{
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.outboundProxy = "priv4.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.ipVersion = IpVersion_Auto_PreferV6;
   alice.init();

   std::unique_ptr<MySipNetworkProbeHandler> aliceHandler(new MySipNetworkProbeHandler(&alice));
   CPCAPI2::SipAccount::SipAccountManagerInternal* mi = dynamic_cast<CPCAPI2::SipAccount::SipAccountManagerInternal*>(alice.account);
   mi->setProbeHandler(alice.handle, aliceHandler.get());
   mi->setProbeMockDelay(alice.handle, IpVersion_V4, 1250);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      alice.account->enable(alice.handle);

      CPCAPI2::SipAccount::SipNetworkProbeStatusChangedEvent evt;

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Probing;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ProbesSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 5000));
      ASSERT_EQ(evt.ipVersionPreferred, IpVersion_V6);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_ResponseReceived;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_PreferredResponseReceived;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 3000));
      ASSERT_EQ(evt.probeIpVersion, IpVersion_V6);
      ASSERT_EQ(evt.signallingStatusCode, 503);
      ASSERT_EQ(evt.signallingResponseText, "No DNS results");

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ResponseReceived;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 5000));
      ASSERT_EQ(evt.probeIpVersion, IpVersion_V4);
      ASSERT_EQ(evt.ipVersionSelected, IpVersion_V4);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_RegisterSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 2000));

      mi->setProbeHandler(alice.handle, NULL);
   });

   waitFor(aliceEvents);
}

TEST_F(NetworkChangeTest, ProbingDualStackNetworkV4PreferredNoDNSResultsOnlyV6RespondsFirst)
{
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.outboundProxy = "priv6.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.ipVersion = IpVersion_Auto;
   alice.init();

   std::unique_ptr<MySipNetworkProbeHandler> aliceHandler(new MySipNetworkProbeHandler(&alice));
   CPCAPI2::SipAccount::SipAccountManagerInternal* mi = dynamic_cast<CPCAPI2::SipAccount::SipAccountManagerInternal*>(alice.account);
   mi->setProbeHandler(alice.handle, aliceHandler.get());
   mi->setProbeMockDelay(alice.handle, IpVersion_V4, 1250);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      alice.account->enable(alice.handle);

      CPCAPI2::SipAccount::SipNetworkProbeStatusChangedEvent evt;

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Probing;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ProbesSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 5000));
      ASSERT_EQ(evt.ipVersionPreferred, IpVersion_V4);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_ResponseReceived;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ResponseReceived;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 3000));
      ASSERT_EQ(evt.probeIpVersion, IpVersion_V6);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_PreferredResponseReceived;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 3000));
      ASSERT_EQ(evt.probeIpVersion, IpVersion_V4);
      ASSERT_EQ(evt.ipVersionSelected, IpVersion_V6);
      ASSERT_EQ(evt.signallingStatusCode, 503);
      ASSERT_EQ(evt.signallingResponseText, "No DNS results");

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_RegisterSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 2000));

      mi->setProbeHandler(alice.handle, NULL);
   });

   waitFor(aliceEvents);
}

TEST_F(NetworkChangeTest, ProbingDualStackNetworkV6PreferredNoDNSResultsOnlyV4RespondsFirst)
{
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.outboundProxy = "priv4.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.ipVersion = IpVersion_Auto_PreferV6;
   alice.init();

   std::unique_ptr<MySipNetworkProbeHandler> aliceHandler(new MySipNetworkProbeHandler(&alice));
   CPCAPI2::SipAccount::SipAccountManagerInternal* mi = dynamic_cast<CPCAPI2::SipAccount::SipAccountManagerInternal*>(alice.account);
   mi->setProbeHandler(alice.handle, aliceHandler.get());
   mi->setProbeMockDelay(alice.handle, IpVersion_V6, 1250);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      alice.account->enable(alice.handle);

      CPCAPI2::SipAccount::SipNetworkProbeStatusChangedEvent evt;

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Probing;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ProbesSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 5000));
      ASSERT_EQ(evt.ipVersionPreferred, IpVersion_V6);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_ResponseReceived;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ResponseReceived;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 3000));
      ASSERT_EQ(evt.probeIpVersion, IpVersion_V4);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_PreferredResponseReceived;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 3000));
      ASSERT_EQ(evt.probeIpVersion, IpVersion_V6);
      ASSERT_EQ(evt.ipVersionSelected, IpVersion_V4);
      ASSERT_EQ(evt.signallingStatusCode, 503);
      ASSERT_EQ(evt.signallingResponseText, "No DNS results");

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_RegisterSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 2000));

      mi->setProbeHandler(alice.handle, NULL);
   });

   waitFor(aliceEvents);
}

TEST_F(NetworkChangeTest, ProbingDualStackNetworkV4PreferredNoDNSResults)
{
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.outboundProxy = "invalid.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.ipVersion = IpVersion_Auto;
   alice.init();

   std::unique_ptr<MySipNetworkProbeHandler> aliceHandler(new MySipNetworkProbeHandler(&alice));
   CPCAPI2::SipAccount::SipAccountManagerInternal* mi = dynamic_cast<CPCAPI2::SipAccount::SipAccountManagerInternal*>(alice.account);
   mi->setProbeHandler(alice.handle, aliceHandler.get());
   mi->setProbeMockDelay(alice.handle, IpVersion_V6, 1250);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      alice.account->enable(alice.handle);

      CPCAPI2::SipAccount::SipNetworkProbeStatusChangedEvent evt;

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Probing;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ProbesSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 5000));
      ASSERT_EQ(evt.ipVersionPreferred, IpVersion_V4);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_ResponseReceived;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_PreferredResponseReceived;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 3000));
      ASSERT_EQ(evt.probeIpVersion, IpVersion_V4);
      ASSERT_EQ(evt.signallingStatusCode, 503);
      ASSERT_EQ(evt.signallingResponseText, "No DNS results");

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ResponseReceived;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 3000));
      ASSERT_EQ(evt.probeIpVersion, IpVersion_V6);
      ASSERT_EQ(evt.ipVersionSelected, IpVersion_V4);
      ASSERT_EQ(evt.signallingStatusCode, 503);
      ASSERT_EQ(evt.signallingResponseText, "No DNS results");

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_RegisterSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 2000));

      mi->setProbeHandler(alice.handle, NULL);
   });

   waitFor(aliceEvents);
}

TEST_F(NetworkChangeTest, ProbingDualStackNetworkV6PreferredNoDNSResults)
{
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.outboundProxy = "invalid.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.ipVersion = IpVersion_Auto_PreferV6;
   alice.init();

   std::unique_ptr<MySipNetworkProbeHandler> aliceHandler(new MySipNetworkProbeHandler(&alice));
   CPCAPI2::SipAccount::SipAccountManagerInternal* mi = dynamic_cast<CPCAPI2::SipAccount::SipAccountManagerInternal*>(alice.account);
   mi->setProbeHandler(alice.handle, aliceHandler.get());
   mi->setProbeMockDelay(alice.handle, IpVersion_V4, 1250);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      alice.account->enable(alice.handle);

      CPCAPI2::SipAccount::SipNetworkProbeStatusChangedEvent evt;

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Probing;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ProbesSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 5000));
      ASSERT_EQ(evt.ipVersionPreferred, IpVersion_V6);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_ResponseReceived;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_PreferredResponseReceived;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 3000));
      ASSERT_EQ(evt.probeIpVersion, IpVersion_V6);
      ASSERT_EQ(evt.signallingStatusCode, 503);
      ASSERT_EQ(evt.signallingResponseText, "No DNS results");

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ResponseReceived;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 3000));
      ASSERT_EQ(evt.probeIpVersion, IpVersion_V4);
      ASSERT_EQ(evt.ipVersionSelected, IpVersion_V6);
      ASSERT_EQ(evt.signallingStatusCode, 503);
      ASSERT_EQ(evt.signallingResponseText, "No DNS results");

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_RegisterSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 2000));

      mi->setProbeHandler(alice.handle, NULL);
   });

   waitFor(aliceEvents);
}

TEST_F(NetworkChangeTest, ProbingDualStackNetworkV4PreferredNoRouteToHostOnlyV6Responds)
{
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.outboundProxy = "dualstackv4invalid.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.ipVersion = IpVersion_Auto;
   alice.init();

   std::unique_ptr<MySipNetworkProbeHandler> aliceHandler(new MySipNetworkProbeHandler(&alice));
   CPCAPI2::SipAccount::SipAccountManagerInternal* mi = dynamic_cast<CPCAPI2::SipAccount::SipAccountManagerInternal*>(alice.account);
   mi->setProbeHandler(alice.handle, aliceHandler.get());
   mi->setProbeMockDelay(alice.handle, IpVersion_V6, 1250);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      alice.account->enable(alice.handle);

      CPCAPI2::SipAccount::SipNetworkProbeStatusChangedEvent evt;

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Probing;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ProbesSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 5000));
      ASSERT_EQ(evt.ipVersionPreferred, IpVersion_V4);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_ResponseReceived;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_PreferredResponseReceived;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 3000));
      ASSERT_EQ(evt.probeIpVersion, IpVersion_V4);
      ASSERT_EQ(evt.signallingStatusCode, 503);
#ifdef TARGET_OS_MAC
      ASSERT_EQ(evt.signallingResponseText, "Transport failure: no transports left to try");
#else
     ASSERT_EQ(evt.signallingResponseText, "No route to host");
#endif

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ResponseReceived;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 3000));
      ASSERT_EQ(evt.probeIpVersion, IpVersion_V6);
      ASSERT_EQ(evt.ipVersionSelected, IpVersion_V6);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_RegisterSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 2000));

      mi->setProbeHandler(alice.handle, NULL);
   });

   waitFor(aliceEvents);
}

TEST_F(NetworkChangeTest, ProbingDualStackNetworkV6PreferredNoRouteToHostOnlyV4Responds)
{
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.outboundProxy = "dualstackv6invalid.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.ipVersion = IpVersion_Auto_PreferV6;
   alice.init();
   // alice.network->setNetworkTransport(NetworkTransport::TransportNone);

   std::unique_ptr<MySipNetworkProbeHandler> aliceHandler(new MySipNetworkProbeHandler(&alice));
   CPCAPI2::SipAccount::SipAccountManagerInternal* mi = dynamic_cast<CPCAPI2::SipAccount::SipAccountManagerInternal*>(alice.account);
   mi->setProbeHandler(alice.handle, aliceHandler.get());
   mi->setProbeMockDelay(alice.handle, IpVersion_V4, 1250);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      alice.account->enable(alice.handle);

      CPCAPI2::SipAccount::SipNetworkProbeStatusChangedEvent evt;

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Probing;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ProbesSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 5000));
      ASSERT_EQ(evt.ipVersionPreferred, IpVersion_V6);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_ResponseReceived;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_PreferredResponseReceived;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 3000));
      ASSERT_EQ(evt.probeIpVersion, IpVersion_V6);
      ASSERT_EQ(evt.signallingStatusCode, 503);
      ASSERT_EQ(evt.signallingResponseText, "No route to host");

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ResponseReceived;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 3000));
      ASSERT_EQ(evt.probeIpVersion, IpVersion_V4);
      ASSERT_EQ(evt.ipVersionSelected, IpVersion_V4);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_RegisterSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 2000));

      mi->setProbeHandler(alice.handle, NULL);
   });

   waitFor(aliceEvents);
}

// OBELISK-5954: 503 response to SUBSCRIBE results in SDK blacklisting IPv4 server address,
// and trying to switch back to using IPv6 server address. this test is not yet setup to assert
// when this problem occurs; you need to look at the logs. You also need ot manually hack TransactionState.cxx; see commment below
TEST_F(NetworkChangeTest, DISABLED_ProbingDualStackNetworkV6PreferredNoRouteToHostOnlyV4Responds_SubscribeReject)
{
   TestAccount alice("alice", Account_NoInit);

   alice.config.settings.outboundProxy = "dualstackv6invalid.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.ipVersion = IpVersion_Auto_PreferV6;
   alice.init();

   std::unique_ptr<MySipNetworkProbeHandler> aliceHandler(new MySipNetworkProbeHandler(&alice));
   CPCAPI2::SipAccount::SipAccountManagerInternal* mi = dynamic_cast<CPCAPI2::SipAccount::SipAccountManagerInternal*>(alice.account);
   mi->setProbeHandler(alice.handle, aliceHandler.get());
   mi->setProbeMockDelay(alice.handle, IpVersion_V4, 1250);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      alice.account->enable(alice.handle);

      CPCAPI2::SipAccount::SipNetworkProbeStatusChangedEvent evt;

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Probing;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ProbesSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 5000));
      ASSERT_EQ(evt.ipVersionPreferred, IpVersion_V6);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_ResponseReceived;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_PreferredResponseReceived;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 3000));
      ASSERT_EQ(evt.probeIpVersion, IpVersion_V6);
      ASSERT_EQ(evt.signallingStatusCode, 503);
      ASSERT_EQ(evt.signallingResponseText, "No route to host");

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ResponseReceived;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 3000));
      ASSERT_EQ(evt.probeIpVersion, IpVersion_V4);
      ASSERT_EQ(evt.ipVersionSelected, IpVersion_V4);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_RegisterSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 2000));

      mi->setProbeHandler(alice.handle, NULL);

      assertAccountRegistering(alice);
      assertAccountRegistered(alice);

      CPCAPI2::SipEvent::SipEventSubscriptionHandle aliceSubs = alice.subs->createSubscription(alice.handle);
      CPCAPI2::SipEvent::SipEventSubscriptionSettings subsSettings;
      subsSettings.eventPackage = "cpcapi2test";
      subsSettings.expiresSeconds = 3600;
      subsSettings.supportedMimeTypes.push_back(CPCAPI2::MimeType("application", "cpcapi2test"));
      alice.subs->applySubscriptionSettings(aliceSubs, subsSettings);

      // hack to get repro to respond with 500 error -- have to locally adjust line 202 in TransactionState.cxx
      // to use 503 instead of 500 to trigger SDK blacklist behaviour. TODO: find an alternate sustainable mechanism.
      // tried: alice subscribes to bob; bob rejects with 503; however repro intercepts this and sends back 4xx
      alice.subs->addParticipant(aliceSubs, "brk:abcd123");
      alice.subs->start(aliceSubs);

      std::this_thread::sleep_for(std::chrono::seconds(5));

      alice.account->disable(alice.handle);
   });
   /*
   auto bobEvents = std::async(std::launch::async, [&] () {
      {
         CPCAPI2::SipEvent::SipEventSubscriptionHandle h;
         CPCAPI2::SipEvent::NewSubscriptionEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.subsEvents,
            "SipEventSubscriptionHandler::onNewSubscription",
            5000, AlwaysTruePred(), h, evt));
         bob.subs->reject(h, 503);
      }
   });
   */
   waitFor(aliceEvents);
   //waitFor2(aliceEvents, bobEvents);

}

TEST_F(NetworkChangeTest, ProbingDualStackNetworkV4PreferredNoRouteToHostOnlyV6RespondsFirst)
{
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.outboundProxy = "dualstackv4invalid.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.ipVersion = IpVersion_Auto;
   alice.init();

   std::unique_ptr<MySipNetworkProbeHandler> aliceHandler(new MySipNetworkProbeHandler(&alice));
   CPCAPI2::SipAccount::SipAccountManagerInternal* mi = dynamic_cast<CPCAPI2::SipAccount::SipAccountManagerInternal*>(alice.account);
   mi->setProbeHandler(alice.handle, aliceHandler.get());
   mi->setProbeMockDelay(alice.handle, IpVersion_V4, 1250);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      alice.account->enable(alice.handle);

      CPCAPI2::SipAccount::SipNetworkProbeStatusChangedEvent evt;

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Probing;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ProbesSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 5000));
      ASSERT_EQ(evt.ipVersionPreferred, IpVersion_V4);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_ResponseReceived;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ResponseReceived;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 3000));
      ASSERT_EQ(evt.probeIpVersion, IpVersion_V6);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_PreferredResponseReceived;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 3000));
      ASSERT_EQ(evt.probeIpVersion, IpVersion_V4);
      ASSERT_EQ(evt.ipVersionSelected, IpVersion_V6);
      ASSERT_EQ(evt.signallingStatusCode, 503);
#ifdef TARGET_OS_MAC
     ASSERT_EQ(evt.signallingResponseText, "Transport failure: no transports left to try");
#else
     ASSERT_EQ(evt.signallingResponseText, "No route to host");
#endif

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_RegisterSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 2000));

      mi->setProbeHandler(alice.handle, NULL);
   });

   waitFor(aliceEvents);
}


TEST_F(NetworkChangeTest, ProbingDualStackNetworkV6PreferredNoRouteToHostOnlyV4RespondsFirst)
{
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.outboundProxy = "dualstackv6invalid.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.ipVersion = IpVersion_Auto_PreferV6;
   alice.init();

   std::unique_ptr<MySipNetworkProbeHandler> aliceHandler(new MySipNetworkProbeHandler(&alice));
   CPCAPI2::SipAccount::SipAccountManagerInternal* mi = dynamic_cast<CPCAPI2::SipAccount::SipAccountManagerInternal*>(alice.account);
   mi->setProbeHandler(alice.handle, aliceHandler.get());
   mi->setProbeMockDelay(alice.handle, IpVersion_V6, 1250);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      alice.account->enable(alice.handle);

      CPCAPI2::SipAccount::SipNetworkProbeStatusChangedEvent evt;

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Probing;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ProbesSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 5000));
      ASSERT_EQ(evt.ipVersionPreferred, IpVersion_V6);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_ResponseReceived;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ResponseReceived;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 3000));
      ASSERT_EQ(evt.probeIpVersion, IpVersion_V4);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_PreferredResponseReceived;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 3000));
      ASSERT_EQ(evt.probeIpVersion, IpVersion_V6);
      ASSERT_EQ(evt.ipVersionSelected, IpVersion_V4);
      ASSERT_EQ(evt.signallingStatusCode, 503);
      ASSERT_EQ(evt.signallingResponseText, "No route to host");

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_RegisterSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 2000));

      mi->setProbeHandler(alice.handle, NULL);
   });

   waitFor(aliceEvents);
}

TEST_F(NetworkChangeTest, ProbingDualStackNetworkV4PreferredNoRouteToHost)
{
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.outboundProxy = "dualstackinvalid.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.ipVersion = IpVersion_Auto;
   alice.init();

   std::unique_ptr<MySipNetworkProbeHandler> aliceHandler(new MySipNetworkProbeHandler(&alice));
   CPCAPI2::SipAccount::SipAccountManagerInternal* mi = dynamic_cast<CPCAPI2::SipAccount::SipAccountManagerInternal*>(alice.account);
   mi->setProbeHandler(alice.handle, aliceHandler.get());
   mi->setProbeMockDelay(alice.handle, IpVersion_V6, 1250);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      alice.account->enable(alice.handle);

      CPCAPI2::SipAccount::SipNetworkProbeStatusChangedEvent evt;

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Probing;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ProbesSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 5000));
      ASSERT_EQ(evt.ipVersionPreferred, IpVersion_V4);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_ResponseReceived;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_PreferredResponseReceived;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 3000));
      ASSERT_EQ(evt.probeIpVersion, IpVersion_V4);
      ASSERT_EQ(evt.signallingStatusCode, 503);
#ifdef TARGET_OS_MAC
     ASSERT_EQ(evt.signallingResponseText, "Transport failure: no transports left to try");
#else
     ASSERT_EQ(evt.signallingResponseText, "No route to host");
#endif

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ResponseReceived;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 3000));
      ASSERT_EQ(evt.probeIpVersion, IpVersion_V6);
      ASSERT_EQ(evt.ipVersionSelected, IpVersion_V4);
      ASSERT_EQ(evt.signallingStatusCode, 503);
      ASSERT_EQ(evt.signallingResponseText, "No route to host");

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_RegisterSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 2000));

      mi->setProbeHandler(alice.handle, NULL);
   });

   waitFor(aliceEvents);
}

TEST_F(NetworkChangeTest, ProbingDualStackNetworkV6PreferredNoRouteToHost)
{
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.outboundProxy = "dualstackinvalid.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.ipVersion = IpVersion_Auto_PreferV6;
   alice.init();

   std::unique_ptr<MySipNetworkProbeHandler> aliceHandler(new MySipNetworkProbeHandler(&alice));
   CPCAPI2::SipAccount::SipAccountManagerInternal* mi = dynamic_cast<CPCAPI2::SipAccount::SipAccountManagerInternal*>(alice.account);
   mi->setProbeHandler(alice.handle, aliceHandler.get());
   mi->setProbeMockDelay(alice.handle, IpVersion_V4, 1250);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      alice.account->enable(alice.handle);

      CPCAPI2::SipAccount::SipNetworkProbeStatusChangedEvent evt;

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Probing;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ProbesSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 5000));
      ASSERT_EQ(evt.ipVersionPreferred, IpVersion_V6);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_ResponseReceived;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_PreferredResponseReceived;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 3000));
      ASSERT_EQ(evt.probeIpVersion, IpVersion_V6);
      ASSERT_EQ(evt.signallingStatusCode, 503);
      ASSERT_EQ(evt.signallingResponseText, "No route to host");

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ResponseReceived;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 3000));
      ASSERT_EQ(evt.probeIpVersion, IpVersion_V4);
      ASSERT_EQ(evt.ipVersionSelected, IpVersion_V6);
      ASSERT_EQ(evt.signallingStatusCode, 503);
#ifdef TARGET_OS_MAC
     ASSERT_EQ(evt.signallingResponseText, "Transport failure: no transports left to try");
#else
     ASSERT_EQ(evt.signallingResponseText, "No route to host");
#endif

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_RegisterSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 2000));

      mi->setProbeHandler(alice.handle, NULL);
   });

   waitFor(aliceEvents);
}

TEST_F(NetworkChangeTest, ProbingDualStackNetworkV4PreferredTransactionTimeoutOnlyV6Responds)
{
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.outboundProxy = "dualstackv4broken.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.ipVersion = IpVersion_Auto;
   alice.init();

   std::unique_ptr<MySipNetworkProbeHandler> aliceHandler(new MySipNetworkProbeHandler(&alice));
   CPCAPI2::SipAccount::SipAccountManagerInternal* mi = dynamic_cast<CPCAPI2::SipAccount::SipAccountManagerInternal*>(alice.account);
   mi->setProbeHandler(alice.handle, aliceHandler.get());
   mi->setProbeMockDelay(alice.handle, IpVersion_V6, 1250);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      alice.account->enable(alice.handle);

      CPCAPI2::SipAccount::SipNetworkProbeStatusChangedEvent evt;

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Probing;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ProbesSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 5000));
      ASSERT_EQ(evt.ipVersionPreferred, IpVersion_V4);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_ResponseReceived;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ResponseReceived;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 3000));
      ASSERT_EQ(evt.probeIpVersion, IpVersion_V6);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ProbeTimeout;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 3000));
      ASSERT_EQ(evt.ipVersionSelected, IpVersion_V6);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_RegisterSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 2000));

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_StaleResponseReceived;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 32000));
      ASSERT_EQ(evt.signallingStatusCode, 408);
      ASSERT_EQ(evt.signallingResponseText, "Request Timeout");

      mi->setProbeHandler(alice.handle, NULL);
   });

   waitFor(aliceEvents);
}

TEST_F(NetworkChangeTest, ProbingDualStackNetworkV6PreferredTransactionTimeoutOnlyV4Responds)
{
   ReproHolder::destroyInstance();

   runRepro("repro_ipv4only.config");

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.outboundProxy = "dualstack.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.ipVersion = IpVersion_Auto_PreferV6;
   alice.init();

   std::unique_ptr<MySipNetworkProbeHandler> aliceHandler(new MySipNetworkProbeHandler(&alice));
   CPCAPI2::SipAccount::SipAccountManagerInternal* mi = dynamic_cast<CPCAPI2::SipAccount::SipAccountManagerInternal*>(alice.account);
   mi->setProbeHandler(alice.handle, aliceHandler.get());
   mi->setProbeMockDelay(alice.handle, IpVersion_V4, 1250);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      alice.account->enable(alice.handle);

      CPCAPI2::SipAccount::SipNetworkProbeStatusChangedEvent evt;

      evt.status = SipNetworkProbeStatusChangedEvent::Status_ResponseReceived;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ResponseReceived;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 3000));
      ASSERT_EQ(evt.probeIpVersion, IpVersion_V4);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ProbeTimeout;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 3000));
      ASSERT_EQ(evt.ipVersionSelected, IpVersion_V4);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_RegisterSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 2000));

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_StaleResponseReceived;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 32000));
      ASSERT_EQ(evt.signallingStatusCode, 408);
      ASSERT_EQ(evt.signallingResponseText, "Request Timeout");

      mi->setProbeHandler(alice.handle, NULL);
   });

   waitFor(aliceEvents);

   runOriginalRepro();
}

TEST_F(NetworkChangeTest, ProbingDualStackNetworkV4PreferredTransactionTimeout)
{
   ReproHolder::destroyInstance();
   
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.outboundProxy = "dualstack.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.ipVersion = IpVersion_Auto;
   alice.init();

   std::unique_ptr<MySipNetworkProbeHandler> aliceHandler(new MySipNetworkProbeHandler(&alice));
   CPCAPI2::SipAccount::SipAccountManagerInternal* mi = dynamic_cast<CPCAPI2::SipAccount::SipAccountManagerInternal*>(alice.account);
   mi->setProbeHandler(alice.handle, aliceHandler.get());
   mi->setProbeMockDelay(alice.handle, IpVersion_V6, 1250);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      alice.account->enable(alice.handle);

      CPCAPI2::SipAccount::SipNetworkProbeStatusChangedEvent evt;

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Probing;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ProbesSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 5000));
      ASSERT_EQ(evt.ipVersionPreferred, IpVersion_V4);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ProbeTimeout;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 3000));
      ASSERT_EQ(evt.ipVersionSelected, IpVersion_V4);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_RegisterSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 2000));

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_StaleResponseReceived;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 32000));
      ASSERT_EQ(evt.signallingStatusCode, 408);
      ASSERT_EQ(evt.signallingResponseText, "Request Timeout");

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_StaleResponseReceived;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 2000));
      ASSERT_EQ(evt.signallingStatusCode, 408);
      ASSERT_EQ(evt.signallingResponseText, "Request Timeout");

      mi->setProbeHandler(alice.handle, NULL);
   });

   waitFor(aliceEvents);

   runOriginalRepro();
}

TEST_F(NetworkChangeTest, ProbingDualStackNetworkV6PreferredTransactionTimeout)
{
   ReproHolder::destroyInstance();

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.outboundProxy = "dualstack.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.ipVersion = IpVersion_Auto_PreferV6;
   alice.init();

   std::unique_ptr<MySipNetworkProbeHandler> aliceHandler(new MySipNetworkProbeHandler(&alice));
   CPCAPI2::SipAccount::SipAccountManagerInternal* mi = dynamic_cast<CPCAPI2::SipAccount::SipAccountManagerInternal*>(alice.account);
   mi->setProbeHandler(alice.handle, aliceHandler.get());
   mi->setProbeMockDelay(alice.handle, IpVersion_V4, 1250);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      alice.account->enable(alice.handle);

      CPCAPI2::SipAccount::SipNetworkProbeStatusChangedEvent evt;

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Probing;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ProbesSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 5000));
      ASSERT_EQ(evt.ipVersionPreferred, IpVersion_V6);

     evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
     evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ProbeTimeout;
     ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 3000));
     ASSERT_EQ(evt.ipVersionSelected, IpVersion_V6);

     evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
     evt.reason = SipNetworkProbeStatusChangedEvent::Reason_RegisterSent;
     ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 2000));

     evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
     evt.reason = SipNetworkProbeStatusChangedEvent::Reason_StaleResponseReceived;
     ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 32000));
     ASSERT_EQ(evt.signallingStatusCode, 408);
     ASSERT_EQ(evt.signallingResponseText, "Request Timeout");

     evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
     evt.reason = SipNetworkProbeStatusChangedEvent::Reason_StaleResponseReceived;
     ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 2000));
     ASSERT_EQ(evt.signallingStatusCode, 408);
     ASSERT_EQ(evt.signallingResponseText, "Request Timeout");

      mi->setProbeHandler(alice.handle, NULL);
   });

   waitFor(aliceEvents);

   runOriginalRepro();
}

TEST_F(NetworkChangeTest, ProbingDualStackNetworkV4PreferredV4SelectedNetworkChangeV6Selected)
{
   TestAccount alice("alice");
   alice.config.settings.outboundProxy = "dualstack.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.ipVersion = IpVersion_Auto;

   SipAccountSettings defaultSettings = alice.config.settings;
   SipAccountSettings wifiSettings = defaultSettings;
   SipAccountSettings wwanSettings = defaultSettings;

   ASSERT_EQ(alice.account->configureDefaultAccountSettings(alice.handle, defaultSettings), kSuccess);
   ASSERT_EQ(alice.account->configureTransportAccountSettings(alice.handle, wifiSettings, TransportWiFi), kSuccess);
   ASSERT_EQ(alice.account->configureTransportAccountSettings(alice.handle, wwanSettings, TransportWWAN), kSuccess);
   ASSERT_EQ(alice.account->applySettings(alice.handle), kSuccess);

   assertAccountDeregistering(alice);
   assertAccountDeregistered(alice);
   assertAccountRegistering(alice);
   assertAccountRegistered(alice);
   // assertAccountRegistered(alice);

   std::unique_ptr<MySipNetworkProbeHandler> aliceHandler(new MySipNetworkProbeHandler(&alice));
   CPCAPI2::SipAccount::SipAccountManagerInternal* mi = dynamic_cast<CPCAPI2::SipAccount::SipAccountManagerInternal*>(alice.account);
   mi->setProbeHandler(alice.handle, aliceHandler.get());
   mi->setProbeMockDelay(alice.handle, IpVersion_V6, 1250);

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> ifaces;
      ifaces.insert("**************");
      alice.network->setMockInterfaces(ifaces);

      CPCAPI2::SipAccount::SipNetworkProbeStatusChangedEvent evt;

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Probing;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_NetworkChangeDelay;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 5000));

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Probing;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ProbesSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 3000));
      ASSERT_EQ(evt.ipVersionPreferred, IpVersion_V4);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_PreferredResponseReceived;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 2000));
      ASSERT_EQ(evt.probeIpVersion, IpVersion_V4);
      ASSERT_EQ(evt.ipVersionSelected, IpVersion_V4);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_RegisterSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 2000));

      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);
      // alice.conversationEvents->clearCommands();
      // alice.accountEvents->clearCommands();

      mi->setProbeMockDelay(alice.handle, IpVersion_V4, 5000);
      alice.network->setNetworkTransport(NetworkTransport::TransportWiFi);
      ifaces.clear();
      ifaces.insert("**************");
      alice.network->setMockInterfaces(ifaces);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Probing;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_NetworkChangeDelay;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 2000));

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Probing;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ProbesSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 2000));
      ASSERT_EQ(evt.ipVersionPreferred, IpVersion_V4);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_ResponseReceived;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ResponseReceived;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 3000));
      ASSERT_EQ(evt.probeIpVersion, IpVersion_V6);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ProbeTimeout;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 3000));
      ASSERT_EQ(evt.ipVersionSelected, IpVersion_V6);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_RegisterSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 2000));

      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);

      mi->setProbeHandler(alice.handle, NULL);
   });

   waitFor(aliceEvents);
}

TEST_F(NetworkChangeTest, ProbingDualStackNetworkV6PreferredV6SelectedNetworkChangeV4Selected)
{
   TestAccount alice("alice");
   alice.config.settings.outboundProxy = "dualstack.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.ipVersion = IpVersion_Auto_PreferV6;

   SipAccountSettings defaultSettings = alice.config.settings;
   SipAccountSettings wifiSettings = defaultSettings;
   SipAccountSettings wwanSettings = defaultSettings;

   ASSERT_EQ(alice.account->configureDefaultAccountSettings(alice.handle, defaultSettings), kSuccess);
   ASSERT_EQ(alice.account->configureTransportAccountSettings(alice.handle, wifiSettings, TransportWiFi), kSuccess);
   ASSERT_EQ(alice.account->configureTransportAccountSettings(alice.handle, wwanSettings, TransportWWAN), kSuccess);
   ASSERT_EQ(alice.account->applySettings(alice.handle), kSuccess);

   assertAccountDeregistering(alice);
   assertAccountDeregistered(alice);
   assertAccountRegistering(alice);
   assertAccountRegistered(alice);

   std::unique_ptr<MySipNetworkProbeHandler> aliceHandler(new MySipNetworkProbeHandler(&alice));
   CPCAPI2::SipAccount::SipAccountManagerInternal* mi = dynamic_cast<CPCAPI2::SipAccount::SipAccountManagerInternal*>(alice.account);
   mi->setProbeHandler(alice.handle, aliceHandler.get());
   mi->setProbeMockDelay(alice.handle, IpVersion_V4, 1250);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> ifaces;
      ifaces.insert("**************");
      alice.network->setMockInterfaces(ifaces);

      CPCAPI2::SipAccount::SipNetworkProbeStatusChangedEvent evt;

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Probing;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_NetworkChangeDelay;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 5000));

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Probing;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ProbesSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 3000));
      ASSERT_EQ(evt.ipVersionPreferred, IpVersion_V6);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_PreferredResponseReceived;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 2000));
      ASSERT_EQ(evt.probeIpVersion, IpVersion_V6);
      ASSERT_EQ(evt.ipVersionSelected, IpVersion_V6);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_RegisterSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 2000));

      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);

      mi->setProbeMockDelay(alice.handle, IpVersion_V6, 5000);
      alice.network->setNetworkTransport(NetworkTransport::TransportWiFi);
      ifaces.clear();
      ifaces.insert("**************");
      alice.network->setMockInterfaces(ifaces);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Probing;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_NetworkChangeDelay;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 2000));

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Probing;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ProbesSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 2000));
      ASSERT_EQ(evt.ipVersionPreferred, IpVersion_V6);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_ResponseReceived;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ResponseReceived;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 3000));
      ASSERT_EQ(evt.probeIpVersion, IpVersion_V4);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ProbeTimeout;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 3000));
      ASSERT_EQ(evt.ipVersionSelected, IpVersion_V4);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_RegisterSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 2000));

      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);

      mi->setProbeHandler(alice.handle, NULL);
   });

   waitFor(aliceEvents);
}

TEST_F(NetworkChangeTest, ProbingDualStackNetworkDisabledDuringProbeDelay)
{
   TestAccount alice("alice");
   alice.config.settings.outboundProxy = "dualstack.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.ipVersion = IpVersion_Auto_PreferV6;

   SipAccountSettings defaultSettings = alice.config.settings;
   SipAccountSettings wifiSettings = defaultSettings;
   SipAccountSettings wwanSettings = defaultSettings;

   ASSERT_EQ(alice.account->configureDefaultAccountSettings(alice.handle, defaultSettings), kSuccess);
   ASSERT_EQ(alice.account->configureTransportAccountSettings(alice.handle, wifiSettings, TransportWiFi), kSuccess);
   ASSERT_EQ(alice.account->configureTransportAccountSettings(alice.handle, wwanSettings, TransportWWAN), kSuccess);
   ASSERT_EQ(alice.account->applySettings(alice.handle), kSuccess);

   assertAccountDeregistering(alice);
   assertAccountDeregistered(alice);
   assertAccountRegistering(alice);
   assertAccountRegistered(alice);

   std::unique_ptr<MySipNetworkProbeHandler> aliceHandler(new MySipNetworkProbeHandler(&alice));
   CPCAPI2::SipAccount::SipAccountManagerInternal* mi = dynamic_cast<CPCAPI2::SipAccount::SipAccountManagerInternal*>(alice.account);
   mi->setProbeHandler(alice.handle, aliceHandler.get());
   mi->setProbeMockDelay(alice.handle, IpVersion_V4, 1250);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> ifaces;
      ifaces.insert("**************");
      alice.network->setMockInterfaces(ifaces);

      CPCAPI2::SipAccount::SipNetworkProbeStatusChangedEvent evt;

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Probing;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_NetworkChangeDelay;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 2000));

      assertAccountRefreshing(alice);
      alice.account->disable(alice.handle);

      mi->setProbeHandler(alice.handle, NULL);
   });

   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(5000)), std::future_status::ready);
}

TEST_F(NetworkChangeTest, ProbingDualStackNetworkChangeDuringProbing)
{
   TestAccount alice("alice");
   alice.config.settings.outboundProxy = "dualstack.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.ipVersion = IpVersion_Auto_PreferV6;

   SipAccountSettings defaultSettings = alice.config.settings;
   SipAccountSettings wifiSettings = defaultSettings;
   SipAccountSettings wwanSettings = defaultSettings;

   ASSERT_EQ(alice.account->configureDefaultAccountSettings(alice.handle, defaultSettings), kSuccess);
   ASSERT_EQ(alice.account->configureTransportAccountSettings(alice.handle, wifiSettings, TransportWiFi), kSuccess);
   ASSERT_EQ(alice.account->configureTransportAccountSettings(alice.handle, wwanSettings, TransportWWAN), kSuccess);
   ASSERT_EQ(alice.account->applySettings(alice.handle), kSuccess);

   assertAccountDeregistering(alice);
   assertAccountDeregistered(alice);
   assertAccountRegistering(alice);
   assertAccountRegistered(alice);

   std::unique_ptr<MySipNetworkProbeHandler> aliceHandler(new MySipNetworkProbeHandler(&alice));
   CPCAPI2::SipAccount::SipAccountManagerInternal* mi = dynamic_cast<CPCAPI2::SipAccount::SipAccountManagerInternal*>(alice.account);
   mi->setProbeHandler(alice.handle, aliceHandler.get());
   mi->setProbeMockDelay(alice.handle, IpVersion_V4, 1250);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> ifaces;
      ifaces.insert("**************");
      alice.network->setMockInterfaces(ifaces);

      CPCAPI2::SipAccount::SipNetworkProbeStatusChangedEvent evt;

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Probing;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_NetworkChangeDelay;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 2000));

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Probing;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ProbesSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 3000));
      ASSERT_EQ(evt.ipVersionPreferred, IpVersion_V6);

      assertAccountRefreshing(alice);

      alice.network->setNetworkTransport(NetworkTransport::TransportWiFi);
      ifaces.clear();
      ifaces.insert("**************");
      alice.network->setMockInterfaces(ifaces);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Probing;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_NetworkChangeDelay;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 2000));

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Probing;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_ProbesSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 2000));
      ASSERT_EQ(evt.ipVersionPreferred, IpVersion_V6);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_PreferredResponseReceived;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 3000));
      ASSERT_EQ(evt.probeIpVersion, IpVersion_V6);

      evt.status = SipNetworkProbeStatusChangedEvent::Status_Completed;
      evt.reason = SipNetworkProbeStatusChangedEvent::Reason_RegisterSent;
      ASSERT_TRUE(EXPECT_PROBE_EVENT(aliceHandler, evt, 2000));

      CPCAPI2::SipAccount::SipAccountStatusChangedEvent accountEvt;
      ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged", 2000, CPCAPI2::test::AlwaysTruePred(), alice.handle, accountEvt));
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Refreshing || CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, accountEvt.accountStatus);

      if (CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Refreshing == accountEvt.accountStatus)
      {
         assertAccountRegistered(alice);
         ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged", 2000, CPCAPI2::test::AlwaysTruePred(), alice.handle, accountEvt));
      }
      else if (CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered == accountEvt.accountStatus)
      {
         // If the registered is resulting from events prior to the network change, we may get another refresh
         cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged", 2000, CPCAPI2::test::AlwaysTruePred(), alice.handle, accountEvt);
         ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged", 20000, CPCAPI2::test::AlwaysTruePred(), alice.handle, accountEvt));
         if (CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Refreshing == accountEvt.accountStatus)
         {
            assertAccountRegistered(alice);
         }
      }

      mi->setProbeHandler(alice.handle, NULL);
   });

   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(5000)), std::future_status::ready);
}

TEST_F(NetworkChangeTest, NetworkChangeRereg) {

   TestAccount bob("bob", Account_NoInit);
   bob.init();
   bob.enable(false);

   assertAccountRegistering(bob);

   cpc::string initialRinstance;
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
      ASSERT_TRUE(bob.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged",
         __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt)) << "error in account registration";
      ASSERT_EQ(bob.handle, h) << "error in account registration";
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus) << "account cannot be registered";
      initialRinstance = evt.rinstance;
      ASSERT_FALSE(initialRinstance.empty());
   }

   const char* mockIfaces[] = { "**************", "**************", "**************", "**************", "**************", "**************" };

   for (int i=0; i<6; i++)
   {
      bob.network->setNetworkTransport(i%2==0 ? NetworkTransport::TransportWWAN : NetworkTransport::TransportWiFi);
      const char* ifaceToSet = mockIfaces[i];
      std::set<resip::Data> ifaces;
      ifaces.insert(ifaceToSet); // NOTE: this has no bearing on the c= line in the SDP offers
      bob.network->setMockInterfaces(ifaces);

      {
         assertAccountRefreshing(bob);

         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
         ASSERT_TRUE(bob.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged",
            __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt)) << "error in account registration";
         ASSERT_EQ(bob.handle, h) << "error in account registration";
         if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
         {
            safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
         }
         ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus) << "account cannot be registered";
         ASSERT_EQ(initialRinstance, evt.rinstance);

      }

      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   }
}

TEST_F(NetworkChangeTest, NetworkChangeRereg_TalkHold) {

   TestAccount alice("alice");

   TestAccount bob("bob", Account_NoInit);
   bob.init();
   bob.enable(false);

   assertAccountRegistering(bob);

   cpc::string initialRinstance;
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
      ASSERT_TRUE(bob.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged",
         __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt)) << "error in account registration";
      ASSERT_EQ(bob.handle, h) << "error in account registration";
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus) << "account cannot be registered";
      initialRinstance = evt.rinstance;
      ASSERT_FALSE(initialRinstance.empty());
   }

   const char* mockIfaces[] = { "**************", "**************", "**************", "**************", "**************", "**************" };

   for (int i=0; i<3; i++)
   {
      bob.network->setNetworkTransport(i%2==0 ? NetworkTransport::TransportWWAN : NetworkTransport::TransportWiFi);
      const char* ifaceToSet = mockIfaces[i];
      std::set<resip::Data> ifaces;
      ifaces.insert(ifaceToSet); // NOTE: this has no bearing on the c= line in the SDP offers
      bob.network->setMockInterfaces(ifaces);

      {
         assertAccountRefreshing(bob);

         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
         ASSERT_TRUE(bob.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged",
            __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt)) << "error in account registration";
         ASSERT_EQ(bob.handle, h) << "error in account registration";
         if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
         {
            safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
         }
         ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus) << "account cannot be registered";
         ASSERT_EQ(initialRinstance, evt.rinstance);

      }

      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   }
   
   dynamic_cast<SipAccountManagerInternal*>(alice.account)->sendOptionsMessage(alice.handle, bob.config.uri());
   
   // we don't want the 200 OK bob sends to the OPTIONS message to have Allow-Events header with repeat items, e.g.
   // Allow-Events: talk, hold, talk, hold, talk, hold, talk, hold, talk, hold, talk, hold, talk, hold
   //
   // doesn't seem to be an easy way to verify this here
   
   std::this_thread::sleep_for(std::chrono::seconds(5));
}

TEST_F(NetworkChangeTest, DISABLED_NetworkChangeReregWhileAccountManualRereg) {

   /*
   * The purpose of this test is to make the network change re-registration logic hit the case
   * where the registration is already being refreshed (in real life this could be due to expiry of the registration interval).
   * The idea is we want the network change re-reg to still happen, but we need to delay it a bit.
   */
   TestAccount bob("bob");

   auto bobEvents = std::async(std::launch::async, [&] () {
      for (int j=0; j<10*5; j++)
      {
         std::this_thread::sleep_for(std::chrono::milliseconds(100));
         bob.account->requestRegistrationRefresh(bob.handle, 0);
      }
      for (int k=0; k<10*5; k++)
      {
         {
            CPCAPI2::SipAccount::SipAccountHandle h;
            CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
            bob.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
               60000, CPCAPI2::test::AlwaysTruePred(), h, evt);
         }
         {
            CPCAPI2::SipAccount::SipAccountHandle h;
            CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
            bob.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
               60000, CPCAPI2::test::AlwaysTruePred(), h, evt);
         }
      }
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
   });

   const char* mockIfaces[] = { "**************", "**************", "**************", "**************", "**************", "**************" };

   for (int i=0; i<6; i++)
   {
      bob.network->setNetworkTransport(i%2==0 ? NetworkTransport::TransportWWAN : NetworkTransport::TransportWiFi);
      const char* ifaceToSet = mockIfaces[i];
      std::set<resip::Data> ifaces;
      ifaces.insert(ifaceToSet); // NOTE: this has no bearing on the c= line in the SDP offers
      bob.network->setMockInterfaces(ifaces);
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
         bob.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
            60000, CPCAPI2::test::AlwaysTruePred(), h, evt);
      }
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
         bob.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
            60000, CPCAPI2::test::AlwaysTruePred(), h, evt);
      }
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   }

   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
}

class MySipConversationAdornmentHandler : public SipConversationAdornmentHandler
{

public:
   std::mutex mtx;
   std::condition_variable cv;
   std::atomic<bool> mWaiting;

   MySipConversationAdornmentHandler(CPCAPI2::SipConversation::SipConversationManager* convMgr) : mConvMgr(convMgr), mWaiting(false) {}

   virtual int onConversationAdornment(SipConversationHandle conversation, const ConversationAdornmentEvent& args)
   {
      const ConversationAdornmentInternalEvent* internal = static_cast<const ConversationAdornmentInternalEvent*>(&args);

      std::unique_lock<std::mutex> lck(mtx);
      if (internal->resipMsg.header(resip::h_Contacts).front().uri().exists(resip::p_rinstance))
      {
         mLastRinstance = internal->resipMsg.header(resip::h_Contacts).front().uri().param(resip::p_rinstance);
      }
      else
      {
         mLastRinstance = resip::Data();
      }

      mWaiting = false;
      cv.notify_all();

      return kSuccess;
   }

   resip::Data lastRinstance()
   {
      std::unique_lock<std::mutex> lck(mtx);
      return mLastRinstance;
   }

private:
   CPCAPI2::SipConversation::SipConversationManager* mConvMgr;
   resip::Data mLastRinstance;
};

void TestNetworkChangeDuringCallVerifyRinstance_Transport(int transportType, bool useRport)
{
   ReproHolder::destroyInstance();

   // our stock repro config does not seem to use Record Route to force SIP traffic through repro when TCP is used.
   // this is problematic if one SDK A registers with TCP and useRport enabled; other SDKs looking at SDK A's Contact
   // header will try to send to it directly, which will fail (no TCP port will be open, since our TCP listen port is different
   // than the one we send intial REGISTERs from
   repro::ReproRunner* reproRunner = runRepro("repro_force_recordroute.config");

   std::shared_ptr<TestAccount> alicePtr = TestAccountFactory::createTestAccount(transportType, "alice", Account_NoInit);
   TestAccount& alice = *alicePtr.get();
   alice.config.settings.useRport = useRport;
   alice.config.settings.sipTransportType = transportType;
   alice.init();
   alice.enable();

   std::shared_ptr<TestAccount> bobPtr = TestAccountFactory::createTestAccount(transportType, "bob", Account_NoInit);
   TestAccount& bob = *bobPtr.get();
   bob.config.settings.useRport = useRport;
   bob.config.settings.sipTransportType = transportType;
   bob.init();
   bob.network->setNetworkTransport(NetworkTransport::TransportWiFi);

   MySipConversationAdornmentHandler adornmentHandler(bob.conversation);
   bob.conversation->setAdornmentHandler(bob.handle, &adornmentHandler);
   bob.enable(false);

   assertAccountRegistering(bob);

   cpc::string initialRinstance;
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
      ASSERT_TRUE(bob.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged",
         __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt)) << "error in account registration";
      ASSERT_EQ(bob.handle, h) << "error in account registration";
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus) << "account cannot be registered";
      initialRinstance = evt.rinstance;
      ASSERT_FALSE(initialRinstance.empty());
   }

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      // bob's re-INVITE for bob's network change. we want to have a generous timeout so we
      // fail first on bob's thread if network change does not occur
      SipConversationHandle h;
      ConversationMediaChangeRequestEvent evt;
      ASSERT_TRUE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onConversationMediaChangeRequest",
      45000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));


      assertSuccess(alice.conversation->accept(aliceCall));
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);

      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());

      adornmentHandler.mWaiting = true;
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });

      {
         std::unique_lock<std::mutex> lck(adornmentHandler.mtx);
         while (adornmentHandler.mWaiting) adornmentHandler.cv.wait_for(lck, std::chrono::seconds(30));
      }

      resip::Data rinstance = adornmentHandler.lastRinstance();
      ASSERT_EQ(rinstance, initialRinstance);

      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      // check rinstance param in Contact header of 200 OK to INVITE both before network change and after
      adornmentHandler.mWaiting = true;

      std::set<resip::Data> ifaces;
      ifaces.insert("*********");
      bob.network->setMockInterfaces(ifaces);

      {
         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
         ASSERT_TRUE(bob.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
            20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(bob.handle, h);
         ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Refreshing, evt.accountStatus);
      }
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
         ASSERT_TRUE(bob.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
            20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(bob.handle, h);
         ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
         ASSERT_EQ(initialRinstance, evt.rinstance);

         {
            std::unique_lock<std::mutex> lck(adornmentHandler.mtx);
            while (adornmentHandler.mWaiting) adornmentHandler.cv.wait_for(lck, std::chrono::seconds(30));
         }

         resip::Data rinstance = adornmentHandler.lastRinstance();
         ASSERT_EQ(rinstance, initialRinstance);
      }

      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);

      std::this_thread::sleep_for(std::chrono::milliseconds(500));
      assertSuccess(bob.conversation->end(bobCall));

      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);

      // expect registration refresh after network change during a call
      assertAccountRefreshing(bob);
      assertAccountRegistered(bob);
   });

   waitFor2(aliceEvents, bobEvents);

   runOriginalRepro(reproRunner);
}

TEST_F(NetworkChangeTest, TestNetworkChangeDuringCall_RportOff_Tcp)
{
   TestNetworkChangeDuringCallVerifyRinstance_Transport(SipAccountTransport_TCP, false);
}

// OBELISK-5411
TEST_F(NetworkChangeTest, TestNetworkChangeDuringCall_RportOff_Tls)
{
   TestNetworkChangeDuringCallVerifyRinstance_Transport(SipAccountTransport_TLS, false);
}

// OBELISK-5411
TEST_F(NetworkChangeTest, TestNetworkChangeDuringCall_RportOff_Udp)
{
   TestNetworkChangeDuringCallVerifyRinstance_Transport(SipAccountTransport_UDP, false);
}

TEST_F(NetworkChangeTest, TestNetworkChangeDuringCall_RportOn_Tcp)
{
   TestNetworkChangeDuringCallVerifyRinstance_Transport(SipAccountTransport_TCP, true);
}

TEST_F(NetworkChangeTest, TestNetworkChangeDuringCall_RportOn_Tls)
{
   TestNetworkChangeDuringCallVerifyRinstance_Transport(SipAccountTransport_TLS, true);
}

TEST_F(NetworkChangeTest, TestNetworkChangeDuringCall_RportOn_Udp)
{
   TestNetworkChangeDuringCallVerifyRinstance_Transport(SipAccountTransport_UDP, true);
}

TEST_F(NetworkChangeTest, DISABLED_RestrictedAcctStayRestrictedAfterNetworkChange)
{
   // initial state is TransportWifi
   TestAccount bob("bob");
   ASSERT_EQ(bob.network->networkTransport(), NetworkTransport::TransportWiFi);
   bob.account->setNetworkRestriction(bob.handle, CPCAPI2::TransportWWAN, true);

   bob.account->disable(bob.handle);
   assertAccountDeregistering(bob);
   assertAccountDeregistered(bob);

   // simulate a change to TransportWWAN
   bob.network->setNetworkTransport(NetworkTransport::TransportWWAN);
   std::set<resip::Data> ifaces;
   ifaces.insert("**************"); // NOTE: this has no bearing on the c= line in the SDP offers
   bob.network->setMockInterfaces(ifaces);
   std::this_thread::sleep_for(std::chrono::milliseconds(10000));

   // enable the account -- BUT note that it is still restricted so this should NOT result in Registering/Registered!!!
   bob.account->enable(bob.handle);

   // !rkk! should the enable fail or should it succeed but do nothing?
}

TEST_F(NetworkChangeTest, BasicCallHandoffToRestrictedNetwork)
{
   TestAccount bob("bob");
   TestAccount charlie("charlie");

   // Initial network
   ASSERT_EQ(charlie.network->networkTransport(), NetworkTransport::TransportWiFi);

   // Charlie isn't allowed to use Transport WWAN
   charlie.account->setNetworkRestriction(charlie.handle, NetworkTransport::TransportWWAN, true);

   // Bob calls Charlie then Charlie hangs up
   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall, charlie.config.uri());
   bob.conversation->start(bobCall);
   auto bobConversationEvents = std::async(std::launch::async, [&] () {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing(bob, bobCall, charlie.config.uri());
      assertConversationStateChanged(bob, bobCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      {
         SipConversationHandle h;
         ConversationMediaChangeRequestEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
                                    "SipConversationHandler::onConversationMediaChangeRequest",
                                    15000,
                                    AlwaysTruePred(),
                                    h, evt));
         ASSERT_EQ(bobCall, h);
         ASSERT_EQ(bob.conversation->accept(bobCall), kSuccess);
      }

      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   auto charlieConversationEvents = std::async(std::launch::async, [&] () {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle charlieCall;
      assertNewConversationIncoming(charlie, &charlieCall, bob.config.uri());
      charlie.conversation->sendRingingResponse(charlieCall);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_LocalRinging);
      charlie.conversation->accept(charlieCall);
      assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_Connected);

      // Add the disable restriction
      charlie.account->disable(charlie.handle);

      // Switch to a restricted network mid-call, stacking on top of disable
      charlie.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> ifaces;
      ifaces.insert("**************");
      charlie.network->setMockInterfaces(ifaces);
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));

      assertAccountRefreshing(charlie);
      assertAccountRegistered(charlie);
      assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);
      charlie.conversation->end(charlieCall);
      assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedLocally);
   });
   waitFor2(bobConversationEvents, charlieConversationEvents);
}

#define NetworkChangeDuringCallHold_USE_NETCHANGE 1

TEST_F(NetworkChangeTest, DISABLED_NetworkChangeDuringCallHold) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendOnly);
      assertSuccess(alice.conversation->accept(aliceCall));
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_FALSE(evt.localHold);
         ASSERT_TRUE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_ReceiveOnly, evt.localMediaInfo[0].mediaDirection);
      });
      
#if NetworkChangeDuringCallHold_USE_NETCHANGE
      alice.network->setNetworkTransport(NetworkTransport::TransportWiFi);
      std::set<resip::Data> ifaces;
      ifaces.insert("**************");
      alice.network->setMockInterfaces(ifaces);
      
      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);
#endif
            
      // bob's unhold request
      assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendReceive);
      safeCout("ALICE IS ACCEPTING BOB'S UNHOLD REQUEST")
      assertSuccess(alice.conversation->accept(aliceCall));
      
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      assertConversationEnded_time(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely, 30000);
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertSuccess(bob.conversation->hold(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_TRUE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendOnly, evt.localMediaInfo[0].mediaDirection);
      });
      
#if NetworkChangeDuringCallHold_USE_NETCHANGE
      // alice's network change re-INVITE
      assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_ReceiveOnly);
      assertSuccess(bob.conversation->accept(bobCall));
      
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_TRUE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendOnly, evt.localMediaInfo[0].mediaDirection);
      });
#endif
      
      std::this_thread::sleep_for(std::chrono::seconds(10));
      assertSuccess(bob.conversation->unhold(bobCall));
      std::this_thread::sleep_for(std::chrono::seconds(5));
      
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });

      // Check that bobCall video has RTCP. We do this by getting two reports and comparing stamps
      TestCallEvents::expectRTCP( __LINE__, bob, bobCall );

      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor2(aliceEvents, bobEvents);
}


void configureMedia(MediaInfo& media, MediaType type, MediaDirection direction, MediaEncryptionMode mode, bool secure, cpc::vector<MediaCryptoSuite>& cryptos)
{
   media.mediaType = type;
   media.mediaDirection = direction;
   media.mediaEncryptionOptions.mediaEncryptionMode = mode;
   media.mediaEncryptionOptions.secureMediaRequired = secure;
   media.mediaEncryptionOptions.mediaCryptoSuites = cryptos;
}

void configureMedia(MediaInfo& media, MediaType type, MediaDirection direction, MediaEncryptionMode mode, bool secure, MediaCryptoSuite crypto1)
{
   cpc::vector<MediaCryptoSuite> cryptos;
   cryptos.push_back(crypto1);
   configureMedia(media, type, direction, mode, secure, cryptos);
}


struct NetworkChangeTestParam
{
   bool mSecureCall;
   NetworkChangeTestParam(bool secureCall) : mSecureCall(secureCall) {}
};

class ParameterizedNetworkChangeTest : public CpcapiAutoTestWithParam<NetworkChangeTestParam>
{
};

INSTANTIATE_TEST_SUITE_P(SecureAndNonSecureCalls, ParameterizedNetworkChangeTest,
   ::testing::Values(true, false)
);

TEST_P(ParameterizedNetworkChangeTest, BasicCallLoseNetworkRegainDifferentNetwork)
{
   bool secureCall = GetParam().mSecureCall;

   std::unique_ptr<TestAccount> bob;
   bob = secureCall ? std::unique_ptr<SecureTestAccount>(new SecureTestAccount("bob")) : std::unique_ptr<TestAccount>(new TestAccount("bob"));

   std::unique_ptr<TestAccount> charlie;
   charlie = secureCall ? std::unique_ptr<SecureTestAccount>(new SecureTestAccount("charlie")) : std::unique_ptr<TestAccount>(new TestAccount("charlie"));

   const MediaCryptoSuite cryptoSuite = secureCall ? MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80 : MediaCryptoSuite_None;

   MediaInfo bobAudio;
   bobAudio.mediaEncryptionOptions.mediaCryptoSuites.clear();
   if (secureCall)
   {
      configureMedia(bobAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, cryptoSuite);
   }
   MediaInfo charlieAudio;
   charlieAudio.mediaEncryptionOptions.mediaCryptoSuites.clear();
   if (secureCall)
   {
      configureMedia(charlieAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, cryptoSuite);
   }

   // Initial network
   ASSERT_EQ(charlie->network->networkTransport(), NetworkTransport::TransportWiFi);

   // Bob calls Charlie then Charlie hangs up
   SipConversationHandle bobCall = bob->conversation->createConversation(bob->handle);
   bob->conversation->configureMedia(bobCall, bobAudio);
   bob->conversation->addParticipant(bobCall, charlie->config.uri());
   bob->conversation->start(bobCall);
   auto bobConversationEvents = std::async(std::launch::async, [&]() {
      assertNewConversationOutgoingAudio_crypto(*bob.get(), bobCall, charlie->config.uri(), bobAudio, [](const NewConversationEvent& evt) {});
      assertConversationStateChanged(*bob.get(), bobCall, ConversationState_RemoteRinging);
      assertConversationMediaChangedAudio_crypto(*bob.get(), bobCall, cryptoSuite, bobAudio, bobAudio, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(*bob.get(), bobCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertMediaFlowing(*bob.get(), bobCall, true, false);
      // for network change re-INVITE
      assertConversationMediaChangeRequestAudio_crypto(*bob.get(), bobCall, charlieAudio, [](const ConversationMediaChangeRequestEvent& evt) {});
      assertSuccess(bob->conversation->accept(bobCall));
      assertConversationMediaChangedAudio_crypto(*bob.get(), bobCall, cryptoSuite, bobAudio, charlieAudio, [](const ConversationMediaChangedEvent& evt) {});

      assertConversationEnded(*bob.get(), bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   auto charlieConversationEvents = std::async(std::launch::async, [&]() {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle charlieCall;
      assertNewConversationIncomingAudio_crypto(*charlie.get(), &charlieCall, bob->config.uri(), bobAudio, [](const NewConversationEvent& evt) {});
      assertSuccess(charlie->conversation->sendRingingResponse(charlieCall));
      assertConversationStateChanged(*charlie.get(), charlieCall, ConversationState_LocalRinging);
      assertSuccess(charlie->conversation->accept(charlieCall));
      assertConversationMediaChanged(*charlie.get(), charlieCall, MediaDirection_SendReceive);
      assertConversationStateChanged(*charlie.get(), charlieCall, ConversationState_Connected);

      // Lose network access
      charlie->network->setNetworkTransport(NetworkTransport::TransportNone);
      std::set<resip::Data> none;
      charlie->network->setMockInterfaces(none);
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));

      // Regain network access but the network is different
      charlie->network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> wwan;
      wwan.insert("*******");
      charlie->network->setMockInterfaces(wwan);
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));

      assertAccountRefreshing(*charlie.get());
      assertAccountRegistered(*charlie.get());
      assertConversationMediaChangedAudio_crypto(*charlie.get(), charlieCall, cryptoSuite, charlieAudio, bobAudio, [](const ConversationMediaChangedEvent& evt) {});

      charlie->conversation->refreshConversationStatistics(charlieCall, true, true, true);

      {
         SipConversationHandle h;
         ConversationStatisticsUpdatedEvent evt;
         ASSERT_TRUE(charlie->conversationEvents->expectEvent("SipConversationHandler::onConversationStatisticsUpdated",
            60000, HandleEqualsPred<SipConversationHandle>(charlieCall), h, evt));
      }

      charlie->conversation->end(charlieCall);
      assertConversationEnded(*charlie.get(), charlieCall, ConversationEndReason_UserTerminatedLocally);

      // expect registration refresh after network change during a call
      assertAccountRefreshing(*charlie.get());
      assertAccountRegistered(*charlie.get());
   });
   waitFor2(bobConversationEvents, charlieConversationEvents);
}

TEST_P(ParameterizedNetworkChangeTest, BasicCallLoseNetworkRegainDifferentNetworkNoRegistrar)
{
   bool secureCall = GetParam().mSecureCall;

   std::unique_ptr<TestAccount> bob;
   bob = secureCall ? std::unique_ptr<SecureTestAccount>(new SecureTestAccount("bob", Account_NoInit)) : std::unique_ptr<TestAccount>(new TestAccount("bob", Account_NoInit));
   bob->config.settings.useRegistrar = false;
   bob->config.settings.useOutbound = false;
   bob->config.settings.domain = "bogus.invalid";
   // needed else bob adds c-line of network interface card
   bob->config.settings.sourceAddress = "127.0.0.1";
   bob->config.settings.outboundProxy = "";
   bob->config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   bob->init();
   bob->enable();

   std::unique_ptr<TestAccount> charlie;
   charlie = secureCall ? std::unique_ptr<SecureTestAccount>(new SecureTestAccount("charlie", Account_NoInit)) : std::unique_ptr<TestAccount>(new TestAccount("charlie", Account_NoInit));
   charlie->config.settings.domain = "autotest.cpcapi2";
   charlie->init();
   charlie->enable();

   const MediaCryptoSuite cryptoSuite = secureCall ? MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80 : MediaCryptoSuite_None;

   MediaInfo bobAudio;
   bobAudio.mediaEncryptionOptions.mediaCryptoSuites.clear();
   if (secureCall)
   {
      configureMedia(bobAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, cryptoSuite);
   }
   MediaInfo charlieAudio;
   charlieAudio.mediaEncryptionOptions.mediaCryptoSuites.clear();
   if (secureCall)
   {
      configureMedia(charlieAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, cryptoSuite);
   }

   // Initial network
   ASSERT_EQ(charlie->network->networkTransport(), NetworkTransport::TransportWiFi);

   // Bob calls Charlie then Charlie hangs up
   SipConversationHandle bobCall = bob->conversation->createConversation(bob->handle);
   bob->conversation->configureMedia(bobCall, bobAudio);
   bob->conversation->addParticipant(bobCall, charlie->config.uri());
   bob->conversation->start(bobCall);
   auto bobConversationEvents = std::async(std::launch::async, [&]() {
      assertNewConversationOutgoingAudio_crypto(*bob.get(), bobCall, charlie->config.uri(), bobAudio, [](const NewConversationEvent& evt) {});
      assertConversationStateChanged(*bob.get(), bobCall, ConversationState_RemoteRinging);
      assertConversationMediaChangedAudio_crypto(*bob.get(), bobCall, cryptoSuite, bobAudio, bobAudio, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(*bob.get(), bobCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertMediaFlowing(*bob.get(), bobCall, true, false);

      // Lose network access
      bob->network->setNetworkTransport(NetworkTransport::TransportNone);
      std::set<resip::Data> none;
      bob->network->setMockInterfaces(none);

      std::this_thread::sleep_for(std::chrono::milliseconds(10000));

      // Regain network access but the network is different
      bob->network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> wwan;
      wwan.insert("*******");
      bob->network->setMockInterfaces(wwan);
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));

      assertAccountRefreshing(*bob.get());
      assertAccountRegistered(*bob.get());

      assertConversationMediaChangedAudio_crypto(*bob.get(), bobCall, cryptoSuite, bobAudio, charlieAudio, [](const ConversationMediaChangedEvent& evt) {});

      // note we need to end it from Bob; if we end from Charlie, the BYE will fail as
      // bob does not have a port in his Contact header.
      bob->conversation->end(bobCall);

      assertConversationEnded(*bob.get(), bobCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto charlieConversationEvents = std::async(std::launch::async, [&]() {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle charlieCall;
      assertNewConversationIncomingAudio_crypto(*charlie.get(), &charlieCall, bob->config.uri(), bobAudio, [](const NewConversationEvent& evt) {});
      assertSuccess(charlie->conversation->sendRingingResponse(charlieCall));
      assertConversationStateChanged(*charlie.get(), charlieCall, ConversationState_LocalRinging);
      assertSuccess(charlie->conversation->accept(charlieCall));
      assertConversationMediaChanged(*charlie.get(), charlieCall, MediaDirection_SendReceive);
      assertConversationStateChanged(*charlie.get(), charlieCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(20000));

      // for network change re-INVITE
      assertConversationMediaChangeRequestAudio_crypto(*charlie.get(), charlieCall, bobAudio, [](const ConversationMediaChangeRequestEvent& evt) {});
      assertSuccess(charlie->conversation->accept(charlieCall));
      assertConversationMediaChangedAudio_crypto(*charlie.get(), charlieCall, cryptoSuite, charlieAudio, bobAudio, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationEnded(*charlie.get(), charlieCall, ConversationEndReason_UserTerminatedRemotely);
   });
   waitFor2(bobConversationEvents, charlieConversationEvents);
}

TEST_P(ParameterizedNetworkChangeTest, BasicCallLoseNetworkRegainDifferentNetworkStrettoTunnel)
{
   ReproHolder::destroyInstance();
   repro::ReproRunner* reproRunner = runRepro("repro_force_recordroute.config");

   bool secureCall = GetParam().mSecureCall;

   std::unique_ptr<TestAccount> bob;
   //bob = secureCall ? std::unique_ptr<SecureTestAccount>(new SecureTestAccount("bob", Account_NoInit)) : std::unique_ptr<TestAccount>(new TestAccount("bob"));
   bob.reset(new TestAccount("bob", Account_NoInit));
   bob->config.settings.useRegistrar = false;
   bob->config.settings.tunnelConfig.useTunnel = true;
   bob->config.settings.sipTransportType = SipAccountTransport_UDP;
   bob->config.settings.tunnelConfig.tunnelType = TunnelType_StrettoTunnel;
   // repro.config has been adjsuted to WSS listen on port 7061
   bob->config.settings.tunnelConfig.strettoTunnelURL = "wss://127.0.0.1:7061";
   // required since without this, calls over the Stretto tunnel end up
   // picking ethernet card IP address for c-line bob sends out
   bob->config.settings.sourceAddress = "127.0.0.1";
   bob->config.settings.ignoreCertVerification = true;
   bob->config.settings.tunnelConfig.strettoTunnelSkipHandshake = true;
   bob->init();
   bob->enable(false);
   assertAccountRegistering(*bob);
   assertAccountRegistered(*bob);

   std::unique_ptr<TestAccount> charlie;
   charlie = secureCall ? std::unique_ptr<SecureTestAccount>(new SecureTestAccount("charlie")) : std::unique_ptr<TestAccount>(new TestAccount("charlie"));

   const MediaCryptoSuite cryptoSuite = secureCall ? MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80 : MediaCryptoSuite_None;

   MediaInfo bobAudio;
   bobAudio.mediaEncryptionOptions.mediaCryptoSuites.clear();
   if (secureCall)
   {
      configureMedia(bobAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, cryptoSuite);
   }
   MediaInfo charlieAudio;
   charlieAudio.mediaEncryptionOptions.mediaCryptoSuites.clear();
   if (secureCall)
   {
      configureMedia(charlieAudio, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, cryptoSuite);
   }

   // Initial network
   ASSERT_EQ(charlie->network->networkTransport(), NetworkTransport::TransportWiFi);

   SipConversationHandle bobCall = bob->conversation->createConversation(bob->handle);
   bob->conversation->configureMedia(bobCall, bobAudio);
   bob->conversation->addParticipant(bobCall, charlie->config.uri());
   bob->conversation->start(bobCall);
   auto bobConversationEvents = std::async(std::launch::async, [&]() {
      assertNewConversationOutgoingAudio_crypto(*bob.get(), bobCall, charlie->config.uri(), bobAudio, [](const NewConversationEvent& evt) {});
      assertConversationStateChanged(*bob.get(), bobCall, ConversationState_RemoteRinging);
      assertConversationMediaChangedAudio_crypto(*bob.get(), bobCall, cryptoSuite, bobAudio, bobAudio, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(*bob.get(), bobCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertMediaFlowing(*bob.get(), bobCall, true, false);
      
      // Lose network access
      bob->network->setNetworkTransport(NetworkTransport::TransportNone);
      std::set<resip::Data> none;
      bob->network->setMockInterfaces(none);
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));

      // Regain network access but the network is different
      bob->network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> wwan;
      wwan.insert("*******");
      bob->network->setMockInterfaces(wwan);
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      
      // Stretto tunnel account goes to waiting to register after a network change, instead of refreshing (why?)
      assertAccountRefreshing(*bob.get());
      assertAccountWaitingToRegister(*bob.get());
      assertAccountRegistering(*bob.get());
      assertAccountRegistered(*bob.get());

      assertConversationMediaChangedAudio_crypto(*bob.get(), bobCall, cryptoSuite, bobAudio, charlieAudio, [](const ConversationMediaChangedEvent& evt) {});

      bob->conversation->end(bobCall);
      assertConversationEnded(*bob.get(), bobCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto charlieConversationEvents = std::async(std::launch::async, [&]() {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle charlieCall;
      assertNewConversationIncomingAudio_crypto(*charlie.get(), &charlieCall, bob->config.uri(), bobAudio, [](const NewConversationEvent& evt) {});
      assertSuccess(charlie->conversation->sendRingingResponse(charlieCall));
      assertConversationStateChanged(*charlie.get(), charlieCall, ConversationState_LocalRinging);
      assertSuccess(charlie->conversation->accept(charlieCall));
      assertConversationMediaChanged(*charlie.get(), charlieCall, MediaDirection_SendReceive);
      assertConversationStateChanged(*charlie.get(), charlieCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(20000));

      // for network change re-INVITE
      assertConversationMediaChangeRequestAudio_crypto(*charlie.get(), charlieCall, charlieAudio, [](const ConversationMediaChangeRequestEvent& evt) {});
      assertSuccess(charlie->conversation->accept(charlieCall));
      assertConversationMediaChangedAudio_crypto(*charlie.get(), charlieCall, cryptoSuite, charlieAudio, bobAudio, [](const ConversationMediaChangedEvent& evt) {});

      // limitation of this testing approach:
      // if charlie ends the call instead, repro won't route the BYE to bob because bob's WSS source port
      // changed after the network change websocket reconnect.
      assertConversationEnded(*charlie.get(), charlieCall, ConversationEndReason_UserTerminatedRemotely);
   });
   waitFor2(bobConversationEvents, charlieConversationEvents);

   runOriginalRepro();
}

class RtpPortCheckAdornmentHandler : public SipConversationAdornmentHandler
{
public:
   std::mutex mtx;
   std::condition_variable cv;
   std::atomic<bool> mWaiting;

   RtpPortCheckAdornmentHandler(CPCAPI2::SipConversation::SipConversationManager* convMgr, int minExpectedAudioPort, int maxExpectedAudioPort, int minExpectedVideoPort=0, int maxExpectedVideoPort=0) :
      mConvMgr(convMgr), mWaiting(false),
      mMinExpectedAudioPort(minExpectedAudioPort), mMaxExpectedAudioPort(maxExpectedAudioPort),
      mMinExpectedVideoPort(minExpectedVideoPort), mMaxExpectedVideoPort(maxExpectedVideoPort),
      mNumInvitesProcessed(0),
      mLastAudioPortUsed(-1), mLastVideoPortUsed(-1),
      mNumOfPossibleAudioPorts(0), mNumOfPossibleVideoPorts(0)
   {
      for (auto i = mMinExpectedAudioPort; i <= mMaxExpectedAudioPort; ++i)
      {
         if (i % 2 == 0 && i + 1 <= mMaxExpectedAudioPort)
         {
            ++mNumOfPossibleAudioPorts;
            ++i;
         }
      }

      for (auto i = mMinExpectedVideoPort; i <= mMaxExpectedVideoPort; ++i)
      {
         if (i % 2 == 0 && i + 1 <= mMaxExpectedVideoPort)
         {
            ++mNumOfPossibleVideoPorts;
            ++i;
         }
      }
   }

   virtual int onConversationAdornment(SipConversationHandle conversation, const ConversationAdornmentEvent& args)
   {
      const ConversationAdornmentInternalEvent* internal = static_cast<const ConversationAdornmentInternalEvent*>(&args);

      std::unique_lock<std::mutex> lck(mtx);
      resip::SdpContents* sdpContents = dynamic_cast<resip::SdpContents*>(internal->resipMsg.getContents());
      bool isInvite = args.method == "INVITE";

      if (sdpContents != NULL)
      {
         for (resip::SdpContents::Session::MediumContainer::const_iterator it = sdpContents->session().media().begin(); it != sdpContents->session().media().end(); ++it)
         {
            if (it->name() == "audio")
            {
               unsigned long port = it->port();

               std::cout << "<<<<<<<<<<<< " << (isInvite ? "INVITE" : "SDP") << " with audio port " << port << std::endl;

               if (mMinExpectedAudioPort != 0)
               {
                  EXPECT_GE(port, mMinExpectedAudioPort);
               }
               if (mMaxExpectedAudioPort != 0)
               {
                  EXPECT_LE(port, mMaxExpectedAudioPort);
               }

               if (isInvite)
               {
                  if (mNumOfPossibleAudioPorts == 1 && mLastAudioPortUsed != -1) EXPECT_EQ(mLastAudioPortUsed, port);
                  else EXPECT_NE(mLastAudioPortUsed, port);

                  mLastAudioPortUsed = port;
               }
            }
            else if (it->name() == "video")
            {
               unsigned long port = it->port();

               std::cout << "<<<<<<<<<<<< " << (isInvite ? "INVITE" : "SDP") << " with video port " << port << std::endl;

               if (mMinExpectedVideoPort != 0)
               {
                  EXPECT_GE(port, mMinExpectedVideoPort);
               }
               if (mMaxExpectedVideoPort != 0)
               {
                  EXPECT_LE(port, mMaxExpectedVideoPort);
               }

               if (isInvite)
               {
                  if (mNumOfPossibleVideoPorts == 1 && mLastVideoPortUsed != -1) EXPECT_EQ(mLastVideoPortUsed, port);
                  else EXPECT_NE(mLastVideoPortUsed, port);

                  mLastVideoPortUsed = port;
               }
            }
         }

         if (isInvite) ++mNumInvitesProcessed;
      }

      return kSuccess;
   }

   int numInvitesProcessed()
   {
      return mNumInvitesProcessed;
   }

private:
   CPCAPI2::SipConversation::SipConversationManager* mConvMgr;
   int mMinExpectedAudioPort, mMaxExpectedAudioPort;
   int mMinExpectedVideoPort, mMaxExpectedVideoPort;
   std::atomic_int mNumInvitesProcessed;
   int mLastAudioPortUsed;
   int mLastVideoPortUsed;
   int mNumOfPossibleAudioPorts;
   int mNumOfPossibleVideoPorts;
};

class mylatch
{
public:
   mylatch(int worker_count) : m_Counter(worker_count) {}

   // Just do a busy wait with a yield
   void wait(void)
   {
      // Because fetch_sub returns the previous value, test
      // for lower-than-or-equal to 1, which means we just went
      // to zero
      if (m_Counter.fetch_sub(1) <= 1)
         return;

      while (m_Counter > 0)
         std::this_thread::yield();
   }

private:
   std::atomic< int > m_Counter;
};

// tests app specifying limited RTP port range; during repeat network changes,
// SDK should stay within this port range for any re-INVITEs
TEST_F(NetworkChangeTest, RepeatNetworkChangeVerifyRtpPortRange)
{
   SipAccountTransportType transportType = SipAccountTransport_UDP;
   bool useRport = false;

   std::shared_ptr<TestAccount> alicePtr = TestAccountFactory::createTestAccount(transportType, "alice", Account_NoInit);
   TestAccount& alice = *alicePtr.get();
   alice.config.settings.useRport = useRport;
   alice.config.settings.sipTransportType = transportType;
   alice.init();
   alice.enable();

   std::shared_ptr<TestAccount> bobPtr = TestAccountFactory::createTestAccount(transportType, "bob", Account_NoInit);
   TestAccount& bob = *bobPtr.get();
   bob.config.settings.useRport = useRport;
   bob.config.settings.sipTransportType = transportType;
   bob.init();
   SipConversationSettings bobConversationSettings;
   bobConversationSettings.minRtpPortAudio = 50000;
   bobConversationSettings.maxRtpPortAudio = 50006; // if 50006 is assigned as RTP port but 50007 is out of range, the SDK should wrap back to 50000 and 50001 instead of using random ports
   bob.conversation->setDefaultSettings(bob.handle, bobConversationSettings);

   RtpPortCheckAdornmentHandler bobAdornmentHandler(bobPtr->conversation, bobConversationSettings.minRtpPortAudio, bobConversationSettings.maxRtpPortAudio);
   bob.conversation->setAdornmentHandler(bob.handle, &bobAdornmentHandler);

   bob.network->setNetworkTransport(NetworkTransport::TransportWiFi);

   bob.enable();

   const int bobNetworkChangeCount = 10;

   //const cpc::string& wavFilePath = "file:" + TestEnvironmentConfig::testResourcePath() + "ctclip3TestClip-16k.wav";

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   // The latch is needed because alice and bob need to be coordinated,
   // alice cannot be expecting both RTCP reports AND an incoming call
   // disconnect simultaneously
   mylatch lt(2);

   auto aliceEvents = std::async(std::launch::async, [&]() {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      //std::stringstream recordFileName;
      //recordFileName << TestEnvironmentConfig::tempPath() << "NetworkChangeTest.RepeatNetworkChangeVerifyRtpPortRange-alice" << resip::Random::getCryptoRandomHex(6).c_str() << ".wav";
      //CPCAPI2::Recording::RecorderHandle recorderHandle = alice.recording->audioRecorderCreate(recordFileName.str().c_str(), true);
      //assertSuccess(alice.recording->recorderAddConversation(recorderHandle, aliceCall));
      //assertSuccess(alice.recording->recorderStart(recorderHandle));

      for (int i = 0; i < bobNetworkChangeCount; ++i)
      {
         // bob's re-INVITE for bob's network change. we want to have a generous timeout so we
         // fail first on bob's thread if network change does not occur
         SipConversationHandle h;
         ConversationMediaChangeRequestEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onConversationMediaChangeRequest",
            45000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));

         assertSuccess(alice.conversation->accept(aliceCall));
         assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
         assertRTCP(alice, aliceCall, true, false);
      }

      // Notify the other thread we are ready to be disconnected
      lt.wait();

      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      //assertSuccess(alice.recording->recorderDestroy(recorderHandle));
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());

      assertSuccess(bob.conversation->accept(bobCall));


      //std::stringstream recordFileName;
      //recordFileName << TestEnvironmentConfig::tempPath() << "NetworkChangeTest.RepeatNetworkChangeVerifyRtpPortRange-bob" << resip::Random::getCryptoRandomHex(6).c_str() << ".wav";
      //CPCAPI2::Recording::RecorderHandle recorderHandle = bob.recording->audioRecorderCreate(recordFileName.str().c_str(), true);
      //assertSuccess(bob.recording->recorderAddConversation(recorderHandle, bobCall));
      //assertSuccess(bob.recording->recorderStart(recorderHandle));

      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });

      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::set<resip::Data> ifaces;

      for (int i = 0; i < bobNetworkChangeCount; ++i)
      {
         std::stringstream ss;
         // Mock network change manager starts with 127.0.0.1 already in place, so we need to use something different to trigger a
         // network change
         ss << "192.168.0." << i;
         ifaces.insert(ss.str().c_str());

         bob.network->setMockInterfaces(ifaces);

         {
            CPCAPI2::SipAccount::SipAccountHandle h;
            CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
            ASSERT_TRUE(bob.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
               40000, CPCAPI2::test::AlwaysTruePred(), h, evt));
            ASSERT_EQ(bob.handle, h);
            ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Refreshing, evt.accountStatus);
         }
         {
            CPCAPI2::SipAccount::SipAccountHandle h;
            CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
            ASSERT_TRUE(bob.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
               20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
            ASSERT_EQ(bob.handle, h);
            ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
         }

         assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
         assertRTCP(bob, bobCall, true, false);
      }

      // Notify the other thread we are ready to be disconnected
      lt.wait();

      std::this_thread::sleep_for(std::chrono::milliseconds(500));
      assertSuccess(bob.conversation->end(bobCall));

      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      //assertSuccess(bob.recording->recorderDestroy(recorderHandle));

      // expect registration refresh after network change during a call
      assertAccountRefreshing(bob);
      assertAccountRegistered(bob);

      ASSERT_EQ(bobAdornmentHandler.numInvitesProcessed(), bobNetworkChangeCount);
   });

   waitFor2Ms(aliceEvents, bobEvents, std::chrono::minutes(5))
}

// tests app specifying limited RTP port range; during repeat network changes,
// SDK should stay within this port range for any re-INVITEs
TEST_F(NetworkChangeTest, RepeatNetworkChangeVerifyRtpPortRange_WithSrtp)
{
   SecureTestAccount alice("alice");

   MediaInfo aliceAudio;
   aliceAudio.mediaDirection = MediaDirection_SendReceive;
   aliceAudio.mediaType = MediaType_Audio;
   aliceAudio.mediaEncryptionOptions.mediaEncryptionMode = MediaEncryptionMode_SRTP_SDES_Encrypted;
   aliceAudio.mediaEncryptionOptions.secureMediaRequired = true;

   MediaInfo bobAudio;
   bobAudio.mediaDirection = MediaDirection_SendReceive;
   bobAudio.mediaType = MediaType_Audio;
   bobAudio.mediaEncryptionOptions.mediaEncryptionMode = MediaEncryptionMode_SRTP_SDES_Encrypted;
   bobAudio.mediaEncryptionOptions.secureMediaRequired = true;


   SecureTestAccount bob("bob", Account_Init);
   SipConversationSettings bobConversationSettings;
   bobConversationSettings.minRtpPortAudio = 50000;
   bobConversationSettings.maxRtpPortAudio = 50004;
   bob.conversation->setDefaultSettings(bob.handle, bobConversationSettings);

   RtpPortCheckAdornmentHandler bobAdornmentHandler(bob.conversation, bobConversationSettings.minRtpPortAudio, bobConversationSettings.maxRtpPortAudio);
   bob.conversation->setAdornmentHandler(bob.handle, &bobAdornmentHandler);
   bob.enable();

   bob.network->setNetworkTransport(NetworkTransport::TransportWiFi);

   const int bobNetworkChangeCount = 10;

   //const cpc::string& wavFilePath = "file:" + TestEnvironmentConfig::testResourcePath() + "ctclip3TestClip-16k.wav";

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->configureMedia(aliceCall, aliceAudio);

   std::promise<void> aliceDone;

   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);
   
   auto aliceEvents = std::async(std::launch::async, [&]() {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      //std::stringstream recordFileName;
      //recordFileName << TestEnvironmentConfig::tempPath() << "NetworkChangeTest.RepeatNetworkChangeVerifyRtpPortRange-alice" << resip::Random::getCryptoRandomHex(6).c_str() << ".wav";
      //CPCAPI2::Recording::RecorderHandle recorderHandle = alice.recording->audioRecorderCreate(recordFileName.str().c_str(), true);
      //assertSuccess(alice.recording->recorderAddConversation(recorderHandle, aliceCall));
      //assertSuccess(alice.recording->recorderStart(recorderHandle));

      for (int i = 0; i < bobNetworkChangeCount; ++i)
      {
         // bob's re-INVITE for bob's network change. we want to have a generous timeout so we
         // fail first on bob's thread if network change does not occur
         SipConversationHandle h;
         ConversationMediaChangeRequestEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onConversationMediaChangeRequest",
            45000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));

         assertSuccess(alice.conversation->accept(aliceCall));
         assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
         TestCallEvents::expectRTCP(__LINE__, alice, aliceCall);
      }
      
      aliceDone.set_value();

      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      //assertSuccess(alice.recording->recorderDestroy(recorderHandle));
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());

      bob.conversation->configureMedia(bobCall, bobAudio);
      assertSuccess(bob.conversation->accept(bobCall));


      //std::stringstream recordFileName;
      //recordFileName << TestEnvironmentConfig::tempPath() << "NetworkChangeTest.RepeatNetworkChangeVerifyRtpPortRange-bob" << resip::Random::getCryptoRandomHex(6).c_str() << ".wav";
      //CPCAPI2::Recording::RecorderHandle recorderHandle = bob.recording->audioRecorderCreate(recordFileName.str().c_str(), true);
      //assertSuccess(bob.recording->recorderAddConversation(recorderHandle, bobCall));
      //assertSuccess(bob.recording->recorderStart(recorderHandle));

      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });

      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::set<resip::Data> ifaces;

      for (int i = 0; i < bobNetworkChangeCount; ++i)
      {
         std::stringstream ss;
         // Mock network change manager starts with 127.0.0.1 already in place, so we need to use something different to trigger a
         // network change
         ss << "192.168.0." << i;
         ifaces.insert(ss.str().c_str());

         bob.network->setMockInterfaces(ifaces);

         {
            CPCAPI2::SipAccount::SipAccountHandle h;
            CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
            ASSERT_TRUE(bob.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
               40000, CPCAPI2::test::AlwaysTruePred(), h, evt));
            ASSERT_EQ(bob.handle, h);
            ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Refreshing, evt.accountStatus);
         }
         {
            CPCAPI2::SipAccount::SipAccountHandle h;
            CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
            ASSERT_TRUE(bob.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
               20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
            ASSERT_EQ(bob.handle, h);
            ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
         }

         assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
         TestCallEvents::expectRTCP(__LINE__, bob, bobCall);
      }

      aliceDone.get_future().wait_for(std::chrono::seconds(10));
      //ASSERT_NO_THROW(aliceDone.get_future().get());
      try {
      aliceDone.get_future().get();
        } catch (const std::future_error& e) {
        //std::cout << "Caught a future_error with code \"" << e.code()
        //          << "\"\nMessage: \"" << e.what() << "\"\n";
        
        std::string w = e.what();
        w = w;
    }
      assertSuccess(bob.conversation->end(bobCall));

      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      //assertSuccess(bob.recording->recorderDestroy(recorderHandle));

      // expect registration refresh after network change during a call
      assertAccountRefreshing(bob);
      assertAccountRegistered(bob);

      ASSERT_EQ(bobAdornmentHandler.numInvitesProcessed(), bobNetworkChangeCount);
   });

   waitFor2Ms(aliceEvents, bobEvents, std::chrono::minutes(5))
}

// tests app specifying limited RTP port range; during repeat network changes,
// SDK should stay within this port range for any re-INVITEs
TEST_F(NetworkChangeTest, RepeatNetworkChangeVerifyRtpPortRange_ReusePorts)
{
   SipAccountTransportType transportType = SipAccountTransport_UDP;
   bool useRport = false;

   std::shared_ptr<TestAccount> alicePtr = TestAccountFactory::createTestAccount(transportType, "alice", Account_NoInit);
   TestAccount& alice = *alicePtr.get();
   alice.config.settings.useRport = useRport;
   alice.config.settings.sipTransportType = transportType;
   alice.init();
   alice.enable();

   std::shared_ptr<TestAccount> bobPtr = TestAccountFactory::createTestAccount(transportType, "bob", Account_NoInit);
   TestAccount& bob = *bobPtr.get();
   bob.config.settings.useRport = useRport;
   bob.config.settings.sipTransportType = transportType;
   bob.init();
   SipConversationSettings bobConversationSettings;
   bobConversationSettings.minRtpPortAudio = 50001;
   bobConversationSettings.maxRtpPortAudio = 50004; // total 4 ports available but only one potential RTP and RTCP pair
   bob.conversation->setDefaultSettings(bob.handle, bobConversationSettings);

   RtpPortCheckAdornmentHandler bobAdornmentHandler(bobPtr->conversation, bobConversationSettings.minRtpPortAudio, bobConversationSettings.maxRtpPortAudio);
   bob.conversation->setAdornmentHandler(bob.handle, &bobAdornmentHandler);

   bob.network->setNetworkTransport(NetworkTransport::TransportWiFi);

   bob.enable();

   const int bobNetworkChangeCount = 10;

   //const cpc::string& wavFilePath = "file:" + TestEnvironmentConfig::testResourcePath() + "ctclip3TestClip-16k.wav";

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   // The latch is needed because alice and bob need to be coordinated,
   // alice cannot be expecting both RTCP reports AND an incoming call
   // disconnect simultaneously
   mylatch lt(2);

   auto aliceEvents = std::async(std::launch::async, [&]() {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      //std::stringstream recordFileName;
      //recordFileName << TestEnvironmentConfig::tempPath() << "NetworkChangeTest.RepeatNetworkChangeVerifyRtpPortRange-alice" << resip::Random::getCryptoRandomHex(6).c_str() << ".wav";
      //CPCAPI2::Recording::RecorderHandle recorderHandle = alice.recording->audioRecorderCreate(recordFileName.str().c_str(), true);
      //assertSuccess(alice.recording->recorderAddConversation(recorderHandle, aliceCall));
      //assertSuccess(alice.recording->recorderStart(recorderHandle));

      for (int i = 0; i < bobNetworkChangeCount; ++i)
      {
         // bob's re-INVITE for bob's network change. we want to have a generous timeout so we
         // fail first on bob's thread if network change does not occur
         SipConversationHandle h;
         ConversationMediaChangeRequestEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onConversationMediaChangeRequest",
            45000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));

         assertSuccess(alice.conversation->accept(aliceCall));
         assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
         assertRTCP(alice, aliceCall, true, false);
      }

      // Notify the other thread we are ready to be disconnected
      lt.wait();

      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      //assertSuccess(alice.recording->recorderDestroy(recorderHandle));
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());

      assertSuccess(bob.conversation->accept(bobCall));


      //std::stringstream recordFileName;
      //recordFileName << TestEnvironmentConfig::tempPath() << "NetworkChangeTest.RepeatNetworkChangeVerifyRtpPortRange-bob" << resip::Random::getCryptoRandomHex(6).c_str() << ".wav";
      //CPCAPI2::Recording::RecorderHandle recorderHandle = bob.recording->audioRecorderCreate(recordFileName.str().c_str(), true);
      //assertSuccess(bob.recording->recorderAddConversation(recorderHandle, bobCall));
      //assertSuccess(bob.recording->recorderStart(recorderHandle));

      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });

      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::set<resip::Data> ifaces;

      for (int i = 0; i < bobNetworkChangeCount; ++i)
      {
         std::stringstream ss;
         // Mock network change manager starts with 127.0.0.1 already in place, so we need to use something different to trigger a
         // network change
         ss << "192.168.0." << i;
         ifaces.insert(ss.str().c_str());

         bob.network->setMockInterfaces(ifaces);

         {
            CPCAPI2::SipAccount::SipAccountHandle h;
            CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
            ASSERT_TRUE(bob.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
               40000, CPCAPI2::test::AlwaysTruePred(), h, evt));
            ASSERT_EQ(bob.handle, h);
            ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Refreshing, evt.accountStatus);
         }
         {
            CPCAPI2::SipAccount::SipAccountHandle h;
            CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
            ASSERT_TRUE(bob.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
               20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
            ASSERT_EQ(bob.handle, h);
            ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
         }

         assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
         assertRTCP(bob, bobCall, true, false);
      }

      // Notify the other thread we are ready to be disconnected
      lt.wait();

      std::this_thread::sleep_for(std::chrono::milliseconds(500));
      assertSuccess(bob.conversation->end(bobCall));

      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      //assertSuccess(bob.recording->recorderDestroy(recorderHandle));

      // expect registration refresh after network change during a call
      assertAccountRefreshing(bob);
      assertAccountRegistered(bob);

      ASSERT_EQ(bobAdornmentHandler.numInvitesProcessed(), bobNetworkChangeCount);
   });

   waitFor2Ms(aliceEvents, bobEvents, std::chrono::minutes(5))
}

// tests app specifying limited RTP port range; during repeat network changes,
// SDK should stay within this port range for any re-INVITEs
TEST_F(NetworkChangeTest, RepeatNetworkChangeVerifyRtpPortRange_WithVideo)
{
   SipAccountTransportType transportType = SipAccountTransport_UDP;
   bool useRport = false;

   std::shared_ptr<TestAccount> alicePtr = TestAccountFactory::createTestAccount(transportType, "alice", Account_NoInit);
   TestAccount& alice = *alicePtr.get();
   alice.config.settings.useRport = useRport;
   alice.config.settings.sipTransportType = transportType;
   alice.init();
   alice.enable();

   std::shared_ptr<TestAccount> bobPtr = TestAccountFactory::createTestAccount(transportType, "bob", Account_NoInit);
   TestAccount& bob = *bobPtr.get();
   bob.config.settings.useRport = useRport;
   bob.config.settings.sipTransportType = transportType;
   bob.init();
   SipConversationSettings bobConversationSettings;
   bobConversationSettings.minRtpPortAudio = 50000;
   bobConversationSettings.maxRtpPortAudio = 50002;
   bobConversationSettings.minRtpPortVideo = 50004;
   bobConversationSettings.maxRtpPortVideo = 50006;
   bob.conversation->setDefaultSettings(bob.handle, bobConversationSettings);

   RtpPortCheckAdornmentHandler bobAdornmentHandler(bobPtr->conversation, bobConversationSettings.minRtpPortAudio, bobConversationSettings.maxRtpPortAudio,
                                                    bobConversationSettings.minRtpPortVideo, bobConversationSettings.maxRtpPortVideo);
   bob.conversation->setAdornmentHandler(bob.handle, &bobAdornmentHandler);

   bob.network->setNetworkTransport(NetworkTransport::TransportWiFi);

   bob.enable();

   alice.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   bob.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   alice.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   bob.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

#if _WIN32
   HWND hwndAliceCapture = NULL;
   ASSERT_EQ(0, ViECreateWindow(hwndAliceCapture, 0, 0, 352, 288, "Alice (capture)"));
   alice.video->startCapture();
   alice.video->setLocalVideoRenderTarget(hwndAliceCapture);

   HWND hwndAliceRemote = NULL;
   ASSERT_EQ(0, ViECreateWindow(hwndAliceRemote, 356, 0, 352, 288, "Alice (incoming)"));
   alice.video->setIncomingVideoRenderTarget(hwndAliceRemote);

   HWND hwndBobCapture = NULL;
   ASSERT_EQ(0, ViECreateWindow(hwndBobCapture, 0, 292, 352, 288, "Bob (capture)"));
   bob.video->startCapture();
   bob.video->setLocalVideoRenderTarget(hwndBobCapture);

   HWND hwndBobRemote = NULL;
   ASSERT_EQ(0, ViECreateWindow(hwndBobRemote, 356, 292, 352, 288, "Bob (incoming)"));
   bob.video->setIncomingVideoRenderTarget(hwndBobRemote);
#endif // _WIN32

   const int bobNetworkChangeCount = 10;

   //const cpc::string& wavFilePath = "file:" + TestEnvironmentConfig::testResourcePath() + "ctclip3TestClip-16k.wav";

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   // The latch is needed because alice and bob need to be coordinated,
   // alice cannot be expecting both RTCP reports AND an incoming call
   // disconnect simultaneously
   mylatch lt(2);

   auto aliceEvents = std::async(std::launch::async, [&]() {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      //std::stringstream recordFileName;
      //recordFileName << TestEnvironmentConfig::tempPath() << "NetworkChangeTest.RepeatNetworkChangeVerifyRtpPortRange-alice" << resip::Random::getCryptoRandomHex(6).c_str() << ".wav";
      //CPCAPI2::Recording::RecorderHandle recorderHandle = alice.recording->audioRecorderCreate(recordFileName.str().c_str(), true);
      //assertSuccess(alice.recording->recorderAddConversation(recorderHandle, aliceCall));
      //assertSuccess(alice.recording->recorderStart(recorderHandle));

      for (int i = 0; i < bobNetworkChangeCount; ++i)
      {
         // bob's re-INVITE for bob's network change. we want to have a generous timeout so we
         // fail first on bob's thread if network change does not occur
         SipConversationHandle h;
         ConversationMediaChangeRequestEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onConversationMediaChangeRequest",
            45000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));

         assertSuccess(alice.conversation->accept(aliceCall));
         assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
         assertRTCP(alice, aliceCall, true, true);
      }

      // Notify the other thread we are ready to be disconnected
      lt.wait();

      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      //assertSuccess(alice.recording->recorderDestroy(recorderHandle));
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());

      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      assertSuccess(bob.conversation->accept(bobCall));


      //std::stringstream recordFileName;
      //recordFileName << TestEnvironmentConfig::tempPath() << "NetworkChangeTest.RepeatNetworkChangeVerifyRtpPortRange-bob" << resip::Random::getCryptoRandomHex(6).c_str() << ".wav";
      //CPCAPI2::Recording::RecorderHandle recorderHandle = bob.recording->audioRecorderCreate(recordFileName.str().c_str(), true);
      //assertSuccess(bob.recording->recorderAddConversation(recorderHandle, bobCall));
      //assertSuccess(bob.recording->recorderStart(recorderHandle));

      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });

      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::seconds(5));

      std::set<resip::Data> ifaces;

      for (int i = 0; i < bobNetworkChangeCount; ++i)
      {
         std::stringstream ss;
         // Mock network change manager starts with 127.0.0.1 already in place, so we need to use something different to trigger a
         // network change
         ss << "192.168.0." << i;
         ifaces.insert(ss.str().c_str());

         bob.network->setMockInterfaces(ifaces);

         {
            CPCAPI2::SipAccount::SipAccountHandle h;
            CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
            ASSERT_TRUE(bob.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
               40000, CPCAPI2::test::AlwaysTruePred(), h, evt));
            ASSERT_EQ(bob.handle, h);
            ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Refreshing, evt.accountStatus);
         }
         {
            CPCAPI2::SipAccount::SipAccountHandle h;
            CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
            ASSERT_TRUE(bob.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
               20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
            ASSERT_EQ(bob.handle, h);
            ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
         }

         assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
         assertRTCP(bob, bobCall, true, true);
      }

      // Notify the other thread we are ready to be disconnected
      lt.wait();

      std::this_thread::sleep_for(std::chrono::milliseconds(500));
      assertSuccess(bob.conversation->end(bobCall));

      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      //assertSuccess(bob.recording->recorderDestroy(recorderHandle));

      // expect registration refresh after network change during a call
      assertAccountRefreshing(bob);
      assertAccountRegistered(bob);

      ASSERT_EQ(bobAdornmentHandler.numInvitesProcessed(), bobNetworkChangeCount);
   });

   waitFor2Ms(aliceEvents, bobEvents, std::chrono::minutes(5))
}


TEST_F(NetworkChangeTest, RTPPortAllocatorRandom)
{
   recon::RTPPortAllocator allocator;

   for (int i = 0; i < 500; i++)
   {
      int port = allocator.getRandomRTPPort(1, 100);
      ASSERT_TRUE(port >= 1 && port <= 99);  // must be in range (100 isn't valid, we need two in a row)
      ASSERT_TRUE(port % 2 == 0);            // must be even
   }
   int port = allocator.getRandomRTPPort(98, 100);
   ASSERT_TRUE(port == 98);   // only possibility since we want two values
   port = allocator.getRandomRTPPort(97, 99);
   ASSERT_TRUE(port == 98);   // must return an even number
   port = allocator.getRandomRTPPort(99, 100);
   ASSERT_TRUE(port == 0);    // there aren't two numbers available with the first being even
}

TEST_F(NetworkChangeTest, RTPPortAllocatorAllocateOnce)
{
   recon::RTPPortAllocator allocator;
   unsigned int rtpPort;
   unsigned int rtcpPort;
   resip::Data targetInterfaceIp = "127.0.0.1";
   
   for (int i = 0; i < 100; i++)
   {
      bool res = allocator.allocateRTPPortFromRange(50001, 50020, rtpPort, rtcpPort, targetInterfaceIp);
      
      ASSERT_TRUE(res);
      ASSERT_TRUE(rtcpPort == rtpPort+1);
      ASSERT_TRUE(rtpPort >= 50002 && rtpPort <= 50019);
      ASSERT_TRUE(rtcpPort >= 50003 && rtcpPort <= 50020);
      
      allocator.freeRTPPort(rtpPort, rtcpPort);
   }
}

TEST_F(NetworkChangeTest, RTPPortAllocatorAllocateTwice)
{
   recon::RTPPortAllocator allocator;
   unsigned int rtpPort1, rtpPort2;
   unsigned int rtcpPort1, rtcpPort2;
   resip::Data targetInterfaceIp = "127.0.0.1";

   for (int i = 0; i < 100; i++)
   {
      // this simulates a call with audio and video...
      bool res1 = allocator.allocateRTPPortFromRange(50001, 50020, rtpPort1, rtcpPort1, targetInterfaceIp);
      bool res2 = allocator.allocateRTPPortFromRange(50001, 50020, rtpPort2, rtcpPort2, targetInterfaceIp);

      ASSERT_TRUE(res1);
      ASSERT_TRUE(res2);
      ASSERT_TRUE(rtcpPort1 == rtpPort1+1);
      ASSERT_TRUE(rtcpPort2 == rtpPort2+1);
      ASSERT_TRUE(rtpPort1 != rtpPort2);

      allocator.freeRTPPort(rtpPort1, rtcpPort1);
      allocator.freeRTPPort(rtpPort2, rtcpPort2);
   }
}

TEST_F(NetworkChangeTest, BasicCallMultiNetworkChangeNoRegistrar)
{
   std::unique_ptr<TestAccount> bob;
   bob = std::unique_ptr<TestAccount>(new TestAccount("bob", Account_NoInit));
   bob->config.settings.useRegistrar = false;
   bob->config.settings.useOutbound = false;
   // useRegistrar off so INVITE routed via domain of participant URI
   bob->config.settings.domain = "bogus.invalid";
   // needed else bob adds c-line of network interface card
   bob->config.settings.sourceAddress = "127.0.0.1";
   bob->config.settings.outboundProxy = "";
   bob->config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   bob->init();
   bob->enable();

   std::unique_ptr<TestAccount> charlie;
   charlie = std::unique_ptr<TestAccount>(new TestAccount("charlie", Account_NoInit));
   charlie->config.settings.domain = "autotest.cpcapi2";
   charlie->init();
   charlie->enable();

   const MediaCryptoSuite cryptoSuite = MediaCryptoSuite_None;

   MediaInfo bobAudio;
   bobAudio.mediaEncryptionOptions.mediaCryptoSuites.clear();
   MediaInfo charlieAudio;
   charlieAudio.mediaEncryptionOptions.mediaCryptoSuites.clear();

   // Initial network
   ASSERT_EQ(charlie->network->networkTransport(), NetworkTransport::TransportWiFi);

   // Bob calls Charlie then Charlie hangs up
   SipConversationHandle bobCall = bob->conversation->createConversation(bob->handle);
   SipConversationSettings bobConversationSettings;
   bobConversationSettings.minRtpPort = 7000;
   bobConversationSettings.maxRtpPort = 7009;
   bobConversationSettings.minRtpPortAudio = 17000;
   bobConversationSettings.maxRtpPortAudio = 17009;
   bobConversationSettings.minRtpPortVideo = 27000;
   bobConversationSettings.maxRtpPortVideo = 27009;
   bob->conversation->setDefaultSettings(bob->handle, bobConversationSettings);

   bob->conversation->configureMedia(bobCall, bobAudio);
   bob->conversation->addParticipant(bobCall, charlie->config.uri());
   bob->conversation->start(bobCall);

   auto bobConversationEvents = std::async(std::launch::async, [&]()
   {
      assertNewConversationOutgoingAudio_crypto(*bob.get(), bobCall, charlie->config.uri(), bobAudio, [](const NewConversationEvent& evt) {});
      assertConversationStateChanged(*bob.get(), bobCall, ConversationState_RemoteRinging);
      assertConversationMediaChangedAudio_crypto(*bob.get(), bobCall, cryptoSuite, bobAudio, bobAudio, [](const ConversationMediaChangedEvent& evt) {});
      assertConversationStateChanged(*bob.get(), bobCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(*bob.get(), bobCall, true, false);

      std::unique_ptr<ReconRtpPortSaturator> rtpPortSaturator(new ReconRtpPortSaturator());

      // Regain network access but the network is different
      bob->network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> wwan;
      wwan.insert("*******");
      bob->network->setMockInterfaces(wwan);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertAccountRefreshing(*bob.get());
      assertAccountRegistered(*bob.get());

      // free fake reserved ports
      rtpPortSaturator.reset();

      // Regain network access but the network is different
      bob->network->setNetworkTransport(NetworkTransport::TransportWiFi);
      std::set<resip::Data> wifi;
      wifi.insert("*******");
      bob->network->setMockInterfaces(wifi);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertAccountRefreshing(*bob.get());
      assertAccountRegistered(*bob.get());

      // note we need to end it from Bob; if we end from Charlie, the BYE will fail as
      // bob does not have a port in his Contact header.
      bob->conversation->end(bobCall);

      assertConversationEnded(*bob.get(), bobCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto charlieConversationEvents = std::async(std::launch::async, [&]()
   {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle charlieCall;
      assertNewConversationIncomingAudio_crypto(*charlie.get(), &charlieCall, bob->config.uri(), bobAudio, [](const NewConversationEvent& evt) {});
      assertSuccess(charlie->conversation->sendRingingResponse(charlieCall));
      assertConversationStateChanged(*charlie.get(), charlieCall, ConversationState_LocalRinging);
      assertSuccess(charlie->conversation->accept(charlieCall));
      assertConversationMediaChanged(*charlie.get(), charlieCall, MediaDirection_SendReceive);
      assertConversationStateChanged(*charlie.get(), charlieCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(15000));

      assertConversationEnded(*charlie.get(), charlieCall, ConversationEndReason_UserTerminatedRemotely);
   });
   waitFor2(bobConversationEvents, charlieConversationEvents);
}

TEST_F(NetworkChangeTest, NetworkChangeOnCallSenderNoRegistrar)
{
   ReproHolder::destroyInstance();
   repro::ReproRunner* reproRunner = runRepro("repro_5060.config");

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.useRegistrar = false;
   alice.config.settings.domain = "autotest.cpcapi2";
   alice.config.settings.outboundProxy = "127.0.0.1";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.enable(true);

   TestAccount bob("bob", Account_NoInit);
   bob.config.settings.domain = "autotest.cpcapi2";
   bob.config.settings.outboundProxy = "127.0.0.1";
   bob.enable(true);

   // Initial network
   ASSERT_EQ(alice.network->networkTransport(), NetworkTransport::TransportWiFi);
   ASSERT_EQ(bob.network->networkTransport(), NetworkTransport::TransportWiFi);

   // Alice calls Bob
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);
   auto aliceConversationEvents = std::async(std::launch::async, [&]()
   {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCall, true, false);

      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> wwan;
      wwan.insert("*******");
      alice.network->setMockInterfaces(wwan);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      alice.conversationEvents->clearCommands();
      alice.accountEvents->clearCommands();
      assertMediaFlowing(alice, aliceCall, true, false);

      alice.network->setNetworkTransport(NetworkTransport::TransportWiFi);
      std::set<resip::Data> wifi;
      wifi.insert("*******");
      alice.network->setMockInterfaces(wifi);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      alice.conversationEvents->clearCommands();
      alice.accountEvents->clearCommands();
      assertMediaFlowing(alice, aliceCall, true, false);

      alice.conversation->end(aliceCall);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobConversationEvents = std::async(std::launch::async, [&]()
   {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
      assertSuccess(bob.conversation->accept(bobCall));

      assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
      assertSuccess(bob.conversation->accept(bobCall));

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      bob.conversationEvents->clearCommands();
      bob.accountEvents->clearCommands();
      assertMediaFlowing(bob, bobCall, true, false);

      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });
   waitFor2(aliceConversationEvents, bobConversationEvents);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   runOriginalRepro(reproRunner);
}

TEST_F(NetworkChangeTest, NetworkChangeOnCallReceiverNoRegistrar)
{
   ReproHolder::destroyInstance();
   repro::ReproRunner* reproRunner = runRepro("repro_5060.config");

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.domain = "autotest.cpcapi2";
   alice.config.settings.outboundProxy = "127.0.0.1";
   alice.enable(true);

   TestAccount bob("bob", Account_NoInit);
   resip::Uri bobTarget;
   bobTarget.user() = bob.config.settings.username;
   bobTarget.host() = "127.0.0.1";
   bobTarget.port() = 5055;
   bob.config.settings.domain = "autotest.cpcapi2";
   bob.config.settings.outboundProxy = "127.0.0.1";
   bob.config.settings.minSipPort = bobTarget.port();
   bob.config.settings.maxSipPort = bobTarget.port();
   bob.config.settings.useRegistrar = false;
   bob.enable(true);

   // Initial network
   ASSERT_EQ(alice.network->networkTransport(), NetworkTransport::TransportWiFi);
   ASSERT_EQ(bob.network->networkTransport(), NetworkTransport::TransportWiFi);

   // Alice calls Bob
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bobTarget.getAOR(true).c_str());
   alice.conversation->start(aliceCall);
   auto aliceConversationEvents = std::async(std::launch::async, [&]()
   {
      assertNewConversationOutgoing(alice, aliceCall, bobTarget.getAOR(false).c_str());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCall, true, false);

      assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendReceive);
      assertSuccess(alice.conversation->accept(aliceCall));

      assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendReceive);
      assertSuccess(alice.conversation->accept(aliceCall));

      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      alice.conversation->end(aliceCall);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobConversationEvents = std::async(std::launch::async, [&]()
   {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      bob.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> wwan;
      wwan.insert("*******");
      bob.network->setMockInterfaces(wwan);
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      bob.conversationEvents->clearCommands();
      bob.accountEvents->clearCommands();
      assertMediaFlowing(bob, bobCall, true, false);

      bob.network->setNetworkTransport(NetworkTransport::TransportWiFi);
      std::set<resip::Data> wifi;
      wifi.insert("*******");
      bob.network->setMockInterfaces(wifi);
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      bob.conversationEvents->clearCommands();
      bob.accountEvents->clearCommands();
      assertMediaFlowing(bob, bobCall, true, false);

      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });
   waitFor2(aliceConversationEvents, bobConversationEvents);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   runOriginalRepro(reproRunner);
}

TEST_F(NetworkChangeTest, FrequentNetworkChangesDuringOutgoingCall)
{
   TestAccount alice("alice");
   TestAccount bob("bob");

   // Initial network
   ASSERT_EQ(alice.network->networkTransport(), NetworkTransport::TransportWiFi);
   ASSERT_EQ(bob.network->networkTransport(), NetworkTransport::TransportWiFi);

   // Alice calls Bob then Bob hangs up
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);
   auto aliceConversationEvents = std::async(std::launch::async, [&] ()
   {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertMediaFlowing(alice, aliceCall, true, false);

      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> wwan;
      wwan.insert("*******");
      alice.network->setMockInterfaces(wwan);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);

      alice.network->setNetworkTransport(NetworkTransport::TransportWiFi);
      std::set<resip::Data> wifi;
      wifi.insert("*******");
      alice.network->setMockInterfaces(wifi);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);

      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> wwan2;
      wwan2.insert("*******");
      alice.network->setMockInterfaces(wwan2);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);

      alice.network->setNetworkTransport(NetworkTransport::TransportWiFi);
      std::set<resip::Data> wifi2;
      wifi2.insert("*******");
      alice.network->setMockInterfaces(wifi2);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);

      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> wwan3;
      wwan3.insert("*******");
      alice.network->setMockInterfaces(wwan3);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);

      alice.network->setNetworkTransport(NetworkTransport::TransportWiFi);
      std::set<resip::Data> wifi3;
      wifi3.insert("*******");
      alice.network->setMockInterfaces(wifi3);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      alice.conversationEvents->clearCommands();
      alice.accountEvents->clearCommands();
      assertMediaFlowing(alice, aliceCall, true, false);

      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      alice.conversation->end(aliceCall);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);

      // expect registration refresh after network change during a call
      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);
   });

   auto bobConversationEvents = std::async(std::launch::async, [&] ()
   {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      bob.conversation->sendRingingResponse(bobCall);
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
      assertSuccess(bob.conversation->accept(bobCall));

      assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
      assertSuccess(bob.conversation->accept(bobCall));

      assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
      assertSuccess(bob.conversation->accept(bobCall));

      assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
      assertSuccess(bob.conversation->accept(bobCall));

      assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
      assertSuccess(bob.conversation->accept(bobCall));

      assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
      assertSuccess(bob.conversation->accept(bobCall));

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(bob, bobCall, true, false);

      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor2(aliceConversationEvents, bobConversationEvents);
}

// 1. alice calls bob
// 2. alice encounters a network change; sends re-INVITE
// 3. before alice receives a response to re-INVITE, another network change occurrs
// 4. test case asserts that SipAccountHandler::onError is fired for the second
//    network change re-INVITE that fails due to previous pending re-INVITE
TEST_F(NetworkChangeTest, NetworkChangeIncompletePreviousNetworkChange)
{
   TestAccount alice("alice");
   TestAccount bob("bob");

   // Initial network
   ASSERT_EQ(alice.network->networkTransport(), NetworkTransport::TransportWiFi);
   ASSERT_EQ(bob.network->networkTransport(), NetworkTransport::TransportWiFi);


   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   std::atomic_bool bobReceivedReinvite = false;
   std::atomic_bool aliceReceivedAccountError = false;

   auto aliceConversationEvents = std::async(std::launch::async, [&] ()
   {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertMediaFlowing(alice, aliceCall, true, false);

      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> wwan;
      wwan.insert("*******");
      alice.network->setMockInterfaces(wwan);

      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);

      while (!bobReceivedReinvite)
      {
         std::this_thread::yield();
      }
      
      alice.network->setNetworkTransport(NetworkTransport::TransportWiFi);
      std::set<resip::Data> wifi;
      wifi.insert("*******");
      alice.network->setMockInterfaces(wifi);

      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);

      assertAccountError(alice, "Can't provide an offer");
      aliceReceivedAccountError = true;

      // for the purposes of speeding up this test case run, bob will accept the re-INVITE after
      // the "Can't provide an offer" exception is seen on alice's side
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
   

      alice.conversation->end(aliceCall);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);

      // expect registration refresh after network change during a call
      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);
   });

   auto bobConversationEvents = std::async(std::launch::async, [&] ()
   {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      bob.conversation->sendRingingResponse(bobCall);
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
      bobReceivedReinvite = true;
      // don't immediately accept the re-INVITE; we want to simulate two network changes on alice's side that happen
      // too quickly (i.e. before the first re-INVITE is accepted the second network change happens)

      while (aliceReceivedAccountError)
      {
         std::this_thread::yield();
      }

      std::this_thread::sleep_for(std::chrono::seconds(5));

      bob.conversation->accept(bobCall);

      
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor2(aliceConversationEvents, bobConversationEvents);
}

TEST_F(NetworkChangeTest, FrequentNetworkChangesDuringOngoingInviteTransactions)
{
   TestAccount alice("alice");
   TestAccount bob("bob");

   // Initial network
   ASSERT_EQ(alice.network->networkTransport(), NetworkTransport::TransportWiFi);
   ASSERT_EQ(bob.network->networkTransport(), NetworkTransport::TransportWiFi);

   // Alice calls Bob then Bob hangs up
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);
   std::atomic<bool> accepting = true;
   auto aliceConversationEvents = std::async(std::launch::async, [&] ()
   {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertMediaFlowing(alice, aliceCall, true, false);

      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> wwan;
      wwan.insert("*******");
      alice.network->setMockInterfaces(wwan);

      alice.network->setNetworkTransport(NetworkTransport::TransportWiFi);
      std::set<resip::Data> wifi;
      wifi.insert("*******");
      alice.network->setMockInterfaces(wifi);

      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> wwan2;
      wwan2.insert("*******");
      alice.network->setMockInterfaces(wwan2);

      alice.network->setNetworkTransport(NetworkTransport::TransportWiFi);
      std::set<resip::Data> wifi2;
      wifi2.insert("*******");
      alice.network->setMockInterfaces(wifi2);

      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> wwan3;
      wwan3.insert("*******");
      alice.network->setMockInterfaces(wwan3);

      alice.conversationEvents->clearCommands();
      alice.accountEvents->clearCommands();
      alice.network->setNetworkTransport(NetworkTransport::TransportWiFi);
      std::set<resip::Data> wifi3;
      wifi3.insert("*******");
      alice.network->setMockInterfaces(wifi3);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      alice.conversationEvents->clearCommands();
      alice.accountEvents->clearCommands();
      assertMediaFlowing(alice, aliceCall, true, false);

      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      accepting = false;
      alice.conversation->end(aliceCall);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);

      // expect registration refresh after network change during a call
      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);
   });

   auto bobConversationEvents = std::async(std::launch::async, [&] ()
   {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      bob.conversation->sendRingingResponse(bobCall);
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      while (accepting)
      {
         {
            SipConversationHandle h;
            ConversationMediaChangeRequestEvent evt;
            bool received = bob.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onConversationMediaChangeRequest", 15000, HandleEqualsPred<SipConversationHandle>(bobCall), h, evt);
            if (received)
            {
               ASSERT_LE((size_t)1, evt.remoteMediaInfo.size());
               for(cpc::vector<MediaInfo>::const_iterator it=evt.remoteMediaInfo.begin(); it != evt.remoteMediaInfo.end(); it++)
               {
                  ASSERT_EQ(MediaDirection_SendReceive, it->mediaDirection);
               }

               assertSuccess(bob.conversation->accept(bobCall));
            }
            else
            {
               ASSERT_FALSE(accepting); // event will not be received if accepting is disabled
            }
         }
      }

      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor2(aliceConversationEvents, bobConversationEvents);
}

TEST_F(NetworkChangeTest, NetworkChangeOnStrettoTunnelCall)
{
   ReproHolder::destroyInstance();

   // need repro to force all traffic through it, otherwise Charlie won't be
   // able to e.g. send a BYE reques to Bob
   repro::ReproRunner* reproRunner = runRepro("repro_force_recordroute.config");

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.useRegistrar = false;
   alice.config.settings.tunnelConfig.useTunnel = true;
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.tunnelConfig.tunnelType = TunnelType_StrettoTunnel;
   // repro.config has been adjsuted to WSS listen on port 7061
   alice.config.settings.tunnelConfig.strettoTunnelURL = "wss://127.0.0.1:7061";
   alice.config.settings.ignoreCertVerification = true;
   alice.config.settings.tunnelConfig.strettoTunnelSkipHandshake = true;
   alice.config.settings.sourceAddress = "127.0.0.1";

   alice.config.settings.enableNat64Support = true;
   alice.config.settings.ipVersion = IpVersion_Auto;
   alice.config.settings.useInstanceId = true;
   alice.config.settings.useRport = true;
   alice.config.settings.useRinstance = true;
   alice.config.settings.alwaysRouteViaOutboundProxy = false;
   alice.config.settings.sslVersion = SSL_HIGHEST;
   alice.config.settings.sessionTimerMode = SipAccountSessionTimerMode_Inactive;
   alice.config.settings.tunnelConfig.transportType = TunnelTransport_TCP;

   alice.init();
   alice.enable(false);
   assertAccountRegistering(alice);
   assertAccountRegistered(alice);

   TestAccount bob("bob");

   // Initial network
   ASSERT_EQ(alice.network->networkTransport(), NetworkTransport::TransportWiFi);
   ASSERT_EQ(bob.network->networkTransport(), NetworkTransport::TransportWiFi);

   // Alice calls Bob then Bob hangs up
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);
   auto aliceConversationEvents = std::async(std::launch::async, [&] ()
   {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertMediaFlowing(alice, aliceCall, true, false);

      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> wwan;
      wwan.insert("*******");
      alice.network->setMockInterfaces(wwan);

      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      alice.conversationEvents->clearCommands();
      alice.accountEvents->clearCommands();
      assertMediaFlowing(alice, aliceCall, true, false);

      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      alice.network->setNetworkTransport(NetworkTransport::TransportWiFi);
      std::set<resip::Data> wifi;
      wifi.insert("*******");
      alice.network->setMockInterfaces(wifi);

      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      alice.conversationEvents->clearCommands();
      alice.accountEvents->clearCommands();
      assertMediaFlowing(alice, aliceCall, true, false);

      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      alice.conversation->end(aliceCall);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobConversationEvents = std::async(std::launch::async, [&] ()
   {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      bob.conversation->sendRingingResponse(bobCall);
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertMediaFlowing(bob, bobCall, true, false);

      assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
      assertSuccess(bob.conversation->accept(bobCall));

      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertMediaFlowing(bob, bobCall, true, false);

      assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
      assertSuccess(bob.conversation->accept(bobCall));

      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertMediaFlowing(bob, bobCall, true, false);

      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor2(aliceConversationEvents, bobConversationEvents);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   bob.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   runOriginalRepro(reproRunner);
}

TEST_F(NetworkChangeTest, FrequentNetworkChangesOnStrettoTunnelCall)
{
   ReproHolder::destroyInstance();

   // need repro to force all traffic through it, otherwise Charlie won't be
   // able to e.g. send a BYE reques to Bob
   repro::ReproRunner* reproRunner = runRepro("repro_force_recordroute.config");

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.useRegistrar = false;
   alice.config.settings.tunnelConfig.useTunnel = true;
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.tunnelConfig.tunnelType = TunnelType_StrettoTunnel;
   // repro.config has been adjsuted to WSS listen on port 7061
   alice.config.settings.tunnelConfig.strettoTunnelURL = "wss://127.0.0.1:7061";
   alice.config.settings.ignoreCertVerification = true;
   alice.config.settings.tunnelConfig.strettoTunnelSkipHandshake = true;
   alice.config.settings.sourceAddress = "127.0.0.1";

   alice.config.settings.enableNat64Support = true;
   alice.config.settings.ipVersion = IpVersion_Auto;
   alice.config.settings.useInstanceId = true;
   alice.config.settings.useRport = true;
   alice.config.settings.useRinstance = true;
   alice.config.settings.alwaysRouteViaOutboundProxy = false;
   alice.config.settings.sslVersion = SSL_HIGHEST;
   alice.config.settings.sessionTimerMode = SipAccountSessionTimerMode_Inactive;
   alice.config.settings.tunnelConfig.transportType = TunnelTransport_TCP;

   alice.init();
   alice.enable(false);
   assertAccountRegistering(alice);
   assertAccountRegistered(alice);

   TestAccount bob("bob");

   // Initial network
   ASSERT_EQ(alice.network->networkTransport(), NetworkTransport::TransportWiFi);
   ASSERT_EQ(bob.network->networkTransport(), NetworkTransport::TransportWiFi);

   // Alice calls Bob then Bob hangs up
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   const int callSetupPauseSec = 10;

   auto aliceConversationEvents = std::async(std::launch::async, [&] ()
   {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::seconds(callSetupPauseSec));
      assertMediaFlowing(alice, aliceCall, true, false);

      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> wwan;
      wwan.insert("*******");
      alice.network->setMockInterfaces(wwan);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);

      alice.network->setNetworkTransport(NetworkTransport::TransportWiFi);
      std::set<resip::Data> wifi;
      wifi.insert("*******");
      alice.network->setMockInterfaces(wifi);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);

      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> wwan2;
      wwan2.insert("*******");
      alice.network->setMockInterfaces(wwan2);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);

      alice.network->setNetworkTransport(NetworkTransport::TransportWiFi);
      std::set<resip::Data> wifi2;
      wifi2.insert("*******");
      alice.network->setMockInterfaces(wifi2);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);

      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> wwan3;
      wwan3.insert("*******");
      alice.network->setMockInterfaces(wwan3);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);

      alice.network->setNetworkTransport(NetworkTransport::TransportWiFi);
      std::set<resip::Data> wifi3;
      wifi3.insert("*******");
      alice.network->setMockInterfaces(wifi3);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      alice.conversationEvents->clearCommands();
      alice.accountEvents->clearCommands();
      assertMediaFlowing(alice, aliceCall, true, false);

      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      alice.conversation->end(aliceCall);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobConversationEvents = std::async(std::launch::async, [&] ()
   {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      bob.conversation->sendRingingResponse(bobCall);
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::seconds(callSetupPauseSec));

      assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
      assertSuccess(bob.conversation->accept(bobCall));

      assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
      assertSuccess(bob.conversation->accept(bobCall));

      assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
      assertSuccess(bob.conversation->accept(bobCall));

      assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
      assertSuccess(bob.conversation->accept(bobCall));

      assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
      assertSuccess(bob.conversation->accept(bobCall));

      assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
      assertSuccess(bob.conversation->accept(bobCall));

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(bob, bobCall, true, false);

      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor2(aliceConversationEvents, bobConversationEvents);
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   bob.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   runOriginalRepro(reproRunner);
}

TEST_F(NetworkChangeTest, FrequentNetworkChangesDuringOngoingInviteTransactionsOnStrettoTunnelCall)
{
   ReproHolder::destroyInstance();

   // need repro to force all traffic through it, otherwise Charlie won't be
   // able to e.g. send a BYE reques to Bob
   repro::ReproRunner* reproRunner = runRepro("repro_force_recordroute.config");

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.useRegistrar = false;
   alice.config.settings.tunnelConfig.useTunnel = true;
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.tunnelConfig.tunnelType = TunnelType_StrettoTunnel;
   // repro.config has been adjsuted to WSS listen on port 7061
   alice.config.settings.tunnelConfig.strettoTunnelURL = "wss://127.0.0.1:7061";
   alice.config.settings.ignoreCertVerification = true;
   alice.config.settings.tunnelConfig.strettoTunnelSkipHandshake = true;
   alice.config.settings.sourceAddress = "127.0.0.1";

   alice.config.settings.enableNat64Support = true;
   alice.config.settings.ipVersion = IpVersion_Auto;
   alice.config.settings.useInstanceId = true;
   alice.config.settings.useRport = true;
   alice.config.settings.useRinstance = true;
   alice.config.settings.alwaysRouteViaOutboundProxy = false;
   alice.config.settings.sslVersion = SSL_HIGHEST;
   alice.config.settings.sessionTimerMode = SipAccountSessionTimerMode_Inactive;
   alice.config.settings.tunnelConfig.transportType = TunnelTransport_TCP;

   alice.init();
   alice.enable(false);
   assertAccountRegistering(alice);
   assertAccountRegistered(alice);

   TestAccount bob("bob");

   // Initial network
   ASSERT_EQ(alice.network->networkTransport(), NetworkTransport::TransportWiFi);
   ASSERT_EQ(bob.network->networkTransport(), NetworkTransport::TransportWiFi);

   // Alice calls Bob then Bob hangs up
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);
   std::atomic<bool> accepting = true;
   auto aliceConversationEvents = std::async(std::launch::async, [&] ()
   {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertMediaFlowing(alice, aliceCall, true, false);

      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> wwan;
      wwan.insert("*******");
      alice.network->setMockInterfaces(wwan);

      alice.network->setNetworkTransport(NetworkTransport::TransportWiFi);
      std::set<resip::Data> wifi;
      wifi.insert("*******");
      alice.network->setMockInterfaces(wifi);

      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> wwan2;
      wwan2.insert("*******");
      alice.network->setMockInterfaces(wwan2);

      alice.network->setNetworkTransport(NetworkTransport::TransportWiFi);
      std::set<resip::Data> wifi2;
      wifi2.insert("*******");
      alice.network->setMockInterfaces(wifi2);

      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> wwan3;
      wwan3.insert("*******");
      alice.network->setMockInterfaces(wwan3);

      alice.conversationEvents->clearCommands();
      alice.accountEvents->clearCommands();
      alice.network->setNetworkTransport(NetworkTransport::TransportWiFi);
      std::set<resip::Data> wifi3;
      wifi3.insert("*******");
      alice.network->setMockInterfaces(wifi3);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      alice.conversationEvents->clearCommands();
      alice.accountEvents->clearCommands();
      assertMediaFlowing(alice, aliceCall, true, false);

      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      accepting = false;
      alice.conversation->end(aliceCall);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobConversationEvents = std::async(std::launch::async, [&] ()
   {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      bob.conversation->sendRingingResponse(bobCall);
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      while (accepting)
      {
         {
            SipConversationHandle h;
            ConversationMediaChangeRequestEvent evt;
            bool received = bob.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onConversationMediaChangeRequest", 15000, HandleEqualsPred<SipConversationHandle>(bobCall), h, evt);
            if (received)
            {
               ASSERT_LE((size_t)1, evt.remoteMediaInfo.size());
               for(cpc::vector<MediaInfo>::const_iterator it=evt.remoteMediaInfo.begin(); it != evt.remoteMediaInfo.end(); it++)
               {
                  ASSERT_EQ(MediaDirection_SendReceive, it->mediaDirection);
               }

               assertSuccess(bob.conversation->accept(bobCall));
            }
            else
            {
               ASSERT_FALSE(accepting); // event will not be received if accepting is disabled
            }
         }
      }

      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor2(aliceConversationEvents, bobConversationEvents);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   bob.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   runOriginalRepro(reproRunner);
}

TEST_F(NetworkChangeTest, NetworkChangesWithTcpShutdownDuringOngoingInviteTransactions)
{
   ReproHolder::destroyInstance();

   // need repro to force all traffic through it, otherwise Charlie won't be
   // able to e.g. send a BYE reques to Bob
   repro::ReproRunner* reproRunner = runRepro("repro_force_recordroute.config");

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.useRegistrar = false;
   alice.config.settings.tunnelConfig.useTunnel = true;
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.tunnelConfig.tunnelType = TunnelType_StrettoTunnel;
   // repro.config has been adjsuted to WSS listen on port 7061
   alice.config.settings.tunnelConfig.strettoTunnelURL = "wss://127.0.0.1:7061";
   alice.config.settings.ignoreCertVerification = true;
   alice.config.settings.tunnelConfig.strettoTunnelSkipHandshake = true;
   alice.config.settings.sourceAddress = "127.0.0.1";

   alice.config.settings.enableNat64Support = true;
   alice.config.settings.ipVersion = IpVersion_Auto;
   alice.config.settings.useInstanceId = true;
   alice.config.settings.useRport = true;
   alice.config.settings.useRinstance = true;
   alice.config.settings.alwaysRouteViaOutboundProxy = false;
   alice.config.settings.sslVersion = SSL_HIGHEST;
   alice.config.settings.sessionTimerMode = SipAccountSessionTimerMode_Inactive;
   alice.config.settings.tunnelConfig.transportType = TunnelTransport_TCP;

   alice.init();
   alice.enable(false);
   assertAccountRegistering(alice);
   assertAccountRegistered(alice);

   TestAccount bob("bob");

   // Initial network
   ASSERT_EQ(alice.network->networkTransport(), NetworkTransport::TransportWiFi);
   ASSERT_EQ(bob.network->networkTransport(), NetworkTransport::TransportWiFi);

   NetworkChangePollingConfig config(200, 3000, 3000);
   alice.network->setPollingConfig(config);
   bob.network->setPollingConfig(config);
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   // Alice calls Bob then Bob hangs up
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);
   std::atomic<bool> accepting = true;
   auto aliceConversationEvents = std::async(std::launch::async, [&] ()
   {
      std::set<resip::Data> nonet;
      std::set<resip::Data> wifi;
      wifi.insert("*******");
      std::set<resip::Data> wwan;
      wwan.insert("*******");

      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCall, true, false);

      // Disable network
      if (TestEnvironmentConfig::dockerContainerized())
      {
         ASSERT_EQ(0, test::NetworkUtils::setDockerContainerNetworkEnabled(false));
         ASSERT_EQ(0, test::NetworkUtils::shutdownTcpSockets());
      }
      alice.network->setNetworkTransport(NetworkTransport::TransportNone);
      alice.network->setMockInterfaces(nonet);

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      // Enable WiFi network
      if (TestEnvironmentConfig::dockerContainerized())
      {
         ASSERT_EQ(0, test::NetworkUtils::setDockerContainerNetworkEnabled(true));
      }
      alice.network->setNetworkTransport(NetworkTransport::TransportWiFi);
      alice.network->setMockInterfaces(wifi);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      alice.conversationEvents->clearCommands();
      alice.accountEvents->clearCommands();
      assertMediaFlowing(alice, aliceCall, true, false);

      // Disable network
      if (TestEnvironmentConfig::dockerContainerized())
      {
         ASSERT_EQ(0, test::NetworkUtils::setDockerContainerNetworkEnabled(false));
         ASSERT_EQ(0, test::NetworkUtils::shutdownTcpSockets());
      }
      alice.network->setNetworkTransport(NetworkTransport::TransportNone);
      alice.network->setMockInterfaces(nonet);

      // Enable WWAN network
      if (TestEnvironmentConfig::dockerContainerized())
      {
         ASSERT_EQ(0, test::NetworkUtils::setDockerContainerNetworkEnabled(true));
      }
      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      alice.network->setMockInterfaces(wwan);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      alice.conversationEvents->clearCommands();
      alice.accountEvents->clearCommands();
      assertMediaFlowing(alice, aliceCall, true, false);

      // Disable network
      if (TestEnvironmentConfig::dockerContainerized())
      {
         ASSERT_EQ(0, test::NetworkUtils::setDockerContainerNetworkEnabled(false));
         ASSERT_EQ(0, test::NetworkUtils::shutdownTcpSockets());
      }
      alice.network->setNetworkTransport(NetworkTransport::TransportNone);
      alice.network->setMockInterfaces(nonet);

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      // Enable WiFi network
      if (TestEnvironmentConfig::dockerContainerized())
      {
         ASSERT_EQ(0, test::NetworkUtils::setDockerContainerNetworkEnabled(true));
      }
      alice.network->setNetworkTransport(NetworkTransport::TransportWiFi);
      alice.network->setMockInterfaces(wifi);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      alice.conversationEvents->clearCommands();
      alice.accountEvents->clearCommands();
      assertMediaFlowing(alice, aliceCall, true, false);

      // Switch to WWAN network
      if (TestEnvironmentConfig::dockerContainerized())
      {
         ASSERT_EQ(0, test::NetworkUtils::setDockerContainerNetworkEnabled(false));
         ASSERT_EQ(0, test::NetworkUtils::shutdownTcpSockets());
      }
      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      alice.network->setMockInterfaces(wwan);
      if (TestEnvironmentConfig::dockerContainerized())
      {
         ASSERT_EQ(0, test::NetworkUtils::setDockerContainerNetworkEnabled(true));
      }
      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      alice.network->setMockInterfaces(wwan);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      alice.conversationEvents->clearCommands();
      alice.accountEvents->clearCommands();
      assertMediaFlowing(alice, aliceCall, true, false);

      // Switch to WiFi network
      if (TestEnvironmentConfig::dockerContainerized())
      {
         ASSERT_EQ(0, test::NetworkUtils::setDockerContainerNetworkEnabled(false));
         ASSERT_EQ(0, test::NetworkUtils::shutdownTcpSockets());
      }
      alice.network->setNetworkTransport(NetworkTransport::TransportWiFi);
      alice.network->setMockInterfaces(wifi);
      if (TestEnvironmentConfig::dockerContainerized())
      {
         ASSERT_EQ(0, test::NetworkUtils::setDockerContainerNetworkEnabled(true));
      }
      alice.network->setNetworkTransport(NetworkTransport::TransportWiFi);
      alice.network->setMockInterfaces(wifi);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      alice.conversationEvents->clearCommands();
      alice.accountEvents->clearCommands();
      assertMediaFlowing(alice, aliceCall, true, false);

      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      accepting = false;
      alice.conversation->end(aliceCall);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobConversationEvents = std::async(std::launch::async, [&] ()
   {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      bob.conversation->sendRingingResponse(bobCall);
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      while (accepting)
      {
         {
            SipConversationHandle h;
            ConversationMediaChangeRequestEvent evt;
            bool received = bob.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onConversationMediaChangeRequest", 25000, HandleEqualsPred<SipConversationHandle>(bobCall), h, evt);
            if (received)
            {
               ASSERT_LE((size_t)1, evt.remoteMediaInfo.size());
               for(cpc::vector<MediaInfo>::const_iterator it=evt.remoteMediaInfo.begin(); it != evt.remoteMediaInfo.end(); it++)
               {
                  ASSERT_EQ(MediaDirection_SendReceive, it->mediaDirection);
               }

               assertSuccess(bob.conversation->accept(bobCall));
            }
            else
            {
               ASSERT_FALSE(accepting); // event will not be received if accepting is disabled
            }
         }
      }

      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor2Ms(aliceConversationEvents, bobConversationEvents, std::chrono::minutes(120000));

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   bob.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   runOriginalRepro(reproRunner);
   std::this_thread::sleep_for(std::chrono::milliseconds(3000));
}

TEST_F(NetworkChangeTest, DISABLED_NetworkChangesWithReproShutdownDuringOngoingInviteTransactions)
{
   ReproHolder::destroyInstance();

   // need repro to force all traffic through it, otherwise Charlie won't be
   // able to e.g. send a BYE reques to Bob
   repro::ReproRunner* reproRunner = runRepro("repro_force_recordroute.config");

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.useRegistrar = false;
   alice.config.settings.tunnelConfig.useTunnel = true;
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.tunnelConfig.tunnelType = TunnelType_StrettoTunnel;
   // repro.config has been adjsuted to WSS listen on port 7061
   alice.config.settings.tunnelConfig.strettoTunnelURL = "wss://127.0.0.1:7061";
   alice.config.settings.ignoreCertVerification = true;
   alice.config.settings.tunnelConfig.strettoTunnelSkipHandshake = true;
   alice.init();
   alice.enable(false);
   assertAccountRegistering(alice);
   assertAccountRegistered(alice);

   TestAccount bob("bob");

   // Initial network
   ASSERT_EQ(alice.network->networkTransport(), NetworkTransport::TransportWiFi);
   ASSERT_EQ(bob.network->networkTransport(), NetworkTransport::TransportWiFi);

   // Alice calls Bob then Bob hangs up
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);
   std::atomic<bool> accepting = true;
   std::atomic<bool> active = true;
   std::promise<bool> proxyOnFuture;
   auto aliceConversationEvents = std::async(std::launch::async, [&] ()
   {
      std::set<resip::Data> nonet;
      std::set<resip::Data> wifi;
      wifi.insert("*******");
      std::set<resip::Data> wwan;
      wwan.insert("*******");

      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCall, true, false);

      // Disable proxy
      try
      {
         proxyOnFuture.set_value(false);
      }
      catch (...)
      {
         ASSERT_TRUE(false);
      }
      alice.network->setNetworkTransport(NetworkTransport::TransportNone);
      alice.network->setMockInterfaces(nonet);

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      // Enable proxy (after network change initiated)
      alice.network->setNetworkTransport(NetworkTransport::TransportWiFi);
      alice.network->setMockInterfaces(wifi);
      std::this_thread::sleep_for(std::chrono::milliseconds(500));
      try
      {
         proxyOnFuture.set_value(true);
      }
      catch (...)
      {
         ASSERT_TRUE(false);
      }
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      alice.conversationEvents->clearCommands();
      alice.accountEvents->clearCommands();
      assertMediaFlowing(alice, aliceCall, true, false);

      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      accepting = false;
      alice.conversation->end(aliceCall);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
      active = false;
   });

   auto bobConversationEvents = std::async(std::launch::async, [&] ()
   {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      bob.conversation->sendRingingResponse(bobCall);
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      while (accepting)
      {
         {
            SipConversationHandle h;
            ConversationMediaChangeRequestEvent evt;
            bool received = bob.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onConversationMediaChangeRequest", 30000, HandleEqualsPred<SipConversationHandle>(bobCall), h, evt);
            if (received)
            {
               ASSERT_LE((size_t)1, evt.remoteMediaInfo.size());
               for(cpc::vector<MediaInfo>::const_iterator it=evt.remoteMediaInfo.begin(); it != evt.remoteMediaInfo.end(); it++)
               {
                  ASSERT_EQ(MediaDirection_SendReceive, it->mediaDirection);
               }

               assertSuccess(bob.conversation->accept(bobCall));
            }
            else
            {
               ASSERT_FALSE(accepting); // event will not be received if accepting is disabled
            }
         }
      }

      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   bool wasEnabled = true;
   while (active)
   {
      auto hF = proxyOnFuture.get_future();
      bool isOn = true;
      try
      {
         isOn = hF.get();
      }
      catch (const std::future_error& e)
      {
         ASSERT_TRUE(false);
      }

      proxyOnFuture = std::promise<bool>(); // reset for next use
      if (isOn && !wasEnabled)
      {
         safeCout("NetworkChangeTest::NetworkChangesWithReproShutdownDuringOngoingInviteTransactions(): Restart proxy");
         wasEnabled = true;
         ASSERT_TRUE(reproRunner == NULL);
         reproRunner = runRepro("repro_force_recordroute.config");
      }
      else if (!isOn && wasEnabled)
      {
         safeCout("NetworkChangeTest::NetworkChangesWithReproShutdownDuringOngoingInviteTransactions(): Shutdown proxy");
         wasEnabled = false;
         ASSERT_FALSE(reproRunner == NULL);
         reproRunner->shutdown();
         delete reproRunner; reproRunner = NULL;
      }
   }
   waitFor2(aliceConversationEvents, bobConversationEvents);

   runOriginalRepro();
   std::this_thread::sleep_for(std::chrono::milliseconds(3000));
}

class MyNetworkChangePollingHandler : public CPCAPI2::NetworkChangePollingHandler,
                                      public CPCAPI2::NetworkChangePollingHandlerInternal
{
public:
   std::atomic<bool> mWaitingForQuery;
   std::atomic<bool> mWaitingForPublish;
   std::atomic<bool> mWaitingForStatus;
   std::atomic<bool> mWaitingForReset;
   std::promise<bool> mWaitForQueryFuture;
   std::promise<bool> mWaitForPublishFuture;
   std::promise<bool> mWaitForStatusFuture;
   std::promise<bool> mWaitForStatusReceivedFuture;
   std::promise<bool> mWaitForResetFuture;
   std::promise<bool> mWaitForResetReceivedFuture;
   NetworkTransport mTransport;
   std::set<std::string> mInterfaces;
   NetworkChangePollingStateType mStatus;
   TestAccount& mAccount;

   MyNetworkChangePollingHandler(TestAccount& account) : mAccount(account), mWaitingForStatus(false), mWaitingForReset(false)
   {
      mAccount.network->addPollingHandler(this);
   }

   virtual~ MyNetworkChangePollingHandler()
   {
      mAccount.network->removePollingHandler(this);
   }

   virtual void onNetworkInterfaceQuery(NetworkInterfaceQueryEvent& evt) OVERRIDE
   {
      bool waitForNow = true;
      while (waitForNow)
      {
         if (mWaitingForQuery)
         {
            waitForNow = false;
            mWaitingForQuery = false;
            evt.transport = mTransport;
            evt.interfaces = mInterfaces;
            mWaitForQueryFuture.set_value(true);
         }
         else
         {
            std::this_thread::sleep_for(std::chrono::milliseconds(200));
         }
      }
   }

   virtual void onPublishNetworkChange(const NetworkChangePublishEvent& evt) OVERRIDE
   {
      bool waitForNow = true;
      while (waitForNow)
      {
         if (mWaitingForPublish)
         {
            waitForNow = false;
            mWaitingForPublish = false;
            mTransport = evt.transport;
            mInterfaces = evt.interfaces;
            mWaitForPublishFuture.set_value(true);
         }
         else
         {
            std::this_thread::sleep_for(std::chrono::milliseconds(200));
         }
      }
   }

   virtual void onPollingStatusUpdate(const NetworkChangePollingStatusEvent& evt) OVERRIDE
   {
      safeCout("MyNetworkChangePollingHandler::onPollingStatusUpdate(): " << evt);
      try
      {
         bool waitForNow = true;
         while (waitForNow)
         {
            if (mWaitingForStatus)
            {
               waitForNow = false;
               mWaitingForStatus = false;
               mTransport = evt.transport;
               mStatus = evt.state;
               mWaitForStatusFuture.set_value(true);
            }
            else
            {
               std::this_thread::sleep_for(std::chrono::milliseconds(200));
            }
         }
      }
      catch (const std::future_error& e)
      {
         ASSERT_TRUE(false) << "Future exception code: " << e.code() << " description: \"" << e.what() << "\" upon receipt of onPollingStatusUpdate callback";
      }
   }

   virtual void onNotificationReset(const NetworkChangePollingNotificationResetEvent& evt) OVERRIDE
   {
      bool waitForNow = true;
      while (waitForNow)
      {
         if (mWaitingForReset)
         {
            waitForNow = false;
            mWaitingForReset = false;
            mWaitForResetFuture.set_value(true);
         }
         else
         {
            std::this_thread::sleep_for(std::chrono::milliseconds(200));
         }
      }
   }

private:
   MyNetworkChangePollingHandler();
};

void NetworkChangeTest::expectNetworkInterfaceQueryEvent(int line, MyNetworkChangePollingHandler* pollingHandler, NetworkTransport transport, std::set<std::string>& interfaces, int waitMsecs)
{
   auto hF = pollingHandler->mWaitForQueryFuture.get_future();
   pollingHandler->mWaitingForQuery = true;
   if (hF.wait_for(std::chrono::milliseconds(waitMsecs)) == std::future_status::ready)
   {
      hF.get();
      
      ASSERT_EQ(pollingHandler->mTransport, transport);
      ASSERT_EQ(pollingHandler->mInterfaces, interfaces);
   }
   else
   {
      // Missing query should not assert
      // ASSERT_TRUE(false) << "Timeout waiting for network interface query event";
   }
   pollingHandler->mWaitForQueryFuture = std::promise<bool>(); // reset for next use
}

#define assertNetworkInterfaceQueryEvent(pollingHandler, transport, interfaces, queryEvt) \
{ \
   SCOPED_TRACE(pollingHandler->mAccount.config.name); \
   NetworkChangeTest::expectNetworkInterfaceQueryEvent(__LINE__, pollingHandler, transport, intrefaces, queryEvt); \
}

#define assertNetworkInterfaceQueryEventMsecs(pollingHandler, transport, interfaces, waitMsecs) \
{ \
   SCOPED_TRACE(pollingHandler->mAccount.config.name); \
   NetworkChangeTest::expectNetworkInterfaceQueryEvent(__LINE__, pollingHandler, transport, interfaces, waitMsecs); \
}

void NetworkChangeTest::expectNetworkChangePublishEvent(int line, MyNetworkChangePollingHandler* pollingHandler, NetworkTransport transport, std::set<std::string>& interfaces, int waitMsecs)
{
   auto hF = pollingHandler->mWaitForPublishFuture.get_future();
   pollingHandler->mWaitingForPublish = true;
   if (hF.wait_for(std::chrono::milliseconds(waitMsecs)) == std::future_status::ready)
   {
      hF.get();
   }
   else
   {
      ASSERT_TRUE(false) << "Timeout waiting for notification reset event";
   }
   pollingHandler->mWaitForPublishFuture = std::promise<bool>(); // reset for next use

   ASSERT_EQ(pollingHandler->mTransport, transport);
   ASSERT_EQ(pollingHandler->mInterfaces, interfaces);
}

#define assertNetworkChangePublishEvent(pollingHandler, transport, interfaces) \
{ \
   SCOPED_TRACE(pollingHandler->mAccount.config.name); \
   NetworkChangeTest::expectNetworkChangePublishEvent(__LINE__, pollingHandler, transport, interfaces); \
}

#define assertPollingNotificationResetEventMsecs(pollingHandler, transport, interfaces, waitMsecs) \
{ \
   SCOPED_TRACE(pollingHandler->mAccount.config.name); \
   NetworkChangeTest::expectNetworkChangePublishEvent(__LINE__, pollingHandler, transport, interfaces, waitMsecs); \
}

void NetworkChangeTest::expectPollingStatusEvent(int line, MyNetworkChangePollingHandler* pollingHandler, NetworkTransport transport, NetworkChangePollingStateType status, int waitMsecs)
{
   try
   {
      auto hF = pollingHandler->mWaitForStatusFuture.get_future();
      pollingHandler->mWaitingForStatus = true;
      if (hF.wait_for(std::chrono::milliseconds(waitMsecs)) == std::future_status::ready)
      {
         hF.get();
      }
      else
      {
         ASSERT_TRUE(false) << "Timeout waiting for polling status event";
      }
      pollingHandler->mWaitForStatusFuture = std::promise<bool>(); // reset for next use
   }
   catch (const std::future_error& e)
   {
      ASSERT_TRUE(false) << "Future exception code: " << e.code() << " description: \"" << e.what() << "\" when waiting for polling status";
   }

   ASSERT_EQ(pollingHandler->mTransport, transport);
   ASSERT_EQ(pollingHandler->mStatus, status);
}

#define assertPollingStatusEvent(pollingHandler, transport, status) \
{ \
   SCOPED_TRACE(pollingHandler->mAccount.config.name); \
   NetworkChangeTest::expectPollingStatusEvent(__LINE__, pollingHandler, transport, status); \
}

#define assertPollingStatusEventMsecs(pollingHandler, transport, status, waitMsecs) \
{ \
   SCOPED_TRACE(pollingHandler->mAccount.config.name); \
   NetworkChangeTest::expectPollingStatusEvent(__LINE__, pollingHandler, transport, status, waitMsecs); \
}

bool NetworkChangeTest::expectPollingStatusEventReceived(int line, MyNetworkChangePollingHandler* pollingHandler, int waitMsecs)
{
   bool received = false;
   auto hF = pollingHandler->mWaitForStatusReceivedFuture.get_future();
   pollingHandler->mWaitingForStatus = true;
   if (hF.wait_for(std::chrono::milliseconds(waitMsecs)) == std::future_status::ready)
   {
      received = hF.get();
   }
   pollingHandler->mWaitForStatusReceivedFuture = std::promise<bool>(); // reset for next use
   return received;
}

#define assertPollingStatusEventReceived(pollingHandler) \
{ \
   SCOPED_TRACE(pollingHandler->mAccount.config.name); \
   NetworkChangeTest::expectPollingStatusEventReceived(__LINE__, pollingHandler); \
}

#define assertPollingStatusEventReceivedMsecs(pollingHandler, waitMsecs) \
{ \
   SCOPED_TRACE(pollingHandler->mAccount.config.name); \
   NetworkChangeTest::expectPollingStatusEventReceived(__LINE__, pollingHandler, waitMsecs); \
}

void NetworkChangeTest::expectPollingNotificationResetEvent(int line, MyNetworkChangePollingHandler* pollingHandler, int waitMsecs)
{
   auto hF = pollingHandler->mWaitForResetFuture.get_future();
   pollingHandler->mWaitingForReset = true;
   if (hF.wait_for(std::chrono::milliseconds(waitMsecs)) == std::future_status::ready)
   {
      hF.get();
   }
   else
   {
      ASSERT_TRUE(false) << "Timeout waiting for notification reset event";
   }
   pollingHandler->mWaitForResetFuture = std::promise<bool>(); // reset for next use
}

#define assertPollingNotificationResetEvent(pollingHandler) \
{ \
   SCOPED_TRACE(pollingHandler->mAccount.config.name); \
   NetworkChangeTest::expectPollingNotificationResetEvent(__LINE__, pollingHandler); \
}

bool NetworkChangeTest::expectPollingNotificationResetEventReceived(int line, MyNetworkChangePollingHandler* pollingHandler, int waitMsecs)
{
   bool received = false;
   auto hF = pollingHandler->mWaitForResetReceivedFuture.get_future();
   pollingHandler->mWaitingForReset = true;
   if (hF.wait_for(std::chrono::milliseconds(waitMsecs)) == std::future_status::ready)
   {
      received = hF.get();
   }
   pollingHandler->mWaitForResetReceivedFuture = std::promise<bool>(); // reset for next use
   return received;
}

#define assertPollingNotificationResetEventReceived(pollingHandler) \
{ \
   SCOPED_TRACE(pollingHandler->mAccount.config.name); \
   NetworkChangeTest::expectPollingNotificationResetEventReceived(__LINE__, pollingHandler); \
}

#define assertPollingNotificationResetEventReceivedMsecs(pollingHandler, waitMsecs) \
{ \
   SCOPED_TRACE(pollingHandler->mAccount.config.name); \
   NetworkChangeTest::expectPollingNotificationResetEventReceived(__LINE__, pollingHandler, waitMsecs); \
}

TEST_F(NetworkChangeTest, PollingValidateInitialNetworkTransport)
{
   TestAccount alice("alice");
   ASSERT_EQ(alice.network->networkTransport(), NetworkTransport::TransportWiFi);

   TestAccount bob("bob", Account_NoInit);
   bob.init(NULL, NetworkTransport::TransportNone);
   ASSERT_EQ(bob.network->networkTransport(), NetworkTransport::TransportNone);

   TestAccount max("max", Account_NoInit);
   max.init(NULL, NetworkTransport::TransportWWAN);
   ASSERT_EQ(max.network->networkTransport(), NetworkTransport::TransportWWAN);
}

TEST_F(NetworkChangeTest, PollingValidateStatusUpdateWiFiToCell)
{
   TestAccount alice("alice");
   ASSERT_EQ(alice.network->networkTransport(), NetworkTransport::TransportWiFi);

   std::atomic<bool> started = false;

   auto pollingHandlerEvents = std::async(std::launch::async, [&] ()
   {
      std::unique_ptr<MyNetworkChangePollingHandler> pollingHandler(new MyNetworkChangePollingHandler(alice));
      started = true;

      assertPollingStatusEvent(pollingHandler.get(), NetworkTransport::TransportWWAN, NetworkChangePollingStateType::NetworkChangePollingStateType_Notify);
      assertPollingStatusEvent(pollingHandler.get(), NetworkTransport::TransportWWAN, NetworkChangePollingStateType::NetworkChangePollingStateType_Stabilize);
      assertPollingStatusEvent(pollingHandler.get(), NetworkTransport::TransportWWAN, NetworkChangePollingStateType::NetworkChangePollingStateType_Sync);
   });

   bool waitForStart = true;
   while (waitForStart)
   {
      std::this_thread::sleep_for(std::chrono::milliseconds(200));
      if (started)
      {
         waitForStart = false;
         alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      }
   }

   assertAccountRefreshing(alice);
   assertAccountRegistered(alice);

   waitForMs(pollingHandlerEvents, std::chrono::milliseconds(30000));
}

TEST_F(NetworkChangeTest, PollingValidateStatusUpdateCellToWiFi)
{
   TestAccount alice("alice", Account_NoInit);
   alice.init(NULL, NetworkTransport::TransportWWAN);
   alice.enable();
   ASSERT_EQ(alice.network->networkTransport(), NetworkTransport::TransportWWAN);

   std::atomic<bool> started = false;

   auto pollingHandlerEvents = std::async(std::launch::async, [&] ()
   {
      std::unique_ptr<MyNetworkChangePollingHandler> pollingHandler(new MyNetworkChangePollingHandler(alice));
      started = true;

      assertPollingStatusEvent(pollingHandler.get(), NetworkTransport::TransportWiFi, NetworkChangePollingStateType::NetworkChangePollingStateType_Notify);
      assertPollingStatusEvent(pollingHandler.get(), NetworkTransport::TransportWiFi, NetworkChangePollingStateType::NetworkChangePollingStateType_Stabilize);
      assertPollingStatusEvent(pollingHandler.get(), NetworkTransport::TransportWiFi, NetworkChangePollingStateType::NetworkChangePollingStateType_Sync);
   });

   bool waitForStart = true;
   while (waitForStart)
   {
      std::this_thread::sleep_for(std::chrono::milliseconds(200));
      if (started)
      {
         waitForStart = false;
         alice.network->setNetworkTransport(NetworkTransport::TransportWiFi);
      }
   }

   assertAccountRefreshing(alice);
   assertAccountRegistered(alice);

   waitForMs(pollingHandlerEvents, std::chrono::milliseconds(30000));
}

TEST_F(NetworkChangeTest, PollingValidateStatusUpdateNoneToWiFi)
{
   TestAccount alice("alice", Account_NoInit);
   alice.init(NULL, NetworkTransport::TransportNone);
   alice.enable(false);
   ASSERT_EQ(alice.network->networkTransport(), NetworkTransport::TransportNone);

   std::atomic<bool> started = false;

   auto pollingHandlerEvents = std::async(std::launch::async, [&] ()
   {
      std::unique_ptr<MyNetworkChangePollingHandler> pollingHandler(new MyNetworkChangePollingHandler(alice));
      started = true;

      assertPollingStatusEvent(pollingHandler.get(), NetworkTransport::TransportWiFi, NetworkChangePollingStateType::NetworkChangePollingStateType_Notify);
      assertPollingStatusEvent(pollingHandler.get(), NetworkTransport::TransportWiFi, NetworkChangePollingStateType::NetworkChangePollingStateType_Stabilize);
      assertPollingStatusEvent(pollingHandler.get(), NetworkTransport::TransportWiFi, NetworkChangePollingStateType::NetworkChangePollingStateType_Sync);
   });

   bool waitForStart = true;
   while (waitForStart)
   {
      std::this_thread::sleep_for(std::chrono::milliseconds(200));
      if (started)
      {
         waitForStart = false;
         alice.network->setNetworkTransport(NetworkTransport::TransportWiFi);
      }
   }

   assertAccountRegistering(alice);
   assertAccountRegistered(alice);

   waitForMs(pollingHandlerEvents, std::chrono::milliseconds(30000));
}

TEST_F(NetworkChangeTest, PollingValidateStatusUpdateInterfaceUpdate)
{
   TestAccount alice("alice");
   ASSERT_EQ(alice.network->networkTransport(), NetworkTransport::TransportWiFi);

   std::atomic<bool> started = false;

   auto pollingHandlerEvents = std::async(std::launch::async, [&] ()
   {
      std::unique_ptr<MyNetworkChangePollingHandler> pollingHandler(new MyNetworkChangePollingHandler(alice));
      started = true;

      assertPollingStatusEvent(pollingHandler.get(), NetworkTransport::TransportWiFi, NetworkChangePollingStateType::NetworkChangePollingStateType_Notify);
      assertPollingStatusEvent(pollingHandler.get(), NetworkTransport::TransportWiFi, NetworkChangePollingStateType::NetworkChangePollingStateType_Stabilize);
      assertPollingStatusEvent(pollingHandler.get(), NetworkTransport::TransportWiFi, NetworkChangePollingStateType::NetworkChangePollingStateType_Sync);
   });

   bool waitForStart = true;
   while (waitForStart)
   {
      std::this_thread::sleep_for(std::chrono::milliseconds(200));
      if (started)
      {
         waitForStart = false;
         std::set<resip::Data> interfaces;
         interfaces.insert("*******");
         alice.network->setMockInterfaces(interfaces);
      }
   }

   assertAccountRefreshing(alice);
   assertAccountRegistered(alice);

   waitForMs(pollingHandlerEvents, std::chrono::milliseconds(30000));
}


TEST_F(NetworkChangeTest, DISABLED_PollingValidateNotificationReset)
{
   TestAccount alice("alice");
   ASSERT_EQ(alice.network->networkTransport(), NetworkTransport::TransportWiFi);

   std::atomic<bool> started = false;

   auto pollingHandlerEvents = std::async(std::launch::async, [&] ()
   {
      std::unique_ptr<MyNetworkChangePollingHandler> pollingHandler(new MyNetworkChangePollingHandler(alice));
      started = true;

      assertPollingStatusEvent(pollingHandler.get(), NetworkTransport::TransportWWAN, NetworkChangePollingStateType::NetworkChangePollingStateType_Notify);
      assertPollingNotificationResetEvent(pollingHandler.get());
      assertPollingNotificationResetEvent(pollingHandler.get());
      assertPollingNotificationResetEvent(pollingHandler.get());
      assertPollingStatusEvent(pollingHandler.get(), NetworkTransport::TransportWiFi, NetworkChangePollingStateType::NetworkChangePollingStateType_Stabilize);
      assertPollingStatusEvent(pollingHandler.get(), NetworkTransport::TransportWiFi, NetworkChangePollingStateType::NetworkChangePollingStateType_Sync);
   });

   bool waitForStart = true;
   while (waitForStart)
   {
      std::this_thread::sleep_for(std::chrono::milliseconds(200));
      if (started)
      {
         waitForStart = false;
         alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
         std::this_thread::sleep_for(std::chrono::milliseconds(200));
         alice.network->setNetworkTransport(NetworkTransport::TransportWiFi);
         std::this_thread::sleep_for(std::chrono::milliseconds(200));
         alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
         std::this_thread::sleep_for(std::chrono::milliseconds(200));
         alice.network->setNetworkTransport(NetworkTransport::TransportWiFi);
      }
   }

   assertAccountRefreshing(alice);
   assertAccountRegistered(alice);

   waitForMs(pollingHandlerEvents, std::chrono::milliseconds(30000));
}

TEST_F(NetworkChangeTest, PollingValidateStabilizationReset)
{
   TestAccount alice("alice");
   ASSERT_EQ(alice.network->networkTransport(), NetworkTransport::TransportWiFi);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   NetworkChangePollingConfig config(200, 3000, 3000);
   alice.network->setPollingConfig(config);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   std::atomic<bool> started = false;
   std::atomic<bool> stabilized = false;

   auto pollingHandlerEvents = std::async(std::launch::async, [&] ()
   {
      std::unique_ptr<MyNetworkChangePollingHandler> pollingHandler(new MyNetworkChangePollingHandler(alice));
      started = true;

      assertPollingStatusEvent(pollingHandler.get(), NetworkTransport::TransportWWAN, NetworkChangePollingStateType::NetworkChangePollingStateType_Notify);
      assertPollingNotificationResetEvent(pollingHandler.get());
      assertPollingStatusEvent(pollingHandler.get(), NetworkTransport::TransportWiFi, NetworkChangePollingStateType::NetworkChangePollingStateType_Stabilize);
      stabilized = true;

      assertPollingNotificationResetEvent(pollingHandler.get());
      assertPollingStatusEvent(pollingHandler.get(), NetworkTransport::TransportWWAN, NetworkChangePollingStateType::NetworkChangePollingStateType_Notify);
      assertPollingNotificationResetEvent(pollingHandler.get());
      assertPollingStatusEvent(pollingHandler.get(), NetworkTransport::TransportWiFi, NetworkChangePollingStateType::NetworkChangePollingStateType_Stabilize);
      assertPollingStatusEvent(pollingHandler.get(), NetworkTransport::TransportWiFi, NetworkChangePollingStateType::NetworkChangePollingStateType_Sync);
   });

   auto networkChangeTriggerEvents = std::async(std::launch::async, [&] ()
   {
      while (!started)
      {
         std::this_thread::sleep_for(std::chrono::milliseconds(500));
         break;
      }
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      std::set<resip::Data> wwanIfaces;
      wwanIfaces.insert(resip::Data("************"));
      std::set<resip::Data> wifiIfaces;
      wifiIfaces.insert(resip::Data("*********"));

      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      alice.network->setMockInterfaces(wwanIfaces);
      
      std::this_thread::sleep_for(std::chrono::milliseconds(200));
      alice.network->setNetworkTransport(NetworkTransport::TransportWiFi);
      alice.network->setMockInterfaces(wifiIfaces);
      std::this_thread::sleep_for(std::chrono::milliseconds(200));

      while (!stabilized)
      {
         std::this_thread::sleep_for(std::chrono::milliseconds(200));
      }

      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);

      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      alice.network->setMockInterfaces(wwanIfaces);
      std::this_thread::sleep_for(std::chrono::milliseconds(200));
      alice.network->setNetworkTransport(NetworkTransport::TransportWiFi);
      alice.network->setMockInterfaces(wifiIfaces);

      // currently we would *not* expect any network change event to be propagated here; see OBELISK-6371 for details
      // we may want to reconsider not propagating a network change event here
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
      ASSERT_FALSE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged",
         __FILE__, 10000, CPCAPI2::test::AlwaysTruePred(), h, evt));

      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      alice.network->setMockInterfaces(wwanIfaces);

      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);
   });

   waitFor2Ms(pollingHandlerEvents, networkChangeTriggerEvents, std::chrono::milliseconds(60000));
}

TEST_F(NetworkChangeTest, PollingValidateStabilizationIgnoreInterfaceChange)
{
   TestAccount alice("alice");
   ASSERT_EQ(alice.network->networkTransport(), NetworkTransport::TransportWiFi);

   NetworkChangePollingConfig config(200, 2000, 2000);
   alice.network->setPollingConfig(config);

   std::atomic<bool> started = false;
   std::atomic<bool> stabilized = false;

   auto pollingHandlerEvents = std::async(std::launch::async, [&] ()
   {
      std::unique_ptr<MyNetworkChangePollingHandler> pollingHandler(new MyNetworkChangePollingHandler(alice));
      started = true;

      assertPollingStatusEvent(pollingHandler.get(), NetworkTransport::TransportWWAN, NetworkChangePollingStateType::NetworkChangePollingStateType_Notify);
      assertPollingStatusEvent(pollingHandler.get(), NetworkTransport::TransportWWAN, NetworkChangePollingStateType::NetworkChangePollingStateType_Stabilize);
      stabilized = true;
      assertPollingNotificationResetEventReceivedMsecs(pollingHandler.get(), 3000);
      assertPollingStatusEvent(pollingHandler.get(), NetworkTransport::TransportWWAN, NetworkChangePollingStateType::NetworkChangePollingStateType_Sync);
   });

   bool waitForStart = true;
   while (waitForStart)
   {
      std::this_thread::sleep_for(std::chrono::milliseconds(200));
      if (started)
      {
         waitForStart = false;
         alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
         std::this_thread::sleep_for(std::chrono::milliseconds(200));
         while (!stabilized)
         {
            std::this_thread::sleep_for(std::chrono::milliseconds(200));
         }
         std::set<resip::Data> interfaces;
         interfaces.insert("*******");
         alice.network->setMockInterfaces(interfaces);
         std::this_thread::sleep_for(std::chrono::milliseconds(500));
      }
   }

   assertAccountRefreshing(alice);
   assertAccountRegistered(alice);

   waitForMs(pollingHandlerEvents, std::chrono::milliseconds(30000));
}

TEST_F(NetworkChangeTest, PollingValidateNotificationIgnoreInterfaceChange)
{
   TestAccount alice("alice");
   ASSERT_EQ(alice.network->networkTransport(), NetworkTransport::TransportWiFi);

   NetworkChangePollingConfig config(200, 3000, 3000);
   alice.network->setPollingConfig(config);

   std::atomic<bool> started = false;
   std::atomic<bool> notified = false;

   auto pollingHandlerEvents = std::async(std::launch::async, [&] ()
   {
      std::unique_ptr<MyNetworkChangePollingHandler> pollingHandler(new MyNetworkChangePollingHandler(alice));
      started = true;

      assertPollingStatusEvent(pollingHandler.get(), NetworkTransport::TransportWWAN, NetworkChangePollingStateType::NetworkChangePollingStateType_Notify);
      notified = true;
      assertPollingNotificationResetEventReceivedMsecs(pollingHandler.get(), 3000);
      assertPollingStatusEvent(pollingHandler.get(), NetworkTransport::TransportWWAN, NetworkChangePollingStateType::NetworkChangePollingStateType_Stabilize);
      assertPollingStatusEvent(pollingHandler.get(), NetworkTransport::TransportWWAN, NetworkChangePollingStateType::NetworkChangePollingStateType_Sync);
   });

   bool waitForStart = true;
   while (waitForStart)
   {
      std::this_thread::sleep_for(std::chrono::milliseconds(200));
      if (started)
      {
         waitForStart = false;
         alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
         std::this_thread::sleep_for(std::chrono::milliseconds(200));
         while (!notified)
         {
            std::this_thread::sleep_for(std::chrono::milliseconds(200));
         }
         std::set<resip::Data> interfaces;
         interfaces.insert("*******");
         alice.network->setMockInterfaces(interfaces);
         std::this_thread::sleep_for(std::chrono::milliseconds(500));
      }
   }

   assertAccountRefreshing(alice);
   assertAccountRegistered(alice);

   waitForMs(pollingHandlerEvents, std::chrono::milliseconds(30000));
}

TEST_F(NetworkChangeTest, PollingSetPollingConfiguration)
{
   TestAccount alice("alice");
   ASSERT_EQ(alice.network->networkTransport(), NetworkTransport::TransportWiFi);

   NetworkChangePollingConfig config(200, 10000, 10000);
   alice.network->setPollingConfig(config);

   std::atomic<bool> started = false;
   std::atomic<bool> synced = false;

   auto pollingHandlerEvents = std::async(std::launch::async, [&] ()
   {
      std::unique_ptr<MyNetworkChangePollingHandler> pollingHandler(new MyNetworkChangePollingHandler(alice));
      started = true;

      assertPollingStatusEventMsecs(pollingHandler.get(), NetworkTransport::TransportWWAN, NetworkChangePollingStateType::NetworkChangePollingStateType_Notify, 2000);
      assertPollingStatusEventReceivedMsecs(pollingHandler.get(), 3000);
      assertPollingStatusEventMsecs(pollingHandler.get(), NetworkTransport::TransportWWAN, NetworkChangePollingStateType::NetworkChangePollingStateType_Stabilize, 8000);
      assertPollingStatusEventReceivedMsecs(pollingHandler.get(), 3000);
      assertPollingStatusEventMsecs(pollingHandler.get(), NetworkTransport::TransportWWAN, NetworkChangePollingStateType::NetworkChangePollingStateType_Sync, 8000);
      synced = true;

      assertPollingStatusEventMsecs(pollingHandler.get(), NetworkTransport::TransportWiFi, NetworkChangePollingStateType::NetworkChangePollingStateType_Notify, 11000);
      assertPollingStatusEventReceivedMsecs(pollingHandler.get(), 10000);
      assertPollingStatusEventMsecs(pollingHandler.get(), NetworkTransport::TransportWiFi, NetworkChangePollingStateType::NetworkChangePollingStateType_Stabilize, 11000);
      assertPollingStatusEventReceivedMsecs(pollingHandler.get(), 10000);
      assertPollingStatusEventMsecs(pollingHandler.get(), NetworkTransport::TransportWiFi, NetworkChangePollingStateType::NetworkChangePollingStateType_Sync, 11000);
      synced = true;
   });

   bool waitForStart = true;
   while (waitForStart)
   {
      std::this_thread::sleep_for(std::chrono::milliseconds(200));
      if (started)
      {
         waitForStart = false;
         alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
         std::this_thread::sleep_for(std::chrono::milliseconds(200));
         while (!synced)
         {
            std::this_thread::sleep_for(std::chrono::milliseconds(200));
         }
         synced = false;
         std::this_thread::sleep_for(std::chrono::milliseconds(500));

         NetworkChangePollingConfig config2(1000, 20000, 20000);
         alice.network->setPollingConfig(config2);

         alice.network->setNetworkTransport(NetworkTransport::TransportWiFi);
         std::this_thread::sleep_for(std::chrono::milliseconds(1000));
         while (!synced)
         {
            std::this_thread::sleep_for(std::chrono::milliseconds(1000));
         }
         synced = false;
         std::this_thread::sleep_for(std::chrono::milliseconds(500));
      }
   }

   assertAccountRefreshing(alice);
   assertAccountRegistered(alice);
   assertAccountRefreshing(alice);
   assertAccountRegistered(alice);

   waitForMs(pollingHandlerEvents, std::chrono::milliseconds(60000));
}

class MyNetworkChangeManagerStub : public NetworkChangeManager_Mock
{

public:

   MyNetworkChangeManagerStub() : NetworkChangeManager_Mock(NULL) {}
   virtual~ MyNetworkChangeManagerStub() {}
   virtual void release() OVERRIDE {}
   virtual int start() OVERRIDE { return kSuccess; }
   virtual int stop() OVERRIDE { return kSuccess; }
   virtual NetworkTransport networkTransport() const OVERRIDE { return NetworkTransport::TransportWiFi; }
   virtual void sendNetworkChangeEvent(const NetworkChangeEvent& event) OVERRIDE {}
   virtual int addObserver(NetworkChangeHandlerInternal* observer) OVERRIDE { return kSuccess; }
   virtual int removeObserver(NetworkChangeHandlerInternal* observer) OVERRIDE { return kSuccess; }

   // NetworkChangePollingManager
   virtual int addPollingHandler(NetworkChangePollingHandlerInternal* handler) OVERRIDE { return kSuccess; }
   virtual int removePollingHandler(NetworkChangePollingHandlerInternal* handler) OVERRIDE { return kSuccess; }
   virtual int setPollingConfig(const NetworkChangePollingConfig& config) OVERRIDE { return kSuccess; }

   // NetworkChangePollingHandler
   virtual void onNetworkInterfaceQuery(NetworkInterfaceQueryEvent& evt) OVERRIDE {}
   virtual void onPublishNetworkChange(const NetworkChangePublishEvent& evt) OVERRIDE {}

   // Overriding to allow control of the startup and shutdown status
   virtual bool isStarted() const OVERRIDE { return mIsStarted; }
   virtual bool isShutdown() const OVERRIDE { return mIsShutdown; }
   bool mIsStarted = false;
   bool mIsShutdown = false;

};

TEST_F(NetworkChangeTest, DISABLED_PollingUnitTest)
{
   TestAccount alice("alice", Account_NoInit);

   NetworkChangeManager_Mock* origNetwork = alice.network;
   MyNetworkChangeManagerStub stubNetwork;
   alice.network = &stubNetwork;
   alice.config.phoneInitConnectionPrefs.useNetworkChangeManager = false;
   alice.init();
   alice.enable();
   CPCAPI2::PhoneInterface* phoneIf = dynamic_cast<PhoneInterface*>(alice.phone);
   ASSERT_TRUE(phoneIf != NULL);

   std::atomic<bool> started = false;
   std::atomic<bool> synced = false;
   std::atomic<bool> shutdown = false;
   std::atomic<bool> query = false;
   std::promise<bool> waitForLifeCycleFuture;
   std::promise<bool> waitForLifeQueryFuture;
   std::unique_ptr<NetworkChangePollingImpl> pollingImpl;
   std::unique_ptr<MyNetworkChangePollingHandler> pollingHandler;

   auto lifeCycleEvents = std::async(std::launch::async, [&] ()
   {
      auto waitForInitialize = waitForLifeCycleFuture.get_future();
      if (waitForInitialize.wait_for(std::chrono::milliseconds(5000)) == std::future_status::ready)
      {
         waitForInitialize.get();

         // Initialize the polling state machine - starts the polling thread
         NetworkInitializeEvent initEvt;
         pollingImpl->onNetworkInitializeEvent(initEvt);
      }
      else
      {
         ASSERT_TRUE(false) << "Timeout waiting for initialization";
      }
      waitForLifeCycleFuture = std::promise<bool>(); // reset for next use

      auto waitForShutdown = waitForLifeCycleFuture.get_future();
      if (waitForShutdown.wait_for(std::chrono::milliseconds(45000)) == std::future_status::ready)
      {
         waitForShutdown.get();

         // Shutdown the polling state machine - stops the polling thread
         NetworkShutdownEvent shutEvt;
         pollingImpl->onNetworkShutdownEvent(shutEvt);
      }
      else
      {
         ASSERT_TRUE(false) << "Timeout waiting for shutdown";
      }
      waitForLifeCycleFuture = std::promise<bool>(); // reset for next use
   });

   auto queryEvents = std::async(std::launch::async, [&] ()
   {
      while (!started)
      {
         std::this_thread::sleep_for(std::chrono::milliseconds(200));
      }

      while (!shutdown)
      {
         // Verify the query expected on behalf of the NetworkChangePollingHandler
         assertNetworkInterfaceQueryEventMsecs(pollingHandler.get(), pollingHandler->mTransport, pollingHandler->mInterfaces, 60000);
      }
   });

   auto pollingHandlerEvents = std::async(std::launch::async, [&] ()
   {
      pollingHandler.reset(new MyNetworkChangePollingHandler(alice));
      pollingImpl.reset(new NetworkChangePollingImpl(phoneIf, pollingHandler.get(), origNetwork));
      pollingHandler->mTransport = NetworkTransport::TransportWiFi;
      pollingHandler->mInterfaces = { "*******" };

      stubNetwork.mIsStarted = true;

      NetworkChangePollingConfig config(1000, 5000, 5000);
      pollingImpl->setPollingConfig(config);

      started = true;
      query = true;
      waitForLifeCycleFuture.set_value(true);

      // Verify the polling status updated expected on behalf of the NetworkChangePollingHandlerInternal
      assertPollingStatusEventMsecs(pollingHandler.get(), NetworkTransport::TransportWiFi, NetworkChangePollingStateType::NetworkChangePollingStateType_Sync, 25000);

      synced = true;
      query = false;

      waitForLifeCycleFuture.set_value(true);

      shutdown = true;
      pollingHandler.reset();
   });

   waitFor3Ms(lifeCycleEvents, queryEvents, pollingHandlerEvents, std::chrono::milliseconds(60000));

   alice.network = origNetwork;
}

TEST_F(NetworkChangeTest, VerifyAppNetworkChangeCallback)
{
   TestAccount alice("alice");

   NetworkTransport transport = TransportWWAN;
   alice.network->setNetworkTransport(transport);

   NetworkChangeEvent evt;
   NetworkChangeManager::NetworkChangeManagerHandle handle;
   ASSERT_TRUE(cpcExpectEvent(alice.networkChangeEvents, "NetworkChangeHandler::onNetworkChangeEx", 30000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
   ASSERT_EQ(evt.networkTransport, transport);

   assertAccountRefreshing(alice);
   assertAccountRegistered(alice);

   transport = TransportNone;
   alice.network->setNetworkTransport(transport);

   ASSERT_TRUE(cpcExpectEvent(alice.networkChangeEvents, "NetworkChangeHandler::onNetworkChangeEx", 30000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
   ASSERT_EQ(evt.networkTransport, transport);

   assertAccountDeregistering(alice);
   assertAccountDeregistered(alice);


   transport = TransportWiFi;
   alice.network->setNetworkTransport(transport);

   ASSERT_TRUE(cpcExpectEvent(alice.networkChangeEvents, "NetworkChangeHandler::onNetworkChangeEx", 30000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
   ASSERT_EQ(evt.networkTransport, transport);

   assertAccountRegistering(alice);
   assertAccountRegistered(alice);
}

// OBELISK-6368: client does not re-register after device is put to sleep and woken up
TEST_F(NetworkChangeTest, VerifyNetworkLossRegainAccountEnable)
{
   std::set<resip::Data> ifaces;
   const resip::Data mockWifiIface("**********");
   
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.useRport = false;
   alice.config.settings.useOutbound = false;

   alice.init();
   alice.enable();


   NetworkTransport transport = TransportNone;
   alice.network->setNetworkTransport(transport);
   alice.network->setMockInterfaces(ifaces);

   ifaces.insert(mockWifiIface);
   transport = TransportWiFi;

   // Simulate the network coming back up *right* before the network change manager goes to send out a TransportNone event.
   // This timing seems to be required to hit the bug
   alice.network->setSendNetworkChangeEventImminentHook([&]() {
      alice.network->setNetworkTransport(transport);
      alice.network->setMockInterfaces(ifaces);
   });

   NetworkChangeEvent evt;
   NetworkChangeManager::NetworkChangeManagerHandle handle;
   ASSERT_TRUE(cpcExpectEvent(alice.networkChangeEvents, "NetworkChangeHandler::onNetworkChangeEx", 30000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
   // The NetworkChangeManager is already committed to sending out a network change of TransportNone even though the network just came back up
   ASSERT_EQ(evt.networkTransport, TransportNone);

   assertAccountDeregistering(alice);
   assertAccountDeregistered(alice);

   // We no longer require the hook
   alice.network->setSendNetworkChangeEventImminentHook(std::function<void()>());

   // Crux of the bug: no network change event propagated
   ASSERT_TRUE(cpcExpectEvent(alice.networkChangeEvents, "NetworkChangeHandler::onNetworkChangeEx", 30000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
   ASSERT_EQ(evt.networkTransport, transport);

   // Crux of the bug: no network change event propagated
   assertAccountRegistering(alice);
   assertAccountRegistered(alice);
}

void NetworkChangeTest_NetworkChangeHandoverUsingStarcode(bool preferPAssertedIdentity)
{
   std::string starcode = "*111";

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.useRport = false;
   alice.config.settings.preferPAssertedIdentity = preferPAssertedIdentity;
   alice.enable();

   TestAccount bob("bob", Account_NoInit);
   bob.config.settings.displayName = bob.config.name;
   bob.init();

   TestAccount conf(starcode.c_str());


   class MySipConversationAdornmentHandler : public SipConversationAdornmentHandler
   {
   public:
      MySipConversationAdornmentHandler(CPCAPI2::SipConversation::SipConversationManager* convMgr, const cpc::string& headerValue) :
         convMgr(convMgr), m_pAssertedIdentityIdHeaderValue(headerValue) {}

      cpc::string m_pAssertedIdentityIdHeaderValue;

      virtual int onConversationAdornment(SipConversationHandle conversation, const ConversationAdornmentEvent& args)
      {
         if (args.responseCode == 200 && !m_pAssertedIdentityIdHeaderValue.empty())
         {
            cpc::vector<CPCAPI2::SipHeader> sipHeaders;
            CPCAPI2::SipHeader sipHeader;
            sipHeader.header = "P-Asserted-Identity";
            sipHeader.value = m_pAssertedIdentityIdHeaderValue;
            sipHeaders.push_back(sipHeader);
            convMgr->adornMessage(conversation, args.adornmentMessageId, sipHeaders);
         }
         return kSuccess;
      }

   private:
      CPCAPI2::SipConversation::SipConversationManager* convMgr;
   };

   const cpc::string bobAssertedDisplayname = "Robert Cleaver";
   const cpc::string bobAssertedAddress = "sip:<EMAIL>";

   std::stringstream bobAssertedIdentity;
   bobAssertedIdentity << "\"" << bobAssertedDisplayname.c_str() << "\" <" << bobAssertedAddress.c_str() << ">";
   MySipConversationAdornmentHandler adornmentHandler(bob.conversation, bobAssertedIdentity.str().c_str());
   bob.conversation->setAdornmentHandler(bob.handle, &adornmentHandler);
   bob.enable();

   SipAccountManagerInternal* accountMgr = dynamic_cast<SipAccountManagerInternal*>(alice.account);
   ASSERT_TRUE(accountMgr != NULL);
   accountMgr->setIgnoreNetworkChangeStarcodeFilter(alice.handle, false);

   SipConversationSettings settings;
   settings.networkChangeHandoverMode = NetworkChangeHandoverMode_Starcode;
   settings.networkChangeHandoverStarcode = conf.config.settings.username;

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      // Establish call between alice and bob
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->setDefaultSettings(alice.handle, settings);
      alice.conversation->addParticipant(aliceCall, bob.config.uri());

      // Reconnection may result with same binding, ignore the filter to ensure that starcode handling is triggered
      SipConversationManagerInternal* convMgr = dynamic_cast<SipConversationManagerInternal*>(alice.conversation);
      ASSERT_TRUE(convMgr != NULL);
      convMgr->ignoreBindingFilterForStarcodeHandover(aliceCall);

      alice.conversation->start(aliceCall);

      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged_ex(alice, aliceCall, [&](const ConversationStateChangedEvent& evt)
      {
         ASSERT_EQ(ConversationState_Connected, evt.conversationState);
         if (alice.config.settings.preferPAssertedIdentity)
         {
            ASSERT_EQ(bobAssertedAddress, evt.remoteAddress);
            ASSERT_EQ(bobAssertedDisplayname, evt.remoteDisplayName);
         }
         else
         {
            ASSERT_EQ(bob.config.uri(), evt.remoteAddress);
            ASSERT_EQ("", evt.remoteDisplayName);
         }
      });

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCall, true, false);

      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> wwan;
      wwan.insert("*******");
      alice.network->setMockInterfaces(wwan);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      // App receives the NewConversationEvent event for the starcode dialog, resulting from the network change
      SipConversationHandle aliceCallToConf = 0;
      {
         NewConversationEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onNewConversation", 15000, AlwaysTruePred(), aliceCallToConf, evt)) << " missed outgoing call event";
         ASSERT_EQ(aliceCall, evt.conversationToReplace) << " replaced conversation handle does not match";
         ASSERT_NE(aliceCall, aliceCallToConf);
         ASSERT_EQ(ConversationType_OutgoingNetworkChangeHandover, evt.conversationType) << " conversation type does not match with outgoing";

         if (alice.config.settings.preferPAssertedIdentity)
         {
            ASSERT_EQ(bobAssertedAddress, evt.remoteAddress);
         }
         else
         {
            ASSERT_EQ(bob.config.uri(), evt.remoteAddress);
         }
      }

      assertConversationMediaChanged(alice, aliceCallToConf, MediaDirection_SendReceive);
      assertConversationStateChanged_ex(alice, aliceCallToConf, [&](const ConversationStateChangedEvent& evt)
      {
         ASSERT_EQ(ConversationState_Connected, evt.conversationState);
         // SCORE-933: remote address / display name not reflective of original call.
         if (alice.config.settings.preferPAssertedIdentity)
         {
            ASSERT_EQ(bobAssertedAddress, evt.remoteAddress);
            ASSERT_EQ(bobAssertedDisplayname, evt.remoteDisplayName);
         }
         else
         {
            ASSERT_EQ(bob.config.uri(), evt.remoteAddress);
            // SCORE-968: currently display name is not populated here
            ASSERT_EQ("", evt.remoteDisplayName);
         }
      });

      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);

      // mimick Swisscom backend sending re-INVITE immediately after starcode call establishes
      assertConversationMediaChangeRequest(alice, aliceCallToConf, MediaDirection_SendReceive);
      alice.conversation->accept(aliceCallToConf);

      // don't expect any conversation state change event (with e.g. changed remote address/displayName)
      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_FALSE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onConversationStateChanged",
            5000, HandleEqualsPred<SipConversationHandle>(aliceCallToConf), h, evt));
      }

      alice.conversationEvents->clearCommands();
      alice.accountEvents->clearCommands();
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCallToConf, true, false);

      assertSuccess(alice.conversation->end(aliceCallToConf));
      assertConversationEnded_time(alice, aliceCallToConf, ConversationEndReason_UserTerminatedLocally, 40000);

      // expect registration refresh after network change during a call
      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall = 0;

      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));

      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      assertConversationEnded_time(bob, bobCall, ConversationEndReason_UserTerminatedRemotely, 40000);
   });

   auto confEvents = std::async(std::launch::async, [&] ()
   {
      std::this_thread::sleep_for(std::chrono::milliseconds(15000));
      SipConversationHandle confCall = 0;
      assertNewConversationIncoming(conf, &confCall, alice.config.uri());
      assertSuccess(conf.conversation->accept(confCall));

      assertConversationMediaChanged(conf, confCall, MediaDirection_SendReceive);
      assertConversationStateChanged(conf, confCall, ConversationState_Connected);

      // mimick Swisscom backend sending re-INVITE immediately after starcode call establishes
      conf.conversation->sendMediaChangeRequest(confCall);

      assertConversationEnded_time(conf, confCall, ConversationEndReason_UserTerminatedRemotely, 30000);
   });

   waitFor3(aliceEvents, bobEvents, confEvents);
}

TEST_F(NetworkChangeTest, NetworkChangeHandoverUsingStarcode_PreferPAssertedIdentity)
{
   NetworkChangeTest_NetworkChangeHandoverUsingStarcode(true);
}

TEST_F(NetworkChangeTest, NetworkChangeHandoverUsingStarcode_NoPreferPAssertedIdentity)
{
   NetworkChangeTest_NetworkChangeHandoverUsingStarcode(false);
}

TEST_F(NetworkChangeTest, NetworkChangeHandoverUsingStarcodeRegistrationRefresh)
{
   std::string starcode = "*111";

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.useRport = false;
   alice.enable();

   TestAccount bob("bob");
   TestAccount conf(starcode.c_str());

   SipAccountManagerInternal* accountMgr = dynamic_cast<SipAccountManagerInternal*>(alice.account);
   ASSERT_TRUE(accountMgr != NULL);
   accountMgr->setIgnoreNetworkChangeStarcodeFilter(alice.handle, false);

   SipConversationSettings settings;
   settings.networkChangeHandoverMode = NetworkChangeHandoverMode_Starcode;
   settings.networkChangeHandoverStarcode = conf.config.settings.username;

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      // Establish call between alice and bob
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->setDefaultSettings(alice.handle, settings);
      alice.conversation->addParticipant(aliceCall, bob.config.uri());

      // Reconnection may result with same binding, ignore the filter to ensure that starcode handling is triggered
      SipConversationManagerInternal* convMgr = dynamic_cast<SipConversationManagerInternal*>(alice.conversation);
      ASSERT_TRUE(convMgr != NULL);
      convMgr->ignoreBindingFilterForStarcodeHandover(aliceCall);

      alice.conversation->start(aliceCall);

      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCall, true, false);

      alice.account->requestRegistrationRefresh(alice.handle, 0);

      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      // App receives the NewConversationEvent event for the starcode dialog, resulting from the network change
      SipConversationHandle aliceCallToConf = 0;
      {
         NewConversationEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onNewConversation", 15000, AlwaysTruePred(), aliceCallToConf, evt)) << " missed outgoing call event";
         ASSERT_EQ(aliceCall, evt.conversationToReplace) << " replaced conversation handle does not match";
         ASSERT_NE(aliceCall, aliceCallToConf);
         ASSERT_EQ(ConversationType_OutgoingNetworkChangeHandover, evt.conversationType) << " conversation type does not match with outgoing";
         ASSERT_EQ(bob.config.uri(), evt.remoteAddress) << " remote-address starcode does not match";
      }

      assertConversationMediaChanged(alice, aliceCallToConf, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCallToConf, ConversationState_Connected);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
      alice.conversationEvents->clearCommands();
      alice.accountEvents->clearCommands();
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCallToConf, true, false);

      assertSuccess(alice.conversation->end(aliceCallToConf));
      assertConversationEnded_time(alice, aliceCallToConf, ConversationEndReason_UserTerminatedLocally, 40000);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall = 0;

      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));

      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      assertConversationEnded_time(bob, bobCall, ConversationEndReason_UserTerminatedRemotely, 40000);
   });

   auto confEvents = std::async(std::launch::async, [&] ()
   {
      std::this_thread::sleep_for(std::chrono::milliseconds(15000));
      SipConversationHandle confCall = 0;
      assertNewConversationIncoming(conf, &confCall, alice.config.uri());
      assertSuccess(conf.conversation->accept(confCall));

      assertConversationMediaChanged(conf, confCall, MediaDirection_SendReceive);
      assertConversationStateChanged(conf, confCall, ConversationState_Connected);

      assertConversationEnded_time(conf, confCall, ConversationEndReason_UserTerminatedRemotely, 30000);
   });

   waitFor3(aliceEvents, bobEvents, confEvents);
}

TEST_F(NetworkChangeTest, NetworkChangeHandoverUsingStarcodeRegistrationRefreshIgnored)
{
   // This would be the default behaviour as the registration refresh request would most likely result in the
   // same binding, as such the starcode handling would be ignored.
   std::string starcode = "*111";

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.useRport = false;
   alice.enable();

   TestAccount bob("bob");
   TestAccount conf(starcode.c_str());

   SipAccountManagerInternal* accountMgr = dynamic_cast<SipAccountManagerInternal*>(alice.account);
   ASSERT_TRUE(accountMgr != NULL);
   accountMgr->setIgnoreNetworkChangeStarcodeFilter(alice.handle, false);

   SipConversationSettings settings;
   settings.networkChangeHandoverMode = NetworkChangeHandoverMode_Starcode;
   settings.networkChangeHandoverStarcode = conf.config.settings.username;

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      // Establish call between alice and bob
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->setDefaultSettings(alice.handle, settings);
      alice.conversation->addParticipant(aliceCall, bob.config.uri());
      alice.conversation->start(aliceCall);

      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCall, true, false);

      alice.account->requestRegistrationRefresh(alice.handle, 0);

      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      
      // App should not receive the NewConversationEvent event for the starcode dialog due to the no-registrar configuration
      SipConversationHandle aliceCallToConf = 0;
      {
         NewConversationEvent evt;
         ASSERT_FALSE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onNewConversation", 10000, AlwaysTruePred(), aliceCallToConf, evt));
      }

      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);

      alice.conversationEvents->clearCommands();
      alice.accountEvents->clearCommands();
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCall, true, false);

      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded_time(alice, aliceCall, ConversationEndReason_UserTerminatedLocally, 40000);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall = 0;

      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));

      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
      assertSuccess(bob.conversation->accept(bobCall));

      assertConversationEnded_time(bob, bobCall, ConversationEndReason_UserTerminatedRemotely, 40000);
   });

   auto confEvents = std::async(std::launch::async, [&] ()
   {
      // we should NOT receive any starcode related call requests
      SipConversationHandle h;
      NewConversationEvent evt;
      ASSERT_FALSE(cpcExpectEvent(conf.conversationEvents, "SipConversationHandler::onNewConversation", 30000, AlwaysTruePred(), h, evt));
   });

   waitFor3(aliceEvents, bobEvents, confEvents);
}

TEST_F(NetworkChangeTest, NetworkChangeHandoverUsingStarcodeWithUnresponsiveHoldBefore)
{
   std::string starcode = "*111";

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.useRport = false;
   alice.enable();

   TestAccount bob("bob");
   TestAccount conf(starcode.c_str());

   SipAccountManagerInternal* accountMgr = dynamic_cast<SipAccountManagerInternal*>(alice.account);
   ASSERT_TRUE(accountMgr != NULL);
   accountMgr->setIgnoreNetworkChangeStarcodeFilter(alice.handle, false);

   SipConversationSettings settings;
   settings.networkChangeHandoverMode = NetworkChangeHandoverMode_Starcode;
   settings.networkChangeHandoverStarcode = conf.config.settings.username;

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      // Establish call between alice and bob
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->setDefaultSettings(alice.handle, settings);
      alice.conversation->addParticipant(aliceCall, bob.config.uri());

      // Reconnection may result with same binding, ignore the filter to ensure that starcode handling is triggered
      SipConversationManagerInternal* convMgr = dynamic_cast<SipConversationManagerInternal*>(alice.conversation);
      ASSERT_TRUE(convMgr != NULL);
      convMgr->ignoreBindingFilterForStarcodeHandover(aliceCall);

      alice.conversation->start(aliceCall);

      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCall, true, false);

      // we place the call on hold just before taking the network offline, so the request may or may not
      // go out to the other end
      assertSuccess(alice.conversation->hold(aliceCall));

      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> wwan;
      wwan.insert("*******");
      alice.network->setMockInterfaces(wwan);

      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);

      // the call should have been dropped because it was on "pending hold" when the network reconnected, 
      // but if the call was instead (incorrectly) restored, then this check would fail
      assertConversationEnded_time(alice, aliceCall, ConversationEndReason_UserTerminatedLocally, 5000);

      // expect registration refresh after network change during a call
      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall = 0;

      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));

      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      // make sure Bob receives the hold request
      assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendOnly);

      // Alice drops the call because it was on hold when the connection came back up, but it will take 
      // a while for Bob to end because Alice will refresh things after the call ends, and also it takes 
      // any re-INVITES about 30s to time out before the BYE is sent.
      // If Alice did NOT drop the call this check would fail.
      assertConversationEnded_time(bob, bobCall, ConversationEndReason_UserTerminatedRemotely, 60000);
   });

   auto confEvents = std::async(std::launch::async, [&] ()
   {
      // we should NOT receive any starcode related call requests
      SipConversationHandle h;
      NewConversationEvent evt;
      ASSERT_FALSE(cpcExpectEvent(conf.conversationEvents,
         "SipConversationHandler::onNewConversation",
         30000,
         AlwaysTruePred(),
         h, evt));
   });

   waitFor3(aliceEvents, bobEvents, confEvents);
}

TEST_F(NetworkChangeTest, NetworkChangeHandoverUsingStarcodeWithUnresponsiveHoldAfter)
{
   std::string starcode = "*111";

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.useRport = false;
   alice.enable();

   TestAccount bob("bob");
   TestAccount conf(starcode.c_str());

   SipAccountManagerInternal* accountMgr = dynamic_cast<SipAccountManagerInternal*>(alice.account);
   ASSERT_TRUE(accountMgr != NULL);
   accountMgr->setIgnoreNetworkChangeStarcodeFilter(alice.handle, false);

   SipConversationSettings settings;
   settings.networkChangeHandoverMode = NetworkChangeHandoverMode_Starcode;
   settings.networkChangeHandoverStarcode = conf.config.settings.username;

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      // Establish call between alice and bob
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->setDefaultSettings(alice.handle, settings);
      alice.conversation->addParticipant(aliceCall, bob.config.uri());

      // Reconnection may result with same binding, ignore the filter to ensure that starcode handling is triggered
      SipConversationManagerInternal* convMgr = dynamic_cast<SipConversationManagerInternal*>(alice.conversation);
      ASSERT_TRUE(convMgr != NULL);
      convMgr->ignoreBindingFilterForStarcodeHandover(aliceCall);

      alice.conversation->start(aliceCall);

      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCall, true, false);

      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> wwan;
      wwan.insert("*******");
      alice.network->setMockInterfaces(wwan);

      // we place the call on hold before the network has been restored
      assertSuccess(alice.conversation->hold(aliceCall));

      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);

      // the call should have been dropped because it was on "pending hold" when the network reconnected, 
      // but if the call was instead (incorrectly) restored, then this check would fail
      assertConversationEnded_time(alice, aliceCall, ConversationEndReason_UserTerminatedLocally, 5000);

      // expect registration refresh after network change during a call
      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall = 0;

      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));

      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      // Alice drops the call because it was on hold when the connection came back up, but it will take 
      // a while for Bob to end because Alice will refresh things after the call ends, and also it takes 
      // any re-INVITES about 30s to time out before the BYE is sent.
      // If Alice did NOT drop the call this check would fail.
      assertConversationEnded_time(bob, bobCall, ConversationEndReason_UserTerminatedRemotely, 60000);
   });

   auto confEvents = std::async(std::launch::async, [&] ()
   {
      // we should NOT receive any starcode related call requests
      SipConversationHandle h;
      NewConversationEvent evt;
      ASSERT_FALSE(cpcExpectEvent(conf.conversationEvents,
         "SipConversationHandler::onNewConversation",
         30000,
         AlwaysTruePred(),
         h, evt));
   });

   waitFor3(aliceEvents, bobEvents, confEvents);
}

TEST_F(NetworkChangeTest, NetworkChangeHandoverUsingStarcodeWithHeldCall)
{
   std::string starcode = "*111";

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.useRport = false;
   alice.enable();

   TestAccount bob("bob");
   TestAccount conf(starcode.c_str());

   SipAccountManagerInternal* accountMgr = dynamic_cast<SipAccountManagerInternal*>(alice.account);
   ASSERT_TRUE(accountMgr != NULL);
   accountMgr->setIgnoreNetworkChangeStarcodeFilter(alice.handle, false);

   SipConversationSettings settings;
   settings.networkChangeHandoverMode = NetworkChangeHandoverMode_Starcode;
   settings.networkChangeHandoverStarcode = conf.config.settings.username;

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      // Establish call between alice and bob
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->setDefaultSettings(alice.handle, settings);
      alice.conversation->addParticipant(aliceCall, bob.config.uri());

      // Reconnection may result with same binding, ignore the filter to ensure that starcode handling is triggered
      SipConversationManagerInternal* convMgr = dynamic_cast<SipConversationManagerInternal*>(alice.conversation);
      ASSERT_TRUE(convMgr != NULL);
      convMgr->ignoreBindingFilterForStarcodeHandover(aliceCall);

      alice.conversation->start(aliceCall);

      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCall, true, false);

      // we place the call on hold before the network has been restored
      assertSuccess(alice.conversation->hold(aliceCall));

      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendOnly);

      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> wwan;
      wwan.insert("*******");
      alice.network->setMockInterfaces(wwan);

      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);

      // the call should have been dropped because it was on "pending hold" when the network reconnected, 
      // but if the call was instead (incorrectly) restored, then this check would fail
      assertConversationEnded_time(alice, aliceCall, ConversationEndReason_UserTerminatedLocally, 5000);

      // expect registration refresh after network change during a call
      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall = 0;

      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));

      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(10000));

      // Alice requested call on hold during disconnected state, but with our unit test framework the 
      // network remains connected so we DO receive the re-INVITE
      assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendOnly);
      assertSuccess(bob.conversation->accept(bobCall));

      // Alice drops the call because it was on hold when the connection came back up, and we use a short
      // timeout here because we want to make sure that accepting the re-INVITE above leads to call drop.
      assertConversationEnded_time(bob, bobCall, ConversationEndReason_UserTerminatedRemotely, 5000);
   });

   auto confEvents = std::async(std::launch::async, [&] ()
   {
      // we should NOT receive any starcode related call requests
      SipConversationHandle h;
      NewConversationEvent evt;
      ASSERT_FALSE(cpcExpectEvent(conf.conversationEvents,
         "SipConversationHandler::onNewConversation",
         30000,
         AlwaysTruePred(),
         h, evt));
   });

   waitFor3(aliceEvents, bobEvents, confEvents);
}

class MySipConversationHandler : public CPCAPI2::EventSyncHandler<CPCAPI2::SipConversation::SipConversationHandlerInternal>
{

public:

   MySipConversationHandler() : mConversation(0) {}
   virtual ~MySipConversationHandler() {}

   virtual int onConversationEndedFromStarcodeNetworkChange(SipConversationHandle conversation, const ConversationEndedEventFromStarcodeNetworkChange& args)
   {
      resip::Lock lck(mMutex);
      mEndedEvent = args;
      mConversation = args.newConversation;
      mCondition.signal();
      return 0;
   }

   SipConversationHandle mConversation;
   resip::Condition mCondition;
   resip::Mutex mMutex;
   ConversationEndedEventFromStarcodeNetworkChange mEndedEvent;

};

TEST_F(NetworkChangeTest, NetworkChangeHandoverUsingStarcodeMultipleCallsVerifyNoEndedCalls)
{
   std::string starcode = "*111";

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.useRport = false;

   TestAccount bob("bob");
   TestAccount conf(starcode.c_str());

   alice.init();
   SipAccountManagerInternal* accountMgr = dynamic_cast<SipAccountManagerInternal*>(alice.account);
   ASSERT_TRUE(accountMgr != NULL);
   accountMgr->setIgnoreNetworkChangeStarcodeFilter(alice.handle, false);

   CPCAPI2::SipConversation::SipConversationManager* aliceConvMgr = CPCAPI2::SipConversation::SipConversationManager::getInterface(alice.phone);
   CPCAPI2::SipConversation::SipAVConversationManagerInterface* aliceConvMgrIf = dynamic_cast<CPCAPI2::SipConversation::SipAVConversationManagerInterface*>(aliceConvMgr);
   std::unique_ptr<MySipConversationHandler> aliceConvObserver(new MySipConversationHandler);
   aliceConvMgrIf->addSdkObserver(aliceConvObserver.get());
   alice.enable();

   SipConversationSettings settings;
   settings.networkChangeHandoverMode = NetworkChangeHandoverMode_Starcode;
   settings.networkChangeHandoverStarcode = conf.config.settings.username;

   int totalCalls = 3;
   for (int index = 0; index < totalCalls; index++)
   {
      auto aliceEvents = std::async(std::launch::async, [&] ()
      {
         // Establish call between alice and bob
         SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
         alice.conversation->setDefaultSettings(alice.handle, settings);
         alice.conversation->addParticipant(aliceCall, bob.config.uri());

         // Reconnection may result with same binding, ignore the filter to ensure that starcode handling is triggered
         SipConversationManagerInternal* convMgr = dynamic_cast<SipConversationManagerInternal*>(alice.conversation);
         ASSERT_TRUE(convMgr != NULL);
         convMgr->ignoreBindingFilterForStarcodeHandover(aliceCall);

         alice.conversation->start(aliceCall);

         assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
         assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
         assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
         assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

         std::this_thread::sleep_for(std::chrono::milliseconds(5000));
         assertMediaFlowing(alice, aliceCall, true, false);

         alice.network->setNetworkTransport((index % 2 == 0) ? NetworkTransport::TransportWWAN : NetworkTransport::TransportWiFi);
         std::stringstream ss;
         ss << "2.2.2." << index;
         std::set<resip::Data> interfaces;
         interfaces.insert(ss.str().c_str());
         alice.network->setMockInterfaces(interfaces);
         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         assertAccountRefreshing(alice);
         assertAccountRegistered(alice);

         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         // App receives the NewConversationEvent event for the starcode dialog, resulting from the network change
         SipConversationHandle aliceCallToConf = 0;
         {
            NewConversationEvent evt;
            ASSERT_TRUE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onNewConversation", 15000, AlwaysTruePred(), aliceCallToConf, evt)) << " missed outgoing call event";
            ASSERT_EQ(aliceCall, evt.conversationToReplace) << " replaced conversation handle does not match";
            ASSERT_NE(aliceCall, aliceCallToConf);
            ASSERT_EQ(ConversationType_OutgoingNetworkChangeHandover, evt.conversationType) << " conversation type does not match with outgoing";
            ASSERT_EQ(bob.config.uri(), evt.remoteAddress) << " remote-address starcode does not match";
         }

         {
            resip::Lock alock(aliceConvObserver->mMutex);
            aliceConvObserver->mCondition.wait(aliceConvObserver->mMutex, 5000);
            ASSERT_NE(aliceConvObserver->mConversation, 0);
            safeCout("BasicCallTests.BasicCallWithAnswerModeHold(): call ended event received for conversation handle: " << aliceConvObserver->mConversation);

            ConversationEndedEventFromStarcodeNetworkChange evt = aliceConvObserver->mEndedEvent;
            ASSERT_EQ(aliceCallToConf, evt.newConversation);
            ASSERT_EQ(aliceCall, evt.originalConversation);
            ASSERT_EQ(0, evt.endedConversations.size()); // As the other calls should have ended, we should not have to end any existng calls
         }

         assertConversationMediaChanged(alice, aliceCallToConf, MediaDirection_SendReceive);
         assertConversationStateChanged(alice, aliceCallToConf, ConversationState_Connected);
         assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
         alice.conversationEvents->clearCommands();
         alice.accountEvents->clearCommands();
         std::this_thread::sleep_for(std::chrono::milliseconds(5000));
         assertMediaFlowing(alice, aliceCallToConf, true, false);

         assertSuccess(alice.conversation->end(aliceCallToConf));
         assertConversationEnded_time(alice, aliceCallToConf, ConversationEndReason_UserTerminatedLocally, 40000);

         // expect registration refresh after network change during a call
         assertAccountRefreshing(alice);
         assertAccountRegistered(alice);
      });

      auto bobEvents = std::async(std::launch::async, [&] ()
      {
         SipConversationHandle bobCall = 0;

         assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
         assertSuccess(bob.conversation->sendRingingResponse(bobCall));
         assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
         assertSuccess(bob.conversation->accept(bobCall));

         assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
         assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

         assertConversationEnded_time(bob, bobCall, ConversationEndReason_UserTerminatedRemotely, 40000);
      });

      auto confEvents = std::async(std::launch::async, [&] ()
      {
         std::this_thread::sleep_for(std::chrono::milliseconds(15000));
         SipConversationHandle confCall = 0;
         assertNewConversationIncoming(conf, &confCall, alice.config.uri());
         assertSuccess(conf.conversation->accept(confCall));

         assertConversationMediaChanged(conf, confCall, MediaDirection_SendReceive);
         assertConversationStateChanged(conf, confCall, ConversationState_Connected);

         assertConversationEnded_time(conf, confCall, ConversationEndReason_UserTerminatedRemotely, 30000);
      });

      waitFor3(aliceEvents, bobEvents, confEvents);
   }

   aliceConvMgrIf->removeSdkObserver(aliceConvObserver.get()); // avoid crash if aliceConvObserver destructs too early
}

TEST_F(NetworkChangeTest, NetworkChangeHandoverUsingStarcodeWithNoRegistrarConfiguration)
{
   ReproHolder::destroyInstance();
   repro::ReproRunner* reproRunner = runRepro("repro_5060.config");

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.useRport = false;
   alice.config.settings.useRegistrar = false;
   alice.config.settings.domain = "autotest.cpcapi2";
   alice.config.settings.outboundProxy = "127.0.0.1";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.init();
   SipAccountManagerInternal* accountMgr = dynamic_cast<SipAccountManagerInternal*>(alice.account);
   ASSERT_TRUE(accountMgr != NULL);
   accountMgr->setIgnoreNetworkChangeStarcodeFilter(alice.handle, false);
   alice.enable(true);

   TestAccount bob("bob", Account_NoInit);
   bob.config.settings.domain = "autotest.cpcapi2";
   bob.config.settings.outboundProxy = "127.0.0.1";
   bob.enable(true);

   std::string starcode = "*111";
   TestAccount conf(starcode.c_str(), Account_NoInit);
   conf.config.settings.domain = "autotest.cpcapi2";
   conf.config.settings.outboundProxy = "127.0.0.1";
   conf.enable(true);

   // Initial network
   ASSERT_EQ(alice.network->networkTransport(), NetworkTransport::TransportWiFi);
   ASSERT_EQ(bob.network->networkTransport(), NetworkTransport::TransportWiFi);
   ASSERT_EQ(conf.network->networkTransport(), NetworkTransport::TransportWiFi);

   SipConversationSettings aliceConversationSettings;
   aliceConversationSettings.networkChangeHandoverMode = NetworkChangeHandoverMode_Starcode;
   aliceConversationSettings.networkChangeHandoverStarcode = conf.config.settings.username;

   // Bob calls Charlie then Charlie hangs up
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->setDefaultSettings(alice.handle, aliceConversationSettings);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);
   auto aliceConversationEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle h;
      NewConversationEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents, "SipConversationHandler::onNewConversation", 5000, AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.account, alice.handle);

      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCall, true, false);

      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> wwan;
      wwan.insert("*******");
      alice.network->setMockInterfaces(wwan);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);

      // App should not receive the NewConversationEvent event for the starcode dialog due to the no-registrar configuration
      SipConversationHandle aliceCallToConf = 0;
      {
         NewConversationEvent evt;
         ASSERT_FALSE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onNewConversation", 10000, AlwaysTruePred(), aliceCallToConf, evt));
      }

      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      alice.conversationEvents->clearCommands();
      alice.accountEvents->clearCommands();
      assertMediaFlowing(alice, aliceCall, true, false);

      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobConversationEvents = std::async(std::launch::async, [&] ()
   {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      bob.conversation->sendRingingResponse(bobCall);
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      bob.conversation->accept(bobCall);
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
      assertSuccess(bob.conversation->accept(bobCall));

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      bob.conversationEvents->clearCommands();
      bob.accountEvents->clearCommands();
      assertMediaFlowing(bob, bobCall, true, false);

      assertConversationEnded_time(bob, bobCall, ConversationEndReason_UserTerminatedRemotely, 45000);
   });

   auto confConversationEvents = std::async(std::launch::async, [&] ()
   {
      // we should NOT receive any starcode related call requests
      SipConversationHandle h;
      NewConversationEvent evt;
      ASSERT_FALSE(cpcExpectEvent(conf.conversationEvents, "SipConversationHandler::onNewConversation", 45000, AlwaysTruePred(), h, evt));
   });

   waitFor3(aliceConversationEvents, bobConversationEvents, confConversationEvents);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   runOriginalRepro(reproRunner);
}

TEST_F(NetworkChangeTest, NetworkChangeHandoverUsingStarcodeMultipleCallsVerifyEndedCalls)
{
   std::string starcode = "*111";

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.useRport = false;
   TestAccount bob("bob");
   TestAccount conf(starcode.c_str());

   alice.init();
   SipAccountManagerInternal* accountMgr = dynamic_cast<SipAccountManagerInternal*>(alice.account);
   ASSERT_TRUE(accountMgr != NULL);
   accountMgr->setIgnoreNetworkChangeStarcodeFilter(alice.handle, false);

   CPCAPI2::SipConversation::SipConversationManager* aliceConvMgr = CPCAPI2::SipConversation::SipConversationManager::getInterface(alice.phone);
   CPCAPI2::SipConversation::SipAVConversationManagerInterface* aliceConvMgrIf = dynamic_cast<CPCAPI2::SipConversation::SipAVConversationManagerInterface*>(aliceConvMgr);
   std::unique_ptr<MySipConversationHandler> aliceConvObserver(new MySipConversationHandler);
   aliceConvMgrIf->addSdkObserver(aliceConvObserver.get());
   alice.enable();

   SipConversationSettings settings;
   settings.networkChangeHandoverMode = NetworkChangeHandoverMode_Starcode;
   settings.networkChangeHandoverStarcode = conf.config.settings.username;

   int totalBackgroundCalls = 2;
   for (int index = 0; index < totalBackgroundCalls; index++)
   {
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->addParticipant(aliceCall, bob.config.uri());

      // Reconnection may result with same binding, ignore the filter to ensure that starcode handling is triggered
      SipConversationManagerInternal* convMgr = dynamic_cast<SipConversationManagerInternal*>(alice.conversation);
      ASSERT_TRUE(convMgr != NULL);
      convMgr->ignoreBindingFilterForStarcodeHandover(aliceCall);

      alice.conversation->start(aliceCall);

      auto aliceEvents = std::async(std::launch::async, [&] ()
      {
         assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
         assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt)
         {
            ASSERT_FALSE(evt.localHold);
            ASSERT_FALSE(evt.remoteHold);
            ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         });
         assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

         assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendOnly);
         assertSuccess(alice.conversation->accept(aliceCall));
         assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt)
         {
            ASSERT_FALSE(evt.localHold);
            ASSERT_TRUE(evt.remoteHold);
            ASSERT_EQ(MediaDirection_ReceiveOnly, evt.localMediaInfo[0].mediaDirection);
         });

         std::this_thread::sleep_for(std::chrono::milliseconds(1000));

         assertSuccess(alice.conversation->hold(aliceCall));
         assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt)
         {
            ASSERT_TRUE(evt.localHold);
            ASSERT_TRUE(evt.remoteHold);
            ASSERT_EQ(MediaDirection_Inactive, evt.localMediaInfo[0].mediaDirection);
         });
      });

      auto bobEvents = std::async(std::launch::async, [&] ()
      {
         SipConversationHandle bobCall = 0;
         assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
         assertSuccess(bob.conversation->accept(bobCall));
         assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt)
         {
            ASSERT_FALSE(evt.localHold);
            ASSERT_FALSE(evt.remoteHold);
            ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         });
         assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

         assertSuccess(bob.conversation->hold(bobCall));
         assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt)
         {
            ASSERT_TRUE(evt.localHold);
            ASSERT_FALSE(evt.remoteHold);
            ASSERT_EQ(MediaDirection_SendOnly, evt.localMediaInfo[0].mediaDirection);
         });

         std::this_thread::sleep_for(std::chrono::milliseconds(1000));

         assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendOnly);
         assertSuccess(bob.conversation->accept(bobCall));
         assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt)
         {
            ASSERT_TRUE(evt.localHold);
            ASSERT_TRUE(evt.remoteHold);
            ASSERT_EQ(MediaDirection_Inactive, evt.localMediaInfo[0].mediaDirection);
         });
      });

      waitFor2(aliceEvents, bobEvents);
   }

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      // Establish call between alice and bob
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->setDefaultSettings(alice.handle, settings);
      alice.conversation->addParticipant(aliceCall, bob.config.uri());
      alice.conversation->start(aliceCall);

      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCall, true, false);

      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> interfaces;
      interfaces.insert("*******");
      alice.network->setMockInterfaces(interfaces);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      // App receives the NewConversationEvent event for the starcode dialog, resulting from the network change
      SipConversationHandle aliceCallToConf = 0;
      {
         NewConversationEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onNewConversation", 15000, AlwaysTruePred(), aliceCallToConf, evt)) << " missed outgoing call event";
         ASSERT_EQ(aliceCall, evt.conversationToReplace) << " replaced conversation handle does not match";
         ASSERT_NE(aliceCall, aliceCallToConf);
         ASSERT_EQ(ConversationType_OutgoingNetworkChangeHandover, evt.conversationType) << " conversation type does not match with outgoing";
         ASSERT_EQ(bob.config.uri(), evt.remoteAddress) << " remote-address starcode does not match";
      }

      {
         resip::Lock alock(aliceConvObserver->mMutex);
         aliceConvObserver->mCondition.wait(aliceConvObserver->mMutex, 5000);
         ASSERT_NE(aliceConvObserver->mConversation, 0);
         safeCout("NetworkChangeTest.NetworkChangeHandoverUsingStarcodeMultipleCallsVerifyEndedCalls(): call ended event received for conversation handle: " << aliceConvObserver->mConversation);

         ConversationEndedEventFromStarcodeNetworkChange evt = aliceConvObserver->mEndedEvent;
         ASSERT_EQ(aliceCallToConf, evt.newConversation);
         ASSERT_EQ(aliceCall, evt.originalConversation);
         ASSERT_EQ(totalBackgroundCalls, evt.endedConversations.size()); // The background calls should have been destroyed
      }

      assertConversationMediaChanged(alice, aliceCallToConf, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCallToConf, ConversationState_Connected);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
      alice.conversationEvents->clearCommands();
      alice.accountEvents->clearCommands();
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCallToConf, true, false);

      assertSuccess(alice.conversation->end(aliceCallToConf));
      assertConversationEnded_time(alice, aliceCallToConf, ConversationEndReason_UserTerminatedLocally, 40000);

      // expect registration refresh after network change during a call
      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);

      aliceConvMgrIf->removeSdkObserver(aliceConvObserver.get()); // avoid crash if aliceConvObserver destructs too early
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall = 0;

      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));

      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      assertConversationEnded_time(bob, bobCall, ConversationEndReason_UserTerminatedRemotely, 40000);
   });

   auto confEvents = std::async(std::launch::async, [&] ()
   {
      std::this_thread::sleep_for(std::chrono::milliseconds(15000));
      SipConversationHandle confCall = 0;
      assertNewConversationIncoming(conf, &confCall, alice.config.uri());
      assertSuccess(conf.conversation->accept(confCall));

      assertConversationMediaChanged(conf, confCall, MediaDirection_SendReceive);
      assertConversationStateChanged(conf, confCall, ConversationState_Connected);

      assertConversationEnded_time(conf, confCall, ConversationEndReason_UserTerminatedRemotely, 30000);
   });

   waitFor3(aliceEvents, bobEvents, confEvents);
}

TEST_F(NetworkChangeTest, NetworkChangeHandoverUsingStarcodeTransportDisconnect)
{
   ReproHolder::destroyInstance();

   // our stock repro config does not seem to use Record Route to force SIP traffic through repro when TCP is used.
   // this is problematic if one SDK A registers with TCP and useRport enabled; other SDKs looking at SDK A's Contact
   // header will try to send to it directly, which will fail (no TCP port will be open, since our TCP listen port is different
   // than the one we send intial REGISTERs from
   repro::ReproRunner* reproRunner = runRepro("repro_force_recordroute.config");

   std::string starcode = "*111";

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.sipTransportType = SipAccountTransport_TCP;
   alice.config.settings.autoRetryOnTransportDisconnect = true;
   alice.config.settings.useRport = false; // reduce log spam
   alice.config.settings.useOutbound = false; // reduce log spam
   alice.config.settings.registrationIntervalSeconds = 3600; // ensure it isn't a re-REGISTER that discovers connection drop

   // use a very low registration retry interval; otherwise the SDK won't
   // fire account state going to Status_WaitingToRegister for 10s of seconds
   // due to filtering code in onRequestRetry(..) -- "// Hold-Off on updating UI on internally generated transport error .. "
   alice.config.settings.minimumRegistrationIntervalSeconds = 3; 
   alice.config.settings.maximumRegistrationIntervalSeconds = 3; 

   alice.config.settings.overrideMsecsTimerF = 8000;
   alice.config.settings.tcpKeepAliveTime = 5; // reduce test run time -- detect connection loss more quickly
   alice.init();
   alice.enable();

   TestAccount bob("bob");
   TestAccount conf(starcode.c_str(), Account_Init);

   SipAccountManagerInternal* accountMgr = dynamic_cast<SipAccountManagerInternal*>(alice.account);
   ASSERT_TRUE(accountMgr != NULL);
   accountMgr->setIgnoreNetworkChangeStarcodeFilter(alice.handle, false);

   SipConversationSettings settings;
   settings.networkChangeHandoverMode = NetworkChangeHandoverMode_Starcode;
   settings.networkChangeHandoverStarcode = conf.config.settings.username;

   SipConversationHandle aliceCall;

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      // Establish call between alice and bob
      aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->setDefaultSettings(alice.handle, settings);
      alice.conversation->addParticipant(aliceCall, bob.config.uri());

      // Reconnection may result with same binding, ignore the filter to ensure that starcode handling is triggered
      SipConversationManagerInternal* convMgr = dynamic_cast<SipConversationManagerInternal*>(alice.conversation);
      ASSERT_TRUE(convMgr != NULL);
      convMgr->ignoreBindingFilterForStarcodeHandover(aliceCall);

      alice.conversation->start(aliceCall);

      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCall, true, false);
   });

   SipConversationHandle bobCall;
   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));

      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
   });
   waitFor2(aliceEvents, bobEvents);

   // restart repro -- this will break the active TCP connection
   delete reproRunner;

   // the SDK should detect the TCP connection being broken, and due to autoRetryOnTransportDisconnect
   // being enabled, immediately establish a new TCP connection and re-REGISTER
   assertAccountWaitingToRegister(alice);

   // std::this_thread::sleep_for(std::chrono::milliseconds(5000));
   reproRunner = runRepro("repro_force_recordroute.config");

   // wait until after repro is restarted to spin up the *111 listener endpoint
   conf.enable();

   auto aliceEvents2 = std::async(std::launch::async, [&] ()
   {
      assertAccountRegistering(alice);
      assertAccountRegistered(alice);

      // App receives the NewConversationEvent event for the starcode dialog, resulting from the connection loss
      SipConversationHandle aliceCallToConf = 0;
      {
         NewConversationEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onNewConversation", 15000, AlwaysTruePred(), aliceCallToConf, evt)) << " missed outgoing call event";
         ASSERT_EQ(aliceCall, evt.conversationToReplace) << " replaced conversation handle does not match";
         ASSERT_EQ(ConversationType_OutgoingNetworkChangeHandover, evt.conversationType) << " conversation type does not match with outgoing";
         ASSERT_EQ(bob.config.uri(), evt.remoteAddress) << " remote-address starcode does not match";
      }

      assertConversationMediaChanged(alice, aliceCallToConf, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCallToConf, ConversationState_Connected);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
      alice.conversationEvents->clearCommands();
      alice.accountEvents->clearCommands();
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCallToConf, true, false);

      assertSuccess(alice.conversation->end(aliceCallToConf));
      assertConversationEnded_time(alice, aliceCallToConf, ConversationEndReason_UserTerminatedLocally, 40000);
   });

   auto bobEvents2 = std::async(std::launch::async, [&] ()
   {
   });

   auto confEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle confCall;
      assertNewConversationIncoming(conf, &confCall, alice.config.uri());
      assertSuccess(conf.conversation->accept(confCall));

      assertConversationMediaChanged(conf, confCall, MediaDirection_SendReceive);
      assertConversationStateChanged(conf, confCall, ConversationState_Connected);

      assertConversationEnded_time(conf, confCall, ConversationEndReason_UserTerminatedRemotely, 30000);
   });

   waitFor3(aliceEvents2, bobEvents2, confEvents);

   // disable all account before restoring original repro
   alice.disable();
   bob.disable();
   conf.disable();

   delete reproRunner;
   runOriginalRepro();
}

TEST_F(NetworkChangeTest, NetworkChangeHandoverUsingStarcodeIgnored)
{
   std::string starcode = "*111";

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.useRport = false;
   alice.enable();
   TestAccount bob("bob");

   SipConversationSettings settings;
   settings.networkChangeHandoverMode = NetworkChangeHandoverMode_Starcode;
   settings.networkChangeHandoverStarcode = starcode.c_str();

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      // Establish call between alice and bob
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->setDefaultSettings(alice.handle, settings);
      alice.conversation->addParticipant(aliceCall, bob.config.uri());
      alice.conversation->start(aliceCall);

      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCall, true, false);

      alice.accountEvents->clearCommands();
      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> wwan;
      wwan.insert("*******");
      alice.network->setMockInterfaces(wwan);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      // As a call is in progress, should ignore the network change as the interfaces are the same
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
         ASSERT_FALSE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      }

      alice.conversationEvents->clearCommands();
      alice.accountEvents->clearCommands();
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCall, true, false);
   
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall;

      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));

      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      assertConversationEnded_time(bob, bobCall, ConversationEndReason_UserTerminatedRemotely, 60000);
   });

   waitFor2(aliceEvents, bobEvents);
}


TEST_F(NetworkChangeTest, NetworkChangeHandoverUsingStarcodeIgnored_TLS)
{
   ReproHolder::destroyInstance();
   repro::ReproRunner* reproRunner = runRepro("repro_force_recordroute.config");

   std::string starcode = "*111";

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.sipTransportType = SipAccountTransport_TLS;
   alice.config.settings.domain = "autotest.cpcapi2:5071";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.outboundProxy = "";
   alice.config.settings.useRport = false;
   alice.config.settings.useOutbound = false;
   alice.init();

   SipAccountManagerInternal* mi = dynamic_cast<SipAccountManagerInternal*>(alice.account);
   mi->setCertStorageLoadType(alice.handle, SipAccountManagerInternal::CertLoadStorageType_FileSystem);
   // this folder should contain the correct cert to trust to get validation to succeed
   mi->setCertStorageFileSystemPath(alice.handle, TestEnvironmentConfig::testResourcePath() + "SelfSignedCerts/root");

   alice.enable();

   TestAccount bob("bob", Account_NoInit);
   bob.config.settings.useRport = false;
   bob.init();
   bob.enable();

   SipConversationSettings settings;
   settings.networkChangeHandoverMode = NetworkChangeHandoverMode_Starcode;
   settings.networkChangeHandoverStarcode = starcode.c_str();

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      // Establish call between alice and bob
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->setDefaultSettings(alice.handle, settings);
      alice.conversation->addParticipant(aliceCall, bob.config.uri());
      alice.conversation->start(aliceCall);

      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCall, true, false);

      alice.accountEvents->clearCommands();
      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> wwan;
      wwan.insert("*******");
      alice.network->setMockInterfaces(wwan);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      // As a call is in progress, should ignore the network change as the interfaces are the same
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
         ASSERT_FALSE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      }

      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall;

      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));

      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      assertConversationEnded_time(bob, bobCall, ConversationEndReason_UserTerminatedRemotely, 60000);
   });

   waitFor2(aliceEvents, bobEvents);

   // disable all account before restoring original repro
   alice.disable();
   bob.disable();

   delete reproRunner;
   runOriginalRepro();
}

TEST_F(NetworkChangeTest, NetworkChangeHandoverUsingStarcodeNoNetworkThenRegainedNetwork_TLS)
{
   ReproHolder::destroyInstance();
   repro::ReproRunner* reproRunner = runRepro("repro_force_recordroute.config");

   std::string starcode = "*111";

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.sipTransportType = SipAccountTransport_TLS;
   alice.config.settings.ignoreCertVerification = false;
   // use repro port that forces Record-Route with IP address of repro instead of DNS name;
   // with TLS cert validation enabled this helps ensure we don't re-open a TLS connection
   // to an IP address which can result in TLS cert validation faiure.
   alice.config.settings.outboundProxy = "autotest.cpcapi2:5071";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.useRport = false;
   alice.config.settings.useOutbound = false;
   alice.init();

   SipAccountManagerInternal* mi = dynamic_cast<SipAccountManagerInternal*>(alice.account);
   mi->setCertStorageLoadType(alice.handle, SipAccountManagerInternal::CertLoadStorageType_FileSystem);
   // this folder should contain the correct cert to trust to get validation to succeed
   mi->setCertStorageFileSystemPath(alice.handle, TestEnvironmentConfig::testResourcePath() + "SelfSignedCerts/root");

   alice.enable();

   TestAccount bob("bob", Account_NoInit);
   bob.config.settings.useRport = false;
   bob.enable();

   TestAccount conf(starcode.c_str(), Account_NoInit);
   conf.enable();

   SipConversationSettings settings;
   settings.networkChangeHandoverMode = NetworkChangeHandoverMode_Starcode;
   settings.networkChangeHandoverStarcode = conf.config.settings.username;

   const resip::Data simWifiIf = "**********";
   std::set<resip::Data> ifs;
   ifs.insert(simWifiIf);
   alice.network->setMockInterfaces(ifs);

   std::atomic_bool starcodeWait = true;

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      // Establish call between alice and bob
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->setDefaultSettings(alice.handle, settings);
      alice.conversation->addParticipant(aliceCall, bob.config.uri());

      // Reconnection may result with same binding, ignore the filter to ensure that starcode handling is triggered
      SipConversationManagerInternal* convMgr = dynamic_cast<SipConversationManagerInternal*>(alice.conversation);
      ASSERT_TRUE(convMgr != NULL);
      convMgr->ignoreBindingFilterForStarcodeHandover(aliceCall);

      alice.conversation->start(aliceCall);

      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCall, true, false);

      alice.accountEvents->clearCommands();
      alice.network->setNetworkTransport(NetworkTransport::TransportNone);
      ifs.clear();
      alice.network->setMockInterfaces(ifs);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      // As a call is in progress and we lose network; should not be ignored (althoug no starcode INVITE will go out). The account status should not change
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
         ASSERT_FALSE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      }

      starcodeWait = false;

      alice.network->setNetworkTransport(NetworkTransport::TransportWiFi);
      std::set<resip::Data> ifs;
      ifs.insert(simWifiIf);
      alice.network->setMockInterfaces(ifs);

      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      // App receives the NewConversationEvent event for the starcode dialog, resulting from the network change
      SipConversationHandle aliceCallToConf = 0;
      {
         NewConversationEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onNewConversation", 15000, AlwaysTruePred(), aliceCallToConf, evt)) << " missed outgoing call event";
         ASSERT_EQ(aliceCall, evt.conversationToReplace) << " replaced conversation handle does not match";
         ASSERT_NE(aliceCall, aliceCallToConf);
         ASSERT_EQ(ConversationType_OutgoingNetworkChangeHandover, evt.conversationType) << " conversation type does not match with outgoing";
         ASSERT_EQ(bob.config.uri(), evt.remoteAddress) << " remote-address starcode does not match";
      }

      assertConversationMediaChanged(alice, aliceCallToConf, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCallToConf, ConversationState_Connected);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
      alice.conversationEvents->clearCommands();
      alice.accountEvents->clearCommands();
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCallToConf, true, false);

      assertSuccess(alice.conversation->end(aliceCallToConf));
      assertConversationEnded_time(alice, aliceCallToConf, ConversationEndReason_UserTerminatedLocally, 40000);

      // crux of OBELISK-6354: the account should *not* deregister automatically; athough due to changes in OBELISK-6318
      // (registration refresh triggered after all calls end if there was a network change during a call) the account should refresh
      // and then go to a registered state
      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);

      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
      ASSERT_FALSE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged", __FILE__, 10000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall;

      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));

      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      assertConversationEnded_time(bob, bobCall, ConversationEndReason_UserTerminatedRemotely, 60000);
   });

   auto confEvents = std::async(std::launch::async, [&] ()
   {
      while (starcodeWait)
      {
         std::this_thread::sleep_for(std::chrono::seconds(1));
      }

      SipConversationHandle confCall;
      assertNewConversationIncoming(conf, &confCall, alice.config.uri());
      assertSuccess(conf.conversation->accept(confCall));

      assertConversationMediaChanged(conf, confCall, MediaDirection_SendReceive);
      assertConversationStateChanged(conf, confCall, ConversationState_Connected);

      assertConversationEnded_time(conf, confCall, ConversationEndReason_UserTerminatedRemotely, 30000);
   });

   waitFor3(aliceEvents, bobEvents, confEvents);

   // disable all account before restoring original repro
   alice.disable();
   bob.disable();
   conf.disable();

   delete reproRunner;
   runOriginalRepro();
}

TEST_F(NetworkChangeTest, NetworkChangeHandoverUsingStarcodeIgnoredWithMultipleCalls)
{
   std::string starcode = "*111";

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.useRport = false;
   alice.enable();
   TestAccount bob("bob");
   TestAccount max("max");

   SipConversationSettings settings;
   settings.networkChangeHandoverMode = NetworkChangeHandoverMode_Starcode;
   settings.networkChangeHandoverStarcode = starcode.c_str();

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      // Establish call between alice and bob
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->setDefaultSettings(alice.handle, settings);
      alice.conversation->addParticipant(aliceCall, bob.config.uri());

      // Reconnection may result with same binding, ignore the filter to ensure that starcode handling is triggered
      SipConversationManagerInternal* convMgr = dynamic_cast<SipConversationManagerInternal*>(alice.conversation);
      ASSERT_TRUE(convMgr != NULL);
      convMgr->ignoreBindingFilterForStarcodeHandover(aliceCall);

      alice.conversation->start(aliceCall);

      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCall, true, false);

      assertSuccess(alice.conversation->hold(aliceCall));
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendOnly);

      // Establish call between alice and max
      SipConversationHandle aliceCall2 = alice.conversation->createConversation(alice.handle);
      alice.conversation->setDefaultSettings(alice.handle, settings);
      alice.conversation->addParticipant(aliceCall2, max.config.uri());
      alice.conversation->start(aliceCall2);

      assertNewConversationOutgoing(alice, aliceCall2, max.config.uri());
      assertConversationStateChanged(alice, aliceCall2, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall2, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall2, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCall2, true, false);

      alice.accountEvents->clearCommands();
      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> wwan;
      wwan.insert("*******");
      alice.network->setMockInterfaces(wwan);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      // As calls are in progress, should ignore the network change as the interfaces are the same
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
         ASSERT_FALSE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      }

      // First call terminated
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);

      alice.accountEvents->clearCommands();
      alice.network->setNetworkTransport(NetworkTransport::TransportWiFi);
      std::set<resip::Data> wifi;
      wifi.insert("*******");
      alice.network->setMockInterfaces(wifi);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      // As a call is still in progress, should ignore the network change as the interfaces are the same
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
         ASSERT_FALSE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      }

      assertSuccess(alice.conversation->end(aliceCall2));
      assertConversationEnded(alice, aliceCall2, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall;

      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));

      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendOnly);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_ReceiveOnly);

      assertConversationEnded_time(bob, bobCall, ConversationEndReason_UserTerminatedRemotely, 60000);
   });

   auto maxEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle maxCall;

      assertNewConversationIncoming(max, &maxCall, alice.config.uri());
      assertSuccess(max.conversation->sendRingingResponse(maxCall));
      assertConversationStateChanged(max, maxCall, ConversationState_LocalRinging);
      assertSuccess(max.conversation->accept(maxCall));

      assertConversationMediaChanged(max, maxCall, MediaDirection_SendReceive);
      assertConversationStateChanged(max, maxCall, ConversationState_Connected);

      assertConversationEnded_time(max, maxCall, ConversationEndReason_UserTerminatedRemotely, 60000);
   });

   waitFor3Ms(aliceEvents, bobEvents, maxEvents, std::chrono::milliseconds(120000));
}

TEST_F(NetworkChangeTest, NetworkChangeHandoverUsingStarcodeIgnoredDueToLocalConference)
{
   std::string starcode = "*111";

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.useRport = false;
   alice.enable();
   TestAccount bob("bob");
   TestAccount max("max");

   SipAccountManagerInternal* accountMgr = dynamic_cast<SipAccountManagerInternal*>(alice.account);
   ASSERT_TRUE(accountMgr != NULL);
   accountMgr->setIgnoreNetworkChangeStarcodeFilter(alice.handle, false);

   SipConversationSettings settings;
   settings.networkChangeHandoverMode = NetworkChangeHandoverMode_Starcode;
   settings.networkChangeHandoverStarcode = starcode.c_str();

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      // Establish call between alice and bob
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->setDefaultSettings(alice.handle, settings);
      alice.conversation->addParticipant(aliceCall, bob.config.uri());

      // Reconnection may result with same binding, ignore the filter to ensure that starcode handling is triggered
      SipConversationManagerInternal* convMgr = dynamic_cast<SipConversationManagerInternal*>(alice.conversation);
      ASSERT_TRUE(convMgr != NULL);
      convMgr->ignoreBindingFilterForStarcodeHandover(aliceCall);

      alice.conversation->start(aliceCall);

      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCall, true, false);

      assertSuccess(alice.conversation->hold(aliceCall));
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendOnly);

      // Establish call between alice and max
      SipConversationHandle aliceCall2 = alice.conversation->createConversation(alice.handle);
      alice.conversation->setDefaultSettings(alice.handle, settings);
      alice.conversation->addParticipant(aliceCall2, max.config.uri());
      alice.conversation->start(aliceCall2);

      assertNewConversationOutgoing(alice, aliceCall2, max.config.uri());
      assertConversationStateChanged(alice, aliceCall2, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall2, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall2, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCall2, true, false);

      // Make this a conference by un-holding all calls
      assertSuccess(alice.conversation->unhold(aliceCall));
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);

      alice.accountEvents->clearCommands();
      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> wwan;
      wwan.insert("*******");
      alice.network->setMockInterfaces(wwan);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      // Should see the network change handling take place despite the calls as the filter has been disabled
      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);

      // Starcode handling will destroy all calls as they cannot be pulled
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
      assertConversationEnded(alice, aliceCall2, ConversationEndReason_UserTerminatedLocally);

      // expect registration refresh after network change during a call
      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall;

      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));

      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendOnly);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_ReceiveOnly);

      assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);

      assertConversationEnded_time(bob, bobCall, ConversationEndReason_UserTerminatedRemotely, 60000);
   });

   auto maxEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle maxCall;

      assertNewConversationIncoming(max, &maxCall, alice.config.uri());
      assertSuccess(max.conversation->sendRingingResponse(maxCall));
      assertConversationStateChanged(max, maxCall, ConversationState_LocalRinging);
      assertSuccess(max.conversation->accept(maxCall));

      assertConversationMediaChanged(max, maxCall, MediaDirection_SendReceive);
      assertConversationStateChanged(max, maxCall, ConversationState_Connected);

      assertConversationEnded_time(max, maxCall, ConversationEndReason_UserTerminatedRemotely, 60000);
   });

   waitFor3Ms(aliceEvents, bobEvents, maxEvents, std::chrono::milliseconds(120000));
}

TEST_F(NetworkChangeTest, NetworkChangeHandoverUsingStarcodeDestroyInactiveCalls)
{
   std::string starcode = "*111";

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.useRport = false;
   alice.enable();
   TestAccount bob("bob");
   TestAccount max("max");
   TestAccount conf(starcode.c_str());

   SipAccountManagerInternal* accountMgr = dynamic_cast<SipAccountManagerInternal*>(alice.account);
   ASSERT_TRUE(accountMgr != NULL);
   accountMgr->setIgnoreNetworkChangeStarcodeFilter(alice.handle, false);

   SipConversationSettings settings;
   settings.networkChangeHandoverMode = NetworkChangeHandoverMode_Starcode;
   settings.networkChangeHandoverStarcode = conf.config.settings.username;

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      // Establish call between alice and bob
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->setDefaultSettings(alice.handle, settings);
      alice.conversation->addParticipant(aliceCall, bob.config.uri());

      // Reconnection may result with same binding, ignore the filter to ensure that starcode handling is triggered
      SipConversationManagerInternal* convMgr = dynamic_cast<SipConversationManagerInternal*>(alice.conversation);
      ASSERT_TRUE(convMgr != NULL);
      convMgr->ignoreBindingFilterForStarcodeHandover(aliceCall);

      alice.conversation->start(aliceCall);

      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCall, true, false);

      assertSuccess(alice.conversation->hold(aliceCall));
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendOnly);

      // Establish call between alice and bob
      SipConversationHandle aliceCall2 = alice.conversation->createConversation(alice.handle);
      alice.conversation->setDefaultSettings(alice.handle, settings);
      alice.conversation->addParticipant(aliceCall2, max.config.uri());

      // Reconnection may result with same binding, ignore the filter to ensure that starcode handling is triggered
      convMgr->ignoreBindingFilterForStarcodeHandover(aliceCall2);

      alice.conversation->start(aliceCall2);

      assertNewConversationOutgoing(alice, aliceCall2, max.config.uri());
      assertConversationStateChanged(alice, aliceCall2, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall2, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall2, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCall2, true, false);

      alice.accountEvents->clearCommands();
      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> wwan;
      wwan.insert("*******");
      alice.network->setMockInterfaces(wwan);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);

      // Should see the held call being terminated automatically
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      // App receives the NewConversationEvent event for the starcode dialog, resulting from the network change
      SipConversationHandle aliceCallToConf = 0;
      {
         NewConversationEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onNewConversation", 15000, AlwaysTruePred(), aliceCallToConf, evt)) << " missed outgoing call event";
         ASSERT_EQ(aliceCall2, evt.conversationToReplace) << " replaced conversation handle does not match";
         ASSERT_NE(aliceCall2, aliceCallToConf);
         ASSERT_EQ(ConversationType_OutgoingNetworkChangeHandover, evt.conversationType) << " conversation type does not match with outgoing";
         ASSERT_EQ(max.config.uri(), evt.remoteAddress) << " remote-address starcode does not match";
      }

      assertConversationMediaChanged(alice, aliceCallToConf, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCallToConf, ConversationState_Connected);
      assertConversationEnded(alice, aliceCall2, ConversationEndReason_UserTerminatedLocally);
      alice.conversationEvents->clearCommands();
      alice.accountEvents->clearCommands();
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCallToConf, true, false);

      assertSuccess(alice.conversation->end(aliceCallToConf));
      assertConversationEnded_time(alice, aliceCallToConf, ConversationEndReason_UserTerminatedLocally, 40000);

      // expect registration refresh after network change during a call
      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall;

      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));

      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendOnly);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_ReceiveOnly);

      assertConversationEnded_time(bob, bobCall, ConversationEndReason_UserTerminatedRemotely, 60000);
   });

   auto maxEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle maxCall;

      assertNewConversationIncoming(max, &maxCall, alice.config.uri());
      assertSuccess(max.conversation->sendRingingResponse(maxCall));
      assertConversationStateChanged(max, maxCall, ConversationState_LocalRinging);
      assertSuccess(max.conversation->accept(maxCall));

      assertConversationMediaChanged(max, maxCall, MediaDirection_SendReceive);
      assertConversationStateChanged(max, maxCall, ConversationState_Connected);

      assertConversationEnded_time(max, maxCall, ConversationEndReason_UserTerminatedRemotely, 60000);
   });

   auto confEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle confCall;
      assertNewConversationIncoming(conf, &confCall, alice.config.uri());
      assertSuccess(conf.conversation->accept(confCall));

      assertConversationMediaChanged(conf, confCall, MediaDirection_SendReceive);
      assertConversationStateChanged(conf, confCall, ConversationState_Connected);

      assertConversationEnded_time(conf, confCall, ConversationEndReason_UserTerminatedRemotely, 30000);
   });

   waitFor4Ms(aliceEvents, bobEvents, maxEvents, confEvents, std::chrono::milliseconds(120000));
}

TEST_F(NetworkChangeTest, NetworkChangeHandoverUsingStarcodeMultipleTimes)
{
   std::string starcode = "*111";

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.useRport = false;
   alice.enable();
   TestAccount bob("bob");
   TestAccount conf(starcode.c_str());

   SipAccountManagerInternal* accountMgr = dynamic_cast<SipAccountManagerInternal*>(alice.account);
   ASSERT_TRUE(accountMgr != NULL);
   accountMgr->setIgnoreNetworkChangeStarcodeFilter(alice.handle, false);

   SipConversationSettings settings;
   settings.networkChangeHandoverMode = NetworkChangeHandoverMode_Starcode;
   settings.networkChangeHandoverStarcode = conf.config.settings.username;

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      // Establish call between alice and bob
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->setDefaultSettings(alice.handle, settings);
      alice.conversation->addParticipant(aliceCall, bob.config.uri());

      // Reconnection may result with same binding, ignore the filter to ensure that starcode handling is triggered
      SipConversationManagerInternal* convMgr = dynamic_cast<SipConversationManagerInternal*>(alice.conversation);
      ASSERT_TRUE(convMgr != NULL);
      convMgr->ignoreBindingFilterForStarcodeHandover(aliceCall);

      alice.conversation->start(aliceCall);

      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCall, true, false);

      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> wwan;
      wwan.insert("*******");
      alice.network->setMockInterfaces(wwan);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      // App receives the NewConversationEvent event for the starcode dialog, resulting from the network change
      SipConversationHandle aliceCallToConf1 = 0;
      {
         NewConversationEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onNewConversation", 15000, AlwaysTruePred(), aliceCallToConf1, evt)) << " missed outgoing call event";
         ASSERT_EQ(aliceCall, evt.conversationToReplace) << " replaced conversation handle does not match";
         ASSERT_EQ(ConversationType_OutgoingNetworkChangeHandover, evt.conversationType) << " conversation type does not match with outgoing";
         ASSERT_EQ(bob.config.uri(), evt.remoteAddress) << " remote-address starcode does not match";
      }

      convMgr->ignoreBindingFilterForStarcodeHandover(aliceCallToConf1);

      assertConversationMediaChanged(alice, aliceCallToConf1, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCallToConf1, ConversationState_Connected);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
      alice.conversationEvents->clearCommands();
      alice.accountEvents->clearCommands();
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCallToConf1, true, false);

      alice.network->setNetworkTransport(NetworkTransport::TransportWiFi);
      std::set<resip::Data> wifi;
      wifi.insert("*******");
      alice.network->setMockInterfaces(wifi);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      // App receives the NewConversationEvent event for the starcode dialog, resulting from the network change
      SipConversationHandle aliceCallToConf2 = 0;
      {
         NewConversationEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onNewConversation", 15000, AlwaysTruePred(), aliceCallToConf2, evt)) << " missed outgoing call event";
         ASSERT_EQ(aliceCallToConf1, evt.conversationToReplace) << " replaced conversation handle does not match";
         ASSERT_NE(evt.conversationToReplace, aliceCallToConf2);
         ASSERT_EQ(ConversationType_OutgoingNetworkChangeHandover, evt.conversationType) << " conversation type does not match with outgoing";
         ASSERT_EQ(bob.config.uri(), evt.remoteAddress) << " remote-address starcode does not match";
      }

      convMgr->ignoreBindingFilterForStarcodeHandover(aliceCallToConf2);
      assertConversationMediaChanged(alice, aliceCallToConf2, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCallToConf2, ConversationState_Connected);
      assertConversationEnded(alice, aliceCallToConf1, ConversationEndReason_UserTerminatedLocally);
      alice.conversationEvents->clearCommands();
      alice.accountEvents->clearCommands();
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCallToConf2, true, false);

      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> wwan2;
      wwan2.insert("*******");
      alice.network->setMockInterfaces(wwan2);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      // App receives the NewConversationEvent event for the starcode dialog, resulting from the network change
      SipConversationHandle aliceCallToConf3 = 0;
      {
         NewConversationEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onNewConversation", 15000, AlwaysTruePred(), aliceCallToConf3, evt)) << " missed outgoing call event";
         ASSERT_EQ(aliceCallToConf2, evt.conversationToReplace) << " replaced conversation handle does not match";
         ASSERT_NE(evt.conversationToReplace, aliceCallToConf3);
         ASSERT_EQ(ConversationType_OutgoingNetworkChangeHandover, evt.conversationType) << " conversation type does not match with outgoing";
         ASSERT_EQ(bob.config.uri(), evt.remoteAddress) << " remote-address starcode does not match";
      }

      convMgr->ignoreBindingFilterForStarcodeHandover(aliceCallToConf3);
      assertConversationMediaChanged(alice, aliceCallToConf3, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCallToConf3, ConversationState_Connected);
      assertConversationEnded(alice, aliceCallToConf2, ConversationEndReason_UserTerminatedLocally);
      alice.conversationEvents->clearCommands();
      alice.accountEvents->clearCommands();
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCallToConf3, true, false);

      assertSuccess(alice.conversation->end(aliceCallToConf3));
      assertConversationEnded(alice, aliceCallToConf3, ConversationEndReason_UserTerminatedLocally);

      // expect registration refresh after network change during a call
      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall;

      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));

      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      assertConversationEnded_time(bob, bobCall, ConversationEndReason_UserTerminatedRemotely, 60000);
   });

   auto confEvents = std::async(std::launch::async, [&] ()
   {
      std::this_thread::sleep_for(std::chrono::milliseconds(15000));
      SipConversationHandle confCall1;
      assertNewConversationIncoming(conf, &confCall1, alice.config.uri());
      assertSuccess(conf.conversation->accept(confCall1));

      assertConversationMediaChanged(conf, confCall1, MediaDirection_SendReceive);
      assertConversationStateChanged(conf, confCall1, ConversationState_Connected);

      SipConversationHandle confCall2;
      assertNewConversationIncoming(conf, &confCall2, alice.config.uri());
      assertSuccess(conf.conversation->accept(confCall2));

      assertConversationMediaChanged(conf, confCall2, MediaDirection_SendReceive);
      assertConversationStateChanged(conf, confCall2, ConversationState_Connected);

      SipConversationHandle confCall3;
      assertNewConversationIncoming(conf, &confCall3, alice.config.uri());
      assertSuccess(conf.conversation->accept(confCall3));

      assertConversationMediaChanged(conf, confCall3, MediaDirection_SendReceive);
      assertConversationStateChanged(conf, confCall3, ConversationState_Connected);

      assertConversationEnded_time(conf, confCall1, ConversationEndReason_UserTerminatedRemotely, 30000);
      assertConversationEnded_time(conf, confCall2, ConversationEndReason_UserTerminatedRemotely, 30000);
      assertConversationEnded_time(conf, confCall3, ConversationEndReason_UserTerminatedRemotely, 30000);
   });

   waitFor3(aliceEvents, bobEvents, confEvents);
}

TEST_F(NetworkChangeTest, NetworkChangeHandoverUsingStarcodeWithLocalHold)
{
   std::string starcode = "*111";

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.useRport = false;
   alice.enable();
   TestAccount bob("bob");
   TestAccount conf(starcode.c_str());

   SipAccountManagerInternal* accountMgr = dynamic_cast<SipAccountManagerInternal*>(alice.account);
   ASSERT_TRUE(accountMgr != NULL);
   accountMgr->setIgnoreNetworkChangeStarcodeFilter(alice.handle, false);

   SipConversationSettings settings;
   settings.networkChangeHandoverMode = NetworkChangeHandoverMode_Starcode;
   settings.networkChangeHandoverStarcode = conf.config.settings.username;

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      // Establish call between alice and bob
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->setDefaultSettings(alice.handle, settings);
      alice.conversation->addParticipant(aliceCall, bob.config.uri());

      // Reconnection may result with same binding, ignore the filter to ensure that starcode handling is triggered
      SipConversationManagerInternal* convMgr = dynamic_cast<SipConversationManagerInternal*>(alice.conversation);
      ASSERT_TRUE(convMgr != NULL);
      convMgr->ignoreBindingFilterForStarcodeHandover(aliceCall);

      alice.conversation->start(aliceCall);

      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCall, true, false);

      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> wwan;
      wwan.insert("*******");
      alice.network->setMockInterfaces(wwan);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      // App receives the NewConversationEvent event for the starcode dialog, resulting from the network change
      SipConversationHandle aliceCallToConf = 0;
      {
         NewConversationEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onNewConversation", 15000, AlwaysTruePred(), aliceCallToConf, evt)) << " missed outgoing call event";
         ASSERT_EQ(aliceCall, evt.conversationToReplace) << " replaced conversation handle does not match";
         ASSERT_EQ(ConversationType_OutgoingNetworkChangeHandover, evt.conversationType) << " conversation type does not match with outgoing";
         ASSERT_EQ(bob.config.uri(), evt.remoteAddress) << " remote-address starcode does not match";
      }

      assertConversationMediaChanged(alice, aliceCallToConf, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCallToConf, ConversationState_Connected);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
      alice.conversationEvents->clearCommands();
      alice.accountEvents->clearCommands();
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCallToConf, true, false);

      assertSuccess(alice.conversation->hold(aliceCallToConf));
      assertConversationMediaChanged_ex(alice, aliceCallToConf, [](const ConversationMediaChangedEvent& evt)
      {
         ASSERT_TRUE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendOnly, evt.localMediaInfo[0].mediaDirection);
      });
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertSuccess(alice.conversation->unhold(aliceCallToConf));
      assertConversationMediaChanged_ex(alice, aliceCallToConf, [](const ConversationMediaChangedEvent& evt)
      {
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCallToConf, true, false);

      assertSuccess(alice.conversation->end(aliceCallToConf));
      assertConversationEnded_time(alice, aliceCallToConf, ConversationEndReason_UserTerminatedLocally, 40000);

      // expect registration refresh after network change during a call
      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall = 0;

      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));

      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      assertConversationEnded_time(bob, bobCall, ConversationEndReason_UserTerminatedRemotely, 40000);
   });

   auto confEvents = std::async(std::launch::async, [&] ()
   {
      std::this_thread::sleep_for(std::chrono::milliseconds(15000));
      SipConversationHandle confCall = 0;
      assertNewConversationIncoming(conf, &confCall, alice.config.uri());
      assertSuccess(conf.conversation->accept(confCall));

      assertConversationMediaChanged(conf, confCall, MediaDirection_SendReceive);
      assertConversationStateChanged(conf, confCall, ConversationState_Connected);

      assertConversationMediaChangeRequest(conf, confCall, MediaDirection_SendOnly);
      assertSuccess(conf.conversation->accept(confCall));
      assertConversationMediaChanged_ex(conf, confCall, [](const ConversationMediaChangedEvent& evt)
      {
         ASSERT_FALSE(evt.localHold);
         ASSERT_TRUE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_ReceiveOnly, evt.localMediaInfo[0].mediaDirection);
      });
      assertConversationMediaChangeRequest(conf, confCall, MediaDirection_SendReceive);
      assertSuccess(conf.conversation->accept(confCall));
      assertConversationMediaChanged_ex(conf, confCall, [](const ConversationMediaChangedEvent& evt)
      {
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });

      assertConversationEnded_time(conf, confCall, ConversationEndReason_UserTerminatedRemotely, 30000);
   });

   waitFor3(aliceEvents, bobEvents, confEvents);
}

TEST_F(NetworkChangeTest, NetworkChangeHandoverUsingStarcodeWithRemoteHold)
{
   std::string starcode = "*111";

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.useRport = false;
   alice.enable();
   TestAccount bob("bob");
   TestAccount conf(starcode.c_str());

   SipAccountManagerInternal* accountMgr = dynamic_cast<SipAccountManagerInternal*>(alice.account);
   ASSERT_TRUE(accountMgr != NULL);
   accountMgr->setIgnoreNetworkChangeStarcodeFilter(alice.handle, false);

   SipConversationSettings settings;
   settings.networkChangeHandoverMode = NetworkChangeHandoverMode_Starcode;
   settings.networkChangeHandoverStarcode = conf.config.settings.username;

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      // Establish call between alice and bob
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->setDefaultSettings(alice.handle, settings);
      alice.conversation->addParticipant(aliceCall, bob.config.uri());

      // Reconnection may result with same binding, ignore the filter to ensure that starcode handling is triggered
      SipConversationManagerInternal* convMgr = dynamic_cast<SipConversationManagerInternal*>(alice.conversation);
      ASSERT_TRUE(convMgr != NULL);
      convMgr->ignoreBindingFilterForStarcodeHandover(aliceCall);

      alice.conversation->start(aliceCall);

      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCall, true, false);

      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> wwan;
      wwan.insert("*******");
      alice.network->setMockInterfaces(wwan);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      // App receives the NewConversationEvent event for the starcode dialog, resulting from the network change
      SipConversationHandle aliceCallToConf = 0;
      {
         NewConversationEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onNewConversation", 15000, AlwaysTruePred(), aliceCallToConf, evt)) << " missed outgoing call event";
         ASSERT_EQ(aliceCall, evt.conversationToReplace) << " replaced conversation handle does not match";
         ASSERT_EQ(ConversationType_OutgoingNetworkChangeHandover, evt.conversationType) << " conversation type does not match with outgoing";
         ASSERT_EQ(bob.config.uri(), evt.remoteAddress) << " remote-address starcode does not match";
      }

      assertConversationMediaChanged(alice, aliceCallToConf, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCallToConf, ConversationState_Connected);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
      alice.conversationEvents->clearCommands();
      alice.accountEvents->clearCommands();
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCallToConf, true, false);

      assertConversationMediaChangeRequest(alice, aliceCallToConf, MediaDirection_SendOnly);
      assertSuccess(alice.conversation->accept(aliceCallToConf));
      assertConversationMediaChanged_ex(alice, aliceCallToConf, [](const ConversationMediaChangedEvent& evt)
      {
         ASSERT_FALSE(evt.localHold);
         ASSERT_TRUE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_ReceiveOnly, evt.localMediaInfo[0].mediaDirection);
      });
      assertConversationMediaChangeRequest(alice, aliceCallToConf, MediaDirection_SendReceive);
      assertSuccess(alice.conversation->accept(aliceCallToConf));
      assertConversationMediaChanged_ex(alice, aliceCallToConf, [](const ConversationMediaChangedEvent& evt)
      {
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCallToConf, true, false);

      assertSuccess(alice.conversation->end(aliceCallToConf));
      assertConversationEnded_time(alice, aliceCallToConf, ConversationEndReason_UserTerminatedLocally, 40000);

      // expect registration refresh after network change during a call
      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall = 0;

      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));

      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      assertConversationEnded_time(bob, bobCall, ConversationEndReason_UserTerminatedRemotely, 40000);
   });

   auto confEvents = std::async(std::launch::async, [&] ()
   {
      std::this_thread::sleep_for(std::chrono::milliseconds(15000));
      SipConversationHandle confCall = 0;
      assertNewConversationIncoming(conf, &confCall, alice.config.uri());
      assertSuccess(conf.conversation->accept(confCall));

      assertConversationMediaChanged(conf, confCall, MediaDirection_SendReceive);
      assertConversationStateChanged(conf, confCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(3000));

      assertSuccess(conf.conversation->hold(confCall));
      assertConversationMediaChanged_ex(conf, confCall, [](const ConversationMediaChangedEvent& evt)
      {
         ASSERT_TRUE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendOnly, evt.localMediaInfo[0].mediaDirection);
      });
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertSuccess(conf.conversation->unhold(confCall));
      assertConversationMediaChanged_ex(conf, confCall, [](const ConversationMediaChangedEvent& evt)
      {
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });

      assertConversationEnded_time(conf, confCall, ConversationEndReason_UserTerminatedRemotely, 30000);
   });

   waitFor3(aliceEvents, bobEvents, confEvents);
}

TEST_F(NetworkChangeTest, NetworkChangeHandoverUsingStarcodeWithLocalTransfer)
{
   std::string starcode = "*111";

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.useRport = false;
   alice.enable();
   TestAccount bob("bob");
   TestAccount conf(starcode.c_str());
   TestAccount max("max");

   SipAccountManagerInternal* accountMgr = dynamic_cast<SipAccountManagerInternal*>(alice.account);
   ASSERT_TRUE(accountMgr != NULL);
   accountMgr->setIgnoreNetworkChangeStarcodeFilter(alice.handle, false);

   SipConversationSettings settings;
   settings.networkChangeHandoverMode = NetworkChangeHandoverMode_Starcode;
   settings.networkChangeHandoverStarcode = conf.config.settings.username;

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      // Establish call between alice and bob
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->setDefaultSettings(alice.handle, settings);
      alice.conversation->addParticipant(aliceCall, bob.config.uri());

      // Reconnection may result with same binding, ignore the filter to ensure that starcode handling is triggered
      SipConversationManagerInternal* convMgr = dynamic_cast<SipConversationManagerInternal*>(alice.conversation);
      ASSERT_TRUE(convMgr != NULL);
      convMgr->ignoreBindingFilterForStarcodeHandover(aliceCall);

      alice.conversation->start(aliceCall);

      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCall, true, false);

      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> wwan;
      wwan.insert("*******");
      alice.network->setMockInterfaces(wwan);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      // App receives the NewConversationEvent event for the starcode dialog, resulting from the network change
      SipConversationHandle aliceCallToConf = 0;
      {
         NewConversationEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onNewConversation", 15000, AlwaysTruePred(), aliceCallToConf, evt)) << " missed outgoing call event";
         ASSERT_EQ(aliceCall, evt.conversationToReplace) << " replaced conversation handle does not match";
         ASSERT_EQ(ConversationType_OutgoingNetworkChangeHandover, evt.conversationType) << " conversation type does not match with outgoing";
         ASSERT_EQ(bob.config.uri(), evt.remoteAddress) << " remote-address starcode does not match";
      }

      assertConversationMediaChanged(alice, aliceCallToConf, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCallToConf, ConversationState_Connected);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
      alice.conversationEvents->clearCommands();
      alice.accountEvents->clearCommands();
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCallToConf, true, false);

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      assertSuccess(alice.conversation->transfer(aliceCallToConf, max.config.uri()));
      assertTransferTryingRingingConnected(alice, aliceCallToConf);

      assertConversationEnded_time(alice, aliceCallToConf, ConversationEndReason_UserTerminatedLocally, 40000);

      // expect registration refresh after network change during a call
      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall = 0;

      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));

      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      assertConversationEnded_time(bob, bobCall, ConversationEndReason_UserTerminatedRemotely, 40000);
   });

   auto confEvents = std::async(std::launch::async, [&] ()
   {
      std::this_thread::sleep_for(std::chrono::milliseconds(15000));
      SipConversationHandle confCall = 0;
      assertNewConversationIncoming(conf, &confCall, alice.config.uri());
      assertSuccess(conf.conversation->accept(confCall));

      assertConversationMediaChanged(conf, confCall, MediaDirection_SendReceive);
      assertConversationStateChanged(conf, confCall, ConversationState_Connected);

      SipConversationHandle aliceCallToMax = 0;
      assertTransferRequest_ex(conf, confCall, &aliceCallToMax, [&](const TransferRequestEvent& evt)
      {
         ASSERT_EQ(max.config.uri(), evt.transferTargetAddress);
         ASSERT_EQ("", evt.transferTargetDisplayName);
         ASSERT_NE(0, evt.transferTargetConversation);
      });

      assertSuccess(conf.conversation->acceptIncomingTransferRequest(aliceCallToMax));

      assertNewConversationOutgoing(conf, aliceCallToMax, max.config.uri());
      assertConversationStateChanged(conf, aliceCallToMax, ConversationState_RemoteRinging);
      assertConversationMediaChanged(conf, aliceCallToMax, MediaDirection_SendReceive);
      assertConversationStateChanged(conf, aliceCallToMax, ConversationState_Connected);

      assertConversationEnded(conf, confCall, ConversationEndReason_UserTerminatedRemotely);
      assertConversationEnded(conf, aliceCallToMax, ConversationEndReason_UserTerminatedRemotely);
   });

   auto maxEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle maxCall = 0;
      assertNewConversationIncoming(max, &maxCall, conf.config.uri());
      assertSuccess(max.conversation->sendRingingResponse(maxCall));
      assertConversationStateChanged(max, maxCall, ConversationState_LocalRinging);
      assertSuccess(max.conversation->accept(maxCall));

      assertConversationMediaChanged(max, maxCall, MediaDirection_SendReceive);
      assertConversationStateChanged(max, maxCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(max, maxCall, true, false);

      assertSuccess(max.conversation->end(maxCall));
      assertConversationEnded(max, maxCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor4Ms(aliceEvents, bobEvents, confEvents, maxEvents, std::chrono::milliseconds(90000));
}

TEST_F(NetworkChangeTest, NetworkChangeHandoverUsingStarcodeWithRemoteTransfer)
{
   std::string starcode = "*111";

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.useRport = false;
   alice.enable();
   TestAccount bob("bob");
   TestAccount conf(starcode.c_str());
   TestAccount max("max");

   SipAccountManagerInternal* accountMgr = dynamic_cast<SipAccountManagerInternal*>(alice.account);
   ASSERT_TRUE(accountMgr != NULL);
   accountMgr->setIgnoreNetworkChangeStarcodeFilter(alice.handle, false);

   SipConversationSettings settings;
   settings.networkChangeHandoverMode = NetworkChangeHandoverMode_Starcode;
   settings.networkChangeHandoverStarcode = conf.config.settings.username;

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      // Establish call between alice and bob
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->setDefaultSettings(alice.handle, settings);
      alice.conversation->addParticipant(aliceCall, bob.config.uri());

      // Reconnection may result with same binding, ignore the filter to ensure that starcode handling is triggered
      SipConversationManagerInternal* convMgr = dynamic_cast<SipConversationManagerInternal*>(alice.conversation);
      ASSERT_TRUE(convMgr != NULL);
      convMgr->ignoreBindingFilterForStarcodeHandover(aliceCall);

      alice.conversation->start(aliceCall);

      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCall, true, false);

      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> wwan;
      wwan.insert("*******");
      alice.network->setMockInterfaces(wwan);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      // App receives the NewConversationEvent event for the starcode dialog, resulting from the network change
      SipConversationHandle aliceCallToConf = 0;
      {
         NewConversationEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onNewConversation", 15000, AlwaysTruePred(), aliceCallToConf, evt)) << " missed outgoing call event";
         ASSERT_EQ(aliceCall, evt.conversationToReplace) << " replaced conversation handle does not match";
         ASSERT_EQ(ConversationType_OutgoingNetworkChangeHandover, evt.conversationType) << " conversation type does not match with outgoing";
         ASSERT_EQ(bob.config.uri(), evt.remoteAddress) << " remote-address starcode does not match";
      }

      assertConversationMediaChanged(alice, aliceCallToConf, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCallToConf, ConversationState_Connected);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
      alice.conversationEvents->clearCommands();
      alice.accountEvents->clearCommands();
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCallToConf, true, false);

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      SipConversationHandle aliceCallToMax = 0;
      assertTransferRequest_ex(alice, aliceCallToConf, &aliceCallToMax, [&](const TransferRequestEvent& evt)
      {
         ASSERT_EQ(max.config.uri(), evt.transferTargetAddress);
         ASSERT_EQ("", evt.transferTargetDisplayName);
         ASSERT_NE(0, evt.transferTargetConversation);
      });

      assertSuccess(alice.conversation->acceptIncomingTransferRequest(aliceCallToMax));

      assertNewConversationOutgoing(alice, aliceCallToMax, max.config.uri());
      assertConversationStateChanged(alice, aliceCallToMax, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCallToMax, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCallToMax, ConversationState_Connected);

      assertConversationEnded(alice, aliceCallToConf, ConversationEndReason_UserTerminatedRemotely);
      assertConversationEnded(alice, aliceCallToMax, ConversationEndReason_UserTerminatedRemotely);

      // expect registration refresh after network change during a call
      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall = 0;

      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));

      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      assertConversationEnded_time(bob, bobCall, ConversationEndReason_UserTerminatedRemotely, 40000);
   });

   auto confEvents = std::async(std::launch::async, [&] ()
   {
      std::this_thread::sleep_for(std::chrono::milliseconds(15000));
      SipConversationHandle confCall = 0;
      assertNewConversationIncoming(conf, &confCall, alice.config.uri());
      assertSuccess(conf.conversation->accept(confCall));

      assertConversationMediaChanged(conf, confCall, MediaDirection_SendReceive);
      assertConversationStateChanged(conf, confCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertSuccess(conf.conversation->transfer(confCall, max.config.uri()));
      assertTransferTryingRingingConnected(conf, confCall);

      assertConversationEnded_time(conf, confCall, ConversationEndReason_UserTerminatedLocally, 40000);
   });

   auto maxEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle maxCall = 0;
      assertNewConversationIncoming_time(max, &maxCall, alice.config.uri(), 60000);
      assertSuccess(max.conversation->sendRingingResponse(maxCall));
      assertConversationStateChanged(max, maxCall, ConversationState_LocalRinging);
      assertSuccess(max.conversation->accept(maxCall));

      assertConversationMediaChanged(max, maxCall, MediaDirection_SendReceive);
      assertConversationStateChanged(max, maxCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(max, maxCall, true, false);

      assertSuccess(max.conversation->end(maxCall));
      assertConversationEnded(max, maxCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor4Ms(aliceEvents, bobEvents, confEvents, maxEvents, std::chrono::milliseconds(90000));
}

TEST_F(NetworkChangeTest, NetworkChangeHandoverUsingStarcodeWithAttendedTransfer)
{
   std::string starcode = "*111";

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.useRport = false;
   alice.enable();
   TestAccount bob("bob");
   TestAccount conf(starcode.c_str());
   TestAccount max("max");

   SipAccountManagerInternal* accountMgr = dynamic_cast<SipAccountManagerInternal*>(alice.account);
   ASSERT_TRUE(accountMgr != NULL);
   accountMgr->setIgnoreNetworkChangeStarcodeFilter(alice.handle, false);

   SipConversationSettings settings;
   settings.networkChangeHandoverMode = NetworkChangeHandoverMode_Starcode;
   settings.networkChangeHandoverStarcode = conf.config.settings.username;

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      // Establish call between alice and bob
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->setDefaultSettings(alice.handle, settings);
      alice.conversation->addParticipant(aliceCall, bob.config.uri());

      // Reconnection may result with same binding, ignore the filter to ensure that starcode handling is triggered
      SipConversationManagerInternal* convMgr = dynamic_cast<SipConversationManagerInternal*>(alice.conversation);
      ASSERT_TRUE(convMgr != NULL);
      convMgr->ignoreBindingFilterForStarcodeHandover(aliceCall);

      alice.conversation->start(aliceCall);

      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCall, true, false);

      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> wwan;
      wwan.insert("*******");
      alice.network->setMockInterfaces(wwan);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      // App receives the NewConversationEvent event for the starcode dialog, resulting from the network change
      SipConversationHandle aliceCallToConf = 0;
      {
         NewConversationEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onNewConversation", 15000, AlwaysTruePred(), aliceCallToConf, evt)) << " missed outgoing call event";
         ASSERT_EQ(aliceCall, evt.conversationToReplace) << " replaced conversation handle does not match";
         ASSERT_EQ(ConversationType_OutgoingNetworkChangeHandover, evt.conversationType) << " conversation type does not match with outgoing";
         ASSERT_EQ(bob.config.uri(), evt.remoteAddress) << " remote-address starcode does not match";
      }

      assertConversationMediaChanged(alice, aliceCallToConf, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCallToConf, ConversationState_Connected);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
      alice.conversationEvents->clearCommands();
      alice.accountEvents->clearCommands();
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCallToConf, true, false);
 
      assertSuccess(alice.conversation->hold(aliceCallToConf));
      assertConversationMediaChanged(alice, aliceCallToConf, MediaDirection_SendOnly);

      SipConversationHandle aliceCallToMax = alice.conversation->createConversation(alice.handle);
      alice.conversation->addParticipant(aliceCallToMax, max.config.uri());
      alice.conversation->start(aliceCallToMax);

      assertNewConversationOutgoing(alice, aliceCallToMax, max.config.uri());
      assertConversationStateChanged(alice, aliceCallToMax, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCallToMax, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCallToMax, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      assertSuccess(alice.conversation->transfer(aliceCallToMax, aliceCallToConf));
      assertTransferTryingRingingConnected(alice, aliceCallToConf);
      assertSuccess(alice.conversation->end(aliceCallToConf));
      assertConversationEnded(alice, aliceCallToConf, ConversationEndReason_UserTerminatedLocally);
      assertConversationEnded(alice, aliceCallToMax, ConversationEndReason_UserTerminatedRemotely);

      // expect registration refresh after network change during a call
      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall = 0;

      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));

      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      assertConversationEnded_time(bob, bobCall, ConversationEndReason_UserTerminatedRemotely, 40000);
   });

   auto confEvents = std::async(std::launch::async, [&] ()
   {
      std::this_thread::sleep_for(std::chrono::milliseconds(15000));
      SipConversationHandle confCall = 0;
      assertNewConversationIncoming(conf, &confCall, alice.config.uri());
      assertSuccess(conf.conversation->accept(confCall));

      assertConversationMediaChanged(conf, confCall, MediaDirection_SendReceive);
      assertConversationStateChanged(conf, confCall, ConversationState_Connected);

      assertConversationMediaChangeRequest(conf, confCall, MediaDirection_SendOnly);
      assertSuccess(conf.conversation->accept(confCall));
      assertConversationMediaChanged(conf, confCall, MediaDirection_ReceiveOnly);

      SipConversationHandle confCallToMax = 0;
      assertTransferRequest_ex(conf, confCall, &confCallToMax, [&](const TransferRequestEvent& evt)
      {
         ASSERT_EQ(max.config.uri(), evt.transferTargetAddress);
         ASSERT_EQ(max.config.name, evt.transferTargetDisplayName);
         ASSERT_NE(0, evt.transferTargetConversation);
      });
      assertSuccess(conf.conversation->acceptIncomingTransferRequest(confCallToMax));

      assertNewConversationOutgoing(conf, confCallToMax, max.config.uri());
      assertConversationStateChanged(conf, confCallToMax, ConversationState_RemoteRinging);
      assertConversationMediaChanged(conf, confCallToMax, MediaDirection_SendReceive);
      assertConversationStateChanged(conf, confCallToMax, ConversationState_Connected);
      assertConversationEnded(conf, confCallToMax, ConversationEndReason_UserTerminatedRemotely);
      assertConversationEnded(conf, confCall, ConversationEndReason_UserTerminatedRemotely);
   });
   
   auto maxEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle maxCall = 0;
      assertNewConversationIncoming_time(max, &maxCall, alice.config.uri(), 60000);
      assertSuccess(max.conversation->sendRingingResponse(maxCall));
      assertConversationStateChanged(max, maxCall, ConversationState_LocalRinging);
      assertSuccess(max.conversation->accept(maxCall));

      assertConversationMediaChanged(max, maxCall, MediaDirection_SendReceive);
      assertConversationStateChanged(max, maxCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(max, maxCall, true, false);

      SipConversationHandle maxCallFromConf = 0;
      assertNewConversationIncomingTransfer_ex(max, &maxCallFromConf, conf.config.uri(), [&](const NewConversationEvent& evt)
      {
         ASSERT_EQ(conf.config.name, evt.remoteDisplayName);
         ASSERT_EQ(maxCall, evt.conversationToReplace);
      });

      assertSuccess(max.conversation->sendRingingResponse(maxCallFromConf));
      assertConversationStateChanged(max, maxCallFromConf, ConversationState_LocalRinging);
      assertSuccess(max.conversation->accept(maxCallFromConf));
      assertConversationMediaChanged(max, maxCallFromConf, MediaDirection_SendReceive);
      assertConversationStateChanged(max, maxCallFromConf, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(max, maxCallFromConf, true, false);

      assertSuccess(max.conversation->end(maxCallFromConf));
      assertConversationEnded(max, maxCallFromConf, ConversationEndReason_UserTerminatedLocally);
      assertConversationEnded(max, maxCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor4Ms(aliceEvents, bobEvents, confEvents, maxEvents, std::chrono::milliseconds(90000));
}


// #if (defined(__linux__) && !defined(ANDROID)) || defined(__APPLE__)
#ifndef __linux__

TEST_F(NetworkChangeTest, NetworkChangeHandoverUsingStarcodeOnStrettoTunnelCall)
{
   ReproHolder::destroyInstance();

   repro::ReproRunner* reproRunner = runRepro("repro_force_recordroute.config");

   std::string starcode = "*111";
   TestAccount conf(starcode.c_str(), Account_NoInit);
   conf.init();
   conf.config.settings.minimumRegistrationIntervalSeconds = 300;
   conf.config.settings.maximumRegistrationIntervalSeconds = 300;
   conf.enable(true);

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.useRegistrar = false;
   alice.config.settings.tunnelConfig.useTunnel = true;
   alice.config.settings.sipTransportType = SipAccountTransport_TCP;
   alice.config.settings.tunnelConfig.tunnelType = TunnelType_StrettoTunnel;
   // repro.config has been adjsuted to WSS listen on port 7061
   alice.config.settings.tunnelConfig.strettoTunnelURL = "wss://127.0.0.1:7061";
   alice.config.settings.ignoreCertVerification = true;
   alice.config.settings.tunnelConfig.strettoTunnelSkipHandshake = true;
   alice.config.settings.sourceAddress = "127.0.0.1";

   alice.config.settings.minimumRegistrationIntervalSeconds = 300;
   alice.config.settings.maximumRegistrationIntervalSeconds = 300;
   alice.config.settings.enableNat64Support = true;
   alice.config.settings.ipVersion = IpVersion_Auto;
   alice.config.settings.useInstanceId = true;
   alice.config.settings.useRport = false;
   alice.config.settings.useRinstance = true;
   alice.config.settings.alwaysRouteViaOutboundProxy = false;
   alice.config.settings.sslVersion = SSL_HIGHEST;
   alice.config.settings.sessionTimerMode = SipAccountSessionTimerMode_Inactive;
   alice.config.settings.tunnelConfig.transportType = TunnelTransport_TCP;

   alice.init();

   SipAccountManagerInternal* accountMgr = dynamic_cast<SipAccountManagerInternal*>(alice.account);
   ASSERT_TRUE(accountMgr != NULL);
   accountMgr->setIgnoreNetworkChangeStarcodeFilter(alice.handle, false);

   SipConversationSettings settings;
   settings.networkChangeHandoverMode = NetworkChangeHandoverMode_Starcode;
   settings.networkChangeHandoverStarcode = conf.config.settings.username;

   alice.enable();

   TestAccount bob("bob");

   // Initial network
   ASSERT_EQ(alice.network->networkTransport(), NetworkTransport::TransportWiFi);
   ASSERT_EQ(bob.network->networkTransport(), NetworkTransport::TransportWiFi);

   NetworkChangePollingConfig config(200, 3000, 3000);
   alice.network->setPollingConfig(config);
   bob.network->setPollingConfig(config);
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   // Alice calls Bob then Bob hangs up
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->setDefaultSettings(alice.handle, settings);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());

   alice.conversation->start(aliceCall);
   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      std::set<resip::Data> nonet;
      std::set<resip::Data> wifi;
      wifi.insert("*******");

      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCall, true, false);

      // Disable network
      if (TestEnvironmentConfig::dockerContainerized())
      {
         ASSERT_EQ(0, test::NetworkUtils::setDockerContainerNetworkEnabled(false));
         ASSERT_EQ(0, test::NetworkUtils::shutdownTcpSockets());
      }
      alice.network->setNetworkTransport(NetworkTransport::TransportNone);
      alice.network->setMockInterfaces(nonet);

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      alice.accountEvents->clearCommands();

      // Enable WiFi network
      if (TestEnvironmentConfig::dockerContainerized())
      {
         ASSERT_EQ(0, test::NetworkUtils::setDockerContainerNetworkEnabled(true));
      }
      alice.network->setNetworkTransport(NetworkTransport::TransportWiFi);
      alice.network->setMockInterfaces(wifi);

      assertAccountRefreshing(alice);
      assertAccountWaitingToRegister(alice);
      assertAccountRegistering(alice);
      assertAccountRegistered(alice);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      // App should not receive the NewConversationEvent event for the starcode dialog due to the tunnel configuration
      SipConversationHandle aliceCallToConf = 0;
      {
         NewConversationEvent evt;
         ASSERT_FALSE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onNewConversation", 15000, AlwaysTruePred(), aliceCallToConf, evt));
      }

      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      alice.conversationEvents->clearCommands();
      alice.accountEvents->clearCommands();
      assertMediaFlowing(alice, aliceCall, true, false);

      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      bob.conversation->sendRingingResponse(bobCall);
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(bob, bobCall, true, false);

      assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
      assertSuccess(bob.conversation->accept(bobCall));

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(bob, bobCall, true, false);

      assertConversationEnded_time(bob, bobCall, ConversationEndReason_UserTerminatedRemotely, 45000);
   });

   auto confEvents = std::async(std::launch::async, [&] ()
   {
      // we should NOT receive any starcode related call requests
      SipConversationHandle h;
      NewConversationEvent evt;
      ASSERT_FALSE(cpcExpectEvent(conf.conversationEvents, "SipConversationHandler::onNewConversation", 45000, AlwaysTruePred(), h, evt));
   });

   waitFor3Ms(aliceEvents, bobEvents, confEvents, std::chrono::minutes(120000));

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   bob.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   conf.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   runOriginalRepro(reproRunner);
   std::this_thread::sleep_for(std::chrono::milliseconds(3000));
}

#endif // !__linux__


TEST_F(NetworkChangeTest, NetworkChangeHandoverUsingStarcodeNoRegistrarVerifyCallsNotTerminated)
{
   ReproHolder::destroyInstance();

   repro::ReproRunner* reproRunner = runRepro("repro_force_recordroute.config");

   std::string starcode = "*111";

   TestAccount conf(starcode.c_str(), Account_NoInit);
   conf.config.settings.minimumRegistrationIntervalSeconds = 300;
   conf.config.settings.maximumRegistrationIntervalSeconds = 300;
   conf.enable();

   TestAccount alice("alice", Account_NoInit);
   TestAccount bob("bob", Account_NoInit);
   TestAccount max("max", Account_NoInit);

   resip::Uri aliceTarget;
   aliceTarget.user() = alice.config.settings.username;
   aliceTarget.host() = "127.0.0.1";
   aliceTarget.port() = 5055;
   
   resip::Uri bobTarget;
   bobTarget.user() = bob.config.settings.username;
   bobTarget.host() = "127.0.0.1";
   bobTarget.port() = 5057;
   
   resip::Uri maxTarget;
   maxTarget.user() = max.config.settings.username;
   maxTarget.host() = "127.0.0.1";
   maxTarget.port() = 5059;

   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.minSipPort = aliceTarget.port();
   alice.config.settings.maxSipPort = aliceTarget.port();
   alice.config.settings.useRegistrar = false;
   alice.config.settings.useRport = false;
   alice.enable();

   bob.config.settings.sipTransportType = SipAccountTransport_UDP;
   bob.config.settings.minSipPort = bobTarget.port();
   bob.config.settings.maxSipPort = bobTarget.port();
   bob.config.settings.useRegistrar = false;
   bob.config.settings.domain = "bogus.invalid";
   bob.config.settings.outboundProxy = "";
   bob.config.settings.sourceAddress = "127.0.0.1";
   bob.enable();
   
   max.config.settings.sipTransportType = SipAccountTransport_UDP;
   max.config.settings.minSipPort = maxTarget.port();
   max.config.settings.maxSipPort = maxTarget.port();
   max.config.settings.useRegistrar = false;
   max.config.settings.domain = "bogus.invalid";
   max.config.settings.outboundProxy = "";
   max.config.settings.sourceAddress = "127.0.0.1";
   max.enable();

   SipAccountManagerInternal* aliceAccountMgr = dynamic_cast<SipAccountManagerInternal*>(alice.account);
   ASSERT_TRUE(aliceAccountMgr != NULL);
   aliceAccountMgr->setIgnoreNetworkChangeStarcodeFilter(alice.handle, false);

   SipConversationSettings convSettings;
   convSettings.networkChangeHandoverMode = NetworkChangeHandoverMode_Starcode;
   convSettings.networkChangeHandoverStarcode = conf.config.settings.username;

   // Initial network
   ASSERT_EQ(alice.network->networkTransport(), NetworkTransport::TransportWiFi);
   ASSERT_EQ(bob.network->networkTransport(), NetworkTransport::TransportWiFi);
   ASSERT_EQ(max.network->networkTransport(), NetworkTransport::TransportWiFi);

   std::atomic_bool bobCallSent = false;
   std::atomic_bool networkChanged = false;

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle aliceIncomingCall1;
      assertNewConversationIncoming(alice, &aliceIncomingCall1, bob.config.uri());
      alice.conversation->sendRingingResponse(aliceIncomingCall1);
      assertConversationStateChanged(alice, aliceIncomingCall1, ConversationState_LocalRinging);

      SipConversationHandle aliceIncomingCall2;
      assertNewConversationIncoming(alice, &aliceIncomingCall2, max.config.uri());
      alice.conversation->sendRingingResponse(aliceIncomingCall2);
      assertConversationStateChanged(alice, aliceIncomingCall2, ConversationState_LocalRinging);

      alice.accountEvents->clearCommands();
      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> wwan;
      wwan.insert("*******");
      alice.network->setMockInterfaces(wwan);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);

      // As it is a no-registrar configuration, starcode handling should not be triggered, and the ringing calls should not be destroyed
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
         ASSERT_FALSE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged", __FILE__, 10000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      }

      {
         ConversationEndedEvent evt;
         ASSERT_FALSE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onConversationEnded", 5000, AlwaysTruePred(), aliceIncomingCall1, evt));
      }

      networkChanged = true;

      // Verify that the calls can be restored
      std::this_thread::sleep_for(std::chrono::milliseconds(500));
      alice.conversation->sendRingingResponse(aliceIncomingCall2);
      assertConversationStateChanged(alice, aliceIncomingCall2, ConversationState_LocalRinging);
      alice.conversation->accept(aliceIncomingCall1);
      assertConversationMediaChanged(alice, aliceIncomingCall1, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceIncomingCall1, ConversationState_Connected);

      alice.conversationEvents->clearCommands();
      alice.accountEvents->clearCommands();
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      alice.conversation->sendRingingResponse(aliceIncomingCall2);
      assertConversationStateChanged(alice, aliceIncomingCall2, ConversationState_LocalRinging);
      assertMediaFlowing(alice, aliceIncomingCall1, true, false);

      assertSuccess(alice.conversation->end(aliceIncomingCall1));
      assertConversationEnded_time(alice, aliceIncomingCall1, ConversationEndReason_UserTerminatedLocally, 45000);

      alice.conversation->accept(aliceIncomingCall2);
      assertConversationMediaChanged(alice, aliceIncomingCall2, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceIncomingCall2, ConversationState_Connected);
      
      alice.conversationEvents->clearCommands();
      alice.accountEvents->clearCommands();
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceIncomingCall2, true, false);

      assertSuccess(alice.conversation->end(aliceIncomingCall2));
      assertConversationEnded_time(alice, aliceIncomingCall2, ConversationEndReason_UserTerminatedLocally, 45000);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
      bob.conversation->setDefaultSettings(bob.handle, convSettings);
      bob.conversation->addParticipant(bobCall, aliceTarget.getAOR(true).c_str());
      bob.conversation->start(bobCall);
      assertNewConversationOutgoing(bob, bobCall, aliceTarget.getAOR(false).c_str());
      assertConversationStateChanged(bob, bobCall, ConversationState_RemoteRinging);

      bobCallSent = true;

      while (networkChanged == false)
      {
         std::this_thread::sleep_for(std::chrono::milliseconds(200));
      }

      assertConversationMediaChanged_time(bob, bobCall, MediaDirection_SendReceive, 45000);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertConversationEnded_time(bob, bobCall, ConversationEndReason_UserTerminatedRemotely, 45000);
   });

   auto maxEvents = std::async(std::launch::async, [&] ()
   {
      while (bobCallSent == false)
      {
         std::this_thread::sleep_for(std::chrono::milliseconds(500));
      }

      SipConversationHandle maxCall = max.conversation->createConversation(max.handle);
      max.conversation->setDefaultSettings(max.handle, convSettings);
      max.conversation->addParticipant(maxCall, aliceTarget.getAOR(true).c_str());
      max.conversation->start(maxCall);
      assertNewConversationOutgoing(max, maxCall, aliceTarget.getAOR(false).c_str());
      assertConversationStateChanged(max, maxCall, ConversationState_RemoteRinging);

      while (networkChanged == false)
      {
         std::this_thread::sleep_for(std::chrono::milliseconds(200));
      }

      assertConversationMediaChanged_time(max, maxCall, MediaDirection_SendReceive, 45000);
      assertConversationStateChanged(max, maxCall, ConversationState_Connected);
      assertConversationEnded_time(max, maxCall, ConversationEndReason_UserTerminatedRemotely, 45000);
   });

   auto confEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle confCall;
      NewConversationEvent evt;
      ASSERT_FALSE(conf.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onNewConversation", 45000, AlwaysTruePred(), confCall, evt));
   });

   waitFor4Ms(aliceEvents, bobEvents, maxEvents, confEvents, std::chrono::minutes(120000));

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   bob.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   max.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   conf.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   runOriginalRepro(reproRunner);
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
}

class SwisscomConversationAdornmentHandler : public SipConversationAdornmentHandler
{
public:
   std::mutex mtx;
   std::condition_variable cv;
   std::atomic<bool> mWaiting;
   TestAccount& mAccount;
   CPCAPI2::SipConversation::SipConversationManager* mConvMgr;

   SwisscomConversationAdornmentHandler(TestAccount& account) :
   mAccount(account),
   mConvMgr(account.conversation),
   mWaiting(false)
   {
   }

   virtual int onConversationAdornment(SipConversationHandle conversation, const ConversationAdornmentEvent& args)
   {
      const ConversationAdornmentInternalEvent* internal = static_cast<const ConversationAdornmentInternalEvent*>(&args);

      std::unique_lock<std::mutex> lck(mtx);
      resip::SdpContents* sdpContents = dynamic_cast<resip::SdpContents*>(internal->resipMsg.getContents());
      bool isInvite = args.method == "INVITE";

      if (isInvite && (sdpContents != NULL))
      {
         // Sample Swisscom SDP
         /*
          v=0
          o=+***********-02 4803929 0 IN IP4 ************
          s=-
          c=IN IP4 ************
          t=0 0
          m=audio 8500 RTP/AVP 9 8 0 100
          a=sendrecv
          a=rtpmap:9 G722/8000
          a=rtpmap:8 PCMA/8000
          a=rtpmap:0 PCMU/8000
          a=rtpmap:100 telephone-event/8000
         */
         /*
          v=0
          o=- **************** 1 IN IP4 **********
          s=Softphone - Enterprise Telephony - mac 6.5.3 QA 109868
          c=IN IP4 **********
          t=0 0
          m=audio 55222 RTP/SAVP 9 8 0 101
          a=rtpmap:101 telephone-event/8000
          a=fmtp:101 0-15
          a=crypto:1 AEAD_AES_128_GCM inline:IF1dNIXLd50tuJCFAGLAu0G7KD8hFBg/o0oOXQ==
          a=crypto:2 AEAD_AES_256_GCM inline:OoJkXyfzcqCgRQWkwk5dywC3k2kYkLUZ4VGL3XgbhX7wmgjzcckPB+CLKog=
          a=crypto:3 AES_CM_128_HMAC_SHA1_80 inline:VLJdifYRXYsxYqKPZMvaBmc3s/YN+5yiCJo6JcCd
          a=crypto:4 AES_192_CM_HMAC_SHA1_80 inline:ADjzHlay4VumWv5F8hEGZwG3UCpal9CICUFfMrcXVdM5hU95owg=
          a=crypto:5 AES_256_CM_HMAC_SHA1_80 inline:oe3pyUKzcrTbZoW/m+79CZ8VzhY4P5bn5XVxJz7FLx550Fw8rCsd+olu2A4yHw==
          a=crypto:6 AES_CM_128_HMAC_SHA1_32 inline:AGJ2cSaFOii5XNszNEWU5Q5i4fUQ61x4PX7xy1Fy
          a=crypto:7 AES_192_CM_HMAC_SHA1_32 inline:Dnw3iQKYy6iF5xnnzFUBz/WQwKb7U5Cz73AYaH+BJiAAFw923gA=
          a=crypto:8 AES_256_CM_HMAC_SHA1_32 inline:1yNJjHMtZYrdMCPc5fIKmjWyAy+URWJjaHfHg/6oD/giV99rdNyDzszWD+A+2g==
          a=sendrecv
         */
         sdpContents->session().origin().user() = mAccount.config.settings.username.c_str();
         sdpContents->session().origin().getVersion() = 0;
         sdpContents->session().name() = "-";

         unsigned long port = 0;
         for (resip::SdpContents::Session::MediumContainer::const_iterator it = sdpContents->session().media().begin(); it != sdpContents->session().media().end(); ++it)
         {
            if (it->name() == "audio")
            {
               port = it->port();
               break;
            }
         }
         sdpContents->session().media().clear();
         resip::SdpContents::Session::Medium medium;
         medium.name() = "audio";
         medium.port() = port;
         medium.protocol() = "RTP/AVP";
         medium.addAttribute("sendrecv");
         resip::SdpContents::Session::Codec codec1("G722", 9, 8000);
         resip::SdpContents::Session::Codec codec2("PCMA", 8, 8000);
         resip::SdpContents::Session::Codec codec3("PCMU", 0, 8000);
         resip::SdpContents::Session::Codec codec4("telephone-event", 101, 8000);
         medium.addCodec(codec1);
         medium.addCodec(codec2);
         medium.addCodec(codec3);
         medium.addCodec(codec4);

         sdpContents->session().addMedium(medium);
      }

      return kSuccess;
   }
};

void NetworkChangeTest::starcodeNetworkChange(TestAccount& account, std::string starcodeUrl, SipConversationHandle& newCall, SipConversationHandle callToReplace, NetworkTransport transport, int index)
{
   SipConversationManagerInternal* convMgr = dynamic_cast<SipConversationManagerInternal*>(account.conversation);
   ASSERT_TRUE(convMgr != NULL);
   convMgr->ignoreBindingFilterForStarcodeHandover(callToReplace);

   account.network->setNetworkTransport(transport);
   std::set<resip::Data> interfaces;
   std::stringstream ss;
   ss << index << "." << index << "." << index << "." << index;
   interfaces.insert(ss.str().c_str());
   account.network->setMockInterfaces(interfaces);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   safeCout("NetworkChangeTest::starcodeNetworkChange(): network-change index: " << index << " callToReplace: " << callToReplace << " transport: " << transport);

   assertAccountRefreshing(account);
   assertAccountRegistered(account);

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   /////
   /*
   // Disable network
   if (TestEnvironmentConfig::dockerContainerized())
   {
      ASSERT_EQ(0, test::NetworkUtils::setDockerContainerNetworkEnabled(false));
      ASSERT_EQ(0, test::NetworkUtils::shutdownTcpSockets());
   }
   std::set<resip::Data> nonet;
   account.network->setNetworkTransport(NetworkTransport::TransportNone);
   account.network->setMockInterfaces(nonet);

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   account.accountEvents->clearCommands();

   std::set<resip::Data> interfaces;
   std::stringstream ss;
   ss << index << "." << index << "." << index << "." << index;
   interfaces.insert(ss.str().c_str());
   account.network->setMockInterfaces(interfaces);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   // Enable network
   if (TestEnvironmentConfig::dockerContainerized())
   {
      ASSERT_EQ(0, test::NetworkUtils::setDockerContainerNetworkEnabled(true));
   }
   account.network->setNetworkTransport(transport);
   account.network->setMockInterfaces(interfaces);

   assertAccountRefreshing(account);
   assertAccountRegistered(account);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));
   */
   /////

   // App receives the NewConversationEvent event for the starcode dialog, resulting from the network change
   {
      NewConversationEvent evt;
      ASSERT_TRUE(account.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onNewConversation", 15000, AlwaysTruePred(), newCall, evt)) << " missed outgoing call event in network-change index " << index;
      ASSERT_EQ(callToReplace, evt.conversationToReplace) << " replaced conversation handle does not match in network-change index " << index;
      ASSERT_EQ(ConversationType_OutgoingNetworkChangeHandover, evt.conversationType) << " conversation type does not match with outgoing in network-change index " << index;
      ASSERT_EQ(starcodeUrl.c_str(), evt.remoteAddress) << " remote-address starcode does not match in network-change index " << index;
   }

   assertConversationMediaChanged(account, newCall, MediaDirection_ReceiveOnly);
   assertConversationStateChanged(account, newCall, ConversationState_Connected);

   account.conversationEvents->clearCommands();
   account.accountEvents->clearCommands();
   std::this_thread::sleep_for(std::chrono::milliseconds(6000));
   assertMediaFlowing(account, newCall, true, false);
}

void NetworkChangeTest::starcodeNetworkChangeNotTriggered(TestAccount& account, SipConversationHandle existingCall, NetworkTransport transport, int index)
{
   account.network->setNetworkTransport(transport);
   std::set<resip::Data> interfaces;
   std::stringstream ss;
   ss << index << "." << index << "." << index << "." << index;
   interfaces.insert(ss.str().c_str());
   account.network->setMockInterfaces(interfaces);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   safeCout("NetworkChangeTest::starcodeNetworkChange(): network-change index: " << index << " transport: " << transport);

   assertAccountRefreshing(account);
   assertAccountRegistered(account);

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   // No starcode expected with same tls ports
   assertConversationMediaChanged(account, existingCall, MediaDirection_SendReceive);
   account.conversationEvents->clearCommands();
   account.accountEvents->clearCommands();
   std::this_thread::sleep_for(std::chrono::milliseconds(6000));
   assertMediaFlowing(account, existingCall, true, false);
}

bool swisscomAccountSetup(TestAccount& account)
{
   std::string starcode = "*111";

   account.config.settings.domain = "imst.swisscom.ch";
   account.config.settings.outboundProxy = "ssc1.imst.swisscom.ch:5076";
   account.config.settings.username = "+***********-01";
   account.config.settings.password = R"()E[OTS^\a9=i >)";
   account.config.settings.useOutbound = false;
   account.config.settings.useRport = false;
   account.config.settings.displayName = "";
   account.config.settings.registrationIntervalSeconds = 900;
   account.config.settings.sipTransportType = SipAccountTransport_TLS;
   account.config.settings.auth_username = "<EMAIL>";
   account.config.settings.userAgent = "Softphone - Enterprise Telephony";
   const cpc::string accountExt = "sip:+<EMAIL>;user=phone";

   SipConversationSettings settings;
   settings.networkChangeHandoverMode = NetworkChangeHandoverMode_Starcode;
   settings.networkChangeHandoverStarcode = starcode.c_str();
   std::stringstream starcodeUrl;
   starcodeUrl << "sip:" << starcode << "@" << account.config.settings.domain;

   account.init();

   SipAccountManagerInternal* accountMgr = dynamic_cast<SipAccountManagerInternal*>(account.account);
   if (accountMgr)
   {
      accountMgr->setIgnoreNetworkChangeStarcodeFilter(account.handle, false);
      return true;
   }

   return false;
}

TEST_F(NetworkChangeTest, DISABLED_SwisscomAccumulateContactBindings)
{
   std::string starcode = "*111";

   TestAccount alice("alice", Account_NoInit);
   ASSERT_TRUE(swisscomAccountSetup(alice));

   int maxNetworkChanges = 10;
   int x = 0;
   while (x < maxNetworkChanges)
   {
      alice.enable();
      ASSERT_TRUE(alice.isEnabled());
      alice.disable();
      x++;
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(3000));
}

TEST_F(NetworkChangeTest, DISABLED_SwisscomNetworkChangeHandoverUsingStarcodeWithWifiWanNoBindingAccumulation)
{
   std::string starcode = "*111";

   TestAccount alice("alice", Account_NoInit);
   ASSERT_TRUE(swisscomAccountSetup(alice));

   alice.enable();
   ASSERT_TRUE(alice.isEnabled());

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   int maxNetworkChanges = 10;

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      int index = 0;
      while (index < maxNetworkChanges)
      {
         NetworkTransport transport = ((index % 2 == 0) ? NetworkTransport::TransportWWAN : NetworkTransport::TransportWiFi);
         std::set<resip::Data> interfaces;
         std::stringstream ss;
         ss << index << "." << index << "." << index << "." << index;
         interfaces.insert(ss.str().c_str());
         alice.network->setNetworkTransport(transport);
         alice.network->setMockInterfaces(interfaces);

         assertAccountRefreshing(alice);
         assertAccountRegistered(alice);

         std::this_thread::sleep_for(std::chrono::milliseconds(5000));
         alice.accountEvents->clearCommands();
         index++;
      }
   });

   waitForMs(aliceEvents, std::chrono::milliseconds(60000 + (30000 * maxNetworkChanges)));
}

TEST_F(NetworkChangeTest, DISABLED_SwisscomNetworkChangeHandoverUsingStarcodeWithNetworkNoneNoBindingAccumulation)
{
   std::string starcode = "*111";

   TestAccount alice("alice", Account_NoInit);
   ASSERT_TRUE(swisscomAccountSetup(alice));

   alice.enable();
   ASSERT_TRUE(alice.isEnabled());

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   int maxNetworkChanges = 10;

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      int index = 0;
      while (index < maxNetworkChanges)
      {
         // Disable network
         std::this_thread::sleep_for(std::chrono::milliseconds(1000));
         std::set<resip::Data> nonet;
         alice.network->setNetworkTransport(NetworkTransport::TransportNone);
         alice.network->setMockInterfaces(nonet);

         std::this_thread::sleep_for(std::chrono::milliseconds(1000));

         assertAccountDeregistering(alice);
         assertAccountDeregistered(alice);

         std::set<resip::Data> interfaces;
         std::stringstream ss;
         ss << index << "." << index << "." << index << "." << index;
         interfaces.insert(ss.str().c_str());
         alice.network->setMockInterfaces(interfaces);
         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         NetworkTransport transport = ((index % 2 == 0) ? NetworkTransport::TransportWWAN : NetworkTransport::TransportWiFi);
         alice.network->setNetworkTransport(transport);
         alice.network->setMockInterfaces(interfaces);

         assertAccountRegistering(alice);
         assertAccountRegistered(alice);

         std::this_thread::sleep_for(std::chrono::milliseconds(5000));
         alice.accountEvents->clearCommands();
         index++;
      }
   });

   waitForMs(aliceEvents, std::chrono::milliseconds(60000 + (30000 * maxNetworkChanges)));
}

TEST_F(NetworkChangeTest, DISABLED_SwisscomNetworkChangeHandoverUsingStarcodeWithNetworkNoneAndNoTransportNoBindingAccumulation)
{
   std::string starcode = "*111";

   TestAccount alice("alice", Account_NoInit);
   ASSERT_TRUE(swisscomAccountSetup(alice));

   alice.enable();
   ASSERT_TRUE(alice.isEnabled());

   SipAccountManagerInternal* accountMgr = dynamic_cast<SipAccountManagerInternal*>(alice.account);
   ASSERT_TRUE(accountMgr != NULL);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   int maxNetworkChanges = 10;

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      int index = 0;
      while (index < maxNetworkChanges)
      {
         accountMgr->closeTransportConnections(alice.handle);

         // Disable network
         std::this_thread::sleep_for(std::chrono::milliseconds(1000));
         std::set<resip::Data> nonet;
         alice.network->setNetworkTransport(NetworkTransport::TransportNone);
         alice.network->setMockInterfaces(nonet);

         std::this_thread::sleep_for(std::chrono::milliseconds(1000));

         assertAccountDeregistering(alice);
         assertAccountDeregistered(alice);

         std::set<resip::Data> interfaces;
         std::stringstream ss;
         ss << index << "." << index << "." << index << "." << index;
         interfaces.insert(ss.str().c_str());
         alice.network->setMockInterfaces(interfaces);
         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         NetworkTransport transport = ((index % 2 == 0) ? NetworkTransport::TransportWWAN : NetworkTransport::TransportWiFi);
         alice.network->setNetworkTransport(transport);
         alice.network->setMockInterfaces(interfaces);

         assertAccountRegistering(alice);
         assertAccountRegistered(alice);

         std::this_thread::sleep_for(std::chrono::milliseconds(5000));
         alice.accountEvents->clearCommands();
         index++;
      }
   });

   waitForMs(aliceEvents, std::chrono::milliseconds(60000 + (30000 * maxNetworkChanges)));
}

// Requires enabling the setFakeResponse test-code in SipAccountImpl and in resip SipStack, TransactionController and TransactionState
TEST_F(NetworkChangeTest, DISABLED_SwisscomNetworkChangeHandoverUsingStarcodeWithNetworkNoneNoTransportAndNoRouteBindingAccumulation)
{
   std::string starcode = "*111";

   TestAccount alice("alice", Account_NoInit);
   ASSERT_TRUE(swisscomAccountSetup(alice));

   alice.enable();
   ASSERT_TRUE(alice.isEnabled());

   SipAccountManagerInternal* accountMgr = dynamic_cast<SipAccountManagerInternal*>(alice.account);
   ASSERT_TRUE(accountMgr != NULL);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   int maxNetworkChanges = 10;

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      int index = 0;
      while (index < maxNetworkChanges)
      {
         // Disable network
         accountMgr->setFakeResponse(alice.handle, true, "REGISTER", 503, "No route to host", 397);
         accountMgr->closeTransportConnections(alice.handle);

         std::this_thread::sleep_for(std::chrono::milliseconds(1000));
         std::set<resip::Data> nonet;
         alice.network->setNetworkTransport(NetworkTransport::TransportNone);
         alice.network->setMockInterfaces(nonet);

         std::this_thread::sleep_for(std::chrono::milliseconds(1000));

         assertAccountDeregistering(alice);
         assertAccountDeregistered(alice);

         std::set<resip::Data> interfaces;
         std::stringstream ss;
         ss << index << "." << index << "." << index << "." << index;
         interfaces.insert(ss.str().c_str());
         alice.network->setMockInterfaces(interfaces);
         std::this_thread::sleep_for(std::chrono::milliseconds(5000));

         assertAccountDeregistered(alice);

         // Enable network
         NetworkTransport transport = ((index % 2 == 0) ? NetworkTransport::TransportWWAN : NetworkTransport::TransportWiFi);
         alice.network->setNetworkTransport(transport);
         alice.network->setMockInterfaces(interfaces);

         assertAccountRegistering(alice);
         accountMgr->setFakeResponse(alice.handle, false, "REGISTER");
         assertAccountRegistered(alice);

         std::this_thread::sleep_for(std::chrono::milliseconds(5000));
         alice.accountEvents->clearCommands();
         index++;
      }
   });

   waitForMs(aliceEvents, std::chrono::milliseconds(60000 + (30000 * maxNetworkChanges)));
}

TEST_F(NetworkChangeTest, DISABLED_SwisscomNetworkChangeHandoverUsingStarcode)
{
   /*
   // max.config.settings.username = "+***********"; // <EMAIL>"
   // max.config.settings.username = "6iRJ6SbyR_M";

   // max.config.settings.username = "+***********"; // "<EMAIL>";
   // max.config.settings.password = "p7MVVD_CXn_";

   // max.config.settings.username = "<EMAIL>";
   // max.config.settings.password = "p7MVVD_CXn_";

   // alice.config.settings.username = "+***********";
   // alice.config.settings.password = "wF9Tz_hEwxFZ";

   // alice.config.settings.username = "+***********";
   // alice.config.settings.password = "Ga1U*jyGdga5";
   */

   std::string starcode = "*111";

   TestAccount alice("alice", Account_NoInit);
   TestAccount bob("bob", Account_NoInit);

   MediaInfo media;
   media.mediaEncryptionOptions.mediaCryptoSuites.clear();
   cpc::vector<MediaCryptoSuite> cryptos;
   cryptos.push_back(MediaCryptoSuite_AEAD_AES_128_GCM);
   cryptos.push_back(MediaCryptoSuite_AEAD_AES_256_GCM);
   cryptos.push_back(MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);
   cryptos.push_back(MediaCryptoSuite_AES_CM_192_HMAC_SHA1_80);
   cryptos.push_back(MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80);
   cryptos.push_back(MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);
   cryptos.push_back(MediaCryptoSuite_AES_CM_192_HMAC_SHA1_32);
   cryptos.push_back(MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32);
   configureMedia(media, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, cryptos);

   alice.config.settings.domain = "imst.swisscom.ch";
   alice.config.settings.outboundProxy = "ssc1.imst.swisscom.ch:5076";
   alice.config.settings.username = "+***********-01";
   alice.config.settings.password = R"()E[OTS^\a9=i >)";
   alice.config.settings.useOutbound = false;
   alice.config.settings.useRport = false;
   alice.config.settings.displayName = "";
   alice.config.settings.registrationIntervalSeconds = 900;
   alice.config.settings.sipTransportType = SipAccountTransport_TLS;
   alice.config.settings.auth_username = "<EMAIL>";
   alice.config.settings.userAgent = "Softphone - Enterprise Telephony";
   const cpc::string aliceExt = "sip:+<EMAIL>;user=phone";

   SipConversationSettings settings;
   settings.networkChangeHandoverMode = NetworkChangeHandoverMode_Starcode;
   settings.networkChangeHandoverStarcode = starcode.c_str();
   std::stringstream starcodeUrl;
   starcodeUrl << "sip:" << starcode << "@" << alice.config.settings.domain;

   alice.init();

   SipAccountManagerInternal* accountMgr = dynamic_cast<SipAccountManagerInternal*>(alice.account);
   ASSERT_TRUE(accountMgr != NULL);
   accountMgr->setIgnoreNetworkChangeStarcodeFilter(alice.handle, false);

   alice.enable();
   ASSERT_TRUE(alice.isEnabled());

   bob.config.settings.domain = "imst.swisscom.ch";
   bob.config.settings.outboundProxy = "ssc1.imst.swisscom.ch:5076";
   bob.config.settings.username = "+***********-01";
   bob.config.settings.password = "YsrS&_tMsDp}UM";
   bob.config.settings.useOutbound = false;
   bob.config.settings.useRport = false;
   bob.config.settings.displayName = "";
   bob.config.settings.registrationIntervalSeconds = 900;
   bob.config.settings.sipTransportType = SipAccountTransport_TLS;
   bob.config.settings.auth_username = "<EMAIL>";
   bob.config.settings.userAgent = "Softphone - Enterprise Telephony";
   const cpc::string bobExt = "sip:+<EMAIL>";
   bob.init();
   bob.enable();
   ASSERT_TRUE(bob.isEnabled());

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   int maxNetworkChanges = 1;

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      // Establish call between alice and bob
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->setDefaultSettings(alice.handle, settings);
      alice.conversation->configureMedia(aliceCall, media);
      alice.conversation->addParticipant(aliceCall, bobExt.c_str());

      // Reconnection may result with same binding, ignore the filter to ensure that starcode handling is triggered
      SipConversationManagerInternal* convMgr = dynamic_cast<SipConversationManagerInternal*>(alice.conversation);
      ASSERT_TRUE(convMgr != NULL);
      convMgr->ignoreBindingFilterForStarcodeHandover(aliceCall);

      alice.conversation->start(aliceCall);

      assertNewConversationOutgoing(alice, aliceCall, bobExt.c_str());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(6000));
      assertMediaFlowing(alice, aliceCall, true, false);

      int index = 0;
      SipConversationHandle callToReplace = aliceCall;
      while (index < maxNetworkChanges)
      {
         index++;
         SipConversationHandle newCall = 0;
         starcodeNetworkChange(alice, starcodeUrl.str().c_str(), newCall, callToReplace, ((index % 2 == 0) ? NetworkTransport::TransportWWAN : NetworkTransport::TransportWiFi), index);
         callToReplace = newCall;
      }

      assertSuccess(alice.conversation->end(callToReplace));
      assertConversationEnded(alice, callToReplace, ConversationEndReason_UserTerminatedLocally);

      // expect registration refresh after network change during a call
      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall;

      assertNewConversationIncoming(bob, &bobCall, aliceExt.c_str());
      // assertNewConversationIncoming(bob, &bobCall, "sip:+<EMAIL>");
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));

      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      
      std::this_thread::sleep_for(std::chrono::milliseconds(6000));
      assertMediaFlowing(bob, bobCall, true, false);

      int index = 0;
      while (index < maxNetworkChanges)
      {
         assertConversationMediaChangeRequest_time(bob, bobCall, MediaDirection_SendReceive, 30000);
         assertSuccess(bob.conversation->accept(bobCall));
         assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
         index++;

         std::this_thread::sleep_for(std::chrono::milliseconds(6000));
         bob.conversationEvents->clearCommands();
         bob.accountEvents->clearCommands();
         assertMediaFlowing(bob, bobCall, true, false);
      }

      assertConversationEnded_time(bob, bobCall, ConversationEndReason_UserTerminatedRemotely, 45000);
   });

   waitFor2Ms(aliceEvents, bobEvents, std::chrono::milliseconds(60000 + (30000 * maxNetworkChanges)));
}

TEST_F(NetworkChangeTest, DISABLED_SwisscomNetworkChangeHandoverUsingStarcodeIgnored)
{
   std::string starcode = "*111";

   TestAccount alice("alice", Account_NoInit);
   TestAccount bob("bob", Account_NoInit);

   MediaInfo media;
   media.mediaEncryptionOptions.mediaCryptoSuites.clear();
   cpc::vector<MediaCryptoSuite> cryptos;
   cryptos.push_back(MediaCryptoSuite_AEAD_AES_128_GCM);
   cryptos.push_back(MediaCryptoSuite_AEAD_AES_256_GCM);
   cryptos.push_back(MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);
   cryptos.push_back(MediaCryptoSuite_AES_CM_192_HMAC_SHA1_80);
   cryptos.push_back(MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80);
   cryptos.push_back(MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);
   cryptos.push_back(MediaCryptoSuite_AES_CM_192_HMAC_SHA1_32);
   cryptos.push_back(MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32);
   configureMedia(media, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, cryptos);

   alice.config.settings.domain = "imst.swisscom.ch";
   alice.config.settings.outboundProxy = "ssc1.imst.swisscom.ch:5076";
   alice.config.settings.username = "+***********-01";
   alice.config.settings.password = R"()E[OTS^\a9=i >)";
   alice.config.settings.useOutbound = false;
   alice.config.settings.useRport = false;
   alice.config.settings.displayName = "";
   alice.config.settings.registrationIntervalSeconds = 900;
   alice.config.settings.sipTransportType = SipAccountTransport_TLS;
   alice.config.settings.auth_username = "<EMAIL>";
   alice.config.settings.userAgent = "Softphone - Enterprise Telephony";
   const cpc::string aliceExt = "sip:+<EMAIL>;user=phone";

   SipConversationSettings settings;
   settings.networkChangeHandoverMode = NetworkChangeHandoverMode_Starcode;
   settings.networkChangeHandoverStarcode = starcode.c_str();
   std::stringstream starcodeUrl;
   starcodeUrl << "sip:" << starcode << "@" << alice.config.settings.domain;

   alice.init();

   SipAccountManagerInternal* accountMgr = dynamic_cast<SipAccountManagerInternal*>(alice.account);
   ASSERT_TRUE(accountMgr != NULL);
   accountMgr->setIgnoreNetworkChangeStarcodeFilter(alice.handle, false);

   alice.enable();
   ASSERT_TRUE(alice.isEnabled());

   bob.config.settings.domain = "imst.swisscom.ch";
   bob.config.settings.outboundProxy = "ssc1.imst.swisscom.ch:5076";
   bob.config.settings.username = "+***********-01";
   bob.config.settings.password = "YsrS&_tMsDp}UM";
   bob.config.settings.useOutbound = false;
   bob.config.settings.useRport = false;
   bob.config.settings.displayName = "";
   bob.config.settings.registrationIntervalSeconds = 900;
   bob.config.settings.sipTransportType = SipAccountTransport_TLS;
   bob.config.settings.auth_username = "<EMAIL>";
   bob.config.settings.userAgent = "Softphone - Enterprise Telephony";
   const cpc::string bobExt = "sip:+<EMAIL>";
   bob.init();
   bob.enable();
   ASSERT_TRUE(bob.isEnabled());

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   int maxNetworkChanges = 1;

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      // Establish call between alice and bob
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->setDefaultSettings(alice.handle, settings);
      alice.conversation->configureMedia(aliceCall, media);
      alice.conversation->addParticipant(aliceCall, bobExt.c_str());
      alice.conversation->start(aliceCall);

      assertNewConversationOutgoing(alice, aliceCall, bobExt.c_str());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(6000));
      assertMediaFlowing(alice, aliceCall, true, false);

      int index = 0;
      while (index < maxNetworkChanges)
      {
         index++;
         starcodeNetworkChangeNotTriggered(alice, aliceCall, ((index % 2 == 0) ? NetworkTransport::TransportWWAN : NetworkTransport::TransportWiFi), index);
      }
      
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);

      // expect registration refresh after network change during a call
      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall;

      assertNewConversationIncoming(bob, &bobCall, aliceExt.c_str());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));

      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      
      std::this_thread::sleep_for(std::chrono::milliseconds(6000));
      assertMediaFlowing(bob, bobCall, true, false);

      int index = 0;
      while (index < maxNetworkChanges)
      {
         assertConversationMediaChangeRequest_time(bob, bobCall, MediaDirection_SendReceive, 30000);
         assertSuccess(bob.conversation->accept(bobCall));
         assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
         index++;

         std::this_thread::sleep_for(std::chrono::milliseconds(6000));
         bob.conversationEvents->clearCommands();
         bob.accountEvents->clearCommands();
         assertMediaFlowing(bob, bobCall, true, false);
      }

      assertConversationEnded_time(bob, bobCall, ConversationEndReason_UserTerminatedRemotely, 45000);
   });

   waitFor2Ms(aliceEvents, bobEvents, std::chrono::milliseconds(60000 + (30000 * maxNetworkChanges)));
}

TEST_F(NetworkChangeTest, DISABLED_SwisscomNetworkChangeHandoverUsingStarcodeWithLocalHold)
{
   std::string starcode = "*111";

   TestAccount alice("alice", Account_NoInit);
   TestAccount bob("bob", Account_NoInit);

   MediaInfo media;
   media.mediaEncryptionOptions.mediaCryptoSuites.clear();
   cpc::vector<MediaCryptoSuite> cryptos;
   cryptos.push_back(MediaCryptoSuite_AEAD_AES_128_GCM);
   cryptos.push_back(MediaCryptoSuite_AEAD_AES_256_GCM);
   cryptos.push_back(MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);
   cryptos.push_back(MediaCryptoSuite_AES_CM_192_HMAC_SHA1_80);
   cryptos.push_back(MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80);
   cryptos.push_back(MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);
   cryptos.push_back(MediaCryptoSuite_AES_CM_192_HMAC_SHA1_32);
   cryptos.push_back(MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32);
   configureMedia(media, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, cryptos);

   alice.config.settings.domain = "imst.swisscom.ch";
   alice.config.settings.outboundProxy = "ssc1.imst.swisscom.ch:5076";
   alice.config.settings.username = "+***********-01";
   alice.config.settings.password = R"()E[OTS^\a9=i >)";
   alice.config.settings.useOutbound = false;
   alice.config.settings.useRport = false;
   alice.config.settings.displayName = "";
   alice.config.settings.registrationIntervalSeconds = 900;
   alice.config.settings.sipTransportType = SipAccountTransport_TLS;
   alice.config.settings.auth_username = "<EMAIL>";
   alice.config.settings.userAgent = "Softphone - Enterprise Telephony";
   const cpc::string aliceExt = "sip:+<EMAIL>;user=phone";

   SipConversationSettings settings;
   settings.networkChangeHandoverMode = NetworkChangeHandoverMode_Starcode;
   settings.networkChangeHandoverStarcode = starcode.c_str();
   std::stringstream starcodeUrl;
   starcodeUrl << "sip:" << starcode << "@" << alice.config.settings.domain;

   alice.init();

   SipAccountManagerInternal* accountMgr = dynamic_cast<SipAccountManagerInternal*>(alice.account);
   ASSERT_TRUE(accountMgr != NULL);
   accountMgr->setIgnoreNetworkChangeStarcodeFilter(alice.handle, false);

   alice.enable();

   bob.config.settings.domain = "imst.swisscom.ch";
   bob.config.settings.outboundProxy = "ssc1.imst.swisscom.ch:5076";
   bob.config.settings.username = "+***********-01";
   bob.config.settings.password = "YsrS&_tMsDp}UM";
   bob.config.settings.useOutbound = false;
   bob.config.settings.useRport = false;
   bob.config.settings.displayName = "";
   bob.config.settings.registrationIntervalSeconds = 900;
   bob.config.settings.sipTransportType = SipAccountTransport_TLS;
   bob.config.settings.auth_username = "<EMAIL>";
   bob.config.settings.userAgent = "Softphone - Enterprise Telephony";
   const cpc::string bobExt = "sip:+<EMAIL>";
   bob.init();
   bob.enable();

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   int maxNetworkChanges = 1;
   std::atomic<bool> mediaChecked = false;

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      // Establish call between alice and bob
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->setDefaultSettings(alice.handle, settings);
      alice.conversation->configureMedia(aliceCall, media);
      alice.conversation->addParticipant(aliceCall, bobExt.c_str());
      alice.conversation->start(aliceCall);

      assertNewConversationOutgoing(alice, aliceCall, bobExt.c_str());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(6000));
      assertMediaFlowing(alice, aliceCall, true, false);

      int index = 0;
      SipConversationHandle callToReplace = aliceCall;
      while (index < maxNetworkChanges)
      {
         index++;
         SipConversationHandle newCall = 0;
         starcodeNetworkChange(alice, starcodeUrl.str().c_str(), newCall, callToReplace, ((index % 2 == 0) ? NetworkTransport::TransportWWAN : NetworkTransport::TransportWiFi), index);
         callToReplace = newCall;
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(3000));

      assertSuccess(alice.conversation->hold(callToReplace));
      assertConversationMediaChanged_ex(alice, callToReplace, [](const ConversationMediaChangedEvent& evt)
      {
         ASSERT_TRUE(evt.localHold);
         ASSERT_TRUE(evt.remoteHold); // Swisscom returns inactive status in SDP
         ASSERT_EQ(MediaDirection_Inactive, evt.localMediaInfo[0].mediaDirection);
      });
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertSuccess(alice.conversation->unhold(callToReplace));
      assertConversationMediaChanged_ex(alice, callToReplace, [](const ConversationMediaChangedEvent& evt)
      {
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      alice.conversationEvents->clearCommands();
      alice.accountEvents->clearCommands();
      assertMediaFlowing(alice, callToReplace, true, false);

      while (mediaChecked == false)
      {
         std::this_thread::sleep_for(std::chrono::milliseconds(500));
      }

      assertSuccess(alice.conversation->end(callToReplace));
      assertConversationEnded(alice, callToReplace, ConversationEndReason_UserTerminatedLocally);

      // expect registration refresh after network change during a call
      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall;

      assertNewConversationIncoming(bob, &bobCall, aliceExt.c_str());
      // assertNewConversationIncoming(bob, &bobCall, "sip:+<EMAIL>");
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));

      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      int index = 0;
      while (index < maxNetworkChanges)
      {
         assertConversationMediaChangeRequest_time(bob, bobCall, MediaDirection_SendReceive, 30000);
         assertSuccess(bob.conversation->accept(bobCall));
         assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
         index++;

         std::this_thread::sleep_for(std::chrono::milliseconds(6000));
         bob.conversationEvents->clearCommands();
         bob.accountEvents->clearCommands();
         assertMediaFlowing(bob, bobCall, true, false);
      }

      assertConversationMediaChangeRequest_time(bob, bobCall, MediaDirection_SendOnly, 30000);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt)
      {
         ASSERT_FALSE(evt.localHold);
         ASSERT_TRUE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_ReceiveOnly, evt.localMediaInfo[0].mediaDirection);
      });
      
      // Received an extra send-recv INVITE for MoH from Swisscom
      assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt)
      {
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      
      assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt)
      {
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });

      std::this_thread::sleep_for(std::chrono::milliseconds(6000));
      bob.conversationEvents->clearCommands();
      bob.accountEvents->clearCommands();
      assertMediaFlowing(bob, bobCall, true, false);
      mediaChecked = true;

      assertConversationEnded_time(bob, bobCall, ConversationEndReason_UserTerminatedRemotely, 45000);
   });

   waitFor2Ms(aliceEvents, bobEvents, std::chrono::milliseconds(60000 + (30000 * maxNetworkChanges)));
}

TEST_F(NetworkChangeTest, DISABLED_SwisscomNetworkChangeHandoverUsingStarcodeWithLocalHoldIgnored)
{
   std::string starcode = "*111";

   TestAccount alice("alice", Account_NoInit);
   TestAccount bob("bob", Account_NoInit);

   MediaInfo media;
   media.mediaEncryptionOptions.mediaCryptoSuites.clear();
   cpc::vector<MediaCryptoSuite> cryptos;
   cryptos.push_back(MediaCryptoSuite_AEAD_AES_128_GCM);
   cryptos.push_back(MediaCryptoSuite_AEAD_AES_256_GCM);
   cryptos.push_back(MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);
   cryptos.push_back(MediaCryptoSuite_AES_CM_192_HMAC_SHA1_80);
   cryptos.push_back(MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80);
   cryptos.push_back(MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);
   cryptos.push_back(MediaCryptoSuite_AES_CM_192_HMAC_SHA1_32);
   cryptos.push_back(MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32);
   configureMedia(media, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, cryptos);

   alice.config.settings.domain = "imst.swisscom.ch";
   alice.config.settings.outboundProxy = "ssc1.imst.swisscom.ch:5076";
   alice.config.settings.username = "+***********-01";
   alice.config.settings.password = R"()E[OTS^\a9=i >)";
   alice.config.settings.useOutbound = false;
   alice.config.settings.useRport = false;
   alice.config.settings.displayName = "";
   alice.config.settings.registrationIntervalSeconds = 900;
   alice.config.settings.sipTransportType = SipAccountTransport_TLS;
   alice.config.settings.auth_username = "<EMAIL>";
   alice.config.settings.userAgent = "Softphone - Enterprise Telephony";
   const cpc::string aliceExt = "sip:+<EMAIL>;user=phone";

   SipConversationSettings settings;
   settings.networkChangeHandoverMode = NetworkChangeHandoverMode_Starcode;
   settings.networkChangeHandoverStarcode = starcode.c_str();
   std::stringstream starcodeUrl;
   starcodeUrl << "sip:" << starcode << "@" << alice.config.settings.domain;

   alice.init();

   SipAccountManagerInternal* accountMgr = dynamic_cast<SipAccountManagerInternal*>(alice.account);
   ASSERT_TRUE(accountMgr != NULL);
   accountMgr->setIgnoreNetworkChangeStarcodeFilter(alice.handle, false);

   alice.enable();

   bob.config.settings.domain = "imst.swisscom.ch";
   bob.config.settings.outboundProxy = "ssc1.imst.swisscom.ch:5076";
   bob.config.settings.username = "+***********-01";
   bob.config.settings.password = "YsrS&_tMsDp}UM";
   bob.config.settings.useOutbound = false;
   bob.config.settings.useRport = false;
   bob.config.settings.displayName = "";
   bob.config.settings.registrationIntervalSeconds = 900;
   bob.config.settings.sipTransportType = SipAccountTransport_TLS;
   bob.config.settings.auth_username = "<EMAIL>";
   bob.config.settings.userAgent = "Softphone - Enterprise Telephony";
   const cpc::string bobExt = "sip:+<EMAIL>";
   bob.init();
   bob.enable();

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   int maxNetworkChanges = 1;
   std::atomic<bool> mediaChecked = false;

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      // Establish call between alice and bob
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->setDefaultSettings(alice.handle, settings);
      alice.conversation->configureMedia(aliceCall, media);
      alice.conversation->addParticipant(aliceCall, bobExt.c_str());
      alice.conversation->start(aliceCall);

      assertNewConversationOutgoing(alice, aliceCall, bobExt.c_str());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(6000));
      assertMediaFlowing(alice, aliceCall, true, false);

      int index = 0;
      while (index < maxNetworkChanges)
      {
         index++;
         starcodeNetworkChangeNotTriggered(alice, aliceCall, ((index % 2 == 0) ? NetworkTransport::TransportWWAN : NetworkTransport::TransportWiFi), index);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(3000));

      assertSuccess(alice.conversation->hold(aliceCall));
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt)
      {
         ASSERT_TRUE(evt.localHold);
         ASSERT_TRUE(evt.remoteHold); // Swisscom returns inactive status in SDP
         ASSERT_EQ(MediaDirection_Inactive, evt.localMediaInfo[0].mediaDirection);
      });
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertSuccess(alice.conversation->unhold(aliceCall));
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt)
      {
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      alice.conversationEvents->clearCommands();
      alice.accountEvents->clearCommands();
      assertMediaFlowing(alice, aliceCall, true, false);

      while (mediaChecked == false)
      {
         std::this_thread::sleep_for(std::chrono::milliseconds(500));
      }

      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);

      // expect registration refresh after network change during a call
      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);
   });

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall;

      assertNewConversationIncoming(bob, &bobCall, aliceExt.c_str());
      // assertNewConversationIncoming(bob, &bobCall, "sip:+<EMAIL>");
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));

      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      int index = 0;
      while (index < maxNetworkChanges)
      {
         assertConversationMediaChangeRequest_time(bob, bobCall, MediaDirection_SendReceive, 30000);
         assertSuccess(bob.conversation->accept(bobCall));
         assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
         index++;

         std::this_thread::sleep_for(std::chrono::milliseconds(6000));
         bob.conversationEvents->clearCommands();
         bob.accountEvents->clearCommands();
         assertMediaFlowing(bob, bobCall, true, false);
      }

      assertConversationMediaChangeRequest_time(bob, bobCall, MediaDirection_SendOnly, 30000);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt)
      {
         ASSERT_FALSE(evt.localHold);
         ASSERT_TRUE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_ReceiveOnly, evt.localMediaInfo[0].mediaDirection);
      });
      
      // Received an extra send-recv INVITE for MoH from Swisscom
      assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt)
      {
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      
      assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendReceive);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt)
      {
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });

      std::this_thread::sleep_for(std::chrono::milliseconds(6000));
      bob.conversationEvents->clearCommands();
      bob.accountEvents->clearCommands();
      assertMediaFlowing(bob, bobCall, true, false);
      mediaChecked = true;

      assertConversationEnded_time(bob, bobCall, ConversationEndReason_UserTerminatedRemotely, 45000);
   });

   waitFor2Ms(aliceEvents, bobEvents, std::chrono::milliseconds(60000 + (30000 * maxNetworkChanges)));
}

TEST_F(NetworkChangeTest, DISABLED_SwisscomNetworkChangeHandoverUsingStarcodeOnIncomingCall)
{
   TestAccount alice("alice", Account_NoInit);

   alice.config.settings.domain = "imst.swisscom.ch";
   alice.config.settings.outboundProxy = "ssc1.imst.swisscom.ch:5076";
   alice.config.settings.username = "+***********-01";
   alice.config.settings.password = R"()E[OTS^\a9=i >)";
   alice.config.settings.useOutbound = false;
   alice.config.settings.useRport = false;
   alice.config.settings.displayName = "";
   alice.config.settings.registrationIntervalSeconds = 3600;
   alice.config.settings.sipTransportType = SipAccountTransport_TLS;
   alice.config.settings.auth_username = "<EMAIL>";
   alice.config.settings.userAgent = "Softphone - Enterprise Telephony - mac 6.5.3 QA 109868"; // Bria 5 release 5.6.0 stamp 98891";
   const cpc::string aliceExt = "sip:+<EMAIL>;user=phone";

   alice.init();
   SipAccountManagerInternal* accountMgr = dynamic_cast<SipAccountManagerInternal*>(alice.account);
   ASSERT_TRUE(accountMgr != NULL);
   accountMgr->setIgnoreNetworkChangeStarcodeFilter(alice.handle, false);
   alice.enable();

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle aliceCall;

      assertNewConversationIncoming_time(alice, &aliceCall, "sip:+<EMAIL>;user=phone", 120000);
      assertSuccess(alice.conversation->sendRingingResponse(aliceCall));
      assertConversationStateChanged(alice, aliceCall, ConversationState_LocalRinging);
      assertSuccess(alice.conversation->accept(aliceCall));

      SipConversationManagerInternal* convMgr = dynamic_cast<SipConversationManagerInternal*>(alice.conversation);
      ASSERT_TRUE(convMgr != NULL);
      convMgr->ignoreBindingFilterForStarcodeHandover(aliceCall);

      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      assertConversationMediaChangeRequest_time(alice, aliceCall, MediaDirection_SendReceive, 120000);
      assertSuccess(alice.conversation->accept(aliceCall));
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);

      std::this_thread::sleep_for(std::chrono::milliseconds(10000));

      assertConversationEnded_time(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely, 90000);
   });

   waitForMs(aliceEvents, std::chrono::milliseconds(120000));
}

TEST_F(NetworkChangeTest, DISABLED_SwisscomNetworkChangeHandoverUsingStarcodeOnIncomingCallMultipleTimes)
{
   TestAccount alice("alice", Account_NoInit);

   alice.config.settings.domain = "imst.swisscom.ch";
   alice.config.settings.outboundProxy = "ssc1.imst.swisscom.ch:5076";
   alice.config.settings.username = "+***********-01";
   alice.config.settings.password = R"()E[OTS^\a9=i >)";
   // alice.config.settings.username = "+***********";
   // alice.config.settings.password = "wF9Tz_hEwxFZ";
   // alice.config.settings.username = "+***********";
   // alice.config.settings.password = "Ga1U*jyGdga5";
   alice.config.settings.useOutbound = false;
   alice.config.settings.useRport = false;
   alice.config.settings.displayName = "";
   alice.config.settings.registrationIntervalSeconds = 3600;
   alice.config.settings.sipTransportType = SipAccountTransport_TLS;
   alice.config.settings.auth_username = "<EMAIL>";
   alice.config.settings.userAgent = "Softphone - Enterprise Telephony - mac 6.5.3 QA 109868"; // Bria 5 release 5.6.0 stamp 98891";
   const cpc::string aliceExt = "sip:+<EMAIL>;user=phone";

   alice.init();
   SipAccountManagerInternal* accountMgr = dynamic_cast<SipAccountManagerInternal*>(alice.account);
   ASSERT_TRUE(accountMgr != NULL);
   accountMgr->setIgnoreNetworkChangeStarcodeFilter(alice.handle, false);
   alice.enable();

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));
   bool keepOnListening = true;

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle aliceCall;

      assertNewConversationIncoming_time(alice, &aliceCall, "sip:+<EMAIL>;user=phone", 120000);
      assertSuccess(alice.conversation->sendRingingResponse(aliceCall));
      assertConversationStateChanged(alice, aliceCall, ConversationState_LocalRinging);
      assertSuccess(alice.conversation->accept(aliceCall));

      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      while (keepOnListening)
      {
         assertConversationMediaChangeRequest_time(alice, aliceCall, MediaDirection_SendReceive, 120000);
         assertSuccess(alice.conversation->accept(aliceCall));
         assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      }

      assertConversationEnded_time(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely, 90000);
   });

   waitForMs(aliceEvents, std::chrono::milliseconds(100 * 120000));
}

TEST_F(NetworkChangeTest, DISABLED_SwisscomNetworkChangeHandoverUsingStarcodeOnOutgoingCall)
{
   std::string starcode = "*111";

   TestAccount alice("alice", Account_NoInit);
   TestAccount bob("bob", Account_NoInit);

   MediaInfo media;
   media.mediaEncryptionOptions.mediaCryptoSuites.clear();
   cpc::vector<MediaCryptoSuite> cryptos;
   cryptos.push_back(MediaCryptoSuite_AEAD_AES_128_GCM);
   cryptos.push_back(MediaCryptoSuite_AEAD_AES_256_GCM);
   cryptos.push_back(MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);
   cryptos.push_back(MediaCryptoSuite_AES_CM_192_HMAC_SHA1_80);
   cryptos.push_back(MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80);
   cryptos.push_back(MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);
   cryptos.push_back(MediaCryptoSuite_AES_CM_192_HMAC_SHA1_32);
   cryptos.push_back(MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32);
   configureMedia(media, MediaType_Audio, MediaDirection_SendReceive, MediaEncryptionMode_SRTP_SDES_Encrypted, true, cryptos);

   alice.config.settings.domain = "imst.swisscom.ch";
   alice.config.settings.outboundProxy = "ssc1.imst.swisscom.ch:5076";
   alice.config.settings.username = "+***********-01";
   alice.config.settings.password = R"()E[OTS^\a9=i >)";
   // alice.config.settings.username = "+***********";
   // alice.config.settings.password = "wF9Tz_hEwxFZ";
   alice.config.settings.useOutbound = false;
   alice.config.settings.useRport = false;
   alice.config.settings.displayName = "";
   alice.config.settings.registrationIntervalSeconds = 3600;
   alice.config.settings.sipTransportType = SipAccountTransport_TLS;
   alice.config.settings.auth_username = "<EMAIL>";
   alice.config.settings.userAgent = "Softphone - Enterprise Telephony - mac 6.5.3 QA 109868"; // Bria 5 release 5.6.0 stamp 98891";
   const cpc::string aliceExt = "sip:+<EMAIL>;user=phone";
   const cpc::string bobExt = "sip:+<EMAIL>";

   SipConversationSettings settings;
   settings.networkChangeHandoverMode = NetworkChangeHandoverMode_Starcode;
   settings.networkChangeHandoverStarcode = starcode.c_str();
   std::stringstream starcodeUrl;
   starcodeUrl << "sip:" << starcode << "@" << alice.config.settings.domain;

   alice.init();
   SipAccountManagerInternal* accountMgr = dynamic_cast<SipAccountManagerInternal*>(alice.account);
   ASSERT_TRUE(accountMgr != NULL);
   accountMgr->setIgnoreNetworkChangeStarcodeFilter(alice.handle, false);
   alice.enable();

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      // Establish call between alice and bob
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->setDefaultSettings(alice.handle, settings);
      alice.conversation->configureMedia(aliceCall, media);
      alice.conversation->addParticipant(aliceCall, bobExt.c_str());
      alice.conversation->start(aliceCall);

      assertNewConversationOutgoing(alice, aliceCall, bobExt.c_str());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCall, true, false);

      alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> wwan;
      wwan.insert("*******");
      alice.network->setMockInterfaces(wwan);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      assertAccountRefreshing(alice);
      assertAccountRegistered(alice);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      // App receives the NewConversationEvent event for the starcode dialog, resulting from the network change
      SipConversationHandle aliceCallToMax = 0;
      {
         NewConversationEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onNewConversation", 15000, AlwaysTruePred(), aliceCallToMax, evt)) << " missed outgoing call event";
         ASSERT_EQ(aliceCall, evt.conversationToReplace) << " replaced conversation handle does not match";
         ASSERT_EQ(ConversationType_OutgoingNetworkChangeHandover, evt.conversationType) << " conversation type does not match with outgoing";
         ASSERT_EQ(bob.config.uri(), evt.remoteAddress) << " remote-address starcode does not match";
      }

      assertConversationMediaChanged(alice, aliceCallToMax, MediaDirection_ReceiveOnly);
      assertConversationStateChanged(alice, aliceCallToMax, ConversationState_Connected);

      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      alice.conversationEvents->clearCommands();
      alice.accountEvents->clearCommands();
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertMediaFlowing(alice, aliceCallToMax, true, false);

      std::this_thread::sleep_for(std::chrono::milliseconds(30000));
      assertSuccess(alice.conversation->end(aliceCallToMax));
      assertConversationEnded(alice, aliceCallToMax, ConversationEndReason_UserTerminatedLocally);
   });

   waitForMs(aliceEvents, std::chrono::milliseconds(120000));
}

TEST_F(NetworkChangeTest, DISABLED_IncomingCallMultipleTimes)
{
   TestAccount alice("***********", Account_NoInit);

   alice.config.settings.username = "***********";
   alice.config.settings.password = "WaMc7wXYVeGf";
   // alice.config.settings.username = "***********";
   // alice.config.settings.password = "87RU3mygolnP";
   alice.config.settings.domain = "bstnma-ncg32.mobilevoiplive.com";
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.useRport = false;
   alice.config.settings.outboundProxy = "";
   alice.config.settings.userAgent = "Softphone - Enterprise Telephony";

   const cpc::string bobExt = "sip:<EMAIL>";

   /*
   alice.config.settings.domain = "ser.diesel.counterpath.net"; // opsip.silverstar.counterpath.net";
   alice.config.settings.username = "1234";
   alice.config.settings.password = "1234";
   alice.config.settings.auth_username = "1234";
   alice.config.settings.sipTransportType = SipAccountTransport_TCP;
   alice.config.settings.useRport = true;
   alice.config.settings.useOutbound = true;
   alice.config.settings.useInstanceId = true;
   alice.config.settings.outboundProxy = "ser.diesel.counterpath.net"; // opsip.silverstar.counterpath.net";
   alice.config.settings.enableNat64Support = true;
   alice.config.settings.sourceAddress = "***************"; */

   alice.init();
   SipAVConversationManagerInterface* convMgr = dynamic_cast<SipAVConversationManagerInterface*>(alice.conversation);
   ASSERT_TRUE(convMgr != NULL);
   SipConversationSettings settings;
   convMgr->setDefaultSettings(alice.handle, settings);
   alice.enable();

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));
   bool keepOnListening = true;

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle aliceCall;

      assertNewConversationIncoming_time(alice, &aliceCall, bobExt.c_str(), 240000); // "sip:<EMAIL>", 240000); // "sip:<EMAIL>", 240000);
      assertSuccess(alice.conversation->sendRingingResponse(aliceCall));
      assertConversationStateChanged(alice, aliceCall, ConversationState_LocalRinging);
      assertSuccess(alice.conversation->accept(aliceCall));

      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      while (keepOnListening)
      {
         assertConversationMediaChangeRequest_time(alice, aliceCall, MediaDirection_SendReceive, 240000);
         assertSuccess(alice.conversation->accept(aliceCall));
         assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      }

      assertConversationEnded_time(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely, 90000);
   });

   waitForMs(aliceEvents, std::chrono::milliseconds(100 * 120000));
}

TEST_F(NetworkChangeTest, TransportQueryAfterPhoneStart) 
{
   TestAccount alice("alice", Account_NoInit);
   alice.config.phoneInitConnectionPrefs.networkChangeManagerType = ConnectionPreferences::NetworkChangeManagerType_PlatformDefault;
   alice.init();

   ASSERT_NE(TransportNone, alice.networkChangePublic->networkTransport());
}

TEST_F(NetworkChangeTest, TransportQueryAfterNetworkChangeRestart) 
{
   TestAccount alice("alice", Account_NoInit);
   alice.config.phoneInitConnectionPrefs.networkChangeManagerType = ConnectionPreferences::NetworkChangeManagerType_PlatformDefault;
   alice.init();

   ASSERT_NE(TransportNone, alice.networkChangePublic->networkTransport());

   ((NetworkChangeManagerInterface*)alice.networkChangePublic)->stop();
   ((NetworkChangeManagerInterface*)alice.networkChangePublic)->start();

   ASSERT_NE(TransportNone, alice.networkChangePublic->networkTransport());
}

TEST_F(NetworkChangeTest, compareInterfaceLists) 
{
   std::set<resip::Data> oldIfs, newIfs;
   NetworkChangeEvent params;

   oldIfs.insert("*************");
   oldIfs.insert("*************");
   oldIfs.insert("2345:0425:2CA1:0000:0000:0567:5673:23b5");

   newIfs.insert("*************");

   NetworkChangeManagerImpl::compareInterfaceLists(oldIfs, newIfs, params.interfacesUp, params.interfacesDown);
   ASSERT_EQ(0, params.interfacesUp.size());
   ASSERT_EQ(2, params.interfacesDown.size());
   ASSERT_EQ("*************", params.interfacesDown[0]);
   ASSERT_EQ("2345:0425:2CA1:0000:0000:0567:5673:23b5", params.interfacesDown[1]);
   
   std::set<resip::Data> oldIfs1, newIfs1;
   NetworkChangeEvent params1;

   oldIfs1.insert("*************");
   oldIfs1.insert("2345:0425:2CA1:0000:0000:0567:5673:23b5");

   newIfs1.insert("*************");
   newIfs1.insert("*************");

   NetworkChangeManagerImpl::compareInterfaceLists(oldIfs1, newIfs1, params1.interfacesUp, params1.interfacesDown);
   ASSERT_EQ(1, params1.interfacesUp.size());
   ASSERT_EQ("*************", params1.interfacesUp[0]);
   ASSERT_EQ(1, params1.interfacesDown.size());
   ASSERT_EQ("2345:0425:2CA1:0000:0000:0567:5673:23b5", params1.interfacesDown[0]);
}

}  // namespace

#endif // CPCAPI2_BRAND_NETWORK_CHANGE_MODULE
