#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#include "cpcapi2_test_fixture.h"
#include "test_account_events.h"
#include "test_call_events.h"
#include "test_events.h"
#include "impl/util/DeviceInfo.h"

#include "repro/Proxy.hxx"
#include "resip/stack/Helper.hxx"
#include "impl/account/SipAccountHandlerInternal.h"
#include "impl/account/CPDialogDnsResultManager.h"
#include <rutil/dns/ares/ares.h>

#include "test_framework/sipp_runner.h"
#include "test_framework/dummy_tcplistener.h"

using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::SipConversation;

/*
 * These tests require you to run checked-in copy of the MaraDNS authoritative DNS server with the
 * zone files that are also checked in.
 */


namespace {

class DnsFailoverTest : public CpcapiAutoTest
{

public:

   DnsFailoverTest() {}
   virtual ~DnsFailoverTest() {}

   static void configureAccount(TestAccount& account);

   static void handlePrimaryNotWorkingSecondaryNotWorkingOverrideTimerF(TestAccount& account);

   void FailoverConnectionProblem_multiRepro_WithCall(SipAccountTransportType transportType);

};


repro::ReproRunner* runRepro(cpc::string config)
{
   repro::ReproRunner* res = new repro::ReproRunner();
   const char* const reproArgs[] = { "" };
   resip::Data configFile = (TestEnvironmentConfig::testResourcePath() + config).c_str();
   res->run(1, reproArgs, configFile);
   return res;
}

bool runOriginalRepro()
{
   const char* const reproArgs[] = { "" };
   resip::Data configFile = (TestEnvironmentConfig::testResourcePath() + "repro.config").c_str();
   return ReproHolder::instance()->run(1, reproArgs, configFile);
}

std::time_t getTime()
{
   std::chrono::time_point<std::chrono::system_clock> startTime = std::chrono::system_clock::now();
   std::time_t ttp = std::chrono::system_clock::to_time_t(startTime);
   return ttp;
}

bool checkDnsServer()
{
#ifdef ANDROID
   char* dnsServer = getenv("CPCAPI2_DNS_SERVER");
   // verify that the external DNS server has been set
   if (dnsServer == NULL || strlen(dnsServer) == 0)
   {
      return false;
   }
#endif
   return true;
}

bool hasUnsupportedTcpConnectionTimeout()
{
#ifdef ANDROID
   cpc::string deviceModel;
   if (DeviceInfo::getDeviceModel(deviceModel) == kSuccess)
   {
      safeCout("Device model: " << deviceModel)
      if (deviceModel == "CT40")
      {
         // Android/Linux kernel configured for short TCP connection timeouts can cause this test to fail;
         // e.g. the CT40XP running Android 11 has a value of 8 for /proc/sys/net/ipv4/tcp_retries2.
         // note that this /proc/sys entry does not seem to be readable by an app (but is via adb)
         return true;
      }
   }
#else
#endif
   return false;
}

void DnsFailoverTest::handlePrimaryNotWorkingSecondaryNotWorkingOverrideTimerF(TestAccount& alice)
{
   alice.account->enable(alice.handle);
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
      std::time_t startTime = getTime();

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged", (int)(2 * alice.config.settings.overrideMsecsTimerF), CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged", (int)(2 * alice.config.settings.overrideMsecsTimerF), CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
      ASSERT_EQ(408, evt.signalingStatusCode);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Dns_Lookup, evt.reason);
      ASSERT_LE((getTime() - startTime) * 1000, (2 * alice.config.settings.overrideMsecsTimerF * 1.25)); // e.g. with 4 second TimerF, Expected Time: 0.5 + 1.0 + 2.0
      
      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged", (int)(2 * alice.config.settings.overrideMsecsTimerF), CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged", (int)(2 * alice.config.settings.overrideMsecsTimerF), CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
      ASSERT_EQ(408, evt.signalingStatusCode);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Dns_Lookup, evt.reason);
      ASSERT_LE((getTime() - startTime) * 1000, (4 * alice.config.settings.overrideMsecsTimerF * 1.25));

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged", (int)(2 * alice.config.settings.overrideMsecsTimerF), CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged", (int)(2 * alice.config.settings.overrideMsecsTimerF), CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
   }
}

TEST_F(DnsFailoverTest, PrimaryWorkingSecondaryNot) {
   ASSERT_TRUE(checkDnsServer());
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.domain = "127.0.0.1";
   alice.config.settings.outboundProxy = "cpcapi2.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_Auto; 
   alice.enable();
}

TEST_F(DnsFailoverTest, PrimaryNotWorkingSecondaryWorking) {
   ASSERT_TRUE(checkDnsServer());
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.domain = "127.0.0.1";
   alice.config.settings.outboundProxy = "pribroken.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.init();
   alice.account->enable(alice.handle);
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
		CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

		ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
			60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);

		ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
			60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
      ASSERT_EQ(408, evt.signalingStatusCode);
      ASSERT_NE(0, evt.failureRetryAfterSecs);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Dns_Lookup, evt.reason);

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
			90000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
		ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
			90000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
		ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   alice.disable();
}

const char* getAccountStatus(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status status)
{
   const char* ss = "";
   switch (status)
   {
      case CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered: ss = "Registered"; break;
      case CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure: ss = "Failure"; break;
      case CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Unregistered: ss = "Unregistered"; break;
      case CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering: ss = "Registering"; break;
      case CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Unregistering: ss = "Unregistering"; break;
      case CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister: ss = "WaitingToRegister"; break;
      case CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Refreshing: ss = "Refreshing"; break;
      default: ss = "-"; break;
   }
   return ss;
}

TEST_F(DnsFailoverTest, PrimaryNotWorkingSecondaryWorkingSamePriority) {
   ASSERT_TRUE(checkDnsServer());
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.domain = "127.0.0.1";
   alice.config.settings.outboundProxy = "pribroken2.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.init();
   alice.account->enable(alice.handle);
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
		CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

      // this unit test confirms that after a failed attempt the SDK will eventually try 
      // a different option with the same priority, but it does NOT check that after a 
      // failure the SDK will try a different option as the next attempt, it may attempt 
      // the same option again since I believe the logic is to choose an option randomly

      int attempts = 0;
      bool hadSuccess = false;
      bool hadFailure = false;
      do
      {
         ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
            60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(alice.handle, h);
         const char* temp = getAccountStatus(evt.accountStatus);
         safeCout("XYZ: Account status " << temp << 
            ", changed code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
         if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister && 
            evt.reason == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Dns_Lookup)
         {
            safeCout("XYZ: Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
            hadFailure = true;
            ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Dns_Lookup, evt.reason);
         }
         else if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered)
         {
            // we are looking for a failure followed by a success to show that the SDK will try the alternate
            if (hadFailure)
            {
               safeCout("XYZ: Registration succeeded after failure");
               hadSuccess = true;
            }
            else
            {
               safeCout("XYZ: Registration succeeded but we have not seen a failure yet, so try again");

               // try again
               std::this_thread::sleep_for(std::chrono::milliseconds(1000));
               alice.account->disable(alice.handle);

               ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
                  5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
               ASSERT_EQ(alice.handle, h);
               ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Unregistering, evt.accountStatus);

               ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
                  5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
               ASSERT_EQ(alice.handle, h);
               ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Unregistered, evt.accountStatus);

               alice.account->enable(alice.handle);
            }
         }
      } while ((!hadSuccess || !hadFailure) && attempts++ < 5);

      ASSERT_EQ(hadSuccess, true);
      ASSERT_EQ(hadFailure, true);
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   alice.disable();
}

TEST_F(DnsFailoverTest, PrimaryNotWorkingNoSecondary) {
	ASSERT_TRUE(checkDnsServer());
	checkDnsServer();
	TestAccount alice("alice", Account_NoInit);
	alice.config.settings.domain = "127.0.0.1";
	alice.config.settings.outboundProxy = "pribrokennosec.local";
	alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
	alice.config.settings.sipTransportType = SipAccountTransport_Auto;
	alice.init();
	alice.account->enable(alice.handle);
	{
		CPCAPI2::SipAccount::SipAccountHandle h;
		CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

		ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
			60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
		ASSERT_EQ(alice.handle, h);
		if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
		{
			safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
		}
		ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);

		ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
			60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
		ASSERT_EQ(alice.handle, h);
		if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
		{
			safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
		}
		ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
		ASSERT_EQ(408, evt.signalingStatusCode);
		ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Local_Timeout, evt.reason);
	}

	std::this_thread::sleep_for(std::chrono::milliseconds(2000));

	alice.disable();
}

TEST_F(DnsFailoverTest, DnsRecordInvalidDomain) {
   ASSERT_TRUE(checkDnsServer());
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.domain = "127.0.0.1";
   alice.config.settings.outboundProxy = "nodomain.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.init();
   alice.account->enable(alice.handle);
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
      
      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
                                             60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      safeCout("Event received: Status: " << evt.accountStatus << " Reason: " << evt.reason);
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);
         
      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
                                             60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      safeCout("Event received: Status: " << evt.accountStatus << " Reason: " << evt.reason);
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
      ASSERT_EQ(503, evt.signalingStatusCode);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Dns_Lookup, evt.reason);
   }
      
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      
   alice.disable();
}

TEST_F(DnsFailoverTest, DnsRecordInvalidTransport) {
   ASSERT_TRUE(checkDnsServer());
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.domain = "127.0.0.1";
   alice.config.settings.outboundProxy = "notransport.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.init();
   alice.account->enable(alice.handle);
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
         
      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
                                             60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);
         
      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
                                             60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
      ASSERT_EQ(503, evt.signalingStatusCode);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Dns_Lookup, evt.reason);
   }
      
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      
   alice.disable();
}
   
// Test case applies to all transport scenarios, i.e. UDP/TCP/TLS, because the transport to use
// for the register message is derived from the DNS responses. So regardless of the intended
// transport, the stack generated 503 response (No DNS results with 396 warning code) will be
// the common response in all cases, as the DNS server itself is un-responsive.
TEST_F(DnsFailoverTest, DnsNotWorking) {
   ASSERT_TRUE(checkDnsServer());
   // Warning: Changing any of the const values may potentially require changes within the test-case
   const int minIntervalSecs = 5;
   
   const int dnsFailureSecs = 32;

   
   const int maxIntervalSecs = 30;
   const int initialAttempts = 3;
   const int maxAttempts = 3;
   
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.domain = "127.0.0.1";
   alice.config.settings.outboundProxy = "pribrokennosec.local";
#ifndef __APPLE__
   alice.config.settings.nameServers.push_back("*********"); // should not be routable -- RFC 5737
#else
   // on mac, ********* results in immediate failure whereas *********** times out (what we want).
   // ********* was previously used here, but we have since started bringing this up via ifconfig alias
   // for PTT tests.
   // https://en.wikipedia.org/wiki/Reserved_IP_addresses
   alice.config.settings.nameServers.push_back("***********");
#endif
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_V4;
   alice.config.settings.sourceAddress = "127.0.0.1"; // Ensures that V6 transports are not created as they trigger AAAA queries when attempting to detect NAT64
   alice.config.settings.minimumRegistrationIntervalSeconds = minIntervalSecs;
   alice.config.settings.maximumRegistrationIntervalSeconds = maxIntervalSecs;
   alice.init();

   // OBELISK-2852 - Make sure account and it's audio interface is initialized on windows before enabling account.
#ifdef WIN32 
   std::this_thread::sleep_for(std::chrono::milliseconds(8000));
#endif
   // Would be better to pull this data from the SDK rather than base the validation on timers,
   // currently providing a 4 second buffer before declaring a failure
   alice.account->enable(alice.handle);
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
      
      std::time_t startTime = getTime();
      
      for (int attempts = 1; attempts <= maxAttempts; attempts++)
      {
         if (attempts <= initialAttempts)
         {
            //
            // Verify that the failure retry duration stays the same for the initial attempts
            //
            if (attempts == 1)
            {
               ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
                                                     2000, CPCAPI2::test::AlwaysTruePred(), h, evt));
            }
            else
            {
               ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
                                                     ((minIntervalSecs + 2) * 1000), CPCAPI2::test::AlwaysTruePred(), h, evt));
               ASSERT_LE((minIntervalSecs - 2), (getTime() - startTime));
            }
         }
         else if (attempts == (initialAttempts + 1))
         {
            //
            // Verify that the failure retry duration starts increasing after reaching the initial attempt threshold
            //
            ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
                                                  (((2 * minIntervalSecs) + 2) * 1000), CPCAPI2::test::AlwaysTruePred(), h, evt));
            ASSERT_LE(((2 * minIntervalSecs) - 2), (getTime() - startTime));
         }
         else if (attempts == (initialAttempts + 2))
         {
            //
            // Verify that the failure duration continues to increase
            //
            ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
                                                  (((2 * 2 * minIntervalSecs) + 2) * 1000), CPCAPI2::test::AlwaysTruePred(), h, evt));
            ASSERT_LE(((2 * 2 * minIntervalSecs) - 2), (getTime() - startTime));
         }
         else if (attempts == (initialAttempts + 3))
         {
            //
            // Verify that the failure duration is set to the max limit, if the duration increment is larger than the max limit
            //
            ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
                                                  ((maxIntervalSecs + 2) * 1000), CPCAPI2::test::AlwaysTruePred(), h, evt));
            ASSERT_LE((maxIntervalSecs - 2), (getTime() - startTime));
         }
         else
         {
            //
            // Verify that the failure durations stops increasing after reaching the max limit
            //
            ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
                                                  ((maxIntervalSecs + 2) * 1000), CPCAPI2::test::AlwaysTruePred(), h, evt));
            ASSERT_LE((maxIntervalSecs - 2), (getTime() - startTime));
         }
         
         ASSERT_EQ(alice.handle, h);
         if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
         {
            safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
         }
         ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);
         
         startTime = getTime();
         ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
                                               ((dnsFailureSecs + 2) * 1000), CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_LE((dnsFailureSecs - 2), (getTime() - startTime));
         ASSERT_EQ(alice.handle, h);
         if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
         {
            safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
         }
         ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
         ASSERT_EQ(503, evt.signalingStatusCode);
         
         startTime = getTime();
      }
   }
   
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      
   alice.disable();
}
   
// primary SIP server accepts TCP conncetion but does not respond to SIP REGISTER;
// no secondary SIP server exists
TEST_F(DnsFailoverTest, PrimaryNotWorkingNoSecondaryTCP) {
	
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();

   DummyTcpListener::Config dtConfig;
   dtConfig.port = 6060; // match singletcpsrvrecord.local
   // to ensure we have a timeout at the SIP transaction level and generate an internal 408
   // vs TCP connection rejection and internal 503, we spin up the dummy TCP listener.
   DummyTcpListener tcpListener(dtConfig);
   ASSERT_GT(tcpListener.start(), 0);
   
   TestAccount alice("alice", Account_NoInit);
	alice.config.settings.domain = "127.0.0.1";
	alice.config.settings.outboundProxy = "singletcpsrvrecord.local";
	alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
	alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.overrideMsecsTimerF = 10; // make test run more quickly
	alice.init();
	alice.account->enable(alice.handle);
	{
		CPCAPI2::SipAccount::SipAccountHandle h;
		CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

		ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
			60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
		ASSERT_EQ(alice.handle, h);
		if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
		{
			safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
		}
		ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);

		ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
			60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
		ASSERT_EQ(alice.handle, h);
		if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
		{
			safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
		}
		ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
		ASSERT_EQ(408, evt.signalingStatusCode);
		ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Local_Timeout, evt.reason);
	}

   alice.disable();

   runOriginalRepro();
}

   
// In this scenario, the IP is valid but the port is invalid, which results in some tcp exchange
// but no established connection. This triggers a stack generated 503 response (Transport failure:
// no transports left to try with 397 warning code).
TEST_F(DnsFailoverTest, PrimaryInvalidPortTCP) {
   ASSERT_TRUE(checkDnsServer());
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.domain = "127.0.0.1";
   alice.config.settings.outboundProxy = "priinvalidporttcp.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.init();
   alice.account->enable(alice.handle);
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
      bool bRegisteringReceived = false;
      int iCount = 0;
      
      // Registering status may be received multiple times
      while (iCount < 5)
      {
         ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
                                                60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(alice.handle, h);
         if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
         {
            safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
         }
         
         if (bRegisteringReceived == false)
         {
            ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);
            iCount++;
            bRegisteringReceived = true;
         }
         else
         {
            if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering)
            {
               iCount++;
            }
            else
            {
               break;
            }
         }
      }
      
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
      
      // With TCP, the 503 response also comes with a Warning header code 397
      ASSERT_EQ(503, evt.signalingStatusCode);
      
      // Need to verify the reason that now we also get Reason_No_Route_To_Host as the reason code,
      // do we still need to check for Reason_Local_Timeout
      // ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Local_Timeout, evt.reason);
      ASSERT_TRUE((evt.reason == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Local_Timeout)
         || (evt.reason == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_No_Route_To_Host));
   }
      
   // std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      
   alice.disable();
}
   
   
TEST_F(DnsFailoverTest, PrimaryNotWorkingSecondaryNotWorking) {
	ASSERT_TRUE(checkDnsServer());
	TestAccount alice("alice", Account_NoInit);
	alice.config.settings.domain = "127.0.0.1";
	alice.config.settings.outboundProxy = "pribrokensecbroken.local";
	alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
	alice.config.settings.sipTransportType = SipAccountTransport_Auto;
	alice.init();
	alice.account->enable(alice.handle);
	{
		CPCAPI2::SipAccount::SipAccountHandle h;
		CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

		ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
			60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
		ASSERT_EQ(alice.handle, h);
		if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
		{
			safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
		}
		ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);

		ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
			60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
		ASSERT_EQ(alice.handle, h);
		if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
		{
			safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
		}
		ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
		ASSERT_EQ(408, evt.signalingStatusCode);
		ASSERT_NE(0, evt.failureRetryAfterSecs);
		ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Dns_Lookup, evt.reason);

		ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
			60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
		ASSERT_EQ(alice.handle, h);
		if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
		{
			safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
		}
		ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);

		ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
			60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
		ASSERT_EQ(alice.handle, h);
		if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
		{
			safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
		}
		ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
		ASSERT_EQ(408, evt.signalingStatusCode);
		ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Local_Timeout, evt.reason);
	}

	std::this_thread::sleep_for(std::chrono::milliseconds(2000));

	alice.disable();
}

TEST_F(DnsFailoverTest, PrimaryNotWorkingSecondaryNotWorkingTCP) {
	ASSERT_TRUE(checkDnsServer());

   if (hasUnsupportedTcpConnectionTimeout())
   {
      GTEST_SKIP() << "This device has short TCP connection timeouts unsupported by this test";
      return;
   }

	TestAccount alice("alice", Account_NoInit);
	alice.config.settings.domain = "127.0.0.1";
	alice.config.settings.outboundProxy = "pribrokensecbrokentcp.local";
	alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
	alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   // seen on some Linux test runs: REGISTER fails with internally generated 503 w/ Warning: 397 header
   // with "Exception on socket 103 code: 101; closing connection" also logged.
   // SipAccountImpl has code to silently retry in this case, which ends up delays any account status updates.
   // We'll lower TimerF here so we get through this silent retry more quickly
   alice.config.settings.overrideMsecsTimerF = 8000;
	alice.init();
	alice.account->enable(alice.handle);
	{
		CPCAPI2::SipAccount::SipAccountHandle h;
		CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

		ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
			60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
		ASSERT_EQ(alice.handle, h);
		if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
		{
			safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
		}
		ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);

		ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
			60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
		ASSERT_EQ(alice.handle, h);
		if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
		{
			safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
		}
		ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
		ASSERT_EQ(408, evt.signalingStatusCode);
      // Need to verify the code-path or environment setup, getting Reason_Dns_Lookup on MAC, previous tests
      // with PC always returned Reason_Local_Timeout, have to re-test on PC to verify if that is reproducible
		// ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Local_Timeout, evt.reason);
      ASSERT_TRUE(((evt.reason == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Local_Timeout)
         || (evt.reason == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Dns_Lookup)));
	}

	std::this_thread::sleep_for(std::chrono::milliseconds(2000));

	alice.disable();
}

TEST_F(DnsFailoverTest, PrimaryNotWorkingSecondaryWorkingManualRereg) {
   ASSERT_TRUE(checkDnsServer());
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.domain = "127.0.0.1";
   alice.config.settings.outboundProxy = "pribroken.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_Auto; 
   alice.init();
   alice.account->enable(alice.handle);
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
		CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

		ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
			60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);
      
		ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
			60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
	  ASSERT_EQ(408, evt.signalingStatusCode);

      alice.account->disable(alice.handle);

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
			500, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
		ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Unregistering, evt.accountStatus);

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
			35000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
		ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Unregistered, evt.accountStatus);

      alice.account->enable(alice.handle);

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
			60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
		ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
			60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
		ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   alice.disable();
}


TEST_F(DnsFailoverTest, DISABLED_PrimaryNotWorkingSecondaryWorkingTCP) {
   ASSERT_TRUE(checkDnsServer());
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.domain = "127.0.0.1";
   alice.config.settings.outboundProxy = "pribrokentcp.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_Auto; 
   alice.init();
   alice.account->enable(alice.handle);
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
		CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

		ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
			60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);
      
      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
                                            60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
                                            60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
			90000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
		ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   alice.disable();
}

TEST_F(DnsFailoverTest, DISABLED_BasicCallRejectIMS) {
   ASSERT_TRUE(checkDnsServer());
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.domain = "127.0.0.1";
   alice.config.settings.outboundProxy = "priudpsectcp.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;

   alice.config.settings.reRegisterOnResponseTypes.push_back(SipResponseType("INVITE", 503));
   alice.config.settings.reRegisterOnResponseTypes.push_back(SipResponseType("INVITE", 408));
   alice.config.settings.reRegisterOnResponseTypes.push_back(SipResponseType("INVITE", 504));
   alice.config.settings.reRegisterOnResponseTypes.push_back(SipResponseType("INVITE", 407));
   
   alice.init();
   alice.account->enable(alice.handle);
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
		CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
			60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);

		ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
			60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
		ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
   }

   TestAccount bob("bob");

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationEnded(alice, aliceCall, ConversationEndReason_ServerRejected);

      // Expect registration success
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

      ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged",
         20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(407, evt.signalingStatusCode);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);

      ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged",
         20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);

      ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged",
         20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(200, evt.signalingStatusCode);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      // Bob pretends to be an IMS core and rejects the call with a funky response code
      assertSuccess(bob.conversation->reject(bobCall, 407));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor2(aliceEvents, bobEvents);
}

//V6 v

TEST_F(DnsFailoverTest, IPv6_PrimaryWorkingSecondaryNot)
{
   ASSERT_TRUE(checkDnsServer());
   TestAccount alice("alice", Account_NoInit);
   //alice.config.settings.domain = "::1";
   alice.config.settings.outboundProxy = "cpcapi2.ipv6.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_V6;
   alice.enable();
}

TEST_F(DnsFailoverTest, IPv6_PrimaryNotWorkingSecondaryWorking)
{
   ASSERT_TRUE(checkDnsServer());
   TestAccount alice("alice", Account_NoInit);
   //alice.config.settings.domain = "::1";
   alice.config.settings.outboundProxy = "pribroken.ipv6.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_V6;
   alice.init();
   alice.account->enable(alice.handle);
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
      ASSERT_EQ(408, evt.signalingStatusCode);

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   alice.disable();
}

TEST_F(DnsFailoverTest, IPv6_PrimaryNotWorkingNoSecondary)
{
   ASSERT_TRUE(checkDnsServer());
   TestAccount alice("alice", Account_NoInit);
   //alice.config.settings.domain = "::1";
   alice.config.settings.outboundProxy = "pribrokennosec.ipv6.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_V6;
   alice.init();
   alice.account->enable(alice.handle);
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);

      //Disabled to pass with no IPv6 transport
     /* ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
      ASSERT_EQ(408, evt.signalingStatusCode);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Local_Timeout, evt.reason);*/
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   alice.disable();
}

TEST_F(DnsFailoverTest, IPv6_DnsRecordInvalidDomain)
{
   ASSERT_TRUE(checkDnsServer());
   TestAccount alice("alice", Account_NoInit);
   //alice.config.settings.domain = "::1";
   alice.config.settings.outboundProxy = "nodomain.ipv6.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_V6;
   alice.init();
   alice.account->enable(alice.handle);
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      safeCout("Event received: Status: " << evt.accountStatus << " Reason: " << evt.reason);
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      safeCout("Event received: Status: " << evt.accountStatus << " Reason: " << evt.reason);
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
      ASSERT_EQ(503, evt.signalingStatusCode);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Dns_Lookup, evt.reason);
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   alice.disable();
}

TEST_F(DnsFailoverTest, IPv6_DnsRecordInvalidTransport)
{
   ASSERT_TRUE(checkDnsServer());
   TestAccount alice("alice", Account_NoInit);
   //alice.config.settings.domain = "::1";
   alice.config.settings.outboundProxy = "notransport.ipv6.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_V6;
   alice.init();
   alice.account->enable(alice.handle);
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
      ASSERT_EQ(503, evt.signalingStatusCode);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Dns_Lookup, evt.reason);
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   alice.disable();
}

// Test case applies to all transport scenarios, i.e. UDP/TCP/TLS, because the transport to use
// for the register message is derived from the DNS responses. So regardless of the intended
// transport, the stack generated 503 response (No DNS results with 396 warning code) will be
// the common response in all cases, as the DNS server itself is un-responsive.
TEST_F(DnsFailoverTest, IPv6_DnsNotWorking)
{
   ASSERT_TRUE(checkDnsServer());
   // Warning: Changing any of the const values may potentially require changes within the test-case
   const int minIntervalSecs = 5;

   const int dnsFailureSecs = 32;

   const int maxIntervalSecs = 30;
   const int initialAttempts = 3;
   const int maxAttempts = 7;

   TestAccount alice("alice", Account_NoInit);
   //alice.config.settings.domain = "::1";
   alice.config.settings.outboundProxy = "pribrokennosec.ipv6.local";
   alice.config.settings.nameServers.push_back("0100::"); // should not be routable -- discard address
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_V6;
   //alice.config.settings.sourceAddress = "::1";
   alice.config.settings.minimumRegistrationIntervalSeconds = minIntervalSecs;
   alice.config.settings.maximumRegistrationIntervalSeconds = maxIntervalSecs;
   alice.init();

   // OBELISK-2852 - Make sure account and it's audio interface is initialized on windows before enabling account.
#ifdef WIN32 
   std::this_thread::sleep_for(std::chrono::milliseconds(8000));
#endif
   // Would be better to pull this data from the SDK rather than base the validation on timers,
   // currently providing a 4 second buffer before declaring a failure
   alice.account->enable(alice.handle);
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

      std::time_t startTime = getTime();

      for (int attempts = 1; attempts <= maxAttempts; attempts++)
      {
         if (attempts <= initialAttempts)
         {
            //
            // Verify that the failure retry duration stays the same for the initial attempts
            //
            if (attempts == 1)
            {
               ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
                  2000, CPCAPI2::test::AlwaysTruePred(), h, evt));
            }
            else
            {
               ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
                  ((minIntervalSecs + 2) * 1000), CPCAPI2::test::AlwaysTruePred(), h, evt));
               ASSERT_LE((minIntervalSecs - 2), (getTime() - startTime));
            }
         }
         else if (attempts == (initialAttempts + 1))
         {
            //
            // Verify that the failure retry duration starts increasing after reaching the initial attempt threshold
            //
            ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
               (((2 * minIntervalSecs) + 2) * 1000), CPCAPI2::test::AlwaysTruePred(), h, evt));
            ASSERT_LE(((2 * minIntervalSecs) - 2), (getTime() - startTime));
         }
         else if (attempts == (initialAttempts + 2))
         {
            //
            // Verify that the failure duration continues to increase
            //
            ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
               (((2 * 2 * minIntervalSecs) + 2) * 1000), CPCAPI2::test::AlwaysTruePred(), h, evt));
            ASSERT_LE(((2 * 2 * minIntervalSecs) - 2), (getTime() - startTime));
         }
         else if (attempts == (initialAttempts + 3))
         {
            //
            // Verify that the failure duration is set to the max limit, if the duration increment is larger than the max limit
            //
            ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
               ((maxIntervalSecs + 2) * 1000), CPCAPI2::test::AlwaysTruePred(), h, evt));
            ASSERT_LE((maxIntervalSecs - 2), (getTime() - startTime));
         }
         else
         {
            //
            // Verify that the failure durations stops increasing after reaching the max limit
            //
            ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
               ((maxIntervalSecs + 2) * 1000), CPCAPI2::test::AlwaysTruePred(), h, evt));
            ASSERT_LE((maxIntervalSecs - 2), (getTime() - startTime));
         }

         ASSERT_EQ(alice.handle, h);
         if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
         {
            safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
         }
         ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);

         startTime = getTime();
         ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
            ((dnsFailureSecs + 2) * 1000), CPCAPI2::test::AlwaysTruePred(), h, evt));
         // Commented out to pass with no v6 transport
         //ASSERT_LE((dnsFailureSecs - 2), (getTime() - startTime));
         ASSERT_EQ(alice.handle, h);
         if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
         {
            safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
         }
         ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
         ASSERT_EQ(503, evt.signalingStatusCode);

         startTime = getTime();
      }
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   alice.disable();
}

TEST_F(DnsFailoverTest, IPv6_PrimaryNotWorkingNoSecondaryTCP)
{
   ASSERT_TRUE(checkDnsServer());
   TestAccount alice("alice", Account_NoInit);
   //alice.config.settings.domain = "::1";
   alice.config.settings.outboundProxy = "pribrokennosectcp.ipv6.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_V6;
   alice.init();
   alice.account->enable(alice.handle);
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
      bool bRegisteringReceived = false;
      int iCount = 0;

      // Registering status may be received multiple times
      while (iCount < 5)
      {
         ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
            60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(alice.handle, h);
         if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
         {
            safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
         }

         if (bRegisteringReceived == false)
         {
            ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);
            iCount++;
            bRegisteringReceived = true;
         }
         else
         {
            if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering)
            {
               iCount++;
            }
            else
            {
               break;
            }
         }
      }

      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);

      // Need to verify the code-path or environment setup, getting 408 timeout on MAC, previous tests
      // with PC always returned 503, have to re-test on PC to verify if that is reproducible
      // With TCP, the 503 response also comes with a Warning header code 397
      // ASSERT_EQ(503, evt.signalingStatusCode);
      ASSERT_TRUE(((evt.signalingStatusCode == 503) || (evt.signalingStatusCode == 408)));

      // Need to verify the reason that now we also get Reason_No_Route_To_Host as the reason code,
      // do we still need to check for Reason_Local_Timeout
      // ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Local_Timeout, evt.reason);
      ASSERT_TRUE((evt.reason == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Local_Timeout)
         || (evt.reason == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_No_Route_To_Host));
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
}


// In this scenario, the IP is valid but the port is invalid, which results in some tcp exchange
// but no established connection. This triggers a stack generated 503 response (Transport failure:
// no transports left to try with 397 warning code).
TEST_F(DnsFailoverTest, IPv6_PrimaryInvalidPortTCP)
{
   ASSERT_TRUE(checkDnsServer());
   TestAccount alice("alice", Account_NoInit);
   //alice.config.settings.domain = "::1";
   alice.config.settings.outboundProxy = "priinvalidporttcp.ipv6.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_V6;
   alice.init();
   alice.account->enable(alice.handle);
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
      bool bRegisteringReceived = false;
      int iCount = 0;

      // Registering status may be received multiple times
      while (iCount < 5)
      {
         ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
            60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(alice.handle, h);
         if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
         {
            safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
         }

         if (bRegisteringReceived == false)
         {
            ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);
            iCount++;
            bRegisteringReceived = true;
         }
         else
         {
            if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering)
            {
               iCount++;
            }
            else
            {
               break;
            }
         }
      }

      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);

      // With TCP, the 503 response also comes with a Warning header code 397
      ASSERT_EQ(503, evt.signalingStatusCode);

      // Need to verify the reason that now we also get Reason_No_Route_To_Host as the reason code,
      // do we still need to check for Reason_Local_Timeout
      // ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Local_Timeout, evt.reason);
      ASSERT_TRUE((evt.reason == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Local_Timeout)
         || (evt.reason == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_No_Route_To_Host));
   }

   // std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   alice.disable();
}


TEST_F(DnsFailoverTest, IPv6_PrimaryNotWorkingSecondaryNotWorking)
{
   ASSERT_TRUE(checkDnsServer());
   TestAccount alice("alice", Account_NoInit);
   //alice.config.settings.domain = "::1";
   alice.config.settings.outboundProxy = "pribrokensecbroken.ipv6.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_V6;
   alice.init();
   alice.account->enable(alice.handle);
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);

      //Commented out to pass with no IPv6 transport
      /*ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
      ASSERT_EQ(408, evt.signalingStatusCode);
      ASSERT_NE(0, evt.failureRetryAfterSecs);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Dns_Lookup, evt.reason);

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
      ASSERT_EQ(408, evt.signalingStatusCode);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Local_Timeout, evt.reason);*/
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   alice.disable();
}

TEST_F(DnsFailoverTest, IPv6_PrimaryNotWorkingSecondaryNotWorkingTCP)
{
   ASSERT_TRUE(checkDnsServer());
   TestAccount alice("alice", Account_NoInit);
   //alice.config.settings.domain = "::1";
   alice.config.settings.outboundProxy = "pribrokensecbrokentcp.ipv6.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_V6;
   alice.init();
   alice.account->enable(alice.handle);
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      //ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
      //ASSERT_EQ(408, evt.signalingStatusCode);
      // Need to verify the code-path or environment setup, getting Reason_Dns_Lookup on MAC, previous tests
      // with PC always returned Reason_Local_Timeout, have to re-test on PC to verify if that is reproducible
      // ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Local_Timeout, evt.reason);
      /*ASSERT_TRUE(((evt.reason == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Local_Timeout)
         || (evt.reason == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Dns_Lookup)));*/
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   alice.disable();
}

TEST_F(DnsFailoverTest, IPv6_PrimaryNotWorkingSecondaryWorkingManualRereg)
{
   ASSERT_TRUE(checkDnsServer());
   TestAccount alice("alice", Account_NoInit);
   //alice.config.settings.domain = "::1";
   alice.config.settings.outboundProxy = "pribroken.ipv6.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_V6;
   alice.init();
   alice.account->setTFTimerValueMs(10000);
   alice.account->enable(alice.handle);
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
      ASSERT_EQ(408, evt.signalingStatusCode);

      alice.account->disable(alice.handle);

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Unregistering, evt.accountStatus);

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         35000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Unregistered, evt.accountStatus);

      alice.account->enable(alice.handle);

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
      ASSERT_EQ(408, evt.signalingStatusCode);

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   alice.disable();
}


TEST_F(DnsFailoverTest, IPv6_PrimaryNotWorkingSecondaryWorkingTCP)
{
   ASSERT_TRUE(checkDnsServer());
   TestAccount alice("alice", Account_NoInit);
   //alice.config.settings.domain = "::1";
   alice.config.settings.outboundProxy = "pribrokentcp.ipv6.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_V6;
   alice.init();
   alice.account->enable(alice.handle);
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);
      /*
      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);*/

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         90000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   alice.disable();
}

TEST_F(DnsFailoverTest, DISABLED_IPv6_BasicCallRejectIMS)
{
   ASSERT_TRUE(checkDnsServer());
   TestAccount alice("alice", Account_NoInit);
   //alice.config.settings.domain = "::1";
   alice.config.settings.outboundProxy = "priudpsectcp.ipv6.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_V6;

   alice.config.settings.reRegisterOnResponseTypes.push_back(SipResponseType("INVITE", 503));
   alice.config.settings.reRegisterOnResponseTypes.push_back(SipResponseType("INVITE", 408));
   alice.config.settings.reRegisterOnResponseTypes.push_back(SipResponseType("INVITE", 504));
   alice.config.settings.reRegisterOnResponseTypes.push_back(SipResponseType("INVITE", 407));

   alice.init();
   alice.account->enable(alice.handle);
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
   }

   TestAccount bob("bob");

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]() {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationEnded(alice, aliceCall, ConversationEndReason_ServerRejected);

      // Expect registration success
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

      ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged",
         20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(407, evt.signalingStatusCode);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);

      ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged",
         20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);

      ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandler::onAccountStatusChanged",
         20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(200, evt.signalingStatusCode);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      // Bob pretends to be an IMS core and rejects the call with a funky response code
      assertSuccess(bob.conversation->reject(bobCall, 407));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor2(aliceEvents, bobEvents);
}

TEST_F(DnsFailoverTest, PrimaryV6SecondaryV4)
{
   ASSERT_TRUE(checkDnsServer());
   TestAccount alice("alice", Account_NoInit);
   //alice.config.settings.domain = "::1";
   alice.config.settings.outboundProxy = "priv6secv4.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_Auto;
   alice.enable();
}

TEST_F(DnsFailoverTest, PrimaryV4SecondaryV6)
{
   ASSERT_TRUE(checkDnsServer());
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.domain = "127.0.0.1";
   alice.config.settings.outboundProxy = "priv4secv6.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_Auto;
   alice.enable();
}

TEST_F(DnsFailoverTest, PrimaryV6NotWorkingSecondaryV4)
{
   ASSERT_TRUE(checkDnsServer());
   TestAccount alice("alice", Account_NoInit);
   //alice.config.settings.domain = "::1";
   alice.config.settings.outboundProxy = "priv6broken.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_Auto;
   alice.init();
   alice.account->enable(alice.handle);
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);

      /*ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
      ASSERT_EQ(408, evt.signalingStatusCode);
      ASSERT_NE(0, evt.failureRetryAfterSecs);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Dns_Lookup, evt.reason);

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         90000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);*/

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         90000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   alice.disable();
}

TEST_F(DnsFailoverTest, PrimaryV4NotWorkingSecondaryV6)
{
   ASSERT_TRUE(checkDnsServer());
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.domain = "127.0.0.1";
   alice.config.settings.outboundProxy = "priv4broken.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_Auto;
   alice.init();
   alice.account->enable(alice.handle);
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
      ASSERT_EQ(408, evt.signalingStatusCode);
      ASSERT_NE(0, evt.failureRetryAfterSecs);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Dns_Lookup, evt.reason);

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         90000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         90000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   alice.disable();
}

TEST_F(DnsFailoverTest, DnsIPv4ConfigurationMismatch)
{
   ASSERT_TRUE(checkDnsServer());
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.outboundProxy = "priv6.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_V4;
   alice.init();
   alice.account->enable(alice.handle);
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
      bool bRegisteringReceived = false;
      int iCount = 0;

      // Registering status may be received multiple times
      while (iCount < 5)
      {
         ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
            60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(alice.handle, h);
         if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
         {
            safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
         }

         if (bRegisteringReceived == false)
         {
            ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);
            iCount++;
            bRegisteringReceived = true;
         }
         else
         {
            if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering)
            {
               iCount++;
            }
            else
            {
               break;
            }
         }
      }

      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);

      ASSERT_EQ(503, evt.signalingStatusCode);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Dns_Lookup, evt.reason);
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
}

TEST_F(DnsFailoverTest, DnsIPv6ConfigurationMismatch)
{
   ASSERT_TRUE(checkDnsServer());
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.outboundProxy = "priv4.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_V6;
   alice.init();
   alice.account->enable(alice.handle);
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
      bool bRegisteringReceived = false;
      int iCount = 0;

      // Registering status may be received multiple times
      while (iCount < 5)
      {
         ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
            60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(alice.handle, h);
         if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
         {
            safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
         }

         if (bRegisteringReceived == false)
         {
            ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);
            iCount++;
            bRegisteringReceived = true;
         }
         else
         {
            if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering)
            {
               iCount++;
            }
            else
            {
               break;
            }
         }
      }

      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);

      ASSERT_EQ(503, evt.signalingStatusCode);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Dns_Lookup, evt.reason);
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
}

TEST_F(DnsFailoverTest, MultipleARecords) {
   ASSERT_TRUE(checkDnsServer());
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.domain = "cp.local";
   alice.config.settings.outboundProxy = "multia.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.init();
   alice.account->enable(alice.handle);
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
      ASSERT_EQ(408, evt.signalingStatusCode);
      ASSERT_NE(0, evt.failureRetryAfterSecs);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Dns_Lookup, evt.reason);

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         90000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         90000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
      ASSERT_EQ("127.0.0.1", evt.serverIpAddress);
      ASSERT_EQ(6060, evt.serverPort);
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   alice.disable();
}

// TODO: proper NAPTR related tests.
// from experiments with this test:
// if NAPTR response contains multiple records with same order value field, the SDK
// would perform an SRV lookup for each of these, and seems to fail over between these.
// if NATPR response contains multiple records with differing order value field, the SDK
// only perform SRV lookup on highest precedence record
TEST_F(DnsFailoverTest, DISABLED_Naptr) {
   ASSERT_TRUE(checkDnsServer());
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.domain = "cp.local";
   alice.config.settings.outboundProxy = "naptr.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.init();
   alice.enable(false);
   /*
   alice.account->enable(alice.handle);
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
      ASSERT_EQ(408, evt.signalingStatusCode);
      ASSERT_NE(0, evt.failureRetryAfterSecs);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Dns_Lookup, evt.reason);

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         90000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         90000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
   }
   */
   std::this_thread::sleep_for(std::chrono::hours(2000));

   alice.disable();
}

//SDK can't seem to find a source address for the first (fake) AAAA record on Windows
TEST_F(DnsFailoverTest, DISABLED_MultipleAAAARecords) {
   ASSERT_TRUE(checkDnsServer());
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.outboundProxy = "multiaaaa.ipv6.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer(IpVersion_V6));
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_V6;
   alice.init();
   alice.account->enable(alice.handle);
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
      ASSERT_EQ(408, evt.signalingStatusCode);
      ASSERT_NE(0, evt.failureRetryAfterSecs);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Dns_Lookup, evt.reason);

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         90000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);

      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         90000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
      {
         safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
      }
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
}

TEST_F(DnsFailoverTest, PrimaryNotWorkingSecondaryNotWorkingOverrideTimerF)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro1 = runRepro("repro_auth1.config");
   ASSERT_TRUE(repro1->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.username = "alice123";
   alice.config.settings.password = "alice123";
   alice.config.settings.domain = "cp.local";
   alice.config.settings.outboundProxy = "pribrokensecbrokenterworking.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.overrideMsecsTimerF = 4000;
   alice.init();
   ASSERT_TRUE(repro1->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(), alice.config.settings.domain.c_str(), "cp.local", alice.config.settings.password.c_str(), true, "Alice", "<EMAIL>"));

   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   handlePrimaryNotWorkingSecondaryNotWorkingOverrideTimerF(alice);
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   alice.config.settings.overrideMsecsTimerF = 8000;
   alice.init();
   handlePrimaryNotWorkingSecondaryNotWorkingOverrideTimerF(alice);
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   
   alice.config.settings.overrideMsecsTimerF = 16000;
   alice.init();
   handlePrimaryNotWorkingSecondaryNotWorkingOverrideTimerF(alice);
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   
   alice.config.settings.overrideMsecsTimerF = 64000;
   alice.init();
   handlePrimaryNotWorkingSecondaryNotWorkingOverrideTimerF(alice);
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   delete repro1;
   runOriginalRepro();
}

TEST_F(DnsFailoverTest, NetworkChangeOverrideTimerF)
{
   if (hasUnsupportedTcpConnectionTimeout())
   {
      GTEST_SKIP() << "This device has short TCP connection timeouts unsupported by this test";
      return;
   }
   
   ASSERT_TRUE(checkDnsServer());

   DummyTcpListener::Config dtConfig1;
   dtConfig1.port = 6080;
   // to ensure we have a timeout at the SIP transaction level and generate an internal 408
   // vs TCP connection rejection and internal 503, we spin up the dummy TCP listener.
   DummyTcpListener tcpListener1(dtConfig1);
   ASSERT_GT(tcpListener1.start(), 0);

   DummyTcpListener::Config dtConfig2;
   dtConfig2.port = 6070;
   // to ensure we have a timeout at the SIP transaction level and generate an internal 408
   // vs TCP connection rejection and internal 503, we spin up the dummy TCP listener.
   DummyTcpListener tcpListener2(dtConfig2);
   ASSERT_GT(tcpListener2.start(), 0);


   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.username = "alice123";
   alice.config.settings.password = "alice123";
   alice.config.settings.domain = "cp.local";
   // primary, secondary point to localhost 6080/6070 which our DummyTcpListener will accept but not respond to
   alice.config.settings.outboundProxy = "tripletcpsrvrecord.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.sipTransportType = SipAccountTransport_TCP;
   alice.config.settings.overrideMsecsTimerF = 16000;

   SipAccountSettings defaultSettings = alice.config.settings;
   SipAccountSettings wifiSettings = defaultSettings;
   SipAccountSettings wwanSettings = defaultSettings;
   wwanSettings.autoRetryOnTransportDisconnect = true;
   wwanSettings.useRport = false;
   wwanSettings.useOutbound = false;
   wwanSettings.sipTransportType = SipAccountTransport_TCP;

   alice.init();

   ASSERT_EQ(alice.account->configureDefaultAccountSettings(alice.handle, defaultSettings), kSuccess);
   ASSERT_EQ(alice.account->configureTransportAccountSettings(alice.handle, wifiSettings, TransportWiFi), kSuccess);
   ASSERT_EQ(alice.account->configureTransportAccountSettings(alice.handle, wwanSettings, TransportWWAN), kSuccess);
   ASSERT_EQ(alice.account->applySettings(alice.handle), kSuccess);

   handlePrimaryNotWorkingSecondaryNotWorkingOverrideTimerF(alice);

   alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
   std::set<resip::Data> ifaces;
   ifaces.insert("**************");
   alice.network->setMockInterfaces(ifaces);

   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
      ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Refreshing, evt.accountStatus);
   }

   std::time_t startTime = getTime();

   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;

      ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged", __FILE__, (int)(2 * alice.config.settings.overrideMsecsTimerF), CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
      ASSERT_EQ(408, evt.signalingStatusCode);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Dns_Lookup, evt.reason);
      ASSERT_LE((getTime() - startTime) * 1000, (alice.config.settings.overrideMsecsTimerF * 1.25));

      ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged", __FILE__, (int)(2 * alice.config.settings.overrideMsecsTimerF), CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);

      ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged", __FILE__, (int)(2 * alice.config.settings.overrideMsecsTimerF), CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
      ASSERT_EQ(408, evt.signalingStatusCode);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Dns_Lookup, evt.reason);
      ASSERT_LE((getTime() - startTime) * 1000, (2 * alice.config.settings.overrideMsecsTimerF * 1.25));

      ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged", __FILE__, (int)(2 * alice.config.settings.overrideMsecsTimerF), CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering, evt.accountStatus);

      ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged", __FILE__, (int)(2 * alice.config.settings.overrideMsecsTimerF), CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
   }


   ASSERT_LE((getTime() - startTime) * 1000, (2 * alice.config.settings.overrideMsecsTimerF * 1.50));

   alice.disable();
}

//local - data: "_sip._tcp.prisecwithauth.local. SRV 0 0 7080 working.prisecwithauth.local."
//local - data : "_sip._tcp.prisecwithauth.local. SRV 10 0 7090 working2.prisecwithauth.local."
TEST_F(DnsFailoverTest, FailbackConnectionProblem)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro1 = runRepro("repro_auth1.config");
   ASSERT_TRUE(repro1->getProxy() != NULL);
   repro::ReproRunner* repro2 = runRepro("repro_auth2.config");
   ASSERT_TRUE(repro2->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.username = "alice123";
   alice.config.settings.password = "alice123";
   alice.config.settings.domain = "cp.local";
   alice.config.settings.outboundProxy = "prisecwithauth.local";
   alice.config.settings.sipTransportType = SipAccountTransport_TCP;
   alice.config.settings.autoRetryOnTransportDisconnect = true;
   alice.config.settings.useRport = false;
   alice.config.settings.useOutbound = false;
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.minimumRegistrationIntervalSeconds = 5;
   alice.config.settings.maximumRegistrationIntervalSeconds = 5;
   
   const int primaryServerPort = 7080;
   const int secondaryServerPort = 7090;

   alice.init();
   alice.account->setTFTimerValueMs(10000);
   
   ASSERT_TRUE(repro1->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   ASSERT_TRUE(repro2->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   delete repro1;
   
   alice.account->enable(alice.handle);
   {
      for (;;)
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountClientAuthEvent evt;
         ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandlerInternal::onClientAuth", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(alice.handle, h);

         if (evt.responseStatusCode == 408) continue;

         // should be the 407 auth challenge
         ASSERT_EQ(407, evt.responseStatusCode);
         ASSERT_TRUE(evt.willSendUpdatedRequest);
         // make sure we got the 407 auth challenge from secondary SIP server
         ASSERT_EQ(evt.responseSourcePort, secondaryServerPort);
         break;
      }
      for (;;)
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
         ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged",
            __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(alice.handle, h);
         if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister ||
            evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering)
            continue;

         ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
         break;
      }
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountClientAuthEvent evt;
         ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandlerInternal::onClientAuth", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(alice.handle, h);
         ASSERT_EQ(200, evt.responseStatusCode);
         ASSERT_FALSE(evt.willSendUpdatedRequest);
         ASSERT_EQ(evt.responseSourcePort, 7090);
      }
   }

   // take secondary SIP server down; now both primary and secondary are down
   delete repro2;
#ifdef _WIN32
   // this delay seems to be caused by this code in SipAccountImpl:
   // else if (response.exists(h_Warnings) && response.header(h_Warnings).front().code() == 397)
   //
   // with comments:
   //    // Hold-Off on updating UI on internally generated transport error "No route to host" if have just experienced
   //    // a network change, and this is the first retry attempt. Likely transport layer gets reset after the first
   //    // registration attempt, as subsequent attempts are successful if the network is available.
   
   std::this_thread::sleep_for(std::chrono::milliseconds(20000));
#endif

   assertAccountWaitingToRegister(alice);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));
   resip::Helper::setNonceHelper(NULL);
   // bring primary SIP server back up
   repro1 = runRepro("repro_auth1.config");
   ASSERT_TRUE(repro1->getProxy() != NULL);

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   int srcPort, cnt = 0;
   for (;;)
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountClientAuthEvent evt;
      ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandlerInternal::onClientAuth", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);

      if (evt.responseStatusCode == 408) continue;
      if (evt.responseStatusCode == 503 && cnt++ < 5)
      {
         safeCout("Received response code " << evt.responseStatusCode << ". Repro not up?");
         continue;
      }

      ASSERT_EQ(407, evt.responseStatusCode);
      ASSERT_TRUE(evt.willSendUpdatedRequest);
      ASSERT_EQ(evt.responseSourcePort, primaryServerPort);
      srcPort = evt.responseSourcePort;
      break;
   }
   for (;;)
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
      ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged",
         __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister ||
         evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering)
         continue;

      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
      ASSERT_EQ("127.0.0.1", evt.serverIpAddress);
      ASSERT_EQ(primaryServerPort, evt.serverPort);
      break;
   }
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountClientAuthEvent evt;
      ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandlerInternal::onClientAuth", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(200, evt.responseStatusCode);
      ASSERT_FALSE(evt.willSendUpdatedRequest);
      ASSERT_EQ(evt.responseSourcePort, srcPort);
   }
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro1;
   runOriginalRepro();
}

//local - data: "_sip._tcp.prisecwithauth.local. SRV 0 0 7080 working.prisecwithauth.local."
//local - data : "_sip._tcp.prisecwithauth.local. SRV 10 0 7090 working2.prisecwithauth.local."

// ensure DNS failover works if TCP is used for DNS. prisecwithauthtcpdns DNS configuration
// should result in a DNS response from unbound which indicates truncated reply; the SDK
// should then re-send the DNS query over TCP
TEST_F(DnsFailoverTest, FailbackConnectionProblem_TcpDns)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro1 = runRepro("repro_auth1.config");
   ASSERT_TRUE(repro1->getProxy() != NULL);
   repro::ReproRunner* repro2 = runRepro("repro_auth2.config");
   ASSERT_TRUE(repro2->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.username = "alice123";
   alice.config.settings.password = "alice123";
   alice.config.settings.domain = "cp.local";
   alice.config.settings.outboundProxy = "prisecwithauthtcpdns.local";
   alice.config.settings.sipTransportType = SipAccountTransport_TCP;
   alice.config.settings.autoRetryOnTransportDisconnect = true;
   alice.config.settings.useRport = false;
   alice.config.settings.useOutbound = false;
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.minimumRegistrationIntervalSeconds = 5;
   alice.config.settings.maximumRegistrationIntervalSeconds = 5;

   alice.init();
   alice.account->setTFTimerValueMs(10000);

   ASSERT_TRUE(repro1->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   ASSERT_TRUE(repro2->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   delete repro1;

   alice.account->enable(alice.handle);
   {
      for (;;)
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountClientAuthEvent evt;
         ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandlerInternal::onClientAuth", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(alice.handle, h);

         if (evt.responseStatusCode == 408) continue;

         ASSERT_EQ(407, evt.responseStatusCode);
         ASSERT_TRUE(evt.willSendUpdatedRequest);
         ASSERT_EQ(evt.responseSourcePort, 7090);
         break;
      }
      for (;;)
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
         ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged",
            __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(alice.handle, h);
         if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister ||
            evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering)
            continue;

         ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
         break;
      }
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountClientAuthEvent evt;
         ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandlerInternal::onClientAuth", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(alice.handle, h);
         ASSERT_EQ(200, evt.responseStatusCode);
         ASSERT_FALSE(evt.willSendUpdatedRequest);
         ASSERT_EQ(evt.responseSourcePort, 7090);
      }
   }

   delete repro2;

   CPCAPI2::SipAccount::SipAccountHandle h;
   CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
   
   // during this time the SDK is trying to re-connect to the many alternate records discovered via DNS.
   // on jenkins, during these many TCP attempts, some seem to stall, with the TCP connection taking upwards
   // of 5 seconds before it times out -- while others immediately fail.
   // this extra stalling ends up delaying the onAccountStatusChanged /Status_WaitingToRegister event
   // longer than our usual 20 second macro timeout.
   ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged",
      __FILE__, 35000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(alice.handle, h);
   if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Failure)
   {
      safeCout("Registration failed with code " << evt.signalingStatusCode << ": " << (evt.signalingResponseText));
   }
   ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));
   resip::Helper::setNonceHelper(NULL);
   repro1 = runRepro("repro_auth1.config");
   ASSERT_TRUE(repro1->getProxy() != NULL);

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   int srcPort, cnt = 0;
   for (;;)
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountClientAuthEvent evt;
      ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandlerInternal::onClientAuth", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);

      if (evt.responseStatusCode == 408) continue;
      if (evt.responseStatusCode == 503 && cnt++ < 5)
      {
         safeCout("Received response code " << evt.responseStatusCode << ". Repro not up?");
         continue;
      }

      ASSERT_EQ(407, evt.responseStatusCode);
      ASSERT_TRUE(evt.willSendUpdatedRequest);
      ASSERT_EQ(evt.responseSourcePort, 7080);
      srcPort = evt.responseSourcePort;
      break;
   }
   for (;;)
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
      ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged",
         __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister ||
         evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering)
         continue;

      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
      break;
   }
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountClientAuthEvent evt;
      ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandlerInternal::onClientAuth", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(200, evt.responseStatusCode);
      ASSERT_FALSE(evt.willSendUpdatedRequest);
      ASSERT_EQ(evt.responseSourcePort, srcPort);
   }
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro1;
   runOriginalRepro();
}

// requires IPv6 connectivity from Android device to host system running unbound.
// disabled on Android for now until we can get this working on jenkins
#ifndef ANDROID
TEST_F(DnsFailoverTest, FailbackConnectionProblem_ipv6)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro1 = runRepro("repro_auth1.config");
   ASSERT_TRUE(repro1->getProxy() != NULL);
   repro::ReproRunner* repro2 = runRepro("repro_auth2.config");
   ASSERT_TRUE(repro2->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.username = "alice123";
   alice.config.settings.password = "alice123";
   alice.config.settings.domain = "cp.local";
   alice.config.settings.outboundProxy = "prisecwithauth.ipv6.local";
   alice.config.settings.ipVersion = IpVersion_V6;
   alice.config.settings.sipTransportType = SipAccountTransport_TCP;
   alice.config.settings.autoRetryOnTransportDisconnect = true;
   alice.config.settings.useRport = false;
   alice.config.settings.useOutbound = false;
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer(IpVersion_V6));
   alice.config.settings.minimumRegistrationIntervalSeconds = 5;
   alice.config.settings.maximumRegistrationIntervalSeconds = 5;
   
   const int primaryServerPort = 7080;
   const int secondaryServerPort = 7090;

   alice.init();
   alice.account->setTFTimerValueMs(10000);
   
   ASSERT_TRUE(repro1->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   ASSERT_TRUE(repro2->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   delete repro1;

   alice.account->enable(alice.handle);
   {
      for (;;)
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountClientAuthEvent evt;
         ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandlerInternal::onClientAuth", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(alice.handle, h);

         if (evt.responseStatusCode == 408) continue;

         // should be the 407 auth challenge
         ASSERT_EQ(407, evt.responseStatusCode);
         ASSERT_TRUE(evt.willSendUpdatedRequest);
         // make sure we got the 407 auth challenge from secondary SIP server
         ASSERT_EQ(evt.responseSourcePort, secondaryServerPort);
         break;
      }
      for (;;)
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
         ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged",
            __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(alice.handle, h);
         if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister ||
            evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering)
            continue;

         ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
         ASSERT_EQ("::1", evt.serverIpAddress);
         ASSERT_EQ(secondaryServerPort, evt.serverPort);
         break;
      }
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountClientAuthEvent evt;
         ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandlerInternal::onClientAuth", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(alice.handle, h);
         ASSERT_EQ(200, evt.responseStatusCode);
         ASSERT_FALSE(evt.willSendUpdatedRequest);
         ASSERT_EQ(evt.responseSourcePort, 7090);
      }
   }

   // take secondary SIP server down; now both primary and secondary are down
   delete repro2;

#ifdef _WIN32
   // this delay seems to be caused by this code in SipAccountImpl:
   // else if (response.exists(h_Warnings) && response.header(h_Warnings).front().code() == 397)
   //
   // with comments:
   //    // Hold-Off on updating UI on internally generated transport error "No route to host" if have just experienced
   //    // a network change, and this is the first retry attempt. Likely transport layer gets reset after the first
   //    // registration attempt, as subsequent attempts are successful if the network is available.
   
   std::this_thread::sleep_for(std::chrono::milliseconds(20000));
#endif

   assertAccountWaitingToRegister(alice);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));
   resip::Helper::setNonceHelper(NULL);
   
   // bring primary SIP server back up
   repro1 = runRepro("repro_auth1.config");
   ASSERT_TRUE(repro1->getProxy() != NULL);

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   int srcPort, cnt = 0;
   for (;;)
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountClientAuthEvent evt;
      ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandlerInternal::onClientAuth", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);

      if (evt.responseStatusCode == 408) continue;
      if (evt.responseStatusCode == 503 && cnt++ < 5)
      {
         safeCout("Received response code " << evt.responseStatusCode << ". Repro not up?");
         continue;
      }

      ASSERT_EQ(407, evt.responseStatusCode);
      ASSERT_TRUE(evt.willSendUpdatedRequest);
      ASSERT_EQ(evt.responseSourcePort, primaryServerPort);
      srcPort = evt.responseSourcePort;
      break;
   }
   for (;;)
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
      ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged",
         __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister ||
         evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering)
         continue;

      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
      ASSERT_EQ("::1", evt.serverIpAddress);
      ASSERT_EQ(primaryServerPort, evt.serverPort);
      break;
   }
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountClientAuthEvent evt;
      ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandlerInternal::onClientAuth", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(200, evt.responseStatusCode);
      ASSERT_FALSE(evt.willSendUpdatedRequest);
      ASSERT_EQ(evt.responseSourcePort, srcPort);
   }
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro1;
   runOriginalRepro();
}
#endif // #ifndef ANDROID

TEST_F(DnsFailoverTest, FailbackConnectionProblem_multiRepro)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro1 = runRepro("repro_auth1.config");
   ASSERT_TRUE(repro1->getProxy() != NULL);
   repro::ReproRunner* repro2 = runRepro("repro_auth2.config");
   ASSERT_TRUE(repro2->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.username = "alice123";
   alice.config.settings.password = "alice123";
   alice.config.settings.domain = "cp.local";
   alice.config.settings.outboundProxy = "prisecwithauth.local";
   alice.config.settings.sipTransportType = SipAccountTransport_TCP;
   alice.config.settings.autoRetryOnTransportDisconnect = true;
   alice.config.settings.useRport = false;
   alice.config.settings.useOutbound = false;
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.minimumRegistrationIntervalSeconds = 5;
   alice.config.settings.maximumRegistrationIntervalSeconds = 5;

   alice.init();
   alice.account->setTFTimerValueMs(10000);

   ASSERT_TRUE(repro1->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   ASSERT_TRUE(repro2->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   delete repro1;

   alice.account->enable(alice.handle);
   {
      for (;;)
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountClientAuthEvent evt;
         ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandlerInternal::onClientAuth", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(alice.handle, h);

         if (evt.responseStatusCode == 408) continue;

         ASSERT_EQ(407, evt.responseStatusCode);
         ASSERT_TRUE(evt.willSendUpdatedRequest);
         ASSERT_EQ(evt.responseSourcePort, 7090);
         break;
      }
      for (;;)
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
         ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged",
            __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(alice.handle, h);
         if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister ||
            evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering)
            continue;

         ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
         break;
      }
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountClientAuthEvent evt;
         ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandlerInternal::onClientAuth", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(alice.handle, h);
         ASSERT_EQ(200, evt.responseStatusCode);
         ASSERT_FALSE(evt.willSendUpdatedRequest);
         ASSERT_EQ(evt.responseSourcePort, 7090);
      }
   }

   delete repro2;
#ifdef _WIN32
   // this delay seems to be caused by this code in SipAccountImpl:
   // else if (response.exists(h_Warnings) && response.header(h_Warnings).front().code() == 397)
   //
   // with comments:
   //    // Hold-Off on updating UI on internally generated transport error "No route to host" if have just experienced
   //    // a network change, and this is the first retry attempt. Likely transport layer gets reset after the first
   //    // registration attempt, as subsequent attempts are successful if the network is available.
   
   std::this_thread::sleep_for(std::chrono::milliseconds(20000));
#endif

   assertAccountWaitingToRegister(alice);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));
   resip::Helper::setNonceHelper(NULL);
   repro1 = runRepro("repro_auth1.config");
   ASSERT_TRUE(repro1->getProxy() != NULL);
   repro2 = runRepro("repro_auth2.config");
   ASSERT_TRUE(repro2->getProxy() != NULL);

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   int srcPort, cnt = 0;
   for (;;)
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountClientAuthEvent evt;
      ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandlerInternal::onClientAuth", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);

      if (evt.responseStatusCode == 408) continue;
      if (evt.responseStatusCode == 503 && cnt++ < 5)
      {
         safeCout("Received response code " << evt.responseStatusCode << ". Repro not up?");
         continue;
      }

      ASSERT_EQ(407, evt.responseStatusCode);
      ASSERT_TRUE(evt.willSendUpdatedRequest);
      srcPort = evt.responseSourcePort;
      break;
   }
   for (;;)
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
      ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged",
         __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister ||
         evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering)
         continue;

      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
      break;
   }
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountClientAuthEvent evt;
      ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandlerInternal::onClientAuth", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(200, evt.responseStatusCode);
      ASSERT_FALSE(evt.willSendUpdatedRequest);
      ASSERT_EQ(evt.responseSourcePort, srcPort);
   }
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro1;
   delete repro2;
   runOriginalRepro();
}

// two SIP servers running; call active on primary SIP server. primary SIP server goes down:
// the SDK should handle this gracefully by transitioning registration to secondary SIP server,
// and ending the active call
void DnsFailoverTest::FailoverConnectionProblem_multiRepro_WithCall(SipAccountTransportType transportType)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro1 = runRepro("repro_auth1.config");
   ASSERT_TRUE(repro1->getProxy() != NULL);
   repro::ReproRunner* repro2 = runRepro("repro_auth2.config");
   ASSERT_TRUE(repro2->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(3000));
   

   
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.username = "alice123";
   alice.config.settings.password = "alice123";
   alice.config.settings.domain = "cp.local";
   alice.config.settings.outboundProxy = "prisecwithauth.local";
   alice.config.settings.sipTransportType = transportType;
   if (transportType == SipAccountTransport_UDP)
   {
      alice.config.settings.registrationIntervalSeconds = 15;
   }
   else
   {
      alice.config.settings.autoRetryOnTransportDisconnect = true;
   }
   alice.config.settings.ignoreCertVerification = true;
   alice.config.settings.useRport = false;
   alice.config.settings.useOutbound = false;
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   
   TestAccount bob("bob", Account_NoInit);
   bob.config.settings.username = "bob123";
   bob.config.settings.password = "bob123";
   bob.config.settings.domain = "cp.local";
   bob.config.settings.outboundProxy = "prisecwithauth.local";
   bob.config.settings.sipTransportType = transportType;
   bob.config.settings.ignoreCertVerification = true;
   // we want to focus on alice, so try to avoid bob reconnecting and cluttering logs
   bob.config.settings.autoRetryOnTransportDisconnect = false;
   bob.config.settings.useRport = false;
   bob.config.settings.useOutbound = false;
   bob.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   bob.init();
   
   ASSERT_TRUE(repro1->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   ASSERT_TRUE(repro1->getProxy()->getUserStore().addUser(bob.config.settings.username.c_str(),
      bob.config.settings.domain.c_str(), "cp.local",
      bob.config.settings.password.c_str(),
      true, "Bob", "<EMAIL>"));

   ASSERT_TRUE(repro2->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
      
   alice.init();
   alice.account->setTFTimerValueMs(10000);
   alice.enable();
   
   bob.enable();
   

   // alice calls bob over repro1
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);
   
   SipConversationHandle bobCall;
   
   auto aliceConversationEvents = std::async(std::launch::async, [&] () {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
   });
   
   auto bobConversationEvents = std::async(std::launch::async, [&] () {
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      bob.conversation->sendRingingResponse(bobCall);
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      bob.conversation->accept(bobCall);
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
   });
   
   waitFor2(aliceConversationEvents, bobConversationEvents);

   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   // repro1 goes down; alice should detect this, and fail over to repro2
   delete repro1;
#ifdef _WIN32
   // this delay seems to be caused by this code in SipAccountImpl:
   // else if (response.exists(h_Warnings) && response.header(h_Warnings).front().code() == 397)
   //
   // with comments:
   //    // Hold-Off on updating UI on internally generated transport error "No route to host" if have just experienced
   //    // a network change, and this is the first retry attempt. Likely transport layer gets reset after the first
   //    // registration attempt, as subsequent attempts are successful if the network is available.
   
   std::this_thread::sleep_for(std::chrono::milliseconds(20000));
#endif

   // if TCP is in use, repro will close the TCP connection; alice should detect this, and attempt to re-REGISTER
   // to primary SIP server (repro that just went down). when this fails, alice should register with secondary SIP
   // server and end the active call.
   
   // if UDP is in use, alice should detect registration refresh failing, move to secondary SIP server, and end the
   // active call

   aliceConversationEvents = std::async(std::launch::async, [&] () {

      // account status only seems to change if UDP is in use
      if (transportType == SipAccountTransport_UDP)
      {
         for (;;)
         {
            CPCAPI2::SipAccount::SipAccountHandle h;
            CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
            ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged",
               __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
            ASSERT_EQ(alice.handle, h);
            if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister ||
               evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering)
               continue;

            ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
            ASSERT_EQ("127.0.0.1", evt.serverIpAddress);
            ASSERT_EQ(7090, evt.serverPort);
            break;
         }
      }

      ConversationEndReason endReason;
      unsigned long waitTimeSec;
      int sipResponseCode; 
      if (transportType == SipAccountTransport_UDP)
      {
         // all retransmissions need to fail
         const unsigned long wiggleRoomSec = 5;
         waitTimeSec = alice.config.settings.registrationIntervalSeconds + (alice.account->getTFTimerValueMs() * 1000) +
                       alice.config.settings.minimumRegistrationIntervalSeconds + wiggleRoomSec;
         endReason = ConversationEndReason_ServerRejected;
         sipResponseCode = 408;
      }
      else
      {
         // TCP write (for re-REGISTER) should immediately fail, but TCP write code might depend on OS / timing of repro shutting down.
         // alice will either immediately try secondary SIP server, or fire 408, wait minimumRegistrationIntervalSeconds, then register
         // with secondary, before ending the call
         const unsigned long wiggleRoomSec = 5;
         waitTimeSec = (alice.account->getTFTimerValueMs() * 1000) +
                       alice.config.settings.minimumRegistrationIntervalSeconds + wiggleRoomSec;
         endReason = ConversationEndReason_ServerError;
         sipResponseCode = 503;
      }

      // registration to secondary SIP server should trigger a re-INVITE, which should fail
      std::function<void(ConversationEndedEvent)> validator = [sipResponseCode] (ConversationEndedEvent evt)
      {
         ASSERT_EQ(evt.sipResponseCode, sipResponseCode);
      };

      assertConversationEnded_time_ex(alice, aliceCall, endReason, static_cast<int>(waitTimeSec) * 1000, validator);
   });

   waitFor(aliceConversationEvents);

   {
      // alice might have fired waitingToRegister / registered account status events depending on OS / timing of repro shutting
      // down. skip registration state check, because it might end up discovering these events before unregistering, unregistered events
      bool assertRegistrationState = false;
      alice.disable(false, assertRegistrationState);
   }

   {
      bool assertRegistrationState = false; // bob may not have realized he lost connection to primary SIP server
      bob.disable(false, assertRegistrationState);
   }

   delete repro2;
   runOriginalRepro();
}

TEST_F(DnsFailoverTest, FailoverConnectionProblemTCP_multiRepro_WithCall)
{
   FailoverConnectionProblem_multiRepro_WithCall(SipAccount::SipAccountTransport_TCP);
}

TEST_F(DnsFailoverTest, FailoverConnectionProblemTLS_multiRepro_WithCall)
{
   FailoverConnectionProblem_multiRepro_WithCall(SipAccount::SipAccountTransport_TLS);
}


TEST_F(DnsFailoverTest, FailoverConnectionProblemUDP_multiRepro_WithCall)
{
   FailoverConnectionProblem_multiRepro_WithCall(SipAccount::SipAccountTransport_UDP);
}

// requires IPv6 connectivity from Android device to host system running unbound.
// disabled on Android for now until we can get this working on jenkins
#ifndef ANDROID
TEST_F(DnsFailoverTest, FailbackConnectionProblem_multiRepro_ipv6)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro1 = runRepro("repro_auth1.config");
   ASSERT_TRUE(repro1->getProxy() != NULL);
   repro::ReproRunner* repro2 = runRepro("repro_auth2.config");
   ASSERT_TRUE(repro2->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.username = "alice123";
   alice.config.settings.password = "alice123";
   alice.config.settings.domain = "cp.local";
   alice.config.settings.outboundProxy = "prisecwithauth.ipv6.local";
   alice.config.settings.ipVersion = IpVersion_V6;
   alice.config.settings.sipTransportType = SipAccountTransport_TCP;
   alice.config.settings.autoRetryOnTransportDisconnect = true;
   alice.config.settings.useRport = false;
   alice.config.settings.useOutbound = false;
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer(IpVersion_V6));
   alice.config.settings.minimumRegistrationIntervalSeconds = 5;
   alice.config.settings.maximumRegistrationIntervalSeconds = 5;

   alice.init();
   alice.account->setTFTimerValueMs(10000);

   ASSERT_TRUE(repro1->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   ASSERT_TRUE(repro2->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   delete repro1;

   alice.account->enable(alice.handle);
   {
      for (;;)
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountClientAuthEvent evt;
         ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandlerInternal::onClientAuth", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(alice.handle, h);

         if (evt.responseStatusCode == 408) continue;

         ASSERT_EQ(407, evt.responseStatusCode);
         ASSERT_TRUE(evt.willSendUpdatedRequest);
         ASSERT_EQ(evt.responseSourcePort, 7090);
         break;
      }
      for (;;)
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
         ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged",
            __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(alice.handle, h);
         if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister ||
            evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering)
            continue;

         ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
         break;
      }
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountClientAuthEvent evt;
         ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandlerInternal::onClientAuth", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(alice.handle, h);
         ASSERT_EQ(200, evt.responseStatusCode);
         ASSERT_FALSE(evt.willSendUpdatedRequest);
         ASSERT_EQ(evt.responseSourcePort, 7090);
      }
   }

   delete repro2;

#ifdef _WIN32
   // this delay seems to be caused by this code in SipAccountImpl:
   // else if (response.exists(h_Warnings) && response.header(h_Warnings).front().code() == 397)
   //
   // with comments:
   //    // Hold-Off on updating UI on internally generated transport error "No route to host" if have just experienced
   //    // a network change, and this is the first retry attempt. Likely transport layer gets reset after the first
   //    // registration attempt, as subsequent attempts are successful if the network is available.
   
   std::this_thread::sleep_for(std::chrono::milliseconds(20000));
#endif

   assertAccountWaitingToRegister(alice);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));
   resip::Helper::setNonceHelper(NULL);
   repro1 = runRepro("repro_auth1.config");
   ASSERT_TRUE(repro1->getProxy() != NULL);
   repro2 = runRepro("repro_auth2.config");
   ASSERT_TRUE(repro2->getProxy() != NULL);

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   int srcPort, cnt = 0;
   for (;;)
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountClientAuthEvent evt;
      ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandlerInternal::onClientAuth", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);

      if (evt.responseStatusCode == 408) continue;
      if (evt.responseStatusCode == 503 && cnt++ < 5)
      {
         safeCout("Received response code " << evt.responseStatusCode << ". Repro not up?");
         continue;
      }

      ASSERT_EQ(407, evt.responseStatusCode);
      ASSERT_TRUE(evt.willSendUpdatedRequest);
      srcPort = evt.responseSourcePort;
      break;
   }
   for (;;)
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
      ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged",
         __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister ||
         evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering)
         continue;

      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
      break;
   }
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountClientAuthEvent evt;
      ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandlerInternal::onClientAuth", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(200, evt.responseStatusCode);
      ASSERT_FALSE(evt.willSendUpdatedRequest);
      ASSERT_EQ(evt.responseSourcePort, srcPort);
   }
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro1;
   delete repro2;
   runOriginalRepro();
}
#endif // #ifndef ANDROID

TEST_F(DnsFailoverTest, NetworkChange)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro1 = runRepro("repro_auth1.config");
   ASSERT_TRUE(repro1->getProxy() != NULL);
   repro::ReproRunner* repro2 = runRepro("repro_auth2.config");
   ASSERT_TRUE(repro2->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.username = "alice123";
   alice.config.settings.password = "alice123";
   alice.config.settings.domain = "cp.local";
   alice.config.settings.outboundProxy = "prisecwithauth.local";
   alice.config.settings.sipTransportType = SipAccountTransport_TCP;
   alice.config.settings.autoRetryOnTransportDisconnect = true;
   alice.config.settings.useRport = false;
   alice.config.settings.useOutbound = false;
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.minimumRegistrationIntervalSeconds = 5;
   alice.config.settings.maximumRegistrationIntervalSeconds = 5;

   alice.init();

   ASSERT_TRUE(repro1->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   ASSERT_TRUE(repro2->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   alice.account->enable(alice.handle);
   {
      for (;;)
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountClientAuthEvent evt;
         ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandlerInternal::onClientAuth", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(alice.handle, h);

         if (evt.responseStatusCode == 408) continue;

         ASSERT_EQ(407, evt.responseStatusCode);
         ASSERT_TRUE(evt.willSendUpdatedRequest);
         ASSERT_EQ(evt.responseSourcePort, 7080);
         break;
      }
      for (;;)
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
         ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged",
            __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(alice.handle, h);
         if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister ||
            evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering)
            continue;

         ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
         break;
      }
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountClientAuthEvent evt;
         ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandlerInternal::onClientAuth", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(alice.handle, h);
         ASSERT_EQ(200, evt.responseStatusCode);
         ASSERT_FALSE(evt.willSendUpdatedRequest);
         ASSERT_EQ(evt.responseSourcePort, 7080);
      }
   }

   alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
   std::set<resip::Data> ifaces;
   ifaces.insert("**************");
   alice.network->setMockInterfaces(ifaces);

   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Refreshing, evt.accountStatus);
   }

   for (;;)
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountClientAuthEvent evt;
      ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandlerInternal::onClientAuth", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);

      if (evt.responseStatusCode == 408) continue;
      ASSERT_EQ(407, evt.responseStatusCode);
      ASSERT_TRUE(evt.willSendUpdatedRequest);
      ASSERT_EQ(evt.responseSourcePort, 7080);
      break;
   }
   for (;;)
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
      ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged",
         __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister ||
         evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering)
         continue;

      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
      break;
   }
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountClientAuthEvent evt;
      ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandlerInternal::onClientAuth", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(200, evt.responseStatusCode);
      ASSERT_FALSE(evt.willSendUpdatedRequest);
      ASSERT_EQ(evt.responseSourcePort, 7080);
   }
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   delete repro1;
   delete repro2;
   runOriginalRepro();
}

// requires IPv6 connectivity from Android device to host system running unbound.
// disabled on Android for now until we can get this working on jenkins
#ifndef ANDROID
TEST_F(DnsFailoverTest, NetworkChange_ipv6)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro1 = runRepro("repro_auth1.config");
   ASSERT_TRUE(repro1->getProxy() != NULL);
   repro::ReproRunner* repro2 = runRepro("repro_auth2.config");
   ASSERT_TRUE(repro2->getProxy() != NULL);

   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.username = "alice123";
   alice.config.settings.password = "alice123";
   alice.config.settings.domain = "cp.local";
   alice.config.settings.outboundProxy = "prisecwithauth.ipv6.local";
   alice.config.settings.ipVersion = IpVersion_V6;
   alice.config.settings.sipTransportType = SipAccountTransport_TCP;
   alice.config.settings.autoRetryOnTransportDisconnect = true;
   alice.config.settings.useRport = false;
   alice.config.settings.useOutbound = false;
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer(IpVersion_V6));
   alice.config.settings.minimumRegistrationIntervalSeconds = 5;
   alice.config.settings.maximumRegistrationIntervalSeconds = 5;

   alice.init();

   ASSERT_TRUE(repro1->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   ASSERT_TRUE(repro2->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   alice.account->enable(alice.handle);
   {
      for (;;)
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountClientAuthEvent evt;
         ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandlerInternal::onClientAuth", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(alice.handle, h);

         if (evt.responseStatusCode == 408) continue;

         ASSERT_EQ(407, evt.responseStatusCode);
         ASSERT_TRUE(evt.willSendUpdatedRequest);
         ASSERT_EQ(evt.responseSourcePort, 7080);
         break;
      }
      for (;;)
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
         ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged",
            __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(alice.handle, h);
         if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister ||
            evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering)
            continue;

         ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
         break;
      }
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountClientAuthEvent evt;
         ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandlerInternal::onClientAuth", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(alice.handle, h);
         ASSERT_EQ(200, evt.responseStatusCode);
         ASSERT_FALSE(evt.willSendUpdatedRequest);
         ASSERT_EQ(evt.responseSourcePort, 7080);
      }
   }

   alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
   std::set<resip::Data> ifaces;
   ifaces.insert("fda4:c563:1be3:df06::");
   alice.network->setMockInterfaces(ifaces);

   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
      ASSERT_TRUE(alice.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
         20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Refreshing, evt.accountStatus);
   }

   for (;;)
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountClientAuthEvent evt;
      ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandlerInternal::onClientAuth", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);

      if (evt.responseStatusCode == 408) continue;
      ASSERT_EQ(407, evt.responseStatusCode);
      ASSERT_TRUE(evt.willSendUpdatedRequest);
      ASSERT_EQ(evt.responseSourcePort, 7080);
      break;
   }
   for (;;)
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
      ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged",
         __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister ||
         evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering)
         continue;

      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
      break;
   }
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountClientAuthEvent evt;
      ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandlerInternal::onClientAuth", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(200, evt.responseStatusCode);
      ASSERT_FALSE(evt.willSendUpdatedRequest);
      ASSERT_EQ(evt.responseSourcePort, 7080);
   }
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   delete repro1;
   delete repro2;
   runOriginalRepro();
}
#endif // #ifndef ANDROID


#if !defined(__APPLE__) // ********* not available by default on mac -- requires running sudo ifconfig lo0 alias ********* up

//local - data: "multiple.multiamultirepro.local. A 127.0.0.1"
//local - data: "multiple.multiamultirepro.local. A *********"
TEST_F(DnsFailoverTest, FailbackConnectionProblem_MultipleA)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro1 = runRepro("repro_auth_127001.config");
   ASSERT_TRUE(repro1->getProxy() != NULL);
   repro::ReproRunner* repro2 = runRepro("repro_auth_127002.config");
   ASSERT_TRUE(repro2->getProxy() != NULL);

   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.username = "alice123";
   alice.config.settings.password = "alice123";
   alice.config.settings.domain = "cp.local";
   alice.config.settings.outboundProxy = "multiamultirepro.local";
   alice.config.settings.sipTransportType = SipAccountTransport_TCP;
   alice.config.settings.autoRetryOnTransportDisconnect = true;
   alice.config.settings.useRport = false;
   alice.config.settings.useOutbound = false;
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.minimumRegistrationIntervalSeconds = 5;
   alice.config.settings.maximumRegistrationIntervalSeconds = 5;

   alice.init();
   alice.account->setTFTimerValueMs(10000);

   ASSERT_TRUE(repro1->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   ASSERT_TRUE(repro2->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   delete repro1;

   alice.account->enable(alice.handle);
   {
      for (;;)
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountClientAuthEvent evt;
         ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandlerInternal::onClientAuth", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(alice.handle, h);

         if (evt.responseStatusCode == 408) continue;

         ASSERT_EQ(407, evt.responseStatusCode);
         ASSERT_TRUE(evt.willSendUpdatedRequest);
         ASSERT_EQ("*********", evt.responseSourceIp);
         break;
      }
      for (;;)
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
         ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged",
            __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(alice.handle, h);
         if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister ||
            evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering)
            continue;

         ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
         break;
      }
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountClientAuthEvent evt;
         ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandlerInternal::onClientAuth", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(alice.handle, h);
         ASSERT_EQ(200, evt.responseStatusCode);
         ASSERT_FALSE(evt.willSendUpdatedRequest);
         ASSERT_EQ("*********", evt.responseSourceIp);
      }
   }

   delete repro2;
   
#ifdef _WIN32
   // this delay seems to be caused by this code in SipAccountImpl:
   // else if (response.exists(h_Warnings) && response.header(h_Warnings).front().code() == 397)
   //
   // with comments:
   //    // Hold-Off on updating UI on internally generated transport error "No route to host" if have just experienced
   //    // a network change, and this is the first retry attempt. Likely transport layer gets reset after the first
   //    // registration attempt, as subsequent attempts are successful if the network is available.
   
   std::this_thread::sleep_for(std::chrono::milliseconds(20000));
#endif

   assertAccountWaitingToRegister(alice);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));
   resip::Helper::setNonceHelper(NULL);
   repro1 = runRepro("repro_auth_127001.config");
   ASSERT_TRUE(repro1->getProxy() != NULL);

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   int cnt = 0;
   for (;;)
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountClientAuthEvent evt;
      ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandlerInternal::onClientAuth", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);

      if (evt.responseStatusCode == 408) continue;
      if (evt.responseStatusCode == 503 && cnt++ < 5)
      {
         safeCout("Received response code " << evt.responseStatusCode << ". Repro not up?");
         continue;
      }

      ASSERT_EQ(407, evt.responseStatusCode);
      ASSERT_TRUE(evt.willSendUpdatedRequest);
      ASSERT_EQ("127.0.0.1", evt.responseSourceIp);
      break;
   }
   for (;;)
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
      ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged",
         __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister ||
         evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering)
         continue;

      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
      break;
   }
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountClientAuthEvent evt;
      ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandlerInternal::onClientAuth", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(200, evt.responseStatusCode);
      ASSERT_FALSE(evt.willSendUpdatedRequest);
      ASSERT_EQ("127.0.0.1", evt.responseSourceIp);
   }
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro1;
   runOriginalRepro();
}

TEST_F(DnsFailoverTest, FailbackConnectionProblem_MultipleA_multiRepro)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro1 = runRepro("repro_auth_127001.config");
   ASSERT_TRUE(repro1->getProxy() != NULL);
   repro::ReproRunner* repro2 = runRepro("repro_auth_127002.config");
   ASSERT_TRUE(repro2->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.username = "alice123";
   alice.config.settings.password = "alice123";
   alice.config.settings.domain = "cp.local";
   alice.config.settings.outboundProxy = "multiamultirepro.local";
   alice.config.settings.sipTransportType = SipAccountTransport_TCP;
   alice.config.settings.autoRetryOnTransportDisconnect = true;
   alice.config.settings.useRport = false;
   alice.config.settings.useOutbound = false;
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   alice.config.settings.minimumRegistrationIntervalSeconds = 5;
   alice.config.settings.maximumRegistrationIntervalSeconds = 5;

   alice.init();
   alice.account->setTFTimerValueMs(10000);

   ASSERT_TRUE(repro1->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   ASSERT_TRUE(repro2->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   delete repro1;

   alice.account->enable(alice.handle);
   {
      for (;;)
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountClientAuthEvent evt;
         ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandlerInternal::onClientAuth", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(alice.handle, h);

         if (evt.responseStatusCode == 408) continue;

         ASSERT_EQ(407, evt.responseStatusCode);
         ASSERT_TRUE(evt.willSendUpdatedRequest);
         ASSERT_EQ("*********", evt.responseSourceIp);
         break;
      }
      for (;;)
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
         ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged",
            __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(alice.handle, h);
         if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister ||
            evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering)
            continue;

         ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
         break;
      }
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountClientAuthEvent evt;
         ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandlerInternal::onClientAuth", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(alice.handle, h);
         ASSERT_EQ(200, evt.responseStatusCode);
         ASSERT_FALSE(evt.willSendUpdatedRequest);
         ASSERT_EQ("*********", evt.responseSourceIp);
      }
   }

   delete repro2;
#ifdef _WIN32
   // this delay seems to be caused by this code in SipAccountImpl:
   // else if (response.exists(h_Warnings) && response.header(h_Warnings).front().code() == 397)
   //
   // with comments:
   //    // Hold-Off on updating UI on internally generated transport error "No route to host" if have just experienced
   //    // a network change, and this is the first retry attempt. Likely transport layer gets reset after the first
   //    // registration attempt, as subsequent attempts are successful if the network is available.
   
   std::this_thread::sleep_for(std::chrono::milliseconds(20000));
#endif

   assertAccountWaitingToRegister(alice);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));
   resip::Helper::setNonceHelper(NULL);
   repro1 = runRepro("repro_auth_127001.config");
   ASSERT_TRUE(repro1->getProxy() != NULL);
   repro2 = runRepro("repro_auth_127002.config");
   ASSERT_TRUE(repro2->getProxy() != NULL);

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   cpc::string srcAddr;
   int cnt = 0;
   for (;;)
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountClientAuthEvent evt;
      ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandlerInternal::onClientAuth", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);

      if (evt.responseStatusCode == 408) continue;
      if (evt.responseStatusCode == 503 && cnt++ < 5)
      {
         safeCout("Received response code " << evt.responseStatusCode << ". Repro not up?");
         continue;
      }

      ASSERT_EQ(407, evt.responseStatusCode);
      ASSERT_TRUE(evt.willSendUpdatedRequest);
      srcAddr = evt.responseSourceIp;
      break;
   }
   for (;;)
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
      ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged",
         __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister ||
         evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering)
         continue;

      ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
      break;
   }
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
      CPCAPI2::SipAccount::SipAccountClientAuthEvent evt;
      ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandlerInternal::onClientAuth", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(alice.handle, h);
      ASSERT_EQ(200, evt.responseStatusCode);
      ASSERT_FALSE(evt.willSendUpdatedRequest);
      ASSERT_EQ(srcAddr, evt.responseSourceIp);
   }
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro1;
   delete repro2;
   runOriginalRepro();
}

#endif // #if !defined(__APPLE__)

void DnsFailoverTest::configureAccount(TestAccount& account)
{
   ASSERT_TRUE(checkDnsServer());
   account.config.settings.username = "alice123";
   account.config.settings.password = "alice123";
   account.config.settings.domain = "cp.local";
   account.config.settings.useOptionsPing = false;
   account.config.settings.optionsPingInterval = 5000;
   account.config.settings.autoRetryOnTransportDisconnect = true;
   account.config.settings.useRport = false;
   account.config.settings.useOutbound = false;
   account.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   account.config.settings.registrationIntervalSeconds = 30;
   account.config.settings.minimumRegistrationIntervalSeconds = 5;
   account.config.settings.maximumRegistrationIntervalSeconds = 5;
   account.config.settings.enableDNSResetOnRegistrationRefresh = true;
   account.config.settings.enableAuthResetUponDNSReset = true;
}

TEST_F(DnsFailoverTest, Switchover_IPv4PreferredAutoTransportSetting_IPv4TCPPrimary_IPv4TCPSecondary)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   repro::ReproRunner* repro_7090 = runRepro("repro_auth2.config"); // Port 7090
   ASSERT_TRUE(repro_7090->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   TestAccount alice("alice", Account_NoInit);
   configureAccount(alice);
   alice.config.settings.outboundProxy = "pritcpv4sectcpv4.local"; // primary is TCP Port 7080 and secondary is TCP Port 7090
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_Auto;

   alice.init();
   alice.account->setTFTimerValueMs(10000);

   ASSERT_TRUE(repro_7080->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7090->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   delete repro_7080;

   alice.account->enable(alice.handle);
   {
      assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_TCP);
      assertOnClientAuthUntilEx(alice, 7090, 200, false);
   }

   repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   assertOnDnsResetEx(alice, 7080, IpVersion_V4, SipAccountTransport_TCP);
   assertAccountRefreshingEx(alice);
   resip::Helper::setNonceHelper(NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_TCP);
   assertOnClientAuthUntilEx(alice, 7080, 200, false);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro_7080;
   delete repro_7090;
   runOriginalRepro();
}

#if CPCAPI2_PLATFORM_SUPPORTS_SIPP
// SDK registers against primary SIP server (sipp in our case),
// then during a registration refresh the TLS connection is dropped
// by the primary SIP server; expected behaviour is for the SDK to
// immediately react to this disconnect; currently it does not
TEST_F(DnsFailoverTest, DISABLED_FailoverDuringRegRefresh)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();

   const int sippListenPort = 7080;

   SippRunnerSettings sippRunnerSettings;
   sippRunnerSettings.sipListenPort = sippListenPort;
   sippRunnerSettings.mediaPort = 50020;
   sippRunnerSettings.timeoutSec = 120;
   sippRunnerSettings.transport = CPCAPI2::test::SippRunnerSettings::Transport_Tls;
   sippRunnerSettings.scenarioFileName = "DnsFailoverTest.FailoverDuringRegRefresh.xml";

   SippRunner sippRunner(sippRunnerSettings);
   sippRunner.start();
   
   std::unique_ptr<repro::ReproRunner> repro_7090(runRepro("repro_auth2.config")); // Port 7090
   ASSERT_TRUE(repro_7090->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   TestAccount alice("alice", Account_NoInit);
   configureAccount(alice);
   alice.config.settings.outboundProxy = "pritcpv4sectlsv4.local"; // primary is TCP Port 7080 and secondary is TCP Port 7090
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_V4;
   alice.config.settings.useRport = false;
   alice.config.settings.useOutbound = false;
   alice.config.settings.ignoreCertVerification = true;

   alice.init();

   alice.enable();

   alice.account->requestRegistrationRefresh(alice.handle, 0);

   assertAccountRefreshing(alice);
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   sippRunner.stop();

   assertAccountWaitingToRegister(alice); // test currently fails here -- SDK ignores ConnectionTerminated 
                                          // (in ClientRegistration::registrationConnectionTerminated()) since
                                          // mState is Refreshing
   assertAccountRegistering(alice);
   assertAccountRegistered(alice);

   repro_7090.reset();
   runOriginalRepro();
}
#endif // CPCAPI2_PLATFORM_SUPPORTS_SIPP

TEST_F(DnsFailoverTest, Switchover_IPv4PreferredAutoTransportSetting_IPv4TCPPrimary_IPv4UDPSecondary)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   repro::ReproRunner* repro_7090 = runRepro("repro_auth2.config"); // Port 7090
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   TestAccount alice("alice", Account_NoInit);
   configureAccount(alice);
   alice.config.settings.outboundProxy = "pritcpv4secudpv4.local"; // primary is TCP Port 7080 and secondary is UDP Port 7090
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_Auto;

   alice.init();
   alice.account->setTFTimerValueMs(10000);

   ASSERT_TRUE(repro_7080->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7090->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   delete repro_7080;

   alice.account->enable(alice.handle);
   {
      assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_UDP);
      assertOnClientAuthUntilEx(alice, 7090, 200, false);
   }

   repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   assertOnDnsResetEx(alice, 7080, IpVersion_V4, SipAccountTransport_TCP);
   assertAccountRefreshingEx(alice);
   resip::Helper::setNonceHelper(NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_TCP);
   assertOnClientAuthUntilEx(alice, 7080, 200, false);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro_7080;
   delete repro_7090;
   runOriginalRepro();
}

TEST_F(DnsFailoverTest, Switchover_IPv4PreferredAutoTransportSetting_IPv4TCPPrimary_IPv6TCPSecondary)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   repro::ReproRunner* repro_7090 = runRepro("repro_auth2.config"); // Port 7090
   ASSERT_TRUE(repro_7090->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   TestAccount alice("alice", Account_NoInit);
   configureAccount(alice);
   alice.config.settings.outboundProxy = "pritcpv4sectcpv6.local"; // primary is TCP Port 7080 and secondary is TCP Port 7090
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_Auto;

   alice.init();
   alice.account->setTFTimerValueMs(10000);

   ASSERT_TRUE(repro_7080->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7090->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   delete repro_7080;

   alice.account->enable(alice.handle);
   {
      assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V6, CPCAPI2::SipAccount::SipAccountTransport_TCP);
      assertOnClientAuthUntilEx(alice, 7090, 200, false);
   }

   repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   assertOnDnsResetEx(alice, 7080, IpVersion_V4, SipAccountTransport_TCP);
   assertAccountRefreshingEx(alice);
   resip::Helper::setNonceHelper(NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_TCP);
   assertOnClientAuthUntilEx(alice, 7080, 200, false);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro_7080;
   delete repro_7090;
   runOriginalRepro();
}

TEST_F(DnsFailoverTest, Switchover_IPv4PreferredAutoTransportSetting_IPv4TCPPrimary_IPv6UDPSecondary)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   repro::ReproRunner* repro_7090 = runRepro("repro_auth2.config"); // Port 7090
   ASSERT_TRUE(repro_7090->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   TestAccount alice("alice", Account_NoInit);
   configureAccount(alice);
   alice.config.settings.outboundProxy = "pritcpv4secudpv6.local"; // primary is TCP Port 7080 and secondary is UDP Port 7090
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_Auto;

   alice.init();
   alice.account->setTFTimerValueMs(10000);

   ASSERT_TRUE(repro_7080->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7090->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   delete repro_7080;

   alice.account->enable(alice.handle);
   {
      assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V6, CPCAPI2::SipAccount::SipAccountTransport_UDP);
      assertOnClientAuthUntilEx(alice, 7090, 200, false);
   }

   repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   assertOnDnsResetEx(alice, 7080, IpVersion_V4, SipAccountTransport_TCP);
   assertAccountRefreshingEx(alice);
   resip::Helper::setNonceHelper(NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_TCP);
   assertOnClientAuthUntilEx(alice, 7080, 200, false);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro_7080;
   delete repro_7090;
   runOriginalRepro();
}

// TODO: TCP still wins dns query despite higher priority and weight for UDP in the dns config file
TEST_F(DnsFailoverTest, DISABLED_Switchover_IPv4PreferredAutoTransportSetting_IPv4UDPPrimary_IPv4TCPSecondary)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   repro::ReproRunner* repro_7090 = runRepro("repro_auth2.config"); // Port 7090
   ASSERT_TRUE(repro_7090->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   TestAccount alice("alice", Account_NoInit);
   configureAccount(alice);
   alice.config.settings.outboundProxy = "priudpv4sectcpv4.local"; // primary is UDP Port 7080 and secondary is TCP Port 7090
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_Auto;

   alice.init();
   alice.account->setTFTimerValueMs(10000);

   ASSERT_TRUE(repro_7080->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7090->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   delete repro_7080;

   alice.account->enable(alice.handle);
   {
      assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_TCP);
      assertOnClientAuthUntilEx(alice, 7090, 200, false);
   }

   repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   assertOnDnsResetEx(alice, 7080, IpVersion_V4, SipAccountTransport_UDP);
   assertAccountRefreshingEx(alice);
   resip::Helper::setNonceHelper(NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_UDP);
   assertOnClientAuthUntilEx(alice, 7080, 200, false);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro_7080;
   delete repro_7090;
   runOriginalRepro();
}

TEST_F(DnsFailoverTest, Switchover_IPv4PreferredAutoTransportSetting_IPv4UDPPrimary_IPv4UDPSecondary)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   repro::ReproRunner* repro_7090 = runRepro("repro_auth2.config"); // Port 7090
   ASSERT_TRUE(repro_7090->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   TestAccount alice("alice", Account_NoInit);
   configureAccount(alice);
   alice.config.settings.outboundProxy = "priudpv4secudpv4.local"; // primary is UDP Port 7080 and secondary is UDP Port 7090
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_Auto;

   alice.init();
   alice.account->setTFTimerValueMs(10000);

   ASSERT_TRUE(repro_7080->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7090->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   delete repro_7080;

   alice.account->enable(alice.handle);
   {
      assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_UDP);
      assertOnClientAuthUntilEx(alice, 7090, 200, false);
   }

   repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   assertOnDnsResetEx(alice, 7080, IpVersion_V4, SipAccountTransport_UDP);
   assertAccountRefreshingEx(alice);
   resip::Helper::setNonceHelper(NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_UDP);
   assertOnClientAuthUntilEx(alice, 7080, 200, false);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro_7080;
   delete repro_7090;
   runOriginalRepro();
}

// TODO: TCP still wins dns query despite higher priority and weight for UDP in the dns config file
TEST_F(DnsFailoverTest, DISABLED_Switchover_IPv4PreferredAutoTransportSetting_IPv4UDPPrimary_IPv6TCPSecondary)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   repro::ReproRunner* repro_7090 = runRepro("repro_auth2.config"); // Port 7090
   ASSERT_TRUE(repro_7090->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   TestAccount alice("alice", Account_NoInit);
   configureAccount(alice);
   alice.config.settings.outboundProxy = "priudpv4sectcpv6.local"; // primary is UDP Port 7080 and secondary is TCP Port 7090
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_Auto;

   alice.init();
   alice.account->setTFTimerValueMs(10000);

   ASSERT_TRUE(repro_7080->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7090->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   delete repro_7080;

   alice.account->enable(alice.handle);
   {
      assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V6, CPCAPI2::SipAccount::SipAccountTransport_TCP);
      assertOnClientAuthUntilEx(alice, 7090, 200, false);
   }

   repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   assertOnDnsResetEx(alice, 7080, IpVersion_V4, SipAccountTransport_UDP);
   assertAccountRefreshingEx(alice);
   resip::Helper::setNonceHelper(NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_UDP);
   assertOnClientAuthUntilEx(alice, 7080, 200, false);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro_7080;
   delete repro_7090;
   runOriginalRepro();
}

TEST_F(DnsFailoverTest, Switchover_IPv4PreferredAutoTransportSetting_IPv4UDPPrimary_IPv6UDPSecondary)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   repro::ReproRunner* repro_7090 = runRepro("repro_auth2.config"); // Port 7090
   ASSERT_TRUE(repro_7090->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   TestAccount alice("alice", Account_NoInit);
   configureAccount(alice);
   alice.config.settings.outboundProxy = "priudpv4secudpv6.local"; // primary is UDP Port 7080 and secondary is UDP Port 7090
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_Auto;

   alice.init();
   alice.account->setTFTimerValueMs(10000);

   ASSERT_TRUE(repro_7080->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7090->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   delete repro_7080;

   alice.account->enable(alice.handle);
   {
      assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V6, CPCAPI2::SipAccount::SipAccountTransport_UDP);
      assertOnClientAuthUntilEx(alice, 7090, 200, false);
   }

   repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   assertOnDnsResetEx(alice, 7080, IpVersion_V4, SipAccountTransport_UDP);
   assertAccountRefreshingEx(alice);
   resip::Helper::setNonceHelper(NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_UDP);
   assertOnClientAuthUntilEx(alice, 7080, 200, false);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro_7080;
   delete repro_7090;
   runOriginalRepro();
}

TEST_F(DnsFailoverTest, Switchover_IPv4PreferredAutoTransportSetting_IPv6TCPPrimary_IPv4TCPSecondary)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   repro::ReproRunner* repro_7090 = runRepro("repro_auth2.config"); // Port 7090
   ASSERT_TRUE(repro_7090->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   TestAccount alice("alice", Account_NoInit);
   configureAccount(alice);
   alice.config.settings.outboundProxy = "pritcpv6sectcpv4.local"; // primary is TCP Port 7080 and secondary is TCP Port 7090
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_Auto;

   alice.init();
   alice.account->setTFTimerValueMs(10000);

   ASSERT_TRUE(repro_7080->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7090->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   delete repro_7080;

   alice.account->enable(alice.handle);
   {
      assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_TCP);
      assertOnClientAuthUntilEx(alice, 7090, 200, false);
   }

   repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   assertOnDnsResetEx(alice, 7080, IpVersion_V6, SipAccountTransport_TCP);
   assertAccountRefreshingEx(alice);
   resip::Helper::setNonceHelper(NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V6, CPCAPI2::SipAccount::SipAccountTransport_TCP);
   assertOnClientAuthUntilEx(alice, 7080, 200, false);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro_7080;
   delete repro_7090;
   runOriginalRepro();
}

TEST_F(DnsFailoverTest, Switchover_IPv4PreferredAutoTransportSetting_IPv6TCPPrimary_IPv4UDPSecondary)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   repro::ReproRunner* repro_7090 = runRepro("repro_auth2.config"); // Port 7090
   ASSERT_TRUE(repro_7090->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   TestAccount alice("alice", Account_NoInit);
   configureAccount(alice);
   alice.config.settings.outboundProxy = "pritcpv6secudpv4.local"; // primary is TCP Port 7080 and secondary is UDP Port 7090
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_Auto;

   alice.init();
   alice.account->setTFTimerValueMs(10000);

   ASSERT_TRUE(repro_7080->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7090->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   delete repro_7080;

   alice.account->enable(alice.handle);
   {
      assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_UDP);
      assertOnClientAuthUntilEx(alice, 7090, 200, false);
   }

   repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   assertOnDnsResetEx(alice, 7080, IpVersion_V6, SipAccountTransport_TCP);
   assertAccountRefreshingEx(alice);
   resip::Helper::setNonceHelper(NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V6, CPCAPI2::SipAccount::SipAccountTransport_TCP);
   assertOnClientAuthUntilEx(alice, 7080, 200, false);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro_7080;
   delete repro_7090;
   runOriginalRepro();
}

TEST_F(DnsFailoverTest, Switchover_IPv4PreferredAutoTransportSetting_IPv6TCPPrimary_IPv6TCPSecondary)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   repro::ReproRunner* repro_7090 = runRepro("repro_auth2.config"); // Port 7090
   ASSERT_TRUE(repro_7090->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   TestAccount alice("alice", Account_NoInit);
   configureAccount(alice);
   alice.config.settings.outboundProxy = "pritcpv6sectcpv6.local"; // primary is TCP Port 7080 and secondary is TCP Port 7090
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_Auto;

   alice.init();
   alice.account->setTFTimerValueMs(10000);

   ASSERT_TRUE(repro_7080->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7090->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   delete repro_7080;

   alice.account->enable(alice.handle);
   {
      assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V6, CPCAPI2::SipAccount::SipAccountTransport_TCP);
      assertOnClientAuthUntilEx(alice, 7090, 200, false);
   }

   repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   assertOnDnsResetEx(alice, 7080, IpVersion_V6, SipAccountTransport_TCP);
   assertAccountRefreshingEx(alice);
   resip::Helper::setNonceHelper(NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V6, CPCAPI2::SipAccount::SipAccountTransport_TCP);
   assertOnClientAuthUntilEx(alice, 7080, 200, false);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro_7080;
   delete repro_7090;
   runOriginalRepro();
}

TEST_F(DnsFailoverTest, Switchover_IPv4PreferredAutoTransportSetting_IPv6TCPPrimary_IPv6UDPSecondary)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   repro::ReproRunner* repro_7090 = runRepro("repro_auth2.config"); // Port 7090
   ASSERT_TRUE(repro_7090->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   TestAccount alice("alice", Account_NoInit);
   configureAccount(alice);
   alice.config.settings.outboundProxy = "pritcpv6secudpv6.local"; // primary is TCP Port 7080 and secondary is UDP Port 7090
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_Auto;

   alice.init();
   alice.account->setTFTimerValueMs(10000);

   ASSERT_TRUE(repro_7080->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7090->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   delete repro_7080;

   alice.account->enable(alice.handle);
   {
      assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V6, CPCAPI2::SipAccount::SipAccountTransport_UDP);
      assertOnClientAuthUntilEx(alice, 7090, 200, false);
   }

   repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   assertOnDnsResetEx(alice, 7080, IpVersion_V6, SipAccountTransport_TCP);
   assertAccountRefreshingEx(alice);
   resip::Helper::setNonceHelper(NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V6, CPCAPI2::SipAccount::SipAccountTransport_TCP);
   assertOnClientAuthUntilEx(alice, 7080, 200, false);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro_7080;
   delete repro_7090;
   runOriginalRepro();
}

// TODO: TCP still wins dns query despite higher priority and weight for UDP in the dns config file
TEST_F(DnsFailoverTest, DISABLED_Switchover_IPv4PreferredAutoTransportSetting_IPv6UDPPrimary_IPv4TCPSecondary)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   repro::ReproRunner* repro_7090 = runRepro("repro_auth2.config"); // Port 7090
   ASSERT_TRUE(repro_7090->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   TestAccount alice("alice", Account_NoInit);
   configureAccount(alice);
   alice.config.settings.outboundProxy = "priudpv6sectcpv4.local"; // primary is UDP Port 7080 and secondary is TCP Port 7090
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_Auto;

   alice.init();
   alice.account->setTFTimerValueMs(10000);

   ASSERT_TRUE(repro_7080->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7090->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   delete repro_7080;

   alice.account->enable(alice.handle);
   {
      assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_TCP);
      assertOnClientAuthUntilEx(alice, 7090, 200, false);
   }

   repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   assertOnDnsResetEx(alice, 7080, IpVersion_V6, SipAccountTransport_UDP);
   assertAccountRefreshingEx(alice);
   resip::Helper::setNonceHelper(NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V6, CPCAPI2::SipAccount::SipAccountTransport_UDP);
   assertOnClientAuthUntilEx(alice, 7080, 200, false);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro_7080;
   delete repro_7090;
   runOriginalRepro();
}

TEST_F(DnsFailoverTest, Switchover_IPv4PreferredAutoTransportSetting_IPv6UDPPrimary_IPv4UDPSecondary)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   repro::ReproRunner* repro_7090 = runRepro("repro_auth2.config"); // Port 7090
   ASSERT_TRUE(repro_7090->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   TestAccount alice("alice", Account_NoInit);
   configureAccount(alice);
   alice.config.settings.outboundProxy = "priudpv6secudpv4.local"; // primary is UDP Port 7080 and secondary is UDP Port 7090
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_Auto;

   alice.init();
   alice.account->setTFTimerValueMs(10000);

   ASSERT_TRUE(repro_7080->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7090->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   delete repro_7080;

   alice.account->enable(alice.handle);
   {
      assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_UDP);
      assertOnClientAuthUntilEx(alice, 7090, 200, false);
   }

   repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   assertOnDnsResetEx(alice, 7080, IpVersion_V6, SipAccountTransport_UDP);
   assertAccountRefreshingEx(alice);
   resip::Helper::setNonceHelper(NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V6, CPCAPI2::SipAccount::SipAccountTransport_UDP);
   assertOnClientAuthUntilEx(alice, 7080, 200, false);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro_7080;
   delete repro_7090;
   runOriginalRepro();
}

// TODO: TCP still wins dns query despite higher priority and weight for UDP in the dns config file
TEST_F(DnsFailoverTest, DISABLED_Switchover_IPv4PreferredAutoTransportSetting_IPv6UDPPrimary_IPv6TCPSecondary)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   repro::ReproRunner* repro_7090 = runRepro("repro_auth2.config"); // Port 7090
   ASSERT_TRUE(repro_7090->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   TestAccount alice("alice", Account_NoInit);
   configureAccount(alice);
   alice.config.settings.outboundProxy = "priudpv6sectcpv6.local"; // primary is UDP Port 7080 and secondary is TCP Port 7090
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_Auto;

   alice.init();
   alice.account->setTFTimerValueMs(10000);

   ASSERT_TRUE(repro_7080->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7090->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   delete repro_7080;

   alice.account->enable(alice.handle);
   {
      assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V6, CPCAPI2::SipAccount::SipAccountTransport_TCP);
      assertOnClientAuthUntilEx(alice, 7090, 200, false);
   }

   repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   assertOnDnsResetEx(alice, 7080, IpVersion_V6, SipAccountTransport_UDP);
   assertAccountRefreshingEx(alice);
   resip::Helper::setNonceHelper(NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V6, CPCAPI2::SipAccount::SipAccountTransport_UDP);
   assertOnClientAuthUntilEx(alice, 7080, 200, false);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro_7080;
   delete repro_7090;
   runOriginalRepro();
}

TEST_F(DnsFailoverTest, Switchover_IPv4PreferredAutoTransportSetting_IPv6UDPPrimary_IPv6UDPSecondary)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   repro::ReproRunner* repro_7090 = runRepro("repro_auth2.config"); // Port 7090
   ASSERT_TRUE(repro_7090->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   TestAccount alice("alice", Account_NoInit);
   configureAccount(alice);
   alice.config.settings.outboundProxy = "priudpv6secudpv6.local"; // primary is UDP Port 7080 and secondary is UDP Port 7090
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_Auto;

   alice.init();
   alice.account->setTFTimerValueMs(10000);

   ASSERT_TRUE(repro_7080->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7090->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   delete repro_7080;

   alice.account->enable(alice.handle);
   {
      assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V6, CPCAPI2::SipAccount::SipAccountTransport_UDP);
      assertOnClientAuthUntilEx(alice, 7090, 200, false);
   }

   repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   assertOnDnsResetEx(alice, 7080, IpVersion_V6, SipAccountTransport_UDP);
   assertAccountRefreshingEx(alice);
   resip::Helper::setNonceHelper(NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V6, CPCAPI2::SipAccount::SipAccountTransport_UDP);
   assertOnClientAuthUntilEx(alice, 7080, 200, false);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro_7080;
   delete repro_7090;
   runOriginalRepro();
}

TEST_F(DnsFailoverTest, Switchover_IPv6PreferredAutoTransportSetting_IPv4TCPPrimary_IPv4TCPSecondary)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   repro::ReproRunner* repro_7090 = runRepro("repro_auth2.config"); // Port 7090
   ASSERT_TRUE(repro_7090->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   TestAccount alice("alice", Account_NoInit);
   configureAccount(alice);
   alice.config.settings.outboundProxy = "pritcpv4sectcpv4.local"; // primary is TCP Port 7080 and secondary is TCP Port 7090
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_Auto_PreferV6;

   alice.init();
   alice.account->setTFTimerValueMs(10000);

   ASSERT_TRUE(repro_7080->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7090->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   delete repro_7080;

   alice.account->enable(alice.handle);
   {
      assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_TCP);
      assertOnClientAuthUntilEx(alice, 7090, 200, false);
   }

   repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   assertOnDnsResetEx(alice, 7080, IpVersion_V4, SipAccountTransport_TCP);
   assertAccountRefreshingEx(alice);
   resip::Helper::setNonceHelper(NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_TCP);
   assertOnClientAuthUntilEx(alice, 7080, 200, false);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro_7080;
   delete repro_7090;
   runOriginalRepro();
}

TEST_F(DnsFailoverTest, Switchover_IPv6PreferredAutoTransportSetting_IPv4TCPPrimary_IPv4UDPSecondary)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   repro::ReproRunner* repro_7090 = runRepro("repro_auth2.config"); // Port 7090
   ASSERT_TRUE(repro_7090->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   TestAccount alice("alice", Account_NoInit);
   configureAccount(alice);
   alice.config.settings.outboundProxy = "pritcpv4secudpv4.local"; // primary is TCP Port 7080 and secondary is UDP Port 7090
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_Auto_PreferV6;

   alice.init();
   alice.account->setTFTimerValueMs(10000);

   ASSERT_TRUE(repro_7080->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7090->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   delete repro_7080;

   alice.account->enable(alice.handle);
   {
      assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_UDP);
      assertOnClientAuthUntilEx(alice, 7090, 200, false);
   }

   repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   assertOnDnsResetEx(alice, 7080, IpVersion_V4, SipAccountTransport_TCP);
   assertAccountRefreshingEx(alice);
   resip::Helper::setNonceHelper(NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_TCP);
   assertOnClientAuthUntilEx(alice, 7080, 200, false);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro_7080;
   delete repro_7090;
   runOriginalRepro();
}

TEST_F(DnsFailoverTest, Switchover_IPv6PreferredAutoTransportSetting_IPv4TCPPrimary_IPv6TCPSecondary)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   repro::ReproRunner* repro_7090 = runRepro("repro_auth2.config"); // Port 7090
   ASSERT_TRUE(repro_7090->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   TestAccount alice("alice", Account_NoInit);
   configureAccount(alice);
   alice.config.settings.outboundProxy = "pritcpv4sectcpv6.local"; // primary is TCP Port 7080 and secondary is TCP Port 7090
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_Auto_PreferV6;

   alice.init();
   alice.account->setTFTimerValueMs(10000);

   ASSERT_TRUE(repro_7080->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7090->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   delete repro_7080;

   alice.account->enable(alice.handle);
   {
      assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V6, CPCAPI2::SipAccount::SipAccountTransport_TCP);
      assertOnClientAuthUntilEx(alice, 7090, 200, false);
   }

   repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   assertOnDnsResetEx(alice, 7080, IpVersion_V4, SipAccountTransport_TCP);
   assertAccountRefreshingEx(alice);
   resip::Helper::setNonceHelper(NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_TCP);
   assertOnClientAuthUntilEx(alice, 7080, 200, false);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro_7080;
   delete repro_7090;
   runOriginalRepro();
}

TEST_F(DnsFailoverTest, Switchover_IPv6PreferredAutoTransportSetting_IPv4TCPPrimary_IPv6UDPSecondary)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   repro::ReproRunner* repro_7090 = runRepro("repro_auth2.config"); // Port 7090
   ASSERT_TRUE(repro_7090->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   TestAccount alice("alice", Account_NoInit);
   configureAccount(alice);
   alice.config.settings.outboundProxy = "pritcpv4secudpv6.local"; // primary is TCP Port 7080 and secondary is UDP Port 7090
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_Auto_PreferV6;

   alice.init();
   alice.account->setTFTimerValueMs(10000);

   ASSERT_TRUE(repro_7080->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7090->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   delete repro_7080;

   alice.account->enable(alice.handle);
   {
      assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V6, CPCAPI2::SipAccount::SipAccountTransport_UDP);
      assertOnClientAuthUntilEx(alice, 7090, 200, false);
   }

   repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   assertOnDnsResetEx(alice, 7080, IpVersion_V4, SipAccountTransport_TCP);
   assertAccountRefreshingEx(alice);
   resip::Helper::setNonceHelper(NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_TCP);
   assertOnClientAuthUntilEx(alice, 7080, 200, false);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro_7080;
   delete repro_7090;
   runOriginalRepro();
}

// TODO: TCP still wins dns query despite higher priority and weight for UDP in the dns config file
TEST_F(DnsFailoverTest, DISABLED_Switchover_IPv6PreferredAutoTransportSetting_IPv4UDPPrimary_IPv4TCPSecondary)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   repro::ReproRunner* repro_7090 = runRepro("repro_auth2.config"); // Port 7090
   ASSERT_TRUE(repro_7090->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   TestAccount alice("alice", Account_NoInit);
   configureAccount(alice);
   alice.config.settings.outboundProxy = "priudpv4sectcpv4.local"; // primary is UDP Port 7080 and secondary is TCP Port 7090
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_Auto_PreferV6;

   alice.init();
   alice.account->setTFTimerValueMs(10000);

   ASSERT_TRUE(repro_7080->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7090->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   delete repro_7080;

   alice.account->enable(alice.handle);
   {
      assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_TCP);
      assertOnClientAuthUntilEx(alice, 7090, 200, false);
   }

   repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   assertOnDnsResetEx(alice, 7080, IpVersion_V4, SipAccountTransport_UDP);
   assertAccountRefreshingEx(alice);
   resip::Helper::setNonceHelper(NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_UDP);
   assertOnClientAuthUntilEx(alice, 7080, 200, false);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro_7080;
   delete repro_7090;
   runOriginalRepro();
}

TEST_F(DnsFailoverTest, Switchover_IPv6PreferredAutoTransportSetting_IPv4UDPPrimary_IPv4UDPSecondary)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   repro::ReproRunner* repro_7090 = runRepro("repro_auth2.config"); // Port 7090
   ASSERT_TRUE(repro_7090->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   TestAccount alice("alice", Account_NoInit);
   configureAccount(alice);
   alice.config.settings.outboundProxy = "priudpv4secudpv4.local"; // primary is UDP Port 7080 and secondary is UDP Port 7090
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_Auto_PreferV6;

   alice.init();
   alice.account->setTFTimerValueMs(10000);

   ASSERT_TRUE(repro_7080->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7090->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   delete repro_7080;

   alice.account->enable(alice.handle);
   {
      assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_UDP);
      assertOnClientAuthUntilEx(alice, 7090, 200, false);
   }

   repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   assertOnDnsResetEx(alice, 7080, IpVersion_V4, SipAccountTransport_UDP);
   assertAccountRefreshingEx(alice);
   resip::Helper::setNonceHelper(NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_UDP);
   assertOnClientAuthUntilEx(alice, 7080, 200, false);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro_7080;
   delete repro_7090;
   runOriginalRepro();
}

// TODO: TCP still wins dns query despite higher priority and weight for UDP in the dns config file
TEST_F(DnsFailoverTest, DISABLED_Switchover_IPv6PreferredAutoTransportSetting_IPv4UDPPrimary_IPv6TCPSecondary)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   repro::ReproRunner* repro_7090 = runRepro("repro_auth2.config"); // Port 7090
   ASSERT_TRUE(repro_7090->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   TestAccount alice("alice", Account_NoInit);
   configureAccount(alice);
   alice.config.settings.outboundProxy = "priudpv4sectcpv6.local"; // primary is UDP Port 7080 and secondary is TCP Port 7090
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_Auto_PreferV6;

   alice.init();
   alice.account->setTFTimerValueMs(10000);

   ASSERT_TRUE(repro_7080->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7090->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   delete repro_7080;

   alice.account->enable(alice.handle);
   {
      assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V6, CPCAPI2::SipAccount::SipAccountTransport_TCP);
      assertOnClientAuthUntilEx(alice, 7090, 200, false);
   }

   repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   assertOnDnsResetEx(alice, 7080, IpVersion_V4, SipAccountTransport_UDP);
   assertAccountRefreshingEx(alice);
   resip::Helper::setNonceHelper(NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_UDP);
   assertOnClientAuthUntilEx(alice, 7080, 200, false);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro_7080;
   delete repro_7090;
   runOriginalRepro();
}

TEST_F(DnsFailoverTest, Switchover_IPv6PreferredAutoTransportSetting_IPv4UDPPrimary_IPv6UDPSecondary)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   repro::ReproRunner* repro_7090 = runRepro("repro_auth2.config"); // Port 7090
   ASSERT_TRUE(repro_7090->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   TestAccount alice("alice", Account_NoInit);
   configureAccount(alice);
   alice.config.settings.outboundProxy = "priudpv4secudpv6.local"; // primary is UDP Port 7080 and secondary is UDP Port 7090
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_Auto_PreferV6;

   alice.init();
   alice.account->setTFTimerValueMs(10000);

   ASSERT_TRUE(repro_7080->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7090->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   delete repro_7080;

   alice.account->enable(alice.handle);
   {
      assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V6, CPCAPI2::SipAccount::SipAccountTransport_UDP);
      assertOnClientAuthUntilEx(alice, 7090, 200, false);
   }

   repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   assertOnDnsResetEx(alice, 7080, IpVersion_V4, SipAccountTransport_UDP);
   assertAccountRefreshingEx(alice);
   resip::Helper::setNonceHelper(NULL);
   
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_UDP);
   assertOnClientAuthUntilEx(alice, 7080, 200, false);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro_7080;
   delete repro_7090;
   runOriginalRepro();
}

TEST_F(DnsFailoverTest, Switchover_IPv6PreferredAutoTransportSetting_IPv6TCPPrimary_IPv4TCPSecondary)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   repro::ReproRunner* repro_7090 = runRepro("repro_auth2.config"); // Port 7090
   ASSERT_TRUE(repro_7090->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   TestAccount alice("alice", Account_NoInit);
   configureAccount(alice);
   alice.config.settings.outboundProxy = "pritcpv6sectcpv4.local"; // primary is TCP Port 7080 and secondary is TCP Port 7090
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_Auto_PreferV6;

   alice.init();
   alice.account->setTFTimerValueMs(10000);

   ASSERT_TRUE(repro_7080->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7090->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   delete repro_7080;

   alice.account->enable(alice.handle);
   {
      assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_TCP);
      assertOnClientAuthUntilEx(alice, 7090, 200, false);
   }

   repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   assertOnDnsResetEx(alice, 7080, IpVersion_V6, SipAccountTransport_TCP);
   assertAccountRefreshingEx(alice);
   resip::Helper::setNonceHelper(NULL);
   
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V6, CPCAPI2::SipAccount::SipAccountTransport_TCP);
   assertOnClientAuthUntilEx(alice, 7080, 200, false);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro_7080;
   delete repro_7090;
   runOriginalRepro();
}

TEST_F(DnsFailoverTest, Switchover_IPv6PreferredAutoTransportSetting_IPv6TCPPrimary_IPv4UDPSecondary)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   repro::ReproRunner* repro_7090 = runRepro("repro_auth2.config"); // Port 7090
   ASSERT_TRUE(repro_7090->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   TestAccount alice("alice", Account_NoInit);
   configureAccount(alice);
   alice.config.settings.outboundProxy = "pritcpv6secudpv4.local"; // primary is TCP Port 7080 and secondary is UDP Port 7090
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_Auto_PreferV6;

   alice.init();
   alice.account->setTFTimerValueMs(10000);

   ASSERT_TRUE(repro_7080->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7090->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   delete repro_7080;

   alice.account->enable(alice.handle);
   {
      assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_UDP);
      assertOnClientAuthUntilEx(alice, 7090, 200, false);
   }

   repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   assertOnDnsResetEx(alice, 7080, IpVersion_V6, SipAccountTransport_TCP);
   assertAccountRefreshingEx(alice);
   resip::Helper::setNonceHelper(NULL);
   
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V6, CPCAPI2::SipAccount::SipAccountTransport_TCP);
   assertOnClientAuthUntilEx(alice, 7080, 200, false);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro_7080;
   delete repro_7090;
   runOriginalRepro();
}

TEST_F(DnsFailoverTest, Switchover_IPv6PreferredAutoTransportSetting_IPv6TCPPrimary_IPv6TCPSecondary)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   repro::ReproRunner* repro_7090 = runRepro("repro_auth2.config"); // Port 7090
   ASSERT_TRUE(repro_7090->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   TestAccount alice("alice", Account_NoInit);
   configureAccount(alice);
   alice.config.settings.outboundProxy = "pritcpv6sectcpv6.local"; // primary is TCP Port 7080 and secondary is TCP Port 7090
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_Auto_PreferV6;

   alice.init();
   alice.account->setTFTimerValueMs(10000);

   ASSERT_TRUE(repro_7080->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7090->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   delete repro_7080;

   alice.account->enable(alice.handle);
   {
      assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V6, CPCAPI2::SipAccount::SipAccountTransport_TCP);
      assertOnClientAuthUntilEx(alice, 7090, 200, false);
   }

   repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   assertOnDnsResetEx(alice, 7080, IpVersion_V6, SipAccountTransport_TCP);
   assertAccountRefreshingEx(alice);
   resip::Helper::setNonceHelper(NULL);
   
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V6, CPCAPI2::SipAccount::SipAccountTransport_TCP);
   assertOnClientAuthUntilEx(alice, 7080, 200, false);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro_7080;
   delete repro_7090;
   runOriginalRepro();
}

TEST_F(DnsFailoverTest, Switchover_IPv6PreferredAutoTransportSetting_IPv6TCPPrimary_IPv6UDPSecondary)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   repro::ReproRunner* repro_7090 = runRepro("repro_auth2.config"); // Port 7090
   ASSERT_TRUE(repro_7090->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   TestAccount alice("alice", Account_NoInit);
   configureAccount(alice);
   alice.config.settings.outboundProxy = "pritcpv6secudpv6.local"; // primary is TCP Port 7080 and secondary is UDP Port 7090
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_Auto_PreferV6;

   alice.init();
   alice.account->setTFTimerValueMs(10000);

   ASSERT_TRUE(repro_7080->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7090->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   delete repro_7080;

   alice.account->enable(alice.handle);
   {
      assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V6, CPCAPI2::SipAccount::SipAccountTransport_UDP);
      assertOnClientAuthUntilEx(alice, 7090, 200, false);
   }

   repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   assertOnDnsResetEx(alice, 7080, IpVersion_V6, SipAccountTransport_TCP);
   assertAccountRefreshingEx(alice);
   resip::Helper::setNonceHelper(NULL);
   
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V6, CPCAPI2::SipAccount::SipAccountTransport_TCP);
   assertOnClientAuthUntilEx(alice, 7080, 200, false);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro_7080;
   delete repro_7090;
   runOriginalRepro();
}

// TODO: TCP still wins dns query despite higher priority and weight for UDP in the dns config file
TEST_F(DnsFailoverTest, DISABLED_Switchover_IPv6PreferredAutoTransportSetting_IPv6UDPPrimary_IPv4TCPSecondary)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   repro::ReproRunner* repro_7090 = runRepro("repro_auth2.config"); // Port 7090
   ASSERT_TRUE(repro_7090->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   TestAccount alice("alice", Account_NoInit);
   configureAccount(alice);
   alice.config.settings.outboundProxy = "priudpv6sectcpv4.local"; // primary is UDP Port 7080 and secondary is TCP Port 7090
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_Auto_PreferV6;

   alice.init();
   alice.account->setTFTimerValueMs(10000);

   ASSERT_TRUE(repro_7080->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7090->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   delete repro_7080;

   alice.account->enable(alice.handle);
   {
      assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_TCP);
      assertOnClientAuthUntilEx(alice, 7090, 200, false);
   }

   repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   assertOnDnsResetEx(alice, 7080, IpVersion_V6, SipAccountTransport_UDP);
   assertAccountRefreshingEx(alice);
   resip::Helper::setNonceHelper(NULL);
   
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V6, CPCAPI2::SipAccount::SipAccountTransport_UDP);
   assertOnClientAuthUntilEx(alice, 7080, 200, false);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro_7080;
   delete repro_7090;
   runOriginalRepro();
}

TEST_F(DnsFailoverTest, Switchover_IPv6PreferredAutoTransportSetting_IPv6UDPPrimary_IPv4UDPSecondary)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   repro::ReproRunner* repro_7090 = runRepro("repro_auth2.config"); // Port 7090
   ASSERT_TRUE(repro_7090->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   TestAccount alice("alice", Account_NoInit);
   configureAccount(alice);
   alice.config.settings.outboundProxy = "priudpv6secudpv4.local"; // primary is UDP Port 7080 and secondary is UDP Port 7090
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_Auto_PreferV6;

   alice.init();
   alice.account->setTFTimerValueMs(10000);

   ASSERT_TRUE(repro_7080->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7090->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   delete repro_7080;

   alice.account->enable(alice.handle);
   {
      assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_UDP);
      assertOnClientAuthUntilEx(alice, 7090, 200, false);
   }

   repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   assertOnDnsResetEx(alice, 7080, IpVersion_V6, SipAccountTransport_UDP);
   assertAccountRefreshingEx(alice);
   resip::Helper::setNonceHelper(NULL);
   
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V6, CPCAPI2::SipAccount::SipAccountTransport_UDP);
   assertOnClientAuthUntilEx(alice, 7080, 200, false);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro_7080;
   delete repro_7090;
   runOriginalRepro();
}

// TODO: TCP still wins dns query despite higher priority and weight for UDP in the dns config file
TEST_F(DnsFailoverTest, DISABLED_Switchover_IPv6PreferredAutoTransportSetting_IPv6UDPPrimary_IPv6TCPSecondary)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   repro::ReproRunner* repro_7090 = runRepro("repro_auth2.config"); // Port 7090
   ASSERT_TRUE(repro_7090->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   TestAccount alice("alice", Account_NoInit);
   configureAccount(alice);
   alice.config.settings.outboundProxy = "priudpv6sectcpv6.local"; // primary is UDP Port 7080 and secondary is TCP Port 7090
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_Auto_PreferV6;

   alice.init();
   alice.account->setTFTimerValueMs(10000);

   ASSERT_TRUE(repro_7080->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7090->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   delete repro_7080;

   alice.account->enable(alice.handle);
   {
      assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V6, CPCAPI2::SipAccount::SipAccountTransport_TCP);
      assertOnClientAuthUntilEx(alice, 7090, 200, false);
   }

   repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   assertOnDnsResetEx(alice, 7080, IpVersion_V6, SipAccountTransport_UDP);
   assertAccountRefreshingEx(alice);
   resip::Helper::setNonceHelper(NULL);
   
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V6, CPCAPI2::SipAccount::SipAccountTransport_UDP);
   assertOnClientAuthUntilEx(alice, 7080, 200, false);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro_7080;
   delete repro_7090;
   runOriginalRepro();
}

TEST_F(DnsFailoverTest, Switchover_IPv6PreferredAutoTransportSetting_IPv6UDPPrimary_IPv6UDPSecondary)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   repro::ReproRunner* repro_7090 = runRepro("repro_auth2.config"); // Port 7090
   ASSERT_TRUE(repro_7090->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   TestAccount alice("alice", Account_NoInit);
   configureAccount(alice);
   alice.config.settings.outboundProxy = "priudpv6secudpv6.local"; // primary is UDP Port 7080 and secondary is UDP Port 7090
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_Auto_PreferV6;

   alice.init();
   alice.account->setTFTimerValueMs(10000);

   ASSERT_TRUE(repro_7080->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7090->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   delete repro_7080;

   alice.account->enable(alice.handle);
   {
      assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V6, CPCAPI2::SipAccount::SipAccountTransport_UDP);
      assertOnClientAuthUntilEx(alice, 7090, 200, false);
   }

   repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   assertOnDnsResetEx(alice, 7080, IpVersion_V6, SipAccountTransport_UDP);
   assertAccountRefreshingEx(alice);
   resip::Helper::setNonceHelper(NULL);
   
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V6, CPCAPI2::SipAccount::SipAccountTransport_UDP);
   assertOnClientAuthUntilEx(alice, 7080, 200, false);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro_7080;
   delete repro_7090;
   runOriginalRepro();
}

TEST_F(DnsFailoverTest, Switchover_IPv4TCPTransportSetting_IPv4TCPPrimary_IPv4TCPSecondary)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   repro::ReproRunner* repro_7090 = runRepro("repro_auth2.config"); // Port 7090
   ASSERT_TRUE(repro_7090->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   TestAccount alice("alice", Account_NoInit);
   configureAccount(alice);
   alice.config.settings.outboundProxy = "pritcpv4sectcpv4.local"; // primary is TCP Port 7080 and secondary is TCP Port 7090
   alice.config.settings.sipTransportType = SipAccountTransport_TCP;
   alice.config.settings.ipVersion = IpVersion_V4;

   alice.init();
   alice.account->setTFTimerValueMs(10000);

   ASSERT_TRUE(repro_7080->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7090->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   delete repro_7080;

   alice.account->enable(alice.handle);
   {
      assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_TCP);
      assertOnClientAuthUntilEx(alice, 7090, 200, false);
   }

   repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   assertOnDnsResetEx(alice, 7080, IpVersion_V4, SipAccountTransport_TCP);
   assertAccountRefreshingEx(alice);
   resip::Helper::setNonceHelper(NULL);
   
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_TCP);
   assertOnClientAuthUntilEx(alice, 7080, 200, false);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro_7080;
   delete repro_7090;
   runOriginalRepro();
}

TEST_F(DnsFailoverTest, Switchover_IPv4UDPTransportSetting_IPv4UDPPrimary_IPv4UDPSecondary)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   repro::ReproRunner* repro_7090 = runRepro("repro_auth2.config"); // Port 7090
   ASSERT_TRUE(repro_7090->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   TestAccount alice("alice", Account_NoInit);
   configureAccount(alice);
   alice.config.settings.outboundProxy = "priudpv4secudpv4.local"; // primary is UDP Port 7080 and secondary is UDP Port 7090
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.ipVersion = IpVersion_V4;

   alice.init();
   alice.account->setTFTimerValueMs(10000);

   ASSERT_TRUE(repro_7080->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7090->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   delete repro_7080;

   alice.account->enable(alice.handle);
   {
      assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_UDP);
      assertOnClientAuthUntilEx(alice, 7090, 200, false);
   }

   repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   assertOnDnsResetEx(alice, 7080, IpVersion_V4, SipAccountTransport_UDP);
   assertAccountRefreshingEx(alice);
   resip::Helper::setNonceHelper(NULL);
   
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_UDP);
   assertOnClientAuthUntilEx(alice, 7080, 200, false);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro_7080;
   delete repro_7090;
   runOriginalRepro();
}

TEST_F(DnsFailoverTest, Switchover_IPv6TCPTransportSetting_IPv6TCPPrimary_IPv6TCPSecondary)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   repro::ReproRunner* repro_7090 = runRepro("repro_auth2.config"); // Port 7090
   ASSERT_TRUE(repro_7090->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   TestAccount alice("alice", Account_NoInit);
   configureAccount(alice);
   alice.config.settings.outboundProxy = "pritcpv6sectcpv6.local"; // primary is TCP Port 7080 and secondary is TCP Port 7090
   alice.config.settings.sipTransportType = SipAccountTransport_TCP;
   alice.config.settings.ipVersion = IpVersion_V6;

   alice.init();
   alice.account->setTFTimerValueMs(10000);

   ASSERT_TRUE(repro_7080->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7090->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   delete repro_7080;

   alice.account->enable(alice.handle);
   {
      assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V6, CPCAPI2::SipAccount::SipAccountTransport_TCP);
      assertOnClientAuthUntilEx(alice, 7090, 200, false);
   }

   repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   assertOnDnsResetEx(alice, 7080, IpVersion_V6, SipAccountTransport_TCP);
   assertAccountRefreshingEx(alice);
   resip::Helper::setNonceHelper(NULL);
   
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V6, CPCAPI2::SipAccount::SipAccountTransport_TCP);
   assertOnClientAuthUntilEx(alice, 7080, 200, false);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro_7080;
   delete repro_7090;
   runOriginalRepro();
}

TEST_F(DnsFailoverTest, Switchover_IPv6UDPTransportSetting_IPv6UDPPrimary_IPv6UDPSecondary)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   repro::ReproRunner* repro_7090 = runRepro("repro_auth2.config"); // Port 7090
   ASSERT_TRUE(repro_7090->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   TestAccount alice("alice", Account_NoInit);
   configureAccount(alice);
   alice.config.settings.outboundProxy = "priudpv6secudpv6.local"; // primary is UDP Port 7080 and secondary is UDP Port 7090
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.ipVersion = IpVersion_V6;

   alice.init();
   alice.account->setTFTimerValueMs(10000);

   ASSERT_TRUE(repro_7080->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7090->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   delete repro_7080;

   alice.account->enable(alice.handle);
   {
      assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V6, CPCAPI2::SipAccount::SipAccountTransport_UDP);
      assertOnClientAuthUntilEx(alice, 7090, 200, false);
   }

   repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   assertOnDnsResetEx(alice, 7080, IpVersion_V6, SipAccountTransport_UDP);
   assertAccountRefreshingEx(alice);
   resip::Helper::setNonceHelper(NULL);
   
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V6, CPCAPI2::SipAccount::SipAccountTransport_UDP);
   assertOnClientAuthUntilEx(alice, 7080, 200, false);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro_7080;
   delete repro_7090;
   runOriginalRepro();
}

// TODO: This triggers an exception if the target is a V6 address, i.e. ::1 as that results in 3 ':' in a row, fix required in CPOptionsPingManager
TEST_F(DnsFailoverTest, DISABLED_Switchover_IPv4PreferredAutoTransportSetting_IPv4TCPPrimary_IPv6TCPSecondary_PingEnabled)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   repro::ReproRunner* repro_7090 = runRepro("repro_auth2.config"); // Port 7090
   ASSERT_TRUE(repro_7090->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   TestAccount alice("alice", Account_NoInit);
   configureAccount(alice);
   alice.config.settings.outboundProxy = "pritcpv4sectcpv6.local"; // primary is TCP Port 7080 and secondary is TCP Port 7090
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_Auto;
   alice.config.settings.useOptionsPing = true;
   alice.config.settings.optionsPingInterval = 30000;

   alice.init();
   alice.account->setTFTimerValueMs(10000);

   ASSERT_TRUE(repro_7080->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7090->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   delete repro_7080;

   alice.account->enable(alice.handle);
   {
      assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V6, CPCAPI2::SipAccount::SipAccountTransport_TCP);
      assertOnClientAuthUntilEx(alice, 7090, 200, false);
   }

   repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   assertOnDnsResetEx(alice, 7080, IpVersion_V4, SipAccountTransport_TCP);
   assertAccountRefreshingEx(alice);
   resip::Helper::setNonceHelper(NULL);
   
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_TCP);
   assertOnClientAuthUntilEx(alice, 7080, 200, false);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro_7080;
   delete repro_7090;
   runOriginalRepro();
}

TEST_F(DnsFailoverTest, Switchover_IPv4PreferredAutoTransportSetting_IPv4TCPPrimary_IPv4TCPSecondary_PingEnabled)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   repro::ReproRunner* repro_7090 = runRepro("repro_auth2.config"); // Port 7090
   ASSERT_TRUE(repro_7090->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   TestAccount alice("alice", Account_NoInit);
   configureAccount(alice);
   alice.config.settings.outboundProxy = "pritcpv4sectcpv4.local"; // primary is TCP Port 7080 and secondary is TCP Port 7090
   alice.config.settings.sipTransportType = SipAccountTransport_Auto;
   alice.config.settings.ipVersion = IpVersion_Auto;
   alice.config.settings.useOptionsPing = true;
   alice.config.settings.optionsPingInterval = 30000;

   alice.init();
   alice.account->setTFTimerValueMs(10000);

   ASSERT_TRUE(repro_7080->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7090->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   delete repro_7080;

   alice.account->enable(alice.handle);
   {
      assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_TCP);
      assertOnClientAuthUntilEx(alice, 7090, 200, false);
   }

   repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   assertOnDnsResetEx(alice, 7080, IpVersion_V4, SipAccountTransport_TCP);
   assertAccountRefreshingEx(alice);
   resip::Helper::setNonceHelper(NULL);
   
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_TCP);
   assertOnClientAuthUntilEx(alice, 7080, 200, false);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro_7080;
   delete repro_7090;
   runOriginalRepro();
}

TEST_F(DnsFailoverTest, Switchover_IPv4TCPTransportSetting_IPv4TCPPrimary_IPv4TCPSecondary_DisableEnable)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   repro::ReproRunner* repro_7090 = runRepro("repro_auth2.config"); // Port 7090
   ASSERT_TRUE(repro_7090->getProxy() != NULL);
   repro::ReproRunner* repro_7070 = runRepro("repro_auth3.config"); // Port 7070
   ASSERT_TRUE(repro_7070->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   TestAccount alice("alice", Account_NoInit);
   configureAccount(alice);
   alice.config.settings.outboundProxy = "pritcpv4sectcpv4.local"; // primary is TCP Port 7080 and secondary is TCP Port 7090
   alice.config.settings.sipTransportType = SipAccountTransport_TCP;
   alice.config.settings.ipVersion = IpVersion_V4;

   alice.init();
   alice.account->setTFTimerValueMs(10000);

   std::unique_ptr<MyDialogDnsResultHandler> aliceHandler(new MyDialogDnsResultHandler(&alice));
   CPCAPI2::SipAccount::SipAccountManagerInternal* mi = dynamic_cast<CPCAPI2::SipAccount::SipAccountManagerInternal*>(alice.account);
   mi->setDnsHandler(alice.handle, aliceHandler.get());

   ASSERT_TRUE(repro_7080->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7090->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7070->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   delete repro_7080;
   delete repro_7070;

   alice.account->enable(alice.handle);
   {
      assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_TCP);
      assertOnClientAuthUntilEx(alice, 7090, 200, false);
   }

   {
      CPCAPI2::SipAccount::DnsResetStatusChangedEvent evt(CPCAPI2::SipAccount::Dns_Reset_Registered_Secondary_State);
      ASSERT_TRUE(EXPECT_DNS_RESET_STATUS_EVENT(aliceHandler, evt, 60000));
   }

   repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   assertOnDnsResetEx(alice, 7080, IpVersion_V4, SipAccountTransport_TCP);
   assertAccountRefreshingEx(alice);
   resip::Helper::setNonceHelper(NULL);
   
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_TCP);
   assertOnClientAuthUntilEx(alice, 7080, 200, false);

   {
      CPCAPI2::SipAccount::DnsResetStatusChangedEvent evt(CPCAPI2::SipAccount::Dns_Reset_Registered_Primary_State);
      ASSERT_TRUE(EXPECT_DNS_RESET_STATUS_EVENT(aliceHandler, evt, 60000));
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   assertOnClientAuthUntilEx(alice, 7080, 200, false); // Sink the onClientAuth event resulting from the 200 OK in response to the deregistration request

   delete repro_7080;
   delete repro_7090; // Deleting as was carrying previous contact bindings
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   repro_7090 = runRepro("repro_auth2.config"); // Port 7090
   ASSERT_TRUE(repro_7090->getProxy() != NULL);
   alice.config.settings.outboundProxy = "pritcpv4sectcpv4_v2.local"; // primary is TCP Port 7070 and secondary is TCP Port 7090
   alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings);
   alice.account->applySettings(alice.handle);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   alice.account->enable(alice.handle);
   {
      assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_TCP);
      assertOnClientAuthUntilEx(alice, 7090, 200, false);
   }

   {
      CPCAPI2::SipAccount::DnsResetStatusChangedEvent evt(CPCAPI2::SipAccount::Dns_Reset_Registered_Secondary_State);
      ASSERT_TRUE(EXPECT_DNS_RESET_STATUS_EVENT(aliceHandler, evt, 60000));
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));
   repro_7070 = runRepro("repro_auth3.config"); // Port 7070
   ASSERT_TRUE(repro_7070->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   assertOnDnsResetEx(alice, 7070, IpVersion_V4, SipAccountTransport_TCP);
   assertAccountRefreshingEx(alice);
   resip::Helper::setNonceHelper(NULL);
   
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_TCP);
   assertOnClientAuthUntilEx(alice, 7070, 200, false);

   {
      CPCAPI2::SipAccount::DnsResetStatusChangedEvent evt(CPCAPI2::SipAccount::Dns_Reset_Registered_Primary_State);
      ASSERT_TRUE(EXPECT_DNS_RESET_STATUS_EVENT(aliceHandler, evt, 60000));
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   mi->setDnsHandler(alice.handle, NULL);
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro_7070;
   delete repro_7090;
   runOriginalRepro();
}

TEST_F(DnsFailoverTest, Switchover_IPv4TCPTransportSetting_IPv4TCPPrimary_IPv4TCPSecondary_ApplySettings)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   repro::ReproRunner* repro_7090 = runRepro("repro_auth2.config"); // Port 7090
   ASSERT_TRUE(repro_7090->getProxy() != NULL);
   repro::ReproRunner* repro_7070 = runRepro("repro_auth3.config"); // Port 7070
   ASSERT_TRUE(repro_7070->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   TestAccount alice("alice", Account_NoInit);
   configureAccount(alice);
   alice.config.settings.outboundProxy = "pritcpv4sectcpv4.local"; // primary is TCP Port 7080 and secondary is TCP Port 7090
   alice.config.settings.sipTransportType = SipAccountTransport_TCP;
   alice.config.settings.ipVersion = IpVersion_V4;

   alice.init();
   alice.account->setTFTimerValueMs(10000);

   ASSERT_TRUE(repro_7080->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7090->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7070->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   delete repro_7080;
   delete repro_7070;

   alice.account->enable(alice.handle);
   {
      assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_TCP);
      assertOnClientAuthUntilEx(alice, 7090, 200, false);
   }

   repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   assertOnDnsResetEx(alice, 7080, IpVersion_V4, SipAccountTransport_TCP);
   assertAccountRefreshingEx(alice);
   resip::Helper::setNonceHelper(NULL);
   
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_TCP);
   assertOnClientAuthUntilEx(alice, 7080, 200, false);

   alice.config.settings.outboundProxy = "pritcpv4sectcpv4_v2.local"; // primary is TCP Port 7070 and secondary is TCP Port 7090
   alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings);
   alice.account->applySettings(alice.handle);
   {
      assertAccountDeregisteringEx(alice);
      assertAccountDeregisteredEx(alice);
      assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_TCP);
      assertOnClientAuthUntilEx(alice, 7090, 200, false);
   }
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   repro_7070 = runRepro("repro_auth3.config"); // Port 7070
   ASSERT_TRUE(repro_7070->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   assertOnDnsResetEx(alice, 7070, IpVersion_V4, SipAccountTransport_TCP);
   assertAccountRefreshingEx(alice);
   resip::Helper::setNonceHelper(NULL);
   
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_TCP);
   assertOnClientAuthUntilEx(alice, 7070, 200, false);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro_7070;
   delete repro_7080;
   delete repro_7090;
   runOriginalRepro();
}

TEST_F(DnsFailoverTest, Switchover_IPv4PreferredTCPTransportSetting_IPv4TCPPrimary_IPv6TCPSecondary_NetworkChange)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   repro::ReproRunner* repro_7090 = runRepro("repro_auth2.config"); // Port 7090
   ASSERT_TRUE(repro_7090->getProxy() != NULL);
   repro::ReproRunner* repro_7070 = runRepro("repro_auth3.config"); // Port 7070
   ASSERT_TRUE(repro_7070->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   TestAccount alice("alice", Account_NoInit);
   configureAccount(alice);
   alice.config.settings.outboundProxy = "pritcpv4sectcpv6.local"; // primary is TCP Port 7080 and secondary is TCP Port 7090
   alice.config.settings.sipTransportType = SipAccountTransport_TCP;
   alice.config.settings.ipVersion = IpVersion_Auto;

   alice.init();
   alice.network->setNetworkTransport(NetworkTransport::TransportWiFi);
   alice.account->setTFTimerValueMs(10000);

   SipAccountSettings defaultSettings = alice.config.settings;
   SipAccountSettings wifiSettings = defaultSettings;
   SipAccountSettings wwanSettings = defaultSettings;
   wwanSettings.outboundProxy = "pritcpv4sectcpv6_v2.local"; // primary is TCP Port 7070 and secondary is TCP Port 7090

   ASSERT_EQ(alice.account->configureDefaultAccountSettings(alice.handle, defaultSettings), kSuccess);
   ASSERT_EQ(alice.account->configureTransportAccountSettings(alice.handle, wifiSettings, TransportWiFi), kSuccess);
   ASSERT_EQ(alice.account->configureTransportAccountSettings(alice.handle, wwanSettings, TransportWWAN), kSuccess);
   ASSERT_EQ(alice.account->applySettings(alice.handle), kSuccess);

   ASSERT_TRUE(repro_7080->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7090->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7070->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));

   
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   delete repro_7080;
   delete repro_7070;

   alice.account->enable(alice.handle);
   {
      assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V6, CPCAPI2::SipAccount::SipAccountTransport_TCP);
      assertOnClientAuthUntilEx(alice, 7090, 200, false);
   }

   repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   assertOnDnsResetEx(alice, 7080, IpVersion_V4, SipAccountTransport_TCP);
   assertAccountRefreshingEx(alice);
   resip::Helper::setNonceHelper(NULL);
   
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_TCP);
   assertOnClientAuthUntilEx(alice, 7080, 200, false);

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   alice.network->setNetworkTransport(NetworkTransport::TransportWWAN);
   const char* interfaceToSet = "**************";
   std::set<resip::Data> interfaces;
   interfaces.insert(interfaceToSet); // NOTE: this has no bearing on the c= line in the SDP offers
   alice.network->setMockInterfaces(interfaces);
   {
      assertAccountRefreshingEx(alice);
      assertAccountRegistered(alice);
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   repro_7070 = runRepro("repro_auth3.config"); // Port 7070
   ASSERT_TRUE(repro_7070->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   assertOnDnsResetEx(alice, 7070, IpVersion_V4, SipAccountTransport_TCP);
   assertAccountRefreshingEx(alice);
   resip::Helper::setNonceHelper(NULL);
   
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_TCP);
   assertOnClientAuthUntilEx(alice, 7070, 200, false);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro_7070;
   delete repro_7080;
   delete repro_7090;
   runOriginalRepro();
}

TEST_F(DnsFailoverTest, Switchover_IPv4PreferredTCPTransportSetting_IPv4TCPPrimary_IPv6TCPSecondary_WithCall)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_7080 = runRepro("repro_auth1.config"); // Port 7080
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   repro::ReproRunner* repro_7090 = runRepro("repro_auth2.config"); // Port 7090
   ASSERT_TRUE(repro_7090->getProxy() != NULL);
   repro::ReproRunner* repro_7070 = runRepro("repro_auth3.config"); // Port 7070
   ASSERT_TRUE(repro_7070->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   TestAccount alice("alice", Account_NoInit);
   configureAccount(alice);
   alice.config.settings.outboundProxy = "pritcpv4sectcpv6.local"; // primary is TCP Port 7080 and secondary is TCP Port 7090
   alice.config.settings.sipTransportType = SipAccountTransport_TCP;
   alice.config.settings.ipVersion = IpVersion_Auto;
   alice.init();
   alice.account->setTFTimerValueMs(10000);

   TestAccount bob("bob", Account_NoInit);
   configureAccount(bob);
   bob.config.settings.username = "bob123";
   bob.config.settings.password = "bob123";
   bob.config.settings.outboundProxy = "pritcpv4sectcpv6.local"; // primary is TCP Port 7080 and secondary is TCP Port 7090
   bob.config.settings.sipTransportType = SipAccountTransport_TCP;
   bob.config.settings.ipVersion = IpVersion_Auto;
   bob.config.settings.enableDNSResetOnRegistrationRefresh = false;
   bob.config.settings.enableAuthResetUponDNSReset = false;
   bob.init();

   ASSERT_TRUE(repro_7080->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7090->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7070->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));
   ASSERT_TRUE(repro_7090->getProxy()->getUserStore().addUser(bob.config.settings.username.c_str(),
      bob.config.settings.domain.c_str(), "cp.local",
      bob.config.settings.password.c_str(),
      true, "Bob", "<EMAIL>"));

   
   bob.phone->setLoggingEnabled(&AutoTestsLogger::instance(), false);
   bob.phone->setLoggingEnabled(&AutoTestsLogger::instance(), true);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   delete repro_7080; repro_7080 = NULL;
   delete repro_7070; repro_7070 = NULL;

   alice.account->enable(alice.handle);
   {
      assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V6, CPCAPI2::SipAccount::SipAccountTransport_TCP);
      assertOnClientAuthUntilEx(alice, 7090, 200, false);
   }

   bob.account->enable(bob.handle);
   {
      assertAccountStatusUptilRegisteredEx2(bob, CPCAPI2::SipAccount::IpVersion_V6, CPCAPI2::SipAccount::SipAccountTransport_TCP);
      assertOnClientAuthUntilEx(bob, 7090, 200, false);
   }

   // Alice calls Bob then Bob hangs up, the DNS reset is delayed until after the call
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);
   auto aliceConversationEvents = std::async(std::launch::async, [&] ()
   {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      repro_7080 = runRepro("repro_auth1.config"); // Port 7080
      ASSERT_TRUE(repro_7080->getProxy() != NULL);

      // Call will stay alive to ensure that the DNS reset timer for Port 7080 should have been triggered, but blocked due to existing call
      assertConversationEnded_time(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely, 60000);

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      alice.config.settings.outboundProxy = "pritcpv4sectcpv6_v2.local"; // primary is TCP Port 7070 and secondary is TCP Port 7090
      alice.account->configureDefaultAccountSettings(alice.handle, alice.config.settings);
      alice.account->applySettings(alice.handle);
      {
         assertAccountDeregisteringEx(alice);
         assertAccountDeregisteredEx(alice);
         assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V6, CPCAPI2::SipAccount::SipAccountTransport_TCP);
         assertOnClientAuthUntilEx(alice, 7090, 200, false);
      }
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      repro_7070 = runRepro("repro_auth3.config"); // Port 7070
      ASSERT_TRUE(repro_7070->getProxy() != NULL);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      assertOnDnsResetEx(alice, 7070, IpVersion_V4, SipAccountTransport_TCP);
      assertAccountRefreshingEx(alice);
      resip::Helper::setNonceHelper(NULL);
      alice.phone->setLoggingEnabled(&AutoTestsLogger::instance(), false);
      alice.phone->setLoggingEnabled(&AutoTestsLogger::instance(), true);
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      assertAccountStatusUptilRegisteredEx2(alice, CPCAPI2::SipAccount::IpVersion_V4, CPCAPI2::SipAccount::SipAccountTransport_TCP);
      assertOnClientAuthUntilEx(alice, 7070, 200, false);
   });

   auto bobConversationEvents = std::async(std::launch::async, [&] ()
   {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      bob.conversation->sendRingingResponse(bobCall);
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      bob.conversation->accept(bobCall);
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      // A DNS reset normally would occur with a value less that the registration interval, i.e. 30 seconds,
      // so persist the call over that time to ensure that the DNS reset is skipped due to the call.
      std::this_thread::sleep_for(std::chrono::milliseconds(50000));

      bob.conversation->end(bobCall);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });

   waitFor2Ms(aliceConversationEvents, bobConversationEvents, std::chrono::milliseconds(120000));

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   alice.disable();
   bob.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   delete repro_7070;
   delete repro_7080;
   delete repro_7090;
   runOriginalRepro();
}

// 7080 is higher priority SIP server, 7090 lower priority SIP server.
// 7080 initially down when alice first tries to register, so alice registers with 7090 successfully.
// we then bring 7080 up, and after, initiate a network change for alice
// after network change, SDK resets whitelist, so alice should connect to 7080 again, since it is higher priority
// per DNS.
TEST_F(DnsFailoverTest, PrimaryNotWorkingSecondaryWorkingNetChange)
{
   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();
   repro::ReproRunner* repro_7080 = runRepro("repro_auth1.config");
   ASSERT_TRUE(repro_7080->getProxy() != NULL);
   repro::ReproRunner* repro_7090 = runRepro("repro_auth2.config");
   ASSERT_TRUE(repro_7090->getProxy() != NULL);
   std::this_thread::sleep_for(std::chrono::milliseconds(3000));

   {
      TestAccount alice("alice", Account_NoInit);
      alice.config.settings.username = "alice123";
      alice.config.settings.password = "alice123";
      alice.config.settings.domain = "cp.local";
      alice.config.settings.outboundProxy = "prisecwithauth.local";
      alice.config.settings.sipTransportType = SipAccountTransport_TCP;
      alice.config.settings.autoRetryOnTransportDisconnect = true;
      alice.config.settings.useRport = false;
      alice.config.settings.useOutbound = false;
      alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
      alice.config.settings.minimumRegistrationIntervalSeconds = 5;
      alice.config.settings.maximumRegistrationIntervalSeconds = 5;

      alice.init();
      alice.account->setTFTimerValueMs(10000);
      
      ASSERT_TRUE(repro_7080->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
         alice.config.settings.domain.c_str(), "cp.local",
         alice.config.settings.password.c_str(),
         true, "Alice", "<EMAIL>"));

      ASSERT_TRUE(repro_7090->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
         alice.config.settings.domain.c_str(), "cp.local",
         alice.config.settings.password.c_str(),
         true, "Alice", "<EMAIL>"));

      std::this_thread::sleep_for(std::chrono::milliseconds(3000));

      delete repro_7080;
      
      alice.account->enable(alice.handle);
      {
         for (;;)
         {
            CPCAPI2::SipAccount::SipAccountHandle h;
            CPCAPI2::SipAccount::SipAccountClientAuthEvent evt;
            ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandlerInternal::onClientAuth", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
            ASSERT_EQ(alice.handle, h);

            if (evt.responseStatusCode == 408) continue;

            ASSERT_EQ(407, evt.responseStatusCode);
            ASSERT_TRUE(evt.willSendUpdatedRequest);
            ASSERT_EQ(evt.responseSourcePort, 7090);
            break;
         }
         for (;;)
         {
            CPCAPI2::SipAccount::SipAccountHandle h;
            CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
            ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged",
               __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
            ASSERT_EQ(alice.handle, h);
            if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister ||
               evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering)
               continue;

            ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
            break;
         }
         {
            CPCAPI2::SipAccount::SipAccountHandle h;
            CPCAPI2::SipAccount::SipAccountClientAuthEvent evt;
            ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandlerInternal::onClientAuth", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
            ASSERT_EQ(alice.handle, h);
            ASSERT_EQ(200, evt.responseStatusCode);
            ASSERT_FALSE(evt.willSendUpdatedRequest);
            ASSERT_EQ(evt.responseSourcePort, 7090);
         }
      }
      
      repro_7080 = runRepro("repro_auth1.config"); // Port 7080
      ASSERT_TRUE(repro_7080->getProxy() != NULL);
      
      std::this_thread::sleep_for(std::chrono::seconds(10));

      std::set<resip::Data> ifaces;
      ifaces.insert("*********");
      alice.network->setMockInterfaces(ifaces);
      
      // network change triggers us to clear our (resip) whitelist; expect SDK connects to 7080 now
      {
         for (;;)
         {
            CPCAPI2::SipAccount::SipAccountHandle h;
            CPCAPI2::SipAccount::SipAccountClientAuthEvent evt;
            ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandlerInternal::onClientAuth", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
            ASSERT_EQ(alice.handle, h);

            if (evt.responseStatusCode == 408) continue;

            ASSERT_EQ(407, evt.responseStatusCode);
            ASSERT_TRUE(evt.willSendUpdatedRequest);
            ASSERT_EQ(evt.responseSourcePort, 7080);
            break;
         }
         for (;;)
         {
            CPCAPI2::SipAccount::SipAccountHandle h;
            CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
            ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged",
               __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
            ASSERT_EQ(alice.handle, h);
            if (evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister ||
               evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registering ||
               evt.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Refreshing)
               continue;

            ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
            break;
         }
         {
            CPCAPI2::SipAccount::SipAccountHandle h;
            CPCAPI2::SipAccount::SipAccountClientAuthEvent evt;
            ASSERT_TRUE(alice.accountEvents->expectEvent(__LINE__, "SipAccountHandlerInternal::onClientAuth", __FILE__, 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
            ASSERT_EQ(alice.handle, h);
            ASSERT_EQ(200, evt.responseStatusCode);
            ASSERT_FALSE(evt.willSendUpdatedRequest);
            ASSERT_EQ(evt.responseSourcePort, 7080);
         }
      }
   }
   
   delete repro_7080;
   delete repro_7090;
   runOriginalRepro();
}

#ifndef ANDROID // Android does not work well with repro on port 5060 (system process using this port)
TEST_F(DnsFailoverTest, DnsHighRcode)
{
   class AresDnsInboundPacketInspector
   {
   public:
      AresDnsInboundPacketInspector()
      {
         set_incoming_udp_intercept_fn(&AresDnsInboundPacketInspector::intercept, NULL);
      }
   
      ~AresDnsInboundPacketInspector()
      {
         set_incoming_udp_intercept_fn(NULL, NULL);
      }
      
      static int intercept(unsigned char *abuf, int abuf_capacity, int abuf_length, void* /*context*/)
      {
         if (abuf_length > 44 && abuf[44] == 0x21) // SRV
         {
            // similar to OBELISK-5937; set a high rcode that ares does not currently handle.
            // ares currently treats this as success, even though it is a failure response per
            // https://www.iana.org/assignments/dns-parameters/dns-parameters.xhtml
            abuf[3] = 6;
            
            // ares helper function doesn't work here since it seems to assume
            // zero'd out starting memory
            // DNS_HEADER_SET_RCODE(abuf, 6);
         }

         return abuf_length;
      }
   };

   ReproHolder::destroyInstance();
   // we need repro to listen on port 5060, since without an SRV response we can't override the SDK's
   // default UDP SIP port of 5060.
   repro::ReproRunner* repro_5060 = runRepro("repro_5060.config");
   
   ASSERT_TRUE(checkDnsServer());
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.outboundProxy = "singlearecord.local";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   
   // registration should succeed; the SDK should have performed a successful A record query after the
   // SRV query failed.
   alice.enable();
   alice.disable();
   
   delete repro_5060;
   runOriginalRepro();
}
#endif // #ifndef ANDROID


// Hooks into all DNS responses ares receives and modifies any 2 A records response
// to have 1 unresponsive A record
class AresDnsInboundPacketMod_UnresposiveARecord
{
public:
   AresDnsInboundPacketMod_UnresposiveARecord() :
      mInterceptCount(0)
   {
      set_incoming_udp_intercept_fn(&AresDnsInboundPacketMod_UnresposiveARecord::intercept, this);
   }

   ~AresDnsInboundPacketMod_UnresposiveARecord()
   {
      set_incoming_udp_intercept_fn(NULL, NULL);
   }

   public:
      int interceptCount() const { return mInterceptCount; }

   private:
      static int intercept(unsigned char *abuf, int abuf_capacity, int abuf_length, void* context)
      {
         AresDnsInboundPacketMod_UnresposiveARecord* self = reinterpret_cast<AresDnsInboundPacketMod_UnresposiveARecord*>(context);

         if (abuf_length > 0x7 && abuf[0x7] == 2 /* 2 answers */ && abuf_length > 0x34 && abuf[0x34] == 1 /* A record */)
         {
            // number of answer records; modify from 2 to 1
            abuf[0x7] = 1;
            // A record response 1; modify from 127.0.0.1 to ********* which is non-reachable
            abuf[0x3d] = 192;
            abuf[0x3e] = 0;
            abuf[0x3f] = 2;
            abuf[0x40] = 0;

            ++(self->mInterceptCount);

            return abuf_length - 16;
         }
         return abuf_length;
      }

      std::atomic_int mInterceptCount;
};


TEST_F(DnsFailoverTest, ValidateAresPacketHook)
{
   AresDnsInboundPacketMod_UnresposiveARecord aresPacketHook;

   TestAccount bob("bob", Account_NoInit);
   bob.config.settings.domain = "cp.local";
   bob.config.settings.outboundProxy = "multiple.multiamultirepro.local:6060";
   bob.config.settings.sipTransportType = SipAccountTransport_UDP;
   bob.config.settings.useRport = false;
   bob.config.settings.useOutbound = false;
   bob.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   bob.config.settings.overrideMsecsTimerF = 5000; // shorten test duration
   bob.init();
   bob.enable(false);
   assertAccountRegistering(bob);
   assertAccountWaitingToRegister(bob);
   ASSERT_GT(aresPacketHook.interceptCount(), 0);

   bob.disable();
}

// OBELISK-6318: DNS records updated after SIP registration such that
// the IP address of the SIP server registered against (and whitelisted)
// no longer exists when call is initiated.
//
// The bug: INVITE is sent to IP address that does not match what was
// SIP registered against.
TEST_F(DnsFailoverTest, DnsARecordUpdatePostRegistration)
{
   // corresponds to DNS TTL set in prisecwithauth.local.txt for multiple.multiamultirepro.local
   const int dnsTTLSec = 10;

   TestAccount bob("bob", Account_NoInit);
   bob.config.settings.domain = "cp.local";
   bob.config.settings.outboundProxy = "multiple.multiamultirepro.local:6060";
   bob.config.settings.sipTransportType = SipAccountTransport_UDP;
   bob.config.settings.useRport = false;
   bob.config.settings.useOutbound = false;
   bob.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   bob.config.settings.registrationIntervalSeconds = 3600;
   bob.init();
   bob.enable();

   TestAccount charlie("charlie");

   // wait for DNS TTL to expire
   std::this_thread::sleep_for(std::chrono::seconds(dnsTTLSec + 2));

   AresDnsInboundPacketMod_UnresposiveARecord aresPacketHook;

   // Bob calls Charlie then Charlie hangs up
   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall, charlie.config.uri());
   bob.conversation->start(bobCall);
   auto bobConversationEvents = std::async(std::launch::async, [&] () {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing(bob, bobCall, charlie.config.uri());
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
   });

   auto charlieConversationEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle charlieCall;

      // with fix for OBELISK-6318 charlie should receive the call; pre-fix
      // bob would have paid attention to updated DNS after TLL and sent the 
      // INVITE to ********* (non-reachable destination).
      assertNewConversationIncoming(charlie, &charlieCall, bob.config.uri());
      charlie.conversation->accept(charlieCall);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_Connected);
   });
   waitFor2(bobConversationEvents, charlieConversationEvents);
   ASSERT_EQ(aresPacketHook.interceptCount(), 0);

}

// ensures the SIP DNS cache is unfrozen after the call ends; if it has actually been unfrozen
// then a new DNS response should be retreived which will result in call failure
TEST_F(DnsFailoverTest, DnsARecordUpdatePostCallNoRegistrar)
{
   // corresponds to DNS TTL set in prisecwithauth.local.txt for multiple.multiamultirepro.local
   const int dnsTTLSec = 10;
   
   TestAccount bob("bob", Account_NoInit);
   bob.config.settings.domain = "bogus.invalid";
   bob.config.settings.outboundProxy = "";
   bob.config.settings.sipTransportType = SipAccountTransport_UDP;
   bob.config.settings.useRport = false;
   bob.config.settings.useOutbound = false;
   bob.config.settings.useRegistrar = false;
   bob.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   bob.config.settings.registrationIntervalSeconds = 3600;
   bob.init();
   bob.enable();

   TestAccount charlie("charlie", Account_NoInit);

   resip::Uri charlieTarget;
   charlieTarget.user() = charlie.config.settings.username;
   charlieTarget.host() = "multiple.multiamultirepro.local";
   charlieTarget.port() = 5055;

   charlie.config.settings.minSipPort = charlieTarget.port();
   charlie.config.settings.maxSipPort = charlieTarget.port();
   charlie.init();
   charlie.enable();

   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall, charlieTarget.getAOR(true).c_str());
   bob.conversation->start(bobCall);

   std::unique_ptr<AresDnsInboundPacketMod_UnresposiveARecord> aresPacketHook;

   auto bobConversationEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(bob, bobCall, charlieTarget.getAOR(false).c_str());
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      bob.conversation->end(bobCall);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);

      aresPacketHook = std::make_unique<AresDnsInboundPacketMod_UnresposiveARecord>();
      // wait for DNS TTL to expire
      std::this_thread::sleep_for(std::chrono::seconds(dnsTTLSec + 2));

      // the outbound call should now fail since the SDK performed new DNS query (and received DNS result with broken A record).
      // this is intended to verify the SDK does not freeze its SIP DNS cache when useRegistrar = false
      SipConversationHandle bobCall2 = bob.conversation->createConversation(bob.handle);
      bob.conversation->addParticipant(bobCall2, charlieTarget.getAOR(true).c_str());
      bob.conversation->start(bobCall2);
      assertNewConversationOutgoing(bob, bobCall2, charlieTarget.getAOR(false).c_str());

      // need to wait for timer B to timeout (INVITE transaction timeout)
      std::this_thread::sleep_for(std::chrono::seconds(32));
      assertConversationEnded(bob, bobCall2, ConversationEndReason_ServerRejected);

   });

   auto charlieConversationEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle charlieCall;
      assertNewConversationIncoming(charlie, &charlieCall, bob.config.uri());
      charlie.conversation->accept(charlieCall);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_Connected);
      assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedRemotely);

      NewConversationEvent evt;
      ASSERT_FALSE(charlie.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onNewConversation", 10000, AlwaysTruePred(), charlieCall, evt));
   });

   waitFor2(bobConversationEvents, charlieConversationEvents);
   ASSERT_GE(aresPacketHook->interceptCount(), 1);
}

// DNS records update mid-call; registration refresh
// should result in re-REGISTER going to original IP address,
// re-INVITE going to original IP address.
TEST_F(DnsFailoverTest, DnsARecordUpdateDuringCall)
{
   // corresponds to DNS TTL set in prisecwithauth.local.txt for multiple.multiamultirepro.local
   const int dnsTTLSec = 10;

   TestAccount bob("bob", Account_NoInit);
   bob.config.settings.domain = "cp.local";
   bob.config.settings.outboundProxy = "multiple.multiamultirepro.local:6060";
   bob.config.settings.sipTransportType = SipAccountTransport_UDP;
   bob.config.settings.useRport = false;
   bob.config.settings.useOutbound = false;
   bob.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   bob.config.settings.registrationIntervalSeconds = 3600;
   bob.init();
   bob.enable();

   TestAccount charlie("charlie");

   std::unique_ptr<AresDnsInboundPacketMod_UnresposiveARecord> aresPacketHook;

   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall, charlie.config.uri());
   bob.conversation->start(bobCall);
   auto bobConversationEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(bob, bobCall, charlie.config.uri());
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected); 

      // once the call has connected, future A record queries will have answers with un-reachable host
      aresPacketHook = std::make_unique<AresDnsInboundPacketMod_UnresposiveARecord>();

      // wait for DNS TTL to expire
      std::this_thread::sleep_for(std::chrono::seconds(dnsTTLSec + 2));

      bob.account->requestRegistrationRefresh(bob.handle, 0);

      assertAccountRefreshing(bob);
      assertAccountRegistered(bob);

      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);

      bob.conversation->end(bobCall);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto charlieConversationEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle charlieCall;
      assertNewConversationIncoming(charlie, &charlieCall, bob.config.uri());
      charlie.conversation->accept(charlieCall);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_Connected);

      // bob's registration refresh change re-INVITE
      SipConversationHandle h;
      ConversationMediaChangeRequestEvent evt;
      ASSERT_TRUE(charlie.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onConversationMediaChangeRequest",
      45000, HandleEqualsPred<SipConversationHandle>(charlieCall), h, evt));

      charlie.conversation->accept(charlieCall);
      assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);

      assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor2(bobConversationEvents, charlieConversationEvents);
   ASSERT_EQ(aresPacketHook->interceptCount(), 0);
}

// DNS records update mid-call; network change
// should result in post network change REGISTER going to original IP address,
// re-INVITE going to original IP address.
TEST_F(DnsFailoverTest, DnsARecordUpdateDuringCall_NetworkChange)
{
   // corresponds to DNS TTL set in prisecwithauth.local.txt for multiple.multiamultirepro.local
   const int dnsTTLSec = 10;
   
   TestAccount bob("bob", Account_NoInit);
   bob.config.settings.domain = "cp.local";
   bob.config.settings.outboundProxy = "multiple.multiamultirepro.local:6060";
   bob.config.settings.sipTransportType = SipAccountTransport_UDP;
   bob.config.settings.useRport = false;
   bob.config.settings.useOutbound = false;
   bob.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   bob.config.settings.registrationIntervalSeconds = 3600;
   bob.config.settings.overrideMsecsTimerF = 10000; // reduce time for transitioning to WaitingToRegister
   bob.init();
   bob.enable();

   TestAccount charlie("charlie");

   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall, charlie.config.uri());
   bob.conversation->start(bobCall);

   std::unique_ptr<AresDnsInboundPacketMod_UnresposiveARecord> aresPacketHook;

   auto bobConversationEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(bob, bobCall, charlie.config.uri());
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      aresPacketHook = std::make_unique<AresDnsInboundPacketMod_UnresposiveARecord>();
      // wait for DNS TTL to expire
      std::this_thread::sleep_for(std::chrono::seconds(dnsTTLSec + 2));

      bob.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> ifaces;
      ifaces.insert("**************");
      bob.network->setMockInterfaces(ifaces);

      assertAccountRefreshing(bob);
      assertAccountRegistered(bob);

      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);

      ASSERT_EQ(aresPacketHook->interceptCount(), 0);
      bob.conversation->end(bobCall);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      
      assertAccountRefreshing(bob);
      assertAccountWaitingToRegister(bob); // assert we requested and received updated DNS records
      ASSERT_GE(aresPacketHook->interceptCount(), 1);
   });

   auto charlieConversationEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle charlieCall;
      assertNewConversationIncoming(charlie, &charlieCall, bob.config.uri());

      charlie.conversation->accept(charlieCall);

      // bob's network change re-INVITE
      SipConversationHandle h;
      ConversationMediaChangeRequestEvent evt;
      ASSERT_TRUE(charlie.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onConversationMediaChangeRequest",
      45000, HandleEqualsPred<SipConversationHandle>(charlieCall), h, evt));

      assertSuccess(charlie.conversation->accept(charlieCall));
      assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);

      assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor2(bobConversationEvents, charlieConversationEvents);

   bob.disable(true);
}


// Hooks into all DNS responses ares receives and modifies any SRV records response with 2 results
// to have 1 unresponsive SRV record
class AresDnsInboundPacketMod_PrioritizeUnresposiveSrvRecord
{
public:
   AresDnsInboundPacketMod_PrioritizeUnresposiveSrvRecord() :
      mInterceptCount(0)
   {
      set_incoming_udp_intercept_fn(&AresDnsInboundPacketMod_PrioritizeUnresposiveSrvRecord::intercept, this);
   }

   ~AresDnsInboundPacketMod_PrioritizeUnresposiveSrvRecord()
   {
      set_incoming_udp_intercept_fn(NULL, NULL);
   }

   public:
      int interceptCount() const { return mInterceptCount; }

   private:
      static int intercept(unsigned char *abuf, int abuf_capacity, int abuf_length, void* context)
      {
         AresDnsInboundPacketMod_PrioritizeUnresposiveSrvRecord* self = reinterpret_cast<AresDnsInboundPacketMod_PrioritizeUnresposiveSrvRecord*>(context);

         if (abuf_length > 0x35 && abuf[0x35] == 0x21) // SRV record query
         {
            EXPECT_GT(abuf_length, 0x07);
            EXPECT_EQ(abuf[0x07], 3); // 3 answer records
            
            EXPECT_EQ(ntohs((uint16_t)*((uint16_t*)&abuf[0xB4])), 5555); // 3rd answer has port of 5555
            abuf[0xB1] = 0; // change priority of answer with port 5555 to 0; most preferred

            // the highest priority answer is now an unresponsive port

            ++self->mInterceptCount;
         }
         return abuf_length;
      }

      std::atomic_int mInterceptCount;
};

// DNS records update mid-call; network change
// should result in post network change REGISTER going to original IP address,
// re-INVITE going to original IP address.
TEST_F(DnsFailoverTest, DnsSrvRecordUpdateDuringCall_NetworkChange)
{
   std::unique_ptr<repro::ReproRunner> repro5070(runRepro("repro_5070.config"));

   // corresponds to DNS TTL set in prisecwithauth.local.txt for multiple.multiamultirepro.local
   const int dnsTTLSec = 10;
   
   TestAccount bob("bob", Account_NoInit);
   bob.config.settings.domain = "cp.local";
   bob.config.settings.outboundProxy = "equalweights_samerepro.local";
   bob.config.settings.sipTransportType = SipAccountTransport_UDP;
   bob.config.settings.useRport = false;
   bob.config.settings.useOutbound = false;
   bob.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   bob.config.settings.registrationIntervalSeconds = 3600;
   bob.config.settings.overrideMsecsTimerF = 10000; // reduce time for transitioning to WaitingToRegister
   bob.init();
   bob.enable();

   TestAccount charlie("charlie", Account_NoInit);
   charlie.config.settings.domain = "cp.local";
   charlie.config.settings.outboundProxy = "host1.equalweights_samerepro.local:5070"; // don't use SRV lookups for charlie
   charlie.config.settings.sipTransportType = SipAccountTransport_UDP;
   charlie.config.settings.useRport = false;
   charlie.config.settings.useOutbound = false;
   charlie.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());
   charlie.init();
   charlie.enable();

   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall, charlie.config.uri());
   bob.conversation->start(bobCall);

   std::unique_ptr<AresDnsInboundPacketMod_PrioritizeUnresposiveSrvRecord> aresPacketHook;

   auto bobConversationEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(bob, bobCall, charlie.config.uri());
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      aresPacketHook = std::make_unique<AresDnsInboundPacketMod_PrioritizeUnresposiveSrvRecord>();

      // wait for DNS TTL to expire; any further DNS SRV responses should result in preferred record being unreachable
      std::this_thread::sleep_for(std::chrono::seconds(dnsTTLSec + 2));

      bob.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> ifaces;
      ifaces.insert("**************");
      bob.network->setMockInterfaces(ifaces);

      assertAccountRefreshing(bob);
      assertAccountRegistered(bob);

      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);

      ASSERT_EQ(aresPacketHook->interceptCount(), 0);

      std::this_thread::sleep_for(std::chrono::seconds(2)); // let bob & charlie logging settle a bit before ending
      bob.conversation->end(bobCall);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      
      assertAccountRefreshing(bob);
      assertAccountWaitingToRegister(bob); // assert we requested and received updated DNS records

      bob.disable(false);

      ASSERT_GE(aresPacketHook->interceptCount(), 1);
   });

   auto charlieConversationEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle charlieCall;
      assertNewConversationIncoming(charlie, &charlieCall, bob.config.uri());

      charlie.conversation->accept(charlieCall);

      // bob's network change re-INVITE
      SipConversationHandle h;
      ConversationMediaChangeRequestEvent evt;
      ASSERT_TRUE(charlie.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onConversationMediaChangeRequest",
      45000, HandleEqualsPred<SipConversationHandle>(charlieCall), h, evt));

      assertSuccess(charlie.conversation->accept(charlieCall));
      assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);

      assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedRemotely);
   });

   waitFor2(bobConversationEvents, charlieConversationEvents);

}


void expectWaitingToRegisterWithCode(TestAccount& account, int expectedResponseCode, int waitSec = 20)
{
   CPCAPI2::SipAccount::SipAccountHandle h;
   CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
   ASSERT_TRUE(account.accountEvents->expectEvent(__LINE__, "SipAccountHandler::onAccountStatusChanged",
      __FILE__, waitSec * 1000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(account.handle, h);
   ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister, evt.accountStatus);
   ASSERT_EQ(expectedResponseCode, evt.signalingStatusCode);
}

void DnsFailoverTest_ARecordFailover(const std::string& sippScenarioFile, int expectedFirstRegisterResposneCode, bool expectFailover)
{
#ifdef __APPLE__
   if (!TestEnvironmentConfig::macLoopbackAliasesSet())
   {
      // need to run: sudo ifconfig lo0 alias ********* up
      // and set CPCAPI2_MAC_LOOPBACKALIASES_SET to confirm you've done this 
      
      GTEST_SKIP() << "This test requires CPCAPI2_MAC_LOOPBACKALIASES_SET env variable to set on mac ack'ing you have loopback 127.0.0.1 up";
   }
#endif // #ifdef __APPLE__

   ASSERT_TRUE(checkDnsServer());
   ReproHolder::destroyInstance();

   std::unique_ptr<repro::ReproRunner> repro1(runRepro("repro_auth_127001.config"));
   ASSERT_TRUE(repro1->getProxy() != NULL);

   const int sippListenPort = 6060;

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.username = "alice111";
   alice.config.settings.password = "alce1234";
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.useRegistrar = true;
   alice.config.settings.outboundProxy = "multiple.multiamultirepro2.local:6060";
   alice.config.settings.nameServers.push_back(TestAccountConfig::defaultDnsServer());

   bool wouldRetry = false;
   for (cpc::vector<SipResponseType>::const_iterator it = alice.config.settings.reRegisterOnResponseTypes.begin(); 
        it != alice.config.settings.reRegisterOnResponseTypes.end(); ++it)
   {
      if (it->method == "REGISTER" && it->responseCode == expectedFirstRegisterResposneCode)
      {
         wouldRetry = true;
         break;
      }
   }

   alice.init();

   ASSERT_TRUE(repro1->getProxy()->getUserStore().addUser(alice.config.settings.username.c_str(),
      alice.config.settings.domain.c_str(), "cp.local",
      alice.config.settings.password.c_str(),
      true, "Alice", "<EMAIL>"));


   SippRunnerSettings sippRunnerSettings;
   sippRunnerSettings.sipListenPort = sippListenPort;
   sippRunnerSettings.sipListenIp = "*********";
   sippRunnerSettings.mediaPort = 50020;
   sippRunnerSettings.timeoutSec = 120;
   sippRunnerSettings.sipTargetPort = alice.config.settings.minSipPort;
   sippRunnerSettings.scenarioFileName = sippScenarioFile;

   SippRunner sippRunner(sippRunnerSettings);
   sippRunner.start();

   alice.enable(false);
   assertAccountRegistering(alice);

   if (expectFailover || wouldRetry)
   {
      {
         SCOPED_TRACE("First waitingToRegister");
         expectWaitingToRegisterWithCode(alice, expectedFirstRegisterResposneCode);
      }

      if (expectFailover)
      {
         assertAccountRegistering(alice);
         assertAccountRegistered(alice);
      }
      else
      {
         // sipp doesn't seem to support multiple inbound REGISTERs with different call-ids
         sippRunner.stop();
         sippRunner.start();
         ASSERT_TRUE(wouldRetry);
         assertAccountRegistering(alice);

         {
            SCOPED_TRACE("Second waitingToRegister");
            expectWaitingToRegisterWithCode(alice, expectedFirstRegisterResposneCode);
            
         }
      }
   }
   else
   {
      assertAccountDeregistered(alice);
   }

   //runOriginalRepro();
}

#ifdef __APPLE__ // SippRunner only supported on mac for now
TEST_F(DnsFailoverTest, ARecordFailover500)
{
   const bool expectFailover = true;
   DnsFailoverTest_ARecordFailover("Register500Response.xml", 500, expectFailover);
}

TEST_F(DnsFailoverTest, ARecordFailover502)
{
   const bool expectFailover = false;
   DnsFailoverTest_ARecordFailover("Register502Response.xml", 502, expectFailover);
}

TEST_F(DnsFailoverTest, ARecordFailover503)
{
   const bool expectFailover = true;
   DnsFailoverTest_ARecordFailover("Register503Response.xml", 503, expectFailover);
}

TEST_F(DnsFailoverTest, ARecordFailover504)
{
   const bool expectFailover = false;
   DnsFailoverTest_ARecordFailover("Register504Response.xml", 504, expectFailover);
}

TEST_F(DnsFailoverTest, ARecordFailover407)
{
   // we would only fail over if the 407 response contains no auth header
   const bool expectFailover = true;
   DnsFailoverTest_ARecordFailover("Register407Response.xml", 407, expectFailover);
}

TEST_F(DnsFailoverTest, ARecordFailover408)
{
   const bool expectFailover = false;
   DnsFailoverTest_ARecordFailover("Register408Response.xml", 408, expectFailover);
}

TEST_F(DnsFailoverTest, ARecordFailover480)
{
   const bool expectFailover = false;
   DnsFailoverTest_ARecordFailover("Register480Response.xml", 480, expectFailover);
}

TEST_F(DnsFailoverTest, ARecordFailover486)
{
   const bool expectFailover = false;
   DnsFailoverTest_ARecordFailover("Register486Response.xml", 486, expectFailover);
}
#endif // #ifdef __APPLE__


}  // namespace
