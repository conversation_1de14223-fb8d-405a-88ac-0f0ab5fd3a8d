#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#if (CPCAPI2_BRAND_WEB_CALL_MODULE == 1)

#include "cpcapi2_test_fixture.h"

#include <gtest/gtest.h>
#include <cpcapi2.h>

#include "test_framework/cpcapi2_test_framework.h"
#include "test_framework/bare_bones_sip_endpoint.h"

#include <thread>
#include <future>

using namespace CPCAPI2;
using namespace CPCAPI2::Media;
using namespace CPCAPI2::WebCall;
using namespace CPCAPI2::test;

namespace {

class WebCallTest : public CpcapiAutoTest
{
public:
   WebCallTest() {}
   virtual ~WebCallTest() {}
};

TEST_F(WebCallTest, DISABLED_IncomingAudioCall) {

   TestAccount alice("alice", Account_Init);

#if _WIN32
   HWND hwndAliceCapture = NULL;
   ASSERT_EQ(0, ViECreateWindow(hwndAliceCapture, 0, 0, 352, 288, "Alice (capture)"));
   alice.video->startCapture();
   alice.video->setLocalVideoRenderTarget(hwndAliceCapture);

   HWND hwndAliceRemote = NULL;
   ASSERT_EQ(0, ViECreateWindow(hwndAliceRemote, 356, 0, 352, 288, "Alice (incoming)"));
   alice.video->setIncomingVideoRenderTarget(hwndAliceRemote);
#endif // _WIN32

   alice.webCall->setHandler((WebCallHandler*)0xDEADBEEF);

   WebNavigationHandle navh = alice.webCall->navigate("http://localhost:8000/cprtc.xml");
   WebCallHandle aliceCall;

   auto aliceEvents = std::async(std::launch::async, [&] () {

   {
      WebCallHandle h;
	   NewConversationEvent evt;
	   ASSERT_TRUE(alice.webCallEvents->expectEvent("WebCallHandler::onNewConversation", 
		   25000, AlwaysTruePred(), h, evt));
      aliceCall = h;
	   ASSERT_EQ(ConversationType_Incoming, evt.conversationType);
      alice.webCall->accept(aliceCall);
   }

   {
      WebCallHandle h;
	   ConversationMediaChangedEvent evt;
	   ASSERT_TRUE(alice.webCallEvents->expectEvent("WebCallHandler::onConversationMediaChanged", 
		   15000, HandleEqualsPred<WebCallHandle>(aliceCall), h, evt));
   }

   {
      WebCallHandle h;
	   ConversationStateChangedEvent evt;
	   ASSERT_TRUE(alice.webCallEvents->expectEvent("WebCallHandler::onConversationStateChanged", 
		   15000, HandleEqualsPred<WebCallHandle>(aliceCall), h, evt));
	   ASSERT_EQ(ConversationState_Connected, evt.conversationState);
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(15000));

   alice.webCall->end(aliceCall);

   {
      WebCallHandle h;
	   ConversationEndedEvent evt;
	   ASSERT_TRUE(alice.webCallEvents->expectEvent("WebCallHandler::onConversationEnded", 
		   15000, HandleEqualsPred<WebCallHandle>(aliceCall), h, evt));
	   ASSERT_EQ(ConversationState_Ended, evt.conversationState);
   }
   });

#if _WIN32
   alice.video->stopCapture();
   alice.video->setLocalVideoRenderTarget(NULL);
   DestroyWindow(hwndAliceCapture);
   DestroyWindow(hwndAliceRemote);
#endif // _WIN32

}

TEST_F(WebCallTest, DISABLED_BasicAudioCall) {

   TestAccount alice("alice", Account_Init);
   alice.webCall->setHandler((WebCallHandler*)0xDEADBEEF);

   WebNavigationHandle navh = alice.webCall->navigate("http://localhost:8000/cprtc.xml");
   WebCallHandle aliceCall = alice.webCall->createConversation(navh);
   alice.webCall->addParticipant(aliceCall, "1234");
   alice.webCall->start(aliceCall);

   {
      WebCallHandle h;
	   NewConversationEvent evt;
	   ASSERT_TRUE(alice.webCallEvents->expectEvent("WebCallHandler::onNewConversation", 
		   15000, HandleEqualsPred<WebCallHandle>(aliceCall), h, evt));
	   ASSERT_EQ(ConversationType_Outgoing, evt.conversationType);
	   ASSERT_EQ("1234", evt.remoteAddress);
   }

   {
      WebCallHandle h;
	   ConversationMediaChangedEvent evt;
	   ASSERT_TRUE(alice.webCallEvents->expectEvent("WebCallHandler::onConversationMediaChanged", 
		   15000, HandleEqualsPred<WebCallHandle>(aliceCall), h, evt));
   }

   {
      WebCallHandle h;
	   ConversationStateChangedEvent evt;
	   ASSERT_TRUE(alice.webCallEvents->expectEvent("WebCallHandler::onConversationStateChanged", 
		   15000, HandleEqualsPred<WebCallHandle>(aliceCall), h, evt));
	   ASSERT_EQ(ConversationState_Connected, evt.conversationState);
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   alice.webCall->end(aliceCall);

   {
      WebCallHandle h;
	   ConversationEndedEvent evt;
	   ASSERT_TRUE(alice.webCallEvents->expectEvent("WebCallHandler::onConversationEnded", 
		   15000, HandleEqualsPred<WebCallHandle>(aliceCall), h, evt));
	   ASSERT_EQ(ConversationState_Ended, evt.conversationState);
   }

   
   //TestAccount bob("bob");

   //PeerConnectionSettings aliceSettings;
   //aliceSettings.certAor = alice.config.settings.username + "@" + alice.config.settings.domain;
   //aliceSettings.natTraversalMode = PeerConnectionSettings::NatTraversalMode_Auto;
   //aliceSettings.natTraversalServerHostname = "stun.counterpath.com";
   //aliceSettings.natTraversalServerPort = 3478;
   //aliceSettings.secureMediaMode = PeerConnectionSettings::SecureMediaMode_DTLS;
   //aliceSettings.secureMediaRequired = true;
   //aliceSettings.sessionName = "peerconnectiontest";
   //PeerConnectionSettings bobSettings;
   //bobSettings = aliceSettings;
   //bobSettings.certAor = bob.config.settings.username + "@" + bob.config.settings.domain;

   //PeerConnectionHandle alicePeerConn = alice.peerConnection->createPeerConnection();
   //alice.peerConnection->setHandler(alicePeerConn, (PeerConnectionHandler*)0xDEADBEEF);
   //alice.peerConnection->setDefaultSettings(alicePeerConn, aliceSettings);
   //MediaStreamConfig aliceAudioConfig = { MediaStreamConfig::MediaType_Audio, MediaStreamConfig::MediaDirection_SendRecv };
   //ASSERT_EQ(alice.peerConnection->configureStream(alicePeerConn, "audio", aliceAudioConfig), kSuccess);
   //ASSERT_EQ(alice.peerConnection->createOffer(alicePeerConn), kSuccess);

   //SessionDescription aliceSdp;
   //{
   //   PeerConnectionHandle h;
   //   LocalSessionDescriptionCreatedEvent evt;
   //   ASSERT_TRUE(cpcExpectEvent(alice.peerConnEvents,
   //      "PeerConnectionHandler::onLocalSessionDescriptionCreated",
   //      10000,
   //      AlwaysTruePred(), h, evt));
   //   ASSERT_NE(strlen(evt.sdp.sdpString), 0);
   //   aliceSdp = evt.sdp;
   //   std::cout << "alice sdp:" << std::endl << resip::Data(aliceSdp.sdpString, aliceSdp.sdpLen) << std::endl;
   //}

   //ASSERT_EQ(alice.peerConnection->setLocalDescription(alicePeerConn, aliceSdp), kSuccess);

   //PeerConnectionHandle bobPeerConn = bob.peerConnection->createPeerConnection();
   //bob.peerConnection->setHandler(bobPeerConn, (PeerConnectionHandler*)0xDEADBEEF);
   //bob.peerConnection->setDefaultSettings(bobPeerConn, bobSettings);
   //MediaStreamConfig bobAudioConfig = { MediaStreamConfig::MediaType_Audio, MediaStreamConfig::MediaDirection_SendRecv };
   //ASSERT_EQ(bob.peerConnection->configureStream(bobPeerConn, "audio", bobAudioConfig), kSuccess);
   //ASSERT_EQ(bob.peerConnection->setRemoteDescription(bobPeerConn, aliceSdp), kSuccess);
   //ASSERT_EQ(bob.peerConnection->createAnswer(bobPeerConn, aliceSdp), kSuccess);

   //SessionDescription bobSdp;
   //{
   //   PeerConnectionHandle h;
   //   LocalSessionDescriptionCreatedEvent evt;
   //   ASSERT_TRUE(cpcExpectEvent(bob.peerConnEvents,
   //      "PeerConnectionHandler::onLocalSessionDescriptionCreated",
   //      10000,
   //      AlwaysTruePred(), h, evt));
   //   ASSERT_NE(strlen(evt.sdp.sdpString), 0);
   //   bobSdp = evt.sdp;
   //   std::cout << "bob sdp:" << std::endl << resip::Data(bobSdp.sdpString, bobSdp.sdpLen) << std::endl;
   //}   
   //ASSERT_EQ(bob.peerConnection->setLocalDescription(bobPeerConn, bobSdp), kSuccess);

   //ASSERT_EQ(alice.peerConnection->setRemoteDescription(alicePeerConn, bobSdp), kSuccess);

   //std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   //ASSERT_EQ(bob.peerConnection->close(bobPeerConn), kSuccess);
   //ASSERT_EQ(alice.peerConnection->close(alicePeerConn), kSuccess);

   ////
   ////cpc::vector<AudioCodecInfo> codecs;
   ////std::unique_ptr<TestAudioHandler> testAudioHandler(new TestAudioHandler(codecs));
   ////alice.audio->setHandler(testAudioHandler.get());

   ////ASSERT_EQ(alice.audio->queryCodecList(), kSuccess);
   ////alice.media->process(MediaManager::kBlockingModeInfinite);
   ////ASSERT_NE(codecs.size(), 0);

   ////alice.audio->setCodecEnabled(codecs[0].id, false);
   ////codecs.clear();
   ////ASSERT_EQ(alice.audio->queryCodecList(), kSuccess);
   ////alice.media->process(MediaManager::kBlockingModeInfinite);

   ////ASSERT_NE(codecs.size(), 0);
   ////ASSERT_EQ(codecs[0].enabled, false);
}

TEST_F(WebCallTest, DISABLED_BasicAudioVideoCall) {

   TestAccount alice("alice");

#if _WIN32
   HWND hwndAliceCapture = NULL;
   ASSERT_EQ(0, ViECreateWindow(hwndAliceCapture, 0, 0, 352, 288, "Alice (capture)"));
   alice.video->startCapture();
   alice.video->setLocalVideoRenderTarget(hwndAliceCapture);

   HWND hwndAliceRemote = NULL;
   ASSERT_EQ(0, ViECreateWindow(hwndAliceRemote, 356, 0, 352, 288, "Alice (incoming)"));
   alice.video->setIncomingVideoRenderTarget(hwndAliceRemote);
#endif // _WIN32

   alice.webCall->setHandler((WebCallHandler*)0xDEADBEEF);

   WebNavigationHandle navh = alice.webCall->navigate("http://localhost:8000/cprtc.xml");
   WebCallHandle aliceCall = alice.webCall->createConversation(navh);
   alice.webCall->addParticipant(aliceCall, "1234");
   alice.webCall->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
   {
      WebCallHandle h;
	   NewConversationEvent evt;
	   ASSERT_TRUE(alice.webCallEvents->expectEvent("WebCallHandler::onNewConversation", 
		   15000, HandleEqualsPred<WebCallHandle>(aliceCall), h, evt));
	   ASSERT_EQ(ConversationType_Outgoing, evt.conversationType);
	   ASSERT_EQ("1234", evt.remoteAddress);
   }

   {
      WebCallHandle h;
	   ConversationMediaChangedEvent evt;
	   ASSERT_TRUE(alice.webCallEvents->expectEvent("WebCallHandler::onConversationMediaChanged", 
		   15000, HandleEqualsPred<WebCallHandle>(aliceCall), h, evt));
   }

   {
      WebCallHandle h;
	   ConversationStateChangedEvent evt;
	   ASSERT_TRUE(alice.webCallEvents->expectEvent("WebCallHandler::onConversationStateChanged", 
		   15000, HandleEqualsPred<WebCallHandle>(aliceCall), h, evt));
	   ASSERT_EQ(ConversationState_Connected, evt.conversationState);
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(15000));

   alice.webCall->end(aliceCall);

   {
      WebCallHandle h;
	   ConversationEndedEvent evt;
	   ASSERT_TRUE(alice.webCallEvents->expectEvent("WebCallHandler::onConversationEnded", 
		   15000, HandleEqualsPred<WebCallHandle>(aliceCall), h, evt));
	   ASSERT_EQ(ConversationState_Ended, evt.conversationState);
   }
   });

#if _WIN32
   alice.video->stopCapture();
   alice.video->setLocalVideoRenderTarget(NULL);
   DestroyWindow(hwndAliceCapture);
   DestroyWindow(hwndAliceRemote);
#endif // _WIN32
}

TEST_F(WebCallTest, DISABLED_BasicAudioVideoCallTwice) {

   TestAccount alice("alice");

#if _WIN32
   HWND hwndAliceCapture = NULL;
   ASSERT_EQ(0, ViECreateWindow(hwndAliceCapture, 0, 0, 352, 288, "Alice (capture)"));
   alice.video->startCapture();
   alice.video->setLocalVideoRenderTarget(hwndAliceCapture);

   HWND hwndAliceRemote = NULL;
   ASSERT_EQ(0, ViECreateWindow(hwndAliceRemote, 356, 0, 352, 288, "Alice (incoming)"));
   alice.video->setIncomingVideoRenderTarget(hwndAliceRemote);
#endif // _WIN32

   alice.webCall->setHandler((WebCallHandler*)0xDEADBEEF);

   WebNavigationHandle navh = alice.webCall->navigate("http://localhost:8000/cprtc.xml");
   WebCallHandle aliceCall = alice.webCall->createConversation(navh);
   alice.webCall->addParticipant(aliceCall, "1234");
   alice.webCall->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
   {
      WebCallHandle h;
	   NewConversationEvent evt;
	   ASSERT_TRUE(alice.webCallEvents->expectEvent("WebCallHandler::onNewConversation", 
		   15000, HandleEqualsPred<WebCallHandle>(aliceCall), h, evt));
	   ASSERT_EQ(ConversationType_Outgoing, evt.conversationType);
	   ASSERT_EQ("1234", evt.remoteAddress);
   }

   {
      WebCallHandle h;
	   ConversationMediaChangedEvent evt;
	   ASSERT_TRUE(alice.webCallEvents->expectEvent("WebCallHandler::onConversationMediaChanged", 
		   15000, HandleEqualsPred<WebCallHandle>(aliceCall), h, evt));
   }

   {
      WebCallHandle h;
	   ConversationStateChangedEvent evt;
	   ASSERT_TRUE(alice.webCallEvents->expectEvent("WebCallHandler::onConversationStateChanged", 
		   15000, HandleEqualsPred<WebCallHandle>(aliceCall), h, evt));
	   ASSERT_EQ(ConversationState_Connected, evt.conversationState);
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(15000));

   alice.webCall->end(aliceCall);

   {
      WebCallHandle h;
	   ConversationEndedEvent evt;
	   ASSERT_TRUE(alice.webCallEvents->expectEvent("WebCallHandler::onConversationEnded", 
		   15000, HandleEqualsPred<WebCallHandle>(aliceCall), h, evt));
	   ASSERT_EQ(ConversationState_Ended, evt.conversationState);
   }
   });

   ASSERT_EQ(alice.webCall->closeWebSession(navh), kSuccess);

   // second time around ...
   navh = alice.webCall->navigate("http://localhost:8000/cprtc.xml");
   aliceCall = alice.webCall->createConversation(navh);
   alice.webCall->addParticipant(aliceCall, "1234");
   alice.webCall->start(aliceCall);

   auto aliceEvents2 = std::async(std::launch::async, [&] () {
   {
      WebCallHandle h;
	   NewConversationEvent evt;
	   ASSERT_TRUE(alice.webCallEvents->expectEvent("WebCallHandler::onNewConversation", 
		   15000, HandleEqualsPred<WebCallHandle>(aliceCall), h, evt));
	   ASSERT_EQ(ConversationType_Outgoing, evt.conversationType);
	   ASSERT_EQ("1234", evt.remoteAddress);
   }

   {
      WebCallHandle h;
	   ConversationMediaChangedEvent evt;
	   ASSERT_TRUE(alice.webCallEvents->expectEvent("WebCallHandler::onConversationMediaChanged", 
		   15000, HandleEqualsPred<WebCallHandle>(aliceCall), h, evt));
   }

   {
      WebCallHandle h;
	   ConversationStateChangedEvent evt;
	   ASSERT_TRUE(alice.webCallEvents->expectEvent("WebCallHandler::onConversationStateChanged", 
		   15000, HandleEqualsPred<WebCallHandle>(aliceCall), h, evt));
	   ASSERT_EQ(ConversationState_Connected, evt.conversationState);
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(15000));

   alice.webCall->end(aliceCall);

   {
      WebCallHandle h;
	   ConversationEndedEvent evt;
	   ASSERT_TRUE(alice.webCallEvents->expectEvent("WebCallHandler::onConversationEnded", 
		   15000, HandleEqualsPred<WebCallHandle>(aliceCall), h, evt));
	   ASSERT_EQ(ConversationState_Ended, evt.conversationState);
   }
   });

   ASSERT_EQ(alice.webCall->closeWebSession(navh), kSuccess);

#if _WIN32
   alice.video->stopCapture();
   alice.video->setLocalVideoRenderTarget(NULL);
   DestroyWindow(hwndAliceCapture);
   DestroyWindow(hwndAliceRemote);
#endif // _WIN32
}


}  // namespace

#endif // CPCAPI2_WEB_CALL_MODULE
