#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#if (CPCAPI2_BRAND_WEBSOCKET_SERVER_MODULE == 1)

#include "cpcapi2_test_fixture.h"

#include <gtest/gtest.h>
#include <cpcapi2.h>

#include <iostream>
#include <fstream>
#include <string>
#include <mutex>
#include <atomic>

#include "test_framework/cpcapi2_test_framework.h"
#include "interface/experimental/websocket_server/WebSocketServerManager.h"
#include "../experimental/websocket_server/WebSocketServerHandler.h"
#include <webrtc/base/platform_thread.h>

#include <websocketpp/config/asio_client.hpp>
#include <websocketpp/client.hpp>
#include <websocketpp/common/thread.hpp>

using namespace CPCAPI2;
using namespace CPCAPI2::WebSocketServer;
using namespace CPCAPI2::test;

typedef websocketpp::client<websocketpp::config::asio_tls_client> wss_client;
//typedef websocketpp::client<websocketpp::config::asio_client> wss_client;
typedef std::shared_ptr<boost::asio::ssl::context> context_ptr;
typedef websocketpp::config::asio_client::message_type::ptr message_ptr;
typedef websocketpp::lib::thread thread_type;
typedef websocketpp::lib::shared_ptr<thread_type> thread_ptr;

using websocketpp::lib::placeholders::_1;
using websocketpp::lib::placeholders::_2;
using websocketpp::lib::bind;

namespace {

class WebSocketSecureClient
{
public:
   WebSocketSecureClient(std::string dn = "[wss_client]", bool disconnectExpect = false) : 
      display_name(dn), expectDisconnect(disconnectExpect), mConnection(NULL),
      messageReceived(false), disconnected(false),
      mWebSocketAccessOstream(&mWebSocketAccessBuffer),
      mWebSocketErrorOstream(&mWebSocketErrorBuffer) {

      mWebSocketAccessBuffer.setDisplayName(display_name);
      mWebSocketErrorBuffer.setDisplayName(display_name);

      mClient.clear_access_channels(websocketpp::log::alevel::all);
      mClient.clear_error_channels(websocketpp::log::elevel::all);
      mClient.init_asio();

      mClient.set_tls_init_handler(bind(&WebSocketSecureClient::on_tls_init, this));
      mClient.set_message_handler(bind(&WebSocketSecureClient::on_message, this, ::_1, ::_2));
      mClient.set_open_handler(bind(&WebSocketSecureClient::on_open, this, ::_1));
      mClient.set_close_handler(bind(&WebSocketSecureClient::on_close, this, ::_1));
      mClient.set_fail_handler(bind(&WebSocketSecureClient::on_fail, this, ::_1));
      mClient.get_alog().set_ostream(&mWebSocketAccessOstream);
      mClient.get_elog().set_ostream(&mWebSocketErrorOstream);

      mClient.set_access_channels(websocketpp::log::alevel::all);
      mClient.set_error_channels(websocketpp::log::alevel::all);

      mClient.start_perpetual();
      mThread.reset(new thread_type(&wss_client::run, &mClient));
   }
   ~WebSocketSecureClient() {
      shutdown();
   }
   bool connect(std::string uri) {
      websocketpp::lib::error_code ec;
      mConnection = mClient.get_connection(uri, ec);
      if (ec) {
         safeCout(display_name << " could not create connection: " << ec.message());
         mConnection.reset();
         return false;
      }
      messageReceivedEvent.lock();
      mConnected = false;
      mClient.connect(mConnection);
      for (;;)
      {
         if (mConnected)
         {
            break;
         }
         std::this_thread::sleep_for(std::chrono::milliseconds(100));
      }
      safeCout(display_name << " connected to " << uri);
      return true;
   }
   void disconnect() {
      if (mConnection) {
         websocketpp::lib::error_code ec;
         mConnection->close(websocketpp::close::status::normal, "", ec);
         if (ec) {
            safeCout(display_name << " error closing connection: " << ec.message());
         }
         else safeCout(display_name << " disconnected");
      }
   }
   bool send(std::string msg) {
      if (mConnection) {
         mConnection->send(msg, websocketpp::frame::opcode::text);
         safeCout(display_name << " sent: " << msg);
         return true;
      }
      return false;
   }
   void shutdown() {
      if (mThread) {
         mClient.stop_perpetual();
         disconnect();
         std::thread::native_handle_type h = mThread->native_handle();
         safeCout(display_name << "Attempting to join websocketpp thread " << h);
         mThread->join();
         safeCout(display_name << "Done joining websocketpp thread " << h);
         mThread.reset();
      }
      if (expectDisconnect) ASSERT_TRUE(disconnected);
   }
private:
   context_ptr on_tls_init() {
      safeCout(display_name << " calling tls init from thread " << rtc::CurrentThreadId());
      context_ptr ctx = std::make_shared<boost::asio::ssl::context>(boost::asio::ssl::context::sslv23);
      try {
         ctx->set_options(boost::asio::ssl::context::default_workarounds |
            boost::asio::ssl::context::no_sslv2 |
            boost::asio::ssl::context::no_sslv3 |
            boost::asio::ssl::context::no_tlsv1);
      } catch (std::exception &e) {
         safeCout(display_name << " error in tls init: " << e.what());
      }
      return ctx;
   }
   void on_fail(websocketpp::connection_hdl hdl) {
      wss_client::connection_ptr con = mClient.get_con_from_hdl(hdl);
      safeCout(display_name << " fail handler:");
      safeCout(con->get_state());
      safeCout(con->get_local_close_code());
      safeCout(con->get_local_close_reason());
      safeCout(con->get_remote_close_code());
      safeCout(con->get_remote_close_reason());
      safeCout(con->get_ec() << " - " << con->get_ec().message());
      mConnection.reset();
   }
   void on_open(websocketpp::connection_hdl hdl) {
      ASSERT_EQ(mClient.get_con_from_hdl(hdl), mConnection);
      mConnected = true;
   }
   void on_close(websocketpp::connection_hdl hdl) {
      ASSERT_EQ(mClient.get_con_from_hdl(hdl), mConnection);
      if (expectDisconnect) {
         safeCout(display_name << " disconnected [" << mConnection->get_remote_close_code() << "]");
         disconnected = true;
      }
      mConnection.reset();
      if (!expectedMessage.empty()) ASSERT_TRUE(messageReceived);
   }
   void on_message(websocketpp::connection_hdl hdl, message_ptr msg) {
      safeCout(display_name << " received: " << msg->get_payload());
      ASSERT_EQ(mClient.get_con_from_hdl(hdl), mConnection);
      ASSERT_EQ(expectedMessage, msg->get_payload());
      messageReceived = true;
      messageReceivedEvent.unlock();
   }
public:
   std::string expectedMessage;
   bool expectDisconnect;
   std::timed_mutex messageReceivedEvent;
private:
   wss_client mClient;
   thread_ptr mThread;
   wss_client::connection_ptr mConnection;
   std::string display_name;
   bool messageReceived, disconnected;
   std::ostream mWebSocketAccessOstream;
   std::ostream mWebSocketErrorOstream;
   std::atomic_bool mConnected = false;

   class OstreamBuffer : public std::stringbuf
   {
   protected:
      bool mIsErrorLog;
      std::string mDisplayName;

   public:
      OstreamBuffer(bool isErrorLog = false) :
         mIsErrorLog(isErrorLog)
      {

      }
      virtual int sync()
      {
         std::string trace = this->str();
         this->str("");

         // Erase timestamp
         size_t index = trace.find(']');
         if (index != std::string::npos)
            trace.erase(0, index + 1); 
         // Erase trailing newline
         index = trace.find_last_not_of("\r\n");
         if (index != std::string::npos)
            trace.erase(index + 1);

         if (mIsErrorLog)
         {
            safeCout("[WSPP] " << mDisplayName << " " << trace);
         }
         else
         {
            safeCout("[WSPP] " << mDisplayName << " " << trace);
         }

         return 0;
      }

      void setDisplayName(const std::string& displayName)
      {
         mDisplayName = displayName;
      }
   };

   OstreamBuffer mWebSocketAccessBuffer;
   OstreamBuffer mWebSocketErrorBuffer;
};

class WebSocketServerTest : public CpcapiAutoTest
{
public:
   WebSocketServerTest() 
   {
      phone = Phone::create();
      LicenseInfo licenseInfo;
      ConnectionPreferences conPref;
      dynamic_cast<PhoneInternal*>(phone)->initialize(licenseInfo, NULL, conPref);

      static_cast<PhoneInternal*>(phone)->setPhoneName("wssServer");
      phone->setLoggingEnabled(&AutoTestsLogger::instance(), true);
      mLocalLogger.reset(new AutoTestsLocalLogger("wssServer"));
      static_cast<PhoneInternal*>(phone)->setLocalCallbackLoggingEnabled(mLocalLogger.get(), true);

      wsServer = WebSocketServerManager::getInterface(phone);
      events = new CPCAPI2::test::EventHandler("wssServer", dynamic_cast<CPCAPI2::AutoTestProcessor*>(wsServer));
      wsServer->setHandler((WebSocketServerHandler*)0xDEADBEEF);
   }
   virtual ~WebSocketServerTest() 
   {
      events->shutdown();
      delete events;
      CPCAPI2::Phone::release(phone); 
   }
    
   CPCAPI2::Phone* phone;
   CPCAPI2::test::EventHandler* events;
   CPCAPI2::WebSocketServer::WebSocketServerManager* wsServer;
   std::unique_ptr<AutoTestsLocalLogger> mLocalLogger;
};

TEST_F(WebSocketServerTest, BasicReceiveSend)
{
   WebSocketServerHandle h;
   ServerStateChangeEvent evt;
   //branded settings:
   wsServer->start();

   ASSERT_TRUE(cpcExpectEvent(events, "WebSocketServerHandler::onServerStateChange",
      15000, AlwaysTruePred(), h, evt));
   ASSERT_EQ(evt.currentState, ServerState_Running);

   auto serverEvents = std::async(std::launch::async, [&]()
   {
      ConnectionOpenedEvent coEvt;
      ASSERT_TRUE(cpcExpectEvent(events, "WebSocketServerHandler::onConnectionOpened",
         15000, AlwaysTruePred(), h, coEvt));
      ASSERT_TRUE(coEvt.connection > 0);
      
      MessageReceivedEvent mrEvt;
      ASSERT_TRUE(cpcExpectEvent(events, "WebSocketServerHandler::onMessageReceived",
         15000, AlwaysTruePred(), h, mrEvt));
      ASSERT_EQ(mrEvt.data, "Hello server.");
      
      wsServer->send(mrEvt.connection, "Hello client.");

      ConnectionClosedEvent ccEvt;
      ASSERT_TRUE(cpcExpectEvent(events, "WebSocketServerHandler::onConnectionClosed",
         15000, AlwaysTruePred(), h, ccEvt));
      ASSERT_EQ(ccEvt.connection, coEvt.connection);
   });

   auto clientEvents = std::async(std::launch::async, [&]()
   {
      WebSocketSecureClient client;

      ASSERT_TRUE(client.connect("wss://localhost:9003"));
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      client.expectedMessage = "Hello client.";
      ASSERT_TRUE(client.send("Hello server."));

      client.messageReceivedEvent.try_lock_for(std::chrono::milliseconds(3000));
      client.disconnect();

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      client.shutdown();
   });
   waitFor2(serverEvents, clientEvents);

   wsServer->stop();
   ASSERT_TRUE(cpcExpectEvent(events, "WebSocketServerHandler::onServerStateChange",
      15000, AlwaysTruePred(), h, evt));
   ASSERT_EQ(evt.currentState, ServerState_Stopped);
}

// flaky test on mac -- see comments inline about serverEvents
#ifdef _WIN32
TEST_F(WebSocketServerTest, MultipleClients)
{
   const int numClients = 10;

   WebSocketServerHandle h;
   ServerStateChangeEvent evt;
   //branded settings:
   wsServer->start();
   ASSERT_TRUE(cpcExpectEvent(events, "WebSocketServerHandler::onServerStateChange",
      15000, AlwaysTruePred(), h, evt));
   ASSERT_EQ(evt.currentState, ServerState_Running);

   auto serverEvents = std::async(std::launch::async, [&]()
   {
      // the timing here is flakey; sometimes the previous iteration's onConnectionClosed
      // doesn't fire right away, which causes a delay in handling the subsequent iteration's
      // events -- and client and server test threads get out of sync, and we hit failures
      for (int i = 1; i <= numClients; i++)
      {
         ConnectionOpenedEvent coEvt;
         ASSERT_TRUE(cpcExpectEvent(events, "WebSocketServerHandler::onConnectionOpened",
            15000, AlwaysTruePred(), h, coEvt));
         ASSERT_TRUE(coEvt.connection > 0);
         
         MessageReceivedEvent mrEvt;
         std::stringstream s, r;
         ASSERT_TRUE(cpcExpectEvent(events, "WebSocketServerHandler::onMessageReceived",
            15000, AlwaysTruePred(), h, mrEvt));
         r << "Hello server. " << i;
         ASSERT_EQ(mrEvt.data, r.str().c_str());
         
         s << "Hello client " << i << ".";
         wsServer->send(mrEvt.connection, s.str().c_str());

         ConnectionClosedEvent ccEvt;
         ASSERT_TRUE(cpcExpectEvent(events, "WebSocketServerHandler::onConnectionClosed",
            15000, AlwaysTruePred(), h, ccEvt));
         ASSERT_EQ(ccEvt.connection, coEvt.connection);
      }
   });

   auto clientEvents = std::async(std::launch::async, [&]()
   {
      for (int i = 1; i <= numClients; i++)
      {
         std::stringstream name, s, r;
         name << "[wss_client_" << i << "]";
         WebSocketSecureClient client(name.str());

         ASSERT_TRUE(client.connect("wss://localhost:9003"));
         std::this_thread::sleep_for(std::chrono::milliseconds(1000));
         
         r << "Hello client " << i << ".";
         client.expectedMessage = r.str();
         s << "Hello server. " << i;
         ASSERT_TRUE(client.send(s.str().c_str()));

         client.messageReceivedEvent.try_lock_for(std::chrono::milliseconds(5000));
         client.disconnect();
         std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      }
   });
   waitFor2(serverEvents, clientEvents);

   wsServer->stop();
   ASSERT_TRUE(cpcExpectEvent(events, "WebSocketServerHandler::onServerStateChange",
      15000, AlwaysTruePred(), h, evt));
   ASSERT_EQ(evt.currentState, ServerState_Stopped);
}
#endif // #ifdef _WIN32

TEST_F(WebSocketServerTest, MultipleClients2)
{
   const int numClients = 10;

   WebSocketServerHandle h;
   ServerStateChangeEvent evt;
   //branded settings:
   wsServer->start();
   ASSERT_TRUE(cpcExpectEvent(events, "WebSocketServerHandler::onServerStateChange",
      15000, AlwaysTruePred(), h, evt));
   ASSERT_EQ(evt.currentState, ServerState_Running);
   
   auto serverEvents = std::async(std::launch::async, [&]()
   {
      for (int i = 0; i < numClients; i++)
      {
         ConnectionOpenedEvent coEvt;
         ASSERT_TRUE(cpcExpectEvent(events, "WebSocketServerHandler::onConnectionOpened",
            15000, AlwaysTruePred(), h, coEvt));
         ASSERT_TRUE(coEvt.connection > 0);
      }
      for (int i = 0; i < numClients; i++)
      {
         MessageReceivedEvent mrEvt;
         ASSERT_TRUE(cpcExpectEvent(events, "WebSocketServerHandler::onMessageReceived",
            15000, AlwaysTruePred(), h, mrEvt));
         ASSERT_EQ(mrEvt.data, "Hello server.");

         wsServer->send(mrEvt.connection, "Hello client.");
      }
      for (int i = 0; i < numClients; i++)
      {
         ConnectionClosedEvent ccEvt;
         ASSERT_TRUE(cpcExpectEvent(events, "WebSocketServerHandler::onConnectionClosed",
            15000, AlwaysTruePred(), h, ccEvt));
      }
   });

   std::vector<WebSocketSecureClient*> clients;
   auto clientEvents = std::async(std::launch::async, [&]()
   {
      for (int i = 0; i < numClients; i++)
      {
         std::stringstream name;
         name << "[wss_client_" << i << "]";
         clients.push_back(new WebSocketSecureClient(name.str()));
         
         ASSERT_TRUE(clients[i]->connect("wss://localhost:9003"));
      }
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      for (int i = 0; i < numClients; i++)
      {
         clients[i]->expectedMessage = "Hello client.";
         ASSERT_TRUE(clients[i]->send("Hello server."));
         clients[i]->messageReceivedEvent.try_lock_for(std::chrono::milliseconds(3000));
      }
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      for (int i = 0; i < numClients; i++)
      {
         clients[i]->disconnect();
      }
   });
   waitFor2(serverEvents, clientEvents);

   wsServer->stop();
   ASSERT_TRUE(cpcExpectEvent(events, "WebSocketServerHandler::onServerStateChange",
      15000, AlwaysTruePred(), h, evt));
   ASSERT_EQ(evt.currentState, ServerState_Stopped);
   for (int i = 0; i < numClients; i++)
   {
      delete clients[i];
   }
}

TEST_F(WebSocketServerTest, ServerDisconnect)
{
   //Try out start() /w custom settings:
   WebSocketServerSettings settings;
   settings.certificatePem = "MIIGlDCCBXygAwIBAgIQeLU0zxOttSudZjGz96exNTANBgkqhkiG9w0BAQsFADCB\njzELMAkGA1UEBhMCR0IxGzAZBgNVBAgTEkdyZWF0ZXIgTWFuY2hlc3RlcjEQMA4G\nA1UEBxMHU2FsZm9yZDEYMBYGA1UEChMPU2VjdGlnbyBMaW1pdGVkMTcwNQYDVQQD\nEy5TZWN0aWdvIFJTQSBEb21haW4gVmFsaWRhdGlvbiBTZWN1cmUgU2VydmVyIENB\nMB4XDTE5MDgxMjAwMDAwMFoXDTIxMDgxMTIzNTk1OVowXTEhMB8GA1UECxMYRG9t\nYWluIENvbnRyb2wgVmFsaWRhdGVkMRQwEgYDVQQLEwtQb3NpdGl2ZVNTTDEiMCAG\nA1UEAxMZY3BjbGllbnRhcGkuc29mdHBob25lLmNvbTCCASIwDQYJKoZIhvcNAQEB\nBQADggEPADCCAQoCggEBAL5G4BFjMRQ3SKB95u0DXMLPqx7sqznf4fTWYzTC8ee5\ni8hT4Ivo5g+CIjHOAHeEexLCGSNIMOS3hngelHSFVPy1OMe42IZuMvjc9robV2oH\nhJ1M/Q3FQ932xtq6/+Gfb3+9ykOrsE+PSozqfGdNHCDVt1QVgTzNcu5NCDC0fSIt\nqjKcPkICLX1xBwZlix9UfdbFLmDKKOJ+3ThIFrAq/iM9fsQ7c67Az0AciHKzcZz0\nAucSBzdR9/BLoisfIgcCFprNETMhWSsaeIhiTyrm8z8vxUcr83oz1pUp3jU4trJA\nr3/glFuc8ur+GtSbDKWHfmLpyWkirqt3joa3YAwBunMCAwEAAaOCAxswggMXMB8G\nA1UdIwQYMBaAFI2MXsRUrYrhd+mb+ZsF4bgBjWHhMB0GA1UdDgQWBBRN4GvkB9du\n4aJ7bRhe92d8p1RTDjAOBgNVHQ8BAf8EBAMCBaAwDAYDVR0TAQH/BAIwADAdBgNV\nHSUEFjAUBggrBgEFBQcDAQYIKwYBBQUHAwIwSQYDVR0gBEIwQDA0BgsrBgEEAbIx\nAQICBzAlMCMGCCsGAQUFBwIBFhdodHRwczovL3NlY3RpZ28uY29tL0NQUzAIBgZn\ngQwBAgEwgYQGCCsGAQUFBwEBBHgwdjBPBggrBgEFBQcwAoZDaHR0cDovL2NydC5z\nZWN0aWdvLmNvbS9TZWN0aWdvUlNBRG9tYWluVmFsaWRhdGlvblNlY3VyZVNlcnZl\nckNBLmNydDAjBggrBgEFBQcwAYYXaHR0cDovL29jc3Auc2VjdGlnby5jb20wQwYD\nVR0RBDwwOoIZY3BjbGllbnRhcGkuc29mdHBob25lLmNvbYIdd3d3LmNwY2xpZW50\nYXBpLnNvZnRwaG9uZS5jb20wggF/BgorBgEEAdZ5AgQCBIIBbwSCAWsBaQB2APZc\nlC/RdzAiFFQYCDCUVo7jTRMZM7/fDC8gC8xO8WTjAAABbIcW6usAAAQDAEcwRQIh\nALJzWN7tP5WIMys+irmi1P5GrpAzUz3MRa3vXjpnoqfSAiANGZZQyJ1Dbnun41PF\nReITU4aB8bUm6QIRYa2hC4W87wB2AESUZS6w7s6vxEAH2Kj+KMDa5oK+2MsxtT/T\nM5a1toGoAAABbIcW670AAAQDAEcwRQIgaSyPobfXXhh8nm2BqVFtdbRTE3pB4Yao\nDU2cl/WlXiUCIQDb/DwsBjvd1+1pI4dzPJZFsI4nHdnVxr85GC2cb5qDxQB3AFWB\n1MIWkDYBSuoLm1c8U/DA5Dh4cCUIFy+jqh0HE9MMAAABbIcW6uQAAAQDAEgwRgIh\nALNF/yLPclckEKWgNz4hGkko/sCJOeIQFmo5nB0xTYYsAiEAxvKH2/AFNKHf39sy\nAbj5XoVjjTy2PLHqNveG5Hft7y8wDQYJKoZIhvcNAQELBQADggEBAJzPkCnoFF4w\na/H1rgXG5MaoIMx22EJjoNVZ2OG44xmrEi5BhKmj7dFU2ZBUj4VDQZAWNtYXlStc\nfMeUqW1kmT24QbqvjSKTPpIKgKvj0Ok9KoTE6+C8xSYoFh+BHlIaI2qL02ry7Nhc\nemv7y1vnX2iF7neuzGZdcYGlnoavHh6BbLew6zb8NL5E3x5MyqmDHgadOpjJDcC5\nLWtq5tU3fs2d2DGsIe3dLjohyRJpZRL13JtWMJ4qUoXjmx7r6cyJwzfmKFckrci5\nAYt+IO6wBkTIXmbOky1wBHdW/uwgussFiyCnLU40QF5pKs3HMOrtujBzCFYtJ+sY\nXozmrQnLVbg=";
   settings.privateKeyPem = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC+RuARYzEUN0ig\nfebtA1zCz6se7Ks53+H01mM0wvHnuYvIU+CL6OYPgiIxzgB3hHsSwhkjSDDkt4Z4\nHpR0hVT8tTjHuNiGbjL43Pa6G1dqB4SdTP0NxUPd9sbauv/hn29/vcpDq7BPj0qM\n6nxnTRwg1bdUFYE8zXLuTQgwtH0iLaoynD5CAi19cQcGZYsfVH3WxS5gyijift04\nSBawKv4jPX7EO3OuwM9AHIhys3Gc9ALnEgc3UffwS6IrHyIHAhaazREzIVkrGniI\nYk8q5vM/L8VHK/N6M9aVKd41OLayQK9/4JRbnPLq/hrUmwylh35i6clpIq6rd46G\nt2AMAbpzAgMBAAECggEAXHXIT+qpYK90hThozGdD3g5XktFiat8Vx4md73eDYRzS\nhefsR1BG6uZLg3Qc2fbwby8OVAyyZZGgsCH79ZsMrzfIfZSe8sP91NxasBi1I3q/\nwEg290gSMHvIKWWRDawR3Dr3XGbDaAWzLkKLbQ8VSgdP7HX+tPMIlC+ueSxbWg2L\nNiL0IJaYiSUB8BfsREpYePfVnVSt9maimgYvJYw4HveoUAVxjN8swEdVEwVuIHuo\nzZTrHT1yfc1i9L+Fh71edp7WBRe6YclHxO5nakVhXmhLVDT2yQTbg9Sik9DyFenL\nD+QjmIbg3bnnbiSRF5Pt66ciTdIa1f6Q6km83hDIgQKBgQDxjZ9zsYERB0Eldm6i\n1lLiMbraIa8e19cGqZzXU9M5XOTcsYojIOCNBOMH1ZL7gQU5eY2f9nVoQEyxkJZ0\n4zW3NCN2rqz+HvF1cmOhwbO8ZJmcr3PbbDDyUZ9hgWkPQO47JCDvW7kLB4ig3ReL\nJL8Nw0oWNanEa8gEERyOQiO5UwKBgQDJqCthoZKcQ0YPJiDVkcR95Bfkye5uIXLS\nrWcNz/n1uImcTcdojiCelYvvsIMWOW+7q/KoBx+KsYYxVH88lo0noPLY0VrH4Q86\nPuTvSmOwwRMQ16Pw6A40sYJM6rQAdMeaPT18eq93X8uLnpLdrGijj1xy3imZnrUx\nWgr7Zfs2YQKBgBcaMHHkPC/gvb5TgWlZjjvL/c73AcS1Z/9fIX0Gq2EFHW5uHIQX\nlOJn4T7Skmgzgshoc78k1z3NSIiDB6DOSkjnV6z2L91uIDHXaugBmqseBAMUVMRb\nqKLEY7XzppSq5R4K6ot5/kyrv8mC28mXDh7G2sMdBsuec1bQPCbu9HO5AoGAaAey\ngYZJzQVDkjMYQzUUyeEull+5+eSViKvd+xksmR/fFdxmH8EagNUjQL6tkXyF8Kou\n35H1KqaUVMSDcy0zmpsETOgLzguDwIeLsQqOTTP9cvXb5D7CfVqJnsaBn66e/inT\nm3DrLkkkB14B5Au6W45sofGMQmzGirQp8DFXHGECgYEA0QObOk1GlCIYxUnDZvOB\nwZitAfkM4S2gc+oOqeKBXmxyUZk5gBW+eOwf0MM6eTF+GkR49ocPg75FVah06T9F\nOKCVxjupDSTarL6rsz44ZvLXno6B0pIcoGY6lgSqvRBkCKDlRn0dx68H21q3bhaK\nyu0jR+1gDG4Uyut1l+9EyBk=";
   settings.ciphers = CipherSuiteAdvanced;
   settings.url = "wss://localhost:9003";
   const int numClients = 10;

   WebSocketServerHandle h;
   ServerStateChangeEvent evt;
   wsServer->start(&settings);
   ASSERT_TRUE(cpcExpectEvent(events, "WebSocketServerHandler::onServerStateChange",
      15000, AlwaysTruePred(), h, evt));
   ASSERT_EQ(evt.currentState, ServerState_Running);

   auto serverEvents = std::async(std::launch::async, [&]()
   {
      for (int i = 0; i < numClients; i++)
      {
         ConnectionOpenedEvent coEvt;
         ASSERT_TRUE(cpcExpectEvent(events, "WebSocketServerHandler::onConnectionOpened",
            15000, AlwaysTruePred(), h, coEvt));
         ASSERT_TRUE(coEvt.connection > 0);
      }

      wsServer->stop();
      ASSERT_TRUE(cpcExpectEvent(events, "WebSocketServerHandler::onServerStateChange",
         15000, AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.currentState, ServerState_Stopped);
   });

   std::vector<WebSocketSecureClient*> clients;
   auto clientEvents = std::async(std::launch::async, [&]()
   {
      for (int i = 0; i < numClients; i++)
      {
         std::stringstream name;
         name << "[wss_client_" << i << "]";
         clients.push_back(new WebSocketSecureClient(name.str(), true));
         ASSERT_TRUE(clients[i]->connect("wss://localhost:9003"));
      }
   });
   waitFor2(serverEvents, clientEvents);
   for (int i = 0; i < numClients; i++)
   {
      delete clients[i];
   }
}

TEST_F(WebSocketServerTest, ServerRestart)
{
   //Try out certificate from file:
   WebSocketServerSettings settings;
   settings.certificatePem = TestEnvironmentConfig::testResourcePath() + "CPcerts/electron/cert.pem";
   settings.privateKeyPem = TestEnvironmentConfig::testResourcePath() + "CPcerts/electron/key.pem";
   settings.ciphers = CipherSuiteAdvanced;
   settings.url = "wss://localhost:9003";
   const int numClients = 10;

   WebSocketServerHandle h;
   ServerStateChangeEvent evt;
   wsServer->start(&settings);
   ASSERT_TRUE(cpcExpectEvent(events, "WebSocketServerHandler::onServerStateChange",
      15000, AlwaysTruePred(), h, evt));
   ASSERT_EQ(evt.currentState, ServerState_Running);

   std::atomic_bool serverRestartFinished = false;

   ConnectionOpenedEvent coEvt;
   auto serverEvents = std::async(std::launch::async, [&]()
   {
      for (int i = 0; i < numClients; i++)
      {
         ASSERT_TRUE(cpcExpectEvent(events, "WebSocketServerHandler::onConnectionOpened",
            15000, AlwaysTruePred(), h, coEvt));
         ASSERT_TRUE(coEvt.connection > 0);
         safeCout("Client connected [" << coEvt.connection << "]");
      }
      wsServer->stop();
      ASSERT_TRUE(cpcExpectEvent(events, "WebSocketServerHandler::onServerStateChange",
         15000, AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.currentState, ServerState_Stopped);
      
      wsServer->start(&settings);
      ASSERT_TRUE(cpcExpectEvent(events, "WebSocketServerHandler::onServerStateChange",
         15000, AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.currentState, ServerState_Running);
      serverRestartFinished = true;

      for (int i = 0; i < numClients; i++)
      {
         ASSERT_TRUE(cpcExpectEvent(events, "WebSocketServerHandler::onConnectionOpened",
            15000, AlwaysTruePred(), h, coEvt));
         ASSERT_TRUE(coEvt.connection > 0);
         safeCout("Client connected [" << coEvt.connection << "]");
      }
   });

   std::vector<WebSocketSecureClient*> clients;
   auto clientEvents = std::async(std::launch::async, [&]()
   {
      for (int i = 0; i < numClients; i++)
      {
         std::stringstream name;
         name << "[wss_client_" << i << "]";
         clients.push_back(new WebSocketSecureClient(name.str(), true));
         ASSERT_TRUE(clients[i]->connect("wss://localhost:9003"));
      }
      while (!serverRestartFinished)
      {
         std::this_thread::sleep_for(std::chrono::milliseconds(100));
      }
      std::this_thread::sleep_for(std::chrono::seconds(3));
      for (int i = 0; i < numClients; i++)
      {
         // previously this code attempted to reuse the existing client and reconnect;
         // after the C++17 update this stopped working -- unclear why, but since
         // we are trying to test the websocket server, and not websocket client here,
         // we work around this issue by re-creating each client
         delete clients[i];
         std::stringstream name;
         name << "[wss_client_" << i << "]";
         clients[i] = new WebSocketSecureClient(name.str(), true);
         clients[i]->expectDisconnect = false;
         ASSERT_TRUE(clients[i]->connect("wss://localhost:9003"));
      }
   });
   waitFor2(serverEvents, clientEvents);

   for (int i = 0; i < numClients; i++)
   {
      delete clients[i];
   }

   wsServer->stop();
   ASSERT_TRUE(cpcExpectEvent(events, "WebSocketServerHandler::onServerStateChange",
      15000, AlwaysTruePred(), h, evt));
   ASSERT_EQ(evt.currentState, ServerState_Stopped);
}

bool fileToString(const cpc::string& filePath, const cpc::string& newlineChar, cpc::string& outString)
{
   std::stringstream ss;
   std::string line;
   std::ifstream file(filePath.c_str());
   if (file.is_open())
   {
      while ( std::getline (file, line) )
      {
         ss << line << newlineChar;
      }
      file.close();
   }
   else
   {
      return false;
   }
   
   outString = ss.str().c_str();
   return true;
}

TEST_F(WebSocketServerTest, WindowsNewlineStringPem)
{
   WebSocketServerSettings settings;
   
   ASSERT_TRUE(fileToString(TestEnvironmentConfig::testResourcePath() + "CPcerts/electron/cert.pem", "\r\n", settings.certificatePem));
   ASSERT_TRUE(fileToString(TestEnvironmentConfig::testResourcePath() + "CPcerts/electron/key.pem", "\r\n", settings.privateKeyPem));
   settings.ciphers = CipherSuiteAdvanced;
   settings.url = "wss://localhost:9003";
   const int numClients = 10;
 

   WebSocketServerHandle h;
   ServerStateChangeEvent evt;
   wsServer->start(&settings);
   ASSERT_TRUE(cpcExpectEvent(events, "WebSocketServerHandler::onServerStateChange",
      15000, AlwaysTruePred(), h, evt));
   ASSERT_EQ(evt.currentState, ServerState_Running);

   auto serverEvents = std::async(std::launch::async, [&]()
   {
      for (int i = 0; i < numClients; i++)
      {
         ConnectionOpenedEvent coEvt;
         ASSERT_TRUE(cpcExpectEvent(events, "WebSocketServerHandler::onConnectionOpened",
            15000, AlwaysTruePred(), h, coEvt));
         ASSERT_TRUE(coEvt.connection > 0);
      }

      wsServer->stop();
      ASSERT_TRUE(cpcExpectEvent(events, "WebSocketServerHandler::onServerStateChange",
         15000, AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.currentState, ServerState_Stopped);
   });

   std::vector<WebSocketSecureClient*> clients;
   auto clientEvents = std::async(std::launch::async, [&]()
   {
      for (int i = 0; i < numClients; i++)
      {
         std::stringstream name;
         name << "[wss_client_" << i << "]";
         clients.push_back(new WebSocketSecureClient(name.str(), true));
         ASSERT_TRUE(clients[i]->connect("wss://localhost:9003"));
      }
   });
   waitFor2(serverEvents, clientEvents);
   for (int i = 0; i < numClients; i++)
   {
      delete clients[i];
   }
}

TEST_F(WebSocketServerTest, UnixNewlineStringPem)
{
   WebSocketServerSettings settings;
   
   ASSERT_TRUE(fileToString(TestEnvironmentConfig::testResourcePath() + "CPcerts/electron/cert.pem", "\n", settings.certificatePem));
   ASSERT_TRUE(fileToString(TestEnvironmentConfig::testResourcePath() + "CPcerts/electron/key.pem", "\n", settings.privateKeyPem));
   settings.ciphers = CipherSuiteAdvanced;
   settings.url = "wss://localhost:9003";
   const int numClients = 10;
 

   WebSocketServerHandle h;
   ServerStateChangeEvent evt;
   wsServer->start(&settings);
   ASSERT_TRUE(cpcExpectEvent(events, "WebSocketServerHandler::onServerStateChange",
      15000, AlwaysTruePred(), h, evt));
   ASSERT_EQ(evt.currentState, ServerState_Running);

   auto serverEvents = std::async(std::launch::async, [&]()
   {
      for (int i = 0; i < numClients; i++)
      {
         ConnectionOpenedEvent coEvt;
         ASSERT_TRUE(cpcExpectEvent(events, "WebSocketServerHandler::onConnectionOpened",
            15000, AlwaysTruePred(), h, coEvt));
         ASSERT_TRUE(coEvt.connection > 0);
      }

      wsServer->stop();
      ASSERT_TRUE(cpcExpectEvent(events, "WebSocketServerHandler::onServerStateChange",
         15000, AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.currentState, ServerState_Stopped);
   });

   std::vector<WebSocketSecureClient*> clients;
   auto clientEvents = std::async(std::launch::async, [&]()
   {
      for (int i = 0; i < numClients; i++)
      {
         std::stringstream name;
         name << "[wss_client_" << i << "]";
         clients.push_back(new WebSocketSecureClient(name.str(), true));
         ASSERT_TRUE(clients[i]->connect("wss://localhost:9003"));
      }
   });
   waitFor2(serverEvents, clientEvents);
   for (int i = 0; i < numClients; i++)
   {
      delete clients[i];
   }
}

TEST_F(WebSocketServerTest, UnixNewlineFilePem)
{
   WebSocketServerSettings settings;
   
   settings.certificatePem = TestEnvironmentConfig::testResourcePath() + "CPcerts/electron/cert.pem";
   settings.privateKeyPem = TestEnvironmentConfig::testResourcePath() + "CPcerts/electron/key.pem";
   settings.ciphers = CipherSuiteAdvanced;
   settings.url = "wss://localhost:9003";
   const int numClients = 10;
 

   WebSocketServerHandle h;
   ServerStateChangeEvent evt;
   wsServer->start(&settings);
   ASSERT_TRUE(cpcExpectEvent(events, "WebSocketServerHandler::onServerStateChange",
      15000, AlwaysTruePred(), h, evt));
   ASSERT_EQ(evt.currentState, ServerState_Running);

   auto serverEvents = std::async(std::launch::async, [&]()
   {
      for (int i = 0; i < numClients; i++)
      {
         ConnectionOpenedEvent coEvt;
         ASSERT_TRUE(cpcExpectEvent(events, "WebSocketServerHandler::onConnectionOpened",
            15000, AlwaysTruePred(), h, coEvt));
         ASSERT_TRUE(coEvt.connection > 0);
      }

      wsServer->stop();
      ASSERT_TRUE(cpcExpectEvent(events, "WebSocketServerHandler::onServerStateChange",
         15000, AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.currentState, ServerState_Stopped);
   });

   std::vector<WebSocketSecureClient*> clients;
   auto clientEvents = std::async(std::launch::async, [&]()
   {
      for (int i = 0; i < numClients; i++)
      {
         std::stringstream name;
         name << "[wss_client_" << i << "]";
         clients.push_back(new WebSocketSecureClient(name.str(), true));
         ASSERT_TRUE(clients[i]->connect("wss://localhost:9003"));
      }
   });
   waitFor2(serverEvents, clientEvents);
   for (int i = 0; i < numClients; i++)
   {
      delete clients[i];
   }
}

TEST_F(WebSocketServerTest, WindowsNewlineNoHeaderFooterStringPem)
{
   WebSocketServerSettings settings;
   
   ASSERT_TRUE(fileToString(TestEnvironmentConfig::testResourcePath() + "CPcerts/electron/cert_no_header_footer.pem", "\r\n", settings.certificatePem));
   ASSERT_TRUE(fileToString(TestEnvironmentConfig::testResourcePath() + "CPcerts/electron/key_no_header_footer.pem", "\r\n", settings.privateKeyPem));
   settings.ciphers = CipherSuiteAdvanced;
   settings.url = "wss://localhost:9003";
   const int numClients = 10;
 

   WebSocketServerHandle h;
   ServerStateChangeEvent evt;
   wsServer->start(&settings);
   ASSERT_TRUE(cpcExpectEvent(events, "WebSocketServerHandler::onServerStateChange",
      15000, AlwaysTruePred(), h, evt));
   ASSERT_EQ(evt.currentState, ServerState_Running);

   auto serverEvents = std::async(std::launch::async, [&]()
   {
      for (int i = 0; i < numClients; i++)
      {
         ConnectionOpenedEvent coEvt;
         ASSERT_TRUE(cpcExpectEvent(events, "WebSocketServerHandler::onConnectionOpened",
            15000, AlwaysTruePred(), h, coEvt));
         ASSERT_TRUE(coEvt.connection > 0);
      }

      wsServer->stop();
      ASSERT_TRUE(cpcExpectEvent(events, "WebSocketServerHandler::onServerStateChange",
         15000, AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.currentState, ServerState_Stopped);
   });

   std::vector<WebSocketSecureClient*> clients;
   auto clientEvents = std::async(std::launch::async, [&]()
   {
      for (int i = 0; i < numClients; i++)
      {
         std::stringstream name;
         name << "[wss_client_" << i << "]";
         clients.push_back(new WebSocketSecureClient(name.str(), true));
         ASSERT_TRUE(clients[i]->connect("wss://localhost:9003"));
      }
   });
   waitFor2(serverEvents, clientEvents);
   for (int i = 0; i < numClients; i++)
   {
      delete clients[i];
   }
}

TEST_F(WebSocketServerTest, UnixNewlineNoHeaderFooterStringPem)
{
   WebSocketServerSettings settings;
   
   ASSERT_TRUE(fileToString(TestEnvironmentConfig::testResourcePath() + "CPcerts/electron/cert_no_header_footer.pem", "\n", settings.certificatePem));
   ASSERT_TRUE(fileToString(TestEnvironmentConfig::testResourcePath() + "CPcerts/electron/key_no_header_footer.pem", "\n", settings.privateKeyPem));
   settings.ciphers = CipherSuiteAdvanced;
   settings.url = "wss://localhost:9003";
   const int numClients = 10;
 

   WebSocketServerHandle h;
   ServerStateChangeEvent evt;
   wsServer->start(&settings);
   ASSERT_TRUE(cpcExpectEvent(events, "WebSocketServerHandler::onServerStateChange",
      15000, AlwaysTruePred(), h, evt));
   ASSERT_EQ(evt.currentState, ServerState_Running);

   auto serverEvents = std::async(std::launch::async, [&]()
   {
      for (int i = 0; i < numClients; i++)
      {
         ConnectionOpenedEvent coEvt;
         ASSERT_TRUE(cpcExpectEvent(events, "WebSocketServerHandler::onConnectionOpened",
            15000, AlwaysTruePred(), h, coEvt));
         ASSERT_TRUE(coEvt.connection > 0);
      }

      wsServer->stop();
      ASSERT_TRUE(cpcExpectEvent(events, "WebSocketServerHandler::onServerStateChange",
         15000, AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.currentState, ServerState_Stopped);
   });

   std::vector<WebSocketSecureClient*> clients;
   auto clientEvents = std::async(std::launch::async, [&]()
   {
      for (int i = 0; i < numClients; i++)
      {
         std::stringstream name;
         name << "[wss_client_" << i << "]";
         clients.push_back(new WebSocketSecureClient(name.str(), true));
         ASSERT_TRUE(clients[i]->connect("wss://localhost:9003"));
      }
   });
   waitFor2(serverEvents, clientEvents);
   for (int i = 0; i < numClients; i++)
   {
      delete clients[i];
   }
}

class WebSocketServerTest_NoInit : public CpcapiAutoTest
{
public:
};

TEST_F(WebSocketServerTest_NoInit, DISABLED_RemoveHandler)
{
   class MyWssHandler : public WebSocketServerHandler
   {
      virtual int onConnectionOpened(WebSocketServerHandle handle, const ConnectionOpenedEvent& evt) { return kSuccess; }
      virtual int onConnectionClosed(WebSocketServerHandle handle, const ConnectionClosedEvent& evt) { return kSuccess; }
      virtual int onMessageReceived(WebSocketServerHandle handle, const MessageReceivedEvent& evt) { return kSuccess; }
      virtual int onServerStateChange(WebSocketServerHandle handle, const ServerStateChangeEvent& evt) { return kSuccess; }
      virtual int onError(WebSocketServerHandle handle, const ErrorEvent& evt) { return kSuccess; }
   };

   std::srand(std::time(0));
   int serverPort = 32000 + (std::rand() % 1001);

   for (int i = 0; i < 20000; ++i)
   {
      Phone* phone = Phone::create();
      phone->setLoggingEnabled(&AutoTestsLogger::instance(), true);
      LicenseInfo licenseInfo;
      ConnectionPreferences conPref;
      dynamic_cast<PhoneInternal*>(phone)->initialize(licenseInfo, NULL, conPref);

      MyWssHandler h;

      CPCAPI2::WebSocketServer::WebSocketServerManager* wsServer = WebSocketServerManager::getInterface(phone);
      wsServer->setHandler(&h);
      wsServer->start();
      std::this_thread::sleep_for(std::chrono::milliseconds(std::rand() % 10));
      wsServer->setHandler(NULL);

      safeCout("Iteration " << i << " complete");

      Phone::release(phone);
   }

}

}  // namespace

#endif // CPCAPI2_BRAND_WEBSOCKET_SERVER_MODULE
