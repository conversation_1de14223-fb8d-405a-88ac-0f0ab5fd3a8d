#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#include "cpcapi2_test_fixture.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>

#include "test_framework/cpcapi2_test_framework.h"
#include "test_framework/bare_bones_sip_endpoint.h"
#include "test_call_events.h"

#include <thread>
#include <future>

using namespace CPCAPI2;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::SipConversation;
using namespace CPCAPI2::Media;
using namespace CPCAPI2::test;

namespace {


class CallStateTests : public CpcapiAutoTest
{
public:
   CallStateTests() {}
   virtual ~CallStateTests() {}
};

TEST_F(CallStateTests, BasicCallState) {
   const int call_duration_ms = 10000;

   TestAccount alice("alice");
   TestAccount bob("bob");

   // make an outgoing (audio only) call from <PERSON> to <PERSON> using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
   alice.conversation->start(aliceCall);

   // spawn a thread for Bob via std::async; both Bob and Alice will have events firing
   // at about the same time, and the ordering of those events is only interesting on a
   // per-person basis -- so <PERSON> gets his own thread, and Alice gets her own thread

   // Overview of Bob's thread:
   //  - wait for onNewConversation (triggered when Bob gets the incoming INVITE)
   //  - send 180 ringing
   //  - wait for onConversationStateChanged (LocalRinging)
   //  - answer the call (200 OK)
   //  - wait for onConversationMediaChanged (Audio -> SendReceive)
   //  - wait for onConversationStateChanged (Connected)
   //  - end the call (BYE)
   //  - wait for onConversationEnded
   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall = 0;
      SipConversationState prevState;
      {
         SipConversationHandle h;
         NewConversationEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
            "SipConversationHandler::onNewConversation",
            15000,
            AlwaysTruePred(),
            h, evt));
         bobCall = h;
         ASSERT_EQ(evt.account, bob.handle);
         ASSERT_EQ(evt.conversationState, ConversationState_RemoteOriginated);
         ASSERT_EQ(evt.conversationType, ConversationType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.uri());
         ASSERT_EQ(evt.remoteDisplayName, alice.config.name);
         ASSERT_EQ(evt.remoteMediaInfo.size(), 2);
         SipConversationState currState;
         ASSERT_EQ(bob.conversationState->getState(bobCall, currState), kSuccess);
         ASSERT_EQ(currState.account, evt.account);
         ASSERT_EQ(currState.conversationState, evt.conversationState);
         ASSERT_EQ(currState.conversationType, evt.conversationType);
         ASSERT_EQ(currState.localHold, false);
         ASSERT_EQ(currState.localMediaInfo.size(), 0);
         ASSERT_EQ(currState.remoteAddress, evt.remoteAddress);
         ASSERT_EQ(currState.remoteDisplayName, evt.remoteDisplayName);
         ASSERT_EQ(currState.remoteHold, false);
         ASSERT_EQ(currState.remoteMediaInfo.size(), 2);
         prevState = currState;
      }

      // Bob sends 180 ringing -- this should trigger state transitions for both Bob and Alice
      ASSERT_EQ(bob.conversation->sendRingingResponse(bobCall), kSuccess);

      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
            "SipConversationHandler::onConversationStateChanged",
            15000,
            HandleEqualsPred<SipConversationHandle>(bobCall),
            h, evt));
         ASSERT_EQ(evt.conversationState, ConversationState_LocalRinging);
         SipConversationState currState;
         ASSERT_EQ(bob.conversationState->getState(bobCall, currState), kSuccess);
         ASSERT_EQ(currState.account, prevState.account);
         ASSERT_EQ(currState.conversationState, evt.conversationState);
         ASSERT_EQ(currState.conversationType, prevState.conversationType);
         ASSERT_EQ(currState.localHold, prevState.localHold);
         ASSERT_EQ(currState.localMediaInfo.size(), 0);
         ASSERT_EQ(currState.remoteAddress, prevState.remoteAddress);
         ASSERT_EQ(currState.remoteDisplayName, prevState.remoteDisplayName);
         ASSERT_EQ(currState.remoteHold, prevState.remoteHold);
         ASSERT_EQ(currState.remoteMediaInfo.size(), 2);
         prevState = currState;
      }

      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      ASSERT_EQ(bob.conversation->accept(bobCall), kSuccess);

      {
         SipConversationHandle h;
         ConversationMediaChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
            "SipConversationHandler::onConversationMediaChanged",
            15000,
            HandleEqualsPred<SipConversationHandle>(bobCall),
            h, evt));
         ASSERT_EQ(evt.localMediaInfo.size(), 2);
         ASSERT_EQ(evt.localMediaInfo[0].mediaType, MediaType_Audio);
         ASSERT_EQ(evt.localMediaInfo[0].mediaDirection, MediaDirection_SendReceive);
         std::string strLocalPlname(evt.localMediaInfo[0].audioCodec.plname);
         //ASSERT_EQ(strLocalPlname, "opus");
         std::string strRemotePlname(evt.remoteMediaInfo[0].audioCodec.plname);
         //ASSERT_EQ(strRemotePlname, "opus");
         SipConversationState currState;
         ASSERT_EQ(bob.conversationState->getState(bobCall, currState), kSuccess);
         ASSERT_EQ(currState.account, prevState.account);
         ASSERT_EQ(currState.conversationState, prevState.conversationState);
         ASSERT_EQ(currState.conversationType, prevState.conversationType);
         ASSERT_EQ(currState.localHold, evt.localHold);
         ASSERT_EQ(currState.localMediaInfo.size(), evt.localMediaInfo.size());
         ASSERT_TRUE(currState.localMediaInfo[1].mediaType == MediaType_Video);
         ASSERT_TRUE(strlen(currState.localMediaInfo[1].videoCodec.plName) > 0);
         ASSERT_EQ(currState.remoteAddress, prevState.remoteAddress);
         ASSERT_EQ(currState.remoteDisplayName, prevState.remoteDisplayName);
         ASSERT_EQ(currState.remoteHold, evt.remoteHold);
         ASSERT_EQ(currState.remoteMediaInfo.size(), evt.remoteMediaInfo.size());
         ASSERT_TRUE(currState.remoteMediaInfo[1].mediaType == MediaType_Video);
         ASSERT_TRUE(strlen(currState.remoteMediaInfo[1].videoCodec.plName) > 0);
         prevState = currState;
      }

      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
            "SipConversationHandler::onConversationStateChanged",
            15000,
            HandleEqualsPred<SipConversationHandle>(bobCall),
            h, evt));
         ASSERT_EQ(evt.conversationState, ConversationState_Connected);
         SipConversationState currState;
         ASSERT_EQ(bob.conversationState->getState(bobCall, currState), kSuccess);
         ASSERT_EQ(currState.account, prevState.account);
         ASSERT_EQ(currState.conversationState, evt.conversationState);
         ASSERT_EQ(currState.conversationType, prevState.conversationType);
         ASSERT_EQ(currState.localHold, prevState.localHold);
         ASSERT_EQ(currState.localMediaInfo.size(), prevState.localMediaInfo.size());
         ASSERT_TRUE(currState.localMediaInfo[1].mediaType == MediaType_Video);
         ASSERT_TRUE(strlen(currState.localMediaInfo[1].videoCodec.plName) > 0);
         std::cout << "local video codec (bob): " << currState.localMediaInfo[1].videoCodec.plName << std::endl;
         ASSERT_EQ(currState.remoteAddress, prevState.remoteAddress);
         ASSERT_EQ(currState.remoteDisplayName, prevState.remoteDisplayName);
         ASSERT_EQ(currState.remoteHold, prevState.remoteHold);
         ASSERT_EQ(currState.remoteMediaInfo.size(), prevState.remoteMediaInfo.size());
         ASSERT_TRUE(currState.remoteMediaInfo[1].mediaType == MediaType_Video);
         ASSERT_TRUE(strlen(currState.remoteMediaInfo[1].videoCodec.plName) > 0);
         std::cout << "remote video codec (bob): " << currState.remoteMediaInfo[1].videoCodec.plName << std::endl;
         prevState = currState;
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(call_duration_ms));

      ASSERT_EQ(bob.conversation->end(bobCall), kSuccess);

      {
         SipConversationHandle h;
         ConversationEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
            "SipConversationHandler::onConversationEnded",
            15000,
            HandleEqualsPred<SipConversationHandle>(bobCall),
            h, evt));
         ASSERT_EQ(evt.endReason, ConversationEndReason_UserTerminatedLocally);
         SipConversationState currState;
         ASSERT_EQ(bob.conversationState->getState(bobCall, currState), kSuccess);
         ASSERT_EQ(currState.account, prevState.account);
         ASSERT_EQ(currState.conversationState, evt.conversationState);
         ASSERT_EQ(currState.conversationType, prevState.conversationType);
         ASSERT_EQ(currState.endReason, evt.endReason);
         ASSERT_EQ(currState.localHold, prevState.localHold);
         ASSERT_EQ(currState.localMediaInfo.size(), 0);
         ASSERT_EQ(currState.remoteAddress, prevState.remoteAddress);
         ASSERT_EQ(currState.remoteDisplayName, prevState.remoteDisplayName);
         ASSERT_EQ(currState.remoteHold, prevState.remoteHold);
         ASSERT_EQ(currState.remoteMediaInfo.size(), 0);
         prevState = currState;
      }
   });

   // Overview of Alice's thread:
   //  - wait for onNewConversation (triggered as soon as Alice sends the INVITE)
   //  - wait for onConversationStateChanged (RemoteRinging) (triggered when Alice receives the 180 from Bob)
   //  - wait for onConversationMediaChanged (Audio -> SendReceive) (triggered when Alice receives the 200 OK from Bob)
   //  - wait for onConversationStateChanged (Connected) (also triggered when Alice receives the 200 OK from Bob)
   //  - wait for onConversationEnded (triggered when Alice receives the BYE from Bob)
   auto aliceEvents = std::async(std::launch::async, [&] () {
      SipConversationState prevState;

      {
         SipConversationHandle h;
         NewConversationEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
            "SipConversationHandler::onNewConversation",
            15000,
            HandleEqualsPred<SipConversationHandle>(aliceCall),
            h, evt));
         ASSERT_EQ(evt.account, alice.handle);
         ASSERT_EQ(evt.conversationType, ConversationType_Outgoing);
         ASSERT_EQ(evt.remoteAddress, bob.config.uri());
         ASSERT_EQ(evt.remoteDisplayName, "");
         ASSERT_EQ(evt.localMediaInfo.size(), 2);
         SipConversationState currState;
         ASSERT_EQ(alice.conversationState->getState(aliceCall, currState), kSuccess);
         ASSERT_EQ(currState.account, evt.account);
         ASSERT_EQ(currState.conversationState, evt.conversationState);
         ASSERT_EQ(currState.conversationType, evt.conversationType);
         ASSERT_EQ(currState.localHold, false);
         ASSERT_EQ(currState.localMediaInfo.size(), 2);
         ASSERT_EQ(currState.remoteAddress, evt.remoteAddress);
         ASSERT_EQ(evt.remoteDisplayName, "");
         ASSERT_EQ(currState.remoteDisplayName, evt.remoteDisplayName);
         ASSERT_EQ(currState.remoteHold, false);
         ASSERT_EQ(currState.remoteMediaInfo.size(), 0);
         prevState = currState;
      }

      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
            "SipConversationHandler::onConversationStateChanged",
            15000,
            HandleEqualsPred<SipConversationHandle>(aliceCall),
            h, evt));
         ASSERT_EQ(evt.conversationState, ConversationState_RemoteRinging);
         SipConversationState currState;
         ASSERT_EQ(alice.conversationState->getState(aliceCall, currState), kSuccess);
         ASSERT_EQ(currState.account, prevState.account);
         ASSERT_EQ(currState.conversationState, evt.conversationState);
         ASSERT_EQ(currState.conversationType, prevState.conversationType);
         ASSERT_EQ(currState.localHold, false);
         ASSERT_EQ(currState.localMediaInfo.size(), 2);
         ASSERT_EQ(currState.remoteAddress, prevState.remoteAddress);
         ASSERT_GT(currState.remoteDisplayName.size(), 0); // we should have a display name at this point
         ASSERT_EQ(currState.remoteDisplayName, evt.remoteDisplayName);
         ASSERT_EQ(currState.remoteHold, false);
         ASSERT_EQ(currState.remoteMediaInfo.size(), 0);
         prevState = currState;
      }

      {
         SipConversationHandle h;
         ConversationMediaChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
            "SipConversationHandler::onConversationMediaChanged",
            15000,
            HandleEqualsPred<SipConversationHandle>(aliceCall),
            h, evt));
         ASSERT_EQ(evt.localMediaInfo.size(), 2);
         ASSERT_EQ(evt.localMediaInfo[0].mediaType, MediaType_Audio);
         ASSERT_EQ(evt.localMediaInfo[0].mediaDirection, MediaDirection_SendReceive);
         SipConversationState currState;
         ASSERT_EQ(alice.conversationState->getState(aliceCall, currState), kSuccess);
         ASSERT_EQ(currState.account, prevState.account);
         ASSERT_EQ(currState.conversationState, prevState.conversationState);
         ASSERT_EQ(currState.conversationType, prevState.conversationType);
         ASSERT_EQ(currState.localHold, evt.localHold);
         ASSERT_EQ(currState.localMediaInfo.size(), evt.localMediaInfo.size());
         ASSERT_TRUE(currState.localMediaInfo[1].mediaType == MediaType_Video);
         ASSERT_TRUE(strlen(currState.localMediaInfo[1].videoCodec.plName) > 0);
         ASSERT_EQ(currState.remoteAddress, prevState.remoteAddress);
         ASSERT_EQ(currState.remoteDisplayName, prevState.remoteDisplayName);
         ASSERT_EQ(currState.remoteHold, evt.remoteHold);
         ASSERT_EQ(currState.remoteMediaInfo.size(), evt.remoteMediaInfo.size());
         ASSERT_TRUE(currState.remoteMediaInfo[1].mediaType == MediaType_Video);
         ASSERT_TRUE(strlen(currState.remoteMediaInfo[1].videoCodec.plName) > 0);
         prevState = currState;
      }

      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
            "SipConversationHandler::onConversationStateChanged",
            15000,
            HandleEqualsPred<SipConversationHandle>(aliceCall),
            h, evt));
         ASSERT_EQ(evt.conversationState, ConversationState_Connected);
         SipConversationState currState;
         ASSERT_EQ(alice.conversationState->getState(aliceCall, currState), kSuccess);
         ASSERT_EQ(currState.account, prevState.account);
         ASSERT_EQ(currState.conversationState, evt.conversationState);
         ASSERT_EQ(currState.conversationType, prevState.conversationType);
         ASSERT_EQ(currState.localHold, false);
         ASSERT_EQ(currState.localMediaInfo.size(), 2);
         ASSERT_TRUE(currState.localMediaInfo[1].mediaType == MediaType_Video);
         ASSERT_TRUE(strlen(currState.localMediaInfo[1].videoCodec.plName) > 0);
         std::cout << "local video codec (alice): " << currState.localMediaInfo[1].videoCodec.plName << std::endl;
         ASSERT_EQ(currState.remoteAddress, prevState.remoteAddress);
         ASSERT_EQ(currState.remoteDisplayName, prevState.remoteDisplayName);
         ASSERT_EQ(currState.remoteHold, false);
         ASSERT_EQ(currState.remoteMediaInfo.size(), 2);
         ASSERT_TRUE(currState.remoteMediaInfo[1].mediaType == MediaType_Video);
         ASSERT_TRUE(strlen(currState.remoteMediaInfo[1].videoCodec.plName) > 0);
         std::cout << "remote video codec (alice): " << currState.remoteMediaInfo[1].videoCodec.plName << std::endl;
         prevState = currState;
      }

      {
         SipConversationHandle h;
         ConversationEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
            "SipConversationHandler::onConversationEnded",
            call_duration_ms + 1000,
            HandleEqualsPred<SipConversationHandle>(aliceCall),
            h, evt));
         ASSERT_EQ(evt.endReason, ConversationEndReason_UserTerminatedRemotely);
         SipConversationState currState;
         ASSERT_EQ(alice.conversationState->getState(aliceCall, currState), kSuccess);
         ASSERT_EQ(currState.account, prevState.account);
         ASSERT_EQ(currState.conversationState, evt.conversationState);
         ASSERT_EQ(currState.conversationType, prevState.conversationType);
         ASSERT_EQ(currState.endReason, evt.endReason);
         ASSERT_EQ(currState.localHold, false);
         ASSERT_EQ(currState.localMediaInfo.size(), 0);
         ASSERT_EQ(currState.remoteAddress, prevState.remoteAddress);
         ASSERT_EQ(currState.remoteDisplayName, prevState.remoteDisplayName);
         ASSERT_EQ(currState.remoteHold, false);
         ASSERT_EQ(currState.remoteMediaInfo.size(), 0);
         prevState = currState;
      }
   });

   waitFor2(bobEvents, aliceEvents);
}

TEST_F(CallStateTests, VerifyCallStateActiveCount)
{
   TestAccount alice("alice");
   TestAccount bob("bob");

   int totalCalls = 3;
   SipConversationHandle activeAliceCall = 0;
   SipConversationHandle activeBobCall = 0;

   for (int index = 0; index < totalCalls; index++)
   {
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->addParticipant(aliceCall, bob.config.uri());
      alice.conversation->start(aliceCall);

      auto aliceEvents = std::async(std::launch::async, [&] ()
      {
         assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
         assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt)
         {
            ASSERT_FALSE(evt.localHold);
            ASSERT_FALSE(evt.remoteHold);
            ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         });
         assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
         assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      });

      auto bobEvents = std::async(std::launch::async, [&] ()
      {
         SipConversationHandle bobCall = 0;
         assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
         assertSuccess(bob.conversation->accept(bobCall));
         assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt)
         {
            ASSERT_FALSE(evt.localHold);
            ASSERT_FALSE(evt.remoteHold);
            ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         });
         assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

         std::this_thread::sleep_for(std::chrono::milliseconds(2000));

         assertSuccess(bob.conversation->end(bobCall));
         assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      });

      waitFor2(aliceEvents, bobEvents);
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   cpc::vector<SipConversationState> aliceConversations;
   alice.conversationState->getStateAllConversations(aliceConversations);
   ASSERT_EQ(aliceConversations.size(), totalCalls);
   int aliceEndedCallCount = 0;
   for (cpc::vector<SipConversationState>::iterator i = aliceConversations.begin(); i != aliceConversations.end(); i++)
   {
      SipConversationState conversationState = (*i);
      safeCout("CallStateTests::VerifyCallStateActiveCount():" << __LINE__ << " *** alice *** conversation " << conversationState.conversation << " in state " << conversationState.conversationState)
      if (conversationState.conversationState == ConversationState::ConversationState_Ended)
      {
         aliceEndedCallCount++;
      }
   }
   ASSERT_EQ(aliceEndedCallCount, totalCalls) << " ended call count does not match: totalCalls: " << totalCalls;

   cpc::vector<SipConversationState> bobConversations;
   bob.conversationState->getStateAllConversations(bobConversations);
   ASSERT_EQ(bobConversations.size(), totalCalls);
   int bobEndedCallCount = 0;
   for (cpc::vector<SipConversationState>::iterator i = bobConversations.begin(); i != bobConversations.end(); i++)
   {
      SipConversationState conversationState = (*i);
      safeCout("CallStateTests::VerifyCallStateActiveCount():" << __LINE__ << " *** bob *** conversation " << conversationState.conversation << " in state " << conversationState.conversationState)
      if (conversationState.conversationState == ConversationState::ConversationState_Ended)
      {
         bobEndedCallCount++;
      }
   }
   ASSERT_EQ(bobEndedCallCount, totalCalls) << " ended call count does not match: totalCalls: " << totalCalls;

   {
      totalCalls++;
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->addParticipant(aliceCall, bob.config.uri());
      alice.conversation->start(aliceCall);

      auto aliceEvents = std::async(std::launch::async, [&] ()
      {
         assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
         assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt)
         {
            ASSERT_FALSE(evt.localHold);
            ASSERT_FALSE(evt.remoteHold);
            ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         });
         assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
         activeAliceCall = aliceCall;

         std::this_thread::sleep_for(std::chrono::milliseconds(2000));

         aliceConversations.clear();
         alice.conversationState->getStateAllConversations(aliceConversations);
         ASSERT_EQ(aliceConversations.size(), totalCalls);
         aliceEndedCallCount = 0;
         for (cpc::vector<SipConversationState>::iterator i = aliceConversations.begin(); i != aliceConversations.end(); i++)
         {
            SipConversationState conversationState = (*i);
            safeCout("CallStateTests::VerifyCallStateActiveCount():" << __LINE__ << " *** alice *** conversation " << conversationState.conversation << " in state " << conversationState.conversationState)
            if (conversationState.conversationState == ConversationState::ConversationState_Ended)
            {
               aliceEndedCallCount++;
            }
         }
         ASSERT_EQ(aliceEndedCallCount, totalCalls - 1) << " ended call count does not match: totalCalls: " << totalCalls;

         assertConversationEnded(alice, activeAliceCall, ConversationEndReason_UserTerminatedRemotely);
      });

      auto bobEvents = std::async(std::launch::async, [&] ()
      {
         SipConversationHandle bobCall = 0;
         assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
         assertSuccess(bob.conversation->accept(bobCall));
         assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt)
         {
            ASSERT_FALSE(evt.localHold);
            ASSERT_FALSE(evt.remoteHold);
            ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         });
         assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
         activeBobCall = bobCall;

         std::this_thread::sleep_for(std::chrono::milliseconds(2000));

         bobConversations.clear();
         bob.conversationState->getStateAllConversations(bobConversations);
         ASSERT_EQ(bobConversations.size(), totalCalls);
         bobEndedCallCount = 0;
         for (cpc::vector<SipConversationState>::iterator i = bobConversations.begin(); i != bobConversations.end(); i++)
         {
            SipConversationState conversationState = (*i);
            safeCout("CallStateTests::VerifyCallStateActiveCount():" << __LINE__ << " *** bob *** conversation " << conversationState.conversation << " in state " << conversationState.conversationState)
            if (conversationState.conversationState == ConversationState::ConversationState_Ended)
            {
               bobEndedCallCount++;
            }
         }
         ASSERT_EQ(bobEndedCallCount, totalCalls - 1);

         assertSuccess(bob.conversation->end(activeBobCall));
         assertConversationEnded(bob, activeBobCall, ConversationEndReason_UserTerminatedLocally);
      });

      waitFor2(aliceEvents, bobEvents);
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   aliceConversations.clear();
   alice.conversationState->getStateAllConversations(aliceConversations);
   ASSERT_EQ(aliceConversations.size(), totalCalls);
   aliceEndedCallCount = 0;
   for (cpc::vector<SipConversationState>::iterator i = aliceConversations.begin(); i != aliceConversations.end(); i++)
   {
      SipConversationState conversationState = (*i);
      safeCout("CallStateTests::VerifyCallStateActiveCount():" << __LINE__ << " *** alice *** conversation " << conversationState.conversation << " in state " << conversationState.conversationState)
      if (conversationState.conversationState == ConversationState::ConversationState_Ended)
      {
         aliceEndedCallCount++;
      }
   }
   ASSERT_EQ(aliceEndedCallCount, totalCalls) << " ended call count does not match: totalCalls: " << totalCalls;

   bobConversations.clear();
   bob.conversationState->getStateAllConversations(bobConversations);
   ASSERT_EQ(bobConversations.size(), totalCalls);
   bobEndedCallCount = 0;
   for (cpc::vector<SipConversationState>::iterator i = bobConversations.begin(); i != bobConversations.end(); i++)
   {
      SipConversationState conversationState = (*i);
      safeCout("CallStateTests::VerifyCallStateActiveCount():" << __LINE__ << " *** bob *** conversation " << conversationState.conversation << " in state " << conversationState.conversationState)
      if (conversationState.conversationState == ConversationState::ConversationState_Ended)
      {
         bobEndedCallCount++;
      }
   }
   ASSERT_EQ(bobEndedCallCount, totalCalls);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
}

TEST_F(CallStateTests, VerifyCallStateActiveCountWithMultipleActiveCalls)
{
   TestAccount alice("alice");
   TestAccount bob("bob");

   int totalCalls = 3;
   int aliceActiveCalls = 0;
   int bobActiveCalls = 0;
   cpc::vector<SipConversationState> aliceConversations;
   int aliceEndedCallCount = 0;
   cpc::vector<SipConversationState> bobConversations;
   int bobEndedCallCount = 0;

   for (int index = 0; index < totalCalls; index++)
   {
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->addParticipant(aliceCall, bob.config.uri());
      alice.conversation->start(aliceCall);

      auto aliceEvents = std::async(std::launch::async, [&] ()
      {
         assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
         assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt)
         {
            ASSERT_FALSE(evt.localHold);
            ASSERT_FALSE(evt.remoteHold);
            ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         });
         assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

         if (index == (totalCalls - 1))
         {
            aliceActiveCalls++;

            std::this_thread::sleep_for(std::chrono::milliseconds(2000));

            alice.conversationState->getStateAllConversations(aliceConversations);
            ASSERT_EQ(aliceConversations.size(), totalCalls);
            aliceEndedCallCount = 0;
            for (cpc::vector<SipConversationState>::iterator i = aliceConversations.begin(); i != aliceConversations.end(); i++)
            {
               SipConversationState conversationState = (*i);
               safeCout("CallStateTests::VerifyCallStateActiveCountWithMultipleActiveCalls():" << __LINE__ << " *** alice *** conversation " << conversationState.conversation << " in state " << conversationState.conversationState)
               if (conversationState.conversationState == ConversationState::ConversationState_Ended)
               {
                  aliceEndedCallCount++;
               }
            }
            ASSERT_EQ(aliceEndedCallCount, totalCalls - aliceActiveCalls) << " ended call count does not match: totalCalls: " << totalCalls;

            for (cpc::vector<SipConversationState>::iterator i = aliceConversations.begin(); i != aliceConversations.end(); i++)
            {
               SipConversationState conversationState = (*i);
               safeCout("CallStateTests::VerifyCallStateActiveCountWithMultipleActiveCalls():" << __LINE__ << " *** alice *** conversation " << conversationState.conversation << " in state " << conversationState.conversationState)
               if (conversationState.conversationState != ConversationState::ConversationState_Ended)
               {
                  assertConversationEnded(alice, conversationState.conversation, ConversationEndReason_UserTerminatedRemotely);
               }
            }

            // Adding wait time to ensure that bob has ended all the calls
            std::this_thread::sleep_for(std::chrono::milliseconds(5000));

            aliceConversations.clear();
            aliceEndedCallCount = 0;
            alice.conversationState->getStateAllConversations(aliceConversations);
            ASSERT_EQ(aliceConversations.size(), totalCalls);
            for (cpc::vector<SipConversationState>::iterator i = aliceConversations.begin(); i != aliceConversations.end(); i++)
            {
               SipConversationState conversationState = (*i);
               safeCout("CallStateTests::VerifyCallStateActiveCountWithMultipleActiveCalls():" << __LINE__ << " *** alice *** conversation " << conversationState.conversation << " in state " << conversationState.conversationState)
               if (conversationState.conversationState == ConversationState::ConversationState_Ended)
               {
                  aliceEndedCallCount++;
               }
            }
            ASSERT_EQ(aliceEndedCallCount, totalCalls) << " ended call count does not match: totalCalls: " << totalCalls;
         }
         else
         {
            assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendOnly);
            assertSuccess(alice.conversation->accept(aliceCall));
            assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt)
            {
               ASSERT_FALSE(evt.localHold);
               ASSERT_TRUE(evt.remoteHold);
               ASSERT_EQ(MediaDirection_ReceiveOnly, evt.localMediaInfo[0].mediaDirection);
            });

            std::this_thread::sleep_for(std::chrono::milliseconds(1000));

            assertSuccess(alice.conversation->hold(aliceCall));
            assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt)
            {
               ASSERT_TRUE(evt.localHold);
               ASSERT_TRUE(evt.remoteHold);
               ASSERT_EQ(MediaDirection_Inactive, evt.localMediaInfo[0].mediaDirection);
            });

            if (index % 2 == 0)
            {
               assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
            }
            else
            {
               aliceActiveCalls++;
            }
         }
      });

      auto bobEvents = std::async(std::launch::async, [&] ()
      {
         SipConversationHandle bobCall = 0;
         assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
         assertSuccess(bob.conversation->accept(bobCall));
         assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt)
         {
            ASSERT_FALSE(evt.localHold);
            ASSERT_FALSE(evt.remoteHold);
            ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         });
         assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

         if (index == (totalCalls - 1))
         {
            bobActiveCalls++;

            bob.conversationState->getStateAllConversations(bobConversations);
            ASSERT_EQ(bobConversations.size(), totalCalls);
            for (cpc::vector<SipConversationState>::iterator i = bobConversations.begin(); i != bobConversations.end(); i++)
            {
               SipConversationState conversationState = (*i);
               safeCout("CallStateTests::VerifyCallStateActiveCountWithMultipleActiveCalls():" << __LINE__ << " *** bob *** conversation " << conversationState.conversation << " in state " << conversationState.conversationState)
               if (conversationState.conversationState == ConversationState::ConversationState_Ended)
               {
                  bobEndedCallCount++;
               }
            }
            ASSERT_EQ(bobEndedCallCount, totalCalls - bobActiveCalls) << " ended call count does not match: totalCalls: " << totalCalls;

            std::this_thread::sleep_for(std::chrono::milliseconds(2000));

            for (cpc::vector<SipConversationState>::iterator i = bobConversations.begin(); i != bobConversations.end(); i++)
            {
               SipConversationState conversationState = (*i);
               if (conversationState.conversationState != ConversationState::ConversationState_Ended)
               {
                  safeCout("CallStateTests::VerifyCallStateActiveCountWithMultipleActiveCalls():" << __LINE__ << " *** bob *** ending conversation " << conversationState.conversation << " in state " << conversationState.conversationState)
                  assertSuccess(bob.conversation->end(conversationState.conversation));
                  assertConversationEnded(bob, conversationState.conversation, ConversationEndReason_UserTerminatedLocally);
               }
            }

            std::this_thread::sleep_for(std::chrono::milliseconds(2000));

            bobConversations.clear();
            bobEndedCallCount = 0;
            bob.conversationState->getStateAllConversations(bobConversations);
            ASSERT_EQ(bobConversations.size(), totalCalls);
            for (cpc::vector<SipConversationState>::iterator i = bobConversations.begin(); i != bobConversations.end(); i++)
            {
               SipConversationState conversationState = (*i);
               safeCout("CallStateTests::VerifyCallStateActiveCountWithMultipleActiveCalls():" << __LINE__ << " *** bob *** conversation " << conversationState.conversation << " in state " << conversationState.conversationState)
               if (conversationState.conversationState == ConversationState::ConversationState_Ended)
               {
                  bobEndedCallCount++;
               }
            }
            ASSERT_EQ(bobEndedCallCount, totalCalls) << " ended call count does not match: totalCalls: " << totalCalls;
         }
         else
         {
            assertSuccess(bob.conversation->hold(bobCall));
            assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt)
            {
               ASSERT_TRUE(evt.localHold);
               ASSERT_FALSE(evt.remoteHold);
               ASSERT_EQ(MediaDirection_SendOnly, evt.localMediaInfo[0].mediaDirection);
            });

            std::this_thread::sleep_for(std::chrono::milliseconds(1000));

            assertConversationMediaChangeRequest(bob, bobCall, MediaDirection_SendOnly);
            assertSuccess(bob.conversation->accept(bobCall));
            assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt)
            {
               ASSERT_TRUE(evt.localHold);
               ASSERT_TRUE(evt.remoteHold);
               ASSERT_EQ(MediaDirection_Inactive, evt.localMediaInfo[0].mediaDirection);
            });

            std::this_thread::sleep_for(std::chrono::milliseconds(1000));

            if (index % 2 == 0)
            {
               assertSuccess(bob.conversation->end(bobCall));
               assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
            }
            else
            {
               bobActiveCalls++;
            }
         }
      });

      waitFor2(aliceEvents, bobEvents);
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
}

}
