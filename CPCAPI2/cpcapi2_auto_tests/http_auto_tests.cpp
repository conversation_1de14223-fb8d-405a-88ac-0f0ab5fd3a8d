#include "cpcapi2_test_fixture.h"
#include "test_account_events.h"
#include "test_events.h"
#include "test_call_events.h"

#include "test_framework/http_test_framework.h"

#include <sstream>

#include "../../impl/util/HttpClient.h"



using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace std::chrono;

// CPCAPI2 (as of this commit) uses a 10 second hardcoded HTTP timeout.
// this may or may not change in the future; the main purpose of this
// definition is to make it clear where we are currently making this
// assumption
static const int CPCAPI2_HARD_CODED_HTTP_TIMEOUT_SEC = 10;



namespace
{

   // uses SimpleWebServer from https://github.com/eidheim/Simple-Web-Server

   class HttpModuleTest : public CpcapiAutoTest
   {
   public:
      HttpModuleTest() : mListenPort(9090), mServer() {
         mServer.config.port = mListenPort;
      }
      virtual ~HttpModuleTest() {}
      
      cpc::string serverBaseUrl() const;
      

      const int mListenPort;
      SimpleWeb::Server<SimpleWeb::HTTP> mServer;
   };
   
   cpc::string HttpModuleTest::serverBaseUrl() const
   {
      std::ostringstream ss;
      ss << "http://127.0.0.1:" << mListenPort;
      
      return ss.str().c_str();
   }
   
   std::string resourcePathRegex(const std::string& resourcePath)
   {
      std::stringstream ss;
      ss << resourcePath << "$";
      return ss.str();
   }

   TEST_F(HttpModuleTest, BasicPostRequest)
   {
      const std::string resourcePath("/test");
   
      // listens for requests at /test
      mServer.resource[resourcePathRegex(resourcePath)]["POST"]=[](std::shared_ptr<HttpServer::Response> response,
                                             std::shared_ptr<HttpServer::Request> request)
      {
         // get body contents, and echo it back in the response
         
         auto content=request->content.string();
         *response << "HTTP/1.1 200 OK\r\nContent-Length: " << content.length() << "\r\n\r\n" << content;
      };
    
      thread server_thread([this]()
      {
         mServer.start();
      });
    
      // the SimpleWeb example code demonstrates waiting for 1 second to let the HTTP server start.
      // unsure if there we could instead rely on an event..
      this_thread::sleep_for(seconds(1));
    
    
      HTTPClient httpClient;
      
      std::stringstream url;
      url << serverBaseUrl() << resourcePath;
      
      HTTPClient::RequestConfig requestConfig;
      requestConfig.messageBody = "test123";
      requestConfig.messageLengthInBytes = strlen(requestConfig.messageBody) * sizeof(requestConfig.messageBody[0]);
      requestConfig.verb = HTTPClient::EHTTPVerbPOST;
      
      HTTPClient::ResponseResult responseResult;
      httpClient.StartHTTPSession(url.str().c_str(), requestConfig, responseResult);
      
      ASSERT_EQ(responseResult.errorCode, 0);
      ASSERT_EQ(requestConfig.messageBody, responseResult.messageBody);

      mServer.stop();
      server_thread.join();
    
      return;
   }
   
   TEST_F(HttpModuleTest, RepeatGetRequest)
   {
      const std::string resourcePath("/repeatget");
   
      const std::string kTestContent = "jasdlfasd;jf;asdlkj";
   
      mServer.resource[resourcePathRegex(resourcePath)]["GET"]=[kTestContent](std::shared_ptr<HttpServer::Response> response,
                                             std::shared_ptr<HttpServer::Request> request)
      {
         // get body contents, and echo it back in the response
         
         *response << "HTTP/1.1 200 OK\r\nContent-Length: " << kTestContent.length() << "\r\n\r\n" << kTestContent;
      };
    
      thread server_thread([this]()
      {
         mServer.start();
      });
    
      // the SimpleWeb example code demonstrates waiting for 1 second to let the HTTP server start.
      // unsure if there we could instead rely on an event..
      this_thread::sleep_for(seconds(1));
    
    
      for (int i = 0; i < 50; ++i)
      {
         HTTPClient httpClient;
         
         std::stringstream url;
         url << serverBaseUrl() << resourcePath;
         
         HTTPClient::RequestConfig requestConfig;
         requestConfig.verb = HTTPClient::EHTTPVerbGET;
         
         HTTPClient::ResponseResult responseResult;
         httpClient.StartHTTPSession(url.str().c_str(), requestConfig, responseResult);
         
         ASSERT_EQ(responseResult.errorCode, 0);
         ASSERT_EQ(cpc::string(kTestContent.c_str()), responseResult.messageBody);
      }

      mServer.stop();
      server_thread.join();
    
      return;
   }

   TEST_F(HttpModuleTest, Redirect)
   {
      const std::string redirectPath("/redirect");
      const std::string redirectDest("/redirectdest");
      const std::string kTestContent = "some test content to validate";

      mServer.resource[resourcePathRegex(redirectPath)]["GET"]=[&](std::shared_ptr<HttpServer::Response> response,
                                                std::shared_ptr<HttpServer::Request> request)
      {
         *response << "HTTP/1.1 302 Moved Temporarily\r\nLocation: " << redirectDest << "\r\nContent-Length: " << kTestContent.length() << "\r\n\r\n" << kTestContent;
      };

      mServer.resource[resourcePathRegex(redirectDest)]["GET"]=[&](std::shared_ptr<HttpServer::Response> response,
                                                    std::shared_ptr<HttpServer::Request> request)
      {
         *response << "HTTP/1.1 200 OK\r\nContent-Length: " << kTestContent.length() << "\r\n\r\n" << kTestContent;
      };
    
      thread server_thread([this]()
      {
         mServer.start();
      });
    
      // the SimpleWeb example code demonstrates waiting for 1 second to let the HTTP server start.
      // unsure if there we could instead rely on an event..
      this_thread::sleep_for(seconds(1));    

      HTTPClient httpClient;
      
      std::stringstream url;
      url << serverBaseUrl() << redirectPath;
      
      HTTPClient::RequestConfig requestConfig;
      requestConfig.verb = HTTPClient::EHTTPVerbGET;
      
      HTTPClient::ResponseResult responseResult;
      httpClient.StartHTTPSession(url.str().c_str(), requestConfig, responseResult);
      
      ASSERT_EQ(responseResult.errorCode, 0);
      ASSERT_EQ(responseResult.redirectInfo.count, 1);
      ASSERT_EQ(cpc::string(kTestContent.c_str()), responseResult.messageBody);

      mServer.stop();
      server_thread.join();
   }
   
   TEST_F(HttpModuleTest, GetWithHeaders)
   {
      const std::string resourcePath("/getwithheaders");
   
      const std::string kTestContent = "jasdlfasd;jf;asdlkj";
   
      std::map<std::string, std::string> headers;
      headers["ETag"] = "";
      headers["X-Bogus-Header"] = "sdklfjsd";
      
   
      mServer.resource[resourcePathRegex(resourcePath)]["GET"]=[kTestContent, headers](std::shared_ptr<HttpServer::Response> response,
                                             std::shared_ptr<HttpServer::Request> request)
      {
         *response << "HTTP/1.1 200 OK\r\n";
         for (std::map<std::string, std::string>::const_iterator it = headers.begin(); it != headers.end(); ++it)
         {
            *response << it->first << ":";
            if (it->second.length() > 0)
            {
               *response << " " << it->second;
            }
            
            *response << "\r\n";
         }
         
         *response << "Content-Length: " << kTestContent.length() << "\r\n\r\n" << kTestContent;
      };
    
      thread server_thread([this]()
      {
         mServer.start();
      });
    
      // the SimpleWeb example code demonstrates waiting for 1 second to let the HTTP server start.
      // unsure if there we could instead rely on an event..
      this_thread::sleep_for(seconds(1));
    
      HTTPClient httpClient;
      
      std::stringstream url;
      url << serverBaseUrl() << resourcePath;
      
      HTTPClient::RequestConfig requestConfig;
      requestConfig.verb = HTTPClient::EHTTPVerbGET;
      
      HTTPClient::ResponseResult responseResult;
      httpClient.StartHTTPSession(url.str().c_str(), requestConfig, responseResult);
      
      ASSERT_EQ(responseResult.errorCode, 0);
      ASSERT_EQ(cpc::string(kTestContent.c_str()), responseResult.messageBody);
      
      // headers are currently not exposed, so we can't check them

      mServer.stop();
      server_thread.join();
    
      return;
   }
   
   TEST_F(HttpModuleTest, PostLargeBody)
   {
      const int bodySizeBytes = 1000 * 50;
      char* body = new char[bodySizeBytes];
      
      int locs[32];
      const int locsLen = sizeof(locs) / sizeof(locs[0]);
      // write values at particular points to compare later
      for (int i = 0; i < locsLen; ++i)
      {
         int writeLoc = (bodySizeBytes / locsLen) * i;
         locs[i] = writeLoc;
         body[writeLoc] = i;
      }
      
      const std::string resourcePath("/postfile");

      mServer.resource[resourcePathRegex(resourcePath)]["POST"]=[&body, &locs, locsLen](std::shared_ptr<HttpServer::Response> response,
                                                             std::shared_ptr<HttpServer::Request> request)
      {
         // get body contents, and compare it to what we submitted
         
         auto content=request->content.string();
         
         bool matches = true;
         for (int i = 0; i < locsLen; ++i)
         {
            if (locs[i] > content.length())
            {
               matches = false;
            }
         
            if (body[locs[i]] != content[locs[i]])
            {
               matches = false;
               break;
            }
         }
         
         if (matches)
         {
            *response << "HTTP/1.1 200 OK\r\nContent-Length: 0";
         }
         else
         {
            *response << "HTTP/1.1 400 Upload mismatch\r\nContent-Length: 0";
         }
      };
    
      thread server_thread([this]()
      {
         mServer.start();
      });
    
      // the SimpleWeb example code demonstrates waiting for 1 second to let the HTTP server start.
      // unsure if there we could instead rely on an event..
      this_thread::sleep_for(seconds(1));
    
    
      HTTPClient httpClient;
      
      std::stringstream url;
      url << serverBaseUrl() << resourcePath;
      
      HTTPClient::RequestConfig requestConfig;
      
      requestConfig.messageBody = body;
      requestConfig.messageLengthInBytes = bodySizeBytes;
      requestConfig.verb = HTTPClient::EHTTPVerbPOST;
      
      HTTPClient::ResponseResult responseResult;
      httpClient.StartHTTPSession(url.str().c_str(), requestConfig, responseResult);
      
      ASSERT_EQ(responseResult.errorCode, 0);
      ASSERT_EQ(responseResult.status, 200);
      
      delete [] body;
      
      mServer.stop();
      server_thread.join();
    
      return;
   }

   TEST_F(HttpModuleTest, DISABLED_PeerlessPostLargeBody)
   {
      TestAccount alice("alice", Account_Init);
   
      const int bodySizeBytes = 8000000;
      char* body = new char[bodySizeBytes];
      
      int locs[32];
      const int locsLen = sizeof(locs) / sizeof(locs[0]);
      // write values at particular points to compare later
      for (int i = 0; i < locsLen; ++i)
      {
         int writeLoc = (bodySizeBytes / locsLen) * i;
         locs[i] = writeLoc;
         body[writeLoc] = i;
      }
   
    
      HTTPClient httpClient;
      
      std::stringstream url;
      url << "https://cd-userserv.peerlessnetwork.io/clienttrace/submit/peerlessnetwork.io?app=Cloud%20Dial&user=sjackson%40peerlessnetwork.io&domain=&token=EC9DC5F52&device=523464a11ef890d67ca3d0fbdd25428f4c3e5913";
      
      HTTPClient::RequestConfig requestConfig;
      requestConfig.verboseLogging = true;
      requestConfig.suppressLogging = false;
      requestConfig.username = "peerless";
      requestConfig.password = "w674slehnn1pedo8";
      requestConfig.mimeType = "text/plain";

      requestConfig.messageBody = body;
      requestConfig.messageLengthInBytes = bodySizeBytes;
      requestConfig.verb = HTTPClient::EHTTPVerbPOST;
      requestConfig.enableCookies = true;
      
      HTTPClient::ResponseResult responseResult;
      httpClient.StartHTTPSession(url.str().c_str(), requestConfig, responseResult);
      
      //ASSERT_EQ(responseResult.errorCode, 0);
      //ASSERT_EQ(responseResult.status, 200);
      
      this_thread::sleep_for(chrono::seconds(5));
      
      delete [] body;
    
      return;
   }
   
   std::time_t getTime()
   {
      time_point<system_clock> startTime = system_clock::now();
      std::time_t ttp = system_clock::to_time_t(startTime);
      return ttp;
   }
   
   // Make a POST request which receives no response at all for 10 seconds
   TEST_F(HttpModuleTest, PostNoResponseDefaultDynamicTimeout)
   {
      const int bodySizeBytes = 1000 * 50;
      char* body = new char[bodySizeBytes];
      bool requestSeen = false;

      const std::string resourcePath("/postnoresponsetimeout");
      
      mServer.resource[resourcePathRegex(resourcePath)]["POST"]=[&requestSeen](std::shared_ptr<HttpServer::Response> response,
                                                                          std::shared_ptr<HttpServer::Request> request)
      {
         requestSeen = true;
      
         // hang the response longer than our timeout, to make sure we are
         // verifying our timeout, and not how long the response takes
         std::this_thread::sleep_for(seconds(CPCAPI2_HARD_CODED_HTTP_TIMEOUT_SEC + 10));

         *response << "HTTP/1.1 200 OK\r\nContent-Length: 0";
      };
    
      thread server_thread([this]()
      {
         mServer.start();
      });
    
      // the SimpleWeb example code demonstrates waiting for 1 second to let the HTTP server start.
      // unsure if there we could instead rely on an event..
      this_thread::sleep_for(seconds(1));
   
    
      HTTPClient httpClient;
      
      std::stringstream url;
      url << serverBaseUrl() << resourcePath;
      
      HTTPClient::RequestConfig requestConfig;
      
      requestConfig.messageBody = body;
      requestConfig.messageLengthInBytes = bodySizeBytes;
      requestConfig.verb = HTTPClient::EHTTPVerbPOST;
      
      HTTPClient::ResponseResult responseResult;
      
      std::time_t startTime = getTime();
      httpClient.StartHTTPSession(url.str().c_str(), requestConfig, responseResult);
      
      // should have timed out within CPCAPI2_HARD_CODED_HTTP_TIMEOUT_SEC seconds
      
      std::time_t requestDurationSec = getTime() - startTime;
      long diff = abs(requestDurationSec - CPCAPI2_HARD_CODED_HTTP_TIMEOUT_SEC);
      ASSERT_LE(diff, 4) << "Request should have been close to our expected timeout duration";

      ASSERT_TRUE(requestSeen);
      ASSERT_EQ(responseResult.errorCode, -1);
      
      delete [] body;
      
      mServer.stop();
      server_thread.join();
    
      return;
   }
   
   // disable the dynamic timeout and make sure the HTTP query takes as long as the
   // response hangs for
   TEST_F(HttpModuleTest, PostNoResponseCustomDynamicTimeout)
   {
      const int bodySizeBytes = 1000 * 50;
      char* body = new char[bodySizeBytes];
      bool requestSeen = false;
      const int customDynamicTimeoutSec = 20;

      const std::string resourcePath("/postnoresponsetimeout");
      
      mServer.resource[resourcePathRegex(resourcePath)]["POST"]=[&requestSeen, customDynamicTimeoutSec](std::shared_ptr<HttpServer::Response> response,
                                                                                                 std::shared_ptr<HttpServer::Request> request)
      {
         requestSeen = true;
      
         // hang the response longer than our timeout, to make sure we are
         // verifying our timeout, and not how long the response takes
         std::this_thread::sleep_for(seconds(customDynamicTimeoutSec + 10));

         *response << "HTTP/1.1 200 OK\r\nContent-Length: 0";
      };
    
      thread server_thread([this]()
      {
         mServer.start();
      });
    
      // the SimpleWeb example code demonstrates waiting for 1 second to let the HTTP server start.
      // unsure if there we could instead rely on an event..
      this_thread::sleep_for(seconds(1));
   
    
      HTTPClient httpClient;
      
      std::stringstream url;
      url << serverBaseUrl() << resourcePath;
      
      HTTPClient::RequestConfig requestConfig;
      
      requestConfig.messageBody = body;
      requestConfig.messageLengthInBytes = bodySizeBytes;
      requestConfig.verb = HTTPClient::EHTTPVerbPOST;
      requestConfig.dynamicTimeoutSeconds = customDynamicTimeoutSec;
      
      HTTPClient::ResponseResult responseResult;
      
      std::time_t startTime = getTime();
      httpClient.StartHTTPSession(url.str().c_str(), requestConfig, responseResult);
      
      // should have timed out within customTimeoutSec seconds
      
      std::time_t requestDurationSec = getTime() - startTime;
      long diff = abs(requestDurationSec - customDynamicTimeoutSec);
      ASSERT_LE(diff, 4) << "Request should have been close to our expected timeout duration";

      ASSERT_TRUE(requestSeen);
      ASSERT_EQ(responseResult.errorCode, -1);
      
      delete [] body;
      
      mServer.stop();
      server_thread.join();
    
      return;
   }
   
   TEST_F(HttpModuleTest, PostNoResponseNoDynamicTimeout)
   {
      const int bodySizeBytes = 1000 * 50;
      char* body = new char[bodySizeBytes];
      bool requestSeen = false;
      // dynamic timeout disabled
      const int customDynamicTimeoutSec = 0;
      // anything longer than CPCAPI2_HARD_CODED_HTTP_TIMEOUT_SEC just to make sure it is not kicking in
      const int responseHangTimeSec = CPCAPI2_HARD_CODED_HTTP_TIMEOUT_SEC * 2;

      const std::string resourcePath("/postnoresponsetimeout");
      
      mServer.resource[resourcePathRegex(resourcePath)]["POST"]=[&requestSeen, responseHangTimeSec](std::shared_ptr<HttpServer::Response> response,
                                                                                                    std::shared_ptr<HttpServer::Request> request)
      {
         requestSeen = true;
      
         // hang the response longer than our timeout, to make sure we are
         // verifying our timeout, and not how long the response takes
         std::this_thread::sleep_for(seconds(responseHangTimeSec));

         *response << "HTTP/1.1 200 OK\r\nContent-Length: 0";
      };
    
      thread server_thread([this]()
      {
         mServer.start();
      });
    
      // the SimpleWeb example code demonstrates waiting for 1 second to let the HTTP server start.
      // unsure if there we could instead rely on an event..
      this_thread::sleep_for(seconds(1));
   
    
      HTTPClient httpClient;
      
      std::stringstream url;
      url << serverBaseUrl() << resourcePath;
      
      HTTPClient::RequestConfig requestConfig;
      
      requestConfig.messageBody = body;
      requestConfig.messageLengthInBytes = bodySizeBytes;
      requestConfig.verb = HTTPClient::EHTTPVerbPOST;
      requestConfig.dynamicTimeoutSeconds = customDynamicTimeoutSec;
      
      HTTPClient::ResponseResult responseResult;
      
      httpClient.StartHTTPSession(url.str().c_str(), requestConfig, responseResult);
      
      ASSERT_TRUE(requestSeen);
      
      // ensure the request did not time out
      ASSERT_EQ(responseResult.errorCode, 0);
      ASSERT_EQ(responseResult.status, 200);
      
      delete [] body;
      
      mServer.stop();
      server_thread.join();
    
      return;
   }
   

   TEST_F(HttpModuleTest, PostNoResponseCustomDynamicTimeout2)
   {
      const int bodySizeBytes = 1000 * 50;
      char* body = new char[bodySizeBytes];
      bool requestSeen = false;
      const int customDynamicTimeoutSec = 20;

      const std::string resourcePath("/postnoresponsetimeout");
      
      mServer.resource[resourcePathRegex(resourcePath)]["POST"]=[&requestSeen, customDynamicTimeoutSec](std::shared_ptr<HttpServer::Response> response,
                                                                                                 std::shared_ptr<HttpServer::Request> request)
      {
         requestSeen = true;
      
         // hang the response longer than our timeout, to make sure we are
         // verifying our timeout, and not how long the response takes
         std::this_thread::sleep_for(seconds(customDynamicTimeoutSec + 10));

         *response << "HTTP/1.1 200 OK\r\nContent-Length: 0";
      };
    
      thread server_thread([this]()
      {
         mServer.start();
      });
    
      // the SimpleWeb example code demonstrates waiting for 1 second to let the HTTP server start.
      // unsure if there we could instead rely on an event..
      this_thread::sleep_for(seconds(1));
   
    
      HTTPClient httpClient;
      
      std::stringstream url;
      url << serverBaseUrl() << resourcePath;
      
      int resultErrorCode;
      int responseStatus;
      cpc::string resultString;
      cpc::string contentType;
      HTTPClient::RedirectInfo redirectInfo;

      std::time_t startTime = getTime();
      
      // ugh Bria is still using this horrible method
      httpClient.HTTPSendMessage(HTTPClient::EHTTPVerbPOST, url.str().c_str(), NULL, "", "", "", "", "", 0, 0, false, false, false, false, "",
                                 cpc::vector<CPCAPI2::HTTPClient::StringPair>(), false, false, resultErrorCode, responseStatus, contentType,
                                 resultString, redirectInfo, "", "", 0, 0, cpc::vector<cpc::string>(), customDynamicTimeoutSec);

      std::time_t requestDurationSec = getTime() - startTime;
      long diff = abs(requestDurationSec - customDynamicTimeoutSec);
      ASSERT_LE(diff, 4) << "Request should have been close to our expected timeout duration";

      ASSERT_TRUE(requestSeen);
      ASSERT_EQ(resultErrorCode, -1);
      
      delete [] body;
      
      mServer.stop();
      server_thread.join();
    
      return;
   }

   // requires adjusting OS DNS settings to point to DNS server (unbound) running at 127.0.0.1
   TEST_F(HttpModuleTest, DISABLED_Post4ARecordsLastWorks)
   {
      const std::string resourcePath("/test");

      // listens for requests at /test
      mServer.resource[resourcePathRegex(resourcePath)]["POST"] = [](std::shared_ptr<HttpServer::Response> response,
         std::shared_ptr<HttpServer::Request> request)
      {
         // get body contents, and echo it back in the response

         auto content = request->content.string();
         *response << "HTTP/1.1 200 OK\r\nContent-Length: " << content.length() << "\r\n\r\n" << content;
      };

      thread server_thread([this]()
      {
         mServer.start();
      });

      // the SimpleWeb example code demonstrates waiting for 1 second to let the HTTP server start.
      // unsure if there we could instead rely on an event..
      this_thread::sleep_for(seconds(1));


      HTTPClient httpClient;

      std::stringstream url;
      url << "http://multialastworks.local:" << mListenPort << resourcePath;

      int resultErrorCode;
      int responseStatus;
      cpc::string resultString;
      cpc::string contentType;
      HTTPClient::RedirectInfo redirectInfo;
      cpc::string requestBody = "Test body";

      const int customDynamicTimeoutSec = 120;

      std::time_t startTime = getTime();

      // ugh Bria is still using this horrible method
      httpClient.HTTPSendMessage(HTTPClient::EHTTPVerbPOST, url.str().c_str(), NULL, "", "", "", "", requestBody, 0, 0, false, false, false, false, "",
         cpc::vector<CPCAPI2::HTTPClient::StringPair>(), false, false, resultErrorCode, responseStatus, contentType,
         resultString, redirectInfo, "", "", 0, 0, cpc::vector<cpc::string>(), customDynamicTimeoutSec);

      std::time_t requestDurationSec = getTime() - startTime;

      safeCout("HttpModuleTest.Post4ARecordsLastWorks request took " << requestDurationSec << " seconds");

      ASSERT_EQ(resultErrorCode, 0);
      ASSERT_EQ(responseStatus, 200);


      mServer.stop();
      server_thread.join();

      return;
   }

   TEST_F(HttpModuleTest, PostNoResponseAbort)
   {
      const int bodySizeBytes = 1000 * 50;
      char* body = new char[bodySizeBytes];
      bool requestSeen = false;

      const std::string resourcePath("/postnoresponseabort");
      
      mServer.resource[resourcePathRegex(resourcePath)]["POST"]=[&requestSeen](std::shared_ptr<HttpServer::Response> response,
                                                                          std::shared_ptr<HttpServer::Request> request)
      {
         requestSeen = true;
      
         // don't respond for 15 seconds
         std::this_thread::sleep_for(seconds(15));
         
         *response << "HTTP/1.1 200 OK\r\nContent-Length: 0";
      };
    
      thread server_thread([this]()
      {
         mServer.start();
      });
    
      // the SimpleWeb example code demonstrates waiting for 1 second to let the HTTP server start.
      // unsure if there we could instead rely on an event..
      this_thread::sleep_for(seconds(1));
   
    
      HTTPClient httpClient;
      
      std::stringstream url;
      url << serverBaseUrl() << resourcePath;
      
      HTTPClient::RequestConfig requestConfig;
      
      requestConfig.messageBody = body;
      requestConfig.messageLengthInBytes = bodySizeBytes;
      requestConfig.verb = HTTPClient::EHTTPVerbPOST;
      
      HTTPClient::ResponseResult responseResult;
      
      std::time_t deltaTime;
      auto httpRequestEvent = std::async(std::launch::async, [&]()
      {
         std::time_t startTime = getTime();
         httpClient.StartHTTPSession(url.str().c_str(), requestConfig, responseResult);
         deltaTime = getTime() - startTime;
      });
      
      std::this_thread::sleep_for(seconds(3));
      
      // make sure the HTTP request is still active
      ASSERT_NE(httpRequestEvent.wait_for(seconds(0)), std::future_status::ready);

      httpClient.Abort();
      
      // give the SDK 3 seconds to abort the HTTP request and return
      ASSERT_EQ(httpRequestEvent.wait_for(seconds(3)), std::future_status::ready);
      
      delete [] body;
      
      mServer.stop();
      server_thread.join();
    
      return;
   }
   
   TEST_F(HttpModuleTest, BasicGetLargeBody)
   {
      // 50 MB
      const int hostedBodySizeBytes = 1000 * 1000 * 50;
      std::unique_ptr<char> hostedBody(new char[hostedBodySizeBytes]);

      int locs[32];
      const int locsLen = sizeof(locs) / sizeof(locs[0]);
      // write values at particular points to compare later
      for (int i = 0; i < locsLen; ++i)
      {
         int writeLoc = (hostedBodySizeBytes / locsLen) * i;
         locs[i] = writeLoc;
         hostedBody.get()[writeLoc] = i;
      }
      
      // use streams so that we avoid making copies of the data where possible on the server side
      HttpTestFramework::FakeFileStream fakeFileBuf(hostedBody.get(), hostedBodySizeBytes);

      const std::string resourcePath("/basicgetlargebody");
      mServer.resource[resourcePathRegex(resourcePath)]["GET"]=[this, &hostedBodySizeBytes,
                                                        &hostedBody, &locs,
                                                        &locsLen, &fakeFileBuf](std::shared_ptr<HttpServer::Response> response,
                                                                                std::shared_ptr<HttpServer::Request> request)
      {
         std::shared_ptr<std::istream> ifs = std::make_shared<std::istream>(&fakeFileBuf);

         *response << "HTTP/1.1 200 OK\r\n" << "Content-Length: " << hostedBodySizeBytes << "\r\n\r\n";
         HttpTestFramework::FileSender::resourceSendStream(mServer, response, ifs, hostedBodySizeBytes);
      };

      thread server_thread([this]()
      {
         mServer.start();
      });
    
      // the SimpleWeb example code demonstrates waiting for 1 second to let the HTTP server start.
      // unsure if there we could instead rely on an event..
      this_thread::sleep_for(seconds(1));
    
      HTTPClient httpClient;
      
      std::stringstream url;
      url << serverBaseUrl() << resourcePath;
      
      HTTPClient::RequestConfig requestConfig;
      requestConfig.messageBody = "";
      requestConfig.messageLengthInBytes = 0;
      requestConfig.verb = HTTPClient::EHTTPVerbGET;

      HTTPClient::ResponseResult responseResult;
      
      std::time_t startTime = getTime();
      httpClient.StartHTTPSession(url.str().c_str(), requestConfig, responseResult);
      std::time_t endTime = getTime();
      
      std::cout << "Took " << endTime - startTime << " seconds to download file in BasicGetLargeBody test" << std::endl;
      
      ASSERT_EQ(responseResult.errorCode, 0);
      ASSERT_EQ(responseResult.messageBody.size(), hostedBodySizeBytes);

      cpc::string& responseBody = responseResult.messageBody;
      
      for (int i = 0; i < locsLen; ++i)
      {
         ASSERT_FALSE(locs[i] > responseBody.size());
         ASSERT_EQ(hostedBody.get()[locs[i]], responseBody[locs[i]]);
      }

      mServer.stop();
      server_thread.join();
    
      return;
   }
   
   TEST_F(HttpModuleTest, BasicGetLargeBodySlowDownload)
   {
      // 50 MB
      const int hostedBodySizeBytes = 1000 * 1000 * 50;
      std::unique_ptr<char> hostedBody(new char[hostedBodySizeBytes]);

      int locs[32];
      const int locsLen = sizeof(locs) / sizeof(locs[0]);
      // write values at particular points to compare later
      for (int i = 0; i < locsLen; ++i)
      {
         int writeLoc = (hostedBodySizeBytes / locsLen) * i;
         locs[i] = writeLoc;
         hostedBody.get()[writeLoc] = i;
      }
      
      // use streams so that we avoid making copies of the data where possible on the server side
      HttpTestFramework::FakeFileStream fakeFileBuf(hostedBody.get(), hostedBodySizeBytes);

      const std::string resourcePath("/basicgetlargebodyslowdownload");
      mServer.resource[resourcePathRegex(resourcePath)]["GET"]=[this, &hostedBodySizeBytes,
                                                        &hostedBody, &locs,
                                                        &locsLen, &fakeFileBuf](std::shared_ptr<HttpServer::Response> response,
                                                                                std::shared_ptr<HttpServer::Request> request)
      {
         std::shared_ptr<std::istream> ifs = std::make_shared<std::istream>(&fakeFileBuf);

         *response << "HTTP/1.1 200 OK\r\n" << "Content-Length: " << hostedBodySizeBytes << "\r\n\r\n";

         // ask the sender to take at least 15 seconds to send the file; however, this should  NOT
         // triger our HTTP timeout, as the server is still sending data, albeit slowly
         HttpTestFramework::FileSender::resourceSendStream(mServer, response, ifs, hostedBodySizeBytes,
                                                           seconds(CPCAPI2_HARD_CODED_HTTP_TIMEOUT_SEC)
                                                           + seconds(5));
      };

      thread server_thread([this]()
      {
         mServer.start();
      });
    
      // the SimpleWeb example code demonstrates waiting for 1 second to let the HTTP server start.
      // unsure if there we could instead rely on an event..
      this_thread::sleep_for(seconds(1));
    
      HTTPClient httpClient;
      
      std::stringstream url;
      url << serverBaseUrl() << resourcePath;
      
      HTTPClient::RequestConfig requestConfig;
      requestConfig.messageBody = "";
      requestConfig.messageLengthInBytes = 0;
      requestConfig.verb = HTTPClient::EHTTPVerbGET;

      HTTPClient::ResponseResult responseResult;
      
      std::time_t startTime = getTime();
      httpClient.StartHTTPSession(url.str().c_str(), requestConfig, responseResult);
      std::time_t endTime = getTime();
      
      std::cout << "Took " << endTime - startTime << " seconds to download file in BasicGetLargeBody test" << std::endl;
      
      ASSERT_EQ(responseResult.errorCode, 0);
      ASSERT_EQ(responseResult.messageBody.size(), hostedBodySizeBytes);

      cpc::string& responseBody = responseResult.messageBody;
      
      for (int i = 0; i < locsLen; ++i)
      {
         ASSERT_FALSE(locs[i] > responseBody.size());
         ASSERT_EQ(hostedBody.get()[locs[i]], responseBody[locs[i]]);
      }

      mServer.stop();
      server_thread.join();
    
      return;
   }
   
   TEST_F(HttpModuleTest, BasicGetMidDownloadTimeout)
   {
      HttpTestFramework::FileSender::CvWrapper cv;
      cv.cv = std::make_shared<std::condition_variable>();
      cv.mx = std::make_shared<std::mutex>();
   
      system_clock::time_point hangPoint;
      const std::string resourcePath("/BasicGetMidDownloadTimeout");
      mServer.resource[resourcePathRegex(resourcePath)]["GET"]=[this, &cv, &hangPoint]
                                                               (std::shared_ptr<HttpServer::Response> response,
                                                                std::shared_ptr<HttpServer::Request> request)
      {
         *response << "HTTP/1.1 200 OK\r\n" << "Content-Length: 50000000\r\n\r\n";

         // ask the sender to send stream data for the download, but hang doing so part way
         
         hangPoint = system_clock::now() + seconds(15);
         HttpTestFramework::FileSender::resourceSendStreamHault(mServer, response, hangPoint, cv);
      };

      thread server_thread([this]()
      {
         mServer.start();
      });
    
      // the SimpleWeb example code demonstrates waiting for 1 second to let the HTTP server start.
      // unsure if there we could instead rely on an event..
      this_thread::sleep_for(seconds(1));
    
      HTTPClient httpClient;
      
      std::stringstream url;
      url << serverBaseUrl() << resourcePath;
      
      HTTPClient::RequestConfig requestConfig;
      requestConfig.messageBody = "";
      requestConfig.messageLengthInBytes = 0;
      requestConfig.verb = HTTPClient::EHTTPVerbGET;

      HTTPClient::ResponseResult responseResult;
      
      time_point<system_clock> startTime = system_clock::now();
      httpClient.StartHTTPSession(url.str().c_str(), requestConfig, responseResult);
      time_point<system_clock> endTime = system_clock::now();
      
      ASSERT_EQ(responseResult.errorCode, -1);
      cv.cv->notify_all();
      
      // should have hung sometime after the request started
      ASSERT_GT(hangPoint, startTime);
      
      // should have hung sometime before the request stopped
      ASSERT_GT(endTime, hangPoint);
      
      // request should have timed out sometime around CPCAPI2_HARD_CODED_HTTP_TIMEOUT_SEC seconds after the hang
      ASSERT_GT(endTime, hangPoint + seconds(CPCAPI2_HARD_CODED_HTTP_TIMEOUT_SEC - 5));
      ASSERT_LT(endTime, hangPoint + seconds(CPCAPI2_HARD_CODED_HTTP_TIMEOUT_SEC + 5));
      
      mServer.stop();
      server_thread.join();
    
      return;
   }
   
   TEST_F(HttpModuleTest, BasicGetMidDownloadAbort)
   {
      HttpTestFramework::FileSender::CvWrapper cv;
      cv.cv = std::make_shared<std::condition_variable>();
      cv.mx = std::make_shared<std::mutex>();
      
      // 50 MB
      const int hostedBodySizeBytes = 1000 * 1000 * 50;
      std::unique_ptr<char> hostedBody(new char[hostedBodySizeBytes]);
      
      HttpTestFramework::FakeFileStream fakeFileBuf(hostedBody.get(), hostedBodySizeBytes);
   
      const std::string resourcePath("/BasicGetMidDownloadAbort");
      mServer.resource[resourcePathRegex(resourcePath)]["GET"]=[this, &hostedBodySizeBytes,
                                                        &hostedBody,
                                                        &fakeFileBuf](std::shared_ptr<HttpServer::Response> response,
                                                                      std::shared_ptr<HttpServer::Request> request)
      {
         std::shared_ptr<std::istream> ifs = std::make_shared<std::istream>(&fakeFileBuf);

         *response << "HTTP/1.1 200 OK\r\n" << "Content-Length: " << hostedBodySizeBytes << "\r\n\r\n";

         // ask the sender to take at least 10 seconds to deliver the file
         HttpTestFramework::FileSender::resourceSendStream(mServer, response, ifs, hostedBodySizeBytes,
                                                           seconds(10));
      };

      thread server_thread([this]()
      {
         mServer.start();
      });
    
      // the SimpleWeb example code demonstrates waiting for 1 second to let the HTTP server start.
      // unsure if there we could instead rely on an event..
      this_thread::sleep_for(seconds(1));
    
      HTTPClient httpClient;
      
      std::stringstream url;
      url << serverBaseUrl() << resourcePath;
      
      HTTPClient::RequestConfig requestConfig;
      requestConfig.messageBody = "";
      requestConfig.messageLengthInBytes = 0;
      requestConfig.verb = HTTPClient::EHTTPVerbGET;

      HTTPClient::ResponseResult responseResult;

      auto httpRequestEvent = std::async(std::launch::async, [&]()
      {
         httpClient.StartHTTPSession(url.str().c_str(), requestConfig, responseResult);
      });
      
      std::this_thread::sleep_for(seconds(3));
      
      // make sure the HTTP request is still active
      ASSERT_NE(httpRequestEvent.wait_for(seconds(0)), std::future_status::ready);

      httpClient.Abort();
      
      // give the SDK 3 seconds to abort the HTTP request and return
      ASSERT_EQ(httpRequestEvent.wait_for(seconds(3)), std::future_status::ready);

      ASSERT_EQ(responseResult.errorCode, -1);
      
      mServer.stop();
      server_thread.join();
    
      return;
   }


#if defined(_WIN32) && !defined(_WIN64)
   // Attempt to download a very large file to memory, and check that it fails gracefully.
   // can't yet run this on macOS, because it always succeeds (the OS seems to not let the process exhaust free heap space)
   TEST_F(HttpModuleTest, MassiveDownloadFailGracefully)
   {
      HttpTestFramework::FileSender::CvWrapper cv;
      cv.cv = std::make_shared<std::condition_variable>();
      cv.mx = std::make_shared<std::mutex>();
      
      // 5000 mB
      const unsigned long hostedBodySizeBytes = 1000l * 1000l * 5000l;
      
      const std::string resourcePath("/MassiveDownloadFailGracefully");
      mServer.resource[resourcePathRegex(resourcePath)]["GET"]=[this, &hostedBodySizeBytes](std::shared_ptr<HttpServer::Response> response,
                                                                                            std::shared_ptr<HttpServer::Request> request)
      {
         *response << "HTTP/1.1 200 OK\r\n" << "Content-Length: " << hostedBodySizeBytes << "\r\n\r\n";

         // place no limit on how fast the download comes in
         HttpTestFramework::FileSender::resourceSendStreamBogusBytes(mServer, response, hostedBodySizeBytes);
      };

      thread server_thread([this]()
      {
         mServer.start();
      });
    
      // the SimpleWeb example code demonstrates waiting for 1 second to let the HTTP server start.
      // unsure if there we could instead rely on an event..
      this_thread::sleep_for(seconds(1));
    
      HTTPClient httpClient;
      
      std::stringstream url;
      url << serverBaseUrl() << resourcePath;
      
      HTTPClient::RequestConfig requestConfig;
      requestConfig.timeoutSeconds = 60 * 10;
      requestConfig.messageBody = "";
      requestConfig.messageLengthInBytes = 0;
      requestConfig.verb = HTTPClient::EHTTPVerbGET;

      HTTPClient::ResponseResult responseResult;

      httpClient.StartHTTPSession(url.str().c_str(), requestConfig, responseResult);
      
      // expect that somewhere along the way, internally the SDK would fail to allocate a large enough
      // contiguous memory region. ensure we don't crash but instead fail the request, and indicate as such
      ASSERT_EQ(responseResult.errorCode, -1);
      ASSERT_EQ(responseResult.status, HTTPClient::EHTTPUnknownError);
      
      mServer.stop();
      server_thread.join();
    
      return;
   }
#endif

   TEST_F(HttpModuleTest, FileSizeLimitTest)
   {
      HttpTestFramework::FileSender::CvWrapper cv;
      cv.cv = std::make_shared<std::condition_variable>();
      cv.mx = std::make_shared<std::mutex>();

      // 20 mB
      const unsigned long hostedBodySizeBytes = 1000l * 1000l * 20l;

      const std::string resourcePath("/FileSizeLimitTest");
      mServer.resource[resourcePathRegex(resourcePath)]["GET"] = [this, &hostedBodySizeBytes](std::shared_ptr<HttpServer::Response> response,
         std::shared_ptr<HttpServer::Request> request)
      {
         *response << "HTTP/1.1 200 OK\r\n" << "Content-Length: " << hostedBodySizeBytes << "\r\n\r\n";

         // place no limit on how fast the download comes in
         HttpTestFramework::FileSender::resourceSendStreamBogusBytes(mServer, response, hostedBodySizeBytes);
      };

      thread server_thread([this]()
      {
         mServer.start();
      });

      // the SimpleWeb example code demonstrates waiting for 1 second to let the HTTP server start.
      // unsure if there we could instead rely on an event..
      this_thread::sleep_for(seconds(1));

      HTTPClient httpClient;

      std::stringstream url;
      url << serverBaseUrl() << resourcePath;

      // first, try a request with no limit and ensure it succeeds
      {
         HTTPClient::RequestConfig requestConfig;
         requestConfig.timeoutSeconds = 60 * 60 * 1; // 1 hour
         requestConfig.messageBody = "";
         requestConfig.messageLengthInBytes = 0;
         requestConfig.maxResponseBodySizeBytes = 0;
         requestConfig.verb = HTTPClient::EHTTPVerbGET;

         HTTPClient::ResponseResult responseResult;

         httpClient.StartHTTPSession(url.str().c_str(), requestConfig, responseResult);

         ASSERT_EQ(responseResult.errorCode, 0);
      }

      // next, try the same request with a size limit, and ensure it fails
      {
         HTTPClient::RequestConfig requestConfig;
         requestConfig.timeoutSeconds = 60 * 60 * 1; // 1 hour
         requestConfig.messageBody = "";
         requestConfig.messageLengthInBytes = 0;
         requestConfig.maxResponseBodySizeBytes = 5000;
         requestConfig.verb = HTTPClient::EHTTPVerbGET;

         HTTPClient::ResponseResult responseResult;

         httpClient.StartHTTPSession(url.str().c_str(), requestConfig, responseResult);

         ASSERT_EQ(responseResult.errorCode, -1);
         ASSERT_EQ(responseResult.status, HTTPClient::EHTTPResponseBodySizeLimitExceeded);
      }

      mServer.stop();
      server_thread.join();

      return;
   }
   
   TEST_F(HttpModuleTest, GetHttpsVerboseLogging)
   {
      // TODO: remove; many certificates at badssl.com have recently expired.
      // try again after May 24, 2022
      if (time(NULL) < **********)
      {
         GTEST_SKIP();
      }

      TestAccount alice("alice", Account_Init); // only to enable logging
               
      HTTPClient httpClient;
      
      HTTPClient::RequestConfig requestConfig;
      requestConfig.verb = HTTPClient::EHTTPVerbGET;
      requestConfig.verboseLogging = true;
      
      HTTPClient::ResponseResult responseResult;
      httpClient.StartHTTPSession("https://tls-v1-2.badssl.com:1012/", requestConfig, responseResult);
      
      ASSERT_EQ(responseResult.errorCode, 0);
      
      // headers are currently not exposed, so we can't check them

      mServer.stop();
    
      return;
   }
   
}

