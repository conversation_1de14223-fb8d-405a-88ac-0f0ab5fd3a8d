#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#include "test_framework/xmpp_test_helper.h"
#include "xmpp/XmppAccountJsonApi.h"
#include "xmpp/XmppMultiUserChatJsonApi.h"
#include "xmpp/XmppRosterJsonApi.h"
#include "impl/auth_server/AuthServerJwtUtils.h"
#include "test_framework/network_utils.h"

#ifndef ANDROID
//define USE_CPCAPI2_JSON_TESTS 1
#endif


#if (CPCAPI2_BRAND_XMPP_MULTI_USER_CHAT_MODULE == 1)

#if defined(__GNUC__) || defined(__clang__)
#pragma GCC diagnostic ignored "-Wdeprecated-declarations"
#endif

using namespace CPCAPI2;
using namespace CPCAPI2::XmppAccount;
using namespace CPCAPI2::XmppRoster;
using namespace CPCAPI2::XmppMultiUserChat;
using namespace CPCAPI2::JsonApi;

static time_t _timegm(tm *tm)
{
#ifdef _WIN32
   return _mkgmtime(tm);
#else
   return timegm(tm);
#endif
}

namespace {

class XmppMultiUserChatModuleTest : public CpcapiAutoTest
{
public:
   XmppMultiUserChatModuleTest() {}
   virtual ~XmppMultiUserChatModuleTest() {}
   static void SetUpTestCase() {}
   static void TearDownTestCase() {}
   virtual void SetUp()
   {
   }
   virtual void TearDown()
   {
   }
};

TEST_F(XmppMultiUserChatModuleTest, GetRoomList) {
   XmppTestAccount alice("alice");

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   alice.mucManager->getRoomList(alice.handle);

   {
      XmppAccountHandle h;
      RoomListRetrievedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onRoomListRetrieved", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      //ASSERT_TRUE(!evt.rooms.empty()); // bliu: there could be no room at all
   }
}

TEST_F(XmppMultiUserChatModuleTest, DISABLED_GetRoomListWithInfo_Imap) {
   XmppTestAccount alice("alice", Account_NoInit);
   alice.config.settings.domain = "imap.mobilevoiplive.com";
   alice.config.settings.port = 5880;
   alice.config.settings.username = "Jason1";
   alice.config.settings.password = "jason1";
   alice.init();
   alice.enable();

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   alice.mucManager->getRoomList(alice.handle);

   {
      XmppAccountHandle h;
      RoomListRetrievedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onRoomListRetrieved", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);

      cpc::vector<XmppMultiUserChatHandle> handles;
      for (cpc::vector<RoomListItem>::const_iterator it = evt.rooms.begin(); it != evt.rooms.end(); ++it)
      {
            XmppMultiUserChatHandle h = alice.mucManager->create(alice.handle, it->jid);
            handles.push_back(h);
      }

      alice.mucManager->getRoomsInfo(handles);

      MultiUserChatRoomStateChangedEvent roomStateChangedEvt;
      for (cpc::vector<XmppMultiUserChatHandle>::const_iterator it = handles.begin(); it != handles.end(); ++it)
      {
         ASSERT_TRUE(cpcWaitForEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatRoomStateChanged", 5000, CPCAPI2::test::AlwaysTruePred(), h, roomStateChangedEvt));
      }
      
      safeCout("Got room info for " << handles.size() << " rooms");
   }
}

TEST_F(XmppMultiUserChatModuleTest, DISABLED_MembersOnlyRemove_Imap) {
   XmppTestAccount alice("alice", Account_NoInit);
   alice.config.settings.domain = "imap.mobilevoiplive.com";
   alice.config.settings.port = 5880;
   alice.config.settings.username = "Jason1";
   alice.config.settings.password = "jason1";
   alice.init();
   alice.enable();

   XmppTestAccount bob("bob", Account_NoInit);
   bob.config.settings.domain = "imap.mobilevoiplive.com";
   bob.config.settings.port = 5880;
   bob.config.settings.username = "Jason2";
   bob.config.settings.password = "jason2";
   bob.init();
   bob.enable();

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bob.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   XmppMultiUserChatHandle aliceChat = alice.mucManager->create(alice.handle, false);

   alice.mucManager->join(aliceChat, "", alice.config.nick);

   {
      XmppMultiUserChatHandle h;
      MultiUserChatConfigurationRequestedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatConfigurationRequested", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);

      auto _configurations = evt.configurations;

      for (auto& item : _configurations.items)
      {
         if (item.name == "muc#roomconfig_membersonly")
         {
            item.values[0] = "1";
         }
      }

      alice.mucManager->setConfigurations(aliceChat, _configurations);
   }

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_TRUE(!evt.roomjid.empty());
      ASSERT_TRUE(evt.isNewRoom);
   }

   alice.mucManager->invite(aliceChat, bob.config.bare(), "invite");

   XmppMultiUserChatHandle bobChat;

   {
      MultiUserChatInvitationReceivedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatInvitationReceived", 5000, CPCAPI2::test::AlwaysTruePred(), bobChat, evt));
      ASSERT_EQ(bobChat, aliceChat + 1);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_TRUE(!evt.roomjid.empty());
      ASSERT_EQ(evt.reason, "invite");
      bob.mucManager->accept(bobChat, bob.config.nick);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bobChat);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_EQ(evt.nickname, alice.config.nick);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      //ASSERT_EQ(evt.jid, bob.config.settings.username + "@" + bob.config.settings.domain);
      ASSERT_EQ(evt.nickname, bob.config.nick);
   }

   XmppMultiUserChatState mucState;
   alice.mucStateManager->getState(aliceChat, mucState);
   ASSERT_TRUE(!mucState.participants.empty());

   alice.mucManager->changeJidAffiliation(aliceChat, bob.config.bare(), AffiliationNone, "remove");

   {
      XmppMultiUserChatHandle h;
      LocalUserLeftEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onLocalUserLeft", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(bobChat, h);
      //ASSERT_EQ(evt.reason, "remove"); // bliu: the reason is not passed on
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantRemovedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantRemoved", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.nickname, bob.config.nick);
   }
}

TEST_F(XmppMultiUserChatModuleTest, DISABLED_Join) {
   XmppTestAccount alice("alice");

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   XmppMultiUserChatHandle aliceChat = alice.mucManager->create(alice.handle);

   alice.mucManager->join(aliceChat, "", alice.config.nick);

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_TRUE(evt.isNewRoom);
   }
}

TEST_F(XmppMultiUserChatModuleTest, DISABLED_JoinExisting) {
   XmppTestAccount alice("alice");

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   XmppMultiUserChatHandle aliceChat = alice.mucManager->create(alice.handle);

   alice.mucManager->join(aliceChat, "everyone", alice.config.nick);

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.room, "everyone");
      ASSERT_TRUE(!evt.isNewRoom);
   }
}

TEST_F(XmppMultiUserChatModuleTest, JoinWithInvalidRoom) {
   XmppTestAccount alice("alice");

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   XmppMultiUserChatHandle aliceChat = alice.mucManager->create(alice.handle);

   alice.mucManager->join(aliceChat, "invalid room", alice.config.nick);

   {
      XmppMultiUserChatHandle h;
      MultiUserChatErrorEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatError", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.type, OtherError);
   }
}

// bliu: server will set nickname as jid in with an invalid nickname provided
TEST_F(XmppMultiUserChatModuleTest, DISABLED_JoinWithInvalidNickname) {
   XmppTestAccount alice("alice");

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   XmppMultiUserChatHandle aliceChat = alice.mucManager->create(alice.handle);

   alice.mucManager->join(aliceChat, "", "invalid@invalid");

   {
      XmppMultiUserChatHandle h;
      MultiUserChatErrorEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatError", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.type, OtherError);
   }
}

TEST_F(XmppMultiUserChatModuleTest, JoinWithSameRoom) {
   XmppTestAccount alice("alice");

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   XmppMultiUserChatHandle aliceChat1 = alice.mucManager->create(alice.handle);

   const cpc::string room = "room_" + cpc::string(resip::Random::getCryptoRandomHex(16).c_str());

   alice.mucManager->join(aliceChat1, room, alice.config.nick);

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat1, h);
      ASSERT_EQ(evt.room, room);
      ASSERT_TRUE(evt.isNewRoom);
   }

   XmppMultiUserChatHandle aliceChat2 = alice.mucManager->create(alice.handle);

   alice.mucManager->join(aliceChat2, room, alice.config.nick + ".clone");

   {
      XmppMultiUserChatHandle h;
      MultiUserChatErrorEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatError", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat2, h);
      ASSERT_EQ(evt.type, OtherError);
   }

   alice.mucManager->leave(aliceChat1);
   alice.mucManager->leave(aliceChat2);
}

TEST_F(XmppMultiUserChatModuleTest, InviteAcceptWithSameRoom) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bob.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   XmppMultiUserChatHandle aliceChat = alice.mucManager->create(alice.handle);

   const cpc::string room = "room_" + cpc::string(resip::Random::getCryptoRandomHex(16).c_str());

   alice.mucManager->join(aliceChat, room, alice.config.nick);

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.room, room);
   }

   alice.mucManager->invite(aliceChat, bob.config.bare(), "invite");

   XmppMultiUserChatHandle bobChat;

   {
      XmppMultiUserChatHandle h;
      MultiUserChatInvitationReceivedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatInvitationReceived", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_EQ(evt.reason, "invite");
      bobChat = h;
      bob.mucManager->accept(bobChat, bob.config.nick);
   }

   alice.mucManager->invite(aliceChat, bob.config.bare(), "reinvite");

   {
      XmppMultiUserChatHandle h;
      MultiUserChatInvitationReceivedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatInvitationReceived", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_EQ(bobChat, h);
      ASSERT_EQ(evt.reason, "reinvite");
      bob.mucManager->accept(bobChat, bob.config.nick);
   }

   {
      XmppMultiUserChatHandle h;
      MultiUserChatErrorEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatError", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.type, OtherError);
      ASSERT_EQ(bobChat, h);
   }
}

TEST_F(XmppMultiUserChatModuleTest, NonownerInvite) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");
   XmppTestAccount carol("carol");

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bob.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(carol.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, carol.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   XmppMultiUserChatHandle aliceChat = alice.mucManager->create(alice.handle, false);

   const cpc::string room = "room_" + cpc::string(resip::Random::getCryptoRandomHex(16).c_str());

   alice.mucManager->join(aliceChat, room, alice.config.nick);

   {
      XmppMultiUserChatHandle h;
      MultiUserChatConfigurationRequestedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatConfigurationRequested", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);

      auto _configurations = evt.configurations;

      for (auto& item : _configurations.items)
      {
         if (item.name == "muc#roomconfig_allowinvites")
         {
            item.values[0] = "1";
         }
      }

      alice.mucManager->setConfigurations(aliceChat, _configurations);
   }

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.room, room);
   }

   XmppMultiUserChatHandle bobChat = bob.mucManager->create(bob.handle);

   bob.mucManager->join(bobChat, room, bob.config.nick);

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(bobChat, h);
      ASSERT_EQ(evt.room, room);
   }

   bob.mucManager->invite(bobChat, carol.config.bare());

   XmppMultiUserChatHandle carolChat;

   {
      MultiUserChatInvitationReceivedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(carol.events, "XmppMultiUserChatHandler::onMultiUserChatInvitationReceived", 5000, CPCAPI2::test::AlwaysTruePred(), carolChat, evt));
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_TRUE(!evt.room.empty());
      carol.mucManager->accept(carolChat, carol.config.nick);
   }

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(carol.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(carolChat, h);
      ASSERT_EQ(evt.room, room);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(carol.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(carolChat, h);
      //ASSERT_EQ(evt.jid, bob.config.settings.username + "@" + bob.config.settings.domain);
      ASSERT_TRUE(evt.nickname == alice.config.nick || evt.nickname == bob.config.nick);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(carol.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(carolChat, h);
      //ASSERT_EQ(evt.jid, bob.config.settings.username + "@" + bob.config.settings.domain);
      ASSERT_TRUE(evt.nickname == alice.config.nick || evt.nickname == bob.config.nick);
   }
}

TEST_F(XmppMultiUserChatModuleTest, DISABLED_Leave) {
   XmppTestAccount alice("alice");

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   XmppMultiUserChatHandle aliceChat = alice.mucManager->create(alice.handle);

   alice.mucManager->join(aliceChat, "", alice.config.nick);

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_TRUE(evt.isNewRoom);
   }

   alice.mucManager->leave(aliceChat, "leave");

   {
      XmppMultiUserChatHandle h;
      LocalUserLeftEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onLocalUserLeft", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.reason, "leave");
   }
}

TEST_F(XmppMultiUserChatModuleTest, DISABLED_InviteAccept) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bob.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   XmppMultiUserChatHandle aliceChat = alice.mucManager->create(alice.handle);

   alice.mucManager->join(aliceChat, "", alice.config.nick);

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_TRUE(!evt.room.empty());
      //ASSERT_TRUE(evt.isNewRoom);
   }

   //{
   //   XmppMultiUserChatHandle h;
   //   MultiUserChatConfigurationRequestedEvent evt;
   //   ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatConfigurationRequested", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   //   ASSERT_EQ(aliceChat, h);
   //}

   alice.mucManager->invite(aliceChat, bob.config.bare(), "invite");

   XmppMultiUserChatHandle bobChat;

   {
      MultiUserChatInvitationReceivedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatInvitationReceived", 5000, CPCAPI2::test::AlwaysTruePred(), bobChat, evt));
      ASSERT_EQ(bobChat, aliceChat + 1);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_EQ(evt.reason, "invite");
      bob.mucManager->accept(bobChat, bob.config.nick);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bobChat);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_EQ(evt.nickname, alice.config.nick);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      //ASSERT_EQ(evt.jid, bob.config.settings.username + "@" + bob.config.settings.domain);
      ASSERT_EQ(evt.nickname, bob.config.nick);
   }

   XmppMultiUserChatState mucState;
   alice.mucStateManager->getState(aliceChat, mucState);
   ASSERT_TRUE(!mucState.participants.empty());
}

TEST_F(XmppMultiUserChatModuleTest, DISABLED_InviteDecline) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bob.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   XmppMultiUserChatHandle aliceChat = alice.mucManager->create(alice.handle);

   alice.mucManager->join(aliceChat, "", alice.config.nick);

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_TRUE(evt.isNewRoom);
   }

   alice.mucManager->invite(aliceChat, bob.config.bare(), "invite");

   {
      XmppMultiUserChatHandle h;
      MultiUserChatInvitationReceivedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatInvitationReceived", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, aliceChat + 1);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_EQ(evt.reason, "invite");
      bob.mucManager->decline(h, "decline");
   }

   {
      XmppMultiUserChatHandle h;
      MultiUserChatInvitationDeclinedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatInvitationDeclined", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      //ASSERT_EQ(evt.jid, bob.config.settings.username + "@" + bob.config.settings.domain);
      ASSERT_EQ(evt.reason, "decline");
   }
}

TEST_F(XmppMultiUserChatModuleTest, DISABLED_InviteMoreThanOnce) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bob.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   XmppMultiUserChatHandle aliceChat = alice.mucManager->create(alice.handle);

   alice.mucManager->join(aliceChat, "", alice.config.nick);

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_TRUE(!evt.room.empty());
      //ASSERT_TRUE(evt.isNewRoom);
   }

   alice.mucManager->invite(aliceChat, bob.config.bare(), "invite");

   XmppMultiUserChatHandle bobChat;

   {
      MultiUserChatInvitationReceivedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatInvitationReceived", 5000, CPCAPI2::test::AlwaysTruePred(), bobChat, evt));
      ASSERT_EQ(bobChat, aliceChat + 1);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_EQ(evt.reason, "invite");
      bob.mucManager->accept(bobChat, bob.config.nick);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bobChat);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_EQ(evt.nickname, alice.config.nick);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      //ASSERT_EQ(evt.jid, bob.config.settings.username + "@" + bob.config.settings.domain);
      ASSERT_EQ(evt.nickname, bob.config.nick);
   }

   alice.mucManager->invite(aliceChat, bob.config.bare(), "reinvite");

   {
      XmppMultiUserChatHandle h;
      MultiUserChatInvitationReceivedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatInvitationReceived", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_EQ(bobChat, h);
      ASSERT_EQ(evt.reason, "reinvite");
   }

   // bliu: TODO quit and rejoin the same chat?
}

TEST_F(XmppMultiUserChatModuleTest, InviteMultipleParticipants) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");
   XmppTestAccount carol("carol");

   auto initialization = [](XmppTestAccount& account) {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(account.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, account.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   };

   auto aliceEvent = std::async(std::launch::async, [&]() {
      initialization(alice);
   });

   auto bobEvent = std::async(std::launch::async, [&]() {
      initialization(bob);
   });

   auto carolEvent = std::async(std::launch::async, [&]() {
      initialization(carol);
   });

   waitFor3(aliceEvent, bobEvent, carolEvent);

   XmppMultiUserChatHandle aliceChat = alice.mucManager->create(alice.handle);

   alice.mucManager->join(aliceChat, "", alice.config.nick);

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_TRUE(evt.isNewRoom);
   }

   alice.mucManager->invite(aliceChat, bob.config.bare(), "invite bob");

   XmppMultiUserChatHandle bobChat;

   {
      MultiUserChatInvitationReceivedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatInvitationReceived", 5000, CPCAPI2::test::AlwaysTruePred(), bobChat, evt));
      ASSERT_EQ(bobChat, aliceChat + 1);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_EQ(evt.reason, "invite bob");
      bob.mucManager->accept(bobChat, bob.config.nick);
   }

   alice.mucManager->invite(aliceChat, carol.config.bare(), "invite carol");

   XmppMultiUserChatHandle carolChat;

   {
      MultiUserChatInvitationReceivedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(carol.events, "XmppMultiUserChatHandler::onMultiUserChatInvitationReceived", 5000, CPCAPI2::test::AlwaysTruePred(), carolChat, evt));
      ASSERT_EQ(carolChat, aliceChat + 2);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_EQ(evt.reason, "invite carol");
      carol.mucManager->accept(carolChat, carol.config.nick);
   }

   auto verifyParticipants = [&](XmppMultiUserChatHandle handle, XmppTestAccount& a1, XmppTestAccount& a2, XmppTestAccount& a3) {
      for (int i = 0; i < 2; ++i)
      {
         XmppMultiUserChatHandle h;
         ParticipantAddedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(a1.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(h, handle);
         //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
         ASSERT_TRUE(evt.nickname == a2.config.nick || evt.nickname == a3.config.nick);
      }
   };

   auto aliceEvent1 = std::async(std::launch::async, [&]() {
      verifyParticipants(aliceChat, alice, bob, carol);
   });

   auto bobEvent1 = std::async(std::launch::async, [&]() {
      verifyParticipants(bobChat, bob, alice, carol);
   });

   auto carolEvent1 = std::async(std::launch::async, [&]() {
      verifyParticipants(carolChat, carol, alice, bob);
   });

   waitFor3(aliceEvent1, bobEvent1, carolEvent1);

   // bliu: TODO quit and rejoin the same chat?
}

#if 0 // disabled until <continue> is widely supported
TEST_F(XmppMultiUserChatModuleTest, Continue) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   cpc::string thread;

   auto aliceEvent = std::async(std::launch::async, [&]() {
      XmppChat::XmppChatHandle chat = alice.chat->createChat(alice.handle);
      alice.chat->addParticipant(chat, bob.config.bare());
      alice.chat->start(chat);

      XmppChat::XmppChatHandle h;
      XmppChat::NewChatEvent evt1;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt1));

      alice.chat->sendMessage(chat, "1");

      XmppChat::SendMessageSuccessEvent evt2;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onSendMessageSuccess", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt2));
      thread = evt2.threadId;

      {
         XmppAccountHandle h;
         ServiceAvailabilityEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(h, alice.handle);
         ASSERT_TRUE(evt.available);
         ASSERT_TRUE(!evt.service.empty());
      }
   });

   auto bobEvent = std::async(std::launch::async, [&]() {
      XmppChat::XmppChatHandle h;
      XmppChat::NewChatEvent evt1;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt1));
      bob.chat->accept(h);

      XmppChat::NewMessageEvent evt2;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewMessage", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt2));

      {
         XmppAccountHandle h;
         ServiceAvailabilityEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(h, bob.handle);
         ASSERT_TRUE(evt.available);
         ASSERT_TRUE(!evt.service.empty());
      }
   });

   waitFor2(aliceEvent, bobEvent);

   XmppMultiUserChatHandle aliceChat = alice.mucManager->create(alice.handle, false);

   alice.mucManager->join(aliceChat, "", alice.config.nick);

   {
      XmppMultiUserChatHandle h;
      MultiUserChatConfigurationRequestedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatConfigurationRequested", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);

      auto _configurations = evt.configurations;

      for (auto& item : _configurations.items)
      {
         if (item.name == "muc#roomconfig_whois")
         {
            item.values[0] = "anyone";
         }
      }

      alice.mucManager->setConfigurations(aliceChat, _configurations);
   }

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_TRUE(evt.isNewRoom);
   }

   //{
   //   XmppMultiUserChatHandle h;
   //   MultiUserChatConfigurationRequestedEvent evt;
   //   ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatConfigurationRequested", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   //   ASSERT_EQ(aliceChat, h);
   //}

   alice.mucManager->invite(aliceChat, bob.config.bare(), "invite", thread);

   XmppMultiUserChatHandle bobChat;

   {
      MultiUserChatInvitationReceivedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatInvitationReceived", 5000, CPCAPI2::test::AlwaysTruePred(), bobChat, evt));
      ASSERT_EQ(bobChat, aliceChat + 1);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_EQ(evt.reason, "invite");
      bob.mucManager->accept(bobChat, bob.config.nick);
   }

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(bobChat, h);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_TRUE(!evt.isNewRoom);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bobChat);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_EQ(evt.nickname, alice.config.name);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      //ASSERT_EQ(evt.jid, bob.config.settings.username + "@" + bob.config.settings.domain);
      ASSERT_EQ(evt.nickname, bob.config.name);
   }

   alice.mucManager->sendMessage(aliceChat, "2", "");

   {
      XmppMultiUserChatHandle h;
      MultiUserChatNewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatNewMessage", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.threadId, thread);
   }

   XmppMultiUserChatState mucState;
   alice.mucStateManager->getState(aliceChat, mucState);
   ASSERT_TRUE(!mucState.participants.empty());
}
#endif

TEST_F(XmppMultiUserChatModuleTest, BasicChatPlain)
{
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onServiceAvailability", 10000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onServiceAvailability", 10000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bob.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   cpc::string room = "";
   XmppMultiUserChatHandle aliceChat = alice.mucManager->create(alice.handle, room);

   RoomConfig rc;
   rc.createIfNotExisting = true;
   rc.isInstant = true;
   alice.mucManager->join(aliceChat, rc, alice.config.nick, "", "seconds:0");

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 50000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_TRUE(evt.isNewRoom);
   }

   alice.mucManager->invite(aliceChat, bob.config.bare(), "invite");

   XmppMultiUserChatHandle bobChat;

   {
      MultiUserChatInvitationReceivedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatInvitationReceived", 5000, CPCAPI2::test::AlwaysTruePred(), bobChat, evt));
      //ASSERT_EQ(bobChat, aliceChat + 1);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_EQ(evt.reason, "invite");
      bob.mucManager->accept(bobChat, bob.config.nick, "seconds:0");
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bobChat);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_EQ(evt.nickname, alice.config.nick);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      //ASSERT_EQ(evt.jid, bob.config.settings.username + "@" + bob.config.settings.domain);
      ASSERT_EQ(evt.nickname, bob.config.nick);
   }

   alice.mucManager->sendMessage(aliceChat, "plain", "");

   {
      XmppMultiUserChatHandle h;
      MultiUserChatNewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatNewMessage", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, aliceChat);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_EQ(evt.nickname, alice.config.nick);
      ASSERT_EQ(evt.plain, "plain");
   }

   {
      XmppMultiUserChatHandle h;
      MultiUserChatNewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatNewMessage", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bobChat);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_EQ(evt.nickname, alice.config.nick);
      ASSERT_EQ(evt.plain, "plain");
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   bob.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(3000));
}

TEST_F(XmppMultiUserChatModuleTest, XmppBasicReaction) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onServiceAvailability", 10000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onServiceAvailability", 10000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bob.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   cpc::string room = "";
   XmppMultiUserChatHandle aliceChat = alice.mucManager->create(alice.handle, room);

   RoomConfig rc;
   rc.createIfNotExisting = true;
   rc.isInstant = true;
   alice.mucManager->join(aliceChat, rc, alice.config.nick, "", "seconds:0");

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 50000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_TRUE(evt.isNewRoom);
   }

   alice.mucManager->invite(aliceChat, bob.config.bare(), "invite");

   XmppMultiUserChatHandle bobChat;

   {
      MultiUserChatInvitationReceivedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatInvitationReceived", 5000, CPCAPI2::test::AlwaysTruePred(), bobChat, evt));
      //ASSERT_EQ(bobChat, aliceChat + 1);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_EQ(evt.reason, "invite");
      bob.mucManager->accept(bobChat, bob.config.nick, "seconds:0");
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bobChat);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_EQ(evt.nickname, alice.config.nick);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      //ASSERT_EQ(evt.jid, bob.config.settings.username + "@" + bob.config.settings.domain);
      ASSERT_EQ(evt.nickname, bob.config.nick);
   }

   alice.mucManager->sendMessage(aliceChat, "plain", "");

   cpc::string sentMsgID;
   {
      XmppMultiUserChatHandle h;
      MultiUserChatNewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatNewMessage", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, aliceChat);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_EQ(evt.nickname, alice.config.nick);
      ASSERT_EQ(evt.plain, "plain");
      sentMsgID = evt.messageId;
   }

   {
      XmppMultiUserChatHandle h;
      MultiUserChatNewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatNewMessage", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bobChat);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_EQ(evt.nickname, alice.config.nick);
      ASSERT_EQ(evt.plain, "plain");
   }

   // setting two reactions
   cpc::vector<cpc::string> reactions;
   reactions.push_back("💘");
   reactions.push_back("💜");
   bob.mucManager->sendReaction(bobChat, sentMsgID, reactions);

   {
      XmppMultiUserChatHandle h;
      MultiUserChatNewReactionEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatNewReaction", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, aliceChat);
      ASSERT_EQ(evt.nickname, bob.config.nick);
      ASSERT_EQ(evt.target, sentMsgID);
      ASSERT_EQ(evt.reactions.size(), 2);
   }

   // clearing reactions
   reactions.clear();
   bob.mucManager->sendReaction(bobChat, sentMsgID, reactions);

   {
      XmppMultiUserChatHandle h;
      MultiUserChatNewReactionEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatNewReaction", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, aliceChat);
      ASSERT_EQ(evt.nickname, bob.config.nick);
      ASSERT_EQ(evt.target, sentMsgID);
      ASSERT_EQ(evt.reactions.size(), 0);
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   bob.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(3000));
}

TEST_F(XmppMultiUserChatModuleTest, XmppBasicRetraction) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onServiceAvailability", 10000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onServiceAvailability", 10000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bob.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   cpc::string room = "";
   XmppMultiUserChatHandle aliceChat = alice.mucManager->create(alice.handle, room);

   RoomConfig rc;
   rc.createIfNotExisting = true;
   rc.isInstant = true;
   alice.mucManager->join(aliceChat, rc, alice.config.nick, "", "seconds:0");

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 50000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_TRUE(evt.isNewRoom);
   }

   alice.mucManager->invite(aliceChat, bob.config.bare(), "invite");

   XmppMultiUserChatHandle bobChat;

   {
      MultiUserChatInvitationReceivedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatInvitationReceived", 5000, CPCAPI2::test::AlwaysTruePred(), bobChat, evt));
      //ASSERT_EQ(bobChat, aliceChat + 1);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_EQ(evt.reason, "invite");
      bob.mucManager->accept(bobChat, bob.config.nick, "seconds:0");
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bobChat);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_EQ(evt.nickname, alice.config.nick);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      //ASSERT_EQ(evt.jid, bob.config.settings.username + "@" + bob.config.settings.domain);
      ASSERT_EQ(evt.nickname, bob.config.nick);
   }

   cpc::string sentMsgID;
   XmppMultiUserChatMessageHandle cm1 = alice.mucManager->sendMessage(aliceChat, "plain", "");
   {
      XmppMultiUserChatHandle h;
      CPCAPI2::XmppMultiUserChat::SendMessageSuccessEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onSendMessageSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.message, cm1);
      ASSERT_NE(evt.messageId, cpc::string(""));
      sentMsgID = evt.messageId;
   }

   {
      XmppMultiUserChatHandle h;
      MultiUserChatNewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatNewMessage", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, aliceChat);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_EQ(evt.nickname, alice.config.nick);
      ASSERT_EQ(evt.plain, "plain");
      ASSERT_TRUE(evt.message != 0);
      // we expect a message ID
      ASSERT_EQ(evt.messageId, sentMsgID);
      // and in this case the origin ID should be the same
      ASSERT_EQ(evt.originId, sentMsgID);
   }

   {
      XmppMultiUserChatHandle h;
      MultiUserChatNewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatNewMessage", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bobChat);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_EQ(evt.nickname, alice.config.nick);
      ASSERT_EQ(evt.plain, "plain");
      ASSERT_TRUE(evt.message != 0);
      // we expect a message ID
      ASSERT_EQ(evt.messageId, sentMsgID);
      // and in this case the origin ID should be the same
      ASSERT_EQ(evt.originId, sentMsgID);
   }

   // retracting the sent message
   bob.mucManager->sendMessageRetraction(bobChat, sentMsgID);

   {
      XmppMultiUserChatHandle h;
      MultiUserChatNewMessageRetractionEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatNewMessageRetraction", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, aliceChat);
      ASSERT_EQ(evt.nickname, bob.config.nick);
      ASSERT_EQ(evt.target, sentMsgID);
   }

   // send again for already retracted message
   bob.mucManager->sendMessageRetraction(bobChat, sentMsgID);

   {
      XmppMultiUserChatHandle h;
      MultiUserChatNewMessageRetractionEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatNewMessageRetraction", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, aliceChat);
      ASSERT_EQ(evt.nickname, bob.config.nick);
      ASSERT_EQ(evt.target, sentMsgID);
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   bob.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(3000));
}

TEST_F(XmppMultiUserChatModuleTest, JoinNetworkChange) {
   XmppTestAccount alice("alice");

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   XmppMultiUserChatHandle aliceChat = alice.mucManager->create(alice.handle);

   alice.mucManager->join(aliceChat, "", alice.config.nick, "", "seconds:0");

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_TRUE(evt.isNewRoom);
   }

   alice.disconnectNetwork(TestEnvironmentConfig::dockerContainerized());
   
   std::this_thread::sleep_for(std::chrono::seconds(2));
   
   alice.connectNetwork(TestEnvironmentConfig::dockerContainerized());

   for (;;)
   {
      XmppAccountHandle h;
      XmppAccountStatusChangedEvent evt;
      ASSERT_TRUE(alice.events->expectEvent(__LINE__, "XmppAccountHandler::onAccountStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));

      if (evt.accountStatus == CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status_Connected)
      {
         break;
      }
   }
}

TEST_F(XmppMultiUserChatModuleTest, JoinSocketProblemReconnect) {

   if (!TestEnvironmentConfig::dockerContainerized())
   {
      GTEST_SKIP() << "Only supported on docker linux runs at the moment";
   }

#if _WIN32
   GTEST_SKIP() << "Only supported on docker linux runs at the moment";
#endif // _WIN32


   XmppTestAccount alice("alice");
   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   XmppMultiUserChatHandle aliceChat = alice.mucManager->create(alice.handle);

   alice.mucManager->join(aliceChat, "", alice.config.nick, "", "seconds:0");

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_TRUE(evt.isNewRoom);
   }

   test::NetworkUtils::shutdownTcpSockets();

   for (;;)
   {
      XmppAccountHandle h;
      XmppAccountStatusChangedEvent evt;
      ASSERT_TRUE(alice.events->expectEvent(__LINE__, "XmppAccountHandler::onAccountStatusChanged", 60000, CPCAPI2::test::AlwaysTruePred(), h, evt));

      if (evt.accountStatus == CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status_Connected)
      {
         break;
      }
   }
}



static void generateJwt(const resip::Data& p8file, const resip::Data& userIdentity, resip::Data& jwt)
{
   std::map<resip::Data, resip::Data> pubClaims;
   pubClaims["cp_user"] = userIdentity;
   CPCAPI2::AuthServer::JwtUtils::GenerateJWT(p8file, "CPCAPI2::AuthServer", pubClaims, 86400, jwt);
}

#if USE_CPCAPI2_JSON_TESTS
TEST_F(XmppMultiUserChatModuleTest, BasicChatPlain_JSON)
{
   XmppTestCloudAccount alice("alice");
   alice.enable();

   XmppTestAccount bob("bob");

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onServiceAvailability", 10000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onServiceAvailability", 10000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bob.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   cpc::string room = "";
   XmppMultiUserChatHandle aliceChat = alice.mucJson->create(alice.handle, room);

   RoomConfig rc;
   rc.createIfNotExisting = true;
   rc.isInstant = true;
   alice.mucJson->join(aliceChat, rc, alice.config.nick, "", "seconds:0");

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 50000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_TRUE(evt.isNewRoom);
   }

   alice.mucJson->invite(aliceChat, bob.config.bare(), "invite");

   XmppMultiUserChatHandle bobChat;

   {
      MultiUserChatInvitationReceivedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatInvitationReceived", 5000, CPCAPI2::test::AlwaysTruePred(), bobChat, evt));
      //ASSERT_EQ(bobChat, aliceChat + 1);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_EQ(evt.reason, "invite");
      bob.mucManager->accept(bobChat, bob.config.nick, "seconds:0");
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bobChat);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_EQ(evt.nickname, alice.config.nick);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      //ASSERT_EQ(evt.jid, bob.config.settings.username + "@" + bob.config.settings.domain);
      ASSERT_EQ(evt.nickname, bob.config.nick);
   }

   alice.mucJson->sendMessage(aliceChat, "plain", "");

   {
      XmppMultiUserChatHandle h;
      MultiUserChatNewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatNewMessage", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, aliceChat);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_EQ(evt.nickname, alice.config.nick);
      ASSERT_EQ(evt.plain, "plain");
   }

   {
      XmppMultiUserChatHandle h;
      MultiUserChatNewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatNewMessage", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bobChat);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_EQ(evt.nickname, alice.config.nick);
      ASSERT_EQ(evt.plain, "plain");
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   bob.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(3000));
}
#endif // USE_CPCAPI2_JSON_TESTS

TEST_F(XmppMultiUserChatModuleTest, DISABLED_BasicChatHtml) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bob.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   XmppMultiUserChatHandle aliceChat = alice.mucManager->create(alice.handle);

   alice.mucManager->join(aliceChat, "", alice.config.nick, "", "seconds:0");

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_TRUE(evt.isNewRoom);
   }

   alice.mucManager->invite(aliceChat, bob.config.bare(), "invite");

   XmppMultiUserChatHandle bobChat;

   {
      MultiUserChatInvitationReceivedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatInvitationReceived", 5000, CPCAPI2::test::AlwaysTruePred(), bobChat, evt));
      ASSERT_EQ(bobChat, aliceChat + 1);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_EQ(evt.reason, "invite");
      bob.mucManager->accept(bobChat, bob.config.nick, "seconds:0");
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bobChat);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_EQ(evt.nickname, alice.config.nick);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      //ASSERT_EQ(evt.jid, bob.config.settings.username + "@" + bob.config.settings.domain);
      ASSERT_EQ(evt.nickname, bob.config.nick);
   }

   alice.mucManager->sendMessage(aliceChat, "", "html");

   {
      XmppMultiUserChatHandle h;
      MultiUserChatNewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatNewMessage", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, aliceChat);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_EQ(evt.nickname, alice.config.nick);
      ASSERT_EQ(evt.html, "html");
   }

   {
      XmppMultiUserChatHandle h;
      MultiUserChatNewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatNewMessage", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bobChat);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_EQ(evt.nickname, alice.config.nick);
      ASSERT_EQ(evt.html, "html");
   }
}

TEST_F(XmppMultiUserChatModuleTest, DISABLED_Kick) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bob.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   XmppMultiUserChatHandle aliceChat = alice.mucManager->create(alice.handle);

   alice.mucManager->join(aliceChat, "", alice.config.nick);

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_TRUE(evt.isNewRoom);
   }

   alice.mucManager->invite(aliceChat, bob.config.bare(), "invite");

   XmppMultiUserChatHandle bobChat;

   {
      MultiUserChatInvitationReceivedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatInvitationReceived", 5000, CPCAPI2::test::AlwaysTruePred(), bobChat, evt));
      ASSERT_EQ(bobChat, aliceChat + 1);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_EQ(evt.reason, "invite");
      bob.mucManager->accept(bobChat, bob.config.nick);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bobChat);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_EQ(evt.nickname, alice.config.nick);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      //ASSERT_EQ(evt.jid, bob.config.settings.username + "@" + bob.config.settings.domain);
      ASSERT_EQ(evt.nickname, bob.config.nick);
   }

   alice.mucManager->kick(aliceChat, "bob", "kick");

   {
      XmppMultiUserChatHandle h;
      LocalUserLeftEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onLocalUserLeft", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(bobChat, h);
      ASSERT_EQ(evt.reason, "kick");
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantRemovedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantRemoved", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.nickname, bob.config.nick);
      //ASSERT_EQ(evt.reason, "kick");
   }
}

TEST_F(XmppMultiUserChatModuleTest, DISABLED_KickForbidden) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bob.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   XmppMultiUserChatHandle aliceChat = alice.mucManager->create(alice.handle);

   alice.mucManager->join(aliceChat, "", alice.config.nick);

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_TRUE(evt.isNewRoom);
   }

   alice.mucManager->invite(aliceChat, bob.config.bare(), "invite");

   XmppMultiUserChatHandle bobChat;

   {
      MultiUserChatInvitationReceivedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatInvitationReceived", 5000, CPCAPI2::test::AlwaysTruePred(), bobChat, evt));
      ASSERT_EQ(bobChat, aliceChat + 1);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_EQ(evt.reason, "invite");
      bob.mucManager->accept(bobChat, bob.config.nick);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bobChat);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_EQ(evt.nickname, alice.config.nick);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      //ASSERT_EQ(evt.jid, bob.config.settings.username + "@" + bob.config.settings.domain);
      ASSERT_EQ(evt.nickname, bob.config.nick);
   }

   bob.mucManager->kick(bobChat, "alice", "kick");

   {
      XmppMultiUserChatHandle h;
      MultiUserChatErrorEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatError", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(bobChat, h);
      ASSERT_EQ(evt.type, OtherError);
      ASSERT_EQ(evt.error, "Operation failed");
   }
}

TEST_F(XmppMultiUserChatModuleTest, DISABLED_Ban) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bob.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   XmppMultiUserChatHandle aliceChat = alice.mucManager->create(alice.handle);

   alice.mucManager->join(aliceChat, "", alice.config.nick);

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_TRUE(evt.isNewRoom);
   }

   alice.mucManager->invite(aliceChat, bob.config.bare(), "invite");

   XmppMultiUserChatHandle bobChat;

   {
      MultiUserChatInvitationReceivedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatInvitationReceived", 5000, CPCAPI2::test::AlwaysTruePred(), bobChat, evt));
      ASSERT_EQ(bobChat, aliceChat + 1);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_EQ(evt.reason, "invite");
      bob.mucManager->accept(bobChat, bob.config.nick);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantSelfUpdatedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onParticipantSelfUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(bobChat, h);
      ASSERT_EQ(evt.state.role, RoleParticipant);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bobChat);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_EQ(evt.nickname, alice.config.nick);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      //ASSERT_EQ(evt.jid, bob.config.settings.username + "@" + bob.config.settings.domain);
      ASSERT_EQ(evt.nickname, bob.config.nick);
   }

   alice.mucManager->ban(aliceChat, "bob", "ban");

   {
      XmppMultiUserChatHandle h;
      ParticipantSelfUpdatedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onParticipantSelfUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(bobChat, h);
      ASSERT_TRUE(evt.state.isBanned);
      ASSERT_EQ(evt.reason, "ban");
   }

   {
      XmppMultiUserChatHandle h;
      LocalUserLeftEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onLocalUserLeft", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(bobChat, h);
      ASSERT_EQ(evt.reason, "ban");
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantUpdatedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.nickname, bob.config.nick);
      ASSERT_TRUE(evt.state.isBanned);
      ASSERT_EQ(evt.reason, "ban");
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantRemovedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantRemoved", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.nickname, bob.config.nick);
      //ASSERT_EQ(evt.reason, "ban");
   }
}

TEST_F(XmppMultiUserChatModuleTest, DISABLED_BanForbidden) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bob.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   XmppMultiUserChatHandle aliceChat = alice.mucManager->create(alice.handle);

   alice.mucManager->join(aliceChat, "", alice.config.nick);

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_TRUE(evt.isNewRoom);
   }

   alice.mucManager->invite(aliceChat, bob.config.bare(), "invite");

   XmppMultiUserChatHandle bobChat;

   {
      MultiUserChatInvitationReceivedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatInvitationReceived", 5000, CPCAPI2::test::AlwaysTruePred(), bobChat, evt));
      ASSERT_EQ(bobChat, aliceChat + 1);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_EQ(evt.reason, "invite");
      bob.mucManager->accept(bobChat, bob.config.nick);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bobChat);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_EQ(evt.nickname, alice.config.nick);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      //ASSERT_EQ(evt.jid, bob.config.settings.username + "@" + bob.config.settings.domain);
      ASSERT_EQ(evt.nickname, bob.config.nick);
   }

   bob.mucManager->ban(bobChat, "alice", "ban");

   {
      XmppMultiUserChatHandle h;
      MultiUserChatErrorEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatError", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(bobChat, h);
      ASSERT_EQ(evt.type, OtherError);
      ASSERT_EQ(evt.error, "Operation failed");
   }
}

TEST_F(XmppMultiUserChatModuleTest, DISABLED_ChangeNickname) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bob.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   XmppMultiUserChatHandle aliceChat = alice.mucManager->create(alice.handle);

   alice.mucManager->join(aliceChat, "", alice.config.nick);

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_TRUE(evt.isNewRoom);
   }

   alice.mucManager->invite(aliceChat, bob.config.bare(), "invite");

   XmppMultiUserChatHandle bobChat;

   {
      MultiUserChatInvitationReceivedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatInvitationReceived", 5000, CPCAPI2::test::AlwaysTruePred(), bobChat, evt));
      ASSERT_EQ(bobChat, aliceChat + 1);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_EQ(evt.reason, "invite");
      bob.mucManager->accept(bobChat, bob.config.nick);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bobChat);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_EQ(evt.nickname, alice.config.nick);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      //ASSERT_EQ(evt.jid, bob.config.settings.username + "@" + bob.config.settings.domain);
      ASSERT_EQ(evt.nickname, bob.config.nick);
   }

   alice.mucManager->changeNickname(aliceChat, "nickname");

   {
      XmppMultiUserChatHandle h;
      ParticipantRemovedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onParticipantRemoved", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(bobChat, h);
      ASSERT_EQ(evt.nickname, alice.config.nick);
      //ASSERT_EQ(evt.reason, "alice");
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(bobChat, h);
      ASSERT_EQ(evt.nickname, "nickname");
      ASSERT_EQ(evt.state.presence, PresenceType_Available);
   }

   alice.mucManager->changeNickname(aliceChat, bob.config.name); // duplicated nickname

   {
      XmppMultiUserChatHandle h;
      MultiUserChatErrorEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatError", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.type, NicknameConflict);
      //ASSERT_EQ(evt.reason, "alice");
   }
}

TEST_F(XmppMultiUserChatModuleTest, DISABLED_ChangeSubject) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bob.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   XmppMultiUserChatHandle aliceChat = alice.mucManager->create(alice.handle);

   alice.mucManager->join(aliceChat, "", alice.config.nick);

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_TRUE(evt.isNewRoom);
   }

   alice.mucManager->invite(aliceChat, bob.config.bare(), "invite");

   XmppMultiUserChatHandle bobChat;

   {
      MultiUserChatInvitationReceivedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatInvitationReceived", 5000, CPCAPI2::test::AlwaysTruePred(), bobChat, evt));
      ASSERT_EQ(bobChat, aliceChat + 1);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_EQ(evt.reason, "invite");
      bob.mucManager->accept(bobChat, bob.config.nick);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bobChat);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_EQ(evt.nickname, alice.config.nick);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      //ASSERT_EQ(evt.jid, bob.config.settings.username + "@" + bob.config.settings.domain);
      ASSERT_EQ(evt.nickname, bob.config.nick);
   }

   alice.mucManager->changeSubject(aliceChat, "alice");

   {
      XmppMultiUserChatHandle h;
      MultiUserChatSubjectChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatSubjectChanged", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.nickname, alice.config.nick);
      ASSERT_EQ(evt.subject, "alice");
   }

   {
      XmppMultiUserChatHandle h;
      MultiUserChatSubjectChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatSubjectChanged", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(bobChat, h);
      ASSERT_EQ(evt.nickname, alice.config.nick);
      ASSERT_EQ(evt.subject, "alice");
   }
}

TEST_F(XmppMultiUserChatModuleTest, DISABLED_Presence) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bob.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   XmppMultiUserChatHandle aliceChat = alice.mucManager->create(alice.handle);

   alice.mucManager->join(aliceChat, "", alice.config.nick);

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_TRUE(evt.isNewRoom);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantSelfUpdatedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantSelfUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain); // bliu: TODO change from another resource??
      ASSERT_EQ(evt.state.presence, PresenceType_Available);
      //ASSERT_EQ(evt.state.message, "");
   }

   alice.mucManager->invite(aliceChat, bob.config.bare(), "invite");

   XmppMultiUserChatHandle bobChat;

   {
      MultiUserChatInvitationReceivedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatInvitationReceived", 5000, CPCAPI2::test::AlwaysTruePred(), bobChat, evt));
      ASSERT_EQ(bobChat, aliceChat + 1);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_EQ(evt.reason, "invite");
      bob.mucManager->accept(bobChat, bob.config.nick);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bobChat);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_EQ(evt.nickname, alice.config.nick);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      //ASSERT_EQ(evt.jid, bob.config.settings.username + "@" + bob.config.settings.domain);
      ASSERT_EQ(evt.nickname, bob.config.nick);
   }

   alice.mucManager->publishPresence(aliceChat, PresenceType_Chat, "alice");

   {
      XmppMultiUserChatHandle h;
      ParticipantUpdatedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onParticipantUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(bobChat, h);
      ASSERT_EQ(evt.nickname, alice.config.nick);
      ASSERT_EQ(evt.state.presence, PresenceType_Chat);
      ASSERT_EQ(evt.state.message, "alice");
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantSelfUpdatedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantSelfUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain); // bliu: TODO change from another resource??
      ASSERT_EQ(evt.state.presence, PresenceType_Chat);
      ASSERT_EQ(evt.state.message, "alice");
   }
}

TEST_F(XmppMultiUserChatModuleTest, DISABLED_HistoryRequest) {
   XmppTestAccount alice("alice");

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   XmppMultiUserChatHandle aliceChat = alice.mucManager->create(alice.handle);

   alice.mucManager->join(aliceChat, "everyone", alice.config.nick, "", "message:0");

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.room, "everyone");
      ASSERT_TRUE(!evt.isNewRoom);
   }

   alice.mucManager->sendMessage(aliceChat, "message", "");

   {
      XmppMultiUserChatHandle h;
      MultiUserChatNewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatNewMessage", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.nickname, alice.config.nick);
      ASSERT_EQ(evt.plain, "message");
      ASSERT_TRUE(evt.timestamp != 0);
   }

   alice.mucManager->leave(aliceChat, "leave");

   {
      XmppMultiUserChatHandle h;
      LocalUserLeftEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onLocalUserLeft", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.reason, "leave");
   }

   aliceChat = alice.mucManager->create(alice.handle);

   alice.mucManager->join(aliceChat, "everyone", alice.config.nick, "", "message:1");

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.room, "everyone");
      ASSERT_TRUE(!evt.isNewRoom);
   }

   time_t timestamp;

   {
      XmppMultiUserChatHandle h;
      MultiUserChatNewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatNewMessage", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.nickname, alice.config.nick);
      ASSERT_EQ(evt.plain, "message");
      timestamp = evt.timestamp;
   }

   alice.mucManager->leave(aliceChat, "leave");

   {
      XmppMultiUserChatHandle h;
      LocalUserLeftEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onLocalUserLeft", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.reason, "leave");
   }

   aliceChat = alice.mucManager->create(alice.handle);

   alice.mucManager->join(aliceChat, "everyone", alice.config.nick, "", "since:" + cpc::to_string(static_cast<int>(timestamp - 1)));

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.room, "everyone");
      ASSERT_TRUE(!evt.isNewRoom);
   }

   {
      XmppMultiUserChatHandle h;
      MultiUserChatNewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatNewMessage", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.timestamp, timestamp);
      ASSERT_EQ(evt.plain, "message");
      ASSERT_EQ(evt.nickname, alice.config.nick);
   }
}

TEST_F(XmppMultiUserChatModuleTest, DISABLED_RoomState) {
   XmppTestAccount alice("alice");

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   XmppMultiUserChatHandle aliceChat = alice.mucManager->create(alice.handle);

   alice.mucManager->join(aliceChat, "everyone", alice.config.nick, "", "message:0");

   cpc::string creation;

   {
      XmppMultiUserChatHandle h;
      MultiUserChatRoomStateChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatRoomStateChanged", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.state.name, "everyone");
      ASSERT_EQ(evt.state.anonymousMode, 1);
      creation = evt.state.creation;
   }

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.room, "everyone");
      ASSERT_TRUE(!evt.isNewRoom);
   }

   alice.mucManager->leave(aliceChat, "leave");

   {
      XmppMultiUserChatHandle h;
      LocalUserLeftEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onLocalUserLeft", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.reason, "leave");
   }

   std::tm tt = { 0 };
   sscanf(creation.c_str(), "%4d%2d%2dT%2d:%2d:%2d", &tt.tm_year, &tt.tm_mon, &tt.tm_mday, &tt.tm_hour, &tt.tm_min, &tt.tm_sec);

   tt.tm_year -= 1900;
   tt.tm_mon -= 1;

   aliceChat = alice.mucManager->create(alice.handle);

   alice.mucManager->join(aliceChat, "everyone", alice.config.nick, "", "since:" + cpc::to_string(static_cast<int>(_timegm(&tt))));

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.room, "everyone");
      ASSERT_TRUE(!evt.isNewRoom);
   }

   {
      XmppMultiUserChatHandle h;
      MultiUserChatNewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatNewMessage", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_TRUE(!evt.plain.empty());
   }
}

TEST_F(XmppMultiUserChatModuleTest, IsComposingNotification) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bob.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   RoomConfig config;
   config.createIfNotExisting = true;
   config.isInstant = true;
   XmppMultiUserChatHandle aliceChat = alice.mucManager->create(alice.handle, cpc::string(""));

   alice.mucManager->join(aliceChat, config, alice.config.nick);

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_TRUE(evt.isNewRoom);
   }

   alice.mucManager->invite(aliceChat, bob.config.bare(), "invite");

   XmppMultiUserChatHandle bobChat;

   {
      MultiUserChatInvitationReceivedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatInvitationReceived", 5000, CPCAPI2::test::AlwaysTruePred(), bobChat, evt));
      ASSERT_EQ(bobChat, aliceChat + 1);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_EQ(evt.reason, "invite");
      bob.mucManager->accept(bobChat, bob.config.nick);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bobChat);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_EQ(evt.nickname, alice.config.nick);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      //ASSERT_EQ(evt.jid, bob.config.settings.username + "@" + bob.config.settings.domain);
      ASSERT_EQ(evt.nickname, bob.config.nick);
   }

   alice.mucManager->setIsComposingMessage(aliceChat, 90, 3);

   {
      XmppMultiUserChatHandle h;
      ParticipantChatStateEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onParticipantChatStateReceived", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bobChat);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_EQ(evt.nickname, alice.config.nick);
      ASSERT_EQ(evt.state, XmppChat::IsComposingMessageState_Active);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantChatStateEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onParticipantChatStateReceived", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bobChat);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_EQ(evt.nickname, alice.config.nick);
      ASSERT_EQ(evt.state, XmppChat::IsComposingMessageState_Idle);
   }
}

TEST_F(XmppMultiUserChatModuleTest, SwitchToIdleStateImmediately) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bob.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   RoomConfig config;
   config.createIfNotExisting = true;
   config.isInstant = true;
   XmppMultiUserChatHandle aliceChat = alice.mucManager->create(alice.handle, cpc::string(""));

   alice.mucManager->join(aliceChat, config, alice.config.nick);

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_TRUE(evt.isNewRoom);
   }

   alice.mucManager->invite(aliceChat, bob.config.bare(), "invite");

   XmppMultiUserChatHandle bobChat;

   {
      MultiUserChatInvitationReceivedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatInvitationReceived", 5000, CPCAPI2::test::AlwaysTruePred(), bobChat, evt));
      ASSERT_EQ(bobChat, aliceChat + 1);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_EQ(evt.reason, "invite");
      bob.mucManager->accept(bobChat, bob.config.nick);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bobChat);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_EQ(evt.nickname, alice.config.nick);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      //ASSERT_EQ(evt.jid, bob.config.settings.username + "@" + bob.config.settings.domain);
      ASSERT_EQ(evt.nickname, bob.config.nick);
   }

   alice.mucManager->setIsComposingMessage(aliceChat, 10, 0);
   alice.mucManager->setIsComposingMessage(aliceChat, 10, 0);
   alice.mucManager->setIsComposingMessage(aliceChat, 15, 15);

   {
      XmppMultiUserChatHandle h;
      ParticipantChatStateEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantChatStateReceived", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, aliceChat);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_EQ(evt.nickname, alice.config.nick);
      ASSERT_EQ(evt.state, XmppChat::IsComposingMessageState_Active);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantChatStateEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onParticipantChatStateReceived", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bobChat);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_EQ(evt.nickname, alice.config.nick);
      ASSERT_EQ(evt.state, XmppChat::IsComposingMessageState_Active);
   }

   alice.mucManager->setIsComposingMessage(aliceChat, 15, 0);
   alice.mucManager->setIsComposingMessage(aliceChat, 15, 0);

   {
      XmppMultiUserChatHandle h;
      ParticipantChatStateEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantChatStateReceived", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, aliceChat);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_EQ(evt.nickname, alice.config.nick);
      ASSERT_EQ(evt.state, XmppChat::IsComposingMessageState_Idle);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantChatStateEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onParticipantChatStateReceived", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bobChat);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_EQ(evt.nickname, alice.config.nick);
      ASSERT_EQ(evt.state, XmppChat::IsComposingMessageState_Idle);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantChatStateEvent evt;
      ASSERT_FALSE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantChatStateReceived", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantChatStateEvent evt;
      ASSERT_FALSE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onParticipantChatStateReceived", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   }
}

TEST_F(XmppMultiUserChatModuleTest, GetRoomListAfterRelogin) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");
   XmppTestAccount charlie("charlie");

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bob.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(charlie.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, charlie.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   XmppMultiUserChatHandle aliceChat = alice.mucManager->create(alice.handle);

   alice.mucManager->join(aliceChat, "", alice.config.nick);
   cpc::string creation;
   {
      XmppMultiUserChatHandle h;
      MultiUserChatRoomStateChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatRoomStateChanged", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      creation = evt.state.creation;
   }

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_TRUE(evt.isNewRoom);
   }
   
   alice.mucManager->invite(aliceChat, bob.config.bare(), "invite");
   XmppMultiUserChatHandle bobChat;
   {
      MultiUserChatInvitationReceivedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatInvitationReceived", 5000, CPCAPI2::test::AlwaysTruePred(), bobChat, evt));
      ASSERT_EQ(bobChat, aliceChat + 1);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_EQ(evt.reason, "invite");
      bob.mucManager->accept(bobChat, bob.config.nick);
   }
   
   alice.mucManager->invite(aliceChat, charlie.config.bare(), "invite");
   XmppMultiUserChatHandle charlieChat;
   {
      MultiUserChatInvitationReceivedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(charlie.events, "XmppMultiUserChatHandler::onMultiUserChatInvitationReceived", 5000, CPCAPI2::test::AlwaysTruePred(), charlieChat, evt));
      ASSERT_EQ(charlieChat, aliceChat + 2);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_EQ(evt.reason, "invite");
      charlie.mucManager->accept(charlieChat, charlie.config.nick);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
   }

   XmppMultiUserChatState mucState;
   alice.mucStateManager->getState(aliceChat, mucState);
   ASSERT_TRUE(!mucState.participants.empty());

   unsigned int roomCount = 0;
   alice.mucManager->getRoomList(alice.handle);

   {
      XmppAccountHandle h;
      RoomListRetrievedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onRoomListRetrieved", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(!evt.rooms.empty());
      roomCount = evt.rooms.size();
   }

   alice.disable();
   alice.enable();

   alice.mucManager->getRoomList(alice.handle);

   {
      XmppAccountHandle h;
      RoomListRetrievedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onRoomListRetrieved", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(!evt.rooms.empty());
      
      // .jza. since we currently share a single XMPP server for all auto test runs, room size may be constantly fluctuating due to other tests
      //ASSERT_TRUE(evt.rooms.size() == roomCount);
   }

}

TEST_F(XmppMultiUserChatModuleTest, DISABLED_ConfigRoom) {
   XmppTestAccount alice("alice");

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   XmppMultiUserChatHandle aliceChat1 = alice.mucManager->create(alice.handle, false);

   const cpc::string room = "room_" + cpc::string(resip::Random::getCryptoRandomHex(16).c_str());

   alice.mucManager->join(aliceChat1, room, alice.config.nick);

   {
      XmppMultiUserChatHandle h;
      MultiUserChatConfigurationRequestedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatConfigurationRequested", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat1, h);

      XmppMultiUserChatConfigurations _configurations = evt.configurations;

      for (auto& item : _configurations.items)
      {
         if (item.name == "muc#roomconfig_publicroom")
         {
            item.values[0] = "0";
         }
      }

      alice.mucManager->setConfigurations(aliceChat1, _configurations);
   }

   std::this_thread::sleep_for(std::chrono::seconds(3));

   alice.mucManager->getRoomList(alice.handle);

   {
      XmppAccountHandle h;
      RoomListRetrievedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onRoomListRetrieved", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);

      bool found = false;

      for (auto& r : evt.rooms)
      {
         if (r.name == room) found = true;
      }

      ASSERT_TRUE(!found);
   }

   //alice.mucManager->leave(aliceChat1);

   //XmppMultiUserChatHandle aliceChat2 = alice.mucManager->create(alice.handle, false);

   //alice.mucManager->join(aliceChat2, "room", alice.config.name);

   alice.mucManager->requestConfigurations(aliceChat1);

   {
      XmppMultiUserChatHandle h;
      MultiUserChatConfigurationRequestedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatConfigurationRequested", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat1, h);

      XmppMultiUserChatConfigurations _configurations = evt.configurations;

      for (auto& item : _configurations.items)
      {
         if (item.name == "muc#roomconfig_publicroom")
         {
            item.values[0] = "1";
         }
      }

      alice.mucManager->setConfigurations(aliceChat1, _configurations);
   }

   std::this_thread::sleep_for(std::chrono::seconds(3));

   alice.mucManager->getRoomList(alice.handle);

   {
      XmppAccountHandle h;
      RoomListRetrievedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onRoomListRetrieved", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);

      bool found = false;

      for (auto& r : evt.rooms)
      {
         if (r.name == room) found = true;
      }

      ASSERT_TRUE(found);
   }
};

TEST_F(XmppMultiUserChatModuleTest, MembersOnlyInviteAccept) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bob.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   XmppMultiUserChatHandle aliceChat = alice.mucManager->create(alice.handle, false);

   alice.mucManager->join(aliceChat, "", alice.config.nick);

   {
      XmppMultiUserChatHandle h;
      MultiUserChatConfigurationRequestedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatConfigurationRequested", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);

      auto _configurations = evt.configurations;

      for (auto& item : _configurations.items)
      {
         if (item.name == "muc#roomconfig_membersonly")
         {
            item.values[0] = "1";
         }
      }

      alice.mucManager->setConfigurations(aliceChat, _configurations);
   }

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_TRUE(evt.isNewRoom);
   }

   alice.mucManager->invite(aliceChat, bob.config.bare(), "invite");

   XmppMultiUserChatHandle bobChat;

   {
      MultiUserChatInvitationReceivedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatInvitationReceived", 5000, CPCAPI2::test::AlwaysTruePred(), bobChat, evt));
      ASSERT_EQ(bobChat, aliceChat + 1);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_EQ(evt.reason, "invite");
      bob.mucManager->accept(bobChat, bob.config.nick);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bobChat);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_EQ(evt.nickname, alice.config.nick);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      //ASSERT_EQ(evt.jid, bob.config.settings.username + "@" + bob.config.settings.domain);
      ASSERT_EQ(evt.nickname, bob.config.nick);
   }

   XmppMultiUserChatState mucState;
   alice.mucStateManager->getState(aliceChat, mucState);
   ASSERT_TRUE(!mucState.participants.empty());
}

TEST_F(XmppMultiUserChatModuleTest, DISABLED_MembersOnlyJoinFailure) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bob.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   XmppMultiUserChatHandle aliceChat = alice.mucManager->create(alice.handle, false);

   const cpc::string room = "room_" + cpc::string(resip::Random::getCryptoRandomHex(16).c_str());

   alice.mucManager->join(aliceChat, room, alice.config.nick);

   {
      XmppMultiUserChatHandle h;
      MultiUserChatConfigurationRequestedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatConfigurationRequested", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);

      auto _configurations = evt.configurations;

      for (auto& item : _configurations.items)
      {
         if (item.name == "muc#roomconfig_membersonly")
         {
            item.values[0] = "1";
         }
      }

      alice.mucManager->setConfigurations(aliceChat, _configurations);
   }

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_TRUE(evt.isNewRoom);
   }

   XmppMultiUserChatHandle bobChat = bob.mucManager->create(bob.handle);

   bob.mucManager->join(bobChat, room, bob.config.nick);

   {
      MultiUserChatErrorEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatError", 5000, CPCAPI2::test::AlwaysTruePred(), bobChat, evt));
      ASSERT_EQ(bobChat, aliceChat + 1);
      ASSERT_EQ(evt.type, NotAMember);
   }
}

TEST_F(XmppMultiUserChatModuleTest, DISABLED_MembersOnlyJoin) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bob.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   XmppMultiUserChatHandle aliceChat = alice.mucManager->create(alice.handle, false);

   const cpc::string room = "room_" + cpc::string(resip::Random::getCryptoRandomHex(16).c_str());

   alice.mucManager->join(aliceChat, room, alice.config.nick);

   {
      XmppMultiUserChatHandle h;
      MultiUserChatConfigurationRequestedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatConfigurationRequested", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);

      auto _configurations = evt.configurations;

      for (auto& item : _configurations.items)
      {
         if (item.name == "muc#roomconfig_membersonly")
         {
            item.values[0] = "1";
         }
      }

      alice.mucManager->setConfigurations(aliceChat, _configurations);
   }

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_TRUE(evt.isNewRoom);
   }

   XmppMultiUserChatConfigurationsListItem item;
   item.jid = bob.config.bare();
   item.affiliation = AffiliationMember;
   cpc::vector<XmppMultiUserChatConfigurationsListItem> items;
   items.push_back(item);

   alice.mucManager->setList(aliceChat, MemberList, items);

   XmppMultiUserChatHandle bobChat = bob.mucManager->create(bob.handle);

   bob.mucManager->join(bobChat, room, bob.config.nick);

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bobChat);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_EQ(evt.nickname, alice.config.nick);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      //ASSERT_EQ(evt.jid, bob.config.settings.username + "@" + bob.config.settings.domain);
      ASSERT_EQ(evt.nickname, bob.config.nick);
   }

   XmppMultiUserChatState mucState;
   alice.mucStateManager->getState(aliceChat, mucState);
   ASSERT_TRUE(!mucState.participants.empty());
}

TEST_F(XmppMultiUserChatModuleTest, MembersOnlyRemove) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bob.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   XmppMultiUserChatHandle aliceChat = alice.mucManager->create(alice.handle, false);

   alice.mucManager->join(aliceChat, "", alice.config.nick);

   {
      XmppMultiUserChatHandle h;
      MultiUserChatConfigurationRequestedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatConfigurationRequested", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);

      auto _configurations = evt.configurations;

      for (auto& item : _configurations.items)
      {
         if (item.name == "muc#roomconfig_membersonly")
         {
            item.values[0] = "1";
         }
      }

      alice.mucManager->setConfigurations(aliceChat, _configurations);
   }

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_TRUE(evt.isNewRoom);
   }

   alice.mucManager->invite(aliceChat, bob.config.bare(), "invite");

   XmppMultiUserChatHandle bobChat;

   {
      MultiUserChatInvitationReceivedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatInvitationReceived", 5000, CPCAPI2::test::AlwaysTruePred(), bobChat, evt));
      ASSERT_EQ(bobChat, aliceChat + 1);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_EQ(evt.reason, "invite");
      bob.mucManager->accept(bobChat, bob.config.nick);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bobChat);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_EQ(evt.nickname, alice.config.nick);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      //ASSERT_EQ(evt.jid, bob.config.settings.username + "@" + bob.config.settings.domain);
      ASSERT_EQ(evt.nickname, bob.config.nick);
   }

   XmppMultiUserChatState mucState;
   alice.mucStateManager->getState(aliceChat, mucState);
   ASSERT_TRUE(!mucState.participants.empty());

   alice.mucManager->changeJidAffiliation(aliceChat, bob.config.bare(), AffiliationNone, "remove");

   {
      XmppMultiUserChatHandle h;
      LocalUserLeftEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onLocalUserLeft", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(bobChat, h);
      //ASSERT_EQ(evt.reason, "remove"); // bliu: the reason is not passed on
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantRemovedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantRemoved", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.nickname, bob.config.nick);
   }
}

#if (CPCAPI2_BRAND_XMPP_IM_COMMAND_MODULE == 1)
TEST_F(XmppMultiUserChatModuleTest, IMCommand) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bob.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   XmppMultiUserChatHandle aliceChat = alice.mucManager->create(alice.handle);

   const cpc::string room = "room_" + cpc::string(resip::Random::getCryptoRandomHex(16).c_str());

   alice.mucManager->join(aliceChat, room, alice.config.nick, "", "seconds:0");

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_TRUE(evt.isNewRoom);
   }

#if 0
   alice.mucManager->invite(aliceChat, bob.config.bare(), "invite");

   XmppMultiUserChatHandle bobChat;

   {
      MultiUserChatInvitationReceivedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatInvitationReceived", 5000, CPCAPI2::test::AlwaysTruePred(), bobChat, evt));
      ASSERT_EQ(bobChat, aliceChat + 1);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_EQ(evt.reason, "invite");
      bob.mucManager->accept(bobChat, bob.config.nick, "seconds:0");
   }
#else
   XmppMultiUserChatHandle bobChat = bob.mucManager->create(bob.handle);

   bob.mucManager->join(bobChat, room, bob.config.nick, "", "seconds:0");

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(bobChat, h);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_TRUE(!evt.isNewRoom);
   }
#endif

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bobChat);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_EQ(evt.nickname, alice.config.nick);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      //ASSERT_EQ(evt.jid, bob.config.settings.username + "@" + bob.config.settings.domain);
      ASSERT_EQ(evt.nickname, bob.config.nick);
   }

   alice.imCommandManager->sendMultiUserChatIMCommand(aliceChat, 2903, "test");

   {
      XmppMultiUserChatHandle h;
      XmppIMCommand::MultiUserChatIMCommandReceivedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppIMCommand::XmppMultiUserChatIMCommandHandler::onIMCommandReceived", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, aliceChat);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      //ASSERT_EQ(evt.remote, alice.config.name);
      ASSERT_EQ(evt.payload, "test");
   }

   //{
   //   XmppMultiUserChatHandle h;
   //   MultiUserChatNewMessageEvent evt;
   //   ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatNewMessage", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   //   ASSERT_EQ(h, bobChat);
   //   //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
   //   ASSERT_EQ(evt.nickname, alice.config.name);
   //   ASSERT_EQ(evt.plain, "plain");
   //}
}
#endif // CPCAPI2_BRAND_XMPP_IM_COMMAND_MODULE

// Test case for OBELISK-4523.
TEST_F(XmppMultiUserChatModuleTest, XmppDisableAndDestroyAccount) {
   XmppTestAccount alice("alice");
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   
   XmppMultiUserChatHandle aliceChat = alice.mucManager->create(alice.handle);
   alice.mucManager->join(aliceChat, "", alice.config.nick);
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   alice.account->disable(alice.handle);
   alice.account->destroy(alice.handle);

   // Wait up to 2 min for the crash.
   std::this_thread::sleep_for(std::chrono::milliseconds(120000));

   // Manually release alice.
   delete alice.events;
#if (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE==1)
   delete alice.jsonApiClientEvents;
#endif
#if (CPCAPI2_BRAND_JSON_API_SERVER_MODULE==1)
   delete alice.jsonApiServerEvents;
#endif
#if (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE==1)
   delete alice.agentJsonEvents;
#endif
#if (CPCAPI2_BRAND_PUSH_NOTIFICATION_CLIENT_MODULE==1)
   delete alice.pushJsonEvents;
#endif
#if (CPCAPI2_BRAND_CLOUD_CONNECTOR_MODULE==1)
   delete alice.cloudConnectorEvents;
#endif
#if (CPCAPI2_BRAND_MESSAGESTORE_MODULE==1)
   delete alice.messageStoreEvents;
#endif
   alice.setInitialized(false);

   Phone::release(alice.phone);
}

TEST_F(XmppMultiUserChatModuleTest, BasicChatWithIsComposingNotification) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");
   XmppTestAccount carol("carol");

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bob.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(carol.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, carol.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   XmppMultiUserChatHandle aliceChat = alice.mucManager->create(alice.handle, cpc::string(""));

   RoomConfig config;
   config.isInstant = true;
   config.createIfNotExisting = true;
   alice.mucManager->join(aliceChat, config, alice.config.nick);

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_TRUE(evt.isNewRoom);
   }

   alice.mucManager->invite(aliceChat, bob.config.bare(), "invite");
   XmppMultiUserChatHandle bobChat;

   {
      MultiUserChatInvitationReceivedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatInvitationReceived", 5000, CPCAPI2::test::AlwaysTruePred(), bobChat, evt));
      ASSERT_EQ(bobChat, aliceChat + 1);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_EQ(evt.reason, "invite");
      bob.mucManager->accept(bobChat, bob.config.nick);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      //ASSERT_EQ(evt.jid, bob.config.settings.username + "@" + bob.config.settings.domain);
      ASSERT_EQ(evt.nickname, bob.config.nick);
   }

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bobChat);
      ASSERT_FALSE(evt.isNewRoom);
   }

   alice.mucManager->invite(aliceChat, carol.config.bare(), "invite");
   XmppMultiUserChatHandle carolChat;

   {
      MultiUserChatInvitationReceivedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(carol.events, "XmppMultiUserChatHandler::onMultiUserChatInvitationReceived", 5000, CPCAPI2::test::AlwaysTruePred(), carolChat, evt));
      ASSERT_EQ(carolChat, aliceChat + 2);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_EQ(evt.reason, "invite");
      carol.mucManager->accept(carolChat, carol.config.nick);
   }

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(carol.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, carolChat);
      ASSERT_FALSE(evt.isNewRoom);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      //ASSERT_EQ(evt.jid, carol.config.settings.username + "@" + carol.config.settings.domain);
      ASSERT_EQ(evt.nickname, carol.config.nick);
   }

   bob.mucManager->setIsComposingMessage(bobChat, 10, 15);

   // make sure things happen in a controlled order
   std::this_thread::sleep_for(std::chrono::seconds(1));

   carol.mucManager->setIsComposingMessage(carolChat, 10, 15);

   // make sure things happen in a controlled order
   std::this_thread::sleep_for(std::chrono::seconds(1));

   {
       // Wait for the isComposing notification [Active] (from Bob)
       ParticipantChatStateEvent evt;
       ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantChatStateReceived", 5000, CPCAPI2::test::AlwaysTruePred(), aliceChat, evt));
       ASSERT_EQ(evt.nickname, bob.config.nick);
       ASSERT_EQ(evt.state, XmppChat::IsComposingMessageState_Active);
   }

   {
      // Wait for the isComposing notification [Active] (from Carol)
      ParticipantChatStateEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantChatStateReceived", 5000, CPCAPI2::test::AlwaysTruePred(), aliceChat, evt));
      ASSERT_EQ(evt.nickname, carol.config.nick);
      ASSERT_EQ(evt.state, XmppChat::IsComposingMessageState_Active);
   }

   {
       // Wait for the isComposing notification [Active] (from Bob)
       ParticipantChatStateEvent evt;
       ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantChatStateReceived", 10000, CPCAPI2::test::AlwaysTruePred(), aliceChat, evt));
       ASSERT_EQ(evt.nickname, bob.config.nick);
       ASSERT_EQ(evt.state, XmppChat::IsComposingMessageState_Active);
   }

   {
       // Wait for the isComposing notification [Active] (from Carol)
       ParticipantChatStateEvent evt;
       ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantChatStateReceived", 10000, CPCAPI2::test::AlwaysTruePred(), aliceChat, evt));
       ASSERT_EQ(evt.nickname, carol.config.nick);
       ASSERT_EQ(evt.state, XmppChat::IsComposingMessageState_Active);
   }

   bob.mucManager->sendMessage(bobChat, "bob", "");

   // wait for carol switching to [Idle]
   std::this_thread::sleep_for(std::chrono::seconds(15));

   carol.mucManager->sendMessage(carolChat, "carol", "");

   {
      // Wait for the isComposing notification [Idle] (from Bob)
      ParticipantChatStateEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantChatStateReceived", 20000, CPCAPI2::test::AlwaysTruePred(), aliceChat, evt));
      ASSERT_EQ(evt.nickname, bob.config.nick);
      ASSERT_EQ(evt.state, XmppChat::IsComposingMessageState_Idle);
   }

   {
      // Wait for the isComposing notification [Idle timeout] (from Carol)
      ParticipantChatStateEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantChatStateReceived", 20000, CPCAPI2::test::AlwaysTruePred(), aliceChat, evt));
      ASSERT_EQ(evt.nickname, carol.config.nick);
      ASSERT_EQ(evt.state, XmppChat::IsComposingMessageState_Idle);
   }

   {
       // No more isComposing notification
       ParticipantChatStateEvent evt;
       ASSERT_FALSE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantChatStateReceived", 20000, CPCAPI2::test::AlwaysTruePred(), aliceChat, evt));
   }
}

TEST_F(XmppMultiUserChatModuleTest, OBELISK_5694) {
    XmppTestAccount alice("alice");

    {
        XmppAccountHandle h;
        ServiceAvailabilityEvent evt;
        ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
        ASSERT_EQ(h, alice.handle);
        ASSERT_TRUE(evt.available);
        ASSERT_TRUE(!evt.service.empty());
    }

    XmppMultiUserChatHandle aliceChat = alice.mucManager->create(alice.handle, cpc::string(""));

    alice.mucManager->setIsComposingMessage(aliceChat, 0, 0);

    XmppMultiUserChatHandle h;
    MultiUserChatErrorEvent evt;
    ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatError", 20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
    ASSERT_EQ(h, aliceChat);
    ASSERT_EQ(evt.type, OtherError);
}

TEST_F(XmppMultiUserChatModuleTest, DeliveryReadReceiptsWithIsComposingNotification) {
   XmppTestAccount alice("alice", Account_NoInit);
   alice.config.settings.enableGroupChatDeliveryReceipts = true;
   alice.config.settings.enableGroupChatReadReceipts = true;
   alice.enable();
   XmppTestAccount bob("bob", Account_NoInit);
   bob.config.settings.enableGroupChatDeliveryReceipts = true;
   bob.config.settings.enableGroupChatReadReceipts = true;
   bob.enable();
   XmppTestAccount carol("carol", Account_NoInit);
   carol.config.settings.enableGroupChatDeliveryReceipts = true;
   carol.config.settings.enableGroupChatReadReceipts = true;
   carol.enable();

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bob.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(carol.events, "XmppMultiUserChatHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, carol.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   XmppMultiUserChatHandle aliceChat = alice.mucManager->create(alice.handle, cpc::string(""));

   RoomConfig config;
   config.isInstant = true;
   config.createIfNotExisting = true;
   alice.mucManager->join(aliceChat, config, alice.config.nick);

   cpc::string roomJid;
   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_TRUE(evt.isNewRoom);
      roomJid = evt.roomjid;
   }

   alice.mucManager->invite(aliceChat, bob.config.bare(), "invite");
   XmppMultiUserChatHandle bobChat;

   {
      MultiUserChatInvitationReceivedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatInvitationReceived", 5000, CPCAPI2::test::AlwaysTruePred(), bobChat, evt));
      ASSERT_EQ(bobChat, aliceChat + 1);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_EQ(evt.reason, "invite");
      bob.mucManager->accept(bobChat, bob.config.nick);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      //ASSERT_EQ(evt.jid, bob.config.settings.username + "@" + bob.config.settings.domain);
      ASSERT_EQ(evt.nickname, bob.config.nick);
   }

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bobChat);
      ASSERT_FALSE(evt.isNewRoom);
   }

   alice.mucManager->invite(aliceChat, carol.config.bare(), "invite");
   XmppMultiUserChatHandle carolChat;

   {
      MultiUserChatInvitationReceivedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(carol.events, "XmppMultiUserChatHandler::onMultiUserChatInvitationReceived", 5000, CPCAPI2::test::AlwaysTruePred(), carolChat, evt));
      ASSERT_EQ(carolChat, aliceChat + 2);
      //ASSERT_EQ(evt.jid, alice.config.settings.username + "@" + alice.config.settings.domain);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_EQ(evt.reason, "invite");
      carol.mucManager->accept(carolChat, carol.config.nick);
   }

   
   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(carol.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, carolChat);
      ASSERT_FALSE(evt.isNewRoom);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      //ASSERT_EQ(evt.jid, carol.config.settings.username + "@" + carol.config.settings.domain);
      ASSERT_EQ(evt.nickname, carol.config.nick);
   }

   bob.mucManager->setIsComposingMessage(bobChat, 10, 15);

   // make sure things happen in a controlled order
   std::this_thread::sleep_for(std::chrono::seconds(1));

   carol.mucManager->setIsComposingMessage(carolChat, 10, 15);

   // make sure things happen in a controlled order
   std::this_thread::sleep_for(std::chrono::seconds(1));

   {
       // Wait for the isComposing notification [Active] (from Bob)
       ParticipantChatStateEvent evt;
       ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantChatStateReceived", 5000, CPCAPI2::test::AlwaysTruePred(), aliceChat, evt));
       ASSERT_EQ(evt.nickname, bob.config.nick);
       ASSERT_EQ(evt.state, XmppChat::IsComposingMessageState_Active);
   }

   {
      // Wait for the isComposing notification [Active] (from Carol)
      ParticipantChatStateEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantChatStateReceived", 5000, CPCAPI2::test::AlwaysTruePred(), aliceChat, evt));
      ASSERT_EQ(evt.nickname, carol.config.nick);
      ASSERT_EQ(evt.state, XmppChat::IsComposingMessageState_Active);
   }

   {
       // Wait for the isComposing notification [Active] (from Bob)
       ParticipantChatStateEvent evt;
       ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantChatStateReceived", 10000, CPCAPI2::test::AlwaysTruePred(), aliceChat, evt));
       ASSERT_EQ(evt.nickname, bob.config.nick);
       ASSERT_EQ(evt.state, XmppChat::IsComposingMessageState_Active);
   }

   {
       // Wait for the isComposing notification [Active] (from Carol)
       ParticipantChatStateEvent evt;
       ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantChatStateReceived", 10000, CPCAPI2::test::AlwaysTruePred(), aliceChat, evt));
       ASSERT_EQ(evt.nickname, carol.config.nick);
       ASSERT_EQ(evt.state, XmppChat::IsComposingMessageState_Active);
   }

   bob.mucManager->sendMessage(bobChat, "bob", "");

   cpc::string bobMsgId;
   cpc::string carolMsgId;
   bool aliceReceipt = false;
   bool bobReceipt = false;
   bool carolReceipt = false;
   int i;
   
   {
       SendMessageSuccessEvent evt;
       ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onSendMessageSuccess", 10000, CPCAPI2::test::AlwaysTruePred(), bobChat, evt));
       bobMsgId = evt.messageId;
   }

   for (i = 0; i < 2 ; ++i)
   {
      MessageDeliveredEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMessageDelivered", 10000, CPCAPI2::test::AlwaysTruePred(), bobChat, evt));
      ASSERT_EQ(evt.messageId, bobMsgId);
      if (evt.from == roomJid + "/" + alice.config.nick) aliceReceipt = true;
      if (evt.from == roomJid + "/" + carol.config.nick) carolReceipt = true;
   }
   ASSERT_TRUE(aliceReceipt && carolReceipt);

   {
       MessageDeliveredEvent evt;
       ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMessageDelivered", 10000, CPCAPI2::test::AlwaysTruePred(), aliceChat, evt));
       ASSERT_EQ(evt.from, roomJid + "/" + carol.config.nick);
       ASSERT_EQ(evt.messageId, bobMsgId);
   }
   {
       MessageDeliveredEvent evt;
       ASSERT_TRUE(cpcExpectEvent(carol.events, "XmppMultiUserChatHandler::onMessageDelivered", 10000, CPCAPI2::test::AlwaysTruePred(), carolChat, evt));
       ASSERT_EQ(evt.from, roomJid + "/" + alice.config.nick);
       ASSERT_EQ(evt.messageId, bobMsgId);
   }

   // wait for carol switching to [Idle]
   std::this_thread::sleep_for(std::chrono::seconds(15));

   carol.mucManager->sendMessage(carolChat, "carol", "");

   {
       SendMessageSuccessEvent evt;
       ASSERT_TRUE(cpcExpectEvent(carol.events, "XmppMultiUserChatHandler::onSendMessageSuccess", 10000, CPCAPI2::test::AlwaysTruePred(), carolChat, evt));
       carolMsgId = evt.messageId;
   }

   aliceReceipt = false;
   bobReceipt = false;
   for (i = 0; i < 2 ; ++i)
   {
      MessageDeliveredEvent evt;
      ASSERT_TRUE(cpcExpectEvent(carol.events, "XmppMultiUserChatHandler::onMessageDelivered", 10000, CPCAPI2::test::AlwaysTruePred(), carolChat, evt));
      ASSERT_EQ(evt.messageId, carolMsgId);
      if (evt.from == roomJid + "/" + alice.config.nick) aliceReceipt = true;
      if (evt.from == roomJid + "/" + bob.config.nick) bobReceipt = true;
   }
   ASSERT_TRUE(aliceReceipt && bobReceipt);

   {
       MessageDeliveredEvent evt;
       ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMessageDelivered", 10000, CPCAPI2::test::AlwaysTruePred(), aliceChat, evt));
       ASSERT_EQ(evt.from, roomJid + "/" + bob.config.nick);
       ASSERT_EQ(evt.messageId, carolMsgId);
   }
   {
       MessageDeliveredEvent evt;
       ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMessageDelivered", 10000, CPCAPI2::test::AlwaysTruePred(), bobChat, evt));
       ASSERT_EQ(evt.from, roomJid + "/" + alice.config.nick);
       ASSERT_EQ(evt.messageId, carolMsgId);
   }
   
   {
      // Wait for the isComposing notification [Idle] (from Bob)
      ParticipantChatStateEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantChatStateReceived", 20000, CPCAPI2::test::AlwaysTruePred(), aliceChat, evt));
      ASSERT_EQ(evt.nickname, bob.config.nick);
      ASSERT_EQ(evt.state, XmppChat::IsComposingMessageState_Idle);
   }

   {
      // Wait for the isComposing notification [Idle timeout] (from Carol)
      ParticipantChatStateEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantChatStateReceived", 20000, CPCAPI2::test::AlwaysTruePred(), aliceChat, evt));
      ASSERT_EQ(evt.nickname, carol.config.nick);
      ASSERT_EQ(evt.state, XmppChat::IsComposingMessageState_Idle);
   }

   alice.mucManager->notifyMessageRead(aliceChat, carolMsgId);

   {
       MessageReadEvent evt;
       ASSERT_TRUE(cpcExpectEvent(carol.events, "XmppMultiUserChatHandler::onMessageRead", 10000, CPCAPI2::test::AlwaysTruePred(), carolChat, evt));
       ASSERT_EQ(evt.from, roomJid + "/" + alice.config.nick);
       ASSERT_EQ(evt.messageId, carolMsgId);
   }
   {
       MessageReadEvent evt;
       ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMessageRead", 10000, CPCAPI2::test::AlwaysTruePred(), bobChat, evt));
       ASSERT_EQ(evt.from, roomJid + "/" + alice.config.nick);
       ASSERT_EQ(evt.messageId, carolMsgId);
   }

   {
       // No more isComposing notification
       ParticipantChatStateEvent evt;
       ASSERT_FALSE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantChatStateReceived", 20000, CPCAPI2::test::AlwaysTruePred(), aliceChat, evt));
   }
}

TEST_F(XmppMultiUserChatModuleTest, BasicChatReplaceMessage)
{
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onServiceAvailability", 10000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   {
      XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onServiceAvailability", 10000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bob.handle);
      ASSERT_TRUE(evt.available);
      ASSERT_TRUE(!evt.service.empty());
   }

   cpc::string room = "";
   XmppMultiUserChatHandle aliceChat = alice.mucManager->create(alice.handle, room);

   RoomConfig rc;
   rc.createIfNotExisting = true;
   rc.isInstant = true;
   alice.mucManager->join(aliceChat, rc, alice.config.nick, "", "seconds:0");

   {
      XmppMultiUserChatHandle h;
      MultiUserChatReadyEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatReady", 50000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_TRUE(evt.isNewRoom);
   }

   alice.mucManager->invite(aliceChat, bob.config.bare(), "invite");

   XmppMultiUserChatHandle bobChat;

   {
      MultiUserChatInvitationReceivedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatInvitationReceived", 5000, CPCAPI2::test::AlwaysTruePred(), bobChat, evt));
      ASSERT_TRUE(!evt.room.empty());
      ASSERT_EQ(evt.reason, "invite");
      bob.mucManager->accept(bobChat, bob.config.nick, "seconds:0");
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bobChat);
      ASSERT_EQ(evt.nickname, alice.config.nick);
   }

   {
      XmppMultiUserChatHandle h;
      ParticipantAddedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onParticipantAdded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.nickname, bob.config.nick);
   }

   cpc::string messageID;
   alice.mucManager->sendMessage(aliceChat, "plain bad", "");

   {
      XmppMultiUserChatHandle h;
      MultiUserChatNewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatNewMessage", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, aliceChat);
      ASSERT_EQ(evt.nickname, alice.config.nick);
      ASSERT_EQ(evt.plain, "plain bad");
      ASSERT_TRUE(evt.replaces.empty());
      messageID = evt.messageId;
   }

   {
      XmppMultiUserChatHandle h;
      MultiUserChatNewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatNewMessage", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bobChat);
      ASSERT_EQ(evt.nickname, alice.config.nick);
      ASSERT_EQ(evt.plain, "plain bad");
      ASSERT_TRUE(evt.replaces.empty());
   }

   alice.mucManager->replaceMessage(aliceChat, messageID, "plain good", "");

   {
      XmppMultiUserChatHandle h;
      MultiUserChatNewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatNewMessage", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, aliceChat);
      ASSERT_EQ(evt.nickname, alice.config.nick);
      ASSERT_EQ(evt.plain, "plain good");
      ASSERT_EQ(evt.replaces, messageID);
   }

   {
      XmppMultiUserChatHandle h;
      MultiUserChatNewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatNewMessage", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bobChat);
      ASSERT_EQ(evt.nickname, alice.config.nick);
      ASSERT_EQ(evt.plain, "plain good");
      ASSERT_EQ(evt.replaces, messageID);
   }

   alice.mucManager->sendMessage(aliceChat, "plain medicore", "");

   {
      XmppMultiUserChatHandle h;
      MultiUserChatNewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppMultiUserChatHandler::onMultiUserChatNewMessage", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, aliceChat);
      ASSERT_EQ(evt.nickname, alice.config.nick);
      ASSERT_EQ(evt.plain, "plain medicore");
      ASSERT_TRUE(evt.replaces.empty());
   }

   {
      XmppMultiUserChatHandle h;
      MultiUserChatNewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppMultiUserChatHandler::onMultiUserChatNewMessage", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, bobChat);
      ASSERT_EQ(evt.nickname, alice.config.nick);
      ASSERT_EQ(evt.plain, "plain medicore");
      ASSERT_TRUE(evt.replaces.empty());
   }

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   bob.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(3000));
}
}  // namespace

#endif
