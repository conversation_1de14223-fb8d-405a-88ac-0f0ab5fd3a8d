#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#include "cpcapi2_test_fixture.h"
#include "test_framework/cpcapi2_test_framework.h"
#include "test_call_events.h"
#include "test_account_events.h"
#include "impl/account/SipAccountHandlerInternal.h"
#include "test_events.h"
#include "webrtc/modules/video_capture/video_capture_impl.h"
#include "test_framework/network_utils.h"
#include "impl/call/SipConversationManagerInternal.h"
#include "impl/util/ThrottledInvoker.h"
#include <future>
#include <stdlib.h>
#include <memory>

using namespace CPCAPI2;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::SipConversation;
using namespace CPCAPI2::Media;
using namespace CPCAPI2::test;

#define LOAD 1

class VideoCallTests : public CpcapiAutoTest
{
public:
   VideoCallTests() {}
   virtual ~VideoCallTests() {}
};

TEST_F(VideoCallTests, AudioCallAddVideo) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   alice.initiateVideo();
   bob.initiateVideo();
   
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(1, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      // add video
      alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
      alice.conversation->sendMediaChangeRequest(aliceCall);
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });

      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
     
      SipConversationHandle bobCall;
      assertNewConversationIncoming_ex(bob, &bobCall, alice.config.uri(), [](const NewConversationEvent& evt){
         ASSERT_EQ(1, evt.remoteMediaInfo.size());
      });
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(1, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertConversationMediaChangeRequest_ex(bob, bobCall, [](const ConversationMediaChangeRequestEvent& evt){
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.remoteMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.remoteMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.remoteMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.remoteMediaInfo[1].mediaDirection);
      });
      // accept the re-INVITE which adds video
      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      bob.conversation->accept(bobCall);
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });

      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });

   
   waitFor2(aliceEvents, bobEvents);

}

// alice attempts to upgrade an audio only call to video; bob declines the video upgrade (200 OK with m=video 0)
TEST_F(VideoCallTests, AudioCallAddVideo_Decline) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   alice.initiateVideo();
   bob.initiateVideo();
   
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(1, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      // add video
      alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
      alice.conversation->sendMediaChangeRequest(aliceCall);
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(1, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });

      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
     
      SipConversationHandle bobCall;
      assertNewConversationIncoming_ex(bob, &bobCall, alice.config.uri(), [](const NewConversationEvent& evt){
         ASSERT_EQ(1, evt.remoteMediaInfo.size());
      });
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(1, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertConversationMediaChangeRequest_ex(bob, bobCall, [](const ConversationMediaChangeRequestEvent& evt){
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.remoteMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.remoteMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.remoteMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.remoteMediaInfo[1].mediaDirection);
      });
      // accept the re-INVITE but decline upgade to video
      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, false);
      bob.conversation->accept(bobCall);
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(1, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });

      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });

   
   waitFor2(aliceEvents, bobEvents);

}

TEST_F(VideoCallTests, QueryVideoDeviceList)
{
   TestAccount alice("alice", Account_Init);
   alice.video->queryDeviceList();
   CPCAPI2::Media::VideoDeviceListUpdatedEvent evt;
   int handle = 0;
   ASSERT_TRUE(alice.mediaEvents->expectEvent("VideoHandler::onVideoDeviceListUpdated", 30000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
   if (evt.deviceInfo.size() > 0)
   {
      alice.video->setCaptureDevice(evt.deviceInfo.begin()->id);
      alice.video->startCapture();
      // todo: an event to wait for
      std::this_thread::sleep_for(std::chrono::seconds(5));
   }
}


TEST_F(VideoCallTests, AudioCallAddVideoForcedCodec) {
   TestAccount alice("alice");
   alice.enableOnlyThisVideoCodec("VP8");
   TestAccount bob("bob");
   bob.enableOnlyThisVideoCodec("H.264");

   alice.initiateVideo();
   bob.initiateVideo();

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   SipConversation::MediaInfo miAudio;
   miAudio.mediaType = SipConversation::MediaType_Audio;
   miAudio.mediaDirection = SipConversation::MediaDirection_SendReceive;
   alice.conversation->configureMedia(aliceCall, miAudio);
   SipConversation::MediaInfo miVideo;
   miVideo.mediaType = SipConversation::MediaType_Video;
   miVideo.mediaDirection = SipConversation::MediaDirection_None;
   strcpy(miVideo.videoCodec.plName, "H264");
   alice.conversation->configureMedia(aliceCall, miVideo);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]() {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_EQ(1, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      // add video
      //SipConversation::MediaInfo miVideo;
      //miVideo.mediaType = SipConversation::MediaType_Video;
      //miVideo.mediaDirection = SipConversation::MediaDirection_SendReceive;
      //strcpy(miVideo.videoCodec.plName, "H264");
      //alice.conversation->configureMedia(aliceCall, miVideo);
      alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
      alice.conversation->sendMediaChangeRequest(aliceCall);
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
         ASSERT_TRUE(resip::isEqualNoCase("H264", evt.localMediaInfo[1].videoCodec.plName));
      });

      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));

      SipConversationHandle bobCall;
      assertNewConversationIncoming_ex(bob, &bobCall, alice.config.uri(), [](const NewConversationEvent& evt) {
         ASSERT_EQ(1, evt.remoteMediaInfo.size());
      });
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_EQ(1, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertConversationMediaChangeRequest_ex(bob, bobCall, [](const ConversationMediaChangeRequestEvent& evt) {
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.remoteMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.remoteMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.remoteMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.remoteMediaInfo[1].mediaDirection);
      });
      // accept the re-INVITE which adds video
      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      bob.conversation->accept(bobCall);
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });

      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });

   
   waitFor2(aliceEvents, bobEvents);

}

class NoResetEvent
{
public:
   NoResetEvent() : state(false) {}
   NoResetEvent(const NoResetEvent& other) = delete;

   void WaitOne() {
      std::unique_lock<std::mutex> lock(sync);
      while (!state) {
         underlying.wait(lock);
      }
   }

   void Set() {
      std::unique_lock<std::mutex> lock(sync);
      state = true;
      underlying.notify_all();
   }

private:
   std::condition_variable underlying;
   std::mutex sync;
   std::atomic_bool state;
};

TEST_F(VideoCallTests, AudioCallAddVideoBothSidesGlare) {
   TestAccount alice("alice");
   //TestAccount alice("alice", Account_NoInit);
   //alice.config.settings.useOutbound = true;
   //alice.config.settings.sipTransportType = SipAccountTransport_TCP;
   //alice.config.settings.domain = "opsip.silverstar.counterpath.net";
   //alice.config.settings.outboundProxy = alice.config.settings.domain;
   //alice.init();
   //alice.enable();

   TestAccount bob("bob");
   //TestAccount bob("bob", Account_NoInit);
   //bob.config.settings.useOutbound = true;
   //bob.config.settings.sipTransportType = SipAccountTransport_TCP;
   //bob.config.settings.domain = "opsip.silverstar.counterpath.net";
   //bob.config.settings.outboundProxy = bob.config.settings.domain;
   //bob.init();
   //bob.enable();

   alice.initiateVideo();
   bob.initiateVideo();

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   NoResetEvent aliceEvt;
   NoResetEvent bobEvt;

   auto aliceEvents = std::async(std::launch::async, [&]() {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(1, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      aliceEvt.Set();
      bobEvt.WaitOne();

      // add video
      alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
      alice.conversation->sendMediaChangeRequest(aliceCall);

      // due to the glare (491), we are still audio-only
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(1, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });

      // we get a re-INVITE from the other side
      assertConversationMediaChangeRequest_ex(alice, aliceCall, [](const ConversationMediaChangeRequestEvent& evt){
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.remoteMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.remoteMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.remoteMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.remoteMediaInfo[1].mediaDirection);
      });

      // accept the re-INVITE which adds video
      alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
      alice.conversation->accept(aliceCall);

      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
      ASSERT_EQ(2, evt.localMediaInfo.size());
      ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
      ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
      ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });

      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));

      SipConversationHandle bobCall;
      assertNewConversationIncoming_ex(bob, &bobCall, alice.config.uri(), [](const NewConversationEvent& evt){
         ASSERT_EQ(1, evt.remoteMediaInfo.size());
      });
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(1, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      //std::this_thread::sleep_for(std::chrono::milliseconds(100));
      bobEvt.Set();
      aliceEvt.WaitOne();

      // add video
      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      bob.conversation->sendMediaChangeRequest(bobCall);
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });

      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });

   
   waitFor2(aliceEvents, bobEvents);

}

TEST_F(VideoCallTests, AudioCallAddVideoBestEffortEncryption) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   alice.initiateVideo();
   bob.initiateVideo();
   
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->setBestEffortMediaEncryption(aliceCall, true);
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(1, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      // add video
      alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
      alice.conversation->sendMediaChangeRequest(aliceCall);
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      // remove video
      alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, false);
      alice.conversation->sendMediaChangeRequest(aliceCall);
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(1, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      // re-add video
      alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
      alice.conversation->sendMediaChangeRequest(aliceCall);
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });

      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
     
      SipConversationHandle bobCall;
      assertNewConversationIncoming_ex(bob, &bobCall, alice.config.uri(), [](const NewConversationEvent& evt){
         ASSERT_EQ(1, evt.remoteMediaInfo.size());
      });
      bob.conversation->setBestEffortMediaEncryption(bobCall, true);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(1, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertConversationMediaChangeRequest_ex(bob, bobCall, [](const ConversationMediaChangeRequestEvent& evt){
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.remoteMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.remoteMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.remoteMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.remoteMediaInfo[1].mediaDirection);
      });
      // accept the re-INVITE which adds video
      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      bob.conversation->accept(bobCall);
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });

      // accept the removal of video
      assertConversationMediaChangeRequest_ex(bob, bobCall, [](const ConversationMediaChangeRequestEvent& evt){
         ASSERT_EQ(1, evt.remoteMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.remoteMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.remoteMediaInfo[0].mediaDirection);
      });
      bob.conversation->accept(bobCall);
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(1, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });

      // accept the re-adding of video
      assertConversationMediaChangeRequest_ex(bob, bobCall, [](const ConversationMediaChangeRequestEvent& evt){
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.remoteMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.remoteMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.remoteMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.remoteMediaInfo[1].mediaDirection);
      });
      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      bob.conversation->accept(bobCall);
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });

   
   waitFor2(aliceEvents, bobEvents);

}

TEST_F(VideoCallTests, AudioCallAddVideoMismatchedCodecs) {
   TestAccount alice("alice");
   alice.enableOnlyThisVideoCodec("H.264");
   TestAccount bob("bob");
   bob.enableOnlyThisVideoCodec("VP8");

   alice.initiateVideo();
   bob.initiateVideo();
   
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(1, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      // add video
      alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
      alice.conversation->sendMediaChangeRequest(aliceCall);
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(1, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });

      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
     
      SipConversationHandle bobCall;
      assertNewConversationIncoming_ex(bob, &bobCall, alice.config.uri(), [](const NewConversationEvent& evt){
         ASSERT_EQ(1, evt.remoteMediaInfo.size());
      });
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(1, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertConversationMediaChangeRequest_ex(bob, bobCall, [](const ConversationMediaChangeRequestEvent& evt){
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.remoteMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.remoteMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.remoteMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.remoteMediaInfo[1].mediaDirection);
      });
      // accept the re-INVITE which adds video
      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      bob.conversation->accept(bobCall);
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(1, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });

   
   waitFor2(aliceEvents, bobEvents);

}

TEST_F(VideoCallTests, AudioCallAddVideoTwiceInvalid) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   alice.initiateVideo();
   bob.initiateVideo();
   
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(1, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      // add video
      alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
      safeCout("send media change request 1");
      alice.conversation->sendMediaChangeRequest(aliceCall);

      // pause a bit (to make sure all media streams are ready)
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));

      // do it a second time (SDK should ignore this attempt)
      alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
      safeCout("send media change request 2");
      alice.conversation->sendMediaChangeRequest(aliceCall);

      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });

      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
     
      SipConversationHandle bobCall;
      assertNewConversationIncoming_ex(bob, &bobCall, alice.config.uri(), [](const NewConversationEvent& evt){
         ASSERT_EQ(1, evt.remoteMediaInfo.size());
      });
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(1, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertConversationMediaChangeRequest_ex(bob, bobCall, [](const ConversationMediaChangeRequestEvent& evt){
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.remoteMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.remoteMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.remoteMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.remoteMediaInfo[1].mediaDirection);
      });

      // pause a bit to give time for alice to make a second attempt
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      // accept the re-INVITE which adds video
      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      bob.conversation->accept(bobCall);
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });

   
   waitFor2(aliceEvents, bobEvents);

}

TEST_F(VideoCallTests, VideoOnlyCall) {

   TestAccount alice("alice");
   TestAccount bob("bob");

#if _WIN32
   HWND hwndAliceCapture = NULL;
   ASSERT_EQ(0, ViECreateWindow(hwndAliceCapture, 0, 0, 352, 288, "Alice (capture)"));
   alice.video->startCapture();
   alice.video->setLocalVideoRenderTarget(hwndAliceCapture);

   HWND hwndAliceRemote = NULL;
   ASSERT_EQ(0, ViECreateWindow(hwndAliceRemote, 356, 0, 352, 288, "Alice (incoming)"));
   alice.video->setIncomingVideoRenderTarget(hwndAliceRemote);

   HWND hwndBobCapture = NULL;
   ASSERT_EQ(0, ViECreateWindow(hwndBobCapture, 0, 292, 352, 288, "Bob (capture)"));
   bob.video->startCapture();
   bob.video->setLocalVideoRenderTarget(hwndBobCapture);

   HWND hwndBobRemote = NULL;
   ASSERT_EQ(0, ViECreateWindow(hwndBobRemote, 356, 292, 352, 288, "Bob (incoming)"));
   bob.video->setIncomingVideoRenderTarget(hwndBobRemote);
#endif // _WIN32
   
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, false);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(1, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      SipConversationHandle bobCall;
      auto val = [](const NewConversationEvent& evt){
         ASSERT_EQ(1, evt.remoteMediaInfo.size());
         ASSERT_EQ(MediaType_Video, evt.remoteMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.remoteMediaInfo[0].mediaDirection);
      };
      assertNewConversationIncoming_ex(bob, &bobCall, alice.config.uri(), val);

      bob.conversation->setMediaEnabled(bobCall, MediaType_Audio, false);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(1, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });

   
   waitFor2(aliceEvents, bobEvents);

#if _WIN32
   alice.video->stopCapture();
   alice.video->setLocalVideoRenderTarget(NULL);
   DestroyWindow(hwndAliceCapture);
   DestroyWindow(hwndAliceRemote);
   bob.video->stopCapture();
   bob.video->setLocalVideoRenderTarget(NULL);
   DestroyWindow(hwndBobCapture);
   DestroyWindow(hwndBobRemote);
#endif // _WIN32
}


TEST_F(VideoCallTests, AudioVideoCall) {

   TestAccount alice("alice");
   TestAccount bob("bob");

   alice.initiateVideo();
   bob.initiateVideo();
   
   const int callDurationSec = 15;

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, true);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::seconds(callDurationSec));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
#ifndef __linux__
      assertCallHadMedia(alice, aliceCall, true, true);
#endif
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      SipConversationHandle bobCall;
      assertNewConversationIncoming_ex(bob, &bobCall, alice.config.uri(), [](const NewConversationEvent& evt){
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
      });

      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::seconds(callDurationSec));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
#ifndef __linux__
      assertCallHadMedia(bob, bobCall, true, true);
#endif
   });

   
   waitFor2Ms(aliceEvents, bobEvents, std::chrono::seconds(callDurationSec + 10));
}


static int nowMs()
{
   unsigned long milliseconds_since_epoch =
      std::chrono::system_clock::now().time_since_epoch() / 
      std::chrono::milliseconds(1);

   return milliseconds_since_epoch;
}

struct PliTracker
{
   std::atomic_int mPliInvoked = 0;
   std::atomic_bool mComplete = false;
};


// Tests basic functionality of ThrottledInvoker which ReconConversationManagerImpl uses
// to help throttle keyframe requests sent via SIP INFO
TEST_F(VideoCallTests, ThrottledPliSender) {

   std::unique_ptr<resip::MultiReactor> reactor = std::make_unique<resip::MultiReactor>();
   reactor->start();

   PliTracker pliTracker;

   const int throttleDelayMSec = 3000;
   Utils::ThrottledInvoker* tpli = new Utils::ThrottledInvoker(reactor.get(), throttleDelayMSec);


   const int expectedFirstPliMsec = nowMs();
   ASSERT_TRUE(tpli->reqInvoke([&]()
   {
      EXPECT_GE(nowMs(), expectedFirstPliMsec);
      ++pliTracker.mPliInvoked;
   }));


   const int expectedSecondPliMsec = nowMs() + throttleDelayMSec;


   for (;;)
   {
      std::this_thread::yield();
      if (pliTracker.mPliInvoked == 1)
      {
         break;
      }
   }

   // this was requested immediately so we expect it to be processed after a throttle delay
   ASSERT_TRUE(tpli->reqInvoke([&]()
   {
      EXPECT_GE(nowMs(), expectedSecondPliMsec);
      ++pliTracker.mPliInvoked;
   }));

   // we don't expect these to be executed since they were made within [expectedThrottleDelayMSec] 
   // and will be  by previous queued request
   ASSERT_FALSE(tpli->reqInvoke([]()
   {
      FAIL();
   }));

   // we don't expect these to be executed since they were made within [expectedThrottleDelayMSec] 
   // and will be hanlded by previous queued request
   ASSERT_FALSE(tpli->reqInvoke([]()
   {
      FAIL();
   }));

   const int bufferMSec = 1000;
   const int expectedThirdPliMsec = nowMs();
   std::this_thread::sleep_for(std::chrono::milliseconds(throttleDelayMSec + bufferMSec));
   ASSERT_EQ(pliTracker.mPliInvoked, 2);

   // since this request was made after [expectedThrottleDelayMSec] we expect it to be handled immediately
   ASSERT_TRUE(tpli->reqInvoke([&]()
   {
      EXPECT_GE(nowMs(), expectedThirdPliMsec);
      ++pliTracker.mPliInvoked;
      pliTracker.mComplete = true;
   }));


   for (;;)
   {
      if (pliTracker.mComplete)
      {
         break;
      }
      std::this_thread::sleep_for(std::chrono::milliseconds(500));
   }

   ASSERT_EQ(pliTracker.mPliInvoked, 3);

   tpli->reactorSafeRelease(reactor.get());
}

// Induce a throttled request that is scheduled to be sent after 3 seconds;
// however before 3 seconds is up, destroy the ThrottledInvoker instance to
// ensure it cancels the throttled request and there is no crash
TEST_F(VideoCallTests, ThrottledPliSenderDestruct) {

   std::unique_ptr<resip::MultiReactor> reactor = std::make_unique<resip::MultiReactor>();
   reactor->start();

   PliTracker pliTracker;

   const int throttleDelayMSec = 3000;
   Utils::ThrottledInvoker* tpli = new Utils::ThrottledInvoker(reactor.get(), throttleDelayMSec);


   const int expectedFirstPliMsec = nowMs();
   ASSERT_TRUE(tpli->reqInvoke([&]()
   {
      EXPECT_GE(nowMs(), expectedFirstPliMsec);
      ++pliTracker.mPliInvoked;
   }));


   const int expectedSecondPliMsec = nowMs() + throttleDelayMSec;

   for (;;)
   {
      std::this_thread::yield();
      if (pliTracker.mPliInvoked == 1)
      {
         break;
      }
   }

   ASSERT_TRUE(tpli->reqInvoke([&]()
   {
      // although the invoke request was accepted, we don't actually expect it
      // to be invoked since we below immediately initiate destruction of this
      // ThrottledInvoker instance.
      FAIL();
   }));

   tpli->reactorSafeRelease(reactor.get());


   const int bufferMs = 1000;
   std::this_thread::sleep_for(std::chrono::milliseconds(throttleDelayMSec + bufferMs));
}


bool AudioVideoCallSpamInfoKeyFrameReq_BadLogParseFunction(const char *message, CPCAPI2::LogLevel)
{
   bool result = false;
   // we should not see this many INFO/PLI requests if the throttling is working
   if (NULL != strstr(message, "CSeq: 20 INFO")) result = true;
   return result;
}


// simulates decoder spamming keyframe requests that end up being sent via SIP INFO.
// These requests should be throttled by an instance of ThrottledInvoker.
// This test does a single check to ensure high CSeq value for SIP INFO is not seen
// as a basic check that throttling is working.
// VideoCallTests.ThrottledPliSenderDestruct* tests help to validate more cases.
TEST_F(VideoCallTests, AudioVideoCallSpamInfoKeyFrameReq) {

#if (defined(__linux__) && !defined(ANDROID))
   GTEST_SKIP() << "Too CPU intensive for Linux docker runs";
#endif

   AutoTestsLogger::ScopedSetBadLogMessageCheckFunction blm(&AudioVideoCallSpamInfoKeyFrameReq_BadLogParseFunction);

   TestAccount alice("alice");
   TestAccount bob("bob");

   alice.initiateVideo();
   bob.initiateVideo();
   
   const int callDurationSec = 15;

   SipConversationManagerInternal* cmi = dynamic_cast<SipConversationManagerInternal*>(alice.conversation);
   cmi->setNackPliEnabled(false);

   cmi = dynamic_cast<SipConversationManagerInternal*>(bob.conversation);
   cmi->setNackPliEnabled(false);


   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, true);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      int recvVideoStreamId;
      assertConversationMediaChanged_ex(alice, aliceCall, [&](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
         for (cpc::vector<MediaInfo>::const_iterator it = evt.remoteMediaInfo.begin(); it != evt.remoteMediaInfo.end(); ++it)
         {
            if (it->mediaType == MediaType_Video)
            {
               recvVideoStreamId = it->mediaStreamId;
               break;
            }
         }
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::seconds(callDurationSec));

      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
#ifndef __linux__
      assertCallHadMedia(alice, aliceCall, true, true);
#endif
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      SipConversationHandle bobCall;
      assertNewConversationIncoming_ex(bob, &bobCall, alice.config.uri(), [](const NewConversationEvent& evt){
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
      });

      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->accept(bobCall));

      int recvVideoStreamId;
      assertConversationMediaChanged_ex(bob, bobCall, [&](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);

         for (cpc::vector<MediaInfo>::const_iterator it = evt.remoteMediaInfo.begin(); it != evt.remoteMediaInfo.end(); ++it)
         {
            if (it->mediaType == MediaType_Video)
            {
               recvVideoStreamId = it->mediaStreamId;
               break;
            }
         }
      });
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::atomic_bool keepSpammingRequestKeyframe = true;
      auto spamRequestKeyframe = std::async(std::launch::async, [&] ()
      {
         for (;;)
         {
            if (!keepSpammingRequestKeyframe)
            {
               break;
            }

            std::this_thread::sleep_for(std::chrono::milliseconds(1));
            bob.video->requestKeyFrame(recvVideoStreamId);
         }
      });

      std::this_thread::sleep_for(std::chrono::seconds(callDurationSec));
      keepSpammingRequestKeyframe = false;

      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
#ifndef __linux__
      assertCallHadMedia(bob, bobCall, true, true);
#endif
   });

   waitFor2Ms(aliceEvents, bobEvents, std::chrono::seconds(callDurationSec + 10));

   ASSERT_EQ(AutoTestsLogger::instance().getBadMessagesCount(), 0);
}






TEST_F(VideoCallTests, AudioVideoCall_PauseLocalVideo) {

// only supported on Windows and Mac for now
#if !(defined(_WIN32) || (defined( __APPLE__ ) && TARGET_OS_IPHONE == 0))
#warning SkipVideoCapture() has not been implemented for this target.
   GTEST_SKIP();
#endif
   
#if _WIN32
   // needs manycam or a real camera -- can't rely on custom video capture device
   if (TestEnvironmentConfig::dockerContainerized()) GTEST_SKIP() << "Test not supported in docker windows environments";
#endif

#if defined(_WIN32)
if (TestEnvironmentConfig::dockerContainerized()) GTEST_SKIP() << "Not supported on docker windows yet; needs real camera or manycam";
#endif

   TestAccount alice("alice");
   TestAccount bob("bob");

   alice.initiateVideo();
   bob.initiateVideo();
   
   const int callDurationSec = 30;

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, true);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));

      CPCAPI2::Media::VideoCaptureStateChangedEvent evt;
      int handle = 0;
      ASSERT_TRUE(alice.mediaEvents->expectEvent("VideoHandler::onVideoCaptureStateChanged", 1000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
      ASSERT_TRUE(evt.state == CPCAPI2::Media::VideoCaptureState_Started);

      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      
      std::this_thread::sleep_for(std::chrono::seconds(callDurationSec / 3)); 
      webrtc::videocapturemodule::VideoCaptureImpl::SkipVideoCapture(true);
      
      // catching video capture failure takes 3 seconds so allow some extra
      // (3.5 worked on Windows but needed more on my Mac M1)
      ASSERT_TRUE(alice.mediaEvents->expectEvent("VideoHandler::onVideoCaptureStateChanged", 4000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
      ASSERT_TRUE(evt.state == CPCAPI2::Media::VideoCaptureState_Hung);

      std::this_thread::sleep_for(std::chrono::seconds(callDurationSec / 3));
      webrtc::videocapturemodule::VideoCaptureImpl::SkipVideoCapture(false);

      // catching capture recovery should happen almost immediately
      ASSERT_TRUE(alice.mediaEvents->expectEvent("VideoHandler::onVideoCaptureStateChanged", 1000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
      ASSERT_TRUE(evt.state == CPCAPI2::Media::VideoCaptureState_Resumed);

      std::this_thread::sleep_for(std::chrono::seconds(callDurationSec / 3));

      // there shouldn't be an event here
      ASSERT_FALSE(alice.mediaEvents->expectEvent("VideoHandler::onVideoCaptureStateChanged", 1000, CPCAPI2::test::AlwaysTruePred(), handle, evt));

      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);

      alice.video->stopCapture();   // just to test that we get the "stopped" event below
      ASSERT_TRUE(alice.mediaEvents->expectEvent("VideoHandler::onVideoCaptureStateChanged", 1000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
      ASSERT_TRUE(evt.state == CPCAPI2::Media::VideoCaptureState_Stopped);
      
#ifndef __linux__
       assertCallHadMedia(alice, aliceCall, true, true);
#endif
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      SipConversationHandle bobCall;
      assertNewConversationIncoming_ex(bob, &bobCall, alice.config.uri(), [](const NewConversationEvent& evt){
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
      });

      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::seconds(callDurationSec));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
#ifndef __linux__
      assertCallHadMedia(bob, bobCall, true, true);
#endif
   });

   
   waitFor2Ms(aliceEvents, bobEvents, std::chrono::seconds(callDurationSec + 10));
}

TEST_F(VideoCallTests, AudioVideoCall_RemoveVideo) {

   TestAccount alice("alice");
   TestAccount bob("bob");

   alice.initiateVideo();
   bob.initiateVideo();

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, true);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      
      // we get a re-INVITE from the other side to remove video
      assertConversationMediaChangeRequest_ex(alice, aliceCall, [](const ConversationMediaChangeRequestEvent& evt){
         ASSERT_EQ(1, evt.remoteMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.remoteMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.remoteMediaInfo[0].mediaDirection);
         //ASSERT_EQ(MediaType_Video, evt.remoteMediaInfo[1].mediaType);
         //ASSERT_EQ(MediaDirection_Inactive, evt.remoteMediaInfo[1].mediaDirection);
      });
      
      // accept the re-INVITE which removes video
      alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, false);
      alice.conversation->accept(aliceCall);
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(1, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         //ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         //ASSERT_EQ(MediaDirection_Inactive, evt.localMediaInfo[1].mediaDirection);
      });
      
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      //assertCallHadMedia(alice, aliceCall, true, true);
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      SipConversationHandle bobCall;
      assertNewConversationIncoming_ex(bob, &bobCall, alice.config.uri(), [](const NewConversationEvent& evt){
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
      });

      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(2000));

      assertSuccess(bob.conversation->setMediaEnabled(bobCall, MediaType_Video, false));
      assertSuccess(bob.conversation->sendMediaChangeRequest(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(1, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      //assertCallHadMedia(bob, bobCall, true, true);
   });

   
   waitFor2(aliceEvents, bobEvents);

}

// Note: passes, but currently the last re-INVITE is not correct
TEST_F(VideoCallTests, AudioVideoCall_Hold_RemoveVideo_Unhold) {

   TestAccount alice("alice");
   TestAccount bob("bob");

   alice.initiateVideo();
   bob.initiateVideo();

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, true);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      
      // bob placed call on hold (INVITE CSeq 2)
      assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendOnly);
      assertSuccess(alice.conversation->accept(aliceCall));
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_FALSE(evt.localHold);
         ASSERT_TRUE(evt.remoteHold);
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaDirection_ReceiveOnly, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaDirection_ReceiveOnly, evt.localMediaInfo[1].mediaDirection);
      });
      
      // we get a re-INVITE from the other side to remove video (INVITE CSeq 3)
      assertConversationMediaChangeRequest_ex(alice, aliceCall, [](const ConversationMediaChangeRequestEvent& evt){
         ASSERT_EQ(1, evt.remoteMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.remoteMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendOnly, evt.remoteMediaInfo[0].mediaDirection);
      });
      
      // accept the re-INVITE which removes video
      alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, false);
      alice.conversation->accept(aliceCall);
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(1, evt.localMediaInfo.size());
         ASSERT_EQ(1, evt.remoteMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_ReceiveOnly, evt.localMediaInfo[0].mediaDirection);
      });
      
      // bob placed call on off hold (INVITE CSeq 4)
      
      // note this re-INVITE appears to incorrectly have video enabled
      assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendReceive);
      assertSuccess(alice.conversation->accept(aliceCall));
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(1, evt.localMediaInfo.size());
         ASSERT_EQ(1, evt.remoteMediaInfo.size());
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      //assertCallHadMedia(alice, aliceCall, true, true);
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      SipConversationHandle bobCall;
      assertNewConversationIncoming_ex(bob, &bobCall, alice.config.uri(), [](const NewConversationEvent& evt){
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
      });

      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      
      assertSuccess(bob.conversation->hold(bobCall)); // (INVITE CSeq 2)
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
         ASSERT_TRUE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendOnly, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaDirection_SendOnly, evt.localMediaInfo[1].mediaDirection);
      });
      
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      
      // remove video // (INVITE CSeq 3)
      assertSuccess(bob.conversation->setMediaEnabled(bobCall, MediaType_Video, false));
      assertSuccess(bob.conversation->sendMediaChangeRequest(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(1, evt.localMediaInfo.size());
         ASSERT_EQ(1, evt.remoteMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendOnly, evt.localMediaInfo[0].mediaDirection);
      });
      
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      
      // note this re-INVITE appears to incorrectly have video enabled
      assertSuccess(bob.conversation->unhold(bobCall)); // (INVITE CSeq 4)
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(1, evt.localMediaInfo.size());
         ASSERT_EQ(1, evt.remoteMediaInfo.size());
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
      });
      
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      //assertCallHadMedia(bob, bobCall, true, true);
   });

   
   waitFor2(aliceEvents, bobEvents);

}

TEST_F(VideoCallTests, AudioVideoCall_SetVideoInactive_Hold_Unhold) {

   TestAccount alice("alice");
   TestAccount bob("bob");

   alice.initiateVideo();
   bob.initiateVideo();

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, true);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]() {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      // we get a re-INVITE from the other side to remove video (INVITE CSeq 2)
      assertConversationMediaChangeRequest_ex(alice, aliceCall, [](const ConversationMediaChangeRequestEvent& evt) {
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.remoteMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.remoteMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaDirection_Inactive, evt.remoteMediaInfo[1].mediaDirection);
      });

      // accept the re-INVITE which removes video
      alice.conversation->accept(aliceCall);
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaDirection_Inactive, evt.localMediaInfo[1].mediaDirection);
      });

      // bob placed call on hold (INVITE CSeq 3)
      assertConversationMediaChangeRequest_ex(alice, aliceCall, [](const ConversationMediaChangeRequestEvent& evt) {
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.remoteMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendOnly, evt.remoteMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.remoteMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_Inactive, evt.remoteMediaInfo[1].mediaDirection);
      });
      assertSuccess(alice.conversation->accept(aliceCall));
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_FALSE(evt.localHold);
         ASSERT_TRUE(evt.remoteHold);
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaDirection_ReceiveOnly, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaDirection_Inactive, evt.localMediaInfo[1].mediaDirection);
      });

      // bob placed call on off hold (INVITE CSeq 4)

      // note this re-INVITE appears to incorrectly have video enabled
      assertConversationMediaChangeRequest_ex(alice, aliceCall, [](const ConversationMediaChangeRequestEvent& evt) {
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.remoteMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.remoteMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.remoteMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_Inactive, evt.remoteMediaInfo[1].mediaDirection);
      });
      assertSuccess(alice.conversation->accept(aliceCall));
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaDirection_Inactive, evt.localMediaInfo[1].mediaDirection);
      });

      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      //assertCallHadMedia(alice, aliceCall, true, true);
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      SipConversationHandle bobCall;
      assertNewConversationIncoming_ex(bob, &bobCall, alice.config.uri(), [](const NewConversationEvent& evt) {
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
      });

      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(2000));

      // remove video by setting it to 'inactive' // (INVITE CSeq 2)
      bob.conversation->setMediaEnabledByDirection(bobCall, MediaType_Video, false);
      assertSuccess(bob.conversation->sendMediaChangeRequest(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaDirection_Inactive, evt.localMediaInfo[1].mediaDirection);
      });

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      assertSuccess(bob.conversation->hold(bobCall)); // (INVITE CSeq 3)
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
         ASSERT_TRUE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendOnly, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaDirection_Inactive, evt.localMediaInfo[1].mediaDirection);
      });

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      assertSuccess(bob.conversation->unhold(bobCall)); // (INVITE CSeq 4)
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaDirection_Inactive, evt.localMediaInfo[1].mediaDirection);
      });

      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      //assertCallHadMedia(bob, bobCall, true, true);
   });

   
   waitFor2(aliceEvents, bobEvents);

}

TEST_F(VideoCallTests, AudioVideoCallDeclineVideo) {

   TestAccount alice("alice");
   TestAccount bob("bob");

   alice.initiateVideo();
   bob.initiateVideo();

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, true);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(1, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         /*ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_None, evt.localMediaInfo[1].mediaDirection);*/
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertCallHadMedia(alice, aliceCall, true, false);
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      SipConversationHandle bobCall;
      assertNewConversationIncoming_ex(bob, &bobCall, alice.config.uri(), [](const NewConversationEvent& evt){
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
      });

      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, false);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(1, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         /*ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_None, evt.localMediaInfo[1].mediaDirection);*/
      });
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      assertCallHadMedia(bob, bobCall, true, false);
   });

   
   waitFor2(aliceEvents, bobEvents);

}

TEST_F(VideoCallTests, DISABLED_AudioVideoCall_ExternalAccount) {

   TestAccount alice("alice", Account_NoInit);
   TestAccount bob("bob", Account_NoInit);
   
   alice.config.settings.domain = "em0102.fonex.jp";
   alice.config.settings.outboundProxy = "";
   alice.config.settings.username = "2003";
   alice.config.settings.password = "2003_4m8pqjZRdTe1bkpr";
   alice.config.settings.useOutbound = false;
   alice.config.settings.useRport = false;
   const cpc::string aliceExt = "sip:<EMAIL>";
   
   bob.config.settings.domain = "em0102.fonex.jp";
   bob.config.settings.outboundProxy = "";
   bob.config.settings.username = "2004";
   bob.config.settings.password = "2004_h2BuckkT41Ki52b7";
   bob.config.settings.useOutbound = false;
   bob.config.settings.useRport = false;
   const cpc::string bobExt = "sip:<EMAIL>";
   
   bool serverInsertsRemoteRing = true;
   
   alice.init();
   alice.enable();
   
   bob.init();
   bob.enable();
   
   alice.enableOnlyThisVideoCodec("H.264");

   alice.initiateVideo();
   bob.initiateVideo();

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, true);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
   alice.conversation->addParticipant(aliceCall, bobExt);
   alice.conversation->start(aliceCall);

   int callLengthMs = 30000;

   auto aliceEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertNewConversationOutgoing_ex(alice, aliceCall, bobExt, [](const NewConversationEvent& evt)
      {
      });
      
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      
      if (serverInsertsRemoteRing)
      {
         assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      }
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      
      std::this_thread::sleep_for(std::chrono::milliseconds(callLengthMs));
      
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertCallHadMedia(alice, aliceCall, true, true);
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      SipConversationHandle bobCall;
      
      NewConversationEvent evt;
      ASSERT_TRUE(bob.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onNewConversation",
         30000, AlwaysTruePred(), bobCall, evt));


      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt)
      {
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(callLengthMs));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      assertCallHadMedia(bob, bobCall, true, true);
   });

   
   waitFor2(aliceEvents, bobEvents);

}

// OBELISK-5578
// intended to be run with SIPp scenario invite_uas_diebold_ack_problem.xml
TEST_F(VideoCallTests, DISABLED_AudioVideoCall_DieboldLostAck) {

   TestAccount alice("alice", Account_NoInit);
   
   alice.config.settings.domain = "127.0.0.1";
   alice.config.settings.outboundProxy = "127.0.0.1:7005";
   alice.config.settings.username = "2003";
   alice.config.settings.useRegistrar = false;
   
   alice.init();
   alice.enable();
   
   alice.enableOnlyThisVideoCodec("H.264");

   alice.initiateVideo();

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, true);
   //alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
   alice.conversation->addParticipant(aliceCall, "sip:foo@127.0.0.1:7005");
   alice.conversation->start(aliceCall);

   int callLengthMs = 30000;

   auto aliceEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertNewConversationOutgoing_ex(alice, aliceCall, "sip:foo@127.0.0.1", [](const NewConversationEvent& evt)
      {
      });
      
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
            
      {
         SipConversationHandle h;
         ConversationMediaChangeRequestEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent("SipConversationHandler::onConversationMediaChangeRequest",
            15000, HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));

         ASSERT_EQ((size_t)2, evt.remoteMediaInfo.size());
         ASSERT_EQ(evt.remoteMediaInfo[0].mediaDirection, MediaDirection_SendReceive);
         ASSERT_EQ(evt.remoteMediaInfo[1].mediaDirection, MediaDirection_SendReceive);
      }
      // SDK ends up dropping ACK response to this 200 OK due to below sendMediaChangeRequest
      assertSuccess(alice.conversation->accept(aliceCall));
      
      alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, true);
      alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
      alice.conversation->sendMediaChangeRequest(aliceCall);
           
      std::this_thread::sleep_for(std::chrono::milliseconds(2000000));
      
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertCallHadMedia(alice, aliceCall, true, true);
   });

   waitFor(aliceEvents);

}

// receive inbound call from sipp; intended for manual testing
TEST_F(VideoCallTests, DISABLED_AudioVideoCall_sipp) {

   TestAccount bob("bob", Account_NoInit);
   
   bob.config.settings.domain = "opsip.silverstar.counterpath.net";
   bob.config.settings.outboundProxy = "";
   bob.config.settings.username = "2003";
   bob.config.settings.useRegistrar = false;
   bob.config.settings.minSipPort = 50000;
   bob.config.settings.maxSipPort = 50010;
   
   bob.init();
   bob.enable();
   
   bob.enableOnlyThisVideoCodec("VP8");

   bob.initiateVideo();

   auto bobEvents = std::async(std::launch::async, [&] () {
     // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      SipConversationHandle bobCall;

      NewConversationEvent evt;
      ASSERT_TRUE(bob.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onNewConversation", 300000, AlwaysTruePred(), bobCall, evt)) << "missed inbound call event";
      ASSERT_EQ(ConversationType_Incoming, evt.conversationType) << "wrong conversation type, expecting conversation type Incoming";
      
      ASSERT_FALSE(evt.isCodecsMismatched) << "Codec Mismatched";

      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(25000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      //assertCallHadMedia(bob, bobCall, true, true);

      SipConversationState currState;
      ASSERT_EQ(bob.conversationState->getState(bobCall, currState), CPCAPI2::kSuccess);
      ASSERT_EQ(1, currState.statistics.videoChannels.size());
#ifndef CPCAPI2_AUTO_TESTS_NO_VIDEO_STATS
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsReceived, 0);
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsSent, 0);
#endif
      ASSERT_NE(0, currState.statistics.remoteVideoChannels.size());
      if (currState.statistics.videoChannels[0].streamDataCounters.packetsSent > 0)
      {
         ASSERT_NE(strlen(currState.statistics.videoChannels[0].encoder.plName), 0);
      }
      if (currState.statistics.videoChannels[0].streamDataCounters.packetsReceived > 0)
      {
         ASSERT_NE(strlen(currState.statistics.videoChannels[0].decoder.plName), 0);
      }
   });

   waitFor(bobEvents);

}

TEST_F(VideoCallTests, AudioVideoCallDeclineVideoWithInactive) {

   TestAccount alice("alice");
   TestAccount bob("bob");

   alice.initiateVideo();
   bob.initiateVideo();

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, true);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]() {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_Inactive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      assertSuccess(alice.conversation->hold(aliceCall));
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_TRUE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendOnly, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaDirection_Inactive, evt.localMediaInfo[1].mediaDirection);
      });
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertSuccess(alice.conversation->unhold(aliceCall));
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaDirection_Inactive, evt.localMediaInfo[1].mediaDirection);
      });


      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertCallHadMedia(alice, aliceCall, true, false);
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      SipConversationHandle bobCall;
      assertNewConversationIncoming_ex(bob, &bobCall, alice.config.uri(), [](const NewConversationEvent& evt) {
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
      });

      MediaInfo bobVideoInactive;
      bobVideoInactive.mediaType = MediaType_Video;
      bobVideoInactive.mediaDirection = MediaDirection_Inactive;
      bob.conversation->configureMedia(bobCall, bobVideoInactive);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_Inactive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      {
         SipConversationHandle h;
         ConversationMediaChangeRequestEvent evt;
         ASSERT_TRUE(bob.conversationEvents->expectEvent("SipConversationHandler::onConversationMediaChangeRequest",
            15000, HandleEqualsPred<SipConversationHandle>(bobCall), h, evt));

         ASSERT_EQ((size_t)2, evt.remoteMediaInfo.size());
         ASSERT_EQ(evt.remoteMediaInfo[0].mediaDirection, MediaDirection_SendOnly);
         ASSERT_EQ(evt.remoteMediaInfo[1].mediaDirection, MediaDirection_Inactive);
      }
      assertSuccess(bob.conversation->accept(bobCall));

      {
         SipConversationHandle h;
         ConversationMediaChangeRequestEvent evt;
         ASSERT_TRUE(bob.conversationEvents->expectEvent("SipConversationHandler::onConversationMediaChangeRequest",
            15000, HandleEqualsPred<SipConversationHandle>(bobCall), h, evt));

         ASSERT_EQ((size_t)2, evt.remoteMediaInfo.size());
         ASSERT_EQ(evt.remoteMediaInfo[0].mediaDirection, MediaDirection_SendReceive);
         ASSERT_EQ(evt.remoteMediaInfo[1].mediaDirection, MediaDirection_Inactive);
      }
      assertSuccess(bob.conversation->accept(bobCall));

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      assertCallHadMedia(bob, bobCall, true, false);
   });

   
   waitFor2(aliceEvents, bobEvents);

}

TEST_F(VideoCallTests, AudioVideoCallDeclineVideoThenStartVideo) {

   TestAccount alice("alice");
   TestAccount bob("bob");

   alice.initiateVideo();
   bob.initiateVideo();

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, true);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]() {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_EQ(1, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      assertConversationMediaChangeRequest_ex(alice, aliceCall, [](const ConversationMediaChangeRequestEvent& evt) {
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.remoteMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.remoteMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.remoteMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.remoteMediaInfo[1].mediaDirection);
      });

      assertSuccess(alice.conversation->accept(aliceCall));
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });

      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertCallHadMedia(alice, aliceCall, true, false);
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      SipConversationHandle bobCall;
      assertNewConversationIncoming_ex(bob, &bobCall, alice.config.uri(), [](const NewConversationEvent& evt) {
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
      });

      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, false);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_EQ(1, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });

      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      // enable video now
      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      assertSuccess(bob.conversation->sendMediaChangeRequest(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      assertCallHadMedia(bob, bobCall, true, false);
   });

   
   waitFor2(aliceEvents, bobEvents);

}

TEST_F(VideoCallTests, AudioVideoCallStartCapAfterCallStarted) {

   TestAccount alice("alice");
   TestAccount bob("bob");

   alice.initiateVideo();
   bob.initiateVideo();

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, true);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      alice.video->startCapture();

      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
#ifndef __linux__
      assertCallHadMedia(alice, aliceCall, true, true);
#endif
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      SipConversationHandle bobCall;
      assertNewConversationIncoming_ex(bob, &bobCall, alice.config.uri(), [](const NewConversationEvent& evt){
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
      });

      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      bob.video->startCapture();

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
#ifndef __linux__
      assertCallHadMedia(bob, bobCall, true, true);
#endif
   });

   
   waitFor2(aliceEvents, bobEvents);

}

TEST_F(VideoCallTests, DISABLED_OnlyVideoNegotiableAnswerAudio) {
   TestAccount alice("alice");
   alice.enableOnlyThisCodec("SPEEX Narrowband");
   alice.enableOnlyThisVideoCodec("VP8");
   
   TestAccount bob("bob");
   bob.enableOnlyThisCodec("G711 aLaw");
   bob.enableOnlyThisVideoCodec("VP8");
   
   alice.initiateVideo();
   bob.initiateVideo();

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, true);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);
   
   auto aliceEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(1, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[0].mediaType);
         ASSERT_FALSE(evt.remoteHold); // A problem per OBELISK-1040.
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertCallHadMedia(alice, aliceCall, false, true);
   });
   
   auto bobEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      SipConversationHandle bobCall;
      assertNewConversationIncoming_ex(bob, &bobCall, alice.config.uri(), [](const NewConversationEvent& evt){
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
      });
      bob.conversation->setMediaEnabled(bobCall, MediaType_Audio, true);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, false);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(1, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[0].mediaType);
         ASSERT_FALSE(evt.remoteHold); // A problem per OBELISK-1040.
      });
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      assertCallHadMedia(bob, bobCall, false, true);
   });
   
   
   waitFor2(aliceEvents, bobEvents);
   
}

TEST_F(VideoCallTests, DISABLED_AudioVideoCallH264ResChange) {

   TestAccount alice("alice");
   TestAccount bob("bob");

   // enable H.264
   
   alice.video->queryCodecList();
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);
   
   bob.video->queryCodecList();
   bob.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);

   alice.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   bob.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   alice.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   bob.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   alice.initiateVideo();
   bob.initiateVideo();

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, true);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]() {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendOnly);
      assertSuccess(alice.conversation->accept(aliceCall));
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_FALSE(evt.localHold);
         ASSERT_TRUE(evt.remoteHold);
      });

      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      //assertCallHadMedia(alice, aliceCall, true, true);

      SipConversationState currState;
      ASSERT_EQ(alice.conversationState->getState(aliceCall, currState), CPCAPI2::kSuccess);
      ASSERT_EQ(1, currState.statistics.videoChannels.size());
#ifndef CPCAPI2_AUTO_TESTS_NO_VIDEO_STATS
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsReceived, 0);
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsSent, 0);
      ASSERT_TRUE(currState.statistics.videoChannels[0].encoder.width > 352);
      ASSERT_TRUE(currState.statistics.videoChannels[0].encoder.height > 288);
      ASSERT_TRUE(currState.statistics.videoChannels[0].decoder.width > 352);
      ASSERT_TRUE(currState.statistics.videoChannels[0].decoder.height > 288);
#endif
      ASSERT_NE(0, currState.statistics.remoteVideoChannels.size());
      if (currState.statistics.videoChannels[0].streamDataCounters.packetsSent > 0)
      {
         ASSERT_NE(strlen(currState.statistics.videoChannels[0].encoder.plName), 0);
      }
      if (currState.statistics.videoChannels[0].streamDataCounters.packetsReceived > 0)
      {
         ASSERT_NE(strlen(currState.statistics.videoChannels[0].decoder.plName), 0);
      }
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      SipConversationHandle bobCall;
      assertNewConversationIncoming_ex(bob, &bobCall, alice.config.uri(), [](const NewConversationEvent& evt){
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
      });

      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(500));

      bob.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_Standard);
      bob.conversation->hold(bobCall);
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_TRUE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
      });

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      //assertCallHadMedia(bob, bobCall, true, true);

      SipConversationState currState;
      ASSERT_EQ(bob.conversationState->getState(bobCall, currState), CPCAPI2::kSuccess);
      ASSERT_EQ(1, currState.statistics.videoChannels.size());
#ifndef CPCAPI2_AUTO_TESTS_NO_VIDEO_STATS
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsReceived, 0);
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsSent, 0);
      ASSERT_TRUE(currState.statistics.videoChannels[0].encoder.width == 352);
      ASSERT_TRUE(currState.statistics.videoChannels[0].encoder.height == 288);
      //ASSERT_TRUE(currState.statistics.videoChannels[0].decoder.width == 352);
      //ASSERT_TRUE(currState.statistics.videoChannels[0].decoder.height == 288);
#endif
      ASSERT_NE(0, currState.statistics.remoteVideoChannels.size());
      if (currState.statistics.videoChannels[0].streamDataCounters.packetsSent > 0)
      {
         ASSERT_NE(strlen(currState.statistics.videoChannels[0].encoder.plName), 0);
      }
      if (currState.statistics.videoChannels[0].streamDataCounters.packetsReceived > 0)
      {
         ASSERT_NE(strlen(currState.statistics.videoChannels[0].decoder.plName), 0);
      }
   });

   
   waitFor2(aliceEvents, bobEvents);

}

#if (CPCAPI2_BRAND_CODEC_H263 == 1)
#if !defined(_WIN64)
TEST_F(VideoCallTests, AudioVideoCallH263) {

   TestAccount alice("alice");
   TestAccount bob("bob");

   // enable H.263
   alice.video->queryCodecList();
   alice.video->setCodecEnabled(cpc::hash(cpc::string("H263")), true);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("H263-1998")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("H264")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   bob.video->queryCodecList();
   bob.video->setCodecEnabled(cpc::hash(cpc::string("H263")), true);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("H264")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);

   alice.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_Standard);
   bob.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_Standard);

   alice.video->setPreferredResolution(cpc::hash(cpc::string("H263")), CPCAPI2::Media::VideoCaptureResolution_Standard);
   bob.video->setPreferredResolution(cpc::hash(cpc::string("H263")), CPCAPI2::Media::VideoCaptureResolution_Standard);

   alice.initiateVideo();
   bob.initiateVideo();
   
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, true);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      //assertCallHadMedia(alice, aliceCall, true, true);

      SipConversationState currState;
      ASSERT_EQ(alice.conversationState->getState(aliceCall, currState), CPCAPI2::kSuccess);
      ASSERT_EQ(1, currState.statistics.videoChannels.size());
#ifndef CPCAPI2_AUTO_TESTS_NO_VIDEO_STATS
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsReceived, 0);
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsSent, 0);
#endif
      ASSERT_NE(0, currState.statistics.remoteVideoChannels.size());
      if (currState.statistics.videoChannels[0].streamDataCounters.packetsSent > 0)
      {
         ASSERT_NE(strlen(currState.statistics.videoChannels[0].encoder.plName), 0);
      }
      if (currState.statistics.videoChannels[0].streamDataCounters.packetsReceived > 0)
      {
         ASSERT_NE(strlen(currState.statistics.videoChannels[0].decoder.plName), 0);
      }
      //ASSERT_TRUE(currState.statistics.videoChannels[0].encoder.width > 352);
      //ASSERT_TRUE(currState.statistics.videoChannels[0].encoder.height > 288);
      //ASSERT_TRUE(currState.statistics.videoChannels[0].decoder.width > 352);
      //ASSERT_TRUE(currState.statistics.videoChannels[0].decoder.height > 288);
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      SipConversationHandle bobCall;
      assertNewConversationIncoming_ex(bob, &bobCall, alice.config.uri(), [](const NewConversationEvent& evt){
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
      });

      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      //assertCallHadMedia(bob, bobCall, true, true);

      SipConversationState currState;
      ASSERT_EQ(bob.conversationState->getState(bobCall, currState), CPCAPI2::kSuccess);
      ASSERT_EQ(1, currState.statistics.videoChannels.size());
#ifndef CPCAPI2_AUTO_TESTS_NO_VIDEO_STATS
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsReceived, 0);
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsSent, 0);
#endif
      ASSERT_NE(0, currState.statistics.remoteVideoChannels.size());
      if (currState.statistics.videoChannels[0].streamDataCounters.packetsSent > 0)
      {
         ASSERT_NE(strlen(currState.statistics.videoChannels[0].encoder.plName), 0);
      }
      if (currState.statistics.videoChannels[0].streamDataCounters.packetsReceived > 0)
      {
         ASSERT_NE(strlen(currState.statistics.videoChannels[0].decoder.plName), 0);
      }
      //ASSERT_TRUE(currState.statistics.videoChannels[0].encoder.width > 352);
      //ASSERT_TRUE(currState.statistics.videoChannels[0].encoder.height > 288);
      //ASSERT_TRUE(currState.statistics.videoChannels[0].decoder.width > 352);
      //ASSERT_TRUE(currState.statistics.videoChannels[0].decoder.height > 288);
   });

   
   waitFor2(aliceEvents, bobEvents);

}
#endif // !defined(_WIN64)
#endif // CPCAPI2_BRAND_CODEC_H263

#if (CPCAPI2_BRAND_CODEC_H263 == 1)
#if !defined(_WIN64)
TEST_F(VideoCallTests, AudioVideoCallH2631998) {

   TestAccount alice("alice");
   TestAccount bob("bob");

   // enable H.263
   alice.video->queryCodecList();
   alice.video->setCodecEnabled(cpc::hash(cpc::string("H263-1998")), true);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("H263")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("H264")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   bob.video->queryCodecList();
   bob.video->setCodecEnabled(cpc::hash(cpc::string("H263-1998")), true);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("H263")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("H264")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);

   alice.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_Standard);
   bob.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_Standard);

   alice.video->setPreferredResolution(cpc::hash(cpc::string("H263-1998")), CPCAPI2::Media::VideoCaptureResolution_Standard);
   bob.video->setPreferredResolution(cpc::hash(cpc::string("H263-1998")), CPCAPI2::Media::VideoCaptureResolution_Standard);

   alice.initiateVideo();
   bob.initiateVideo();
   
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, true);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      //assertCallHadMedia(alice, aliceCall, true, true);

      SipConversationState currState;
      ASSERT_EQ(alice.conversationState->getState(aliceCall, currState), CPCAPI2::kSuccess);
      ASSERT_EQ(1, currState.statistics.videoChannels.size());
#ifndef CPCAPI2_AUTO_TESTS_NO_VIDEO_STATS
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsReceived, 0);
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsSent, 0);
#endif
      ASSERT_NE(0, currState.statistics.remoteVideoChannels.size());
      if (currState.statistics.videoChannels[0].streamDataCounters.packetsSent > 0)
      {
         ASSERT_NE(strlen(currState.statistics.videoChannels[0].encoder.plName), 0);
      }
      if (currState.statistics.videoChannels[0].streamDataCounters.packetsReceived > 0)
      {
         ASSERT_NE(strlen(currState.statistics.videoChannels[0].decoder.plName), 0);
      }
      //ASSERT_TRUE(currState.statistics.videoChannels[0].encoder.width > 352);
      //ASSERT_TRUE(currState.statistics.videoChannels[0].encoder.height > 288);
      //ASSERT_TRUE(currState.statistics.videoChannels[0].decoder.width > 352);
      //ASSERT_TRUE(currState.statistics.videoChannels[0].decoder.height > 288);
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      SipConversationHandle bobCall;
      assertNewConversationIncoming_ex(bob, &bobCall, alice.config.uri(), [](const NewConversationEvent& evt){
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
      });

      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      //assertCallHadMedia(bob, bobCall, true, true);

      SipConversationState currState;
      ASSERT_EQ(bob.conversationState->getState(bobCall, currState), CPCAPI2::kSuccess);
      ASSERT_EQ(1, currState.statistics.videoChannels.size());
#ifndef CPCAPI2_AUTO_TESTS_NO_VIDEO_STATS
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsReceived, 0);
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsSent, 0);
#endif
      ASSERT_NE(0, currState.statistics.remoteVideoChannels.size());
      if (currState.statistics.videoChannels[0].streamDataCounters.packetsSent > 0)
      {
         ASSERT_NE(strlen(currState.statistics.videoChannels[0].encoder.plName), 0);
      }
      if (currState.statistics.videoChannels[0].streamDataCounters.packetsReceived > 0)
      {
         ASSERT_NE(strlen(currState.statistics.videoChannels[0].decoder.plName), 0);
      }
      //ASSERT_TRUE(currState.statistics.videoChannels[0].encoder.width > 352);
      //ASSERT_TRUE(currState.statistics.videoChannels[0].encoder.height > 288);
      //ASSERT_TRUE(currState.statistics.videoChannels[0].decoder.width > 352);
      //ASSERT_TRUE(currState.statistics.videoChannels[0].decoder.height > 288);
   });

   
   waitFor2(aliceEvents, bobEvents);

}
#endif // !defined(_WIN64)
#endif // CPCAPI2_BRAND_CODEC_H263

TEST_F(VideoCallTests, DISABLED_AudioVideoCallVP8ScreenShare) {

   TestAccount alice("alice");
   TestAccount bob("bob");

   // enable VP8
   
   alice.video->queryCodecList();
   alice.video->setCodecEnabled(cpc::hash(cpc::string("H263")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("H263-1998")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("H264")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), true);
   
   bob.video->queryCodecList();
   bob.video->setCodecEnabled(cpc::hash(cpc::string("H263")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("H264")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), true);

   alice.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_Standard);
   bob.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_Standard);

   alice.video->setPreferredResolution(cpc::hash(cpc::string("vp8")), CPCAPI2::Media::VideoCaptureResolution_Standard);
   bob.video->setPreferredResolution(cpc::hash(cpc::string("vp8")), CPCAPI2::Media::VideoCaptureResolution_Standard);

   alice.initiateVideo();
   bob.initiateVideo();

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, true);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]() {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      //assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      {
         SipConversationHandle h;
         ConversationEndedEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent("SipConversationHandler::onConversationEnded",
            45000, CPCAPI2::test::HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
         ASSERT_EQ(ConversationEndReason_UserTerminatedRemotely, evt.endReason);
      }
      //assertCallHadMedia(alice, aliceCall, true, true);

      SipConversationState currState;
      ASSERT_EQ(alice.conversationState->getState(aliceCall, currState), CPCAPI2::kSuccess);
      ASSERT_EQ(1, currState.statistics.videoChannels.size());
#ifndef CPCAPI2_AUTO_TESTS_NO_VIDEO_STATS
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsReceived, 0);
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsSent, 0);
#endif
      ASSERT_NE(0, currState.statistics.remoteVideoChannels.size());
      if (currState.statistics.videoChannels[0].streamDataCounters.packetsSent > 0)
      {
         ASSERT_NE(strlen(currState.statistics.videoChannels[0].encoder.plName), 0);
      }
      if (currState.statistics.videoChannels[0].streamDataCounters.packetsReceived > 0)
      {
         ASSERT_NE(strlen(currState.statistics.videoChannels[0].decoder.plName), 0);
      }
      //ASSERT_TRUE(currState.statistics.videoChannels[0].encoder.width > 352);
      //ASSERT_TRUE(currState.statistics.videoChannels[0].encoder.height > 288);
      //ASSERT_TRUE(currState.statistics.videoChannels[0].decoder.width > 352);
      //ASSERT_TRUE(currState.statistics.videoChannels[0].decoder.height > 288);
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      SipConversationHandle bobCall;
      assertNewConversationIncoming_ex(bob, &bobCall, alice.config.uri(), [](const NewConversationEvent& evt){
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
      });

      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(25000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      //assertCallHadMedia(bob, bobCall, true, true);

      SipConversationState currState;
      ASSERT_EQ(bob.conversationState->getState(bobCall, currState), CPCAPI2::kSuccess);
      ASSERT_EQ(1, currState.statistics.videoChannels.size());
#ifndef CPCAPI2_AUTO_TESTS_NO_VIDEO_STATS
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsReceived, 0);
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsSent, 0);
#endif
      ASSERT_NE(0, currState.statistics.remoteVideoChannels.size());
      if (currState.statistics.videoChannels[0].streamDataCounters.packetsSent > 0)
      {
         ASSERT_NE(strlen(currState.statistics.videoChannels[0].encoder.plName), 0);
      }
      if (currState.statistics.videoChannels[0].streamDataCounters.packetsReceived > 0)
      {
         ASSERT_NE(strlen(currState.statistics.videoChannels[0].decoder.plName), 0);
      }
      safeCout("incoming video resolution (what Bob receives): " << currState.statistics.videoChannels[0].decoder.width << "x" << currState.statistics.videoChannels[0].decoder.height);
      //ASSERT_TRUE(currState.statistics.videoChannels[0].encoder.width > 352);
      //ASSERT_TRUE(currState.statistics.videoChannels[0].encoder.height > 288);
      //ASSERT_TRUE(currState.statistics.videoChannels[0].decoder.width > 352);
      //ASSERT_TRUE(currState.statistics.videoChannels[0].decoder.height > 288);
   });

   
   waitFor2(aliceEvents, bobEvents);

}

TEST_F(VideoCallTests, AudioVideoCallSetPortRange) {

   TestAccount alice("alice");
   TestAccount bob("bob");

   SipConversationSettings aliceSettings;
   aliceSettings.minRtpPort = 51000;
   aliceSettings.maxRtpPort = 51010;
   alice.conversation->setDefaultSettings(alice.handle, aliceSettings);

   alice.initiateVideo();
   bob.initiateVideo();
   
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, true);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      SipConversationHandle bobCall;
      assertNewConversationIncoming_ex(bob, &bobCall, alice.config.uri(), [](const NewConversationEvent& evt){
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
      });

      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });

   
   waitFor2(aliceEvents, bobEvents);

}

TEST_F(VideoCallTests, AudioVideoCallMuteUnmute) {

   TestAccount alice("alice");
   TestAccount bob("bob");

   alice.initiateVideo();
   bob.initiateVideo();
   
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, true);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      {
         SipConversationHandle h;
         ConversationEndedEvent evt;
         ASSERT_TRUE(alice.conversationEvents->expectEvent("SipConversationHandler::onConversationEnded",
            45000, CPCAPI2::test::HandleEqualsPred<SipConversationHandle>(aliceCall), h, evt));
         ASSERT_EQ(ConversationEndReason_UserTerminatedRemotely, evt.endReason);

      }
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      SipConversationHandle bobCall;
      assertNewConversationIncoming_ex(bob, &bobCall, alice.config.uri(), [](const NewConversationEvent& evt){
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
      });

      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      safeCout("[alice] setVideoMute(true)");
      alice.video->setVideoMute(true);
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      safeCout("[alice] stopCapture()");
      alice.video->stopCapture();
      std::this_thread::sleep_for(std::chrono::milliseconds(7000));

      safeCout("[alice] startCapture() -- video should not be sent yet (still muted)");
      alice.video->startCapture(); // video should not be sent yet (still muted)
      std::this_thread::sleep_for(std::chrono::milliseconds(7000));

      safeCout("[alice] setVideoMute(false)");
      alice.video->setVideoMute(false);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });

   
   waitFor2(aliceEvents, bobEvents);

}

class mylatch
{
public:
   mylatch( int worker_count ) : m_Counter( worker_count ) {}
   
   // Just do a busy wait with a yield
   void wait( void )
   {
      // Because fetch_sub returns the previous value, test
      // for lower-than-or-equal to 1, which means we just went
      // to zero
      if( m_Counter.fetch_sub( 1 ) <= 1 )
         return;

      while( m_Counter > 0 )
         std::this_thread::yield();
   }
   
private:
   std::atomic< int > m_Counter;
};

TEST_F(VideoCallTests, H264VideoCallMuteRTCPCheck) {

   TestAccount alice("alice");
   TestAccount bob("bob");

   alice.initiateVideo();
   bob.initiateVideo();
   
   // The latch is needed because alice and bob need to be coordinated,
   // alice cannot be expecting both RTCP reports AND an incoming call
   // disconnect siultaneously
   mylatch lt( 2 );

   auto aliceEvents = std::async(std::launch::async, [&] () {
      //alice.video->setCaptureDevice(kScreenCaptureDeviceId);
      CPCAPI2::Media::VideoCodecListUpdatedEvent evt;
      int handle = 0;
      
      ASSERT_EQ(alice.video->queryCodecList(), kSuccess);
      ASSERT_TRUE(alice.mediaEvents->expectEvent("VideoHandler::onVideoCodecListUpdated", 30000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
      ASSERT_NE(evt.codecInfo.size(), 0);
      bool foundH264 = false;
      for(cpc::vector<VideoCodecInfo>::const_iterator it = evt.codecInfo.begin(); it != evt.codecInfo.end(); it++)
      {
         bool isH264 = it->codecName == "H.264";
         foundH264 |= isH264;
         alice.video->setCodecEnabled(it->id, isH264);
      }
      ASSERT_TRUE(foundH264);

      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, false);
      alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
		alice.conversation->addParticipant(aliceCall, bob.config.uri());
		alice.conversation->start(aliceCall);

      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      
      alice.video->setVideoMute( true );
      
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      
      // Check that aliceCall video has RTCP. We do this by getting two reports and comparing stamps
      //std::this_thread::sleep_for( std::chrono::milliseconds( 5000 ));
      TestCallEvents::expectRTCP( __LINE__, alice, aliceCall, false, true );

      // Notify the other thread we are ready to be disconnected
      lt.wait();
      
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      //assertCallHadOutgoingMedia(alice, aliceCall, true, false);
	});

	auto bobEvents = std::async(std::launch::async, [&] () {
		SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
		assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Audio, false);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
		assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);

      bob.video->setVideoMute( true );
      
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      // Check that bobCall video has RTCP. We do this by getting two reports and comparing stamps
      //std::this_thread::sleep_for( std::chrono::milliseconds( 5000 ));
      TestCallEvents::expectRTCP( __LINE__, bob, bobCall, false, true );
      
      // Only hangup the call once all threads are ready.
      lt.wait();
      
		assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      //assertCallHadIncomingMedia(bob, bobCall, true, false);
	});

   
   waitFor2(aliceEvents, bobEvents);

}

TEST_F(VideoCallTests, H264VideoCallEarlyMuteRTCPCheck) {

   TestAccount alice("alice");
   TestAccount bob("bob");

   alice.initiateVideo();
   bob.initiateVideo();
   
   // The latch is needed because alice and bob need to be coordinated,
   // alice cannot be expecting both RTCP reports AND an incoming call
   // disconnect siultaneously
   mylatch lt( 2 );

   auto aliceEvents = std::async(std::launch::async, [&] () {
      //alice.video->setCaptureDevice(kScreenCaptureDeviceId);
      CPCAPI2::Media::VideoCodecListUpdatedEvent evt;
      int handle = 0;
      
      ASSERT_EQ(alice.video->queryCodecList(), kSuccess);
      ASSERT_TRUE(alice.mediaEvents->expectEvent("VideoHandler::onVideoCodecListUpdated", 30000, CPCAPI2::test::AlwaysTruePred(), handle, evt));
      ASSERT_NE(evt.codecInfo.size(), 0);
      bool foundH264 = false;
      for(cpc::vector<VideoCodecInfo>::const_iterator it = evt.codecInfo.begin(); it != evt.codecInfo.end(); it++)
      {
         bool isH264 = it->codecName == "H.264";
         foundH264 |= isH264;
         alice.video->setCodecEnabled(it->id, isH264);
      }
      ASSERT_TRUE(foundH264);

      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, false);
      alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
		alice.conversation->addParticipant(aliceCall, bob.config.uri());
      alice.video->setVideoMute( true );
		alice.conversation->start(aliceCall);

      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      
      
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      
      // Check that aliceCall video has RTCP. We do this by getting two reports and comparing stamps
      //std::this_thread::sleep_for( std::chrono::milliseconds( 5000 ));
      TestCallEvents::expectRTCP( __LINE__, alice, aliceCall, false, true );

      // Notify the other thread we are ready to be disconnected
      lt.wait();
      
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      //assertCallHadOutgoingMedia(alice, aliceCall, true, false);
	});

	auto bobEvents = std::async(std::launch::async, [&] () {
		SipConversationHandle bobCall;
      bob.video->setVideoMute( true );
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
		assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Audio, false);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
		assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);

      
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      // Check that bobCall video has RTCP. We do this by getting two reports and comparing stamps
      //std::this_thread::sleep_for( std::chrono::milliseconds( 5000 ));
      TestCallEvents::expectRTCP( __LINE__, bob, bobCall, false, true );
      
      // Only hangup the call once all threads are ready.
      lt.wait();
      
		assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      //assertCallHadIncomingMedia(bob, bobCall, true, false);
	});

   
   waitFor2(aliceEvents, bobEvents);

}

#ifndef ANDROID // OBELISK-5917: hardware accelerated non interleaved currently broken on Android
TEST_F(VideoCallTests, H264VideoCallNonInterleavedModePreferred) {

   TestAccount alice("alice");
   TestAccount bob("bob");

   alice.initiateVideo(true, false);
   bob.initiateVideo(false, true);

   H264Config config;
   config.enableNonInterleavedMode = true;
   config.preferNonInterleavedMode = true;
   alice.video->setCodecConfig(config);
   bob.video->setCodecConfig(config);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      alice.enableOnlyThisVideoCodec("H.264");
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, false);
      alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
		alice.conversation->addParticipant(aliceCall, bob.config.uri());
		alice.conversation->start(aliceCall);

      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(20000));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertCallHadOutgoingMedia(alice, aliceCall, false, true);
	});

	auto bobEvents = std::async(std::launch::async, [&] () {
		SipConversationHandle bobCall;
      
      NewConversationEvent evt;
      ASSERT_TRUE(bob.conversationEvents->expectEvent("SipConversationHandler::onNewConversation", 30000, AlwaysTruePred(), bobCall, evt)) << "missed inbound call event";
      ASSERT_EQ(ConversationType_Incoming, evt.conversationType) << "wrong conversation type, expecting conversation type Incoming";
      cpc::string data = evt.sessionDescription.sdpString;
      
      size_t npos = cpc::string::npos; // OSX compiler didn't like having this below

      // for this test we want both packetization options with non-interlaced first
      ASSERT_NE(data.find("RTP/AVP 126 127\r\n"), npos);
      
      // the 126 and 127 are arbitrary so we now make sure they're assigned as we expect
      ASSERT_NE(data.find("a=fmtp:126 profile-level-id=428033;packetization-mode=1"), npos);
      ASSERT_NE(data.find("a=fmtp:127 profile-level-id=428033;packetization-mode=0"), npos);
      
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Audio, false);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
		assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

		std::this_thread::sleep_for(std::chrono::milliseconds(20000));
		assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      assertCallHadIncomingMedia(bob, bobCall, false, true);
	});

   waitFor2(aliceEvents, bobEvents);

}
#endif // #ifndef ANDROID // OBELISK-5917: hardware accelerated non interleaved currently broken on Android


TEST_F(VideoCallTests, H264VideoCallNonInterleavedModeNotPreferred) {

   TestAccount alice("alice");
   TestAccount bob("bob");

   alice.initiateVideo(true, false);
   bob.initiateVideo(false, true);

   H264Config config;
   config.enableNonInterleavedMode = true;
   config.preferNonInterleavedMode = false;
   alice.video->setCodecConfig(config);
   bob.video->setCodecConfig(config);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      alice.enableOnlyThisVideoCodec("H.264");
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, false);
      alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
      alice.conversation->addParticipant(aliceCall, bob.config.uri());
      alice.conversation->start(aliceCall);

      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(20000));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertCallHadOutgoingMedia(alice, aliceCall, false, true);
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall;
      
      NewConversationEvent evt;
      ASSERT_TRUE(bob.conversationEvents->expectEvent("SipConversationHandler::onNewConversation", 30000, AlwaysTruePred(), bobCall, evt)) << "missed inbound call event";
      ASSERT_EQ(ConversationType_Incoming, evt.conversationType) << "wrong conversation type, expecting conversation type Incoming";
      cpc::string data = evt.sessionDescription.sdpString;
      
      size_t npos = cpc::string::npos; // OSX compiler didn't like having this below

      // for this test we want both packetization options with non-interlaced second
      ASSERT_NE(data.find("RTP/AVP 127 126\r\n"), npos);
      
      // the 126 and 127 are arbitrary so we now make sure they're assigned as we expect
      ASSERT_NE(data.find("a=fmtp:127 profile-level-id=428033;packetization-mode=0"), npos);
      ASSERT_NE(data.find("a=fmtp:126 profile-level-id=428033;packetization-mode=1"), npos);

      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Audio, false);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(20000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      assertCallHadIncomingMedia(bob, bobCall, false, true);
   });

   waitFor2(aliceEvents, bobEvents);

}

TEST_F(VideoCallTests, H264VideoCallSingleNALMode) {

   TestAccount alice("alice");
   TestAccount bob("bob");

   alice.initiateVideo(true, false);
   bob.initiateVideo(false, true);

   H264Config config;
   config.enableNonInterleavedMode = false;
   config.preferNonInterleavedMode = true;   // set to make sure it doesn't affect the above
   alice.video->setCodecConfig(config);
   bob.video->setCodecConfig(config);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      alice.enableOnlyThisVideoCodec("H.264");
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, false);
      alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
      alice.conversation->addParticipant(aliceCall, bob.config.uri());
      alice.conversation->start(aliceCall);

      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(20000));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertCallHadOutgoingMedia(alice, aliceCall, false, true);
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall;
      
      NewConversationEvent evt;
      ASSERT_TRUE(bob.conversationEvents->expectEvent("SipConversationHandler::onNewConversation", 30000, AlwaysTruePred(), bobCall, evt)) << "missed inbound call event";
      ASSERT_EQ(ConversationType_Incoming, evt.conversationType) << "wrong conversation type, expecting conversation type Incoming";
      cpc::string data = evt.sessionDescription.sdpString;
      
      size_t npos = cpc::string::npos; // OSX compiler didn't like having this below

      // for this test we want both packetization options with non-interlaced first
      ASSERT_NE(data.find("RTP/AVP 127\r\n"), npos);
      
      // the 127 is arbitrary so we now make sure it's assigned as we expect
      ASSERT_NE(data.find("a=fmtp:127 profile-level-id=428033;packetization-mode=0"), npos);

      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Audio, false);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(20000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      assertCallHadIncomingMedia(bob, bobCall, false, true);
   });

   waitFor2(aliceEvents, bobEvents);

}


// alice only has non-interleaved mode enabled, and calls bob who has default settings
#ifndef ANDROID // OBELISK-5917: hardware accelerated non interleaved currently broken on Android
TEST_F(VideoCallTests, H264VideoCallNonInterleavedMode2) {

   TestAccount alice("alice");
   TestAccount bob("bob");

   alice.initiateVideo(true, false);
   bob.initiateVideo(false, true);

   dynamic_cast<VideoExt*>(alice.video)->setInterleavedModeEnabled(false);
//   H264Config h264Config;
//   h264Config.enableNonInterleavedMode = false;
//   alice.video->setCodecConfig(h264Config);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      alice.enableOnlyThisVideoCodec("H.264");
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, false);
      alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
      alice.conversation->addParticipant(aliceCall, bob.config.uri());
      alice.conversation->start(aliceCall);

      //std::this_thread::sleep_for(std::chrono::milliseconds(20000));

      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(20000));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertCallHadOutgoingMedia(alice, aliceCall, false, true);
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall;
      //std::this_thread::sleep_for(std::chrono::milliseconds(20000));
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Audio, false);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(20000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      assertCallHadIncomingMedia(bob, bobCall, false, true);
   });

   waitFor2(aliceEvents, bobEvents);

}
#endif // #ifndef ANDROID // OBELISK-5917: hardware accelerated non interleaved currently broken on Android

TEST_F(VideoCallTests, DISABLED_H264VideoCallOut) {

   TestAccount alice("alice");

   alice.initiateVideo(true, false);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      alice.video->queryCodecList();
      alice.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);
      alice.video->setCodecEnabled(cpc::hash(cpc::string("VP8")), false);

      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, false);
      alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
		alice.conversation->addParticipant(aliceCall, "sip:6161@127.0.0.1");
		alice.conversation->start(aliceCall);

      assertNewConversationOutgoing(alice, aliceCall, "sip:6161@127.0.0.1");
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::hours(1000));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
	});

   
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

}

TEST_F(VideoCallTests, DISABLED_H264VideoCallIn) {

   TestAccount bob("bob");

   std::cout << "Bob registered as: " << bob.config.uri() << std::endl;

   bob.initiateVideo(false, true);

	auto bobEvents = std::async(std::launch::async, [&] () {
		SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, "sip:6161@127.0.0.1");
		assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Audio, false);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
		assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, nullptr);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

		std::this_thread::sleep_for(std::chrono::hours(1000));
		assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
	});

   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());

}

TEST_F(VideoCallTests, VideoMultipleCallHoldNetworkChange) {
   TestAccount alice("alice");
   TestAccount bob("bob");
   TestAccount max("max");

   alice.enableOnlyThisVideoCodec("H.264");
   bob.enableOnlyThisVideoCodec("H.264");
   max.enableOnlyThisVideoCodec("H.264");

   alice.video->setLocalVideoPreviewResolution(TestEnvironmentConfig::defaultVideoRes());
   bob.video->setLocalVideoPreviewResolution(TestEnvironmentConfig::defaultVideoRes());
   max.video->setLocalVideoPreviewResolution(TestEnvironmentConfig::defaultVideoRes());

   alice.video->setPreferredResolution(cpc::hash(cpc::string("H264")), TestEnvironmentConfig::defaultVideoRes());
   bob.video->setPreferredResolution(cpc::hash(cpc::string("H264")), TestEnvironmentConfig::defaultVideoRes());
   max.video->setPreferredResolution(cpc::hash(cpc::string("H264")), TestEnvironmentConfig::defaultVideoRes());

   alice.initiateVideo();
   bob.initiateVideo();
   max.initiateVideo();

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, true);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]() {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendOnly);
      assertSuccess(alice.conversation->accept(aliceCall));
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_ReceiveOnly);

      // Bob re-INVITEs due to network change
      {
         SCOPED_TRACE(alice.config.name);
         TestCallEvents::expectConversationMediaChangeRequest(__LINE__, alice, aliceCall, MediaDirection_SendOnly, 30000);
      }
      assertSuccess(alice.conversation->accept(aliceCall));
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_ReceiveOnly);

      std::this_thread::sleep_for(std::chrono::milliseconds(15000));
      safeCout("Alice ending call to Bob");
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      // Bob sends 180 ringing -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      safeCout("Bob putting Alice on hold");
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertSuccess(bob.conversation->hold(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendOnly);

      // make an outgoing (audio only) call from Bob to Max using the demo.xten.com server
      safeCout("Bob calling Max");
      SipConversationHandle bobCallToMax = bob.conversation->createConversation(bob.handle);
      bob.conversation->addParticipant(bobCallToMax, max.config.uri());
      bob.conversation->setMediaEnabled(bobCallToMax, MediaType_Audio, true);
      bob.conversation->setMediaEnabled(bobCallToMax, MediaType_Video, true);
      bob.conversation->start(bobCallToMax);

      // BOB <=> MAX
      assertNewConversationOutgoing(bob, bobCallToMax, max.config.uri());
      assertConversationStateChanged(bob, bobCallToMax, ConversationState_RemoteRinging);
      assertConversationMediaChanged(bob, bobCallToMax, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCallToMax, ConversationState_Connected);

      safeCout("Bob connected to Max");
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));

      // network change
      safeCout("Bob network change");
      bob.network->setNetworkTransport(NetworkTransport::TransportWWAN);
      std::set<resip::Data> ifaces;
      ifaces.insert("**************"); // NOTE: this has no bearing on the c= line in the SDP offers
      bob.network->setMockInterfaces(ifaces);

      {
         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
         ASSERT_TRUE(bob.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
            20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(bob.handle, h);
         ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Refreshing, evt.accountStatus);
      }
      {
         CPCAPI2::SipAccount::SipAccountHandle h;
         CPCAPI2::SipAccount::SipAccountStatusChangedEvent evt;
         ASSERT_TRUE(bob.accountEvents->expectEvent("SipAccountHandler::onAccountStatusChanged",
            20000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(bob.handle, h);
         ASSERT_EQ(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered, evt.accountStatus);
      }

      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendOnly);
      assertConversationMediaChanged(bob, bobCallToMax, MediaDirection_SendReceive);

      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      safeCout("Bob ending call to Max");
      assertSuccess(bob.conversation->end(bobCallToMax));
      assertConversationEnded(bob, bobCallToMax, ConversationEndReason_UserTerminatedLocally);

      // BOB <=> ALICE
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);

      // expect registration refresh after network change during a call
      assertAccountRefreshing(bob);
      assertAccountRegistered(bob);
   });

   // Overview of Max's thread:
   auto maxEvents = std::async(std::launch::async, [&]() {
      SipConversationHandle maxCallFromBob;
      assertNewConversationIncoming(max, &maxCallFromBob, bob.config.uri());
      // Bob sends 180 ringing -- this should trigger state transitions for both Bob and Max
      assertSuccess(max.conversation->sendRingingResponse(maxCallFromBob));
      assertConversationStateChanged(max, maxCallFromBob, ConversationState_LocalRinging);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      max.conversation->setMediaEnabled(maxCallFromBob, MediaType_Video, true);
      assertSuccess(max.conversation->accept(maxCallFromBob));
      assertConversationMediaChanged(max, maxCallFromBob, MediaDirection_SendReceive);
      assertConversationStateChanged(max, maxCallFromBob, ConversationState_Connected);

      // After the network change at Bob's side, there is a re-INVITE
      assertConversationMediaChangeRequest(max, maxCallFromBob, MediaDirection_SendReceive);
      assertSuccess(max.conversation->accept(maxCallFromBob));
      assertConversationMediaChanged(max, maxCallFromBob, MediaDirection_SendReceive);

      assertConversationStateChanged(max, maxCallFromBob, ConversationState_Connected);
      assertConversationEnded(max, maxCallFromBob, ConversationEndReason_UserTerminatedRemotely);
   });

   //waitFor3(aliceEvents, bobEvents, maxEvents);
   waitFor3(aliceEvents, bobEvents, maxEvents);

}

TEST_F(VideoCallTests, DISABLED_VideoConfParticipantHold) {
   TestAccount alice("alice");
   TestAccount bob("bob");
   TestAccount max("max");

   alice.enableOnlyThisVideoCodec("H.264");
   bob.enableOnlyThisVideoCodec("H.264");
   max.enableOnlyThisVideoCodec("H.264");

   alice.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   bob.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   max.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   alice.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   bob.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   max.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   alice.initiateVideo();
   bob.initiateVideo();
   max.initiateVideo();

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, true);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]() {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendOnly);
      assertSuccess(alice.conversation->accept(aliceCall));
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_ReceiveOnly);

      // Bob re-INVITEs due to conference
      assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendReceive);
      assertSuccess(alice.conversation->accept(aliceCall));
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      safeCout("Alice in conference");

      //std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      //safeCout("Alice putting Bob on hold");
      //assertSuccess(alice.conversation->hold(aliceCall));
      //assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendOnly);

      std::this_thread::sleep_for(std::chrono::milliseconds(15000));
      safeCout("Alice ending call to Bob");
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      // Bob sends 180 ringing -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      safeCout("Bob putting Alice on hold");
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertSuccess(bob.conversation->hold(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendOnly);

      // make an outgoing (audio only) call from Bob to Max using the demo.xten.com server
      safeCout("Bob calling Max");
      SipConversationHandle bobCallToMax = bob.conversation->createConversation(bob.handle);
      bob.conversation->addParticipant(bobCallToMax, max.config.uri());
      bob.conversation->setMediaEnabled(bobCallToMax, MediaType_Audio, true);
      bob.conversation->setMediaEnabled(bobCallToMax, MediaType_Video, true);
      bob.conversation->start(bobCallToMax);

      // BOB <=> MAX
      assertNewConversationOutgoing(bob, bobCallToMax, max.config.uri());
      assertConversationStateChanged(bob, bobCallToMax, ConversationState_RemoteRinging);
      assertConversationMediaChanged(bob, bobCallToMax, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCallToMax, ConversationState_Connected);

      safeCout("Bob connected to Max");
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      safeCout("Bob taking Alice off hold ==> conference");
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertSuccess(bob.conversation->unhold(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);

      // Max puts Bob on hold
      safeCout("Max puts Bob on hold");
      assertConversationMediaChangeRequest(bob, bobCallToMax, MediaDirection_SendOnly);
      assertSuccess(bob.conversation->accept(bobCallToMax));
      assertConversationMediaChanged(bob, bobCallToMax, MediaDirection_ReceiveOnly);
      safeCout("Bob was put on hold by Max");

      std::this_thread::sleep_for(std::chrono::milliseconds(10000));

      safeCout("Bob ending call to Max");
      assertSuccess(bob.conversation->end(bobCallToMax));
      assertConversationEnded(bob, bobCallToMax, ConversationEndReason_UserTerminatedLocally);

      // BOB <=> ALICE
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   // Overview of Max's thread:
   auto maxEvents = std::async(std::launch::async, [&]() {
      SipConversationHandle maxCallFromBob;
      assertNewConversationIncoming(max, &maxCallFromBob, bob.config.uri());
      // Bob sends 180 ringing -- this should trigger state transitions for both Bob and Max
      assertSuccess(max.conversation->sendRingingResponse(maxCallFromBob));
      assertConversationStateChanged(max, maxCallFromBob, ConversationState_LocalRinging);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      max.conversation->setMediaEnabled(maxCallFromBob, MediaType_Video, true);
      assertSuccess(max.conversation->accept(maxCallFromBob));
      assertConversationMediaChanged(max, maxCallFromBob, MediaDirection_SendReceive);
      assertConversationStateChanged(max, maxCallFromBob, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      safeCout("Max putting Bob on hold");
      assertSuccess(max.conversation->hold(maxCallFromBob));
      assertConversationMediaChanged(max, maxCallFromBob, MediaDirection_SendOnly);

      std::this_thread::sleep_for(std::chrono::milliseconds(10000));

      // Bob ends the call
      assertConversationEnded(max, maxCallFromBob, ConversationEndReason_UserTerminatedRemotely);
   });

   //waitFor3(aliceEvents, bobEvents, maxEvents);
   waitFor3(aliceEvents, bobEvents, maxEvents);

}

TEST_F(VideoCallTests, SeparateVideoWindows) {
   TestAccount alice("alice");
   TestAccount bob("bob");
   TestAccount max("max");

   alice.enableOnlyThisVideoCodec("H.264");
   bob.enableOnlyThisVideoCodec("H.264");
   max.enableOnlyThisVideoCodec("H.264");

   alice.video->setLocalVideoPreviewResolution(TestEnvironmentConfig::defaultVideoRes());
   bob.video->setLocalVideoPreviewResolution(TestEnvironmentConfig::defaultVideoRes());
   max.video->setLocalVideoPreviewResolution(TestEnvironmentConfig::defaultVideoRes());

   alice.video->setPreferredResolution(cpc::hash(cpc::string("H264")), TestEnvironmentConfig::defaultVideoRes());
   bob.video->setPreferredResolution(cpc::hash(cpc::string("H264")), TestEnvironmentConfig::defaultVideoRes());
   max.video->setPreferredResolution(cpc::hash(cpc::string("H264")), TestEnvironmentConfig::defaultVideoRes());

   alice.initiateVideo();
   bob.initiateVideo();
   max.initiateVideo();

   CpTestWindowHandle hwndBobRemoteFromMax = bob.videoHelper->getHwndIncomingWindow();

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, true);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]() {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendOnly);
      assertSuccess(alice.conversation->accept(aliceCall));
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_ReceiveOnly);

      // Bob re-INVITEs due to conference
      assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendReceive);
      assertSuccess(alice.conversation->accept(aliceCall));
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      safeCout("Alice in conference");

      //std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      //safeCout("Alice putting Bob on hold");
      //assertSuccess(alice.conversation->hold(aliceCall));
      //assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendOnly);

      std::this_thread::sleep_for(std::chrono::milliseconds(15000));
      safeCout("Alice ending call to Bob");
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      // Bob sends 180 ringing -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      safeCout("Bob putting Alice on hold");
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertSuccess(bob.conversation->hold(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendOnly);

      // make an outgoing (audio only) call from Bob to Max using the demo.xten.com server
      safeCout("Bob calling Max");
      SipConversationHandle bobCallToMax = bob.conversation->createConversation(bob.handle);
      bob.conversation->addParticipant(bobCallToMax, max.config.uri());
      bob.conversation->setMediaEnabled(bobCallToMax, MediaType_Audio, true);
      bob.conversation->setMediaEnabled(bobCallToMax, MediaType_Video, true);
#ifdef _WIN32
      bob.conversation->setIncomingVideoRenderTarget(bobCallToMax, hwndBobRemoteFromMax);
#endif // _WIN32
      bob.conversation->start(bobCallToMax);

      // BOB <=> MAX
      assertNewConversationOutgoing(bob, bobCallToMax, max.config.uri());
      assertConversationStateChanged(bob, bobCallToMax, ConversationState_RemoteRinging);
      {
         SipConversationHandle h;
         ConversationMediaChangedEvent evt;
         ASSERT_TRUE(bob.conversationEvents->expectEvent("SipConversationHandler::onConversationMediaChanged",
            15000, HandleEqualsPred<SipConversationHandle>(bobCallToMax), h, evt));
         ASSERT_EQ((size_t)2, evt.localMediaInfo.size());
         for (cpc::vector<MediaInfo>::const_iterator it = evt.localMediaInfo.begin(); it != evt.localMediaInfo.end(); it++)
         {
            ASSERT_EQ(MediaDirection_SendReceive, it->mediaDirection);
         }
      }
      assertConversationStateChanged(bob, bobCallToMax, ConversationState_Connected);

      safeCout("Bob connected to Max");
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      safeCout("Bob taking Alice off hold ==> conference");
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertSuccess(bob.conversation->unhold(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);

      std::this_thread::sleep_for(std::chrono::milliseconds(10000));

      safeCout("Bob ending call to Max");
      assertSuccess(bob.conversation->end(bobCallToMax));
      assertConversationEnded(bob, bobCallToMax, ConversationEndReason_UserTerminatedLocally);

      // BOB <=> ALICE
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });

   // Overview of Max's thread:
   auto maxEvents = std::async(std::launch::async, [&]() {
      SipConversationHandle maxCallFromBob;
      assertNewConversationIncoming(max, &maxCallFromBob, bob.config.uri());
      // Bob sends 180 ringing -- this should trigger state transitions for both Bob and Max
      assertSuccess(max.conversation->sendRingingResponse(maxCallFromBob));
      assertConversationStateChanged(max, maxCallFromBob, ConversationState_LocalRinging);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      max.conversation->setMediaEnabled(maxCallFromBob, MediaType_Video, true);
      assertSuccess(max.conversation->accept(maxCallFromBob));
      assertConversationMediaChanged(max, maxCallFromBob, MediaDirection_SendReceive);
      assertConversationStateChanged(max, maxCallFromBob, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      std::this_thread::sleep_for(std::chrono::milliseconds(10000));

      // Bob ends the call
      assertConversationEnded(max, maxCallFromBob, ConversationEndReason_UserTerminatedRemotely);
   });

   //waitFor(aliceEvents, bobEvents, maxEvents);
   waitFor3(aliceEvents, bobEvents, maxEvents);

}

/** Screen share conference performance test
*   Tests performance of CPCAPI2 running asd RTP proxy on RHEL
*   IP address of a server ************* (Boston)
*   To measure performance run command "top" or command "top | grep cpcapi2"
*   on RHEL server.
*   SSH to RHEL server with ssh -p 54245 root@**************
*   SSH to RHEL server with ssh -R 443:svn.cp.local:443 -p 54245 root@************** if you want to be able to update CPCAPI2 code
*   Username: root, Password: cl!3ntApi
*   Code is located in folder /root/cpsvn
*/

TestAccount* accounts[LOAD];
SipConversationHandle aliceCalls[LOAD];
unsigned int listenerWaitTime = 15000;

void startVideoCallToRtpProxy(unsigned int count, cpc::string serverIp, MediaInfo receiverVideo, MediaInfo receiverAudio, SipConversationSettings callSettings)
{
   std::string name = "listener_";
   name.append(resip::Random::getCryptoRandomHex(2).c_str());
   accounts[count] = new TestAccount(name, Account_NoInit);
   accounts[count]->config.settings.useOutbound = true;
   accounts[count]->config.settings.sipTransportType = SipAccountTransport_TCP;
   accounts[count]->config.settings.domain = "opsip.silverstar.counterpath.net";
   accounts[count]->config.settings.outboundProxy = accounts[count]->config.settings.domain;
   accounts[count]->init();
   accounts[count]->enable();

   aliceCalls[count] = accounts[count]->conversation->createConversation(accounts[count]->handle);
   accounts[count]->video->setCaptureDevice(kCustomVideoSourceDeviceId);
   accounts[count]->conversation->setMediaEnabled(aliceCalls[count], MediaType_Audio, true);
   accounts[count]->conversation->setMediaEnabled(aliceCalls[count], MediaType_Video, true);
   accounts[count]->conversation->configureMedia(aliceCalls[count], receiverAudio);
   accounts[count]->conversation->configureMedia(aliceCalls[count], receiverVideo);
   accounts[count]->conversation->addParticipant(aliceCalls[count], serverIp);
   accounts[count]->conversation->setDefaultSettings(accounts[count]->handle, callSettings);
   accounts[count]->conversation->start(aliceCalls[count]);

   assertNewConversationOutgoing_time(*accounts[count], aliceCalls[count], serverIp, listenerWaitTime);
   assertConversationMediaChanged_ex_time(*accounts[count], aliceCalls[count], [](const ConversationMediaChangedEvent& evt)
   {
      ASSERT_EQ(2, evt.localMediaInfo.size());
      ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
      ASSERT_EQ(MediaDirection_ReceiveOnly, evt.localMediaInfo[0].mediaDirection);
      ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
      ASSERT_EQ(MediaDirection_ReceiveOnly, evt.localMediaInfo[1].mediaDirection);
   }, listenerWaitTime);
   assertConversationStateChanged_time(*accounts[count], aliceCalls[count], ConversationState_Connected, listenerWaitTime);
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));
   assertSuccess(accounts[count]->conversation->end(aliceCalls[count]));
   assertConversationEnded_time(*accounts[count], aliceCalls[count], ConversationEndReason_UserTerminatedLocally, listenerWaitTime);
   assertCallHadMedia(*accounts[count], aliceCalls[count], true, true);
}

TEST_F(VideoCallTests, DISABLED_RtpProxyPerformance) {
   
   // !!!!!!!!!!!!!!!!!! Enter necessary data for bob account to run tests
   
   // set up and run presenter account
   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.useOutbound = true;
   alice.config.settings.username = "1298";
   alice.config.settings.password = "ta6Ls86CarXysvX7";
   alice.config.settings.sipTransportType = SipAccountTransport_TCP;
   alice.config.settings.domain = "counterpath.com";
   alice.config.settings.outboundProxy = "sbc.counterpath.com:16060";
   alice.config.settings.ipVersion = IpVersion_V4;
   alice.config.settings.useRegistrar = true;
   alice.config.settings.useRport = true;
   alice.config.settings.registrationIntervalSeconds = 45;
   alice.enable();

   // Last audience account. With video to confirm it works

   TestAccount bob("bob", Account_NoInit);
   bob.config.settings.useOutbound = true;
   bob.config.settings.username = ""; // <----- enter you counterpath SIP extension here
   bob.config.settings.password = ""; // <----- enter you counterpath SIP password here
   bob.config.settings.sipTransportType = SipAccountTransport_TCP;
   bob.config.settings.domain = "counterpath.com";
   bob.config.settings.outboundProxy = "sbc.counterpath.com:16060";
   bob.config.settings.ipVersion = IpVersion_V4;
   bob.config.settings.useRegistrar = true;
   bob.config.settings.useRport = true;
   bob.config.settings.registrationIntervalSeconds = 45;
   bob.enable();

   cpc::string serverIp = "sip:1297@***********:16060";
   MediaInfo senderAudio = MediaInfo(MediaType_Audio, MediaDirection::MediaDirection_SendReceive);
   MediaInfo senderVideo = MediaInfo(MediaType_Video, MediaDirection::MediaDirection_SendReceive);
   MediaInfo receiverAudio = MediaInfo(MediaType_Audio, MediaDirection::MediaDirection_SendReceive);
   MediaInfo receiverVideo = MediaInfo(MediaType_Video, MediaDirection::MediaDirection_ReceiveOnly);
   SipConversationSettings callSettings;
   callSettings.natTraversalMode = NatTraversalMode::NatTraversalMode_None;

   alice.video->queryCodecList();

   alice.video->queryCodecList();
   alice.video->setCodecEnabled(cpc::hash(cpc::string("H264")), false);

   bob.video->queryCodecList();
   bob.video->setCodecEnabled(cpc::hash(cpc::string("H264")), false);

   alice.initiateVideo();
   bob.initiateVideo(false, true);

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, true);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
   alice.conversation->configureMedia(aliceCall, senderAudio);
   alice.conversation->configureMedia(aliceCall, senderVideo);
   alice.conversation->addParticipant(aliceCall, serverIp);
   alice.conversation->setDefaultSettings(alice.handle, callSettings);

   auto aliceEvents = std::async(std::launch::async, [&]() {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertNewConversationOutgoing_time(alice, aliceCall, serverIp, listenerWaitTime);
      assertConversationMediaChanged_ex_time(alice, aliceCall, [](const ConversationMediaChangedEvent& evt)
      {
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      }, listenerWaitTime);
      assertConversationStateChanged_time(alice, aliceCall, ConversationState_Connected, listenerWaitTime);
      std::this_thread::sleep_for(std::chrono::milliseconds(90000));
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded_time(alice, aliceCall, ConversationEndReason_UserTerminatedLocally, listenerWaitTime);
      assertCallHadMedia(alice, aliceCall, false, true);
   });
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   alice.conversation->start(aliceCall);

   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
   bob.video->setCaptureDevice(kCustomVideoSourceDeviceId);
   bob.conversation->setMediaEnabled(bobCall, MediaType_Audio, true);
   bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
   bob.conversation->configureMedia(bobCall, receiverAudio);
   bob.conversation->configureMedia(bobCall, receiverVideo);
   bob.conversation->addParticipant(bobCall, serverIp);
   bob.conversation->setDefaultSettings(bob.handle, callSettings);

   auto bobEvents = std::async(std::launch::async, [&]() {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertNewConversationOutgoing_time(bob, bobCall, serverIp, listenerWaitTime);
      assertConversationMediaChanged_ex_time(bob, bobCall, [](const ConversationMediaChangedEvent& evt)
      {
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_ReceiveOnly, evt.localMediaInfo[1].mediaDirection);
      }, listenerWaitTime);
      assertConversationStateChanged_time(bob, bobCall, ConversationState_Connected, listenerWaitTime);
      std::this_thread::sleep_for(std::chrono::milliseconds(70000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded_time(bob, bobCall, ConversationEndReason_UserTerminatedLocally, listenerWaitTime);
      assertCallHadMedia(bob, bobCall, false, true);
   });
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   bob.conversation->start(bobCall);

   // Set up and run audience accounts
   /*std::future<void> accountThreads[LOAD];
   for (unsigned int i = 0; i < LOAD; i++)
   {
      accountThreads[i] = std::async(std::launch::async, startVideoCallToRtpProxy, i, serverIp, receiverVideo, receiverAudio, callSettings);
   }*/

   // Wait for processes to end
   
   waitFor2(aliceEvents, bobEvents);

   /*runMediaProcess(bob, bobEvents);
   waitFor(bobEvents);
   
   waitFor(aliceEvents);*/

   /*for (unsigned int i = 0; i < LOAD; i++)
   {
      runMediaProcess(*accounts[i], accountThreads[i]);
      waitFor(accountThreads[i]);
   }*/

   // Wait 60 seconds to measure performance during screen share conference
   //std::this_thread::sleep_for(std::chrono::milliseconds(60000));

   /*for (unsigned int i = 0; i < LOAD; i++)
   {
      if (accounts[i] != NULL)
      {
         delete accounts[i];
         accounts[i] = NULL;
      }
   }*/

}

TEST_F(VideoCallTests, DISABLED_AudioVideoCallH264HD_Turn) {

   TestAccount alice("alice", Account_NoInit);
   // use an external server, otherwise, as is, the SDK will bind TURN sockets to localhost, and TURN connection attempts to the 
   // external TURN server will fail
   alice.config.settings.domain = "opsip.silverstar.counterpath.net";
   alice.config.settings.outboundProxy = "";
   alice.init();
   alice.enable();

   TestAccount bob("bob", Account_NoInit);
   bob.config.settings.domain = "opsip.silverstar.counterpath.net";
   bob.config.settings.outboundProxy = "";
   bob.init();
   bob.enable();

   SipConversationSettings cs;
   cs.natTraversalMode = NatTraversalMode_TURN;
   cs.natTraversalServer = "numb.viagenie.ca";
   cs.turnUsername = "<EMAIL>";
   cs.turnPassword = "foo";
   alice.conversation->setDefaultSettings(alice.handle, cs);
   bob.conversation->setDefaultSettings(bob.handle, cs);


   // enable H.264
   alice.video->queryCodecList();
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   alice.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);
   
   bob.video->queryCodecList();
   bob.video->setCodecEnabled(cpc::hash(cpc::string("vp9")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("vp8")), false);
   bob.video->setCodecEnabled(cpc::hash(cpc::string("H264")), true);

   alice.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   bob.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   alice.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
   bob.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   alice.initiateVideo();
   bob.initiateVideo();

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, true);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]() {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      //assertCallHadMedia(alice, aliceCall, true, true);

      SipConversationState currState;
      ASSERT_EQ(alice.conversationState->getState(aliceCall, currState), CPCAPI2::kSuccess);
      ASSERT_EQ(1, currState.statistics.videoChannels.size());
#ifndef CPCAPI2_AUTO_TESTS_NO_VIDEO_STATS
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsReceived, 0);
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsSent, 0);
      ASSERT_TRUE(currState.statistics.videoChannels[0].encoder.width > 352);
      ASSERT_TRUE(currState.statistics.videoChannels[0].encoder.height > 288);
      ASSERT_TRUE(currState.statistics.videoChannels[0].decoder.width > 352);
      ASSERT_TRUE(currState.statistics.videoChannels[0].decoder.height > 288);
#endif
      ASSERT_NE(currState.statistics.audioChannels[0].streamDataCounters.packetsReceived, 0);
      ASSERT_NE(currState.statistics.audioChannels[0].streamDataCounters.packetsSent, 0);

      ASSERT_NE(0, currState.statistics.remoteVideoChannels.size());
      if (currState.statistics.videoChannels[0].streamDataCounters.packetsSent > 0)
      {
         ASSERT_NE(strlen(currState.statistics.videoChannels[0].encoder.plName), 0);
      }
      if (currState.statistics.videoChannels[0].streamDataCounters.packetsReceived > 0)
      {
         ASSERT_NE(strlen(currState.statistics.videoChannels[0].decoder.plName), 0);
      }
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      SipConversationHandle bobCall;
      assertNewConversationIncoming_ex(bob, &bobCall, alice.config.uri(), [](const NewConversationEvent& evt) {
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
      });

      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      //assertCallHadMedia(bob, bobCall, true, true);

      SipConversationState currState;
      ASSERT_EQ(bob.conversationState->getState(bobCall, currState), CPCAPI2::kSuccess);
      ASSERT_EQ(1, currState.statistics.videoChannels.size());
#ifndef CPCAPI2_AUTO_TESTS_NO_VIDEO_STATS
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsReceived, 0);
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsSent, 0);
      ASSERT_TRUE(currState.statistics.videoChannels[0].encoder.width > 352);
      ASSERT_TRUE(currState.statistics.videoChannels[0].encoder.height > 288);
      ASSERT_TRUE(currState.statistics.videoChannels[0].decoder.width > 352);
      ASSERT_TRUE(currState.statistics.videoChannels[0].decoder.height > 288);
#endif
      ASSERT_NE(currState.statistics.audioChannels[0].streamDataCounters.packetsReceived, 0);
      ASSERT_NE(currState.statistics.audioChannels[0].streamDataCounters.packetsSent, 0);

      ASSERT_NE(0, currState.statistics.remoteVideoChannels.size());
      if (currState.statistics.videoChannels[0].streamDataCounters.packetsSent > 0)
      {
         ASSERT_NE(strlen(currState.statistics.videoChannels[0].encoder.plName), 0);
      }
      if (currState.statistics.videoChannels[0].streamDataCounters.packetsReceived > 0)
      {
         ASSERT_NE(strlen(currState.statistics.videoChannels[0].decoder.plName), 0);
      }
   });

   
   waitFor2(aliceEvents, bobEvents);

}

#if (CPCAPI2_BRAND_CODEC_H265 == 1)
TEST_F(VideoCallTests, AudioVideoCallH265) {

   TestAccount alice("alice");
   TestAccount bob("bob");

   alice.enableOnlyThisVideoCodec("H.265");
   bob.enableOnlyThisVideoCodec("H.265");

   //alice.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_High);
   //bob.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_High);

   // note: 1080p likely won't work if we are running as a 32-bit process
   alice.video->setPreferredResolution(cpc::hash(cpc::string("H265")), CPCAPI2::Media::VideoCaptureResolution_High);
   bob.video->setPreferredResolution(cpc::hash(cpc::string("H265")), CPCAPI2::Media::VideoCaptureResolution_High);

   alice.initiateVideo();
   bob.initiateVideo();

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, true);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]() {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);

      SipConversationState currState;
      ASSERT_EQ(alice.conversationState->getState(aliceCall, currState), CPCAPI2::kSuccess);
      ASSERT_EQ(1, currState.statistics.videoChannels.size());
#ifndef CPCAPI2_AUTO_TESTS_NO_VIDEO_STATS
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsReceived, 0);
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsSent, 0);
      ASSERT_EQ(std::strcmp(currState.statistics.videoChannels[0].encoder.plName, "H265"), 0);
      ASSERT_TRUE(currState.statistics.videoChannels[0].encoder.width == 640);
      ASSERT_TRUE(currState.statistics.videoChannels[0].encoder.height == 480);
      ASSERT_TRUE(currState.statistics.videoChannels[0].decoder.width == 640);
      ASSERT_TRUE(currState.statistics.videoChannels[0].decoder.height == 480);
#endif
      ASSERT_NE(0, currState.statistics.remoteVideoChannels.size());
      if (currState.statistics.videoChannels[0].streamDataCounters.packetsSent > 0)
      {
         ASSERT_NE(strlen(currState.statistics.videoChannels[0].encoder.plName), 0);
      }
      if (currState.statistics.videoChannels[0].streamDataCounters.packetsReceived > 0)
      {
         ASSERT_NE(strlen(currState.statistics.videoChannels[0].decoder.plName), 0);
      }
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      SipConversationHandle bobCall;
      assertNewConversationIncoming_ex(bob, &bobCall, alice.config.uri(), [](const NewConversationEvent& evt) {
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
      });

      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      //assertCallHadMedia(bob, bobCall, true, true);

      SipConversationState currState;
      ASSERT_EQ(bob.conversationState->getState(bobCall, currState), CPCAPI2::kSuccess);
      ASSERT_EQ(1, currState.statistics.videoChannels.size());
#ifndef CPCAPI2_AUTO_TESTS_NO_VIDEO_STATS
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsReceived, 0);
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsSent, 0);
      ASSERT_EQ(std::strcmp(currState.statistics.videoChannels[0].encoder.plName, "H265"), 0);
      ASSERT_TRUE(currState.statistics.videoChannels[0].encoder.width == 640);
      ASSERT_TRUE(currState.statistics.videoChannels[0].encoder.height == 480);
      ASSERT_TRUE(currState.statistics.videoChannels[0].decoder.width == 640);
      ASSERT_TRUE(currState.statistics.videoChannels[0].decoder.height == 480);
#endif
      ASSERT_NE(0, currState.statistics.remoteVideoChannels.size());
      if (currState.statistics.videoChannels[0].streamDataCounters.packetsSent > 0)
      {
         ASSERT_NE(strlen(currState.statistics.videoChannels[0].encoder.plName), 0);
      }
      if (currState.statistics.videoChannels[0].streamDataCounters.packetsReceived > 0)
      {
         ASSERT_NE(strlen(currState.statistics.videoChannels[0].decoder.plName), 0);
      }
   });

   
   waitFor2(aliceEvents, bobEvents);

}
#endif // #if (CPCAPI2_BRAND_CODEC_H265 == 1)


TEST_F(VideoCallTests, DISABLED_AudioVideoCallVP9_1080p) {
   
   TestAccount alice("alice");
   TestAccount bob("bob");
   
   alice.enableOnlyThisVideoCodec("VP9");
   bob.enableOnlyThisVideoCodec("VP9");
   
   alice.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_HD_1920x1080p);
   bob.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_HD_1920x1080p);
   
   alice.video->setPreferredResolution(cpc::hash(cpc::string("vp9")), CPCAPI2::Media::VideoCaptureResolution_HD_1920x1080p);
   bob.video->setPreferredResolution(cpc::hash(cpc::string("vp9")), CPCAPI2::Media::VideoCaptureResolution_HD_1920x1080p);
   
   alice.initiateVideo();
   bob.initiateVideo();
   
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, true);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);
   
   auto aliceEvents = std::async(std::launch::async, [&]() {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      
      SipConversationState currState;
      ASSERT_EQ(alice.conversationState->getState(aliceCall, currState), CPCAPI2::kSuccess);
      ASSERT_EQ(1, currState.statistics.videoChannels.size());
#ifndef CPCAPI2_AUTO_TESTS_NO_VIDEO_STATS
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsReceived, 0);
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsSent, 0);
      ASSERT_EQ(std::strcmp(currState.statistics.videoChannels[0].encoder.plName, "VP9"), 0);
      ASSERT_TRUE(currState.statistics.videoChannels[0].encoder.width == 1920);
      ASSERT_TRUE(currState.statistics.videoChannels[0].encoder.height == 1080);
      ASSERT_TRUE(currState.statistics.videoChannels[0].decoder.width == 1920);
      ASSERT_TRUE(currState.statistics.videoChannels[0].decoder.height == 1080);
#endif
      ASSERT_NE(0, currState.statistics.remoteVideoChannels.size());
      if (currState.statistics.videoChannels[0].streamDataCounters.packetsSent > 0)
      {
         ASSERT_NE(strlen(currState.statistics.videoChannels[0].encoder.plName), 0);
      }
      if (currState.statistics.videoChannels[0].streamDataCounters.packetsReceived > 0)
      {
         ASSERT_NE(strlen(currState.statistics.videoChannels[0].decoder.plName), 0);
      }
   });
   
   auto bobEvents = std::async(std::launch::async, [&]() {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      SipConversationHandle bobCall;
      assertNewConversationIncoming_ex(bob, &bobCall, alice.config.uri(), [](const NewConversationEvent& evt) {
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
      });
      
      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      //assertCallHadMedia(bob, bobCall, true, true);
      
      SipConversationState currState;
      ASSERT_EQ(bob.conversationState->getState(bobCall, currState), CPCAPI2::kSuccess);
      ASSERT_EQ(1, currState.statistics.videoChannels.size());
#ifndef CPCAPI2_AUTO_TESTS_NO_VIDEO_STATS
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsReceived, 0);
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsSent, 0);
      ASSERT_EQ(std::strcmp(currState.statistics.videoChannels[0].encoder.plName, "VP9"), 0);
      ASSERT_TRUE(currState.statistics.videoChannels[0].encoder.width == 1920);
      ASSERT_TRUE(currState.statistics.videoChannels[0].encoder.height == 1080);
      ASSERT_TRUE(currState.statistics.videoChannels[0].decoder.width == 1920);
      ASSERT_TRUE(currState.statistics.videoChannels[0].decoder.height == 1080);
#endif
      ASSERT_NE(0, currState.statistics.remoteVideoChannels.size());
      if (currState.statistics.videoChannels[0].streamDataCounters.packetsSent > 0)
      {
         ASSERT_NE(strlen(currState.statistics.videoChannels[0].encoder.plName), 0);
      }
      if (currState.statistics.videoChannels[0].streamDataCounters.packetsReceived > 0)
      {
         ASSERT_NE(strlen(currState.statistics.videoChannels[0].decoder.plName), 0);
      }
   });
   
   
   waitFor2(aliceEvents, bobEvents);
   
}


TEST_F(VideoCallTests, AudioVideoCallNoSetLocalVideoPreviewResolution) {

   TestAccount alice("alice");
   TestAccount bob("bob");

   alice.enableOnlyThisVideoCodec("H.264");
   bob.enableOnlyThisVideoCodec("H.264");

   alice.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_HD_1280x720p);
   //bob.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_HD_1280x720p);

   alice.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_HD_1280x720p);

   bob.video->setPreferredResolution(cpc::hash(cpc::string("vp8")), CPCAPI2::Media::VideoCaptureResolution_Low);
   bob.video->setPreferredResolution(cpc::hash(cpc::string("vp9")), CPCAPI2::Media::VideoCaptureResolution_Low);
   bob.video->setPreferredResolution(cpc::hash(cpc::string("H263")), CPCAPI2::Media::VideoCaptureResolution_Low);
   bob.video->setPreferredResolution(cpc::hash(cpc::string("H263-1998")), CPCAPI2::Media::VideoCaptureResolution_Low);
   bob.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_HD_1920x1080p);
   bob.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_HD_1280x720p);
   bob.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_Low);
   bob.video->setPreferredResolution(cpc::hash(cpc::string("H264")), CPCAPI2::Media::VideoCaptureResolution_HD_1280x720p);

   alice.initiateVideo();
   bob.initiateVideo();

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, true);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]() {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);

      SipConversationState currState;
      ASSERT_EQ(alice.conversationState->getState(aliceCall, currState), CPCAPI2::kSuccess);
      ASSERT_EQ(1, currState.statistics.videoChannels.size());
#ifndef CPCAPI2_AUTO_TESTS_NO_VIDEO_STATS
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsReceived, 0);
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsSent, 0);
      ASSERT_EQ(std::strcmp(currState.statistics.videoChannels[0].encoder.plName, "H264"), 0);
      ASSERT_TRUE(currState.statistics.videoChannels[0].encoder.width == 1280);
      ASSERT_TRUE(currState.statistics.videoChannels[0].encoder.height == 720);
      ASSERT_TRUE(currState.statistics.videoChannels[0].decoder.width == 1280);
      ASSERT_TRUE(currState.statistics.videoChannels[0].decoder.height == 720);
#endif
      ASSERT_NE(0, currState.statistics.remoteVideoChannels.size());
      if (currState.statistics.videoChannels[0].streamDataCounters.packetsSent > 0)
      {
         ASSERT_NE(strlen(currState.statistics.videoChannels[0].encoder.plName), 0);
      }
      if (currState.statistics.videoChannels[0].streamDataCounters.packetsReceived > 0)
      {
         ASSERT_NE(strlen(currState.statistics.videoChannels[0].decoder.plName), 0);
      }
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      SipConversationHandle bobCall;
      assertNewConversationIncoming_ex(bob, &bobCall, alice.config.uri(), [](const NewConversationEvent& evt) {
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
      });

      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      bob.video->setVideoMute(true);

      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      bob.video->setVideoMute(false);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));

      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      //assertCallHadMedia(bob, bobCall, true, true);

      SipConversationState currState;
      ASSERT_EQ(bob.conversationState->getState(bobCall, currState), CPCAPI2::kSuccess);
      ASSERT_EQ(1, currState.statistics.videoChannels.size());
#ifndef CPCAPI2_AUTO_TESTS_NO_VIDEO_STATS
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsReceived, 0);
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsSent, 0);
      ASSERT_EQ(std::strcmp(currState.statistics.videoChannels[0].encoder.plName, "H264"), 0);
      ASSERT_TRUE(currState.statistics.videoChannels[0].encoder.width == 1280);
      ASSERT_TRUE(currState.statistics.videoChannels[0].encoder.height == 720);
      ASSERT_TRUE(currState.statistics.videoChannels[0].decoder.width == 1280);
      ASSERT_TRUE(currState.statistics.videoChannels[0].decoder.height == 720);
#endif
      ASSERT_NE(0, currState.statistics.remoteVideoChannels.size());
      if (currState.statistics.videoChannels[0].streamDataCounters.packetsSent > 0)
      {
         ASSERT_NE(strlen(currState.statistics.videoChannels[0].encoder.plName), 0);
      }
      if (currState.statistics.videoChannels[0].streamDataCounters.packetsReceived > 0)
      {
         ASSERT_NE(strlen(currState.statistics.videoChannels[0].decoder.plName), 0);
      }
   });

   
   waitFor2(aliceEvents, bobEvents);

}

TEST_F(VideoCallTests, DISABLED_AudioVideoCallVP9_720p) {

   TestAccount alice("alice");
   TestAccount bob("bob");

   alice.enableOnlyThisVideoCodec("VP9");
   bob.enableOnlyThisVideoCodec("VP9");

   alice.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_HD_1280x720p);
   bob.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_HD_1280x720p);

   alice.video->setPreferredResolution(cpc::hash(cpc::string("vp9")), CPCAPI2::Media::VideoCaptureResolution_HD_1280x720p);
   bob.video->setPreferredResolution(cpc::hash(cpc::string("vp9")), CPCAPI2::Media::VideoCaptureResolution_HD_1280x720p);

   alice.initiateVideo();
   bob.initiateVideo();

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, true);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]() {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);

      SipConversationState currState;
      ASSERT_EQ(alice.conversationState->getState(aliceCall, currState), CPCAPI2::kSuccess);
      ASSERT_EQ(1, currState.statistics.videoChannels.size());
#ifndef CPCAPI2_AUTO_TESTS_NO_VIDEO_STATS
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsReceived, 0);
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsSent, 0);
      ASSERT_EQ(std::strcmp(currState.statistics.videoChannels[0].encoder.plName, "VP9"), 0);
      ASSERT_TRUE(currState.statistics.videoChannels[0].encoder.width == 1280);
      ASSERT_TRUE(currState.statistics.videoChannels[0].encoder.height == 720);
      ASSERT_TRUE(currState.statistics.videoChannels[0].decoder.width == 1280);
      ASSERT_TRUE(currState.statistics.videoChannels[0].decoder.height == 720);
#endif
      ASSERT_NE(0, currState.statistics.remoteVideoChannels.size());
      if (currState.statistics.videoChannels[0].streamDataCounters.packetsSent > 0)
      {
         ASSERT_NE(strlen(currState.statistics.videoChannels[0].encoder.plName), 0);
      }
      if (currState.statistics.videoChannels[0].streamDataCounters.packetsReceived > 0)
      {
         ASSERT_NE(strlen(currState.statistics.videoChannels[0].decoder.plName), 0);
      }
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      SipConversationHandle bobCall;
      assertNewConversationIncoming_ex(bob, &bobCall, alice.config.uri(), [](const NewConversationEvent& evt) {
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
      });

      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      //assertCallHadMedia(bob, bobCall, true, true);

      SipConversationState currState;
      ASSERT_EQ(bob.conversationState->getState(bobCall, currState), CPCAPI2::kSuccess);
      ASSERT_EQ(1, currState.statistics.videoChannels.size());
#ifndef CPCAPI2_AUTO_TESTS_NO_VIDEO_STATS
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsReceived, 0);
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsSent, 0);
      ASSERT_EQ(std::strcmp(currState.statistics.videoChannels[0].encoder.plName, "VP9"), 0);
      ASSERT_TRUE(currState.statistics.videoChannels[0].encoder.width == 1280);
      ASSERT_TRUE(currState.statistics.videoChannels[0].encoder.height == 720);
      ASSERT_TRUE(currState.statistics.videoChannels[0].decoder.width == 1280);
      ASSERT_TRUE(currState.statistics.videoChannels[0].decoder.height == 720);
#endif
      ASSERT_NE(0, currState.statistics.remoteVideoChannels.size());
      if (currState.statistics.videoChannels[0].streamDataCounters.packetsSent > 0)
      {
         ASSERT_NE(strlen(currState.statistics.videoChannels[0].encoder.plName), 0);
      }
      if (currState.statistics.videoChannels[0].streamDataCounters.packetsReceived > 0)
      {
         ASSERT_NE(strlen(currState.statistics.videoChannels[0].decoder.plName), 0);
      }
   });

   
   waitFor2(aliceEvents, bobEvents);

}

#if (CPCAPI2_BRAND_CODEC_H265 == 1)
TEST_F(VideoCallTests, H265VideoCall)
{
   TestAccount alice("alice");
   TestAccount bob("bob");
   //to test H264 set codec = "H.264"
   cpc::string codec = "H.265";
   unsigned int codecHash = (codec == "H.265") ? cpc::hash("H265") : cpc::hash("H264");
   long durationSec = /*60*/60;
   CPCAPI2::Media::VideoCaptureResolution resolution = CPCAPI2::Media::VideoCaptureResolution_Standard;

   TestVideoHandler videoHandler;
   alice.video->setHandler(&videoHandler);
   alice.video->queryCodecList();

   // let the camera get going and allow queryCodecs to run
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   alice.video->setCodecEnabled(cpc::hash("vp9"), false);
   alice.video->setCodecEnabled(cpc::hash("vp8"), false);
   alice.video->setCodecEnabled(cpc::hash("H263"), false);
   alice.video->setCodecEnabled(cpc::hash("H263-1998"), false);
   alice.video->setCodecEnabled(cpc::hash("H264"), (codec == "H.264"));
   alice.video->setCodecEnabled(cpc::hash("H265"), (codec == "H.265"));

   bob.video->setHandler(&videoHandler);
   bob.video->queryCodecList();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   bob.video->setCodecEnabled(cpc::hash("vp9"), false);
   bob.video->setCodecEnabled(cpc::hash("vp8"), false);
   bob.video->setCodecEnabled(cpc::hash("H263"), false);
   bob.video->setCodecEnabled(cpc::hash("H263-1998"), false);
   bob.video->setCodecEnabled(cpc::hash("H264"), (codec == "H.264"));
   bob.video->setCodecEnabled(cpc::hash("H265"), (codec == "H.265"));

   alice.video->setLocalVideoPreviewResolution(resolution);
   bob.video->setLocalVideoPreviewResolution(resolution);

   alice.video->setPreferredResolution(codecHash, resolution);
   bob.video->setPreferredResolution(codecHash, resolution);

   int wW = 640, wH = 360;
   switch (resolution)
   {
   case VideoCaptureResolution_Low:
   case VideoCaptureResolution_Standard:
      wW = 352; wH = 288;
      break;
   case VideoCaptureResolution_High:
      wW = 720; wH = 480;
      break;
   case VideoCaptureResolution_HD_1280x720p:
   case VideoCaptureResolution_HD_1920x1080p:
      wW = 1280; wH = 720;
      break;
   }

   alice.initiateVideo(true, false);
   bob.initiateVideo(false, true);

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, false);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]() {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(durationSec*1000));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      assertCallHadOutgoingMedia(alice, aliceCall, false, true);
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      SipConversationHandle bobCall;
      assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Audio, false);
      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(durationSec * 1000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      assertCallHadIncomingMedia(bob, bobCall, false, true);
   });

   
   waitFor2(aliceEvents, bobEvents);

}

// configure a remote <NAME_EMAIL>
// for this test
TEST_F(VideoCallTests, DISABLED_H265VideoCall2SimplePhone)
{
   TestAccount alice("alice");
   cpc::string codec = "H.265";
   unsigned int codecHash = (codec == "H.265") ? cpc::hash("H265") : cpc::hash("H264");
   long durationSec = 60 * 60;
   CPCAPI2::Media::VideoCaptureResolution resolution = CPCAPI2::Media::VideoCaptureResolution_HD_1280x720p;

   TestVideoHandler videoHandler;
   alice.video->setHandler(&videoHandler);
   alice.video->queryCodecList();

   // let the camera get going and allow queryCodecs to run
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   alice.video->setCodecEnabled(cpc::hash("vp9"), false);
   alice.video->setCodecEnabled(cpc::hash("vp8"), false);
   alice.video->setCodecEnabled(cpc::hash("H263"), false);
   alice.video->setCodecEnabled(cpc::hash("H263-1998"), false);
   alice.video->setCodecEnabled(cpc::hash("H264"), (codec == "H.264"));
   alice.video->setCodecEnabled(cpc::hash("H265"), (codec == "H.265"));

   alice.video->setLocalVideoPreviewResolution(resolution);
   alice.video->setPreferredResolution(codecHash, resolution);

#if _WIN32
   HWND hwndAliceCapture = NULL;
   ASSERT_EQ(0, ViECreateWindow(hwndAliceCapture, 0, 0, 640, 360, "Alice (capture)"));
   alice.video->startCapture();
   alice.video->setLocalVideoRenderTarget(hwndAliceCapture);

   HWND hwndAliceRemote = NULL;
   ASSERT_EQ(0, ViECreateWindow(hwndAliceRemote, 0, 360, 640, 360, "Alice (incoming)"));
   alice.video->setIncomingVideoRenderTarget(hwndAliceRemote);
#endif // _WIN32
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, false);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
   alice.conversation->addParticipant(aliceCall, "sip:<EMAIL>");
   alice.conversation->start(aliceCall);
   auto aliceEvents = std::async(std::launch::async, [&]() {
      assertNewConversationOutgoing_time(alice, aliceCall, "sip:<EMAIL>", 3000);
      assertConversationStateChanged_time(alice, aliceCall, ConversationState_RemoteRinging, 3000);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged_time(alice, aliceCall, ConversationState_Connected, 3000);
      std::this_thread::sleep_for(std::chrono::milliseconds(durationSec * 1000));
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
      assertCallHadOutgoingMedia(alice, aliceCall, false, true);
   });

   
   waitFor(aliceEvents);

#if _WIN32
   alice.video->stopCapture();
   alice.video->setLocalVideoRenderTarget(NULL);
   DestroyWindow(hwndAliceCapture);
   DestroyWindow(hwndAliceRemote);
#endif // _WIN32
}
#endif


struct VideoTestParam
{
   std::string codecType;
};

class PerCodecVideoCallTests : public CpcapiAutoTestWithParam<VideoTestParam>
{
public:
   // seems to need a newer version of gtest
   /*
   struct PrintToStringParamName
   {
      template <class ParamType>
      std::string operator()( const ::testing::TestParamInfo<ParamType>& info ) const
      {
         auto location = static_cast<Location>(info.param);
         return location.Name;
      }
   };
   */
};

VideoTestParam kTestParams[] = { { "H.264" }, { "VP8" } };

INSTANTIATE_TEST_SUITE_P(VideoCallTests, PerCodecVideoCallTests,
   ::testing::ValuesIn(kTestParams));

// to run just these tests (e.g. for local development), adjust tests_include in cpcapi2_auto_tests.cpp
// to have a value of */PerCodecVideoCallTests.*

TEST_P(PerCodecVideoCallTests, AudioVideoCall_Hold_Unhold)
{
   const VideoTestParam testParam = GetParam();

   TestAccount alice("alice");
   TestAccount bob("bob");
   
   alice.enableOnlyThisVideoCodec(testParam.codecType.c_str());
   bob.enableOnlyThisVideoCodec(testParam.codecType.c_str());
   
   alice.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_HD_1280x720p);
   bob.video->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_HD_1280x720p);
   
   alice.video->setPreferredResolution(cpc::hash(cpc::string(testParam.codecType.c_str())), CPCAPI2::Media::VideoCaptureResolution_HD_1280x720p);
   bob.video->setPreferredResolution(cpc::hash(cpc::string(testParam.codecType.c_str())), CPCAPI2::Media::VideoCaptureResolution_HD_1280x720p);
   
   const int beforeHoldLengthSec = 10;
   const int duringHoldLengthSec = 5;
   const int offHoldLengthSec = 3;

   // Only start capture on alice side, and only display remote vide on bob side.
   // OBELISK-4405 tracked an issue where on mac camera would stop part way through
   // the test if both sides were capturing at the same time
   //alice.videoHelper->startVideo(true, false);
   //bob.videoHelper->startVideo(false, true);
   alice.initiateVideo(true, false);
   bob.initiateVideo(false, true);
   
   CpTestWindowHandle nswindowAliceCaptureWindow = alice.videoHelper->getNsLocalWindow();
   CpTestWindowHandle nswindowBobRemoteWindow = bob.videoHelper->getNsIncomingWindow();


   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, true);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      CPCAPI2::SipAccount::SipAccountHandle aliceHandle;
      CPCAPI2::SipAccount::SipAccountEnabledEvent enabledEvt;
      ASSERT_TRUE(cpcExpectEvent(alice.accountEvents, "SipAccountHandlerInternal::onAccountEnabled",
                                 20000, CPCAPI2::test::AlwaysTruePred(), aliceHandle, enabledEvt));
      ASSERT_EQ(alice.handle, aliceHandle);
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      
      std::this_thread::sleep_for(std::chrono::milliseconds(beforeHoldLengthSec * 1000));
      
#ifndef __linux__
      ASSERT_LE(alice.videoHelper->LocalWindowSecondsSinceLastRender(), 1);
#endif
      
      // bob placed call on hold (INVITE CSeq 2)
      assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendOnly);
      assertSuccess(alice.conversation->accept(aliceCall));
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_FALSE(evt.localHold);
         ASSERT_TRUE(evt.remoteHold);
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaDirection_ReceiveOnly, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaDirection_ReceiveOnly, evt.localMediaInfo[1].mediaDirection);
      });
      
      std::this_thread::sleep_for(std::chrono::milliseconds(duringHoldLengthSec * 1000));

#ifndef __linux__
      ASSERT_LE(alice.videoHelper->LocalWindowSecondsSinceLastRender(), 1);
#endif

      // bob placed call on off hold (INVITE CSeq 4)
      assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendReceive);
      assertSuccess(alice.conversation->accept(aliceCall));
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      
      std::this_thread::sleep_for(std::chrono::milliseconds(beforeHoldLengthSec * 1000));
      
#ifndef __linux__
      ASSERT_LE(alice.videoHelper->LocalWindowSecondsSinceLastRender(), 1);
#endif
      
      std::this_thread::sleep_for(std::chrono::milliseconds(offHoldLengthSec * 1000));
      
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      //assertCallHadMedia(alice, aliceCall, true, true);
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      CPCAPI2::SipAccount::SipAccountHandle bobHandle;
      CPCAPI2::SipAccount::SipAccountEnabledEvent enabledEvt;
      ASSERT_TRUE(cpcExpectEvent(bob.accountEvents, "SipAccountHandlerInternal::onAccountEnabled",
                                 20000, CPCAPI2::test::AlwaysTruePred(), bobHandle, enabledEvt));
      ASSERT_EQ(bob.handle, bobHandle);
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      SipConversationHandle bobCall;
      assertNewConversationIncoming_ex(bob, &bobCall, alice.config.uri(), [](const NewConversationEvent& evt){
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
      });

      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(beforeHoldLengthSec * 1000));
      
#ifndef __linux__
      ASSERT_LE(bob.videoHelper->IncomingWindowSecondsSinceLastRender(), 1);
#endif
      
      assertSuccess(bob.conversation->hold(bobCall)); // (INVITE CSeq 2)
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
         ASSERT_TRUE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendOnly, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaDirection_SendOnly, evt.localMediaInfo[1].mediaDirection);
      });
      
      std::this_thread::sleep_for(std::chrono::milliseconds(duringHoldLengthSec * 1000));
      
      assertSuccess(bob.conversation->unhold(bobCall)); // (INVITE CSeq 4)
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      
      std::this_thread::sleep_for(std::chrono::milliseconds(offHoldLengthSec * 1000));
      
#ifndef __linux__
      ASSERT_LE(bob.videoHelper->IncomingWindowSecondsSinceLastRender(), 1);
#endif
      
      std::this_thread::sleep_for(std::chrono::milliseconds(offHoldLengthSec * 1000));
      
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      //assertCallHadMedia(bob, bobCall, true, true);
   });

   
   waitFor2(aliceEvents, bobEvents);

}

TEST_F(VideoCallTests, AudioCallAddVideoInvalidSurface) {
   TestAccount alice("alice");
   TestAccount bob("bob");
   
  
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);
   
   auto aliceEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(1, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      
      // add video
      alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
      alice.conversation->sendMediaChangeRequest(aliceCall);
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
   });
   
   auto bobEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      

      bob.initiateVideo(false, true);
      // destroy video surface without letting SDK know
      bob.videoHelper.reset();
      
      
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      
      SipConversationHandle bobCall;
      assertNewConversationIncoming_ex(bob, &bobCall, alice.config.uri(), [](const NewConversationEvent& evt){
         ASSERT_EQ(1, evt.remoteMediaInfo.size());
      });
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(1, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
      });
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertConversationMediaChangeRequest_ex(bob, bobCall, [](const ConversationMediaChangeRequestEvent& evt){
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.remoteMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.remoteMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.remoteMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.remoteMediaInfo[1].mediaDirection);
      });
      
      // accept the re-INVITE which adds video
      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      
      // not necessary to avoid crash
      //bob.video->setIncomingVideoRenderTarget(NULL);
      
      bob.conversation->accept(bobCall);
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      
      std::this_thread::sleep_for(std::chrono::milliseconds(10000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
   });
   
   
   waitFor2(aliceEvents, bobEvents);
   
}

TEST_F(VideoCallTests, AudioCallAddVideoInvalidSurface2)
{
   TestAccount alice("alice");
   
   for (int i = 0; i < 3; ++i)
   {
      alice.initiateVideo();
      alice.videoHelper.reset();
      std::this_thread::sleep_for(std::chrono::milliseconds(3000));
      alice.videoHelper.reset(new TestVideoHelper("foo"));
      alice.videoHelper->setTestAccount(&alice);
   }
}

bool VideoCallTestsPreviewOnly_BadLogParseFunction(const char *message, CPCAPI2::LogLevel level)
{
   bool failTest = false;
   if (level == LogLevel_Error)
   {
      // TODO: update SDK to not log this message
      if (NULL != strstr(message, "unable to set stereo mode while playing side is initialized"))
      {
         failTest = false;
      }
      // TODO: update SDK to not log this message
      else if (NULL != strstr(message, "recording in stereo is not supported"))
      {
         failTest = false;
      }
      // TODO: update SDK to not log this message
      else if (NULL != strstr(message, "Failed to set agc to enabled"))
      {
         failTest = false;
      }
      // TODO: update SDK to not log this message
      else if (NULL != strstr(message, "failed to enable stereo recording"))
      {
         failTest = false;
      }
      // TODO: update SDK to not log this message
      else if (NULL != strstr(message, "unable to set thread priority"))
      {
         failTest = false;
      }
      else
      {
         ADD_FAILURE() << "Unexpectd error level log message: " << message;
         // any other error log line should fail our test
         failTest = true;
      }
   }
   
   return failTest;
}
 
TEST_F(VideoCallTests, PreviewOnly)
{
#if _WIN32
   // "Failed to enumerate CLSID_SystemDeviceEnum, error 0x1. No webcam exist?" logged by WebRTC at error level on docker windows
   if (TestEnvironmentConfig::dockerContainerized()) GTEST_SKIP() << "Test not supported in docker windows environments";
#endif

   AutoTestsLogger::ScopedSetBadLogMessageCheckFunction blm(&VideoCallTestsPreviewOnly_BadLogParseFunction);

   TestAccount alice("alice", Account_Init);

   if (alice.phone->getInstanceId() == "ed6334e3-4d15-5485-ae86-33809bf7f356" /* # YVR-JenSL-SDK1 */)
   {
      GTEST_SKIP() << "Skipping since YVR-JenSL-SDK1 cannot currently create a render surface";
   }

   alice.initiateVideo(true, false);

   std::this_thread::sleep_for(std::chrono::seconds(10));
   ASSERT_EQ(0, AutoTestsLogger::instance().getBadMessagesCount());
}


TEST_F(VideoCallTests, TestExtQueryScreenshareDeviceList)
{
   TestAccount alice("alice");
   
   class MyScreenshareDeviceListHandlerHandler : public ScreenshareDeviceListHandler
   {
   public:
      void onScreenshareDeviceList(const ScreenshareDeviceListEvent& evt)
      {
      
      }
   };
   
   MyScreenshareDeviceListHandlerHandler handler;
   
   VideoExt* videoExt = VideoExt::getInterface(alice.media);
   
   for (int i = 0; i < 8; ++i)
   {
      videoExt->queryScreenshareDeviceList(&handler, true, true);
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   }
}

// OBELISK-5912: currently failing: bob is sending alice H264 at 1280x720 -- seemingly not honouring alice's profile level
TEST_F(VideoCallTests, DISABLED_AudioVideoCallH264_SingleNalUnit_MixedRes)
{
   TestAccount alice("alice");
   TestAccount bob("bob");

   size_t videoCodecHash;
   cpc::string videoCodecPlType;
   videoCodecPlType = "H264";
   videoCodecHash = cpc::hash(videoCodecPlType);
   
   // what the clients request to send
   VideoCaptureResolution aliceRequstSendVideoRes = VideoCaptureResolution_848x480p;
   VideoCaptureResolution bobRequstSendVideoRes = VideoCaptureResolution_HD_1280x720p;
   
   // what the clients actually send, based on profile level limits
   int aliceActualSendVideoResWidth = 848;
   int aliceActualSendVideoResHeight = 480;
   int bobActualSendVideoResWidth = 720;
   int bobActualSendVideoResHeight = 480;
   
   alice.enableOnlyThisVideoCodec("H.264");
   bob.enableOnlyThisVideoCodec("H.264");

   alice.video->setCodecEncodingHardwareAccelerationEnabled(videoCodecHash, false);
   alice.video->setCodecDecodingHardwareAccelerationEnabled(videoCodecHash, false);
   bob.video->setCodecEncodingHardwareAccelerationEnabled(videoCodecHash, false);
   bob.video->setCodecDecodingHardwareAccelerationEnabled(videoCodecHash, false);

   alice.video->setLocalVideoPreviewResolution(aliceRequstSendVideoRes);
   bob.video->setLocalVideoPreviewResolution(bobRequstSendVideoRes);

   alice.video->setPreferredResolution(videoCodecHash, aliceRequstSendVideoRes);
   bob.video->setPreferredResolution(videoCodecHash, bobRequstSendVideoRes);

   alice.initiateVideo();
   bob.initiateVideo();

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, true);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]() {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);

      SipConversationState currState;
      ASSERT_EQ(alice.conversationState->getState(aliceCall, currState), CPCAPI2::kSuccess);
      ASSERT_EQ(1, currState.statistics.videoChannels.size());

      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsReceived, 0);
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsSent, 0);
      ASSERT_EQ(std::strcmp(currState.statistics.videoChannels[0].encoder.plName, videoCodecPlType), 0);
      ASSERT_EQ(currState.statistics.videoChannels[0].encoder.width, aliceActualSendVideoResWidth);
      ASSERT_EQ(currState.statistics.videoChannels[0].encoder.height, aliceActualSendVideoResHeight);
      ASSERT_EQ(currState.statistics.videoChannels[0].decoder.width, bobActualSendVideoResWidth);
      ASSERT_EQ(currState.statistics.videoChannels[0].decoder.height, bobActualSendVideoResHeight);

      ASSERT_NE(0, currState.statistics.remoteVideoChannels.size());
      ASSERT_NE(0, currState.statistics.videoChannels[0].streamDataCounters.packetsSent);
      ASSERT_NE(strlen(currState.statistics.videoChannels[0].encoder.plName), 0);
      ASSERT_NE(0, currState.statistics.videoChannels[0].streamDataCounters.packetsReceived);
      ASSERT_NE(strlen(currState.statistics.videoChannels[0].decoder.plName), 0);
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      SipConversationHandle bobCall;
      assertNewConversationIncoming_ex(bob, &bobCall, alice.config.uri(), [](const NewConversationEvent& evt) {
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
      });

      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      
      SipConversationState currState;
      ASSERT_EQ(bob.conversationState->getState(bobCall, currState), CPCAPI2::kSuccess);
      ASSERT_EQ(1, currState.statistics.videoChannels.size());

      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsReceived, 0);
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsSent, 0);
      ASSERT_EQ(std::strcmp(currState.statistics.videoChannels[0].encoder.plName, videoCodecPlType), 0);
      ASSERT_EQ(currState.statistics.videoChannels[0].encoder.width, bobActualSendVideoResWidth);
      ASSERT_EQ(currState.statistics.videoChannels[0].encoder.height, bobActualSendVideoResHeight);
      ASSERT_EQ(currState.statistics.videoChannels[0].decoder.width, aliceActualSendVideoResWidth);
      ASSERT_EQ(currState.statistics.videoChannels[0].decoder.height, aliceActualSendVideoResHeight);

      ASSERT_NE(0, currState.statistics.remoteVideoChannels.size());
      ASSERT_NE(0, currState.statistics.videoChannels[0].streamDataCounters.packetsSent);
      ASSERT_NE(strlen(currState.statistics.videoChannels[0].encoder.plName), 0);
      ASSERT_NE(0, currState.statistics.videoChannels[0].streamDataCounters.packetsReceived);
      ASSERT_NE(strlen(currState.statistics.videoChannels[0].decoder.plName), 0);
   });

   waitFor2(aliceEvents, bobEvents);
}

struct CallOptions
{
   bool disableHardwareAccel = false;
   int callDurationSec = 5;
   bool inducePacketloss = false;
};

void doVideoCall(TestAccount& alice, TestAccount& bob, const cpc::string& videoCodec, VideoCaptureResolution videoRes, void (*codecConfig)(CPCAPI2::Media::Video*), CallOptions& opt)
{
   opt.callDurationSec = 30;

   size_t videoCodecHash;
   cpc::string videoCodecPlType;
   if (videoCodec == "H.264")
   {
      videoCodecPlType = "H264";
      videoCodecHash = cpc::hash(videoCodecPlType);
      
   }
   else if (videoCodec == "VP8")
   {
      videoCodecPlType = "VP8";
      videoCodecHash = cpc::hash("vp8");
   }
   else
   {
      FAIL() << "Unsupported video codec '" << videoCodec << "'; must be 'H.264' or 'VP8'";
   }
   
   int videoResWidth = 0;
   int videoResHeight = 0;
   bool skipWidthCheck = false;
   switch (videoRes)
   {
      case VideoCaptureResolution_Low:
         videoResWidth = 176;
         videoResHeight = 144;
         break;
      case VideoCaptureResolution_Standard:
         videoResWidth = 352;
         videoResHeight = 288;
         break;
      case VideoCaptureResolution_High:
         videoResWidth = 640;
         videoResHeight = 480;
         break;
      case VideoCaptureResolution_HD_1280x720p:
         videoResWidth = 1280;
         videoResHeight = 720;
         break;
      case VideoCaptureResolution_HD_1920x1080p:
         // conditional video res taken from VideoImpl.cpp
#if (defined(TARGET_OS_IPHONE) && TARGET_OS_IPHONE) || (defined(TARGET_IPHONE_SIMULATOR) && TARGET_IPHONE_SIMULATOR) || (defined(TARGET_OS_ANDROID) && TARGET_OS_ANDROID)
         videoResWidth = 1280;
         videoResHeight = 720;
#else
         videoResWidth = 1920;
         videoResHeight = 1080;
#endif
         break;
      case VideoCaptureResolution_MaxSupported:
#if (defined(TARGET_OS_IPHONE) && TARGET_OS_IPHONE) || (defined(TARGET_IPHONE_SIMULATOR) && TARGET_IPHONE_SIMULATOR) || (defined(TARGET_OS_ANDROID) && TARGET_OS_ANDROID)
         videoResWidth = 1280;
         videoResHeight = 720;
#else
         videoResWidth = 1920;
         videoResHeight = 1080;
#endif
         break;
      case VideoCaptureResolution_848x480p:
         videoResWidth = 848;
         videoResHeight = 480;
         skipWidthCheck = true; // OBELISK-6260
         break;
   }

   alice.enableOnlyThisVideoCodec(videoCodec.c_str());
   bob.enableOnlyThisVideoCodec(videoCodec.c_str());

   if (opt.disableHardwareAccel)
   {
      alice.video->setCodecEncodingHardwareAccelerationEnabled(videoCodecHash, false);
      alice.video->setCodecDecodingHardwareAccelerationEnabled(videoCodecHash, false);
      bob.video->setCodecEncodingHardwareAccelerationEnabled(videoCodecHash, false);
      bob.video->setCodecDecodingHardwareAccelerationEnabled(videoCodecHash, false);
   }
   
   codecConfig(alice.video);
   codecConfig(bob.video);

   alice.video->setLocalVideoPreviewResolution(videoRes);
   bob.video->setLocalVideoPreviewResolution(videoRes);

   alice.video->setPreferredResolution(videoCodecHash, videoRes);
   bob.video->setPreferredResolution(videoCodecHash, videoRes);

   bool createAliceInboundVideoWindow = true;
#ifdef ANDROID
   // our Android autotest UI only supports 2 video surfaces at the moment;
   // let alice take the local video surface, and bob take the remote video surface
   createAliceInboundVideoWindow = false;
#endif

   alice.initiateVideo(true, createAliceInboundVideoWindow);
   bob.initiateVideo(true, true);

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, true);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&]() {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::milliseconds(opt.callDurationSec * 1000 / 3));

      std::unique_ptr<TurnAsyncUdpSocket_OutgoingPacketlossInducer> packetlossInducer;
      if (opt.inducePacketloss)
      {
         // induce packetloss 1/3 way through call if requested
         TurnAsyncUdpSocket_OutgoingPacketlossInducer::Config udpLossConfig;
         udpLossConfig.lossRatePct = 2;
         packetlossInducer = std::make_unique<TurnAsyncUdpSocket_OutgoingPacketlossInducer>(udpLossConfig);

         // TODO: validate PLI requests are received and responded to
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(opt.callDurationSec * 1000 / 3) * 2);

      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);

      SipConversationState currState;
      ASSERT_EQ(alice.conversationState->getState(aliceCall, currState), CPCAPI2::kSuccess);
      ASSERT_EQ(1, currState.statistics.videoChannels.size());
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsReceived, 0);
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsSent, 0);
      ASSERT_EQ(std::strcmp(currState.statistics.videoChannels[0].encoder.plName, videoCodecPlType), 0);
      if (!skipWidthCheck)
      {
         ASSERT_EQ(currState.statistics.videoChannels[0].encoder.width, videoResWidth);
      }
      ASSERT_EQ(currState.statistics.videoChannels[0].encoder.height, videoResHeight);
      if (!skipWidthCheck)
      {
         ASSERT_EQ(currState.statistics.videoChannels[0].decoder.width, videoResWidth);
      }
      ASSERT_EQ(currState.statistics.videoChannels[0].decoder.height, videoResHeight);
      
      ASSERT_NE(0, currState.statistics.remoteVideoChannels.size());
      ASSERT_NE(0, currState.statistics.videoChannels[0].streamDataCounters.packetsSent);
      ASSERT_NE(strlen(currState.statistics.videoChannels[0].encoder.plName), 0);
      ASSERT_NE(0, currState.statistics.videoChannels[0].streamDataCounters.packetsReceived);
      ASSERT_NE(strlen(currState.statistics.videoChannels[0].decoder.plName), 0);
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      SipConversationHandle bobCall;
      assertNewConversationIncoming_ex(bob, &bobCall, alice.config.uri(), [](const NewConversationEvent& evt) {
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
      });

      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt) {
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::seconds(opt.callDurationSec));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      
      SipConversationState currState;
      ASSERT_EQ(bob.conversationState->getState(bobCall, currState), CPCAPI2::kSuccess);
      ASSERT_EQ(1, currState.statistics.videoChannels.size());
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsReceived, 0);
      ASSERT_NE(currState.statistics.videoChannels[0].streamDataCounters.packetsSent, 0);
      ASSERT_EQ(std::strcmp(currState.statistics.videoChannels[0].encoder.plName, videoCodecPlType), 0);
      ASSERT_EQ(currState.statistics.videoChannels[0].encoder.width, videoResWidth);
      ASSERT_EQ(currState.statistics.videoChannels[0].encoder.height, videoResHeight);
      if (!skipWidthCheck)
      {
         ASSERT_EQ(currState.statistics.videoChannels[0].decoder.width, videoResWidth);
      }
      ASSERT_EQ(currState.statistics.videoChannels[0].decoder.height, videoResHeight);

      ASSERT_NE(0, currState.statistics.remoteVideoChannels.size());
      ASSERT_NE(0, currState.statistics.videoChannels[0].streamDataCounters.packetsSent);
      ASSERT_NE(strlen(currState.statistics.videoChannels[0].encoder.plName), 0);
      ASSERT_NE(0, currState.statistics.videoChannels[0].streamDataCounters.packetsReceived);
      ASSERT_NE(strlen(currState.statistics.videoChannels[0].decoder.plName), 0);
   });

   waitFor2(aliceEvents, bobEvents);
}

void doVideoCall(TestAccount& alice, TestAccount& bob, const cpc::string& videoCodec, VideoCaptureResolution videoRes, void (*codecConfig)(CPCAPI2::Media::Video*), bool disableHardwareAccel = false)
{
   CallOptions opt;
   opt.disableHardwareAccel = disableHardwareAccel;

   doVideoCall(alice, bob, videoCodec, videoRes, codecConfig, opt);
}

#if (defined(__linux__) && !defined(ANDROID))
#define NO_HIRES_TESTS // trying to reduce load on jenkins linux docker test runs
#endif

#if !(defined(__linux__)) // OBELISK-5897; H264 non-interleaved calls at certain resolutions failing on linux, android
#if !(defined(__APPLE__)) // OBELISK-5897; H264 non-interleaved calls also failing on macOS at low resolution
TEST_F(VideoCallTests, AudioVideoCallH264_NonInterleaved_LowRes)
{
   bool disableHardwareAccel = true;

   TestAccount alice("alice");
   TestAccount bob("bob");
   
   if (alice.usingCustomVideoSource() || bob.usingCustomVideoSource())
   {
      GTEST_SKIP() << "Non interleaved tests disabled if custom video capturer in use until OBELISK-5897 is resolved";
   }
   
   doVideoCall(alice, bob, "H.264", VideoCaptureResolution_Low,
              [](CPCAPI2::Media::Video* video)
              {
                 H264Config h264Config;
                 h264Config.enableNonInterleavedMode = true;
                 h264Config.preferNonInterleavedMode = true;
                 video->setCodecConfig(h264Config);
              }, disableHardwareAccel);
}
#endif // #if !(defined(__APPLE__)) // OBELISK-5897; H264 non-interleaved calls also failing on macOS at low resolution
#endif // #if !(defined(__linux__)) // OBELISK-5897; H264 non-interleaved calls at certain resolutions failing on linux, android

#if !(defined(__linux__)) // OBELISK-5897; H264 non-interleaved calls at certain resolutions failing on linux, android
#if !(defined(__APPLE__)) // OBELISK-5897; H264 non-interleaved calls at certain resolutions on macOS at standard resolution
TEST_F(VideoCallTests, AudioVideoCallH264_NonInterleaved_StandardRes)
{
   bool disableHardwareAccel = true;

   TestAccount alice("alice");
   TestAccount bob("bob");
   
   if (alice.usingCustomVideoSource() || bob.usingCustomVideoSource())
   {
      GTEST_SKIP() << "Non interleaved tests disabled if custom video capturer in use until OBELISK-5897 is resolved";
   }
   
   doVideoCall(alice, bob, "H.264", VideoCaptureResolution_Standard,
              [](CPCAPI2::Media::Video* video)
              {
                 H264Config h264Config;
                 h264Config.enableNonInterleavedMode = true;
                 h264Config.preferNonInterleavedMode = true;
                 video->setCodecConfig(h264Config);
              }, disableHardwareAccel);
}
#endif // #if !(defined(__APPLE__)) // OBELISK-5897; H264 non-interleaved calls at certain resolutions on macOS at standard resolution
#endif // #if !(defined(__linux__)) // OBELISK-5897; H264 non-interleaved calls at certain resolutions failing on linux, android

#if !(defined(__linux__)) // OBELISK-5897; H264 non-interleaved calls at certain resolutions failing on linux, android, macOS
#if !(defined(__APPLE__))
TEST_F(VideoCallTests, AudioVideoCallH264_NonInterleaved_HighRes)
{
   bool disableHardwareAccel = true;

   TestAccount alice("alice");
   TestAccount bob("bob");
   
   if (alice.usingCustomVideoSource() || bob.usingCustomVideoSource())
   {
      GTEST_SKIP() << "Non interleaved tests disabled if custom video capturer in use until OBELISK-5897 is resolved";
   }
   
   doVideoCall(alice, bob, "H.264", VideoCaptureResolution_High,
              [](CPCAPI2::Media::Video* video)
              {
                 H264Config h264Config;
                 h264Config.enableNonInterleavedMode = true;
                 h264Config.preferNonInterleavedMode = true;
                 video->setCodecConfig(h264Config);
              }, disableHardwareAccel);
}
#endif // #if !(defined(__APPLE__))
#endif // #if !(defined(__linux__)) // OBELISK-5897; H264 non-interleaved calls at certain resolutions failing on linux, android

#if !(defined(__linux__)) // OBELISK-5897; H264 non-interleaved calls at certain resolutions failing on linux, android
TEST_F(VideoCallTests, AudioVideoCallH264_NonInterleaved_480pRes)
{
   bool disableHardwareAccel = true;

   TestAccount alice("alice");
   TestAccount bob("bob");
   
   if (alice.usingCustomVideoSource() || bob.usingCustomVideoSource())
   {
      GTEST_SKIP() << "Non interleaved tests disabled if custom video capturer in use until OBELISK-5897 is resolved";
   }
   
   doVideoCall(alice, bob, "H.264", VideoCaptureResolution_848x480p,
              [](CPCAPI2::Media::Video* video)
              {
                 H264Config h264Config;
                 h264Config.enableNonInterleavedMode = true;
                 h264Config.preferNonInterleavedMode = true;
                 video->setCodecConfig(h264Config);
              }, disableHardwareAccel);
}
#endif // #if !(defined(__linux__)) // OBELISK-5897; H264 non-interleaved calls at certain resolutions failing on linux, android

#ifndef NO_HIRES_TESTS
TEST_F(VideoCallTests, AudioVideoCallH264_NonInterleaved_720pRes)
{
   bool disableHardwareAccel = true;

   TestAccount alice("alice");
   TestAccount bob("bob");
   
   if (alice.usingCustomVideoSource() || bob.usingCustomVideoSource())
   {
      GTEST_SKIP() << "Non interleaved tests disabled if custom video capturer in use until OBELISK-5897 is resolved";
   }
   
   doVideoCall(alice, bob, "H.264", VideoCaptureResolution_HD_1280x720p,
              [](CPCAPI2::Media::Video* video)
              {
                 H264Config h264Config;
                 h264Config.enableNonInterleavedMode = true;
                 h264Config.preferNonInterleavedMode = true;
                 video->setCodecConfig(h264Config);
              }, disableHardwareAccel);
}
#endif // #ifndef NO_HIRES_TESTS

#ifndef NO_HIRES_TESTS
TEST_F(VideoCallTests, AudioVideoCallH264_NonInterleaved_1080pRes)
{
   bool disableHardwareAccel = true;

   TestAccount alice("alice");
   TestAccount bob("bob");
   
   if (alice.usingCustomVideoSource() || bob.usingCustomVideoSource())
   {
      GTEST_SKIP() << "Non interleaved tests disabled if custom video capturer in use until OBELISK-5897 is resolved";
   }
   
   doVideoCall(alice, bob, "H.264", VideoCaptureResolution_HD_1920x1080p,
              [](CPCAPI2::Media::Video* video)
              {
                 H264Config h264Config;
                 h264Config.enableNonInterleavedMode = true;
                 h264Config.preferNonInterleavedMode = true;
                 video->setCodecConfig(h264Config);
              }, disableHardwareAccel);
}
#endif // #ifndef NO_HIRES_TESTS


#ifndef NO_HIRES_TESTS
TEST_F(VideoCallTests, AudioVideoCallH264_NonInterleaved_MaxSupportedRes)
{
   bool disableHardwareAccel = true;

   TestAccount alice("alice");
   TestAccount bob("bob");
   
   if (alice.usingCustomVideoSource() || bob.usingCustomVideoSource())
   {
      GTEST_SKIP() << "Non interleaved tests disabled if custom video capturer in use until OBELISK-5897 is resolved";
   }
   
   doVideoCall(alice, bob, "H.264", VideoCaptureResolution_MaxSupported,
              [](CPCAPI2::Media::Video* video)
              {
                 H264Config h264Config;
                 h264Config.enableNonInterleavedMode = true;
                 h264Config.preferNonInterleavedMode = true;
                 video->setCodecConfig(h264Config);
              }, disableHardwareAccel);
}
#endif // #ifndef NO_HIRES_TESTS

TEST_F(VideoCallTests, AudioVideoCallH264_SingleNalUnit_LowRes)
{
   bool disableHardwareAccel = true;

   TestAccount alice("alice");
   TestAccount bob("bob");
   
   doVideoCall(alice, bob, "H.264", VideoCaptureResolution_Low,
              [](CPCAPI2::Media::Video* video)
              {
                 H264Config h264Config;
                 h264Config.enableNonInterleavedMode = false;
                 h264Config.preferNonInterleavedMode = false;
                 video->setCodecConfig(h264Config);
              }, disableHardwareAccel);
}

TEST_F(VideoCallTests, AudioVideoCallH264_SingleNalUnit_StandardRes)
{
   bool disableHardwareAccel = true;

   TestAccount alice("alice");
   TestAccount bob("bob");
   
   doVideoCall(alice, bob, "H.264", VideoCaptureResolution_Standard,
              [](CPCAPI2::Media::Video* video)
              {
                 H264Config h264Config;
                 h264Config.enableNonInterleavedMode = false;
                 h264Config.preferNonInterleavedMode = false;
                 video->setCodecConfig(h264Config);
              }, disableHardwareAccel);
}

TEST_F(VideoCallTests, AudioVideoCallH264_SingleNalUnit_HighRes)
{
   bool disableHardwareAccel = true;

   TestAccount alice("alice");
   TestAccount bob("bob");
   
   doVideoCall(alice, bob, "H.264", VideoCaptureResolution_High,
              [](CPCAPI2::Media::Video* video)
              {
                 H264Config h264Config;
                 h264Config.enableNonInterleavedMode = false;
                 h264Config.preferNonInterleavedMode = false;
                 video->setCodecConfig(h264Config);
              }, disableHardwareAccel);
}

TEST_F(VideoCallTests, AudioVideoCallH264_SingleNalUnit_480pRes)
{
   bool disableHardwareAccel = true;

   TestAccount alice("alice");
   TestAccount bob("bob");
   
   doVideoCall(alice, bob, "H.264", VideoCaptureResolution_848x480p,
              [](CPCAPI2::Media::Video* video)
              {
                 H264Config h264Config;
                 h264Config.enableNonInterleavedMode = false;
                 h264Config.preferNonInterleavedMode = false;
                 video->setCodecConfig(h264Config);
              }, disableHardwareAccel);
}

#ifndef NO_HIRES_TESTS
TEST_F(VideoCallTests, AudioVideoCallH264_SingleNalUnit_720pRes)
{
   bool disableHardwareAccel = true;

   TestAccount alice("alice");
   TestAccount bob("bob");
   
   doVideoCall(alice, bob, "H.264", VideoCaptureResolution_HD_1280x720p,
              [](CPCAPI2::Media::Video* video)
              {
                 H264Config h264Config;
                 h264Config.enableNonInterleavedMode = false;
                 h264Config.preferNonInterleavedMode = false;
                 video->setCodecConfig(h264Config);
              }, disableHardwareAccel);
}
#endif // #ifndef NO_HIRES_TESTS

#ifndef NO_HIRES_TESTS
TEST_F(VideoCallTests, AudioVideoCallH264_SingleNalUnit_1080pRes)
{
   bool disableHardwareAccel = true;

   TestAccount alice("alice");
   TestAccount bob("bob");
   
   doVideoCall(alice, bob, "H.264", VideoCaptureResolution_HD_1920x1080p,
              [](CPCAPI2::Media::Video* video)
              {
                 H264Config h264Config;
                 h264Config.enableNonInterleavedMode = false;
                 h264Config.preferNonInterleavedMode = false;
                 video->setCodecConfig(h264Config);
              }, disableHardwareAccel);
}
#endif // #ifndef NO_HIRES_TESTS

#ifndef NO_HIRES_TESTS
TEST_F(VideoCallTests, AudioVideoCallH264_SingleNalUnit_MaxSupportedRes)
{
   bool disableHardwareAccel = true;

   TestAccount alice("alice");
   TestAccount bob("bob");
   
   doVideoCall(alice, bob, "H.264", VideoCaptureResolution_MaxSupported,
              [](CPCAPI2::Media::Video* video)
              {
                 H264Config h264Config;
                 h264Config.enableNonInterleavedMode = false;
                 h264Config.preferNonInterleavedMode = false;
                 video->setCodecConfig(h264Config);
              }, disableHardwareAccel);
}
#endif // #ifndef NO_HIRES_TESTS

TEST_F(VideoCallTests, AudioVideoCallH264_SingleNalUnit_Lossy)
{
   TestAccount alice("alice");
   TestAccount bob("bob");

   CallOptions callOptions;
   callOptions.disableHardwareAccel = true;
   callOptions.inducePacketloss = true;
   callOptions.callDurationSec = 10;
   
   // todo: some way of validating PLI requests are received and responded to
   doVideoCall(alice, bob, "H.264", VideoCaptureResolution_848x480p,
              [](CPCAPI2::Media::Video* video)
              {
                 H264Config h264Config;
                 h264Config.enableNonInterleavedMode = false;
                 h264Config.preferNonInterleavedMode = false;
                 video->setCodecConfig(h264Config);
              }, callOptions);
}

#if !(defined(__linux__) && !defined(ANDROID)) // OBELISK-5082 - VP8 tests intermittently failing on linux
TEST_F(VideoCallTests, AudioVideoCallVP8_LowRes)
{
   bool disableHardwareAccel = true;

   TestAccount alice("alice");
   TestAccount bob("bob");
   
   doVideoCall(alice, bob, "VP8", VideoCaptureResolution_Low,
              [](CPCAPI2::Media::Video* video)
              {}, disableHardwareAccel);
}

TEST_F(VideoCallTests, AudioVideoCallVP8_StandardRes)
{
   bool disableHardwareAccel = true;

   TestAccount alice("alice");
   TestAccount bob("bob");
   
   doVideoCall(alice, bob, "VP8", VideoCaptureResolution_Standard,
              [](CPCAPI2::Media::Video* video)
              {}, disableHardwareAccel);
}

TEST_F(VideoCallTests, AudioVideoCallVP8_HighRes)
{
   bool disableHardwareAccel = true;

   TestAccount alice("alice");
   TestAccount bob("bob");
   
   doVideoCall(alice, bob, "VP8", VideoCaptureResolution_High,
              [](CPCAPI2::Media::Video* video)
              {}, disableHardwareAccel);
}

TEST_F(VideoCallTests, AudioVideoCallVP8_480pRes)
{
   bool disableHardwareAccel = true;

   TestAccount alice("alice");
   TestAccount bob("bob");
   
   doVideoCall(alice, bob, "VP8", VideoCaptureResolution_848x480p,
              [](CPCAPI2::Media::Video* video)
              {}, disableHardwareAccel);
}

#ifndef NO_HIRES_TESTS
TEST_F(VideoCallTests, AudioVideoCallVP8_720pRes)
{
   bool disableHardwareAccel = true;

   TestAccount alice("alice");
   TestAccount bob("bob");
   
   doVideoCall(alice, bob, "VP8", VideoCaptureResolution_HD_1280x720p,
              [](CPCAPI2::Media::Video* video)
              {}, disableHardwareAccel);
}
#endif // #ifndef NO_HIRES_TESTS

#ifndef NO_HIRES_TESTS
TEST_F(VideoCallTests, AudioVideoCallVP8_1080pRes)
{
   TestAccount alice("alice");
   TestAccount bob("bob");
   
   CallOptions opt;
   // .jza. test extending call from 5 to 10 seconds to see if it helps jenkins failures
   opt.callDurationSec = 10;
   opt.disableHardwareAccel = true;

   doVideoCall(alice, bob, "VP8", VideoCaptureResolution_HD_1920x1080p,
              [](CPCAPI2::Media::Video* video)
              {}, opt);
}
#endif // #ifndef NO_HIRES_TESTS

#ifndef NO_HIRES_TESTS
TEST_F(VideoCallTests, AudioVideoCallVP8_MaxSupportedRes)
{
   TestAccount alice("alice");
   TestAccount bob("bob");

   CallOptions opt;
   // .jza. test extending call from 5 to 10 seconds to see if it helps jenkins failures
   opt.callDurationSec = 10;
   opt.disableHardwareAccel = true;

   doVideoCall(alice, bob, "VP8", VideoCaptureResolution_MaxSupported,
              [](CPCAPI2::Media::Video* video)
              {}, opt);
}
#endif // #ifndef NO_HIRES_TESTS

#endif // #if !(defined(__linux__) && !defined(ANDROID)) // OBELISK-5082 - VP8 tests intermittently failing on linux


// hardware accel disabled tests; since we support hardware accelerated video
// codecs on Android at the moment, it doesn't make sense to run these on other platforms
#ifdef ANDROID
TEST_F(VideoCallTests, AudioVideoCallVP8_LowRes_HardwareAccel)
{
   bool disableHardwareAccel = false;
   
   TestAccount alice("alice");
   TestAccount bob("bob");
   
   doVideoCall(alice, bob, "VP8", VideoCaptureResolution_Low,
              [](CPCAPI2::Media::Video* video)
              {}, disableHardwareAccel);
}

TEST_F(VideoCallTests, AudioVideoCallVP8_StandardRes_HardwareAccel)
{
   bool disableHardwareAccel = false;

   TestAccount alice("alice");
   TestAccount bob("bob");
   
   doVideoCall(alice, bob, "VP8", VideoCaptureResolution_Standard,
              [](CPCAPI2::Media::Video* video)
              {}, disableHardwareAccel);
}

TEST_F(VideoCallTests, AudioVideoCallVP8_HighRes_HardwareAccel)
{
   bool disableHardwareAccel = false;

   TestAccount alice("alice");
   TestAccount bob("bob");
   
   doVideoCall(alice, bob, "VP8", VideoCaptureResolution_High,
              [](CPCAPI2::Media::Video* video)
              {}, disableHardwareAccel);
}

TEST_F(VideoCallTests, AudioVideoCallVP8_480pRes_HardwareAccel)
{
   bool disableHardwareAccel = false;

   TestAccount alice("alice");
   TestAccount bob("bob");
   
   doVideoCall(alice, bob, "VP8", VideoCaptureResolution_848x480p,
              [](CPCAPI2::Media::Video* video)
              {}, disableHardwareAccel);
}

TEST_F(VideoCallTests, AudioVideoCallVP8_720pRes_HardwareAccel)
{
   bool disableHardwareAccel = false;

   TestAccount alice("alice");
   TestAccount bob("bob");
   
   doVideoCall(alice, bob, "VP8", VideoCaptureResolution_HD_1280x720p,
              [](CPCAPI2::Media::Video* video)
              {}, disableHardwareAccel);
}

TEST_F(VideoCallTests, AudioVideoCallVP8_1080pRes_HardwareAccel)
{
   bool disableHardwareAccel = false;

   TestAccount alice("alice");
   TestAccount bob("bob");
   
   doVideoCall(alice, bob, "VP8", VideoCaptureResolution_HD_1920x1080p,
              [](CPCAPI2::Media::Video* video)
              {}, disableHardwareAccel);
}

TEST_F(VideoCallTests, AudioVideoCallVP8_MaxSupportedRes_HardwareAccel)
{
   bool disableHardwareAccel = false;

   TestAccount alice("alice");
   TestAccount bob("bob");
   
   doVideoCall(alice, bob, "VP8", VideoCaptureResolution_MaxSupported,
              [](CPCAPI2::Media::Video* video)
              {}, disableHardwareAccel);
}

#if 0 // OBELISK-5917: hardware accelerated non interleaved currently broken on Android
TEST_F(VideoCallTests, AudioVideoCallH264_NonInterleaved_LowRes_HardwareAccel)
{
   bool disableHardwareAccel = false;

   TestAccount alice("alice");
   TestAccount bob("bob");
   
   doVideoCall(alice, bob, "H.264", VideoCaptureResolution_Low,
              [](CPCAPI2::Media::Video* video)
              {
                 H264Config h264Config;
                 h264Config.enableNonInterleavedMode = true;
                 h264Config.preferNonInterleavedMode = true;
                 video->setCodecConfig(h264Config);
              }, disableHardwareAccel);
}

TEST_F(VideoCallTests, AudioVideoCallH264_NonInterleaved_StandardRes_HardwareAccel)
{
   bool disableHardwareAccel = false;

   TestAccount alice("alice");
   TestAccount bob("bob");
   
   doVideoCall(alice, bob, "H.264", VideoCaptureResolution_Standard,
              [](CPCAPI2::Media::Video* video)
              {
                 H264Config h264Config;
                 h264Config.enableNonInterleavedMode = true;
                 h264Config.preferNonInterleavedMode = true;
                 video->setCodecConfig(h264Config);
              }, disableHardwareAccel);
}

TEST_F(VideoCallTests, AudioVideoCallH264_NonInterleaved_HighRes_HardwareAccel)
{
   bool disableHardwareAccel = false;

   TestAccount alice("alice");
   TestAccount bob("bob");
   
   doVideoCall(alice, bob, "H.264", VideoCaptureResolution_High,
              [](CPCAPI2::Media::Video* video)
              {
                 H264Config h264Config;
                 h264Config.enableNonInterleavedMode = true;
                 h264Config.preferNonInterleavedMode = true;
                 video->setCodecConfig(h264Config);
              }, disableHardwareAccel);
}

TEST_F(VideoCallTests, AudioVideoCallH264_NonInterleaved_480pRes_HardwareAccel)
{
   bool disableHardwareAccel = false;

   TestAccount alice("alice");
   TestAccount bob("bob");
   
   doVideoCall(alice, bob, "H.264", VideoCaptureResolution_848x480p,
              [](CPCAPI2::Media::Video* video)
              {
                 H264Config h264Config;
                 h264Config.enableNonInterleavedMode = true;
                 h264Config.preferNonInterleavedMode = true;
                 video->setCodecConfig(h264Config);
              }, disableHardwareAccel);
}

TEST_F(VideoCallTests, AudioVideoCallH264_NonInterleaved_720pRes_HardwareAccel)
{
   bool disableHardwareAccel = false;

   TestAccount alice("alice");
   TestAccount bob("bob");
   
   doVideoCall(alice, bob, "H.264", VideoCaptureResolution_HD_1280x720p,
              [](CPCAPI2::Media::Video* video)
              {
                 H264Config h264Config;
                 h264Config.enableNonInterleavedMode = true;
                 h264Config.preferNonInterleavedMode = true;
                 video->setCodecConfig(h264Config);
              }, disableHardwareAccel);
}

TEST_F(VideoCallTests, AudioVideoCallH264_NonInterleaved_1080pRes_HardwareAccel)
{
   bool disableHardwareAccel = false;

   TestAccount alice("alice");
   TestAccount bob("bob");
   
   doVideoCall(alice, bob, "H.264", VideoCaptureResolution_HD_1920x1080p,
              [](CPCAPI2::Media::Video* video)
              {
                 H264Config h264Config;
                 h264Config.enableNonInterleavedMode = true;
                 h264Config.preferNonInterleavedMode = true;
                 video->setCodecConfig(h264Config);
              }, disableHardwareAccel);
}

TEST_F(VideoCallTests, AudioVideoCallH264_NonInterleaved_MaxSupportedRes_HardwareAccel)
{
   bool disableHardwareAccel = false;

   TestAccount alice("alice");
   TestAccount bob("bob");
   
   doVideoCall(alice, bob, "H.264", VideoCaptureResolution_MaxSupported,
              [](CPCAPI2::Media::Video* video)
              {
                 H264Config h264Config;
                 h264Config.enableNonInterleavedMode = true;
                 h264Config.preferNonInterleavedMode = true;
                 video->setCodecConfig(h264Config);
              }, disableHardwareAccel);
}
#endif // #if 0 // hardware accelerated non interleaved currently broken on Android

TEST_F(VideoCallTests, AudioVideoCallH264_SingleNalUnit_LowRes_HardwareAccel)
{
   bool disableHardwareAccel = false;

   TestAccount alice("alice");
   TestAccount bob("bob");
   
   doVideoCall(alice, bob, "H.264", VideoCaptureResolution_Low,
              [](CPCAPI2::Media::Video* video)
              {
                 H264Config h264Config;
                 h264Config.enableNonInterleavedMode = false;
                 h264Config.preferNonInterleavedMode = false;
                 video->setCodecConfig(h264Config);
              }, disableHardwareAccel);
}

TEST_F(VideoCallTests, AudioVideoCallH264_SingleNalUnit_StandardRes_HardwareAccel)
{
   bool disableHardwareAccel = false;

   TestAccount alice("alice");
   TestAccount bob("bob");
   
   doVideoCall(alice, bob, "H.264", VideoCaptureResolution_Standard,
              [](CPCAPI2::Media::Video* video)
              {
                 H264Config h264Config;
                 h264Config.enableNonInterleavedMode = false;
                 h264Config.preferNonInterleavedMode = false;
                 video->setCodecConfig(h264Config);
              }, disableHardwareAccel);
}

TEST_F(VideoCallTests, AudioVideoCallH264_SingleNalUnit_HighRes_HardwareAccel)
{
   bool disableHardwareAccel = false;

   TestAccount alice("alice");
   TestAccount bob("bob");
   
   doVideoCall(alice, bob, "H.264", VideoCaptureResolution_High,
              [](CPCAPI2::Media::Video* video)
              {
                 H264Config h264Config;
                 h264Config.enableNonInterleavedMode = false;
                 h264Config.preferNonInterleavedMode = false;
                 video->setCodecConfig(h264Config);
              }, disableHardwareAccel);
}

TEST_F(VideoCallTests, AudioVideoCallH264_SingleNalUnit_480pRes_HardwareAccel)
{
   bool disableHardwareAccel = false;

   TestAccount alice("alice");
   TestAccount bob("bob");
   
   doVideoCall(alice, bob, "H.264", VideoCaptureResolution_848x480p,
              [](CPCAPI2::Media::Video* video)
              {
                 H264Config h264Config;
                 h264Config.enableNonInterleavedMode = false;
                 h264Config.preferNonInterleavedMode = false;
                 video->setCodecConfig(h264Config);
              }, disableHardwareAccel);
}

TEST_F(VideoCallTests, AudioVideoCallH264_SingleNalUnit_720pRes_HardwareAccel)
{
   bool disableHardwareAccel = false;

   TestAccount alice("alice");
   TestAccount bob("bob");
   
   doVideoCall(alice, bob, "H.264", VideoCaptureResolution_HD_1280x720p,
              [](CPCAPI2::Media::Video* video)
              {
                 H264Config h264Config;
                 h264Config.enableNonInterleavedMode = false;
                 h264Config.preferNonInterleavedMode = false;
                 video->setCodecConfig(h264Config);
              }, disableHardwareAccel);
}

TEST_F(VideoCallTests, AudioVideoCallH264_SingleNalUnit_1080pRes_HardwareAccel)
{
   bool disableHardwareAccel = false;

   TestAccount alice("alice");
   TestAccount bob("bob");
   
   doVideoCall(alice, bob, "H.264", VideoCaptureResolution_HD_1920x1080p,
              [](CPCAPI2::Media::Video* video)
              {
                 H264Config h264Config;
                 h264Config.enableNonInterleavedMode = false;
                 h264Config.preferNonInterleavedMode = false;
                 video->setCodecConfig(h264Config);
              }, disableHardwareAccel);
}


TEST_F(VideoCallTests, AudioVideoCallH264_SingleNalUnit_MaxSupportedRes_HardwareAccel)
{
   bool disableHardwareAccel = false;

   TestAccount alice("alice");
   TestAccount bob("bob");
   
   doVideoCall(alice, bob, "H.264", VideoCaptureResolution_MaxSupported,
              [](CPCAPI2::Media::Video* video)
              {
                 H264Config h264Config;
                 h264Config.enableNonInterleavedMode = false;
                 h264Config.preferNonInterleavedMode = false;
                 video->setCodecConfig(h264Config);
              }, disableHardwareAccel);
}

#endif // #ifdef ANDROID

TEST_F(VideoCallTests, AudioVideoCall_RtpKeepAlive) {

   TestAccount alice("alice");
   TestAccount bob("bob");

   alice.initiateVideo();
   bob.initiateVideo();
   
   // per desired behaviour from OBELISK-3994, if alice is muted, and not sending any video -- we still
   // want 12 RTP keep-alive packets to go out, for FreeSwitch latching
   alice.video->setVideoMute(true);
   
   const int callDurationSec = 10;

   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Audio, true);
   alice.conversation->setMediaEnabled(aliceCall, MediaType_Video, true);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationMediaChanged_ex(alice, aliceCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::seconds(callDurationSec));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      
      SipConversationState currState;
      ASSERT_EQ(alice.conversationState->getState(aliceCall, currState), kSuccess);
      ASSERT_GE(currState.statistics.videoChannels[0].streamDataCounters.packetsSent, 12);
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      // let the cameras get going
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      SipConversationHandle bobCall;
      assertNewConversationIncoming_ex(bob, &bobCall, alice.config.uri(), [](const NewConversationEvent& evt){
         ASSERT_EQ(2, evt.remoteMediaInfo.size());
      });

      bob.conversation->setMediaEnabled(bobCall, MediaType_Video, true);
      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged_ex(bob, bobCall, [](const ConversationMediaChangedEvent& evt){
         ASSERT_EQ(2, evt.localMediaInfo.size());
         ASSERT_EQ(MediaType_Audio, evt.localMediaInfo[0].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[0].mediaDirection);
         ASSERT_EQ(MediaType_Video, evt.localMediaInfo[1].mediaType);
         ASSERT_EQ(MediaDirection_SendReceive, evt.localMediaInfo[1].mediaDirection);
      });
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);

      std::this_thread::sleep_for(std::chrono::seconds(callDurationSec));
      assertSuccess(bob.conversation->end(bobCall));
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedLocally);
      
      // TODO: this doesn't seem to include RTP keep-alive packets; confirm once we've fixed OBELISK-6088
      ///SipConversationState currState;
      //ASSERT_EQ(bob.conversationState->getState(bobCall, currState), kSuccess);
      //ASSERT_GE(currState.statistics.videoChannels[0].streamDataCounters.packetsReceived, 12);
   });

   
   waitFor2Ms(aliceEvents, bobEvents, std::chrono::seconds(callDurationSec + 10));
}

