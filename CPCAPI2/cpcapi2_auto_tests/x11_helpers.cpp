#include "x11_helpers.h"
#include <X11/Xlib.h>
#include <X11/Xutil.h>

namespace CPCAPI2
{
namespace test
{
TestX11App::TestX11App()
{
}

TestX11App::~TestX11App()
{
}

WindowAndDisplayHolder TestX11App::createWindow(int x_pos, int y_pos, unsigned int width,
                                                unsigned int height, const char* title)
{
   WindowAndDisplayHolder holder;
   holder.window = nullptr;
   holder.display = nullptr;
   Display* display = XOpenDisplay(nullptr);
   if (display == nullptr)
   {
      return holder;
   }

   int screen = DefaultScreen(display);

   // Try to establish a 24-bit TrueColor display
   // (our environment must allow this).
   XVisualInfo visual_info;
   if (XMatchVisualInfo(display, screen, 24, TrueColor, &visual_info) == 0)
   {
      return holder;
   }

   // Create suitable window attributes.
   XSetWindowAttributes window_attributes;
   window_attributes.colormap = XCreateColormap(
            display, DefaultRootWindow(display), visual_info.visual, AllocNone);
   window_attributes.event_mask = StructureNotifyMask | ExposureMask;
   window_attributes.background_pixel = 0;
   window_attributes.border_pixel = 0;

   unsigned long attribute_mask = CWBackPixel | CWBorderPixel | CWColormap |
                                  CWEventMask;

   VideoWindow _window = XCreateWindow(display, DefaultRootWindow(display), x_pos,
                                       y_pos, width, height, 0, visual_info.depth,
                                       InputOutput, visual_info.visual,
                                       attribute_mask, &window_attributes);

   // SDK doesn't handle window resize so do not allow resizing.
   XSizeHints *size_hints;
   size_hints = XAllocSizeHints();
   if(size_hints == nullptr)
   {
	   return holder;
   }
   size_hints->flags=USPosition | PMinSize | PMaxSize;
   size_hints->min_width = static_cast<int>(width);
   size_hints->min_height = static_cast<int>(height);
   size_hints->max_width = static_cast<int>(width);
   size_hints->max_height = static_cast<int>(height);
   XSetWMNormalHints(display, _window, size_hints);

   // Set window name.
   XStoreName(display, _window, title);
   XSetIconName(display, _window, title);

   // Make x report events for mask.
   XSelectInput(display, _window, StructureNotifyMask);

   // Map the window to the display.
   XMapWindow(display, _window);

   // Wait for map event.
   XEvent event;
   do {
      XNextEvent(display, &event);
   } while (event.type != MapNotify || event.xmap.event != _window);

   holder.window = reinterpret_cast<void*>(_window);
   holder.display = display;
   return holder;
}

void TestX11App::destroyWindow(void* display, void* window)
{
   if(display == nullptr || window == nullptr)
   {
      return;
   }
   VideoWindow vw = reinterpret_cast<VideoWindow>(window);
   Display* xDisplay = (Display*) display;
   XUnmapWindow(xDisplay, vw);
   XDestroyWindow(xDisplay, vw);
   XSync(xDisplay, false);
   XCloseDisplay(xDisplay);
}

}
}
