#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#include "tscf_test_fixture.h"
#include "test_account_events.h"
#include "test_call_events.h"
#include "test_events.h"

#include <vector>

using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::SipConversation;
using namespace CPCAPI2::SipEvent;
using namespace CPCAPI2::Media;

namespace {

class TscfModuleTest : public CpcapiAutoTest
{
public:
   TscfModuleTest() {}
   virtual ~TscfModuleTest() {}
};

// for whatever reason the SIP server includes port 6060 in the from header for INVITES it relays
const cpc::string kTscfServerFromPort = ":6060";


// Note: all tests require CPCAPI2_BRAND_ACME_TSCF_MODULE and a TSCF server


TEST_F(TscfModuleTest, DISABLED_TscfAccountInitOnly) {
   TscfAccount("***********", "JGRxYbFxmMIL", Account_Init);
}

TEST_F(TscfModuleTest, DISABLED_TscfAccountEnableDisable) {
   TscfAccount("***********", "JGRxYbFxmMIL");
}

TEST_F(TscfModuleTest, DISABLED_TscfBasicCallCalleeHangsUp)
{
   TscfAccount bob("***********", "6CoAMTpMdnWP");
   TscfAccount charlie("***********", "96KIQcToE0CR");

   // Bob calls Charlie then Charlie hangs up
   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall, charlie.config.uri());
   bob.conversation->start(bobCall);
   auto bobConversationEvents = std::async(std::launch::async, [&] () {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing(bob, bobCall, charlie.config.uri());
      assertConversationStateChanged(bob, bobCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);   
   });
   auto charlieConversationEvents = std::async(std::launch::async, [&] () {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle charlieCall;
      assertNewConversationIncoming(charlie, &charlieCall, bob.config.uri() + kTscfServerFromPort);
      charlie.conversation->sendRingingResponse(charlieCall);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_LocalRinging);
      charlie.conversation->accept(charlieCall);
      assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_Connected);
      charlie.conversation->end(charlieCall);
      assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedLocally);
   });
   waitFor2(bobConversationEvents, charlieConversationEvents);
}

TEST_F(TscfModuleTest, DISABLED_TscfBasicCallCalleeHangsUp_RedundancyFactor1)
{
   TscfAccount bob("***********", "6CoAMTpMdnWP", Account_NoInit);
   bob.config.settings.tunnelConfig.transportType = TunnelTransport_TLS;
   bob.config.settings.tunnelConfig.ignoreCertVerification = true;
   bob.config.settings.tunnelConfig.redundancyFactor = 1;
   bob.init();
   bob.enable();

   TscfAccount charlie("***********", "96KIQcToE0CR", Account_NoInit);
   charlie.config.settings.tunnelConfig.transportType = TunnelTransport_TLS;
   charlie.config.settings.tunnelConfig.ignoreCertVerification = true;
   charlie.config.settings.tunnelConfig.redundancyFactor = 1;
   charlie.init();
   charlie.enable();

   // Bob calls Charlie then Charlie hangs up
   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall, charlie.config.uri());
   bob.conversation->start(bobCall);
   auto bobConversationEvents = std::async(std::launch::async, [&]() {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing(bob, bobCall, charlie.config.uri());
      assertConversationStateChanged(bob, bobCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });
   auto charlieConversationEvents = std::async(std::launch::async, [&]() {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle charlieCall;
      assertNewConversationIncoming(charlie, &charlieCall, bob.config.uri() + kTscfServerFromPort);
      charlie.conversation->sendRingingResponse(charlieCall);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_LocalRinging);
      charlie.conversation->accept(charlieCall);
      assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_Connected);
      charlie.conversation->end(charlieCall);
      assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedLocally);
   });
   waitFor2(bobConversationEvents, charlieConversationEvents);
}

TEST_F(TscfModuleTest, DISABLED_TscfBasicCallCalleeHangsUp_DTLS)
{
   TscfAccount bob("***********", "6CoAMTpMdnWP", Account_NoInit);
   bob.config.settings.tunnelConfig.transportType = TunnelTransport_DTLS;
   bob.config.settings.tunnelConfig.ignoreCertVerification = true;
   bob.init();
   bob.enable();

   TscfAccount charlie("***********", "96KIQcToE0CR", Account_NoInit);
   charlie.config.settings.tunnelConfig.transportType = TunnelTransport_DTLS;
   charlie.config.settings.tunnelConfig.ignoreCertVerification = true;
   charlie.init();
   charlie.enable();

   // Bob calls Charlie then Charlie hangs up
   SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
   bob.conversation->addParticipant(bobCall, charlie.config.uri());
   bob.conversation->start(bobCall);
   auto bobConversationEvents = std::async(std::launch::async, [&]() {
      // Wait for the expected state changes and for the call to end
      assertNewConversationOutgoing(bob, bobCall, charlie.config.uri());
      assertConversationStateChanged(bob, bobCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);
   });
   auto charlieConversationEvents = std::async(std::launch::async, [&]() {
      // Wait for the expected state changes and for the call to end
      SipConversationHandle charlieCall;
      assertNewConversationIncoming(charlie, &charlieCall, bob.config.uri() + kTscfServerFromPort);
      charlie.conversation->sendRingingResponse(charlieCall);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_LocalRinging);
      charlie.conversation->accept(charlieCall);
      assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);
      assertConversationStateChanged(charlie, charlieCall, ConversationState_Connected);
      charlie.conversation->end(charlieCall);
      assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedLocally);
   });
   waitFor2(bobConversationEvents, charlieConversationEvents);
}

TEST_F(TscfModuleTest, DISABLED_TscfBasicCallCallerEnds_BuildSanity) {
   TscfAccount alice("***********", "JGRxYbFxmMIL");
   TscfAccount bob("***********", "6CoAMTpMdnWP");

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());
   alice.conversation->start(aliceCall);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(alice, aliceCall, bob.config.uri());
      assertConversationStateChanged(alice, aliceCall, ConversationState_RemoteRinging);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      assertAudioFlowing(alice, aliceCall);
      std::this_thread::sleep_for(std::chrono::milliseconds(500));
      assertSuccess(alice.conversation->end(aliceCall));
      assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedLocally);
      assertCallHadAudio(alice, aliceCall);
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall;
      
      // currently doesn't work; URI assertion fails
      //assertNewConversationIncoming(bob, &bobCall, alice.config.uri());
      
      NewConversationEvent evt;
      ASSERT_TRUE(bob.conversationEvents->expectEvent(__LINE__, "SipConversationHandler::onNewConversation",
                  30000, AlwaysTruePred(), bobCall, evt));
      ASSERT_EQ(ConversationType_Incoming, evt.conversationType);
      
      assertSuccess(bob.conversation->sendRingingResponse(bobCall));
      assertConversationStateChanged(bob, bobCall, ConversationState_LocalRinging);
      assertSuccess(bob.conversation->accept(bobCall));
      assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
      assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      assertAudioFlowing(bob, bobCall);

      {
         SipConversationHandle h;
         ConversationEndedEvent evt;
         ASSERT_TRUE(bob.conversationEvents->expectEvent("SipConversationHandler::onConversationEnded",
            35000, HandleEqualsPred<SipConversationHandle>(bobCall), h, evt));
         ASSERT_EQ(ConversationEndReason_UserTerminatedRemotely, evt.endReason);
      }
      assertCallHadAudio(bob, bobCall);
   });

   waitFor2(aliceEvents, bobEvents);
}

TEST_F(TscfModuleTest, DISABLED_TscfBasicCallCallerEnds_InnovativeSolutionsServer) {
   TscfAccount alice("9001", "Sohail1144", Account_NoInit);
   alice.config.settings.domain = "*******:6060";
   alice.config.settings.outboundProxy = "";
   alice.config.settings.sipTransportType = SipAccountTransport_UDP;
   alice.config.settings.ipVersion = IpVersion_V4;
   alice.config.settings.sslVersion = SSL_HIGHEST;
   alice.config.settings.useRport = false;
   alice.config.settings.tunnelConfig.useTunnel = true;
   alice.config.settings.tunnelConfig.tunnelType = TunnelType_TSCF;
   alice.config.settings.tunnelConfig.server = "t1.thephoenixlive.com";
   alice.config.settings.tunnelConfig.transportType = TunnelTransport_DTLS;
   alice.config.settings.tunnelConfig.mediaTransportType = TunnelMediaTransport_Default;
   alice.config.settings.tunnelConfig.redundancyFactor = 2;
   alice.config.settings.tunnelConfig.doLoadBalancing = false;
   alice.config.settings.tunnelConfig.ignoreCertVerification = true;
   alice.config.settings.tunnelConfig.disableNagleAlgorithm = true;
   
   alice.init();
   alice.enable(false);
   
   std::this_thread::sleep_for(std::chrono::milliseconds(5000));
   
   //assertAccountRegistering(alice);
   //assertAccountRegistered(alice);
   
   for (int i = 0; i < 5; ++i)
   {
      SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
      alice.conversation->addParticipant(aliceCall, "sip:9002");
      alice.conversation->start(aliceCall);

      auto aliceEvents = std::async(std::launch::async, [&] () {
         assertConversationEnded(alice, aliceCall, ConversationEndReason_UserTerminatedRemotely);
      });

      waitFor(aliceEvents);
      
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
   }
}

}  // namespace

