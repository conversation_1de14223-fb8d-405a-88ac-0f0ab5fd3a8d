#include "cpcapi2_test_fixture.h"
#include "test_events.h"

#include "phone/PhoneInterface.h"
#include "resip/stack/NameAddr.hxx"

#ifdef ANDROID
#include "JniHelper.h"
#include "../language_wrapper/Android/jni/AndroidBackgroundManagerInterface.h"
// opendir
#include <sys/types.h>
#include <dirent.h>
#endif

#include "impl/util/FileUtils.h"
#include "impl/phone/EventQueue.h"

#include <vector>
#include <fstream>
#include <chrono>
#include <random>
#include <iostream>
#include <sstream>
#include <filesystem>

#include "nlohmann/json.hpp"
#include "gen/Phone/datatypes/PhoneEvents.h"
#include "gen/Phone/datatypes/PhoneLogEvent.h"
#include "gen/Phone/datatypes/PhoneOnLicensingErrorEvent.h"
#include "gen/Phone/datatypes/PhoneOnLicensingSuccessEvent.h"
#include "gen/Phone/datatypes/LicensingErrorReason.h"

using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::SipEvent;

namespace
{

   class PhoneModuleTest : public CpcapiAutoTest
   {
   public:
      PhoneModuleTest() {}
      virtual ~PhoneModuleTest() {}
   };

   TEST_F(PhoneModuleTest, TestReactorExecute)
   {
      class TestReactorExecuteHelper
      {
      public:
         TestReactorExecuteHelper() : mTestFunctionFinished(false) {}

         std::atomic<bool> mTestFunctionFinished;

         static void staticTestFunction(void* context)
         {
            if (TestReactorExecuteHelper* instance = static_cast<TestReactorExecuteHelper*>(context))
            {
               instance->testFunction();
            }
         }

         void testFunction()
         {
            mTestFunctionFinished = true;
         }
      };

      Phone* phone = Phone::create();
      phone->initialize(LicenseInfo(), (PhoneHandler*)NULL);

      PhoneInternal* pi = static_cast<PhoneInternal*>(phone);

      for (int i = 0; i < 50; ++i)
      {
         std::vector< std::future<void> > futures;
         for (int i = 0; i < 10; ++i)
         {
            TestReactorExecuteHelper* reh = new TestReactorExecuteHelper();
            futures.push_back(std::async(std::launch::async, [&, reh]()
            {
               auto start = std::chrono::system_clock::now();
               pi->blockUntilRanOnSdkModuleThread(TestReactorExecuteHelper::staticTestFunction, reinterpret_cast<void*>(reh));

               auto end = std::chrono::system_clock::now();

               std::chrono::duration<double> diff = end - start;
               ASSERT_GE(1, diff.count()) << "Took too long to run on SDK thread";
               ASSERT_EQ(true, reh->mTestFunctionFinished);

               delete reh;
            }));
         }

         for (std::vector< std::future<void> >::const_iterator it = futures.begin(); it != futures.end(); ++it)
         {
            ASSERT_EQ(it->wait_for(std::chrono::milliseconds(1000)), std::future_status::ready);
         }
      }

      Phone::release(phone);
   }

   TEST_F(PhoneModuleTest, TestReactorExecuteFromReactorThread)
   {
      class TestReactorExecuteHelper
      {
      public:
         TestReactorExecuteHelper(PhoneInternal* phoneInternal) :
            mFirstTestFunctionFinished(false), mSecondTestFunctionFinished(false), mPhoneInternal(phoneInternal) {}

         PhoneInternal* mPhoneInternal;
         std::atomic<bool> mFirstTestFunctionFinished;
         std::atomic<bool> mSecondTestFunctionFinished;

         static void staticTestFunction(void* context)
         {
            if (TestReactorExecuteHelper* instance = static_cast<TestReactorExecuteHelper*>(context))
            {
               instance->testFunction();
            }
         }

         static void staticTestFunction2(void* context)
         {
            if (TestReactorExecuteHelper* instance = static_cast<TestReactorExecuteHelper*>(context))
            {
               instance->testFunction2();
            }
         }

      private:
         void testFunction()
         {
            mFirstTestFunctionFinished = true;
            // we're running on the main SDK thread and also asking to execute synchronously on that thread here
            mPhoneInternal->blockUntilRanOnSdkModuleThread(TestReactorExecuteHelper::staticTestFunction2, reinterpret_cast<void*>(this));
         }

         void testFunction2()
         {
            mSecondTestFunctionFinished = true;
         }
      };

      Phone* phone = Phone::create();
      LicenseInfo li;
      phone->initialize(li, static_cast<PhoneHandler*>(NULL));

      PhoneInternal* pi = static_cast<PhoneInternal*>(phone);
      TestReactorExecuteHelper* reh = new TestReactorExecuteHelper(pi);

      pi->blockUntilRanOnSdkModuleThread(TestReactorExecuteHelper::staticTestFunction, reinterpret_cast<void*>(reh));

      ASSERT_TRUE(reh->mFirstTestFunctionFinished);
      ASSERT_TRUE(reh->mSecondTestFunctionFinished);

      Phone::release(phone);
   }

   TEST_F(PhoneModuleTest, TestIoServicePost)
   {
      class TestHelper
      {
      public:
         TestHelper() : mTestFunctionFinished(false) {}

         std::condition_variable mCv;
         std::mutex mMutex;
         bool mTestFunctionFinished;

         static void staticTestFunction(void* context)
         {
            if (TestHelper* instance = static_cast<TestHelper*>(context))
            {
               instance->testFunction();
            }
         }

         void testFunction()
         {
            {
               std::lock_guard<std::mutex> lock(mMutex);
               mTestFunctionFinished = true;
            }
            mCv.notify_all();
         }
      };

      Phone* phone = PhoneInternal::create(1);
      phone->initialize(LicenseInfo(), (PhoneHandler*)NULL);
      {
         PhoneInternal* pi = static_cast<PhoneInternal*>(phone);
         TestHelper th;
         pi->runOnAsioThread(TestHelper::staticTestFunction, reinterpret_cast<void*>(&th));

         {
            std::unique_lock<std::mutex> lk(th.mMutex);
            th.mCv.wait_until(lk, std::chrono::steady_clock::now() + std::chrono::milliseconds(500));
            ASSERT_TRUE(th.mTestFunctionFinished);
         }
      }

      Phone* phone2 = PhoneInternal::create(static_cast<PhoneInternal*>(phone));
      phone2->initialize(LicenseInfo(), (PhoneHandler*)NULL);
      {
         PhoneInternal* pi = static_cast<PhoneInternal*>(phone2);
         TestHelper th;
         pi->runOnAsioThread(TestHelper::staticTestFunction, reinterpret_cast<void*>(&th));

         {
            std::unique_lock<std::mutex> lk(th.mMutex);
            th.mCv.wait_until(lk, std::chrono::steady_clock::now() + std::chrono::milliseconds(500));
            ASSERT_TRUE(th.mTestFunctionFinished);
         }
      }

      Phone* phone3 = PhoneInternal::create(static_cast<PhoneInternal*>(phone));
      phone3->initialize(LicenseInfo(), (PhoneHandler*)NULL);
      {
         PhoneInternal* pi = static_cast<PhoneInternal*>(phone2);
         TestHelper th;
         pi->runOnAsioThread(TestHelper::staticTestFunction, reinterpret_cast<void*>(&th));

         {
            std::unique_lock<std::mutex> lk(th.mMutex);
            th.mCv.wait_until(lk, std::chrono::steady_clock::now() + std::chrono::milliseconds(500));
            ASSERT_TRUE(th.mTestFunctionFinished);
         }
      }

      Phone::release(phone3);
      // make sure phone2's io_service still works
      {
         PhoneInternal* pi = static_cast<PhoneInternal*>(phone2);
         TestHelper th;
         pi->runOnAsioThread(TestHelper::staticTestFunction, reinterpret_cast<void*>(&th));

         {
            std::unique_lock<std::mutex> lk(th.mMutex);
            th.mCv.wait_until(lk, std::chrono::steady_clock::now() + std::chrono::milliseconds(500));
            ASSERT_TRUE(th.mTestFunctionFinished);
         }
      }

      // make sure master phone's io_service still works
      {
         PhoneInternal* pi = static_cast<PhoneInternal*>(phone);
         TestHelper th;
         pi->runOnAsioThread(TestHelper::staticTestFunction, reinterpret_cast<void*>(&th));

         {
            std::unique_lock<std::mutex> lk(th.mMutex);
            th.mCv.wait_until(lk, std::chrono::steady_clock::now() + std::chrono::milliseconds(500));
            ASSERT_TRUE(th.mTestFunctionFinished);
         }
      }

   }

   TEST_F(PhoneModuleTest, TestReactorBacklogged)
   {
#if _WIN32
      if (TestEnvironmentConfig::dockerContainerized())
      {
         GTEST_SKIP() << "Test is flaky on docker windows runs";
      }
#endif

      class TestReactorExecuteHelper2
      {
      public:
         TestReactorExecuteHelper2(int testFnSleepIntervalMs) : mCount(0), mTestFnSleepIntervalMs(testFnSleepIntervalMs) {}

         std::atomic<int> mCount;
         int mTestFnSleepIntervalMs;

         static void staticTestFunction(void* context)
         {
            if (TestReactorExecuteHelper2* instance = static_cast<TestReactorExecuteHelper2*>(context))
            {
               instance->testFunction();
            }
         }

         void testFunction()
         {
            std::this_thread::sleep_for(std::chrono::milliseconds(mTestFnSleepIntervalMs));
            ++mCount;

            //safeCout("Processed " << mCount);
         }
      };

      Phone* phone = Phone::create();
      phone->initialize(LicenseInfo(), (PhoneHandler*)NULL);

      const int testFnSleepIntervalMs = 5;
      TestReactorExecuteHelper2 reh(testFnSleepIntervalMs);

      PhoneInternal* pi = static_cast<PhoneInternal*>(phone);

      int count = 0;
      for (int i = 0; i < 3000; ++i)
      {
         pi->runOnSdkModuleThread(TestReactorExecuteHelper2::staticTestFunction, reinterpret_cast<void*>(&reh));
         ++count;
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(testFnSleepIntervalMs * count));
      // the reactor thread should be nearly done by now..
      ASSERT_GT(reh.mCount, count * 0.25);


      // block, to make sure all the above runOnSdkModuleThread calls (which translate into Reactor::post) have finished
      pi->blockUntilRanOnSdkModuleThread(TestReactorExecuteHelper2::staticTestFunction, reinterpret_cast<void*>(&reh));
      ++count;

      ASSERT_EQ(count, reh.mCount);

      Phone::release(phone);
   }

   // spams main SDK thread reactor with posts, and for each of those posts,
   // schedules a further post from main SDK thread to main SDK thread
   TEST_F(PhoneModuleTest, TestReactorBacklogged2)
   {
      class TestReactorExecuteHelper
      {
      public:
         TestReactorExecuteHelper(int testFnSleepIntervalMs, PhoneInternal* pi) :
            mFirstCount(0), mSecondCount(0), mTestFnSleepIntervalMs(testFnSleepIntervalMs),
            mPhoneInternal(pi) {}

         std::atomic<int> mFirstCount;
         std::atomic<int> mSecondCount;
         int mTestFnSleepIntervalMs;
         PhoneInternal* mPhoneInternal;

         static void staticTestFunction1(void* context)
         {
            if (TestReactorExecuteHelper* instance = static_cast<TestReactorExecuteHelper*>(context))
            {
               instance->testFunction1();
            }
         }

         static void staticTestFunction2(void* context)
         {
            if (TestReactorExecuteHelper* instance = static_cast<TestReactorExecuteHelper*>(context))
            {
               instance->testFunction2();
            }
         }

      private:
         void testFunction1()
         {
            std::this_thread::sleep_for(std::chrono::milliseconds(mTestFnSleepIntervalMs));
            ++mFirstCount;

            if (mFirstCount == 1)
            {
               // block to further help get reactor get backlogged
               std::this_thread::sleep_for(std::chrono::milliseconds(2000));
            }

            // testFunction1() is currently running on PhoneInterface's reactor thread. we'll now attempt to
            // post to the same reactor
            mPhoneInternal->runOnSdkModuleThread(TestReactorExecuteHelper::staticTestFunction2, reinterpret_cast<void*>(this));
         }

         void testFunction2()
         {
            std::this_thread::sleep_for(std::chrono::milliseconds(mTestFnSleepIntervalMs));
            ++mSecondCount;
         }
      };

      Phone* phone = Phone::create();
      LicenseInfo li;
      phone->initialize(li, static_cast<PhoneHandler*>(NULL));

      const int testFnSleepIntervalMs = 2;
      PhoneInternal* pi = static_cast<PhoneInternal*>(phone);
      TestReactorExecuteHelper reh(testFnSleepIntervalMs, pi);

      int count = 0;
      for (int i = 0; i < 3000; ++i)
      {
         pi->runOnSdkModuleThread(TestReactorExecuteHelper::staticTestFunction1, reinterpret_cast<void*>(&reh));
         ++count;
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(testFnSleepIntervalMs * count));

      // block; when this eventually returns, all staticTestFunction1 should have already been run
      pi->blockUntilRanOnSdkModuleThread(TestReactorExecuteHelper::staticTestFunction1, reinterpret_cast<void*>(&reh));
      ++count;

      // block again; when this eventually returns, all staticTestFunction2 should have already been run
      pi->blockUntilRanOnSdkModuleThread(TestReactorExecuteHelper::staticTestFunction2, reinterpret_cast<void*>(&reh));

      ASSERT_EQ(count, reh.mFirstCount);
      ASSERT_EQ(count+1, reh.mSecondCount);

      Phone::release(phone);
   }


   TEST_F(PhoneModuleTest, TestNotInitialized)
   {
      Phone* phone = Phone::create();
      SipAccountManager* intf = SipAccountManager::getInterface(phone);
      ASSERT_TRUE(intf != NULL);
      ASSERT_TRUE(dynamic_cast<PhoneInterface*>(phone)->isInitialized() == false);
   }

   class TestReactorSafeRelease_TestObj : public resip::ReactorBinded
   {
   public:
      TestReactorSafeRelease_TestObj(resip::MultiReactor& reactor, int adj) : mReactor(reactor), mAdj(adj) {}
      virtual ~TestReactorSafeRelease_TestObj() {
         safeCout("dtor: " << mAdj);
      }

      virtual void release() {
         delete this;
      }

      void printMsg(int msgnum) {
         int newnum = msgnum + mAdj;
         safeCout("msg: " << newnum);
         std::this_thread::sleep_for(std::chrono::milliseconds(20));
      }

      void printMsg2(int msgnum) {
         safeCout("msg: " << msgnum);
         mReactor.post(resip::resip_safe_bind(&TestReactorSafeRelease_TestObj::printMsg, this, msgnum));
      }

   private:
      resip::MultiReactor& mReactor;
      int mAdj;
   };

   TEST_F(PhoneModuleTest, TestReactorSafeRelease) {

      TestAccount bob("bob", Account_Init, true, NULL, NULL, false, false);

      auto bobEvents = std::async(std::launch::async, [&]() {
         TestReactorSafeRelease_TestObj* testObj = new TestReactorSafeRelease_TestObj(dynamic_cast<PhoneInterface*>(bob.phone)->getSdkModuleThread(), std::abs(resip::Random::getCryptoRandom()) % 100);
         for (int i = 0; i < 100; i++)
         {
            if (resip::Random::getCryptoRandom() % 2 == 0)
            {
               std::this_thread::sleep_for(std::chrono::milliseconds(20));
            }
            dynamic_cast<PhoneInterface*>(bob.phone)->getSdkModuleThread().post(resip::resip_safe_bind(&TestReactorSafeRelease_TestObj::printMsg, testObj, i));
         }
         testObj->reactorSafeRelease(&dynamic_cast<PhoneInterface*>(bob.phone)->getSdkModuleThread());

         // SCORE-1118: ensure invoking reactorSafeRelease more than once is safe
         testObj->reactorSafeRelease(&dynamic_cast<PhoneInterface*>(bob.phone)->getSdkModuleThread());
      });
      bob.shutdown(10, true);
      bob.phone = NULL;

   }

   TEST_F(PhoneModuleTest, TestReactorSafeReleaseAfter) {

      TestAccount bob("bob", Account_Init, true, NULL, NULL, false, false);

      auto bobEvents = std::async(std::launch::async, [&]() {
         TestReactorSafeRelease_TestObj* testObj = new TestReactorSafeRelease_TestObj(dynamic_cast<PhoneInterface*>(bob.phone)->getSdkModuleThread(), std::abs(resip::Random::getCryptoRandom()) % 100);
         for (int i = 0; i < 100; i++)
         {
            if (resip::Random::getCryptoRandom() % 2 == 0)
            {
               std::this_thread::sleep_for(std::chrono::milliseconds(20));
            }
            dynamic_cast<PhoneInterface*>(bob.phone)->getSdkModuleThread().post(resip::resip_safe_bind(&TestReactorSafeRelease_TestObj::printMsg, testObj, i));
         }
         testObj->reactorSafeReleaseAfter(&dynamic_cast<PhoneInterface*>(bob.phone)->getSdkModuleThread());
      });
      bob.shutdown(10, true);
      bob.phone = NULL;

   }

   TEST_F(PhoneModuleTest, TestReactorSafeReleaseAfterPostedPost) {

      TestAccount bob("bob", Account_Init, true, NULL, NULL, false, false);

      auto bobEvents = std::async(std::launch::async, [&]() {
         TestReactorSafeRelease_TestObj* testObj = new TestReactorSafeRelease_TestObj(dynamic_cast<PhoneInterface*>(bob.phone)->getSdkModuleThread(), std::abs(resip::Random::getCryptoRandom()) % 100);
         for (int i = 0; i < 100; i++)
         {
            if (resip::Random::getCryptoRandom() % 2 == 0)
            {
               std::this_thread::sleep_for(std::chrono::milliseconds(20));
            }
            resip::MultiReactor* r = &dynamic_cast<PhoneInterface*>(bob.phone)->getSdkModuleThread();
            dynamic_cast<PhoneInterface*>(bob.phone)->getSdkModuleThread().post(resip::resip_safe_bind(&TestReactorSafeRelease_TestObj::printMsg2, testObj, i));
         }
         testObj->reactorSafeReleaseAfter(&dynamic_cast<PhoneInterface*>(bob.phone)->getSdkModuleThread());
      });
      bob.shutdown(10, true);
      bob.phone = NULL;

   }

   TEST_F(PhoneModuleTest, TestReactorQuickShutdown) {

      class Foo
      {
      public:
         void bar()
         {
            std::this_thread::sleep_for(std::chrono::milliseconds(300));
         }
      };

      Foo foo;

      for (int i = 0; i < 50; ++i)
      {
         resip::MultiReactor* reactor = new resip::MultiReactor("TestReactor");
         reactor->start();

         reactor->post(resip::resip_bind(&Foo::bar, &foo));

         auto start = std::chrono::system_clock::now();
         delete reactor;
         auto end = std::chrono::system_clock::now();

         auto diff = std::chrono::duration_cast<std::chrono::seconds>(end - start);
         ASSERT_GE(3, diff.count()) << "Took too long to delete reactor";
      }
   }

  TEST_F(PhoneModuleTest, TestReactorStartedThenDestroyed) {

     for (int i = 0; i < 50; ++i)
     {
        resip::MultiReactor* reactor = new resip::MultiReactor("TestReactor");
        reactor->start();

        auto start = std::chrono::system_clock::now();
        delete reactor;
        auto end = std::chrono::system_clock::now();

        auto diff = std::chrono::duration_cast<std::chrono::seconds>(end - start);
        ASSERT_GE(3, diff.count()) << "Took too long to delete reactor";
     }
   }

   TEST_F(PhoneModuleTest, TestReactorStartedStoppedThenDestroyed) {

      for (int i = 0; i < 50; ++i)
      {
         resip::MultiReactor* reactor = new resip::MultiReactor("TestReactor");
         reactor->start();

         reactor->stop();

         auto start = std::chrono::system_clock::now();
         delete reactor;
         auto end = std::chrono::system_clock::now();

         auto diff = std::chrono::duration_cast<std::chrono::seconds>(end - start);
         ASSERT_GE(3, diff.count()) << "Took too long to delete reactor";
      }
   }

   TEST_F(PhoneModuleTest, TestReactorStartedStoppedThenDestroyed2) {

      class Foo
      {
      public:
         void bar()
         {
            std::this_thread::sleep_for(std::chrono::milliseconds(300));
         }
      };

      Foo foo;

      for (int i = 0; i < 50; ++i)
      {
         resip::MultiReactor* reactor = new resip::MultiReactor("TestReactor");
         reactor->start();

         reactor->post(resip::resip_bind(&Foo::bar, &foo));

         reactor->stop();

         auto start = std::chrono::system_clock::now();
         delete reactor;
         auto end = std::chrono::system_clock::now();

         auto diff = std::chrono::duration_cast<std::chrono::seconds>(end - start);
         ASSERT_GE(3, diff.count()) << "Took too long to delete reactor";
      }
   }

   TEST_F(PhoneModuleTest, TestReactorStartedStoppedThenDestroyed3) {

      class Foo
      {
      public:
         void bar()
         {
            std::this_thread::sleep_for(std::chrono::milliseconds(300));
         }
      };

      Foo foo;

      for (int i = 0; i < 50; ++i)
      {
         resip::MultiReactor* reactor = new resip::MultiReactor("TestReactor");
         reactor->start();

         reactor->execute(resip::resip_bind(&Foo::bar, &foo));

         reactor->stop();

         auto start = std::chrono::system_clock::now();
         delete reactor;
         auto end = std::chrono::system_clock::now();

         auto diff = std::chrono::duration_cast<std::chrono::seconds>(end - start);
         ASSERT_GE(3, diff.count()) << "Took too long to delete reactor";
      }
   }

   // invokes execute(..) on a reactor before starting it, and makes sure
   // the execute(..) is processed after reactor is started
   TEST_F(PhoneModuleTest, TestReactorExecuteThenStart) {

      for (int i = 0; i < 50; ++i)
      {
         resip::MultiReactor* reactor = new resip::MultiReactor("TestReactor");

         class Foo
         {
         public:
            void bar()
            {
               std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }
         };

         std::atomic<bool> reactorExecuteReady(false);
         std::atomic<bool> reactorExecuteDone(false);

         auto reactorExecute = std::async(std::launch::async, [&]()
         {
            Foo foo;
            reactorExecuteReady = true;
            reactor->execute(resip::resip_bind(&Foo::bar, &foo));
            reactorExecuteDone = true;
         });

         while (!reactorExecuteReady)
         {
            std::this_thread::yield();
         }

         // try and make sure execute is called before we call start
         std::this_thread::sleep_for(std::chrono::milliseconds(100));
         reactor->start();

         waitForMs(reactorExecute, std::chrono::seconds(2));

         ASSERT_TRUE(reactorExecuteDone);

         auto start = std::chrono::system_clock::now();
         delete reactor;
         auto end = std::chrono::system_clock::now();

         auto diff = std::chrono::duration_cast<std::chrono::seconds>(end - start);
         ASSERT_GE(3, diff.count()) << "Took too long to delete reactor";
      }
   }

   // invokes post(..) on a reactor before starting it, and makes sure
   // the post(..) is processed after reactor is started
   TEST_F(PhoneModuleTest, TestReactorPostThenStart) {

      for (int i = 0; i < 50; ++i)
      {
         resip::MultiReactor* reactor = new resip::MultiReactor("TestReactor");

         class Foo
         {
         public:
            Foo() : barExecuted(false) {}

            void bar()
            {
               std::this_thread::sleep_for(std::chrono::milliseconds(10));
               barExecuted = true;
            }

            std::atomic<bool> barExecuted;
         };

         std::atomic<bool> reactorPostReady(false);

         Foo foo;
         auto reactorExecute = std::async(std::launch::async, [&]()
         {
            reactorPostReady = true;
            reactor->post(resip::resip_bind(&Foo::bar, &foo));
         });

         while (!reactorPostReady)
         {
            std::this_thread::yield();
         }

         // try and make sure post is called before we call start
         std::this_thread::sleep_for(std::chrono::milliseconds(100));
         reactor->start();

         waitForMs(reactorExecute, std::chrono::seconds(2));

         {
            auto start = std::chrono::system_clock::now();
            while (!foo.barExecuted)
            {
               std::this_thread::yield();
            }
            auto end = std::chrono::system_clock::now();
            auto diff = std::chrono::duration_cast<std::chrono::seconds>(end - start);
            ASSERT_GE(3, diff.count()) << "Took too long for post to run";
         }

         {
            auto start = std::chrono::system_clock::now();
            delete reactor;
            auto end = std::chrono::system_clock::now();

            auto diff = std::chrono::duration_cast<std::chrono::seconds>(end - start);
            ASSERT_GE(3, diff.count()) << "Took too long to delete reactor";
         }
      }
   }

   TEST_F(PhoneModuleTest, TestPhoneInitRelease)
   {
      for (int i = 0; i < 10; ++i)
      {
         Phone* phone = Phone::create();
         phone->initialize(LicenseInfo(), (PhoneHandler*)NULL);

         Phone::release(phone);
      }
   }

#ifdef ANDROID
   TEST_F(PhoneModuleTest, TestEnableDisableAndroidBgManager)
   {
      Phone* phone = Phone::create();
      phone->initialize(LicenseInfo(), (PhoneHandler*)NULL);

      for (int i = 0; i < 30; ++i)
      {
         AndroidBackgroundManagerInterface::getInterface(phone)->enableBackgroundingSupport(CPCAPI2::Jni::GetContext());
         auto start = std::chrono::system_clock::now();
         AndroidBackgroundManagerInterface::getInterface(phone)->disableBackgroundingSupport();
         auto diff = std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now() - start);
         //safeCout("Run " << i << " finished in " << diff.count() << "s");
         ASSERT_GE(3, diff.count()) << "Took too long to disable backgroundmanager";
      }

      Phone::release(phone);
   }
#endif

   TEST_F(PhoneModuleTest, TestFileUtilsCreateDir)
   {
#ifdef ANDROID
      cpc::string tempPath = TestEnvironmentConfig::tempPath();

      std::filesystem::path testPath;
      if (tempPath.empty())
      {
         testPath = std::filesystem::current_path();
      }
      else
      {
         testPath = tempPath.c_str();
      }

      testPath /= "TestFileUtilsCreateDir";

      std::filesystem::remove_all(testPath);

      ASSERT_FALSE(std::filesystem::exists(testPath));
      FileUtils::CreateDir(testPath.string().c_str(), true);
      ASSERT_TRUE(std::filesystem::exists(testPath));
#endif
   }

   TEST_F(PhoneModuleTest, VerifyEventSourcePollerPhoneFireEvents)
   {
      TestAccount alice("alice", Account_Init);
      PhoneInterface* phone = dynamic_cast<PhoneInterface*>(alice.phone);

      const std::string licenseErrorMessage = "License Error";

      {
         // Only subscribed to phone error events - should no receive license success
         {
            cpc::vector<cpc::string> events;
            events.push_back("Phone.onError");
            events.push_back(to_string(jsonrpc::CPCAPI2::Phone::PhoneEvents::PhoneDotOnLicensingError).c_str());
            int queueId = phone->createEventQueue(events);
            ASSERT_TRUE(queueId > 0);

            auto app = std::async(std::launch::async, [&]()
            {
               SdkEvent event = phone->getEvent(queueId, 5000);
               ASSERT_EQ(event.type(), to_string(jsonrpc::CPCAPI2::Phone::PhoneEvents::PhoneDotOnLicensingError));

               jsonrpc::CPCAPI2::Phone::PhoneOnLicensingErrorEvent jrpcEvt(event.json());
               ASSERT_FALSE(jrpcEvt.errorText.value().empty());
               ASSERT_EQ(jrpcEvt.errorReason.value(), jsonrpc::CPCAPI2::Phone::LicensingErrorReason::LicensingErrorReasonInvalid);

               phone->destroyEventQueue(queueId);
            });
            auto sdk = std::async(std::launch::async, [&]()
            {
               // Induce license success message
               phone->handleLicensingSuccess();

               // Induce license failure
               phone->handleLicensingError(Licensing::LicenseStatus_Invalid, licenseErrorMessage.c_str());
            });
            waitFor2Ms(app, sdk, std::chrono::seconds(20000));
         }

         // Subscribed to all events - verify receiving all phone event
         {
            cpc::vector<cpc::string> events;
            int queueId = phone->createEventQueue(events);
            ASSERT_TRUE(queueId > 0);

            auto app = std::async(std::launch::async, [&]()
            {
               bool gotLicenseSuccess = false, gotLicensingError = false, gotLogEvent = false;
               for (;;)
               {
                  SdkEvent event = phone->getEvent(queueId, 5000);

                  if (event.type() == to_string(jsonrpc::CPCAPI2::Phone::PhoneEvents::PhoneDotOnLicensingSuccess).c_str())
                  {
                     ASSERT_FALSE(gotLicenseSuccess);
                     jsonrpc::CPCAPI2::Phone::PhoneOnLicensingSuccessEvent jrpcEvt(event.json());
                     gotLicenseSuccess = true;
                  }
                  else if (event.type() == to_string(jsonrpc::CPCAPI2::Phone::PhoneEvents::PhoneDotOnLicensingError).c_str())
                  {
                     ASSERT_FALSE(gotLicensingError);
                     jsonrpc::CPCAPI2::Phone::PhoneOnLicensingErrorEvent jrpcEvt(event.json());
                     ASSERT_FALSE(jrpcEvt.errorText.value().empty());
                     ASSERT_EQ(jrpcEvt.errorReason.value(), jsonrpc::CPCAPI2::Phone::LicensingErrorReason::LicensingErrorReasonExpired);

                     gotLicensingError = true;
                  }
                  else if (event.type() == to_string(jsonrpc::CPCAPI2::Phone::PhoneEvents::PhoneDotLog).c_str())
                  {
                     jsonrpc::CPCAPI2::Phone::PhoneLogEvent jrpcEvt(event.json());
                     ASSERT_FALSE(jrpcEvt.subsystem.value().empty());
                     ASSERT_FALSE(jrpcEvt.file.value().empty());
                     ASSERT_GE(jrpcEvt.line.value(), 0);
                     ASSERT_FALSE(jrpcEvt.message.value().empty());
                     ASSERT_FALSE(jrpcEvt.messageWithHeaders.value().empty());
                     
                     gotLogEvent = true;
                  }

                  if (gotLicenseSuccess && gotLicensingError && gotLogEvent)
                  {
                     break;
                  }
               }

               phone->destroyEventQueue(queueId);
            });

            auto sdk = std::async(std::launch::async, [&]()
            {
               // Induce license success
               phone->handleLicensingSuccess();

               // Induce license failure
               phone->handleLicensingError(Licensing::LicenseStatus_Expired, "No license or invalid license provided");
            });
            waitFor2Ms(app, sdk, std::chrono::seconds(20000));
         }
      }
   }
}
