// bliu: how to simulate 480 and 404 from server

#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#if (CPCAPI2_BRAND_CAPABILITY_DISCOVERY_MODULE == 1)

#include "cpcapi2_test_fixture.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>

#include "test_framework/cpcapi2_test_framework.h"
#include "test_framework/bare_bones_sip_endpoint.h"

#include <thread>
#include <future>
#include <string>
#include <set>

using namespace CPCAPI2;
using namespace CPCAPI2::SipEvent;
using namespace CPCAPI2::RcsCapabilityDiscovery;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::test;

namespace cpc {
inline bool operator ==(const RcsCapabilitySet& lhs, const RcsCapabilitySet& rhs)
{
   if (lhs.size() != rhs.size()) return false;

   std::set<RcsCapabilitySet::value_type> l (lhs.begin(), lhs.end());
   std::set<RcsCapabilitySet::value_type> r (rhs.begin(), rhs.end());

   return l == r;
}
}

namespace {

class CapabilityDiscoveryModuleTest : public CpcapiAutoTest
{
public:
   CapabilityDiscoveryModuleTest() {}
   virtual ~CapabilityDiscoveryModuleTest() {}
};

const RcsCapabilitySet _GetAllCaps()
{
   RcsCapabilitySet result;

   result.push_back(RcsCapability::ImageShare);
   result.push_back(RcsCapability::VideoShare);

   result.push_back(RcsCapability::Chat);
   result.push_back(RcsCapability::FullStoreAndForwardGroupChat);
   result.push_back(RcsCapability::FileTransfer);
   result.push_back(RcsCapability::FileTransferThumbnail);
   result.push_back(RcsCapability::FileTransferViaHTTP);

   result.push_back(RcsCapability::IPBasedStandaloneMessaging);

   result.push_back(RcsCapability::VideoShareOutsideOfAVoiceCall);

   result.push_back(RcsCapability::SocialPresenceInformation);

   result.push_back(RcsCapability::IPVoiceCall);
   result.push_back(RcsCapability::IPVideoCall);
   result.push_back(RcsCapability::RCSIPVoiceCall);
   result.push_back(RcsCapability::RCSIPVideoCall);
   result.push_back(RcsCapability::RCSIPVideoCallOnly);

   result.push_back(RcsCapability::GeolocationPUSH);
   result.push_back(RcsCapability::GeolocationPULL);
   result.push_back(RcsCapability::GeolocationPULLUsingFileTransfer);

   return result;
};

#if 1
// test a basic capability discovery sent from alice to bob
TEST_F(CapabilityDiscoveryModuleTest, BasicCapabilityDiscovery) {
   TestAccount alice("alice");
   TestAccount bob("bob");
   alice.capability->addContact(alice.handle, bob.config.uri());
   bob.capability->addContact(bob.handle, alice.config.uri());

   auto aliceEvents = std::async(std::launch::async, [&] () {
      { // expect success sending
         RcsOnContactCapabilityStatusChangedEvent evt;
         SipAccountHandle hdl;
         ASSERT_TRUE(alice.capabilityEvents->expectEvent( "RcsCapabilityDiscoveryHandler::onContactCapabilityStatusChanged",
            15000, HandleEqualsPred<SipAccountHandle>(alice.handle), hdl, evt));
         ASSERT_EQ(alice.handle, hdl);
         ASSERT_EQ(evt.status.targetAddress, bob.config.uri());
         ASSERT_EQ(evt.signalingStatusCode, 200);
         ASSERT_EQ(evt.status.isRcsCapable, false);
         ASSERT_TRUE(evt.status.caps.empty());
      }
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipAccountHandle hdl;
      { // expect to receive capability discovery
         RcsOnContactCapabilityStatusChangedEvent evt;
         ASSERT_TRUE(bob.capabilityEvents->expectEvent( "RcsCapabilityDiscoveryHandler::onContactCapabilityStatusChanged",
            15000, HandleEqualsPred<SipAccountHandle>(bob.handle), hdl, evt));
         ASSERT_EQ(bob.handle, hdl);
         ASSERT_EQ(evt.status.targetAddress, alice.config.uri());
         ASSERT_EQ(evt.signalingStatusCode, 200);
         ASSERT_EQ(evt.status.isRcsCapable, false);
         ASSERT_TRUE(evt.status.caps.empty());
      }
   });

   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
}
#endif

#if 1
// test a basic capability discovery sent from alice to bob
TEST_F(CapabilityDiscoveryModuleTest, NormalCapabilityDiscovery) {
   TestAccount alice("alice");
   TestAccount bob("bob");
   auto caps = _GetAllCaps();
   alice.capability->setMyCapabilities(alice.handle, caps);
   bob.capability->setMyCapabilities(bob.handle, caps);
   alice.capability->addContact(alice.handle, bob.config.uri());
   bob.capability->addContact(bob.handle, alice.config.uri());

   auto aliceEvents = std::async(std::launch::async, [&] () {
      { // expect success sending
         RcsOnContactCapabilityStatusChangedEvent evt;
         SipAccountHandle hdl;
         ASSERT_TRUE(alice.capabilityEvents->expectEvent( "RcsCapabilityDiscoveryHandler::onContactCapabilityStatusChanged",
            15000, HandleEqualsPred<SipAccountHandle>(alice.handle), hdl, evt));
         ASSERT_EQ(alice.handle, hdl);
         ASSERT_EQ(evt.status.targetAddress, bob.config.uri());
         ASSERT_EQ(evt.signalingStatusCode, 200);
         ASSERT_EQ(evt.status.isRcsCapable, true);
         ASSERT_EQ(evt.status.caps, caps);
      }
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipAccountHandle hdl;
      { // expect to receive capability discovery
         RcsOnContactCapabilityStatusChangedEvent evt;
         ASSERT_TRUE(bob.capabilityEvents->expectEvent( "RcsCapabilityDiscoveryHandler::onContactCapabilityStatusChanged",
            15000, HandleEqualsPred<SipAccountHandle>(bob.handle), hdl, evt));
         ASSERT_EQ(bob.handle, hdl);
         ASSERT_EQ(evt.status.targetAddress, alice.config.uri());
         ASSERT_EQ(evt.signalingStatusCode, 200);
         ASSERT_EQ(evt.status.isRcsCapable, true);
         ASSERT_EQ(evt.status.caps, caps);
      }
   });

   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
}

// test a basic capability discovery sent from alice to bob
TEST_F(CapabilityDiscoveryModuleTest, NormalCapabilityDiscoveryFromCache) {
   TestAccount alice("alice");
   TestAccount bob("bob");
   auto caps = _GetAllCaps();
   alice.capability->setMyCapabilities(alice.handle, caps);
   bob.capability->setMyCapabilities(bob.handle, caps);
   alice.capability->addContact(alice.handle, bob.config.uri());
   bob.capability->addContact(bob.handle, alice.config.uri());

   auto aliceEvents = std::async(std::launch::async, [&] () {
      { // expect success sending
         RcsOnContactCapabilityStatusChangedEvent evt;
         SipAccountHandle hdl;
         ASSERT_TRUE(alice.capabilityEvents->expectEvent( "RcsCapabilityDiscoveryHandler::onContactCapabilityStatusChanged",
            15000, HandleEqualsPred<SipAccountHandle>(alice.handle), hdl, evt));
         ASSERT_EQ(alice.handle, hdl);
         ASSERT_EQ(evt.status.targetAddress, bob.config.uri());
         ASSERT_EQ(evt.signalingStatusCode, 200);
         ASSERT_EQ(evt.status.isRcsCapable, true);
         ASSERT_EQ(evt.status.caps, caps);

         RcsCapabilityStatus status;
         ASSERT_EQ(alice.capability->getContactCapabilityStatus(alice.handle, bob.config.uri(), status), RcsCapabilityDiscoveryManager::kCompletedSynchronously);
         ASSERT_EQ(status.isRcsCapable, true);
         ASSERT_EQ(status.targetAddress, bob.config.uri());
         ASSERT_EQ(status.caps, caps);
      }
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      SipAccountHandle hdl;
      { // expect to receive capability discovery
         RcsOnContactCapabilityStatusChangedEvent evt;
         ASSERT_TRUE(bob.capabilityEvents->expectEvent( "RcsCapabilityDiscoveryHandler::onContactCapabilityStatusChanged",
            15000, HandleEqualsPred<SipAccountHandle>(bob.handle), hdl, evt));
         ASSERT_EQ(bob.handle, hdl);
         ASSERT_EQ(evt.status.targetAddress, alice.config.uri());
         ASSERT_EQ(evt.signalingStatusCode, 200);
         ASSERT_EQ(evt.status.isRcsCapable, true);
         ASSERT_EQ(evt.status.caps, caps);

         RcsCapabilityStatus status;
         ASSERT_EQ(bob.capability->getContactCapabilityStatus(bob.handle, alice.config.uri(), status), RcsCapabilityDiscoveryManager::kCompletedSynchronously);
         ASSERT_EQ(status.targetAddress, alice.config.uri());
         ASSERT_EQ(status.isRcsCapable, true);
         ASSERT_EQ(status.caps, caps);
      }
   });

   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
}

// test a basic capability discovery sent from alice to bob
TEST_F(CapabilityDiscoveryModuleTest, BasicCapabilityDiscoveryNotAContact) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   auto aliceEvents = std::async(std::launch::async, [&] () {
      { // expect success sending
         RcsCapabilityStatus status;
         ASSERT_EQ(alice.capability->getContactCapabilityStatus(alice.handle, bob.config.uri(), status), kError);
      }
   });

   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
}
#endif

#if 1 // bliu: to be done as bob's thread is not running
// test a basic capability discovery sent from alice to bob
TEST_F(CapabilityDiscoveryModuleTest, NormalCapabilityDiscoveryNotAContact) {
   TestAccount alice("alice");
   TestAccount bob("bob");
   auto caps = _GetAllCaps();
   bob.capability->setMyCapabilities(bob.handle, caps);
   alice.capability->addContact(alice.handle, bob.config.uri());

   auto aliceEvents = std::async(std::launch::async, [&] () {
      SipAccountHandle hdl;
      { // expect success sending
         RcsOnContactCapabilityStatusChangedEvent evt;
         ASSERT_TRUE(alice.capabilityEvents->expectEvent( "RcsCapabilityDiscoveryHandler::onContactCapabilityStatusChanged",
            15000, HandleEqualsPred<SipAccountHandle>(alice.handle), hdl, evt));
         ASSERT_EQ(alice.handle, hdl);
         ASSERT_EQ(evt.status.targetAddress, bob.config.uri());
         ASSERT_EQ(evt.signalingStatusCode, 200);
         ASSERT_EQ(evt.status.isRcsCapable, true);
         ASSERT_EQ(evt.status.caps, caps);

         RcsCapabilityStatus status;
         ASSERT_EQ(alice.capability->getContactCapabilityStatus(alice.handle, bob.config.uri(), status), RcsCapabilityDiscoveryManager::kCompletedSynchronously);
         ASSERT_EQ(status.targetAddress, bob.config.uri());
         ASSERT_EQ(status.isRcsCapable, true);
         ASSERT_EQ(status.caps, caps);
      }
   });

   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
}
#endif

#if 1 // bliu: to be done as bob's thread is not running
// test a basic capability discovery sent from alice to bob and coco
TEST_F(CapabilityDiscoveryModuleTest, NormalCapabilityDiscoveryMultipleContacts) {
   TestAccount alice("alice");
   TestAccount bob("bob");
   TestAccount coco("coco");
   auto caps = _GetAllCaps();
   bob.capability->setMyCapabilities(bob.handle, caps);
   alice.capability->addContact(alice.handle, bob.config.uri());
   alice.capability->addContact(alice.handle, coco.config.uri());

   auto aliceEvents = std::async(std::launch::async, [&] () {
      SipAccountHandle hdl;
      { // expect success sending
         RcsOnContactCapabilityStatusChangedEvent evt;
         ASSERT_TRUE(alice.capabilityEvents->expectEvent( "RcsCapabilityDiscoveryHandler::onContactCapabilityStatusChanged",
            15000, HandleEqualsPred<SipAccountHandle>(alice.handle), hdl, evt));
         ASSERT_EQ(alice.handle, hdl);
         ASSERT_EQ(evt.status.targetAddress, bob.config.uri());
         ASSERT_EQ(evt.signalingStatusCode, 200);
         ASSERT_EQ(evt.status.isRcsCapable, true);
         ASSERT_EQ(evt.status.caps, caps);

         RcsCapabilityStatus status;
         ASSERT_EQ(alice.capability->getContactCapabilityStatus(alice.handle, bob.config.uri(), status), RcsCapabilityDiscoveryManager::kCompletedSynchronously);
         ASSERT_EQ(status.targetAddress, bob.config.uri());
         ASSERT_EQ(status.isRcsCapable, true);
         ASSERT_EQ(status.caps, caps);

         ASSERT_TRUE(alice.capabilityEvents->expectEvent( "RcsCapabilityDiscoveryHandler::onContactCapabilityStatusChanged",
            15000, HandleEqualsPred<SipAccountHandle>(alice.handle), hdl, evt));
         ASSERT_EQ(alice.handle, hdl);
         ASSERT_EQ(evt.status.targetAddress, coco.config.uri());
         ASSERT_EQ(evt.signalingStatusCode, 200);
         ASSERT_EQ(evt.status.isRcsCapable, false);
         ASSERT_TRUE(evt.status.caps.empty());

         ASSERT_EQ(alice.capability->getContactCapabilityStatus(alice.handle, coco.config.uri(), status), RcsCapabilityDiscoveryManager::kCompletedSynchronously);
         ASSERT_EQ(status.targetAddress, coco.config.uri());
         ASSERT_EQ(status.isRcsCapable, false);
         ASSERT_TRUE(status.caps.empty());
      }
   });

   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
}
#endif

#if 0 // bliu: to be done as bob's thread is not running
// test a basic capability discovery sent from alice to bob
TEST_F(CapabilityDiscoveryModuleTest, NormalCapabilityDiscoveryTimeout) {
   TestAccount alice("alice");
   TestAccount bob("bob");
   auto caps = _GetAllCaps();
   alice.capability->setMyCapabilities(alice.handle, caps);
   alice.capability->addContact(alice.handle, bob.config.uri());

   auto aliceEvents = std::async(std::launch::async, [&] () {
      { // expect success sending
         RcsOnContactCapabilityStatusChangedEvent evt;
         SipAccountHandle hdl;
         RcsCapabilityStatus status;

         ASSERT_TRUE(alice.capabilityEvents->expectEvent( "RcsCapabilityDiscoveryHandler::onContactCapabilityStatusChanged",
            15000, HandleEqualsPred<SipAccountHandle>(alice.handle), hdl, evt));
         ASSERT_EQ(alice.handle, hdl);
         ASSERT_EQ(evt.status.targetAddress, bob.config.uri());
         ASSERT_EQ(evt.signalingStatusCode, 404);
         ASSERT_EQ(evt.status.isRcsCapable, false);
         ASSERT_TRUE(evt.status.caps.empty());

         ASSERT_EQ(alice.capability->getContactCapabilityStatus(alice.handle, bob.config.uri(), status), RcsCapabilityDiscoveryManager::kCompletedSynchronously);
         ASSERT_EQ(status.targetAddress, bob.config.uri());
         ASSERT_EQ(status.isRcsCapable, false);
         ASSERT_TRUE(status.caps.empty());

         ASSERT_TRUE(alice.capabilityEvents->expectEvent( "RcsCapabilityDiscoveryHandler::onContactCapabilityStatusChanged",
            15000, HandleEqualsPred<SipAccountHandle>(alice.handle), hdl, evt));
         ASSERT_EQ(alice.handle, hdl);
         ASSERT_EQ(evt.status.targetAddress, bob.config.uri());
         ASSERT_EQ(evt.signalingStatusCode, 404);
         ASSERT_EQ(evt.status.isRcsCapable, false);
         ASSERT_TRUE(evt.status.caps.empty());

         ASSERT_EQ(alice.capability->getContactCapabilityStatus(alice.handle, bob.config.uri(), status), RcsCapabilityDiscoveryManager::kCompletedSynchronously);
         ASSERT_EQ(status.targetAddress, bob.config.uri());
         ASSERT_EQ(status.isRcsCapable, false);
         ASSERT_TRUE(status.caps.empty());
      }
   });

   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
}
#endif

#if 1
// test a basic capability discovery sent from alice to bob
TEST_F(CapabilityDiscoveryModuleTest, DISABLED_NormalCapabilityDiscoveryAddAndRemoveContact) {
   TestAccount alice("alice");
   TestAccount bob("bob");
   TestAccount coco("coco");
   alice.capability->addContact(alice.handle, bob.config.uri());
   alice.capability->addContact(alice.handle, coco.config.uri());
   alice.capability->removeContact(alice.handle, bob.config.uri());
   alice.capability->addContact(alice.handle, bob.config.uri());

   auto aliceEvents = std::async(std::launch::async, [&] () {
      SipAccountHandle hdl;
      { // expect success sending
         RcsOnContactCapabilityStatusChangedEvent evt;
         ASSERT_TRUE(alice.capabilityEvents->expectEvent( "RcsCapabilityDiscoveryHandler::onContactCapabilityStatusChanged",
            15000, HandleEqualsPred<SipAccountHandle>(alice.handle), hdl, evt));
         ASSERT_EQ(alice.handle, hdl);
         ASSERT_EQ(evt.status.targetAddress, bob.config.uri());
         ASSERT_EQ(evt.signalingStatusCode, 200);
         ASSERT_EQ(evt.status.isRcsCapable, false);
         ASSERT_TRUE(evt.status.caps.empty());

         RcsCapabilityStatus status;
         ASSERT_EQ(alice.capability->getContactCapabilityStatus(alice.handle, bob.config.uri(), status), RcsCapabilityDiscoveryManager::kCompletedSynchronously);
         ASSERT_EQ(status.targetAddress, bob.config.uri());
         ASSERT_EQ(status.isRcsCapable, false);
         ASSERT_TRUE(status.caps.empty());
      }
   });

   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
}
#endif

#if 0
// test a basic capability discovery sent from alice to bob
TEST_F(CapabilityDiscoveryModuleTest, CapabilityDiscoveryInvalidContact) {
   TestAccount alice("alice");

   for (int i = 0; i < 10; ++i)
   {
      auto bob_uri = "sip:bob" + std::to_wstring(i) + "@demo.xten.com";
   alice.capability->addContact(alice.handle, bob_uri);
   }

   auto aliceEvents = std::async(std::launch::async, [&] () {
      SipAccountHandle hdl;
      { // expect success sending

         while (true) {
         RcsOnContactCapabilityStatusChangedEvent evt;
         ASSERT_TRUE(alice.capabilityEvents->expectEvent( "RcsCapabilityDiscoveryHandler::onContactCapabilityStatusChanged",
            150000, HandleEqualsPred<SipAccountHandle>(alice.handle), hdl, evt));
         ASSERT_EQ(alice.handle, hdl);
         ASSERT_EQ(evt.signalingStatusCode, 404);
         ASSERT_EQ(evt.status.isRcsCapable, false);
         ASSERT_TRUE(evt.status.caps.empty());
         }
      }
   });

   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(1500000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
}
#endif

#if 0
// test a basic capability discovery sent from alice to bob
TEST_F(CapabilityDiscoveryModuleTest, CapabilityDiscoveryInvalidContact) {
   TestAccount alice("alice");
   auto caps = _GetAllCaps();
   alice.capability->setMyCapabilities(alice.handle, caps);

   for (int i = 0; i < 3; ++i)
   {
      auto ptr = new TestAccount(("bob" + std::to_string(i)).c_str());
      alice.capability->addContact(alice.handle, ptr->uri);
   }

   auto aliceEvents = std::async(std::launch::async, [&] () {
      SipAccountHandle hdl;
      { // expect success sending

         while (true) {
         RcsOnContactCapabilityStatusChangedEvent evt;
         ASSERT_TRUE(alice.capabilityEvents->expectEvent( "RcsCapabilityDiscoveryHandler::onContactCapabilityStatusChanged",
            150000, HandleEqualsPred<SipAccountHandle>(alice.handle), hdl, evt));
         ASSERT_EQ(alice.handle, hdl);
         ASSERT_EQ(evt.signalingStatusCode, 200);
         ASSERT_EQ(evt.status.isRcsCapable, false);
         //ASSERT_TRUE(evt.status.caps.empty());
         }
      }
   });

   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(1500000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
}
#endif

#if 1
// test import functionalities
TEST_F(CapabilityDiscoveryModuleTest, CapabilityDiscoveryImportContact) {
   TestAccount alice("alice");
   TestAccount bob("bob");
   TestAccount coco("coco");

   alice.capability->addContact(alice.handle, bob.config.uri());
   alice.capability->addContact(alice.handle, bob.config.uri());
   alice.capability->removeContact(alice.handle, coco.config.uri());

   cpc::vector<RcsCapabilityStatus> cache;
   RcsCapabilityStatus status;
   status.isRcsCapable = true;
   status.lastUpdated = std::chrono::system_clock::to_time_t(std::chrono::system_clock::now() - std::chrono::seconds(5));
   status.targetAddress = bob.config.uri();
   cache.push_back(status);
   status.isRcsCapable = true;
   status.lastUpdated = 0;
   status.targetAddress = coco.config.uri();
   cache.push_back(status);
   alice.capability->importToCache(alice.handle, cache);

   ASSERT_EQ(alice.capability->getContactCapabilityStatus(alice.handle, bob.config.uri(), status), RcsCapabilityDiscoveryManager::kCompletedSynchronously);
   ASSERT_EQ(status.targetAddress, bob.config.uri());
   ASSERT_EQ(status.isRcsCapable, true);
   ASSERT_TRUE(status.caps.empty());

   ASSERT_EQ(alice.capability->getContactCapabilityStatus(alice.handle, coco.config.uri(), status), RcsCapabilityDiscoveryManager::kCompletedSynchronously);
}

// test import and export functionalities
TEST_F(CapabilityDiscoveryModuleTest, CapabilityDiscoveryImportExportContact) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   cpc::vector<RcsCapabilityStatus> cache;
   RcsCapabilityStatus status;
   status.isRcsCapable = true;
   status.lastUpdated = 0;
   status.targetAddress = bob.config.uri();
   cache.push_back(status);
   alice.capability->importToCache(alice.handle, cache);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      SipAccountHandle hdl;
      { // expect success sending
         RcsOnContactCapabilityStatusChangedEvent evt;
         ASSERT_TRUE(alice.capabilityEvents->expectEvent( "RcsCapabilityDiscoveryHandler::onContactCapabilityStatusChanged",
            15000, HandleEqualsPred<SipAccountHandle>(alice.handle), hdl, evt));
         ASSERT_EQ(alice.handle, hdl);
         ASSERT_EQ(evt.status.targetAddress, bob.config.uri());
         ASSERT_EQ(evt.signalingStatusCode, 200);
         ASSERT_EQ(evt.status.isRcsCapable, false);
         ASSERT_TRUE(evt.status.caps.empty());

         ASSERT_EQ(alice.capability->getContactCapabilityStatus(alice.handle, bob.config.uri(), status), RcsCapabilityDiscoveryManager::kCompletedSynchronously);
         ASSERT_EQ(status.targetAddress, bob.config.uri());
         ASSERT_EQ(status.isRcsCapable, false);
         ASSERT_TRUE(status.caps.empty());

         cache.clear();
         ASSERT_EQ(alice.capability->exportFromCache(alice.handle, cache), kSuccess);
         ASSERT_EQ(cache.size(), 1);
         ASSERT_EQ(cache[0].targetAddress, bob.config.uri());
         ASSERT_EQ(cache[0].isRcsCapable, false);
         ASSERT_TRUE(cache[0].caps.empty());

         auto now = std::chrono::system_clock::from_time_t(std::chrono::system_clock::to_time_t(std::chrono::system_clock::now()));
         auto lastUpdated = std::chrono::system_clock::from_time_t(cache[0].lastUpdated);
         ASSERT_TRUE(lastUpdated <= now);
         ASSERT_TRUE(lastUpdated >= now - std::chrono::seconds(20));
      }
   });

   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
}
#endif

#if 1
// test synchronization functionalities
TEST_F(CapabilityDiscoveryModuleTest, CapabilityDiscoverySychronizeContact) {
   TestAccount alice("alice");
   TestAccount bob("bob");
   auto aliceEvents = std::async(std::launch::async, [&] () {
      SipAccountHandle hdl;
      { // expect success sending
         cpc::vector<cpc::string> contacts;
         contacts.push_back(bob.config.uri());

         alice.capability->synchronizeAllContacts(alice.handle, contacts);

         RcsOnContactCapabilityStatusChangedEvent evt;
         ASSERT_TRUE(alice.capabilityEvents->expectEvent( "RcsCapabilityDiscoveryHandler::onContactCapabilityStatusChanged",
            15000, HandleEqualsPred<SipAccountHandle>(alice.handle), hdl, evt));
         ASSERT_EQ(alice.handle, hdl);
         ASSERT_EQ(evt.status.targetAddress, bob.config.uri());
         ASSERT_EQ(evt.signalingStatusCode, 200);
         ASSERT_EQ(evt.status.isRcsCapable, false);
         ASSERT_TRUE(evt.status.caps.empty());

         RcsCapabilityStatus status;
         ASSERT_EQ(alice.capability->getContactCapabilityStatus(alice.handle, bob.config.uri(), status), RcsCapabilityDiscoveryManager::kCompletedSynchronously);
         ASSERT_EQ(status.targetAddress, bob.config.uri());
         ASSERT_EQ(status.isRcsCapable, false);
         ASSERT_TRUE(status.caps.empty());

         cpc::vector<RcsCapabilityStatus> cache;
         ASSERT_EQ(alice.capability->exportFromCache(alice.handle, cache), kSuccess);
         ASSERT_EQ(cache.size(), 1);
         ASSERT_EQ(cache[0].targetAddress, bob.config.uri());
         ASSERT_EQ(cache[0].isRcsCapable, false);
         ASSERT_TRUE(cache[0].caps.empty());

         auto now = std::chrono::system_clock::from_time_t(std::chrono::system_clock::to_time_t(std::chrono::system_clock::now()));
         auto lastUpdated = std::chrono::system_clock::from_time_t(cache[0].lastUpdated);
         ASSERT_TRUE(lastUpdated <= now);
         ASSERT_TRUE(lastUpdated >= now - std::chrono::seconds(20));

         contacts.clear();
         alice.capability->synchronizeAllContacts(alice.handle, contacts);

         ASSERT_EQ(alice.capability->getContactCapabilityStatus(alice.handle, bob.config.uri(), status), kError);
      }
   });

   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
}
#endif

#if 1
// test rapid request where addContact is immediately followed by getContactCapabilityStatus
// getContactCapabilityStatus shouldn't trigger onContactCapabilityStatusChanged at all
TEST_F(CapabilityDiscoveryModuleTest, DISABLED_CapabilityDiscoveryRapidRequest) {
   TestAccount alice("alice");
   TestAccount bob("bob");
   alice.capability->addContact(alice.handle, bob.config.uri());

   auto aliceEvents = std::async(std::launch::async, [&] () {
      SipAccountHandle hdl;
      { // expect success sending
         RcsOnContactCapabilityStatusChangedEvent evt;
         ASSERT_TRUE(alice.capabilityEvents->expectEvent( "RcsCapabilityDiscoveryHandler::onContactCapabilityStatusChanged",
            15000, HandleEqualsPred<SipAccountHandle>(alice.handle), hdl, evt));
         ASSERT_EQ(alice.handle, hdl);
         ASSERT_EQ(evt.status.targetAddress, bob.config.uri());
         ASSERT_EQ(evt.signalingStatusCode, 200);
         ASSERT_EQ(evt.status.isRcsCapable, false);
         ASSERT_TRUE(evt.status.caps.empty());

         RcsCapabilityStatus status;
         ASSERT_EQ(alice.capability->getContactCapabilityStatus(alice.handle, bob.config.uri(), status), RcsCapabilityDiscoveryManager::kCompletedSynchronously);
         ASSERT_EQ(status.targetAddress, bob.config.uri());
         ASSERT_EQ(status.isRcsCapable, false);
         ASSERT_TRUE(status.caps.empty());

         ASSERT_FALSE(alice.capabilityEvents->expectEvent( "RcsCapabilityDiscoveryHandler::onContactCapabilityStatusChanged",
            10000, HandleEqualsPred<SipAccountHandle>(alice.handle), hdl, evt));

         ASSERT_EQ(alice.capability->getContactCapabilityStatus(alice.handle, bob.config.uri(), status), RcsCapabilityDiscoveryManager::kCompletedSynchronously);

         ASSERT_TRUE(alice.capabilityEvents->expectEvent( "RcsCapabilityDiscoveryHandler::onContactCapabilityStatusChanged",
            10000, HandleEqualsPred<SipAccountHandle>(alice.handle), hdl, evt));
      }
   });

   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
}
#endif


TEST_F(CapabilityDiscoveryModuleTest, CapabilityDiscoveryRemoveHandler) {
   Phone* phone = Phone::create();
   phone->initialize(LicenseInfo(), (PhoneHandler*)NULL);
   CPCAPI2::SipAccount::SipAccountManager* acct = CPCAPI2::SipAccount::SipAccountManager::getInterface(phone);
   
   TestAccountConfig config("alice");
   CPCAPI2::SipAccount::SipAccountHandle accountHandle = acct->create(config.settings);
   
   class MyRcsCapabilityDiscoveryHandler : public RcsCapabilityDiscoveryHandler
   {
   public:
      MyRcsCapabilityDiscoveryHandler() : receivedEvent(false) {}
      
      bool receivedEvent;
      int onContactCapabilityStatusChanged(CPCAPI2::SipAccount::SipAccountHandle account, const RcsOnContactCapabilityStatusChangedEvent& args)
      {
         receivedEvent = true; return kSuccess;
      }
   };
   std::unique_ptr<MyRcsCapabilityDiscoveryHandler> rcsHandler(new MyRcsCapabilityDiscoveryHandler());
   
   RcsCapabilityDiscoveryManager* capDiscMgr = RcsCapabilityDiscoveryManager::getInterface(phone);
   ASSERT_EQ(kSuccess, capDiscMgr->setHandler(accountHandle, rcsHandler.get()));

   class MySipAccountHandler : public SipAccountHandler {
   public:
      bool registered = false;
      MySipAccountHandler() {}
      int onAccountStatusChanged(CPCAPI2::SipAccount::SipAccountHandle account, const SipAccountStatusChangedEvent& args)
      {
         if (args.accountStatus == SipAccountStatusChangedEvent::Status_Registered)
         {
            registered = true;
         }
         return kSuccess;
      }
      int onError(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::ErrorEvent& args){ return kSuccess; }
   };
   std::unique_ptr<MySipAccountHandler> accountHandler(new MySipAccountHandler);
   
   ASSERT_EQ(kSuccess, acct->setHandler(accountHandle, accountHandler.get()));
   ASSERT_EQ(kSuccess, acct->enable(accountHandle));
   
   std::atomic_bool threadStopFlag(false);
   auto acctEnableEvent = std::async(std::launch::async, [&] ()
   {
      while (accountHandler->registered == false)
      {
         phone->process(CPCAPI2::Phone::kBlockingModeNonBlocking);

         if (threadStopFlag) return;

         std::this_thread::sleep_for(std::chrono::milliseconds(100));
      }
   });
   flaggableWaitFor(acctEnableEvent, threadStopFlag);
   
   ASSERT_EQ(kSuccess, capDiscMgr->addContact(accountHandle, "sip:bogus238942398"));
   auto start = std::chrono::high_resolution_clock::now();
   
   threadStopFlag = false;
   auto discoveryEvent = std::async(std::launch::async, [&] ()
   {
     while (rcsHandler->receivedEvent == false)
     {
        phone->process(CPCAPI2::Phone::kBlockingModeNonBlocking);

        if (threadStopFlag) return;

        std::this_thread::sleep_for(std::chrono::milliseconds(100));
     }
   });

   flaggableWaitFor(discoveryEvent, threadStopFlag);
   auto end = std::chrono::high_resolution_clock::now();
   
   ASSERT_EQ(kSuccess, capDiscMgr->setHandler(accountHandle, NULL));
   rcsHandler.reset();
   
   // RcsCapabilityDiscoveryManager does not use reactor queue for setHandler, but addContact does..
   // need a better way of waiting until setHandler finishes, or RcsCapabilityDiscoveryManager could
   // change
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
   
   ASSERT_EQ(kSuccess, capDiscMgr->addContact(accountHandle, "sip:2bogus28323238"));
   
   // wait about as long as it took before
   std::this_thread::sleep_for((end-start) * 2);
   
   phone->process(CPCAPI2::Phone::kBlockingModeNonBlocking);
   
   Phone::release(phone);
}
   
}

#endif // CPCAPI2_CAPABILITY_DISCOVERY_MODULE
