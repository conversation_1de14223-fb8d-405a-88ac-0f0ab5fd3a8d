#pragma once

#ifndef CPCAPI2_TEST_FIXTURE_H
#define CPCAPI2_TEST_FIXTURE_H

#include "brand_branded.h"
#include <stdlib.h>
#include <gtest/gtest.h>
#include <cpcapi2.h>
#include <rutil/Random.hxx>
#include "cpcapi2utils.h"
#include "interface/experimental/phone/PhoneInternal.h"
#include "impl/phone/NetworkChangeManager_Mock.h"
#include "impl/account/SipAccountHandlerInternal.h"
#include "impl/ptt/PushToTalkHandlerInternal.h"
#include "impl/util/FileDescriptorMonitor.h"
#include "interface/experimental/remotesync/RemoteSyncJsonProxy.h"
#include "interface/experimental/push_endpoint/PushNotificationEndpointJsonProxy.h"
#include "interface/experimental/media/MediaManagerInternal.h"
#include "interface/experimental/pcap/PcapManager.h"
#include "interface/experimental/ptt/PushToTalkManager.h"
#include "interface/experimental/cloudrelayconnector/CloudRelayConnector.h"
#include "interface/experimental/confbridge/ConferenceRegistrar.h"
#include "interface/experimental/confbridge/ConferenceBridgeManager.h"
#include "impl/confbridge/ConferenceBridgeHandlerInternal.h"
#include "test_framework/cpcapi2_test_framework.h"
#include "test_framework/bare_bones_sip_endpoint.h"
#include "alianza_api/interface/public/alianza_api_manager.h"
#include "sipua_alianza_api_test_fixture.h"
#include "TestVideoHelper.h"
#include "ReproRunner.h"
#include "test_framework/single_ownable.h"

#include <thread>
#include <future>
#include <fstream>
#include <iomanip>
#include <filesystem>

#define CPCAPI2_TEST_FIXTURE_TIMEOUT 60000


#define flaggableWaitFor(thread1, timeoutFlag) \
{ \
   std::future_status fs = thread1.wait_for(std::chrono::milliseconds(CPCAPI2_TEST_FIXTURE_TIMEOUT)); \
   if (fs != std::future_status::ready) \
   { \
      timeoutFlag = true; \
   } \
   ASSERT_EQ(fs, std::future_status::ready); \
   ASSERT_NO_THROW(thread1.get()); \
}


template<typename T1>
void noThrowCheck(std::future<T1>& thread1)
{
   // this needs to go in its own actual function otherwise defining using ASSERT_NO_THROW in the same
   // macro will result in compilation errors
   ASSERT_NO_THROW(thread1.get());
}


// ! note: the reason waitFor is a macro is ASSER_* functions returns on assertion faliure, which is important for stopping
// execution of the calling function, to stop the current test (or thread). i.e. if waitFor was instead a funciton, only that functino would return

#define waitForMs(thread1, timeoutMs) \
{ \
   ASSERT_EQ(thread1.wait_for(timeoutMs), std::future_status::ready); \
   noThrowCheck(thread1); \
}

#define waitFor(thread1) waitForMs(thread1, std::chrono::milliseconds(CPCAPI2_TEST_FIXTURE_TIMEOUT))

#define waitFor2Ms(thread1, thread2, timeoutMs) \
{ \
   ASSERT_EQ(thread1.wait_for(timeoutMs), std::future_status::ready); \
   noThrowCheck(thread1); \
   ASSERT_EQ(thread2.wait_for(timeoutMs), std::future_status::ready); \
   noThrowCheck(thread2); \
}

#define waitFor2(thread1, thread2) waitFor2Ms(thread1, thread2, std::chrono::milliseconds(CPCAPI2_TEST_FIXTURE_TIMEOUT))

#define noReturnWaitFor2Ms(thread1, thread2, timeoutMs) \
{ \
   EXPECT_EQ(thread1.wait_for(timeoutMs), std::future_status::ready); \
   noThrowCheck(thread1); \
   EXPECT_EQ(thread2.wait_for(timeoutMs), std::future_status::ready); \
   noThrowCheck(thread2); \
}

#define noReturnWaitFor2(thread1, thread2) noReturnWaitFor2Ms(thread1, thread2, std::chrono::milliseconds(CPCAPI2_TEST_FIXTURE_TIMEOUT))

#define waitFor3Ms(thread1, thread2, thread3, timeoutMs) \
{ \
   ASSERT_EQ(thread1.wait_for(timeoutMs), std::future_status::ready); \
   noThrowCheck(thread1); \
   ASSERT_EQ(thread2.wait_for(timeoutMs), std::future_status::ready); \
   noThrowCheck(thread2); \
   ASSERT_EQ(thread3.wait_for(timeoutMs), std::future_status::ready); \
   noThrowCheck(thread3); \
}

#define waitFor3(thread1, thread2, thread3) waitFor3Ms(thread1, thread2, thread3, std::chrono::milliseconds(CPCAPI2_TEST_FIXTURE_TIMEOUT))

#define waitFor4Ms(thread1, thread2, thread3, thread4, timeoutMs) \
{ \
   ASSERT_EQ(thread1.wait_for(timeoutMs), std::future_status::ready); \
   noThrowCheck(thread1); \
   ASSERT_EQ(thread2.wait_for(timeoutMs), std::future_status::ready); \
   noThrowCheck(thread2); \
   ASSERT_EQ(thread3.wait_for(timeoutMs), std::future_status::ready); \
   noThrowCheck(thread3); \
   ASSERT_EQ(thread4.wait_for(timeoutMs), std::future_status::ready); \
   noThrowCheck(thread4); \
}

#define waitFor4(thread1, thread2, thread3, thread4) waitFor4Ms(thread1, thread2, thread3, thread4, std::chrono::milliseconds(CPCAPI2_TEST_FIXTURE_TIMEOUT))

#define waitFor5Ms(thread1, thread2, thread3, thread4, thread5, timeoutMs) \
{ \
   ASSERT_EQ(thread1.wait_for(timeoutMs), std::future_status::ready); \
   noThrowCheck(thread1); \
   ASSERT_EQ(thread2.wait_for(timeoutMs), std::future_status::ready); \
   noThrowCheck(thread2); \
   ASSERT_EQ(thread3.wait_for(timeoutMs), std::future_status::ready); \
   noThrowCheck(thread3); \
   ASSERT_EQ(thread4.wait_for(timeoutMs), std::future_status::ready); \
   noThrowCheck(thread4); \
   ASSERT_EQ(thread5.wait_for(timeoutMs), std::future_status::ready); \
   noThrowCheck(thread5); \
}

#define waitFor5(thread1, thread2, thread3, thread4, thread5) waitFor5Ms(thread1, thread2, thread3, thread4, thread5, std::chrono::milliseconds(CPCAPI2_TEST_FIXTURE_TIMEOUT))

#define assertSuccess(x) ASSERT_EQ(kSuccess, x)

namespace CPCAPI2
{
namespace test
{
   class AndroidBackgroundingBookkeeper;
   class AlianzaApiManager;
}
}

class CpcapiAutoTestBase
{

public:

   CpcapiAutoTestBase();
   virtual ~CpcapiAutoTestBase();

   virtual void start();
   virtual void end();
   virtual bool checkFdLeaks(CPCAPI2::Utils::FileDescriptorsCheckpoint& check);

   std::unique_ptr<CPCAPI2::Utils::FileDescriptorsCheckpoint> fd;
   bool fdCheckEnabled;

};

class CpcapiAutoTest : public ::testing::Test,
                       public CpcapiAutoTestBase
{

public:

   CpcapiAutoTest() {}
   virtual ~CpcapiAutoTest() {}
   virtual void SetUp() OVERRIDE { start(); }
   virtual void TearDown() OVERRIDE { end(); }

};

template <typename T>
class CpcapiAutoTestWithParam : public ::testing::TestWithParam<T>,
                                public CpcapiAutoTestBase
{

public:

   virtual void SetUp() OVERRIDE { start(); }
   virtual void TearDown() OVERRIDE { end(); }

};

class TestEnvironmentConfig
{
public:
   static bool pauseAfterTests();
   static bool includeAudioQualityTests();
   static bool isCpInternalNetwork();
   static bool saveLoggingToFile();
   static bool logToConsole();
   static bool redirectCoutToFile(); // largely for Android
   static bool pageHeap();
   static bool saveCrashdumpsToFile();
   static bool fullMemoryCrashdumps();
   static bool testTimerWatchdog();
   static bool modifyPowerSchemeAsNeeded();
   static bool drawLocalVideo();
   static void setDrawLocalVideo(bool draw);
   static bool forceDummyAudioDevice();
   static CPCAPI2::Media::VideoCaptureResolution defaultVideoRes();
   static bool useRandomXmppUsernames();
   static bool autoCreateXmppUsers();
   static bool disableXmppSelfPresenceCheck();
   static bool disableXmppCompression();
   static bool logXmppTlsEncryptionKey();
   static bool disableDtlsByDefault();
   static bool dockerContainerized();
   static bool macLoopbackAliasesSet();
   static cpc::string loggingFilePath();
   // where to save potentially useful output files from tests (e.g. recorded WAV files)
   static std::filesystem::path artifactFilePath();
   static unsigned int openfireRestApiServerPort();
   static cpc::string openfireRestApiKey();
   static cpc::string defaultXmppServer();
   static unsigned int defaultXmppServerPort();
   static cpc::string defaultXmppServerProxy();
   static cpc::string defaultXcapServer();
   // returns an IP address which should result in no response; ICMP or otherwise.
   // useful for simulating e.g. unresponsive SIP server or DNS server
   static cpc::string unreachableV4Ip();
   static cpc::string unreachableV6Ip();

   static void setLogFileName(const cpc::string& logFileName);
   static cpc::string logFileName();

   static void setCrashDumpFileName(const cpc::string& crashDumpFileName);
   static cpc::string crashDumpFileName();

   static bool getResourceFile(const std::string configFileName, std::string& configFile);
   static cpc::string testResourcePath();
   static cpc::string tempPath();

   static cpc::string pcapCaptureTestCases();
   static bool pcapCapture() { return pcapCaptureTestCases().size() > 0; }

   static cpc::string logcatServerIp();
   static unsigned int logcatServerPort();

   static cpc::string expectedAutoTestsVersion();

   static const std::vector<std::string>& commandLineArgs();
   static void setCommandLineArgs(const std::vector<std::string>& args);

   static cpc::string testEnvironmentId();
   
   
   // for large CPE tests
   enum class CpeTestLevel
   {
      Exhaustive = 0, // Includes tests that e.g. only validate REST API calls to CPE2 work for creating accounts.
                      // Given much of this functionality is exercised during normal test runs, these additional tests
                      // are largely redundant.
      Standard = 1,   // Runs all standard tests against CPE2 (e.g. SIP registration, calling, etc). Suitable for running against CPE2 QA, Beta environments
      Production = 2  // Runs only those tests against CPE2 that are deemed valid for running in production (e.g. basic call)
   };

   static CpeTestLevel cpeTestLevel();

private:
   static cpc::string mLogFileName;
   static cpc::string mCrashDumpFileName;

   // Environment-Id for the different sipua test environments
   //    - repro (internal sdk proxy by default)
   //    - d2, q2, b2, p2 (for CPE1)
   //    - dev, qa, beta, prod (for CPE2)
   static cpc::string mTestEnvironmentId;
};

struct PhoneShutdownInfo
{
   std::unique_ptr<std::condition_variable> cv;
   std::unique_ptr<std::mutex> mutex;
   bool shutdown; // already shut down successfully
};

struct TestAccountConfig
{
   TestAccountConfig(const std::string& name);

   std::string license;
   std::string licenseDocumentLocation;
   std::string licenseAor;
   cpc::string name;
   CPCAPI2::SipAccount::SipAccountSettings settings;
   CPCAPI2::RemoteSync::RemoteSyncSettings remoteSyncSettings;
   CPCAPI2::XCAP::XcapSettings xcapSettings;
   CPCAPI2::Media::MediaStackSettings mediaSettings;
   cpc::string uri();
   bool registerHandlers;
   static TestAccountConfig makeSecureConfig(const std::string& name);
   static cpc::string defaultDnsServer(
      CPCAPI2::SipAccount::IpVersion ipversion = CPCAPI2::SipAccount::IpVersion::IpVersion_V4);
   bool disableAnalyticsModuleAutoTestEventHandling;
   bool disableStrettoUemModuleAutoTestEventHandling;
   bool useFileAudioDevice;
   bool dtlsSupported;
   CPCAPI2::ConnectionPreferences phoneInitConnectionPrefs;
   bool useAAudio; // Android specific
   AlianzaAccountConfig alianzaConfig;
   AlianzaSessionInfo alianzaSession;

private:
   // use TestAccountConfig(const std::string&) instead
   TestAccountConfig(); // Blocked
};

enum TestAccountInitMode
{
   Account_NoInit,
   Account_Init,
   Account_Enable
};

class AutoTestsLocalLogger;

enum TestAccountType
{
   TestAccountType_SIP,
   TestAccountType_XMPP
};

class TestAccountBase
{
public:
   TestAccountBase(TestAccountType accountType_) : accountType(accountType_) {}
   virtual~ TestAccountBase() {}
   TestAccountType getType() { return accountType; }
protected:
   TestAccountBase() : accountType(TestAccountType_SIP) {}
   TestAccountType accountType;
};

class TestAccount : public TestAccountBase
{
public:
   struct TestAccountOptions
   {
      bool disableOnDestruct;
      CPCAPI2::Phone* phone;
      CPCAPI2::Media::MediaTransportsReactorFactory* mediaReactorFactory;
      bool initSlave;
      CPCAPI2::ConnectionPreferences::NetworkChangeManagerType networkChangeManagerType;

      TestAccountOptions()
      {
         disableOnDestruct = true;
         phone = NULL;
         mediaReactorFactory = NULL;
         initSlave = false;
         networkChangeManagerType = CPCAPI2::ConnectionPreferences::NetworkChangeManagerType_Mock;
      }
   };

   TestAccount(const std::string& name, TestAccountInitMode initMode=Account_Enable, bool disableOnDestruct=true, CPCAPI2::Phone* phone=NULL, CPCAPI2::Media::MediaTransportsReactorFactory* mediaReactorFactory=NULL,
      bool initSlave = false, bool useVideoHelper = true);
   virtual ~TestAccount();

   virtual void init(CPCAPI2::PhoneHandler* phoneHandler = NULL, CPCAPI2::NetworkTransport transport = CPCAPI2::TransportWiFi);
   void shutdown(int timeoutSeconds=10, bool skipOpenSslCheck=false);
   void enable(bool assertRegistrationState = true);
   void disable(bool force = false, bool assertRegistrationState = true, bool destroyBackendAccount = true);
   void enableOnlyThisCodec(const resip::Data& codecName);
   void enableOnlyThisVideoCodec(const resip::Data& codecName);
   void enableCodec(const resip::Data& codecName);
   void disableCodec(const resip::Data& codecName);
   void disableAllAudioCodecs();
   void setCodecPriority(const resip::Data& codecName, unsigned int priority);
   void setCustomVideoSourceDeviceCapture();
   void setManyCamCapture();
   int setCaptureDeviceMatching(const std::string& friendlyNameSubstring);
   bool didShutdownTimeout()  { return shutdownTimedout; }
   static void onPhoneRelease(void* p);
   void initiateVideo(bool createLocalWindow = true, bool createIncomingWindow = true);
   void setupRecorderHandler(CPCAPI2::Recording::RecorderHandle handle);
   void cleanupRecorderHandler(CPCAPI2::Recording::RecorderHandle handle);
   bool usingCustomVideoSource() const { return mUsingCustomVideoSource; }
   // enable the SDK's Android background manager, which uses wakelocks to keep the device active in the background
   // when necessary for SIP accounts; AndroidBackgroundingBookkeeper can optionally be null.
   void enableAndroidBackgrounding(const std::shared_ptr<CPCAPI2::test::AndroidBackgroundingBookkeeper>& bk);
   void disableAndroidBackgrounding();

   CPCAPI2::ConferenceConnector::ConferenceConnectorHandle createConferenceConnector();
   CPCAPI2::CallQuality::CallQualityReporterHandle createCallQualityReporter();

   CPCAPI2::Phone* phone;
   CPCAPI2::Media::MediaTransportsReactorFactory* mediaReactorFac;
   bool slave;
   CPCAPI2::test::EventHandler* phoneEvents;

   CPCAPI2::SipAccount::SipAccountManager* account;
   CPCAPI2::test::EventHandler* accountEvents;
   CPCAPI2::SipAccount::SipAccountHandle handle;
   TestAccountConfig config;

   CPCAPI2::SipMessageWaitingIndication::SipMessageWaitingIndicationManager* mwi;
   CPCAPI2::test::EventHandler* mwiEvents;
   CPCAPI2::SipPresence::SipPresenceManager* presence;
   CPCAPI2::test::EventHandler* presenceEvents;
   CPCAPI2::SipEvent::SipEventManager* subs;
   CPCAPI2::test::EventHandler* subsEvents;
#if 1 // (CPCAPI2_BRAND_SIP_FILE_TRANSFER_MODULE == 1)
   CPCAPI2::SipFileTransfer::SipFileTransferManager* fileTransferManager;
   CPCAPI2::test::EventHandler* fileTransferEvents;
#endif
   CPCAPI2::Media::MediaManager* media;
   CPCAPI2::Media::Audio* audio;
   CPCAPI2::test::EventHandler* mediaEvents;
   CPCAPI2::Media::Video* video;
   CPCAPI2::SipConversation::SipConversationManager* conversation;
   CPCAPI2::test::EventHandler* conversationEvents;
   CPCAPI2::SipConversation::SipConversationStateManager* conversationState;
   CPCAPI2::test::EventHandler* conversationStateEvents;

   CPCAPI2::SipConversation::SipConversationHandle conversationHandle;
   CPCAPI2::SipInstantMessage::SipInstantMessageManager* im;
   CPCAPI2::test::EventHandler* imEvents;
   CPCAPI2::Recording::RecordingManager* recording;
   CPCAPI2::test::EventHandler* recordingEvents;
   cpc::vector<CPCAPI2::Recording::RecorderHandle> recordingHandles;

   // Alianza API
   CPCAPI2::test::AlianzaApiAccountHandle alianzaApiAccountHandle;
   std::shared_ptr<CPCAPI2::test::AlianzaApiManager> alianzaApiManager;
   CPCAPI2::test::EventHandler* alianzaApiEvents;
   void createAlianzaAccountInBackend();
   void destroyAlianzaAccountInBackend();
   void checkAlianzaAccountRegistrationStatus(bool checkSip = true, bool checkPush = false);

   void skipAutoCreateAlianzaAccountInBackend() { mSkipAutoCreateAlianzaAccountInBackend = true; }

   /**
    * Reset the default alianza sipua from initialization with the ua being applied. Used
    * primarily when creating sdk accounts to handle registration for alianza extensions.
   */
   void applyAlianzaExtensionUa(const AlianzaSipUaInfo& ua, const AlianzaSessionInfo& sessionInfo);

   CPCAPI2::CallQuality::CallQualityReportManager* callQualityReport;
   CPCAPI2::test::EventHandler* callQualityEvents;

#if 1 // (CPCAPI2_BRAND_PTT_MODULE == 1)
   CPCAPI2::PushToTalk::PushToTalkManager* ptt;
   CPCAPI2::test::EventHandler* pttEvents;
   CPCAPI2::PushToTalk::PushToTalkServiceSettings pttSettings;
#endif

#if 1 // (CPCAPI2_BRAND_REMOTE_SYNC_MODULE == 1)
   CPCAPI2::RemoteSync::RemoteSyncManager* remoteSync;
   CPCAPI2::test::EventHandler* remoteSyncEvents;
   CPCAPI2::RemoteSync::SessionHandle remoteSyncSession;
#endif

#if 1 // (CPCAPI2_BRAND_ANALYTICS_MODULE == 1)
   CPCAPI2::Analytics::AnalyticsManager* analyticsManager;
   CPCAPI2::test::EventHandler* analyticsEvents;
   CPCAPI2::Analytics::AnalyticsHandle analyticsHandle;
#endif

#if 1 // (CPCAPI2_BRAND_STRETTO_UEM_MODULE == 1)
   CPCAPI2::StrettoUem::StrettoUemManager* strettoUemManager;
   CPCAPI2::test::EventHandler* strettoUemEvents;
#endif

#if 1 // (CPCAPI2_BRAND_VCCS_MODULE == 1)
   CPCAPI2::VCCS::Account::VccsAccountManager* vccsAccountManager;
   CPCAPI2::VCCS::Conference::VccsConferenceManager* vccsConferenceManager;
   CPCAPI2::test::EventHandler* vccsEvents;
   CPCAPI2::VCCS::Account::VccsAccountHandle vccsAccountHandle;
#endif

#if 1 // (CPCAPI2_BRAND_SNS_MODULE == 1)
   CPCAPI2::Notification::NotificationService* notificationService;
   CPCAPI2::test::EventHandler* notificationEvents;
   CPCAPI2::Notification::ChannelHandle notificationHandle;
#endif

#if 1 // (CPCAPI2_BRAND_BIEVENTS_MODULE == 1)
   CPCAPI2::BIEvents::BIEventsManager* biEventManager;
   CPCAPI2::BIEvents::BIEventsHelper* biEventHelper;
   CPCAPI2::test::EventHandler* biEvents;
   CPCAPI2::BIEvents::BIEventsHandle biEventHandle;
#endif

#if 1 // (CPCAPI2_BRAND_STRETTO_PROVISIONING_MODULE == 1)
   CPCAPI2::StrettoProvisioning::StrettoProvisioning* provisioningManager;
   CPCAPI2::test::EventHandler* provisioningEvents;
   CPCAPI2::StrettoProvisioning::StrettoProvisioningHandle provisioningHandle;
#endif

#if 1 // (CPCAPI2_BRAND_PUSH_NOTIFICATION_SERVER_MODULE == 1)
   CPCAPI2::PushService::PushNotificationServiceManager* pushNotificationServerManager;
   CPCAPI2::test::EventHandler* pushNotificationServerEvents;
#endif

#if 1 // (CPCAPI2_BRAND_PUSH_NOTIFICATION_CLIENT_MODULE == 1)
   CPCAPI2::PushEndpoint::PushNotificationEndpointManager* pushNotificationClientManager;
   CPCAPI2::test::EventHandler* pushNotificationClientEvents;
#endif

#if 1 // (CPCAPI2_BRAND_NETWORK_CHANGE_MODULE == 1)
   CPCAPI2::NetworkChangeManager_Mock* network;
   CPCAPI2::NetworkChangeManager* networkChangePublic;
   CPCAPI2::NetworkChangeManager::NetworkChangeManagerHandle networkChangeHandle;
   CPCAPI2::test::EventHandler* networkChangeEvents;
#endif
#if 1 // (CPCAPI2_BRAND_SIP_CHAT_MODULE == 1)
   CPCAPI2::SipChat::SipChatManager* chatManager;
   CPCAPI2::test::EventHandler* chatEvents;
#endif
#if 1 // (CPCAPI2_BRAND_SIP_STANDALONE_MESSAGING_MODULE == 1)
   CPCAPI2::SipStandaloneMessaging::SipStandaloneMessagingManager* standaloneMessagingManager;
   CPCAPI2::test::EventHandler* standaloneMessagingEvents;
#endif
   CPCAPI2::RcsCapabilityDiscovery::RcsCapabilityDiscoveryManager* capability;
   CPCAPI2::test::EventHandler* capabilityEvents;
   CPCAPI2::RcsProvision::RcsProvisionManager* provision;
   CPCAPI2::PeerConnection::PeerConnectionManager* peerConnection;
   CPCAPI2::test::EventHandler* peerConnEvents;
#if 1 // (CPCAPI2_BRAND_WEB_CALL_MODULE == 1)
   CPCAPI2::WebCall::WebCallManager* webCall;
   CPCAPI2::test::EventHandler* webCallEvents;
#endif
#if 1 // def CPCAPI2_GENBAND_MODULE
   CPCAPI2::Genband::GenbandRestAPIInterface* genbandManager;
   //CPCAPI2::Genband::GenbandRestAPIHandler* genbandHandler;
#endif
#if 1 // (CPCAPI2_BRAND_GENBAND_SOPI_MODULE == 1)
   CPCAPI2::GenbandSopi::GenbandSopiManager* genbandSopiManager;
#endif
#if 1 // (CPCAPI2_BRAND_RESOURCE_LIST_MODULE == 1)
   CPCAPI2::XCAP::XcapResourceListManager* xcapResourceListManager;
   CPCAPI2::test::EventHandler* xcapResourceListEvents;
#endif
#if 1 // (CPCAPI2_BRAND_WATCHER_INFO_MODULE == 1)
   CPCAPI2::WatcherInfo::WatcherInfoManager* winfoManager;
   CPCAPI2::test::EventHandler* winfoEvents;
#endif
#if 1 // (CPCAPI2_BRAND_CONFERENCE_MODULE == 1)
   CPCAPI2::SipConference::SipConferenceManager* conferenceManager;
#endif
#if 1 // (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
   CPCAPI2::JsonApi::JsonApiServer* jsonApiServer;
   CPCAPI2::test::EventHandler* jsonApiServerEvents;
#endif
#if 1 // (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
   CPCAPI2::JsonApi::JsonApiClient* jsonApiClient;
   CPCAPI2::test::EventHandler* jsonApiClientEvents;
   CPCAPI2::SipAccount::SipAccountManagerJsonProxy* sipAccountJsonProxy;
   CPCAPI2::test::EventHandler* sipAccountJsonProxyEvents;
   CPCAPI2::SipConversation::SipConversationManagerJsonProxy* sipConvJsonProxy;
#endif
#if 1 // (CPCAPI2_BRAND_REMOTE_SYNC_MODULE == 1)
   CPCAPI2::RemoteSync::RemoteSyncJsonProxy* remoteSyncJsonProxy;
   CPCAPI2::test::EventHandler* remoteSyncJsonProxyEvents = nullptr;
   CPCAPI2::RemoteSync::SessionHandle remoteSyncJsonProxySession;
#endif
#if 1 // (CPCAPI2_BRAND_AUTH_SERVER_MODULE == 1)
   CPCAPI2::AuthServer::AuthServer* authServer = nullptr;
   CPCAPI2::test::EventHandler* authServerEvents = nullptr;
#endif
#if 1 // (CPCAPI2_BRAND_ORCHESTRATION_SERVER_MODULE == 1)
   CPCAPI2::OrchestrationServer::OrchestrationServer* orchestrationServer = nullptr;
   CPCAPI2::test::EventHandler* orchestrationServerEvents;
#endif
#if 1 // (CPCAPI2_BRAND_CLOUD_WATCHDOG_SERVER_MODULE == 1)
   CPCAPI2::CloudWatchdog::CloudWatchdogService* cloudWatchdogServer = nullptr;
   CPCAPI2::test::EventHandler* cloudWatchdogServerEvents;
#endif
#if 1 // (CPCAPI2_BRAND_CLOUD_SERVICE_CONFIG_MODULE == 1)
   CPCAPI2::CloudServiceConfig::CloudServiceConfigManager* cloudServiceConfig = nullptr;
   CPCAPI2::test::EventHandler* cloudServiceConfigEvents;
#endif
#if 1 // (CPCAPI2_BRAND_CONFERENCE_CONNECTOR_MODULE == 1)
   CPCAPI2::ConferenceConnector::ConferenceConnectorManager* conferenceConnector;
   CPCAPI2::test::EventHandler* conferenceConnectorEvents;
#endif

   CPCAPI2::ConferenceBridge::ConferenceBridgeManager* conferenceBridge;
   CPCAPI2::test::EventHandler* conferenceBridgeEvents;
   CPCAPI2::ConferenceBridge::ConferenceRegistrar* conferenceRegistrar;
   CPCAPI2::test::EventHandler* conferenceRegistrarEvents;

   CPCAPI2::CloudRelayConnector::CloudRelayConnector* cloudRelayConnector;
   CPCAPI2::test::EventHandler* cloudRelayConnectorEvents;

   std::unique_ptr<CPCAPI2::test::TestVideoHelper> videoHelper;

   std::unique_ptr<AutoTestsLocalLogger> mLocalLogger;

   // book keeping for platforms that don't support multiple
   // CPCAPI2::Phone instances each accessing the device's audio back end
   static CPCAPI2::test::SingleOwnable sSystemAudioOwner;
   static CPCAPI2::test::SingleOwnable sSystemCameraOwner;
   bool mOwnsSystemAudio;
   bool mOwnsSystemCamera;
   bool mUsingCustomVideoSource;
   bool mUseVideoHelper;
   bool alianzaAccountCreated;
   bool isEnabled() { return enabled; }
   cpc::string uri();

protected:

   bool initialized;
   bool enabled;
   bool shouldDisableOnDestruct;
   bool shutdownTimedout;
   bool phoneReleased;
   bool initSlave;
   bool mAndroidBackgroundingEnabled;
   bool mSkipAutoCreateAlianzaAccountInBackend = false;
};

class SecureTestAccount : public TestAccount
{
public:
   SecureTestAccount(const std::string& name, TestAccountInitMode initMode=Account_Enable, bool disableOnDestruct=true, CPCAPI2::Phone* phone=NULL);
   virtual ~SecureTestAccount();

};

class TestAccountFactory
{
public:
   static std::shared_ptr<TestAccount> createTestAccount(unsigned int transportType, const std::string& name, TestAccountInitMode initMode=Account_Enable)
   {
      if (transportType == CPCAPI2::SipAccount::SipAccountTransport_TLS)
      {
         return std::make_shared<SecureTestAccount>(name, initMode);
      }

      return std::make_shared<TestAccount>(name, initMode);
   }
};

class AutoTestsLogger : public CPCAPI2::PhoneLogger
{
public:

   static AutoTestsLogger& instance();
   virtual ~AutoTestsLogger();

   // convenience class for invoking setBadLogMessageCheckFunction to ensure removal of custom
   // log message check funciton when finished (i.e. so other unrelated tests don't use the function)
   class ScopedSetBadLogMessageCheckFunction
   {
   public:
      ScopedSetBadLogMessageCheckFunction(const std::function<bool(const char *message, CPCAPI2::LogLevel level)>& externalBadLogMessageCheckFn)
      {
         AutoTestsLogger::instance().setBadLogMessageCheckFunction(externalBadLogMessageCheckFn);
      }

      ~ScopedSetBadLogMessageCheckFunction()
      {
         AutoTestsLogger::instance().setBadLogMessageCheckFunction(std::function<bool(const char *message, CPCAPI2::LogLevel level)>());
      }
   };

   virtual bool operator()(CPCAPI2::LogLevel level, const char *subsystem, const char *appName, const char *file,
      int line, const char *message, const char *messageWithHeaders);
   // optional handler which can monitor log messages for bad/unwanted log messages. Function should return
   // true if a log line contains bad/unwanted log message.
   void setBadLogMessageCheckFunction(const std::function<bool(const char *message, CPCAPI2::LogLevel level)>& externalBadLogMessageCheckFunction);
   int getBadMessagesCount();

   // optional handler for listening to log events
   void setLogMessageListenerFunction(const std::function<void(const char *message, const char* subsystem, CPCAPI2::LogLevel level)>& logMessageListenerFunction);

   class ScopedMessageListenerFunction
   {
   public:
      ScopedMessageListenerFunction(const std::function<void(const char *message, const char* subsystem, CPCAPI2::LogLevel level)>& logMessageListenerFunction)
      {
         AutoTestsLogger::instance().setLogMessageListenerFunction(logMessageListenerFunction);
      }

      ~ScopedMessageListenerFunction()
      {
         AutoTestsLogger::instance().setLogMessageListenerFunction(std::function<void(const char *message, const char* subsystem, CPCAPI2::LogLevel level)>());
      }
   };
private:
   AutoTestsLogger();
   static void initInstance();

   std::ofstream mLogFile;
   std::mutex mMutex;

   int mBadLogMessageCount;
   std::function<bool(const char *message, CPCAPI2::LogLevel level)> mExternalBadLogMessageCheckFn;
   std::function<void(const char *message, const char* subsystem, CPCAPI2::LogLevel level)> mExternalListenerLogMessageCheckFn;
};

class AutoTestsLocalLogger : public CPCAPI2::LocalLoggerHandler
{
public:
   AutoTestsLocalLogger(const std::string& name);
   void onLogMessage(CPCAPI2::LocalLogMessage& msg);
private:
   std::string mName;

};

namespace CPCAPI2
{
   namespace test
   {
      class ReproHolder
      {
      public:
         static CPCAPI2::ReproRunner* instance();
         static void destroyInstance();
      private:
         static CPCAPI2::ReproRunner* s_reproRunner;
      };
   }
}

#endif
