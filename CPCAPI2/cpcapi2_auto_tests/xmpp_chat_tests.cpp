#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#include "test_framework/xmpp_test_helper.h"
#include "xmpp/XmppAccountInterface.h"
#include "xmpp/XmppAccountJsonApi.h"
#include "xmpp/XmppChatJsonApi.h"
#include "xmpp/XmppRosterJsonApi.h"
#include "xmpp/XmppChatManagerImpl.h"
#include "../../impl/auth_server/AuthServerJwtUtils.h"

#ifndef ANDROID
//define USE_CPCAPI2_JSON_TESTS 1
#endif

#if (CPCAPI2_BRAND_XMPP_CHAT_MODULE == 1)

#if defined(__GNUC__) || defined(__clang__)
#pragma GCC diagnostic ignored "-Wdeprecated-declarations"
#endif

using namespace CPCAPI2;
using namespace CPCAPI2::XmppAccount;
using namespace CPCAPI2::XmppRoster;
using namespace CPCAPI2::XmppChat;
using namespace CPCAPI2::Json<PERSON><PERSON>;

extern "C" 
{
   extern int xmppUnrequestedAckCount;
}

namespace {

class XmppChatModuleTest : public CpcapiAutoTest
{
public:
   XmppChatModuleTest() {}
   virtual ~XmppChatModuleTest() {}
   static void SetUpTestCase() {}
   static void TearDownTestCase() {}
   virtual void SetUp() {}
   virtual void TearDown() {}
};

class DiscoMockEvent
{
public:
   DiscoMockEvent(XmppChatManagerImpl& chatImpl, XmppChatInfo* chatInfo, const std::string& jid) : 
   mChatImpl{chatImpl}, mChatInfo{chatInfo}, mJid{jid} {

   }

   void timeout()
   {
      mChatImpl.peerDiscoverySetTimeout(mChatInfo, mJid, 1);
   }

private:
   XmppChatManagerImpl& mChatImpl;
   XmppChatInfo* mChatInfo;
   const std::string mJid;
};

TEST_F(XmppChatModuleTest, XmppBasicChat) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   auto aliceEvent = std::async(std::launch::async, [&] () {
      XmppChatHandle aliceChat = alice.chat->createChat(alice.handle);
      alice.chat->addParticipant(aliceChat, bob.config.bare());
      alice.chat->start(aliceChat);

      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewChatEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.chatType, ChatType_Outgoing);
      }

      XmppChatMessageHandle cm1 = alice.chat->sendMessage(aliceChat, "test123");
      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::SendMessageSuccessEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onSendMessageSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.message, cm1);
      }

      cpc::string remoteSyncToID = alice.chat->getRemoteSyncToID(aliceChat, cm1);
      ASSERT_GT(remoteSyncToID.size(), 2);

      {
      // Wait for the message delivery notification (from Bob)
      XmppChatHandle h;
      MessageDeliveredEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onMessageDelivered", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.message, cm1);
      }

      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::IsComposingMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onIsComposingMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.state, CPCAPI2::XmppChat::IsComposingMessageState_Active);
      }

      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.from, bob.config.bare());
      ASSERT_EQ(evt.messageContent, "test456");
      XmppChatMessageHandle aliceRecvdCm1 = evt.message;
      alice.chat->notifyMessageDelivered(aliceChat, aliceRecvdCm1, CPCAPI2::XmppChat::MessageDeliveryStatus_Delivered);
      }
      
      alice.chat->validateChatHandle(alice.handle, aliceChat);
      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::ValidateChatHandleEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onValidateChatHandleResult", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_TRUE(evt.chatHandleValid);
      }
      

      alice.chat->end(aliceChat);

      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::ChatEndedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onChatEnded", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedLocally);
      }
      
      alice.chat->validateChatHandle(alice.handle, aliceChat);
      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::ValidateChatHandleEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onValidateChatHandleResult", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_FALSE(evt.chatHandleValid);
      }

   });

   auto bobEvent = std::async(std::launch::async, [&] () {
      XmppChatHandle bobChat = 0;

      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewChatEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewChat", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_NE(h, 0);
      ASSERT_EQ(evt.chatType, ChatType_Incoming);
      bobChat = h;
      }

      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.from, alice.config.bare());
      ASSERT_EQ(evt.messageContent, "test123");
      ASSERT_TRUE(evt.message != 0);
      XmppChatMessageHandle bobRecvdCm1 = evt.message;
      bob.chat->notifyMessageDelivered(bobChat, bobRecvdCm1, CPCAPI2::XmppChat::MessageDeliveryStatus_Delivered);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      bob.chat->setIsComposingMessage(bobChat);
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      XmppChatMessageHandle bobCm1 = bob.chat->sendMessage(bobChat, "test456");

      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::SendMessageSuccessEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onSendMessageSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.message, bobCm1);
      }

      {
      // Wait for the message delivery notification (from Bob)
      XmppChatHandle h;
      MessageDeliveredEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onMessageDelivered", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.message, bobCm1);
      }

      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::ChatEndedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onChatEnded", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedRemotely);
      }
   });

   waitFor2(aliceEvent, bobEvent);
}

TEST_F(XmppChatModuleTest, XmppHtmlTextChat) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   auto aliceEvent = std::async(std::launch::async, [&] () {
      XmppChatHandle aliceChat = alice.chat->createChat(alice.handle);
      alice.chat->addParticipant(aliceChat, bob.config.bare());
      alice.chat->start(aliceChat);

      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewChatEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.chatType, ChatType_Outgoing);
      }

      XmppChatMessageHandle cm1 = alice.chat->sendMessage(aliceChat, "", "hello", "");

      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::SendMessageSuccessEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onSendMessageSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.message, cm1);
      }

      {
      // Wait for the message delivery notification (from Bob)
      XmppChatHandle h;
      MessageDeliveredEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onMessageDelivered", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.message, cm1);
      }

      alice.chat->end(aliceChat);

      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::ChatEndedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onChatEnded", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedLocally);
      }

   });

   auto bobEvent = std::async(std::launch::async, [&] () {
      XmppChatHandle bobChat = 0;

      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewChatEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewChat", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_NE(h, 0);
      ASSERT_EQ(evt.chatType, ChatType_Incoming);
      bobChat = h;
      }

      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.from, alice.config.bare());
      ASSERT_EQ(evt.htmlText, "hello");
      ASSERT_TRUE(evt.message != 0);

      bob.chat->notifyMessageDelivered(bobChat, evt.message, CPCAPI2::XmppChat::MessageDeliveryStatus_Delivered);
      }

      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::ChatEndedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onChatEnded", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedRemotely);
      }
   });

   waitFor2(aliceEvent, bobEvent);
}

TEST_F(XmppChatModuleTest, XmppBasicReaction) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   auto aliceEvent = std::async(std::launch::async, [&] () {
      XmppChatHandle aliceChat = alice.chat->createChat(alice.handle);
      alice.chat->addParticipant(aliceChat, bob.config.bare());
      alice.chat->start(aliceChat);

      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewChatEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.chatType, ChatType_Outgoing);
      }

      cpc::string sentMsgID;
      XmppChatMessageHandle cm1 = alice.chat->sendMessage(aliceChat, "test123");
      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::SendMessageSuccessEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onSendMessageSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.message, cm1);
      sentMsgID = evt.messageId;
      }

      cpc::string remoteSyncToID = alice.chat->getRemoteSyncToID(aliceChat, cm1);
      ASSERT_GT(remoteSyncToID.size(), 2);

      {
      // Wait for the message delivery notification (from Bob)
      XmppChatHandle h;
      MessageDeliveredEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onMessageDelivered", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.message, cm1);
      }

      // setting two reactions
      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewReactionEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewReaction", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.from, bob.config.bare());
      ASSERT_EQ(evt.reactionTarget, sentMsgID);
      ASSERT_EQ(evt.reactions.size(), 2);
      XmppChatMessageHandle aliceRecvdCm1 = evt.message;
      alice.chat->notifyMessageDelivered(aliceChat, aliceRecvdCm1, CPCAPI2::XmppChat::MessageDeliveryStatus_Delivered);
      }

      // removing all reactions
      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewReactionEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewReaction", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.from, bob.config.bare());
      ASSERT_EQ(evt.reactionTarget, sentMsgID);
      ASSERT_EQ(evt.reactions.size(), 0);
      XmppChatMessageHandle aliceRecvdCm1 = evt.message;
      alice.chat->notifyMessageDelivered(aliceChat, aliceRecvdCm1, CPCAPI2::XmppChat::MessageDeliveryStatus_Delivered);
      }
      
      alice.chat->validateChatHandle(alice.handle, aliceChat);
      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::ValidateChatHandleEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onValidateChatHandleResult", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_TRUE(evt.chatHandleValid);
      }
      

      alice.chat->end(aliceChat);

      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::ChatEndedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onChatEnded", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedLocally);
      }
      
      alice.chat->validateChatHandle(alice.handle, aliceChat);
      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::ValidateChatHandleEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onValidateChatHandleResult", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_FALSE(evt.chatHandleValid);
      }

   });

   auto bobEvent = std::async(std::launch::async, [&] () {
      XmppChatHandle bobChat = 0;

      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewChatEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewChat", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_NE(h, 0);
      ASSERT_EQ(evt.chatType, ChatType_Incoming);
      bobChat = h;
      }

      cpc::string bobRecvdCm1;
      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.from, alice.config.bare());
      ASSERT_EQ(evt.messageContent, "test123");
      ASSERT_TRUE(evt.message != 0);
      bobRecvdCm1 = evt.messageId;
      bob.chat->notifyMessageDelivered(bobChat, evt.message, CPCAPI2::XmppChat::MessageDeliveryStatus_Delivered);
      }

      // setting two reactions
      cpc::vector<cpc::string> reactions;
      reactions.push_back("💘");
      reactions.push_back("💜");
      XmppChatMessageHandle bobCm1 = bob.chat->sendReaction(bobChat, bobRecvdCm1, reactions);

      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::SendMessageSuccessEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onSendMessageSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.message, bobCm1);
      }

      // removing all reactions
      reactions.clear();
      bobCm1 = bob.chat->sendReaction(bobChat, bobRecvdCm1, reactions);

      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::SendMessageSuccessEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onSendMessageSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.message, bobCm1);
      }

      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::ChatEndedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onChatEnded", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedRemotely);
      }
   });

   waitFor2(aliceEvent, bobEvent);
}

TEST_F(XmppChatModuleTest, XmppBasicRetraction) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   auto aliceEvent = std::async(std::launch::async, [&] () {
      XmppChatHandle aliceChat = alice.chat->createChat(alice.handle);
      alice.chat->addParticipant(aliceChat, bob.config.bare());
      alice.chat->start(aliceChat);

      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewChatEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.chatType, ChatType_Outgoing);
      }

      cpc::string sentMsgID;
      XmppChatMessageHandle cm1 = alice.chat->sendMessage(aliceChat, "test123");
      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::SendMessageSuccessEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onSendMessageSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.message, cm1);
      ASSERT_NE(evt.messageId, cpc::string(""));
      sentMsgID = evt.messageId;
      }

      cpc::string remoteSyncToID = alice.chat->getRemoteSyncToID(aliceChat, cm1);
      ASSERT_GT(remoteSyncToID.size(), 2);

      {
      // Wait for the message delivery notification (from Bob)
      XmppChatHandle h;
      MessageDeliveredEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onMessageDelivered", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.message, cm1);
      }

      // retracting sent message
      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewMessageRetractionEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewMessageRetraction", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.from, bob.config.bare());
      // use the origin ID top retract the message
      ASSERT_EQ(evt.retractTarget, sentMsgID);
      XmppChatMessageHandle aliceRecvdCm1 = evt.message;
      alice.chat->notifyMessageDelivered(aliceChat, aliceRecvdCm1, CPCAPI2::XmppChat::MessageDeliveryStatus_Delivered);
      }

      // retracting message already retracted
      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewMessageRetractionEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewMessageRetraction", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.from, bob.config.bare());
      // use the origin ID top retract the message
      ASSERT_EQ(evt.retractTarget, sentMsgID);
      XmppChatMessageHandle aliceRecvdCm1 = evt.message;
      alice.chat->notifyMessageDelivered(aliceChat, aliceRecvdCm1, CPCAPI2::XmppChat::MessageDeliveryStatus_Delivered);
      }
      
      alice.chat->validateChatHandle(alice.handle, aliceChat);
      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::ValidateChatHandleEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onValidateChatHandleResult", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_TRUE(evt.chatHandleValid);
      }
      

      alice.chat->end(aliceChat);

      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::ChatEndedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onChatEnded", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedLocally);
      }
      
      alice.chat->validateChatHandle(alice.handle, aliceChat);
      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::ValidateChatHandleEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onValidateChatHandleResult", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_FALSE(evt.chatHandleValid);
      }

   });

   auto bobEvent = std::async(std::launch::async, [&] () {
      XmppChatHandle bobChat = 0;

      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewChatEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewChat", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_NE(h, 0);
      ASSERT_EQ(evt.chatType, ChatType_Incoming);
      bobChat = h;
      }

      cpc::string bobRecvdCm1;
      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.from, alice.config.bare());
      ASSERT_EQ(evt.messageContent, "test123");
      ASSERT_TRUE(evt.message != 0);
      bobRecvdCm1 = evt.messageId;
      // and in this case the origin ID should be the same
      ASSERT_EQ(evt.originId, evt.messageId);
      bob.chat->notifyMessageDelivered(bobChat, evt.message, CPCAPI2::XmppChat::MessageDeliveryStatus_Delivered);
      }

      // retracting sent message
      XmppChatMessageHandle bobCm1 = bob.chat->sendMessageRetraction(bobChat, bobRecvdCm1);

      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::SendMessageSuccessEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onSendMessageSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.message, bobCm1);
      }

      // retracting already retracted message
      bobCm1 = bob.chat->sendMessageRetraction(bobChat, bobRecvdCm1);

      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::SendMessageSuccessEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onSendMessageSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.message, bobCm1);
      }

      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::ChatEndedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onChatEnded", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedRemotely);
      }
   });

   waitFor2(aliceEvent, bobEvent);
}

TEST_F(XmppChatModuleTest, OfflineChat) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob", Account_Init);

   XmppChatHandle aliceChat = alice.chat->createChat(alice.handle);
   alice.chat->addParticipant(aliceChat, bob.config.bare());
   alice.chat->start(aliceChat);

   {
   XmppChatHandle h;
   CPCAPI2::XmppChat::NewChatEvent evt;
   ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(aliceChat, h);
   ASSERT_EQ(evt.chatType, ChatType_Outgoing);
   }

   XmppChatMessageHandle chat = alice.chat->sendMessage(aliceChat, "offline");

   {
   XmppChatHandle h;
   CPCAPI2::XmppChat::SendMessageSuccessEvent evt;
   ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onSendMessageSuccess", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(evt.message, chat);
   }

   bob.enable();

   XmppChatHandle bobChat = 0;

   {
   CPCAPI2::XmppChat::NewChatEvent evt;
   ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewChat", 15000, CPCAPI2::test::AlwaysTruePred(), bobChat, evt));
   ASSERT_NE(bobChat, 0);
   ASSERT_EQ(evt.chatType, ChatType_Incoming);
   }

   {
   XmppChatHandle h;
   CPCAPI2::XmppChat::NewMessageEvent evt;
   ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(evt.from, alice.config.settings.username + "@" + alice.config.settings.domain);
   ASSERT_EQ(evt.messageContent, "offline");
   ASSERT_TRUE(evt.message != 0);

   bob.chat->notifyMessageDelivered(bobChat, evt.message, CPCAPI2::XmppChat::MessageDeliveryStatus_Delivered);
   }

   {
   // Wait for the message delivery notification (from Bob)
   XmppChatHandle h;
   MessageDeliveredEvent evt;
   ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onMessageDelivered", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(evt.message, chat);
   }
}

TEST_F(XmppChatModuleTest, DISABLED_ChatWithMultipleResources) { // require server support of routing to multiple resources
   XmppTestAccount alice("alice");
   XmppTestAccount bob("", Account_NoInit);
   XmppTestAccount bob1(bob, "bob1");
   XmppTestAccount bob2(bob, "bob2");

   XmppChatHandle aliceChat = alice.chat->createChat(alice.handle);
   alice.chat->addParticipant(aliceChat, bob.config.bare());
   alice.chat->start(aliceChat);

   XmppChatHandle bob1Chat = 0;
   XmppChatHandle bob2Chat = 0;

   {
   XmppChatHandle h;
   CPCAPI2::XmppChat::NewChatEvent evt;
   ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(aliceChat, h);
   ASSERT_EQ(evt.chatType, ChatType_Outgoing);
   }

   XmppChatMessageHandle cm1 = alice.chat->sendMessage(aliceChat, "alice");

   {
   XmppChatHandle h;
   CPCAPI2::XmppChat::NewChatEvent evt;
   ASSERT_TRUE(cpcExpectEvent(bob1.events, "XmppChatHandler::onNewChat", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_NE(h, 0);
   ASSERT_EQ(evt.chatType, ChatType_Incoming);
   bob1Chat = h;
   }

   {
   XmppChatHandle h;
   CPCAPI2::XmppChat::NewMessageEvent evt;
   ASSERT_TRUE(cpcExpectEvent(bob1.events, "XmppChatHandler::onNewMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(evt.from, alice.config.bare());
   ASSERT_EQ(evt.messageContent, "alice");
   ASSERT_TRUE(evt.message != 0);
   }

   {
   XmppChatHandle h;
   CPCAPI2::XmppChat::NewChatEvent evt;
   ASSERT_TRUE(cpcExpectEvent(bob2.events, "XmppChatHandler::onNewChat", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_NE(h, 0);
   ASSERT_EQ(evt.chatType, ChatType_Incoming);
   bob2Chat = h;
   }

   {
   XmppChatHandle h;
   CPCAPI2::XmppChat::NewMessageEvent evt;
   ASSERT_TRUE(cpcExpectEvent(bob2.events, "XmppChatHandler::onNewMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(evt.from, alice.config.bare());
   ASSERT_EQ(evt.messageContent, "alice");
   ASSERT_TRUE(evt.message != 0);
   }

   XmppChatMessageHandle bob1Cm1 = bob1.chat->sendMessage(bob1Chat, "bob1");

   {
   XmppChatHandle h;
   CPCAPI2::XmppChat::NewMessageEvent evt;
   ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(evt.from, bob.config.bare());
   ASSERT_EQ(evt.messageContent, "bob1");
   }

   XmppChatMessageHandle cm2 = alice.chat->sendMessage(aliceChat, "alice");

   {
   XmppChatHandle h;
   CPCAPI2::XmppChat::NewMessageEvent evt;
   ASSERT_TRUE(cpcExpectEvent(bob1.events, "XmppChatHandler::onNewMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(evt.from, alice.config.bare());
   ASSERT_EQ(evt.messageContent, "alice");
   ASSERT_TRUE(evt.message != 0);
   }

   XmppChatMessageHandle bob2Cm1 = bob2.chat->sendMessage(bob2Chat, "bob2");

   {
   XmppChatHandle h;
   CPCAPI2::XmppChat::NewMessageEvent evt;
   ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(evt.from, bob.config.bare());
   ASSERT_EQ(evt.messageContent, "bob2");
   }

   XmppChatMessageHandle cm3 = alice.chat->sendMessage(aliceChat, "alice");

   {
   XmppChatHandle h;
   CPCAPI2::XmppChat::NewMessageEvent evt;
   ASSERT_TRUE(cpcExpectEvent(bob2.events, "XmppChatHandler::onNewMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
   ASSERT_EQ(evt.from, alice.config.bare());
   ASSERT_EQ(evt.messageContent, "alice");
   ASSERT_TRUE(evt.message != 0);
   }
}

TEST_F(XmppChatModuleTest, XmppBasicEndChat) { // alice ends the chat
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   auto aliceEvent = std::async(std::launch::async, [&] () {
      XmppChatHandle aliceChat = alice.chat->createChat(alice.handle);
      alice.chat->addParticipant(aliceChat, bob.config.bare());
      alice.chat->start(aliceChat);

      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewChatEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.chatType, ChatType_Outgoing);
      }

      alice.chat->end(aliceChat);

      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::ChatEndedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onChatEnded", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedLocally);
      }
   });

   auto bobEvent = std::async(std::launch::async, [&] () {
      XmppChatHandle bobChat = 0;

      {
      CPCAPI2::XmppChat::NewChatEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewChat", 15000, CPCAPI2::test::AlwaysTruePred(), bobChat, evt));
      ASSERT_NE(bobChat, 0);
      ASSERT_EQ(evt.chatType, ChatType_Incoming);
      }

      //bob.chat->accept(bobChat); // unnecessary for this test

      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::ChatEndedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onChatEnded", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(bobChat, h);
      ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedRemotely);
      }
   });

   waitFor2(aliceEvent, bobEvent);
}

TEST_F(XmppChatModuleTest, XmppBasicEndChat2) { // bob ends the chat
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   auto aliceEvents = std::async(std::launch::async, [&] () {
      XmppChatHandle aliceChat = alice.chat->createChat(alice.handle);
      alice.chat->addParticipant(aliceChat, bob.config.bare());
      alice.chat->start(aliceChat);

      {
         XmppChatHandle h;
         XmppChat::NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(aliceChat, h);
         ASSERT_EQ(evt.chatType, ChatType_Outgoing);
      }

      {
         XmppChatHandle h;
         XmppChat::ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onChatEnded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(aliceChat, h);
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedRemotely);
      }

   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      XmppChatHandle bobChat = 0;
      {
         XmppChat::NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), bobChat, evt));
         ASSERT_EQ(evt.chatType, ChatType_Incoming);
      }

      //bob.chat->accept(bobChat); // unnecessary for this test

      bob.chat->end(bobChat);

      {
         XmppChatHandle h;
         XmppChat::ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onChatEnded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobChat, h);
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedLocally);
      }
   });

   waitFor2(aliceEvents, bobEvents);
}

TEST_F(XmppChatModuleTest, XmppBasicEndChat3) { // A sends to B, B sends to A, A sends to B, B sends to A, disable account
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   auto aliceEvents = std::async(std::launch::async, [&]() {
      XmppChatHandle aliceChat = alice.chat->createChat(alice.handle);
      alice.chat->addParticipant(aliceChat, bob.config.bare());
      alice.chat->start(aliceChat);

      {
         XmppChatHandle h;
         XmppChat::NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(aliceChat, h);
         ASSERT_EQ(evt.chatType, ChatType_Outgoing);
      }

      alice.chat->sendMessage(aliceChat, "1. A -> B");

      {
         // Wait for the new message notification (from Alice)
         XmppChatHandle h;
         XmppChat::NewMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewMessage", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(aliceChat, h);
         //ASSERT_EQ(evt.message, newMessageHandle);
         ASSERT_EQ(evt.messageContent, "2. B -> A");
      }

      //alice.account->requestRegistrationRefresh(alice.handle, 0);
      //assertXmppConnecting(alice);
      //assertXmppConnected(alice);

      alice.chat->sendMessage(aliceChat, "3. A -> B");

      {
         // Wait for the new message notification (from Alice)
         XmppChatHandle h;
         XmppChat::NewMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewMessage", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(aliceChat, h);
         //ASSERT_EQ(evt.message, newMessageHandle);
         ASSERT_EQ(evt.messageContent, "4. B -> A");
      }

      {
         XmppChatHandle h;
         XmppChat::ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onChatEnded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(aliceChat, h);
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedRemotely);
      }

   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      XmppChatHandle bobChat = 0;
      {
         XmppChat::NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), bobChat, evt));
         ASSERT_EQ(evt.chatType, ChatType_Incoming);
      }

      bob.chat->accept(bobChat); // unnecessary for this test

      {
         // Wait for the new message notification (from Alice)
         XmppChatHandle h;
         XmppChat::NewMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewMessage", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobChat, h);
         //ASSERT_EQ(evt.message, newMessageHandle);
         ASSERT_EQ(evt.messageContent, "1. A -> B");
      }

      bob.chat->sendMessage(bobChat, "2. B -> A");

      {
         // Wait for the new message notification (from Alice)
         XmppChatHandle h;
         XmppChat::NewMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewMessage", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobChat, h);
         //ASSERT_EQ(evt.message, newMessageHandle);
         ASSERT_EQ(evt.messageContent, "3. A -> B");
      }

      bob.chat->sendMessage(bobChat, "4. B -> A");

      bob.disable();
      //bob.chat->end(bobChat);

      //{
      //   XmppChatHandle h;
      //   XmppChat::ChatEndedEvent evt;
      //   ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onChatEnded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      //   ASSERT_EQ(bobChat, h);
      //   ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedLocally);
      //}
   });

   waitFor2(aliceEvents, bobEvents);
}

TEST_F(XmppChatModuleTest, BasicChatWithIsComposingNotification) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   // Alice's thread
   auto aliceEvents = std::async(std::launch::async, [&] () {
      // Start chat session with bob
      XmppChatHandle aliceChat = alice.chat->createChat(alice.handle);
      alice.chat->addParticipant(aliceChat, bob.config.bare());
      alice.chat->start(aliceChat);

      {
         // Wait for the new chat notification (from Alice)
         XmppChatHandle h;
         XmppChat::NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(aliceChat, h);
         ASSERT_EQ(evt.chatType, ChatType_Outgoing);
      }

      alice.chat->setIsComposingMessage(aliceChat);

      // wait until idle state is sent out (default idle timeout = 15 seconds)
      std::this_thread::sleep_for(std::chrono::seconds(18));

      XmppChatMessageHandle newMessageHandle = alice.chat->sendMessage(aliceChat, "Hi Bob");

      {
         // Wait for the send message notification (from Alice)
         XmppChat::SendMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onSendMessageSuccess", 5000, CPCAPI2::test::AlwaysTruePred(), aliceChat, evt));
         ASSERT_EQ(evt.message, newMessageHandle);
      }

      {
         // Wait for the message delivery notification (from Bob)
         XmppChatHandle h;
         MessageDeliveredEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onMessageDelivered", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.message, newMessageHandle);
      }

      // End the chat session
      alice.chat->end(aliceChat);

      {
         // Wait for the end chat notification (from Bob)
         XmppChatHandle h;
         XmppChat::ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onChatEnded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(aliceChat, h);
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedLocally);
      }
   });

   // Bob's thread
   auto bobEvents = std::async(std::launch::async, [&] () {
      XmppChatHandle bobChat = 0;
      {
         // Wait for the new chat notification (from Alice)
         NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), bobChat, evt));
         ASSERT_NE(bobChat, 0);
         ASSERT_EQ(evt.chatType, ChatType_Incoming);
      }

      // Accept the chat session (from Alice)
      bob.chat->accept(bobChat);

      {
         // Wait for the isComposing notification [Active] (from Alice)
         IsComposingMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onIsComposingMessage", 5000, CPCAPI2::test::AlwaysTruePred(), bobChat, evt));
         ASSERT_EQ(evt.state, XmppChat::IsComposingMessageState_Active);
      }

      {
         // Wait for the isComposing notification [Idle] (from Alice)
         IsComposingMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onIsComposingMessage", 20000, CPCAPI2::test::AlwaysTruePred(), bobChat, evt));
         ASSERT_EQ(evt.state, XmppChat::IsComposingMessageState_Idle);
      }

      {
         // Wait for the new message notification (from Alice)
         XmppChatHandle h;
         XmppChat::NewMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewMessage", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobChat, h);
         //ASSERT_EQ(evt.message, newMessageHandle);
         ASSERT_EQ(evt.messageContent, "Hi Bob");

         bob.chat->notifyMessageDelivered(bobChat, evt.message, XmppChat::MessageDeliveryStatus_Delivered);
      }

      {
         // Wait for the end of chat session notification (from Bob)
         XmppChatHandle h;
         XmppChat::ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onChatEnded", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobChat, h);
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedRemotely);
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   waitFor2(aliceEvents, bobEvents);
}

TEST_F(XmppChatModuleTest, SwitchToIdleStateImmediately) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   auto aliceEvent = std::async(std::launch::async, [&]() {
      while (true) {
         // Wait for the previous chat notification (from Alice)
         XmppChatHandle h;
         XmppChat::NewChatEvent evt;
         if (!cpcExpectEvent(alice.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt)) break;
         alice.chat->end(h);
      }
   });

   auto bobEvent = std::async(std::launch::async, [&]() {
      while (true) {
         // Wait for the previous chat notification (from Alice)
         XmppChatHandle h;
         XmppChat::NewChatEvent evt;
         if (!cpcExpectEvent(bob.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt)) break;
         bob.chat->end(h);
      }
   });

   waitFor2(aliceEvent, bobEvent);

   alice.events->clearCommands();
   bob.events->clearCommands();

   // Start chat session with bob
   XmppChatHandle aliceChat = alice.chat->createChat(alice.handle);
   alice.chat->addParticipant(aliceChat, bob.config.bare());
   alice.chat->start(aliceChat);

   XmppChatHandle bobChat = 0;

   {
      // Wait for the new chat notification (from Alice)
      NewChatEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), bobChat, evt));
      ASSERT_NE(bobChat, 0);
      ASSERT_EQ(evt.chatType, ChatType_Incoming);
   }

   // Accept the chat session (from Alice)
   bob.chat->accept(bobChat);

   {
      // Wait for the new chat notification (from Alice)
      XmppChatHandle h;
      XmppChat::NewChatEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.chatType, ChatType_Outgoing);
   }

   alice.chat->setIsComposingMessage(aliceChat, 90, 1);

   // Wait for the isComposing notification [Active] (from Alice)
   {
      IsComposingMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onIsComposingMessage", 5000, CPCAPI2::test::AlwaysTruePred(), bobChat, evt));
      ASSERT_EQ(evt.state, XmppChat::IsComposingMessageState_Active);
   }

   // Wait for the isComposing notification [Idle] (from Alice)
   {
      IsComposingMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onIsComposingMessage", 5000, CPCAPI2::test::AlwaysTruePred(), bobChat, evt));
      ASSERT_EQ(evt.state, XmppChat::IsComposingMessageState_Idle);
   }

   alice.chat->setIsComposingMessage(aliceChat, 90, 0); // immediate idle

   {
      IsComposingMessageEvent evt;
      ASSERT_FALSE(cpcExpectEvent(bob.events, "XmppChatHandler::onIsComposingMessage", 5000, CPCAPI2::test::AlwaysTruePred(), bobChat, evt));
   }

   alice.chat->setIsComposingMessage(aliceChat, 90, 15);
   alice.chat->setIsComposingMessage(aliceChat, 90, 0); // immediate idle

   {
      IsComposingMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onIsComposingMessage", 5000, CPCAPI2::test::AlwaysTruePred(), bobChat, evt));
      ASSERT_EQ(evt.state, XmppChat::IsComposingMessageState_Active);
   }

   {
      IsComposingMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onIsComposingMessage", 2000, CPCAPI2::test::AlwaysTruePred(), bobChat, evt));
      ASSERT_EQ(evt.state, XmppChat::IsComposingMessageState_Idle);
   }

   {
      IsComposingMessageEvent evt;
      ASSERT_FALSE(cpcExpectEvent(bob.events, "XmppChatHandler::onIsComposingMessage", 15000, CPCAPI2::test::AlwaysTruePred(), bobChat, evt));
   }
}

TEST_F(XmppChatModuleTest, MessageDeliveryError_InvalidReceiver) {
   XmppTestAccount alice("alice");

   auto aliceEvent = std::async(std::launch::async, [&]() {
      XmppChatHandle aliceChat = alice.chat->createChat(alice.handle);
      cpc::string badJid = "jid:<EMAIL>";
      alice.chat->addParticipant(aliceChat, badJid);
      alice.chat->start(aliceChat);

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(aliceChat, h);
         ASSERT_EQ(evt.chatType, ChatType_Outgoing);
      }

      XmppChatMessageHandle em1 = alice.chat->sendMessage(aliceChat, "message in a bottle");

#if 0 // bliu: with StreamManagement enabled, the message cannot be sent successfully, hence disable this check
      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::SendMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onSendMessageSuccess", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.message, em1);
         ASSERT_EQ(aliceChat, h);
      }
#endif

      {
         XmppChatHandle h;
         MessageDeliveryErrorEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onMessageDeliveryError", 60000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(aliceChat, h);
         ASSERT_EQ(evt.message, em1);
      }
   });

   waitFor(aliceEvent);
}

TEST_F(XmppChatModuleTest, MessageDelivery_OfflineReceiver) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob", Account_Init);

   auto aliceEvent = std::async(std::launch::async, [&]() {
      XmppChatHandle aliceChat = alice.chat->createChat(alice.handle);
      alice.chat->addParticipant(aliceChat, bob.config.bare());
      alice.chat->start(aliceChat);

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(aliceChat, h);
         ASSERT_EQ(evt.chatType, ChatType_Outgoing);
      }

      XmppChatMessageHandle em1 = alice.chat->sendMessage(aliceChat, "message in a bottle");

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::SendMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onSendMessageSuccess", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.message, em1);
         ASSERT_EQ(aliceChat, h);
      }

      bob.enable();

      {
         XmppChatHandle h;
         MessageDeliveredEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onMessageDelivered", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(aliceChat, h);
         ASSERT_EQ(evt.message, em1);
      }
   });

   waitFor(aliceEvent);
}

TEST_F(XmppChatModuleTest, MessageDelivery_OfflineSender)
{
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob", Account_Init);

   auto aliceEvent = std::async(std::launch::async, [&]() {
      XmppChatHandle aliceChat = alice.chat->createChat(alice.handle);
      alice.chat->addParticipant(aliceChat, bob.config.bare());
      alice.chat->start(aliceChat);

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(aliceChat, h);
         ASSERT_EQ(evt.chatType, ChatType_Outgoing);
      }

      XmppChatMessageHandle em1 = alice.chat->sendMessage(aliceChat, "message in a bottle");

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::SendMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onSendMessageSuccess", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.message, em1);
         ASSERT_EQ(aliceChat, h);
      }

      alice.disable();
      bob.enable();
      alice.enable();

      {
         XmppChatHandle h;
         MessageDeliveredEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onMessageDelivered", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         //ASSERT_EQ(aliceChat, h);
         //ASSERT_EQ(evt.message, em1);
      }
      
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      alice.disable();
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      bob.disable();
   });

   waitFor(aliceEvent);
}

#if (CPCAPI2_BRAND_XMPP_IM_COMMAND_MODULE == 1)
TEST_F(XmppChatModuleTest, IMCommand) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   auto aliceEvent = std::async(std::launch::async, [&]() {
      XmppChatHandle aliceChat = alice.chat->createChat(alice.handle);
      alice.chat->addParticipant(aliceChat, bob.config.bare());
      alice.chat->start(aliceChat);

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(aliceChat, h);
         ASSERT_EQ(evt.chatType, ChatType_Outgoing);
      }

      alice.imCommandManager->sendChatIMCommand(aliceChat, 2903, "test123");

      alice.imCommandManager->sendChatIMCommand(aliceChat, 2905, "");

      alice.chat->end(aliceChat);

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onChatEnded", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedLocally);
      }

   });

   auto bobEvent = std::async(std::launch::async, [&]() {
      XmppChatHandle bobChat = 0;

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewChat", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_NE(h, 0);
         ASSERT_EQ(evt.chatType, ChatType_Incoming);
         bobChat = h;
      }

      {
         XmppChatHandle h;
         CPCAPI2::XmppIMCommand::ChatIMCommandReceivedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppIMCommand::XmppChatIMCommandHandler::onIMCommandReceived", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.remote, alice.config.settings.username + "@" + alice.config.settings.domain);
         ASSERT_EQ(evt.payload, "test123");
         ASSERT_EQ(evt.type, 2903);
      }

      {
         XmppChatHandle h;
         CPCAPI2::XmppIMCommand::ChatIMCommandReceivedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppIMCommand::XmppChatIMCommandHandler::onIMCommandReceived", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.remote, alice.config.settings.username + "@" + alice.config.settings.domain);
         ASSERT_EQ(evt.payload, "");
         ASSERT_EQ(evt.type, 2905);
      }

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onChatEnded", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedRemotely);
      }
   });

   waitFor2(aliceEvent, bobEvent);
}
#endif // CPCAPI2_BRAND_XMPP_IM_COMMAND_MODULE

static void generateJwt(const resip::Data& p8file, const resip::Data& userIdentity, resip::Data& jwt)
{
   std::map<resip::Data, resip::Data> pubClaims;
   pubClaims["cp_user"] = userIdentity;
   CPCAPI2::AuthServer::JwtUtils::GenerateJWT(p8file, "CPCAPI2::AuthServer", pubClaims, 86400, jwt);
}

#if USE_CPCAPI2_JSON_TESTS
TEST_F(XmppChatModuleTest, BasicChat_JSON)
{
   XmppTestAccount bob("bob");
   
   XmppTestCloudAccount alice("alice");
   alice.enable();
   
   auto aliceEvent = std::async(std::launch::async, [&]()
   {
      XmppChatHandle aliceChat = alice.chatJson->createChat(alice.handle);
      alice.chatJson->addParticipant(aliceChat, bob.config.bare());
      alice.chatJson->start(aliceChat);

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewChat", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(aliceChat, h);
         ASSERT_EQ(evt.chatType, ChatType_Outgoing);
      }

      XmppChatMessageHandle cm1 = alice.chatJson->sendMessage(aliceChat, "test123");

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::SendMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onSendMessageSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.message, cm1);
      }

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::IsComposingMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onIsComposingMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.state, CPCAPI2::XmppChat::IsComposingMessageState_Active);
      }

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::NewMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.from, bob.config.bare());
         ASSERT_EQ(evt.messageContent, "test456");
      }
      
      alice.chatJson->validateChatHandle(alice.handle, aliceChat);
      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::ValidateChatHandleEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onValidateChatHandleResult", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_TRUE(evt.chatHandleValid);
      }

      alice.chatJson->end(aliceChat);

      alice.chatJson->validateChatHandle(alice.handle, aliceChat);
      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::ValidateChatHandleEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onValidateChatHandleResult", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_FALSE(evt.chatHandleValid);
      }

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onChatEnded", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedLocally);
      }

   });

   auto bobEvent = std::async(std::launch::async, [&]()
   {
      XmppChatHandle bobChat = 0;

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewChat", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_NE(h, 0);
         ASSERT_EQ(evt.chatType, ChatType_Incoming);
         bobChat = h;
      }

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::NewMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.from, alice.config.bare());
         ASSERT_EQ(evt.messageContent, "test123");
         ASSERT_TRUE(evt.message != 0);
         XmppChatMessageHandle bobRecvdCm1 = evt.message;
         bob.chat->notifyMessageDelivered(bobChat, bobRecvdCm1, CPCAPI2::XmppChat::MessageDeliveryStatus_Delivered);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      bob.chat->setIsComposingMessage(bobChat);
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      XmppChatMessageHandle bobCm1 = bob.chat->sendMessage(bobChat, "test456");

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::SendMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onSendMessageSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.message, bobCm1);
      }

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onChatEnded", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedRemotely);
      }
   });

   waitFor2(aliceEvent, bobEvent);

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   bob.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(3000));
}

TEST_F(XmppChatModuleTest, BasicChatEndAndSendAgain_JSON)
{
   XmppTestAccount bob("bob");
   
   XmppTestCloudAccount alice("alice");
   alice.enable();
   
   auto aliceEvent = std::async(std::launch::async, [&]()
   {
      XmppChatHandle aliceChat = alice.chatJson->createChat(alice.handle);
      alice.chatJson->addParticipant(aliceChat, bob.config.bare());
      alice.chatJson->start(aliceChat);

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewChat", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(aliceChat, h);
         ASSERT_EQ(evt.chatType, ChatType_Outgoing);
      }

      XmppChatMessageHandle cm1 = alice.chatJson->sendMessage(aliceChat, "test123");

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::SendMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onSendMessageSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.message, cm1);
      }

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::IsComposingMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onIsComposingMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.state, CPCAPI2::XmppChat::IsComposingMessageState_Active);
      }

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::NewMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.from, bob.config.bare());
         ASSERT_EQ(evt.messageContent, "test456");
      }
      
      alice.chatJson->validateChatHandle(alice.handle, aliceChat);
      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::ValidateChatHandleEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onValidateChatHandleResult", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_TRUE(evt.chatHandleValid);
      }

      alice.chatJson->end(aliceChat);

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onChatEnded", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedLocally);
      }
      
      std::this_thread::sleep_for(std::chrono::milliseconds(3000));
      // expect bob to send another chat, but this time it should come with a new chat handle

      XmppChatHandle aliceChat2 = 0;

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewChat", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.chatType, ChatType_Incoming);
         aliceChat2 = h;
      }
      
      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::NewMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.from, bob.config.bare());
         ASSERT_EQ(evt.messageContent, "test789");
         ASSERT_TRUE(evt.message != 0);
         XmppChatMessageHandle aliceRecvdCm1 = evt.message;
         alice.chatJson->notifyMessageDelivered(aliceChat2, aliceRecvdCm1, CPCAPI2::XmppChat::MessageDeliveryStatus_Delivered);
      }

   });

   auto bobEvent = std::async(std::launch::async, [&]()
   {
      XmppChatHandle bobChat = 0;

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewChat", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_NE(h, 0);
         ASSERT_EQ(evt.chatType, ChatType_Incoming);
         bobChat = h;
      }

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::NewMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.from, alice.config.bare());
         ASSERT_EQ(evt.messageContent, "test123");
         ASSERT_TRUE(evt.message != 0);
         XmppChatMessageHandle bobRecvdCm1 = evt.message;
         bob.chat->notifyMessageDelivered(bobChat, bobRecvdCm1, CPCAPI2::XmppChat::MessageDeliveryStatus_Delivered);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      bob.chat->setIsComposingMessage(bobChat);
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      XmppChatMessageHandle bobCm1 = bob.chat->sendMessage(bobChat, "test456");

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::SendMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onSendMessageSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.message, bobCm1);
      }

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onChatEnded", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedRemotely);
      }
      
      std::this_thread::sleep_for(std::chrono::milliseconds(3000));
      safeCout("===== Starting new chat =====");
      
      XmppChatHandle bobChat2 = bob.chat->createChat(bob.handle);
      bob.chat->addParticipant(bobChat2, alice.config.bare());
      bob.chat->start(bobChat2);

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewChat", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobChat2, h);
         ASSERT_EQ(evt.chatType, ChatType_Outgoing);
      }
      
      XmppChatMessageHandle cm1 = bob.chat->sendMessage(bobChat2, "test789");
      
      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::SendMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onSendMessageSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.message, cm1);
      }
      
      
   });

   waitFor2(aliceEvent, bobEvent);

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   bob.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(3000));
}

TEST_F(XmppChatModuleTest, BasicHtmlChat_JSON)
{
   XmppTestCloudAccount alice("alice");
   alice.enable();

   XmppTestAccount bob("bob");
   
   auto aliceEvent = std::async(std::launch::async, [&]()
   {
      XmppChatHandle aliceChat = alice.chatJson->createChat(alice.handle);
      alice.chatJson->addParticipant(aliceChat, bob.config.bare());
      alice.chatJson->start(aliceChat);

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewChat", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(aliceChat, h);
         ASSERT_EQ(evt.chatType, ChatType_Outgoing);
      }

      XmppChatMessageHandle cm1 = alice.chatJson->sendMessage(aliceChat, "", "html");

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::SendMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onSendMessageSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.message, cm1);
      }

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::IsComposingMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onIsComposingMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.state, CPCAPI2::XmppChat::IsComposingMessageState_Active);
      }

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::NewMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.from, bob.config.bare());
         ASSERT_EQ(evt.htmlText, "html");
      }

      alice.chatJson->end(aliceChat);

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onChatEnded", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedLocally);
      }

   });

   auto bobEvent = std::async(std::launch::async, [&]()
   {
      XmppChatHandle bobChat = 0;

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewChat", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_NE(h, 0);
         ASSERT_EQ(evt.chatType, ChatType_Incoming);
         bobChat = h;
      }

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::NewMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.from, alice.config.bare());
         ASSERT_EQ(evt.htmlText, "html");
         ASSERT_TRUE(evt.message != 0);
         XmppChatMessageHandle bobRecvdCm1 = evt.message;
         bob.chat->notifyMessageDelivered(bobChat, bobRecvdCm1, CPCAPI2::XmppChat::MessageDeliveryStatus_Delivered);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      bob.chat->setIsComposingMessage(bobChat);
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      XmppChatMessageHandle bobCm1 = bob.chat->sendMessage(bobChat, "", "html");

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::SendMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onSendMessageSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.message, bobCm1);
      }

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onChatEnded", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedRemotely);
      }
   });

   waitFor2(aliceEvent, bobEvent);

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   alice.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   bob.disable();
   std::this_thread::sleep_for(std::chrono::milliseconds(3000));
}
#endif // USE_CPCAPI2_JSON_TESTS

// Test case for OBELISK-4523.
TEST_F(XmppChatModuleTest, XmppDisableAndDestroyAccount) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");
   
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   
   CPCAPI2::XmppChat::XmppChatHandle aliceChat = alice.chat->createChat(alice.handle);
   alice.chat->addParticipant(aliceChat, bob.config.bare());
   alice.chat->start(aliceChat);
   
   std::this_thread::sleep_for(std::chrono::milliseconds(1000));
   alice.account->disable(alice.handle);
   alice.account->destroy(alice.handle);
   
   // Wait up to 2 min for the crash.
   std::this_thread::sleep_for(std::chrono::milliseconds(120000));

   // Manually release alice.
   delete alice.events;
#if (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE==1)
   delete alice.jsonApiClientEvents;
#endif
#if (CPCAPI2_BRAND_JSON_API_SERVER_MODULE==1)
   delete alice.jsonApiServerEvents;
#endif
#if (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE==1)
   delete alice.agentJsonEvents;
#endif
#if (CPCAPI2_BRAND_PUSH_NOTIFICATION_CLIENT_MODULE==1)
   delete alice.pushJsonEvents;
#endif
#if (CPCAPI2_BRAND_CLOUD_CONNECTOR_MODULE==1)
   delete alice.cloudConnectorEvents;
#endif
#if (CPCAPI2_BRAND_MESSAGESTORE_MODULE==1)
   delete alice.messageStoreEvents;
#endif
   alice.setInitialized(false);
}

TEST_F(XmppChatModuleTest, GetRemoteSyncConversationID)
{
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");
   
   XmppChatHandle aliceChat = alice.chat->createChat(alice.handle);
   alice.chat->addParticipant(aliceChat, bob.config.bare());
   alice.chat->start(aliceChat);
   
   {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewChatEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.chatType, ChatType_Outgoing);
   }

   cpc::string conversationId = alice.chat->getRemoteSyncConversationID(aliceChat);

   std::stringstream expectedConversationId;
   expectedConversationId << "xmpp:" << alice.config.settings.username << "@" << alice.config.settings.domain << ":" << bob.config.settings.username << "@" << bob.config.settings.domain;

   ASSERT_EQ(expectedConversationId.str(), std::string(conversationId.c_str()));
}

TEST_F(XmppChatModuleTest, DISABLED_ReadReceiptWithMessageHandle)
{
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob", Account_Init);

   XmppChatHandle aliceChat = alice.chat->createChat(alice.handle);
   alice.chat->addParticipant(aliceChat, bob.config.bare());
   alice.chat->start(aliceChat);

   {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewChatEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.chatType, ChatType_Outgoing);
   }

   XmppChatMessageHandle cm1 = alice.chat->sendMessage(aliceChat, "test123");
   cpc::string messageId1;
   cpc::string threadId1;

   {
      XmppChatHandle h;
      CPCAPI2::XmppChat::SendMessageSuccessEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onSendMessageSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.message, cm1);
      messageId1 = evt.messageId;
      threadId1 = evt.threadId;
   }

   alice.chat->end(aliceChat);

   aliceChat = alice.chat->createChat(alice.handle);
   alice.chat->addParticipant(aliceChat, bob.config.bare());
   alice.chat->start(aliceChat);

   {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewChatEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.chatType, ChatType_Outgoing);
   }

   XmppChatMessageHandle cm2 = alice.chat->sendMessage(aliceChat, "test456");
   cpc::string messageId2;
   cpc::string threadId2;

   {
      XmppChatHandle h;
      CPCAPI2::XmppChat::SendMessageSuccessEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onSendMessageSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.message, cm2);
      messageId2 = evt.messageId;
      threadId2 = evt.threadId;
   }

   ASSERT_NE(threadId1, threadId2);

   alice.disable();

   bob.enable();

   XmppChatHandle bobChat = 0;

   {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewChatEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewChat", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_NE(h, 0);
      ASSERT_EQ(evt.chatType, ChatType_Incoming);
      bobChat = h;
   }

   {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.from, alice.config.bare());
      ASSERT_EQ(evt.messageContent, "test123");
      ASSERT_TRUE(evt.message != 0);
      ASSERT_EQ(evt.messageId, messageId1);
      ASSERT_EQ(evt.threadId, threadId1);

      bob.chat->notifyMessageRead(bobChat, evt.message);
   }

   {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.from, alice.config.bare());
      ASSERT_EQ(evt.messageContent, "test456");
      ASSERT_TRUE(evt.message != 0);
      ASSERT_EQ(evt.messageId, messageId2);
      ASSERT_EQ(evt.threadId, threadId2);

      bob.chat->notifyMessageRead(bobChat, evt.message);
   }

   std::this_thread::sleep_for(std::chrono::seconds(2));

   alice.enable();

   {
      // Wait for the message delivery notification (from Bob)
      XmppChatHandle h;
      MessageDeliveredEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onMessageDelivered", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.messageId, messageId1);
      ASSERT_EQ(evt.threadId, threadId1);
      ASSERT_TRUE(evt.isDelayedDelivery);
   }

   {
      // Wait for the message read receipt (from Bob)
      XmppChatHandle h;
      MessageReadEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onMessageRead", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.messageId, messageId1);
      ASSERT_EQ(evt.threadId, threadId1);
      ASSERT_TRUE(evt.isDelayedDelivery);
   }

   {
      // Wait for the message delivery notification (from Bob)
      XmppChatHandle h;
      MessageDeliveredEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onMessageDelivered", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.messageId, messageId2);
      ASSERT_EQ(evt.threadId, threadId2);
      ASSERT_TRUE(evt.isDelayedDelivery);
   }

   {
      // Wait for the message read receipt (from Bob)
      XmppChatHandle h;
      MessageReadEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onMessageRead", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.messageId, messageId2);
      ASSERT_EQ(evt.threadId, threadId2);
      ASSERT_TRUE(evt.isDelayedDelivery);
   }
}

TEST_F(XmppChatModuleTest, ReadReceiptWithoutMessageHandle)
{
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob", Account_Init);

   XmppChatHandle aliceChat = alice.chat->createChat(alice.handle);
   alice.chat->addParticipant(aliceChat, bob.config.bare());
   alice.chat->start(aliceChat);

   {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewChatEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.chatType, ChatType_Outgoing);
   }

   XmppChatMessageHandle cm1 = alice.chat->sendMessage(aliceChat, "test123");
   cpc::string messageId1;
   cpc::string threadId1;

   {
      XmppChatHandle h;
      CPCAPI2::XmppChat::SendMessageSuccessEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onSendMessageSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.message, cm1);
      messageId1 = evt.messageId;
      threadId1 = evt.threadId;
   }

   alice.chat->end(aliceChat);

   aliceChat = alice.chat->createChat(alice.handle);
   alice.chat->addParticipant(aliceChat, bob.config.bare());
   alice.chat->start(aliceChat);

   {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewChatEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.chatType, ChatType_Outgoing);
   }

   XmppChatMessageHandle cm2 = alice.chat->sendMessage(aliceChat, "test456");
   cpc::string messageId2;
   cpc::string threadId2;

   {
      XmppChatHandle h;
      CPCAPI2::XmppChat::SendMessageSuccessEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onSendMessageSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.message, cm2);
      messageId2 = evt.messageId;
      threadId2 = evt.threadId;
   }

   ASSERT_NE(threadId1, threadId2);

   alice.disable();

   bob.enable();

   XmppChatHandle bobChat = 0;

   {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewChatEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewChat", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_NE(h, 0);
      ASSERT_EQ(evt.chatType, ChatType_Incoming);
      bobChat = h;
   }

   {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.from, alice.config.bare());
      ASSERT_EQ(evt.messageContent, "test123");
      ASSERT_TRUE(evt.message != 0);
      ASSERT_EQ(evt.messageId, messageId1);
      ASSERT_EQ(evt.threadId, threadId1);

      bob.chat->notifyMessageRead(bobChat, evt.threadId, evt.messageId);
   }

   {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.from, alice.config.bare());
      ASSERT_EQ(evt.messageContent, "test456");
      ASSERT_TRUE(evt.message != 0);
      ASSERT_EQ(evt.messageId, messageId2);
      ASSERT_EQ(evt.threadId, threadId2);

      bob.chat->notifyMessageRead(bobChat, evt.threadId, evt.messageId);
   }

   std::this_thread::sleep_for(std::chrono::seconds(2));

   alice.enable();

   {
      // Wait for the message delivery notification (from Bob)
      XmppChatHandle h;
      MessageDeliveredEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onMessageDelivered", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.messageId, messageId1);
      ASSERT_EQ(evt.threadId, threadId1);
      ASSERT_TRUE(evt.isDelayedDelivery);
   }

   {
      // Wait for the message read receipt (from Bob)
      XmppChatHandle h;
      MessageReadEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onMessageRead", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.messageId, messageId1);
      ASSERT_EQ(evt.threadId, threadId1);
      ASSERT_TRUE(evt.isDelayedDelivery);
   }

   {
      // Wait for the message delivery notification (from Bob)
      XmppChatHandle h;
      MessageDeliveredEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onMessageDelivered", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.messageId, messageId2);
      ASSERT_EQ(evt.threadId, threadId2);
      ASSERT_TRUE(evt.isDelayedDelivery);
   }

   {
      // Wait for the message read receipt (from Bob)
      XmppChatHandle h;
      MessageReadEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onMessageRead", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.messageId, messageId2);
      ASSERT_EQ(evt.threadId, threadId2);
      ASSERT_TRUE(evt.isDelayedDelivery);
   }
}

TEST_F(XmppChatModuleTest, ReadReceiptWithoutChatHandle)
{
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob", Account_Init);

   XmppChatHandle aliceChat = alice.chat->createChat(alice.handle);
   alice.chat->addParticipant(aliceChat, bob.config.bare());
   alice.chat->start(aliceChat);

   {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewChatEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.chatType, ChatType_Outgoing);
   }

   XmppChatMessageHandle cm1 = alice.chat->sendMessage(aliceChat, "test123");
   cpc::string messageId1;
   cpc::string threadId1;

   {
      XmppChatHandle h;
      CPCAPI2::XmppChat::SendMessageSuccessEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onSendMessageSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.message, cm1);
      messageId1 = evt.messageId;
      threadId1 = evt.threadId;
   }

   alice.chat->end(aliceChat);

   aliceChat = alice.chat->createChat(alice.handle);
   alice.chat->addParticipant(aliceChat, bob.config.bare());
   alice.chat->start(aliceChat);

   {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewChatEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.chatType, ChatType_Outgoing);
   }

   XmppChatMessageHandle cm2 = alice.chat->sendMessage(aliceChat, "test456");
   cpc::string messageId2;
   cpc::string threadId2;

   {
      XmppChatHandle h;
      CPCAPI2::XmppChat::SendMessageSuccessEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onSendMessageSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.message, cm2);
      messageId2 = evt.messageId;
      threadId2 = evt.threadId;
   }

   ASSERT_NE(threadId1, threadId2);

   alice.disable();

   bob.enable();

   XmppChatHandle bobChat = 0;

   {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewChatEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewChat", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_NE(h, 0);
      ASSERT_EQ(evt.chatType, ChatType_Incoming);
      bobChat = h;
   }

   {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.from, alice.config.bare());
      ASSERT_EQ(evt.messageContent, "test123");
      ASSERT_TRUE(evt.message != 0);
      ASSERT_EQ(evt.messageId, messageId1);
      ASSERT_EQ(evt.threadId, threadId1);

      bob.chat->notifyMessageRead(bob.handle, evt.from, evt.threadId, evt.messageId);
   }

   {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.from, alice.config.bare());
      ASSERT_EQ(evt.messageContent, "test456");
      ASSERT_TRUE(evt.message != 0);
      ASSERT_EQ(evt.messageId, messageId2);
      ASSERT_EQ(evt.threadId, threadId2);

      bob.chat->notifyMessageRead(bob.handle, evt.from, evt.threadId, evt.messageId);
   }

   std::this_thread::sleep_for(std::chrono::seconds(2));

   alice.enable();

   {
      // Wait for the message delivery notification (from Bob)
      XmppChatHandle h;
      MessageDeliveredEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onMessageDelivered", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.messageId, messageId1);
      ASSERT_EQ(evt.threadId, threadId1);
      ASSERT_TRUE(evt.isDelayedDelivery);
   }

   {
      // Wait for the message read receipt (from Bob)
      XmppChatHandle h;
      MessageReadEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onMessageRead", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.messageId, messageId1);
      ASSERT_EQ(evt.threadId, threadId1);
      ASSERT_TRUE(evt.isDelayedDelivery);
   }

   {
      // Wait for the message delivery notification (from Bob)
      XmppChatHandle h;
      MessageDeliveredEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onMessageDelivered", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.messageId, messageId2);
      ASSERT_EQ(evt.threadId, threadId2);
      ASSERT_TRUE(evt.isDelayedDelivery);
   }

   {
      // Wait for the message read receipt (from Bob)
      XmppChatHandle h;
      MessageReadEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onMessageRead", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.messageId, messageId2);
      ASSERT_EQ(evt.threadId, threadId2);
      ASSERT_TRUE(evt.isDelayedDelivery);
   }
}

// OBELISK-5965
TEST_F(XmppChatModuleTest, ReadReceiptWithoutNetwork)
{
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob", Account_Init);

   XmppChatHandle aliceChat = alice.chat->createChat(alice.handle);
   alice.chat->addParticipant(aliceChat, bob.config.bare());
   alice.chat->start(aliceChat);

   {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewChatEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.chatType, ChatType_Outgoing);
   }

   XmppChatMessageHandle cm1 = alice.chat->sendMessage(aliceChat, "test123");
   cpc::string messageId1;
   cpc::string threadId1;

   {
      XmppChatHandle h;
      CPCAPI2::XmppChat::SendMessageSuccessEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onSendMessageSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.message, cm1);
      messageId1 = evt.messageId;
      threadId1 = evt.threadId;
   }

   alice.chat->end(aliceChat);

   aliceChat = alice.chat->createChat(alice.handle);
   alice.chat->addParticipant(aliceChat, bob.config.bare());
   alice.chat->start(aliceChat);

   {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewChatEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.chatType, ChatType_Outgoing);
   }

   XmppChatMessageHandle cm2 = alice.chat->sendMessage(aliceChat, "test456");
   cpc::string messageId2;
   cpc::string threadId2;

   {
      XmppChatHandle h;
      CPCAPI2::XmppChat::SendMessageSuccessEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onSendMessageSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.message, cm2);
      messageId2 = evt.messageId;
      threadId2 = evt.threadId;
   }

   ASSERT_NE(threadId1, threadId2);

   alice.disable();

   bob.enable();

   XmppChatHandle bobChat = 0;

   {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewChatEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewChat", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_NE(h, 0);
      ASSERT_EQ(evt.chatType, ChatType_Incoming);
      bobChat = h;
   }

   {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.from, alice.config.bare());
      ASSERT_EQ(evt.messageContent, "test123");
      ASSERT_TRUE(evt.message != 0);
      ASSERT_EQ(evt.messageId, messageId1);
      ASSERT_EQ(evt.threadId, threadId1);
   }

   {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.from, alice.config.bare());
      ASSERT_EQ(evt.messageContent, "test456");
      ASSERT_TRUE(evt.message != 0);
      ASSERT_EQ(evt.messageId, messageId2);
      ASSERT_EQ(evt.threadId, threadId2);
   }

   std::this_thread::sleep_for(std::chrono::seconds(2));

   bob.disconnectNetwork(false);
   assertXmppDisconnecting(bob);
   assertXmppDisconnected(bob);

   bob.chat->notifyMessageRead(bobChat, threadId1, messageId1);
   bob.chat->notifyMessageRead(bob.handle, alice.config.bare(), threadId2, messageId2);

   std::this_thread::sleep_for(std::chrono::seconds(2));
}

TEST_F(XmppChatModuleTest, XmppUnrequestedAckOn) {
   XmppTestAccount alice("alice", Account_NoInit);
   XmppTestAccount bob("bob", Account_NoInit);

   ASSERT_TRUE(bob.config.settings.enableStreamManagement);
   bob.config.settings.unrequestedAckSendIntervalSec = 2;   // enabled with 2sec checking

   alice.enable();
   bob.enable();

   auto aliceEvent = std::async(std::launch::async, [&] () {
      XmppChatHandle aliceChat = alice.chat->createChat(alice.handle);
      alice.chat->addParticipant(aliceChat, bob.config.bare());
      alice.chat->start(aliceChat);

      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewChatEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.chatType, ChatType_Outgoing);
      }

      int startAckCount = xmppUnrequestedAckCount;

      for (int i = 0; i < 5; i++)
      {
         XmppChatMessageHandle cm1 = alice.chat->sendMessage(aliceChat, "test123");
         {
         XmppChatHandle h;
         CPCAPI2::XmppChat::SendMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onSendMessageSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.message, cm1);
         }
      }

      // wait long enough for at least one unrequested ACK to be sent
      std::this_thread::sleep_for(std::chrono::milliseconds(3000));

      alice.chat->end(aliceChat);

      int endAckCount = xmppUnrequestedAckCount;      
      ASSERT_TRUE(startAckCount < endAckCount);

      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::ChatEndedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onChatEnded", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedLocally);
      }
      
      alice.chat->validateChatHandle(alice.handle, aliceChat);
      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::ValidateChatHandleEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onValidateChatHandleResult", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_FALSE(evt.chatHandleValid);
      }
   });

   auto bobEvent = std::async(std::launch::async, [&] () {
      XmppChatHandle bobChat = 0;

      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewChatEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewChat", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_NE(h, 0);
      ASSERT_EQ(evt.chatType, ChatType_Incoming);
      bobChat = h;
      }

      int startAckCount = xmppUnrequestedAckCount;

      for (int i = 0; i < 5; i++)
      {
         {
         XmppChatHandle h;
         CPCAPI2::XmppChat::NewMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.from, alice.config.bare());
         ASSERT_EQ(evt.messageContent, "test123");
         ASSERT_TRUE(evt.message != 0);
         XmppChatMessageHandle bobRecvdCm1 = evt.message;
         bob.chat->notifyMessageDelivered(bobChat, bobRecvdCm1, CPCAPI2::XmppChat::MessageDeliveryStatus_Delivered);
         }
      }

      // wait long enough for at least one unrequested ACK to be sent
      std::this_thread::sleep_for(std::chrono::milliseconds(3000));

      int endAckCount = xmppUnrequestedAckCount;      
      ASSERT_TRUE(startAckCount < endAckCount);

      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::ChatEndedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onChatEnded", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedRemotely);
      }
   });

   waitFor2(aliceEvent, bobEvent);
}

TEST_F(XmppChatModuleTest, XmppUnrequestedAckOff) {
   XmppTestAccount alice("alice", Account_NoInit);
   XmppTestAccount bob("bob", Account_NoInit);

   ASSERT_TRUE(bob.config.settings.enableStreamManagement);
   bob.config.settings.unrequestedAckSendIntervalSec = 0;   // disabled

   alice.enable();
   bob.enable();

   auto aliceEvent = std::async(std::launch::async, [&] () {
      XmppChatHandle aliceChat = alice.chat->createChat(alice.handle);
      alice.chat->addParticipant(aliceChat, bob.config.bare());
      alice.chat->start(aliceChat);

      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewChatEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.chatType, ChatType_Outgoing);
      }

      int startAckCount = xmppUnrequestedAckCount;

      for (int i = 0; i < 5; i++)
      {
         XmppChatMessageHandle cm1 = alice.chat->sendMessage(aliceChat, "test123");
         {
         XmppChatHandle h;
         CPCAPI2::XmppChat::SendMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onSendMessageSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.message, cm1);
         }
      }

      // wait long enough for at least one unrequested ACK to be sent
      std::this_thread::sleep_for(std::chrono::milliseconds(3000));

      alice.chat->end(aliceChat);

      int endAckCount = xmppUnrequestedAckCount;      
      ASSERT_TRUE(startAckCount == endAckCount);

      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::ChatEndedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onChatEnded", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedLocally);
      }
      
      alice.chat->validateChatHandle(alice.handle, aliceChat);
      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::ValidateChatHandleEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onValidateChatHandleResult", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_FALSE(evt.chatHandleValid);
      }
   });

   auto bobEvent = std::async(std::launch::async, [&] () {
      XmppChatHandle bobChat = 0;

      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewChatEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewChat", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_NE(h, 0);
      ASSERT_EQ(evt.chatType, ChatType_Incoming);
      bobChat = h;
      }

      int startAckCount = xmppUnrequestedAckCount;

      for (int i = 0; i < 5; i++)
      {
         {
         XmppChatHandle h;
         CPCAPI2::XmppChat::NewMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.from, alice.config.bare());
         ASSERT_EQ(evt.messageContent, "test123");
         ASSERT_TRUE(evt.message != 0);
         XmppChatMessageHandle bobRecvdCm1 = evt.message;
         bob.chat->notifyMessageDelivered(bobChat, bobRecvdCm1, CPCAPI2::XmppChat::MessageDeliveryStatus_Delivered);
         }
      }

      // wait long enough for at least one unrequested ACK to be sent
      std::this_thread::sleep_for(std::chrono::milliseconds(3000));

      int endAckCount = xmppUnrequestedAckCount;      
      ASSERT_TRUE(startAckCount == endAckCount);

      {
      XmppChatHandle h;
      CPCAPI2::XmppChat::ChatEndedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onChatEnded", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedRemotely);
      }
   });

   waitFor2(aliceEvent, bobEvent);
}

TEST_F(XmppChatModuleTest, DeliveryReadReceiptsLegacyChatDisabled)
{
   XmppTestAccount alice("alice", Account_NoInit);
   alice.config.settings.legacyChatXepSupport = false;
   alice.config.settings.enableChatDeliveryReceipts = true;
   alice.config.settings.enableChatReadReceipts = true;
   alice.enable();
   XmppTestAccount bob("bob", Account_NoInit);
   bob.config.settings.legacyChatXepSupport = false;
   bob.config.settings.enableChatDeliveryReceipts = true;
   bob.config.settings.enableChatReadReceipts = true;
   bob.init();

   XmppChatHandle aliceChat = alice.chat->createChat(alice.handle);
   alice.chat->addParticipant(aliceChat, bob.config.bare());
   alice.chat->start(aliceChat);

   {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewChatEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.chatType, ChatType_Outgoing);
   }

   XmppChatMessageHandle cm1 = alice.chat->sendMessage(aliceChat, "test123");
   cpc::string messageId1;
   cpc::string threadId1;

   {
      XmppChatHandle h;
      CPCAPI2::XmppChat::SendMessageSuccessEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onSendMessageSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.message, cm1);
      messageId1 = evt.messageId;
      threadId1 = evt.threadId;
   }

   alice.chat->end(aliceChat);

   aliceChat = alice.chat->createChat(alice.handle);
   alice.chat->addParticipant(aliceChat, bob.config.bare());
   alice.chat->start(aliceChat);

   {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewChatEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.chatType, ChatType_Outgoing);
   }

   XmppChatMessageHandle cm2 = alice.chat->sendMessage(aliceChat, "test456");
   cpc::string messageId2;
   cpc::string threadId2;

   {
      XmppChatHandle h;
      CPCAPI2::XmppChat::SendMessageSuccessEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onSendMessageSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.message, cm2);
      messageId2 = evt.messageId;
      threadId2 = evt.threadId;
   }

   ASSERT_NE(threadId1, threadId2);

   alice.disable();

   bob.enable();

   XmppChatHandle bobChat = 0;

   {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewChatEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewChat", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_NE(h, 0);
      ASSERT_EQ(evt.chatType, ChatType_Incoming);
      bobChat = h;
   }

   {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.from, alice.config.bare());
      ASSERT_EQ(evt.messageContent, "test123");
      ASSERT_TRUE(evt.message != 0);
      ASSERT_EQ(evt.messageId, messageId1);
      ASSERT_EQ(evt.threadId, threadId1);

      bob.chat->notifyMessageRead(bobChat, evt.threadId, evt.messageId);
   }

   {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.from, alice.config.bare());
      ASSERT_EQ(evt.messageContent, "test456");
      ASSERT_TRUE(evt.message != 0);
      ASSERT_EQ(evt.messageId, messageId2);
      ASSERT_EQ(evt.threadId, threadId2);

      bob.chat->notifyMessageRead(bobChat, evt.threadId, evt.messageId);
   }

   std::this_thread::sleep_for(std::chrono::seconds(2));

   alice.enable();

   {
      // Wait for the message delivery notification (from Bob)
      XmppChatHandle h;
      MessageDeliveredEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onMessageDelivered", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.messageId, messageId1);
      ASSERT_EQ(evt.threadId, threadId1);
      ASSERT_TRUE(evt.isDelayedDelivery);
   }

   {
      // Wait for the message read receipt (from Bob)
      XmppChatHandle h;
      MessageReadEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onMessageRead", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.messageId, messageId1);
      ASSERT_EQ(evt.threadId, threadId1);
      ASSERT_TRUE(evt.isDelayedDelivery);
   }

   {
      // Wait for the message delivery notification (from Bob)
      XmppChatHandle h;
      MessageDeliveredEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onMessageDelivered", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.messageId, messageId2);
      ASSERT_EQ(evt.threadId, threadId2);
      ASSERT_TRUE(evt.isDelayedDelivery);
   }

   {
      // Wait for the message read receipt (from Bob)
      XmppChatHandle h;
      MessageReadEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onMessageRead", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.messageId, messageId2);
      ASSERT_EQ(evt.threadId, threadId2);
      ASSERT_TRUE(evt.isDelayedDelivery);
   }
}

TEST_F(XmppChatModuleTest, ReplaceMessage)
{
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob", Account_Init);

   XmppChatHandle aliceChat = alice.chat->createChat(alice.handle);
   alice.chat->addParticipant(aliceChat, bob.config.bare());
   alice.chat->start(aliceChat);

   {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewChatEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceChat, h);
      ASSERT_EQ(evt.chatType, ChatType_Outgoing);
   }

   XmppChatMessageHandle cm1 = alice.chat->sendMessage(aliceChat, "badmsg test123");
   cpc::string messageId1;
   cpc::string threadId1;

   {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewOutboundMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.message, cm1);
      messageId1 = evt.messageId;
      threadId1 = evt.threadId;
      ASSERT_TRUE(evt.isOutbound);
      ASSERT_TRUE(evt.replaces.empty());
   }

   XmppChatMessageHandle cm2 = alice.chat->replaceMessage(aliceChat, messageId1, "good msg test456");
   cpc::string messageId2;

   {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewOutboundMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.message, cm2);
      messageId2 = evt.messageId;
      ASSERT_TRUE(evt.isOutbound);
      ASSERT_EQ(evt.replaces, messageId1);
   }

   XmppChatMessageHandle cm3 = alice.chat->sendMessage(aliceChat, "some other message");
   cpc::string messageId3;
   
   {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewOutboundMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.message, cm3);
      messageId3 = evt.messageId;
      ASSERT_TRUE(evt.isOutbound);
      ASSERT_TRUE(evt.replaces.empty());
   }

   alice.disable();

   bob.enable();

   XmppChatHandle bobChat = 0;

   {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewChatEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewChat", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_NE(h, 0);
      ASSERT_EQ(evt.chatType, ChatType_Incoming);
      bobChat = h;
   }

   {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.from, alice.config.bare());
      ASSERT_EQ(evt.messageContent, "badmsg test123");
      ASSERT_TRUE(evt.message != 0);
      ASSERT_EQ(evt.messageId, messageId1);
      ASSERT_EQ(evt.threadId, threadId1);
      ASSERT_TRUE(evt.replaces.empty());

      bob.chat->notifyMessageRead(bobChat, evt.threadId, evt.messageId);
   }

   {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.from, alice.config.bare());
      ASSERT_EQ(evt.messageContent, "good msg test456");
      ASSERT_TRUE(evt.message != 0);
      ASSERT_EQ(evt.messageId, messageId2);
      ASSERT_EQ(evt.threadId, threadId1);
      ASSERT_EQ(evt.replaces, messageId1);
   }

   {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewMessageEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.from, alice.config.bare());
      ASSERT_EQ(evt.messageContent, "some other message");
      ASSERT_TRUE(evt.message != 0);
      ASSERT_EQ(evt.messageId, messageId3);
      ASSERT_EQ(evt.threadId, threadId1);
      ASSERT_TRUE(evt.replaces.empty());
   }

   std::this_thread::sleep_for(std::chrono::seconds(2));
}

TEST_F(XmppChatModuleTest, PeerDiscovery)
{
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");
   XmppTestAccount max("max");
   XmppTestAccount tim("tim");

   auto aliceEvents = std::async(std::launch::async, [&] () {
      // ----------------------------- alice sending messages to bob -----------------------------
      XmppChatHandle aliceBobChat = alice.chat->createChat(alice.handle);
      alice.chat->addParticipant(aliceBobChat, bob.config.full());
      alice.chat->start(aliceBobChat);

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(aliceBobChat, h);
         ASSERT_EQ(evt.chatType, ChatType_Outgoing);
      }

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::ChatDiscoEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onChatDiscoCompleted", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(aliceBobChat, h);
         ASSERT_EQ(evt.remoteJID, bob.config.full());
         ASSERT_TRUE(evt.replaceMessageSupported);
      }

      XmppChatMessageHandle cm1 = alice.chat->sendMessage(aliceBobChat, "AliceBob123");
      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::SendMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onSendMessageSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.message, cm1);
      }
      
      {
         // Wait for the message delivery notification (from Bob)
         XmppChatHandle h;
         MessageDeliveredEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onMessageDelivered", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.message, cm1);
      }

      alice.chat->end(aliceBobChat);
      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onChatEnded", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedLocally);
      }
      
      alice.chat->validateChatHandle(alice.handle, aliceBobChat);
      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::ValidateChatHandleEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onValidateChatHandleResult", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_FALSE(evt.chatHandleValid);
      }

      // ----------------------------- alice sending messages to max -----------------------------
      XmppChatHandle aliceMaxChat = alice.chat->createChat(alice.handle);
      alice.chat->addParticipant(aliceMaxChat, max.config.full());
      alice.chat->start(aliceMaxChat);

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(aliceMaxChat, h);
         ASSERT_EQ(evt.chatType, ChatType_Outgoing);
      }

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::ChatDiscoEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onChatDiscoCompleted", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(aliceMaxChat, h);
         ASSERT_EQ(evt.remoteJID, max.config.full());
         ASSERT_TRUE(evt.replaceMessageSupported);
      }

      XmppChatMessageHandle cm2 = alice.chat->sendMessage(aliceMaxChat, "AliceMax123");
      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::SendMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onSendMessageSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.message, cm2);
      }
      
      {
         // Wait for the message delivery notification (from Max)
         XmppChatHandle h;
         MessageDeliveredEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onMessageDelivered", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.message, cm2);
      }

      alice.chat->end(aliceMaxChat);
      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onChatEnded", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedLocally);
      }
      
      alice.chat->validateChatHandle(alice.handle, aliceMaxChat);
      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::ValidateChatHandleEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onValidateChatHandleResult", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_FALSE(evt.chatHandleValid);
      }

      // ----------------------------- alice sending messages to tim -----------------------------
      XmppChatHandle aliceTimChat = alice.chat->createChat(alice.handle);
      alice.chat->addParticipant(aliceTimChat, tim.config.bare());
      alice.chat->start(aliceTimChat);

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(aliceTimChat, h);
         ASSERT_EQ(evt.chatType, ChatType_Outgoing);
      }

      XmppChatMessageHandle cm3 = alice.chat->sendMessage(aliceTimChat, "AliceTim123");
      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::SendMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onSendMessageSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.message, cm3);
      }
      
      {
         // Wait for the message delivery notification (from Tim)
         XmppChatHandle h;
         MessageDeliveredEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onMessageDelivered", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.message, cm3);
      }

      alice.chat->end(aliceTimChat);
      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onChatEnded", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedLocally);
      }
      
      alice.chat->validateChatHandle(alice.handle, aliceTimChat);
      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::ValidateChatHandleEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onValidateChatHandleResult", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_FALSE(evt.chatHandleValid);
      }
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      // ----------------------------- bob receiving messages from alice -----------------------------
      XmppChatHandle bobAliceChat = 0;

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewChat", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_NE(h, 0);
         ASSERT_EQ(evt.chatType, ChatType_Incoming);
         bobAliceChat = h;
      }

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::NewMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.from, alice.config.bare());
         ASSERT_EQ(evt.messageContent, "AliceBob123");
         ASSERT_TRUE(evt.message != 0);
         XmppChatMessageHandle bobRecvdCm1 = evt.message;
         bob.chat->notifyMessageDelivered(bobAliceChat, bobRecvdCm1, CPCAPI2::XmppChat::MessageDeliveryStatus_Delivered);
      }

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onChatEnded", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedRemotely);
      }
   });

   auto maxEvents = std::async(std::launch::async, [&] () {
      // ----------------------------- max receiving messages from alice -----------------------------
      XmppChatHandle maxAliceChat = 0;

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(max.events, "XmppChatHandler::onNewChat", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_NE(h, 0);
         ASSERT_EQ(evt.chatType, ChatType_Incoming);
         maxAliceChat = h;
      }

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::NewMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(max.events, "XmppChatHandler::onNewMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.from, alice.config.bare());
         ASSERT_EQ(evt.messageContent, "AliceMax123");
         ASSERT_TRUE(evt.message != 0);
         XmppChatMessageHandle maxRecvdCm1 = evt.message;
         max.chat->notifyMessageDelivered(maxAliceChat, maxRecvdCm1, CPCAPI2::XmppChat::MessageDeliveryStatus_Delivered);
      }

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(max.events, "XmppChatHandler::onChatEnded", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedRemotely);
      }
   });

   auto timEvents = std::async(std::launch::async, [&] () {
      // ----------------------------- tim receiving messages from alice -----------------------------
      XmppChatHandle timAliceChat = 0;

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(tim.events, "XmppChatHandler::onNewChat", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_NE(h, 0);
         ASSERT_EQ(evt.chatType, ChatType_Incoming);
         timAliceChat = h;
      }

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::NewMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(tim.events, "XmppChatHandler::onNewMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.from, alice.config.bare());
         ASSERT_EQ(evt.messageContent, "AliceTim123");
         ASSERT_TRUE(evt.message != 0);
         XmppChatMessageHandle timRecvdCm1 = evt.message;
         tim.chat->notifyMessageDelivered(timAliceChat, timRecvdCm1, CPCAPI2::XmppChat::MessageDeliveryStatus_Delivered);
      }

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(tim.events, "XmppChatHandler::onChatEnded", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedRemotely);
      }
   });

   waitFor4(aliceEvents, bobEvents, maxEvents, timEvents);
}

TEST_F(XmppChatModuleTest, PeerDiscoveryTimeout)
{
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   CPCAPI2::Phone* phone = alice.phone;
   PhoneInterface* phoneIf = dynamic_cast<PhoneInterface*>(phone);

   XmppChatManager* chatManager = XmppChatManager::getInterface(phone);
   XmppChatManagerInterface* chatIf = dynamic_cast<XmppChatManagerInterface*>(chatManager);
   std::shared_ptr<XmppChatManagerImpl> chatImpl = chatIf->getImpl(alice.handle);

   auto aliceEvents = std::async(std::launch::async, [&] () {
      // ----------------------------- alice sending messages to bob -----------------------------
      XmppChatHandle aliceBobChat = alice.chat->createChat(alice.handle);
      alice.chat->addParticipant(aliceBobChat, bob.config.full());
      alice.chat->start(aliceBobChat);

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(aliceBobChat, h);
         ASSERT_EQ(evt.chatType, ChatType_Outgoing);
      }

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::ChatDiscoErrorEvent evt;

         XmppChatInfo* chatInfo = chatImpl->getChatInfo(aliceBobChat);
         DiscoMockEvent timeoutEvent{*chatImpl, chatInfo, bob.config.full().c_str()};

         phoneIf->getSdkModuleThread().post(resip::resip_bind(&DiscoMockEvent::timeout, &timeoutEvent));

         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onChatDiscoError", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(aliceBobChat, h);
         ASSERT_EQ(evt.remoteJID, bob.config.bare());
         ASSERT_EQ(evt.reason, CPCAPI2::XmppChat::PeerDiscoErrorReason::PeerDiscoErrorReason_Timeout);
      }

      XmppChatMessageHandle cm1 = alice.chat->sendMessage(aliceBobChat, "AliceBob123");
      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::SendMessageSuccessEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onSendMessageSuccess", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.message, cm1);
      }
      
      {
         // Wait for the message delivery notification (from Bob)
         XmppChatHandle h;
         MessageDeliveredEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onMessageDelivered", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.message, cm1);
      }

      alice.chat->end(aliceBobChat);
      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onChatEnded", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedLocally);
      }
      
      alice.chat->validateChatHandle(alice.handle, aliceBobChat);
      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::ValidateChatHandleEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onValidateChatHandleResult", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_FALSE(evt.chatHandleValid);
      }
   });

   auto bobEvents = std::async(std::launch::async, [&] () {
      // ----------------------------- bob receiving messages from alice -----------------------------
      XmppChatHandle bobAliceChat = 0;

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::NewChatEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewChat", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_NE(h, 0);
         ASSERT_EQ(evt.chatType, ChatType_Incoming);
         bobAliceChat = h;
      }

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::NewMessageEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onNewMessage", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.from, alice.config.bare());
         ASSERT_EQ(evt.messageContent, "AliceBob123");
         ASSERT_TRUE(evt.message != 0);
         XmppChatMessageHandle bobRecvdCm1 = evt.message;
         bob.chat->notifyMessageDelivered(bobAliceChat, bobRecvdCm1, CPCAPI2::XmppChat::MessageDeliveryStatus_Delivered);
      }

      {
         XmppChatHandle h;
         CPCAPI2::XmppChat::ChatEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppChatHandler::onChatEnded", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedRemotely);
      }
   });

   waitFor2(aliceEvents, bobEvents);
}

TEST_F(XmppChatModuleTest, PeerDiscoveryInvalidJid)
{
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   CPCAPI2::Phone* phone = alice.phone;
   PhoneInterface* phoneIf = dynamic_cast<PhoneInterface*>(phone);

   XmppChatManager* chatManager = XmppChatManager::getInterface(phone);
   XmppChatManagerInterface* chatIf = dynamic_cast<XmppChatManagerInterface*>(chatManager);
   std::shared_ptr<XmppChatManagerImpl> chatImpl = chatIf->getImpl(alice.handle);

   XmppChatHandle aliceBobChat = alice.chat->createChat(alice.handle);
   alice.chat->addParticipant(aliceBobChat, bob.config.bare());
   alice.chat->start(aliceBobChat);

   {
      XmppChatHandle h;
      CPCAPI2::XmppChat::NewChatEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onNewChat", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceBobChat, h);
      ASSERT_EQ(evt.chatType, ChatType_Outgoing);
   }

   {
      XmppChatHandle h;
      CPCAPI2::XmppChat::ChatDiscoErrorEvent evt;      

      XmppChatInfo* chatInfo = chatImpl->getChatInfo(aliceBobChat);
      chatImpl->peerDiscovery(chatInfo, gloox::JID(bob.config.bare().c_str()));

      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onChatDiscoError", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(aliceBobChat, h);
      ASSERT_EQ(evt.remoteJID, bob.config.bare());
      ASSERT_EQ(evt.reason, CPCAPI2::XmppChat::PeerDiscoErrorReason::PeerDiscoErrorReason_InvalidJid);
   }

   alice.chat->end(aliceBobChat);
   {
      XmppChatHandle h;
      CPCAPI2::XmppChat::ChatEndedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppChatHandler::onChatEnded", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(evt.endReason, ChatEndReason_UserTerminatedLocally);
   }
}

}  // namespace

#endif
