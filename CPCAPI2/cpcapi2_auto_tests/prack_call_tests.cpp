#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#include "cpcapi2_test_fixture.h"

using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::SipConversation;
using namespace CPCAPI2::SipAccount;

class PrackTests : public CpcapiAutoTest
{
public:
   PrackTests() {}
   virtual ~PrackTests() {}
};

// need a server that doesnt' support prack to test this with
TEST_F(PrackTests, DISABLED_PrackRequired) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   SipConversationSettings settings;
   settings.prackMode = SipConversation::PrackMode_Required;
   alice.conversation->setDefaultSettings(alice.handle, settings);
   bob.conversation->setDefaultSettings(bob.handle, settings);

   // make an outgoing (audio only) call from <PERSON> to <PERSON> using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());

   alice.conversation->start(aliceCall);

   // Overview of Alice's thread:
   //  - wait for onConversationEnded (triggered when the INVITE request was rejected since the server doesn't support PRACK)
   auto aliceEvents = std::async(std::launch::async, [&] () {
      {
      SipConversationHandle h;
      ConversationEndedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
         "SipConversationHandler::onConversationEnded",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(aliceCall, h);
      ASSERT_EQ(evt.endReason, ConversationEndReason_ServerRejected);
      }

      ASSERT_EQ(alice.conversation->end(aliceCall), kSuccess);
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(10000));
}

TEST_F(PrackTests, PrackDisabled) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   SipConversationSettings settings;
   settings.prackMode = SipConversation::PrackMode_Disabled;
   alice.conversation->setDefaultSettings(alice.handle, settings);
   bob.conversation->setDefaultSettings(bob.handle, settings);

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());

   alice.conversation->start(aliceCall);

   // Overview of Bob's thread:
   //  - wait for onNewConversation (triggered when Bob gets the incoming INVITE)
   //  - send 180 ringing
   //  - wait for onConversationStateChanged (LocalRinging)
   //  - answer the call (200 OK)
   //  - wait for onConversationMediaChanged (Audio -> SendReceive)
   //  - wait for onConversationStateChanged (Connected)
   //  - wait for onConversationMediaChangeRequest (triggered when Bob receives the re-INVITE w/sendonly from Alice)
   //  - accept the media change request
   //  - wait for onConversationMediaChanged (Audio -> ReceiveOnly)
   //  - wait for onConversationMediaChangeRequest (Bob receives the re-INVITE w/sendrecv from Alice)
   //  - accept the media change request
   //  - wait for onConversationMediaChanged (Audio -> SendReceive)
   //  - wait for onConversationEnded (triggered when Bob receives the BYE from Alice)
   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall = 0;
      {
      SipConversationHandle h;
      NewConversationEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
         "SipConversationHandler::onNewConversation",
         15000,
         AlwaysTruePred(),
         h, evt));
      bobCall = h;
      }

      // Bob sends 180 ringing -- this should trigger state transitions for both Bob and Alice
      ASSERT_EQ(bob.conversation->sendRingingResponse(bobCall), kSuccess);

      {
      SipConversationHandle h;
      ConversationStateChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
         "SipConversationHandler::onConversationStateChanged",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(bobCall, h);
      ASSERT_EQ(evt.conversationState, ConversationState_LocalRinging);
      }

      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      ASSERT_EQ(bob.conversation->accept(bobCall), kSuccess);
      {
      SipConversationHandle h;
      ConversationMediaChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
         "SipConversationHandler::onConversationMediaChanged",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(bobCall, h);
      ASSERT_EQ(evt.localMediaInfo.size(), 1);
      ASSERT_EQ(evt.localMediaInfo[0].mediaType, MediaType_Audio);
      ASSERT_EQ(evt.localMediaInfo[0].mediaDirection, MediaDirection_SendReceive);
      ASSERT_FALSE(evt.localHold);
      ASSERT_FALSE(evt.remoteHold);
      }

      {
      SipConversationHandle h;
      ConversationStateChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
         "SipConversationHandler::onConversationStateChanged",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(bobCall, h);
      ASSERT_EQ(evt.conversationState, ConversationState_Connected);
      }

      // alice holds the call
      {
      SipConversationHandle h;
      ConversationMediaChangeRequestEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
         "SipConversationHandler::onConversationMediaChangeRequest",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(bobCall, h);
      ASSERT_EQ(bob.conversation->accept(bobCall), kSuccess);
      }

      {
      SipConversationHandle h;
      ConversationMediaChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
         "SipConversationHandler::onConversationMediaChanged",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(bobCall, h);
      ASSERT_EQ(evt.localMediaInfo.size(), 1);
      ASSERT_EQ(evt.localMediaInfo[0].mediaType, MediaType_Audio);
      ASSERT_EQ(evt.localMediaInfo[0].mediaDirection, MediaDirection_ReceiveOnly);
      ASSERT_FALSE(evt.localHold);
      ASSERT_TRUE(evt.remoteHold);
      }

      // alice unholds the call
      {
      SipConversationHandle h;
      ConversationMediaChangeRequestEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
         "SipConversationHandler::onConversationMediaChangeRequest",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(bobCall, h);
      ASSERT_EQ(bob.conversation->accept(bobCall), kSuccess);
      }
      {
      SipConversationHandle h;
      ConversationMediaChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
         "SipConversationHandler::onConversationMediaChanged",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(bobCall, h);
      ASSERT_EQ(evt.localMediaInfo.size(), 1);
      ASSERT_EQ(evt.localMediaInfo[0].mediaType, MediaType_Audio);
      ASSERT_EQ(evt.localMediaInfo[0].mediaDirection, MediaDirection_SendReceive);
      ASSERT_FALSE(evt.localHold);
      ASSERT_FALSE(evt.remoteHold);
      }

      {
      SipConversationHandle h;
      ConversationEndedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
         "SipConversationHandler::onConversationEnded",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(bobCall, h);
      ASSERT_EQ(evt.endReason, ConversationEndReason_UserTerminatedRemotely);
      }
   });

   // Overview of Alice's thread:
   //  - wait for onNewConversation (triggered as soon as Alice sends the INVITE)
   //  - wait for onConversationStateChanged (RemoteRinging) (triggered when Alice receives the 180 from Bob)
   //  - wait for onConversationMediaChanged (Audio -> SendReceive) (triggered when Alice receives the 200 OK from Bob)
   //  - wait for onConversationStateChanged (Connected) (also triggered when Alice receives the 200 OK from Bob)
   //  - Alice puts the call on hold (re-INVITE)
   //  - wait for onConversationMediaChanged (Audio -> SendOnly)
   //  - Alice takes the call off hold (re-INVITE)
   //  - wait for onConversationMediaChanged (Audio -> SendReceive)
   //  - end the call (BYE)
   //  - wait for onConversationEnded
   auto aliceEvents = std::async(std::launch::async, [&] () {
      {
      SipConversationHandle h;
      NewConversationEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
         "SipConversationHandler::onNewConversation",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(aliceCall, h);
      }

      {
      SipConversationHandle h;
      ConversationStateChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
         "SipConversationHandler::onConversationStateChanged",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(aliceCall, h);
      ASSERT_EQ(evt.conversationState, ConversationState_RemoteRinging);
      }

      {
      SipConversationHandle h;
      ConversationMediaChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
         "SipConversationHandler::onConversationMediaChanged",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(aliceCall, h);
      ASSERT_EQ(evt.localMediaInfo.size(), 1);
      ASSERT_EQ(evt.localMediaInfo[0].mediaType, MediaType_Audio);
      ASSERT_EQ(evt.localMediaInfo[0].mediaDirection, MediaDirection_SendReceive);
      ASSERT_FALSE(evt.localHold);
      ASSERT_FALSE(evt.remoteHold);
      }

      {
      SipConversationHandle h;
      ConversationStateChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
         "SipConversationHandler::onConversationStateChanged",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(aliceCall, h);
      ASSERT_EQ(evt.conversationState, ConversationState_Connected);
      }

      // alice holds the call
      ASSERT_EQ(alice.conversation->hold(aliceCall), kSuccess);
      {
      SipConversationHandle h;
      ConversationMediaChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
         "SipConversationHandler::onConversationMediaChanged",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(aliceCall, h);
      ASSERT_EQ(evt.localMediaInfo.size(), 1);
      ASSERT_EQ(evt.localMediaInfo[0].mediaType, MediaType_Audio);
      ASSERT_EQ(evt.localMediaInfo[0].mediaDirection, MediaDirection_SendOnly);
      ASSERT_TRUE(evt.localHold);
      ASSERT_FALSE(evt.remoteHold);
      }

      // alice unholds the call
      ASSERT_EQ(alice.conversation->unhold(aliceCall), kSuccess);
      {
      SipConversationHandle h;
      ConversationMediaChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
         "SipConversationHandler::onConversationMediaChanged",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(aliceCall, h);
      ASSERT_EQ(evt.localMediaInfo.size(), 1);
      ASSERT_EQ(evt.localMediaInfo[0].mediaType, MediaType_Audio);
      ASSERT_EQ(evt.localMediaInfo[0].mediaDirection, MediaDirection_SendReceive);
      ASSERT_FALSE(evt.localHold);
      ASSERT_FALSE(evt.remoteHold);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(500));

      ASSERT_EQ(alice.conversation->end(aliceCall), kSuccess);
      {
      SipConversationHandle h;
      ConversationEndedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
         "SipConversationHandler::onConversationEnded",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(aliceCall, h);
      ASSERT_EQ(evt.endReason, ConversationEndReason_UserTerminatedLocally);
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(10000));
}

TEST_F(PrackTests, PrackSupportedAliceHolds) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   SipConversationSettings settings;
   settings.prackMode = SipConversation::PrackMode_Supported;
   alice.conversation->setDefaultSettings(alice.handle, settings);
   bob.conversation->setDefaultSettings(bob.handle, settings);

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());

   alice.conversation->start(aliceCall);

   // Overview of Bob's thread:
   //  - wait for onNewConversation (triggered when Bob gets the incoming INVITE)
   //  - send 180 ringing
   //  - wait for onConversationStateChanged (LocalRinging)
   //  - answer the call (200 OK)
   //  - wait for onConversationMediaChanged (Audio -> SendReceive)
   //  - wait for onConversationStateChanged (Connected)
   //  - wait for onConversationMediaChangeRequest (triggered when Bob receives the re-INVITE w/sendonly from Alice)
   //  - accept the media change request
   //  - wait for onConversationMediaChanged (Audio -> ReceiveOnly)
   //  - wait for onConversationMediaChangeRequest (Bob receives the re-INVITE w/sendrecv from Alice)
   //  - accept the media change request
   //  - wait for onConversationMediaChanged (Audio -> SendReceive)
   //  - wait for onConversationEnded (triggered when Bob receives the BYE from Alice)
   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall = 0;
      {
      SipConversationHandle h;
      NewConversationEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
         "SipConversationHandler::onNewConversation",
         15000,
         AlwaysTruePred(),
         h, evt));
      bobCall = h;
      }

      // Bob sends 180 ringing -- this should trigger state transitions for both Bob and Alice
      ASSERT_EQ(bob.conversation->sendRingingResponse(bobCall), kSuccess);
      {
      SipConversationHandle h;
      ConversationStateChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
         "SipConversationHandler::onConversationStateChanged",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(bobCall, h);
      ASSERT_EQ(evt.conversationState, ConversationState_LocalRinging);
      }

      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      ASSERT_EQ(bob.conversation->accept(bobCall), kSuccess);
      {
      SipConversationHandle h;
      ConversationMediaChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
         "SipConversationHandler::onConversationMediaChanged",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(bobCall, h);
      ASSERT_EQ(evt.localMediaInfo.size(), 1);
      ASSERT_EQ(evt.localMediaInfo[0].mediaType, MediaType_Audio);
      ASSERT_EQ(evt.localMediaInfo[0].mediaDirection, MediaDirection_SendReceive);
      ASSERT_FALSE(evt.localHold);
      ASSERT_FALSE(evt.remoteHold);
      }

      {
      SipConversationHandle h;
      ConversationStateChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
         "SipConversationHandler::onConversationStateChanged",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(bobCall, h);
      ASSERT_EQ(evt.conversationState, ConversationState_Connected);
      }

      // alice holds the call
      {
      SipConversationHandle h;
      ConversationMediaChangeRequestEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
         "SipConversationHandler::onConversationMediaChangeRequest",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(bobCall, h);
      ASSERT_EQ(bob.conversation->accept(bobCall), kSuccess);
      }

      {
      SipConversationHandle h;
      ConversationMediaChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
         "SipConversationHandler::onConversationMediaChanged",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(bobCall, h);
      ASSERT_EQ(evt.localMediaInfo.size(), 1);
      ASSERT_EQ(evt.localMediaInfo[0].mediaType, MediaType_Audio);
      ASSERT_EQ(evt.localMediaInfo[0].mediaDirection, MediaDirection_ReceiveOnly);
      ASSERT_FALSE(evt.localHold);
      ASSERT_TRUE(evt.remoteHold);
      }

      // alice unholds the call
      {
      SipConversationHandle h;
      ConversationMediaChangeRequestEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
         "SipConversationHandler::onConversationMediaChangeRequest",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(bobCall, h);
      ASSERT_EQ(bob.conversation->accept(bobCall), kSuccess);
      }

      {
      SipConversationHandle h;
      ConversationMediaChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
         "SipConversationHandler::onConversationMediaChanged",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(bobCall, h);
      ASSERT_EQ(evt.localMediaInfo.size(), 1);
      ASSERT_EQ(evt.localMediaInfo[0].mediaType, MediaType_Audio);
      ASSERT_EQ(evt.localMediaInfo[0].mediaDirection, MediaDirection_SendReceive);
      ASSERT_FALSE(evt.localHold);
      ASSERT_FALSE(evt.remoteHold);
      }

      {
      SipConversationHandle h;
      ConversationEndedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
         "SipConversationHandler::onConversationEnded",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(bobCall, h);
      ASSERT_EQ(evt.endReason, ConversationEndReason_UserTerminatedRemotely);
      }
   });

   // Overview of Alice's thread:
   //  - wait for onNewConversation (triggered as soon as Alice sends the INVITE)
   //  - wait for onConversationStateChanged (RemoteRinging) (triggered when Alice receives the 180 from Bob)
   //  - wait for onConversationMediaChanged (Audio -> SendReceive) (triggered when Alice receives the 200 OK from Bob)
   //  - wait for onConversationStateChanged (Connected) (also triggered when Alice receives the 200 OK from Bob)
   //  - Alice puts the call on hold (re-INVITE)
   //  - wait for onConversationMediaChanged (Audio -> SendOnly)
   //  - Alice takes the call off hold (re-INVITE)
   //  - wait for onConversationMediaChanged (Audio -> SendReceive)
   //  - end the call (BYE)
   //  - wait for onConversationEnded
   auto aliceEvents = std::async(std::launch::async, [&] () {
      {
      SipConversationHandle h;
      NewConversationEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
         "SipConversationHandler::onNewConversation",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(aliceCall, h);
      }

      {
      SipConversationHandle h;
      ConversationStateChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
         "SipConversationHandler::onConversationStateChanged",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(aliceCall, h);
      ASSERT_EQ(evt.conversationState, ConversationState_RemoteRinging);
      }

      {
      SipConversationHandle h;
      ConversationMediaChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
         "SipConversationHandler::onConversationMediaChanged",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(aliceCall, h);
      ASSERT_EQ(evt.localMediaInfo.size(), 1);
      ASSERT_EQ(evt.localMediaInfo[0].mediaType, MediaType_Audio);
      ASSERT_EQ(evt.localMediaInfo[0].mediaDirection, MediaDirection_SendReceive);
      ASSERT_FALSE(evt.localHold);
      ASSERT_FALSE(evt.remoteHold);
      }

      {
      SipConversationHandle h;
      ConversationStateChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
         "SipConversationHandler::onConversationStateChanged",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(aliceCall, h);
      ASSERT_EQ(evt.conversationState, ConversationState_Connected);
      }

      // alice holds the call
      ASSERT_EQ(alice.conversation->hold(aliceCall), kSuccess);
      {
      SipConversationHandle h;
      ConversationMediaChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
         "SipConversationHandler::onConversationMediaChanged",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(aliceCall, h);
      ASSERT_EQ(evt.localMediaInfo.size(), 1);
      ASSERT_EQ(evt.localMediaInfo[0].mediaType, MediaType_Audio);
      ASSERT_EQ(evt.localMediaInfo[0].mediaDirection, MediaDirection_SendOnly);
      ASSERT_TRUE(evt.localHold);
      ASSERT_FALSE(evt.remoteHold);
      }

      // alice unholds the call
      ASSERT_EQ(alice.conversation->unhold(aliceCall), kSuccess);
      {
      SipConversationHandle h;
      ConversationMediaChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
         "SipConversationHandler::onConversationMediaChanged",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(aliceCall, h);
      ASSERT_EQ(evt.localMediaInfo.size(), 1);
      ASSERT_EQ(evt.localMediaInfo[0].mediaType, MediaType_Audio);
      ASSERT_EQ(evt.localMediaInfo[0].mediaDirection, MediaDirection_SendReceive);
      ASSERT_FALSE(evt.localHold);
      ASSERT_FALSE(evt.remoteHold);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(500));

      ASSERT_EQ(alice.conversation->end(aliceCall), kSuccess);
      {
      SipConversationHandle h;
      ConversationEndedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
         "SipConversationHandler::onConversationEnded",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(aliceCall, h);
      ASSERT_EQ(evt.endReason, ConversationEndReason_UserTerminatedLocally);
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(10000));
}

TEST_F(PrackTests, PrackSupportedBobHolds) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   SipConversationSettings settings;
   settings.prackMode = SipConversation::PrackMode_Supported;
   alice.conversation->setDefaultSettings(alice.handle, settings);
   bob.conversation->setDefaultSettings(bob.handle, settings);

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());

   alice.conversation->start(aliceCall);

   // Overview of Bob's thread:
   //  - wait for onNewConversation (triggered when Bob gets the incoming INVITE)
   //  - send 180 ringing
   //  - wait for onConversationStateChanged (LocalRinging)
   //  - answer the call (200 OK)
   //  - wait for onConversationMediaChanged (Audio -> SendReceive)
   //  - wait for onConversationStateChanged (Connected)
   //  - Bob puts the call on hold (re-INVITE)
   //  - wait for onConversationMediaChanged (Audio -> SendOnly)
   //  - Bob takes the call off hold (re-INVITE)
   //  - wait for onConversationMediaChanged (Audio -> SendReceive)
   //  - end the call (BYE)
   //  - wait for onConversationEnded
   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall = 0;
      {
      SipConversationHandle h;
      NewConversationEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
         "SipConversationHandler::onNewConversation",
         15000,
         AlwaysTruePred(),
         h, evt));
      bobCall = h;
      }

      // Bob sends 180 ringing -- this should trigger state transitions for both Bob and Alice
      ASSERT_EQ(bob.conversation->sendRingingResponse(bobCall), kSuccess);
      {
      SipConversationHandle h;
      ConversationStateChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
         "SipConversationHandler::onConversationStateChanged",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(bobCall, h);
      ASSERT_EQ(evt.conversationState, ConversationState_LocalRinging);
      }

      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      ASSERT_EQ(bob.conversation->accept(bobCall), kSuccess);
      {
      SipConversationHandle h;
      ConversationMediaChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
         "SipConversationHandler::onConversationMediaChanged",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(bobCall, h);
      ASSERT_EQ(evt.localMediaInfo.size(), 1);
      ASSERT_EQ(evt.localMediaInfo[0].mediaType, MediaType_Audio);
      ASSERT_EQ(evt.localMediaInfo[0].mediaDirection, MediaDirection_SendReceive);
      ASSERT_FALSE(evt.localHold);
      ASSERT_FALSE(evt.remoteHold);
      }

      {
      SipConversationHandle h;
      ConversationStateChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
         "SipConversationHandler::onConversationStateChanged",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(bobCall, h);
      ASSERT_EQ(evt.conversationState, ConversationState_Connected);
      }

      bob.conversation->setDefaultSettings(bob.handle, settings);
      ASSERT_EQ(bob.conversation->hold(bobCall), kSuccess);
      {
      SipConversationHandle h;
      ConversationMediaChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
         "SipConversationHandler::onConversationMediaChanged",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(bobCall, h);
      ASSERT_EQ(evt.localMediaInfo.size(), 1);
      ASSERT_EQ(evt.localMediaInfo[0].mediaType, MediaType_Audio);
      ASSERT_EQ(evt.localMediaInfo[0].mediaDirection, MediaDirection_SendOnly);
      ASSERT_TRUE(evt.localHold);
      ASSERT_FALSE(evt.remoteHold);
      }

      ASSERT_EQ(bob.conversation->unhold(bobCall), kSuccess);
      {
      SipConversationHandle h;
      ConversationMediaChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
         "SipConversationHandler::onConversationMediaChanged",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(bobCall, h);
      ASSERT_EQ(evt.localMediaInfo.size(), 1);
      ASSERT_EQ(evt.localMediaInfo[0].mediaType, MediaType_Audio);
      ASSERT_EQ(evt.localMediaInfo[0].mediaDirection, MediaDirection_SendReceive);
      ASSERT_FALSE(evt.localHold);
      ASSERT_FALSE(evt.remoteHold);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(500));

      ASSERT_EQ(bob.conversation->end(bobCall), kSuccess);
      {
      SipConversationHandle h;
      ConversationEndedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
         "SipConversationHandler::onConversationEnded",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(bobCall, h);
      ASSERT_EQ(evt.endReason, ConversationEndReason_UserTerminatedLocally);
      }
   });

   // Overview of Alice's thread:
   //  - wait for onNewConversation (triggered as soon as Alice sends the INVITE)
   //  - wait for onConversationStateChanged (RemoteRinging) (triggered when Alice receives the 180 from Bob)
   //  - wait for onConversationMediaChanged (Audio -> SendReceive) (triggered when Alice receives the 200 OK from Bob)
   //  - wait for onConversationStateChanged (Connected) (also triggered when Alice receives the 200 OK from Bob)
   //  - wait for onConversationMediaChangeRequest (triggered when Alice receives the re-INVITE w/sendonly from Bob)
   //  - accept the media change request
   //  - wait for onConversationMediaChanged (Audio -> ReceiveOnly)
   //  - wait for onConversationMediaChangeRequest (Alice receives the re-INVITE w/sendrecv from Bob)
   //  - accept the media change request
   //  - wait for onConversationMediaChanged (Audio -> SendReceive)
   //  - wait for onConversationEnded (triggered when Alice receives the BYE from Bob)
   auto aliceEvents = std::async(std::launch::async, [&] () {
      {
      SipConversationHandle h;
      NewConversationEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
         "SipConversationHandler::onNewConversation",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(aliceCall, h);
      }

      {
      SipConversationHandle h;
      ConversationStateChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
         "SipConversationHandler::onConversationStateChanged",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(aliceCall, h);
      ASSERT_EQ(evt.conversationState, ConversationState_RemoteRinging);
      }

      {
      SipConversationHandle h;
      ConversationMediaChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
         "SipConversationHandler::onConversationMediaChanged",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(aliceCall, h);
      ASSERT_EQ(evt.localMediaInfo.size(), 1);
      ASSERT_EQ(evt.localMediaInfo[0].mediaType, MediaType_Audio);
      ASSERT_EQ(evt.localMediaInfo[0].mediaDirection, MediaDirection_SendReceive);
      ASSERT_FALSE(evt.localHold);
      ASSERT_FALSE(evt.remoteHold);
      }

      {
      SipConversationHandle h;
      ConversationStateChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
         "SipConversationHandler::onConversationStateChanged",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(aliceCall, h);
      ASSERT_EQ(evt.conversationState, ConversationState_Connected);
      }

      {
      SipConversationHandle h;
      ConversationMediaChangeRequestEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
         "SipConversationHandler::onConversationMediaChangeRequest",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(aliceCall, h);
      ASSERT_EQ(alice.conversation->accept(aliceCall), kSuccess);
      }

      {
      SipConversationHandle h;
      ConversationMediaChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
         "SipConversationHandler::onConversationMediaChanged",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(aliceCall, h);
      ASSERT_EQ(evt.localMediaInfo.size(), 1);
      ASSERT_EQ(evt.localMediaInfo[0].mediaType, MediaType_Audio);
      ASSERT_EQ(evt.localMediaInfo[0].mediaDirection, MediaDirection_ReceiveOnly);
      ASSERT_FALSE(evt.localHold);
      ASSERT_TRUE(evt.remoteHold);
      }

      {
      SipConversationHandle h;
      ConversationMediaChangeRequestEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
         "SipConversationHandler::onConversationMediaChangeRequest",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(aliceCall, h);
      ASSERT_EQ(alice.conversation->accept(aliceCall), kSuccess);
      }

      {
      SipConversationHandle h;
      ConversationMediaChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
         "SipConversationHandler::onConversationMediaChanged",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(aliceCall, h);
      ASSERT_EQ(evt.localMediaInfo.size(), 1);
      ASSERT_EQ(evt.localMediaInfo[0].mediaType, MediaType_Audio);
      ASSERT_EQ(evt.localMediaInfo[0].mediaDirection, MediaDirection_SendReceive);
      ASSERT_FALSE(evt.localHold);
      ASSERT_FALSE(evt.remoteHold);
      }

      {
      SipConversationHandle h;
      ConversationEndedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
         "SipConversationHandler::onConversationEnded",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(aliceCall, h);
      ASSERT_EQ(evt.endReason, ConversationEndReason_UserTerminatedRemotely);
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(10000));
}

TEST_F(PrackTests, PrackUasSupportedAliceHolds) {
   TestAccount alice("alice", Account_Init);
   TestAccount bob("bob", Account_Init);

   // we have to set these BEFORE we enable the account
   SipConversationSettings settings;
   settings.prackMode = SipConversation::PrackMode_SupportUasAndUac;
   alice.conversation->setDefaultSettings(alice.handle, settings);
   bob.conversation->setDefaultSettings(bob.handle, settings);

   alice.enable();
   bob.enable();

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());

   alice.conversation->start(aliceCall);

   // Overview of Bob's thread:
   //  - wait for onNewConversation (triggered when Bob gets the incoming INVITE)
   //  - send 180 ringing
   //  - wait for onConversationStateChanged (LocalRinging)
   //  - answer the call (200 OK)
   //  - wait for onConversationMediaChanged (Audio -> SendReceive)
   //  - wait for onConversationStateChanged (Connected)
   //  - wait for onConversationMediaChangeRequest (triggered when Bob receives the re-INVITE w/sendonly from Alice)
   //  - accept the media change request
   //  - wait for onConversationMediaChanged (Audio -> ReceiveOnly)
   //  - wait for onConversationMediaChangeRequest (Bob receives the re-INVITE w/sendrecv from Alice)
   //  - accept the media change request
   //  - wait for onConversationMediaChanged (Audio -> SendReceive)
   //  - wait for onConversationEnded (triggered when Bob receives the BYE from Alice)
   auto bobEvents = std::async(std::launch::async, [&] () {
      SipConversationHandle bobCall = 0;
      {
      SipConversationHandle h;
      NewConversationEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
         "SipConversationHandler::onNewConversation",
         15000,
         AlwaysTruePred(),
         h, evt));
      bobCall = h;
      }

      // Bob sends 180 ringing -- this should trigger state transitions for both Bob and Alice
      ASSERT_EQ(bob.conversation->sendRingingResponse(bobCall), kSuccess);
      {
      SipConversationHandle h;
      ConversationStateChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
         "SipConversationHandler::onConversationStateChanged",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(bobCall, h);
      ASSERT_EQ(evt.conversationState, ConversationState_LocalRinging);
      }

      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      ASSERT_EQ(bob.conversation->accept(bobCall), kSuccess);
      {
      SipConversationHandle h;
      ConversationMediaChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
         "SipConversationHandler::onConversationMediaChanged",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(bobCall, h);
      ASSERT_EQ(evt.localMediaInfo.size(), 1);
      ASSERT_EQ(evt.localMediaInfo[0].mediaType, MediaType_Audio);
      ASSERT_EQ(evt.localMediaInfo[0].mediaDirection, MediaDirection_SendReceive);
      ASSERT_FALSE(evt.localHold);
      ASSERT_FALSE(evt.remoteHold);
      }

      {
      SipConversationHandle h;
      ConversationStateChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
         "SipConversationHandler::onConversationStateChanged",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(bobCall, h);
      ASSERT_EQ(evt.conversationState, ConversationState_Connected);
      }

      // alice holds the call
      {
      SipConversationHandle h;
      ConversationMediaChangeRequestEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
         "SipConversationHandler::onConversationMediaChangeRequest",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(bobCall, h);
      ASSERT_EQ(bob.conversation->accept(bobCall), kSuccess);
      }

      {
      SipConversationHandle h;
      ConversationMediaChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
         "SipConversationHandler::onConversationMediaChanged",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(bobCall, h);
      ASSERT_EQ(evt.localMediaInfo.size(), 1);
      ASSERT_EQ(evt.localMediaInfo[0].mediaType, MediaType_Audio);
      ASSERT_EQ(evt.localMediaInfo[0].mediaDirection, MediaDirection_ReceiveOnly);
      ASSERT_FALSE(evt.localHold);
      ASSERT_TRUE(evt.remoteHold);
      }

      // alice unholds the call
      {
      SipConversationHandle h;
      ConversationMediaChangeRequestEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
         "SipConversationHandler::onConversationMediaChangeRequest",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(bobCall, h);
      ASSERT_EQ(bob.conversation->accept(bobCall), kSuccess);
      }

      {
      SipConversationHandle h;
      ConversationMediaChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
         "SipConversationHandler::onConversationMediaChanged",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(bobCall, h);
      ASSERT_EQ(evt.localMediaInfo.size(), 1);
      ASSERT_EQ(evt.localMediaInfo[0].mediaType, MediaType_Audio);
      ASSERT_EQ(evt.localMediaInfo[0].mediaDirection, MediaDirection_SendReceive);
      ASSERT_FALSE(evt.localHold);
      ASSERT_FALSE(evt.remoteHold);
      }

      {
      SipConversationHandle h;
      ConversationEndedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents,
         "SipConversationHandler::onConversationEnded",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(bobCall, h);
      ASSERT_EQ(evt.endReason, ConversationEndReason_UserTerminatedRemotely);
      }
   });

   // Overview of Alice's thread:
   //  - wait for onNewConversation (triggered as soon as Alice sends the INVITE)
   //  - wait for onConversationStateChanged (RemoteRinging) (triggered when Alice receives the 180 from Bob)
   //  - wait for onConversationMediaChanged (Audio -> SendReceive) (triggered when Alice receives the 200 OK from Bob)
   //  - wait for onConversationStateChanged (Connected) (also triggered when Alice receives the 200 OK from Bob)
   //  - Alice puts the call on hold (re-INVITE)
   //  - wait for onConversationMediaChanged (Audio -> SendOnly)
   //  - Alice takes the call off hold (re-INVITE)
   //  - wait for onConversationMediaChanged (Audio -> SendReceive)
   //  - end the call (BYE)
   //  - wait for onConversationEnded
   auto aliceEvents = std::async(std::launch::async, [&] () {
      {
      SipConversationHandle h;
      NewConversationEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
         "SipConversationHandler::onNewConversation",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(aliceCall, h);
      }

      {
      SipConversationHandle h;
      ConversationStateChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
         "SipConversationHandler::onConversationStateChanged",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(aliceCall, h);
      ASSERT_EQ(evt.conversationState, ConversationState_RemoteRinging);
      }

      {
      SipConversationHandle h;
      ConversationMediaChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
         "SipConversationHandler::onConversationMediaChanged",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(aliceCall, h);
      ASSERT_EQ(evt.localMediaInfo.size(), 1);
      ASSERT_EQ(evt.localMediaInfo[0].mediaType, MediaType_Audio);
      ASSERT_EQ(evt.localMediaInfo[0].mediaDirection, MediaDirection_SendReceive);
      ASSERT_FALSE(evt.localHold);
      ASSERT_FALSE(evt.remoteHold);
      }

      {
      SipConversationHandle h;
      ConversationStateChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
         "SipConversationHandler::onConversationStateChanged",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(aliceCall, h);
      ASSERT_EQ(evt.conversationState, ConversationState_Connected);
      }

      // alice holds the call
      ASSERT_EQ(alice.conversation->hold(aliceCall), kSuccess);
      {
      SipConversationHandle h;
      ConversationMediaChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
         "SipConversationHandler::onConversationMediaChanged",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(aliceCall, h);
      ASSERT_EQ(evt.localMediaInfo.size(), 1);
      ASSERT_EQ(evt.localMediaInfo[0].mediaType, MediaType_Audio);
      ASSERT_EQ(evt.localMediaInfo[0].mediaDirection, MediaDirection_SendOnly);
      ASSERT_TRUE(evt.localHold);
      ASSERT_FALSE(evt.remoteHold);
      }

      // alice unholds the call
      ASSERT_EQ(alice.conversation->unhold(aliceCall), kSuccess);
      {
      SipConversationHandle h;
      ConversationMediaChangedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
         "SipConversationHandler::onConversationMediaChanged",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(aliceCall, h);
      ASSERT_EQ(evt.localMediaInfo.size(), 1);
      ASSERT_EQ(evt.localMediaInfo[0].mediaType, MediaType_Audio);
      ASSERT_EQ(evt.localMediaInfo[0].mediaDirection, MediaDirection_SendReceive);
      ASSERT_FALSE(evt.localHold);
      ASSERT_FALSE(evt.remoteHold);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(500));

      ASSERT_EQ(alice.conversation->end(aliceCall), kSuccess);
      {
      SipConversationHandle h;
      ConversationEndedEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents,
         "SipConversationHandler::onConversationEnded",
         15000,
         AlwaysTruePred(),
         h, evt));
      ASSERT_EQ(aliceCall, h);
      ASSERT_EQ(evt.endReason, ConversationEndReason_UserTerminatedLocally);
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(10000));
}

TEST_F(PrackTests, MultiplePracks)
{
   TestAccount alice("alice", Account_Init);
   TestAccount bob("bob", Account_Init);

   // we have to set these BEFORE we enable the account
   SipConversationSettings settings;
   settings.prackMode = SipConversation::PrackMode_SupportUasAndUac;
   alice.conversation->setDefaultSettings(alice.handle, settings);
   bob.conversation->setDefaultSettings(bob.handle, settings);

   alice.enable();
   bob.enable();

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());

   alice.conversation->start(aliceCall);

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall = 0;
      {
         SipConversationHandle h;
         NewConversationEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents, "SipConversationHandler::onNewConversation", 15000, AlwaysTruePred(), h, evt));
         bobCall = h;
      }

      // Bob sends 180 ringing -- this should trigger state transitions for both Bob and Alice
      ASSERT_EQ(bob.conversation->sendRingingResponse(bobCall), kSuccess);
      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents, "SipConversationHandler::onConversationStateChanged", 15000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobCall, h);
         ASSERT_EQ(evt.conversationState, ConversationState_LocalRinging);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      ASSERT_EQ(bob.conversation->sendRingingResponse(bobCall), kSuccess);
      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents, "SipConversationHandler::onConversationStateChanged", 15000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobCall, h);
         ASSERT_EQ(evt.conversationState, ConversationState_LocalRinging);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      ASSERT_EQ(bob.conversation->sendRingingResponse(bobCall), kSuccess);
      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents, "SipConversationHandler::onConversationStateChanged", 15000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobCall, h);
         ASSERT_EQ(evt.conversationState, ConversationState_LocalRinging);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      ASSERT_EQ(bob.conversation->sendRingingResponse(bobCall), kSuccess);
      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents, "SipConversationHandler::onConversationStateChanged", 15000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobCall, h);
         ASSERT_EQ(evt.conversationState, ConversationState_LocalRinging);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      ASSERT_EQ(bob.conversation->accept(bobCall), kSuccess);
      {
         SipConversationHandle h;
         ConversationMediaChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents, "SipConversationHandler::onConversationMediaChanged", 15000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobCall, h);
         ASSERT_EQ(evt.localMediaInfo.size(), 1);
         ASSERT_EQ(evt.localMediaInfo[0].mediaType, MediaType_Audio);
         ASSERT_EQ(evt.localMediaInfo[0].mediaDirection, MediaDirection_SendReceive);
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
      }

      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents, "SipConversationHandler::onConversationStateChanged", 15000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobCall, h);
         ASSERT_EQ(evt.conversationState, ConversationState_Connected);
      }

      {
         SipConversationHandle h;
         ConversationEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents, "SipConversationHandler::onConversationEnded", 15000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobCall, h);
         ASSERT_EQ(evt.endReason, ConversationEndReason_UserTerminatedRemotely);
      }
   });

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      {
         SipConversationHandle h;
         NewConversationEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents, "SipConversationHandler::onNewConversation", 15000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(aliceCall, h);
      }

      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents, "SipConversationHandler::onConversationStateChanged", 15000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(aliceCall, h);
         ASSERT_EQ(evt.conversationState, ConversationState_RemoteRinging);
      }

      {
         SipConversationHandle h;
         ConversationMediaChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents, "SipConversationHandler::onConversationMediaChanged", 15000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(aliceCall, h);
         ASSERT_EQ(evt.localMediaInfo.size(), 1);
         ASSERT_EQ(evt.localMediaInfo[0].mediaType, MediaType_Audio);
         ASSERT_EQ(evt.localMediaInfo[0].mediaDirection, MediaDirection_SendReceive);
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
      }

      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents, "SipConversationHandler::onConversationStateChanged", 15000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(aliceCall, h);
         ASSERT_EQ(evt.conversationState, ConversationState_Connected);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(500));

      ASSERT_EQ(alice.conversation->end(aliceCall), kSuccess);
      {
         SipConversationHandle h;
         ConversationEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents, "SipConversationHandler::onConversationEnded", 15000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(aliceCall, h);
         ASSERT_EQ(evt.endReason, ConversationEndReason_UserTerminatedLocally);
      }
   });

   waitFor2(aliceEvents, bobEvents);
}

TEST_F(PrackTests, MultiplePracksQueuedProvisionalResponses)
{
   // Same as MultiplePrack test-case except that as there is no delay between the provisional responses, it ensures that the
   // provisional responses are queued as the PRACK transactions have not completed.

   TestAccount alice("alice", Account_Init);
   TestAccount bob("bob", Account_Init);

   // we have to set these BEFORE we enable the account
   SipConversationSettings settings;
   settings.prackMode = SipConversation::PrackMode_SupportUasAndUac;
   alice.conversation->setDefaultSettings(alice.handle, settings);
   bob.conversation->setDefaultSettings(bob.handle, settings);

   alice.enable();
   bob.enable();

   // make an outgoing (audio only) call from Alice to Bob using the demo.xten.com server
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, bob.config.uri());

   alice.conversation->start(aliceCall);

   auto bobEvents = std::async(std::launch::async, [&] ()
   {
      SipConversationHandle bobCall = 0;
      {
         SipConversationHandle h;
         NewConversationEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents, "SipConversationHandler::onNewConversation", 15000, AlwaysTruePred(), h, evt));
         bobCall = h;
      }

      // Bob sends 180 ringing -- this should trigger state transitions for both Bob and Alice
      ASSERT_EQ(bob.conversation->sendRingingResponse(bobCall), kSuccess);
      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents, "SipConversationHandler::onConversationStateChanged", 15000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobCall, h);
         ASSERT_EQ(evt.conversationState, ConversationState_LocalRinging);
      }

      ASSERT_EQ(bob.conversation->sendRingingResponse(bobCall), kSuccess);
      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents, "SipConversationHandler::onConversationStateChanged", 15000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobCall, h);
         ASSERT_EQ(evt.conversationState, ConversationState_LocalRinging);
      }

      ASSERT_EQ(bob.conversation->sendRingingResponse(bobCall), kSuccess);
      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents, "SipConversationHandler::onConversationStateChanged", 15000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobCall, h);
         ASSERT_EQ(evt.conversationState, ConversationState_LocalRinging);
      }

      ASSERT_EQ(bob.conversation->sendRingingResponse(bobCall), kSuccess);
      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents, "SipConversationHandler::onConversationStateChanged", 15000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobCall, h);
         ASSERT_EQ(evt.conversationState, ConversationState_LocalRinging);
      }

      // Bob answers the call (200 OK) -- this should trigger state transitions for both Bob and Alice
      ASSERT_EQ(bob.conversation->accept(bobCall), kSuccess);
      {
         SipConversationHandle h;
         ConversationMediaChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents, "SipConversationHandler::onConversationMediaChanged", 15000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobCall, h);
         ASSERT_EQ(evt.localMediaInfo.size(), 1);
         ASSERT_EQ(evt.localMediaInfo[0].mediaType, MediaType_Audio);
         ASSERT_EQ(evt.localMediaInfo[0].mediaDirection, MediaDirection_SendReceive);
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
      }

      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents, "SipConversationHandler::onConversationStateChanged", 15000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobCall, h);
         ASSERT_EQ(evt.conversationState, ConversationState_Connected);
      }

      {
         SipConversationHandle h;
         ConversationEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.conversationEvents, "SipConversationHandler::onConversationEnded", 15000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(bobCall, h);
         ASSERT_EQ(evt.endReason, ConversationEndReason_UserTerminatedRemotely);
      }
   });

   auto aliceEvents = std::async(std::launch::async, [&] ()
   {
      {
         SipConversationHandle h;
         NewConversationEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents, "SipConversationHandler::onNewConversation", 15000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(aliceCall, h);
      }

      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents, "SipConversationHandler::onConversationStateChanged", 15000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(aliceCall, h);
         ASSERT_EQ(evt.conversationState, ConversationState_RemoteRinging);
      }

      {
         SipConversationHandle h;
         ConversationMediaChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents, "SipConversationHandler::onConversationMediaChanged", 15000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(aliceCall, h);
         ASSERT_EQ(evt.localMediaInfo.size(), 1);
         ASSERT_EQ(evt.localMediaInfo[0].mediaType, MediaType_Audio);
         ASSERT_EQ(evt.localMediaInfo[0].mediaDirection, MediaDirection_SendReceive);
         ASSERT_FALSE(evt.localHold);
         ASSERT_FALSE(evt.remoteHold);
      }

      {
         SipConversationHandle h;
         ConversationStateChangedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents, "SipConversationHandler::onConversationStateChanged", 15000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(aliceCall, h);
         ASSERT_EQ(evt.conversationState, ConversationState_Connected);
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(500));

      ASSERT_EQ(alice.conversation->end(aliceCall), kSuccess);
      {
         SipConversationHandle h;
         ConversationEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.conversationEvents, "SipConversationHandler::onConversationEnded", 15000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(aliceCall, h);
         ASSERT_EQ(evt.endReason, ConversationEndReason_UserTerminatedLocally);
      }
   });

   waitFor2(aliceEvents, bobEvents);
}
