#ifndef CPCSFU_Logger_hxx
#define CPCSFU_Logger_hxx

//#include "rutil/Log.hxx"
//#include "rutil/Lock.hxx"
//#include "rutil/DataStream.hxx"
//#include "rutil/Data.hxx"
//#include "rutil/Subsystem.hxx"

#ifdef WIN32
#include <windows.h>
#endif 

#include <cpcapi2.h>
#include <string.h>



#define DELIM 

#define CPCSFU_SUBSYSTEM ""


#define StackLog(args_)                                                         \
GenericLog(CPCSFU_SUBSYSTEM, CPCAPI2::LogLevel_Max, getPhoneInternalForLogger(), args_)

#define DebugLog(args_) \
GenericLog(CPCSFU_SUBSYSTEM, CPCAPI2::LogLevel_Debug, getPhoneInternalForLogger(), args_)

#define InfoLog(args_) \
GenericLog(CPCSFU_SUBSYSTEM, CPCAPI2::LogLevel_Info, getPhoneInternalForLogger(), args_)

#define WarningLog(args_) \
GenericLog(CPCSFU_SUBSYSTEM, CPCAPI2::LogLevel_Warning, getPhoneInternalForLogger(), args_)

#define ErrLog(args_) \
GenericLog(CPCSFU_SUBSYSTEM, CPCAPI2::LogLevel_Error, getPhoneInternalForLogger(), args_)

#define CritLog(args_) \
GenericLog(CPCSFU_SUBSYSTEM, CPCAPI2::LogLevel_Error, getPhoneInternalForLogger(), args_)

#define SpecialLog(args_) \
GenericLog(CPCSFU_SUBSYSTEM, CPCAPI2::LogLevel_Error, getPhoneInternalForLogger(), args_)


static const char* stripPath(const char* fileWithPath)
{
   // Fix the file so it doesn't contain any paths
   const char *pos = fileWithPath + strlen(fileWithPath);

#if defined( WIN32 )
   while (pos != fileWithPath && *pos != '\\')
      --pos;
#else // #if defined( WIN32 ) || defined( __APPLE__ )
   while (pos != fileWithPath && *pos != '/')
      --pos;
#endif

   if (pos != fileWithPath)
      ++pos;

   return pos;
}


// do/while allows a {} block in an expression
#define GenericLog(system_, level_, phoneInternal, args_)                           \
   do                                                                               \
   {                                                                                \
      std::ostringstream ss;                                                        \
      /* externalLog doesn't currently support passing over file name/line, so hard \
         code it into the log message for now */                                    \
      ss << stripPath(__FILE__) << ":" << __LINE__ << "   ";                        \
      ss args_;                                                                     \
      phoneInternal->externalLog(level_, ss.str().c_str());                         \
   }                                                                                \
   while (false)


// Suppress debug logging at compile time
#ifdef NO_DEBUG
#undef DebugLog
#define DebugLog(args_)
#endif
#ifdef NDEBUG
#undef StackLog 
#define StackLog(args_)
#endif


#endif

/* ====================================================================
* The Vovida Software License, Version 1.0
*
* Copyright (c) 2000 Vovida Networks, Inc.  All rights reserved.
*
* Redistribution and use in source and binary forms, with or without
* modification, are permitted provided that the following conditions
* are met:
*
* 1. Redistributions of source code must retain the above copyright
*    notice, this list of conditions and the following disclaimer.
*
* 2. Redistributions in binary form must reproduce the above copyright
*    notice, this list of conditions and the following disclaimer in
*    the documentation and/or other materials provided with the
*    distribution.
*
* 3. The names "VOCAL", "Vovida Open Communication Application Library",
*    and "Vovida Open Communication Application Library (VOCAL)" must
*    not be used to endorse or promote products derived from this
*    software without prior written permission. For written
*    permission, <NAME_EMAIL>.
*
* 4. Products derived from this software may not be called "VOCAL", nor
*    may "VOCAL" appear in their name, without prior written
*    permission of Vovida Networks, Inc.
*
* THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
* WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
* OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, TITLE AND
* NON-INFRINGEMENT ARE DISCLAIMED.  IN NO EVENT SHALL VOVIDA
* NETWORKS, INC. OR ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT DAMAGES
* IN EXCESS OF $1,000, NOR FOR ANY INDIRECT, INCIDENTAL, SPECIAL,
* EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
* PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
* PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY
* OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
* (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE
* USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH
* DAMAGE.
*
* ====================================================================
*
* This software consists of voluntary contributions made by Vovida
* Networks, Inc. and many individuals on behalf of Vovida Networks,
* Inc.  For more information on Vovida Networks, Inc., please see
* <http://www.vovida.org/>.
*
*/
