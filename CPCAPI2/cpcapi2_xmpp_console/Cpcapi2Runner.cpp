#if _WIN32
#include "stdafx.h"
#endif

#include "Cpcapi2Runner.h"

#include "brand_branded.h"
#include "Logger.h"

#include <interface/experimental/xmpp/XmppAccountInternal.h>
#include <interface/experimental/xmpp/XmppAccountJsonProxy.h>
#include <interface/experimental/xmpp/XmppChatJsonProxy.h>

#include <impl/jsonapi/JsonApiClientInterface.h>
#include <impl/xmpp/jsonapi/XmppAccountJsonProxyInterface.h>
#include <impl/xmpp/jsonapi/XmppChatJsonProxyInterface.h>

#include <rutil/Random.hxx>

#include <fstream>

using namespace CPCAPI2;
using namespace CPCAPI2::XmppAccount;
using namespace CPCAPI2::XmppChat;

namespace CPCAPI2
{
namespace Agent
{
Cpcapi2Runner::Cpcapi2Runner(int maxAccounts, const std::string& xmppProxy, const resip::Data& acctUsername, int sdkThreadPoolThreadIdx, resip::MultiReactor* appReactor, CPCAPI2::Phone* masterSdkPhone)
   : mAcctUsername(acctUsername), mSdkThreadPoolThreadIdx(sdkThreadPoolThreadIdx), mAppReactor(appReactor), mPhone(NULL), mMasterSdkPhone(masterSdkPhone),
     mStatsTimer(*appReactor), mMaxAccounts(1), mXmppProxy(xmppProxy)
   //, mTTSApi(new WatsonTTS())
{
   appInit(mMaxAccounts);
   //mStatsTimer.expires_from_now(5000);
   //mStatsTimer.async_wait(this, 8889, NULL);
}

Cpcapi2Runner::~Cpcapi2Runner()
{
   mAppReactor->detach();
}

CPCAPI2::PhoneInternal* Cpcapi2Runner::getPhoneInternalForLogger() const
{
   return mPhone;
}

void Cpcapi2Runner::shutdown()
{
   assert(mAppReactor->isCurrentThread());
   appShutdown();
}

void Cpcapi2Runner::handleSdkCallback()
{
   mAppReactor->post(resip::resip_bind(&Cpcapi2Runner::handleSdkCallbackImpl, this));
}

void Cpcapi2Runner::handleSdkCallbackImpl()
{
   mPhone->process(CPCAPI2::Phone::kBlockingModeNonBlocking);
   mAccount->process(CPCAPI2::Phone::kBlockingModeNonBlocking);
   mJsonApiClient->process(CPCAPI2::Phone::kBlockingModeNonBlocking);
}

void sdkCallbackHook(void* context)
{
   Cpcapi2Runner* cpcRunner = (Cpcapi2Runner*)context;
   cpcRunner->handleSdkCallback();
}

void Cpcapi2Runner::appInit(int maxAccounts)
{
   mPhone = CPCAPI2::PhoneInternal::create(mSdkThreadPoolThreadIdx);
   LicenseInfo licenseInfo;
   licenseInfo.licenseKey = "lkj";
   licenseInfo.licenseDocumentLocation = "C:\\Temp";
   licenseInfo.licenseAor = "";
   mPhone->setCallbackHook(sdkCallbackHook, this);
   mPhone->initialize(licenseInfo, this, false);
   mPhone->setLoggingEnabled("cpcxmppcon", true);

/*
{\
\"authProvider\" : \"stretto\",\
\"username\" : \"testuser\",\
\"password\" : \"foo\"\
}
*/

   InfoLog(<< "Cpcapi2Runner::thread: phone instance initialized.");

   mJsonApiClient = CPCAPI2::JsonApi::JsonApiClient::getInterface(mPhone);
   dynamic_cast<CPCAPI2::JsonApi::JsonApiClientInterface*>(mJsonApiClient)->setCallbackHook(sdkCallbackHook, this);

   mJsonApiClient->setHandler(this);

   mAccount = CPCAPI2::XmppAccount::XmppAccountManagerJsonProxy::getInterface(mPhone);
   dynamic_cast<XmppAccountJsonProxyInterface*>(mAccount)->setCallbackHook(sdkCallbackHook, this);

   //int randMs = resip::Random::getCryptoRandom() % 20000;
   //mAppReactor->postMS(resip::resip_bind(&Cpcapi2Runner::doLogin, this), randMs);
}

void Cpcapi2Runner::doLogin()
{
   //mJsonApiClient->connect("ws://**************:9003");
   //mJsonApiClient->connect("ws://***************:9003");
   //mJsonApiClient->connect("ws://***************:9003");
   //mJsonApiClient->connect("ws://**************:9003");
   //mJsonApiClient->connect("ws://**************:9003");
   mJsonApiClient->connect("ws://***********:9003");
   //mJsonApiClient->connect("ws://**************:9003");
   cpc::string loginContext = mAcctUsername.c_str();
   mJsonApiClient->login(loginContext);
}

void Cpcapi2Runner::appShutdown()
{
   //InfoLog(<< "Cpcapi2Runner::thread: cpcapi2 rtp proxy is shutdown!");
   std::map<XmppAccountHandle, AcctInfo>::iterator itAcctInfo = mAccounts.begin();
   for (; itAcctInfo != mAccounts.end(); ++itAcctInfo)
   {
      mAccount->disable(itAcctInfo->first);
   }
   mAccount->process(5000);
   mAccount->process(10000);
   mPhone->process(100);
}

void Cpcapi2Runner::initAccountSettings(int maxAccounts)
{
   mAccountSettings.clear();

   for (int i = 0; i < maxAccounts; i++)
   {
      XmppAccountSettings acctSettings;
      //acctSettings.username = mAcctUsername.c_str();
      //acctSettings.password = mAcctUsername.c_str();
      //acctSettings.domain = "cpdebianfs.local";
      //acctSettings.proxy = "*************"; // mXmppProxy.c_str();
      //acctSettings.ignoreCertVerification = true;
      //acctSettings.resource = resip::Random::getCryptoRandom(4).base64encode(true).c_str();
      //acctSettings.connectTimeOut = 5*60;
      //acctSettings.nameServers.clear();
      //acctSettings.nameServers.push_back("*******");
      acctSettings.username = "jgeras";
      acctSettings.password = "";
      acctSettings.domain = "counterpath.com";
      //acctSettings.proxy = "*************"; // mXmppProxy.c_str();
      acctSettings.ignoreCertVerification = true;
      acctSettings.resource = resip::Random::getCryptoRandom(4).base64encode(true).c_str();
      acctSettings.connectTimeOut = 5 * 60;
      acctSettings.nameServers.clear();
      acctSettings.nameServers.push_back("*******");

      mAccountSettings.push_back(acctSettings);
   }

   //InfoLog(<< "cpcapi2_conf account name: " << mAccountSettings.username << "@" << mAccountSettings.domain);
}

// JsonApiClientHandler
int Cpcapi2Runner::onConnectResult(CPCAPI2::JsonApi::JsonApiConnectionHandle h, const CPCAPI2::JsonApi::ConnectResultEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onLoginResult(CPCAPI2::JsonApi::JsonApiLoginHandle h, const CPCAPI2::JsonApi::LoginResultEvent& args)
{
   if (args.success)
   {
      initAccountSettings(mMaxAccounts);
      cpc::vector<XmppAccount::XmppAccountSettings>::iterator itAcctSettings = mAccountSettings.begin();
      int acctCnt = 0;
      for (; itAcctSettings != mAccountSettings.end() && acctCnt < mMaxAccounts; ++itAcctSettings, acctCnt++)
      {
         XmppAccount::XmppAccountHandle acctHandle = mAccount->create(*itAcctSettings);
         mAccounts[acctHandle].accountSettings = *itAcctSettings;
         mAccount->configureDefaultAccountSettings(acctHandle, *itAcctSettings);
         mAccount->applySettings(acctHandle);
         mAccount->setHandler(acctHandle, this);
      }

      InfoLog(<< "Cpcapi2Runner::thread: cpcapi2 rtp proxy account initialized");

      mChat = XmppChatManagerJsonProxy::getInterface(mPhone);

      std::map<XmppAccountHandle, AcctInfo>::iterator itAcctInfo = mAccounts.begin();
      for (; itAcctInfo != mAccounts.end(); ++itAcctInfo)
      {
         mChat->setHandler(itAcctInfo->first, this);
      }

      itAcctInfo = mAccounts.begin();
      for (; itAcctInfo != mAccounts.end(); ++itAcctInfo)
      {
         mAcctsToEnable.push_back(itAcctInfo->first);
      }

      CPCAPI2::XmppAccount::XmppAccountHandle firstAccount = mAcctsToEnable.front();
      mAcctsToEnable.pop_front();

      const int intervalMs = 5000;
      mAppReactor->postMS(resip::resip_bind(&XmppAccountManager::enable, mAccount, firstAccount), intervalMs);
   }
   return 0;
}

int Cpcapi2Runner::onChatDiscoCompleted(XmppChatHandle chat, const ChatDiscoEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onChatDiscoError(XmppChatHandle chat, const ChatDiscoErrorEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onError(const cpc::string& sourceModule, const CPCAPI2::PhoneErrorEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onLicensingError(const CPCAPI2::LicensingErrorEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onAccountStatusChanged(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent & args)
{
   if (args.accountStatus == XmppAccountStatusChangedEvent::Status_Connected)
   {
      int randMs = 500 + (std::abs(resip::Random::getCryptoRandom()) % 25000);
      mAppReactor->postMS(resip::resip_bind(&Cpcapi2Runner::startChat, this, account), randMs);
   }
   return 0;
}

int Cpcapi2Runner::onError(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::ErrorEvent & args)
{
   return 0;
}

int Cpcapi2Runner::onLicensingError(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::LicensingErrorEvent & args)
{
   return 0;
}

int Cpcapi2Runner::onEntityTime(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::EntityTimeEvent & args)
{
   return 0;
}

int Cpcapi2Runner::onEntityFeature(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::EntityFeatureEvent & args)
{
   return 0;
}

int Cpcapi2Runner::onNewChat(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::NewChatEvent & args)
{
   if (args.chatType == XmppChat::ChatType_Incoming)
   {
      mChat->accept(chat);
      mChat->sendMessage(chat, "hello");
   }
   return 0;
}

int Cpcapi2Runner::onIsComposingMessage(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::IsComposingMessageEvent & args)
{
   return 0;
}

int Cpcapi2Runner::onNewMessage(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::NewMessageEvent & args)
{
   mChat->notifyMessageDelivered(chat, args.message, XmppChat::MessageDeliveryStatus_Delivered);
   int randMs = 20000 + (std::abs(resip::Random::getCryptoRandom()) % 120000);
   mAppReactor->postMS(resip::resip_bind(&Cpcapi2Runner::sendAnotherMessage, this, chat), randMs);
   return 0;
}

int Cpcapi2Runner::onNewReaction(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::NewReactionEvent & args)
{
   // DRL FIXIT? Do we need the message delivered code from onNewMessage here too?
   return 0;
}

int Cpcapi2Runner::onNewMessageRetraction(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::NewMessageRetractionEvent & args)
{
   // DRL FIXIT? Do we need the message delivered code from onNewMessage here too?
   return 0;
}

int Cpcapi2Runner::onSendMessageSuccess(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::SendMessageSuccessEvent & args)
{
   return 0;
}

int Cpcapi2Runner::onSendMessageFailure(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::SendMessageFailureEvent & args)
{
   return 0;
}

int Cpcapi2Runner::onMessageDelivered(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::MessageDeliveredEvent & args)
{
   return 0;
}

int Cpcapi2Runner::onMessageDeliveryError(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::MessageDeliveryErrorEvent & args)
{
   return 0;
}

int Cpcapi2Runner::onMessageDisplayed(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::MessageDisplayedEvent & args)
{
   return 0;
}

int Cpcapi2Runner::onChatEnded(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::ChatEndedEvent & event)
{
   return 0;
}

int Cpcapi2Runner::onError(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::ErrorEvent & event)
{
   return 0;
}

void Cpcapi2Runner::onTimer(unsigned short timerId, void* appState)
{

}

void Cpcapi2Runner::startChat(CPCAPI2::XmppAccount::XmppAccountHandle account)
{
   if (mChats.find(account) == mChats.end())
   {
      XmppAccountSettings acctSettings = mAccounts[account].accountSettings;
      XmppChatHandle chat = mChat->createChat(account);
      mChat->addParticipant(chat, acctSettings.username + "@" + acctSettings.domain);
      mChat->start(chat);
      mChats[account].chat = chat;
      mChats[account].address = acctSettings.username + "@" + acctSettings.domain;

      mChat->sendMessage(chat, "initial");
   }
}

void Cpcapi2Runner::sendAnotherMessage(CPCAPI2::XmppChat::XmppChatHandle chat)
{
   mChat->sendMessage(chat, "another message");
}

}
}
