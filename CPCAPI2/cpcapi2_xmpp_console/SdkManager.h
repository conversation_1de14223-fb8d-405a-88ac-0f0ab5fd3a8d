#pragma once

#include <cpcapi2.h>
#include <jsonapi/JsonApiServer.h>
#include <jsonapi/JsonApiServerHandler.h>
#include <phone/PhoneInternal.h>

// rutil includes
#include <rutil/MultiReactor.hxx>
#include <rutil/Data.hxx>

namespace CPCAPI2
{
namespace Agent
{
class Cpcapi2Runner;
class SdkManager : public CPCAPI2::PhoneErrorHandler,
                   public CPCAPI2::JsonApi::JsonApiServerHandler
{
public:
   SdkManager(int maxAccounts=5000, const std::string& xmppProxy="");
   ~SdkManager();

   void run();
   void shutdown();
   void join();

   void handleSdkCallback();

   // PhoneErrorHandler
   virtual int onError(const cpc::string& sourceModule, const CPCAPI2::PhoneErrorEvent& args);
   virtual int onLicensingError(const CPCAPI2::LicensingErrorEvent& args);

   // JsonApiServerHandler
   virtual int onNewLogin(CPCAPI2::JsonApi::JsonApiUserHandle json<PERSON>pi<PERSON>, const CPCAPI2::JsonApi::NewLoginEvent& args);

private:
   void appInit();
   void initAccounts();
   void appShutdown();
   void handleSdkCallbackImpl();

private:
   int mMaxAccounts;
   std::string mXmppProxy;
   resip::MultiReactor mReactor;
   CPCAPI2::PhoneInternal* mPhone;
   CPCAPI2::JsonApi::JsonApiServer* mJsonApiServer;
   std::map<resip::Data, Cpcapi2Runner*> mMapContextToRunner;
};
}
}
