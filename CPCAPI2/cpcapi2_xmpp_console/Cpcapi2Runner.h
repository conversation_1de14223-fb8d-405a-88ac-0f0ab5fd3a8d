#pragma once

// SDK includes
#include <cpcapi2.h>
#include <jsonapi/JsonApiClient.h>
#include <phone/PhoneInternal.h>

#include "CpcWebSocketServer.h"

// rutil includes
#include <rutil/MultiReactor.hxx>
#include <rutil/Data.hxx>
#include <rutil/DeadlineTimer.hxx>

#include <list>

namespace CPCAPI2
{
namespace Agent
{
class WatsonTTS;

class Cpcapi2Runner : public CPCAPI2::PhoneErrorHandler,
                      public CPCAPI2::XmppAccount::XmppAccountHandler,
                      public CPCAPI2::XmppChat::XmppChatHandler,
                      public CPCAPI2::JsonApi::JsonApiClientHandler,
                      public resip::DeadlineTimerHandler
{
public:
   Cpcapi2Runner(int maxAccounts, const std::string& xmppProxy, const resip::Data& acctUsername, int sdkThreadPoolThreadIdx, resip::MultiRea<PERSON>* appReactor, CPCAPI2::Phone* masterSdkPhone);
   virtual ~Cpcapi2Runner();

   void handleSdkCallback();
   void shutdown();
   void doLogin();

   CPCAPI2::Phone* getPhone() const {
      return mPhone;
   }

   // PhoneErrorHandler
   virtual int onError(const cpc::string& sourceModule, const CPCAPI2::PhoneErrorEvent& args);
   virtual int onLicensingError(const CPCAPI2::LicensingErrorEvent& args);

   // Inherited via XmppAccountHandler
   virtual int onAccountStatusChanged(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent & args);
   virtual int onError(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::ErrorEvent & args);
   virtual int onLicensingError(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::LicensingErrorEvent & args);
   virtual int onEntityTime(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::EntityTimeEvent & args);
   virtual int onEntityFeature(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::EntityFeatureEvent & args);

   // Inherited via XmppChatHandler
   virtual int onNewChat(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::NewChatEvent & args);
   virtual int onIsComposingMessage(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::IsComposingMessageEvent & args);
   virtual int onNewMessage(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::NewMessageEvent & args);
   virtual int onNewReaction(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::NewReactionEvent & args);
   virtual int onNewMessageRetraction(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::NewMessageRetractionEvent & args);
   virtual int onSendMessageSuccess(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::SendMessageSuccessEvent & args);
   virtual int onSendMessageFailure(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::SendMessageFailureEvent & args);
   virtual int onMessageDelivered(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::MessageDeliveredEvent & args);
   virtual int onMessageDeliveryError(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::MessageDeliveryErrorEvent & args);
   virtual int onMessageDisplayed(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::MessageDisplayedEvent & args);
   virtual int onChatEnded(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::ChatEndedEvent & event);
   virtual int onChatDiscoCompleted(XmppChatHandle chat, const ChatDiscoEvent& args);
   virtual int onChatDiscoError(XmppChatHandle chat, const ChatDiscoErrorEvent& args);
   virtual int onError(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::ErrorEvent & event);

   // JsonApiClientHandler
   virtual int onConnectResult(CPCAPI2::JsonApi::JsonApiConnectionHandle h, const CPCAPI2::JsonApi::ConnectResultEvent& args);
   virtual int onLoginResult(CPCAPI2::JsonApi::JsonApiLoginHandle h, const CPCAPI2::JsonApi::LoginResultEvent& args);

private:
   void appInit(int maxAccounts);
   void appShutdown();

   void handleSdkCallbackImpl();

   CPCAPI2::PhoneInternal* getPhoneInternalForLogger() const;
   void initAccountSettings(int maxAccounts);

   // resip::DeadlineTimerHandler
   void onTimer(unsigned short timerId, void* appState) OVERRIDE;

   void startChat(CPCAPI2::XmppAccount::XmppAccountHandle acct);
   void sendAnotherMessage(CPCAPI2::XmppChat::XmppChatHandle chat);

private:
   resip::Data mAcctUsername;
   int mSdkThreadPoolThreadIdx;
   resip::MultiReactor* mAppReactor;
   CPCAPI2::PhoneInternal* mPhone;
   CPCAPI2::Phone* mMasterSdkPhone;
   CPCAPI2::JsonApi::JsonApiClient* mJsonApiClient;
   CPCAPI2::XmppAccount::XmppAccountManager* mAccount;
   CPCAPI2::XmppChat::XmppChatManager* mChat;
   cpc::vector<CPCAPI2::XmppAccount::XmppAccountSettings> mAccountSettings;

   struct AcctInfo
   {
      CPCAPI2::XmppAccount::XmppAccountSettings accountSettings;
   };
   std::map<CPCAPI2::XmppAccount::XmppAccountHandle, AcctInfo> mAccounts;

   struct ChatInfo
   {
      CPCAPI2::XmppChat::XmppChatHandle chat;
      cpc::string address;
      cpc::string displayName;
   };
   std::map<CPCAPI2::XmppAccount::XmppAccountHandle, ChatInfo> mChats;

   resip::DeadlineTimer<resip::MultiReactor> mStatsTimer;
   std::list<CPCAPI2::XmppAccount::XmppAccountHandle> mAcctsToEnable;
   int mMaxAccounts;
   std::string mXmppProxy;
};
}
}
