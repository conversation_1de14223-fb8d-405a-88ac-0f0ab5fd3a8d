ROOT_PATH = ../..
GLOOX_PATH = $(ROOT_PATH)/shared/gloox
RESIP_PATH = $(ROOT_PATH)/shared/sipfoundry/main
CXXFLAGS_WARNINGS = -Wno-deprecated-declarations
CXXFLAGS_OPTIMIZATIONS = 
CXXFLAGS_DEBUG = -g -std=c++11
LDFLAGS = -L.
INCLUDE_DIRS = -I$(RESIP_PATH) -I$(ROOT_PATH)/../linux_libs/openssl/include
CXXFLAGS = $(CXXFLAGS_WARNINGS) $(CXXFLAGS_OPTIMIZATIONS) $(CXXFLAGS_DEBUG) $(INCLUDE_DIRS)
RESIP_SRCS = \
    src/CpcXepUserActivity.cpp \
    src/CpcResipLogging.cpp \
    src/adhoc.cpp \
    src/amp.cpp \
    src/annotations.cpp \
    src/atomicrefcount.cpp \
    src/attention.cpp \
    src/base64.cpp \
    src/bookmarkstorage.cpp \
    src/capabilities.cpp \
    src/carbons.cpp \
    src/chatstate.cpp \
    src/chatstatefilter.cpp \
    src/client.cpp \
    src/clientbase.cpp \
    src/component.cpp \
    src/compressiondefault.cpp \
    src/compressionzlib.cpp \
    src/connectionbosh.cpp \
    src/connectionhttpproxy.cpp \
    src/connectionsocks5proxy.cpp \
    src/connectiontcpbase.cpp \
    src/connectiontcpclient.cpp \
    src/connectiontcpserver.cpp \
    src/connectiontls.cpp \
    src/connectiontlsserver.cpp \
    src/dataform.cpp \
    src/dataformfield.cpp \
    src/dataformfieldcontainer.cpp \
    src/dataformitem.cpp \
    src/dataformreported.cpp \
    src/delayeddelivery.cpp \
    src/disco.cpp \
    src/dns.cpp \
    src/error.cpp \
    src/eventdispatcher.cpp \
    src/featureneg.cpp \
    src/flexoff.cpp \
    src/gloox.cpp \
    src/gpgencrypted.cpp \
    src/gpgsigned.cpp \
    src/inbandbytestream.cpp \
    src/instantmucroom.cpp \
    src/iq.cpp \
    src/jid.cpp \
    src/jinglecontent.cpp \
    src/jinglefiletransfer.cpp \
    src/jingleiceudp.cpp \
    src/jinglepluginfactory.cpp \
    src/jinglesession.cpp \
    src/jinglesessionmanager.cpp \
    src/lastactivity.cpp \
    src/logsink.cpp \
    src/md5.cpp \
    src/message.cpp \
    src/messageevent.cpp \
    src/messageeventfilter.cpp \
    src/messagefilter.cpp \
    src/messagesession.cpp \
    src/mucmessagesession.cpp \
    src/mucinvitationhandler.cpp \
    src/mucroom.cpp \
    src/mutex.cpp \
    src/nickname.cpp \
    src/nonsaslauth.cpp \
    src/oob.cpp \
    src/parser.cpp \
    src/prep.cpp \
    src/presence.cpp \
    src/privacyitem.cpp \
    src/privacymanager.cpp \
    src/privatexml.cpp \
    src/pubsubevent.cpp \
    src/pubsubitem.cpp \
    src/pubsubmanager.cpp \
    src/receipt.cpp \
    src/registration.cpp \
    src/rosteritem.cpp \
    src/rostermanager.cpp \
    src/search.cpp \
    src/sha.cpp \
    src/shim.cpp \
    src/simanager.cpp \
    src/siprofileft.cpp \
    src/socks5bytestream.cpp \
    src/socks5bytestreammanager.cpp \
    src/socks5bytestreamserver.cpp \
    src/softwareversion.cpp \
    src/stanza.cpp \
    src/stanzaextensionfactory.cpp \
    src/subscription.cpp \
    src/tag.cpp \
    src/tlsdefault.cpp \
    src/tlsgnutlsbase.cpp \
    src/tlsgnutlsclient.cpp \
    src/tlsgnutlsclientanon.cpp \
    src/tlsgnutlsserveranon.cpp \
    src/tlsopensslbase.cpp \
    src/tlsopensslclient.cpp \
    src/tlsopensslserver.cpp \
    src/tlsschannel.cpp \
    src/uniquemucroom.cpp \
    src/util.cpp \
    src/utils.common.cpp \
    src/utils.socket.cpp \
    src/vcard.cpp \
    src/vcardmanager.cpp \
    src/vcardupdate.cpp \
    src/xhtmlim.cpp
SRCS = $(RESIP_SRCS:%=$(GLOOX_PATH)/%)
OBJS = $(SRCS:%.cpp=%.o)
HEADERS = $(SRCS:%.cpp=%.h)
TARGET = libgloox.a

.PHONY: all
all : $(OBJS) $(TARGET)

.PHONY: rebuild
rebuild : clean all

.PHONY: clean
clean :
	$(RM) $(OBJS)
	$(RM) $(TARGET)

$(OBJS) : $(SRCS)

$(TARGET) : $(OBJS)
	$(AR) rcs $(TARGET) $(OBJS)

# Rule to generate a .o file from the .c file.
%.o : %.c
	$(CC) -c $(CXXFLAGS) $< -o $@
