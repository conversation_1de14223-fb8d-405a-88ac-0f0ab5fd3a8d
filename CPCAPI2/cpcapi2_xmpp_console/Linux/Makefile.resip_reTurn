ROOT_PATH = ../..
RESIP_PATH = $(ROOT_PATH)/shared/sipfoundry/main
RESIP_RETURN_PATH = $(RESIP_PATH)/reTurn
CXXFLAGS_WARNINGS = -Wno-deprecated-declarations
CXXFLAGS_OPTIMIZATIONS = -O3 -DNDEBUG
CXXFLAGS_DEBUG = -g
CXXFLAGS_MACROS = -DASIO_DISABLE_IOCP -DASIO_ENABLE_CANCELIO -DRESIP_USE_STL_STREAMS -DUSE_IPV6 -DUSE_SSL -DRESIP_USE_POLL_FDSET -DUSE_DNS_VIP
INCLUDE_DIRS = -I$(RESIP_PATH) -I$(ROOT_PATH)/external/boost/include -I$(ROOT_PATH)/../linux_libs/openssl/include
CXXFLAGS = -std=c++11 -frtti -fexceptions $(CXXFLAGS_WARNINGS) $(CXXFLAGS_OPTIMIZATIONS) $(CXXFLAGS_DEBUG) $(CXXFLAGS_MACROS) $(INCLUDE_DIRS)
RESIP_RETURN_SRCS = \
   ChannelManager.cxx DataBuffer.cxx RemotePeer.cxx \
   ReTurnSubsystem.cxx StunMessage.cxx StunTuple.cxx \
   client/TurnAsyncSocket_no_asio.cxx client/TurnAsyncSocketHandler.cxx \
   client/TurnAsyncUdpSocket_no_asio.cxx
SRCS = $(RESIP_RETURN_SRCS:%=$(RESIP_RETURN_PATH)/%)
OBJS = $(SRCS:%.cxx=%.o)
TARGET = libresip_reTurn.a

.PHONY: all
all : $(OBJS) $(TARGET)

.PHONY: rebuild
rebuild : clean all

.PHONY: clean
clean :
	$(RM) $(OBJS)
	$(RM) $(TARGET)

$(OBJS) : $(SRCS)

$(TARGET) : $(OBJS)
	$(AR) rcs $(TARGET) $(OBJS)

# Rule to generate a .o file from the .c file.
%.o : %.cxx
	$(CXX) -c $(CXXFLAGS) $< -o $@
