#include "stdafx.h"
#include "SdkManager.h"
#include "Cpcapi2Runner.h"

#include <cpcapi2.h>

#include <fstream>

#include <rutil/Random.hxx>

#define CPCAPI2_CONF_NUM_SDK_REACTORS 4

using namespace CPCAPI2;
using namespace CPCAPI2::SipAccount;

namespace CPCAPI2
{
namespace Agent
{
SdkManager::SdkManager(int maxAccounts, const std::string& xmppProxy)
   : mMaxAccounts(maxAccounts), mXmppProxy(xmppProxy)
{
}

SdkManager::~SdkManager()
{
}

void SdkManager::run()
{
   mReactor.start();
   mReactor.post(resip::resip_bind(&SdkManager::appInit, this));
}

void SdkManager_sdkCallbackHook(void* context)
{
   SdkManager* cpcRunner = (SdkManager*)context;
   cpcRunner->handleSdkCallback();
}

void SdkManager::handleSdkCallback()
{
   mReactor.post(resip::resip_bind(&SdkManager::handleSdkCallbackImpl, this));
}

void SdkManager::handleSdkCallbackImpl()
{
   mPhone->process(CPCAPI2::Phone::kBlockingModeNonBlocking);
}

void SdkManager::appInit()
{
   mPhone = CPCAPI2::PhoneInternal::create(0);
   LicenseInfo licenseInfo;
   licenseInfo.licenseKey = "lkj";
   licenseInfo.licenseDocumentLocation = "/tmp";
   licenseInfo.licenseAor = "";
   mPhone->setCallbackHook(SdkManager_sdkCallbackHook, this);
   mPhone->initialize(licenseInfo, this, false);
   mPhone->setLoggingEnabled("cpcapi2_xmpp_console", true);

   initAccounts();
}

void SdkManager::shutdown()
{
   //mTTSApi->shutdown();
   mReactor.execute(resip::resip_bind(&SdkManager::appShutdown, this));
   mReactor.stop();
}

void SdkManager::appShutdown()
{
   std::map<resip::Data, Cpcapi2Runner*> contextMapCpy = mMapContextToRunner;
   std::map<resip::Data, Cpcapi2Runner*>::iterator it = contextMapCpy.begin();
   for (; it != contextMapCpy.end(); ++it)
   {
      Cpcapi2Runner* runner = it->second;
      runner->shutdown();
      delete runner;
   }

   mJsonApiServer->shutdown();
}

void SdkManager::join()
{
   mReactor.join();
}

void SdkManager::initAccounts()
{
   //resip::Data accountPrefix = resip::Random::getCryptoRandom(4).base64encode(true);
   int startOffset = 1000;
   for (int i = 0; i < 1; i++)
   {
      resip::Data accountUsername;
      {
         resip::DataStream ds(accountUsername);
         ds << "cpuser" << (startOffset+i);
      }
      Cpcapi2Runner* sdk = new Cpcapi2Runner(mMaxAccounts, mXmppProxy, accountUsername, i%CPCAPI2_CONF_NUM_SDK_REACTORS, &mReactor, mPhone);
      mMapContextToRunner[accountUsername] = sdk;
   }

   int lc = 0;
   std::map<resip::Data, Cpcapi2Runner*>::iterator it = mMapContextToRunner.begin();
   for (; it != mMapContextToRunner.end(); ++it, lc++)
   {
      int randMs = 100*lc;
      mReactor.postMS(resip::resip_bind(&Cpcapi2Runner::doLogin, it->second), randMs);
   }
}

int SdkManager::onError(const cpc::string& sourceModule, const CPCAPI2::PhoneErrorEvent& args)
{
   return 0;
}

int SdkManager::onLicensingError(const CPCAPI2::LicensingErrorEvent& args)
{
   return 0;
}

int SdkManager::onNewLogin(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const CPCAPI2::JsonApi::NewLoginEvent& args)
{
   return 0;
}


}
}
