AR=gcc-ar
NM=gcc-nm
RANLIB=gcc-ranlib
ROOT_PATH = ../..
RESIP_PATH = $(ROOT_PATH)/shared/sipfoundry/main
RESIP_REFLOW_PATH = $(RESIP_PATH)/reflow
CXXFLAGS_WARNINGS = -Wno-deprecated-declarations
CXXFLAGS_OPTIMIZATIONS = -O3 -DNDEBUG -flto
CXXFLAGS_DEBUG = -g
CXXFLAGS_MACROS = -DUSE_SSL -DRESIP_USE_STL_STREAMS -DUSE_DTLS -DUSE_IPV6 -DRESIP_USE_POLL_FDSET
INCLUDE_DIRS = -I$(RESIP_REFLOW_PATH) -I$(RESIP_PATH) -I$(ROOT_PATH)/../linux_libs/openssl/include -I$(ROOT_PATH)/shared/libsrtp/srtp/include -I$(ROOT_PATH)/shared/libsrtp/srtp/crypto/include -I$(ROOT_PATH)/external/boost/include
CXXFLAGS = -std=c++11 -frtti -fexceptions $(CXXFLAGS_WARNINGS) $(CXXFLAGS_OPTIMIZATIONS) $(CXXFLAGS_DEBUG) $(CXXFLAGS_MACROS) $(INCLUDE_DIRS)
RESIP_REFLOW_SRCS = \
    dtls_wrapper/bf_dwrap.c dtls_wrapper/DtlsFactory.cxx dtls_wrapper/DtlsSocket.cxx dtls_wrapper/DtlsTimer.cxx \
    FakeSelectSocketDescriptor.cxx Flow.cxx FlowDtlsSocketContext.cxx FlowDtlsTimerContext.cxx \
    FlowManager.cxx FlowManagerSubsystem.cxx MediaStream.cxx TurnSocketFactory.cxx
SRCS = $(RESIP_REFLOW_SRCS:%=$(RESIP_REFLOW_PATH)/%)
OBJS = $(patsubst %.cxx,%.o,$(patsubst %.c,%.o, $(SRCS)))
TARGET = libresip_reflow.a
CC = $(CXX)
CFLAGS = $(CXXFLAGS)

.PHONY: all
all : $(OBJS) $(TARGET)

.PHONY: rebuild
rebuild : clean all

.PHONY: clean
clean :
	$(RM) $(OBJS)
	$(RM) $(TARGET)

$(OBJS) : $(SRCS)

$(TARGET) : $(OBJS)
	$(AR) rcs $(TARGET) $(OBJS)

# Rule to generate a .o file from the .c file.
%.o : %.cxx
	$(CXX) -c $(CXXFLAGS) $< -o $@
