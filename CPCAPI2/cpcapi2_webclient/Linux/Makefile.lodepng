ROOT_PATH = ../..
LODEPNG_PATH = $(ROOT_PATH)/external/lodepng
CXXFLAGS_WARNINGS = -Wno-deprecated-declarations
CXXFLAGS_OPTIMIZATIONS = -O3
CXXFLAGS_DEBUG = -g
CXXFLAGS_MACROS = 
LDFLAGS = -L.
INCLUDE_DIRS = -I$(LODEPNG_PATH)
CXXFLAGS = $(CXXFLAGS_WARNINGS) $(CXXFLAGS_OPTIMIZATIONS) $(CXXFLAGS_DEBUG) $(CXXFLAGS_MACROS) $(INCLUDE_DIRS)
LODEPNG_SRCS = \
 lodepng.cpp lodepng_util.cpp pngdetail.cpp
SRCS = $(LODEPNG_SRCS:%=$(LODEPNG_PATH)/%)
OBJS = $(SRCS:%.cpp=%.o)
HEADERS = $(SRCS:%.cpp=%.h)
TARGET = liblodepng.a

.PHONY: all
all : $(OBJS) $(TARGET)

.PHONY: rebuild
rebuild : clean all

.PHONY: clean
clean :
	$(RM) $(OBJS)
	$(RM) $(TARGET)

$(OBJS) : $(SRCS)

$(TARGET) : $(OBJS)
	$(AR) rcs $(TARGET) $(OBJS)

# Rule to generate a .o file from the .c file.
%.o : %.c
	$(CC) -c $(CXXFLAGS) $< -o $@
