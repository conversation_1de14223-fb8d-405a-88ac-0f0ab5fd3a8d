ROOT_PATH = ../../..
CURLPP_PATH = $(ROOT_PATH)/linux_libs/curlpp
CXXFLAGS_WARNINGS = -Wno-deprecated-declarations
CXXFLAGS_OPTIMIZATIONS = -O3
CXXFLAGS_DEBUG = -g
CXXFLAGS_MACROS = 
LDFLAGS = -L.
INCLUDE_DIRS = -I$(CURLPP_PATH)/include
CXXFLAGS = $(CXXFLAGS_WARNINGS) $(CXXFLAGS_OPTIMIZATIONS) $(CXXFLAGS_DEBUG) $(CXXFLAGS_MACROS) $(INCLUDE_DIRS)
CURLPP_SRCS = \
 src/curlpp/cURLpp.cpp \
 src/curlpp/Easy.cpp \
 src/curlpp/Exception.cpp \
 src/curlpp/Form.cpp \
 src/curlpp/Info.cpp \
 src/curlpp/Multi.cpp \
 src/curlpp/OptionBase.cpp \
 src/curlpp/Options.cpp \
 src/curlpp/internal/CurlHandle.cpp \
 src/curlpp/internal/OptionList.cpp \
 src/curlpp/internal/OptionSetter.cpp \
 src/curlpp/internal/SList.cpp
SRCS = $(CURLPP_SRCS:%=$(CURLPP_PATH)/%)
OBJS = $(SRCS:%.cpp=%.o)
HEADERS = $(SRCS:%.cpp=%.h)
TARGET = libcurlpp.a

.PHONY: all
all : $(OBJS) $(TARGET)

.PHONY: rebuild
rebuild : clean all

.PHONY: clean
clean :
	$(RM) $(OBJS)
	$(RM) $(TARGET)

$(OBJS) : $(SRCS)

$(TARGET) : $(OBJS)
	$(AR) rcs $(TARGET) $(OBJS)

# Rule to generate a .o file from the .c file.
%.o : %.c
	$(CC) -c $(CXXFLAGS) $< -o $@
