AR=gcc-ar
NM=gcc-nm
RANLIB=gcc-ranlib
ROOT_PATH = ../..
RESIP_PATH = $(ROOT_PATH)/shared/sipfoundry/main
RESIP_RUTIL_PATH = $(RESIP_PATH)/rutil
CXXFLAGS_WARNINGS = -Wno-deprecated-declarations
CXXFLAGS_OPTIMIZATIONS = -O3 -DNDEBUG -flto
CXXFLAGS_DEBUG = -g
CXXFLAGS_MACROS = -DUSE_ARES -DRESIP_USE_STL_STREAMS -DUSE_SSL -DUSE_IPV6 -DRESIP_USE_POLL_FDSET
LDFLAGS = -L.
INCLUDE_DIRS = -I$(RESIP_PATH) -I$(ROOT_PATH)/external/boost/include -I$(RESIP_RUTIL_PATH) -I$(ROOT_PATH)/../linux_libs/openssl/include -I$(RESIP_PATH)/rutil/dns/ares
CXXFLAGS = -std=c++11 -frtti -fexceptions -pthread $(CXXFLAGS_WARNINGS) $(CXXFLAGS_OPTIMIZATIONS) $(CXXFLAGS_DEBUG) $(CXXFLAGS_MACROS) $(INCLUDE_DIRS)
RESIP_RUTIL_SRCS = \
    AbstractFifo.cxx BaseException.cxx Coders.cxx Condition.cxx ConfigParse.cxx CountStream.cxx  \
    Data.cxx DataStream.cxx DigestStream.cxx DnsUtil.cxx FdPoll.cxx FileSystem.cxx  \
    GeneralCongestionManager.cxx HeapInstanceCounter.cxx IpSynth.cxx KeyValueStore.cxx Lock.cxx Log.cxx  \
    MD5Stream.cxx Mutex.cxx ParseBuffer.cxx ParseException.cxx Poll.cxx PoolBase.cxx  \
    RADIUSDigestAuthenticator.cxx Random.cxx RecursiveMutex.cxx  \
    resipfaststreams.cxx RWMutex.cxx SelectInterruptor.cxx ServerProcess.cxx Socket.cxx Subsystem.cxx  \
    SysLogBuf.cxx SysLogStream.cxx ThreadIf.cxx Time.cxx Timer.cxx TransportType.cxx vmd5.cxx  \
    XMLCursor.cxx dns/AresDns.cxx dns/DnsAAAARecord.cxx dns/DnsCnameRecord.cxx dns/DnsHostRecord.cxx  \
    dns/DnsNaptrRecord.cxx dns/DnsResourceRecord.cxx dns/DnsSrvRecord.cxx dns/DnsStub.cxx  \
    dns/DnsThread.cxx dns/ExternalDnsFactory.cxx dns/LocalDns.cxx dns/QueryTypes.cxx dns/RRCache.cxx  \
    dns/RRList.cxx dns/RROverlay.cxx dns/RRVip.cxx ssl/OpenSSLInit.cxx ssl/SHA1Stream.cxx  \
    stun/Stun.cxx stun/Udp.cxx
SRCS = $(RESIP_RUTIL_SRCS:%=$(RESIP_RUTIL_PATH)/%)
OBJS = $(SRCS:%.cxx=%.o)
TARGET = libresip_rutil.a

.PHONY: all
all : $(OBJS) $(TARGET)

.PHONY: rebuild
rebuild : clean all

.PHONY: clean
clean :
	$(RM) $(OBJS)
	$(RM) $(TARGET)

$(OBJS) : $(SRCS)

$(TARGET) : $(OBJS)
	$(AR) rcs $(TARGET) $(OBJS)

# Rule to generate a .o file from the .c file.
%.o : %.cxx
	$(CXX) -c $(CXXFLAGS) $< -o $@
