ROOT_PATH = ../..
LIBSRTP_PATH = $(ROOT_PATH)/shared/libsrtp/srtp
CXXFLAGS_WARNINGS = -Wno-deprecated-declarations
CXXFLAGS_OPTIMIZATIONS = 
CXXFLAGS_DEBUG = -g
CXXFLAGS_MACROS = -DUSE_ARES -DRESIP_USE_STL_STREAMS
LDFLAGS = -L.
INCLUDE_DIRS = -I$(LIBSRTP_PATH)/include -I$(LIBSRTP_PATH)/crypto/include
CXXFLAGS = $(CXXFLAGS_WARNINGS) $(CXXFLAGS_OPTIMIZATIONS) $(CXXFLAGS_DEBUG) $(CXXFLAGS_MACROS) $(INCLUDE_DIRS)
RESIP_SRCS = \
                crypto/cipher/aes.c crypto/cipher/aes_cbc.c \
    crypto/cipher/aes_icm.c crypto/cipher/cipher.c crypto/cipher/null_cipher.c \
    crypto/hash/auth.c crypto/hash/hmac.c crypto/hash/null_auth.c \
    crypto/hash/sha1.c crypto/kernel/alloc.c crypto/kernel/crypto_kernel.c \
    crypto/kernel/err.c crypto/kernel/key.c crypto/math/datatypes.c \
    crypto/math/gf2_8.c crypto/math/math.c crypto/math/stat.c \
    crypto/replay/rdb.c crypto/replay/rdbx.c crypto/replay/ut_sim.c \
    crypto/rng/ctr_prng.c crypto/rng/prng.c \
    crypto/rng/rand_source.c srtp/srtp.c srtp/ekt.c tables/aes_tables.c
SRCS = $(RESIP_SRCS:%=$(LIBSRTP_PATH)/%)
OBJS = $(SRCS:%.c=%.o)
HEADERS = $(SRCS:%.c=%.h)
TARGET = libsrtp.a

.PHONY: all
all : $(OBJS) $(TARGET)

.PHONY: rebuild
rebuild : clean all

.PHONY: clean
clean :
	$(RM) $(OBJS)
	$(RM) $(TARGET)

$(OBJS) : $(SRCS)

$(TARGET) : $(OBJS)
	$(AR) rcs $(TARGET) $(OBJS)

# Rule to generate a .o file from the .c file.
%.o : %.c
	$(CC) -c $(CXXFLAGS) $< -o $@
