#if _WIN32
#include "stdafx.h"
#endif

#include "Cpcapi2Runner.h"

#include "brand_branded.h"
#include "Logger.h"
#include "PlaySoundFileStream.h"

#include <interface/experimental/account/SipAccountManagerInternal.h>
#include <interface/experimental/account/SipAccountJsonApi.h>
#include <interface/experimental/call/SipConversationJsonApi.h>
#include <interface/experimental/confbridge/ConferenceBridgeJsonApi.h>
#include <interface/experimental/confbridge/ConferenceBridgeInternal.h>
#include <interface/experimental/media/MediaManagerInternal.h>
#include <interface/experimental/peerconnection/PeerConnectionJsonApi.h>
#include <interface/public/media/MediaManager.h>
#include <interface/experimental/media/AudioJsonApi.h>
#include <interface/experimental/media/VideoJsonApi.h>
#include <interface/experimental/confconnector/ConferenceConnectorJsonApi.h>

#include <fstream>

using namespace CPCAPI2;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::SipConversation;
using namespace CPCAPI2::Media;
using namespace CPCAPI2::PeerConnection;
using namespace CPCAPI2::ConferenceBridge;

namespace CPCAPI2
{
namespace Agent
{
Cpcapi2Runner::Cpcapi2Runner(int sdkThreadPoolThreadIdx, resip::MultiReactor* appReactor, CPCAPI2::Phone* masterSdkPhone, CPCAPI2::JsonApi::JsonApiServer* jsonApiServer)
   : mSdkThreadPoolThreadIdx(sdkThreadPoolThreadIdx), mAppReactor(appReactor), mPhone(NULL), mMasterSdkPhone(masterSdkPhone), mJsonApiServer(jsonApiServer), mAudioStreamCnt(0)
   //, mTTSApi(new WatsonTTS())
{
   appInit();
}

Cpcapi2Runner::~Cpcapi2Runner()
{
   mAppReactor->detach();
}

CPCAPI2::PhoneInternal* Cpcapi2Runner::getPhoneInternalForLogger() const
{
   return mPhone;
}

void Cpcapi2Runner::shutdown()
{
   assert(mAppReactor->isCurrentThread());
   appShutdown();
}

void Cpcapi2Runner::handleSdkCallback()
{
   mAppReactor->post(resip::resip_bind(&Cpcapi2Runner::handleSdkCallbackImpl, this));
}

void Cpcapi2Runner::handleSdkCallbackImpl()
{
   mPhone->process(CPCAPI2::Phone::kBlockingModeNonBlocking);
   mMedia->process(CPCAPI2::Phone::kBlockingModeNonBlocking);
   mPeerConnIf->process(CPCAPI2::Phone::kBlockingModeNonBlocking);
   mConfConnectorIf->process(CPCAPI2::Phone::kBlockingModeNonBlocking);
   if (mSipAcctMgrIf != NULL)
   {
      mSipAcctMgrIf->process(CPCAPI2::Phone::kBlockingModeNonBlocking);
   }
}

void Cpcapi2Runner::handleSessionDisconnected()
{
   if (mConvStateMgrIf != NULL)
   {
      cpc::vector<SipConversationState> convState;
      mConvStateMgrIf->getStateAllConversations(convState);

      for (const SipConversationState& cs : convState)
      {
         mConvMgrIf->end(cs.conversation);
      }
   }
}

void sdkCallbackHook(void* context)
{
   Cpcapi2Runner* cpcRunner = (Cpcapi2Runner*)context;
   cpcRunner->handleSdkCallback();
}

void Cpcapi2Runner::appInit()
{
   mPhone = CPCAPI2::PhoneInternal::create(mSdkThreadPoolThreadIdx);
   LicenseInfo licenseInfo;
   licenseInfo.licenseKey = "lkj";
   licenseInfo.licenseDocumentLocation = "C:\\Temp";
   licenseInfo.licenseAor = "";
   mPhone->setCallbackHook(sdkCallbackHook, this);
   mPhone->initialize(licenseInfo, this, false);
   mPhone->setLoggingEnabled("cpcapi2webclient", false);

   InfoLog(<< "Cpcapi2Runner::thread: cpcapi2runner phone instance initialized.");
   // need to explicitly create the modules we want to expose
   CPCAPI2::JsonApi::JsonApiServer* jsonApiServer = CPCAPI2::JsonApi::JsonApiServer::getInterface(mMasterSdkPhone);
   CPCAPI2::PeerConnection::PeerConnectionJsonApi::getInterface(mPhone);
   CPCAPI2::ConferenceConnector::ConferenceConnectorJsonApi::getInterface(mPhone);
   CPCAPI2::SipAccount::SipAccountJsonApi::getInterface(mPhone);
   CPCAPI2::SipConversation::SipConversationJsonApi::getInterface(mPhone);

   CPCAPI2::Media::MediaStackSettings mediaSettings;
   //mediaSettings.audioLayer = CPCAPI2::Media::AudioLayers_File;
   mMedia = MediaManager::getInterface(mPhone);
   if (!mMedia)
   {
      InfoLog(<< "Cpcapi2Runner::thread: MediaManager interface not created successfully");
   }
   dynamic_cast<MediaManagerInternal*>(mMedia)->setCallbackHook(sdkCallbackHook, this);
   AudioExt* audioExt = AudioExt::getInterface(mMedia);
   if (audioExt)
   {
      InfoLog(<< "Cpcapi2Runner::thread: AudioExt interface not created successfully");
   }
   //audioExt->setAudioDeviceFile("silence16.pcm", "");
   mMedia->initializeMediaStack(mediaSettings);

   // A couple more JSON API servers to init -- have to do these ones *after* the media stack is initialized
   CPCAPI2::Media::AudioJsonApi::getInterface(mPhone);
   CPCAPI2::Media::VideoJsonApi::getInterface(mPhone);
   // setup a mapping between the SDK instance for this user (mPhone) and the single-instance WebSocket server managed by mMasterSdkPhone (i.e. jsonApiServer)
   CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(mPhone)->setJsonApiServer(mJsonApiServer);

   CPCAPI2::VideoStreaming::VideoStreamingManager::getInterface(mPhone)->setVideoStreamingServer(CPCAPI2::VideoStreaming::VideoStreamingManager::getInterface(mMasterSdkPhone));

   CPCAPI2::Media::Audio* audioIf = Audio::getInterface(mMedia);
   audioIf->setHandler(this);
   //audioIf->setEchoCancellationMode(CPCAPI2::Media::AudioDeviceRole_Headset, CPCAPI2::Media::EchoCancellationMode_None);
   //audioIf->setNoiseSuppressionMode(CPCAPI2::Media::AudioDeviceRole_Headset, CPCAPI2::Media::NoiseSuppressionMode_None);
   //AudioExt::getInterface(mMedia)->setMicAGCEnabled(false);
   audioIf->queryCodecList();
   //audioIf->setMicMute(true);
   //audioIf->setSpeakerMute(true);

   Video::getInterface(mMedia)->setHandler(this);
   //Video::getInterface(mMedia)->setCaptureDevice(CPCAPI2::Media::kCustomVideoSourceDeviceId);
   //Video::getInterface(mMedia)->startCapture();
   Video::getInterface(mMedia)->setVideoMixMode(CPCAPI2::Media::VideoMixMode_NoMixing);
   Video::getInterface(mMedia)->queryCodecList();

   mPeerConnIf = CPCAPI2::PeerConnection::PeerConnectionManager::getInterface(mPhone);
   mConfConnectorIf = CPCAPI2::ConferenceConnector::ConferenceConnectorManager::getInterface(mPhone);
   mSipAcctMgrIf = CPCAPI2::SipAccount::SipAccountManager::getInterface(mPhone);
   if (mSipAcctMgrIf != NULL)
   {
      dynamic_cast<CPCAPI2::SipAccount::SipAccountManagerInternal*>(mSipAcctMgrIf)->setCallbackHook(sdkCallbackHook, this);
   }
   mConvMgrIf = CPCAPI2::SipConversation::SipConversationManager::getInterface(mPhone);
   mConvStateMgrIf = CPCAPI2::SipConversation::SipConversationStateManager::getInterface(mConvMgrIf);

   InfoLog(<< "Cpcapi2Runner::thread: cpcapi2 rtp proxy media module initialized");
}

void Cpcapi2Runner::appShutdown()
{
   //InfoLog(<< "Cpcapi2Runner::thread: cpcapi2 rtp proxy is shutdown!");
   Video::getInterface(mMedia)->stopCapture();
   VideoExt::getInterface(mMedia)->stopWebsocketServer();
   mPhone->process(100);
}

void Cpcapi2Runner::initAccountSettings()
{
}

resip::Data Cpcapi2Runner::getContext() const
{
   resip::Data contextStr;
   return contextStr;
}

int Cpcapi2Runner::onError(const cpc::string& sourceModule, const CPCAPI2::PhoneErrorEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onLicensingError(const CPCAPI2::LicensingErrorEvent& args)
{
   return 0;
}

///////////////////////////////////////////////////////////////////////////////
// SipAccountHandler
int Cpcapi2Runner::onAccountStatusChanged(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountStatusChangedEvent& args)
{
   DebugLog(<< "onAccountStatusChanged: " << account);
   return 0;
}

int Cpcapi2Runner::onError(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::ErrorEvent& args)
{
   return 0;
}

///////////////////////////////////////////////////////////////////////////////
// ConferenceBridgeHandler
int Cpcapi2Runner::onConferenceDetails(CPCAPI2::ConferenceBridge::ConferenceHandle conference, const CPCAPI2::ConferenceBridge::ConferenceDetailsResult& args)
{
   InfoLog(<< "Cpcapi2Runner::onConferenceDetails: " << conference << " token " << args.conferenceToken);
   std::cout << "conference token is: " << args.conferenceToken << std::endl;
   return 0;
}

///////////////////////////////////////////////////////////////////////////////
// SipConversationHandler
int Cpcapi2Runner::onNewConversation(SipConversationHandle conversation, const NewConversationEvent& args)
{
   InfoLog(<< "Cpcapi2Runner::onNewConversation: " << conversation << " from " << args.remoteAddress);

   //CallInfo callInfo;
   //callInfo.address = args.remoteAddress;
   //callInfo.displayName = args.remoteDisplayName;
   //mCalls[conversation] = callInfo;

   //mConversation->setMediaEnabled(conversation, SipConversation::MediaType_Audio, true);
   //mConversation->setMediaEnabled(conversation, SipConversation::MediaType_Video, true);

   //mConversation->accept(conversation);

   //InfoLog(<< "Cpcapi2Runner::onNewConversation: Conversation from " << args.remoteAddress << "accepted");

   return 0;
}

int Cpcapi2Runner::onConversationEnded(SipConversationHandle conversation, const ConversationEndedEvent& args)
{
   InfoLog(<< "Cpcapi2Runner::onConversationEnded: Conversation " << conversation << "ended");
   //std::map<CPCAPI2::SipConversation::SipConversationHandle, CallInfo>::iterator callsIt = mCalls.find(conversation);
   //mCalls.erase(callsIt);
   //mConversation->refreshConversationStatistics(conversation, true, true, true);
   return 0;
}

int Cpcapi2Runner::onIncomingTransferRequest(SipConversationHandle conversation, const TransferRequestEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onIncomingRedirectRequest(SipConversationHandle conversation, const RedirectRequestEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onIncomingTargetChangeRequest(SipConversationHandle conversation, const TargetChangeRequestEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onIncomingHangupRequest(SipConversationHandle conversation, const HangupRequestEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onIncomingBroadsoftTalkRequest(SipConversationHandle conversation, const BroadsoftTalkEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onIncomingBroadsoftHoldRequest(SipConversationHandle conversation, const BroadsoftHoldEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onTransferProgress(SipConversationHandle conversation, const TransferProgressEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onConversationStateChangeRequest(SipConversationHandle conversation, const ConversationStateChangeRequestEvent& args)
{
   return 0;
}

void Cpcapi2Runner::playWelcome(CPCAPI2::SipConversation::SipConversationHandle conversation, const std::string& fileName)
{
   //PlaySoundFileStream* playSoundStream = new PlaySoundFileStream();
   //if (playSoundStream->OpenFile(fileName.c_str(), true, false, false) == 0)
   //{
   //   mConversation->playSound(conversation, playSoundStream, false);
   //}
}

int Cpcapi2Runner::onConversationStateChanged(SipConversationHandle conversation, const ConversationStateChangedEvent& args)
{
   //if (args.conversationState == CPCAPI2::SipConversation::ConversationState_Connected)
   //{
   //   //mTTSApi->connect();
   //   std::string welcomeStr;
   //   {
   //      SipConversationState convState;
   //      mConvStateMan->getState(conversation, convState);
   //      std::stringstream ss;
   //      ss << "Welcome ";
   //      ss << convState.remoteDisplayName.c_str();
   //      welcomeStr = ss.str();
   //   }
   //   //mTTSApi->convert(welcomeStr.c_str(), std::bind(&Cpcapi2Runner::playWelcome, this, conversation, std::placeholders::_1));
   //}
   return 0;
}

int Cpcapi2Runner::onConversationMediaChangeRequest(SipConversationHandle conversation, const ConversationMediaChangeRequestEvent& args)
{
   //mConversation->accept(conversation);
   return 0;
}

int Cpcapi2Runner::onConversationMediaChanged(SipConversationHandle conversation, const ConversationMediaChangedEvent& args)
{
   InfoLog(<< "onConversationMediaChanged: " << conversation << ", localHold: " << args.localHold << ", remoteHold: " << args.remoteHold);
   VideoExt* videoExt = VideoExt::getInterface(mMedia);
   for (cpc::vector<CPCAPI2::SipConversation::MediaInfo>::const_iterator itMedInfo = args.remoteMediaInfo.begin(); itMedInfo != args.remoteMediaInfo.end(); ++itMedInfo)
   {
      InfoLog(<< "    remoteMedia: " << itMedInfo->mediaType);
      if (itMedInfo->mediaType == SipConversation::MediaType_Video)
      {
         //videoExt->startWebsocketServerForReceiveStream(itMedInfo->mediaStreamId);
      }
   }

   return 0;
}

int Cpcapi2Runner::onConversationStatisticsUpdated(SipConversationHandle conversation, const ConversationStatisticsUpdatedEvent& args)
{
   //DebugLog(<< "=========== CONVERSATION STATISTICS =============");
   //CPCAPI2::SipConversation::ConversationStatistics conversationStatistics = args.conversationStatistics;
   //if (conversationStatistics.audioChannels.size() > 0)
   //{
   //   DebugLog(<< "---------- LOCAL ----------"
   //      << "cumulativeLost:      " << conversationStatistics.audioChannels[0].streamStatistics.cumulativeLost << std::endl
   //      << "fractionLost:        " << conversationStatistics.audioChannels[0].streamStatistics.fractionLost << std::endl
   //      << "jitterSamples:       " << conversationStatistics.audioChannels[0].streamStatistics.jitterSamples << std::endl
   //      << "rttMs:               " << conversationStatistics.audioChannels[0].streamStatistics.rttMs << std::endl
   //      << "packetsReceived:     " << conversationStatistics.audioChannels[0].streamDataCounters.packetsReceived << std::endl
   //      << "packetsSent:         " << conversationStatistics.audioChannels[0].streamDataCounters.packetsSent << std::endl
   //      << "decoder.plname:      " << conversationStatistics.audioChannels[0].decoder.plname << std::endl
   //      << "encoder.plname:      " << conversationStatistics.audioChannels[0].encoder.plname << std::endl
   //      << "localEndpoint:       " << conversationStatistics.audioChannels[0].endpoint.ipAddress << ":" << conversationStatistics.audioChannels[0].endpoint.port);
   //}
   //if (conversationStatistics.videoChannels.size() > 0)
   //{
   //   DebugLog(<< "---------- LOCAL (video) ----------"
   //      << "cumulativeLost:       " << conversationStatistics.videoChannels[0].streamStatistics.cumulativeLost << std::endl
   //      << "fractionLost:         " << conversationStatistics.videoChannels[0].streamStatistics.fractionLost << std::endl
   //      << "jitterSamples:        " << conversationStatistics.videoChannels[0].streamStatistics.jitterSamples << std::endl
   //      << "rttMs:                " << conversationStatistics.videoChannels[0].streamStatistics.rttMs << std::endl
   //      << "packetsReceived:      " << conversationStatistics.videoChannels[0].streamDataCounters.packetsReceived << std::endl
   //      << "packetsSent:          " << conversationStatistics.videoChannels[0].streamDataCounters.packetsSent << std::endl
   //      << "decoder.plname:       " << conversationStatistics.videoChannels[0].decoder.plName << std::endl
   //      << "encoder.plname:       " << conversationStatistics.videoChannels[0].encoder.plName << std::endl
   //      << "currentTargetBitrate: " << conversationStatistics.videoChannels[0].currentTargetBitrate << std::endl
   //      << "localEndpoint:        " << conversationStatistics.videoChannels[0].endpoint.ipAddress << ":" << conversationStatistics.videoChannels[0].endpoint.port);
   //}
   //if (conversationStatistics.remoteAudioChannels.size() > 0)
   //{
   //   DebugLog(<< "---------- REMOTE ----------"
   //      << "cumulativeLost:      " << conversationStatistics.remoteAudioChannels[0].streamStatistics.cumulativeLost << std::endl
   //      << "fractionLost:        " << conversationStatistics.remoteAudioChannels[0].streamStatistics.fractionLost << std::endl
   //      << "jitterSamples:       " << conversationStatistics.remoteAudioChannels[0].streamStatistics.jitterSamples << std::endl
   //      << "rttMs:               " << conversationStatistics.remoteAudioChannels[0].streamStatistics.rttMs << std::endl
   //      << "remoteEndpoint:      " << conversationStatistics.remoteAudioChannels[0].endpoint.ipAddress << ":" << conversationStatistics.remoteAudioChannels[0].endpoint.port);
   //}
   //if (conversationStatistics.remoteVideoChannels.size() > 0)
   //{
   //   DebugLog(<< "---------- REMOTE (video) ----------" << std::endl
   //      << "cumulativeLost:      " << conversationStatistics.remoteVideoChannels[0].streamStatistics.cumulativeLost << std::endl
   //      << "fractionLost:        " << conversationStatistics.remoteVideoChannels[0].streamStatistics.fractionLost << std::endl
   //      << "jitterSamples:       " << conversationStatistics.remoteVideoChannels[0].streamStatistics.jitterSamples << std::endl
   //      << "rttMs:               " << conversationStatistics.remoteVideoChannels[0].streamStatistics.rttMs << std::endl
   //      << "remoteEndpoint:      " << conversationStatistics.remoteVideoChannels[0].endpoint.ipAddress << ":" << conversationStatistics.remoteVideoChannels[0].endpoint.port);
   //}
   //DebugLog(<< "======> Call Quality: " << conversationStatistics.callQuality);

   return 0;
}

int Cpcapi2Runner::onError(SipConversationHandle conversation, const CPCAPI2::SipConversation::ErrorEvent& args)
{
   return 0;
}

///////////////////////////////////////////////////////////////////////////////
// VideoHandler
int Cpcapi2Runner::onVideoDeviceListUpdated(const CPCAPI2::Media::VideoDeviceListUpdatedEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onVideoCodecListUpdated(const CPCAPI2::Media::VideoCodecListUpdatedEvent& args)
{
   cpc::vector<CPCAPI2::Media::VideoCodecInfo>::const_iterator it = args.codecInfo.begin();
   for (; it != args.codecInfo.end(); ++it)
   {
      if (strcmp(it->codecName, "H.264") == 0)
      {
         Video::getInterface(mMedia)->setCodecEnabled(it->id, true);
         CPCAPI2::Media::H264Config h264config;
         h264config.enableNonInterleavedMode = false;
         Video::getInterface(mMedia)->setCodecConfig(h264config);
         Video::getInterface(mMedia)->setPreferredResolution(it->id, CPCAPI2::Media::VideoCaptureResolution_HD_1280x720p);
      }
      else if (strcmp(it->codecName, "VP8") == 0)
      {
         Video::getInterface(mMedia)->setCodecEnabled(it->id, true);
         Video::getInterface(mMedia)->setPreferredResolution(it->id, CPCAPI2::Media::VideoCaptureResolution_HD_1280x720p);
      }
      else
      {
         Video::getInterface(mMedia)->setCodecEnabled(it->id, false);
      }
   }
   return 0;
}

////////////////////////////////////////////////////////////////////////////////
// AudioHandler
int Cpcapi2Runner::onAudioDeviceListUpdated(const CPCAPI2::Media::AudioDeviceListUpdatedEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onPlaySoundComplete(CPCAPI2::Media::PlaySoundHandle soundClip)
{
   return 0;
}

int Cpcapi2Runner::onPlaySoundFailure(CPCAPI2::Media::PlaySoundHandle soundClip)
{
   return 0;
}

int Cpcapi2Runner::onAudioCodecListUpdated(const CPCAPI2::Media::AudioCodecListUpdatedEvent& args)
{
   cpc::vector<CPCAPI2::Media::AudioCodecInfo>::const_iterator it = args.codecInfo.begin();
   for (; it != args.codecInfo.end(); ++it)
   {
      if (strcmp(it->codecName, "OPUS") == 0)
      {
         Audio::getInterface(mMedia)->setCodecEnabled(it->id, true);
      }
      else
      {
         Audio::getInterface(mMedia)->setCodecEnabled(it->id, false);
      }
   }
   return 0;
}

int Cpcapi2Runner::onAudioDeviceVolume(const CPCAPI2::Media::AudioDeviceVolumeEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onAudioStreamStarted(const CPCAPI2::Media::AudioStreamStartedEvent& args)
{
   if (mAudioStreamCnt == 0 && mAudioStreamStartStopCb)
   {
      mAudioStreamStartStopCb(true);
   }
   mAudioStreamCnt++;
   return 0;
}

int Cpcapi2Runner::onAudioStreamStopped(const CPCAPI2::Media::AudioStreamStoppedEvent& args)
{
   mAudioStreamCnt--;
   if (mAudioStreamCnt == 0 && mAudioStreamStartStopCb)
   {
      mAudioStreamStartStopCb(false);
   }
   return 0;
}


}
}
