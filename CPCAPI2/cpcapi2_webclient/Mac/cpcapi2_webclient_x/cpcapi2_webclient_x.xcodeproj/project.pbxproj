// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		75B08FA41F94693B004E6C35 /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = 75B08FA31F94693B004E6C35 /* AppDelegate.mm */; };
		75B08FA71F94693B004E6C35 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 75B08FA61F94693B004E6C35 /* main.m */; };
		75B08FAA1F94693B004E6C35 /* ViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 75B08FA91F94693B004E6C35 /* ViewController.m */; };
		75B08FAC1F94693B004E6C35 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 75B08FAB1F94693B004E6C35 /* Assets.xcassets */; };
		75B08FAF1F94693B004E6C35 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 75B08FAD1F94693B004E6C35 /* Main.storyboard */; };
		75B08FB71F946A44004E6C35 /* logoSmall1616.png in Resources */ = {isa = PBXBuildFile; fileRef = 75B08FB61F946A44004E6C35 /* logoSmall1616.png */; };
		75B08FD31F946CD2004E6C35 /* Cpcapi2Runner.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 75B08FCD1F946CD2004E6C35 /* Cpcapi2Runner.cpp */; };
		75B08FD41F946CD2004E6C35 /* SdkManager.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 75B08FCF1F946CD2004E6C35 /* SdkManager.cpp */; };
		75B08FD51F946CD2004E6C35 /* stdafx.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 75B08FD11F946CD2004E6C35 /* stdafx.cpp */; };
		75B090011F947299004E6C35 /* libsnappy.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090001F947299004E6C35 /* libsnappy.a */; };
		75B090431F9472BD004E6C35 /* libaudio_coding_module.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090021F9472BD004E6C35 /* libaudio_coding_module.a */; };
		75B090441F9472BD004E6C35 /* libaudio_conference_mixer.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090031F9472BD004E6C35 /* libaudio_conference_mixer.a */; };
		75B090451F9472BD004E6C35 /* libaudio_decoder_interface.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090041F9472BD004E6C35 /* libaudio_decoder_interface.a */; };
		75B090461F9472BD004E6C35 /* libaudio_device.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090051F9472BD004E6C35 /* libaudio_device.a */; };
		75B090471F9472BD004E6C35 /* libaudio_encoder_interface.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090061F9472BD004E6C35 /* libaudio_encoder_interface.a */; };
		75B090481F9472BD004E6C35 /* libaudio_processing_sse2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090071F9472BD004E6C35 /* libaudio_processing_sse2.a */; };
		75B090491F9472BD004E6C35 /* libaudio_processing.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090081F9472BD004E6C35 /* libaudio_processing.a */; };
		75B0904A1F9472BD004E6C35 /* libbitrate_controller.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090091F9472BD004E6C35 /* libbitrate_controller.a */; };
		75B0904B1F9472BD004E6C35 /* libCNG.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B0900A1F9472BD004E6C35 /* libCNG.a */; };
		75B0904C1F9472BD004E6C35 /* libcommon_audio_sse2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B0900B1F9472BD004E6C35 /* libcommon_audio_sse2.a */; };
		75B0904D1F9472BD004E6C35 /* libcommon_audio.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B0900C1F9472BD004E6C35 /* libcommon_audio.a */; };
		75B0904E1F9472BD004E6C35 /* libcommon_video.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B0900D1F9472BD004E6C35 /* libcommon_video.a */; };
		75B0904F1F9472BD004E6C35 /* libdesktop_capture_differ_sse2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B0900E1F9472BD004E6C35 /* libdesktop_capture_differ_sse2.a */; };
		75B090501F9472BD004E6C35 /* libdesktop_capture.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B0900F1F9472BD004E6C35 /* libdesktop_capture.a */; };
		75B090511F9472BD004E6C35 /* libfield_trial_default.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090101F9472BD004E6C35 /* libfield_trial_default.a */; };
		75B090521F9472BD004E6C35 /* libG711.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090111F9472BD004E6C35 /* libG711.a */; };
		75B090531F9472BD004E6C35 /* libG722.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090121F9472BD004E6C35 /* libG722.a */; };
		75B090541F9472BD004E6C35 /* libgenperf_libs.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090131F9472BD004E6C35 /* libgenperf_libs.a */; };
		75B090551F9472BD004E6C35 /* libiSAC.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090141F9472BD004E6C35 /* libiSAC.a */; };
		75B090561F9472BD004E6C35 /* libiSACFix.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090151F9472BD004E6C35 /* libiSACFix.a */; };
		75B090571F9472BD004E6C35 /* libjpeg_turbo.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090161F9472BD004E6C35 /* libjpeg_turbo.a */; };
		75B090581F9472BD004E6C35 /* libmedia_file.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090171F9472BD004E6C35 /* libmedia_file.a */; };
		75B090591F9472BD004E6C35 /* libmetrics_default.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090181F9472BD004E6C35 /* libmetrics_default.a */; };
		75B0905A1F9472BD004E6C35 /* libneteq.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090191F9472BD004E6C35 /* libneteq.a */; };
		75B0905B1F9472BD004E6C35 /* libopenmax_dl.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B0901A1F9472BD004E6C35 /* libopenmax_dl.a */; };
		75B0905C1F9472BD004E6C35 /* libopus.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B0901B1F9472BD004E6C35 /* libopus.a */; };
		75B0905D1F9472BD004E6C35 /* libpaced_sender.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B0901C1F9472BD004E6C35 /* libpaced_sender.a */; };
		75B0905E1F9472BD004E6C35 /* libPCM16B.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B0901D1F9472BD004E6C35 /* libPCM16B.a */; };
		75B0905F1F9472BD004E6C35 /* libred.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B0901E1F9472BD004E6C35 /* libred.a */; };
		75B090601F9472BD004E6C35 /* libremote_bitrate_estimator.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B0901F1F9472BD004E6C35 /* libremote_bitrate_estimator.a */; };
		75B090611F9472BD004E6C35 /* librtc_base_approved.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090201F9472BD004E6C35 /* librtc_base_approved.a */; };
		75B090631F9472BD004E6C35 /* librtp_rtcp.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090221F9472BD004E6C35 /* librtp_rtcp.a */; };
		75B090641F9472BD004E6C35 /* libsystem_wrappers.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090231F9472BD004E6C35 /* libsystem_wrappers.a */; };
		75B090651F9472BD004E6C35 /* libvideo_capture_module_internal_impl.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090241F9472BD004E6C35 /* libvideo_capture_module_internal_impl.a */; };
		75B090661F9472BD004E6C35 /* libvideo_capture_module.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090251F9472BD004E6C35 /* libvideo_capture_module.a */; };
		75B090671F9472BD004E6C35 /* libvideo_capture.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090261F9472BD004E6C35 /* libvideo_capture.a */; };
		75B090681F9472BD004E6C35 /* libvideo_coding_utility.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090271F9472BD004E6C35 /* libvideo_coding_utility.a */; };
		75B090691F9472BD004E6C35 /* libvideo_engine_core.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090281F9472BD004E6C35 /* libvideo_engine_core.a */; };
		75B0906A1F9472BD004E6C35 /* libvideo_processing_sse2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090291F9472BD004E6C35 /* libvideo_processing_sse2.a */; };
		75B0906B1F9472BD004E6C35 /* libvideo_processing.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B0902A1F9472BD004E6C35 /* libvideo_processing.a */; };
		75B0906C1F9472BD004E6C35 /* libvideo_render_module_internal_impl.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B0902B1F9472BD004E6C35 /* libvideo_render_module_internal_impl.a */; };
		75B0906D1F9472BD004E6C35 /* libvideo_render_module.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B0902C1F9472BD004E6C35 /* libvideo_render_module.a */; };
		75B0906E1F9472BD004E6C35 /* libvideo_render.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B0902D1F9472BD004E6C35 /* libvideo_render.a */; };
		75B0906F1F9472BD004E6C35 /* libvoice_engine.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B0902E1F9472BD004E6C35 /* libvoice_engine.a */; };
		75B090701F9472BD004E6C35 /* libvpx_intrinsics_avx.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B0902F1F9472BD004E6C35 /* libvpx_intrinsics_avx.a */; };
		75B090711F9472BD004E6C35 /* libvpx_intrinsics_avx2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090301F9472BD004E6C35 /* libvpx_intrinsics_avx2.a */; };
		75B090721F9472BD004E6C35 /* libvpx_intrinsics_mmx.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090311F9472BD004E6C35 /* libvpx_intrinsics_mmx.a */; };
		75B090731F9472BD004E6C35 /* libvpx_intrinsics_sse2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090321F9472BD004E6C35 /* libvpx_intrinsics_sse2.a */; };
		75B090741F9472BD004E6C35 /* libvpx_intrinsics_sse4_1.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090331F9472BD004E6C35 /* libvpx_intrinsics_sse4_1.a */; };
		75B090751F9472BD004E6C35 /* libvpx_intrinsics_ssse3.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090341F9472BD004E6C35 /* libvpx_intrinsics_ssse3.a */; };
		75B090761F9472BD004E6C35 /* libvpx.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090351F9472BD004E6C35 /* libvpx.a */; };
		75B090771F9472BD004E6C35 /* libwebrtc_AMRWB.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090361F9472BD004E6C35 /* libwebrtc_AMRWB.a */; };
		75B090781F9472BD004E6C35 /* libwebrtc_common.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090371F9472BD004E6C35 /* libwebrtc_common.a */; };
		75B090791F9472BD004E6C35 /* libwebrtc_g729.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090381F9472BD004E6C35 /* libwebrtc_g729.a */; };
		75B0907A1F9472BD004E6C35 /* libwebrtc_i420.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090391F9472BD004E6C35 /* libwebrtc_i420.a */; };
		75B0907B1F9472BD004E6C35 /* libwebrtc_opus.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B0903A1F9472BD004E6C35 /* libwebrtc_opus.a */; };
		75B0907C1F9472BD004E6C35 /* libwebrtc_SILK.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B0903B1F9472BD004E6C35 /* libwebrtc_SILK.a */; };
		75B0907D1F9472BD004E6C35 /* libwebrtc_speex.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B0903C1F9472BD004E6C35 /* libwebrtc_speex.a */; };
		75B0907E1F9472BD004E6C35 /* libwebrtc_utility.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B0903D1F9472BD004E6C35 /* libwebrtc_utility.a */; };
		75B0907F1F9472BD004E6C35 /* libwebrtc_video_coding.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B0903E1F9472BD004E6C35 /* libwebrtc_video_coding.a */; };
		75B090801F9472BD004E6C35 /* libwebrtc_vp8.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B0903F1F9472BD004E6C35 /* libwebrtc_vp8.a */; };
		75B090811F9472BD004E6C35 /* libwebrtc_vp9.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090401F9472BD004E6C35 /* libwebrtc_vp9.a */; };
		75B090821F9472BD004E6C35 /* libwebrtc.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090411F9472BD004E6C35 /* libwebrtc.a */; };
		75B090831F9472BD004E6C35 /* libyuv.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090421F9472BD004E6C35 /* libyuv.a */; };
		75B0908A1F947323004E6C35 /* libresiprocate.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090881F947323004E6C35 /* libresiprocate.a */; };
		75B0908B1F947323004E6C35 /* libSRTP.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090891F947323004E6C35 /* libSRTP.a */; };
		75B0908C1F9476E5004E6C35 /* libCPCAPI2_Static.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B08FF51F947152004E6C35 /* libCPCAPI2_Static.a */; };
		75B0908E1F9476E5004E6C35 /* libwebrtc_recon.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B0908D1F9476E5004E6C35 /* libwebrtc_recon.a */; };
		75B090911F94771E004E6C35 /* AppKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B0908F1F94771E004E6C35 /* AppKit.framework */; };
		75B090921F94771E004E6C35 /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090901F94771E004E6C35 /* AudioToolbox.framework */; };
		75B090961F947746004E6C35 /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090931F947746004E6C35 /* CoreAudio.framework */; };
		75B090971F947746004E6C35 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090941F947746004E6C35 /* Foundation.framework */; };
		75B090981F947746004E6C35 /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090951F947746004E6C35 /* IOKit.framework */; };
		75B0909C1F94776E004E6C35 /* OpenGL.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090991F94776E004E6C35 /* OpenGL.framework */; };
		75B0909D1F94776E004E6C35 /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B0909A1F94776E004E6C35 /* QuartzCore.framework */; };
		75B0909E1F94776E004E6C35 /* Security.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B0909B1F94776E004E6C35 /* Security.framework */; };
		75B090A21F94778B004E6C35 /* AVFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B0909F1F94778B004E6C35 /* AVFoundation.framework */; };
		75B090A31F94778B004E6C35 /* AVKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090A01F94778B004E6C35 /* AVKit.framework */; };
		75B090A41F94778B004E6C35 /* SystemConfiguration.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090A11F94778B004E6C35 /* SystemConfiguration.framework */; };
		75B090A61F9477AB004E6C35 /* CoreMedia.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090A51F9477AB004E6C35 /* CoreMedia.framework */; };
		75B090AE1F94791E004E6C35 /* libresolv.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090AD1F94791E004E6C35 /* libresolv.tbd */; };
		96E380F622B7F65500F6B32D /* librtc_base.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B090211F9472BD004E6C35 /* librtc_base.a */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		75B08FF41F947152004E6C35 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 75B08FE21F947151004E6C35 /* CPCAPI2_OSX.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 75CF0AB5166982A700558408;
			remoteInfo = CPCAPI2_Static;
		};
		75B08FF61F947152004E6C35 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 75B08FE21F947151004E6C35 /* CPCAPI2_OSX.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 1785FAAC1958611500D217F3;
			remoteInfo = CPCAPI2_UnitTest;
		};
		75B08FF81F947152004E6C35 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 75B08FE21F947151004E6C35 /* CPCAPI2_OSX.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 756EEC7B199BF90D0028D689;
			remoteInfo = CPCAPI2_Shared;
		};
		75B08FFA1F947152004E6C35 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 75B08FE21F947151004E6C35 /* CPCAPI2_OSX.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 6E3A6F011A24FFD600836E4A;
			remoteInfo = CPCAPI2;
		};
		75B08FFD1F94718F004E6C35 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 75B08FE21F947151004E6C35 /* CPCAPI2_OSX.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 75CF0AB4166982A700558408;
			remoteInfo = CPCAPI2_Static;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		0AF31D542332C7C0000188EC /* IOSurface.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = IOSurface.framework; path = System/Library/Frameworks/IOSurface.framework; sourceTree = SDKROOT; };
		75B08F9F1F94693B004E6C35 /* cpcapi2_webclient_x.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = cpcapi2_webclient_x.app; sourceTree = BUILT_PRODUCTS_DIR; };
		75B08FA21F94693B004E6C35 /* AppDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		75B08FA31F94693B004E6C35 /* AppDelegate.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = AppDelegate.mm; sourceTree = "<group>"; };
		75B08FA61F94693B004E6C35 /* main.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		75B08FA81F94693B004E6C35 /* ViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ViewController.h; sourceTree = "<group>"; };
		75B08FA91F94693B004E6C35 /* ViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ViewController.m; sourceTree = "<group>"; };
		75B08FAB1F94693B004E6C35 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		75B08FAE1F94693B004E6C35 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		75B08FB01F94693B004E6C35 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		75B08FB61F946A44004E6C35 /* logoSmall1616.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = logoSmall1616.png; sourceTree = "<group>"; };
		75B08FC51F946B97004E6C35 /* cpcapi2_webclient.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = cpcapi2_webclient.cpp; sourceTree = "<group>"; };
		75B08FCD1F946CD2004E6C35 /* Cpcapi2Runner.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = Cpcapi2Runner.cpp; path = ../../Cpcapi2Runner.cpp; sourceTree = "<group>"; };
		75B08FCE1F946CD2004E6C35 /* Cpcapi2Runner.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = Cpcapi2Runner.h; path = ../../Cpcapi2Runner.h; sourceTree = "<group>"; };
		75B08FCF1F946CD2004E6C35 /* SdkManager.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = SdkManager.cpp; path = ../../SdkManager.cpp; sourceTree = "<group>"; };
		75B08FD01F946CD2004E6C35 /* SdkManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = SdkManager.h; path = ../../SdkManager.h; sourceTree = "<group>"; };
		75B08FD11F946CD2004E6C35 /* stdafx.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = stdafx.cpp; path = ../../stdafx.cpp; sourceTree = "<group>"; };
		75B08FD21F946CD2004E6C35 /* stdafx.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = stdafx.h; path = ../../stdafx.h; sourceTree = "<group>"; };
		75B08FE21F947151004E6C35 /* CPCAPI2_OSX.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = CPCAPI2_OSX.xcodeproj; path = ../../../projects/xcode/CPCAPI2/CPCAPI2_OSX.xcodeproj; sourceTree = "<group>"; };
		75B090001F947299004E6C35 /* libsnappy.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libsnappy.a; path = ../../../../osx_libs/x86_64/snappy/release/libsnappy.a; sourceTree = "<group>"; };
		75B090021F9472BD004E6C35 /* libaudio_coding_module.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libaudio_coding_module.a; path = ../../../../osx_libs/webrtc/Release/libaudio_coding_module.a; sourceTree = "<group>"; };
		75B090031F9472BD004E6C35 /* libaudio_conference_mixer.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libaudio_conference_mixer.a; path = ../../../../osx_libs/webrtc/Release/libaudio_conference_mixer.a; sourceTree = "<group>"; };
		75B090041F9472BD004E6C35 /* libaudio_decoder_interface.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libaudio_decoder_interface.a; path = ../../../../osx_libs/webrtc/Release/libaudio_decoder_interface.a; sourceTree = "<group>"; };
		75B090051F9472BD004E6C35 /* libaudio_device.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libaudio_device.a; path = ../../../../osx_libs/webrtc/Release/libaudio_device.a; sourceTree = "<group>"; };
		75B090061F9472BD004E6C35 /* libaudio_encoder_interface.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libaudio_encoder_interface.a; path = ../../../../osx_libs/webrtc/Release/libaudio_encoder_interface.a; sourceTree = "<group>"; };
		75B090071F9472BD004E6C35 /* libaudio_processing_sse2.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libaudio_processing_sse2.a; path = ../../../../osx_libs/webrtc/Release/libaudio_processing_sse2.a; sourceTree = "<group>"; };
		75B090081F9472BD004E6C35 /* libaudio_processing.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libaudio_processing.a; path = ../../../../osx_libs/webrtc/Release/libaudio_processing.a; sourceTree = "<group>"; };
		75B090091F9472BD004E6C35 /* libbitrate_controller.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libbitrate_controller.a; path = ../../../../osx_libs/webrtc/Release/libbitrate_controller.a; sourceTree = "<group>"; };
		75B0900A1F9472BD004E6C35 /* libCNG.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libCNG.a; path = ../../../../osx_libs/webrtc/Release/libCNG.a; sourceTree = "<group>"; };
		75B0900B1F9472BD004E6C35 /* libcommon_audio_sse2.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libcommon_audio_sse2.a; path = ../../../../osx_libs/webrtc/Release/libcommon_audio_sse2.a; sourceTree = "<group>"; };
		75B0900C1F9472BD004E6C35 /* libcommon_audio.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libcommon_audio.a; path = ../../../../osx_libs/webrtc/Release/libcommon_audio.a; sourceTree = "<group>"; };
		75B0900D1F9472BD004E6C35 /* libcommon_video.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libcommon_video.a; path = ../../../../osx_libs/webrtc/Release/libcommon_video.a; sourceTree = "<group>"; };
		75B0900E1F9472BD004E6C35 /* libdesktop_capture_differ_sse2.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libdesktop_capture_differ_sse2.a; path = ../../../../osx_libs/webrtc/Release/libdesktop_capture_differ_sse2.a; sourceTree = "<group>"; };
		75B0900F1F9472BD004E6C35 /* libdesktop_capture.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libdesktop_capture.a; path = ../../../../osx_libs/webrtc/Release/libdesktop_capture.a; sourceTree = "<group>"; };
		75B090101F9472BD004E6C35 /* libfield_trial_default.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libfield_trial_default.a; path = ../../../../osx_libs/webrtc/Release/libfield_trial_default.a; sourceTree = "<group>"; };
		75B090111F9472BD004E6C35 /* libG711.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libG711.a; path = ../../../../osx_libs/webrtc/Release/libG711.a; sourceTree = "<group>"; };
		75B090121F9472BD004E6C35 /* libG722.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libG722.a; path = ../../../../osx_libs/webrtc/Release/libG722.a; sourceTree = "<group>"; };
		75B090131F9472BD004E6C35 /* libgenperf_libs.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libgenperf_libs.a; path = ../../../../osx_libs/webrtc/Release/libgenperf_libs.a; sourceTree = "<group>"; };
		75B090141F9472BD004E6C35 /* libiSAC.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libiSAC.a; path = ../../../../osx_libs/webrtc/Release/libiSAC.a; sourceTree = "<group>"; };
		75B090151F9472BD004E6C35 /* libiSACFix.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libiSACFix.a; path = ../../../../osx_libs/webrtc/Release/libiSACFix.a; sourceTree = "<group>"; };
		75B090161F9472BD004E6C35 /* libjpeg_turbo.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libjpeg_turbo.a; path = ../../../../osx_libs/webrtc/Release/libjpeg_turbo.a; sourceTree = "<group>"; };
		75B090171F9472BD004E6C35 /* libmedia_file.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libmedia_file.a; path = ../../../../osx_libs/webrtc/Release/libmedia_file.a; sourceTree = "<group>"; };
		75B090181F9472BD004E6C35 /* libmetrics_default.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libmetrics_default.a; path = ../../../../osx_libs/webrtc/Release/libmetrics_default.a; sourceTree = "<group>"; };
		75B090191F9472BD004E6C35 /* libneteq.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libneteq.a; path = ../../../../osx_libs/webrtc/Release/libneteq.a; sourceTree = "<group>"; };
		75B0901A1F9472BD004E6C35 /* libopenmax_dl.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libopenmax_dl.a; path = ../../../../osx_libs/webrtc/Release/libopenmax_dl.a; sourceTree = "<group>"; };
		75B0901B1F9472BD004E6C35 /* libopus.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libopus.a; path = ../../../../osx_libs/webrtc/Release/libopus.a; sourceTree = "<group>"; };
		75B0901C1F9472BD004E6C35 /* libpaced_sender.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libpaced_sender.a; path = ../../../../osx_libs/webrtc/Release/libpaced_sender.a; sourceTree = "<group>"; };
		75B0901D1F9472BD004E6C35 /* libPCM16B.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libPCM16B.a; path = ../../../../osx_libs/webrtc/Release/libPCM16B.a; sourceTree = "<group>"; };
		75B0901E1F9472BD004E6C35 /* libred.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libred.a; path = ../../../../osx_libs/webrtc/Release/libred.a; sourceTree = "<group>"; };
		75B0901F1F9472BD004E6C35 /* libremote_bitrate_estimator.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libremote_bitrate_estimator.a; path = ../../../../osx_libs/webrtc/Release/libremote_bitrate_estimator.a; sourceTree = "<group>"; };
		75B090201F9472BD004E6C35 /* librtc_base_approved.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = librtc_base_approved.a; path = ../../../../osx_libs/webrtc/Release/librtc_base_approved.a; sourceTree = "<group>"; };
		75B090211F9472BD004E6C35 /* librtc_base.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = librtc_base.a; path = ../../../../osx_libs/webrtc/Release/librtc_base.a; sourceTree = "<group>"; };
		75B090221F9472BD004E6C35 /* librtp_rtcp.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = librtp_rtcp.a; path = ../../../../osx_libs/webrtc/Release/librtp_rtcp.a; sourceTree = "<group>"; };
		75B090231F9472BD004E6C35 /* libsystem_wrappers.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libsystem_wrappers.a; path = ../../../../osx_libs/webrtc/Release/libsystem_wrappers.a; sourceTree = "<group>"; };
		75B090241F9472BD004E6C35 /* libvideo_capture_module_internal_impl.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libvideo_capture_module_internal_impl.a; path = ../../../../osx_libs/webrtc/Release/libvideo_capture_module_internal_impl.a; sourceTree = "<group>"; };
		75B090251F9472BD004E6C35 /* libvideo_capture_module.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libvideo_capture_module.a; path = ../../../../osx_libs/webrtc/Release/libvideo_capture_module.a; sourceTree = "<group>"; };
		75B090261F9472BD004E6C35 /* libvideo_capture.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libvideo_capture.a; path = ../../../../osx_libs/webrtc/Release/libvideo_capture.a; sourceTree = "<group>"; };
		75B090271F9472BD004E6C35 /* libvideo_coding_utility.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libvideo_coding_utility.a; path = ../../../../osx_libs/webrtc/Release/libvideo_coding_utility.a; sourceTree = "<group>"; };
		75B090281F9472BD004E6C35 /* libvideo_engine_core.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libvideo_engine_core.a; path = ../../../../osx_libs/webrtc/Release/libvideo_engine_core.a; sourceTree = "<group>"; };
		75B090291F9472BD004E6C35 /* libvideo_processing_sse2.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libvideo_processing_sse2.a; path = ../../../../osx_libs/webrtc/Release/libvideo_processing_sse2.a; sourceTree = "<group>"; };
		75B0902A1F9472BD004E6C35 /* libvideo_processing.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libvideo_processing.a; path = ../../../../osx_libs/webrtc/Release/libvideo_processing.a; sourceTree = "<group>"; };
		75B0902B1F9472BD004E6C35 /* libvideo_render_module_internal_impl.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libvideo_render_module_internal_impl.a; path = ../../../../osx_libs/webrtc/Release/libvideo_render_module_internal_impl.a; sourceTree = "<group>"; };
		75B0902C1F9472BD004E6C35 /* libvideo_render_module.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libvideo_render_module.a; path = ../../../../osx_libs/webrtc/Release/libvideo_render_module.a; sourceTree = "<group>"; };
		75B0902D1F9472BD004E6C35 /* libvideo_render.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libvideo_render.a; path = ../../../../osx_libs/webrtc/Release/libvideo_render.a; sourceTree = "<group>"; };
		75B0902E1F9472BD004E6C35 /* libvoice_engine.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libvoice_engine.a; path = ../../../../osx_libs/webrtc/Release/libvoice_engine.a; sourceTree = "<group>"; };
		75B0902F1F9472BD004E6C35 /* libvpx_intrinsics_avx.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libvpx_intrinsics_avx.a; path = ../../../../osx_libs/webrtc/Release/libvpx_intrinsics_avx.a; sourceTree = "<group>"; };
		75B090301F9472BD004E6C35 /* libvpx_intrinsics_avx2.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libvpx_intrinsics_avx2.a; path = ../../../../osx_libs/webrtc/Release/libvpx_intrinsics_avx2.a; sourceTree = "<group>"; };
		75B090311F9472BD004E6C35 /* libvpx_intrinsics_mmx.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libvpx_intrinsics_mmx.a; path = ../../../../osx_libs/webrtc/Release/libvpx_intrinsics_mmx.a; sourceTree = "<group>"; };
		75B090321F9472BD004E6C35 /* libvpx_intrinsics_sse2.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libvpx_intrinsics_sse2.a; path = ../../../../osx_libs/webrtc/Release/libvpx_intrinsics_sse2.a; sourceTree = "<group>"; };
		75B090331F9472BD004E6C35 /* libvpx_intrinsics_sse4_1.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libvpx_intrinsics_sse4_1.a; path = ../../../../osx_libs/webrtc/Release/libvpx_intrinsics_sse4_1.a; sourceTree = "<group>"; };
		75B090341F9472BD004E6C35 /* libvpx_intrinsics_ssse3.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libvpx_intrinsics_ssse3.a; path = ../../../../osx_libs/webrtc/Release/libvpx_intrinsics_ssse3.a; sourceTree = "<group>"; };
		75B090351F9472BD004E6C35 /* libvpx.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libvpx.a; path = ../../../../osx_libs/webrtc/Release/libvpx.a; sourceTree = "<group>"; };
		75B090361F9472BD004E6C35 /* libwebrtc_AMRWB.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libwebrtc_AMRWB.a; path = ../../../../osx_libs/webrtc/Release/libwebrtc_AMRWB.a; sourceTree = "<group>"; };
		75B090371F9472BD004E6C35 /* libwebrtc_common.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libwebrtc_common.a; path = ../../../../osx_libs/webrtc/Release/libwebrtc_common.a; sourceTree = "<group>"; };
		75B090381F9472BD004E6C35 /* libwebrtc_g729.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libwebrtc_g729.a; path = ../../../../osx_libs/webrtc/Release/libwebrtc_g729.a; sourceTree = "<group>"; };
		75B090391F9472BD004E6C35 /* libwebrtc_i420.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libwebrtc_i420.a; path = ../../../../osx_libs/webrtc/Release/libwebrtc_i420.a; sourceTree = "<group>"; };
		75B0903A1F9472BD004E6C35 /* libwebrtc_opus.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libwebrtc_opus.a; path = ../../../../osx_libs/webrtc/Release/libwebrtc_opus.a; sourceTree = "<group>"; };
		75B0903B1F9472BD004E6C35 /* libwebrtc_SILK.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libwebrtc_SILK.a; path = ../../../../osx_libs/webrtc/Release/libwebrtc_SILK.a; sourceTree = "<group>"; };
		75B0903C1F9472BD004E6C35 /* libwebrtc_speex.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libwebrtc_speex.a; path = ../../../../osx_libs/webrtc/Release/libwebrtc_speex.a; sourceTree = "<group>"; };
		75B0903D1F9472BD004E6C35 /* libwebrtc_utility.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libwebrtc_utility.a; path = ../../../../osx_libs/webrtc/Release/libwebrtc_utility.a; sourceTree = "<group>"; };
		75B0903E1F9472BD004E6C35 /* libwebrtc_video_coding.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libwebrtc_video_coding.a; path = ../../../../osx_libs/webrtc/Release/libwebrtc_video_coding.a; sourceTree = "<group>"; };
		75B0903F1F9472BD004E6C35 /* libwebrtc_vp8.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libwebrtc_vp8.a; path = ../../../../osx_libs/webrtc/Release/libwebrtc_vp8.a; sourceTree = "<group>"; };
		75B090401F9472BD004E6C35 /* libwebrtc_vp9.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libwebrtc_vp9.a; path = ../../../../osx_libs/webrtc/Release/libwebrtc_vp9.a; sourceTree = "<group>"; };
		75B090411F9472BD004E6C35 /* libwebrtc.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libwebrtc.a; path = ../../../../osx_libs/webrtc/Release/libwebrtc.a; sourceTree = "<group>"; };
		75B090421F9472BD004E6C35 /* libyuv.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libyuv.a; path = ../../../../osx_libs/webrtc/Release/libyuv.a; sourceTree = "<group>"; };
		75B090881F947323004E6C35 /* libresiprocate.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libresiprocate.a; path = ../../../shared/sipfoundry/Projects/resiprocate/Mac/build/Debug/libresiprocate.a; sourceTree = "<group>"; };
		75B090891F947323004E6C35 /* libSRTP.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libSRTP.a; path = ../../../shared/libsrtp/srtp/mac/build/Debug/libSRTP.a; sourceTree = "<group>"; };
		75B0908D1F9476E5004E6C35 /* libwebrtc_recon.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libwebrtc_recon.a; path = DerivedData/cpcapi2_webclient_x/Build/Products/Debug/libwebrtc_recon.a; sourceTree = "<group>"; };
		75B0908F1F94771E004E6C35 /* AppKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AppKit.framework; path = System/Library/Frameworks/AppKit.framework; sourceTree = SDKROOT; };
		75B090901F94771E004E6C35 /* AudioToolbox.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AudioToolbox.framework; path = System/Library/Frameworks/AudioToolbox.framework; sourceTree = SDKROOT; };
		75B090931F947746004E6C35 /* CoreAudio.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreAudio.framework; path = System/Library/Frameworks/CoreAudio.framework; sourceTree = SDKROOT; };
		75B090941F947746004E6C35 /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = System/Library/Frameworks/Foundation.framework; sourceTree = SDKROOT; };
		75B090951F947746004E6C35 /* IOKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = IOKit.framework; path = System/Library/Frameworks/IOKit.framework; sourceTree = SDKROOT; };
		75B090991F94776E004E6C35 /* OpenGL.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = OpenGL.framework; path = System/Library/Frameworks/OpenGL.framework; sourceTree = SDKROOT; };
		75B0909A1F94776E004E6C35 /* QuartzCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = QuartzCore.framework; path = System/Library/Frameworks/QuartzCore.framework; sourceTree = SDKROOT; };
		75B0909B1F94776E004E6C35 /* Security.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Security.framework; path = System/Library/Frameworks/Security.framework; sourceTree = SDKROOT; };
		75B0909F1F94778B004E6C35 /* AVFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVFoundation.framework; path = System/Library/Frameworks/AVFoundation.framework; sourceTree = SDKROOT; };
		75B090A01F94778B004E6C35 /* AVKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVKit.framework; path = System/Library/Frameworks/AVKit.framework; sourceTree = SDKROOT; };
		75B090A11F94778B004E6C35 /* SystemConfiguration.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SystemConfiguration.framework; path = System/Library/Frameworks/SystemConfiguration.framework; sourceTree = SDKROOT; };
		75B090A51F9477AB004E6C35 /* CoreMedia.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMedia.framework; path = System/Library/Frameworks/CoreMedia.framework; sourceTree = SDKROOT; };
		75B090AD1F94791E004E6C35 /* libresolv.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libresolv.tbd; path = usr/lib/libresolv.tbd; sourceTree = SDKROOT; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		75B08F9C1F94693B004E6C35 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				96E380F622B7F65500F6B32D /* librtc_base.a in Frameworks */,
				75B090AE1F94791E004E6C35 /* libresolv.tbd in Frameworks */,
				75B090A61F9477AB004E6C35 /* CoreMedia.framework in Frameworks */,
				75B090A21F94778B004E6C35 /* AVFoundation.framework in Frameworks */,
				75B090A31F94778B004E6C35 /* AVKit.framework in Frameworks */,
				75B090A41F94778B004E6C35 /* SystemConfiguration.framework in Frameworks */,
				75B0909C1F94776E004E6C35 /* OpenGL.framework in Frameworks */,
				75B0909D1F94776E004E6C35 /* QuartzCore.framework in Frameworks */,
				75B0909E1F94776E004E6C35 /* Security.framework in Frameworks */,
				75B090961F947746004E6C35 /* CoreAudio.framework in Frameworks */,
				75B090971F947746004E6C35 /* Foundation.framework in Frameworks */,
				75B090981F947746004E6C35 /* IOKit.framework in Frameworks */,
				75B090911F94771E004E6C35 /* AppKit.framework in Frameworks */,
				75B090921F94771E004E6C35 /* AudioToolbox.framework in Frameworks */,
				75B0908E1F9476E5004E6C35 /* libwebrtc_recon.a in Frameworks */,
				75B0908C1F9476E5004E6C35 /* libCPCAPI2_Static.a in Frameworks */,
				75B0908A1F947323004E6C35 /* libresiprocate.a in Frameworks */,
				75B0908B1F947323004E6C35 /* libSRTP.a in Frameworks */,
				75B090431F9472BD004E6C35 /* libaudio_coding_module.a in Frameworks */,
				75B090441F9472BD004E6C35 /* libaudio_conference_mixer.a in Frameworks */,
				75B090451F9472BD004E6C35 /* libaudio_decoder_interface.a in Frameworks */,
				75B090461F9472BD004E6C35 /* libaudio_device.a in Frameworks */,
				75B090471F9472BD004E6C35 /* libaudio_encoder_interface.a in Frameworks */,
				75B090481F9472BD004E6C35 /* libaudio_processing_sse2.a in Frameworks */,
				75B090491F9472BD004E6C35 /* libaudio_processing.a in Frameworks */,
				75B0904A1F9472BD004E6C35 /* libbitrate_controller.a in Frameworks */,
				75B0904B1F9472BD004E6C35 /* libCNG.a in Frameworks */,
				75B0904C1F9472BD004E6C35 /* libcommon_audio_sse2.a in Frameworks */,
				75B0904D1F9472BD004E6C35 /* libcommon_audio.a in Frameworks */,
				75B0904E1F9472BD004E6C35 /* libcommon_video.a in Frameworks */,
				75B0904F1F9472BD004E6C35 /* libdesktop_capture_differ_sse2.a in Frameworks */,
				75B090501F9472BD004E6C35 /* libdesktop_capture.a in Frameworks */,
				75B090511F9472BD004E6C35 /* libfield_trial_default.a in Frameworks */,
				75B090521F9472BD004E6C35 /* libG711.a in Frameworks */,
				75B090531F9472BD004E6C35 /* libG722.a in Frameworks */,
				75B090541F9472BD004E6C35 /* libgenperf_libs.a in Frameworks */,
				75B090551F9472BD004E6C35 /* libiSAC.a in Frameworks */,
				75B090561F9472BD004E6C35 /* libiSACFix.a in Frameworks */,
				75B090571F9472BD004E6C35 /* libjpeg_turbo.a in Frameworks */,
				75B090581F9472BD004E6C35 /* libmedia_file.a in Frameworks */,
				75B090591F9472BD004E6C35 /* libmetrics_default.a in Frameworks */,
				75B0905A1F9472BD004E6C35 /* libneteq.a in Frameworks */,
				75B0905B1F9472BD004E6C35 /* libopenmax_dl.a in Frameworks */,
				75B0905C1F9472BD004E6C35 /* libopus.a in Frameworks */,
				75B0905D1F9472BD004E6C35 /* libpaced_sender.a in Frameworks */,
				75B0905E1F9472BD004E6C35 /* libPCM16B.a in Frameworks */,
				75B0905F1F9472BD004E6C35 /* libred.a in Frameworks */,
				75B090601F9472BD004E6C35 /* libremote_bitrate_estimator.a in Frameworks */,
				75B090611F9472BD004E6C35 /* librtc_base_approved.a in Frameworks */,
				75B090631F9472BD004E6C35 /* librtp_rtcp.a in Frameworks */,
				75B090641F9472BD004E6C35 /* libsystem_wrappers.a in Frameworks */,
				75B090651F9472BD004E6C35 /* libvideo_capture_module_internal_impl.a in Frameworks */,
				75B090661F9472BD004E6C35 /* libvideo_capture_module.a in Frameworks */,
				75B090671F9472BD004E6C35 /* libvideo_capture.a in Frameworks */,
				75B090681F9472BD004E6C35 /* libvideo_coding_utility.a in Frameworks */,
				75B090691F9472BD004E6C35 /* libvideo_engine_core.a in Frameworks */,
				75B0906A1F9472BD004E6C35 /* libvideo_processing_sse2.a in Frameworks */,
				75B0906B1F9472BD004E6C35 /* libvideo_processing.a in Frameworks */,
				75B0906C1F9472BD004E6C35 /* libvideo_render_module_internal_impl.a in Frameworks */,
				75B0906D1F9472BD004E6C35 /* libvideo_render_module.a in Frameworks */,
				75B0906E1F9472BD004E6C35 /* libvideo_render.a in Frameworks */,
				75B0906F1F9472BD004E6C35 /* libvoice_engine.a in Frameworks */,
				75B090701F9472BD004E6C35 /* libvpx_intrinsics_avx.a in Frameworks */,
				75B090711F9472BD004E6C35 /* libvpx_intrinsics_avx2.a in Frameworks */,
				75B090721F9472BD004E6C35 /* libvpx_intrinsics_mmx.a in Frameworks */,
				75B090731F9472BD004E6C35 /* libvpx_intrinsics_sse2.a in Frameworks */,
				75B090741F9472BD004E6C35 /* libvpx_intrinsics_sse4_1.a in Frameworks */,
				75B090751F9472BD004E6C35 /* libvpx_intrinsics_ssse3.a in Frameworks */,
				75B090761F9472BD004E6C35 /* libvpx.a in Frameworks */,
				75B090771F9472BD004E6C35 /* libwebrtc_AMRWB.a in Frameworks */,
				75B090781F9472BD004E6C35 /* libwebrtc_common.a in Frameworks */,
				75B090791F9472BD004E6C35 /* libwebrtc_g729.a in Frameworks */,
				75B0907A1F9472BD004E6C35 /* libwebrtc_i420.a in Frameworks */,
				75B0907B1F9472BD004E6C35 /* libwebrtc_opus.a in Frameworks */,
				75B0907C1F9472BD004E6C35 /* libwebrtc_SILK.a in Frameworks */,
				75B0907D1F9472BD004E6C35 /* libwebrtc_speex.a in Frameworks */,
				75B0907E1F9472BD004E6C35 /* libwebrtc_utility.a in Frameworks */,
				75B0907F1F9472BD004E6C35 /* libwebrtc_video_coding.a in Frameworks */,
				75B090801F9472BD004E6C35 /* libwebrtc_vp8.a in Frameworks */,
				75B090811F9472BD004E6C35 /* libwebrtc_vp9.a in Frameworks */,
				75B090821F9472BD004E6C35 /* libwebrtc.a in Frameworks */,
				75B090831F9472BD004E6C35 /* libyuv.a in Frameworks */,
				75B090011F947299004E6C35 /* libsnappy.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		75B08F961F94693B004E6C35 = {
			isa = PBXGroup;
			children = (
				75B08FE21F947151004E6C35 /* CPCAPI2_OSX.xcodeproj */,
				75B08FC41F946B61004E6C35 /* cpp */,
				75B08FA11F94693B004E6C35 /* cpcapi2_webclient_x */,
				75B08FA01F94693B004E6C35 /* Products */,
				75B08FFF1F947298004E6C35 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		75B08FA01F94693B004E6C35 /* Products */ = {
			isa = PBXGroup;
			children = (
				75B08F9F1F94693B004E6C35 /* cpcapi2_webclient_x.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		75B08FA11F94693B004E6C35 /* cpcapi2_webclient_x */ = {
			isa = PBXGroup;
			children = (
				75B08FB61F946A44004E6C35 /* logoSmall1616.png */,
				75B08FA21F94693B004E6C35 /* AppDelegate.h */,
				75B08FA31F94693B004E6C35 /* AppDelegate.mm */,
				75B08FA81F94693B004E6C35 /* ViewController.h */,
				75B08FA91F94693B004E6C35 /* ViewController.m */,
				75B08FAB1F94693B004E6C35 /* Assets.xcassets */,
				75B08FAD1F94693B004E6C35 /* Main.storyboard */,
				75B08FB01F94693B004E6C35 /* Info.plist */,
				75B08FA51F94693B004E6C35 /* Supporting Files */,
			);
			path = cpcapi2_webclient_x;
			sourceTree = "<group>";
		};
		75B08FA51F94693B004E6C35 /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				75B08FA61F94693B004E6C35 /* main.m */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		75B08FC41F946B61004E6C35 /* cpp */ = {
			isa = PBXGroup;
			children = (
				75B08FCD1F946CD2004E6C35 /* Cpcapi2Runner.cpp */,
				75B08FCE1F946CD2004E6C35 /* Cpcapi2Runner.h */,
				75B08FCF1F946CD2004E6C35 /* SdkManager.cpp */,
				75B08FD01F946CD2004E6C35 /* SdkManager.h */,
				75B08FD11F946CD2004E6C35 /* stdafx.cpp */,
				75B08FD21F946CD2004E6C35 /* stdafx.h */,
				75B08FC51F946B97004E6C35 /* cpcapi2_webclient.cpp */,
			);
			name = cpp;
			sourceTree = "<group>";
		};
		75B08FE31F947151004E6C35 /* Products */ = {
			isa = PBXGroup;
			children = (
				75B08FF51F947152004E6C35 /* libCPCAPI2_Static.a */,
				75B08FF71F947152004E6C35 /* libCPCAPI2_UnitTest.a */,
				75B08FF91F947152004E6C35 /* libCPCAPI2_Shared.dylib */,
				75B08FFB1F947152004E6C35 /* CPCAPI2.framework */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		75B08FFF1F947298004E6C35 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				75B090AD1F94791E004E6C35 /* libresolv.tbd */,
				0AF31D542332C7C0000188EC /* IOSurface.framework */,
				75B090A51F9477AB004E6C35 /* CoreMedia.framework */,
				75B0909F1F94778B004E6C35 /* AVFoundation.framework */,
				75B090A01F94778B004E6C35 /* AVKit.framework */,
				75B090A11F94778B004E6C35 /* SystemConfiguration.framework */,
				75B090991F94776E004E6C35 /* OpenGL.framework */,
				75B0909A1F94776E004E6C35 /* QuartzCore.framework */,
				75B0909B1F94776E004E6C35 /* Security.framework */,
				75B090931F947746004E6C35 /* CoreAudio.framework */,
				75B090941F947746004E6C35 /* Foundation.framework */,
				75B090951F947746004E6C35 /* IOKit.framework */,
				75B0908F1F94771E004E6C35 /* AppKit.framework */,
				75B090901F94771E004E6C35 /* AudioToolbox.framework */,
				75B0908D1F9476E5004E6C35 /* libwebrtc_recon.a */,
				75B090881F947323004E6C35 /* libresiprocate.a */,
				75B090891F947323004E6C35 /* libSRTP.a */,
				75B090021F9472BD004E6C35 /* libaudio_coding_module.a */,
				75B090031F9472BD004E6C35 /* libaudio_conference_mixer.a */,
				75B090041F9472BD004E6C35 /* libaudio_decoder_interface.a */,
				75B090051F9472BD004E6C35 /* libaudio_device.a */,
				75B090061F9472BD004E6C35 /* libaudio_encoder_interface.a */,
				75B090071F9472BD004E6C35 /* libaudio_processing_sse2.a */,
				75B090081F9472BD004E6C35 /* libaudio_processing.a */,
				75B090091F9472BD004E6C35 /* libbitrate_controller.a */,
				75B0900A1F9472BD004E6C35 /* libCNG.a */,
				75B0900B1F9472BD004E6C35 /* libcommon_audio_sse2.a */,
				75B0900C1F9472BD004E6C35 /* libcommon_audio.a */,
				75B0900D1F9472BD004E6C35 /* libcommon_video.a */,
				75B0900E1F9472BD004E6C35 /* libdesktop_capture_differ_sse2.a */,
				75B0900F1F9472BD004E6C35 /* libdesktop_capture.a */,
				75B090101F9472BD004E6C35 /* libfield_trial_default.a */,
				75B090111F9472BD004E6C35 /* libG711.a */,
				75B090121F9472BD004E6C35 /* libG722.a */,
				75B090131F9472BD004E6C35 /* libgenperf_libs.a */,
				75B090141F9472BD004E6C35 /* libiSAC.a */,
				75B090151F9472BD004E6C35 /* libiSACFix.a */,
				75B090161F9472BD004E6C35 /* libjpeg_turbo.a */,
				75B090171F9472BD004E6C35 /* libmedia_file.a */,
				75B090181F9472BD004E6C35 /* libmetrics_default.a */,
				75B090191F9472BD004E6C35 /* libneteq.a */,
				75B0901A1F9472BD004E6C35 /* libopenmax_dl.a */,
				75B0901B1F9472BD004E6C35 /* libopus.a */,
				75B0901C1F9472BD004E6C35 /* libpaced_sender.a */,
				75B0901D1F9472BD004E6C35 /* libPCM16B.a */,
				75B0901E1F9472BD004E6C35 /* libred.a */,
				75B0901F1F9472BD004E6C35 /* libremote_bitrate_estimator.a */,
				75B090201F9472BD004E6C35 /* librtc_base_approved.a */,
				75B090211F9472BD004E6C35 /* librtc_base.a */,
				75B090221F9472BD004E6C35 /* librtp_rtcp.a */,
				75B090231F9472BD004E6C35 /* libsystem_wrappers.a */,
				75B090241F9472BD004E6C35 /* libvideo_capture_module_internal_impl.a */,
				75B090251F9472BD004E6C35 /* libvideo_capture_module.a */,
				75B090261F9472BD004E6C35 /* libvideo_capture.a */,
				75B090271F9472BD004E6C35 /* libvideo_coding_utility.a */,
				75B090281F9472BD004E6C35 /* libvideo_engine_core.a */,
				75B090291F9472BD004E6C35 /* libvideo_processing_sse2.a */,
				75B0902A1F9472BD004E6C35 /* libvideo_processing.a */,
				75B0902B1F9472BD004E6C35 /* libvideo_render_module_internal_impl.a */,
				75B0902C1F9472BD004E6C35 /* libvideo_render_module.a */,
				75B0902D1F9472BD004E6C35 /* libvideo_render.a */,
				75B0902E1F9472BD004E6C35 /* libvoice_engine.a */,
				75B0902F1F9472BD004E6C35 /* libvpx_intrinsics_avx.a */,
				75B090301F9472BD004E6C35 /* libvpx_intrinsics_avx2.a */,
				75B090311F9472BD004E6C35 /* libvpx_intrinsics_mmx.a */,
				75B090321F9472BD004E6C35 /* libvpx_intrinsics_sse2.a */,
				75B090331F9472BD004E6C35 /* libvpx_intrinsics_sse4_1.a */,
				75B090341F9472BD004E6C35 /* libvpx_intrinsics_ssse3.a */,
				75B090351F9472BD004E6C35 /* libvpx.a */,
				75B090361F9472BD004E6C35 /* libwebrtc_AMRWB.a */,
				75B090371F9472BD004E6C35 /* libwebrtc_common.a */,
				75B090381F9472BD004E6C35 /* libwebrtc_g729.a */,
				75B090391F9472BD004E6C35 /* libwebrtc_i420.a */,
				75B0903A1F9472BD004E6C35 /* libwebrtc_opus.a */,
				75B0903B1F9472BD004E6C35 /* libwebrtc_SILK.a */,
				75B0903C1F9472BD004E6C35 /* libwebrtc_speex.a */,
				75B0903D1F9472BD004E6C35 /* libwebrtc_utility.a */,
				75B0903E1F9472BD004E6C35 /* libwebrtc_video_coding.a */,
				75B0903F1F9472BD004E6C35 /* libwebrtc_vp8.a */,
				75B090401F9472BD004E6C35 /* libwebrtc_vp9.a */,
				75B090411F9472BD004E6C35 /* libwebrtc.a */,
				75B090421F9472BD004E6C35 /* libyuv.a */,
				75B090001F947299004E6C35 /* libsnappy.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		75B08F9E1F94693B004E6C35 /* cpcapi2_webclient_x */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 75B08FB31F94693B004E6C35 /* Build configuration list for PBXNativeTarget "cpcapi2_webclient_x" */;
			buildPhases = (
				75B08F9B1F94693B004E6C35 /* Sources */,
				75B08F9C1F94693B004E6C35 /* Frameworks */,
				75B08F9D1F94693B004E6C35 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				75B08FFE1F94718F004E6C35 /* PBXTargetDependency */,
			);
			name = cpcapi2_webclient_x;
			productName = cpcapi2_webclient_x;
			productReference = 75B08F9F1F94693B004E6C35 /* cpcapi2_webclient_x.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		75B08F971F94693B004E6C35 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 0830;
				ORGANIZATIONNAME = CounterPath;
				TargetAttributes = {
					75B08F9E1F94693B004E6C35 = {
						CreatedOnToolsVersion = 8.3.3;
						DevelopmentTeam = UQC9N9AMZM;
						ProvisioningStyle = Automatic;
					};
				};
			};
			buildConfigurationList = 75B08F9A1F94693B004E6C35 /* Build configuration list for PBXProject "cpcapi2_webclient_x" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				English,
				en,
				Base,
			);
			mainGroup = 75B08F961F94693B004E6C35;
			productRefGroup = 75B08FA01F94693B004E6C35 /* Products */;
			projectDirPath = "";
			projectReferences = (
				{
					ProductGroup = 75B08FE31F947151004E6C35 /* Products */;
					ProjectRef = 75B08FE21F947151004E6C35 /* CPCAPI2_OSX.xcodeproj */;
				},
			);
			projectRoot = "";
			targets = (
				75B08F9E1F94693B004E6C35 /* cpcapi2_webclient_x */,
			);
		};
/* End PBXProject section */

/* Begin PBXReferenceProxy section */
		75B08FF51F947152004E6C35 /* libCPCAPI2_Static.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libCPCAPI2_Static.a;
			remoteRef = 75B08FF41F947152004E6C35 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		75B08FF71F947152004E6C35 /* libCPCAPI2_UnitTest.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libCPCAPI2_UnitTest.a;
			remoteRef = 75B08FF61F947152004E6C35 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		75B08FF91F947152004E6C35 /* libCPCAPI2_Shared.dylib */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libCPCAPI2_Shared.dylib;
			remoteRef = 75B08FF81F947152004E6C35 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		75B08FFB1F947152004E6C35 /* CPCAPI2.framework */ = {
			isa = PBXReferenceProxy;
			fileType = wrapper.framework;
			path = CPCAPI2.framework;
			remoteRef = 75B08FFA1F947152004E6C35 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
/* End PBXReferenceProxy section */

/* Begin PBXResourcesBuildPhase section */
		75B08F9D1F94693B004E6C35 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				75B08FB71F946A44004E6C35 /* logoSmall1616.png in Resources */,
				75B08FAC1F94693B004E6C35 /* Assets.xcassets in Resources */,
				75B08FAF1F94693B004E6C35 /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		75B08F9B1F94693B004E6C35 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				75B08FAA1F94693B004E6C35 /* ViewController.m in Sources */,
				75B08FA71F94693B004E6C35 /* main.m in Sources */,
				75B08FD41F946CD2004E6C35 /* SdkManager.cpp in Sources */,
				75B08FD51F946CD2004E6C35 /* stdafx.cpp in Sources */,
				75B08FD31F946CD2004E6C35 /* Cpcapi2Runner.cpp in Sources */,
				75B08FA41F94693B004E6C35 /* AppDelegate.mm in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		75B08FFE1F94718F004E6C35 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = CPCAPI2_Static;
			targetProxy = 75B08FFD1F94718F004E6C35 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		75B08FAD1F94693B004E6C35 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				75B08FAE1F94693B004E6C35 /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		75B08FB11F94693B004E6C35 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "-";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					RESIP_USE_STL_STREAMS,
					RESIP_APPLE_USE_SYSTEM_TYPES,
					CPCAPI2_USE_BUILT_IN_STRINGCONV,
					CPCAPI2_USE_STD_THREAD,
					CPCAPI2_USE_LICENSING,
					CPCAPI2_INCLUDE_UNRELEASED_HEADERS,
					RESIP_USE_GETIFADDRS,
					"DEBUG=1",
					BOOST_ALL_NO_LIB,
					BOOST_ASIO_DISABLE_VISIBILITY,
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					../..,
					../../../shared/sipfoundry/main,
					../../../osx_libs/x86_64/curlpp/debug/include,
					../../../osx_libs/x86_64/curlpp/debug/include/curlpp/internal,
					../../../CPCAPI2/interface/public,
					../../../CPCAPI2/interface/experimental,
					../../../external/websocketpp,
					../../../external/boost/include,
					../../../CPCAPI2,
					../../../../osx_libs/x86_64/openssl/debug/include,
					../../../external/rapidjson,
					../../../CPCAPI2/impl/jsonapi,
					../../../CPCAPI2/impl/call/jsonapi,
					../../../CPCAPI2/impl/account/jsonapi,
					../../../CPCAPI2/impl,
					../../../shared/sipfoundry/main/resip/recon,
					../../../shared/sipfoundry/main/reflow,
					../../../shared/libsrtp/srtp/include,
					../../../shared/libsrtp/srtp/crypto/include,
					../../../shared/webrtc_recon,
					../../../shared/WebRTCv/trunk/webrtc/video_engine/include,
					../../../shared/WebRTCv/trunk,
					../../../shared/WebRTCv/trunk/webrtc/voice_engine/include,
					../../../shared/sipfoundry/main/reTurn,
				);
				LIBRARY_SEARCH_PATHS = (
					../../../../osx_libs/x86_64/openssl/debug/lib,
					../../../../osx_libs/webrtc/Debug,
					"../../../external/vqmon/vqmon/lib/macosx-i386_x86_64/debug",
					../../../../osx_libs/x86_64/snappy/debug/lib,
					../../../../osx_libs/x86_64/curlpp/debug/lib,
					../../../../osx_libs/x86_64/curl/debug/lib,
				);
				MACOSX_DEPLOYMENT_TARGET = 11.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
			};
			name = Debug;
		};
		75B08FB21F94693B004E6C35 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "-";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					RESIP_USE_STL_STREAMS,
					RESIP_APPLE_USE_SYSTEM_TYPES,
					CPCAPI2_USE_BUILT_IN_STRINGCONV,
					CPCAPI2_USE_STD_THREAD, 
					CPCAPI2_USE_LICENSING,
					CPCAPI2_INCLUDE_UNRELEASED_HEADERS,
					RESIP_USE_GETIFADDRS,
					BOOST_ALL_NO_LIB,
					BOOST_ASIO_DISABLE_VISIBILITY,
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					../..,
					../../../shared/sipfoundry/main,
					../../../osx_libs/x86_64/curlpp/release/include,
					../../../osx_libs/x86_64/curlpp/release/include/curlpp/internal,
					../../../CPCAPI2/interface/public,
					../../../CPCAPI2/interface/experimental,
					../../../external/websocketpp,
					../../../external/boost/include,
					../../../CPCAPI2,
					../../../../osx_libs/x86_64/openssl/release/include,
					../../../external/rapidjson,
					../../../CPCAPI2/impl/jsonapi,
					../../../CPCAPI2/impl/call/jsonapi,
					../../../CPCAPI2/impl/account/jsonapi,
					../../../CPCAPI2/impl,
					../../../shared/sipfoundry/main/resip/recon,
					../../../shared/sipfoundry/main/reflow,
					../../../shared/libsrtp/srtp/include,
					../../../shared/libsrtp/srtp/crypto/include,
					../../../shared/webrtc_recon,
					../../../shared/WebRTCv/trunk/webrtc/video_engine/include,
					../../../shared/WebRTCv/trunk,
					../../../shared/WebRTCv/trunk/webrtc/voice_engine/include,
					../../../shared/sipfoundry/main/reTurn,
				);
				LIBRARY_SEARCH_PATHS = (
					../../../../osx_libs/x86_64/openssl/reelase/lib,
					../../../../osx_libs/webrtc/Release,
					"../../../external/vqmon/vqmon/lib/macosx-i386_x86_64/release",
					../../../../osx_libs/x86_64/snappy/release/lib,
					../../../../osx_libs/x86_64/curlpp/release/lib,
					../../../../osx_libs/x86_64/curl/release/lib,
				);
				MACOSX_DEPLOYMENT_TARGET = 11.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = macosx;
			};
			name = Release;
		};
		75B08FB41F94693B004E6C35 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_IDENTITY = "Mac Developer";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				DEVELOPMENT_TEAM = "";
				INFOPLIST_FILE = cpcapi2_webclient_x/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/../Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/DerivedData/cpcapi2_webclient_x/Build/Products/Debug",
				);
				OTHER_LDFLAGS = (
					"-lz",
					"-lcrypto",
					"-lssl",
					"-lnghttp2",
					"-lcurl",
					"-lcurlpp",
					"-lxml2",
					"-lxmlsec1",
					"-lxmlsec1-openssl",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "cp.cpcapi2-webclient-x";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Debug;
		};
		75B08FB51F94693B004E6C35 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_IDENTITY = "Mac Developer";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				DEVELOPMENT_TEAM = "";
				INFOPLIST_FILE = cpcapi2_webclient_x/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/../Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/DerivedData/cpcapi2_webclient_x/Build/Products/Debug",
				);
				OTHER_LDFLAGS = (
					"-lz",
					"-lcrypto",
					"-lssl",
					"-lnghttp2",
					"-lcurl",
					"-lcurlpp",
					"-lxml2",
					"-lxmlsec1",
					"-lxmlsec1-openssl",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "cp.cpcapi2-webclient-x";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		75B08F9A1F94693B004E6C35 /* Build configuration list for PBXProject "cpcapi2_webclient_x" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				75B08FB11F94693B004E6C35 /* Debug */,
				75B08FB21F94693B004E6C35 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		75B08FB31F94693B004E6C35 /* Build configuration list for PBXNativeTarget "cpcapi2_webclient_x" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				75B08FB41F94693B004E6C35 /* Debug */,
				75B08FB51F94693B004E6C35 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 75B08F971F94693B004E6C35 /* Project object */;
}
