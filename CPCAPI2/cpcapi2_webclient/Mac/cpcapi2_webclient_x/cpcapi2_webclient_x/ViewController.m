//
//  ViewController.m
//  cpcapi2_webclient_x
//
//  Created by <PERSON> on 2017-10-15.
//  Copyright © 2017 CounterPath. All rights reserved.
//

#import "ViewController.h"

@implementation ViewController

- (void)viewDidLoad {
   [super viewDidLoad];

   // Do any additional setup after loading the view.
}


- (void)setRepresentedObject:(id)representedObject {
   [super setRepresentedObject:representedObject];

   // Update the view, if already loaded.
}


@end
