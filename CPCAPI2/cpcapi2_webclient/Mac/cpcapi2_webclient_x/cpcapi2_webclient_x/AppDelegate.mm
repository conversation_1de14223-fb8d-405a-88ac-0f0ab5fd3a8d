//
//  AppDelegate.m
//  cpcapi2_webclient_x
//
//  Created by <PERSON> on 2017-10-15.
//  Copyright © 2017 CounterPath. All rights reserved.
//

#import "AppDelegate.h"
#include "SdkManager.h"

static void noOpCb(CPCAPI2::Agent::Cpcapi2Runner*) {}

@interface AppDelegate ()
{
   CPCAPI2::Agent::SdkManager* sdkInstance;
}
@property (strong, nonatomic) NSStatusItem* statusItem;
@property (strong, nonatomic) NSThread* sdkThread;
- (void)createStatusBarItem;
- (NSMenu*)createStatusBarMenu;
- (void)quitApplication;
- (void)initSdk;
- (void)sdkThreadFn;
@end

@implementation AppDelegate

- (void)applicationDidFinishLaunching:(NSNotification *)aNotification {
   // Insert code here to initialize your application
   [self createStatusBarItem];
   [self initSdk];
}


- (void)applicationWillTerminate:(NSNotification *)aNotification {
   // Insert code here to tear down your application
   sdkInstance->shutdown();
}

- (void)createStatusBarItem {
  NSStatusBar *statusBar = [NSStatusBar systemStatusBar];
  _statusItem = [statusBar statusItemWithLength:NSSquareStatusItemLength];
  
  NSImage* icon = [NSImage imageNamed:@"logoSmall1616.png"];
  //icon.template = true;
  
  _statusItem.image = icon;
  _statusItem.highlightMode = YES;
  _statusItem.menu = [self createStatusBarMenu];
}

- (NSMenu*)createStatusBarMenu {
    NSMenu* menu = [[NSMenu alloc] init];
 
    NSMenuItem* quit =
      [[NSMenuItem alloc] initWithTitle:@"Quit"
                                 action:@selector(quitApplication)
                          keyEquivalent:@""];
    [quit setTarget:self];
    [menu addItem:quit];
  
    return menu;
}

- (void)quitApplication {
   [NSApp terminate:self];
}

- (void)initSdk {
   _sdkThread = [[NSThread alloc] initWithTarget:self selector:@selector(sdkThreadFn) object:nil];
   [_sdkThread start];
}

- (void)sdkThreadFn {
   sdkInstance = new CPCAPI2::Agent::SdkManager();
   sdkInstance->run(noOpCb);
}
@end
