﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{82C2D67D-E6C2-4A82-B542-D3F382645E80}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>cpcapi2_webclient</RootNamespace>
    <WindowsTargetPlatformVersion>8.1</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v140</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v140</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v140</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v140</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="..\..\..\projects\vs11\CPCAPI2_dependencies.props" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="..\..\..\projects\vs11\CPCAPI2_dependencies.props" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WIN32_WINNT=0x0600;USE_SSL;USE_DNS_VIP;RESIP_USE_STL_STREAMS;CPCAPI2_INCLUDE_UNRELEASED_HEADERS;CPCAPI2_NO_DEPRECATIONS;_WEBSOCKETPP_CPP11_CHRONO_;_WEBSOCKETPP_CPP11_FUNCTIONAL_;_WEBSOCKETPP_CPP11_MEMORY_;_WEBSOCKETPP_CPP11_RANDOM_DEVICE_;_WEBSOCKETPP_CPP11_REGEX_;_WEBSOCKETPP_CPP11_SYSTEM_ERROR_;_WEBSOCKETPP_CPP11_THREAD_;_WEBSOCKETPP_NOEXCEPT_;BOOST_ALL_NO_LIB;_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS;CURL_STATICLIB;CURL_DISABLE_LDAP;CURLPP_STATICLIB;_CRT_SECURE_NO_WARNINGS;_SCL_SECURE_NO_WARNINGS;ASIO_ENABLE_CANCELIO;BOOST_ASIO_ENABLE_CANCELIO;CPCAPI2_STATIC_LIB;_VARIADIC_MAX=10;USE_IPV6;USE_ARES;DEFAULT_BRIDGE_MAX_IN_OUTPUTS=10;USE_DTLS;TSC_WINDOWS;__WXMSW__;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>true</SDLCheck>
      <AdditionalIncludeDirectories>..\..;..\..\..\CPCAPI2\interface\public;..\..\..\CPCAPI2\interface\experimental;..\..\..\shared\sipfoundry\main;..\..\..\CPCAPI2\impl;..\..\..\CPCAPI2;..\..\..\external\websocketpp;..\..\..\external\boost\include;..\..\..\external\rapidjson;..\..\..\..\windows_libs\x86\curl\debug\include;..\..\..\..\windows_libs\x86\curlpp\debug\include;..\..\..\..\windows_libs\x86\openssl\debug\include;..\..\..\external\wxWidgets-3.1.1\include\msvc;..\..\..\external\wxWidgets-3.1.1\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>..\..\..\build;..\..\..\lib;..\..\..\..\windows_libs\x86\curlpp\debug\lib;..\..\..\..\windows_libs\x86\curl\debug\lib;..\..\..\..\windows_libs\x86\openssl\debug\lib;..\..\..\..\windows_libs\voiceage\AmrwbFull_13.0;..\..\..\..\windows_libs\x86\zlib\debug\lib;..\..\..\..\windows_libs\x86\curlpp\debug\lib;..\..\..\..\windows_libs\webrtc\Debug;..\..\..\..\windows_libs\openh264;..\..\..\..\windows_libs\tsc;..\..\..\external\Microsoft\v110\Samples\multimedia\directshow\baseclasses\Debug;..\..\..\external\speex\win32\libspeex\Debug;..\..\..\external\bv32;..\..\..\external\SILK;..\..\..\external\VSoft\HD_H264\Windows\lib;..\..\..\external\VSoft\HD_H264\Windows\lib-icl32;..\..\..\external\UBVideo\H263\dec\Library\Debug;..\..\..\external\UBVideo\H263\enc\Library\Debug;..\..\..\..\windows_libs\codecpro\g729\x86;..\..\..\external\g726\G726AsDLL\Debug;..\..\..\external\libmsrp\Win32\VS_2012\Debug;;..\..\..\..\windows_libs\x86\libxml2\debug\lib;..\..\..\external\v8\build\Debug\lib;..\..\..\..\windows_libs\x86\xmlsec\debug\lib;..\..\..\..\windows_libs\vqmon\win-i386-VS2012\debug;..\..\..\external\sopi\Debug;..\..\..\..\windows_libs\shaka_packager\Debug;..\..\..\..\windows_libs\breakpad\debug;..\..\..\..\windows_libs\x86\snappy\debug\lib;..\..\..\external\Beamr\H265\lib;..\..\..\external\Beamr\H265\lib-icl32;..\..\..\external\wxWidgets-3.1.1\lib\vc_lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;wbemuuid.lib;uuid.lib;odbc32.lib;odbccp32.lib;Ws2_32.lib;winmm.lib;zlib.lib;curlpp.lib;strmiids.lib;libmsrp.lib;libxml.lib;Traffic.lib;xmlsec.lib;winhttp.lib;psapi.lib;dbghelp.lib;Pdh.lib;exception_handler.lib;snappy.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_DEBUG;_WINDOWS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>true</SDLCheck>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WIN32_WINNT=0x0600;USE_SSL;USE_DNS_VIP;RESIP_USE_STL_STREAMS;CPCAPI2_INCLUDE_UNRELEASED_HEADERS;_WEBSOCKETPP_CPP11_CHRONO_;_WEBSOCKETPP_CPP11_FUNCTIONAL_;_WEBSOCKETPP_CPP11_MEMORY_;_WEBSOCKETPP_CPP11_RANDOM_DEVICE_;_WEBSOCKETPP_CPP11_REGEX_;_WEBSOCKETPP_CPP11_SYSTEM_ERROR_;_WEBSOCKETPP_CPP11_THREAD_;_WEBSOCKETPP_NOEXCEPT_;BOOST_ALL_NO_LIB;_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS;CURL_STATICLIB;CURL_DISABLE_LDAP;CURLPP_STATICLIB;_CRT_SECURE_NO_WARNINGS;_SCL_SECURE_NO_WARNINGS;ASIO_ENABLE_CANCELIO;BOOST_ASIO_ENABLE_CANCELIO;CPCAPI2_STATIC_LIB;_VARIADIC_MAX=10;USE_IPV6;USE_ARES;DEFAULT_BRIDGE_MAX_IN_OUTPUTS=10;USE_DTLS;TSC_WINDOWS;__WXMSW__;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>true</SDLCheck>
      <AdditionalIncludeDirectories>..\..;..\..\..\CPCAPI2\interface\public;..\..\..\CPCAPI2\interface\experimental;..\..\..\shared\sipfoundry\main;..\..\..\CPCAPI2\impl;..\..\..\CPCAPI2;..\..\..\external\websocketpp;..\..\..\external\boost\include;..\..\..\external\rapidjson;..\..\..\..\windows_libs\x86\curl\release\include;..\..\..\..\windows_libs\x86\curlpp\release\include;..\..\..\..\windows_libs\x86\openssl\release\include;..\..\..\external\wxWidgets-3.1.1\include\msvc;..\..\..\external\wxWidgets-3.1.1\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <DisableSpecificWarnings>4996</DisableSpecificWarnings>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>..\..\..\build;..\..\..\lib;..\..\..\..\windows_libs\x86\curlpp\release\lib;..\..\..\..\windows_libs\x86\curl\release\lib;..\..\..\..\windows_libs\x86\openssl\release\lib;..\..\..\..\windows_libs\voiceage\AmrwbFull_13.0;..\..\..\..\windows_libs\x86\zlib\release\lib;..\..\..\..\windows_libs\webrtc\Release;..\..\..\..\windows_libs\openh264;..\..\..\..\windows_libs\tsc;..\..\..\external\Microsoft\v110\Samples\multimedia\directshow\baseclasses\Release;..\..\..\external\speex\win32\libspeex\Release;..\..\..\external\bv32;..\..\..\external\SILK;..\..\..\external\VSoft\HD_H264\Windows\lib;..\..\..\external\VSoft\HD_H264\Windows\lib-icl32;..\..\..\external\UBVideo\H263\dec\Library\Release;..\..\..\external\UBVideo\H263\enc\Library\Release;..\..\..\..\windows_libs\codecpro\g729\x86;..\..\..\external\g726\G726AsDLL\Release\;..\..\..\external\libmsrp\Win32\VS_2012\Release;..\..\..\..\windows_libs\x86\libxml2\release\lib;..\..\..\external\v8\build\Release\lib;..\..\..\..\windows_libs\x86\xmlsec\release\lib;..\..\..\..\windows_libs\vqmon\win-i386-VS2012\release;..\..\..\external\sopi\Release;..\..\..\..\windows_libs\shaka_packager\Release;..\..\..\..\windows_libs\breakpad\release;..\..\..\..\windows_libs\x86\snappy\release\lib;..\..\..\external\Beamr\H265\lib;..\..\..\external\Beamr\H265\lib-icl32;..\..\..\external\wxWidgets-3.1.1\lib\vc_lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;odbccp32.lib;Ws2_32.lib;winmm.lib;zlib.lib;curlpp.lib;strmiids.lib;libxml.lib;Traffic.lib;xmlsec.lib;winhttp.lib;psapi.lib;dbghelp.lib;Pdh.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalOptions>/alternatename:___iob_func=___intel_lib_iob_func %(AdditionalOptions)</AdditionalOptions>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>NDEBUG;_WINDOWS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>true</SDLCheck>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <Text Include="ReadMe.txt" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\Cpcapi2Runner.h" />
    <ClInclude Include="..\..\SdkManager.h" />
    <ClInclude Include="cpcapi2_webclient.h" />
    <ClInclude Include="Resource.h" />
    <ClInclude Include="stdafx.h" />
    <ClInclude Include="targetver.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\BaseException.cxx" />
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\Condition.cxx" />
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\Data.cxx" />
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\DataStream.cxx" />
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\FdPoll.cxx" />
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\Lock.cxx" />
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\Log.cxx" />
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\MD5Stream.cxx" />
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\Mutex.cxx" />
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\ParseBuffer.cxx" />
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\ParseException.cxx" />
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\Random.cxx" />
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\SelectInterruptor.cxx" />
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\Socket.cxx" />
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\ssl\OpenSSLInit.cxx" />
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\Subsystem.cxx" />
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\SysLogBuf.cxx" />
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\SysLogStream.cxx" />
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\ThreadIf.cxx" />
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\Time.cxx" />
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\vmd5.cxx" />
    <ClCompile Include="..\..\..\shared\WebRTCv\CPCAPI2_webrtc_libs_windows.cpp" />
    <ClCompile Include="..\..\Cpcapi2Runner.cpp" />
    <ClCompile Include="..\..\SdkManager.cpp" />
    <ClCompile Include="cpcapi2_webclient.cpp" />
    <ClCompile Include="stdafx.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Create</PrecompiledHeader>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="cpcapi2_webclient.rc" />
  </ItemGroup>
  <ItemGroup>
    <Image Include="cpcapi2_webclient.ico" />
    <Image Include="logoSmall1616.png" />
    <Image Include="microphone.png" />
    <Image Include="muted.png" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\external\lodepng\lodepng\lodepng.vcxproj">
      <Project>{5a3c0440-db87-4023-b1d1-2d9676e8618c}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\projects\vs11\CPCAPI2_win_full.vcxproj">
      <Project>{4f4a352d-d41b-4352-aff4-61e50bdc9b3e}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\shared\libsrtp\srtp\libSRTP.vcxproj">
      <Project>{7e1e1308-f82e-4dd3-b25c-cd12756a1dd9}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\shared\sipfoundry\Projects\ares\Win32\ares_7_1.vcxproj">
      <Project>{ce7cf5e0-cad1-49d6-95d1-143ded7b226e}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\shared\sipfoundry\Projects\dum\Win32\dum.vcxproj">
      <Project>{31b0654f-e08e-405f-909f-80f86cb14b9d}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\shared\sipfoundry\Projects\recon\recon_8_0.vcxproj">
      <Project>{16cd976a-5d3b-4329-88ba-a32560cdfcc7}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\shared\sipfoundry\Projects\reflow\reflow_8_0.vcxproj">
      <Project>{d2ab531b-86ac-43dd-a330-9809b4f1bb53}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\shared\sipfoundry\Projects\resiprocate\Win32\resiprocate.vcxproj">
      <Project>{2633fbf4-2c87-44cf-b167-14aae6136bf2}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\shared\sipfoundry\Projects\reTurn\reTurnClient_9_0.vcxproj">
      <Project>{67b5906c-5c9d-4d09-ac7e-af71d72175f8}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\shared\webrtc_recon\webrtc_recon.vcxproj">
      <Project>{da830cf9-22d6-41bb-a47e-4e0b68071efd}</Project>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
