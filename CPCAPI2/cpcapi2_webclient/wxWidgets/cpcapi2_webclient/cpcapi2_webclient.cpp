// cpcapi2_webclient.cpp : Defines the entry point for the application.
//

#include "stdafx.h"
#include "cpcapi2_webclient.h"
#include "SdkManager.h"
#include "Cpcapi2Runner.h"

#include <cpcapi2.h>

#include "wx/imagpng.h"
#include "wx/graphics.h"
#include "wx/taskbar.h"
#include "wx/display.h"

#include <functional>

using namespace CPCAPI2::Agent;

// Define a new application type, each program should derive a class from wxApp
class MyApp : public wxApp
{
public:
   // override base class virtuals
   // ----------------------------

   // this one is called on application startup and is a good place for the app
   // initialization (doing it here and not in the ctor allows to have an error
   // return: if OnInit() returns false, the application terminates)
   virtual bool OnInit() wxOVERRIDE;

private:
   SdkManager mSdkManager;
};

class MovableButton : public wxButton
{
   bool dragging;
   int x, y;
   wxWindow* parent;

public:

   MovableButton(wxWindow *parent,
      wxWindowID id,
      const wxString& label = wxEmptyString,
      const wxPoint& pos = wxDefaultPosition,
      const wxSize& size = wxDefaultSize,
      long style = 0,
      const wxValidator& validator = wxDefaultValidator,
      const wxString& name = wxButtonNameStr) : wxButton(parent, id, label, pos, size, style, validator, name)
   {
      MovableButton::parent = parent;
      dragging = false;

      //Connect(wxEVT_BUTTON,
      //   wxCommandEventHandler(MovableButton::onClickDynamicHandler));
      Bind(wxEVT_LEFT_DOWN, &MovableButton::onMouseDown, this);
      Bind(wxEVT_LEFT_UP, &MovableButton::onMouseUp, this);
      Bind(wxEVT_MOTION, &MovableButton::onMove, this);
   }

   void onMouseDown(wxMouseEvent& evt)
   {
      CaptureMouse();
      x = evt.GetX();
      y = evt.GetY();
      dragging = true;
      //evt.Veto();
   }
   void onMouseUp(wxMouseEvent& evt)
   {
      ReleaseMouse();
      dragging = false;

      if (x - evt.GetX() == 0 &&
         y - evt.GetY() == 0)
      {
         wxCommandEvent event(wxEVT_BUTTON);
         event.SetEventObject(this);

         // invia evento
         wxPostEvent(this, event);
      }
   }
   void onMove(wxMouseEvent& evt)
   {
      if (dragging)
      {
         wxPoint mouseOnScreen = wxGetMousePosition();
         int newx = mouseOnScreen.x - x;
         int newy = mouseOnScreen.y - y;
         parent->Move(newx, newy);
      }
   }

private:
   wxDECLARE_EVENT_TABLE();
};

wxBEGIN_EVENT_TABLE(MovableButton, wxButton)
//EVT_LEFT_DOWN(MovableButton::onMouseDown)
//EVT_LEFT_UP(MovableButton::onMouseUp)
//EVT_MOTION(MovableButton::onMove)
wxEND_EVENT_TABLE()

// Define a new frame type: this is going to be our main frame
class MyFrame : public wxFrame
{
public:
   // ctor(s)
   MyFrame(const wxString& title);

   // event handlers (these functions should _not_ be virtual)
   void OnQuit(wxCommandEvent& event);
   void OnMute(wxCommandEvent& event);
   void OnThreadEvent(wxThreadEvent &event);
   
   void setSdkRunner(Cpcapi2Runner* sdkRunner)
   {
      mSdkRunner = sdkRunner;
      mSdkRunner->setAudioStreamStartStopCallback(std::bind(&MyFrame::onAudioStreamStartStop, this, std::placeholders::_1));
   }
   Cpcapi2Runner* getSdkRunner() const
   {
      return mSdkRunner;
   }

private:
   void onAudioStreamStartStop(bool started);

private:
   // any class wishing to process wxWidgets events must use this macro
   wxDECLARE_EVENT_TABLE();

   MovableButton* mMuteBtn;
   bool mIsMuted;
   Cpcapi2Runner* mSdkRunner;
};

class MyTaskBarIcon : public wxTaskBarIcon
{
public:
#if defined(__WXOSX__) && wxOSX_USE_COCOA
   MyTaskBarIcon(wxApp* parent, wxTaskBarIconType iconType = wxTBI_DEFAULT_TYPE)
      : wxTaskBarIcon(iconType), mParent(parent)
#else
   MyTaskBarIcon(wxApp* parent) : mParent(parent)
#endif
   {}

   void OnLeftButtonDClick(wxTaskBarIconEvent&) {}
   void OnMenuRestore(wxCommandEvent&) {}
   void OnMenuExit(wxCommandEvent&) {
      mParent->Exit();
   }
   void OnMenuSetNewIcon(wxCommandEvent&) {}
   void OnMenuCheckmark(wxCommandEvent&) {}
   void OnMenuUICheckmark(wxUpdateUIEvent&) {}
   void OnMenuSub(wxCommandEvent&) {}
   virtual wxMenu *CreatePopupMenu() wxOVERRIDE {
      wxMenu *menu = new wxMenu;
      //menu->Append(PU_RESTORE, wxT("&Restore main window"));
      //menu->AppendSeparator();
      //menu->Append(PU_NEW_ICON, wxT("&Set New Icon"));
      //menu->AppendSeparator();
      //menu->AppendCheckItem(PU_CHECKMARK, wxT("Test &check mark"));
      //menu->AppendSeparator();
      //wxMenu *submenu = new wxMenu;
      //submenu->Append(PU_SUB1, wxT("One submenu"));
      //submenu->AppendSeparator();
      //submenu->Append(PU_SUB2, wxT("Another submenu"));
      //menu->Append(PU_SUBMAIN, wxT("Submenu"), submenu);
      /* OSX has built-in quit menu for the dock menu, but not for the status item */
#ifdef __WXOSX__ 
      if (OSXIsStatusItem())
#endif
      {
         //menu->AppendSeparator();
         menu->Append(10001, wxT("E&xit"));
      }
      return menu;
   }

private:
   wxApp* mParent;

   wxDECLARE_EVENT_TABLE();
};

wxBEGIN_EVENT_TABLE(MyTaskBarIcon, wxTaskBarIcon)
//EVT_MENU(PU_RESTORE, MyTaskBarIcon::OnMenuRestore)
EVT_MENU(10001, MyTaskBarIcon::OnMenuExit)
//EVT_MENU(PU_NEW_ICON, MyTaskBarIcon::OnMenuSetNewIcon)
//EVT_MENU(PU_CHECKMARK, MyTaskBarIcon::OnMenuCheckmark)
//EVT_UPDATE_UI(PU_CHECKMARK, MyTaskBarIcon::OnMenuUICheckmark)
EVT_TASKBAR_LEFT_DCLICK(MyTaskBarIcon::OnLeftButtonDClick)
//EVT_MENU(PU_SUB1, MyTaskBarIcon::OnMenuSub)
//EVT_MENU(PU_SUB2, MyTaskBarIcon::OnMenuSub)
wxEND_EVENT_TABLE()


// ----------------------------------------------------------------------------
// constants
// ----------------------------------------------------------------------------

// IDs for the controls and the menu commands
enum
{
   // menu items
   Minimal_Quit = wxID_EXIT,

   // it is important for the id corresponding to the "About" command to have
   // this standard value as otherwise it won't be handled properly under Mac
   // (where it is special and put into the "Apple" menu)
   Minimal_About = wxID_ABOUT
};

// ----------------------------------------------------------------------------
// event tables and other macros for wxWidgets
// ----------------------------------------------------------------------------

// the event tables connect the wxWidgets events with the functions (event
// handlers) which process them. It can be also done at run-time, but for the
// simple menu events like this the static method is much simpler.
wxBEGIN_EVENT_TABLE(MyFrame, wxFrame)
EVT_MENU(Minimal_Quit, MyFrame::OnQuit)
EVT_MENU(Minimal_About, MyFrame::OnMute)
EVT_THREAD(wxID_ANY, MyFrame::OnThreadEvent)
wxEND_EVENT_TABLE()

// Create a new application object: this macro will allow wxWidgets to create
// the application object during program execution (it's better than using a
// static object for many reasons) and also implements the accessor function
// wxGetApp() which will return the reference of the right type (i.e. MyApp and
// not wxApp)
wxIMPLEMENT_APP(MyApp);

// 'Main program' equivalent: the program execution "starts" here
bool MyApp::OnInit()
{
   // call the base class initialization method, currently it only parses a
   // few common command-line options but it could be do more in the future
   if (!wxApp::OnInit())
      return false;

   wxImage::AddHandler(new wxPNGHandler);

   // create the main application window
   MyFrame* frame = new MyFrame("CounterPath Web Client");

   mSdkManager.run(std::bind(&MyFrame::setSdkRunner, frame, std::placeholders::_1));

   // and show it (the frames, unlike simple controls, are not shown when
   // created initially)
   //frame->Show(true);

   {
      wxLogNull suppressPngWarning;
      wxIcon cplogoIcon;
      cplogoIcon.CopyFromBitmap(wxBITMAP_PNG(logoSmall1616));
      MyTaskBarIcon* taskBarIcon = new MyTaskBarIcon(this);
      taskBarIcon->SetIcon(cplogoIcon, "CounterPath Web Client");
   }

   // success: wxApp::OnRun() will be called which will enter the main message
   // loop and the application will run. If we returned false here, the
   // application would exit immediately.
   return true;
}

// ----------------------------------------------------------------------------
// main frame
// ----------------------------------------------------------------------------

// frame constructor
MyFrame::MyFrame(const wxString& title)
   : wxFrame(NULL, wxID_ANY, title, wxPoint(20, 20), wxSize(98, 98),
      0
      | wxFRAME_SHAPED
      | wxNO_BORDER
      | wxFRAME_NO_TASKBAR
      | wxSTAY_ON_TOP)
{
   mIsMuted = false;

   wxGraphicsPath path = wxGraphicsRenderer::GetDefaultRenderer()->CreatePath();
   path.AddRoundedRectangle(0, 0, 98, 98, 10);
   SetShape(path);

   SetBackgroundColour(wxColour(232, 232, 232));

   // set the frame icon
   //SetIcon(wxICON(sample));

   SetTransparent(200);

#if 0 // wxUSE_MENUS
   // create a menu bar
   wxMenu *fileMenu = new wxMenu;

   // the "About" item should be in the help menu
   wxMenu *helpMenu = new wxMenu;
   helpMenu->Append(Minimal_About, "&About\tF1", "Show about dialog");

   fileMenu->Append(Minimal_Quit, "E&xit\tAlt-X", "Quit this program");

   // now append the freshly created menu to the menu bar...
   wxMenuBar *menuBar = new wxMenuBar();
   menuBar->Append(fileMenu, "&File");
   menuBar->Append(helpMenu, "&Help");

   // ... and attach this menu bar to the frame
   SetMenuBar(menuBar);
#else // !wxUSE_MENUS
   // If menus are not available add a button to access the about box
   //wxSizer* sizer = new wxBoxSizer(wxHORIZONTAL);
   mMuteBtn = new MovableButton(this, wxID_ANY, wxEmptyString, wxPoint(5, 5), wxSize(88, 88), wxTRANSPARENT_WINDOW | wxBORDER_NONE);
   //muteBtn->SetTransparent(0);
   //muteBtn->SetBackgroundStyle(wxBG_STYLE_TRANSPARENT);
   mMuteBtn->SetBitmap(wxBITMAP_PNG(microphone));
   mMuteBtn->SetBackgroundColour(GetBackgroundColour());
   mMuteBtn->Bind(wxEVT_BUTTON, &MyFrame::OnMute, this);
   //sizer->Add(mMuteBtn, wxSizerFlags().Center());
#endif // wxUSE_MENUS/!wxUSE_MENUS

#if 0 // wxUSE_STATUSBAR
   // create a status bar just for fun (by default with 1 pane only)
   CreateStatusBar(2);
   SetStatusText("Welcome to wxWidgets!");
#endif // wxUSE_STATUSBAR
}


// event handlers

void MyFrame::OnQuit(wxCommandEvent& WXUNUSED(event))
{
   // true is to force the frame to close
   Close(true);
}

void MyFrame::OnMute(wxCommandEvent& WXUNUSED(event))
{
   if (!mIsMuted)
   {
      mIsMuted = true;
      mMuteBtn->SetBitmap(wxBITMAP_PNG(muted));
      CPCAPI2::Media::MediaManager* mediaMgrIf = CPCAPI2::Media::MediaManager::getInterface(getSdkRunner()->getPhone());
      CPCAPI2::Media::Audio* audioIf = CPCAPI2::Media::Audio::getInterface(mediaMgrIf);
      audioIf->setMicMute(true);
   }
   else
   {
      mIsMuted = false;
      mMuteBtn->SetBitmap(wxBITMAP_PNG(microphone));
      CPCAPI2::Media::MediaManager* mediaMgrIf = CPCAPI2::Media::MediaManager::getInterface(getSdkRunner()->getPhone());
      CPCAPI2::Media::Audio* audioIf = CPCAPI2::Media::Audio::getInterface(mediaMgrIf);
      audioIf->setMicMute(false);
   }
   //wxMessageBox(wxString::Format
   //(
   //   "Welcome to %s!\n"
   //   "\n"
   //   "This is the minimal wxWidgets sample\n"
   //   "running under %s.",
   //   wxVERSION_STRING,
   //   wxGetOsDescription()
   //),
   //   "About wxWidgets minimal sample",
   //   wxOK | wxICON_INFORMATION,
   //   this);
}

void MyFrame::OnThreadEvent(wxThreadEvent &event)
{
   if (event.GetInt() > 0)
   {
      Show(true);
      wxPoint mouseOnScreen = wxGetMousePosition();
      wxDisplay primaryDisplay(wxDisplay::GetFromPoint(mouseOnScreen));
      wxRect primaryGeo = primaryDisplay.GetGeometry();
      Move(primaryGeo.GetTopRight().x - 100, primaryGeo.GetTopRight().y + 54);
   }
   else
   {
      Show(false);
   }
}

void MyFrame::onAudioStreamStartStop(bool started)
{
   wxThreadEvent *evt = new wxThreadEvent(wxEVT_THREAD);
   evt->SetInt(started ? 1 : 0);
   QueueEvent(evt);
}
