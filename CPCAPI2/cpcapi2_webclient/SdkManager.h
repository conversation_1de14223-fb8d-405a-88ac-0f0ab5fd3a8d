#pragma once

#include <cpcapi2.h>
#include <jsonapi/JsonApiServer.h>
#include <jsonapi/JsonApiServerHandler.h>
#include <phone/PhoneInternal.h>

// rutil includes
#include <rutil/MultiReactor.hxx>
#include <rutil/Data.hxx>

namespace CPCAPI2
{
namespace Agent
{
class Cpcapi2Runner;
class SdkManager : public CPCAPI2::PhoneHandler,
                   public CPCAPI2::JsonApi::JsonApiServerHandler,
                   public CPCAPI2::Licensing::LicensingClientHandler
{
public:
   SdkManager();
   ~SdkManager();

   void run(std::function<void(Cpcapi2Runner*)> callbackFn)
   {
      mReactor.start();
      mReactor.post(resip::resip_bind(&SdkManager::appInit, this, callbackFn));
   }

   void shutdown();
   void join();

   void handleSdkCallback();

   Cpcapi2Runner* getRunner() const {
      return mRunner.get();
   }

   // PhoneHandler
   virtual int onError(const cpc::string& sourceModule, const CPCAPI2::PhoneErrorEvent& args);
   virtual int onLicensingError(const CPCAPI2::LicensingErrorEvent& args);
   virtual int onLicensingSuccess() {
      return 0;
   }

   // JsonApiServerHandler
   virtual int onNewLogin(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiuser, const CPCAPI2::JsonApi::NewLoginEvent& args);
   virtual int onSessionState(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiuser, const CPCAPI2::JsonApi::SessionStateEvent& args);
   virtual int onReLogin(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const CPCAPI2::JsonApi::ReLoginEvent& args) { return 0; }
   virtual int onPreLogout(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const CPCAPI2::JsonApi::PreLogoutEvent& args) { return 0; }
   virtual int onLogout(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const CPCAPI2::JsonApi::LogoutEvent& args) { return 0; }

   // LicensingClientHandler
   virtual int onValidateLicensesSuccess(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ValidateLicensesSuccessEvent& args);
   virtual int onValidateLicensesFailure(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ValidateLicensesFailureEvent& args);
   virtual int onError(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ErrorEvent& args);

private:
   void appInit(std::function<void(Cpcapi2Runner*)> callbackFn)
   {
      appInitImpl();
      callbackFn(mRunner.get());
   }

   void appInitImpl();
   void appShutdown();
   void handleSdkCallbackImpl();

   void initFromSettings();

private:
   resip::MultiReactor mReactor;
   CPCAPI2::PhoneInternal* mPhone;
   CPCAPI2::Licensing::LicensingClientManager* mLicensingMgr;
   CPCAPI2::JsonApi::JsonApiServer* mJsonApiServer;
   std::unique_ptr<Cpcapi2Runner> mRunner;
};
}
}
