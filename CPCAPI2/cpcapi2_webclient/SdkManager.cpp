#include "stdafx.h"
#include "SdkManager.h"
#include "Cpcapi2Runner.h"
#include "Cpcapi2ServerLicense.h"

#include <cpcapi2.h>

#include <fstream>

#define CPCAPI2_CONF_NUM_SDK_REACTORS 4

using namespace CPCAPI2;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::Licensing;

namespace CPCAPI2
{
namespace Agent
{
SdkManager::SdkManager()
{
}

SdkManager::~SdkManager()
{
}

void SdkManager_sdkCallbackHook(void* context)
{
   SdkManager* cpcRunner = (SdkManager*)context;
   cpcRunner->handleSdkCallback();
}

void SdkManager::handleSdkCallback()
{
   mReactor.post(resip::resip_bind(&SdkManager::handleSdkCallbackImpl, this));
}

void SdkManager::handleSdkCallbackImpl()
{
   mPhone->process(CPCAPI2::Phone::kBlockingModeNonBlocking);
   mJsonApiServer->process(CPCAPI2::JsonApi::JsonApiServer::kBlockingModeNonBlocking);
}

void SdkManager::appInitImpl()
{
   mPhone = CPCAPI2::PhoneInternal::create(0);
   LicenseInfo licenseInfo;
   licenseInfo.licenseKey = "lkj";
   licenseInfo.licenseDocumentLocation = "C:\\Temp";
   licenseInfo.licenseAor = "";
   mPhone->setCallbackHook(SdkManager_sdkCallbackHook, this);
   mPhone->initialize(licenseInfo, this, false);
   mPhone->setLoggingEnabled("cpcapi2webclient", false);

   mLicensingMgr = LicensingClientManager::getInterface(mPhone);
   mLicensingMgr->setCallbackHook(SdkManager_sdkCallbackHook, this);
   LicensingClientHandle client = mLicensingMgr->create();
   mLicensingMgr->setHandler(client, this);

   LicensingClientSettings settings;
   settings.licenseKeys.push_back(LICENSE_KEY_1);
   settings.licenseDocumentLocation = LICENSE_DOCUMENT_LOCATION;
   settings.provisioningId = LICENSE_AOR;
   settings.osVersion = "Windows for Workgroups 3.1";
   settings.keySourceUrl = "http://license.counterpath.com/generate";
   mLicensingMgr->applySettings(client, settings);
   mLicensingMgr->validateLicenses(client);

   mJsonApiServer = CPCAPI2::JsonApi::JsonApiServer::getInterface(mPhone);
   mJsonApiServer->setHandler(this);
   mJsonApiServer->setCallbackHook(SdkManager_sdkCallbackHook, this);
   CPCAPI2::JsonApi::JsonApiServerConfig serverConfig;
   serverConfig.jsonApiVersion = 9;
   serverConfig.websocketPort = 2114;
   serverConfig.numThreads = 1;
   serverConfig.wssCertificateFilePath = "-----BEGIN CERTIFICATE----- \n"
      "MIIG8TCCBdmgAwIBAgIRAJTeM8QLivWOfqDiVQnZHaswDQYJKoZIhvcNAQELBQAw \n"
      "gZAxCzAJBgNVBAYTAkdCMRswGQYDVQQIExJHcmVhdGVyIE1hbmNoZXN0ZXIxEDAO \n"
      "BgNVBAcTB1NhbGZvcmQxGjAYBgNVBAoTEUNPTU9ETyBDQSBMaW1pdGVkMTYwNAYD \n"
      "VQQDEy1DT01PRE8gUlNBIERvbWFpbiBWYWxpZGF0aW9uIFNlY3VyZSBTZXJ2ZXIg \n"
      "Q0EwHhcNMTgwODA5MDAwMDAwWhcNMjAwODA4MjM1OTU5WjBdMSEwHwYDVQQLExhE \n"
      "b21haW4gQ29udHJvbCBWYWxpZGF0ZWQxFDASBgNVBAsTC1Bvc2l0aXZlU1NMMSIw \n"
      "IAYDVQQDExljcGNsaWVudGFwaS5zb2Z0cGhvbmUuY29tMIIBIjANBgkqhkiG9w0B \n"
      "AQEFAAOCAQ8AMIIBCgKCAQEAvkbgEWMxFDdIoH3m7QNcws+rHuyrOd/h9NZjNMLx \n"
      "57mLyFPgi+jmD4IiMc4Ad4R7EsIZI0gw5LeGeB6UdIVU/LU4x7jYhm4y+Nz2uhtX \n"
      "ageEnUz9DcVD3fbG2rr/4Z9vf73KQ6uwT49KjOp8Z00cINW3VBWBPM1y7k0IMLR9 \n"
      "Ii2qMpw+QgItfXEHBmWLH1R91sUuYMoo4n7dOEgWsCr+Iz1+xDtzrsDPQByIcrNx \n"
      "nPQC5xIHN1H38EuiKx8iBwIWms0RMyFZKxp4iGJPKubzPy/FRyvzejPWlSneNTi2 \n"
      "skCvf+CUW5zy6v4a1JsMpYd+YunJaSKuq3eOhrdgDAG6cwIDAQABo4IDdjCCA3Iw \n"
      "HwYDVR0jBBgwFoAUkK9qOpRaC9iQ6hJWc99DtDoo2ucwHQYDVR0OBBYEFE3ga+QH \n"
      "127honttGF73Z3ynVFMOMA4GA1UdDwEB/wQEAwIFoDAMBgNVHRMBAf8EAjAAMB0G \n"
      "A1UdJQQWMBQGCCsGAQUFBwMBBggrBgEFBQcDAjBPBgNVHSAESDBGMDoGCysGAQQB \n"
      "sjEBAgIHMCswKQYIKwYBBQUHAgEWHWh0dHBzOi8vc2VjdXJlLmNvbW9kby5jb20v \n"
      "Q1BTMAgGBmeBDAECATBUBgNVHR8ETTBLMEmgR6BFhkNodHRwOi8vY3JsLmNvbW9k \n"
      "b2NhLmNvbS9DT01PRE9SU0FEb21haW5WYWxpZGF0aW9uU2VjdXJlU2VydmVyQ0Eu \n"
      "Y3JsMIGFBggrBgEFBQcBAQR5MHcwTwYIKwYBBQUHMAKGQ2h0dHA6Ly9jcnQuY29t \n"
      "b2RvY2EuY29tL0NPTU9ET1JTQURvbWFpblZhbGlkYXRpb25TZWN1cmVTZXJ2ZXJD \n"
      "QS5jcnQwJAYIKwYBBQUHMAGGGGh0dHA6Ly9vY3NwLmNvbW9kb2NhLmNvbTBDBgNV \n"
      "HREEPDA6ghljcGNsaWVudGFwaS5zb2Z0cGhvbmUuY29tgh13d3cuY3BjbGllbnRh \n"
      "cGkuc29mdHBob25lLmNvbTCCAX0GCisGAQQB1nkCBAIEggFtBIIBaQFnAHYA7ku9 \n"
      "t3XOYLrhQmkfq+GeZqMPfl+wctiDAMR7iXqo/csAAAFlIKa2xgAABAMARzBFAiBN \n"
      "nKBKIvlThdKYJsE3SfqPkQmsnttdrRrLKxp/4TE7cQIhAIXP26gE8JWUGT06yKxM \n"
      "IfFCwv1dm6nEsLf4gRiO0CoCAHUAXqdz+d9WwOe1Nkh90EngMnqRmgyEoRIShBh1 \n"
      "loFxRVgAAAFlIKa3BwAABAMARjBEAiBlTyXmTN1iBB/NGVNbqN1MEUJt4XIV7zqN \n"
      "UMt9rKrfnQIgNKXl6VBfbvHKGakhwOpPo+t2Aq2K/BAgKohg8xRwEQQAdgBVgdTC \n"
      "FpA2AUrqC5tXPFPwwOQ4eHAlCBcvo6odBxPTDAAAAWUgprbrAAAEAwBHMEUCIQCc \n"
      "s23Cb8djNfLmSfAgURzyyIlkGQSsUIk4hWsf02S7sQIgbOibohoLYiMbE4/ocTCn \n"
      "cvgjtKR3E6T3XCoQRbMsOZUwDQYJKoZIhvcNAQELBQADggEBAALeP3EQkGIWC9mw \n"
      "gum5JvCVmxSoHFHKYQPcEqKXI7zcU7mbq35G/ZZUzADd4Ckc9DPlDudekxgLGLK4 \n"
      "pNffrv4GR+zuk4VZX9bQoD2Op7vhaSX1coN06Qt8L4FQyonagUU99svy86/bWhCH \n"
      "eEAg1Dhq8CuY/y3D+n9FSv+v1NL/jkfnwg09AZ+FQsoTK1sGCmwAnESR3ueL+jus \n"
      "MBdM8MpIWjp9OOmvLU+GvZTt0eKFh0ZKHXbUOsBeWVk8AqHU8XcB8qIomgQJsrX+ \n"
      "tVrrbVTZ4AgkTgpHxm/boSiEQCNzjsKkuFHuc3MMVgf3557HgXDgAybbX9b+k4pj \n"
      "/dGuVTw= \n"
      "-----END CERTIFICATE-----\n";
**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
   serverConfig.wssDiffieHellmanParamsFilePath = "-----BEGIN DH PARAMETERS-----\n"
      "MIIBCAKCAQEA5P4ae+ijV2PnB7DdKHL4NDozSQKGf3N+0H6M75H1EUbFoP00hPFo\n"
      "sC/58Yz1pAXNIDW17PrAPIVKbUVgYG6ZwV7xcUSf7nOhdo+SbUMmCw/r1DEXGyu6\n"
      "+GLgGkYk8AejSZ1TtmBcOEg6ENstZ6xTSO+Wi/i8sIYon57Z7qv6oiFgwh0QXByL\n"
      "MxNDA7ZjYEs6GvgsYWMIHVPyD+w1V4JDh3wQE4Mi41iezvuUFRcq+HdmwUDG9Pmp\n"
      "u8iNFCrHiPu+LX6Bxwyfose4fwQgM31jWOCizynB9TapY6XKVlQ4lYwLkxQ51aKu\n"
      "T4WlnBc2ntTxJxDsvGxtOSez6jOM6zv1AwIBAg==\n"
      "-----END DH PARAMETERS-----\n";

   mJsonApiServer->start(serverConfig);
   //mJsonApiServer->setJsonApiUserContext((CPCAPI2::JsonApi::JsonApiUserHandle)UINT_MAX, mPhone);

   CPCAPI2::VideoStreaming::VideoStreamingManager* videoStreamingMgr = CPCAPI2::VideoStreaming::VideoStreamingManager::getInterface(mPhone);
   if (videoStreamingMgr != NULL)
   {
      VideoStreaming::VideoStreamingServerConfig streamingServerConfig;
      streamingServerConfig.listenPort = 2115;
      streamingServerConfig.wssCertificateFilePath = "-----BEGIN CERTIFICATE----- \n"
         "MIIG8TCCBdmgAwIBAgIRAJTeM8QLivWOfqDiVQnZHaswDQYJKoZIhvcNAQELBQAw \n"
         "gZAxCzAJBgNVBAYTAkdCMRswGQYDVQQIExJHcmVhdGVyIE1hbmNoZXN0ZXIxEDAO \n"
         "BgNVBAcTB1NhbGZvcmQxGjAYBgNVBAoTEUNPTU9ETyBDQSBMaW1pdGVkMTYwNAYD \n"
         "VQQDEy1DT01PRE8gUlNBIERvbWFpbiBWYWxpZGF0aW9uIFNlY3VyZSBTZXJ2ZXIg \n"
         "Q0EwHhcNMTgwODA5MDAwMDAwWhcNMjAwODA4MjM1OTU5WjBdMSEwHwYDVQQLExhE \n"
         "b21haW4gQ29udHJvbCBWYWxpZGF0ZWQxFDASBgNVBAsTC1Bvc2l0aXZlU1NMMSIw \n"
         "IAYDVQQDExljcGNsaWVudGFwaS5zb2Z0cGhvbmUuY29tMIIBIjANBgkqhkiG9w0B \n"
         "AQEFAAOCAQ8AMIIBCgKCAQEAvkbgEWMxFDdIoH3m7QNcws+rHuyrOd/h9NZjNMLx \n"
         "57mLyFPgi+jmD4IiMc4Ad4R7EsIZI0gw5LeGeB6UdIVU/LU4x7jYhm4y+Nz2uhtX \n"
         "ageEnUz9DcVD3fbG2rr/4Z9vf73KQ6uwT49KjOp8Z00cINW3VBWBPM1y7k0IMLR9 \n"
         "Ii2qMpw+QgItfXEHBmWLH1R91sUuYMoo4n7dOEgWsCr+Iz1+xDtzrsDPQByIcrNx \n"
         "nPQC5xIHN1H38EuiKx8iBwIWms0RMyFZKxp4iGJPKubzPy/FRyvzejPWlSneNTi2 \n"
         "skCvf+CUW5zy6v4a1JsMpYd+YunJaSKuq3eOhrdgDAG6cwIDAQABo4IDdjCCA3Iw \n"
         "HwYDVR0jBBgwFoAUkK9qOpRaC9iQ6hJWc99DtDoo2ucwHQYDVR0OBBYEFE3ga+QH \n"
         "127honttGF73Z3ynVFMOMA4GA1UdDwEB/wQEAwIFoDAMBgNVHRMBAf8EAjAAMB0G \n"
         "A1UdJQQWMBQGCCsGAQUFBwMBBggrBgEFBQcDAjBPBgNVHSAESDBGMDoGCysGAQQB \n"
         "sjEBAgIHMCswKQYIKwYBBQUHAgEWHWh0dHBzOi8vc2VjdXJlLmNvbW9kby5jb20v \n"
         "Q1BTMAgGBmeBDAECATBUBgNVHR8ETTBLMEmgR6BFhkNodHRwOi8vY3JsLmNvbW9k \n"
         "b2NhLmNvbS9DT01PRE9SU0FEb21haW5WYWxpZGF0aW9uU2VjdXJlU2VydmVyQ0Eu \n"
         "Y3JsMIGFBggrBgEFBQcBAQR5MHcwTwYIKwYBBQUHMAKGQ2h0dHA6Ly9jcnQuY29t \n"
         "b2RvY2EuY29tL0NPTU9ET1JTQURvbWFpblZhbGlkYXRpb25TZWN1cmVTZXJ2ZXJD \n"
         "QS5jcnQwJAYIKwYBBQUHMAGGGGh0dHA6Ly9vY3NwLmNvbW9kb2NhLmNvbTBDBgNV \n"
         "HREEPDA6ghljcGNsaWVudGFwaS5zb2Z0cGhvbmUuY29tgh13d3cuY3BjbGllbnRh \n"
         "cGkuc29mdHBob25lLmNvbTCCAX0GCisGAQQB1nkCBAIEggFtBIIBaQFnAHYA7ku9 \n"
         "t3XOYLrhQmkfq+GeZqMPfl+wctiDAMR7iXqo/csAAAFlIKa2xgAABAMARzBFAiBN \n"
         "nKBKIvlThdKYJsE3SfqPkQmsnttdrRrLKxp/4TE7cQIhAIXP26gE8JWUGT06yKxM \n"
         "IfFCwv1dm6nEsLf4gRiO0CoCAHUAXqdz+d9WwOe1Nkh90EngMnqRmgyEoRIShBh1 \n"
         "loFxRVgAAAFlIKa3BwAABAMARjBEAiBlTyXmTN1iBB/NGVNbqN1MEUJt4XIV7zqN \n"
         "UMt9rKrfnQIgNKXl6VBfbvHKGakhwOpPo+t2Aq2K/BAgKohg8xRwEQQAdgBVgdTC \n"
         "FpA2AUrqC5tXPFPwwOQ4eHAlCBcvo6odBxPTDAAAAWUgprbrAAAEAwBHMEUCIQCc \n"
         "s23Cb8djNfLmSfAgURzyyIlkGQSsUIk4hWsf02S7sQIgbOibohoLYiMbE4/ocTCn \n"
         "cvgjtKR3E6T3XCoQRbMsOZUwDQYJKoZIhvcNAQELBQADggEBAALeP3EQkGIWC9mw \n"
         "gum5JvCVmxSoHFHKYQPcEqKXI7zcU7mbq35G/ZZUzADd4Ckc9DPlDudekxgLGLK4 \n"
         "pNffrv4GR+zuk4VZX9bQoD2Op7vhaSX1coN06Qt8L4FQyonagUU99svy86/bWhCH \n"
         "eEAg1Dhq8CuY/y3D+n9FSv+v1NL/jkfnwg09AZ+FQsoTK1sGCmwAnESR3ueL+jus \n"
         "MBdM8MpIWjp9OOmvLU+GvZTt0eKFh0ZKHXbUOsBeWVk8AqHU8XcB8qIomgQJsrX+ \n"
         "tVrrbVTZ4AgkTgpHxm/boSiEQCNzjsKkuFHuc3MMVgf3557HgXDgAybbX9b+k4pj \n"
         "/dGuVTw= \n"
         "-----END CERTIFICATE-----\n";
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
      streamingServerConfig.wssDiffieHellmanParamsFilePath = "-----BEGIN DH PARAMETERS-----\n"
         "MIIBCAKCAQEA5P4ae+ijV2PnB7DdKHL4NDozSQKGf3N+0H6M75H1EUbFoP00hPFo\n"
         "sC/58Yz1pAXNIDW17PrAPIVKbUVgYG6ZwV7xcUSf7nOhdo+SbUMmCw/r1DEXGyu6\n"
         "+GLgGkYk8AejSZ1TtmBcOEg6ENstZ6xTSO+Wi/i8sIYon57Z7qv6oiFgwh0QXByL\n"
         "MxNDA7ZjYEs6GvgsYWMIHVPyD+w1V4JDh3wQE4Mi41iezvuUFRcq+HdmwUDG9Pmp\n"
         "u8iNFCrHiPu+LX6Bxwyfose4fwQgM31jWOCizynB9TapY6XKVlQ4lYwLkxQ51aKu\n"
         "T4WlnBc2ntTxJxDsvGxtOSez6jOM6zv1AwIBAg==\n"
         "-----END DH PARAMETERS-----\n";
      videoStreamingMgr->startVideoStreamingServer(streamingServerConfig);
   }

   initFromSettings();

   mRunner.reset(new Cpcapi2Runner(0, &mReactor, mPhone, mJsonApiServer));
}

void SdkManager::shutdown()
{
   //mTTSApi->shutdown();
   mReactor.execute(resip::resip_bind(&SdkManager::appShutdown, this));
   mReactor.stop();
}

void SdkManager::appShutdown()
{
   if (mRunner.get() != NULL)
   {
      mRunner->shutdown();
   }

   mJsonApiServer->shutdown();

   CPCAPI2::VideoStreaming::VideoStreamingManager* videoStreamingMgr = CPCAPI2::VideoStreaming::VideoStreamingManager::getInterface(mPhone);
   if (videoStreamingMgr != NULL)
   {
      videoStreamingMgr->stopVideoStreamingServer();
   }
}

void SdkManager::join()
{
   mReactor.join();
}

void SdkManager::initFromSettings()
{
}

int SdkManager::onError(const cpc::string& sourceModule, const CPCAPI2::PhoneErrorEvent& args)
{
   return 0;
}

int SdkManager::onLicensingError(const CPCAPI2::LicensingErrorEvent& args)
{
   return 0;
}

int SdkManager::onNewLogin(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const CPCAPI2::JsonApi::NewLoginEvent& args)
{
   cpc::vector<cpc::string> permissions;
   permissions.push_back("*");
   assert(mRunner.get() != NULL);
   mJsonApiServer->setJsonApiUserContext(jsonApiUser, mRunner->getPhone(), permissions);

   CPCAPI2::JsonApi::LoginResultEvent loginResult;
   loginResult.success = true;

   mJsonApiServer->sendLoginResult(jsonApiUser, loginResult);

   return 0;
}

int SdkManager::onSessionState(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiuser, const CPCAPI2::JsonApi::SessionStateEvent& args) 
{ 
   if (!args.isActive)
   {
      mRunner->handleSessionDisconnected();
   }
   return 0; 
}

int SdkManager::onValidateLicensesSuccess(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ValidateLicensesSuccessEvent& args)
{
   return 0;
}

int SdkManager::onValidateLicensesFailure(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ValidateLicensesFailureEvent& args)
{
   return 0;
}

int SdkManager::onError(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ErrorEvent& args)
{
   return 0;
}
}
}
