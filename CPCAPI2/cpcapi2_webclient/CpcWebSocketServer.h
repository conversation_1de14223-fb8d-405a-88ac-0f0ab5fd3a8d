#pragma once

#include <websocketpp/config/asio_no_tls.hpp>
#include <websocketpp/server.hpp>
#include <iostream>
#include <set>
#include <thread>

namespace CPCAPI2
{
namespace Agent
{
class CpcWebSocketServer
{
public:
   CpcWebSocketServer();
   virtual ~CpcWebSocketServer();

   void StartServer(CPCAPI2::Phone* phone);
   void StopServer();

private:
   typedef websocketpp::server<websocketpp::config::asio> server;
   typedef server::message_ptr message_ptr;

   struct ConnInfo
   {
      ConnInfo(websocketpp::connection_hdl h) : conn(h), waitingForSap(true), nextSeqNum(0), decodeTimeOffset(0) {}
      websocketpp::connection_hdl conn;
      bool waitingForSap;
      uint32_t nextSeqNum;
      uint64_t decodeTimeOffset;
   };
   typedef std::map<websocketpp::connection_hdl, std::shared_ptr<ConnInfo>, std::owner_less<websocketpp::connection_hdl> > con_list;

   void on_open(server* s, websocketpp::connection_hdl hdl);
   void on_close(server* s, websocketpp::connection_hdl hdl);
   void on_message(server* s, websocketpp::connection_hdl hdl, message_ptr msg);

private:
   CPCAPI2::Phone* mPhone;
   server mWebSockServer;
   con_list mConnections;
   std::thread* mServerThread;
};
}
}
