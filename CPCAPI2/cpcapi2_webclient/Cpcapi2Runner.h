#pragma once

// SDK includes
#include <cpcapi2.h>
#include <jsonapi/JsonApiServer.h>
#include <phone/PhoneInternal.h>

namespace resip
{
   class MultiReactor;
}

namespace CPCAPI2
{
namespace Agent
{
class WatsonTTS;

class Cpcapi2Runner : public CPCAPI2::<PERSON><PERSON><PERSON><PERSON>,
                      public CPCAPI2::SipAccount::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
                      public CPCAPI2::SipConversation::SipConversationHandler,
                      public CPCAPI2::ConferenceBridge::ConferenceBridgeHandler,
                      public CPCAPI2::Media::VideoHandler,
                      public CPCAPI2::Media::AudioHandler
{
public:
   Cpcapi2Runner(int sdkThreadPoolThreadIdx, resip::MultiReactor* appReactor, CPCAPI2::Phone* masterSdkPhone, CPCAPI2::JsonApi::JsonApiServer* jsonApiServer);
   virtual ~Cpcapi2Runner();

   resip::Data getContext() const;

   void handleSdkCallback();
   void shutdown();

   void handleSessionDisconnected();

   CPCAPI2::Phone* getPhone() const {
      return mPhone;
   }

   void setAudioStreamStartStopCallback(const std::function<void(bool)>& callbackFn) {
      mAudioStreamStartStopCb = callbackFn;
   }

   // PhoneHandler
   virtual int onError(const cpc::string& sourceModule, const CPCAPI2::PhoneErrorEvent& args);
   virtual int onLicensingError(const CPCAPI2::LicensingErrorEvent& args);
   virtual int onLicensingSuccess() {
      return 0;
   }

   // SipAccountHandler
   virtual int onAccountStatusChanged(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountStatusChangedEvent& args);
   virtual int onError(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::ErrorEvent& args);

   // SipConversationHandler
   virtual int onNewConversation(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::NewConversationEvent& args);
   virtual int onConversationEnded(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationEndedEvent& args);
   virtual int onIncomingTransferRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::TransferRequestEvent& args);
   virtual int onIncomingRedirectRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::RedirectRequestEvent& args);
   virtual int onIncomingTargetChangeRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::TargetChangeRequestEvent& args);
   virtual int onIncomingHangupRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::HangupRequestEvent& args);
   virtual int onIncomingBroadsoftTalkRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::BroadsoftTalkEvent& args);
   virtual int onIncomingBroadsoftHoldRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::BroadsoftHoldEvent& args);
   virtual int onTransferProgress(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::TransferProgressEvent& args);
   virtual int onConversationStateChangeRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationStateChangeRequestEvent& args);
   virtual int onConversationStateChanged(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationStateChangedEvent& args);
   virtual int onConversationMediaChangeRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationMediaChangeRequestEvent& args);
   virtual int onConversationMediaChanged(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationMediaChangedEvent& args);
   virtual int onConversationStatisticsUpdated(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationStatisticsUpdatedEvent& args);
   virtual int onError(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ErrorEvent& args);

   // VideoHandler
   virtual int onVideoDeviceListUpdated(const CPCAPI2::Media::VideoDeviceListUpdatedEvent& args);
   virtual int onVideoCodecListUpdated(const CPCAPI2::Media::VideoCodecListUpdatedEvent& args);

   // AudioHandler
   virtual int onAudioDeviceListUpdated(const CPCAPI2::Media::AudioDeviceListUpdatedEvent& args);
   virtual int onPlaySoundComplete(CPCAPI2::Media::PlaySoundHandle soundClip);
   virtual int onPlaySoundFailure(CPCAPI2::Media::PlaySoundHandle soundClip);
   virtual int onAudioCodecListUpdated(const CPCAPI2::Media::AudioCodecListUpdatedEvent& args);
   virtual int onAudioDeviceVolume(const CPCAPI2::Media::AudioDeviceVolumeEvent& args);
   virtual int onAudioStreamStarted(const CPCAPI2::Media::AudioStreamStartedEvent& args);
   virtual int onAudioStreamStopped(const CPCAPI2::Media::AudioStreamStoppedEvent& args);

   // ConferenceBridgeHandler
   virtual int onConferenceDetails(CPCAPI2::ConferenceBridge::ConferenceHandle conference, const CPCAPI2::ConferenceBridge::ConferenceDetailsResult& args);
   virtual int onConferenceEnded(CPCAPI2::ConferenceBridge::ConferenceHandle conference, const CPCAPI2::ConferenceBridge::ConferenceEndedEvent& args) {
      return kSuccess;
   }
   virtual int onPeerConnectionAnswer(CPCAPI2::ConferenceBridge::ConferenceHandle conference, const CPCAPI2::ConferenceBridge::PeerConnectionAnswerEvent& args) {
      return kSuccess;
   }
   virtual int onParticipantListState(CPCAPI2::ConferenceBridge::ConferenceHandle conference, const CPCAPI2::ConferenceBridge::ParticipantListState& args) {
      return kSuccess;
   }
   virtual int onConferenceTranscriptionResult(CPCAPI2::ConferenceBridge::ConferenceHandle conference, const CPCAPI2::ConferenceBridge::ConferenceTranscriptionEvent& args) {
      return kSuccess;
   }

private:
   void appInit();
   void appShutdown();

   void handleSdkCallbackImpl();

   CPCAPI2::PhoneInternal* getPhoneInternalForLogger() const;
   void initAccountSettings();

   void playWelcome(CPCAPI2::SipConversation::SipConversationHandle conversation, const std::string& fileName);

private:
   int mSdkThreadPoolThreadIdx;
   resip::MultiReactor* mAppReactor;
   CPCAPI2::PhoneInternal* mPhone;
   CPCAPI2::Phone* mMasterSdkPhone;
   CPCAPI2::JsonApi::JsonApiServer* mJsonApiServer;
   CPCAPI2::Media::MediaManager* mMedia;
   CPCAPI2::PeerConnection::PeerConnectionManager* mPeerConnIf;
   CPCAPI2::ConferenceConnector::ConferenceConnectorManager* mConfConnectorIf;
   std::function<void(bool)> mAudioStreamStartStopCb;
   int mAudioStreamCnt;

   CPCAPI2::SipAccount::SipAccountManager* mSipAcctMgrIf;
   CPCAPI2::SipConversation::SipConversationManager* mConvMgrIf;
   CPCAPI2::SipConversation::SipConversationStateManager* mConvStateMgrIf;
};
}
}
