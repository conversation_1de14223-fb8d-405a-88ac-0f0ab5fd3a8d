console.info("index", "START");
var infoDiv;
var conversationsTableBody;
var inputDestAddr;
var inputLoginContext;

var sdkConnector = new SdkConnector();
var sipConvMgr = new SipConversationManager();
var confBridgeMgr = new ConferenceBridgeManager();
sdkConnector.registerEventProcessor(sipConvMgr);
sdkConnector.registerEventProcessor(confBridgeMgr);
sipConvMgr.setSdkConnector(sdkConnector);
confBridgeMgr.setSdkConnector(sdkConnector);

sdkConnector.setConnectedCallback( 
	function () {
		sipConvMgr.setCallback(
			512,
			function (eventDataObj) {
				console.info("index", "sipconv event: " + eventDataObj.functionName);
				infoDiv.innerHTML += "sipconv event: " + eventDataObj.functionName + "<br>\r\n";
				infoDiv.scrollTop = infoDiv.scrollHeight;
				
				
				/*
				if (eventDataObj.functionName == "onConversationState") {
					var new_tbody = document.createElement('tbody');
					var convState = sipConvMgr.conversationState;
					convState.forEach(function(convStateItem) {
						if (convStateItem.conversationState != 1060) {
							var convRow = new_tbody.insertRow(new_tbody.rows.length);
							var dnCell = convRow.insertCell(0);
							dnCell.innerHTML = convStateItem.remoteDisplayName + " (" + convStateItem.remoteAddress + ")";
							var stateCell = convRow.insertCell(1);
							stateCell.innerHTML = getConvStateName(convStateItem.conversationState);
							var endCell = convRow.insertCell(2);
							endCell.innerHTML = "<input type='submit' value='End' onclick='endCall(" + convStateItem.conversation + ")'/>"
							var muteAudioCell = convRow.insertCell(3);
							muteAudioCell.innerHTML = "<input type='submit' value='Mute Audio' onclick='muteAudio(" + convStateItem.conversation + ")'/>"
							var unmuteAudioCell = convRow.insertCell(4);
							unmuteAudioCell.innerHTML = "<input type='submit' value='UnMute Audio' onclick='unmuteAudio(" + convStateItem.conversation + ")'/>"
							var muteVideoCell = convRow.insertCell(5);
							muteVideoCell.innerHTML = "<input type='submit' value='Mute Video' onclick='muteVideo(" + convStateItem.conversation + ")'/>"
							var unmuteVideoCell = convRow.insertCell(6);
							unmuteVideoCell.innerHTML = "<input type='submit' value='UnMute Video' onclick='unmuteVideo(" + convStateItem.conversation + ")'/>"
						}
					});
					conversationsTableBody.parentNode.replaceChild(new_tbody, conversationsTableBody);
					conversationsTableBody = new_tbody;
				}
				else {
					sipConvMgr.requestStateAllConversations();
				}
				*/
			}
		);
		//sipConvMgr.requestStateAllConversations();
		
		confBridgeMgr.queryConferenceDetails("KESM", function(confHandle, args) {
			console.info("index", "onConferenceDetails: " + args.conferenceToken + " (" + confHandle + ")");
			confBridgeMgr.setCallback(confHandle, function(args) {
				if (args.functionName == "onParticipantListState") {
					var new_tbody = document.createElement('tbody');
					var participants = args.participantsArray;
					participants.forEach(function(partInfo) {
						var convRow = new_tbody.insertRow(new_tbody.rows.length);
						var dnCell = convRow.insertCell(0);
						dnCell.innerHTML = partInfo.displayName + " (" + partInfo.address + ")";
						//var stateCell = convRow.insertCell(1);
						//stateCell.innerHTML = getConvStateName(convStateItem.conversationState);
						//var endCell = convRow.insertCell(2);
						//endCell.innerHTML = "<input type='submit' value='End' onclick='endCall(" + convStateItem.conversation + ")'/>"
						//var muteAudioCell = convRow.insertCell(3);
						//muteAudioCell.innerHTML = "<input type='submit' value='Mute Audio' onclick='muteAudio(" + convStateItem.conversation + ")'/>"
						//var unmuteAudioCell = convRow.insertCell(4);
						//unmuteAudioCell.innerHTML = "<input type='submit' value='UnMute Audio' onclick='unmuteAudio(" + convStateItem.conversation + ")'/>"
						//var muteVideoCell = convRow.insertCell(5);
						//muteVideoCell.innerHTML = "<input type='submit' value='Mute Video' onclick='muteVideo(" + convStateItem.conversation + ")'/>"
						//var unmuteVideoCell = convRow.insertCell(6);
						//unmuteVideoCell.innerHTML = "<input type='submit' value='UnMute Video' onclick='unmuteVideo(" + convStateItem.conversation + ")'/>"
					});
					conversationsTableBody.parentNode.replaceChild(new_tbody, conversationsTableBody);
					conversationsTableBody = new_tbody;					
				}
			});
			confBridgeMgr.queryParticipantList(confHandle);
		});
	}
);
sdkConnector.start();

window.onload = function () {
	infoDiv = document.getElementById('infoDiv');
	conversationsTableBody = document.getElementById('conversationsTableBody');
	inputDestAddr = document.getElementById('inputDestAddr');
	inputLoginContext = document.getElementById('inputLoginContext');
}

function getConvStateName(_state) {
   if (_state == 0) { return "None"; }
   if (_state == 1000) { return "LocalOriginated"; }
   if (_state == 1010) { return "RemoteOriginated"; }
   if (_state == 1020) { return "RemoteRinging"; }
   if (_state == 1030) { return "LocalRinging"; }
   if (_state == 1040) { return "Connected"; }
   if (_state == 1050) { return "Early"; }
   if (_state == 1060) { return "Ended"; }
}

function makeCall() {
	console.info("index", "makeCall: " + inputDestAddr.value);
	sipConvMgr.createConversation(512, function(convHandle) {
		sipConvMgr.addParticipant(convHandle, "sip:" + inputDestAddr.value);
		sipConvMgr.start(convHandle);
	});
}

function endCall(_conversation) {
	console.info("index", "endCall: " + _conversation);
	sipConvMgr.end(_conversation);
}

function muteAudio(_conversation) {
	console.info("index", "muteAudio: " + _conversation);
/*
enum MediaType
{
    MediaType_Audio            = 0x1,
    MediaType_Video            = 0x2
};	
enum MediaDirection
{
    MediaDirection_None           = 0,
    MediaDirection_SendReceive    = 1,
    MediaDirection_SendOnly       = 2,
    MediaDirection_ReceiveOnly    = 3,
    MediaDirection_Inactive       = 4
};
*/
	sipConvMgr.configureMedia(_conversation, 1, 4, 0, 1, false);
	sipConvMgr.sendMediaChangeRequest(_conversation);
}

function unmuteAudio(_conversation) {
	console.info("index", "unmuteAudio: " + _conversation);
	sipConvMgr.configureMedia(_conversation, 1, 1, 0, 1, false);
	sipConvMgr.sendMediaChangeRequest(_conversation);
}

function muteVideo(_conversation) {
	console.info("index", "muteVideo: " + _conversation);
	sipConvMgr.configureMedia(_conversation, 2, 4, 0, 1, false);
	sipConvMgr.sendMediaChangeRequest(_conversation);
}

function unmuteVideo(_conversation) {
	console.info("index", "unmuteVideo: " + _conversation);
	sipConvMgr.configureMedia(_conversation, 2, 1, 0, 1, false);
	sipConvMgr.sendMediaChangeRequest(_conversation);
}

function doLogin() {
	console.info("index", "login: " + inputLoginContext.value);
	sdkConnector.login(inputLoginContext.value);
}
console.info("index", "END");