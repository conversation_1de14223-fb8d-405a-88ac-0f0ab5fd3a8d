
function ConferenceBridgeManager() {
	this.callback = null;
	this.confDetailsCallback = null;
	this.sdkConnector = null;
}

ConferenceBridgeManager.prototype.setSdkConnector = function(_sdkConnector) {
	this.sdkConnector = _sdkConnector;
	return this;
}

ConferenceBridgeManager.prototype.setCallback = function(_account, _callback) {
	this.callback = _callback;
	var setHandlerFunc = {};
	setHandlerFunc.moduleId = "SipConversationJsonApi";
	setHandlerFunc.functionObject = {};
	setHandlerFunc.functionObject.functionName = "setHandler";
	setHandlerFunc.functionObject.account = _account;
	this.sdkConnector.send(JSON.stringify(setHandlerFunc));
	return this;
}

ConferenceBridgeManager.prototype.queryConferenceDetails = function(_confToken, _callback) {
	var fn = {};
	fn.moduleId = "ConferenceBridgeJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "queryConferenceDetails2";
	fn.functionObject.conferenceToken = _confToken;
	this.confDetailsCallback = _callback;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

ConferenceBridgeManager.prototype.queryParticipantList = function(_conf) {
	var fn = {};
	fn.moduleId = "ConferenceBridgeJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "queryParticipantList";
	fn.functionObject.conference = _conf;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

ConferenceBridgeManager.prototype.processEvent = function(_jsonEventObj) {
	if (_jsonEventObj.functionObject.functionName == "onConferenceDetails") {
		if (this.confDetailsCallback) {
			var args = {};
			args.conference = _jsonEventObj.functionObject.conference;
			args.conferenceToken = _jsonEventObj.functionObject.conferenceToken;
			this.confDetailsCallback(_jsonEventObj.functionObject.conference, args);
		}
		this.confDetailsCallback = null;
	}
	else if (_jsonEventObj.functionObject.functionName == "onParticipantListState") {
    	this.callback(_jsonEventObj.functionObject);
	}
	return this;
}
