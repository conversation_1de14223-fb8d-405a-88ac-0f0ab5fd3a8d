
function SipAccountManager() {
	this.callback = null;
	this.sdkConnector = null;
}

SipAccountManager.prototype.setSdkConnector = function(_sdkConnector) {
	this.sdkConnector = _sdkConnector;
	return this;
}

SipAccountManager.prototype.setCallback = function(_account, _callback) {
	this.callback = _callback;
	//var setHandlerFunc = {};
	//setHandlerFunc.moduleId = "SipConversationJsonApi";
	//setHandlerFunc.functionObject = {};
	//setHandlerFunc.functionObject.functionName = "setHandler";
	//setHandlerFunc.functionObject.account = _account;
	//this.sdkConnector.send(JSON.stringify(setHandlerFunc));
	return this;
}

SipAccountManager.prototype.processEvent = function(_jsonEventObj) {
	this.callback(_jsonEventObj.functionObject);
	return this;
}
