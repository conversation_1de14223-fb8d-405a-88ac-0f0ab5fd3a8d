console.info("SdkConnector", "load");

function SdkConnector() {
	console.info("SdkConnector", "ctor");
	this.isActive = false;
	this.url = null;
	this.callback = null;
	this.exampleSocket = null;
	this.eventProcessors = [];
	this.connectedCallback = null;
}

SdkConnector.prototype.setUrl = function(_url) {
	this.url = _url;
	return this;
}

SdkConnector.prototype.setConnectedCallback = function(_callback) {
	this.connectedCallback = _callback;
	return this;
}

SdkConnector.prototype.setCallback = function(_callback) {
	this.callback = _callback;
	return this;
}

SdkConnector.prototype.registerEventProcessor = function(_eventProcessor) {
	this.eventProcessors.push(_eventProcessor);
}

SdkConnector.prototype.isStopped = function () {
	return !this.isActive;
}

function makeWsUrl(port) {
    var l = window.location;
    return ((l.protocol === "https:") ? "wss://" : "ws://") + l.hostname + ":" + port;
}

SdkConnector.prototype.start = function() {
	console.info("SdkConnector", "Connecting to WebSocket server");
    var dl = this;
    this.exampleSocket = new WebSocket(makeWsUrl(9003));
	//this.exampleSocket.binaryType = "arraybuffer";
    this.exampleSocket.onopen = function (event) {
		console.info("SdkConnector", "Connected to WebSocket server");
		//dl.connectedCallback();
    };	
    this.exampleSocket.onmessage = function (event) {
		//dl.callback(event.data);
		console.debug("SdkConnector", "Received data from WebSocket server (" + event.data.byteLength + " bytes) " + event.data);
		var eventObj = JSON.parse(event.data);
		if (eventObj.functionObject.functionName == "onLoginResult") {
			dl.connectedCallback();
		}
		else {
			dl.eventProcessors.forEach( function (eventProcessor)
			{
				eventProcessor.processEvent(eventObj);
			});		
		}
    }	
	this.isActive = true;
	return this;
}

SdkConnector.prototype.stop = function() {
	//Log.info("Downloader", "Stopping file download");
	this.isActive = false;
	return this;
}

SdkConnector.prototype.login = function(_context) {
	var loginFunc = {};
	loginFunc.moduleId = "JsonApiServer";
	loginFunc.functionObject = {};
	loginFunc.functionObject.functionName = "login";
	loginFunc.functionObject.context = _context;
	this.send(JSON.stringify(loginFunc));
	return this;
}

SdkConnector.prototype.send = function(_payload) {
	this.exampleSocket.send(_payload);
	return this;
}
