#pragma once

#include <cpcapi2.h>

#include <memory>
#include <mutex>

namespace CPCAPI2
{
   namespace Agent
   {
      class PlaySoundFileStream : public CPCAPI2::PlaySoundStream {
      public:
         static const size_t kMaxFileNameSize = 1024;

         PlaySoundFileStream();
         virtual ~PlaySoundFileStream();

         bool Open() const;

         int OpenFile(const char* file_name_utf8,
            bool read_only,
            bool loop = false,
            bool text = false);

         int OpenFromFileHandle(FILE* handle,
            bool manage_file,
            bool read_only,
            bool loop = false);

         int CloseFile();
         int SetMaxFileSize(size_t bytes);
         int Flush();

         // PlaySoundStream
         virtual int Read(void* buf, size_t length) override;
         virtual int Rewind() override;

      private:
         int CloseFileImpl();
         int FlushImpl();

         std::unique_ptr<std::mutex> rw_lock_;

         FILE* id_;
         bool managed_file_handle_;
         bool open_;
         bool looping_;
         bool read_only_;
         size_t max_size_in_bytes_;  // -1 indicates file size limitation is off
         size_t size_in_bytes_;
         char file_name_utf8_[kMaxFileNameSize];

      };
   }
}
