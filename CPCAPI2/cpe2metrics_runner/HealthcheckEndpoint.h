#include <string>

// Simple-Web-Server
#include <server_http.hpp>

namespace CPCAPI2
{
namespace Agent
{

struct HealthcheckEndpointConfig
{
   std::string listenIpWithPort = "0.0.0.0:8081";
};

class HealthcheckEndpointHandler
{
public:
   virtual bool onHealthCheckRequest() = 0;
};


class HealthcheckEndpoint
{
public:
   HealthcheckEndpoint(const HealthcheckEndpointConfig& config);

   bool start(HealthcheckEndpointHandler* handler);
   void stop();

private:
   std::string goodHttpResponse() const;
   std::string badHttpResponse() const;

   bool splitIpPort(const std::string& ipPort, std::string& outIp, unsigned short& outPort) const;

private:
   HealthcheckEndpointConfig mConfig;

   typedef SimpleWeb::Server<SimpleWeb::HTTP> HttpServer;
   HttpServer mHealthCheckServer;
   std::thread mHealthCheckServerThread;
   HealthcheckEndpointHandler* mHandler = nullptr;
};

}
}