#if _WIN32
#include "stdafx.h"
#endif

#include "RegistrationScenario.h"
#include "ScenarioSettings.h"
#include "brand_branded.h"
#include "Logger.h"
#include "WatsonTTS.h"
#include "PlaySoundFileStream.h"
#include "Cpcapi2ServerLicense.h"
#include "SdkManager.h"
#include "cpcapi2_conf_buildinfo.h"
#include "LoglevelConverter.h"

#include <interface/experimental/account/SipAccountManagerInternal.h>
#include <interface/experimental/account/SipAccountJsonApi.h>
#include <interface/experimental/call/SipConversationJsonApi.h>
#include <interface/experimental/confbridge/ConferenceBridgeJsonApi.h>
#include <interface/experimental/confbridge/ConferenceBridgeInternal.h>
#include <interface/experimental/media/MediaManagerInternal.h>
#include <interface/experimental/peerconnection/PeerConnectionManagerInternal.h>
#include <interface/experimental/confbridge/ConferenceRegistrar.h>

#include "rutil/ConfigParse.hxx"

#include <prometheus/exposer.h>
#include <prometheus/registry.h>

#include <spdlog/spdlog.h>
#include <nlohmann/json.hpp>

// rapidjson
#include <writer.h>
#include <stringbuffer.h>
#include <document.h>
#include <prettywriter.h>

#define RegistrationScenario_TIMER_REREG 1
#define RegistrationScenario_TIMER_POST_REG_DELAY 2
#define RegistrationScenario_TIMER_CALL_DURATION 3

using namespace CPCAPI2;
using namespace CPCAPI2::Licensing;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::SipConversation;
using namespace CPCAPI2::Media;
using namespace CPCAPI2::PeerConnection;
using namespace CPCAPI2::ConferenceBridge;
using namespace prometheus;

namespace CPCAPI2
{
namespace Agent
{

static prometheus::Histogram::BucketBoundaries create_fixed_duration_buckets_reg() {
    // boundaries need to be sorted!
    auto bucket_boundaries = prometheus::Histogram::BucketBoundaries{0.005, 0.01, 0.02, 0.05, 0.1, 0.2, 0.3, 
                                                                     0.5,   1,    2,    5,    10,  30,  60};
    return bucket_boundaries;
}

RegistrationScenario::RegistrationScenario(
   const std::string& scenarioEnvPrefix, 
   const resip::Data& confConfigFilename,
   const cpc::string& overrideOutboundProxy,
   int sdkThreadPoolThreadIdx, 
   SdkManager* sdkMgr,
   resip::MultiReactor* appReactor, 
   CPCAPI2::Phone* masterSdkPhone, 
   CPCAPI2::Media::MediaTransportsReactorFactory* mediaReactorFactory)
   : mUserIdentity("counterpath"), 
   mResource("counterpath"),
   mScenarioEnvPrefix(scenarioEnvPrefix), 
   mConfConfigFilename(confConfigFilename),
   mOverrideOutboundProxy(overrideOutboundProxy),
   mSdkThreadPoolThreadIdx(sdkThreadPoolThreadIdx), 
   mSdkMgr(sdkMgr),
   mAppReactor(appReactor), 
   mTimer(*appReactor),
   mPhone(NULL), 
   mMedia(NULL),
   mSipAccount(NULL),
   mSipConversation(NULL),
   mMasterSdkPhone(masterSdkPhone), 
   mMediaReactorFactory(mediaReactorFactory), 
   mCallProbeEventCounterFamily(prometheus::BuildCounter()
      .Name("call_probe_scenario_count")
      .Help("The outcome (success/failure) of a scenario, including data on events observed by caller and callee.")
      .Register(*mSdkMgr->prometheusRegistry())),
   mCallProbeEventResponseTimeFamily(prometheus::BuildHistogram()
      .Name("call_probe_event_response_time")
      .Help("Histogram showing response times and status for various call-related events.")
      .Register(*mSdkMgr->prometheusRegistry())),      
   mSipAcct0Handle(-1),
   mTestIntervalSecs(120),
   mSipAcct0Status(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Unregistered)
{
   appInit();
}

RegistrationScenario::~RegistrationScenario()
{
   mAppReactor->detach();
}

CPCAPI2::PhoneInternal* RegistrationScenario::getPhoneInternalForLogger() const
{
   return mPhone;
}

void RegistrationScenario::shutdown(bool force)
{
   assert(mAppReactor->isCurrentThread());
   mTaskCompleteCb = [this](RunnerTask*)
   {
      assert(mAppReactor->isCurrentThread());
      mSipAccount->disable(mSipAcct0Handle);

      Phone::release(mPhone);
      mPhone = nullptr;

      delete this;
   };

   if (force)
   {
      mTaskCompleteCb(this);
   }
}

void RegistrationScenario::handleSdkCallback()
{
   mAppReactor->post(resip::resip_bind(&RegistrationScenario::handleSdkCallbackImpl, this));
}

void RegistrationScenario::handleSdkCallbackImpl()
{
   if (mPhone)
   {
      mPhone->process(CPCAPI2::Phone::kBlockingModeNonBlocking);
      mMedia->process(CPCAPI2::Phone::kBlockingModeNonBlocking);
   }
}

void sdkCallbackHookReg(void* context)
{
   RegistrationScenario* cpcRunner = (RegistrationScenario*)context;
   cpcRunner->handleSdkCallback();
}

std::string RegistrationScenario::sipAccountSettingsEnvVariablePrefix(int acct) const
{
   std::string sipAccountSettings0EnvVariablePrefix = mScenarioEnvPrefix + "_SIPACCOUNT_";
   sipAccountSettings0EnvVariablePrefix += resip::Data::from(acct).c_str();
   sipAccountSettings0EnvVariablePrefix += "_";
   return sipAccountSettings0EnvVariablePrefix;
}

void RegistrationScenario::stopTask()
{
   mAppReactor->post(resip::resip_bind(&RegistrationScenario::stopTaskImpl, this));
}

void RegistrationScenario::stopTaskImpl()
{
   mTaskCompleteCb = [](RunnerTask* rt) {};
}

void RegistrationScenario::startTask(const std::function<void(RunnerTask*)>& onCompleteCb)
{
   mAppReactor->post(resip::resip_bind(&RegistrationScenario::startTaskImpl, this, onCompleteCb));
}

void RegistrationScenario::startTaskImpl(const std::function<void(RunnerTask*)>& onCompleteCb)
{
   mTaskCompleteCb = onCompleteCb;

    if (!mSipAcct0Settings.username.empty())
    {
        mLastSipAccount0RequestOp = AccountOperation::Enable;
        mSipAccount->enable(mSipAcct0Handle);
    }
}

void RegistrationScenario::appInit()
{
   mLicensingMgr = LicensingClientManager::getInterface(mMasterSdkPhone);
   mPhone = CPCAPI2::PhoneInternal::create(mSdkThreadPoolThreadIdx);
   mPhone->setCallbackHook(sdkCallbackHookReg, this);
   mPhone->initialize(mLicensingMgr, this, false);

   // logging enabled in SdkManager.cpp which applies to all child phones
   //mPhone->setLoggingEnabled(this, true);

   mPhone->blockUntilRanOnSdkModuleThread([](void* context) 
   {
      std::stringstream ss;
      ss << std::this_thread::get_id();

      RegistrationScenario* self = reinterpret_cast<RegistrationScenario*>(context);
      self->mPhoneMainThreadId = ss.str();

   }, this);

   InfoLog(<< "RegistrationScenario::thread: cpcapi2 rtp proxy phone instance initialized.");
   CPCAPI2::Media::MediaManagerInternal::getInterface(mPhone, mMediaReactorFactory);

   CPCAPI2::Media::MediaStackSettings mediaSettings;
   mediaSettings.audioLayer = CPCAPI2::Media::AudioLayers_File;
   mediaSettings.audioOutputDisabled = true;
   mediaSettings.numAudioEncoderThreads = 0;
   mMedia = MediaManager::getInterface(mPhone);
   if (!mMedia)
   {
      InfoLog(<< "RegistrationScenario::thread: MediaManager interface not created successfully");
   }
   dynamic_cast<MediaManagerInternal*>(mMedia)->setCallbackHook(sdkCallbackHookReg, this);
   AudioExt* audioExt = AudioExt::getInterface(mMedia);
   if (!audioExt)
   {
      InfoLog(<< "RegistrationScenario::thread: AudioExt interface not created successfully");
   }
   audioExt->setAudioDeviceFile("", "");
   mMedia->initializeMediaStack(mediaSettings);

   mSipAccount = CPCAPI2::SipAccount::SipAccountManager::getInterface(mPhone);
   mSipConversation = CPCAPI2::SipConversation::SipConversationManager::getInterface(mPhone);

   InfoLog(<< "RegistrationScenario::thread: registration scenario initialized");

   std::string testIntervalEnvName = mScenarioEnvPrefix + "_TEST_INTERVAL_SEC";
   std::string dialStringEnvName = mScenarioEnvPrefix + "_DIAL_STRING";

   if (const char* testIntervalSec = std::getenv(testIntervalEnvName.c_str()))
   {
      mTestIntervalSecs = atoi(testIntervalSec);
   }
   else
   {
      mTestIntervalSecs = 20;
      ErrLog(<< "No value for " << testIntervalEnvName << " supplied; defaulting to " << mTestIntervalSecs << " sec");
   }

   std::string testLengthEnvName = mScenarioEnvPrefix + "_TEST_LENGTH_SEC";
   if (const char* testLengthSec = std::getenv(testLengthEnvName.c_str()))
   {
      mTestLengthSec = atoi(testLengthSec);
   }
   else
   {
      mTestLengthSec = 10;
   }

   initAccountSettings(sipAccountSettingsEnvVariablePrefix(0), mSipAcct0Handle, mSipAcct0Settings);
}


bool RegistrationScenario::initAccountSettings(const std::string& sipAccountSettingsEnvVarPrefix, 
                                        CPCAPI2::SipAccount::SipAccountHandle& sipAccountHandle, 
                                        SipAccountSettings& sipAccountSettings)
{
   // CPE centric defaults
   sipAccountSettings.useRport = false;
   sipAccountSettings.sipTransportType = SipAccountTransport_TCP;

   ScenarioSettings::parseSettings(sipAccountSettingsEnvVarPrefix, sipAccountSettings);

   // handle all registration failures the same way; future attempts will be scheduled by this
   // app when the account goes to state Status_Unregistered
   sipAccountSettings.reRegisterOnResponseTypes.clear();

   if (sipAccountSettings.username.empty())
   {
      return false;
   }

   if (!mOverrideOutboundProxy.empty())
   {
      sipAccountSettings.outboundProxy = mOverrideOutboundProxy;
   }

   sipAccountHandle = mSipAccount->create();
   mSipAccount->setHandler(sipAccountHandle, this);
   mSipAccount->configureDefaultAccountSettings(sipAccountHandle, sipAccountSettings);
   mSipAccount->applySettings(sipAccountHandle);

   mSipConversation->setHandler(sipAccountHandle, this);

   mSipMwi = SipMessageWaitingIndication::SipMessageWaitingIndicationManager::getInterface(mPhone);
   mSipMwi->setHandler(sipAccountHandle, this);

   return true;
}

void RegistrationScenario::setLoggingEnabled(bool enable)
{

}

resip::Data RegistrationScenario::getContext() const
{
   resip::Data contextStr;
   return contextStr;
}

void RegistrationScenario::onTimer(unsigned short timerId, void* appState)
{
    if (timerId == RegistrationScenario_TIMER_REREG)
    {
        mTaskCompleteCb(this);
    }
    else if (timerId == RegistrationScenario_TIMER_POST_REG_DELAY)
    {
        DebugLog(<< "Invoking disable from RegistrationScenario_TIMER_POST_REG_DELAY firing");
        mLastSipAccount0RequestOp = AccountOperation::Disable;
        mSipAccount->disable(mSipAcct0Handle);
    }
}

int RegistrationScenario::onError(const cpc::string& sourceModule, const CPCAPI2::PhoneErrorEvent& args)
{
   return 0;
}

bool RegistrationScenario::operator()(const PhoneLogEvent2& logEvent)
{
   // logging handled in SdkManager.cpp which applies to all child phones
   return true;
}

int RegistrationScenario::onLicensingError(const CPCAPI2::LicensingErrorEvent& args)
{
   return 0;
}

int RegistrationScenario::onValidateLicensesSuccess(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ValidateLicensesSuccessEvent& args)
{
   return 0;
}
int RegistrationScenario::onValidateLicensesFailure(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ValidateLicensesFailureEvent& args)
{
   return 0;
}
int RegistrationScenario::onError(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ErrorEvent& args)
{
   return 0;
}

///////////////////////////////////////////////////////////////////////////////
// SipAccountHandler
int RegistrationScenario::onAccountStatusChanged(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountStatusChangedEvent& args)
{
   DebugLog(<< "onAccountStatusChanged: " << account << ", responseTimeMs: " << args.responseTimeMs);
   if (account == mSipAcct0Handle)
   {
      mSipAcct0Status = args.accountStatus;
   }

   if ((account == mSipAcct0Handle && mLastSipAccount0RequestOp == AccountOperation::Enable)) // for now ignore failures on account disable
   {
      if (args.signalingStatusCode == 200)
      {
         if (args.accountStatus == SipAccountStatusChangedEvent::Status_Registered)
         {
            auto& counter = mCallProbeEventCounterFamily.Add({{"role", "device"}, 
                                                   {"target", sipAcct0OutboundProxy()},
                                                   {"outcome", "success"},
                                                   {"scenario", "registration"}});
            counter.Increment();
         }
      }
      else if (args.signalingStatusCode != 0)
      {
         std::string errorStr = "REGISTER/";
         errorStr += resip::Data::from(args.signalingStatusCode).c_str();

         auto& counter = mCallProbeEventCounterFamily.Add({{"role", "device"}, 
                                                {"target", sipAcct0OutboundProxy()},
                                                {"outcome", "failure"},
                                                {"error", errorStr.c_str()},
                                                {"scenario", "registration"}});
         counter.Increment();
      }

      if (args.signalingStatusCode != 0)
      {
         auto& counter = mCallProbeEventResponseTimeFamily.Add({{"role", "device"},
                                                                  {"event", (args.accountStatus == SipAccountStatusChangedEvent::Status_Registered ? "register" : "unregister")},
                                                                  {"target", sipAcct0OutboundProxy()},
                                                                  {"code", resip::Data::from(args.signalingStatusCode).c_str()},
                                                                  {"scenario", "registration"}}, create_fixed_duration_buckets_reg());
         float responseTimeSec = (float)args.responseTimeMs / 1000.0f;
         counter.Observe(responseTimeSec);
      }
   }

   if (args.accountStatus == SipAccountStatusChangedEvent::Status_Registered) {
      if (account == mSipAcct0Handle)
      {
         mTimer.cancel();
         mTimer.expires_from_now(mTestLengthSec*1000);
         DebugLog(<< "Queueing RegistrationScenario_TIMER_POST_REG_DELAY");
         mTimer.async_wait(this, RegistrationScenario_TIMER_POST_REG_DELAY, NULL);
      }
   }
   else if (args.accountStatus == SipAccountStatusChangedEvent::Status_WaitingToRegister) 
   {
      // need to handle this scenario until SCORE-1448 is resolved
   
      mLastSipAccount0RequestOp = AccountOperation::Disable;
      mSipAccount->disable(mSipAcct0Handle);
   }
   else if (args.accountStatus == SipAccountStatusChangedEvent::Status_Unregistered)
   {
      if (account == mSipAcct0Handle && mLastSipAccount0RequestOp == AccountOperation::Disable)
      {
         // The happy path
         mTimer.cancel();
         mTimer.expires_from_now(mTestIntervalSecs*1000);
         mTimer.async_wait(this, RegistrationScenario_TIMER_REREG, NULL);
      }
      else if (account == mSipAcct0Handle && mLastSipAccount0RequestOp == AccountOperation::Enable)
      {
         // Our last account operation was an enable, but the account went to an unregistered state.
         // We need to set a timer for the next account enable attempt, otherwise no future registrations will occur.

         mTimer.cancel();
         mTimer.expires_from_now(mTestLengthSec*1000);
         DebugLog(<< "Queueing RegistrationScenario_TIMER_POST_REG_DELAY");
         mTimer.async_wait(this, RegistrationScenario_TIMER_POST_REG_DELAY, NULL);
      }
   }
   return 0;
}

int RegistrationScenario::onError(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::ErrorEvent& args)
{
   return 0;
}


}
}
