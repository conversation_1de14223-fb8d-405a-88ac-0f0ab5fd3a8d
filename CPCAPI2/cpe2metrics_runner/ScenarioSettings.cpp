#include "ScenarioSettings.h"

using namespace CPCAPI2;
using namespace CPCAPI2::SipAccount;

namespace CPCAPI2
{
namespace Agent
{
void ScenarioSettings::parseSettings(const std::string& envPrefix, CPCAPI2::SipAccount::SipAccountSettings& settings) {

   auto getenvStr = [](const std::string& name) -> std::optional<cpc::string> 
   {
      if (const char* value = std::getenv(name.c_str()))
      {
         return cpc::string(value);
      }
      else
      {
         return std::nullopt;
      }
   };
   auto getenvUInt = [](const std::string& name) -> std::optional<unsigned int> 
   {
      if (const char* value = std::getenv(name.c_str()))
      {
      return std::stoi(value);
      }
      else
      {
      return std::nullopt;
      }
   };
   auto getenvInt = [](const std::string& name) -> std::optional<int> {
      if (const char* value = std::getenv(name.c_str()))
      {
         return std::stoi(value);
      }
      else
      {
         return std::nullopt;
      }
   };
   auto getenvBool = [](const std::string& name) -> std::optional<bool> {
      if (const char* value = std::getenv(name.c_str()))
      {
         return static_cast<bool>(std::stoi(value));
      }
      else
      {
         return std::nullopt;
      }
   };
   auto getenvVector = [&](const std::string& name) -> std::optional<cpc::vector<cpc::string>> {
      cpc::vector<cpc::string> vec;
      
      if (const char* value = std::getenv(name.c_str()))
      {
         std::string tmp;
         std::stringstream ss(value);

         while(std::getline(ss, tmp, ','))
         {
            vec.push_back(tmp.c_str());
         }
      }

      return vec;
  };
#define TRY_ASSIGN_ENV_VALUE(name) {\
  auto value = getenvStr(envPrefix + #name); \
  if (value) settings.name = value.value();\
}
#define TRY_ASSIGN_ENV_VALUE_UINT(name) {\
  auto value = getenvUInt(envPrefix + #name); \
  if (value) settings.name = value.value(); \
}
#define TRY_ASSIGN_ENV_VALUE_INT(name) {\
  auto value = getenvInt(envPrefix + #name); \
  if (value) settings.name = value.value(); \
}
#define TRY_ASSIGN_ENV_VALUE_BOOL(name) {\
  auto value = getenvBool(envPrefix + #name); \
  if (value) settings.name = value.value(); \
}
#define TRY_ASSIGN_ENV_VALUE_ENUM(name, EnumType) {\
  auto value = getenvInt(envPrefix + #name); \
  if (value) settings.name = static_cast<EnumType>(value.value()); \
}
#define TRY_ASSIGN_ENV_VALUE_VECTOR(name) {\
  auto value = getenvVector(envPrefix + #name);\
  if (value) settings.name = value.value(); \
}
  TRY_ASSIGN_ENV_VALUE(username);
  TRY_ASSIGN_ENV_VALUE(domain);
  TRY_ASSIGN_ENV_VALUE(password);
  TRY_ASSIGN_ENV_VALUE(displayName);
  TRY_ASSIGN_ENV_VALUE(auth_username);
  TRY_ASSIGN_ENV_VALUE(auth_realm);
  TRY_ASSIGN_ENV_VALUE_BOOL(useRegistrar);
  TRY_ASSIGN_ENV_VALUE(outboundProxy);
  TRY_ASSIGN_ENV_VALUE_BOOL(alwaysRouteViaOutboundProxy);
  TRY_ASSIGN_ENV_VALUE_UINT(registrationIntervalSeconds);
  TRY_ASSIGN_ENV_VALUE_UINT(minimumRegistrationIntervalSeconds);
  TRY_ASSIGN_ENV_VALUE_UINT(maximumRegistrationIntervalSeconds);
  TRY_ASSIGN_ENV_VALUE_BOOL(useRport);
  TRY_ASSIGN_ENV_VALUE_UINT(sipTransportType);
  TRY_ASSIGN_ENV_VALUE_BOOL(excludeEncryptedTransports);
  TRY_ASSIGN_ENV_VALUE(userAgent);
  TRY_ASSIGN_ENV_VALUE_UINT(udpKeepAliveTime);
  TRY_ASSIGN_ENV_VALUE_UINT(tcpKeepAliveTime);
  TRY_ASSIGN_ENV_VALUE_BOOL(useOutbound);
  TRY_ASSIGN_ENV_VALUE_BOOL(useGruu);
  TRY_ASSIGN_ENV_VALUE_VECTOR(nameServers);
  TRY_ASSIGN_ENV_VALUE_VECTOR(additionalNameServers);
  TRY_ASSIGN_ENV_VALUE_BOOL(ignoreCertVerification);
  TRY_ASSIGN_ENV_VALUE_VECTOR(additionalCertPeerNames);
  TRY_ASSIGN_ENV_VALUE_VECTOR(acceptedCertPublicKeys);
  TRY_ASSIGN_ENV_VALUE_VECTOR(requiredCertPublicKeys);
  TRY_ASSIGN_ENV_VALUE_INT(minSipPort);
  TRY_ASSIGN_ENV_VALUE_INT(maxSipPort);
  TRY_ASSIGN_ENV_VALUE_INT(defaultSipPort);
  TRY_ASSIGN_ENV_VALUE_INT(defaultSipsPort);
  TRY_ASSIGN_ENV_VALUE_BOOL(useInstanceId);
  TRY_ASSIGN_ENV_VALUE_ENUM(ipVersion, IpVersion);
  TRY_ASSIGN_ENV_VALUE_ENUM(sslVersion, SSLVersion);
  TRY_ASSIGN_ENV_VALUE(cipherSuite);
  //TRY_ASSIGN_ENV_VALUE_VECTOR(reRegisterOnResponseTypes);
  TRY_ASSIGN_ENV_VALUE(sourceAddress);
  TRY_ASSIGN_ENV_VALUE_BOOL(autoRetryOnTransportDisconnect);
  TRY_ASSIGN_ENV_VALUE_ENUM(keepAliveMode, KeepAliveMode);
  TRY_ASSIGN_ENV_VALUE_BOOL(useRinstance);
  TRY_ASSIGN_ENV_VALUE_BOOL(enableNat64Support);
  TRY_ASSIGN_ENV_VALUE_BOOL(useOptionsPing);
  TRY_ASSIGN_ENV_VALUE_UINT(optionsPingInterval);
  TRY_ASSIGN_ENV_VALUE_UINT(overrideMsecsTimerF);
}
}
}
