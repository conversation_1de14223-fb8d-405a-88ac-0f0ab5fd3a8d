#!/bin/bash

export CPE_SCENARIO_0_NAME=registration
export CPE_SCENARIO_0_TEST_INTERVAL_SEC=5
export CPE_SCENARIO_0_TEST_LENGTH_SEC=5
export CPE_SCENARIO_0_SIPACCOUNT_0_username=300
export CPE_SCENARIO_0_SIPACCOUNT_0_domain=metricsrunner.dev
export CPE_SCENARIO_0_SIPACCOUNT_0_outboundProxy=127.0.0.1:50010
export CPE_SCENARIO_0_SIPACCOUNT_0_sipTransportType=2
export CPE_SCENARIO_0_SIPACCOUNT_0_overrideMsecsTimerF=5000
export PROMETHEUS_LISTEN_ENDPOINT=127.0.0.1:8090

rm -rf logs/*

./run.sh &> logs/sipp.log &

./bin/cpe2metrics_runner nofork &> logs/cpe2metrics_runner.log &

WAIT_TIME_SEC=60

echo "Runner started; waiting $WAIT_TIME_SEC seconds"

sleep $WAIT_TIME_SEC
curl http://127.0.0.1:8090/metrics
pkill cpe2_metricsrunner
pkill sipp

echo "cpe2metrics_runner exited"
