FROM centos:7

WORKDIR /cpe2_metricsrunner_tests

# Add EPEL repository for newer software versions
RUN yum install -y epel-release
# Basic build requirements

#Installing a higher version of the compiler
RUN yum install -y wget pv python3
RUN yum install -y centos-release-scl
RUN yum install -y sipp
RUN yum install -y libX11 libXext 

#WORKDIR /cpe2_metricsrunner_tests

COPY dockerfile.run.centos-entrypoint.sh /cpe2_metricsrunner_tests

ENTRYPOINT ["sh", "-c", "ls && echo pwd is && pwd && ./Docker/dockerfile.run.centos-entrypoint.sh \"$@\"", "--"]
#ENTRYPOINT ["sh", "-c", "ls && pwd", "--"]
