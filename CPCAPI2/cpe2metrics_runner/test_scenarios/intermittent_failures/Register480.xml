<?xml version="1.0" encoding="UTF-8" ?>
<scenario name="Basic UAS scenario">
 
 <recv request="REGISTER"></recv>
 <!-- expect tool adds this 480 to metrics -->
 <send>
  <![CDATA[
  SIP/2.0 480 Server Error
  [last_Via:]
  [last_From:]
  [last_To:];tag=[pid]SIPpTag01[call_number]
  [last_Call-ID:]
  [last_CSeq:]
  [last_Contact:]
  Allow: INVITE, ACK, BYE, CANCEL, OPTIONS, MESSAGE, INFO, UPDATE, REGISTER, REFER, NOTIFY, PUBLISH, SUBSCRIBE
  Supported: path, replaces
  User-Agent: FreeSWITCH
  Expires: 3600
  Content-Length: 0
  ]]>
 </send>

</scenario>
