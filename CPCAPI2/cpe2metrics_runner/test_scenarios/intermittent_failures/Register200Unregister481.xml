<?xml version="1.0" encoding="UTF-8" ?>
<scenario name="Basic UAS scenario">

 <label id="1"/>
 <recv request="REGISTER"></recv>
 <send>
  <![CDATA[
  SIP/2.0 401 Unauthorized
  [last_Via:]
  [last_From:]
  [last_To:];tag=[pid]SIPpTag01[call_number]
  [last_Call-ID:]
  [last_CSeq:]
  Allow: INVITE, ACK, BYE, CANCEL, OPTIONS, MESSAGE, INFO, UPDATE, REGISTER, REFER, NOTIFY, PUBLISH, SUBSCRIBE
  Supported: path, replaces
  User-Agent: FreeSWITCH
  WWW-Authenticate: Digest realm="irak.com.bd", nonce="d3295b7c-af3d-11ec-bb02-91a2d44546e1", stale=true, algorithm=MD5, qop="auth"
  Content-Length: 0
  ]]>
 </send>

 <recv request="REGISTER"></recv>
 <send>
  <![CDATA[
  SIP/2.0 200 OK
  [last_Via:]
  [last_From:]
  [last_To:];tag=[pid]SIPpTag01[call_number]
  [last_Call-ID:]
  [last_CSeq:]
  [last_Contact:]
  Allow: INVITE, ACK, BYE, CANCEL, OPTIONS, MESSAGE, INFO, UPDATE, REGISTER, REFER, NOTIFY, PUBLISH, SUBSCRIBE
  Supported: path, replaces
  User-Agent: FreeSWITCH
  Expires: 3600
  Content-Length: 0
  ]]>
 </send>

 <!-- expect un-register -->
 <recv request="REGISTER"></recv>
 <!-- expect tool ignores un-register error -->
 <send>
  <![CDATA[
  SIP/2.0 481 Registration not found
  [last_Via:]
  [last_From:]
  [last_To:];tag=[pid]SIPpTag01[call_number]
  [last_Call-ID:]
  [last_CSeq:]
  Allow: INVITE, ACK, BYE, CANCEL, OPTIONS, MESSAGE, INFO, UPDATE, REGISTER, REFER, NOTIFY, PUBLISH, SUBSCRIBE
  Supported: path, replaces
  User-Agent: FreeSWITCH
  Content-Length: 0
  ]]>
 </send>

</scenario>
