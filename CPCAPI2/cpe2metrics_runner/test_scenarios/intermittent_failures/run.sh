#!/bin/sh

EXPECTED_REGISTER_200_OK_COUNT=0
EXPECTED_REGISTER_480_COUNT=0
EXPECTED_REGISTER_408_COUNT=0

set -e

ITERATION_COUNT=0
while :
do
   echo "Iteration $ITERATION_COUNT started"

   sipp -i 127.0.0.1 -mi 127.0.0.1 127.0.0.1:55060 -p 50010 -mp 50020 -r 1 -l 1 -timeout 120 -m 1 -s alice -sf ./Register200Unregister481.xml &> logs/sipp.Register200Unregister481.log
   ((EXPECTED_REGISTER_200_OK_COUNT=EXPECTED_REGISTER_200_OK_COUNT+1)) 

   echo "============> Metrics should report REGISTER/408:  $EXPECTED_REGISTER_408_COUNT\n"
   echo "============>                       REGISTER/480:  $EXPECTED_REGISTER_480_COUNT\n"
   echo "============>                       REGISTER/200:  $EXPECTED_REGISTER_200_OK_COUNT\n"

   sipp -i 127.0.0.1 -mi 127.0.0.1 127.0.0.1:55060 -p 50010 -mp 50020 -r 1 -l 1 -timeout 120 -m 1 -s alice -sf ./Register480.xml &> logs/sipp.Register480.log
   ((EXPECTED_REGISTER_480_COUNT=EXPECTED_REGISTER_480_COUNT+1))
   
   echo "============> Metrics should report REGISTER/408:  $EXPECTED_REGISTER_408_COUNT\n"
   echo "============>                       REGISTER/480:  $EXPECTED_REGISTER_480_COUNT\n"
   echo "============>                       REGISTER/200:  $EXPECTED_REGISTER_200_OK_COUNT\n"

   sipp -i 127.0.0.1 -mi 127.0.0.1 127.0.0.1:55060 -p 50010 -mp 50020 -r 1 -l 1 -timeout 120 -m 1 -s alice -sf ./Register408.xml &> logs/sipp.Register408.log
   ((EXPECTED_REGISTER_408_COUNT=EXPECTED_REGISTER_408_COUNT+1))
   
   echo "============> Metrics should report REGISTER/408:  $EXPECTED_REGISTER_408_COUNT\n"
   echo "============>                       REGISTER/480:  $EXPECTED_REGISTER_480_COUNT\n"
   echo "============>                       REGISTER/200:  $EXPECTED_REGISTER_200_OK_COUNT\n"

   sipp -i 127.0.0.1 -mi 127.0.0.1 127.0.0.1:55060 -p 50010 -mp 50020 -r 1 -l 1 -timeout 120 -m 1 -s alice -sf ./RegisterNoResponse.xml &> logs/sipp.RegisterNoResponse.log
   ((EXPECTED_REGISTER_408_COUNT=EXPECTED_REGISTER_408_COUNT+1))
   
   echo "============> Metrics should report REGISTER/408:  $EXPECTED_REGISTER_408_COUNT\n"
   echo "============>                       REGISTER/480:  $EXPECTED_REGISTER_480_COUNT\n"
   echo "============>                       REGISTER/200:  $EXPECTED_REGISTER_200_OK_COUNT\n"

   echo "============> Iteration $ITERATION_COUNT finished"
   sleep 5

   ((ITERATION_COUNT=ITERATION_COUNT+1))
done
