#pragma once

#include <cpcapi2.h>
#include <jsonapi/JsonApiServer.h>
#include <jsonapi/JsonApiServerHandler.h>
#include <jsonapi/HttpServerInternal.h>
#include <phone/PhoneInternal.h>
#include <orchestration_server/OrchestrationServer.h>
#include <orchestration_server/OrchestrationServerHandler.h>
#include <media/MediaManagerInternal.h>
#include <auth_server/AuthServer.h>
#include <auth_server/AuthServerHandler.h>
#include <confbridge/ConferenceRegistrarHandler.h>

// rutil includes
#include <rutil/MultiReactor.hxx>
#include <rutil/Data.hxx>
#include <rutil/Mutex.hxx>
#include <rutil/DeadlineTimer.hxx>

#include <prometheus/counter.h>
#include <prometheus/exposer.h>
#include <prometheus/registry.h>

#include <set>
#include <atomic>
#include <queue>

#include "HealthcheckEndpoint.h"

namespace CPCAPI2
{
namespace Utils
{
class DnsClient;
}
namespace ConferenceBridge
{
class ConferenceRegistrar;
}
namespace Agent
{
class Cpcapi2Runner;
class RunnerTask;

class SdkManager : public CPCAPI2::PhoneHandler,
                   public CPCAPI2::PhoneLogger2,
                   public CPCAPI2::Licensing::LicensingClientHandler,
                   public CPCAPI2::OrchestrationServer::OrchestrationServerHandler,
                   public CPCAPI2::JsonApi::JsonApiServerHandler,
                   public CPCAPI2::AuthServer::AuthServerHandler,
                   public CPCAPI2::ConferenceBridge::ConferenceRegistrarHandler,
                   public resip::DeadlineTimerHandler,
                   public HealthcheckEndpointHandler
{
public:
   struct StartupConfig
   {
      bool logging = true;
   };

   SdkManager(StartupConfig& startupConfig);
   ~SdkManager();

   void run();
   void shutdown();
   void join();
   int nextTicketId();
   void postAsyncResult(int ticketId, const cpc::string& res);
   void recheckDns();

   void handleSdkCallback();

   // PhoneHandler
   virtual int onError(const cpc::string& sourceModule, const CPCAPI2::PhoneErrorEvent& args);
   virtual int onLicensingError(const CPCAPI2::LicensingErrorEvent& args);
   virtual int onLicensingSuccess() {
      return 0;
   }

   // PhoneLogger2
   bool operator()(const PhoneLogEvent2& logEvent);

   // LicensingClientHandler
   virtual int onValidateLicensesSuccess(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ValidateLicensesSuccessEvent& args);
   virtual int onValidateLicensesFailure(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ValidateLicensesFailureEvent& args);
   virtual int onError(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ErrorEvent& args);

   // JsonApiServerHandler
   int onNewLogin(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiuser, const CPCAPI2::JsonApi::NewLoginEvent& args) override;
   int onLogout(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const CPCAPI2::JsonApi::LogoutEvent& args) override { return 0; }
   int onConnectionClosed(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const CPCAPI2::JsonApi::ConnectionClosedEvent& args) override;

   // OrchestrationServerHandler
   virtual int onSetServerInfoResult(int requestHandle, const CPCAPI2::OrchestrationServer::SetServerInfoResult& args);
   virtual int onRequestServiceResult(int requestHandle, const CPCAPI2::OrchestrationServer::RequestServiceResult& args) { return 0; }
   virtual int onQueryServersResult(int requestHandle, const CPCAPI2::OrchestrationServer::QueryServersResult& args) { return 0; }
   virtual int onQueryServerTtlResult(int requestHandle, const CPCAPI2::OrchestrationServer::QueryServerTtlResult& args) { return 0; }
   virtual int onQueryServerUsersResult(int requestHandle, const CPCAPI2::OrchestrationServer::QueryServerUsersResult& args) { return 0; }

   // ConferenceRegistrarHandler
   virtual int onStartupComplete(CPCAPI2::ConferenceBridge::ConferenceRegistrarHandle registrar, const CPCAPI2::ConferenceBridge::ConferenceRegistrarStartupResult& args);
   virtual int onAddFollowerComplete(CPCAPI2::ConferenceBridge::ConferenceRegistrarHandle registrar, const CPCAPI2::ConferenceBridge::ConferenceRegistrarAddFollowerResult& args) {
      return 0;
   }

   // HealthcheckEndpointHandler
   virtual bool onHealthCheckRequest() override;
   virtual void onHealthCheckRequestImpl(std::atomic_bool* healthy);

   // DeadlineTimerHandler
   void onTimer(unsigned short timerId, void* appState) override;

   static void onPrivilegedAccessCompleted(void* context);
   void onPrivilegedAccessCompletedImpl();

   void setLoggingTimer(int expires_ms);
   void writeSessionSummary();

   std::shared_ptr<prometheus::Registry>& prometheusRegistry() {
      return mPrometheusRegistry;
   }

   bool exitProcess() const { return mExitProcess; }

private:
   void appInit();
   void initConf();
   void appShutdown();
   void handleSdkCallbackImpl();

   void initFromSettings();
   void sigtermHandler(int signum);

   void listNodes(const std::function<void(const cpc::vector<cpc::string>&)>& fn);

   void writeSessionSummaryImpl();
   void writeConnectedEndpointSummary();

   void setLoggingTimerImpl(int expires_ms);

   CPCAPI2::PhoneInternal* getPhoneInternalForLogger() const
   {
      return mPhone;
   }

   void runnerTaskCompleteCb(RunnerTask* runner);

   void initReg(const std::string& envScenarioBase);
   void initOnToOn(const std::string& envScenarioBase);

private:
   resip::MultiReactor mReactor;
   CPCAPI2::PhoneInternal* mPhone;
   CPCAPI2::Licensing::LicensingClientManager* mLicensingMgr;
   CPCAPI2::AuthServer::AuthServer* mAuthServer;
   CPCAPI2::JsonApi::JsonApiServer* mJsonApiServer;
   CPCAPI2::ConferenceBridge::ConferenceRegistrar* mConfRegistrar;
   CPCAPI2::Media::MediaTransportsReactorFactory* mMediaReactorFactory;
   std::map<resip::Data, RunnerTask*> mMapContextToRunner;
   std::map<CPCAPI2::JsonApi::JsonApiUserHandle, Cpcapi2Runner*> mMapJsonApiUserToRunner;
   static std::atomic_int sNextSdkThreadPoolIdx;
   CPCAPI2::OrchestrationServer::OrchestrationServer* mOrchServer;
   CPCAPI2::ConferenceBridge::ConferenceNatTraversalServerInfo mNatServerInfo;
   CPCAPI2::ConferenceBridge::ConferenceBitrateConfig mScreenshareBitrateConfig;
   CPCAPI2::ConferenceBridge::ConferenceBitrateConfig mCameraVideoBitrateConfig;
   CPCAPI2::ConferenceBridge::ConferenceMediaEncryptionMode mMediaEncryptionMode;
   std::atomic_bool mMaintenanceModeEnabled;
   bool mUseServerUidInJoinUrls;
   bool mCreateRootConference;
   resip::Mutex mConnectedEndpointSummaryMtx;
   cpc::string mConnectedEndpointSummary;
   resip::Mutex mSessionSummaryMtx;
   cpc::string mSessionSummary;
   resip::DeadlineTimer<resip::MultiReactor> mLoggingTimer;
   bool mIsLoggingEnabled;
   std::unique_ptr<prometheus::Exposer> mPrometheusExposer;
   std::shared_ptr<prometheus::Registry> mPrometheusRegistry;
   std::unique_ptr<CPCAPI2::Utils::DnsClient> mDnsClient;
   std::atomic_bool mExitProcess = false;
   std::unique_ptr<HealthcheckEndpoint> mHealthCheckEndpoint;

   struct StaticLoglineItems
   {
      std::string app;
      std::string environmentName;
      std::string environmentType;
   };

   StaticLoglineItems mStaticLoglineItems;

   struct RunnerStaticLoglineItems
   {
      std::string scenarioName;
      std::string target;
   };

   std::map<std::string, RunnerStaticLoglineItems> mMapPhoneThreadIdToRunnerLoglineItems;
   std::mutex mMapPhoneThreadIdToRunnerLoglineItemsMutex;

   void setRunnerLoglineItems(const std::string& runnerPhoneThreadId, const RunnerStaticLoglineItems& items);
   std::optional<RunnerStaticLoglineItems> getRunnerLoglineItems(const std::string& runnerPhoneThreadId);


   struct ConnectedEndpointInfo
   {
      cpc::string userIdentity;
   };
   std::map<CPCAPI2::JsonApi::JsonApiUserHandle, ConnectedEndpointInfo> mMapJsonApiUserToEndpointInfo;

   class BuildInfo : public CPCAPI2::JsonApi::HttpServerRequestHandler
   {
   public:
      BuildInfo(SdkManager* sdkMgr);
      virtual cpc::string output() OVERRIDE;
      virtual int response_code() OVERRIDE;
      virtual bool requiresAuth() OVERRIDE {
         return false;
      }
   private:
      SdkManager* mSdkMgr;
   };

   class SessionSummaryRequestHandler : public CPCAPI2::JsonApi::HttpServerRequestHandler
   {
   public:
      SessionSummaryRequestHandler(SdkManager* sdkMgr);
      virtual cpc::string output() OVERRIDE;
      virtual bool requiresAuth() OVERRIDE {
         return false;
      }
   private:
      SdkManager* mSdkMgr;
   };

   class ConnectedEndpointSummaryRequestHandler : public CPCAPI2::JsonApi::HttpServerRequestHandler
   {
   public:
      ConnectedEndpointSummaryRequestHandler(SdkManager* sdkMgr);
      virtual cpc::string output() OVERRIDE;
      virtual bool requiresAuth() OVERRIDE {
         return false;
      }
   private:
      SdkManager* mSdkMgr;
   };

   class MaintMode : public CPCAPI2::JsonApi::HttpServerRequestHandler
   {
   public:
      MaintMode(SdkManager* sdkMgr);
      virtual cpc::string output() OVERRIDE;
      virtual bool requiresAuth() OVERRIDE {
         return false;
      }
   private:
      SdkManager* mSdkMgr;
   };

   class LoggingToggle : public CPCAPI2::JsonApi::HttpServerRequestHandler
   {
   public:
      LoggingToggle(SdkManager* sdkMgr, bool enable);
      virtual cpc::string output() OVERRIDE;
      virtual bool requiresAuth() OVERRIDE {
         return false;
      }
   private:
      SdkManager* mSdkMgr;
      bool mEnable;
   };

   class LoggingStatus : public CPCAPI2::JsonApi::HttpServerRequestHandler
   {
   public:
      LoggingStatus(SdkManager* sdkMgr);
      virtual cpc::string output() OVERRIDE;
      virtual bool requiresAuth() OVERRIDE {
         return false;
      }
   private:
      SdkManager* mSdkMgr;
   };

   class CrashRequest : public CPCAPI2::JsonApi::HttpServerRequestHandler
   {
   public:
      CrashRequest(SdkManager* sdkMgr);
      virtual cpc::string output() OVERRIDE;
   private:
      SdkManager* mSdkMgr;
   };

   class RaftListNodes : public CPCAPI2::JsonApi::HttpServerRequestHandler
   {
   public:
      RaftListNodes(SdkManager* sdkMgr);
      virtual int processRequest(const cpc::string& path, const cpc::string& body, const CPCAPI2::JsonApi::AuthTokenInfo& authTokenInfo, const cpc::vector<CPCAPI2::JsonApi::QueryStringParam>& queryString) OVERRIDE;
      virtual cpc::string output() OVERRIDE;
      virtual bool requiresAuth() OVERRIDE {
         return false;
      }
   private:
      SdkManager* mSdkMgr;
      int mTicketId = -1;
   };

   class RestAsyncResult : public CPCAPI2::JsonApi::HttpServerRequestHandler
   {
   public:
      RestAsyncResult(SdkManager* sdkMgr);
      virtual int processRequest(const cpc::string& path, const cpc::string& body, const CPCAPI2::JsonApi::AuthTokenInfo& authTokenInfo, const cpc::vector<CPCAPI2::JsonApi::QueryStringParam>& queryString) OVERRIDE;
      virtual cpc::string output() OVERRIDE;
      virtual bool requiresAuth() OVERRIDE {
         return false;
      }
      virtual int response_code() OVERRIDE;
      void setResult(int ticketId, const cpc::string& res) {
         mResultMap[ticketId] = res;
      }
      int nextTicketId() {
         return mNextTicketId++;
      }
      
   private:
      SdkManager* mSdkMgr;
      std::map<int, cpc::string> mResultMap;
      cpc::string mNextResult;
      int mNextTicketId = 1;
      int mRespCode = 404;
   };

   RestAsyncResult mRestAsyncResultHandler;
   BuildInfo mBuildInfo;
   SessionSummaryRequestHandler mSessionSummaryReqHandler;
   ConnectedEndpointSummaryRequestHandler mConnectedEndpointSummaryReqHandler;
   CrashRequest mCrashReqHandler;
   MaintMode mMaintMode;
   LoggingToggle mLoggingEnableHandler;
   LoggingToggle mLoggingDisableHandler;
   LoggingStatus mLoggingStatusReqHandler;
   RaftListNodes mRaftListNodes;
   StartupConfig mStartupConfig;

   int mTotalPrivilegedAccesses;

   bool mIsLeader;
   int mNodeId;
};

}
}
