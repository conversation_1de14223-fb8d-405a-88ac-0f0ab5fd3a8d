#pragma once

#include "RunnerTask.h"

// SDK includes
#include <cpcapi2.h>
#include <media/MediaManagerInternal.h>
#include <account/SipAccountManagerInternal.h>
#include <phone/PhoneInternal.h>

// rutil includes
#include <rutil/MultiReactor.hxx>
#include <rutil/Data.hxx>
#include <rutil/DeadlineTimer.hxx>

#include <prometheus/family.h>
#include <prometheus/histogram.h>
#include <prometheus/counter.h>

#include <mutex>
#include <functional>

namespace CPCAPI2
{
namespace Agent
{
class SdkManager;

class RegistrationScenario : public RunnerTask,
                      public CPCAPI2::PhoneHandler,
                      public CPCAPI2::PhoneLogger2,
                      public CPCAPI2::Licensing::LicensingClientHandler,
                      public CPCAPI2::SipAccount::SipAccountHandler,
                      public CPCAPI2::SipConversation::SipConversationHand<PERSON>,
                      public CPCAPI2::SipMessageWaitingIndication::SipMessageWaitingIndicationHandler,
                      public resip::DeadlineTimerHandler
{
public:
   RegistrationScenario(
      const std::string& scenarioEnvPrefix, 
      const resip::Data& confConfigFilename,
      const cpc::string& overrideOutboundProxy,
      int sdkThreadPoolThreadIdx, 
      SdkManager* sdkMgr,
      resip::MultiReactor* appReactor, 
      CPCAPI2::Phone* masterSdkPhone, 
      CPCAPI2::Media::MediaTransportsReactorFactory* mediaReactorFactory);
   virtual ~RegistrationScenario();

   resip::Data getContext() const;

   void handleSdkCallback();

   // RunnerTask
   virtual void startTask(const std::function<void(RunnerTask*)>& onCompleteCb);
   virtual void stopTask();
   virtual void shutdown(bool waitForActiveTask);
   virtual void updateAccountDomainsFromSRV(const std::set<resip::Data>& srvAccountDomains) {}

   CPCAPI2::Phone* getPhone() const {
      return mPhone;
   }

   std::string getPhoneMainThreadId() const {
      return mPhoneMainThreadId;
   }

   resip::Data getUserIdentity() const {
      return mUserIdentity;
   }

   std::string sipAcct0OutboundProxy() const {
      return mSipAcct0Settings.outboundProxy.c_str();
   }

   bool isValidResource(const cpc::vector<cpc::string>& requestedResources);

   // DeadlineTimerHandler
   void onTimer(unsigned short timerId, void* appState) override;

   // PhoneHandler
   virtual int onError(const cpc::string& sourceModule, const CPCAPI2::PhoneErrorEvent& args);
   virtual int onLicensingError(const CPCAPI2::LicensingErrorEvent& args);
   virtual int onLicensingSuccess() {
      return 0;
   }

   // PhoneLogger2
   bool operator()(const PhoneLogEvent2& logEvent);

   // LicensingClientHandler
   virtual int onValidateLicensesSuccess(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ValidateLicensesSuccessEvent& args);
   virtual int onValidateLicensesFailure(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ValidateLicensesFailureEvent& args);
   virtual int onError(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ErrorEvent& args);

   // SipAccountHandler
   virtual int onAccountStatusChanged(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountStatusChangedEvent& args);
   virtual int onError(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::ErrorEvent& args);

   // SipConversationHandler
   virtual int onNewConversation(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::NewConversationEvent& args) { return kSuccess; }
   virtual int onConversationEnded(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationEndedEvent& args) { return kSuccess; }
   virtual int onIncomingTransferRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::TransferRequestEvent& args) { return kSuccess; }
   virtual int onIncomingRedirectRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::RedirectRequestEvent& args) { return kSuccess; }
   virtual int onIncomingTargetChangeRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::TargetChangeRequestEvent& args) { return kSuccess; }
   virtual int onIncomingHangupRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::HangupRequestEvent& args) { return kSuccess; }
   virtual int onIncomingBroadsoftTalkRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::BroadsoftTalkEvent& args) { return kSuccess; }
   virtual int onIncomingBroadsoftHoldRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::BroadsoftHoldEvent& args) { return kSuccess; }
   virtual int onTransferProgress(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::TransferProgressEvent& args) { return kSuccess; }
   virtual int onConversationStateChangeRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationStateChangeRequestEvent& args) { return kSuccess; }
   virtual int onConversationStateChanged(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationStateChangedEvent& args) { return kSuccess; }
   virtual int onConversationMediaChangeRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationMediaChangeRequestEvent& args) { return kSuccess; }
   virtual int onConversationMediaChanged(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationMediaChangedEvent& args) { return kSuccess; }
   virtual int onConversationStatisticsUpdated(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationStatisticsUpdatedEvent& args) { return kSuccess; }
   virtual int onError(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ErrorEvent& args) { return kSuccess; }

   // SipMessageWaitingIndicationHandler
   virtual int onNewSubscription(SipEvent::SipEventSubscriptionHandle subscription, const SipMessageWaitingIndication::NewMWISubscriptionEvent& args) { return kSuccess; }
   virtual int onSubscriptionEnded(SipEvent::SipEventSubscriptionHandle subscription, const SipMessageWaitingIndication::MWISubscriptionEndedEvent& args) { return kSuccess; }
   virtual int onIncomingMWIStatus(SipEvent::SipEventSubscriptionHandle subscription, const SipMessageWaitingIndication::IncomingMWIStatusEvent& args) { return kSuccess; }
   virtual int onSubscriptionStateChanged(SipEvent::SipEventSubscriptionHandle subscription, const SipMessageWaitingIndication::MWISubscriptionStateChangedEvent& args) { return kSuccess; }
   virtual int onError(SipEvent::SipEventSubscriptionHandle subscription, const SipMessageWaitingIndication::ErrorEvent& args) { return kSuccess; }

   void setLoggingEnabled(bool enable);

private:
   void appInit();

   void handleSdkCallbackImpl();

   CPCAPI2::PhoneInternal* getPhoneInternalForLogger() const;
   bool initAccountSettings(const std::string& sipAccountSettingsEnvVarPrefix, CPCAPI2::SipAccount::SipAccountHandle& sipAccountHandle, SipAccount::SipAccountSettings& outSipAccountSettings);
   void initConfSettings();
   std::string getWebsockUrl() const;

   std::string sipAccountSettingsEnvVariablePrefix(int acct) const;
   void startTaskImpl(const std::function<void(RunnerTask*)>& onCompleteCb);
   void stopTaskImpl();

private:
   resip::Data mUserIdentity;
   resip::Data mResource;
   std::string mScenarioEnvPrefix;
   resip::Data mConfConfigFilename;
   cpc::string mOverrideOutboundProxy;
   int mSdkThreadPoolThreadIdx;
   SdkManager* mSdkMgr;
   resip::MultiReactor* mAppReactor;
   CPCAPI2::PhoneInternal* mPhone;
   std::string mPhoneMainThreadId;
   CPCAPI2::Phone* mMasterSdkPhone;
   CPCAPI2::Licensing::LicensingClientManager* mLicensingMgr;
   CPCAPI2::Media::MediaTransportsReactorFactory* mMediaReactorFactory;
   CPCAPI2::Media::MediaManager* mMedia;
   CPCAPI2::SipAccount::SipAccountManager* mSipAccount;
   CPCAPI2::SipConversation::SipConversationManager* mSipConversation;
   resip::DeadlineTimer<resip::MultiReactor> mTimer;
   SipMessageWaitingIndication::SipMessageWaitingIndicationManager* mSipMwi;
   std::function<void(RunnerTask*)> mTaskCompleteCb;

   bool mShutdown = false;

   enum class AccountOperation
   {
      None,
      Enable,
      Disable
   };

   AccountOperation mLastSipAccount0RequestOp = AccountOperation::None;
   CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status mSipAcct0Status;

   prometheus::Family<prometheus::Counter>& mCallProbeEventCounterFamily;
   prometheus::Family<prometheus::Histogram>& mCallProbeEventResponseTimeFamily;

   CPCAPI2::SipAccount::SipAccountHandle mSipAcct0Handle;
   int mTestIntervalSecs;
   std::string mTaskID;
   std::string mTargetGroup;

   CPCAPI2::SipAccount::SipAccountSettings mSipAcct0Settings;
   int mTestLengthSec;

};
}
}
