if [ -z "$BUILD_ARCH" ]
then
  # default to armv8 builds
  _BUILD_ARCH=armv8
else
  _BUILD_ARCH=$BUILD_ARCH
fi


BUILD_DIR=build_armv8
if [ "$_BUILD_ARCH" == "x86_64" ]; then
  BUILD_DIR=build_x86_64
fi


if [ ! -d $BUILD_DIR ]
then
  mkdir $BUILD_DIR
fi

# ccache not required to be installed on host; we just want to share cache files outside of the container
mkdir -p /home/<USER>/.ccache

printf "#pragma once\n#define BUILD_INFO \"Job=$JOB_BASE_NAME Revision=$SVN_REVISION_1 Build=$BUILD_NUMBER\"\n#define BUILD_STAMP \"$MAJOR_VER.$MINOR_VER.$PATCH_VER+$BUILD_NUMBER\"" > ../cpcapi2_conf_buildinfo.h

docker build -t cpcapi2_builder:ubuntu.clang -f ../../projects/docker_build/Dockerfile.ubuntu.clang ../../projects/docker_build
docker build -t cpcapi2_cpe2metrics_builder:ubuntu.clang -f Dockerfile.builder.ubuntu.clang .

docker run --name cpcapi2_cpe2metrics_builder --user $(id -u):$(id -g) -v /home/<USER>/.ccache:/.ccache -e CCACHE_DIR=/.ccache -e BUILD_ARCH=$_BUILD_ARCH -e BUILD_NUMBER=$BUILD_NUMBER --rm --mount type=bind,source="$(pwd)"/../../../,target=/CPCAPI2 --mount type=bind,source=$HOME/.conan/,target=/tmp/conan/.conan --env CPCAPI2_CONAN_USER --env CPCAPI2_CONAN_APIKEY cpcapi2_cpe2metrics_builder:ubuntu.clang -u $(whoami) "$@"

#docker buildx build -t cpcapi2_cpe2metrics_builder:ubuntu.clang -f Dockerfile.builder.amazonlinux --load --platform linux/arm64 .
#docker run --name cpcapi2_cpe2metrics_builder_amazonlinux --platform linux/arm64 -v /usr/bin/qemu-aarch64-static:/usr/bin/qemu-aarch64-static -v /home/<USER>/.ccache:/.ccache -e CCACHE_DIR=/.ccache -e BUILD_NUMBER=$BUILD_NUMBER --rm --mount type=bind,source="$(pwd)"/../../../,target=/CPCAPI2 --mount type=bind,source=$HOME/.conan/,target=/tmp/conan/.conan --env CPCAPI2_CONAN_USER --env CPCAPI2_CONAN_APIKEY cpcapi2_cpe2metrics_builder_amazonlinux:ubuntu.clang -u $(whoami) "$@"
