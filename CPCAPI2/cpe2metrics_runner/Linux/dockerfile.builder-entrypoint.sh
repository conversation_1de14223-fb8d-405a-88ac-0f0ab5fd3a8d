#!/bin/bash
set -e
NUMBER_OF_CPUS=`grep -c ^processor "/proc/cpuinfo"`
CMAKE=cmake
export CCACHE_DIR=/.ccache  # mount is setup by docker-build.sh
export CCACHE_BASEDIR=/CPCAPI2 # mount is setup by docker-build.sh
export CCACHE_MAXSIZE=20G 

uname -m

if command -v cmake3 >/dev/null 2>&1;
then
  CMAKE=cmake3
fi

CPCAPI2_TOOLCHAIN=../../../../core/projects/cmake/toolchains/linux-clang-armv8.cmake
BUILD_DIR=./build_armv8
OBJCOPY_BIN=aarch64-linux-gnu-objcopy
if [ "$BUILD_ARCH" == "x86_64" ]; then
  CPCAPI2_TOOLCHAIN=../../../../core/projects/cmake/toolchains/linux-clang-x86_64.cmake
  BUILD_DIR=./build_x86_64
  OBJCOPY_BIN=objcopy
fi

cd $BUILD_DIR

pushd .
cd ../../prometheus-cpp
./jenkins-build.sh
popd

echo "CPCAPI2_CONAN_USER is $CPCAPI2_CONAN_USER"

python3 ../../../tools/conan_detect.py


$CMAKE -G Ninja -DCMAKE_BUILD_TYPE=RelWithDebInfo -DCMAKE_TOOLCHAIN_FILE=$CPCAPI2_TOOLCHAIN -DCPCAPI2_CONAN=1 ../../

ninja

$OBJCOPY_BIN --only-keep-debug cpcapi2_conf cpcapi2_conf.debug
$OBJCOPY_BIN --strip-debug cpcapi2_conf cpcapi2_conf
$OBJCOPY_BIN --add-gnu-debuglink=cpcapi2_conf.debug cpcapi2_conf

echo Build Complete
echo Built$TARGETS_BUILT
