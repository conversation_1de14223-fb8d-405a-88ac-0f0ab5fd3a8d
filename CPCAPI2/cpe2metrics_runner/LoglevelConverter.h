#pragma once
#include <spdlog/spdlog.h>
#include <cpcapi2.h>

namespace CPCAPI2
{
namespace Agent
{
   class LoglevelConverter
   {
   public:
      static spdlog::level::level_enum cpclevel2spdlevel(CPCAPI2::LogLevel cpcLevel)
      {
         spdlog::level::level_enum spdlevel;
         switch (cpcLevel)
         {
            case LogLevel_None:
               spdlevel = spdlog::level::level_enum::off;
               break;
            case LogLevel_Error:
               spdlevel = spdlog::level::level_enum::err;
               break;
            case LogLevel_Warning:
               spdlevel = spdlog::level::level_enum::warn;
               break;
            case LogLevel_Info:
               spdlevel = spdlog::level::level_enum::info;
               break;
            case LogLevel_Debug:
               spdlevel = spdlog::level::level_enum::debug;
               break;
            case LogLevel_Max:
               spdlevel = spdlog::level::level_enum::trace;
               break;
         }

         return spdlevel;
      }

      static std::string cpclevel2javastr(CPCAPI2::LogLevel cpcLevel)
      {
         std::string levelString;
         switch (cpcLevel)
         {
            case LogLevel_None:
               break;
            case LogLevel_Error:
               levelString = "ERROR";
               break;
            case LogLevel_Warning:
               levelString = "WARN";
               break;
            case LogLevel_Info:
               levelString = "INFO";
               break;
            case LogLevel_Debug:
               levelString = "DEBUG";
               break;
            case LogLevel_Max:
               levelString = "TRACE";
               break;
         }

         return levelString;
      }

      static spdlog::level::level_enum javastr2spdlvl(const std::string& javalevel)
      {
         spdlog::level::level_enum spdlevel;
         if (javalevel == "ERROR")
         {
            spdlevel = spdlog::level::level_enum::err;
         }
         else if (javalevel == "WARN")
         {
            spdlevel = spdlog::level::level_enum::warn;
         }
         else if (javalevel == "INFO")
         {
            spdlevel = spdlog::level::level_enum::info;
         }
         else if (javalevel == "DEBUG")
         {
            spdlevel = spdlog::level::level_enum::debug;
         }
         else if (javalevel == "TRACE")
         {
            spdlevel = spdlog::level::level_enum::trace;
         }
         else
         {
            spdlevel = spdlevel = spdlog::level::level_enum::debug;
         }

         return spdlevel;
      }
   };

}
}