{"configurations": [{"name": "<PERSON>", "includePath": ["${workspaceFolder}/**"], "defines": [], "macFrameworkPath": ["/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks"], "compilerPath": "/usr/bin/clang", "cStandard": "c17", "cppStandard": "c++98", "intelliSenseMode": "macos-clang-arm64", "configurationProvider": "ms-vscode.cmake-tools"}, {"name": "Win32", "includePath": ["${workspaceFolder}/**"], "defines": [], "windowsSdkVersion": "8.1", "cStandard": "c17", "cppStandard": "c++17", "intelliSenseMode": "windows-msvc-x64", "configurationProvider": "ms-vscode.cmake-tools"}], "version": 4}