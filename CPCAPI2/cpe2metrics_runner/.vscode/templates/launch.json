// VSCode will read launch.json from cpcapi2_auto_tests/.vscode/launch.json
// This file is not in source control so that developers can freely customize it. If it does not exist during CMake configure (e.g. when opening 
// our workspace in VSCode), it will be copied from our versioned template at cpcapi2_auto_tests/.vscode/templates/launch.json
{
    "configurations": [
        {
            // This config should be only used for running the autotests on macOS or Linux
            "name": "cpe2metrics_runner",
            "args": ["nofork"],
            "type": "lldb",
            "request": "launch",
            "program": "${command:cmake.launchTargetPath}",
            "cwd": "${workspaceFolder}",
            // Only certain tests require enabling this environment variable; those tests will fail if this env variable is not set.
            // Before enabling this environment variable ensure you have followed the instructions printed when the test fails.
            "env": {"CPE_SCENARIO_0_NAME": "sip_registration_request_delay_ms",
                    "CPE_SCENARIO_0_TEST_INTERVAL_SEC": "20",
                    "CPE_SCENARIO_0_SIPACCOUNT_0_username": "cpe2metrics",
                    "CPE_SCENARIO_0_SIPACCOUNT_0_domain": "opsip.silverstar.counterpath.net",
                    //"CPE_SCENARIO_0_edgeproxy_domain": "opsip.silverstar.counterpath.net",
                    "CPE_SCENARIO_0_SIPACCOUNT_0_sipTransportType": "2" }
        },
    ]
}
