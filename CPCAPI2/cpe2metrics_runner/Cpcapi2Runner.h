#pragma once

#include "RunnerTask.h"

// SDK includes
#include <cpcapi2.h>
#include <media/MediaManagerInternal.h>
#include <account/SipAccountManagerInternal.h>
#include <interface/experimental/call/SipConversationManagerExt.h>
#include <phone/PhoneInternal.h>

// rutil includes
#include <rutil/MultiReactor.hxx>
#include <rutil/Data.hxx>
#include <rutil/DeadlineTimer.hxx>

#include <prometheus/family.h>
#include <prometheus/histogram.h>
#include <prometheus/counter.h>

#include <mutex>
#include <functional>

namespace CPCAPI2
{
namespace Agent
{
class WatsonTTS;
class SdkManager;

enum ScenarioType
{
   ScenarioType_Registrations,
   ScenarioType_BasicCalls
};

class Cpcapi2Runner : public RunnerTask,
                      public CPCAPI2::PhoneHandler,
                      public CPCAPI2::PhoneLogger2,
                      public CPCAPI2::Licensing::LicensingClient<PERSON><PERSON><PERSON>,
                      public CPCAPI2::SipAccount::SipAccountHand<PERSON>,
                      public CPCAPI2::SipConversation::<PERSON><PERSON><PERSON>onversation<PERSON>and<PERSON>,
                      public CPCAPI2::ConferenceBridge::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
                      public CPCAPI2::Media::VideoHandler,
                      public CPCAPI2::Media::AudioHandler,
                      public CPCAPI2::SipMessageWaitingIndication::SipMessageWaitingIndicationHandler,
                      public resip::DeadlineTimerHandler
{
public:
   Cpcapi2Runner(
      const std::string& scenarioEnvPrefix, 
      const resip::Data& confConfigFilename,
      const std::set<resip::Data>& srvAccountDomains,
      int sdkThreadPoolThreadIdx, 
      SdkManager* sdkMgr,
      resip::MultiReactor* appReactor, 
      CPCAPI2::Phone* masterSdkPhone, 
      CPCAPI2::Media::MediaTransportsReactorFactory* mediaReactorFactory, 
      const CPCAPI2::ConferenceBridge::ConferenceNatTraversalServerInfo& natServerInfo,
      const CPCAPI2::ConferenceBridge::ConferenceBitrateConfig& screenshareBitrateConfig,
      const CPCAPI2::ConferenceBridge::ConferenceBitrateConfig& cameraBitrateConfig,
      const CPCAPI2::ConferenceBridge::ConferenceMediaEncryptionMode& mediaEncryptionMode);
   virtual ~Cpcapi2Runner();

   resip::Data getContext() const;

   void handleSdkCallback();

   // RunnerTask
   virtual void startTask(const std::function<void(RunnerTask*)>& onCompleteCb);
   virtual void stopTask();
   virtual void shutdown(bool force);
   virtual void updateAccountDomainsFromSRV(const std::set<resip::Data>& srvAccountDomains);

   CPCAPI2::Phone* getPhone() const {
      return mPhone;
   }

   std::string getPhoneMainThreadId() const {
      return mPhoneMainThreadId;
   }

   resip::Data getUserIdentity() const {
      return mUserIdentity;
   }

   std::string sipAcct0OutboundProxy() const {
      return mSipAcct0Settings.outboundProxy.c_str();
   }

   bool isValidResource(const cpc::vector<cpc::string>& requestedResources);

   // DeadlineTimerHandler
   void onTimer(unsigned short timerId, void* appState) override;

   // PhoneHandler
   virtual int onError(const cpc::string& sourceModule, const CPCAPI2::PhoneErrorEvent& args);
   virtual int onLicensingError(const CPCAPI2::LicensingErrorEvent& args);
   virtual int onLicensingSuccess() {
      return 0;
   }

   // PhoneLogger2
   bool operator()(const PhoneLogEvent2& logEvent);

   // LicensingClientHandler
   virtual int onValidateLicensesSuccess(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ValidateLicensesSuccessEvent& args);
   virtual int onValidateLicensesFailure(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ValidateLicensesFailureEvent& args);
   virtual int onError(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ErrorEvent& args);

   // SipAccountHandler
   virtual int onAccountStatusChanged(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountStatusChangedEvent& args);
   virtual int onError(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::ErrorEvent& args);

   // SipConversationHandler
   virtual int onNewConversation(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::NewConversationEvent& args);
   virtual int onConversationEnded(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationEndedEvent& args);
   virtual int onIncomingTransferRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::TransferRequestEvent& args);
   virtual int onIncomingRedirectRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::RedirectRequestEvent& args);
   virtual int onIncomingTargetChangeRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::TargetChangeRequestEvent& args);
   virtual int onIncomingHangupRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::HangupRequestEvent& args);
   virtual int onIncomingBroadsoftTalkRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::BroadsoftTalkEvent& args);
   virtual int onIncomingBroadsoftHoldRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::BroadsoftHoldEvent& args);
   virtual int onTransferProgress(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::TransferProgressEvent& args);
   virtual int onConversationStateChangeRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationStateChangeRequestEvent& args);
   virtual int onConversationStateChanged(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationStateChangedEvent& args);
   virtual int onConversationMediaChangeRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationMediaChangeRequestEvent& args);
   virtual int onConversationMediaChanged(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationMediaChangedEvent& args);
   virtual int onConversationStatisticsUpdated(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationStatisticsUpdatedEvent& args);
   virtual int onError(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ErrorEvent& args);

   // VideoHandler
   virtual int onVideoDeviceListUpdated(const CPCAPI2::Media::VideoDeviceListUpdatedEvent& args);
   virtual int onVideoCodecListUpdated(const CPCAPI2::Media::VideoCodecListUpdatedEvent& args);

   // AudioHandler
   virtual int onAudioDeviceListUpdated(const CPCAPI2::Media::AudioDeviceListUpdatedEvent& args);
   virtual int onPlaySoundComplete(CPCAPI2::Media::PlaySoundHandle soundClip);
   virtual int onPlaySoundFailure(CPCAPI2::Media::PlaySoundHandle soundClip);
   virtual int onAudioCodecListUpdated(const CPCAPI2::Media::AudioCodecListUpdatedEvent& args);
   virtual int onAudioDeviceVolume(const CPCAPI2::Media::AudioDeviceVolumeEvent& args);

   // ConferenceBridgeHandler
   virtual int onConferenceDetails(CPCAPI2::ConferenceBridge::ConferenceHandle conference, const CPCAPI2::ConferenceBridge::ConferenceDetailsResult& args);
   virtual int onConferenceEnded(CPCAPI2::ConferenceBridge::ConferenceHandle conference, const CPCAPI2::ConferenceBridge::ConferenceEndedEvent& args);
   virtual int onPeerConnectionAnswer(CPCAPI2::ConferenceBridge::ConferenceHandle conference, const CPCAPI2::ConferenceBridge::PeerConnectionAnswerEvent& args) {
      return kSuccess;
   }
   virtual int onParticipantListState(CPCAPI2::ConferenceBridge::ConferenceHandle conference, const CPCAPI2::ConferenceBridge::ParticipantListState& args);
   virtual int onConferenceTranscriptionResult(CPCAPI2::ConferenceBridge::ConferenceHandle conference, const CPCAPI2::ConferenceBridge::ConferenceTranscriptionEvent& args) {
      return kSuccess;
   }

   // SipMessageWaitingIndicationHandler
   virtual int onNewSubscription(SipEvent::SipEventSubscriptionHandle subscription, const SipMessageWaitingIndication::NewMWISubscriptionEvent& args) { return kSuccess; }
   virtual int onSubscriptionEnded(SipEvent::SipEventSubscriptionHandle subscription, const SipMessageWaitingIndication::MWISubscriptionEndedEvent& args) { return kSuccess; }
   virtual int onIncomingMWIStatus(SipEvent::SipEventSubscriptionHandle subscription, const SipMessageWaitingIndication::IncomingMWIStatusEvent& args) { return kSuccess; }
   virtual int onSubscriptionStateChanged(SipEvent::SipEventSubscriptionHandle subscription, const SipMessageWaitingIndication::MWISubscriptionStateChangedEvent& args) { return kSuccess; }
   virtual int onError(SipEvent::SipEventSubscriptionHandle subscription, const SipMessageWaitingIndication::ErrorEvent& args) { return kSuccess; }

   int createFullConference();
   int createScreenshare();

   void updateJoinUrl(const cpc::string& joinUrlBase);
   void setLoggingEnabled(bool enable);

private:
   void appInit();

   void handleSdkCallbackImpl();

   CPCAPI2::PhoneInternal* getPhoneInternalForLogger() const;
   bool initAccountSettings(const std::string& sipAccountSettingsEnvVarPrefix, CPCAPI2::SipAccount::SipAccountHandle& sipAccountHandle, SipAccount::SipAccountSettings& outSipAccountSettings);
   void initConfSettings();
   std::string getWebsockUrl() const;

   void playWelcome(CPCAPI2::SipConversation::SipConversationHandle conversation, const std::string& fileName);
   std::string sipAccountSettingsEnvVariablePrefix(int acct) const;
   void startTaskImpl(const std::function<void(RunnerTask*)>& onCompleteCb);
   void stopTaskImpl();
   void updateAccountDomainsFromSRVImpl(const std::set<resip::Data>& srvAccountDomains);

   void startCall();
   std::string getAccountDomain();

   std::string getRegisterDomain(const std::set<resip::Data>& srvDomains) const;

   void prepopulateMetrics();

   bool checkCallHadAudio(CPCAPI2::SipConversation::SipConversationHandle conversation) const;

private:
   resip::Data mUserIdentity;
   resip::Data mResource;
   std::string mScenarioEnvPrefix;
   resip::Data mConfConfigFilename;
   std::set<resip::Data> mSrvAccountDomains;
   std::set<resip::Data>::iterator mNextAccountDomainIt;
   resip::Data mCurrentAccountDomain;
   int mSdkThreadPoolThreadIdx;
   SdkManager* mSdkMgr;
   resip::MultiReactor* mAppReactor;
   CPCAPI2::PhoneInternal* mPhone;
   std::string mPhoneMainThreadId;
   CPCAPI2::Phone* mMasterSdkPhone;
   CPCAPI2::Licensing::LicensingClientManager* mLicensingMgr;
   CPCAPI2::Media::MediaTransportsReactorFactory* mMediaReactorFactory;
   CPCAPI2::Media::MediaManager* mMedia;
   CPCAPI2::SipAccount::SipAccountManager* mSipAccount;
   CPCAPI2::SipConversation::SipConversationManager* mSipConversation;
   CPCAPI2::SipConversation::SipConversationStateManager* mSipConvState;
   resip::DeadlineTimer<resip::MultiReactor> mTimer;
   SipMessageWaitingIndication::SipMessageWaitingIndicationManager* mSipMwi;
   std::function<void(RunnerTask*)> mTaskCompleteCb;

   enum Role
   {
      Role_caller,
      Role_callee
   };
   struct CallInfo
   {
      cpc::string address;
      cpc::string displayName;
      UInt64 startTime = 0;
      Role role = Role_caller;
      bool ringing = false;
      bool connected = false;
      bool hangup = false;
      bool error = false;
   };

   enum class AccountOperation
   {
      None,
      Enable,
      Disable
   };

   AccountOperation mLastSipAccount0RequestOp = AccountOperation::None;
   AccountOperation mLastSipAccount1RequestOp = AccountOperation::None;
   CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status mSipAcct0Status;
   CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status mSipAcct1Status;

   std::map<CPCAPI2::SipConversation::SipConversationHandle, CallInfo> mCalls;
   cpc::string mCalleeUri;

   prometheus::Family<prometheus::Counter>& mCallProbeEventCounterFamily;
   prometheus::Family<prometheus::Histogram>& mCallProbeEventResponseTimeFamily;
   prometheus::Family<prometheus::Histogram>& mCallDurationFamily;
   prometheus::Family<prometheus::Histogram>& mCallQualityFamily;
   prometheus::Family<prometheus::Counter>& mCallMediaStatusFamily;

   CPCAPI2::SipAccount::SipAccountHandle mSipAcct0Handle;
   CPCAPI2::SipAccount::SipAccountHandle mSipAcct1Handle;
   CPCAPI2::SipConversation::SipConversationHandle mSipAcct0ConvHandle;
   CPCAPI2::SipConversation::SipConversationHandle mSipAcct1ConvHandle;
   int mTestIntervalSecs;
   std::string mTaskID;
   std::string mTargetGroup;

   CPCAPI2::SipAccount::SipAccountSettings mSipAcct0Settings;
   CPCAPI2::SipAccount::SipAccountSettings mSipAcct1Settings;

};
}
}
