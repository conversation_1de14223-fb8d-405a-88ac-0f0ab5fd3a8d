#if _WIN32
#include "stdafx.h"
#endif

#include "Cpcapi2Runner.h"
#include "ScenarioSettings.h"
#include "brand_branded.h"
#include "Logger.h"
#include "WatsonTTS.h"
#include "PlaySoundFileStream.h"
#include "Cpcapi2ServerLicense.h"
#include "SdkManager.h"
#include "cpcapi2_conf_buildinfo.h"
#include "LoglevelConverter.h"

#include <interface/experimental/account/SipAccountManagerInternal.h>
#include <interface/experimental/media/MediaManagerInternal.h>
#include <interface/experimental/peerconnection/PeerConnectionManagerInternal.h>

#include "rutil/ConfigParse.hxx"

#include <prometheus/exposer.h>
#include <prometheus/registry.h>

#include <spdlog/spdlog.h>
#include <nlohmann/json.hpp>

// rapidjson
#include <writer.h>
#include <stringbuffer.h>
#include <document.h>
#include <prettywriter.h>

#define CPCAPI2RUNNER_TIMER_REREG 1
#define CPCAPI2RUNNER_TIMER_POST_REG_DELAY 2
#define CPCAPI2RUNNER_TIMER_CALL_DURATION 3

using namespace CPCAPI2;
using namespace CPCAPI2::Licensing;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::SipConversation;
using namespace CPCAPI2::Media;
using namespace CPCAPI2::PeerConnection;
using namespace CPCAPI2::ConferenceBridge;
using namespace prometheus;

namespace CPCAPI2
{
namespace Agent
{
class ConfConfigParse : public resip::ConfigParse
{
private:
   void parseCommandLine(int argc, char** argv, int skipCount = 0) {}
   void printHelpText(int argc, char **argv) {}
};

static prometheus::Histogram::BucketBoundaries create_fixed_duration_buckets() {
    // boundaries need to be sorted!
    auto bucket_boundaries = prometheus::Histogram::BucketBoundaries{0.005, 0.01, 0.02, 0.05, 0.1, 0.2, 0.3, 
                                                                     0.5,   1,    2,    5,    10,  30,  60};
    return bucket_boundaries;
}

static prometheus::Histogram::BucketBoundaries create_call_duration_buckets() {
    // boundaries need to be sorted!
    auto bucket_boundaries = prometheus::Histogram::BucketBoundaries{1, 2, 5, 10, 30, 32, 34, 36, 38, 40, 60};
    return bucket_boundaries;
}

static prometheus::Histogram::BucketBoundaries create_call_quality_buckets() {
    // boundaries need to be sorted!
    auto bucket_boundaries = prometheus::Histogram::BucketBoundaries{0.1, 0.2, 0.5, 0.7, 0.9, 1.0};
    return bucket_boundaries;
}

Cpcapi2Runner::Cpcapi2Runner(
   const std::string& scenarioEnvPrefix, 
   const resip::Data& confConfigFilename,
   const std::set<resip::Data>& srvAccountDomains,
   int sdkThreadPoolThreadIdx, 
   SdkManager* sdkMgr,
   resip::MultiReactor* appReactor, 
   CPCAPI2::Phone* masterSdkPhone, 
   CPCAPI2::Media::MediaTransportsReactorFactory* mediaReactorFactory, 
   const CPCAPI2::ConferenceBridge::ConferenceNatTraversalServerInfo& natServerInfo,
   const CPCAPI2::ConferenceBridge::ConferenceBitrateConfig& screenshareBitrateConfig,
   const CPCAPI2::ConferenceBridge::ConferenceBitrateConfig& cameraBitrateConfig, 
   const CPCAPI2::ConferenceBridge::ConferenceMediaEncryptionMode& mediaEncryptionMode)
   : mUserIdentity("counterpath"), 
   mResource("counterpath"),
   mScenarioEnvPrefix(scenarioEnvPrefix), 
   mConfConfigFilename(confConfigFilename),
   mSrvAccountDomains(srvAccountDomains),
   mNextAccountDomainIt(mSrvAccountDomains.begin()),
   mSdkThreadPoolThreadIdx(sdkThreadPoolThreadIdx), 
   mSdkMgr(sdkMgr),
   mAppReactor(appReactor), 
   mTimer(*appReactor),
   mPhone(NULL), 
   mMedia(NULL),
   mSipAccount(NULL),
   mSipConversation(NULL),
   mSipConvState(NULL),
   mMasterSdkPhone(masterSdkPhone), 
   mMediaReactorFactory(mediaReactorFactory), 
   mCallProbeEventCounterFamily(prometheus::BuildCounter()
      .Name("call_probe_scenario_count")
      .Help("The outcome (success/failure) of a scenario, including data on events observed by caller and callee.")
      .Register(*mSdkMgr->prometheusRegistry())),
   mCallProbeEventResponseTimeFamily(prometheus::BuildHistogram()
      .Name("call_probe_event_response_time")
      .Help("Histogram showing response times and status for various call-related events.")
      .Register(*mSdkMgr->prometheusRegistry())),      
   mCallDurationFamily(prometheus::BuildHistogram()
      .Name("call_probe_call_duration")
      .Help("RFC 6076 SDT")
      .Register(*mSdkMgr->prometheusRegistry())),
   mCallQualityFamily(prometheus::BuildHistogram()
      .Name("call_probe_call_quality")
      .Help("Custom media quality")
      .Register(*mSdkMgr->prometheusRegistry())),
   mCallMediaStatusFamily(prometheus::BuildCounter()
      .Name("call_probe_media_status")
      .Help("Indicates the presence of flowing media for caller and callee.")
      .Register(*mSdkMgr->prometheusRegistry())),
   mSipAcct0Handle(-1),
   mSipAcct1Handle(-1),
   mSipAcct0ConvHandle(-1),
   mSipAcct1ConvHandle(-1),
   mTestIntervalSecs(120),
   mSipAcct0Status(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Unregistered),
   mSipAcct1Status(CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Unregistered)
{
   appInit();
   prepopulateMetrics();
}

Cpcapi2Runner::~Cpcapi2Runner()
{
   mAppReactor->detach();
}

void Cpcapi2Runner::prepopulateMetrics()
{
   std::set<resip::Data> accountDomains = mSrvAccountDomains;
   if (accountDomains.empty())
   {
      accountDomains.insert(getAccountDomain().c_str());
   }
   auto it = accountDomains.begin();
   for (; it != accountDomains.end(); ++it)
   {
      mCallProbeEventCounterFamily.Add({{"role","callee"},
                                       {"error","none"},
                                       {"outcome","failure"},
                                       {"scenario","on-to-on"},
                                       {"target", it->c_str()}});
      mCallProbeEventCounterFamily.Add({{"role","caller"},
                                       {"error","none"},
                                       {"outcome","failure"},
                                       {"scenario","on-to-on"},
                                       {"target", it->c_str()}});
      mCallProbeEventCounterFamily.Add({{"role","callee"},
                                       {"outcome","success"},
                                       {"scenario","on-to-on"},
                                       {"target", it->c_str()}});   
      mCallProbeEventCounterFamily.Add({{"role","caller"},
                                       {"outcome","success"},
                                       {"scenario","on-to-on"},
                                       {"target", it->c_str()}});                              
   }
}

CPCAPI2::PhoneInternal* Cpcapi2Runner::getPhoneInternalForLogger() const
{
   return mPhone;
}

void Cpcapi2Runner::shutdown(bool force)
{
   assert(mAppReactor->isCurrentThread());
   mTaskCompleteCb = [this](RunnerTask*)
   {
      assert(mAppReactor->isCurrentThread());
      mSipAccount->disable(mSipAcct0Handle, true);
      mPhone->process(100);

      Phone::release(mPhone);
      mPhone = nullptr;

      delete this;
   };

   if (force)
   {
      mTaskCompleteCb(this);
   }
}

void Cpcapi2Runner::handleSdkCallback()
{
   mAppReactor->post(resip::resip_bind(&Cpcapi2Runner::handleSdkCallbackImpl, this));
}

void Cpcapi2Runner::handleSdkCallbackImpl()
{
   if (mPhone)
   {
      mPhone->process(CPCAPI2::Phone::kBlockingModeNonBlocking);
      mMedia->process(CPCAPI2::Phone::kBlockingModeNonBlocking);
   }
}

void sdkCallbackHook(void* context)
{
   Cpcapi2Runner* cpcRunner = (Cpcapi2Runner*)context;
   cpcRunner->handleSdkCallback();
}

std::string Cpcapi2Runner::sipAccountSettingsEnvVariablePrefix(int acct) const
{
   std::string sipAccountSettings0EnvVariablePrefix = mScenarioEnvPrefix + "_SIPACCOUNT_";
   sipAccountSettings0EnvVariablePrefix += resip::Data::from(acct).c_str();
   sipAccountSettings0EnvVariablePrefix += "_";
   return sipAccountSettings0EnvVariablePrefix;
}

void Cpcapi2Runner::updateAccountDomainsFromSRV(const std::set<resip::Data>& srvAccountDomains)
{
   mAppReactor->post(resip::resip_bind(&Cpcapi2Runner::updateAccountDomainsFromSRVImpl, this, srvAccountDomains));
}

void Cpcapi2Runner::updateAccountDomainsFromSRVImpl(const std::set<resip::Data>& srvAccountDomains)
{
   std::string currentRegDomain = getRegisterDomain(mSrvAccountDomains);

   if (srvAccountDomains.empty())
   {
      return;
   }

   if (!(mSrvAccountDomains == srvAccountDomains))
   {
      mSrvAccountDomains = srvAccountDomains;
      mNextAccountDomainIt = mSrvAccountDomains.begin();

      if (mSrvAccountDomains.find(currentRegDomain.c_str()) == mSrvAccountDomains.end())
      {
         std::string newRegDomain = getRegisterDomain(mSrvAccountDomains);

         DebugLog(<< "updateAccountDomainsFromSRVImpl Updating account 0, account 1 outbound proxy to " << newRegDomain);

         // domain we are registered against is no longer in list of updated SRV records; register against a new domain
         mSipAcct0Settings.outboundProxy = newRegDomain.c_str();
         mSipAccount->configureDefaultAccountSettings(mSipAcct0Handle, mSipAcct0Settings);
         mSipAccount->applySettings(mSipAcct0Handle);

         mSipAcct1Settings.outboundProxy = newRegDomain.c_str();
         mSipAccount->configureDefaultAccountSettings(mSipAcct1Handle, mSipAcct1Settings);
         mSipAccount->applySettings(mSipAcct1Handle);
      }

      prepopulateMetrics();
   }
}

void Cpcapi2Runner::stopTask()
{
   mAppReactor->post(resip::resip_bind(&Cpcapi2Runner::stopTaskImpl, this));
}

void Cpcapi2Runner::stopTaskImpl()
{
   mTaskCompleteCb = [](RunnerTask* rt) {};
}

void Cpcapi2Runner::startTask(const std::function<void(RunnerTask*)>& onCompleteCb)
{
   mAppReactor->post(resip::resip_bind(&Cpcapi2Runner::startTaskImpl, this, onCompleteCb));
}

void Cpcapi2Runner::startTaskImpl(const std::function<void(RunnerTask*)>& onCompleteCb)
{
   mTaskCompleteCb = onCompleteCb;

   if (mSipAcct0Status == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered &&
      mSipAcct1Status == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered)
   {
      startCall();
   }
   else
   {
      if (mSipAcct0Status != CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered)
      {
         if (!mSipAcct0Settings.username.empty())
         {
            mLastSipAccount0RequestOp = AccountOperation::Enable;
            mSipAccount->enable(mSipAcct0Handle);
         }
      }

      if (mSipAcct1Status != CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered)
      {
         if (!mSipAcct1Settings.username.empty())
         {
            mLastSipAccount1RequestOp = AccountOperation::Enable;
            mSipAccount->enable(mSipAcct1Handle);
         }
      }
   }
}

void Cpcapi2Runner::startCall()
{
   if (mSipAcct1ConvHandle != -1)
   {
      mSipConversation->end(mSipAcct1ConvHandle);
      mSipAcct1ConvHandle = -1;
   }

   mSipAcct0ConvHandle = mSipConversation->createConversation(mSipAcct0Handle);
   mSipConversation->addParticipant(mSipAcct0ConvHandle, mCalleeUri);
   if (mSrvAccountDomains.size() > 0)
   {
      mNextAccountDomainIt++;
      if (mNextAccountDomainIt == mSrvAccountDomains.end())
      {
         mNextAccountDomainIt = mSrvAccountDomains.begin();
      }

      cpc::string accountDomain = "sip:";
      accountDomain += (*mNextAccountDomainIt).c_str();
      mCurrentAccountDomain = (*mNextAccountDomainIt).c_str();
      dynamic_cast<CPCAPI2::SipConversation::SipConversationManagerExt*>(mSipConversation)->setForceTarget(mSipAcct0ConvHandle, accountDomain);
   }
   mSipConversation->start(mSipAcct0ConvHandle);
}

std::string Cpcapi2Runner::getAccountDomain()
{
   std::string ret = mCurrentAccountDomain.c_str();
   if (ret.empty())
   {
      // _ITERATE_SRV not in use
      ret = mSipAcct0Settings.outboundProxy;
   }
   return ret;
}

std::string Cpcapi2Runner::getRegisterDomain(const std::set<resip::Data>& srvDomains) const
{
   if (!srvDomains.empty())
   {
      return srvDomains.begin()->c_str();
   }
   return std::string();
}

void Cpcapi2Runner::appInit()
{
   mLicensingMgr = LicensingClientManager::getInterface(mMasterSdkPhone);
   mPhone = CPCAPI2::PhoneInternal::create(mSdkThreadPoolThreadIdx);
   mPhone->setCallbackHook(sdkCallbackHook, this);
   mPhone->initialize(mLicensingMgr, this, false);

   // logging enabled in SdkManager.cpp which applies to all child phones
   //mPhone->setLoggingEnabled(this, true);

   mPhone->blockUntilRanOnSdkModuleThread([](void* context) 
   {
      std::stringstream ss;
      ss << std::this_thread::get_id();

      Cpcapi2Runner* self = reinterpret_cast<Cpcapi2Runner*>(context);
      self->mPhoneMainThreadId = ss.str();

   }, this);

   InfoLog(<< "Cpcapi2Runner::thread: cpcapi2 rtp proxy phone instance initialized.");
   CPCAPI2::Media::MediaManagerInternal::getInterface(mPhone, mMediaReactorFactory);

   CPCAPI2::Media::MediaStackSettings mediaSettings;
   mediaSettings.audioLayer = CPCAPI2::Media::AudioLayers_File;
   mediaSettings.audioOutputDisabled = true;
   mediaSettings.numAudioEncoderThreads = 0;
   mMedia = MediaManager::getInterface(mPhone);
   if (!mMedia)
   {
      InfoLog(<< "Cpcapi2Runner::thread: MediaManager interface not created successfully");
   }
   dynamic_cast<MediaManagerInternal*>(mMedia)->setCallbackHook(sdkCallbackHook, this);
   AudioExt* audioExt = AudioExt::getInterface(mMedia);
   if (!audioExt)
   {
      InfoLog(<< "Cpcapi2Runner::thread: AudioExt interface not created successfully");
   }
   audioExt->setAudioDeviceFile("", "");
   mMedia->initializeMediaStack(mediaSettings);

   CPCAPI2::Media::Audio* audioIf = Audio::getInterface(mMedia);
   audioIf->setHandler(this);
   audioIf->setEchoCancellationMode(CPCAPI2::Media::AudioDeviceRole_Headset, CPCAPI2::Media::EchoCancellationMode_None);
   audioIf->setNoiseSuppressionMode(CPCAPI2::Media::AudioDeviceRole_Headset, CPCAPI2::Media::NoiseSuppressionMode_None);
   AudioExt::getInterface(mMedia)->setMicAGCEnabled(false);
   audioIf->queryCodecList();
   CPCAPI2::Media::OpusConfig opusConfig;
   opusConfig.complexity = 2;
   opusConfig.enableDtx = false;
   opusConfig.application = 2;
   opusConfig.bitrate = 32000;
   audioIf->setCodecConfig(opusConfig);

   Video::getInterface(mMedia)->setHandler(this);
   Video::getInterface(mMedia)->setCaptureDevice(CPCAPI2::Media::kCustomVideoSourceDeviceId);
   Video::getInterface(mMedia)->queryCodecList();

   mSipAccount = CPCAPI2::SipAccount::SipAccountManager::getInterface(mPhone);
   mSipConversation = CPCAPI2::SipConversation::SipConversationManager::getInterface(mPhone);
   mSipConvState = CPCAPI2::SipConversation::SipConversationStateManager::getInterface(mSipConversation);

   InfoLog(<< "Cpcapi2Runner::thread: cpcapi2 rtp proxy media module initialized");

   std::string testIntervalEnvName = mScenarioEnvPrefix + "_TEST_INTERVAL_SEC";
   std::string dialStringEnvName = mScenarioEnvPrefix + "_DIAL_STRING";

   if (const char* testIntervalSec = std::getenv(testIntervalEnvName.c_str()))
   {
      mTestIntervalSecs = atoi(testIntervalSec);
   }
   else
   {
      mTestIntervalSecs = 20;
      ErrLog(<< "No value for " << testIntervalEnvName << " supplied; defaulting to " << mTestIntervalSecs << " sec");
   }

   if (const char* dialString = std::getenv(dialStringEnvName.c_str()))
   {
      mCalleeUri = dialString;
   }
   else
   {
      WarningLog(<< "No value for " << dialStringEnvName << " supplied; will default to second account SIP username");
   }

   initAccountSettings(sipAccountSettingsEnvVariablePrefix(0), mSipAcct0Handle, mSipAcct0Settings);
   initAccountSettings(sipAccountSettingsEnvVariablePrefix(1), mSipAcct1Handle, mSipAcct1Settings);
}


bool Cpcapi2Runner::initAccountSettings(const std::string& sipAccountSettingsEnvVarPrefix, 
                                        CPCAPI2::SipAccount::SipAccountHandle& sipAccountHandle, 
                                        SipAccountSettings& sipAccountSettings)
{
   // CPE centric defaults
   sipAccountSettings.useRport = false;
   sipAccountSettings.sipTransportType = SipAccountTransport_TCP;

   ScenarioSettings::parseSettings(sipAccountSettingsEnvVarPrefix, sipAccountSettings);

   // handle all registration failures the same way; future attempts will be scheduled by this
   // app when the account goes to state Status_Unregistered
   sipAccountSettings.reRegisterOnResponseTypes.clear();

   if (sipAccountSettings.username.empty())
   {
      return false;
   }

   if (mSrvAccountDomains.size() > 0)
   {
      sipAccountSettings.outboundProxy = getRegisterDomain(mSrvAccountDomains).c_str();
   }

   sipAccountHandle = mSipAccount->create();
   mSipAccount->setHandler(sipAccountHandle, this);
   mSipAccount->configureDefaultAccountSettings(sipAccountHandle, sipAccountSettings);
   mSipAccount->applySettings(sipAccountHandle);

   mSipConversation->setHandler(sipAccountHandle, this);

   mSipMwi = SipMessageWaitingIndication::SipMessageWaitingIndicationManager::getInterface(mPhone);
   mSipMwi->setHandler(sipAccountHandle, this);

   if (mCalleeUri.empty())
   {
      if (sipAccountHandle == mSipAcct1Handle)
      {
         resip::Data calleeUri;
         {
            resip::DataStream calleeUriDs(calleeUri);
            calleeUriDs << "sip:";
            calleeUriDs << sipAccountSettings.username;
            calleeUriDs << "@";
            calleeUriDs << sipAccountSettings.domain;
         }
         mCalleeUri = calleeUri.c_str();
      }
   }

   return true;
}

void Cpcapi2Runner::initConfSettings()
{
}

void Cpcapi2Runner::updateJoinUrl(const cpc::string& joinUrlBase)
{
}

void Cpcapi2Runner::setLoggingEnabled(bool enable)
{

}

int Cpcapi2Runner::createFullConference()
{
   return 0;
}

int Cpcapi2Runner::createScreenshare()
{
   return 0;
}

resip::Data Cpcapi2Runner::getContext() const
{
   resip::Data contextStr;
   return contextStr;
}

bool Cpcapi2Runner::isValidResource(const cpc::vector<cpc::string>& requestedResources)
{
   return false;
}

void Cpcapi2Runner::onTimer(unsigned short timerId, void* appState)
{
   if (timerId == CPCAPI2RUNNER_TIMER_REREG)
   {
      mTaskCompleteCb(this);
   }
   else if (timerId == CPCAPI2RUNNER_TIMER_POST_REG_DELAY)
   {
      if (mCalleeUri.empty())
      {
         DebugLog(<< "Invoking disable from CPCAPI2RUNNER_TIMER_POST_REG_DELAY firing");
         mLastSipAccount0RequestOp = AccountOperation::Disable;
         mSipAccount->disable(mSipAcct0Handle);

         // wait for account disable to complete before scheduling CPCAPI2RUNNER_TIMER_REREG to avoid races between longer
         // account disables & CPCAPI2RUNNER_TIMER_REREG
      }
      else
      {
         startCall();
      }
   }
   else if (timerId == CPCAPI2RUNNER_TIMER_CALL_DURATION)
   {
      mSipConversation->end(mSipAcct0ConvHandle);
   }
}

int Cpcapi2Runner::onError(const cpc::string& sourceModule, const CPCAPI2::PhoneErrorEvent& args)
{
   return 0;
}

bool Cpcapi2Runner::operator()(const PhoneLogEvent2& logEvent)
{
   // logging handled in SdkManager.cpp which applies to all child phones
   return true;
}

int Cpcapi2Runner::onLicensingError(const CPCAPI2::LicensingErrorEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onValidateLicensesSuccess(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ValidateLicensesSuccessEvent& args)
{
   return 0;
}
int Cpcapi2Runner::onValidateLicensesFailure(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ValidateLicensesFailureEvent& args)
{
   return 0;
}
int Cpcapi2Runner::onError(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ErrorEvent& args)
{
   return 0;
}

///////////////////////////////////////////////////////////////////////////////
// SipAccountHandler
int Cpcapi2Runner::onAccountStatusChanged(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountStatusChangedEvent& args)
{
   DebugLog(<< "onAccountStatusChanged: " << account << ", responseTimeMs: " << args.responseTimeMs);
   if (account == mSipAcct0Handle)
   {
      mSipAcct0Status = args.accountStatus;
   }
   else if (account == mSipAcct1Handle)
   {
      mSipAcct1Status = args.accountStatus;
   }

   if ((account == mSipAcct0Handle && mLastSipAccount0RequestOp == AccountOperation::Enable) || // for now ignore failures on account disable
       (account == mSipAcct1Handle && mLastSipAccount1RequestOp == AccountOperation::Enable))
   {
      if (args.signalingStatusCode == 200)
      {
         /*
         if (args.accountStatus == SipAccountStatusChangedEvent::Status_Registered)
         {
            auto& counter = mCallProbeEventCounterFamily.Add({{"role", (account == mSipAcct0Handle ? "caller" : "callee")}, 
                                                   {"target", sipAcct0OutboundProxy()},
                                                   {"outcome", "success"},
                                                   {"scenario", "on-to-on"}});
            counter.Increment();
         }
         */
      }
      else if (args.signalingStatusCode != 0)
      {
         std::string errorStr = "REGISTER/";
         errorStr += resip::Data::from(args.signalingStatusCode).c_str();

         auto& counter = mCallProbeEventCounterFamily.Add({{"role", (account == mSipAcct0Handle ? "caller" : "callee")}, 
                                                {"target", sipAcct0OutboundProxy()},
                                                {"outcome", "failure"},
                                                {"error", errorStr.c_str()},
                                                {"scenario", "on-to-on"}});
         counter.Increment();
      }

      /*
      if (args.signalingStatusCode != 0)
      {
         auto& counter = mCallProbeEventResponseTimeFamily.Add({{"role", (account == mSipAcct0Handle ? "caller" : "callee")},
                                                                  {"target", sipAcct0OutboundProxy()},
                                                                  {"code", resip::Data::from(args.signalingStatusCode).c_str()},
                                                                  {"scenario", "on-to-on"}}, create_fixed_duration_buckets());
         float responseTimeSec = (float)args.responseTimeMs / 1000.0f;
         counter.Observe(responseTimeSec);
      }
      */
   }

   if (args.accountStatus == SipAccountStatusChangedEvent::Status_Registered) {
      if (account == mSipAcct0Handle)
      {
         mTimer.cancel();
         mTimer.expires_from_now(5*1000);
         DebugLog(<< "Queueing CPCAPI2RUNNER_TIMER_POST_REG_DELAY");
         mTimer.async_wait(this, CPCAPI2RUNNER_TIMER_POST_REG_DELAY, NULL);
      }
   }
   else if (args.accountStatus == SipAccountStatusChangedEvent::Status_WaitingToRegister) 
   {
      // need to handle this scenario until SCORE-1448 is resolved
   
      mLastSipAccount0RequestOp = AccountOperation::Disable;
      mSipAccount->disable(mSipAcct0Handle);

      if (!mCalleeUri.empty())
      {
         mLastSipAccount1RequestOp = AccountOperation::Disable;
         mSipAccount->disable(mSipAcct1Handle);
      }
   }
   else if (args.accountStatus == SipAccountStatusChangedEvent::Status_Unregistered)
   {
      if (account == mSipAcct0Handle && mLastSipAccount0RequestOp == AccountOperation::Disable)
      {
         // The happy path
         mSipConversation->end(mSipAcct1ConvHandle);
         mSipAcct1ConvHandle = -1;
         //mLastSipAccount1RequestOp = AccountOperation::Disable;
         //mSipAccount->disable(mSipAcct1Handle);

         mTimer.cancel();
         mTimer.expires_from_now(mTestIntervalSecs*1000);
         mTimer.async_wait(this, CPCAPI2RUNNER_TIMER_REREG, NULL);
      }
      else if (account == mSipAcct0Handle && mLastSipAccount0RequestOp == AccountOperation::Enable)
      {
         // Our last account operation was an enable, but the account went to an unregistered state.
         // We need to set a timer for the next account enable attempt, otherwise no future registrations will occur.

         mTimer.cancel();
         mTimer.expires_from_now(5*1000);
         DebugLog(<< "Queueing CPCAPI2RUNNER_TIMER_POST_REG_DELAY");
         mTimer.async_wait(this, CPCAPI2RUNNER_TIMER_POST_REG_DELAY, NULL);
      }
   }
   return 0;
}

int Cpcapi2Runner::onError(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::ErrorEvent& args)
{
   return 0;
}

///////////////////////////////////////////////////////////////////////////////
// ConferenceBridgeHandler
int Cpcapi2Runner::onConferenceDetails(CPCAPI2::ConferenceBridge::ConferenceHandle conference, const CPCAPI2::ConferenceBridge::ConferenceDetailsResult& args)
{
   DebugLog(<< "Cpcapi2Runner::onConferenceDetails: " << conference);
   std::cout << "conference join URL: " << args.conferenceJoinUrl << std::endl;
   return 0;
}

int Cpcapi2Runner::onParticipantListState(CPCAPI2::ConferenceBridge::ConferenceHandle conference, const CPCAPI2::ConferenceBridge::ParticipantListState& args)
{
   mSdkMgr->writeSessionSummary();
   return 0;
}

int Cpcapi2Runner::onConferenceEnded(CPCAPI2::ConferenceBridge::ConferenceHandle conference, const CPCAPI2::ConferenceBridge::ConferenceEndedEvent& args) {
   DebugLog(<< "Cpcapi2Runner::onConferenceEnded: " << conference);
   return kSuccess;
}

///////////////////////////////////////////////////////////////////////////////
// SipConversationHandler
int Cpcapi2Runner::onNewConversation(SipConversationHandle conversation, const NewConversationEvent& args)
{
   InfoLog(<< "Cpcapi2Runner::onNewConversation: " << conversation << " from " << args.remoteAddress);
   CallInfo callInfo;
   callInfo.address = args.remoteAddress;
   callInfo.displayName = args.remoteDisplayName;
   callInfo.startTime = resip::ResipClock::getTimeMicroSec();

   if (args.conversationType == CPCAPI2::SipConversation::ConversationType_Incoming)
   {
      mSipAcct1ConvHandle = conversation;
      mSipConversation->sendRingingResponse(conversation);
      callInfo.ringing = true;
      std::this_thread::sleep_for(std::chrono::seconds(2));
      mSipConversation->accept(conversation);
      callInfo.role = Role_callee;
   }
   else if (args.conversationType == CPCAPI2::SipConversation::ConversationType_Outgoing)
   {
      callInfo.role = Role_caller;
   }

   mCalls[conversation] = callInfo;

   return 0;
}

bool Cpcapi2Runner::checkCallHadAudio(SipConversationHandle conversation) const
{
   SipConversationState currState;
   mSipConvState->getState(conversation, currState);

   bool callHadAudio = currState.statistics.audioChannels.size() > 0 &&
                       currState.statistics.audioChannels[0].streamDataCounters.packetsReceived > 0 &&
                       currState.statistics.audioChannels[0].streamDataCounters.packetsSent > 0 &&
                       strlen(currState.statistics.audioChannels[0].encoder.plname) > 0 &&
                       strlen(currState.statistics.audioChannels[0].decoder.plname) > 0 &&
                       currState.statistics.remoteAudioChannels.size() > 0;

   return callHadAudio;
}

int Cpcapi2Runner::onConversationEnded(SipConversationHandle conversation, const ConversationEndedEvent& args)
{
   InfoLog(<< "Cpcapi2Runner::onConversationEnded: Conversation " << conversation << "ended");
   
   std::map<CPCAPI2::SipConversation::SipConversationHandle, CallInfo>::iterator callsIt = mCalls.find(conversation);

   const bool callHadAudio = checkCallHadAudio(conversation);

   if (args.endReason == CPCAPI2::SipConversation::ConversationEndReason_UserTerminatedLocally)
   {
      if (callsIt != mCalls.end())
      {
         callsIt->second.hangup = true;

         if (callHadAudio)
         {
            auto& counter = mCallProbeEventCounterFamily.Add({{"role","caller"},
                                                            {"outcome","success"},
                                                            {"scenario","on-to-on"},
                                                            {"target", getAccountDomain()}});
            counter.Increment();
         }
         else
         {
            auto& counter = mCallProbeEventCounterFamily.Add({{"role","caller"},
                                                            {"error","nomedia"},
                                                            {"outcome","failure"},
                                                            {"scenario","on-to-on"},
                                                            {"target", getAccountDomain()}});
            counter.Increment();  
         }
      }

      // !jjg! It would be nice to measure this, but the machinery just doesn't exist in DUM ...
      // InviteSession doesn't call a handler when it gets a BYE/200 response because it already
      // considers the InviteSession to be terminated when the UAC sends out the BYE (which is when onTerminated is called).
/*
      auto& respTimeCounter = mCallProbeEventResponseTimeFamily.Add({{"event","hangup"},
                                                               {"scenario","on-to-on"},
                                                               {"code",resip::Data::from(args.sipResponseCode).c_str()}},
                                                            create_fixed_duration_buckets());                                             
      float responseTimeSec = (float)args.responseTimeMs / 1000.0f;
      respTimeCounter.Observe(responseTimeSec);
*/

      mSipAcct0ConvHandle = -1;

      mTimer.cancel();
      mTimer.expires_from_now(mTestIntervalSecs*1000);
      mTimer.async_wait(this, CPCAPI2RUNNER_TIMER_REREG, NULL);
   }
   else if (args.endReason == CPCAPI2::SipConversation::ConversationEndReason_UserTerminatedRemotely)
   {
      if (callsIt != mCalls.end())
      {
         callsIt->second.hangup = true;

         if (callHadAudio)
         {
            auto& counter = mCallProbeEventCounterFamily.Add({{"role","callee"},
                                                         {"outcome","success"},
                                                         {"scenario","on-to-on"},
                                                         {"target", getAccountDomain()}});
            counter.Increment();
         }
         else
         {
            auto& counter = mCallProbeEventCounterFamily.Add({{"role","callee"},
                                                            {"error","nomedia"},
                                                            {"outcome","failure"},
                                                            {"scenario","on-to-on"},
                                                            {"target", getAccountDomain()}});
            counter.Increment();
         }
      }

      mSipAcct1ConvHandle = -1;
   }
   else if (args.endReason == CPCAPI2::SipConversation::ConversationEndReason_ServerError ||
            args.endReason == CPCAPI2::SipConversation::ConversationEndReason_ServerRejected ||
            args.endReason == CPCAPI2::SipConversation::ConversationEndReason_Unknown)
   {
      if (conversation == mSipAcct0ConvHandle)
      {
         if (callsIt != mCalls.end())
         {
            callsIt->second.error = true;
            std::string errorStr = "INVITE/";
            errorStr += resip::Data::from(args.sipResponseCode).c_str();
         
            auto& counter = mCallProbeEventCounterFamily.Add({{"role","caller"},
                                                            {"error",errorStr},
                                                            {"outcome","failure"},
                                                            {"scenario","on-to-on"},
                                                            {"target", getAccountDomain()}});
            counter.Increment();

            if (mSipAcct1ConvHandle == -1)
            {
               // callee never received a call
               auto& calleeCounter = mCallProbeEventCounterFamily.Add({{"role","callee"},
                                                                       {"error","nocall"},
                                                                       {"outcome","failure"},
                                                                       {"scenario","on-to-on"},
                                                                       {"target", getAccountDomain()}});
               calleeCounter.Increment();
            }
         }

         auto& respTimeCounter = mCallProbeEventResponseTimeFamily.Add({{"event","newcall"},
                                                            {"scenario","on-to-on"},
                                                            {"code",resip::Data::from(args.sipResponseCode).c_str()},
                                                            {"target", getAccountDomain()}},
                                                               create_fixed_duration_buckets());                                             
         float responseTimeSec = (float)args.responseTimeMs / 1000.0f;
         respTimeCounter.Observe(responseTimeSec);

         mSipAcct0ConvHandle = -1;

         mTimer.cancel();
         mTimer.expires_from_now(mTestIntervalSecs*1000);
         mTimer.async_wait(this, CPCAPI2RUNNER_TIMER_REREG, NULL);
      }
   }

   if (callsIt != mCalls.end())
   {
      UInt64 now = resip::ResipClock::getTimeMicroSec();
      UInt64 durationUs = now - callsIt->second.startTime;
      auto& counter = mCallDurationFamily.Add({{"role",(callsIt->second.role == Role_caller ? "caller" : "callee")},
                                               {"scenario","on-to-on"},
                                               {"target", getAccountDomain()}},
                                              create_call_duration_buckets());
      float durationSecs = (float)durationUs / 1000000.0f;
      counter.Observe(durationSecs);
   }

   {
      //auto& counter = mCallQualityFamily.Add({}, create_call_quality_buckets());
      //counter.Observe(callHadAudio ? 1.0f : 0.0f);

      auto& counter = mCallMediaStatusFamily.Add({{"role",(callsIt->second.role == Role_caller ? "caller" : "callee")},
                                                         {"outcome",(callHadAudio ? "success" : "failure")},
                                                         {"scenario","on-to-on"},
                                                         {"media","audio"},
                                                         {"target", getAccountDomain()}});
      counter.Increment();
   }

   mCalls.erase(callsIt);
   
   return 0;
}

int Cpcapi2Runner::onIncomingTransferRequest(SipConversationHandle conversation, const TransferRequestEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onIncomingRedirectRequest(SipConversationHandle conversation, const RedirectRequestEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onIncomingTargetChangeRequest(SipConversationHandle conversation, const TargetChangeRequestEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onIncomingHangupRequest(SipConversationHandle conversation, const HangupRequestEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onIncomingBroadsoftTalkRequest(SipConversationHandle conversation, const BroadsoftTalkEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onIncomingBroadsoftHoldRequest(SipConversationHandle conversation, const BroadsoftHoldEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onTransferProgress(SipConversationHandle conversation, const TransferProgressEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onConversationStateChangeRequest(SipConversationHandle conversation, const ConversationStateChangeRequestEvent& args)
{
   return 0;
}

void Cpcapi2Runner::playWelcome(CPCAPI2::SipConversation::SipConversationHandle conversation, const std::string& fileName)
{
   //PlaySoundFileStream* playSoundStream = new PlaySoundFileStream();
   //if (playSoundStream->OpenFile(fileName.c_str(), true, false, false) == 0)
   //{
   //   mConversation->playSound(conversation, playSoundStream, false);
   //}
}

int Cpcapi2Runner::onConversationStateChanged(SipConversationHandle conversation, const ConversationStateChangedEvent& args)
{
   if (args.conversationState == CPCAPI2::SipConversation::ConversationState_Connected)
   {
      std::map<CPCAPI2::SipConversation::SipConversationHandle, CallInfo>::iterator callsIt = mCalls.find(conversation);
      if (conversation == mSipAcct0ConvHandle)
      {
         if (callsIt != mCalls.end())
         {
            callsIt->second.connected = true;
            if (!callsIt->second.ringing)
            {
               auto& counter = mCallProbeEventResponseTimeFamily.Add({{"event","newcall"},
                                                                {"scenario","on-to-on"},
                                                                {"code",resip::Data::from(args.responseCode).c_str()},
                                                                {"target", getAccountDomain()}},
                                                               create_fixed_duration_buckets());                                             
               float responseTimeSec = (float)args.responseTimeMs / 1000.0f;
               counter.Observe(responseTimeSec);
            }
         }

         mTimer.cancel();
         mTimer.expires_from_now(30*1000);
         mTimer.async_wait(this, CPCAPI2RUNNER_TIMER_CALL_DURATION, NULL);
      }
      else if (conversation == mSipAcct1ConvHandle)
      {
         if (callsIt != mCalls.end())
         {
            callsIt->second.connected = true;
         }
      }
   }
   else if (args.conversationState == CPCAPI2::SipConversation::ConversationState_RemoteRinging)
   {
      if (conversation == mSipAcct0ConvHandle)
      {
         std::map<CPCAPI2::SipConversation::SipConversationHandle, CallInfo>::iterator callsIt = mCalls.find(conversation);
         if (callsIt != mCalls.end())
         {
            callsIt->second.ringing = true;
         }

         auto& counter = mCallProbeEventResponseTimeFamily.Add({{"event","newcall"},
                                                                {"scenario","on-to-on"},
                                                                {"code",resip::Data::from(args.responseCode).c_str()},
                                                                {"target", getAccountDomain()}},
                                                               create_fixed_duration_buckets());                                             
         float responseTimeSec = (float)args.responseTimeMs / 1000.0f;
         counter.Observe(responseTimeSec);
      }
   }
   return 0;
}

int Cpcapi2Runner::onConversationMediaChangeRequest(SipConversationHandle conversation, const ConversationMediaChangeRequestEvent& args)
{
   //mConversation->accept(conversation);
   return 0;
}

int Cpcapi2Runner::onConversationMediaChanged(SipConversationHandle conversation, const ConversationMediaChangedEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onConversationStatisticsUpdated(SipConversationHandle conversation, const ConversationStatisticsUpdatedEvent& args)
{
   //DebugLog(<< "=========== CONVERSATION STATISTICS =============");
   //CPCAPI2::SipConversation::ConversationStatistics conversationStatistics = args.conversationStatistics;
   //if (conversationStatistics.audioChannels.size() > 0)
   //{
   //   DebugLog(<< "---------- LOCAL ----------"
   //      << "cumulativeLost:      " << conversationStatistics.audioChannels[0].streamStatistics.cumulativeLost << std::endl
   //      << "fractionLost:        " << conversationStatistics.audioChannels[0].streamStatistics.fractionLost << std::endl
   //      << "jitterSamples:       " << conversationStatistics.audioChannels[0].streamStatistics.jitterSamples << std::endl
   //      << "rttMs:               " << conversationStatistics.audioChannels[0].streamStatistics.rttMs << std::endl
   //      << "packetsReceived:     " << conversationStatistics.audioChannels[0].streamDataCounters.packetsReceived << std::endl
   //      << "packetsSent:         " << conversationStatistics.audioChannels[0].streamDataCounters.packetsSent << std::endl
   //      << "decoder.plname:      " << conversationStatistics.audioChannels[0].decoder.plname << std::endl
   //      << "encoder.plname:      " << conversationStatistics.audioChannels[0].encoder.plname << std::endl
   //      << "localEndpoint:       " << conversationStatistics.audioChannels[0].endpoint.ipAddress << ":" << conversationStatistics.audioChannels[0].endpoint.port);
   //}
   //if (conversationStatistics.videoChannels.size() > 0)
   //{
   //   DebugLog(<< "---------- LOCAL (video) ----------"
   //      << "cumulativeLost:       " << conversationStatistics.videoChannels[0].streamStatistics.cumulativeLost << std::endl
   //      << "fractionLost:         " << conversationStatistics.videoChannels[0].streamStatistics.fractionLost << std::endl
   //      << "jitterSamples:        " << conversationStatistics.videoChannels[0].streamStatistics.jitterSamples << std::endl
   //      << "rttMs:                " << conversationStatistics.videoChannels[0].streamStatistics.rttMs << std::endl
   //      << "packetsReceived:      " << conversationStatistics.videoChannels[0].streamDataCounters.packetsReceived << std::endl
   //      << "packetsSent:          " << conversationStatistics.videoChannels[0].streamDataCounters.packetsSent << std::endl
   //      << "decoder.plname:       " << conversationStatistics.videoChannels[0].decoder.plName << std::endl
   //      << "encoder.plname:       " << conversationStatistics.videoChannels[0].encoder.plName << std::endl
   //      << "currentTargetBitrate: " << conversationStatistics.videoChannels[0].currentTargetBitrate << std::endl
   //      << "localEndpoint:        " << conversationStatistics.videoChannels[0].endpoint.ipAddress << ":" << conversationStatistics.videoChannels[0].endpoint.port);
   //}
   //if (conversationStatistics.remoteAudioChannels.size() > 0)
   //{
   //   DebugLog(<< "---------- REMOTE ----------"
   //      << "cumulativeLost:      " << conversationStatistics.remoteAudioChannels[0].streamStatistics.cumulativeLost << std::endl
   //      << "fractionLost:        " << conversationStatistics.remoteAudioChannels[0].streamStatistics.fractionLost << std::endl
   //      << "jitterSamples:       " << conversationStatistics.remoteAudioChannels[0].streamStatistics.jitterSamples << std::endl
   //      << "rttMs:               " << conversationStatistics.remoteAudioChannels[0].streamStatistics.rttMs << std::endl
   //      << "remoteEndpoint:      " << conversationStatistics.remoteAudioChannels[0].endpoint.ipAddress << ":" << conversationStatistics.remoteAudioChannels[0].endpoint.port);
   //}
   //if (conversationStatistics.remoteVideoChannels.size() > 0)
   //{
   //   DebugLog(<< "---------- REMOTE (video) ----------" << std::endl
   //      << "cumulativeLost:      " << conversationStatistics.remoteVideoChannels[0].streamStatistics.cumulativeLost << std::endl
   //      << "fractionLost:        " << conversationStatistics.remoteVideoChannels[0].streamStatistics.fractionLost << std::endl
   //      << "jitterSamples:       " << conversationStatistics.remoteVideoChannels[0].streamStatistics.jitterSamples << std::endl
   //      << "rttMs:               " << conversationStatistics.remoteVideoChannels[0].streamStatistics.rttMs << std::endl
   //      << "remoteEndpoint:      " << conversationStatistics.remoteVideoChannels[0].endpoint.ipAddress << ":" << conversationStatistics.remoteVideoChannels[0].endpoint.port);
   //}
   //DebugLog(<< "======> Call Quality: " << conversationStatistics.callQuality);

   return 0;
}

int Cpcapi2Runner::onError(SipConversationHandle conversation, const CPCAPI2::SipConversation::ErrorEvent& args)
{
   return 0;
}

///////////////////////////////////////////////////////////////////////////////
// VideoHandler
int Cpcapi2Runner::onVideoDeviceListUpdated(const CPCAPI2::Media::VideoDeviceListUpdatedEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onVideoCodecListUpdated(const CPCAPI2::Media::VideoCodecListUpdatedEvent& args)
{
   cpc::vector<CPCAPI2::Media::VideoCodecInfo>::const_iterator it = args.codecInfo.begin();
   for (; it != args.codecInfo.end(); ++it)
   {
      if (strcmp(it->codecName, "H.264") == 0)
      {
         Video::getInterface(mMedia)->setCodecEnabled(it->id, true);
         CPCAPI2::Media::H264Config h264config;
         h264config.enableNonInterleavedMode = false;
         Video::getInterface(mMedia)->setCodecConfig(h264config);
         Video::getInterface(mMedia)->setPreferredResolution(it->id, CPCAPI2::Media::VideoCaptureResolution_HD_1280x720p);
      }
      else if (strcmp(it->codecName, "VP8") == 0)
      {
         Video::getInterface(mMedia)->setCodecEnabled(it->id, false);
         Video::getInterface(mMedia)->setPreferredResolution(it->id, CPCAPI2::Media::VideoCaptureResolution_HD_1280x720p);
      }
      else
      {
         Video::getInterface(mMedia)->setCodecEnabled(it->id, false);
      }
   }
   return 0;
}

////////////////////////////////////////////////////////////////////////////////
// AudioHandler
int Cpcapi2Runner::onAudioDeviceListUpdated(const CPCAPI2::Media::AudioDeviceListUpdatedEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onPlaySoundComplete(CPCAPI2::Media::PlaySoundHandle soundClip)
{
   return 0;
}

int Cpcapi2Runner::onPlaySoundFailure(CPCAPI2::Media::PlaySoundHandle soundClip)
{
   return 0;
}

int Cpcapi2Runner::onAudioCodecListUpdated(const CPCAPI2::Media::AudioCodecListUpdatedEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onAudioDeviceVolume(const CPCAPI2::Media::AudioDeviceVolumeEvent& args)
{
   return 0;
}


}
}
