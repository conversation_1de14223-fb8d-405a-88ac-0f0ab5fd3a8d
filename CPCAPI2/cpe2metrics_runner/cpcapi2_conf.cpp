#include "stdafx.h"

#ifdef __linux__
#include <sys/types.h>
#include <sys/stat.h>
#include <stdio.h>
#include <stdlib.h>
#include <fcntl.h>
#include <errno.h>
#include <unistd.h>
#include <syslog.h>
#include <string.h>
#elif __APPLE__
#include <sys/types.h>
#include <sys/stat.h>
#endif

#include "SdkManager.h"

using namespace CPCAPI2::Agent;

#if _WIN32
int _tmain(int argc, _TCHAR* argv[])
#else
int main(int argc, char * argv[])
#endif
{
   bool forkProcess = true;
#ifdef WIN32
   forkProcess = false;
#endif
   bool startupLogging = false;

   for (int i = 0; i < argc; ++i)
   {
      if (strcmp(argv[i], "nofork") == 0)
      {
         forkProcess = false;
      }
      else if (strcmp(argv[i], "logging") == 0)
      {
         startupLogging = true;
      }
   }

   if (forkProcess)
   {
#ifndef WIN32
      /* Our process ID and Session ID */
      pid_t pid, sid;

      /* Fork off the parent process */
      pid = fork();
      if (pid < 0)
      {
         exit(EXIT_FAILURE);
      }

      /* If we got a good PID, then we can exit the parent process. */
      if (pid > 0)
      {
         exit(EXIT_SUCCESS);
      }

      /* Change the file mode mask */
      umask(0);

      /* Open any logs here */
      openlog("cpcapi2_log", LOG_PID | LOG_CONS, LOG_USER);

      /* Create a new SID for the child process */
      sid = setsid();
      if (sid < 0)
      {
         /* Log the failure */
         system("echo SID creation error.");
         exit(EXIT_FAILURE);
      }

      /* Close out the standard file descriptors */
      close(STDIN_FILENO);
      close(STDOUT_FILENO);
      close(STDERR_FILENO);

      /* Daemon-specific initialization goes here */

      /* The Big Loop. Run CPCAPI2 RTP proxy tasks here */
      syslog(LOG_INFO, "Start a sdkrunner.");
#endif
   }

   SdkManager::StartupConfig cfg;
   cfg.logging = startupLogging;

   SdkManager sdkManager(cfg);
   sdkManager.run();

   if (forkProcess)
   {
      sdkManager.join();
#ifndef WIN32
      closelog();
#endif
      exit(EXIT_SUCCESS);
   }
   else
   {
      for (;;)
      {
         std::this_thread::sleep_for(std::chrono::seconds(5));
         if (sdkManager.exitProcess())
         {
            break;
         }
      }
   }

   return 0;
}

