cmake_minimum_required(VERSION 3.8.0)

if (CMAKE_BUILD_TYPE MATCHES Debug)
  # note this will also evaluate to true if cross compiling for Android on macOS
  if (APPLE)
    # enable ASan by for debug builds by default.
    # note that we need to set SANI<PERSON>ZER before configure.cmake is included otherwise it won't
    # be applied.
    set(SANITIZER "Address" CACHE STRING "")
  endif()
endif()

add_definitions(-DCPCAPI2_CPE_METRICS_RUNNER -DCPCAPI2_INCLUDE_UNRELEASED_HEADERS)
include(${CMAKE_CURRENT_SOURCE_DIR}/../projects/cmake/toolchains/configure.cmake) # Must be included before project()

project(cpcapi2_conf LANGUAGES ${CPCAPI2_LANGUAGES})
init_project()

include_directories(${INCLUDE_DIRS})

option(DISABLE_WSS_JSON_SERVER "Disable use of secure websocket JSON server" OFF)

# App sources
set(SOURCES
  cpcapi2_conf.cpp
  Cpcapi2Runner.cpp
  PlaySoundFileStream.cpp
  SdkManager.cpp
  HealthcheckEndpoint.cpp
  ScenarioSettings.cpp
  RegistrationScenario.cpp
)

set(CMAKE_THREAD_PREFER_PTHREAD ON)
set(THREADS_PREFER_PTHREAD_FLAG ON)

find_package(Threads REQUIRED)

add_executable(${CMAKE_PROJECT_NAME} ${SOURCES})
set_property(TARGET ${CMAKE_PROJECT_NAME} PROPERTY CXX_STANDARD ${CPCAPI2_CXX_STANDARD})

set_property(TARGET ${CMAKE_PROJECT_NAME} PROPERTY CXX_STANDARD 17)
set_property(TARGET ${CMAKE_PROJECT_NAME} PROPERTY CMAKE_CXX_STANDARD_REQUIRED ON)
set_property(TARGET ${CMAKE_PROJECT_NAME} PROPERTY CMAKE_CXX_EXTENSIONS OFF)
set(CMAKE_OSX_DEPLOYMENT_TARGET "11.6" CACHE STRING "Minimum OS X deployment version" FORCE)

if (OS_LINUX)
  SET(USE_X11 ON CACHE BOOL "Build with X11 support")
endif()

include(${CMAKE_CURRENT_SOURCE_DIR}/../projects/cmake/CPCAPI2/CPCAPI2.cmake)

set(PROMETHEUS_INSTALL_DIR "${CMAKE_CURRENT_SOURCE_DIR}/prometheus-cpp/installed" CACHE INTERNAL "")
if (CMAKE_PLATFORM_NAME MATCHES "x86_64")
  set(PROMETHEUS_INSTALL_DIR "${CMAKE_CURRENT_SOURCE_DIR}/prometheus-cpp/installed_x86_64" CACHE INTERNAL "")
endif()

add_library(prometheus-cpp-core STATIC IMPORTED GLOBAL)
set_target_properties(prometheus-cpp-core PROPERTIES IMPORTED_LOCATION "${PROMETHEUS_INSTALL_DIR}/lib/${CMAKE_STATIC_LIBRARY_PREFIX}prometheus-cpp-core${CMAKE_STATIC_LIBRARY_SUFFIX}")
target_include_directories(prometheus-cpp-core INTERFACE "${PROMETHEUS_INSTALL_DIR}/include")

add_library(prometheus-cpp-pull STATIC IMPORTED GLOBAL)
set_target_properties(prometheus-cpp-pull PROPERTIES IMPORTED_LOCATION "${PROMETHEUS_INSTALL_DIR}/lib/${CMAKE_STATIC_LIBRARY_PREFIX}prometheus-cpp-pull${CMAKE_STATIC_LIBRARY_SUFFIX}")
target_include_directories(prometheus-cpp-pull INTERFACE "${PROMETHEUS_INSTALL_DIR}/include")


target_link_libraries(
  ${CMAKE_PROJECT_NAME} 
  PUBLIC
    CPCAPI2_Static
    rapidjson
    Threads::Threads
    prometheus-cpp-core
    prometheus-cpp-pull
)

if (OS_LINUX)
  target_link_libraries(
    ${CMAKE_PROJECT_NAME} 
    PUBLIC
      -lstdc++fs
)
endif()

# copy a launch.json with reasonable defaults for the user, since it looks like they don't have one already.
# this should not clobber a user's own/customized .vscode/launch.json
if(NOT EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/.vscode/launch.json")
  file(COPY ${CMAKE_CURRENT_SOURCE_DIR}/.vscode/templates/launch.json
     DESTINATION ${CMAKE_CURRENT_SOURCE_DIR}/.vscode)
endif()