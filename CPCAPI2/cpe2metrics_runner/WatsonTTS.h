#pragma once

#include <websocketpp/config/asio_client.hpp>
#include <websocketpp/client.hpp>

namespace CPCAPI2
{
namespace Agent
{
class WatsonTTS
{
public:
   WatsonTTS();
   virtual ~WatsonTTS();

   int connect();
   int shutdown();
   int convert(const cpc::string& textStr, const std::function<void(const std::string&)>& cb);

private:
   typedef websocketpp::lib::thread thread_type;
   typedef websocketpp::lib::shared_ptr<thread_type> thread_ptr;
   typedef websocketpp::client<websocketpp::config::asio_tls_client> tlsClient;
   typedef websocketpp::config::asio_tls_client::message_type::ptr tlsMessagePtr;
   typedef boost::asio::ssl::stream< boost::asio::ip::tcp::socket > tlsStream;
   typedef websocketpp::lib::shared_ptr<boost::asio::ssl::context> tlsContext;
   typedef tlsClient::connection_ptr tlsConnectionPtr;

   void on_open(websocketpp::connection_hdl hdl);
   void on_close(websocketpp::connection_hdl hdl);
   void on_message(websocketpp::connection_hdl hdl, tlsMessagePtr msg);
   void on_fail(websocketpp::connection_hdl hdl);

   void connectImpl();
   void shutdownImpl();
   void convertImpl(const cpc::string& textStr, const std::function<void(const std::string&)>& cb);

   size_t getApiTokenHandler(char* ptr, size_t size, size_t nmemb);

private:
   tlsClient mEndpoint;
   tlsClient::connection_ptr mConn;
   thread_ptr mThread;
   std::mutex mMutex;
   std::condition_variable mCondConnected;
   std::condition_variable mCondFailed;
   std::string mWatsonToken;
   std::function<void(const std::string&)> mAudioDataCallback;
};
}
}
