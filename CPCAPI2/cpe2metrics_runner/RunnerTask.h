#pragma once

#include <functional>

namespace CPCAPI2
{
namespace Agent
{
class RunnerTask
{
public:
   RunnerTask() {}
   virtual ~RunnerTask() {}

   virtual void startTask(const std::function<void(RunnerTask*)>& onCompleteCb) = 0;
   virtual void stopTask() = 0;
   virtual void shutdown(bool force) = 0;

   virtual void updateAccountDomainsFromSRV(const std::set<resip::Data>& srvAccountDomains) = 0;
};
}
}
