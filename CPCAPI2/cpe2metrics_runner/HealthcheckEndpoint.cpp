#include "HealthcheckEndpoint.h"

#include <iostream>
#include <string>
#include <vector>

using namespace CPCAPI2::Agent;

HealthcheckEndpoint::HealthcheckEndpoint(const HealthcheckEndpointConfig& config) :
   mConfig(config)
{
}

std::string resourcePathRegex(const std::string& resourcePath)
{
   std::stringstream ss;
   ss << resourcePath << "$";
   return ss.str();
}

std::string HealthcheckEndpoint::goodHttpResponse() const
{
   return "HTTP/1.1 200 OK\r\nContent-Length: 0\r\n\r\n";
}

std::string HealthcheckEndpoint::badHttpResponse() const
{
   return "HTTP/1.1 500 OK\r\nContent-Length: 0\r\n\r\n";
}

bool HealthcheckEndpoint::splitIpPort(const std::string& ipPort, std::string& outIp, unsigned short& outPort) const
{
   std::vector<std::string> tokens;
   std::stringstream ss(ipPort);
   std::string token;
   while (std::getline(ss, token, ':')) 
   {
      tokens.push_back(token);
   }

   if (tokens.size() == 2)
   {
      outIp = tokens[0];
      outPort = std::stoi(tokens[1]);
      return true;
   }
   else
   {
      return false;
   }
}

bool HealthcheckEndpoint::start(HealthcheckEndpointHandler* handler)
{
   mHandler = handler;
   bool configApplied = splitIpPort(mConfig.listenIpWithPort, mHealthCheckServer.config.address, mHealthCheckServer.config.port);
   if (!configApplied)
   {
      return false;
   }

   const std::string resourcePath("/healthcheck");

   mHealthCheckServerThread = std::thread([this]()
   {
      mHealthCheckServer.start();
   });

   mHealthCheckServer.resource[resourcePathRegex(resourcePath)]["GET"]=[&](std::shared_ptr<HttpServer::Response> response,
                                                               std::shared_ptr<HttpServer::Request> request)
   {
      if (mHandler)
      {
         if (bool healthy = mHandler->onHealthCheckRequest())
         {
            *response << goodHttpResponse();;
            return;
         }
      }

      *response << badHttpResponse();
   };

   return true;
}

void HealthcheckEndpoint::stop()
{
   mHealthCheckServer.stop();
   mHealthCheckServerThread.join();
}