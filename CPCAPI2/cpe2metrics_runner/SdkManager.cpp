#include "stdafx.h"
#include "SdkManager.h"
#include "RunnerTask.h"
#include "Cpcapi2Runner.h"
#include "RegistrationScenario.h"
#include "Cpcapi2ServerLicense.h"
#include "cpcapi2_conf_buildinfo.h"
#include "Logger.h"
#include "../CPCAPI2/impl/util/DnsClient.h"
#include "LoglevelConverter.h"

#include <cpcapi2.h>
#include <cloudserviceconfig/CloudServiceConfig.h>
#include <confbridge/ConferenceBridgeInternal.h>
#include <confbridge/ConferenceRegistrar.h>

#include "rutil/ConfigParse.hxx"
#include "rutil/Random.hxx"
#include "resip/stack/Tuple.hxx"

#include <boost/preprocessor/stringize.hpp>

// rapidjson
#include <writer.h>
#include <stringbuffer.h>
#include <document.h>
#include <prettywriter.h>

#include <spdlog/spdlog.h>
#include <nlohmann/json.hpp>

#include <boost/algorithm/string/predicate.hpp>

#include <fstream>

#ifndef WIN32
#include <signal.h>
#endif

#ifdef __linux__
#include <sys/stat.h>
#include <sys/types.h> // setuid()
#include <unistd.h> // setuid()
#endif

#include <thread>
#include <functional>
#include <filesystem>
#include <cstdlib>
#include <chrono>
#include <iomanip>
#include <iostream>
#include <ctime>
#include <sstream>

#define CPCAPI2_CONF_NUM_SDK_REACTORS std::thread::hardware_concurrency()
#define RANDOM_PATH_COMPONENT_LENGTH 8
#define SDKMANAGER_LOGGING_TIMER 1

#ifdef BUILD_INFO
#define BUILD_INFO_STRING BOOST_PP_STRINGIZE(BUILD_INFO)
#else
#define BUILD_INFO_STRING "No build info"
#endif

static const std::string kRegistrationScenarioName = "registration";
static const std::string kOnToOnScenarioName = "on-to-on";

using namespace CPCAPI2;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::Licensing;
using namespace CPCAPI2::CloudServiceConfig;

namespace CPCAPI2
{
namespace Agent
{
class MyConfigParse : public resip::ConfigParse
{
private:
   void parseCommandLine(int argc, char** argv, int skipCount = 0) {}
   void printHelpText(int argc, char **argv) {}
};

std::atomic_int SdkManager::sNextSdkThreadPoolIdx(0);
std::function<void(int)> signal_callback_wrapper;
void signal_callback_function(int val)
{
   signal_callback_wrapper(val);
}

SdkManager::SdkManager(StartupConfig& startupConfig)
   : mPhone(NULL),
     mLicensingMgr(NULL),
     mAuthServer(NULL),
     mConfRegistrar(NULL),
     mMediaEncryptionMode(CPCAPI2::ConferenceBridge::ConferenceMediaEncryptionMode_SRTP_DTLS),
     mRestAsyncResultHandler(this),
     mBuildInfo(this),
     mSessionSummaryReqHandler(this),
     mConnectedEndpointSummaryReqHandler(this),
     mCrashReqHandler(this),
     mMaintMode(this),
     mLoggingEnableHandler(this, true),
     mLoggingDisableHandler(this, false),
     mLoggingStatusReqHandler(this),
     mRaftListNodes(this),
     mMaintenanceModeEnabled(false),
     mUseServerUidInJoinUrls(false),
     mCreateRootConference(true),
     mTotalPrivilegedAccesses(0),
     mLoggingTimer(mReactor),
     mIsLoggingEnabled(false),
     mStartupConfig(startupConfig),
     mIsLeader(false),
     mNodeId(-1)
{

}

SdkManager::~SdkManager()
{
}

void SdkManager::run()
{
   mReactor.start();
   mReactor.post(resip::resip_bind(&SdkManager::appInit, this));
}

void SdkManager_sdkCallbackHook(void* context)
{
   SdkManager* cpcRunner = (SdkManager*)context;
   cpcRunner->handleSdkCallback();
}

void SdkManager::handleSdkCallback()
{
   mReactor.post(resip::resip_bind(&SdkManager::handleSdkCallbackImpl, this));
}

void SdkManager::handleSdkCallbackImpl()
{
   mPhone->process(CPCAPI2::Phone::kBlockingModeNonBlocking);
   //mJsonApiServer->process(CPCAPI2::JsonApi::JsonApiServer::kBlockingModeNonBlocking);
   if (mConfRegistrar != NULL)
   {
      mConfRegistrar->process(CPCAPI2::ConferenceBridge::ConferenceRegistrar::kBlockingModeNonBlocking);
   }
}

void SdkManager::sigtermHandler(int signum)
{
   InfoLog(<< "Received SIGTERM; shutting down");
   shutdown();
}

void SdkManager::onPrivilegedAccessCompletedImpl()
{
#ifdef __linux__
   assert(mTotalPrivilegedAccesses > 0);

   if (--mTotalPrivilegedAccesses == 0)
   {

      char buffer[1024];
      memset(buffer, 0, sizeof(buffer));
      readlink("/proc/self/exe", buffer, sizeof(buffer) - 1);

      struct stat _stat;
      stat(buffer, &_stat);

      setgid(_stat.st_gid);
      setuid(_stat.st_uid);

   }
#endif
}

void SdkManager::onPrivilegedAccessCompleted(void* context)
{
   auto sdkManager = static_cast<SdkManager*>(context);
   sdkManager->mReactor.post(resip::resip_bind(&SdkManager::onPrivilegedAccessCompletedImpl, sdkManager));
}

void SdkManager::appInit()
{
#ifndef WIN32
   // OBELISK-4439: prevent SIGPIPE from killing the whole process by ignoring SIGPIPE;
   // write errors will be handled locally instead
   signal(SIGPIPE, SIG_IGN);

   // systemd will send us SIGTERM when it wants us to shutdown
   struct sigaction sthandler;
   signal_callback_wrapper = std::bind(&SdkManager::sigtermHandler, this, std::placeholders::_1);
   sthandler.sa_handler = signal_callback_function;
   sigemptyset(&sthandler.sa_mask);
   sthandler.sa_flags = 0;
   sigaction(SIGTERM, &sthandler, NULL);
#endif



   mPhone = CPCAPI2::PhoneInternal::create(0);
   LicenseInfo licenseInfo;
   licenseInfo.licenseKey = "lkj";
   licenseInfo.licenseDocumentLocation = "/tmp";
   licenseInfo.licenseAor = "";
   mPhone->setCallbackHook(SdkManager_sdkCallbackHook, this);

   const int maxPerLogfileSizeMb = 1;
   const int maxLogfileCount = 3;
   spdlog::set_pattern("%v"); // avoids spdlog printing timestamp and log level; we'll print this ourselves in the JSON blob we supply for each log line

   if (const char* loglevel = std::getenv("PROJECT_LOG_LEVEL"))
   {
      spdlog::set_level(LoglevelConverter::javastr2spdlvl(loglevel));
   }
   else
   {
      spdlog::set_level(spdlog::level::level_enum::debug);
   }
   spdlog::flush_every(std::chrono::seconds(5));

   mPhone->setLoggingEnabled2(this, true);
   mPhone->initialize(licenseInfo, this, false);

   initConf();
}

void SdkManager::initConf()
{
   mMediaReactorFactory = CPCAPI2::Media::MediaTransportsReactorFactory::create();
   mMediaReactorFactory->initialize();
   mMediaReactorFactory->addRef();

   if (const char* prometheusEndpoint = std::getenv("PROMETHEUS_LISTEN_ENDPOINT"))
   {
      mPrometheusExposer.reset(new prometheus::Exposer(prometheusEndpoint));
   }
   else
   {
      mPrometheusExposer.reset(new prometheus::Exposer("0.0.0.0:8080"));
   }
   mPrometheusRegistry = std::make_shared<prometheus::Registry>();
   mPrometheusExposer->RegisterCollectable(mPrometheusRegistry);

   HealthcheckEndpointConfig hcConfig;
   if (const char* healthcheckEndpoint = std::getenv("HEALTHCHECK_LISTEN_ENDPOINT"))
   {
      hcConfig.listenIpWithPort = healthcheckEndpoint;
   }
   mHealthCheckEndpoint = std::make_unique<HealthcheckEndpoint>(hcConfig);

   initFromSettings();
   if (!mHealthCheckEndpoint->start(this))
   {
      ErrLog(<< "Health check endpoint startup failed; check config");
   }
}

void SdkManager::shutdown()
{
   // stop exposing metrics to prevent any erroneous metrics induced by the shutdown
   mPrometheusExposer.reset();

   //mTTSApi->shutdown();
   mReactor.execute(resip::resip_bind(&SdkManager::appShutdown, this));
   mReactor.stop();
   mExitProcess = true;
   Phone::release(mPhone);
   mHealthCheckEndpoint->stop();
}

void SdkManager::appShutdown()
{
   mDnsClient.reset();
   mLoggingTimer.cancel();
   mIsLoggingEnabled = false;

   std::map<resip::Data, RunnerTask*> contextMapCpy = mMapContextToRunner;
   std::map<resip::Data, RunnerTask*>::iterator it = contextMapCpy.begin();
   for (; it != contextMapCpy.end(); ++it)
   {
      RunnerTask* runner = it->second;
      const bool force = true; // don't wait for scenario to complete
      runner->shutdown(force);
   }

   mMediaReactorFactory->releaseRef();
}

void SdkManager::join()
{
   mReactor.join();
}

class RecheckDnsTask : public RunnerTask
{
public:
   RecheckDnsTask(SdkManager* sdkManager, resip::MultiReactor& reactor) : 
      mSdkManager(sdkManager),
      mReactor(reactor) {}
   virtual ~RecheckDnsTask() {}

   virtual void startTask(const std::function<void(RunnerTask*)>& onCompleteCb)
   {
      mCompleteCb = onCompleteCb;
      mSdkManager->recheckDns();
      mReactor.postMS(resip::resip_bind(&RecheckDnsTask::postComplete, this), 30000);
   }
   virtual void stopTask()
   {
   }
   virtual void shutdown(bool /*force*/)
   {
   }
   virtual void updateAccountDomainsFromSRV(const std::set<resip::Data>& srvAccountDomains) {}

private:
   void postComplete()
   {
      mCompleteCb(this);
   }

private:
   SdkManager* mSdkManager;
   resip::MultiReactor& mReactor;
   std::function<void(RunnerTask*)> mCompleteCb;
};

void SdkManager::initFromSettings()
{
   if (const char* appName = std::getenv("APP_NAME"))
   {
      mStaticLoglineItems.app = appName;
   }
   if (const char* environmentName = std::getenv("ENVIRONMENT_NAME"))
   {
      mStaticLoglineItems.environmentName = environmentName;
   }
   if (const char* environmentType = std::getenv("ENVIRONMENT_TYPE"))
   {
      mStaticLoglineItems.environmentType = environmentType;
   }

   RecheckDnsTask* recheckDnsTask = new RecheckDnsTask(this, mReactor);
   recheckDnsTask->startTask(std::bind(&SdkManager::runnerTaskCompleteCb, this, std::placeholders::_1));
}

void SdkManager::recheckDns()
{
   resip::DnsStub::DnsSettings dnsSettings;
   if (const char* dnsOverride = std::getenv("NAMESERVER_OVERRIDE"))
   {
      dnsSettings.includeSystemDnsServers = false;
      dnsSettings.includeIpv6SystemDnsServers = false;

      resip::Tuple t(dnsOverride, 53, resip::UDP);
      dnsSettings.nameServers.push_back(t.toGenericIPAddress());
   }
   mDnsClient.reset(new CPCAPI2::Utils::DnsClient(dnsSettings));

   for (int i = 0; i < 2; i++)
   {
      std::string envScenarioBase = "CPE_SCENARIO_" + std::to_string(i);
      std::string envScenarioName = envScenarioBase + "_NAME";

      if (const char* scenarioNameVal = std::getenv(envScenarioName.c_str()))
      {
         resip::Data scenarioNameValStr(scenarioNameVal);
         if (resip::isEqualNoCase(scenarioNameValStr, kRegistrationScenarioName.c_str()))
            initReg(envScenarioBase);
         else if (resip::isEqualNoCase(scenarioNameValStr, kOnToOnScenarioName.c_str()))
            initOnToOn(envScenarioBase);
         else
            ErrLog(<< "Unsupported scenario type: " << scenarioNameValStr);
      }
      else
      {
         if (i == 1)
            initOnToOn(envScenarioBase);
         else if (i == 0)
            initReg(envScenarioBase);
      }
   }
}

static bool recordExists(cpc::vector<CPCAPI2::Utils::DnsSrvRecord>& records, const resip::Data& recordTarget)
{
   for (const CPCAPI2::Utils::DnsSrvRecord& srvResult : records)
   {
      if (srvResult.target == recordTarget)
      {
         return true;
      }
   }

   return false;
}

void SdkManager::initReg(const std::string& envScenarioBase)
{
   std::string envScenarioName = envScenarioBase + "_NAME";

   if (NULL == std::getenv(envScenarioName.c_str()))
   {
      return;
   }

   // CPE_SCENARIO_0_iterate_srv
   std::string envEdgeProxyDomain = envScenarioBase + "_ITERATE_SRV";
   const char* envEdgeProxyDomainVal = std::getenv(envEdgeProxyDomain.c_str());
   cpc::string edgeProxyDomain = (envEdgeProxyDomainVal == 0 ? "" : envEdgeProxyDomainVal);
   if (!edgeProxyDomain.empty())
   {
      cpc::vector<CPCAPI2::Utils::DnsSrvRecord> results;
      mDnsClient->getDnsSrvRecords(1 /* 1 is SIP */, edgeProxyDomain, results);
      InfoLog(<< "DNS SRV lookup of " << edgeProxyDomain << " returned results: " << results.size());

      int ithreadpoolthreadid = 0;
      for (const CPCAPI2::Utils::DnsSrvRecord& srvResult : results)
      {
         resip::Data accountDomain(srvResult.target.c_str(), srvResult.target.size());
         if (mMapContextToRunner.count(accountDomain.c_str()) == 0)
         {
            RegistrationScenario* sdk = new RegistrationScenario(envScenarioBase, "", accountDomain.c_str(), ithreadpoolthreadid++%CPCAPI2_CONF_NUM_SDK_REACTORS, this, &mReactor, mPhone, mMediaReactorFactory);
            mMapContextToRunner[accountDomain] = sdk;
            setRunnerLoglineItems(sdk->getPhoneMainThreadId(), { kRegistrationScenarioName, srvResult.target.c_str() });
            sdk->startTask(std::bind(&SdkManager::runnerTaskCompleteCb, this, std::placeholders::_1));
         }
      }

      // check to see if existing registration scenario should be shutdown and removed if it
      // no longer exists in DNS
      auto itContext = mMapContextToRunner.begin();
      for (; itContext != mMapContextToRunner.end(); )
      {
         if (!boost::starts_with(itContext->first.c_str(), "CPE_SCENARIO_"))
         {
            if (!recordExists(results, itContext->first))
            {
               if (RegistrationScenario* sdk = dynamic_cast<RegistrationScenario*>(itContext->second))
               {
                  InfoLog(<< "DNS refresh: " << itContext->first.c_str() << " no longer exists in SRV results, stopping registration tests against this");
                  const bool force = false;
                  sdk->shutdown(force);
                  mMapContextToRunner.erase(itContext++);
                  continue;
               }
               else
               {
                  ErrLog(<< "Failed casting mMapContextToRunner with key " << itContext->first.c_str() << " to RegistrationScenario");
               }
            }
         }
         ++itContext;  
      }
   }
   else
   {
      std::string context = envScenarioBase;
      if (mMapContextToRunner.count(envScenarioBase.c_str()) == 0)
      {
         RegistrationScenario* sdk = new RegistrationScenario(envScenarioBase, "", "", mMapContextToRunner.size()%CPCAPI2_CONF_NUM_SDK_REACTORS, this, &mReactor, mPhone, mMediaReactorFactory);
         std::string obProxy = sdk->sipAcct0OutboundProxy();
         setRunnerLoglineItems(sdk->getPhoneMainThreadId(), { kRegistrationScenarioName, obProxy });
         mMapContextToRunner[context.c_str()] = sdk;
         sdk->startTask(std::bind(&SdkManager::runnerTaskCompleteCb, this, std::placeholders::_1));
      }
   }
}

void SdkManager::initOnToOn(const std::string& envScenarioBase)
{
   std::string envScenarioName = envScenarioBase + "_NAME";
   std::set<resip::Data> srvAccountDomains;

   if (NULL == std::getenv(envScenarioName.c_str()))
   {
      return;
   }

   // CPE_SCENARIO_0_iterate_srv
   std::string envEdgeProxyDomain = envScenarioBase + "_ITERATE_SRV";
   const char* envEdgeProxyDomainVal = std::getenv(envEdgeProxyDomain.c_str());
   cpc::string edgeProxyDomain = (envEdgeProxyDomainVal == 0 ? "" : envEdgeProxyDomainVal);
   if (!edgeProxyDomain.empty())
   {
      cpc::vector<CPCAPI2::Utils::DnsSrvRecord> results;
      mDnsClient->getDnsSrvRecords(1 /* 1 is SIP */, edgeProxyDomain, results);
      InfoLog(<< "DNS SRV lookup of " << edgeProxyDomain << " returned results: " << results.size());

      for (const CPCAPI2::Utils::DnsSrvRecord& srvResult : results)
      {
         resip::Data accountDomain(srvResult.target.c_str(), srvResult.target.size());
         srvAccountDomains.insert(accountDomain);
      }

      std::string context = envScenarioBase;
      if (mMapContextToRunner.count(envScenarioBase.c_str()) == 0)
      {
         Cpcapi2Runner* sdk = new Cpcapi2Runner(envScenarioBase, "", srvAccountDomains, mMapContextToRunner.size()%CPCAPI2_CONF_NUM_SDK_REACTORS, this, &mReactor, mPhone, mMediaReactorFactory, mNatServerInfo, mScreenshareBitrateConfig, mCameraVideoBitrateConfig, mMediaEncryptionMode);
         setRunnerLoglineItems(sdk->getPhoneMainThreadId(), { kOnToOnScenarioName });
         mMapContextToRunner[context.c_str()] = sdk;
         sdk->startTask(std::bind(&SdkManager::runnerTaskCompleteCb, this, std::placeholders::_1));
      }
      else
      {
         auto itContext = mMapContextToRunner.begin();
         for (; itContext != mMapContextToRunner.end(); ++itContext)
         {
            itContext->second->updateAccountDomainsFromSRV(srvAccountDomains);
         }
      }
   }
   else
   {
      std::string context = envScenarioBase;
      if (mMapContextToRunner.count(envScenarioBase.c_str()) == 0)
      {
         Cpcapi2Runner* sdk = new Cpcapi2Runner(envScenarioBase, "", std::set<resip::Data>(), mMapContextToRunner.size()%CPCAPI2_CONF_NUM_SDK_REACTORS, this, &mReactor, mPhone, mMediaReactorFactory, mNatServerInfo, mScreenshareBitrateConfig, mCameraVideoBitrateConfig, mMediaEncryptionMode);
         std::string obProxy = sdk->sipAcct0OutboundProxy();
         setRunnerLoglineItems(sdk->getPhoneMainThreadId(), { kOnToOnScenarioName, obProxy });
         mMapContextToRunner[context.c_str()] = sdk;
         sdk->startTask(std::bind(&SdkManager::runnerTaskCompleteCb, this, std::placeholders::_1));
      }
   }
}

void SdkManager::runnerTaskCompleteCb(RunnerTask* runner)
{
   runner->startTask(std::bind(&SdkManager::runnerTaskCompleteCb, this, std::placeholders::_1));
}

static std::string getISO8601TimestampWithMilliseconds() 
{
  auto now = std::chrono::system_clock::now();
  auto now_t = std::chrono::system_clock::to_time_t(now);
  auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()) % 1000;

  std::stringstream ss;
  ss << std::put_time(std::gmtime(&now_t), "%FT%T.");
  ss << std::setfill('0') << std::setw(3) << ms.count();
  ss << "Z";
  return ss.str();
}

bool SdkManager::operator()(const PhoneLogEvent2& logEvent)
{
   spdlog::level::level_enum spdlevel = LoglevelConverter::cpclevel2spdlevel(logEvent.level);
   
   nlohmann::json jsonLogObject{{"message", logEvent.message}, {"build", BUILD_STAMP}};
   jsonLogObject["level"] = LoglevelConverter::cpclevel2javastr(logEvent.level);
   jsonLogObject["timestamp"] = getISO8601TimestampWithMilliseconds();
   if (!mStaticLoglineItems.app.empty())
   {
      jsonLogObject["app"] = mStaticLoglineItems.app;
   }
   if (!mStaticLoglineItems.environmentName.empty())
   {
      jsonLogObject["environmentName"] = mStaticLoglineItems.environmentName;
   }
   if (!mStaticLoglineItems.environmentType.empty())
   {
      jsonLogObject["environmentType"] = mStaticLoglineItems.environmentType;
   }
   
   jsonLogObject["file"] = logEvent.file;
   jsonLogObject["line"] = logEvent.line;

   //if (!edgeProxy.empty())
   //{
   //   jsonLogObject["target"] = edgeProxy;
   //}

   if (std::optional<SdkManager::RunnerStaticLoglineItems> runnerLogContext = getRunnerLoglineItems(logEvent.sourceThreadId))
   {
      jsonLogObject["scenario"] = runnerLogContext->scenarioName;
      if (!runnerLogContext->target.empty())
      {
         jsonLogObject["target"] = runnerLogContext->target;
      }
   }
   
   spdlog::log(spdlevel, "{}", jsonLogObject.dump());

   return true;
}

void SdkManager::setRunnerLoglineItems(const std::string& runnerPhoneThreadId, const SdkManager::RunnerStaticLoglineItems& items)
{
   std::lock_guard<std::mutex> lck(mMapPhoneThreadIdToRunnerLoglineItemsMutex);
   mMapPhoneThreadIdToRunnerLoglineItems[runnerPhoneThreadId] = items;
}

std::optional<SdkManager::RunnerStaticLoglineItems> SdkManager::getRunnerLoglineItems(const std::string& runnerPhoneThreadId)
{
   std::lock_guard<std::mutex> lck(mMapPhoneThreadIdToRunnerLoglineItemsMutex);
   std::map<std::string, RunnerStaticLoglineItems>::iterator it = mMapPhoneThreadIdToRunnerLoglineItems.find(runnerPhoneThreadId);
   if (it != mMapPhoneThreadIdToRunnerLoglineItems.end())
   {
      return it->second;
   }

   return std::nullopt;
}

int SdkManager::onValidateLicensesSuccess(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ValidateLicensesSuccessEvent& args)
{
   return 0;
}

int SdkManager::onValidateLicensesFailure(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ValidateLicensesFailureEvent& args)
{
   return 0;
}

int SdkManager::onError(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ErrorEvent& args)
{
   return 0;
}

int SdkManager::onError(const cpc::string& sourceModule, const CPCAPI2::PhoneErrorEvent& args)
{
   return 0;
}

int SdkManager::onLicensingError(const CPCAPI2::LicensingErrorEvent& args)
{
   return 0;
}

int SdkManager::onStartupComplete(CPCAPI2::ConferenceBridge::ConferenceRegistrarHandle registrar, const CPCAPI2::ConferenceBridge::ConferenceRegistrarStartupResult& args)
{
   InfoLog(<< "ConferenceRegistrarHandler::onStartupComplete() - result: " << args.success << "; localIsLeader: " + args.localIsLeader);
   return 0;
}

int SdkManager::onNewLogin(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const CPCAPI2::JsonApi::NewLoginEvent& args)
{
   return 0;
}

int SdkManager::onConnectionClosed(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const CPCAPI2::JsonApi::ConnectionClosedEvent& args)
{
   return 0;
}

void SdkManager::writeSessionSummary()
{
}

void SdkManager::writeSessionSummaryImpl()
{
}

void SdkManager::writeConnectedEndpointSummary()
{
}

int SdkManager::onSetServerInfoResult(int requestHandle, const CPCAPI2::OrchestrationServer::SetServerInfoResult& args)
{
   return 0;
}

bool SdkManager::onHealthCheckRequest()
{
   // warning: arrives on HTTP server thread

   std::atomic_bool healthy = false;
   mReactor.execute(resip::resip_bind(&SdkManager::onHealthCheckRequestImpl, this, &healthy));
   return healthy;
}

void SdkManager::onHealthCheckRequestImpl(std::atomic_bool* healthy)
{
   *healthy = true;

   if (mMapContextToRunner.size() == 0)
   {
      *healthy = false;
   }
}

void SdkManager::onTimer(unsigned short timerId, void* appState)
{
}

void SdkManager::setLoggingTimer(int expires_ms)
{
}

void SdkManager::setLoggingTimerImpl(int expires_ms)
{
}

int SdkManager::nextTicketId()
{
   return mRestAsyncResultHandler.nextTicketId();
}

void SdkManager::postAsyncResult(int ticketId, const cpc::string& res)
{
   mRestAsyncResultHandler.setResult(ticketId, res);
}

void SdkManager::listNodes(const std::function<void(const cpc::vector<cpc::string>&)>& fn)
{
}

SdkManager::RestAsyncResult::RestAsyncResult(SdkManager* sdkMgr) : mSdkMgr(sdkMgr)
{
}

int SdkManager::RestAsyncResult::processRequest(const cpc::string& path, const cpc::string& body, const CPCAPI2::JsonApi::AuthTokenInfo& authTokenInfo, const cpc::vector<CPCAPI2::JsonApi::QueryStringParam>& queryString)
{
   int ticketId = -1;
   mRespCode = 404;

   for (const CPCAPI2::JsonApi::QueryStringParam& qsp : queryString)
   {
      if (qsp.name == "ticketId")
      {
         ticketId = resip::Data::from(qsp.value.c_str()).convertInt();
      }
   }

   if (ticketId != -1)
   {
      auto it = mResultMap.find(ticketId);
      if (it != mResultMap.end())
      {
         mNextResult = it->second;
         mResultMap.erase(it);
         mRespCode = 200;
      }
   }
   return 0;
}

cpc::string SdkManager::RestAsyncResult::output()
{
   cpc::string ret = mNextResult;
   mNextResult = "";
   if (ret.empty())
   {
      ret = "ERR";
   }
   return ret;
}

int SdkManager::RestAsyncResult::response_code()
{
   return mRespCode;
}

SdkManager::BuildInfo::BuildInfo(SdkManager* sdkMgr) : mSdkMgr(sdkMgr)
{
}

cpc::string SdkManager::BuildInfo::output()
{
   return BUILD_INFO_STRING;
}

int SdkManager::BuildInfo::response_code()
{
   return 200;
}

SdkManager::MaintMode::MaintMode(SdkManager* sdkMgr) : mSdkMgr(sdkMgr)
{
}

cpc::string SdkManager::MaintMode::output()
{
   mSdkMgr->mMaintenanceModeEnabled = true;
   CPCAPI2::ConferenceBridge::ConferenceRegistrar* confRegistrar = CPCAPI2::ConferenceBridge::ConferenceRegistrar::getInterface(mSdkMgr->mPhone);
   confRegistrar->shutdown();
   return "OK";
}

SdkManager::LoggingToggle::LoggingToggle(SdkManager* sdkMgr, bool enable) : mSdkMgr(sdkMgr), mEnable(enable)
{
}

cpc::string SdkManager::LoggingToggle::output()
{
   return "OK";
}

SdkManager::LoggingStatus::LoggingStatus(SdkManager* sdkMgr) : mSdkMgr(sdkMgr)
{
}

cpc::string SdkManager::LoggingStatus::output()
{
   return resip::Data::from(mSdkMgr->mIsLoggingEnabled).c_str();
}

SdkManager::SessionSummaryRequestHandler::SessionSummaryRequestHandler(SdkManager* sdkMgr)
   : mSdkMgr(sdkMgr)
{
}

cpc::string SdkManager::SessionSummaryRequestHandler::output()
{
   cpc::string retVal;
   {
      resip::Lock lck(mSdkMgr->mSessionSummaryMtx);
      retVal = mSdkMgr->mSessionSummary;
   }
   mSdkMgr->writeSessionSummary();
   return retVal;
}

SdkManager::ConnectedEndpointSummaryRequestHandler::ConnectedEndpointSummaryRequestHandler(SdkManager* sdkMgr)
   : mSdkMgr(sdkMgr)
{
}

cpc::string SdkManager::ConnectedEndpointSummaryRequestHandler::output()
{
   cpc::string retVal;
   {
      resip::Lock lck(mSdkMgr->mConnectedEndpointSummaryMtx);
      retVal = mSdkMgr->mConnectedEndpointSummary;
   }
   return retVal;
}

SdkManager::CrashRequest::CrashRequest(SdkManager* sdkMgr) : mSdkMgr(sdkMgr)
{
}

cpc::string SdkManager::CrashRequest::output()
{
   int* a = 0;
   *a = NULL;

   return cpc::string();
}

SdkManager::RaftListNodes::RaftListNodes(SdkManager* sdkMgr) : mSdkMgr(sdkMgr)
{
}

int SdkManager::RaftListNodes::processRequest(const cpc::string& path, const cpc::string& body, const CPCAPI2::JsonApi::AuthTokenInfo& authTokenInfo, const cpc::vector<CPCAPI2::JsonApi::QueryStringParam>& queryString)
{
   return 0;
}

cpc::string SdkManager::RaftListNodes::output()
{
   return resip::Data::from(mTicketId).c_str();
}

}
}
