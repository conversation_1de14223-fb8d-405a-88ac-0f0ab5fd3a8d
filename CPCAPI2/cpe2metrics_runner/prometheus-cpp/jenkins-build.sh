BUILD_DIR=_build_armv8
if [ "$BUILD_ARCH" == "x86_64" ]; then
  BUILD_DIR=_build_x86_64
fi

rm -rf $BUILD_DIR || true

if [ ! -d $BUILD_DIR ]
then
  mkdir $BUILD_DIR
fi

rm -rf _tmp || true
mkdir _tmp
cd _tmp
wget https://github.com/jupp0r/prometheus-cpp/releases/download/v1.1.0/prometheus-cpp-with-submodules.tar.gz
tar -zxvf prometheus-cpp-with-submodules.tar.gz
rm prometheus-cpp-with-submodules/CMakeLists.txt
cp -a prometheus-cpp-with-submodules/* ../

cd ../$BUILD_DIR

PROMETHEUS_INSTALL_PREFIX=/CPCAPI2/core/cpe2metrics_runner/prometheus-cpp/installed
CPCAPI2_TOOLCHAIN=../../../core/projects/cmake/toolchains/linux-clang-armv8.cmake
if [ "$BUILD_ARCH" == "x86_64" ]; then
  CPCAPI2_TOOLCHAIN=../../../core/projects/cmake/toolchains/linux-clang-x86_64.cmake
  PROMETHEUS_INSTALL_PREFIX=/CPCAPI2/core/cpe2metrics_runner/prometheus-cpp/installed_x86_64
fi

echo "BUILD_ARCH is $BUILD_ARCH, PROMETHEUS_INSTALL_PREFIX is $PROMETHEUS_INSTALL_PREFIX"
pwd

cmake -G Ninja .. -DBUILD_SHARED_LIBS=OFF -DENABLE_PUSH=OFF -DENABLE_COMPRESSION=OFF -DCMAKE_INSTALL_PREFIX:PATH=$PROMETHEUS_INSTALL_PREFIX -DCMAKE_TOOLCHAIN_FILE=$CPCAPI2_TOOLCHAIN
cmake --build .
cmake --install .
