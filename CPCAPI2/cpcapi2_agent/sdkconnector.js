function SdkConnector() {
	this.isActive = false;
	this.url = null;
	this.callback = null;
	this.exampleSocket = null;
}

SdkConnector.prototype.setUrl = function(_url) {
	this.url = _url;
	return this;
}

SdkConnector.prototype.setCallback = function(_callback) {
	this.callback = _callback;
	return this;
}

SdkConnector.prototype.isStopped = function () {
	return !this.isActive;
}

function makeWsUrl(port) {
    var l = window.location;
    return ((l.protocol === "https:") ? "wss://" : "ws://") + l.hostname + ":" + port;
}

SdkConnector.prototype.start = function() {
	//Log.info("Downloader", "Starting file download");
    var dl = this;
    this.exampleSocket = new WebSocket(makeWsUrl(9003));
	//this.exampleSocket.binaryType = "arraybuffer";
    this.exampleSocket.onopen = function (event) {
        // log something
		//Log.info("Downloader", "Connected to WebSocket server");
    };	
    this.exampleSocket.onmessage = function (event) {
        //Log.info("Downloader", "Received data from WebSocket server (" + event.data.byteLength + " bytes)");
		dl.callback(event.data);
    }	
	this.isActive = true;
	return this;
}

SdkConnector.prototype.stop = function() {
	//Log.info("Downloader", "Stopping file download");
	this.isActive = false;
	return this;
}
