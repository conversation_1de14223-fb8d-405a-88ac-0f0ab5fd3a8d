ROOT_PATH = ../..
RESIP_PATH = $(ROOT_PATH)/shared/sipfoundry/main
RESIP_RECON_PATH = $(RESIP_PATH)/resip/recon
CXXFLAGS_WARNINGS = -Wno-deprecated-declarations
CXXFLAGS_OPTIMIZATIONS = 
CXXFLAGS_DEBUG = -g
CXXFLAGS_MACROS = -DUSE_SSL -DRESIP_USE_STL_STREAMS -DUSE_DTLS -DUSE_IPV6
INCLUDE_DIRS = -I$(RESIP_PATH)/reTurn -I$(RESIP_PATH)/reflow -I$(RESIP_PATH) -I$(ROOT_PATH)/external/boost/include -I$(ROOT_PATH)/external/OpenSSL/openssl-current/include -I$(ROOT_PATH)/shared/libsrtp/srtp/include -I$(ROOT_PATH)/shared/libsrtp/srtp/crypto/include
CXXFLAGS = -std=c++0x -frtti -fexceptions $(CXXFLAGS_WARNINGS) $(CXXFLAGS_OPTIMIZATIONS) $(CXXFLAGS_DEBUG) $(CXXFLAGS_MACROS) $(INCLUDE_DIRS)
RESIP_RECON_SRCS = \
    ApplicationTimers.cxx AVOfferAnswerSession.cxx BasicUserAgent.cxx BridgeMixer.cxx Conversation.cxx ConversationManager.cxx  \
    ConversationParticipantAssignment.cxx ConversationProfile.cxx DefaultDialogSet.cxx DtmfEvent.cxx LocalParticipant.cxx  \
    MediaEvent.cxx MediaResourceCache.cxx MediaResourceParticipant.cxx MediaStreamEvent.cxx Participant.cxx ReconSubsystem.cxx  \
    RegistrationManager.cxx RelatedConversationSet.cxx RemoteParticipant.cxx RemoteParticipantDialogSet.cxx RTPPortAllocator.cxx  \
    UserAgent.cxx UserAgentClientSubscription.cxx UserAgentDialogSetFactory.cxx UserAgentMasterProfile.cxx  \
    UserAgentRegistration.cxx UserAgentServerAuthManager.cxx sdp/Sdp.cxx sdp/SdpCandidate.cxx sdp/SdpCandidatePair.cxx  \
    sdp/SdpCodec.cxx sdp/SdpHelperResip.cxx sdp/SdpMediaLine.cxx
SRCS = $(RESIP_RECON_SRCS:%=$(RESIP_RECON_PATH)/%)
OBJS = $(SRCS:%.cxx=%.o)
TARGET = libresip_recon.a

.PHONY: all
all : $(OBJS) $(TARGET)

.PHONY: rebuild
rebuild : clean all

.PHONY: clean
clean :
	$(RM) $(OBJS)
	$(RM) $(TARGET)

$(OBJS) : $(SRCS)

$(TARGET) : $(OBJS)
	$(AR) rcs $(TARGET) $(OBJS)

# Rule to generate a .o file from the .c file.
%.o : %.cxx
	$(CXX) -c $(CXXFLAGS) $< -o $@
