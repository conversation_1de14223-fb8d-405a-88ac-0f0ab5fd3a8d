ROOT_PATH = ../..
RESIP_PATH = $(ROOT_PATH)/shared/sipfoundry/main
RESIP_DUM_PATH = $(RESIP_PATH)/resip/dum
CXXFLAGS_WARNINGS = -Wno-deprecated-declarations
CXXFLAGS_OPTIMIZATIONS = 
CXXFLAGS_DEBUG = -g
CXXFLAGS_MACROS =  -DRESIP_USE_STL_STREAMS -DUSE_IPV6 -DUSE_SSL
INCLUDE_DIRS = -I$(RESIP_PATH) -I$(ROOT_PATH)/external/OpenSSL/openssl-current/include
CXXFLAGS = -frtti -fexceptions $(CXXFLAGS_WARNINGS) $(CXXFLAGS_OPTIMIZATIONS) $(CXXFLAGS_DEBUG) $(CXXFLAGS_MACROS) $(INCLUDE_DIRS)
RESIP_DUM_SRCS = \
    AppDialog.cxx AppDialogSet.cxx AppDialogSetFactory.cxx BaseCreator.cxx BaseSubscription.cxx BaseUsage.cxx CertMessage.cxx \
    ChallengeInfo.cxx ClientAuthExtension.cxx ClientAuthManager.cxx ClientInviteSession.cxx ClientOutOfDialogReq.cxx \
    ClientPagerMessage.cxx ClientPublication.cxx ClientRegistration.cxx ClientSubscription.cxx ContactInstanceRecord.cxx \
    DefaultServerReferHandler.cxx DestroyUsage.cxx Dialog.cxx DialogEventInfo.cxx DialogEventStateManager.cxx DialogId.cxx \
    DialogSet.cxx DialogSetId.cxx DialogUsage.cxx DialogUsageManager.cxx DumDecrypted.cxx DumFeature.cxx DumFeatureChain.cxx \
    DumFeatureMessage.cxx DumHelper.cxx DumProcessHandler.cxx DumThread.cxx DumTimeout.cxx EncryptionRequest.cxx Handle.cxx \
    Handled.cxx HandleException.cxx HandleManager.cxx HttpGetMessage.cxx HttpProvider.cxx IdentityHandler.cxx \
    InMemoryRegistrationDatabase.cxx InviteSession.cxx InviteSessionCreator.cxx InviteSessionHandler.cxx KeepAliveManager.cxx \
    KeepAliveTimeout.cxx MasterProfile.cxx MergedRequestKey.cxx MergedRequestRemovalCommand.cxx NetworkAssociation.cxx \
    NonDialogUsage.cxx OutgoingEvent.cxx OutOfDialogReqCreator.cxx PagerMessageCreator.cxx Profile.cxx PublicationCreator.cxx \
    RedirectManager.cxx RegistrationCreator.cxx RegistrationHandler.cxx ServerAuthManager.cxx ServerInviteSession.cxx \
    ServerOutOfDialogReq.cxx ServerPagerMessage.cxx ServerPublication.cxx ServerRegistration.cxx ServerSubscription.cxx \
    SubscriptionCreator.cxx SubscriptionHandler.cxx SubscriptionState.cxx TargetCommand.cxx UserAuthInfo.cxx UserProfile.cxx \
    ssl/EncryptionManager.cxx
SRCS = $(RESIP_DUM_SRCS:%=$(RESIP_DUM_PATH)/%)
OBJS = $(SRCS:%.cxx=%.o)
TARGET = libresip_dum.a

.PHONY: all
all : $(OBJS) $(TARGET)

.PHONY: rebuild
rebuild : clean all

.PHONY: clean
clean :
	$(RM) $(OBJS)
	$(RM) $(TARGET)

$(OBJS) : $(SRCS)

$(TARGET) : $(OBJS)
	$(AR) rcs $(TARGET) $(OBJS)

# Rule to generate a .o file from the .c file.
%.o : %.cxx
	$(CXX) -c $(CXXFLAGS) $< -o $@
