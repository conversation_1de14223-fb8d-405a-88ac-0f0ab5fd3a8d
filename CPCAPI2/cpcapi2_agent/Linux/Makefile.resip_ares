ROOT_PATH = ../..
RESIP_PATH = $(ROOT_PATH)/shared/sipfoundry/main
RESIP_ARES_PATH = $(RESIP_PATH)/rutil/dns/ares
CXXFLAGS_WARNINGS = -Wno-deprecated-declarations
CXXFLAGS_OPTIMIZATIONS = 
CXXFLAGS_DEBUG = -g
CXXFLAGS_MACROS = -DUSE_ARES -DRESIP_USE_STL_STREAMS -DUSE_IPV6 -DUSE_SSL 
LDFLAGS = -L.
INCLUDE_DIRS = -I$(RESIP_ARES_PATH)
CXXFLAGS = $(CXXFLAGS_WARNINGS) $(CXXFLAGS_OPTIMIZATIONS) $(CXXFLAGS_DEBUG) $(CXXFLAGS_MACROS) $(INCLUDE_DIRS)
RESIP_SRCS = \
        ares__close_sockets.c ares__get_hostent.c ares__read_line.c \
        ares_destroy.c ares_expand_name.c ares_fds.c ares_free_errmem.c \
        ares_free_hostent.c ares_free_string.c ares_gethostbyaddr.c \
        ares_gethostbyname.c ares_init.c ares_mkquery.c ares_parse_a_reply.c \
        ares_parse_ptr_reply.c ares_process.c ares_query.c ares_search.c \
        ares_send.c ares_strerror.c ares_timeout.c ares_local.c
SRCS = $(RESIP_SRCS:%=$(RESIP_ARES_PATH)/%)
OBJS = $(SRCS:%.c=%.o)
HEADERS = $(SRCS:%.c=%.h)
TARGET = libresip_ares.a

.PHONY: all
all : $(OBJS) $(TARGET)

.PHONY: rebuild
rebuild : clean all

.PHONY: clean
clean :
	$(RM) $(OBJS)
	$(RM) $(TARGET)

$(OBJS) : $(SRCS)

$(TARGET) : $(OBJS)
	$(AR) rcs $(TARGET) $(OBJS)

# Rule to generate a .o file from the .c file.
%.o : %.c
	$(CC) -c $(CXXFLAGS) $< -o $@
