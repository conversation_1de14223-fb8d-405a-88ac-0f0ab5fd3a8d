ROOT_PATH = ../..
RESIP_RETURN_BOOST_PATH = $(ROOT_PATH)/external/boost
CXXFLAGS_WARNINGS = -Wno-deprecated-declarations
CXXFLAGS_OPTIMIZATIONS = 
CXXFLAGS_DEBUG = -g
CXXFLAGS_MACROS = -DASIO_DISABLE_IOCP -DASIO_ENABLE_CANCELIO -DRESIP_USE_STL_STREAMS -DUSE_IPV6 -DUSE_SSL
INCLUDE_DIRS = -I$(RESIP_RETURN_BOOST_PATH)/include 
CXXFLAGS = -std=c++0x -frtti -fexceptions $(CXXFLAGS_WARNINGS) $(CXXFLAGS_OPTIMIZATIONS) $(CXXFLAGS_DEBUG) $(CXXFLAGS_MACROS) $(INCLUDE_DIRS)
RESIP_RETURN_BOOST_SRCS = \
   system/error_code.cpp
SRCS = $(RESIP_RETURN_BOOST_SRCS:%=$(RESIP_RETURN_BOOST_PATH)/sources/%)
OBJS = $(SRCS:%.cpp=%.o)
TARGET = libresip_reTurn_boost.a

.PHONY: all
all : $(OBJS) $(TARGET)

.PHONY: rebuild
rebuild : clean all

.PHONY: clean
clean :
	$(RM) $(OBJS)
	$(RM) $(TARGET)

$(OBJS) : $(SRCS)

$(TARGET) : $(OBJS)
	$(AR) rcs $(TARGET) $(OBJS)

# Rule to generate a .o file from the .c file.
%.o : %.cpp
	$(CXX) -c $(CXXFLAGS) $< -o $@
