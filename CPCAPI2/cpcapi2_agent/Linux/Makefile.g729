ROOT_PATH = ../..
LIBG729_PATH = $(ROOT_PATH)/external/G729_G722
CXXFLAGS_WARNINGS = -Wno-deprecated-declarations
CXXFLAGS_OPTIMIZATIONS = 
CXXFLAGS_DEBUG = -g
CXXFLAGS_MACROS = 
LDFLAGS = -L.
INCLUDE_DIRS = $(LIBG729_PATH)/source
CXXFLAGS = -std=c++0x -fpermissive $(CXXFLAGS_WARNINGS) $(CXXFLAGS_OPTIMIZATIONS) $(CXXFLAGS_DEBUG) $(CXXFLAGS_MACROS) $(INCLUDE_DIRS)
LIBG729_SRCS = \
   G729lib.cpp
SRCS = $(LIBG729_SRCS:%=$(LIBG729_PATH)/source/%)
OBJS = $(SRCS:%.cpp=%.o)
TARGET = libg729.a

.PHONY: all
all : $(OBJS) $(TARGET)

.PHONY: rebuild
rebuild : clean all

.PHONY: clean
clean :
	$(RM) $(OBJS)
	$(RM) $(TARGET)

$(OBJS) : $(SRCS)

$(TARGET) : $(OBJS)
	$(AR) rcs $(TARGET) $(OBJS)

# Rule to generate a .o file from the .c file.
%.o : %.cpp
	$(CXX) -c $(CXXFLAGS) $< -o $@
