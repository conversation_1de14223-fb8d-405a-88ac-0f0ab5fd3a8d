#include "stdafx.h"
#include "Cpcapi2Runner.h"

#include "brand_branded.h"
#include "Logger.h"

#include <interface/experimental/xmpp/XmppAccountJsonApi.h>
#include <interface/experimental/xmpp/XmppChatJsonApi.h>

#include <fstream>

using namespace CPCAPI2;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::SipConversation;
using namespace CPCAPI2::Media;
using namespace CPCAPI2::PeerConnection;

namespace CPCAPI2
{
namespace Agent
{
Cpcapi2Runner::Cpcapi2Runner() 
   : mPhone(NULL)
{
}

Cpcapi2Runner::~Cpcapi2Runner()
{
   ThreadIf::join();
}

void Cpcapi2Runner::run()
{
   ThreadIf::run();
}

void Cpcapi2Runner::shutdown()
{
   if (mPhone != NULL)
   {
      mPhone->interruptProcess();
   }
   ThreadIf::shutdown();
}

CPCAPI2::PhoneInternal* Cpcapi2Runner::getPhoneInternalForLogger() const
{
   return mPhone;
}

void Cpcapi2Runner::thread()
{
   mPhone = dynamic_cast<PhoneInternal*>(CPCAPI2::Phone::create());
   LicenseInfo licenseInfo;
   licenseInfo.licenseKey = "lkj";
   licenseInfo.licenseDocumentLocation = "C:\\Temp";
   licenseInfo.licenseAor = "";
   mPhone->initialize(licenseInfo, this);
   mPhone->setLoggingEnabled("cpcagent", true);

   // need to explicitly create the modules we want to expose
   CPCAPI2::XmppAccount::XmppAccountJsonApi::getInterface(mPhone);
   CPCAPI2::XmppChat::XmppChatJsonApi::getInterface(mPhone);

   //InfoLog(<< "Cpcapi2Runner::thread: cpcapi2 rtp proxy phone instance initialized.");

   mJsonApiServer = CPCAPI2::JsonApi::JsonApiServer::getInterface(mPhone);
   mJsonApiServer->start();
   mJsonApiServer->setJsonApiUserContext((CPCAPI2::JsonApi::JsonApiUserHandle)UINT_MAX, mPhone);

   //mWebStreamer.StartServer(mPhone);

   while (!isShutdown())
   {
      CPCAPI2::XmppAccount::XmppAccountManager::getInterface(mPhone)->process(CPCAPI2::XmppAccount::XmppAccountManager::kBlockingModeNonBlocking);
      mPhone->process(CPCAPI2::Phone::kBlockingModeNonBlocking);
      resip::sleepMilliseconds(20);
   }

   mJsonApiServer->shutdown();

   //mWebStreamer.StopServer();
   //InfoLog(<< "Cpcapi2Runner::thread: cpcapi2 rtp proxy shutting down!");

   mPhone->process(100);

   //InfoLog(<< "Cpcapi2Runner::thread: cpcapi2 rtp proxy is shutdown!");
}

int Cpcapi2Runner::onError(const cpc::string& sourceModule, const CPCAPI2::PhoneErrorEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onLicensingError(const CPCAPI2::LicensingErrorEvent& args)
{
   return 0;
}


}
}
