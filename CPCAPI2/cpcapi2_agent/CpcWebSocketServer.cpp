#include "stdafx.h"
#include "CpcWebSocketServer.h"

#include <thread>
#include <future>
#include <iostream>
#include <fstream>

using namespace CPCAPI2::Media;

namespace CPCAPI2
{
namespace Agent
{
CpcWebSocketServer::CpcWebSocketServer()
   : mPhone(NULL), mServerThread(NULL)
{
}

CpcWebSocketServer::~CpcWebSocketServer()
{
}

void CpcWebSocketServer::StartServer(CPCAPI2::Phone* phone)
{
   mPhone = phone;
   mServerThread = new std::thread([&]() {
      try {
         // Set logging settings
         mWebSockServer.set_access_channels(websocketpp::log::alevel::all);
         mWebSockServer.clear_access_channels(websocketpp::log::alevel::frame_payload);

         // Initialize Asio
         mWebSockServer.init_asio();

         // Register our message handler
         mWebSockServer.set_open_handler(std::bind(&CpcWebSocketServer::on_open, this, &mWebSockServer, std::placeholders::_1));
         mWebSockServer.set_close_handler(std::bind(&CpcWebSocketServer::on_close, this, &mWebSockServer, std::placeholders::_1));
         mWebSockServer.set_message_handler(std::bind(&CpcWebSocketServer::on_message, this, &mWebSockServer, std::placeholders::_1, std::placeholders::_2));

         // Listen on port 9002
         mWebSockServer.listen(9003);

         // Start the server accept loop
         mWebSockServer.start_accept();

         // Start the ASIO io_service run loop
         mWebSockServer.run();

         std::cout << "web streamer thread exit..." << std::endl;
      }
      catch (websocketpp::exception const & e) {
         std::cout << e.what() << std::endl;
      }
      catch (...) {
         std::cout << "other exception" << std::endl;
      }
   });
}

void CpcWebSocketServer::StopServer()
{
   mWebSockServer.get_io_service().post([&]() {
      websocketpp::lib::error_code ec;
      //mWebSockServer.stop_listening(ec);
      mWebSockServer.stop();
   });
   mServerThread->join();
   delete mServerThread;
}

void CpcWebSocketServer::on_message(server* s, websocketpp::connection_hdl hdl, message_ptr msg)
{
}

void CpcWebSocketServer::on_open(server* s, websocketpp::connection_hdl hdl)
{
   std::cout << "accepted a websocket connection" << std::endl;
   mConnections[hdl] = std::shared_ptr<ConnInfo>(new ConnInfo(hdl));
   //mWebSockServer.send(hdl, &mFtypMoov[0], mFtypMoov.size(), websocketpp::frame::opcode::binary);
   //mConnections[hdl]->nextSeqNum++;
}

void CpcWebSocketServer::on_close(server* s, websocketpp::connection_hdl hdl)
{
   std::cout << "websocket connection closed" << std::endl;
   mConnections.erase(hdl);
}

}
}
