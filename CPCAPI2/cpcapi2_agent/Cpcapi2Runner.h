#pragma once

// SDK includes
#include <cpcapi2.h>
#include <jsonapi/JsonApiServer.h>
#include <phone/PhoneInternal.h>

// rutil includes
#include <rutil/ThreadIf.hxx>

#include "CpcWebSocketServer.h"

namespace CPCAPI2
{
namespace Agent
{
class Cpcapi2Runner : public CPCAPI2::PhoneErrorHandler,
                      public resip::ThreadIf
{
public:
   Cpcapi2Runner();
   virtual ~Cpcapi2Runner();

   // ThreadIf
   virtual void run();
   virtual void shutdown();

   // PhoneErrorHandler
   virtual int onError(const cpc::string& sourceModule, const CPCAPI2::PhoneErrorEvent& args);
   virtual int onLicensingError(const CPCAPI2::LicensingErrorEvent& args);

private:
   virtual void thread();
   CPCAPI2::PhoneInternal* getPhoneInternalForLogger() const;

private:
   CPCAPI2::PhoneInternal* mPhone;
   CPCAPI2::JsonApi::JsonApiServer* mJsonApiServer;

   CpcWebSocketServer mWebStreamer;
};
}
}
