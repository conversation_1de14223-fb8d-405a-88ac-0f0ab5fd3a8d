#include "stdafx.h"
#include "SdkManager.h"
#include "Cpcapi2ServerLicense.h"
#include "rutil/ConfigParse.hxx"

#include <cpcapi2.h>

#include <fstream>

#define CPCAPI2_CONF_NUM_SDK_REACTORS 4

using namespace CPCAPI2;
using namespace CPCAPI2::Licensing;

namespace CPCAPI2
{
namespace OrchestrationServer
{
class MyConfigParse : public resip::ConfigParse
{
private:
   void parseCommandLine(int argc, char** argv, int skipCount = 0) {}
   void printHelpText(int argc, char **argv) {}
};


SdkManager::SdkManager() : mOrchServer(NULL)
{
}

SdkManager::~SdkManager()
{
}

void SdkManager::run()
{
   mReactor.start();
   mReactor.post(resip::resip_bind(&SdkManager::appInit, this));
}

void SdkManager_sdkCallbackHook(void* context)
{
   SdkManager* cpcRunner = (SdkManager*)context;
   cpcRunner->handleSdkCallback();
}

void SdkManager::handleSdkCallback()
{
   mReactor.post(resip::resip_bind(&SdkManager::handleSdkCallbackImpl, this));
}

void SdkManager::handleSdkCallbackImpl()
{
   mPhone->process(CPCAPI2::Phone::kBlockingModeNonBlocking);
   mOrchServer->process(CPCAPI2::AuthServer::AuthServer::kBlockingModeNonBlocking);
   mJsonApiServer->process(CPCAPI2::JsonApi::JsonApiServer::kBlockingModeNonBlocking);
}

void SdkManager::appInit()
{
   mPhone = CPCAPI2::PhoneInternal::create(0);
   LicenseInfo licenseInfo;
   licenseInfo.licenseKey = "lkj";
   licenseInfo.licenseDocumentLocation = "C:\\Temp";
   licenseInfo.licenseAor = "";
   mPhone->setCallbackHook(SdkManager_sdkCallbackHook, this);
   mPhone->initialize(licenseInfo, this, false);
   mPhone->setLoggingEnabled("cpcapi2webclient", true);
   
   MyConfigParse fileConfig;
   fileConfig.parseConfig(0, NULL, "cpcapi2_orchestration_server.config");      

   mLicensingMgr = LicensingClientManager::getInterface(mPhone);
   mLicensingMgr->setCallbackHook(SdkManager_sdkCallbackHook, this);
   LicensingClientHandle client = mLicensingMgr->create();
   mLicensingMgr->setHandler(client, this);

   LicensingClientSettings settings;
   settings.licenseKeys.push_back(LICENSE_KEY_1);
   settings.licenseDocumentLocation = LICENSE_DOCUMENT_LOCATION;
   settings.provisioningId = LICENSE_AOR;
   settings.osVersion = "Windows for Workgroups 3.1";
   settings.keySourceUrl = "http://license.counterpath.com/generate";
   mLicensingMgr->applySettings(client, settings);
   mLicensingMgr->validateLicenses(client);

   mJsonApiServer = CPCAPI2::JsonApi::JsonApiServer::getInterface(mPhone);
   mJsonApiServer->setHandler(this);
   mJsonApiServer->setCallbackHook(SdkManager_sdkCallbackHook, this);

   JsonApi::JsonApiServerConfig jsonApiServCfg;
   jsonApiServCfg.websocketPort = 9003;
   fileConfig.getConfigValue("jsonapi_websocket_port", jsonApiServCfg.websocketPort);
   
   jsonApiServCfg.httpPort = 18080;
   fileConfig.getConfigValue("jsonapi_http_port", jsonApiServCfg.httpPort);
  
   resip::Data httpsCertificateFilePath;
   if (fileConfig.getConfigValue("orch_server_http_cert_file_path", httpsCertificateFilePath))
   {
      jsonApiServCfg.httpsCertificateFilePath = httpsCertificateFilePath.c_str();
   }

   resip::Data httpsPrivateKeyFilePath;
   if (fileConfig.getConfigValue("orch_server_http_priv_key_file_path", httpsPrivateKeyFilePath))
   {
      jsonApiServCfg.httpsPrivateKeyFilePath = httpsPrivateKeyFilePath.c_str();
   }

   resip::Data httpsDiffieHellmanParamsFilePath;
   if (fileConfig.getConfigValue("orch_service_http_dh_params_file_path", httpsDiffieHellmanParamsFilePath))
   {
      jsonApiServCfg.httpsDiffieHellmanParamsFilePath = httpsDiffieHellmanParamsFilePath.c_str();
   }

   jsonApiServCfg.certificateFilePath = "p256-public-key.spki"; // pub key used to validate auth tokens
   mJsonApiServer->start(jsonApiServCfg);

   mOrchServer = CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(mPhone);
   mOrchServer->setHandler(this);
   mOrchServer->setCallbackHook(SdkManager_sdkCallbackHook, this);
   CPCAPI2::OrchestrationServer::OrchestrationServerConfig serverConfig;
   serverConfig.redisIp = "mock"; // "**************";
   serverConfig.redisPort = 6379;
   mOrchServer->start(serverConfig);

   initFromSettings();
}

void SdkManager::shutdown()
{
   //mTTSApi->shutdown();
   mReactor.execute(resip::resip_bind(&SdkManager::appShutdown, this));
   mReactor.stop();
}

void SdkManager::appShutdown()
{
   mOrchServer->shutdown();
}

void SdkManager::join()
{
   mReactor.join();
}

void SdkManager::initFromSettings()
{
}

int SdkManager::onError(const cpc::string& sourceModule, const CPCAPI2::PhoneErrorEvent& args)
{
   return 0;
}

int SdkManager::onLicensingError(const CPCAPI2::LicensingErrorEvent& args)
{
   return 0;
}

int SdkManager::onValidateLicensesSuccess(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ValidateLicensesSuccessEvent& args)
{
   return 0;
}

int SdkManager::onValidateLicensesFailure(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ValidateLicensesFailureEvent& args)
{
   return 0;
}

int SdkManager::onError(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ErrorEvent& args)
{
   return 0;
}

int SdkManager::onNewLogin(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const CPCAPI2::JsonApi::NewLoginEvent& args)
{   
   // args.authToken - the auth token has already been validated by the SDK
   // resip::Data authTokenStr(args.authToken.c_str(), args.authToken.size());

   cpc::vector<cpc::string> permissions;
   permissions.push_back("*");
   mJsonApiServer->setJsonApiUserContext(jsonApiUser, mPhone, permissions);

   CPCAPI2::JsonApi::LoginResultEvent loginResult;
   loginResult.success = true;
   mJsonApiServer->sendLoginResult(jsonApiUser, loginResult);

   return 0;
}
}
}
