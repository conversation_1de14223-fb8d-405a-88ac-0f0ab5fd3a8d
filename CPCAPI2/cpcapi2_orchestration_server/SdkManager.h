#pragma once

#include <cpcapi2.h>
#include <jsonapi/JsonApiServer.h>
#include <jsonapi/JsonApiServerHandler.h>
#include <orchestration_server/OrchestrationServer.h>
#include <orchestration_server/OrchestrationServerHandler.h>
#include <phone/PhoneInternal.h>

// rutil includes
#include <rutil/MultiReactor.hxx>
#include <rutil/Data.hxx>

namespace CPCAPI2
{
namespace OrchestrationServer
{
class SdkManager : public CPCAPI2::PhoneErrorHandler,
                   public CPCAPI2::Licensing::LicensingClientHandler,
                   public CPCAPI2::JsonApi::JsonApiServerHandler,
                   public CPCAPI2::OrchestrationServer::OrchestrationServerHandler
{
public:
   SdkManager();
   ~SdkManager();

   void run();
   void shutdown();
   void join();

   void handleSdkCallback();

   // PhoneErrorHandler
   virtual int onError(const cpc::string& sourceModule, const CPCAPI2::PhoneErrorEvent& args);
   virtual int onLicensingError(const CPCAPI2::LicensingErrorEvent& args);

   // LicensingClientHandler
   virtual int onValidateLicensesSuccess(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ValidateLicensesSuccessEvent& args);
   virtual int onValidateLicensesFailure(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ValidateLicensesFailureEvent& args);
   virtual int onError(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ErrorEvent& args);

   // JsonApiServerHandler
   virtual int onNewLogin(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiuser, const CPCAPI2::JsonApi::NewLoginEvent& args);
   virtual int onSessionState(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiuser, const CPCAPI2::JsonApi::SessionStateEvent& args) { return 0; };
   virtual int onReLogin(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const CPCAPI2::JsonApi::ReLoginEvent& args) { return 0; }
   virtual int onPreLogout(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const CPCAPI2::JsonApi::PreLogoutEvent& args) { return 0; }
   virtual int onLogout(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const CPCAPI2::JsonApi::LogoutEvent& args) { return 0; }

   // OrchestrationServerHandler
   virtual int onSetServerInfoResult(int requestHandle, const SetServerInfoResult& args) { return 0; }
   virtual int onRequestServiceResult(int requestHandle, const RequestServiceResult& args) { return 0; }
   virtual int onQueryServersResult(int requestHandle, const QueryServersResult& args) { return 0; }
   virtual int onQueryServerTtlResult(int requestHandle, const QueryServerTtlResult& args) { return 0; }
   virtual int onQueryServerUsersResult(int requestHandle, const QueryServerUsersResult& args) { return 0; }

private:
   void appInit();
   void appShutdown();
   void handleSdkCallbackImpl();

   void initFromSettings();

private:
   resip::MultiReactor mReactor;
   CPCAPI2::PhoneInternal* mPhone;
   CPCAPI2::Licensing::LicensingClientManager* mLicensingMgr;
   CPCAPI2::JsonApi::JsonApiServer* mJsonApiServer;
   CPCAPI2::OrchestrationServer::OrchestrationServer* mOrchServer;
};
}
}
