AR=gcc-ar
NM=gcc-nm
RANLIB=gcc-ranlib
ARCH = $(shell arch)
# CXX = /opt/gcc-4.8.2/bin/g++
CXXFLAGS_WARNINGS = -Wno-deprecated-declarations
CXXFLAGS_OPTIMIZATIONS = -O3 -DNDEBUG -flto
CXXFLAGS_DEBUG = -g
CXXFLAGS_MACROS = -DGLOOX_USE_SHA1_OPENSSL -DCPCAPI2_SERVER_ORCH -DCPCAPI2_LINUX -DUSE_IPV6 -DUSE_ARES -DUSE_SSL -DUSE_DTLS -DASIO_ENABLE_CANCELIO -DBOOST_ASIO_ENABLE_CANCELIO -DWEBRTC_RECON_PNG_SUPPORT -D__MSRP_FLOW_C__ -DRESIP_USE_STL_STREAMS -DCPCAPI2_INCLUDE_UNRELEASED_HEADERS -DCPSI_MEDIA_CODEC_SPEEX -DCPCAPI2_NO_INTERNAL_PROXY -DXMLSEC_NO_XSLT -DXMLSEC_CRYPTO_OPENSSL -DXMLSEC_STATIC -DWEBSOCKETPP_STRICT_MASKING -DRESIP_USE_POLL_FDSET -DCPCAPI2_USE_RUTIL_REACTOR -DWEBRTC_POSIX
#CC = $(CXX)

ifeq ($(DISABLE_WSS_JSON_SERVER), 1)
else
CXXFLAGS_MACROS += -DUSE_WSS_JSON_SERVER=1
endif

CPCAPI2_ROOT = ../..
CPCAPI2_IMPL = $(CPCAPI2_ROOT)/CPCAPI2/impl
CPCAPI2_INTERFACE = $(CPCAPI2_ROOT)/CPCAPI2/interface
SHARED_DIR = $(CPCAPI2_ROOT)/shared
EXTERNAL_DIR = $(CPCAPI2_ROOT)/external
EXTERNAL_LIBS_DIR = $(CPCAPI2_ROOT)/../linux_libs
INCLUDE_DIRS = \
	-I$(EXTERNAL_DIR)/openh264/codec/api/svc \
	-I$(EXTERNAL_LIBS_DIR)/boost/include \
	-I$(EXTERNAL_DIR)/libmsrp \
	-I$(EXTERNAL_DIR)/libxml/include \
	-I$(EXTERNAL_LIBS_DIR)/openssl/include \
	-I$(EXTERNAL_LIBS_DIR)/curl/include \
	-I$(EXTERNAL_DIR)/v8/include \
	-I$(EXTERNAL_DIR)/websocketpp \
	-I$(EXTERNAL_DIR)/speex/Linux/include \
	-I$(EXTERNAL_DIR)/speex/include \
	-I$(EXTERNAL_DIR)/SILK/interface \
	-I$(EXTERNAL_DIR)/xmlsec/include \
	-I$(EXTERNAL_DIR)/rapidjson \
	-I$(EXTERNAL_DIR)/lodepng \
	-I$(EXTERNAL_DIR)/Beamr/H265/inc \
	-I$(EXTERNAL_DIR)/Simple-Web-Server \
	-I$(EXTERNAL_LIBS_DIR)/mozjpeg/include \
	-I$(EXTERNAL_DIR)/snappy \
	-I$(EXTERNAL_LIBS_DIR)/curlpp/include \
	-I$(SHARED_DIR)/sipfoundry/main \
	-I$(SHARED_DIR)/sipfoundry/main/resip/recon \
	-I$(SHARED_DIR)/sipfoundry/main/reflow \
	-I$(SHARED_DIR)/sipfoundry/main/reTurn \
	-I$(SHARED_DIR)/sipfoundry/main/repro \
	-I$(SHARED_DIR)/libsrtp/srtp/include \
	-I$(SHARED_DIR)/libsrtp/srtp/crypto/include \
	-I$(SHARED_DIR)/WebRTCv/trunk \
	-I$(SHARED_DIR)/WebRTCv/trunk/webrtc \
	-I$(SHARED_DIR)/WebRTCv/trunk/webrtc/voice_engine/include \
	-I$(SHARED_DIR)/WebRTCv/trunk/webrtc/video_engine/include \
	-I$(SHARED_DIR)/WebRTCv/trunk/third_party/libyuv/include \
	-I$(SHARED_DIR)/webrtc_recon \
	-I$(SHARED_DIR)/gloox/src \
	-I$(CPCAPI2_ROOT)/CPCAPI2 \
    -I$(CPCAPI2_IMPL) \
	-I$(CPCAPI2_INTERFACE)/public \
	-I$(CPCAPI2_INTERFACE)/experimental
LIBRARY_DIRECTORIES = -L. \
	-L$(EXTERNAL_LIBS_DIR)/codecs/video/openh264/lib \
	-L$(EXTERNAL_DIR)/speex/Linux/libspeex/$(ARCH) \
	-L$(EXTERNAL_LIBS_DIR)/curl/lib \
	-L$(EXTERNAL_LIBS_DIR)/curlpp/lib \
	-L$(EXTERNAL_LIBS_DIR)/nghttp2/lib \
	-L$(EXTERNAL_LIBS_DIR)/openssl/lib \
        -L$(EXTERNAL_LIBS_DIR)/zlib/lib \
        -L$(EXTERNAL_DIR)/libxml/Linux/libxml/lib/$(ARCH) \
	-L$(EXTERNAL_DIR)/SILK/Linux/$(ARCH) \
	-L$(EXTERNAL_DIR)/xmlsec/lib/linux/$(ARCH) \
	-L$(EXTERNAL_LIBS_DIR)/webrtc/Release \
	-L$(EXTERNAL_DIR)/bv32 \
	-L$(EXTERNAL_DIR)/SILK \
	-L$(EXTERNAL_DIR)/v8/build/Release/lib \
	-L$(EXTERNAL_LIBS_DIR)/boost/lib \
	-L$(EXTERNAL_DIR)/lodepng \
	-L$(EXTERNAL_LIBS_DIR)/mozjpeg/lib \
	-L$(EXTERNAL_LIBS_DIR)/soci/lib \
	-L$(CPCAPI2_ROOT)/projects/linux
WEBRTC_LIBS = \
	-laudio_coding_module \
	-laudio_conference_mixer \
	-laudio_processing \
	-laudio_processing_sse2 \
	-lbitrate_controller \
	-lCNG \
	-lwebrtc_common \
	-lcommon_audio \
	-lcommon_audio_sse2 \
	-lcommon_video \
	-lG711 \
	-lG722 \
	-ljpeg_turbo \
	-lmedia_file \
	-lneteq \
	-lopus \
	-liLBC \
	-lpaced_sender \
	-lPCM16B \
	-lremote_bitrate_estimator \
	-lsystem_wrappers \
	-lvideo_processing \
	-lvideo_processing_sse2 \
	-lvoice_engine \
	-lvpx \
	-lvpx_intrinsics_avx \
	-lvpx_intrinsics_avx2 \
	-lvpx_intrinsics_mmx \
	-lvpx_intrinsics_sse2 \
	-lvpx_intrinsics_ssse3 \
	-lvpx_intrinsics_sse4_1 \
	-lwebrtc_i420 \
	-lwebrtc_opus \
	-lwebrtc_vp8 \
	-lwebrtc_vp9 \
	-lwebrtc_utility \
	-lwebrtc \
	-lyuv \
	-laudio_device \
	-lrtp_rtcp \
	-lwebrtc_video_coding \
	-lvideo_coding_utility \
	-lvideo_engine_core \
	-lvideo_render_module \
	-lvideo_render_module_internal_impl \
	-lvideo_capture_module \
	-lvideo_capture_module_internal_impl \
	-lrtc_base_approved \
	-lrtc_base \
	-lmetrics_default \
	-laudio_encoder_interface \
	-laudio_decoder_interface \
	-lwebrtc_speex \
	-lwebrtc_SILK \
	-lred \
	-liSAC \
	-liSACFix \
	-lopenmax_dl
LIBRARY_DEPENDENCIES = \
	-Wl,--start-group \
	-lturbojpeg \
	-llodepng \
	-lxml \
	-lxmlsec \
	-lcurl \
	-lcurlpp \
	-lnghttp2 \
	$(WEBRTC_LIBS) \
	-lcpcapi2_static \
	-lresip_dum \
	-lresip_stack \
	-lresip_recon \
	-lresip_reflow \
	-lresip_reTurn \
	-lresip_rutil \
	-lresip_ares \
	-lssl \
	-lcrypto \
	-lsrtp \
	-lg729 \
	-lmsrp \
	-lspeex \
	-lSKP_SILK_SDK \
	-lz \
	-ldl \
	-lrt \
	-lXext \
	-lX11 \
	-lXfixes \
	-lXdamage \
	-lXcomposite \
	-lgloox \
	-lcommon \
	-ldecoder \
	-lencoder \
	-lopenh264 \
	-lprocessing \
	-lz \
	-lboost_date_time-gcc62-mt-1_65_1 \
	-lboost_system-gcc62-mt-1_65_1 \
	-lsoci_core \
	-lsoci_sqlite3 \
	-lsqlite3_cpc \
	-lredisclient_cpc \
	-Wl,--end-group

CXXFLAGS = -std=c++11 -fPIC $(CXXFLAGS_WARNINGS) $(CXXFLAGS_OPTIMIZATIONS) $(CXXFLAGS_DEBUG) $(CXXFLAGS_MACROS) $(INCLUDE_DIRS)
CFLAGS = -fPIC $(CXXFLAGS_WARNINGS) $(CXXFLAGS_OPTIMIZATIONS) $(CXXFLAGS_DEBUG) $(CXXFLAGS_MACROS) $(INCLUDE_DIRS)

SRCS = $(wildcard $(CPCAPI2_ROOT)/cpcapi2_orchestration_server/*.cpp )
OBJS = $(patsubst %.cc,%.o,$(patsubst %.c,%.o,$(patsubst %.cpp,%.o,$(patsubst %.cxx,%.o,$(SRCS)))))
HEADERS = $(patsubst %.cc,%.h,$(patsubst %.c,%.h,$(patsubst %.cpp,%.h,$(patsubst %.cxx,%.h,$(SRCS)))))
LDFLAGS = -O3 -flto -Wl,-O3 -Wl,-flto -pthread -DCPCAPI2_USE_RUTIL_REACTOR $(LIBRARY_DIRECTORIES) $(LIBRARY_DEPENDENCIES)
TARGET = cpcapi2_orchestration_server

.PHONY: all
all : $(TARGET)

.PHONY: rebuild
rebuild : clean all

.PHONY: deps
deps :
	$(MAKE) -C ../../projects/linux CPCAPI2_SERVER_ORCH=1

.PHONY: depsclean localclean clean

depsclean :
	$(MAKE) -C ../../projects/linux clean

localclean :
	$(RM) $(OBJS)
	$(RM) $(TARGET)

clean :	depsclean localclean

$(OBJS) : $(SRCS)

$(TARGET) : $(OBJS) deps
	$(CXX) $(OBJS) -o $(TARGET) $(LDFLAGS)

# Rule to generate a .o file from the .cxx file. (non-standard file suffix)
%.o : %.cxx
	$(CXX) -c $(CXXFLAGS) $< -o $@

# Rule to generate a .o file from the .cc file. (non-standard file suffix)
%.o : %.cc
	$(CXX) -c $(CXXFLAGS) $< -o $@
