#!/bin/sh

usage() {
    echo "-d [service_dir]:  (REQUIRED) the directory context in which the service will run"
    echo "-s [service_exe]:  (REQUIRED) the relative path (to service_dir) of the executable"
    echo "-b:                (OPTIONAL) begin watching the service specified by -s"
    echo "-e:                (OPTIONAL) end watching the service specified by -s"
    echo "-c:                (OPTIONAL) (ASSUMED) perform a test on the service to see"
    echo "                   whether it is running. service may be killed and restarted"
    echo "                   if it is non-responsive. If the service is not running,"
    echo "                   it will be started."
    exit -1
}

SERVICE_DIR=""
SERVICE_EXE=""
SCRIPT_MODE="check"
PS_PATH="/bin/ps"
NOHUP_PATH="/bin/nohup"
PGREP_PATH="/bin/pgrep"
CRONTAB_PATH="/bin/crontab"

while getopts "d:s:bec" opt ; do
    case $opt in
    d)
        SERVICE_DIR=${OPTARG}
        ;;
    s)
        SERVICE_EXE=${OPTARG}
        ;;
    b)
        SCRIPT_MODE="begin"
        ;;
    e)
        SCRIPT_MODE="end"
        ;;
    c)
        SCRIPT_MODE="check"
        ;;
    \?)
        usage
        ;;
    esac
done

if [ "${SERVICE_DIR}" == "" -o "${SERVICE_EXE}" == "" ] ; then
    usage
fi

SERVICE_NAME=`basename ${SERVICE_EXE}`
SERVICE_PID=""

# change directory to the service location
cd ${SERVICE_DIR}
if [ "${?}" != "0" ] ; then
    echo "Couldn't change directory to service path. Aborting"
    exit -1
fi

# The script should be colocated with the service
if [ ! -x "${0}" ] ; then
    echo "Couldn't find the ${0} script in ${SERVICE_DIR}"
    exit -1
fi

if [ "${SCRIPT_MODE}" == "check" ] ; then

    echo "Testing whether ${SERVICE_NAME} is running"

    # Invoke ps to determine whether the service is currently running.
    # ps -Ao pid,user,args |grep ${SERVICE_NAME}
    # Is this the best way? what about..

    # Look for a .pid file for the service in the current directory
    if [ -r "${SERVICE_NAME}.pid" ] ; then

        # We found one, get the number from it so we can do process control
        SERVICE_PID=`cat ${SERVICE_NAME}.pid`

        # Check to see if that pid is active
        ${PGREP_PATH} -F ${SERVICE_NAME}.pid > /dev/null 2>&1
        if [ "${?}" == "0" ] ; then
            # One or more processes matched the criteria.

            # Still running, do nothing for now. Later, implement
            # a test to see if the process is still active and not stuck.
            echo "Found the process, everything is fine."

        elif [ "${?}" == "1" ] ; then

            # TODO: service arguments
            echo "running: ${NOHUP_PATH} ./${SERVICE_EXE} > /dev/null 2>&1 &"
            ${NOHUP_PATH} ./${SERVICE_EXE} > /dev/null 2>&1 &
            echo "${!}" > ./${SERVICE_NAME}.pid

        else

            # Some other problem with pgrep
            echo "Problem determining whether the process is running, aborting."

        fi

    else

        # No PID file, process is not launching, or not launched by us.
        # TODO: service arguments
        echo "running: ${NOHUP_PATH} ./${SERVICE_EXE} > /dev/null 2>&1 &"
        ${NOHUP_PATH} ./${SERVICE_EXE} > /dev/null 2>&1 &
        echo "${!}" > ./${SERVICE_NAME}.pid

    fi

elif [ "${SCRIPT_MODE}" == "begin" ] ; then

    echo "Adding crontab entries..."
    CRONTAB_OUTPUT=`${CRONTAB_PATH} -l 2> /dev/null`
    if [ "$?" == "1" ] ; then

        #No crontab for this user. install one.
        # For now just check every 5 minutes.
        echo "*/5 * * * * ( cd ${SERVICE_DIR} ; ${0} -c -d ${SERVICE_DIR} -s ${SERVICE_EXE} > /dev/null 2> /dev/null )" | ${CRONTAB_PATH}

    fi

    if [ "$?" == "0" ] ; then

        # Existing crontab entry. Must append our entry but only
        # if our entry isn't already present in the crontab file.
        #
        # For now just check every 5 minutes.
        echo ${CRONTAB_OUTPUT} | grep "${0}" > /dev/null 2>&1
        if [ "${?}" != "0" ] ; then

            echo ${CRONTAB_OUTPUT} && echo "*/5 * * * * ( cd ${SERVICE_DIR} ; ${0} -c -d ${SERVICE_DIR} -s ${SERVICE_EXE} > /dev/null 2> /dev/null )" | ${CRONTAB_PATH}

        fi
        
    fi


elif [ "${SCRIPT_MODE}" == "end" ] ; then

    echo "Removing crontab entries..."
    CRONTAB_OUTPUT=`${CRONTAB_PATH} -l 2> /dev/null`
    if [ "${?}" == "1" ] ; then
        # Nothing to do
        exit 0
    fi

    CRONTAB_OUTPUT=`echo "${CRONTAB_OUTPUT}" | grep -v -- "-d ${SERVICE_DIR} -s ${SERVICE_EXE}"`
    echo "${CRONTAB_OUTPUT}" | ${CRONTAB_PATH}

fi
