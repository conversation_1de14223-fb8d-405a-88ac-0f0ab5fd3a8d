#include "NativeMemoryPressure.h"



NativeMemoryPressure::NativeMemoryPressure()
{
	_memoryAllocated = 0;
}

NativeMemoryPressure::!NativeMemoryPressure()
{
	if (_memoryAllocated > 0)
	{
		System::GC::RemoveMemoryPressure(_memoryAllocated);
	}
	_memoryAllocated = 0;
}

void NativeMemoryPressure::Allocated(long long bytes)
{
	if (bytes > 0)
	{
		_memoryAllocated += bytes;
		System::GC::AddMemoryPressure(bytes);
	}
}
