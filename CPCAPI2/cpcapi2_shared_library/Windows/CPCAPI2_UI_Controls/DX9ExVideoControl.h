#pragma once
#include <memory>
#include <windows.h>
#include "../../../shared/WebRTCv/trunk/webrtc/modules/video_render/windows/WindowsVideoControl.h"

using namespace System;
using namespace System::Windows;
using namespace System::Windows::Controls;
using namespace System::Windows::Interop;
using namespace System::Runtime::InteropServices;

namespace CPCAPI2
{
	namespace UI
	{
		namespace WPF
		{
			private ref class DX9ExVideoControl :
				public Viewbox
			{
			public:
				DX9ExVideoControl();
				~DX9ExVideoControl();
				!DX9ExVideoControl();

				IntPtr Handle();

				void updateSize();

			private:
				void onRegisterRenderParamsChanged(WindowsVideoControl::IWindowsVideoControl* iVideoRenderWin);
				void onVideoSurfaceUpdated(void* surface, int width, int height);
				void onVideoFrameReady();

				delegate void RegisterRenderParamsChanged(WindowsVideoControl::IWindowsVideoControl*);
				delegate void VideoSurfaceUpdated(void*, int, int);
				delegate void VideoFrameReady();

				void onUnloaded(Object^ sender, RoutedEventArgs^ e);
				void onRendering(Object^ sender, EventArgs^ e);
				void updateWindow();

				delegate void UpdateWindow();
				delegate void OnVideoSurfaceUpdated(IntPtr);

				RegisterRenderParamsChanged^ _fpRegisterRenderParamsChanged;
				VideoSurfaceUpdated^ _fpVideoSurfaceUpdated;
				VideoFrameReady^ _fpVideoFrameReady;

				System::Windows::RoutedEventHandler^ _unloadedEventHandler;
				System::EventHandler^ _renderingEventHandler;

				CPCAPI2::WindowsVideoControl::WindowsVideoControlIdType _id;
				double _dpiX, _dpiY;
				Image^ _image;
				int _imageWidth, _imageHeight;
				D3DImage^ _d3DImage;
				WindowsVideoControl::WindowsVideoControlDetails* _holder;
				WindowsVideoControl::IWindowsVideoControl* _renderer;
				Object^ _rendererLock;
				IntPtr _surface;
				bool _videoSurfaceUpdated;
				bool _videoFrameReady;
				bool _renderError;
				bool _errorSettingWindow;
				GCHandle _memoryPressure; // Weak handle
			};
		}
	}
}
