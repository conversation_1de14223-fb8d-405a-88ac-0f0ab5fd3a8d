#include "DX9ExVideoControl.h"
#include "NativeMemoryPressure.h"

#define kTraceError 0x0004
#define kTraceWarning 0x0002
#define	kTraceDebug 0x0800

namespace CPCAPI2
{
	namespace UI
	{
		namespace WPF
		{
			DX9ExVideoControl::DX9ExVideoControl() :
				_id(sNextId++),
				_dpiX(96.0),
				_dpiY(96.0),
				_image(gcnew Image()),
				_holder(new WindowsVideoControl::WindowsVideoControlDetails()),
				_renderer(nullptr),
				_rendererLock(gcnew Object()),
				_surface(IntPtr(nullptr)),
				_videoSurfaceUpdated(false),
				_videoFrameReady(false),
				_renderError(false),
				_errorSettingWindow(true)
			{
				if (NULL == sNextId) // NULL isn't considered a valid id by the renderer
				{
					sNextId++;
				}

				_unloadedEventHandler = gcnew System::Windows::RoutedEventHandler(this, &DX9ExVideoControl::onUnloaded);
				_renderingEventHandler = gcnew System::EventHandler(this, &CPCAPI2::UI::WPF::DX9ExVideoControl::onRendering);

				this->Unloaded += _unloadedEventHandler;
				System::Windows::Media::CompositionTarget::Rendering += _renderingEventHandler;

				this->Child = _image;

				_fpRegisterRenderParamsChanged = gcnew RegisterRenderParamsChanged(this, &DX9ExVideoControl::onRegisterRenderParamsChanged);
				_fpVideoSurfaceUpdated = gcnew VideoSurfaceUpdated(this, &DX9ExVideoControl::onVideoSurfaceUpdated);
				_fpVideoFrameReady = gcnew VideoFrameReady(this, &DX9ExVideoControl::onVideoFrameReady);

				_holder->mode = CPCAPI2::WindowsVideoControl::kDirectX9Surface;

				_holder->RegisterRenderParamsChanged = static_cast<CPCAPI2::WindowsVideoControl::RegisterRenderParamsChanged>(Marshal::GetFunctionPointerForDelegate(_fpRegisterRenderParamsChanged).ToPointer());
				_holder->onVideoSurfaceUpdated = static_cast<CPCAPI2::WindowsVideoControl::VideoSurfaceUpdated>(Marshal::GetFunctionPointerForDelegate(_fpVideoSurfaceUpdated).ToPointer());
				_holder->onVideoFrameReady = static_cast<CPCAPI2::WindowsVideoControl::VideoFrameReady>(Marshal::GetFunctionPointerForDelegate(_fpVideoFrameReady).ToPointer());

				CPCAPI2::WindowsVideoControl::VideoControlList::addDX9ExVideoControl(_id, _holder);
			}

			DX9ExVideoControl::~DX9ExVideoControl()
			{
				this->!DX9ExVideoControl();
			}

			DX9ExVideoControl::!DX9ExVideoControl()
			{
				_holder->window = nullptr;
				_holder->RegisterRenderParamsChanged = nullptr;
				_holder->onVideoSurfaceUpdated = nullptr;
				_holder->onVideoFrameReady = nullptr;

				if (nullptr != _renderer)
				{
					_renderer->onVideoWindowChanged(_holder->window);
				}

				CPCAPI2::WindowsVideoControl::VideoControlList::removeDX9ExVideoControl(_id);

				_renderer = nullptr;
				delete _holder;
			}

			void DX9ExVideoControl::onRegisterRenderParamsChanged(CPCAPI2::WindowsVideoControl::IWindowsVideoControl* renderer)
			{
				try
				{
					System::Threading::Monitor::Enter(_rendererLock);
					_renderer = renderer;
					if (nullptr != _renderer)
					{
						_renderer->log(kTraceDebug, "DX9ExVideoControl::onRegisterRenderParamsChanged %f x %f", SystemParameters::VirtualScreenWidth, SystemParameters::VirtualScreenHeight);
						_renderer->setScreenSize(SystemParameters::VirtualScreenWidth, SystemParameters::VirtualScreenHeight);
					}
				}
				finally
				{
					System::Threading::Monitor::Exit(_rendererLock);
				}
				this->Dispatcher->BeginInvoke(System::Windows::Threading::DispatcherPriority::Normal, gcnew UpdateWindow(this, &DX9ExVideoControl::updateWindow));
			}

			void DX9ExVideoControl::updateWindow()
			{
				Window^ win = Window::GetWindow(this);
				if (nullptr != win)
				{
					HWND window = (HWND)WindowInteropHelper(win).Handle.ToInt32();
					if (nullptr != window && (_errorSettingWindow || window != _holder->window))
					{
						try
						{
							System::Threading::Monitor::Enter(_rendererLock);

							_holder->window = window;

							if (nullptr != _renderer)
							{
								_renderer->log(kTraceDebug, "DX9ExVideoControl::updateWindow %d", _holder->window);
								int err = _renderer->onVideoWindowChanged(_holder->window);
								if (S_OK != err)
								{
									char errMsg[128];
									sprintf_s(errMsg, 128, "DX9ExVideoControl::updateWindow error %d", err);
									_renderer->log(kTraceError, errMsg);
									_errorSettingWindow = true;
								}
								else
								{
									_errorSettingWindow = false;
								}
							}
						}
						finally
						{
							System::Threading::Monitor::Exit(_rendererLock);
						}
					}
				}
				if (!_errorSettingWindow)
				{
					updateSize();
				}
			}

			void DX9ExVideoControl::updateSize()
			{
				Canvas^ canvas = safe_cast<Canvas^>(this->Parent);
				this->Width = canvas->ActualWidth;
				this->Height = canvas->ActualHeight;
			}

			void DX9ExVideoControl::onUnloaded(Object^ sender, RoutedEventArgs^ e)
			{
				this->Unloaded -= _unloadedEventHandler;
				System::Windows::Media::CompositionTarget::Rendering -= _renderingEventHandler;
			}

			void DX9ExVideoControl::onRendering(Object^ sender, EventArgs^ e)
			{
				if (nullptr == _d3DImage)
				{
					PresentationSource^ source = PresentationSource::FromVisual(this);
					_dpiX = 96.0 * source->CompositionTarget->TransformToDevice.M11;
					_dpiY = 96.0 * source->CompositionTarget->TransformToDevice.M22;

					_d3DImage = gcnew D3DImage(_dpiX, _dpiY);
					_image->Source = _d3DImage;
					updateWindow();
				}

				if (_errorSettingWindow)
				{
					updateWindow();
				}

				if ((_videoSurfaceUpdated || _videoFrameReady) && nullptr != _renderer)
				{
					try
					{
						System::Threading::Monitor::Enter(_rendererLock);

						if (nullptr != _renderer)
						{
							_videoFrameReady = false;
							int err = _renderer->UpdateRenderSurface(0, 0);
							_renderError = (0 != err);
							if (_renderError)
							{
								char errMsg[128];
								sprintf_s(errMsg, 128, "DX9ExVideoControl::onRendering error rendering frame %d", err);
								_renderer->log(kTraceWarning, errMsg);
							}
						}

						_d3DImage->Lock();
						_image->Width = _imageWidth;
						_image->Height = _imageHeight;

						if (_videoSurfaceUpdated && IntPtr::Zero != _surface)
						{
							try
							{
								_d3DImage->SetBackBuffer(D3DResourceType::IDirect3DSurface9, _surface, true);
							}
							catch (ArgumentException^ e)
							{
								IntPtr err = Marshal::StringToHGlobalAnsi(e->Message);

								int len = snprintf(NULL, 0, "DX9ExVideoControl::onRendering %s", (char*)err.ToPointer());
								char* errMsg = new char[len];
								snprintf(errMsg, len, "DX9ExVideoControl::onRendering %s", (char*)err.ToPointer());
								_renderer->log(kTraceError, errMsg);
								delete[] errMsg;

								Marshal::FreeHGlobal(err);
								_errorSettingWindow = true;
								return;
							}

							_videoSurfaceUpdated = false;
							if (IntPtr::Zero == _surface)
							{
								_d3DImage->Unlock();
								return;
							}

							if (!_memoryPressure.IsAllocated || nullptr == _memoryPressure.Target)
							{
								NativeMemoryPressure^ memoryPressure = gcnew NativeMemoryPressure();
								_memoryPressure = GCHandle::Alloc(memoryPressure, GCHandleType::WeakTrackResurrection);
							}
							((NativeMemoryPressure^)_memoryPressure.Target)->Allocated(_d3DImage->PixelWidth * _d3DImage->PixelHeight * 4 /*D3DFMT_A8R8G8B88*/);
						}

						// Setting the actual dirty rect had no noticable improvement in CPU usage so for simplicity set whole image as dirty
						if (IntPtr::Zero != _surface)
						{
							_d3DImage->AddDirtyRect(Int32Rect(0, 0, _d3DImage->PixelWidth, _d3DImage->PixelHeight));
						}
						_d3DImage->Unlock();
					}
					finally
					{
						System::Threading::Monitor::Exit(_rendererLock);
					}
				}
				else if (_renderError)
				{
					try
					{
						System::Threading::Monitor::Enter(_rendererLock);

						if (nullptr != _renderer)
						{
							_renderError = (0 != _renderer->UpdateRenderSurface(0, 0));
							if (!_renderError)
							{
								_renderer->log(kTraceWarning, "DX9ExVideoControl::onRendering recreated video objects.");
							}
						}
					}
					finally
					{
						System::Threading::Monitor::Exit(_rendererLock);
					}
				}
			}

			// This doesn't need to be an IntPtr, but leaving as IntPtr for backwards compatiblity reasons
			IntPtr DX9ExVideoControl::Handle()
			{
				return IntPtr((Int64)_id);
			}

			void DX9ExVideoControl::onVideoSurfaceUpdated(void* surface, int width, int height)
			{
				if (_surface.ToPointer() != surface)
				{
					_surface = IntPtr(surface);
					_videoSurfaceUpdated = true;
					_imageWidth = width;
					_imageHeight = height;
				}
			}

			void DX9ExVideoControl::onVideoFrameReady()
			{
				_videoFrameReady = true;
			}
		}
	}
}
