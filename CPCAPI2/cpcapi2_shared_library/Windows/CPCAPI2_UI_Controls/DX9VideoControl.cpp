#include "DX9VideoControl.h"
#include "NativeMemoryPressure.h"

#include <d3d9.h>
#include <dxvahd.h>

#pragma comment(lib, "dxva2.lib")
#pragma comment(lib, "d3d9.lib")       // located in DirectX SDK

namespace CPCAPI2
{
	namespace UI
	{
		namespace WPF
		{
			DX9VideoControl::DX9VideoControl()
			{
				_loadedEventHandler = gcnew System::Windows::RoutedEventHandler(this, &DX9VideoControl::onLoaded);
				_sizeChangedEventHandler = gcnew System::Windows::SizeChangedEventHandler(this, &DX9VideoControl::onSizeChanged);

				this->Loaded += _loadedEventHandler;
				this->SizeChanged += _sizeChangedEventHandler;
			}

			DX9VideoControl::~DX9VideoControl()
			{
				this->!DX9VideoControl();
			}

			DX9VideoControl::!DX9VideoControl()
			{
			}

			// This doesn't need to be an IntPtr, but leaving as IntPtr for backwards compatiblity reasons
			IntPtr DX9VideoControl::Handle()
			{
				if (nullptr != _dx9exVideoControl)
				{
					return _dx9exVideoControl->Handle();
				}
				else if (nullptr != _dxvaVideoControl)
				{
					return _dxvaVideoControl->Handle();
				}
				else
				{
					return (IntPtr)-1;
				}
			}

			void DX9VideoControl::SetMode(const int mode)
			{
				sMode = mode;
			}


			void DX9VideoControl::onSizeChanged(Object^ sender, SizeChangedEventArgs^ e)
			{
				if (nullptr != _dx9exVideoControl)
				{
					_dx9exVideoControl->updateSize();;
				}
			}

			void DX9VideoControl::onLoaded(Object^ sender, RoutedEventArgs^ e)
			{
				if (nullptr == _dx9exVideoControl && nullptr == _dxvaVideoControl)
				{
					if (kAutoMode == sMode)
					{
						detectVideoMode();
					}

					if (kDX9Mode == sMode)
					{
						_dx9exVideoControl = gcnew DX9ExVideoControl();
						this->Children->Add(_dx9exVideoControl);
					}
					else if (kDXVAHDMode == sMode)
					{
						_dxvaVideoControl = gcnew DXVAVideoControl();
						this->Children->Add(_dxvaVideoControl);
					}
				}
			}

			void DX9VideoControl::detectVideoMode()
			{
				HWND window;
				Window^ win = Window::GetWindow(this);
				if (nullptr != win)
				{
					window = (HWND)WindowInteropHelper(win).Handle.ToInt32();
				}
				else
				{
					return;
				}

				IDirect3D9Ex* _pD3D = NULL;
				if (FAILED(Direct3DCreate9Ex(D3D_SDK_VERSION, &_pD3D)))
				{
						// No supported mode
						return;
				}

				D3DPRESENT_PARAMETERS d3dpp = {0};
				d3dpp.BackBufferWidth  = 0;
				d3dpp.BackBufferHeight = 0;
				d3dpp.BackBufferFormat           = D3DFMT_X8R8G8B8;
				d3dpp.BackBufferCount            = 1;
				d3dpp.SwapEffect                 = D3DSWAPEFFECT_DISCARD;
				d3dpp.hDeviceWindow              = window;
				d3dpp.Windowed                   = TRUE;
				d3dpp.Flags                      = D3DPRESENTFLAG_VIDEO;
				d3dpp.FullScreen_RefreshRateInHz = D3DPRESENT_RATE_DEFAULT;
				d3dpp.PresentationInterval       = D3DPRESENT_INTERVAL_ONE;

				//
				// Mark the back buffer lockable if software DXVA2 could be used.
				// This is because software DXVA2 device requires a lockable render target
				// for the optimal performance.
				//
				d3dpp.Flags |= D3DPRESENTFLAG_LOCKABLE_BACKBUFFER;

				IDirect3DDevice9Ex* _pd3dDevice = NULL;
				HRESULT hr = _pD3D->CreateDeviceEx(D3DADAPTER_DEFAULT,
																	 D3DDEVTYPE_HAL,
																	 window,
																	 D3DCREATE_FPU_PRESERVE |
																	 D3DCREATE_MULTITHREADED |
																	 D3DCREATE_SOFTWARE_VERTEXPROCESSING,
																	 &d3dpp,
																	 NULL,
																	 &_pd3dDevice);

				if (FAILED(hr))
				{
					if (_pD3D != NULL)
					{
						_pD3D->Release();
						_pD3D = NULL;
					}
						// No supported mode
						return;
				}

				DXVAHD_CONTENT_DESC videoDesc;
				videoDesc.InputFrameFormat = DXVAHD_FRAME_FORMAT_PROGRESSIVE;
				videoDesc.InputFrameRate.Numerator = 60;
				videoDesc.InputFrameRate.Denominator = 1;
				videoDesc.InputWidth = 1920;
				videoDesc.InputHeight = 1080;
				videoDesc.OutputFrameRate.Numerator = 60;
				videoDesc.OutputFrameRate.Denominator = 1;
				videoDesc.OutputWidth = 1920;
				videoDesc.OutputHeight = 1080;

				IDXVAHD_Device* _pDXVAHD = NULL;
				if (FAILED(DXVAHD_CreateDevice(_pd3dDevice, &videoDesc, DXVAHD_DEVICE_USAGE_PLAYBACK_NORMAL, NULL, &_pDXVAHD)))
				{
					if (_pd3dDevice != NULL)
					{
						_pd3dDevice->Release();
						_pd3dDevice = NULL;
					}

					if (_pD3D != NULL)
					{
						_pD3D->Release();
						_pD3D = NULL;
					}

					sMode = kDX9Mode;
					return;
				}

				DXVAHD_VPDEVCAPS caps;
				ZeroMemory(&caps, sizeof(caps));

				if (FAILED(_pDXVAHD->GetVideoProcessorDeviceCaps(&caps)) ||
							0 >= caps.MaxInputStreams)
				{
					if (NULL != _pDXVAHD)
					{
						_pDXVAHD->Release();
						_pDXVAHD = NULL;
					}

					if (_pd3dDevice != NULL)
					{
						_pd3dDevice->Release();
						_pd3dDevice = NULL;
					}

					if (_pD3D != NULL)
					{
						_pD3D->Release();
						_pD3D = NULL;
					}
					sMode = kDX9Mode;
					return;
				}

				// Check the output format.
				D3DFORMAT* pFormats = new D3DFORMAT[caps.OutputFormatCount];
				if (FAILED(_pDXVAHD->GetVideoProcessorOutputFormats(caps.OutputFormatCount, pFormats)))
				{
					delete[] pFormats;
					if (NULL != _pDXVAHD)
					{
						_pDXVAHD->Release();
						_pDXVAHD = NULL;
					}

					if (_pd3dDevice != NULL)
					{
						_pd3dDevice->Release();
						_pd3dDevice = NULL;
					}

					if (_pD3D != NULL)
					{
						_pD3D->Release();
						_pD3D = NULL;
					}

					sMode = kDX9Mode;
					return;
				}

				bool sucess = false;
				for (uint32_t i = 0; i < caps.OutputFormatCount; i++)
				{
					if (pFormats[i] == D3DFMT_X8R8G8B8)
					{
						sucess = true;
						break;
					}
				}
				delete[] pFormats;
				pFormats = NULL;

				if (!sucess)
				{
					if (NULL != _pDXVAHD)
					{
						_pDXVAHD->Release();
						_pDXVAHD = NULL;
					}

					if (_pd3dDevice != NULL)
					{
						_pd3dDevice->Release();
						_pd3dDevice = NULL;
					}

					if (_pD3D != NULL)
					{
						_pD3D->Release();
						_pD3D = NULL;
					}

					sMode = kDX9Mode;
					return;
				}

				// Check the input formats.
				pFormats = new D3DFORMAT[caps.InputFormatCount];
				if (FAILED(_pDXVAHD->GetVideoProcessorInputFormats(caps.InputFormatCount, pFormats)))
				{
					if (NULL != _pDXVAHD)
					{
						_pDXVAHD->Release();
						_pDXVAHD = NULL;
					}

					if (_pd3dDevice != NULL)
					{
						_pd3dDevice->Release();
						_pd3dDevice = NULL;
					}

					if (_pD3D != NULL)
					{
						_pD3D->Release();
						_pD3D = NULL;
					}

					delete[] pFormats;
					sMode = kDX9Mode;
					return;
				}

				sucess = false;
				for (uint32_t i = 0; i < caps.InputFormatCount; i++)
				{
					if (pFormats[i] == (D3DFORMAT)MAKEFOURCC('N', 'V', '1', '2'))
					{
						sucess = true;
						break;
					}
				}
				delete[] pFormats;
				pFormats = NULL;

				if (!sucess)
				{
					if (NULL != _pDXVAHD)
					{
						_pDXVAHD->Release();
						_pDXVAHD = NULL;
					}

					if (_pd3dDevice != NULL)
					{
						_pd3dDevice->Release();
						_pd3dDevice = NULL;
					}

					if (_pD3D != NULL)
					{
						_pD3D->Release();
						_pD3D = NULL;
					}

					sMode = kDX9Mode;
					return;
				}

				// Create the VP device.
				DXVAHD_VPCAPS* pVPCaps = new DXVAHD_VPCAPS[caps.VideoProcessorCount];
				if (FAILED(_pDXVAHD->GetVideoProcessorCaps(caps.VideoProcessorCount, pVPCaps)))
				{
					if (NULL != _pDXVAHD)
					{
						_pDXVAHD->Release();
						_pDXVAHD = NULL;
					}
					delete[] pVPCaps;
					sMode = kDX9Mode;
					return;
				}

				IDXVAHD_VideoProcessor* _pDXVAVP = NULL;
				sucess = false;
				for (size_t i = 0; i < caps.VideoProcessorCount; i++)
				{
					if (!FAILED(_pDXVAHD->CreateVideoProcessor(&pVPCaps[0].VPGuid, &_pDXVAVP)))
					{
						sucess = true;
						break;
					}
				}

				if (NULL != _pDXVAVP)
				{
					_pDXVAVP->Release();
					_pDXVAVP = NULL;
				}

				delete[] pVPCaps;
				pVPCaps = NULL;

				if (NULL != _pDXVAHD)
				{
					_pDXVAHD->Release();
					_pDXVAHD = NULL;
				}

				if (_pd3dDevice != NULL)
				{
					_pd3dDevice->Release();
					_pd3dDevice = NULL;
				}

				if (_pD3D != NULL)
				{
					_pD3D->Release();
					_pD3D = NULL;
				}

				if (!sucess)
				{
					sMode = kDX9Mode;
					return;
				}

				sMode = kDXVAHDMode;
			}
		}
	}
}
