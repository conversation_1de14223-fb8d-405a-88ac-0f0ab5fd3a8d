#pragma once
#include <chrono>
#include <memory>
#include <windows.h>
#include "../../../shared/WebRTCv/trunk/webrtc/modules/video_render/windows/WindowsVideoControl.h"

using namespace System;
using namespace System::Windows;
using namespace System::Windows::Controls;
using namespace System::Windows::Interop;
using namespace System::Runtime::InteropServices;

namespace CPCAPI2
{
	namespace UI
	{
		namespace WPF
		{
			private ref class DXVAVideoControl :
				public Image
			{
			public:
				DXVAVideoControl();
				~DXVAVideoControl();
				!DXVAVideoControl();

				IntPtr Handle();

			private:
				void onRegisterRenderParamsChanged(WindowsVideoControl::IWindowsVideoControl* iVideoRenderWin);
				void onVideoSurfaceUpdated(void* surface, uint32_t width, uint32_t height);

				delegate void RegisterRenderParamsChanged(WindowsVideoControl::IWindowsVideoControl* renderer);
				delegate void VideoSurfaceUpdated(void*, uint32_t, uint32_t);
				delegate void VideoFrameReady();

				void onUnloaded(Object^ sender, RoutedEventArgs^ e);
				void onRendering(Object^ sender, EventArgs^ e);

				delegate void UpdateWindow();
				delegate void OnVideoSurfaceUpdated(IntPtr);

				RegisterRenderParamsChanged^ _fpRegisterRenderParamsChanged;
				VideoSurfaceUpdated^ _fpVideoSurfaceUpdated;
				VideoFrameReady^ _fpVideoFrameReady;

				System::Windows::RoutedEventHandler^ _unloadedEventHandler;
				System::EventHandler^ _renderingEventHandler;

				CPCAPI2::WindowsVideoControl::WindowsVideoControlIdType _id;
				double _dpiX, _dpiY;
				D3DImage^ _d3DImage;
				WindowsVideoControl::WindowsVideoControlDetails* _holder;
				WindowsVideoControl::IWindowsVideoControl* _renderer;
				Object^ _rendererLock;
				Object^ _surfaceLock;
				IntPtr _newSurface;
				IntPtr _currentSurface;
				GCHandle _memoryPressure; // Weak handle
			};
		}
	}
}
