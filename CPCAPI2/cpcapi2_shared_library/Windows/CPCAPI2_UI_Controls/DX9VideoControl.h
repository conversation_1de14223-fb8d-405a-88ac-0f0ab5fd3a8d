#pragma once
#include <memory>
#include <windows.h>
#include "DX9ExVideoControl.h"
#include "DXVAVideoControl.h"

using namespace System;
using namespace System::Windows;
using namespace System::Windows::Controls;
using namespace System::Windows::Interop;
using namespace System::Runtime::InteropServices;

namespace CPCAPI2
{
	namespace UI
	{
		namespace WPF
		{
			public ref class DX9VideoControl :
				public Canvas
			{
			public:
				static const int kAutoMode = 0;
				static const int kDX9Mode = 1;
				static const int kDXVAHDMode = 2;

				static void SetMode(const int mode);

				DX9VideoControl();
				~DX9VideoControl();
				!DX9VideoControl();

				IntPtr Handle();

			private:
				static int sMode = kAutoMode;

				void onLoaded(Object^ sender, RoutedEventArgs^ e);
				void onSizeChanged(Object^ sender, SizeChangedEventArgs^ e);

				void detectVideoMode();

				System::Windows::RoutedEventHandler^ _loadedEventHandler;
				System::Windows::SizeChangedEventHandler^ _sizeChangedEventHandler;

				DX9ExVideoControl^ _dx9exVideoControl = nullptr;
				DXVAVideoControl^ _dxvaVideoControl = nullptr;
			};
		}
	}
}
