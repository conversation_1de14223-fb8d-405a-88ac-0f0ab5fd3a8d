#include "DXVAVideoControl.h"
#include "NativeMemoryPressure.h"

#include <d3d9.h>

#define kTraceError 0x0004
#define kTraceWarning 0x0002
#define	kTraceDebug 0x0800

namespace CPCAPI2
{
	namespace UI
	{
		namespace WPF
		{
			DXVAVideoControl::DXVAVideoControl() :
				_id(sNextId++),
				_dpiX(96.0),
				_dpiY(96.0),
				_holder(new WindowsVideoControl::WindowsVideoControlDetails()),
				_renderer(nullptr),
				_rendererLock(gcnew Object()),
				_surfaceLock(gcnew Object()),
				_newSurface(IntPtr(nullptr)),
				_currentSurface(IntPtr(nullptr))
			{
				if (NULL == sNextId) // NULL isn't considered a valid id by the renderer
				{
					sNextId++;
				}

				_unloadedEventHandler = gcnew System::Windows::RoutedEventHandler(this, &DXVAVideoControl::onUnloaded);
				_renderingEventHandler = gcnew System::EventHandler(this, &CPCAPI2::UI::WPF::DXVAVideoControl::onRendering);

				this->Unloaded += _unloadedEventHandler;
				System::Windows::Media::CompositionTarget::Rendering += _renderingEventHandler;

				_fpRegisterRenderParamsChanged = gcnew RegisterRenderParamsChanged(this, &DXVAVideoControl::onRegisterRenderParamsChanged);
				_fpVideoSurfaceUpdated = gcnew VideoSurfaceUpdated(this, &DXVAVideoControl::onVideoSurfaceUpdated);

				_holder->mode = CPCAPI2::WindowsVideoControl::kDXVAHDSurface;

				_holder->RegisterRenderParamsChanged = static_cast<CPCAPI2::WindowsVideoControl::RegisterRenderParamsChanged>(Marshal::GetFunctionPointerForDelegate(_fpRegisterRenderParamsChanged).ToPointer());
				_holder->onVideoSurfaceUpdated = static_cast<CPCAPI2::WindowsVideoControl::VideoSurfaceUpdated>(Marshal::GetFunctionPointerForDelegate(_fpVideoSurfaceUpdated).ToPointer());

				CPCAPI2::WindowsVideoControl::VideoControlList::addDX9ExVideoControl(_id, _holder);
			}

			DXVAVideoControl::~DXVAVideoControl()
			{
				this->!DXVAVideoControl();
			}

			DXVAVideoControl::!DXVAVideoControl()
			{
				_holder->window = nullptr;
				_holder->RegisterRenderParamsChanged = nullptr;
				_holder->onVideoSurfaceUpdated = nullptr;

				if (nullptr != _renderer)
				{
					_renderer->onVideoWindowChanged(_holder->window);
				}

				CPCAPI2::WindowsVideoControl::VideoControlList::removeDX9ExVideoControl(_id);

				_renderer = nullptr;
				delete _holder;
			}

			void DXVAVideoControl::onRegisterRenderParamsChanged(CPCAPI2::WindowsVideoControl::IWindowsVideoControl* renderer)
			{
				static TimeSpan lockTimeout = TimeSpan(0 /*days*/, 0 /*hours*/, 0 /*minutes*/, 0 /*seconds*/, 16 /*milliseconds*/); // 1 frame at 60hz
				bool lockTaken = System::Threading::Monitor::TryEnter(_rendererLock, lockTimeout);
				
				_renderer = renderer;
				
				if (lockTaken)
				{
					System::Threading::Monitor::Exit(_rendererLock);
				}
			}

			void DXVAVideoControl::onUnloaded(Object^ sender, RoutedEventArgs^ e)
			{
				if (nullptr != _unloadedEventHandler)
					this->Unloaded -= _unloadedEventHandler;
				if (nullptr != _renderingEventHandler)
					System::Windows::Media::CompositionTarget::Rendering -= _renderingEventHandler;

				static TimeSpan lockTimeout = TimeSpan(0 /*days*/, 0 /*hours*/, 0 /*minutes*/, 0 /*seconds*/, 16 /*milliseconds*/); // 1 frame at 60hz
				bool lockTaken = System::Threading::Monitor::TryEnter(_rendererLock, lockTimeout);
				try
				{
					if (nullptr != _renderer)
					{
						_renderer->log(kTraceDebug, "DXVAVideoControl::onUnloaded control %d", _id);
						_renderer->onVideoWindowChanged(0);
					}

					if (_newSurface != _currentSurface && IntPtr::Zero != _newSurface)
					{
						((IDirect3DSurface9*)((void*)_newSurface))->Release();
						_newSurface = IntPtr::Zero;
					}

					if (IntPtr::Zero != _currentSurface)
					{
						((IDirect3DSurface9*)((void*)_currentSurface))->Release();
						_currentSurface = IntPtr::Zero;
					}
				}
				finally
				{
					if (lockTaken)
					{
						System::Threading::Monitor::Exit(_rendererLock);
					}
				}
			}

			void DXVAVideoControl::onRendering(Object^ sender, EventArgs^ e)
			{
				if (nullptr == _d3DImage)
				{
					PresentationSource^ source = PresentationSource::FromVisual(this);
					_dpiX = 96.0 * source->CompositionTarget->TransformToDevice.M11;
					_dpiY = 96.0 * source->CompositionTarget->TransformToDevice.M22;

					_d3DImage = gcnew D3DImage(_dpiX, _dpiY);
					this->Source = _d3DImage;
				}
		
				static TimeSpan lockTimeout = TimeSpan(0 /*days*/, 0 /*hours*/, 0 /*minutes*/, 0 /*seconds*/, 100 /*milliseconds*/); // 6 frames at 60hz
				static Duration lockTimeoutDuration = Duration(lockTimeout);

				HWND window = 0;
				Window^ win = Window::GetWindow(this);
				if (nullptr != win)
				{
					window = (HWND)WindowInteropHelper(win).Handle.ToInt32();
				}
				_holder->window = window;

				unsigned int width = 0;
				unsigned int height = 0;

				Canvas^ canvas = safe_cast<Canvas^>(this->Parent);
				if (nullptr != canvas)
				{
					width = (canvas->ActualWidth * _dpiX / 96.0);
					height = (canvas->ActualHeight * _dpiY / 96.0);
				}

				if (!System::Threading::Monitor::TryEnter(_rendererLock, lockTimeout))
				{
					return;
				}

				try
				{
					if (nullptr != _renderer)
					{
						int err = _renderer->UpdateRenderSurface(width, height);
						if (0 != err)
						{
							char errMsg[128];
							sprintf_s(errMsg, 128, "DXVAVideoControl::onRendering control %d error rendering frame %d", _id, err);
							_renderer->log(kTraceWarning, errMsg);
						}
					}
				}
				finally
				{
					System::Threading::Monitor::Exit(_rendererLock);
				}

				try
				{
					// Even if TryLock fails, we need to call Unlock() since the internal lock count gets incremented
					if (!_d3DImage->TryLock(lockTimeoutDuration))
					{
						char errMsg[128];
						sprintf_s(errMsg, 128, "DXVAVideoControl::onRendering TryLock failed control %d", _id);
						if (nullptr != _renderer)
						{
							_renderer->log(kTraceWarning, errMsg);
						}
						return;
					}

					if (System::Threading::Monitor::TryEnter(_surfaceLock, lockTimeout))
					{
						try
						{
							if (_newSurface != _currentSurface)
							{
								try
								{
									_d3DImage->SetBackBuffer(D3DResourceType::IDirect3DSurface9, _newSurface, true);
									if (IntPtr::Zero != _currentSurface)
									{
										((IDirect3DSurface9*)((void*)_currentSurface))->Release();
									}
									_currentSurface = _newSurface;
								}
								catch (ArgumentException ^ e)
								{
									IntPtr err = Marshal::StringToHGlobalAnsi(e->Message);

									int len = snprintf(NULL, 0, "DXVAVideoControl::onRendering control %d surface %s", _id, (char*)err.ToPointer());
									char* errMsg = new char[len];
									snprintf(errMsg, len, "DXVAVideoControl::onRendering control %d surface %s", _id, (char*)err.ToPointer());
									if (nullptr != _renderer)
										_renderer->log(kTraceError, errMsg);
									delete[] errMsg;

									Marshal::FreeHGlobal(err);
									return;
								}

								if (!_memoryPressure.IsAllocated || nullptr == _memoryPressure.Target)
								{
									NativeMemoryPressure^ memoryPressure = gcnew NativeMemoryPressure();
									_memoryPressure = GCHandle::Alloc(memoryPressure, GCHandleType::WeakTrackResurrection);
								}
								((NativeMemoryPressure^)_memoryPressure.Target)->Allocated(width * height * 4 /*D3DFMT_A8R8G8B88*/);
							}
						}
						finally
						{
							System::Threading::Monitor::Exit(_surfaceLock);
						}

						// Setting the actual dirty rect had no noticable improvement in CPU usage so for simplicity set whole image as dirty
						if (IntPtr::Zero != _currentSurface)
						{
							_d3DImage->AddDirtyRect(Int32Rect(0, 0, _d3DImage->PixelWidth, _d3DImage->PixelHeight));
						}
					}
				}
				finally
				{
					_d3DImage->Unlock();
				}
			}

			// This doesn't need to be an IntPtr, but leaving as IntPtr for backwards compatiblity reasons
			IntPtr DXVAVideoControl::Handle()
			{
				return IntPtr((Int64)_id);
			}

			void DXVAVideoControl::onVideoSurfaceUpdated(void* surface, uint32_t width, uint32_t height)
			{
				// Lock here to prevent surface deletion mid-render
				
				static TimeSpan lockTimeout = TimeSpan(0 /*days*/, 0 /*hours*/, 0 /*minutes*/, 0 /*seconds*/, 16 /*milliseconds*/); // 1 frame at 60hz
				bool lockTaken = System::Threading::Monitor::TryEnter(_surfaceLock, lockTimeout);

				try
				{
					_newSurface = IntPtr(surface);
				}
				finally
				{
					if (lockTaken)
					{
						System::Threading::Monitor::Exit(_surfaceLock);
					}
				}
			}
		}
	}
}
