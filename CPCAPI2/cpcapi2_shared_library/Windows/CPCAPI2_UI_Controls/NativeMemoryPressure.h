#pragma once

#include "../../../shared/WebRTCv/trunk/webrtc/modules/video_render/windows/WindowsVideoControl.h"

namespace CPCAPI2
{
	namespace UI
	{
		namespace WPF
		{
			static CPCAPI2::WindowsVideoControl::WindowsVideoControlIdType sNextId = 1;
		}
	}
}

// Let the GC call the finalizer so diable the warning about nondeterministic finalizer
#pragma warning(disable : 4461) 
ref class NativeMemoryPressure
{
public:
	NativeMemoryPressure();
	!NativeMemoryPressure();


	void Allocated(long long bytes);

private:
	long long _memoryAllocated;
};

