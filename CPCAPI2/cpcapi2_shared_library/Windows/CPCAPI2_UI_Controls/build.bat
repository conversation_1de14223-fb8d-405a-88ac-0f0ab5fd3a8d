@echo off
set VSDBcolLight=[97m
set VSDBcolReset=[0m

set RELEASE=1
set VSDB_64=0

if /I "%1"=="x64" (
  set VSDB_64=1
)
if /I "%2"=="x64" (
  set VSDB_64=1
)
if /I "%1"=="debug" (
  set RELEASE=0
)
if /I "%2"=="debug" (
  set RELEASE=0
)

if /I "%VSDB_64%"=="1" (
  set VSDB_TOOLCHAIN=../../../projects/cmake/toolchains/windows-msvc-x86_64.cmake
  set VSDB_PLATFORM=x64
  if /I "%RELEASE%"=="1" (
    set VSDB_CMAKEBINDIR=build/Windows-x86_64-Release
  ) else (
    set VSDB_CMAKEBINDIR=build/Windows-x86_64-Debug
  )
) else (
  set VSDB_TOOLCHAIN=../../../projects/cmake/toolchains/windows-msvc-x86.cmake
  set VSDB_PLATFORM=WIN32
  if /I "%RELEASE%"=="1" (
    set VSDB_CMAKEBINDIR=build/Windows-x86-Release
  ) else (
    set VSDB_CMAKEBINDIR=build/Windows-x86-Debug
  )
)

call ../SetVs2019Env.bat %VSDB_PLATFORM%

if /I "%RELEASE%"=="1" (
  set CONFIGURATION=RelWithDebInfo
) else (
  set CONFIGURATION=Debug
)

echo %VSDBcolLight%
call cmake.exe -DCMAKE_BUILD_TYPE=%CONFIGURATION% -DCMAKE_TOOLCHAIN_FILE=%VSDB_TOOLCHAIN% -S. -B%VSDB_CMAKEBINDIR% -DSHAREDLIB_NOWARN=1 -G Ninja
echo %VSDBcolReset%
call cmake.exe --build %VSDB_CMAKEBINDIR% --config %CONFIGURATION% --

echo %VSDBcolLight%
if ERRORLEVEL 1 (
  echo CPCAPI2_UI_Controls %CONFIGURATION% %VSDB_PLATFORM% build failed.
) else (
  echo CPCAPI2_UI_Controls %CONFIGURATION% %VSDB_PLATFORM% build succeeded.
)
echo %VSDBcolReset%
pause
