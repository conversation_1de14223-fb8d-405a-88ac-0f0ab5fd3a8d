cmake_minimum_required(VERSION 3.8.0)

include(${CMAKE_CURRENT_SOURCE_DIR}/../../../projects/cmake/toolchains/configure.cmake) # Must be included before project()

# project(CPCAPI2_UI_Controls LANGUAGES ${CPCAPI2_LANGUAGES})

if(NOT DOTNET_TARGET_FRAMEWORK_VERSION)
  set(DOTNET_TARGET_FRAMEWORK_VERSION 4.8)
endif()

if(DEFINED "ENV{ProgramW6432}")
  file(TO_CMAKE_PATH "$ENV{ProgramFiles\(x86\)}/Reference Assemblies/Microsoft/Framework/.NETFramework/v${DOTNET_TARGET_FRAMEWORK_VERSION}" _ASSEMBLY_REF_PATH)
else()
  file(TO_CMAKE_PATH "$ENV{ProgramFiles}/Reference Assemblies/Microsoft/Framework/.NETFramework/v${DOTNET_TARGET_FRAMEWORK_VERSION}" _ASSEMBLY_REF_PATH)
endif()
string(REPLACE "/" "\\" _ASSEMBLY_REF_PATH "${_ASSEMBLY_REF_PATH}")

list(APPEND _ASSEMBLY_FU_FLAGS /AI"${_ASSEMBLY_REF_PATH}\\\\")
# list(APPEND _ASSEMBLY_FU_FLAGS /AI"$ENV{ProgramFiles\(x86\)}\\WINDOWS KITS\\10\\REFERENCES")
list(APPEND _ASSEMBLY_FU_FLAGS /AI"${_ASSEMBLY_REF_PATH}\\FACADES\\\\")

foreach(_lib_name mscorlib PresentationCore PresentationFramework System System.Xaml WindowsBase)
  list(APPEND _ASSEMBLY_FU_FLAGS /FU\"${_ASSEMBLY_REF_PATH}\\${_lib_name}.dll\")
endforeach()

list(JOIN _ASSEMBLY_FU_FLAGS " " _ASSEMBLY_FU_FLAGS)

# We have to unset/replace compiler flags that are conflicting /clr options
string(REPLACE "/EHs" "/GF /Gm- /EHa" CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS}")
string(REPLACE "/W3" "/clr /nologo /W3 /WX- /diagnostics:column /sdl" CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS}")
string(REPLACE "/Zc:__cplusplus" "/Zc:wchar_t /Zc:forScope /Zc:inline" CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS}")
string(REPLACE "/Qpar" "/Qpar-" CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS}")
foreach(flag_var CMAKE_CXX_FLAGS CMAKE_CXX_FLAGS_DEBUG CMAKE_CXX_FLAGS_RELEASE CMAKE_CXX_FLAGS_MINSIZEREL CMAKE_CXX_FLAGS_RELWITHDEBINFO)
  STRING (REGEX REPLACE "/RTC[^ ]*" "" ${flag_var} "${${flag_var}}")
  STRING (REGEX REPLACE "/cgthreads[^ ]*" "" ${flag_var} "${${flag_var}}")
  # STRING (REGEX REPLACE "/D_WIN32_WINNT=0x0601"  "" ${flag_var} "${${flag_var}}")
  STRING (REGEX REPLACE "/DWIN32_LEAN_AND_MEAN" "" ${flag_var} "${${flag_var}}")
endforeach()
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} ${_ASSEMBLY_FU_FLAGS} /FC /clr:nostdlib")

set(_TARGET CPCAPI2_UI_Controls)
add_library(${_TARGET} SHARED 
  DX9ExVideoControl.cpp DX9ExVideoControl.h 
  DX9VideoControl.cpp DX9VideoControl.h 
  DXVAVideoControl.cpp DXVAVideoControl.h 
  NativeMemoryPressure.cpp NativeMemoryPressure.h
  ${SHARED_PATH}/WebRTCv/trunk/webrtc/modules/video_render/windows/WindowsVideoControl.h
  CPCAPI2_UI_Controls.rc
)
target_include_directories(${_TARGET} BEFORE PRIVATE ${SHARED_PATH}/WebRTCv/trunk)
target_compile_definitions(${_TARGET} PRIVATE _MBCS _DLL _WINDLL CPCAPI2_UI_VIDEO_CONTROL WEBRTC_WIN)
target_link_libraries(${_TARGET} PRIVATE CPCAPI2_SharedLibrary_${CMAKE_BUILD_TYPE})
