﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="DX9ExVideoControl.cpp" />
    <ClCompile Include="DX9VideoControl.cpp" />
    <ClCompile Include="DXVAVideoControl.cpp" />
    <ClCompile Include="NativeMemoryPressure.cpp" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
    <Reference Include="System" />
    <Reference Include="System.Xaml" />
    <Reference Include="WindowsBase" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\..\shared\WebRTCv\trunk\webrtc\modules\video_render\windows\WindowsVideoControl.h" />
    <ClInclude Include="DX9ExVideoControl.h" />
    <ClInclude Include="DX9VideoControl.h" />
    <ClInclude Include="DXVAVideoControl.h" />
    <ClInclude Include="NativeMemoryPressure.h" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="CPCAPI2_UI_Controls.rc" />
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{ED21580F-88EF-4F64-8FF2-E574C690BA25}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>CPCAPI2_UI_Controls</RootNamespace>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <WindowsTargetPlatformVersion>10.0.18362.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <CLRSupport>true</CLRSupport>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <CLRSupport>true</CLRSupport>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
    <CLRSupport>true</CLRSupport>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
    <CLRSupport>true</CLRSupport>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>..\..\..\build\</OutDir>
    <TargetName>$(ProjectName)</TargetName>
    <CustomBuildAfterTargets>Build</CustomBuildAfterTargets>
    <PostBuildEventUseInBuild>false</PostBuildEventUseInBuild>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <TargetName>$(ProjectName)</TargetName>
    <LinkIncremental>false</LinkIncremental>
    <OutDir>..\..\..\build\$(Platform)</OutDir>
    <PostBuildEventUseInBuild>false</PostBuildEventUseInBuild>
    <CustomBuildAfterTargets>Build</CustomBuildAfterTargets>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>..\..\..\build\</OutDir>
    <CustomBuildAfterTargets>Build</CustomBuildAfterTargets>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>..\..\..\build\$(Platform)</OutDir>
    <CustomBuildAfterTargets>Build</CustomBuildAfterTargets>
    <PostBuildEventUseInBuild>false</PostBuildEventUseInBuild>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <SDLCheck>true</SDLCheck>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <AdditionalIncludeDirectories>..\..\..\shared\WebRTCv\trunk;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>CPCAPI2_UI_VIDEO_CONTROL;WEBRTC_WIN;_WINDLL;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SupportJustMyCode>false</SupportJustMyCode>
      <StringPooling>true</StringPooling>
      <MinimalRebuild>false</MinimalRebuild>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <EnableParallelCodeGeneration>false</EnableParallelCodeGeneration>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>DebugFull</GenerateDebugInformation>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalDependencies>CPCAPI2_SharedLibrary_Debug.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>..\..\..\build;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
    </Link>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
    <CustomBuildStep>
      <Command>if not exist ..\..\..\samples\windows\dotnet\SharedLibs mkdir ..\..\..\samples\windows\dotnet\SharedLibs\x86\

copy /Y $(TargetPath) ..\..\..\samples\windows\dotnet\SharedLibs\x86


if $(ConfigurationName) == Release (
copy /Y $(OutDir)\CPCAPI2_UI_Controls.pdb ..\..\..\samples\windows\dotnet\SharedLibs\x86
)</Command>
    </CustomBuildStep>
    <CustomBuildStep>
      <Outputs>force</Outputs>
    </CustomBuildStep>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <SDLCheck>true</SDLCheck>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <AdditionalIncludeDirectories>..\..\..\shared\WebRTCv\trunk;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>CPCAPI2_UI_VIDEO_CONTROL;WEBRTC_WIN;_WINDLL;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <SupportJustMyCode>false</SupportJustMyCode>
      <StringPooling>true</StringPooling>
      <MinimalRebuild>false</MinimalRebuild>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <EnableParallelCodeGeneration>false</EnableParallelCodeGeneration>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>DebugFull</GenerateDebugInformation>
      <AdditionalDependencies>CPCAPI2_SharedLibrary_Debug.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>..\..\..\build\$(Platform);%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
    </Link>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
    <CustomBuildStep>
      <Command>if not exist ..\..\..\samples\windows\dotnet\SharedLibs mkdir ..\..\..\samples\windows\dotnet\SharedLibs\x64\

copy /Y $(TargetPath) ..\..\..\samples\windows\dotnet\SharedLibs\x64


if $(ConfigurationName) == Release (
copy /Y $(OutDir)\CPCAPI2_UI_Controls.pdb ..\..\..\samples\windows\dotnet\SharedLibs\x64
)</Command>
    </CustomBuildStep>
    <CustomBuildStep>
      <Outputs>force</Outputs>
    </CustomBuildStep>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <AdditionalOptions>/d2Zi+</AdditionalOptions>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <AdditionalIncludeDirectories>..\..\..\shared\WebRTCv\trunk;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>CPCAPI2_UI_VIDEO_CONTROL;WEBRTC_WIN;_WINDLL;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <OmitFramePointers>true</OmitFramePointers>
      <StringPooling>true</StringPooling>
      <MinimalRebuild>false</MinimalRebuild>
      <EnableParallelCodeGeneration>false</EnableParallelCodeGeneration>
      <ControlFlowGuard>false</ControlFlowGuard>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <LinkTimeCodeGeneration>Default</LinkTimeCodeGeneration>
      <AdditionalDependencies>CPCAPI2_SharedLibrary.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>..\..\..\build;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
    </Link>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
    <CustomBuildStep>
      <Command>if not exist ..\..\..\samples\windows\dotnet\SharedLibs mkdir ..\..\..\samples\windows\dotnet\SharedLibs\x86\

copy /Y $(TargetPath) ..\..\..\samples\windows\dotnet\SharedLibs\x86


if $(ConfigurationName) == Release (
copy /Y $(OutDir)\CPCAPI2_UI_Controls.pdb ..\..\..\samples\windows\dotnet\SharedLibs\x86
)</Command>
      <Outputs>force</Outputs>
    </CustomBuildStep>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <AdditionalOptions>/d2Zi+</AdditionalOptions>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <AdditionalIncludeDirectories>..\..\..\shared\WebRTCv\trunk;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>CPCAPI2_UI_VIDEO_CONTROL;WEBRTC_WIN;_WINDLL;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <OmitFramePointers>true</OmitFramePointers>
      <StringPooling>true</StringPooling>
      <MinimalRebuild>false</MinimalRebuild>
      <EnableParallelCodeGeneration>false</EnableParallelCodeGeneration>
      <ControlFlowGuard>false</ControlFlowGuard>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <LinkTimeCodeGeneration>Default</LinkTimeCodeGeneration>
      <AdditionalDependencies>CPCAPI2_SharedLibrary.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>..\..\..\build\$(Platform);%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
    </Link>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
    <CustomBuildStep>
      <Command>if not exist ..\..\..\samples\windows\dotnet\SharedLibs mkdir ..\..\..\samples\windows\dotnet\SharedLibs\x64\

copy /Y $(TargetPath) ..\..\..\samples\windows\dotnet\SharedLibs\x64


if $(ConfigurationName) == Release (
copy /Y $(OutDir)\CPCAPI2_UI_Controls.pdb ..\..\..\samples\windows\dotnet\SharedLibs\x64
)</Command>
    </CustomBuildStep>
    <CustomBuildStep>
      <Outputs>force</Outputs>
    </CustomBuildStep>
  </ItemDefinitionGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets" />
</Project>