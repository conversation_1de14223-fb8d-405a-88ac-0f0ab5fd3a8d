@echo off
set VSRBcolLight=[97m
set VSRBcolReset=[0m

set VSRB_CONAN=1
set VSRB_64=0

if /I "%1"=="x64" (
  set VSRB_64=1
)
if /I "%2"=="x64" (
  set VSRB_64=1
)
if /I "%VSRB_64%"=="1" (
  set VSRB_TOOLCHAIN=../../projects/cmake/toolchains/windows-msvc-x86_64.cmake
  set VSRB_PLATFORM=x64
  set VSRB_CMAKEBINDIR=../../build/Windows-x86_64-Release
) else (
  set VSRB_TOOLCHAIN=../../projects/cmake/toolchains/windows-msvc-x86.cmake
  set VSRB_PLATFORM=WIN32
  set VSRB_CMAKEBINDIR=../../build/Windows-x86-Release
)
if /I "%1"=="noconan" (
  set VSRB_CONAN=0
)
if /I "%2"=="noconan" (
  set VSRB_CONAN=0
)
call SetVs2019Env.bat %VSRB_PLATFORM%

echo %VSRBcolLight%
call cmake.exe -DLIBRARY_OUTPUT_NAME=CPCAPI2_SharedLibrary -DCMAKE_BUILD_TYPE=RelWithDebInfo -DCMAKE_TOOLCHAIN_FILE=%VSRB_TOOLCHAIN% -S..\..\cpcapi2_shared_library -B%VSRB_CMAKEBINDIR% -DSHAREDLIB_NOWARN=1 -DBUILD_UI_CONTROLS=1 -DCPCAPI2_CONAN=%VSRB_CONAN% -G Ninja
echo %VSRBcolReset%
call cmake.exe --build %VSRB_CMAKEBINDIR% --config RelWithDebInfo --

echo %VSRBcolLight%
if ERRORLEVEL 1 (
  echo CPCAPI2_SharedLibrary %VSRB_PLATFORM% build failed.
) else (
  echo CPCAPI2_SharedLibrary %VSRB_PLATFORM% build succeeded.
)
echo %VSRBcolReset%
