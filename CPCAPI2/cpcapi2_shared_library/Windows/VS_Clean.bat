@echo off

echo:
if exist ..\..\build\Windows-x86_64-Release (
  echo Cleaning Windows-x86_64-Release
  call cmake.exe --build ../../build/Windows-x86_64-Release -- -t clean
)
if exist ..\..\build\Windows-x86_64-Debug (
  echo Cleaning Windows-x86_64-Debug
  call cmake.exe --build ../../build/Windows-x86_64-Debug -- -t clean
)
if exist ..\..\build\x64\CPCAPI2_SharedLibrary_Debug.dll (
  del ..\..\build\x64\CPCAPI2_SharedLibrary_Debug.*
)
if exist ..\..\build\x64\CPCAPI2_SharedLibrary.dll (
  del ..\..\build\x64\CPCAPI2_SharedLibrary.*
)
if exist ..\..\build\Windows-x86-Release (
  echo Cleaning Windows-x86-Release
  call cmake.exe --build ../../build/Windows-x86-Release -- -t clean
)
if exist ..\..\build\Windows-x86-Debug (
  echo Cleaning Windows-x86-Debug
  call cmake.exe --build ../../build/Windows-x86-Debug -- -t clean
)
if exist ..\..\build\CPCAPI2_SharedLibrary_Debug.dll (
  del ..\..\build\CPCAPI2_SharedLibrary_Debug.*
)
if exist ..\..\build\CPCAPI2_SharedLibrary.dll (
  del ..\..\build\CPCAPI2_SharedLibrary.*
)
if exist CPCAPI2_SharedLibrary_Debug.log (
  del CPCAPI2_SharedLibrary_Debug.log
)
if exist CPCAPI2_SharedLibrary.log (
  del CPCAPI2_SharedLibrary.log
)
echo:
call SetVs2019Env.bat
call MSBuild.exe CPCAPI2_UI_Controls/CPCAPI2_UI_Controls.vcxproj /property:Configuration=Debug /p:Platform=WIN32 /t:Clean
call MSBuild.exe CPCAPI2_UI_Controls/CPCAPI2_UI_Controls.vcxproj /property:Configuration=Release /p:Platform=WIN32 /t:Clean
call MSBuild.exe CPCAPI2_UI_Controls/CPCAPI2_UI_Controls.vcxproj /property:Configuration=Debug /p:Platform=x64 /t:Clean
call MSBuild.exe CPCAPI2_UI_Controls/CPCAPI2_UI_Controls.vcxproj /property:Configuration=Release /p:Platform=x64 /t:Clean

if exist CPCAPI2_UI_Controls\CPCAPI2_UI_Controls.log (
  del CPCAPI2_UI_Controls\CPCAPI2_UI_Controls.log
)

pause
