@echo OFF

rem * OpenSSL Build script
rem * To use within Visual Studio project only
rem * To successfully use this script from the command line,
rem * uncomment the MS build tools related lines

echo %OPENSSL_BRANDING% > debug.txt
pushd "%~dp0..\..\..\..\windows_libs\openssl"
(SET LIBS_DIR="%cd%")
popd
pushd "%~dp0..\..\..\external\OpenSSL"
(SET OPENSSL_HOME="%cd%")
popd
(SET OPENSSL_SRC_PATH=)
(SET INFOFILE=lastbuild.txt)
(SET FAILED=)
(SET BUILD_FLAGS=)
pushd %OPENSSL_BRANDING%
FOR /F "tokens=*" %%I IN ('OpenSSLBranding.exe') DO (SET BUILD_FLAGS=%%I)
rem unquote build flags:
(SET BUILD_FLAGS=%BUILD_FLAGS:"=%)
IF "%BUILD_FLAGS%"=="" (
    echo -!!!- Could NOT get OpenSSL configuration options! Is CPCAPI2_OPENSSL_BUILD_FLAGS missing from branding?
    (SET FAILED=1)
    GOTO purge
) ELSE (
    echo ----- OpenSSL configuration options: %BUILD_FLAGS%)
popd
rem search for OpenSSL source:
pushd %OPENSSL_HOME%
echo ----- Searching for OpenSSL source in %OPENSSL_HOME%
FOR /D %%G in ("openssl*") DO (
    (SET OPENSSL_SRC_PATH=%%~nxG)
)
IF "%OPENSSL_SRC_PATH%"=="" (
    echo -!!!- Could NOT find OpenSSL source folder
    (SET FAILED=1)
    GOTO purge
) ELSE (
    echo ----- OpenSSL source folder: %OPENSSL_SRC_PATH%)
popd
pushd %~dp0
rem check if version or configuration has changed since last build
(SET VERSIONMATCH=)
(SET CONFIGMATCH=)
FOR /F "tokens=1,*" %%D IN (%INFOFILE%) DO (
    IF %%D==VERSION (IF "%%E"=="%OPENSSL_SRC_PATH%" SET VERSIONMATCH=1)
    IF %%D==CONFIG (IF "%%E"=="%BUILD_FLAGS%" SET CONFIGMATCH=1)
)

(SET LIBS_EXIST=1)
IF NOT EXIST %LIBS_DIR%\build-win32\lib\libeay32.lib (SET LIBS_EXIST=0)
IF NOT EXIST %LIBS_DIR%\build-win32\lib\ssleay32.lib (SET LIBS_EXIST=0)

IF [%VERSIONMATCH%]==[1] (IF [%CONFIGMATCH%]==[1] (IF [%LIBS_EXIST%]==[1] (
    echo ----- No changes detected since last build, skipping OpenSSL build...
    GOTO skip)
))
rem store build info to file
(echo VERSION %OPENSSL_SRC_PATH%) > %INFOFILE%
(echo CONFIG %BUILD_FLAGS%) >> %INFOFILE%
(echo BUILT %DATE% %TIME%) >> %INFOFILE%
popd

:purge
rem delete old openssl libs
echo ----- Removing existing build-win32 folder from %LIBS_DIR%
rmdir /S /Q %LIBS_DIR%\build-win32
IF [%FAILED%]==[1] GOTO skip
pushd "%OPENSSL_HOME%\%OPENSSL_SRC_PATH%"

rem initialize MS build tools:
rem CALL "%VS140COMNTOOLS%VsDevCmd.bat"
rem IF NOT ERRORLEVEL 0 (
    rem search for earlier version of MS build tools:
rem     CALL "%VS120COMNTOOLS%VsDevCmd.bat"
rem     IF NOT ERRORLEVEL 0 (
        rem VsDevCmd.bat not found
rem         echo -!!!- VsDevCmd.bat not found, aborting -!!!-
rem         GOTO skip)
rem )

rem echo on to show the command line used
@echo ON
perl Configure VC-WIN32 %BUILD_FLAGS% no-shared --openssldir=ssl --prefix=build-win32
@echo OFF
cmd /c ms\do_ms
IF EXIST tmp32/*.* nmake -f ms\nt.mak clean
nmake -f ms\nt.mak install
IF NOT ERRORLEVEL 0 (
   echo openSSL build failed!
   GOTO skip
)
rem copy new openssl libs
xcopy /E /Y /F build-win32\* %LIBS_DIR%\build-win32\

:skip
popd
