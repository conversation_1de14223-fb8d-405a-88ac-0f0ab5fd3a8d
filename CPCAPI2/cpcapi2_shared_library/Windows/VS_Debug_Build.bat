@echo off
set VSDBcolLight=[97m
set VSDBcolReset=[0m

set VSDB_CONAN=1
set VSDB_64=0

if /I "%1"=="x64" (
  set VSDB_64=1
)
if /I "%2"=="x64" (
  set VSDB_64=1
)
if /I "%VSDB_64%"=="1" (
  set VSDB_TOOLCHAIN=../../projects/cmake/toolchains/windows-msvc-x86_64.cmake
  set VSDB_PLATFORM=x64
  set VSDB_CMAKEBINDIR=../../build/Windows-x86_64-Debug
) else (
  set VSDB_TOOLCHAIN=../../projects/cmake/toolchains/windows-msvc-x86.cmake
  set VSDB_PLATFORM=WIN32
  set VSDB_CMAKEBINDIR=../../build/Windows-x86-Debug
)
if /I "%1"=="noconan" (
  set VSDB_CONAN=0
)
if /I "%2"=="noconan" (
  set VSDB_CONAN=0
)
call SetVs2019Env.bat %VSDB_PLATFORM%

echo %VSDBcolLight%
call cmake.exe -DLIBRARY_OUTPUT_NAME=CPCAPI2_SharedLibrary_Debug -DCMAKE_BUILD_TYPE=Debug -DCMAKE_TOOLCHAIN_FILE=%VSDB_TOOLCHAIN% -S../../cpcapi2_shared_library -B%VSDB_CMAKEBINDIR% -DSHAREDLIB_NOWARN=1 -DBUILD_UI_CONTROLS=1 -DCPCAPI2_CONAN=%VSDB_CONAN% -G Ninja
echo %VSDBcolReset%
call cmake.exe --build %VSDB_CMAKEBINDIR% --config Debug --

echo %VSDBcolLight%
if ERRORLEVEL 1 (
  echo CPCAPI2_SharedLibrary_Debug %VSDB_PLATFORM% build failed.
) else (
  echo CPCAPI2_SharedLibrary_Debug %VSDB_PLATFORM% build succeeded.
)
echo %VSDBcolReset%
pause
