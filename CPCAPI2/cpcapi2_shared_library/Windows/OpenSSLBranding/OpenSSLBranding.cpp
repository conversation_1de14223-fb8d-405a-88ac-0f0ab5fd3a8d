// OpenSSLBranding.cpp : Defines the entry point for the console application.
//
#include "brand_branded.h"
#include <iostream>

int main()
{
#if CPCAPI2_OPENSSL_BUILD_CITI_ASPECT == 1 && defined(CPCAPI2_OPENSSL_BUILD_FLAGS_CITI_ASPECT)
   std::cout << CPCAPI2_OPENSSL_BUILD_FLAGS_CITI_ASPECT;
   return 0;
#elif defined(CPCAPI2_OPENSSL_BUILD_FLAGS)
   std::cout << CPCAPI2_OPENSSL_BUILD_FLAGS;
   return 0;
#else
   std::cout << "\"\"";
   return -1;
#endif
}

