@echo off

set SVS2019E_LAST_PLATFORM=%SVS2019E_PLATFORM%
set SVS2019E_INSTALLPATH=
set SVS2019E_PLATFORM=

if "%1"=="x64" (
  set SVS2019E_PLATFORM=x64
)
if "%1"=="WIN32" (
  set SVS2019E_PLATFORM=x64_x86
)

set vswherepath="%ProgramFiles(x86)%\Microsoft Visual Studio\Installer\vswhere.exe"

if exist "%vswherepath%" (
  for /F "tokens=* USEBACKQ" %%F in (`%vswherepath% -version 16.0 -property installationPath`) do (
    set SVS2019E_INSTALLPATH=%%F
    echo "Visual Studio 2019 proper found!"
    goto foundInstallPath
  )

  echo "Visual Studio 2019 was not found! Looking for BuildTools next..."

  for /F "tokens=* USEBACKQ" %%F in (`%vswherepath% -products Microsoft.VisualStudio.Product.BuildTools -version 16.0 -property installationPath`) do (
    set SVS2019E_INSTALLPATH=%%F
    echo "Visual Studio 2019 BuildTools found!"
    goto foundInstallPath
  )
  
  echo "Visual Studio 2019 BuildTools also not found!"
)

:foundInstallPath
echo INSTALLPATH is "%SVS2019E_INSTALLPATH%"

if NOT ""=="%SVS2019E_INSTALLPATH%" (
  if NOT "%SVS2019E_LAST_PLATFORM%"=="%SVS2019E_PLATFORM%" (
    echo Cleaning environment...
    (call "%SVS2019E_INSTALLPATH%\Common7\Tools\VsDevCmd.bat" /clean_env) >nul
  )
  if "%SVS2019E_PLATFORM%"=="" (
    call "%SVS2019E_INSTALLPATH%\Common7\Tools\VsDevCmd.bat"
  ) else (
    call "%SVS2019E_INSTALLPATH%\VC\Auxiliary\Build\vcvarsall.bat" %SVS2019E_PLATFORM%
  )
) else (
  goto ERROR_NO_VS16
)

goto END

:ERROR_NO_VS16
echo Visual Studio 2019 %SVS2019E_PLATFORM% Environment Not Available

:END
echo: