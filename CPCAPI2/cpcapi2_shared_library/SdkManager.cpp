#include "brand_branded.h"

#if (CPCAPI2_BRAND_NODE_MODULE == 1)
#include "SdkManager.h"
#include "Cpcapi2Runner.h"
#include "json/JsonHelper.h"
#include "util/PlatformUtils.h"

#include <cpcapi2.h>

#include <fstream>

#ifdef ANDROID
#include "common/Java/JniHelper.h"
#include "android/log.h"
#endif

#define LICENSE_AOR "<EMAIL>"

#ifdef _WIN32
#define LICENSE_DOCUMENT_LOCATION "C:\\Windows\\Temp"
#elif defined(ANDROID)
#define LICENSE_DOCUMENT_LOCATION mAndroidLicenseLocation
#elif defined(__linux__)
#define LICENSE_DOCUMENT_LOCATION "/tmp"
#elif TARGET_OS_IPHONE
#define LICENSE_DOCUMENT_LOCATION "/tmp"
#elif TARGET_OS_MAC
#define LICENSE_DOCUMENT_LOCATION "/tmp"
#endif

#define CPCAPI2_CONF_NUM_SDK_REACTORS 4

#define RESIPROCATE_SUBSYSTEM resip::Subsystem::APP

using namespace CPCAPI2;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::Licensing;

namespace CPCAPI2
{
namespace Agent
{

SdkManagerExt* SdkManagerExt::create()
{
   return new SdkManager();
}

void SdkManagerExt::destroy()
{
   delete this;
}


SdkManager::SdkManager()
   : mNativeCallback(NULL)
{
#ifdef ANDROID
  Jni::ScopedLocalRef<jobject> file = Jni::CallObjectMethod("android/content/Context", Jni::GetContext(), "getExternalFilesDir", "(Ljava/lang/String;)Ljava/io/File;", NULL);
  mAndroidLicenseLocation = Jni::CallStringMethod("java/io/File", *file, "toString", "()Ljava/lang/String;").c_str();
#endif

}

SdkManager::~SdkManager()
{
}

void SdkManager_sdkCallbackHook(void* context)
{
   SdkManager* cpcRunner = (SdkManager*)context;
   cpcRunner->handleSdkCallback();
}

void SdkManager::handleSdkCallback()
{
   mReactor.post(resip::resip_bind(&SdkManager::handleSdkCallbackImpl, this));
}

void SdkManager::handleSdkCallbackImpl()
{
   mPhone->process(CPCAPI2::Phone::kBlockingModeNonBlocking);
   mJsonApiServer->process(CPCAPI2::JsonApi::JsonApiServer::kBlockingModeNonBlocking);
}

void SdkManager::run(std::function<void(Cpcapi2Runner*)> callbackFn)
{
   mReactor.start();
   mReactor.post(resip::resip_bind(&SdkManager::appInit, this, callbackFn));
}

void SdkManager::appInit(std::function<void(Cpcapi2Runner*)> callbackFn)
{
   appInitImpl();
   callbackFn(mRunner.get());
}

void SdkManager::appInitImpl()
{
   mPhone = CPCAPI2::PhoneInternal::create(0);
   LicenseInfo licenseInfo;
   licenseInfo.licenseKey = "lkj";
   licenseInfo.licenseDocumentLocation = LICENSE_DOCUMENT_LOCATION;
   licenseInfo.licenseAor = "";
   mPhone->setCallbackHook(SdkManager_sdkCallbackHook, this);
   mPhone->initialize(licenseInfo, this, false);
#ifdef ANDROID
   class AndroidLogger : public PhoneLogger
   {
   public:
      virtual bool operator()(CPCAPI2::LogLevel level, const char *subsystem, const char *appName, const char *file, int line, const char *message, const char *messageWithHeaders)
      {
         switch(level)
         {
            case CPCAPI2::LogLevel_Max:
               __android_log_print(ANDROID_LOG_VERBOSE, "CPCAPI2", "%s (%s:%d): %s", subsystem, file, line, message);
               break;
            case CPCAPI2::LogLevel_Debug:
               __android_log_print(ANDROID_LOG_DEBUG, "CPCAPI2", "%s (%s:%d): %s", subsystem, file, line, message);
               break;
            case CPCAPI2::LogLevel_Info:
               __android_log_print(ANDROID_LOG_INFO, "CPCAPI2", "%s (%s:%d): %s", subsystem, file, line, message);
               break;
            case CPCAPI2::LogLevel_Warning:
               __android_log_print(ANDROID_LOG_WARN, "CPCAPI2", "%s (%s:%d): %s", subsystem, file, line, message);
               break;
            case CPCAPI2::LogLevel_Error:
               __android_log_print(ANDROID_LOG_ERROR, "CPCAPI2", "%s (%s:%d): %s", subsystem, file, line, message);
               break;
            case CPCAPI2::LogLevel_None:
               break;
         }
         return true;
      }
   };
   AndroidLogger* androidLogger = new AndroidLogger();
   mPhone->setLoggingEnabled(androidLogger, true);
#else
   mPhone->setLoggingEnabled("cpcapi2webclient", false);
#endif

   mLicensingMgr = LicensingClientManager::getInterface(mPhone);
   mLicensingMgr->setCallbackHook(SdkManager_sdkCallbackHook, this);
   mLicensingClientHandle = mLicensingMgr->create();
   mLicensingMgr->setHandler(mLicensingClientHandle, this);

   // Determine the OS Name and Version string.
   std::string osValue;
   PlatformUtils::OSInfo osInfo;
   if( PlatformUtils::PlatformUtils::getOSInfo( osInfo ))
   {
      switch( osInfo.osType )
      {
      case PlatformUtils::OSType_Windows:
         osValue += "Windows ";
         break;
      case PlatformUtils::OSType_OSX:
         osValue += "OSX ";
         break;
      case PlatformUtils::OSType_Linux:
         osValue += "Linux ";
         break;
      case PlatformUtils::OSType_Android:
         osValue += "Android ";
         break;
      case PlatformUtils::OSType_iOS:
         osValue += "iOS ";
         break;
      case PlatformUtils::OSType_Other:
      default:
         break;
      }
   }
   osValue += osInfo.osVersion;

   LicensingClientSettings settings;
   settings.licenseKeys.push_back(CPCAPI2_BRAND_LICENSE_BUILTIN_KEY);
   settings.licenseDocumentLocation = LICENSE_DOCUMENT_LOCATION;
   settings.osVersion = osValue.c_str();
   //settings.provisioningId = LICENSE_AOR;
   //settings.keySourceUrl = "http://license.counterpath.com/generate";
   mLicensingMgr->setBrandedExpiry(settings);

   mLicensingMgr->applySettings(mLicensingClientHandle, settings);

   mJsonApiServer = CPCAPI2::JsonApi::JsonApiServer::getInterface(mPhone);
   mJsonApiServer->setHandler(this);
   mJsonApiServer->setCallbackHook(SdkManager_sdkCallbackHook, this);
   CPCAPI2::JsonApi::JsonApiServerConfig serverConfig;
   serverConfig.jsonApiVersion = 9;
   serverConfig.websocketPort = -1; // 2114;
   serverConfig.httpPort = -1;
   serverConfig.outgoingJsonHandler = this;
   serverConfig.numThreads = 1;
   serverConfig.wssCertificateFilePath = "-----BEGIN CERTIFICATE----- \n"
      "MIIG8TCCBdmgAwIBAgIRAJTeM8QLivWOfqDiVQnZHaswDQYJKoZIhvcNAQELBQAw \n"
      "gZAxCzAJBgNVBAYTAkdCMRswGQYDVQQIExJHcmVhdGVyIE1hbmNoZXN0ZXIxEDAO \n"
      "BgNVBAcTB1NhbGZvcmQxGjAYBgNVBAoTEUNPTU9ETyBDQSBMaW1pdGVkMTYwNAYD \n"
      "VQQDEy1DT01PRE8gUlNBIERvbWFpbiBWYWxpZGF0aW9uIFNlY3VyZSBTZXJ2ZXIg \n"
      "Q0EwHhcNMTgwODA5MDAwMDAwWhcNMjAwODA4MjM1OTU5WjBdMSEwHwYDVQQLExhE \n"
      "b21haW4gQ29udHJvbCBWYWxpZGF0ZWQxFDASBgNVBAsTC1Bvc2l0aXZlU1NMMSIw \n"
      "IAYDVQQDExljcGNsaWVudGFwaS5zb2Z0cGhvbmUuY29tMIIBIjANBgkqhkiG9w0B \n"
      "AQEFAAOCAQ8AMIIBCgKCAQEAvkbgEWMxFDdIoH3m7QNcws+rHuyrOd/h9NZjNMLx \n"
      "57mLyFPgi+jmD4IiMc4Ad4R7EsIZI0gw5LeGeB6UdIVU/LU4x7jYhm4y+Nz2uhtX \n"
      "ageEnUz9DcVD3fbG2rr/4Z9vf73KQ6uwT49KjOp8Z00cINW3VBWBPM1y7k0IMLR9 \n"
      "Ii2qMpw+QgItfXEHBmWLH1R91sUuYMoo4n7dOEgWsCr+Iz1+xDtzrsDPQByIcrNx \n"
      "nPQC5xIHN1H38EuiKx8iBwIWms0RMyFZKxp4iGJPKubzPy/FRyvzejPWlSneNTi2 \n"
      "skCvf+CUW5zy6v4a1JsMpYd+YunJaSKuq3eOhrdgDAG6cwIDAQABo4IDdjCCA3Iw \n"
      "HwYDVR0jBBgwFoAUkK9qOpRaC9iQ6hJWc99DtDoo2ucwHQYDVR0OBBYEFE3ga+QH \n"
      "127honttGF73Z3ynVFMOMA4GA1UdDwEB/wQEAwIFoDAMBgNVHRMBAf8EAjAAMB0G \n"
      "A1UdJQQWMBQGCCsGAQUFBwMBBggrBgEFBQcDAjBPBgNVHSAESDBGMDoGCysGAQQB \n"
      "sjEBAgIHMCswKQYIKwYBBQUHAgEWHWh0dHBzOi8vc2VjdXJlLmNvbW9kby5jb20v \n"
      "Q1BTMAgGBmeBDAECATBUBgNVHR8ETTBLMEmgR6BFhkNodHRwOi8vY3JsLmNvbW9k \n"
      "b2NhLmNvbS9DT01PRE9SU0FEb21haW5WYWxpZGF0aW9uU2VjdXJlU2VydmVyQ0Eu \n"
      "Y3JsMIGFBggrBgEFBQcBAQR5MHcwTwYIKwYBBQUHMAKGQ2h0dHA6Ly9jcnQuY29t \n"
      "b2RvY2EuY29tL0NPTU9ET1JTQURvbWFpblZhbGlkYXRpb25TZWN1cmVTZXJ2ZXJD \n"
      "QS5jcnQwJAYIKwYBBQUHMAGGGGh0dHA6Ly9vY3NwLmNvbW9kb2NhLmNvbTBDBgNV \n"
      "HREEPDA6ghljcGNsaWVudGFwaS5zb2Z0cGhvbmUuY29tgh13d3cuY3BjbGllbnRh \n"
      "cGkuc29mdHBob25lLmNvbTCCAX0GCisGAQQB1nkCBAIEggFtBIIBaQFnAHYA7ku9 \n"
      "t3XOYLrhQmkfq+GeZqMPfl+wctiDAMR7iXqo/csAAAFlIKa2xgAABAMARzBFAiBN \n"
      "nKBKIvlThdKYJsE3SfqPkQmsnttdrRrLKxp/4TE7cQIhAIXP26gE8JWUGT06yKxM \n"
      "IfFCwv1dm6nEsLf4gRiO0CoCAHUAXqdz+d9WwOe1Nkh90EngMnqRmgyEoRIShBh1 \n"
      "loFxRVgAAAFlIKa3BwAABAMARjBEAiBlTyXmTN1iBB/NGVNbqN1MEUJt4XIV7zqN \n"
      "UMt9rKrfnQIgNKXl6VBfbvHKGakhwOpPo+t2Aq2K/BAgKohg8xRwEQQAdgBVgdTC \n"
      "FpA2AUrqC5tXPFPwwOQ4eHAlCBcvo6odBxPTDAAAAWUgprbrAAAEAwBHMEUCIQCc \n"
      "s23Cb8djNfLmSfAgURzyyIlkGQSsUIk4hWsf02S7sQIgbOibohoLYiMbE4/ocTCn \n"
      "cvgjtKR3E6T3XCoQRbMsOZUwDQYJKoZIhvcNAQELBQADggEBAALeP3EQkGIWC9mw \n"
      "gum5JvCVmxSoHFHKYQPcEqKXI7zcU7mbq35G/ZZUzADd4Ckc9DPlDudekxgLGLK4 \n"
      "pNffrv4GR+zuk4VZX9bQoD2Op7vhaSX1coN06Qt8L4FQyonagUU99svy86/bWhCH \n"
      "eEAg1Dhq8CuY/y3D+n9FSv+v1NL/jkfnwg09AZ+FQsoTK1sGCmwAnESR3ueL+jus \n"
      "MBdM8MpIWjp9OOmvLU+GvZTt0eKFh0ZKHXbUOsBeWVk8AqHU8XcB8qIomgQJsrX+ \n"
      "tVrrbVTZ4AgkTgpHxm/boSiEQCNzjsKkuFHuc3MMVgf3557HgXDgAybbX9b+k4pj \n"
      "/dGuVTw= \n"
      "-----END CERTIFICATE-----\n";
**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
   serverConfig.wssDiffieHellmanParamsFilePath = "-----BEGIN DH PARAMETERS-----\n"
      "MIIBCAKCAQEA5P4ae+ijV2PnB7DdKHL4NDozSQKGf3N+0H6M75H1EUbFoP00hPFo\n"
      "sC/58Yz1pAXNIDW17PrAPIVKbUVgYG6ZwV7xcUSf7nOhdo+SbUMmCw/r1DEXGyu6\n"
      "+GLgGkYk8AejSZ1TtmBcOEg6ENstZ6xTSO+Wi/i8sIYon57Z7qv6oiFgwh0QXByL\n"
      "MxNDA7ZjYEs6GvgsYWMIHVPyD+w1V4JDh3wQE4Mi41iezvuUFRcq+HdmwUDG9Pmp\n"
      "u8iNFCrHiPu+LX6Bxwyfose4fwQgM31jWOCizynB9TapY6XKVlQ4lYwLkxQ51aKu\n"
      "T4WlnBc2ntTxJxDsvGxtOSez6jOM6zv1AwIBAg==\n"
      "-----END DH PARAMETERS-----\n";

   mJsonApiServer->start(serverConfig);
   //mJsonApiServer->setJsonApiUserContext((CPCAPI2::JsonApi::JsonApiUserHandle)UINT_MAX, mPhone);

   CPCAPI2::VideoStreaming::VideoStreamingManager* videoStreamingMgr = CPCAPI2::VideoStreaming::VideoStreamingManager::getInterface(mPhone);
   if (videoStreamingMgr != NULL)
   {
      VideoStreaming::VideoStreamingServerConfig streamingServerConfig;
      streamingServerConfig.serverType = VideoStreaming::ServerType_CallbackFunction;
#if 0
      streamingServerConfig.listenPort = 2115;
      streamingServerConfig.wssCertificateFilePath = "-----BEGIN CERTIFICATE----- \n"
         "MIIG8TCCBdmgAwIBAgIRAJTeM8QLivWOfqDiVQnZHaswDQYJKoZIhvcNAQELBQAw \n"
         "gZAxCzAJBgNVBAYTAkdCMRswGQYDVQQIExJHcmVhdGVyIE1hbmNoZXN0ZXIxEDAO \n"
         "BgNVBAcTB1NhbGZvcmQxGjAYBgNVBAoTEUNPTU9ETyBDQSBMaW1pdGVkMTYwNAYD \n"
         "VQQDEy1DT01PRE8gUlNBIERvbWFpbiBWYWxpZGF0aW9uIFNlY3VyZSBTZXJ2ZXIg \n"
         "Q0EwHhcNMTgwODA5MDAwMDAwWhcNMjAwODA4MjM1OTU5WjBdMSEwHwYDVQQLExhE \n"
         "b21haW4gQ29udHJvbCBWYWxpZGF0ZWQxFDASBgNVBAsTC1Bvc2l0aXZlU1NMMSIw \n"
         "IAYDVQQDExljcGNsaWVudGFwaS5zb2Z0cGhvbmUuY29tMIIBIjANBgkqhkiG9w0B \n"
         "AQEFAAOCAQ8AMIIBCgKCAQEAvkbgEWMxFDdIoH3m7QNcws+rHuyrOd/h9NZjNMLx \n"
         "57mLyFPgi+jmD4IiMc4Ad4R7EsIZI0gw5LeGeB6UdIVU/LU4x7jYhm4y+Nz2uhtX \n"
         "ageEnUz9DcVD3fbG2rr/4Z9vf73KQ6uwT49KjOp8Z00cINW3VBWBPM1y7k0IMLR9 \n"
         "Ii2qMpw+QgItfXEHBmWLH1R91sUuYMoo4n7dOEgWsCr+Iz1+xDtzrsDPQByIcrNx \n"
         "nPQC5xIHN1H38EuiKx8iBwIWms0RMyFZKxp4iGJPKubzPy/FRyvzejPWlSneNTi2 \n"
         "skCvf+CUW5zy6v4a1JsMpYd+YunJaSKuq3eOhrdgDAG6cwIDAQABo4IDdjCCA3Iw \n"
         "HwYDVR0jBBgwFoAUkK9qOpRaC9iQ6hJWc99DtDoo2ucwHQYDVR0OBBYEFE3ga+QH \n"
         "127honttGF73Z3ynVFMOMA4GA1UdDwEB/wQEAwIFoDAMBgNVHRMBAf8EAjAAMB0G \n"
         "A1UdJQQWMBQGCCsGAQUFBwMBBggrBgEFBQcDAjBPBgNVHSAESDBGMDoGCysGAQQB \n"
         "sjEBAgIHMCswKQYIKwYBBQUHAgEWHWh0dHBzOi8vc2VjdXJlLmNvbW9kby5jb20v \n"
         "Q1BTMAgGBmeBDAECATBUBgNVHR8ETTBLMEmgR6BFhkNodHRwOi8vY3JsLmNvbW9k \n"
         "b2NhLmNvbS9DT01PRE9SU0FEb21haW5WYWxpZGF0aW9uU2VjdXJlU2VydmVyQ0Eu \n"
         "Y3JsMIGFBggrBgEFBQcBAQR5MHcwTwYIKwYBBQUHMAKGQ2h0dHA6Ly9jcnQuY29t \n"
         "b2RvY2EuY29tL0NPTU9ET1JTQURvbWFpblZhbGlkYXRpb25TZWN1cmVTZXJ2ZXJD \n"
         "QS5jcnQwJAYIKwYBBQUHMAGGGGh0dHA6Ly9vY3NwLmNvbW9kb2NhLmNvbTBDBgNV \n"
         "HREEPDA6ghljcGNsaWVudGFwaS5zb2Z0cGhvbmUuY29tgh13d3cuY3BjbGllbnRh \n"
         "cGkuc29mdHBob25lLmNvbTCCAX0GCisGAQQB1nkCBAIEggFtBIIBaQFnAHYA7ku9 \n"
         "t3XOYLrhQmkfq+GeZqMPfl+wctiDAMR7iXqo/csAAAFlIKa2xgAABAMARzBFAiBN \n"
         "nKBKIvlThdKYJsE3SfqPkQmsnttdrRrLKxp/4TE7cQIhAIXP26gE8JWUGT06yKxM \n"
         "IfFCwv1dm6nEsLf4gRiO0CoCAHUAXqdz+d9WwOe1Nkh90EngMnqRmgyEoRIShBh1 \n"
         "loFxRVgAAAFlIKa3BwAABAMARjBEAiBlTyXmTN1iBB/NGVNbqN1MEUJt4XIV7zqN \n"
         "UMt9rKrfnQIgNKXl6VBfbvHKGakhwOpPo+t2Aq2K/BAgKohg8xRwEQQAdgBVgdTC \n"
         "FpA2AUrqC5tXPFPwwOQ4eHAlCBcvo6odBxPTDAAAAWUgprbrAAAEAwBHMEUCIQCc \n"
         "s23Cb8djNfLmSfAgURzyyIlkGQSsUIk4hWsf02S7sQIgbOibohoLYiMbE4/ocTCn \n"
         "cvgjtKR3E6T3XCoQRbMsOZUwDQYJKoZIhvcNAQELBQADggEBAALeP3EQkGIWC9mw \n"
         "gum5JvCVmxSoHFHKYQPcEqKXI7zcU7mbq35G/ZZUzADd4Ckc9DPlDudekxgLGLK4 \n"
         "pNffrv4GR+zuk4VZX9bQoD2Op7vhaSX1coN06Qt8L4FQyonagUU99svy86/bWhCH \n"
         "eEAg1Dhq8CuY/y3D+n9FSv+v1NL/jkfnwg09AZ+FQsoTK1sGCmwAnESR3ueL+jus \n"
         "MBdM8MpIWjp9OOmvLU+GvZTt0eKFh0ZKHXbUOsBeWVk8AqHU8XcB8qIomgQJsrX+ \n"
         "tVrrbVTZ4AgkTgpHxm/boSiEQCNzjsKkuFHuc3MMVgf3557HgXDgAybbX9b+k4pj \n"
         "/dGuVTw= \n"
         "-----END CERTIFICATE-----\n";
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
      streamingServerConfig.wssDiffieHellmanParamsFilePath = "-----BEGIN DH PARAMETERS-----\n"
         "MIIBCAKCAQEA5P4ae+ijV2PnB7DdKHL4NDozSQKGf3N+0H6M75H1EUbFoP00hPFo\n"
         "sC/58Yz1pAXNIDW17PrAPIVKbUVgYG6ZwV7xcUSf7nOhdo+SbUMmCw/r1DEXGyu6\n"
         "+GLgGkYk8AejSZ1TtmBcOEg6ENstZ6xTSO+Wi/i8sIYon57Z7qv6oiFgwh0QXByL\n"
         "MxNDA7ZjYEs6GvgsYWMIHVPyD+w1V4JDh3wQE4Mi41iezvuUFRcq+HdmwUDG9Pmp\n"
         "u8iNFCrHiPu+LX6Bxwyfose4fwQgM31jWOCizynB9TapY6XKVlQ4lYwLkxQ51aKu\n"
         "T4WlnBc2ntTxJxDsvGxtOSez6jOM6zv1AwIBAg==\n"
         "-----END DH PARAMETERS-----\n";
#endif
      videoStreamingMgr->startVideoStreamingServer(streamingServerConfig);
   }

   initFromSettings();

   mRunner.reset(new Cpcapi2Runner(0, &mReactor, mPhone, mJsonApiServer));
}

void SdkManager::shutdown()
{
   //mTTSApi->shutdown();
   mReactor.execute(resip::resip_bind(&SdkManager::appShutdown, this));
   mReactor.stop();
}

void SdkManager::appShutdown()
{
   if (mRunner.get() != NULL)
   {
      mRunner->shutdown();
   }

   mJsonApiServer->shutdown();

   CPCAPI2::VideoStreaming::VideoStreamingManager* videoStreamingMgr = CPCAPI2::VideoStreaming::VideoStreamingManager::getInterface(mPhone);
   if (videoStreamingMgr != NULL)
   {
      videoStreamingMgr->stopVideoStreamingServer();
   }
}

void SdkManager::addVideoStreamCallback(int videoStreamHandle, void* cbState, void(*cb)(void*, unsigned int, int, int, const unsigned char*, unsigned int, const unsigned char*, unsigned int, const unsigned char*, unsigned int))
{
   InfoLog(<< "Adding callback for video (addVideoStreamCallback)");
   CPCAPI2::VideoStreaming::VideoStreamingManager* videoStreamingMgr = CPCAPI2::VideoStreaming::VideoStreamingManager::getInterface(mPhone);
   if (videoStreamingMgr != NULL)
   {
      videoStreamingMgr->addVideoStreamCallback(videoStreamHandle, cbState, cb);
   }
}

void SdkManager::removeVideoStreamCallback(int videoStreamHandle)
{
   CPCAPI2::VideoStreaming::VideoStreamingManager* videoStreamingMgr = CPCAPI2::VideoStreaming::VideoStreamingManager::getInterface(mPhone);
   if (videoStreamingMgr != NULL)
   {
      videoStreamingMgr->removeVideoStreamCallback(videoStreamHandle);
   }
}

bool SdkManager::isProdBuild() const
{
   bool isProdBuild = false;
   if (!mLicensingMgr)
   {
      isProdBuild = false;
   }
   else if (mLicensingMgr->appResourceSignatureCheckEnabled())
   {
      isProdBuild = true;
   }

   return isProdBuild;
}

void SdkManager::join()
{
   mReactor.join();
}

void SdkManager::initFromSettings()
{
}

int SdkManager::invokeJsonApi(const cpc::string& jsonApiCall)
{
   mReactor.post(resip::resip_bind(&SdkManager::invokeJsonApiImpl, this, jsonApiCall));
   return 0;
}

void SdkManager::invokeJsonApiImpl(const cpc::string& jsonApiCall)
{
   mJsonApiServer->processIncomingJson(jsonApiCall);
}

void SdkManager::setJsonApiCallback(void(*cb)(const cpc::string&))
{
   mNativeCallback = cb;
}

int SdkManager::onError(const cpc::string& sourceModule, const CPCAPI2::PhoneErrorEvent& args)
{
   return 0;
}

int SdkManager::onLicensingError(const CPCAPI2::LicensingErrorEvent& args)
{
   return 0;
}

int SdkManager::onNewLogin(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const CPCAPI2::JsonApi::NewLoginEvent& args)
{
   cpc::vector<cpc::string> permissions;
   permissions.push_back("*");
   assert(mRunner.get() != NULL);
   mJsonApiServer->setJsonApiUserContext(jsonApiUser, mRunner->getPhone(), permissions);

   CPCAPI2::JsonApi::LoginResultEvent loginResult;
   loginResult.success = true;

   mJsonApiServer->sendLoginResult(jsonApiUser, loginResult);
   mLicensingMgr->validateLicenses(mLicensingClientHandle);

   return 0;
}

/*
int SdkManager::onSessionState(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiuser, const CPCAPI2::JsonApi::SessionStateEvent& args)
{
   if (!args.isActive)
   {
      mRunner->handleSessionDisconnected();
   }
   return 0;
}
*/

int SdkManager::onValidateLicensesSuccess(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ValidateLicensesSuccessEvent& args)
{
   CPCAPI2::Licensing::ValidateLicensesSuccessEvent a = args;
   for (size_t i = 0; i < a.licenses.size(); i++)
   {
      a.licenses[i].key = "";
   }
   CPCAPI2::Json::JsonDataPointer json = CPCAPI2::Json::MakeJsonDataPointer();
   CPCAPI2::Json::JsonFunctionSerialize serializer(json, false, "LicensingClientJsonProxy", "onValidateLicensesSuccess");
   serializer.addValue("client", client);
   serializer.addValue("args", a);
   serializer.finalize();
   onJson(json->getMessageData());
   return kSuccess;
}

int SdkManager::onValidateLicensesFailure(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ValidateLicensesFailureEvent& args)
{
   CPCAPI2::Json::JsonDataPointer json = CPCAPI2::Json::MakeJsonDataPointer();
   CPCAPI2::Json::JsonFunctionSerialize serializer(json, false, "LicensingClientJsonProxy", "onValidateLicensesFailure");
   serializer.addValue("client", client);
   serializer.addValue("args", args);
   serializer.finalize();
   onJson(json->getMessageData());
   return kSuccess;
}

int SdkManager::onError(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ErrorEvent& args)
{
   CPCAPI2::Json::JsonDataPointer json = CPCAPI2::Json::MakeJsonDataPointer();
   CPCAPI2::Json::JsonFunctionSerialize serializer(json, false, "LicensingClientJsonProxy", "onError");
   serializer.addValue("client", client);
   serializer.addValue("args", args);
   serializer.finalize();
   onJson(json->getMessageData());
   return kSuccess;
}

int SdkManager::onJson(const cpc::string& json)
{
   if (NULL != mNativeCallback)
      mNativeCallback(json);
   return 0;
}
}
}
#endif
