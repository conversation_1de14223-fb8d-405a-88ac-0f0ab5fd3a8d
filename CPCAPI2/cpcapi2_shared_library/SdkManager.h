#pragma once

#include <cpcapi2.h>
#include <jsonapi/JsonApiServer.h>
#include <jsonapi/JsonApiServerHandler.h>
#include <phone/PhoneInternal.h>
#include <licensing/LicensingClientSyncHandler.h>

#include <interface/experimental/agent/SdkManagerExt.h>

// rutil includes
#include <rutil/MultiReactor.hxx>
#include <rutil/Data.hxx>
namespace CPCAPI2
{
namespace Agent
{
class Cpcapi2Runner;
class SdkManager : public CPCAPI2::Agent::SdkManagerExt,
                   public CPCAPI2::PhoneHandler,
                   public CPCAPI2::JsonApi::JsonApiServerHandler,
                   public CPCAPI2::JsonApi::OutgoingJsonHandler,
                   public CPCAPI2::Licensing::LicensingClientHandler,
                   public CPCAPI2::Licensing::LicensingClientSyncHandler
{
public:
   SdkManager();
   ~SdkManager();

   void* operator new(size_t i)
   {
#if defined(ANDROID)
      return memalign(folly::hardware_destructive_interference_size, i);
#else
      return _mm_malloc(i, folly::hardware_destructive_interference_size);
#endif
   }

   void operator delete(void* p)
   {
#if defined(ANDROID)
      free(p);
#else
      _mm_free(p);
#endif
   }

   void run(std::function<void(Cpcapi2Runner*)> callbackFn) OVERRIDE;

   void shutdown();
   void join();

   void handleSdkCallback();
   int invokeJsonApi(const cpc::string& jsonApiCall) OVERRIDE;
   void setJsonApiCallback(void(*cb)(const cpc::string&)) OVERRIDE;
   void addVideoStreamCallback(int videoStreamHandle, void* cbState, void(*cb)(void*, unsigned int, int, int, const unsigned char*, unsigned int, const unsigned char*, unsigned int, const unsigned char*, unsigned int)) OVERRIDE;
   void removeVideoStreamCallback(int videoStreamHandle) OVERRIDE;
   bool isProdBuild() const OVERRIDE;

   Cpcapi2Runner* getRunner() const {
      return mRunner.get();
   }

   // PhoneHandler
   virtual int onError(const cpc::string& sourceModule, const CPCAPI2::PhoneErrorEvent& args);
   virtual int onLicensingError(const CPCAPI2::LicensingErrorEvent& args);
   virtual int onLicensingSuccess() {
      return 0;
   }

   // JsonApiServerHandler
   virtual int onNewLogin(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiuser, const CPCAPI2::JsonApi::NewLoginEvent& args);
   //virtual int onSessionState(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiuser, const CPCAPI2::JsonApi::SessionStateEvent& args);
   //virtual int onPreLogout(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const CPCAPI2::JsonApi::PreLogoutEvent& args) { return 0; }
   virtual int onLogout(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const CPCAPI2::JsonApi::LogoutEvent& args) { return 0; }

   // OutgoingJsonHandler
   virtual int onJson(const cpc::string& json);

   // LicensingClientHandler
   virtual int onValidateLicensesSuccess(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ValidateLicensesSuccessEvent& args);
   virtual int onValidateLicensesFailure(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ValidateLicensesFailureEvent& args);
   virtual int onError(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ErrorEvent& args);

private:
   void appInit(std::function<void(Cpcapi2Runner*)> callbackFn);
   void appInitImpl();
   void appShutdown();
   void handleSdkCallbackImpl();

   void initFromSettings();
   void invokeJsonApiImpl(const cpc::string& jsonApiCall);

private:
   resip::MultiReactor mReactor;
   CPCAPI2::PhoneInternal* mPhone;
   CPCAPI2::Licensing::LicensingClientHandle mLicensingClientHandle;
   CPCAPI2::Licensing::LicensingClientManager* mLicensingMgr;
   cpc::string mAndroidLicenseLocation;
   CPCAPI2::JsonApi::JsonApiServer* mJsonApiServer;
   std::unique_ptr<Cpcapi2Runner> mRunner;
   void(*mNativeCallback)(const cpc::string&);
};
}
}
