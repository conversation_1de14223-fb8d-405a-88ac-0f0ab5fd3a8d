// The following ifdef block is the standard way of creating macros which make exporting
// from a DLL simpler. All files within this DLL are compiled with the CPCAPI2_SHAREDLIBRARY_EXPORTS
// symbol defined on the command line. This symbol should not be defined on any project
// that uses this DLL. This way any other project whose source files include this file see
// CPCAPI2_SHAREDLIBRARY_API functions as being imported from a DLL, whereas this DLL sees symbols
// defined with this macro as being exported.
#if _WIN32
  #ifdef CPCAPI2_SHAREDLIBRARY_EXPORTS
    #define CPCAPI2_SHAREDLIBRARY_API __declspec(dllexport)
  #elif defined(CPCAPI2_STATIC_LIB)
    #define CPCAPI2_SHAREDLIBRARY_API
  #else
    #define CPCAPI2_SHAREDLIBRARY_API __declspec(dllimport)
  #endif
#else
  #define CPCAPI2_SHAREDLIBRARY_API
  #pragma GCC visibility push(default)
#endif

// This class is exported from the CPCAPI2_SharedLibrary.dll
CPCAPI2_SHAREDLIBRARY_API CPCAPI2::Phone* CPCAPI2_CreatePhone(void);
CPCAPI2_SHAREDLIBRARY_API CPCAPI2::SipAccount::SipAccountManager* CPCAPI2_CreateSipAccountManager(CPCAPI2::Phone* phone);
CPCAPI2_SHAREDLIBRARY_API CPCAPI2::SipConversation::SipConversationManager* CPCAPI2_CreateSipConversationManager(CPCAPI2::Phone* phone);
CPCAPI2_SHAREDLIBRARY_API CPCAPI2::Media::MediaManager* CPCAPI2_CreateMediaManager(CPCAPI2::Phone* phone);
CPCAPI2_SHAREDLIBRARY_API CPCAPI2::SipEvent::SipEventManager* CPCAPI2_CreateSipEventManager(CPCAPI2::Phone* phone);
CPCAPI2_SHAREDLIBRARY_API CPCAPI2::SipPresence::SipPresenceManager* CPCAPI2_CreateSipPresenceManager(CPCAPI2::Phone* phone);

CPCAPI2_SHAREDLIBRARY_API std::string CPCAPI2_wstringToUtf8(const wchar_t* str);
CPCAPI2_SHAREDLIBRARY_API std::wstring CPCAPI2_utf8ToWstring(const char* str);

#if _WIN32
#else
  #pragma GCC visibility pop
#endif
