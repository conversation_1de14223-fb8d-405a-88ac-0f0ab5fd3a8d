#!/bin/zsh -l

# DRL This was required on some Mac systems to initialize the environment when invoked
# from XCode
source ~/.zshrc

err_report() {
    echo "Error on line $1"
}

# ensure the script exits immediately with failure code if an error is encountered
set -eE  # same as: `set -o errexit -o errtrace`
trap 'err_report $LINENO' ERR

which cmake || echo "Please ensure your default shell is set to zsh and cmake is installed and set in your PATH .zshrc or .zprofile"

# ===========================================================
# Handle the parameters
# ===========================================================

platform=""         # ios, osx
architecture="All"  # All, Arm, Intel, Sim_Arm, Sim_Intel, armv8, x86_64
build_type="Debug"  # Debug, RelWithDebInfo
source_folder="."   # the path to cpcapi2_shared_library
intermediate_folder="."           # where to put the intermediate files (in subfolders)
output_folder="../samples/libs"   # the destination path for the created framework
sdk_folder=""       # path to the Xcode SDK folder
branding_file=""    # path and name of branding file
exclude_headers="1" # 1, 0
no_xcframework="0"  # 1, 0
clean="0"           # 1, 0
xcodeaction=""
clean_only="0"
use_conan="1"
use_mac_audio_version="3"  # shall we use the new Mac Audio Unit V3 API (default) or the older V2 API?

while getopts p:a:b:s:i:o:x:f:h:c:q:n:j:u: flag
do
    case "${flag}" in
        p) platform=${OPTARG};;
        a) architecture=${OPTARG};;
        b) build_type=${OPTARG};;
        s) source_folder=${OPTARG};;
        i) intermediate_folder=${OPTARG};;
        o) output_folder=${OPTARG};;
        x) sdk_folder=${OPTARG};;
        f) branding_file=${OPTARG};;
        h) if [[ ${OPTARG} = "1" || ${OPTARG} = "True" ]]; then exclude_headers="0";  elif [[ ${OPTARG} = "0" || ${OPTARG} = "False" ]]; then exclude_headers="1"; else exclude_headers=""; fi;;
        c) if [[ ${OPTARG} = "1" || ${OPTARG} = "True" ]]; then clean="1";  elif [[ ${OPTARG} = "0" || ${OPTARG} = "False" ]]; then clean="0"; else clean=""; fi;;
        q) if [[ ${OPTARG} = "1" || ${OPTARG} = "True" ]]; then no_xcframework="1";  elif [[ ${OPTARG} = "0" || ${OPTARG} = "False" ]]; then no_xcframework="0"; else no_xcframework=""; fi;;
        n) xcodeaction=${OPTARG};;
        j) use_conan=${OPTARG};;
        u) use_mac_audio_version=${OPTARG};;
    esac
done

if [[ $xcodeaction = "ACTION_clean" ]]; then
   clean="1"
   clean_only="1"
fi

if [[ $build_type = "Release" ]]; then
   build_type="RelWithDebInfo"
fi

# this identifies which lib and which dSYM file to use for this specific debug session (since we sometimes 
# build multiple files)
current_lib_name=""
current_dsym_name=""

if [[ $platform = "ios" ]]; then
  # these are passed in by Xcode so we convert them
  if [[ $architecture = "armv8" || $architecture = "arm64" || $architecture = "arm64 x86_64" ]]; then  # M1 Mac case, sometimes it wants both
    if [[ $sdk_folder = "" || $architecture = "arm64 x86_64" ]]; then
      architecture="All"
    elif [[ $sdk_folder =~ "Simulator" ]]; then
      architecture="Sim_Arm"
      current_lib_name="CPCAPI2_${platform}_sim_arm"
      current_dsym_name="CPCAPI2_${platform}_sim_arm.dSYM"
    else
      architecture="Arm"
      current_lib_name="CPCAPI2_${platform}_arm"
      current_dsym_name="CPCAPI2_${platform}_arm.dSYM"
    fi
  elif [[ $architecture = "x86_64" ]]; then
    if [[ $sdk_folder = "" ]]; then
      architecture="All"
    elif [[ $sdk_folder =~ "Simulator" ]]; then
      architecture="Sim_Intel"
      current_lib_name="CPCAPI2_${platform}_sim_intel"
      current_dsym_name="CPCAPI2_${platform}_sim_intel.dSYM"
    else
      architecture="Intel"
      current_lib_name="CPCAPI2_${platform}_intel"
      current_dsym_name="CPCAPI2_${platform}_intel.dSYM"
    fi
  fi
else
  if [[ $architecture = "armv8" ]]; then
    current_dsym_name="CPCAPI2_${platform}_arm.dSYM"
    architecture="Arm"
  elif [[ $architecture = "x86_64" ]]; then
    current_lib_name="CPCAPI2_${platform}_intel"
    current_dsym_name="CPCAPI2_${platform}_intel.dSYM"
    architecture="Intel"
  fi
fi

# clear the screen and history
printf '\33c\e[3J'

echo "
Current settings...
Platform:            $platform
Architecture:        $architecture
Build type:          $build_type
Source folder:       $source_folder
Intermediate folder: $intermediate_folder
Output folder:       $output_folder
SDK folder:          $sdk_folder
Branding file:       $branding_file
Exclude headers:     $exclude_headers
No xcframework:      $no_xcframework
Force clean:         $clean
Clean only:          $clean_only
Conan:               $use_conan
Use MAC audio API:   $use_mac_audio_version
"

if [[ ($platform = "ios" || $platform = "osx") && 
      ($architecture = "All" || $architecture = "Arm" || $architecture = "Sim_Arm" || $architecture = "Intel" || $architecture = "Sim_Intel" || $architecture = "Sim" || $architecture = "Sim_Both" || $architecture = "Universal") && 
      ($build_type = "Debug" || $build_type = "RelWithDebInfo") && 
      ($exclude_headers = "0" || $exclude_headers = "1") && 
      ($no_xcframework = "0" || $no_xcframework = "1") && 
      ($clean = "0" || $clean = "1") && 
      (-d $source_folder) ]]; then
  # we're good to go
else
  echo "
Usage: $0 -p ios
-p [platform]            required: ios, osx
-a [architecture]        optional (default All): All (with simulators), Universal (no simulators), Arm, Intel, Sim_Arm, Sim_Intel, Sim (one framework), Sim_Both (separate frameworks), armv8, x86_64
-b [build type]          optional (default Debug): Debug, Release (includes debug info)
-s [source folder]       optional (default .): the path to cpcapi2_shared_library
-i [intermediate folder] optional (default .): where to put the intermediate files (in subfolders)
-o [output folder]       optional (default ../samples/libs): the destination path for the created framework
-x [SDK folder]          optional (default ignored): path to Xcode SDK, used to identify whether a simulator architecture is wanted or not, otherwise builds both
-f [branding file]       optional: path and name of branding file (used to override brand_branded.h)
-h [include headers]     optional (default 0): 1, 0, True, False
-q [no xcframework]      optional (default 0): 1, 0, True, False
-c [force clean]         optional (default 0): 1, 0, True, False
-n [action]              optional: ACTION_clean
-j [use conan]           optional (default 1): 1, 0
-u [MAC audio version]   optional (default 3): 2, 3
"
  exit 1
fi

#read -s -n 1 -p "Press any key to continue..."
echo ""

# add terminating slash if missing
if [[ $source_folder != */ ]]; then 
  source_folder="${source_folder}/"
fi
if [[ $intermediate_folder != */ ]]; then 
  intermediate_folder="${intermediate_folder}/"
fi
if [[ $output_folder != */ ]]; then 
  output_folder="${output_folder}/"
fi
toolchains_folder="${source_folder}../projects/cmake/toolchains/"

# We use a file that contains the relevant options in the name so we can catch 
# when those options change requiring a clean build. We use a folder for each 
# platform and architecture so we only need to clean when the build type or header 
# settings change.
check_file="building_${build_type}_xhdr_${exclude_headers}"

# Simplify some code wrt framework extension difference between iOS and OSX.
if [[ $platform = "ios" && $no_xcframework = "0" ]]; then
  framework_extension="xcframework"
  current_lib_name="CPCAPI2"
else
  framework_extension="framework"
fi

# rebuild the index if we clean anything below, and this is the architecture to use
index_arch=""

# ===========================================================
# Make the build files (if missing or cleaning), then build the libraries
# ===========================================================

if [[ $architecture = "All" || $architecture = "Arm" ]]; then
#  read -s -n 1 -p "Press any key to build $platform Arm $build_type..."

  build_folder="${intermediate_folder}build_${platform}_arm/"

  if [[ $clean = "1" || ! (-f "${build_folder}${check_file}") ]]; then
    echo "Cleaning Arm..."
    rm -r -f $build_folder
    if [[ $clean_only = "1" ]]; then
      return 0
    fi
    mkdir -p $build_folder
    touch "${build_folder}${check_file}"
    index_arch="armv8"
  fi
#  read -s -n 1 -p "Press any key to build $platform Arm..."
  echo ""
  cmake --trace-redirect="${build_folder}Trace.log" -Wdev -G Ninja -S $source_folder -B $build_folder -DCPCAPI2_CONAN=$use_conan -DCPCAPI2_BRAND_HEADER=$branding_file -DSKIP_HEADERS=$exclude_headers -DCMAKE_BUILD_TYPE=$build_type -DBUILD_WEBRTC=1 -DUSE_MAC_AUDIO_VERSION=$use_mac_audio_version -DSKIP_COMPRESS=1 -DCMAKE_TOOLCHAIN_FILE="${toolchains_folder}${platform}-clang-armv8.cmake"
  cmake --build $build_folder -- -v

  # we need a modulemap file to tell Xcode where to find our headers
  mkdir -p "${build_folder}CPCAPI2.framework/Modules"
  echo "framework module CPCAPI2 {\n  umbrella header \"CPCAPI2.h\"\n\n  export *\n  module * { export * }\n}" > "${build_folder}CPCAPI2.framework/Modules/module.modulemap"
fi

if [[ $platform = "ios" && ($architecture = "All" || $architecture = "Sim" || $architecture = "Sim_Both" || $architecture = "Sim_Arm") ]]; then
#  read -s -n 1 -p "Press any key to build $platform Arm Simulator $build_type..."

  build_folder="${intermediate_folder}build_${platform}_sim_arm/"

  if [[ $clean = "1" || ! (-f "${build_folder}${check_file}") ]]; then
    echo "Cleaning Arm simulator..."
    rm -r -f $build_folder
    if [[ $clean_only = "1" ]]; then
      return 0
    fi
    mkdir -p $build_folder
    touch "${build_folder}${check_file}"
    index_arch="armv8"
  fi
#  read -s -n 1 -p "Press any key to build $platform Arm simulator..."
  echo ""
  cmake --trace-redirect="${build_folder}Trace.log" -Wdev -G Ninja -S $source_folder -B $build_folder -DCPCAPI2_CONAN=$use_conan -DCPCAPI2_BRAND_HEADER=$branding_file -DSKIP_HEADERS=$exclude_headers -DCMAKE_BUILD_TYPE=$build_type -DBUILD_WEBRTC=1 -DUSE_MAC_AUDIO_VERSION=$use_mac_audio_version -DSKIP_COMPRESS=1 -DCMAKE_TOOLCHAIN_FILE="${toolchains_folder}${platform}-clang-armv8-simulator.cmake"
  cmake --build $build_folder -- -v

  # we need a modulemap file to tell Xcode where to find our headers
  mkdir -p "${build_folder}CPCAPI2.framework/Modules"
  echo "framework module CPCAPI2 {\n  umbrella header \"CPCAPI2.h\"\n\n  export *\n  module * { export * }\n}" > "${build_folder}CPCAPI2.framework/Modules/module.modulemap"
fi

if [[ $platform = "osx" && ($architecture = "All" || $architecture = "Intel") ]]; then
#  read -s -n 1 -p "Press any key to build $platform Intel $build_type..."

  build_folder="${intermediate_folder}build_${platform}_intel/"

  if [[ $clean = "1" || ! (-f "${build_folder}${check_file}") ]]; then
    echo "Cleaning Intel..."
    rm -r -f $build_folder
    if [[ $clean_only = "1" ]]; then
      return 0
    fi
    mkdir -p $build_folder
    touch "${build_folder}${check_file}"
    index_arch="x86_64"
  fi
#  read -s -n 1 -p "Press any key to build $platform Intel..."
  echo ""
  cmake --trace-redirect="${build_folder}Trace.log" -Wdev -G Ninja -S $source_folder -B $build_folder -DCPCAPI2_CONAN=$use_conan -DCPCAPI2_BRAND_HEADER=$branding_file -DSKIP_HEADERS=$exclude_headers -DCMAKE_BUILD_TYPE=$build_type -DBUILD_WEBRTC=1 -DUSE_MAC_AUDIO_VERSION=$use_mac_audio_version -DSKIP_COMPRESS=1 -DCMAKE_TOOLCHAIN_FILE="${toolchains_folder}${platform}-clang-x86_64.cmake"
  cmake --build $build_folder -- -v

  # we need a modulemap file to tell Xcode where to find our headers
  mkdir -p "${build_folder}CPCAPI2.framework/Modules"
  echo "framework module CPCAPI2 {\n  umbrella header \"CPCAPI2.h\"\n\n  export *\n  module * { export * }\n}" > "${build_folder}CPCAPI2.framework/Modules/module.modulemap"
fi

if [[ $platform = "ios" && ($architecture = "All" || $architecture = "Sim" || $architecture = "Sim_Both" || $architecture = "Universal" || $architecture = "Sim_Intel") ]]; then
#  read -s -n 1 -p "Press any key to build $platform Intel Simulator $build_type..."

  build_folder="${intermediate_folder}build_${platform}_sim_intel/"

  if [[ $clean = "1" || ! (-f "${build_folder}${check_file}") ]]; then
    echo "Cleaning Intel simulator..."
    rm -r -f $build_folder
    if [[ $clean_only = "1" ]]; then
      return 0
    fi
    mkdir -p $build_folder
    touch "${build_folder}${check_file}"
    index_arch="x86_64-simulator"
  fi
#  read -s -n 1 -p "Press any key to build $platform Intel simulator..."
  echo ""
  cmake --trace-redirect="${build_folder}Trace.log" -Wdev -G Ninja -S $source_folder -B $build_folder -DCPCAPI2_CONAN=$use_conan -DCPCAPI2_BRAND_HEADER=$branding_file -DSKIP_HEADERS=$exclude_headers -DCMAKE_BUILD_TYPE=$build_type -DBUILD_WEBRTC=1 -DUSE_MAC_AUDIO_VERSION=$use_mac_audio_version -DSKIP_COMPRESS=1 -DCMAKE_TOOLCHAIN_FILE="${toolchains_folder}${platform}-clang-x86_64-simulator.cmake"
  cmake --build $build_folder -- -v

  # we need a modulemap file to tell Xcode where to find our headers
  mkdir -p "${build_folder}CPCAPI2.framework/Modules"
  echo "framework module CPCAPI2 {\n  umbrella header \"CPCAPI2.h\"\n\n  export *\n  module * { export * }\n}" > "${build_folder}CPCAPI2.framework/Modules/module.modulemap"
fi


# ===========================================================
# Build the index if it does not exist; we avoid this on iOS
# due to it causing intermittent Xcode build failures if this script
# was triggered via Xcode
#
# To generate indexing projects for iOS, invoke
# generateIndexing_iOS.sh directly via terminal
#
# To generate indexing projects manually via macOS,
# invoke ./generateIndexing_macOS.sh directly via terminal

# ===========================================================

if [[ $platform = "osx" && ! (-d "./indexingCPCAPI2") ]]; then
  ./generateIndexing_macOS.sh
fi

# ===========================================================
# Combine the framework/libraries as needed for the target platform and architecture(s)
# ===========================================================

pre_pushd_stack_len=$(dirs -v | wc -l | xargs)
pushd $intermediate_folder  # we will stay in the intermediate folder until the end of this script

rm -r -f build
mkdir build

if [[ $platform = "ios" && ($architecture = "All" || $architecture = "Sim" || $architecture = "Sim_Both" || $architecture = "Universal") ]]; then
#  read -s -n 1 -p "Press any key to create iOS universal framework..."
  echo ""
  
  if [[ $architecture != "Sim_Both" ]]; then
    # We must create a simulator library that contains the two architectures, since we 
    # can't add them separately to an xcframework.
    rm -r -f build_sim
    mkdir build_sim
    cp -R "build_${platform}_sim_arm/" build_sim
    rm -r build_sim/CPCAPI2.framework/CPCAPI2
    lipo -create "build_${platform}_sim_arm/CPCAPI2.framework/CPCAPI2" "build_${platform}_sim_intel/CPCAPI2.framework/CPCAPI2" -output build_sim/CPCAPI2.framework/CPCAPI2
  fi

  if [[ $architecture = "Universal" ]]; then
    # We must create a native library that contains the two architectures, since we 
    # can't add them separately to an xcframework.
    rm -r -f build_${platform}
    mkdir build_${platform}
    cp -R "build_${platform}_arm/" build_${platform}
    rm -r build_${platform}/CPCAPI2.framework/CPCAPI2
    lipo -create "build_${platform}_arm/CPCAPI2.framework/CPCAPI2" "build_${platform}_intel/CPCAPI2.framework/CPCAPI2" -output build_${platform}/CPCAPI2.framework/CPCAPI2
  fi

  # now we can combine to create a universal xcframework
  if [[ $architecture = "All" ]]; then
    # native arm in one framework and simulators in a second
    if [[ $no_xcframework = "0" ]]; then
      xcodebuild -create-xcframework -framework "build_${platform}_arm/CPCAPI2.framework" -framework "build_sim/CPCAPI2.framework" -output "build/CPCAPI2.xcframework"
    else
      cp -R "build_${platform}_arm/CPCAPI2.framework" "build/CPCAPI2_${platform}_arm.framework"
      cp -R "build_sim/CPCAPI2.framework" "build/CPCAPI2_${platform}_sim.framework"
    fi
    cp -R "build_${platform}_arm/CPCAPI2.dSYM" "build/CPCAPI2_${platform}_arm.dSYM"
    cp -R "build_${platform}_sim_arm/CPCAPI2.dSYM" "build/CPCAPI2_${platform}_sim_arm.dSYM"
    cp -R "build_${platform}_sim_intel/CPCAPI2.dSYM" "build/CPCAPI2_${platform}_sim_intel.dSYM"
  elif [[ $architecture = "Sim_Both" ]]; then
    # arm and intel simulators in separate frameworks
    if [[ $no_xcframework = "0" ]]; then
      xcodebuild -create-xcframework -framework "build_${platform}_sim_arm/CPCAPI2.framework" -framework "build_${platform}_sim_intel/CPCAPI2.framework" -output "build/CPCAPI2.xcframework"
    else
      cp -R "build_${platform}_sim_arm/CPCAPI2.framework" "build/CPCAPI2_${platform}_sim_arm.framework"
      cp -R "build_${platform}_sim_intel/CPCAPI2.framework" "build/CPCAPI2_${platform}_sim_intel.framework"
    fi
    cp -R "build_${platform}_sim_arm/CPCAPI2.dSYM" "build/CPCAPI2_${platform}_sim_arm.dSYM"
    cp -R "build_${platform}_sim_intel/CPCAPI2.dSYM" "build/CPCAPI2_${platform}_sim_intel.dSYM"
  elif [[ $architecture = "Universal" ]]; then
    # both native and intel simulator in separate frameworks
    if [[ $no_xcframework = "0" ]]; then
      xcodebuild -create-xcframework -framework "build_${platform}/CPCAPI2.framework" -framework "build_${platform}_sim_intel/CPCAPI2.framework" -output "build/CPCAPI2.xcframework"
    else
      cp -R "build_${platform}/CPCAPI2.framework" "build/CPCAPI2_${platform}.framework"
      cp -R "build_${platform}_sim_intel/CPCAPI2.framework" "build/CPCAPI2_${platform}_sim_intel.framework"
    fi
    cp -R "build_${platform}_arm/CPCAPI2.dSYM" "build/CPCAPI2_${platform}_arm.dSYM"
    cp -R "build_${platform}_intel/CPCAPI2.dSYM" "build/CPCAPI2_${platform}_intel.dSYM"
    cp -R "build_${platform}_sim_intel/CPCAPI2.dSYM" "build/CPCAPI2_${platform}_sim_intel.dSYM"
  else
    # arm and intel simulators in the same framework
    if [[ $no_xcframework = "0" ]]; then
      xcodebuild -create-xcframework -framework "build_sim/CPCAPI2.framework" -output "build/CPCAPI2.xcframework"
    else
      cp -R "build_sim/CPCAPI2.framework" "build/CPCAPI2_${platform}_sim.framework"
    fi
    cp -R "build_${platform}_sim_arm/CPCAPI2.dSYM" "build/CPCAPI2_${platform}_sim_arm.dSYM"
    cp -R "build_${platform}_sim_intel/CPCAPI2.dSYM" "build/CPCAPI2_${platform}_sim_intel.dSYM"  
  fi

elif [[ $platform = "osx" && ($architecture = "All" || $architecture = "Universal") ]]; then
#  read -s -n 1 -p "Press any key to create OSX universal framework..."
  echo ""

  # We must create a simulator library that contains the two architectures, since we 
  # can't add them separately to an xcframework.
  cp -R "build_${platform}_arm/" build
  rm -r build/CPCAPI2.framework/Versions/A/CPCAPI2
  lipo -create "build_${platform}_arm/CPCAPI2.framework/CPCAPI2" "build_${platform}_intel/CPCAPI2.framework/CPCAPI2" -output build/CPCAPI2.framework/Versions/A/CPCAPI2

  # copy the DSYM files for each architecture
  cp -R "build_${platform}_arm/CPCAPI2.dSYM" build/CPCAPI2_${platform}_arm.dSYM
  cp -R "build_${platform}_intel/CPCAPI2.dSYM" build/CPCAPI2_${platform}_intel.dSYM  

elif [[ $platform = "osx" ]]; then
#  read -s -n 1 -p "Press any key to create $platform $architecture framework..."
  echo ""

  # for OSX we don't build an xcframework, we use the framework
  cp -R "build_${platform}_${architecture:l}/CPCAPI2.framework" build
  cp -R "build_${platform}_${architecture:l}/CPCAPI2.dSYM" "build/CPCAPI2_${platform}_${architecture:l}.dSYM"
  current_lib_name="CPCAPI2"

else
#  read -s -n 1 -p "Press any key to create $platform $architecture framework..."
  echo ""

  # for iOS we build an xcframework
  if [[ $no_xcframework = "0" ]]; then
    xcodebuild -create-xcframework -framework "build_${platform}_${architecture:l}/CPCAPI2.framework" -output "build/CPCAPI2.xcframework"
  else
    cp -R "build_${platform}_${architecture:l}/CPCAPI2.framework" "build/CPCAPI2_${platform}_${architecture:l}.framework"
  fi
  cp -R "build_${platform}_${architecture:l}/CPCAPI2.dSYM" "build/CPCAPI2_${platform}_${architecture:l}.dSYM"

fi


pre_popd_stack_len=$(dirs -v | wc -l | xargs)
if [[ "${pre_pushd_stack_len}" != "${pre_popd_stack_len}" ]]; then
  echo "popping directory: "$(dirs -v | head -1 | awk '{print $2}')
  popd
fi


# ===========================================================
# Copy the result
# ===========================================================

# we do this part in the original working folder since output_folder could be relative

mkdir -p $output_folder

# NOTE: In the paths below we have to put the wildcard outside the double quotes for it 
# to be a wildcard, but we keep the quotes elsewhere because they allow spaces in the path

if [[ $current_dsym_name == "" ]]; then
  # I remove any files from the destination that would be copied so that the date is correct, 
  # otherwise it doesn't get updated for frameworks because they're folders.
  for f in "${intermediate_folder}build/"*; do rm -R -f "${output_folder}$(basename ${f})"; done


  # .jza. current_dsym_name being set to empty string is for scenario where we are building for all
  # platforms?
  # why do we need separate copying logic based on current_dsym_name empty vs not?
  if [[ $platform = "ios" ]]; then
    cp -R "${intermediate_folder}build/CPCAPI2.xcframework" $output_folder
  else
    cp -R "${intermediate_folder}build/CPCAPI2.framework" $output_folder
  fi

  cp -R "${intermediate_folder}build/"*.dSYM $output_folder
  
else
  # we are debugging so we only copy the needed lib and dSYM and use a common name

  echo "lib source: ${intermediate_folder}build/${current_lib_name}.${framework_extension}"
  echo "dSYM source: ${intermediate_folder}build/${current_dsym_name}"

  rm -R -f "${output_folder}/CPCAPI2.${framework_extension}"
  rm -R -f "${output_folder}/CPCAPI2.dSYM"
  cp -R "${intermediate_folder}build/${current_lib_name}.${framework_extension}" "${output_folder}/CPCAPI2.${framework_extension}"
  cp -R "${intermediate_folder}build/${current_dsym_name}" "${output_folder}/CPCAPI2.dSYM"
fi
