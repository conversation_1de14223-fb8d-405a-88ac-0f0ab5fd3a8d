#!/bin/bash

set -e

#!/bin/bash
for i in "$@"
do
case $i in
    -q=*)
    QA_BUILD="${i#*=}"
    ;;
    -m=*)
    MC_BUILD="${i#*=}"
    ;;
    --default)
    DEFAULT=YES
    ;;
    --nozip)
    NOZIP="True"
    ;;
    *)
            # unknown option
    ;;
esac
done
echo QA_BUILD = ${QA_BUILD}
echo MC_BUILD = ${MC_BUILD}

mkdir -p CPCAPI2_Linux/docs CPCAPI2_Linux/libs CPCAPI2_Linux/sample \
CPCAPI2_Linux/include

# Copy lib file.
cp build/cpcapi2_shared_library.so CPCAPI2_Linux/libs/

if [ "$MC_BUILD" = "True" ]; then

   rsync -r --verbose --exclude 'build' $(pwd)/../../../samples/MediaCluster/SimplePhone CPCAPI2_Linux/sample/

   #change docker build mount path
   sed -i 's|"$(pwd)"/../../../../../,target=/CPCAPI2|"$(pwd)"/../../../,target=/CPCAPI2_Linux|g' CPCAPI2_Linux/sample/SimplePhone/docker_build/build.sh

   #change workdir of the docker container
   sed -i 's|/CPCAPI2/core/samples/MediaCluster/SimplePhone/docker_build/build|/CPCAPI2_Linux/sample/SimplePhone/docker_build/build|g' CPCAPI2_Linux/sample/SimplePhone/docker_build/Dockerfile.centos

else

   # Copy SimplePhone QT example.
   if [ "$QA_BUILD" = "True" ]; then
     rsync -r --verbose $(pwd)/../../../samples/QT/SimplePhone CPCAPI2_Linux/sample/
   else
     rsync -r --verbose --exclude 'build' $(pwd)/../../../samples/QT/SimplePhone CPCAPI2_Linux/sample/
   fi

   #change includepath in CPCAPI2_Linux/sample/SimplePhone/SimplePhone.pro
   sed -i 's|../../../../core/CPCAPI2/interface/public|../../include|g' CPCAPI2_Linux/sample/SimplePhone/SimplePhone.pro
   sed -i 's|-L$$PWD/../../../cpcapi2_shared_library/Linux/docker_build/build/|-L$$PWD/../../libs/|g' CPCAPI2_Linux/sample/SimplePhone/SimplePhone.pro

   #change docker build mount path
   sed -i 's|"$(pwd)"/../../../../../,target=/CPCAPI2|"$(pwd)"/../../../,target=/CPCAPI2_Linux|g' CPCAPI2_Linux/sample/SimplePhone/docker_build/build.sh

   #change workdir of the docker container
   sed -i 's|/CPCAPI2/core/samples/QT/SimplePhone/docker_build/build|/CPCAPI2_Linux/sample/SimplePhone/docker_build/build|g' CPCAPI2_Linux/sample/SimplePhone/docker_build/Dockerfile.centos
fi


if [ "$MC_BUILD" = "True" ]; then
   mkdir -p CPCAPI2_Linux/include/public CPCAPI2_Linux/include/experimental
   cp -R $(pwd)/../../../CPCAPI2/interface/public CPCAPI2_Linux/include
   cp -R $(pwd)/../../../CPCAPI2/interface/experimental CPCAPI2_Linux/include
else
   # Copy public headers.
   cp -R $(pwd)/../../../CPCAPI2/interface/public/* CPCAPI2_Linux/include
fi


# Generate and copy docs.
cd $(pwd)/../../../docs/doxygen_C++/
doxygen sdk_doxyfile
cd -

zip -j -r CPCAPI2_Linux/docs/CPCAPI2_Linux-docs.zip $(pwd)/../../../docs/doxygen_C++/html_C++/html/*
cp $(pwd)/../../../docs/to_publish/common_docs/SDK_Documentation.html CPCAPI2_Linux/docs/

cmd=$(svn info | grep Revision)
echo $cmd

IFS=': ' read -r -a array <<< "$cmd"

export SVN_REV=$BRANCH"-"${array[1]}

set +e
# Delete old version.
rm CPCAPI2-Linux-*
set -e

if [ "$NOZIP" != "True" ]; then
    zip -r CPCAPI2-Linux$SVN_REV.zip CPCAPI2_Linux
fi
