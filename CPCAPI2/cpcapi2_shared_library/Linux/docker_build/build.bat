if not exist "build" mkdir build
if not exist "%USERPROFILE%"\.conan mkdir "%USERPROFILE%"\.conan

docker build -t cpcapi2_builder:ubuntu.clang -f ..\..\..\projects\docker_build\Dockerfile.ubuntu.clang ..\..\..\projects\docker_build
docker build -t cpcapi2_shared_library_builder:ubuntu.clang -f Dockerfile.ubuntu.clang .
docker run --name cpcapi2_shared_library_builder --rm --mount type=bind,source="%cd%"\..\..\..\..\,target=/CPCAPI2 --mount type=bind,source="%USERPROFILE%"\.conan\,target=/tmp/conan/.conan --env CPCAPI2_CONAN_USER --env CPCAPI2_CONAN_APIKEY cpcapi2_shared_library_builder:ubuntu.clang "%*"
