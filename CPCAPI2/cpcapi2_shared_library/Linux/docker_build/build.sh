#!/bin/bash

set -e
set -x

if [ ! -d build ]
then
  mkdir build
fi
mkdir -p $HOME/.conan

docker build -t cpcapi2_builder:ubuntu.clang -f ../../../projects/docker_build/Dockerfile.ubuntu.clang ../../../projects/docker_build
docker build -t cpcapi2_shared_library_builder:ubuntu.clang -f Dockerfile.ubuntu.clang .

if [ -d "/home/<USER>/.ccache:/ccache" ]; then
  CCACHE_VOLUME="-v /home/<USER>/.ccache:/ccache"
fi
docker run --name cpcapi2_shared_library_builder $CCACHE_VOLUME -e CCACHE_DIR=/ccache --rm --mount type=bind,source="$(pwd)"/../../../../,target=/CPCAPI2 --mount type=bind,source=$HOME/.conan/,target=/tmp/conan/.conan --env CPCAPI2_CONAN_USER --env CPCAPI2_CONAN_APIKEY cpcapi2_shared_library_builder:ubuntu.clang "$@"
