cmake_minimum_required(VERSION 3.8.0)

# Do we want CPCAPI2_INCLUDE_UNRELEASED_HEADERS defined for this build?
add_definitions(-DCPCAPI2_INCLUDE_UNRELEASED_HEADERS)
include(${CMAKE_CURRENT_SOURCE_DIR}/../../../projects/cmake/toolchains/configure.cmake) # Must be included before project()

project (cpcapi2_shared_library LANGUAGES ${CPCAPI2_LANGUAGES})
init_project()

SET(USE_X11 ON CACHE BOOL "Build with X11 support")
SET(USE_PULSEAUDIO ON CACHE BOOL "Build with PusleAudio support")

set(CMAKE_THREAD_PREFER_PTHREAD ON)
set(THREADS_PREFER_PTHREAD_FLAG ON)

include(${CMAKE_CURRENT_SOURCE_DIR}/../../../projects/cmake/CPCAPI2/CPCAPI2.cmake)

add_custom_target(build_CPCAPI2_shared_library ALL)
add_dependencies(build_CPCAPI2_shared_library CPCAPI2_Shared)
