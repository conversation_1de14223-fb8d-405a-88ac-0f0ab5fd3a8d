#!/bin/bash

set -e
set -x

for i in "$@"
do
case $i in
    --cpcapi2_brand=*)
    CPCAPI2_BRAND="${i#*=}"
    ;;
    --cpcapi2_build_stamp=*)
    CPCAPI2_BUILD_STAMP="${i#*=}"
    ;;
    --cpcapi2_version=*)
    CPCAPI2_VERSION="${i#*=}"
    ;;
    --target=*)
    TARGET="--target ${i#*=}"
    ;;
    *)

    ;;
esac
done


CMAKE=cmake
if command -v cmake3 >/dev/null 2>&1;
then
  CMAKE=cmake3
fi

export LC_CTYPE=en_US.UTF-8

BUILD_DIR=/CPCAPI2/core/cpcapi2_shared_library/Linux/docker_build/build
ls /CPCAPI2/core/cpcapi2_shared_library/Linux/docker_build
#build directory is created in dokcerfile by the WORKDIR command
#mkdir -p $BUILD_DIR
cd $BUILD_DIR

if [ -n "$CPCAPI2_BRAND" ]; then
   if [ -z "$CPCAPI2_BUILD_STAMP" ]; then
     echo "Must specify --cpcapi2_build_stamp if specifying --cpcapi2_brand"
     exit 1
   fi
   if [ -z "$CPCAPI2_VERSION" ]; then
     echo "Must specify --cpcapi2_version if specifying --cpcapi2_brand"
     exit 1
   fi
fi

if [ -n "$CPCAPI2_BRAND" ]; then
$CMAKE -G Ninja -DCPCAPI2_BRAND=$CPCAPI2_BRAND -DCPCAPI2_BRAND_STAMP=$CPCAPI2_BUILD_STAMP -DCPCAPI2_BRAND_RELEASE=$CPCAPI2_VERSION -DCPCAPI2_CONAN=1 -DCMAKE_BUILD_TYPE=RelWithDebInfo ../
else
$CMAKE -G Ninja -DCPCAPI2_CONAN=1 -DCMAKE_BUILD_TYPE=RelWithDebInfo ../
fi

ccache -z
time $CMAKE --build . $TARGET
ccache -s
