cmake_minimum_required(VERSION 3.8.0)

# Do we want CPCAPI2_INCLUDE_UNRELEASED_HEADERS defined for this build?
add_definitions(-DCPCAPI2_INCLUDE_UNRELEASED_HEADERS)
include(${CMAKE_CURRENT_SOURCE_DIR}/../projects/cmake/toolchains/configure.cmake) # Must be included before project()

project (cpcapi2_shared_library LANGUAGES ${CPCAPI2_LANGUAGES})
init_project()

if (OS_LINUX)
   SET(USE_X11 ON CACHE BOOL "Build with X11 support")
   SET(USE_PULSEAUDIO ON CACHE BOOL "Build with PusleAudio support")
endif()

set(CMAKE_THREAD_PREFER_PTHREAD ON)
set(THREADS_PREFER_PTHREAD_FLAG ON)

include(${TOP_SOURCE_PATH}/projects/cmake/CPCAPI2/CPCAPI2.cmake)

add_custom_target(build_CPCAPI2_shared_library ALL)
add_dependencies(build_CPCAPI2_shared_library CPCAPI2_Shared)

if (OS_MACOS OR OS_IOS)
  add_dependencies(build_CPCAPI2_shared_library CPCAPI2ObjC)

  set(MODULE_NAME "CPCAPI2ObjC")		# must match library target name in CPCObjAPI2.cmake
  set(PRODUCT_NAME "CPCAPI2-Shared-Library")

  include(${TOP_SOURCE_PATH}/projects/cmake/CPCAPI2/CPCAPI2ObjC.cmake)
  include(${TOP_SOURCE_PATH}/projects/cmake/debug_symbols.cmake)

  seperate_debug_symbols(${MODULE_NAME} SKIP_COMPRESS 1)

  set_target_properties(${MODULE_NAME} PROPERTIES
    FRAMEWORK TRUE
    MACOSX_FRAMEWORK_IDENTIFIER counterpath.CPCAPI2
    FRAMEWORK_VERSION A
    MACOSX_FRAMEWORK_BUNDLE_VERSION 1
    MACOSX_FRAMEWORK_SHORT_VERSION_STRING 1.0

    # DRL This fails because of variables like "${PRODUCT_NAME:rfc1034identifier}" cause a syntax error.
    #MACOSX_FRAMEWORK_INFO_PLIST ${CMAKE_CURRENT_SOURCE_DIR}/../projects/xcode/CPCAPI2/CPCAPI2_Framework/Info.plist

    OUTPUT_NAME CPCAPI2
  )

  set(PRIVACYINFO_PATH "${TOP_SOURCE_PATH}/projects/xcode/CPCAPI2/CPCAPI2_Framework")
  # cmake doesn't do this for us (several people have mentioned it's a cmake bug) so we do it manually...
  if(OS_IOS)
    add_custom_command(TARGET CPCAPI2_Shared POST_BUILD
      COMMAND /usr/libexec/PlistBuddy -c "Delete :MinimumOSVersion" $<TARGET_FILE_DIR:CPCAPI2_Shared>/CPCAPI2.framework/Info.plist > /dev/null 2>&1 || true
      COMMAND /usr/libexec/PlistBuddy -c "Add :MinimumOSVersion string ${CMAKE_OSX_DEPLOYMENT_TARGET}" $<TARGET_FILE_DIR:CPCAPI2_Shared>/CPCAPI2.framework/Info.plist
      COMMAND cp ${PRIVACYINFO_PATH}/PrivacyInfo.ios.xcprivacy $<TARGET_FILE_DIR:CPCAPI2_Shared>/CPCAPI2.framework/PrivacyInfo.xcprivacy
    )
  else()
    add_custom_command(TARGET CPCAPI2_Shared POST_BUILD
      COMMAND /usr/libexec/PlistBuddy -c "Delete :LSMinimumSystemVersion" $<TARGET_FILE_DIR:CPCAPI2_Shared>/CPCAPI2.framework/Resources/Info.plist > /dev/null 2>&1  || true
      COMMAND /usr/libexec/PlistBuddy -c "Add :LSMinimumSystemVersion string ${CMAKE_OSX_DEPLOYMENT_TARGET}" $<TARGET_FILE_DIR:CPCAPI2_Shared>/CPCAPI2.framework/Resources/Info.plist
      COMMAND cp ${PRIVACYINFO_PATH}/PrivacyInfo.mac.xcprivacy $<TARGET_FILE_DIR:CPCAPI2_Shared>/CPCAPI2.framework/Resources/PrivacyInfo.xcprivacy
    )
  endif()
  
elseif (OS_WINDOWS)
  target_sources(CPCAPI2_Shared PRIVATE ${SHARED_PATH}/WebRTCv/trunk/webrtc/modules/video_render/windows/WindowsVideoControl.cc ${CMAKE_CURRENT_SOURCE_DIR}/Windows/CPCAPI2_SharedLibrary.rc)
  target_include_directories(CPCAPI2_Shared PRIVATE ${SHARED_PATH}/WebRTCv/trunk/webrtc/modules/video_render/windows)
  target_link_libraries(CPCAPI2_Shared PRIVATE delayimp.lib)
  set_target_properties(CPCAPI2_Shared PROPERTIES LINK_FLAGS "/DELAYLOAD:wpcap.dll /NXCOMPAT /DYNAMICBASE /SUBSYSTEM:WINDOWS /alternatename:___iob_func=___intel_lib_iob_func")

  if (BUILD_UI_CONTROLS MATCHES 1)
    if (CMAKE_PLATFORM_NAME MATCHES x86_64)
      set(BUILD_DIR "${TOP_SOURCE_PATH}/build/x64")
      set(MSBUILD_PLATFORM "x64")
      set(SAMPLES_DIR "${TOP_SOURCE_PATH}/samples/windows/dotnet/SharedLibs/x64")
    else()
      set(BUILD_DIR "${TOP_SOURCE_PATH}/build")
      set(MSBUILD_PLATFORM "WIN32")
      set(SAMPLES_DIR "${TOP_SOURCE_PATH}/samples/windows/dotnet/SharedLibs/x86")
    endif()
    string(REPLACE "/" "\\" BUILD_DIR "${BUILD_DIR}")
    string(REPLACE "/" "\\" SAMPLES_DIR "${SAMPLES_DIR}")
    if (CMAKE_BUILD_TYPE MATCHES Debug)
      set(MSBUILD_CONF "Debug")
    else()
      set(MSBUILD_CONF "Release")
    endif()
    set(UI_CONTROLS_DIR "${TOP_SOURCE_PATH}/cpcapi2_shared_library/Windows/CPCAPI2_UI_Controls")
    string(REPLACE "/" "\\" UI_CONTROLS_DIR "${UI_CONTROLS_DIR}")

    # patch for Visual Studio generator(s):
    if ("${CMAKE_GENERATOR}" MATCHES "^Visual Studio")
      set(BINDIR_ACTUAL "${CMAKE_BINARY_DIR}/${CMAKE_BUILD_TYPE}")
    else()
      set(BINDIR_ACTUAL "${CMAKE_BINARY_DIR}")
    endif("${CMAKE_GENERATOR}" MATCHES "^Visual Studio")
    string(REPLACE "/" "\\" BINDIR_ACTUAL "${BINDIR_ACTUAL}")
    message ("Output Directory: ${BINDIR_ACTUAL}")

    add_custom_command(TARGET CPCAPI2_Shared PRE_BUILD
      COMMAND if not exist ${BUILD_DIR} mkdir ${BUILD_DIR}
      VERBATIM
    )
    add_custom_command(TARGET CPCAPI2_Shared POST_BUILD
      COMMAND xcopy /y /f ${LIBRARY_OUTPUT_NAME}.dll ${BUILD_DIR}
      COMMAND xcopy /y /f ${LIBRARY_OUTPUT_NAME}.lib ${BUILD_DIR}
      COMMAND (if exist ${LIBRARY_OUTPUT_NAME}.pdb xcopy /y /f ${LIBRARY_OUTPUT_NAME}.pdb ${BUILD_DIR})
      WORKING_DIRECTORY ${BINDIR_ACTUAL}
      VERBATIM
    )
    add_custom_command(TARGET CPCAPI2_Shared POST_BUILD
      COMMAND MSBuild.exe CPCAPI2_UI_Controls.vcxproj /property:Configuration=${MSBUILD_CONF} /p:Platform=${MSBUILD_PLATFORM} /fl /flp:logfile=CPCAPI2_UI_Controls.log
      WORKING_DIRECTORY ${UI_CONTROLS_DIR}
      USES_TERMINAL
    )
    add_custom_command(TARGET CPCAPI2_Shared POST_BUILD
      COMMAND xcopy /y /f ${LIBRARY_OUTPUT_NAME}.dll ${SAMPLES_DIR}
      COMMAND (if exist ${LIBRARY_OUTPUT_NAME}.pdb xcopy /y /f ${LIBRARY_OUTPUT_NAME}.pdb ${SAMPLES_DIR})
      WORKING_DIRECTORY ${BINDIR_ACTUAL}
      VERBATIM
    )
  endif(BUILD_UI_CONTROLS MATCHES 1)

  if (SHAREDLIB_NOWARN MATCHES 1)
    # set warning level 0 if SHAREDLIB_NOWARN set. Doesn't always work
    if(CMAKE_CXX_FLAGS MATCHES "/W[1-4]")
      string(REGEX REPLACE "/W[1-4]" "/W0" CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS}")
    else()
      add_compiler_flag("/W0")
    endif()
  endif(SHAREDLIB_NOWARN MATCHES 1)
endif()
