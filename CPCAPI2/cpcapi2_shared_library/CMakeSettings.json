﻿{
  "configurations": [
    {
      "name": "_SharedLibrary-Windows-x86-Debug",
      "generator": "Ninja",
      "configurationType": "Debug",
      "buildRoot": "${projectDir}/../build/Windows-x86-Debug",
      "installRoot": "${projectDir}\\out\\install\\${name}",
      "cmakeCommandArgs": "-DLIBRARY_OUTPUT_NAME=CPCAPI2_SharedLibrary_Debug -DBUILD_UI_CONTROLS=1",
      "buildCommandArgs": "",
      "ctestCommandArgs": "",
      "inheritEnvironments": [ "msvc_x86_x64" ],
      "variables": [],
      "cmakeToolchain": "${projectDir}/../projects/cmake/toolchains/windows-msvc-x86.cmake"
    },
    {
      "name": "_SharedLibrary-Windows-x86-Release",
      "generator": "Ninja",
      "configurationType": "RelWithDebInfo",
      "buildRoot": "${projectDir}/../build/Windows-x86-Release",
      "installRoot": "${projectDir}\\out\\install\\${name}",
      "cmakeCommandArgs": "-DLIBRARY_OUTPUT_NAME=CPCAPI2_SharedLibrary -DBUILD_UI_CONTROLS=1",
      "buildCommandArgs": "",
      "ctestCommandArgs": "",
      "inheritEnvironments": [ "msvc_x86_x64" ],
      "cmakeToolchain": "${projectDir}/../projects/cmake/toolchains/windows-msvc-x86.cmake"
    },
    {
      "name": "_SharedLibrary-Windows-x86_64-Debug",
      "generator": "Ninja",
      "configurationType": "Debug",
      "buildRoot": "${projectDir}/../build/Windows-x86_64-Debug",
      "installRoot": "${projectDir}\\out\\install\\${name}",
      "cmakeCommandArgs": "-DLIBRARY_OUTPUT_NAME=CPCAPI2_SharedLibrary_Debug -DBUILD_UI_CONTROLS=1",
      "buildCommandArgs": "",
      "ctestCommandArgs": "",
      "inheritEnvironments": [ "msvc_x64_x64" ],
      "variables": [],
      "cmakeToolchain": "${projectDir}/../projects/cmake/toolchains/windows-msvc-x86_64.cmake"
    },
    {
      "name": "_SharedLibrary-Windows-x86_64-Release",
      "generator": "Ninja",
      "configurationType": "RelWithDebInfo",
      "buildRoot": "${projectDir}/../build/Windows-x86_64-Release",
      "installRoot": "${projectDir}\\out\\install\\${name}",
      "cmakeCommandArgs": "-DLIBRARY_OUTPUT_NAME=CPCAPI2_SharedLibrary -DBUILD_UI_CONTROLS=1",
      "buildCommandArgs": "",
      "ctestCommandArgs": "",
      "inheritEnvironments": [ "msvc_x64_x64" ],
      "variables": [],
      "cmakeToolchain": "${projectDir}/../projects/cmake/toolchains/windows-msvc-x86_64.cmake"
    },
    {
      "name": "_SharedLibrary-CentOS-x86_64-Debug",
      "generator": "Ninja",
      "configurationType": "Debug",
      "buildRoot": "${projectDir}\\out\\build\\CentOS-x86_64-Debug",
      "installRoot": "${projectDir}\\out\\install\\${name}",
      "cmakeExecutable": "/usr/bin/cmake",
      "cmakeCommandArgs": "",
      "buildCommandArgs": "",
      "ctestCommandArgs": "",
      "cmakeToolchain": "${projectDir}/../projects/cmake/toolchains/centos-devtoolset8-x86_64.cmake",
      "inheritEnvironments": [ "linux_x64" ],
      "wslPath": "${defaultWSLPath}",
      "addressSanitizerRuntimeFlags": "detect_leaks=0",
      "variables": []
    },
    {
      "name": "_SharedLibrary-CentOS-x86_64-Release",
      "generator": "Ninja",
      "configurationType": "RelWithDebInfo",
      "buildRoot": "${projectDir}\\out\\build\\CentOS-x86_64-Release",
      "installRoot": "${projectDir}\\out\\install\\${name}",
      "cmakeExecutable": "/usr/bin/cmake",
      "cmakeCommandArgs": "",
      "buildCommandArgs": "",
      "ctestCommandArgs": "",
      "cmakeToolchain": "${projectDir}/../projects/cmake/toolchains/centos-devtoolset8-x86_64.cmake",
      "inheritEnvironments": [ "linux_x64" ],
      "wslPath": "${defaultWSLPath}",
      "addressSanitizerRuntimeFlags": "detect_leaks=0",
      "variables": []
    }
  ]
}