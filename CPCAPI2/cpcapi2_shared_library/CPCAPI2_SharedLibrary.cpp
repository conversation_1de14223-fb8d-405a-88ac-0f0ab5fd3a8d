// CPCAPI2_SharedLibrary.cpp : Defines the exported functions for the DLL application.
//

#include <cpcapi2.h>
#include <cpcapi2utils.h>
#include "CPCAPI2_SharedLibrary.h"

CPCAPI2_SHAREDLIBRARY_API CPCAPI2::Phone* CPCAPI2_CreatePhone(void)
{
   return CPCAPI2::Phone::create();
}

CPCAPI2_SHAREDLIBRARY_API CPCAPI2::SipAccount::SipAccountManager* CPCAPI2_CreateSipAccountManager(CPCAPI2::Phone* phone)
{
   return CPCAPI2::SipAccount::SipAccountManager::getInterface(phone);
}

CPCAPI2_SHAREDLIBRARY_API CPCAPI2::SipConversation::SipConversationManager* CPCAPI2_CreateSipConversationManager(CPCAPI2::Phone* phone)
{
   return CPCAPI2::SipConversation::SipConversationManager::getInterface(phone);
}

CPCAPI2_SHAREDLIBRARY_API CPCAPI2::Media::MediaManager* CPCAPI2_CreateMediaManager(CPCAPI2::Phone* phone)
{
   return CPCAPI2::Media::MediaManager::getInterface(phone);
}

CPCAPI2_SHAREDLIBRARY_API CPCAPI2::SipEvent::SipEventManager* CPCAPI2_CreateSipEventManager(CPCAPI2::Phone* phone)
{
   return CPCAPI2::SipEvent::SipEventManager::getInterface(phone);
}

CPCAPI2_SHAREDLIBRARY_API CPCAPI2::SipPresence::SipPresenceManager* CPCAPI2_CreateSipPresenceManager(CPCAPI2::Phone* phone)
{
   return CPCAPI2::SipPresence::SipPresenceManager::getInterface(phone);
}

CPCAPI2_SHAREDLIBRARY_API std::string CPCAPI2_wstringToUtf8(const wchar_t* str)
{
   return CPCAPI2::StringConv::wstringToUtf8(str);
}

CPCAPI2_SHAREDLIBRARY_API std::wstring CPCAPI2_utf8ToWstring(const char* str)
{
   return CPCAPI2::StringConv::utf8ToWstring(str);
}
