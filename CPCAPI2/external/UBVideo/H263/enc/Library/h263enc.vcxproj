﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="12.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="DebugNoSSL|Win32">
      <Configuration>DebugNoSSL</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ReleaseNoSSL|Win32">
      <Configuration>ReleaseNoSSL</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{E1A1C3D1-C648-4911-9974-7B34F6CE8F1E}</ProjectGuid>
    <RootNamespace>h263enc</RootNamespace>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseNoSSL|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v120</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v120</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v120</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v120</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseNoSSL|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC71.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC71.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC71.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC71.props" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.30319.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">$(SolutionDir)$(Configuration)\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">$(Configuration)\</IntDir>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">$(SolutionDir)$(Configuration)\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">$(Configuration)\</IntDir>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">$(Configuration)\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">$(Configuration)\</IntDir>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='ReleaseNoSSL|Win32'">$(Configuration)\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='ReleaseNoSSL|Win32'">$(Configuration)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <Optimization>Full</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;_LIB;MMX_DETECTED;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <FunctionLevelLinking>false</FunctionLevelLinking>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <PrecompiledHeaderOutputFile>.\Debug/Library.pch</PrecompiledHeaderOutputFile>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ProgramDataBaseFileName>$(IntDir)vc$(PlatformToolsetVersion).pdb</ProgramDataBaseFileName>
      <WarningLevel>Level4</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Lib>
      <OutputFile>$(OutDir)UBVEnc10.lib</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <Optimization>MaxSpeed</Optimization>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;_LIB;MMX_DETECTED;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <StringPooling>true</StringPooling>
      <FunctionLevelLinking>false</FunctionLevelLinking>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <PrecompiledHeaderOutputFile>.\Release/Library.pch</PrecompiledHeaderOutputFile>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ProgramDataBaseFileName>$(IntDir)vc$(PlatformToolsetVersion).pdb</ProgramDataBaseFileName>
      <WarningLevel>Level4</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Lib>
      <OutputFile>$(OutDir)UBVEnc10.lib</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">
    <ClCompile>
      <Optimization>Full</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;_LIB;MMX_DETECTED;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <FunctionLevelLinking>false</FunctionLevelLinking>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <PrecompiledHeaderOutputFile>.\Debug/Library.pch</PrecompiledHeaderOutputFile>
      <AssemblerListingLocation>.\Debug/</AssemblerListingLocation>
      <ObjectFileName>.\Debug/</ObjectFileName>
      <ProgramDataBaseFileName>.\Debug/</ProgramDataBaseFileName>
      <WarningLevel>Level4</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Lib>
      <OutputFile>$(OutDir)UBVEnc10.lib</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseNoSSL|Win32'">
    <ClCompile>
      <Optimization>MaxSpeed</Optimization>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;_LIB;MMX_DETECTED;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <StringPooling>true</StringPooling>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <FunctionLevelLinking>false</FunctionLevelLinking>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <PrecompiledHeaderOutputFile>.\Release/Library.pch</PrecompiledHeaderOutputFile>
      <AssemblerListingLocation>.\Release/</AssemblerListingLocation>
      <ObjectFileName>.\Release/</ObjectFileName>
      <ProgramDataBaseFileName>.\Release/</ProgramDataBaseFileName>
      <WarningLevel>Level4</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Lib>
      <OutputFile>$(OutDir)UBVEnc10.lib</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="UB_Live_P_Enc_1_0\advancedintracoding_annex_i.c">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">Disabled</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='ReleaseNoSSL|Win32'">MaxSpeed</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='ReleaseNoSSL|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <ClCompile Include="UB_Live_P_Enc_1_0\blockoperations.c">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">Disabled</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='ReleaseNoSSL|Win32'">MaxSpeed</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='ReleaseNoSSL|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <ClCompile Include="UB_Live_P_Enc_1_0\bpic_annex_o.c">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">Disabled</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='ReleaseNoSSL|Win32'">MaxSpeed</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='ReleaseNoSSL|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <ClCompile Include="UB_Live_P_Enc_1_0\deblockingfilter_annex_j.c">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">Disabled</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='ReleaseNoSSL|Win32'">MaxSpeed</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='ReleaseNoSSL|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <ClCompile Include="UB_Live_P_Enc_1_0\encodecoefficients.c">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">Disabled</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='ReleaseNoSSL|Win32'">MaxSpeed</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='ReleaseNoSSL|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <ClCompile Include="UB_Live_P_Enc_1_0\encoder.c">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">Disabled</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='ReleaseNoSSL|Win32'">MaxSpeed</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='ReleaseNoSSL|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <ClCompile Include="UB_Live_P_Enc_1_0\idct.c">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">Disabled</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='ReleaseNoSSL|Win32'">MaxSpeed</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='ReleaseNoSSL|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <ClCompile Include="UB_Live_P_Enc_1_0\interpolation.c">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">Disabled</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='ReleaseNoSSL|Win32'">MaxSpeed</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='ReleaseNoSSL|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <ClCompile Include="UB_Live_P_Enc_1_0\ipic.c">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">Disabled</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='ReleaseNoSSL|Win32'">MaxSpeed</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='ReleaseNoSSL|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <ClCompile Include="UB_Live_P_Enc_1_0\iquant.c">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">Disabled</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='ReleaseNoSSL|Win32'">MaxSpeed</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='ReleaseNoSSL|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <ClCompile Include="UB_Live_P_Enc_1_0\mblockoperations.c">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">Disabled</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='ReleaseNoSSL|Win32'">MaxSpeed</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='ReleaseNoSSL|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <ClCompile Include="UB_Live_P_Enc_1_0\motionest.c">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">Disabled</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='ReleaseNoSSL|Win32'">MaxSpeed</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='ReleaseNoSSL|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <ClCompile Include="UB_Live_P_Enc_1_0\outputcoefficients.c">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">Disabled</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='ReleaseNoSSL|Win32'">MaxSpeed</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='ReleaseNoSSL|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <ClCompile Include="UB_Live_P_Enc_1_0\ppic.c">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">Disabled</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='ReleaseNoSSL|Win32'">MaxSpeed</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='ReleaseNoSSL|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <ClCompile Include="UB_Live_P_Enc_1_0\qdct.c">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">Disabled</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='ReleaseNoSSL|Win32'">MaxSpeed</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='ReleaseNoSSL|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <ClCompile Include="UB_Live_P_Enc_1_0\ratectrl.c">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">Disabled</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='ReleaseNoSSL|Win32'">MaxSpeed</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='ReleaseNoSSL|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <ClCompile Include="UB_Live_P_Enc_1_0\slicestructured_annex_k.c">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">Disabled</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='ReleaseNoSSL|Win32'">MaxSpeed</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='ReleaseNoSSL|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <ClCompile Include="UB_Live_P_Enc_1_0\snr.c">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">Disabled</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='ReleaseNoSSL|Win32'">MaxSpeed</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='ReleaseNoSSL|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <ClCompile Include="UB_Live_P_Enc_1_0\ublivepencapi.c">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">Disabled</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='ReleaseNoSSL|Win32'">MaxSpeed</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='ReleaseNoSSL|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <ClCompile Include="UB_Live_P_Enc_1_0\ubvbuffer.c">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">Disabled</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='ReleaseNoSSL|Win32'">MaxSpeed</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='ReleaseNoSSL|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <ClCompile Include="UB_Live_P_Enc_1_0\ubvrtp.c">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">Disabled</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='DebugNoSSL|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Disabled</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">EnableFastChecks</BasicRuntimeChecks>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='ReleaseNoSSL|Win32'">MaxSpeed</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='ReleaseNoSSL|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="UB_Live_P_Enc_1_0\advancedintracoding_annex_i.h" />
    <ClInclude Include="UB_Live_P_Enc_1_0\blockoperations.h" />
    <ClInclude Include="UB_Live_P_Enc_1_0\bpic_annex_o.h" />
    <ClInclude Include="UB_Live_P_Enc_1_0\deblockingfilter_annex_j.h" />
    <ClInclude Include="UB_Live_P_Enc_1_0\encodecoefficients.h" />
    <ClInclude Include="UB_Live_P_Enc_1_0\encoder.h" />
    <ClInclude Include="UB_Live_P_Enc_1_0\encodertables.h" />
    <ClInclude Include="UB_Live_P_Enc_1_0\idct.h" />
    <ClInclude Include="UB_Live_P_Enc_1_0\interpolation.h" />
    <ClInclude Include="UB_Live_P_Enc_1_0\ipic.h" />
    <ClInclude Include="UB_Live_P_Enc_1_0\iquant.h" />
    <ClInclude Include="UB_Live_P_Enc_1_0\mblockoperations.h" />
    <ClInclude Include="UB_Live_P_Enc_1_0\motionest.h" />
    <ClInclude Include="UB_Live_P_Enc_1_0\outputcoefficients.h" />
    <ClInclude Include="UB_Live_P_Enc_1_0\ppic.h" />
    <ClInclude Include="UB_Live_P_Enc_1_0\qdct.h" />
    <ClInclude Include="UB_Live_P_Enc_1_0\ratectrl.h" />
    <ClInclude Include="UB_Live_P_Enc_1_0\slicestructured_annex_k.h" />
    <ClInclude Include="UB_Live_P_Enc_1_0\snr.h" />
    <ClInclude Include="UB_Live_P_Enc_1_0\ublivepencapi.h" />
    <ClInclude Include="UB_Live_P_Enc_1_0\ubvbuffer.h" />
    <ClInclude Include="UB_Live_P_Enc_1_0\ubvcommon.h" />
    <ClInclude Include="UB_Live_P_Enc_1_0\ubvdefs.h" />
    <ClInclude Include="UB_Live_P_Enc_1_0\ubvencoder.h" />
    <ClInclude Include="UB_Live_P_Enc_1_0\ubvmacros.h" />
    <ClInclude Include="UB_Live_P_Enc_1_0\ubvrtp.h" />
    <ClInclude Include="UB_Live_P_Enc_1_0\ubvtypedefs.h" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>