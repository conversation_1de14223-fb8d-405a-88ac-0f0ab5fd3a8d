/*****************************************************************
 **                                                             **
 **   API File : Header File                                    **
 **   Copyright � 2000-2001 UB Video Inc, All rights reserved.  **
 **                                                             **
 **   Description: This file contains prototypes for the        **
 **      API library functions that encapsulate the use of      **
 **      the UBLive Encoder library.                            **
 **                                                             **
 *****************************************************************/


#ifndef UBLIVE_ENCODER_API_H
#define UBLIVE_ENCODER_API_H

/*  Max number of simultaneous Encoders  */
#define MAX_NUM_ENCODERS 10

/*  Error Message Flags  */
#define H263_ENCODE_SUCCESS             0
#define H263_ENCODE_FRAME_SKIPPED       1
#define ERR_NO_AVAILABLE_CODER          10
#define ERR_MEMORY_ALLOCATION_FAILURE   20
#define ERR_INVALID_CODER_ID            25
#define ERR_EXCEEDED_FRAME_LIMIT        30

#define ENCODING_FAILURE_TYPE           255

#ifndef INT8
	#define     INT8    char
#endif
#ifndef UINT8
	#define     UINT8   unsigned char
#endif
#ifndef UINT16
	#define     UINT16  unsigned short
#endif
#ifndef UINT32
	#define     UINT32  unsigned long
#endif
#ifndef FLOAT
	#define     FLOAT   float
#endif

typedef struct tagStatsData {
	UINT32 ulFramesEncoded;
	UINT32 ulFramesDropped;
	UINT32 ulTotalFrames;
	UINT32 ulIFrameCount;
	UINT32 ulPFrameCount;
} stEncStats;


typedef struct tagConfigData {

	UINT32  ulNumFrames;
	UINT8   ucSourceFormat;
    UINT16	uiFrameWidth;
    UINT16	uiFrameHeight;

    UINT8	ucIFrameQuant;
    UINT8	ucPFrameQuant;
    UINT8	ucRateControl;

	UINT8   bSkipFrames;
	UINT8   bUseGOBMode;

	UINT16  uiIntraPeriod;
    UINT16  uiIntraMBRefreshRate;
    UINT32	ulTargetBitrate;
    UINT8	ucFrameRate;

    UINT8	bAdvancedIntra;
    UINT8	bSliceStructured;
    UINT16	uiNumMBPerSlice;
    UINT8	bModifiedQuant;
    UINT8	bDeblockingFilter;
    UINT8	bTemporalScalability;
    UINT8	ucNumBFrames;
    UINT8	ucBFrameQuant;

	UINT8   bOutputRTP;
	UINT32  ulSSRC;
	UINT8   ucRTPType;

	UINT8   bUseMMX;
} h263stConfig;


/**
 **
 ** Exported function definitions
 **
 **/
#ifdef __cplusplus
	extern "C" {
#endif

	UINT8  h263UBVInitEncoder (h263stConfig	*pConfig, UINT32* ulBytes0,	INT8** pcOutBuffer,	UINT32*	ulEncoderID);
	UINT8  h263UBVEncodeFrame(UINT8* pcInData, UINT32 ulFrameIndex, UINT8 bForceIFrame, UINT32 ulTargetBitRate, UINT8* pucPictureType, UINT8* pbIFrame, UINT32* pulHeaderPositions, UINT16* puiHeaderCount, UINT32 ulEncoderID);
	UINT8  h263UBVEncodeRemainingFrames(UINT8* pucPictureType, UINT32 ulEncoderID);
	UINT8  h263UBVReleaseEncoder(UINT32 ulEncoderID);
	UINT8  h263UBVGetEncStatistics(UINT32 ulEncoderID, stEncStats *pstStats);
	UINT8  h263UBVReconfigureEncoder(UINT32 ulEncoderID, h263stConfig *pConfig);
	UINT8  h263UBVSuspendEncoding(UINT32 ulEncoderID);
	UINT8  h263UBVResumeEncoding(UINT32 ulEncoderID);

#ifdef __cplusplus
	}
#endif


#endif

