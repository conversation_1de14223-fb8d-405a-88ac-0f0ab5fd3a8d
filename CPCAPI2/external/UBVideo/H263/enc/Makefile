CBUILD = ../../../../source/build
include $(CBUILD)/Makefile.pre

CODE_SUBDIRS = Library/UB_Live_P_Enc_1_0

SRC = \
		Library/UB_Live_P_Enc_1_0/advancedintracoding_annex_i.c \
		Library/UB_Live_P_Enc_1_0/blockoperations.c \
		Library/UB_Live_P_Enc_1_0/bpic_annex_o.c	\
		Library/UB_Live_P_Enc_1_0/deblockingfilter_annex_j.c \
		Library/UB_Live_P_Enc_1_0/encodecoefficients.c \
		Library/UB_Live_P_Enc_1_0/encoder.c \
		Library/UB_Live_P_Enc_1_0/idct.c \
		Library/UB_Live_P_Enc_1_0/interpolation.c \
		Library/UB_Live_P_Enc_1_0/ipic.c \
		Library/UB_Live_P_Enc_1_0/iquant.c \
		Library/UB_Live_P_Enc_1_0/mblockoperations.c \
		Library/UB_Live_P_Enc_1_0/motionest.c   \
		Library/UB_Live_P_Enc_1_0/outputcoefficients.c \
		Library/UB_Live_P_Enc_1_0/ppic.c \
		Library/UB_Live_P_Enc_1_0/qdct.c \
		Library/UB_Live_P_Enc_1_0/ratectrl.c \
		Library/UB_Live_P_Enc_1_0/slicestructured_annex_k.c \
		Library/UB_Live_P_Enc_1_0/snr.c \
		Library/UB_Live_P_Enc_1_0/ublivepencapi.c \
		Library/UB_Live_P_Enc_1_0/ubvbuffer.c \
		Library/UB_Live_P_Enc_1_0/ubvrtp.c

CFLAGS += -DTARGET_OS_LINUX

TARGET_LIBRARY = libh263enc

include $(CBUILD)/Makefile.post
