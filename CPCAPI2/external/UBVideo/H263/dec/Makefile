CBUILD = ../../../../source/build
include $(CBUILD)/Makefile.pre

CODE_SUBDIRS = Library/UB_Live_P_Dec_1_0

TARGET_LIBRARY = libh263dec

SRC = \
		Library/UB_Live_P_Dec_1_0/advancedintracoding_annex_i.c \
		Library/UB_Live_P_Dec_1_0/bpicd_annex_o.c \
		Library/UB_Live_P_Dec_1_0/deblockingfilter_annex_j.c \
		Library/UB_Live_P_Dec_1_0/decoder.c \
		Library/UB_Live_P_Dec_1_0/extendedmotionvectors_annex_d.c \
		Library/UB_Live_P_Dec_1_0/frameoperations.c \
		Library/UB_Live_P_Dec_1_0/getfunctions.c \
		Library/UB_Live_P_Dec_1_0/idct.c \
		Library/UB_Live_P_Dec_1_0/ipicd.c \
		Library/UB_Live_P_Dec_1_0/parser.c \
		Library/UB_Live_P_Dec_1_0/postfilter.c \
		Library/UB_Live_P_Dec_1_0/ppicd.c \
		Library/UB_Live_P_Dec_1_0/readfunctions.c \
		Library/UB_Live_P_Dec_1_0/slicestructured_annex_k.c \
		Library/UB_Live_P_Dec_1_0/ublivepdecapi.c \
		Library/UB_Live_P_Dec_1_0/ubvbufferd.c

CFLAGS += -DTARGET_OS_LINUX

include $(CBUILD)/Makefile.post
