// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		12F87B0D08B6AE0D0073489E /* advancedintracoding_annex_i.c in Sources */ = {isa = PBXBuildFile; fileRef = 12F87ADD08B6AE0D0073489E /* advancedintracoding_annex_i.c */; };
		12F87B0F08B6AE0D0073489E /* blockoperations.c in Sources */ = {isa = PBXBuildFile; fileRef = 12F87ADF08B6AE0D0073489E /* blockoperations.c */; };
		12F87B1308B6AE0D0073489E /* deblockingfilter_annex_j.c in Sources */ = {isa = PBXBuildFile; fileRef = 12F87AE308B6AE0D0073489E /* deblockingfilter_annex_j.c */; };
		12F87B1508B6AE0D0073489E /* encodecoefficients.c in Sources */ = {isa = PBXBuildFile; fileRef = 12F87AE508B6AE0D0073489E /* encodecoefficients.c */; };
		12F87B1708B6AE0D0073489E /* encoder.c in Sources */ = {isa = PBXBuildFile; fileRef = 12F87AE708B6AE0D0073489E /* encoder.c */; };
		12F87B1A08B6AE0D0073489E /* idct.c in Sources */ = {isa = PBXBuildFile; fileRef = 12F87AEA08B6AE0D0073489E /* idct.c */; };
		12F87B1E08B6AE0D0073489E /* ipic.c in Sources */ = {isa = PBXBuildFile; fileRef = 12F87AEE08B6AE0D0073489E /* ipic.c */; };
		12F87B2008B6AE0D0073489E /* iquant.c in Sources */ = {isa = PBXBuildFile; fileRef = 12F87AF008B6AE0D0073489E /* iquant.c */; };
		12F87B2608B6AE0D0073489E /* outputcoefficients.c in Sources */ = {isa = PBXBuildFile; fileRef = 12F87AF608B6AE0D0073489E /* outputcoefficients.c */; };
		12F87B2C08B6AE0D0073489E /* ratectrl.c in Sources */ = {isa = PBXBuildFile; fileRef = 12F87AFC08B6AE0D0073489E /* ratectrl.c */; };
		12F87B2E08B6AE0D0073489E /* slicestructured_annex_k.c in Sources */ = {isa = PBXBuildFile; fileRef = 12F87AFE08B6AE0D0073489E /* slicestructured_annex_k.c */; };
		12F87B3008B6AE0D0073489E /* snr.c in Sources */ = {isa = PBXBuildFile; fileRef = 12F87B0008B6AE0D0073489E /* snr.c */; };
		12F87B3208B6AE0D0073489E /* ublivepencapi.c in Sources */ = {isa = PBXBuildFile; fileRef = 12F87B0208B6AE0D0073489E /* ublivepencapi.c */; };
		12F87B3408B6AE0D0073489E /* ubvbuffer.c in Sources */ = {isa = PBXBuildFile; fileRef = 12F87B0408B6AE0D0073489E /* ubvbuffer.c */; };
		12F87B3A08B6AE0D0073489E /* ubvrtp.c in Sources */ = {isa = PBXBuildFile; fileRef = 12F87B0A08B6AE0D0073489E /* ubvrtp.c */; };
		12F87B9508B6B1750073489E /* bpic_annex_o.c in Sources */ = {isa = PBXBuildFile; fileRef = 12F87B9408B6B1750073489E /* bpic_annex_o.c */; };
		12F87B9708B6B18B0073489E /* interpolation.c in Sources */ = {isa = PBXBuildFile; fileRef = 12F87B9608B6B18B0073489E /* interpolation.c */; };
		12F87B9A08B6B1A30073489E /* motionest.c in Sources */ = {isa = PBXBuildFile; fileRef = 12F87B9808B6B1A30073489E /* motionest.c */; };
		12F87B9B08B6B1A30073489E /* mblockoperations.c in Sources */ = {isa = PBXBuildFile; fileRef = 12F87B9908B6B1A30073489E /* mblockoperations.c */; };
		12F87B9E08B6B1B40073489E /* qdct.c in Sources */ = {isa = PBXBuildFile; fileRef = 12F87B9C08B6B1B40073489E /* qdct.c */; };
		12F87B9F08B6B1B40073489E /* ppic.c in Sources */ = {isa = PBXBuildFile; fileRef = 12F87B9D08B6B1B40073489E /* ppic.c */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		12F87ADD08B6AE0D0073489E /* advancedintracoding_annex_i.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; name = advancedintracoding_annex_i.c; path = ../../enc/Library/UB_Live_P_Enc_1_0/advancedintracoding_annex_i.c; sourceTree = SOURCE_ROOT; };
		12F87ADF08B6AE0D0073489E /* blockoperations.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; name = blockoperations.c; path = ../../enc/Library/UB_Live_P_Enc_1_0/blockoperations.c; sourceTree = SOURCE_ROOT; };
		12F87AE308B6AE0D0073489E /* deblockingfilter_annex_j.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; name = deblockingfilter_annex_j.c; path = ../../enc/Library/UB_Live_P_Enc_1_0/deblockingfilter_annex_j.c; sourceTree = SOURCE_ROOT; };
		12F87AE508B6AE0D0073489E /* encodecoefficients.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; name = encodecoefficients.c; path = ../../enc/Library/UB_Live_P_Enc_1_0/encodecoefficients.c; sourceTree = SOURCE_ROOT; };
		12F87AE708B6AE0D0073489E /* encoder.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; name = encoder.c; path = ../../enc/Library/UB_Live_P_Enc_1_0/encoder.c; sourceTree = SOURCE_ROOT; };
		12F87AEA08B6AE0D0073489E /* idct.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; name = idct.c; path = ../../enc/Library/UB_Live_P_Enc_1_0/idct.c; sourceTree = SOURCE_ROOT; };
		12F87AEE08B6AE0D0073489E /* ipic.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; name = ipic.c; path = ../../enc/Library/UB_Live_P_Enc_1_0/ipic.c; sourceTree = SOURCE_ROOT; };
		12F87AF008B6AE0D0073489E /* iquant.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; name = iquant.c; path = ../../enc/Library/UB_Live_P_Enc_1_0/iquant.c; sourceTree = SOURCE_ROOT; };
		12F87AF608B6AE0D0073489E /* outputcoefficients.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; name = outputcoefficients.c; path = ../../enc/Library/UB_Live_P_Enc_1_0/outputcoefficients.c; sourceTree = SOURCE_ROOT; };
		12F87AFC08B6AE0D0073489E /* ratectrl.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; name = ratectrl.c; path = ../../enc/Library/UB_Live_P_Enc_1_0/ratectrl.c; sourceTree = SOURCE_ROOT; };
		12F87AFE08B6AE0D0073489E /* slicestructured_annex_k.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; name = slicestructured_annex_k.c; path = ../../enc/Library/UB_Live_P_Enc_1_0/slicestructured_annex_k.c; sourceTree = SOURCE_ROOT; };
		12F87B0008B6AE0D0073489E /* snr.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; name = snr.c; path = ../../enc/Library/UB_Live_P_Enc_1_0/snr.c; sourceTree = SOURCE_ROOT; };
		12F87B0208B6AE0D0073489E /* ublivepencapi.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; name = ublivepencapi.c; path = ../../enc/Library/UB_Live_P_Enc_1_0/ublivepencapi.c; sourceTree = SOURCE_ROOT; };
		12F87B0408B6AE0D0073489E /* ubvbuffer.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; name = ubvbuffer.c; path = ../../enc/Library/UB_Live_P_Enc_1_0/ubvbuffer.c; sourceTree = SOURCE_ROOT; };
		12F87B0A08B6AE0D0073489E /* ubvrtp.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; name = ubvrtp.c; path = ../../enc/Library/UB_Live_P_Enc_1_0/ubvrtp.c; sourceTree = SOURCE_ROOT; };
		12F87B9408B6B1750073489E /* bpic_annex_o.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; name = bpic_annex_o.c; path = ../../enc/Library/UB_Live_P_Enc_1_0/bpic_annex_o.c; sourceTree = SOURCE_ROOT; };
		12F87B9608B6B18B0073489E /* interpolation.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; name = interpolation.c; path = ../../enc/Library/UB_Live_P_Enc_1_0/interpolation.c; sourceTree = SOURCE_ROOT; };
		12F87B9808B6B1A30073489E /* motionest.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; name = motionest.c; path = ../../enc/Library/UB_Live_P_Enc_1_0/motionest.c; sourceTree = SOURCE_ROOT; };
		12F87B9908B6B1A30073489E /* mblockoperations.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; name = mblockoperations.c; path = ../../enc/Library/UB_Live_P_Enc_1_0/mblockoperations.c; sourceTree = SOURCE_ROOT; };
		12F87B9C08B6B1B40073489E /* qdct.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; name = qdct.c; path = ../../enc/Library/UB_Live_P_Enc_1_0/qdct.c; sourceTree = SOURCE_ROOT; };
		12F87B9D08B6B1B40073489E /* ppic.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; name = ppic.c; path = ../../enc/Library/UB_Live_P_Enc_1_0/ppic.c; sourceTree = SOURCE_ROOT; };
		D2AAC046055464E500DB518D /* libUBH263enc.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libUBH263enc.a; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		D289987405E68DCB004EDB86 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		08FB7794FE84155DC02AAC07 /* UBH263enc */ = {
			isa = PBXGroup;
			children = (
				08FB7795FE84155DC02AAC07 /* Source */,
				1AB674ADFE9D54B511CA2CBB /* Products */,
			);
			name = UBH263enc;
			sourceTree = "<group>";
		};
		08FB7795FE84155DC02AAC07 /* Source */ = {
			isa = PBXGroup;
			children = (
				12F87ADD08B6AE0D0073489E /* advancedintracoding_annex_i.c */,
				12F87ADF08B6AE0D0073489E /* blockoperations.c */,
				12F87B9408B6B1750073489E /* bpic_annex_o.c */,
				12F87AE308B6AE0D0073489E /* deblockingfilter_annex_j.c */,
				12F87AE508B6AE0D0073489E /* encodecoefficients.c */,
				12F87AE708B6AE0D0073489E /* encoder.c */,
				12F87AEA08B6AE0D0073489E /* idct.c */,
				12F87B9608B6B18B0073489E /* interpolation.c */,
				12F87AEE08B6AE0D0073489E /* ipic.c */,
				12F87AF008B6AE0D0073489E /* iquant.c */,
				12F87B9808B6B1A30073489E /* motionest.c */,
				12F87B9908B6B1A30073489E /* mblockoperations.c */,
				12F87AF608B6AE0D0073489E /* outputcoefficients.c */,
				12F87B9C08B6B1B40073489E /* qdct.c */,
				12F87B9D08B6B1B40073489E /* ppic.c */,
				12F87AFC08B6AE0D0073489E /* ratectrl.c */,
				12F87AFE08B6AE0D0073489E /* slicestructured_annex_k.c */,
				12F87B0008B6AE0D0073489E /* snr.c */,
				12F87B0208B6AE0D0073489E /* ublivepencapi.c */,
				12F87B0408B6AE0D0073489E /* ubvbuffer.c */,
				12F87B0A08B6AE0D0073489E /* ubvrtp.c */,
			);
			name = Source;
			sourceTree = "<group>";
		};
		1AB674ADFE9D54B511CA2CBB /* Products */ = {
			isa = PBXGroup;
			children = (
				D2AAC046055464E500DB518D /* libUBH263enc.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		D2AAC043055464E500DB518D /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		D2AAC045055464E500DB518D /* UBH263enc */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 12F87AD308B6ADF90073489E /* Build configuration list for PBXNativeTarget "UBH263enc" */;
			buildPhases = (
				D2AAC043055464E500DB518D /* Headers */,
				D2AAC044055464E500DB518D /* Sources */,
				D289987405E68DCB004EDB86 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = UBH263enc;
			productName = UBH263enc;
			productReference = D2AAC046055464E500DB518D /* libUBH263enc.a */;
			productType = "com.apple.product-type.library.static";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		08FB7793FE84155DC02AAC07 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 0460;
			};
			buildConfigurationList = 12F87AD708B6ADF90073489E /* Build configuration list for PBXProject "UBH263enc" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 1;
			knownRegions = (
				English,
				en,
			);
			mainGroup = 08FB7794FE84155DC02AAC07 /* UBH263enc */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				D2AAC045055464E500DB518D /* UBH263enc */,
			);
		};
/* End PBXProject section */

/* Begin PBXSourcesBuildPhase section */
		D2AAC044055464E500DB518D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				12F87B0D08B6AE0D0073489E /* advancedintracoding_annex_i.c in Sources */,
				12F87B0F08B6AE0D0073489E /* blockoperations.c in Sources */,
				12F87B1308B6AE0D0073489E /* deblockingfilter_annex_j.c in Sources */,
				12F87B1508B6AE0D0073489E /* encodecoefficients.c in Sources */,
				12F87B1708B6AE0D0073489E /* encoder.c in Sources */,
				12F87B1A08B6AE0D0073489E /* idct.c in Sources */,
				12F87B1E08B6AE0D0073489E /* ipic.c in Sources */,
				12F87B2008B6AE0D0073489E /* iquant.c in Sources */,
				12F87B2608B6AE0D0073489E /* outputcoefficients.c in Sources */,
				12F87B2C08B6AE0D0073489E /* ratectrl.c in Sources */,
				12F87B2E08B6AE0D0073489E /* slicestructured_annex_k.c in Sources */,
				12F87B3008B6AE0D0073489E /* snr.c in Sources */,
				12F87B3208B6AE0D0073489E /* ublivepencapi.c in Sources */,
				12F87B3408B6AE0D0073489E /* ubvbuffer.c in Sources */,
				12F87B3A08B6AE0D0073489E /* ubvrtp.c in Sources */,
				12F87B9508B6B1750073489E /* bpic_annex_o.c in Sources */,
				12F87B9708B6B18B0073489E /* interpolation.c in Sources */,
				12F87B9A08B6B1A30073489E /* motionest.c in Sources */,
				12F87B9B08B6B1A30073489E /* mblockoperations.c in Sources */,
				12F87B9E08B6B1B40073489E /* qdct.c in Sources */,
				12F87B9F08B6B1B40073489E /* ppic.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		12F87AD408B6ADF90073489E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = NO;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_MODEL_TUNING = G5;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = TARGET_OS_MAC;
				GCC_VERSION = com.apple.compilers.llvm.clang.1_0;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INSTALL_PATH = /usr/local/lib;
				LIBRARY_STYLE = STATIC;
				MACOSX_DEPLOYMENT_TARGET = 10.8;
				PRODUCT_NAME = UBH263enc;
				SDKROOT = macosx;
				ZERO_LINK = YES;
			};
			name = Debug;
		};
		12F87AD508B6ADF90073489E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = YES;
				DEAD_CODE_STRIPPING = YES;
				GCC_GENERATE_DEBUGGING_SYMBOLS = NO;
				GCC_MODEL_TUNING = G5;
				GCC_PREPROCESSOR_DEFINITIONS = TARGET_OS_MAC;
				GCC_VERSION = com.apple.compilers.llvm.clang.1_0;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INSTALL_PATH = /usr/local/lib;
				LIBRARY_STYLE = STATIC;
				MACOSX_DEPLOYMENT_TARGET = 10.8;
				PRODUCT_NAME = UBH263enc;
				SDKROOT = macosx;
				STRIP_INSTALLED_PRODUCT = YES;
				STRIP_STYLE = all;
				ZERO_LINK = NO;
			};
			name = Release;
		};
		12F87AD808B6ADF90073489E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD)";
				DEBUG_INFORMATION_FORMAT = dwarf;
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
			};
			name = Debug;
		};
		12F87AD908B6ADF90073489E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD)";
				DEBUG_INFORMATION_FORMAT = dwarf;
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		12F87AD308B6ADF90073489E /* Build configuration list for PBXNativeTarget "UBH263enc" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				12F87AD408B6ADF90073489E /* Debug */,
				12F87AD508B6ADF90073489E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		12F87AD708B6ADF90073489E /* Build configuration list for PBXProject "UBH263enc" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				12F87AD808B6ADF90073489E /* Debug */,
				12F87AD908B6ADF90073489E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
/* End XCConfigurationList section */
	};
	rootObject = 08FB7793FE84155DC02AAC07 /* Project object */;
}
