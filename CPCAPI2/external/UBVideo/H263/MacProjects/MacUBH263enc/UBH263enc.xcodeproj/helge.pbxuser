// !$*UTF8*$!
{
	08FB7793FE84155DC02AAC07 /* Project object */ = {
		activeBuildConfigurationName = Debug;
		activeTarget = D2AAC045055464E500DB518D /* UBH263enc */;
		codeSenseManager = E8F6BD771125F60D00327C94 /* Code sense */;
		perUserDictionary = {
			PBXPerProjectTemplateStateSaveDate = 287704019;
			PBXWorkspaceStateSaveDate = 287704019;
		};
		perUserProjectItems = {
			E8F6BE55112604D500327C94 /* PBXTextBookmark */ = E8F6BE55112604D500327C94 /* PBXTextBookmark */;
			E8F6BE57112604D500327C94 /* PBXTextBookmark */ = E8F6BE57112604D500327C94 /* PBXTextBookmark */;
		};
		sourceControlManager = E8F6BD761125F60D00327C94 /* Source Control */;
		userBuildSettings = {
		};
	};
	12F87ADD08B6AE0D0073489E /* advancedintracoding_annex_i.c */ = {
		uiCtxt = {
			sepNavIntBoundsRect = "{{0, 0}, {978, 6594}}";
			sepNavSelRange = "{2464, 0}";
			sepNavVisRange = "{546, 2237}";
			sepNavWindowFrame = "{{308, 117}, {1037, 1059}}";
		};
	};
	12F87AEE08B6AE0D0073489E /* ipic.c */ = {
		uiCtxt = {
			sepNavIntBoundsRect = "{{0, 0}, {778, 2730}}";
			sepNavSelRange = "{2245, 0}";
			sepNavVisRange = "{1655, 986}";
		};
	};
	12F87B9408B6B1750073489E /* bpic_annex_o.c */ = {
		uiCtxt = {
			sepNavIntBoundsRect = "{{0, 0}, {978, 16296}}";
			sepNavSelRange = "{2831, 0}";
			sepNavVisRange = "{1256, 2536}";
			sepNavWindowFrame = "{{61, 72}, {1037, 1059}}";
		};
	};
	12F87B9C08B6B1B40073489E /* qdct.c */ = {
		uiCtxt = {
			sepNavIntBoundsRect = "{{0, 0}, {778, 7784}}";
			sepNavSelRange = "{4410, 0}";
			sepNavVisRange = "{4098, 573}";
			sepNavWindowFrame = "{{38, 93}, {1037, 1059}}";
		};
	};
	12F87B9D08B6B1B40073489E /* ppic.c */ = {
		uiCtxt = {
			sepNavIntBoundsRect = "{{0, 0}, {978, 7770}}";
			sepNavSelRange = "{2807, 0}";
			sepNavVisRange = "{930, 2707}";
			sepNavWindowFrame = "{{456, 119}, {1037, 1059}}";
		};
	};
	D2AAC045055464E500DB518D /* UBH263enc */ = {
		activeExec = 0;
	};
	E8F6BD761125F60D00327C94 /* Source Control */ = {
		isa = PBXSourceControlManager;
		fallbackIsa = XCSourceControlManager;
		isSCMEnabled = 0;
		scmConfiguration = {
			repositoryNamesForRoots = {
				"" = "";
			};
		};
	};
	E8F6BD771125F60D00327C94 /* Code sense */ = {
		isa = PBXCodeSenseManager;
		indexTemplatePath = "";
	};
	E8F6BE55112604D500327C94 /* PBXTextBookmark */ = {
		isa = PBXTextBookmark;
		fRef = E8F6BE56112604D500327C94 /* advancedintracoding_annex_i.c */;
		rLen = 0;
		rLoc = 9223372036854775807;
		rType = 0;
	};
	E8F6BE56112604D500327C94 /* advancedintracoding_annex_i.c */ = {
		isa = PBXFileReference;
		lastKnownFileType = sourcecode.c.c;
		name = advancedintracoding_annex_i.c;
		path = "/Volumes/Coding/branches/b-Bria30-20091020/dev/ua/sdk/external/UBVideo/H263/enc/Library/UB_Live_P_Enc_1_0/advancedintracoding_annex_i.c";
		sourceTree = "<absolute>";
		uiCtxt = {
			sepNavWindowFrame = "{{618, 114}, {1037, 1059}}";
		};
	};
	E8F6BE57112604D500327C94 /* PBXTextBookmark */ = {
		isa = PBXTextBookmark;
		fRef = E8F6BE58112604D500327C94 /* advancedintracoding_annex_i.c */;
		name = "advancedintracoding_annex_i.c: 73";
		rLen = 92;
		rLoc = 2376;
		rType = 0;
		vrLen = 3092;
		vrLoc = 1574;
	};
	E8F6BE58112604D500327C94 /* advancedintracoding_annex_i.c */ = {
		isa = PBXFileReference;
		name = advancedintracoding_annex_i.c;
		path = "/Volumes/Coding/branches/b-Bria30-20091020/dev/ua/sdk/external/UBVideo/H263/enc/Library/UB_Live_P_Enc_1_0/advancedintracoding_annex_i.c";
		sourceTree = "<absolute>";
		uiCtxt = {
			sepNavIntBoundsRect = "{{0, 0}, {978, 6440}}";
			sepNavSelRange = "{2376, 92}";
			sepNavVisRange = "{1574, 3092}";
		};
	};
}
