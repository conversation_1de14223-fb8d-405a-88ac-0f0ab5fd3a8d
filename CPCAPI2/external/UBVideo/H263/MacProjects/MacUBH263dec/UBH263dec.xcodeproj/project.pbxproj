// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		12F87A9E08B6AD950073489E /* advancedintracoding_annex_i.c in Sources */ = {isa = PBXBuildFile; fileRef = 12F87A7808B6AD950073489E /* advancedintracoding_annex_i.c */; };
		12F87A9F08B6AD950073489E /* advancedintracoding_annex_i.h in Headers */ = {isa = PBXBuildFile; fileRef = 12F87A7908B6AD950073489E /* advancedintracoding_annex_i.h */; };
		12F87AA008B6AD950073489E /* bpicd_annex_o.c in Sources */ = {isa = PBXBuildFile; fileRef = 12F87A7A08B6AD950073489E /* bpicd_annex_o.c */; };
		12F87AA108B6AD950073489E /* bpicd_annex_o.h in Headers */ = {isa = PBXBuildFile; fileRef = 12F87A7B08B6AD950073489E /* bpicd_annex_o.h */; };
		12F87AA208B6AD950073489E /* deblockingfilter_annex_j.c in Sources */ = {isa = PBXBuildFile; fileRef = 12F87A7C08B6AD950073489E /* deblockingfilter_annex_j.c */; };
		12F87AA308B6AD950073489E /* deblockingfilter_annex_j.h in Headers */ = {isa = PBXBuildFile; fileRef = 12F87A7D08B6AD950073489E /* deblockingfilter_annex_j.h */; };
		12F87AA408B6AD950073489E /* decoder.c in Sources */ = {isa = PBXBuildFile; fileRef = 12F87A7E08B6AD950073489E /* decoder.c */; };
		12F87AA508B6AD950073489E /* decoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 12F87A7F08B6AD950073489E /* decoder.h */; };
		12F87AA608B6AD950073489E /* decodertables.h in Headers */ = {isa = PBXBuildFile; fileRef = 12F87A8008B6AD950073489E /* decodertables.h */; };
		12F87AA708B6AD950073489E /* extendedmotionvectors_annex_d.c in Sources */ = {isa = PBXBuildFile; fileRef = 12F87A8108B6AD950073489E /* extendedmotionvectors_annex_d.c */; };
		12F87AA808B6AD950073489E /* extendedmotionvectors_annex_d.h in Headers */ = {isa = PBXBuildFile; fileRef = 12F87A8208B6AD950073489E /* extendedmotionvectors_annex_d.h */; };
		12F87AAB08B6AD950073489E /* getfunctions.c in Sources */ = {isa = PBXBuildFile; fileRef = 12F87A8508B6AD950073489E /* getfunctions.c */; };
		12F87AAC08B6AD950073489E /* getfunctions.h in Headers */ = {isa = PBXBuildFile; fileRef = 12F87A8608B6AD950073489E /* getfunctions.h */; };
		12F87AAD08B6AD950073489E /* idct.c in Sources */ = {isa = PBXBuildFile; fileRef = 12F87A8708B6AD950073489E /* idct.c */; };
		12F87AAE08B6AD950073489E /* idct.h in Headers */ = {isa = PBXBuildFile; fileRef = 12F87A8808B6AD950073489E /* idct.h */; };
		12F87AAF08B6AD950073489E /* ipicd.c in Sources */ = {isa = PBXBuildFile; fileRef = 12F87A8908B6AD950073489E /* ipicd.c */; };
		12F87AB008B6AD950073489E /* ipicd.h in Headers */ = {isa = PBXBuildFile; fileRef = 12F87A8A08B6AD950073489E /* ipicd.h */; };
		12F87AB108B6AD950073489E /* parser.c in Sources */ = {isa = PBXBuildFile; fileRef = 12F87A8B08B6AD950073489E /* parser.c */; };
		12F87AB208B6AD950073489E /* parser.h in Headers */ = {isa = PBXBuildFile; fileRef = 12F87A8C08B6AD950073489E /* parser.h */; };
		12F87AB508B6AD950073489E /* ppicd.c in Sources */ = {isa = PBXBuildFile; fileRef = 12F87A8F08B6AD950073489E /* ppicd.c */; };
		12F87AB608B6AD950073489E /* ppicd.h in Headers */ = {isa = PBXBuildFile; fileRef = 12F87A9008B6AD950073489E /* ppicd.h */; };
		12F87AB708B6AD950073489E /* readfunctions.c in Sources */ = {isa = PBXBuildFile; fileRef = 12F87A9108B6AD950073489E /* readfunctions.c */; };
		12F87AB808B6AD950073489E /* readfunctions.h in Headers */ = {isa = PBXBuildFile; fileRef = 12F87A9208B6AD950073489E /* readfunctions.h */; };
		12F87AB908B6AD950073489E /* slicestructured_annex_k.c in Sources */ = {isa = PBXBuildFile; fileRef = 12F87A9308B6AD950073489E /* slicestructured_annex_k.c */; };
		12F87ABA08B6AD950073489E /* slicestructured_annex_k.h in Headers */ = {isa = PBXBuildFile; fileRef = 12F87A9408B6AD950073489E /* slicestructured_annex_k.h */; };
		12F87ABB08B6AD950073489E /* ublivepdecapi.c in Sources */ = {isa = PBXBuildFile; fileRef = 12F87A9508B6AD950073489E /* ublivepdecapi.c */; };
		12F87ABC08B6AD950073489E /* ublivepdecapi.h in Headers */ = {isa = PBXBuildFile; fileRef = 12F87A9608B6AD950073489E /* ublivepdecapi.h */; };
		12F87ABD08B6AD950073489E /* ubvbufferd.c in Sources */ = {isa = PBXBuildFile; fileRef = 12F87A9708B6AD950073489E /* ubvbufferd.c */; };
		12F87ABE08B6AD950073489E /* ubvbufferd.h in Headers */ = {isa = PBXBuildFile; fileRef = 12F87A9808B6AD950073489E /* ubvbufferd.h */; };
		12F87ABF08B6AD950073489E /* ubvcommon.h in Headers */ = {isa = PBXBuildFile; fileRef = 12F87A9908B6AD950073489E /* ubvcommon.h */; };
		12F87AC008B6AD950073489E /* ubvdecoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 12F87A9A08B6AD950073489E /* ubvdecoder.h */; };
		12F87AC108B6AD950073489E /* ubvdefsd.h in Headers */ = {isa = PBXBuildFile; fileRef = 12F87A9B08B6AD950073489E /* ubvdefsd.h */; };
		12F87AC208B6AD950073489E /* ubvmacros.h in Headers */ = {isa = PBXBuildFile; fileRef = 12F87A9C08B6AD950073489E /* ubvmacros.h */; };
		12F87AC308B6AD950073489E /* ubvtypedefsd.h in Headers */ = {isa = PBXBuildFile; fileRef = 12F87A9D08B6AD950073489E /* ubvtypedefsd.h */; };
		12F87B7D08B6B09F0073489E /* frameoperations.c in Sources */ = {isa = PBXBuildFile; fileRef = 12F87B7B08B6B09F0073489E /* frameoperations.c */; };
		12F87B7E08B6B09F0073489E /* frameoperations.h in Headers */ = {isa = PBXBuildFile; fileRef = 12F87B7C08B6B09F0073489E /* frameoperations.h */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		12F87A7808B6AD950073489E /* advancedintracoding_annex_i.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; name = advancedintracoding_annex_i.c; path = ../../dec/Library/UB_Live_P_Dec_1_0/advancedintracoding_annex_i.c; sourceTree = SOURCE_ROOT; };
		12F87A7908B6AD950073489E /* advancedintracoding_annex_i.h */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.h; name = advancedintracoding_annex_i.h; path = ../../dec/Library/UB_Live_P_Dec_1_0/advancedintracoding_annex_i.h; sourceTree = SOURCE_ROOT; };
		12F87A7A08B6AD950073489E /* bpicd_annex_o.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; name = bpicd_annex_o.c; path = ../../dec/Library/UB_Live_P_Dec_1_0/bpicd_annex_o.c; sourceTree = SOURCE_ROOT; };
		12F87A7B08B6AD950073489E /* bpicd_annex_o.h */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.h; name = bpicd_annex_o.h; path = ../../dec/Library/UB_Live_P_Dec_1_0/bpicd_annex_o.h; sourceTree = SOURCE_ROOT; };
		12F87A7C08B6AD950073489E /* deblockingfilter_annex_j.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; name = deblockingfilter_annex_j.c; path = ../../dec/Library/UB_Live_P_Dec_1_0/deblockingfilter_annex_j.c; sourceTree = SOURCE_ROOT; };
		12F87A7D08B6AD950073489E /* deblockingfilter_annex_j.h */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.h; name = deblockingfilter_annex_j.h; path = ../../dec/Library/UB_Live_P_Dec_1_0/deblockingfilter_annex_j.h; sourceTree = SOURCE_ROOT; };
		12F87A7E08B6AD950073489E /* decoder.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; name = decoder.c; path = ../../dec/Library/UB_Live_P_Dec_1_0/decoder.c; sourceTree = SOURCE_ROOT; };
		12F87A7F08B6AD950073489E /* decoder.h */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.h; name = decoder.h; path = ../../dec/Library/UB_Live_P_Dec_1_0/decoder.h; sourceTree = SOURCE_ROOT; };
		12F87A8008B6AD950073489E /* decodertables.h */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.h; name = decodertables.h; path = ../../dec/Library/UB_Live_P_Dec_1_0/decodertables.h; sourceTree = SOURCE_ROOT; };
		12F87A8108B6AD950073489E /* extendedmotionvectors_annex_d.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; name = extendedmotionvectors_annex_d.c; path = ../../dec/Library/UB_Live_P_Dec_1_0/extendedmotionvectors_annex_d.c; sourceTree = SOURCE_ROOT; };
		12F87A8208B6AD950073489E /* extendedmotionvectors_annex_d.h */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.h; name = extendedmotionvectors_annex_d.h; path = ../../dec/Library/UB_Live_P_Dec_1_0/extendedmotionvectors_annex_d.h; sourceTree = SOURCE_ROOT; };
		12F87A8508B6AD950073489E /* getfunctions.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; name = getfunctions.c; path = ../../dec/Library/UB_Live_P_Dec_1_0/getfunctions.c; sourceTree = SOURCE_ROOT; };
		12F87A8608B6AD950073489E /* getfunctions.h */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.h; name = getfunctions.h; path = ../../dec/Library/UB_Live_P_Dec_1_0/getfunctions.h; sourceTree = SOURCE_ROOT; };
		12F87A8708B6AD950073489E /* idct.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; name = idct.c; path = ../../dec/Library/UB_Live_P_Dec_1_0/idct.c; sourceTree = SOURCE_ROOT; };
		12F87A8808B6AD950073489E /* idct.h */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.h; name = idct.h; path = ../../dec/Library/UB_Live_P_Dec_1_0/idct.h; sourceTree = SOURCE_ROOT; };
		12F87A8908B6AD950073489E /* ipicd.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; name = ipicd.c; path = ../../dec/Library/UB_Live_P_Dec_1_0/ipicd.c; sourceTree = SOURCE_ROOT; };
		12F87A8A08B6AD950073489E /* ipicd.h */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.h; name = ipicd.h; path = ../../dec/Library/UB_Live_P_Dec_1_0/ipicd.h; sourceTree = SOURCE_ROOT; };
		12F87A8B08B6AD950073489E /* parser.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; name = parser.c; path = ../../dec/Library/UB_Live_P_Dec_1_0/parser.c; sourceTree = SOURCE_ROOT; };
		12F87A8C08B6AD950073489E /* parser.h */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.h; name = parser.h; path = ../../dec/Library/UB_Live_P_Dec_1_0/parser.h; sourceTree = SOURCE_ROOT; };
		12F87A8F08B6AD950073489E /* ppicd.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; name = ppicd.c; path = ../../dec/Library/UB_Live_P_Dec_1_0/ppicd.c; sourceTree = SOURCE_ROOT; };
		12F87A9008B6AD950073489E /* ppicd.h */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.h; name = ppicd.h; path = ../../dec/Library/UB_Live_P_Dec_1_0/ppicd.h; sourceTree = SOURCE_ROOT; };
		12F87A9108B6AD950073489E /* readfunctions.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; name = readfunctions.c; path = ../../dec/Library/UB_Live_P_Dec_1_0/readfunctions.c; sourceTree = SOURCE_ROOT; };
		12F87A9208B6AD950073489E /* readfunctions.h */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.h; name = readfunctions.h; path = ../../dec/Library/UB_Live_P_Dec_1_0/readfunctions.h; sourceTree = SOURCE_ROOT; };
		12F87A9308B6AD950073489E /* slicestructured_annex_k.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; name = slicestructured_annex_k.c; path = ../../dec/Library/UB_Live_P_Dec_1_0/slicestructured_annex_k.c; sourceTree = SOURCE_ROOT; };
		12F87A9408B6AD950073489E /* slicestructured_annex_k.h */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.h; name = slicestructured_annex_k.h; path = ../../dec/Library/UB_Live_P_Dec_1_0/slicestructured_annex_k.h; sourceTree = SOURCE_ROOT; };
		12F87A9508B6AD950073489E /* ublivepdecapi.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; name = ublivepdecapi.c; path = ../../dec/Library/UB_Live_P_Dec_1_0/ublivepdecapi.c; sourceTree = SOURCE_ROOT; };
		12F87A9608B6AD950073489E /* ublivepdecapi.h */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.h; name = ublivepdecapi.h; path = ../../dec/Library/UB_Live_P_Dec_1_0/ublivepdecapi.h; sourceTree = SOURCE_ROOT; };
		12F87A9708B6AD950073489E /* ubvbufferd.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; name = ubvbufferd.c; path = ../../dec/Library/UB_Live_P_Dec_1_0/ubvbufferd.c; sourceTree = SOURCE_ROOT; };
		12F87A9808B6AD950073489E /* ubvbufferd.h */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.h; name = ubvbufferd.h; path = ../../dec/Library/UB_Live_P_Dec_1_0/ubvbufferd.h; sourceTree = SOURCE_ROOT; };
		12F87A9908B6AD950073489E /* ubvcommon.h */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.h; name = ubvcommon.h; path = ../../dec/Library/UB_Live_P_Dec_1_0/ubvcommon.h; sourceTree = SOURCE_ROOT; };
		12F87A9A08B6AD950073489E /* ubvdecoder.h */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.h; name = ubvdecoder.h; path = ../../dec/Library/UB_Live_P_Dec_1_0/ubvdecoder.h; sourceTree = SOURCE_ROOT; };
		12F87A9B08B6AD950073489E /* ubvdefsd.h */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.h; name = ubvdefsd.h; path = ../../dec/Library/UB_Live_P_Dec_1_0/ubvdefsd.h; sourceTree = SOURCE_ROOT; };
		12F87A9C08B6AD950073489E /* ubvmacros.h */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.h; name = ubvmacros.h; path = ../../dec/Library/UB_Live_P_Dec_1_0/ubvmacros.h; sourceTree = SOURCE_ROOT; };
		12F87A9D08B6AD950073489E /* ubvtypedefsd.h */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.h; name = ubvtypedefsd.h; path = ../../dec/Library/UB_Live_P_Dec_1_0/ubvtypedefsd.h; sourceTree = SOURCE_ROOT; };
		12F87B7B08B6B09F0073489E /* frameoperations.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; name = frameoperations.c; path = ../../dec/Library/UB_Live_P_Dec_1_0/frameoperations.c; sourceTree = SOURCE_ROOT; };
		12F87B7C08B6B09F0073489E /* frameoperations.h */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.h; name = frameoperations.h; path = ../../dec/Library/UB_Live_P_Dec_1_0/frameoperations.h; sourceTree = SOURCE_ROOT; };
		D2AAC046055464E500DB518D /* libUBH263dec.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libUBH263dec.a; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		D289987405E68DCB004EDB86 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		08FB7794FE84155DC02AAC07 /* UBH263dec */ = {
			isa = PBXGroup;
			children = (
				08FB7795FE84155DC02AAC07 /* Source */,
				1AB674ADFE9D54B511CA2CBB /* Products */,
			);
			name = UBH263dec;
			sourceTree = "<group>";
		};
		08FB7795FE84155DC02AAC07 /* Source */ = {
			isa = PBXGroup;
			children = (
				12F87A7808B6AD950073489E /* advancedintracoding_annex_i.c */,
				12F87A7908B6AD950073489E /* advancedintracoding_annex_i.h */,
				12F87A7A08B6AD950073489E /* bpicd_annex_o.c */,
				12F87A7B08B6AD950073489E /* bpicd_annex_o.h */,
				12F87A7C08B6AD950073489E /* deblockingfilter_annex_j.c */,
				12F87A7D08B6AD950073489E /* deblockingfilter_annex_j.h */,
				12F87A7E08B6AD950073489E /* decoder.c */,
				12F87A7F08B6AD950073489E /* decoder.h */,
				12F87A8008B6AD950073489E /* decodertables.h */,
				12F87A8108B6AD950073489E /* extendedmotionvectors_annex_d.c */,
				12F87A8208B6AD950073489E /* extendedmotionvectors_annex_d.h */,
				12F87B7B08B6B09F0073489E /* frameoperations.c */,
				12F87B7C08B6B09F0073489E /* frameoperations.h */,
				12F87A8508B6AD950073489E /* getfunctions.c */,
				12F87A8608B6AD950073489E /* getfunctions.h */,
				12F87A8708B6AD950073489E /* idct.c */,
				12F87A8808B6AD950073489E /* idct.h */,
				12F87A8908B6AD950073489E /* ipicd.c */,
				12F87A8A08B6AD950073489E /* ipicd.h */,
				12F87A8B08B6AD950073489E /* parser.c */,
				12F87A8C08B6AD950073489E /* parser.h */,
				12F87A8F08B6AD950073489E /* ppicd.c */,
				12F87A9008B6AD950073489E /* ppicd.h */,
				12F87A9108B6AD950073489E /* readfunctions.c */,
				12F87A9208B6AD950073489E /* readfunctions.h */,
				12F87A9308B6AD950073489E /* slicestructured_annex_k.c */,
				12F87A9408B6AD950073489E /* slicestructured_annex_k.h */,
				12F87A9508B6AD950073489E /* ublivepdecapi.c */,
				12F87A9608B6AD950073489E /* ublivepdecapi.h */,
				12F87A9708B6AD950073489E /* ubvbufferd.c */,
				12F87A9808B6AD950073489E /* ubvbufferd.h */,
				12F87A9908B6AD950073489E /* ubvcommon.h */,
				12F87A9A08B6AD950073489E /* ubvdecoder.h */,
				12F87A9B08B6AD950073489E /* ubvdefsd.h */,
				12F87A9C08B6AD950073489E /* ubvmacros.h */,
				12F87A9D08B6AD950073489E /* ubvtypedefsd.h */,
			);
			name = Source;
			sourceTree = "<group>";
		};
		1AB674ADFE9D54B511CA2CBB /* Products */ = {
			isa = PBXGroup;
			children = (
				D2AAC046055464E500DB518D /* libUBH263dec.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		D2AAC043055464E500DB518D /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				12F87A9F08B6AD950073489E /* advancedintracoding_annex_i.h in Headers */,
				12F87AA108B6AD950073489E /* bpicd_annex_o.h in Headers */,
				12F87AA308B6AD950073489E /* deblockingfilter_annex_j.h in Headers */,
				12F87AA508B6AD950073489E /* decoder.h in Headers */,
				12F87AA608B6AD950073489E /* decodertables.h in Headers */,
				12F87AA808B6AD950073489E /* extendedmotionvectors_annex_d.h in Headers */,
				12F87AAC08B6AD950073489E /* getfunctions.h in Headers */,
				12F87AAE08B6AD950073489E /* idct.h in Headers */,
				12F87AB008B6AD950073489E /* ipicd.h in Headers */,
				12F87AB208B6AD950073489E /* parser.h in Headers */,
				12F87AB608B6AD950073489E /* ppicd.h in Headers */,
				12F87AB808B6AD950073489E /* readfunctions.h in Headers */,
				12F87ABA08B6AD950073489E /* slicestructured_annex_k.h in Headers */,
				12F87ABC08B6AD950073489E /* ublivepdecapi.h in Headers */,
				12F87ABE08B6AD950073489E /* ubvbufferd.h in Headers */,
				12F87ABF08B6AD950073489E /* ubvcommon.h in Headers */,
				12F87AC008B6AD950073489E /* ubvdecoder.h in Headers */,
				12F87AC108B6AD950073489E /* ubvdefsd.h in Headers */,
				12F87AC208B6AD950073489E /* ubvmacros.h in Headers */,
				12F87AC308B6AD950073489E /* ubvtypedefsd.h in Headers */,
				12F87B7E08B6B09F0073489E /* frameoperations.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		D2AAC045055464E500DB518D /* UBH263dec */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 12F87A6308B6AD530073489E /* Build configuration list for PBXNativeTarget "UBH263dec" */;
			buildPhases = (
				D2AAC043055464E500DB518D /* Headers */,
				D2AAC044055464E500DB518D /* Sources */,
				D289987405E68DCB004EDB86 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = UBH263dec;
			productName = UBH263dec;
			productReference = D2AAC046055464E500DB518D /* libUBH263dec.a */;
			productType = "com.apple.product-type.library.static";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		08FB7793FE84155DC02AAC07 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 0460;
			};
			buildConfigurationList = 12F87A6708B6AD530073489E /* Build configuration list for PBXProject "UBH263dec" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 1;
			knownRegions = (
				English,
				en,
			);
			mainGroup = 08FB7794FE84155DC02AAC07 /* UBH263dec */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				D2AAC045055464E500DB518D /* UBH263dec */,
			);
		};
/* End PBXProject section */

/* Begin PBXSourcesBuildPhase section */
		D2AAC044055464E500DB518D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				12F87A9E08B6AD950073489E /* advancedintracoding_annex_i.c in Sources */,
				12F87AA008B6AD950073489E /* bpicd_annex_o.c in Sources */,
				12F87AA208B6AD950073489E /* deblockingfilter_annex_j.c in Sources */,
				12F87AA408B6AD950073489E /* decoder.c in Sources */,
				12F87AA708B6AD950073489E /* extendedmotionvectors_annex_d.c in Sources */,
				12F87AAB08B6AD950073489E /* getfunctions.c in Sources */,
				12F87AAD08B6AD950073489E /* idct.c in Sources */,
				12F87AAF08B6AD950073489E /* ipicd.c in Sources */,
				12F87AB108B6AD950073489E /* parser.c in Sources */,
				12F87AB508B6AD950073489E /* ppicd.c in Sources */,
				12F87AB708B6AD950073489E /* readfunctions.c in Sources */,
				12F87AB908B6AD950073489E /* slicestructured_annex_k.c in Sources */,
				12F87ABB08B6AD950073489E /* ublivepdecapi.c in Sources */,
				12F87ABD08B6AD950073489E /* ubvbufferd.c in Sources */,
				12F87B7D08B6B09F0073489E /* frameoperations.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		12F87A6408B6AD530073489E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD)";
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = NO;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_MODEL_TUNING = G5;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = TARGET_OS_MAC;
				GCC_VERSION = com.apple.compilers.llvm.clang.1_0;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INSTALL_PATH = /usr/local/lib;
				LIBRARY_STYLE = STATIC;
				MACOSX_DEPLOYMENT_TARGET = 10.8;
				PRODUCT_NAME = UBH263dec;
				SDKROOT = macosx;
				ZERO_LINK = YES;
			};
			name = Debug;
		};
		12F87A6508B6AD530073489E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD)";
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = YES;
				DEAD_CODE_STRIPPING = YES;
				GCC_GENERATE_DEBUGGING_SYMBOLS = NO;
				GCC_MODEL_TUNING = G5;
				GCC_PREPROCESSOR_DEFINITIONS = TARGET_OS_MAC;
				GCC_VERSION = com.apple.compilers.llvm.clang.1_0;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INSTALL_PATH = /usr/local/lib;
				LIBRARY_STYLE = STATIC;
				MACOSX_DEPLOYMENT_TARGET = 10.8;
				PRODUCT_NAME = UBH263dec;
				SDKROOT = macosx;
				STRIP_INSTALLED_PRODUCT = YES;
				STRIP_STYLE = all;
				ZERO_LINK = NO;
			};
			name = Release;
		};
		12F87A6808B6AD530073489E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD)";
				DEBUG_INFORMATION_FORMAT = dwarf;
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
			};
			name = Debug;
		};
		12F87A6908B6AD530073489E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD)";
				DEBUG_INFORMATION_FORMAT = dwarf;
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				SDKROOT = macosx;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		12F87A6308B6AD530073489E /* Build configuration list for PBXNativeTarget "UBH263dec" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				12F87A6408B6AD530073489E /* Debug */,
				12F87A6508B6AD530073489E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		12F87A6708B6AD530073489E /* Build configuration list for PBXProject "UBH263dec" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				12F87A6808B6AD530073489E /* Debug */,
				12F87A6908B6AD530073489E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
/* End XCConfigurationList section */
	};
	rootObject = 08FB7793FE84155DC02AAC07 /* Project object */;
}
