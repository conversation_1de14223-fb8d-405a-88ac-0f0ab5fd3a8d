// !$*UTF8*$!
{
	08FB7793FE84155DC02AAC07 /* Project object */ = {
		activeBuildConfigurationName = Debug;
		activeTarget = D2AAC045055464E500DB518D /* UBH263dec */;
		codeSenseManager = E8F6BD831125F63600327C94 /* Code sense */;
		perUserDictionary = {
			PBXPerProjectTemplateStateSaveDate = 287704286;
			PBXWorkspaceStateSaveDate = 287704286;
		};
		sourceControlManager = E8F6BD821125F63600327C94 /* Source Control */;
		userBuildSettings = {
		};
	};
	12F87A7C08B6AD950073489E /* deblockingfilter_annex_j.c */ = {
		uiCtxt = {
			sepNavIntBoundsRect = "{{0, 0}, {978, 4368}}";
			sepNavSelRange = "{2752, 56}";
			sepNavVisRange = "{1229, 2828}";
			sepNavWindowFrame = "{{573, 44}, {1037, 1059}}";
		};
	};
	12F87A8008B6AD950073489E /* decodertables.h */ = {
		uiCtxt = {
			sepNavIntBoundsRect = "{{0, 0}, {978, 3553}}";
			sepNavSelRange = "{5202, 20}";
			sepNavVisRange = "{2571, 6338}";
			sepNavWindowFrame = "{{15, 114}, {1037, 1059}}";
		};
	};
	12F87A8508B6AD950073489E /* getfunctions.c */ = {
		uiCtxt = {
			sepNavIntBoundsRect = "{{0, 0}, {978, 12880}}";
			sepNavSelRange = "{19346, 0}";
			sepNavVisRange = "{17871, 1923}";
			sepNavWindowFrame = "{{38, 93}, {1037, 1059}}";
		};
	};
	D2AAC045055464E500DB518D /* UBH263dec */ = {
		activeExec = 0;
	};
	E8F6BD821125F63600327C94 /* Source Control */ = {
		isa = PBXSourceControlManager;
		fallbackIsa = XCSourceControlManager;
		isSCMEnabled = 0;
		scmConfiguration = {
			repositoryNamesForRoots = {
				"" = "";
			};
		};
	};
	E8F6BD831125F63600327C94 /* Code sense */ = {
		isa = PBXCodeSenseManager;
		indexTemplatePath = "";
	};
}
