<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>ActivePerspectiveName</key>
	<string>Project</string>
	<key>AllowedModules</key>
	<array>
		<dict>
			<key>BundleLoadPath</key>
			<string></string>
			<key>MaxInstances</key>
			<string>n</string>
			<key>Module</key>
			<string>PBXSmartGroupTreeModule</string>
			<key>Name</key>
			<string>Groups and Files Outline View</string>
		</dict>
		<dict>
			<key>BundleLoadPath</key>
			<string></string>
			<key>MaxInstances</key>
			<string>n</string>
			<key>Module</key>
			<string>PBXNavigatorGroup</string>
			<key>Name</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>BundleLoadPath</key>
			<string></string>
			<key>MaxInstances</key>
			<string>n</string>
			<key>Module</key>
			<string>XCTaskListModule</string>
			<key>Name</key>
			<string>Task List</string>
		</dict>
		<dict>
			<key>BundleLoadPath</key>
			<string></string>
			<key>MaxInstances</key>
			<string>n</string>
			<key>Module</key>
			<string>XCDetailModule</string>
			<key>Name</key>
			<string>File and Smart Group Detail Viewer</string>
		</dict>
		<dict>
			<key>BundleLoadPath</key>
			<string></string>
			<key>MaxInstances</key>
			<string>1</string>
			<key>Module</key>
			<string>PBXBuildResultsModule</string>
			<key>Name</key>
			<string>Detailed Build Results Viewer</string>
		</dict>
		<dict>
			<key>BundleLoadPath</key>
			<string></string>
			<key>MaxInstances</key>
			<string>1</string>
			<key>Module</key>
			<string>PBXProjectFindModule</string>
			<key>Name</key>
			<string>Project Batch Find Tool</string>
		</dict>
		<dict>
			<key>BundleLoadPath</key>
			<string></string>
			<key>MaxInstances</key>
			<string>n</string>
			<key>Module</key>
			<string>XCProjectFormatConflictsModule</string>
			<key>Name</key>
			<string>Project Format Conflicts List</string>
		</dict>
		<dict>
			<key>BundleLoadPath</key>
			<string></string>
			<key>MaxInstances</key>
			<string>n</string>
			<key>Module</key>
			<string>PBXBookmarksModule</string>
			<key>Name</key>
			<string>Bookmarks Tool</string>
		</dict>
		<dict>
			<key>BundleLoadPath</key>
			<string></string>
			<key>MaxInstances</key>
			<string>n</string>
			<key>Module</key>
			<string>PBXClassBrowserModule</string>
			<key>Name</key>
			<string>Class Browser</string>
		</dict>
		<dict>
			<key>BundleLoadPath</key>
			<string></string>
			<key>MaxInstances</key>
			<string>n</string>
			<key>Module</key>
			<string>PBXCVSModule</string>
			<key>Name</key>
			<string>Source Code Control Tool</string>
		</dict>
		<dict>
			<key>BundleLoadPath</key>
			<string></string>
			<key>MaxInstances</key>
			<string>n</string>
			<key>Module</key>
			<string>PBXDebugBreakpointsModule</string>
			<key>Name</key>
			<string>Debug Breakpoints Tool</string>
		</dict>
		<dict>
			<key>BundleLoadPath</key>
			<string></string>
			<key>MaxInstances</key>
			<string>n</string>
			<key>Module</key>
			<string>XCDockableInspector</string>
			<key>Name</key>
			<string>Inspector</string>
		</dict>
		<dict>
			<key>BundleLoadPath</key>
			<string></string>
			<key>MaxInstances</key>
			<string>n</string>
			<key>Module</key>
			<string>PBXOpenQuicklyModule</string>
			<key>Name</key>
			<string>Open Quickly Tool</string>
		</dict>
		<dict>
			<key>BundleLoadPath</key>
			<string></string>
			<key>MaxInstances</key>
			<string>1</string>
			<key>Module</key>
			<string>PBXDebugSessionModule</string>
			<key>Name</key>
			<string>Debugger</string>
		</dict>
		<dict>
			<key>BundleLoadPath</key>
			<string></string>
			<key>MaxInstances</key>
			<string>1</string>
			<key>Module</key>
			<string>PBXDebugCLIModule</string>
			<key>Name</key>
			<string>Debug Console</string>
		</dict>
		<dict>
			<key>BundleLoadPath</key>
			<string></string>
			<key>MaxInstances</key>
			<string>n</string>
			<key>Module</key>
			<string>XCSnapshotModule</string>
			<key>Name</key>
			<string>Snapshots Tool</string>
		</dict>
	</array>
	<key>BundlePath</key>
	<string>/Developer/Library/PrivateFrameworks/DevToolsInterface.framework/Resources</string>
	<key>Description</key>
	<string>CondensedDescriptionKey</string>
	<key>DockingSystemVisible</key>
	<false/>
	<key>Extension</key>
	<string>mode2v3</string>
	<key>FavBarConfig</key>
	<dict>
		<key>PBXProjectModuleGUID</key>
		<string>E8F6BD801125F63600327C94</string>
		<key>XCBarModuleItemNames</key>
		<dict/>
		<key>XCBarModuleItems</key>
		<array/>
	</dict>
	<key>FirstTimeWindowDisplayed</key>
	<false/>
	<key>Identifier</key>
	<string>com.apple.perspectives.project.mode2v3</string>
	<key>MajorVersion</key>
	<integer>34</integer>
	<key>MinorVersion</key>
	<integer>0</integer>
	<key>Name</key>
	<string>Condensed</string>
	<key>Notifications</key>
	<array>
		<dict>
			<key>XCObserverAutoDisconnectKey</key>
			<true/>
			<key>XCObserverDefintionKey</key>
			<dict/>
			<key>XCObserverFactoryKey</key>
			<string>XCPerspectivesSpecificationIdentifier</string>
			<key>XCObserverGUIDKey</key>
			<string>XCObserverProjectIdentifier</string>
			<key>XCObserverNotificationKey</key>
			<string>PBXStatusBuildStateMessageNotification</string>
			<key>XCObserverTargetKey</key>
			<string>XCMainBuildResultsModuleGUID</string>
			<key>XCObserverTriggerKey</key>
			<string>awakenModuleWithObserver:</string>
			<key>XCObserverValidationKey</key>
			<dict/>
		</dict>
	</array>
	<key>OpenEditors</key>
	<array/>
	<key>PerspectiveWidths</key>
	<array>
		<integer>-1</integer>
	</array>
	<key>Perspectives</key>
	<array>
		<dict>
			<key>ChosenToolbarItems</key>
			<array>
				<string>buildOrClean</string>
				<string>active-combo-popup</string>
				<string>NSToolbarFlexibleSpaceItem</string>
				<string>servicesModuledebug</string>
				<string>get-info</string>
			</array>
			<key>ControllerClassBaseName</key>
			<string></string>
			<key>IconName</key>
			<string>WindowOfProject</string>
			<key>Identifier</key>
			<string>perspective.project</string>
			<key>IsVertical</key>
			<false/>
			<key>Layout</key>
			<array>
				<dict>
					<key>Proportion</key>
					<string>446pt</string>
					<key>Tabs</key>
					<array>
						<dict>
							<key>BecomeActive</key>
							<true/>
							<key>ContentConfiguration</key>
							<dict>
								<key>PBXBottomSmartGroupGIDs</key>
								<array/>
								<key>PBXProjectModuleGUID</key>
								<string>1C9437FD063B20B00039CFAC</string>
								<key>PBXProjectModuleLabel</key>
								<string>Files</string>
								<key>PBXProjectStructureProvided</key>
								<string>yes</string>
								<key>PBXSmartGroupTreeModuleColumnData</key>
								<dict>
									<key>PBXSmartGroupTreeModuleColumnWidthsKey</key>
									<array>
										<real>323</real>
										<real>20</real>
										<real>43</real>
										<real>43</real>
									</array>
									<key>PBXSmartGroupTreeModuleColumnsKey_v4</key>
									<array>
										<string>MainColumn</string>
										<string>FileBuiltColumn</string>
										<string>ErrorsColumn</string>
										<string>WarningsColumn</string>
									</array>
								</dict>
								<key>PBXSmartGroupTreeModuleOutlineStateKey_v7</key>
								<dict>
									<key>PBXSmartGroupTreeModuleOutlineStateExpansionKey</key>
									<array>
										<string>08FB7794FE84155DC02AAC07</string>
									</array>
									<key>PBXSmartGroupTreeModuleOutlineStateSelectionKey</key>
									<array>
										<array>
											<integer>0</integer>
										</array>
									</array>
									<key>PBXSmartGroupTreeModuleOutlineStateVisibleRectKey</key>
									<string>{{0, 0}, {429, 333}}</string>
								</dict>
								<key>PBXTopSmartGroupGIDs</key>
								<array/>
								<key>XCIncludePerspectivesSwitch</key>
								<false/>
							</dict>
							<key>GeometryConfiguration</key>
							<dict>
								<key>Frame</key>
								<string>{{10, 27}, {446, 351}}</string>
								<key>GroupTreeTableConfiguration</key>
								<array>
									<string>MainColumn</string>
									<real>323</real>
									<string>FileBuiltColumn</string>
									<real>20</real>
									<string>ErrorsColumn</string>
									<real>43</real>
									<string>WarningsColumn</string>
									<real>43</real>
								</array>
								<key>RubberWindowFrame</key>
								<string>474 289 446 419 0 0 1920 1178 </string>
							</dict>
							<key>Module</key>
							<string>PBXSmartGroupTreeModule</string>
						</dict>
						<dict>
							<key>ContentConfiguration</key>
							<dict>
								<key>PBXBottomSmartGroupGIDs</key>
								<array>
									<string>1C37FBAC04509CD000000102</string>
									<string>1C37FAAC04509CD000000102</string>
								</array>
								<key>PBXProjectModuleGUID</key>
								<string>1C9437FE063B20B00039CFAC</string>
								<key>PBXProjectModuleLabel</key>
								<string>Targets</string>
								<key>PBXProjectStructureProvided</key>
								<string>no</string>
								<key>PBXSmartGroupTreeModuleColumnData</key>
								<dict>
									<key>PBXSmartGroupTreeModuleColumnWidthsKey</key>
									<array>
										<real>429</real>
									</array>
									<key>PBXSmartGroupTreeModuleColumnsKey_v4</key>
									<array>
										<string>MainColumn</string>
									</array>
								</dict>
								<key>PBXSmartGroupTreeModuleOutlineStateKey_v7</key>
								<dict>
									<key>PBXSmartGroupTreeModuleOutlineStateExpansionKey</key>
									<array>
										<string>1C37FBAC04509CD000000102</string>
									</array>
									<key>PBXSmartGroupTreeModuleOutlineStateSelectionKey</key>
									<array>
										<array>
											<integer>1</integer>
											<integer>0</integer>
										</array>
									</array>
									<key>PBXSmartGroupTreeModuleOutlineStateVisibleRectKey</key>
									<string>{{0, 0}, {429, 333}}</string>
								</dict>
								<key>PBXTopSmartGroupGIDs</key>
								<array/>
								<key>XCIncludePerspectivesSwitch</key>
								<false/>
							</dict>
							<key>GeometryConfiguration</key>
							<dict>
								<key>Frame</key>
								<string>{{10, 27}, {446, 351}}</string>
								<key>GroupTreeTableConfiguration</key>
								<array>
									<string>MainColumn</string>
									<real>429</real>
								</array>
							</dict>
							<key>Module</key>
							<string>PBXSmartGroupTreeModule</string>
						</dict>
						<dict>
							<key>ContentConfiguration</key>
							<dict>
								<key>PBXBottomSmartGroupGIDs</key>
								<array>
									<string>1C37FABC05509CD000000102</string>
									<string>1C37FABC05539CD112110102</string>
									<string>E2644B35053B69B200211256</string>
									<string>1C37FABC04509CD000100104</string>
									<string>1CC0EA4004350EF90044410B</string>
									<string>1CC0EA4004350EF90041110B</string>
								</array>
								<key>PBXProjectModuleGUID</key>
								<string>1C9437FF063B20B00039CFAC</string>
								<key>PBXProjectModuleLabel</key>
								<string>Other</string>
								<key>PBXProjectStructureProvided</key>
								<string>no</string>
								<key>PBXSmartGroupTreeModuleColumnData</key>
								<dict>
									<key>PBXSmartGroupTreeModuleColumnWidthsKey</key>
									<array>
										<real>555</real>
									</array>
									<key>PBXSmartGroupTreeModuleColumnsKey_v4</key>
									<array>
										<string>MainColumn</string>
									</array>
								</dict>
								<key>PBXSmartGroupTreeModuleOutlineStateKey_v7</key>
								<dict>
									<key>PBXSmartGroupTreeModuleOutlineStateExpansionKey</key>
									<array/>
									<key>PBXSmartGroupTreeModuleOutlineStateSelectionKey</key>
									<array>
										<array>
											<integer>0</integer>
										</array>
									</array>
									<key>PBXSmartGroupTreeModuleOutlineStateVisibleRectKey</key>
									<string>{{0, 0}, {555, 301}}</string>
								</dict>
								<key>PBXTopSmartGroupGIDs</key>
								<array/>
								<key>XCIncludePerspectivesSwitch</key>
								<false/>
							</dict>
							<key>GeometryConfiguration</key>
							<dict>
								<key>Frame</key>
								<string>{{0, 0}, {572, 319}}</string>
								<key>GroupTreeTableConfiguration</key>
								<array>
									<string>MainColumn</string>
									<real>555</real>
								</array>
							</dict>
							<key>Module</key>
							<string>PBXSmartGroupTreeModule</string>
						</dict>
					</array>
				</dict>
			</array>
			<key>Name</key>
			<string>Project</string>
			<key>ServiceClasses</key>
			<array>
				<string>XCModuleDock</string>
				<string>XCDockableTabModule</string>
				<string>PBXSmartGroupTreeModule</string>
				<string>PBXSmartGroupTreeModule</string>
				<string>PBXSmartGroupTreeModule</string>
			</array>
			<key>TableOfContents</key>
			<array>
				<string>E8F6BE65112604F400327C94</string>
				<string>E8F6BE66112604F400327C94</string>
				<string>1C9437FD063B20B00039CFAC</string>
				<string>1C9437FE063B20B00039CFAC</string>
				<string>1C9437FF063B20B00039CFAC</string>
			</array>
			<key>ToolbarConfigUserDefaultsMinorVersion</key>
			<string>2</string>
			<key>ToolbarConfiguration</key>
			<string>xcode.toolbar.config.default.shortV3</string>
		</dict>
	</array>
	<key>PerspectivesBarVisible</key>
	<false/>
	<key>ShelfIsVisible</key>
	<false/>
	<key>SourceDescription</key>
	<string>file at '/Developer/Library/PrivateFrameworks/DevToolsInterface.framework/Resources/XCPerspectivesSpecificationMode2.xcperspec'</string>
	<key>StatusbarIsVisible</key>
	<true/>
	<key>TimeStamp</key>
	<real>287704456.20104402</real>
	<key>ToolbarConfigUserDefaultsMinorVersion</key>
	<string>2</string>
	<key>ToolbarDisplayMode</key>
	<integer>2</integer>
	<key>ToolbarIsVisible</key>
	<true/>
	<key>ToolbarSizeMode</key>
	<integer>2</integer>
	<key>Type</key>
	<string>Perspectives</string>
	<key>UpdateMessage</key>
	<string></string>
	<key>WindowJustification</key>
	<integer>0</integer>
	<key>WindowOrderList</key>
	<array>
		<string>1C530D52069F1CE1000CFCEE</string>
		<string>/Volumes/Coding/branches/trunk/dev/ua/sdk/external/UBVideo/H263/MacProjects/MacUBH263dec/UBH263dec.xcodeproj</string>
	</array>
	<key>WindowString</key>
	<string>474 289 446 419 0 0 1920 1178 </string>
	<key>WindowToolsV3</key>
	<array>
		<dict>
			<key>Identifier</key>
			<string>windowTool.detail</string>
			<key>IsVertical</key>
			<integer>0</integer>
			<key>Layout</key>
			<array>
				<dict>
					<key>Dock</key>
					<array>
						<dict>
							<key>BecomeActive</key>
							<integer>1</integer>
							<key>ContentConfiguration</key>
							<dict>
								<key>PBXBottomSmartGroupGIDs</key>
								<array>
									<string>1C37FBAC04509CD000000102</string>
									<string>1C37FAAC04509CD000000102</string>
									<string>1C08E77C0454961000C914BD</string>
									<string>1C37FABC05509CD000000102</string>
									<string>1C37FABC05539CD112110102</string>
									<string>E2644B35053B69B200211256</string>
									<string>1C37FABC04509CD000100104</string>
									<string>1CC0EA4004350EF90044410B</string>
									<string>1CC0EA4004350EF90041110B</string>
								</array>
								<key>PBXProjectModuleGUID</key>
								<string>1CE0B1FE06471DED0097A5F4</string>
								<key>PBXProjectModuleLabel</key>
								<string>Files</string>
								<key>PBXProjectStructureProvided</key>
								<string>yes</string>
								<key>PBXSmartGroupTreeModuleColumnData</key>
								<dict>
									<key>PBXSmartGroupTreeModuleColumnWidthsKey</key>
									<array>
										<real>245</real>
									</array>
									<key>PBXSmartGroupTreeModuleColumnsKey_v4</key>
									<array>
										<string>MainColumn</string>
									</array>
								</dict>
								<key>PBXSmartGroupTreeModuleOutlineStateKey_v7</key>
								<dict>
									<key>PBXSmartGroupTreeModuleOutlineStateExpansionKey</key>
									<array>
										<string>00C654E9FEEE28EC7F000001</string>
										<string>1C37FABC05509CD000000102</string>
									</array>
									<key>PBXSmartGroupTreeModuleOutlineStateSelectionKey</key>
									<array>
										<array>
											<integer>0</integer>
										</array>
									</array>
									<key>PBXSmartGroupTreeModuleOutlineStateVisibleRectKey</key>
									<string>{{0, 0}, {245, 350}}</string>
								</dict>
								<key>PBXTopSmartGroupGIDs</key>
								<array/>
								<key>XCIncludePerspectivesSwitch</key>
								<integer>0</integer>
							</dict>
							<key>GeometryConfiguration</key>
							<dict>
								<key>Frame</key>
								<string>{{0, 0}, {262, 368}}</string>
								<key>GroupTreeTableConfiguration</key>
								<array>
									<string>MainColumn</string>
									<real>245</real>
								</array>
								<key>RubberWindowFrame</key>
								<string>31 446 744 409 0 0 1440 878 </string>
							</dict>
							<key>Module</key>
							<string>PBXSmartGroupTreeModule</string>
							<key>Proportion</key>
							<string>262pt</string>
						</dict>
						<dict>
							<key>ContentConfiguration</key>
							<dict>
								<key>PBXProjectModuleGUID</key>
								<string>1CA1AED706398EBD00589147</string>
								<key>PBXProjectModuleLabel</key>
								<string>Detail</string>
							</dict>
							<key>GeometryConfiguration</key>
							<dict>
								<key>Frame</key>
								<string>{{267, 0}, {477, 368}}</string>
								<key>RubberWindowFrame</key>
								<string>31 446 744 409 0 0 1440 878 </string>
							</dict>
							<key>Module</key>
							<string>XCDetailModule</string>
							<key>Proportion</key>
							<string>477pt</string>
						</dict>
					</array>
					<key>Proportion</key>
					<string>100%</string>
				</dict>
			</array>
			<key>Name</key>
			<string>Detail</string>
			<key>ServiceClasses</key>
			<array>
				<string>PBXSmartGroupTreeModule</string>
				<string>XCDetailModule</string>
			</array>
			<key>StatusbarIsVisible</key>
			<integer>1</integer>
			<key>TableOfContents</key>
			<array>
				<string>1C335F2C07B51CD20023D4EE</string>
				<string>1C335F2D07B51CD20023D4EE</string>
				<string>1C335F2E07B51CD20023D4EE</string>
				<string>1CE0B1FE06471DED0097A5F4</string>
				<string>1CA1AED706398EBD00589147</string>
			</array>
			<key>ToolbarConfiguration</key>
			<string>xcode.toolbar.config.defaultV3</string>
			<key>WindowString</key>
			<string>31 446 744 409 0 0 1440 878 </string>
			<key>WindowToolGUID</key>
			<string>1C335F2C07B51CD20023D4EE</string>
			<key>WindowToolIsVisible</key>
			<integer>1</integer>
		</dict>
		<dict>
			<key>Identifier</key>
			<string>MENUSEPARATOR</string>
		</dict>
		<dict>
			<key>FirstTimeWindowDisplayed</key>
			<false/>
			<key>Identifier</key>
			<string>windowTool.build</string>
			<key>IsVertical</key>
			<true/>
			<key>Layout</key>
			<array>
				<dict>
					<key>Dock</key>
					<array>
						<dict>
							<key>ContentConfiguration</key>
							<dict>
								<key>PBXProjectModuleGUID</key>
								<string>1CD0528F0623707200166675</string>
								<key>PBXProjectModuleLabel</key>
								<string>&lt;No Editor&gt;</string>
								<key>StatusBarVisibility</key>
								<true/>
							</dict>
							<key>GeometryConfiguration</key>
							<dict>
								<key>Frame</key>
								<string>{{0, 0}, {602, 0}}</string>
								<key>RubberWindowFrame</key>
								<string>196 557 602 449 0 0 1920 1178 </string>
							</dict>
							<key>Module</key>
							<string>PBXNavigatorGroup</string>
							<key>Proportion</key>
							<string>0pt</string>
						</dict>
						<dict>
							<key>BecomeActive</key>
							<true/>
							<key>ContentConfiguration</key>
							<dict>
								<key>PBXProjectModuleGUID</key>
								<string>XCMainBuildResultsModuleGUID</string>
								<key>PBXProjectModuleLabel</key>
								<string>Build Results</string>
								<key>XCBuildResultsTrigger_Collapse</key>
								<integer>1022</integer>
								<key>XCBuildResultsTrigger_Open</key>
								<integer>1010</integer>
							</dict>
							<key>GeometryConfiguration</key>
							<dict>
								<key>Frame</key>
								<string>{{0, 5}, {602, 403}}</string>
								<key>RubberWindowFrame</key>
								<string>196 557 602 449 0 0 1920 1178 </string>
							</dict>
							<key>Module</key>
							<string>PBXBuildResultsModule</string>
							<key>Proportion</key>
							<string>403pt</string>
						</dict>
					</array>
					<key>Proportion</key>
					<string>408pt</string>
				</dict>
			</array>
			<key>Name</key>
			<string>Build Results</string>
			<key>ServiceClasses</key>
			<array>
				<string>PBXBuildResultsModule</string>
			</array>
			<key>StatusbarIsVisible</key>
			<true/>
			<key>TableOfContents</key>
			<array>
				<string>1C530D52069F1CE1000CFCEE</string>
				<string>E8F6BE62112604E900327C94</string>
				<string>1CD0528F0623707200166675</string>
				<string>XCMainBuildResultsModuleGUID</string>
			</array>
			<key>ToolbarConfiguration</key>
			<string>xcode.toolbar.config.buildV3</string>
			<key>WindowContentMinSize</key>
			<string>486 300</string>
			<key>WindowString</key>
			<string>196 557 602 449 0 0 1920 1178 </string>
			<key>WindowToolGUID</key>
			<string>1C530D52069F1CE1000CFCEE</string>
			<key>WindowToolIsVisible</key>
			<false/>
		</dict>
		<dict>
			<key>Identifier</key>
			<string>windowTool.debugger</string>
			<key>Layout</key>
			<array>
				<dict>
					<key>Dock</key>
					<array>
						<dict>
							<key>ContentConfiguration</key>
							<dict>
								<key>Debugger</key>
								<dict>
									<key>HorizontalSplitView</key>
									<dict>
										<key>_collapsingFrameDimension</key>
										<real>0.0</real>
										<key>_indexOfCollapsedView</key>
										<integer>0</integer>
										<key>_percentageOfCollapsedView</key>
										<real>0.0</real>
										<key>isCollapsed</key>
										<string>yes</string>
										<key>sizes</key>
										<array>
											<string>{{0, 0}, {333, 166}}</string>
											<string>{{333, 0}, {384, 166}}</string>
										</array>
									</dict>
									<key>VerticalSplitView</key>
									<dict>
										<key>_collapsingFrameDimension</key>
										<real>0.0</real>
										<key>_indexOfCollapsedView</key>
										<integer>0</integer>
										<key>_percentageOfCollapsedView</key>
										<real>0.0</real>
										<key>isCollapsed</key>
										<string>yes</string>
										<key>sizes</key>
										<array>
											<string>{{0, 0}, {717, 166}}</string>
											<string>{{0, 166}, {717, 229}}</string>
										</array>
									</dict>
								</dict>
								<key>LauncherConfigVersion</key>
								<string>8</string>
								<key>PBXProjectModuleGUID</key>
								<string>1C162984064C10D400B95A72</string>
								<key>PBXProjectModuleLabel</key>
								<string>Debug - GLUTExamples (Underwater)</string>
							</dict>
							<key>GeometryConfiguration</key>
							<dict>
								<key>DebugConsoleDrawerSize</key>
								<string>{100, 120}</string>
								<key>DebugConsoleVisible</key>
								<string>None</string>
								<key>DebugConsoleWindowFrame</key>
								<string>{{200, 200}, {500, 300}}</string>
								<key>DebugSTDIOWindowFrame</key>
								<string>{{200, 200}, {500, 300}}</string>
								<key>Frame</key>
								<string>{{0, 0}, {717, 395}}</string>
								<key>RubberWindowFrame</key>
								<string>124 412 717 437 0 0 1440 878 </string>
							</dict>
							<key>Module</key>
							<string>PBXDebugSessionModule</string>
							<key>Proportion</key>
							<string>100%</string>
						</dict>
					</array>
					<key>Proportion</key>
					<string>100%</string>
				</dict>
			</array>
			<key>Name</key>
			<string>Debugger</string>
			<key>ServiceClasses</key>
			<array>
				<string>PBXDebugSessionModule</string>
			</array>
			<key>StatusbarIsVisible</key>
			<integer>1</integer>
			<key>TableOfContents</key>
			<array>
				<string>1C530D54069F1CE1000CFCEE</string>
				<string>1C530D55069F1CE1000CFCEE</string>
				<string>1C162984064C10D400B95A72</string>
				<string>1C530D56069F1CE1000CFCEE</string>
			</array>
			<key>ToolbarConfiguration</key>
			<string>xcode.toolbar.config.debugV3</string>
			<key>WindowString</key>
			<string>124 412 717 437 0 0 1440 878 </string>
			<key>WindowToolGUID</key>
			<string>1C530D54069F1CE1000CFCEE</string>
			<key>WindowToolIsVisible</key>
			<integer>0</integer>
		</dict>
		<dict>
			<key>Identifier</key>
			<string>windowTool.find</string>
			<key>Layout</key>
			<array>
				<dict>
					<key>Dock</key>
					<array>
						<dict>
							<key>Dock</key>
							<array>
								<dict>
									<key>ContentConfiguration</key>
									<dict>
										<key>PBXProjectModuleGUID</key>
										<string>1CDD528C0622207200134675</string>
										<key>PBXProjectModuleLabel</key>
										<string>&lt;No Editor&gt;</string>
										<key>PBXSplitModuleInNavigatorKey</key>
										<dict>
											<key>Split0</key>
											<dict>
												<key>PBXProjectModuleGUID</key>
												<string>1CD0528D0623707200166675</string>
											</dict>
											<key>SplitCount</key>
											<string>1</string>
										</dict>
										<key>StatusBarVisibility</key>
										<integer>1</integer>
									</dict>
									<key>GeometryConfiguration</key>
									<dict>
										<key>Frame</key>
										<string>{{0, 0}, {781, 167}}</string>
										<key>RubberWindowFrame</key>
										<string>62 385 781 470 0 0 1440 878 </string>
									</dict>
									<key>Module</key>
									<string>PBXNavigatorGroup</string>
									<key>Proportion</key>
									<string>781pt</string>
								</dict>
							</array>
							<key>Proportion</key>
							<string>50%</string>
						</dict>
						<dict>
							<key>BecomeActive</key>
							<integer>1</integer>
							<key>ContentConfiguration</key>
							<dict>
								<key>PBXProjectModuleGUID</key>
								<string>1CD0528E0623707200166675</string>
								<key>PBXProjectModuleLabel</key>
								<string>Project Find</string>
							</dict>
							<key>GeometryConfiguration</key>
							<dict>
								<key>Frame</key>
								<string>{{8, 0}, {773, 254}}</string>
								<key>RubberWindowFrame</key>
								<string>62 385 781 470 0 0 1440 878 </string>
							</dict>
							<key>Module</key>
							<string>PBXProjectFindModule</string>
							<key>Proportion</key>
							<string>50%</string>
						</dict>
					</array>
					<key>Proportion</key>
					<string>428pt</string>
				</dict>
			</array>
			<key>Name</key>
			<string>Project Find</string>
			<key>ServiceClasses</key>
			<array>
				<string>PBXProjectFindModule</string>
			</array>
			<key>StatusbarIsVisible</key>
			<integer>1</integer>
			<key>TableOfContents</key>
			<array>
				<string>1C530D57069F1CE1000CFCEE</string>
				<string>1C530D58069F1CE1000CFCEE</string>
				<string>1C530D59069F1CE1000CFCEE</string>
				<string>1CDD528C0622207200134675</string>
				<string>1C530D5A069F1CE1000CFCEE</string>
				<string>1CE0B1FE06471DED0097A5F4</string>
				<string>1CD0528E0623707200166675</string>
			</array>
			<key>WindowString</key>
			<string>62 385 781 470 0 0 1440 878 </string>
			<key>WindowToolGUID</key>
			<string>1C530D57069F1CE1000CFCEE</string>
			<key>WindowToolIsVisible</key>
			<integer>0</integer>
		</dict>
		<dict>
			<key>Identifier</key>
			<string>MENUSEPARATOR</string>
		</dict>
		<dict>
			<key>Identifier</key>
			<string>windowTool.debuggerConsole</string>
			<key>Layout</key>
			<array>
				<dict>
					<key>Dock</key>
					<array>
						<dict>
							<key>BecomeActive</key>
							<integer>1</integer>
							<key>ContentConfiguration</key>
							<dict>
								<key>PBXProjectModuleGUID</key>
								<string>1C78EAAC065D492600B07095</string>
								<key>PBXProjectModuleLabel</key>
								<string>Debugger Console</string>
							</dict>
							<key>GeometryConfiguration</key>
							<dict>
								<key>Frame</key>
								<string>{{0, 0}, {650, 250}}</string>
								<key>RubberWindowFrame</key>
								<string>149 87 650 250 0 0 1440 878 </string>
							</dict>
							<key>Module</key>
							<string>PBXDebugCLIModule</string>
							<key>Proportion</key>
							<string>209pt</string>
						</dict>
					</array>
					<key>Proportion</key>
					<string>209pt</string>
				</dict>
			</array>
			<key>Name</key>
			<string>Debugger Console</string>
			<key>ServiceClasses</key>
			<array>
				<string>PBXDebugCLIModule</string>
			</array>
			<key>StatusbarIsVisible</key>
			<integer>1</integer>
			<key>TableOfContents</key>
			<array>
				<string>1C530D5B069F1CE1000CFCEE</string>
				<string>1C530D5C069F1CE1000CFCEE</string>
				<string>1C78EAAC065D492600B07095</string>
			</array>
			<key>ToolbarConfiguration</key>
			<string>xcode.toolbar.config.consoleV3</string>
			<key>WindowString</key>
			<string>149 87 650 250 0 0 1440 878 </string>
			<key>WindowToolGUID</key>
			<string>1C530D5B069F1CE1000CFCEE</string>
			<key>WindowToolIsVisible</key>
			<integer>0</integer>
		</dict>
		<dict>
			<key>Identifier</key>
			<string>windowTool.snapshots</string>
			<key>Layout</key>
			<array>
				<dict>
					<key>Dock</key>
					<array>
						<dict>
							<key>Module</key>
							<string>XCSnapshotModule</string>
							<key>Proportion</key>
							<string>100%</string>
						</dict>
					</array>
					<key>Proportion</key>
					<string>100%</string>
				</dict>
			</array>
			<key>Name</key>
			<string>Snapshots</string>
			<key>ServiceClasses</key>
			<array>
				<string>XCSnapshotModule</string>
			</array>
			<key>StatusbarIsVisible</key>
			<string>Yes</string>
			<key>ToolbarConfiguration</key>
			<string>xcode.toolbar.config.snapshots</string>
			<key>WindowString</key>
			<string>315 824 300 550 0 0 1440 878 </string>
			<key>WindowToolIsVisible</key>
			<string>Yes</string>
		</dict>
		<dict>
			<key>Identifier</key>
			<string>windowTool.scm</string>
			<key>Layout</key>
			<array>
				<dict>
					<key>Dock</key>
					<array>
						<dict>
							<key>ContentConfiguration</key>
							<dict>
								<key>PBXProjectModuleGUID</key>
								<string>1C78EAB2065D492600B07095</string>
								<key>PBXProjectModuleLabel</key>
								<string>&lt;No Editor&gt;</string>
								<key>PBXSplitModuleInNavigatorKey</key>
								<dict>
									<key>Split0</key>
									<dict>
										<key>PBXProjectModuleGUID</key>
										<string>1C78EAB3065D492600B07095</string>
									</dict>
									<key>SplitCount</key>
									<string>1</string>
								</dict>
								<key>StatusBarVisibility</key>
								<integer>1</integer>
							</dict>
							<key>GeometryConfiguration</key>
							<dict>
								<key>Frame</key>
								<string>{{0, 0}, {452, 0}}</string>
								<key>RubberWindowFrame</key>
								<string>743 379 452 308 0 0 1280 1002 </string>
							</dict>
							<key>Module</key>
							<string>PBXNavigatorGroup</string>
							<key>Proportion</key>
							<string>0pt</string>
						</dict>
						<dict>
							<key>BecomeActive</key>
							<integer>1</integer>
							<key>ContentConfiguration</key>
							<dict>
								<key>PBXProjectModuleGUID</key>
								<string>1CD052920623707200166675</string>
								<key>PBXProjectModuleLabel</key>
								<string>SCM</string>
							</dict>
							<key>GeometryConfiguration</key>
							<dict>
								<key>ConsoleFrame</key>
								<string>{{0, 259}, {452, 0}}</string>
								<key>Frame</key>
								<string>{{0, 7}, {452, 259}}</string>
								<key>RubberWindowFrame</key>
								<string>743 379 452 308 0 0 1280 1002 </string>
								<key>TableConfiguration</key>
								<array>
									<string>Status</string>
									<real>30</real>
									<string>FileName</string>
									<real>199</real>
									<string>Path</string>
									<real>197.09500122070312</real>
								</array>
								<key>TableFrame</key>
								<string>{{0, 0}, {452, 250}}</string>
							</dict>
							<key>Module</key>
							<string>PBXCVSModule</string>
							<key>Proportion</key>
							<string>262pt</string>
						</dict>
					</array>
					<key>Proportion</key>
					<string>266pt</string>
				</dict>
			</array>
			<key>Name</key>
			<string>SCM</string>
			<key>ServiceClasses</key>
			<array>
				<string>PBXCVSModule</string>
			</array>
			<key>StatusbarIsVisible</key>
			<integer>1</integer>
			<key>TableOfContents</key>
			<array>
				<string>1C78EAB4065D492600B07095</string>
				<string>1C78EAB5065D492600B07095</string>
				<string>1C78EAB2065D492600B07095</string>
				<string>1CD052920623707200166675</string>
			</array>
			<key>ToolbarConfiguration</key>
			<string>xcode.toolbar.config.scm</string>
			<key>WindowString</key>
			<string>743 379 452 308 0 0 1280 1002 </string>
		</dict>
		<dict>
			<key>Identifier</key>
			<string>windowTool.breakpoints</string>
			<key>IsVertical</key>
			<integer>0</integer>
			<key>Layout</key>
			<array>
				<dict>
					<key>Dock</key>
					<array>
						<dict>
							<key>BecomeActive</key>
							<integer>1</integer>
							<key>ContentConfiguration</key>
							<dict>
								<key>PBXBottomSmartGroupGIDs</key>
								<array>
									<string>1C77FABC04509CD000000102</string>
								</array>
								<key>PBXProjectModuleGUID</key>
								<string>1CE0B1FE06471DED0097A5F4</string>
								<key>PBXProjectModuleLabel</key>
								<string>Files</string>
								<key>PBXProjectStructureProvided</key>
								<string>no</string>
								<key>PBXSmartGroupTreeModuleColumnData</key>
								<dict>
									<key>PBXSmartGroupTreeModuleColumnWidthsKey</key>
									<array>
										<real>168</real>
									</array>
									<key>PBXSmartGroupTreeModuleColumnsKey_v4</key>
									<array>
										<string>MainColumn</string>
									</array>
								</dict>
								<key>PBXSmartGroupTreeModuleOutlineStateKey_v7</key>
								<dict>
									<key>PBXSmartGroupTreeModuleOutlineStateExpansionKey</key>
									<array>
										<string>1C77FABC04509CD000000102</string>
									</array>
									<key>PBXSmartGroupTreeModuleOutlineStateSelectionKey</key>
									<array>
										<array>
											<integer>0</integer>
										</array>
									</array>
									<key>PBXSmartGroupTreeModuleOutlineStateVisibleRectKey</key>
									<string>{{0, 0}, {168, 350}}</string>
								</dict>
								<key>PBXTopSmartGroupGIDs</key>
								<array/>
								<key>XCIncludePerspectivesSwitch</key>
								<integer>0</integer>
							</dict>
							<key>GeometryConfiguration</key>
							<dict>
								<key>Frame</key>
								<string>{{0, 0}, {185, 368}}</string>
								<key>GroupTreeTableConfiguration</key>
								<array>
									<string>MainColumn</string>
									<real>168</real>
								</array>
								<key>RubberWindowFrame</key>
								<string>315 424 744 409 0 0 1440 878 </string>
							</dict>
							<key>Module</key>
							<string>PBXSmartGroupTreeModule</string>
							<key>Proportion</key>
							<string>185pt</string>
						</dict>
						<dict>
							<key>ContentConfiguration</key>
							<dict>
								<key>PBXProjectModuleGUID</key>
								<string>1CA1AED706398EBD00589147</string>
								<key>PBXProjectModuleLabel</key>
								<string>Detail</string>
							</dict>
							<key>GeometryConfiguration</key>
							<dict>
								<key>Frame</key>
								<string>{{190, 0}, {554, 368}}</string>
								<key>RubberWindowFrame</key>
								<string>315 424 744 409 0 0 1440 878 </string>
							</dict>
							<key>Module</key>
							<string>XCDetailModule</string>
							<key>Proportion</key>
							<string>554pt</string>
						</dict>
					</array>
					<key>Proportion</key>
					<string>368pt</string>
				</dict>
			</array>
			<key>MajorVersion</key>
			<integer>3</integer>
			<key>MinorVersion</key>
			<integer>0</integer>
			<key>Name</key>
			<string>Breakpoints</string>
			<key>ServiceClasses</key>
			<array>
				<string>PBXSmartGroupTreeModule</string>
				<string>XCDetailModule</string>
			</array>
			<key>StatusbarIsVisible</key>
			<integer>1</integer>
			<key>TableOfContents</key>
			<array>
				<string>1CDDB66807F98D9800BB5817</string>
				<string>1CDDB66907F98D9800BB5817</string>
				<string>1CE0B1FE06471DED0097A5F4</string>
				<string>1CA1AED706398EBD00589147</string>
			</array>
			<key>ToolbarConfiguration</key>
			<string>xcode.toolbar.config.breakpointsV3</string>
			<key>WindowString</key>
			<string>315 424 744 409 0 0 1440 878 </string>
			<key>WindowToolGUID</key>
			<string>1CDDB66807F98D9800BB5817</string>
			<key>WindowToolIsVisible</key>
			<integer>1</integer>
		</dict>
		<dict>
			<key>Identifier</key>
			<string>windowTool.debugAnimator</string>
			<key>Layout</key>
			<array>
				<dict>
					<key>Dock</key>
					<array>
						<dict>
							<key>Module</key>
							<string>PBXNavigatorGroup</string>
							<key>Proportion</key>
							<string>100%</string>
						</dict>
					</array>
					<key>Proportion</key>
					<string>100%</string>
				</dict>
			</array>
			<key>Name</key>
			<string>Debug Visualizer</string>
			<key>ServiceClasses</key>
			<array>
				<string>PBXNavigatorGroup</string>
			</array>
			<key>StatusbarIsVisible</key>
			<integer>1</integer>
			<key>ToolbarConfiguration</key>
			<string>xcode.toolbar.config.debugAnimatorV3</string>
			<key>WindowString</key>
			<string>100 100 700 500 0 0 1280 1002 </string>
		</dict>
		<dict>
			<key>Identifier</key>
			<string>windowTool.bookmarks</string>
			<key>Layout</key>
			<array>
				<dict>
					<key>Dock</key>
					<array>
						<dict>
							<key>Module</key>
							<string>PBXBookmarksModule</string>
							<key>Proportion</key>
							<string>100%</string>
						</dict>
					</array>
					<key>Proportion</key>
					<string>100%</string>
				</dict>
			</array>
			<key>Name</key>
			<string>Bookmarks</string>
			<key>ServiceClasses</key>
			<array>
				<string>PBXBookmarksModule</string>
			</array>
			<key>StatusbarIsVisible</key>
			<integer>0</integer>
			<key>WindowString</key>
			<string>538 42 401 187 0 0 1280 1002 </string>
		</dict>
		<dict>
			<key>Identifier</key>
			<string>windowTool.projectFormatConflicts</string>
			<key>Layout</key>
			<array>
				<dict>
					<key>Dock</key>
					<array>
						<dict>
							<key>Module</key>
							<string>XCProjectFormatConflictsModule</string>
							<key>Proportion</key>
							<string>100%</string>
						</dict>
					</array>
					<key>Proportion</key>
					<string>100%</string>
				</dict>
			</array>
			<key>Name</key>
			<string>Project Format Conflicts</string>
			<key>ServiceClasses</key>
			<array>
				<string>XCProjectFormatConflictsModule</string>
			</array>
			<key>StatusbarIsVisible</key>
			<integer>0</integer>
			<key>WindowContentMinSize</key>
			<string>450 300</string>
			<key>WindowString</key>
			<string>50 850 472 307 0 0 1440 877</string>
		</dict>
		<dict>
			<key>Identifier</key>
			<string>windowTool.classBrowser</string>
			<key>Layout</key>
			<array>
				<dict>
					<key>Dock</key>
					<array>
						<dict>
							<key>BecomeActive</key>
							<integer>1</integer>
							<key>ContentConfiguration</key>
							<dict>
								<key>OptionsSetName</key>
								<string>Hierarchy, all classes</string>
								<key>PBXProjectModuleGUID</key>
								<string>1CA6456E063B45B4001379D8</string>
								<key>PBXProjectModuleLabel</key>
								<string>Class Browser - NSObject</string>
							</dict>
							<key>GeometryConfiguration</key>
							<dict>
								<key>ClassesFrame</key>
								<string>{{0, 0}, {368, 96}}</string>
								<key>ClassesTreeTableConfiguration</key>
								<array>
									<string>PBXClassNameColumnIdentifier</string>
									<real>208</real>
									<string>PBXClassBookColumnIdentifier</string>
									<real>22</real>
								</array>
								<key>Frame</key>
								<string>{{0, 0}, {624, 318}}</string>
								<key>MembersFrame</key>
								<string>{{0, 105}, {368, 395}}</string>
								<key>MembersTreeTableConfiguration</key>
								<array>
									<string>PBXMemberTypeIconColumnIdentifier</string>
									<real>22</real>
									<string>PBXMemberNameColumnIdentifier</string>
									<real>216</real>
									<string>PBXMemberTypeColumnIdentifier</string>
									<real>91</real>
									<string>PBXMemberBookColumnIdentifier</string>
									<real>22</real>
								</array>
								<key>PBXModuleWindowStatusBarHidden2</key>
								<integer>1</integer>
								<key>RubberWindowFrame</key>
								<string>128 171 624 339 0 0 1440 878 </string>
							</dict>
							<key>Module</key>
							<string>PBXClassBrowserModule</string>
							<key>Proportion</key>
							<string>319pt</string>
						</dict>
					</array>
					<key>Proportion</key>
					<string>319pt</string>
				</dict>
			</array>
			<key>Name</key>
			<string>Class Browser</string>
			<key>ServiceClasses</key>
			<array>
				<string>PBXClassBrowserModule</string>
			</array>
			<key>StatusbarIsVisible</key>
			<integer>0</integer>
			<key>TableOfContents</key>
			<array>
				<string>1C530D60069F1CE1000CFCEE</string>
				<string>1C530D61069F1CE1000CFCEE</string>
				<string>1CA6456E063B45B4001379D8</string>
			</array>
			<key>ToolbarConfiguration</key>
			<string>xcode.toolbar.config.classbrowser</string>
			<key>WindowString</key>
			<string>128 171 624 339 0 0 1440 878 </string>
			<key>WindowToolGUID</key>
			<string>1C530D60069F1CE1000CFCEE</string>
			<key>WindowToolIsVisible</key>
			<integer>0</integer>
		</dict>
		<dict>
			<key>Identifier</key>
			<string>windowTool.refactoring</string>
			<key>IncludeInToolsMenu</key>
			<integer>0</integer>
			<key>Layout</key>
			<array>
				<dict>
					<key>Dock</key>
					<array>
						<dict>
							<key>BecomeActive</key>
							<integer>1</integer>
							<key>GeometryConfiguration</key>
							<dict>
								<key>Frame</key>
								<string>{0, 0}, {500, 335}</string>
								<key>RubberWindowFrame</key>
								<string>{0, 0}, {500, 335}</string>
							</dict>
							<key>Module</key>
							<string>XCRefactoringModule</string>
							<key>Proportion</key>
							<string>100%</string>
						</dict>
					</array>
					<key>Proportion</key>
					<string>100%</string>
				</dict>
			</array>
			<key>Name</key>
			<string>Refactoring</string>
			<key>ServiceClasses</key>
			<array>
				<string>XCRefactoringModule</string>
			</array>
			<key>WindowString</key>
			<string>200 200 500 356 0 0 1920 1200 </string>
		</dict>
	</array>
</dict>
</plist>
