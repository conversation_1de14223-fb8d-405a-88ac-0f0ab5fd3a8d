@online{asio,
  author = {<PERSON><PERSON><PERSON>, <PERSON>},
  title = {Asio C++ Library},
  year = {2003},
  url = {https://think-async.com/Asio/},
  urldate = {2018-07-17}
}

@online{clang_thread_safety,
  author = {{The Clang Team}},
  title = {Clang Thread Safety Analysis},
  year = {2007},
  url = {https://clang.llvm.org/docs/ThreadSafetyAnalysis.html},
  urldate = {2018-07-17}
}

@online{beast,
  author = {<PERSON><PERSON><PERSON>, <PERSON>},
  title = {Boost.Beast},
  year = {2016},
  url = {https://github.com/boostorg/beast},
  urldate = {2018-07-17}
}

@online{h20,
  author = {{DeNA Co., Ltd.}},
  title = {H2O},
  year = {2014},
  url = {https://github.com/h2o/h2o},
  urldate = {2018-07-17}
}

@online{websocket_protocol,
  author = {<PERSON><PERSON> and <PERSON><PERSON>},
  title = {The WebSocket Protocol},
  howpublished = {Internet Requests for Comments},
  type = {RFC},
  number = {6455},
  year = {2011},
  month = {December},
  issn = {2070-1721},
  publisher = {RFC Editor},
  institution = {RFC Editor},
  url = {http://www.rfc-editor.org/rfc/rfc6455.txt},
  note = {\url{http://www.rfc-editor.org/rfc/rfc6455.txt}},
  doi = {10.17487/RFC6455}
}

@online{simple_websocket_server,
  author = {Eidheim, Ole Christian},
  title = {Simple-WebSocket-Server},
  year = {2014},
  url = {https://gitlab.com/eidheim/Simple-WebSocket-Server},
  urldate = {2018-07-17}
}

@online{mame,
  author = {MAMEDev},
  title = {MAME},
  year = {1997},
  url = {https://www.mamedev.org/},
  urldate = {2018-07-17}
}

@online{wakely,
  author = {Wakely, Jonathan},
  title = {Working Draft, C++ Extensions for Networking},
  year = {2017},
  url = {http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2017/n4656.pdf},
  urldate = {2018-07-17}
}

@misc{chung,
  title = {Point Cloud Framework for Rendering {3D} Models Using {Google} {Tango}},
  author = {Chung, Maxen and Callin, Julian},
  year = {2017},
  publisher = {Santa Clara: Santa Clara University, 2017},
  url = {https://scholarcommons.scu.edu/cseng_senior/84},
  howpublished = {Computer Science and Engineering Senior Theses, Santa Clara University},
}
