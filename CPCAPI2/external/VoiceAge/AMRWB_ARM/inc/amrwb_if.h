#ifndef _AMRWB_IF_H
#define _AMRWB_IF_H

#include "typedef.h"

// codec version
extern const int amrwb_major_version;
extern const int amrwb_minor_version;
extern const int amrwb_build_version;

#define  L_FRAME16K_AMRWB	320      /* Frame size at 16kHz  */
#define  L_PACKED_AMRWB		61		 /* max serial size      */

#define _good_frame  0
#define _bad_frame   1
#define _lost_frame  2
#define _no_frame    3

// encoder error codes
#define AMRWBENC_ERROR_NONE				0x0		// no error
#define AMRWBENC_ERROR_GENERAL			0x1		// general error
#define AMRWBENC_ERROR_INVALIDMODE		0x2		// encoder mode is out of range (0..8)

// decoder error codes
#define AMRWBDEC_ERROR_NONE				0x0		// no error
#define AMRWBDEC_ERROR_GENERAL			0x1		// general error
#define AMRWBDEC_ERROR_BITSTREAM		0x2		// bitstream is corrupt

#ifdef __cplusplus
extern "C" {
#endif

Word32 E_IF_amrwb_init(
	void *hState, 
	void *hScratch
);

Word32 E_IF_amrwb_encode(
		void *hState,
		Word16 *pSpeech,
		UWord8 *pBitstream,
		Word32 *iOutputSize,
		Word32 iMode,
		Word32 iEnableVAD
);

void E_IF_amrwb_queryBlockSize(
		Word32 *iSizeState,
		Word32 *iSizeScratch
);

Word32 D_IF_amrwb_init(
		void *hState,
		void *hScratch
);

Word32 D_IF_amrwb_decode(
		void *hState,
		UWord8 *pBitstream,
		Word16 *pSynthSpeech,
		Word32 iBadFrame
);

void D_IF_amrwb_queryBlockSize(
		Word32 *iSizeState,
		Word32 *iSizeScratch
);

Word32 D_IF_amrwb_getFrameProperties(
		UWord8 *pBitstream,
		Word32 *iPackedFrameSize
);

#ifdef __cplusplus
}
#endif

#endif //_AMRWB_IF_H_
