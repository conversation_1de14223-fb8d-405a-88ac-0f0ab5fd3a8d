/*
********************************************************************************
*
*      File             : typedef.c
*      Purpose          : Basic types.
*
********************************************************************************
*/

#ifndef _TYPEDEF_H_
#define _TYPEDEF_H_

#ifdef WIN32
typedef __int64			Word64;
#else
typedef long long Word64;
#endif
typedef int Word32;
typedef unsigned int UWord32;
typedef short Word16;
typedef unsigned short UWord16;
typedef char Word8;
typedef unsigned char UWord8;

#define AMRWB_SIZE32ALIGN(a)		((((a)>>2)+2)<<2)

//#define NULL ((void *)0)
#define HOMING

#endif //_TYPEDEF_H_
