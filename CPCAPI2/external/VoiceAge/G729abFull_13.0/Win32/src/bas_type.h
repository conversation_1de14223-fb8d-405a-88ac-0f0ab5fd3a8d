

#ifndef _BAS_TYPE_H_
#define _BAS_TYPE_H_


/* Inline functions */
/* This is valid for PC */

typedef           char     Word8;
typedef  unsigned char     UWord8;
typedef           short    Word16;
typedef  unsigned short    UWord16;
typedef           int      Word32;
typedef  unsigned int      UWord32;
#define AMRWBP_SIZE32ALIGN(a)		(((a+3)>>2)<<2)

#if defined(_MSC_VER)
typedef __int64 Word64;
typedef unsigned __int64 UWord64;
#else
typedef long long Word64;
typedef unsigned long long UWord64;
#endif

typedef           float    Float32;
typedef           double   Float64;

typedef  unsigned char     BYTE;
typedef  unsigned int      UINT;
typedef  unsigned long     dword;
typedef unsigned short int UINT16;
typedef double REAL_DOUBLE;
typedef float REAL_SINGLE;

#ifndef _BASETSD_H_

#if defined(__BORLANDC__) || defined (__WATCOMC__) || defined(_MSC_VER) || defined(__ZTC__) || defined(__HIGHC__) || defined (__CYGWIN32__)

//typedef long  int INT64;
//typedef long  int INT32;
typedef short int INT16;
typedef       int FLAG;
typedef double    FLOAT64;
typedef float     FLOAT32;
typedef unsigned short int UNS_Word16;  /* 16 bit "register"  (sw*) */
typedef short int Word16Rom;        /* 16 bit ROM data    (sr*) */
typedef       int Word32Rom;          /* 32 bit ROM data    (L_r*)  */
typedef double Word40;		     /* 40 bit accumulator        */

#ifndef _32_BITS_TARGET
typedef     int INT32;
typedef unsigned  int UINT32;
#elif !defined(_BASETSD_H_)
typedef int INT32;
typedef unsigned int UINT32;
#endif

#elif defined( __sun) || defined (MAC)
typedef long   INT64;
typedef int    INT32;
typedef short  INT16;
typedef unsigned short  UNS_INT16;
typedef int    FLAG;
typedef double FLOAT64;
typedef float  FLOAT32;
typedef short  Word16Rom;        /* 16 bit ROM data    (sr*) */
typedef int    Word32Rom;          /* 32 bit ROM data    (L_r*)  */
typedef double Word40;				   /* 40 bit accumulator        */
typedef unsigned int UINT32;

#elif defined(__unix__) || defined(__unix)
typedef long   INT64;
typedef int    INT32;
typedef short  INT16;
typedef unsigned short  UNS_INT16;
typedef int    FLAG;
typedef double FLOAT64;
typedef float  FLOAT32;
typedef unsigned short UNS_Word16;  /* 16 bit "register"  (sw*) */
typedef short  Word16Rom;        /* 16 bit ROM data    (sr*) */
typedef int    Word32Rom;          /* 32 bit ROM data    (L_r*)  */
typedef double Word40;				   /* 40 bit accumulator        */
typedef unsigned int UINT32;

#elif defined(VMS) || defined(__VMS)
typedef long   INT64;
typedef int    INT32;
typedef short  INT16;
typedef unsigned short  UNS_INT16;
typedef int    FLAG;
typedef double FLOAT64;
typedef float  FLOAT32;
typedef unsigned short UNS_Word16;  /* 16 bit "register"  (sw*) */
typedef short  Word16Rom;        /* 16 bit ROM data    (sr*) */
typedef int    Word32Rom;          /* 32 bit ROM data    (L_r*)  */
typedef double Word40;				   /* 40 bit accumulator        */
#else
#error  COMPILER NOT TESTED typedef.h needs to be updated, see readme
#endif

#endif   // _BASETSD_H_

typedef  float  FLOAT;

#define  F  FLOAT
/* Manage the simple or double precision for floating variables */
#define  SINGLE                  /* Must be put in comment for double precision */

/* Must be defined if compilation target is a 32 bits platform */
#define _32_BITS_TARGET
#ifdef SINGLE
typedef REAL_SINGLE REAL;
#else
typedef REAL_DOUBLE REAL;
#endif





#ifndef _PHDISP_STRUCT
#define _PHDISP_STRUCT

typedef struct{
	int		prevDispState;
	FLOAT	gainMem[6];
	FLOAT	prevCbGain;
	int		onset;
}PHDISP_STATMEM;
#endif



#ifdef CODECLIB_DLL
// The following ifdef block is the standard way of creating macros which make exporting 
// from a DLL simpler. All files within this DLL are compiled with the ENCAMR_WBP_EXPORTS
// symbol defined on the command line. this symbol should not be defined on any project
// that uses this DLL. This way any other project whose source files include this file see 
// CODEC_API functions as being imported from a DLL, wheras this DLL sees symbols
// defined with this macro as being exported.
#ifdef CODEC_EXPORTS
#define CODEC_API __declspec(dllexport)
#else
#define CODEC_API __declspec(dllimport)
#endif

#else
#define CODEC_API
#endif

#ifdef __cplusplus
#define CODEC_CPP_CONV extern "C"
#else
#define CODEC_CPP_CONV 
#endif
#endif //_BAS_TYPE_H_




