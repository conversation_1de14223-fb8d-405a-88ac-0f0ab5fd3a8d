//  This file was automatically generated on Sun Jun  5 16:50:18 2022
//  by libs/config/tools/generate.cpp
//  Copyright <PERSON> 2002-21.
//  Use, modification and distribution are subject to the 
//  Boost Software License, Version 1.0. (See accompanying file 
//  LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

//  See http://www.boost.org/libs/config for the most recent version.//
//  Revision $Id$
//

#include <boost/config.hpp>
#include <boost/config/assert_cxx03.hpp>

#ifdef BOOST_NO_CXX11_ADDRESSOF
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_ADDRESSOF."
#endif
#ifdef BOOST_NO_CXX11_ALIGNAS
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_ALIGNAS."
#endif
#ifdef BOOST_NO_CXX11_ALLOCATOR
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_ALLOCATOR."
#endif
#ifdef BOOST_NO_CXX11_AUTO_DECLARATIONS
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_AUTO_DECLARATIONS."
#endif
#ifdef BOOST_NO_CXX11_AUTO_MULTIDECLARATIONS
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_AUTO_MULTIDECLARATIONS."
#endif
#ifdef BOOST_NO_CXX11_CHAR16_T
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_CHAR16_T."
#endif
#ifdef BOOST_NO_CXX11_CHAR32_T
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_CHAR32_T."
#endif
#ifdef BOOST_NO_CXX11_CONSTEXPR
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_CONSTEXPR."
#endif
#ifdef BOOST_NO_CXX11_DECLTYPE
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_DECLTYPE."
#endif
#ifdef BOOST_NO_CXX11_DECLTYPE_N3276
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_DECLTYPE_N3276."
#endif
#ifdef BOOST_NO_CXX11_DEFAULTED_FUNCTIONS
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_DEFAULTED_FUNCTIONS."
#endif
#ifdef BOOST_NO_CXX11_DEFAULTED_MOVES
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_DEFAULTED_MOVES."
#endif
#ifdef BOOST_NO_CXX11_DELETED_FUNCTIONS
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_DELETED_FUNCTIONS."
#endif
#ifdef BOOST_NO_CXX11_EXPLICIT_CONVERSION_OPERATORS
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_EXPLICIT_CONVERSION_OPERATORS."
#endif
#ifdef BOOST_NO_CXX11_EXTERN_TEMPLATE
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_EXTERN_TEMPLATE."
#endif
#ifdef BOOST_NO_CXX11_FINAL
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_FINAL."
#endif
#ifdef BOOST_NO_CXX11_FIXED_LENGTH_VARIADIC_TEMPLATE_EXPANSION_PACKS
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_FIXED_LENGTH_VARIADIC_TEMPLATE_EXPANSION_PACKS."
#endif
#ifdef BOOST_NO_CXX11_FUNCTION_TEMPLATE_DEFAULT_ARGS
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_FUNCTION_TEMPLATE_DEFAULT_ARGS."
#endif
#ifdef BOOST_NO_CXX11_HDR_ARRAY
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_HDR_ARRAY."
#endif
#ifdef BOOST_NO_CXX11_HDR_ATOMIC
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_HDR_ATOMIC."
#endif
#ifdef BOOST_NO_CXX11_HDR_CHRONO
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_HDR_CHRONO."
#endif
#ifdef BOOST_NO_CXX11_HDR_CONDITION_VARIABLE
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_HDR_CONDITION_VARIABLE."
#endif
#ifdef BOOST_NO_CXX11_HDR_EXCEPTION
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_HDR_EXCEPTION."
#endif
#ifdef BOOST_NO_CXX11_HDR_FORWARD_LIST
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_HDR_FORWARD_LIST."
#endif
#ifdef BOOST_NO_CXX11_HDR_FUNCTIONAL
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_HDR_FUNCTIONAL."
#endif
#ifdef BOOST_NO_CXX11_HDR_FUTURE
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_HDR_FUTURE."
#endif
#ifdef BOOST_NO_CXX11_HDR_INITIALIZER_LIST
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_HDR_INITIALIZER_LIST."
#endif
#ifdef BOOST_NO_CXX11_HDR_MUTEX
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_HDR_MUTEX."
#endif
#ifdef BOOST_NO_CXX11_HDR_RANDOM
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_HDR_RANDOM."
#endif
#ifdef BOOST_NO_CXX11_HDR_RATIO
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_HDR_RATIO."
#endif
#ifdef BOOST_NO_CXX11_HDR_REGEX
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_HDR_REGEX."
#endif
#ifdef BOOST_NO_CXX11_HDR_SYSTEM_ERROR
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_HDR_SYSTEM_ERROR."
#endif
#ifdef BOOST_NO_CXX11_HDR_THREAD
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_HDR_THREAD."
#endif
#ifdef BOOST_NO_CXX11_HDR_TUPLE
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_HDR_TUPLE."
#endif
#ifdef BOOST_NO_CXX11_HDR_TYPEINDEX
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_HDR_TYPEINDEX."
#endif
#ifdef BOOST_NO_CXX11_HDR_TYPE_TRAITS
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_HDR_TYPE_TRAITS."
#endif
#ifdef BOOST_NO_CXX11_HDR_UNORDERED_MAP
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_HDR_UNORDERED_MAP."
#endif
#ifdef BOOST_NO_CXX11_HDR_UNORDERED_SET
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_HDR_UNORDERED_SET."
#endif
#ifdef BOOST_NO_CXX11_INLINE_NAMESPACES
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_INLINE_NAMESPACES."
#endif
#ifdef BOOST_NO_CXX11_LAMBDAS
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_LAMBDAS."
#endif
#ifdef BOOST_NO_CXX11_LOCAL_CLASS_TEMPLATE_PARAMETERS
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_LOCAL_CLASS_TEMPLATE_PARAMETERS."
#endif
#ifdef BOOST_NO_CXX11_NOEXCEPT
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_NOEXCEPT."
#endif
#ifdef BOOST_NO_CXX11_NON_PUBLIC_DEFAULTED_FUNCTIONS
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_NON_PUBLIC_DEFAULTED_FUNCTIONS."
#endif
#ifdef BOOST_NO_CXX11_NULLPTR
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_NULLPTR."
#endif
#ifdef BOOST_NO_CXX11_NUMERIC_LIMITS
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_NUMERIC_LIMITS."
#endif
#ifdef BOOST_NO_CXX11_OVERRIDE
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_OVERRIDE."
#endif
#ifdef BOOST_NO_CXX11_POINTER_TRAITS
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_POINTER_TRAITS."
#endif
#ifdef BOOST_NO_CXX11_RANGE_BASED_FOR
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_RANGE_BASED_FOR."
#endif
#ifdef BOOST_NO_CXX11_RAW_LITERALS
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_RAW_LITERALS."
#endif
#ifdef BOOST_NO_CXX11_REF_QUALIFIERS
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_REF_QUALIFIERS."
#endif
#ifdef BOOST_NO_CXX11_RVALUE_REFERENCES
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_RVALUE_REFERENCES."
#endif
#ifdef BOOST_NO_CXX11_SCOPED_ENUMS
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_SCOPED_ENUMS."
#endif
#ifdef BOOST_NO_CXX11_SFINAE_EXPR
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_SFINAE_EXPR."
#endif
#ifdef BOOST_NO_CXX11_SMART_PTR
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_SMART_PTR."
#endif
#ifdef BOOST_NO_CXX11_STATIC_ASSERT
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_STATIC_ASSERT."
#endif
#ifdef BOOST_NO_CXX11_STD_ALIGN
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_STD_ALIGN."
#endif
#ifdef BOOST_NO_CXX11_TEMPLATE_ALIASES
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_TEMPLATE_ALIASES."
#endif
#ifdef BOOST_NO_CXX11_THREAD_LOCAL
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_THREAD_LOCAL."
#endif
#ifdef BOOST_NO_CXX11_TRAILING_RESULT_TYPES
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_TRAILING_RESULT_TYPES."
#endif
#ifdef BOOST_NO_CXX11_UNICODE_LITERALS
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_UNICODE_LITERALS."
#endif
#ifdef BOOST_NO_CXX11_UNIFIED_INITIALIZATION_SYNTAX
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_UNIFIED_INITIALIZATION_SYNTAX."
#endif
#ifdef BOOST_NO_CXX11_UNRESTRICTED_UNION
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_UNRESTRICTED_UNION."
#endif
#ifdef BOOST_NO_CXX11_USER_DEFINED_LITERALS
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_USER_DEFINED_LITERALS."
#endif
#ifdef BOOST_NO_CXX11_VARIADIC_MACROS
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_VARIADIC_MACROS."
#endif
#ifdef BOOST_NO_CXX11_VARIADIC_TEMPLATES
#  error "Your compiler appears not to be fully C++11 compliant.  Detected via defect macro BOOST_NO_CXX11_VARIADIC_TEMPLATES."
#endif
