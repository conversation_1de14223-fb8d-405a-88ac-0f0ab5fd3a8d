// Boost.Function library

//  Copyright <PERSON> Gregor 2003. Use, modification and
//  distribution is subject to the Boost Software License, Version
//  1.0. (See accompanying file LICENSE_1_0.txt or copy at
//  http://www.boost.org/LICENSE_1_0.txt)

// For more information, see http://www.boost.org

#if BOOST_FUNCTION_NUM_ARGS == 0
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 0
#  ifndef BOOST_FUNCTION_0
#    define BOOST_FUNCTION_0
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 1
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 1
#  ifndef BOOST_FUNCTION_1
#    define BOOST_FUNCTION_1
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 2
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 2
#  ifndef BOOST_FUNCTION_2
#    define BOOST_FUNCTION_2
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 3
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 3
#  ifndef BOOST_FUNCTION_3
#    define BOOST_FUNCTION_3
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 4
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 4
#  ifndef BOOST_FUNCTION_4
#    define BOOST_FUNCTION_4
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 5
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 5
#  ifndef BOOST_FUNCTION_5
#    define BOOST_FUNCTION_5
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 6
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 6
#  ifndef BOOST_FUNCTION_6
#    define BOOST_FUNCTION_6
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 7
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 7
#  ifndef BOOST_FUNCTION_7
#    define BOOST_FUNCTION_7
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 8
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 8
#  ifndef BOOST_FUNCTION_8
#    define BOOST_FUNCTION_8
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 9
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 9
#  ifndef BOOST_FUNCTION_9
#    define BOOST_FUNCTION_9
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 10
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 10
#  ifndef BOOST_FUNCTION_10
#    define BOOST_FUNCTION_10
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 11
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 11
#  ifndef BOOST_FUNCTION_11
#    define BOOST_FUNCTION_11
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 12
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 12
#  ifndef BOOST_FUNCTION_12
#    define BOOST_FUNCTION_12
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 13
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 13
#  ifndef BOOST_FUNCTION_13
#    define BOOST_FUNCTION_13
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 14
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 14
#  ifndef BOOST_FUNCTION_14
#    define BOOST_FUNCTION_14
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 15
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 15
#  ifndef BOOST_FUNCTION_15
#    define BOOST_FUNCTION_15
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 16
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 16
#  ifndef BOOST_FUNCTION_16
#    define BOOST_FUNCTION_16
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 17
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 17
#  ifndef BOOST_FUNCTION_17
#    define BOOST_FUNCTION_17
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 18
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 18
#  ifndef BOOST_FUNCTION_18
#    define BOOST_FUNCTION_18
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 19
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 19
#  ifndef BOOST_FUNCTION_19
#    define BOOST_FUNCTION_19
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 20
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 20
#  ifndef BOOST_FUNCTION_20
#    define BOOST_FUNCTION_20
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 21
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 21
#  ifndef BOOST_FUNCTION_21
#    define BOOST_FUNCTION_21
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 22
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 22
#  ifndef BOOST_FUNCTION_22
#    define BOOST_FUNCTION_22
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 23
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 23
#  ifndef BOOST_FUNCTION_23
#    define BOOST_FUNCTION_23
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 24
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 24
#  ifndef BOOST_FUNCTION_24
#    define BOOST_FUNCTION_24
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 25
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 25
#  ifndef BOOST_FUNCTION_25
#    define BOOST_FUNCTION_25
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 26
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 26
#  ifndef BOOST_FUNCTION_26
#    define BOOST_FUNCTION_26
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 27
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 27
#  ifndef BOOST_FUNCTION_27
#    define BOOST_FUNCTION_27
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 28
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 28
#  ifndef BOOST_FUNCTION_28
#    define BOOST_FUNCTION_28
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 29
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 29
#  ifndef BOOST_FUNCTION_29
#    define BOOST_FUNCTION_29
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 30
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 30
#  ifndef BOOST_FUNCTION_30
#    define BOOST_FUNCTION_30
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 31
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 31
#  ifndef BOOST_FUNCTION_31
#    define BOOST_FUNCTION_31
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 32
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 32
#  ifndef BOOST_FUNCTION_32
#    define BOOST_FUNCTION_32
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 33
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 33
#  ifndef BOOST_FUNCTION_33
#    define BOOST_FUNCTION_33
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 34
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 34
#  ifndef BOOST_FUNCTION_34
#    define BOOST_FUNCTION_34
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 35
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 35
#  ifndef BOOST_FUNCTION_35
#    define BOOST_FUNCTION_35
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 36
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 36
#  ifndef BOOST_FUNCTION_36
#    define BOOST_FUNCTION_36
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 37
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 37
#  ifndef BOOST_FUNCTION_37
#    define BOOST_FUNCTION_37
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 38
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 38
#  ifndef BOOST_FUNCTION_38
#    define BOOST_FUNCTION_38
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 39
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 39
#  ifndef BOOST_FUNCTION_39
#    define BOOST_FUNCTION_39
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 40
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 40
#  ifndef BOOST_FUNCTION_40
#    define BOOST_FUNCTION_40
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 41
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 41
#  ifndef BOOST_FUNCTION_41
#    define BOOST_FUNCTION_41
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 42
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 42
#  ifndef BOOST_FUNCTION_42
#    define BOOST_FUNCTION_42
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 43
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 43
#  ifndef BOOST_FUNCTION_43
#    define BOOST_FUNCTION_43
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 44
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 44
#  ifndef BOOST_FUNCTION_44
#    define BOOST_FUNCTION_44
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 45
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 45
#  ifndef BOOST_FUNCTION_45
#    define BOOST_FUNCTION_45
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 46
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 46
#  ifndef BOOST_FUNCTION_46
#    define BOOST_FUNCTION_46
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 47
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 47
#  ifndef BOOST_FUNCTION_47
#    define BOOST_FUNCTION_47
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 48
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 48
#  ifndef BOOST_FUNCTION_48
#    define BOOST_FUNCTION_48
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 49
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 49
#  ifndef BOOST_FUNCTION_49
#    define BOOST_FUNCTION_49
#    include <boost/function/function_template.hpp>
#  endif
#elif BOOST_FUNCTION_NUM_ARGS == 50
#  undef BOOST_FUNCTION_MAX_ARGS_DEFINED
#  define BOOST_FUNCTION_MAX_ARGS_DEFINED 50
#  ifndef BOOST_FUNCTION_50
#    define BOOST_FUNCTION_50
#    include <boost/function/function_template.hpp>
#  endif
#else
#  error Cannot handle Boost.Function objects that accept more than 50 arguments!
#endif
