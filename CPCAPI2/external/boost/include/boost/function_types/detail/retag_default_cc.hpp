
// (C) Copyright <PERSON>
//
// Use modification and distribution are subject to the boost Software License,
// Version 1.0. (See http://www.boost.org/LICENSE_1_0.txt).

//------------------------------------------------------------------------------

#ifndef BOOST_FT_DETAIL_RETAG_DEFAULT_CC_HPP_INCLUDED
#define BOOST_FT_DETAIL_RETAG_DEFAULT_CC_HPP_INCLUDED

#include <boost/mpl/bitand.hpp>

#include <boost/function_types/components.hpp>

#if defined(BOOST_FT_PREPROCESSING_MODE)
#   include <boost/function_types/detail/pp_retag_default_cc/master.hpp>
#else
#   include <boost/function_types/detail/pp_retag_default_cc/preprocessed.hpp>
#endif

#endif

