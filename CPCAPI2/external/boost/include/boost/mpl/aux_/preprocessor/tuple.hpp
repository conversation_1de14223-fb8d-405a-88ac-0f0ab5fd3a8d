
#ifndef BOOST_MPL_AUX_PREPROCESSOR_TUPLE_HPP_INCLUDED
#define BOOST_MPL_AUX_PREPROCESSOR_TUPLE_HPP_INCLUDED

// Copyright Aleksey Gurtovoy 2002-2004
//
// Distributed under the Boost Software License, Version 1.0. 
// (See accompanying file LICENSE_1_0.txt or copy at 
// http://www.boost.org/LICENSE_1_0.txt)
//
// See http://www.boost.org/libs/mpl for documentation.

// $Id$
// $Date$
// $Revision$

#define BOOST_MPL_PP_TUPLE_11_ELEM_0(e0,e1,e2,e3,e4,e5,e6,e7,e8,e9,e10) e0
#define BOOST_MPL_PP_TUPLE_11_ELEM_1(e0,e1,e2,e3,e4,e5,e6,e7,e8,e9,e10) e1
#define BOOST_MPL_PP_TUPLE_11_ELEM_2(e0,e1,e2,e3,e4,e5,e6,e7,e8,e9,e10) e2
#define BOOST_MPL_PP_TUPLE_11_ELEM_3(e0,e1,e2,e3,e4,e5,e6,e7,e8,e9,e10) e3
#define BOOST_MPL_PP_TUPLE_11_ELEM_4(e0,e1,e2,e3,e4,e5,e6,e7,e8,e9,e10) e4
#define BOOST_MPL_PP_TUPLE_11_ELEM_5(e0,e1,e2,e3,e4,e5,e6,e7,e8,e9,e10) e5
#define BOOST_MPL_PP_TUPLE_11_ELEM_6(e0,e1,e2,e3,e4,e5,e6,e7,e8,e9,e10) e6
#define BOOST_MPL_PP_TUPLE_11_ELEM_7(e0,e1,e2,e3,e4,e5,e6,e7,e8,e9,e10) e7
#define BOOST_MPL_PP_TUPLE_11_ELEM_8(e0,e1,e2,e3,e4,e5,e6,e7,e8,e9,e10) e8
#define BOOST_MPL_PP_TUPLE_11_ELEM_9(e0,e1,e2,e3,e4,e5,e6,e7,e8,e9,e10) e9
#define BOOST_MPL_PP_TUPLE_11_ELEM_10(e0,e1,e2,e3,e4,e5,e6,e7,e8,e9,e10) e10

#endif // BOOST_MPL_AUX_PREPROCESSOR_TUPLE_HPP_INCLUDED
