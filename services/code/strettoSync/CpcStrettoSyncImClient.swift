//
//  CpcStrettoSyncImClient.swift
//  Services
//
//  Created by <PERSON><PERSON> on 04/06/2019.
//  Copyright © 2019 CounterPath. All rights reserved.
//

import Foundation
import ServicesPrivate

private let log = LogSubsystem(kLogSubCpc)


@objc(CpcStrettoSyncImClient) class CpcStrettoSyncImClient: CpcStrettoSyncClient {
	private(set) var strettoSyncAccId: String?
	
	private(set) var connectionState: CpcStrettoSyncConnectionState = .disconnected(nil)
	private var updateNotification: CpcStrettoSyncUpdateNotification!
	private var convUpdateNotification: CpcStrettoSyncConversationUpdateNotification!
	private var itemsUpdateNotification: CpcStrettoSyncItemsUpdateNotification!
	private var reactionsUpdateNotification: CpcStrettoSyncReactionsUpdateNotification!
	private var loginReactionsRev: CPCRemoteSyncRevision = 0
	private var mustSyncReactions = false
	
	override init(account: CpcAccountImpl?) {
		super.init(account: account)
	}
	
	deinit {
		disconnect()
		NotificationCenter.default.removeObserver(self)
	}
}

extension CpcStrettoSyncImClient {
	
	@objc func connect() {
		guard let ssAccountId = account?.strettoSyncAccountId() else {
			return
		}
		
		// lock account id
		strettoSyncAccId = ssAccountId
		
		if let conn = connection {
			log.Log(.EDebug, "Connection is already set")
			conn.removeAccount(handler: self)
			connection = nil
		}
		
		connectionState = .disconnected(nil)
		
		guard let strettoSyncUrl = account?.strettoSyncUrl, let passwordData = account?.strettoSyncPasswordData else {
			return
		}
		
		let pw = passwordData.cpcDecrypt(NSStringFromClass(CpcAccount.self)) ?? ""
		let settings = CpcStrettoSyncConnectionSettings(type: account?.protocol == ESignalingProtocolSmsApi ? .smsSync: .imSync, url: strettoSyncUrl, password: pw)
		
		log.Log(.EDebug, "RemoteSyncSettings: url: \(settings.url), account: \(strettoSyncAccountId)")
		
		self.connection = CpcServicesImpl.instance()?.cpcStrettoSync.makeConnection(settings: settings)
		
		connection?.addAccount(handler: self)
		
		createNotificationHandlers()
	}
	
	@objc func disconnect() {
		log.Log(.EDebug)
		
		if strettoSyncAccId == nil {
			return			// seems not connected
		}
		
		connection?.removeAccount(handler: self)
		
		if let notification = updateNotification {
			connection?.removeNotificationHandler(notification: notification)
			updateNotification = nil
		}
		
		if let notification = convUpdateNotification {
			connection?.removeNotificationHandler(notification: notification)
			convUpdateNotification = nil
		}
		
		if let notification = itemsUpdateNotification {
			connection?.removeNotificationHandler(notification: notification)
			itemsUpdateNotification = nil
		}
		
		if let notification = reactionsUpdateNotification {
			connection?.removeNotificationHandler(notification: notification)
			reactionsUpdateNotification = nil
		}
		
		strettoSyncAccId = nil
	}
	
	func createNotificationHandlers() {
		
		updateNotification = CpcStrettoSyncUpdateNotification { [weak self] event in
			guard let strongSelf = self, strongSelf.state != ECpcStrettoSyncStateDisconnected && strongSelf.state != ECpcStrettoSyncStateDisconnecting else {
				log.Log(.EDebug, "Ignored: shutting down")
				return
			}
			
			if strongSelf.state == ECpcStrettoSyncStateConnected && strongSelf.currentRev < event.rev {
				strongSelf.currentRev = event.rev
			}
			
			strongSelf.processNotificationUpdateEvent(event)
		}
		
		convUpdateNotification = CpcStrettoSyncConversationUpdateNotification { [weak self] event in
			guard let strongSelf = self, strongSelf.state != ECpcStrettoSyncStateDisconnected && strongSelf.state != ECpcStrettoSyncStateDisconnecting else {
				log.Log(.EDebug, "Ignored: shutting down")
				return
			}
			
			if strongSelf.state == ECpcStrettoSyncStateConnected && strongSelf.currentRev < event.rev {
				strongSelf.currentRev = event.rev
			}
			
			strongSelf.processNotificationConversationUpdate(event)
		}
		
		itemsUpdateNotification = CpcStrettoSyncItemsUpdateNotification { [weak self] event in
			guard let strongSelf = self, strongSelf.state != ECpcStrettoSyncStateDisconnected && strongSelf.state != ECpcStrettoSyncStateDisconnecting else {
				log.Log(.EDebug, "Ignored: shutting down")
				return
			}
			
			strongSelf.processNotificationItemsUpdate(event)
		}
		
		reactionsUpdateNotification = CpcStrettoSyncReactionsUpdateNotification { [weak self] event in
			guard let self, self.state != ECpcStrettoSyncStateDisconnected && self.state != ECpcStrettoSyncStateDisconnecting else {
				log.Log(.EDebug, "Ignored: shutting down")
				return
			}
			
			processNotificationMessageReactionsEvent(event)
		}
		
		connection?.addNotificationHandler(notification: updateNotification)
		connection?.addNotificationHandler(notification: convUpdateNotification)
		connection?.addNotificationHandler(notification: itemsUpdateNotification)
		
		if CpcFeature.getBool(kFeatureXmppMessageReactions) &&
		   (CpcServices.instance()?.cpcOptions?.enableXmppMessageReactions ?? false) {
			connection?.addNotificationHandler(notification: reactionsUpdateNotification)
		} else {
			log.Log(.EDebug, "Not registering reactions notification handler because feature is disabled")
		}
	}
	
	func transition(to state: CpcStrettoSyncState) {
		guard self.state != state else {
			return
		}
		
		log.Log(.EDebug, "state = \(state)")
		
		self.state = state
		
		var accountSyncStatus: SynchronizationStatus = ESynchronizationStatusDisconnected
		
		switch state {
		case ECpcStrettoSyncStateConnecting:
			accountSyncStatus = ESynchronizationStatusConnecting
		case ECpcStrettoSyncStateConnected:
			accountSyncStatus = ESynchronizationStatusConnected
		case ECpcStrettoSyncStateDisconnecting:
			accountSyncStatus = ESynchronizationStatusDisconnected
		default:
			break
		}
		
		account?.notifySynchronizationStatusChanged(accountSyncStatus)
		
		switch state {
		case ECpcStrettoSyncStateConnecting:
			loginRev = 0
			transition(connectingStage: ECpcStrettoSyncConnectingStarted)
		case ECpcStrettoSyncStateConnected:
			currentRev = loginRev
			ensureSelfConversationExists(true)
		case ECpcStrettoSyncStateDisconnected:
			transition(connectingStage: ECpcStrettoSyncConnectingInit)
		default:
			break
		}
	}
	
	func transition(connectingStage: CpcStrettoSyncConnectingStage) {
		if self.connectingStage == connectingStage {
			return
		}
		
		self.connectingStage = connectingStage
		
		log.Log(.EDebug, "stage = \(connectingStage)")
		
		switch connectingStage {
		case ECpcStrettoSyncConnectingInit:
			// SDK remote sync module may be re-connecting at this point.
			break
			
		case ECpcStrettoSyncConnectingStarted:
			let strettoSyncDb = CpcServicesImpl.instance()?.cpcIm?.strettoSyncDb
			strettoSyncDb?.deleteAllPersistentRequests(ofType: CpcStrettoSyncSweepMarkerRequest.self, accountId: strettoSyncAccountId)
			let pendingCount = strettoSyncDb?.countOfPersistentRequests(accountId: strettoSyncAccountId) ?? 0
			
			if pendingCount > 0 {
				log.Log(.EInfo, "Adding sweep op: pending=\(pendingCount)")
				connection?.sendRequest(CpcStrettoSyncSweepMarkerRequest(strettoSyncAccountId))
			}
			
		case ECpcStrettoSyncConnectingFetchRevision:
			let request = CpcStrettoSyncFetchRevisionRequest(accountId: strettoSyncAccountId)
			request.onSuccess = { response in
				
				if self.state == ECpcStrettoSyncStateDisconnected || self.state == ECpcStrettoSyncStateDisconnecting {
					log.Log(.EDebug, "Ignored: shutting down")
					return
				}
				
				self.loginRev = response.revision
				log.Log(.EDebug, "Start revision=\(response.revision)")
				
				if CpcFeature.getBool(kFeatureXmppMessageReactions) &&
						(CpcServices.instance()?.cpcOptions?.enableXmppMessageReactions ?? false) {
					self.transition(connectingStage: ECpcStrettoSyncConnectingFetchReactionsRevision)
				} else {
					self.transition(connectingStage: ECpcStrettoSyncConnectingGetMessageCounts)
				}
			}
			
			connection?.sendRequest(request)
		
		case ECpcStrettoSyncConnectingFetchReactionsRevision:
			let request = CpcStrettoSyncFetchMessageReactionsRequest(
				accountId: strettoSyncAccountId
			)
			request.onSuccess = { response in
				if self.state == ECpcStrettoSyncStateDisconnected || self.state == ECpcStrettoSyncStateDisconnecting {
					log.Log(.EDebug, "Ignored: shutting down")
					return
				}
				
				if self.loginReactionsRev == 0 {
					self.mustSyncReactions = true
					log.Log(.EDebug, "Reactions revision=\(response)")
				} else if self.loginReactionsRev != response {
					self.mustSyncReactions = true
					log.Log(.EDebug, "Reactions revision changed: \(self.loginReactionsRev) -> \(response)")
				} else {
					self.mustSyncReactions = false
					log.Log(.EDebug, "Reactions revision unchanged: \(response)")
				}
				
				self.loginReactionsRev = response
				log.Log(.EDebug, "Reactions revision=\(response)")
				self.transition(connectingStage: ECpcStrettoSyncConnectingGetMessageCounts)
			}
			request.onError = { error in
				log.Log(.EError, "Fetch reactions revision failed: \(error.localizedDescription)")
				self.transition(connectingStage: ECpcStrettoSyncConnectingGetMessageCounts)
			}
			
			connection?.sendRequest(request)
			
		case ECpcStrettoSyncConnectingGetMessageCounts:
			let request = CpcStrettoSyncAccountMessageCountsRequest(accountId: strettoSyncAccountId, itemTypes: getAccountSyncItemTypes().map({ $0.intValue }))
			request.onSuccess = { response in
				
				guard let strettoSyncDb = CpcServicesImpl.instance()?.cpcIm?.strettoSyncDb,
							let account = self.account else {
					return
				}
				
				let unsyncedUnread = strettoSyncDb.getStrettoUnsyncedUnreadMessagesCountForAccountId(account.accountId)
				let unsyncedTotal = strettoSyncDb.getStrettoUnsyncedTotalMessagesCountForAccountId(account.accountId)
				strettoSyncDb.updateAccount(account.accountId, strettoSyncUnreadCount: response.unread + unsyncedUnread, totalCount: response.total+unsyncedTotal)
				
				if (self.loginRev > 0 && self.loginRev == self.currentRev) && !self.mustSyncReactions {
					log.Log(.EDebug, "No changes since last connect: rev=\(self.currentRev)")
					self.transition(connectingStage: ECpcStrettoSyncConnectingCompleted)
				} else {
					log.Log(.EDebug, "There are changes since last connect: login rev=\(self.loginRev), rev=\(self.currentRev)")
					self.transition(connectingStage: ECpcStrettoSyncConnectingFetchTopConversations)
				}

				// Note: mustSyncReactions flag will be reset after actual synchronization is complete
			}
			
			connection?.sendRequest(request)
			
		case ECpcStrettoSyncConnectingFetchTopConversations:
			let count = CpcServicesImpl.instance()?.cpcOptions?.strettoSyncPrefetchNumberOfConversations ?? 0
			let request = CpcStrettoSyncFetchConversationsRequest(accountId: strettoSyncAccountId, timeBegin: 0, timeEnd: 0, offset: 0, count: Int(count))
			request.onSuccess = { response in
				
				if self.state == ECpcStrettoSyncStateDisconnected || self.state == ECpcStrettoSyncStateDisconnecting {
					log.Log(.EDebug, "Ignored: shutting down")
					return
				}
				
				let pendingConversations = CpcServicesImpl.instance()?.cpcIm?.strettoSyncConversationsWithScheduledIncomingMessages()
				
				self.saveExistingConversations(response.items)
	
				self.purgeUnsavedConversations()
				
				// Restore stretto sync conversations with pending incoming messages
				pendingConversations?.forEach { CpcServicesImpl.instance()?.cpcIm?.recreateStrettoSyncConversation($0) }
				
				let items = response.items.compactMap {
					(($0 as? CPCRemoteSyncConversationThreadItem)?.latestMessage?.account == self.strettoSyncAccountId) ? $0: nil
				}
				
				self.ensureSelfConversationExists(false)
				
				CpcServicesImpl.instance()?.cpcIm?.reloadConversations(completion: {
					_ = self.processConversationThreadItems(items)
					self.disposeOfExistingConversations()

					// Sync reactions if needed after main sync is complete
					if self.mustSyncReactions {
						self.syncAllReactionsForExistingMessages()
					} else {
						self.transition(to: ECpcStrettoSyncStateConnected)
					}
				})
			}
			
			request.onError = { error in
				log.Log(.EError, "Fetch conversation list failed: \(error.localizedDescription)")
				
				if self.state == ECpcStrettoSyncStateDisconnected || self.state == ECpcStrettoSyncStateDisconnecting {
					log.Log(.EDebug, "Ignored: shutting down")
					return
				}
				
				self.disconnect()
				self.connect()
			}
			
			connection?.sendRequest(request)

		case ECpcStrettoSyncConnectingCompleted:
			transition(to: ECpcStrettoSyncStateConnected)
		
		default:
			log.Log(.EDebug, "Unknown stage \(connectingStage)")
		}
	}
}

extension CpcStrettoSyncImClient {
	@objc func add(conversation: CpcImConversation) {
		#if REMOTE_SYNC_GROUP_CHAT
		#else
		if conversation.isMUC || conversation.isRoom {
			log.Log(.EMax, "MUC syncing disabled: remotePartyAddress=\(conversation.debugAddress())")
			return
		}
		#endif
		
		if conversation.isMUC == false {
			log.Log(.EMax, "Skipping one-to-one conversation: remotePartyAddress=\(conversation.debugAddress())")
			return
		}

		log.Log(.EDebug, "remotePartyAddress=\(conversation.debugAddress())")
		
		let request = CpcStrettoSyncAddConversationRequest(accountId: strettoSyncAccountId, conversationId: conversation.conversationId)
		connection?.sendRequest(request)
	}
	
	@objc func add(message: CpcImMessage, conversation: CpcImConversation) {
		
		if message.system {
			log.Log(.EDebug, "Skipping system message: \(message.message ?? "")")
			return
		}
		
		if message.isFileMessage() {
			log.Log(.EDebug, "Skipping file transfer message")
			return
		}
		
		if let msg = message as? CpcImMessageImpl {
			log.Log(.EDebug, "uuid=\(msg.uuid ?? ""), messageId=\(msg.messageId), incoming=\(msg.incoming)), read=\(msg.read), message=\(CpcImMessageImpl.debugMessage(msg.message) ?? "")")
		}
		
		let strettoSyncDb = CpcServicesImpl.instance()?.cpcIm?.strettoSyncDb
		strettoSyncDb?.updateConversation(conversation.conversationId, strettoSyncUnreadCountDelta: message.read ? 0: 1, totalCountDelta: 1)
		strettoSyncDb?.updateAccount(conversation.accountId, strettoSyncUnreadCountDelta: message.read ? 0: 1, totalCountDelta: 1)
		
		#if REMOTE_SYNC_GROUP_CHAT
		#else
		if conversation.isMUC || conversation.isRoom {
			log.Log(.EMax, "MUC syncing disabled: remotePartyAddress=\(CpcImMessageImpl.debugMessage(message.message) ?? "")")
			return
		}
		#endif
		
		let request = CpcStrettoSyncAddMessageRequest(accountId: strettoSyncAccountId, conversationId: conversation.conversationId, messageId: message.messageId)
		connection?.sendRequest(request)
	}
	
	@objc func markRead(conversation: CpcImConversation) {
		log.Log(.EDebug, "conversation(remotePartyAddress=\(String(describing: conversation.debugAddress()))")
		
		var convUnread: Int = 0
		var convTotal: Int = 0
		
		let strettoSyncDb = CpcServicesImpl.instance()?.cpcIm?.strettoSyncDb
		strettoSyncDb?.getStrettoSyncUnreadMessagesCount(conversation: conversation, unreadMessagesCount: &convUnread, totalMessagesCount: &convTotal)
		strettoSyncDb?.updateAccount(conversation.accountId, strettoSyncUnreadCountDelta: -convUnread, totalCountDelta: 0)
		strettoSyncDb?.updateConversation(conversation.conversationId, strettoSyncUnreadCount: 0, totalCount: convTotal)
		
		#if REMOTE_SYNC_GROUP_CHAT
		#else
		if conversation.isMUC || conversation.isRoom {
			log.Log(.EMax, "MUC mark conversation read disabled")
			return
		}
		#endif
		
		// Marking entire conversation as read; don't need last message timestamp for now
		if let acc = account, let conversationId = conversation.strettoSyncConversationId(account: acc) {
			let request = CpcStrettoSyncMarkConvReadRequest(accountId: strettoSyncAccountId, conversationId: conversationId, clientTimeStamp: 0)
			connection?.sendRequest(request)
		}
	}
	
	@objc func markDeleted(conversation: CpcImConversation!) {
		log.Log(.EDebug, "conversation(remotePartyAddress=\(String(describing: conversation.debugAddress()))")
		
		var convUnread: Int = 0
		var convTotal: Int = 0
		
		let strettoSyncDb = CpcServicesImpl.instance()?.cpcIm?.strettoSyncDb
		strettoSyncDb?.getStrettoSyncUnreadMessagesCount(conversation: conversation, unreadMessagesCount: &convUnread, totalMessagesCount: &convTotal)
		strettoSyncDb?.updateAccount(conversation.accountId, strettoSyncUnreadCountDelta: -convUnread, totalCountDelta: -convTotal)
		strettoSyncDb?.updateConversation(conversation.conversationId, strettoSyncUnreadCount: 0, totalCount: 0)
		
		#if REMOTE_SYNC_GROUP_CHAT
		#else
		if conversation.isMUC || conversation.isRoom {
			log.Log(.EMax, "MUC mark conversation deleted disabled")
			return
		}
		#endif

		guard let lastMessage = conversation.lastMessage as? CpcImMessageImpl, let lastMessageTime = lastMessage.time else {
			return
		}
		
		guard let connection = connection else {
			return
		}
		
		var clientTimeStamp = lastMessageTime.timeIntervalSince1970.toStrettoSyncTime()
		
		if !lastMessage.externalClock {
			clientTimeStamp += connection.clockDiffInMilliseconds
		}
		
		if let acc = account, let conversationId = conversation.strettoSyncConversationId(account: acc) {
			let request = CpcStrettoSyncMarkConvDeletedRequest(accountId: strettoSyncAccountId, conversationId: conversationId, clientTimeStamp: clientTimeStamp)
			connection.sendRequest(request)
		}
	}
	
	@objc override func changeMessageState(_ message: CpcImMessage, conversation: CpcImConversation) {
		
		guard let messageImpl = message as? CpcImMessageImpl else {
			return
		}
		
		log.Log(.EDebug, "strettoSyncId=\(messageImpl.strettoSyncId)")
		
		let request = CpcStrettoSyncUpdateMessageStateRequest(accountId: strettoSyncAccountId, message: messageImpl, conversation: conversation, updateType: .updateState, invalidateSyncId: false)
		connection?.sendRequest(request)
	}
	
	@objc func markMessageRead(message: CpcImMessage, conversation: CpcImConversation) {
		
		guard let messageImpl = message as? CpcImMessageImpl else {
			return
		}
		
		guard !message.isFileMessage() else {
			log.Log(.EDebug, "Skipping file transfer message")
			return
		}
		
		log.Log(.EDebug, "strettoSyncId=\(messageImpl.strettoSyncId)")
		
		let strettoSyncDb = CpcServicesImpl.instance()?.cpcIm?.strettoSyncDb
		strettoSyncDb?.updateConversation(conversation.conversationId, strettoSyncUnreadCountDelta: -1, totalCountDelta: 0)
		strettoSyncDb?.updateAccount(conversation.accountId, strettoSyncUnreadCountDelta: -1, totalCountDelta: 0)

		let request = CpcStrettoSyncUpdateMessageStateRequest(accountId: strettoSyncAccountId, message: messageImpl, conversation: conversation, updateType: .markRead, invalidateSyncId: false)
		connection?.sendRequest(request)
	}
	
	@objc override func sendMessageRead(_ message: CpcImMessageImpl, conversation: CpcImConversation) {
		let request = CpcStrettoSyncUpdateMessageStateRequest(accountId: strettoSyncAccountId, message: message, conversation: conversation, updateType: .markRead, invalidateSyncId: false)
		connection?.sendRequest(request)
	}
	
	@objc func markMessagesDeleted(messages: [CpcImMessage], conversation: CpcImConversation, invalidateSyncId: Bool) {
		let unread = messages.reduce(0) { (res, msg) in (msg.read || msg.isFileMessage()) ? res: (res + 1) }
		let total = messages.reduce(0) { (res, msg) in msg.isFileMessage() ? res: (res + 1) }
		
		let strettoSyncDb = CpcServicesImpl.instance()?.cpcIm?.strettoSyncDb
		strettoSyncDb?.updateConversation(conversation.conversationId, strettoSyncUnreadCountDelta: -unread, totalCountDelta: -total)
		strettoSyncDb?.updateAccount(conversation.accountId, strettoSyncUnreadCountDelta: -unread, totalCountDelta: -total)

		for message in messages {
			if let messageImpl = message as? CpcImMessageImpl {
				let request = CpcStrettoSyncUpdateMessageStateRequest(accountId: strettoSyncAccountId, message: messageImpl, conversation: conversation, updateType: .markDeleted, invalidateSyncId: invalidateSyncId)
				connection?.sendRequest(request)
			}
		}
	}
	
	@objc func updateEditedMessage(oldMessage: CpcImMessageImpl, newMessage: CpcImMessageImpl, conversation: CpcImConversation) {
		if conversation.isRoom || conversation.isMUC {
			return
		}
		
		let request = CpcStrettoSyncEditMessageRequest(accountId: strettoSyncAccountId, oldMessage: oldMessage, newMessage: newMessage, conversation: conversation)
		connection?.sendRequest(request)
	}
	
	@objc override func auditAccountMessageCounts() {
		let request = CpcStrettoSyncAccountMessageCountsRequest(accountId: strettoSyncAccountId, itemTypes: getAccountSyncItemTypes().map({ $0.intValue }))
		
		request.onSuccess = { response in
			if let acc = self.account {
				self.processMessageCounts(response, for: acc)
			}
		}
		
		connection?.sendRequest(request)
	}
	
	@objc override func auditConversationMessageCounts(_ conversation: CpcImConversation) {
		let request = CpcStrettoSyncConvMessageCountsRequest(accountId: strettoSyncAccountId, conversation: conversation)
		
		request.onSuccess = { response in
			if let item = response.items.first as? CPCRemoteSyncConversationThreadItem {
				self.processMessageCounts(item, for: conversation)
			}
		}
		
		connection?.sendRequest(request)
	}
	
	@objc override func prefetchMesssages(for conversation: CpcImConversation) {
		
		guard let acc = account, let conversationId = conversation.strettoSyncConversationId(account: acc) else {
			return
		}
		
		let request = CpcStrettoSyncFetchMessagesRequest(conversationId: conversationId, accountId: strettoSyncAccountId, itemTypes: getAccountSyncItemTypes(), offset: 0, count: Int(CpcServices.instance()?.cpcOptions?.strettoSyncPrefetchNumberOfMessages ?? 0))
		
		request.onSuccess = { response in
			let messages: [Any] = self.processFetchedSyncItems(response.items, for: conversation, purgeDeletedMessages: true)
			
			if response.items.count > 0 && messages.count == 0 {
				log.Log(.EInfo, "No applicable items found: items.count=\(response.items.count), messages.count=\(messages.count)")
			}
			
			CpcServicesImpl.instance()?.cpcIm?.notifyFetchMesssagesCompleted(messages, forConversation: conversation as? CpcImConversationImpl)
		}
		
		request.onError = { error in
			CpcServicesImpl.instance()?.cpcIm?.notifyFetchMessagesFailed(error, forConversation: conversation as? CpcImConversationImpl)
		}
		
		connection?.sendRequest(request)
	}
	
	@objc func fetchOlderMessages(conversation: CpcImConversation, count: Int) {
		log.Log(.EDebug, "count=\(count)")
		
		var timeEnd: Int64 = 0

		guard let connection = connection else {
			return
		}
		
		if let oldestImMessage = CpcServicesImpl.instance()?.cpcIm?.strettoSyncDb?.getOldestImMessageForConversation(conversation) as? CpcImMessageImpl,
			let oldestImMessageTime = oldestImMessage.time {
			timeEnd = oldestImMessageTime.timeIntervalSince1970.toStrettoSyncTime()
			timeEnd -= 1 // subtract 1 millisecond to skip current oldestImMessage
			
			if !oldestImMessage.externalClock {
				timeEnd += connection.clockDiffInMilliseconds
			}
		}
		
		guard let acc = account, let conversationId = conversation.strettoSyncConversationId(account: acc) else {
			return
		}
		
		let request = CpcStrettoSyncFetchMessagesRequest(conversationId: conversationId, accountId: strettoSyncAccountId, itemTypes: getAccountSyncItemTypes(), offset: 0, count: count, timeBegin: 0, timeEnd: timeEnd)
		
		request.onSuccess = { response in
			let messages: [Any] = self.processFetchedSyncItems(response.items, for: conversation, purgeDeletedMessages: false)
			
			if response.items.count > 0 && messages.count == 0 {
				log.Log(.EInfo, "No applicable items found: items.count=\(response.items.count), messages.count=\(messages.count)")
			}
			
			CpcServicesImpl.instance()?.cpcIm?.notifyFetchOlderMesssagesCompleted(messages, forConversation: conversation as? CpcImConversationImpl)
		}
		
		request.onError = { error in
			CpcServicesImpl.instance()?.cpcIm?.notifyFetchOlderMessagesFailed(error, forConversation: conversation as? CpcImConversationImpl)
		}
		
		connection.sendRequest(request)
	}
	
	@objc func fetchOlderConversations(count: Int) -> Bool {
		log.Log(.EDebug, "count=\(count)")
		
		var timeEnd: Int64 = 0
		
		guard let connection = connection, let acc = account else {
			return false
		}
		
		if let lastMessage = CpcServicesImpl.instance()?.cpcIm?.strettoSyncDb?.getOldestConversationForAccountId(acc.accountId)?.lastMessage as? CpcImMessageImpl,
			let lastMessageTime = lastMessage.time {
			timeEnd = lastMessageTime.timeIntervalSince1970.toStrettoSyncTime()
			
			if !lastMessage.externalClock {
				timeEnd += connection.clockDiffInMilliseconds
			}
		}
		
		let request = CpcStrettoSyncFetchConversationsRequest(accountId: strettoSyncAccountId, timeBegin: 0, timeEnd: timeEnd, offset: 0, count: count)
		
		request.onSuccess = { response in
			let conversations = self.processConversationThreadItems(response.items)
			CpcServicesImpl.instance()?.cpcIm?.notifyFetchOlderConversationsCompleted(conversations)
		}
		
		request.onError = { error in
			CpcServicesImpl.instance()?.cpcIm?.notifyFetchOlderConversationsFailed(error)
		}
		
		connection.sendRequest(request)
		return true
	}
	
	@objc func sendReaction(forMessage messageId: Int, address: String, value: String, time: Date) {
		guard CpcFeature.getBool(kFeatureXmppMessageReactions) &&
						(CpcServices.instance()?.cpcOptions?.enableXmppMessageReactions ?? false) else {
			log.Log(.EDebug, "Not syncing message reactions because feature is disabled")
			return
		}
		
		log.Log(.EDebug, "\(messageId), address: \(address), value: \(value)")
		
		guard let connection = connection else {
			return
		}
		
		let request = CpcStrettoSyncSendReactionRequest(accountId: strettoSyncAccountId, messageId: messageId, address: address, value: value, time: time)
		connection.sendRequest(request)
	}
	
	override func syncDeliveryStatus(_ status: CpcImMessageDeliveryStatus, read: Date?, deliveredDate: Date?, message: CpcImMessageImpl, conversation: CpcImConversationImpl) {
		
		switch (status, message.deliveryStatus) {
		
		case (.requesting, .succeeded), (.requesting, .delivered), (.requesting, .displayed), (.requesting, .failed),
				 (.succeeded, .delivered), (.succeeded, .displayed), (.succeeded, .failed),
				 (.delivered, .displayed),
				 (.failed, .requesting), (.failed, .succeeded), (.failed, .delivered), (.failed, .displayed):
			log.Log(.EDebug, "Updating remote delivery from \(CpcImMessageDeliveryStatusEnum.string(forValue: Int(status.rawValue)) ?? "") to \(CpcImMessageDeliveryStatusEnum.string(forValue: Int(message.deliveryStatus.rawValue)) ?? "")")
			changeMessageState(message, conversation: conversation)
		
		case (.displayed, .succeeded), (.displayed, .delivered), (.displayed, .failed), (.displayed, .requesting):
			message.readDate = read
			fallthrough
		case (.delivered, .succeeded), (.delivered, .failed), (.delivered, .requesting):
			message.deliveredDate = deliveredDate

			log.Log(.EDebug, "Updating local delivery from \(CpcImMessageDeliveryStatusEnum.string(forValue: Int(message.deliveryStatus.rawValue)) ?? "") to \(CpcImMessageDeliveryStatusEnum.string(forValue: Int(status.rawValue)) ?? "")")
			message.deliveryStatus = status
			CpcServicesImpl.instance()?.cpcIm?.updateStrettoSyncMessageDelivery(message, forConversation: conversation)
			
		default:
			break
		}
	}
	
	override func syncReactions(_ reactions: [CPCRemoteSyncReaction], message: CpcImMessage) {
		guard let connection, reactions.count > 0 else {
			return
		}
		
		for reaction in reactions {
			guard let address = reaction.address?.nonEmpty, let reactions = reaction.value?.nonEmpty else {
				continue
			}
			
			let serverTimeStamp = reaction.date.addingTimeInterval(TimeInterval(connection.clockDiffInMilliseconds)/1000)
			
			message.reactions.updateReaction(reactions, for: address, time: serverTimeStamp)
		}
	}
	
	private func syncAllReactionsForExistingMessages() {
		guard CpcFeature.getBool(kFeatureXmppMessageReactions) &&
					(CpcServices.instance()?.cpcOptions?.enableXmppMessageReactions ?? false) else {
			log.Log(.EDebug, "Not syncing reactions because feature is disabled")
			mustSyncReactions = false
			transition(to: ECpcStrettoSyncStateConnected)
			return
		}

		log.Log(.EDebug, "Syncing reactions for existing messages")

		guard let cpcIm = CpcServicesImpl.instance()?.cpcIm else {
			mustSyncReactions = false
			transition(to: ECpcStrettoSyncStateConnected)
			return
		}

		// Get all conversations for this account
		let conversations = cpcIm.getAllConversations().compactMap { conversation -> CpcImConversationImpl? in
			guard let conv = conversation as? CpcImConversationImpl,
						conv.accountId == account?.accountId else {
				return nil
			}
			return conv
		}

		var pendingRequests = 0
		let totalConversations = conversations.count

		if totalConversations == 0 {
			log.Log(.EDebug, "No conversations to sync reactions for")
			mustSyncReactions = false
			transition(to: ECpcStrettoSyncStateConnected)
			return
		}

		// Fetch messages with reactions for each conversation
		for conversation in conversations {
			guard let acc = account, let conversationId = conversation.strettoSyncConversationId(account: acc) else {
				continue
			}

			pendingRequests += 1
			let request = CpcStrettoSyncFetchMessagesRequest(
				conversationId: conversationId,
				accountId: strettoSyncAccountId,
				itemTypes: getAccountSyncItemTypes(),
				offset: 0,
				count: Int(CpcServices.instance()?.cpcOptions?.strettoSyncPrefetchNumberOfMessages ?? 50)
			)

			request.onSuccess = { [weak self] response in
				guard let self = self else { return }

				// Process the fetched items to update reactions
				_ = self.processFetchedSyncItems(response.items, for: conversation, purgeDeletedMessages: false)

				pendingRequests -= 1
				if pendingRequests == 0 {
					log.Log(.EDebug, "Completed syncing reactions for all conversations")
					self.mustSyncReactions = false
					self.transition(to: ECpcStrettoSyncStateConnected)
				}
			}

			request.onError = { [weak self] error in
				guard let self = self else { return }

				log.Log(.EError, "Failed to sync reactions for conversation: \(error.localizedDescription)")
				pendingRequests -= 1
				if pendingRequests == 0 {
					log.Log(.EDebug, "Completed syncing reactions (with some errors)")
					self.mustSyncReactions = false
					self.transition(to: ECpcStrettoSyncStateConnected)
				}
			}

			connection?.sendRequest(request)
		}
	}

	private func ensureSelfConversationExists(_ reload: Bool) {
		let isSelfChatEnabled = CpcFeature.getBool(kFeatureSelfChat)
			&& CpcServices.instance()?.cpcOptions?.enableSelfChat ?? false
		guard isSelfChatEnabled else {
			return
		}

		guard let account, let uri = account.uri else {
			return
		}

		let type: CpcIMConversationType = account.protocol == ESignalingProtocolXmpp ? .ECpcIMConversationIM: .ECpcIMConversationSMS

		if CpcServicesImpl.instance()?.cpcIm?.getConversationForUri(uri.aorNoPort, type: type, account: account) != nil {
			return
		}

		log.Log(.EDebug, "Self conversation not found, creating new")

		let cpcUri = CpcUri(string: uri.aorWithSchemeNoPort)
		let request = CpcOutgoingImRequest()
		request.account = account
		request.type = type
		request.participants = [CpcImParticipant(address: cpcUri, routeAddress: cpcUri, displayName: nil)]

		guard let conv = CpcServicesImpl.instance()?.cpcIm?.startConversation(request, existing: nil) as? CpcImConversationImpl else {
			return
		}

		let modTime = Date()
		conv.modTime = modTime
		CpcServicesImpl.instance()?.cpcIm?.imDb.updateModTime(modTime, conversation: conv)

		if reload {
			CpcServicesImpl.instance()?.cpcIm?.reloadConversations()
		}
	}
}


extension CpcStrettoSyncImClient: CpcStrettoSyncConnectionHandler {
	
	@objc var strettoSyncAccountId: CpcStrettoSyncAccountId {
		return strettoSyncAccId ?? (account?.strettoSyncAccountId() ?? "")
	}
	
	func onConnectionState(_ connection: CpcStrettoSyncConnection, state: CpcStrettoSyncConnectionState) {
		guard let conn = self.connection, connection == conn else {
			return
		}
		
		connectionState = state

		log.Log(.EDebug, "state = \(String(describing: state))")
		
		switch state {
		case .connecting:
			transition(to: ECpcStrettoSyncStateConnecting)
		case .connected:
			transition(connectingStage: ECpcStrettoSyncConnectingFetchRevision)
		case .disconnecting:
			transition(to: ECpcStrettoSyncStateDisconnecting)
		case .disconnected:
			transition(to: ECpcStrettoSyncStateDisconnected)
		}
	}
	
	func onPersistentRequestSucceeded(_ request: CpcStrettoSyncPersistentRequest) {

		log.Log(.EDebug, "Request \(type(of: request).name), account: \(request.accountId)")
		
		switch request {
		case is CpcStrettoSyncSweepMarkerRequest:
			auditAccountMessageCounts()

		case let req as CpcStrettoSyncMarkConvReadRequest:
			if let response = req.response {
				if state == ECpcStrettoSyncStateConnected && currentRev < response.rev {
					currentRev = response.rev
				}
			}
			
		case let req as CpcStrettoSyncMarkConvDeletedRequest:
			if let response = req.response {
				if state == ECpcStrettoSyncStateConnected && currentRev < response.rev {
					currentRev = response.rev
				}
			}
			
		case let req as CpcStrettoSyncUpdateMessageStateRequest:
			if let response = req.response {
				if state == ECpcStrettoSyncStateConnected && currentRev < response.rev {
					currentRev = response.rev
				}
			}
			
			var convId: Int = req.conversationId
			
			if req.invalidateSyncId,
				 let message = CpcServicesImpl.instance()?.cpcIm?.imDb.getImMessage(req.messageId, conversationId: &convId) as? CpcImMessageImpl {
				log.Log(.EDebug, "Resetting strettoSyncId: \(message.strettoSyncId)")
				
				CpcServicesImpl.instance()?.cpcIm?.strettoSyncDb?.updateMessage(message.messageId, strettoSyncId: 0)
			}
			
		case let req as CpcStrettoSyncAddConversationRequest:
			if let response = req.response {
				assert(response.items.count == 1, "Should be one update item")
				
				if state == ECpcStrettoSyncStateConnected && currentRev < response.rev {
					currentRev = response.rev
				}
				
				if let items = response.items as? [CPCRemoteSyncItemUpdate] {
					for itemUpdate in items {
						log.Log(.EDebug, "Add conversation done: serverID=\(itemUpdate.serverID)")
						
						CpcServicesImpl.instance()?.cpcIm?.strettoSyncDb?.updateConversation(req.conversationId, strettoSyncId: itemUpdate.serverID)
					}
				}
			}
			
		case let req as CpcStrettoSyncAddMessageRequest:
			if let response = req.response {
				let imdb = CpcServicesImpl.instance()?.cpcIm?.imDb
				
				if state == ECpcStrettoSyncStateConnected && currentRev < response.rev {
					currentRev = response.rev
				}
				
				guard let conversation = imdb?.getConversation(req.conversationId) as? CpcImConversationImpl, let message = imdb?.getImMessage(req.messageId, conversationId: nil) as? CpcImMessageImpl else {
					return
				}
				
				completeAddMessage(response, forConversation: conversation, andMessage: message)
			}
			
		case let req as CpcStrettoSyncEditMessageRequest:
			if let response = req.response {
				let imdb = CpcServicesImpl.instance()?.cpcIm?.imDb
				
				if state == ECpcStrettoSyncStateConnected && currentRev < response.rev {
					currentRev = response.rev
				}
				
				guard let conversation = imdb?.getConversation(req.conversationId) as? CpcImConversationImpl, let message = imdb?.getImMessage(req.newMessageId, conversationId: nil) as? CpcImMessageImpl else {
					return
				}
				
				completeEditMessage(response, conversation: conversation, message: message)
				syncReactions(for: message)
			}
			
		case let req as CpcStrettoSyncSendReactionRequest:
			if let response = req.response {
				if req.value != response.value || req.time != response.date {
					completeReactionUpdateWithUpdatedValue(messageId: req.messageId, address: req.address, value: response.value, date: response.date)
				}
			}
			
		default:
			break
		}
	}
	
	private func syncReactions(for message: CpcImMessageImpl) {
		guard CpcFeature.getBool(kFeatureXmppMessageReactions) &&
		      (CpcServices.instance()?.cpcOptions?.enableXmppMessageReactions ?? false) else {
			log.Log(.EDebug, "Not syncing message reactions because feature is disabled")
			return
		}
		
		let reactions = message.reactions
		for (address, value) in reactions.reactions {
			sendReaction(
				forMessage: message.messageId,
				address: address,
				value: value.value,
				time: value.time
			)
		}
	}
	
	func onPersistentRequestFailed(_ request: CpcStrettoSyncPersistentRequest) {
		log.Log(.EDebug, "Request \(type(of: request).name), account: \(request.accountId)")
	}
	
}

extension CpcStrettoSyncImClient {
	
	private func completeEditMessage(_ syncItemsEvent: CPCRemoteSyncSyncItemsCompleteEvent, conversation: CpcImConversationImpl, message: CpcImMessageImpl) {
		assert(syncItemsEvent.items.count == 2, "Should be two update items")
		
		guard let items = syncItemsEvent.items as? [CPCRemoteSyncItemUpdate] else {
			log.Log(.EError, "Invalid items")
			return
		}
		
		guard let item = items.first(where: { $0.itemDeleted == false }) else {
			log.Log(.EError, "Added item not found")
			return
		}
		
		log.Log(.EDebug, "Edit message done: new serverID=\(item.serverID)")
		
		guard let cpcIm = CpcServicesImpl.instance()?.cpcIm else {
			return
		}
		
		if let existingMessage = cpcIm.strettoSyncDb?.getImMessageByStrettoSyncId(item.serverID, conversationId: nil) as? CpcImMessageImpl, existingMessage.messageId != message.messageId {
			log.Log(.EError, "Message with serverID \(item.serverID) already exists uuid=\(existingMessage.uuid ?? "")")
		}
		
		message.strettoSyncId = item.serverID
		message.time = item.clientCreatedDate
		message.externalClock = true
		
		var deltaUnreadCount = 0
		var needToSortConversations = false
		var updateServerItemAsRead = false
		var updateLocalItemAsRead = false
		
		if item.itemRead == false && message.read == true {
			updateServerItemAsRead = true
			
		} else if item.itemRead == true && message.read == false {
			deltaUnreadCount -= 1
			message.read = true
			
			if message.messageId == conversation.lastMessage?.messageId {
				(conversation.lastMessage as? CpcImMessageImpl)?.read = true
			}
			updateLocalItemAsRead = true
		}

		cpcIm.strettoSyncDb?.updateMessage(message.messageId, strettoSyncId: message.strettoSyncId, isRead: message.read, timeStamp: (message.time ?? Date()).timeIntervalSince1970, externalClock: true, flags: message.flags)
		
		if updateServerItemAsRead {
			sendMessageRead(message, conversation: conversation)
		} else if updateLocalItemAsRead {
			cpcIm.markStrettoSyncMessageRead(message, forConversation: conversation)
		}

		if !message.incoming {
			syncDeliveryStatus(CpcStrettoSyncClient.toMessageDeliveryStatus(item.itemState), read: item.readDate, deliveredDate: item.deliveredDate, message: message, conversation: conversation)
		}

		if conversation.updateLastMessage(forNewMessageIfNeeded: message) {
			needToSortConversations = true
			conversation.modTime = message.time
			cpcIm.imDb.updateModTime(message.time ?? Date(), conversation: conversation)
		}
		
		if deltaUnreadCount != 0 {
			log.Log(.EInfo, "Updating unread count: unreadDelta=\(deltaUnreadCount)")
			cpcIm.strettoSyncDb?.updateConversation(conversation.conversationId, strettoSyncUnreadCountDelta: deltaUnreadCount, totalCountDelta: 0)
			cpcIm.strettoSyncDb?.updateAccount(conversation.accountId, strettoSyncUnreadCountDelta: deltaUnreadCount, totalCountDelta: 0)
		}

		if needToSortConversations {
			cpcIm.sortConversationsByModTime()
		}
	}
	
	func completeReactionUpdateWithUpdatedValue(messageId: Int, address: String, value: String, date: Date) {
		log.Log(.EDebug, "Id: \(messageId), address: \(address), value: \(value), date: \(date)")

		let imdb = CpcServicesImpl.instance()?.cpcIm?.imDb

		var convId: Int = 0
		
		guard let message = imdb?.getImMessage(messageId, conversationId: &convId) as? CpcImMessageImpl else {
			return
		}
		
		message.reactions.updateReaction(value, for: address, time: date)
		
		if message.reactions.hasUpdates {
			imdb?.updateMessageForReactions(message)
			
			// assume from history
			message.isHistory = true
			
			if let conversation = imdb?.getConversation(convId) {
				CpcServicesImpl.instance()?.cpcIm?.delegate?.imMessagesReactionsUpdated([message], for: conversation)
			}
		}
	}
}

extension CpcStrettoSyncImClient {
	
	func processNotificationMessageReactionsEvent(_ event: CPCRemoteSyncMessageReactionsEvent) {
		guard CpcFeature.getBool(kFeatureXmppMessageReactions) &&
					(CpcServices.instance()?.cpcOptions?.enableXmppMessageReactions ?? false) else {
			log.Log(.EDebug, "Not processing message reactions notification because feature is disabled")
			return
		}
		
		log.Log(.EDebug, "serverID: \(event.serverID), addr: \(event.address ?? ""), reactions: \(event.value ?? "")")
		
		guard let address = event.address?.nonEmpty, let reactions = event.value else {
			return
		}
		
		var conversationId: Int = 0
		
		guard let cpcIm = CpcServicesImpl.instance()?.cpcIm, let connection else {
			return
		}
		
		guard let message = cpcIm.strettoSyncDb?.getImMessageByStrettoSyncId(event.serverID, conversationId: &conversationId) as? CpcImMessageImpl else {
			log.Log(.EError, "Message with serverID \(event.serverID) not found")
			return
		}
		
		guard let conversation = cpcIm.imDb.getConversation(conversationId) else {
			log.Log(.EError, "Conversation not found")
			return
		}
		
		
		let serverTimeStamp = event.date.addingTimeInterval(TimeInterval(connection.clockDiffInMilliseconds)/1000)
		
		message.reactions.updateReaction(reactions, for: address, time: serverTimeStamp)
		
		guard message.reactions.hasUpdates else {
			log.Log(.EMax, "Skipping, not a new reaction")
			return
		}

		cpcIm.imDb.updateMessageForReactions(message)
		
		// assume from history
		message.isHistory = true
		
		cpcIm.delegate?.imMessagesReactionsUpdated([message], for: conversation)
	}
}

extension CpcImConversation {
	@objc func getStrettoSyncClient() -> CpcStrettoSyncImClient? {
		#if REMOTE_SYNC_GROUP_CHAT
		#else
		if isMUC || isRoom {
			return nil
		}
		#endif

		if CpcFeature.getBool(kFeatureStrettoSync) == false {
			return nil
		}
		
		return (CpcUtilsServices.getAccountById(accountId) as? CpcAccountImpl)?.strettoSyncImClient
	}
}
